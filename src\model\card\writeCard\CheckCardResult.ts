/*
 * @Author: 黎钰龙
 * @Date: 2024-08-11 13:44:51
 * @LastEditTime: 2025-04-30 16:33:09
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\card\writeCard\CheckCardResult.ts
 * 记得注释
 */
import { CardMedium } from "model/default/CardMedium"

export default class CheckCardResult {
  // 卡号
  code: Nullable<string> = null
  // 卡介质
  cardMedium: Nullable<CardMedium> = null
  // 卡状态
  cardState: Nullable<string> = null
  // 卡模板类型
  cardType: Nullable<string> = null
}