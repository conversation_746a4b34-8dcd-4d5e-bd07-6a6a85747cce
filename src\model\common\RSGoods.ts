/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-04-26 10:13:19
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\common\RSGoods.ts
 * 记得注释
 */
export default class RSGoods {
  //
  gid: Nullable<string> = null
  //商品代码
  code: Nullable<string> = null
  //商品名称
  name: Nullable<string> = null
  //条码
  barcode: Nullable<string> = null
  //
  qpcStr: Nullable<string> = null
  //价格
  price: Nullable<number> = null
  //备注
  remark: Nullable<string> = null
  //品牌id
  brandId: Nullable<string> = null
  //品类id
  categoryId: Nullable<string> = null
  //品牌名称
  brandName: Nullable<string> = null
  //品类名称
  categoryName: Nullable<string> = null
  // 是否是增值商品
  appreciationGoods: Nullable<boolean> = false
}