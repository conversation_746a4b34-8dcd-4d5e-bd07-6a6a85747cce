<template>
  <div class="register-send-gift-dtl">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button
          @click="doAudut"
          type="primary"
          v-if="
            dtl &&
              dtl &&
              dtl.state === 'INITIAL' &&
              hasOptionPermission('/券/券管理/券核销', '单据审核')
          "
          >{{ formatI18n("/公用/按钮", "审核") }}</el-button
        >
      </template>
    </BreadCrume>
    
    <div style="overflow: auto; height: 95%">
      <div class="top-wrap">
        <div class="left">
          <div class="back">
            <img src="~assets/image/storevalue/back.png" />
          </div>
        </div>
        <div class="right">
          <div class="top">
            <div class="item1">
              <div class="bill">
                {{ i18n('单号') }}：{{
                  number
                }}
              </div>
              <div
                style="
                  width: 300px;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                "
                class="name"
              >
                {{ i18n('券核销单') }}
              </div>
            </div>
            <div class="item2">
              <div class="desc">
                {{ formatI18n("/营销/券礼包活动/券礼包活动", "状态") }}
              </div>
              <div class="state">
                <el-tag
                  size="small"
                  type="warning"
                  v-if="dtl && dtl.state === 'INITIAL'"
                  >{{ formatI18n("/公用/过滤器", "未审核") }}</el-tag
                >
                <el-tag
                  size="small"
                  type="success"
                  v-if="dtl && dtl.state === 'EFFECTED'"
                  >{{
                    formatI18n("/公用/过滤器", "已审核")
                  }}</el-tag
                >
              </div>
            </div>
          </div>
        </div>
      </div>
      <div style="height: 20px; background-color: rgba(242, 242, 242, 1)"></div>
      <BlockTitle
        :title="i18n('核销明细')"
      ></BlockTitle>
      <div class="gift-set">
          <el-form label-width="100px">
            <el-form-item :label="i18n('核销券号')">
              <div>
                <div v-for="(item, index) in dtl.couponCodes" :key="index">{{item}}</div>
              </div>
            </el-form-item>
            <el-form-item :label="i18n('发生组织')">
              <div>
                [{{dtl.issueOrgId}}]{{dtl.issueOrgName}}
              </div>
            </el-form-item>
            <el-form-item :label="i18n('说明')">
              <div>
                {{dtl.remark || '-'}}
              </div>
            </el-form-item>
          </el-form>
      </div>
      <BlockTitle
        :title="i18n('操作日志')"
      ></BlockTitle>
      <div style="padding: 0 20px">
        <el-table :data="logData">
          <el-table-column :label="i18n('操作类型')">
            <template slot-scope="scope">
              {{scope.row.state === 'EFFECTED'? i18n('审核信息'): i18n('新建信息')}}
            </template>
          </el-table-column>
          <el-table-column :label="i18n('操作人')">
            <template slot-scope="scope">
              {{scope.row.operator || '-'}}
            </template>
          </el-table-column>
          <el-table-column :label="i18n('操作时间')">
            <template slot-scope="scope">
              {{scope.row.lastModified | dateFormate3}}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./CouponWriteOffDtl.ts"></script>

<style lang="scss">
.register-send-gift-dtl {
  width: 100%;
  height: 100%;
  background: white;
  overflow: hidden;
  padding-bottom: 50px;
  .el-form-item__label {
    text-align: left;
  }
  .martop_15 {
    margin-top: 15px;
  }
  .flex {
    display: flex;
  }
  .martop_5 {
    margin-top: 5px;
  }
  .active-desc {
    margin: 30px;
    padding: 20px;
    background-color: #f9f9f9;
    height: auto;
    .item {
      margin-top: 10px;
    }
    .left57 {
      padding-left: 57px;
    }
  }
  .top-wrap {
    display: flex;
    flex-direction: row;
    .left {
      width: 80px;
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
      .back {
        width: 48px;
        height: 48px;
        border-radius: 100%;
        background-color: rgba(242, 242, 242, 1);
        img {
          width: 24px;
          height: 24px;
          position: relative;
          top: 13px;
          left: 12px;
        }
      }
    }
    .right {
      display: flex;
      flex: 1;
      flex-direction: column;
      position: relative;
      .top {
        display: flex;
        height: 105px;
        border-bottom: 1px solid rgba(242, 242, 242, 1);
        .item1 {
          .bill {
            margin-top: 16px;
            color: rgba(51, 51, 51, 0.***************);
          }
          .name {
            font-weight: 500;
            margin-top: 8px;
            font-size: 20px;
          }
        }
        .item2 {
          padding-left: 70px;
          padding-top: 16px;
          .desc {
            color: rgba(51, 51, 51, 0.***************);
          }
          .state {
            font-weight: 500;
            margin-top: 8px;
            font-size: 20px;
          }
        }
      }
      .bottom {
        padding-bottom: 20px;
        .account-info {
          margin-top: 10px;
          .item {
            line-height: 36px;
          }
        }
        .red {
          color: red;
        }
        .green {
          color: #008000;
        }
      }
    }
  }
  .gift-set {
    margin-left: 80px;
    .line-height {
      line-height: 36px;
    }
  }
}
</style>
