import {Component, Inject} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import RSBrand from 'model/common/RSBrand'
import RSBrandFilter from 'model/common/RSBrandFilter'
import BrandApi from 'http/brand/BrandApi'
import AbstractSelectDialog from './AbstractSelectDialog'

@Component({
  name: 'BrandSelectorDialog',
  components: {
    FormItem
  }
})
export default class BrandSelectorDialog extends AbstractSelectDialog<RSBrand> {

  brandFilter: RSBrandFilter = new RSBrandFilter()

  reset() {
    this.brandFilter = new RSBrandFilter()
  }

  getId(ins: RSBrand): string {
    return ins!.brand!.id as string;
  }

  getName(ins: RSBrand): string {
    return ins!.brand!.name as string;
  }

  getResponseData(response: any): any {
    return response.data
  }

  queryFun(): Promise<any> {
    this.brandFilter.page = this.page.currentPage - 1
    this.brandFilter.pageSize = this.page.size
    let oldMarketCenter = sessionStorage.getItem('marketCenter') || ''
		sessionStorage.setItem('marketCenter', this.marketCenter)
    return BrandApi.query(this.brandFilter).finally(()=>{
			sessionStorage.setItem('marketCenter', oldMarketCenter as string)
		});
  }
}
