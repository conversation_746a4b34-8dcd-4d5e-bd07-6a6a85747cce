/*
 * @Author: 黎钰龙
 * @Date: 2024-05-14 13:33:43
 * @LastEditTime: 2024-05-20 09:44:28
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\promotion\marketApply\MarketingApplyActivityFilter.ts
 * 记得注释
 */


// 营销申请列表活动过滤
export default class MarketingApplyActivityFilter {
  // 活动名称类似于
  nameLike: Nullable<string> = null
  // 活动号等于
  numberEquals: Nullable<string> = null
  // 类型in
  typeIn: string[] = []
  // 申请单号等于
  applyNumberEquals: Nullable<string> = null
  // 活动开始日期小于
  end: Nullable<Date> = null
  // 活动开始日期大于等于
  begin: Nullable<Date> = null
  // 页数
  page: Nullable<number> = null
  // 页面大小
  pageSize: Nullable<number> = null
  // 营销中心
  marketingCenter: Nullable<string> = null
  // 创建人类似于
  creatorLike: Nullable<string> = null
  // 活动状态等于
  stateEquals: Nullable<string> = null
}