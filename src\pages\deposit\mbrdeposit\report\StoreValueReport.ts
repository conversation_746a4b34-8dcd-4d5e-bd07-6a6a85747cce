import {Component, Vue} from 'vue-property-decorator'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import RechargeStream from 'pages/deposit/mbrdeposit/report/tabs/RechargeStream.vue'
import RechargeReturnStream from 'pages/deposit/mbrdeposit/report/tabs/RechargeReturnStream.vue'
import ConsumeFlow from 'pages/deposit/mbrdeposit/report/tabs/ConsumeFlow.vue'
import ConsumeReturnFlow from 'pages/deposit/mbrdeposit/report/tabs/ConsumeReturnFlow.vue'
import AdjustFlow from 'pages/deposit/mbrdeposit/report/tabs/AdjustFlow.vue'
import RechargeFlowByPay from 'pages/deposit/mbrdeposit/report/tabs/RechargeFlowByPay.vue'
import RechargeReturnFlowByPay from 'pages/deposit/mbrdeposit/report/tabs/RechargeReturnFlowByPay.vue'
import PrePayReportApi from 'http/prepay/report/prepay/PrePayReportApi'
import PrePayReportSum from 'model/prepay/report/prepay/PrePayReportSum'
import FormItem from 'cmp/formitem/FormItem.vue'
import AccountApi from 'http/account/common/AccountApi'
import ConstantMgr from 'mgr/ConstantMgr'
import PrePayConfigApi from 'http/prepay/config/PrePayConfigApi'
import I18nPage from "common/I18nDecorator";
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import DownloadCenterDialog from "pages/main/dowmload_center_dialog/DownloadCenterDialog";
import MbrStoreValueReportExportDialog from "pages/deposit/mbrdeposit/report/cmp/MbrStoreValueReportExportDialog.vue"
import PrePayReportFilter from "model/prepay/report/prepay/PrePayReportFilter";
import PermissionMgr from 'mgr/PermissionMgr'

@Component({
  name: 'StoreValueReport',
  components: {
    SubHeader,
    RechargeStream,
    RechargeReturnStream,
    ConsumeFlow,
    ConsumeReturnFlow,
    AdjustFlow,
    RechargeFlowByPay,
    RechargeReturnFlowByPay,
    FormItem,
    BreadCrume,
    DownloadCenterDialog,
    MbrStoreValueReportExportDialog
  }
})
@I18nPage({
  prefix: ['/储值/会员储值/会员储值报表/列表页面', '/公用/按钮'],
})
export default class StoreValueReport extends Vue {

  get getExportDialogShow() {
    return this.exportDialogShow
  }
  $refs: any
  switchFlag = false
  bindTab = '0'
  account = ''
  summary: PrePayReportSum = new PrePayReportSum()
  accounts: any[] = []
  isActive = false
  panelArray: any = []
  exportDialogShow = false
  fileDialogVisible = false
  showTip = false
  chooseAccount: string = ''
  daqiaoshihuaDingkai = PermissionMgr.daqiaoshihuaDingkai()
  created() {
    this.panelArray = [
      {
        name: this.formatI18n('/公用/菜单/会员储值报表'),
        url: ''
      }
    ]
    if (sessionStorage.getItem('locale') === 'zh_CN') {
      this.isActive = true
    } else {
      this.isActive = false
    }
    this.getPrePermission()
  }

  mounted() {
    this.bindTab = '0'
  }

  doAccountChange(item: any) {
    this.accounts.forEach(((ac)=> {
      if (ac.id == item) {
        this.chooseAccount = `[${item}]${ac.name}`
      }
    }))
    this.setState()
    this.getSummary()
  }

  doTabClick() {
    this.setState()
  }

  doBatchExport() {
    this.exportDialogShow = true
  }

  doDownloadDialogClose() {
    this.showTip = false
    this.fileDialogVisible = false
  }

  doExportDialogClose() {
    this.exportDialogShow = false
  }

  doExportSubmit(type: string, filter: PrePayReportFilter) {
    if (!type || !filter) {
      return
    }
    if (this.switchFlag) {
      if (this.account) {
        filter.accountIdEquals = this.account
      }
    }
    if (type === 'RECHARGE_HST') {
      this.export(false,filter,type)
    } else if (type === 'RECHARGE_REFUND_HST') {
      this.export(true,filter,type)
    } else if (type === 'CONSUME_HST') {
      this.export(false,filter,type)
    } else if (type === 'CONSUME_REFUND_HST') {
      this.export(true,filter,type)

    } else {
      this.export(true,filter,type)
    }
  }

  exportAfter() {
    this.showTip = true
    this.fileDialogVisible = true
  }

  private setState() {
    if (this.bindTab === '0') { // '充值流水'
      this.$refs.rechargeStreamFlag.onReset(this.account)
    } else if (this.bindTab === '1') { // '充值退款流水'
      this.$refs.rechargeReturnStreamFlag.onReset(this.account)
    } else if (this.bindTab === '2') { // '消费流水'
      this.$refs.consumeFlowFlag.onReset(this.account)
    } else if (this.bindTab === '3') { // '消费退款流水'
      this.$refs.consumeReturnFlowFlag.onReset(this.account)
    } else if (this.bindTab === '4') { // '调整流水'
      this.$refs.adjustFlowFlag.onReset(this.account)
    } else if (this.bindTab === '5') { // '充值流水-按支付方式'
      this.$refs.rechargeFlowByPayFlag.onReset(this.account)
    } else {
      this.$refs.rechargeReturnFlowByPayFlag.onReset(this.account)
    }
  }

  private getSummary() {
    if (this.daqiaoshihuaDingkai) return  //大桥定开不展示数据
    // PrePayReportApi.summary(this.account).then((resp: any) => {
    //   if (resp && resp.code === 2000) {
    //     this.summary = resp.data
    //   }
    // }).catch((error) => {
    //   if (error && error.message) {
    //     this.$message.error(error.message)
    //   }
    // })
  }

  private getAccountType() {
    AccountApi.getAccountTypes().then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.accounts = resp.data
        if (this.accounts && this.accounts.length > 0) {
          this.account = this.accounts[0].id;
          this.chooseAccount = `[${this.accounts[0].id}]${this.accounts[0].name}`
        }
        this.getSummary()
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private getPrePermission() {
    const loading = this.$loading(ConstantMgr.loadingOption)
    PrePayConfigApi.get().then((resp: any) => {
      if (resp && resp.code === 2000) {
        loading.close()
        if (resp.data && resp.data.enableMultipleAccount) { // 开启多账户
          this.switchFlag = resp.data.enableMultipleAccount
          // this.switchFlag = false
          if (this.switchFlag) {
            this.getAccountType()
          }
        } else {
          this.switchFlag = false // 未开启多账户
          this.getSummary()
        }
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
      loading.close()
    })
  }

  private export(refund: boolean, filter: PrePayReportFilter, type: string) {
    PrePayReportApi.export(filter, refund,type).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.exportAfter()
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
}
