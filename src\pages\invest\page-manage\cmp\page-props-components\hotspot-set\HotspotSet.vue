<!--
 * @Author: haiding <EMAIL>
 * @Date: 2025-02-19 10:03:52
 * @LastEditors: 黎钰龙
 * @LastEditTime: 2025-05-22 14:59:49
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\cmp\page-props-components\hotspot-set\HotspotSet.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <el-form :label-position="labelPosition" :model="value" :rules="rules" ref="form">
      <el-form-item :label="label" prop="propItems" label-width="100px" style="width: 100%">
        <div class="hot-spot-action">
          <el-button type="primary" @click="addHotSpot">{{ i18n('新增热区') }}</el-button>
          <el-button @click="delHotSpot">{{ i18n('清空热区') }}</el-button>
        </div>
      </el-form-item>
      <div style="padding-left: 20px" v-if="value.propItems.length > 0">
        <div v-for="item in value.propItems" :key="item">
          <el-form-item :label="i18n('名称')" label-width="100px" style="width: 100%">{{ i18n('热区') }} {{ item.zIndex + 1 }}</el-form-item>
          <page-jump ref="jumpPage" :advertiseChannel="advertiseChannel" v-model="item.jumpPageInfo"></page-jump>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script lang="ts" src="./HotspotSet.ts"></script>

<style lang="scss" scoped>
</style>
