/*
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:05
 * @LastEditTime: 2024-02-28 18:01:33
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\prepaycard\prepaycardChargeGift\PrepayCardChargeGiftForm.ts
 * 记得注释
 */
import CardBalancePromotionActivity, { Strategy } from "model/payment/member/MemberBalancePromotionActivity";
import StoreRange from "model/common/StoreRange";
import ActivityBody from "model/common/ActivityBody";
import DateUtil from "util/DateUtil";
import RSGrade from "model/common/RSGrade";
import IdName from "model/common/IdName";
import CardDepositActivity from "model/cardDepositActivity/CardDepositActivity";
import CardDepositRuleLine from "model/cardDepositActivity/CardDepositRuleLine";
import DateTimeCondition from "model/common/DateTimeCondition";
import ActivityDateTimeCondition from "model/common/ActivityDateTimeCondition";
import ChannelRange from "model/common/ChannelRange";
import Vue from "vue";

class CardBalancePromotionFormData {
  // 活动id
  activityId: Nullable<string> = null
  // 名称
  name: Nullable<string> = null
  // 活动主题代码
  topicCode: Nullable<string> = null
  // 活动时间
  dateTimeCondition: ActivityDateTimeCondition = new ActivityDateTimeCondition()
  // 活动门店
  stores: Nullable<StoreRange> = new StoreRange()
  // 卡模板
  cardTemplates: IdName[] = []
  // 明细
  lines: CardDepositRuleLine[] = [new CardDepositRuleLine()]
  // 活动状态
  state: Nullable<string> = null
  // 每充值金额
  amount: Nullable<number> = null
  // 赠送积分数量
  points: Nullable<number> = null
  // 仅前端用 是否勾选积分设置
  pointCheck: Nullable<boolean> = false
  // 全部阶梯 %或元
  rebateType: Nullable<'amount' | 'percentage'> = 'amount'
  // 充值说明
  description: Nullable<string> = null
  // 充值协议
  agreement: Nullable<string> = null
}

export default class CardBalancePromotionForm {
  data: CardBalancePromotionFormData = new CardBalancePromotionFormData()
  rules: any
  amountRules: any
  qtyRules: any
  init(master: any) {
    this.rules = {
      name: [
        { required: true, message: master.i18n('请输入活动名称'), trigger: ['change', 'blur'] },
        { min: 1, max: 80, message: master.i18n('长度在80个字符以内'), trigger: ['change', 'blur'] }
      ],
      cardTemplates: [
        {
          validator: (rule: any, value: IdName[], callback: any) => {
            if (value.length === 0) {
              callback(master.i18n('请选择卡模板'))
            }
            callback()
          }, trigger: ['change', 'blur']
        }
      ],
      amount: [
        {
          validator: (rule: any, value: any, callback: any) => {
            if (this.data.pointCheck && !this.data.amount) {
              callback(master.i18n('请填写必填项'))
            }
            callback()
          }, trigger: ['change', 'blur']
        }
      ],
      points: [
        {
          validator: (rule: any, value: any, callback: any) => {
            if (this.data.pointCheck && !this.data.points) {
              callback(master.i18n('请填写必填项'))
            }
            callback()
          }, trigger: ['change', 'blur']
        }
      ],
    }

    this.amountRules = [
      { required: true, message: master.i18n('请输入金额'), trigger: ['change', 'blur'] },
    ]

    this.qtyRules = [
      { required: true, message: master.i18n('请输入数量'), trigger: ['change', 'blur'] },
    ]
  }

  toParams() {
    let formData: CardBalancePromotionFormData = JSON.parse(JSON.stringify(this.data))
    let params = new CardDepositActivity()
    params.body = new ActivityBody()
    params.body.name = formData.name
    params.body.topicCode = formData.topicCode
    // 活动时间
    let endDate = DateUtil.parseDate(formData.dateTimeCondition.endDate)
    endDate.setHours(23)
    endDate.setMinutes(59)
    endDate.setSeconds(59)
    params.body.beginDate = DateUtil.format(formData.dateTimeCondition.beginDate)
    params.body.endDate = DateUtil.format(endDate)
    params.body.stores = formData.stores
    params.dateTimeCondition = formData.dateTimeCondition.dateTimeCondition
    params.cardTemplates = formData.cardTemplates
    params.lines = formData.lines
    params.channelRange = new ChannelRange()
    params.channelRange.channelRangeType = 'ALL' as any
    params.channelRange.channels = []
    params.amount = formData.amount
    params.points = formData.points
    params.description = formData.description
    params.agreement = formData.agreement
    return params
  }

  of(activity: CardDepositActivity) {
    if (!activity || !activity.body) {
      return
    }
    this.data.name = activity.body.name
    this.data.topicCode = activity.body.topicCode
    this.data.stores = activity.body.stores
    this.data.dateTimeCondition.beginDate = DateUtil.parseDate(activity.body.beginDate)
    this.data.dateTimeCondition.endDate = DateUtil.parseDate(activity.body.endDate)
    this.data.dateTimeCondition.dateTimeCondition = activity.dateTimeCondition as DateTimeCondition
    this.data.cardTemplates = activity.cardTemplates
    this.data.amount = activity.amount
    this.data.points = activity.points
    this.data.description = activity.description
    this.data.agreement = activity.agreement
    if (this.data.amount && this.data.points) {
      this.data.pointCheck = true
    }

    for (const item of activity.lines) {
      if (item.rebateAmount || item.rebatePercentage) {
        item.rebateCheck = true
      } else {
        item.rebateCheck = false
      }
      if (!item.rebateType) {
        item.rebateType = 'amount' //兼容老数据 默认选中元
      }
      this.data.rebateType = item.rebateType
    }
    
    this.data.lines = activity.lines
    this.data.state = activity.body.state
  }
}