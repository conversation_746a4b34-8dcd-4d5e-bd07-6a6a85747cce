<template>
  <div class="store-value-adjust">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button @click="doStoreValueAdd" size="small" type="primary" v-if="permission.editable">新建储值调整单</el-button>
        <el-button size="small" type="primary" @click="doBatchImport" v-if="permission.editable">批量导入</el-button>
        <el-button @click="doStoreValueReason" size="small" v-if="permission.reasonEditable">储值调整原因设置</el-button>
      </template>
    </BreadCrume>
    <ListWrapper class="current-page" style="height: 95%">
      <template slot="query">
        <el-row>
          <el-col :span="8">
            <form-item label="单号">
              <el-input :placeholder="i18n('/储值/预付卡/预付卡查询/列表页面/请输入单号')" v-model="query.billNumberEquals"></el-input>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item label="会员">
              <el-input placeholder="请输入手机号/会员号" v-model="query.memberCodeLikes"></el-input>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item label="创建时间">
              <el-date-picker v-model="query.createdBetweenClosedClosed" end-placeholder="结束日期" format="yyyy-MM-dd" range-separator="-"
                ref="selectDate" size="small" start-placeholder="开始日期" type="daterange" value-format="yyyy-MM-dd">
              </el-date-picker>
            </form-item>
          </el-col>
        </el-row>
        <el-row style="margin-top: 10px">
          <el-col :span="8">
            <form-item label="最后修改时间">
              <el-date-picker v-model="query.lastModifiedBetweenClosedClosed" end-placeholder="结束日期" format="yyyy-MM-dd" range-separator="-"
                ref="selectDate" size="small" start-placeholder="开始日期" type="daterange" value-format="yyyy-MM-dd">
              </el-date-picker>
            </form-item>
          </el-col>
          <el-col :span="8" v-if="switchFlag">
            <form-item label="账户">
              <el-select placeholder="请选择" v-model="query.accountEquals">
                <el-option no-i18n :label="item.name" :value="item.id" :key="item.id" v-for="item in types">[{{item.id}}]{{item.name}}</el-option>
              </el-select>
            </form-item>
          </el-col>
        </el-row>
        <el-row style="margin-top: 10px">
          <el-col :span="8">
            <form-item>
              <el-button @click="doSearch" type="primary">查询</el-button>
              <el-button @click="doReset">重置</el-button>
            </form-item>
          </el-col>
        </el-row>
      </template>
      <template slot="btn">

      </template>
      <template slot="list">
        <el-tabs @tab-click="doHandleClick" v-model="activeName">
          <!-- 全部 -->
          <el-tab-pane no-i18n :label="getAllCount" name="ALL">
            <el-checkbox @change="doSelectAll" style="margin-right: 0px;margin-left: 14px" v-model="selectAll"></el-checkbox>&nbsp;&nbsp;
            <i18n k="/储值/会员储值/储值调整单/列表/已选择{0}张单据">
              <template slot="0">&nbsp;{{selectedArr.length}}&nbsp;</template>
            </i18n>
            <el-button style="margin-left: 15px" v-if="batchAuditPermission" @click="doBatchAudit">{{i18n('批量审核')}}</el-button>
            <el-button v-if="permission.editable" @click="doBatchDelete" style="margin-left: 20px;color: red">{{i18n('批量删除')}}</el-button>
            <el-button v-if="batchRejectPermission" @click="doBatchReject" style="margin-left: 20px;">{{i18n('批量驳回')}}</el-button>
            <el-button v-if="batchSubmitPermission" @click="doBatchSubmit" style="margin-left: 20px;">{{i18n('批量提交')}}</el-button>
          </el-tab-pane>
          <!-- 未提交 -->
          <el-tab-pane no-i18n :label="getNoAudit" name="INITIAL">
            <el-checkbox @change="doSelectAll" style="margin-right: 0px;margin-left: 14px" v-model="selectAll"></el-checkbox>&nbsp;&nbsp;
            <i18n k="/储值/会员储值/储值调整单/列表/已选择{0}张单据">
              <template slot="0">&nbsp;{{selectedArr.length}}&nbsp;</template>
            </i18n>
            <el-button style="margin-left: 15px" v-if="batchAuditPermission" @click="doBatchAudit">{{i18n('批量审核')}}</el-button>
            <el-button v-if="permission.editable" @click="doBatchDelete" style="margin-left: 20px;color: red">{{i18n('批量删除')}}</el-button>
            <el-button v-if="batchSubmitPermission" @click="doBatchSubmit" style="margin-left: 20px;">{{i18n('批量提交')}}</el-button>
          </el-tab-pane>
          <!-- 已提交 -->
          <el-tab-pane no-i18n :label="getSubmit" name="SUBMIT">
            <el-checkbox @change="doSelectAll" style="margin-right: 0px;margin-left: 14px" v-model="selectAll"></el-checkbox>&nbsp;&nbsp;
            <i18n k="/储值/会员储值/储值调整单/列表/已选择{0}张单据">
              <template slot="0">&nbsp;{{selectedArr.length}}&nbsp;</template>
            </i18n>
            <el-button style="margin-left: 15px" v-if="batchAuditPermission" @click="doBatchAudit">{{i18n('批量审核')}}</el-button>
            <el-button v-if="batchRejectPermission" @click="doBatchReject" style="margin-left: 20px;">{{i18n('批量驳回')}}</el-button>
          </el-tab-pane>
          <!-- 已审核 -->
          <el-tab-pane no-i18n :label="getAudit" name="AUDITED">
          </el-tab-pane>
          <!-- 已驳回 -->
          <el-tab-pane no-i18n :label="getRejected" name="REJECTED">
          </el-tab-pane>
        </el-tabs>
        <el-table ref="table" :data="tableData" @selection-change="handleSelectionChange" style="width: 100%;margin-top: 10px">
          <el-table-column v-if="activeName !== 'third'" type="selection" width="55">
          </el-table-column>
          <el-table-column label="单号" prop="billNumber" width="140">
            <template slot-scope="scope">
              <el-button v-if="permission.viewable" @click="doGoDtl(scope.row)" type="text" no-i18n>{{scope.row.billNumber}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="状态" prop="state" width="200">
            <template slot-scope="scope">
              <div>
                <el-tag type="success" v-if="scope.row.state === 'AUDITED'">{{i18n('已审核')}}</el-tag>
                <el-tag v-else-if="scope.row.state === 'SUBMIT'">{{i18n('已提交')}}</el-tag>
                <el-tag type="danger" v-else-if="scope.row.state === 'REJECTED'">{{i18n('已驳回')}}</el-tag>
                <el-tag type="warning" v-else>{{i18n('未提交')}}</el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" prop="created" width="140">
            <template slot-scope="scope">
              <div no-i18n>{{scope.row.created | dateFormate3}}</div>
            </template>
          </el-table-column>
          <el-table-column label="最后修改时间" prop="lastModified" width="140">
            <template slot-scope="scope">
              <div no-i18n>{{scope.row.lastModified | dateFormate3}}</div>
            </template>
          </el-table-column>
          <el-table-column label="摘要" prop="remark">
            <template slot-scope="scope">
              <div no-i18n style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;" :title="scope.row.remark">
                {{scope.row.remark | strFormat}}</div>
            </template>
          </el-table-column>
          <el-table-column label="创建人" prop="created" width="140">
            <template slot-scope="scope">
              <div
                v-if="scope.row.logs!==null && scope.row.logs.length === 1 && (scope.row.logs[0].type==='新建信息' || scope.row.logs[0].type==='Create')">
                {{ scope.row.logs[0].operator }}</div>
              <div v-if="scope.row.logs!==null && scope.row.logs.length > 1">
                <div v-for="(item,index) in scope.row.logs" :key="index">
                  <div v-if="item.type==='新建信息' || item.type==='Create'">
                    {{ item.operator }}
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="审核人" prop="created" width="140">
            <template slot-scope="scope">
              <div v-if="scope.row.state == 'AUDITED' || scope.row.state == 'REJECTED'">            {{ scope.row.auditor }}</div>
              <div v-else>--</div>
  
              <!-- <div
                v-if="scope.row.logs===null || (scope.row.logs!==null && scope.row.logs.length === 1 && (scope.row.logs[0].type==='新建信息' || scope.row.logs[0].type==='Create'))">
                --</div>
              <div
                v-if="scope.row.logs!==null && scope.row.logs.length === 1 && (scope.row.logs[0].type==='审核信息' || scope.row.logs[0].type==='Audit')">
                {{ scope.row.logs[0].operator }}</div>
              <div v-if="scope.row.logs!==null && scope.row.logs.length > 1">
                <div v-for="(item,index) in scope.row.logs" :key="index">
                  <div v-if="item.type==='审核信息' || item.type==='Audit'">
                    {{ item.operator }}
                  </div>
                </div>
              </div> -->
            </template>
          </el-table-column>

          <el-table-column label="发生组织" prop="created" width="140">
            <template slot-scope="scope">
              <div no-i18n>{{scope.row.occurredOrg.name}}[{{scope.row.occurredOrg.id}}]</div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="156">
            <template slot-scope="scope">
              <span class="span-btn" @click="doModify(scope.row)"
                v-if="permission.editable && scope.row.state === 'INITIAL' && scope.row.source === 'create'">
                {{i18n('修改')}}
              </span>
              <span class="span-btn" @click="doSubmit(scope.row)" v-if="permission.submitable && scope.row.state === 'INITIAL'">
                {{i18n('提交')}}
              </span>
              <span class="span-btn" @click="doSubmitAndAudit(scope.row)"
                v-if="permission.auditable && permission.submitable && scope.row.state === 'INITIAL' && !hasOaPermission">
                {{i18n('提交并审核')}}
              </span>
              <span class="span-btn" @click="doAudit(scope.row)" v-if="permission.auditable && scope.row.state === 'SUBMIT' && !hasOaPermission">
                {{i18n('审核')}}
              </span>
              <span class="span-btn" @click="doReject(scope.row)" v-if="permission.rejectable && scope.row.state === 'SUBMIT' && !hasOaPermission">
                {{i18n('驳回')}}
              </span>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <!--分页栏-->
      <template slot="page">
        <el-pagination no-i18n :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
          @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background layout="total, prev, pager, next, sizes,  jumper">
        </el-pagination>
      </template>
    </ListWrapper>
    <ImportDialog no-i18n @upload-success="doUploadSuccess" @dialogClose="doDialogClose" :importUrl="importUrl" :orgs="orgs" :showOrg="showOrg"
      :templatePath="templatePath" :templateName="i18n('储值调整单模板')" :importNumber="50000" :dialogShow="dialogShow" :title="i18n('导入')">
    </ImportDialog>
    <RejectDialog ref="rejectDialog" @submit="doSubmitReject" @cancel="doCancelOperateRow"></RejectDialog>
    <DownloadCenterDialog :dialogvisiable="fileDialogvisiable" :showTip="true" @dialogClose="doDownloadDialogClose"></DownloadCenterDialog>
  </div>
</template>

<script lang="ts" src="./StoreValueAdjust.ts">
</script>

<style lang="scss">
.store-value-adjust {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: hidden;
  .current-page {
    .el-select {
      width: 100%;
    }
  }

  .el-range-editor.el-input__inner {
    width: 100%;
  }
  .el-table__body .el-table__row td {
    border-bottom: 1px solid #d7dfeb !important;
  }
  .span-btn {
    margin-right: 4px;
  }
}
</style>