/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-10-21 11:33:12
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\points\activity\goodsgainbyqtypoints\GoodsGainByQtyPointsActivity.ts
 * 记得注释
 */
import ActivityBody from 'model/common/ActivityBody'
import RuleLine from 'model/points/activity/goodsgainbyqtypoints/RuleLine'
import DateTimeCondition from "model/common/DateTimeCondition";
import ChannelRange from "model/common/ChannelRange";
import GradesRange from 'model/common/GradeRange';
import PushGroup from 'model/precisionmarketing/pushplan/PushGroup';

// 全场满数量加送积分活动
export default class GoodsGainByQtyPointsActivity {
  // 是否同时审核
  needAudit: Nullable<boolean> = null
  // 活动信息
  activityBody: Nullable<ActivityBody> = new ActivityBody()
  // 活动时间限制
  dateTimeCondition = new DateTimeCondition()
  // 每人每天限制
  pointsPerMember: Nullable<number> = null
  // 规则明细
  lines: RuleLine[] = []
  //渠道范围
  channelRange: Nullable<ChannelRange> = null
  // 参与会员等级
  gradeRange: Nullable<GradesRange> = null
  // 参与叠加促销
  joinPromotion: Nullable<boolean> = false;
  // 排除优惠商品
  excludeFavourGoodTypes: string[] = []
  // 排除优惠金额
  excludeFavourAmountTypes: string[] = []
  // 参与人群
  rule: Nullable<PushGroup> = null
}