<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:05
 * @LastEditTime: 2023-08-31 17:38:12
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\prepaycard\countCardReport\CountCardReport.vue
 * 记得注释
-->
<template>
  <div class="count-card-report">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button size="large" @click="doBatchExport" v-if="hasOptionPermission('/数据/报表/次卡报表','报表导出')">
          {{ formatI18n('/会员/会员资料', '批量导出') }}
        </el-button>
      </template>
    </BreadCrume>
    <div class="current-container">
      <el-tabs v-model="activeName" class="tabs">
        <el-tab-pane :label="i18n('售卡流水')" name="售卡流水" />
        <el-tab-pane :label="i18n('消费流水')" name="消费流水" />
        <el-tab-pane :label="i18n('退款流水')" name="退款流水" />
        <el-tab-pane :label="i18n('退卡流水')" name="退卡流水" />
      </el-tabs>
      <CountSalesCardsReport :stores="stores" :areaData="areaData" v-if="activeName === '售卡流水'" />
      <CountConsumCardsReport :stores="stores" :areaData="areaData" v-if="activeName === '消费流水'" />
      <CountRefundReport :stores="stores" :areaData="areaData" v-if="activeName === '退款流水'" />
      <CountRefundCardReport :stores="stores" :areaData="areaData" v-if="activeName === '退卡流水'"></CountRefundCardReport>
    </div>
    <DownloadCenterDialog :dialogvisiable="fileDialogVisible" :showTip="showTip" @dialogClose="doDownloadDialogClose">
    </DownloadCenterDialog>
    <CountCardReportExport :dialogShow="getExportDialogShow" @dialogClose="doExportDialogClose" @doSubmit="doExportSubmit">
    </CountCardReportExport>
  </div>
</template>

<script lang="ts" src="./CountCardReport.ts">
</script>

<style lang="scss">
.count-card-report {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: auto;

  .current-container {
    height: calc(100% - 150px);
    padding: 20px 0 0;
    .tabs {
      padding: 0 20px;
    }
  }
}
</style>