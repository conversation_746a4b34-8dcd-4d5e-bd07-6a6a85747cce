/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2023-07-17 11:21:19
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\report\query\coupon\list\CouponList.ts
 * 记得注释
 */
import MemberIdent from 'model/common/member/MemberIdent'
import { CouponState } from 'model/report/query/coupon/CouponState'

export default class CouponList extends MemberIdent {
  // 券号
  code: Nullable<string> = null
  // 外部券号
  outerCode: Nullable<string> = null
  // 外部券id
  outerId: Nullable<string> = null
  // 券状态
  state: Nullable<CouponState> = null
  // 券名称
  name: Nullable<string> = null
  // 失效时间
  expireDate: Nullable<Date> = null
  // 发券时间
  issueDate: Nullable<Date> = null
  // 用券时间
  useDate: Nullable<Date> = null
  // 用券交易号
  useTranNo: Nullable<string> = null
  // 用券门店
  store: Nullable<string> = null
  // 发券门店
  issueStore: Nullable<string> = null
  // 活动名称
  activityName: Nullable<string> = null
  // 活动id
  activityId: Nullable<string> = null
  // 活动类型
  activityType: Nullable<string> = null
  // 是否可跳转
  canJump: boolean = false
  // 券模板号
  templateNumber: Nullable<string> = null
  // 是否可跳转
  templateCanJump: boolean = true
  // 是否是延期券: 1-是，0-否
  delayCoupon: Nullable<string> = null
  /**
   * 券类型
 * all_cash 现金券
 * all_discount、rfm_type、goods_discount 折扣券
 * special_price 特价券
 * goods 提货券
 * freight 运费券
 * random_cash 随机金额券
 * exchange_goods 兑换券
 * points 积分券
 */
  couponType: Nullable<string> = null
  // 券面额/折扣力度/特价
  faceAmountOrDiscount: Nullable<number> = null
  // 随机金额券开始金额
  randomMinAmount: Nullable<number> = null
  // 随机金额券结束金额
  randomMaxAmount: Nullable<number> = null
}