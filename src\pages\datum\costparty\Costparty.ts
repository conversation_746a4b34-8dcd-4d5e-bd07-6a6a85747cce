import {Component, Vue} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import ListWrapper from 'cmp/list/ListWrapper.vue'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import FloatBlock from 'cmp/floatblock/FloatBlock.vue'
import CostPartyApi from 'http/costparty/CostPartyApi';
import RSCostParty from 'model/common/RSCostParty';
import RSCostPartyFilter from 'model/common/RSCostPartyFilter';
import IdName from 'model/common/IdName';
import RSSaveCostPartyRequest from 'model/common/RSSaveCostPartyRequest';
import BreadCrume from "cmp/bread-crumb/BreadCrume";

@Component({
    name: 'Costparty',
    components: {
        FormItem,
        ListWrapper,
        SubHeader,
        FloatBlock,
        BreadCrume
    }
})
export default class Costparty extends Vue {
    query: RSCostPartyFilter = new RSCostPartyFilter()
    queryData: RSCostParty[] = []
    selected: RSCostParty[] = []
    $refs: any
    panelArray: any = []
    checkedAll: boolean = false
    newIns: RSSaveCostPartyRequest = new RSSaveCostPartyRequest()
    updateIns: RSSaveCostPartyRequest = new RSSaveCostPartyRequest()
    dialogVisible: boolean = false
    // 分页
    page = {
        currentPage: 1,
        total: 0,
        size: 10
    }

    created() {
        this.panelArray = [
            {
                name: this.formatI18n('/公用/菜单/券承担方'),
                url: ''
            },
        ]
        this.newIns.costParty = new RSCostParty()
        this.newIns.costParty.costParty = new IdName()
        this.updateIns.costParty = new RSCostParty()
        this.updateIns.costParty.costParty = new IdName()
        this.getList()
    }

    doSearch() {
        this.page.currentPage = 1
        this.getList()
    }

    doReset() {
        this.query = new RSCostPartyFilter()
        this.page.currentPage = 1
        this.getList()
    }

    /**
     * 查询
     */
    onSearch() {
        this.page.currentPage = 1
        this.getList()
    }

    /**
     * 分页页码改变的回调
     * @param val
     */
    onHandleCurrentChange(val: number) {
        this.page.currentPage = val
        this.getList()
    }

    /**
     * 每页多少条的回调
     * @param val
     */
    onHandleSizeChange(val: number) {
        this.page.size = val
        this.getList()
    }

    /**
     * 表格排序条件
     */
    onSortChange({ column, prop, order }: any) {
        // todo
    }

    handleSelectionChange(val: any) {
        this.selected = val
    }

    deleteBatch() {
        let idSet: string[] = []
        for (const one of this.selected) {
            if (one.costParty && one.costParty.id) {
                idSet.push(one.costParty.id)
            }
        }
        if (idSet.length === 0) {
            this.$message.warning(this.formatI18n('/资料/券承担方/请选择需要删除的记录'))
            return
        }
        this.$confirm(this.formatI18n('/资料/券承担方/是否确认删除？'), this.formatI18n('/资料/券承担方/提示'), {
            confirmButtonText: this.formatI18n('/资料/券承担方/确定'),
            cancelButtonText: this.formatI18n('/资料/券承担方/取消'),
            type: 'warning'
        }).then(() => {
            CostPartyApi.deleteBatch(idSet).then((resp: any) => {
                if (resp && resp.code === 2000) {
                    this.$message.success(this.formatI18n('/资料/券承担方/删除成功'))
                    this.getList()
                }
            }).catch((error) => {
                if (error && error.message) {
                    this.$message.error(error.message)
                }
            })
        });
    }

    private getList() {
        this.query.page = this.page.currentPage - 1
        this.query.pageSize = this.page.size
        CostPartyApi.query(this.query).then((resp: any) => {
            if (resp && resp.code === 2000) {
                this.queryData = resp.data
                this.page.total = resp.total
            }
        }).catch((error) => {
            if (error && error.message) {
                this.$message.error(error.message)
            }
        })
    }

    private add() {
        if (!this.newIns.costParty || !this.newIns.costParty.costParty || !this.newIns.costParty.costParty.id || !this.newIns.costParty.costParty.name) {
            this.$message.error(this.formatI18n('/资料/券承担方/请输入券承担方代码和名称'))
            return
        }
        CostPartyApi.save(this.newIns).then((resp: any) => {
            if (resp && resp.code === 2000) {
                this.$message.success(this.formatI18n('/资料/券承担方/添加成功'))
                this.getList()
                this.clear()
            } else {
                this.$message.error(resp.msg)
            }
        }).catch((error) => {
            if (error && error.message) {
                this.$message.error(error.message)
            }
        })
    }

    private showUpdateDialog(row: RSCostParty) {
        this.updateIns.costParty = JSON.parse(JSON.stringify(row))
        this.dialogVisible = true
    }

    private update() {
        CostPartyApi.modify(this.updateIns).then((resp: any) => {
            if (resp && resp.code === 2000) {
                this.$message.success(this.formatI18n('/资料/券承担方/修改成功'))
                this.getList()
                this.dialogVisible = false
            } else {
                this.$message.error(resp.msg)
            }
        }).catch((error) => {
            if (error && error.message) {
                this.$message.error(error.message)
            }
        })
    }

    private clear() {
        this.newIns.costParty = new RSCostParty()
        this.newIns.costParty.costParty = new IdName()
    }

    private del(id: string) {
        this.$confirm(this.formatI18n('/资料/券承担方/是否确认删除？'), this.formatI18n('/资料/券承担方/提示'), {
            confirmButtonText: this.formatI18n('/资料/券承担方/确定'),
            cancelButtonText: this.formatI18n('/资料/券承担方/取消'),
            type: 'warning'
        }).then(() => {
            CostPartyApi.remove(id).then((resp: any) => {
                if (resp && resp.code === 2000) {
                    this.$message.success(this.formatI18n('/资料/券承担方/删除成功'))
                    this.getList()
                }
            }).catch((error) => {
                if (error && error.message) {
                    this.$message.error(error.message)
                }
            })
        });
    }

    private checkedAllRow() {
        if (this.checkedAll) {
            for (let row of this.queryData) {
                this.$refs.table.toggleRowSelection(row, true)
            }
        } else {
            this.$refs.table.clearSelection();
        }
    }
}
