import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import AllCashCoupon from "cmp/coupontenplate/AllCashCoupon/AllCashCoupon.vue";
import AllDiscountCoupon from "cmp/coupontenplate/AllDiscountCoupon/AllDiscountCoupon.vue";
import GoodsCashCoupon from "cmp/coupontenplate/GoodsCashCoupon/GoodsCashCoupon.vue";
import GoodsDiscountCoupon from "cmp/coupontenplate/GoodsDiscountCoupon/GoodsDiscountCoupon.vue";
import PickUpCoupon from "cmp/coupontenplate/PickUpCoupon/PickUpCoupon.vue";
import SingleDiscountCoupon from "cmp/coupontenplate/SingleDiscountCoupon/SingleDiscountCoupon.vue";
import FormItem from "cmp/formitem/FormItem.vue";
import CouponItem from "model/common/CouponItem";
import CouponInfo from "model/common/CouponInfo";
import StoreRange from "model/common/StoreRange";
import ChannelManagementApi from "http/channelmanagement/ChannelManagementApi";
import RSChannelManagementFilter from "model/common/RSChannelManagementFilter";
import RSChannelManagement from "model/common/RSChannelManagement";
import Channel from "model/common/Channel";
import { SessionStorage, LocalStorage } from "mgr/BrowserMgr";
import ChannelRange from "model/common/ChannelRange";
import FreightCoupon from "cmp/coupontenplate/freightcoupon/FreightCoupon";
import ExchangeGoodsCoupon from "./ExchangeGoodsCoupon/ExchangeGoodsCoupon";
import PointCoupon from "./PointCoupon/PointCoupon";
import CashCoupon from "./CashCoupon/CashCoupon";
import DiscountCoupon from "./DiscountCoupon/DiscountCoupon";
import SpecialCoupon from "./SpecialCoupon/SpecialCoupon";
import RandomCashCoupon from "./RandomCashCoupon/RandomCashCoupon";
import CouponTemplateApi from 'http/coupon/template/CouponTemplateApi'
import ConstantMgr from "mgr/ConstantMgr";
import EquityCoupon from "cmp/coupontenplate/equity/EquityCoupon";

@Component({
  name: "CouponTemplateWrap",
  components: {
    AllCashCoupon,
    AllDiscountCoupon,
    GoodsCashCoupon,
    GoodsDiscountCoupon,
    PickUpCoupon,
    SingleDiscountCoupon,
    FreightCoupon,
    ExchangeGoodsCoupon,
    FormItem,
    PointCoupon,
    CashCoupon,
    DiscountCoupon,
    SpecialCoupon,
    RandomCashCoupon,
    EquityCoupon
  },
})
export default class CouponTemplateWrap extends Vue {
  $refs: any;
  channels: RSChannelManagement[] = [];
  data: CouponItem = new CouponItem();
  isPmsPayEngine = false;
  isShow = true;
  $eventHub: any;
  @Prop({
    type: Boolean,
    default: false,
  })
  baseSettingFlag: boolean;
  @Prop({
    type: String,
    default: "add",
  })
  copyFlag: string; // 是否是复制\新建\编辑
  @Prop()
  sameStore: boolean;
  @Prop({
    type: String,
    default: 'all_cash'
  })
  couponType: string;
  @Prop({
    type: Boolean,
    default: false,
  })
  enableStore: boolean;

  /**
   * all_cash 全场现金券
   * goods_cash 商品现金券
   * all_discount 全场折扣券
   * rfm_type 商品折扣券(未应用)
   * goods_discount 单品折扣券(未应用)
   * goods 提货券
   * random_cash 随机金额券
   * exchange_goods 兑换券
   */
  @Prop({
    type: Array,
    default: () => {
      return [];
    },
  })
  types: string[];
  @Prop()
  state: string;
  @Prop()
  value: CouponItem;

  @Prop({
    type: String,
    default: "400",
  })
  remarkMaxlength: number;

  @Prop({
    type: Boolean,
    default: false,
  })
  weixinCouponHideFiled: boolean; //微信扫码领券活动隐藏 是否展示用券限量、高级备注字段

  @Prop({
    type: Boolean,
    default: false,
  })
  baseFieldEditable: boolean; // 叠加促销、用券记录方式、券承担方、用券顺序 是否可编辑

  @Prop()
  options: {
    // 指定最大券面额，可选配置，用于微信扫码领券
    maxAmount: number; // 指定最大券面额
    maxValidDay: number; // 指定最大券有效天数
    maxUseThreshold: number; // 指定最大用券门槛
    fixedTime: boolean; // 固定用券时段为全部时段
  };

  @Prop({
    type: Boolean,
    default: false,
  })
  wxScanForCoupon: boolean;

  @Prop({ 
    type: Boolean,
    default: true
  })
  showTypeRadio: boolean;

  @Prop({ default :()=>{
    return {
      outerTemplateCode: true, //是否展示外部券模板号
      tags: true //是否展示标签
    }
  }
  }) hideOptions: any;

  @Watch("value", { immediate: true })
  onValueChange(value: CouponItem) {
    if (value && value.coupons) {
      this.data = value;

      if (value.coupons.couponBasicType) {
        this.couponType = value.coupons.couponBasicType as string;
      }
    }
  }

  created() {
    this.getChannels();
    this.getCouponTemplateProperties();
    this.pmsPayEngine();
  }

  changeShow() {
    this.isShow = false;
    setTimeout(() => {
      this.isShow = true;
    }, 10);
  }

  getCouponType(type: string) {
    if (this.types && this.types.length > 0) {
      if (this.types.indexOf(type) >= 0) {
        return true;
      } else {
        return false;
      }
    }
  }

  doTabChange() {
    this.$eventHub.$off("limitChange"); //销毁事件 防止多次调用
    if (this.couponType === "goods_cash") {
      let value: CouponItem = new CouponItem();
      value.coupons = new CouponInfo();
      value.coupons.couponBasicType = "goods_cash" as any;
      this.data = value;
      this.$emit("input", value);
    }
    if (this.couponType === "goods") {
      let value: CouponItem = new CouponItem();
      value.coupons = new CouponInfo();
      value.coupons.couponBasicType = "goods" as any;
      value.coupons.useStores = new StoreRange();
      value.coupons.useChannels = new ChannelRange();
      value.coupons.useChannels.channels = [];
      if (this.sameStore) {
        value.coupons.useStores.storeRangeType = "SAME" as any;
      } else {
        value.coupons.useStores.storeRangeType = "ALL" as any;
      }
      this.data = value;
      this.$emit("input", value);
    }
    // todo
  }

  beforeDestroy() {
    this.$eventHub.$off("limitChange"); //销毁事件 防止多次调用
  }

  doValidate() {
    if (["all_cash"].includes(this.couponType)) {
      let p = new Promise<void>((resolve) => {
        Promise.all(this.$refs.allCashCoupon.doValidate()).then(() => {
          resolve();
        });
      });
      return p;
    } else if (this.couponType === "goods_cash") {
      let p = new Promise<void>((resolve) => {
        Promise.all(this.$refs.goodsCashCoupon.doValidate()).then(() => {
          resolve();
        });
      });
      return p;
    } else if (this.couponType === "all_discount") {
      let p = new Promise<void>((resolve) => {
        Promise.all(this.$refs.allDiscontCoupon.doValidate()).then(() => {
          resolve();
        });
      });
      return p;
    } else if (this.couponType === "rfm_type") {
      let p = new Promise<void>((resolve) => {
        Promise.all(this.$refs.goodsDiscountCoupon.doValidate()).then(() => {
          resolve();
        });
      });
      return p;
    } else if (this.couponType === "goods_discount") {
      let p = new Promise<void>((resolve) => {
        Promise.all(this.$refs.singleDiscountCoupon.doValidate()).then(() => {
          resolve();
        });
      });
      return p;
    } else if (this.couponType === "freight") {
      let p = new Promise<void>((resolve) => {
        Promise.all(this.$refs.freightCoupon.doValidate()).then(() => {
          resolve();
        });
      });
      return p;
    } else if (this.couponType === "exchange_goods") {
      let p = new Promise<void>((resolve) => {
        Promise.all(this.$refs.exchangeGoodsCoupon.doValidate()).then(() => {
          resolve();
        });
      });
      return p;
    } else if (this.couponType === "points") {
      let p = new Promise<void>((resolve) => {
        Promise.all(this.$refs.pointCoupon.doValidate()).then(() => {
          resolve();
        });
      });
      return p;
    } else if (this.couponType === "special_price") {
      let p = new Promise<void>((resolve) => {
        Promise.all(this.$refs.specialCoupon.doValidate()).then(() => {
          resolve();
        });
      });
      return p;
    } else if (this.couponType === "random_cash") {
      let p = new Promise<void>((resolve) => {
        Promise.all(this.$refs.RandomCashCoupon.doValidate()).then(() => {
          resolve();
        });
      });
      return p;
    } else if (this.couponType === "equity") {
      let p = new Promise<void>((resolve) => {
        Promise.all(this.$refs.EquityCoupon.doValidate()).then(() => {
          resolve();
        });
      });
      return p;
    } else {
      let p = new Promise<void>((resolve) => {
        Promise.all(this.$refs.pickUpCoupon.doValidate()).then(() => {
          resolve();
        });
      });
      return p;
    }
  }

  doAllCashChange(channel: any) {
    if (
      this.data &&
      this.data.coupons &&
      ["all_cash"].includes(
        this.data.coupons.couponBasicType as string
      )
    ) {
      this.$emit("input", this.translateCouponName(this.data, channel));
    }
  }

  doEquityChange(channel: any) {
    if (
        this.data &&
        this.data.coupons &&
        ["equity"].includes(
            this.data.coupons.couponBasicType as string
        )
    ) {
      this.$emit("input", this.translateCouponName(this.data, channel));
    }
  }

  doSpecialChange(channel: any) {
    if (
      this.data &&
      this.data.coupons &&
      ["special_price"].includes(this.data.coupons.couponBasicType as string)
    ) {
      this.$emit("input", this.translateCouponName(this.data, channel));
    }
  }

  doRandomCashChange(channel: any) {
    if (
      this.data &&
      this.data.coupons &&
      ["random_cash"].includes(this.data.coupons.couponBasicType as string)
    ) {
      this.$emit("input", this.translateCouponName(this.data, channel));
    }
  }

  doFreightChange(channel: any) {
    if (
      this.data &&
      this.data.coupons &&
      this.data.coupons.couponBasicType === "freight"
    ) {
      this.$emit("input", this.translateCouponName(this.data, channel));
    }
  }

  doGoodsCashChange(channel: any) {
    if (
      this.data &&
      this.data.coupons &&
      this.data.coupons.couponBasicType === "goods_cash"
    ) {
      this.$emit("input", this.translateCouponName(this.data, channel));
    }
  }

  doAllDiscountChange(channel: any) {
    if (
      this.data &&
      this.data.coupons &&
      this.data.coupons.couponBasicType === "all_discount"
    ) {
      this.$emit("input", this.translateCouponName(this.data, channel));
    }
  }

  doGoodsDiscountChange(channel: any) {
    if (
      this.data &&
      this.data.coupons &&
      this.data.coupons.couponBasicType === "rfm_type"
    ) {
      this.$emit("input", this.translateCouponName(this.data, channel));
    }
  }

  doSingleDiscountChange(channel: any) {
    if (
      this.data &&
      this.data.coupons &&
      this.data.coupons.couponBasicType === "goods_discount"
    ) {
      this.$emit("input", this.translateCouponName(this.data, channel));
    }
  }

  doPickUpChange(channel: any) {
    if (
      this.data &&
      this.data.coupons &&
      this.data.coupons.couponBasicType === "goods"
    ) {
      this.$emit("input", this.translateCouponName(this.data, channel));
    }
  }

  doExchangeGoodsChange(channel: any) {
    if (
      this.data &&
      this.data.coupons &&
      this.data.coupons.couponBasicType === "exchange_goods"
    ) {
      this.$emit("input", this.translateCouponName(this.data, channel));
    }
  }

  doPointCouponChange(channel: any) {
    if (
      this.data &&
      this.data.coupons &&
      this.data.coupons.couponBasicType === "points"
    ) {
      this.$emit("input", this.translateCouponName(this.data, channel));
    }
  }

  private translateCouponName(data: CouponItem, channels: any) {
    if (data && data.coupons && data.coupons.remark) {
      if (!data.qty) {
        data.qty = 1;
      }
      if (
        data &&
        data.coupons &&
        data.coupons.useChannels &&
        data.coupons.useChannels.channelRangeType === "PART"
      ) {
        if (
          data.coupons.useChannels.channels &&
          data.coupons.useChannels.channels.length > 0
        ) {
          let arrs: any = [];
          data.coupons!.useChannels!.channels.forEach((item: any) => {
            if (channels && channels.length > 0) {
              channels.forEach((sub: RSChannelManagement) => {
                if (sub.channel && item === sub.channel.typeId) {
                  let channel: Channel = new Channel();
                  channel.type = sub.channel.type;
                  channel.id = sub.channel.id;
                  channel.typeId = sub.channel.typeId;
                  arrs.push(channel);
                }
              });
            } else {
              channels = SessionStorage.getItem("channels");
              channels.forEach((sub: RSChannelManagement) => {
                if (sub.channel && item === sub.channel.typeId) {
                  let channel: Channel = new Channel();
                  channel.type = sub.channel.type;
                  channel.id = sub.channel.id;
                  arrs.push(channel);
                }
              });
            }
          });
          data.coupons!.useChannels!.channels = arrs;
        }
      }
    }
    return data;
  }

  private getChannels() {
    const loading = this.$loading(ConstantMgr.loadingOption)
    let param: RSChannelManagementFilter = new RSChannelManagementFilter();
    param.stateEquals = "ENABLED";
    ChannelManagementApi.query(param)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.channels = resp.data;
          SessionStorage.setItem("channels", this.channels);
        }
      })
      .catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      }).finally(()=>{
        loading.close()
      })
  }

  private pmsPayEngine(){
    CouponTemplateApi.pmsPayEngine()
    .then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.isPmsPayEngine = resp.data;
      }
    })
    .catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message);
      }
    });
  }

  private getCouponTemplateProperties() {
    CouponTemplateApi.getCouponTemplateProperties()
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          LocalStorage.setItem("superpositionVersion", resp.data.superpositionVersion);
          LocalStorage.setItem('templateValidateLimit', resp.data.templateValidateLimit);
          LocalStorage.setItem('accountItemRange', resp.data.accountItemRange);
          if (resp.data.appreciationGoods === true) {
            LocalStorage.setItem('appreciationGoods', true)
          } else {
            LocalStorage.setItem('appreciationGoods', false)
          }
        }
      })
      .catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
  }
}
