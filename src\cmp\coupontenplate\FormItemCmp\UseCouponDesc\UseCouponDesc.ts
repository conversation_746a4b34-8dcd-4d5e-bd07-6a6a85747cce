import I18nPage from 'common/I18nDecorator';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  name: 'UseCouponDesc'
})
@I18nPage({
  prefix: [
    "/公用/券模板",
    '/营销/券礼包活动/核销第三方券'
  ],
  auto: true
})
export default class UseCouponDesc extends Vue {
  @Prop()
  ruleForm: any;

  @Prop()
  copyFlag: 'add' | 'edit' | 'copy';

  @Prop({
    type: Boolean,
    default: false
  })
  isShowTips: boolean;

  @Prop({
    type: Boolean,
    default: true
  }) descRequired: boolean;

  rules: any = []

  created() {
    this.rules = [
      { required: this.descRequired, message: this.i18n("请输入必填项"), trigger: "blur" },
    ]
  }
};