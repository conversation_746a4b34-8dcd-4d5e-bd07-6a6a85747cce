<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:03
 * @LastEditTime: 2024-09-29 15:11:58
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\custom\dqsh\member\dialog\AdjustMemberLevelDialog.vue
 * 记得注释
-->
<template>
  <el-dialog :before-close="doBeforeClose" :close-on-click-modal="false" :title="title" :visible.sync="dialogShow" append-to-body
    class="adjust-member-level-dialog">
    <div class="wrap">
      <el-form :model="ruleForm" :rules="rules" class="demo-ruleForm" label-width="100px" ref="ruleForm">
        <el-form-item label="当前等级">
          <el-input disabled v-model="ruleForm.level"></el-input>
        </el-form-item>
        <el-form-item label="调整后等级" prop="adjustLevel">
          <el-select @change="doLevelChange" placeholder="请选择" v-model="ruleForm.adjustLevel">
            <el-option :label="item.name" :key="item.code" :value="item.code" v-for="item in memberLevel">[{{item.code}}]{{item.name}}</el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="当前等级有效期">
          <el-date-picker disabled format="yyyy-MM-dd" placeholder="请选择日期" style="width: 100%;" v-model="ruleForm.curLevel" value-format="yyyy-MM-dd">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="调整后等级有效期" prop="adjustDate">
          <el-date-picker :disabled="adjustDateDisabled" format="yyyy-MM-dd" placeholder="选择日期" style="width: 100%;" v-model="ruleForm.adjustDate"
            value-format="yyyy-MM-dd">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="调整后说明">
          <el-input maxlength="50" placeholder="请输入不超过50个字" type="textarea" v-model="ruleForm.remark"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div class="dialog-footer" slot="footer">
      <el-button @click="doCancel">取 消</el-button>
      <el-button @click="doModalClose" size="small" type="primary">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./AdjustMemberLevelDialog.ts">
</script>
<style lang="scss">
.adjust-member-level-dialog {
  display: flex;
  align-items: center;
  justify-content: center;
  .wrap {
    .el-form-item__label {
      width: 140px !important;
    }
    .el-form-item__content {
      margin-left: 140px !important;
    }
    .el-select {
      width: 100% !important;
    }
  }
  .el-dialog {
    width: 500px;
  }
}
</style>