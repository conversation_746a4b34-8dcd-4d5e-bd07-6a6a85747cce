<template>
  <el-dialog :title="formatI18n('/公用/公共组件/积分活动选择弹框组件/标题/选择活动')" class="select-coupon-template-tpl-dialog" append-to-body :close-on-click-modal="false"
    :visible.sync="dialogShow" v-if="dialogShow">
    <div class="wrap">
      <el-row>
        <el-form :label-width="labelWidth">
          <el-row>
            <el-col :span="8">
              <el-form-item :label="formatI18n('/营销/券礼包活动/券礼包活动', '活动名称')">
                <el-input v-model="filter.nameLike" :placeholder="formatI18n('/公用/查询条件/提示/类似于')" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="formatI18n('/营销/券礼包活动/券礼包活动/活动号')">
                <el-input v-model="filter.numberEquals" :placeholder="formatI18n('/营销/券礼包活动/券查询/券号输入框placeholder/等于')" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item>
                <el-button type="primary" @click="doSearch()">{{ formatI18n('/公用/按钮/查询') }}</el-button>
                <el-button @click="doReset()">{{ formatI18n('/公用/按钮/重置') }}</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-row>
      <el-row>
        <el-col :span="model === 'multiple' ? 17 : 24">
          <el-row class="table-wrap" v-loading="loading.query">
            <div class="thead" style="display: flex;">
              <div style="flex: 1;">
                <el-checkbox style="margin-left: 8px;" v-if="model === 'multiple'" @change="doCheckAll($event)" v-model="checkAll" />
                <span v-else>&nbsp;</span>
              </div>
              <div style="flex: 4">{{ formatI18n('/营销/券礼包活动/券礼包活动', '活动号') }}</div>
              <div style="flex: 3">{{ formatI18n('/营销/券礼包活动/券礼包活动', '活动名称') }}</div>
              <div style="flex: 3">{{ formatI18n('/营销/券礼包活动/核销第三方券', '活动类型') }}</div>
            </div>
            <el-row class="tbody" v-if="!loading.query" style="overflow-x:auto">
              <template v-if="currentList && currentList.length > 0">
                <div v-for="(item, index) of currentList" :key="index" style="display: flex;" class="trow">
                  <!-- 勾选框 -->
                  <div style="flex: 1;">
                    <div class="height-set">
                      <span style="position: relative;top: 18px;left: 8px;">
                        <el-checkbox
                          :disabled="alwaysSelectedIds.includes(item.activityId)"
                          v-model="checkboxList[index]" @change="doCheck($event, index)" />
                      </span>
                    </div>
                  </div>
                  <!-- 活动号 -->
                  <div @click="doCheckRow(index)" style="flex: 3;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 100%;"
                       :title="item.activityId" class="height-set line-height-set">
                    {{ item.activityId }}
                  </div>
                  <!-- 活动名称 -->
                  <div @click="doCheckRow(index)" style="flex: 3;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 100%;"
                       :title="item.name" class="height-set line-height-set">
                    {{ item.name}}
                  </div>
                  <!-- 活动类型 -->
                  <div @click="doCheckRow(index)" style="flex: 3;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 100%;"
                    :title="item.type | lineCouponType" class="height-set line-height-set">
                    {{ item.type | allActivityType }}
                  </div>
                </div>
              </template>
              <el-row v-if="!currentList || currentList.length === 0" class="trow" style="text-align: center;color: #909399">
                {{ formatI18n('/公用/提示/暂无数据') }}
              </el-row>
            </el-row>
          </el-row>
        </el-col>
        <el-col :span="model === 'multiple' ? 7 : 0" style="padding-left: 5px">
          <el-row class="right-table">
            <el-row class="thead">
              {{ formatI18n('/公用/公共组件/积分活动选择弹框组件/表格/已选活动：') }}{{
                selected ? (selected.filter(e => e.activityId)).length : 0
              }}
            </el-row>
            <el-row style="padding: 5px;">
              <el-input style="width: 100%;" @keyup.enter.native="filterSelected()" @change="filterSelected()" v-model="selectedFilter" clearable
                @clear="filterSelected()" :placeholder="formatI18n('/营销/券礼包活动/券礼包活动/活动名称')"></el-input>
            </el-row>
            <el-row class="tbody">
              <template v-if="filteredSelected && filteredSelected.length > 0">
                <el-row class="trow" style="position: relative;display: flex;align-items: center" v-for="(item, index) of filteredSelected"
                  :key="index" :title="{id:item.activityId,name:item.name}|idName">
                  <div class="left">{{ {id: item.activityId, name: item.name}|idName }}</div>
                  <div class="clear-btn" style="display: none">
                    <a @click="delItem(item, index)" v-if="!alwaysSelectedIds.includes(item.activityId)">{{ formatI18n('/公用/公共组件/品牌选择弹框组件/表格/清除') }}</a>
                  </div>
                </el-row>
              </template>
              <el-row v-if="!filteredSelected || filteredSelected.length === 0" class="trow" style="text-align: center;color: #909399">
                {{ formatI18n('/公用/提示/暂无数据') }}
              </el-row>
            </el-row>
          </el-row>
        </el-col>
      </el-row>
    </div>
    <div class="page" style="margin-top: 15px">
      <el-pagination :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40, 100]" :total="page.total"
        @current-change="handleCurrentChange($event)" @size-change="handleSizeChange($event)" background layout="total, prev, pager, next, sizes,  jumper">
      </el-pagination>
    </div>
    <div slot="footer" class="dialog-footer" style="position: relative;top: -27px;">
      <el-button size="small" @click="doCancel()">{{ formatI18n('/公用/按钮/取消') }}</el-button>
      <el-button size="small" type="primary" @click="doModalClose()">{{ formatI18n('/公用/按钮/确定') }}</el-button>
    </div>
<!--    <SelectStoreActiveDtlDialog :baseSettingFlag="true" :child="child" :dialogShow="couponDialogShow" @dialogClose="doCouponDialogClose">-->
<!--    </SelectStoreActiveDtlDialog>-->
  </el-dialog>
</template>

<script lang="ts" src="./ActivitySelectorDialog.ts"/>

<style lang="scss" scoped>
.select-coupon-template-tpl-dialog {
  display: flex;
  align-items: center;
  justify-content: center;
  @import "SelectorDialogCommon";

  .height-set {
    height: 68px;
    border-bottom: 1px solid #eeeeee;
  }

  .line-height-set {
    line-height: 68px;
    padding-left: 7px;
  }

  .wrap {
    height: 615px;
  }

  .wrap .table-wrap .tbody .trow {
    height: 68px;
    line-height: 34px;
  }

  .overflow_text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }
}
</style>
<style>
.short-label .el-form-item__label {
  width: 100px !important;
}
.select-coupon-template-tpl-dialog .el-dialog {
  width: 1407px !important;
  height: 800px !important;
  margin-top: 0 !important;
}
</style>
