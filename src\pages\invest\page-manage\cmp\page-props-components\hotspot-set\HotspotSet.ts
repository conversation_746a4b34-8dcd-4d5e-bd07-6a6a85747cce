import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import emitter from 'util/emitter';
import { FormMode } from 'model/local/FormMode';
import FormDefUtil from 'util/FormDefUtil';
import TargetPage from 'model/local/TargetPage';
import I18nPage from 'common/I18nDecorator';
import { CmsConfigChannel } from 'model/template/CmsConfig';

interface PropType {
  leftVertexMarginTop: number;
  leftVertexMarginLeft: number;
  hotZoneHeight: number;
  hotZoneWidth: number;
  zIndex: number;
  validateResult: string;
}

@Component({
  name: 'HotspotSet',
  mixins: [emitter],
  components: {  },
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理',
    '/页面/导航设置',
  ],
  auto: true
})
export default class HotspotSet extends Vue {
  @Prop({ type: Boolean, default: false })
  readonly: boolean; //
  @Prop({ type: String, default: 'HotspotSet' })
  validateName: string; // 组件校验名称，editPage通过该名称进行组件校验及校验信息清除
  @Prop({ type: Object })
  value: any; // 数据模型
  @Prop({ type: String, default: '热区设置' })
  label: string; // label名
  @Prop()
  formKey: any;
  @Prop() advertiseChannel: Nullable<CmsConfigChannel[]>; //当前页面可用的投放渠道
  FormModeType = FormMode;
  $refs: any;
  labelPosition: string = 'left';

  isControl: boolean = false;
  isShow: boolean = false;
  rules = {
    propItems: [
      {
        required: true,
        message: this.i18n('请设置热区'),
        trigger: ['blur', 'change'],
      },
    ],
  };

  form: { propItems: PropType | null; hotspotSet: string } = {
    hotspotSet: this.i18n('按钮'),
    propItems: {
      // targetPage: new TargetPage(),
      leftVertexMarginTop: 0,
      leftVertexMarginLeft: 0,
      hotZoneHeight: 88,
      hotZoneWidth: 88,
      zIndex: 0,
      validateResult: 'false',
    },
  };

  get spotSet() {
    return this.$store.state.spotSet;
  }

  // 监听热区图位置大小更新
  @Watch('spotSet', { deep: true })
  handleSpotSet(val: any) {
    this.isControl = false;
    this.form.propItems = {
      // targetPage: {
      //   targetPage: val.item.targetPage,
      //   pageParams: val.item.pageParams,
      // },
      leftVertexMarginTop: val.item.leftVertexMarginTop,
      leftVertexMarginLeft: val.item.leftVertexMarginLeft,
      hotZoneHeight: val.item.hotZoneHeight,
      hotZoneWidth: val.item.hotZoneWidth,
      zIndex: val.item.zIndex,
      validateResult: val.item.validateResult,
    };

    this.$nextTick(() => {
      this.$refs.jumpPage.validate((val: any) => {
        console.log('valvalval', val);
      });
    });
  }

  @Watch('value', { deep: true, immediate: true })
  handleValue(value: any) {
    const val = value.propItems.find((value: any) => {
      return value.isClick;
    });
    if (!val) return;
    if (this.isControl) return;
    this.form.propItems = {
      // targetPage: {
      //   targetPage: val.targetPage,
      //   pageParams: val.pageParams,
      // },
      leftVertexMarginTop: val.leftVertexMarginTop,
      leftVertexMarginLeft: val.leftVertexMarginLeft,
      hotZoneHeight: val.hotZoneHeight,
      hotZoneWidth: val.hotZoneWidth,
      zIndex: val.zIndex,
      validateResult: val.validateResult,
    };
  }

  get formMode() {
    if (this.validateName === 'propMarginBottom') {
      return FormDefUtil.getFormMode(this.readonly, this.validateName, this.formKey);
    } else {
      return FormDefUtil.getFormMode(
        this.readonly,
        this.validateName + 'propMarginBottom',
        this.formKey
      );
    }
  }

  get imageAdProp() {
    return this.$store.state.imageAdProp;
  }

  // 监听热区跳转页面属性变化
  handleChange() {
    if (this.form.propItems) {
      const currentComponent = this.value.propItems.find(
        (current: any) => current.zIndex === this.form.propItems?.zIndex
      );
      // currentComponent.targetPage = this.form.propItems.targetPage.targetPage;
      // currentComponent.pageParams = this.form.propItems.targetPage.pageParams;
      this.$nextTick(() => {
        this.$refs.jumpPage.validate((flag: any) => {
          currentComponent.validateResult = flag;
        });
      });
    }

    this.handleInput();
  }

  // 新增热区
  addHotSpot() {
    this.isControl = true;
    if (!this.value.propImageUrl) {
      this.$message({
        message: this.i18n('请先上传入口图片'),
        type: 'warning',
      });
      return false;
    }

    if (this.value.propItems.length >= 10) {
      this.$message.error(this.i18n('热区数量已达上限'));
      return;
    }

    const propItem: any = {
      targetPage: '',
      pageParams: {},
      leftVertexMarginTop: 0,
      leftVertexMarginLeft: 0,
      hotZoneHeight: 88,
      hotZoneWidth: 88,
      zIndex: this.value.propItems.length,
      isClick: true,
    };

    if (!this.value.propItems) {
      this.$set(this.value, 'propItems', []);
    }

    this.form.propItems = {
      // targetPage: {
      //   targetPage: propItem.targetPage,
      //   pageParams: propItem.pageParams,
      // },
      leftVertexMarginTop: propItem.leftVertexMarginTop,
      leftVertexMarginLeft: propItem.leftVertexMarginLeft,
      hotZoneHeight: propItem.hotZoneHeight,
      hotZoneWidth: propItem.hotZoneWidth,
      zIndex: propItem.zIndex,
      validateResult: propItem.validateResult,
    };

    this.value.propItems.push(propItem);
    this.handleInput();
  }

  // 清空热区
  delHotSpot() {
    if (this.value.propItems.length === 0) return;
    this.$confirm(this.i18n('确定清空当前已添加的热区?'), this.i18n('提示'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消'),
    })
      .then(() => {
        this.form.propItems = null;
        this.value.propItems = [];
        this.handleInput();
      })
      .catch(() => {});
  }

  // 处理双向绑定
  handleInput() {
    // if(this.value.propItems.some(item => item.validateResult === false)) {

    // }
    // this.$bus.emit('cms.edit.addForm', this);
    this.value.propImageHeight = this.imageAdProp.propImageHeight;
    this.value.propImageWidth = this.imageAdProp.propImageWidth;
    console.log('this.value', this.value);
    this.$emit('input', this.value);
    this.$emit('change', this.value);
    this.validate(() => {});
  }

  mounted() {
    // this.$bus.emit('cms.edit.addForm', this);
  }

  validate(callback: any) {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.validate(callback);
    }
  }

  clearValidate() {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.clearValidate();
    }
  }
}
