import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import UserGroupMark from 'model/precisionmarketing/group/mark/UserGroupMark'
import UserGroupMarkBody from 'model/precisionmarketing/group/mark/UserGroupMarkBody'
import UserGroupMarkFilter from 'model/precisionmarketing/group/mark/UserGroupMarkFilter'

export default class UserGroupMarkApi {
  /**
   * 删除客群标记信息
   * 
   */
  static delete(uuid: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/precision-marketing/user-group-mark/delete/${uuid}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询客群标记信息
   * 
   */
  static query(body: UserGroupMarkFilter): Promise<Response<UserGroupMark[]>> {
    return ApiClient.server().post(`/v1/precision-marketing/user-group-mark/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建客群标记信息
   * 
   */
  static saveNew(body: UserGroupMarkBody): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/precision-marketing/user-group-mark/saveNew`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
