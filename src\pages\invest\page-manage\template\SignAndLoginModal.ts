/*
 * @Author: shikailei
 * @Date: 2025-07-15 14:00:15
 * @LastEditTime: 2025-07-15 14:50:22
 * @LastEditors: shikailei
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\template\SignAndLoginModal.ts
 * 记得注释
 */

// H5登录注册
export default class SignAndLoginModal {
  /** 组件id */
  id: string = 'signAndLogin'
   /** 组件类型 */
  type: 'other'
  /** 顶部标题样式：文字或者图片，前端自行约定枚举值 */
  topTitleStyle: Nullable<string> = 'text'
  /** 顶部标题内容，顶部标题样式为文字时使用 */
  topTitle: Nullable<string> = 'H5登录注册'
  /** 顶部图片，顶部标题为图片时使用 */
  topImage: Nullable<string> = ''
  /** 登录方式 */
  loginTypes: string[] = ['mobile']
  /** 按钮背景色 */
  buttonBgColor: Nullable<string> = '#007AFF'
  /** 按钮字体色 */
  buttonFontColor: Nullable<string> = '#FFFFFF'
  /** 按钮文字 */
  buttonText: Nullable<string> = '继续'
  /** 发送验证码提示.文案颜色 */
  sendCodeTipFontColor: Nullable<string> = '#999999'
  /** 选中状态颜色 */
  activeColor: Nullable<string> = '#ff4444'
  /** 是否显示国家选择器 */
  showCountrySelector: boolean = true
  /** 说明内容 */
  description: Nullable<string> = ''
}