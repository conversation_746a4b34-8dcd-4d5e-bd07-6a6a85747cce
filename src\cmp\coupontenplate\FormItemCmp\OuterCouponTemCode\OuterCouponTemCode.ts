import I18nPage from 'common/I18nDecorator';
import ChannelManagement<PERSON>pi from 'http/channelmanagement/ChannelManagementApi';
import OrgApi from 'http/org/OrgApi';
import RSChannelManagementFilter from 'model/common/RSChannelManagementFilter';
import CouponTemplateOuterRelation from 'model/coupon/template/CouponTemplateOuterRelation';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
@Component({
  name: 'OuterCouponTemCode'
})
@I18nPage({
  prefix: [
    '/公用/券模板'
  ],
  auto: true
})
export default class OuterCouponTemCode extends Vue {
  @Prop() value: CouponTemplateOuterRelation;
  @Prop() copyFlag: 'add' | 'edit' | 'copy';
  platformList: any[] = []; //渠道选项
  rules: any = {}
  propsCascader = {
    value: 'value',
    label: 'name',
    children: 'children',
    disabled: 'disabled'
  }
  ruleForm: any = {
    selectCascader: [],  //已选择的渠道
    templateCode: '', //外部券模板号 
    channelName: ''
  }
  $refs: any

  @Watch('value', { deep: true,immediate:true })
  handle() {
    if (this.value) {
      if (this.value.channel?.type && this.value.channel?.id) {
        this.ruleForm.selectCascader = [];
        this.ruleForm.selectCascader.push(this.value.channel?.type);
        this.ruleForm.selectCascader.push(this.value.channel?.id)
      }
      this.ruleForm.templateCode = this.value.outerNumber || ''
      this.ruleForm.channelName = this.value.channelName || ''
    }
  }

  async created() {
    if (this.copyFlag !== 'edit') {
      await this.getPlatform()
      this.rules = {
        cascader: [
          {
            validator: (rule: any, value: any, callback: any) => {
              if (this.ruleForm.templateCode && (!this.ruleForm.selectCascader || this.ruleForm.selectCascader.length < 2)) {
                callback(this.formatI18n("/公用/券模板", "请输入必填项"));
              }
              callback()
            },
            trigger: "blur",
          },
        ],
        outerCode: [
          {
            validator: (rule: any, value: any, callback: any) => {
              if ((this.ruleForm.selectCascader && this.ruleForm.selectCascader.length > 0) && !this.ruleForm.templateCode) {
                callback(this.formatI18n("/公用/券模板", "请输入必填项"));
              }
              callback()
            },
            trigger: "blur",
          },
        ],
      }
    }
  }

  async getPlatform() {
    this.platformList = []
    try {
      const res: any = await OrgApi.getPlatform()
      if (res.data && res.data.length > 0) {
        res.data.forEach((item: any, index: any) => {
          this.platformList.push({
            value: item.channelType,
            name: item.platForm.name,
            disabled: false,
            children: []
          });
          this.queryPlateId(item.channelType, index)
        });
      }
    } catch (err) {
      console.log(err)
    }
  }


  async queryPlateId(type: string, flagIndex: number) {
    let param: RSChannelManagementFilter = new RSChannelManagementFilter();
    param.channelTypeEquals = type
    param.page = 0
    param.pageSize = 50
    try {
      const { data } = await ChannelManagementApi.query(param)
      this.platformList[flagIndex].children = (data || []).reduce((acc: any, cur: any) => {
        acc.push({
          value: cur.channel.id,
          name: "[" + cur.channel.id + "]" + cur.name ,
          channelName: cur.name
        })
        return acc
      }, [])
      this.$forceUpdate()
    } catch (err) {
      console.log(err)
    }
  }

  doValidate() {
    let promise = new Promise<void>((resolve, reject) => {
      this.$refs.ruleForm.validate((valid: any) => {
        if (valid) {
          resolve();
        } else {
          reject();
        }
      });
    });
    return promise;
  }

  doFormChange() {
    const data = {
      outerNumber: this.ruleForm.templateCode,
      channel: {
        type: this.ruleForm.selectCascader[0],
        id: this.ruleForm.selectCascader[1]
      },
      innerNumber: null
    }
    this.$emit('change', data)
  }

  doClear() {
    this.ruleForm.selectCascader = []
    this.ruleForm.templateCode = ''
    this.ruleForm.channelName = ''
    this.doFormChange()
  }
};