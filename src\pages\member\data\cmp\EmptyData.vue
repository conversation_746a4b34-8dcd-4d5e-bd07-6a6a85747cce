<template>
  <div class="empty-data">
    <img src="~assets/image/member/<EMAIL>">
    <div class="tip">{{ i18n('暂无数据') }}</div>
  </div>
</template>

<script lang="ts"
        src="./EmptyData.ts">
</script>

<style lang="scss"
       scoped>
.empty-data {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  img {
    display: inline-block;
    width: 96px;
    height: 96px;
  }

  .tip {
    font-weight: 400;
    font-size: 12px;
    color: #79879E;
    line-height: 18px;
    margin-top: 4px;
  }
}
</style>
