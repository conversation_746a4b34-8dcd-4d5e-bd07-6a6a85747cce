import ApiClient from "http/ApiClient";
import CouponFilter from "model/report/query/coupon/CouponFilter";
import CouponIssueDailyHst from "model/report/query/coupon/issue/CouponIssueDailyHst";
import CouponIssueHst from "model/report/query/coupon/issuehst/CouponIssueHst";
import CouponList from "model/report/query/coupon/list/CouponList";
import PresentHst from "model/report/query/coupon/list/PresentHst";
import CouponStatus from "model/report/query/coupon/CouponStatus";
import CouponUseDailyHst from "model/report/query/coupon/use/CouponUseDailyHst";
import CouponUseHst from "model/report/query/coupon/usehst/CouponUseHst";
import IssueCouponGoodsHst from "model/report/query/coupon/issuegoods/IssueCouponGoodsHst";
import Response from "model/common/Response";
import UseCouponGoodsHst from "model/report/query/coupon/usegoodshst/UseCouponGoodsHst";
import CancelBody from "model/report/query/coupon/list/CancelBody";
import CancelResult from "model/report/query/coupon/list/CancelResult";
import CouponExportFilter from "model/report/query/coupon/CouponExportFilter";
import IssueCouponData from "model/report/query/coupon/issue/IssueCouponData";
import CouponPresentHstFilter from "model/report/query/coupon/list/CouponPresentHstFilter";
import PresentHstDtl from "model/report/query/coupon/list/PresentHstDtl";
import PlatformCouponUseDetailReportFilter from "model/report/query/coupon/PlatformCouponUseDetailReportFilter";
import PlatformCouponUseDetailReport from "model/report/query/coupon/PlatformCouponUseDetailReport";

export default class CouponReportApi {
	/**
	 * 作废券
	 * 作废券。
	 *
	 */
	static cancelCoupon(body: CancelBody): Promise<Response<CancelResult>> {
		return ApiClient.server()
			.post(`/v1/coupon/report/cancelCoupon`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 券转增流水
	 * 券转增流水。
	 *
	 */
  static getPresentHst(body: CouponPresentHstFilter): Promise<Response<PresentHstDtl[]>> {
		return ApiClient.server()
      .post(`/v1/coupon/report/queryPresentHst`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 券列表查询
	 * 券列表查询。
	 *
	 */
	static queryCouponList(body: CouponFilter): Promise<Response<CouponList[]>> {
		return ApiClient.server()
			.post(`/v1/coupon/report/queryCouponList`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 发券日报
	 * 发券日报。
	 *
	 */
	static queryIssueDaily(body: CouponFilter): Promise<Response<CouponIssueDailyHst[]>> {
		return ApiClient.server()
			.post(`/v1/coupon/report/queryIssueDaily`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 发券商品明细
	 * 发券商品明细。
	 *
	 */
	static queryIssueGoodsHst(body: CouponFilter): Promise<Response<IssueCouponGoodsHst[]>> {
		return ApiClient.server()
			.post(`/v1/coupon/report/queryIssueGoodsHst`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 发券流水
	 * 发券流水。
	 *
	 */
	static queryIssueHst(body: CouponFilter): Promise<Response<CouponIssueHst[]>> {
		return ApiClient.server()
			.post(`/v1/coupon/report/queryIssueHst`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 用券日报
	 * 用券日报。
	 *
	 */
	static queryUseDaily(body: CouponFilter): Promise<Response<CouponUseDailyHst[]>> {
		return ApiClient.server()
			.post(`/v1/coupon/report/queryUseDaily`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 用券商品明细
	 * 用券商品明细。
	 *
	 */
	static queryUseGoodsHst(body: CouponFilter): Promise<Response<UseCouponGoodsHst[]>> {
		return ApiClient.server()
			.post(`/v1/coupon/report/queryUseGoodsHst`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 用券流水
	 * 用券流水。
	 *
	 */
	static queryUseHst(body: CouponFilter): Promise<Response<CouponUseHst[]>> {
		return ApiClient.server()
			.post(`/v1/coupon/report/queryUseHst`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 发券日报合计
	 * 发券日报合计。
	 *
	 */
	static statusIssueDaily(body: CouponFilter): Promise<Response<number>> {
		return ApiClient.server()
			.post(`/v1/coupon/report/statusIssueDaily`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 用券流水合计
	 * 用券流水合计。
	 *
	 */
	static statusUseHst(body: CouponFilter): Promise<Response<CouponStatus>> {
		return ApiClient.server()
			.post(`/v1/coupon/report/statusUseHst`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 用券日报合计
	 * 用券日报合计。
	 *
	 */
	static statusUseDaily(body: CouponFilter): Promise<Response<CouponStatus>> {
		return ApiClient.server()
			.post(`/v1/coupon/report/statusUseDaily`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 券列表导出
	 * 券列表导出。
	 *
	 */
	static exportCouponHst(body: CouponFilter): Promise<Response<void>> {
		return ApiClient.server()
			.post(`/v1/coupon/report/exportCouponHst`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 用券日报导出
	 * 用券日报导出。
	 *
	 */
	static exportDailyUse(body: CouponFilter): Promise<Response<void>> {
		return ApiClient.server()
			.post(`/v1/coupon/report/exportDailyUse`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 发券日报导出
	 * 发券日报导出。
	 *
	 */
	static exportIssueDaily(body: CouponFilter): Promise<Response<void>> {
		return ApiClient.server()
			.post(`/v1/coupon/report/exportIssueDaily`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 发券商品导出
	 * 发券商品导出。
	 *
	 */
	static exportIssueGoods(body: CouponFilter): Promise<Response<void>> {
		return ApiClient.server()
			.post(`/v1/coupon/report/exportIssueGoods`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 发券流水导出
	 * 发券流水导出。
	 *
	 */
	static exportIssueHst(body: CouponFilter): Promise<Response<void>> {
		return ApiClient.server()
			.post(`/v1/coupon/report/exportIssueHst`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 用券商品导出
	 * 用券商品导出。
	 *
	 */
	static exportUseGoods(body: CouponFilter): Promise<Response<void>> {
		return ApiClient.server()
			.post(`/v1/coupon/report/exportUseGoods`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 用券流水导出
	 * 用券流水导出。
	 *
	 */
	static exportUseHst(body: CouponFilter): Promise<Response<void>> {
		return ApiClient.server()
			.post(`/v1/coupon/report/exportUseHst`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 获取是否统计和展现用券、发券人数
	 *
	 */
	static getEnableMemberCount(): Promise<Response<boolean>> {
		return ApiClient.server()
			.get(`/v1/coupon/report/getEnableMemberCount`, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 发券日报数据统计
	 * 发券日报数据统计。
	 *
	 */
	static queryIssueCouponData(body: CouponFilter): Promise<Response<IssueCouponData>> {
		return ApiClient.server()
			.post(`/v1/coupon/report/queryIssueCouponData`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 发券流水统计
	 * 发券流水统计。
	 *
	 */
	static queryIssueHstData(body: CouponFilter): Promise<Response<IssueCouponData>> {
		return ApiClient.server()
			.post(`/v1/coupon/report/queryIssueHstData`, body, {})
			.then((res) => {
				return res.data;
			});
	}

  /**
  * 平台券核销统计
  * 平台券核销统计。
  *
  */
  static queryUseDetailReport(body: PlatformCouponUseDetailReportFilter): Promise<Response<PlatformCouponUseDetailReport[]>> {
    return ApiClient.server()
      .post(`/v1/coupon/report/queryUseDetailReport`, body, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
  * 平台券核销导出
  * 平台券核销导出。
  *
  */
  static exportUseDetailReport(body: PlatformCouponUseDetailReportFilter): Promise<Response<PlatformCouponUseDetailReport[]>> {
    return ApiClient.server()
      .post(`/v1/coupon/report/exportUseDetailReport`, body, {})
      .then((res) => {
        return res.data;
      });
  }
}
