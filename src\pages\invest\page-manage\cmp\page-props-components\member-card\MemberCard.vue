<template>
  <div class="membercard">
    <div class="title pt-12 pl-12 pr-12">{{ i18n('会员卡片') }}</div>
    <div class="info pl-12 pr-12">{{ i18n('会员信息') }}：</div>
    <el-form ref="form" :model="value" :rules="rules">
      <div style="padding: 0 12px;">
        <!-- 头像 -->
        <el-form-item prop="propShowAvatar">
          <div class="checked">
            <div class="name">
              <span>{{ i18n('头像') }}</span>
              <b>{{ value.propShowAvatar ? i18n('显示') : i18n('不显示') }}</b>
            </div>
            <el-checkbox disabled v-model="value.propShowAvatar"></el-checkbox>
          </div>
          <div class="icon-form" v-if="value.propShowAvatar">
            <div class="icon-content">
              <div class="icon-left">{{i18n('图标')}}</div>
              <div class="icon-right">
                <div class="gray-tips">
                  <i18n k="/公用/券模板/建议尺寸{0}像素，支持{1}，大小不超过{2}">
                    <template slot="0">210*210</template>
                    <template slot="1">jpg/jpeg/png</template>
                    <template slot="2">200KB</template>
                  </i18n>
                </div>
                <UploadImg v-model="value.propAvatarIcon" @change="handleChange" :isShowKb="true" :maximum="200">
                </UploadImg>
              </div>
            </div>
          </div>
        </el-form-item>
        <!-- 昵称 -->
        <el-form-item prop="propShowNickname">
          <div class="checked">
            <div class="name">
              <span>{{ i18n('昵称') }}</span>
              <b>{{ value.propShowNickname ? i18n('显示') : i18n('不显示') }}</b>
            </div>
            <el-checkbox disabled v-model="value.propShowNickname"></el-checkbox>
          </div>
        </el-form-item>
        <!-- 手机号 -->
        <el-form-item prop="propShowPhoneNumber">
          <div class="checked">
            <div class="name">
              <span>{{ i18n('手机号') }}</span>
              <b>{{ value.propShowPhoneNumber ? i18n('显示') : i18n('不显示') }}</b>
            </div>
            <el-checkbox @change="handleChange" v-model="value.propShowPhoneNumber"></el-checkbox>
          </div>
        </el-form-item>
        <!-- 修改手机号 -->
        <el-form-item prop="propShowEditPhoneNumber">
          <div v-show="value.propShowPhoneNumber" class="checked">
            <div class="name">
              <span>{{ i18n('修改手机号') }}</span>
              <b>{{ value.propShowEditPhoneNumber ? i18n('支持') : i18n('不支持') }}</b>
            </div>
            <el-checkbox @change="handleChange" v-model="value.propShowEditPhoneNumber"></el-checkbox>
          </div>
        </el-form-item>
        <!-- 会员等级 -->
        <el-form-item prop="propShowMemberLevel">
          <div class="checked">
            <div class="name">
              <span>{{ i18n('会员等级') }}</span>
              <b>{{ value.propShowMemberLevel ? i18n('显示') : i18n('不显示') }}</b>
            </div>
            <el-checkbox @change="handleChange" v-model="value.propShowMemberLevel"></el-checkbox>
          </div>
        </el-form-item>
        <!-- 会员码 -->
        <el-form-item prop="propShowMemberCode">
          <div class="checked">
            <div class="name">
              <span>{{ i18n('会员码') }}</span>
              <b>{{ value.propShowMemberCode ? i18n('显示') : i18n('不显示') }}</b>
            </div>
            <el-checkbox @change="handleChange" v-model="value.propShowMemberCode"></el-checkbox>
          </div>
        </el-form-item>
        <el-form-item v-if="value.propShowMemberCode && value.propMemberCodeJumpPageInfo && hasOptionPermission('/小程序定制页/百联储值付款码/百联储值付款码', '开通')"
          prop="propShowMemberCode">
          <div class="checked">
            <div class="name">
              <span style="margin-left: 10px;">{{ i18n('跳转页面') }}</span>
            </div>
            <div>
              <el-select @change="handleMemberCodeJumpPageInfo(value.propMemberCodeJumpPageInfo)"
                v-model="value.propMemberCodeJumpPageInfo.templateId" size="mini">
                <el-option :label="formatI18n('/储值/会员储值/百联储值付款码')" value="BaiLianStoredPayCode"></el-option>
                <el-option :label="formatI18n('/储值/会员储值/会员动态码')" value="MemberDynamicCode"></el-option>
              </el-select>
            </div>
          </div>
        </el-form-item>
        <div class="xian"></div>
        <div class="info">{{ i18n('会员权益') }}：</div>
        <!-- 积分 -->
        <el-form-item prop="propShowPoint">
          <div class="checked">
            <div class="name">
              <span>{{ i18n('积分') }}</span>
              <b>{{ value.propShowPoint ? i18n('显示') : i18n('不显示') }}</b>
            </div>
            <el-checkbox @change="handleChange" v-model="value.propShowPoint"></el-checkbox>
          </div>
        </el-form-item>
        <el-form-item prop="propPointText" style="margin-bottom: 12px;">
          <el-input type="text" :placeholder="i18n('积分')" v-model="value.propPointText" maxlength="4" show-word-limit @change="handleChange"
            v-show="value.propShowPoint"></el-input>
          <div class="icon-form" v-if="value.propShowPoint">
            <div class="icon-content">
              <div class="icon-left">{{i18n('图标')}}</div>
              <div class="icon-right">
                <div class="gray-tips">
                  <i18n k="/公用/券模板/建议尺寸{0}像素，支持{1}，大小不超过{2}">
                    <template slot="0">210*210</template>
                    <template slot="1">jpg/jpeg/png</template>
                    <template slot="2">200KB</template>
                  </i18n>
                </div>
                <UploadImg v-model="value.propPointIcon" @change="handleChange" :isShowKb="true" :maximum="200">
                </UploadImg>
              </div>
            </div>
          </div>
        </el-form-item>
        <!-- 储值 -->
        <el-form-item prop="propShowBalance">
          <div class="checked">
            <div class="name">
              <span>{{ i18n('储值') }}</span>
              <b>{{ value.propShowBalance ? i18n('显示') : i18n('不显示') }}</b>
            </div>
            <el-checkbox @change="handleChange" v-model="value.propShowBalance"></el-checkbox>
          </div>
        </el-form-item>
        <el-form-item prop="propBalanceText">
          <el-input @change="handleChange" class="input" type="text" :placeholder="i18n('储值')" v-model="value.propBalanceText" maxlength="4"
            show-word-limit v-show="value.propShowBalance"></el-input>
          <div class="icon-form" v-if="value.propShowBalance">
            <div class="icon-content">
              <div class="icon-left">{{i18n('图标')}}</div>
              <div class="icon-right">
                <div class="gray-tips">
                  <i18n k="/公用/券模板/建议尺寸{0}像素，支持{1}，大小不超过{2}">
                    <template slot="0">210*210</template>
                    <template slot="1">jpg/jpeg/png</template>
                    <template slot="2">200KB</template>
                  </i18n>
                </div>
                <UploadImg v-model="value.propBalanceIcon" @change="handleChange" :isShowKb="true" :maximum="200">
                </UploadImg>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item v-if="value.propShowBalance && value.propBalanceJumpPageInfo && hasOptionPermission('/小程序定制页/百联储值/百联储值', '开通')"
          prop="propShowMemberCode" style="margin-bottom: 12px;">
          <div class="checked">
            <div class="name">
              <span style="margin-left: 10px;">{{ i18n('跳转页面') }}</span>
            </div>
            <div>
              <el-select v-model="value.propBalanceJumpPageInfo.templateId" @change="handleBalenceJumpPageInfoChange(value.propBalanceJumpPageInfo)"
                size="mini">
                <el-option :label="formatI18n('/储值/会员储值/百联储值')" value="BaiLianStoredValue"></el-option>
                <el-option :label="formatI18n('/储值/会员储值/储值')" value="StoredValue"></el-option>
              </el-select>
            </div>
          </div>
        </el-form-item>
        <!-- 优惠券 -->
        <el-form-item prop="propShowCoupon">
          <div class="checked">
            <div class="name">
              <span>{{ i18n('优惠券') }}</span>
              <b>{{ value.propShowCoupon ? i18n('显示') : i18n('不显示') }}</b>
            </div>
            <el-checkbox @change="handleChange" v-model="value.propShowCoupon"></el-checkbox>
          </div>
        </el-form-item>
        <el-form-item prop="propCouponText" style="margin-bottom: 12px;">
          <el-input @change="handleChange" class="input" type="text" :placeholder="i18n('优惠券')" v-model="value.propCouponText" maxlength="4"
            show-word-limit v-show="value.propShowCoupon"></el-input>
          <div class="icon-form" v-if="value.propShowCoupon">
            <div class="icon-content">
              <div class="icon-left">{{i18n('图标')}}</div>
              <div class="icon-right">
                <div class="gray-tips">
                  <i18n k="/公用/券模板/建议尺寸{0}像素，支持{1}，大小不超过{2}">
                    <template slot="0">210*210</template>
                    <template slot="1">jpg/jpeg/png</template>
                    <template slot="2">200KB</template>
                  </i18n>
                </div>
                <UploadImg v-model="value.propCouponIcon" @change="handleChange" :isShowKb="true" :maximum="200">
                </UploadImg>
              </div>
            </div>
          </div>
        </el-form-item>
        <!-- 预付卡 -->
        <el-form-item prop="propShowPrepaidCard">
          <div class="checked">
            <div class="name">
              <span>{{ i18n('预付卡') }}</span>
              <b>{{ value.propShowPrepaidCard ? i18n('显示') : i18n('不显示') }}</b>
            </div>
            <el-checkbox @change="handleChange" v-model="value.propShowPrepaidCard"></el-checkbox>
          </div>
        </el-form-item>
        <el-form-item prop="propPrepaidCardText">
          <el-input @change="handleChange" class="input" type="text" :placeholder="i18n('预付卡')" v-model="value.propPrepaidCardText" maxlength="4"
            show-word-limit v-show="value.propShowPrepaidCard"></el-input>
          <div class="icon-form" v-if="value.propShowPrepaidCard">
            <div class="icon-content">
              <div class="icon-left">{{i18n('图标')}}</div>
              <div class="icon-right">
                <div class="gray-tips">
                  <i18n k="/公用/券模板/建议尺寸{0}像素，支持{1}，大小不超过{2}">
                    <template slot="0">210*210</template>
                    <template slot="1">jpg/jpeg/png</template>
                    <template slot="2">200KB</template>
                  </i18n>
                </div>
                <UploadImg v-model="value.propPrepaidCardIcon" @change="handleChange" :isShowKb="true" :maximum="200">
                </UploadImg>
              </div>
            </div>
          </div>
        </el-form-item>
      </div>
      <!-- <div class="xian"></div> -->
      <!-- 付费会员： -->
      <div class="paidMember-list">
        <div class="paidMember-list-container">
          <el-form-item prop="propshowPaidMember" class="pl-12 pr-12 pt-12">
            <div class="checked">
              <div class="name">
                <span>{{ i18n('/会员/付费会员/付费会员卡') }}：</span>
                <b>{{ value.propshowPaidMember ? i18n('启用') : i18n('不启用') }}</b>
              </div>
              <el-switch @change="handleChange" inactive-color="#D0D4DA" v-model="value.propshowPaidMember"></el-switch>
            </div>
          </el-form-item>
          <!-- 付款会员卡列表 -->
          <div v-loading="loadingBenefitCardTemplateList" v-if="value.propshowPaidMember">
            <el-form-item prop="propPaidMemberList" v-for="(item, index) in value.propPaidMemberCards" :key="index" style="padding: 12px;">
              <div class="checked">
                <div class="name">
                  <span>{{ i18n('/会员/付费会员/付费会员卡') }} {{ index + 1 }}</span>

                </div>
                <div @click="removeBenefitCard(index)" style="color: #A1A6AE ;cursor: pointer; font-size: 14px;line-height: 20px">
                  {{ i18n('/公用/按钮/删除') }}</div>
              </div>
              <el-select v-model.sync="value.propPaidMemberCards[index]" @change="handleChange" size="mini" style="width: 100%;">
                <el-option v-for="benefitCard in benefitCardTemplateList" :key="benefitCard.uuid" :label="benefitCard.name" :value="benefitCard.code"
                  :disabled="isBenefitCardDisabled(item, benefitCard.code)"></el-option>
              </el-select>
            </el-form-item>
            <el-button v-if="!hideAddBenefitCard" @click="addBenefitCard" icon="el-icon-plus" plain type="default" class="membercard-paid-add-btn">
              {{ i18n('/会员/会员资料/添加付费会员卡') }}
            </el-button>

            <div class="paid-member-text">
              <div class="xian"></div>
              <div class="info">{{ i18n('内容设置') }}：</div>
              <el-form-item class="paid-member-text-form" prop="propPaidMemberText" :label="i18n('/会员/会员资料/开卡文案')">
                <el-input @change="handleChange" class="paid-member-text-form-input" type="text" size="mini"
                  :placeholder="formatI18n('/页面/页面管理/立即开卡，尊享超值权益')" v-model="value.propPaidMemberText" maxlength="15" show-word-limit
                  v-show="value.propshowPaidMember"></el-input>
              </el-form-item>
            </div>
          </div>
        </div>
      </div>

    </el-form>
  </div>
</template>

<script lang="ts" src="./MemberCard.ts"></script>

<style lang="scss" scoped>
.membercard {
  background: #f0f2f6;
  border-radius: 4px;

  // padding: 12px;
  .pb-12 {
    padding-bottom: 12px;
  }

  .pt-12 {
    padding-top: 12px;
  }

  .pl-12 {
    padding-left: 12px;
  }

  .pr-12 {
    padding-right: 12px;
  }

  &-paid-add-btn {
    width: calc(100% - 24px);
    margin-left: 12px;
    margin-bottom: 20px;
    color: #007eff;
  }

  .title {
    font-weight: 500;
    font-size: 14px;
    color: #24272b;
    line-height: 16px;
    margin-bottom: 24px;
  }

  .info {
    font-weight: 400;
    font-size: 14px;
    color: #5a5f66;
    line-height: 20px;
    margin-bottom: 12px;
  }

  .checked {
    display: flex;
    justify-content: space-between;

    .name {
      margin-bottom: 14px;

      span {
        margin-right: 12px;
        font-weight: 400;
        font-size: 14px;
        color: #24272b;
        line-height: 20px;
      }

      b {
        font-weight: 400;
        font-size: 14px;
        color: #a1a6ae;
        line-height: 20px;
      }
    }
  }

  .xian {
    margin-top: 6px;
    margin-bottom: 20px;
    width: 300px;
    height: 1px;
    background: #d7dfeb;
  }

  .el-input {
    margin-top: -8px;
  }

  .el-form-item {
    margin-bottom: 0px;

    ::v-deep .el-form-item__content {
      line-height: 0px !important;
    }
  }

  .paidMember-list {
    padding-top: 20px;
    background-color: #f9fafc;

    .paidMember-list-container {
      background-color: #f0f2f6;
    }
  }

  .paid-member-text {
    padding-right: 12px;
    padding-left: 12px;
    .xian {
      margin-top: 0;
    }

    &-form {
      display: flex;
      padding-bottom: 12px;

      ::v-deep .el-form-item__content {
        flex: 1;
      }

      ::v-deep .el-form-item__label {
        line-height: 28px;
      }

      &-input {
        margin-bottom: 0;
        margin-top: 0;
      }
    }
  }

  .icon-form {
    background-color: #fff;
    padding: 6px;
    line-height: 14px;
    margin: 6px 0;
    .icon-content {
      display: flex;
      .icon-left {
        width: 40px;
        margin-right: 12px;
      }
      .icon-right {
        flex: 1;
      }
    }
  }
}
</style>
