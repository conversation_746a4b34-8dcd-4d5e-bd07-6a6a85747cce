import SettingToolbar from '../setting-toolbar/SettingToolbar.vue';

export default {
  components: {
    SettingToolbar,
  },
  props: {
    toolBarBtns: {
      type: Array,
      default: () => {
        return [];
      },
    },
    activeComponentId: {
      type: String,
    },
    index: {
      type: Number,
    },
    activeIndex: {
      type: Number,
    },
  },
  computed: {
    ossSourceUrl() {
      return  '/-/cms/thumbnail/';
    },
  },
  methods: {
    toolbarClick(e) {
      console.log(e);
      this.$emit('toolBarClick', { item: this.componentItem, index: this.index, clickName: e });
    },
    activeTemplate() {
      this.$emit('activeCom', { item: this.componentItem, index: this.index });
    },
  },
};
