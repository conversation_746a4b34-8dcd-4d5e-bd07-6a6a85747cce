<template>
    <div>
        <el-dialog :before-close="doBeforeClose" :close-on-click-modal="false"
                   :title="title"
                   :visible.sync="dialogShow"
                   append-to-body class="edit-data-dialog">
            <div class="wrap">
                <el-form :model="ruleForm" :rules="rules" class="demo-ruleForm" label-width="100px" ref="ruleForm">
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="手机号" prop="mobile">
                                <el-input
                                        maxlength="18"
                                        placeholder="请输入手机号"
                                        v-model="ruleForm.mobile"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="姓名" prop="name">
                                <el-input maxlength="30" placeholder="请输入姓名" v-model="ruleForm.name"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="性别" prop="gender">
                                <el-select clearable placeholder="请选择"v-model="ruleForm.gender" >
                                <el-option label="男" value="男">男</el-option>
                                <el-option label="女" value="女">女</el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="生日" prop="birthday">
                                <el-date-picker
                                format="yyyy-MM-dd"
                                placeholder="请选择日期"
                                style="width: 100%;"
                                v-model="ruleForm.birthday"
                                value-format="yyyy-MM-dd">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="身份证号" prop="idCard">
                                <el-input
                                maxlength="30"
                                placeholder="请输入身份证号"
                                v-model="ruleForm.idCard">
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="学历" prop="education">
                                <el-select clearable v-model="ruleForm.education">
                                    <el-option value="研究生">研究生</el-option>
                                    <el-option value="大学">大学</el-option>
                                    <el-option value="大专">大专</el-option>
                                    <el-option value="高中">高中</el-option>
                                    <el-option value="初中">初中</el-option>
                                    <el-option value="小学">小学</el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="行业" prop="industry">
                                <el-select clearable v-model="ruleForm.industry">
                                <el-option value="人事/行政/管理">人事/行政/管理</el-option>
                                <el-option value="建筑/房产/物业">建筑/房产/物业</el-option>
                                <el-option value="消费品/贸易/物流">消费品/贸易/物流</el-option>
                                <el-option value="咨询/法律/认证">咨询/法律/认证</el-option>
                                <el-option value="生产/制造/营运/采购">生产/制造/营运/采购</el-option>
                                <el-option value="生物/制药/医疗/护理">生物/制药/医疗/护理</el-option>
                                <el-option value="教育/培训/翻译">教育/培训/翻译</el-option>
                                <el-option value="科研/环保/休闲/其他">科研/环保/休闲/其他</el-option>
                                <el-option value="IT/互联网/通信/电子">IT/互联网/通信/电子</el-option>
                                <el-option value="金融/投资/财会">金融/投资/财会</el-option>
                                <el-option value="广告/媒体/出版/艺术">广告/媒体/出版/艺术</el-option>
                                <el-option value="市场/销售/客服">市场/销售/客服</el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="年收入" prop="annualIncome">
                                <el-select clearable v-model="ruleForm.annualIncome">
                                    <el-option value="5万以下">5万以下</el-option>
                                    <el-option value="5万-15万">5万-15万</el-option>
                                    <el-option value="15万-30万">15万-30万</el-option>
                                    <el-option value="30万以上">30万以上</el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="爱好" prop="hobbies">
                                <el-input maxlength="120" placeholder="请输入爱好" v-model="ruleForm.hobbies"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="备用手机号" prop="spareMobile">
                                <el-input
                                maxlength="18"
                                placeholder="请输入备用手机号"
                                v-model="ruleForm.spareMobile"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="邮箱" prop="email">
                                <el-input  maxlength="64" placeholder="请输入邮箱" v-model="ruleForm.email"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item :label="formatI18n('/会员/会员资料', '地址')" prop="address">
                              <AddressSelector style="width: 100%"
                                               :full="true"
                                               value-type="IdName"
                                               v-model="ruleForm.address"></AddressSelector>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item class="cur-address">
                                <el-input :placeholder="formatI18n('/会员/会员资料', '请输入地址')"
                                          maxlength="254"
                                          v-model="ruleForm.addressInfo">
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="变更说明" prop="remark">
                                <el-input maxlength="50"
                                placeholder="请输入不超过50字的变更说明"
                                type="textarea" v-model="ruleForm.remark"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div class="dialog-footer" slot="footer">
                <el-button @click="doCancel">取 消</el-button>
                <el-button @click="doModalClose" size="small" type="primary">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script lang="ts" src="./EditDataDialog.ts">
</script>

<style lang="scss">
.edit-data-dialog{
    display: flex;
    align-items: center;
    justify-content: center;
    .wrap{
        margin: 30px;
        .el-row{
            margin-top: 10px !important;
        }
        .el-select{
            width: 100%;
        }
        .multi-address .el-col{
            &:first-child{
                padding-left: 0 !important;
            }
            &:last-child{
                padding-right: 0 !important;
            }
        }
    }
}
</style>