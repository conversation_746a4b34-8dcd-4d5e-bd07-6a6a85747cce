/*
 * @Author: 黎钰龙
 * @Date: 2024-11-21 19:31:52
 * @LastEditTime: 2024-12-09 18:42:14
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\autoTag\MemberActionRule.ts
 * 记得注释
 */
import { ConnectiveType } from "./ConnectiveType"
import MemberActionConditionGroup from "./MemberActionConditionGroup"

export default class MemberActionRule {
  // 关系连接符：或者 / 且
  connective: Nullable<ConnectiveType> = ConnectiveType.and
  // 会员属性条件
  memberAttributeConditions: MemberActionConditionGroup[] = []
}