/*
 * @Author: 黎钰龙
 * @Date: 2025-02-18 16:29:31
 * @LastEditTime: 2025-02-24 15:45:51
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\analysis\BCustomerProfileTarget.ts
 * 记得注释
 */
import ConstantMgr from "mgr/ConstantMgr"
import { CustomerProfileCategory } from "./CustomerProfileCategory"
import { CustomerProfileMemberProp } from "./CustomerProfileMemberProp"
import { TargetType } from "./TargetType"

// 指标
export default class BCustomerProfileTarget {
  // 类型枚举： attribute; 会员属性，tag会员标签
  category: Nullable<CustomerProfileCategory> = null
  // 标签id
  tagId: Nullable<string> = null
  // 标签名
  tagName: Nullable<string> = null
  // 会员属性：枚举
  memberProp: Nullable<CustomerProfileMemberProp> = null
  // 会员属性名
  memberPropName: Nullable<string> = null
  // 指标算法: sum 求和,  average 平均子,   max 最大值,   min 最小值;
  type: Nullable<TargetType> = null
}

export class ProfileTargetTypeUtil {
  static getTypeName(val: Nullable<TargetType>) {
    switch (val) {
      case TargetType.sum:
        return new ConstantMgr.MenusFuc().format("/会员/智能打标", "总和")
      case TargetType.average:
        return new ConstantMgr.MenusFuc().format("/会员/智能打标", "均值")
      case TargetType.max:
        return new ConstantMgr.MenusFuc().format("/会员/智能打标", "最大值")
      case TargetType.min:
        return new ConstantMgr.MenusFuc().format("/会员/智能打标", "最小值")
      default:
        break;
    }
  }
}