import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import I18nPage from 'common/I18nDecorator';


@Component({
  name: 'ElasticLayer',
  components: {}
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理',
    '/公用/提示'
  ],
  auto: true
})
export default class ElasticLayer extends Vue {
  $refs: any
  @Prop({ type: Object }) value: any; // 数据模型


  dialogShow: boolean = false;
  activeIndex: number = -1
  tableData: any = [{
    name: this.i18n('消费有礼'),
    address: '--'
  }, {
    name: this.i18n('微信营销'),
    address: this.i18n('微信小程序里免费领或付费购买的券（微信支付代金券除外）')
  }, {
    name: this.i18n('支付宝营销'),
    address: this.i18n('支付宝小程序里免费领或付费购买的券')
  }, {
    name: this.i18n('积分兑换券'),
    address: '--'
  }, {
    name: this.i18n('大转盘'),
    address: '--'
  }, {
    name: this.i18n('等级赠礼'),
    address: this.i18n('等级月礼/套餐赠礼/续费赠礼送券')
  }, {
    name: this.i18n('群发券'),
    address: '--'
  }, {
    name: this.i18n('第三方发券'),
    address: '--'
  }, {
    name: this.i18n('客群发大礼包'),
    address: '--'
  }, {
    name: this.i18n('节日有礼'),
    address: this.i18n('会员日和生日送券')
  }, {
    name: this.i18n('会员成长'),
    address: this.i18n('邀请有礼/升级有礼/注册激活发大礼包/完善资料送券')
  }, {
    name: this.i18n('充值赠礼'),
    address: this.i18n('会员储值充值送券')
  }, {
    name: this.i18n('预付卡赠礼'),
    address: this.i18n('电子卡/实体卡售卡送券、预付卡充值送券、权益卡送券')
  }]
  multipleSelection: any = [] //已选择的发券场景

  handleSelectionChange(val: any, row: any) {
    const flag = val.some((item: any) => item.name === row.name)
    const index = this.multipleSelection.findIndex((item: any) => {
      return item === row.name
    })
    if (flag) {
      this.multipleSelection.push(row.name)
    } else {
      this.multipleSelection.splice(index, 1)
    }
  }

  selectAll(selection: any) {
    if (selection.length > 0) {
      selection.map((row: any, index: number) => {
        const flagIndex = this.multipleSelection.findIndex((item: any) =>
          item == row.name
        )
        if (flagIndex == -1) {
          if (this.multipleSelection.length >= 20) {
            this.multipleSelection.splice(index, 1)
          } else {
            this.multipleSelection.push(row.name)
          }
        }
      })
    } else {
      this.tableData.map((row: any) => {
        const flagIndex = this.multipleSelection.findIndex((item: any) =>
          item == row.name
        )
        if (flagIndex > -1) {
          this.multipleSelection.splice(flagIndex, 1)
        }
      })
    }
  }

  remove(index: number) {
    const removedItem = this.multipleSelection[index];
    // 从multipleSelection中移除指定项
    this.multipleSelection.splice(index, 1);
    // 更新表格的选中状态
    this.$nextTick(() => {
      this.tableData.forEach((item: any) => {
        if (item.name === removedItem) {
          this.$refs.multipleTable.toggleRowSelection(item, false);
        }
      });
    });
  }

  open(arr: string[], index: number) {
    this.dialogShow = true;
    this.activeIndex = index
    this.$nextTick(() => {
      this.multipleSelection = JSON.parse(JSON.stringify(arr))
      this.tableData.forEach((item: any) => {
        this.multipleSelection.some((val: string) => {
          if (item.name === val) {
            this.$refs.multipleTable.toggleRowSelection(item, true);
            return true
          }
        })
      })
    })
  }
  canel() {
    this.dialogShow = false;
  }
  confirm() {
    this.dialogShow = false;
    this.$emit('change', this.multipleSelection, this.activeIndex);
  }
};