import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import MemberDetail from "model/member_standard/MemberDetail";
import I18nPage from "common/I18nDecorator";
import MemberCardTitle from "pages/member/data/cmp/MemberCardTitle";
import BMemberChannel from "model/member/BMemberChannel";
import MemberApi from "http/member_standard/MemberApi";
import MemberChannelIcon from "pages/member/data/cmp/info/MemberChannelIcon.vue";
import EmptyData from "pages/member/data/cmp/EmptyData";
import MemberFormItem from "pages/member/data/cmp/MemberFormItem";

@Component({
  name: "MemberInfoChannel",
  components: { MemberCardTitle, MemberChannelIcon,EmptyData,MemberFormItem },
})
@I18nPage({
  prefix: [
    "/会员/会员资料",
  ],
  auto: true,
})
export default class MemberInfoChannel extends Vue {
  @Prop()
  dtl: MemberDetail;


  @Watch("dtl", { immediate: true })
  dtlChanged() {
    if (!(this.dtl && this.dtl.memberId)) return;
    this.getChannels();
  }

  channels: BMemberChannel[] = [];

  getChannels() {
    MemberApi.getChannelIdentity(this.dtl.memberId!).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.channels = resp.data;
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message);
      }
    });
  }

  get title() {
    return this.i18n("渠道信息（{0}）", [this.channels.length + ""]);
  }
}
