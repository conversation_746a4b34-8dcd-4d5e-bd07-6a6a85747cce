import {Component, Prop, Vue, Watch} from 'vue-property-decorator'
import CouponItem from 'model/common/CouponItem'
import AmountToFixUtil from 'util/AmountToFixUtil'
import TimeRange from 'cmp/coupontenplate/cmp/TimeRange.vue'
import GoodsScopeEx from 'cmp/goodsscope/GoodsScopeEx.vue'
import GoodsRange from 'model/common/GoodsRange'
import UseCouponStep from 'cmp/coupontenplate/cmp/UseCouponStep.vue'
import CouponBear from 'cmp/coupontenplate/cmp/CouponBear.vue'
import CouponInfo from 'model/common/CouponInfo'
import CashCouponAttribute from 'model/common/CashCouponAttribute'
import ValidityInfo from 'model/common/ValidityInfo'
import DateTimeRange from 'model/common/DateTimeRange'
import SubjectApportion from 'model/common/SubjectApportion'
import GoodsRangeView from 'cmp/coupontenplate/cmp/GoodsRange.vue'
import StoreRange from 'model/common/StoreRange'
import DateUtil from 'util/DateUtil'
import CostParty from 'model/common/CostParty'
import CouponThreshold from 'model/common/CouponThreshold'
import ChannelRange from 'model/common/ChannelRange'
import CouponInitialApi from 'http/v2/coupon/init/CouponInitialApi'
import RSCostPartyFilter from 'model/common/RSCostPartyFilter'
import CostPartyApi from 'http/costparty/CostPartyApi'
import GroupMutexTemplate from "cmp/coupontenplate/cmp/GroupMutexTemplate";
import GroupMutexTemplateData from "cmp/coupontenplate/cmp/GroupMutexTemplateData";
import CouponTemplateLogo from "cmp/coupontenplate/cmp/CouponTemplateLogo";
import ActiveStore from "cmp/activestore/ActiveStore";
import CouponTemplateLabel from "cmp/coupontenplate/cmp/CouponTemplateLabel.vue"
import BrowserMgr from "mgr/BrowserMgr";
import CouponConfig from 'model/v2/coupon/init/CouponConfig'


class CostPartyDetail {
  // 承担方
  party: Nullable<string> = null
  // 承担百分比/金额
  value: Nullable<number> = null
}
@Component({
	name: "GoodsCashCoupon",
	components: {
		TimeRange,
		ActiveStore,
		GoodsScopeEx,
		UseCouponStep,
		CouponBear,
		GoodsRangeView,
		GroupMutexTemplate,
		CouponTemplateLogo,
		CouponTemplateLabel
	},
})
export default class GoodsCashCoupon extends Vue {
	dtl: CouponConfig = new CouponConfig()
	goodsMatchRuleMode: string = "barcode"
	timeParam: any = "";
	noLimit: Boolean = true;
	// useCouponTemplate: any = {}
	ruleForm: any = {
		amount: "",
		name: "",
		dateType: "RALATIVE",
		dateFrom: "",
		dateTo: "",
		dateFix: "",
		useDate: "",
		storeRange: "{}",
		useCouponGood: new GoodsRange(),
		promotion: true,
		recordWay: "FAV",
		discountWay: "",
		payWay: "",
		couponOrder: "",
		couponGoodsDesc: "",
		couponProduct: "",
		type: "",
		time: "",
		couponUnder: {},
		templateId: "",
		useFrom: "step2",
		from: [],
		sychChannel: null,
		logoUrl: "",
		groupMutex: new GroupMutexTemplateData(true),
		prefix: "",
		transferable: true, // 是否可转赠
		templateTag: []
	};
	$refs: any;
	rules: any = {};
	curState = "";
	@Prop()
	sameStore: boolean; // 与活动门店一致
	@Prop()
	maxAmount: number;
	@Prop()
	state: string;
	@Prop()
	channels: any;
	@Prop()
	value: CouponItem;
	@Prop({
		type: Boolean,
		default: false,
	})
	baseSettingFlag: boolean;
	@Prop({
		type: String,
		default: "add",
	})
	copyFlag: string;

	@Prop({
		type: String,
		default: "400",
	})
	remarkMaxlength: string;

	@Prop({
		type: Boolean,
		default: false,
	})
	baseFieldEditable: false; // 叠加促销、用券记录方式、券承担方、用券顺序 是否可编辑

	@Prop({
		default: () => {
			return {
				maxAmount: 99999999,
				maxValidDay: 36500,
				maxUseThreshold: 99999999,
				fixedTime: false,
			};
		},
	})
	options: {
		// 指定最大券面额，可选配置，用于微信扫码领券
		maxAmount: number; // 指定最大券面额
		maxValidDay: number; // 指定最大券有效天数
		maxUseThreshold: number; // 指定最大用券门槛
		fixedTime: boolean; // 固定用券时段为全部时段
	};

	@Prop({
		type: Boolean,
		default: false,
	})
	wxScanForCoupon: boolean;

	parties: any = [];

	// 是否是复制\新建\编辑
	@Watch("state")
	onStateChange(value: string) {
		this.curState = value;
	}

	@Watch("value")
	onDataChange(value: CouponItem) {
		if (value && value.coupons) {
			this.doBindValue(JSON.parse(JSON.stringify(value)));
		}
	}

	get dateRangeOption() {
		return {
			disabledDate(time: any) {
				return time.getTime() < DateUtil.nowDayTime();
			},
		};
	}

	get remarkPlaceholder() {
		let str = this.formatI18n("/营销/积分活动/积分活动/积分兑换券/编辑页面", "请输入不超过{0}个字符");
		return str.replace(/\{0\}/g, this.remarkMaxlength);
	}

	created() {
		let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
		if (sysConfig && this.goodsMatchRuleMode) {
			this.goodsMatchRuleMode = sysConfig.goodsMatchRuleMode;
		}
		this.curState = this.state;
		this.rules = {
			amount: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
			dateFrom: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
			dateTo: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
			dateFix: [
				// { required: true, message: this.formatI18n('/公用/券模板', '请输入必填项'), trigger: 'blur' },
				{
					required: true,
					validator: (rule: any, value: any, callback: any) => {
						if (value && value.length > 0 && value[0] !== "--") {
              let start = DateUtil.parseDate(DateUtil.format(DateUtil.parseDate(value[0]), "yyyy-MM-dd HH:mm:ss")).getTime();
              let end = DateUtil.parseDate(DateUtil.format(DateUtil.parseDate(value[1]), "yyyy-MM-dd HH:mm:ss")).getTime();
              if (start > end) {
								callback(new Error(this.formatI18n("/营销/券礼包活动/核销第三方券", "开始时间不允许大于结束时间") as string));
							} else {
								callback();
							}
						} else {
							callback(new Error(this.formatI18n("/公用/券模板", "请输入必填项") as string));
						}
					},
					trigger: "blur",
				},
			],
			couponUnder: [
				{
					validator: (rule: any, value: any, callback: any) => {
						if (value) {
              if (value.bearType == 'unset') {
                callback();
              }
							if (value.costPartyDetails && value.costPartyDetails.length <= 0) {
                if (value.bearType == 'unset') {
                  callback();
                } else {
                  callback('请选择承运商');
                }
							} else {
								let flag = false;
                let count: number = 0
                if (value.bearType == 'PROPORTION') {
                  // 按比例
                  (value.costPartyDetails || []).forEach((item: CostPartyDetail) => {
                    if (!item.value) {
                      flag = true;
                    } else {
                      count += parseFloat(item.value as any)
                    }
                    if (!item.party) {
                      flag = true;
                    }
                  });

                } else if (value.bearType == 'AMOUNT') {
                  // 按金额
                  (value.costPartyDetails || []).forEach((item: CostPartyDetail, index: number) => {
                    if (!item.value && value.amountType == 'part' && (index != value.costPartyDetails.length - 1 || index == 0)) {
                      flag = true;
                    }
                    if (!item.party) {
                      flag = true;
                    }
                  });
                } else {
                  // 不设置
                  flag = false;
                }
								if (flag) {
									callback(this.formatI18n("/公用/券模板", "请输入必填项"));
								} else {
                  if (value.bearType == 'PROPORTION') {
                    if (count != 100) {
                      callback('所有承担方合计承担100%，请重新填写');
                    } else {
                      callback();
                    } 
                  } else {
                    callback();
                  }
								}
							}
						}
					},
					trigger: "blur",
				},
			],
			useFrom: [
				{
					validator: (rule: any, value: any, callback: any) => {
						if (value) {
							if (value === "step1") {
								callback();
							} else {
								if (this.ruleForm.from && this.ruleForm.from.length > 0) {
									callback();
								} else {
									callback(this.formatI18n("/公用/券模板", "请输入必填项"));
								}
							}
						}
					},
					trigger: "blur",
				},
			],
			couponOrder: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
			couponProduct: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
			discountWay: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
			payWay: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
			prefix: [
				{
					validator: (rule: any, value: any, callback: any) => {
						let re = /^[0-9a-zA-Z]*$/g; // 判断字符串是否为数字和字母组合
						if (!re.test(value)) {
							callback(this.formatI18n("/资料/渠道", "请输入数字或字母"));
						} else {
							callback();
						}
					},
					tirgger: "blur",
				},
			],
		};
		if (this.value && this.value.coupons && this.value.coupons.remark) {
			this.doBindValue(JSON.parse(JSON.stringify(this.value)));
		}
		if (this.copyFlag) {
			this.getCostParty();
		}
		if (!this.$route.query.id) {
			// 新建时使用配置的用券记录方式
			this.getPayWayDtl();
		}
		this.getCouponPrefix("cash");

	}

	private getCouponPrefix(type: string){
		if (["edit", "copy"].includes(this.copyFlag)) {
			return;
		}
		CouponInitialApi.get().then((resp: any) => {
		  if (resp && resp.code === 2000) {
			this.dtl = resp.data;
			if(!this.dtl || !this.dtl.couponCodePrefixes) {
				return "";
			  }
			  const coupon = this.dtl.couponCodePrefixes.find(
				item => item.couponType === type
			);
			this.ruleForm.prefix = coupon ? coupon.prefix : "";
		  }
		})
	}

	limitChange(noLimit: Boolean) {
		this.noLimit = noLimit;
	}

	getFaceAmount(amount: number) {
		let str: any = this.formatI18n("/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级套餐", "{0}元");
		str = str.replace(/\{0\}/g, Number(amount).toFixed(2));
		return str;
	}

	submit() {
		this.$emit("input", this.doTransParams());
		this.$emit("change", this.channels);
	}

	doTimeChange() {
		this.submit();
	}

	doFromChange() {
		this.$refs.ruleForm.validateField("useFrom");
		this.submit();
	}

	doSychChannelChange() {
		console.log("ruleForm.sychChannel", this.ruleForm.sychChannel);
		this.$refs.ruleForm.validateField("useFrom");
		this.$emit("input", this.doTransParams());
		this.$emit("change", this.channels);
	}

	doAmountChange() {
		this.ruleForm.amount = AmountToFixUtil.formatAmount(this.ruleForm.amount, this.options.maxAmount, 0.01, "");
		this.submit();
	}

	doUseFromChange() {
		if (this.ruleForm.useFrom === "step1") {
			this.ruleForm.from = [];
			this.submit();
		}
		this.$refs.ruleForm.validateField("useFrom");
	}

	doNameChange() {
		this.submit();
	}

	doPrefixChange() {
		this.submit();
	}

	logoUrlCallBack(url: any) {
		this.ruleForm.logoUrl = url;
		this.submit();
	}

	doCouponValidateChange() {
		if (this.ruleForm.dateType === "RALATIVE") {
			this.ruleForm.dateFix = [];
		} else {
			this.ruleForm.dateFrom = "";
			this.ruleForm.dateTo = "";
		}
	}

	getFavValue(favValue: any, payValue: any) {
		let str: any = this.formatI18n(
			"/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送券的时候/点击券名称/用券记录方式为组合方式",
			"{0}%优惠方式+{1}%支付方式"
		);
		str = str.replace(/\{0\}/g, `&nbsp;<span style="font-weight: bold">${favValue}</span>&nbsp;`);
		str = str.replace(/\{1\}/g, `&nbsp;<span style="font-weight: bold">${payValue}</span>&nbsp;`);
		return str;
	}

	getCostPartr(party: any, percent: any) {
		let str: any = this.formatI18n(
			"/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送券的时候/点击券名称/券承担方",
			"{0}承担用券金额{1}%"
		);
		str = str.replace(/\{0\}/g, this.getPartyNameById(party));
		str = str.replace(/\{1\}/g, `&nbsp;<span style="font-weight: bold">${percent}</span>&nbsp;`);
		return str;
	}

	getPartyNameById(id: string) {
		let str = "";
		if (this.parties && this.parties.length > 0) {
			this.parties.forEach((item: any) => {
				if (item.costParty.id === id) {
					str = item.costParty.name;
				}
			});
		}
		return str;
	}

	doGoodsCashMutexTemplateChange() {
		this.submit();
	}

	getAllCash(threshold: any) {
		let str: any = this.formatI18n("/公用/券模板详情/商品现金券/用券门槛/可叠加使用，用券商品消费每满{0}元可用1张券");
		str = str.replace(/\{0\}/g, `&nbsp;<span style="font-weight: bold">${threshold}</span>&nbsp;`);
		return str;
	}

	getAllCashNo(threshold: any) {
		let str: any = this.formatI18n("/公用/券模板详情/商品现金券/用券门槛/不可叠加使用，用券商品消费满{0}元及以上可用1张券");
		str = str.replace(/\{0\}/g, `&nbsp;<span style="font-weight: bold">${threshold}</span>&nbsp;`);
		return str;
	}

	getGoodsNo(threshold: any) {
		let str: any = this.formatI18n(
			"/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送全场现金券的时候/用券门槛/不可叠加使用",
			"商品数量满{0}件及以上可用1张券"
		);
		str = str.replace(/\{0\}/g, `&nbsp;<span style="font-weight: bold">${threshold}</span>&nbsp;`);
		return str;
	}

	doCouponChange(type: number) {
		if (type === 1) {
			this.ruleForm.dateTo = AmountToFixUtil.formatNumber(this.ruleForm.dateTo, this.options.maxValidDay, 1);
		} else {
			this.ruleForm.dateFrom = AmountToFixUtil.formatNumber(this.ruleForm.dateFrom, 365, 0);
		}
		this.submit();
	}

	doDateFixChange() {
		this.submit();
	}

	doTimeParam(value: any) {
		this.timeParam = value;
		this.submit();
	}

	doPromotionChange() {
		this.submit();
	}

	doTransferableChange() {
		this.submit();
	}

	doStepChange() {
		this.submit();
	}

	doGoodsRange() {
		this.submit();
	}

	doStoreChange() {
		if (this.$refs["ruleForm"] && (this.$refs["ruleForm"] as any).validateField) {
			(this.$refs["ruleForm"] as any).validateField("storeRange");
		}
		this.submit();
		// this.$forceUpdate()
	}

	doRecordWayChange() {
		if (this.ruleForm.recordWay === "COLLOCATION") {
			this.ruleForm.discountWay = Number(100).toFixed(2);
			this.ruleForm.payWay = Number(0).toFixed(2);
		} else {
			this.ruleForm.discountWay = "";
			this.ruleForm.payWay = "";
		}
		this.submit();
	}

	doDiscountWay() {
		this.ruleForm.discountWay = AmountToFixUtil.formatAmount(this.ruleForm.discountWay, 100, 0, "");
		this.ruleForm.payWay = (100 - Number(this.ruleForm.discountWay)).toFixed(2);
		this.submit();
	}

	doDateFocus() {
    // this.ruleForm.dateFix = [DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"), DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss")];
	}

	doPayWay() {
		this.ruleForm.payWay = AmountToFixUtil.formatAmount(this.ruleForm.payWay, 100, 0, "");
		this.ruleForm.discountWay = (100 - Number(this.ruleForm.payWay)).toFixed(2);
		this.submit();
	}

	doBearChange() {
		this.submit();
	}

	doLabelChange() {
		this.submit();
	}

	doThisOrder() {
		this.ruleForm.couponOrder = AmountToFixUtil.formatNumber(this.ruleForm.couponOrder, 99, 1);
		this.submit();
	}

	doRemarkChange() {
		this.submit();
	}

	doGoodsRemarkChange() {
		this.submit();
	}

	doValidate() {
		let arr: any = [];
		let p0 = new Promise((resolve, reject) => {
			this.$refs.ruleForm.validate((valid: any) => {
				if (valid) {
					resolve(null);
				}
			});
		});
		arr.push(p0);
		arr.push(this.$refs.activeStore.validate());
    // 券承担方
    if (this.$refs.CouponBear) {
      arr.push(this.$refs.CouponBear.formValiPromise())
    }
		// 用券门槛
		if (this.$refs.useCouponStep) {
			let p1 = this.$refs.useCouponStep.doValidate();
			arr.push(p1);
		}

		// 用券时段
		if (this.$refs.timeRange) {
			let p2 = this.$refs.timeRange.doValidate();
			arr.push(p2);
		}
		// 用券商品
		if (this.$refs.goodsScope2) {
			let p3 = this.$refs.goodsScope2.validate();
			arr.push(p3);
		}
		// 券叠加组
		if (this.$refs.goodsCashMutexTemplate) {
			arr.push(this.$refs.goodsCashMutexTemplate.doValidate());
		}
		return arr;
	}

	private doTransParams() {
		let params: CouponItem = new CouponItem();
		params.coupons = new CouponInfo();
		params.coupons.couponBasicType = "goods_cash" as any;
		params.coupons.name = this.ruleForm.name;
		params.coupons.templateId = this.ruleForm.templateId;
		params.coupons.cashCouponAttribute = new CashCouponAttribute();
		params.coupons.cashCouponAttribute.faceAmount = this.ruleForm.amount;
		// 券有效期
		params.coupons.validityInfo = new ValidityInfo();
		params.coupons.validityInfo.validityType = this.ruleForm.dateType;
		if (this.ruleForm.dateType === "RALATIVE") {
			params.coupons.validityInfo.delayEffectDays = this.ruleForm.dateFrom;
			params.coupons.validityInfo.validityDays = this.ruleForm.dateTo;
		} else {
			if (this.ruleForm.dateFix && this.ruleForm.dateFix[0]) {
				params.coupons.validityInfo.beginDate = (this.ruleForm.dateFix[0]) as any;
			}
			if (this.ruleForm.dateFix && this.ruleForm.dateFix[1]) {
				params.coupons.validityInfo.endDate = (this.ruleForm.dateFix[1]) as any;
			}
		}
		// 用券时段
		params.coupons.useTimeRange = new DateTimeRange();
		if (this.ruleForm.time) {
			// params.coupons.useTimeRange = this.timeTemplate
			params.coupons.useTimeRange = this.ruleForm.time;
		} else {
			params.coupons.useTimeRange = new DateTimeRange();
			params.coupons.useTimeRange.dateTimeRangeType = "ALL" as any;
		}
		// todo 用券渠道
		params.coupons.useChannels = new ChannelRange();
		if (this.ruleForm.useFrom === "step1") {
			params.coupons.useChannels.channelRangeType = "ALL" as any;
			params.coupons.useChannels.channels = [];
		} else {
			params.coupons.useChannels.channelRangeType = "PART" as any;
			params.coupons.useChannels.channels = this.ruleForm.from;
		}
		//同步渠道
		params.coupons.sychChannel = this.ruleForm.sychChannel;
		// 用券门店
		if (this.ruleForm.storeRange === "{}") {
			let storeRange: StoreRange = new StoreRange();
			if (this.sameStore) {
				storeRange.storeRangeType = "SAME" as any;
			} else {
				storeRange.storeRangeType = "ALL" as any;
			}
			params.coupons.useStores = storeRange;
		} else {
			params.coupons.useStores = this.ruleForm.storeRange;
		}
		// 用券商品
		params.coupons.useGoods = this.ruleForm.useCouponGood;
		// 用券门槛
		params.coupons.useThreshold = this.ruleForm.type;
		params.coupons.useThresholdType = this.ruleForm.type.useThresholdType;
		// 叠加促销
		params.coupons.excludePromotion = this.ruleForm.promotion;
		// 用券记录方式
		params.coupons.useApporion = new SubjectApportion();
		params.coupons.useApporion.subjectApprotionType = this.ruleForm.recordWay;
		if (this.ruleForm.recordWay === "COLLOCATION") {
			params.coupons.useApporion.favValue = this.ruleForm.discountWay;
			params.coupons.useApporion.payValue = this.ruleForm.payWay;
		}
		// 券承担方
		// params.coupons.costParties = [];
		// params.coupons.costParties = this.ruleForm.couponUnder;
    ;(params.coupons.costParty as any) = {};
    if (this.ruleForm.couponUnder.bearType == 'unset') {
      this.ruleForm.couponUnder.bearType = null
      this.ruleForm.couponUnder.amountType = null
      this.ruleForm.couponUnder.costPartyDetails = null
    }
		params.coupons.costParty = this.ruleForm.couponUnder;
		// 用券顺序
		params.coupons.priority = this.ruleForm.couponOrder;
		// 用券商品说明
		params.coupons.goodsRemark = this.ruleForm.couponGoodsDesc;
		// 用券说明
		params.coupons.remark = this.ruleForm.couponProduct;
		// 券叠加促销
		params.coupons.couponSuperposition = this.ruleForm.groupMutex.couponSuperposition;
		// params.coupons.groupMutexFlag = this.ruleForm.groupMutex.groupMutexFlag;
		// 是否支持转赠
		params.coupons.transferable = this.ruleForm.transferable;
		// params.coupons.groupMutexTemplates = this.ruleForm.groupMutex.groupMutexTemplates;
		// 券logo
		params.coupons.logoUrl = this.ruleForm.logoUrl;
		// 券码前缀
		params.coupons.codePrefix = this.ruleForm.prefix;
		params.coupons.templateTag = this.ruleForm.templateTag
		return params;
	}

	private doBindValue(value: CouponItem) {
		if (value && value.coupons) {
			let coupon: CouponInfo = value.coupons;
			this.ruleForm.name = coupon.name;
			this.ruleForm.templateId = coupon.templateId;
			if (coupon.cashCouponAttribute) {
				this.ruleForm.amount = coupon.cashCouponAttribute!.faceAmount;
			} else {
				this.ruleForm.amount = "";
			}
			if (coupon.validityInfo) {
				this.ruleForm.dateType = coupon.validityInfo!.validityType;
				if (this.ruleForm.dateType === "RALATIVE") {
					this.ruleForm.dateFrom = coupon.validityInfo!.delayEffectDays;
					this.ruleForm.dateTo = coupon.validityInfo!.validityDays;
				} else {
					this.ruleForm.dateFix = [
            DateUtil.format(coupon.validityInfo!.beginDate, "yyyy-MM-dd HH:mm:ss"),
            DateUtil.format(coupon.validityInfo!.endDate, "yyyy-MM-dd HH:mm:ss"),
					];
				}
			} else {
				this.ruleForm.dateType = "RALATIVE";
				this.ruleForm.dateFrom = "";
				this.ruleForm.dateTo = "";
			}
			this.ruleForm.sychChannel = coupon.sychChannel;
			// 用券门店
			if (coupon.useStores) {
				this.ruleForm.storeRange = coupon.useStores;
				// console.log(JSON.stringify(this.ruleForm.storeRange))
			} else {
				this.ruleForm.storeRange = "{}";
			}

			// 用券商品
			if (coupon.useGoods) {
				this.ruleForm.useCouponGood = coupon.useGoods;
			} else {
				this.$nextTick(() => {
					let useGoods: GoodsRange = new GoodsRange();
					useGoods.limit = false;
					useGoods.excludeBrands = [];
					useGoods.excludeCategories = [];
					useGoods.excludeGoods = [];
					useGoods.includeGoods = [];
					useGoods.includeCategories = [];
					useGoods.includeBrands = [];
					this.ruleForm.useCouponGood = useGoods;
				});
			}
			if (coupon.useTimeRange) {
				this.ruleForm.time = coupon.useTimeRange;
			} else {
				let timeRange: DateTimeRange = new DateTimeRange();
				timeRange.dateTimeRangeType = "ALL" as any;
				this.ruleForm.time = timeRange;
			}
			// todo 用券渠道
			if (coupon.useChannels && coupon.templateId) {
				if (coupon.useChannels.channelRangeType === "ALL") {
					this.ruleForm.useFrom = "step1";
					this.ruleForm.from = [];
				} else {
					this.ruleForm.useFrom = "step2";
					if (coupon.useChannels.channels && coupon.useChannels.channels.length > 0) {
						let arrs: string[] = [];
						coupon.useChannels.channels.forEach((item: any) => {
							if (item.id || item.type) {
								if (item.id && item.id !== "-") {
									arrs.push(item.type + item.id);
								} else {
									arrs.push(item.type);
								}
							} else {
								arrs.push(item);
							}
						});
						this.ruleForm.from = arrs;
					}
				}
			}

			if (coupon.useThreshold) {
				this.ruleForm.type = coupon.useThreshold;
				this.ruleForm.type.useThresholdType = coupon.useThresholdType;
			} else {
				let step: CouponThreshold = new CouponThreshold();
				step.value = null;
				step.thresholdType = null
				step.threshold = null;
				this.ruleForm.type = step;
				this.ruleForm.type.useThresholdType = "NONE";
			}

			// 叠加促销
			if (coupon.excludePromotion || coupon.excludePromotion === false) {
				this.ruleForm.promotion = coupon.excludePromotion;
			} else {
				this.ruleForm.promotion = true;
			}

			// 用券记录方式
			if (coupon.useApporion && coupon.useApporion!.subjectApprotionType) {
				this.ruleForm.recordWay = coupon.useApporion!.subjectApprotionType;
			} else {
				this.ruleForm.recordWay = "FAV";
			}

			if (this.ruleForm.recordWay === "COLLOCATION") {
				this.ruleForm.discountWay = coupon.useApporion!.favValue;
				this.ruleForm.payWay = coupon.useApporion!.payValue;
			}
			// 券承担方
			if (coupon.costParties) {
				// this.ruleForm.couponUnder = coupon.costParties;
        this.ruleForm.couponUnder = coupon.costParty;
			} else {
				this.ruleForm.couponUnder = {};
			}

			// 用券顺序
			this.ruleForm.couponOrder = coupon.priority;
			// 用券商品说明
			this.ruleForm.couponGoodsDesc = coupon.goodsRemark;
			// 用券说明
			this.ruleForm.couponProduct = coupon.remark;
			// 券logo
			this.ruleForm.logoUrl = coupon.logoUrl;
			// 券码前缀
			this.ruleForm.prefix = coupon.codePrefix;
			// 是否可以叠加用券
			this.$nextTick(() => this.$refs.goodsCashMutexTemplate.initValue2(coupon, this.copyFlag));
			// 是否支持转赠
			this.ruleForm.transferable = coupon.transferable;
			this.ruleForm.templateTag = coupon.templateTag
		}
	}

	private getPayWayDtl() {
		CouponInitialApi.get().then((resp: any) => {
			if (resp && resp.code === 2000) {
				if (resp.data && resp.data.subjectApportion === "pay") {
					this.ruleForm.recordWay = "PAY";
        } else if (resp.data && resp.data.subjectApportion === "fav") {
          this.ruleForm.recordWay = "FAV";
        } else if (resp.data && resp.data.subjectApportion === "collection") {
          this.ruleForm.recordWay = "COLLOCATION";
          this.ruleForm.recordType = "AMOUNT"
          this.ruleForm.payWay = 0
        }
			}
		});
	}

	private getCostParty() {
		let params: RSCostPartyFilter = new RSCostPartyFilter();
		params.page = 0;
		params.pageSize = 0;
		CostPartyApi.query(params)
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					this.parties = resp.data;
				}
			})
			.catch((error: any) => {
				this.$message.error(error.message);
			});
	}
}