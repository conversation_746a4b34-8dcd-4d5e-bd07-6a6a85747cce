<template>
    <el-dialog :before-close="doBeforeClose" :close-on-click-modal="false"
               :title="title"
               :visible.sync="dialogShow" append-to-body class="check-coupons-dialog">
        <div class="wrap">
            <div class="flex-wrap" style="height: 450px;overflow: auto;">
                <div class="item" v-for="item in coupons">
                    <div class="content" style="font-size: 16px;font-weight: 500;margin: 20px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">{{item.name}}</div>
                    <div class="content">{{item.beginTime | dateFormate2}}至{{item.endTime | dateFormate2}}</div>
                </div>
            </div>
        </div>
        <div class="dialog-footer" slot="footer">
            <el-button @click="doCancel">取 消</el-button>
            <el-button @click="doModalClose" size="small" type="primary">确 定</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts" src="./CheckCouponDialog.ts">
</script>

<style lang="scss">
.check-coupons-dialog{
    display: flex;
    align-items: center;
    justify-content: center;
    .wrap{
        height: 445px;
        .item{
            width: 228px;
            height: 108px;
            border-radius: 5px;
            display: inline-block;
            margin-bottom: 24px;
            background: rgba(0, 0, 0, 0.0470588235294118);
            margin-right: 10px;
            .content{
                text-align: center;
            }
        }
    }
    .el-dialog{
        width: 800px !important;
        height: 600px !important;
    }
}
</style>