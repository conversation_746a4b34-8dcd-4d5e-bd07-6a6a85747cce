import {Component, Vue} from 'vue-property-decorator'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import FormItem from 'cmp/formitem/FormItem.vue'
import PrePayAdjustBillApi from 'http/prepay/adjustbill/PrePayAdjustBillApi'
import PrepayAdjustBillLine from 'model/prepay/adjustbill/PrepayAdjustBillLine'
import DepositActivityApi from 'http/deposit/activity/DepositActivityApi'
import DepositActivity from 'model/deposit/activity/DepositActivity'
import SelectStoreActiveDtlDialog from 'pages/deposit/mbrdeposit/active/dialog/SelectStoreActiveDtlDialog.vue'
import CheckStoreDialog from 'pages/deposit/mbrdeposit/active/dialog/CheckStoreDialog.vue'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import ActivityBody from 'model/common/ActivityBody'
import StoreRange from 'model/common/StoreRange'
import ActiveStoreDtl from 'cmp/activestoredtl/ActiveStoreDtl.vue'
import I18nPage from 'common/I18nDecorator'
import ConstantMgr from 'mgr/ConstantMgr'
import ExportConfirm from "cmp/exportconfirm/ExportConfirm"
import Channel from "model/common/Channel";
import ChannelManagementFilter from "model/channel/ChannelManagementFilter";
import {ChannelState} from "model/channel/ChannelState";
import ChannelManagementApi from "http/channelmanagement/ChannelManagementApi";
import SelectGradeDetail from 'cmp/selectGrade/SelectGradeDetail'
import CardPicList from 'cmp/cardpiclist/CardPicList'
import MarketingBudgetDtl from 'cmp/MarketingBudget/MarketingBudgetDtl'
import ActivityStateTag from 'cmp/activity-state-tag/ActivityStateTag'
import ActivityMgr from 'mgr/ActivityMgr'
import { MarketBudgetActivityEnum } from 'model/promotion/MarketBudgetActivityEnum'
import DateTimeConditionDtl from "cmp/date-time-condition-picker/DateTimeConditionDtl";

@Component({
	name: "StoreValueActiveDtl",
	components: {
		SubHeader,
		FormItem,
		SelectStoreActiveDtlDialog,
		CheckStoreDialog,
		BreadCrume,
		ActiveStoreDtl,
		ExportConfirm,
		SelectGradeDetail,
		CardPicList,
    MarketingBudgetDtl,
    ActivityStateTag,
	DateTimeConditionDtl
	},
})
@I18nPage({
	prefix: [
		"/储值/会员储值/储值充值活动/详情页面",
		"/储值/会员储值/储值充值活动/列表页面",
		"/储值/会员储值/储值充值活动/编辑页面",
		"/公用/按钮",
		"/公用/菜单",
	],
})
export default class StoreValueActiveDtl extends Vue {
	i18n: any;
	panelArray: any;
	warnMsg = "";
	dialogShow = false;
	parent: any = {};
	child: any = {};
	channelMap: any = {};
	checkdialogShow = false;
	// 分页
	page = {
		currentPage: 1,
		total: 0,
		size: 10,
	};
	dtlTableData = [];
	billDtl: DepositActivity = new DepositActivity();
	queryDtl: PrepayAdjustBillLine[] = [];
	exportDialogShow = false;
	created() {
		this.panelArray = [
			{
				name: this.i18n("充值有礼"),
				url: "store-value-active-list",
			},
			{
				name: this.i18n("储值充值活动详情"),
				url: "",
			},
		];
		this.billDtl.body = new ActivityBody();
		this.billDtl.body.stores = new StoreRange();
		this.billDtl.body.channels = [];
		this.billDtl.lines = [];
		this.getStoreValueDtl();
		this.getChannelList();
	}

  get isOaActivity() {
    return ActivityMgr.isOaActivity(MarketBudgetActivityEnum.PrepayDepositActivityRule)
  }

	get getSource() {
		let str = "";
		if (this.channelMap && this.billDtl && this.billDtl.body && this.billDtl.body.channels && this.billDtl.body.channels.length > 0) {
			this.billDtl.body.channels.forEach((item: Channel) => {
				if (item.type) {
					str += this.channelMap[item.type + item.id] + "、";
				} else {
					str += "--";
				}
			});
			str = str.substring(0, str.length - 1);
			return str;
		} else {
			return "--";
		}
	}

	getChannelList() {
		let query = new ChannelManagementFilter();
		query.stateEquals = ChannelState.ENABLED;
		ChannelManagementApi.query(query)
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					this.channelMap = {};
					for (let item of resp.data) {
						this.channelMap[item.channel.type + item.channel.id] = item.name;
					}
				}
			})
			.catch((error) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}

	/**
	 * 分页页码改变的回调
	 * @param val
	 */
	doBack() {
		this.$router.back();
	}

	doCheckDialogClose() {
		this.checkdialogShow = false;
	}
	doExportDialogClose() {
		this.exportDialogShow = false;
	}
	doCheckStore() {
		this.checkdialogShow = true;
	}
	doConfirmSummit(flag: any) {
		if (flag) {
			this.submitAudit(this.$route.query.id as string);
		}
	}
	onHandleCurrentChange(val: number) {
		this.page.currentPage = val;
		this.getQueryDetail();
	}

	/**
	 * 每页多少条的回调
	 * @param val
	 */
	onHandleSizeChange(val: number) {
		this.page.size = val;
		this.getQueryDetail();
	}

	doDialogClose() {
		this.dialogShow = false;
	}

	doCheckDtl(parent: any, child: any) {
		this.parent = parent;
		this.child = child;
		this.dialogShow = true;
	}

	doAudit() {
		const loading = this.$loading(ConstantMgr.loadingOption);
		DepositActivityApi.checkConflict(this.billDtl)
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					if (resp.data) {
						// 有冲突
						this.warnMsg = resp.data;
						this.exportDialogShow = true;
					} else {
						// 无冲突
						this.submitAudit(this.$route.query.id as string);
					}
					loading.close();
				}
			})
			.catch((error) => {
				loading.close();
				this.$message.error(error.message);
			});
	}

	doCopy() {
		this.$router.push({ name: "store-value-active-add", query: { id: this.$route.query.id as string, from: "copy" } });
	}

	doModify() {
		this.$router.push({ name: "store-value-active-add", query: { id: this.$route.query.id as string, from: "edit" } });
	}

	doDelete() {
		this.$confirm(this.i18n("是否确定删除该单据?"), this.i18n("删除"), {
			confirmButtonText: this.i18n("确定"),
			cancelButtonText: this.i18n("取消"),
		}).then(() => {
			this.submitDelete(this.$route.query.id as string);
		});
	}

	doValidate() {
		this.$router.push({
			name: "effect-evaluation",
			query: { id: this.$route.query.id as string, row: this.billDtl as any },
		});
	}

	doStop() {
		this.$confirm(this.i18n("是否确定终止该单据?"), this.i18n("终止"), {
			confirmButtonText: this.i18n("确定"),
			cancelButtonText: this.i18n("取消"),
		}).then(() => {
			this.submitStop(this.$route.query.id as string);
		});
	}

	private submitStop(uuid: string) {
		DepositActivityApi.stop(uuid)
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					this.$message.success(this.i18n("终止成功"));
					this.getStoreValueDtl();
				}
			})
			.catch((error) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}

	private submitDelete(uuid: string) {
		DepositActivityApi.remove(uuid)
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					this.$message.success(this.i18n("删除成功"));
					this.$router.push({ name: "store-value-active-list" });
					// this.getStoreValueDtl()
				}
			})
			.catch((error) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}

	private submitAudit(uuid: string) {
		DepositActivityApi.audit(uuid)
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					this.$message.success(this.i18n("审核成功"));
					this.getStoreValueDtl();
				}
			})
			.catch((error) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}

	private getStoreValueDtl() {
		DepositActivityApi.info(this.$route.query.id.toString())
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					this.billDtl = resp.data;
				}
			})
			.catch((error) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}

	private getQueryDetail() {
		PrePayAdjustBillApi.queryDetail(this.$route.query.id.toString(), this.page.currentPage - 1, this.page.size)
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					this.queryDtl = resp.data;
					this.page.total = resp.total;
				}
			})
			.catch((error) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}
}
