<template>
  <div class="gift-activity-refund-report">
    <div class="current-page">
      <el-form label-width="140px">
        <el-row  class="query">
          <TimeRange no-i18n @submit="handleTimeRange" ref="timeRange"></TimeRange>
        </el-row>
        <el-row  class="query" style="margin-top: 8px">
          <el-col :span="8">
            <form-item label="卡号">
              <el-input placeholder="类似于"  v-model="query.codeLikes"/>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item label="退款交易号">
              <el-input placeholder="类似于" v-model="query.transNoLikes"/>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item label="原交易号">
              <el-input placeholder="类似于" v-model="query.originalTransNoLikes"/>
            </form-item>
          </el-col>
        </el-row>
        <el-row  class="query" style="margin-top: 8px" v-if="expandQuery">
          <el-col :span="8">
            <form-item label="卡模板">
              <el-input placeholder="等于" v-model="query.templateLikes"/>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item label="发生组织">
              <SelectStores v-model="query.orgIdEquals" :isOnlyId="true" :hideAll="false" width="100%"
                :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
              </SelectStores>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水/发生区域')">
              <el-select placeholder="不限" v-model="query.zoneIdEquals">
                <el-option
                  :label="formatI18n('/公用/查询条件/下拉列表/不限')"
                  :value="null"
                >{{ formatI18n('/公用/查询条件/下拉列表/不限') }}</el-option>
                <el-option
                  :key="item.zone.id"
                  :label="'['+item.zone.id+']'+item.zone.name"
                  :value="item.zone.id"
                  v-for="item in areaData"
                >[{{item.zone.id}}]{{item.zone.name}}</el-option>
              </el-select>
            </form-item>
          </el-col>
        </el-row>
        <el-row  class="query" style="margin-top: 8px" v-if="expandQuery">
          <el-col :span="8">
            <form-item label="余额增加">
              <div class="el-date-editor" style="display:flex">
                <el-input :placeholder="formatI18n('/储值/预付卡/预付卡查询/列表页面/最小值')" class="el-range-input"
                          v-model="query.balanceGreaterOrEquals"/>
                <span class="el-range-separator" style="padding:0px 15px;height:32px;">-</span>
                <el-input :placeholder="formatI18n('/储值/预付卡/预付卡查询/列表页面/最大值')" class="el-range-input"
                          v-model="query.balanceLessOrEquals"/>
              </div>
            </form-item>
          </el-col>
        </el-row>
        <el-row  class="query" style="margin-top: 8px">
          <el-col :span="8">
            <form-item label=" ">
              <el-button class="btn-search" type="primary" @click="doSearch">查询</el-button>
              <el-button class="btn-reset" @click="doReset">重置</el-button>
              <el-button type="text" @click="expandQuery=!expandQuery">
                <span v-if="!expandQuery"><i class="el-icon-arrow-down"></i>{{formatI18n('/公用/查询条件/展开')}}</span>
                <span v-if="expandQuery"><i class="el-icon-arrow-up"></i>{{formatI18n('/公用/查询条件/收起')}}</span>
              </el-button>
            </form-item>
          </el-col>
        </el-row>
      </el-form>
      <hr/>
      <el-row style="line-height: 35px" v-if="isShowSum">
        <i class="el-icon-warning" />
        <i18n k="/储值/预付卡/实体礼品卡报表/退款流水/实体礼品卡交易退款{0}元，实充增加{1}元，返现增加{2}元">
          <template slot="0">&nbsp;<span style="color: red">{{dataUtil.showTotalAmount(sum.totalAmount)}}</span>&nbsp;</template>
          <template slot="1">&nbsp;<span style="color: red">{{dataUtil.showTotalAmount(sum.amount)}}</span>&nbsp;</template>
          <template slot="2">&nbsp;<span style="color: red">{{dataUtil.showTotalAmount(sum.giftAmount)}}</span>&nbsp;</template>
        </i18n>
      </el-row>
      <el-row class="table">
        <el-table
            :data="queryData"
            style="width: 100%;margin-top: 20px"
        >
          <el-table-column
              label="卡号"
              prop="code"
              width="230"
          >
            <template slot-scope="scope">
              <span no-i18n :title="scope.row.code">{{scope.row.code}}</span>
            </template>
          </el-table-column>
          <el-table-column
              label="退款时间"
              prop="occurredTime"
              width="140"
          >
            <template slot-scope="scope">
              <span no-i18n></span>{{dataUtil.format(scope.row.occurredTime, 'yyyy-MM-dd HH:mm:ss')}}
            </template>
          </el-table-column>
          <el-table-column
              label="发生组织"
              prop="occurredOrg"
          >
            <template slot-scope="scope">
              <span no-i18n
                    :title="dataUtil.showIdName(scope.row.occurredOrg)">{{dataUtil.showIdName(scope.row.occurredOrg)}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水/发生区域')" prop="zone" >
                            <template slot-scope="scope">
                                <div v-if="scope.row.zone!==null && scope.row.zone!==''">
                                    <el-tooltip class="item" effect="light"  placement="right-end">
                                    <div> {{'['+scope.row.zone.id+']'}}{{scope.row.zone.name}}</div>
                                    <div slot="content">
                                         {{'['+scope.row.zone.id+']'}}{{scope.row.zone.name}}
                                    </div>
                                    </el-tooltip>
                                </div>
                                <div v-else>-</div>
                                </template>
          </el-table-column>
          <el-table-column
              label="持卡人"
              prop="hdCardMbrId"
          >
            <template slot-scope="scope">
              <span no-i18n :title="dataUtil.showMemberId(scope.row)">{{dataUtil.showMemberId(scope.row)}}</span>
            </template>
          </el-table-column>
          <el-table-column
              label="卡模板"
              prop="templateName"
          >
            <template slot-scope="scope">
              <a no-i18n href="javascript: void(0)" :title="scope.row.templateName"
                 @click="gotoTplDtl(scope.row.templateNumber)">{{scope.row.templateName}}</a>
            </template>
          </el-table-column>
          <el-table-column
              label="卡面额(元)"
              prop="faceAmount"
              align="right"
              width="140"
          >
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.faceAmount.toFixed(2)}}</span>
            </template>
          </el-table-column>
          <el-table-column
              label="余额变动(元)"
              prop="totalAmount"
              align="right"
              width="140"
          >
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.totalAmount.toFixed(2)}}</span>
            </template>
          </el-table-column>
          <el-table-column
              label="实充变动(元)"
              prop="amount"
              align="right"
              width="140"
          >
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.amount.toFixed(2)}}</span>
            </template>
          </el-table-column>
          <el-table-column
              label="返现变动(元)"
              prop="giftAmount"
              align="right"
              width="140"
          >
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.giftAmount.toFixed(2)}}</span>
            </template>
          </el-table-column>
          <el-table-column
              label="退款交易号"
              prop="transNo"
          >
            <template slot-scope="scope">
              <span no-i18n :title="scope.row.transNo">{{scope.row.transNo}}</span>
            </template>
          </el-table-column>
          <el-table-column
              label="原交易号"
              prop="originalTransNo"
          >
            <template slot-scope="scope">
              <span no-i18n :title="scope.row.sourceTransNo">{{scope.row.sourceTransNo}}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-pagination no-i18n
                     :current-page="page.currentPage"
                     :page-size="page.size"
                     :page-sizes="[10, 20, 30, 40]"
                     :total="page.total"
                     @current-change="onHandleCurrentChange"
                     @size-change="onHandleSizeChange"
                     background
                     layout="total, prev, pager, next, sizes,  jumper"
                     class="pagin"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script lang="ts" src="./RefundReport.ts">
</script>

<style lang="scss" scoped>
.gift-activity-refund-report {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: auto;
  .total {
    margin: 20px;
  }

  div.el-range-input {
    flex: 1;
    input.el-input__inner {
      border: none;
      padding: 0px;
      line-height: 1;
      height: 100%;
    }
  }

  .current-page {
    height: calc(100% - 150px);
    padding: 0 20px 20px 20px;
    .el-select {
      width: 100%;
    }

    .query {
      .el-form-item {
        margin-bottom: 0;
      }
    }

    .pagin {
      margin-top: 25px;
    }

    tbody {
      .cell {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }
}
</style>
