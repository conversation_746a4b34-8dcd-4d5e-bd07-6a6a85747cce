<template>
  <el-dialog :title="title" width="640px" :show-close="false" :close-on-click-modal="false" :visible.sync="groupVisible">
    <el-form :model="formData" ref="form" :rules="rules">
      <el-form-item :label="i18n('选择类型')" prop="infoType">
        <el-radio-group v-model="formData.infoType" @change="closePopover">
          <el-radio label="AttributeDistributionCalculationRule">{{i18n('属性分布')}}</el-radio>
          <el-radio label="AttributeStatisticsCalculationRule">{{i18n('属性统计')}}</el-radio>
          <el-radio label="CrossDistributionCalculationRule">{{i18n('交叉分布')}}</el-radio>
          <el-radio label="CrossStatisticsCalculationRule">{{i18n('交叉统计')}}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="isShowTarget" :label="i18n('/数据/数据洞察/详情页/指标')" prop="target">
        <div class="tag-line">
          <el-tag type="info" v-for="(item,index) in formData.target" @close="removeTarget(index)" :key="index" closable disable-transitions
            style="margin: 0 8px 4px 0">
            {{getTargetName(item)}}
          </el-tag>
        </div>
        <PortraitIndicatorSelect v-if="formData.target.length < 1" @submit="doSubmitTarget" ref="portraitIndicatorTarget" :isShowMemberCount="false"
          type="target">
        </PortraitIndicatorSelect>
      </el-form-item>
      <el-form-item :label="i18n('/数据/数据洞察/详情页/维度')" prop="dimension">
        <div class="tag-line">
          <el-tag type="info" v-for="(item,index) in formData.dimension" @close="removeDimension(index)" :key="index" closable disable-transitions
            style="margin: 0 8px 4px 0">
            {{getDimensionName(item)}}
          </el-tag>
        </div>
        <PortraitIndicatorSelect v-if="formData.dimension.length < 10" @submit="doSubmitDimension" ref="portraitIndicatorDimension"
          :isShowMemberCount="false" type="dimension">
        </PortraitIndicatorSelect>
      </el-form-item>
    </el-form>
    <div class="footer">
      <el-button size="large" @click="closeDialog">{{i18n('/公用/按钮/取消')}}</el-button>
      <el-button type="primary" size="large" @click="confirm">{{i18n('/公用/按钮/确定')}}</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./AddPortraitInfoDialog.ts">
</script>

<style lang="scss" scoped>
.tag-line {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.footer {
  display: flex;
  justify-content: end;
  margin-top: 50px;
}
</style>