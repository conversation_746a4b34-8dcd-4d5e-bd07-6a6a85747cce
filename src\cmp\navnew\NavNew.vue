<template>
  <div class="nav-new">
    <div class="left">
      <div class="left-title">
        <div class="open-side" @click="doShowSubNav" v-if="!iconVisiable"></div>
      </div>
      <div class="content" v-if="menus">
        <div v-for="(item, index) in menus" :key="index"
             @click="doMouseClick(index)"
             @mouseenter="doMouseOver(index)"
             @mouseleave="doMouseLeave(index)"
             v-popper:popper
             :class="[{ 'content-li': true,
                        'is-selected': selectedMajorMenu && item.hash === selectedMajorMenu.hash,
                        'is-hover': selectedMajorMenu && item.hash !== selectedMajorMenu.hash }]">
          <!--展开时图标文字-->
          <div>
            <img v-if="item.hash !== majorHash" :src="item.icon" alt=""/>
            <img v-if="item.hash === majorHash" :src="item.iconSelect" alt=""/>
            <div class="nav-name">{{i18n(item.name)}}</div>
          </div>
          <!--收起时图标文字-->
          <div ref="popper_div">
            <el-popover
                    :close-delay="100"
                    :open-delay="100"
                    :tabindex="index"
                    :visible-arrow="false"
                    placement="right-start"
                    popper-class="nav-popover"
                    ref="popper"
                    trigger="click"
                    v-if="!subNavVisiable">
              <div class="nav-popover-context">
                <div :key="childIndex" class="inner-context" v-for="(inner, childIndex) in item.children">
                  <div :title="i18n(inner.subTitle)" class="inner-title" v-if="inner.subTitle">{{i18n(inner.subTitle)}}</div>
                  <ul class="inner-ul">
                    <li :class="[{'li-sub-selected': selectedMinorMenu && item.hash === selectedMinorMenu.hash},
                               {'inner-lis': inner.children.length > 1}]" :key="subIndex"
                        @click.stop="doGoView(item)"
                        class="inner-li"
                        :title="i18n(item.name)"
                        v-for="(item, subIndex) in inner.children">
                      {{i18n(item.name)}}
                    </li>
                  </ul>
                </div>
              </div>
            </el-popover>
          </div>

        </div>
      </div>
    </div>
    <div class="right" v-if="subNavVisiable">
      <div class="right-title">
        <div class="logo"></div>
        <div class="close-side" @click="doHideSubNav" v-if="iconVisiable"></div>
        <div class="line"></div>
      </div>
      <div class="content" v-if="selectedMajorMenu">
        <template v-if="menus && menus.length > 0"></template>
        <ul class="item" v-for="(item,index) in selectedMajorMenu.children" :key="index">
          <li>
            <div :title="i18n(item.subTitle)" class="gray-title color" v-if="item.subTitle">
              {{i18n(item.subTitle)}}
              <!-- <i class="el-icon-arrow-down title-icon" :style="{transform: item.fold ? 'rotate(-180deg)' : 'rotate(0deg)'}" @click="doClickFold(index)"></i> -->
            </div>
            <!-- <div class="sub-nav-children" :style="{height: item.fold ? '0' : getMenuHeight(index)}"> -->
              <ul v-for="(sub, subIndex) in item.children" :key="subIndex">
              <li class="text"
                  :title="i18n(sub.name)"
                  :class="[{'is-sub-selected': selectedMinorMenu && sub.hash === selectedMinorMenu.hash}]"
                  @click="doGoView(sub)">
                {{i18n(sub.name)}}
              </li>
            </ul>
            <!-- </div> -->
          </li>
        </ul>
      </div>
    </div>
    <div class="file-entry" @click="onToFileEntry">
      <div class="tip">{{formatI18n('/公用/预约文件列表/文件中心')}}</div>
    </div>
  </div>
</template>

<script lang="ts" src="./NavNew.ts">
</script>

<style lang="scss">
  @mixin navNameOpacity1 {
    .nav-name {
      opacity: 1 !important;
    }
  }

  @mixin iconAndName {
    img {
      display: block;
      width: 32px;
      height: 32px;
    }
    .nav-name {
      height: 20px;
      line-height: 20px;
    }
    &:hover {
      @include navNameOpacity1
    }
  }

  .nav-new {
    width: auto;
    display: flex;
    position: relative;
    .left {
      width: 64px;
      display: flex;
      flex-direction: column;
      background-color: #007EFF;
      .left-title {
        width: 100%;
        height: 64px;
        position: relative;
        .open-side {
          position: absolute;
          top: 0;
          left: 0;
          width: 32px;
          height: 64px;
          cursor: pointer;
          background: url(~assets/image/nav/ic_zhankai.svg) no-repeat;
          &:hover {
            background: url(~assets/image/nav/ic_zhankai_hover.svg) no-repeat;
          }
        }
      }
      .content {
        width: 64px;
        flex: 1;
        padding: 0;
        margin: 0;
        .content-li {
          height: 80px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          text-align: center;
          position: relative;
          color: white;
          text-align: -webkit-center;
          i {
            font-size: 32px;
          }
          .nav-name {
            opacity: 0.7;
          }
          @include iconAndName;
        }
        .nav-popover-content {
          width: 64px;
          height: 80px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          @include iconAndName;
          outline: none;
          &:focus {
            outline: none !important;
          }
        }

      }
    }
    .right {
      width: 180px;
      display: flex;
      flex: 1;
      flex-direction: column;
      background-color: white;
      box-shadow: 2px 0px 8px 0px rgba(78, 84, 105, 0.1);
      .right-title {
        width: 180px;
        height: 64px;
        position: relative;
        .logo {
          position: absolute;
          left: 14px;
          top: 15px;
          width: 120px;
          height: 35px;
          background: url(~assets/image/auth/logo_login.png) no-repeat;
          background-size: cover;
        }
        .close-side {
          position: absolute;
          right: 0;
          top: 0;
          width: 32px;
          height: 64px;
          cursor: pointer;
          background: url(~assets/image/nav/ic_retract.svg) no-repeat;
          &:hover {
            background: url(~assets/image/nav/ic_retract_hover.svg) no-repeat;
          }
        }
        .line {
          border-bottom: 1px solid #F1F5F9;
          position: absolute;
          bottom: 0;
          left: 8px;
          right: 8px;
        }
      }
      .content {
        width: 180px;
        flex: 1;
        padding-top: 10px;
        overflow: auto;
        ul {
          list-style: none;
        }
        .item {
          padding: 0;
          margin: 0 0 15px 0;
          list-style: none;
          display: block;
          .gray-title {
            height: 32px;
            line-height: 32px;
            padding-left: 20px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            &::before {
              content: '';
              width: 6px;
              height: 6px;
              border-radius: 50%;
              display: inline-block;
              border: 2px solid #D0D4DA;
              margin-right: 6px;
            }
            .title-icon {
              float: right;
              line-height: 32px;
              margin-right: 12px;
              cursor: pointer;
              transition: all 0.3s;
            }
          }
          .sub-nav-children {
            overflow: hidden;
            transition: all 0.3s;
          }
          .text {
            height: 32px;
            line-height: 32px;
            padding-left: 12px;
            cursor: pointer;
            margin: 4px 8px;
            border-radius: 8px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            &:hover {
              color: #007EFF;
            }
          }
          .color {
            color: #A1A6AE;
          }
        }
      }
    }
    .my-popover {
      background-color: red;
    }
    .is-selected {
      background: rgba(0, 0, 0, 0.15);
      color: white;
      opacity: 1 !important;
      cursor: default;
      @include navNameOpacity1
    }
    .is-hover {
      &:hover {
        opacity: 1 !important;
        color: white;
        background: rgba(255, 255, 255, 0.2);
        cursor: pointer;
        @include navNameOpacity1
      }
    }
    .is-sub-selected {
      color: #007EFF;
      background-color: #E4F1FF;
    }
    .file-entry {
      position: absolute;
      width: 40px;
      height: 40px;
      bottom: 28px;
      left: 12px;
      cursor: pointer;
      border-radius: 24px;
      background: url(~assets/image/nav/ic_wenjianliebiao_normal.svg) no-repeat;
      &:hover {
        background: url(~assets/image/nav/ic_wenjianliebiao_hover.svg) no-repeat;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.2);
        .tip {
          display: block;
        }
      }
      .tip {
        display: none;
        width: 100px;
        height: 32px;
        line-height: 32px;
        font-size: 14px;
        color: #ffffff;
        background: rgba(36, 39, 43, 0.8);
        position: absolute;
        top: 4px;
        left: 56px;
        border-radius: 4px;
        text-align: center;
        z-index: 1000000 !important;
        &::before {
          content: '';
          width: 0;
          height: 0;
          border: 5px solid;
          position: absolute;
          border-color: transparent rgba(36, 39, 43, 0.8) transparent transparent;
          left: -10px;
          top: 11px;
        }
      }
    }
  }

  .li-sub-selected {
    color: #007EFF;
    background-color: #E4F1FF;
  }

  .inner-title {
    color: #A1A6AE;
    height: 32px;
    line-height: 32px;
    padding-left: 12px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    &::before {
      content: '';
      width: 6px;
      height: 6px;
      border-radius: 50%;
      display: inline-block;
      border: 2px solid #D0D4DA;
      margin-right: 6px;
    }
  }

  .nav-popover {
    left: 64px;
    top: 0px;
    padding: 0 0 0 8px !important;
    border-radius: 8px !important;
    box-shadow: none !important;
    margin-left: 0 !important;
    border: none !important;
    background-color: transparent !important;
    .nav-popover-context {
      background-color: #ffffff;
      padding: 24px 8px;
      border-radius: 8px;
      box-shadow: 4px 0px 25px 0px rgba(78, 84, 105, 0.16);
      .inner-context {
        .inner-ul {
          list-style: none;
        }

        .inner-li {
          width: 164px;
          height: 32px;
          line-height: 32px;
          padding-left: 12px;
          border-radius: 8px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          &:hover {
            color: #007EFF;
            cursor: pointer;
          }
        }

        .inner-lis {
          margin: 4px 0;
          &:first-child {
            margin: 0 0 4px 0;
          }
          &:last-child {
            margin: 4px 0 0 0;
          }
        }
      }
      .inner-context:not(:last-child) {
        .inner-ul {
          list-style: none;
          margin-bottom: 16px;
        }
      }
    }
  }

</style>