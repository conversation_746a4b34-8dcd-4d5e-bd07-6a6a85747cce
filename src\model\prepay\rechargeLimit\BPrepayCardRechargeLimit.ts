/*
 * @Author: 黎钰龙
 * @Date: 2024-02-28 09:52:52
 * @LastEditTime: 2024-02-28 10:07:54
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\prepay\rechargeLimit\BPrepayCardRechargeLimit.ts
 * 记得注释
 */
import CardRechargeLimitRule from "./CardRechargeLimitRule"

export default class BPrepayCardRechargeLimit {
  // 卡单次充值额度设置排序
  sequence: Nullable<number> = null
  // 卡单次充值额度设置规则
  body: Nullable<CardRechargeLimitRule> = null
}