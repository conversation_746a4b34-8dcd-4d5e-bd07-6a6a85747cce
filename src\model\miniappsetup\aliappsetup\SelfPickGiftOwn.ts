/*
 * @Author: 黎钰龙
 * @Date: 2023-12-07 10:14:19
 * @LastEditTime: 2023-12-07 11:28:09
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\miniappsetup\aliappsetup\SelfPickGiftOwn.ts
 * 记得注释
 */

import SelfPickGift from "./SelfPickGift";

export default class SelfPickGiftOwn extends SelfPickGift {
  //uuid（前端自己用）
  uuid: Nullable<string> = null
  //时间数组（前端自己用）
  timeRange: Nullable<Date[]> = []
}