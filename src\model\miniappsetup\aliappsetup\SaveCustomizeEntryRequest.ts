/*
 * @Author: l<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-11-25 09:18:28
 * @LastEditors: 黎钰龙
 * @LastEditTime: 2023-03-29 12:52:19
 * @FilePath: \phoenix-web-ui\src\model\miniappsetup\aliappsetup\SaveCustomizeEntryRequest.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import CustomizeEntry from './CustomizeEntry'

export default class SaveCustomizeEntryRequest {
  // 功能入口
  customizeEntries: CustomizeEntry[] = []
  // 操作人
  operator: Nullable<string> = null
  //是否开启配置  "start" 开启  "stop" 不开启
  state: string = 'stop'
}