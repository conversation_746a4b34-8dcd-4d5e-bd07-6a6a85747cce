<template>
  <el-row class="ali-pay-init" v-loading="loading">
    <template v-if="!hasCard">
      <div class="header" v-if="!loading">
        <el-steps :active="active" :align-center="true" finish-status="success" process-status="finish">
          <el-step title="支付宝应用申请"></el-step>
          <el-step title="创建会员卡"></el-step>
          <el-step title="会员卡投放"></el-step>
        </el-steps>
      </div>
      <div class="context" v-if="!loading">
        <div class="title">
          请完成支付宝商家应用申请及相关配置
        </div>
        <div class="field">
          注册并登录 <a target="_blank" href="https://developers.alipay.com/developmentAccess/developmentAccess.htm">支付宝开放平台</a> ，
          按照 <a target="_blank"
            href="http://apidoc.hd123.com/public/phoenix/%E6%94%AF%E4%BB%98%E5%AE%9D%E5%95%86%E5%AE%B6%E4%BC%9A%E5%91%98%E5%BA%94%E7%94%A8%E5%88%9D%E5%A7%8B%E5%8C%96%E6%89%8B%E5%86%8C.pdf">支付宝商家会员应用初始化手册.pdf</a>
          中的步骤说明完成应用申请，并提供相关信息给海鼎技术支持人员进行系统配置。
        </div>
        <div class="field tip">
          <p>完成后可获得以下能力:</p>

          <ul>
            <li>支持支付宝扫码即会员、支付即会员功能，增加引流渠道，实现会员统一管理</li>
            <li>支持会员账户信息同步到支付宝卡包中的商家会员卡</li>
            <li>支持会员在支付宝查看和使用相关的会员权益，增强会员与商家之间的粘性</li>
          </ul>
        </div>
        <div class="btn">
          <el-button v-if="hasOptionPermission('/设置/渠道/支付宝会员设置/支付宝会员初始化', '配置维护')" type="primary" @click="onToCard">已完成，前往下一步</el-button>
        </div>
      </div>
    </template>
    <AlipayInitCompleted v-else></AlipayInitCompleted>
  </el-row>
</template>

<script lang="ts" src="./Alipay.ts"></script>

<style lang="scss">
.ali-pay-init {
  width: 100%;
  background: #fff;
  min-width: 1024px;
  .header {
    height: 100px;
    padding-top: 25px;
    border-bottom: 1px solid #f2f2f2;
    background-color: #fcfcfc;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
  }
  .context {
    padding: 40px 20px;
    .title {
      font-size: 18px;
      color: rgba(51, 51, 51, 1);
      margin-bottom: 16px;
      font-weight: bold;
    }
    .field {
      font-size: 14px;
      color: #888;
      p {
        font-size: 16px;
      }
      ul {
        margin: 10px 0 0 20px;
        li {
          margin: 5px 0;
        }
      }
    }
    .tip {
      margin: 50px 0;
    }
    .btn {
    }
  }
  .el-step__head.is-success {
    color: #20a0ff !important;
    border-color: #20a0ff !important;
  }
  .el-step__title.is-success {
    color: #a0abbc !important;
  }
  .el-step__title.is-finish {
    color: #a0abbc !important;
  }
}
</style>