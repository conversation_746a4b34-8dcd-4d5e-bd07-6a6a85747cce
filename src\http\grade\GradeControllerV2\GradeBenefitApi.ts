import ApiClient from 'http/ApiClient'
import Response from 'model/default/Response'
import GradeBenefitV2 from 'model/grade/GradeControllerV2/GradeBenefitV2'

export default class GradeBenefitApi {
  /**
   * 等级权益详情
   * 等级权益详情。
   * 
   */
  static get(): Promise<Response<GradeBenefitV2>> {
    return ApiClient.server().get(`/v2/grade/benefit/get`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 等级权益保存
   * 等级权益保存。
   * 
   */
  static save(body: GradeBenefitV2): Promise<Response<void>> {
    return ApiClient.server().post(`/v2/grade/benefit/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
