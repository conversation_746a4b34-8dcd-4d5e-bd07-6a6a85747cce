/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:19
 * @LastEditTime: 2024-05-08 14:52:19
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\prepay\config\PrePayConfigApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import PrepayConfig from 'model/prepay/config/PrepayConfig'
import Response from 'model/common/Response'
import PointsAdjustBillConfig from "model/points/adjustbill/PointsAdjustBillConfig";
import FaceAmount from 'model/prepay/config/FaceAmount';

export default class PrePayConfigApi {
  /**
   * 查询储值是否开启配置
   *
   */
  static get(): Promise<Response<PrepayConfig>> {
    return ApiClient.server().get(`/v1/prepay/config/get`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 已废弃
   * 查询储值面额
   *
   */
  static getFaceAmounts(): Promise<Response<number[]>> {
    return ApiClient.server().get(`/v1/prepay/config/get/face-amount`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 已废弃
   * 保存储值面额
   *
   */
  static saveFaceAmounts(body: number[]): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/prepay/config/save/face-amount`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
 * 查询储值配置
 * 查询储值配置。
 * 
 */
  static getConfig(): Promise<Response<FaceAmount>> {
    return ApiClient.server().get(`/v1/prepay/config/getConfig`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
 * 保存储值配置
 * 保存储值配置。
 * 
 */
  static saveConfig(body: FaceAmount): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/prepay/config/saveConfig`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
