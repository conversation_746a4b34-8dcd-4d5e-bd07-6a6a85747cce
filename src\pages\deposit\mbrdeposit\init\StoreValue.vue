<template>
  <div class="store-value">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button key="编辑" @click="doEdit" size="small" type="primary"
          v-if="detail.faceAmountList.length > 0 && hasOptionPermission('/储值/储值管理/充值面额设置', '配置维护')">编辑
        </el-button>
        <el-button key="立即设置" @click="doAdd" size="small" type="primary"
          v-if="detail.faceAmountList.length <= 0 && hasOptionPermission('/储值/储值管理/充值面额设置', '配置维护')">立即设置
        </el-button>
      </template>
    </BreadCrume>
    <div style="overflow: auto;height: 95%">
      <div class="flex-wrap">
        <div class="flex-item left">
          <img class="size-back" src="~assets/image/storevalue/iphone.png">
          <img class="size-content" src="~assets/image/storevalue/rechargeInit.png">
        </div>
        <div class="flex-item right">
          <div v-if="detail.faceAmountList.length > 0">
            <div style="font-size: 12px;color: rgba(51, 51, 51, 0.647058823529412);margin-left: 22px;margin-bottom: 20px;">
              <i class="iconfont  ic-info" style="font-size: 18px;color: #20A0FF"></i>&nbsp;
              <span>设置在线充值的可选面额（如左图所示），最多可设置10个；在储值充值活动中，亦将按此面额分别设置赠礼规则。</span>
            </div>
            <form-item label="充值面额：">
              <div class="right-content">
                <div class="money-value" v-for="(item,index) in detail.faceAmountList" :key="index">{{ item }}<span>元</span></div>
              </div>
            </form-item>
          </div>
          <div v-if="detail.faceAmountList.length <= 0">
            <div style="font-size: 12px;color: rgba(51, 51, 51, 0.647058823529412)">
              <i class="iconfont ic-info" style="font-size: 18px;color: #20A0FF"></i>&nbsp;
              <span>设置在线充值的可选面额（如左图所示），最多可设置10个；在储值充值活动中，亦将按此面额分别设置赠礼规则。</span>
            </div>
            <div style="margin-top: 30px">充值面额：<span style="color: red">未设置</span></div>
          </div>
          <form-item :label="i18n('自定义金额')">
            <div class="normal-content">
              <template v-if="detail.showCustomizedAmount">
                {{i18n('展示')}}，{{i18n('充值基数')}} {{detail.customizedBase}} {{i18n('/储值/预付卡/充值卡制售单/编辑页面/元')}}
              </template>
              <template v-else>
                {{i18n('不展示')}}
              </template>
            </div>
          </form-item>
          <form-item :label="i18n('C端默认充值面额')">
            <div class="normal-content">
              <template v-if="detail.appletDefaultDepositAmount">
                {{ detail.appletDefaultDepositAmount }}
                <span>元</span>
              </template>
              <template v-else>
                --
              </template>
            </div>
          </form-item>
          <form-item :label="i18n('充值须知')">
            <div class="normal-content" v-if="detail.depositNotice">
              <span v-html="xss(detail.depositNotice.replace(/\n/g, '<br/>'))"></span>
            </div>
            <div class="normal-content" v-else>--</div>
          </form-item>
          <form-item :label="i18n('/储值/预付卡/预付卡充值有礼/充值协议')">
            <div v-if="detail.depositAgreement" v-html="detail.depositAgreement" style="padding:10px;border:1px solid #eee"></div>
            <div v-else style="height:36px;line-height:32px">--</div>
          </form-item>
        </div>
      </div>
    </div>

  </div>
</template>

<script lang="ts" src="./StoreValue.ts">
</script>

<style lang="scss">
.store-value {
  width: 100%;
  height: 100%;
  background-color: white;
  overflow: hidden;
  .qf-form-item .qf-form-label {
    width: 120px !important;
  }

  .flex-wrap {
    display: flex;
    padding: 50px;
    background-color: white;

    .flex-item {
      .size-back {
        width: 285px;
        height: 586px;
      }

      .size-content {
        width: 255px;
        height: 455px;
        position: absolute;
        top: 59px;
        left: 13px;
      }
    }

    .left {
      width: 300px;
      position: relative;
    }

    .right {
      flex: 1;
      padding-left: 30px;

      .right-content {
        width: 400px;
        display: flex;
        flex-flow: row wrap;
        align-content: flex-start;

        .money-value {
          font-size: 18px;
          background-color: #f8f8f8;
          height: 30px;
          width: 180px;
          display: inline-block;
          border: 1px solid #bfbfbf;
          text-align: center;
          border-radius: 5px;
          margin-top: 5px;
          margin-left: 15px;
          line-height: 30px;
          margin-bottom: 5px;
        }
      }

      .normal-content {
        line-height: 36px;
        vertical-align: middle;
      }
    }
  }
}
</style>