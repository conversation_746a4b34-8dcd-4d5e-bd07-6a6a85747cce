/*
 * @Author: 黎钰龙
 * @Date: 2022-11-30 10:11:05
 * @LastEditTime: 2023-02-17 14:31:13
 * @LastEditors: mazhengfa <EMAIL>
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\weixin\open-screen\wxCouponBrandContent.ts
 * 记得注释
 */
export default class CouponBrandContentItem {
  //活动名称
  activityName: Nullable<string> = null;
  //活动号
  activityNumber: Nullable<string> = null;
  brandImage?: Nullable<string> = null;
}