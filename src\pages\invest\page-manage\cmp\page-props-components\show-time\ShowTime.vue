<template>
  <div>
    <el-form
      inline
      v-if="formMode === FormModeType.form"
      :label-position="labelPosition"
      :model="value"
      :rules="rules"
      ref="form"
      style="width: 100%"
    >
      <el-form-item :label="label" style="width: 100%">
        <el-date-picker
          v-model="value.propDateRange"
          type="datetimerange"
          range-separator="-"
          :start-placeholder="i18n('开始日期')"
          :end-placeholder="i18n('结束日期')"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          :picker-options="pickerOptions"
          @change="handleChange"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-checkbox v-model="value.propCycle" class="cycle-time" @change="handleChanges">
          <span>{{ i18n('周期投放') }}</span>
          <b>（{{ i18n('每天固定时段投放，最多不超过5个时段') }}）</b>
        </el-checkbox>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" src="./ShowTime.ts"></script>

<style lang="scss" scoped>
.el-date-editor--datetimerange.el-input,
.el-date-editor--datetimerange.el-input__inner {
  width: 100%;
}
.cycle-time {
  b {
    font-weight: 400;
    font-size: 12px;
    color: #a1a6ae;
    line-height: 20px;
  }
  span {
    font-weight: 400;
    font-size: 13px;
    color: #24272b;
    line-height: 20px;
  }
}
</style>
