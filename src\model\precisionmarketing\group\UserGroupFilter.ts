export default class UserGroupFilter {
  // 名称类似于
  nameLikes: Nullable<string> = null
  // 创建方式;impt-导入；create-创建
  sourceEquals: Nullable<string> = null
  // 状态等于;Enable-已启用;Disable-已禁用
  stateEquals: Nullable<string> = null
  // 最后执行状等于：Processing-进行中；Success-计算成功；Fail-计算失败，unStart-未开始
  lastExecuteStateEquals: Nullable<string> = null
  // 标记值类似于
  tagValueLike: Nullable<string> = null
  // 创建人等于
  creatorEquals: Nullable<string> = null
  // 更新频次等于
  scheduleTypeEquals: Nullable<string> = null
  // 页数>=0
  page: number = 0
  // 页面大小>0
  pageSize: number = 0
}