/*
 * @Author: hl-cool <EMAIL>
 * @Date: 2024-08-01 11:44:12
 * @LastEditors: hl-cool <EMAIL>
 * @LastEditTime: 2024-08-05 16:59:54
 * @FilePath: \phoenix-web-ui\src\http\promotion\RedemptionCodeApi.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import BRedemptionCodeCancel from 'model/promotion/exchangeCodeValue/BRedemptionCodeCancel'
import BRedemptionCodeFilter from 'model/promotion/exchangeCodeValue/BRedemptionCodeFilter'
import BRedemptionCode from 'model/promotion/exchangeCodeValue/BRedemptionCode'
import Response from 'model/common/Response'
import ApiClient from 'http/ApiClient'
export default class RedemptionCodeApi {
    /**
     * 作废兑换码
     * 作废兑换码。
     * 
     */
    static cancel(body: BRedemptionCodeCancel): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/redemption-code/cancel`, body, {
    }).then((res) => {
        return res.data
    })
    }

    /**
     * 分页查询兑换单据
     * 分页查询兑换单据。
     * 
     */
    static query(body: BRedemptionCodeFilter): Promise<Response<BRedemptionCode[]>> {
        return ApiClient.server().post(`/v1/redemption-code/query`, body, {
        }).then((res) => {
        return res.data
        })
    }

    /**
     * 导出
     * 导出
     * 
     */
    static export(billNumber: string): Promise<Response<void>> {
        return ApiClient.server().post(`/v1/redemption-code/export/${billNumber}`, {}, {
        }).then((res) => {
        return res.data
        })
    }

    }