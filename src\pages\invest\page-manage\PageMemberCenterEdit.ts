import BreadCrume from "cmp/bread-crumb/BreadCrume";
import I18nPage from "common/I18nDecorator";
import { Component, Vue } from "vue-property-decorator";
import UploadImg from "@/cmp/upload-imgs/UploadImg.vue";
import GradeApi from "http/grade/grade/GradeApi";
import ContentTemplateFilter from "model/template/ContentTemplateFilter";
import ContentTemplateApi from "http/template/ContentTemplateApi";
import ContentTemplate from "model/template/ContentTemplate";
import CreateRequest from "model/template/CreateRequest";
import UpdateRequest from "model/template/UpdateRequest";
import PublishRequest from "model/template/PublishRequest";
import ConstantMgr from "mgr/ConstantMgr";
import CommonUtil from "util/CommonUtil";
import PageJump from "@/pages/invest/page-manage/cmp/page-props-components/page-jump/PageJump.vue";

import { BigWheelImageVO } from "./BigWheelImageVO";
import bigWheelImageLink from "./BigWheelImageUrlLink";
const backgroundImage = bigWheelImageLink[BigWheelImageVO.img_card_default_3] || require("@/assets/image/invest/img_card_default.png");
const fontColor = '#FFF4DC' // 默认字体颜色
// const backgroundImage = require("@/assets/image/invest/img_card_default.png");
@Component({
  name: "PageMemberCenterEdit",
  components: {
    PageJump,
    BreadCrume,
    UploadImg,
  },
})
@I18nPage({
  prefix: ["/公用/券模板", "/页面/页面管理"],
  auto: true,
})
export default class PagePaidMemberEdit extends Vue {
  ContentTemplateFilter: ContentTemplateFilter = new ContentTemplateFilter();
  ContentTemplate: ContentTemplate[] = []; // 活动模型
  // 保存参数
  saveParams: CreateRequest = new CreateRequest();
  // 编辑参数
  updateParams: UpdateRequest = new UpdateRequest();
  // 发布参数
  publishRequestId: PublishRequest = new PublishRequest();
  editId: any = "";
  page = {
    currentPage: 1,
    total: 0,
    size: 20,
  };
  gradeItems = [];
  currentIndex = 1;
  startX = 0;
  isDragging = false;
  dragOffset = 0;
  itemWidth = 300; // 轮播项宽度
  containerWidth = 334; // 容器宽度
  // 卡面元素显隐
  showCardNumber = true // 卡号
  showPeriod = false // 有效期
  showGrowth = false // 成长值
  // 接收属性值
  widgets: any = [];
  imgData: any = {
    id: "memberCenter",
    uuid: "",
    propTitle: "会员中心",
    propCardElement: {
      // 卡面元素显隐
      showCardNumber: false, // 卡号
      showPeriod: true, // 有效期
      showGrowth: true, // 成长值
    },
    propBackgroundType: "unified", // 'unified': 统一背景; 'byGrade': 按等级
    propUnifiedCardBackground: {
      // 统一背景
      gradeName: "",
      backgroundImage: backgroundImage,
      fontColor: fontColor, //
    },
    propByGradeCardBackgrounds: [
      {
        // 按等级设置背景
        gradeName: "",
        backgroundImage: backgroundImage,
        fontColor: fontColor,
      },
    ],
    propMemberCode: {
      showBarcode: true, // 条形码
      showQrcode: true, // 二维码
    },
    propMemberBenefits: {
      imageType: "default", //会员权益: custom: 自定义
      gradeImages: [
        {
          id: "",
          imageUrl: "",
        },
      ], // 自定义图片列表
    },
    propVisibleLevel: "all", // 'all' 'only'
    propMarketingPosition: {
      enabled: false,
      items: [
        {
          name: "",
          prompt: "",
          jumpPageInfo: null,
        },
      ],
    }, // 推荐位
  };
  rules: any = {
    propTitle: [{ required: true, message: this.i18n("请填写页面名称"), trigger: "blur" }],
    propBackgroundType: [{ required: true, message: this.i18n("请选择"), trigger: "blur" }],
    "propUnifiedCardBackground.fontColor": [{ required: true, message: this.i18n("请填写字体颜色"), trigger: "blur" }],
    "propUnifiedCardBackground.backgroundImage": [{ required: true, message: this.i18n("请选择"), trigger: "blur" }],
    "propByGradeCardBackgroundsBackgroundImage": [{ required: true, message: this.i18n("请选择"), trigger: "blur" }],
    "propByGradeCardBackgroundsFontColor": [{ required: true, message: this.i18n("请填写字体颜色"), trigger: "blur" }],
    "propMemberBenefits.imageType": [{ required: true, message: this.i18n("请选择会员权益"), trigger: "blur" }],
    "propMemberBenefitsGradeImages": [{ required: true, message: this.i18n("请选择会员权益图片"), trigger: "blur" }],
    "propMarketingPositionName": [{ required: true, message: this.i18n("请填写推荐位名称"), trigger: ["blur",'change'] }],
    "propMarketingPositionPrompt": [{ required: true, message: this.i18n("请填写提示语"), trigger: ["blur",'change'] }],
    "propMarketingPositionJumpPageInfo": [{ required: true, message: this.i18n("请选择跳转链接"), trigger: ["blur",'change'] }]
  };
  $refs: any;
  imgFormat: any = ["gif", "jpg", "jpeg", "png"];

  handleValidate() {}

  get trackStyle() {
    return {
      transform: `translateX(${this.dragOffset}px)`,
      transition: this.isDragging ? "none" : "transform 0.3s ease",
    };
  }

  created() {
    if (this.$route.query.id) {
      this.editId = this.$route.query.id as string;
      this.loadData();
    }
  }
  get pageTopImage() {
    return "";
    // return "url(" + bigWheelImageLink[BigWheelImageVO.ic_page_top] + ")";
  }

  get isEnglish() {
    return sessionStorage.getItem("locale") === "en_US";
  }

  get credential() {
    return this.$store.state.credential;
  }

  get panelArray() {
    return [
      {
        name: this.i18n("编辑会员中心页"),
        url: "",
      },
    ];
  }

  getItemStyle(index: number) {
    const offset = (index - this.currentIndex) * this.itemWidth;
    return {
      transform: `translateX(${offset}px)`,
      left: `${(this.containerWidth - this.itemWidth) / 2}px`,
      width: `${this.itemWidth}px`,
    };
  }
  handleMouseDown(e: any) {
    this.isDragging = true;
    this.startX = e.clientX;
  }
  handleMouseMove(e: any) {
    if (!this.isDragging) return;
    this.dragOffset = e.clientX - this.startX;
  }
  handleMouseUp(e: any) {
    if (!this.isDragging) return;
    this.isDragging = false;

    const delta = e.clientX - this.startX;
    if (Math.abs(delta) > 100) {
      delta > 0 ? this.prev() : this.next();
    }
    this.dragOffset = 0;
  }
  next() {
    if (this.currentIndex < this.gradeItems.length - 1) {
      this.currentIndex++;
    }
  }
  prev() {
    if (this.currentIndex > 0) {
      this.currentIndex--;
    }
  }

  // 编辑
  async doUpdate() {
    this.widgets[1].props = this.imgData;
    this.widgets[1].name = this.imgData.propTitle;
    this.widgets[1].type = "custom";
    const loading = CommonUtil.Loading();
    try {
      let content: any = JSON.parse(this.updateParams.content);
      this.updateParams.type = "system";
      content.widgets = this.widgets;
      this.updateParams.name = this.i18n("会员中心");
      this.updateParams.content = content;
      let res = await ContentTemplateApi.update(this.updateParams);
      if (res.code === 2000) {
        this.$message.success(this.i18n("保存成功"));
        this.$router.push({
          name: "page-manage",
          query: { activeName: "sys-manage" },
        });
      } else {
        this.$message.error(res.msg || this.i18n("保存失败"));
      }
    } catch (error) {
      this.$message.error((error as Error).message || this.i18n("保存失败"));
    } finally {
      loading.close();
    }
  }
  // 调用发布接口
  publish() {
    this.publishRequestId.id = this.editId;
    ContentTemplateApi.publish(this.publishRequestId)
      .then((res) => {
        // console.log(res);
        if (res.code === 2000) {
          this.$message.success(this.i18n("发布成功"));
          this.$router.push({
            name: "page-manage",
            query: { activeName: "sys-manage" },
          });
        } else {
          this.$message.error(res.msg || this.i18n("发布失败"));
        }
      })
      .catch((error) => {
        let e = error as any;
        this.$message.error(e.message || this.i18n("发布失败"));
      });
  }
  // 取消返回
  goBack() {
    this.$router.push({
      name: "page-manage",
      query: { activeName: "sys-manage" },
    });
  }
  preserve(isPublish: any) {
    if (this.imgData.propMarketingPosition.enabled && !this.imgData.propMarketingPosition.items.length) {
      this.$message.error(this.i18n("至少添加一条推荐位"));
      return;
    }
    this.$refs.form.validate(async (valid: any) => {
      if (valid) {        
        await this.doUpdate();
        if (isPublish) {
          this.publish();
        }
      } else {
        this.$message.error(this.i18n("请完善装修信息！"));
      }
    });
  }
  // 编辑get参数
  loadData() {
    const loading = this.$loading(ConstantMgr.loadingOption);
    ContentTemplateApi.get(this.editId)
      .then((res) => {
        if (res.data) {
          this.updateParams = res.data as any;
          if (res.data.content) {
            const details = JSON.parse(res.data.content);
            this.widgets = details.widgets;
            // 当传过来有为自定义的就赋值
            this.widgets.forEach((item: any) => {
              if (item.type == "custom") {
                this.imgData = item.props;
                this.showCardNumber = !this.imgData.propCardElement.showCardNumber // 卡号
                this.showPeriod = !this.imgData.propCardElement.showPeriod // 有效期
                this.showGrowth = !this.imgData.propCardElement.showGrowth // 成长值
              }
            });
            // 当他只有一个值，并且为默认值时，得添加一个自定义
            if (this.widgets.length == 1 && this.widgets[0].type == "default") {
              let obj = {};
              this.widgets.forEach((item: any) => {
                obj = item;
              });
              this.widgets.push({ ...obj });
              if (obj.props) {
                this.imgData = obj.props;
              }
            }
            this.getGradeList();
          }
        }
      })
      .catch((err) => {
        this.$message.error(err.message || this.i18n("数据加载失败"));
      })
      .finally(() => {
        loading.close();
      });
  }

  getGradeList() {
    GradeApi.listGrade("ALL_V2")
      .then((resp: any) => {
        if (resp.data) {
          this.gradeItems = resp.data;
          this.gradeItems.forEach((item, index) => {
            if (!this.imgData.propByGradeCardBackgrounds[index]) {
              // 使用 this.$set 确保数组项修改是响应式的
              this.$set(this.imgData.propByGradeCardBackgrounds, index, Object.assign({}, {
                gradeName: item.name,
                backgroundImage: backgroundImage,
                fontColor: fontColor,
              }));
            }
          
            if (!this.imgData.propMemberBenefits.gradeImages[index] || !this.imgData.propMemberBenefits.gradeImages[index].id) {
              // 同样使用 this.$set 更新数组中的元素
              this.$set(this.imgData.propMemberBenefits.gradeImages, index, Object.assign({}, {
                id: item.code,
                imageUrl: "",
              }));
            }
          })
        }
      })
      .catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
  }

  //添加推荐位
  doAddFeaturedFirst() {
    if (this.imgData.propMarketingPosition.items.length >= 10) return this.$message.warning(this.i18n("最多设置{0}个档位").replace(/\{0\}/g, "10"));
    this.imgData.propMarketingPosition.items.push({
      name: "",
      prompt: "",
      jumpPageInfo: null,
    });
  }
  doRemoveProject(index: any) {
    this.imgData.propMarketingPosition.items.splice(index, 1);
  }
  // 会员权益切换
  imageTypeChange(val: any) {
    if (val === 'default') {
      this.imgData.propMemberBenefits.gradeImages.map((item:any) => {
        return Object.assign(item,{
          imageUrl: "",
        })
      })
    }
  }
  // 背景切换
  propBackgroundTypeChange(val: any) {
    if(val === 'unified') {
      this.imgData.propByGradeCardBackgrounds.map((item: any) => {
        return Object.assign(item, {
          // 按等级设置背景
          gradeName: "",
          backgroundImage: backgroundImage,
          fontColor: fontColor,
        })
      })
    } else {
      this.imgData.propUnifiedCardBackground = {
        // 统一背景
        gradeName: "",
        backgroundImage: backgroundImage,
        fontColor: fontColor, // #fff4dc
      }
    }
  }
  // 选择跳转页面回调
  changeLink(val: object, obj: any) {
    Object.assign(obj.jumpPageInfo, val);
  }

  cardElementChange(val:any, type: any) {
    this.imgData.propCardElement[type] = !val
  }
}
