/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2023-12-01 14:40:23
 * @LastEditors: 申鹏渤
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\pageConfig\CrmPageConfig.ts
 * 记得注释
 */
import LuDaoConfig from "model/common/LuDaoConfig"

export default class CrmPageConfig {
  // 
  showEnableWriteOffByPhx: Nullable<boolean> = null
  // 
  luDao: Nullable<LuDaoConfig> = null
  // 是否展示积分报表页面积分统计模块
  showSumPoints: Nullable<boolean> = null;
  // 是否将姓和名拆开展示
  showFirstNameWithLastName: Nullable<boolean> = null
  // 是否展示预付卡购卡人、持卡人、内部卡号
  showCardBuyerOwnerAndInnerCode: Nullable<boolean> = null
  // 是否展示卡模板扩展规则
  showCardExtRule: Nullable<boolean> = null
  // 是否展示会员储值设置
  showGiftBagActivityGiftAmount: Nullable<boolean> = null
  // 是否展示折扣选项
  showPrepayCardChargeGiftActivityDiscount: Nullable<boolean> = null
  // 是否展示会员手机号和邮箱核验信息
  showMobileAndEmailCheckInfo: Nullable<boolean> = null
  // 是否展示会员自定义信息（宠物资料）
  showMemberCustomGroupInfo: Nullable<boolean> = null
}