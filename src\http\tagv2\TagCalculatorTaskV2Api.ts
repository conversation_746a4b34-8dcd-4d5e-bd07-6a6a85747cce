import ApiClient from 'http/ApiClient'
import EventBehaviorAttribute from 'model/autoTag/EventBehaviorAttribute'
import EventBehaviorTypeData from 'model/autoTag/EventBehaviorTypeData'
import TagCalculatorResult from 'model/autoTag/TagCalculatorResult'
import TagCalculatorTaskV2 from 'model/autoTag/TagCalculatorTaskV2'
import TagCalculatorTaskV2BatchResult from 'model/autoTag/TagCalculatorTaskV2BatchResult'
import TagCalculatorTaskV2Filter from 'model/autoTag/TagCalculatorTaskV2Filter'
import TagCalculatorTaskV2SaveOrModifyRequest from 'model/autoTag/TagCalculatorTaskV2SaveOrModifyRequest'
import TagRuleEventDataFilter from 'model/autoTag/TagRuleEventDataFilter'
import IdName from 'model/common/IdName'
import Response from 'model/default/Response'

export default class TagCalculatorTaskV2Api {
  /**
   * 审核标签计算任务
   * 
   */
  static audit(uuid: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v2/precision-marketing/tag-calculator/audit/${uuid}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量手动更新标签计算任务
   * 
   */
  static batchManualUpdate(body: Array<string>): Promise<Response<TagCalculatorTaskV2BatchResult>> {
    return ApiClient.server().post(`/v2/precision-marketing/tag-calculator/batchManualUpdate`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量恢复标签计算任务
   * 
   */
  static batchRecover(body: Array<string>): Promise<Response<TagCalculatorTaskV2BatchResult>> {
    return ApiClient.server().post(`/v2/precision-marketing/tag-calculator/batchRecover`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量删除标签计算任务
   * 
   */
  static batchRemove(body: Array<string>): Promise<Response<TagCalculatorTaskV2BatchResult>> {
    return ApiClient.server().post(`/v2/precision-marketing/tag-calculator/batchRemove`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取标签计算任务详情
   * 
   */
  static get(uuid: string): Promise<Response<TagCalculatorTaskV2>> {
    return ApiClient.server().get(`/v2/precision-marketing/tag-calculator/get/${uuid}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 获取事件选项
  * 
  */
  static getEventOption(): Promise<Response<EventBehaviorTypeData[]>> {
    return ApiClient.server().get(`/v2/precision-marketing/tag-calculator/getEventOption`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 获取事件属性
  * 
  */
  static getEventProperties(eventOption: Nullable<string>, extraAttributes = false): Promise<Response<EventBehaviorAttribute[]>> {
    return ApiClient.server().get(`/v2/precision-marketing/tag-calculator/getEventAttribute`, {
      params: {
        eventOption: eventOption,
        extraAttributes: extraAttributes
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 手动更新标签计算任务
   * 
   */
  static manualUpdate(uuid: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v2/precision-marketing/tag-calculator/manualUpdate/${uuid}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询标签计算任务
   * 
   */
  static query(body: TagCalculatorTaskV2Filter): Promise<Response<TagCalculatorResult>> {
    return ApiClient.server().post(`/v2/precision-marketing/tag-calculator/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 获取事件属性值
  * 
  */
  static queryAttributeValue(body: TagRuleEventDataFilter): Promise<Response<IdName[]>> {
    return ApiClient.server().post(`/v2/precision-marketing/tag-calculator/getEventAttributeValue`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 恢复标签计算任务
   * 
   */
  static recover(uuid: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v2/precision-marketing/tag-calculator/recover/${uuid}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除标签计算任务
   * 
   */
  static remove(uuid: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v2/precision-marketing/tag-calculator/delete/${uuid}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存并审核标签计算任务
   * 
   */
  static saveAndAudit(body: TagCalculatorTaskV2SaveOrModifyRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v2/precision-marketing/tag-calculator/saveAndAudit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改或保存标签计算任务
   * 
   */
  static saveOrUpdate(body: TagCalculatorTaskV2SaveOrModifyRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v2/precision-marketing/tag-calculator/saveOrUpdate`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 停止标签计算任务
   * 
   */
  static stop(uuid: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v2/precision-marketing/tag-calculator/stop/${uuid}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

}
