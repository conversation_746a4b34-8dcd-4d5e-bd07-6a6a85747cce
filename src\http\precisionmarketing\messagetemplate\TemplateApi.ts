import ApiClient from 'http/ApiClient'
import MessageTemplate from 'model/precisionmarketing/messagetemplate/MessageTemplate'
import Response from 'model/common/Response'

export default class TemplateApi {
  /**
   * 查询消息模板
   * 查询消息模板
   * 
   */
  static query(): Promise<Response<MessageTemplate[]>> {
    return ApiClient.server().post(`/v1/message-template/queryTemplate`, {}, {
    }).then((res) => {
      return res.data
    })
  }

}
