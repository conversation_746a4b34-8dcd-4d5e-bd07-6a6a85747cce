/*
 * @Author: 黎钰龙
 * @Date: 2024-04-28 14:11:17
 * @LastEditTime: 2024-04-29 09:58:28
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\grade\equityCenter\BenefitConfigApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import BenefitConfig from 'model/member/BenefitConfig'
import BenefitConfigFilter from 'model/member/BenefitConfigFilter'

export default class BenefitConfigApi {
  /**
   * 编辑
   * 编辑
   * 
   */
  static modify(body: BenefitConfig): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/benefit/config/modify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存
   * 保存
   * 
   */
  static save(body: BenefitConfig): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/benefit/config/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 权益详情
  * 权益详情
  * 
  */
  static get(uuid: string): Promise<Response<BenefitConfig>> {
    return ApiClient.server().get(`/v1/benefit/config/get`, {
      params: {
        id: uuid
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除权益
   * 删除权益
   * 
   */
  static delete(uuid: string, version: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/benefit/config/delete`, {}, {
      params: {
        id: uuid,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询权益列表
   * 查询权益列表
   * 
   */
  static query(body: BenefitConfigFilter): Promise<Response<BenefitConfig[]>> {
    return ApiClient.server().post(`/v1/benefit/config/query`, body, {}).then((res) => {
      return res.data
    })
  }
}
