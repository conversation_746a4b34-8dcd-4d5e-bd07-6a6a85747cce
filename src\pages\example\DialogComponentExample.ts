import {Component, Prop, Vue} from 'vue-property-decorator'

@Component({
  name: 'DialogComponentExample',
  components: {},
})
export default class DialogComponentExample extends Vue {
  @Prop({
    type: Boolean,
    default: false
  })
  dialogShow: boolean

  private doCancel() {
    // do something
    this.$emit('update:dialogShow', false)
  }

  private doModalClose() {
    // do something
    this.$emit('update:dialogShow', false)
  }
}
