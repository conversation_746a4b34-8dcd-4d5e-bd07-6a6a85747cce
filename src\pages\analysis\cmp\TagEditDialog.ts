import {Component, Prop, Vue, Watch} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import CodeInput from 'pages/analysis/cmp/CodeInput.vue'
import TagApi from 'http/tag/TagApi'
import TagOption from 'model/tag/TagOption'

@Component({
  name: 'StoreValueAdjustReasonAdd',
  components: {
    FormItem,
    CodeInput
  }
})
export default class TagEditDialog extends Vue {
  tag = ''
  tagId = ''
  tags: any = []
  flag = false
  isActive = false
  $refs: any
  receiveArr: any = []
  @Prop()
  data: any

  @Prop({
    type: Boolean,
    default: false
  })
  dialogShow: boolean

  @Watch('data', {deep: true})
  onDataChange(value: any) {
    this.tagId = value.tagId
    this.tags = value.tagValues
  }

  created() {
    this.localType()
  }

  onSelectCodeDialog(arr: any) {
    this.receiveArr = arr
  }

  doBeforeClose(done: any) {
    this.$emit('dialogClose')
    done()
  }

  doBlur() {
    if (this.receiveArr.length > 0 || this.tags.length > 0) {
      this.flag = false
    }
  }

  doModalClose(type: string) {
    if (type === 'confirm') {
      let params: TagOption = new TagOption()
      params.tagId = this.tagId
      if (this.receiveArr && this.receiveArr.length > 0) {
        params.tagValues = this.receiveArr
      } else {
        params.tagValues = this.tags
      }
      if (params.tagValues.length <= 0) {
        this.flag = true
        return
      }
      TagApi.saveOrModify(params).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.formatI18n('/资料/门店/修改成功'))
          this.tags = []
          this.$emit('dialogClose')
        }
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    } else {
      this.$emit('dialogClose')
    }
  }

  localType() {
    if (sessionStorage.getItem('locale') === 'zh_CN') {
      this.isActive = true
    } else {
      this.isActive = false
    }
  }
}
