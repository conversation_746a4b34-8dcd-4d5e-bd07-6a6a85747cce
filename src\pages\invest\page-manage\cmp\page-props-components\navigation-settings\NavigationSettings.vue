<template>
  <div>
    <el-form v-if="formMode === FormModeType.form" :label-position="labelPosition" :model="form" :rules="rules" ref="form" label-width="100px">
      <!-- <el-form-item label="添加导航" prop="propNavigationType">
        <el-button @click="handleAddInside" v-if="form.propNavigationType === 'inside'">添加导航名称</el-button>
        <el-button @click="handleAddOutside" v-if="form.propNavigationType === 'outside'">添加导航名称</el-button>
      </el-form-item> -->
      <div>
        <!-- 页面内定位 -->
        <div v-if="form.propNavigationType === 'inside'">
          <draggable v-model="form.insideData" animation="500" @change="handleDragEnd(form.insideData)">
            <div v-for="(navigation, index) in form.insideData" :key="navigation.id" class="navigation-item">
              <div class="navigation-label">{{ i18n('导航') }}{{ index + 1 }}</div>
              <el-form-item :prop="`insideData[${index}]`" label-width="0px" :rules="rules.navigationItem">
                <div>
                  <div class="title">{{ i18n('导航形式') }}：</div>
                  <el-radio @change="handleChange" v-model="navigation.navigationStyle" label="text">{{ i18n('纯文字') }}</el-radio>
                  <!-- <el-radio @change="handleChange" v-model="navigation.navigationStyle" label="image">图片</el-radio> -->
                  <div class="title">
                    <span style="color: red">*</span>
                    {{ i18n('导航名称') }}：
                  </div>
                  <el-input
                    v-if="navigation.navigationStyle === 'text'"
                    v-model="navigation.name"
                    style="width: 100%; margin-right: 10px"
                    :placeholder="i18n('请输入导航名称')"
                    @change="handleChange"
                    :maxlength="navigation.icon ? 5 : 10"
                    show-word-limit
                  ></el-input>
                </div>
                <div>
                  <div v-if="navigation.navigationStyle === 'image'" class="navigation-icon">
                    <div class="text">{{ i18n('建议图片宽度为132像素，高度为40像素，支持jpg/jpeg/png，大小不超过2M') }}</div>
                    <upload-img
                      v-model="navigation.image"
                      :signature-result="credential"
                      @validate="handleValidate"
                      width="80px"
                      height="80px"
                      @change="handleChange"
                      :imgFormat="imgFormat"
                    ></upload-img>
                  </div>
                </div>
              </el-form-item>
              <div class="navigation-icon" v-if="navigation.navigationStyle === 'text'" style="margin-top: 10px">
                <div class="title">{{ i18n('导航图标') }}</div>
                <div class="wz">
                  {{ i18n('若设置导航图标，为保证前端展示效果导航名称最多设置5个字符，建议图片宽度为48像素，高度为48像素，支持jpg/jpeg/png，大小不超过2M，每一张图片高度相同') }}
                </div>
                <upload-img
                  v-model="navigation.icon"
                  :signature-result="credential"
                  @validate="handleValidate"
                  width="80px"
                  height="80px"
                  @change="handleChange"
                  :imgFormat="imgFormat"
                ></upload-img>
              </div>
              <div class="navigation-text">{{ i18n('导航定位设置') }}</div>
              <el-form-item
                label-width="0px"
                :prop="`insideData[${index}].componentUuid`"
                :rules="{
                  required: true,
                  message: i18n('导航名称未定位到组件!'),
                  trigger: ['change', 'blur'],
                }"
              >
                <el-select
                  v-model="navigation.componentUuid"
                  :placeholder="i18n('定位到组件')"
                  style="width: 100%; margin-right: 10px"
                  clearable
                  @change="
                    (val) => {
                      handleSelectChange(val, navigation, index);
                    }
                  "
                >
                  <el-option
                    v-for="item in renderTemplateList.filter((res) => res.id !== 'pageNavigation')"
                    :key="item.uuid"
                    :label="item.name"
                    :value="item.uuid"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label-width="0px" class="delete-item">
                <el-button type="text" :disabled="index == 0" @click="upInsideData(index, form.insideData)">{{ i18n('上移') }}</el-button>
                <el-button type="text" :disabled="index == form.insideData.length - 1" @click="downInsideData(index, form.insideData)">
                  {{ i18n('下移') }}
                </el-button>
                <el-button type="text" :disabled="form.insideData.length <= 1" @click="deleteInsideData(index, form.insideData)">{{ i18n('删除') }}</el-button>
              </el-form-item>
            </div>
          </draggable>
        </div>

        <!-- 页面切换 -->
        <!-- <div v-if="form.propNavigationType === 'outside'">
          <draggable v-model="form.insideData" animation="500" @change="handleDragEnd(form.insideData)">
            <div class="navigation-item" v-for="(navigation, index) in form.outsideData" :key="navigation.id">
              <div class="navigation-label">导航{{ index + 1 }}</div>
              <el-form-item :prop="`outsideData[${index}]`" label-width="0px" :rules="rules.navigationItem">
                <div>
                  <el-radio @change="handleChange" v-model="navigation.navigationStyle" label="text">纯文字</el-radio>
                  <el-radio @change="handleChange" v-model="navigation.navigationStyle" label="image">图片</el-radio>
                  <el-input
                    v-if="navigation.navigationStyle === 'text'"
                    v-model="navigation.name"
                    style="width: 100%; margin-right: 10px"
                    placeholder="请输入导航名称"
                    @change="handleChange"
                    :maxlength="10"
                    show-word-limit
                  ></el-input>
                  <div class="navigation-icon" v-if="navigation.navigationStyle === 'text'">
                    <div class="title">导航图标</div>
                    <div class="wz">
                      若设置导航图标，为保证前端展示效果导航名称最多设置5个字符，建议图片宽度为48像素，高度为48像素，支持jpg/jpeg/png，大小不超过2M，每一张图片高度相同
                    </div>
                    <upload-img
                      v-model="navigation.icon"
                      :signature-result="credential"
                      @validate="handleValidate"
                      width="80px"
                      height="80px"
                      @change="handleChange"
                      :imgFormat="imgFormat"
                    ></upload-img>
                  </div>
                </div>
                <div>
                  <div v-if="navigation.navigationStyle === 'image'">
                    <div class="text">建议图片宽度为132像素，高度为40像素，支持jpg/jpeg/png，大小不超过2M</div>
                    <upload-img
                      v-model="navigation.image"
                      :signature-result="credential"
                      @validate="handleValidate"
                      width="80px"
                      height="80px"
                      @change="handleChange"
                      :imgFormat="imgFormat"
                    ></upload-img>
                  </div>
                </div>
              </el-form-item>
              <div class="navigation-text">导航定位设置</div>
              <el-form-item label-width="0px">
                <JumpPage
                  v-model="navigation.targetPage"
                  :isShowSelectInput="false"
                  :optionsOut="optionsOut"
                  label=""
                  rowWidth="150px"
                  @change="handleChange"
                  :required="true"
                />
              </el-form-item>
              <SetTime
                :readonly="readonly"
                :validateName="validateName"
                :value="value"
                :label="label"
                :prefixDescribe="prefixDescribe"
                :suffixDescribe="prefixDescribe"
                :required="required"
                :relativeValidateName="relativeValidateName"
                :formKey="formKey"
              />
              <CycleConditions
                :readonly="readonly"
                :validateName="validateName"
                :value="value"
                :label="label"
                :prefixDescribe="prefixDescribe"
                :suffixDescribe="prefixDescribe"
                :required="required"
                :relativeValidateName="relativeValidateName"
                :formKey="formKey"
              />
              <el-form-item label-width="0px" class="delete-item">
                <el-button type="text" :disabled="index == 0" @click="upInsideData(index, form.insideData)">上移</el-button>
                <el-button type="text" :disabled="index == form.insideData.length - 1" @click="downInsideData(index, form.insideData)">
                  下移
                </el-button>
                <el-button type="text" :disabled="form.insideData.length <= 2" @click="deleteOutsideData(index, form.insideData)">删除</el-button>
              </el-form-item>
            </div>
          </draggable>
        </div> -->
      </div>
      <!-- 背景填充 -->
      <el-form-item prop="propNavigationType">
        <el-button v-show="value.propNavigationList.length < 10" class="btn" @click="handleAddInside" v-if="form.propNavigationType === 'inside'">
          +{{ i18n('添加导航')}}
        </el-button>
        <!-- <el-button class="btn" @click="handleAddOutside" v-if="form.propNavigationType === 'outside'">+添加导航</el-button> -->
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" src="./NavigationSettings.ts"></script>

<style lang="scss" scoped>
.navigation-item {
  padding: 12px;
  position: relative;
  background: #f0f2f6;
  border-radius: 4px;
  margin-bottom: 12px;
  .delete-item {
    text-align: right;
    margin-top: -40px;
    position: absolute;
    bottom: -25px;
    right: 0px;
  }
  .navigation-text {
    padding: 10px 0;
    color: #666666;
  }
  .navigation-label {
    padding-bottom: 20px;
  }
  .navigation-icon {
    margin-bottom: 1px solid #999999;
    .wz {
      font-weight: 400;
      font-size: 13px;
      color: #a1a6ae;
      line-height: 18px;
    }
    .title {
      font-weight: 400;
      font-size: 14px;
      color: #5a5f66;
      line-height: 20px;
      margin-bottom: 12px;
    }
  }
  .text {
    font-weight: 400;
    font-size: 13px;
    color: #a1a6ae;
    line-height: 18px;
  }
}
::v-deep .el-form-item__content {
  margin-left: 0px !important;
  .btn {
    width: 100%;
    height: 36px;
    background: #ffffff;
    border-radius: 4px;
    font-weight: 400;
    color: #007eff;
    line-height: 17px;
    span {
      font-size: 12px;
    }
  }
}
.navigation-icon-background {
  margin-bottom: 30px;
}
.el-tag--medium {
  border-radius: 4px !important;
}
.el-button + .el-button {
  margin-left: 0px;
}
</style>
