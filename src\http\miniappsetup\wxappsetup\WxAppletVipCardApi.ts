/*
 * @Author: 黎钰龙
 * @Date: 2022-11-15 17:07:29
 * @LastEditTime: 2023-05-16 19:00:19
 * @LastEditors: 苏国友 <EMAIL>
 * @Description: 
 * @FilePath: /phoenix-web-ui/src/http/miniappsetup/wxappsetup/WxAppletVipCardApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import BWeixinAppletSettingResponse from 'model/miniappsetup/aliappsetup/BWeixinAppletSettingResponse'
import Response from 'model/common/Response'
// import BWeixinAppletConfig from 'model/miniappsetup/aliappsetup/BWeixinAppletConfig'
import BWeixinAppletSettingRequest from 'model/miniappsetup/aliappsetup/BWeixinAppletSettingRequest'
export default class WxAppletVipCardApi {

  /**
   * 会员卡片保存
   * 会员卡片保存。
   * 
   */
  static save(body: B<PERSON>eixinAppletSettingRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/weixin-applet/setting/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 会员卡片查询
   * 会员卡片查询。
   * type=HOME 或 MINE
   */
  static query(type: string): Promise<Response<BWeixinAppletSettingResponse>> {
    return ApiClient.server().post(`/v1/weixin-applet/setting/query?type=${type}`, {}, {}).then((res) => {
      return res.data
    })
  }
}
