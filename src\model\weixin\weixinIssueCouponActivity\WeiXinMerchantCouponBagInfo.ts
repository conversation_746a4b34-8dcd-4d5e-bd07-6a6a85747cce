import WeiXinMerchantCoupon from './WeiXinMerchantCoupon'
import { ExpireRefundType } from './ExpireRefundType'
import { RefundAlgorithm } from './RefundAlgorithm'

export default class WeiXinMerchantCouponBagInfo {
  // 是否允许用户申请退款
  enableUserApplyRefund: Nullable<boolean> = false
  // 券包图片
  images: string[] = []
  // 券售价
  price: Nullable<number> = null
  // 使用须知
  notice: Nullable<string> = null
  // 过期退款方式
  expireRefundType: Nullable<ExpireRefundType> = ExpireRefundType.MANUAL_REFUND
  // 退款金额算法
  refundAlgorithm: Nullable<RefundAlgorithm> = RefundAlgorithm.BY_FAV
  // 券信息
  merchantCoupons: WeiXinMerchantCoupon[] = []
  // 券批次数量
  batchCount: Nullable<number> = null
  // 发券总数量
  totalCount: Nullable<number> = null
  // 是否要人工审核
  enableManualAudit: Nullable<boolean> = true
}