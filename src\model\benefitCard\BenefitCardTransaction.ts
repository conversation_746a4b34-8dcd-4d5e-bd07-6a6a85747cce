

// 付费会员卡/权益卡流水
import IdName from "model/common/IdName";
import Channel from "model/common/Channel";

export default class BenefitCardTransaction {
  // 
  marketingCenter: Nullable<string> = null
  // 卡模版类型，paid-付费卡 free-免费卡
  templateType: Nullable<string> = null
  //  购卡时间
  occurTime: Nullable<Date> = null
  //  购卡组织
  occurOrg: Nullable<IdName> = null
  // 购卡区域
  occurZone: Nullable<IdName> = null
  // 卡模版ID
  templateId: Nullable<string> = null
  // 卡模版代码
  templateCode: Nullable<string> = null
  // 会员ID
  memberId: Nullable<string> = null
  // 卡号
  cardNo: Nullable<string> = null
  // 卡名称
  cardName: Nullable<string> = null
  //  有效期类型，days-天 month-月 year-年
  expireType: Nullable<string> = null
  // 有效期天数
  expireDays: Nullable<number> = null
  // 有效期结束时间
  expireStopTime: Nullable<Date> = null
  // 支付类型，paid-付款 recharge-储值充值 point-积分支付 free-免费
  payType: Nullable<string> = null
  //  支付类型扩展，比如类型为付款时，记录为weixin 、aliApplet
  payTypeEx: Nullable<string> = null
  // 支付金额
  payAmount: Nullable<number> = null
  // 支付交易号
  payTranNo: Nullable<string> = null
  // 活动ID
  activity: Nullable<IdName> = null
  // 说明
  remark: Nullable<string> = null
  // 渠道
  channel: Nullable<Channel> = null
  // 交易号
  transNo: Nullable<string> = null
  // crm会员号
  crmCode: Nullable<string> = null
  // 手机号
  mobile: Nullable<string> = null
  // 动作
  action: Nullable<string> = null
}