<template>
    <el-dialog :before-close="doBeforeClose" :close-on-click-modal="false"
               :title="title"
               :visible.sync="dialogShow" append-to-body class="reset-password-dialog">
        <div class="wrap">
            <el-form :model="ruleForm" :rules="rules" class="demo-ruleForm" label-width="100px" ref="ruleForm">
                <el-form-item :label="formatI18n('/会员/会员资料', '新支付密码')" prop="password">
                    <el-input :placeholder="formatI18n('/会员/会员资料', '请输入新支付密码，6位数字')"
                              @change="doPwdChange"
                              maxlength="6"
                              type="password"
                              v-model="ruleForm.password">
                    </el-input>
                </el-form-item>
                <el-form-item :label="formatI18n('/会员/会员资料', '确认新支付密码')" prop="confirmPassword">
                    <el-input :placeholder="formatI18n('/会员/会员资料', '请再次输入新支付密码，6位数字')"
                              @change="doConfirmPwdChange"
                              maxlength="6"
                              type="password"
                              v-model="ruleForm.confirmPassword"></el-input>
                </el-form-item>
            </el-form>
        </div>
        <div class="dialog-footer" slot="footer">
            <el-button @click="doCancel">{{formatI18n('/公用/按钮', '取消')}}</el-button>
            <el-button @click="doModalClose" size="small" type="primary">{{formatI18n('/公用/按钮', '确定')}}</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts" src="./ResetPasswordDialog.ts">
</script>

<style lang="scss">
.reset-password-dialog{
    display: flex;
    align-items: center;
    justify-content: center;
    .wrap{
        .el-form-item__label{
            width: 140px !important;
        }
        .el-form-item__content{
            margin-left: 140px !important;
        }
    }
    .el-dialog{
        width: 500px;
    }
}
</style>