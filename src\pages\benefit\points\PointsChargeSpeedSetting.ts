import {Component, Vue} from 'vue-property-decorator'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import MemberDayPointsChargeActivityApi from 'http/points/init/memberdaypointscharge/MemberDayPointsChargeActivityApi'
import MemberDayPointsChargeRule from 'model/points/init/memberdaypointscharge/MemberDayPointsChargeRule'
import PointsChargeMetaActivityApi from 'http/points/init/pointschargemeta/PointsChargeMetaActivityApi'
import AmountToFixUtil from 'util/AmountToFixUtil'
import ConstantMgr from 'mgr/ConstantMgr'

@Component({
  name: 'PointsChargeSpeedSetting',
  components: {
    BreadCrume
  }
})
export default class PointsChargeSpeedSetting extends Vue {
  $refs: any
  panelArray: any = []
  weeks: string[] = []
  score = ''
  amount = ''
  ruleForm = {
    option: 'first',
    day: '',
    dayUse: '',
    dayAmount: '',
    month: '',
    monthUse: '',
    monthAmount: '',
    // remark: ''
  }
  rules: any = {}
  created() {
    this.weeks = [
      this.formatI18n('/公用/券模板', '周一'),
      this.formatI18n('/公用/券模板', '周二'),
      this.formatI18n('/公用/券模板', '周三'),
      this.formatI18n('/公用/券模板', '周四'),
      this.formatI18n('/公用/券模板', '周五'),
      this.formatI18n('/公用/券模板', '周六'),
      this.formatI18n('/公用/券模板', '周日')
    ]
    this.panelArray = [
			{
				name: this.formatI18n("/公用/菜单", "节日有礼"),
				url: "score-init",
			},
			{
				name: this.formatI18n("/权益/积分初始化/会员日积分抵现加速/新建界面/会员日积分抵现加速设置"),
				url: "",
			},
		];
    this.rules = {
      day: [
        { required: true, validator: (rule: any, value: any, callback: any) => {
            if (this.ruleForm.option === 'first') {
              if (value) {
                callback()
              } else {
                callback(this.formatI18n('/公用/券模板', '请输入必填项'))
              }
            } else {
              callback()
            }
          }, trigger: 'blur' }
      ],
      dayUse: [
        { required: true, validator: (rule: any, value: any, callback: any) => {
            if (this.ruleForm.option === 'first') {
              if (value) {
                callback()
              } else {
                callback(this.formatI18n('/公用/券模板', '请输入必填项'))
              }
            } else {
              callback()
            }
          }, trigger: 'blur' }
      ],
      dayAmount: [
        { required: true, validator: (rule: any, value: any, callback: any) => {
            if (this.ruleForm.option === 'first') {
              if (value) {
                callback()
              } else {
                callback(this.formatI18n('/公用/券模板', '请输入必填项'))
              }
            } else {
              callback()
            }
          }, trigger: 'blur' }
      ],
      month: [
        { required: true, validator: (rule: any, value: any, callback: any) => {
            if (this.ruleForm.option === 'second') {
              if (value) {
                callback()
              } else {
                callback(this.formatI18n('/公用/券模板', '请输入必填项'))
              }
            } else {
              callback()
            }
          }, trigger: 'blur' }
      ],
      monthUse: [
        { required: true, validator: (rule: any, value: any, callback: any) => {
            if (this.ruleForm.option === 'second') {
              if (value) {
                callback()
              } else {
                callback(this.formatI18n('/公用/券模板', '请输入必填项'))
              }
            } else {
              callback()
            }
          }, trigger: 'blur' }
      ],
      monthAmount: [
        { required: true, validator: (rule: any, value: any, callback: any) => {
            if (this.ruleForm.option === 'second') {
              if (value) {
                callback()
              } else {
                callback(this.formatI18n('/公用/券模板', '请输入必填项'))
              }
            } else {
              callback()
            }
          }, trigger: 'blur' }
      ],
      // remark: [
      //   { required: true, message: this.formatI18n('/公用/券模板', '请输入必填项'), trigger: 'blur' }
      // ]
    }
    this.getDtl()
    if (this.$route.query.from === 'edit') {
      this.getSpeedDtl()
    }
  }
  doOptionChange() {
    if (this.ruleForm.option === 'first') {
      this.ruleForm.month = ''
      this.ruleForm.monthUse = ''
      this.ruleForm.monthAmount = ''
    } else {
      this.ruleForm.day = ''
      this.ruleForm.dayUse = ''
      this.ruleForm.dayAmount = ''
    }
    this.$refs.ruleForm.validate()
  }
  doDayChange() {
    this.$refs.ruleForm.validateField('day')
  }
  doMonthChange() {
    this.$refs.ruleForm.validateField('month')
  }
  doSave() {
    this.$refs.ruleForm.validate((resp: any) => {
      if (resp) {
        this.$confirm(this.formatI18n('/权益/积分/积分初始化/积分抵现/立即设置/保存后，新的会员日积分抵现加速规则将立即生效。请确认是否保存？'), this.formatI18n('/公用/弹出模态框提示标题', '提示'), {
          confirmButtonText: this.formatI18n('/公用/按钮', '确定'),
          cancelButtonText: this.formatI18n('/公用/按钮', '取消')
        }).then(() => {
          const loading = this.$loading(ConstantMgr.loadingOption)
          let params: MemberDayPointsChargeRule = new MemberDayPointsChargeRule()
          if (this.ruleForm.option === 'first') {
            params.dayType = 'BY_MONTH'
            params.day = Number(this.ruleForm.day)
            params.points = Number(this.ruleForm.dayUse)
            params.amount = Number(this.ruleForm.dayAmount)
          } else {
            params.dayType = 'BY_WEEK'
            params.day = Number(this.ruleForm.month)
            params.points = Number(this.ruleForm.monthUse)
            params.amount = Number(this.ruleForm.monthAmount)
          }
          MemberDayPointsChargeActivityApi.saveOrModify(params).then((resp: any) => {
            if (resp && resp.code === 2000) {
              loading.close()
              this.$message.success(this.formatI18n('/设置/权限/用户管理/功基本信息/保存', '保存成功'))
              this.$router.push({name: 'points-charge-speed-setting-dtl'})
            }
          }).catch((error: any) => {
            loading.close()
            this.$message.error(error.message)
          })
        })
      }
    })
  }
  doCancel() {
    this.$router.back()
  }
  doDayUseChange() {
    this.ruleForm.dayUse = AmountToFixUtil.formatNumber(this.ruleForm.dayUse, 99999999, 1)
  }
  doDayAmountChange() {
    this.ruleForm.dayAmount = AmountToFixUtil.formatAmount(this.ruleForm.dayAmount, 99999999, 0.01, '')
  }
  doMonthUseChange() {
    this.ruleForm.monthUse = AmountToFixUtil.formatNumber(this.ruleForm.monthUse, 99999999, 1)
  }
  doMonthAmountChange() {
    this.ruleForm.monthAmount = AmountToFixUtil.formatAmount(this.ruleForm.monthAmount, 99999999, 0.01, '')
  }
  getDtl() {
    PointsChargeMetaActivityApi.detail().then((resp: any) => {
      if (resp && resp.code === 2000) {
        if (resp.data) {
          this.score = resp.data.points
          this.amount = resp.data.amount
        }
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  getTip(score: string, amount: string) {
    let str = this.formatI18n('/权益/积分/积分初始化/未初始化状态/积分抵现规则/会员日积分加速抵现/点击立即设置/当前基础积分抵现权益：每使用{0}积分，抵现{1}元，不满{2}积分不能抵现')
    str = str.replace(/\{0\}/g, score)
    str = str.replace(/\{1\}/g, Number(amount).toFixed(2))
    str = str.replace(/\{2\}/g, score)
    return str
  }
  getSpeedDtl() {
    MemberDayPointsChargeActivityApi.detail().then((resp: any) => {
      if (resp && resp.code === 2000) {
        if (resp.data) {
          if (resp.data.dayType === 'BY_MONTH') {
            this.ruleForm.option = 'first'
            this.ruleForm.day = resp.data.day
            this.ruleForm.dayUse = resp.data.points
            this.ruleForm.dayAmount = resp.data.amount
          } else {
            this.ruleForm.option = 'second'
            this.ruleForm.month = resp.data.day
            this.ruleForm.monthUse = resp.data.points
            this.ruleForm.monthAmount = resp.data.amount
          }
        }
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
}