/*
 * @Author: 黎钰龙
 * @Date: 2024-01-04 14:53:41
 * @LastEditTime: 2024-01-04 14:53:51
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\v2\coupon\aliGroupBuyCoupon\PltGroupCouponActivity.ts
 * 记得注释
 */
import BaseCouponActivity from 'model/coupon/activity/BaseCouponActivity'

// 支付宝平台团购券活动
export default class PltGroupCouponActivity extends BaseCouponActivity {
  // 投放方式：SELF_PICK_CARD——自提卡，LOCAL_LIFESTYLE_GOODS——本地生活商品
  pickType: Nullable<string> = null
  // 字体卡Id/商家侧编码
  code: string = ''
  // 价格
  price: Nullable<number> = null
}