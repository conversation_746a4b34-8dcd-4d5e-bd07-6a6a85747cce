import ApiClient from 'http/ApiClient'
import PayAsMemberRule from 'model/weixin/payrule/PayAsMemberRule'
import Response from 'model/common/Response'

export default class WeixinPayRuleApi {
  /**
   * 取得微信支付即会员规则
   *
   */
  static get(): Promise<Response<PayAsMemberRule>> {
    return ApiClient.server().get(`/v1/weixin-payrule/get`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存微信支付即会员规则
   *
   */
  static save(body: PayAsMemberRule): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/weixin-payrule/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 禁用微信支付即会员规则
   *
   */
  static stop(): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/weixin-payrule/stop`, {}, {
    }).then((res) => {
      return res.data
    })
  }

}
