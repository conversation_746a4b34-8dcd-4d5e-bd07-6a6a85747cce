/*
 * @Autor: 司浩
 * @Description:
 * @Date: 2023-03-02 17:24:23
 * @LastEditors: 司浩
 * @LastEditTime: 2023-03-07 15:54:41
 * @FilePath: \phoenix-web-ui\src\pages\benefit\equity-card\EquityCardPermission.ts
 */
import { Vue } from 'vue-property-decorator'
import PermissionMgr from 'mgr/PermissionMgr'

export default class EquityCardPermission extends Vue {
  // 维护
  get editable() {
    return PermissionMgr.hasOptionPermission(
      '/会员/权益卡管理/权益卡设置',
      '权益卡维护'
    )
  }

  // 停用启用
  get disableEnable() {
    return PermissionMgr.hasOptionPermission(
      '/会员/权益卡管理/权益卡设置',
      '权益卡停用启用'
    )
  }

  // 数据查看
  get viewable() {
    return PermissionMgr.hasOptionPermission(
      '/会员/权益卡管理/权益卡查询',
      '数据查看'
    )
  }

  // 批量导出
  get exportable() {
    return PermissionMgr.hasOptionPermission(
      '/会员/权益卡管理/权益卡查询',
      '批量导出'
    )
  }

  // 作废
  get voidable() {
    return PermissionMgr.hasOptionPermission(
      '/会员/权益卡管理/权益卡查询',
      '权益卡作废'
    )
  }
}
