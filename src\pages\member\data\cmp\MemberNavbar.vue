<template>
  <div class="member-card member-navbar">
    <div class="navbar-item"
         v-for="(item,index) in navbar"
         :key="item"
         :class="{active:index==currentIndex}" @click="onChange(index)">{{ item }}
    </div>
  </div>
</template>
<script lang="ts"
        src="./MemberNavbar.ts">
</script>
<style lang="scss"
       scoped>
.member-navbar {
  padding: 0 12px !important;
  display: flex;
  margin-bottom: 16px;

  .navbar-item {
    padding: 16px 20px;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    color: #79879E;
    cursor: pointer;

    &.active {
      color: #007EFF;
    }
  }
}
</style>
