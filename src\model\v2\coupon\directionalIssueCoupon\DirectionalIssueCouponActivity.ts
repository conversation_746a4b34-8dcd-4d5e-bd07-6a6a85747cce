import BaseCouponActivity from 'model/v2/coupon/BaseCouponActivity'
import DateTimeRange from 'model/common/DateTimeRange'
import GiftInfo from 'model/common/GiftInfo'
import PushGroup from 'model/precisionmarketing/pushplan/PushGroup'

export default class DirectionalIssueCouponActivity extends BaseCouponActivity {
  // 发券时段
  timeRange: Nullable<DateTimeRange> = null
  // 适用客群
  // group: Nullable<PushGroup> = null
  groups: PushGroup[] = []
  // 礼包
  giftInfo: Nullable<GiftInfo> = null
}