/*
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:04
 * @LastEditTime: 2024-05-09 18:08:21
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\prepaycard\payment\CardBalancePromotionDtl.ts
 * 记得注释
 */
import { Component, Vue } from 'vue-property-decorator'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import GoodsScopeDtl from "cmp/goodsscope/GoodsScopeDtl";
import RSGrade from "model/common/RSGrade";
import CardBalancePromotionApi from "http/payment/card/CardBalancePromotionApi";
import CardBalancePromotionActivity from "model/payment/card/CardBalancePromotionActivity";
import ActivityState from "cmp/activitystate/ActivityState";
import EditType from "common/EditType";
import ActiveStoreDtl from "cmp/activestoredtl/ActiveStoreDtl";
import I18nPage from "common/I18nDecorator";
import CardBalancePromotionPermission from "./CardBalancePromotionPermission";
import RoutePermissionMgr from "mgr/RoutePermissionMgr";
import DateTimeConditionDtl from "cmp/date-time-condition-picker/DateTimeConditionDtl";
import BrowserMgr from "mgr/BrowserMgr";

@Component({
  name: 'CardBalancePromotionDtl',
  components: {
    BreadCrume,
    GoodsScopeDtl,
    ActiveStoreDtl,
    ActivityState,
    DateTimeConditionDtl
  }
})
@I18nPage({
  prefix: [
    '/储值/预付卡/预付卡支付活动/详情页面',
    "/储值/预付卡/预付卡支付活动/编辑页面",
    "/储值/预付卡/预付卡支付优惠",
    '/公用/活动/状态',
    '/公用/活动/活动信息',
    '/公用/活动/提示信息',
    '/公用/按钮',
    '/公用/菜单'
  ],
})
export default class CardBalancePromotionDtl extends Vue {
  goodsMatchRuleMode: string = "barcode"
  i18n: (str: string, params?: string[]) => string
  activityId: string = ''
  data: CardBalancePromotionActivity = new CardBalancePromotionActivity()
  gradeDifferentStepValueMap: any = {} // grade -> gradeDifferentStepReduction
  gradeList: RSGrade[] = []
  permission = new CardBalancePromotionPermission()

  $refs: any
  panelArray: any

  get sameGradeRuleStr() {
    let str = ''
    if (this.data.newStrategy === 'BY_AMOUNT') {
      str = '/储值/预付卡/预付卡支付活动/编辑页面/适用商品消费每满{0}元，立减{1}元，不足部分不优惠'
    } else if (this.data.newStrategy === 'BY_QTY') {
      str = '/储值/预付卡/预付卡支付活动/编辑页面/适用商品中每单品消费每满{0}件，立减{1}元，不足部分不优惠'
    } else if (this.data.newStrategy === 'BY_FULL_AMOUNT') {
      str = '/储值/预付卡/预付卡支付活动/编辑页面/适用商品消费满{0}元，立减{1}元，不足部分不优惠'
    } else if (this.data.newStrategy === 'BY_FULL_QTY') {
      str = '/储值/预付卡/预付卡支付活动/编辑页面/适用商品消费满{0}件，立减{1}元，不足部分不优惠'
    }
    return str
  }


  get activityLimitStr() {
    let str = ''
    str = this.data.maxCardJoinTimes ? this.i18n('单卡限参与') + this.data.maxCardJoinTimes + this.i18n('次') : this.i18n('单卡参与次数不限制')
    str += '；'
    str += this.data.maxDailyCardJoinTime ? this.i18n('单卡单日限参与') + this.data.maxDailyCardJoinTime + this.i18n('次') : this.i18n('单卡单日参与次数不限制')
    str += '；'
    str += this.data.maxActivityTimes ? this.i18n('活动总限制') + this.data.maxActivityTimes + this.i18n('次') : this.i18n('活动总次数不限制')
    return str
  }

  created() {
    let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
		if (sysConfig && this.goodsMatchRuleMode) {
			this.goodsMatchRuleMode = sysConfig.goodsMatchRuleMode;
		}
    this.panelArray = [
      {
        name: this.i18n("预付卡支付优惠"),
        url: "card-balance-promotion-list",
      },
      {
        name: this.i18n("预付卡支付立减活动详情"),
        url: "",
      },
    ];
    this.activityId = this.$route.query.activityId as string
    this.getDetail()
  }

  private getGradeList() {
    CardBalancePromotionApi.gradeList().then((res: any) => {
      if (res.code === 2000) {
        this.gradeList = res.data
        this.gradeList.sort((a: any, b: any) => {
          if (a.type !== b.type) {
            let typeMap: any = {
              FREE: 1,
              PAID: 2,
              SPECIAL: 3
            }
            return typeMap[a.type] - typeMap[b.type]
          } else {
            return a.no - b.no
          }
        })
      }
    })
  }

  private getDetail() {
    CardBalancePromotionApi.info(this.activityId).then((res: any) => {
      if (res.code === 2000) {
        Object.assign(this.data, res.data)
        if (this.data.gradeDifferentStepValue) {
          for (let item of this.data.gradeDifferentStepValue) {
            this.gradeDifferentStepValueMap[item.grade as string] = item
          }
          this.getGradeList()
        }
      }
    })
  }

  private audit() {
    this.$alert(this.i18n('确认要审核吗？'), this.i18n('提示'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消'),
      type: 'warning',
      callback: (action) => {
        if (action === 'confirm') {
          CardBalancePromotionApi.audit(this.activityId).then((resp: any) => {
            if (resp && resp.code === 2000) {
              this.$message.success(this.i18n('审核成功'))
              this.getDetail()
            } else {
              throw new Error(resp.msg)
            }
          }).catch((error) => {
            if (error && error.message) {
              this.$message.error(error.message)
            }
          })
        }
      }
    });
  }

  private copy() {
    if (this.data.body && this.data.body.type === 'CARD_BALANCE_REDUCTION') {
      this.$router.push({
        name: 'card-balance-promotion-edit',
        query: { activityId: this.activityId, editType: EditType.COPY }
      })
    }
  }

  private edit() {
    if (this.data.body && this.data.body.type === 'CARD_BALANCE_REDUCTION') {
      this.$router.push({
        name: 'card-balance-promotion-edit',
        query: { activityId: this.activityId, editType: EditType.EDIT }
      })
    }
  }

  private stop() {
    this.$alert(this.i18n('确认要停止吗？'), this.i18n('提示'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消'),
      type: 'warning',
      callback: (action) => {
        if (action === 'confirm') {
          CardBalancePromotionApi.stop(this.activityId).then((resp: any) => {
            if (resp && resp.code === 2000) {
              this.$message.success(this.i18n('停止成功'))
              this.getDetail()
            }
          }).catch((error) => {
            if (error && error.message) {
              this.$message.error(error.message)
            }
          })
        }
      }
    });
  }

  private del() {
    this.$alert(this.i18n('确认要删除吗？'), this.i18n('提示'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消'),
      type: 'warning',
      callback: (action) => {
        if (action === 'confirm') {
          CardBalancePromotionApi.remove(this.activityId).then((resp: any) => {
            if (resp && resp.code === 2000) {
              this.$message.success(this.i18n('删除成功'))
              this.$router.push({ name: 'card-balance-promotion-list' })
            }
          }).catch((error) => {
            if (error && error.message) {
              this.$message.error(error.message)
            }
          })
        }
      }
    });
  }

  private gotoTplDtl(num: string) {
    RoutePermissionMgr.openBlank({ name: 'prepay-card-tpl-dtl', query: { number: num } })
  }
}
