/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2023-03-09 15:55:40
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\prepay\report\card\GiftCardConsumeOrRefundHst.ts
 * 记得注释
 */
import GiftReportBaseData from 'model/prepay/report/card/GiftReportBaseData'

export default class GiftCardConsumeOrRefundHst extends GiftReportBaseData {
  // 原交易号
  sourceTransNo: Nullable<string> = null
  // 余额变动
  totalAmount: Nullable<number> = null
  // 实充变动
  amount: Nullable<number> = null
  // 返现变动
  giftAmount: Nullable<number> = null
  //消费、退款余额
  balance: Nullable<number> = null
}