import {Component, Prop, Vue, Watch} from 'vue-property-decorator'

class Value {
  value1: string = ''
  value2: string = ''
}

@Component({
  name: 'ComponentExample',
  components: {},
  // model: { // v-model默认绑定属性名为value，提交事件为input,可通过model自定义
  //   prop: 'myProp',
  //   event: 'change',
  // }
})
export default class ComponentExample extends Vue {
  @Prop()
  value: Value
  valueCopy: Value = new Value()

  @Watch('value', {immediate: true, deep: true})
  // @Watch('value', {immediate: true, deep: true}) // immediate立即监听，deep深度监听
  watchValue(value: Value) {
    this.valueCopy = JSON.parse(JSON.stringify(value))
  }

  private submit() {
    this.$emit('input', this.valueCopy)
  }
}