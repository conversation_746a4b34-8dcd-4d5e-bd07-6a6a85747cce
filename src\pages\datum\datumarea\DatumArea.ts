import { Component, Vue, Watch } from "vue-property-decorator";
import BreadCrume from "cmp/bread-crumb/BreadCrume";
import AddAreaDialog from "./components/AddAreaDialog.vue";
import IdName from "model/common/IdName";
import ZoneApi from "http/area/ZoneApi";
import BZoneTree from "model/datum/zone/BZoneTree";
import Zone from "model/datum/zone/Zone";
import SaveBatchZoneRequest from "model/datum/zone/SaveBatchZoneRequest";
import I18nPage from "common/I18nDecorator";
import { ElForm } from "fant3-hd/types/form";
import BrowserMgr from "mgr/BrowserMgr";

@Component({
  name: "DatumArea",
  components: {
    BreadCrume,
    AddAreaDialog,
  },
})
@I18nPage({
  auto: false,
  prefix: ["/公用/按钮", '/资料/区域', '/公用/菜单'],
})
export default class DatumArea extends Vue {
  $refs: any;
  panelArray: any = [
    {
      name: this.formatI18n("/公用/菜单/区域"),
      url: "",
    },
  ];
  filterText: string = "";
  @Watch("filterText")
  watchValue(value: string) {
    this.$refs.tree.filter(value);
  }

  filterNode(value: string, data: any) {
    if (!value) return true;
    const zone = data?.zone?.zone;
    return zone?.id?.indexOf(value) !== -1 || zone?.name?.indexOf(value)!==-1;
  }

  treeLoading: boolean = false;

  expandAll: boolean = false;
  @Watch("expandAll")
  watchExpandAll(value: string) {
    const currentNode = this.$refs.tree?.getCurrentNode();
    this.$nextTick(() => {
      if (currentNode) {
        this.$refs.tree?.setCurrentKey(this.data[0]?.zone?.zone?.id);
        this.currentChange(this.data[0]);
      }
    });
  }
  form = {
    areaCode: "",
    areaName: "",
  };
  data: BZoneTree[] = [];
  count: number = 0;

  marketingCenters: IdName = new IdName();
  marketCenter: any = [];
  marketCenterName: any = [];
  enableMultiMarketingCenter: boolean = true; // 是否开启多营销中心
  isMoreMarketing: boolean = false; // 是否开启多营销中心 true 开启 false 不开启
  // 当前选中的树节点对象
  currentSelectBZoneTreeNode: BZoneTree = new BZoneTree();
  saveBtnLoading: boolean = false;

  // 表单校验规则
  rules = {
    areaCode: [{ required: true, message: this.i18n("请输入区域代码"), trigger: "blur" }],
    areaName: [{ required: true, message: this.i18n("请输入区域名称"), trigger: "blur" }],
  };

  onUpdate() {
    const $form = this.$refs.form as InstanceType<typeof ElForm>;

    $form?.validate((flag) => {
      if (!flag) {
        return;
      }
      let zone = this.currentSelectBZoneTreeNode.zone;
      zone.marketingCenter = sessionStorage.getItem("marketCenter") || "";
      zone.zone = { id: this.form.areaCode, name: this.form.areaName };

      const saveBatchZoneRequest: SaveBatchZoneRequest = {
        zones: [zone],
        operator: undefined,
      };
      this.saveBtnLoading = true;
      ZoneApi.saveBatch(saveBatchZoneRequest)
        .then((res) => {
          if (res.code === 2000) {
            this.$message.success(this.formatI18n("/资料/门店/修改成功"));
            this.queryTreeData();
          }
        })
        .finally(() => {
          this.saveBtnLoading = false;
        });
    });
  }

  onAddArea(data: BZoneTree, type: string) {
    this.$refs.AddAreaDialog?.open(data, type);
  }

  created() {
    let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
    if (sysConfig) {
      this.isMoreMarketing = sysConfig.enableMultiMarketingCenter;
    }
    const id = sessionStorage.getItem("marketCenter") || null;
    const name = sessionStorage.getItem("marketCenterName") || null;
    this.marketingCenters = { id: id, name: name };
    if (sessionStorage.getItem("isMultipleMC") == "1") {
      this.enableMultiMarketingCenter = true;
    } else {
      this.enableMultiMarketingCenter = false;
    }
    this.marketCenter = sessionStorage.getItem("marketCenter");
    this.marketCenterName = sessionStorage.getItem("marketCenterName");

    this.queryTreeData();
  }

  setTreeDataId(data: BZoneTree[]) {
    data?.forEach((item) => {
      // @ts-ignore
      item.zoneId = item.zone.zone?.id;
      if (item.child?.length) {
        this.setTreeDataId(item.child);
      }
    });
  }

  queryTreeData() {
    this.treeLoading = true;
    const currentNode = this.$refs.tree?.getCurrentNode();
    ZoneApi.getTree()
      .then((response) => {
        if (response?.data?.trees) {
          this.data = response.data.trees;
          this.setTreeDataId(this.data);
          this.count = response.data.count;
          if (currentNode) {
            this.$nextTick(() => {
              this.$refs.tree?.setCurrentKey(currentNode?.zone?.zone?.id);
            });
          } else {
            if (this.data.length) {
              this.$nextTick(() => {
                this.$refs.tree?.setCurrentKey(this.data[0]?.zone?.zone?.id);
                this.currentChange(this.data[0]);
              });
            }
          }
        } else {
          this.data = [];
          this.count = 0;
        }
      })
      .finally(() => {
        this.treeLoading = false;
      });
  }

  currentChange(data: BZoneTree) {
    this.currentSelectBZoneTreeNode = data;
    const zone = data.zone.zone;
    this.form.areaCode = zone.id!;
    this.form.areaName = zone.name!;
  }

  /**
   * 作废
   */
  openCancellationDialog(data: BZoneTree) {
    this.$confirm(
      this.formatI18n("/资料/区域/作废以后，该区域下的门店将没有归属区域，此操作不可恢复，请谨慎操作"),
      this.formatI18n("/资料/区域/作废"),
      {
        confirmButtonText: this.i18n('确定'),
        cancelButtonText: this.i18n('取消'),
        // callback: (action) => {
        //     debugger;
        //     this.cancellationDialog(data)
        // },
      },
    )
      .then(() => {
        this.cancellationDialog(data);
      })
      .catch(() => {
        // 取消
      });
  }

  cancellationDialog(value: BZoneTree) {
    const zone = JSON.parse(JSON.stringify(value.zone)) as Zone;
    zone.state = 'CANCEL'
    const params: SaveBatchZoneRequest = new SaveBatchZoneRequest();
    params.zones.push(zone);
    ZoneApi.saveBatch(params).then((res) => {
      if (res.code === 2000) {
        this.$message({
          type: "success",
          message: this.formatI18n("/储值/预付卡/预付卡查询/列表页面/作废成功"),
        });
        this.queryTreeData();
      }else{
        this.$message.error(res.msg ? res.msg : this.i18n('/公用/券模板/操作失败'));
      }
    }).catch((error)=>{
      // 
      if (error && error.message) {
        this.$message.error(error.message);
    }
    })
  }
}
