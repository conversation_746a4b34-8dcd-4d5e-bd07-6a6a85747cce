/*
 * @Author: 黎钰龙
 * @Date: 2024-12-19 17:45:36
 * @LastEditTime: 2025-02-19 16:21:15
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\autoTag\StatsTagRule.ts
 * 记得注释
 */
import { EventBehaviorCalculateType } from "./EventBehaviorCalculateType"
import EventBehaviorRule from "./EventBehaviorRule"
import { NumberOperatorType } from "./NumberOperatorType"
import PartitionRule from "./PartitionRule"
import { PartitionType } from "./PartitionType"
import { PropType } from "./PropType"

export default class StatsTagRule {
  // 标签名称
  tagName: Nullable<string> = null
  // 事件行为规则
  eventBehaviorRule: Nullable<EventBehaviorRule> = null
  // 事件行为指标类型：字符串，数字，日期，布尔
  metricsType: Nullable<PropType> = null
  // 事件行为指标属性
  metricsProp: Nullable<string> = null
  // 事件行为指标计算方式
  metricsCalculation: Nullable<EventBehaviorCalculateType> = null
  // 事件行为指标属性值
  // metricsValue: number[] = []
  // 运算符
  // metricsOperator: Nullable<NumberOperatorType> = null
  // 分区类型：none:不分区；byRatio：按百分比；byRange：按数值区间
  partitionType: Nullable<PartitionType> = PartitionType.none
  // 分区规则
  partitionRules: PartitionRule[] = []
}