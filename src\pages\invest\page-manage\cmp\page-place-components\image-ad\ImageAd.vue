<template>
  <div
    class="template-image-ad"
    :style="{ padding: localProperty.styMarginTop + 'px ' + localProperty.styMarginRight + 'px ' + localProperty.styMarginBottom + 'px ' + localProperty.styMarginLeft + 'px' }"
    :class="[{ activeCom: activeIndex === index,  }]"
    @click="activeTemplate"
  >
    <div class="toolBar" v-if="activeIndex === index">
      <setting-toolbar :btns="toolBarBtns" @click="toolbarClick"></setting-toolbar>
    </div>
    <img
      :src="localProperty.propImageUrl"
      v-if="localProperty.propImageUrl"
      :ref="`backgroundImg${activeIndex}`"
      class="image-ad-background"
    />
    <div v-else class="defaul-image">
      <!-- <img :src="defaultUrl" :ref="`backgroundImg${activeIndex}`" class="image-ad-background" />
      <div class="image-ad-background-text">请在右边添加你需要的静态图片或动态图片</div> -->
      <img src="@/assets/image/icons/cutting_pic_empty.png" :ref="`backgroundImg${activeIndex}`" class="image-ad-background" />
      <div class="no-image-tip">{{ i18n('请在右侧添加图片') }}</div>
    </div>
    <!-- <div class="defaul-image image-ad-background" v-if="localProperty.propImageUrl" >
      <img :src="localProperty.propImageUrl" :ref="`backgroundImg${activeIndex}`" style="width: 100%; height: 100%; margin-top: 0" />
    </div>
    <div class="defaul-image image-ad-background" v-else>
      <img src="@/assets/image/icons/cutting_pic_empty.png" style="width: 84px; height: 72px" />
      <div class="no-image-tip">请在右侧添加图片</div>
    </div> -->

    <template v-if="flag">
      <vue-draggable-resizable
        v-for="(item, index) of localProperty.propItems"
        :key="(item, index)"
        :min-width="9"
        :min-height="9"
        :x="item.leftVertexMarginLeft"
        :y="item.leftVertexMarginTop"
        :w="item.hotZoneWidth"
        :h="item.hotZoneHeight"
        :handles="['br']"
        :parent="true"
        :on-drag-start="onDragStartCallback"
        @dragstop="onDragstop"
        @resizestop="onResizeStop"
        @activated="onActivated(item, index)"
        :class-name="className(item.validateResult, item)"
        :resizable="true"
      >
        <!-- class-name-active="drag-area-active" -->
        <div @click="doRemoveHotZone(index)" class="remove-btn">×</div>
      </vue-draggable-resizable>
    </template>
  </div>
</template>

<script lang="ts" src="./ImageAd.ts"></script>

<style lang="scss" scoped>
.template-image-ad {
  width: 100%;
  position: relative;
  min-height: 137px;
  background: white;

  .toolBar {
    position: absolute;
    top: 0;
    right: 1px;
    z-index: 999;
    cursor: pointer;
  }
  .defaul-image {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    text-align: center;
    img {
      margin-top: 25px;
    }
    .no-image-tip {
      width: 100%;
      margin-top: 12px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #a1a6ae;
    }
  }
  .image-ad-background {
    width: 100%;
    height: auto;
    background-size: cover;
  }
  .image-ad-background-text {
    margin-top: -30px;
    text-align: center;
    color: #8d8d8d;
  }

  .image-ad-hot-zone-success {
    position: absolute;
    top: 0;
    background-color: rgba(49, 139, 255, 0.65);
    border-color: #318bff;
    .remove-btn {
      position: absolute;
      right: -8px;
      top: -8px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 16px;
      height: 16px;
      border: 1px solid #de3232;
      border-radius: 50%;
      background-color: #fff;
      font-size: 16px;
      border-color: #318bff;
      color: #318bff;
    }
  }
  .image-ad-hot-zone-fail {
    position: absolute;
    top: 0;
    border: 1px solid #de3232;
    background-color: rgba(222, 50, 50, 0.65);
    .remove-btn {
      position: absolute;
      right: -8px;
      top: -8px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 16px;
      height: 16px;
      border: 1px solid #de3232;
      border-radius: 50%;
      background-color: #fff;
      font-size: 16px;
      border-color: #318bff;
      color: #318bff;
    }
  }
  .drag-area-active {
    position: absolute;
    top: 0;
    border: solid 1px #4559d4;
    background-color: rgba(74, 95, 245, 0.65);
    .remove-btn {
      position: absolute;
      right: -8px;
      top: -8px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 16px;
      height: 16px;
      border: 1px solid #de3232;
      border-radius: 50%;
      background-color: #fff;
      font-size: 16px;
      color: #de3232;
      cursor: pointer;
    }
  }
  // background-color: rgba(74,95,245,.65);
  //   border-color: #4559d4;
}
.activeCom {
  border: 2px solid #4d63ec;
}
.my-class {
  border: 1px solid #4d63ec;
}
</style>
