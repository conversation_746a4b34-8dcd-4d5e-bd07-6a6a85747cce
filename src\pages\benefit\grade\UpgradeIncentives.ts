import {Component, Vue} from 'vue-property-decorator'
import BreadCrume from 'cmp/bread-crumb/BreadCrume'
import I18nPage from "common/I18nDecorator";
import TradeGiftGradeApi from "http/tradegiftgrade/TradeGiftGradeApi";
import TradeGiftGradeActivity from "model/tradegiftgrade/TradeGiftGradeActivity";
import RSGrade from "model/common/RSGrade";
import MemberBalancePromotionApi from "http/payment/member/MemberBalancePromotionApi";

@Component({
  name: 'UpgradeIncentives',
  components: {
    BreadCrume
  }
})
@I18nPage({
  prefix: [
    '/权益/等级/单笔消费升级激励/编辑页',
    '/公用/按钮',
  ]
})
export default class UpgradeIncentives extends Vue {
  i18n: (str: string, params?: string[]) => string
  panelArray: any = []
  data: TradeGiftGradeActivity = new TradeGiftGradeActivity()
  initial = true
  gradeList: RSGrade[] = []
  gradeMap: any = {}
  rules: any
  $refs: any
  loading = {
    tradeGift: false,
    grade: false,
  }

  get anyLoading() {
    return this.loading.tradeGift || this.loading.grade
  }

  created() {
    this.panelArray = [
      {
        name: this.formatI18n("/公用/菜单", "消费有礼"),
        url: "coupon-active-manage",
      },
      {
        name: this.i18n('单笔消费升级激励'),
        url: ''
      }
    ]
    this.initRules()
    this.loadData()
  }

  loadData() {
    this.loading.tradeGift = true
    TradeGiftGradeApi.get().then((res: any) => {
      if (res.code === 2000 && res.data) {
        this.data = res.data
        this.initial = false
      }
    }).finally(() => {
      this.loading.tradeGift = false
    })

    this.loading.grade = true
    MemberBalancePromotionApi.gradeList().then((res: any) => {
      if (res.code === 2000 && res.data) {
        this.gradeList = res.data
        this.gradeList = this.gradeList.filter((e) => e.type !== 'SPECIAL')
        this.gradeList.sort((a: any, b: any) => {
          if (a.type !== b.type) {
            let typeMap: any = {
              FREE: 1,
              PAID: 2,
            }
            return typeMap[a.type] - typeMap[b.type]
          } else {
            return a.no - b.no
          }
        })
        this.gradeMap = {}
        for (let grade of this.gradeList) {
          if (grade && grade.code) {
            this.gradeMap[grade.code] = grade
          }
        }
      }
    }).finally(() => {
      this.loading.grade = false
    })
  }

  initRules() {
    this.rules = {
      validDate: { required: true, message: this.i18n('请输入大于0的整数'), trigger: ['blur', 'change'] },
      amount: [
        {
          required: true, validator: (rule: any, value: any, callback: any) => {
            if (!value || value < 0) {
              callback(this.i18n("请输入金额"))
            }
            callback()
          }, trigger: ['blur', 'change']
        }
      ],
      sourceGrade: [
        {
          required: true, validator: (rule: any, value: any, callback: any) => {
            if (!value) {
              callback(this.i18n("请选择等级"))
            }
            if (value === this.data.targetGrade) {
              callback(this.i18n("升级等级不可与当前等级重复！"))
            }
            callback()
          }, trigger: ['blur', 'change']
        }
      ],
      targetGrade: [
        {
          required: true, validator: (rule: any, value: any, callback: any) => {
            if (!value) {
              callback(this.i18n("请选择等级"))
            }
            if (value === this.data.sourceGrade) {
              callback(this.i18n("升级等级不可与当前等级重复！"))
            }
            callback()
          }, trigger: ['blur', 'change']
        }
      ],
    }
  }

  validateGrade() {
    if (this.data.sourceGrade) {
      this.$refs.form.validateField('sourceGrade')
    }
    if (this.data.targetGrade) {
      this.$refs.form.validateField('targetGrade')
    }
  }

  save() {
    this.data.justSave = true
    this.doSave(this.i18n('保存成功'))
  }

  cancel() {
    this.$router.back()
  }

  saveAndEnable() {
    this.data.justSave = false
    this.doSave(this.i18n('保存并启用成功'))
  }

  doSave(successMsg: string) {
    this.$refs.form.validate().then((res: any) => {
      if (this.data.sourceGrade) {
        this.data.sourceGradeType = this.gradeMap[this.data.sourceGrade].type
      }
      if (this.data.targetGrade) {
        this.data.targetGradeType = this.gradeMap[this.data.targetGrade].type
      }
      let saveFun = this.initial ? TradeGiftGradeApi.initialSave : TradeGiftGradeApi.save
      saveFun(this.data).then((res: any) => {
        this.$message.success(successMsg)
        this.$router.push({name: 'upgrade-incentives-dtl'})
      }).catch((e: any) => {
        this.$message.error(e.message)
      })
    })
  }
}