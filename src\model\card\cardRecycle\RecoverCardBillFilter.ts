/*
 * @Author: 黎钰龙
 * @Date: 2023-11-06 14:47:17
 * @LastEditTime: 2023-11-08 13:54:43
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\card\cardRecycle\RecoverCardBillFilter.ts
 * 记得注释
 */
import RecoverCardState from "./RecoverCardState"

export default class RecoverCardBillFilter {
  // 单号等于
  billNumberEquals: Nullable<string> = null
  // 单号类似于
  billNumberLikes: Nullable<string> = null
  // 状态等于
  stateEquals: Nullable<RecoverCardState> = null
  // 状态in
  stateIn: Nullable<RecoverCardState[]> = null
  // [用卡开始时间：useStartDate:>=]
  useStartDateAfterOrEqual: Nullable<Date> = null
  // [用卡开始时间：useStartDate:>]
  useStartDateAfter: Nullable<Date> = null
  // [用卡结束时间：useEndDate:<=]
  useEndDateBeforeOrEqual: Nullable<Date> = null
  // [用卡结束时间：useEndDate:<]
  useEndDateBefore: Nullable<Date> = null
  // 营销中心
  marketingCenterEquals: Nullable<string> = null
  // 用卡门店
  useStoreIdEquals: Nullable<string> = null
  // 
  page: Nullable<number> = null
  // 
  pageSize: Nullable<number> = null
}