<template>
  <el-dialog :title="i18n('选择发券场景')" append-to-body :close-on-click-modal="false" :visible.sync="dialogShow" @close="canel" width="75%">
    <div class="wrap">
      <el-row>
        <el-col :span="18">
          <el-table
            v-if="dialogShow"
            class="custom-table"
            ref="multipleTable"
            :data="tableData"
            stripe
            tooltip-effect="dark"
            style="width: 100%"
            @select="handleSelectionChange"
            @select-all="selectAll"
          >
            <el-table-column type="selection" width="60"></el-table-column>
            <el-table-column prop="name" :label="i18n('发券场景')" width="240"></el-table-column>
            <el-table-column prop="address" :label="i18n('说明')" show-overflow-tooltip></el-table-column>
          </el-table>
        </el-col>
        <el-col :span="6" style="padding-left: 5px">
          <el-row class="right-table">
            <el-row class="thead">{{ i18n('已选发券场景') }}: {{ multipleSelection.length }}</el-row>
            <el-row class="tbody">
              <el-row class="trow" v-for="(item, i) in multipleSelection" :key="item">
                <div class="content">
                  <div class="left">{{ item }}</div>
                  <div class="clear-btn" @click="remove(i)">
                    <div></div>
                  </div>
                </div>
              </el-row>
              <el-row v-if="!multipleSelection || multipleSelection.length === 0" class="trow" style="text-align: center; color: #909399">
                {{ i18n('暂无数据') }}
              </el-row>
            </el-row>
          </el-row>
        </el-col>
      </el-row>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="canel">{{ i18n('取消') }}</el-button>
      <el-button size="small" type="primary" @click="confirm">{{ i18n('确定') }}</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./SelectPopUp.ts"></script>

<style lang="scss" scoped>
.wrap {
  .custom-table ::v-deep .el-table__header th {
    background-color: #dde2eb;
  }
  .right-table {
    .thead {
      height: 35px;
      line-height: 35px;
      font-weight: 600;
      font-size: 13px;
      color: #020203;
      background-color: #dde2eb;
      padding-left: 8px;
      margin-bottom: 14px;
    }
    .tbody {
      .content {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 35px;
        line-height: 35px;
        font-size: 12px;
        color: #020203;
        background: #f2f4f8;
        padding: 0 12px;
        margin-bottom: 4px;
        background: #f2f4f8;
        border-radius: 2px;
        .clear-btn {
          width: 16px;
          height: 16px;
          position: relative;
          background: #ffffff;
          border: 1px solid #dde2eb;
          border-radius: 8px;
          div {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 9px;
            height: 2px;
            background: #1597ff;
            border-radius: 2px;
          }
        }
      }
    }
  }
}
</style>
