import ApiClient from 'http/ApiClient'
import Response from 'model/default/Response'
import CouponLedgerFilter from "model/bill/storeSettleBill/crmcoupon/CouponLedgerFilter";
import PhoenixCouponLedger from "model/bill/storeSettleBill/crmcoupon/PhoenixCouponLedger";

export default class PhoenixCouponLedgerApi {
  /**
   * 导出明细
   * 导出明细
   * 
   */
  static export(body: CouponLedgerFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/phoenix-ledger/coupon/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询券账单
   * 分页查询券账单。
   * 
   */
  static query(body: CouponLedgerFilter): Promise<Response<PhoenixCouponLedger[]>> {
    return ApiClient.server().post(`/v1/phoenix-ledger/coupon/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
