/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-07-30 11:11:56
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\common\RSOrgFilter.ts
 * 记得注释
 */
import {OrgState} from "model/common/OrgState";

export default class RSOrgFilter {
  // PHX-phx系统;THIRD_PARTY-第三方
  orgTypeEquals: Nullable<string> = null
  //
  orgIdEquals: Nullable<string> = null
  //
  idNameLikes: Nullable<string> = null
  //
  orgNameEquals: Nullable<string> = null
  //
  orgNameLikes: Nullable<string> = null
  //
  page: Nullable<number> = null
  //
  pageSize: Nullable<number> = null
  //
  sorters: any = {}
  
  queryNoZone: Nullable<boolean> = null
  // 
  zoneIdNotEquals: Nullable<string> = null
  // 
  zoneIdEquals: Nullable<string> = null

  marketingCenterIdNameLikes: Nullable<string> = null
  // 是否只查看无营销中心的门店
  queryNoMarketingCenter: boolean = false
  //
  marketingCenterIdNotEquals: Nullable<string> = null
  //
  marketingCenterIdEquals: Nullable<string> = null
  // 是否只根据当前营销中心查门店
  queryByMarketingCenter?: Nullable<Boolean> = null
  //
  orgStateEquals: Nullable<OrgState> = null
  // 高德审核状态   INIT- 审核中,SUCCESS - 审核成功 ，FAIL - 审核失败
  gaodeSFSyncState: Nullable<string> = null
}