<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:02
 * @LastEditTime: 2024-04-24 15:08:06
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\coupontenplate\cmp\CouponTemplateLogo.vue
 * 记得注释
-->
<template>
  <div class="coupon-template-logo">
    <el-form-item :label="formatI18n('/公用/券模板/新建券模板/券图标')">
      <el-row style="color: #79879E"><i class="el-icon-warning"/>
        <i18n k="/公用/券模板/规格：建议{0}，格式：{1}，单图大小：{2}以内；支持添加1张，不设表示使用默认图片">
          <template slot="0">800*600px</template>
          <template slot="1">png/jpg/jpeg/gif</template>
          <template slot="2">300KB</template>
        </i18n>
      </el-row>
      <el-row class="upload" v-loading="ruleForm.upload">
        <el-upload
            :headers="uploadHeaders"
            :action="uploadUrl"
            :with-credentials="true"
            :show-file-list="false"
            :on-success="onUploadSuccess"
            :on-error="onUploadError"
            :before-upload="prepareUpload"
        >
          <img v-if="ruleForm.logoUrl" :src="ruleForm.logoUrl" class="avatar">
          <div class="del-item" v-if="ruleForm.logoUrl">
            {{ i18n('点击更换') }}
            <img class="remove-icon" @click.stop="resetUrl" src="~assets/image/icons/ic_delete.png" alt="">
          </div>
          <div class="uploader-btn" v-else>
            <img src="~assets/image/icons/ic_uploader.png" alt="" style="margin-right:4px">
            <span style="white-space: nowrap;">{{i18n('/营销/大转盘活动/上传图片')}}</span>
          </div>
        </el-upload>
      </el-row>
    </el-form-item>
  </div>
</template>

<script lang="ts" src="./CouponTemplateLogo.ts"></script>

<style lang="scss">
.coupon-template-logo {
  .upload {
    position: relative;
    width: 100px;

    .el-upload {
      border-radius: 2px;
      border: 1px solid #D7DFEB;
      cursor: pointer;
      position: relative;

      .uploader-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 4px;
        height: 32px;
        font-size: 13px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #242633;
        img {
          width: 16px;
          height: 16px;
        }
      }
    }

    .el-upload:hover {
      border-color: #409EFF;
    }

    .avatar {
      width: 100px;
      height: 100px;
      display: block;
    }

    &:hover {
      .del-item {
        display: initial !important;
      }
    }

    .del-item {
      display: none;
      position: absolute;
      width: 102px;
      height: 102px;
      background: rgba(0, 0, 0, 0.5);
      top: 0;
      left: 0;
      text-align: center;
      line-height: 100px;
      color: white;
      cursor: pointer;
      z-index: 2;
    }
    .remove-icon {
      position: absolute;
      top: -8px;
      right: -8px;
      width: 20px;
      height: 20px;
      &:hover {
        cursor: pointer;
      }
    }
  }
}
</style>