import IdName from 'model/equityCard/default/IdName'
import LimitedMarketingCenter from 'model/equityCard/default/LimitedMarketingCenter'
import zones from 'model/equityCard/default/zones'

export default class StoreRange {
  // 门店范围类型：SAME——与活动门店一致； ALL——全部； PART——部分；EXCLUDE——指定门店不参加；
  storeRangeType: Nullable<string> = null
  // 门店范围
  stores: IdName[] = []
  // 营销中心范围。storeRangeLimitType为MARKETING_CENTER时使用
  marketingCenters: LimitedMarketingCenter[] = []
  // 门店范围限制类型：STORE——门店； MARKETING_CENTER——营销中心；
  storeRangeLimitType: Nullable<string> = null
  // 区域范围。storeRangeLimitType为ZONES时使用
  zones: zones[] = []
}