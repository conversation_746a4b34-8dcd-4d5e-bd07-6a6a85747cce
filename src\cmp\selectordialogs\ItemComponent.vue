<template>
	<div class="trow" style="position: relative;display: flex;align-items: center" :title="{ id: source.barcode, name: source.name } | idName">
		<div class="left">
			{{ { id: source.barcode, name: source.name } | idName }}
		</div>
		<div class="clear-btn" style="display: none">
			<a @click="delItem(source, index)">{{ formatI18n("/公用/公共组件/品牌选择弹框组件/表格/清除") }}</a>
		</div>
	</div>
</template>

<script lang="ts" src="./ItemComponent.ts"></script>

<style lang="scss" scoped>
.left {
	width: 177px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
    padding: 10px;
}
.trow {
	&:hover {
        cursor: pointer;
        background-color: #F8F9FC;
		.clear-btn {
			display: initial !important;
			position: absolute;
			right: 0;
		}
	}
}
</style>
