<!--
 * @Author: 黎钰龙
 * @Date: 2025-04-27 15:59:56
 * @LastEditTime: 2025-04-29 15:07:54
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\cmp\page-props-components\line-show-qty\LineShowQty.vue
 * 记得注释
-->
<template>
  <div class="line-show-qty">
    <el-form label-position="left" :model="value" :rules="rules" ref="form">
      <div>展示方式</div>
      <el-form-item prop="propLayoutStyle" label-width="0" style="margin-bottom: 0">
        <el-radio-group v-model="value.propLayoutStyle" @change="handleChange">
          <el-radio label="1">{{ i18n('一行一个') }}</el-radio>
          <el-radio label="2">{{ i18n('一行两个') }}</el-radio>
          <el-radio label="3">{{ i18n('一行三个') }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <template v-if="value.propLayoutStyle === '1'">
        <div>
          {{ i18n('单券样式') }}
          <el-tooltip effect="dark" :content="i18n('仅对单券活动生效')" placement="top">
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </div>
        <el-form-item prop="propShowStyle" label-width="0" style="margin-bottom: 0">
          <el-radio-group v-model="value.propShowStyle" @change="handleChange">
            <el-radio label="1">{{ i18n('样式一') }}</el-radio>
            <el-radio label="2">{{ i18n('样式二') }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </template>
      <el-form-item v-if="!(value.propLayoutStyle === '1' && value.propShowStyle === '2')" prop="propShowClaimedAndAvailableQty" label-width="0"
        style="margin-bottom: 0">
        <el-checkbox v-model="value.propShowClaimedAndAvailableQty" @change="handleChange">
          {{ i18n('显示已领/可领数量') }}
          <el-tooltip effect="dark" :content="i18n('仅对单券活动生效')" placement="top">
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </el-checkbox>
      </el-form-item>
      <div style="margin-bottom: 10px">预览效果</div>
      <template v-if="value.propLayoutStyle === '1'">
        <img class="coupon-img" mode="widthFix" v-if="value.propShowStyle === '1'" src="@/assets/image/fellow/single_coupon_style1.png" />
        <img class="coupon-img" mode="widthFix" v-else src="@/assets/image/fellow/single_coupon_style2.png" />
      </template>
      <template v-else-if="value.propLayoutStyle === '2'">
        <img class="coupon-img" mode="widthFix" src="@/assets/image/fellow/coupon_style_line_2.png" />
      </template>
      <img class="coupon-img" mode="widthFix" v-else-if="value.propLayoutStyle === '3'" src="@/assets/image/fellow/coupon_style_line_three.png" />
    </el-form>
  </div>
</template>

<script lang="ts" src="./LineShowQty.ts">
</script>

<style lang="scss" scoped>
.line-show-qty {
  padding: 12px;
  background: #f0f2f6;
  border-radius: 4px;
  margin-bottom: 20px;

  .coupon-img {
    width: 300px;
  }
}
</style>