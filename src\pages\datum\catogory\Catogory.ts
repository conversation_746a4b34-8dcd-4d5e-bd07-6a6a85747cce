import {Component, Vue} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import ListWrapper from 'cmp/list/ListWrapper.vue'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import RSCategoryFilter from 'model/common/RSCategoryFilter';
import CategoryApi from 'http/category/CategoryApi';
import Category from 'model/category/Category';
import FloatBlock from 'cmp/floatblock/FloatBlock.vue'
import BreadCrume from "cmp/bread-crumb/BreadCrume";

@Component({
    name: 'Catogory',
    components: {
        FormItem,
        ListWrapper,
        SubHeader,
        FloatBlock,
        BreadCrume
    }
})
export default class Catogory extends Vue {
    query: RSCategoryFilter = new RSCategoryFilter()
    panelArray: any = []
    data: Category[] = []
    queryData: Category[] = []
    // 分页
    page = {
        currentPage: 1,
        total: 0,
        size: 10
    }
    tableHeight: number = 0

    total: string = '0'

    created() {
        this.panelArray = [
            {
                name: this.formatI18n('/公用/菜单/品类'),
                url: ''
            },
        ]
        this.getList()
    }

    doSearch() {
        this.page.currentPage = 1
        this.getList()
    }

    doReset() {
        this.query = new RSCategoryFilter()
        this.page.currentPage = 1
        this.getList()
    }

    /**
     * 查询
     */
    onSearch() {
        this.page.currentPage = 1
        this.getList()
    }

    /**
     * 分页页码改变的回调
     * @param val
     */
    onHandleCurrentChange(val: number) {
        this.page.currentPage = val
        this.getList()
    }

    /**
     * 每页多少条的回调
     * @param val
     */
    onHandleSizeChange(val: number) {
        this.page.size = val
        this.getList()
    }

    /**
     * 表格排序条件
     */
    onSortChange({column, prop, order}: any) {
        // todo
    }

    private getList() {
        this.query.page = this.page.currentPage - 1
        this.query.pageSize = this.page.size
        CategoryApi.queryTree(this.query).then((resp: any) => {
            if (resp && resp.code === 2000) {
                this.data = resp.data
                this.queryData = JSON.parse(JSON.stringify(this.data))
                this.page.total = resp.total
                this.total = resp.fields.total
            }
        }).catch((error) => {
            if (error && error.message) {
                this.$message.error(error.message)
            }
        })
    }
}
