/*
 * @Author: 黎钰龙
 * @Date: 2024-08-12 16:43:36
 * @LastEditTime: 2024-10-14 09:53:35
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\card\writeCard\WriteMachineApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import ReadCardInfo from 'model/card/writeCard/ReadCardInfo'
import WriteCardMachineRequest from 'model/card/writeCard/WriteCardMachineRequest'
import Response from 'model/common/Response'
import WriteCardResponse from 'model/common/WriteCardResponse'

export default class WriteMachineApi {
  /**
   * 检测是否有卡(返回值为卡号)
   * 检测是否有卡。
   *  0——未放置卡片
   */
  static checkAndGet(): Promise<Response<string | number>> {
    return ApiClient.localhostServer().get(`/v1/card/checkAndGet`, {
    }).then((res) => {
      console.log('看看res', res);
      return res.data
    })
  }

  /**
  * 打开卡机，初始化读写器
  * 打开卡机，初始化读写器。
  * 
  */
  static open(medium: string): Promise<Response<boolean>> {
    return ApiClient.localhostServer().get(`/v1/card/device/open?medium=${medium}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 关闭卡机，关闭读写器
  * 关闭卡机，关闭读写器。
  * 
  */
  static close(): Promise<Response<boolean>> {
    return ApiClient.localhostServer().get(`/v1/card/device/close`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 检测是否为空卡
  * 检测是否为空卡。
  * 
  */
  static isEmpty(sn: string): Promise<Response<boolean>> {
    return ApiClient.localhostServer().get(`/v1/card/isEmpty?sn=${sn}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 制卡
  * 制卡。
  * 
  */
  static make(body: WriteCardMachineRequest): Promise<Response<boolean>> {
    return ApiClient.localhostServer().post(`/v1/card/make`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 读卡
  * 读卡
  * 
  */
  static read(sn: string): Promise<Response<ReadCardInfo>> {
    return ApiClient.localhostServer().get(`/v1/card/read?sn=${sn}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 清卡
  * 清卡
  * 
  */
  static clear(sn: string): Promise<Response<boolean>> {
    return ApiClient.localhostServer().get(`/v1/card/clear?sn=${sn}`, {
    }).then((res) => {
      return res.data
    })
  }
}