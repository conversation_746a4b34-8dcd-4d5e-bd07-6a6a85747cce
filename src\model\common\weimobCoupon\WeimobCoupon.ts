/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-01-23 10:50:05
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\common\weimobCoupon\WeimobCoupon.ts
 * 记得注释
 */
import CanUseDiscount from '../weimob/CanUseDiscount'
import { FreeChannel } from './FreeChannel'
import { IssueChannel } from './IssueChannel'
import BWeimobExtGoodsUseRule from 'model/common/weimob/BWeimobExtGoodsUseRule'
import BWeimobOrgUseRule from 'model/common/weimob/BWeimobOrgUseRule'


class UseScene {
  /**
   * 是否是全部场景
   * 默认true
   */
  allSceneDTO: Nullable<boolean> = null
  /**
   * 核销场景
   * 核销场景：1-网店订单；3-APP&收银台核销；7-API核销；8-买家直接消费；9-商家直接消费；10-商家开单；12-扫码购
   */
  shoppingMallSceneList: number[] = []
}
export default class WeimobCoupon {
  /**
   * 发放渠道
   * 可选值：PAY，FREE
   */
  issueChannel: Nullable<IssueChannel> = IssueChannel.FREE
  /**
   * 免费渠道
   * issueChannel为FREE时必填
   */
  freeChannels: FreeChannel[] = []
  /**
   * 适用商品类型
   * 1-全部商品适用；2-部分商品适用；3-部分分组适用；4-部分类目适用
   */
  limitGoodsType: Nullable<number> = 1
  // 微盟适用商品ids
  includeGoodsIds: string[] = []
  // 是否存在排除商品
  existExcludeGoods: Nullable<boolean> = false
  // 排除商品ids
  excludeGoodsIds: string[] = []
  // 每人限量
  perLimit: Nullable<number> = null
  // 每天限量
  dayLimit: Nullable<number> = null
  // 核销场景
  useScene: Nullable<UseScene> = null
  // 是否开启推荐，默认false，发放场景为店铺直接领取时生效
  enableRecommend: Nullable<boolean> = false
  // 推荐领取开始时间
  recommendStartTime: Nullable<Date> = null
  // 推荐领取结束时间
  recommendEndTime: Nullable<Date> = null
  // 商品适用规则
  goodsUseRule: Nullable<BWeimobExtGoodsUseRule> = new BWeimobExtGoodsUseRule()
  //券叠加其他优惠
  canUseDiscount: Nullable<CanUseDiscount> = new CanUseDiscount()
  // 可领券时间类型，类型包括：1-无限制；2-固定时间。
  sendTimeType: Nullable<number> = 1
  // 可领券的开始时间
  sendStartDate: Nullable<Date> = null
  // 可领券的结束时间
  sendEndDate: Nullable<Date> = null
  // 微盟适用门店(写死全部门店)
  limitOrgType: Nullable<string> = 'ALL'
  // 门店适用规则
  orgsUseRule: Nullable<BWeimobOrgUseRule> = new BWeimobOrgUseRule()
}