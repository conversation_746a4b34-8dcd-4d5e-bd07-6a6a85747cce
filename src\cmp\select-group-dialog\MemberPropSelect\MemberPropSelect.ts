/*
 * @Author: 黎钰龙
 * @Date: 2025-01-06 20:02:16
 * @LastEditTime: 2025-05-09 14:34:10
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\select-group-dialog\MemberPropSelect\MemberPropSelect.ts
 * 记得注释
 */
import { Component, Mixins, Prop } from 'vue-property-decorator'
import I18nPage from "common/I18nDecorator";
import Connective1 from "pages/member/insight/cmp/connective/Connective1";
import MemberGradeSelector
  from "pages/member/insight/cmp/rules/commonrule/memberrule/selectors/membergrade/MemberGradeSelector";
import MemberStateSelector
  from "pages/member/insight/cmp/rules/commonrule/memberrule/selectors/memberstate/MemberStateSelector";
import MemberRegisterSceneSelector
  from "pages/member/insight/cmp/rules/commonrule/memberrule/selectors/memberregisterscene/MemberRegisterSceneSelector";
import MemberGenderSelector
  from "pages/member/insight/cmp/rules/commonrule/memberrule/selectors/membergender/MemberGenderSelector";
import TimeRangeSelector from "pages/member/insight/cmp/rules/commonselector/timerange/TimeRangeSelector";
import MemberBirthdaySelector
  from "pages/member/insight/cmp/rules/commonrule/memberrule/selectors/memberbirthday/MemberBirthdaySelector";
import MemberRegisterChannelSelector
  from "pages/member/insight/cmp/rules/commonrule/memberrule/selectors/memberregisterchannel/MemberRegisterChannelSelector";
import MemberStoreScopeSelector
  from "pages/member/insight/cmp/rules/commonselector/storescope/MemberStoreScopeSelector";
import MemberRefereeSelector
  from "pages/member/insight/cmp/rules/commonrule/memberrule/selectors/memberreferee/MemberRefereeSelector";
import BaseMemberRuleEditor from "pages/member/insight/cmp/rules/commonrule/memberrule/BaseMemberRuleEditor";

import PaidMemberCardSelector
  from "pages/member/insight/cmp/rules/commonrule/memberrule/selectors/membercard/PaidMemberCardSelector.vue";
import TagDataDict from 'pages/member/insight/common/TagDataDict';
import I18nTool from 'common/I18nTool';
@Component({
  name: 'MemberPropSelect',
  components: {
    Connective1,
    MemberGradeSelector,
    MemberStateSelector,
    MemberRegisterSceneSelector,
    MemberGenderSelector,
    TimeRangeSelector,
    MemberBirthdaySelector,
    MemberRefereeSelector,
    MemberStoreScopeSelector,
    MemberRegisterChannelSelector,
    PaidMemberCardSelector
  }
})
@I18nPage({
  auto: false,
  prefix: [
    '/会员/洞察/标签管理/自定义标签/新建页/会员属性满足',
  ],
})
export default class TagMemberRuleEditor extends Mixins(BaseMemberRuleEditor) {
  @Prop({ type: Boolean, default: false }) loading: boolean;

  created() {
    this.propList.splice(this.propList.indexOf('referee'), 1)
    this.propList.splice(this.propList.indexOf('state'), 1)
    delete this.memberBasePropMap.state
    delete this.memberBasePropMap.referee
    this.memberBasePropMap.benefitCardTemplateCodes = I18nTool.match('/会员/权益卡/权益卡') + '/' + I18nTool.match('/会员/付费会员/付费会员卡')
    delete this.memberBasePropMap.paidBenefitCardTemplateCodes
  }
}
