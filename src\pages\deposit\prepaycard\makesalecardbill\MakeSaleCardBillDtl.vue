<template>
  <div class="make-sale-card-bill-dtl">

    <BreadCrume :panelArray="panelArray" :fontSize="18">
      <template slot="operate">
        <el-button @click="doExport" v-if="'AUDITED' === detail.state && hasOptionPermission('/卡/卡管理/制售单', '卡密导出')">
          {{ i18n('导出') }}
        </el-button>
        <el-button @click="doAudit" v-if="'INITIAL' === detail.state && hasOptionPermission('/卡/卡管理/制售单', '单据审核')">
          {{i18n('审核')}}
        </el-button>
<!--        <el-button @click="doRemove" v-if="'INITIAL' === detail.state && hasOptionPermission('/卡/卡管理/制售单', '单据维护')">-->
<!--          {{i18n('删除')}}-->
<!--        </el-button>-->
      </template>
    </BreadCrume>
    <div class="current-page">
      <el-form :model="detail" label-width="100px">
        <div class="panel">
          <div class="content">
            <el-row>
              <el-col :span="1" style="max-width: 100px">
                <!-- <img src="asd"/> -->
                &nbsp;
              </el-col>
              <el-col :span="18">
                <p style="color: #999999"><span>单号：</span>{{detail.billNumber}}</p>
                <p style="font-size: 20px;">制售单</p>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="split"></div>
        <div class="panel">
          <div class="content">
            <el-form-item label="卡模板" prop="cardTemplateNumber">
              <CardTplItem no-i18n :number="detail.cardTemplateNumber" :readonly="true" />
            </el-form-item>
            <el-form-item :label="i18n('/储值/预付卡/充值卡制售单/卡信息')" prop="cardTemplateNumber">
              <div class="face-amount-spec">
                <div class="line">
                  <el-row style="margin-top: 0">
                    <el-col :span="8">
                        <span class="amount">
                          {{i18n('/卡/卡管理/售卡单/卡面额/次数')}}：
                          <template v-if="detail.cardCount">{{detail.cardCount}}{{i18n('/营销/券礼包活动/券礼包活动/次')}}</template>
                          <template v-else>{{detail.cardFaceAmount}}{{i18n('/券/购券管理/元')}}</template>
                        </span>
                    </el-col>
                    <el-col :span="8" class="price">
                      <i18n k="/储值/预付卡/充值卡制售单/详情页面/售价：{0}元">
                        <template slot="0">
                          &nbsp;<span style="font-weight: 600">{{detail.cardPrice}}</span>&nbsp;
                        </template>
                      </i18n>&nbsp;
                    </el-col>
                    <el-col :span="8" class="price">
                      <i18n k="/储值/预付卡/充值卡制售单/详情页面/制售数量：{0}张">
                        <template slot="0">
                          &nbsp;<span style="font-weight: 600">{{detail.makeSaleQty}}</span>&nbsp;
                        </template>
                      </i18n>&nbsp;
                    </el-col>
                  </el-row>
                </div>
              </div>
              <el-row style="line-height: 35px">
                <i class="el-icon-warning" />
                <i18n k="/储值/预付卡/充值卡制售单/详情页面/本单共计制售{0}张卡">
                  <template slot="0">
                    &nbsp;{{this.detail.makeSaleQty}}&nbsp;
                  </template>
                </i18n>
              </el-row>
            </el-form-item>
            <el-form-item label="备注">
              <div no-i18n v-if="detail.remark" v-html="detail.remark.replace(/\n/g, '<br/>')"></div>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts" src="./MakeSaleCardBillDtl.ts">
</script>

<style lang="scss">
.make-sale-card-bill-dtl {
  width: 100%;
  height: 100%;
  background-color: white;
  overflow: auto;
  .current-page {
    overflow-y: auto;
    .panel {
      .header {
        font-weight: 500;
        padding: 20px 20px 0 20px;
        font-size: 18px;
      }
      .content {
        padding: 20px;
      }
      .face-amount-spec {
        background-color: #f9f9f9;
        .line {
          padding: 0;
          margin: 0 20px;
          line-height: 50px;
          .amount {
            font-size: 16px;
            font-weight: 600;
          }
          .price {
            font-size: 14px;
          }
        }
      }
    }

    .split {
      height: 20px;
      background-color: #eeeff1;
    }
  }
}
</style>