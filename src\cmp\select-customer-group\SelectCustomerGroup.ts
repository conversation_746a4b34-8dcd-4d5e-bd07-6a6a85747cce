/*
 * @Author: 黎钰龙
 * @Date: 2025-02-07 13:59:14
 * @LastEditTime: 2025-02-24 16:30:03
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\select-customer-group\SelectCustomerGroup.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import UserGroupV2Api from 'http/precisionmarketing/userGroup/UserGroupV2Api';
import IdName from 'model/common/IdName';
import UserGroupV2 from 'model/precisionmarketing/userGroup/UserGroupV2';
import UserGroupV2Filter from 'model/precisionmarketing/userGroup/UserGroupV2Filter';
import { Component, Model, Prop, Vue, Watch } from 'vue-property-decorator';
@Component({
  name: 'SelectCustomerGroup',
  components: {}
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/数据/客群画像'
  ],
  auto: true
})
export default class SelectCustomerGroup extends Vue {
  @Model('change') selectGroupIdName: IdName | string
  @Prop({ type: String, default: '400px' }) width: string;
  @Prop() placeholder: any;
  @Prop({ type: Boolean, default: false }) hideAll: boolean; //是否隐藏“全部”选项
  @Prop({ type: Boolean, default: true }) isOnlyId: boolean; //是否只绑定id
  @Prop({ type: Boolean, default: false }) disabled: boolean;
  selectLoading: boolean = false
  groups: UserGroupV2[] = []

  @Watch('selectGroup', { deep: true })
  handle(value: any) {
    if (!value) {
      this.getGroup('')
    }
  }

  get selectGroup() {
    return this.selectGroupIdName
  }
  set selectGroup(value: any) {
    const res = value ?? null
    this.$emit('change', res)
    const wholeInfo = this.groups.find((item) => {
      if ((this.isOnlyId && item.uuid === value) || (!this.isOnlyId && item.uuid === value.id)) {
        return true
      }
    })
    this.$emit('submit', wholeInfo)
  }

  created() {
    this.getGroup()
  }
  doRemoteMethod(value: string) {
    this.getGroup(value);
  }
  getGroup(value?: string) {
    let params: UserGroupV2Filter = new UserGroupV2Filter();
    params.nameLikes = value ?? null;
    params.page = 0;
    params.pageSize = 0;
    this.selectLoading = true
    UserGroupV2Api.query(params).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.groups = resp.data;
      } else {
        throw new Error(resp.msg)
      }
    }).catch((error: any) => {
      this.$message.error(error.message || this.i18n('内部异常'));
    }).finally(() => this.selectLoading = false)
  }
};