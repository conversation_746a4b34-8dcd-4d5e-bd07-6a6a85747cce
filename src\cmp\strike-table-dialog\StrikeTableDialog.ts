/*
 * @Author: 黎钰龙
 * @Date: 2023-08-28 10:44:54
 * @LastEditTime: 2023-08-28 14:43:19
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\strike-table-dialog\strikeTableDialog.ts
 * 记得注释
 */
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
@Component({
  name: 'StrikeTableDialog'
})
export default class StrikeTableDialog extends Vue {
  @Prop() value: any;
  @Prop({ type: Boolean }) dialogTableVisible: boolean;
  @Prop({ type: Boolean }) isMoreMarketing: boolean;

  @Watch('dialogTableVisible', { immediate: true })
  handle(value: boolean) {
    this.visible = value
  }

  visible: boolean = false

  isCancel(amount: string, type: string, points?: string): string {
    if (type === "交易冲账券作废" || type === "Cancellation of trade reversal vouchers") {
      return '-'
    } else {
      let pointsStr = points ? parseFloat(points) + this.i18n('积分') : ''
      let amountStr = amount ? amount + this.i18n('元') : ''
      let str = points ? ((pointsStr && amountStr) ? `${pointsStr}+${amountStr}` : pointsStr) : amount
      return str
    }
  }

  doClose() {
    this.$emit('close')
  }
};