import {Component, Prop, Vue, Watch} from 'vue-property-decorator'
import GoodsSelectorDialog from 'cmp/selectordialogs/GoodsSelectorDialog.vue'
import RSGoods from 'model/common/RSGoods'
import AmountToFixUtil from 'util/AmountToFixUtil'
import PickUpGoods from 'model/common/PickUpGoods'
import IdName from 'model/common/IdName'
import NumberUtil from 'util/NumberUtil'
import I18nPage from 'common/I18nDecorator'

@Component({
	name: "GetGoodsRange",
	components: {
		GoodsSelectorDialog,
	},
})
@I18nPage({
  prefix: [
    "/公用/券模板",
    '/公用/券模板/提货券/用券商品',
    '/营销/积分活动/积分活动/积分抵现活动/立即新建/单笔抵现上限'
  ],
  auto: true
})
export default class GetGoodsRange extends Vue {
	$refs: any;
	data: PickUpGoods[] = [];
	switchFlag = false;
	@Prop({
		type: String,
		default: "barcode",
	  })
	goodsMatchRuleMode: "barcode" | "code";
	@Prop()
	enablePayApportion: boolean;
	@Prop()
	value: any;
	@Prop({
		type: Boolean,
		default: true,
	})
	isAutoFixQty: Boolean;
	@Watch("value", { deep: true })
	onValueChange(value: any) {}
	get getAllAmount() {
		let num: number = 0;
		for (let i = 0; i < this.data.length; i++) {
			const item = this.data[i];
			if (item.bookPayPrice != null && item.qty != null) {
				num += item.bookPayPrice * item.qty;
			}
		}
		return num.toFixed(2);
	}
	created() {
		if (this.value && this.value.length > 0) {
			this.data = this.value;
			this.switchFlag = this.enablePayApportion;
			this.data.forEach((item) => {
				item.bookPayPrice = NumberUtil.format(item.bookPayPrice);
			});
		}
	}
	changeSwitch(flag: any) {
		this.$emit("changeSwitch", flag);
	}
	doChange(index: number) {
		if (this.isAutoFixQty === true) {
			this.data[index].qty = AmountToFixUtil.formatNumber(this.data[index].qty, 999999, 1);
		} else {
			this.data[index].qty = AmountToFixUtil.formatAmount(this.data[index].qty, 999999.0, 0.01, 2);
		}
		this.data[index].bookPayPrice = AmountToFixUtil.formatAmount(this.data[index].bookPayPrice, 999999.0, 0.01, 2);
		this.$emit("input", this.data);
		this.$emit("change");
	}
	doClear(index: number) {
		this.data[index].qty = null;
		this.data[index].bookPayPrice = null;
		this.$emit("input", this.data);
		this.$emit("change");
	}
	doDelete(index: number) {
		this.data.splice(index, 1);
		this.$emit("input", this.data);
		this.$emit("change");
	}
	doAdd() {
		let arr: RSGoods[] = [];
		if (this.data && this.data.length > 0) {
			this.data.forEach((item: any) => {
				let obj: RSGoods = new RSGoods();
				obj.barcode = item.goods.id;
				obj.name = item.goods.name;
				obj.price = item.price;
				if (this.goodsMatchRuleMode == 'code') {
					obj.qpcStr = item.qpcStr
				}
				arr.push(obj);
			});
		}
		this.$refs.selectGoodsScopeDialog.open(arr);
	}
	doSubmitGoods(arr: RSGoods[]) {
		let recordDataArray: any = JSON.parse(JSON.stringify(this.data));
		this.data = [];
		if (arr && arr.length > 0) {
			for (let i = 0; i < arr.length; i++) {
				if (recordDataArray && recordDataArray.length > 0) {
					let count = 0;
					for (let j = 0; j < recordDataArray.length; j++) {
						if (arr[i].barcode === recordDataArray[j].goods.id) {
							this.data.push(recordDataArray[j]);
							break;
						} else {
							count++;
						}
						if (count === recordDataArray.length) {
							let obj: PickUpGoods = new PickUpGoods();
							obj.goods = new IdName();
							obj.goods.id = arr[i].barcode;
							obj.goods.name = arr[i].name;
							obj.price = arr[i].price;
							obj.qpcStr = arr[i].qpcStr;
							obj.qty = null;
							this.data.push(obj);
						}
					}
				} else {
					let obj: PickUpGoods = new PickUpGoods();
					obj.goods = new IdName();
					obj.goods.id = arr[i].barcode;
					obj.goods.name = arr[i].name;
					obj.price = arr[i].price;
					obj.qpcStr = arr[i].qpcStr;
					obj.qty = null;
					this.data.push(obj);
				}
			}
		}
		this.$emit("input", this.data);
		this.$emit("change");
		if (this.data && this.data.length > 10) {
			this.data = this.data.splice(0, 10);
			this.$message.warning(this.formatI18n("/公用/券模板/提货券/用券商品/点击添加/选择超过10条数据点击确定js提示信息/最多能选择10个商品"));
		}
	}
	formatNum(index: any) {
		let obj = this.data[index].bookPayPrice ? this.data[index].bookPayPrice!.toString() : "";

		obj = obj.replace(/[^\d.]/g, ""); // 清除"数字"和"."以外的字符
		obj = obj.replace(/^\./g, ""); // 验证第一个字符是数字
		obj = obj.replace(/\.{2,}/g, "."); // 只保留第一个, 清除多余的
		obj = obj
			.replace(".", "$#$")
			.replace(/\./g, "")
			.replace("$#$", ".");
		obj = obj.replace(/^(\-)*(\d+)\.(\d\d).*$/, "$1$2.$3"); // 只能输入两个小数
		console.log(obj);
		this.data[index].bookPayPrice = Number(obj);
	}
}