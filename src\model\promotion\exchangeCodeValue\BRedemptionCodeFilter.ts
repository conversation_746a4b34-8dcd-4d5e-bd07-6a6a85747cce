/*
 * @Author: hl-cool <EMAIL>
 * @Date: 2024-08-01 13:53:27
 * @LastEditors: hl-cool <EMAIL>
 * @LastEditTime: 2024-08-01 14:10:36
 * @FilePath: \phoenix-web-ui\src\model\promotion\exchangeCodeValue\BRedemptionCodeFilter.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { State } from 'model/promotion/exchangeCodeValue/State'

// 兑换码查询
export default class BRedemptionCodeFilter {
    // 会员号，手机号，实体卡号等于
    memberIdentEquals: Nullable<string> = null
    // 单号等于
    billNumberEquals: Nullable<string> = null
    // 状态
    stateEquals: Nullable<State> = null
    // 码值
    codeIn: Nullable<string[]> = null
    // 页数
    page: Nullable<number> = null
    // 页面大小
    pageSize: Nullable<number> = null
}