import ApiClient from 'http/ApiClient'
import Response from 'model/default/Response'
import MeiTuanPTAuthRequest from "model/meituanpt/MeiTuanPTAuthRequest";

export default class MeiTuanPTAuthApi {

  static getAuthUrl(body: MeiTuanPTAuthRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/meituanpt/getAuthUrl`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   *
   * 获取美团到综平台解除授权链接
   * 获取美团到综平台解除授权链接
   *
   * */
  static getUnBindAuthUrl(body: MeiTuanPTAuthRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/meituanpt/getUnBindAuthUrl`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   *
   * 查询是否授权
   *
   * */
  static isAuthorized(body: MeiTuanPTAuthRequest): Promise<Response<boolean>> {
    return ApiClient.server().post(`/v1/meituanpt/isAuthorized`, body, {
    }).then((res) => {
      return res.data
    })
  }
}
