import { Vue, Component, Prop } from 'vue-property-decorator';
import PlaceTemplateMixins from '../PlaceTemplateMixins';
import I18nPage from 'common/I18nDecorator';
import { t } from '@wangeditor/editor';

@Component({
  name: 'MyCoupon',
  components: {},
  mixins: [PlaceTemplateMixins],
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理',
    '/设置/页面管理'
  ],
  auto: true
})
export default class MyCoupon extends Vue {
  @Prop()
  componentItem: any;
  marginBottom = 10;
  currentIndex = 0;
  tabsActiveIndex = 0;
  options: any = [this.i18n('未使用'), this.i18n('已使用'), this.i18n('已过期'), this.i18n('已失效')];
  active: number = 0;
  tabName: string = '我的券'

  activeTab(val: string) {
    this.tabName = val
  }
  handleClick(index: number) {
    this.tabsActiveIndex = index;
  }
  get componentItemProps() {
    return this.componentItem.props;
  }
}
