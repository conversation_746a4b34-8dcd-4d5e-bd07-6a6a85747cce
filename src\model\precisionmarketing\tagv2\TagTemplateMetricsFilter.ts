import GroupSection from 'model/default/GroupSection'

export default class TagTemplateMetricsFilter {
  // 标签模板ID
  tagTemplateId: Nullable<string> = null
  // 按标签值: tagValue;标签人数: coveredCount;
  orderField: Nullable<string> = null
  // true 升序；false 降序
  asc: Nullable<boolean> = null
  // 是否默认分组
  defaultGroup: Nullable<boolean> = null
  // 日期类型标签分组  day-日；month-月；year-年
  dateGroupType: Nullable<string> = null
  // 自定义分组
  groupSections: GroupSection[] = []
}