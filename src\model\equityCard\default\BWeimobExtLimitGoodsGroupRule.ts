import BWeimobExtLimitGoodsGroupRuleInfo from 'model/equityCard/default/BWeimobExtLimitGoodsGroupRuleInfo'
import IdName from 'model/equityCard/default/IdName'

// 适用商品组规则
export default class BWeimobExtLimitGoodsGroupRule {
  // 设置商品分组明细
  ruleInfos: BWeimobExtLimitGoodsGroupRuleInfo[] = []
  // 是否包含下级自建商品
  includeChildGoods: Nullable<boolean> = null
  // 是否限制是否不可用商品
  existExcludeGoods: Nullable<boolean> = null
  // 不可用商品
  excludeGoodsIds: IdName[] = []
}