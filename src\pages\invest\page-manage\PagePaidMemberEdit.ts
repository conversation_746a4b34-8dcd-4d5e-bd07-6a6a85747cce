import BreadCrume from 'cmp/bread-crumb/BreadCrume'
import I18nPage from 'common/I18nDecorator'
import { Component, Vue } from 'vue-property-decorator'
import UploadImg from '@/cmp/upload-imgs/UploadImg.vue'
import ContentTemplateFilter from 'model/template/ContentTemplateFilter'
import ContentTemplateApi from 'http/template/ContentTemplateApi'
import ContentTemplate from 'model/template/ContentTemplate'
import CreateRequest from 'model/template/CreateRequest'
import UpdateRequest from 'model/template/UpdateRequest'
import PublishRequest from 'model/template/PublishRequest'
import ConstantMgr from 'mgr/ConstantMgr'
import { enefitCardTemplateType } from "model/equityCard/default/enefitCardTemplateType";
import BenefitCardTemplateFilter from "model/equityCard/default/BenefitCardTemplateFilter";
import BenefitCardTemplateApi from "http/equityCard/BenefitCardTemplateApi";
import BenefitCardTemplate from "model/equityCard/BenefitCardTemplate";
import { enefitCardTemplateStatus } from 'model/equityCard/default/enefitCardTemplateStatus'
import CommonUtil from 'util/CommonUtil'
import PageJump from "@/pages/invest/page-manage/cmp/page-props-components/page-jump/PageJump.vue";

const backgroundImage = require("@/assets/image/invest/default_image_card.png");
const propPaidMemberCardsDefault:any = {
  propTitle: "付费会员中心",
  cardElement: {
    showCardNumber: true,
    showPeriod: true
  },
  paidMemberCard: "",
  memberBenefits: {
    imageType: "default", //会员权益: custom: 自定义
    notActivatedImage: "",
    activatedImage: ""
  },
  marketingPosition: {
    enabled: false,
    items: [
      {
        name: "",
        prompt: "",
        jumpPageInfo: null
      }
    ]
  }
}

@Component({
  name: 'PagePaidMemberEdit',
  components: {
    BreadCrume,
    UploadImg,
    PageJump
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理'
  ],
  auto: true
})
export default class PagePaidMemberEdit extends Vue {
  backgroundImage = backgroundImage
  ContentTemplateFilter: ContentTemplateFilter = new ContentTemplateFilter()
  ContentTemplate: ContentTemplate[] = []// 活动模型
  // 保存参数
  saveParams: CreateRequest = new CreateRequest()
  // 编辑参数
  updateParams: UpdateRequest = new UpdateRequest()
  // 发布参数
  publishRequestId: PublishRequest = new PublishRequest()
  editId: any = ''
  page = {
    currentPage: 1,
    total: 0,
    size: 20
  }
  cardList: BenefitCardTemplate[] = [];
  paidMemberCard = ''
  cardItem: any = {}
  // 卡面元素显隐
  showCardNumber = false // 卡号
  showPeriod = false // 有效期
  // 接收属性值
  widgets: any = []
  imgData: any = {
    id: "paidMemberCardCenter",
    type: "member",
    uuid: null,
    name: "付费会员中心",
    propTitle: "付费会员中心",
    propPaidMemberCards: []
  }
  propPaidMemberCards = propPaidMemberCardsDefault
  rules: any = {
    "paidMemberCard": [{ required: true, message: this.i18n("请选择付费会员卡"), trigger: "blur" }],
    "memberBenefits.imageType": [{ required: true, message: this.i18n("请选择会员权益"), trigger: "blur" }],
    "memberBenefits.notActivatedImage": [{ required: true, message: this.i18n("请选择会员权益图片"), trigger: "blur" }], 
    "memberBenefits.activatedImage": [{ required: true, message: this.i18n("请选择会员权益图片"), trigger: "blur" }], 
    "propMarketingPositionName": [{ required: true, message: this.i18n("请填写推荐位名称"), trigger: ["blur",'change'] }],
    "propMarketingPositionPrompt": [{ required: true, message: this.i18n("请填写提示语"), trigger: ["blur",'change'] }],
    "propMarketingPositionJumpPageInfo": [{ required: true, message: this.i18n("请选择跳转链接"), trigger: ["blur",'change'] }]    
  }
  $refs: any
  imgFormat: any = ['gif', 'jpg', 'jpeg', 'png'];

  handleChange() {
    // this.$emit('input', value);
    // this.$emit('change', value);

  }
  handleValidate() {
    
  }

  async created() {
    await this.queryMemberCardsList()
    if (this.$route.query.id) {
      this.editId = this.$route.query.id as string
      this.loadData()
    }
  }


  get isEnglish() {
    return sessionStorage.getItem("locale") === "en_US";
  }
  
  get credential() {
    return this.$store.state.credential;
  }

  get panelArray() {
    return [
      {
        name: this.i18n('编辑付费会员中心'),
        url: ''
      }
    ]
  }

  // 编辑
  async doUpdate() {
    const index = this.imgData.propPaidMemberCards.findIndex((item: any) => item.paidMemberCard === this.propPaidMemberCards.paidMemberCard);
    if (index === -1) {
      this.imgData.propPaidMemberCards.push(this.propPaidMemberCards);
    } else {
      this.imgData.propPaidMemberCards[index] = this.propPaidMemberCards;
    }
    this.widgets[1].props = this.imgData;
    this.widgets[1].type = 'custom';
    const loading = CommonUtil.Loading()
    try {
      let content: any = JSON.parse(this.updateParams.content);
      this.updateParams.type = 'system';
      content.widgets = this.widgets;
      this.updateParams.name = this.i18n('付费会员中心');
      this.updateParams.content = content;
      let res = await ContentTemplateApi.update(this.updateParams);
      if (res.code === 2000) {
        this.$message.success(this.i18n('保存成功'))
        this.$router.push({
          name: 'page-manage',
          query: { activeName: 'sys-manage' }
        });
      } else {
        this.$message.error(res.msg || this.i18n('保存失败'))
      }
    } catch (error) {
      this.$message.error((error as Error).message || this.i18n('保存失败'))
    } finally {
      loading.close()
    }
  }
  // 调用发布接口
  publish() {
    this.publishRequestId.id = this.editId
    ContentTemplateApi.publish(this.publishRequestId).then((res) => {
      // console.log(res);
      if (res.code === 2000) {
        this.$message.success(this.i18n('发布成功'))
        this.$router.push({
          name: 'page-manage',
          query: { activeName: 'sys-manage' }
        });
      } else {
        this.$message.error(res.msg || this.i18n('发布失败'))
      }
    }).catch(error => {
      let e = error as any;
      this.$message.error(e.message || this.i18n('发布失败'));
    })
  }

  // 取消返回
  goBack() {
    this.$router.push({
      name: 'page-manage',
      query: { activeName: 'sys-manage' }
    });
  }
  preserve(isPublish: any) {
    if (this.propPaidMemberCards.marketingPosition.enabled && !this.propPaidMemberCards.marketingPosition.items.length) {
      this.$message.error(this.i18n("至少添加一条推荐位"));
      return;
    }
    this.$refs.form.validate(async (valid: any) => {
      if(valid) {        
        await this.doUpdate()
        if(isPublish) {
         this.publish()
        }
      }else {
        this.$message.error(this.i18n('请完善装修信息！'));
      }
    });
  }
  // 编辑get参数
  loadData() {
    const loading = this.$loading(ConstantMgr.loadingOption)
    ContentTemplateApi.get(this.editId).then(res => {
      if (res.data) {
        console.log('res.data ==>', JSON.parse(res.data.content))
        this.updateParams = res.data as any
        if (res.data.content) {
          const details = JSON.parse(res.data.content)
          this.widgets = details.widgets
          // 当传过来有为自定义的就赋值
          this.widgets.forEach((item: any) => {
            if (item.type == 'custom') {
              this.imgData = item.props
              this.propPaidMemberCards = JSON.parse(JSON.stringify(this.imgData.propPaidMemberCards[0]))
              this.paidMemberCard = this.propPaidMemberCards.paidMemberCard
              this.showCardNumber = !this.propPaidMemberCards.cardElement.showCardNumber // 卡号
              this.showPeriod = !this.propPaidMemberCards.cardElement.showPeriod // 有效期
              this.cardItem = this.cardList.find(item => item.code === this.propPaidMemberCards.paidMemberCard) || new BenefitCardTemplate()
            }
          })
          // 当他只有一个值，并且为默认值时，得添加一个自定义
          if (this.widgets.length == 1 && this.widgets[0].type == 'default') {
            let obj = {}
            this.widgets.forEach((item: any) => {
              obj = item
            })
            this.widgets.push({ ...obj });
          }
        }
      }
    }).catch((err) => {
      this.$message.error(err.message || this.i18n('数据加载失败'))
    }).finally(() => {
      loading.close()
    })
  }

  //添加推荐位
  doAddFeaturedFirst() {
    if (this.propPaidMemberCards.marketingPosition.items.length >= 10) return this.$message.warning(this.i18n("最多设置{0}个档位").replace(/\{0\}/g, "10"));
    this.propPaidMemberCards.marketingPosition.items.push({
      name: "",
      prompt: "",
      jumpPageInfo: null,
    });
  }
  doRemoveProject(index: any) {
    this.propPaidMemberCards.marketingPosition.items.splice(index, 1);
  }

  // 选择跳转页面回调
  changeLink(val: object, obj: any) {
    Object.assign(obj.jumpPageInfo, val);
  }

  cardElementChange(val:any, type: any) {
    this.propPaidMemberCards.cardElement[type] = !val
  }

  async queryMemberCardsList() {
    const loading = CommonUtil.Loading()
    const queryParam = new BenefitCardTemplateFilter();
    queryParam.marketingCenterEquals = sessionStorage.getItem("marketCenter");
    queryParam.typeEquals = enefitCardTemplateType.paid;
    queryParam.statusEquals = enefitCardTemplateStatus.start;
    queryParam.page = 0;
    queryParam.pageSize = 99999;
    const res = await BenefitCardTemplateApi.query(queryParam)
    if (res && res.code === 2000) {
      this.cardList = res.data || [];
      this.page.total = res.total;
    }
    loading.close()
  }

  paidMemberCardChange(val: any) {
    this.cardItem = this.cardList.find(item => item.code === val) || new BenefitCardTemplate()
    const paidMemberCards = this.imgData.propPaidMemberCards.find((item: any) => item.paidMemberCard === val);
    if (paidMemberCards) {
      this.propPaidMemberCards = JSON.parse(JSON.stringify(paidMemberCards));
    } else {
      this.propPaidMemberCards = propPaidMemberCardsDefault;
    }
    this.propPaidMemberCards.paidMemberCard = val
    this.showCardNumber = !this.propPaidMemberCards.cardElement.showCardNumber // 卡号
    this.showPeriod = !this.propPaidMemberCards.cardElement.showPeriod // 有效期
  }
};