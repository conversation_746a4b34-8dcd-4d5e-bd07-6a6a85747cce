<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:05
 * @LastEditTime: 2024-04-07 15:44:02
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\prepaycard\prepaycardadjust\dialog\StoreValueAdjustReasonAdd.vue
 * 记得注释
-->
<template>
    <el-dialog :before-close="doBeforeClose" :close-on-click-modal="false"  :visible.sync="dialogShow" class="store-value-adjust-reason-add" :title="title ? title : i18n('修改预付卡调整原因')">
        <div class="wrap">
            <FormItem :label="label ? label : i18n('预付卡调整原因')">
                <el-input maxlength="50" @blur="doBlur" ref="reason" v-model="reason"></el-input>
                <div style="color: red" v-if="flag">{{validateMsg ? validateMsg : i18n('预付卡原因不能为空')}}</div>
            </FormItem>
        </div>
        <div class="dialog-footer" slot="footer">
            <el-button @click="doModalClose('cancel')">取消</el-button>
            <el-button @click="doModalClose('confirm')" size="small" type="primary">确定</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts" src="./StoreValueAdjustReasonAdd.ts">
</script>

<style lang="scss">
    .store-value-adjust-reason-add{
        display: flex;
        align-items: center;
        justify-content: center;
        .el-dialog{
            width: 582px;
            height: 226px;
            margin: 0 !important;
        }
        .wrap{
            width: 560px;
            margin: 0 auto;
            padding: 20px 30px 30px 30px;
            overflow: auto;
            .item{
                width: 228px;
                height: 108px;
                border: 1px solid #c7c7c7;
                border-radius: 10px;
                display: inline-block;
                margin-bottom: 24px;

                &:nth-child(odd) {
                    margin-right: 12px;

                }
                &:nth-child(even) {
                    margin-left: 12px;
                }
            }
        }
        .el-dialog__body{
            height: 119px;
        }
        .qf-form-item .qf-form-label {
          word-break:keep-all;
        }
        .qf-form-item .qf-form-content{
            margin-left: 110px !important;
        }
    }
</style>