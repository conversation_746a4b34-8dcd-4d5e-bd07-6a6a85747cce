<!--
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-04-24 14:58:59
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\benefit\couponinit\CouponTemplateAdd.vue
 * 记得注释
-->
<template>
  <div class="coupon-template-add">
    <BreadCrume :panelArray="panelArray" :fontSize="18">
    </BreadCrume>
    <template>
      <div class="coupon-template-navTab">
        <img class="left-nav-img" src="~assets/image/coupon/img_coupon_steps_bg.png" fit="aspectFill" />
        <div class="right-nav">
          <div class="nav-title">
            <span class="title-theme">
              {{barNavTest}}
              <template v-if="currentCouponLabel && currentStep !== 1">
                <span class="nav-coupon-type">
                  {{currentCouponLabel}}
                </span>
                {{i18n('模板功能')}}
              </template>
            </span>
            <span class="nav-desc">{{i18n('券模板是券营销活动的基础，它可以定义券的类型、数量、面值以及有效期等信息。通过预先定义券模板，您可以避免繁琐的重复设置')}}</span>
          </div>
          <div class="nav-bottom">
            <div :class="currentStep === 1 ? 'step-block current-step' : 'step-block'">
              <span class="step-title current">
                <img src="~assets/image/icons/ic_success.png" v-if="currentStep > 1">
                {{i18n('第1步：选择券类型')}}
              </span>
              <span class="step-desc">{{i18n('请仔细阅读每个券类型的描述，以确定其作用')}}</span>
            </div>
            <div class="arrow">
              <i class="el-icon-arrow-right"></i>
            </div>
            <div :class="currentStep === 2 ? 'step-block current-step' : 'step-block'">
              <span class="step-title">
                <img src="~assets/image/icons/ic_success.png" v-if="currentStep > 2">
                {{i18n('第2步：填写券内容')}}
              </span>
              <span class="step-desc">{{i18n('请确保填写的信息准确无误，例如券名称、券面额等')}}</span>
            </div>
            <div class="arrow">
              <i class="el-icon-arrow-right"></i>
            </div>
            <div :class="currentStep === 3 ? 'step-block current-step' : 'step-block'">
              <span class="step-title">{{i18n('第3步：完成建券')}}</span>
              <span class="step-desc">{{i18n('您可以创建一个与该券相关的券活动')}}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="coupon-template-selectType" v-if="currentStep === 1">
        <template v-for="item in couponTypeArray">
          <div class="type-item" :key="item.label" v-if="!item.shouldHide">
            <div class="type-left">
              <el-image style="width: 64px; height: 64px" :src="item.imgUrl" fit="aspectFill"></el-image>
            </div>
            <div class="type-right">
              <div class="type-title">{{item.label}}</div>
              <div class="type-desc">{{item.desc}}</div>
              <div class="type-btn" @click="doCreateCouponTemplate(item)">{{i18n('/公用/按钮/立即创建')}}</div>
            </div>
          </div>
        </template>
      </div>
    </template>
    <div class="coupon-operate" v-if="currentStep === 2">
      <el-button
        v-if="hasOptionPermission('/券/券管理/券模板', '券模板维护') && dtl.outerNumberNamespace && dtl.outerNumberId && $route.query.from === 'edit' && dtl.state === 'NOT_EFFECTED' && !auditPermission"
        @click="saveEffect()" type="primary">
        {{ formatI18n('/权益/券/券模板/保存并生效') }}
      </el-button>
      <el-button v-if="hasOptionPermission('/券/券管理/券模板', '券模板维护') && auditPermission && hasOptionPermission('/券/券管理/券模板', '券模板审核')" @click="saveAudit()"
        type="primary">
        {{ formatI18n('/公用/按钮', '保存并审核') }}
      </el-button>
      <el-button v-if="hasOptionPermission('/券/券管理/券模板', '券模板维护')" @click="save()" type="primary"
        style="background-color:#fff !important;color:#007EFF">
        {{ formatI18n('/公用/按钮', '保存') }}
      </el-button>
      <el-button v-if="hasOptionPermission('/券/券管理/券模板', '券模板维护')" @click="backToFirstStep">
        {{ i18n('/会员/等级/等级管理/未初始化状态/点击立即开始免费等级初始化/输入所有必填项点击下一步/底部按钮/上一步') }}
      </el-button>
      <el-button v-if="hasOptionPermission('/券/券管理/券模板', '券模板维护')" @click="doCancel">
        {{ formatI18n('/公用/按钮', '取消') }}
      </el-button>
    </div>

    <div v-if="currentStep === 2">
      <CouponTemplateWrap :showTypeRadio="false" :couponType="currentCouponType" v-model="coupon" :types="types" :state="dtl.state"
        :baseFieldEditable="baseFieldEditable" :copyFlag="copyFlag" :enableStore="true" @changeCouponType="changeCouponType" :baseSettingFlag="true"
        remarkMaxlength="5000" ref="couponTemplate">
      </CouponTemplateWrap>
    </div>

    <div v-if="currentStep === 3" class="success-container">
      <img src="~assets/image/icons/ic_success_large.png" style="width:100px;height:100px">
      <div class="success-title">
        {{coupon.coupons.name}}
        <template v-if="$route.query.from === 'edit'">
          {{i18n('/权益/积分/积分调整原因设置/点击修改/修改成功')}}
        </template>
        <template v-else>
          {{i18n('/权益/积分/积分调整原因设置/点击添加/新建成功')}}
        </template>
      </div>
      <div class="label-views">
        <span class="label-items" v-if="currentCouponTemplateNumber">
          {{i18n('/权益/券/券模板/券模板号')}}：{{currentCouponTemplateNumber}}
        </span>
        <span class="label-items">{{currentCouponLabel}}</span>
        <span class="label-items" v-if="couponFaceInfo">{{couponFaceInfo}}</span>
        <span :class="'label-items ' + couponFaceState.class">{{couponFaceState.str}}</span>
      </div>
      <div class="success-desc">{{i18n('可以根据此模板在以下平台进行相关券营销活动的创建')}}</div>
      <div style="margin-top:24px">
        <el-button @click="backToFirstStep">
          {{i18n("继续新建")}}
        </el-button>
        <el-button @click="gotoDtl">{{i18n("查看详情")}}</el-button>
        <el-button @click="backToList">{{i18n("返回列表")}}</el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./CouponTemplateAdd.ts">
</script>

<style lang="scss">
.coupon-template-add {
  height: 100%;
  width: 100%;

  .coupon-operate {
    position: fixed;
    bottom: 0;
    left: 240px;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 48px;
    background: #ffffff;
    box-shadow: 0px -4px 4px 0px rgba(0, 0, 0, 0.04);
    border-radius: 8px 8px 0px 0px;
    z-index: 10;
  }

  .coupon-template-navTab {
    width: 100%;
    min-height: 202px;
    background: #ffffff;
    border-radius: 8px;
    margin: 14px auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px;

    .left-nav-img {
      width: 90px;
      height: 144px;
      margin-bottom: 10px;
    }
    .right-nav {
      display: flex;
      flex-direction: column;
      flex: 1;
      min-height: 154px;
      margin-left: 20px;

      .nav-title {
        width: 100%;
        display: flex;
        flex-direction: column;
        text-align: left;

        .title-theme {
          font-size: 18px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #242633;
          line-height: 24px;
          .nav-coupon-type {
            font-size: 18px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #007eff;
            &::before {
              content: "「";
            }
            &::after {
              content: "」";
            }
          }
        }

        .nav-desc {
          font-size: 13px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #79879e;
          line-height: 20px;
          margin-top: 4px;
        }
      }

      .nav-bottom {
        display: flex;
        margin-top: 28px;
        width: 100%;
        .step-block {
          padding: 16px 12px;
          flex: 1;
          max-width: 303px;
          min-height: 82px;
          border-radius: 8px;
          display: flex;
          flex-direction: column;
          background: linear-gradient(360deg, #ffffff 0%, #f2f2f7 100%);
          border: 1px solid rgba(151, 151, 151, 0.2);

          .step-title {
            font-size: 16px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            line-height: 24px;
            color: #546072;
            display: flex;
            align-items: center;
            img {
              width: 18px;
              height: 18px;
            }
          }

          .step-desc {
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #79879e;
            line-height: 18px;
            margin-top: 4px;
          }
        }
        .arrow {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 40px;
          height: 78px;
        }
        .current-step {
          background: linear-gradient(180deg, rgba(0, 126, 255, 0.1) 0%, rgba(0, 126, 255, 0) 100%);
          border: 1px solid rgba(0, 151, 255, 0.2);
          .step-title {
            color: #007eff;
          }
        }
      }
    }
  }

  .coupon-template-selectType {
    padding: 24px;
    background: #ffffff;
    border-radius: 8px;
    display: flex;
    flex-wrap: wrap;

    .type-item {
      display: flex;
      width: 380px;
      height: 170px;
      padding: 24px 24px 0;
      margin-right: 12px;
      margin-bottom: 20px;
      background: linear-gradient(360deg, rgba(255, 255, 255, 0.2) 0%, rgba(215, 215, 234, 0.2) 100%);

      .type-left {
        width: 64px;
        height: 100%;
      }
      .type-right {
        position: relative;
        display: flex;
        flex-direction: column;
        flex: 1;
        margin-left: 16px;
        .type-title {
          font-size: 20px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #242633;
        }
        .type-desc {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #79879e;
          margin-top: 8px;
        }
        .type-btn {
          position: absolute;
          bottom: 0;
          width: 76px;
          height: 32px;
          font-size: 13px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #242633;
          line-height: 32px;
          text-align: center;
          background: #ffffff;
          border-radius: 2px;
          border: 1px solid #d7dfeb;
          cursor: pointer;
          &:hover {
            color: #007eff;
            border: 1px solid #007eff;
          }
        }
      }
    }
  }

  .success-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #ffffff;
    border-radius: 8px;
    padding: 24px;
    min-height: 650px;
    .success-title {
      font-size: 24px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #242633;
      margin-top: 24px;
    }
    .label-views {
      display: flex;
      justify-content: space-around;
      margin-top: 8px;
      .label-items {
        background: #f0f2f6;
        border-radius: 4px;
        padding: 4px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #36445a;
        margin: 0 3px;
      }
      .label-success {
        background: #e7f9f0;
        color: #0cc66d;
      }
      .label-warning {
        background-color: #f8f9e6;
        color: #ffaa00;
      }
      .label-cancel {
        background-color: #a1b0c8;
        color: #ffffff;
      }
    }
    .success-desc {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #79879e;
      margin-top: 8px;
    }
  }

  .el-form-item {
    margin-bottom: 8px;
  }
}
</style>