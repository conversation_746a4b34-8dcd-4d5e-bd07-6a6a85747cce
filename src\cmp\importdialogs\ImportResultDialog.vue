<template>
  <div class="import-result-dialog">
    <el-dialog :visible.sync="dialogShow" append-to-body
               class="import-result-dialog-view" :title="formatI18n('/公用/导入/导入结果')">
      <div class="wrap">
        <div>{{ formatI18n('/公用/导入/导入结果') }}：
          <template v-if="data.importResult">
            <el-button @click="doDownload" type="text">{{ formatI18n('/公用/导入/下载') }}
            </el-button>
          </template>
          <template v-else><span style="color: red">{{formatI18n('/公用/导入/导入失败')}}</span></template>
        </div>
        <div>{{ formatI18n('/公用/导入/导入成功') }}：&nbsp;<span style="color: green">{{ data.successCount }}</span></div>
        <div>{{ formatI18n('/公用/导入/导入失败') }}：&nbsp;<span style="color: red">{{ data.errorCount }}</span></div>
        <div>{{ formatI18n('/公用/导入/导入忽略') }}：&nbsp;<span>{{ data.ignoreCount }}</span></div>
      </div>
      <div class="dialog-footer" slot="footer">
        <el-button @click="doModalClose">{{ formatI18n('/资料/门店/取 消') }}</el-button>
        <el-button @click="doModalClose" size="small" type="primary">{{ formatI18n('/资料/门店/确 定') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" src="./ImportResultDialog.ts">
</script>

<style lang="scss">
.import-result-dialog-view {
  display: flex;
  align-items: center;
  justify-content: center;

  .el-dialog {
    width: 350px;
    height: 250px;
    margin-top: 0px !important;

    .el-dialog__body {
      height: 137px;
    }
  }

  .wrap {
    padding-left: 75px;
    padding-top: 20px;
  }
}
</style>
