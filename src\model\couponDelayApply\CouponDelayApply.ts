import IdName from 'model/common/IdName'
import MemberIdent from 'model/common/member/MemberIdent'
import MutableNsid from 'model/common/MutableNsid'
import { State } from 'model/common/State'

export default class CouponDelayApply extends MemberIdent {
  // uuid
  uuid: Nullable<string> = null
  // 营销中心
  marketingCenter: Nullable<string> = null
  // 购券门店
  purchaseOrg: Nullable<IdName> = null
  // 发生区域
  zone: Nullable<IdName> = null
  // 延期申请时间
  delayApplyTime: Nullable<Date> = null
  // 会员id
  memberId: Nullable<string> = null
  // 活动号
  activityNumber: Nullable<string> = null
  // 处理状态
  state: Nullable<State> = null
  // 活动名称
  activityName: Nullable<string> = null
  // 活动类型
  activityType: Nullable<string> = null
  // 活动形式
  activityForm: Nullable<string> = null
  // 购券时间
  purchaseTime: Nullable<Date> = null
  // 购券订单号
  purchaseOrderNo: Nullable<string> = null
  // 支付订单号
  paymentOrderNo: Nullable<string> = null
  // 延期请求交易
  tradeId: Nullable<MutableNsid> = null
  // 券模板号
  templateNumber: Nullable<string> = null
  // 券模板名称
  templateName: Nullable<string> = null
  // 购券数量
  purchaseCount: Nullable<number> = null
  // 过期数量
  expireCount: Nullable<number> = null
}