import I18nPage from "common/I18nDecorator";
import DateUtil from "util/DateUtil";
import { Component, Prop, Vue } from "vue-property-decorator";
import DateMultiplySelector from "../DateMultiplySelector/DateMultiplySelector";

@Component({
  name: "AnalysisDateSelector",
  components: {
    DateMultiplySelector,
  },
})
@I18nPage({
  prefix: ["/公用/券模板", "/公用/日期"],
  auto: true,
})
export default class AnalysisDateSelector extends Vue {
  $refs: any
  @Prop({ type: String }) label: string;
  selectData: string = ''

  get dayOptions() {
    return {
      firstDayOfWeek: 1,
      shortcuts: [
        {
          text: this.i18n("昨日"),
          onClick(picker: any) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
            end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit("pick", [start, end]);
          },
        },
        {
          text: this.i18n("近7天"),
          onClick(picker: any) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit("pick", [start, end]);
          },
        },
        {
          text: this.i18n("近30天"),
          onClick(picker: any) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit("pick", [start, end]);
          },
        },
        {
          text: this.i18n("近90天"),
          onClick(picker: any) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit("pick", [start, end]);
          },
        },
        {
          text: this.i18n("上周"),
          onClick(picker: any) {
            const end = new Date();
            const start = new Date();
            const lastWeekTime = start.getTime() - 3600 * 1000 * 24 * 8;
            start.setTime(DateUtil.getWeekDatesOne(lastWeekTime)[0].getTime());
            end.setTime(DateUtil.getWeekDatesOne(lastWeekTime)[1].getTime());
            picker.$emit("pick", [start, end]);
          },
        },
        {
          text: this.i18n("上月"),
          onClick(picker: any) {
            const end = new Date();
            const start = new Date();
            const year = start.getFullYear();
            const month = start.getMonth();
            const firstDay = new Date(year, month - 1, 1); // 上月第一天
            const lastDay = new Date(year, month, 0); // 上月最后一天
            start.setTime(firstDay.getTime());
            end.setTime(lastDay.getTime());
            picker.$emit("pick", [start, end]);
          },
        },
      ],
      //首先是抉择出开始时间，依据开始时间给出可选的六个月时间范畴
      onPick: ({ maxDate, minDate }: any) => {
        this.selectData = minDate.getTime();
        if (maxDate) {
          // 解除限制
          this.selectData = "";
        }
      },
      disabledDate: (time: any) => {
        if (this.selectData) {
          const curDate = this.selectData;
          const three = 90 * 24 * 3600 * 1000; // 90天
          const threeMonths = curDate + three; // 开始时间+90天
          return (time.getTime() < curDate || time.getTime() > threeMonths) || time.getTime() >= DateUtil.nowDayTime();
        } else {
          return time.getTime() >= DateUtil.nowDayTime(); //没选的时候，今天及以后的时间都不能选
        }
      },
    };
  }

  get weekOptions() {
    return {
      firstDayOfWeek: 1,
      disabledDate: (time: any) => {
        return time.getTime() + 24 * 3600 * 1000 >= DateUtil.getWeekDatesOne(new Date())[0].getTime(); //本周及以后的时间都不能选
      },
    };
  }

  get monthOptions() {
    return {
      disabledDate: (time: any) => {
        const date = new Date();
        const year = date.getFullYear();
        const month = date.getMonth();
        const firstDay = new Date(year, month, 1);
        return time.getTime() + 24 * 3600 * 1000 >= firstDay.getTime(); //本月及以后的时间都不能选
      },
    };
  }

  // 按日 默认选择最近七天
  defaultDayRange() {
    const start = DateUtil.format(new Date(new Date().getTime() - 3600 * 1000 * 24 * 7), "yyyy-MM-dd");
    const end = DateUtil.format(new Date(new Date().getTime() - 3600 * 1000 * 24 * 1), "yyyy-MM-dd");
    return [start, end];
  }

  // 按周 默认选择上周
  defaultWeekRange() {
    const date = new Date().getTime() - 3600 * 1000 * 24 * 8;
    return new Date(date);
  }

  // 按月 默认选择上月
  defaultMonthRange() {
    const start = new Date();
    const year = start.getFullYear();
    const month = start.getMonth();
    return new Date(year, month - 1, 1); // 上月第一天
  }

  onChange(value: any) {
    this.$emit('change', value)
  }

  doReset() {
    this.$refs.dateMultiplySelector.doReset()
  }

  doValidate() {
    return this.$refs.dateMultiplySelector.doValidate()
  }
}