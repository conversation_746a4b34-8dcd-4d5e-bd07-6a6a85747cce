/*
 * @Author: hl-cool <EMAIL>
 * @Date: 2024-08-01 13:51:44
 * @LastEditors: hl-cool <EMAIL>
 * @LastEditTime: 2024-08-05 16:03:16
 * @FilePath: \phoenix-web-ui\src\model\promotion\exchangeCodeValue\BRedemptionCode.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import BRedemptionCodeGift from 'model/promotion/exchangeCodeValue/BRedemptionCodeGift'
import { State } from 'model/promotion/exchangeCodeValue/State'

// 兑换码
export default class BRedemptionCode {
    // 码
    code: Nullable<string> = null
    // 单号
    billNumber: Nullable<string> = null
    // 码状态
    codeState: Nullable<State> = null
    // 开始时间
    beginTimeInclusive: Nullable<Date> = null
    // 结束时间
    endTimeExclusive: Nullable<Date> = null
    // 生成时间
    createTime: Nullable<Date> = null
    // 使用时间
    useTime: Nullable<Date> = null
    // 作废时间
    abortTime: Nullable<Date> = null
    // 使用会员
    useMemberId: Nullable<string> = null
    // 备注
    remark: Nullable<string> = null
    // 手机号
    mobile: Nullable<string> = null
    // 券/卡
    gifts: BRedemptionCodeGift[] = []
    disabled?: boolean; // 添加可选的布尔类型属性

    //会员号 
    crmCode : Nullable<string> = null
}