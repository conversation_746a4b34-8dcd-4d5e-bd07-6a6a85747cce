import BarcodeWithAmountImportResult from "model/goods/BarcodeWithAmountImportResult";

export default class BarcodeImportResult {
  // 商品条码
  barcodes: Nullable<string[]> = null
  barcodeWithAmountImportResult: Nullable<BarcodeWithAmountImportResult> = null
  //
  backUrl: Nullable<string> = null
  errorCount: Nullable<number> = null
  ignoreCount: Nullable<number> = null;
  successCount: Nullable<number> = null;
}