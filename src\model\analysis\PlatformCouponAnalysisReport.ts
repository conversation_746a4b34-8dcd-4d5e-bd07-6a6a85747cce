import AnalysisChartData from "./AnalysisChartData"
import { AnalysisReportDateUnit } from "./AnalysisReportDateUnit"
import PlatformCouponAnalysisSummary from "./PlatformCouponAnalysisSummary"

// 平台券分析报表响应
export default class PlatformCouponAnalysisReport {
  // 时间维度
  dateUnit: Nullable<AnalysisReportDateUnit> = null
  // 数据概览
  summary: Nullable<PlatformCouponAnalysisSummary> = null
  // 用券数量
  usedCouponCountData: AnalysisChartData[] = []
  // 连带销售额
  tradeAmountData: AnalysisChartData[] = []
  // 抵用金额
  deductAmountData: AnalysisChartData[] = []
}