<template>
  <div class="member-growth-analysis-container">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button v-if="hasOptionPermission('/数据/分析/会员增长分析','数据导出')" @click="doExport" size="large">
          {{formatI18n('/储值/预付卡/充值卡制售单/列表页面','导出')}}
        </el-button>
      </template>
    </BreadCrume>
    <div class="search-block">
      <MyQueryCmp @reset="doReset" @search="onSearch" :showExpand="false">
        <el-row>
          <el-col :span="16">
            <AnalysisDateSelector ref="analysisDateSelector" :label="i18n('日期粒度')" @change="doDateChange">
            </AnalysisDateSelector>
          </el-col>
          <el-col :span="8">
            <FormItem :label="i18n('/资料/渠道/渠道')">
              <ChannelSelect v-model="filter.channel" :isShowAll="true" width="100%"></ChannelSelect>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <FormItem :label="i18n('门店')">
              <SelectStores v-model="filter.store" :isOnlyId="true" :hideAll="false" width="100%" :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
              </SelectStores>
            </FormItem>
          </el-col>
          <el-col :span="8">
            <FormItem :label="i18n('/资料/门店/区域主管')">
              <SelectEmployees v-model="filter.areaLeaderEquals" width="100%"></SelectEmployees>
            </FormItem>
          </el-col>
          <el-col :span="8">
            <FormItem :label="i18n('/资料/门店/营运经理')">
              <SelectEmployees v-model="filter.operationEquals" width="100%"></SelectEmployees>
            </FormItem>
          </el-col>
        </el-row>
      </MyQueryCmp>
    </div>

    <!-- 数据概览 -->
    <div class="overview-block" v-if="detail.summary">
      <div class="overview-title">
        {{i18n('/储值/预付卡/电子礼品卡活动/效果评估/数据概览')}}
        <el-tooltip placement="top" effect="light">
          <div slot="content">
            <div style="color: #36445A;line-height: 20px;font-size: 13px;">
              <div style="font-size: 15px;margin-bottom:8px">{{i18n('/储值/预付卡/电子礼品卡活动/效果评估/指标说明')}}</div>
              <div>
                {{i18n('新会员数：在查询条件下新注册的会员数')}}；<br />
                {{i18n('有手机新会员数：在查询条件下新注册的有手机号的会员数')}}；<br />
                {{i18n('会员数：在查询条件下，截止查询时间结束时间的会员数')}}；<br />
                {{i18n('有手机会员数：在查询条件下，截止查询时间结束时间有手机号的会员数')}}；<br />
                {{i18n('有消会员数：在查询条件下，统计期间有消费会员数量，即使消费冲账也统计在内')}}；<br />
                {{i18n('有消新会员数量：在查询条件下，首次消费时间在统计期间的会员数')}}；<br />
                {{i18n('新会员有消占比：有消新会员数量/新会员数')}}；<br />
                {{i18n('复购会员数量：在查询条件下，统计期间消费2次及以上的会员数')}}；<br />
                {{i18n('复购率：复购会员数量/有消会员数量')}}；<br />
                {{i18n('查询条件中的门店是根据会员归属门店统计')}}；<br />
              </div>
            </div>
          </div>
          <i class="el-icon-warning-outline"></i>
        </el-tooltip>
      </div>
      <div class="overview-info">
        <div class="info-item" v-for="(item,index) in summaryViewArr" :key="index">
          <div class="item-title">{{item.label}}</div>
          <div class="item-number">{{item.value}}</div>
          <div class="item-trend">
            <span style="margin-right:4px">{{i18n('环比')}}</span>
            <span style="color: #00D29B" v-if="item.ringValue > 0">
              <i class="el-icon-sort-up"></i>
              +{{(item.ringValue * 100).toFixed(2)}}%
            </span>
            <span style="color: #FA5050" v-else>
              <i class="el-icon-sort-down"></i>
              {{(item.ringValue * 100).toFixed(2)}}%
            </span>
          </div>
        </div>
      </div>
    </div>

    <div class="chart-block">
      <MemberLineChart :legendNames="legendNames" :xAxisArray="xAxisArray" :dateType="detail.dateUnit" :valueArray="valueArray" :showPercentName="showPercentName">
      </MemberLineChart>
    </div>
    <DownloadCenterDialog :dialogvisiable="downloadCenterFlag" :showTip="true" @dialogClose="doDownloadDialogClose">
    </DownloadCenterDialog>
  </div>
</template>

<script lang="ts" src="./MemberGrowthAnalysis.ts">
</script>

<style lang="scss" scoped>
.member-growth-analysis-container {
  width: 100%;
  padding-bottom: 30px;
  overflow: auto;
  .search-block {
    width: 100%;
    border-radius: 8px;
    background-color: #fff;
    padding: 24px;
  }
  .overview-block {
    width: 100%;
    border-radius: 8px;
    background-color: #fff;
    padding: 24px 0 0 24px;
    margin-top: 16px;
    .overview-title {
      display: flex;
      align-items: center;
      font-weight: 600;
      font-family: PingFangSC, PingFang SC;
      font-size: 16px;
      color: #111111;
      line-height: 24px;
      margin-bottom: 20px;
      .el-icon-warning-outline {
        cursor: pointer;
        margin-left: 4px;
        &:hover {
          color: #2878ff;
        }
      }
    }
    .overview-info {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
    }
    .info-item {
      width: 185px;
      margin-bottom: 24px;
      .item-title {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #898fa3;
        line-height: 20px;
        text-align: left;
      }
      .item-number {
        font-family: DINAlternate, DINAlternate;
        font-weight: bold;
        font-size: 20px;
        color: #111111;
        line-height: 24px;
        text-align: left;
        margin-top: 2px;
      }
      .item-trend {
        display: flex;
        align-items: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #969799;
        line-height: 17px;
        margin: 12px 4px 0 0;
      }
    }
  }
  .chart-block {
    padding: 0px 24px 24px;
    background: #ffffff;
    border-radius: 8px;
    margin-top: 16px;
  }
}
</style>