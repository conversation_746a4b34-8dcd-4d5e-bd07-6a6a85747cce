import I18nPage from 'common/I18nDecorator';
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import ContentTemplateApi from 'http/template/ContentTemplateApi'
import NavigationSetting from 'model/navigation/NavigationSetting'
import ContentTemplate from 'model/template/ContentTemplate'
import ListWrapper from 'cmp/list/ListWrapper.vue'
import FormItem from 'cmp/formitem/FormItem.vue'
import { EditMode } from 'model/local/EditMode';
import CommonUtil from 'util/CommonUtil';
import UnionActivityQuery from 'model/promotion/UnionActivityQuery';
import UnionActivityApi from 'http/promotion/UnionActivityApi';
import ActivityBody from 'model/common/ActivityBody';
import MyQueryCmp from 'cmp/querycondition/MyQueryCmp';

class Query {
  nameLike: string = ''  //活动名称
  numberEquals: string = '' //活动号
  topicNameLikes: string = ''  //所属主题
  stateEquals: Nullable<string> = ''  //状态
  typeEquals: Nullable<string> = null //活动类型
  outerNumberIdLike: string = '' //外部活动号
}
class Total {
  all: number = 0
  initial: number = 0
  audit: number = 0
  doing: number = 0
  end: number = 0
  suspend: number = 0
}
class UnionActivityItem {
  label: string = ''  //标签值
  value: string = ''  //活动筛选值
  activityType: string = '' //活动类型
  viewAble: boolean = false //查看权限
  modifyAble: boolean = false //修改权限
  auditAble: boolean = false  //审核权限
  stopAble: boolean = false //终止权限
  removeAble: boolean = false //删除权限
  goToDtl: Function = () => { }  //前往详细页
  goToCopy: Function = () => { }  //前往复制
  goToModify: Function = () => { } //前往修改
}
@Component({
  name: 'ElasticLayer',
  components: {
    FormItem,
    ListWrapper,
    MyQueryCmp
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理',
    '/公用/菜单',
    '/营销/券礼包活动/券礼包活动',
    '/营销/券礼包活动/小程序领微信券'
  ],
  auto: true
})
export default class ElasticLayer extends Vue {
  @Prop({ type: String })
  activityType: string;

  ContentTemplate: ContentTemplate[] = []// 活动模型
  UnionActivityQuery: UnionActivityQuery = new UnionActivityQuery()
  // INITAIL——未审核；UNSTART——未开始；PROCESSING——进行中；STOPED——已结束
  ContentTemplateState: any = [{
    value: 'UNSTART',
    label: this.i18n('未开始')
  }, {
    value: 'PROCESSING',
    label: this.i18n('进行中')
  }]
  $refs: any
  dialogShow: boolean = false;
  titleString: string = '';
  arrList: any = [];

  query: any = {
    nameLike: '', //活动名称
    numberEquals: '', //活动号
    topicNameLikes: '', //所属主题
    stateEquals: 'PROCESSING', //活动状态
    typeEquals: null  //活动类型
  }
  total: Total = {
    all: 0,
    initial: 0,
    audit: 0,
    doing: 0,
    end: 0,
    suspend: 0
  }
  selectDate: any = []  //活动时间
  tableData: ActivityBody[] = []
  activitiesInfo: UnionActivityItem[] = []
  activityList: string[] = [] //选中数据
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }

  get panelArray() {
    return [
      {
        name: this.i18n('页面管理'),
        url: ''
      }
    ]
  }

  get isEnglish() {
    return sessionStorage.getItem("locale") === "en_US";
  }

  doCreate() {
    this.$router.push({
      name: "page-manage-edit",
      query: {
        editModel: EditMode.create
      }
    });
  }

  open(ids: string[]) {
    this.doReset()
    this.dialogShow = true
    this.activityList = ids || []
    if (this.activityType === 'PointsExchangeCouponActivityRule') {
      this.titleString = this.i18n('积分兑换券')
    } else if (this.activityType === 'WeiXinAppletIssueCouponActivityRule') {
      this.titleString = this.i18n('小程序领微信券')
    } else if (this.activityType === 'MiniProgramGainCouponActivityRule') {
      this.titleString = this.i18n('小程序领券')
    } else if (this.activityType === 'AliAppletIssueCouponActivityRule') {
      this.titleString = this.i18n('小程序领支付宝券')
    }
  }

  canel() {
    this.dialogShow = false;
  }

  confirm() {
    this.dialogShow = false;
    this.$emit('submit', this.activityList);
  }

  getActivityTypes() {
    let arr: string[] = []
    this.activitiesInfo.forEach((item) => {
      if (item.viewAble) {
        arr.push(item.value)
      }
    })
    if (this.query.typeEquals) {
      arr = arr.filter((item) => item === this.query.typeEquals)
    }
    return arr
  }

  private getList() {
    const loading = CommonUtil.Loading()
    const params = new UnionActivityQuery()
    params.topicNameLikes = this.query.topicNameLikes ? this.query.topicNameLikes : null;
    params.nameLike = this.query.nameLike ? this.query.nameLike : null
    params.numberEquals = this.query.numberEquals ? this.query.numberEquals : null
    params.outerNumberIdLike = this.query.outerNumberIdLike ? this.query.outerNumberIdLike : null
    // 活动时间
    if (this.selectDate && this.selectDate.length > 0) {
      params.begin = this.selectDate[0];
      params.end = this.selectDate[1];
    } else {
      params.begin = null;
      params.end = null;
    }
    params.page = this.page.currentPage - 1;
    params.pageSize = this.page.size;
    params.stateEquals = this.query.stateEquals ? this.query.stateEquals : null
    let arr: any = []
    arr.push(this.activityType)
    params.typeIn = arr
    UnionActivityApi.queryByType(params).then((res) => {
      if (res.code === 2000) {
        this.tableData = res.data?.list || []
        this.page.total = res.total
        this.total.all = res.data?.summary?.sum || 0;
        this.total.initial = res.data?.summary?.initail || 0;
        this.total.audit = res.data?.summary?.unstart || 0;
        this.total.doing = res.data?.summary?.processing || 0;
        this.total.end = res.data?.summary?.stoped || 0;
        this.updateTableSelection();
      } else {
        throw new Error(res.msg || this.i18n('查询失败'))
      }
    }).catch((error) => {
      this.$message.error(error.message)
    }).finally(() => {
      loading.close()
    })
  }

  handleSelectionChange(val: any[], row: any) {
    const rowId = this.checkBodyToActivityId(row);
    const isSelected = val.some((item) => this.checkBodyToActivityId(item) === rowId);
    if (isSelected) {
      if (!this.activityList.includes(rowId)) {
        this.activityList.push(rowId);
      }
    } else {
      const index = this.activityList.indexOf(rowId);
      if (index > -1) {
        this.activityList.splice(index, 1);
      }
    }
  }

  handleSelectAll(selection: any[]) {
    const currentPageData = selection.filter((item) => {
      return this.tableData.find((val) => this.checkBodyToActivityId(val) === this.checkBodyToActivityId(item))
    })
    if (currentPageData.length) {
      currentPageData.forEach((item) => {
        this.activityList.push(this.checkBodyToActivityId(item))
      })
    } else {
      this.tableData.forEach((item) => {
        const id = this.checkBodyToActivityId(item)
        const targetIndex = this.activityList.indexOf(id)
        if (targetIndex > -1) {
          this.activityList.splice(targetIndex, 1)
        }
      })
    }
    this.activityList = [...new Set(this.activityList)]
  }

  // 获取不同结构下的activityId
  checkBodyToActivityId(obj: any) {
    if (obj.body) {
      return obj.body.activityId
    } else {
      return obj.activityId
    }
  }

  // 同步选中状态
  private updateTableSelection() {
    this.$nextTick(() => {
      this.tableData.forEach((item) => {
        const rowId = this.checkBodyToActivityId(item);
        if (this.activityList.includes(rowId)) {
          this.$refs.table.toggleRowSelection(item, true);
        }
      });
    });
  }

  /**
   * 查询
   */
  doSearch() {
    this.page.currentPage = 1
    this.getList()
  }

  /**
   * 重置
   */
  doReset() {
    this.page.currentPage = 1;
    this.query = {
      nameLike: '', //活动名称
      numberEquals: '', //活动号
      topicNameLikes: '', //所属主题
      stateEquals: 'PROCESSING', //活动状态
      typeEquals: null  //活动类型
    }
    this.getList()
  }

  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.getList()
  }

  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.getList()
  }

  /**
   * 编辑
   */
  doEdit(row: any) {
    this.$router.push({
      name: "page-manage-edit", query: {
        editId: row.id,
        editModel: EditMode.edit
      }
    });
  }

  /**
   * 发布
   */
  doPublish(row: any) {
    ContentTemplateApi.publish({ id: row.id }).then(res => {
      if (res && res.code === 2000) {
        this.$message.success(this.i18n('发布成功'))
        this.getList()
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  /**
   * 推广
   */
  doSpread(row: any) {
    // 推广逻辑
  }

  /**
   * 删除
   */
  doRemove(row: any) {
    this.$confirm(this.i18n('是否确认删除？'), this.i18n('提示'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消'),
      type: 'warning'
    }).then(() => {
      ContentTemplateApi.publish({ id: row.id }).then(res => {
        if (res && res.code === 2000) {
          this.$message.success(this.i18n('删除成功'))
          this.getList()
        }
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    });
  }
}
