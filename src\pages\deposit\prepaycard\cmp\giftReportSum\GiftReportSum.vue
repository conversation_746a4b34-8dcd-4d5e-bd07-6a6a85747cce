<template>
	<div class="gift-report-sum">
		<div class="all-total">
			<el-block-panel>
				<el-block-panel-item>
					<div class="height-80">
						<div class="amount-desc">
							<span>礼品卡总余额</span>
							<el-tooltip content="Bottom center" effect="light" placement="bottom">
								<i class="iconfont  ic-info" style="font-size: 18px;color: #20A0FF"></i>
								<template slot="content">
									<div>{{ i18n("礼品卡总余额=实充总余额+返现总余额") }}</div>
								</template>
							</el-tooltip>
						</div>
						<div class="amount">
							<span no-i18n>{{ summary.total | fmt }}</span
							>&nbsp;<span>元</span>
						</div>
					</div>
				</el-block-panel-item>
				<el-block-panel-item>
					<div class="height-80">
						<div class="amount-desc">
							<span>实充总余额</span>
							<el-tooltip content="Bottom center" effect="light" placement="bottom">
								<i class="iconfont  ic-info" style="font-size: 18px;"></i>
								<template slot="content">
									<div>{{ i18n("实充总余额=所有礼品卡（售价+消费冲账+调整）实充增加-（消费+调整）实充减少") }}</div>
								</template>
							</el-tooltip>
						</div>
						<div class="amount color-default">
							<span no-i18n>{{ summary.balance | fmt }}</span
							>&nbsp; <span>元</span>
						</div>
					</div>
				</el-block-panel-item>
				<el-block-panel-item>
					<div class="height-80">
						<div class="amount-desc">
							<span>返现总余额</span>
							<el-tooltip content="Bottom center" effect="light" placement="bottom">
								<i class="iconfont  ic-info" style="font-size: 18px;color: #20A0FF"></i>
								<template slot="content">
									<div>{{ i18n("返现总余额=所有礼品卡（售价+消费冲账+调整）实充增加-（消费+调整）实充减少") }}</div>
								</template>
							</el-tooltip>
						</div>
						<div class="amount">
							<span no-i18n>{{ summary.giftBalance | fmt }}</span
							>&nbsp;<span>元</span>
						</div>
					</div>
				</el-block-panel-item>
			</el-block-panel>
		</div>
	</div>
</template>
<script lang="ts" src="./GiftReportSum.ts"></script>

<style lang="scss">
.gift-report-sum {
	width: 100%;
	background-color: #f2f2f2;
	overflow: hidden;
    padding-bottom: 20px;

	.cur-tab {
		padding-left: 20px;
		padding-right: 20px;
		margin-top: 20px;
	}

	.all-total {
        background: #fff;
		.multy-account {
			margin-top: 20px;
			margin-left: 5px;

			.qf-form-label {
				width: 180px !important;
			}
		}

		.multy-account-en {
			margin-top: 20px;
			margin-left: 5px;

			.qf-form-label {
				width: 250px !important;
			}
		}

		.height-80 {
			height: 80px;

			.amount {
				font-size: 24px;
				font-weight: 500;
				display: block;
				margin-top: 10px;
			}

			.amount-desc {
				margin-top: 10px;
			}

			.color-default {
			}
		}
	}
}
</style>
