<template>
  <div class="prepay-card-adjust">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button size="large" type="primary" @click="doExport"
                   v-if="hasOptionPermission('/数据/门店结算账单/储值账单', '数据导出')">
          {{ i18n('导出') }}
        </el-button>
      </template>
    </BreadCrume>
      <ListWrapper class="current-page" style="height: 95%">
        <template slot="query">
          <el-row>
            <el-col :span="8">
              <form-item :label="i18n('账单日期')">
                <el-date-picker
                    :end-placeholder="formatI18n('/营销/券礼包活动/券礼包活动', '结束时间')"
                    format="yyyy-MM-dd"
                    range-separator="-"
                    ref="selectDate"
                    size="small"
                    :start-placeholder="formatI18n('/营销/券礼包活动/券礼包活动', '开始时间')"
                    type="daterange"
                    v-model="billDate"
                    value-format="yyyy-MM-dd">
                </el-date-picker>
              </form-item>
            </el-col>
            <el-col :span="8">
              <form-item :label="i18n('门店')">
                <SelectStores v-model="occurredOrg" @change="$forceUpdate()" :isOnlyId="false"
                              :hideAll="true" width="100%"
                              :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
                </SelectStores>
              </form-item>
            </el-col>
          </el-row>
          <el-row style="margin-top: 10px">
            <el-col :span="8">
              <form-item>
                <el-button @click="onSearch" type="primary">{{i18n('查询')}}</el-button>
                <el-button @click="onReset">{{i18n('重置')}}</el-button>
              </form-item>
            </el-col>
          </el-row>
        </template>
        <template slot="btn">
        </template>
        <template slot="list"style="width: 100%">
          <el-table
              :data="tableData"
              ref="table"
              border
              style="width: 100%;
              margin-top:10px"
              >
            <el-table-column :label="i18n('门店')" :width="getColumnWidth('occurredOrgId', 150)">
              <template slot-scope="scope">
                {{ showIdName(scope.row.occurredOrgId,scope.row.occurredOrgName) || '--'}}
              </template>
            </el-table-column>
            <el-table-column :label="i18n('账单日期')" :width="getColumnWidth('sourcePullTask', 150)">
              <template slot-scope="scope">
                {{ scope.row.settleTime || '--' }}
              </template>
            </el-table-column>
            <el-table-column :label="i18n('发生渠道')" :width="getColumnWidth('settleTime', 150)">
              <template slot-scope="scope">
                {{ showChannel(scope.row.channelId, scope.row.channelType) }}
              </template>
            </el-table-column>
            <el-table-column :label="i18n('发生时间')" :width="getColumnWidth('occurredTime', 150)">
              <template slot-scope="scope">
                {{ scope.row.occurredTime || '--' }}
              </template>
            </el-table-column>
            <el-table-column :label="i18n('业务类型')" :width="getColumnWidth('category', 150)">
              <template slot-scope="scope">
                {{ getCateGory(scope.row.category) }}
              </template>
            </el-table-column>
            <el-table-column :label="i18n('支付金额')" :width="getColumnWidth('deductionAmount', 150)">
              <template slot-scope="scope">
                {{ getNumber(scope.row.originalAmount) }}
              </template>
            </el-table-column>
            <el-table-column :label="i18n('本金支付金额')" :width="getColumnWidth('payOfCostAmount', 150)">
              <template slot-scope="scope">
                {{ getNumber(scope.row.amount) }}
              </template>
            </el-table-column>
            <el-table-column :label="i18n('赠送支付金额')" :width="getColumnWidth('favOfCostAmount', 150)">
              <template slot-scope="scope">
                {{ getNumber(scope.row.giftAmount) }}
              </template>
            </el-table-column>
            <el-table-column :label="i18n('优惠金额')" :width="getColumnWidth('serviceAmount', 150)">
              <template slot-scope="scope">
                {{ getNumber(scope.row.favAmount) }}
              </template>
            </el-table-column>
            <el-table-column :label="i18n('储值支付收入')" :width="getColumnWidth('settleAmount', 150)">
              <template slot-scope="scope">
                {{ getNumber(scope.row.totalAmount) }}
              </template>
            </el-table-column>
            <el-table-column :label="i18n('技术服务费')" :width="getColumnWidth('settleAmount', 150)">
              <template slot-scope="scope">
                {{ getNumber(scope.row.serviceAmount) }}
              </template>
            </el-table-column>
            <el-table-column :label="i18n('结算金额')" :width="getColumnWidth('settleAmount', 150)">
              <template slot-scope="scope">
                {{ getNumber(scope.row.settleAmount) }}
              </template>
            </el-table-column>
            <el-table-column :label="i18n('支付ID')" :width="getColumnWidth('transIdId', 150)">
              <template slot-scope="scope">
                {{ scope.row.transIdId || '--' }}
              </template>
            </el-table-column>
            <el-table-column :label="i18n('交易号')" :width="getColumnWidth('tradeNo', 150)">
              <template slot-scope="scope">
                {{ scope.row.tradeNo || '--' }}
              </template>
            </el-table-column>
          </el-table>
        </template>
        <template slot="page">
          <el-pagination :current-page="page.page" :page-size="page.pageSize"
                         :page-sizes="[10, 20, 30, 40]" :total="page.total"
                         @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange"
                         background
                         :layout="getPageLayout('total, prev, pager, next, sizes, jumper',page.probeEnabled)">
          </el-pagination>
        </template>
      </ListWrapper>
    <DownloadCenterDialog :dialogvisiable="fileDialogVisible" :showTip="showTip" @dialogClose="doDownloadDialogClose"></DownloadCenterDialog>

  </div>
</template>

<script lang="ts" src="./PrepaySettleBill.ts">
</script>

<style lang="scss" scoped>
.prepay-card-adjust{
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: hidden;
  .current-page{
    .el-select{
      width: 100%;
    }
  }

  .el-range-editor.el-input__inner{
    width: 100%;
  }
  .el-table__body .el-table__row td {
    border-bottom: 1px solid #d7dfeb !important;
  }
}
</style>