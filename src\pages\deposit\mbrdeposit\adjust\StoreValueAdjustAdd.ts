import { Component, Vue, Watch } from 'vue-property-decorator'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import FormItem from 'cmp/formitem/FormItem.vue'
import PrePayAdjustBillApi from 'http/prepay/adjustbill/PrePayAdjustBillApi'
import PrePayReasonFilter from 'model/prepay/adjustbill/PrePayReasonFilter'
import AdjustBillReason from 'model/prepay/adjustbill/AdjustBillReason'
import PrepayAdjustBill from 'model/prepay/adjustbill/PrepayAdjustBill'
import PrepayAdjustBillLine from 'model/prepay/adjustbill/PrepayAdjustBillLine'
import MemberAccount from 'model/prepay/adjustbill/MemberAccount'
import Account from 'model/prepay/adjustbill/Account'
import ConstantMgr from 'mgr/ConstantMgr'
import PrePayConfigApi from 'http/prepay/config/PrePayConfigApi'
import MutableNsid from 'model/common/MutableNsid'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import I18nPage from 'common/I18nDecorator'
import StoreValueAdjustPermission from 'pages/deposit/mbrdeposit/adjust/StoreValueAdjustPermission'
import RSOrg from "model/common/RSOrg";
import RSOrgFilter from "model/common/RSOrgFilter";
import OrgApi from "http/org/OrgApi";
import SysConfigApi from "http/config/SysConfigApi";
import SelectStores from 'cmp/selectStores/SelectStores'
import BPrepayAdjustBillOperatorRequest from 'model/default/BPrepayAdjustBillOperatorRequest'
import CommonUtil from 'util/CommonUtil'

@Component({
  name: 'StoreValueAdjustAdd',
  components: {
    SubHeader,
    FormItem,
    BreadCrume,
    SelectStores
  }
})
@I18nPage({
  prefix: [
    '/储值/会员储值/储值调整单/新建',
    '/公用/按钮',
    '/公用/过滤器'
  ]
})
export default class StoreValueAdjustAdd extends Vue {
  i18n: I18nFunc
  saveLoading = false
  permission = new StoreValueAdjustPermission()
  saveAndAuditLoading = false
  panelArray: any = []
  isDisabled = true
  switchFlag = false // 多储值账户开关
  member = ''
  timer = 0
  memberObj: MemberAccount = new MemberAccount()
  memberFlag = false
  memberFlagContent = ''
  reasons: AdjustBillReason[] = []
  saveParams: PrepayAdjustBill = new PrepayAdjustBill()
  accountTotalArray: number[] = []
  accountSelectArray: boolean[] = []
  noUse = ''
  showOrg = false // 控制模态框的展示
  hasOaPermission = false // 控制oa审批按钮的展示
  isMoreMarketing: boolean = true // 是否开启多营销中心 true 开启 false 不开启
  useMemberOwnerStore = false

  get getdefaultOccurTotal() {
    if (this.saveParams && this.saveParams.lines && this.saveParams.lines.length > 0) {
      return (Number(this.saveParams.lines[0].occurAmount) + Number(this.saveParams.lines[0].occurGiftAmount)).toFixed(2)
    }
    return ''
  }
  @Watch('member')
  onMmeberChange(value: string) {
    if (!value) {
      this.memberFlag = true
      this.memberFlagContent = this.i18n('会员不能为空')
    }
  }
  @Watch('memberObj.name')
  onMemberNameChange(value: string) {
    if (value) {
      this.memberFlag = false
    } else {
      this.memberFlag = true
      this.memberObj.name = ''
    }
  }
  created() {
    if (sessionStorage.getItem('isMultipleMC') == '1') {
      this.isMoreMarketing = true
    } else {
      this.isMoreMarketing = false
    }
    let account: Account = new Account()
    account.id = ''
    account.balance = 0
    account.giftBalance = 0
    this.memberObj.accounts.push(account)
    let line: PrepayAdjustBillLine = new PrepayAdjustBillLine()
    line.occurAmount = '' as any
    line.occurGiftAmount = '' as any
    line.lineNo = 1
    this.saveParams.lines.push(line)
    this.getPrePermission()
    this.getStoreValueAdjustReason()
    this.getConfig()
  }
  mounted() {
    this.panelArray = [
      {
        name: this.i18n('储值调整单'),
        url: 'store-value-adjust'
      },
      {
        name: this.i18n('新建储值调整单'),
        url: ''
      }
    ]
    this.panelArray[1].name = this.$route.query.from === 'edit' ? this.i18n('编辑储值调整单') : this.i18n('新建储值调整单')
  }
  doOccurAmountChange(index: number) {
    let regex = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/g
    let ownerAmont = Number(this.memberObj.accounts[index].balance)
    if (regex.test((this.saveParams.lines[index].occurAmount as any))) {
      if (Number(this.saveParams.lines[index].occurAmount) > 999999) {
        this.saveParams.lines[index].occurAmount = 999999 as any
        this.$forceUpdate()
      }
    } else {
      if (Number(this.saveParams.lines[index].occurAmount) < 0) {
        if (Math.abs(Number(this.saveParams.lines[index].occurAmount)) > ownerAmont) {
          this.saveParams.lines[index].occurAmount = -ownerAmont as any
        }
        this.$forceUpdate()
      }
    }
    this.saveParams.lines[index].occurAmount = (Number(this.saveParams.lines[index].occurAmount).toFixed(2)) as any
    this.$forceUpdate()
    if (this.saveParams.lines[index].occurGiftAmount) {
      this.accountTotalArray[index] = (Number(this.saveParams.lines[index].occurAmount) +
        Number(this.saveParams.lines[index].occurGiftAmount)).toFixed(2) as any
      this.$forceUpdate()
    }

  }
  doGiftOccurAmountChange(index: number) {
    let regex = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/g
    let ownerAmont = Number(this.memberObj.accounts[index].giftBalance)
    if (regex.test((this.saveParams.lines[index].occurGiftAmount as any))) {
      if (Number(this.saveParams.lines[index].occurGiftAmount) > 999999) {
        this.saveParams.lines[index].occurGiftAmount = 999999 as any
        this.$forceUpdate()
      }
    } else {
      if (Math.abs(Number(this.saveParams.lines[index].occurGiftAmount)) > ownerAmont) {
        this.saveParams.lines[index].occurGiftAmount = -ownerAmont as any
      }
      this.$forceUpdate()
    }
    this.saveParams.lines[index].occurGiftAmount = (Number(this.saveParams.lines[index].occurGiftAmount).toFixed(2)) as any
    this.$forceUpdate()
    if (this.saveParams.lines[index].occurAmount) {
      this.accountTotalArray[index] = (Number(this.saveParams.lines[index].occurAmount) + Number(this.saveParams.lines[index].occurGiftAmount)).toFixed(2) as any
      this.$forceUpdate()
    }
  }
  doBack() {
    this.$router.back()
  }
  doMemberChange() {
    if (!this.member) {
      this.isDisabled = true
      return
    }
    this.doQuerySearchAsync('')
  }
  async doSaveAndAudit() {
    if (!this.doValidate()) {
      return
    }
    this.saveAndAuditLoading = true
    if (this.$route.query.from === 'edit') {
      const loading = CommonUtil.Loading()
      try {
        const saveResp = await PrePayAdjustBillApi.saveModify(this.getParams());
        if (saveResp.code !== 2000) {
          throw new Error(saveResp?.msg || '保存失败');
        }
        const operatorRequest = new BPrepayAdjustBillOperatorRequest();
        operatorRequest.billNum = this.$route.query.id as string;
        const submitResp = await PrePayAdjustBillApi.submit(operatorRequest);
        if (submitResp.code !== 2000) {
          throw new Error(submitResp?.msg || '提交失败');
        }
        const auditResp = await PrePayAdjustBillApi.audit(this.$route.query.id as string);
        if (auditResp.code !== 2000) {
          throw new Error(auditResp?.msg || '审核失败');
        }
        this.$message.success(this.i18n('保存并审核成功'));
        this.$router.push({ name: 'store-value-adjust-dtl', query: { id: this.$route.query.id } });
      } catch (error: any) {
        this.$message.error(error.message || '操作失败');
      } finally {
        loading.close()
        this.saveAndAuditLoading = false;
      }
    } else {
      PrePayAdjustBillApi.saveAndAudit(this.getParams()).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.i18n('保存并审核成功'))
          this.$router.push({ name: 'store-value-adjust-dtl', query: { id: resp.data } })
        } else {
          throw new Error(resp.msg)
        }
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      }).finally(() => {
        this.saveAndAuditLoading = false
      })
    }
  }
  doSave() {
    if (!this.doValidate()) {
      return
    }
    this.saveLoading = true
    if (this.$route.query.from === 'edit') {
      PrePayAdjustBillApi.saveModify(this.getParams()).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.i18n('保存成功'))
          this.$router.push({ name: 'store-value-adjust-dtl', query: { id: this.$route.query.id } })
        } else {
          throw new Error(resp.msg)
        }
      }).catch((error) => {
        this.$message.error(error.message)
      }).finally(() => {
        this.saveLoading = false
      })
    } else {
      PrePayAdjustBillApi.save(this.getParams()).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.i18n('保存成功'))
          this.$router.push({ name: 'store-value-adjust-dtl', query: { id: resp.data } })
        } else {
          throw new Error(resp.msg)
        }
      }).catch((error) => {
        this.$message.error(error.message)
      }).finally(() => {
        this.saveLoading = false
      })
    }
  }

  doValidate() {
    if (!this.member) {
      this.memberFlag = true
      this.memberFlagContent = this.i18n('会员不能为空')
      return false
    }
    if (this.member && this.memberFlag) {
      return false
    }
    if (!this.tramsParams()) {
      this.$message.warning(this.i18n('请勾选账户'))
      return false
    }
    // let nullCount = 0
    let zeroCount = 0
    let onlyOneNull = 0
    if (this.saveParams && this.saveParams.lines && this.saveParams.lines.length > 0) {
      this.saveParams.lines.forEach((item: any, pos: number) => {
        const nullMap = [null, undefined, '']
        if (this.switchFlag) { // 多账户
          if (this.accountSelectArray[pos]) {
            // if (!Number(item.occurAmount) && !Number(item.occurGiftAmount)) {
            //   nullCount++
            // }
            if (!Number(item.occurAmount) && !Number(item.occurGiftAmount)) {
              zeroCount++
            }
            if (nullMap.includes(item.occurAmount) || nullMap.includes(item.occurGiftAmount)) {
              onlyOneNull++
            }
          }
        } else { // 单账户
          // if (!Number(item.occurAmount) && !Number(item.occurGiftAmount)) {
          //   nullCount++
          // }
          if (!Number(item.occurAmount) && !Number(item.occurGiftAmount)) {
            zeroCount++
          }
          if (nullMap.includes(item.occurAmount) || nullMap.includes(item.occurGiftAmount)) {
            onlyOneNull++
          }
        }
      })
    }
    // if (nullCount > 0) {
    //   this.$message.warning(this.i18n('实充调整和返现调整不能同时为空'))
    //   return false
    // }
    if (zeroCount > 0) {
      this.$message.warning(this.i18n('实充调整和返现调整不能同时为0'))
      return false
    }
    if (onlyOneNull > 0) {
      this.$message.warning(this.i18n('请填写实充调整和返现调整'))
      return false
    }
    if ((!this.saveParams.occurredOrg || !this.saveParams.occurredOrg.id) && this.showOrg) {
      this.$message.warning(this.formatI18n('/公用/查询条件/提示', '请选择发生组织'))
      return false
    }
    return true
  }
  doCancel() {
    this.$router.back()
  }
  doQuerySearchAsync(param: any) {
    PrePayAdjustBillApi.getMemberAccount(this.member).then((resp: any) => {
      if (resp && resp.code === 2000) {
        if (resp.data) {
          this.isDisabled = false
          this.memberObj = resp.data
          if (this.memberObj && this.memberObj.accounts && this.memberObj.accounts.length === 0) {
            let account: Account = new Account()
            account.balance = 0
            account.giftBalance = 0
            this.memberObj.accounts.push(account)
          }
          if (this.switchFlag) {
            this.saveParams.lines = []
            for (let i = 0; i < this.memberObj.accounts.length; i++) {
              this.accountSelectArray[i] = false
              let line: PrepayAdjustBillLine = new PrepayAdjustBillLine()
              // line.accountId = this.memberObj.memberId
              // line.accountName = this.memberObj.accounts[i].name
              line.accountId = this.memberObj.accounts[i].id
              line.accountName = this.memberObj.accounts[i].name
              line.occurAmount = '' as any
              line.occurGiftAmount = '' as any
              line.lineNo = (i + 1)
              this.saveParams.lines.push(line)
            }
          }
          if (this.memberObj && this.memberObj.memberId) {
            this.saveParams.lines.forEach((item: PrepayAdjustBillLine, index: number) => {
              if (!item.accountOwner) {
                item.accountOwner = new MutableNsid()
              }
              // item.accountId = this.memberObj.memberId
              item.accountOwner!.id = this.memberObj.memberId
              // item.accountName = this.memberObj.name
              item.oldAmount = this.memberObj.accounts[index].balance
              item.oldGiftAmount = this.memberObj.accounts[index].giftBalance
            })
          }
          if (this.memberObj && !this.memberObj.name) {
            this.memberObj.name = ''
          }
          this.$nextTick(() => {
            this.memberFlag = false
          })
        } else {
          this.memberFlag = true
          this.memberFlagContent = this.i18n('不存在该会员')
        }
        if (param && param.lines && param.lines.length > 0) {
          if (this.switchFlag) {
            for (let i = 0; i < param.lines.length; i++) {
              for (let j = 0; j < this.memberObj.accounts.length; j++) {
                if (param.lines[i].accountId === this.memberObj.accounts[j].id) {
                  this.accountSelectArray[j] = true
                  this.memberObj.accounts[j].balance = param.lines[i].oldAmount
                  this.memberObj.accounts[j].giftBalance = param.lines[i].oldGiftAmount
                  this.saveParams.lines[j] = param.lines[i]
                  this.saveParams.billNumber = param.billNumber
                  this.accountTotalArray[j] = Number(this.saveParams.lines[j].occurAmount) + Number(this.saveParams.lines[j].occurGiftAmount)
                }
              }
            }
          } else {
            this.saveParams.lines = param.lines
          }
          if (this.memberObj && this.memberObj.ownStore && this.saveParams.occurredOrg
            && this.saveParams.occurredOrg.id == this.memberObj.ownStore.id) {
            this.useMemberOwnerStore = true
          }
        }
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  private getStoreValueAdjustReason() {
    let filter: PrePayReasonFilter = new PrePayReasonFilter()
    filter.page = 0
    filter.pageSize = 0
    PrePayAdjustBillApi.listReason(filter).then((resp: any) => {
      if (resp && resp.data) {
        this.reasons = resp.data
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  private getPrePermission() {
    const loading = this.$loading(ConstantMgr.loadingOption)
    PrePayConfigApi.get().then((resp: any) => {
      if (resp && resp.code === 2000) {
        loading.close()
        if (resp.data && resp.data.enableMultipleAccount) { // 开启多账户
          this.switchFlag = resp.data.enableMultipleAccount
          this.getAccount()
        } else {
          this.switchFlag = false // 未开启多账户
          if (this.$route.query.id) {
            this.getModify()
          }
        }
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
      loading.close()
    })
  }
  private getParams() {
    if (this.switchFlag) {
      let params: any = JSON.parse(JSON.stringify(this.saveParams))
      params.lines = []
      this.accountSelectArray.forEach((item, index) => {
        if (item) {
          params.lines.push(this.saveParams.lines[index])
        }
      })
      return params
    } else {
      return this.saveParams
    }
  }
  private tramsParams() {
    let count = 0
    // let arr: any = JSON.parse(JSON.stringify(this.saveParams.lines))
    // let newArr: any = []
    if (this.switchFlag) { // 如果是多账户执行勾选操作
      // this.saveParams.lines = []
      this.accountSelectArray.forEach((item, index) => {
        if (!item) {
          count++
          // this.saveParams.lines.splice(index, 1)
        } else {
          // this.saveParams.lines.push(arr[index])
        }
      })
      if (count === this.accountSelectArray.length) {
        return false
      } else {
        return true
      }
    } else {
      return true
    }

  }
  private getModify() {
    const loading = CommonUtil.Loading()
    PrePayAdjustBillApi.getModify(this.$route.query.id as string).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.member = resp.data.mobile || resp.data.hdCardMbrId || resp.data.hdCardCardNum
        this.memberObj.name = resp.data.name
        this.saveParams.billNumber = resp.data.billNumber
        this.saveParams.occurredOrg = resp.data.occurredOrg
        this.doQuerySearchAsync(resp.data)
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    }).finally(() => {
      loading.close()
    })
  }
  private getAccount() {
    const loading = CommonUtil.Loading()
    PrePayAdjustBillApi.getAccount().then((resp: any) => {
      if (resp && resp.code === 2000) {
        if (resp.data && resp.data.length > 0) {
          this.saveParams.lines = []
          resp.data.forEach((item: Account, index: number) => {
            let line: PrepayAdjustBillLine = new PrepayAdjustBillLine()
            line.occurAmount = '' as any
            line.occurGiftAmount = '' as any
            line.lineNo = 1
            this.saveParams.lines.push(line)
            if (!this.memberObj.accounts[index]) {
              this.memberObj.accounts[index] = new Account()
            }
            this.memberObj.accounts[index].id = item.id
            this.memberObj.accounts[index].name = item.name
          })
        }
        if (this.$route.query.from === 'edit') {
          this.getModify()
        }
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    }).finally(() => {
      loading.close()
    })
  }

  private getConfig() {
    // 获取储值调整单oa配置
    PrePayAdjustBillApi.getOaConfig().then((resp) => {
      if (resp.code === 2000) {
        this.hasOaPermission = resp.data || false
      } else {
        throw new Error(resp.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    })

    SysConfigApi.get().then((resp: any) => {
      if (resp && resp.data) {
        this.showOrg = resp.data.enableMultiMarketingCenter
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private changeOrg() {
    if (this.useMemberOwnerStore) {
      if (!this.memberObj.ownStore || !this.memberObj.ownStore.id) {
        this.$message.error(this.formatI18n('/权益/积分/新建积分调整单/点击使用会员所属门店作为发生组织', '会员所属门店为空！'))
        this.useMemberOwnerStore = false
        return
      }
      this.saveParams.occurredOrg!.id = this.memberObj.ownStore.id;
      this.saveParams.occurredOrg!.name = this.memberObj.ownStore.name;
    }
  }

  // 保存并提交
  doSaveAndSubmit() {
    if (!this.doValidate()) {
      return
    }
    this.saveAndAuditLoading = true
    if (this.$route.query.from === 'edit') {
      PrePayAdjustBillApi.saveModify(this.getParams()).then((resp: any) => {
        if (resp && resp.code === 2000) {
          let operatorRequest: BPrepayAdjustBillOperatorRequest = new BPrepayAdjustBillOperatorRequest()
          operatorRequest.billNum = this.$route.query.id as string
          PrePayAdjustBillApi.submit(operatorRequest).then((resp: any) => {
            if (resp && resp.code === 2000) {
              this.$message.success(this.i18n('操作成功'))
              this.$router.push({ name: 'store-value-adjust-dtl', query: { id: this.$route.query.id } })
            } else {
              throw new Error(resp.msg)
            }
          }).catch((error) => {
            if (error && error.message) {
              this.$message.error(error.message)
            }
          })
        }
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      }).finally(() => {
        this.saveAndAuditLoading = false
      })
    } else {
      PrePayAdjustBillApi.saveAndSubmit(this.getParams()).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.i18n('操作成功'))
          this.$router.push({ name: 'store-value-adjust-dtl', query: { id: resp.data } })
        } else {
          throw new Error(resp.msg)
        }
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      }).finally(() => {
        this.saveAndAuditLoading = false
      })
    }
  }
}
