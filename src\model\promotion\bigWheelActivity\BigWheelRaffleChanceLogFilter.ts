export default class BigWheelRaffleChanceLogFilter {
  // uuid>
  uuidGreater: Nullable<string> = null
  // 活动号=
  activityNumberEquals: Nullable<string> = null
  // 活动号in
  activityNumberIn: string[] = []
  // 会员id=
  memberIdEquals: Nullable<string> = null
  // 发生门店id=
  occurredOrgIdEquals: Nullable<string> = null
  // 会员标识等于
  memberCodeEquals: Nullable<string> = null
  // 页数>=0
  page: number = 0
  // 页面大小>0
  pageSize: number = 0
}