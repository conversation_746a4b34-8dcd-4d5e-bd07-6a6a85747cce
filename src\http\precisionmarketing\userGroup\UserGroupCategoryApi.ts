import ApiClient from 'http/ApiClient'
import Response from 'model/default/Response'
import UserGroupCategory from 'model/precisionmarketing/userGroup/UserGroupCategory'
import UserGroupCategoryDeleteRequest from 'model/precisionmarketing/userGroup/UserGroupCategoryDeleteRequest'
import UserGroupCategoryFilter from 'model/precisionmarketing/userGroup/UserGroupCategoryFilter'
import UserGroupCategoryModifyRequest from 'model/precisionmarketing/userGroup/UserGroupCategoryModifyRequest'
import UserGroupCategorySaveRequest from 'model/precisionmarketing/userGroup/UserGroupCategorySaveRequest'
import UserGroupCategorySortRequest from 'model/precisionmarketing/userGroup/UserGroupCategorySortRequest'

export default class UserGroupCategoryApi {
  /**
   * 删除客群分类
   * 
   */
  static delete(body: UserGroupCategoryDeleteRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v2/precision-marketing/user-group/category/delete`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询客群分类
   * 
   */
  static query(body: UserGroupCategoryFilter): Promise<Response<UserGroupCategory[]>> {
    return ApiClient.server().post(`/v2/precision-marketing/user-group/category/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 编辑客群分类
   * 
   */
  static saveModify(body: UserGroupCategoryModifyRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v2/precision-marketing/user-group/category/saveModify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新增客群分类
   * 
   */
  static saveNew(body: UserGroupCategorySaveRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v2/precision-marketing/user-group/category/saveNew`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 客群分类排序
   * 
   */
  static sort(body: UserGroupCategorySortRequest): Promise<Response<void[]>> {
    return ApiClient.server().post(`/v2/precision-marketing/user-group/category/sort`, body, {
    }).then((res) => {
      return res.data
    })
  }
}
