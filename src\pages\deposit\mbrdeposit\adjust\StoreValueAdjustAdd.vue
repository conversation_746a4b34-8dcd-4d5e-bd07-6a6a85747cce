<template>
  <div class="store-value-adjust-add">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button v-if="hasOptionPermission('/储值/储值管理/储值调整单', '单据维护')" :loading="saveLoading" @click="doSave" type="primary">
          保存
        </el-button>
        <el-button v-if="hasOptionPermission('/储值/储值管理/储值调整单', '单据审核') && !hasOaPermission" :loading="saveAndAuditLoading" @click="doSaveAndAudit">
          保存并审核
        </el-button>
        <el-button v-if="hasOptionPermission('/储值/储值管理/储值调整单', '单据维护') && hasOptionPermission('/储值/储值管理/储值调整单', '单据提交') && !hasOaPermission"
          :loading="saveAndAuditLoading" @click="doSaveAndSubmit">
          保存并提交
        </el-button>
        <el-button @click="doCancel">取消</el-button>
      </template>
    </BreadCrume>
    <div class="content">
      <FormItem label="会员">
        <el-input @change="doMemberChange" class="width-298" v-model="member" :disabled="!hasOptionPermission('/储值/储值管理/储值调整单', '单据维护')"></el-input>
        <div style="color: red" v-show="memberFlag">{{memberFlagContent}}</div>
      </FormItem>
      <FormItem label="会员姓名">
        <el-input class="width-298" disabled v-model="memberObj.name"></el-input>
      </FormItem>
      <FormItem label="储值调整" :required="true">
        <div class="inner-content" v-if="!switchFlag">
          <div style="line-height: 36px">
            <span v-if="isDisabled">
              <i18n k="/储值/会员储值/储值调整单/新建/当前储值余额{0}元=实充余额{1}元+返现余额{2}元">
                <template slot="0">&nbsp;<span class="weight">-</span>&nbsp;</template>
                <template slot="1">&nbsp;<span class="weight">-</span>&nbsp;</template>
                <template slot="2">&nbsp;<span class="weight">-</span>&nbsp;</template>
              </i18n>
            </span>
            <span v-if="!isDisabled">
              <i18n k="/储值/会员储值/储值调整单/新建/当前储值余额{0}元=实充余额{1}元+返现余额{2}元">
                <template slot="0">&nbsp;<span
                    class="weight">{{(memberObj.accounts[0].balance + memberObj.accounts[0].giftBalance) | fmt}}</span>&nbsp;</template>
                <template slot="1">&nbsp;<span class="weight">{{memberObj.accounts[0].balance | fmt}}</span>&nbsp;</template>
                <template slot="2">&nbsp;<span class="weight">{{memberObj.accounts[0].giftBalance | fmt}}</span>&nbsp;</template>
              </i18n>
            </span>
          </div>
          <FormItem label="储值调整">
            <div style="line-height: 36px">
              <i18n k="/储值/会员储值/储值调整单/新建/实充调整{0}元+返现调整{1}元=余额调整{2}元">
                <template slot="0">
                  &nbsp;<el-input :disabled="isDisabled" @change="doOccurAmountChange(0)" class="width-78"
                    v-model="saveParams.lines[0].occurAmount"></el-input>&nbsp;
                </template>
                <template slot="1">
                  &nbsp;<el-input :disabled="isDisabled" @change="doGiftOccurAmountChange(0)" class="width-78"
                    v-model="saveParams.lines[0].occurGiftAmount"></el-input>&nbsp;
                </template>
                <template slot="2">
                  &nbsp;<el-input disabled class="width-78" v-model="getdefaultOccurTotal"></el-input>&nbsp;
                </template>
              </i18n>
            </div>
          </FormItem>
          <FormItem>
            <div> <i class="iconfont  ic-info" style="font-size: 18px;color: #20A0FF"></i>&nbsp;<span>输入正数表示增加余额，负数表示扣减余额，支持输入两位小数</span></div>
            <div style="padding-left: 23px">如：实充输入50，表示实充增加50元；实充输入-50，表示实充扣减50元
            </div>
            <div style="padding-left: 23px">扣减余额不得大于当前余额</div>
          </FormItem>
          <FormItem label="储值调整原因">
            <el-select :disabled="isDisabled" class="width-298" placeholder="请选择" v-model="saveParams.lines[0].reason">
              <el-option no-i18n :label="item.content" :value="item.content" :key="item.content" v-for="item in reasons">{{item.content}}</el-option>
            </el-select>
          </FormItem>
          <FormItem v-if="showOrg" :label="formatI18n('/权益/积分/新建积分调整单/条目', '发生组织')">
            <SelectStores v-model="saveParams.occurredOrg.id" :disabled="isDisabled || useMemberOwnerStore" :isOnlyId="true" :hideAll="true"
              width="298px" :appendAttr="{orgTypeEquals: 'PHX'}" :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
            </SelectStores>
          </FormItem>
          <FormItem v-if="showOrg">
            <el-checkbox v-if="!isMoreMarketing" :disabled="isDisabled" @change="changeOrg" v-model="useMemberOwnerStore">
              {{formatI18n('/权益/积分/新建积分调整单/条目', '使用会员所属门店作为发生组织')}}
            </el-checkbox>
          </FormItem>
          <FormItem label="说明">
            <el-input :disabled="isDisabled" class="width-298" maxlength="200" type="textarea" v-model="saveParams.lines[0].remark"></el-input>
          </FormItem>
        </div>
        <!--多账户-->
        <div v-if="switchFlag">
          <div class="inner-content" v-for="(item, index) in memberObj.accounts" :key="index">
            <div style="line-height: 36px;display:flex">
              <el-checkbox :disabled="isDisabled" v-model="accountSelectArray[index]"></el-checkbox>
              <span class="weight">[{{item.id}}]{{item.name}}</span>
              <span v-if="isDisabled">
                <i18n k="/储值/会员储值/储值调整单/新建/当前储值余额{0}元=实充余额{1}元+返现余额{2}元">
                  <template slot="0">&nbsp;<span class="weight">-</span>&nbsp;</template>
                  <template slot="1">&nbsp;<span class="weight">-</span>&nbsp;</template>
                  <template slot="2">&nbsp;<span class="weight">-</span>&nbsp;</template>
                </i18n>
              </span>
              <span v-if="!isDisabled">
                <i18n k="/储值/会员储值/储值调整单/新建/当前储值余额{0}元=实充余额{1}元+返现余额{2}元">
                  <template slot="0">&nbsp;<span class="weight">{{(item.balance + item.giftBalance) | fmt}}</span>&nbsp;</template>
                  <template slot="1">&nbsp;<span class="weight"><span
                        class="weight">{{memberObj.accounts[index].balance | fmt}}</span></span>&nbsp;</template>
                  <template slot="2">&nbsp;<span class="weight">{{memberObj.accounts[index].giftBalance | fmt}}</span>&nbsp;</template>
                </i18n>
              </span>
            </div>
            <div style="padding-bottom: 20px" v-if="accountSelectArray[index]">
              <FormItem label="储值调整">
                <div style="line-height: 36px">
                  <div style="line-height: 36px">
                    <i18n k="/储值/会员储值/储值调整单/新建/实充调整{0}元+返现调整{1}元=余额调整{2}元">
                      <template slot="0">
                        &nbsp;<el-input :disabled="isDisabled" @change="doOccurAmountChange(index)" class="width-78"
                          v-if="!(saveParams.lines && saveParams.lines[index])"></el-input>
                        <el-input :disabled="isDisabled" @change="doOccurAmountChange(index)" class="width-78"
                          v-if="(saveParams.lines && saveParams.lines[index])" v-model="saveParams.lines[index].occurAmount"></el-input>&nbsp;
                      </template>
                      <template slot="1">&nbsp;
                        <el-input :disabled="isDisabled" @change="doGiftOccurAmountChange(index)" class="width-78"
                          v-if="!(saveParams.lines && saveParams.lines[index])"></el-input>
                        <el-input :disabled="isDisabled" @change="doGiftOccurAmountChange(index)" class="width-78"
                          v-if="(saveParams.lines && saveParams.lines[index])" v-model="saveParams.lines[index].occurGiftAmount"></el-input>
                        &nbsp;
                      </template>
                      <template slot="2">&nbsp;
                        <el-input class="width-78" disabled v-model="accountTotalArray[index]"></el-input>&nbsp;
                      </template>
                    </i18n>
                  </div>
                </div>
              </FormItem>
              <FormItem>
                <div> <i class="iconfont  ic-info" style="font-size: 18px;color: #20A0FF"></i>&nbsp;<span>输入正数表示增加余额，负数表示扣减余额，支持输入两位小数</span></div>
                <div style="padding-left: 23px">如：实充输入50，表示实充增加50元；实充输入-50，表示实充扣减50元</div>
                <div style="padding-left: 23px">扣减余额不得大于当前余额</div>
              </FormItem>
              <FormItem label="储值调整原因">
                <el-select :disabled="isDisabled" class="width-298" placeholder="请选择" v-if="!(saveParams.lines && saveParams.lines[index])"
                  v-model="noUse">
                  <el-option label="" value=""></el-option>
                </el-select>
                <el-select :disabled="isDisabled" class="width-298" placeholder="请选择" v-if="(saveParams.lines && saveParams.lines[index])"
                  v-model="saveParams.lines[index].reason">
                  <el-option no-i18n :label="item.content" :value="item.content" :key="item.content"
                    v-for="item in reasons">{{item.content}}</el-option>
                </el-select>
              </FormItem>
              <FormItem v-if="showOrg" :label="formatI18n('/权益/积分/新建积分调整单/条目', '发生组织')">
                <SelectStores v-model="saveParams.occurredOrg.id" :disabled="isDisabled || useMemberOwnerStore" :isOnlyId="true" :hideAll="true"
                  width="298px" :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
                </SelectStores>
              </FormItem>
              <FormItem v-if="showOrg">
                <el-checkbox v-if="!isMoreMarketing" :disabled="isDisabled" @change="changeOrg" v-model="useMemberOwnerStore">
                  {{formatI18n('/权益/积分/新建积分调整单/条目', '使用会员所属门店作为发生组织')}}
                </el-checkbox>
              </FormItem>
              <FormItem label="说明">
                <el-input :disabled="isDisabled" class="width-298" type="textarea" v-if="!(saveParams.lines && saveParams.lines[index])"></el-input>
                <el-input :disabled="isDisabled" class="width-298" type="textarea" v-if="saveParams.lines && saveParams.lines[index]"
                  v-model="saveParams.lines[index].remark"></el-input>
              </FormItem>
            </div>
          </div>
        </div>

      </FormItem>
      <FormItem>

      </FormItem>

    </div>
  </div>
</template>

<script lang="ts" src="./StoreValueAdjustAdd.ts">
</script>

<style lang="scss">
.store-value-adjust-add {
  width: 100%;
  height: 100%;
  background-color: white;
  overflow: auto;
  .content {
    padding-top: 30px;
    padding-left: 30px;
  }
  .width-78 {
    width: 78px;
  }
  .width-298 {
    width: 298px;
  }
  .el-textarea__inner {
    height: 100px;
  }
  .inner-content {
    background-color: rgba(249, 249, 249, 1);
    padding-left: 20px;
    margin: 20px 20px 10px 0;
    height: auto;
    &:nth-child(n + 2) {
      margin-top: -7px;
    }
  }
  .el-checkbox {
    margin-right: 10px;
  }
  .weight {
    font-weight: 600;
    color: #515151;
  }
  .qf-form-item .qf-form-content {
    position: relative;
  }
}
</style>