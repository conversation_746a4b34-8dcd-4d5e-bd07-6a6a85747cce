import { Component, Vue } from 'vue-property-decorator'
import BreadCrume from "cmp/bread-crumb/BreadCrume";
import ListWrapper from 'cmp/list/ListWrapper.vue'
import FloatBlock from 'cmp/floatblock/FloatBlock.vue'
import UploadFileModal from "pages/datum/area/UploadFileModal";
import IdName from 'model/common/IdName'
import ZoneApi from 'http/area/ZoneApi';
import SaveBatchZoneRequest from 'model/datum/zone/SaveBatchZoneRequest'
import Zone from 'model/datum/zone/Zone'
import ZoneFilter from 'model/datum/zone/ZoneFilter'
import BrowserMgr from 'mgr/BrowserMgr';

@Component({
  name: 'Area',
  components: {
    BreadCrume,
    ListWrapper,
    FloatBlock,
    UploadFileModal
  }
})

export default class Area extends Vue {
  $refs:any
  areaCode: string = '' // 区域代码
  areaName: string = '' // 区域名称
  panelArray: any = []
  marketingCenters: IdName = new IdName()
  loading: boolean = false
  checkedAll: boolean = false
  areaData: any = []
  selected: any = []
  marketCenter:any = []
  marketCenterName:any = []
  areaStateSelect:any = 'null' // 区域状态选择
  areaCodeOrName:any = null // 区域代码或名称
  ZoneFilter:ZoneFilter = new ZoneFilter()
  SaveBatchZoneRequest:SaveBatchZoneRequest = new SaveBatchZoneRequest()
  modifyLoading = false // dialog确认loading
  updateDialog: boolean = false // 修改Dialog
  isModification: boolean = false // false 作废 true 修改
  enableMultiMarketingCenter:boolean = true // 是否开启多营销中心
  isMoreMarketing: boolean = false // 是否开启多营销中心 true 开启 false 不开启
  visible: boolean = false
  rowState: any = ''
  page: any = {
    currentPage: 0,
    pageSize: 10,
    total: 0
  }
  query: any = {
    marketingCenterIdEquals: '',
    idNameLikes: ''
  }
  updateIns: any = {
    areaCode: null,
    areaName: null
  }
  created() {
    let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
    if (sysConfig) {
      this.isMoreMarketing = sysConfig.enableMultiMarketingCenter;
    }
    const id = sessionStorage.getItem('marketCenter') || null
    const name = sessionStorage.getItem('marketCenterName') || null
    this.marketingCenters = {id: id, name: name}
    if(sessionStorage.getItem('isMultipleMC') == '1') {
      this.enableMultiMarketingCenter = true
    } else {
      this.enableMultiMarketingCenter = false
    }

    this.panelArray = [
      {
        name: this.formatI18n('/公用/菜单/区域'),
        url: ''
      },
    ]
    this.marketCenter = sessionStorage.getItem("marketCenter")
    this.marketCenterName = sessionStorage.getItem("marketCenterName")
    this.getList()
  }
  // 添加
  add() {
    if(!this.areaCode) {
      this.$message.error(this.formatI18n('/资料/区域/请输入区域代码后再添加'))
      return
    }
    if (!this.areaName) {
      this.$message.error(this.formatI18n('/资料/区域/请输入区域名称后再添加'))
      return
    }
    let zones:any = {}
    zones.marketingCenter = sessionStorage.getItem("marketCenter") || undefined
    zones.zone = {id : this.areaCode, name : this.areaName}
    this.SaveBatchZoneRequest.zones = []
    this.SaveBatchZoneRequest.zones.push(zones)
    let addFiter = new ZoneFilter()
    addFiter.zoneIdEquals = this.areaCode
    ZoneApi.isExistZone(addFiter).then((res:any)=>{
      if(res.code === 2000) {
        if (res.data && res.data.length > 0) {
          this.$message.error(this.formatI18n('/资料/区域/区域已存在'))
          return
        } else {
          ZoneApi.saveBatch(this.SaveBatchZoneRequest).then((res)=>{
            if(res.code === 2000) {
              this.$message.success(this.formatI18n('/资料/门店/添加成功'))
              this.areaCode = ''
              this.areaName = ''
              this.getList()
            }
          }).catch((error) => {
            if (error && error.message) {
              this.$message.error(error.message)
            }
          })  
        }
      } else {
        this.$message.error(res.msg)
      }
    })
    .catch((error)=>{
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  // 清空
  clear() {
    this.areaCode = ''
    this.areaName = ''
  }

  private getList() {
    this.loading = true
    this.ZoneFilter.page = this.page.currentPage - 1
    this.ZoneFilter.pageSize = this.page.pageSize
    this.ZoneFilter.sorters = { lastModifyInfotime: 'desc' }
    ZoneApi.query(this.ZoneFilter).then((res)=>{
      if(res.code === 2000) {
        this.areaData = res.data
        this.page.total = res.total
      }else {
        this.$message.error(res.msg as string)
      }
      this.loading = false
    }).catch(()=>{
      this.loading = false
    })
  }
  /**
   * 修改
   */
  showModifyDialog(row:any) {
    this.isModification = true
    this.updateDialog = true
    this.rowState = row.state
    this.updateIns.areaCode = row.zone.id
    this.updateIns.areaName = row.zone.name
  }

  /**
   * 作废
   */
  cancellationDialog(row:any) {
    this.isModification = false
    this.updateDialog = true
    this.updateIns.areaCode = row.zone.id
    this.updateIns.areaName = row.zone.name
  }


  /**
   * 区域信息查询
   */
  private checkedAllRow() {
    if (this.checkedAll) {
      for (let row of this.areaData) {
        this.$refs.areaTable.toggleRowSelection(row, true)
      }
    } else {
      this.$refs.areaTable.clearSelection();
    }
  }

  /**
  * 分页页码改变的回调
  * @param val
  */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.getList()
  }

  /**
  * 每页多少条的回调
  * @param val
  */
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.getList()
  }

  /**
  * 查询
  */
  onSearch() {
    this.ZoneFilter.idOrNameLikes = this.areaCodeOrName
    this.page.currentPage = 0
    this.getList()
  }

  handleSelectionChange(val: any) {
    this.selected = val
  }

  doSearch() {
    this.page.currentPage = 0
    if(this.areaStateSelect === 'null') {
      this.ZoneFilter.stateEquals = null
    } else {
      this.ZoneFilter.stateEquals = this.areaStateSelect
    }
    this.getList()
  }

  /**
   * dialog 确认
   */
  modify() {
    if(this.isModification) {
      let zones:any = {}
      if (this.rowState === 'CANCEL') {
        zones.state = 'CANCEL'
      }
      zones.marketingCenter = sessionStorage.getItem("marketCenter") || undefined
      zones.zone = {id : this.updateIns.areaCode, name : this.updateIns.areaName}
      this.SaveBatchZoneRequest.zones = []
      this.SaveBatchZoneRequest.zones.push(zones)
      ZoneApi.saveBatch(this.SaveBatchZoneRequest).then((res)=>{
        if(res.code === 2000) {
          this.$message.success(this.formatI18n('/资料/门店/修改成功'))
          this.updateDialog = false
          this.getList()
        }
      })
    } else {
        let zones:any = {}
        zones.marketingCenter = sessionStorage.getItem("marketCenter") || undefined
        zones.state = 'CANCEL'
        zones.zone = {id : this.updateIns.areaCode, name : this.updateIns.areaName}
        this.SaveBatchZoneRequest.zones = []
        this.SaveBatchZoneRequest.zones.push(zones)
        ZoneApi.saveBatch(this.SaveBatchZoneRequest).then((res)=>{
          if(res.code === 2000) {
            this.$message({
              type: 'success',
              message: this.formatI18n('/储值/预付卡/预付卡查询/列表页面/作废成功')
            });
            this.updateDialog = false
            this.getList()
          }
        })
    }
  }

  /**
   * 
   */
}