// 权益卡流水查询
export default class BenefitCardTransactionQueryRequest {
  // 模版类型，paid-付费卡 free-免费卡
  templateTypeEquals: Nullable<string> = null
  // 会员识别码等于
  identCodeEquals: Nullable<string> = null
  // 会员id
  memberIdEquals: Nullable<string> = null
  // 发生组织等于
  occurOrgIdEquals: Nullable<string> = null
  // 交易号等于
  transNoEquals: Nullable<string> = null
  // 交易号类似于
  transNoLikes: Nullable<string> = null
  // 交易时间位于
  occurTimeBetweenClosedOpen: Date[] = []
  // 交易时间范围起始
  occurTimeBegin: Nullable<Date> = null
  // 交易时间范围结束
  occurTimeEnd: Nullable<Date> = null
  // 营销中心等于
  marketingCenterEquals: Nullable<string> = null
  // 区域
  occurZoneIdEquals: Nullable<string> = null
  // 排序，key表示排序的字段，可选值：occurredTime、created；value表示排序方向，可选值为：asc, desc
  sorters: any
  // 
  page: Nullable<number> = null
  // 
  pageSize: Nullable<number> = null
}