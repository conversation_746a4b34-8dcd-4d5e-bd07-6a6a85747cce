import ApiClient from 'http/ApiClient'
import PointsActivityRule from 'model/points/init/PointsActivityRule'
import Response from 'model/common/Response'

export default class PointsInitializeApi {
  /**
   * 首页积分初始化首页查询规则信息
   *
   */
  static getRules(): Promise<Response<PointsActivityRule>> {
    return ApiClient.server().get(`/v1/points-initialize/getRules`, {}).then((res) => {
      return res.data
    })
  }

}
