<template>
  <el-dialog :title="title" :visible.sync="visible" width="800px" :close-on-click-modal="false">
    <div class="share-info-dialog">
      <div class="left-form">
        <el-form :model="formData" ref="form" label-width="100px" :rules="rules">
          <el-form-item :label="i18n('页面分享')" prop="isShare">
            <el-radio-group v-model="formData.isShare">
              <el-radio :label="true">{{i18n('支持')}}</el-radio>
              <el-radio :label="false">{{i18n('不支持')}}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="i18n('分享标题')">
            <el-row class="gray-tips">- {{i18n('若不设置，默认为该页面名称')}}</el-row>
            <el-input maxlength="26" v-model="formData.title" :placeholder="i18n('若不设置，默认为该页面名称')" style="width: 230px"></el-input>
          </el-form-item>
          <el-form-item :label="i18n('分享图片')">
            <el-row class="gray-tips" style="line-height: 20px;margin-top:10px">
              {{i18n('/公用/券模板/图片比例支持{0}（建议尺寸{1}像素），支持{2}，大小不超过{3}',['5:4','500*400','jpg/jpeg/png','300KB'])}}
            </el-row>
            <UploadImg v-model="formData.image" :maximum="300" style="margin-top:12px"></UploadImg>
          </el-form-item>
        </el-form>
      </div>
      <div class="right-view">
        <el-image :src="require('@/assets/image/fellow/bg_share_window.png')" class="view-bg-img" />
        <el-image :src="require('@/assets/image/fellow/bg_share_avatar.png')" class="view-avatar" />
        <el-image :src="require('@/assets/image/fellow/bg_share_app.png')" class="view-notice" />
        <el-image :src="require('@/assets/image/fellow/share_coupon.png')" class="view-icon" />
        <div class="view-app-name">{{i18n('会员小程序')}}</div>
        <div class="view-title">{{formData.title}}</div>
        <el-image v-if="formData.image" :src="formData.image" class="view-image" />
        <div v-else style="background: #EEEEF1" class="view-image"></div>
      </div>
      <div class="btn-block">
        <el-button @click="close">{{i18n('取消')}}</el-button>
        <el-button type="primary" @click="doConfirm">{{i18n('确定')}}</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./ShareConfigDialog.ts">
</script>

<style lang="scss" scoped>
.share-info-dialog {
  display: flex;
  justify-content: space-between;
  padding-bottom: 40px;
  .left-form {
    flex: 1;
  }
  .right-view {
    position: relative;
    width: 346px;
    height: 484px;
    margin-left: 30px;
    .view-bg-img {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      bottom: 0;
      z-index: 1;
    }
    .view-avatar {
      position: absolute;
      top: 97px;
      right: 12px;
      width: 38px;
      height: 38px;
      z-index: 2;
    }
    .view-notice {
      position: absolute;
      top: 96px;
      right: 56px;
      width: 220px;
      height: 248px;
      z-index: 2;
    }
    .view-icon {
      position: absolute;
      top: 111px;
      left: 80px;
      width: 18.5px;
      height: 18.5px;
      z-index: 2;
    }
    .view-app-name {
      position: absolute;
      top: 111px;
      left: 102px;
      z-index: 2;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 11px;
      color: #65686f;
      line-height: 19px;
    }
    .view-title {
      position: absolute;
      top: 132px;
      left: 80px;
      width: 200px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 15px;
      color: #202124;
      z-index: 2;
    }
    .view-image {
      position: absolute;
      top: 159px;
      left: 80px;
      width: 194px;
      height: 155px;
      z-index: 2;
    }
  }
  .btn-block {
    position: absolute;
    bottom: 10px;
    right: 20px;
  }
}
</style>