/*
 * @Author: 黎钰龙
 * @Date: 2023-08-15 18:09:54
 * @LastEditTime: 2023-08-24 14:34:50
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\promotion\CalendarActivityApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import CalendarActivity from 'model/promotion/CalendarActivity'
import CalendarActivityFilter from 'model/promotion/CalendarActivityFilter'
import Response from 'model/common/Response'
import ConsumeCalendarActivity from 'model/promotion/ConsumeCalendarActivity'
import ConsumeCalendarTakePartRecord from 'model/promotion/ConsumeCalendarTakePartRecord'
import ConsumeCalendarTakePartRecordFilter from 'model/promotion/ConsumeCalendarTakePartRecordFilter'

export default class CalendarActivityApi {
  /**
   * 消费日历详情
   * 消费日历详情。
   * 
   */
  static getConsumeCalendarActivity(id: string): Promise<Response<ConsumeCalendarActivity>> {
    return ApiClient.server().get(`/v1/calendar-activity/getConsumeCalendarActivity/${id}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量导入参与会员信息
   * 批量导入参与会员信息。
   * 
   */
  static importTakePartMember(body: any, activityNumber: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/calendar-activity/importTakePartMember`, body, {
      params: {
        activityNumber: activityNumber
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量导入第三方兑换码
   * 批量导入第三方兑换码。
   * 
   */
  static importThirdCouponCode(body: any, activityNumber: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/calendar-activity/importThirdCouponCode`, body, {
      params: {
        activityNumber: activityNumber
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建或修改消费日历活动
   * 新建或修改消费日历活动
   * 
   */
  static saveConsumeCalendarActivity(body: ConsumeCalendarActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/calendar-activity/saveConsumeCalendarActivity`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询消费日历活动号
   * 查询消费日历活动号。
   * 
   */
  static get(): Promise<Response<string>> {
    return ApiClient.server().get(`/v1/calendar-activity/get`, {
    }).then((res) => {
      return res.data
    })
  }
  /**
   * 获取参与人群导入数量
   * 获取参与人群导入数量。
   * @id 活动号
   */
  static getTakePartMemberCount(id: string): Promise<Response<number>> {
    return ApiClient.server().get(`/v1/calendar-activity/getTakePartMemberCount/${id}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取外部券码导入数量
   * 获取外部券码导入数量。
   * @id 活动号
   */
  static getThirdCouponCodeCount(id: string): Promise<Response<number>> {
    return ApiClient.server().get(`/v1/calendar-activity/getThirdCouponCodeCount/${id}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 消费日历参与记录列表查询
   * 消费日历参与记录列表查询
   * 
   */
  static queryTakePartRecord(body: ConsumeCalendarTakePartRecordFilter): Promise<Response<ConsumeCalendarTakePartRecord[]>> {
    return ApiClient.server().post(`/v1/calendar-activity/queryTakePartRecord`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量导出
   * 批量导出。
   * 
   */
  static export(body: ConsumeCalendarTakePartRecordFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/calendar-activity/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  
  /**
   * 查询会员的第三方券码
   * 查询会员的第三方券码
   * 
   */
  static queryThirdCodes(memberId: string): Promise<Response<string[]>> {
    return ApiClient.server().post(`/v1/calendar-activity/queryThirdCodes`, {}, {
      params: {
        memberId: memberId
      }
    }).then((res) => {
      return res.data
    })
  }
    /**
   * 日历活动活动查询
   * 日历活动活动查询
   * 
   */
    static search(body: CalendarActivityFilter): Promise<Response<CalendarActivity[]>> {
      return ApiClient.server().post(`/v1/calendar-activity/search`, body, {
      }).then((res) => {
        return res.data
      })
    }
}
