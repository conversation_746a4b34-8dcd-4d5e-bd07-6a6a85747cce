import { Component, Vue } from "vue-property-decorator";
import ListWrapper from "cmp/list/ListWrapper.vue";
import SubHeader from "cmp/subheader/SubHeader.vue";
import FloatBlock from "cmp/floatblock/FloatBlock.vue";
import GiftCardActivityFilter from "model/card/activity/GiftCardActivityFilter";
import GiftCardActivity from "model/card/activity/GiftCardActivity";
import ActivityStateCountResult from "model/common/ActivityStateCountResult";
import DateUtil from "util/DateUtil";
import DataUtil from "../common/DataUtil";
import I18nPage from "common/I18nDecorator";
import ActivityState from "cmp/activitystate/ActivityState";
import BreadCrume from "cmp/bread-crumb/BreadCrume.vue";
import PrepayCardTplPermission from "../prepaycardtpl/PrepayCardTplPermission";
import FormItem from "cmp/formitem/FormItem";
import MyQueryCmp from "cmp/querycondition/MyQueryCmp";
import EntityCardActivityApi from "http/card/activity/EntityCardActivityApi";

@Component({
  name: "EntityCardActivityList",
  components: {
    ListWrapper,
    SubHeader,
    FloatBlock,
    ActivityState,
    BreadCrume,
    FormItem,
    MyQueryCmp
  },
})
@I18nPage({
  prefix: [
    "/储值/预付卡/电子礼品卡活动/列表页面",
    "/公用/活动/状态",
    "/公用/活动/活动信息",
    "/公用/活动/提示信息",
    "/公用/按钮",
    '/储值/预付卡/卡模板/列表页面',
    "/卡/卡活动/实体卡售卡活动"
  ],
})
export default class EntityCardActivityList extends Vue {
  i18n: (str: string, params?: string[]) => string;
  query: GiftCardActivityFilter = new GiftCardActivityFilter();
  queryData: GiftCardActivity[] = [];
  countResult: ActivityStateCountResult = new ActivityStateCountResult();
  tabName: string = "ALL";
  $refs: any;
  selected: GiftCardActivity[] = [];
  prepayCardTplPermission = new PrepayCardTplPermission();
  dataUtil: DataUtil = new DataUtil();
  panelArray: any = [];
  tableLoading: boolean = false
  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10,
  };

  get allTab() {
    return `${this.i18n("全部")}(${this.countResult.sum})`;
  }

  get initialTab() {
    return `${this.i18n("未审核")}(${this.countResult.initail})`;
  }

  get unstartTab() {
    return `${this.i18n("未开始")}(${this.countResult.unstart})`;
  }

  get processingTab() {
    return `${this.i18n("进行中")}(${this.countResult.processing})`;
  }

  get stopedTab() {
    return `${this.i18n("已结束")}(${this.countResult.stoped})`;
  }

  created() {
    this.panelArray = [
      {
        name: this.i18n("实体卡售卡活动"),
        url: "",
      },
    ];
    this.getList();
  }

  doReset() {
    this.query = new GiftCardActivityFilter();
    this.tabName = "ALL";
    this.page.currentPage = 1;
    this.getList();
  }

  /**
   * 查询
   */
  onSearch() {
    this.page.currentPage = 1;
    this.getList();
  }

  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val;
    this.getList();
  }

  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val;
    this.getList();
  }

  getList() {
    this.query.page = this.page.currentPage - 1;
    this.query.pageSize = this.page.size;
    this.tableLoading = true
    EntityCardActivityApi.query(this.query)
      .then((resp: any) => {
        this.tableLoading = false
        if (resp && resp.code === 2000) {
          this.countResult = resp.data.countResult;
          this.queryData = resp.data.result;
          this.page.total = resp.data.total;
        } else {
          throw new Error(resp.msg || this.i18n('查询失败'))
        }
      })
      .catch((error) => {
        this.tableLoading = false
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
  }

  add() {
    this.$router.push({ name: "entity-card-activity-edit" });
  }

  gotoDtl(row: any) {
    this.$router.push({ name: "entity-card-activity-dtl", query: { activityId: row.body.activityId } });
  }

  del(activityId: string) {
    this.$alert(this.i18n("确认要删除吗？"), this.i18n("提示"), {
      confirmButtonText: this.i18n("确定"),
      cancelButtonText: this.i18n("取消"),
      type: "warning",
      callback: (action) => {
        if (action === "confirm") {
          EntityCardActivityApi.remove(activityId)
            .then((resp: any) => {
              if (resp && resp.code === 2000) {
                this.$message.success(this.i18n("删除成功"));
                this.getList();
              } else {
                throw new Error(resp.msg || this.i18n('操作失败'))
              }
            })
            .catch((error) => {
              if (error && error.message) {
                this.$message.error(error.message);
              }
            });
        }
      },
    });
  }

  delBatch() {
    if (this.selectedActivityIdList.length === 0) {
      this.$message.error(this.i18n("请先勾选要删除的记录"));
      return;
    }
    this.$alert(this.i18n("确认要删除吗？"), this.i18n("提示"), {
      confirmButtonText: this.i18n("确定"),
      cancelButtonText: this.i18n("取消"),
      type: "warning",
      callback: (action) => {
        if (action === "confirm") {
          EntityCardActivityApi.batchRemove(this.selectedActivityIdList as any)
            .then((resp: any) => {
              if (resp && resp.code === 2000) {
                this.$message.success(resp.data);
                this.getList();
              } else {
                throw new Error(resp.msg || this.i18n('操作失败'))
              }
            })
            .catch((error) => {
              if (error && error.message) {
                this.$message.error(error.message);
              }
            });
        }
      },
    });
  }

  audit(activityId: string) {
    this.$alert(this.i18n("确认要审核吗？"), this.i18n("提示"), {
      confirmButtonText: this.i18n("确定"),
      cancelButtonText: this.i18n("取消"),
      type: "warning",
      callback: (action) => {
        if (action === "confirm") {
          EntityCardActivityApi.audit(activityId)
            .then((resp: any) => {
              if (resp && resp.code === 2000) {
                this.$message.success(this.i18n("审核成功"));
                this.getList();
              } else {
                throw new Error(resp.msg || this.i18n('操作失败'))
              }
            })
            .catch((error) => {
              if (error && error.message) {
                this.$message.error(error.message);
              }
            });
        }
      },
    });
  }

  auditBatch() {
    if (this.selectedActivityIdList.length === 0) {
      this.$message.error(this.i18n("请先勾选要审核的记录"));
      return;
    }
    this.$alert(this.i18n("确认要审核吗？"), this.i18n("提示"), {
      confirmButtonText: this.i18n("确定"),
      cancelButtonText: this.i18n("取消"),
      type: "warning",
      callback: (action) => {
        if (action === "confirm") {
          EntityCardActivityApi.batchAudit(this.selectedActivityIdList as any)
            .then((resp: any) => {
              if (resp && resp.code === 2000) {
                this.$message.success(resp.data);
                this.getList();
              } else {
                throw new Error(resp.msg || this.i18n('操作失败'))
              }
            })
            .catch((error) => {
              if (error && error.message) {
                this.$message.error(error.message);
              }
            });
        }
      },
    });
  }

  stop(activityId: string) {
    this.$alert(this.i18n("确认要停止吗？"), this.i18n("提示"), {
      confirmButtonText: this.i18n("确定"),
      cancelButtonText: this.i18n("取消"),
      type: "warning",
      callback: (action) => {
        if (action === "confirm") {
          EntityCardActivityApi.stop(activityId)
            .then((resp: any) => {
              if (resp && resp.code === 2000) {
                this.$message.success(this.i18n("停止成功"));
                this.getList();
              } else {
                throw new Error(resp.msg || this.i18n('操作失败'))
              }
            })
            .catch((error) => {
              if (error && error.message) {
                this.$message.error(error.message);
              }
            });
        }
      },
    });
  }

  stopBatch() {
    if (this.selectedActivityIdList.length === 0) {
      this.$message.error(this.i18n("请先勾选要停止的记录"));
      return;
    }
    this.$alert(this.i18n("确认要停止吗？"), this.i18n("提示"), {
      confirmButtonText: this.i18n("确定"),
      cancelButtonText: this.i18n("取消"),
      type: "warning",
      callback: (action) => {
        if (action === "confirm") {
          EntityCardActivityApi.batchStop(this.selectedActivityIdList as any)
            .then((resp: any) => {
              if (resp && resp.code === 2000) {
                this.$message.success(resp.data);
                this.getList();
              } else {
                throw new Error(resp.msg || this.i18n('操作失败'))
              }
            })
            .catch((error) => {
              if (error && error.message) {
                this.$message.error(error.message);
              }
            });
        }
      },
    });
  }

  copy(activityId: string) {
    this.$router.push({ name: "entity-card-activity-edit", query: { activityId: activityId, editType: "复制" } });
  }

  edit(activityId: string) {
    this.$router.push({ name: "entity-card-activity-edit", query: { activityId: activityId, editType: "修改" } });
  }

  handleSelectionChange(val: any) {
    this.selected = val;
  }

  get selectedActivityIdList() {
    return this.selected.map((e) => (e.body ? e.body.activityId : null));
  }

  activityTime(row: any) {
    return `${DateUtil.format(row.body.beginDate, "yyyy-MM-dd")}${this.i18n("至")}${DateUtil.format(row.body.endDate, "yyyy-MM-dd")}`;
  }

  handleTabClick(tab: any, event: any) {
    this.query.stateEquals = tab.name === "ALL" ? null : tab.name;
    this.getList();
  }

  gotoCardTplDtl(num: string) {
    const route = this.$router.resolve({
      name: 'prepay-card-tpl-dtl',
      query: {
        number: num
      }
    })
    window.open(route.href, '_blank')
  }
}
