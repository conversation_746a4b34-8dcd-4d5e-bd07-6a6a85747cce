import I18nPage from 'common/I18nDecorator';
import CustomerProfileApi from 'http/analysis/CustomerProfileApi';
import TagCategoryV2Api from 'http/tagv2/TagCategoryV2Api';
import TagTemplateV2Api from 'http/tagv2/TagTemplateV2Api';
import BCustomerProfileDimension from 'model/analysis/BCustomerProfileDimension';
import BCustomerProfileTarget from 'model/analysis/BCustomerProfileTarget';
import MemberAnalysisCondition from 'model/default/MemberAnalysisCondition';
import BMemberPropFilter from 'model/analysis/BMemberPropFilter';
import { CustomerProfileCategory } from 'model/analysis/CustomerProfileCategory';
import { CustomerProfileType } from 'model/analysis/CustomerProfileType';
import { TargetType } from 'model/analysis/TargetType';
import IdName from 'model/common/IdName';
import { TagTypeEnum } from 'model/common/TagTypeEnum';
import TagCategoryFilterV2 from 'model/precisionmarketing/tagv2/TagCategoryFilterV2';
import TagCategoryV2 from 'model/precisionmarketing/tagv2/TagCategoryV2';
import TagTemplateFilterV2 from 'model/precisionmarketing/tagv2/TagTemplateFilterV2';
import TagTemplateV2 from 'model/precisionmarketing/tagv2/TagTemplateV2';
import { Component, Prop, Vue } from 'vue-property-decorator';
import CustomRangeDialog from '../CustomRangeDialog/CustomRangeDialog';
import BMemberItem from 'model/analysis/BMemberItem';
import { DateGroupType } from 'model/analysis/DateGroupType';
import { AnalysisPropType } from 'model/default/AnalysisPropType';

class TagItem extends TagCategoryV2 {
  children: TagTemplateV2[] = []
  isFold: boolean = true
  loading: boolean = false
}

@Component({
  name: 'PortraitIndicatorSelect',
  components: {
  }
})
@I18nPage({
  prefix: [
    '/会员/智能打标',
    '/公用/券模板',
    '/数据/客群画像'
  ],
  auto: true
})
export default class PortraitIndicatorSelect extends Vue {
  $refs: any
  popVisible: boolean = false
  @Prop() type: 'target' | 'dimension' | 'condition'  // 类型：指标/维度
  @Prop() where: 'customerProfile' | 'vipAnalysis'  // 使用位置：默认客群分析  会员分析
  @Prop({ type: Boolean, default: false }) isShowMemberCount: boolean;  //是否展示用户数
  filterValue: string = ''  //输入框的过滤条件
  tagFilter: Nullable<'memberAttr' | 'memberTag' | 'memberCount'> = null //快速选择类型
  tagList: TagItem[] = [] //会员标签列表
  attrList: BMemberItem[] = [] //用户属性列表
  searchLoading: boolean = false

  get title() {
    return this.type === 'target' ? `+${this.i18n('添加指标')}` : this.type === 'dimension' ? `+${this.i18n('添加维度')}` : `+${this.i18n('/数据/数据洞察/详情页/添加筛选条件')}`
  }

  // 是否展示用户属性列表
  get showAttrList() {
    return (!this.tagFilter || this.tagFilter === 'memberAttr') && this.attrList?.length
  }

  // 是否展示会员标签列表
  get showTagList() {
    return (!this.tagFilter || this.tagFilter === 'memberTag') && this.tagList?.length
  }

  // 是否展示用户数
  get showMemberLabel() {
    return this.isShowMemberCount && (!this.tagFilter || this.tagFilter === 'memberCount')
  }

  created() {
    this.queryMemberAttr()
    this.doQueryTagCategorize()
    console.log('创建完成', this.type, this.where)
  }

  // 过滤画像属性
  doFilter(type: 'memberAttr' | 'memberTag' | 'memberCount') {
    this.tagFilter = this.tagFilter === type ? null : type
  }

  // 查询用户属性
  queryMemberAttr() {
    const params = new BMemberPropFilter()
    return CustomerProfileApi.queryMemberProp(params).then((res) => {
      if (res.code === 2000) {
        this.attrList = (this.type === 'target' ? res.data?.filter((item) => item.type === CustomerProfileType.num) : res.data) || []
      } else {
        throw new Error(res.msg!)
      }
    }).catch((err) => {
      this.$message.error(err.message)
    })
  }

  // 查询标签分类列表
  doQueryTagCategorize() {
    const params = new TagCategoryFilterV2()
    TagCategoryV2Api.query(params).then((res) => {
      if (res.code === 2000) {
        this.tagList = res.data?.map((item) => {
          return {
            ...item,
            children: [],
            isFold: true,
            loading: false
          } as TagItem
        }) || []
      } else {
        throw new Error(res.msg!)
      }
    }).catch((err) => {
      this.$message.error(err.message)
    })
  }

  // 查询某一标签分类下的模板
  doQueryTagTemplate(id: Nullable<string>, nameLikes?: string) {
    const params = new TagTemplateFilterV2()
    params.categoryIdEquals = id || null
    params.nameLikes = nameLikes || null
    return TagTemplateV2Api.query(params).then((res) => {
      if (res.code === 2000) {
        if (nameLikes) {
          // 如果是根据模板名称查询的，则根据查出来的数据整理标签分类
          this.doResetCategory(res.data || [])
        } else {
          this.tagList.forEach((item) => {
            if (item.uuid === id) {
              item.children = (this.type === 'target' ? res.data?.filter((item) => item.tagType === TagTypeEnum.number) : res.data) || []
            }
          })
        }
      } else {
        throw new Error(res.msg!)
      }
    }).catch((err) => {
      this.$message.error(err.message)
    })
  }

  // 根据查询的标签模板 整理分类
  doResetCategory(templateList: TagTemplateV2[]) {
    this.tagList = []
    templateList.forEach((item) => {
      const parentIndex = this.tagList.findIndex((val) => val.uuid === item.category)
      if (parentIndex > -1) {
        // 当前分类已存在
        this.tagList[parentIndex].children.push(item)
      } else {
        // 分类不存在 手动创建
        const categoryItem = new TagItem()
        categoryItem.uuid = item.category
        categoryItem.name = item.categoryName
        categoryItem.isFold = false
        categoryItem.children.push(item)
        this.tagList.push(categoryItem)
      }
    })
  }

  // 输入查询某个标签模板
  async doSearch() {
    if (this.filterValue) {
      this.searchLoading = true
      await this.doQueryTagTemplate(null, this.filterValue)
      this.attrList = this.attrList.filter((item) => item.name?.includes(this.filterValue))
      this.searchLoading = false
    }
  }

  searchBlur() {
    if (!this.filterValue) {
      this.doQueryTagCategorize()
      this.queryMemberAttr()
    }
  }

  // 展开标签分类下的模板
  async doUnfold(id: string, index: number) {
    if (!this.tagList[index].children.length) {
      this.tagList[index].loading = true
      await this.doQueryTagTemplate(id)
      this.tagList[index].loading = false
    }
    this.tagList[index].isFold = false
  }

  doFold(index: number) {
    this.tagList[index].isFold = true
    this.$forceUpdate()
  }

  // 选择了标签 总和/均值/最大值/最小值
  doSelectTagChild(value: TargetType, tagItem: TagTemplateV2) {
    // 指标
    const res = new BCustomerProfileTarget()
    res.category = CustomerProfileCategory.tag
    res.tagId = tagItem.uuid
    res.tagName = tagItem.name
    res.type = value
    this.doSubmit(res)
  }

  // 选择了标签
  selectTag(tagItem: TagTemplateV2) {
    let res
    console.log('选择标签')
    if (this.type === 'target') {
      // 指标
      res = new BCustomerProfileTarget()
      res.category = CustomerProfileCategory.tag
      res.tagId = tagItem.uuid
      res.tagName = tagItem.name
    } else if (this.type === 'dimension') {
      // 维度
      res = new BCustomerProfileDimension()
      res.category = CustomerProfileCategory.tag
      res.tagId = tagItem.uuid
      res.tagName = tagItem.name
      res.type = tagItem.tagType === TagTypeEnum.date ? CustomerProfileType.date : tagItem.tagType === TagTypeEnum.number ? CustomerProfileType.num : CustomerProfileType.other
      // if (res.type === CustomerProfileType.date) {
      //   res.dateGroupType = DateGroupType.year
      // }
    } else if (this.type === 'condition') {
      // 筛选条件
      res = new MemberAnalysisCondition()
      res.tagId = tagItem.uuid
      res.tagName = tagItem.name
      if(tagItem.tagType === TagTypeEnum.number) res.fieldType = 'num'
      else if(tagItem.tagType === TagTypeEnum.date) res.fieldType = 'date'
      else res.fieldType = 'other'
      console.log('选择筛选条件，子组件传递tagItem', tagItem)
    }
    this.doSubmit(res)
  }

  // 选择了用户属性
  selectAttr(item: BMemberItem) {
    let res
    if (this.type === 'target') {
      // 指标
      res = new BCustomerProfileTarget()
      res.category = CustomerProfileCategory.attribute
      res.memberProp = item.id
      res.memberPropName = item.name
    } else if (this.type === 'dimension') {
      // 维度
      res = new BCustomerProfileDimension()
      res.category = CustomerProfileCategory.attribute
      res.type = item.type
      res.memberProp = item.id
      res.memberPropName = item.name
      console.log('选择维度，子组件传递item', item)
      // if (res.type === CustomerProfileType.date) {
      //   res.dateGroupType = DateGroupType.year
      // }
    } else if (this.type === 'condition') {
      // 筛选条件
      res = new MemberAnalysisCondition()
      res.memberPropName = item.name
      res.memberProp = item.id
      res.fieldType = item.type
      console.log('选择筛选条件，子组件传递item', res, item)
    }
    this.doSubmit(res)
  }

  // 选择了用户属性 总和/均值/最大值/最小值
  doSelectAttrChild(value: TargetType, attrItem: BMemberItem) {
    // 指标
    const res = new BCustomerProfileTarget()
    res.category = CustomerProfileCategory.attribute
    res.memberProp = attrItem.id
    res.memberPropName = attrItem.name
    res.type = value
    this.doSubmit(res)
  }

  // 选择了用户数
  selectMemberCount() {
    const res = null
    this.doSubmit(res)
  }

  // 提交数据
  doSubmit(res: any) {
    console.log(res)
    this.$emit('submit', res)
    this.popVisible = false
    console.log('提交成功,弹窗关闭')
    this.doReset()
  }

  close() {
    this.popVisible = false
  }

  // 提交后 需要初始化弹窗内部的数据
  doReset() {
    this.filterValue = ''
    this.tagFilter = null
    this.doQueryTagCategorize()
    this.queryMemberAttr()
  }
};