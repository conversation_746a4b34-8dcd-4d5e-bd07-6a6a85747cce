<!--
 * @Author: 黎钰龙
 * @Date: 2023-09-27 14:24:23
 * @LastEditTime: 2024-08-28 15:13:34
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\prepaycard\produce-card\ProduceCardList.vue
 * 记得注释
-->
<template>
  <div class="produce-card-list-container">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button size="large" type="primary" @click="doCreate" v-if="hasOptionPermission('/卡/卡管理/制卡单', '单据维护')">
          {{ i18n('新建制卡单') }}
        </el-button>
      </template>
    </BreadCrume>
    <div class="setting-container">
      <MyQueryCmp @reset="onReset" @search="onSearch">
        <el-row>
          <el-col :span="8">
            <form-item :label="i18n('制卡单号')">
              <el-input :placeholder="i18n('请输入制卡单号')" v-model="query.billNumberEquals"></el-input>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="i18n('状态')">
              <el-select placeholder="不限" v-model="query.stateEquals" style="width:100%">
                <el-option :label="i18n('全部')" :value="null"></el-option>
                <el-option :label="i18n('未审核')" value="INITIAL"></el-option>
                <el-option :label="i18n('已审核')" value="AUDITED"></el-option>
                <el-option :label="i18n('制卡中')" value="MAKING"></el-option>
                <el-option :label="i18n('已制卡')" value="FINISH"></el-option>
                <el-option :label="i18n('已作废')" value="CANCELED"></el-option>
              </el-select>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="i18n('卡模板号')">
              <el-input :placeholder="i18n('请输入卡模板号')" v-model="query.cardTemplateNumberEquals"></el-input>
            </form-item>
          </el-col>
        </el-row>
        <template slot="opened">
          <el-row>
            <el-col :span="8">
              <form-item :label="i18n('卡模板名称')">
                <el-input :placeholder="i18n('类似于')" v-model="query.cardTemplateNameLikes"></el-input>
              </form-item>
            </el-col>
            <el-col :span="8">
              <form-item :label="i18n('创建时间')">
                <el-date-picker style="width: 100%" v-model="query.createdBetweenClosedClosed" type="daterange" range-separator="-"
                  :picker-options="pickerOptions" start-placeholder="开始日期" end-placeholder="结束日期">
                </el-date-picker>
              </form-item>
            </el-col>
            <el-col :span="8">
              <form-item :label="i18n('最后修改时间')">
                <el-date-picker style="width: 100%" v-model="query.lastModifiedBetweenClosedClosed" type="daterange" range-separator="-"
                  :picker-options="pickerLastModifyOptions" start-placeholder="开始日期" end-placeholder="结束日期">
                </el-date-picker>
              </form-item>
            </el-col>
          </el-row>
        </template>
      </MyQueryCmp>
      <div style="margin:12px 0">
        <i18n k="/卡/卡管理/卡回收单/共选中{0}行">
          <span slot="0" class="select-num">{{selected.length}}</span>
        </i18n>
        <el-button style="margin-left:12px" v-if="hasOptionPermission('/卡/卡管理/制卡单', '单据审核')" @click="batchAudit">
          {{i18n('批量审核')}}
        </el-button>
      </div>
      <el-table :data="tableData" ref="table" row-key="billNumber" style="width: 100%;margin-top:12px" @selection-change="handleSelectionChange"
        fixed>
        <el-table-column type="selection" width="55" reserve-selection>
        </el-table-column>
        <el-table-column :label="i18n('制卡单号')" width="200" fixed>
          <template slot-scope="scope">
            <span class="span-btn" @click="goDtl(scope.row.billNumber)">{{scope.row.billNumber ||'-'}}</span>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('状态')" width="200">
          <template slot-scope="scope">
            <div style="display:flex;align-items:center">
              <span class="dot" :style="{background: computeState(scope.row.state).color}"></span>
              <span>{{computeState(scope.row.state).state}}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('卡模板号/名称')" width="180">
          <template slot-scope="scope">
            <p style="color: rgba(51, 51, 51, 0.647058823529412);">{{scope.row.cardTemplateNumber}}</p>
            <span class="span-btn" :title="scope.row.cardTemplateName" @click="goCardDtl(scope.row.cardTemplateNumber)">
              {{scope.row.cardTemplateName}}
            </span>
          </template>
        </el-table-column>
        <el-table-column width="180">
          <template slot="header">
            {{ i18n("制卡数量") }}
            <el-tooltip class="item" effect="dark" :content="i18n('制卡单据设置的需要制卡数量')" placement="top">
              <i class="iconfont  ic-info icon-tip" />
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <span>{{scope.row.makeQty || 0}}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column :label="i18n('已制卡数量')" width="180">
          <template slot-scope="scope">
            {{scope.row.makeQty || 0}}
          </template>
        </el-table-column> -->
        <el-table-column :label="i18n('创建时间')" width="200">
          <template slot-scope="scope">
            {{scope.row.created | dateFormate3}}
          </template>
        </el-table-column>
        <el-table-column :label="i18n('最后修改时间')" width="200">
          <template slot-scope="scope">
            {{scope.row.lastModified | dateFormate3}}
          </template>
        </el-table-column>
        <el-table-column label="创建人" prop="created" width="140">
          <template slot-scope="scope">
            <div
              v-if="scope.row.logs!==null && scope.row.logs.length === 1 && (scope.row.logs[0].type==='新建信息' || scope.row.logs[0].type==='Create')">
              {{ scope.row.logs[0].operator }}</div>
            <div v-if="scope.row.logs!==null && scope.row.logs.length > 1">
              <div v-for="(item,index) in scope.row.logs" :key="index">
                <div v-if="item.type==='新建信息' || item.type==='Create'">
                  {{ item.operator || '--' }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('操作')" width="200">
          <template slot-scope="scope">
            <div>
              <template v-if="['INITIAL'].indexOf(scope.row.state) > -1">
                <span class="span-btn" v-if="hasOptionPermission('/卡/卡管理/制卡单', '单据审核')" @click="doAudit(scope.row.billNumber)">
                  {{i18n('审核')}}
                </span>
                <span class="span-btn" v-if="hasOptionPermission('/卡/卡管理/制卡单', '单据维护')" @click="doEdit(scope.row.billNumber)">
                  {{i18n('修改')}}
                </span>
                <span class="span-btn" v-if="hasOptionPermission('/卡/卡管理/制卡单', '单据维护')" @click="doRemove(scope.row.billNumber)">
                  {{i18n('删除')}}
                </span>
              </template>
              <span class="span-btn" v-if="hasOptionPermission('/卡/卡管理/制卡单', '导出卡密') && scope.row.state === 'AUDITED'"
                @click="exportCodePwd(scope.row)">
                {{i18n('导出卡号密码')}}
              </span>
              <span class="span-btn" v-if="hasOptionPermission('/卡/卡管理/制卡单', '查看文件密码') && scope.row.state === 'AUDITED' && scope.row.exportFinish"
                @click="viewPwd(scope.row.billNumber)">
                {{i18n('查看加密密码')}}
              </span>
              <span class="span-btn" v-if="hasOptionPermission('/卡/卡管理/制卡单', '制卡完成') && scope.row.state === 'AUDITED' "
                @click="doFinishCard(scope.row)">
                {{i18n('制卡完成')}}
              </span>
              <span class="span-btn" v-if="hasOptionPermission('/卡/卡管理/制卡单', '单据维护') && scope.row.state === 'AUDITED'"
                @click="doCancel(scope.row.billNumber)">
                {{i18n('作废')}}
              </span>
              <span class="span-btn" v-if="isShowLogs(scope.row)" @click="doViewLogs(scope.row.logs)">
                {{i18n('操作日志')}}
              </span>
              <!-- <span class="span-btn">{{i18n('重新生成全部卡号密码')}}</span> -->
              <!-- <span class="span-btn">{{i18n('重新生成剩余卡号密码')}}</span> -->
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!--分页栏-->
      <el-pagination :current-page="page.page" :page-size="page.pageSize" :page-sizes="[10, 20, 30, 40]" :total="page.total"
        @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background layout="total, prev, pager, next, sizes,  jumper">
      </el-pagination>
    </div>
    <DownloadCenterDialog :dialogvisiable="fileDialogVisible" :showTip="showTip" @dialogClose="doDownloadDialogClose"></DownloadCenterDialog>
    <el-dialog :title="i18n('操作日志')" :visible.sync="logsDialogVisible">
      <el-table :data="logsData" style="width: 100%" height="500">
        <el-table-column :label="i18n('操作时间')">
          <template slot-scope="scope">
            <span>{{scope.row.occurredTime | dateFormate3}}</span>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('操作人')">
          <template slot-scope="scope">
            <span>{{scope.row.operator || '--'}}</span>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('操作')">
          <template slot-scope="scope">
            <span>{{scope.row.type || '--'}}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script lang="ts" src="./ProduceCardList.ts">
</script>

<style lang="scss" scoped>
.produce-card-list-container {
  width: 100%;

  .dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin: 0 4px 2px;
  }
  .span-btn {
    margin-right: 8px;
  }

  ::v-deep .el-range__icon {
    line-height: 26px;
  }

  ::v-deep .el-range-separator {
    line-height: 26px;
  }

  ::v-deep .el-range__close-icon {
    line-height: 26px;
  }
}
</style>