<template>
    <div class="prepay-card-adjust">
        <BreadCrume :panelArray="panelArray">
            <template slot="operate">
                <el-button v-if="hasOptionPermission('/卡/卡管理/预付卡调整单', '单据维护')" @click="doStoreValueAdd" size="small" type="primary">新建预付卡调整单</el-button>
                <el-button v-if="hasOptionPermission('/卡/卡管理/预付卡调整单', '单据维护')" @click="doBatchImport" size="small" type="primary">批量导入</el-button>
                <el-button v-if="hasOptionPermission('/卡/卡管理/预付卡调整单', '调整原因设置')" @click="doStoreValueReason" size="small">预付卡调整原因设置</el-button>
            </template>
        </BreadCrume>
        <ListWrapper class="current-page" style="height: 95%">
            <template slot="query">
                <el-row>
                    <el-col :span="8">
                        <form-item label="单号">
                            <el-input v-model="query.billNumberEquals" :placeholder="i18n('请输入单号')"></el-input>
                        </form-item>
                    </el-col>
                    <el-col :span="8">
                        <form-item label="卡号">
                            <el-input v-model="query.codeEquals" :placeholder="i18n('请输入卡号')"></el-input>
                        </form-item>
                    </el-col>
                    <el-col :span="8">
                        <form-item label="创建时间">
                            <el-date-picker
                                    end-placeholder="结束日期"
                                    format="yyyy-MM-dd"
                                    range-separator="-"
                                    ref="selectDate"
                                    size="small"
                                    start-placeholder="开始日期"
                                    type="daterange"
                                    v-model="createdDate"
                                    value-format="yyyy-MM-dd">
                            </el-date-picker>
                        </form-item>
                    </el-col>
                </el-row>
                <el-row style="margin-top: 10px">
                    <el-col :span="8">
                        <form-item label="最后修改时间">
                            <el-date-picker
                                    end-placeholder="结束日期"
                                    format="yyyy-MM-dd"
                                    range-separator="-"
                                    ref="selectDate"
                                    size="small"
                                    start-placeholder="开始日期"
                                    type="daterange"
                                    v-model="lastModifyDate"
                                    value-format="yyyy-MM-dd">
                            </el-date-picker>
                        </form-item>
                    </el-col>
                </el-row>
                <el-row style="margin-top: 10px">
                    <el-col :span="8">
                        <form-item>
                            <el-button @click="doSearch" type="primary">查询</el-button>
                            <el-button @click="doReset">重置</el-button>
                        </form-item>
                    </el-col>
                </el-row>
            </template>
            <template slot="btn">

            </template>
            <template slot="list">
                <el-tabs @tab-click="doHandleClick" v-model="activeName">
                    <el-tab-pane :label="getAllCount" name="first">
                        <el-checkbox @change="doSelectAll" style="margin-right: 0px;margin-left: 14px" v-model="selectAll"></el-checkbox>&nbsp;
                      <i18n k="/储值/预付卡/预付卡调整单/列表页面/已选择{0}张单据">
                        <template slot="0">&nbsp;{{selectedArr.length}}&nbsp;</template>
                      </i18n>
                        <el-button style="margin-left: 15px" v-if="hasOptionPermission('/卡/卡管理/预付卡调整单', '单据审核')" @click="doBatchAudit">批量审核</el-button>
                        <el-button v-if="hasOptionPermission('/卡/卡管理/预付卡调整单', '单据维护')"  @click="doBatchDelete" style="margin-left: 20px;color: red">批量删除</el-button>

                    </el-tab-pane>
                    <el-tab-pane :label="getNoAudit" name="second">
                        <el-checkbox @change="doSelectAll" style="margin-right: 0px;margin-left: 14px" v-model="selectAll"></el-checkbox>&nbsp;
                      <i18n k="/储值/预付卡/预付卡调整单/列表页面/已选择{0}张单据">
                        <template slot="0">&nbsp;{{selectedArr.length}}&nbsp;</template>
                      </i18n>
                        <el-button style="margin-left: 15px" v-if="hasOptionPermission('/卡/卡管理/预付卡调整单', '单据审核')" @click="doBatchAudit">批量审核</el-button>
                        <el-button v-if="hasOptionPermission('/卡/卡管理/预付卡调整单', '单据维护')"  @click="doBatchDelete" style="margin-left: 20px;color: red">批量删除</el-button>
                    </el-tab-pane>
                    <el-tab-pane :label="getAudit" name="third">
                        <!--<el-checkbox @change="doSelectAll" style="margin-right: 0px;margin-left: 14px" v-model="selectAll"></el-checkbox>&nbsp;已选择&nbsp;<span>{{selectedArr.length}}</span>&nbsp;张单据-->
<!--                        <el-button @click="doBatchAudit">批量审核</el-button>-->
<!--                        <el-button  @click="doBatchDelete" style="margin-left: 20px;color: red">批量删除</el-button>-->
                    </el-tab-pane>
                </el-tabs>
                <el-table
                        :data="tableData"
                        @selection-change="handleSelectionChange"
                        ref="table"
                        style="width: 100%;margin-top: 10px">
                    <el-table-column
                            type="selection"
                            v-if="activeName !== 'third'"
                            width="55">
                    </el-table-column>
                    <el-table-column label="单号" prop="billNumber" width="140">
                        <template slot-scope="scope">
                            <el-button  v-if="hasOptionPermission('/卡/卡管理/预付卡调整单', '单据查看')" @click="doGoDtl(scope.row)" type="text">{{scope.row.billNumber}}</el-button>
                        </template>
                    </el-table-column>
                    <el-table-column label="状态" prop="state">
                        <template slot-scope="scope">
                            <div>
                                <el-tag type="success" v-if="scope.row.state === 'AUDITED'">{{i18n('已审核')}}</el-tag>
                                <el-tag type="warning" v-else>{{i18n('未审核')}}</el-tag>
                            </div>
                        </template>
                    </el-table-column>
<!--                    <el-table-column label="账户" prop="occurredOrg.name">-->
<!--                        <template slot-scope="scope">-->
<!--                            <div>{{scope.row.occurredOrg.name}}</div>-->
<!--                        </template>-->
<!--                    </el-table-column>-->
                    <el-table-column label="创建时间" prop="created">
                        <template slot-scope="scope">
                            <div>{{scope.row.created | dateFormate3}}</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="最后修改时间" prop="lastModified">
                        <template slot-scope="scope">
                            <div>{{scope.row.lastModified | dateFormate3}}</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="摘要" prop="remark">
                        <template slot-scope="scope">
                            <div :title="scope.row.remark" style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">{{scope.row.remark | strFormat}}</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="创建人" prop="created" width="140">
                      <template slot-scope="scope">
                        <div v-if="scope.row.logs!==null && scope.row.logs.length === 1 && (scope.row.logs[0].type==='新建信息' || scope.row.logs[0].type==='Create')">
                          {{ scope.row.logs[0].operator }}</div>
                        <div v-if="scope.row.logs!==null && scope.row.logs.length > 1">
                          <div v-for="(item,index) in scope.row.logs" :key="index">
                            <div v-if="item.type==='新建信息' || item.type==='Create'">
                              {{ item.operator }}
                            </div>
                          </div>
                        </div>
                      </template>
                    </el-table-column>

                    <el-table-column label="审核人" prop="created" width="140">
                      <template slot-scope="scope">
                        <div v-if="scope.row.logs===null || (scope.row.logs!==null && scope.row.logs.length === 1 && (scope.row.logs[0].type==='新建信息' || scope.row.logs[0].type==='Create'))">
                          --</div>
                        <div v-if="scope.row.logs!==null && scope.row.logs.length === 1 && (scope.row.logs[0].type==='审核信息' || scope.row.logs[0].type==='Audit')">
                          {{ scope.row.logs[0].operator }}</div>
                        <div v-if="scope.row.logs!==null && scope.row.logs.length > 1">
                          <div v-for="(item,index) in scope.row.logs" :key="index">
                            <div v-if="item.type==='审核信息' || item.type==='Audit'">
                              {{ item.operator }}
                            </div>
                          </div>
                        </div>
                      </template>
                    </el-table-column>

                    <el-table-column label="发生组织" prop="created" width="140">
                      <template slot-scope="scope">
                        <div no-i18n>{{scope.row.occurredOrg.name}}[{{scope.row.occurredOrg.id}}]</div>
                      </template>
                    </el-table-column>
                </el-table>
            </template>
            <!--分页栏-->
            <template slot="page">
                <div style="margin-bottom: 10px" v-if="activeName !== 'third'">
                    <el-checkbox @change="doSelectAll" style="margin-right: 0px;margin-left: 14px" v-model="selectAll"></el-checkbox>
                  &nbsp;
                  <i18n k="/储值/预付卡/预付卡调整单/列表页面/已选择{0}张单据">
                    <template slot="0">&nbsp;{{selectedArr.length}}&nbsp;</template>
                  </i18n>
                  <el-button style="margin-left: 15px" v-if="hasOptionPermission('/卡/卡管理/预付卡调整单', '单据审核') && activeName !== 'third'" @click="doBatchAudit">批量审核</el-button>
                  <el-button v-if="hasOptionPermission('/卡/卡管理/预付卡调整单', '单据维护') && activeName !== 'third'"  @click="doBatchDelete" style="margin-left: 20px;color: red">批量删除</el-button>
                </div>
                <el-pagination
                    no-i18n
                        :current-page="page.currentPage"
                        :page-size="page.size"
                        :page-sizes="[10, 20, 30, 40]"
                        :total="page.total"
                        @current-change="onHandleCurrentChange"
                        @size-change="onHandleSizeChange"
                        background
                        layout="total, prev, pager, next, sizes,  jumper">
                </el-pagination>
            </template>
        </ListWrapper>
        <ImportDialog
            no-i18n
                :dialogShow="dialogShow"
                :orgs="orgs"
                :showOrg="showOrg"
                :importNumber="2000"
                :importUrl="importUrl"
                :templatePath="templatePath"
                @dialogClose="doDialogClose"
                @upload-success="doUploadSuccess"
                :templateName="i18n('预付卡调整单模板')" :title="i18n('/公用/券模板/导入')">
        </ImportDialog>
        <DownloadCenterDialog :dialogvisiable="fileDialogvisiable"
                              :showTip="true"
                              @dialogClose="doDownloadDialogClose"></DownloadCenterDialog>
    </div>
</template>

<script lang="ts" src="./PrepayCardAdjust.ts">
</script>

<style lang="scss">
.prepay-card-adjust{
    background-color: white;
    height: 100%;
    width: 100%;
    overflow: hidden;
    .current-page{
        .el-select{
            width: 100%;
        }
    }

    .el-range-editor.el-input__inner{
        width: 100%;
    }
    .el-table__body .el-table__row td {
        border-bottom: 1px solid #d7dfeb !important;
    }
}
</style>