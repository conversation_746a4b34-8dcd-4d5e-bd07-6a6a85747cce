<!--
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:19
 * @LastEditTime: 2024-10-22 16:45:13
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\activeaddcoupon\ActiveAddCoupon.vue
 * 记得注释
-->
<template>
  <div class="active-add-coupon">
    <span>
      <el-form ref="addCoupons" :model="formData">
        <div style="margin:5px 0 5px" v-for="(item, index) in formData.receiveParamsArray" :key="index">
          <template v-if="justShowIdName === false">
            <div style="display: flex;align-items: center;">
              <el-form-item :prop="`receiveParamsArray[${index}].qty`" :rules="rules" style="margin-bottom:0">
                <el-input @change="doQtyChange(item, index)" style="width: 140px" v-model="item.qty" :disabled="qtyDisabled">
                  <template slot="append">{{formatI18n('/储值/预付卡/电子礼品卡活动/编辑页面', '张')}}</template>
                </el-input>
              </el-form-item>
              <span :title="receiveNameArray[index]" class="coupon-name" @click="doEditStoreValueActive(index)">
                {{receiveNameArray[index]}}
              </span>
              <div v-if="showDelay">
                <span>{{i18n('/营销/支付宝营销/小程序领支付宝券/允许延期申请')}}&emsp;</span>
                <el-radio-group v-model="item.enableApplyDelay" @change="commitChange">
                  <el-radio :label="true">{{i18n('/营销/券礼包活动/小程序领微信券/允许')}}</el-radio>
                  <el-radio :label="false">{{i18n('/营销/券礼包活动/小程序领微信券/不允许')}}</el-radio>
                </el-radio-group>
              </div>
              <div style="margin-left:20px" v-if="showTemplatePrice">
                <el-form-item :prop="`receiveParamsArray[${index}].templatePrice`" :rules="{
                      required: true,
                      message: formatI18n('/公用/js提示信息', '请填写必填项'),
                      trigger: 'blur',
                    }">
                  {{formatI18n('/公用/券模板/券价格')}}
                  <AutoFixInput :min="0.01" :max="99999.99" :fixed="2" style="width: 90px" v-model="item.templatePrice" placeholder="请输入"
                    :disabled="canTemplatePriceEdit">
                  </AutoFixInput>
                  {{formatI18n('/公用/券模板/元')}}
                </el-form-item>
              </div>
              <div v-if="availableState">
                <i class="el-icon-delete" @click="doDelete(index)"></i>
              </div>
            </div>
            <div style="padding: 10px 10px 0 0">
              <span @click="doAddCoupon(index)" class="span-btn"
                v-if="availableState && index === formData.receiveParamsArray.length - 1 && ((maxLimit && maxLimit > formData.receiveParamsArray.length) || !maxLimit)">
                +{{ formatI18n('/会员/等级/等级管理/未初始化状态/点击立即开始免费等级初始化/输入所有必填项点击下一步/然后输入所有必填项点击下一步/等级月礼下的表格', '添加券') }}
              </span>
            </div>
          </template>
          <div v-else style="display: flex">
            <span :title="receiveNameArray[index]" class="coupon-name" @click="doEditStoreValueActive(index)">
              {{receiveNameArray[index]}}
            </span>
            <div v-if="availableState">
              <i class="el-icon-delete" @click="doDelete(index)"></i>
            </div>
            <el-button @click="doAddCoupon(index)" type="text"
              v-if="availableState && index === formData.receiveParamsArray.length - 1 && ((maxLimit && maxLimit > formData.receiveParamsArray.length) || !maxLimit)">
              +{{ formatI18n('/会员/等级/等级管理/未初始化状态/点击立即开始免费等级初始化/输入所有必填项点击下一步/然后输入所有必填项点击下一步/等级月礼下的表格', '添加券') }}
            </el-button>
          </div>
        </div>

      </el-form>
    </span>
    <!--:data="templateData"-->
    <CouponTemplateSelectorDialog ref="couponTemplate" :filter="cardTemplateFilter" :propMaxLimit="propMaxLimit" @summit="doCardTemplateSelected"
      :excludedTypes="excludedTypes" :showActivityReference="showActivityReference">

    </CouponTemplateSelectorDialog>
    <SelectStoreActiveDtlDialog :baseSettingFlag="baseSettingFlag" :child="child" ref="couponTemplateDtlDialog" @dialogClose="doDialogClose"
      :dialogShow="couponTemplateDtlDialogFlag">
    </SelectStoreActiveDtlDialog>

  </div>
</template>

<script lang="ts" src="./ActiveAddCoupon.ts">
</script>

<style lang="scss">
.active-add-coupon {
  display: inline-flex;
  text-align: left;
  .el-form-item {
    margin-bottom: 0;
  }
  .coupon-name {
    color: #20a0ff;
    cursor: pointer;
    width: 120px;
    white-space: nowrap;
    font-size: 13px;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-right: 20px;
    margin-left: 8px;
  }
  .el-icon-delete {
    color: red;
    cursor: pointer;
    font-size: 15px;
    margin-left: 8px;
  }
}
</style>