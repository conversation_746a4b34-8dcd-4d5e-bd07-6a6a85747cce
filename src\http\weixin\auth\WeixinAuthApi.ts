import ApiClient from 'http/ApiClient'
import AuthFiled from 'model/weixin/auth/AuthFiled'
import Response from 'model/common/Response'

export default class WeixinAuthApi {
  /**
   * 微信授权页面加载控制
   *
   */
  static preAuthorize(): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/weixin-auth/preAuthorize`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 微信授权成功回调
   *
   */
  static success(body: AuthFiled): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/weixin-auth/success`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
