import ApiClient from 'http/ApiClient'
import MemberAnalysis from 'model/report/memberanalysis/MemberAnalysis'
import MemberAnalysisFilter from 'model/report/memberanalysis/MemberAnalysisFilter'
import MemberAnalysisQueryResponse from 'model/report/memberanalysis/MemberAnalysisQueryResponse'
import MemberAnalysisListFilter from 'model/report/memberanalysis/MemberAnalysisListFilter'
import Response from 'model/default/Response'

export default class MemberAnalysisApi {
  /**
   * 批量导出会员分析数据
   * 批量导出会员分析数据。
   * 
   */
  static exportMemberAnalysis(id: String): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/member-data-analysis/exportMemberAnalysis?id=${id}`,{
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询
   * 查询。
   * 
   */
  static getBy(id: string): Promise<Response<MemberAnalysis>> {
    return ApiClient.server().get(`/v1/member-data-analysis/getBy`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询列表
   * 查询列表。
   * 
   */
    /**
   * 查询分析数据列表
   * 查询分析数据列表。
   * 
   */
    static queryList(body: MemberAnalysisListFilter): Promise<Response<MemberAnalysis[]>> {
      return ApiClient.server().post(`/v1/member-data-analysis/queryList`, body, {
      }).then((res) => {
        return res.data
      })
    }

  static listBy(): Promise<Response<MemberAnalysis[]>> {
    return ApiClient.server().post(`/v1/member-data-analysis/listBy`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改会员分析
   * 修改会员分析
   * 
   */
  static modify(body: MemberAnalysis): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/member-data-analysis/modify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分析数据
   * 分析数据。
   * 
   */
  static query(body: MemberAnalysisFilter): Promise<Response<MemberAnalysisQueryResponse[]>> {
    return ApiClient.server().post(`/v1/member-data-analysis/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除
   * 删除。
   * 
   */
  static remove(id: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/member-data-analysis/remove`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存会员分析
   * 保存会员分析
   * 
   */
  static save(body: MemberAnalysis): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/member-data-analysis/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
