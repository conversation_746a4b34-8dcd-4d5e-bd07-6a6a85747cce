/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2025-05-20 17:58:52
 * @LastEditors: 黎钰龙
 * @Description:
 * @FilePath: \phoenix-web-ui\src\mgr\ConstantMgr.ts
 * 记得注释
 */
import { Vue } from "vue-property-decorator";
import i18n from "../locale/Locale.js";
import store from "store/index";
import Permission from "model/authorize/Permission";

export const loadingOption = {
  // lock: true,
  // text: "加载中，请稍后...",
  // spinner: "el-icon-loading",
  // background: "transparent",
};

export class MenusFuc {
  /**
   * 查找resourceId前缀为resourceIdPrefix参数并且actions存在action参数的权限，找到返回true否则false
   * 应用场景：如券礼包活动这个二级菜单下有商品满额发券、商品满数量发券、小程序领券等模块，
   * 当某个模块存在查看权限时就展示券礼包活动这个二级菜单，此时就可以使用该方法进行判断。
   */
  static checkByResourceIdPrefix(permissionsList: Permission[], resourceIdPrefix: string, action: string) {
    let matchResourceId = permissionsList.filter((e) => e.resourceId!.indexOf(resourceIdPrefix) > -1);
    for (let item of matchResourceId) {
      if (!!item.actions.find((e: string) => e === action)) {
        return true;
      }
    }
    return false;
  }

  static menus() {
    //用来渲染一二级菜单列表的
    return [
      {
        // 菜名名称，需要在phoenix-crm国际化文件中添加以'/公用/菜单'为前缀的国际化信息，此处配置时不需要添加前缀，会在NavNew组件中自动拼接。
        // 如此处配置“会员”后，在index_en_US(CN).properties文件中添加“/公用/菜单/会员”的信息即可。
        name: "会员",
        icon: require("assets/image/nav/ic_member_normal.svg"), // 菜单图标
        iconSelect: require("assets/image/nav/ic_member_selected.svg"), // 已选中时的菜单图标
        // 菜单的标识，用于刷新页面时根据路由回显选中状态，如当页面路由为/member/xxx时，会员一级菜单高亮。
        hash: "member",
        children: [
          {
            subTitle: "会员管理", // 二级菜单分组名，不配置就不显示
            children: [
              {
                name: "会员首页",
                hash: "home",
                permissions: [{ resourceId: "/会员/会员管理/会员首页", action: "查看" }],
              },
              {
                name: "会员资料",
                hash: "standard-member",
                permissions: [{ resourceId: "/会员/会员管理/会员资料", action: "会员资料查看" }],
              },
              {
                name: "营销中心会员",
                hash: "marketcenter-member",
                permissions: [{ resourceId: "/会员/会员管理/营销中心会员", action: "会员资料查看" }],
              },
              {
                name: "会员资料",
                hash: "dqsh-member",
                permissions: [{ resourceId: "/会员/会员管理/会员资料（大桥石化）", action: "会员资料查看" }],
              },
              {
                name: "会员批量操作单",
                hash: "batch-operate",
                permissions: [{ resourceId: "/会员/会员管理/会员批量操作单", action: "单据查看" }],
              },
              {
                name: "黑名单",
                hash: "member-blacklist",
                permissions: [{ resourceId: "/会员/会员管理/黑名单", action: "查看" }],
              },
            ],
          },
          {
            subTitle: "会员体系",
            children: [
              {
                name: "等级管理",
                hash: "level",
                permissions: [{ resourceId: "/会员/会员体系/等级管理1.0", action: "等级查看" }],
              },
              {
                name: "等级管理",
                hash: "level2",
                permissions: [{ resourceId: "/会员/会员体系/等级管理2.0", action: "等级查看" }],
              },
                {
                name: "付费会员",
                hash: "paid-membership",
                  permissions: [
                    { resourceId: "/会员/会员体系/付费会员/付费记录", action: "数据查看" },
                    { resourceId: "/会员/会员体系/付费会员/付费会员卡查询", action: "数据查看" },
                    { resourceId: "/会员/会员体系/付费会员/付费会员卡", action: "数据查看" },
                  ],
              },
              {
                name: "权益卡",
                hash: "benefit-card",
                permissions: [
                  { resourceId: "/会员/会员体系/权益卡/权益卡流水", action: "数据查看" },
                  { resourceId: "/会员/会员体系/权益卡/权益卡查询", action: "数据查看" },
                  { resourceId: "/会员/会员体系/权益卡/权益卡管理", action: "数据查看" },
                ],
              },
              {
                name: "权益中心",
                hash: "equity-center",
                permissions: [{ resourceId: "/会员/会员体系/权益中心", action: "权益查看" }],
              },
              {
                name: "门店套餐设置",
                hash: "store-pay",
                permissions: [{ resourceId: "/会员/会员体系/门店套餐设置", action: "配置查看" }],
              },
              {
                name: "成长值规则",
                hash: "grow-value-rule",
                permissions: [{ resourceId: "/会员/会员体系/成长值规则", action: "规则查看" }],
              },
              // {
              //   name: "单笔消费升级激励",
              //   hash: "upgrade-incentives-dtl",
              //   permissions: [{ resourceId: "/营销/营销/消费有礼/单笔消费升级激励", action: "规则查看" }],
              // },
            ],
          },
          // {
          //   subTitle: "权益卡管理",
          //   children: [
          //     {
          //       name: "权益卡设置",
          //       hash: "equity-card",
          //       permissions: [{ resourceId: "/会员/权益卡管理/权益卡设置", action: "权益卡维护" }],
          //     },
          //     {
          //       name: "权益卡查询",
          //       hash: "EquityCardListSearch",
          //       permissions: [{ resourceId: "/会员/权益卡管理/权益卡查询", action: "数据查看" }],
          //     },
          //   ],
          // },
          {
            subTitle: "标签客群",
            children: [
              {
                name: "标签",
                hash: "label-manage",
                permissions: [{ resourceId: "/会员/标签客群/标签", action: "标签查看" }],
              },
              {
                name: "规则打标",
                hash: "tag-manage",
                permissions: [{ resourceId: "/会员/标签客群/规则打标", action: "规则打标查看" }],
              },
              {
                name: "智能打标",
                hash: "auto-make-tag",
                permissions: [{ resourceId: "/会员/标签客群/智能打标", action: "智能打标查看" }],
              },
              {
                name: "客群",
                hash: "customer-group",
                permissions: [{ resourceId: "/会员/标签客群/客群", action: "客群查看" }],
              },
              {
                name: "规则建群", // 二级菜单名，国际化方式同一级菜单
                // 参考NavNew.doGoView，当点击该菜单时，将会触发this.$router.push({name: 'user-group'})
                // 会跳转到/member/user-group路由，由于user-group配置了redirect: 'user-group/user-group-manage',
                // 最终会跳转到/member/user-group/user-group-manage，这样做的目的主要是为了规划路由层次和根据路由回显菜单选中状态
                // 如对于路由/member/user-group/user-group-manage，直接高亮一级菜单会员，和二级菜单规则建群即可，不需要借助localstorage等其他需要额外存储的方式。
                hash: "user-group",
                // 根据权限判断菜单是否展示，此处配置的是数组，精确到action，配置多个时，满足一个就显示对应的菜单。
                // 另一种方式是使用permissionsFunc，如：
                // permissionsFunc: (permissionsList: Permission[]) => true|false，提供一个返回false或true的方法来控制菜单是否显示
                // 当同时配置permissionsFunc和permissions时，使用permissions提供的配置进行判断，permissionsFunc的配置会被忽略。
                // 对于权限的判断，相关代码参考PermissionMgr.refreshPermission
                permissions: [{ resourceId: "/会员/标签客群/规则建群", action: "规则建群查看" }],
              },
              {
                name: "智能建群",
                hash: "intelligence-group",
                permissions: [{ resourceId: "/会员/标签客群/智能建群", action: "智能建群查看" }],
              },
            ],
          },
        ],
      },
      {
        name: "积分",
        icon: require("assets/image/nav/ic_quanyi_normal.svg"),
        iconSelect: require("assets/image/nav/ic_quanyi_selected.svg"),
        hash: "score",
        children: [
          {
            subTitle: "积分管理",
            children: [
              {
                name: "积分查询",
                hash: "score-query-list",
                permissions: [{ resourceId: "/积分/积分管理/积分查询", action: "数据查看" }],
              },
              {
                name: "积分调整单",
                hash: "score-adjust",
                permissions: [{ resourceId: "/积分/积分管理/积分调整单", action: "单据查看" }],
              },
            ],
          },
          {
            subTitle: "积分规则",
            children: [
              {
                name: "基础得积分",
                hash: "score-dtl",
                permissions: [{ resourceId: "/积分/积分规则/基础得积分", action: "规则查看" }],
              },
              {
                name: "不积分商品",
                hash: "gain-points-goods-list",
                permissions: [{ resourceId: "/积分/积分规则/不积分商品", action: "活动查看" }],
              },
              {
                name: "积分抵现",
                hash: "points-charge-setting-dtl",
                permissions: [{ resourceId: "/积分/积分规则/积分抵现", action: "规则查看" }],
              },
              {
                name: "积分抵现活动",
                hash: "score-activity-list",
                permissions: [{ resourceId: "/积分/积分规则/积分抵现活动", action: "活动查看" }],
              },
            ],
          },
        ],
      },
      {
        name: "储值",
        icon: require("assets/image/nav/ic_member_normal.png"),
        iconSelect: require("assets/image/nav/ic_member_selected.png"),
        hash: "deposit",
        children: [
          {
            subTitle: "储值管理",
            children: [
              {
                name: "多储值账户",
                hash: "store-value-account",
                permissions: [{ resourceId: "/储值/储值管理/多储值账户", action: "账户信息查看" }],
              },
              {
                name: "充值面额设置",
                hash: "store-value-init",
                permissions: [{ resourceId: "/储值/储值管理/充值面额设置", action: "配置查看" }],
              },
              {
                name: "会员储值查询",
                hash: "store-value-query",
                permissions: [{ resourceId: "/储值/储值管理/会员储值查询", action: "数据查看" }],
              },
              {
                name: "储值调整单",
                hash: "store-value-adjust",
                permissions: [{ resourceId: "/储值/储值管理/储值调整单", action: "单据查看" }],
              },
              {
                name: "储值充值单",
                hash: "storage-recharge-bill",
                permissions: [{ resourceId: "/储值/储值管理/储值充值单", action: "单据查看" }],
              },
              {
                name: "储值适用商品设置",
                hash: "store-value-applicable-goods-dtl",
                permissions: [{ resourceId: "/储值/储值管理/储值适用商品设置", action: "账户信息查看" }],
              },
              {
                name: "储值限额设置",
                hash: "member-balance-limit-setting",
                permissions: [{ resourceId: "/储值/储值管理/储值限额设置", action: "配置查看" }],
              },
            ],
          },
          {
            subTitle: "储值活动",
            children: [
              {
                name: "充值有礼",
                hash: "store-value-active",
                permissions: [{ resourceId: "/储值/储值活动/充值有礼/储值充值活动", action: "活动查看" }],
              },
              {
                name: "储值支付优惠",
                hash: "member-balance-promotion",
                permissions: [
                  { resourceId: "/储值/储值活动/储值支付优惠/储值支付立减", action: "活动查看" },
                  { resourceId: "/储值/储值活动/储值支付优惠/储值支付折扣", action: "活动查看" },
                ],
              },
            ],
          },
        ],
      },
      {
        name: "卡",
        icon: require("assets/image/nav/ic_card_normal.png"),
        iconSelect: require("assets/image/nav/ic_card_selected.png"),
        hash: "card",
        children: [
          {
            subTitle: "卡管理",
            children: [
              {
                name: "卡模板",
                hash: "prepay-card-tpl",
                permissions: [
                  { resourceId: "/卡/卡管理/卡模板/充值卡", action: "卡模板查看" },
                  { resourceId: "/卡/卡管理/卡模板/礼品卡", action: "卡模板查看" },
                  { resourceId: "/卡/卡管理/卡模板/储值卡", action: "卡模板查看" },
                  { resourceId: "/卡/卡管理/卡模板/次卡", action: "卡模板查看" },
                ],
              },
              {
                name: "卡查询",
                hash: "prepay-card",
                permissions: [{ resourceId: "/卡/卡管理/卡查询/卡查询", action: "预付卡查看" }],
              },
              {
                name: "制卡单",
                hash: "produce-card-tpl",
                permissionsFunc: (permissionsList: Permission[]) =>
                  MenusFuc.checkByResourceIdPrefix(permissionsList, "/卡/卡管理/制卡单", "单据查看"),
              },
              {
                name: "领卡单",
                hash: "receive-card-tpl",
                permissionsFunc: (permissionsList: Permission[]) =>
                  MenusFuc.checkByResourceIdPrefix(permissionsList, "/卡/卡管理/领卡单", "单据查看"),
              },
              {
                name: "售卡单",
                hash: "sale-card-tpl",
                permissionsFunc: (permissionsList: Permission[]) =>
                  MenusFuc.checkByResourceIdPrefix(permissionsList, "/卡/卡管理/售卡单", "单据查看"),
              },
              {
                name: "写卡单",
                hash: "write-card",
                permissions: [{ resourceId: "/卡/卡管理/写卡单", action: "单据查看" }],
              },
              {
                name: "制售单",
                hash: "make-sale-card-bill",
                permissions: [{ resourceId: "/卡/卡管理/制售单", action: "单据查看" }],
              },
              {
                name: "预付卡调整单",
                hash: "prepay-card-adjust",
                permissions: [{ resourceId: "/卡/卡管理/预付卡调整单", action: "单据查看" }],
              },
              {
                name: "预付卡充值单",
                hash: "prepay-card-pay",
                permissions: [{ resourceId: "/卡/卡管理/预付卡充值单", action: "单据查看" }],
              },
              {
                name: "退卡设置",
                hash: "prepay-card-store",
                permissions: [{ resourceId: "/卡/卡管理/退卡设置", action: "配置查看" }],
              },
              {
                name: "卡限额设置",
                hash: "store-value-manage",
                permissions: [{ resourceId: "/卡/卡管理/卡限额设置", action: "配置查看" }],
              },
              {
                name: "卡充值限额设置",
                hash: "card-recharge-limit",
                permissions: [{ resourceId: "/卡/卡管理/卡充值限额设置", action: "配置查看" }],
              },
              {
                name: "卡回收单",
                hash: "card-recycle-bill",
                permissions: [{ resourceId: "/卡/卡管理/卡回收单", action: "单据查看" }],
              },
              {
                name: "坏卡重制",
                hash: "bad-card-reset",
                permissions: [{ resourceId: "/卡/卡管理/坏卡重制", action: "单据查看" }],
              },
              {
                name: "旧卡重制",
                hash: "old-card-reset",
                permissions: [{ resourceId: "/卡/卡管理/旧卡重制", action: "单据查看" }],
              }
            ],
          },
          {
            subTitle: "卡活动",
            children: [
              {
                name: "电子卡售卡活动",
                hash: "gift-card-activity",
                permissions: [{ resourceId: "/卡/卡活动/电子卡售卡活动", action: "活动查看" }],
              },
              {
                name: "实体卡售卡活动",
                hash: "entity-card-activity",
                permissions: [{ resourceId: "/卡/卡活动/实体卡售卡活动", action: "活动查看" }],
              },
              {
                name: "预付卡支付优惠",
                hash: "card-balance-promotion",
                permissions: [
                  { resourceId: "/卡/卡活动/预付卡支付优惠/预付卡支付折扣", action: "活动查看" },
                  { resourceId: "/卡/卡活动/预付卡支付优惠/预付卡支付立减", action: "活动查看" },
                ],
              },
              {
                name: "预付卡充值有礼",
                hash: "prepay-card-charge-gift",
                permissionsFunc: (permissionsList: Permission[]) =>
                  MenusFuc.checkByResourceIdPrefix(permissionsList, "/卡/卡活动/预付卡充值有礼", "活动查看"),
              },
            ],
          },
        ],
      },
      {
        name: "券",
        icon: require("assets/image/nav/ic_yingxiao_normal.svg"),
        iconSelect: require("assets/image/nav/ic_yingxiao_selected.svg"),
        hash: "coupons",
        children: [
          {
            subTitle: "券管理",
            children: [
              {
                name: "券初始化",
                hash: "coupon-init",
                permissions: [{ resourceId: "/券/券管理/券初始化", action: "查看" }],
              },
              {
                name: "券模板",
                hash: "coupon-template",
                permissions: [{ resourceId: "/券/券管理/券模板", action: "券模板查看" }],
              },
              {
                name: "券查询",
                hash: "coupon-list",
                permissions: [{ resourceId: "/券/券管理/券查询", action: "数据查看" }],
              },
              {
                name: "券核销",
                hash: "coupon-write-off",
                permissions: [{ resourceId: "/券/券管理/券核销", action: "单据查看" }],
              },
              {
                name: "购券交易",
                hash: "coupon-purchase",
                permissions: [{ resourceId: "/券/券管理/购券交易", action: "单据查看" }],
              },
              {
                name: "退款申请",
                hash: "after-sale",
                permissions: [{ resourceId: "/券/券管理/退款申请", action: "单据查看" }],
              },
              {
                name: "延期申请",
                hash: "coupon-delay-apply",
                permissions: [{ resourceId: "/券/券管理/延期申请", action: "单据查看" }],
              },
            ],
          },
        ],
      },
      {
        name: "营销",
        icon: require("assets/image/nav/ic_yingxiao_normal.svg"),
        iconSelect: require("assets/image/nav/ic_yingxiao_selected.svg"),
        hash: "promotion",
        children: [
          {
            subTitle: "营销",
            children: [
              {
                name: "营销日历",
                hash: "marketing-calendar",
                permissions: [{ resourceId: "/营销/营销/营销日历", action: "单据查看" }],
              },
              {
                name: "活动主题",
                hash: "topic",
                permissions: [{ resourceId: "/营销/营销/活动主题", action: "主题查看" }],
              },
              {
                name: "消费有礼",
                hash: "coupon-active",
                permissions: [
                  { resourceId: "/营销/营销/消费有礼/单品满数量加送积分", action: "活动查看" },
                  { resourceId: "/营销/营销/消费有礼/商品组合满数量加送积分", action: "活动查看" },
                  { resourceId: "/营销/营销/消费有礼/商品满额加送积分", action: "活动查看" },
                  { resourceId: "/营销/营销/消费有礼/商品满额积分加倍", action: "活动查看" },
                  { resourceId: "/营销/营销/消费有礼/商品满额发券", action: "活动查看" },
                  { resourceId: "/营销/营销/消费有礼/商品满数量发券", action: "活动查看" },
                  { resourceId: "/营销/营销/消费有礼/商品满额发大礼包", action: "活动查看" },
                  { resourceId: "/营销/营销/消费有礼/用券发券", action: "活动查看" },
                  { resourceId: "/营销/营销/消费有礼/消费日历", action: "查看详情" },
                  { resourceId: "/营销/营销/消费有礼/集点活动", action: "活动查看" },
                  { resourceId: "/营销/营销/消费有礼/单笔消费升级激励", action: "规则查看" },
                  { resourceId: "/营销/营销/消费有礼/累计消费有礼", action: "活动查看" },
                ],
              },
              {
                name: "会员成长",
                hash: "membership-growth",
                permissions: [
                  { resourceId: "/营销/营销/会员成长/邀请有礼", action: "活动查看" },
                  { resourceId: "/营销/营销/会员成长/升级有礼", action: "活动查看" },
                  { resourceId: "/营销/营销/会员成长/注册发大礼包", action: "活动查看" },
                  { resourceId: "/营销/营销/会员成长/微信激活发大礼包", action: "活动查看" },
                  { resourceId: "/营销/营销/会员成长/完善资料有礼", action: "活动查看" },
                ],
              },
              {
                name: "节日有礼",
                hash: "score-init",
                permissions: [
                  { resourceId: "/营销/营销/节日有礼/会员日积分加速", action: "规则查看" },
                  { resourceId: "/营销/营销/节日有礼/会员日积分加速抵现", action: "规则查看" },
                  { resourceId: "/营销/营销/节日有礼/生日积分加倍", action: "规则查看" },
                  { resourceId: "/营销/营销/节日有礼/生日送礼", action: "规则查看" },
                ],
              },
              {
                name: "营销工具",
                hash: "promotion-tools",
                permissions: [
                  { resourceId: "/营销/营销/营销工具/积分兑换券", action: "活动查看" },
                  { resourceId: "/营销/营销/营销工具/积分兑换商品", action: "活动查看" },
                  { resourceId: "/营销/营销/营销工具/第三方发券", action: "活动查看" },
                  { resourceId: "/营销/营销/营销工具/核销第三方券", action: "活动查看" },
                  { resourceId: "/营销/营销/营销工具/导出券码发券", action: "活动查看" },
                  { resourceId: "/营销/营销/营销工具/客群发大礼包", action: "活动查看" },
                  { resourceId: "/营销/营销/营销工具/群发券", action: "活动查看" },
                  { resourceId: "/营销/营销/营销工具/限量抢购", action: "活动查看" },
                ],
              },
              {
                name: "兑换码",
                hash: "exchange-code",
                permissions: [{ resourceId: "/营销/营销/兑换码/兑换码", action: "单据查看" }],
              },
              {
                name: "微信营销",
                hash: "wx-promotion",
                permissions: [
                  { resourceId: "/营销/营销/微信营销/小程序领券", action: "活动查看" },
                  { resourceId: "/营销/营销/微信营销/微信扫码领券", action: "活动查看" },
                  { resourceId: "/营销/营销/微信营销/小程序领微信券", action: "活动查看" },
                  { resourceId: "/营销/营销/微信营销/定向发券", action: "活动查看" },
                  { resourceId: "/营销/营销/微信营销/开屏推广", action: "活动查看" },
                  { resourceId: "/营销/营销/微信营销/开屏券包", action: "活动查看" },
                  { resourceId: "/营销/营销/微信营销/微信支付有礼", action: "活动查看" },
                  { resourceId: "/营销/营销/微信营销/微信支付营销", action: "活动查看" },
                ],
              },
              {
                name: "支付宝营销",
                hash: "alipay-promotion",
                permissions: [
                  { resourceId: "/营销/营销/支付宝营销/小程序领支付宝券", action: "活动查看" },
                  { resourceId: "/营销/营销/支付宝营销/支付有礼", action: "活动查看" },
                  { resourceId: "/营销/营销/支付宝营销/平台团购券", action: "活动查看" },
                ],
              },
              {
                name: "视频号发券",
                hash: "videonumberActivity",
                permissions: [{ resourceId: "/营销/营销/视频号发券/视频号发券", action: "活动查看" }],
              },
              {
                name: "高德发券",
                hash: "gaode-send-coupon",
                permissions: [{ resourceId: "/营销/营销/高德三方券/高德发券", action: "活动查看" }],
              },
              {
                name: "推送计划",
                hash: "push-plan",
                permissionsFunc: (permissionsList: Permission[]) =>
                  MenusFuc.checkByResourceIdPrefix(permissionsList, "/营销/营销/推送计划/推送计划", "计划查看"),
              },
              {
                name: "门店促销设置",
                hash: "store-promotion",
                permissions: [{ resourceId: "/营销/营销/门店促销设置", action: "规则查看" }],
              },
              {
                name: "营销申请",
                hash: "marketing-apply",
                permissions: [{ resourceId: "/营销/营销/营销申请", action: "查看" }],
              },
            ],
          },
        ],
      },
      {
        name: "数据",
        icon: require("assets/image/nav/ic_fenxi_normal.svg"),
        iconSelect: require("assets/image/nav/ic_fenxi_selected.svg"),
        hash: "data-table",
        children: [
          {
            subTitle: "报表",
            children: [
              {
                name: "付费等级流水报表",
                hash: "fee-level",
                permissions: [{ resourceId: "/数据/报表/付费等级流水报表", action: "查看" }],
              },
              {
                name: "积分报表",
                hash: "score-report",
                permissions: [{ resourceId: "/数据/报表/积分报表", action: "报表查看" }],
              },
              {
                name: "会员储值报表",
                hash: "store-value-report",
                permissions: [{ resourceId: "/数据/报表/会员储值报表", action: "报表查看" }],
              },
              {
                name: "券报表",
                hash: "coupon-report",
                permissions: [{ resourceId: "/数据/报表/券报表", action: "报表查看" }],
              },
              {
                name: "礼品卡报表",
                hash: "gift-card-activity-report",
                permissions: [{ resourceId: "/数据/报表/礼品卡报表", action: "报表查看" }],
              },
              {
                name: "储值卡报表",
                hash: "value-card-activity-report",
                permissions: [{ resourceId: "/数据/报表/储值卡报表", action: "报表查看" }],
              },
              {
                name: "充值卡报表",
                hash: "recharge-card-report",
                permissions: [{ resourceId: "/数据/报表/充值卡报表", action: "报表查看" }],
              },
              {
                name: "次卡报表",
                hash: "count-card-report",
                permissions: [{ resourceId: "/数据/报表/次卡报表", action: "报表查看" }],
              },
              {
                name: "paso报表",
                hash: "paso-report",
                permissions: [{ resourceId: "/数据/报表/paso报表", action: "查看" }],
              },
            ],
          },
          {
            subTitle: "分析",
            children: [
              {
                name: "会员增长分析",
                hash: "member-growth-analysis",
                permissions: [{ resourceId: "/数据/分析/会员增长分析", action: "数据查看" }],
              },
              {
                name: "会员交易分析",
                hash: "member-transaction-analysis",
                permissions: [{ resourceId: "/数据/分析/会员交易分析", action: "数据查看" }],
              },
              {
                name: "平台券分析",
                hash: "platform-volume-analysis",
                permissions: [{ resourceId: "/数据/分析/平台券分析", action: "数据查看" }],
              },
              {
                name: "门店会员增长概况",
                hash: "member-analysis",
                permissions: [{ resourceId: "/数据/分析/门店会员增长概况", action: "数据查看" }],
              },
              {
                name: "门店会员交易概况",
                hash: "trans-analysis",
                permissions: [{ resourceId: "/数据/分析/门店会员交易概况", action: "数据查看" }],
              },
            ],
          },
          {
            subTitle: "资产",
            children: [
              {
                name: "会员积分分析",
                hash: "member-points-analysis",
                permissions: [{ resourceId: "/数据/资产/会员积分分析", action: "数据查看" }],
              },
              {
                name: "会员储值分析",
                hash: "member-deposit-analysis",
                permissions: [{ resourceId: "/数据/资产/会员储值分析", action: "数据查看" }],
              }
              ,
              {
                name: "会员礼品卡分析",
                hash: "gift-card-data-analysis",
                permissions: [{ resourceId: "/数据/资产/会员礼品卡分析", action: "数据查看" }],
              }
              ,
              {
                name: "会员储值卡分析",
                hash: "rechargeable-card-data-analysis",
                permissions: [{ resourceId: "/数据/资产/会员储值卡分析", action: "数据查看" }],
              }
              ,
              {
                name: "会员充值卡分析",
                hash: "imprest-card-data-analysis",
                permissions: [{ resourceId: "/数据/资产/会员充值卡分析", action: "数据查看" }],
              }
            ]
          },
          {
            subTitle: "数据洞察",
            children: [
              {
                name: "RFM分析",
                hash: "rfm-analysis",
                permissions: [{ resourceId: "/数据/数据洞察/RFM分析", action: "数据查看" }],
              },
              {
                name: "会员分析",
                hash: "member-vip-analysis",
                permissions: [{ resourceId: "/数据/数据洞察/会员分析", action: "数据查看" }],
              },
              {
                name: "客群画像",
                hash: "customer-profile",
                permissions: [{ resourceId: "/数据/数据洞察/客群画像", action: "数据查看" }],
              }
            ]
          },
          {
            subTitle: "门店结算账单",
            children: [
              {
                name: "平台券账单",
                hash: "coupon-settle-bill",
                permissions: [{ resourceId: "/数据/门店结算账单/平台券账单", action: "数据查看" }],
              },
              {
                name: "CRM券账单",
                hash: "phoenix-coupon-settle-bill",
                permissions: [{ resourceId: "/数据/门店结算账单/CRM券账单", action: "数据查看" }],
              },
              {
                name: "储值账单",
                hash: "prepay-settle-bill",
                permissions: [{ resourceId: "/数据/门店结算账单/储值账单", action: "数据查看" }],
              },
              {
                name: "礼品卡账单",
                hash: "prepay-card-settle-bill",
                permissions: [{ resourceId: "/数据/门店结算账单/礼品卡账单", action: "数据查看" }],
              },
              {
                name: "储值卡账单",
                hash: "deposit-card-settle-bill",
                permissions: [{ resourceId: "/数据/门店结算账单/储值卡账单", action: "数据查看" }],
              },
              {
                name: "积分账单",
                hash: "points-settle-bill",
                permissions: [{ resourceId: "/数据/门店结算账单/积分账单", action: "数据查看" }],
              }
            ]
          }
        ],
      },
      {
        name: "设置",
        icon: require("assets/image/nav/ic_set_normal.svg"),
        iconSelect: require("assets/image/nav/ic_set_selected.svg"),
        hash: "setting",
        children: [
          {
            subTitle: "资料",
            children: [
              {
                name: "商品",
                hash: "product",
                permissions: [{ resourceId: "/设置/资料/商品", action: "查看" }],
              },
              {
                name: "品类",
                hash: "catogory",
                permissions: [{ resourceId: "/设置/资料/品类", action: "查看" }],
              },
              {
                name: "品牌",
                hash: "brand",
                permissions: [{ resourceId: "/设置/资料/品牌", action: "查看" }],
              },
              {
                name: "营销中心",
                hash: "marketing-center",
                permissions: [{ resourceId: "/设置/资料/营销中心", action: "资料查看" }],
              },
              {
                name: "区域",
                hash: "area",
                permissions: [{ resourceId: "/设置/资料/区域", action: "资料查看" }],
              },
              {
                name: "门店",
                hash: "store",
                permissions: [{ resourceId: "/设置/资料/门店", action: "资料查看" }],
              },
              {
                name: "员工",
                hash: "employ",
                permissions: [{ resourceId: "/设置/资料/员工", action: "查看" }],
              },
              {
                name: "券承担方",
                hash: "costparty",
                permissions: [{ resourceId: "/设置/资料/券承担方", action: "资料查看" }],
              },
              {
                name: "支付方式",
                hash: "pay-method-list",
                permissions: [{ resourceId: "/设置/资料/支付方式", action: "支付方式查看" }],
              },
            ],
          },
          {
            subTitle: "消息通知",
            children: [
              {
                name: "消息模板",
                hash: "msg-template-list",
                permissions: [{ resourceId: "/设置/消息通知/消息模板", action: "查看" }],
              },
              {
                name: "提醒设置", //提醒设置
                hash: "msg-notice-setting",
                permissions: [{ resourceId: "/设置/消息通知/提醒设置", action: "查看" }],
              },
              {
                name: "用户通知",
                hash: "member-notify-list",
                permissions: [{ resourceId: "/设置/消息通知/用户通知", action: "查看" }],
              },
            ],
          },
          {
            subTitle: "渠道",
            children: [
              {
                name: "渠道",
                hash: "channel",
                permissions: [{ resourceId: "/设置/渠道/渠道", action: "资料查看" }],
              },
              {
                name: "微信会员设置",
                hash: "wx-member-setting",
                permissions: [
                  { resourceId: "/设置/渠道/微信会员设置/微信会员初始化", action: "配置查看" },
                  { resourceId: "/设置/渠道/微信会员设置/微信小程序装修", action: "设置" },
                ],
              },
              {
                name: "支付宝会员设置",
                hash: "ali-member-setting",
                permissions: [
                  { resourceId: "/设置/渠道/支付宝会员设置/支付宝会员初始化", action: "配置查看" },
                  { resourceId: "/设置/渠道/支付宝会员设置/支付宝小程序装修", action: "设置" },
                ],
              },
              {
                name: "美团到综授权",
                hash: "meituan",
                permissions: [{ resourceId: "/设置/渠道/美团到综授权", action: "配置维护" }],
              },
              {
                name: "美团到餐授权",
                hash: "meituan-restaurant",
                permissions: [{ resourceId: "/设置/渠道/美团到餐授权", action: "配置维护" }],
              },
              {
                name: "美团到综三方授权",
                hash: "meituan-sf",
                permissions: [{ resourceId: "/设置/渠道/美团到综三方授权", action: "配置维护" }],
              },
              {
                name: "美团到综平台授权",
                hash: "meituan-pt",
                permissions: [{ resourceId: "/设置/渠道/美团到综平台授权", action: "配置维护" }],
              },
              {
                name: "抖音会员授权",
                hash: "douyin-member",
                permissions: [{ resourceId: "/设置/渠道/抖音会员授权", action: "配置维护" }],
              },
              {
                name: "高德门店入驻",
                hash: "gaode-store",
                permissions: [{ resourceId: "/设置/渠道/高德门店入驻", action: "查看" }],
              },
            ],
          },
          {
            subTitle: "小程序装修",
            children: [
              {
                name: "页面管理",
                hash: "page-manage",
                permissions: [{ resourceId: "/设置/小程序装修/页面管理", action: "查看" }],
              },
              {
                name: "风格管理",
                hash: "style-manage",
                permissions: [{ resourceId: "/设置/小程序装修/风格管理", action: "查看" }],
              },
              {
                name: "微信导航设置",
                hash: "nav-manage-weixin",
                permissions: [{ resourceId: "/设置/小程序装修/微信导航设置", action: "查看" }],
              },
              {
                name: "支付宝导航设置",
                hash: "nav-manage-ali",
                permissions: [{ resourceId: "/设置/小程序装修/支付宝导航设置", action: "查看" }],
              },
              {
                name: "H5导航设置",
                hash: "nav-manage-h5",
                permissions: [{ resourceId: "/设置/小程序装修/H5导航设置", action: "查看" }],
              },
              {
                name: "进店规则",
                hash: "shop-entry-rule",
                permissions: [{ resourceId: "/设置/小程序装修/进店规则", action: "配置查看" }],
              },
            ],
          },
          {
            subTitle: "账号权限",
            children: [
              {
                name: "用户管理",
                hash: "user",
                permissions: [{ resourceId: "/设置/账号权限/用户管理", action: "查看" }],
              },
              {
                name: "角色管理",
                hash: "role",
                permissions: [{ resourceId: "/设置/账号权限/角色管理", action: "查看" }],
              },
            ],
          },
          {
            subTitle: "系统设置",
            children: [
              {
                name: "系统设置",
                hash: "set",
                permissions: [{ resourceId: "/设置/系统设置/系统设置", action: "查看" }],
              },
              {
                name: "国际化设置",
                hash: "i18n-set",
                permissions: [{ resourceId: "/设置/系统设置/国际化设置", action: "查看" }],
              },
            ],
          },
        ],
      },
    ];
  }
  fuc: any;

  constructor() {
    this.fuc = new Vue({ i18n, store });
  }

  format(id: string, str: string) {
    return this.fuc.$t(`m.pages.i18n[\"${id}/${str}\"]`);
  }
}

export default {
  MenusFuc,
  loadingOption,
};
