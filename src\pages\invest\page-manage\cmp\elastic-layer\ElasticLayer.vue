<!--
 * @Author: hl-cool <EMAIL>
 * @Date: 2024-07-25 17:33:52
 * @LastEditors: haiding <EMAIL>
 * @LastEditTime: 2025-03-13 17:29:47
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\cmp\elastic-layer\ElasticLayer.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-dialog :title="titleString" append-to-body :close-on-click-modal="false" :visible.sync="dialogShow" @close="canel"
    width="75%">
    <ListWrapper class="current-page">
      <template slot="query">
        <el-row>
          <el-col :span="8">
            <form-item :label="i18n('活动名称')">
              <el-input :placeholder="i18n('请输入活动名称')" v-model="query.nameLike" style="width: 90%" />
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="i18n('活动号')">
              <el-input :placeholder="i18n('请输入活动号')" v-model="query.numberEquals" style="width: 90%" />
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="i18n('页面状态')">
              <el-select style="width: 180px" v-model="query.stateEquals" :placeholder="i18n('请选择')">
                <el-option v-for="item in ContentTemplateState" :value="item.value" :label="item.label"
                  :key="item.value"></el-option>
              </el-select>
            </form-item>
          </el-col>
        </el-row>
        <el-row style="margin-top: 10px">
          <el-col :span="8">
            <form-item :label="i18n('活动主题')">
              <el-input :placeholder="i18n('请输入活动主题')" v-model="query.topicNameLikes" style="width: 90%" />
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="i18n('外部活动号')" :style="{'word-break': isEnglish ? 'keep-all' : 'normal'}">
              <el-input placeholder="" v-model="query.outerNumberIdLike" style="width: 90%" />
            </form-item>
          </el-col>
        </el-row>
        <el-row style="margin-top: 10px">
          <el-col :span="8">
            <form-item label="">
              <el-button class="btn-search" @click="doSearch" size="small" type="primary">{{ i18n('查询') }}</el-button>
              <el-button class="btn-reset" @click="doReset" size="small">{{ i18n('重置') }}</el-button>
            </form-item>
          </el-col>
        </el-row>
      </template>
      <template slot="list">
        <el-table v-if="dialogShow" :data="tableData" row-key="activityId" ref="table" @select="handleSelectionChange"
          @select-all="handleSelectAll" style="width: 100%; margin-top: 12px">
          <el-table-column type="selection" width="60" reserve-selection> </el-table-column>
          <el-table-column :label="i18n('活动名称')">
            <template slot-scope="scope">
              {{ scope.row.name || "--" }}
            </template>
          </el-table-column>
          <el-table-column :label="i18n('活动号')">
            <template slot-scope="scope">
              {{ scope.row.activityId || "--" }}
            </template>
          </el-table-column>
          <el-table-column :label="i18n('活动主题')">
            <template slot-scope="scope">
              {{ scope.row.topicName || "--" }}
            </template>
          </el-table-column>
          <el-table-column :label="i18n('券批次号')">
            <template slot-scope="scope">
              {{ scope.row.batchNumber || "--" }}
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template slot="page">
        <el-pagination :current-page.sync="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]"
          :total="page.total" background layout="total, prev, pager, next, sizes,  jumper"
          @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange"
          style="margin-top: 20px"></el-pagination>
      </template>
    </ListWrapper>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="canel">{{ i18n('取消') }}</el-button>
      <el-button size="small" type="primary" @click="confirm">{{ i18n('确定') }}</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./ElasticLayer.ts"></script>

<style lang="scss" scoped></style>
