<!--
 * @Author: 黎钰龙
 * @Date: 2023-02-24 15:42:02
 * @LastEditTime: 2024-04-24 13:36:57
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\benefit\equity-card\EquityCardList.vue
 * 记得注释
-->
<template>
  <div class="equity-card-list-container">
    <BreadCrume :panelArray="panelArray">
    </BreadCrume>
    <ListWrapper class="current-page" :showQuery="false">
      <template slot="list">
        <el-button @click="doCreateCard" size="small" type="primary">
          {{ i18n("新建权益卡") }}
        </el-button>
        <el-table :data="cardList" ref="table" style="width: 100%;margin-top: 15px">
          <el-table-column :label="i18n('权益卡名称')" prop="name">
            <template slot-scope="scope">
              <div>{{scope.row.name}}</div>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('有效期设置')" prop="expiryRules">
            <template slot-scope="scope">
              <ul>
                <div v-for="(item,index) in scope.row.expiryRules" :key="index">
                  <span class="li">·</span><span style="vertical-align:super">{{ValidityTime(item)}}</span>
                </div>
              </ul>
            </template>
          </el-table-column>
          <!-- <el-table-column :label="i18n('领卡奖励')" prop="giftBag" v-if="false">
            <template slot-scope="scope">
              <div v-if="hasGiftBag(scope.row)">
                <div v-if="scope.row.channels && scope.row.channels.length">
                  领卡渠道：
                  <div v-for="(item,index) in scope.row.channels" :key="index" style="margin-left: 20px">
                    【{{item.id}}】{{getChannelName(item.id + item.type)}}
                  </div>
                </div>
                <div v-if="scope.row.giftBag.points">送{{scope.row.giftBag.points}}积分</div>
                <div v-for="(item,index) in scope.row.giftBag.coupons" :key="index">送{{item.qty}}张
                  <el-button type="text" @click="doShowDialog(item)">{{item.couponTemplateName}}
                  </el-button>
                </div>
              </div>
              <div v-else>无</div>
            </template>
          </el-table-column> -->
          <el-table-column :label="i18n('领卡权益')" prop="equity">
            <template slot-scope="scope">
              <div>
                <div v-if="scope.row.equity.couponItems.length">
                  <i18n k="/会员/权益卡设置/领卡后每{0}发放一次，一共发{1}次">
                    <template slot="0">{{limitPartakeDateType(scope.row.equity.limitPartakeDateType,scope.row.equity.weekDay,scope.row.equity.dateInterval)}}</template>
                    <template slot="1">{{scope.row.equity.limitMemberPerTime}}</template>
                  </i18n>
                </div>
                <div v-else>{{i18n('/公用/券模板/无')}}</div>
                <div v-for="(item,index) in scope.row.equity.couponItems" :key="index">
                  <i18n k="/会员/等级/等级管理/已初始化状态的免费等级/表格/等级月礼/券赠送/送{0}张">
                    <template slot="0">{{item.qty}}</template>
                  </i18n>
                  <el-button type="text" @click="doShowDialog(item)">{{item.couponTemplateName}}
                  </el-button>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('/会员/会员资料/权益卡/状态')" prop="billNumber">
            <template slot-scope="scope">
              <div>{{showState(scope.row.state)}}</div>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('/会员/洞察/客群管理/列表页/操作')" prop="billNumber" width="140">
            <template slot-scope="scope">
              <el-button type="text" @click="doEdit(scope.$index)">{{i18n('/会员/会员资料/编辑')}}</el-button>
              <el-button type="text" @click="doSwitchState(scope.$index)"
                v-if="scope.row.state === 'stop' && permission.disableEnable">{{i18n('/公用/按钮/启用')}}</el-button>
              <el-button type="text" @click="doSwitchState(scope.$index)"
                v-if="scope.row.state === 'start' && permission.disableEnable">{{i18n('/设置/权限/用户管理/功基本信息/停用')}}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </ListWrapper>
    <SelectStoreActiveDtlDialog :dialogShow="dialogShow" @dialogClose="doDialogClose" :child="child">
    </SelectStoreActiveDtlDialog>
  </div>
</template>

<script lang="ts" src="./EquityCardList.ts">
</script>

<style lang="scss">
.equity-card-list-container {
  width: 100%;
  height: 100%;
  background: white;
  overflow: hidden;

  .current-page {
    height: calc(100% - 45px);
    overflow-y: auto;
    .el-select {
      width: 100%;
    }

    .list-wrapper-query {
      margin: 0 !important;
    }
    .line-blank {
      display: none;
    }

    .li {
      display: inline-block;
      font-size: 30px;
    }
  }
}
</style>