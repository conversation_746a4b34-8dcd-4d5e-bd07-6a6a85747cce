import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue';
import FormItem from 'cmp/formitem/FormItem.vue';
import MyQueryCmp from 'cmp/querycondition/MyQueryCmp.vue';
import I18nPage from 'common/I18nDecorator';
import { Component } from 'vue-property-decorator';
import AnalysisDateSelector from '../cmp/AnalysisDateSelector/AnalysisDateSelector.vue';
import ChannelSelect from 'cmp/channelselect/ChannelSelect.vue';
import SelectStores from 'cmp/selectStores/SelectStores.vue';
import MemberLineChart from '../cmp/MemberLineChart/MemberLineChart.vue';
import SelectEmployees from 'cmp/selectEmployees/selectEmployees.vue';
import OrgMemberAnalysisReportQuery from 'model/analysis/OrgMemberAnalysisReportQuery';
import AnalysisReportApi from 'http/analysis/AnalysisReportApi';
import CommonUtil from 'util/CommonUtil';
import DecimalFormatterUtil from 'util/DecimalFormatterUtil';
import AbstractLineChart from '../cmp/AbstractLineChart';
import DownloadCenterDialog from 'pages/main/dowmload_center_dialog/DownloadCenterDialog.vue';
import MemberPointsAnalysisReport from "model/analysis/MemberPointsAnalysisReport";
import MemberDepositAnalysisReport from "model/analysis/MemberDepositAnalysisReport";

class Filter {
  dataRange: any = null  //时间
  store: any = null  //门店
}

@Component({
  name: 'MemberDepositAnalysis',
  components: {
    BreadCrume,
    MyQueryCmp,
    FormItem,
    AnalysisDateSelector,
    ChannelSelect,
    SelectStores,
    MemberLineChart,
    SelectEmployees,
    DownloadCenterDialog
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/数据/会员增长分析',
    '/数据/会员积分分析',
    '/数据/会员储值分析'
  ],
  auto: true
})
export default class MemberDepositAnalysis extends AbstractLineChart {
  $refs: any
  panelArray: any = []
  filter: Filter = new Filter()
  detail: MemberDepositAnalysisReport = new MemberDepositAnalysisReport()
  downloadCenterFlag: boolean = false; //文件下载中心弹窗

  /* 每个item的第一项：数据名
  /  第二项：数据值
  /  第三项：是否展示在右y轴上 */
  get valueArray() {
    const arr = [
      [this.i18n('充值'), this.detail.rechargeAmountData, 0],
      [this.i18n('消费'), this.detail.consumeAmountData, 0],
      [this.i18n('调整增加'), this.detail.adjustAddAmountData, 0],
      [this.i18n('调整减少'), this.detail.adjustSubAmountData, 0],
      [this.i18n('转入'), this.detail.transferInAmountData, 0],
      [this.i18n('可用余额'), this.detail.usableTotalData, 0],
      [this.i18n('可用本金'), this.detail.usableBalanceData, 0],
      [this.i18n('可用赠金'), this.detail.usableGiftBalanceData, 0]
    ]
    return this.doTransValueArray(arr)
  }

  get summaryViewArr() {
    return [
      {
        label: this.i18n('充值'),
        value: DecimalFormatterUtil.formatNumber(this.detail.summary?.rechargeAmount),
      },
      {
        label: this.i18n('消费'),
        value: DecimalFormatterUtil.formatNumber(this.detail.summary?.consumeAmount),
      },
      {
        label: this.i18n('调整增加'),
        value: DecimalFormatterUtil.formatNumber(this.detail.summary?.adjustAddAmount),
      },
      {
        label: this.i18n('调整减少'),
        value: DecimalFormatterUtil.formatNumber(this.detail.summary?.adjustSubAmount),
      },
      {
        label: this.i18n('转入'),
        value: DecimalFormatterUtil.formatNumber(this.detail.summary?.transferInAmount),
      },
      {
        label: this.i18n('可用余额'),
        value: DecimalFormatterUtil.formatNumber(this.detail.summary?.usableTotal),
      },
      {
        label: this.i18n('可用本金'),
        value: DecimalFormatterUtil.formatNumber(this.detail.summary?.usableBalance),
      },
      {
        label: this.i18n('可用赠金'),
        value: DecimalFormatterUtil.formatNumber(this.detail.summary?.usableGiftBalance),
      }
    ]
  }

  created() {
    this.panelArray = [
      {
        name: this.i18n('/公用/菜单/会员储值分析'),
        url: ""
      }
    ]
  }

  mounted() {
    this.onSearch()
  }

  doExport() {
    this.$confirm(this.i18n("将根据当前查询条件生成报表，确认导出吗？"), this.i18n('/储值/预付卡/充值卡制售单/列表页面/导出'), {
      confirmButtonText: this.formatI18n("/公用/按钮", "确定") as string,
      cancelButtonText: this.formatI18n("/公用/按钮", "取消") as string,
    }).then(() => {
      const body = this.doFilterParams()
      AnalysisReportApi.exportMemberDepositReport(body).then((res) => {
        if (res.code === 2000) {
          this.downloadCenterFlag = true;
        } else {
          throw new Error(res.msg!)
        }
      }).catch((error) => {
        this.$message.error(error.message)
      })
    });
  }

  doReset() {
    this.filter = new Filter()
    this.$refs.analysisDateSelector.doReset()
    this.onSearch()
  }

  onSearch() {
    const body = this.doFilterParams()
    const loading = CommonUtil.Loading()
    AnalysisReportApi.memberDepositReport(body).then((res) => {
      if (res.code === 2000) {
        this.detail = res.data || new MemberDepositAnalysisReport()
      } else {
        throw new Error(res.msg as any)
      }
    }).catch((error) => {
      this.$message.error(error.message || this.i18n('内部异常'))
    }).finally(() => {
      loading.close()
    })
  }

  // 查询条件
  doFilterParams() {
    const params = new OrgMemberAnalysisReportQuery()
    params.dateUnitEquals = this.filter.dataRange?.type
    if (this.filter.dataRange?.date?.length) {
      params.startDate = this.filter.dataRange.date[0]
      params.endDate = this.filter.dataRange.date[1]
    }
    params.storeIdEquals = this.filter.store
    return params
  }

  doDateChange(value: any) { // {type: 'DAY' | 'WEEK' | 'MONTH', date:['2024-03-05','2024-03-06']}
    this.filter.dataRange = value
  }

  doDownloadDialogClose() {
    this.downloadCenterFlag = false;
  }

  get getSumUsableTotal() {
    let value = this.detail.summary?.sumUsableTotal
    if(value) {
      return  DecimalFormatterUtil.formatNumber(value);
    }
    return 0
  }

  get getSumUsableBalance() {
    let value = this.detail.summary?.sumUsableBalance
    if(value) {
      return  DecimalFormatterUtil.formatNumber(value);
    }
    return 0
  }

  get getSumUsableGiftBalance() {
    let value = this.detail.summary?.sumUsableGiftBalance
    if(value) {
      return  DecimalFormatterUtil.formatNumber(value);
    }
    return 0
  }

  // 需要显示百分号的数据名称
  get showPercentName() {
    return [this.i18n('新会员有消占比'), this.i18n('复购率')]
  }
};