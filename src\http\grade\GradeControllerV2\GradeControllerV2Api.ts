/*
 * @Author: 黎钰龙
 * @Date: 2025-04-02 18:43:10
 * @LastEditTime: 2025-04-29 10:20:09
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\grade\GradeControllerV2\GradeControllerV2Api.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import GradePurchaseRecords from 'model/default/GradePurchaseRecords'
import GradePurchaseRecordsFilter from 'model/default/GradePurchaseRecordsFilter'
import Response from 'model/default/Response'
import GradeRuleDataV2 from 'model/grade/GradeControllerV2/GradeRuleDataV2'

export default class GradeControllerV2Api {
  /**
   * 等级规则详情
   * 等级规则详情
   * 
   */
  static get(): Promise<Response<GradeRuleDataV2>> {
    return ApiClient.server().get(`/v2/grade/get`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 开启等级管理
   * 开启等级管理
   * 
   */
  static open(): Promise<Response<Nullable<GradeRuleDataV2>>> {
    return ApiClient.server().post(`/v2/grade/open`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 等级规则保存
   * 等级规则保存
   * 
   */
  static save(body: GradeRuleDataV2): Promise<Response<void>> {
    return ApiClient.server().post(`/v2/grade/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 等级付费记录查询
  * 等级付费记录查询。
  * 
  */
  static queryRecords(body: GradePurchaseRecordsFilter): Promise<Response<GradePurchaseRecords[]>> {
    return ApiClient.server().post(`/v2/grade/purchase/records`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
