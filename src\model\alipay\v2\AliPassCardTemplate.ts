export default class AliPassCardTemplate {
  // 内部模版id,新建时可不传
  innerId: Nullable<string> = null
  // 内部模版id,新建时可不传
  alipassTemplateid: Nullable<string> = null
  // 会员卡标题
  title: Nullable<string> = null
  // 会员卡logo url
  logo: Nullable<string> = null
  // 背景图片url
  background: Nullable<string> = null
  // 会员号显示方式
  mbrCodeType: Nullable<number> = null
  // 显示的权益类型，来源
  equityType: string[] = []
  // 是否显示会费
  showMbrfee: Nullable<boolean> = null
  // 是否显示权益说明
  showEquityRemark: Nullable<boolean> = null
  // 权益说明
  equityRemark: Nullable<string> = null
  // 会员开卡填写属性,字段定义{@link #MBR_ATTRIBUTE}列表,当值为0时表示不勾选，1时表示勾选
  mbrAttribute: Nullable<string> = null
}