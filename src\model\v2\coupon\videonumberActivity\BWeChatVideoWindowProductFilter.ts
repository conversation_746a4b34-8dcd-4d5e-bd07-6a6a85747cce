import { GoodsState } from 'model/v2/coupon/videonumberActivity/GoodsState'

export default class BWeChatVideoWindowProductFilter {
  // 活动号类似于
  numberLikes: Nullable<string> = null
  // 活动名称类似于
  nameLikes: Nullable<string> = null
  // 活动开始日期大于等于
  begin: Nullable<Date> = null
  // 活动开始日期小于
  end: Nullable<Date> = null
  // 上下架状态等于
  goodsStateEquals: Nullable<GoodsState> = null
  // 页数
  page: Nullable<number> = null
  // 页面大小
  pageSize: Nullable<number> = null
  // 营销中心
  marketingCenter: Nullable<string> = null
}
