import {Component, Prop} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import AbstractSelectDialog from './AbstractSelectDialog';
import RSOrg from 'model/common/RSOrg';
import ZoneApi from 'http/area/ZoneApi';
import ZoneFilter from 'model/datum/zone/ZoneFilter';

@Component({
  name: 'ZoneSelectorDialog',
  components: {
    FormItem
  }
})
export default class ZoneSelectorDialog extends AbstractSelectDialog<RSOrg> {
  zoneFilter: ZoneFilter = new ZoneFilter()
  // title: String = this.formatI18n('/公用/公共组件/门店选择弹框组件/标题/选择区域')
  title: String = this.formatI18n('/公用/门店组件/选择区域')
  @Prop({
    type: String,
    default: null
  })
  marketCenterSearch: string

  @Prop({
    type:Boolean,
    default: true
  })
  queryByMarketingCenter: Boolean

  
  reset() {
    this.zoneFilter = new ZoneFilter()
  }

  getId(ins: RSOrg): string {
    // @ts-ignore
    return ins.zone.id;
  }

  getName(ins: RSOrg): string {
    // @ts-ignore
    return ins.zone.name;
  }

  getResponseData(response: any): any {
    return response.data
  }

  queryFun(): Promise<any> {
    this.zoneFilter.page = this.page.currentPage - 1
    this.zoneFilter.pageSize = this.page.size
    this.zoneFilter.queryByMarketingCenter = this.queryByMarketingCenter
    let oldMarketCenter = sessionStorage.getItem('marketCenter') || ''
    if (this.marketCenterSearch) {
      sessionStorage.setItem('marketCenter', this.marketCenterSearch)
      console.log(this.marketCenterSearch);
    }
    return ZoneApi.query(this.zoneFilter).finally(()=>{
			sessionStorage.setItem('marketCenter', oldMarketCenter as string)
		});
  }
}
