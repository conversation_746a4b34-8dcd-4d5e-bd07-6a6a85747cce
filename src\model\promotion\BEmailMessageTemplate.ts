// 邮箱模板
export default class BEmailMessageTemplate {
  // 邮箱模板uuid
  uuid: Nullable<string> = null;
  // 模板标题
  title: Nullable<string> = null;
  // 备注
  remark: Nullable<string> = null;
  // 邮件标题
  emailTitle: Nullable<string> = null;
  // 内容
  content: Nullable<string> = null;
  // 创建时间
  createTime: Nullable<Date> = null;
  // 最后修改时间
  lastModifyTime: Nullable<Date> = null;
  // 操作人
  operator: Nullable<string> = null;
}
