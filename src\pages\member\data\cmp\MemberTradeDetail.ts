import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import MemberApi from "http/member_standard/MemberApi";
import TradeFilter from "model/member/TradeFilter";
import BMemberTrade from "model/member/BMemberTrade";
import DateUtil from "util/DateUtil";
import SelectStores from "cmp/selectStores/SelectStores";
import MemberDetail from "model/member_standard/MemberDetail";
import I18nPage from "common/I18nDecorator";
import MemberTab from "pages/member/data/cmp/MemberTab";
import QueryCondition from "cmp/querycondition/QueryCondition.vue";
import FormItem from "cmp/formitem/FormItem.vue";
import ListWrapper from "cmp/list/ListWrapper.vue";


@Component({
  name: "MemberTradeDetail",
  components: {
    SelectStores,
    MemberTab,
    QueryCondition,
    FormItem,
    ListWrapper,
  },
})
@I18nPage({
  prefix: [
    "/会员/会员资料",
    "/公用/菜单",
  ],
  auto: true,
})
export default class MemberTradeDetail extends Vue {

  @Prop()
  dtl: MemberDetail;

  @Watch("dtl", { immediate: true })
  dtlChanged() {
    this.getList();
  }

  get isStandardMember() {
    return this.$route.fullPath.indexOf("/standard-member") != -1;
  }

  get permissionResourceId() {
    return this.isStandardMember ? "/会员/会员管理/会员资料" : "/会员/会员管理/营销中心会员";
  }

  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10,
    probeEnabled: null,
  };
  query: TradeFilter = new TradeFilter();
  defaultDate: Date[] = [DateUtil.format(DateUtil.prevDate(new Date(), 89), "yyyy-MM-dd"), DateUtil.format(new Date(), "yyyy-MM-dd")];
  queryData: BMemberTrade[] = [];

  created() {
    this.query.tranTimeBetweenClosedOpen = this.defaultDate;
  }

  onSearch() {
    this.page.currentPage = 1;
    this.getList();
  }

  onReset() {
    this.query = new TradeFilter();
    this.page.currentPage = 1;
    // 今天减去90天
    this.query.tranTimeBetweenClosedOpen = this.defaultDate;
    this.getList();
  }

  private getList() {
    this.query.memberId = this.dtl.memberId;
    this.query.saleType = this.currentTabIndex == 0 ? ["sale"] : ["refund", "killSale"];
    this.query.page = this.page.currentPage - 1;
    this.query.pageSize = this.page.size;
    MemberApi.queryTrade(this.query).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.queryData = resp.data;
        // this.mockData();
        this.page.total = resp.total;
        this.page.probeEnabled = resp.fields ? resp.fields.probeEnabled : null;
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message);
      }
    });
  }

  mockData() {
    this.queryData = [{
      uuid: "fdsf111",
      tranTime: new Date(),
      tradeNo: "12342",
      channel: { id: "1", type: "meituan", typeId: "meituan1" },
      channelName: "meituan1",
      occurredOrgId: "12",
      occurredOrgName: "hheh",
      posNo: "pos111",
      stdAmount: 100,
      total: 60,
      favAmount: 40,
      sourceTradeId: { id: "223", namespace: null },
    }];
  }

  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val;
    this.getList();
  }

  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val;
    this.getList();
  }

  currentTabIndex: number = 0;
  tabs: Array<string> = [this.i18n("销售单"), this.i18n("售后单")];

  onTabChange(index: number) {
    this.currentTabIndex = index;
    this.onReset();
  }

  get isSale() {
    return this.currentTabIndex == 0;
  }

  onTradeNoClick(item: BMemberTrade, sale: boolean) {
    this.$router.push({
      name: this.isStandardMember ? "standard-member-trade-dtl" : "marketcenter-member-trade-dtl",
      query: {
        memberId: this.dtl.memberId,
        tradeId: item.tradeId?.id,
        namespace: item.tradeId?.namespace,
        sale: sale + "",
      },
    });
  }
}
