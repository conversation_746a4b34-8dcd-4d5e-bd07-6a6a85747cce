<template>
  <div class="page-paidMember-edit">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button
          size="large"
          type="primary"
          v-if="hasOptionPermission('/设置/小程序装修/页面管理', '编辑') && hasOptionPermission('/设置/小程序装修/页面管理', '发布')"
          @click="preserve(true)"
        >
          {{ i18n("保存并发布") }}
        </el-button>
        <el-button size="large" @click="preserve(false)">{{ i18n("/公用/按钮/保存") }}</el-button>
        <el-button size="large" @click="goBack">{{ i18n("取消") }}</el-button>
      </template>
    </BreadCrume>
    <!-- 设置 -->
    <div class="panel">
      <div class="panel-left">
        <el-form :model="propPaidMemberCards" :rules="rules" ref="form" label-width="120px">
          <!-- 页面设置 -->
          <div class="backDecoration">
            <div class="title">{{ i18n("页面设置") }}</div>
            <div class="content">
              <el-form-item :label="i18n('页面名称')" prop="propTitle" :rules="[{ required: true, message: '页面名称不能为空' }]">
                <el-input v-model="imgData.propTitle" placeholder="请输入页面名称" maxlength="8" @input="propPaidMemberCards.propTitle = imgData.propTitle " />
              </el-form-item>
              <el-form-item :label="i18n('选择权益卡')" prop="paidMemberCard">
                <el-select v-model="paidMemberCard" @change="paidMemberCardChange" filterable placeholder="选择权益卡名称">
                  <el-option v-for="item in cardList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="i18n('卡面元素')">
                <el-checkbox v-model="showCardNumber" @change="(val) => cardElementChange(val, 'showCardNumber')">
                  {{ i18n("隐藏卡号") }}
                </el-checkbox>
                <el-checkbox v-model="showPeriod" @change="(val) => cardElementChange(val, 'showPeriod')">
                  {{ i18n("隐藏有效期") }}
                </el-checkbox>
              </el-form-item>
              <el-form-item :label="i18n('会员权益')" prop="memberBenefits.imageType">
                <template v-slot:label>
                  {{ i18n("会员权益") }}
                  <el-tooltip placement="top-start" effect="light" style="padding:0 !important;">
                    <div slot="content">
                      {{ i18n("1. 默认图片：展示权益卡-权益说明字段关联的权益，包括权益图标和权益名称。") }}
                      <br />
                      {{ i18n("2. 自定义图片：展示自定义图片，不展示当前权益卡关联的权益") }}
                    </div>
                    <el-button style="padding: 0; border: none"><i class="el-icon-warning"
                        style="color: #999999" /></el-button>
                  </el-tooltip>
                </template>
                <el-radio-group v-model="propPaidMemberCards.memberBenefits.imageType">
                  <el-radio label="default">{{ i18n("默认图片") }}</el-radio>
                  <el-radio label="custom">{{ i18n("自定义图片") }}</el-radio>
                </el-radio-group>
                <div class="carousel" v-if="propPaidMemberCards.memberBenefits.imageType === 'custom'">
                  <div>{{ i18n("建议尺寸1041*480px，支持格式：png/jpg/jpeg/gif") }}</div>
                  <div class="becorationContent" style="display: flex">
                    <el-form-item prop="memberBenefits.notActivatedImage" style="width: 100px">
                      <div>未开通:</div>
                      <upload-img
                        v-model="propPaidMemberCards.memberBenefits.notActivatedImage"
                        :signature-result="credential"
                        width="80px"
                        height="80px"
                        :imgFormat="imgFormat"
                      ></upload-img>
                    </el-form-item>
                    <el-form-item prop="memberBenefits.activatedImage" style="width: 100px">
                      <div>已开通:</div>
                      <upload-img
                        v-model="propPaidMemberCards.memberBenefits.activatedImage"
                        :signature-result="credential"
                        width="80px"
                        height="80px"
                        :imgFormat="imgFormat"
                      ></upload-img>
                    </el-form-item>
                  </div>
                </div>
              </el-form-item>
            </div>
          </div>
          <!-- 推荐位设置 -->
          <div class="backDecoration">
            <div class="title">{{ i18n("营销位设置") }}</div>
            <div class="featured_first">
              <div class="switch">
                <el-switch v-model="propPaidMemberCards.marketingPosition.enabled"></el-switch>
                {{ i18n("启动推荐位") }}
              </div>
              <div v-show="propPaidMemberCards.marketingPosition.enabled">
                <el-button plain size="medium" @click="doAddFeaturedFirst" v-if="propPaidMemberCards.marketingPosition.items.length < 10">
                  +添加推荐位
                </el-button>
                <span class="position-tip">{{ i18n('最多添加10个推荐位') }}</span>
                <el-table :data="propPaidMemberCards.marketingPosition.items" style="width: 100%; margin-top: 12px">
                  <el-table-column prop="name" :label="i18n('推荐位名称')">
                    <template slot="header">
                      <span style="color: red">*</span> {{i18n('推荐位名称')}} 
                    </template>
                    <template slot-scope="scope">
                      <el-form-item
                        :prop="`marketingPosition.items.${scope.$index}.name`"
                        :rules="(propPaidMemberCards.marketingPosition.enabled && rules.propMarketingPositionName) || []"
                      >
                        <el-input v-model="scope.row.name" placeholder="请输入" maxlength="6" />
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column prop="prompt" :label="i18n('提示语')">
                    <template slot-scope="scope">
                      <el-form-item :prop="`marketingPosition.items.${scope.$index}.prompt`">
                        <el-input v-model="scope.row.prompt" placeholder="请输入" maxlength="6" />
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column :label="i18n('跳转链接')">
                    <template slot="header">
                      <span style="color: red">*</span> {{i18n('跳转链接')}} 
                    </template>
                    <template slot-scope="scope">
                      <div class="link-box">
                        <el-form-item
                          :prop="`marketingPosition.items.${scope.$index}.jumpPageInfo`"
                          :rules="(propPaidMemberCards.marketingPosition.enabled && rules.propMarketingPositionJumpPageInfo) || []"
                        >
                          <page-jump
                            ref="jumpPage"
                            v-model="scope.row.jumpPageInfo"
                            @change="changeLink($event, scope.row)"
                            :showTitile="true"
                          ></page-jump>
                        </el-form-item>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column :label="i18n('操作')">
                    <template slot-scope="scope">
                      <el-button
                        type="text"
                        size="small"
                        :disabled="propPaidMemberCards.marketingPosition.items.length <= 1"
                        @click="doRemoveProject(scope.$index)"
                      >
                        {{ i18n("移除") }}
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </el-form>
      </div>
      <div class="panel-right">
        <div class="pageBox page-place">
          <div class="page-place-box-top">
            {{ imgData.propTitle || '' }}
          </div>
          <div class="card-container">
            <!-- 顶部tab -->
            <div class="tab-scroll scroll-container">
              <div class="tab-container">
                <div class="tab-item active">权益卡名称1</div>
                <div class="tab-item">权益卡名称2</div>
                <div class="tab-item">权益卡名称3</div>
                <div class="tab-item">权益卡名称4</div>
              </div>
            </div>

            <div class="card-main">
              <div class="carousel-container">
                <div class="carousel-track">
                  <div class="item-content" v-for="(item, index) in 2" :key="index">
                    <div class="card-info" :style="{ backgroundImage: `url('${ cardItem.cardStyle && cardItem.cardStyle.picture || backgroundImage}')` }">
                      <div class="card-name">
                        <div class="name">{{ cardItem.name || '卡名称' }}</div>
                        <div class="level-box">
                          <span>{{ i18n("已领取") }}</span>
                        </div>
                      </div>
                      <div class="card-number">
                        <div v-if="propPaidMemberCards.cardElement.showCardNumber" style="opacity: 0.4">卡号: {{cardItem.code || '84946515164466'}}</div>
                        <div v-if="propPaidMemberCards.cardElement.showPeriod" class="period">有效期至: 2025-12-31</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 权益标签 -->
              <div class="equity-box">
                <div class="equity-title" v-if="propPaidMemberCards.memberBenefits.imageType === 'default'">可享权益</div>
                <div class="equity-container">
                  <img src="@/assets/image/invest/entitlement_img.png" alt="" v-if="propPaidMemberCards.memberBenefits.imageType === 'default'" />
                  <img :src="propPaidMemberCards.memberBenefits.activatedImage" alt="" v-else />
                </div>
              </div>

              <!-- 续费优惠 -->
              <div class="renew-title">续费享优惠</div>
              <div class="plan-scroll scroll-container">
                <div class="plan-container">
                  <div class="plan-item">
                    <div class="duration">1天</div>
                    <div class="points">1积分</div>
                  </div>
                  <div class="plan-item active">
                    <div class="duration">30天</div>
                    <div class="points">4积分</div>
                    <div class="outline"><span>6积分</span></div>
                  </div>
                  <div class="plan-item">
                    <div class="duration">90天</div>
                    <div class="points">30积分</div>
                    <div class="outline"><span>60积分</span></div>
                  </div>
                  <div class="plan-item">
                    <div class="duration">365天</div>
                    <div class="points">30积分</div>
                    <div class="outline"><span>60积分</span></div>
                  </div>
                </div>
              </div>

              <!-- 使用须知 -->
              <div class="notice" style="margin-bottom: 12px">
                <div class="notice-item" style="padding: 8px 0">
                  <span class="name">使用须知</span>
                  <span class="hint_tip">!</span>
                </div>
              </div>

              <!-- 推荐位 -->
              <div class="notice" v-if="propPaidMemberCards.marketingPosition.enabled && propPaidMemberCards.marketingPosition.items.length">
                <div class="notice-item" v-for="(item, index) in propPaidMemberCards.marketingPosition.items" :key="index">
                  <span class="name">{{ item.name }}</span>
                  <span class="hint">{{ item.prompt }} ></span>
                </div>
              </div>
            </div>

            <!-- 底部操作栏 -->
            <div class="action-bar">
              <div class="price">
                <div>
                  优惠后实付
                  <span>4积分</span>
                </div>
                <div class="o-price">原价6积分</div>
              </div>
              <button class="btn">立即领卡</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./PageBenefitCardEdit.ts">
</script>

<style lang="scss" scoped>
.page-paidMember-edit {
  width: 100%;

  .panel {
    border-radius: 8px;
    padding: 24px;
    display: flex;

    .panel-left {
      width: 750px;
      margin-right: 12px;

      .featured_first {
        .switch {
          font-size: 14px;
          color: #24272b;
          margin: 12px 0 8px;
        }
        .position-tip {
          font-size: 12px;
          color: #bbb;
          margin-left: 10px;
        }
        .el-form-item {
          transform: translateY(6px);
        }
        ::v-deep .el-form-item__content {
          margin-left: 0 !important;
        }
      }
    }

    .title {
      font-weight: 600;
      font-size: 16px;
      color: #242633;
      line-height: 24px;
    }

    .backDecoration {
      padding: 26px 20px;
      margin-bottom: 12px;
      border-radius: 8px;
      background: #ffffff;
    }

    .content {
      margin-left: 68px;
      margin-top: 6px;
    }

    .carousel {
      background: #f0f2f6;
      border-radius: 4px;
      padding: 12px;
      margin-top: 20px;
      font-weight: 400;
      font-size: 13px;
      color: #a1a6ae;
      line-height: 18px;
    }

    .panel-right {
      position: relative;
      border-radius: 8px;
      background: #ffffff;
      padding: 30px 25px;
      width: 400px;
      height: 764px;
      box-sizing: border-box;

      // 可享权益
      .equity-box {
        padding: 10px;
        background: #fff;
        border-radius: 10px;

        .equity-title {
          font-size: 15px;
          font-weight: 600;
        }

        .equity-container {
          display: flex;
          justify-content: space-between;
          align-items: center;
          // height: 90px;
          width: 100%;
          overflow-x: auto;
          padding: 10px 0;
          img {
            // height: 100%;
            max-width: 100%;
            pointer-events: none;
            object-fit: fill;
          }

          // &::-webkit-scrollbar {
          //   width: 3px;
          // }
        }
      }

      .pageBox {
        width: 100%;
        height: 100%;
        background: #f0f2f6;
        border: 8px solid #000000;
        border-radius: 50px;
        box-sizing: border-box;
        overflow: hidden;
        position: relative;
      }

      .page-place {
        display: flex;
        width: 100%;
        height: 100%;
        flex-direction: column;

        &-box {
          flex: 1;
          width: 400px;
          margin: auto;
          overflow: hidden;
          height: 100%;

          &-top {
            height: 74px;
            color: #242633;
            font-size: 16px;
            font-weight: 600;
            line-height: 111px;
            text-align: center;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-color: #fdf1da;
          }
        }
      }

      .carousel-container {
        overflow: hidden;
        position: relative;
        width: 334px;
        min-height: 170px;
        margin: 0 auto;
      }

      .carousel-track {
        display: flex;
        height: 100%;
        align-items: center;
        position: relative;
      }

      .item-content {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: #333;
        pointer-events: none;
        margin-right: 12px;
        img {
          width: 100%;
          height: 100%;
        }
      }

      .card-info {
        width: 290px;
        min-height: 130px;
        padding: 15px;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        position: relative;
        border-radius: 10px;

        .card-name {
          display: flex;
          align-items: center;

          .name {
            font-size: 19px;
            font-weight: 600;
          }

          .level-box {
            position: absolute;
            border-radius: 15px;
            font-size: 12px;
            top: 8px;
            right: 8px;
            padding: 4px 10px;
            background: #171619;
            color: #fff4da;
          }

          .level {
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 13px;
          }
        }

        .card-number {
          font-size: 12px;
          line-height: 16px;
          margin-bottom: 20px;
        }
        .period {
          position: absolute;
          bottom: 20px;
        }
      }

      // 公共滑动容器样式
      .scroll-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        white-space: nowrap;

        &::-webkit-scrollbar {
          // display: none;
        }
      }

      .card-container {
        max-width: 375px;
        background: #fff;
        font-family: system-ui;
        padding-bottom: 100px;
        overflow-y: auto;
        &::-webkit-scrollbar {
          display: none;
          /* 隐藏滚动条 */
        }
        // 顶部tab
        .tab-scroll {
          .tab-container {
            display: inline-flex;
            gap: 24px;
            padding: 0 16px;
            padding-bottom: 8px;
            background: linear-gradient(to bottom, #fdf1da, #ffffff);

            .tab-item {
              font-size: 16px;
              color: #666;
              padding: 8px 0;

              &.active {
                color: #333;
                font-weight: 500;
                border-bottom: 3px solid #ecc070;
              }
            }
          }
        }

        .card-main {
          padding: 16px;
          background: #f0f2f6;
        }

        .card-scroll {
        }

        .card-msg {
          width: 250px;
          height: 74px;
          background-size: 100% 100%;
          background-repeat: no-repeat;
        }

        // 权益标签
        .benefit-scroll {
          margin: 16px 0;

          .benefit-tags {
            display: inline-flex;
            gap: 12px;

            .tag {
              padding: 6px 12px;
              background: #f5f5f5;
              border-radius: 15px;
              font-size: 14px;
            }
          }
        }

        .renew-title {
          font-size: 15px;
          font-weight: 600;
          margin: 16px 0 0;
        }
        // 续费计划
        .plan-scroll {
          margin: 12px 0;

          .plan-container {
            display: inline-flex;
            gap: 4px;

            .plan-item {
              padding: 12px;
              border: 1px solid #eee;
              border-radius: 8px;
              text-align: center;
              background: #fff;
              position: relative;
              overflow: hidden;
              .duration {
                font-size: 14px;
                margin-bottom: 8px;
              }

              .points {
                color: #30302e;
                font-weight: 600;
                font-size: 20px;

                span {
                  font-size: 12px;
                }
              }
              .outline {
                font-size: 12px;
                text-decoration: line-through;
              }
            }

            .active {
              background: linear-gradient(to right, #f2d6a3, #f7e7be);
              &::after {
                content: "√"; /* 显示勾选符号 */
                position: absolute;
                height: 12px;
                bottom: 0px;
                right: 0px;
                font-size: 12px;
                color: white;
                background-color: #553c2c;
                padding: 5px;
                border-radius: 5px 0 0 5px;
              }
            }
          }
        }

        // 底部操作栏
        .action-bar {
          display: flex;
          justify-content: space-between;
          margin-top: 24px;
          position: absolute;
          bottom: 0;
          width: 100%;
          height: 100px;
          background: #fff;
          padding: 12px 12px;
          .price {
            font-size: 14px;
            font-weight: 500;
            height: 45px;
            width: 62%;
            margin-right: -10px;
            background: #30302e;
            border-radius: 12px;
            color: #b5b5b3;
            padding-left: 10px;
            padding-top: 5px;
            span {
              color: #f3daa5;
            }
            .o-price {
              font-size: 12px;
            }
          }

          .btn {
            background: linear-gradient(to right, #f2d6a3, #faefda);
            color: #866e4e;
            border: none;
            width: 40%;
            height: 45px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
          }
        }

        // 使用须知
        .notice {
          background: #fff;
          border-radius: 10px;
          padding: 0 12px;

          margin-top: 10px;

          .notice-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            font-size: 14px;
            border-bottom: 1px solid #f2f2f2;

            .hint {
              color: #999;
            }
            .hint_tip {
              width: 12px;
              height: 12px;
              border-radius: 50%;
              border: 1px solid;
              line-height: 12px;
              text-align: center;
              font-size: 10px;
            }
          }

          .notice-item:last-child {
            border-bottom: none;
          }
        }
      }
    }
  }
}
</style>