export default class DistrictFilter {
  // 代码等于
  codeEquals: Nullable<string> = null
  // 代码起始于
  codeStartsWith: Nullable<string> = null
  // 名称等于
  nameEquals: Nullable<string> = null
  // 名称起始于
  nameStartsWith: Nullable<string> = null
  // 名称等于
  nameLikes: Nullable<string> = null
  // 类型等于，取值：PROVINCE——；CITY——市；COUNTY——区；STREET——街道
  typeEquals: Nullable<string> = null
  // 类型在...中，取值：PROVINCE——；CITY——市；COUNTY——区；STREET——街道
  typeIn: string[] = []
  // 所属路径等于
  pathEquals: Nullable<string> = null
  // 上级代码等于
  parentEquals: Nullable<string> = null
  // 
  page: Nullable<number> = null
  // 
  pageSize: Nullable<number> = null
}