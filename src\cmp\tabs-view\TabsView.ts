/*
 * @Author: 黎钰龙
 * @Date: 2024-04-10 11:16:45
 * @LastEditTime: 2024-04-12 16:02:57
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\tabs-view\TabsView.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  name: 'TabsView',
  components: {}
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/营销/大转盘活动'
  ],
  auto: true
})
export default class TabsView extends Vue {
  @Prop({ type: Array, default: [] }) tabList: any[];

  currentGoodsTab: number = 0

  doClickTabs(index:number) {
    this.currentGoodsTab = index
  }
};