import GiftInfo from "model/common/GiftInfo";

export default class InviteRegisterRule {
	// 每邀请一个会员激活成功，邀请者可以获得礼包设置
	inviterGiftPerInvitation: Nullable<GiftInfo> = null;
	// 被邀请者是否同时获得赠礼
	registerMemberSameGift: Nullable<boolean> = null;
	// 邀请会员阈值，超过得额外赠礼
	inviteNumber: Nullable<number> = null;
	// 额外礼包设置
	additionalGift: Nullable<GiftInfo> = null;
	// 邀请会员类型 PAID/FREE
	inviteMemberType: Nullable<string> = null;
	// 被邀请人获得赠礼额单独设置
	acceptInviterGiftPerInvitation: Nullable<GiftInfo> = null;
	// 赠礼与邀请者是否相同
	acceptInviterSameGift: Nullable<Boolean> = null
}
