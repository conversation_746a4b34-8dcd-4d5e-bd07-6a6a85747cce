<template>
  <div class="coupon-template-list">
    <BreadCrume :panelArray="panelArray" :fontSize="18">
      <template slot="operate">
        <div class="tag_button">
          <div class="tag_button_box" @click="doLabelCouponTemplate" v-if="hasOptionPermission('/券/券管理/券模板', '券模板标签管理')">
            <img src="~assets/image/icons/ic_coupon_tag.png" style="width: 20px; height: 20px">
            <span>{{ formatI18n("/权益/券/券模板/券模板标签管理") }}</span>
          </div>
          <el-button v-if="hasOptionPermission('/券/券管理/券模板', '券模板维护')" @click="doAddCouponTemplate" size="large" type="primary"
            style="background:#007eff">{{ formatI18n("/权益/券/券模板/新建券模板") }}</el-button>
          <el-button v-if="hasOptionPermission('/券/券管理/券模板', '批量导出')" @click="exportFile" size="large"
                     >{{ formatI18n("/资料/门店/批量导出") }}</el-button>
          <el-button v-if="hasOptionPermission('/券/券管理/券模板', '券模板维护')" @click="importFile" size="large"
          >{{ formatI18n("/权益/券/券模板/批量导入") }}</el-button>
        </div>
      </template>
    </BreadCrume>
    <ListWrapper class="current-page">
      <template slot="query">
        <QueryCondition @reset="doReset" @search="doSearch">
          <el-row>
            <el-col :span="8">
              <form-item :label="formatI18n('/营销/券礼包活动/券查询', '券名称')">
                <el-input :placeholder="formatI18n('/公用/查询条件/提示/类似于')" v-model="query.nameLikes"></el-input>
              </form-item>
            </el-col>
            <el-col :span="8">
              <form-item :label="formatI18n('/权益/券/券模板/券模板号')">
                <el-input :placeholder="
                  formatI18n(
                    '/营销/券礼包活动/券查询/券号输入框placeholder/等于'
                  )
                " v-model="query.numberEquals"></el-input>
              </form-item>
            </el-col>
            <el-col :span="8">
              <form-item :label="formatI18n('/权益/券/券模板/券模板状态')">
                <el-select :placeholder="formatI18n('/权益/券/券模板/不限')" v-model="query.stateEquals">
                  <el-option :label="formatI18n('/权益/券/券模板/不限')" value>{{
                  formatI18n("/权益/券/券模板/不限")
                }}</el-option>
                  <el-option :label="
                    formatI18n('/权益/券/券模板/券模板状态下拉选项/未生效')
                  " value="NOT_EFFECTED">{{
                    formatI18n("/权益/券/券模板/券模板状态下拉选项/未生效")
                  }}</el-option>
                  <el-option :label="
                    formatI18n('/权益/券/券模板/券模板状态下拉选项/已生效')
                  " value="EFFECTED">{{
                    formatI18n("/权益/券/券模板/券模板状态下拉选项/已生效")
                  }}</el-option>
                  <el-option :label="
                    formatI18n('/权益/券/券模板/券模板状态下拉选项/已作废')
                  " value="CANCELLED">{{
                    formatI18n("/权益/券/券模板/券模板状态下拉选项/已作废")
                  }}</el-option>
                  <el-option :label="
                    formatI18n('/公用/过滤器/未审核')
                  " v-if="auditPermission" value="INITIAL">{{
                    formatI18n('/公用/过滤器/未审核')
                  }}</el-option>
                  <el-option :label="
                    formatI18n('/营销/券礼包活动/券查询/券状态下拉选项/已过期')
                  " value="EXPIRED">{{
                    formatI18n('/营销/券礼包活动/券查询/券状态下拉选项/已过期')
                  }}</el-option>
                </el-select>
              </form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <form-item :label="formatI18n('/权益/券/券模板/外部券模板号')">
                <el-input :placeholder="
                  formatI18n(
                    '/营销/券礼包活动/券查询/券号输入框placeholder/等于'
                  )
                " v-model="query.outerNumberIdEquals"></el-input>
              </form-item>
            </el-col>
            <el-col :span="8">
              <form-item :label="formatI18n('/公用/券模板/券适用')">
                <div class="inline-select">
                  <el-select v-model="queryParamsLike" @change="queryParamsLikeChange">
                    <el-option :label="formatI18n('/公用/活动/状态/全部')" value="">
                      {{ formatI18n("/公用/活动/状态/全部") }}
                    </el-option>
                    <el-option :label="formatI18n('/公用/菜单/区域')" value="zone">
                      {{ formatI18n("/公用/菜单/区域") }}
                    </el-option>
                    <el-option :label="formatI18n('/资料/门店/门店')" value="store">
                      {{ formatI18n("/资料/门店/门店") }}
                    </el-option>
                  </el-select>
                  <el-select v-if="queryParamsLike === 'zone'" v-model="query.zoneLikes">
                    <el-option v-for="(item, index) in zoneList" :key="index" :label="getIdName(item.zone)"
                      :value="item.zone.id">{{ getIdName(item.zone) }}</el-option>
                  </el-select>
                  <template v-if="queryParamsLike === 'store'">
                    <SelectStores v-model="query.storeLikes" :isOnlyId="true" :hideAll="true" width="300px"
                      :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
                    </SelectStores>
                  </template>
                </div>
              </form-item>
            </el-col>
            <el-col :span="8">
              <form-item :label="formatI18n('/公用/券模板/用券商品')">
                <div class="inline-select">
                  <el-select v-model="goodsQueryLike" @change="goodsQueryChange">
                    <el-option :label="formatI18n('/公用/活动/状态/全部')" value="">
                      {{ formatI18n("/公用/活动/状态/全部") }}
                    </el-option>
                    <el-option :label="formatI18n('/公用/券模板/商品代码或名称')" value="goods">
                      {{ formatI18n("/公用/券模板/商品代码或名称") }}
                    </el-option>
                    <el-option :label="formatI18n('/公用/券模板/品类代码或名称')" value="category">
                      {{ formatI18n("/公用/券模板/品类代码或名称") }}
                    </el-option>
                    <el-option :label="formatI18n('/公用/券模板/品牌代码或名称')" value="brand">
                      {{ formatI18n("/公用/券模板/品牌代码或名称") }}
                    </el-option>
                  </el-select>
                  <el-select v-if="goodsQueryLike" v-model="query.goodsLikes" filterable remote :remote-method="selectRemoteFilter" :loading="loading"
                    :placeholder="formatI18n('/会员/洞察/客群管理/列表页/搜索')">
                    <el-option v-for="(item, index) in goodsList" :key="index"
                      :label="goodsQueryLike === 'goods' ? getCodeName(item) : getIdName(item[goodsQueryLike])"
                      :value="goodsQueryLike === 'goods' ? item.code : item[goodsQueryLike].id">{{ goodsQueryLike === 'goods' ? getCodeName(item) : getIdName(item[goodsQueryLike]) }}</el-option>
                  </el-select>
                </div>
              </form-item>
            </el-col>
          </el-row>
          <template slot="opened">
            <el-row>
              <el-col :span="8">
                <form-item :label="formatI18n('/营销/券礼包活动/券查询', '券名称')">
                  <el-input :placeholder="formatI18n('/公用/查询条件/提示/类似于')" v-model="query.nameLikes"></el-input>
                </form-item>
              </el-col>
              <el-col :span="8">
                <form-item :label="formatI18n('/权益/券/券模板/券模板号')">
                  <el-input :placeholder="
                  formatI18n(
                    '/营销/券礼包活动/券查询/券号输入框placeholder/等于'
                  )
                " v-model="query.numberEquals"></el-input>
                </form-item>
              </el-col>
              <el-col :span="8">
                <form-item :label="formatI18n('/权益/券/券模板/券模板状态')">
                  <el-select :placeholder="formatI18n('/权益/券/券模板/不限')" v-model="query.stateEquals">
                    <el-option :label="formatI18n('/权益/券/券模板/不限')" value>{{
                  formatI18n("/权益/券/券模板/不限")
                }}</el-option>
                    <el-option :label="
                    formatI18n('/权益/券/券模板/券模板状态下拉选项/未生效')
                  " value="NOT_EFFECTED">{{
                    formatI18n("/权益/券/券模板/券模板状态下拉选项/未生效")
                  }}</el-option>
                    <el-option :label="
                    formatI18n('/权益/券/券模板/券模板状态下拉选项/已生效')
                  " value="EFFECTED">{{
                    formatI18n("/权益/券/券模板/券模板状态下拉选项/已生效")
                  }}</el-option>
                    <el-option :label="
                    formatI18n('/权益/券/券模板/券模板状态下拉选项/已作废')
                  " value="CANCELLED">{{
                    formatI18n("/权益/券/券模板/券模板状态下拉选项/已作废")
                  }}</el-option>
                    <el-option :label="
                    formatI18n('/公用/过滤器/未审核')
                  " v-if="auditPermission" value="INITIAL">{{
                    formatI18n('/公用/过滤器/未审核')
                  }}</el-option>
                    <el-option :label="
                    formatI18n('/营销/券礼包活动/券查询/券状态下拉选项/已过期')
                  " value="EXPIRED">{{
                    formatI18n('/营销/券礼包活动/券查询/券状态下拉选项/已过期')
                  }}</el-option>
                  </el-select>
                </form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <form-item :label="formatI18n('/权益/券/券模板/外部券模板号')">
                  <el-input :placeholder="
                  formatI18n(
                    '/营销/券礼包活动/券查询/券号输入框placeholder/等于'
                  )
                " v-model="query.outerNumberIdEquals"></el-input>
                </form-item>
              </el-col>
              <el-col :span="8">
                <form-item :label="formatI18n('/公用/券模板/券适用')">
                  <div class="inline-select">
                    <el-select v-model="queryParamsLike" @change="queryParamsLikeChange">
                      <el-option :label="formatI18n('/公用/活动/状态/全部')" value="">
                        {{ formatI18n("/公用/活动/状态/全部") }}
                      </el-option>
                      <el-option :label="formatI18n('/公用/菜单/区域')" value="zone">
                        {{ formatI18n("/公用/菜单/区域") }}
                      </el-option>
                      <el-option :label="formatI18n('/资料/门店/门店')" value="store">
                        {{ formatI18n("/资料/门店/门店") }}
                      </el-option>
                    </el-select>
                    <el-select v-if="queryParamsLike === 'zone'" v-model="query.zoneLikes">
                      <el-option v-for="(item, index) in zoneList" :key="index" :label="getIdName(item.zone)"
                        :value="item.zone.id">{{ getIdName(item.zone) }}</el-option>
                    </el-select>
                    <template v-if="queryParamsLike === 'store'">
                      <SelectStores v-model="query.storeLikes" :isOnlyId="true" :hideAll="true" width="300px"
                        :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
                      </SelectStores>
                  </template>
                  </div>
                </form-item>
              </el-col>
              <el-col :span="8">
                <form-item :label="formatI18n('/公用/券模板/用券商品')">
                  <div class="inline-select">
                    <el-select v-model="goodsQueryLike" @change="goodsQueryChange">
                      <el-option :label="formatI18n('/公用/活动/状态/全部')" value="">
                        {{ formatI18n("/公用/活动/状态/全部") }}
                      </el-option>
                      <el-option :label="formatI18n('/公用/券模板/商品代码或名称')" value="goods">
                        {{ formatI18n("/公用/券模板/商品代码或名称") }}
                      </el-option>
                      <el-option :label="formatI18n('/公用/券模板/品类代码或名称')" value="category">
                        {{ formatI18n("/公用/券模板/品类代码或名称") }}
                      </el-option>
                      <el-option :label="formatI18n('/公用/券模板/品牌代码或名称')" value="brand">
                        {{ formatI18n("/公用/券模板/品牌代码或名称") }}
                      </el-option>
                    </el-select>
                    <el-select v-if="goodsQueryLike" v-model="query.goodsLikes" filterable remote :remote-method="selectRemoteFilter"
                      :loading="loading" :placeholder="formatI18n('/会员/洞察/客群管理/列表页/搜索')">
                      <el-option v-for="(item, index) in goodsList" :key="index"
                        :label="goodsQueryLike === 'goods' ? getCodeName(item) : getIdName(item[goodsQueryLike])"
                        :value="goodsQueryLike === 'goods' ? item.code : item[goodsQueryLike].id">{{ goodsQueryLike === 'goods' ? getCodeName(item) : getIdName(item[goodsQueryLike]) }}</el-option>
                    </el-select>
                  </div>
                </form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <form-item :label="formatI18n('/营销/券礼包活动/核销第三方券/用券说明')">
                  <el-input :placeholder="formatI18n('/公用/查询条件/提示/类似于')" v-model="query.remarkLikes"></el-input>
                </form-item>
              </el-col>
              <el-col :span="8">
                <form-item :label="formatI18n('/公用/券模板', '标签')">
                  <el-select v-model="mustHaveTagUuids" multiple collapse-tags placeholder="请选择">
                    <el-option v-for="item in tagList" :key="item.uuid" :label="item.value" :value="item.uuid">
                    </el-option>
                  </el-select>
                </form-item>
              </el-col>
              <!-- <el-col :span="8">
            <form-item
                :label="formatI18n('/储值/预付卡/充值卡制售单/列表页面/创建人')"
                
            >
              <el-input
                  :placeholder="
                  formatI18n(
                    '/公用/查询条件/提示/类似于'
                  )
                "
                  v-model="query.creatorIdLikes"
              ></el-input>
            </form-item>
          </el-col> -->
            </el-row>
          </template>
        </QueryCondition>
      </template>
      <template slot="list">
        <el-tabs @tab-click="doHandleClick" v-model="activeName">
          <el-tab-pane :label="getAllCount" name="first"></el-tab-pane>
          <el-tab-pane :label="getAllCashCouponCount" name="second"></el-tab-pane>
          <el-tab-pane :label="getAllDiscountCouponCount" name="forth"></el-tab-pane>
          <el-tab-pane :label="getSpecialCouponCount" name="special"></el-tab-pane>
          <el-tab-pane :label="getPickUpDiscountCouponCount" name="seven"></el-tab-pane>
          <el-tab-pane :label="getFreightCouponCount" name="eight"></el-tab-pane>
          <el-tab-pane v-if="showRandom" :label="getRandomCouponCount" name="nine"></el-tab-pane>
          <el-tab-pane :label="getExchangeCouponCount" name="ten"></el-tab-pane>
          <el-tab-pane v-if="showPoint" :label="getPointCouponCount" name="eleven"></el-tab-pane>
          <el-tab-pane v-if="showEquity" :label="getEquityCouponCount" name="twelve"></el-tab-pane>
        </el-tabs>
        <div class="option-btn" style="margin-bottom: 5px">
          <i18n k="/公用/券模板/已选择{0}个券模板">
            <span class="select-num" slot="0">{{selectedArr.length}}</span>
          </i18n>
          <el-button @click="doBatchAudit" style="margin-left: 14px"
            v-if="hasOptionPermission('/券/券管理/券模板', '券模板维护') && auditPermission && hasOptionPermission('/券/券管理/券模板', '券模板审核')">
            {{ formatI18n('/营销/券礼包活动/券礼包活动/批量审核') }}
          </el-button>
          <el-button @click="showStore" v-if="hasOptionPermission('/券/券管理/券模板', '券模板维护')">
            {{formatI18n("/公用/券模板/批量调整用券门店")}}
          </el-button>
        </div>
        <el-table v-loading="loading" ref="table" :data="tableData" @selection-change="handleSelectionChange" style="width: 100%; margin-top: 10px">
          <el-table-column type="selection" width="55"> </el-table-column>
          <el-table-column width="180" :label="formatI18n('/权益/券/券模板/券模板/券名称')" prop="name">
            <template slot-scope="scope">
              <div>{{ scope.row.number | strFormat }}</div>
              <!--v-if="hasCheckPermission(scope.row.type)"-->
              <div :title="scope.row.name" @click="doToDtl(scope.row)" style="
                  color: #20a9ff;
                  text-align: left;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  cursor: pointer;
                ">
                {{ scope.row.name }}
              </div>
            </template>
          </el-table-column>
          <el-table-column width="120" :label="formatI18n('/营销/券礼包活动/核销第三方券', '券类型')" prop="type">
            <template slot-scope="scope">{{
              scope.row.type | lineCouponType
            }}</template>
          </el-table-column>
          <el-table-column width="144" :label="formatI18n('/储值/会员储值/储值调整单/列表/最后修改时间')" prop="lastModified">
            <template slot-scope="scope">
              <span no-i18n>{{ scope.row.lastModified | dateFormate3 }}</span>
            </template>
          </el-table-column>
          <el-table-column width="120" :label="formatI18n('/权益/券/券模板/状态')" prop="type">
            <template slot-scope="scope">
              <div class="state-block" v-if="scope.row.state === 'EFFECTED'">
                <div class="state-color" style="background-color: #0CC66D"></div>
                {{ formatI18n("/权益/券/券模板/已生效") }}
              </div>
              <div class="state-block" v-if="scope.row.state === 'NOT_EFFECTED'">
                <div class="state-color" style="background-color: #FFAA00"></div>
                {{ formatI18n("/权益/券/券模板/未生效") }}
              </div>
              <div class="state-block" v-if="scope.row.state === 'CANCELLED'">
                <div class="state-color" style="background-color: #A1B0C8"></div>
                {{ formatI18n("/权益/券/券模板/券模板状态下拉选项/已作废") }}
              </div>
              <div class="state-block" v-if="scope.row.state === 'EXPIRED'">
                <div class="state-color" style="background-color: #A1B0C8"></div>
                {{ formatI18n("/营销/券礼包活动/券查询/券状态下拉选项/已过期") }}
              </div>
              <div class="state-block" v-if="scope.row.state === 'INITIAL'">
                <div class="state-color" style="background-color: #FFAA00"></div>
                {{ formatI18n('/公用/过滤器/未审核') }}
              </div>
            </template>
          </el-table-column>
          <el-table-column width="126" :label="i18n('券面额/折扣力度/特价')" prop="faceAmountOrDiscount">
            <template slot-scope="scope">
              {{showFaceInfo(scope.row)}}
            </template>
          </el-table-column>
          <el-table-column width="136" :label="formatI18n('/公用/券模板', '券有效期')" prop="validityType" align="center">
            <template slot-scope="scope">
              {{
                scope.row.validityType === "RALATIVE"
                  ? formatI18n("/公用/券模板", "相对有效期")
                  : formatI18n("/公用/券模板", "固定有效期")
              }}
            </template>
          </el-table-column>
          <el-table-column width="136" :label="formatI18n('/公用/券模板', '用券渠道')" prop="channels">
            <template slot-scope="scope">
              <div style="
                  text-align: left;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  width: 100px;
                " :title="getChannel(scope.row.channels)">
                {{ getChannel(scope.row.channels) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column width="136" :label="formatI18n('/公用/券模板', '用券门店')" prop="storeRangeType">
            <template slot-scope="scope">
              <!--{{scope.row.storeRangeType === 'ALL' ? formatI18n('/公用/券模板', '全部门店') : (scope.row.storeRangeType === 'PART' ? formatI18n('/权益/积分/积分初始化/未初始化状态/得积分规则/基础得积分/点击立即设置', '指定门店适用') : formatI18n('/权益/积分/积分初始化/未初始化状态/得积分规则/基础得积分/点击立即设置', '指定门店不适用'))}}-->
              {{
                scope.row.storeRangeType === "ALL"
                  ? formatI18n("/公用/券模板", "全部门店")
                  : scope.row.storeRangeType === "PART" &&
                    scope.row.allMarketingCenterStores === true
                  ? formatI18n("/公用/券模板", "全部门店")
                  : formatI18n("/储值/预付卡/卡模板/列表页面/部分门店")
              }}
            </template>
          </el-table-column>
          <el-table-column width="160" :label="formatI18n('/公用/券模板', '标签')" prop="tabs">
            <template slot-scope="scope">
              <el-tooltip class="tabs_tooltip" placement="top" effect="light"
                :disabled="scope.row.templateTag == null || scope.row.templateTag.length <= 1">
                <el-button type="text" style="color:#1F375D;cursor:default">
                  <span :title="getTemplateTag(scope.row.templateTag)">{{ getTemplateTag(scope.row.templateTag) }}</span>
                  <span v-if="scope.row.templateTag && scope.row.templateTag.length > 1" style="font-size:18px">...</span>
                </el-button>
                <div slot="content">
                  <span v-for="item in scope.row.templateTag" :key="item.tagUuid">{{item.tagValue}}<br /></span>
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column width="136" :label="formatI18n('/储值/预付卡/充值卡制售单/列表页面', '创建人')" prop="creator">
            <template slot-scope="scope">
              <div style="
                  text-align: left;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  width: 100px;
                " :title="scope.row.creator">
                {{ scope.row.creator }}
              </div>
            </template>
          </el-table-column>
          <el-table-column width="136" :label="formatI18n('/储值/预付卡/充值卡制售单/列表页面', '最后修改人')" prop="lastModifier">
            <template slot-scope="scope">
              <div style="
                  text-align: left;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  width: 100px;
                " :title="scope.row.lastModifier">
                {{ scope.row.lastModifier }}
              </div>
            </template>
          </el-table-column>
          <el-table-column :label="formatI18n('/权益/券/券模板/外部券模板号')" prop="type" width="156">
            <template slot-scope="scope">
              <!-- <span v-if="scope.row.outerNumberNamespace === 'weimob'">{{ formatI18n('/权益/券/券模板/微盟') }}</span>
              <span v-if="scope.row.outerNumberNamespace === 'wxMerchant'">{{ formatI18n('/权益/券/券模板/微信') }}</span>-->
              <div style="word-break:break-all" v-if="
                  scope.row.outerRelations &&
                    scope.row.outerRelations.length === 1
                ">
                {{ tableConfigTicket(scope.row.outerRelations) }}
              </div>
              <el-tooltip v-if="
                  scope.row.outerRelations &&
                    scope.row.outerRelations.length > 1
                " class="item" effect="light" placement="right">
                <el-button type="text">查看更多</el-button>
                <div slot="content">
                  <div v-for="(item, index) in tableConfigTicket(
                      scope.row.outerRelations
                    )" :key="index">
                    {{ item }}
                  </div>
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column width="120" :label="formatI18n('/公用/预约文件列表', '操作')" fixed="right">
            <template slot-scope="scope">
              <div style="display:flex;flex-direction:column;justify-content:center;height:51px">
                <el-row style="height:20px">
                  <span class="split-line" v-if="hasOptionPermission('/券/券管理/券模板', '券模板维护')">
                    <span class="span-btn" @click="doCopy(scope.row)">{{formatI18n("/公用/按钮/复制")}}</span>
                  </span>
                  <span class="split-line" v-if="
                  scope.row.state !== 'CANCELLED' &&
                    hasOptionPermission('/券/券管理/券模板', '券模板维护') &&
                    scope.row.scope !== 'weixin'
                ">
                    <span class="span-btn" @click="doModify(scope.row)">{{formatI18n("/公用/按钮/修改")}}</span>
                  </span>
                </el-row>
                <el-row>
                  <span class="split-line" v-if="
                  scope.row.state !== 'CANCELLED' &&
                  hasOptionPermission('/券/券管理/券模板', '券模板作废') &&
                  scope.row.scope !== 'weixin'
                ">
                    <span class="span-btn" @click="doCancel(scope.row)">{{formatI18n("/资料/区域/作废")}}</span>
                  </span>
                  <span class="split-line" v-if="
                  (scope.row.state === 'INITIAL'  || scope.row.state === 'NOT_EFFECTED')&&
                    hasOptionPermission('/券/券管理/券模板', '券模板维护') &&
                    auditPermission &&  hasOptionPermission('/券/券管理/券模板', '券模板审核') &&
                    scope.row.scope !== 'weixin'
                ">
                    <span class="span-btn" @click="doAudit(scope.row)">{{formatI18n("/公用/按钮/审核")}}</span>
                  </span>
                </el-row>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <!--分页栏-->
      <template slot="page">
        <el-pagination :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
          @current-change="onHandleCurrentChange" background @size-change="onHandleSizeChange"
          layout="total, prev, pager, next, sizes,  jumper"></el-pagination>
      </template>
    </ListWrapper>
    <el-dialog :title="formatI18n('/公用/券模板/批量调整用券门店')" :visible.sync="storeShow" width="30%" :before-close="storeClose">
      <el-form ref="selectStoreForm" label-width="100px" :model="selectStoreForm">
        <el-form-item>
          <div style="color: rgb(128, 128, 128)">
            {{ formatI18n('/公用/券模板/仅对指定门店适用或指定门店不适用生效') }}
          </div>

        </el-form-item>
        <el-form-item :label="formatI18n('/公用/券模板/增加用券门店')">
          <el-input @focus="showSelectStoreDialog" v-model="storeStr" style="width: 200px"></el-input>
          {{ getStoreCount(selectedStores.length) }}
        </el-form-item>
        <el-form-item :label="formatI18n('/公用/券模板/减少用券门店')">
          <el-input @focus="showSelectDeductStoreDialog" v-model="deductStoreStr" style="width: 200px"></el-input>
          {{ getStoreCount(selectedDeductStores.length) }}
        </el-form-item>
        <el-form-item prop="inputValue" :rules="inputValueRules" class="auto-expand-form-item"></el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="storeClose">{{
          formatI18n("/公用/按钮/取消")
        }}</el-button>
        <el-button type="primary" @click="storeConfirm">{{
          formatI18n("/公用/按钮/确定")
        }}</el-button>
      </span>
    </el-dialog>
    <StoreSelectorDialog ref="storeSelectDialog" :enableStore="true" @summit="doSubmitGoods">
    </StoreSelectorDialog>
    <StoreSelectorDialog ref="storeSelectDialog1" :enableStore="true" @summit="doSubmitGoods2">
    </StoreSelectorDialog>
    <UploadFileModal :dialogShow="uploadDialogShow" @dialogClose="doDialogClose" @upload-success="doUploadSuccess">
    </UploadFileModal>
    <DownloadCenterDialog :dialogvisiable="fileDialogVisible" :showTip="showTip" @dialogClose="doDownloadDialogClose">
    </DownloadCenterDialog>
  </div>
</template>

<script lang="ts" src="./CouponTemplateList.ts"></script>

<style lang="scss">
.coupon-template-list {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: hidden;
  .tag_button {
    display: flex;
    justify-content: flex-end;
    align-content: center;
    .tag_button_box {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #309af6;
      line-height: 20px;
      align-items: center;
      margin-right: 32px;
      cursor: pointer;
      user-select: none;
      .icon_ic_tag {
        width: 20px;
        height: 20px;
        margin-right: 4px;
      }
    }
  }

  .current-page {
    height: calc(100% - 48px);
    overflow: auto;

    .el-select {
      width: 100%;
    }
  }

  .el-range-editor.el-input__inner {
    width: 100%;
  }
}

.state-block {
  display: flex;
  /*justify-content:center;*/
  align-items: center;

  .state-color {
    height: 6px;
    width: 6px;
    border-radius: 10px;
    margin-right: 5px;
  }
}
.inline-select {
  display: flex;
}
.tabs_tooltip {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>

<style lang="scss" scoped>
::v-deep .el-button {
  display: inline-flex;
  align-content: center;
}
::v-deep .option-btn > .el-button--medium {
  font-size: 13px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #242633;
}
::v-deep .el-button > .ic_coupon_tag {
  font-size: 20px;
  background-size: cover;
  background: url("~assets/image/icons/ic_coupon_tag.png") no-repeat;
}
::v-deep .el-button > .ic_coupon_tag:before {
  content: "替";
  font-size: 20px;
  visibility: hidden;
}
::v-deep .split-line {
  padding-right: 12px;
  .el-button--medium {
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #007eff;
  }
}
</style>