import {Component, Prop, Vue} from 'vue-property-decorator'
import InMemPage from "cmp/in-mem-page/InMemPage";

@Component({
    name: 'StoresDtl',
    components: {
        InMemPage
    }
})
export default class StoresDtl extends Vue {
    @Prop({
        type: Array,
        default: () => []
    })
    data: []
    @Prop({
        type: Boolean,
        default: false
    })
    hideButton: boolean
    dialogVisible: boolean = false
    doQuery(data: any[], filter: any) {
        if (filter.codeName == null || filter.codeName.trim() === '') {
            return data
        }
        return data.filter((e: any) => {
            return e.id.indexOf(filter.codeName) > -1 || e.name.indexOf(filter.codeName) > -1
        })
    }
}
