<template>
  <div class="member-balance-limit-setting-edit-container">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button v-if="hasOptionPermission('/储值/储值管理/储值限额设置','配置维护')" @click="doSave" type="primary" size="large">
          {{i18n('保存')}}
        </el-button>
        <el-button @click="doCancel" size="large">
          {{i18n('取消')}}
        </el-button>
      </template>
    </BreadCrume>

      <el-tabs class="main-tabs" style="width: 100%" @tab-click="handleTabClick" v-model="activeName">
        <el-tab-pane :label="formatI18n('单次充值限额')" name="memberSingleRechargeLimit"> 
          <el-form :model="ruleForm" ref="form" label-width="120px">
            <div class="form-block" v-for="(item,index) in ruleForm.data" :key="index">
                <div class="block-header">
                <div class="block-title">
                    <i18n k="/卡/卡充值限额设置/设置{0}">
                    <template slot="0">{{index + 1}}</template>
                    </i18n>
                </div>
                <div style="color: #FA5050; cursor: pointer" @click="doRemove(index)" v-if="ruleForm.data.length > 1">
                    <i class="el-icon-delete"></i>
                    {{i18n('删除')}}
                </div>
                </div>
                <el-form-item :label="i18n('充值渠道')" :prop="`data[${index}].rechargeChannels`" :rules="rules.rechargeChannels">
                <ChannelSelect v-model="ruleForm.data[index].rechargeChannels" :multiple="true" :filterHideId="selectedChannels(index)">
                </ChannelSelect>
                </el-form-item>
                <el-form-item :label="i18n('单次充值限额')" :prop="`data[${index}].singleRechargeLimit`" :rules="rules.singleRechargeLimit">
                <el-checkbox v-model="ruleForm.data[index].singleRechargeLimit.minLimit.hasMinLimit" @change="doCheckLimitChange(index,'min')">
                    {{i18n('最小充值金额')}}
                </el-checkbox>
                <el-form-item :prop="`data[${index}].singleRechargeLimit.minLimit`" style="display:inline-block;margin:0 10px 0 -25px"
                    :rules="rules.minAmount">
                    <AutoFixInput :min="0.01" :max="99999.99" :fixed="2" style="width: 132px" :appendTitle="i18n('元')"
                    v-model="ruleForm.data[index].singleRechargeLimit.minLimit.minAmount" @change="doCheckLimitChange(index,'min')"
                    :disabled="!ruleForm.data[index].singleRechargeLimit.minLimit.hasMinLimit">
                    </AutoFixInput>
                </el-form-item>
                <el-checkbox v-model="ruleForm.data[index].singleRechargeLimit.maxLimit.hasMaxLimit" @change="doCheckLimitChange(index,'max')">
                    {{i18n('最大充值金额')}}
                </el-checkbox>
                <el-form-item :prop="`data[${index}].singleRechargeLimit.maxLimit`" style="display:inline-block;margin:0 10px 0 -25px"
                    :rules="rules.maxAmount">
                    <AutoFixInput :min="0" :max="99999.99" :fixed="2" style="width: 132px" :appendTitle="i18n('元')"
                    v-model="ruleForm.data[index].singleRechargeLimit.maxLimit.maxAmount" @change="doCheckLimitChange(index,'max')"
                    :disabled="!ruleForm.data[index].singleRechargeLimit.maxLimit.hasMaxLimit">
                    </AutoFixInput>
                </el-form-item>
                </el-form-item>
            </div>
            </el-form>
            <div class="add-btn" @click="doAdd">
            + {{i18n('添加设置')}}
            </div>
        </el-tab-pane>
       <!-- 账户限额 -->
        <el-tab-pane :label="formatI18n('账户限额')" name="memberBalanceAccountLimit">
            <el-form :model="memberBalanceAccountLimitConfig" label-width="140px" :rules="memberBalanceAccountLimitRule" ref="memberBalanceAccountLimitConfig">
                <el-form-item :label="formatI18n('账户余额最大值')">
                      <AutoFixInput :min="0" :max="99999.99" :fixed="2" style="width: 132px" :appendTitle="i18n('元')"
                      v-model="memberBalanceAccountLimitConfig.maxAmount">
                      </AutoFixInput>
                </el-form-item>
            </el-form>
        </el-tab-pane>
      </el-tabs>
  </div>
</template>

<script lang="ts" src="./member-balance-limit-setting-edit.ts">
</script>

<style lang="scss" scoped>
.member-balance-limit-setting-edit-container {
  width: 100%;
  padding-bottom: 20px;
  .form-block {
    width: 100%;
    background: #ffffff;
    border-radius: 8px;
    margin-bottom: 16px;
    padding: 24px;
    .block-header {
      display: flex;
      justify-content: space-between;
      .block-title {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 16px;
        color: #222222;
        line-height: 20px;
        margin-bottom: 12px;
      }
    }
  }
  .add-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    border: 1px dashed #318bff;
    background: #ffffff;
    height: 44px;
    text-align: center;
    font-family: PingFangSC, PingFang SC;
    font-size: 14px;
    color: #318bff;
    line-height: 20px;
    font-style: normal;
    cursor: pointer;
  }
}
</style>