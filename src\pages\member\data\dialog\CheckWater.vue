<template>
    <el-dialog :before-close="doBeforeClose" :close-on-click-modal="false"
               :title="title"
               :visible.sync="dialogShow" append-to-body class="check-water-view">
        <div class="wrap">
            <el-row class="header" style="padding-left: 20px">
                <el-col :span="12">
                    <div :label="formatI18n('/会员/会员资料', '姓名')" class="primary" style="font-size: 18px">
                        {{formatI18n('/会员/会员资料', '姓名')}}: {{detail.mbrName}}
                    </div>
                    <el-row class="secondary">
                        <el-col :span="12">
                            {{formatI18n('/会员/会员资料', '会员号')}}: {{detail.crmCode}}
                        </el-col>
                        <el-col :span="12">
                            {{formatI18n('/会员/会员资料', '手机号')}}: {{detail.mobile}}
                        </el-col>
                    </el-row>
                </el-col>
                <el-col :span="12">
                    <div class="primary">
                        <span v-if="enableMultipleAccount">{{account|idName}} &nbsp;&nbsp; </span><span style="font-size: 18px">{{formatI18n('/会员/会员资料/详情界面/会员资产/储值余额有值时点击/当前储值余额：')}}</span>
                        <span style="font-size: 18px">{{detail.total|amount}}</span>
                    </div>
                    <el-row class="secondary">
                        <el-col :span="12">
                            {{formatI18n('/会员/会员资料/详情界面/会员资产/储值余额有值时点击/实充余额：')}}{{detail.balance|amount}}
                        </el-col>
                        <el-col :span="12">
                            {{formatI18n('/会员/会员资料/详情界面/会员资产/储值余额有值时点击/返现余额：')}}{{detail.giftBalance|amount}}
                        </el-col>
                    </el-row>
                </el-col>
            </el-row>


            <div class="all-total">
                <el-block-panel>
                    <el-block-panel-item>
                        <div class="height-80">
                            <p class="primary" style="font-weight: bold">{{formatI18n('/会员/会员资料/详情界面/会员资产/储值余额有值时点击/累计充值：')}}{{detail.totalRecharge|amount}}{{formatI18n('/公用/券模板/元')}}</p>
                            <p class="secondary">{{formatI18n('/会员/会员资料/详情界面/会员资产/储值余额有值时点击/累计充值/实充增加：')}}{{detail.realRecharge|amount}}{{formatI18n('/公用/券模板/元')}}</p>
                            <p class="secondary">{{formatI18n('/会员/会员资料/详情界面/会员资产/储值余额有值时点击/累计充值/返现增加：')}}{{detail.giftRecharge|amount}}{{formatI18n('/公用/券模板/元')}}</p>
                        </div>
                    </el-block-panel-item>
                    <el-block-panel-item>
                        <div class="height-80">
                            <p class="primary" style="font-weight: bold">{{formatI18n('/会员/会员资料/详情界面/会员资产/储值余额有值时点击/累计消费：')}}{{Math.abs(detail.totalConsume)|amount}}{{formatI18n('/公用/券模板/元')}}</p>
                            <p class="secondary">{{formatI18n('/会员/会员资料/详情界面/会员资产/储值余额有值时点击/实充扣减：')}}{{Math.abs(detail.realConsume)|amount}}{{formatI18n('/公用/券模板/元')}}</p>
                            <p class="secondary">{{formatI18n('/会员/会员资料/详情界面/会员资产/储值余额有值时点击/返现扣减：')}}{{Math.abs(detail.giftConsume)|amount}}{{formatI18n('/公用/券模板/元')}}</p>
                        </div>
                    </el-block-panel-item>
                    <el-block-panel-item>
                        <div class="height-80">
                            <p class="primary" style="font-weight: bold;word-break: break-word">{{formatI18n('/会员/会员资料/详情界面/会员资产/储值余额有值时点击/累计消费退款：')}}{{Math.abs(detail.totalRefund)|amount}}{{formatI18n('/公用/券模板/元')}}</p>
                            <p class="secondary">{{formatI18n('/会员/会员资料/详情界面/会员资产/储值余额有值时点击/累计消费退款/实充增加：')}}{{Math.abs(detail.realRefund)|amount}}{{formatI18n('/公用/券模板/元')}}</p>
                            <p class="secondary">{{formatI18n('/会员/会员资料/详情界面/会员资产/储值余额有值时点击/累计消费退款/返现增加：')}}{{Math.abs(detail.giftRefund)|amount}}{{formatI18n('/公用/券模板/元')}}</p>
                        </div>
                    </el-block-panel-item>
                </el-block-panel>
            </div>


            <ListWrapper class="current-page">
                <template slot="list">
                    <el-table
                            :data="transactions"
                            style="margin-top: 20px">
                        <el-table-column :label="formatI18n('/会员/会员资料/详情界面/会员资产/储值余额有值时点击/表格/交易时间')" fixed prop="occurredTime" width="150">
                            <template slot-scope="scope">
                                {{scope.row.occurredTime|yyyyMMddHHmmss}}
                            </template>
                        </el-table-column>
                        <el-table-column :label="formatI18n('/会员/会员资料/详情界面/会员资产/储值余额有值时点击/表格/发生组织')" fixed prop="occurredOrg.id" width="150">
                            <template slot-scope="scope">
                                {{scope.row.occurredOrg | idName}}
                            </template>
                        </el-table-column>
                        <el-table-column :label="formatI18n('/会员/会员资料/详情界面/会员资产/储值余额有值时点击/表格/交易类型')" fixed prop="category" width="150">
                            <template slot-scope="scope">
                                {{parseCategory(scope.row.category)}}
                            </template>
                        </el-table-column>
                        <el-table-column :label="formatI18n('/会员/会员资料/详情界面/会员资产/储值余额有值时点击/表格/原余额（元）')"  align="right" prop="originalAmount" width="200">
                            <template slot-scope="scope">
                                {{scope.row.originalAmount + scope.row.originalGiftAmount|amount}}
                            </template>
                        </el-table-column>
                        <el-table-column :label="formatI18n('/会员/会员资料/详情界面/会员资产/储值余额有值时点击/表格/发生金额（元）')"  align="right" prop="totalAmount" width="200">
                            <template slot-scope="scope">
                                <span style="color: red" v-if="scope.row.totalAmount >= 0">+{{scope.row.totalAmount|amount}}</span>
                                <span style="color: green" v-if="scope.row.totalAmount < 0">{{scope.row.totalAmount|amount}}</span>
                            </template>
                        </el-table-column>
                        <el-table-column :label="formatI18n('/会员/会员资料/详情界面/会员资产/储值余额有值时点击/表格/发生后余额（元）')"  align="right" prop="amount" width="200">
                            <template slot-scope="scope">
                                {{scope.row.originalAmount + scope.row.originalGiftAmount +
                                scope.row.totalAmount|amount}}
                            </template>
                        </el-table-column>
                        <el-table-column :label="formatI18n('/会员/会员资料/详情界面/会员资产/储值余额有值时点击/表格/交易流水号')" prop="transNo" width="250"/>
                    </el-table>
                </template>
                <!--分页栏-->
                <template slot="page">
                    <el-pagination
                            :current-page="page.currentPage"
                            :page-size="page.size"
                            :page-sizes="[10, 20, 30, 40]"
                            :total="page.total"
                            @current-change="onHandleCurrentChange"
                            @size-change="onHandleSizeChange"
                            background
                            layout="total, prev, pager, next, sizes,  jumper">
                    </el-pagination>
                </template>
            </ListWrapper>
        </div>
        <div class="dialog-footer" slot="footer">
            <el-button @click="doCancel">{{formatI18n('/公用/按钮', '取消')}}</el-button>
            <el-button @click="doModalClose" size="small" type="primary">{{formatI18n('/公用/按钮', '确定')}}</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts" src="./CheckWater.ts">
</script>

<style lang="scss">
.check-water-view{
    display: flex;
    align-items: center;
    justify-content: center;
    .wrap{
        height: 445px;
        overflow: auto;
        .item{
            width: 228px;
            height: 108px;
            border-radius: 5px;
            display: inline-block;
            margin-bottom: 24px;
            background: rgba(0, 0, 0, 0.0470588235294118);
            margin-right: 10px;
            .content{
                text-align: center;
            }
        }
    }
    .el-dialog{
        width: 1200px !important;
        height: 600px !important;
    }
}
</style>