<template>
  <div>
    <el-dialog :title="formatI18n('/系统/上导航/密码修改弹框/标题/修改密码')" width="450px" :close-on-click-modal="false" :visible.sync="visible" @close="onCancel" class="change-password-dialog">
      <el-form ref="changePwdForm" :model="ruleForm" :rules="changeRules" label-width="100px">
        <el-form-item :label="formatI18n('/系统/上导航/密码修改弹框/原密码')" prop="oldPassword">
          <el-input type="password" v-model="ruleForm.oldPassword" :placeholder="formatI18n('/系统/上导航/密码修改弹框/原密码/提示/请输入原密码')"></el-input>
        </el-form-item>
        <el-form-item :label="formatI18n('/系统/上导航/密码修改弹框/新密码')" prop="newPassword">
          <el-input type="password" v-model="ruleForm.newPassword" :placeholder="formatI18n('/系统/上导航/密码修改弹框/新密码/提示/请输入新密码')">
            <el-tooltip slot="append" placement="top" effect="light" v-if="loginInfo.useStrict">
              <i class="el-icon-warning"/>
              <span slot="content">
                {{formatI18n('/系统/上导航/密码修改弹框/1.密码不能少于8位；')}}<br/>
                {{formatI18n('/系统/上导航/密码修改弹框/2.密码需包括数字、字母、特殊字符3种字符；')}}<br/>
                {{formatI18n('/系统/上导航/密码修改弹框/3.不能与原密码相同；')}}<br/>
                {{formatI18n('/系统/上导航/密码修改弹框/4.不能和用户名相同；')}}
              </span>
            </el-tooltip>
          </el-input>
        </el-form-item>
        <el-form-item :label="formatI18n('/系统/上导航/密码修改弹框/确认新密码')" prop="confirmNewPassword">
          <el-input type="password" v-model="ruleForm.confirmNewPassword" :placeholder="formatI18n('/系统/上导航/密码修改弹框/确认新密码/提示/请再次输入新密码')"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="onCancel">{{formatI18n('/公用/按钮', '取消')}}</el-button>
        <el-button type="primary" @click="onConfirm">{{formatI18n('/公用/按钮', '确定')}}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" src="./ChangePwd.ts"></script>

<style lang="scss">
  .change-password-dialog {
    display: flex;
    align-items: center;
    justify-content: center;
    .el-input-group__prepend {
      padding: 0 10px!important;
    }
    .el-dialog {
      .el-dialog__header{
        .el-dialog__headerbtn { top: 13px;}
      }
      .el-dialog__body{
        height: auto;
        padding: 20px 20px 0 20px;
      }
      .el-dialog__footer {
        height: auto;
        .dialog-footer {
          height: 50px;
          line-height: 50px;
        }
      }
      .el-input {
        width: 250px;
      }
    }
  }
</style>