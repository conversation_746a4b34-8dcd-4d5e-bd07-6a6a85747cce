/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:19
 * @LastEditTime: 2025-05-06 18:53:58
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmd\service.js
 * 记得注释
 */
import I18nApi from '../http/i18n/I18nApi'
import i18n from '../locale/Locale'
import store from '../store'
import CommonUtil from '../util/CommonUtil'
import pagnation_zh from '../locale/lang/zh/component.js'
import pagnation_en from '../locale/lang/en/component.js'
import pagnation_tw from '../locale/lang/tw/component.js';
import UrlParamUtil from "util/UrlParamUtil";
import LoginApi from "http/login/LoginApi";
import SysConfigApi from "http/config/SysConfigApi";
import { LocalStorage, SessionStorage } from 'mgr/BrowserMgr'

export default class I18nData {
  static init () {
    return new Promise((async (resolve,reject) => {
      let locale = CommonUtil.getLocale('locale')
      if (!locale) {
        locale = 'zh_CN'
      }
      try {
        if (UrlParamUtil.getParams(location.href).get('cors-token')) {
          // store.commit('updateToken', { temporaryToken: UrlParamUtil.getParams(location.href).get('cors-token') })
          await LoginApi.refreshToken(UrlParamUtil.getParams(location.href).get('cors-token') || '')
          this.uniGetSysConfig()
          this.getLanguageNames()
          SessionStorage.setItem('isUniLogin', true)
        }
        const resp = await I18nApi.gets(locale)
        if (resp && resp.code === 2000) {
          i18n.locale = locale
          let page_resouce = ''
          if (locale === 'zh_CN') {
            page_resouce = pagnation_zh
          } else if(locale === 'zh_TW') {
            page_resouce = pagnation_tw
          } else {
            page_resouce = pagnation_en
          }
          i18n.setLocaleMessage(locale, {
            el: page_resouce,
            m: {
              pages: {
                i18n: resp.data
              }
            }
          })
          store.commit('i18n', resp.data)
          resolve()
        } else {
          throw new Error(resp.message)
        }
      } catch(error) {
        reject(error)
      }
    }))
  }

  //uni登录的情况下，跳过了登录流程，需要设定LocalStorage
  static uniGetSysConfig () {
    console.log('uniGetSysConfig');
    SysConfigApi.get()
      .then((resp) => {
        if (resp && resp.code === 2000) {
          let data = resp.data;
          LocalStorage.clearItem('sysConfig')
          LocalStorage.setItem("sysConfig", data);
          console.log('uni模式设定LocalStorage', LocalStorage.getItem("sysConfig"));
        }
      })
      .catch((error) => {
        // this.$message.error(error.message)
      });
  }

  static getLanguageNames () {
    sessionStorage.setItem("locale", 'zh_CN')
  }
}