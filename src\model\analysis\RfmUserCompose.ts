/*
 * @Author: 黎钰龙
 * @Date: 2025-01-15 10:23:26
 * @LastEditTime: 2025-01-15 10:23:44
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\analysis\RfmUserCompose.ts
 * 记得注释
 */
export default class RfmUserCompose {
  // 标签值
  tagValue: Nullable<string> = null
  // 覆盖人数
  coveredCount: Nullable<number> = null
  // 覆盖率
  coveredPercentage: Nullable<number> = null
  // 人均消费间隔
  avgTransInterval: Nullable<number> = null
  // 人均消费次数
  avgTradeQty: Nullable<number> = null
  // 消费次数
  totalTradeQty: Nullable<number> = null
  // 人均消费金额
  avgTradeAmount: Nullable<number> = null
  // 消费金额
  totalTradeAmount: Nullable<number> = null
  // 消费间隔
  totalTransInterval: Nullable<number> = null
}