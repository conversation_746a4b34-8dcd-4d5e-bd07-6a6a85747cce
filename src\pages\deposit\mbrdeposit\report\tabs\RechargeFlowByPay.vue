<template>
  <div class="recharge-flow-by-pay">
    <div no-i18n>
      <el-radio-group v-model="date">
        <el-radio-button :label="i18n('今天')"></el-radio-button>
        <el-radio-button :label="i18n('昨天')"></el-radio-button>
        <el-radio-button :label="i18n('近7天')"></el-radio-button>
        <el-radio-button v-if="!daqiaoshihuaDingkai" :label="i18n('近30天')"></el-radio-button>
        <el-radio-button v-if="!daqiaoshihuaDingkai" :label="i18n('近90天')"></el-radio-button>
        <el-radio-button v-if="!daqiaoshihuaDingkai" :label="i18n('自定义')"></el-radio-button>
        <el-date-picker
        v-if="!daqiaoshihuaDingkai"
          style="position: relative;top: 5px;left: 10px"
          @change="doDateChange"
          :end-placeholder="i18n('结束日期')"
          format="yyyy-MM-dd"
          range-separator="-"
          :clearable="false"
          size="small"
          :start-placeholder="i18n('起始日期')"
          type="daterange"
          v-model="createCouponDate"
          value-format="yyyy-MM-dd"
        ></el-date-picker>
      </el-radio-group>
    </div>
    <div>
      <ListWrapper>
        <template slot="query">
          <QueryCondition @reset="onReset" @search="onSearch" @toggle="doToggle">
            <el-row>
              <el-col :span="8">
                <form-item label="充值门店">
                  <SelectStores v-model="query.storeIdEquals" :isOnlyId="true" :hideAll="false" width="200px"
                    :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
                  </SelectStores>
                </form-item>
              </el-col>
              <el-col :span="8">
                <form-item label="会员">
                  <el-input v-model="query.memberCode" palceholder="手机号/会员号/实体卡号"></el-input>
                </form-item>
              </el-col>
              <el-col :span="8">
                <form-item label="充值卡号">
                  <el-input v-model="query.payCardNoEquals" :placeholder="i18n('/公用/查询条件/提示/等于')"></el-input>
                </form-item>
              </el-col>
            </el-row>
            <template slot="opened">
              <el-row>
                <el-col :span="8">
                  <form-item :label="i18n('充值门店')" no-i18n>
                    <SelectStores v-model="query.storeIdEquals" :isOnlyId="true" :hideAll="false" width="200px"
                      :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
                    </SelectStores>
                  </form-item>
                </el-col>
                <el-col :span="8">
                  <form-item label="会员">
                    <el-input v-model="query.memberCode" palceholder="手机号/会员号/实体卡号"></el-input>
                  </form-item>
                </el-col>
                <el-col :span="8">
                  <form-item label="充值卡号">
                    <el-input v-model="query.payCardNoEquals" :placeholder="i18n('/公用/查询条件/提示/等于')"></el-input>
                  </form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <form-item label="付款方式">
                    <el-input v-model="query.payTypeEquals" :placeholder="i18n('/公用/查询条件/提示/等于')"></el-input>
                  </form-item>
                </el-col>
                <el-col :span="8">
                  <form-item label="交易号">
                    <el-input v-model="query.transNoEquals" :placeholder="i18n('/公用/查询条件/提示/等于')"></el-input>
                  </form-item>
                </el-col>
                <el-col :span="8">
                  <form-item label="活动名称">
                    <el-input v-model="query.activityNameStartsWith" :placeholder="i18n('/公用/查询条件/提示/起始于')"></el-input>
                  </form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8" v-if="isMoreMarketing">
                  <form-item :label="formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水/发生区域')">
                    <el-select placeholder="不限" style="width: 200px" v-model="query.zoneIdEquals">
                      <el-option
                        :key="item.zone.id"
                        :label="'['+item.zone.id+']'+item.zone.name"
                        :value="item.zone.id"
                        v-for="item in areaData"
                      >[{{item.zone.id}}]{{item.zone.name}}</el-option>
                    </el-select>
                  </form-item>
                </el-col>
              </el-row>
            </template>
          </QueryCondition>
        </template>
        <template slot="btn" v-if="isShowSum">
          <div style="display:flex;align-items:center">
            <i class="iconfont ic-info" style="font-size: 18px;color: #20A0FF"></i>
            &nbsp;&nbsp;{{ date }}
            <i18n k="/储值/会员储值/会员储值报表/充值流水-按支付方式/付款总金额增加{0}元">
              <template slot="0">{{ sum.payAmount | fmt }}</template>
            </i18n>
          </div>
        </template>
        <template slot="list">
          <el-table :data="queryData" border v-loading="loading">
            <el-table-column fixed label="充值时间" prop="occurredTime" width="140">
              <template slot-scope="scope">
                <span no-i18n>{{ scope.row.occurredTime | dateFormate3 }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed label="充值门店" prop="store" width="140">
              <template slot-scope="scope">
                <span no-i18n>{{ scope.row.store }}</span>
              </template>
            </el-table-column>
            <el-table-column v-if="isMoreMarketing" :label="formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水/发生区域')">
            <template slot-scope="scope">
              <div v-if="scope.row.zone!==null && scope.row.zone!==''">
                <el-tooltip class="item" effect="light"  placement="right-end">
                  <div>{{scope.row.zone}}</div>
                  <div slot="content">
                    {{scope.row.zone}}
                  </div>
                </el-tooltip>
              </div>
              <div v-else>-</div>
            </template>
            </el-table-column>
            <el-table-column label="会员" prop="mobile" width="140">
              <template slot-scope="scope">
                <span no-i18n>{{
                  scope.row.mobile ? scope.row.mobile : (scope.row.crmCode ? scope.row.crmCode : (scope.row.hdCardCardNum ? scope.row.hdCardCardNum : '--'))
                  }}</span>
              </template>
            </el-table-column>
            <el-table-column label="充值卡号" prop="payCardNo" width="140">
              <template slot-scope="scope">
                <span no-i18n>{{ scope.row.payCardNo }}</span>
              </template>
            </el-table-column>
            <el-table-column label="付款金额（元）" prop="amount" width="140">
              <template slot-scope="scope">
                <span no-i18n>{{ scope.row.payAmount | fmt }}</span>
              </template>
            </el-table-column>
            <el-table-column label="付款方式" prop="payType" width="140">
              <template slot-scope="scope">
                <span no-i18n>{{ scope.row.payType }}</span>
              </template>
            </el-table-column>
            <el-table-column label="交易号" prop="transNo" width="140">
              <template slot-scope="scope">
                <span no-i18n>{{ scope.row.transNo }}</span>
              </template>
            </el-table-column>
            <el-table-column label="活动名称" prop="activityName">
              <template slot-scope="scope">
                <span no-i18n>{{ scope.row.activityName }}</span>
              </template>
            </el-table-column>
          </el-table>
        </template>
        <template slot="page">
          <el-pagination
            no-i18n
            :current-page="page.currentPage"
            :page-size="page.size"
            :page-sizes="[10, 20, 30, 40]"
            :total="page.total"
            @current-change="onHandleCurrentChange"
            @size-change="onHandleSizeChange"
            background
            :layout="getPageLayout('total, prev, pager, next, sizes, jumper',page.probeEnabled)"
          ></el-pagination>
        </template>
      </ListWrapper>
    </div>
  </div>
</template>

<script lang="ts" src="./RechargeFlowByPay.ts">
</script>

<style lang="scss">
.recharge-flow-by-pay {
  .qf-form-item .qf-form-label {
  }

  .qf-form-item .qf-form-content {
    width: 200px !important;
  }
}
</style>