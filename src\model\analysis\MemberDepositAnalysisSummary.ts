// 会员储值分析报表概览
export default class MemberDepositAnalysisSummary {
  // 充值金额指标
  rechargeAmount: Nullable<number> = null
  // 消费金额指标
  consumeAmount: Nullable<number> = null
  // 调整增加金额指标
  adjustAddAmount: Nullable<number> = null
  // 调整减少金额指标
  adjustSubAmount: Nullable<number> = null
  // 转入金额指标
  transferInAmount: Nullable<number> = null
  // 可用余额指标
  sumUsableTotal: Nullable<number> = null
  // 可用本金指标
  sumUsableBalance: Nullable<number> = null
  // 可用赠金指标
  sumUsableGiftBalance: Nullable<number> = null
  // 可用余额指标（截止查询日期结束日期）
  usableTotal: Nullable<number> = null
  // 可用本金指标（截止查询日期结束日期）
  usableBalance: Nullable<number> = null
  // 可用赠金指标（截止查询日期结束日期）
  usableGiftBalance: Nullable<number> = null
}