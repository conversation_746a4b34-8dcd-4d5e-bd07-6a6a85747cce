import {Component, Vue} from 'vue-property-decorator'
import ListWrapper from "cmp/list/ListWrapper";
import SubHeader from 'cmp/subheader/SubHeader.vue'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import FloatBlock from 'cmp/floatblock/FloatBlock.vue'
import ActivityStateCountResult from 'model/common/ActivityStateCountResult';
import DateUtil from 'util/DateUtil';
import CardBalancePromotionActivity from "model/payment/card/CardBalancePromotionActivity";
import CardBalancePromotionActivityFilter from "model/payment/card/CardBalancePromotionActivityFilter";
import CardBalancePromotionApi from "http/payment/card/CardBalancePromotionApi";
import ActivityTopicApi from "http/v2/controller/points/topic/ActivityTopicApi";
import ActivityTopic from "model/v2/controller/points/topic/ActivityTopic";
import ActivityState from "cmp/activitystate/ActivityState";
import EditType from "common/EditType";
import I18nPage from "common/I18nDecorator";
import CardBalancePromotionPermission from "./CardBalancePromotionPermission";
import PrepayCardTplPermission from "pages/deposit/prepaycard/prepaycardtpl/PrepayCardTplPermission";
import RoutePermissionMgr from 'mgr/RoutePermissionMgr';

@Component({
	name: "CardBalancePromotionList",
	components: {
		ListWrapper,
		SubHeader,
		BreadCrume,
		FloatBlock,
		ActivityState,
	},
})
@I18nPage({
	prefix: [
		"/储值/预付卡/预付卡支付活动/列表页面",
		"/储值/预付卡/预付卡支付活动/编辑页面",
		"/公用/活动/状态",
		"/公用/活动/活动信息",
		"/公用/活动/提示信息",
		"/公用/按钮",
		"/公用/提示",
		"/公用/菜单",
	],
})
export default class CardBalancePromotionList extends Vue {
	i18n: (str: string, params?: string[]) => string;
	query: CardBalancePromotionActivityFilter = new CardBalancePromotionActivityFilter();
	queryData: CardBalancePromotionActivity[] = [];
	countResult: ActivityStateCountResult = new ActivityStateCountResult();
	tabName: string = "ALL";
	$refs: any;
	checkedAll: boolean = false;
	selected: CardBalancePromotionActivity[] = [];
	permission = new CardBalancePromotionPermission();
	prepayCardTplPermission = new PrepayCardTplPermission();

	panelArray: any;

	// 分页
	page = {
		currentPage: 1,
		total: 0,
		size: 10,
	};

	themes: ActivityTopic[] = [];
	get activityTypes() {
    return [
      {
        code: null,
        label: this.i18n("全部"),
      },
      {
        code: "CardBalanceReductionActivityRule",
        label: this.i18n("预付卡支付立减"),
      },
      {
        code: "CardBalanceDiscountActivityRule",
        label: this.i18n("预付卡支付折扣"),
      },
    ];
  } 
	activityTypeWidth: number = 150;
	activityOptWidth: number = 150;

	created() {
		this.panelArray = [
			{
				name: this.i18n("预付卡支付优惠"),
				url: "",
			},
		];
		let lang = sessionStorage.getItem("locale");
		if (lang === "zh_CN") {
			this.activityTypeWidth = 150;
			this.activityOptWidth = 200;
		} else {
			this.activityTypeWidth = 340;
			this.activityOptWidth = 240;
		}
		this.getList();
		this.getTheme();
	}

	doSearch() {
		this.page.currentPage = 1;
		this.getList();
	}

	doReset() {
		this.query = new CardBalancePromotionActivityFilter();
		this.tabName = "ALL";
		this.page.currentPage = 1;
		this.getList();
	}

	/**
	 * 查询
	 */
	onSearch() {
		this.page.currentPage = 1;
		this.getList();
	}

	/**
	 * 分页页码改变的回调
	 * @param val
	 */
	onHandleCurrentChange(val: number) {
		this.page.currentPage = val;
		this.getList();
	}

	/**
	 * 每页多少条的回调
	 * @param val
	 */
	onHandleSizeChange(val: number) {
		this.page.size = val;
		this.getList();
	}

	/**
	 * 表格排序条件
	 */
	onSortChange({ column, prop, order }: any) {
		// todo
	}

	private getList() {
		if (this.permission.viewable && !this.permission.discountViewable) {
			this.query.activityTypeEquals = "CardBalanceReductionActivityRule"; // 只查立减
		} else if (!this.permission.viewable && this.permission.discountViewable) {
			this.query.activityTypeEquals = "CardBalanceDiscountActivityRule"; // 只查折扣
		}
		this.query.page = this.page.currentPage - 1;
		this.query.pageSize = this.page.size;
		CardBalancePromotionApi.query(this.query)
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					this.countResult = resp.data.countResult;
					this.queryData = resp.data.result;
					this.page.total = resp.data.total;
				} else {
          throw new Error(resp.msg)
        }
			})
			.catch((error) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}

	private add() {
		this.$router.push({ name: "card-balance-promotion-edit" });
	}

	private gotoDtl(row: any) {
		if (row.type === "预付卡支付折扣") {
			this.$router.push({ name: "card-balance-promotion-discount-dtl", query: { activityId: row.body.activityId } });
		}
		if (row.type === "预付卡支付立减") {
			this.$router.push({ name: "card-balance-promotion-dtl", query: { activityId: row.body.activityId } });
		}
	}

	private del(activityId: string) {
		this.$alert(this.i18n("确认要删除吗？"), this.i18n("提示"), {
			confirmButtonText: this.i18n("确定"),
			cancelButtonText: this.i18n("取消"),
			type: "warning",
			callback: (action) => {
				if (action === "confirm") {
					CardBalancePromotionApi.remove(activityId)
						.then((resp: any) => {
							if (resp && resp.code === 2000) {
								this.$message.success(this.i18n("删除成功"));
								this.getList();
							}
						})
						.catch((error) => {
							if (error && error.message) {
								this.$message.error(error.message);
							}
						});
				}
			},
		});
	}

	private delBatch() {
		if (this.selectedActivityIdList.length === 0) {
			this.$message.error(this.i18n("请先勾选要删除的记录"));
			return;
		}
		this.$alert(this.i18n("确认要删除吗？"), this.i18n("提示"), {
			confirmButtonText: this.i18n("确定"),
			cancelButtonText: this.i18n("取消"),
			type: "warning",
			callback: (action) => {
				if (action === "confirm") {
					CardBalancePromotionApi.batchRemove(this.selectedActivityIdList as any)
						.then((resp: any) => {
							if (resp && resp.code === 2000) {
								this.$message.success(resp.data);
								this.getList();
							}
						})
						.catch((error) => {
							if (error && error.message) {
								this.$message.error(error.message);
							}
						});
				}
			},
		});
	}

	private audit(activityId: string) {
		this.$alert(this.i18n("确认要审核吗？"), this.i18n("提示"), {
			confirmButtonText: this.i18n("确定"),
			cancelButtonText: this.i18n("取消"),
			type: "warning",
			callback: (action) => {
				if (action === "confirm") {
					CardBalancePromotionApi.audit(activityId)
						.then((resp: any) => {
							if (resp?.code === 2000) {
								this.$message.success(this.i18n("审核成功"));
								this.getList();
							} else {
                throw new Error(resp.msg)
              }
						})
						.catch((error) => {
							if (error && error.message) {
								this.$message.error(error.message);
							}
						});
				}
			},
		});
	}

	private auditBatch() {
		if (this.selectedActivityIdList.length === 0) {
			this.$message.error(this.i18n("请先勾选要审核的记录"));
			return;
		}
		this.$alert(this.i18n("确认要审核吗？"), this.i18n("提示"), {
			confirmButtonText: this.i18n("确定"),
			cancelButtonText: this.i18n("取消"),
			type: "warning",
			callback: (action) => {
				if (action === "confirm") {
					CardBalancePromotionApi.batchAudit(this.selectedActivityIdList as any)
						.then((resp: any) => {
							if (resp && resp.code === 2000) {
								this.$message.success(resp.data);
								this.getList();
							}
						})
						.catch((error) => {
							if (error && error.message) {
								this.$message.error(error.message);
							}
						});
				}
			},
		});
	}

	private stop(activityId: string) {
		this.$alert(this.i18n("确认要停止吗？"), this.i18n("提示"), {
			confirmButtonText: this.i18n("确定"),
			cancelButtonText: this.i18n("取消"),
			type: "warning",
			callback: (action) => {
				if (action === "confirm") {
					CardBalancePromotionApi.stop(activityId)
						.then((resp: any) => {
							if (resp && resp.code === 2000) {
								this.$message.success(this.i18n("停止成功"));
								this.getList();
							}
						})
						.catch((error) => {
							if (error && error.message) {
								this.$message.error(error.message);
							}
						});
				}
			},
		});
	}

	private stopBatch() {
		if (this.selectedActivityIdList.length === 0) {
			this.$message.error(this.i18n("请先勾选要停止的记录"));
			return;
		}
		this.$alert(this.i18n("确认要停止吗？"), this.i18n("提示"), {
			confirmButtonText: this.i18n("确定"),
			cancelButtonText: this.i18n("取消"),
			type: "warning",
			callback: (action) => {
				if (action === "confirm") {
					CardBalancePromotionApi.batchStop(this.selectedActivityIdList as any)
						.then((resp: any) => {
							if (resp && resp.code === 2000) {
								this.$message.success(resp.data);
								this.getList();
							}
						})
						.catch((error) => {
							if (error && error.message) {
								this.$message.error(error.message);
							}
						});
				}
			},
		});
	}

	private createMbp() {
		this.$router.push({ name: "card-balance-promotion-edit", query: { editType: EditType.CREATE } });
	}

	private createMbdp() {
		this.$router.push({ name: "card-balance-promotion-discount-edit", query: { editType: EditType.CREATE } });
	}

	private copy(activityId: string, type: string) {
		if (type === "CARD_BALANCE_REDUCTION") {
			this.$router.push({
				name: "card-balance-promotion-edit",
				query: { activityId: activityId, editType: EditType.COPY },
			});
		}
		if (type === "CARD_BALANCE_DISCOUNT") {
			this.$router.push({
				name: "card-balance-promotion-discount-edit",
				query: { activityId: activityId, editType: EditType.COPY },
			});
		}
	}

	private edit(activityId: string, type: string) {
		if (type === "CARD_BALANCE_REDUCTION") {
			this.$router.push({
				name: "card-balance-promotion-edit",
				query: { activityId: activityId, editType: EditType.EDIT },
			});
		}
		if (type === "CARD_BALANCE_DISCOUNT") {
			this.$router.push({
				name: "card-balance-promotion-discount-edit",
				query: { activityId: activityId, editType: EditType.EDIT },
			});
		}
	}

	private checkedAllRow() {
		if (this.checkedAll) {
			for (let row of this.queryData) {
				this.$refs.table.toggleRowSelection(row, true);
			}
		} else {
			this.$refs.table.clearSelection();
		}
	}

	private handleSelectionChange(val: any) {
		this.selected = val;
	}

	get selectedActivityIdList() {
		return this.selected.map((e) => (e.body ? e.body.activityId : null));
	}

	get allTab() {
		return `${this.i18n("全部")}(${this.countResult.sum})`;
	}

	get initialTab() {
		return `${this.i18n("未审核")}(${this.countResult.initail})`;
	}

	get unstartTab() {
		return `${this.i18n("未开始")}(${this.countResult.unstart})`;
	}

	get processingTab() {
		return `${this.i18n("进行中")}(${this.countResult.processing})`;
	}

	get stopedTab() {
		return `${this.i18n("已结束")}(${this.countResult.stoped})`;
	}

	private activityTime(row: any) {
		return `${DateUtil.format(row.body.beginDate, "yyyy-MM-dd")}${this.i18n("至")}${DateUtil.format(row.body.endDate, "yyyy-MM-dd")}`;
	}

	private handleTabClick(tab: any, event: any) {
		this.query.stateEquals = tab.name === "ALL" ? null : tab.name;
		this.doSearch();
	}

	private getTheme() {
		ActivityTopicApi.listTopic().then((resp: any) => {
			if (resp && resp.code === 2000) {
				this.themes = resp.data;
			}
		});
	}

	private gotoCardTplDtl(num: string) {
		RoutePermissionMgr.openBlank({ name: "prepay-card-tpl-dtl", query: { number: num } });
	}
}
