import { ExecuteState } from "./ExecuteState"

export default class RedemptionCodeBillFilter {
  // 单号等于
  numberEquals: Nullable<string> = null
  // 名称类似于
  nameLikes: Nullable<string> = null
  // 状态等于: INITAIL-未审核;UNSTART-未开始;PROCESSING-进行中数量；STOPED-已结束
  stateEquals: Nullable<ExecuteState> = null
  // 最后修改时间
  lastModifiedBetweenClosedClosed: Date[] = []
  // 创建时间
  createdBetweenClosedClosed: Date[] = []
  // 营销中心
  marketingCenterEquals: Nullable<string> = null
  // 
  page: Nullable<number> = null
  // 
  pageSize: Nullable<number> = null
}