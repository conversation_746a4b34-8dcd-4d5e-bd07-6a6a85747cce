import ActivityBody from "model/common/ActivityBody";
import ChannelRange from "model/common/ChannelRange";
import GradesRange from "model/common/GradeRange";

export default class BaseCouponActivity {
	// true表示只保存，false表示保存并审核  这里是常浩修改  我备注下
	justSave: Nullable<boolean> = null;
	// 活动类型
	body: Nullable<ActivityBody> = null;
	// 活动总限
	maxIssueTimes: Nullable<number> = null;
	// 每人限量
	maxPerIssueTimes: Nullable<number> = null;
	// 活动每天限量
	maxDayQuotaQty: Nullable<number> = null;
	// 单笔订单购买限量
	maxOrderIssueTimes: Nullable<number> = null;
	// 每人每天限量
	maxPerDateRangeIssueTimes: Nullable<number> = null;
	// 非会员是否参与发券
	nonmemberIssue: Nullable<boolean> = null;
	// 渠道
	channelRange: Nullable<ChannelRange> = null;
	// 参与会员等级
	gradeRange: Nullable<GradesRange> = null;
	// 显示排序
	sequence: Nullable<number> = null
	// 预告天数
	advanceDay: Nullable<number> = null
	//限量时间类型，DAY——每天；WEEK——每周,MONTH——每月，YEAR——每年;,可用值:DAY,WEEK,MONTH,YEAR
	dateLimitType: Nullable<string> = null;
}
