import SubHeader from 'cmp/subheader/SubHeader.vue'
import GiftCardActivityApi from 'http/card/activity/GiftCardActivityApi'
import GiftCardActivity from 'model/card/activity/GiftCardActivity'
import {Component, Vue} from 'vue-property-decorator'
import GiftCardEvaluation from 'model/card/activity/GiftCardEvaluation';
import PrepayDataUtil from '../common/DataUtil';
import I18nPage from "common/I18nDecorator";
import ActivityState from "cmp/activitystate/ActivityState";
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import DateUtil from "util/DateUtil";

@Component({
  name: 'GiftCardActivityEvaluate',
  components: {
    SubHeader,
    ActivityState,
    BreadCrume
  }
})
@I18nPage({
  prefix: [
    '/储值/预付卡/电子礼品卡活动/效果评估',
    '/公用/活动/状态',
    '/公用/活动/活动信息',
    '/公用/活动/提示信息',
    '/公用/按钮',
  ],
})
export default class GiftCardActivityEvaluate extends Vue {
  $refs: any
  panelArray: any = []
  detail: GiftCardActivity = new GiftCardActivity()
    evaluate: GiftCardEvaluation = new GiftCardEvaluation()
    prepayDataUtil: PrepayDataUtil = new PrepayDataUtil()
    begin: Nullable<Date> = null
    end: Nullable<Date> = null
    days: Nullable<number> = null

    created() {
      this.panelArray = [
        {
          name: this.formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水/电子礼品卡活动'),
          url: 'gift-card-activity'
        },
        {
          name: this.formatI18n('/储值/预付卡/电子礼品卡活动/效果评估/电子礼品卡活动效果评估'),
          url: ''
        }
      ]
      this.getDetail()
      this.getEvaluate()
    }

    private getDetail() {
        let activityId = this.$route.query.activityId as string
        GiftCardActivityApi.info(activityId).then((resp: any) => {
            if (resp && resp.code === 2000) {
                this.detail = resp.data
                this.parseDate()
            } else {
                this.$message.error(resp.msg)
            }
        }).catch((error) => {
          if (error && error.message) {
            this.$message.error(error.message)
          }
        })
    }

    private getEvaluate() {
        let activityId = this.$route.query.activityId as string
        GiftCardActivityApi.evaluate(activityId).then((resp: any) => {
            if (resp && resp.code === 2000) {
                this.evaluate = resp.data
            } else {
                this.$message.error(resp.msg)
            }
        }).catch((error) => {
          if (error && error.message) {
            this.$message.error(error.message)
          }
        })
    }

    private parseDate() {
        if (!this.detail.body) {
            return '-'
        }
        let now = new Date()
        this.begin = DateUtil.parseDate(this.detail.body.beginDate as any)
        this.end = DateUtil.parseDate(this.detail.body.endDate as any)
        if (now.getTime() > this.begin.getTime() && now.getTime() < this.end.getTime()) {
            this.end = now
        }
        this.days = parseInt(String((this.end.getTime() - this.begin.getTime()) / 1000 / 60 / 60 / 24 + 1))
    }
}
