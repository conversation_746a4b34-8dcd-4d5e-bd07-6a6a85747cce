/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:19
 * @LastEditTime: 2024-04-01 16:50:40
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\customizecontent\CustomizeContentApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import CustomizeContent from 'model/customizecontent/CustomizeContent'
import CustomizeContentFilter from 'model/customizecontent/CustomizeContentFilter'
import Response from 'model/common/Response'

export default class CustomizeContentApi {
    /**
     * 查询自定义信息
     *
     */
    static list(body: CustomizeContentFilter): Promise<Response<CustomizeContent[]>> {
        return ApiClient.server().post(`/v1/customize-content/list`, body, {}).then((res) => {
            return res.data
        })
    }

    /**
     * 修改自定义信息
     *
     */
    static modify(id: string, reason: string, type: string): Promise<Response<void>> {
        return ApiClient.server().post(`/v1/customize-content/modify/${id}`, {}, {
            params: {
                reason: reason,
                type: type
            }
        }).then((res) => {
            return res.data
        })
    }

    /**
     * 删除自定义信息
     *
     */
    static remove(body: string[]): Promise<Response<void>> {
      return ApiClient.server().post(`/v1/customize-content/remove`, body, {}).then((res) => {
        return res.data
      })
    }

    /**
     * 添加自定义信息。type:类型 scoreAdjustReason：积分调整原因； balanceAdjustReason：储值调整原因;cardAdjustReason:预付卡调整原因;recoverCardBillReason:卡回收调整原因
     *
     */
    static save(reason: string, type: string): Promise<Response<string>> {
        return ApiClient.server().post(`/v1/customize-content/save`, {}, {
            params: {
                reason: reason,
                type: type
            }
        }).then((res) => {
            return res.data
        })
    }

}
