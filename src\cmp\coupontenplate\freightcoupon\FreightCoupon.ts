import {Component, Prop, Vue, Watch} from 'vue-property-decorator'
import CouponItem from 'model/common/CouponItem'
import AmountToFixUtil from 'util/AmountToFixUtil'
import TimeRange from 'cmp/coupontenplate/cmp/TimeRange.vue'
import GoodsScopeEx from 'cmp/goodsscope/GoodsScopeEx.vue'
import GoodsRange from 'model/common/GoodsRange'
import UseCouponStep from 'cmp/coupontenplate/cmp/UseCouponStep.vue'
import CouponBear from 'cmp/coupontenplate/cmp/CouponBear.vue'
import CouponInfo from 'model/common/CouponInfo'
import CashCouponAttribute from 'model/common/CashCouponAttribute'
import ValidityInfo from 'model/common/ValidityInfo'
import DateTimeRange from 'model/common/DateTimeRange'
import SubjectApportion from 'model/common/SubjectApportion'
import StoreRange from 'model/common/StoreRange'
import DateUtil from 'util/DateUtil'
import ChannelRange from 'model/common/ChannelRange'
import CouponInitialApi from 'http/v2/coupon/init/CouponInitialApi'
import RSCostPartyFilter from 'model/common/RSCostPartyFilter'
import CostPartyApi from 'http/costparty/CostPartyApi'
import CouponTemplateSelectorDialog from "cmp/selectordialogs/CouponTemplateSelectorDialog";
import CouponTemplateLogo from "cmp/coupontenplate/cmp/CouponTemplateLogo";
import ActiveStore from "cmp/activestore/ActiveStore";
import CouponThreshold from "model/common/CouponThreshold";
import {ThresholdType} from "model/common/ThresholdType";
import CouponTemplateLabel from "cmp/coupontenplate/cmp/CouponTemplateLabel.vue"
import { LocalStorage } from 'mgr/BrowserMgr'
import I18nPage from 'common/I18nDecorator'
import UseCouponDesc from '../FormItemCmp/UseCouponDesc/UseCouponDesc'
import CouponName from '../FormItemCmp/CouponName/CouponName'
import CouponEffectPeriod from '../FormItemCmp/CouponEffectPeriod/CouponEffectPeriod'
import SelectCostParty from 'cmp/selectCostParty/SelectCostParty';
import { ExpiryType } from "model/common/ExpiryType";
import CouponConfig from 'model/v2/coupon/init/CouponConfig'

@Component({
  name: 'FreightCoupon',
  components: {
    CouponName,
    UseCouponDesc,
    CouponEffectPeriod,
    TimeRange,
    ActiveStore,
    GoodsScopeEx,
    UseCouponStep,
    CouponBear,
    CouponTemplateSelectorDialog,
    CouponTemplateLogo,
    CouponTemplateLabel,
    SelectCostParty
  }
})
@I18nPage({
  prefix: [
    "/公用/券模板",
    '/公用/券模板/单品折扣券/用券门槛'
  ],
  auto: true
})
export default class FreightCoupon extends Vue {
  dtl: CouponConfig = new CouponConfig()
  queryCostParyRange:string = 'customize'
  timeParam: any = ''

  ruleForm: any = {
    amount: '',
    name: '',
    dateType: 'RALATIVE',
    dateFrom: '',
    dateTo: '',
    dateFix: '',
    useDate: '',
    storeRange: '{}',
    recordWay: 'FAV',
    discountWay: '',
    payWay: '',
    couponOrder: '',
    couponGoodsDesc: '',
    couponProduct: '',
    type: '',
    templateId: '',
    useFrom: 'step2',
    from: [],
    logoUrl: '',
    transferable: true,  // 是否可转赠
    templateTag: [],
    price: null,
    termsModel: null,
    couponSubscriptType: "COMMON",
  }
  $refs: any
  rules: any = {}
  curState = ''
  telescoping: boolean = true  //为true收起高级设置，为false展开
  @Prop()
  sameStore: boolean // 与活动门店一致
  @Prop()
  state: string
  @Prop()
  channels: any
  @Prop()
  value: CouponItem
  @Prop({
    type: Boolean,
    default: false
  })
  baseSettingFlag: boolean
  @Prop({
    type: Boolean,
    default: false
  })
  enableStore: boolean
  @Prop({
    type: String,
    default: 'add'
  })
  copyFlag: string

  @Prop({
    type: String,
    default: '400'
  })
  remarkMaxlength: string

  @Prop({
    type: Boolean,
    default: false
  })
  baseFieldEditable: false // 叠加促销、用券记录方式、券承担方、用券顺序 是否可编辑

  @Prop({
    default: () => {
      return {
        maxAmount: 99999999,
        maxValidDay: 36500,
        maxUseThreshold: 99999999,
        fixedTime: false,
      }
    }
  })
  options: {  // 指定最大券面额，可选配置，用于微信扫码领券
    maxAmount: number,// 指定最大券面额
    maxValidDay: number, // 指定最大券有效天数
    maxUseThreshold: number,// 指定最大用券门槛
    fixedTime: boolean, // 固定用券时段为全部时段
  }

  parties: any = []

  // 是否是复制\新建\编辑
  @Watch('state')
  onStateChange(value: string) {
    this.curState = value
  }

  @Watch('value', {immediate: true})
  onDataChange(value: CouponItem) {
    if (value && value.coupons) {
      this.doBindValue(JSON.parse(JSON.stringify(value)))
    }
  }

  get accountItemRange() {
    if (this.queryCostParyRange) {
      return this.queryCostParyRange;
    }
      return 'customize';
  }

  created() {
    this.queryCostParyRange = LocalStorage.getItem("accountItemRange");
    this.rules = {
      amount: [
        {required: true, message: this.formatI18n('/公用/券模板', '请输入必填项'), trigger: 'blur'},
      ],
      useFrom: [
        {
          validator: (rule: any, value: any, callback: any) => {
            if (value) {
              if (value === 'step1') {
                callback()
              } else {
                if (this.ruleForm.from && this.ruleForm.from.length > 0) {
                  callback()
                } else {
                  callback(this.formatI18n('/公用/券模板', '请输入必填项'))
                }
              }
            }
          }, trigger: 'blur'
        },
      ],
      couponOrder: [
        {required: true, message: this.formatI18n('/公用/券模板', '请输入必填项'), trigger: 'blur'},
      ],
      couponProduct: [
        {required: true, message: this.formatI18n('/公用/券模板', '请输入必填项'), trigger: 'blur'},
      ],
      discountWay: [
        {required: true, message: this.formatI18n('/公用/券模板', '请输入必填项'), trigger: 'blur'},
      ],
      payWay: [
        {required: true, message: this.formatI18n('/公用/券模板', '请输入必填项'), trigger: 'blur'},
      ],
      couponThreshold: [
				{
					validator: (rule: any, value: any, callback: any) => {
						if(this.ruleForm.type.thresholdType === 'NONREUSEABLE') {
              if (this.ruleForm.type.threshold) {
                callback()
              } else {
                callback(this.formatI18n("/公用/券模板", "请输入必填项"));
              }
						} else {
							callback()
						}
					},
					trigger: "change",
				}
			],
    }
    if (this.copyFlag) {
      this.getCostParty()
    }
    if (!this.$route.query.id) { // 新建时使用配置的用券记录方式
      this.getPayWayDtl()
    }
    //this.getCouponPrefix("freight");
  }

  private getCouponPrefix(type: string){
    if (["edit", "copy"].includes(this.copyFlag)) {
			return;
		}
		CouponInitialApi.get().then((resp: any) => {
		  if (resp && resp.code === 2000) {
			this.dtl = resp.data;
			if(!this.dtl || !this.dtl.couponCodePrefixes) {
				return "";
			  }
        const coupon = this.dtl.couponCodePrefixes.find(
          item => item.couponType === type
        );
        this.ruleForm.prefix = coupon ? coupon.prefix : "";
      }
		})
	}

  getFaceAmount(amount: number) {
    let str: any = this.formatI18n('/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级套餐', '{0}元')
    str = str.replace(/\{0\}/g, Number(amount).toFixed(2))
    return str
  }

  doFormItemChange() {
    this.$emit('input',  this.doTransParams())
    this.$emit('change', this.channels)
  }

  doUseFromChange() {
    if (this.ruleForm.useFrom === 'step1') {
      this.ruleForm.from = []
      this.doFormItemChange()
    }
    this.$refs.ruleForm.validateField('useFrom')
  }

  doFromChange() {
    this.$refs.ruleForm.validateField('useFrom')
    this.doFormItemChange()
  }

  doAmountChange() {
    this.ruleForm.amount = AmountToFixUtil.formatAmount(this.ruleForm.amount, this.options.maxAmount, 0.01, '')
    this.doFormItemChange()
  }

  logoUrlCallBack(url: any) {
    this.ruleForm.logoUrl = url
    this.doFormItemChange()
  }

  doCouponValidateChange() {
    if (this.ruleForm.dateType === 'RALATIVE') {
      this.ruleForm.dateFix = []
    } else {
      this.ruleForm.dateFrom = ''
      this.ruleForm.dateTo = ''
    }
    this.doFormItemChange()
  }

  doStepChange() {
    this.ruleForm.type.threshold = ''
    this.doFormItemChange()
		this.$refs.ruleForm.validate()
  }

  doThresholdChange() {
    this.ruleForm.type.threshold = AmountToFixUtil.formatAmount(this.ruleForm.type.threshold, 99999999, 0.01, "");
    this.doFormItemChange()
  }

  doStoreChange() {
    this.doFormItemChange()
    if (this.$refs['ruleForm'] && (this.$refs['ruleForm'] as any).validateField) {
      (this.$refs['ruleForm'] as any).validateField('storeRange')
    }
    // this.$forceUpdate()
  }

  get remarkPlaceholder() {
    let str = this.formatI18n('/营销/积分活动/积分活动/积分兑换券/编辑页面', '请输入不超过{0}个字符')
    return str.replace(/\{0\}/g, this.remarkMaxlength);
  }

  doValidate() {
    this.telescoping = false
    let arr: any = []
    let p0 = new Promise<void>((resolve, reject) => {
      this.$refs.ruleForm.validate((valid: any) => {
        if (valid) {
          resolve()
        }
      })
    })
    arr.push(p0)
    arr.push(this.$refs.activeStore.validate())
    // 用券门槛
    if (this.$refs.useCouponStep) {
      let p1 = this.$refs.useCouponStep.doValidate()
      arr.push(p1)
    }

    // 用券时段
    if (this.$refs.timeRange) {
      let p2 = this.$refs.timeRange.doValidate()
      arr.push(p2)
    }
    // 用券商品
    if (this.$refs.goodsScope) {
      let p3 = this.$refs.goodsScope.validate()
      arr.push(p3)
    }
    //券有效期校验
    if (this.$refs.couponEffectPeriod) {
      arr.push(this.$refs.couponEffectPeriod.validate());
    }
    return arr
  }

  private doTransParams() {
    let params: CouponItem = new CouponItem()
    params.coupons = new CouponInfo()
    params.coupons.couponBasicType = 'freight' as any
    params.coupons.name = this.ruleForm.name
    params.coupons.templateId = this.ruleForm.templateId
    params.coupons.cashCouponAttribute = new CashCouponAttribute()
    params.coupons.cashCouponAttribute.faceAmount = this.ruleForm.amount
    // 券有效期
    params.coupons.validityInfo = new ValidityInfo();
    params.coupons.validityInfo.validityType = this.ruleForm.dateType;
    if (this.ruleForm.dateType === "RALATIVE") {
      params.coupons.validityInfo.delayEffectDays = this.ruleForm.dateFrom;
      params.coupons.validityInfo.expiryType = this.ruleForm.expiryType
      if ([ExpiryType.DAYS, ExpiryType.NATURAL_DAY].indexOf(this.ruleForm.expiryType) > -1) {
        params.coupons.validityInfo.validityDays = this.ruleForm.dateTo;
        params.coupons.validityInfo.months = null
      } else if (this.ruleForm.expiryType === ExpiryType.MONTHS) {
        params.coupons.validityInfo.months = this.ruleForm.dateTo;
        params.coupons.validityInfo.validityDays = null
      } else {
        params.coupons.validityInfo.months = null;
        params.coupons.validityInfo.validityDays = null;
      }
    } else {
      // 固定有效期
      if (this.ruleForm.dateFix && this.ruleForm.dateFix[0]) {
        params.coupons.validityInfo.beginDate = (this.ruleForm.dateFix[0]) as any;
      }
      if (this.ruleForm.dateFix && this.ruleForm.dateFix[1]) {
        params.coupons.validityInfo.endDate = (this.ruleForm.dateFix[1]) as any;
      }
    }
    // 用券时段
    params.coupons.useTimeRange = new DateTimeRange()
    params.coupons.useTimeRange.dateTimeRangeType = 'ALL' as any
    // todo 用券渠道
    params.coupons.useChannels = new ChannelRange()
    if (this.ruleForm.useFrom === 'step1') {
      params.coupons.useChannels.channelRangeType = 'ALL' as any
      params.coupons.useChannels.channels = []
    } else {
      params.coupons.useChannels.channelRangeType = 'PART' as any
      params.coupons.useChannels.channels = this.ruleForm.from
    }
    // 用券门店
    if (this.ruleForm.storeRange === '{}') {
      let storeRange: StoreRange = new StoreRange()
      if (this.sameStore) {
        storeRange.storeRangeType = 'SAME' as any
      } else {
        storeRange.storeRangeType = 'ALL' as any
      }
      params.coupons.useStores = storeRange
    } else {
      params.coupons.useStores = this.ruleForm.storeRange
    }
    // 用券商品
    params.coupons.useGoods = new GoodsRange()
    params.coupons.useGoods.limit = false
    // 用券门槛
    params.coupons.useThreshold = new CouponThreshold()
    params.coupons.useThreshold.thresholdType = this.ruleForm.type.thresholdType
    // params.coupons.useThreshold.threshold = Number(this.ruleForm.type.threshold)
    params.coupons.useThreshold.threshold = this.ruleForm.type.threshold
    params.coupons.useThreshold.value = Number(this.ruleForm.amount)
    // 叠加促销
    params.coupons.excludePromotion = false
    // 用券记录方式
    params.coupons.useApporion = new SubjectApportion()
    params.coupons.useApporion.subjectApprotionType = this.ruleForm.recordWay
    if (this.ruleForm.recordWay === 'COLLOCATION') {
      params.coupons.useApporion.favValue = this.ruleForm.discountWay
      params.coupons.useApporion.payValue = this.ruleForm.payWay
    }
    // 券承担方
    params.coupons.costParties = []
    // 用券顺序
    params.coupons.priority = this.ruleForm.couponOrder
    // 用券商品说明
    params.coupons.goodsRemark = this.ruleForm.couponGoodsDesc
    // 用券说明
    params.coupons.remark = this.ruleForm.couponProduct
    // 券logo
    params.coupons.logoUrl = this.ruleForm.logoUrl
    // 是否支持转赠
    params.coupons.transferable = this.ruleForm.transferable
		params.coupons.templateTag = this.ruleForm.templateTag
    //价格
    params.coupons.salePrice = this.ruleForm.price
    //账款项目
    params.coupons.termsModel = this.ruleForm.termsModel
    //券角标
    params.coupons.couponSubscriptType = this.ruleForm.couponSubscriptType
    return params
  }

  private doBindValue(value: CouponItem) {
    if (value && value.coupons) {
      let coupon: CouponInfo = value.coupons
      this.ruleForm.templateId = coupon.templateId
      this.ruleForm.name = coupon.name
      this.ruleForm.amount = coupon.cashCouponAttribute!.faceAmount
      this.ruleForm.dateType = coupon.validityInfo!.validityType;
      if (this.ruleForm.dateType === "RALATIVE") {
        this.ruleForm.dateFrom = coupon.validityInfo!.delayEffectDays;
        this.ruleForm.expiryType = coupon.validityInfo!.expiryType
        this.ruleForm.dateTo = coupon.validityInfo!.validityDays || coupon.validityInfo!.months;
      } else {
        this.ruleForm.dateFix = [
          DateUtil.format(coupon.validityInfo!.beginDate, "yyyy-MM-dd HH:mm:ss"),
          DateUtil.format(coupon.validityInfo!.endDate, "yyyy-MM-dd HH:mm:ss"),
        ];
      }
      // 用券门店
      this.ruleForm.storeRange = coupon.useStores
      // todo 用券渠道
      if (coupon.useChannels && coupon.templateId) {
				if (coupon.useChannels.channelRangeType === "ALL") {
					this.ruleForm.useFrom = "step1";
					this.ruleForm.from = [];
				} else {
					this.ruleForm.useFrom = "step2";
					if (coupon.useChannels.channels && coupon.useChannels.channels.length > 0) {
						let arrs: string[] = [];
						coupon.useChannels.channels.forEach((item: any) => {
							if (item.id || item.type) {
								if (item.id && item.id !== "-") {
									arrs.push(item.type + item.id);
								} else {
									arrs.push(item.type);
								}
							} else {
								arrs.push(item);
							}
						});
						this.ruleForm.from = arrs;
					}
				}
			}
      // 用券门槛类型
      if (coupon.useThreshold) {
        this.ruleForm.type = coupon.useThreshold
        this.ruleForm.type.thresholdType = coupon.useThreshold && coupon.useThreshold.thresholdType || ThresholdType.NONE
      } else {
        this.ruleForm.type = new CouponThreshold()
        this.ruleForm.type.thresholdType = ThresholdType.NONE
      }

      // 用券记录方式
      if (coupon.useApporion && coupon.useApporion!.subjectApprotionType) {
        this.ruleForm.recordWay = coupon.useApporion!.subjectApprotionType
      } else {
        this.ruleForm.recordWay = 'FAV'
      }

      if (this.ruleForm.recordWay === 'COLLOCATION') {
        this.ruleForm.discountWay = coupon.useApporion!.favValue
        this.ruleForm.payWay = coupon.useApporion!.payValue
      }
      // 用券顺序
      this.ruleForm.couponOrder = coupon.priority
      // 用券商品说明
      this.ruleForm.couponGoodsDesc = coupon.goodsRemark
      // 用券说明
      this.ruleForm.couponProduct = coupon.remark
      // 券logo
      this.ruleForm.logoUrl = coupon.logoUrl
      // 是否支持转赠
      this.ruleForm.transferable = coupon.transferable
      this.ruleForm.templateTag = coupon.templateTag
      //价格
      this.ruleForm.price = coupon.salePrice
      //账款项目
      this.ruleForm.termsModel = coupon.termsModel
      //券角标
      this.ruleForm.couponSubscriptType = coupon.couponSubscriptType
    }
  }

  private getPayWayDtl() {
    CouponInitialApi.get().then((resp: any) => {
      if (resp && resp.code === 2000) {
        if (resp.data && resp.data.subjectApportion === 'pay') {
          this.ruleForm.recordWay = 'PAY'
        } else if (resp.data && resp.data.subjectApportion === "fav") {
          this.ruleForm.recordWay = "FAV";
        } else if (resp.data && resp.data.subjectApportion === "collection") {
          this.ruleForm.recordWay = "COLLOCATION";
          this.ruleForm.recordType = "AMOUNT"
          this.ruleForm.payWay = 0
        }
      }
    })
  }

  private getCostParty() {
    let params: RSCostPartyFilter = new RSCostPartyFilter()
    params.page = 0
    params.pageSize = 0
    CostPartyApi.query(params).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.parties = resp.data
      }
    }).catch((error: any) => {
      this.$message.error(error.message)
    })
  }

  telescopingChange() {
    this.telescoping = !this.telescoping
  }
}