import GiftInfo from 'model/common/GiftInfo'
import { PrizeType } from "model/promotion/luckydraw/PrizeType";
import IdName from "model/common/IdName";
export default class LuckyDrawPrize {
  // 奖品类型
  prizeType: Nullable<PrizeType> = null
  // 优惠券
  giftBag: Nullable<GiftInfo> = new GiftInfo()
  // 奖品
  prize: Nullable<IdName> = new IdName()
  // 奖品数量
  prizeCount: Nullable<number> = null
  // 中奖限制门槛次数
  thresholdCount: Nullable<number> = null
  // 每人最多中奖次数
  maxPerPersonWinJackpot: Nullable<number> = null
  // 奖品图片
  prizeImage: Nullable<string> = null
  // 奖品说明
  prizeRemark: Nullable<string> = null
}
