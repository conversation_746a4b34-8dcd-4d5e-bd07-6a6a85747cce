<!--
 * @Author: 黎钰龙
 * @Date: 2024-04-27 09:51:01
 * @LastEditTime: 2024-04-29 16:16:09
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\member\equity\EquityCenterDtl.vue
 * 记得注释
-->
<template>
  <div class="equity-center-dtl-container">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button type="primary" v-if="hasOptionPermission('/会员/会员体系/权益中心','权益维护')" size="large" @click="doAdd">
          {{i18n('新增权益')}}
        </el-button>
      </template>
    </BreadCrume>
    <ListWrapper class="current-page">
      <template slot="query">
        <MyQueryCmp @reset="doReset" @search="doSearch" :showExpand="false">
          <el-row>
            <el-col :span="8">
              <FormItem :label="i18n('权益名称')">
                <el-input v-model="query.nameLikes" :placeholder="i18n('/公用/查询条件/提示/类似于')">
                </el-input>
              </FormItem>
            </el-col>
          </el-row>
        </MyQueryCmp>
      </template>
      <template slot="list">
        <el-table :data="tableData" ref="table" style="width: 100%;margin-top: 10px">
          <el-table-column :label="i18n('权益名称')" width="300">
            <template slot-scope="scope">
              <div style="display:flex;align-items:center">
                <img :src="scope.row.imagePath" style="width:30px;height:30px;border-radius:50%;margin-right:8px">
                {{scope.row.name || '--'}}
              </div>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('/会员/等级/等级管理/点击付费等级tab页/未初始化状态下/点击立即开始付费等级初始化/表格/权益说明')">
            <template slot-scope="scope">
              <el-tooltip placement="top-start" effect="light" v-if="scope.row.remark">
                <div slot="content" v-html="scope.row.remark.replace(/\n/g,'<br/>')"></div>
                <div class="text-overflow-ellipsis">
                  {{scope.row.remark}}
                </div>
              </el-tooltip>
              <template v-else>--</template>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('操作')" width="300">
            <template slot-scope="scope">
              <span class="span-btn" v-if="hasOptionPermission('/会员/会员体系/权益中心','权益维护')" @click="doModify(scope.row.uuid)">
                {{i18n('设置')}}
              </span>
              <span class="span-btn" v-if="hasOptionPermission('/会员/会员体系/权益中心','权益维护')" style="margin-left:6px" @click="doRemove(scope.row.uuid,scope.row.version)">
                {{i18n('删除')}}
              </span>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template slot="page">
        <el-pagination :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
          @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background layout="total, prev, pager, next, sizes,  jumper">
        </el-pagination>
      </template>
    </ListWrapper>
  </div>
</template>

<script lang="ts" src="./EquityCenterDtl.ts">
</script>

<style lang="scss">
.equity-center-dtl-container {
  width: 100%;
}
.el-tooltip__popper {
  max-width: 50% !important;
}
</style>