/*
 * @Autor: 司浩
 * @Description:
 * @Date: 2023-03-02 17:36:54
 * @LastEditors: 司浩
 * @LastEditTime: 2023-03-06 11:51:29
 * @FilePath: \phoenix-web-ui\src\http\benefit\EquityCardApi.ts
 */
import ApiClient from 'http/ApiClient'
import EquityCard from 'model/equityCard/EquityCard'
import EquityCardQueryRequest from 'model/equityCard/EquityCardQueryRequest'
import EquityCardCancelRequest from 'model/equityCard/EquityCardCancelRequest'
import CancelResult from 'model/equityCard/CancelResult'
import Response from 'model/common/Response'

export default class EquityCardApi {
  /**
   * 查询权益卡
   * 查询权益卡。
   *
   */
  static query(body: EquityCardQueryRequest): Promise<Response<EquityCard[]>> {
    return ApiClient.server()
      .post(`/v1/equity-card/query`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 作废权益卡
   * 作废权益卡。
   *
   */
  static cancel(
    body: EquityCardCancelRequest
  ): Promise<Response<CancelResult>> {
    return ApiClient.server()
      .post(`/v1/equity-card/cancel`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 批量导出
   * 批量导出。
   *
   */
  static export(body: EquityCardQueryRequest): Promise<Response<void>> {
    return ApiClient.server()
      .post(`/v1/equity-card/export`, body, {})
      .then((res) => {
        return res.data
      })
  }
}
