import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import emitter from 'util/emitter';
import { FormMode } from 'model/local/FormMode';
import FormDefUtil from 'util/FormDefUtil';
import ObjectUtil from 'util/ObjectUtil';
// import TargetPage from '@/model/local/TargetPage';
import I18nPage from 'common/I18nDecorator';
import draggable from 'vuedraggable';
import { CmsConfigChannel } from 'model/template/CmsConfig';
@Component({
  name: 'DraggableImg',
  mixins: [emitter],
  components: { draggable },
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理',
    '/页面/导航设置',
  ],
  auto: true
})
export default class DraggableImg extends Vue {
  @Prop({ type: Boolean, default: false })
  readonly: boolean; //
  @Prop({ type: String, default: 'DraggableImg' })
  validateName: string; // 组件校验名称，editPage通过该名称进行组件校验及校验信息清除
  @Prop({ type: Object })
  value: any; // 数据模型
  @Prop({ type: String, default: '' })
  label: string; // label名
  @Prop({ type: String })
  prefixDescribe: string; // 前置描述
  @Prop({ type: String })
  suffixDescribe: string; // 后置描述
  @Prop() advertiseChannel: Nullable<CmsConfigChannel[]>; //当前页面可用的投放渠道
  @Prop({ type: Boolean, default: true })
  required: boolean; // 是否必填
  @Prop({ type: String })
  relativeValidateName: string; // 联动校验名称
  @Prop()
  formKey: any;
  @Prop({
    type: Object,
    default: () => {
      return {
        delete: true,
        multiple: true,
        select: true,
      };
    },
  })
  config: any; // 配置项
  form: any = {}; // 表单项
  FormModeType = FormMode;
  $refs: any;
  labelPosition: string = 'top';
  imgFormat: any = ['gif', 'jpg', 'jpeg', 'png'];
  rules = {
    propImages: [{ required: true, message: this.i18n('请上传图片'), trigger: ['blur', 'change'] }],
  };

  get credential() {
    return this.$store.state.credential;
  }

  get formMode() {
    if (this.validateName === 'PropTitle') {
      return FormDefUtil.getFormMode(this.readonly, this.validateName, this.formKey);
    } else {
      return FormDefUtil.getFormMode(this.readonly, this.validateName + 'PropTitle', this.formKey);
    }
  }

  get isAddImg() {
    if (this.config.multiple) {
      if (!this.config.multipleNumber) {
        return true;
      } else {
        //  处理多选的时候限制选择数量
        return !(this.value.propImages.length === this.config.multipleNumber);
      }
    } else {
      return this.value.propImages.length === 0;
    }
  }

  @Watch('value', { immediate: true, deep: true })
  handleInput(val: any) {
    if (!val.propImages) return;
    const valItem = ObjectUtil.clone(val);
    valItem.propImages.forEach((item: any) => {
      if (!item.id) {
        item.id = ObjectUtil.uuid();
      }
      const copyItem = ObjectUtil.clone(item);
      const targetPage = {};
      item.targetPage = targetPage;
      item.targetPage.targetPage = copyItem.targetPage;
      item.targetPage.pageParams = copyItem.pageParams;
      item.targetPage.jumpPageInfo = copyItem.jumpPageInfo;
    });
    this.form = valItem;
  }
  // 轮播图jumpPageInfo修改
  changeJumpPageInfo(from: any, index: any) {
    this.form.propImages[index].jumpPageInfo = from
    this.handleChange();
  }
  // 上移
  upInsideData(index: any, data: any, jumpPageInfo: any) {
    const temp = data[index];
    data.splice(index, 1, data[index - 1]);
    data.splice(index - 1, 1, temp);

    this.form.propImages = data
    this.handleChange();
  }
  // 下移
  downInsideData(index: any, data: any) {
    const temp = data[index];
    data.splice(index, 1, data[index + 1]);
    data.splice(index + 1, 1, temp);

    this.form.propImages = data
    this.handleChange();
  }
  handleChange() {
    const copyForm = ObjectUtil.clone(this.form);
    copyForm.propImages.forEach((res: any, index: any) => {
      res.targetPage = this.form.propImages[index].targetPage.targetPage;
      res.pageParams = this.form.propImages[index].targetPage.pageParams;
      res.jumpPageInfo = this.form.propImages[index].jumpPageInfo;
    });
    this.$emit('input', copyForm);
    this.$emit('change', copyForm);
  }

  handleValidate() { }

  // 添加图片
  propImages: any = [];
  addPic() {
    this.form.propImages.push({
      imageUrl: '',
      redirectType: 'innerLink',
      targetPage: {},
      id: ObjectUtil.uuid(),
    });

    this.handleChange();
    this.validate(() => { });
  }

  // 删除图片
  deleteImg(index: any, propImages: any[]) {
    propImages.splice(index, 1);
    this.handleChange();
  }

  mounted() { }

  validate(callback: () => void) {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.validate(callback);
    }
  }

  clearValidate() {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.clearValidate();
    }
  }
}
