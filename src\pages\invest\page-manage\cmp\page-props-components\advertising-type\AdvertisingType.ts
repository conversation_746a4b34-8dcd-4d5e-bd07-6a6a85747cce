import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import { FormMode } from 'model/local/FormMode';
import I18nPage from 'common/I18nDecorator';

@Component({
  name: 'AdvertisingType',
  mixins: [],
  components: {},
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理',
    '/页面/导航设置',
  ],
  auto: true
})
export default class AdvertisingType extends Vue {
  @Prop({ type: Boolean, default: false })
  readonly: boolean; //
  @Prop({ type: String, default: 'AdvertisingType' })
  validateName: string; // 组件校验名称，editPage通过该名称进行组件校验及校验信息清除
  @Prop({ type: Object })
  value: any; // 数据模型
  @Prop({ type: String, default: '组件名称' })
  label: string; // label名
  @Prop({ type: String })
  prefixDescribe: string; // 前置描述
  @Prop({ type: String })
  suffixDescribe: string; // 后置描述

  @Prop({ type: Boolean, default: true })
  required: boolean; // 是否必填
  @Prop({ type: String })
  relativeValidateName: string; // 联动校验名称
  @Prop()
  formKey: any;
  @Prop({ type: Array })
  renderTemplateList: any[];
  @Prop()
  activeIndex: number;

  FormModeType = FormMode;
  $refs: any;
  labelPosition: string = 'left';
  options: any = [{
    value: 'PointsExchangeCouponActivityRule',
    label: this.i18n('积分兑换券')
  }, {
    value: 'WeiXinAppletIssueCouponActivityRule',
    label: this.i18n('小程序领微信券')
  }, {
    value: 'MiniProgramGainCouponActivityRule',
    label: this.i18n('小程序领券')
  }];

  rules = {
    region: [
      { required: false, message: this.i18n('请选择'), trigger: 'change' }
    ],
  };
  get filterActivityWidgets() {
    return this.renderTemplateList.filter((item, index) => index !== this.activeIndex);
  }
  handleChange() {
    this.$emit('input', this.value);
    this.$emit('change', this.value);
    this.validate(() => { });
  }

  mounted() { }

  beforeDestroy() { }

  validate(callback: any) {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.validate(callback);
    }
  }

  clearValidate() {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.clearValidate();
    }
  }
}
