

// 大转盘获得抽奖机会日志
import IdName from "model/common/IdName";

export default class BigWheelRaffleChanceLog {
  // uuid
  uuid: Nullable<string> = null
  // 活动号
  activityNumber: Nullable<string> = null
  // 会员id
  memberId: Nullable<string> = null
  // 发生门店
  occurredOrg: Nullable<IdName> = null
  // 获取抽奖机会时间
  obtainTime: Nullable<Date> = null
  // 获取抽奖机会方式
  obtainType: Nullable<string> = null
  // 获取抽奖机会次数
  obtainRaffleCount: Nullable<number> = null
  // 创建时间
  created: Nullable<Date> = null
  // 创建人
  creator: Nullable<string> = null
  // 会员标识
  memberCode: Nullable<string> = null
}