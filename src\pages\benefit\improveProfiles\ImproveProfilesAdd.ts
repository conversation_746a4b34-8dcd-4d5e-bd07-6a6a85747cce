import { Component, Vue, Provide, Watch } from 'vue-property-decorator'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import EditType from "common/EditType";
import ActiveStore from "cmp/activestore/ActiveStore";
import ImproveProfilesAddForm
  from "pages/benefit/improveProfiles/ImproveProfilesAddForm";
import ImproveProfilesPermission
  from "pages/benefit/improveProfiles/ImproveProfilesPermission";
import DateUtil from "util/DateUtil";
import I18nPage from "common/I18nDecorator";
import CouponActivityApi from "http/v2/coupon/CouponActivityApi";
import EnvUtil from "util/EnvUtil";
import ActivityTopic from "model/v2/controller/points/topic/ActivityTopic";
import MiniProgramGainCouponActivity from "model/v2/coupon/miniprogramgaincoupon/MiniProgramGainCouponActivity";
import CouponActivityTemplate from 'pages/promotion/couponactivities/active/cmp/CouponActivityTemplate.vue'
import SelectGrade from 'cmp/selectGrade/SelectGrade';
import UserGroupSelectorDialog from 'cmp/selectordialogs/UserGroupSelectorDialog'
import CouponActivityTemplateDtl from 'pages/promotion/couponactivities/active/cmp/CouponActivityTemplateDtl.vue'
import UpgradeLine from 'model/grade/upgradegift/UpgradeLine'
import ActiveAddCoupon from 'cmp/activeaddcoupon/ActiveAddCoupon'
import AmountToFixUtil from 'util/AmountToFixUtil'
import SelectMemberInfoDialog from './select-member-info/SelectMemberInfoDialog.vue'
import { MemberInfoFieldName, MemberInfoFieldNameMap } from 'model/systemConfig/MemberInfoFieldName';
import SelectGroupCmp from 'cmp/select-group-dialog/SelectGroupCmp';
@Component({
  name: "ImproveProfilesAdd",
  components: {
    BreadCrume,
    ActiveStore,
    CouponActivityTemplate,
    SelectGrade,
    UserGroupSelectorDialog,
    CouponActivityTemplateDtl,
    ActiveAddCoupon,
    SelectMemberInfoDialog,
    SelectGroupCmp
  },
})
@I18nPage({
  prefix: [
    "/营销/券礼包活动/小程序领券/编辑页面",
    "/营销/券礼包活动/券礼包活动",
    "/公用/活动/活动信息",
    "/公用/表单校验",
    "/公用/券模板",
    "/公用/按钮",
    "/公用/js提示信息",
    "/公用/菜单",
    '/储值/预付卡/预付卡调整单/编辑页面',
    '/储值/预付卡/预付卡充值单',
    '/营销/升级有礼',
    '/储值/预付卡/预付卡调整单/编辑页面',
    '/设置/系统设置'
  ],
  auto: false,
})
export default class ImproveProfilesAdd extends Vue {
  @Provide('maxCouponItemLimit') maxCouponItemLimit: Number = 200
  defaultValue = [DateUtil.nowDayTime(), new Date(DateUtil.nowDayTime().getTime() + 24 * 60 * 60 * 1000 - 1)]
  i18n: (str: string, params?: string[]) => string;
  state = "";
  panelArray: any[] = [];
  editType: string = EditType.CREATE;
  activityId = "";
  detail: MiniProgramGainCouponActivity;
  form: ImproveProfilesAddForm = new ImproveProfilesAddForm();
  permission: ImproveProfilesPermission = new ImproveProfilesPermission();
  $refs: any;
  loading = {
    save: false,
    detail: false,
    upload: false,
  };
  themes: ActivityTopic[] = [];
  uploadHeaders: any = {
    marketingCenter: sessionStorage.getItem("marketCenter"),
  };
  excludedSelectableCouponTypes: string[] = [];
  selectData: any = "";
  timerange: any = [[new Date(new Date().setSeconds(0)), new Date(new Date().setSeconds(59))]]
  excludedTypes: any = []
  // 礼包
  giftBagRules: any = {}
  UpgradeLines: UpgradeLine = new UpgradeLine()
  checkListDoc: any[] = []
  get uploadUrl() {
    return EnvUtil.getServiceUrl() + "v1/upload/upload";
  }

  get allDataLoaded() {
    return !this.loading.detail;
  }

  get dateRangeOption() {
    return {
      disabledDate(time: any) {
        return time.getTime() < DateUtil.nowDayTime();
      },
    };
  }

  changeTime() {
    if (this.form.data && this.form.data.dateRange && this.form.data.dateRange.length === 2) {
      const maxDate = new Date('2038-01-01 23:59:59').getTime();
      if (this.form.data.dateRange[0] && new Date(this.form.data.dateRange[0]).getTime() > maxDate) {
        this.form.data.dateRange[0] = '2038-01-01 00:00:00' as any;
      }
      if (this.form.data.dateRange[1] && new Date(this.form.data.dateRange[1]).getTime() > maxDate) {
        this.form.data.dateRange[1] = '2038-01-01 23:59:59' as any
      }
    }
  }

  pickerOptions: any = {
    //首先是抉择出开始时间，依据开始时间给出可选的六个月时间范畴
    onPick: ({ maxDate, minDate }: any) => {
      this.selectData = minDate.getTime();
      if (maxDate) {
        // 解除限制
        this.selectData = "";
      }
    },
    disabledDate: (time: any) => {
      if (this.selectData) {
        const curDate = this.selectData;
        const three = 180 * 24 * 3600 * 1000; // 6个月
        const threeMonths = curDate + three; // 开始时间+6个月
        return time.getTime() < curDate || time.getTime() > threeMonths || time.getTime() > new Date('2038-01-01 23:59:59').getTime();
      } else {
        return time.getTime() < DateUtil.nowDayTime() || time.getTime() > new Date('2038-01-01 23:59:59').getTime(); //没选的时候，今天以前的时间都不能选
      }
    },
  };

  get optionsDoc() {
    return [
      {
        key: this.i18n('生日'),
        value: 'BIRTHDAY'
      },
      {
        key: this.i18n('性别'),
        value: 'GENDER'
      },
      // {
      //   key: this.i18n('年龄'),
      //   value: 'AGE'
      // },
      {
        key: this.i18n('身份证号'),
        value: 'ID_CARD'
      },
      {
        key: this.i18n('行业'),
        value: 'INDUSTRY'
      },
      {
        key: this.i18n('年收入'),
        value: 'ANNUAL_INCOME'
      },
      {
        key: this.i18n('邮箱'),
        value: 'EMAIL'
      },
      {
        key: this.i18n('地址'),
        value: 'ADDRESS'
      }
    ]
  }
  @Watch('UpgradeLines.pointCheck', { deep: true })
  onPointCheckChange(val: any, old: any) {
    if (val) {
      this.$refs.gift.validateField('points')
    } else {
      this.$refs.points.clearValidate()
    }
    this.$refs.giftBag.validateField('gift')
  }
  @Watch('UpgradeLines.couponCheck', { deep: true })
  onCouponCheckChange(val: any, old: any) {
    if (val) {
      this.$refs.gift.validateField('couponItems')
    } else {
      this.$refs.couponItems.clearValidate()
    }
    this.$refs.giftBag.validateField('gift')
  }
  @Watch('UpgradeLines.growthValueCheck', { deep: true })
  onGrowthValueCheckChange(val: any, old: any) {
    if (val) {
      this.$refs.gift.validateField('growthValue')
    } else {
      this.$refs.growthValue.clearValidate()
    }
    this.$refs.giftBag.validateField('gift')
  }
  @Watch('UpgradeLines.gift.points', { deep: true })
  onPointsChange(val: any, old: any) {
    this.$refs.gift.validateField('points')
  }
  @Watch('UpgradeLines.gift.couponItems', { deep: true })
  onCouponItemsChange(val: any, old: any) {
    this.$refs.gift.validateField('couponItems')
  }
  @Watch('UpgradeLines.growthValue', { deep: true })
  onGrowthValueChange(val: any, old: any) {
    this.$refs.gift.validateField('growthValue')
  }

  created() {
    this.form.init(this);
    let editType = this.$route.query.from as string;
    this.editType = editType ? editType : EditType.CREATE;
    this.panelArray = [
      {
        name: this.formatI18n("/公用/菜单", "会员成长"),
        url: "membership-growth",
      },
      {
        name: this.editType === EditType.EDIT ? this.i18n("修改完善资料有礼活动") : this.i18n("新建完善资料有礼活动"),
        url: "",
      },
    ];
    this.activityId = this.$route.query.id as string;
    if ([EditType.COPY, EditType.EDIT].indexOf(this.editType) > -1) {
      this.loadData();
    }
    this.giftBagRules = {
      gift: [
        {
          validator: this.giftRule,
          trigger: ['change', 'blur'],
        }
      ]
    }
  }

  doSave(justSave: boolean) {
    this.$refs.giftBag.clearValidate()
    this.$refs.gift.clearValidate()
    if (!this.UpgradeLines.pointCheck && !this.UpgradeLines.couponCheck && !this.UpgradeLines.growthValueCheck) {
      this.$refs.giftBag && this.$refs.giftBag.validateField('gift')
      this.$refs.points.clearValidate()
      this.$refs.growthValue.clearValidate()
    } else {
      if (this.UpgradeLines.pointCheck) {
        this.$refs.gift && this.$refs.gift.validateField('points')
      }
      if (this.UpgradeLines.couponCheck) {
        this.$refs.gift && this.$refs.gift.validateField('couponItems')
      }
      if (this.UpgradeLines.growthValueCheck) {
        this.$refs.gift && this.$refs.gift.validateField('growthValue')
      }
    }

    Promise.all([this.$refs.form.validate(), this.$refs.storeScope.validate()])
      .then((res: any[]) => {
        let v1 = false
        let v2 = false
        let v3 = false
        let v4 = false
        if (!this.UpgradeLines.pointCheck && !this.UpgradeLines.couponCheck && !this.UpgradeLines.growthValueCheck) {
          v1 = this.$refs.giftBag && this.$refs.giftBag.validateField('gift').validateMessage
        } else {
          if (this.UpgradeLines.pointCheck) {
            v2 = this.$refs.points && this.$refs.points.validateMessage
          }
          if (this.UpgradeLines.couponCheck) {
            v3 = this.$refs.couponItems && this.$refs.couponItems.validateMessage
            v3 = !this.UpgradeLines.gift!.couponItems.length || this.UpgradeLines.gift!.couponItems.some(x => !x.qty)
          }
          if (this.UpgradeLines.growthValueCheck) {
            v4 = this.$refs.growthValue && this.$refs.growthValue.validateMessage
          }
        }
        if (v1 || v2 || v3 || v4) {
          return
        }
        console.log(this.form);
        let requestParams = this.form.toParams();
        requestParams.justSave = justSave;
        if (this.activityId && requestParams.body && this.editType === EditType.EDIT) {
          requestParams.body.activityId = this.activityId;
        }
        this.loading.save = true;
        CouponActivityApi.saveImproveProfilesActivity(requestParams)
          .then((res) => {
            if (res.code === 2000) {
              if (justSave) {
                this.$message.success(this.i18n("保存成功"));
              } else {
                this.$message.success(this.i18n("保存并审核成功"));
              }
              this.$router.push({ name: "improveProfiles-dtl", query: { id: res.data } });
            } else {
              this.$message.error(res.msg as string)
            }
          })
          .catch((error: any) => {
            this.$message.error(error.message);
          })
          .finally(() => {
            this.loading.save = false;
          });
      })
      .catch((e: any) => {
        console.log(e)
      });
  }

  doCancel() {
    this.$router.back();
  }


  private loadData() {
    this.loading.detail = true;
    CouponActivityApi.getImproveProfilesActivity(this.activityId)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.detail = resp.data;
          if (this.editType !== EditType.COPY) {
            this.state = resp.data.body.state;
            // @ts-ignore
            this.detail.body.state = null;
          }
          this.form.of(resp.data);
        } else {
          this.$message.error(resp.msg);
        }
      })
      .catch((error: any) => {
        this.$message.error(error.message);
      })
      .finally(() => {
        this.loading.detail = false;
      });
  }

  showSelectUserGroupDialog() {
    let selected: any[] = []
      ; (this.form.data.groups as any || []).forEach((item: any) => {
        if (item.obj) {
          selected.push({ ...item.obj })
        } else {
          selected.push({ ...item })
        }
      })
    this.$refs.userGroupSelectorDialog.open(selected, 'multiple')
  }

  showSelectMemberInfoDialog() {
    const arr = []
    if (this.form.data.needImprove?.length) {
      arr.push(...this.form.data.needImprove)
    }
    this.$refs.selectMemberInfoDialog.open(arr)
  }

  // 礼包
  giftRule(rule: any, value: any, callback: any) {
    if (!this.UpgradeLines.pointCheck && !this.UpgradeLines.couponCheck && !this.UpgradeLines.growthValueCheck) {
      callback(this.i18n("至少勾选一项"))
    }
    callback()
  }
  pointsRule(rule: any, value: any, callback: any) {
    if (!this.UpgradeLines.gift!.points) {
      callback(this.i18n('请输入'))
    }
    callback()
  }
  couponItemsRule(rule: any, value: any, callback: any) {
    if (this.UpgradeLines.couponCheck && this.UpgradeLines.gift!.couponItems && !this.UpgradeLines.gift!.couponItems.length) {
      callback(this.i18n('请选择'))
    }
    callback()
  }
  growthValueRule(rule: any, value: any, callback: any) {
    if (!this.UpgradeLines.growthValue) {
      callback(this.i18n('请输入'))
    }
    callback()
  }


  doAddCoupon() {
    let r = 'firAddCoupon'
    if (this.$refs[r]) {
      this.$refs[r].doAddCoupon(0);
    }
  }

  pointChange() {
    this.UpgradeLines.gift!.points = AmountToFixUtil.formatNumber(this.UpgradeLines.gift!.points, 99999999, 1);
  }
  growChange() {
    this.UpgradeLines.growthValue = AmountToFixUtil.formatNumber(this.UpgradeLines.growthValue, 99999999, 1);
  }
  pointCheckChange(e: any) {
    if (e === false) {
      this.UpgradeLines.gift!.points = null
    }
  }

  couponCheckChange(e: any) {
    if (e === false) {
      this.UpgradeLines.gift!.couponItems = []
    }
  }

  growthValueCheckChange(e: any) {
    if (e === false) {
      this.UpgradeLines.growthValue = null
    }
  }

  // 确认提交会员资料
  doSubmitMemberInfo(ids: MemberInfoFieldName[]) {
    this.form.data.needImprove = ids || []
  }

  transMemberInfoName(id: MemberInfoFieldName) {
    return MemberInfoFieldNameMap[id]
  }

  memberInfoDelete(index: number) {
    this.form.data.needImprove.splice(index, 1)
  }
}