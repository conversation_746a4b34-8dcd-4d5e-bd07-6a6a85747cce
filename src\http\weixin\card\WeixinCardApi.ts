import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import WeixinCard from 'model/weixin/card/WeixinCard'
import BWeixinConfig from 'src/model/weixin/card/BWeixinConfig'

export default class WeixinCardApi {
  /**
   * 创建微信会员卡
   *
   */
  static create(body: WeixinCard): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/weixin-card/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 取得微信会员卡
   *
   */
  static get(): Promise<Response<WeixinCard>> {
    return ApiClient.server().post(`/v1/weixin-card/get`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 更新微信会员卡
   *
   */
  static update(body: WeixinCard): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/weixin-card/update`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 微信初始化是否展示储值二维码界面
   *
   */
  static getShowBalanceQrCodePage(): Promise<Response<BWeixinConfig>> {
    return ApiClient.server().get(`/v1/weixin-card/getWeixinConfig`, {
    }).then((res) => {
      return res.data
    })
  }
}
