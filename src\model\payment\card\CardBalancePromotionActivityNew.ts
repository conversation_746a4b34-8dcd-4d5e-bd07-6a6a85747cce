/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-01-10 11:06:44
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\payment\card\CardBalancePromotionActivityNew.ts
 * 记得注释
 */
import ActivityBody from "model/common/ActivityBody";
import GoodsRange from "model/common/GoodsRange";
import IdName from "model/common/IdName";
import GradeStepValue from "model/common/GradeSameReductionNew";
import GradeStepValues from "model/common/GradeStepValues";
import DateTimeCondition from "model/common/DateTimeCondition";

type Strategy = "BY_AMOUNT" | "BY_QTY"; // 金额、数量
export default class CardBalancePromotionActivity {
	// 活动主体
	body: Nullable<ActivityBody> = new ActivityBody();
	// 翻译后的活动类型
	type: Nullable<String> = null;
	// 卡模板
	cardTemplates: IdName[] = [];
	// 商品范围
	goods: Nullable<GoodsRange> = new GoodsRange();
	// 门槛
	favThreshold: Nullable<number> = null;
	// 立减策略
	strategy: Nullable<Strategy> = "BY_AMOUNT";
	// 不同等级规则
	gradeDifferentStepValue: Nullable<GradeStepValues[]> = null;
	// 相同等级规则
	gradeSameStepValue: Nullable<GradeStepValues> = null;
	// 是否参与前台促销
	excludePromotion: Nullable<boolean> = true;
  // 每天卡最大使用次数
  maxDailyCardJoinTime: Nullable<number> = null
  // 卡最大使用次数
  maxCardJoinTimes: Nullable<number> = null
  // 活动最大次数
  maxActivityTimes: Nullable<number> = null
    	// 活动时间限制
	dateTimeCondition = new DateTimeCondition();
}
export { Strategy };
