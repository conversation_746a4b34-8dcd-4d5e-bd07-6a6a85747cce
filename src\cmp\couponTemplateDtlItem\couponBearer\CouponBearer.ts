/*
 * @Author: 黎钰龙
 * @Date: 2023-06-29 18:04:15
 * @LastEditTime: 2023-06-29 18:16:16
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\couponTemplateDtlItem\couponBearer\CouponBearer.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import { ApportionType } from 'model/common/ApportionType';
import CouponItem from 'model/common/CouponItem';
import RSCostParty from 'model/common/RSCostParty';
import CBearSpecialGoodsDtl from 'cmp/coupontenplate/cmp/cBearSpecialGoodsDtl.vue'
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  name: 'CouponBearer',
  components: {
    CBearSpecialGoodsDtl,
  },
})
@I18nPage({
  prefix: [
    '/公用/券模板'
  ],
  auto: true
})
export default class CouponBearer extends Vue {
  @Prop()
  data: CouponItem;

  @Prop({ default: [] })
  parties: RSCostParty[];

  getCostPartr(party: any, percent: any) {
    let str = ''
    if (this.data.coupons && this.data.coupons!.costParties && this.data.coupons!.costParties.length > 0 && this.data.coupons!.costParties[0].bearType === ApportionType.AMOUNT) {
      str = str.replace('%', this.formatI18n('/公用/券模板/元'));
    }
    if (this.data.coupons && this.data.coupons!.costParty) {
      if (this.data.coupons.costParty.bearType == 'AMOUNT') {
        if (this.data.coupons.costParty.amountType == 'all') {
          str = `${this.getPartyNameById(party)}`;
          str = str +
            `&nbsp;<span style="font-weight: bold">${this.i18n('承担全部券抵扣金额')}</span>&nbsp;`
        } else if (this.data.coupons.costParty.amountType == 'part') {
          str = `${this.getPartyNameById(party)}`;
          if (percent == -1) {
            str = str +
              `&nbsp;<span style="font-weight: bold">${this.i18n('剩余券抵扣金额')}</span>&nbsp;`
          } else if (percent > 0) {
            str = str +
              `&nbsp;<span style="font-weight: bold">${this.i18n('承担最多')}${percent}${this.i18n('元')}</span>&nbsp;`
          }
        }
      } else if (this.data.coupons.costParty.bearType == 'PROPORTION') {
        str = this.formatI18n(
          "/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送券的时候/点击券名称/券承担方",
          "{0}承担用券金额{1}%"
        );
        str = str.replace(/\{0\}/g, this.getPartyNameById(party));
        str = str.replace(
          /\{1\}/g,
          `&nbsp;<span style="font-weight: bold">${percent}</span>&nbsp;`
        );
      }
    }
    // 承担全部券抵扣金额
    // 剩余券抵扣金额
    // 承担最多x元
    return str;
  }

  getPartyNameById(id: string) {
    let str = "";
    if (this.parties && this.parties.length > 0) {
      this.parties.forEach((item: any) => {
        if (item.costParty.id === id) {
          str = item.costParty.name;
        }
      });
    }
    return str;
  }

  viewCBSpecialGoods() {
    ; (this.$refs.CBearSpecialGoodsDtl as any).open()
  }
};