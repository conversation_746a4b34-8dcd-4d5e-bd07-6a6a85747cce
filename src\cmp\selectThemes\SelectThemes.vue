<!--
 * @Author: 黎钰龙
 * @Date: 2023-11-29 17:00:54
 * @LastEditTime: 2024-07-18 16:36:04
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\selectThemes\SelectThemes.vue
 * 记得注释
-->
<template>
  <el-select
    v-model="selectTheme"
    value-key="code"
    :style="{width: width}"
    :loading="selectLoading"
    :disabled="disabled"
    clearable
    :placeholder="placeholder ? placeholder : i18n('请选择/输入门店')"
  >
    <el-option v-if="!hideAll" :label="i18n('全部')" :value="null">
      {{ i18n("全部") }}
    </el-option>
    <template v-if="isOnlyId">
      <el-option :label="item.name" :value="item.code" v-for="(item, index) in themes" :key="index">
        [{{ item.code }}]{{ item.name }}
      </el-option>
    </template>
    <template v-else>
      <el-option :label="item.name" :value="item" v-for="(item, index) in themes" :key="index">
        [{{ item.code }}]{{ item.name }}
      </el-option>
    </template>
  </el-select>
</template>

<script lang="ts" src="./SelectThemes.ts"></script>

<style>
</style>