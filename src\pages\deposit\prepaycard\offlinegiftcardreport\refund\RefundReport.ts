import ListWrapper from 'cmp/list/ListWrapper.vue';
import SubHeader from 'cmp/subheader/SubHeader.vue';
import GiftCardConsumeOrRefundHst from 'model/prepay/report/card/GiftCardConsumeOrRefundHst';
import GiftCardFilter from 'model/prepay/report/card/GiftCardFilter';
import {Component, Vue} from 'vue-property-decorator';
import TimeRange from '../../cmp/timerange/TimeRange';
import CardReportSum from 'model/prepay/report/card/CardReportSum';
import DataUtil from 'pages/deposit/prepaycard/common/DataUtil';
import RSOrgFilter from 'model/common/RSOrgFilter';
import OrgApi from 'http/org/OrgApi';
import RSOrg from 'model/common/RSOrg';
import OffLineGiftCardReportApi from 'http/prepay/report/card/OffLineGiftCardReportApi';
import I18nPage from "common/I18nDecorator";
import ZoneApi from 'http/area/ZoneApi';
import ZoneFilter from 'model/datum/zone/ZoneFilter'
import FormItem from 'cmp/formitem/FormItem';
import SelectStores from 'cmp/selectStores/SelectStores';

@Component({
    name: 'RefundReport',
    components: {
        FormItem,
        ListWrapper,
        SubHeader,
        TimeRange,
      SelectStores
    }
})
@I18nPage({
    prefix: [
        '/储值/预付卡/电子礼品卡报表/退款流水',
        '/公用/按钮',
        '/公用/提示',
    ],
})
export default class RefundReport extends Vue {
    i18n: (str: string, params?: string[]) => string
    expandQuery: boolean = false
    query: GiftCardFilter = new GiftCardFilter()
    queryData: GiftCardConsumeOrRefundHst[] = []
    sum: CardReportSum = new CardReportSum()
    areaData: any = []
    ZoneFilter: ZoneFilter = new ZoneFilter()
    dataUtil: DataUtil = new DataUtil()
    stores: RSOrg[] = []
    $refs: any
    // 分页
    page = {
        currentPage: 1,
        total: 0,
        size: 10
    }

    created() {
        this.query.occurredTimeAfterOrEqual = this.dataUtil.format(new Date(), 'yyyy-MM-dd')
        this.query.occurredTimeBefore = this.dataUtil.format(new Date(), 'yyyy-MM-dd')
        this.getList()
        this.getAreaList()
    }

    doSearch() {
        this.page.currentPage = 1
        this.getList()
    }

    doReset() {
        this.query = new GiftCardFilter()
        this.page.currentPage = 1
        this.$refs['timeRange'].reset()
    }
      /**
  * 查询区域
  */
       getAreaList() {
        // this.ZoneFilter.page = 0
        // this.ZoneFilter.pageSize = 10
        ZoneApi.query(this.ZoneFilter).then((res) => {
          if (res.code === 2000) {
            this.areaData = res.data
          } else {
            this.$message.error(res.msg as string)
          }
        })
      }
    /**
     * 查询
     */
    onSearch() {
        this.page.currentPage = 1
        this.getList()
    }

    /**
     * 分页页码改变的回调
     * @param val
     */
    onHandleCurrentChange(val: number) {
        this.page.currentPage = val
        this.getList()
    }

    /**
     * 每页多少条的回调
     * @param val
     */
    onHandleSizeChange(val: number) {
        this.page.size = val
        this.getList()
    }

    /**
     * 表格排序条件
     */
    onSortChange({column, prop, order}: any) {
        // todo
    }

    private getList() {
        this.query.page = this.page.currentPage - 1
        this.query.pageSize = this.page.size
        OffLineGiftCardReportApi.queryConsumeRefundHst(this.query).then((resp: any) => {
            if (resp && resp.code === 2000) {
                this.queryData = resp.data
                this.page.total = resp.total
                this.getSum()
            } else {
                this.$message.error(resp.msg)
            }
        }).catch((error) => {
            if (error && error.message) {
                this.$message.error(error.message)
            }
        })
    }

    isShowSum: boolean = false
    private getSum() {
        OffLineGiftCardReportApi.consumeRefundHstSum(this.query).then((resp: any) => {
            if (resp && resp.code === 2000) {
                this.sum = resp.data
                this.isShowSum = true
            } else {
                if(resp.code === 2404) return
                this.$message.error(resp.msg)
            }
        }).catch((error) => {
            if (error && error.message) {
                this.$message.error(error.message)
            }
        })
    }

    private handleTimeRange(dateArr: Date[]) {
        this.query.occurredTimeAfterOrEqual = dateArr[0]
        this.query.occurredTimeBefore = dateArr[1]
        this.getList()
    }

    private gotoTplDtl(num: string) {
        this.$router.push({name: 'prepay-card-tpl-dtl', query: {number: num}})
    }
}
