import MemberIdent from 'model/common/member/MemberIdent'

export default class MemberPointsStats extends MemberIdent {
  name: Nullable<string> = null
  // 会员积分余额
  balance: Nullable<number> = null
  // 累计得积分
  totalObtainBalance: Nullable<number> = null
  // 累计用积分
  totalUseBalance: Nullable<number> = null
  // 最近将过期积分余额
  expireBalance: Nullable<number> = null
  // 最近将过期积分,过期日期(当天有效)
  expireDate: Nullable<Date> = null
}