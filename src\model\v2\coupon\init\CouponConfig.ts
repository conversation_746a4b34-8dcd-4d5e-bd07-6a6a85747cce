import RSCostParty from "model/common/RSCostParty";
import CouponCodePrefix from 'model/v2/coupon/init/CouponCodePrefix'

export default class CouponConfig {
  //  券记录方式，pay——支付；fav——优惠；collocation——组合方式
  subjectApportion: Nullable<string> = null
  // 券最大使用张数，null表示不限制
  useLimit: Nullable<number> = null
  //  叠加优惠，coupon——用券优先；fav——优惠优先
  superpositionFavRule: Nullable<string> = null
  //  券承担方配置，用于建券模板券承担方默认展示值，可选值：none:不记录（默认值），amount:按金额，proportion：按比例。
  costPartyConfig: Nullable<string> = null
  //  券承担方选项配置，用于建券模板券承担方展示值，可选值：none:不记录（默认值），amount:按金额，proportion：按比例。
  costPartyConfigOptions: string[] = []
  //  券码前缀
  couponCodePrefixes: CouponCodePrefix[] = []
}