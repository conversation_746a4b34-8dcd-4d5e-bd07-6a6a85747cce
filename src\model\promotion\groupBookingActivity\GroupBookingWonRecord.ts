

// 中奖记录
import GiftBag from "model/grade/upgradegift/GiftBag";
import PrizeReceivedResult from "model/promotion/groupBookingActivity/PrizeReceivedResult";

export default class GroupBookingWonRecord {
  // uuid
  uuid: Nullable<string> = null
  // 版本
  version: Nullable<number> = null
  // 活动号
  activityNumber: Nullable<string> = null
  // 团号
  groupNumber: Nullable<string> = null
  // 会员Id
  memberId: Nullable<string> = null
  // 奖品Id
  prizeId: Nullable<string> = null
  // 奖品名称
  prizeName: Nullable<string> = null
  // 奖品类型
  prizeType: Nullable<string> = null
  // 奖品礼包
  giftBag: Nullable<GiftBag> = null
  // 中奖时间
  winTime: Nullable<Date> = null
  // 关团时间
  receivedTime: Nullable<Date> = null
  // 领取结果
  receivedResult: Nullable<PrizeReceivedResult> = null
  // 收件人姓名
  receivedName: Nullable<string> = null
  // 收件人手机号
  receivedPhone: Nullable<string> = null
  // 收件人地址
  receivedAddress: Nullable<string> = null
  // 手机号
  mobile: Nullable<string> = null
  // 会员号
  crmCode: Nullable<string> = null
}