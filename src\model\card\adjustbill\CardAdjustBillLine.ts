import IdName from 'model/common/IdName'
import MutableNsid from 'model/common/MutableNsid'

export default class CardAdjustBillLine {
    // 行号
    lineNo: Nullable<number> = null
    // 所属会员信息
    accountOwner: Nullable<MutableNsid> = null
    // 原实充金额
    oldAmount: Nullable<number> = null
    // 原返现金额
    oldGiftAmount: Nullable<number> = null
    // 调整实充金额
    occurAmount: Nullable<number> = null
    // 调整返现金额
    occurGiftAmount: Nullable<number> = null
    // 储值调整金额只用于前端展示
    totalAmount: Nullable<number> = null
    // 调整原因
    reason: Nullable<string> = null
    // 备注
    remark: Nullable<string> = null
    // 卡账户类型
    accountType: Nullable<IdName> = null
    // 卡类型
    cardType: Nullable<string> = null
    // 发生组织
    occurredOrg: Nullable<IdName> = null
    // 营销中心
    marketingCenter: Nullable<string> = null
}