// 微盟门店
export default class WeiMobCloudOrg {
  // 组织名称
  vidName: Nullable<string> = null
  // 组织 ID
  vid: Nullable<string> = null
  // 上级组织 ID。parentVid=0 表示当前组织是顶级节点。
  parentVid: Nullable<number> = null
  // 组织的自定义编码，一个店铺下组织编码是唯一的。
  vidCode: Nullable<string> = null
  // 枚举值 1-集团；2-品牌；3-区域；4部门；5-商场；6-楼层；10-门店；11-网点；100-自提点。
  vidType: Nullable<number> = null
  // 枚举值 1-启用；0-停用。
  vidStatus: Nullable<number> = null
  // 上级组织编号。组织编号是组织的自定义编号，一个店铺下组织编号是唯一的
  parentVidCode: Nullable<string> = null
}