import BrowserMgr, { LocalStorage } from 'mgr/BrowserMgr'
import store from "store/index";
import AuthorizeApi from "http/authorize/AuthorizeApi";
import { MenusFuc } from "mgr/ConstantMgr";
import Vue from "vue";

class DragItem {
  prop: string //列表头prop
  width: number //列表宽度
}
// 这里的中文不需要国际化
export default class PermissionMgr {
  static refreshPermission() {
    return new Promise((resolve, reject) => {
      AuthorizeApi.getUserPermission().then((resp: any) => {
        if (resp && resp.code === 2000) {
          let permissionsMap: any = {}
          let pageUrlsMap: any = {}
          for (let item of resp.data) {
            permissionsMap[item.resourceId] = item.actions
            pageUrlsMap[item.resourceId] = item.pageUrl
          }
          let menus = MenusFuc.menus()
          for (let i = 0; i < menus.length; i++) {
            for (let subMenu of menus[i].children) {
              subMenu.children = subMenu.children.filter((e: any) => { // 过滤二级菜单
                if (e.permissions) {
                  for (let per of e.permissions) {
                    if (permissionsMap[per.resourceId] && permissionsMap[per.resourceId].indexOf(per.action) > -1) {
                      return true
                    }
                  }
                } else if (e.permissionsFunc && e.permissionsFunc(resp.data)) {
                  return true
                }
                return false
              })
            }
            menus[i].children = menus[i].children.filter((e: any) => e.children && e.children.length > 0) // 过滤空组
          }
          menus = menus.filter((e: any) => e.children && e.children.length > 0) // 过滤无二级菜单的一级菜单
          store.dispatch('permissions', permissionsMap)
          store.dispatch('pageUrls', pageUrlsMap)

          resolve(menus)
        } else {
          reject(resp.msg)
        }
      }).catch((error) => {
        reject(error.message)
      })
    })
  }
  /**
   * 页面按钮操作权限
   */
  static hasOptionPermission(resourceId: string, action: string) {
    let permissions = store.state.permissions
    if (permissions[resourceId]) {
      return permissions[resourceId].indexOf(action) > -1
    }
    return false
  }

  //可拖拽table中，记录上次拖拽的宽度
  static tableDragend(newWidth: number, oldWidth: number, column: any) {
    console.log('看看拖拽效果', newWidth, oldWidth, column);
    let tableDragWidth: any[] = BrowserMgr.LocalStorage.getItem("tableDragWidth");
    if (!tableDragWidth?.length) {
      tableDragWidth = []
    }
    const existIndex = tableDragWidth.findIndex((item) => item.prop === column.property)
    if (existIndex > -1) {
      //之前已经记录过该分栏的宽度
      tableDragWidth[existIndex].width = column.width
    } else {
      const columnItem = new DragItem()
      columnItem.prop = column.property
      columnItem.width = column.width
      tableDragWidth.push(columnItem)
    }
    LocalStorage.setItem("tableDragWidth", tableDragWidth);
  }

  // 获取table对应列宽度
  static getColumnWidth(prop: string, width: number) {
    let tableDragWidth: any[] = BrowserMgr.LocalStorage.getItem("tableDragWidth") || [];
    const draggedWidth = tableDragWidth.find((item) => item.prop === prop)?.width
    return draggedWidth ? draggedWidth : width
  }

  /**
 * 根据系统配置获取用券门槛重复满减权限
 */
  static hasPermissionEveryfullreduction() {
    let permission = BrowserMgr.LocalStorage.getItem('sysConfig')
    if (permission.enableCouponEveryfullreduction) {
      return true
    } else {
      return false
    }
  }

  /**
   * 根据系统配置获取积分通过数量权限
   */
  static hasPermissionGainPointsByQty() {
    let permission = BrowserMgr.LocalStorage.getItem('sysConfig')
    if (permission.enableGainPointsByQty) {
      return true
    } else {
      return false
    }
  }
  static hasMemberOption() {
    let permission = BrowserMgr.LocalStorage.getItem('sysConfig')
    if (permission && permission.showMemberExtendDetail) {
      return true
    } else {
      return false
    }
  }

  static daqiaoshihuaDingkai() {
    let permission = BrowserMgr.LocalStorage.getItem('sysConfig')
    if (permission && permission.daqiaoshihuaDingkai) {
      return true
    } else {
      return false
    }
  }

  // 根据是否启用探测，是否展示total
  static getPageLayout(layout: string, probeEnabled: string) {
    if (probeEnabled === 'true') {
      const parts = layout.split(',');
      const filteredParts = parts.filter(part => (part.trim() !== 'total' && part.trim() !== 'jumper'));
      return filteredParts.join(',');
    } else {
      return layout
    }
  }

  static init() {
    Vue.prototype.hasOptionPermission = PermissionMgr.hasOptionPermission
    Vue.prototype.hasPermissionGainPointsByQty = PermissionMgr.hasPermissionGainPointsByQty
    Vue.prototype.hasMemberOption = PermissionMgr.hasMemberOption
    Vue.prototype.tableDragend = PermissionMgr.tableDragend
    Vue.prototype.getColumnWidth = PermissionMgr.getColumnWidth
    Vue.prototype.getPageLayout = PermissionMgr.getPageLayout
  }
}
