export default class HomeResult {
  code: Nullable<string>
  data: {
    consumeFirstCount: Nullable<number>,
    consumeSecondCount: Nullable<number>,
    consumeTotalCount: Nullable<number>,
    datetype: Nullable<number>,
    memberPerPrice: Nullable<number>,
    memberTradeAmount: Nullable<number>,
    memberTradeCount: Nullable<number>,
    mobileMemberRate: Nullable<number>,
    newMemberCount: Nullable<number>,
    newMobileMemberCount: Nullable<number>,
    perPrice: Nullable<number>,
    totalTradeAmount: Nullable<number>,
    totalTradeCount: Nullable<number>,
    tradeAmountRate: Nullable<number>,
    tradeCountRate: Nullable<number>,
  }
  message: Nullable<string>
  success: boolean = false
}