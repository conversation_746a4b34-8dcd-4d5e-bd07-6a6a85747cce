/*
 * @Author: 黎钰龙
 * @Date: 2023-06-25 17:10:31
 * @LastEditTime: 2023-07-05 15:30:16
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\coupon\template\CouponTemplateProperties.ts
 * 记得注释
 */

export default class CouponTemplateProperties {
  // 叠加版本
  superpositionVersion: Nullable<string> = null
  // 券模板保存效期限制多少天，超过界面将给出提示。默认为空
  templateValidateLimit: Nullable<number> = null
  // 是否使用增值商品，默认false
  appreciationGoods: boolean = false
  // 券模板账款项目取值范围，默认：customize——自定义，可选：cost_party 承担方，customize——自定义
  accountItemRange: Nullable<string> = 'customize'
}