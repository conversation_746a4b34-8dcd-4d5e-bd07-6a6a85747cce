/*
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:02
 * @LastEditTime: 2025-03-11 17:59:22
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\exportconfirm\ExportConfirm.ts
 * 记得注释
 */
import {Component, Prop, Vue} from 'vue-property-decorator'

@Component({
  name: 'ExportConfirm',
  components: {}
})
export default class ExportConfirm extends Vue {
  @Prop({
    type: String,
    default: ''
  })
  warnMsg: string
  @Prop({
    type: String,
    default: ''
  })
  checkMsg: boolean
  @Prop({
    type: String,
    default: ''
  })
  confirmBtnMsg: boolean
  @Prop({
    type: Boolean,
    default: false
  })
  dialogShow: boolean
  @Prop({
    type: String,
    default: ''
  })
  accountNm: string
  
  agreeFlag = false

  get isEnglish() {
    return sessionStorage.getItem("locale") === "en_US";
  }

  doBeforeClose(done: any) {
    this.agreeFlag = false
    this.$emit('dialogClose')
    done()
  }

  doModalConfirm() {
    this.$emit('summit', this.agreeFlag)
    this.$emit('dialogClose')
    this.agreeFlag = false
  }

  doModalClose() {
    this.agreeFlag = false
    this.$emit('dialogClose')
  }
}