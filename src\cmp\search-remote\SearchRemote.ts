import { Vue, Prop, Component } from 'vue-property-decorator';

@Component({
  name: 'SearchRemote',
})
export default class SearchRemote extends Vue {
  @Prop({ type: String, required: true })
  valueKey: string; // 值key(必传)
  @Prop({ type: Function, required: true })
  queryMethod: (query: string, page: number) => Promise<any>; // 远程搜索方法(必传)
  @Prop({ type: String, default: '' })
  width: string;
  @Prop({ type: String, default: '280px' })
  minWidth: string;
  @Prop({ type: String })
  className: string;
  /** 选中值中每一项的格式处理方法（默认不处理，返回为valueKey项的值）*/
  @Prop({ type: Function })
  valueFormat: (item: any) => any;
  @Prop({ type: Function })
  labelFormat: (item: any) => string | number;
  @Prop({ type: Boolean, default: true })
  multiple: boolean; // 是否多选
  @Prop({ type: [String, Array, Object], default: '' })
  value: any; // 默认值
  @Prop({ type: Boolean, default: false })
  disabled: boolean; // 是否禁用
  @Prop({ type: String, default: '请输入关键词' })
  placeholder: string; // 占位符
  @Prop({ type: Boolean, default: true })
  clearable: boolean; // 是否可以清空选项
  @Prop({ type: String, default: '加载中' })
  loadingText: string; // 远程加载时显示的文字
  @Prop({ type: String })
  size: 'medium' | 'small' | 'mini'; // 输入框尺寸
  @Prop({ type: String })
  popperClass: string; // Select 下拉框的类名
  @Prop({ type: Boolean, default: false })
  collapseTags: string; // 多选时是否将选中值按文字的形式展示
  @Prop({ type: Number, default: 0 })
  multipleLimit: number; // 多选时用户最多可以选择的项目数，为 0 则不限制
  @Prop({ type: String, default: '无数据' })
  noDataText: string; // 选项为空时显示的文字，也可以使用slot="empty"设置
  @Prop({ type: String, default: '无匹配数据' })
  noMatchText: string; // 搜索条件无匹配时显示的文字，也可以使用slot="empty"设置
  @Prop({ type: Boolean, default: false })
  defaultFirstOption: boolean; // 在输入框按下回车，选择第一个匹配项
  @Prop({ type: Boolean, default: false })
  lazyLoad: boolean; // 是否开启懒加载
  @Prop({ type: Boolean, default: true })
  popperAppendToBody: boolean; // 是否将弹出框插入至 body 元素
  @Prop({ type: Boolean, default: true })
  reserveKeyword: boolean; //多选且可搜索时，是否在选中一个选项后保留当前的搜索关键词
  @Prop({ type: Boolean, default: true })
  trim: boolean; // 输入框去除两边空格
  @Prop({ type: Boolean, default: true })
  filterable: boolean; // 是否可以搜索
  $refs: any;
  options: any[] = []; // 选项列表
  loading = false; // 加载动画
  page = 0; // 懒加载-当前页
  total = 0; // 懒加载-总数
  isScroll = false; // 是否已滚动
  isVisiable = false; // 下拉框是否展示

  get localValue(): any | any[] {
    if (this.multiple && !Array.isArray(this.value)) {
      console.error('参数类型错误：multiple为true，value的类型必须是数组！');
    }
    if (!this.multiple && Array.isArray(this.value)) {
      console.error('参数类型错误：multiple为false，value的类型必须是字符串！');
    }
    return this.value;
  }

  set localValue(val: any | any[]) {
    console.log(val);

    this.$emit('input', val);
  }

  get selectElm(): HTMLDivElement {
    return this.$refs.select.$el;
  }

  get inputElm(): HTMLInputElement {
    return this.$refs.select.$el.querySelector('input');
  }

  get popperElm(): HTMLDivElement {
    return this.$refs.select.popperElm;
  }

  get arrowIconElm(): HTMLDivElement {
    return this.selectElm.querySelector('.el-select__caret') as HTMLDivElement;
  }

  get scrollbar() {
    return this.$refs.select.$refs.scrollbar;
  }

  get popoverDisabled() {
    if (this.multiple && this.collapseTags) {
      if (this.localValue.length > 1) {
        return false;
      } else {
        return true;
      }
    } else {
      return true;
    }
  }

  get selectLabels() {
    return this.$refs.select?.selected.map((item:any) => item.label);
  }

  mounted() {
    this.remoteMethod('');
    if (this.lazyLoad) {
      this.onScroll();
    }
    if (this.trim) {
      this.onTrim();
    }
    // 下拉框的长度等于选择框的长度
    this.popperElm.style.maxWidth = this.selectElm.clientWidth + 'px';
    // 下拉框图标
    this.arrowIconElm.classList.add('el-icon-arrow-up');
  }

  beforeDestroy() {
    if (this.lazyLoad) {
      this.offScroll();
    }
    if (this.trim) {
      this.offTrim();
    }
  }

  /**
   * 输入框去除两边空格
   * @param e
   */
  trimFunc(e: any) {
    if (typeof e.target.value === 'string') {
      e.target.value = e.target.value.trim();
    }
  }

  /**
   * 下拉框滚动到底部，远程加载数据
   */
  scrollFunc(e: any) {
    //变量scrollHeight是滚动条的总高度
    const scrollHeight = this.scrollbar.$refs.resize.clientHeight;
    //变量scrollTop是滚动条滚动时，距离顶部的距离
    const scrollTop = Math.ceil(e.target.scrollTop);
    //变量clientHeight是可视区的高度
    const clientHeight = e.target.clientHeight;
    //滚动条到底部的条件
    if (scrollTop + clientHeight >= scrollHeight && this.isScroll) {
      //到了这个就可以进行业务逻辑加载后台数据了
      if (this.total > this.options.length) {
        this.page++;
        this.queryMethod(this.inputElm.value, this.page).then((res) => {
          this.options = [...this.options, ...res.data];
          this.total = res.total;
        });
      }
    }
    this.isScroll = true;
  }

  onScroll() {
    this.scrollbar.$refs.wrap.addEventListener('scroll', this.scrollFunc);
  }

  offScroll() {
    this.scrollbar.$refs.wrap.removeEventListener('scroll', this.scrollFunc);
  }

  onTrim() {
    this.inputElm.addEventListener('input', this.trimFunc);
  }

  offTrim() {
    this.inputElm.removeEventListener('input', this.trimFunc);
  }

  /**
   * 远程方法
   * @param query
   */
  remoteMethod(query: string) {
    this.page = 0;
    this.isScroll = false;
    this.loading = true;
    this.queryMethod(query, this.page)
      .then((res) => {
        this.options = res.data;
        this.total = res.total;
      })
      .catch((err) => {
        this.$message.error(err.message);
      })
      .finally(() => {
        this.loading = false;
      });
  }

  /**
   * 处理选中的值的格式
   * @param item
   * @returns
   */
  optionValue(item: any) {
    return this.valueFormat ? this.valueFormat(item) : item[this.valueKey];
  }

  /**
   * 处理选中值的展示
   * @param item
   * @returns
   */
  optionLabel(item: any) {
    return this.labelFormat && this.labelFormat(item);
  }

  /**
   * 输入框聚焦事件
   * @returns
   */
  onFocus() {
    if (this.isVisiable) return;
    if (this.multiple) {
      if (this.localValue.length > 0) return;
    } else {
      if (this.localValue) return;
    }
    this.remoteMethod('');
  }

  /**
   * 下拉框展示与隐藏
   * @param visible
   */
  onVisibleChange(visible: boolean) {
    this.arrowIconElm.classList.toggle('is-reverse');
    this.isVisiable = visible;
  }
}
