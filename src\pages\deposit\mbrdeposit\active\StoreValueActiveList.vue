<template>
    <div class="store-value-active-list">
        <BreadCrume :panelArray="panelArray">
            <template slot="operate">
                <el-button v-if="hasOptionPermission('/储值/储值活动/充值有礼/储值充值活动', '活动维护')" @click="doStoreValueAddActive" size="small" type="primary">新建储值充值活动</el-button>
            </template>
        </BreadCrume>
        <ListWrapper class="current-page" style="height: 95%">
            <template slot="query">
                <el-row>
                    <el-col :span="8">
                        <form-item label="活动名称" labelWidth="120px">
                            <el-input  v-model="query.nameLikes"></el-input>
                        </form-item>
                    </el-col>
                    <el-col :span="8">
                        <form-item label="活动号" labelWidth="120px">
                            <el-input  v-model="query.activityIdLikes"></el-input>
                        </form-item>
                    </el-col>
                    <el-col :span="8">
                        <form-item>
                            <el-button @click="doSearch" type="primary">查询</el-button>
                            <el-button @click="doReset">重置</el-button>
                        </form-item>
                    </el-col>
                </el-row>

            </template>
            <template slot="btn">

            </template>
            <template slot="list">
                <el-tabs @tab-click="doHandleClick" v-model="activeName">
                    <el-tab-pane :label="getAllCount" name="first">
                      <el-checkbox
                          @change="checkedAllRow"
                          style="margin-left: 14px;margin-right: 10px"
                          v-model="singleAll">
                      </el-checkbox>
                      <i18n k="/储值/会员储值/储值充值活动/列表页面/已选择{0}个活动">
                        <template slot="0">
                          &nbsp;<span>{{ selectedArr.length }}</span>&nbsp;
                        </template>
                      </i18n>&nbsp;&nbsp;
                      <el-button v-if="!isOaActivity && hasOptionPermission('/储值/储值活动/充值有礼/储值充值活动', '活动审核')" @click="doBatchAudit">批量审核</el-button>
                      <el-button v-if="hasOptionPermission('/储值/储值活动/充值有礼/储值充值活动', '活动终止')" @click="doBatchEnd">批量终止</el-button>
                      <el-button v-if="hasOptionPermission('/储值/储值活动/充值有礼/储值充值活动', '活动维护')" @click="doBatchDelete" style="color: red">批量删除</el-button>

                    </el-tab-pane>
                    <el-tab-pane :label="getNoAudit" name="second">
                        <el-checkbox
                                @change="checkedAllRow"
                                style="margin-left: 14px;margin-right: 10px"
                                v-model="singleAll">
                        </el-checkbox>
                      <i18n k="/储值/会员储值/储值充值活动/列表页面/已选择{0}个活动">
                        <template slot="0">
                          &nbsp;<span>{{ selectedArr.length }}</span>&nbsp;
                        </template>
                      </i18n>&nbsp;&nbsp;
                        <el-button @click="doBatchAudit" v-if="!isOaActivity && hasOptionPermission('/储值/储值活动/充值有礼/储值充值活动', '活动审核')">批量审核</el-button>
                        <el-button  @click="doBatchDelete" v-if="hasOptionPermission('/储值/储值活动/充值有礼/储值充值活动', '活动维护')" style="margin-left: 20px;color: red">批量删除</el-button>
                    </el-tab-pane>
                    <el-tab-pane :label="getAuditing" v-if="isOaActivity" name="auditing">
                    </el-tab-pane>
                    <el-tab-pane :label="getReject" v-if="isOaActivity" name="reject">
                      <el-checkbox @change="checkedAllRow" v-model="singleAll" style="margin-left: 14px;margin-right: 10px">
                      </el-checkbox>
                      <i18n k="/储值/会员储值/储值充值活动/列表页面/已选择{0}个活动">
                        <template slot="0">
                          <span>{{ selectedArr.length }}</span>
                        </template>
                      </i18n>
                        <el-button  @click="doBatchDelete" v-if="hasOptionPermission('/储值/储值活动/充值有礼/储值充值活动', '活动维护')" style="margin-left: 20px;color: red">批量删除</el-button>
                    </el-tab-pane>
                    <el-tab-pane :label="getNoStart" name="third">
                        <el-checkbox
                                @change="checkedAllRow"
                                style="margin-left: 14px;margin-right: 10px"
                                v-model="singleAll">
                        </el-checkbox>
                      <i18n k="/储值/会员储值/储值充值活动/列表页面/已选择{0}个活动">
                        <template slot="0">
                          &nbsp;<span>{{ selectedArr.length }}</span>&nbsp;
                        </template>
                      </i18n>&nbsp;&nbsp;
                        <el-button v-if="hasOptionPermission('/储值/储值活动/充值有礼/储值充值活动', '活动终止')"  @click="doBatchEnd">批量终止</el-button>
                    </el-tab-pane>
                    <el-tab-pane :label="getDoing" name="forth">
                        <el-checkbox
                                @change="checkedAllRow"
                                style="margin-left: 14px;margin-right: 10px"
                                v-model="singleAll">
                        </el-checkbox>
                      <i18n k="/储值/会员储值/储值充值活动/列表页面/已选择{0}个活动">
                        <template slot="0">
                          &nbsp;<span>{{ selectedArr.length }}</span>&nbsp;
                        </template>
                      </i18n>&nbsp;&nbsp;
                        <el-button v-if="hasOptionPermission('/储值/储值活动/充值有礼/储值充值活动', '活动终止')"  @click="doBatchEnd">批量终止</el-button>
                    </el-tab-pane>
                    <el-tab-pane :label="getEnd" name="five">
                    </el-tab-pane>
                </el-tabs>
                <el-table
                        ref="table"
                        :data="tableData"
                        @selection-change="handleSelectionChange"
                        style="width: 100%;margin-top: 10px">
                    <el-table-column
                            v-if="activeName !== 'five'"
                            type="selection"
                            width="55">
                    </el-table-column>
                    <el-table-column label="活动号/活动名称" prop="body.name" width="200">
                        <template slot-scope="scope">
                             <p>{{scope.row.body.activityId || '后端没返回'}}</p> 
                            <span class="span-btn" no-i18n @click="doDtl(scope.row)" :title="scope.row.body.name">
                              {{ scope.row.body.name || '后端没返回' }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="活动时间" prop="body.beginDate">
                        <template slot-scope="scope">
                          <div no-i18n>
                            {{ scope.row.body.beginDate | dateFormate2 }}{{ formatI18n('/公用/公共组件/积分活动选择弹框组件/表格/至') }}{{ scope.row.body.endDate | dateFormate2 }}
                          </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="状态" prop="body.state" align="center">
                      <template slot-scope="scope">
                        <ActivityStateTag :stateEquals="scope.row.body.state"></ActivityStateTag>
                      </template>
                    </el-table-column>
                    <el-table-column label="活动渠道" prop="body.channels[0].type">
                        <template slot-scope="scope">
                          <div v-if="scope.row.channelName">{{ scope.row.channelName }}</div>
                          <div v-else>{{scope.row.body.channels[0].type | storeValueTypeFmt}}</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作">
                      <template slot-scope="scope">
                        <el-button key="1" v-if="scope.row.body.state === 'INITAIL' && !isOaActivity && hasOptionPermission('/储值/储值活动/充值有礼/储值充值活动', '活动审核')" type="text"
                                   @click="doAudit(scope.row)">审核
                        </el-button>
                        <el-button v-if="hasOptionPermission('/储值/储值活动/充值有礼/储值充值活动', '活动维护')" key="2" type="text" @click="doCopy(scope.row)">复制</el-button>
                        <el-button key="3" v-if="['INITAIL','REJECTED','UNSTART', 'PROCESSING'].indexOf(scope.row.body.state) > -1 && hasOptionPermission('/储值/储值活动/充值有礼/储值充值活动', '活动维护')" type="text"
                                   @click="doModify(scope.row)">修改
                        </el-button>
                        <el-button key="4" v-if="['INITAIL','REJECTED'].indexOf(scope.row.body.state) > -1 && hasOptionPermission('/储值/储值活动/充值有礼/储值充值活动', '活动维护')" type="text"
                                   @click="doDelete(scope.row)">删除
                        </el-button>
                        <el-button key="5"
                                   v-if="(scope.row.body.state === 'PROCESSING' ||  scope.row.body.state === 'STOPED') && hasOptionPermission('/储值/储值活动/充值有礼/储值充值活动', '活动查看')"
                                   type="text" @click="doValidate(scope.row)">效果评估
                        </el-button>
                        <el-button key="6"
                                   v-if="(scope.row.body.state === 'PROCESSING' ||  scope.row.body.state === 'UNSTART') && hasOptionPermission('/储值/储值活动/充值有礼/储值充值活动', '活动终止')"
                                   type="text" @click="doStop(scope.row)">终止
                        </el-button>
                      </template>
                    </el-table-column>
                </el-table>
            </template>
            <!--分页栏-->
            <template slot="page">
                <el-pagination
                    no-i18n
                    :current-page="page.currentPage"
                    :page-size="page.size"
                    :page-sizes="[10, 20, 30, 40]"
                    :total="page.total"
                    @current-change="onHandleCurrentChange"
                    @size-change="onHandleSizeChange"
                    background
                    layout="total, prev, pager, next, sizes,  jumper">
                </el-pagination>
            </template>
        </ListWrapper>
        <ExportConfirm :dialogShow="exportDialogShow"
                       :warnMsg="warnMsg"
                       :confirmBtnMsg="formatI18n('/公用/按钮', '审核')"
                       :checkMsg="formatI18n('/储值/会员储值/储值充值活动/新建界面/存在冲突活动/点击保存并审核/弹框提示', '已知晓冲突活动，并确认继续执行审核')"
                       @dialogClose="doExportDialogClose"
                       @summit="doConfirmSummit"></ExportConfirm>
    </div>
</template>

<script lang="ts" src="./StoreValueActiveList.ts">
</script>

<style lang="scss">
    .store-value-active-list{
        background-color: white;
        height: 100%;
        width: 100%;
        overflow: hidden;
        .current-page{
            .el-select{
                width: 100%;
            }
        }

        .el-range-editor.el-input__inner{
            width: 100%;
        }
    }
</style>