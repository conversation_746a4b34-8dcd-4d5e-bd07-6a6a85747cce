import { Component, Vue } from "vue-property-decorator";
import SubHeader from "cmp/subheader/SubHeader.vue";
import GoodsScopeDtl from "cmp/goodsscope/GoodsScopeDtl.vue";
import IdName from "model/common/IdName";
import ActiveStoreDtl from "cmp/activestoredtl/ActiveStoreDtl";
import I18nPage from "common/I18nDecorator";
import BreadCrume from "cmp/bread-crumb/BreadCrume.vue";
import ListWrapper from "cmp/list/ListWrapper.vue";
import FloatBlock from "cmp/floatblock/FloatBlock.vue";
import State from "model/deposit/store/State";
import StoreSelectorDialog from "cmp/selectordialogs/StoreSelectorDialog";
import AutoFixInput from "cmp/autofixinput/AutoFixInput";
import EnvUtil from "util/EnvUtil";
import DownloadCenterDialog from "pages/main/dowmload_center_dialog/DownloadCenterDialog";
import ZoneApi from "http/area/ZoneApi";
import ZoneFilter from "model/datum/zone/ZoneFilter";
import OrgCardSettingApi from "http/prepay/prepayStore/OrgCardSettingApi";
import OrgCardSettingFilter from "model/prepay/prepaycardStore/OrgCardSettingFilter";
import OrgCardSettingCreateRequest from "model/prepay/prepaycardStore/OrgCardSettingCreateRequest";
import OrgCardSetting from "model/prepay/prepaycardStore/OrgCardSetting";
import { RecycleType } from "model/prepay/prepaycardStore/RecycleType";
import { RecycleScope } from "model/prepay/prepaycardStore/RecycleScope";
import OrgCardSettingModifyRequest from "model/prepay/prepaycardStore/OrgCardSettingModifyRequest";
import RSOrgFilter from "model/common/RSOrgFilter";
import OrgApi from "http/org/OrgApi";
import SelectStores from "cmp/selectStores/SelectStores";

import Redemption from './Redemption'
import UploadApi from "http/upload/UploadApi";

@Component({
	name: "StoreValueAccount",
	components: {
		SubHeader,
		GoodsScopeDtl,
		ActiveStoreDtl,
		BreadCrume,
		ListWrapper,
		FloatBlock,
		StoreSelectorDialog,
		AutoFixInput,
		DownloadCenterDialog,
		Redemption,
    SelectStores
	},
})
@I18nPage({
	prefix: ["/储值/会员储值/门店储值管理", "/储值/会员储值/门店预付卡管理", "/公用/提示",'/公用/菜单'],
})
export default class PrepayCardStore extends Vue {
	i18n: (str: string, params?: string[]) => string;

	data: OrgCardSetting[] = [];
	currentStores: IdName[] = [];
	$refs: any;
	panelArray: any = [];
	page = {
		currentPage: 1,
		total: 0,
		size: 10,
	};
	state: State = new State();
	selected: OrgCardSetting[] = [];
	queryFilter: OrgCardSettingFilter = new OrgCardSettingFilter();
	stateShow: Boolean = false;
	stateContent: String = "";
	stateTile: string = "";

	setupShow: Boolean = false;
	areaData: any = []; // 区域查询
	ZoneFilter: ZoneFilter = new ZoneFilter();
	currentSetup: Nullable<OrgCardSetting> = new OrgCardSetting();
	batchSetupShow: Boolean = false;
	batchSetup: Nullable<OrgCardSetting> = new OrgCardSetting();
	fileDialogVisible: Boolean = false;

	importShow: Boolean = false;
	uploadHeaders: any = {};
	fileCount = 0;
	showTip = false;

	get uploadUrl() {
		return EnvUtil.getServiceUrl() + "v1/orgCard/setting/importConfig";
	}
	get templateUrl() {
		if (location.href.indexOf("localhost") === -1) {
			return "template_import_card_recycling_settings.xlsx";
		} else {
			return "template_import_card_recycling_settings.xlsx";
		}
	}
	created() {
		this.panelArray = [
			{
				name: this.i18n("退卡设置"),
				url: "",
			},
		];
		this.getAreaList();
		this.query();
	}

	/**
	 * 查询区域
	 */
	getAreaList() {
		// this.ZoneFilter.page = 0
		// this.ZoneFilter.pageSize = 10
		ZoneApi.query(this.ZoneFilter).then((res) => {
			if (res.code === 2000) {
				this.areaData = res.data;
			} else {
				this.$message.error(res.msg as string);
			}
		});
	}

	handleSelectionChange(val: any) {
		this.selected = val;
		console.log(this.selected);
	}

	openEdit() {
		this.$refs.storeSelectorDialog.dialogShow = true;
		this.$refs.storeSelectorDialog.title = this.formatI18n("/储值/会员储值/门店储值管理/新增");
	}

	openDialog() {
		this.$refs.Redemption.openDialog()
		// this.$refs.storeSelectorDialog.title = this.formatI18n("/储值/会员储值/门店储值管理/新增");
	}



	selectStore(selected: any) {
		console.log(selected);
		if (selected.length == 0) {
			return;
		}
		let params = new OrgCardSettingCreateRequest();
		params.recycleType = RecycleType.RECHARGE_BALANCE;
		params.recycleScope = RecycleScope.LESS;
		params.orgIds = selected.map((item: any) => {
			return item.org.id;
		});
		params.operator = JSON.parse(sessionStorage.getItem("vuex") as string)?.loginInfo.user?.account;
		OrgCardSettingApi.create(params).then((res: any) => {
			if (res.code == 2000) {
				this.$message.success(this.formatI18n("/储值/会员储值/门店储值管理/新增成功"));
				this.query();
			} else {
				this.$message.error(res.msg as string);
			}
		});
	}

	onHandleCurrentChange(val: number) {
		this.page.currentPage = val;
		this.query();
	}

	/**
	 * 每页多少条的回调
	 * @param val
	 */
	onHandleSizeChange(val: number) {
		this.page.size = val;
		this.query();
	}
	doReset() {
		this.queryFilter = new OrgCardSettingFilter();
		this.query();
	}
	doSearch() {
		this.query();
	}
	query() {
		this.queryFilter.page = this.page.currentPage - 1;
		this.queryFilter.pageSize = this.page.size;

		OrgCardSettingApi.query(this.queryFilter)
			.then((resp: any) => {
				if (resp && resp.data) {
					this.data = resp.data;
					this.page.total = resp.total;
				}
			})
			.catch((error) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}

	openImport() {
		this.importShow = true;
	}
	handleImportClose() {
		this.importShow = false;
	}
	handleImportConfirm() {
		this.$refs.upload.submit();
	}

	openSetup(row: OrgCardSetting) {
		this.currentSetup = JSON.parse(JSON.stringify(row));
		this.setupShow = true;
	}
	openBatch() {
		if (this.selected.length === 0) {
			this.$message.warning(this.formatI18n("/储值/会员储值/门店储值管理/请至少选择一个门店"));
		} else {
			this.batchSetup!.moneyScope = 0;
			this.batchSetup!.recycleType = RecycleType.RECHARGE_BALANCE;
			this.batchSetup!.recycleScope = RecycleScope.LESS;
			this.batchSetupShow = true;
		}
	}
	handleSetupClose() {
		this.setupShow = false;
	}
	handleSetupConfirm() {
		let params = new OrgCardSettingModifyRequest();
		if (this.currentSetup) {
			params.moneyScope = this.currentSetup!.moneyScope;
			params.orgIdsIn = [this.currentSetup!.orgId as string];
			params.recycleScope = this.currentSetup!.recycleScope;
			params.recycleType = this.currentSetup!.recycleType;
			params.operator = JSON.parse(sessionStorage.getItem("vuex") as string)?.loginInfo.user?.account;
			OrgCardSettingApi.modify(params).then((res) => {
				if (res.code == 2000) {
					this.$message.success(this.formatI18n("/公用/活动/提示信息/操作成功"));
					this.setupShow = false;
					this.query();
				} else {
					this.$message.error(res.msg as string);
				}
			});
		}
	}
	handleBatchSetupClose() {
		this.batchSetupShow = false;
	}
	handleBatchSetupConfirm() {
		let params = new OrgCardSettingModifyRequest();
		params.moneyScope = this.batchSetup!.moneyScope;
		params.orgIdsIn = this.selected.map((item: OrgCardSetting) => {
			return item.orgId as string;
		});
		params.recycleScope = this.batchSetup!.recycleScope;
		params.recycleType = this.batchSetup!.recycleType;
		params.operator = JSON.parse(sessionStorage.getItem("vuex") as string)?.loginInfo.user?.account;
		OrgCardSettingApi.modify(params).then((res) => {
			if (res.code == 2000) {
				this.$message.success(this.formatI18n("/公用/活动/提示信息/操作成功"));
				this.batchSetupShow = false;
				this.query();
			} else {
				this.$message.error(res.msg as string);
			}
		});
	}

	private initUploadHeaders() {
		let locale = sessionStorage.getItem("locale");
		this.uploadHeaders = {
			locale: locale ? locale : "zh_CN",
			time_zone: new Date().getTimezoneOffset(),
			marketingCenter: sessionStorage.getItem("marketCenter"),
		};
    const authorization = EnvUtil.getUniAuthorization()
    if (authorization) {
      this.uploadHeaders.authorization = authorization
    }
	}
	doHandleChange(file: any, fileList: any) {
		if (fileList.length > 0) {
			this.fileCount++;
		}
	}
	getErrorInfo(a: any, b: any, c: any) {
		this.$message.error(this.formatI18n("/公用/导入", "导入失败，请重新导入") as any);
		this.fileCount = 0;
		this.$refs.upload.clearFiles();
	}
	getSuccessInfo(response: any, b: any, c: any) {
		console.log(response);

		if (response.code === 2000) {
			//   this.formData.filename = response.data
			// this.$message.success(this.formatI18n('/公用/活动/提示信息/操作成功'))
			this.importShow = false;
			this.fileCount = 0;
			this.$refs.upload.clearFiles();
			this.exportAfter();
		} else {
			this.fileCount = 0;
			this.$refs.upload.clearFiles();
			this.$message.error(response.msg);
		}
	}
	doDownloadDialogClose() {
		this.showTip = false;
		this.fileDialogVisible = false;
		this.query();
	}
	exportAfter() {
		this.showTip = true;
		this.fileDialogVisible = true;
	}

	downloadTemplate() {
		UploadApi.getUrl(this.templateUrl).then((resp: any) => {
			if (resp && resp.data) {
				window.open(resp.data);
			}
		}).catch((error) => {
			if (error && error.message) {
				this.$message.error(error.message)
			}
		})
	}
}
