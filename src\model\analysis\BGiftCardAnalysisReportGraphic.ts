import AnalysisChartData from 'model/analysis/AnalysisChartData'
import { AnalysisReportDateUnit } from 'model/analysis/AnalysisReportDateUnit'

// 礼品卡分析图像
export default class BGiftCardAnalysisReportGraphic {
  // 门店
  occurredOrgId: Nullable<string> = null
  // 模板
  templateNumber: Nullable<string> = null
  // 报表分析单位，决定BCardAnalysisReportGraphicData坐标取值是 周、月、日
  dateUnit: Nullable<AnalysisReportDateUnit> = null
  // 买卡数据线
  buyCard: AnalysisChartData[] = []
  // 消费
  pay: AnalysisChartData[] = []
  // 转出数据线
  transferOut: AnalysisChartData[] = []
  // 调整加
  adjustAdd: AnalysisChartData[] = []
  // 调整减
  adjustSub: AnalysisChartData[] = []
  // 作废
  cancel: AnalysisChartData[] = []

  // 可用余额
  usableTotalAmount: AnalysisChartData[] = []
  // 可用本金
  usableAmount: AnalysisChartData[] = []
  // 可用赠金
  usableGiftAmount: AnalysisChartData[] = []
}