import {Component, Inject, Prop, Watch} from 'vue-property-decorator'
import GoodsApi from 'http/goods/GoodsApi'
import RSGoodsFilter from 'model/common/RSGoodsFilter'
import RSGoods from 'model/common/RSGoods'
import AbstractSelectDialog from './AbstractSelectDialog'

import VirtualList from "vue-virtual-scroll-list";
import ItemComponent from './ItemComponent'
import RSMarketingCenter from 'model/common/RSMarketingCenter'
import MarketingCenterApi from 'http/marketingcenter/MarketingCenterApi'
import RSMarketingCenterFilter from 'model/common/RSMarketingCenterFilter'
import I18nPage from 'common/I18nDecorator'

@Component({
	name: "GoodsSelectorDialog",
	components: {
		VirtualList,
	},
})
@I18nPage({
  prefix: [
    '/公用/券模板'
  ],
  auto: true
})
export default class GoodsSelectorDialog extends AbstractSelectDialog<RSGoods> {
	@Prop({
		type: String,
		default: "barcode",
	  })
	  goodsMatchRuleMode: "barcode" | "code";
	@Prop()
	  maxSelect: number;
    @Prop({ type: Boolean, default: false }) isSelectSpecialGoods: boolean; //是否为会员专享商品
    @Prop({ type: Boolean, default: false }) isSelectByCode: boolean; //是否根据商品code作为唯一标识(为false则以barcode作为唯一标识)
	goodsFilter: RSGoodsFilter = new RSGoodsFilter();
	itemComponent: any = ItemComponent;
	$eventHub: any;
	currentFakePage: number = 1;
	pageFakeSize: number = 10;
	totalFake: number = 0;
	@Prop({ type: Boolean, default: false })
	appreciationGoods: boolean;
	@Prop({ type: String, default: 'normal' })
	chooseGoodType: String;


	get tableRowSpan() {
    return this.isSelectByCode ? 11 : 6
  }

	get selectable() {
		if (this.maxSelect) {
			return this.selected.length < this.maxSelect;
		}
		return true;
	}

	get allSelectable() {
		if (this.maxSelect) {
			return this.selected.length + this.currentList.length <= this.maxSelect;
		}
		return true;
	}

  get tableFakeData() {
    const startIndex = (this.currentFakePage - 1) * this.pageFakeSize
    const endIndex = (this.currentFakePage) * this.pageFakeSize
    return this.filteredSelected.slice(startIndex, endIndex)
  }

  // 会员专享：根据商品代码和名称筛选；非会员专享：根据商品条码和名称筛选
  filterSelected() {
    if (this.totalFake <= 11) {
      this.currentFakePage = 1
    }
    if (this.selectedFilter) {
      this.filteredSelected = this.selected.filter((e: any) => {
        return (
          this.getId(e).indexOf(this.selectedFilter) > -1 ||
          this.getName(e).indexOf(this.selectedFilter) > -1
        )
      })
    } else {
      this.filteredSelected = this.selected
    }
    this.totalFake = this.filteredSelected.length;
  }

	created() {
		// this.$eventHub.$on("delItem", this.delItem);
	}

	reset() {
		this.goodsFilter = new RSGoodsFilter();
	}

	getId(ins: RSGoods): string { //特殊商品用code作为唯一标识，普通商品用barcode
    if (this.goodsMatchRuleMode == 'code') {
      return ins.code!+ins.qpcStr!
    } else {
      return ins.barcode!
    }
	}

	getName(ins: RSGoods): string {
		// @ts-ignore
		return ins.name;
	}

	getResponseData(response: any): any {
		return response.data;
	}

	queryFun(): Promise<any> {
		this.goodsFilter.page = this.page.currentPage - 1;
		this.goodsFilter.pageSize = this.page.size;
		let oldMarketCenter = sessionStorage.getItem('marketCenter') || ''
		sessionStorage.setItem('marketCenter', this.marketCenter)
    this.goodsFilter.memberGoodsEquals = this.isSelectSpecialGoods ? true : null
	this.goodsFilter.groupByCode = this.goodsMatchRuleMode == 'code'
	if (this.appreciationGoods) {
		if (this.chooseGoodType == 'normal') {
			this.goodsFilter.appreciationGoodsEquals = false
		} else {
			this.goodsFilter.appreciationGoodsEquals = true
		}
	} else {
		this.goodsFilter.appreciationGoodsEquals = false
	}
		return GoodsApi.query(this.goodsFilter).finally(()=>{
			sessionStorage.setItem('marketCenter', oldMarketCenter as string)
		});
	}

	clickRow(item: RSGoods, index: number) {
		if (this.model === "single") {
			this.dialogShow = false;
			this.$emit("summit", item);
		} else if (this.model === "multiple") {
			this.doCheckRow(index);
		}
	}

	handleFakeCurrentChange(val: any) {
    console.log(val);
    this.currentFakePage = val;
	}
}
