<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:02
 * @LastEditTime: 2024-03-18 17:01:09
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\activestore\ActiveStoreArea.vue
 * 记得注释
-->
<template>
  <div>
    <el-row>
      <span class="plain-btn-blue" @click="openZone">
        {{formatI18n('/公用/门店组件/添加区域')}}
      </span>
    </el-row>
    <div class="area-active-store-view" v-if="areaList.length">
      <el-form :model="store" ref="form">
        <el-row style="background: #F0F2F6">
          <el-col :span="6" class="title">
            {{i18n('区域名称')}}
          </el-col>
          <el-col :span="16" class="title">
            {{i18n('门店范围')}}
          </el-col>
          <el-col :span="2" class="title">
            {{i18n('操作')}}
          </el-col>
        </el-row>
        <el-row v-for="(item, index) in areaList" :key="index">
          <el-col :span="6">
            <div class="ellipse" :title="item.zone.name">{{item.zone.name}}</div>
          </el-col>
          <el-col :span="16">
            <el-radio-group @change="doStoreRange(index)" v-model="item.store.marketingCenters[0].stores.storeRangeType" style="margin-left:10px">
              <el-radio label="ALL">{{ formatI18n("/公用/券模板", "全部门店") }}</el-radio>
              <el-radio label="PART">
                {{ i18n("指定门店适用") }}
                <template v-if="item.store.marketingCenters[0].stores.storeRangeType === 'PART'">
                  <span @click="doSelect(index,item)" v-if=" !item.store.marketingCenters[0].stores.zones[0].stores.stores.length"
                    style="color:#20A0FF">
                    {{i18n('选择门店')}}
                  </span>
                  <span v-else>
                    {{i18n('已选择')}}
                    <span class="number-text">{{item.store.marketingCenters[0].stores.zones[0].stores.stores.length}}</span>
                    {{i18n('家门店')}}
                    <span style="color:#20A0FF" @click="doSelect(index)">{{i18n('修改')}}</span>
                  </span>
                </template>
              </el-radio>
              <el-radio label="EXCLUDE">
                {{i18n("指定门店不适用")}}
                <template v-if="item.store.marketingCenters[0].stores.storeRangeType === 'EXCLUDE'">
                  <span @click="doSelect(index,item)" v-if=" !item.store.marketingCenters[0].stores.zones[0].stores.stores.length"
                    style="color:#20A0FF">
                    {{i18n('选择门店')}}
                  </span>
                  <span v-else>
                    {{i18n('已选择')}}
                    <span class="number-text">{{item.store.marketingCenters[0].stores.zones[0].stores.stores.length}}</span>
                    {{i18n('家门店')}}
                    <span style="color:#20A0FF" @click="doSelect(index)">{{i18n('修改')}}</span>
                  </span>
                </template>
              </el-radio>
            </el-radio-group>
            <StoreSelectorDialog ref="selectGoodsScopeDialog" @summit="doSubmitGoods($event, index)" :zoneId="item.zone.id"></StoreSelectorDialog>
            <ImportDialog :dialogShow.sync="importDialogShow" :importUrl="importUrl" :templatePath="templatePath"
              @dialogClose="importDialogShow = false" @upload-success="doUploadSuccess" :templateName="formatI18n('/公用/券模板', '门店模板')"
              :title="formatI18n('/公用/券模板', '导入')" :isSingle="true">
            </ImportDialog>
          </el-col>
          <el-col :span="2">
            <el-button type="text" @click="deleteZone(index)" style="margin-left:4px">{{i18n('移除')}}</el-button>
          </el-col>
        </el-row>

      </el-form>
    </div>

    <ZoneSelectorDialog ref="zoneSelectorDialog" @summit="doSubmitZones"> </ZoneSelectorDialog>
    <PromotionCenterSelectorDialog ref="selectPromotionCenterSelectorDialog" @summit="doPromotionSubmitGoods"></PromotionCenterSelectorDialog>

    <ImportResultDialog :data="importResult" :dialogShow="importResultDialogShow" @importResultDialogClose="importResultDialogShow = false">
    </ImportResultDialog>
  </div>
</template>

<script lang="ts" src="./ActiveStoreArea.ts"></script>

<style lang="scss" scoped>
.area-active-store-view {
  max-width: 900px;
  border: solid 1px #f2f2f2;
  .promotion-center-store {
    max-width: 800px;
  }

  .title {
    padding-left: 10px;
    height: 32px;
    line-height: 32px;
    font-size: 13px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #242633;
  }

  .rows {
    padding: 10px;
    border: 1px solid #ced0da;
    border-top: none;
  }
  .ellipse {
    width: 100%;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    height: 32px;
    padding-left: 12px;
  }
}
</style>
