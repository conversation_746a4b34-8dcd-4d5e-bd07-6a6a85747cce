import I18nPage from "common/I18nDecorator";
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import ContentTemplate from "model/template/ContentTemplate";
import ListWrapper from "cmp/list/ListWrapper.vue";
import FormItem from "cmp/formitem/FormItem.vue";
import { EditMode } from "model/local/EditMode";
import CommonUtil from "util/CommonUtil";
import UnionActivityQuery from "model/promotion/UnionActivityQuery";
import GiftCardActivityFilter from 'model/card/activity/GiftCardActivityFilter'
import CouponActivityFilter from 'model/v2/coupon/CouponActivityFilter'
import UnionActivityApi from "http/promotion/UnionActivityApi";
import CouponActivityApi from "http/v2/coupon/CouponActivityApi";
import GiftCardActivityApi from "http/card/activity/GiftCardActivityApi"; //query
import ActivityBody from "model/common/ActivityBody";
import MyQueryCmp from "cmp/querycondition/MyQueryCmp";
import ActivityStateTag from 'cmp/activity-state-tag/ActivityStateTag';

class Query {
  nameLike: string = ""; //活动名称
  numberEquals: string = ""; //活动号
  topicNameLikes: string = ""; //所属主题
  stateEquals: Nullable<string> = ""; //状态
  typeEquals: Nullable<string> = null; //活动类型
  outerNumberIdLike: Nullable<string> = ""; //外部活动号
}
class Total {
  all: number = 0;
  initial: number = 0;
  audit: number = 0;
  doing: number = 0;
  end: number = 0;
  suspend: number = 0;
}
class UnionActivityItem {
  label: string = ""; //标签值
  value: string = ""; //活动筛选值
  activityType: string = ""; //活动类型
  viewAble: boolean = false; //查看权限
  modifyAble: boolean = false; //修改权限
  auditAble: boolean = false; //审核权限
  stopAble: boolean = false; //终止权限
  removeAble: boolean = false; //删除权限
  goToDtl: Function = () => { }; //前往详细页
  goToCopy: Function = () => { }; //前往复制
  goToModify: Function = () => { }; //前往修改
}
@Component({
  name: "ElasticLayerPage",
  components: {
    FormItem,
    ListWrapper,
    MyQueryCmp,
    ActivityStateTag
  },
})
@I18nPage({
  prefix: ["/公用/券模板",'/页面/页面管理'],
  auto: true,
})
export default class ElasticLayer extends Vue {
  @Prop({ type: String }) activityType: string;
  $refs: any
  ContentTemplate: ContentTemplate[] = []; // 活动模型
  UnionActivityQuery: UnionActivityQuery = new UnionActivityQuery();
  // INITAIL——未审核；UNSTART——未开始；PROCESSING——进行中；STOPED——已结束

  dialogShow: boolean = false;
  titleString: string = "";

  query: Query = {
    nameLike: "", //活动名称
    outerNumberIdLike: "", //外部活动号
    numberEquals: "", //活动号
    topicNameLikes: "", //所属主题
    stateEquals: "PROCESSING", //活动状态
    typeEquals: null, //活动类型
  };
  total: Total = {
    all: 0,
    initial: 0,
    audit: 0,
    doing: 0,
    end: 0,
    suspend: 0,
  };
  selectDate: any = []; //活动时间
  tableData: ActivityBody[] = [];
  activitiesInfo: UnionActivityItem[] = [];
  activityList: any = []; //最终确定选中数据
  page = {
    currentPage: 1,
    total: 0,
    size: 10,
  };
  get panelArray() {
    return [
      {
        name: this.i18n("页面管理"),
        url: "",
      },
    ];
  }
  get getRowKey() {
    if ([this.i18n('电子卡发售活动')].indexOf(this.activityType) > -1) {
      return 'body.activityId'
    } else {
      return 'activityId'
    }
  }
  get ContentTemplateState() {
    let res = [
      {
        value: "UNSTART",
        label: this.i18n("未开始"),
      },
      {
        value: "PROCESSING",
        label: this.i18n("进行中"),
      },
    ]
    if (this.activityType === this.i18n('电子卡发售活动')) {
      res = res.filter((item) => item.value === 'PROCESSING')
    }
    return res
  }
  doCreate() {
    this.$router.push({
      name: "page-manage-edit",
      query: {
        editModel: EditMode.create,
      },
    });
  }
  open(ids: string[]) {
    this.dialogShow = true
    this.titleString = this.activityType ===this.i18n('卡活动') ? this.i18n('电子卡售卡活动') : this.activityType
    this.activityList = ids || []
    this.query = {
      nameLike: "", //活动名称
      outerNumberIdLike: "", //外部活动号
      numberEquals: "", //活动号
      topicNameLikes: "", //所属主题
      stateEquals: "PROCESSING", //活动状态
      typeEquals: null, //活动类型
    }
    this.page.currentPage = 1
    this.getList()
  }
  canel() {
    this.dialogShow = false;
  }
  confirm() {
    this.dialogShow = false;
    this.$emit('submit', this.activityList)
  }
  // 小程序领券/小程序领微信券/积分兑换券
  private getActivitytList(id: any, isUpDataTable: boolean = false) {
    const loading = CommonUtil.Loading()
    const params = new UnionActivityQuery()
    params.topicNameLikes = this.query.topicNameLikes ? this.query.topicNameLikes : null;
    params.nameLike = this.query.nameLike ? this.query.nameLike : null
    params.numberLike = this.query.numberEquals ? this.query.numberEquals : null
    params.outerNumberIdLike = this.query.outerNumberIdLike ? this.query.outerNumberIdLike : null
    // 活动时间
    params.begin = this.selectDate?.length > 0 ? this.selectDate[0] : null
    params.end = this.selectDate?.length > 0 ? this.selectDate[1] : null
    params.page = this.page.currentPage - 1;
    params.pageSize = this.page.size;
    params.stateEquals = this.query.stateEquals ? this.query.stateEquals : null
    let arr: any = []
    arr.push(id)
    params.typeIn = arr
    UnionActivityApi.queryByType(params).then((res) => {
      if (res.code === 2000) {
        this.tableData = res.data?.list || [];
        this.page.total = res.total;
        this.tableData.forEach(item => {
          this.activityList.some((ele: any) => {
            if (item.activityId == ele) {
              this.$refs.table.toggleRowSelection(item, true);
              return true
            }
          })
        })
      } else {
        throw new Error(res.msg || this.i18n('查询失败'))
      }
    }).catch((error) => {
      this.$message.error(error.message)
    }).finally(() => {
      loading.close()
    })
  }
  // 获取当前列表页活动类型
  getActivityTypes() {
    let arr: string[] = [];
    this.activitiesInfo.forEach((item) => {
      if (item.viewAble) {
        arr.push(item.value);
      }
    });
    if (this.query.typeEquals) {
      arr = arr.filter((item) => item === this.query.typeEquals);
    }
    return arr;
  }
  // 电子卡活动列表  /v1/giftcard-activity/query
  private getelectronicCardList(isUpDataTable: boolean = false): void {
    const loading = CommonUtil.Loading();
    const params = new GiftCardActivityFilter();
    params.topicNameLikes = this.query.topicNameLikes ? this.query.topicNameLikes : null;
    params.nameLikes = this.query.nameLike ? this.query.nameLike : null;
    params.activityIdLikes = this.query.numberEquals ? this.query.numberEquals : null;
    // 活动时间
    params.beginDateGreaterOrEquals = this.selectDate?.length > 0 ? this.selectDate[0] : null
    params.endDateLess = this.selectDate?.length > 0 ? this.selectDate[1] : null
    params.page = this.page.currentPage - 1;
    params.pageSize = this.page.size;
    params.stateEquals = this.query.stateEquals ? this.query.stateEquals : null;
    let arr: any = [];
    arr.push(this.activityType);
    this.tableData = [];
    GiftCardActivityApi.query(params)
      .then((res) => {
        if (res.code === 2000) {
          this.tableData = res.data?.result?.map((item)=>item.body) as any || [];
          this.page.total = res.data?.total || 0;
          this.tableData.forEach((item: any) => {
            this.activityList.some((ele: any) => {
              if (item.activityId == ele) {
                this.$refs.table.toggleRowSelection(item, true);
                return true
              }
            })
          })
        } else {
          throw new Error(res.msg || this.i18n("查询失败"));
        }
      })
      .catch((error) => {
        this.$message.error(error.message);
      })
      .finally(() => {
        loading.close();
      });


  }
  //集点 /v1/web/activity/query
  private getcollectionPointList(isUpDataTable: boolean = false): void {
    const loading = CommonUtil.Loading();
    const params: CouponActivityFilter = new CouponActivityFilter();
    params.topicNameLikes = this.query.topicNameLikes ? this.query.topicNameLikes : null;
    params.nameLike = this.query.nameLike ? this.query.nameLike : null;
    params.numberLike = this.query.numberEquals ? this.query.numberEquals : null;
    // 活动时间
    params.begin = this.selectDate?.length > 0 ? this.selectDate[0] : null
    params.end = this.selectDate?.length > 0 ? this.selectDate[1] : null
    params.page = this.page.currentPage - 1;
    params.pageSize = this.page.size;
    params.stateEquals = this.query.stateEquals ? this.query.stateEquals : null;
    params.groupType = 'CONSUME_GIFT' as any
    params.typeEquals = 'COLLECT_POINTS_ACTIVITY'
    this.tableData = [];
    CouponActivityApi.query(params)
      .then((res) => {
        if (res.code === 2000) {
          this.tableData = res.data?.list || [];
          this.page.total = res.total;
          this.total.all = res.data?.summary?.sum || 0;
          this.total.initial = res.data?.summary?.initail || 0;
          this.total.audit = res.data?.summary?.unstart || 0;
          this.total.doing = res.data?.summary?.processing || 0;
          this.total.end = res.data?.summary?.stoped || 0;
          this.tableData.forEach(item => {
            this.activityList.some((ele: any) => {
              if (item.activityId == ele) {
                this.$refs.table.toggleRowSelection(item, true);
                return true
              }
            })
          })
        } else {
          throw new Error(res.msg || this.i18n("查询失败"));
        }
      })
      .catch((error) => {
        this.$message.error(error.message);
      })
      .finally(() => {
        loading.close();
      });
  }
  //   大转盘活动列表 /v1/web/activity/queryByType
  private getrouletteList(isUpDataTable: boolean = false): void {
    const loading = CommonUtil.Loading();
    const params = new UnionActivityQuery();
    params.topicNameLikes = this.query.topicNameLikes ? this.query.topicNameLikes : null;
    params.nameLike = this.query.nameLike ? this.query.nameLike : null;
    params.numberLike = this.query.numberEquals ? this.query.numberEquals : null;
    // 活动时间
    params.begin = this.selectDate?.length > 0 ? this.selectDate[0] : null
    params.end = this.selectDate?.length > 0 ? this.selectDate[1] : null
    params.page = this.page.currentPage - 1;
    params.pageSize = this.page.size;
    params.stateEquals = this.query.stateEquals ? this.query.stateEquals : null;
    let arr: any = [];
    arr.push('BigWheelActivityRule');
    params.typeIn = arr;
    this.tableData = [];
    UnionActivityApi.queryByType(params)
      .then((res) => {
        if (res.code === 2000) {
          this.tableData = res.data?.list || [];
          this.page.total = res.total;
          this.total.all = res.data?.summary?.sum || 0;
          this.total.initial = res.data?.summary?.initail || 0;
          this.total.audit = res.data?.summary?.unstart || 0;
          this.total.doing = res.data?.summary?.processing || 0;
          this.total.end = res.data?.summary?.stoped || 0;
          this.tableData.forEach(item => {
            this.activityList.some((ele: any) => {
              if (item.activityId == ele) {
                this.$refs.table.toggleRowSelection(item, true);
                return true
              }
            })
          })
        } else {
          throw new Error(res.msg || this.i18n("查询失败"));
        }
      })
      .catch((error) => {
        this.$message.error(error.message);
      })
      .finally(() => {
        loading.close();
      });
  }
  // 拼团抽奖列表 /v1/web/activity/queryByType
  private getGroupBookingList(isUpDataTable: boolean = false) {
    const loading = CommonUtil.Loading();
    const params = new UnionActivityQuery();
    params.topicNameLikes = this.query.topicNameLikes ? this.query.topicNameLikes : null;
    params.nameLike = this.query.nameLike ? this.query.nameLike : null;
    params.numberLike = this.query.numberEquals ? this.query.numberEquals : null;
    // 活动时间
    params.begin = this.selectDate?.length > 0 ? this.selectDate[0] : null
    params.end = this.selectDate?.length > 0 ? this.selectDate[1] : null
    params.page = this.page.currentPage - 1;
    params.pageSize = this.page.size;
    params.stateEquals = this.query.stateEquals ? this.query.stateEquals : null;
    let arr: any = [];
    arr.push('GroupBookingActivityRule');
    params.typeIn = arr;
    this.tableData = [];
    UnionActivityApi.queryByType(params)
      .then((res) => {
        if (res.code === 2000) {
          this.tableData = res.data?.list || [];
          this.page.total = res.total;
          this.total.all = res.data?.summary?.sum || 0;
          this.total.initial = res.data?.summary?.initail || 0;
          this.total.audit = res.data?.summary?.unstart || 0;
          this.total.doing = res.data?.summary?.processing || 0;
          this.total.end = res.data?.summary?.stoped || 0;
          this.tableData.forEach(item => {
            this.activityList.some((ele: any) => {
              if (item.activityId == ele) {
                this.$refs.table.toggleRowSelection(item, true);
                return true
              }
            })
          })
        } else {
          throw new Error(res.msg || this.i18n("查询失败"));
        }
      })
      .catch((error) => {
        this.$message.error(error.message);
      })
      .finally(() => {
        loading.close();
      });
  }
  //   抽锦鲤活动列表 /v1/web/activity/queryByType
  private getLuckyDrawList(isUpDataTable: boolean = false): void {
    const loading = CommonUtil.Loading();
    const params = new UnionActivityQuery();
    params.topicNameLikes = this.query.topicNameLikes ? this.query.topicNameLikes : null;
    params.nameLike = this.query.nameLike ? this.query.nameLike : null;
    params.numberLike = this.query.numberEquals ? this.query.numberEquals : null;
    // 活动时间
    params.begin = this.selectDate?.length > 0 ? this.selectDate[0] : null
    params.end = this.selectDate?.length > 0 ? this.selectDate[1] : null
    params.page = this.page.currentPage - 1;
    params.pageSize = this.page.size;
    params.stateEquals = this.query.stateEquals ? this.query.stateEquals : null;
    let arr: any = [];
    arr.push('LuckyDrawActivityRule');
    params.typeIn = arr;
    this.tableData = [];
    UnionActivityApi.queryByType(params)
      .then((res) => {
        if (res.code === 2000) {
          this.tableData = res.data?.list || [];
          this.page.total = res.total;
          this.total.all = res.data?.summary?.sum || 0;
          this.total.initial = res.data?.summary?.initail || 0;
          this.total.audit = res.data?.summary?.unstart || 0;
          this.total.doing = res.data?.summary?.processing || 0;
          this.total.end = res.data?.summary?.stoped || 0;
          this.tableData.forEach(item => {
            this.activityList.some((ele: any) => {
              if (item.activityId == ele) {
                this.$refs.table.toggleRowSelection(item, true);
                return true
              }
            })
          })
        } else {
          throw new Error(res.msg || this.i18n("查询失败"));
        }
      })
      .catch((error) => {
        this.$message.error(error.message);
      })
      .finally(() => {
        loading.close();
      });
  }
  // 查询活动列表
  private getList(isUpDataTable: boolean = false) {
    switch (this.activityType) {
      case this.i18n("卡活动"): //电子卡 活动列表
        this.getelectronicCardList(isUpDataTable);
        break;
      case this.i18n("集点活动"): // 集点活动列表
        this.getcollectionPointList(isUpDataTable);
        break;
      case this.i18n("大转盘"): // 大转盘活动列表
        this.getrouletteList(isUpDataTable);
        break;
      case this.i18n("拼团抽奖"): // 拼团抽奖活动列表
        this.getGroupBookingList(isUpDataTable);
        break;
      case this.i18n("抽奖团"): // 拼团抽奖活动列表
        this.getActivitytList('GroupBookingActivityRule', isUpDataTable);
        break;
      case this.i18n("邀请有礼"): // 拼团抽奖活动列表
        this.getActivitytList('MemberInviteRegisterGiftActivityRule', isUpDataTable);
        break;
      case this.i18n("小程序领券"): // 小程序领券活动列表
        this.getActivitytList('MiniProgramGainCouponActivityRule', isUpDataTable);
        break;
      case this.i18n("小程序领微信券"): // 小程序领微信券活动列表
        this.getActivitytList('WeiXinAppletIssueCouponActivityRule', isUpDataTable);
        break;
      case this.i18n("积分兑换券"): // 积分兑换券活动列表
        this.getActivitytList('PointsExchangeCouponActivityRule', isUpDataTable);
        break;
      case this.i18n("抽锦鲤"): // 抽锦鲤活动列表
        this.getLuckyDrawList(isUpDataTable);
        break;
    }
  }
  handleSelectionChange(val: any[], row: any) {
    const flag = val.some((item) => this.checkBodyToActivityId(item) === this.checkBodyToActivityId(row))
    const index = this.activityList.findIndex((item: any) =>
      item == this.checkBodyToActivityId(row)
    )
    if (!flag) {
      this.activityList.splice(index, 1)
    } else {
      this.activityList.push(this.checkBodyToActivityId(row))
    }
  }
  // 获取不同结构下的activityId
  checkBodyToActivityId(obj: any) {
    if (obj.body) {
      return obj.body.activityId
    } else {
      return obj.activityId
    }
  }
  handleSelectAll(selectArr: any[]) {
    const currentPageData = selectArr.filter((item) => {
      return this.tableData.find((val) => this.checkBodyToActivityId(val) === this.checkBodyToActivityId(item))
    })
    if (currentPageData.length) {
      currentPageData.forEach((item) => {
        this.activityList.push(this.checkBodyToActivityId(item))
      })
    } else {
      this.tableData.forEach((item) => {
        const id = this.checkBodyToActivityId(item)
        const targetIndex = this.activityList.indexOf(id)
        if (targetIndex > -1) {
          this.activityList.splice(targetIndex, 1)
        }
      })
    }
    this.activityList = [...new Set(this.activityList)]
  }
  /**
   * 查询
   */
  doSearch() {
    this.page.currentPage = 1;
    this.getList(true);
  }

  /**
   * 重置
   */
  doReset() {
    this.page.currentPage = 1;
    this.query = {
      nameLike: "", //活动名称
      numberEquals: "", //活动号
      topicNameLikes: "", //所属主题
      stateEquals: "PROCESSING", //活动状态
      typeEquals: null, //活动类型
    } as any;
    this.getList(true);
  }
  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.currentPage = 1
    this.page.size = val;
    this.getList(true);
  }
  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val;
    this.getList();
  }
}
