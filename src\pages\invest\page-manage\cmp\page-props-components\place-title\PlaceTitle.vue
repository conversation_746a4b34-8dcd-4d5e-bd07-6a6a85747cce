<template>
  <div>
    <el-form
      inline
      v-if="formMode === FormModeType.form"
      :label-position="labelPosition"
      :model="value"
      :rules="rules"
      ref="form"
    >
      <el-form-item :label="label" prop="placeTitle" label-width="130px" style="width: 100%;">
        <el-input
          v-model="value.placeTitle"
          :placeholder="i18n('请输入资源位标题')"
          @change="handleChange"
          style="width:100%"
          :maxlength="50"
        >
        </el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" src="./PlaceTitle.ts"></script>

<style lang="scss" scoped>
</style>
