import { Component, Prop, Vue, Emit, Watch } from 'vue-property-decorator'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import ListWrapper from 'cmp/list/ListWrapper'
import I18nPage from 'common/I18nDecorator'
import EquityCardApi from 'http/benefit/EquityCardApi'
import EquityCardQueryRequest from 'model/equityCard/EquityCardQueryRequest'
import EquityCard from 'model/equityCard/EquityCard'
import RSOrgFilter from 'model/common/RSOrgFilter'
import OrgApi from 'http/org/OrgApi'
import RSOrg from 'model/common/RSOrg'
import EquityCardCancelRequest from 'model/equityCard/EquityCardCancelRequest'
import {
  EquityCardState,
  EquityCardStateMap
} from 'model/equityCard/EquityCardState'
import EquityCardPermission from './EquityCardPermission'
import { State } from 'vuex-class'
import UserLoginResult from 'model/login/UserLoginResult'
import CommonUtil from 'util/CommonUtil'
import DownloadCenterDialog from 'pages/main/dowmload_center_dialog/DownloadCenterDialog'
import SelectStores from 'cmp/selectStores/SelectStores'

@Component({
  name: 'EquityCardListSearchList',
  components: { BreadCrume, ListWrapper, DownloadCenterDialog, SelectStores }
})
@I18nPage({
  prefix: [
    '/储值/会员储值/会员储值报表/调整流水',
    '/营销/券礼包活动/券查询',
    '/会员/会员资料',
    '/储值/预付卡/预付卡查询/列表页面',
    '/公用/菜单',
    '/公用/券模板',
    '/营销/券礼包活动/券查询/表格',
    '/储值/会员储值/会员储值报表/充值流水',
    '/公用/按钮',
    '/会员/权益卡管理',
    '/营销/券礼包活动/券查询/批量作废'
  ]
})
export default class EquityCardListSearchList extends Vue {
  @State('loginInfo') loginInfo: UserLoginResult

  $refs: any
  i18n: (str: string, params?: string[]) => string
  panelArray = [
    {
      name: '权益卡查询',
      url: ''
    }
  ]
  tableLoading: boolean = false
  tableData: EquityCard[] = []
  selectDate: string[] = [] // 活动时间
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  selected: EquityCard[] = []
  query: EquityCardQueryRequest = new EquityCardQueryRequest()
  EquityCardStateMap = EquityCardStateMap
  EquityCardState = EquityCardState
  permission = new EquityCardPermission()
  fileDialogVisible = false
  showTip = false

  created() {
    this.getList()
  }

  // 查询
  doSearch() {
    this.$refs.table.clearSelection()
    this.page.currentPage = 1
    this.getList()
  }

  // 重置
  doReset() {
    this.$refs.table.clearSelection()
    this.query = new EquityCardQueryRequest()
    this.selectDate = []
    this.doSearch()
  }

  // 请求获取表格数据
  async getList() {
    try {
      this.setParam()
      this.query.page = this.page.currentPage - 1
      this.query.pageSize = this.page.size
      this.tableLoading = true
      const { code, data, total, msg } = await EquityCardApi.query(this.query)
      this.tableLoading = false
      if (code === 2000) {
        this.tableData = data ?? []
        this.page.total = total || 0
      } else {
        this.tableData = []
        this.page.total = 0
        this.$message.error(msg ?? '接口异常')
      }
    } catch (error) {
      this.tableLoading = false
      this.$message.error((error as Error).message)
    }
  }

  // 处理列表查询入参
  setParam() {
    this.query.memberCodeLikes = this.query.memberCodeLikes || null
    this.query.nameLikes = this.query.nameLikes || null
    this.query.codeEquals = this.query.codeEquals || null
    this.query.issueTimeBegin = this.selectDate[0] || null
    this.query.issueTimeEnd = this.selectDate[1] || null
  }

  // 表格选中
  handleSelectionChange(val: EquityCard[]) {
    this.selected = val
  }

  // 批量作废
  doBatchVoid() {
    if (this.selected.length <= 0) {
      this.$message.warning(this.i18n('请先勾选要作废的权益卡'))
      return
    }
    this.$confirm(
      this.i18n(
        '作废后，会员将不能享受该权益卡的权益，但不影响已获取的积分和券使用'
      ),
      this.i18n('批量作废'),
      {
        confirmButtonText: this.i18n('确定'),
        cancelButtonText: this.i18n('取消')
      }
    ).then(() => {
      const cardNos = this.selected.map((item) => {
        return item.code!
      })
      this.doCancel(cardNos, true)
    })
  }

  // 作废
  doVoid(row: EquityCard) {
    this.$confirm(
      this.i18n(
        '作废后，会员将不能享受该权益卡的权益，但不影响已获取的积分和券使用'
      ),
      this.i18n('作废'),
      {
        confirmButtonText: this.i18n('确定'),
        cancelButtonText: this.i18n('取消')
      }
    ).then(() => {
      this.doCancel([row.code!], false)
    })
  }

  // 请求作废操作
  async doCancel(cardNos: string[], isBatch: boolean) {
    try {
      const params = new EquityCardCancelRequest()
      params.operator = this.loginInfo.user?.account
      params.cardNos = cardNos
      const { code, data, msg } = await EquityCardApi.cancel(params)
      if (code === 2000) {
        let message = this.i18n('作废成功')
        if (isBatch) {
          message = this.i18n('作废成功{0}条，失败{1}条')
            .replace(/\{0\}/g, data?.success as any)
            .replace(/\{1\}/g, data?.fail as any)
        }
        this.$message.success(message)
        this.doSearch()
      } else {
        this.$message.error(msg ?? '接口异常')
      }
    } catch (error) {
      this.$message.error((error as Error).message)
    }
  }

  // 分页器当前页改变
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.getList()
  }

  // 分页器每页显示个数改变
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.getList()
  }

  getActiveCount(count: number) {
    return this.i18n('已选择{0}张卡').replace(/\{0\}/g, count.toString())
  }

  // 批量导出
  async exportFile() {
    const loading = CommonUtil.Loading()
    try {
      this.setParam()
      const { code, msg } = await EquityCardApi.export(this.query)
      loading.close()
      if (code === 2000) {
        this.showTip = true
        this.fileDialogVisible = true
      } else {
        this.$message.error(msg as string)
      }
    } catch (error) {
      loading.close()
      this.$message.error((error as Error).message)
    }
  }

  // 关闭文件中心弹框
  doDownloadDialogClose() {
    this.showTip = false
    this.fileDialogVisible = false
  }
}
