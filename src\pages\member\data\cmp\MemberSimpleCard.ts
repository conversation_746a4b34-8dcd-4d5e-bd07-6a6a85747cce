import { Component, Prop, Vue, Watch } from "vue-property-decorator";

@Component({
  name: "MemberSimpleCard",
  components: {},
})
export default class MemberSimpleCard extends Vue {
  @Prop()
  card: any;

  // 是否是付费会员卡，样式不一样
  @Prop({ default: true })
  benefit: boolean;

  hasEllipsis: boolean = false;

  @Watch("card", { immediate: true, deep: true })
  contentChange() {
    this.$nextTick(() => {
      // @ts-ignore
      const el = this.$refs.content.$el;
      // @ts-ignore
      this.hasEllipsis = el.offsetHeight < el.scrollHeight;
    });
  }
}
