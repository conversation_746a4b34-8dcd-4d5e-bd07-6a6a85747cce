export default class CouponWriteoffFilter {
  // [活动号：number:=]
  // @ApiModelProperty(value = "单号=")
  numberEquals: Nullable<string> = null;
  // [活动号：number:%=%]
  // @ApiModelProperty(value = "单号类似")
  numberLike: Nullable<string> = null;
  // @ApiModelProperty(value = "券码类似")
  couponCodeLike: Nullable<string> = null;
  // 券码等于
  couponCodeEquals: Nullable<string> = null;
  // @ApiModelProperty(value = "活动制定所属营销中心")
  marketingCenterEquals: Nullable<string> = null;
  // [活动开始日期小于<]
  // @ApiModelProperty(value = "活动开始日期小于")
  createLess: Nullable<string> = null;
  // [活动开始日期 :>=]
  // @ApiModelProperty(value = "活动开始日期")
  createGreaterOrEquals: Nullable<string> = null;

  // [活动开始日期小于<]
  // @ApiModelProperty(value = "活动开始日期小于")
  lastModifiedLess: Nullable<string> = null;
  // [活动开始日期 :>=]
  // @ApiModelProperty(value = "活动开始日期")
  lastModifiedGreaterOrEquals: Nullable<string> = null;
  // @ApiModelProperty(value = "状态")
  stateEquals: Nullable<"INITIAL" | "EFFECTED"> = null;

  // @ApiModelProperty(value = "page")
  page: Nullable<number> = null;
  // 页面大小>0
  // @ApiModelProperty(value = "pageSize")
  pageSize: Nullable<number> = null;
  // @ApiModelProperty(value = "order")
  order: Nullable<Object> = null;
}
