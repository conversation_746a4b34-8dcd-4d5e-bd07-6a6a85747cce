import {ChannelState} from 'model/channel/ChannelState'

export default class ChannelManagementFilter {
    //
    idNameLikes: Nullable<string> = null
    //
    channelIdEquals: Nullable<string> = null
    //
    channelTypeEquals: Nullable<string> = null
    //
    nameEquals: Nullable<string> = null
    //
    stateEquals: Nullable<ChannelState> = null
    //
    sorters: any
    //
    page: Nullable<number> = null
    //
    pageSize: Nullable<number> = null
}