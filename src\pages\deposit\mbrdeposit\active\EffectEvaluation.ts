import {Component, Vue} from 'vue-property-decorator'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import FormItem from 'cmp/formitem/FormItem.vue'
import DepositActivityApi from 'http/deposit/activity/DepositActivityApi'
import DepositEvaluation from 'model/deposit/activity/DepositEvaluation'
import DateUtil from 'util/DateUtil'
import I18nPage from "common/I18nDecorator";

@Component({
  name: 'EffectEvaluation',
  components: {
    SubHeader,
    FormItem
  }
})
@I18nPage({
  prefix: ['/储值/会员储值/储值充值活动/效果评估', '/公用/按钮']
})
export default class EffectEvaluation extends Vue {
  row: any = ''
    evaluate: DepositEvaluation = new DepositEvaluation()
    endDate: any = ''
    get getDay() {
        if (this.row && this.row.body && this.row.body.beginDate && this.row.body.endDate) {
            let last = DateUtil.parseDate(DateUtil.format(this.endDate, 'yyyy-MM-dd')).getTime()
            let pre = DateUtil.parseDate(this.row.body.beginDate.substring(0, 10)).getTime()
            let day = Math.abs(last - pre) / (24 * 60 * 60 * 1000)
            return Math.ceil(day) + 1
        }
        return  0
    }
    get getEndDate() {
        let today = new Date().getTime()
        let endDate = DateUtil.parseDate(this.row.body.endDate).getTime()
        if (today - endDate > 0) {
            this.endDate = endDate
            return endDate
        } else {
            this.endDate = today
            return today
        }
    }

    created() {
        this.getEvalate()
    }
    mounted() {
        this.row = this.$route.query.row
    }

    /**
     * 分页页码改变的回调
     * @param val
     */
    doBack() {
        this.$router.back()
    }
    doExport() {

    }
    private getEvalate() {
        DepositActivityApi.evaluate(this.$route.query.id as string).then((resp: any) => {
            if (resp && resp.code === 2000) {
                this.evaluate = resp.data
            }
        }).catch((error: any) => {
          if (error && error.message) {
            this.$message.error(error.message)
          }
        })
    }
}
