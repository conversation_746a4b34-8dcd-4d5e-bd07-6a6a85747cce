/*
 * @Author: 黎钰龙
 * @Date: 2024-01-02 10:24:40
 * @LastEditTime: 2024-01-02 16:24:34
 * @LastEditors: 黎钰龙
 * @Description:
 * @FilePath: \phoenix-web-ui\src\http\promotion\EmailMessageNotifyApi.ts
 * 记得注释
 */
import ApiClient from "http/ApiClient";
import Response from "model/common/Response";
import BEmailMessageTemplate from "model/promotion/BEmailMessageTemplate";
import BEmailMessageTemplateSaveRequest from "model/promotion/BEmailMessageTemplateSaveRequest";

export default class EmailMessageNotifyApi {
  /**
   * 获取邮箱消息列表页
   * 获取邮箱消息列表页。
   *
   */
  static gets(): Promise<Response<BEmailMessageTemplate[]>> {
    return ApiClient.server()
      .post(`/v1/email-message-template/gets`, {}, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 保存(传uuid就是修改)
   * 保存(传uuid就是修改)。
   *
   */
  static saveNew(body: BEmailMessageTemplateSaveRequest): Promise<Response<void>> {
    return ApiClient.server()
      .post(`/v1/email-message-template/saveNew`, body, {})
      .then((res) => {
        return res.data;
      });
  }
}
