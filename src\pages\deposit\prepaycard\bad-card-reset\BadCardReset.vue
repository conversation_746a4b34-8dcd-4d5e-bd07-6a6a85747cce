<template>
  <div class="page-container">
    <BreadCrume :panelArray="panelArray">
    </BreadCrume>
    <div class="top-header">
      <div class="header-left">
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-form-item :label="i18n('/储值/预付卡/预付卡查询/列表页面/卡号')" prop="card">
            <el-select v-model="form.card" value-key="code" remote filterable :loading="selectLoading" :disabled="state === 'RESETTING'"
              :remote-method="doRemoteQuery" style="width:257px" :placeholder="i18n('/储值/预付卡/电子礼品卡报表/退款流水/类似于')">
              <el-option v-for="(item,index) in cardList" :key="index" :label="item.code" :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="i18n('/卡/卡管理/卡介质/卡介质')">
            <template v-if="form.card && form.card.cardMedium">
              <div>{{form.card.cardMedium | cardMediumStr}}</div>
            </template>
            <div v-else>--</div>
          </el-form-item>
        </el-form>
        <div class="btn-block">
          <el-button type="primary" v-if="hasOptionPermission('/卡/卡管理/坏卡重制', '单据维护')" :disabled="state === 'RESETTING'" class="start-btn"
            @click="startReset">
            {{i18n('确认重制')}}
          </el-button>
        </div>
      </div>
      <div class="header-right">
        <div class="empty-info" v-if="state === 'UNSTART'">
          <!-- 初始化状态 -->
          <img src="@/assets/image/icons/cutting_pic_empty.png" class="empty-img" />
          <span class="gray-tips">{{i18n('请在左侧输入信息，点击确认重制')}}</span>
        </div>
        <div class="info-header" style="background: #EEF8EA" v-else-if="state === 'SUCCESS'">
          <!-- 重制成功 -->
          <img src="@/assets/image/icons/ic_success_large.png" class="info-icon">
          <div class="info-text" style="color: #08A440">{{i18n('重制成功!')}}</div>
        </div>
        <div class="info-header" v-else>
          <!-- 重制失败/重制中 -->
          <img src="@/assets/image/icons/ic_infofill.png" class="info-icon">
          <div class="info-text">{{topicText}}</div>
        </div>
        <div class="info-form" v-if="state === 'SUCCESS'">
          <div class="form-child">
            <div class="child-label">{{i18n('当前重制的卡号')}}:</div>
            <div class="child-text">{{successCode || '--'}}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="setting-container" style="margin-top: 20px">
      <div class="section-title">{{i18n('卡重制操作日志')}}</div>
      <MyQueryCmp @reset="doReset" @search="doSearch" :showExpand="false" style="margin-bottom: 12px">
        <el-row>
          <el-col :span="8">
            <form-item :label="i18n('/储值/预付卡/预付卡调整单/编辑页面/卡号')">
              <el-input v-model="cardCodeLikes" :placeholder="i18n('/储值/预付卡/电子礼品卡报表/消费流水/类似于')"></el-input>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="i18n('/储值/会员储值/储值调整单/详情/操作时间')">
              <el-date-picker style="width: 80%" v-model="operateDateRange" type="daterange" range-separator="-"
                :start-placeholder="i18n('/会员/会员资料/开始日期')" :end-placeholder="i18n('/会员/会员资料/结束日期')">
              </el-date-picker>
            </form-item>
          </el-col>
        </el-row>
      </MyQueryCmp>
      <el-table :data="logList" v-loading="loading">
        <el-table-column :label="i18n('/储值/预付卡/预付卡调整单/列表页面/卡号')" width="214px">
          <template slot-scope="scope">
            {{scope.row.cardCode || '--'}}
          </template>
        </el-table-column>
        <el-table-column :label="i18n('/卡/写卡/操作内容')" width="214px">
          <template slot-scope="scope">
            {{scope.row.type || '--'}}
          </template>
        </el-table-column>
        <el-table-column :label="i18n('/会员/会员资料/详情界面/会员操作日志/操作人')">
          <template slot-scope="scope">
            {{scope.row.operator || '--'}}
          </template>
        </el-table-column>
        <el-table-column :label="i18n('/公用/券核销/操作时间')">
          <template slot-scope="scope">
            {{scope.row.created | dateFormate3}}
          </template>
        </el-table-column>
      </el-table>
      <el-pagination :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
        @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background layout="total, prev, pager, next, sizes,  jumper">
      </el-pagination>
    </div>
  </div>
</template>

<script lang="ts" src="./BadCardReset.ts">
</script>

<style lang="scss" scoped>
.page-container {
  width: 100%;
  .top-header {
    display: flex;
    justify-content: space-between;
    height: 220px;
    .header-left {
      width: 450px;
      height: 100%;
      margin-right: 20px;
      background-color: #fff;
      border-radius: 8px;
      padding: 24px;
      box-sizing: border-box;
      .btn-block {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: 28px;
      }
      .start-btn {
        width: 263px;
        height: 36px;
      }
    }
    .header-right {
      position: relative;
      flex: 1;
      height: 100%;
      background-color: #fff;
      border-radius: 8px;
      padding: 24px;
      box-sizing: border-box;
      .empty-info {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        .empty-img {
          width: 84px;
          height: 72px;
          margin-bottom: 12px;
        }
      }
      .info-header {
        display: flex;
        padding: 14px;
        width: 100%;
        background: #e6f2ff;
        border-radius: 4px;
        .info-icon {
          width: 24px;
          height: 24px;
          margin-right: 8px;
        }
        .info-text {
          font-family: PingFangSC, PingFang SC;
          font-weight: 600;
          font-size: 20px;
          color: #007eff;
          line-height: 22px;
        }
      }
      .info-form {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
        .form-child {
          min-width: 230px;
          flex-basis: 50%;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 13px;
          .child-label {
            color: #79879e;
          }
          .child-text {
            color: #242633;
            margin-top: 12px;
          }
        }
        .current-code {
          display: flex;
          align-items: baseline;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 20px;
          color: #242633;
          margin-top: 8px;
        }
      }
    }
  }
}
</style>