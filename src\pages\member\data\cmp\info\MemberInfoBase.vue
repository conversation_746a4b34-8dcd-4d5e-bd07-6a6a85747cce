<template>
  <div class="member-card">
    <member-card-title :icon="require('~assets/image/member/<EMAIL>')"
                       :title="i18n('会员基础资料')">
      <el-button type="primary" slot="right" @click="onEdit" v-if="hasOptionPermission(permissionResourceId, '编辑资料')">编辑</el-button>
    </member-card-title>
    <el-row>
      <el-col :span="8">
        <member-form-item :label="i18n('昵称') + '：'">{{ dtl.nickName | strFormat }}</member-form-item>
      </el-col>
      <el-col :span="8">
        <member-form-item :label="i18n('性别') + '：'">{{ dtl.genderI18n ? dtl.genderI18n : formatI18n("/会员/会员资料", "未知") }}</member-form-item>
      </el-col>
      <el-col :span="8">
        <member-form-item :label="i18n('生日') + '：'">{{ dtl.birthday | strFormat }}</member-form-item>
      </el-col>
      <el-col :span="8">
        <member-form-item :label="i18n('年龄') + '：'">{{ dtl.age | strFormat }}</member-form-item>
      </el-col>
      <el-col :span="8">
        <member-form-item :label="i18n('身份证号') + '：'">{{ dtl.idCard | strFormat }}</member-form-item>
      </el-col>
      <el-col :span="8">
        <member-form-item :label="i18n('学历') + '：'">
          <template v-if="hasMemberOption()">
          {{ dtl.educationI18n | strFormat }}
          </template>
          <template v-else-if="dtl.education">
          {{formatI18n("/会员/会员资料", dtl.education)}}
          </template>
          <template v-else>--</template>
        </member-form-item>
      </el-col>
      <el-col :span="8">
        <member-form-item :label="i18n('行业') + '：'">{{ dtl.industryI18n | strFormat }}</member-form-item>
      </el-col>
      <el-col :span="8">
        <member-form-item :label="i18n('年收入') + '：'">{{ dtl.annualIncomeI18n | strFormat }}</member-form-item>
      </el-col>
      <el-col :span="8">
        <member-form-item :label="i18n('爱好') + '：'">{{ dtl.hobbies | strFormat }}</member-form-item>
      </el-col>
      <el-col :span="8">
        <member-form-item :label="i18n('邮箱') + '：'">
          {{ dtl.email | strFormat }}
          <span style="margin-left:10px;font-weight:600"
                v-if="dtl.email && showMobileAndEmailCheckInfo">
                {{ dtl.emailChecked ? i18n("已校验") : i18n("未校验") }}
              </span>
        </member-form-item>
      </el-col>
      <el-col :span="8">
        <member-form-item :label="i18n('地址') + '：'">{{ dtlAddress | strFormat }}</member-form-item>
      </el-col>
      <el-col :span="8" v-if="hasMemberOption()">
        <member-form-item :label="i18n('员工号') + '：'">{{ dtl.employeeID | strFormat }}</member-form-item>
      </el-col>
      <el-col :span="8" v-if="hasMemberOption()">
        <member-form-item :label="i18n('职位') + '：'">{{ dtl.office | strFormat }}</member-form-item>
      </el-col>
      <el-col :span="8" v-if="hasMemberOption()">
        <member-form-item :label="i18n('国籍') + '：'">{{ dtl.nationality | strFormat }}</member-form-item>
      </el-col>
      <el-col :span="8" v-if="hasMemberOption()">
        <member-form-item :label="i18n('宗教') + '：'">{{ dtl.religion | strFormat }}</member-form-item>
      </el-col>
      <el-col :span="8">
        <member-form-item :label="i18n('备用手机号') + '：'">{{ dtl.spareMobile | strFormat }}</member-form-item>
      </el-col>
      <el-col :span="8" v-for="(item,index) in customMemberAttr" :key="index">
        <member-form-item :label="item.label+'：'">
          {{ item.value | strFormat }}
        </member-form-item>
      </el-col>
    </el-row>
  </div>
</template>
<script lang="ts"
        src="./MemberInfoBase.ts">
</script>
<style lang="scss"
       scoped>
</style>
