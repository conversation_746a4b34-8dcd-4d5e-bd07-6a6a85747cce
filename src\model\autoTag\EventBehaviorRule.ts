/*
 * @Author: 黎钰龙
 * @Date: 2024-11-21 19:31:52
 * @LastEditTime: 2024-11-28 10:28:21
 * @LastEditors: 黎钰龙
 * @Description: z
 * @FilePath: \phoenix-web-ui\src\model\autoTag\EventBehaviorRule.ts
 * 记得注释
 */
import { ConnectiveType } from "./ConnectiveType"
import { DoneOrNot } from "./DoneOrNot"
import EventBehaviorCondition from "./EventBehaviorCondition"
import { TimeRangeType } from "./TimeRangeType"

export default class EventBehaviorRule {
  // 事件行为时间类型
  timeRangeType: Nullable<TimeRangeType> = null
  // 事件行为时间范围
  timeRange: Date[] = []
  // 是否做过【做过，没做过】
  doneOrNot: Nullable<DoneOrNot> = DoneOrNot.done
  // 关系连接符：或者/ 且
  connective: Nullable<ConnectiveType> = ConnectiveType.and
  // 事件行为条件
  conditions: EventBehaviorCondition[] = []
  // 事件行为类型【消费，购买商品】
  behaviorType: Nullable<string> = null
  // 事件行为
  behavior: Nullable<string> = null
}