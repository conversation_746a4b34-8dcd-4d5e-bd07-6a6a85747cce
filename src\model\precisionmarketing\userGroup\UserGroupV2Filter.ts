/*
 * @Author: 黎钰龙
 * @Date: 2025-01-06 20:02:16
 * @LastEditTime: 2025-01-23 10:11:45
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\precisionmarketing\userGroup\UserGroupV2Filter.ts
 * 记得注释
 */
export default class UserGroupV2Filter {
  // 分类Id等于
  categoryIdEquals: Nullable<string> = null
  // 来源渠道Id等于['phoenix-CRM','QiWei-企微']
  sourceChannelIdEquals: Nullable<string> = null
  // 客群名称等于
  nameLikes: Nullable<string> = null
  // 页数
  page: number = 0
  // 页面大小
  pageSize: number = 0
  // 抓取字段
  fetchParts: string[] = []
}