/*
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:05
 * @LastEditTime: 2025-02-21 14:37:20
 * @LastEditors: 黎钰龙
 * @Description:
 * @FilePath: \phoenix-web-ui\src\pages\member\data\StandardMemberDtl.ts
 * 记得注释
 */
import { Component, Vue, Watch } from 'vue-property-decorator'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import FormItem from 'cmp/formitem/FormItem.vue'
import EditDataDialog from 'pages/member/data/dialog/EditDataDialog.vue'
import AdjustMemberLevelDialog from 'pages/member/data/dialog/AdjustMemberLevelDialog.vue'
import ResetPasswordDialog from 'pages/member/data/dialog/ResetPasswordDialog.vue'
import MemberApi from 'http/member_standard/MemberApi'
import MemberDetail from 'model/member_v2/member/MemberDetail'
import MemberCustomGroup from 'model/member_v2/member/MemberCustomGroup'
import EditTagInfoDialog from 'pages/member/data/dialog/EditTagInfoDialog.vue'
import CheckCouponDialog from 'pages/member/data/dialog/CheckCouponDialog.vue'
import CheckPrePayCardDialog from 'pages/member/data/dialog/CheckPrePayCardDialog.vue'
import CheckWater from 'pages/member/data/dialog/CheckWater.vue'
import PrePayConfigApi from 'http/prepay/config/PrePayConfigApi'
import UnBindDialog from 'pages/member/data/dialog/UnBindDialog.vue'
import ConstantMgr from 'mgr/ConstantMgr'
import MemberOptLogDrawer from "pages/member/data/drawer/MemberOptLogDrawer";
import MemberAssetsDrawer from "pages/member/data/drawer/MemberAssetsDrawer";
import SingleModifyStoreDialog from 'pages/member/data/cmp/SingleModifyStoreDialog.vue'
import PageConfigApi from 'http/pageConfig/PageConfigApi'
import LuDaoConfig from 'model/common/LuDaoConfig'
import LuDaoFamilyCardApi from 'http/member_standard/LuDaoFamilyCardApi'
import LuDaoFamilyCardFilter from 'model/common/LuDaoFamilyCardFilter'
import DeleteMemberIdentRequest from 'model/member_standard/DeleteMemberIdentRequest'
import UnbindSubMemberRequest from 'model/member_standard/UnbindSubMemberRequest'
import MainAndSubCard from 'model/member_standard/MainAndSubCard'
import UnBindSubDialog from './dialog/UnBindSubDialog'
import EquityCardApi from 'http/benefit/EquityCardApi'
import EquityCardQueryRequest from 'model/equityCard/EquityCardQueryRequest'
import EquityCard from 'model/equityCard/EquityCard'
import DateUtil from 'util/DateUtil'
import EquityCardCancelRequest from 'model/equityCard/EquityCardCancelRequest'
import { State } from 'vuex-class'
import UserLoginResult from 'model/login/UserLoginResult'
import BrowserMgr from 'mgr/BrowserMgr'
import I18nPage from 'common/I18nDecorator'
import SendCouponDialog from './cmp/SendCouponDialog'
import CouponItem from 'model/common/CouponItem'
import ReissueCouponRequest from 'model/member/ReissueCouponRequest'
import CommonUtil from 'util/CommonUtil'
import MemberLeftInfo from "pages/member/data/cmp/MemberLeftInfo";
import MemberNavbar from "pages/member/data/cmp/MemberNavbar";
import MemberCardDetail from "pages/member/data/cmp/MemberCardDetail";
import MemberInfoDetail from "pages/member/data/cmp/MemberInfoDetail";
import MemberTradeDetail from "pages/member/data/cmp/MemberTradeDetail";
import Tools from 'util/Tools'
@Component({
  name: 'StandardMemberDtl',
  components: {
    BreadCrume,
    FormItem,
    EditDataDialog,
    AdjustMemberLevelDialog,
    ResetPasswordDialog,
    EditTagInfoDialog,
    CheckCouponDialog,
    CheckPrePayCardDialog,
    CheckWater,
    UnBindDialog,
    MemberOptLogDrawer,
    MemberAssetsDrawer,
    SingleModifyStoreDialog,
    UnBindSubDialog,
    SendCouponDialog,
    MemberLeftInfo,
    MemberNavbar,
    MemberCardDetail,
    MemberInfoDetail,
    MemberTradeDetail
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/会员/会员资料'
  ],
  auto: true
})
export default class StandardMemberDtl extends Vue {
  get getRegisterChannel() {
    // 如果type存在【third，alipay，weixin，store，phoenix】，根据type去取id
    if (this.dtl.registerChannel && this.dtl.registerChannel.type) {
      if (this.dtl.registerChannel.type === 'third') {
        return this.formatI18n('/会员/会员资料', '第三方')
      } else if (this.dtl.registerChannel.type === 'alipay') {
        return this.formatI18n('/会员/会员资料', '支付宝')
      } else if (this.dtl.registerChannel.type === 'weixin') {
        return this.formatI18n('/会员/会员资料', '微信')
      } else if (this.dtl.registerChannel.type === 'store') {
        return this.formatI18n('/会员/会员资料', '门店注册')
      } else if (this.dtl.registerChannel.type === 'phoenix') {
        return 'CRM'
      } else if (this.dtl.registerChannel.type === 'weixinApp') {
        return this.formatI18n('/会员/会员资料', '海鼎会员小程序')
      } else {
        if (this.dtl.registerChannel && this.dtl.registerChannel.id) {
          return this.dtl.registerChannel.id
        } else {
          return '--'
        }
      }
    } else {
      return '--'
    }
    // 如果type不存在，但是id存在，直接显示id
  }
  get getInvited() {
    if (this.dtl.referee && this.dtl.referee.mobile) {
      return this.dtl.referee.mobile
    } else if (this.dtl.referee && this.dtl.referee.crmCode) {
      return this.dtl.referee.crmCode
    } else {
      return ''
    }
  }
  get getDay() {
    if (this.dtl) {
      if (this.dtl.lastConsumeDay === 0) {
        return this.formatI18n('/会员/会员资料', '今天')
      } else if (this.dtl.lastConsumeDay === 1) {
        return this.formatI18n('/会员/会员资料', '昨天')
      } else {
        let str: any = this.formatI18n('/会员/会员资料', '{0}天前')
        str = str.replace(/\{0\}/g, this.dtl.lastConsumeDay)
        return str
      }
    } else {
      return ''
    }
  }
  get getFirstNameAndLastName() {
    let str = ''
    if (!this.dtl.name && !this.dtl.lastName) {
      str = this.formatI18n('/会员/会员资料', '会员姓名未知')
    } else {
      str = (this.dtl.name ?? '') + ' ' + (this.dtl.lastName ?? '')
    }
    return str
  }
  @State('loginInfo') loginInfo: UserLoginResult
  mainAndSubCard: any = []
  isMainMember = false
  unBindFlag = false
  waterFlag = false
  couponFlag = false
  prepayCardFlag = false
  tags: any = []
  cars: any = []
  adjustParams: any = {}
  dtl: MemberDetail = new MemberDetail()
  memberCustomGroups: MemberCustomGroup[] = []
  groupedData: any = []
  icCards: any = []
  editDataFlag = false
  adjustMemberLevelFlag = false
  resetPasswordFlag = false
  editTagFlag = false
  equityCards: EquityCard[] = [] //权益卡列表
  originalMemberId: string = ''
  nameMaxLength: Nullable<number> = null  //会员姓名最大长度限制
  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  panelArray: any = []
  remarkDialog = {
    visible: false,
    remark: '',
  }
  $refs: any
  switchFlag = false
  bindUuid: string = ''
  dtlAddress = ''
  singleStoreDialogShow: boolean = false
  isLuDao: Boolean = false
  unBindSubFlag: Boolean = false
  currentSubCard: Nullable<MainAndSubCard> = null
  isShowFirstAndLastName: boolean = false //是否将姓和名拆开显示
  showMobileAndEmailCheckInfo: boolean = false //是否展示会员手机号和邮箱核验信息
  showMemberCustomGroupInfo: boolean = false //是否展示会员自定义分组信息(宠物资料)
  customMemberAttr: any[] = []

  created() {
    this.panelArray = [
      {
        name: this.formatI18n('/会员/会员资料', '会员资料'),
        url: 'standard-member-list'
      },
      {
        name: this.formatI18n('/会员/会员资料', '会员详情'),
        url: ''
      }
    ]
    this.getDtl()
    this.getPrePermission()
    this.getMaxNameLength()
  }
  doEdit() {
    this.editDataFlag = true
  }
  doAdjust() {
    this.adjustMemberLevelFlag = true
  }
  doReset() {
    this.resetPasswordFlag = true
  }
  @Watch('$route.query.id')
  watchMemberIdChanged() {
    this.getDtl()
  }
  doInviteMember() {
    this.$router.push({ name: 'standard-member-dtl', query: { id: this.dtl.referee!.memberId } })
  }
  doUnbindClose() {
    this.unBindFlag = false
    this.getDtl()
  }
  doUnBind(id: string) {
    this.unBindFlag = true
    this.bindUuid = id
  }
  doFreezon() {
    let str: any = this.formatI18n('/会员/会员资料', '会员冻结后将无法继续使用和获得权益。请确认是否冻结此会员？')
    if (this.dtl.state === 'Blocked') {
      str = this.formatI18n('/会员/会员资料', '会员解冻后将可正常使用和获得权益。请确认是否解冻此会员？')
    }
    this.$confirm(str, this.formatI18n('/公用/弹出模态框提示标题', '提示') as any, {
      confirmButtonText: this.dtl.state === 'Blocked' ? this.formatI18n('/会员/会员资料', '解冻') as any : this.formatI18n('/会员/会员资料', '冻结') as any,
      cancelButtonText: this.formatI18n('/公用/按钮', '取消') as any
    }).then(() => {
      if (this.dtl.state === 'Blocked') {
        MemberApi.unBlock(this.$route.query.id as string).then((resp: any) => {
          if (resp && resp.code === 2000) {
            this.$message.success(this.formatI18n('/会员/会员资料', '解冻成功') as any)
            this.getDtl()
          }
        }).catch((error: any) => {
          if (error && error.message) {
            this.$message.error(error.message)
          }
        })
      } else {
        MemberApi.block(this.$route.query.id as string).then((resp: any) => {
          if (resp && resp.code === 2000) {
            this.$message.success(this.formatI18n('/会员/会员资料', '冻结成功') as any)
            this.getDtl()
          }
        }).catch((error: any) => {
          if (error && error.message) {
            this.$message.error(error.message)
          }
        })
      }
    })
  }
  doCancel() {
    // if (this.dtl.balance && !this.dtl.cardCount) {
    //   this.$message.warning(this.formatI18n("该会员有未用完的储值，需用完后才能注销") as string);
    //   return
    // }
    // if (!this.dtl.balance && this.dtl.cardCount) {
    //   this.$message.warning(this.formatI18n("该会员有未用完的礼品卡，需用完后才能注销") as string);
    //   return
    // }
    // if (this.dtl.balance && this.dtl.cardCount) {
    //   this.$message.warning(this.formatI18n("该会员有未用完的储值和礼品卡，需用完后才能注销") as string);
    //   return
    // }
    let str: any = this.formatI18n('/会员/会员资料', '请谨慎操作，注销以后该会员就不存在了，且不可恢复！')
    this.$confirm(str, this.formatI18n('/公用/弹出模态框提示标题', '提示') as any, {
      confirmButtonText: this.formatI18n('/会员/会员资料', '注销') as any,
      cancelButtonText: this.formatI18n('/公用/按钮', '取消') as any
    }).then(() => {
      MemberApi.cancelMember(this.$route.query.id as string).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.formatI18n('/会员/会员资料', '注销成功') as any)
          this.$router.push({ name: 'standard-member-list', query: {} })
        } else {
          throw new Error(resp.msg)
        }
      }).catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    })
  }
  doCardTemplate(cardId: string) {
    this.$router.push({ name: 'prepay-card-tpl-dtl', query: { number: cardId } })
  }
  doCheckCoupon() {
    this.couponFlag = true
  }
  doCheckPrePayCard() {
    this.prepayCardFlag = true
  }
  doEditTag() {
    this.editTagFlag = true
  }
  doWaterClose() {
    this.waterFlag = false
  }
  doCheckWater() {
    if (this.switchFlag) { // 开启多账户
      this.$router.push({ name: 'store-value-query', query: { id: this.dtl.crmCode } })
    } else {
      this.waterFlag = true
    }
  }

  doResetPwdClose() {
    this.resetPasswordFlag = false
  }
  doEditTagClose(tags: any) {
    this.editTagFlag = false
    this.getDtl()
  }
  doCouponClose() {
    this.couponFlag = false
  }
  doPrepayCardClose() {
    this.prepayCardFlag = false
  }
  doEditDataClose() {
    this.editDataFlag = false
    this.getDtl()
  }
  doEditAjustMemberLevelClose() {
    this.adjustMemberLevelFlag = false
    this.getDtl()
  }
  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
  }
  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val
  }
  getGrade(gradeCode: string, gradeName: string) {
    if (gradeCode) {
      return `[${gradeCode}]${gradeName}`
    } else {
      return '--'
    }
  }
  // 打开批量修改门店弹框
  private doSinglkeModifyStore() {
    this.singleStoreDialogShow = true
  }

  private singleStoreDialogClose() {
    this.singleStoreDialogShow = false
  }

  private getCRMConfig() {
    return new Promise((resolve, reject) => {
      PageConfigApi.getConfig().then(res => {
        if (res.code === 2000 && res.data) {
          this.isShowFirstAndLastName = BrowserMgr.SessionStorage.getItem('isShowFirstAndLastName')
          this.showMobileAndEmailCheckInfo = res.data.showMobileAndEmailCheckInfo || false
          this.showMemberCustomGroupInfo = res.data.showMemberCustomGroupInfo || false
          resolve(res.data.luDao)
        } else {
          resolve(null)
        }
      })
    })
  }

  getMainAndSubCardInfo(originalMemberId: string) {
    this.getCRMConfig().then((luDao: LuDaoConfig) => {
      // 鹿岛家庭卡
      if (luDao && luDao.showFamilyCard) {
        this.isLuDao = luDao.showFamilyCard
        let params = new LuDaoFamilyCardFilter()
        params.memberId = originalMemberId
        params.page = 0
        params.pageSize = 10
        LuDaoFamilyCardApi.query(params).then(res => {
          if (res.code === 2000 && res.data) {
            this.isMainMember = res.data.isMaster as boolean
            this.mainAndSubCard = res.data.infos
          } else {
            this.mainAndSubCard = []
          }
        })
      } else {
        // 小象主副卡
        this.isLuDao = false
        MemberApi.getMainAndSubCard(originalMemberId).then((resp: any) => {
          if (resp && resp.code === 2000) {
            if (resp.data != null && resp.data.length > 0) {
              this.isMainMember = resp.data[0].mainCard
              this.mainAndSubCard = resp.data
              this.$forceUpdate()
            } else {
              this.mainAndSubCard = []
            }
          }
        }).catch((error: any) => {
          if (error && error.message) {
            this.$message.error(error.message)
          }
        })
      }
    })
  }

  private singleStoreChangeSuccess() {
    this.getDtl()
    this.singleStoreDialogShow = false
  }

  private getDtl() {
    const loading = this.$loading(ConstantMgr.loadingOption)
    let originalMemberId = this.$route.query.id
    MemberApi.detail(this.$route.query.id as string).then(async (resp: any) => {
      if (resp?.code === 2000) {
        this.$route.query.id = resp.data.memberId
        this.dtl = resp.data
        console.log('email', this.dtl.email);
        if (this.dtl.province) {
          if (this.dtl.address) {
            this.dtlAddress = (this.dtl.province ? this.dtl.province.name + '/' : '') + (this.dtl.city ? this.dtl.city!.name + '/' : '') + (this.dtl.district ? this.dtl.district!.name + '/' : '') + (this.dtl.street ? this.dtl.street!.name + '/' : '') + this.dtl.address
          } else {
            this.dtlAddress = (this.dtl.province ? this.dtl.province.name + '/' : '') + (this.dtl.city ? this.dtl.city!.name + '/' : '') + (this.dtl.district ? this.dtl.district!.name + '/' : '') + (this.dtl.street ? this.dtl.street!.name + '/' : '')
          }
        } else {
          if (this.dtl.address) {
            this.dtlAddress = this.dtl.address
          } else {
            this.dtlAddress = ''
          }
        }
        this.cars = resp.data.cars
        this.memberCustomGroups = resp.data.memberCustomGroups
        this.getGroupedData()
        this.tags = []
        this.customMemberAttr = await Tools.getMemberExtInfo(this.dtl.extObj!)
        this.adjustParams = {
          level: this.dtl.gradeCode ? `[${this.dtl.gradeCode}]${this.dtl.gradeName}` : '',
          date: this.dtl.gradeValidate
        }
      } else {
        throw new Error(resp.msg)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    })
      .finally(() => {
        loading.close()
        this.getMainAndSubCardInfo(originalMemberId as string)
      })
    const params = new EquityCardQueryRequest()
    params.memberIdEquals = this.$route.query.id as string
    params.limitMarketingCenter = false
    params.page = this.page.currentPage - 1
    params.pageSize = this.page.size
    EquityCardApi.query(params).then(res => {
      if (res && res.code === 2000) {
        console.log('权益卡数据', res);
        this.equityCards = res.data!
      }
    })
  }
  private getPrePermission() {
    PrePayConfigApi.get().then((resp: any) => {
      if (resp && resp.code === 2000) {
        if (resp.data && resp.data.enableMultipleAccount) { // 开启多账户
          this.switchFlag = true
        } else {
          this.switchFlag = false // 未开启多账户
        }
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  getMaxNameLength() {
    MemberApi.getNameLengthLimit().then((res) => {
      if (res.code === 2000) {
        this.nameMaxLength = res.data || null
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    })
  }

  private getGroupedData() {
    console.log('会员自定义分组数据', this.memberCustomGroups)
    const tempGroupedData: { [key: string]: { fieldId: string, fieldKey: string, value: string }[] } = {};
    if(this.memberCustomGroups == null || this.memberCustomGroups.length == 0) {
      this.groupedData = []
      return
    }
    this.memberCustomGroups.forEach((item) => {
      const groupNum = item.groupNum
      if(groupNum == null) {
        return
      }
      if (!tempGroupedData[groupNum]) {
        tempGroupedData[groupNum] = []
      }
      const fieldId = item.fieldId;
      const fieldKey = item.fieldKey;
      const value = item.value;
      if (fieldId && fieldKey && value) {
        // 将字段名称和字段值添加到对应组的对象中
        tempGroupedData[groupNum].push({ fieldId, fieldKey, value })
      }
    })
    this.groupedData = Object.entries(tempGroupedData).map(([groupNum, fields]) => ({
      groupNum,
      fields
    }))
    console.log('分组数据', this.groupedData)
  }

  private editRemark() {
    this.remarkDialog.remark = this.dtl.remark as string
    this.remarkDialog.visible = true
  }

  private updateRemark() {
    MemberApi.editRemark(this.dtl.memberId as string, this.remarkDialog.remark).then((res) => {
      this.$message.success(this.formatI18n('/会员/会员资料/编辑成功'))
      this.remarkDialog.visible = false
      this.dtl.remark = this.remarkDialog.remark

    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private showOptLog() {
    this.$refs.memberOptLogDrawer.show(this.dtl.memberId)
  }

  private showAssets() {
    this.$refs.memberAssetsDrawer.show(this.dtl)
  }

  private deleteMobile(phone: string) {
    this.$confirm(this.formatI18n('/会员/会员资料/确定删除吗？'), this.formatI18n('/公用/弹出模态框提示标题/提示'), {
      confirmButtonText: this.formatI18n('/公用/按钮/确定'),
      cancelButtonText: this.formatI18n('/公用/按钮/取消'),
    }).then(() => {
      let params: DeleteMemberIdentRequest = new DeleteMemberIdentRequest()
      params.memberId = this.dtl.memberId as string
      params.ident.push({
        id: phone,
        type: 'mobile',
        source: null
      })
      MemberApi.deleteMemberIdent(params).then(res => {
        if (res.code === 2000) {
          this.$message({
            type: 'success',
            message: this.formatI18n('/公用/活动/提示信息/操作成功')
          });
        } else {
          this.$message.error(res.msg as string)
        }
      })
    }).catch(() => {

    });
  }

  doUnbindSubClose() {
    this.unBindSubFlag = false
  }

  private unbindSub(row: MainAndSubCard) {
    this.currentSubCard = row
    this.unBindSubFlag = true
  }

  unbindSubConfirm() {
    let params: UnbindSubMemberRequest = new UnbindSubMemberRequest()
    params.mainMemberId = this.$route.query.id as string
    params.subMemberId = this.currentSubCard!.subMemberId

    MemberApi.unbindSubMember(params).then(res => {
      if (res.code === 2000) {
        this.$message.success(this.formatI18n('/公用/活动/提示信息/操作成功'))
        this.doUnbindSubClose()
        this.getMainAndSubCardInfo(this.$route.query.id as string)
      } else {
        this.$message.error(res.msg as string)
      }
    })
  }

  timeFormat(time: Date) {
    return DateUtil.format(time, "yyyy-MM-dd HH:mm:ss")
  }

  stateFormate(state: string) {
    let str = ""
    switch (state) {
      case "CANCEL":
        str = this.formatI18n('/会员/权益卡', '已作废')
        break
      case "USING":
        str = this.formatI18n('/会员/权益卡', '使用中')
        break
      case "EXPIRED":
        str = this.formatI18n('/会员/权益卡', '已过期')
        break
      default:
        break
    }
    return str
  }

  cancelCard(cardNos: string[]) {
    this.$confirm('作废后，会员将不能享受该权益卡的权益，但不影响已获取的积分和券使用', '', {
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    }).then(() => {
      const params = new EquityCardCancelRequest()
      params.operator = this.loginInfo.user?.account
      params.cardNos = cardNos
      EquityCardApi.cancel(params).then((res) => {
        this.$message.success('操作成功')
        this.getDtl()
      }).catch((err) => {
        this.$message.error(err.msg)
      })
    })
  }

  //打开发券弹窗
  doSendCoupon() {
    this.$refs.sendCouponDialog.open()
  }

  //确定发券
  doSendSubmit(couponList: CouponItem[]) {
    const params = new ReissueCouponRequest()
    params.transId!.id = CommonUtil.uuid()
    params.transNo = params.transId!.id
    params.memberId = this.dtl.memberId
    if (couponList.length) {
      params.templateNumber = couponList[0].coupons?.templateId
      params.couponCount = couponList[0].qty
    }
    MemberApi.reissueCoupon(params).then((res) => {
      if (res.code === 2000) {
        this.$message.success(this.i18n('操作成功'))
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    })
  }

  // 是否为图片url
  isImgStr(str: string) {
    return CommonUtil.isImgStr(str)
  }

  // tab页面索引
  currentTabIndex: number = 0;

  onTabChange(index: number) {
    this.currentTabIndex = index;
  }
}
