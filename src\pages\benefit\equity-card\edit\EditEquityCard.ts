
/*
 * @Author: 黎钰龙
 * @Date: 2023-02-24 16:34:11
 * @LastEditTime: 2024-04-24 13:34:45
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\benefit\equity-card\edit\EditEquityCard.ts
 * 记得注释
 */
import ActiveAddCoupon from 'cmp/activeaddcoupon/ActiveAddCoupon';
import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import FormItem from 'cmp/formitem/FormItem';
import I18nPage from 'common/I18nDecorator';
import ChannelManagementApi from 'http/channelmanagement/ChannelManagementApi';
import EquityCardApi from 'http/equityCard/equityCardApi';
import { ExpiryType } from 'model/common/ExpiryType';
import RSChannelManagement from 'model/common/RSChannelManagement';
import RSChannelManagementFilter from 'model/common/RSChannelManagementFilter';
import BEquityCardExpiryRule from 'model/equityCard/BEquityCardExpiryRule';
import { DateType } from 'model/weixin/weixinIssueCouponActivity/DateType';
import { Component, Provide, Vue, Watch } from 'vue-property-decorator';
import EditEquityCardForm from './EditEquityCardForm';
@Component({
  name: 'EditEquityCard',
  components: {
    BreadCrume,
    ActiveAddCoupon,
    FormItem,
  }
})
@I18nPage({
  auto: false,
  prefix: [
    '/会员/会员批量操作单',
    '/会员/权益卡设置',
    '/公用/券模板'
  ]
})
export default class EditEquityCard extends Vue {
  @Provide('maxCouponItemLimit') maxCouponItemLimit = 99999 //ActiveAddCoupon组件的输入框最大值
  panelArray: Array<any> = []
  $refs: any
  form = new EditEquityCardForm() //当前页面的表单数据
  loading = {
    save: false,
    detail: false,
  }
  availableDate: BEquityCardExpiryRule[] = [new BEquityCardExpiryRule()]
  giftBag = { //领卡奖励
    points: {
      isSelect: false,
      value: 1
    },
    coupons: {
      isSelect: false,
      list: []
    }
  }
  cardEquity = { //领卡权益 发放时间
    intervalVal: 1,
    intervalType: DateType.MONTH,
    weekDay: 1,
    total: 1,
    coupons: []
  }
  receiveCardChannel: RSChannelManagement[] = []  //领卡渠道列表
  selectChannel: string[] = []  //已选领卡渠道,id+type
  excludedTypes: any[] = []

  created() {
    this.form.init(this)
    this.panelArray = [
      {
        name: this.i18n('/公用/菜单/权益卡设置'),
        url: 'equity-card-list'
      },
      {
        name: this.i18n('新建权益卡'),
        url: ''
      }
    ]
    if (this.$route.query.type === 'edit' && this.$route.query.item) {
      this.panelArray[1].name = this.i18n('编辑权益卡')
      this.form.of(this, JSON.parse(this.$route.query.item as any))
    }
    this.excludedTypes = [
      "exchange_goods",
      "goods",
      "freight",
      "random_cash",
      "points",
    ];
    this.getCardChannel()
  }

  //添加规划
  doAddProject() {
    if (this.availableDate.length >= 10) return this.$message.warning(this.i18n('最多设置{0}个档位').replace(/\{0\}/g,'10'))
    this.availableDate.push(new BEquityCardExpiryRule())
    let newInfo = this.availableDate[this.availableDate.length - 1].validityInfo
    if (newInfo) {
      newInfo.expiryType = ExpiryType.DAYS
    }
  }

  //删除有效期
  doRemoveProject(index: any) {
    this.availableDate.splice(index, 1)
  }

  //礼包设置 添加券
  doAddCoupon(type: string) {
    let ref = type
    this.$refs[ref].doAddCoupon(0)
  }

  //保存
  doSave() {
    const params = this.form.toParams(this)
    this.$refs.form.validate().then(() => {
      if (this.$route.query.type === "edit") {
        EquityCardApi.modify(params).then(res => {
          if (res.code === 2000) {
            this.$message.success(this.i18n('/公用/活动/提示信息/保存成功'))
            this.$router.push({
              name: 'equity-card-list'
            })
          } else {
            this.$message.error(res.msg!)
          }
        })
      } else {
        EquityCardApi.save(params).then(res => {
          if (res.code === 2000) {
            this.$message.success(this.i18n('/公用/活动/提示信息/保存成功'))
            this.$router.push({
              name: 'equity-card-list'
            })
          } else {
            this.$message.error(res.msg!)
          }
        })
      }
    })
  }

  //取消
  doCancel() {
    this.$router.push({
      name: 'equity-card-list'
    })
  }

  //有效期设置 有效期输入框
  autoInputValidityNum(index: any) {
    const validityInfo = this.availableDate[index].validityInfo
    if (validityInfo && validityInfo.expiryType === ExpiryType.YEARS) {
      return 10
    }
    if (validityInfo && validityInfo.expiryType === ExpiryType.MONTHS) {
      return 50
    }
    if (validityInfo && validityInfo.expiryType === ExpiryType.DAYS) {
      return 1000
    }
  }

  //发放时间设置 发放周期输入框
  provideInputValidityNum() {
    const validityInfo = this.cardEquity.intervalType
    if (validityInfo === "DAY") {
      return 1000
    }
    if (validityInfo === "WEEK") {
      return 52
    }
    if (validityInfo === "MONTH") {
      return 50
    }
  }

  //查询领卡渠道
  getCardChannel() {
    console.log('getCardChannel');
    let param: RSChannelManagementFilter = new RSChannelManagementFilter();
    ChannelManagementApi.query(param).then((res) => {
      if (res.code === 2000) {
        this.receiveCardChannel = res.data!
      } else {
        throw new Error(res.msg!)
      }
    }).catch(err => {
      this.$message.error(err.message)
    })
  }

  clearCoupons(val:boolean) {
    if(!val) {
      this.giftBag.coupons.list.splice(0, this.giftBag.coupons.list.length)
    }
  }

  //当有效期select状态改变后，纠正对应input值
  changeSelect(index: any) {
    const validityInfo = this.availableDate[index].validityInfo
    if (validityInfo) {
      if (validityInfo.validityDays! > this.autoInputValidityNum(index)!) {
        validityInfo.validityDays = this.autoInputValidityNum(index)
      }
    }
  }

  //当有效期select状态改变后，纠正对应input值
  changeProvideSelect() {
    if (this.cardEquity.intervalVal > this.provideInputValidityNum()!) {
      this.cardEquity.intervalVal = this.provideInputValidityNum()!
    }
  }
};