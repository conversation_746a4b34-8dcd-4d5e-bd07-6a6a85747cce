// 适用商品组规则明细
export default class BWeimobExtLimitGoodsGroupRuleInfo {
  // 组id
  classifyId: Nullable<string> = null
  // 组级别
  classifyLevel: Nullable<string> = null
  // 父级id
  parentId: Nullable<string> = null
  // 组名称
  name: Nullable<string> = null
  // 是否检查
  isChecked: Nullable<boolean> = null
  // 子节点个数
  childNum: Nullable<number> = null
  // 子节点
  childs: BWeimobExtLimitGoodsGroupRuleInfo[] = []
}