<?xml version="1.0" encoding="UTF-8"?>
<svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 60 (88103) - https://sketch.com -->
    <title>ic_ziliao_selected</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="10" y="14" width="12" height="2" rx="1"></rect>
        <filter x="-58.3%" y="-250.0%" width="216.7%" height="800.0%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0.494117647   0 0 0 0 1  0 0 0 0.301491477 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="🔪icon" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="切图" transform="translate(-650.000000, -189.000000)">
            <g id="ic_ziliao_selected" transform="translate(650.000000, 189.000000)">
                <g id="cutting/ic_ziliao_selected">
                    <g>
                        <path d="M7.52788809,6 L10.3897599,6 C10.8220998,6 11.2427993,6.14009636 11.588823,6.39929774 L14.5276118,8.60070226 C14.8736355,8.85990364 15.2943351,9 15.7266749,9 L24,9 C26.209139,9 28,10.790861 28,13 L28,22 C28,24.209139 26.209139,26 24,26 L8,26 C5.790861,26 4,24.209139 4,22 L4,9.52788809 C4,7.5794893 5.5794893,6 7.52788809,6 Z" id="矩形" fill="#FFFFFF"></path>
                        <g id="矩形">
                            <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                            <use fill="#007EFF" fill-rule="evenodd" xlink:href="#path-1"></use>
                        </g>
                        <rect x="0" y="0" width="32" height="32"></rect>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>