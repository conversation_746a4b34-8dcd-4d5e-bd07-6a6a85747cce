<template>
  <div class="gitf-card-activity-edit">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button v-if="
            hasOptionPermission('/卡/卡活动/电子卡售卡活动', '活动维护')
          " type="primary" @click="save">保存
        </el-button>
        <el-button v-if="
            hasOptionPermission('/卡/卡活动/电子卡售卡活动', '活动审核') &&
            editFlag === false &&
            loading === false
          " type="primary" @click="saveAndAudit">保存并审核
        </el-button>
        <el-button @click="$router.go(-1)">取消</el-button>
      </template>
    </BreadCrume>

    <el-row v-loading="loading" class="current-page">
      <el-form :model="form.data" :rules="form.rules" ref="form" label-width="100px">
        <div class="panel">
          <div class="header">活动信息</div>
          <div class="content">
            <el-form-item label="活动名称" :required="true" prop="name">
              <el-input :disabled="editFlag" style="width: 298px" v-model="form.data.name" placeholder="请输入不超过80个字符" maxlength="80" />&nbsp;&nbsp;
            </el-form-item>
            <el-form-item :label="formatI18n('/营销/积分活动/积分活动/商品满额加送积分/新建页面/活动信息/活动主题')" prop="topicCode">
              <el-select :disabled="editFlag" :placeholder="formatI18n('/营销/积分活动/积分活动/商品满额加送积分/新建页面/活动信息/请选择')" clearable style="width: 350px"
                value-key="code" v-model="form.data.topic">
                <el-option :label="item.name" :value="item" :key="item.code" v-for="item in themes">{{ item.name }}
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="活动时间" :required="true">
              <!-- <el-date-picker type="daterange" :picker-options="dateRangeOption" range-separator="-"
                v-model="form.data.timeRange" start-placeholder="开始日期" end-placeholder="结束日期">
              </el-date-picker> -->
               <ActivityDateTimeConditionPicker v-model="form.data.activityDateTimeCondition" ref="activityDateTimeConditionPicker">
                  </ActivityDateTimeConditionPicker>
            </el-form-item>

            <el-form-item :disabled="editFlag" :label="formatI18n('/储值/预付卡/卡模板/编辑页面/适用门店')" :required="true">
              <ActiveStore v-if="editFlag === false" ref="storeScope" :enableStore="true" :sameStore="false" v-model="form.data.stores">
              </ActiveStore>
              <ActiveStoreDtl style="width: 60%; min-width: 800px" :data="form.data.stores" v-if="editFlag === true"></ActiveStoreDtl>
            </el-form-item>

            <el-form-item label="活动封面" :required="true" prop="themePictureUrls">
              <el-row style="display: inline-block">
                <el-row style="line-height: 35px">
                  <span style="color: #999">
                    {{formatI18n("/储值/预付卡/电子礼品卡活动/编辑页面","图片比例支持4:3（建议尺寸800*600像素），支持jpg/jpeg/png/gif，不超过300KB")}}
                  </span>
                </el-row>
                <el-col :span="12" style="max-width: 90px" v-show="form.data.themePictureUrls.length < 8">
                  <el-upload ref="upload" :disabled="editFlag" :headers="uploadHeaders" :action="uploadUrl" :with-credentials="true"
                    :show-file-list="false" :limit="8" multiple :auto-upload="false" :on-success="onUploadSuccess" :before-upload="beforeAvatarUpload"
                    :on-change="onUploadChange">
                    <el-button :disabled="editFlag" style="color: rgb(0, 126, 255); border-color: rgb(0, 126, 255)">上传</el-button>
                  </el-upload>
                </el-col>
              </el-row>

              <el-row>
                <div class="cardpic-list" v-if="form.data.themePictureUrls && form.data.themePictureUrls.length">
                  <el-input style="display: none" v-model="form.data.themePictureUrls" />
                  <div class="pic" v-for="(img, index) in form.data.themePictureUrls" :key="index">
                    <div class="pic-content">
                      <div v-if="!editFlag" class="pic-cancel" @click="editFlag === true ? '' : deleteImg(index)" no-i18n>
                        ×
                      </div>
                      <img :src="img" />
                    </div>
                  </div>
                </div>
              </el-row>
            </el-form-item>
            <el-form-item label="祝福语" prop="defaultGiftMsg">
              <el-input :disabled="editFlag" type="textarea" placeholder="请输入不超过50个字" maxlength="50" v-model="form.data.defaultGiftMsg"
                style="width: 500px" />
            </el-form-item>
            <el-form-item label="活动说明" prop="theme">
              <el-input :disabled="editFlag" v-model="form.data.remark" type="textarea" placeholder="请输入不超过500个字" :rows="5" maxlength="500"
                style="width: 500px" />
            </el-form-item>
          </div>
        </div>

        <div class="split"></div>

        <div class="panel">
          <div class="header">发售设置</div>
          <div class="content">
            <!-- 新改版电子卡发售 PHX-9673 -->
            <el-form-item label="优惠类型" prop="favType">
              <el-radio-group v-model="form.data.favType" :disabled="editFlag">
                <el-radio label="discount">{{i18n('折扣')}}</el-radio>
                <el-radio label="amount">{{i18n('指定售价')}}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="卡类型" prop="cardType">
              <el-radio-group v-model="form.data.cardType" :disabled="editFlag" @input="cardTypeChange">
                <el-radio label="GiftCard">{{i18n('礼品卡')}}</el-radio>
                <el-radio label="RechargeableCard">{{i18n('储值卡')}}</el-radio>
                <el-radio label="ImprestCard">{{i18n('充值卡')}}</el-radio>
                <el-radio label="CountingCard">{{i18n('次卡')}}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="卡模板" prop="saleSpecs">
              <ActivePresentCard ref="activePresentCard" v-model="form.data.saleSpecs" @change="()=>{this.$refs.form.validateField(`saleSpecs`)}"
                :cardMediumIn="[CardMedium.online]" :favType="form.data.favType" :editFlag="editFlag" :cardType="form.data.cardType">
              </ActivePresentCard>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-row>
    <CouponTemplateSelectorDialog ref="couponTemplate" :filter="cardTemplateFilter" @summit="doCardTemplateSelected">
    </CouponTemplateSelectorDialog>
    <SelectStoreActiveDtlDialog :child="child" :dialogShow="couponDialogShow" @dialogClose="doCouponDialogClose">
    </SelectStoreActiveDtlDialog>
    <el-dialog title="选择卡样" :visible.sync="picSelectorDialog.dialogVisible" class="inner-dialog-center" width="50%">
      <img @click="selectPic(picUrl)" class="card-pic" style="width: 100px; height: 100px; margin: 10px" :src="picUrl"
        v-for="(picUrl, index) of picSelectorDialog.tplPicList" :key="index" alt="" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="picSelectorDialog.dialogVisible = false">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script lang="ts" src="./GiftCardActivityEdit.ts"></script>

<style lang="scss">
.gitf-card-activity-edit {
  width: 100%;
  height: 100%;
  background-color: white;
  overflow: auto;

  .inner-dialog-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .current-page {
    height: calc(100% - 60px) !important;
    overflow-y: scroll;

    .panel {
      .header {
        font-weight: 500;
        padding: 20px 20px 0 20px;
        font-size: 18px;
      }

      .content {
        padding: 20px;

        .el-range__icon {
          line-height: 26px;
        }

        .el-range-separator {
          line-height: 26px;
        }

        .el-range__close-icon {
          line-height: 26px;
        }

        .cardpic-list {
          .pic {
            width: 100px;
            height: 120px;
            margin-right: 10px;
            display: inline-block;

            .pic-content {
              position: relative;
              width: 100px;
              height: 100px;
              background-color: #dddddd;

              .pic-cancel {
                position: absolute;
                right: 0;
                top: 0;
                color: white;
                background-color: black;
                font-size: 22px;
                line-height: 15px;
              }

              img {
                width: 100%;
                height: 100%;
              }
            }
          }
        }

        .face-amount-spec {
          .el-row {
            margin-top: 15px;
            background-color: #f9f9f9;
          }

          .el-button {
            max-width: 300px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .header {
            padding: 0;
            margin: 0 20px;
            font-weight: 500;
            font-size: 16px;
          }
        }
      }
    }

    .split {
      height: 20px;
      background-color: #eeeff1;
    }
  }

  .card-pic {
    &:hover {
      box-shadow: 5px 5px 10px #888888;
      cursor: pointer;
    }
  }
}
</style>
<style>
.gitf-card-activity-edit .el-table td .cell {
  padding: 15px 6px;
}
</style>
