<template>
  <div>
    <el-dialog :title="formatI18n('/资料/门店/导入门店')" class="upload-file-dialogxxx" :close-on-click-modal="false"
               :visible.sync="dialogShow"
               :before-close="doBeforeClose">
      <div class="wrap">
        <FormItem label="">
          {{formatI18n('/公用/导入/实例模板')}}&nbsp;&nbsp;
          <a
              style="line-height: 12px; color: #318BFF;text-decoration: none"
              v-if="templateType === 'store'"
              class="action-hover_download" @click="downloadTemplate">{{formatI18n('/资料/门店/门店资料模板')}}</a>
        </FormItem>
        <FormItem label=" ">
          <div style="position: relative;top: 6px; margin-bottom: 20px;width: 90%">{{formatI18n('/公用/导入/为保障上传成功，建议每次最多上传{0}条信息',
            null, ['5000'])}}
          </div>
          <el-upload
              :headers="uploadHeaders"
              class="upload-demo"
              ref="upload"
              :file-list="fileList"
              :action="getUploadUrl"
              :with-credentials="true"
              :on-success="getSuccessInfo"
              :on-error="getErrorInfo"
              :on-change="doHandleChange"
              :before-upload="beforeUpload"
              :auto-upload="false">
            <el-button slot="trigger" size="small" type="default">{{formatI18n('/公用/导入/选择文件')}}</el-button>
          </el-upload>
        </FormItem>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="doModalClose('cancel')">{{formatI18n('/资料/门店/取 消')}}</el-button>
        <el-button size="small" type="primary" @click="doModalClose('confirm')" :loading="loading">
          {{formatI18n('/资料/门店/确 定')}}
        </el-button>
      </div>
    </el-dialog>
    <DownloadCenterDialog :dialogvisiable="dialogvisiable"
                          @dialogClose="dialogClose"></DownloadCenterDialog>
  </div>
</template>

<script lang="ts" src="./UploadFileModal.ts">
</script>

<style lang="scss">
  .upload-file-dialogxxx {
    display: flex;
    justify-content: center;
    align-items: center;

    .wrap {
      margin-top: 30px;
      //.left{
      //  text-align: left;
      //  margin-bottom: 10px;
      //}
    }

    .el-dialog {
      width: 600px;
      margin: 0 !important;
    }
  }
  .action-hover_download {
    cursor: pointer;
    /* 添加手型光标 */
  }
</style>