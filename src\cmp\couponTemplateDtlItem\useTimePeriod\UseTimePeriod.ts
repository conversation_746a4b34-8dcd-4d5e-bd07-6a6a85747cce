/*
 * @Author: 黎钰龙
 * @Date: 2023-06-30 15:49:58
 * @LastEditTime: 2023-06-30 15:52:24
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\couponTemplateDtlItem\useTimePeriod\UseTimePeriod.ts
 * 记得注释
 */
import CouponItem from 'model/common/CouponItem';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  name: 'UseTimePeriod'
})
export default class UseTimePeriod extends Vue {
  @Prop()
  data: CouponItem;

  getDayTime(beginTime: any, endTime: any) {
    let str: any = this.formatI18n(
      "/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送券的时候并且该券用券时段为指定可用时段每天时/点击券名称",
      "每天{0}至{1}"
    );
    str = str.replace(
      /\{0\}/g,
      `&nbsp;<span style="font-weight: bold">${beginTime}</span>&nbsp;`
    );
    str = str.replace(
      /\{1\}/g,
      `&nbsp;<span style="font-weight: bold">${endTime}</span>&nbsp;`
    );
    return str;
  }


  getMonthTime(days: any, beginTime: any, endTime: any) {
    let str: any = this.formatI18n(
      "/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送券的时候并且该券用券时段为指定可用时段每月时/点击券名称",
      "每月{0}日{1}至{2}"
    );
    str = str.replace(
      /\{0\}/g,
      days.slice().sort((a: number, b: number) => {
        return a - b;
      })
    );
    str = str.replace(
      /\{1\}/g,
      `&nbsp;<span style="font-weight: bold">${beginTime}</span>&nbsp;`
    );
    str = str.replace(
      /\{2\}/g,
      `&nbsp;<span style="font-weight: bold">${endTime}</span>&nbsp;`
    );
    return str;
  }

  getWeekTime(days: any, beginTime: any, endTime: any) {
    let str: any = this.formatI18n(
      "/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送券的时候并且该券用券时段为指定可用时段每周时/点击券名称",
      "每周{0}{1}到{2}"
    );
    str = str.replace(
      /\{0\}/g,
      `&nbsp;<span style="font-weight: bold">${this.getWeeks(
        days
      )}</span>&nbsp;`
    );
    str = str.replace(
      /\{1\}/g,
      `&nbsp;<span style="font-weight: bold">${beginTime}</span>&nbsp;`
    );
    str = str.replace(
      /\{2\}/g,
      `&nbsp;<span style="font-weight: bold">${endTime}</span>&nbsp;`
    );
    return str;
  }


  getWeeks(value: any) {
    if (value && value.length > 0) {
      let arr = value;
      value.forEach((item: any, index: number) => {
        if (item === 1) {
          arr[index] = this.formatI18n("/公用/券模板", "周一");
        }
        if (item === 2) {
          arr[index] = this.formatI18n("/公用/券模板", "周二");
        }
        if (item === 3) {
          arr[index] = this.formatI18n("/公用/券模板", "周三");
        }
        if (item === 4) {
          arr[index] = this.formatI18n("/公用/券模板", "周四");
        }
        if (item === 5) {
          arr[index] = this.formatI18n("/公用/券模板", "周五");
        }
        if (item === 6) {
          arr[index] = this.formatI18n("/公用/券模板", "周六");
        }
        if (item === 7) {
          arr[index] = this.formatI18n("/公用/券模板", "周日");
        }
      });
      return arr;
    } else {
      return [];
    }
  }
};