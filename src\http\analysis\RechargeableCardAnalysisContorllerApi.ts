import ApiClient from 'http/ApiClient'
import BPrepayCardAnalysisFilter from 'model/analysis/BPrepayCardAnalysisFilter'
import BRechargeableCardAnalysisReportGraphic from 'model/analysis/BRechargeableCardAnalysisReportGraphic'
import BRechargeableCardAnalysisReport from 'model/analysis/BRechargeableCardAnalysisReport'

import Response from 'model/default/Response'

export default class RechargeableCardAnalysisContorllerApi {
  /**
   * 导出
   * 导出
   * 
   */
  static export(body: BPrepayCardAnalysisFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/analysis-report/rechargeable-card/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 充值卡分析报表
   * 充值卡分析报表
   * 
   */
  static query(body: BPrepayCardAnalysisFilter): Promise<Response<BRechargeableCardAnalysisReportGraphic>> {
    return ApiClient.server().post(`/v1/analysis-report/rechargeable-card/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

    /**
   * 汇总查询
   * 汇总查询
   * 
   */
     static querySum(body: BPrepayCardAnalysisFilter): Promise<Response<BRechargeableCardAnalysisReport>> {
      return ApiClient.server().post(`/v1/analysis-report/rechargeable-card/querySum`, body, {
      }).then((res) => {
        return res.data
      })
    }

}
