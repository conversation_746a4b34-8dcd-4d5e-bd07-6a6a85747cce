<template>
  <div class="invite-send-gift">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button
          @click="doSave('save')"
          type="primary"
          v-if="
            hasOptionPermission(
              '/券/券管理/券核销',
              '单据维护'
            )
          "
          >{{ formatI18n("/公用/按钮", "保存") }}</el-button
        >
        <el-button
          @click="doSave('save-and-audit')"
          v-if="
            (state === 'INITAIL' || !state) &&
              hasOptionPermission(
                '/券/券管理/券核销',
                '单据维护'
              ) &&
              hasOptionPermission(
                '/券/券管理/券核销',
                '单据审核'
              )
          "
          >{{ formatI18n("/公用/按钮", "保存并审核") }}</el-button
        >
        <el-button
          @click="doBack"
          v-if="
            hasOptionPermission(
              '/券/券管理/券核销',
              '单据维护'
            )
          "
          >{{ formatI18n("/公用/按钮", "取消") }}</el-button
        >
      </template>
    </BreadCrume>
    <div style="overflow: auto;height: 95%">
      <div class="cur-content">
        <el-form
          :model="form"
          :rules="rules"
          class="demo-ruleForm"
          label-width="130px"
          ref="ruleForm"
        >
          <el-form-item >
            <div slot="label">
              <span style="color: red">*</span>
              <span>{{i18n('券号')}}</span>
            </div>
            <div v-for="(item, index) in form.couponCodes" :key="index" style="margin-bottom: 20px">
              <el-form-item
                :prop="'couponCodes.' + index"
                :rules="{
                  required: true,
                  message: formatI18n('/公用/js提示信息', '请填写必填项'),
                  trigger: 'blur',
                }"
              >
                <el-input
                  class="code-input"
                  v-model="form.couponCodes[index]"
                  @change="couponCodeChange(index)"
                ></el-input>
                <el-button
                  type="text"
                  class="del-btn"
                  v-if="form.couponCodes.length > 1"
                  @click="delCouponCode(index)"
                  >{{ i18n("删除") }}</el-button
                >
                <el-button
                  type="text"
                  @click="addCouponCode"
                  v-if="index === form.couponCodes.length - 1 && form.couponCodes.length < 10"
                  >+{{ i18n("添加") }}</el-button
                >
              </el-form-item>
            </div>
          </el-form-item>
          <el-form-item prop="issueOrgId" :label="i18n('发生组织')">
            <SelectStores v-model="form.issueOrgId" :isOnlyId="true" :hideAll="true" width="400px"
              :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
            </SelectStores>
            </el-form-item>
            <el-form-item label="说明">
                <el-input type="textarea" v-model="form.remark" style="width: 500px"></el-input>
            </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./CouponWriteOffEdit.ts"></script>

<style lang="scss">
.invite-send-gift {
  width: 100%;
  height: 100%;
  background: white;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .code-input {
    width: 400px;
    margin-right: 20px;
    margin-top: 20px;
  }
  .code-input:nth-child(1) {
    margin-top: 0;
  }
  .cur-content {
    padding-top: 40px;
  }
  .del-btn {
    color: red;
  }
}
</style>
