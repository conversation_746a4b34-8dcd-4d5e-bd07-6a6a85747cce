import {Vue} from 'vue-property-decorator'
import PermissionMgr from "mgr/PermissionMgr";

export default class CardBalancePromotionPermission extends Vue {
  // 预付卡支付立减
  get editable() {
    return PermissionMgr.hasOptionPermission('/卡/卡活动/预付卡支付优惠/预付卡支付立减', '活动维护');
  }

  get auditable() {
    return PermissionMgr.hasOptionPermission('/卡/卡活动/预付卡支付优惠/预付卡支付立减', '活动审核');
  }

  get terminable() {
    return PermissionMgr.hasOptionPermission('/卡/卡活动/预付卡支付优惠/预付卡支付立减', '活动终止');
  }

  get viewable() {
    return PermissionMgr.hasOptionPermission('/卡/卡活动/预付卡支付优惠/预付卡支付立减', '活动查看');
  }

  // 预付卡支付折扣
  get discountViewable() {
    return PermissionMgr.hasOptionPermission('/卡/卡活动/预付卡支付优惠/预付卡支付折扣', '活动查看');
  }

  get discountEditable() {
    return PermissionMgr.hasOptionPermission('/卡/卡活动/预付卡支付优惠/预付卡支付折扣', '活动维护');
  }

  get discountAuditable() {
    return PermissionMgr.hasOptionPermission('/卡/卡活动/预付卡支付优惠/预付卡支付折扣', '活动审核');
  }

  get discountTerminable() {
    return PermissionMgr.hasOptionPermission('/卡/卡活动/预付卡支付优惠/预付卡支付折扣', '活动终止');
  }
}
