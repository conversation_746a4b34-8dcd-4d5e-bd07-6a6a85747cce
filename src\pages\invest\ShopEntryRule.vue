<template>
  <div class="shop-entry-rule-container">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button @click="doSave" type="primary" v-if="modelType !== 'view' && hasOptionPermission('/设置/小程序装修/进店规则', '配置维护')">
          {{ formatI18n("/公用/按钮", "保存") }}
        </el-button>
        <el-button @click="doUpdate" type="primary" v-if="modelType === 'view' && hasOptionPermission('/设置/小程序装修/进店规则', '配置维护') ">{{formatI18n('/公用/按钮', '修改')}}</el-button>

        <el-button @click="doCancel" v-if="modelType !== 'view'" >{{formatI18n('/公用/按钮', '取消')}}</el-button>
      </template>
    </BreadCrume>
    <div class="current-page">
      <el-form :model="config" >

        <el-form-item :label="formatI18n('/设置/小程序装修/进店规则', '网店模式') + ':'" prop="multipleStores">
          <div v-if="modelType === 'view'">
            {{ config.multipleStores ? formatI18n('/设置/小程序装修/进店规则/多网店模式') : formatI18n('/设置/小程序装修/进店规则/单网店模式')}}
          </div>
          <div v-else>
              <el-radio-group v-model="config.multipleStores">
                <el-radio :label="true">{{ formatI18n("/设置/小程序装修/进店规则", "多网店模式") }}</el-radio>
                <el-radio :label="false">{{ formatI18n("/设置/小程序装修/进店规则", "单网店模式") }}</el-radio>
              </el-radio-group>
          </div>
          <div v-if="!config.multipleStores" style="margin-left: 70px">
            {{ formatI18n("/设置/小程序装修/进店规则", "顾客将无差别进入默认门店") }}
          </div>
        </el-form-item>
        <el-form-item v-if="config.multipleStores" :label="formatI18n('/设置/小程序装修/进店规则', 'LBS定位推荐')" prop="lbsEnabled">
          <div v-if="modelType === 'view'">
             {{ config.lbsEnabled ? formatI18n('/设置/系统设置/开启') : formatI18n('/设置/系统设置/关闭')}}
          </div>
          <div v-else>
            <div class="form-item-tip">{{ config.lbsEnabled ? formatI18n('/设置/小程序装修/进店规则/禁用提示') : formatI18n('/设置/小程序装修/进店规则/启用提示') }}</div>
            <el-switch v-model="config.lbsEnabled" :disabled="!hasOptionPermission('/设置/小程序装修/进店规则', '配置维护')"></el-switch>
          </div>
        </el-form-item>
      </el-form>

    </div>

  </div>
</template>

<script lang="ts" src="./ShopEntryRule.ts">
</script>
<style>
.shop-entry-rule-container {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: hidden;
  .current-page {
    margin-top: 30px;
    margin-left: 30px;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 30px;
  }

  .form-item-tip {
    color: #999999;
  }
}
</style>