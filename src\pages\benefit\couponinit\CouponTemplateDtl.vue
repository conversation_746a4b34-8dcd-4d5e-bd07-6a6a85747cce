<template>
  <div class="coupon-template-dtl">
    <BreadCrume :panelArray="panelArray" :fontSize="18">
      <template slot="operate">
        <el-button v-if="hasOptionPermission('/券/券管理/券模板', '券模板维护')" @click="doCopy">{{
            formatI18n('/公用/按钮/复制')
          }}
        </el-button>
        <el-button v-if="hasOptionPermission('/券/券管理/券模板', '券模板维护')&&dtl.scope !== 'weixin' && dtl.state !== 'CANCELLED'" @click="doEdit">{{
            formatI18n('/公用/按钮/修改')
          }}
        </el-button>
      </template>
    </BreadCrume>
    <div style="overflow: auto">
      <div class="top-wrap">

        <div class="top-header">
          <div v-if="loaded" class="avatar">
            <el-image v-if="dtl.logoUrl" class="avatar-img" :src="dtl.logoUrl" fit="fill">
            </el-image>
            <div class="back" v-else>
              <img src="~assets/image/storevalue/img_default_no_picture.png">
            </div>
          </div>
          <div class="header-info">
            <div class="header-title">
              <div class="coupon-name" :title="dtl.name" v-if="dtl  && dtl.name">{{ dtl.name }}
              </div>
              <template v-if="dtl && dtl.state">
                <div class="state-block" v-if="dtl.state === 'EFFECTED'" style="background:#0CC66D">
                  {{ i18n('已生效') }}
                </div>
                <div class="state-block" v-if="dtl.state === 'NOT_EFFECTED'" style="background:#007EFF">
                  {{ i18n('未生效') }}
                </div>
                <div class="state-block" v-if="dtl.state === 'CANCELLED'" style="background:#A1B0C8">
                  {{ i18n('已作废') }}
                </div>
                <div class="state-block" v-if="dtl.state === 'INITIAL'" style="background:#FFAA00">
                  {{ i18n('未审核') }}
                </div>
                <div class="state-block" v-if="dtl.state === 'EXPIRED'" style="background:#A1B0C8">
                  {{ i18n('已过期') }}
                </div>
              </template>
            </div>
            <div class="coupon-number" v-if="dtl && dtl.templateId">{{ i18n('券模板号') }}：{{ dtl.templateId }}
            </div>
          </div>
        </div>
        <el-row class="top-body">
          <el-col :span="5" class="body-section">
            <div class="body-container">
              <div class="body-title">{{ i18n('券类型') }}</div>
              <div class="body-info">{{ dtl.couponBasicType | lineCouponType }}</div>
            </div>
          </el-col>
          <template>
            <!-- 不同券类型展示的不同字段 -->
            <el-col :span="5" class="body-section"
              v-if="dtl.couponBasicType === 'all_cash' || dtl.couponBasicType === 'goods_cash' || dtl.couponBasicType === 'freight'">
              <div class="body-container">
                <!-- 现金券、运费券 -->
                <div class="body-title">{{ i18n('券面额') }}</div>
                <div class="body-info">
                  {{dtl.cashCouponAttribute.faceAmount | fmt}}
                  {{ i18n('元') }}</div>
              </div>
            </el-col>
            <el-col :span="5" class="body-section"
              v-else-if="dtl.couponBasicType === 'random_cash' && dtl.randomCouponAttribute && dtl.randomCouponAttribute.minFaceAmount && dtl.randomCouponAttribute.maxFaceAmount">
              <div class="body-container">
                <!-- 随机金额券 -->
                <div class="body-title">{{ i18n('券面额范围') }}</div>
                <div class="body-info">
                  {{ dtl.randomCouponAttribute.minFaceAmount }}{{ i18n('元') }} —
                  {{ dtl.randomCouponAttribute.maxFaceAmount }}{{ i18n('元') }}
                </div>
              </div>
            </el-col>
            <el-col :span="5" class="body-section"
              v-else-if="dtl.couponBasicType === 'all_discount' || dtl.couponBasicType === 'goods_discount' || dtl.couponBasicType === 'rfm_type'">
              <!-- 折扣券 -->
              <div class="body-container">
                <div class="body-title">{{ i18n('折扣力度') }}</div>
                <div class="body-info">
                  {{ Number(dtl.discountCouponAttribute.discount).toFixed(1) }}
                  {{ i18n('折') }}
                </div>
              </div>
            </el-col>
            <el-col :span="5" class="body-section" v-else-if="dtl.couponBasicType === 'points' && dtl.pointsCouponAttribute">
              <!-- 积分券 -->
              <div class="body-container">
                <div class="body-title">{{ i18n('券可换') }}</div>
                <div class="body-info">
                  {{ dtl.pointsCouponAttribute.pointAmount }}
                  {{ i18n('积分') }}
                </div>
              </div>
            </el-col>
            <el-col :span="5" class="body-section" v-else-if="dtl.couponBasicType === 'special_price' && dtl.specialPriceCouponAttribute">
              <!-- 特价券 -->
              <div class="body-container">
                <div class="body-title">{{ i18n('特价设置') }}</div>
                <div class="body-info" style="white-space: nowrap">
                  {{getSpecialSetup}}
                </div>
              </div>
            </el-col>
            <template v-else-if="dtl.couponBasicType === 'goods' && dtl.pickUpCouponAttribute">
              <!-- 提货券 -->
              <el-col :span="5" class="body-section">
                <div class="body-container">
                  <div class="body-title">{{ i18n('预约提货') }}</div>
                  <div class="body-info">
                    {{ dtl.pickUpCouponAttribute.reserve ? i18n('需要线上预约') : i18n('不需要线上预约') }}
                  </div>
                </div>
              </el-col>
              <el-col :span="5" class="body-section" v-if="dtl.pickUpCouponAttribute.reserve">
                <div class="body-container">
                  <div class="body-title">{{ i18n('提货时间') }}</div>
                  <div class="body-info">
                    <span style="white-space: nowrap">
                      {{ i18n('预约后第') }}
                      {{ dtl.pickUpCouponAttribute.reserveStartDay }}
                      {{ i18n('天至') }}
                      {{ dtl.pickUpCouponAttribute.reserveEndDay }}
                      {{ i18n('天之间到店提货') }}
                    </span>
                  </div>
                </div>
              </el-col>
            </template>
          </template>
        </el-row>
        <div class="top-footer" v-if=" dtl && dtl.remark">
          <FormItem :label="i18n('/储值/预付卡/卡模板/编辑页面/使用须知') + '：'">
            <el-tooltip effect="light" placement="top-start">
              <div slot="content" v-html="xss(dtl.remark.replace(/\n/g, '<br/>'))"></div>
              <div class="overtext" v-html="xss(dtl.remark.replace(/\n/g, '<br/>'))"></div>
            </el-tooltip>
          </FormItem>
        </div>

        <!-- <div class="right">
          <el-row style="margin: 10px 0;">
            <span v-if="dtl && !(dtl.couponBasicType === 'freight')">
              <span style="color: rgba(51, 51, 51, 0.***************)">{{ formatI18n('/营销/券礼包活动/核销第三方券/券码生成规则：') }}</span>
              <span v-if="dtl.codePrefix">{{formatI18n('/营销/券礼包活动/核销第三方券/固定开头')}} {{ dtl.codePrefix }}</span>
              <span v-else>{{ formatI18n('/权益/券/券模板/券码前缀/系统随机生成') }}
              </span><br><br>
            </span>
            <span v-if="dtl.outerRelations !== null">
              <span style="color: rgba(51, 51, 51, 0.***************)">{{ formatI18n('/权益/券/券模板/外部券模板号：') }}</span>
              <span v-if="state === 2">
                <span v-for="(item,index) in tableConfigTicket()" :key="index">
                  <span>{{item}}</span>
                  <span v-if="index !==tableConfigTicket().length-1 ">{{'，'}}</span>
                </span>
              </span>
              <span v-else>
                {{ tableConfigTicket() }}
              </span>
            </span>
          </el-row>
        </div> -->
      </div>
      <div class="gift-set">
        <CouponTemplateDtlSection :deepDialog="true" :data="child" :options="{
                              hideTitle: false,
                              hideState: true,
                              hideFaceAmount: true,
                              hideType: true,
                              hideName: true,
                              hideOuterNumberNamespace: false,
                            }">
        </CouponTemplateDtlSection>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./CouponTemplateDtl.ts">
</script>

<style lang="scss">
.coupon-template-dtl {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .gift-set {
    margin-top: 16px;
  }

  .top-wrap {
    border-radius: 8px;
    background-color: white;
    padding: 24px 24px 0 24px;

    .top-header {
      display: flex;
      .avatar {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 56px;
        height: 56px;
        border-radius: 4px;
        overflow: hidden;
        margin-right: 16px;
        .avatar-img {
          width: 48px;
          height: 48px;
          border-radius: 4px;
        }
        .back {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 56px;
          height: 56px;
          background-color: rgba(242, 242, 242, 1);
          img {
            width: 56px;
            height: 56px;
          }
        }
      }
      .header-info {
        .header-title {
          display: flex;
          align-items: center;
          .coupon-name {
            max-width: 300px;
            height: 28px;
            font-size: 20px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #242633;
            line-height: 28px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
        .state-block {
          display: flex;
          justify-content: center;
          height: 22px;
          border-radius: 4px;
          margin-left: 4px;
          font-size: 14px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #ffffff;
          line-height: 22px;
          padding: 0 4px;
        }
        .coupon-number {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #79879e;
          line-height: 22px;
          margin-top: 8px;
        }
      }
    }

    .top-body {
      display: flex;
      width: 100%;
      height: 80px;
      background: #f7f9fc;
      border-radius: 4px;
      margin-top: 26px;

      .body-section {
        padding: 12px 0 16px 16px;
        &:nth-last-child(1) {
          .body-container {
            border: none !important;
          }
        }

        .body-container {
          border-right: 1px solid #d7dfeb;

          .body-title {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #79879e;
            line-height: 22px;
          }
          .body-info {
            font-size: 18px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #242633;
            line-height: 24px;
            margin-top: 6px;
          }
        }
      }
    }

    .top-footer {
      margin-top: 20px;
      .overtext {
        font-size: 13px;
        line-height: 22px;
        clear: both;
        position: relative;
        top: -30px;
        width: 530px;
      }
    }

    .right {
      margin-top: 13px;
      display: flex;
      flex: 1;
      flex-direction: column;
      position: relative;

      .top {
        padding: 15px 0;
        display: flex;
        border-bottom: 1px solid rgba(242, 242, 242, 1);

        .bill {
          color: rgba(51, 51, 51, 0.***************);
        }

        .name {
          font-weight: 500;
          margin-top: 8px;
          font-size: 20px;
        }

        .desc {
          color: rgba(51, 51, 51, 0.***************);
        }

        .state {
          font-weight: 500;
          margin-top: 8px;
          font-size: 20px;
        }
      }

      .bottom {
        padding-bottom: 20px;

        .account-info {
          margin-top: 10px;
        }

        .red {
          color: red;
        }

        .green {
          color: #008000;
        }
      }
    }
  }
  .qf-form-item .qf-form-label {
    text-align: left !important;
    width: 130px !important;
  }
}
</style>