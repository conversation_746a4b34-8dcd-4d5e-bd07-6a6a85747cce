<template>
  <div class="promotion-show-dialog">
    <el-dialog
      :visible.sync="dialogShow"
      :close-on-click-modal="false"
      :show-close="false"
      width="1082px"
    >
      <template slot="title">
        <div class="header">
          <div class="header-text">
            <span>{{ formatI18n('/公用/券模板详情/选择促销单') }}</span>
            <span class="interpret-gray">{{i18n('只能选择海鼎SPMS系统的促销单，暂不支持外部促销单')}}</span>
          </div>
          <i
            class="el-icon-close ic-back"
            @click.stop="doBack"
          ></i>
        </div>
      </template>
      <div class="dialog-content">
        <div class="content-left">
          <el-tabs
            v-model="activeTab"
            type="card"
            @tab-click="doChangeTab"
          >
            <el-tab-pane
              :label="formatI18n('/公用/券模板/选择')"
              name="table"
            >
              <div class="table-query">
                <div class="query-ul">
                  <div class="li">
                    <div class="li-label">{{ formatI18n('/公用/券模板详情/促销单号') }}</div>
                    <el-input
                      class="li-input"
                      v-model="query.billNumberLikes"
                      :placeholder="formatI18n('/公用/菜单/请输入')"
                    ></el-input>
                  </div>
                  <div class="li">
                    <div class="li-label">{{ formatI18n('/公用/券模板详情/促销主题') }}</div>
                    <el-input
                      class="li-input"
                      v-model="query.topicNameLikes"
                      :placeholder="formatI18n('/公用/菜单/请输入')"
                    ></el-input>
                  </div>
                </div>
                <div class="query-ul">
                  <div class="li">
                    <div class="li-label">{{ formatI18n('/公用/券模板详情/活动描述') }}</div>
                    <el-input
                      class="li-input"
                      v-model="query.activityDescLikes"
                      :placeholder="formatI18n('/公用/菜单/请输入')"
                    ></el-input>
                  </div>
                  <div class="search-btn">
                    <el-button
                      type="primary"
                      @click.stop="doSearch"
                    >{{ formatI18n('/公用/菜单/搜索') }}</el-button>
                    <el-button @click.stop="doReset">{{ formatI18n('/公用/券模板/重置') }}</el-button>
                  </div>
                </div>
              </div>
              <div class="table-content">
                <el-table
                  ref="table"
                  :data="tableData"
                  :header-cell-style="{ background: '#DDE2EB', fontWeight: '600', color: '#020203' }"
                  :row-class-name="tableRowClassName"
                  style="width: 770px;"
                  height="378"
                  :row-key="getRowKey"
                  v-loading="loading"
                  @select="selectCol"
                  @select-all="selectAll"
                >
                  <el-table-column
                    type="selection"
                    width="50"
                    :selectable="selectChange"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="billNumber"
                    :label="formatI18n('/公用/券模板详情/促销单号')"
                    width="140"
                  >
                    <template slot-scope="scope">
                      <el-tooltip
                        effect="light"
                        :content="scope.row.billNumber"
                        placement="top"
                      >
                        <div class="tab-tip">{{ scope.row.billNumber }}</div>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="topicName"
                    :label="formatI18n('/公用/券模板详情/促销主题')"
                    width="140"
                  >
                    <template slot-scope="scope">
                      <el-tooltip
                        effect="light"
                        :content="scope.row.topicName"
                        placement="top"
                      >
                        <div class="tab-tip">{{ scope.row.topicName }}</div>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="activityDesc"
                    :label="formatI18n('/公用/券模板详情/活动描述')"
                    width="160"
                  >
                    <template slot-scope="scope">
                      <el-tooltip
                        effect="light"
                        :content="scope.row.activityDesc"
                        placement="top"
                      >
                        <div class="tab-tip">{{ scope.row.activityDesc }}</div>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="formatI18n('/公用/券模板详情/促销日期范围')"
                    width="180"
                  >
                    <template slot-scope="scope">
                      <span>{{ timeFrame(scope.row) }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="formatI18n('/公用/券模板详情/单据状态')"
                    min-width="80"
                  >
                    <template slot-scope="scope">
                      <div v-if="getStatus(scope.row).name == 'hybrid'">
                        <div class="state-line" v-for="item in getStatus(scope.row).list" :key="item.color">
                          <div
                            class="state-border"
                            :style="{ background: item.color }"
                          ></div>
                          <div>{{ item.name }}</div>
                        </div>
                      </div>
                      <div class="state-line" v-else>
                        <div
                          class="state-border"
                          :style="{ background: getStatus(scope.row).color }"
                        ></div>
                        <div>{{ getStatus(scope.row).name }}</div>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div class="dialog-page">
                <el-pagination
                  small
                  @current-change="handleCurrentChange"
                  :current-page="page.currentPage"
                  :page-size="50"
                  layout="total, prev, pager, next,  jumper"
                  :total="page.total"
                >
                </el-pagination>
              </div>
            </el-tab-pane>
            <el-tab-pane
              :label="formatI18n('/会员/洞察/客群管理/导入建群/文件导入')"
              name="document"
              v-loading="loading"
            >
              <div class="document-content">
                <template v-if="uploadStatus == 'init'">
                  <div class="document-header">
                    <div class="header-title">{{ formatI18n('/公用/券模板详情/添加所需文件并导入至此处：') }}</div>
                    <a @click="downloadTemplate"
                      class="header-download"
                    >
                      <i class="el-icon-download"></i>
                      {{ formatI18n('/公用/券模板详情/下载模板') }}
                    </a>
                  </div>
                  <el-upload
                    ref="upload"
                    class="upload-style"
                    accept=".xls, .xlsx"
                    :headers="uploadHeaders"
                    :action="getUploadUrl"
                    :file-list="fileList"
                    :on-change="doHandleChange"
                    :on-error="getErrorInfo"
                    :on-success="getSuccessInfo"
                    :before-upload="beforeUpload"
                    :show-file-list="false"
                    :with-credentials="true"
                    drag
                  >
                    <i
                      class="el-icon-upload"
                      style="color: #1b7eff;"
                    ></i>
                    <div class="el-upload__text"><em>{{ formatI18n('/公用/券模板详情/点击上传') }}</em>{{
                      formatI18n('/公用/券模板详情/或拖动文件至虚线框内上传') }}
                      <br />
                      {{ formatI18n('/公用/券模板详情/支持xls、xlsx等格式文件') }}
                    </div>
                  </el-upload>
                </template>
                <template v-if="uploadStatus == 'success'">
                  <div class="upload_result">
                    <img
                      class="update_image"
                      src="~assets/image/coupon/img_Import_succeeded.png"
                    >
                    <div class="upload_text">{{ formatI18n('/公用/券模板详情/文件导入成功') }}</div>
                    <!-- <div class="upload_remark">这里显示导入失败的原因这里显示导入失败的原因</div> -->
                    <el-upload
                      class="upload-btn"
                      ref="upload"
                      :headers="uploadHeaders"
                      accept=".xls, .xlsx"
                      :action="getUploadUrl"
                      :file-list="fileList"
                      :on-change="doHandleChange"
                      :on-error="getErrorInfo"
                      :before-upload="beforeUpload"
                      :on-success="getSuccessInfo"
                      :show-file-list="false"
                      :with-credentials="true"
                    >
                      <el-button size="small">{{ formatI18n('/公用/券模板详情/再次导入') }}</el-button>
                    </el-upload>
                  </div>
                </template>
                <template v-if="uploadStatus == 'error'">
                  <div class="upload_result">
                    <img
                      class="update_image"
                      src="~assets/image/coupon/img_Import_failed.png"
                    >
                    <div class="upload_text">{{ formatI18n('/公用/券模板详情/文件导入失败') }}</div>
                    <!-- <div class="upload_remark">这里显示导入失败的原因这里显示导入失败的原因</div> -->
                    <el-upload
                      class="upload-btn"
                      ref="upload"
                      accept=".xls, .xlsx"
                      :headers="uploadHeaders"
                      :action="getUploadUrl"
                      :file-list="fileList"
                      :on-change="doHandleChange"
                      :on-error="getErrorInfo"
                      :before-upload="beforeUpload"
                      :on-success="getSuccessInfo"
                      :with-credentials="true"
                      :show-file-list="false"
                    >
                      <el-button size="small">{{ formatI18n('/公用/券模板详情/重新导入') }}</el-button>
                    </el-upload>
                  </div>
                </template>
              </div>
            </el-tab-pane>
            <el-tab-pane
              :label="formatI18n('/公用/券模板详情/粘贴导入')"
              name="paste"
              v-loading="loading"
            >
              <div
                class="paste-content"
                id="paste"
                @mouseover="mouseOver"
                @mouseleave="mouseLeave"
              >
                <p>{{ formatI18n('/公用/券模板详情/鼠标移入框内可通过ctrl+V进行粘贴导入') }}</p>
                <p style="color: #6C727A;">{{ formatI18n('/公用/券模板详情/一次性最多可复制') }}1000{{ formatI18n('/公用/券模板/微盟适用商品/条') }}
                </p>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
        <div class="content-right">
          <div class="right-header">
            <div class="header-total">{{ formatI18n('/公用/券模板详情/已选中：') + selectedList.length +
              formatI18n('/公用/券模板/微盟适用商品/条') }}</div>
            <span
              class="header-clear"
              @click.stop="doClear"
            >{{ formatI18n('/公用/导入/清空') }}</span>
          </div>
          <div class="right-scroll">
            <div class="ul">
              <div
                class="li"
                v-for="(item, index) in selectedList"
                :key="item.uuid"
              >
                <div class="li-content">[{{ item.billNumber }}]{{ item.topicName }}</div>
                <div
                  class="li-delete"
                  @click.stop="doCancel(index)"
                >
                  <div class="icon-delete"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="right-btn">
            <el-button @click.stop="doBack">{{ formatI18n('/公用/按钮/取消') }}</el-button>
            <el-button
              type="primary"
              @click.stop="doSure"
            >{{ formatI18n('/公用/按钮/确定') }}</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script lang="ts">
import { Vue, Component, Prop, Watch } from "vue-property-decorator";
import PromotionBill from "model/coupon/template/PromotionBill";
import PromotionQueryParams from "model/coupon/template/PromotionQueryParams";
import DateUtil from "util/DateUtil";
import CouponInitialApi from "http/coupon/template/CouponTemplateApi";
import EnvUtil from "util/EnvUtil";
import CommonUtil from "util/CommonUtil";
import I18nPage from "common/I18nDecorator";
import UploadApi from "http/upload/UploadApi";
@Component({
  components: {},
})
@I18nPage({
  prefix: ["/公用/券模板详情"],
  auto: true
})
export default class Index extends Vue {
  @Prop({
    type: Boolean,
    default: false,
  })
  dialogShow: boolean; // 控制模态框的展示
  @Prop({
    default: [],
  })
  promotionList: any;

  uploadHeaders: any = {};
  loading: boolean = false;
  activeTab: string = "table";
  tableData: PromotionBill[] = [];
  selectedList: any[] = [];
  uploadStatus: string = "init";

  page = {
    currentPage: 1,
    total: 0,
  };
  query = new PromotionQueryParams();
  canPaste: boolean = false;
  fileList: any[] = [];
  uploadUuid: string = "";

  get getUploadUrl() {
    return (
      EnvUtil.getServiceUrl() +
      "v1/coupon-template/importTemplateProNum/" +
      this.uploadUuid
    );
  }

  @Watch("dialogShow")
  watchDialogShow(value: boolean) {
    if (value) {
      this.selectedList = JSON.parse(JSON.stringify(this.promotionList));
      this.uploadUuid = CommonUtil.uuid();
      this.query = new PromotionQueryParams();
      this.page.currentPage = 1;
      this.uploadStatus = "init";
      this.queryPromotion();
    } else {
      this.selectedList = [];
    }
  }

  @Watch("tableData", { deep: true })
  watchTableData(value: any) {
    this.checkSelected();
  }

  mounted() {
    this.listenPaste();
    let locale = sessionStorage.getItem("locale");
    this.uploadHeaders = {
      locale: locale ? locale : "zh_CN",
      time_zone: new Date().getTimezoneOffset(),
      marketingCenter: sessionStorage.getItem("marketCenter"),
    };
    const authorization = EnvUtil.getUniAuthorization()
    if (authorization) {
      this.uploadHeaders.authorization = authorization
    }
  }
  handleCurrentChange(val: number) {
    this.page.currentPage = val;
    this.queryPromotion();
  }
  doChangeTab(tab: string) {
    console.log("搜索", this.activeTab);
  }
  doSearch() {
    this.page.currentPage = 1;
    this.queryPromotion();
  }
  doReset() {
    this.query = new PromotionQueryParams();
    this.page.currentPage = 1;
    this.queryPromotion();
  }
  async queryPromotion() {
    this.query.page = this.page.currentPage - 1;
    this.loading = true;
    try {
      const resp: any = await CouponInitialApi.queryPromotion(this.query);
      if (resp.code == 2000 || resp.data) {
        this.page.total = resp.total || 0;
        this.tableData = resp.data || [];
      }
      this.loading = false;
      this.$nextTick(() => {
        // @ts-ignore
        this.$refs.table.bodyWrapper.scrollTop = 0;
      });
    } catch (err) {
      this.loading = false;
    }
  }
  getRowKey(row: any) {
    return row.uuid;
  }

  selectAll(selection: any) {
    if (selection.length > 0) {
      selection.map((row: any, index: number) => {
        const flagIndex = this.selectedList.findIndex(
          (item: any) => item.uuid == row.uuid
        );
        if (flagIndex == -1) {
          if (this.selectedList.length >= 1000) {
            this.cancelSelected(index);
          } else {
            this.selectedList.push({
              billNumber: row.billNumber,
              topicName: row.topicName,
              uuid: row.uuid,
            });
          }
        }
      });
    } else {
      this.tableData.map((row: any) => {
        const flagIndex = this.selectedList.findIndex(
          (item: any) => item.uuid == row.uuid
        );
        if (flagIndex > -1) {
          this.selectedList.splice(flagIndex, 1);
        }
      });
    }
    this.$forceUpdate();
  }

  cancelSelected(index: number) {
    const refTable: any = this.$refs.table;
    this.$nextTick(() => {
      refTable.toggleRowSelection(this.tableData[index], false);
    });
  }

  selectChange(row: any, index: any) {
    const flag = this.selectedList.find((item: any) => item.uuid === row.uuid);
    if (this.selectedList.length >= 4000 && !flag) {
      return false;
    } else {
      return true;
    }
  }

  checkSelected() {
    if (this.tableData.length == 0 || this.selectedList.length == 0) return;
    const refTable: any = this.$refs.table;
    this.tableData.map((item: any, index: number) => {
      const flagIndex = this.selectedList.findIndex(
        (info) => info.uuid == item.uuid
      );
      this.$nextTick(() => {
        refTable.toggleRowSelection(this.tableData[index], flagIndex > -1);
      });
    });
  }

  selectCol(selection: any, row: any) {
    const flag = selection.includes(row);
    const index = this.selectedList.findIndex(
      (item: any) => item.uuid == row.uuid
    );
    if (!flag) {
      this.selectedList.splice(index, 1);
    } else {
      this.selectedList.push({
        billNumber: row.billNumber,
        topicName: row.topicName,
        uuid: row.uuid,
      });
    }
  }

  doCancel(index: number) {
    const flagIndex = this.tableData.findIndex(
      (item) => item.uuid == this.selectedList[index].uuid
    );
    if (flagIndex > -1) {
      const refTable: any = this.$refs.table;
      this.$nextTick(() => {
        refTable.toggleRowSelection(this.tableData[flagIndex], false);
      });
    }
    this.selectedList.splice(index, 1);
  }

  doSure() {
    this.$emit("save", this.selectedList);
  }

  doClear() {
    this.selectedList = [];
    const refTable: any = this.$refs.table;
    this.$nextTick(() => {
      refTable.clearSelection();
    });
  }

  timeFrame(row: PromotionBill) {
    let start = row.start || "";
    let end = row.finish || "";
    const startTime = DateUtil.format(start, "yyyy-MM-dd");
    const endTime = DateUtil.format(end, "yyyy-MM-dd");
    return `${startTime} 至 ${endTime}`;
  }

  getStatus(row: PromotionBill) {
    const state = row.execState;
    if (state == "yet") {
      return {
        name: this.formatI18n("/公用/券模板详情/未开始"),
        color: "#FFAA00",
      };
    } else if (state == "finish") {
      return {
        name: this.formatI18n("/公用/券模板详情/已结束"),
        color: "#A1B0C8",
      };
    } else if (state == "sleeping") {
      return {
        name: this.formatI18n("/公用/券模板详情/休眠中"),
        color: "#A1B0C8",
      };
    } else if (state == "hybrid") {
      return {
        name: "hybrid",
        color: "#58B929",
        list: [
          {
            name: this.formatI18n("/公用/券模板详情/执行中"),
            color: "#58B929",
          },
          {
            name: this.formatI18n("/公用/券模板详情/休眠中"),
            color: "#A1B0C8",
          },
        ],
      };
    } else {
      return {
        name: this.formatI18n("/公用/券模板详情/执行中"),
        color: "#58B929",
      };
    }
  }

  tableRowClassName(row: any) {
    const { rowIndex } = row;
    if (rowIndex % 2 == 0) {
      return "black-row";
    } else {
      return "normal-row";
    }
  }

  listenPaste() {
    document.addEventListener("paste", async (event) => {
      // @ts-ignore
      const text = (event.clipboardData || window.clipboardData).getData(
        "text"
      );
      if (!this.canPaste) return;
      if (this.selectedList.length >= 1000) {
        let str: any = this.formatI18n("/公用/券模板详情/选择促销单最多{0}条");
        str = str.replace(/\{0\}/g, `0`);
        this.$message.warning(str);
        return;
      }
      let pasteTextList = text
        .split("\n")
        .map((val: any) => val.split("\r")[0]?.trim())
        .filter((item: any) => item);
      pasteTextList = Array.from(new Set(pasteTextList));
      if (pasteTextList.length > 1000) {
        pasteTextList.splice(1000);
      }
      const body = new PromotionQueryParams();
      body.billNumberIn = pasteTextList;
      body.page = 0;
      body.pageSize = pasteTextList.length;
      this.loading = true;
      try {
        const resp: any = await CouponInitialApi.queryPromotion(body);
        if (resp.code == 2000 && resp.data) {
          const list = (resp.data || []).reduce((acc: any, cur: any) => {
            acc.push({
              billNumber: cur.billNumber,
              topicName: cur.topicName,
              uuid: cur.uuid,
            });
            return acc;
          }, []);
          let selectedList = this.selectedList.concat(list);
          const map = new Map();
          const newArr = selectedList.filter(
            (v) => !map.has(v.uuid) && map.set(v.uuid, 1)
          );
          if (newArr.length > 1000) {
            newArr.splice(1000);
          }
          this.selectedList = newArr;
          this.checkSelected();
        }
        this.loading = false;
      } catch (err) {
        this.loading = false;
      }
    });
  }

  mouseOver() {
    this.canPaste = true;
  }

  mouseLeave() {
    this.canPaste = false;
  }

  doBack() {
    this.$emit("dialogClose");
  }

  getErrorInfo(a: any, b: any, c: any) {
    this.$message.error(
      this.formatI18n("/公用/导入", "导入失败，请重新导入") as string
    );
    // @ts-ignore
    this.$refs.upload.clearFiles();
    this.uploadStatus = "error";
  }
  doHandleChange(file: any, fileList: any) {
    if (fileList.length > 0) {
      // this.fileCount++;
      this.fileList = [fileList[fileList.length - 1]];
    }
  }
  getSuccessInfo(a: any, b: any, c: any) {
    if (a && a.code === 2000) {
      // @ts-ignore
      this.$refs.upload.clearFiles();
      this.getUploadResult(a.data);
    } else {
      this.$message.error(
        a.msg
          .replace("java.lang.Exception:", "")
          .replace("com.hd123.phoenix.crm.api.CrmException:", "")
          .trimEnd()
      );
      // @ts-ignore
      this.$refs.upload.clearFiles();
      this.uploadStatus = "error";
    }
  }
  beforeUpload() {
    if (this.selectedList.length >= 1000) {
      let str: any = this.formatI18n("/公用/券模板详情/选择促销单最多{0}条");
      str = str.replace(/\{0\}/g, `0`);
      this.$message.warning(str);
      return false;
    }
  }
  uploadResult: number = 0;
  getUploadResult(uuid: string) {
    this.loading = true;
    CouponInitialApi.getUploadResult(uuid)
      .then((resp) => {
        console.log(resp);
        if (resp.code == 2000 && resp.data == "success") {
          this.uploadResult = 0;
          this.queryImport();
        } else if (resp.code == 2000 && resp.data == "fail") {
          this.uploadResult = 0;
          this.uploadStatus = "error";
          this.loading = false;
          this.queryImport();
        } else {
          if (this.uploadResult < 10) {
            setTimeout(() => {
              this.uploadResult++;
              this.getUploadResult(uuid);
            }, 1000);
          } else {
            this.loading = false;
            this.uploadResult = 0;
            this.uploadStatus = "error";
          }
        }
      })
      .catch((err) => {
        this.$message.error(err.msg);
      });
  }
  async queryImport() {
    try {
      const resp: any = await CouponInitialApi.queryImport(this.uploadUuid);
      if (resp.code == 2000 && resp.data) {
        const list = (resp.data || []).reduce((acc: any, cur: any) => {
          acc.push({
            billNumber: cur.billNumber,
            topicName: cur.topicName,
            uuid: cur.uuid,
          });
          return acc;
        }, []);
        if(resp.data?.length) {
          this.uploadStatus = "success";
        }
        let selectedList = this.selectedList.concat(list);
        const map = new Map();
        const newArr = selectedList.filter(
          (v) => !map.has(v.uuid) && map.set(v.uuid, 1)
        );
        if (newArr.length > 1000) {
          newArr.splice(1000);
        }
        this.selectedList = newArr || [];
        this.checkSelected();
        this.uploadUuid = CommonUtil.uuid();
      } else {
        this.$message.error(resp.msg);
      }
      this.loading = false;
    } catch (err) {
      this.$message.error((err as any).message);
      this.loading = false;
    }
  }

  downloadTemplate() {
    let templatePath = 'template_coupon_template_pronums.xlsx'
    UploadApi.getUrl(templatePath).then((resp: any) => {
      if (resp && resp.data) {
        window.open(resp.data);
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
}
</script>
<style lang="scss" scoped>
.promotion-show-dialog {
  .header {
    padding-top: 10px;
    display: flex;
    justify-content: space-between;
    align-content: center;

    .header-text {
      flex: 1;
      height: 22px;
      font-size: 14px;
      font-weight: 600;
      color: #020203;
      line-height: 22px;
      .interpret-gray {
        font-size: 12px;
        color: #8a9099;
        font-weight: 500;
        margin-left: 10px;
      }
    }

    .ic-back {
      color: #8a9099;
      font-size: 16px;
      font-weight: 700;
      line-height: 22px;
      cursor: pointer;
    }
  }

  .dialog-content {
    width: 100%;
    display: flex;
    justify-content: space-between;

    .content-left {
      flex: 1;
      height: 544px;
      padding-right: 12px;

      .table-query {
        .query-ul {
          display: flex;
          justify-content: flex-start;
          align-items: center;

          &:last-child {
            margin-top: 8px;
          }

          .li {
            width: 272px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-right: 12px;

            .li-label {
              width: 96px;
              height: 28px;
              padding-right: 16px;
              font-size: 12px;
              color: #36445a;
              line-height: 28px;
              text-align: right;
            }

            .li-input {
              flex: 1;
              height: 28px;
            }
          }
          .search-btn {
            width: 272px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin: 10px 0 0 95px;
          }
        }
      }

      .table-content {
        margin-top: 12px;
        width: 100%;

        .tab-tip {
          width: 100%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          -o-text-overflow: ellipsis;
        }

        .state-line {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .state-border {
          display: block;
          width: 6px;
          height: 6px;
          margin-right: 8px;
          border-radius: 50%;
        }
      }

      .document-content {
        margin-top: 5px;

        .document-header {
          width: 372px;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .header-title {
            width: 180px;
            height: 18px;
            font-size: 13px;
            color: #020203;
            line-height: 18px;
          }

          .header-download {
            width: 70px;
            height: 18px;
            font-size: 12px;
            color: #1597ff;
            line-height: 18px;
            text-align: right;
            cursor: pointer;
          }
        }

        .upload-style {
          width: 372px;
          margin-top: 12px;
        }

        .upload_result {
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          align-items: center;
          margin-top: 80px;

          .update_image {
            width: 96px;
            height: 96px;
          }

          .upload_text {
            width: 100%;
            height: 22px;
            font-size: 14px;
            font-weight: 600;
            color: #42464d;
            line-height: 22px;
            text-align: center;
          }

          .upload_remark {
            width: 100%;
            height: 18px;
            font-size: 12px;
            color: #6c727a;
            line-height: 18px;
            text-align: center;
            margin-top: 8px;
          }

          .upload-btn {
            margin-top: 16px;
          }
        }
      }

      .paste-content {
        width: 630px;
        height: 280px;
        border-radius: 2px;
        border: 1px solid #dde2eb;
        display: flex;
        justify-content: center;
        flex-direction: column;
        align-items: center;
        font-size: 12px;
        color: #020203;
        line-height: 18px;
        cursor: pointer;
      }
    }

    .content-right {
      width: 250px;
      height: 544px;
      padding-left: 10px;
      padding-top: 3px;
      position: relative;

      &::after {
        content: "";
        width: 1px;
        height: 500px;
        border-left: 1px solid #dde2eb;
        position: absolute;
        top: 0;
        left: 0;
      }

      .right-header {
        width: 100%;
        padding-bottom: 12px;
        display: flex;
        justify-content: space-between;

        .header-total {
          flex: 1;
          height: 18px;
          font-size: 12px;
          color: #020203;
          line-height: 18px;
        }

        .header-clear {
          width: 30px;
          height: 18px;
          font-size: 12px;
          color: #1597ff;
          line-height: 18px;
          text-align: right;
          cursor: pointer;
        }
      }

      .right-scroll {
        width: 240px;
        height: 467px;
        overflow-x: hidden;
        overflow-y: auto;

        &::-webkit-scrollbar {
          display: none;
        }

        .ul > .li {
          width: 240px;
          padding: 0 12px;
          height: 32px;
          background: #f2f4f8;
          border-radius: 2px;
          margin-bottom: 4px;
          display: flex;
          justify-content: space-between;
          align-items: center;

          &:last-child {
            margin-bottom: 0;
          }

          .li-content {
            width: 210px;
            height: 18px;
            font-size: 12px;
            color: #020203;
            line-height: 18px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }

          .li-delete {
            width: 16px;
            height: 16px;
            background: #ffffff;
            border: 1px solid #dde2eb;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;

            .icon-delete {
              width: 9px;
              height: 1px;
              background: #1597ff;
              border-radius: 2px;
            }
          }
        }
      }

      .right-btn {
        display: flex;
        justify-content: flex-end;
        margin-top: 28px;
      }
    }
  }

  .dialog-page {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-content: center;
    margin-top: 6px;
  }
}

::v-deep.el-dialog__body {
  padding-top: 12px;
}

::v-deep.black-row {
  height: 28px;
  background: #f4f6fa;
  font-size: 12px;
  color: #020203;
  line-height: 18px;
  cursor: pointer;
}

::v-deep.normal-row {
  height: 28px;
  font-size: 12px;
  color: #020203;
  line-height: 18px;
  cursor: pointer;
}

::v-deep.el-tabs__item.is-active {
  font-weight: 600;
  color: #020203;
  background: #ffffff;
}

::v-deep.el-tabs__item {
  width: 102px;
  height: 28px;
  background: #f4f6fa;
  font-size: 14px;
  color: #42464d;
  line-height: 28px;
  text-align: center;
}

::v-deep.el-upload-dragger {
  width: 372px;
}
</style>