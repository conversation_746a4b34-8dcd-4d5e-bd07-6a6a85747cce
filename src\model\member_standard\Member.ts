/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2023-09-20 09:57:59
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\member_standard\Member.ts
 * 记得注释
 */
import Channel from 'model/common/Channel'
import IdName from 'model/common/IdName'
import MemberIdent from 'model/common/member/MemberIdent'

export default class Member extends MemberIdent {
  // 姓名
  name: Nullable<string> = null
  // 姓(配置开启时传入)
  lastName: Nullable<string> = null
  // 等级代码
  gradeCode: Nullable<string> = null
  // 等级名称
  gradeName: Nullable<string> = null
  // 状态 取值：Using-使用中；Blocked-已冻结；Unactivated-未激活；Canceled-已注销
  state: Nullable<string> = null
  // 归属门店
  ownStore: Nullable<IdName> = null
  // 注册日期
  registerTime: Nullable<Date> = null
  // 激活日期
  activateTime: Nullable<Date> = null
  // 创建日期
  created: Nullable<Date> = null
  // 注册渠道
  registerChannel: Nullable<Channel> = null
  // 招募方式
  registerScene: Nullable<string> = null
  // 招募方式国际化
  registerSceneI18n: Nullable<string> = null
  // 性别
  gender: Nullable<string> = null
  // 性别国际化
  genderI18n: Nullable<string> = null
  // 生日
  birthday: Nullable<string> = null
}