import ActivityBody from 'model/common/ActivityBody'
import UpgradeLine from 'model/grade/upgradegift/UpgradeLine'
import ActivityDateTimeCondition from "model/common/ActivityDateTimeCondition";
import DateTimeCondition from "model/common/DateTimeCondition";

export default class UpGradeGiftActivity {
  // 活动主体
  body: Nullable<ActivityBody> = new ActivityBody()
  // 活动规则明细
  lines: UpgradeLine[] = []
  // 营销中心
  marketingCenter: Nullable<string> = null
  	// 活动时间和时间限制
	activityDateTimeCondition = new ActivityDateTimeCondition();
  	// 活动时间限制
	dateTimeCondition = new DateTimeCondition();
}