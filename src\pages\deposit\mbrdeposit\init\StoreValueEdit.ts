import { error } from 'console';
import { Component, Vue, Watch } from 'vue-property-decorator'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import PrePayConfigApi from 'http/prepay/config/PrePayConfigApi'
import ConstantMgr from 'mgr/ConstantMgr'
import I18nPage from "common/I18nDecorator";
import AmountToFixUtil from 'util/AmountToFixUtil'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import RichText from 'cmp/rich-text/RichText';
import FaceAmount from 'model/prepay/config/FaceAmount';

@Component({
  name: 'StoreValueEdit',
  components: {
    SubHeader,
    BreadCrume,
    RichText
  }
})
@I18nPage({
  prefix: ['/储值/会员储值/充值面额设置/编辑页面', '/储值/会员储值/充值面额设置/详情页面', '/公用/按钮']
})
export default class StoreValueEdit extends Vue {
  i18n: any
  $refs: any
  uuid: Nullable<string> = null
  dynamicValidateForm = {
    domains: [{
      value: ''
    }],
    customPrice: {
      isShowEntry: false,  //是否展示充值金额入口
      amount: null  //充值基数
    },
    remark: '',  //充值须知
    agreement: '', //充值协议
    appletDefaultDepositAmount: null as Nullable<number>, //默认充值金额
  }
  faceAmountRule = {
    required: true, validator: ((rule: any, value: any, callback: any) => {
      if (value === '') {
        callback(new Error(this.formatI18n('/储值/会员储值/充值面额设置/编辑页面/面额不能为空')));
      } else {
        if (Number(value) < 0 || Number(value) > 99999999) {
          callback(new Error(this.formatI18n('/储值/会员储值/充值面额设置/编辑页面/面额取值范围0~99999999')));
        }
        callback();
      }
    }), trigger: 'blur'
  }
  customPriceRule = {
    required: true,
    validator: ((rule: any, value: any, callback: any) => {
      if (value.isShowEntry && !value.amount) {
        callback(new Error(this.formatI18n('/公用/券模板/请输入必填项')));
      }
      callback()
    }),
    trigger: ['blur', 'change']
  }
  panelArray: any = []

  get filterDynamicValidateFormDomains(){
    return  this.dynamicValidateForm.domains.filter((item)=>item.value)
  }

  created() {
    this.panelArray = [
      {
        name: this.formatI18n('/公用/菜单/充值面额设置'),
        url: ''
      }
    ]
    if (this.$route.query.from === 'edit') {
      this.getDtl()
    }
  }

  doValueChange(index: number) {
    this.dynamicValidateForm.domains[index].value = AmountToFixUtil.formatAmount(this.dynamicValidateForm.domains[index].value, 99999999, 0.01, '')
    this.doResetAppletDefaultDepositAmount();
  }

  doResetAppletDefaultDepositAmount(){
      // 如果上方充值面额发生变动
      const appletDefaultDepositAmount = this.dynamicValidateForm?.appletDefaultDepositAmount;
      const find = this.dynamicValidateForm.domains.find((item)=>{
      let flag = false
          try{
            if(appletDefaultDepositAmount && parseFloat(item.value)?.toFixed(2) ==parseFloat(appletDefaultDepositAmount.toString())?.toFixed(2) ){
              flag = true
            }
          }catch(error){
            // 
          }
        return flag;
      })
      if(!find){
        //  c端默认充值面额重置为第一项
        this.dynamicValidateForm.appletDefaultDepositAmount =  this.dynamicValidateForm.domains[0].value as any
      }
  }

  doSave() {
    this.$refs['dynamicValidateForm'].validate((valid: any) => {
      if (valid) {
        this.doSaveFaceAmount()
      } else {
        return false;
      }
    });
  }

  doRemoveDomain(item: any) {
    if (this.dynamicValidateForm.domains && this.dynamicValidateForm.domains.length === 1) {
      return
    }
    let index = this.dynamicValidateForm.domains.indexOf(item)
    if (index !== -1) {
      this.dynamicValidateForm.domains.splice(index, 1)
    }
    this.doResetAppletDefaultDepositAmount();
  }

  doAddDomain() {
    if (this.dynamicValidateForm.domains.length === 10) {
      this.$message.warning(this.formatI18n('/储值/会员储值/充值面额设置/编辑页面/最多只能设置10个面额'))
      return
    }
    this.dynamicValidateForm.domains.push({
      value: ''
    });
    this.doResetAppletDefaultDepositAmount();
  }

  doBack() {
    this.$router.back()
  }

  getDtl() {
    const loading = this.$loading(ConstantMgr.loadingOption)
    PrePayConfigApi.getConfig().then((res) => {
      if (res.code === 2000 && res.data) {
        this.bindValue(res.data)
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    }).finally(() => {
      loading.close()
    })
  }

  bindValue(body: FaceAmount) {
    this.dynamicValidateForm.domains = []
    if (!body.faceAmountList?.length) {
      this.dynamicValidateForm.domains.push({
        value: ''
      })
    } else {
      body.faceAmountList.forEach((item: any) => {
        this.dynamicValidateForm.domains.push({
          value: item
        })
      })

      if(body.appletDefaultDepositAmount==null || body.appletDefaultDepositAmount==undefined){
        this.dynamicValidateForm.appletDefaultDepositAmount = this.dynamicValidateForm.domains[0].value as any
      }else{
        this.dynamicValidateForm.appletDefaultDepositAmount = body.appletDefaultDepositAmount
      }
    }
    this.dynamicValidateForm.customPrice.isShowEntry = body.showCustomizedAmount || false
    this.dynamicValidateForm.customPrice.amount = body.customizedBase as any
    this.dynamicValidateForm.remark = body.depositNotice || ''
    this.dynamicValidateForm.agreement = body.depositAgreement || ''
    this.uuid = body.uuid
  }

  private doSaveFaceAmount() {
    const params = new FaceAmount()
    let faceAmount: number[] = []
    if (this.dynamicValidateForm.domains && this.dynamicValidateForm.domains.length > 0) {
      this.dynamicValidateForm.domains.forEach((item: any) => {
        faceAmount.push(item.value)
      })
    }
    params.faceAmountList = faceAmount
    params.uuid = this.uuid
    params.depositNotice = this.dynamicValidateForm.remark
    params.depositAgreement = this.dynamicValidateForm.agreement === '<p><br></p>' ? null : this.dynamicValidateForm.agreement
    params.showCustomizedAmount = this.dynamicValidateForm.customPrice.isShowEntry
    params.customizedBase = this.dynamicValidateForm.customPrice.amount
    params.appletDefaultDepositAmount = this.dynamicValidateForm.appletDefaultDepositAmount
    const loading = this.$loading(ConstantMgr.loadingOption)
    PrePayConfigApi.saveConfig(params).then((resp: any) => {
      if (resp) {
        this.$message.success(this.formatI18n('/公用/js提示信息/保存成功'))
        this.$router.back()
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    }).finally(() => {
      loading.close()
    })
  }

  clearAmount() {
    this.dynamicValidateForm.customPrice.amount = null
  }
}
