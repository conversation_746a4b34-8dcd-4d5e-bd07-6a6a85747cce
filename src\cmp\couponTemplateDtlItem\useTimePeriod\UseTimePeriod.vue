<!--
 * @Author: 黎钰龙
 * @Date: 2023-07-05 11:04:16
 * @LastEditTime: 2023-07-05 14:34:08
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\couponTemplateDtlItem\useTimePeriod\UseTimePeriod.vue
 * 记得注释
-->
<template>
  <div>
    <div style="line-height: 36px" v-if="data && data.coupons && data.coupons.useTimeRange && data.coupons.useTimeRange.dateTimeRangeType === 'ALL'">
      {{ formatI18n("/公用/券模板", "全部时段") }}
    </div>
    <div v-if="data && data.coupons && data.coupons.useTimeRange && data.coupons.useTimeRange.dateTimeRangeType === 'USEABLE'">
      <div style="margin:5px 0 6px">
        {{ formatI18n("/公用/券模板", "指定时段适用") }}
      </div>
      <div v-if="data && data.coupons && data.coupons.useTimeRange.cycleType === 'DAYS'">
        <div v-html="getDayTime(data.coupons.useTimeRange.beginTime, data.coupons.useTimeRange.endTime)">
        </div>
      </div>
      <div v-if="data && data.coupons && data.coupons.useTimeRange.cycleType === 'MONTHS'">
        <div v-html="getMonthTime(data.coupons.useTimeRange.days, data.coupons.useTimeRange.beginTime, data.coupons.useTimeRange.endTime)">
        </div>
      </div>
      <div v-if="data && data.coupons && data.coupons.useTimeRange.cycleType === 'WEEKS'">
        <div v-html="getWeekTime(data.coupons.useTimeRange.days, data.coupons.useTimeRange.beginTime, data.coupons.useTimeRange.endTime)">
        </div>
      </div>
    </div>
    <div v-if="data && data.coupons && data.coupons.useTimeRange && data.coupons.useTimeRange.dateTimeRangeType === 'UNUSEABLE'">
      <div style="margin:5px 0 6px">
        {{ formatI18n("/公用/券模板", "指定时段不适用") }}
      </div>
      <div v-if="data && data.coupons && data.coupons.useTimeRange.cycleType === 'DAYS'">
        <div v-html="getDayTime(data.coupons.useTimeRange.beginTime, data.coupons.useTimeRange.endTime)">
        </div>
      </div>
      <div v-if="data && data.coupons && data.coupons.useTimeRange.cycleType === 'MONTHS'">
        <div v-html="getMonthTime(data.coupons.useTimeRange.days, data.coupons.useTimeRange.beginTime, data.coupons.useTimeRange.endTime)">
        </div>
      </div>
      <div v-if="data && data.coupons && data.coupons.useTimeRange.cycleType === 'WEEKS'">
        <div v-html="getWeekTime(data.coupons.useTimeRange.days, data.coupons.useTimeRange.beginTime, data.coupons.useTimeRange.endTime)">
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./UseTimePeriod.ts">
</script>

<style lang='scss' scoped>
</style>