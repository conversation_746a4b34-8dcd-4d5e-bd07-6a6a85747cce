import {Component, Prop, Vue, Watch} from 'vue-property-decorator'
import ImportDialog from 'cmp/importdialog/ImportDialog.vue'
import ImportResultDialog from 'pages/deposit/mbrdeposit/active/dialog/ImportResultDialog.vue'
import GoodsSelectorDialog from 'cmp/selectordialogs/GoodsSelectorDialog.vue'
import RSGoods from 'model/common/RSGoods'
import BrandSelectorDialog from 'cmp/selectordialogs/BrandSelectorDialog.vue'
import CatogorySelectorDialog from 'cmp/selectordialogs/CatogorySelectorDialog.vue'
import RSBrand from 'model/common/RSBrand'
import RSCategory from 'model/common/RSCategory'
import IdName from 'model/common/IdName'
import GoodsRangeModel from 'model/common/GoodsRange'

@Component({
    name: 'GoodsRange',
    components: {
        ImportDialog,
        ImportResultDialog,
        GoodsSelectorDialog,
        BrandSelectorDialog,
        CatogorySelectorDialog
    }
})
export default class GoodsRange extends Vue {
    pre: any = ''
    importResultDialogClose = false
    importResultData: any = {}
    goodsData: any = []
    selectType: any = []
    recordSelectType: any = []
    recordBrands: any = []
    recordIndex = 0
    recordName = ''
    goods = {
        includeGoods: [],
        excludeGoods: [],
        includeBrands: [],
        excludeBrands: [],
        includeCategories: [],
        excludeCategories: []
    }
    importUrl = 'v1/goods/importExcel'
    dialogShow = false
    importDialogShow = false
    dynamicValidateForm: any = {}
    $refs: any

    @Prop()
    value: GoodsRangeModel
    @Prop({
        default: true,
        type: Boolean
    })
    showDesc: boolean

    @Watch('value')
    onValueChange(value: GoodsRangeModel) {
        // 有值
        if (!(value && value.includeBrands.length === 0 && value.excludeBrands.length === 0 && value.includeGoods.length === 0 && value.excludeGoods.length === 0 && value.includeCategories.length === 0 && value.excludeCategories.length === 0)) {
            this.setBindValue(value)
            if (JSON.stringify(this.pre) !== JSON.stringify(value)) {
                this.$emit('input', this.doTransParams())
                this.$emit('change')
            }
            this.pre = value
        } else { // 无值
            this.goods = {
                includeGoods: [],
                excludeGoods: [],
                includeBrands: [],
                excludeBrands: [],
                includeCategories: [],
                excludeCategories: []
            }
            if (!this.dynamicValidateForm && this.dynamicValidateForm.domains.length > 0) {
                this.dynamicValidateForm = {
                    domains: [{
                        value: '',
                        brand: this.formatI18n('/公用/券模板', '品类'),
                        belong: this.formatI18n('/公用/券模板', '属于'),
                        goods: []
                    }],

                }
            }
        }
    }
    get templatePath() {
        if (location.href.indexOf('localhost') === -1) {
            return 'template_specify_goods.xlsx'
        } else {
            return 'template_specify_goods.xlsx'
        }
    }
    created() {
        this.selectType = [this.formatI18n('/公用/券模板', '品类'), this.formatI18n('/公用/券模板', '品牌'), this.formatI18n('/公用/券模板', '单品')]
        this.recordSelectType = [this.formatI18n('/公用/券模板', '品类'), this.formatI18n('/公用/券模板', '品牌'), this.formatI18n('/公用/券模板', '单品')]
        this.recordBrands = [this.formatI18n('/公用/券模板', '品类') as string, '', '']
        this.dynamicValidateForm = {
            domains: [{
                value: '',
                brand: this.formatI18n('/公用/券模板', '品类'),
                belong: this.formatI18n('/公用/券模板', '属于'),
                goods: []
            }],

        }
        if (!(this.value && this.value.includeBrands.length === 0 && this.value.excludeBrands.length === 0 && this.value.includeGoods.length === 0 && this.value.excludeGoods.length === 0 && this.value.includeCategories.length === 0 && this.value.excludeCategories.length === 0)) {
            this.setBindValue(this.value)
            if (JSON.stringify(this.pre) !== JSON.stringify(this.value)) {
                this.$emit('input', this.doTransParams())
                this.$emit('change')
            }
            this.pre = this.value
        }
    }
    mounted() {
        this.setBindValue('')
    }
    doValidateForm() {
        if (this.dynamicValidateForm && this.dynamicValidateForm.domains && this.dynamicValidateForm.domains.length > 0) {
            let count = 0
            this.dynamicValidateForm.domains.forEach((item: any) => {
                if (!item.value) {
                    count++
                }
            })
            if (count > 0) {
                return false
            } else {
                return true
            }
        } else {
            return false
        }
    }
    doBelongChange(index: number, type: string, belong: string) {
        if (type === this.formatI18n('/公用/券模板', '品牌')) {
            if (belong === this.formatI18n('/公用/券模板', '属于')) {
                this.goods.includeBrands = this.goods.excludeBrands
                this.goods.excludeBrands = []
            } else {
                this.goods.excludeBrands = this.goods.includeBrands
                this.goods.includeBrands = []
            }
        } else if (type === this.formatI18n('/公用/券模板', '品类')) {
            if (belong === this.formatI18n('/公用/券模板', '属于')) {
                this.goods.includeCategories = this.goods.excludeCategories
                this.goods.excludeCategories = []
            } else {
                this.goods.excludeCategories = this.goods.includeCategories
                this.goods.includeCategories = []
            }
        } else {
            if (belong === this.formatI18n('/公用/券模板', '属于')) {
                this.goods.includeGoods = this.goods.excludeGoods
                this.goods.excludeGoods = []
            } else {
                this.goods.excludeGoods = this.goods.includeGoods
                this.goods.includeGoods = []
            }
        }
        this.$emit('input', this.doTransParams())
        this.$emit('change')
    }
    doSubmitGoods(arr: RSGoods[]) {
        this.doSummit(arr, this.formatI18n('/公用/券模板', '单品'))
    }
    doSubmitBrand(arr: RSBrand[]) {
        this.doSummit(arr, this.formatI18n('/公用/券模板', '品牌'))
    }
    doSubmitCatogory(arr: RSCategory[]) {
        this.doSummit(arr, this.formatI18n('/公用/券模板', '品类'))
    }
    doClearDomain(index: number) {
        this.dynamicValidateForm.domains[index].value = ''
    }
    doTypeChange(index: number, type: string) {
        this.dynamicValidateForm.domains[index].value = ''
        this.dynamicValidateForm.domains[index].goods = []
        let newArr = JSON.parse(JSON.stringify(this.recordSelectType))
        for (let i = 0; i < this.dynamicValidateForm.domains.length; i++) {
            for (let j = 0; j < newArr.length; j++) {
                if (this.dynamicValidateForm.domains[i].brand === newArr[j]) {
                    newArr.splice(j, 1)
                }
            }
        }
        this.selectType = newArr
        this.$emit('input', this.doTransParams())
        this.$emit('change')
    }
    doAddDomain(index: number, type: string) {
        if (this.dynamicValidateForm.domains.length >= 3) {
            return
        }
        let newArr = JSON.parse(JSON.stringify(this.recordSelectType))
        for (let i = 0; i < this.dynamicValidateForm.domains.length; i++) {
            for (let j = 0; j < newArr.length; j++) {
                if (this.dynamicValidateForm.domains[i].brand === newArr[j]) {
                    newArr.splice(j, 1)
                }
            }
        }
        let brand = newArr[0]
        this.dynamicValidateForm.domains.push({
            value: '',
            brand: brand,
            belong: this.formatI18n('/公用/券模板', '属于'),
            goods: []
        });
        // todo
        for (let i = 0; i < this.dynamicValidateForm.domains.length; i++) {
            for (let j = 0; j < this.selectType.length; j++) {
                if (this.dynamicValidateForm.domains[i].brand === this.selectType[j]) {
                    this.selectType.splice(j, 1)
                }
            }
        }
    }
    doDeleteDomain(index: number) {
        if (this.dynamicValidateForm.domains.length === 1) {
            return
        }
        this.selectType.push(this.dynamicValidateForm.domains[index].brand)
        this.dynamicValidateForm.domains.splice(index, 1)
        this.goods = {
            includeGoods: [],
            excludeGoods: [],
            includeBrands: [],
            excludeBrands: [],
            includeCategories: [],
            excludeCategories: []
        }
        this.dynamicValidateForm.domains.forEach((item: any, index: number) => {
            if (item.brand === this.formatI18n('/公用/券模板', '品牌')) {
                if (item.belong === this.formatI18n('/公用/券模板', '属于')) {
                    let curArr: any = []
                    let arr = this.dynamicValidateForm.domains[index].value.split(';')
                    for (let i = 0; i < arr.length; i++) {
                        let arrSplit = arr[i].split('[')
                        if (arrSplit[0] && arrSplit[1]) {
                            let obj = {
                                id: arrSplit[0],
                                name: arrSplit[1].split(']')[0]
                            }
                            curArr.push(obj)
                        }
                    }

                    this.goods.includeBrands = curArr
                } else {
                    let curArr: any = []
                    let arr = this.dynamicValidateForm.domains[index].value.split(';')
                    for (let i = 0; i < arr.length; i++) {
                        let arrSplit = arr[i].split('[')
                        if (arrSplit[0] && arrSplit[1]) {
                            let obj = {
                                id: arrSplit[0],
                                name: arrSplit[1].split(']')[0]
                            }
                            curArr.push(obj)
                        }
                    }
                    this.goods.excludeBrands = curArr
                }
            }
            if (item.brand === this.formatI18n('/公用/券模板', '品类')) {
                if (item.belong === this.formatI18n('/公用/券模板', '属于')) {
                    let curArr: any = []
                    let arr = this.dynamicValidateForm.domains[index].value.split(';')
                    for (let i = 0; i < arr.length; i++) {
                        let arrSplit = arr[i].split('[')
                        if (arrSplit[0] && arrSplit[1]) {
                            let obj = {
                                id: arrSplit[0],
                                name: arrSplit[1].split(']')[0]
                            }
                            curArr.push(obj)
                        }
                    }
                    this.goods.includeCategories = curArr
                } else {
                    let curArr: any = []
                    let arr = this.dynamicValidateForm.domains[index].value.split(';')
                    for (let i = 0; i < arr.length; i++) {
                        let arrSplit = arr[i].split('[')
                        if (arrSplit[0] && arrSplit[1]) {
                            let obj = {
                                id: arrSplit[0],
                                name: arrSplit[1].split(']')[0]
                            }
                            curArr.push(obj)
                        }
                    }
                    this.goods.excludeCategories = curArr
                }
            }
            if (item.brand === this.formatI18n('/公用/券模板', '单品')) {
                if (item.belong === this.formatI18n('/公用/券模板', '属于')) {
                    let curArr: any = []
                    let arr = this.dynamicValidateForm.domains[index].value.split(';')
                    for (let i = 0; i < arr.length; i++) {
                        let arrSplit = arr[i].split('[')
                        if (arrSplit[0] && arrSplit[1]) {
                            let obj = {
                                id: arrSplit[0],
                                name: arrSplit[1].split(']')[0]
                            }
                            curArr.push(obj)
                        }
                    }
                    this.goods.includeGoods = curArr
                } else {
                    let curArr: any = []
                    let arr = this.dynamicValidateForm.domains[index].value.split(';')
                    for (let i = 0; i < arr.length; i++) {
                        let arrSplit = arr[i].split('[')
                        if (arrSplit[0] && arrSplit[1]) {
                            let obj = {
                                id: arrSplit[0],
                                name: arrSplit[1].split(']')[0]
                            }
                            curArr.push(obj)
                        }
                    }
                    this.goods.excludeGoods = curArr
                }
            }
        })
        this.$emit('input', this.doTransParams())
        this.$emit('change')
    }
    doImport() {
        this.importDialogShow = true
    }
    doImportDialogClose() {
        this.importDialogShow = false
    }
    doImportResultDialogClose() {
        this.importResultDialogClose = false
    }
    doUploadSuccess(value: any) {
        if (value.response.code === 2000) {
            this.importResultDialogClose = true
            this.importResultData = {
                importResult : value.response.data.success, // 导入结果
                backUrl:  value.response.data.backUrl,
                errorCount: value.response.data.errorCount,
                ignoreCount: value.response.data.ignoreCount,
                successCount: value.response.data.successCount
            }
            this.setBindValue(value.response.data)
            this.$emit('input', this.doTransParams())
            this.$emit('change')
        } else {
            this.$message.error(value.response.msg)
        }
    }
    getPlaceholder(value: string) {
        if (value === this.formatI18n('/公用/券模板', '品牌')) {
            return this.formatI18n('/公用/券模板', '请点击选择品牌')
        } else if (value === this.formatI18n('/公用/券模板', '品类')) {
            return this.formatI18n('/公用/券模板', '请点击选择品类')
        } else {
            return this.formatI18n('/公用/券模板', '请点击选择单品')
        }
    }
    doFocus(index: number, name: string) {
        this.recordIndex = index
        this.recordName = name
        // todo 处理数据 如果是编辑
        this.goodsData = []
        if (this.dynamicValidateForm.domains[index].value &&
          this.dynamicValidateForm.domains[index].value.length > 0) {
            // 0000[3231313];1111[3231313];
            let arr = this.dynamicValidateForm.domains[index].value.split(';')
            for (let i = 0; i < arr.length; i++) {
                if (arr[i]) {
                    let obj: any= {}
                    if (this.dynamicValidateForm.domains[index].brand === this.formatI18n('/公用/券模板', '单品')) {
                        obj = new RSGoods()
                        if (arr[i].split('[') && arr[i].split('[')[1]) {
                            let subArr = arr[i].split('[')[1].split(']')[0]
                            obj.name = subArr
                        }
                        obj.barcode =  arr[i].split('[')[0]
                    } else if (this.dynamicValidateForm.domains[index].brand === this.formatI18n('/公用/券模板', '品牌')) {
                        obj = new RSBrand()
                        obj.brand = new IdName()
                        obj.brand.id = arr[i].split('[')[0]
                        if (arr[i].split('[') && arr[i].split('[')[1]) {
                            let subArr = arr[i].split('[')[1].split(']')[0]
                            obj.brand.name = subArr
                        }
                    } else {
                        obj = new RSCategory()
                        obj.category = new IdName()
                        obj.category.id = arr[i].split('[')[0]
                        if (arr[i].split('[') && arr[i].split('[')[1]) {
                            let subArr = arr[i].split('[')[1].split(']')[0]
                            obj.category.name = subArr
                        }
                    }
                    this.goodsData.push(obj)
                }
            }
        }
        if (this.dynamicValidateForm.domains[index].brand === this.formatI18n('/公用/券模板', '单品')) {
            this.$refs.selectGoodsScopeDialog.open(this.goodsData, 'multiple')
        } else if (this.dynamicValidateForm.domains[index].brand === this.formatI18n('/公用/券模板', '品牌')) {
            this.$refs.selectBrandScopeDialog.open(this.goodsData, 'multiple')
        } else {
            this.$refs.selectCatogoryScopeDialog.open(this.goodsData, 'multiple')
        }
    }
    doDialogClose() {
        this.dialogShow = false
    }
    doSummit(arr: any, type: string) {
        this.goodsData = []
        this.goods = {
            includeGoods: [],
            excludeGoods: [],
            includeBrands: [],
            excludeBrands: [],
            includeCategories: [],
            excludeCategories: []
        }
        let str = ''
        if (arr && arr.length > 0) {
            arr.forEach((item: any) => {
                if (item) {
                    if (type === this.formatI18n('/公用/券模板', '品牌') && item.brand && item.brand.id) {
                        str += item.brand.id + `[${item.brand.name}];`
                    } else if (type === this.formatI18n('/公用/券模板', '单品') && item.barcode) {
                        str += item.barcode + `[${item.name}];`
                    } else if (type === this.formatI18n('/公用/券模板', '品类') && item.category && item.category.id) {
                        str += item.category.id + `[${item.category.name}];`
                    }
                }
            })
        }
        // 显示界面的值
        this.dynamicValidateForm.domains[this.recordIndex].value = str
        // 记录选择的单品、品类、品牌
        this.dynamicValidateForm.domains[this.recordIndex].goods = arr
        // 处理回调出去的参数
        this.dynamicValidateForm.domains.forEach((item: any) => {
            if (item.brand === this.formatI18n('/公用/券模板', '品牌')) {
                if (item.belong === this.formatI18n('/公用/券模板', '属于')) {
                    let curArr: any = []
                    if (item.goods && item.goods.length > 0) {
                        item.goods.forEach((subItem: any) => {
                            if (subItem && subItem.brand && subItem.brand.id) {
                                let obj = {
                                    name: subItem.brand.name,
                                    id: subItem.brand.id
                                }
                                curArr.push(obj)
                            }
                        })
                    }
                    this.goods.includeBrands = curArr
                } else {
                    let curArr: any = []
                    if (item.goods && item.goods.length > 0) {
                        item.goods.forEach((subItem: any) => {
                            if (subItem && subItem.brand && subItem.brand.id) {
                                let obj = {
                                    name: subItem.brand.name,
                                    id: subItem.brand.id
                                }
                                curArr.push(obj)
                            }
                        })
                    }
                    this.goods.excludeBrands = curArr
                }
            }
            if (item.brand === this.formatI18n('/公用/券模板', '品类')) {
                if (item.belong === this.formatI18n('/公用/券模板', '属于')) {
                    let curArr: any = []
                    if (item.goods && item.goods.length > 0) {
                        item.goods.forEach((subItem: any) => {
                            if (subItem && subItem.category && subItem.category.id) {
                                let obj = {
                                    name: subItem.category.name,
                                    id: subItem.category.id
                                }
                                curArr.push(obj)
                            }
                        })
                    }
                    this.goods.includeCategories = curArr
                } else {
                    let curArr: any = []
                    if (item.goods && item.goods.length > 0) {
                        item.goods.forEach((subItem: any) => {
                            if (subItem && subItem.category && subItem.category.id) {
                                let obj = {
                                    name: subItem.category.name,
                                    id: subItem.category.id
                                }
                                curArr.push(obj)
                            }
                        })
                    }
                    this.goods.excludeCategories = curArr
                }
            }
            if (item.brand === this.formatI18n('/公用/券模板', '单品')) {
                if (item.belong === this.formatI18n('/公用/券模板', '属于')) {
                    let curArr: any = []
                    if (item.goods && item.goods.length > 0) {
                        item.goods.forEach((subItem: any) => {
                            if (subItem && subItem.barcode) {
                                let obj = {
                                    name: subItem.name,
                                    id: subItem.barcode
                                }
                                curArr.push(obj)
                            }
                        })
                    }
                    this.goods.includeGoods = curArr
                } else {
                    let curArr: any = []
                    if (item.goods && item.goods.length > 0) {
                        item.goods.forEach((subItem: any) => {
                            if (subItem && subItem.barcode) {
                                let obj = {
                                    name: subItem.name,
                                    id: subItem.barcode
                                }
                                curArr.push(obj)
                            }
                        })
                    }
                    this.goods.excludeGoods = curArr
                }
            }
        })
        this.$emit('input', this.doTransParams())
        this.$emit('change')
        this.$refs['dynamicValidateForm'].validate((valid: any) => {
            // todo
        })
    }
    private doTransParams() {
        let params: GoodsRangeModel = new GoodsRangeModel()
        params.limit = true
        params.excludePrecondition = false
        params.includeGoods =  this.goods.includeGoods
        params.includeCategories = this.goods.includeCategories
        params.includeBrands = this.goods.includeBrands
        params.excludeCategories = this.goods.excludeCategories
        params.excludeBrands = this.goods.excludeBrands
        params.excludeGoods = this.goods.excludeGoods
        return params
    }
    private setBindValue(data: any) {
        if (data) {
            this.dynamicValidateForm = {
                domains: [{
                    value: '',
                    brand: this.formatI18n('/公用/券模板', '品类'),
                    belong: this.formatI18n('/公用/券模板', '属于'),
                    goods: []
                }]
            }
            this.goods = {
                includeGoods: [],
                excludeGoods: [],
                includeBrands: [],
                excludeBrands: [],
                includeCategories: [],
                excludeCategories: []
            }
            if (data.includeGoods && data.includeGoods.length > 0) {
                this.goods.includeGoods = data.includeGoods
                let str = ''
                let oArr: any = []
                data.includeGoods.forEach((item: any) => {
                    str += item.id + '[' + item.name + '];'
                    oArr.push({
                        name: item.name,
                        barcode: item.id
                    })
                })
                if (this.dynamicValidateForm.domains && this.dynamicValidateForm.domains.length > 0) {
                    let count = 0
                    this.dynamicValidateForm.domains.forEach((subItem: any) => {
                        if (subItem.brand !== this.formatI18n('/公用/券模板', '单品')) {
                            count++
                        }
                    })
                    if (count === this.dynamicValidateForm.domains.length) {
                        this.dynamicValidateForm.domains.push(
                          {
                              value: '',
                              brand: this.formatI18n('/公用/券模板', '单品'),
                              belong: this.formatI18n('/公用/券模板', '属于'),
                              goods: oArr
                          }
                        )
                    }
                    this.dynamicValidateForm.domains.forEach((subItem: any) => {
                        if (subItem.brand === this.formatI18n('/公用/券模板', '单品') && subItem.belong === this.formatI18n('/公用/券模板', '属于')) {
                            this.$set(subItem, 'value', str)
                        }
                    })
                }
            }
            if (data.excludeGoods && data.excludeGoods.length > 0) {
                this.goods.excludeGoods = data.excludeGoods
                let str = ''
                let oArr: any = []
                data.excludeGoods.forEach((item: any) => {
                    str += item.id + '[' + item.name + '];'
                    oArr.push({
                        name: item.name,
                        barcode: item.id
                    })
                })
                if (this.dynamicValidateForm.domains && this.dynamicValidateForm.domains.length > 0) {
                    let count = 0
                    this.dynamicValidateForm.domains.forEach((subItem: any) => {
                        if (subItem.brand !== this.formatI18n('/公用/券模板', '单品')) {
                            count++
                        }
                    })
                    if (count === this.dynamicValidateForm.domains.length) {
                        this.dynamicValidateForm.domains.push(
                          {
                              value: '',
                              brand: this.formatI18n('/公用/券模板', '单品'),
                              belong: this.formatI18n('/公用/券模板', '不属于'),
                              goods: oArr
                          }
                        )
                    }
                    this.dynamicValidateForm.domains.forEach((subItem: any) => {
                        if (subItem.brand === this.formatI18n('/公用/券模板', '单品') && subItem.belong === this.formatI18n('/公用/券模板', '不属于')) {
                            this.$set(subItem, 'value', str)
                        }
                    })
                }
            }
            if (data.includeBrands && data.includeBrands.length > 0) {
                this.goods.includeBrands = data.includeBrands
                let str = ''
                let oArr: any = []
                data.includeBrands.forEach((item: any) => {
                    str += item.id + '[' + item.name + '];'
                    oArr.push({
                        brand: {
                            id: item.id,
                            name: item.name
                        }
                    })
                })
                if (this.dynamicValidateForm.domains && this.dynamicValidateForm.domains.length > 0) {
                    let count = 0
                    this.dynamicValidateForm.domains.forEach((subItem: any) => {
                        if (subItem.brand !== this.formatI18n('/公用/券模板', '品牌')) {
                            count++
                        } else {
                            subItem.goods = oArr
                        }
                    })
                    if (count === this.dynamicValidateForm.domains.length) {
                        this.dynamicValidateForm.domains.push(
                          {
                              value: '',
                              brand: this.formatI18n('/公用/券模板', '品牌'),
                              belong: this.formatI18n('/公用/券模板', '属于'),
                              goods: oArr
                          }
                        )
                    }
                    this.dynamicValidateForm.domains.forEach((subItem: any) => {
                        if (subItem.brand === this.formatI18n('/公用/券模板', '品牌') && subItem.belong === this.formatI18n('/公用/券模板', '属于')) {
                            this.$set(subItem, 'value', str)
                        }
                    })
                }
            }
            if (data.excludeBrands && data.excludeBrands.length > 0) {
                this.goods.excludeBrands = data.excludeBrands
                let str = ''
                let oArr: any = []
                data.excludeBrands.forEach((item: any) => {
                    str += item.id + '[' + item.name + '];'
                    oArr.push({
                        brand: {
                            id: item.id,
                            name: item.name
                        }
                    })
                })
                if (this.dynamicValidateForm.domains && this.dynamicValidateForm.domains.length > 0) {
                    let count = 0
                    this.dynamicValidateForm.domains.forEach((subItem: any) => {
                        if (subItem.brand !== this.formatI18n('/公用/券模板', '品牌')) {
                            count++
                        }
                        if (subItem.brand === this.formatI18n('/公用/券模板', '品牌')) {
                            subItem.belong = this.formatI18n('/公用/券模板', '不属于')
                        }
                    })
                    if (count === this.dynamicValidateForm.domains.length) {
                        this.dynamicValidateForm.domains.push(
                          {
                              value: '',
                              brand: this.formatI18n('/公用/券模板', '品牌'),
                              belong: this.formatI18n('/公用/券模板', '不属于'),
                              goods: oArr
                          }
                        )
                    }
                    this.dynamicValidateForm.domains.forEach((subItem: any) => {
                        if (subItem.brand === this.formatI18n('/公用/券模板', '品牌')) {
                            this.$set(subItem, 'value', str)
                        }
                    })
                }
            }
            if (data.includeCategories && data.includeCategories.length > 0) {
                this.goods.includeCategories = data.includeCategories
                let str = ''
                let oArr: any = []
                data.includeCategories.forEach((item: any) => {
                    str += item.id + '[' + item.name + '];'
                    oArr.push({
                        category: {
                            id: item.id,
                            name: item.name
                        }
                    })
                })
                if (this.dynamicValidateForm.domains && this.dynamicValidateForm.domains.length > 0) {
                    let count = 0
                    this.dynamicValidateForm.domains.forEach((subItem: any) => {
                        if (subItem.brand !== this.formatI18n('/公用/券模板', '品类')) {
                            count++
                        }
                    })
                    if (count === this.dynamicValidateForm.domains.length) {
                        this.dynamicValidateForm.domains.push(
                          {
                              value: '',
                              brand: this.formatI18n('/公用/券模板', '品类'),
                              belong: this.formatI18n('/公用/券模板', '属于'),
                              goods: oArr
                          }
                        )
                    }
                    this.dynamicValidateForm.domains.forEach((subItem: any) => {
                        if (subItem.brand === this.formatI18n('/公用/券模板', '品类') && subItem.belong === this.formatI18n('/公用/券模板', '属于')) {
                            this.$set(subItem, 'value', str)
                        }
                    })
                }
            }
            if (data.excludeCategories && data.excludeCategories.length > 0) {
                this.goods.excludeCategories = data.excludeCategories
                let str = ''
                let oArr: any = []
                data.excludeCategories.forEach((item: any) => {
                    str += item.id + '[' + item.name + '];'
                    oArr.push({
                        category: {
                            id: item.id,
                            name: item.name
                        }
                    })
                })
                if (this.dynamicValidateForm.domains && this.dynamicValidateForm.domains.length > 0) {
                    let count = 0
                    this.dynamicValidateForm.domains.forEach((subItem: any) => {
                        if (subItem.brand !== this.formatI18n('/公用/券模板', '品类')) {
                            count++
                        }
                    })
                    if (count === this.dynamicValidateForm.domains.length) {
                        this.dynamicValidateForm.domains.push(
                          {
                              value: '',
                              brand: this.formatI18n('/公用/券模板', '品类'),
                              belong: this.formatI18n('/公用/券模板', '不属于'),
                              goods: oArr
                          }
                        )
                    }
                    this.dynamicValidateForm.domains.forEach((subItem: any) => {
                        if (subItem.brand === this.formatI18n('/公用/券模板', '品类') && subItem.belong === this.formatI18n('/公用/券模板', '不属于')) {
                            this.$set(subItem, 'value', str)
                        }
                    })
                }
            }
            if (this.dynamicValidateForm.domains && this.dynamicValidateForm.domains.length > 0) {
                let newArray: any = []
                this.dynamicValidateForm.domains.forEach((item: any) => {
                    if (item.value) {
                        newArray.push(item)
                    }
                })
                this.dynamicValidateForm.domains = newArray
                if (this.dynamicValidateForm.domains.length === 0) {
                    this.dynamicValidateForm = {
                        domains: [{
                            value: '',
                            brand: this.formatI18n('/公用/券模板', '品牌'),
                            belong: this.formatI18n('/公用/券模板', '属于'),
                            goods: []
                        }]
                    }
                }
            }
        } else {
            this.goods = {
                includeGoods: [],
                excludeGoods: [],
                includeBrands: [],
                excludeBrands: [],
                includeCategories: [],
                excludeCategories: []
            }
            this.dynamicValidateForm = {
                domains: [{
                    value: '',
                    brand: this.formatI18n('/公用/券模板', '品类'),
                    belong: this.formatI18n('/公用/券模板', '属于'),
                    goods: []
                }],

            }
        }
        // 处理selectType
        this.setSelectType()
        // this.$emit('input', this.doTransParams())
        // this.$emit('change')
    }
    private setSelectType() {
        let record = JSON.parse(JSON.stringify(this.recordSelectType))
        let b = this.dynamicValidateForm.domains
        let a = record
        for(let i=0; i<b.length; i++) {
            for(let j=0; j<a.length; j++) {
                if(a[j] === b[i].brand) {
                    a.splice(j,1)
                    j = j - 1
                }
            }
        }
        this.selectType = a
    }
    private validate() {
        let p0 = new Promise((resolve) => {
            this.$refs.dynamicValidateForm.validate((valid: any) => {
                if (valid) {
                    resolve()
                }
            })
        })
        return p0
    }
}
