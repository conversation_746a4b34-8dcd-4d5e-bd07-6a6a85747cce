/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-09-12 13:45:57
 * @LastEditTime: 2024-09-18 20:33:03
 * @LastEditors: fang<PERSON><PERSON><PERSON>
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\precisionmarketing\userGroup\UserGroupV2Api.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import Response from 'model/default/Response'
import Member from 'model/member/Member'
import UserGroupMemberFIter from 'model/precisionmarketing/userGroup/UserGroupMemberFIter'
import UserGroupV2 from 'model/precisionmarketing/userGroup/UserGroupV2'
import UserGroupV2Filter from 'model/precisionmarketing/userGroup/UserGroupV2Filter'
import UserGroupV2Import from 'model/precisionmarketing/userGroup/UserGroupV2Import'
import UserGroupV2ModifyRequest from 'model/precisionmarketing/userGroup/UserGroupV2ModifyRequest'

export default class UserGroupV2Api {
  /**
   * 查询客群详情信息
   * 
   */
  static get(uuid: string): Promise<Response<UserGroupV2>> {
    return ApiClient.server().get(`/v2/precision-marketing/user-group/get/${uuid}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入客群信息
   * 
   */
  static importOrUpdate(body: UserGroupV2Import): Promise<Response<string>> {
    return ApiClient.server().post(`/v2/precision-marketing/user-group/importOrUpdate`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改导入客群信息
   * 
   */
  static modify(body: UserGroupV2ModifyRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v2/precision-marketing/user-group/modify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 会员列表信息查询
   * 会员列表信息查询。
   * 
   */
  static queryGroupMember(body: UserGroupMemberFIter): Promise<Response<Member[]>> {
    return ApiClient.server().post(`/v2/precision-marketing/user-group/member/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询客群列表
   * 
   */
  static query(body: UserGroupV2Filter): Promise<Response<UserGroupV2[]>> {
    return ApiClient.server().post(`/v2/precision-marketing/user-group/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
