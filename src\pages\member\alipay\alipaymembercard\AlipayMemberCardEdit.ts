import {Component, Vue} from 'vue-property-decorator'
import WechatHeader from 'cmp/wechatheader/WechatHeader.vue'
import WeixinCard from 'model/weixin/card/WeixinCard'
import WxEntry from 'model/weixin/card/WxEntry'
import ImgUpload from 'pages/member/alipay/alipaymembercard/cmp/ImgUpload.vue'
import InitialCardInfo from 'pages/member/alipay/alipaymembercard/cmp/InitialCardInfo.vue'
import MbrCardTitle from 'pages/member/alipay/alipaymembercard/cmp/MbrCardTitle.vue'
import ModifyConfirm from 'pages/member/alipay/alipaymembercard/dialog/ModifyConfirm.vue'
import ConstantMgr from 'mgr/ConstantMgr'
import AlipayInitApi from 'http/aliPay/v2/AlipayInitApi'
import AliPassCardTemplate from 'model/alipay/v2/AliPassCardTemplate'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import SpecialTpl from "pages/member/entities/SpecialTpl";
import I18nPage from 'common/I18nDecorator'

@Component({
  name: 'AlipayMemberCardEdit',
  components: {
    WechatHeader,
    ImgUpload,
    InitialCardInfo,
    MbrCardTitle,
    ModifyConfirm,
    BreadCrume
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板'
  ],
  auto: true
})
export default class AlipayMemberCardEdit extends Vue {
  saveFlag = ''
  codeViewType = false
  isEdit = false
  title = ''
  $refs: any
  messageTpl: any = ''
  customEnter = []
  recordMemberRights = []
  merchantName = ''
  mbrCardTitle = ''
  cardDtl: any = ''
  cardInfo: any = ''
  merchangeLogo = '' // 商户logo
  mbrCardLogo = ''
  memberCard: any = false // 会员卡信息
  wechatCard: WeixinCard = new WeixinCard()
  grade: any[] = []
  gradeRadioSelected: number[] = []
  gradeRadioImgs: string[] = []
  entry: WxEntry = new WxEntry()
  memberRights = []
  codeType = 'CODE_TYPE_QRCODE'
  selected: number[] = []
  active = 1
  ruleForm = {
    benefitRemark: ''
  }
  panelArray:any = []
  rules: any = {}
  created() {
    this.rules = {
      benefitRemark: [
        {required: true, message: this.formatI18n('/公用/券模板', '请输入必填项'), trigger: 'blur'}
      ]
    }
    if (this.$route.query.from === 'edit') {
      this.isEdit = true
      this.title = '支付宝会员卡'
    } else {
      this.isEdit = false
      this.title = '支付宝会员初始化'
    }
    this.panelArray = [
      {
        name: this.i18n('支付宝会员设置'),
        url: 'ali-member-setting'
      },
      {
        name: this.title,
        url: ""
      }
    ]
  }
  mounted() {
    this.getMemberCard()
  }

  // 获取会员卡信息
  getMemberCard() {
    const loading = this.$loading(ConstantMgr.loadingOption)
    AlipayInitApi.getTemplate().then((res: any) => {
      if (res.code === 2000) {
        loading.close()
        if (!res.data) {
          this.memberCard = false
        } else {
          this.memberCard = res.data
        }
        // 商户名称
        this.merchantName = res.data.merchantName
        // 商户logo
        this.merchangeLogo = res.data.logo
        // 会员卡卡样
        this.mbrCardLogo = res.data.background
        // 会员卡标题
        this.mbrCardTitle = res.data.title
        // 会员权益
        this.memberRights = res.data.equityType
        this.recordMemberRights = res.data.memberRights
        this.ruleForm.benefitRemark = res.data.equityRemark
        // 会员卡详情
        let obj: SpecialTpl = new SpecialTpl()
        obj.notice = res.data.remark
        obj.remark = res.data.notice
        this.cardDtl =obj
        // 识别方式
        this.codeType = res.data.mbrCodeType === 1 ? 'CODE_TYPE_BARCODE' : 'CODE_TYPE_QRCODE'
        this.codeViewType = res.data.showQrCode
        // 开卡填写信息
        let cardInfoStr = res.data.mbrAttribute
        if (cardInfoStr) {
          let newInfo = JSON.parse(cardInfoStr)
          this.cardInfo = {
            optionalInfo: [],
            requiredInfo: []
          }
          newInfo.forEach((item: any) => {
            if (item.value === 1) { // 必填
              let obj = {
                cardChecked: true,
                cardName: this.doGetNameByCode(item.key),
                cardCode: item.key,
                cardSelected: 1
              }
              this.cardInfo.requiredInfo.push(obj)
            } else {
              let obj = {
                cardChecked: true,
                cardName: this.doGetNameByCode(item.key),
                cardCode: item.key,
                cardSelected: 2
              }
              this.cardInfo.optionalInfo.push(obj)
            }
          })
        }
      } else {
        this.memberCard = false
      }
    }).catch((error: any) => {
      this.cardInfo = {
        requiredInfo: [{
          cardChecked: true,
          cardName:this.formatI18n('/会员/会员资料/手机号'),
          cardCode: 'OPEN_FORM_FIELD_MOBILE',
          cardSelected: 1
        }],
        optionalInfo: [
          {
            cardChecked: true,
            cardName: this.formatI18n('/会员/会员资料/性别'),
            cardCode: 'OPEN_FORM_FIELD_GENDER',
            cardSelected: 2
          },
          {
            cardChecked: true,
            cardName: this.formatI18n('/会员/会员资料/姓名'),
            cardCode: 'OPEN_FORM_FIELD_NAME',
            cardSelected: 2
          },
          {
            cardChecked: true,
            cardName: this.formatI18n('/会员/会员资料/生日'),
            cardCode: 'OPEN_FORM_FIELD_BIRTHDAY_WITH_YEAR',
            cardSelected: 2
          }
        ]
      }
      loading.close()
    })
  }
  doGetNameByCode(code: string) {
    let obj: any = {
      OPEN_FORM_FIELD_MOBILE: this.formatI18n('/会员/会员资料/手机号'),
      OPEN_FORM_FIELD_GENDER: this.formatI18n('/会员/会员资料/性别'),
      OPEN_FORM_FIELD_NAME: this.formatI18n('/会员/会员资料/姓名'),
      OPEN_FORM_FIELD_NICKNAME: this.formatI18n('/会员/会员资料/昵称'),
      OPEN_FORM_FIELD_BIRTHDAY_WITH_YEAR: this.formatI18n('/会员/会员资料/生日'),
      OPEN_FORM_FIELD_ADDRESS: this.formatI18n('/会员/会员资料/地址'),
      OPEN_FORM_FIELD_CITY: '城市',
      OPEN_FORM_FIELD_EMAIL: this.formatI18n('/会员/会员资料/邮箱'),
      OPEN_FORM_FIELD_IS_STUDENT: '是否学生认证'
    }
    return obj[code]
  }
  getDisabled(type: string) {
    if (this.recordMemberRights && (this.recordMemberRights as any).indexOf(type) !== -1 && this.memberCard) {
      return true
    } else {
      return false
    }
  }
  typeFilter(type: string) {
    if (type === 'FREE') {
      return this.formatI18n('/营销/积分活动/积分活动/商品满额积分加倍积分活动/编辑页面/复制规则弹窗/免费等级')
    } else if (type === 'PAID') {
      return this.formatI18n('/营销/积分活动/积分活动/商品满额积分加倍积分活动/编辑页面/复制规则弹窗/付费等级')
    } else if (type === 'SPECIAL') {
      return this.formatI18n('/营销/积分活动/积分活动/商品满额积分加倍积分活动/编辑页面/复制规则弹窗/特殊等级')
    }
  }
  doRuleDtl() {
    window.open('https://mp.weixin.qq.com/cgi-bin/readtemplate?t=cardticket/card_cover_tmpl&type=info&lang=zh_CN', '_blank')
  }

  // 取消
  toWechatMemberCard() {
    this.$confirm('确定放弃页面信息修改并离开此页吗？', '离开当前页', {
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    }).then(() => {
      this.$router.back()
    })
  }

  // 上一步
  toWechatAuthorizeAfter() {
    this.$confirm('确定放弃页面信息修改并离开此页吗？', '离开当前页', {
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    }).then(() => {
      this.$router.push({ name: 'ali-member-setting' })
    })
  }

  // 保存(跳转)
  goWechatMemberCard() {
    this.saveFlag = 'save'
    this.doValidate()
  }

  // 下一步
  toWechatMemberCode() {
    this.saveFlag = 'next'
    this.doValidate()
  }
  doConfirm() {
    this.goWechatMemberCard()
  }
  doCancel() {
    this.$router.push({ name: 'alipay-member-qr-code' })
  }
  private doValidate() {
    let p: any[] = []
    if (this.$refs.mbrCardTitle) {
      p[0] = new Promise<void>((resolve, reject) => {
        this.$refs.mbrCardTitle.$refs.ruleForm.validate((valid: any) => {
          if (valid) {
            resolve()
          }
        })
      })
    }
    if (this.$refs.ruleForm) {
      p[1] = new Promise<void>((resolve, reject) => {
        this.$refs.ruleForm.validate((valid: any) => {
          if (valid) {
            resolve()
          }
        })
      })
    }
    Promise.all(p).then(() => {
      // todo 需要判断是否创建过会员卡，创建过调用update，没创建过调用create
      if (!this.merchangeLogo) {
        this.$message.warning(this.formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/商户logo不上传点击保存js提示信息/请设置商户logo'))
        return
      }
      if (!this.mbrCardLogo) {
        this.$message.warning('请设置会员卡封面')
        return
      }
      const loading = this.$loading(ConstantMgr.loadingOption)
      if (this.transParams()) {
        if (this.memberCard) { // 修改
          AlipayInitApi.modifyTemplate(this.transParams() as any).then((res: any) => {
            if (res.code === 2000) {
              loading.close()
              if (this.saveFlag === 'save') {
                if (this.$route.query.from === 'edit') {
                  this.$message.success('支付宝会员卡修改成功！')
                  this.$router.push({ name: 'alipay-member-card-edit-dtl' })
                } else {
                  this.$message.success('支付宝会员卡修改成功！')
                  this.getMemberCard()
                }
              } else {
                this.$message.success('支付宝会员卡修改成功！')
                this.$router.push({ name: 'alipay-member-qr-code' })
              }
            } else {
              loading.close()
              this.$message.error(res.msg)
            }
          }).catch((error: any) => {
            loading.close()
            this.$message.error(error.message)
          })
        } else { // 新建
          AlipayInitApi.createTemplate(this.transParams() as any).then((res: any) => {
            if (res.code === 2000) {
              loading.close()
              if (this.saveFlag === 'save') {
                this.$message.success('支付宝会员卡创建成功')
                this.getMemberCard()
              } else {
                this.$message.success('支付宝会员卡创建成功')
                this.$router.push({ name: 'alipay-member-qr-code' })
              }
            } else {
              loading.close()
              this.$message.error(res.msg)
            }
          }).catch((error: any) => {
            loading.close()
            this.$message.error(error.message)
          })
        }
      }
    })
  }
  private transParams() {
    let wechatCard: AliPassCardTemplate = new AliPassCardTemplate()
    // 会员卡标题
    wechatCard.title = this.mbrCardTitle
    // 商户logo
    wechatCard.logo = this.merchangeLogo
    // 会员卡卡样
    wechatCard.background = this.mbrCardLogo
    // 识别方式
    if (this.codeType === 'CODE_TYPE_QRCODE') {
      wechatCard.mbrCodeType = 0
    } else {
      wechatCard.mbrCodeType = 1
    }
    if (this.memberCard) {
      wechatCard.innerId = this.memberCard.innerId
      wechatCard.alipassTemplateid = this.memberCard.alipassTemplateid
    }
    // 会员权益
    wechatCard.equityType = []
    wechatCard.equityType = this.memberRights
    wechatCard.showMbrfee = false
    wechatCard.showEquityRemark = true
    // 会员权益说明
    wechatCard.equityRemark = this.ruleForm.benefitRemark
    // 开卡信息
    let arr: any = []
    if (this.cardInfo && this.cardInfo.requiredInfo && this.cardInfo.requiredInfo.length > 0) {
      if (this.cardInfo && this.cardInfo.requiredInfo && this.cardInfo.requiredInfo.length > 0) {
        this.cardInfo.requiredInfo.forEach((item: any) => {
          let obj: any = {
            key: item.cardCode,
            value: 1
          }
          arr.push(obj)
        })
      }
    }
    if (this.cardInfo && this.cardInfo.optionalInfo && this.cardInfo.optionalInfo.length > 0) {
      this.cardInfo.optionalInfo.forEach((item: any) => {
        let obj: any = {
          key: item.cardCode,
          value:0
        }
        arr.push(obj)
      })
    }
    wechatCard.mbrAttribute = JSON.stringify(arr)
    return wechatCard
  }
}