/*
 * @Author: haiding <EMAIL>
 * @Date: 2025-01-10 10:23:47
 * @LastEditors: haiding <EMAIL>
 * @LastEditTime: 2025-01-10 18:02:21
 * @FilePath: \new\src\model\analysis\BRechargeableCardAnalysisReportGraphic.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import AnalysisChartData from 'model/analysis/AnalysisChartData'
import { AnalysisReportDateUnit } from 'model/analysis/AnalysisReportDateUnit'

// 储值卡分析图像
export default class BRechargeableCardAnalysisReportGraphic {
  // 门店
  occurredOrgId: Nullable<string> = null
  // 模板
  templateNumber: Nullable<string> = null
  // 报表分析单位，决定BCardAnalysisReportGraphicData坐标取值是 周、月、日
  dateUnit: Nullable<AnalysisReportDateUnit> = null
  // 买卡数据线
  buyCard: AnalysisChartData[] = []
  // 消费
  pay: AnalysisChartData[] = []
  // 充值
  deposit: AnalysisChartData[] = []
  // 转入
  transferIn: AnalysisChartData[] = []
  // 转出数据线
  transferOut: AnalysisChartData[] = []
  // 调整加
  adjustAdd: AnalysisChartData[] = []
  // 调整减
  adjustSub: AnalysisChartData[] = []
  // 作废
  cancel: AnalysisChartData[] = []


  // 可用余额
  usableTotalAmount: AnalysisChartData[] = []
  // 可用本金
  usableAmount: AnalysisChartData[] = []
  // 可用赠金
  usableGiftAmount: AnalysisChartData[] = []

}