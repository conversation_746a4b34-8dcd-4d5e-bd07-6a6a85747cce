import ApiClient from 'http/ApiClient'
import GrowthValueConfig from 'model/grade/growthvalue/GrowthValueConfig'
import Response from 'model/common/Response'

export default class GrowthValueApi {
  /**
   * 保存成长值规则
   *
   */
  static get(): Promise<Response<GrowthValueConfig>> {
    return ApiClient.server().post(`/v1/growthvaluerule/get`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 保存成长值规则
   *
   */
  static save(body: GrowthValueConfig): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/growthvaluerule/save`, body, {}).then((res) => {
      return res.data
    })
  }

}
