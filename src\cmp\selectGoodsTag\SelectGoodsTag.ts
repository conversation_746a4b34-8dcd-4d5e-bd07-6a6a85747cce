/*
 * @Author: 黎钰龙
 * @Date: 2024-04-26 10:38:58
 * @LastEditTime: 2024-04-26 16:14:32
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\selectGoodsTag\SelectGoodsTag.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import GoodsApi from 'http/goods/GoodsApi';
import IdName from 'model/common/IdName';
import GoodsTagFilter from 'model/goods/GoodsTagFilter'

import { Component, Model, Prop, Vue, Watch } from 'vue-property-decorator';
@Component({
  name: 'SelectGoodsTag',
  components: {}
})
@I18nPage({
  prefix: [
    '/公用/下拉框/提示'
  ],
  auto: true
})
export default class SelectGoodsTag extends Vue {
  @Model('change') selectTags: Nullable<IdName[]> | Nullable<string[]>
  @Prop({ type: String, default: '400px' }) width: string;
  @Prop() placeholder: any;
  @Prop({ type: Boolean, default: true }) isOnlyId: boolean; //是否只绑定id
  @Prop({ type: Boolean, default: false }) disabled: boolean;
  @Prop({ default: () => { return {} } }) appendAttr: any; //需要追加的参数
  selectLoading: boolean = false
  tags: IdName[] = []
  originTags: IdName[] = [] //原标签列表

  @Watch('tagsValue', { deep: true })
  handle(value: any) {
    if (!value) {
      this.getTags('')
    }
  }

  get tagsValue() {
    return this.selectTags
  }
  set tagsValue(value: any) {
    const res = value?.length === 0 ? null : value ?? null
    this.$emit('change', res)
  }

  created() {
    this.getTags()
  }

  doRemoteMethod(value: string) {
    this.getTags(value);
  }
  getTags(value?: string) {

    var goodsTagFilter: GoodsTagFilter = new GoodsTagFilter();
    goodsTagFilter.page = 0;
    goodsTagFilter.pageSize = 200;
    if(value){
      goodsTagFilter.key = value;
    }  

    GoodsApi.getGoodsTag(goodsTagFilter).then((res) => {
      if(res.code === 2000) {
        this.originTags = res.data || []
        this.tags = res.data || []
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    })
  }
};