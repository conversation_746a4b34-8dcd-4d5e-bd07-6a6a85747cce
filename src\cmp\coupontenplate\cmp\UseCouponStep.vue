<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:02
 * @LastEditTime: 2023-07-28 18:02:11
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\coupontenplate\cmp\UseCouponStep.vue
 * 记得注释
-->
<template>
  <div class="time-range">
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
      <el-radio-group @change="doTypeStepChange" v-model="ruleForm.useCouponStep" class="small-radio">
        <el-radio label="step1">
          {{ formatI18n("/公用/券模板/无门槛") }}
        </el-radio>
        <br />

        <el-radio label="step2">
          {{ formatI18n("/公用/券模板/单品折扣券/用券门槛/用券商品满") }}
          <el-form-item prop="step2" style="display: inline-block;margin-left: 0" class="cur-time">
            &nbsp;<el-input :disabled="ruleForm.useCouponStep !== 'step2'" @change="doStepChange(2)" style="width: 120px" v-model="ruleForm.step2">
              <template slot="append">{{i18n('元')}}</template>
            </el-input>&nbsp;
          </el-form-item> 
          {{ i18n("及以上可用") }}
        </el-radio>
        <br />

        <el-radio label="step3" v-if="showQty">
          {{ formatI18n("/公用/券模板/单品折扣券/用券门槛/用券商品满") }}
          <el-form-item prop="step3" style="display: inline-block;margin-left: 0" class="cur-time">
            &nbsp;<el-input :disabled="ruleForm.useCouponStep !== 'step3'" @change="doStepChange(1)" style="width: 120px" v-model="ruleForm.step1">
              <template slot="append">{{i18n('件')}}</template>
            </el-input>&nbsp;
          </el-form-item>
          {{ i18n("及以上可用") }}
        </el-radio>
        <br />

        <el-radio label="step4" v-if="duplicateDiscount && hasPermissionEveryfullreduction" style="height:40px">
          {{ i18n("用券商品每满") }}
          <el-form-item prop="step4_thresholdAmount" style="display: inline-block;margin-left: 0" class="cur-time">
            <el-input :disabled="ruleForm.useCouponStep !== 'step4'" @change="doStepChange(4)" style="width: 120px"
              v-model="ruleForm.step4.thresholdAmount">
              <template slot="append">{{i18n('元')}}</template>
            </el-input>
            {{ formateThresholdStr }}
          </el-form-item>
          <el-form-item prop="step4_maxAmount" style="display: inline-block;margin-left: 0" class="cur-time">
            &nbsp;<el-input :disabled="ruleForm.useCouponStep !== 'step4'" @change="doStepChange(4)" style="width: 120px"
              v-model="ruleForm.step4.maxAmount">
              <template slot="append">{{i18n('元')}}</template>
            </el-input>&nbsp;
          </el-form-item>
        </el-radio>
        <div class="step4_tips gray-tips" v-if="ruleForm.useCouponStep === 'step4' && hasPermissionEveryfullreduction">
          <i class="el-icon-warning-outline" style="margin-right:4px"></i>
          {{i18n('核销方式：每满减的券只能出示券码单券核销，其他支付方式不可核销')}}
        </div>
      </el-radio-group>
    </el-form>
  </div>
</template>

<script lang="ts" src="./UseCouponStep.ts"></script>

<style lang="scss">
.time-range {
  width: 800px;
  padding: 12px 0 0;
  .cur-time {
    .el-form-item__content {
      margin-left: 0 !important;
    }
  }
  .goods-qty-limit {
    display: block;
    background: #fff;
    padding: 10px;
    .el-radio {
      height: 40px;
    }
  }
  .small-radio .el-radio {
    height: 54px;
  }
  .small-radio .el-radio:nth-child(1) {
    height: 30px;
  }
  .step4_tips {
    margin: 24px 0 0 24px;
  }
}
</style>
