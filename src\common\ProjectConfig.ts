/*
 * @Author: 黎钰龙
 * @Date: 2024-07-02 14:56:58
 * @LastEditTime: 2024-07-02 15:51:09
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\common\ProjectConfig.ts
 * 记得注释
 */

import { LocalStorage } from "mgr/BrowserMgr";

export default class ProjectConfig {
  static init() {
    let config = require('../../package.json'); //开发环境下，package.json的配置不会热更新，需要重启项目
    LocalStorage.setItem("version", config?.vue?.version || '1.0.0');
  }
}