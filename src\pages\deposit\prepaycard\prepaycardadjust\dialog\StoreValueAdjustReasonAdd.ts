/*
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:05
 * @LastEditTime: 2024-04-07 15:51:43
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\prepaycard\prepaycardadjust\dialog\StoreValueAdjustReasonAdd.ts
 * 记得注释
 */
import {Component, Prop, Vue, Watch} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import CustomizeContentApi from 'http/customizecontent/CustomizeContentApi'
import I18nPage from "common/I18nDecorator";

@Component({
  name: 'StoreValueAdjustReasonAdd',
  components: {
    FormItem
  }
})
@I18nPage({
  prefix: [
    '/储值/预付卡/预付卡调整单/预付卡调整原因设置/修改预付卡调整原因',
    '/公用/按钮',
  ],
})
export default class StoreValueAdjustReasonAdd extends Vue {
  i18n: (str: string, params?: string[]) => string
  flag = false
  id = ''
  $refs: any
  @Prop()data: any
  @Prop({ type: String}) reasonType: string;
  @Prop({type: Boolean,default: false})dialogShow: boolean
  @Prop({ type: String}) title: string;
  @Prop({ type: String}) label: string;
  @Prop({ type: String}) validateMsg: string;
  reason = ''

  @Watch('data')
  onDataChange(value: any) {
    if (value) {
      this.reason = value.content
      this.id = value.id
    }
  }

  mounted() {
    this.reason = this.data.content
  }
  doBeforeClose(done: any) {
    this.$emit('dialogClose')
    done()
  }
  doModalClose(type: string) {
    if (type === 'confirm') {
      if (!this.reason) {
        this.$refs.reason.focus()
        return
      }
      CustomizeContentApi.modify(this.id, this.reason, this.reasonType).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.i18n('修改成功'))
          this.$emit('dialogClose')
        } else {
          throw new Error(resp.msg)
        }
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    } else {
      this.$emit('dialogClose')
    }
  }
  doBlur() {
    if (!this.reason) {
      this.flag = true
    } else {
      this.flag = false
    }
  }
}
