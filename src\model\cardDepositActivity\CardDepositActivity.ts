import ActivityBody from 'model/common/ActivityBody'
import CardDepositRuleLine from './CardDepositRuleLine'
import ChannelRange from 'model/common/ChannelRange'
import DateTimeCondition from 'model/common/DateTimeCondition'
import IdName from 'model/common/IdName'

export default class CardDepositActivity {
  // 活动主体
  body: Nullable<ActivityBody> = null
  // 活动时间条件
  dateTimeCondition: Nullable<DateTimeCondition> = null
  // 活动规则明细
  lines: CardDepositRuleLine[] = []
  // 营销中心
  marketingCenter: Nullable<string> = null
  // 卡模板
  cardTemplates: IdName[] = []
  // 渠道范围
  channelRange: Nullable<ChannelRange> = null
  // 每充值金额
  amount: Nullable<number> = null
  // 赠送积分数量
  points: Nullable<number> = null
  // 充值说明
  description: Nullable<string> = null
  // 充值协议
  agreement: Nullable<string> = null
}