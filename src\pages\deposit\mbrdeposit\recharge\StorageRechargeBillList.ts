/*
 * @Author: 黎钰龙
 * @Date: 2024-07-09 19:13:25
 * @LastEditTime: 2024-07-22 10:38:55
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\mbrdeposit\recharge\StorageRechargeBillList.ts
 * 记得注释
 */
import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import FormItem from 'cmp/formitem/FormItem';
import ListWrapper from 'cmp/list/ListWrapper';
import I18nPage from 'common/I18nDecorator';
import MemberBalanceRechargeBillApi from 'http/deposit/activity/MemberBalanceRechargeBillApi';
import BMemberBalanceRechargeBill from 'model/deposit/activity/BMemberBalanceRechargeBill';
import BMemberBalanceRechargeBillBatchRequest from 'model/deposit/activity/BMemberBalanceRechargeBillBatchRequest';
import BMemberBalanceRechargeBillFilter from 'model/deposit/activity/BMemberBalanceRechargeBillFilter';
import CommonUtil from 'util/CommonUtil';
import { Component, Vue } from 'vue-property-decorator';

class QueryObj {
  numberEquals = '' //单号等于
  memberCodeLikes = '' //手机号或会员号等于
  stateEquals = null //状态等于
}

@Component({
  name: 'StorageRechargeBillList',
  components: {
    BreadCrume,
    FormItem,
    ListWrapper
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/储值/会员储值/储值充值单'
  ],
  auto: true
})
export default class StorageRechargeBillList extends Vue {
  $refs: any
  query: QueryObj = new QueryObj()
  createTime: string[] = []
  activeName: string = 'first' // 列表tab
  selectAll = ''
  tableData: BMemberBalanceRechargeBill[] = []
  selectedArr: BMemberBalanceRechargeBill[] = []
  total: any = {
    all: 0,
    initial: 0,
    audit: 0
  }
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }

  get panelArray() {
    return [
      {
        name: this.formatI18n('/公用/菜单/储值充值单'),
        url: ''
      }
    ]
  }
  get getAllCount() {
    return `${this.i18n('/公用/活动/状态/全部')}(${this.total.all})`
  }
  get getNoAudit() {
    return `${this.i18n('/公用/活动/状态/未审核')}(${this.total.initial})`
  }
  get getAudit() {
    return `${this.i18n('/公用/活动/状态/已审核')}(${this.total.audit})`
  }

  created() {
    this.queryList()
  }

  doSelectAll() {
    if (this.selectAll) {
      for (let row of this.tableData) {
        this.$refs.table.toggleRowSelection(row, true)
      }
    } else {
      this.$refs.table.clearSelection();
    }
  }

  handleSelectionChange(val: BMemberBalanceRechargeBill[]) {
    this.selectedArr = val
  }

  doSearch() {
    this.page.currentPage = 1
    this.queryList()
  }

  doReset() {
    this.page.currentPage = 1
    this.query = new QueryObj()
    this.activeName = 'first'
    this.createTime = []
    this.queryList()
  }

  getTotal(params: BMemberBalanceRechargeBillFilter) {
    MemberBalanceRechargeBillApi.statistics(params).then((res) => {
      if (res.code === 2000) {
        this.total.all = res.data?.all || 0
        this.total.initial = res.data?.init || 0
        this.total.audit = res.data?.audit || 0
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    })
  }

  queryList() {
    const params = new BMemberBalanceRechargeBillFilter()
    if (this.createTime?.length) {
      params.createDateBegin = this.createTime[0] as any
      params.createDateEnd = this.createTime[1] as any
    }
    params.memberCodeLikes = this.query.memberCodeLikes || null
    params.numberEquals = this.query.numberEquals || null
    params.page = this.page.currentPage - 1
    params.pageSize = this.page.size
    params.stateEquals = this.query.stateEquals
    this.getTotal(params)
    MemberBalanceRechargeBillApi.query(params).then((res) => {
      if (res.code === 2000) {
        this.tableData = res.data || []
        this.page.total = res.total || 0
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    })
  }

  canAudit(row: BMemberBalanceRechargeBill) {
    const isImported = (row.rechargeType === 'file' && row.importTask) || row.rechargeType === 'mobie'
    return row.state === 'INITIAL' && this.hasOptionPermission('/储值/储值管理/储值充值单', '单据审核') && isImported
  }

  // 切换筛选状态
  doHandleClick() {
    this.page.currentPage = 1
    if (this.activeName === 'first') {
      this.query.stateEquals = null
    } else if (this.activeName === 'second') {
      this.query.stateEquals = 'INITIAL' as any
    } else {
      this.query.stateEquals = 'ADUIT' as any
    }
    this.queryList()
  }

  doBatchDelete() {
    if (this.selectedArr.length <= 0) {
      this.$message.warning(this.formatI18n('/权益/积分/积分调整单/列表/按钮/点击批量删除/请先勾选要删除的单据') as string)
      return
    }
    this.$confirm(this.formatI18n('/权益/积分/积分调整单/列表/按钮/点击批量删除/是否批量删除这些单据?') as string, this.formatI18n('/权益/积分/积分调整单/列表/按钮/批量删除') as string, {
      confirmButtonText: this.formatI18n('/公用/按钮', '确定') as any,
      cancelButtonText: this.formatI18n('/公用/按钮', '取消') as any
    }).then(() => {
      this.submitBatchDelete()
    })
  }

  doBatchAudit() {
    if (this.selectedArr.length <= 0) {
      this.$message.warning(this.formatI18n('/权益/积分/积分调整单/列表/按钮/点击批量审核/请先勾选要审核的单据') as string)
      return
    }
    this.$confirm(this.formatI18n('/权益/积分/积分调整单/列表/按钮/点击批量审核/是否批量审核这些单据?') as string, this.formatI18n('/权益/积分/积分调整单/列表/按钮/批量审核') as string, {
      confirmButtonText: this.formatI18n('/公用/按钮', '确定') as any,
      cancelButtonText: this.formatI18n('/公用/按钮', '取消') as any
    }).then(() => {
      this.submitBatchAudit()
    })
  }

  submitBatchDelete() {
    let ids: string[] = []
    if (this.selectedArr?.length > 0) {
      this.selectedArr.forEach((item) => {
        ids.push(item.billNumber!)
      })
    }
    const params = new BMemberBalanceRechargeBillBatchRequest()
    params.billNumbers = ids
    const loading = CommonUtil.Loading()
    MemberBalanceRechargeBillApi.batchRemove(params).then((res: any) => {
      if (res.code === 2000) {
        this.$message.success(this.i18n('本次操作成功{0}条,失败{1}条', [res.data?.success as any, res.data?.fail]))
        this.queryList()
      } else {
        throw new Error(res.msg)
      }
    }).catch((error: any) => {
      this.$message.error(error.message)
    }).finally(() => {
      loading.close()
    })
  }

  submitBatchAudit() {
    let ids: string[] = []
    if (this.selectedArr?.length > 0) {
      this.selectedArr.forEach((item) => {
        ids.push(item.billNumber!)
      })
    }
    const params = new BMemberBalanceRechargeBillBatchRequest()
    params.billNumbers = ids
    const loading = CommonUtil.Loading()
    MemberBalanceRechargeBillApi.batchAudit(params).then((res) => {
      if (res.code === 2000) {
        this.$message.success(this.i18n('本次操作成功{0}条,失败{1}条', [res.data?.success as any, res.data?.fail]))
        this.queryList()
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error: any) => {
      this.$message.error(error.message)
    }).finally(() => {
      loading.close()
    })
  }

  // 查看详情页
  doDtl(billNumber: string) {
    this.$router.push({
      name: 'storage-recharge-bill-dtl',
      query: {
        billNumber: billNumber
      }
    })
  }

  doAudit(row: BMemberBalanceRechargeBill) {
    const loading = CommonUtil.Loading()
    MemberBalanceRechargeBillApi.audit(row.billNumber!).then((res) => {
      if (res.code === 2000) {
        this.$message.success(this.i18n('操作成功'))
        this.queryList()
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    }).finally(() => {
      loading.close()
    })
  }

  doModify(row: BMemberBalanceRechargeBill) {
    this.$router.push({
      name: 'storage-recharge-bill-edit',
      query: {
        billNumber: row.billNumber,
        importState: row.importTask ? 'complete' : 'undone'
      }
    })
  }

  doRemove(row: BMemberBalanceRechargeBill) {
    this.$confirm(
      this.i18n("/会员/会员资料/确定删除吗？"),
      this.i18n("删除"),
      {
        confirmButtonText: this.formatI18n("/公用/按钮", "确定"),
        cancelButtonText: this.formatI18n("/公用/按钮", "取消"),
      }
    ).then(() => {
      const loading = CommonUtil.Loading()
      MemberBalanceRechargeBillApi.remove(row.billNumber!).then((res) => {
        if (res.code === 2000) {
          this.$message.success(this.i18n('操作成功'))
          this.queryList()
        } else {
          throw new Error(res.msg!)
        }
      }).catch((error) => {
        this.$message.error(error.message)
      }).finally(() => {
        loading.close()
      })
    });
  }

  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.queryList()
  }

  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.queryList()
  }

  computeState(state: string) {
    return CommonUtil.computeState(state, [
      ['INITIAL', this.i18n('/公用/活动/状态/未审核'), '#FFAA00'],
      ['ADUIT', this.i18n('/公用/活动/状态/已审核'), '#0CC66D']
    ])
  }

  doCreate() {
    this.$router.push({
      name: 'storage-recharge-bill-edit'
    })
  }
};