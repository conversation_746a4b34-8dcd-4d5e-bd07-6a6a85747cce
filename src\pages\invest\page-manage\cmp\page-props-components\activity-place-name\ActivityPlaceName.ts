/*
 * @Author: haiding <EMAIL>
 * @Date: 2025-02-19 10:03:52
 * @LastEditors: haiding <EMAIL>
 * @LastEditTime: 2025-03-06 09:41:22
 * @FilePath: \new-kequn\src\pages\invest\page-manage\cmp\page-props-components\activity-place-name\ActivityPlaceName.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import { FormMode } from 'model/local/FormMode';
import I18nPage from 'common/I18nDecorator';

@Component({
  name: 'ActivityPlaceName',
  mixins: [],
  components: {},
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理',
    '/页面/导航设置',
  ],
  auto: true
})
export default class ActivityPlaceName extends Vue {
  @Prop({ type: Boolean, default: false })
  readonly: boolean; //
  @Prop({ type: String, default: 'PlaceName' })
  validateName: string; // 组件校验名称，editPage通过该名称进行组件校验及校验信息清除
  @Prop({ type: Object })
  value: any; // 数据模型
  @Prop({ type: String, default: '组件名称' })
  label: string; // label名
  @Prop({ type: String })
  prefixDescribe: string; // 前置描述
  @Prop({ type: String })
  suffixDescribe: string; // 后置描述

  @Prop({ type: Boolean, default: true })
  required: boolean; // 是否必填
  @Prop({ type: String })
  relativeValidateName: string; // 联动校验名称
  @Prop()
  formKey: any;
  @Prop({ type: Array })
  renderTemplateList: any[];
  @Prop()
  activeIndex: number;

  FormModeType = FormMode;
  $refs: any;
  labelPosition: string = 'left';

  rules = {
    name: [
      {
        required: true,
        trigger: ['blur', 'change'],
        validator: (rule, value, callback) => {
          if (!value.trim()) {
            return callback(new Error(this.i18n('请输入组件名称')));
          }
          // if (this.filterActivityWidgets.some((item) => item.props.name === value)) {
          //   return callback(new Error('组件名称不能重复'));
          // }
          // if (this.filterActivityWidgets.some((item) => item.props.name === value)) {
          //   return callback(new Error('组件名称不能重复'));
          // }
          return callback();
        },
      },
    ],
  };
  // get filterActivityWidgets() {
  //   console.log();

  //   return this.activityWidgets.filter((item, index) => index !== this.activeIndex);
  // }
  get filterActivityWidgets() {
    return this.renderTemplateList.filter((item, index) => index !== this.activeIndex);
  }
  handleChange() {
    this.$emit('input', this.value);
    this.$emit('change', this.value);
    this.validate(() => { });
  }

  mounted() { }

  beforeDestroy() { }

  validate(callback) {
    if (this.$refs.form) {
      return this.$refs.form.validate(callback);
    }
  }

  clearValidate() {
    if (this.$refs.form) {
      return this.$refs.form.clearValidate();
    }
  }
}
