<template>
    <span class="active-store-dtl">
        <span v-if="data.storeRangeType !== 'ALL' && data.storeRangeLimitType === 'MARKETING_CENTER'">
            <div style="border: 1px solid #e6e6e6;width: 720px">
                <div style="background-color: #e6e6e6;padding-left: 10px;height: 32px;line-height: 32px">{{getStoreUse(data.storeRangeType)}}</div>
                <div class="flex-wrap">
                    <div class="flex-item width-100">{{formatI18n('/公用/券模板', '品牌')}}</div>
                    <div class="flex-item width-100">{{formatI18n('/公用/券模板', '属于')}}</div>
                    <div class="flex-item flex-1" style="padding: 10px;max-height: 770px;overflow: auto">
                        <div v-for="item in getMarketIdName(data.stores)">{{item}}</div>
                    </div>
                </div>
                <div class="flex-wrap">
                    <div class="flex-item width-100">{{formatI18n('/公用/券模板', '品牌')}}</div>
                    <div class="flex-item width-100">{{formatI18n('/公用/券模板', '不属于')}}</div>
                    <div class="flex-item flex-1" style="padding: 10px;max-height: 770px;overflow: auto">
                        <div v-for="item in getMarketIdName(data.stores)">{{item}}</div>
                    </div>
                </div>
                <div class="flex-wrap">
                    <div class="flex-item width-100">{{formatI18n('/公用/券模板', '品类')}}</div>
                    <div class="flex-item width-100">{{formatI18n('/公用/券模板', '属于')}}</div>
                    <div class="flex-item flex-1" style="padding: 10px;max-height: 770px;overflow: auto">
                        <div v-for="item in getMarketIdName(data.stores)">{{item}}</div>
                    </div>
                </div>
                <div class="flex-wrap">
                    <div class="flex-item width-100">{{formatI18n('/公用/券模板', '品类')}}</div>
                    <div class="flex-item width-100">{{formatI18n('/公用/券模板', '不属于')}}</div>
                    <div class="flex-item flex-1" style="padding: 10px;max-height: 770px;overflow: auto">
                        <div v-for="item in getMarketIdName(data.stores)">{{item}}</div>
                    </div>
                </div>
                <div class="flex-wrap">
                    <div class="flex-item width-100">{{formatI18n('/公用/券模板', '单品')}}</div>
                    <div class="flex-item width-100">{{formatI18n('/公用/券模板', '属于')}}</div>
                    <div class="flex-item flex-1" style="padding: 10px;max-height: 770px;overflow: auto">
                        <div v-for="item in getMarketIdName(data.stores)">{{item}}</div>
                    </div>
                </div>
                <div class="flex-wrap">
                    <div class="flex-item width-100">{{formatI18n('/公用/券模板', '单品')}}</div>
                    <div class="flex-item width-100">{{formatI18n('/公用/券模板', '不属于')}}</div>
                    <div class="flex-item flex-1" style="padding: 10px;max-height: 770px;overflow: auto">
                        <div v-for="item in getMarketIdName(data.stores)">{{item}}</div>
                    </div>
                </div>
            </div>
        </span>
    </span>
</template>

<script lang="ts" src="./GoodsDtl.ts">
</script>

<style lang="scss">
.active-store-dtl{
    display: inline-flex;
    .flex-wrap{
        display: flex;
        .width-100{
            display: flex;
            width: 100px;
            justify-content: center;
            align-items: center;
        }
        .flex-item{

        }
        .flex-1{
            flex: 1;
        }
    }
}
</style>