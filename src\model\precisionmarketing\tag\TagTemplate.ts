/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-10-11 15:51:36
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\precisionmarketing\tag\TagTemplate.ts
 * 记得注释
 */
import Channel from 'model/common/Channel'
import { SyncChannelTypeEnum } from 'model/common/SyncChannelTypeEnum'
import TagRuleBody from 'model/precisionmarketing/tag/tagrule/TagRuleBody'

// tag列表相关字段
export default class TagTemplate {
  // 为true时候为仅保存，false为保存并启用
  justSave: Nullable<boolean> = null
  // 标签模板uuid
  uuid: Nullable<string> = null
  // 标签名称
  name: Nullable<string> = null
  // 分类id
  category: Nullable<string> = null
  // 分类名称
  categoryName: Nullable<string> = null
  // 打标方式等于：Manual-手动；Auto-自动
  tagModel: Nullable<string> = null
  // 状态：Enable-已启用，Disable-已禁用
  state: Nullable<string> = null
  // 备注
  remark: Nullable<string> = null
  // 更新频次：执行类型：DailyScheduleType-按日；MonthlyScheduleType-按月
  scheduleType: Nullable<string> = null
  // ScheduleType为MonthlyScheduleType时的天
  executeDay: Nullable<number> = null
  // 最近计算开始时间
  lastBeginTime: Nullable<Date> = null
  // 最近计算时间
  lastEndTime: Nullable<Date> = null
  // 最近计算状态：Processing-进行中；Success-计算成功；Fail-计算失败
  lastExecuteState: Nullable<string> = null
  // 创建人
  creator: Nullable<string> = null
  // 创建时间
  created: Nullable<Date> = null
  // 最后修改人
  lastModifier: Nullable<string> = null
  // 最后修改时间
  lastModified: Nullable<Date> = null
  // 改模板对应es字段名称
  esFieldName: Nullable<string> = null
  // 标签规则
  ruleData: Nullable<TagRuleBody> = null
  // 标签值类型
  tagType: Nullable<string> = null
  // POS是否展示
  deskDisplay: Nullable<boolean> = null
  // 同步渠道
  syncChannels: Channel[] = [];
  // ALL-全部渠道，PART-指定渠道 
  syncType: SyncChannelTypeEnum = SyncChannelTypeEnum.ALL
}