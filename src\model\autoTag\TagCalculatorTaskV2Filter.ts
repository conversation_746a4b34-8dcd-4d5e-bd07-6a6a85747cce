import { TagTypeEnum } from "model/common/TagTypeEnum"
import { TagCalculatorTaskState } from "./TagCalculatorTaskState"
import { TagCalculatorTaskType } from "./TagCalculatorTaskType"
import { TagCalculatorTaskExecuteState } from "./TagCalculatorTaskExecuteState"

export default class TagCalculatorTaskV2Filter {
  // 标签类型等于
  tagTypeEquals: Nullable<TagTypeEnum> = null
  // 任务状态等于
  stateEquals: Nullable<TagCalculatorTaskState> = null
  // 任务执行状态等于
  executeStateEquals: Nullable<TagCalculatorTaskExecuteState> = null
  // 创建方式等于
  typeEquals: Nullable<TagCalculatorTaskType> = null
  // 名称类似于
  tagNameLikes: Nullable<string> = null
  // 页数>=0
  page: number = 0
  // 页面大小>0
  pageSize: number = 0
}