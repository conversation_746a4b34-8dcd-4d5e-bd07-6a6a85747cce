import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import CouponItem from "model/common/CouponItem";
import ActiveStoreDtl from "cmp/activestoredtl/ActiveStoreDtl.vue";
import GoodsScopeDtl from "cmp/goodsscope/GoodsScopeDtl.vue";
import FormItem from "cmp/formitem/FormItem.vue";
import RSCostPartyFilter from "model/common/RSCostPartyFilter";
import CostPartyApi from "http/costparty/CostPartyApi";
import RSCostParty from "model/common/RSCostParty";
import RSChannelManagementFilter from "model/common/RSChannelManagementFilter";
import ChannelManagement<PERSON>pi from "http/channelmanagement/ChannelManagementApi";
import RSChannelManagement from "model/common/RSChannelManagement";
import Channel from "model/common/Channel";
import ChannelRange from "model/common/ChannelRange";
import { SessionStorage } from "mgr/BrowserMgr";
import { ExpiryType } from "model/common/ExpiryType";
import { IssueChannel } from "model/common/weimobCoupon/IssueChannel";
import { FreeChannel } from "model/common/weimobCoupon/FreeChannel";
import { ApportionType } from "model/common/ApportionType";
import xss from "xss";
import SpecialGoodsDialog from 'cmp/coupontenplate/cmp/specialGoodsDialog.vue'
import DateUtil from "util/DateUtil";
import CBearSpecialGoodsDtl from 'cmp/coupontenplate/cmp/cBearSpecialGoodsDtl.vue'
import I18nPage from "common/I18nDecorator";
import CouponInfo from 'model/common/CouponInfo'
import PromotionShowDialog from 'cmp/coupontenplate/cmp/PromotionShowDialog.vue'
import UseCouponGoods from "cmp/couponTemplateDtlItem/useCouponGoods/UseCouponGoods";
import Tools from "util/Tools";

@Component({
  name: "ActiveAddCouponDtl",
  components: {
    FormItem,
    ActiveStoreDtl,
    GoodsScopeDtl,
    SpecialGoodsDialog,
    CBearSpecialGoodsDtl,
    PromotionShowDialog,
    UseCouponGoods,
    GroupMutexTemplateDtl: () =>
      import("cmp/coupontenplate/cmp/GroupMutexTemplateDtl.vue"),
  },
})
@I18nPage({
  prefix: [
    "/公用/券模板",
    '/公用/券模板/单品折扣券/用券门槛',
    '/公用/券模板/提货券/用券商品'
  ],
  auto: true
})
export default class ActiveAddCouponDtl extends Vue {
  checkGoodsDialog = false;
  AllAmount: number = 0;
  @Prop()
  data: CouponItem;
  @Prop()
  externalCode: string;

  @Prop({
    default: () => {
      return {
        hideTitle: false,
        hideState: false,
        hideFaceAmount: false,
        hideType: false,
        hideName: false,
        hideOuterNumberNamespace: false,
      };
    },
  })
  options: {
    hideTitle: boolean;
    hideState: boolean;
    hideFaceAmount: boolean;
    hideType: boolean;
    hideName: boolean;
    hideOuterNumberNamespace: boolean;
  };

  @Prop({
    type: Boolean,
    default: false,
  })
  isTopPanel: boolean;

  @Prop({
    type: Boolean,
    default: false,
  })
  activityDtl: boolean;

  @Prop({
    type: Boolean,
    default: false,
  })
  deepDialog: boolean;

  @Prop({
    type: Boolean,
    default: false,
  })
  wxScanForCoupon: boolean;

  parties: RSCostParty[] = [];
  channels: RSChannelManagement[] = [];
  labelWidth: string = '110px'
  // @Watch('data', { deep: true})
  xss: Function = xss
  get getAllAmount() {
    let num: number = 0;
    if (
      this.data.coupons != null &&
      this.data.coupons.pickUpCouponAttribute != null &&
      this.data.coupons.pickUpCouponAttribute.pickUpGoods != null &&
      this.data.coupons.enablePayApportion == true
    ) {
      for (
        let i = 0;
        i < this.data.coupons.pickUpCouponAttribute.pickUpGoods.length;
        i++
      ) {
        const item = this.data.coupons.pickUpCouponAttribute.pickUpGoods[i];
        if (item.bookPayPrice != null && item.qty != null) {
          num += item.bookPayPrice * item.qty;
        }
      }
    }
    // this.AllAmount = num
    return num.toFixed(2);
  }

  get isEnglish() {
    return sessionStorage.getItem("locale") === "en_US";
  }

  get getLimitReceiveText() {
    let str = ''
    if (this.data && this.data.coupons && this.data.coupons.weimobCoupon) {
      if (this.data.coupons.weimobCoupon.perLimit) {
        str = this.formatI18n("/公用/券模板", "每位用户只能领取") + " " + this.data.coupons.weimobCoupon.perLimit + " " + this.formatI18n("/营销/券礼包活动/券礼包活动/张")
        if (this.data.coupons.weimobCoupon.dayLimit) {
          str += ' ' + this.formatI18n("/公用/券模板", "每天可领取") + ' ' + this.data.coupons.weimobCoupon.dayLimit + this.formatI18n("/营销/券礼包活动/券礼包活动/张")
        } else {
          str += ' ' + this.formatI18n("/公用/券模板", "每天可领取") + ' ' + this.formatI18n('/公用/券模板', '不限制数量')
        }
      } else {
        str = this.formatI18n('/储值/预付卡/电子礼品卡活动/编辑页面/不限制')
      }
    }
    return str
  }

  get formateThresholdStr() {
    let str = ''
    str = this.i18n('元减{0}元，最多可减')
    str = str.replace(/\{0\}/g, String(this.data?.coupons?.cashCouponAttribute?.faceAmount) || '--');
    return str
  }

   // 微盟适用商品排除
   get excludedGoodsList() {
    const goodsUseRule: any = this.data && this.data.coupons && this.data.coupons.weimobCoupon && this.data.coupons.weimobCoupon.goodsUseRule || {}
    if (goodsUseRule.limitedGoodsType == 'goods') {
      const limitGoodsTypeRule = goodsUseRule.limitGoodsTypeRule
      return limitGoodsTypeRule.existExcludeGoods && limitGoodsTypeRule.excludeGoodsIds
    }
    if (goodsUseRule.limitedGoodsType == 'goodsCategory') {
      const limitGoodsCategoryTypeRule = goodsUseRule.limitGoodsCategoryTypeRule
      return limitGoodsCategoryTypeRule.existExcludeGoods && limitGoodsCategoryTypeRule.excludeGoodsIds
    }
    if (goodsUseRule.limitedGoodsType == 'goodsGroup') {
      const limitGoodsGroupRule = goodsUseRule.limitGoodsGroupRule
      return limitGoodsGroupRule.existExcludeGoods && limitGoodsGroupRule.excludeGoodsIds
    }
    return []
  }

  get goodsInfo() {
    const goodsUseRule: any = this.data && this.data.coupons && this.data.coupons.weimobCoupon && this.data.coupons.weimobCoupon.goodsUseRule || {}
    if (!goodsUseRule.limitedGoods) {
      return {
        str: '全部商品',
        list: [],
        type: 'none'
      }
    }
    if (goodsUseRule.limitedGoodsType == 'goods') {
      const limitGoodsTypeRule = goodsUseRule.limitGoodsTypeRule
      if (limitGoodsTypeRule!.goodsRange == 'all') {
        return {
          str: '全部商品',
          list: [],
          type: 'none'
        }
      } else {
        if (limitGoodsTypeRule.includeGoodsIds && limitGoodsTypeRule.includeGoodsIds.length > 0) {
          return {
            str: `已选择${limitGoodsTypeRule.includeGoodsIds.length}件商品`,
            list: limitGoodsTypeRule.includeGoodsIds,
            type: 'tips'
          }
        }
      }
    } else if (goodsUseRule.limitedGoodsType == 'goodsCategory') {
      const limitGoodsCategoryTypeRule = goodsUseRule.limitGoodsCategoryTypeRule
      let str = ''
      if (limitGoodsCategoryTypeRule.ruleInfos && limitGoodsCategoryTypeRule.ruleInfos.length > 0) {
        const categoryInfo = limitGoodsCategoryTypeRule.ruleInfos[0]
        str = '指定类目：' + categoryInfo.categoryName
        if (categoryInfo.childs && categoryInfo.childs.length > 0) {
          str = `${str} > ${categoryInfo.childs[0].categoryName}`
        }
        return {
          str: str,
          list: [],
          type: 'none'
        }
      }
    } else if (goodsUseRule.limitedGoodsType == 'goodsGroup') {
      const limitGoodsGroupRule = goodsUseRule.limitGoodsGroupRule
      let str = ''
      if (limitGoodsGroupRule.ruleInfos && limitGoodsGroupRule.ruleInfos.length > 0) {
        let childsNum = 0
        const list = limitGoodsGroupRule.ruleInfos.reduce((acc: any, cur: any) => {
          const firstName = cur.name
          if (cur.childs && cur.childs.length > 0) {
            cur.childs.map((item: any) => {
              childsNum = childsNum + 1
              acc.push({
                id: '',
                name: `${firstName} > ${item.name}`
              })
            })
          } else {
            childsNum = childsNum + 1
            acc.push({
              id: '',
              name: firstName
            })
          }
          return acc
        }, [])
        str = `${this.i18n('已选择')}${childsNum}${this.i18n('个分组')}`
        return {
          str,
          list,
          type: 'tips'
        }
      }
    }
    return {
      str: '',
      list: [],
      type: 'none'
    }
  }

  created() {
    // console.log(xss("满0.01-0.01<img src='1' onerror=alert('123') />"));

    if (sessionStorage.getItem('locale') === 'en_US') {
      this.labelWidth = '180px'
    }
    this.getCostParty();
    this.getChannels();
  }

  getCouponPromotionInfo(coupon: CouponInfo) {
    if (coupon.excludePromotion) {
      return coupon.promotionSuperpositionType == 'PART' ? this.formatI18n('/公用/券模板详情/指定促销活动叠加') : this.formatI18n('/公用/券模板详情/全部促销活动叠加')
    } else {
      return coupon.promotionSuperpositionType == 'PART' ? this.formatI18n('/公用/券模板详情/指定促销活动不叠加') : this.formatI18n('/公用/券模板详情/全部促销活动不叠加')
    }
  }

  getValidateDate(
    delayEffectDays: number,
    validityDays: number,
    expiryType: ExpiryType,
    months: number
  ) {
    return Tools.getValidateDate(delayEffectDays, validityDays, expiryType, months)
  }

  getGoodsNo(threshold: any) {
    let str: any = this.formatI18n(
      "/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送全场现金券的时候/用券门槛/不可叠加使用",
      "商品数量满{0}件及以上可用1张券"
    );
    str = str.replace(
      /\{0\}/g,
      `&nbsp;<span style="font-weight: bold">${threshold}</span>&nbsp;`
    );
    return str;
  }

  getDayTime(beginTime: any, endTime: any) {
    let str: any = this.formatI18n(
      "/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送券的时候并且该券用券时段为指定可用时段每天时/点击券名称",
      "每天{0}至{1}"
    );
    str = str.replace(
      /\{0\}/g,
      `&nbsp;<span style="font-weight: bold">${beginTime}</span>&nbsp;`
    );
    str = str.replace(
      /\{1\}/g,
      `&nbsp;<span style="font-weight: bold">${endTime}</span>&nbsp;`
    );
    return str;
  }

  getMonthTime(days: any, beginTime: any, endTime: any) {
    let str: any = this.formatI18n(
      "/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送券的时候并且该券用券时段为指定可用时段每月时/点击券名称",
      "每月{0}日{1}至{2}"
    );
    str = str.replace(
      /\{0\}/g,
      days.slice().sort((a: number, b: number) => {
        return a - b;
      })
    );
    str = str.replace(
      /\{1\}/g,
      `&nbsp;<span style="font-weight: bold">${beginTime}</span>&nbsp;`
    );
    str = str.replace(
      /\{2\}/g,
      `&nbsp;<span style="font-weight: bold">${endTime}</span>&nbsp;`
    );
    return str;
  }

  getWeekTime(days: any, beginTime: any, endTime: any) {
    let str: any = this.formatI18n(
      "/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送券的时候并且该券用券时段为指定可用时段每周时/点击券名称",
      "每周{0}{1}到{2}"
    );
    str = str.replace(
      /\{0\}/g,
      `&nbsp;<span style="font-weight: bold">${this.getWeeks(
        days
      )}</span>&nbsp;`
    );
    str = str.replace(
      /\{1\}/g,
      `&nbsp;<span style="font-weight: bold">${beginTime}</span>&nbsp;`
    );
    str = str.replace(
      /\{2\}/g,
      `&nbsp;<span style="font-weight: bold">${endTime}</span>&nbsp;`
    );
    return str;
  }

  getGoodsLength(length: number) {
    let str: any = this.formatI18n(
      "/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送券的时候/点击券名称/用券商品为非全部商品时",
      "已选择{0}个商品"
    );
    str = str.replace(/\{0\}/g, length);
    return str;
  }

  getChannel(curChannel: ChannelRange) {
    let str: string = "";
    if (curChannel.channelRangeType === "ALL") {
      str = this.formatI18n("/公用/券模板/用券渠道", "全部渠道");
      return str;
    } else {
      // let channel = SessionStorage.getItem("channels");
      let channel = this.channels;
      if (channel && channel.length > 0) {
        if (
          curChannel &&
          curChannel.channels &&
          curChannel.channels.length > 0
        ) {
          curChannel.channels.forEach((sub: Channel) => {
            channel.forEach((item: RSChannelManagement) => {
              if (
                item &&
                item.channel &&
                sub.id === item.channel.id &&
                sub.type === item.channel.type
              ) {
                str += `${item.name}，`;
              }
            });
          });
        }
      }
      str = str.substring(0, str.length - 1);
    }
    return str;
  }
  getTemplateLabel() {
    const labelList = this.data.coupons?.templateTag || []
    return labelList.reduce((acc:any, cur) => {
      acc.push(cur.tagValue)
      return acc
    }, []).join('、')
  }

  getSychChannel() {
    const sychChannelIds = this.data?.coupons?.sychChannel?.map(item => this.channelId(item)) ?? []
    const channles = this.channels.filter(item => sychChannelIds?.includes(this.channelId(item.channel!)))
    const channelNames = channles.map(item => item.name)
    return channelNames.join('、')
  }

  getMoney(money: number, qty: number, useThresholdType: any) {
    if (money) {
      let str: any = this.formatI18n(
        "/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级套餐",
        "{0}元"
      );
      str = str.replace(/\{0\}/g, money);
      return str;
    } else if (qty && useThresholdType === 'QTY') {
      let str: any = this.formatI18n(
        "/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级套餐",
        "{0}件"
      );
      str = str.replace(/\{0\}/g, qty);
      return str;
    } else {
      return this.formatI18n(
        "/营销/积分活动/积分活动/单品满数量加送积分活动/编辑页面/积分加送规则/不限制"
      );
    }
  }

  getCostPartr(party: any, percent: any) {
    // let str: any = this.formatI18n(
    //   "/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送券的时候/点击券名称/券承担方",
    //   "{0}承担用券金额{1}%"
    // );
    // str = str.replace(/\{0\}/g, this.getPartyNameById(party));
    // str = str.replace(
    //   /\{1\}/g,
    //   `&nbsp;<span style="font-weight: bold">${percent}</span>&nbsp;`
    // );
    let str = ''
    if (this.data.coupons && this.data.coupons!.costParties && this.data.coupons!.costParties.length > 0 && this.data.coupons!.costParties[0].bearType === ApportionType.AMOUNT) {
      str = str.replace('%', this.formatI18n('/公用/券模板/元'));
    }
    if (this.data.coupons && this.data.coupons!.costParty) {
      if (this.data.coupons.costParty.bearType == 'AMOUNT') {
        if (this.data.coupons.costParty.amountType == 'all') {
          str = `${this.getPartyNameById(party)}`;
          str = str +
            `&nbsp;<span style="font-weight: bold">${this.i18n('承担全部券抵扣金额')}</span>&nbsp;`
        } else if (this.data.coupons.costParty.amountType == 'part') {
          str = `${this.getPartyNameById(party)}`;
          if (percent == -1) {
            str = str +
            `&nbsp;<span style="font-weight: bold">${this.i18n('剩余券抵扣金额')}</span>&nbsp;`
          } else if (percent > 0) {
            str = str +
            `&nbsp;<span style="font-weight: bold">${this.i18n('承担最多')}${percent}${this.i18n('元')}</span>&nbsp;`
          }
        }
      } else if (this.data.coupons.costParty.bearType == 'PROPORTION') {
        str = this.formatI18n(
          "/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送券的时候/点击券名称/券承担方",
          "{0}承担用券金额{1}%"
        );
        str = str.replace(/\{0\}/g, this.getPartyNameById(party));
        str = str.replace(
          /\{1\}/g,
          `&nbsp;<span style="font-weight: bold">${percent}</span>&nbsp;`
        );
      }
    }
    // 承担全部券抵扣金额
    // 剩余券抵扣金额
    // 承担最多x元
    return str;
  }

  getFavValue() {
    return Tools.getFavValue(this.data.coupons!, this.getPartyNameById)
  }

  getAllCash(threshold: any) {
    let str: any = this.formatI18n(
      "/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送全场现金券的时候/用券门槛/可叠加使用",
      "可叠加使用，全场消费每满{0}元可用1张券"
    );
    str = str.replace(
      /\{0\}/g,
      `&nbsp;<span style="font-weight: bold">${Number(threshold).toFixed(
        2
      )}</span>&nbsp;`
    );
    return str;
  }

  getAllCashNo(threshold: any) {
    let str: any = this.formatI18n(
      "/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送全场现金券的时候/用券门槛/不可叠加使用",
      "不可叠加使用，全场消费满{0}元及以上可用1张券"
    );
    str = str.replace(
      /\{0\}/g,
      `&nbsp;<span style="font-weight: bold">${Number(threshold).toFixed(
        2
      )}</span>&nbsp;`
    );
    return str;
  }

  getGoodsCash(threshold: any) {
    let str: any = this.formatI18n(
      "/公用/券模板详情/商品现金券/用券门槛/可叠加使用，用券商品消费每满{0}元可用1张券"
    );
    str = str.replace(
      /\{0\}/g,
      `&nbsp;<span style="font-weight: bold">${Number(threshold).toFixed(
        2
      )}</span>&nbsp;`
    );
    return str;
  }

  getGoodsCashNo(threshold: any) {
    let str: any = this.formatI18n(
      "/公用/券模板详情/商品现金券/用券门槛/不可叠加使用，用券商品消费满{0}元及以上可用1张券"
    );
    str = str.replace(
      /\{0\}/g,
      `&nbsp;<span style="font-weight: bold">${Number(threshold).toFixed(
        2
      )}</span>&nbsp;`
    );
    return str;
  }

  getAllDiscountCoupon(threshold: any) {
    let str: any = this.formatI18n(
      "/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送全场折扣券的时候/用券门槛",
      "全场消费满{0}元及以上可以用1张券"
    );
    str = str.replace(
      /\{0\}/g,
      `&nbsp;<span style="font-weight: bold">${Number(threshold).toFixed(
        2
      )}</span>&nbsp;`
    );
    return str;
  }

  getGoodsDiscount(threshold: any, discount: any, value: any) {
    let str: any = this.formatI18n(
      "/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送单品折扣券的时候/用券门槛/用券商品满{0}件可享受其中{1}件{2}折"
    );
    str = str.replace(
      /\{0\}/g,
      `&nbsp;<span style="font-weight: bold">${threshold}</span>&nbsp;`
    );
    str = str.replace(
      /\{1\}/g,
      `&nbsp;<span style="font-weight: bold">${value}</span>&nbsp;`
    );
    str = str.replace(
      /\{2\}/g,
      `&nbsp;<span style="font-weight: bold">${discount}</span>&nbsp;`
    );
    return str;
  }

  getRfmTypeStep(amount: number) {
    let str: any = this.formatI18n(
      "/公用/券模板/商品折扣券/用券门槛/用券商品满{0}元及以上可用1张券"
    );
    str = str.replace(
      /\{0\}/g,
      `&nbsp;<span style="font-weight: bold">${Number(amount).toFixed(
        2
      )}</span>&nbsp;`
    );
    return str;
  }

  getMenkan(threshold: any) {
    if (threshold.thresholdType === "NONE") {
      return this.formatI18n("/公用/券模板/无门槛");
    } else {
      return `${this.formatI18n("/公用/券模板/满")}${threshold.threshold
        }${this.formatI18n("/公用/券模板/元")}${this.formatI18n(
          "/公用/券模板/可兑换"
        )}`;
    }
  }

  get getPointsAmount() {
    if (this.data.coupons!.pointsCouponAttribute) {
      return (
        this.formatI18n("/公用/券模板/券可换") +
        " " +
        this.data.coupons!.pointsCouponAttribute!.pointAmount +
        " " +
        this.formatI18n("/公用/券模板/积分")
      );
    }
  }

  get getIssueChannel() {
    let str = ''
    if (this.data && this.data.coupons && this.data.coupons.weimobCoupon) {
      if (this.data.coupons.weimobCoupon.issueChannel === IssueChannel.FREE) {
        str = this.formatI18n('/公用/券模板/仅免费发放') + ': '
        this.data.coupons.weimobCoupon.freeChannels.forEach((item,index,array) => {
          let split = index === array.length - 1 ? '' : '、'
          switch (item) {
            case FreeChannel.activity_issue:
              str += this.formatI18n('/公用/券模板/活动发券') + split
              break;
            case FreeChannel.directly_receive:
              const startTime = DateUtil.format(this.data.coupons?.weimobCoupon?.recommendStartTime!, "yyyy-MM-dd HH:mm:ss") || ''
              const endTime = DateUtil.format(this.data.coupons?.weimobCoupon?.recommendEndTime!, "yyyy-MM-dd HH:mm:ss") || ''
              const recommend = this.data.coupons?.weimobCoupon?.enableRecommend ? ` 开启推荐，推荐时间${startTime}至${endTime}` : '未开启推荐'
              str += this.formatI18n('/公用/券模板/直接领取') + recommend + split
              break;
            case FreeChannel.merchants_issue:
              str += this.formatI18n('/公用/券模板/商家发券') + split
              break;
            case FreeChannel.qiwei_issue:
              str += this.formatI18n('/公用/券模板/企微助手可发券') + split
              break;
            default:
              break;
          }
        });
      } else {
        str = this.formatI18n('/公用/券模板/付费购买')
      }
    }
    return str
  }

  get getUseScene() {
    let str = ''
    if (this.data && this.data.coupons && this.data.coupons.weimobCoupon) {
      const useScene = this.data.coupons.weimobCoupon.useScene
      if (useScene) {
        if (useScene.allSceneDTO) {
          str = this.i18n('全部场景')
        } else {
          let UseScenes: any[] = [
            {
              key: this.i18n('网店订单'),
              value: 1
            },
            {
              key: this.i18n('商家开单'),
              value: 10
            },
            {
              key: this.i18n('买家直接消费'),
              value: 8
            },
            {
              key: this.i18n('商家直接消费'),
              value: 9
            },
            {
              key: this.i18n('APP&收银台核销'),
              value: 3
            },
            {
              key: this.i18n('API核销'),
              value: 7
            },
            {
              key: this.i18n('扫码购'),
              value: 12
            }
          ]
          let scenes = (useScene.shoppingMallSceneList || []).map(x => {
            let obj = UseScenes.find(i => i.value == x)
            return obj ? obj.key : ''
          })
          str = this.i18n('指定场景')+'：' + scenes.join(', ')
        }
      }
    return str
    }
  }

  get getSpecialSetup() {
    let str = ''
    if (this.data.coupons && this.data.coupons!.useThreshold && this.data.coupons.specialPriceCouponAttribute) {
      str = `${this.formatI18n("/公用/券模板", "订单满")} 
          ${this.data.coupons.useThreshold.threshold} 
          ${this.formatI18n("/公用/券模板", "元")}，
          ${this.formatI18n("/公用/券模板", "用券商品第")} 
          ${this.data.coupons.specialPriceCouponAttribute.favSegment} 
          ${this.formatI18n("/公用/券模板", "件享")} 
          ${this.data.coupons.specialPriceCouponAttribute.specialPrice} 
          ${this.formatI18n("/公用/券模板", "元购")}
          `
    }

    return str
  }

  getWeeks(value: any) {
    if (value && value.length > 0) {
      let arr = value;
      value.forEach((item: any, index: number) => {
        if (item === 1) {
          arr[index] = this.formatI18n("/公用/券模板", "周一");
        }
        if (item === 2) {
          arr[index] = this.formatI18n("/公用/券模板", "周二");
        }
        if (item === 3) {
          arr[index] = this.formatI18n("/公用/券模板", "周三");
        }
        if (item === 4) {
          arr[index] = this.formatI18n("/公用/券模板", "周四");
        }
        if (item === 5) {
          arr[index] = this.formatI18n("/公用/券模板", "周五");
        }
        if (item === 6) {
          arr[index] = this.formatI18n("/公用/券模板", "周六");
        }
        if (item === 7) {
          arr[index] = this.formatI18n("/公用/券模板", "周日");
        }
      });
      return arr;
    } else {
      return [];
    }
  }

  getSendCouponLimit(maxPerIssueTimes: number, maxIssueTimes: number) {
    if (maxIssueTimes && maxIssueTimes) {
      let str: any = this.formatI18n(
        "/营销/券礼包活动/新建商品满额发券活动/详情界面/发券数限制",
        "每人限{0}张，活动总限{1}张"
      );
      str = str.replace(/\{0\}/g, maxPerIssueTimes);
      str = str.replace(/\{1\}/g, maxIssueTimes);
      return str;
    } else {
      return "--";
    }
  }

  getendCouponStep(type: string, threshold: number, value: number) {
    if (type === "NONREUSEABLE") {
      let str: any = this.formatI18n(
        "/营销/券礼包活动/新建商品满额发券活动/详情界面/发券商品",
        "不可累加发券，发券商品消费满{0}元及以上发{1}张券"
      );
      str = str.replace(/\{0\}/g, threshold);
      str = str.replace(/\{1\}/g, value);
      return str;
    } else {
      let str: any = this.formatI18n(
        "/营销/券礼包活动/新建商品满额发券活动/详情界面/发券商品",
        "可累加发券，发券商品消费每满{0}元发{1}张券"
      );
      str = str.replace(/\{0\}/g, threshold);
      str = str.replace(/\{1\}/g, value);
      return str;
    }
  }

  getImportCount(count: number) {
    let str: any = this.formatI18n(
      "/营销/券礼包活动/群发券详情界面/导入发券对象导入成功后",
      "当前已导入{0}个会员，每个活动最多支持给20000个会员发券。"
    );
    str = str.replace(/\{0\}/g, " " + count + " ");
    str = str.replace(
      /20000/g,
      `<span style="color: #FF9933">&nbsp;20000&nbsp;</span>`
    );
    return str;
  }

  getCountByPerson(count: number) {
    let str: any = this.formatI18n(
      "/营销/券礼包活动/群发券详情界面/发券数量",
      "{0}张/人"
    );
    str = str.replace(/\{0\}/g, count);
    return str;
  }

  getWx(count: number) {
    if (count > 0) {
      let str: any = this.formatI18n(
        "/营销/券礼包活动/群发券详情界面/投放渠道",
        "微信模版消息  额外送{0}积分"
      );
      str = str.replace(/\{0\}/g, count);
      return str;
    } else {
      return "--";
    }
  }

  getSendCouponObj(importCount: number, issueCount: number) {
    let str: any = this.formatI18n(
      "/营销/券礼包活动/群发券详情界面/进行中或者已结束状态下",
      "共{0}条发券对象，已发{1}张券"
    );
    str = str.replace(
      /\{0\}/g,
      `<span style="font-weight: 700;color: #0000FF">&nbsp;&nbsp;${importCount}&nbsp;&nbsp;</span>`
    );
    if (issueCount <= 0 || !issueCount) {
      str = str.replace(
        /\{1\}/g,
        `<span style="font-weight: 700;color: #0000FF">&nbsp;&nbsp;0&nbsp;&nbsp;</span>`
      );
    } else {
      str = str.replace(
        /\{1\}/g,
        `<span style="font-weight: 700;color: #0000FF">&nbsp;&nbsp;${issueCount}&nbsp;&nbsp;</span>`
      );
    }

    return str;
  }

  getNoStart(importCount: number) {
    let str: any = this.formatI18n(
      "/营销/券礼包活动/群发券详情界面/未开始状态下",
      "当前共有{0}条发券对象"
    );
    str = str.replace(
      /\{0\}/g,
      `<span style="font-weight: 700;color: #0000FF">&nbsp;&nbsp;${importCount}&nbsp;&nbsp;</span>`
    );
    return str;
  }

  getPartyNameById(id: string) {
    let str = "";
    if (this.parties && this.parties.length > 0) {
      this.parties.forEach((item: any) => {
        if (item.costParty.id === id) {
          str = item.costParty.name;
        }
      });
    }
    return str;
  }

  private channelId(item: Channel) {
		return `${item.type}${item.id}`
	}

  private getCostParty() {
    let params: RSCostPartyFilter = new RSCostPartyFilter();
    params.page = 0;
    params.pageSize = 0;
    CostPartyApi.query(params)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.parties = resp.data;
        }
      })
      .catch((error: any) => {
        this.$message.error(error.message);
      });
  }

  private getChannels() {
    let param: RSChannelManagementFilter = new RSChannelManagementFilter();
    ChannelManagementApi.query(param)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.channels = resp.data;
        }
      })
      .catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
  }
  viewSpecialGoods() {
    ;(this.$refs.SpecialGoodsDialog as any).open()
  }
  viewCBSpecialGoods() {
    ;(this.$refs.CBearSpecialGoodsDtl as any).open()
  }
  openPromotionShow() {
    ;(this.$refs.promotionShow as any).open()
  }
}
