export default class DataAnalysisFilter {
  // 门店代码等于
  storeIdEquals: Nullable<string> = null
  // 日期范围：TODAY-今天；LST_SEVEN_DAY-仅七天；LST_THIRTY_DAY-近三十天
  dateRangeType: Nullable<string> = null
  // 日期范围：DAY-昨天；WEEK-本周；MONTH-本月
  dateUnitEquals: Nullable<string> = null
  // 开始时间大于等于
  beginAfterOrEquals: Nullable<Date> = null
  // 结束时间小于
  endBefore: Nullable<Date> = null
  // 页码
  page: Nullable<number> = null
  // 页面大小
  pageSize: Nullable<number> = null
}