import ApiClient from 'http/ApiClient'
import Response from 'model/response/Response'
import Send<PERSON><PERSON><PERSON>n<PERSON><PERSON>ult from 'model/coupon/report/SendCouponResult'
import SendCouponTotal from 'model/coupon/report/SendCouponTotal'
import SendWaterResult from 'model/coupon/report/SendWaterResult'
import Use<PERSON><PERSON>Result from 'model/coupon/report/UseWaterResult'
import SendCouponGoodsDtlResult from 'model/coupon/report/SendCouponGoodsDtlResult'
import UseCouponGoodsDtlResult from 'model/coupon/report/UseCouponGoodsDtlResult'
import UseCouponResult from 'model/coupon/report/UseCouponResult'

const qs = require('qs');
export default class CouponReportApi {

  static getSendCouponList(params: any): Promise<Response<SendCouponResult>> {

    return ApiClient.server().post(`crm-web/coupon/report/query.hd`, qs.stringify(params), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }).then((res) => {
      return res.data
    })
  }
  static getSendCouponTotal(params: any): Promise<Response<SendCouponTotal>> {

    return ApiClient.server().post(`crm-web/coupon/report/total.hd`, qs.stringify(params), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }).then((res) => {
      return res.data
    })
  }
  static getUseCouponList(params: any): Promise<Response<UseCouponResult>> {

    return ApiClient.server().post(`crm-web/coupon/report/query.hd`, qs.stringify(params), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }).then((res) => {
      return res.data
    })
  }
  static getSendWaterList(params: any): Promise<Response<SendWaterResult>> {

    return ApiClient.server().post(`crm-web/coupon/report/query.hd`, qs.stringify(params), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }).then((res) => {
      return res.data
    })
  }
  static getUseWaterList(params: any): Promise<Response<UseWaterResult>> {

    return ApiClient.server().post(`crm-web/coupon/report/query.hd`, qs.stringify(params), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }).then((res) => {
      return res.data
    })
  }
  static getSendCouponGoddsDtlList(params: any): Promise<Response<SendCouponGoodsDtlResult>> {

    return ApiClient.server().post(`crm-web/coupon/report/query.hd`, qs.stringify(params), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }).then((res) => {
      return res.data
    })
  }
  static getUseCouponGoddsDtlList(params: any): Promise<Response<UseCouponGoodsDtlResult>> {

    return ApiClient.server().post(`crm-web/coupon/report/query.hd`, qs.stringify(params), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }).then((res) => {
      return res.data
    })
  }
  static getUseCouponTotal(params: any): Promise<Response<SendCouponTotal>> {

    return ApiClient.server().post(`crm-web/coupon/report/total.hd`, qs.stringify(params), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }).then((res) => {
      return res.data
    })
  }
}
