/*
 * @Author: 黎钰龙
 * @Date: 2023-03-02 17:58:23
 * @LastEditTime: 2023-03-05 16:17:58
 * @LastEditors: liyulong <EMAIL>
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\benefit\BEquityCardExpiryRule.ts
 * 记得注释
 */
import BEquityCardValidity from 'model/benefit/BEquityCardValidity'

// 权益卡有效期规则明细
export default class BEquityCardExpiryRule {
  // 有效期
  validityInfo: Nullable<BEquityCardValidity> = new BEquityCardValidity()
  // 售价
  price: Nullable<number> = null
  // 续费价格
  renewPrice: Nullable<number> = null
}