<template>
  <div class="marketcenter-member-list">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
      <!--{{$t('m.pages.i18n[\"/会员/会员资料/批量导入\"]')}}-->
      <el-button @click="doModifyStore"
                 v-if="hasOptionPermission('/会员/会员管理/营销中心会员', '修改归属门店')">
        {{ formatI18n("/会员/会员资料", "批量修改归属门店") }}
      </el-button>
      <el-button @click="doBatchImport"
                 v-if="hasOptionPermission('/会员/会员管理/营销中心会员', '批量导入')">
        {{ formatI18n("/会员/会员资料", "批量导入") }}
      </el-button>
      <el-button @click="doBatchExport"
                 v-if="hasOptionPermission('/会员/会员管理/营销中心会员', '批量导出')">
        {{ formatI18n("/会员/会员资料", "批量导出") }}
      </el-button>
      </template>
    </BreadCrume>
    <ListWrapper class="current-page">
      <template slot="query">
      <MyQueryCmp @reset="onReset"
                  @search="onSearch"
                  @toggle="doToggle">
        <el-row>
          <el-col :span="8">
            <form-item :label="formatI18n('/会员/会员资料', '会员')">
              <el-input :placeholder=identPlaceholder
                        v-model="identCode">
                <el-select style="width: 100px;"
                           v-model="identType"
                           slot="prepend"
                           @change="identTypeChange()">
                  <el-option :label="formatI18n('/会员/会员资料','不限')"
                             value="all"></el-option>
                  <el-option :label="formatI18n('/会员/会员资料', '手机号')"
                             value="mobile"></el-option>
                  <el-option :label="formatI18n('/会员/会员资料', '实体卡号')"
                             value="hdCardCardNumber"></el-option>
                  <el-option :label="formatI18n('/会员/会员资料', '会员号')"
                             value="crmCode"></el-option>
                </el-select>
              </el-input>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="formatI18n('/会员/会员资料', '会员等级')">
              <el-select :placeholder="formatI18n('/公用/下拉框/提示', '全部')"
                         v-model="query.gradeEquals">
                <el-option :label="formatI18n('/公用/下拉框/提示', '全部')"
                           value="">{{ formatI18n("/公用/下拉框/提示", "全部") }}
                </el-option>
                <el-option :label="item.name"
                           :value="item.code"
                           v-for="(item, index) in memberLevel"
                           :key="index">
                  [{{ item.code }}]{{ item.name }}
                </el-option>
              </el-select>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="formatI18n('/会员/会员资料', '会员状态')">
              <el-select :placeholder="formatI18n('/公用/下拉框/提示', '全部')"
                         v-model="query.stateEquals">
                <el-option :label="formatI18n('/公用/下拉框/提示', '全部')"
                           value="">{{ formatI18n("/公用/下拉框/提示", "全部") }}
                </el-option>
                <el-option :label="formatI18n('/会员/会员资料', '使用中')"
                           value="Using">{{ formatI18n("/会员/会员资料", "使用中") }}
                </el-option>
                <el-option :label="formatI18n('/会员/会员资料', '已冻结')"
                           value="Blocked">
                  {{ formatI18n("/会员/会员资料", "已冻结") }}
                </el-option>
                <el-option :label="formatI18n('/会员/会员资料', '未激活')"
                           value="Unactivated">
                  {{ formatI18n("/会员/会员资料", "未激活") }}
                </el-option>
              </el-select>
            </form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <form-item :label="formatI18n('/会员/会员资料', '归属门店')">
              <el-select clearable
                         filterable
                         remote
                         :remote-method="doRemoteMethod"
                         :placeholder="formatI18n('/会员/会员资料', '请输入归属门店')"
                         v-model="query.ownerStoreIdEquals">
                <el-option :label="formatI18n('/公用/下拉框/提示', '全部')"
                           value="">{{ formatI18n("/公用/下拉框/提示", "全部") }}
                </el-option>
                <el-option :label="item.org.name"
                           :value="item.org.id"
                           v-for="(item, index) in stores"
                           :key="index">
                  [{{ item.org.id }}]{{ item.org.name }}
                </el-option>
              </el-select>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="formatI18n('/会员/会员资料', '首次注册渠道')">
              <el-select :placeholder="formatI18n('/公用/下拉框/提示', '全部')"
                         v-model="searchChannel"
                         @change="channelChange"
                         value-key="channel.typeId">
                <el-option :label="formatI18n('/公用/下拉框/提示', '全部')"
                           :value="null">{{ formatI18n("/公用/下拉框/提示", "全部") }}
                </el-option>
                <!-- <el-option :label="formatI18n('/会员/会员资料', '第三方')" value="third">
                  {{ formatI18n('/会员/会员资料', '第三方') }}
                </el-option>
                <el-option :label="formatI18n('/会员/会员资料', '支付宝')" value="alipay">
                  {{ formatI18n('/会员/会员资料', '支付宝') }}
                </el-option>
                <el-option :label="formatI18n('/会员/会员资料', '微信')" value="weixin">{{ formatI18n('/会员/会员资料', '微信') }}
                </el-option>
                <el-option :label="formatI18n('/会员/会员资料', '门店注册')" value="store">
                  {{ formatI18n('/会员/会员资料', '门店注册') }}
                </el-option>
                <el-option label="CRM" value="phoenix">CRM</el-option> -->
                <el-option :label="item.name"
                           :value="item"
                           v-for="(item, index) in  channels"
                           :key="index">

                </el-option>
              </el-select>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="i18n('会员渠道')">
              <el-select :placeholder="formatI18n('/公用/下拉框/提示', '全部')"
                         v-model="memberChannelTypeFilter"
                         @change="onMemberChannelTypeChange"
                         value-key="code"
                         multiple>
                <el-option :label="item.name"
                           :value="item"
                           v-for="(item, index) in  memberChannelTypes"
                           :key="index">
                </el-option>
              </el-select>
            </form-item>
          </el-col>
        </el-row>
        <template slot="opened">
        <el-row>
          <el-col :span="8">
            <form-item :label="i18n('会员渠道ID')">
              <el-input :placeholder="i18n('请输入会员渠道ID')"
                        v-model="query.memberChannelIdEquals"
                        :disabled="!query.memberChannelTypeIn"></el-input>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="i18n('首次注册日期')">
              <el-date-picker :end-placeholder="formatI18n('/会员/会员资料', '结束日期')"
                              :start-placeholder="formatI18n('/会员/会员资料', '开始日期')"
                              format="yyyy-MM-dd"
                              range-separator="-"
                              type="daterange"
                              v-model="registerDate"
                              value-format="yyyy-MM-dd">
              </el-date-picker>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="formatI18n('/会员/会员资料', '激活日期')">
              <el-date-picker :end-placeholder="formatI18n('/会员/会员资料', '结束日期')"
                              :start-placeholder="formatI18n('/会员/会员资料', '开始日期')"
                              format="yyyy-MM-dd"
                              range-separator="-"
                              type="daterange"
                              v-model="activeDate"
                              value-format="yyyy-MM-dd">
              </el-date-picker>
            </form-item>
          </el-col>
          <!--业务数据value,常浩说不做国际化-->
          <!--              <el-col :span="8">-->
          <!--                <form-item :label="formatI18n('/会员/会员资料', '招募方式')">-->
          <!--                  <el-select :placeholder="formatI18n('/公用/下拉框/提示', '全部')" v-model="query.recruitmentMethodEquals">-->
          <!--                    <el-option :label="formatI18n('/公用/下拉框/提示', '全部')" value="">{{ formatI18n('/公用/下拉框/提示', '全部') }}-->
          <!--                    </el-option>-->
          <!--                    <el-option :label="formatI18n('/会员/会员资料', '支付即会员')" value="支付即会员">-->
          <!--                      {{ formatI18n('/会员/会员资料', '支付即会员') }}-->
          <!--                    </el-option>-->
          <!--                    <el-option :label="formatI18n('/会员/会员资料', '扫码即会员')" value="扫码即会员">-->
          <!--                      {{ formatI18n('/会员/会员资料', '扫码即会员') }}-->
          <!--                    </el-option>-->
          <!--                    <el-option :label="formatI18n('/会员/会员资料', '注册')" value="注册">{{ formatI18n('/会员/会员资料', '注册') }}-->
          <!--                    </el-option>-->
          <!--                    <el-option :label="formatI18n('/会员/会员资料', '小程序')" value="小程序">{{ formatI18n('/会员/会员资料', '小程序') }}-->
          <!--                    </el-option>-->
          <!--                  </el-select>-->
          <!--                </form-item>-->
          <!--              </el-col>-->
        </el-row>
        <el-row>
          <el-col :span="8">
            <form-item :label="formatI18n('/会员/会员资料', '邀请人会员')">
              <el-input :placeholder="formatI18n('/会员/会员资料', '请输入邀请人手机号/会员号')"
                        v-model="query.refereeCodeEquals"></el-input>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="formatI18n('/会员/会员资料', '邀请人员工')">
              <el-input :placeholder="formatI18n('/会员/会员资料', '请输入邀请人员工姓名/代码')"
                        v-model="query.referredEmployeeIdEquals"></el-input>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="isShowFirstAndLastName ? i18n('名') : formatI18n('/会员/会员资料', '姓名')">
              <el-input :placeholder="i18n('请输入')"
                        v-model="query.nameLikes"></el-input>
            </form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="16">
            <form-item :label="formatI18n('/会员/会员资料', '生日')">
              <div style="display: flex">
                <el-select v-model="birthdayType"
                           @change="birthdayTypeChange()"
                           style="width: 20%">
                  <el-option :label="formatI18n('/会员/会员资料/生日/按日期')"
                             value="date"></el-option>
                  <el-option :label="formatI18n('/会员/会员资料/生日/按年份')"
                             value="year"></el-option>
                </el-select>
                <div v-show="birthdayType==='date'"
                     style="display: inline-block;width: 80%;">
                  <el-date-picker :end-placeholder="formatI18n('/会员/会员资料', '结束日期')"
                                  :start-placeholder="formatI18n('/会员/会员资料', '开始日期')"
                                  format="MM-dd"
                                  range-separator="-"
                                  type="daterange"
                                  v-model="birthdayDate"
                                  value-format="MM-dd">
                  </el-date-picker>
                </div>
                <div v-show="birthdayType==='year'"
                     style="display: flex;align-items: center;width: 80%;">
                  <el-date-picker style="width: 100%"
                                  :placeholder="formatI18n('/会员/会员资料/生日/按年份/开始年份')"
                                  type="year"
                                  v-model="birthdayBegin">
                  </el-date-picker>
                  -
                  <el-date-picker style="width: 100%"
                                  :placeholder="formatI18n('/会员/会员资料/生日/按年份/结束年份')"
                                  type="year"
                                  v-model="birthdayEnd">
                  </el-date-picker>
                </div>
              </div>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="formatI18n('/会员/会员资料', '邮箱')">
              <el-input :placeholder="formatI18n('/会员/会员资料', '请输入邮箱')"
                        v-model="query.mailLikes"></el-input>
            </form-item>
          </el-col>
          <!--              <el-col :span="8">-->
          <!--                <form-item :label="formatI18n('/会员/会员资料', '创建日期')">-->
          <!--                  <el-date-picker :end-placeholder="formatI18n('/会员/会员资料', '结束日期')" :start-placeholder="formatI18n('/会员/会员资料', '开始日期')"-->
          <!--                    format="yyyy-MM-dd" range-separator="-" type="daterange" v-model="createDate" value-format="yyyy-MM-dd">-->
          <!--                  </el-date-picker>-->
          <!--                </form-item>-->
          <!--              </el-col>-->
        </el-row>
        <el-row>
          <el-col :span="8">
            <form-item :label="formatI18n('/会员/会员资料', '性别')">
              <el-select :placeholder="formatI18n('/会员/会员资料', '不限')"
                         v-model="query.genderEquals">
                <el-option :label="formatI18n('/会员/会员资料', '不限')"
                           value="">{{ formatI18n("/会员/会员资料", "不限") }}
                </el-option>
                <el-option :label="formatI18n('/会员/会员资料', '男')"
                           value="男">{{ formatI18n("/会员/会员资料", "男") }}
                </el-option>
                <el-option :label="formatI18n('/会员/会员资料', '女')"
                           value="女">{{ formatI18n("/会员/会员资料", "女") }}
                </el-option>
              </el-select>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="i18n('昵称')">
              <el-input :placeholder="i18n('请输入昵称')"
                        v-model="query.nickNameLikes"></el-input>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="formatI18n('/会员/会员资料', '归属营销中心')">
              <el-select value-key="id"
                         v-model="fromMarketingCenter"
                         :placeholder="formatI18n('/资料/渠道/请选择')">
                <el-option :value="null"
                           label="全部">全部
                </el-option>
                <el-option v-for="(value) in marketingCenterData"
                           :key="value.id"
                           :value="value"
                           :label="'['+value.id+']'+value.name">
                  [{{ value.id }}]{{ value.name }}
                </el-option>
              </el-select>
            </form-item>
          </el-col>
        </el-row>
        </template>
        <template slot="openedQuery">
        </template>
      </MyQueryCmp>
      </template>
      <template slot="list">
      <el-table
        :data="tableData"
        style="width: 100%">
        <el-table-column :label="formatI18n('/会员/会员资料', '会员号')"
                         fixed
                         prop="crmCode"
                         width="180">
          <template slot-scope="scope">
          <el-button @click="doGoDtl(scope.row)"
                     type="text"
                     v-if="hasOptionPermission('/会员/会员管理/营销中心会员', '会员资料查看')">
            {{ scope.row.crmCode }}
          </el-button>
          </template>
        </el-table-column>
        <el-table-column :label="formatI18n('/会员/会员资料', '手机号')"
                         fixed
                         prop="mobile"
                         width="180">
          <template slot-scope="scope">
          {{ scope.row.mobile | strFormat }}
          </template>
        </el-table-column>
        <el-table-column :label="formatI18n('/会员/会员资料', '实体卡号')"
                         prop="hdcardCardNo"
                         width="180">
          <template slot-scope="scope">
          <div>{{ scope.row.hdCardCardNum | strFormat }}</div>
          </template>
        </el-table-column>
        <el-table-column :label="formatI18n('/会员/会员资料', '姓名')"
                         prop="name"
                         width="120">
          <template slot-scope="scope">
          {{ scope.row.name | strFormat }}
          </template>
        </el-table-column>
        <el-table-column :label="formatI18n('/会员/会员资料', '会员等级')"
                         prop="gradeName"
                         width="120">
          <template slot-scope="scope">
          {{ scope.row.gradeName | strFormat }}
          </template>
        </el-table-column>
        <el-table-column :label="formatI18n('/会员/会员资料', '会员状态')"
                         prop="state"
                         width="120">
          <template slot-scope="scope">
          <el-tag size="small"
                  type="success"
                  v-if="scope.row.state === 'Using'">
            {{ formatI18n("/会员/会员资料", "使用中") }}
          </el-tag>
          <el-tag size="small"
                  type="danger"
                  v-if="scope.row.state === 'Blocked'">
            {{ formatI18n("/会员/会员资料", "已冻结") }}
          </el-tag>
          <el-tag size="small"
                  type="warning"
                  v-if="scope.row.state === 'Unactivated'">
            {{ formatI18n("/会员/会员资料", "未激活") }}
          </el-tag>
          <el-tag size="small"
                  type="info"
                  v-if="scope.row.state === 'Canceled'">
            {{ formatI18n("/会员/会员资料", "已注销") }}
          </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="formatI18n('/会员/会员资料', '归属门店')"
                         prop="ownStore.name"
                         width="270">
          <template slot-scope="scope">
          <el-tooltip v-if="scope.row.ownStore && scope.row.ownStore.id"
                      class="item"
                      effect="dark"
                      :content="'['+scope.row.ownStore.id+']'+scope.row.ownStore.name"
                      placement="right-end">
            <div style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">
              [{{ scope.row.ownStore.id }}]{{ scope.row.ownStore.name }}
            </div>
          </el-tooltip>
          <el-tooltip v-else
                      class="item"
                      effect="dark"
                      :content="'--'"
                      placement="right-end">
            <div>--</div>
          </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('首次注册日期')"
                         prop="registerTime"
                         width="150">
          <template slot-scope="scope">
          <div>{{ scope.row.registerTime | dateFormate3 }}</div>
          </template>
        </el-table-column>
        <el-table-column :label="formatI18n('/会员/会员资料', '激活日期')"
                         prop="activateTime"
                         width="150">
          <template slot-scope="scope">
          <div>{{ scope.row.activateTime | dateFormate3 }}</div>
          </template>
        </el-table-column>
        <!--          <el-table-column :label="formatI18n('/会员/会员资料', '创建日期')" prop="created" width="150">-->
        <!--            <template slot-scope="scope">-->
        <!--              <div>{{ scope.row.created | dateFormate3 }}</div>-->
        <!--            </template>-->
        <!--          </el-table-column>-->
        <el-table-column :label="i18n( '首次注册渠道')"
                         prop="registerChannelName"
                         width="110">
          <!--<template slot-scope="scope">-->
          <!--<div>{{scope.row.registerChannel.id}}</div>-->
          <!--</template>-->
        </el-table-column>
        <el-table-column :label="i18n( '会员渠道')"
                         width="110">
          <template slot-scope="scope">
          <div>{{ getMemberChannelLabel(scope.row.memberChannels) }}</div>
          </template>
        </el-table-column>
        <el-table-column :label="formatI18n('/会员/会员资料', '招募方式')"
                         prop="registerScene"
                         width="100">
          <template slot-scope="scope">
          <div>{{ scope.row.registerSceneI18n | strFormat }}</div>
          </template>
        </el-table-column>
        <el-table-column :label="formatI18n('/会员/会员资料', '性别')"
                         prop="gender"
                         width="100">
          <template slot-scope="scope">
          <div v-if="scope.row.gender">{{ scope.row.genderI18n }}</div>
          <div v-else>{{ formatI18n("/会员/会员资料", "未知") }}</div>
          </template>
        </el-table-column>
        <el-table-column :label="formatI18n('/会员/会员资料', '生日')"
                         prop="birthday"
                         width="100">
          <template slot-scope="scope">
          <div>{{ scope.row.birthday }}</div>
          </template>
        </el-table-column>
      </el-table>
      </template>
      <!--分页栏-->
      <template slot="page">
      <el-pagination
        :current-page="page.currentPage"
        :page-size="page.size"
        :page-sizes="[10, 20, 30, 40]"
        :total="page.total"
        @current-change="onHandleCurrentChange"
        @size-change="onHandleSizeChange"
        background
        layout="total, prev, pager, next, sizes,  jumper">
      </el-pagination>
      </template>
    </ListWrapper>
    <UploadFileModal
      :dialogShow="uploadDialogShow"
      :isMarketCenter="true"
      @dialogClose="doDialogClose"
      @upload-success="doUploadSuccess">
    </UploadFileModal>
    <DownloadCenterDialog
      :dialogvisiable="fileDialogvisiable"
      :showTip="showTip"
      @dialogClose="doDownloadDialogClose"
    ></DownloadCenterDialog>
    <ExportConfirm
      :dialogShow="exportDialogShow"
      @dialogClose="doExportDialogClose"
      @summit="doSummit">
    </ExportConfirm>
    <ModifyStoreDialog
      :dialogShow="storeDialogShow"
      @dialogClose="storeDialogClose"
      :isMarketCenter="true"
      :stores="stores"
      @upload-success="storeUploadSuccess">
    </ModifyStoreDialog>
  </div>
</template>

<script lang="ts"
        src="./MarketCenterMemberList.ts">
</script>

<style lang="scss"
       scoped>
.marketcenter-member-list {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: hidden;

  .title {
    height: 60px;
    margin-top: 20px;
    line-height: 60px;
    padding-left: 20px;
    border-bottom: 1px solid #dfe2e5;
    font-size: 12px;
  }

  .current-page {
    height: calc(100% - 60px);
    overflow-y: auto;

    .el-select {
      width: 100%;
    }
  }

  .birthdayTypeYear {
    width: 20%;
  }

  .birthdayTypeDate {
    width: 20%;
    top: -5px;
  }

  ::v-deep .el-input-group__append,::v-deep .el-input-group__prepend {
    background-color: white;
  }

  ::v-deep .el-input-group__append .el-button,
  ::v-deep .el-input-group__append .el-input,
  ::v-deep .el-input-group__prepend .el-button,
  ::v-deep .el-input-group__prepend .el-input {
    width: 100px;
  }

  .el-range-editor.el-input__inner {
    width: 100%;
  }
}
</style>
