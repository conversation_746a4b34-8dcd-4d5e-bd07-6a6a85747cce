import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import FormItem from 'cmp/formitem/FormItem';
import I18nPage from 'common/I18nDecorator';
import 'echarts/lib/chart/treemap'
import 'echarts/lib/component/tooltip';
import { Component, Vue } from 'vue-property-decorator';
import MemberAnalysisApi from 'http/analysis/MemberAnalysisApi';
import MemberAnalysis from 'model/report/memberanalysis/MemberAnalysis';
import CommonUtil from 'util/CommonUtil';
import MemberAnalysisListFilter from 'model/report/memberanalysis/MemberAnalysisListFilter';
import MyQueryCmp from 'cmp/querycondition/MyQueryCmp';


@Component({
  name: 'VipAnalysis',
  components: {
    BreadCrume,
    FormItem,
    MyQueryCmp
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/数据/数据洞察'
  ],
  auto: true
})
export default class VipAnalysis extends Vue {
  $refs: any
  $echarts: any
  dialogVisible: boolean = false
  nameLikes: Nullable<string> = null  // 报表名称模糊查询
  labelForm: any = { // 弹窗表单数据
    labelName: ''
  }
  queryAnalysis: MemberAnalysis = new MemberAnalysis() // 修改名称时，查询对应卡片
  analysisList: MemberAnalysis[] = [] // 会员分析列表

  page = {
    page: 0,
    pageSize: 10,
    total: 0
  }
  pageData: any[] = []



  get panelArray() {
    return [
      {
        name: this.i18n('/公用/菜单/会员分析'),
        url: ''
      }
    ]
  }

  created() {
    this.getMemberAnalysisList()
  }

  // 初始化获取会员分析列表
  private getMemberAnalysisList() {
    const loading = CommonUtil.Loading()
    let data = new MemberAnalysisListFilter()
    data.page = this.page.page
    data.pageSize = this.page.pageSize
    data.nameLikes = this.nameLikes
    MemberAnalysisApi.queryList(data).then((resp) => {
      if (resp && resp.code === 2000) {
        this.analysisList = resp.data || []
        this.page.total = resp.total || 0
      } else {
        this.$message.error(resp.msg || this.i18n('获取会员分析列表失败'))
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    }).finally(() => {
      loading.close()
    })
  }

  // 关闭弹窗
  handleClose() {
    this.dialogVisible = false
  }

  // 修改报表名称
  handleChangeName(item: MemberAnalysis) {
    // 先根据item 获取到对应的 会员分析名称
    this.labelForm.labelName = item.name
    this.queryAnalysis = item
    this.dialogVisible = true
  }


  get dialogRules() {
    return {
      labelName: [{ required: true, message: this.i18n('/数据/数据洞察/列表页/报表名称不能为空'), trigger: ['blur', 'change'] }]
    }
  }

  // 弹窗提交，修改报表名称
  handleChangeNameSubmit() {
    this.$refs.labelForm.validate().then(() => {
      const loading = CommonUtil.Loading()
      let data: MemberAnalysis = this.queryAnalysis
      data.name = this.labelForm.labelName
      MemberAnalysisApi.modify(data).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.i18n('操作成功'))
          this.dialogVisible = false
          this.getMemberAnalysisList()
        } else {
          this.$message.error(resp.msg || this.i18n('操作失败'))
        }
      }).catch((error) => {
        this.$message.error(error.message || this.i18n('内部异常'))
      }).finally(() => {
        loading.close()
      })

    })

  }

  // 跳转会员分析详情
  doGoDtl(type: string, item: MemberAnalysis | null) {
    if (type === 'edit' || type === 'dtl') {
      let id = item?.uuid || ''
      this.$router.push({ name: "vip-analysis-dtl", query: { type: type, id } });
    } else {
      this.$router.push({ name: "vip-analysis-dtl", query: { type: 'create' } });
    }
  }

  // 重置查询条件
  doReset() {
    this.nameLikes = null
    this.page.page = 0
    this.getMemberAnalysisList()
  }

  // 查询
  onSearch() {
    this.page.page = 0
    this.getMemberAnalysisList()
  }


  // 删除会员分析
  removeCard(item: MemberAnalysis) {
    this.$confirm(
      this.i18n('/数据/数据洞察/列表页/删除报表后不可恢复，请谨慎操作'),
      this.i18n('/数据/数据洞察/列表页/是否确认删除'),
      {
        cancelButtonText: this.i18n('/公用/按钮/取消'),
        confirmButtonText: this.formatI18n("/公用/按钮", "确定"),
        type: 'warning',
      }
    ).then(() => {
      const loading = CommonUtil.Loading()
      MemberAnalysisApi.remove(item.uuid ? item.uuid : '').then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.i18n('删除成功'))
          this.getMemberAnalysisList()
        } else {
          this.$message.error(resp.msg || this.i18n('删除失败'))
        }
      }).catch((error) => {
        this.$message.error(error.message || this.i18n('内部异常'))
      }).finally(() => {
        loading.close()
      })
    });
  }

  onHandleCurrentChange(val: number) {
    this.page.page = val - 1;
    this.getMemberAnalysisList();
  }

  onHandleSizeChange(val: number) {
    this.page.page = 0;
    this.page.pageSize = val;
    this.getMemberAnalysisList();
  }

};