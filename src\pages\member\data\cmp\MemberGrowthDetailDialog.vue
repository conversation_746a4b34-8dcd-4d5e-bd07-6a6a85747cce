<template>
  <el-dialog :before-close="beforeClose"
             :close-on-click-modal="false"
             :title="i18n('成长值明细')"
             :visible.sync="value">
    <ListWrapper :show-query="false">
      <template slot="list">
      <el-table :data="queryData"
                style="width: 100%"
                border>
        <el-table-column :label="i18n('数量')"
                         width="104">
          <template slot-scope="scope">
          <span :style="'color:'+getGrowthValueColor(scope.row.growthValue)">
          {{ getGrowthValueLabel(scope.row.growthValue) }}
          </span>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('发生时间')"
                         width="200">
          <template slot-scope="scope">
          {{ scope.row.occurredTime | dateFormate3 }}
          </template>
        </el-table-column>
        <el-table-column :label="i18n('变动原因')">
          <template slot-scope="scope">
          {{ scope.row.reason | strFormat }}
          </template>
        </el-table-column>
      </el-table>
      </template>
      <template slot="page">
      <el-pagination
        :current-page="page.currentPage"
        :page-size="page.size"
        :page-sizes="[10, 20, 30, 40]"
        :total="page.total"
        @current-change="onHandleCurrentChange"
        @size-change="onHandleSizeChange"
        background
        :layout="getPageLayout('total, prev, pager, next, sizes, jumper',page.probeEnabled)">
      </el-pagination>
      </template>
    </ListWrapper>
  </el-dialog>
</template>
<script lang="ts"
        src="./MemberGrowthDetailDialog.ts">
</script>
<style lang="scss"
       scoped>
.tag-editor {
  display: flex;
  height: 400px;
  gap: 16px;

  & > * {
    border: 1px solid #D7DFEB;
  }

  .tag-list {
    width: calc(70% - 16px);
    overflow-y: auto;
    padding: 16px;

    .label-item {
      display: flex;
      flex-direction: column;
      margin-bottom: 12px;

      .label-type {
        display: flex;
        align-items: center;
        cursor: pointer;

        .label-icon {
          font-size: 12px;
          margin-right: 4px;
        }
      }

      .label-content {
        display: flex;
        flex-wrap: wrap;
        padding-left: 16px;
        margin-top: 8px;
        width: 50%;

        .check-item {
          max-width: 500px;
          height: 24px;
          line-height: 24px;
          text-align: center;
          padding: 0 12px;
          font-weight: 400;
          color: #242633;
          border-radius: 2px;
          background: #f7f9fc;
          border: 1px solid #d7dfeb;
          margin-right: 4px;
          margin-bottom: 3px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          cursor: pointer;

          &.selected {
            border: 1px solid #007eff;
            background: #e6f2ff;
            color: #007eff;
          }
        }
      }
    }
  }

  .tag-selected-list {
    width: 30%;

    .tag-selected-list-header {
      padding: 0 8px;
      background: #D2D9E5;
      height: 36px;
      line-height: 36px;
    }

    .tag-selected-list-content {
      overflow-y: auto;
      height: calc(100% - 36px);
    }

    .tag-selected-list-item {
      padding: 4px 8px;
      display: flex;
      align-items: center;

      .selected-label {
        flex-grow: 1;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }

      .selected-btn {
        flex-shrink: 0;
        margin-left: 12px;
      }
    }
  }
}
</style>
