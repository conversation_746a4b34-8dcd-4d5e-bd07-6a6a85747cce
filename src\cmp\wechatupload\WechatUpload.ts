/*
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:03
 * @LastEditTime: 2023-11-01 17:27:10
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\wechatupload\WechatUpload.ts
 * 记得注释
 */
import { Component, Vue } from 'vue-property-decorator'
import EnvUtil from 'util/EnvUtil'

@Component({
  name: 'WechatUpload',
  components: {}
})
export default class WechatUpload extends Vue {
  importUrl = ''
  imageUrl = ''
  $refs: any
  uploadHeaders: any = {}

  get getUploadUrl() {
    return EnvUtil.getServiceUrl() + this.importUrl
  }
  created() {
    let locale = sessionStorage.getItem('locale')
    this.uploadHeaders = {
			locale: locale ? locale : "zh_CN",
			time_zone: new Date().getTimezoneOffset(),
			marketingCenter: sessionStorage.getItem("marketCenter"),
		};
    const authorization = EnvUtil.getUniAuthorization()
    if (authorization) {
      this.uploadHeaders.authorization = authorization
    }
  }
  doHandleChange(file: any, fileList: any) {
    // console.log('file', file)
  }

  getErrorInfo(a: any, b: any, c: any) {
    this.$message.error('导入失败，请重新导入')
    this.$refs.upload.clearFiles()
  }

  getSuccessInfo(a: any, b: any, c: any) {
    if (a && a.code === 2000) {
      this.$refs.upload.clearFiles()
    }
  }
}