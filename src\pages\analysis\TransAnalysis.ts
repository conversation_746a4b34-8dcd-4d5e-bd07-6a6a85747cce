import { Component, Vue, Watch } from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import ListWrapper from 'cmp/list/ListWrapper.vue'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import DateUtil from 'util/DateUtil'
import MemberExportFilter from 'model/v2/report/MemberExportFilter'
import ReportExportApi from 'http/v2/report/ReportExportApi'
import DownloadCenterDialog from 'pages/main/dowmload_center_dialog/DownloadCenterDialog.vue'
import RSOrgFilter from 'model/common/RSOrgFilter'
import OrgApi from 'http/org/OrgApi'
import DataAnalysisV2Api from 'http/analysis/DataAnalysisV2Api'
import DataAnalysisFilter from 'model/analysis/v2/DataAnalysisFilter'
import MemberTrade from 'model/analysis/v2/MemberTrade'
import MemberTradeStats from 'model/analysis/v2/MemberTradeStats'
import Response from 'model/common/Response'
import TradeIncreaseData from 'model/analysis/v2/TradeIncreaseData'
import Echart from 'echarts/lib/echarts'
import 'echarts/lib/chart/bar'
import BreadCrume from 'cmp/bread-crumb/BreadCrume'
import EnvUtil from 'util/EnvUtil'
import SelectStores from 'cmp/selectStores/SelectStores'

@Component({
  name: 'TransAnalysis',
  components: {
    FormItem,
    ListWrapper,
    SubHeader,
    DownloadCenterDialog,
    BreadCrume,
    SelectStores
  }
})
export default class TransAnalysis extends Vue {
  lineWidth = '350px'
  time = ''
  curStore = ''
  $echarts: any
  barDataArray: any[] = []
  selectDate: any = []
  barChart: any = ''
  $refs: any
  options: any = {}
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  analysisList: TradeIncreaseData[] = []
  stores: any = []
  recordStoreArr: any = []
  analysisCount: MemberTradeStats = new MemberTradeStats()
  analysisAllCount: any = {}
  showTip: boolean = false
  dialogvisiable: boolean = false
  panelArray: any = []
  barChartLoading: boolean = false
  tableLoading: boolean = false

  get getFirstPercent() {
    if (
      !this.analysisCount.tradeAmount ||
      this.analysisCount.tradeAmount === 0
    ) {
      return 0
    }
    if (
      !this.analysisCount.memberTradeAmount ||
      this.analysisCount.memberTradeAmount === 0
    ) {
      return 0
    }
    if (
      this.analysisCount.memberTradeAmount &&
      this.analysisCount.tradeAmount
    ) {
      return Number(
        (
          Number(
            this.analysisCount.memberTradeAmount /
              this.analysisCount.tradeAmount
          ) * 100
        ).toFixed(2)
      )
    }
  }

  get getSecPercent() {
    if (!this.analysisCount.tradeCount || this.analysisCount.tradeCount === 0) {
      return 0
    }
    if (
      !this.analysisCount.memberTradeCount ||
      this.analysisCount.memberTradeCount === 0
    ) {
      return 0
    }
    return Number(
      (
        Number(
          this.analysisCount.memberTradeCount / this.analysisCount.tradeCount
        ) * 100
      ).toFixed(2)
    )
  }

  @Watch('time')
  OnTimeChange(value: string) {
    if (
      value ===
      this.formatI18n('/分析/门店会员增长概况/门店会员增长概况', '自定义')
    ) {
      return
    }
    this.time = value
    this.getRequest()
  }

  created() {
    this.panelArray = [
      {
        name: this.formatI18n('/公用/菜单/门店会员交易概况'),
        url: ''
      }
    ]
    this.$echarts = Echart
    if (EnvUtil.isZh_Cn()) {
      this.lineWidth = '350px'
    } else {
      this.lineWidth = '600px'
    }
    this.time = this.formatI18n('/公用/日期', '今天')
    this.options = {
      color: ['#0CC66D', '#016CFF'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          // 坐标轴指示器，坐标轴触发有效
          type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
        }
      },
      grid: {
        left: '0%',
        right: '5%',
        bottom: '10%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          data: [
            this.formatI18n(
              '/分析/门店会员增长概况/门店会员交易概况',
              '会员客单价（元）'
            ),
            this.formatI18n(
              '/分析/门店会员增长概况/门店会员交易概况',
              '客单价（元）'
            )
          ],
          axisTick: {
            alignWithLabel: true
          }
        }
      ],
      yAxis: [
        {
          type: 'value'
        }
      ],
      series: [
        {
          name: '',
          type: 'bar',
          barWidth: '10%',
          data: [],
          itemStyle: {
            normal: {
              // 这里是重点
              color: function(params: any) {
                // 注意，如果颜色太少的话，后面颜色不会自动循环，最好多定义几个颜色
                let colorList = ['#016CFF', '#FFAA00']
                return colorList[params.dataIndex]
              }
            }
          }
        }
      ]
    }
  }

  mounted() {
    const ele = document.getElementById('myEcharts')
    this.barChart = this.$echarts.init(ele)
    if (this.barChart) {
      this.barChart.setOption(this.options)
    }
  }

  doDialogClose() {
    this.dialogvisiable = false
  }

  doExport() {
    let params: MemberExportFilter = new MemberExportFilter()
    params.storeIdEquals = this.curStore
    if (this.getTimeParams() === 'TODAY') {
      params.begin = this.getDateRange()[0][0]
      params.end = this.getDateRange()[0][1]
    } else if (this.getTimeParams() === 'LST_SEVEN_DAY') {
      params.begin = this.getDateRange()[1][0]
      params.end = this.getDateRange()[1][1]
    } else if (this.getTimeParams() === 'LST_THIRTY_DAY') {
      params.begin = this.getDateRange()[2][0]
      params.end = this.getDateRange()[2][1]
    } else if (this.getTimeParams() === 'CUSTOM') {
      params.begin = this.selectDate[0]
      params.end = this.selectDate[1]
    }

    ReportExportApi.exportStoreMemberTrade(params)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.showTip = true
          this.dialogvisiable = true
        }
      })
      .catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
  }

  doStoreChange() {
    this.getRequest()
  }

  doCustomQuery() {
    if (!this.selectDate || this.selectDate.length <= 0) {
      this.$message.warning(this.formatI18n('/会员/会员资料/请选择日期'))
      return
    }
    this.getRequest()
  }

  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.getAnalysisCount()
  }

  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.getAnalysisCount()
  }

  getDateRange() {
    let arr: any = [
      [
        DateUtil.format(new Date(), 'yyyy-MM-dd'),
        DateUtil.format(new Date(), 'yyyy-MM-dd')
      ],
      [
        DateUtil.format(DateUtil.prevDate(new Date(), 7), 'yyyy-MM-dd'),
        DateUtil.format(DateUtil.prevDate(new Date(), 1), 'yyyy-MM-dd')
      ],
      [
        DateUtil.format(DateUtil.prevDate(new Date(), 30), 'yyyy-MM-dd'),
        DateUtil.format(DateUtil.prevDate(new Date(), 1), 'yyyy-MM-dd')
      ]
    ]
    return arr
  }

  getRequest() {
    this.page.currentPage = 1
    this.getAnalysisCount()
    this.getTradeInfo()
  }

  getTimeParams() {
    if (this.time === this.formatI18n('/公用/日期', '今天')) {
      return 'TODAY'
    } else if (this.time === this.formatI18n('/公用/日期', '近7天')) {
      return 'LST_SEVEN_DAY'
    } else if (this.time === this.formatI18n('/公用/日期', '近30天')) {
      return 'LST_THIRTY_DAY'
    } else if (
      this.time ===
      this.formatI18n('/分析/门店会员增长概况/门店会员增长概况', '自定义')
    ) {
      return 'CUSTOM'
    } else {
      return ''
    }
  }

  getAnalysisCount() {
    let params = new DataAnalysisFilter()
    let dateType = this.getTimeParams()
    if (dateType === 'CUSTOM') {
      params.beginAfterOrEquals = this.selectDate[0]
      params.endBefore = this.selectDate[1]
    } else {
      params.dateRangeType = dateType
    }

    if (this.curStore) {
      params.storeIdEquals = this.curStore
    }
    params.pageSize = this.page.size
    params.page = this.page.currentPage - 1
    this.tableLoading = true
    DataAnalysisV2Api.queryTradeIncrease(params)
      .then((resp: any) => {
        this.tableLoading = false
        if (resp && resp.code === 2000) {
          this.populateList(resp)
        }
      })
      .catch((error: any) => {
        this.tableLoading = false
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
  }

  // 获取会员交易概况
  getTradeInfo() {
    let params = new DataAnalysisFilter()
    let dateType = this.getTimeParams()
    if (dateType === 'CUSTOM') {
      params.beginAfterOrEquals = this.selectDate[0]
      params.endBefore = this.selectDate[1]
    } else {
      params.dateRangeType = dateType
    }

    if (this.curStore) {
      params.storeIdEquals = this.curStore
    }
    params.pageSize = this.page.size
    params.page = this.page.currentPage - 1
    this.barChartLoading = true
    DataAnalysisV2Api.statsMemberTradeAmount(params)
      .then((resp: any) => {
        this.barChartLoading = false
        if (resp) {
          this.populateChart(resp.data)
        }
      })
      .catch((error: any) => {
        this.barChartLoading = false
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
  }

  populateList(response: Response<any>) {
    if (response.data) {
      this.analysisList = response.data
      this.page.total = response.total
    }
  }

  populateChart(data: any) {
    if (data) {
      this.analysisCount = data
      if (this.analysisCount.memberPerPrice && this.analysisCount.perPrice) {
        this.barDataArray = [
          this.analysisCount.memberPerPrice.toFixed(2),
          this.analysisCount.perPrice.toFixed(2)
        ]
      } else {
        this.barDataArray = [0, 0]
      }
      if (this.barChart) {
        this.barChart.setOption({
          series: [
            {
              data: this.barDataArray
            }
          ]
        })
      }
    }
  }

  validateDate() {
    if (
      this.time !==
      this.formatI18n('/分析/门店会员增长概况/门店会员增长概况', '自定义')
    ) {
      return true
    }
    if (!this.selectDate[0] || !this.selectDate[1]) {
      this.$message.warning(
        this.formatI18n('/会员/会员资料', '请选择日期') as string
      )
      this.$refs.selectDate.focus()
      return
    }
    let pre = this.selectDate[0]
    let next = this.selectDate[1]
    let threeMonth = DateUtil.addMonth(new Date(), -3)
    if (
      DateUtil.clearTime(new Date(next)).getTime() >
      DateUtil.clearTime(new Date()).getTime()
    ) {
      this.$message.warning(
        this.formatI18n(
          '/分析/门店会员增长概况/门店会员增长概况/筛选时段点击自定义/开始时间和结束时间间隔大于3个月或者结束日期大于今天的提示信息',
          '只支持查询近三个月的数据且不支持查询明天和明天以后的数据'
        ) as string
      )
      this.$refs.selectDate.focus()
      return false
    }
    if (
      DateUtil.clearTime(new Date(pre)).getTime() <
      DateUtil.clearTime(new Date(threeMonth)).getTime()
    ) {
      this.$message.warning(
        this.formatI18n(
          '/分析/门店会员增长概况/门店会员增长概况/筛选时段点击自定义/开始时间和结束时间间隔大于3个月或者结束日期大于今天的提示信息',
          '只支持查询近三个月的数据且不支持查询明天和明天以后的数据'
        ) as string
      )
      this.$refs.selectDate.focus()
      return false
    }
    return true
  }
}
