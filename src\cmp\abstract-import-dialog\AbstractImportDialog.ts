/*
 * @Author: 黎钰龙
 * @Date: 2024-04-10 16:35:43
 * @LastEditTime: 2024-08-02 09:23:42
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\abstract-import-dialog\AbstractImportDialog.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import EnvUtil from 'util/EnvUtil';
import { Vue } from 'vue-property-decorator';

@I18nPage({
  prefix: [
    '/公用/券模板'
  ],
  auto: true
})
export default abstract class AbstractImportDialog extends Vue {
  $refs: any;
  uploadHeaders: any = {};
  fileCount: number = 0;
  dialogShow: boolean = false;
  params: any = null

  open(params?: any) {
    let locale = sessionStorage.getItem("locale");
    this.uploadHeaders = {
      locale: locale ? locale : "zh_CN",
      time_zone: new Date().getTimezoneOffset(),
      marketingCenter: sessionStorage.getItem("marketCenter"),
    };
    const authorization = EnvUtil.getUniAuthorization();
    if (authorization) {
      this.uploadHeaders.authorization = authorization;
    }
    this.params = params || null
    this.dialogShow = true
  }

  //确认导入
  doModalClose = (type: string) => {
    if (type === "confirm") {
      if (this.fileCount > 0) {
        this.$refs.upload.submit();
      } else {
        this.$message.warning(this.i18n("/公用/导入/请先选择文件"));
      }
    } else {
      this.close()
    }
  }

  // 导入成功 文件+1
  doHandleChange = (file: any, fileList: any) => {
    if (fileList.length > 0) {
      this.fileCount = Number(this.fileCount) + 1;
    }
  }

  // 弹窗关闭
  doBeforeClose = (done: any) => {
    this.close()
    done();
  }

  // 导入成功
  getSuccessInfo = (a: any, b: any, c: any) => {
    if (a?.code === 2000) {
      this.$refs.upload.clearFiles();
      this.fileCount = 0;
      this.$message.success(this.i18n('/公用/导入/导入成功'))
      this.close()
      this.uploadSuccess()
    } else {
      this.$message.error(a?.msg || this.i18n('导入失败'))
    }
  }

  abstract uploadSuccess(): void

  abstract close(): void

  // 导入失败
  getErrorInfo = (a: any, b: any, c: any) => {
    this.$message.error(this.i18n("/公用/导入/导入失败，请重新导入"));
    this.fileCount = 0;
    this.$refs.upload.clearFiles();
  }
};