/*
 * @Author: 黎钰龙
 * @Date: 2024-11-21 19:31:52
 * @LastEditTime: 2024-12-10 17:04:23
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\autoTag\PreferenceTagRule.ts
 * 记得注释
 */
import { EventBehaviorCalculateType } from "./EventBehaviorCalculateType"
import EventBehaviorRule from "./EventBehaviorRule"
import { PropType } from "./PropType"

export default class PreferenceTagRule {
  // 标签名称
  tagName: Nullable<string> = null
  // 事件行为规则
  eventBehaviorRule: Nullable<EventBehaviorRule> = new EventBehaviorRule()
  // 事件行为指标类型：字符串，数字，日期，布尔
  metricsType: Nullable<PropType> = null
  // 事件行为指标属性
  metricsProp: Nullable<string> = null
  // 事件行为指标计算方式
  metricsCalculation: Nullable<EventBehaviorCalculateType> = null
  // 事件行为指标计算阈值
  metricsCalThreshold: Nullable<string> = null
  // 事件行为指标计算属性
  metricsCalProp: Nullable<string> = null
}