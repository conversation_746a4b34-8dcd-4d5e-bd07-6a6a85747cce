<template>
  <div class="coupon-template-list">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button
          v-if="hasOptionPermission('/券/券管理/券核销', '单据维护')"
          @click="doAdd()"
          size="small"
          type="primary"
          >{{ i18n("新建券核销单") }}</el-button
        >
      </template>
    </BreadCrume>
    <ListWrapper class="current-page">
      <template slot="query">
        <el-row>
          <el-col :span="8">
            <form-item :label="i18n('单号')" labelWidth="120px">
              <el-input
                :placeholder="i18n('单号')"
                v-model="query.numberEquals"
              ></el-input>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="i18n('券号')" labelWidth="120px">
              <el-input
                :placeholder="i18n('券号')"
                v-model="query.couponCodeLike"
              ></el-input>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="i18n('创建时间')" labelWidth="120px">
              <el-date-picker
                v-model="createBetween"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                range-separator="-"
                ref="selectDate"
                size="small"
                start-placeholder="开始日期"
                type="daterange"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </form-item>
          </el-col>
           <el-col :span="8">
            <form-item :label="i18n('最后修改时间')" labelWidth="120px">
              <el-date-picker
                v-model="modifyBetween"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                range-separator="-"
                ref="selectDate"
                size="small"
                start-placeholder="开始日期"
                type="daterange"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </form-item>
          </el-col>
        </el-row>
        <el-row style="margin-top: 10px">
          <el-col :span="8">
            <form-item labelWidth="120px">
              <el-button @click="doSearch" type="primary">{{
                formatI18n("/公用/券模板", "查询")
              }}</el-button>
              <el-button @click="doReset">{{
                formatI18n("/公用/券模板", "重置")
              }}</el-button>
            </form-item>
          </el-col>
        </el-row>
      </template>
      <!-- <template slot="btn">
        <el-checkbox @change="checkedAllRow" style="margin-left: 14px;margin-right: 10px" v-model="singleAll"> </el-checkbox>{{ getSelectActive }}
        <el-button v-if="true" @click="doBatchAudit">{{ formatI18n("/营销/券礼包活动/券礼包活动", "批量审核") }}</el-button>
        <el-button v-if="true" @click="doBatchDelete" style="color: red">{{
            formatI18n("/营销/券礼包活动/券礼包活动", "批量删除")
        }}</el-button>
      </template> -->
      <template slot="list">
        <el-tabs @tab-click="doHandleClick" v-model="activeName">
          <el-tab-pane :label="getAllCount" name="all"></el-tab-pane>
          <el-tab-pane :label="getNoAudit" name="noAudit"></el-tab-pane>
          <el-tab-pane :label="getAudit" name="audit"></el-tab-pane>
        </el-tabs>
        <el-table
          v-loading="loading"
          ref="table"
          :data="tableData"
          @selection-change="handleSelectionChange"
          style="width: 100%; margin-top: 10px"
        >
          <el-table-column type="selection" width="55"> </el-table-column>
          <el-table-column :label="i18n('单号')">
            <template slot-scope="scope">
              <el-button type="text" @click="doToDtl(scope.row.number)">{{scope.row.number}}</el-button>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('状态')">
            <template slot-scope="scope">
                <div class="state-block" v-if="scope.row.state === 'INITIAL'">
                  <div class="state-color" style="background-color: orange"></div>
                  {{ scope.row.state | couponWriteOffState }}
                </div>

                <div class="state-block" v-if="scope.row.state === 'EFFECTED'">
                  <div class="state-color" style="background-color: green"></div>
                  {{ scope.row.state | couponWriteOffState }}
                </div>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('创建时间')">
            <template slot-scope="scope">
              {{scope.row.occurredTime | dateFormate3}}
            </template>
          </el-table-column>
          <el-table-column :label="i18n('/会员/会员资料/最近修改时间')">
            <template slot-scope="scope">
              {{scope.row.lastModified | dateFormate3}}
            </template>
          </el-table-column>
          <el-table-column :label="i18n('摘要')">
            <template slot-scope="scope">
              <div class="one-line" :title="scope.row.remark">{{scope.row.remark || '-'}}</div>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('创建人')">
            <template slot-scope="scope">
              {{scope.row.creator || '-'}}
            </template>
          </el-table-column>
          <!-- <el-table-column :label="i18n('审核人')">
            <template slot-scope="scope">
              {{scope.row.operator || '-'}}
            </template>
          </el-table-column> -->
          <el-table-column :label="i18n('发生组织')">
            <template slot-scope="scope">
              [{{scope.row.issueOrgId}}]{{scope.row.issueOrgName}}
            </template>
          </el-table-column>
        </el-table>
      </template>
      <!--分页栏-->
      <template slot="page">
        <el-pagination
          :current-page="page.currentPage"
          :page-size="page.size"
          :page-sizes="[10, 20, 30, 40]"
          :total="page.total"
          @current-change="onHandleCurrentChange"
          @size-change="onHandleSizeChange"
          background
          layout="total, prev, pager, next, sizes,  jumper"
        ></el-pagination>
      </template>
    </ListWrapper>
  </div>
</template>

<script lang="ts" src="./CouponWriteOff.ts"></script>

<style lang="scss">
.coupon-template-list {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: hidden;

  .current-page {
    height: calc(100% - 48px);
    overflow: auto;

    .el-select {
      width: 100%;
    }
  }

  .el-range-editor.el-input__inner {
    width: 100%;
  }

  .split-line {
    border-right: 1px solid #d7dfeb;
    padding-right: 5px;

    &:last-child {
      padding-left: 5px;
      border-right: none;
    }
  }
}

.state-block {
  display: flex;
  /*justify-content:center;*/
  align-items: center;

  .state-color {
    height: 5px;
    width: 5px;
    border-radius: 10px;
    margin-right: 5px;
  }
}
.inline-select {
  display: flex;
}
.one-line {
  width: 350px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
