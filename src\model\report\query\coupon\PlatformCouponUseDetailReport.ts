export default class PlatformCouponUseDetailReport {
  // crm uuid
  uuid: Nullable<string> = null
  // 券模板号
  templateNumber: Nullable<string> = null
  // 券模板名称
  templateName: Nullable<string> = null
  // 券码
  couponCode: Nullable<string> = null
  // 购券订单号
  buyerOrderNo: Nullable<string> = null
  // 平台
  platform: Nullable<string> = null
  // 平台名称
  platformName: Nullable<string> = null
  // 交易号
  tradeNo: Nullable<string> = null
  // 核销状态 SUCCESS-已核销  ROLLBACK-撤销核销
  state: Nullable<'SUCCESS' | 'ROLLBACK'> = null
  // 核销时间
  verifyTime: Nullable<Date> = null
  // 撤销核销时间
  rollBackTime: Nullable<Date> = null
  // 核销门店id
  occurredOrgId: Nullable<string> = null
  // 核销门店名称
  occurredOrgName: Nullable<string> = null
  // 券抵扣金额
  deductAmount: Nullable<number> = null
  // 券原始金额
  originalAmount: Nullable<number> = null
  // 券用户实付金额
  payAmount: Nullable<number> = null
  // 券优惠金额
  favAmount: Nullable<number> = null
  // 商家营销金额
  merchantTicketAmount: Nullable<number> = null
  // 商家结算金额
  purchaseAmount: Nullable<number> = null
}