import AnalysisChartData from "./AnalysisChartData"
import OrgTradeAnalysisSummary from "./OrgTradeAnalysisSummary"

// 交易分析报表响应
export default class OrgTradeAnalysisReport {
  // 时间维度
  dateUnit: Nullable<string> = null
  // 数据概览
  summary: Nullable<OrgTradeAnalysisSummary> = null
  // 交易金额指标
  tradeTotalData: AnalysisChartData[] = []
  // 交易数量指标
  tradeGoodsQtyData: AnalysisChartData[] = []
  // 客单数指标
  tradeQtyData: AnalysisChartData[] = []
  // 客单价指标
  perPriceData: AnalysisChartData[] = []
  // 客单件指标
  perQtyData: AnalysisChartData[] = []
  // 件单价指标
  perQtyPriceData: AnalysisChartData[] = []
  // 会员交易金额指标
  memberTradeTotalData: AnalysisChartData[] = []
  // 会员交易金额占比指标
  memberTradeTotalRatioData: AnalysisChartData[] = []
  // 会员交易数量指标
  memberTradeGoodsQtyData: AnalysisChartData[] = []
  // 会员客单数指标
  memberTradeQtyData: AnalysisChartData[] = []
  // 会员客单数指标
  memberTradeQtyRatioData: AnalysisChartData[] = []
  // 会员客单价指标
  memberPerPriceData: AnalysisChartData[] = []
  // 会员客单件指标
  memberPerQtyData: AnalysisChartData[] = []
  // 会员件单价指标
  memberPerQtyPriceData: AnalysisChartData[] = []
}