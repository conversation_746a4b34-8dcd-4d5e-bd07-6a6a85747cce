import IdName from 'model/common/IdName'
import { SuperpositionLevel } from './SuperpositionLevel'
import { SuperpositionType } from './SuperpositionType'

export default class CouponSuperposition {
  // GOODS--单品级别； TRADE——订单级别；
  superpositionLevel: Nullable<SuperpositionLevel> = null
  // REUSEABLE——可叠加； NONREUSEABLE——不可叠加；
  superpositionType: Nullable<string> = null
  // 不可叠加模板信息
  groupMutexTemplates: IdName[] = []
  // 不可叠加类型：ALL_COUPON，CURRENT_COUPON，OTHER_COUPON
  nonSuperpositionTypeValue: Nullable<SuperpositionType> = null
  // 可叠加类型：ALL_COUPON，CURRENT_COUPON，OTHER_COUPON
  superpositionTypeValue: SuperpositionType[] = []
}