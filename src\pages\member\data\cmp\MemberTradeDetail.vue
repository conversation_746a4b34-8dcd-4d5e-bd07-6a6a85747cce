<template>
  <div class="member-card member-trade-card">
    <member-tab :tabs="tabs"
                :current-index="currentTabIndex"
                @change="onTabChange"
                style="margin-bottom: 8px"></member-tab>
    <ListWrapper>
      <template slot="query">
      <QueryCondition @reset="onReset"
                      @search="onSearch"
                      :show-expand="false">
        <el-row>
          <el-col :span="12">
            <form-item :label="i18n('发生组织')">
              <SelectStores v-model="query.storeIdEquals"
                            :isOnlyId="true"
                            :hideAll="false"
                            width="100%"
                            :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
              </SelectStores>
            </form-item>
          </el-col>
          <el-col :span="12">
            <form-item :label="i18n(isSale?'交易单号':'售后单号')">
              <el-input :placeholder="i18n('请输入')"
                        v-model="query.tradeNo" clearable></el-input>
            </form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <form-item :label="i18n(isSale?'交易时间':'售后时间')">
              <el-date-picker
                :end-placeholder="i18n('结束日期')"
                format="yyyy-MM-dd"
                range-separator="-"
                clearable
                size="small"
                :start-placeholder="i18n('开始日期')"
                type="daterange"
                v-model="query.tranTimeBetweenClosedOpen"
                value-format="yyyy-MM-dd"
              style="width: 100%">
              </el-date-picker>
            </form-item>
          </el-col>
        </el-row>
      </QueryCondition>
      </template>
      <template slot="list">
      <el-table :data="queryData"
                style="width: 100%">
        <el-table-column :label="i18n(isSale?'交易时间':'售后时间')"
                         fixed
                         width="104" key="0">
          <template slot-scope="scope">
          {{ scope.row.tranTime | dateFormate3 }}
          </template>
        </el-table-column>
        <el-table-column :label="i18n('渠道')"
                         fixed
                         width="104" key="1">
          <template slot-scope="scope">
          {{ scope.row.channelName | strFormat}}
          </template>
        </el-table-column>
        <el-table-column :label="i18n(isSale?'交易单号':'售后单号')"
                         fixed
                         width="118" key="2">
          <template slot-scope="scope">
          <a href="javascript:void(0);" @click="onTradeNoClick(scope.row,isSale)">{{ scope.row.tradeNo }}</a>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('关联原单')"
                         width="118"  key="3" v-if="!isSale">
          <template slot-scope="scope">
          <template v-if="scope.row.sourceTradeId">
          {{ scope.row.sourceTradeId.id }}
          </template>
          <template v-else>-</template>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('发生组织')"
                         minWidth="214"
                         prop="store" key="4">
          <template slot-scope="scope">
          <template v-if="scope.row.occurredOrgId">
          [{{ scope.row.occurredOrgId }}]{{ scope.row.occurredOrgName }}
          </template>
          <template v-else>--</template>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('收银机号')"
                         minWidth="104"
                         prop="posNo" key="5" v-if="isSale">
          <template slot-scope="scope">
          {{ scope.row.posNo | strFormat }}
          </template>
        </el-table-column>
        <el-table-column :label="i18n('金额小计')"
                         minWidth="104" key="6" v-if="isSale">
          <template slot-scope="scope">
          {{ scope.row.stdAmount | amount }}
          </template>
        </el-table-column>
        <el-table-column :label="i18n(isSale?'实付金额':'售后金额')"
                         minWidth="104" key="7">
          <template slot-scope="scope">
          {{ scope.row.total | amount }}
          </template>
        </el-table-column>
        <el-table-column :label="i18n('优惠金额')"
                         minWidth="104" key="8" v-if="isSale">
          <template slot-scope="scope">
          {{ scope.row.favAmount | amount }}
          </template>
        </el-table-column>
      </el-table>
      </template>
      <template slot="page">
      <el-pagination
        :current-page="page.currentPage"
        :page-size="page.size"
        :page-sizes="[10, 20, 30, 40]"
        :total="page.total"
        @current-change="onHandleCurrentChange"
        @size-change="onHandleSizeChange"
        background
        :layout="getPageLayout('total, prev, pager, next, sizes, jumper',page.probeEnabled)">
      </el-pagination>
      </template>
    </ListWrapper>

  </div>
</template>
<script lang="ts"
        src="./MemberTradeDetail.ts">
</script>
<style lang="scss"
       scoped>
.member-trade-card {
  ::v-deep .list-wrapper {
    background: white;
  }

  ::v-deep .line-blank {
    display: none;
  }
}
</style>
