<template>
  <div class="member-tab">
    <div class="tab-item"
         v-for="(item,index) in tabs"
         :key="item"
         :class="{active:index==currentIndex}"
         @click="onChange(index)">{{ item }}
    </div>
    <div class="blank"></div>
  </div>
</template>
<script lang="ts"
        src="./MemberTab.ts">
</script>
<style lang="scss"
       scoped>
.member-tab {
  display: flex;

  .tab-item {
    font-weight: 400;
    font-size: 14px;
    color: #79879E;
    line-height: 22px;
    padding: 9px 20px;
    cursor: pointer;
    border-top: 1px solid #D7DFEB;
    border-right: 1px solid #D7DFEB;
    border-bottom: 1px solid #D7DFEB;
    background: #F8F9FC;

    &:first-child {
      border-left: 1px solid #D7DFEB;
    }

    &.active {
      color: #007EFF;
      font-weight: 600;
      background: white;
      border-bottom: 1px solid white;
    }
  }

  .blank {
    flex-grow: 1;
    border-bottom: 1px solid #D7DFEB;

  }
}
</style>
