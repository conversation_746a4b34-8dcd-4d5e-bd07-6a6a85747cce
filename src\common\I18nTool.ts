import store from "store/index";

export default class I18nTool {
  static TRANSLATED_FLAG = 'translated'
  static NO_I18N_FLAG = 'no-i18n' // 元素包含该属性时，不进行国际化处理

  static render(el: Element, prefix: string[], selectors: Nullable<string[]> = null, handleChild = true, debug = false) {
    let start = new Date().getTime()
    if (selectors) {
      for (const selector of selectors) {
        this.renderNodes(el.querySelectorAll(selector), null, ['placeholder', 'title'], handleChild, prefix, debug)
      }
    } else {
      this.renderNodes(el.childNodes, null, ['placeholder', 'title'], handleChild, prefix, debug)
    }
    if (debug) {
      // console.debug('触发国际化自动渲染，耗时' + (new Date().getTime() - start) + '毫秒')
    }
  }

  /**
   *
   * @param nodes 节点列表
   * @param tags 元素tag列表，被该tag包裹的纯文本内容会被国际化，但子元素除了纯文本还有其他元素不会自动国际化
   * @param attrs 属性列表，所有遍历到的元素的属性值会被自动国际化
   * @param handleChild 是否处理子节点
   * @param prefix 国际化前缀，数组，支持多个前缀
   * @param debug 调试模式
   */
  static renderNodes(nodes: NodeList, tags: Nullable<string[]> = null, attrs: string[] = [], handleChild = true, prefix: string[], debug = false) {
    for (const node of nodes) {
      let nodeAsAny = node as any
      let translateFlag = false
      // 处理有属性的元素
      if (nodeAsAny.attributes) {
        let hasTranslatedAttr = nodeAsAny.hasAttribute(this.TRANSLATED_FLAG) // 忽略已翻译的元素
        let hasNoI18nAttr = nodeAsAny.hasAttribute(this.NO_I18N_FLAG) // 忽略有i18n属性的元素
        if (hasTranslatedAttr || hasNoI18nAttr) {
          continue
        }
        for (let attr of attrs) {
          if (nodeAsAny.hasAttribute(attr)) {
            nodeAsAny[attr] = this.match(nodeAsAny[attr], prefix, [], debug)
            translateFlag = true
          }
        }
      }
      // 处理纯文本元素
      let isPlainTextElement = nodeAsAny.hasChildNodes() && nodeAsAny.childNodes.length === 1 && nodeAsAny.firstChild.nodeName === '#text'
      let matchTags = tags == null || (tags && tags.indexOf(nodeAsAny.nodeName.toLowerCase()) > -1)
      if (isPlainTextElement && matchTags) {
        nodeAsAny.firstChild.nodeValue = this.match(nodeAsAny.firstChild.nodeValue, prefix, [], debug)
        translateFlag = true
      }
      if (translateFlag) {
        nodeAsAny.attributes.setNamedItem(document.createAttribute(this.TRANSLATED_FLAG))
      }
      // 处理子元素
      if (handleChild && node.hasChildNodes() && !isPlainTextElement) {
        this.renderNodes(node.childNodes, tags, attrs, handleChild, prefix, debug)
      }
    }
  }

  /**
   * 自动国际化元素以及子元素的属性、文本内容
   * 注意事项： v-if控制的组件如果Tag相同，vue不会重新渲染，需要增加key属性让vue重新渲染
   * @param el 绑定的元素
   * @param prefix 国际化前缀，数组，支持多个前缀
   * @param selectors
   * @param handleChild
   * @param debug 调试模式
   */
  static addMutationObserver(el: Element, prefix: string[], selectors: Nullable<string[]> = null, handleChild = true, debug = false) {
    let options = {
      attributes: true,
      childList: true,
      subtree: true,
      attributeFilter: ["placeholder", "title"]
    };
    let mutationObserver = new MutationObserver((mutationsList: any[], observer: any) => {
      I18nTool.render(el, prefix, selectors, handleChild, debug)
    });
    mutationObserver.observe(el, options);
  }

  static match(content: string, prefix: string[] = [], params: string[] = [], debug: boolean = false) {
    let result = this._match(content, prefix, debug).content
    if (params && params.length > 0) {
      for (let i=0;i<params.length;i++) {
        result = result.replace(new RegExp(`\\{${i}\}`, 'g'), params[i])
      }
    }
    return result
  }

  static _match(content: string, prefix: string[] = [], debug: boolean = false) {
    if (!content || !content.trim()) {
      return {prefix: null, content: debug ? '×': '' }
    }
    prefix.push('')
    for (let item of prefix) {
      item = item ? item + '/' : ''
      let result = store.state.i18n[item + content.trim()]
      if (result !== undefined && result !== null) {
        return {prefix: item, content: debug && !result.endsWith('√') ? result + '√' : result}
      }
    }
    return {prefix: null, content: debug && !content.endsWith('×') ? content + '×' : content}
  }
}
