/*
 * @Author: 黎钰龙
 * @Date: 2024-09-14 10:04:06
 * @LastEditTime: 2024-09-18 10:15:23
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\points\activity\pointsexchangecoupon\BBatchPointsExchangeCouponActivityLine.ts
 * 记得注释
 */
import CouponItem from "model/v2/coupon/improveProfiles/CouponItem"
import CommonUtil from "util/CommonUtil"
import {ExpireRefundType} from "model/weixin/weixinIssueCouponActivity/ExpireRefundType";

export default class BBatchPointsExchangeCouponActivityLine {
  // uuid(只前端使用)
  uuid?: string = CommonUtil.uuid()
  // 活动图片
  imageId: Nullable<string> = null
  // 名称
  name: Nullable<string> = null
  // 兑换券信息
  couponItem: Nullable<CouponItem> = null
  // 兑换所需积分
  points: Nullable<number> = null
  // 兑换所需储值
  cost: Nullable<number> = null
  // 每人每天限量
  maxMemberPerDayTime: Nullable<number> = null
  // 活动总限
  maxStockQty: Nullable<number> = null
  // 每人限量
  maxMemberQuotaQty: Nullable<number> = null
  // 活动每天限量
  maxIssueDayTimes: Nullable<number> = null
  // 显示排序
  sequence: Nullable<number> = 99;
  // 过期退款方式
  expireRefundType: Nullable<ExpireRefundType> = ExpireRefundType.MANUAL_REFUND
  // 是否允许用户申请退款
  enableUserApplyRefund: Nullable<boolean> = false
  // 是否要人工审核
  enableManualAudit: Nullable<boolean> = true
}