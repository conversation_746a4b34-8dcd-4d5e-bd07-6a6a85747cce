<template>
  <div class="member-analysis">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
      </template>
    </BreadCrume>
    <div style="flex: 1; overflow: auto;">
      <div class="top-tab" v-loading="analysisSummaryLoading">
        <div class="sketch">
          <div class="name">{{formatI18n('/分析/门店会员增长概况/门店会员增长概况', '累计会员数（人）')}}</div>
          <div class="value">{{ analysisAllCount.memberCount | strFormat }}</div>
        </div>
        <div class="vertical"></div>
        <div class="sketch">
          <div class="name">{{formatI18n('/分析/门店会员增长概况/门店会员增长概况', '累计有手机号会员（人）')}}</div>
          <div class="value">{{ analysisAllCount.memberHasMobileCount | strFormat }}</div>
        </div>
        <div class="vertical"></div>
        <div class="sketch">
          <div class="name">{{formatI18n('/分析/门店会员增长概况/门店会员增长概况', '有手机号会员占比')}}</div>
          <div class="value">{{ (analysisAllCount.memberHasMobileRate * 100) | fmt }}%</div>
        </div>
      </div>
      <div class="line"></div>
      <div class="content" id="content">
        <div class="title" style="position: relative">{{formatI18n('/分析/门店会员增长概况/门店会员增长概况', '增长概况')}}
          <el-button @click="doExport" style="position: absolute;right: 0px;" type="primary"
            v-if="hasOptionPermission('/数据/分析/门店会员增长概况', '数据导出')">{{formatI18n('/分析/门店会员增长概况/门店会员增长概况', '导出报表')}}</el-button>
        </div>
        <el-row>
          <el-col :span="18">
            <form-item :label="formatI18n('/分析/门店会员增长概况/门店会员增长概况', '筛选时段')" style="margin-left: -10px">
              <el-radio-group style="padding-top: 5px" v-model="time">
                <el-radio-button :label="formatI18n('/公用/日期', '今天')"></el-radio-button>
                <el-radio-button :label="formatI18n('/公用/日期', '近7天')"></el-radio-button>
                <el-radio-button :label="formatI18n('/公用/日期', '近30天')"></el-radio-button>
                <el-radio-button :label="formatI18n('/分析/门店会员增长概况/门店会员增长概况', '自定义')"></el-radio-button>
              </el-radio-group>
              <el-date-picker :end-placeholder="formatI18n('/公用/券模板', '结束时间')" format="yyyy-MM-dd" range-separator="-" ref="selectDate" size="small"
                :start-placeholder="formatI18n('/公用/券模板', '开始时间')" style="margin-left: 20px;margin-right: 10px;    position: relative; top: 4px;"
                type="daterange" v-if="time === formatI18n('/分析/门店会员增长概况/门店会员增长概况', '自定义')" v-model="selectDate" value-format="yyyy-MM-dd">
              </el-date-picker>
              <el-button style="position: relative; top: 3px;" @click="doCustomQuery" size="small" type="primary"
                v-if="time === formatI18n('/分析/门店会员增长概况/门店会员增长概况', '自定义')">{{formatI18n('/公用/券模板', '查询')}}</el-button>
            </form-item>

          </el-col>
          <el-col :span="6">
            <form-item :label="formatI18n('/公用/券模板', '门店')" style="float: right;margin-right: 24px;">
              <SelectStores v-model="curStore" @change="doStoreChange" :isOnlyId="true" :hideAll="false" width="300px"
                :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
              </SelectStores>
            </form-item>
          </el-col>
        </el-row>
        <div class="gird">
          <div class="grid-item" v-loading="statsMemberIncreaseLoading">
            <div id="myEcharts" style="height: 350px;width: 300px" :style="{width: lineWidth}"></div>
            <div class="bottom-desc" style="bottom: -3px">
              <div class="left"><span class="dot color3"></span>{{ formatI18n('/分析/门店会员增长概况/门店会员增长概况', '新增会员（人）') }}<span
                  class="count">{{ analysisCount.newMemberCount }}</span></div>
              <div class="left">
                <span class="dot color5"></span>
                {{ formatI18n('/分析/门店会员增长概况/门店会员增长概况', '新增门店会员（人）') }}
                <span>
                  <el-tooltip effect="light" placement="top">
                    <i class="iconfont  ic-info" style="font-size: 18px;color: #20A0FF;position: relative;top: 2px;"></i>
                    <template slot="content">
                      <div style="width: 230px">
                        <div>{{ formatI18n('/分析/门店会员增长概况/门店会员增长概况', '因存在部分新增会员暂无归属门店的情况，所以新增门店会员数可能小于新增会员数。') }}</div>
                      </div>
                    </template>
                  </el-tooltip>
                </span>
                <span class="count">{{ analysisCount.newStoreMemberCount }}</span>
              </div>
              <div class="left"><span class="dot color4"></span>
                {{ formatI18n('/分析/门店会员增长概况/门店会员增长概况', '新增手机号会员（人）') }}

                <span class="count">{{ analysisCount.newMobileMemberCount }}</span>
              </div>
            </div>
          </div>
          <div class="grid-item left-border" v-loading="statsMemberConsumeLoading">
            <el-progress :percentage="getFirstPercent" :stroke-width="18" :width="220" color="#6A4EF3" type="circle">
            </el-progress>
            <div class="bottom-desc">
              <div class="left"><span class="dot color1"></span>{{ formatI18n('/分析/门店会员增长概况/门店会员增长概况/表格', '首次消费会员（人）') }}<span
                  class="count">{{ analysisCount.consumeFirstCount }}</span></div>
              <div class="left"><span class="dot"></span>{{ formatI18n('/分析/门店会员增长概况/门店会员增长概况', '有消费会员（人）') }}<span
                  class="count">{{ analysisCount.consumeCount }}</span></div>
            </div>
          </div>
        </div>
        <div class="table-wrap" v-loading="analysisTableLoading">
          <el-table :data="analysisList" border style="width: 100%">
            <el-table-column :label="formatI18n('/公用/券模板', '门店')" fixed prop="store" min-width="150" show-overflow-tooltip />
            <el-table-column :label="formatI18n('/分析/门店会员增长概况/门店会员增长概况', '新增门店会员（人）')" fixed prop="newStoreMemberCount" width="150" />
            <el-table-column :label="formatI18n('/分析/门店会员增长概况/门店会员增长概况', '新增手机号会员（人）')" fixed prop="newMobileMemberCount" width="200" />
            <el-table-column :label="formatI18n('/分析/门店会员增长概况/门店会员增长概况', '有消费会员（人）')" prop="consumeCount" width="150" />
            <el-table-column :label="formatI18n('/分析/门店会员增长概况/门店会员增长概况', '首次消费会员数（人）')" prop="consumeFirstCount" width="200" />
            <el-table-column :label="formatI18n('/分析/门店会员增长概况/门店会员增长概况', '新客占比')" width="150">
              <template slot-scope="scope">
                <div v-if="scope.row.consumeCount === 0 || scope.row.consumeFirstCount===0">--</div>
                <div v-else> {{ ((scope.row.consumeFirstCount / scope.row.consumeCount) * 100) | fmt }}%</div>

              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="footer">
          <el-pagination :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total" :pager-count="5"
            @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background layout="total, prev, pager, next, sizes,  jumper">
          </el-pagination>
        </div>
        <div class="line"></div>
        <div class="title" style="position: relative">{{formatI18n('/分析/门店会员增长概况/门店会员增长概况', '复购分析')}}
          <el-button @click="doOrgCycleReportExport" style="position: absolute;right: 0px;" type="primary"
            v-if="hasOptionPermission('/数据/分析/门店会员增长概况', '数据导出')">{{formatI18n('/分析/门店会员增长概况/门店会员增长概况', '导出报表')}}</el-button>
        </div>
        <el-row>
          <el-col :span="18">
            <form-item style="margin-left: -10px" label-width="115px">
              <span slot="label">
                <span>{{ formatI18n('/分析/门店会员增长概况/门店会员增长概况', '筛选时段') }}</span>
                <el-tooltip effect="light" placement="top">
                  <i class="iconfont  ic-info" style="font-size: 18px;color: #20A0FF;position: relative;top: 2px;"></i>
                  <template slot="content">
                    <div style="width: 450px">
                      <div>{{ formatI18n('/分析/门店会员增长概况/门店会员增长概况', '近7天：最近7天，比如2023.1.13查询是2023.1.6-2023.1.12') }}</div>
                      <div>{{ formatI18n('/分析/门店会员增长概况/门店会员增长概况', '近30天：最近30天，比如2023.1.13查询是2022.12.14-2023.1.12') }}</div>
                      <div>{{ formatI18n('/分析/门店会员增长概况/门店会员增长概况', '上周：上一个自然周，比如2023.1.13查询是2023.1.2-2023.1.8') }}</div>
                      <div>{{ formatI18n('/分析/门店会员增长概况/门店会员增长概况', '上月：上个自然月，比如2023.1.13查询是2022.12.1-2022.12.31') }}</div>
                      <div>{{ formatI18n('/分析/门店会员增长概况/门店会员增长概况', '上季度：上一个季度，比如2023.1.13查询是2022.10.1-2022.12.31') }}</div>
                      <div>{{ formatI18n('/分析/门店会员增长概况/门店会员增长概况', '上自然半年：上一个自然半年，比如2023.1.13查询是2022.7.1-2022.12.31') }}</div>
                      <div>{{ formatI18n('/分析/门店会员增长概况/门店会员增长概况', '上一年：上一个自然年，比如2023.1.13查询是2022.1.1-2022.12.31') }}</div>
                    </div>
                  </template>
                </el-tooltip>
              </span>
              <el-radio-group style="padding-top: 5px" v-model="orgCycleReportTime">
                <el-radio-button :label="DateRangeType.LST_SEVEN_DAY">{{ formatI18n('/公用/日期', '近7天') }}</el-radio-button>
                <el-radio-button :label="DateRangeType.LST_THIRTY_DAY">{{ formatI18n('/公用/日期', '近30天') }}</el-radio-button>
                <el-radio-button :label="DateRangeType.PREVIOUS_WEEK">{{ formatI18n('/公用/日期', '上周') }}</el-radio-button>
                <el-radio-button :label="DateRangeType.PREVIOUS_MONTH">{{ formatI18n('/公用/日期', '上月') }}</el-radio-button>
                <el-radio-button :label="DateRangeType.PREVIOUS_QUARTER">{{ formatI18n('/公用/日期', '上季度') }}</el-radio-button>
                <el-radio-button :label="DateRangeType.PREVIOUS_HALF_YEAR">{{ formatI18n('/公用/日期', '上自然半年') }}</el-radio-button>
                <el-radio-button :label="DateRangeType.PREVIOUS_YEAR">{{ formatI18n('/公用/日期', '上一年') }}</el-radio-button>
              </el-radio-group>
            </form-item>

          </el-col>
          <el-col :span="6">
            <form-item :label="formatI18n('/公用/券模板', '门店')" style="float: right;margin-right: 24px;">
              <SelectStores v-model="orgCurStore" @change="doOrgStoreChange" :isOnlyId="true" :hideAll="false" width="300px"
                :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
              </SelectStores>
            </form-item>
          </el-col>
        </el-row>
        <div class="gird" style="padding-bottom: 24px;">
          <div class="grid-item" style="min-width: 380px;" v-loading="statsOrgCycleLoading">
            <el-progress :percentage="getOrgCycleStatsPercent" :stroke-width="18" :width="220" color="#FFAA00" type="circle">
            </el-progress>
            <div class="bottom-desc">
              <div class="left"><span class="dot color2"></span>{{ formatI18n('/分析/门店会员增长概况/门店会员增长概况', '消费2次以上会员（人）') }}<span
                  class="count">{{ orgCycleStats.consumeGE2Count }}</span></div>
              <div class="left"><span class="dot"></span>{{ formatI18n('/分析/门店会员增长概况/门店会员增长概况', '有消费会员（人）') }}<span
                  class="count">{{ orgCycleStats.consumeCount }}</span></div>
            </div>
          </div>
          <div class="grid-item" style="align-items: flex-end;" v-loading="orgCycleReportLoading">
            <el-table :data="orgCycleReportList" border style="width: 100%; flex: initial;">
              <el-table-column :label="formatI18n('/公用/券模板', '门店')" show-overflow-tooltip prop="store" />
              <el-table-column :label="formatI18n('/分析/门店会员增长概况/门店会员增长概况', '消费2次以上会员（人）')" prop="consumeGE2Count" />
              <el-table-column :label="formatI18n('/分析/门店会员增长概况/门店会员增长概况', '会员复购率')" prop="repurchaseRate" />
            </el-table>
            <el-pagination style="margin-top: 24px;" :current-page="orgCycleReportPage.currentPage" :page-size="orgCycleReportPage.size"
              :page-sizes="[10, 20, 30, 40]" :total="orgCycleReportPage.total" :pager-count="5" @current-change="orgCycleReportCurrentChange"
              @size-change="orgCycleReportSizeChange" background layout="total, prev, pager, next, sizes,  jumper">
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
    <DownloadCenterDialog :dialogvisiable="dialogvisiable" :showTip="showTip" @dialogClose="doDialogClose">
    </DownloadCenterDialog>
  </div>
</template>

<script lang="ts" src="./MemberAnalysis.ts">
</script>

<style lang="scss">
.member-analysis {
  width: 100%;
  height: 100%;
  background-color: white;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .gird {
    display: flex;
    margin-left: 24px;
    margin-right: 24px;
    margin-top: 20px;
    .grid-item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      .bottom-desc {
        width: 100%;
        position: relative;
        padding-left: 44px;
        padding-right: 44px;
        color: rgba(90, 95, 102, 1);
        .left {
          text-align: left;
          margin-top: 10px;
        }
        .dot {
          display: inline-block;
          width: 16px;
          height: 16px;
          border-radius: 100%;
          position: relative;
          top: 2px;
          margin-right: 5px;
        }
        .color1 {
          background-color: #6a4ef3;
        }
        .color2 {
          background-color: #ffaa00;
        }
        .color3 {
          background-color: #0cc66d;
        }
        .color4 {
          background-color: #016cff;
        }
        .color5 {
          background-color: #5cdff7;
        }
        .count {
          float: right;
          font-size: 14px;
          font-weight: 500;
        }
      }
    }
    .left-border {
      border-left: 1px solid rgba(238, 239, 241, 1);
    }
  }
  .content {
    background-color: white;
    .title {
      margin: 24px;
      font-size: 16px;
      font-weight: 500;
      color: rgba(36, 39, 43, 1);
      line-height: 24px;
    }
  }
  .table-wrap {
    margin: 30px 24px;
  }
  .footer {
    margin: 24px;
  }
  .line {
    height: 16px;
    background-color: #eeeff1;
  }
  .vertical {
    height: 100%;
    width: 16px;
    background-color: #eeeff1;
  }
  .sketch {
    flex: 1;
    border-radius: 10px;
    .name {
      margin-top: 28px;
      margin-left: 20px;
      font-size: 14px;
      font-weight: 500;
      color: rgba(90, 95, 102, 1);
    }
    .value {
      margin-top: 16px;
      margin-left: 20px;
      font-size: 32px;
      font-weight: bold;
      color: rgba(36, 39, 43, 1);
    }
  }
  .top-tab {
    height: 136px;
    border-radius: 10px;
    display: flex;
    .back {
    }
  }
}
</style>