import { ExpireRefundType } from './ExpireRefundType'
import { RefundAlgorithm } from './RefundAlgorithm'
export default class AliPromoteCouponInfo {
  // 券批次号
  batchNumber: Nullable<string> = null
  // 券模板号
  couponTemplateNumber: Nullable<string> = null
  // 券模板名称
  couponTemplateName: Nullable<string> = null
  // 券包图片
  image: Nullable<string> = null
  // 价格
  price: Nullable<number> = null
  // 购买须知
  notice: Nullable<string> = null
  // 促销主题
  promoteTopic: Nullable<string> = null
  // 促销类型
  promoteType: Nullable<string> = null
  // 促销单号
  promoteNumber: Nullable<string> = null
  // 促销说明
  promoteRemark: Nullable<string> = null
  // 券价格
  templatePrice: Nullable<number> = null
  // 是否允许用户申请退款
  enableUserApplyRefund: Nullable<boolean> = false
  // 是否允许延期
  enableApplyDelay: Nullable<boolean> = false
}