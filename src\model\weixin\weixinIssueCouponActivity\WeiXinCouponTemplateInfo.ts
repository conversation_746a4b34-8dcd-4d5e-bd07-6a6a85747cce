import { WeiXinCouponType } from './WeiXinCouponType'

// 支付宝券模板信息
export default class WeiXinCouponTemplateInfo {
  // 小程序券类型
  weiXinCouponType: Nullable<WeiXinCouponType> = null
  // 批次号
  batchNumber: Nullable<string> = null
  // 售价
  price: Nullable<number> = null
  // 批次名
  batchName: Nullable<string> = null
  // 券类型,满减券：NORMAL：固定面额满减券批次,CUT_TO：减至券
  couponType: Nullable<string> = null
  // 券有效期类型、ABSOLUTE：绝对时间 RELATIVE 相对时间
  validPeriodType: Nullable<string> = null
  // 特殊可选: 券生效后N天内可以使用。 可以配合wait_days_after_receive字段使用
  validDaysAfterReceive: Nullable<number> = null
  // 特殊可选: 用户领券后需要等待N天，券才可以生效。默认用户领券后立刻生效。 限制： 1、type为RELATIVE时可选。
  waitDaysAfterReceive: Nullable<number> = null
  // 有效期
  beginTime: Nullable<Date> = null
  // 有效期
  endTime: Nullable<Date> = null
  // 面额
  faceAmount: Nullable<number> = null
  // 门槛
  threshold: Nullable<number> = null
  // 总发券张数
  totalIssueCount: Nullable<number> = null
  // 单个用户可领数
  memberIssueCount: Nullable<number> = null
  // 封顶金额
  maxAmount: Nullable<number> = null
  // 折扣率
  discount: Nullable<number> = null
  // 商品范围
  goodRange: Nullable<string> = null
  image?: any
}