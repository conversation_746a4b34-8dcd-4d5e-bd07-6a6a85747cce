<!--
 * @Author: 黎钰龙
 * @Date: 2023-07-31 18:14:05
 * @LastEditTime: 2024-03-06 14:21:43
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\datum\store\StoreDtl.vue
 * 记得注释
-->
<template>
  <div class="store-view">
    <BreadCrume :panelArray="panelArray">
    </BreadCrume>
    <div class="current-page">
      <div class="section-title">
        {{ formatI18n('/会员/洞察/客群管理/新建页/基础信息') }}
      </div>
      <FormItem :label="formatI18n('/资料/门店/门店代码') + ': '" v-if="shopDetail.org">
        <div style="height: 36px; line-height: 36px">
          {{ shopDetail.org.id || '--' }}
        </div>
      </FormItem>
      <FormItem :label="formatI18n('/资料/门店/门店名称') + ': '" v-if="shopDetail.org">
        <div style="height: 36px; line-height: 36px">
          {{ shopDetail.org.name || '--' }}
        </div>
      </FormItem>
      <FormItem :label="formatI18n('/资料/门店/所属营销中心') + ': '" v-if="enableMultiMarketingCenter">
        <div style="height: 36px; line-height: 36px">
          【{{ shopDetail.marketingCenter }}】{{ shopDetail.marketingCenterName }}
        </div>
      </FormItem>
      <FormItem :label="formatI18n('/资料/门店/所属区域') + ': '">
        <div style="height: 36px; line-height: 36px" v-if="shopDetail.zoneId">
          【{{ shopDetail.zoneId }}】{{ shopDetail.zoneName }}
        </div>
        <div style="height: 36px; line-height: 36px; color: red" v-else>
          {{ formatI18n("/资料/门店/无所属区域") }}
        </div>

      </FormItem>
      <FormItem :label="formatI18n('/资料/门店/门店地址') + ': '" v-if="shopDetail.address">
        <div style="height: 36px; line-height: 36px">
          {{ buildStoreAddress(shopDetail.address) }}
        </div>
      </FormItem>
      <FormItem :label="formatI18n('/资料/门店/区域主管') + ': '">
        <div style="height: 36px; line-height: 36px">
          <template v-if="shopDetail.areaLeader">
            [{{shopDetail.areaLeader}}]{{shopDetail.areaLeaderName}}
          </template>
          <template v-else>-</template>
        </div>
      </FormItem>
      <FormItem :label="formatI18n('/资料/门店/营运经理') + ': '">
        <div style="height: 36px; line-height: 36px">
          <template v-if="shopDetail.operationManager">
            [{{shopDetail.operationManager}}]{{shopDetail.operationManagerName}}
          </template>
          <template v-else>-</template>
        </div>
      </FormItem>
      <FormItem :label="formatI18n('/资料/门店/联系电话') + ': '">
        <div style="height: 36px; line-height: 36px">
          {{ shopDetail.telephone || '-' }}
        </div>
      </FormItem>
      <FormItem :label="formatI18n('/公用/券模板/标签') + ': '">
        <div style="height: 36px; line-height: 36px">
          <template v-if="shopDetail.orgTags && shopDetail.orgTags.length">
            <el-tag class="tag-block" v-for="item in shopDetail.orgTags" :key="item.tagName" type="info">
              {{item.tagName}}
            </el-tag>
          </template>
          <template v-else>-</template>
        </div>
      </FormItem>
      <FormItem :label="formatI18n('/资料/门店/坐标') + ': '">
        <div style="height: 36px; line-height: 36px">
          {{  buildStoreCoordinate(shopDetail.lat, shopDetail.lng) }}
        </div>
      </FormItem>
      <FormItem :label="formatI18n('/资料/门店/渠道门店')">
        <el-table :data="platOrgInfos" max-height="250" style="width: 750px" ref="table">
          <el-table-column width="120" :label="formatI18n('/资料/渠道/渠道类型') + ': '">
            <template slot-scope="scope">
              {{ scope.row.platformName || '--'}}
            </template>
          </el-table-column>
          <el-table-column width="180" :label="formatI18n('/资料/渠道/渠道名称') + ': '">
            <template slot-scope="scope">
              {{ scope.row.channelName || '--' }}
            </template>
          </el-table-column>
          <el-table-column :label="formatI18n('/资料/渠道/渠道ID') + ': '">
            <template slot-scope="scope">
              {{ scope.row.channelId || '--' }}
            </template>
          </el-table-column>
          <el-table-column :label="formatI18n('/资料/门店/渠道门店ID') + ': '">
            <template slot-scope="scope">
              {{ scope.row.platformStoreId || '--' }}
            </template>
          </el-table-column>
        </el-table>
      </FormItem>
      <FormItem :label="formatI18n('/资料/门店/门店状态') + ': '">
        <div style="height: 36px; line-height: 36px">{{OrgStateMap[shopDetail.orgState]}}</div>
      </FormItem>
    </div>
  </div>
</template>
  
<script lang="ts" src="./StoreDtl.ts">
</script>
  
<style lang="scss" scoped>
.store-view {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: hidden;

  .current-page {
    padding: 12px 24px 12px 24px;
    height: calc(100% - 46px);
    background: #ffffff;
    border-radius: 8px;
    overflow-y: auto;
    .tag-block {
      display: inline-block;
      padding: 0 6px;
      margin-top: 6px;
      margin-right: 8px;

      &:nth-last-child {
        margin-right: 0;
      }
    }
  }
  ::v-deep .qf-form-item .qf-form-label {
    text-align: left !important;
  }
}
</style>