<template>
    <div class="datum-area-view ">
        <BreadCrume :panelArray="panelArray">
        </BreadCrume>
        <div class="datum-area-view-page">
            <div class="datum-area-view-page-left">
                <el-input placeholder="请输入名称、代码" v-model="filterText" suffix-icon="el-icon-search">
                </el-input>
                <div class="datum-area-view-page-left-title">
                    <div>
                        <span style="color: #79879E ;">
                            {{   }}
                            <span style="font-weight: bold;color: #000;margin: 0 4px;">{{
                            count }}</span>
                            {{ i18n('个区域')  }}    
                        </span>
                        <span @click="expandAll = !expandAll" style="cursor: pointer;">
                            <img class="datum-area-view-page-left-title-img"
                                src="@/assets/image/datum/<EMAIL>" />
                            {{ expandAll ? i18n('/公用/查询条件/收起') : i18n('/公用/查询条件/展开') }}
                        </span>
                    </div>
                    <div>
                        <el-button @click="onAddArea" type="primary" icon="el-icon-add">
                            {{  i18n('/公用/门店组件/添加区域') }}
                        </el-button>
                    </div>
                </div>
                <div class="datum-area-view-page-left-tree">
                    <el-tree class="filter-tree" :data="data" :props="{
                        children: 'child',
                    }" :key="expandAll" :default-expand-all="expandAll" highlight-current
                        :empty-text="formatI18n('/公用/提示/暂无数据')" :filter-node-method="filterNode"
                        @current-change="currentChange"
                        node-key="zoneId"
                        ref="tree">
                        <template #default="{ node, data }">
                            <span class="custom-tree-node">
                                <span>
                                    <img v-if="node.level <= 3" class="custom-tree-node-img"
                                        src="@/assets/image/datum/<EMAIL>" />
                                    <img v-else class="custom-tree-node-img"
                                        src="@/assets/image/datum/<EMAIL>" />
                                    {{ data.zone.zone.name }}
                                </span>
                                <span v-if="node.level <= 4" class="custom-tree-node-btn">
                                    <el-button icon="el-icon-plus" type="text" size="mini" @click.stop="onAddArea(data, 'peer')">
                                        {{ i18n('同级区域') }}
                                    </el-button>
                                    <el-button v-if="node.level <= 3" icon="el-icon-plus" type="text" size="mini" @click.stop="onAddArea(data, 'lower')">
                                        {{ i18n('下级区域') }}
                                    </el-button>
                                    <el-button v-if="data.zone.state !== 'CANCEL'" class="custom-tree-node-btn-red" type="text" size="mini"
                                        @click.stop="openCancellationDialog(data)">
                                        {{ i18n('作废') }}
                                    </el-button>
                                </span>
                            </span>
                        </template>
                    </el-tree>
                </div>
            </div>
            <div class="datum-area-view-page-right">
                <div v-if="currentSelectBZoneTreeNode.zone.zone.id">
                    【{{currentSelectBZoneTreeNode.zone.zone.id}}】{{  currentSelectBZoneTreeNode.zone.zone.name }}
                </div>
                <div class="datum-area-view-page-right-form">
                    <el-form ref="form" :model="form"  :rules="rules" label-width="140px">
                        <el-form-item :label="i18n('区域代码')">
                            <el-input v-model="form.areaCode" disabled></el-input>
                        </el-form-item>
                        <el-form-item :label="i18n('区域名称')">
                            <el-input v-model="form.areaName"></el-input>
                        </el-form-item>
                        <el-form-item v-if="isMoreMarketing" :label="i18n('所属营销中心')">
                            【{{marketCenter}}】{{ marketCenterName }}
                        </el-form-item>
                        <el-form-item>
                            <el-button :loading="saveBtnLoading" type="primary" @click="onUpdate">{{ i18n('保存') }}</el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </div>
        </div>

        <AddAreaDialog :marketCenter="marketCenter" :marketCenterName="marketCenterName" ref="AddAreaDialog" @success="queryTreeData()"></AddAreaDialog>
    </div>
</template>
<script lang="ts" src='./DatumArea.ts'>
</script>
<style lang="scss" scoped>
.datum-area-view {
    background-color: white;
    height: 100%;
    width: 100%;
    overflow: hidden;

    .datum-area-view-page {
        height: calc(100% - 50px);
        overflow: auto;
        display: flex;

        &-left {
            padding: 24px;
            flex: 448px;
            flex-shrink: 0;
            border-right: 1px solid #D7DFEB;

            &-tree {
                height: calc(100% - 84px);
                overflow-y: auto;

                ::v-deep .is-current {
                    &>.el-tree-node__content {
                        .custom-tree-node-btn {
                            display: inline-block !important;
                        }
                    }
                }
            }

            &-title {
                display: flex;
                justify-content: space-between;
                padding: 12px 0;
                height: 52px;
                line-height: 28px;

                &-img {
                    width: 16px;
                    height: 16px;
                    margin-left: 12px;
                    position: relative;
                    top: 2px;
                    cursor: pointer;
                }
            }

            .custom-tree-node {
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-size: 14px;
                padding-right: 8px;

                &-img {
                    width: 16px;
                    height: 16px;
                    position: relative;
                    top: 2px;
                    cursor: pointer;
                }

                .custom-tree-node-btn-red {
                    color: #FC0049;
                }

                &-btn {
                    display: none;
                }

                &:hover {
                    .custom-tree-node-btn {
                        display: inline-block;
                    }
                }
            }
        }

        &-right {
            flex: calc(100% - 448px);
            padding: 24px;
            min-width: 400px;

            &-form {
                padding-top: 12px;
                max-width: 460px;

                ::v-deep .el-form-item {
                    margin-bottom: 12px;
                }
            }
        }
    }
}
</style>