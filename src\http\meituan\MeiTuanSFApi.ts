import ApiClient from 'http/ApiClient'
import MeiTuanShopCateringAuthRequest from 'model/meituanshopcatering/MeiTuanShopCateringAuthRequest'
import Response from 'model/default/Response'
import MeiTuanSFAuthRequest from "model/meituansf/MeiTuanSFAuthRequest";

export default class MeiTuanSFApi {
  /**
   * 获取到综三方授权链接
   * 获取到综三方授权链接。
   * 
   */
  static getAuthUrl(body: MeiTuanSFAuthRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/meituansf/getAuthUrl`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
