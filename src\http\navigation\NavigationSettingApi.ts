import ApiClient from 'http/ApiClient'
import NavigationSetting from 'model/navigation/NavigationSetting'
import Response from 'model/default/Response'
import { CmsConfigChannel } from 'model/template/CmsConfig'

export default class NavigationSettingApi {
  /**
   * 获取导航内容
   * 
   */
  static get(channel: CmsConfigChannel): Promise<Response<NavigationSetting>> {
    return ApiClient.server().get(`/v1/cms/navigation/setting`, {
      params: {
        channel
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存导航内容
   * 
   */
  static save(body: NavigationSetting): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/cms/navigation/setting/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
