import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import MemberTab from "pages/member/data/cmp/MemberTab";
import I18nPage from "common/I18nDecorator";
import BenefitCard from "model/benefitCard/BenefitCard";
import EquityCard from "model/equityCard/EquityCard";
import MemberApi from "http/member_standard/MemberApi";
import EquityCardQueryRequest from "model/equityCard/EquityCardQueryRequest";
import EquityCardApi from "http/benefit/EquityCardApi";
import MemberDetailCard from "pages/member/data/cmp/MemberDetailCard";
import MemberDetail from "model/member_standard/MemberDetail";
import EmptyData from "pages/member/data/cmp/EmptyData";
import TradeFilter from "model/member/TradeFilter";
import BenefitCardApi from "http/benefitCard/BenefitCardApi";
import BenefitCardQueryReq from "model/benefitCard/BenefitCardQueryReq";

@Component({
  name: "MemberCardDetail",
  components: { MemberTab, MemberDetailCard, EmptyData },
})
@I18nPage({
  prefix: [
    "/会员/会员资料",
  ],
  auto: true,
})
export default class MemberCardDetail extends Vue {
  @Prop()
  dtl: MemberDetail;

  created() {
    this.changeTabLabel();
  }

  @Watch("dtl", { immediate: true })
  dtlChanged() {
    if (!(this.dtl && this.dtl.memberId)) return;
    this.onSearch();
    // 获取权益卡的数量（因为初始化不加载权益卡
    this.getFreeTotal();
  }

  currentTabIndex: number = 0;
  tabs: Array<string> = [];

  changeTabLabel() {
    this.tabs = [this.i18n("付费会员（{0}张）", [this.bCardCount + ""]), this.i18n("权益卡（{0}张）", [this.eCardCount + ""])];
  }

  onTabChange(index: number) {
    this.currentTabIndex = index;
    this.onSearch();
  }

  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10,
    probeEnabled: null,
  };

  query: BenefitCardQueryReq = new BenefitCardQueryReq();
  cards: BenefitCard[] = [];
  bCardCount: number = 0;
  eCardCount: number = 0;

  onSearch() {
    this.page.currentPage = 1;
    this.cards = [];
    this.getList();
  }

  getFreeTotal() {
    const query = new BenefitCardQueryReq();
    query.memberIdEquals = this.dtl.memberId;
    query.templateTypeEquals = "free";
    query.page = 0;
    query.pageSize = 1;
    BenefitCardApi.query(query).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.eCardCount = resp.total;
        this.changeTabLabel();
      }
    });
  }

  private getList() {
    this.query.memberIdEquals = this.dtl.memberId;
    this.query.templateTypeEquals = this.currentTabIndex == 0 ? "paid" : "free";
    this.query.page = this.page.currentPage - 1;
    this.query.pageSize = this.page.size;
    BenefitCardApi.query(this.query).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.cards = resp.data;
        if (this.currentTabIndex == 0)
          this.bCardCount = resp.total;
        else
          this.eCardCount = resp.total;
        this.changeTabLabel();
        this.page.total = resp.total;
        this.page.probeEnabled = resp.fields ? resp.fields.probeEnabled : null;
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message);
      }
    });
  }

  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val;
    this.getList();
  }

  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val;
    this.getList();
  }
}
