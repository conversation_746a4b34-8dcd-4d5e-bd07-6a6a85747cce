<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:03
 * @LastEditTime: 2023-08-25 10:03:28
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\prepaycard\giftcardreport\GiftCardReportReport.vue
 * 记得注释
-->
<template>
  <div class="gift-card-report">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button
          @click="doBatchExport"
          size="large"
          v-if="hasOptionPermission('/数据/报表/礼品卡报表','报表导出')"
        >{{ formatI18n('/会员/会员资料', '批量导出') }}</el-button>
      </template>
    </BreadCrume>
    <!-- <GiftReportSum type="OnlineGiftCard"></GiftReportSum> -->
    <div class="current-page">
      <div style="padding:20px 0 0">
        <el-tabs v-model="activeName" class="tabs">
          <el-tab-pane :label="i18n('售卡流水')" name="售卡流水" />
          <el-tab-pane :label="i18n('消费流水')" name="消费流水" />
          <el-tab-pane :label="i18n('退款流水')" name="退款流水" />
          <el-tab-pane :label="i18n('退卡流水')" name="退卡流水" />
          <el-tab-pane :label="i18n('对账报表')" name="对账报表" />
        </el-tabs>
        <SalesCardsReport v-if="activeName === '售卡流水'"></SalesCardsReport>
        <ComsumeReport v-if="activeName === '消费流水'"></ComsumeReport>
        <RefundReport v-if="activeName === '退款流水'"></RefundReport>
        <RefundCardReport v-if="activeName === '退卡流水'"></RefundCardReport>
        <DailyReport v-if="activeName === '对账报表'"></DailyReport>
      </div>
    </div>
    <DownloadCenterDialog
      :dialogvisiable="fileDialogVisible"
      :showTip="showTip"
      @dialogClose="doDownloadDialogClose"
    ></DownloadCenterDialog>
    <GiftCardExportConfirm
      :dialogShow="getExportDialogShow"
      @dialogClose="doExportDialogClose"
      @doSubmit="doExportSubmit"
    ></GiftCardExportConfirm>
  </div>
</template>

<script lang="ts" src="./GiftCardReportReport.ts">
</script>

<style lang="scss">
.gift-card-report {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: auto;
  .current-page {
    height: calc(100% - 150px);
    .tabs {
      padding: 0 20px;
    }
  }
}
</style>