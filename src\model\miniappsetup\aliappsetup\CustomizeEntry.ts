/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2023-05-15 20:25:09
 * @LastEditors: 苏国友 <EMAIL>
 * @Description: 
 * @FilePath: /phoenix-web-ui/src/model/miniappsetup/aliappsetup/CustomizeEntry.ts
 * 记得注释
 */
import { AliAppletEntryType } from './AliAppletEntryType'

// 支付宝小程序功能入口
export default class CustomizeEntry {
  // uuid
  uuid: Nullable<string> = null
  // 入口类型；APPLET——小程序跳转；SUBPAGE——子页面
  type: Nullable<AliAppletEntryType> = AliAppletEntryType.APPLET
  // 标题
  title: Nullable<string> = ''
  // 介绍
  remark: Nullable<string> = null
  // 图片
  image: Nullable<string> = null
  // 子页面图片
  subpageImage: Nullable<string> = null
  // 目标小程序appId
  appId: Nullable<string> = null
  // 目标小程序路径
  path: Nullable<string> = null
  // 排序
  sort: Nullable<number> = null
  // 是否选择
  state: Nullable<string> = 'stop'
}