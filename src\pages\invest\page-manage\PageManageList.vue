<!--
 * @Author: 黎钰龙
 * @Date: 2024-05-06 11:16:16
 * @LastEditTime: 2025-05-28 16:45:07
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\PageManageList.vue
 * 记得注释
-->
<template>
  <div style="width: 100%">
    <div class="content">
      <ListWrapper class="current-page">
        <template slot="query">
          <el-row>
            <el-col :span="8">
              <form-item :label="i18n('页面状态')">
                <el-select style="width: 180px" v-model="ContentTemplateFilter.stateEquals">
                  <el-option :value="null" :label="i18n('全部')">{{ i18n("全部") }}</el-option>
                  <el-option v-for="value in ContentTemplateState" :value="value" :label="value === 'unpublished' ? i18n('未发布') : i18n('已发布')"
                    :key="value">
                    {{ value === "unpublished" ? i18n("未发布") : i18n("已发布") }}
                  </el-option>
                </el-select>
              </form-item>
            </el-col>
            <el-col :span="8">
              <form-item :label="i18n('页面名称')">
                <el-input :placeholder="i18n('请输入页面名称')" v-model="ContentTemplateFilter.nameLike" style="width: 90%" />
              </form-item>
            </el-col>
          </el-row>
          <el-row style="margin-top: 10px">
            <el-col :span="8">
              <form-item label="">
                <el-button class="btn-search" @click="doSearch" size="small" type="primary">{{i18n('查询')}}</el-button>
                <el-button class="btn-reset" @click="doReset" size="small">{{i18n('重置')}}</el-button>
              </form-item>
            </el-col>
          </el-row>
        </template>
        <template slot="btn">
          <el-button size="large" type="primary" @click="doCreate" v-if="hasOptionPermission('/设置/小程序装修/页面管理','新建')">
            {{i18n('新建自定义页面')}}
          </el-button>
          <el-button size="large" @click="batchEditShareInfo" v-if="hasOptionPermission('/设置/小程序装修/页面管理','编辑')">
            {{i18n('批量修改分享信息')}}
          </el-button>
        </template>
        <template slot="list">
          <el-table :data="ContentTemplate" row-key="templateId" ref="table" @selection-change="handleSelectionChange"
            style="width: 100%; margin-top: 12px">
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column :label="i18n('页面名称')">
              <template slot-scope="scope">
                {{ scope.row.name || "--" }}
              </template>
            </el-table-column>
            <el-table-column :label="i18n('页面状态')" width="255">
              <template slot="header">
                {{ i18n("页面状态") }}
                <el-tooltip class="item" effect="dark" :content="i18n('发布状态：已发布的页面，小程序端会展示；未发布的页面，小程序端不会展示，但可以编辑。点击发布后，小程序端是5分钟内生效，不是立即生效')"
                  placement="top">
                  <i class="el-icon-warning"></i>
                </el-tooltip>
              </template>
              <template slot-scope="scope">
                <span class="dot" :class="scope.row.state === 'unpublished' ? 'orange' : 'green'"></span>
                {{ scope.row.state === "unpublished" ? i18n("未发布") : i18n("已发布") || "--" }}
                <el-tooltip class="item" effect="dark" :content="i18n('存在页面内容已保存但未发布')" placement="top-start">
                  <i class="el-icon-warning" v-if="scope.row.hasDraft"></i>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column :label="i18n('/设置/页面管理/投放渠道')" width="255">
              <template slot-scope="scope">
                {{ getLabel(scope.row.channels) }}
              </template>
            </el-table-column>
            <el-table-column :label="i18n('最后修改时间')" width="255">
              <template slot-scope="scope">
                {{ scope.row.lastModified || "--" }}
              </template>
            </el-table-column>
            <el-table-column :label="i18n('操作')">
              <template slot-scope="scope">
                <span class="span-btn" style="width: 30px" @click.stop="doEdit(scope.row)" v-if="hasOptionPermission('/设置/小程序装修/页面管理','编辑')">
                  {{ i18n("编辑") }}
                </span>
                <span class="span-btn" style="margin-left: 8px" @click.stop="doPublish(scope.row)"
                  v-if="(scope.row.state === 'unpublished' || scope.row.hasDraft) && hasOptionPermission('/设置/小程序装修/页面管理','发布')">
                  {{ i18n("发布") }}
                </span>
                <span class="span-btn" style="margin-left:8px" @click.stop="doSpread(scope.row)"
                  v-if="scope.row.state !== 'unpublished' && hasOptionPermission('/设置/小程序装修/页面管理','推广')">
                  {{ i18n("推广") }}
                </span>
                <span class="span-btn" style="margin-left: 8px" @click.stop="doRemove(scope.row)"
                  v-if="scope.row.state === 'unpublished' && hasOptionPermission('/设置/小程序装修/页面管理','删除')">
                  {{ i18n("删除") }}
                </span>
                <span @click.stop="doEditShare(scope.row)" v-if="hasOptionPermission('/设置/小程序装修/页面管理','编辑')" style="margin-left: 8px"
                  class="span-btn">
                  {{i18n("分享信息")}}
                </span>
                <span @click.stop="showEmployeeCode(scope.row)" style="margin-left: 8px" class="span-btn"
                  v-if="scope.row.state !== 'unpublished' && hasOptionPermission('/设置/小程序装修/页面管理','员工推广码')">
                  {{i18n("员工推广码")}}
                </span>
                <span @click.stop="showStoreCode(scope.row)" style="margin-left: 8px" class="span-btn"
                      v-if="scope.row.state !== 'unpublished' && hasOptionPermission('/设置/小程序装修/页面管理','门店推广码')">
                  {{i18n("门店推广码")}}
                </span>
                <template v-if="canSetNavigation(scope.row).stopable">
                  <span @click.stop="doChangeNavigationShow(scope.row,false)" style="margin-left: 8px" class="span-btn"
                    v-if="hasOptionPermission('/设置/小程序装修/页面管理','启用/停用页面导航')">
                    {{i18n("停用页面导航")}}
                  </span>
                </template>
                <template v-else-if="canSetNavigation(scope.row).enable">
                  <span @click.stop="doChangeNavigationShow(scope.row, true)" style="margin-left: 8px" class="span-btn"
                    v-if="hasOptionPermission('/设置/小程序装修/页面管理','启用/停用页面导航')">
                    {{i18n("启用页面导航")}}
                  </span>
                </template>
              </template>
            </el-table-column>
          </el-table>
        </template>
        <template slot="page">
          <el-pagination :current-page.sync="page.currentPage" :page-size="page.size" :page-sizes="[20, 30, 40]" :total="page.total" background
            layout="total, prev, pager, next, sizes,  jumper" @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange"
            style="margin-top: 20px"></el-pagination>
        </template>
      </ListWrapper>
    </div>
    <PageSpreadDialog ref="pageSpreadDialog"></PageSpreadDialog>
    <ShareConfigDialog ref="shareConfigDialog" @submit="doChangeShareInfo"></ShareConfigDialog>
    <EmployeeDialog ref="employeeDialog" @openDownload="openDownload"></EmployeeDialog>
    <StoreCodeDialog ref="storeCodeDialog" @openDownload="openDownload"></StoreCodeDialog>
    <DownloadCenterDialog :dialogvisiable="downloadShow" @dialogClose="doDialogClose" :showTip="true">
    </DownloadCenterDialog>
    <ReleaseChannelDialog :channelOptions="cmsConfig.publishedChannels" ref="releaseChannelDialog" @submit="doSelectChannel"></ReleaseChannelDialog>
  </div>
</template>

<script lang="ts" src="./PageManageList.ts">
</script>

<style lang="scss" scoped>
.content {
  .dot {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }
  .orange {
    background: #ef821e;
  }
  .green {
    background: #58b929;
  }
}
</style>