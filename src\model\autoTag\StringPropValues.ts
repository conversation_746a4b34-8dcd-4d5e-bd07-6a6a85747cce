/*
 * @Author: 黎钰龙
 * @Date: 2024-11-28 16:02:19
 * @LastEditTime: 2024-12-13 11:40:31
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\autoTag\StringPropValues.ts
 * 记得注释
 */
import IdName from "model/common/IdName"
import { OperatorType } from "./OperatorType"

export default class StringPropValues {
  // 运算符
  operator: Nullable<OperatorType> = null
  // 字符串属性值
  value: Nullable<string> = null
  // 属性值选择列表（仅前端使用）
  selectList: Nullable<IdName[]> = []
}