import IdName from 'model/common/IdName'

export default class GiftReportBaseData {
  // 卡号
  code: Nullable<string> = null
  // 卡名称
  cardName: Nullable<string> = null
  // 发生时间
  occurredTime: Nullable<Date> = null
  // 发生组织
  occurredOrg: Nullable<IdName> = null
  // 购卡人会员id
  memberId: Nullable<string> = null
  // 购卡人会员手机号
  mobile: Nullable<string> = null
  // 购卡人会员号
  hdCardMbrId: Nullable<string> = null
  // 购卡人实体卡号
  hdCardCardNum: Nullable<string> = null
  // 交易号
  transNo: Nullable<string> = null
  // 卡模板号用于详情跳转
  templateNumber: Nullable<string> = null
  // 卡模板名称
  templateName: Nullable<string> = null
  // 卡面额
  faceAmount: Nullable<number> = null
  // 原实充金额
  originalAmount: Nullable<number> = null
  // 原赠送金额
  originalGiftAmount: Nullable<number> = null
}