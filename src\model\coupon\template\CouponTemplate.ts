/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-11-06 14:21:54
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\coupon\template\CouponTemplate.ts
 * 记得注释
 */
import ChannelRange from 'model/common/ChannelRange'
import CouponTemplateOuterRelation from 'model/coupon/template/CouponTemplateOuterRelation'
import CouponTemplateTagRelation from '../CouponTemplateTagRelation'

export default class CouponTemplate {
  // 券模板号
  number: Nullable<string> = null
  // 券模板名称
  name: Nullable<string> = null
  // 券模板类型:券基础类型：all_cash——全场现金券； goods_cash——商品现金券； all_discount——全场折扣券； rfm_type——商品折扣券； goods_discount——单品折扣券;goods——提货券；
  type: Nullable<string> = null
  // 券面额/折扣力度
  faceAmountOrDiscount: Nullable<number> = null
  // 有效期类型：FIXED——固定有效期； RALATIVE——相对有效期;
  validityType: Nullable<string> = null
  // 用券渠道
  channels: Nullable<ChannelRange> = null
  // 用券门店：ALL——全部； PART——部分； EXCLUDE--指定门店不参加
  storeRangeType: Nullable<string> = null
  // 组Id
  groupId: Nullable<string> = null
  // 券模板状态
  state: Nullable<string> = null
  // 外部券模板号id
  outerNumberId: Nullable<string> = null
  // 外部券模板号namespace
  outerNumberNamespace: Nullable<string> = null
  // 应用范围，为weixin时不可编辑
  scope: Nullable<string> = null
  // 最后修改时间
  lastModified: Nullable<Date> = null
  // 券模板外部关系
  outerRelations: CouponTemplateOuterRelation[] = []
  // 是否当前营销中心全部门店
  allMarketingCenterStores: Nullable<Boolean> = null
  // 创建人
  creator: Nullable<Boolean> = null
  // 最后修改人
  lastModifier: Nullable<string> = null
  // 关联券模板活动
  activityReference: string[] = []
  // 标签关系
  templateTag: CouponTemplateTagRelation[] = []
}
