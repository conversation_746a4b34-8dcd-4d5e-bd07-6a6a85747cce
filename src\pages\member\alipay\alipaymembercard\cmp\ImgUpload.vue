<template>
    <div class="img-upload">
        <el-upload
                :headers="uploadHeaders"
                :action="uploadUrl"
                :with-credentials="true"
                :show-file-list="false"
                :on-success="onUploadLogo"
                :before-upload="beforeAvatarUpload"
                class="avatar-uploader">
            <div style="position: relative">
                <span v-if="logo"
                      @click.stop="doRemove"
                      style="display: inline-block;width: 15px;height: 15px;background-color: red;position: absolute;top: 0px;right: 0px;border-radius: 100%;cursor: pointer">
                    <i style="color: white" class="iconfont ic-close"></i>
                </span>
                <img :src="logo" class="avatar" v-if="logo"/>
                <i class="el-icon-plus avatar-uploader-icon" v-else></i>
            </div>
        </el-upload>
    </div>
</template>

<script lang="ts" src="./ImgUpload.ts">
</script>

<style lang="scss">

</style>