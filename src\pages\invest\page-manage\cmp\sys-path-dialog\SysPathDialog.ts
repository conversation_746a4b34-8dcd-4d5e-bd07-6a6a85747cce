import I18nPage from 'common/I18nDecorator';
import ContentTemplateApi from 'http/template/ContentTemplateApi';
import { Component, Vue } from 'vue-property-decorator';
import PageSpreadDialog from '../page-spread-dialog/PageSpreadDialog';
import UnionActivityApi from 'http/promotion/UnionActivityApi';
import UnionActivityQuery from 'model/promotion/UnionActivityQuery';
import ActivityStateTag from 'cmp/activity-state-tag/ActivityStateTag';
import GiftCardActivityApi from 'http/card/activity/GiftCardActivityApi';
import GiftCardActivityFilter from 'model/card/activity/GiftCardActivityFilter';
import PointsActivityApi from 'http/points/activity/PointsActivityApi';
import PointsActivityFilter from 'model/points/activity/PointsActivityFilter';
import ActivityGroupType from 'model/common/ActivityGroupType';
import { debounce } from "lodash";
import ConstantMgr from 'mgr/ConstantMgr';
import CmsConfig, { CmsConfigChannel } from 'model/template/CmsConfig';
import PopularizeResponse from 'model/template/PopularizeResponse';
type PageItem = {
  name: string
  path: string
}

class PageData {
  static pageData(){
    return [
      {
        name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "会员码"),
        path: 'member/member-code/member-code'
      },
      {
        name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "我的优惠券"),
        path: 'pages/coupon/CouponList'
      },
      {
        name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "我的礼品卡"),
        path: 'giftCard/list/List'
      },
      {
        name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "我的储值"),
        path: 'pagesSub/deposit/DepositDtl'
      },
      {
        name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "积分付款码"),
        path: 'point/point-code'
      },
      {
        name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "储值付款码"),
        path: 'pages/balance/balance-code'
      },
      {
        name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "卡充值"),
        path: 'pagesSub/deposit/CardRecharge'
      },
      {
        name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "充值有礼"),
        path: 'pagesSub/deposit/DepositCharge'
      },
      {
        name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "门店列表页"),
        path: 'pages/shop/index'
      },
      {
        name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "修改个人资料"),
        path: 'member/modify-member/modify-member'
      },
      {
        name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "等级规则"),
        path: 'member/member-rules/member-rules'
      },
      {
        name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "兑换码"),
        path: 'coupon/code/ExchangeCode'
      },
      {
        name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "大转盘中奖记录"),
        path: 'pagesSub/big-wheel/MyAward'
      },
      {
        name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "订单"),
        path: 'member/order/OrderList'
      },
      // {
      //   name: '积分兑换券',
      //   path: 'point/PointHome'
      // },
      // {
      //   name: '领券中心',
      //   path: 'pages/coupon/CouponCenter'
      // },
      // {
      //   name: '邀请有礼',
      //   path: 'pagesSub/invite-gift/InviteGift'
      // },
      // {
      //   name: '集点活动',
      //   path: 'pagesSub/collect-point/CollectPointList'
      // },
      // {
      //   name: '大转盘活动',
      //   path: 'pagesSub/big-wheel/BigWheelList'
      // },
    ] as PageItem[]
  } 
}



@Component({
  name: 'SysPathDialog',
  components: {
    PageSpreadDialog,
    ActivityStateTag
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理',
    '/资料/渠道'
  ],
  auto: true
})
export default class SysPathDialog extends Vue {
  $refs: any
  visible: boolean = false
  tabSelect: string = 'activePage'
  tabChildSelect: Nullable<string> = null
  sysPathLike: string = ''  //页面名称类似于
  sysPath: PageItem[] = []
  filterData: PageItem[] = []
  loading: boolean = false
  detailListLoading: boolean = false
  cmsConfig: CmsConfig = new CmsConfig()
  centerType: string = this.i18n('功能页面')

  detailList: any[] = []

  debounceQueryMethod = this.debounceQueryActiveDetailList()

  filter = {
    state: 'PROCESSING',
    keyWords: '',
    activeNo: '',
  }

  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10,
  }

  get showActiveList() {
    return (this.centerType === this.i18n('功能页面') || this.centerType === this.i18n('活动列表')) ? true : false
  }

  get isBigTurntableDetail() {
    // 活动-功能页面
    return this.tabSelect === 'activePage' && this.centerType === this.i18n('功能页面')
  }

  get showH5Btn() {
    // 只有大转盘有H5链接
    return this.tabSelect === 'marketing' && this.tabChildSelect === 'bigTurntable' && this.centerType === this.i18n('活动详情')
  }

  get showWeixinBtn() {
    return this.cmsConfig.publishedChannels?.includes(CmsConfigChannel.WEIXIN)
  }

  get showAliPayBtn() {
    return this.cmsConfig.publishedChannels?.includes(CmsConfigChannel.ALIPAY)
  }

  mounted() {
    this.getConfig()
  }

  getConfig() {
    ContentTemplateApi.getConfig().then(res => {
      if (res.code === 2000) {
        this.cmsConfig = res.data || new CmsConfig()
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    })
  }

  getDetailPathUrl(row: any) {
    let result = '';
    if (this.tabSelect === 'activePage' || this.centerType === this.i18n('活动列表')) {
      result = row.path
    } else if (this.tabSelect === 'ticket') {
      // 积分兑换券
      if (this.tabChildSelect === 'points') {
        result = 'point/GoodsDtl?activityNumber=' + row?.activityId
      } else if (this.tabChildSelect === 'coupon') {
        // 小程序领券
        result = `coupon/monthly-coupon-detail/MonthlyCouponDetail?sceneNumber=${row?.activityId}`
      } else if (this.tabChildSelect === 'coupon-wx') {
        // 小程序领微信券
        result = row.batchNumber ? `pages/coupon/CouponDetail?sceneNumber=${row?.activityId}` : `coupon/monthly-coupon-detail/MonthlyCouponDetail?sceneNumber=${row?.activityId}`
      }
    } else if (this.tabSelect === 'card') {
      // 卡活动
      result = `giftCard/activity-dtl/activityDtl?activityNumber=${row?.activityId}`
    } else if (this.tabSelect === 'marketing') {
      // 营销活动
      if (this.tabChildSelect === 'bigTurntable') {
        // 大转盘
        result = `pagesSub/big-wheel/BigWheelDtl?activityNumber=${row?.activityId}`
      } else if (this.tabChildSelect === 'gatheringPoint') {
        // 集点活动
        result = `pagesSub/collect-point/CollectPointDtl?activityNumber=${row?.activityId}`
      } else if (this.tabChildSelect === 'prizeDraw') {
        // 抽奖团
        result = `pagesSub/group-booking/GroupBookingDtl?activityNumber=${row?.activityId}`
      } else if (this.tabChildSelect === 'inviteCourtesy') {
        // 邀请有礼
        result = `pagesSub/invite-gift/InviteGift`
      }
    }

    return result;
  }

  setSysPath() {
    // 页面
    if (this.tabSelect === 'activePage') {
      this.sysPath = PageData.pageData()
    } else if (this.tabSelect === 'ticket') {
      if (this.tabChildSelect === 'points') {
        this.sysPath = [{
          name: this.i18n('积分兑换券'),
          path: 'pages/coupon/CouponCenter?couponType=points_coupon'
        }]
      } else if (this.tabChildSelect === 'coupon') {
        this.sysPath = [
          {
            name: this.i18n('小程序领券'),
            path: 'pages/coupon/CouponCenter?couponType=private_coupon'
          }
        ]
      } else if (this.tabChildSelect === 'coupon-wx') {
        this.sysPath = [
          {
            name: this.i18n('小程序领微信券'),
            path: 'pages/coupon/CouponCenter?couponType=uni_single_bag_coupon'
          }
        ]
      }
    } else if (this.tabSelect === 'card') {
      this.sysPath = [
        {
          name: this.i18n('电子卡售卡活动'),
          path: 'giftCard/activity-list/ActivityList'
        }
      ]
    } else if (this.tabSelect === 'marketing') {
      if (this.tabChildSelect === 'bigTurntable') {
        this.sysPath = [
          {
            name: this.i18n('大转盘'),
            path: 'pagesSub/big-wheel/BigWheelList'
          }
        ]
      } else if (this.tabChildSelect === 'gatheringPoint') {
        this.sysPath = [
          {
            name: this.i18n('集点活动'),
            path: 'pagesSub/collect-point/CollectPointList'
          }
        ]
      } else if (this.tabChildSelect === 'prizeDraw') {
        this.sysPath = [
          {
            name: this.i18n('抽奖团'),
            path: 'pagesSub/group-booking/GroupBookingList'
          }
        ]
      } else {
        this.sysPath = []
      }
    } else {
      this.sysPath = []
    }
  }

  // 类型tab
  selectTabs = [
    {
      name: this.i18n('页面'),
      value: 'activePage'
    },
    {
      name: this.i18n('券活动'),
      value: 'ticket',
      isOpened: false,
      children: [
        {
          pid: 'ticket',
          name: this.i18n('积分兑换券'),
          value: 'points'
        },
        {
          pid: 'ticket',
          name: this.i18n('小程序领券'),
          value: 'coupon'
        },
        {
          pid: 'ticket',
          name: this.i18n('小程序领微信券'),
          value: 'coupon-wx'
        },
      ]
    },
    {
      name: this.i18n('卡活动'),
      value: 'card'
    },
    {
      name: this.i18n('营销活动'),
      value: 'marketing',
      isOpened: false,
      children: [
        {
          pid: 'marketing',
          name: this.i18n('大转盘'),
          value: 'bigTurntable'
        },
        {
          pid: 'marketing',
          name: this.i18n('集点活动'),
          value: 'gatheringPoint'
        },
        {
          pid: 'marketing',
          name: this.i18n('抽奖团'),
          value: 'prizeDraw'
        },
        {
          pid: 'marketing',
          name: this.i18n('邀请有礼'),
          value: 'inviteCourtesy'
        },
      ]
    },
  ]

  open() {
    this.visible = true
    this.tabSelect = 'activePage';
    this.sysPathLike = '';
    this.setSysPath();
    this.queryTableData(true)
  }

  onTabClick(item: any) {
    if (item.value === this.tabSelect) {
      return
    }

    this.centerType = this.i18n('活动列表')

    if (!item.children) {
      this.tabSelect = item.value
      this.tabChildSelect = null
      this.sysPathLike = ''
      this.setSysPath();
      this.queryTableData(true)
      if (item.value === 'activePage') {
        this.centerType = this.i18n('功能页面')
      }
    } else {
      item.isOpened = !item.isOpened
    }
  }

  onTabChildClick(item: any) {
    this.tabSelect = item.pid;
    this.tabChildSelect = item.value;
    this.centerType = this.i18n('活动列表')
    if (this.showActiveList) {
      // 邀请有礼没有活动列表页
      if (item.value === 'inviteCourtesy') {
        this.centerType = this.i18n('活动详情')
        this.resetFilter()
        this.queryActiveDetailList(true)
      } else {
        this.sysPathLike = ''
        this.setSysPath();
        this.queryTableData(true)
      }
    } else {
      this.resetFilter()
      this.queryActiveDetailList(true)
    }
  }

  queryTableData(resetPage = false) {
    if (resetPage) {
      this.page.size = 10;
      this.page.currentPage = 1;
    }

    const filter = this.sysPath.filter((item) => {
      return !this.sysPathLike ? true : item.name && item.name.indexOf(this.sysPathLike) !== -1
    })

    this.page.total = filter.length;

    this.filterData = filter.filter((item, index) => {
      return (this.page.currentPage - 1) * this.page.size <= index && index < this.page.currentPage * this.page.size
    })
  }

  get cmsChannelEnum() {
    return CmsConfigChannel
  }

  doSpread(row: any, channel: CmsConfigChannel) {
    console.log(row, channel);
    const params = new PopularizeResponse()
    params.channel = channel
    if (channel === CmsConfigChannel.H5) {
      if (this.centerType === this.i18n('活动详情')) {
        params.appletPath = `/pagesSub/big-wheel/BigWheelDtl?activityNumber=${row.activityId}`
      } else {
        // 活动列表
        params.appletPath = `/pagesSub/big-wheel/MyAward`
      }
      this.$refs.pageSpreadDialog.open(row, [params], CmsConfigChannel.H5)
    } else {
      this.loading = true
      ContentTemplateApi.customPopularize(this.getDetailPathUrl(row), channel).then((res) => {
        if (res.code === 2000 && res.data) {
          const obj = Object.assign(params, res.data)
          this.$refs.pageSpreadDialog.open(row, [obj], channel)
        } else {
          throw new Error(res.msg!)
        }
      }).catch((error) => {
        this.$message.error(error.message)
      }).finally(() => {
        this.loading = false
      })
    }
  }

  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    if (this.showActiveList) {
      this.queryTableData()
    } else {
      this.queryActiveDetailList();
    }
  }

  onHandleSizeChange(val: number) {
    this.page.size = val
    if (this.showActiveList) {
      this.queryTableData()
    } else {
      this.queryActiveDetailList();
    }
  }

  changeCenterType(type: string) {
    if (this.centerType === type) {
      return
    }
    this.detailList = []
    this.centerType = type
    if (this.showActiveList) {
      this.queryTableData()
    } else {
      this.resetFilter()
      this.queryActiveDetailList(true);
    }
  }


  debounceQueryActiveDetailList() {
    return debounce(this.debounceQueryActiveDetailListMethod, 500)
  }

  debounceQueryActiveDetailListMethod() {
    this.queryActiveDetailList(true)
  }


  resetFilter() {
    this.filter.state = 'PROCESSING';
    this.filter.keyWords = '';
    this.filter.activeNo = '';
  }


  queryActiveDetailList(resetPage = false) {
    if (resetPage) {
      this.page.currentPage = 1;
      this.page.size = 10;
    }

    // 券活动
    if (this.tabSelect === 'ticket' || this.tabSelect === 'marketing') {
      const filter = new UnionActivityQuery()
      filter.page = this.page.currentPage - 1;
      filter.pageSize = this.page.size;

      filter.stateEquals = this.filter.state
      filter.nameLike = this.filter.keyWords
      // 积分兑换卷
      if (this.tabChildSelect === 'points') {
        filter.typeIn = ["PointsExchangeCouponActivityRule"]
      } else if (this.tabChildSelect === 'coupon-wx') {
        // 小程序领微信券
        filter.typeIn = ["WeiXinAppletIssueCouponActivityRule"]
      } else if (this.tabChildSelect === 'coupon') {
        // 小程序领券
        filter.typeIn = ["MiniProgramGainCouponActivityRule"]
      } else if (this.tabChildSelect === 'bigTurntable') {
        // 大转盘
        filter.typeIn = ["BigWheelActivityRule"]
      } else if (this.tabChildSelect === 'prizeDraw') {
        // 抽奖团
        filter.typeIn = ['GroupBookingActivityRule']
      } else if (this.tabChildSelect === 'inviteCourtesy') {
        // 邀请有礼
        filter.typeIn = ['MemberInviteRegisterGiftActivityRule']
      }

      if (this.filter.activeNo) {
        filter.numberEquals = this.filter.activeNo
      }

      if (this.tabChildSelect === 'gatheringPoint') {
        // 集点活动
        const pointsActivityFilter = new PointsActivityFilter()
        pointsActivityFilter.groupType = "CONSUME_GIFT" as unknown as ActivityGroupType
        pointsActivityFilter.typeEquals = "COLLECT_POINTS_ACTIVITY"
        pointsActivityFilter.page = this.page.currentPage - 1;
        pointsActivityFilter.pageSize = this.page.size;

        pointsActivityFilter.stateEquals = this.filter.state;
        pointsActivityFilter.nameLike = this.filter.keyWords;

        if (this.filter.activeNo) {
          pointsActivityFilter.numberEquals = this.filter.activeNo
        }
        this.detailListLoading = true
        PointsActivityApi.query(pointsActivityFilter).then((response) => {
          if (response && response.data && response.data.list) {
            this.detailList = response.data.list
          } else {
            this.detailList = []
          }
          this.page.total = response.total
        }).finally(() => {
          this.detailListLoading = false
        })

      } else {
        this.detailListLoading = true
        UnionActivityApi.queryByType(filter).then((response) => {
          if (response.data && response.data.list) {
            this.detailList = response.data.list
          } else {
            this.detailList = []
          }
          this.page.total = response.total
        }).finally(() => {
          this.detailListLoading = false
        })
      }
    } else if (this.tabSelect === 'card') {
      // 电子卡制卡活动
      const filter = new GiftCardActivityFilter();
      filter.page = this.page.currentPage - 1;
      filter.pageSize = this.page.size;
      filter.stateEquals = this.filter.state
      filter.nameLikes = this.filter.keyWords
      if (this.filter.activeNo) {
        filter.activityIdLikes = this.filter.activeNo
      }
      this.detailListLoading = true
      GiftCardActivityApi.query(filter).then((response) => {
        this.page.total = response.data?.total!
        if (response.data && response.data.result) {
          this.detailList = response.data.result.map((item) => {
            return item.body
          })
        } else {
          this.detailList = []
        }
      }).finally(() => {
        this.detailListLoading = false
      })
    }
  }
};