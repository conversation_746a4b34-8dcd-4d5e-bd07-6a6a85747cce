/*
 * @Author: 黎钰龙
 * @Date: 2024-08-12 16:39:33
 * @LastEditTime: 2024-08-13 18:12:43
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\card\writeCard\WriteCardBillLine.ts
 * 记得注释
 */
import { CardMedium } from 'model/default/CardMedium'
import { WriteCardAction } from './WriteCardAction'
import { CardState } from 'model/default/CardState'

export default class WriteCardBillLine {
  // 单号
  billNumber: Nullable<string> = null
  // 卡号
  cardCode: Nullable<string> = null
  // 卡模板号
  cardTemplateNumber: Nullable<string> = null
  // 卡模板名称
  cardTemplateName: Nullable<string> = null
  // 卡介质
  cardMedium: Nullable<CardMedium> = null
  // 备注
  remark: Nullable<string> = null
  // 卡状态
  cardState: Nullable<CardState> = null
  // 操作内容
  action: Nullable<WriteCardAction> = null
  // 操作人
  operator: Nullable<string> = null
  // 发生时间
  occurredTime: Nullable<Date> = null
}