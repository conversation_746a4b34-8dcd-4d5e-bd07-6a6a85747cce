import { Component, Inject, Prop, Vue, Watch } from "vue-property-decorator";
import ImportDialog from "cmp/importdialog/ImportDialog.vue";
import IdName from "model/entity/IdName";
import StoreSelectorDialog from "cmp/selectordialogs/StoreSelectorDialog.vue";
import RSOrg from "model/common/RSOrg";
import ImportResultDialog from "pages/deposit/mbrdeposit/active/dialog/ImportResultDialog.vue";
import PromotionCenterSelectorDialog from "cmp/selectordialogs/PromotionCenterSelectorDialog.vue";
import StoreRange from "model/common/StoreRange";
import BrowserMgr from "mgr/BrowserMgr";
import RSMarketingCenter from "model/common/RSMarketingCenter";
import StoreMulPromotionSelectorDialog from "cmp/selectordialogs/StoreMulPromotionSelectorDialog.vue";
import LimitedMarketingCenter from "model/common/LimitedMarketingCenter";
import NoMarketCenter from "./NoMarketCenter";
import Zone from "model/datum/zone/Zone";
import LimitedZones from "model/common/LimitedZones";
import MarketCenterSelectorDialog from "cmp/selectordialogs/MarketCenterSelectorDialog";
import ActiveStoreAreaForMarket from "./ActiveStoreAreaForMarket";
import I18nPage from "common/I18nDecorator";

class CustomStore {
	// 控制营销中心展示
	promotionCenter: boolean;
	storeRange: StoreRange;
}

class ExportResult {
	importResult: boolean;
	backUrl: string;
	errorCount: number;
	ignoreCount: number;
	successCount: number;
}

class AreaStoreRange extends Zone {
	store: Nullable<StoreRange> = null
}

export { ExportResult };

@Component({
	name: "ActiveStoreMarket",
	components: {
		ImportDialog,
		StoreSelectorDialog,
		ImportResultDialog,
		PromotionCenterSelectorDialog,
		StoreMulPromotionSelectorDialog,
		NoMarketCenter,
		MarketCenterSelectorDialog,
		ActiveStoreAreaForMarket
	},
})
@I18nPage({
  prefix: [
    "/公用/券模板",
    '/公用/门店组件',
    '/权益/积分/积分初始化/未初始化状态/得积分规则/基础得积分/点击立即设置'
  ],
  auto: true
})
export default class ActiveStoreMarket extends Vue {
	promotionCenter = false; // 是否开启营销中心
	$refs: any;
	store: StoreRange = new StoreRange();
	currentMarketCenter: LimitedMarketingCenter = new LimitedMarketingCenter();
	importDialogShow = false;
	importUrl = "v1/org/importExcel";
	@Prop()
	showTip: boolean;
	// 是否展示与活动门店一致
	@Prop({
		type: Boolean,
		default: false,
	})
	sameStore: boolean;
	reloadList = false;
	inputValueRules: any;
	marketCenterRules: any;

	comeValue: any = {};

	@Inject({
		from: 'showAll',
		default: false
	})
	showAll: Boolean

	@Prop({
		type: Boolean,
		default: true,
	})
	internalValidate: boolean;
	@Prop()
	value: CustomStore;
	importResult: ExportResult = new ExportResult();
	importResultDialogShow = false;
	headquarters: Nullable<string> = null
	areaList: AreaStoreRange[] = []
	// storeRangeType: any[] = []
	// isZone: any[] = []

	@Watch("value", { deep: true, immediate: true })
	onValueChange(value: StoreRange) {
		this.comeValue = value;
		let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
		if (sysConfig) {
			this.promotionCenter = sysConfig.enableMultiMarketingCenter;
		}
		if (this.promotionCenter) {
			if (!value.storeRangeType) {
				this.store = new StoreRange();
				this.store.storeRangeType = "PART";
			} else {
				this.store = JSON.parse(JSON.stringify(value));
				this.doSetIdName();
				this.doBindValue()
			}
			// this.setMarketCenter();
		}
	}
	get marketCenterId() {
		if (this.currentMarketCenter && this.currentMarketCenter.marketingCenter) {
			return this.currentMarketCenter.marketingCenter.id;
		}
		return null;
	}
	get marketCenterName() {
		if (this.currentMarketCenter && this.currentMarketCenter.marketingCenter) {
			return this.currentMarketCenter.marketingCenter.name;
		}
		return null;
	}
	get templatePath() {
		if (location.href.indexOf("localhost") === -1) {
			return "template_specify_stores.xlsx";
		} else {
			return "template_specify_stores.xlsx";
		}
	}
	created() {
		if (this.sameStore) {
			this.store.storeRangeType = "SAME";
		}
		let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
		if (sysConfig) {
			this.promotionCenter = sysConfig.enableMultiMarketingCenter;
		}
		this.headquarters = sessionStorage.getItem('headquarters')
		this.inputValueRules = [
			{
				validator: (rule: any, value: string, callback: any) => {
					if (this.store.storeRangeType !== "ALL" && this.store.marketingCenters[0].stores?.stores?.length === 0) {
						callback(new Error(this.formatI18n("/营销/积分活动/积分活动/商品满额加送积分/表单校验/提示信息/请选择门店范围")));
					}
					callback();
				},
				trigger: ["change", "blur"],
			},
		];
		this.marketCenterRules = [
			{
				validator: (rule: any, value: string, callback: any) => {
					if (this.promotionCenter) {
						if (!this.store.marketingCenters || this.store.marketingCenters.length === 0) {
							callback(new Error(this.formatI18n("/营销/积分活动/积分活动/商品满额加送积分/表单校验/提示信息/请选择营销中心范围")));
							return;
						}
					}
					callback();
				},
				trigger: ["change", "blur"],
			},
		];
	}

	doBindValue() {
		this.store.marketingCenters.forEach((item: LimitedMarketingCenter, index: number) =>{
			if (item.stores!.storeRangeType === 'ALL') {
				item.stores!.storeRangeTypeFront = 'ALL'
				item.stores!.isZone = false
			} else if (item.stores!.storeRangeType === 'PART') {
				if (item.stores!.zones.length > 0 || item.stores!.isZone === true) {
					item.stores!.storeRangeTypeFront = 'ZONE'
					item.stores!.isZone = true
				} else {
					item.stores!.storeRangeTypeFront = 'PART'
					item.stores!.isZone = false
				}
			} else if(item.stores!.storeRangeType === 'EXCLUDE') {
				item.stores!.storeRangeTypeFront = 'EXCLUDE'
				item.stores!.isZone = false
			}
		})
	}

	validate() {
		if (this.promotionCenter) {
			return this.$refs.form.validate();
		} else {
			return this.$refs.noMarket.validate();
		}
	}

	doClearStore(index: number) {
		let item = this.store.marketingCenters[index]
		if (item.stores) {
			item.stores.stores = [];
			item.storesValue = "";
			this.doCommitData()
		}
		this.$forceUpdate()
	}

	deleteZone(index: number) {
    this.$confirm(this.formatI18n('/公用/活动/提示信息/确认要删除吗？'), this.formatI18n('/公用/活动/提示信息/提示'), {
      confirmButtonText: this.formatI18n('/权益/券/券模板/确定'),
      cancelButtonText: this.formatI18n('/权益/券/券模板/取消'),
		}).then(() => {
			this.store.marketingCenters.splice(index, 1)
			this.doCommitData()
		})

	}

	doImport() {
		this.importDialogShow = true;
	}
	doSelect(index: number) {
		let item = this.store.marketingCenters[index]
		let goodsData: RSOrg[] = [];
		if (item.stores && item.stores.stores.length > 0) {
			item.stores.stores.forEach((item: IdName) => {
				let obj: RSOrg = new RSOrg();
				obj.org = new IdName();
				obj.org.id = item.id;
				obj.org.name = item.name;
				goodsData.push(obj);
			});
		}
		this.$refs.selectGoodsScopeDialog[index].open(goodsData, "multiple");
	}
	openPromotionCenterDialog() {
		// todo 这里缺少  营销中心类型  需要调整
		this.$refs.selectPromotionCenterSelectorDialog.open(this.store.marketingCenters, "multiple");
	}
	openMarketCenter() {
		this.$refs.marketCenterSelectorDialog.open(this.store.marketingCenters, 'multiple')
	}
	doPromotionSubmitGoods(arr: RSMarketingCenter[]) {
		// todo 这里缺少  营销中心类型  需要调整
		let marketingCentersMap: any = {};
		for (let item of this.store.marketingCenters) {
			if (item!.marketingCenter!.id) {
				marketingCentersMap[item!.marketingCenter!.id] = item;
			}
		}
		let result = [];
		for (let mc of arr) {
			let id = mc!.marketingCenter!.id;
			if (!id) {
				continue;
			}
			if (marketingCentersMap[id]) {
				// 没选就添加
				result.push(marketingCentersMap[id]);
			} else {
				let center = new LimitedMarketingCenter();
				center.stores = new StoreRange();
				center.marketingCenter = mc.marketingCenter;
				result.push(center);
			}
		}
		this.store.marketingCenters = result;
		this.reloadList = true;
		this.$nextTick(() => {
			this.doEmitInput();
		});
		setTimeout(() => (this.reloadList = false), 10);
	}
	doSubmitGoods(arr: RSOrg[], index: number) {
		let item = this.store.marketingCenters[index]
		let stores: IdName[] = [];
		let str = "";
		if (arr && arr.length > 0) {
			arr.forEach((item: any) => {
				if (item && item.org && item.org.id) {
					str += item.org.id + `[${item.org.name}];`;
					let obj: IdName = new IdName();
					obj.id = item.org.id;
					obj.name = item.org.name;
					stores.push(obj);
				}
				if (item && item.id) {
					str += item.id + `[${item.name}];`;
					let obj: IdName = new IdName();
					obj.id = item.id;
					obj.name = item.name;
					stores.push(obj);
				}
			});
		}
		
		if (item.stores) {
			item.stores.stores = stores;
			item.storesValue = str;
		}
		this.$nextTick(() => {
			this.doEmitInput();
		});
		this.$refs.form.validate();
		this.doCommitData()
	}
	doSubmitMarketCenters(arr: LimitedMarketingCenter[]) {
		// todo 在类里添加前端使用参数代替storeRangeType
		let result: LimitedMarketingCenter[] = []
		arr.forEach((item: LimitedMarketingCenter) => {
			if (!item.stores) {
				item.stores = new StoreRange()
				const mc: LimitedMarketingCenter = new LimitedMarketingCenter();
				mc.stores = new StoreRange();
				mc.stores.storeRangeType = "ALL";
				mc.stores.storeRangeTypeFront = 'ALL'
				mc.stores.isZone = false
				mc.stores.storeRangeLimitType = "MARKETING_CENTER";
				mc.marketingCenter = {
					id: item.marketingCenter!.id,
					name: item.marketingCenter!.name,
				};
				
				result.push(mc);
				this.doEmitInput();
			} else {
				result.push(item)
			}
		})
		this.store.marketingCenters = result
		this.doCommitData()
	}
	doStoreRange(index: number) {
		let item = this.store.marketingCenters[index]
		item.storesValue = ''
		if (item.stores) {
			if (this.store.marketingCenters[index].stores!.storeRangeTypeFront === "ALL") {
				item.stores = new StoreRange();
				item.stores.storeRangeType = "ALL";
				item.stores.isZone = false
				this.doEmitInput();
			} else if (this.store.marketingCenters[index].stores!.storeRangeTypeFront === "PART") {
				item.stores = new StoreRange();
				item.stores.storeRangeType = "PART";
				item.stores.isZone = false
				this.doEmitInput();
			} else if (this.store.marketingCenters[index].stores!.storeRangeTypeFront === "EXCLUDE") {
				item.stores = new StoreRange();
				item.stores.storeRangeType = "EXCLUDE";
				item.stores.isZone = false
				this.doEmitInput();
			} else if (this.store.marketingCenters[index].stores!.storeRangeTypeFront === "ZONE") {
				item.stores = new StoreRange();
				item.stores.storeRangeType = "PART";
				item.stores.isZone = true
				this.doEmitInput();
			}
			this.$refs.form.validate();
			this.doCommitData()
		}
	}
	doPromCenterStoreRange(marketCenter: LimitedMarketingCenter) {
		let storeRangeType = marketCenter!.stores!.storeRangeType;
		marketCenter.storesValue = "";
		marketCenter.stores = new StoreRange();
		marketCenter.stores.storeRangeType = storeRangeType;
		marketCenter.stores.stores = [];
		this.doEmitInput();
		this.$refs.form.validate();
	}
	getStoreCount(count: number) {
		let str: any = this.formatI18n("/公用/券模板", "已选择{0}家门店");
		str = str.replace(/\{0\}/g, count);
		return str;
	}
	private doSetIdName() {
		if (!this.store.marketingCenters) {
			return;
		}
		for (let marketingCenter of this.store.marketingCenters) {
			let arr = marketingCenter!.stores!.stores;
			let str = "";
			if (arr && arr.length > 0) {
				if (arr && arr.length > 0) {
					arr.forEach((item: any) => {
						if (item && item.org && item.org.id) {
							str += item.org.id + `[${item.org.name}];`;
						}
						if (item && item.id) {
							str += item.id + `[${item.name}];`;
						}
					});
				}
			}
			marketingCenter.storesValue = str;
		}
	}
	private doEmitInput() {
		this.doSetStoreRangeLimitType();
	}
	private doSetStoreRangeLimitType() {
		// if (this.store.marketingCenters && this.store.marketingCenters.length > 0) {
			// for (let item of this.store.marketingCenters) {
				// item.stores!.storeRangeLimitType = "STORE";
			// }
		// }
		this.store.storeRangeLimitType = "MARKETING_CENTER";
	}

	private parentChange() {
		this.$emit("change");
	}

	commitNoMC(val: any) {
		this.$emit('input', val)
	}

  openZone(index: any) {
    this.$refs.activeStoreAreaForMarket[index].openZone()
  }

	doCommitData() {
		this.$emit('change', this.store)
	}

	getAreaData(data: StoreRange, index: number) {
		this.store.marketingCenters[index].stores = data
    this.$emit('change', this.store)
	}

	doValidate() {
		if (this.store.marketingCenters.length === 0) {
			this.$message.warning(this.formatI18n('/公用/门店组件/请至少选择一个营销中心'))
			return Promise.reject()
		}
		let all = []
		for (let index = 0; index < this.store.marketingCenters.length; index++) {
			const item = this.store.marketingCenters[index];
			if ((item.stores!.storeRangeTypeFront === 'PART' || item.stores!.storeRangeTypeFront === 'EXCLUDE') && (item.stores!.stores === null || item.stores!.stores.length === 0)) {
				this.$message.warning(this.formatI18n('/储值/会员储值/门店储值管理/请至少选择一个门店'))
				return Promise.reject()
			}
		}
		if (this.$refs.activeStoreAreaForMarket) {
			for (let index = 0; index < this.$refs.activeStoreAreaForMarket.length; index++) {
        if (this.store.marketingCenters[index].stores?.isZone) {
          const item = this.$refs.activeStoreAreaForMarket[index];
          return item.doValidate()
        }
			}
		}
	}
}
