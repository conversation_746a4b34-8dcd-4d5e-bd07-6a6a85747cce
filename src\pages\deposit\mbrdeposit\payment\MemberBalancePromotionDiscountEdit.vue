<template>
	<div class="member-balance-promotion-edit">
		<BreadCrume :panelArray="panelArray">
			<template slot="operate">
				<el-button v-if="permission.discountEditable" @click="doSave(null)" type="primary">保存</el-button>
				<el-button v-if="!disabled && permission.discountAuditable && !isOaActivity" @click="doSaveAudit" type="primary">保存并审核</el-button>
				<el-button @click="doCancel">取消</el-button>
			</template>
		</BreadCrume>
		<div class="current-page">
			<el-form :model="form.data" :rules="form.rules" ref="form" label-width="160px">
				<div class="panel">
					<div class="header">
						活动信息
					</div>
					<div class="content">
						<el-form-item label="活动名称" :required="true" prop="name">
							<el-input :disabled="disabled" maxlength="80" placeholder="请输入不超过80个字符" style="width: 350px" v-model="form.data.name"></el-input>
						</el-form-item>
						<el-form-item label="所属主题" prop="topicCode">
							<el-select :disabled="disabled" clearable placeholder="请选择" style="width: 350px" v-model="form.data.topicCode">
								<el-option no-i18n :label="item.name" :value="item.code" v-for="(item, index) in themes" :key="index">{{ item.name }} </el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="活动时间" :required="true">
							<ActivityDateTimeConditionPicker v-model="form.data.activityDateTimeCondition" ref="activityDateTimeConditionPicker">
                  			</ActivityDateTimeConditionPicker>
							<!-- <el-date-picker :disabled="disabled"
								type="daterange"
								range-separator="-"
								:picker-options="dateRangeOption"
								v-model="form.data.timeRange"
								start-placeholder="开始时间"
								end-placeholder="结束时间"
							>
							</el-date-picker> -->
						</el-form-item>
						<el-form-item label="活动门店" :required="true">
							<ActiveStore no-i18n ref="storeScope" :sameStore="false" v-model="form.data.stores" />
						</el-form-item>
            <el-form-item :label="formatI18n('/储值/预付卡/电子礼品卡活动/编辑页面', '活动说明')">
              <el-input :disabled="disabled" v-model="form.data.remark" type="textarea" :placeholder="formatI18n('/储值/会员储值/储值充值活动/编辑页面','请输入不超过500个字')" :rows="10" maxlength="500" style="width: 500px;" />
            </el-form-item>
					</div>
				</div>

				<div class="split"></div>

				<div class="panel">
					<div class="header">
						支付优惠规则
					</div>
					<div class="content">
						<el-form-item label="适用商品" :required="true">
							<GoodsScopeEx :disabled="disabled" no-i18n v-model="form.data.goods" :goodsMatchRuleMode="goodsMatchRuleMode" ref="goodsScope" />
						</el-form-item>
						<!-- <el-form-item label="优惠计算" :required="true" prop="strategy">
              <el-radio-group v-model="form.data.strategy" @change="changeStrategy">
                <el-radio label="BY_AMOUNT">{{i18n('按商品金额')}}</el-radio>
                <el-radio label="BY_QTY">{{i18n('按每单品数量')}}</el-radio>
              </el-radio-group>
            </el-form-item> -->
						<el-form-item label="优惠门槛" :required="true">
							<el-row style="display:flex;flex-direction:column">
								<el-radio :disabled="disabled" v-model="form.data.favThresholdLimit" :label="false" @change="changeFavThresholdLimit">{{ i18n("不限制") }}</el-radio>
								<el-radio :disabled="disabled" v-model="form.data.favThresholdLimit" :label="true" @change="changeFavThresholdLimit" style="display:flex;align-items:center">
                <i18n advance k="/储值/会员储值/储值支付活动/编辑页面/适用商品消费满{0}元及以上" style="display:flex;align-items:center">
									<template slot-scope="{ items }">
										<div class="text">{{ items.s0.prefix }}&nbsp;&nbsp;</div>
										<el-form-item prop="favThreshold">
											<AutoFixInput
												:disabled="disabled || !form.data.favThresholdLimit"
												v-model="form.data.favThreshold"
												style="width: 150px"
												:min="0.01"
												:max="99999999.0"
												:fixed="2"
											></AutoFixInput>
										</el-form-item>
										<div class="text">&nbsp;&nbsp;{{ items.s0.suffix }}</div>
									</template>
								</i18n>
                </el-radio>
							</el-row>
						</el-form-item>
						<el-form-item label="优惠规则设置">
							<el-radio-group :disabled="disabled" v-model="form.data.favRule" v-if="gradeList.length > 0">
								<el-radio label="gradeSame">{{ i18n("不同等级会员适用 相同规则") }}</el-radio>
								<el-radio label="gradeDiff">{{ i18n("不同等级会员适用 不同规则") }}</el-radio>
							</el-radio-group>
							<!-- 单个 不同等级相同规则 -->
							<el-row v-if="form.data.favType === 'step' && form.data.favRule === 'gradeSame'">
								<div style="display: inline-flex;">
									<i18n
										advance
										:k="
											`/储值/会员储值/储值支付活动/编辑页面/${
												form.data.strategy === 'BY_AMOUNT' ? '适用商品消费满{0}元，打{1}折' : '适用商品消费满{0}件，打{1}折'
											}`
										"
									>
										<template slot-scope="{ items }">
											<div style="float: left">{{ items.s0.prefix }}&nbsp;</div>
											<el-form-item
												:rules="form.data.strategy === 'BY_AMOUNT' ? form.amountRules : form.qtyRules"
												prop="gradeSameStepValue.threshold"
												style="float: left;height: 40px;"
											>
												<AutoFixInput :disabled="disabled"
													style="width: 80px"
													size="small"
													@change="refresh"
													v-if="form.data.strategy === 'BY_AMOUNT'"
													:min="0.01"
													:max="999999"
													:fixed="2"
													v-model="form.data.gradeSameStepValue.threshold"
												/>
												<AutoFixInput :disabled="disabled"
													style="width: 80px"
													size="small"
													@change="refresh"
													v-if="form.data.strategy === 'BY_QTY'"
													:min="1"
													:max="999999"
													:fixed="0"
													v-model="form.data.gradeSameStepValue.threshold"
												/>
											</el-form-item>
											<div style="float: left">&nbsp;{{ items.s0.suffix }}&nbsp;</div>
											<el-form-item :rules="form.discountRules" prop="gradeSameStepValue.value" style="float: left;height: 40px;">
												<AutoFixInput :disabled="disabled"
													v-model="form.data.gradeSameStepValue.value"
													size="small"
													:min="0.1"
													:max="9.9"
													:fixed="1"
													style="width: 80px"
												></AutoFixInput>
											</el-form-item>
											<div style="float: left">&nbsp;{{ items.s1.suffix }}</div>
										</template>
									</i18n>
								</div>
							</el-row>

							<!-- 阶梯 不同等级相同规则 -->
							<el-row class="rule-table" v-if="form.data.favType === 'stairs' && form.data.favRule === 'gradeSame'">
								<el-form :model="form.data.gradeSameStepValue" ref="stepValuesForm">
									<el-table :data="form.data.gradeSameStepValue.stepValues">
										<el-table-column :label="i18n('阶梯')" type="index"></el-table-column>
										<el-table-column :label="i18n('优惠条件')">
											<template slot-scope="scope">
												<div style="display: flex;align-items: center;min-height: 80px">
													<div>{{i18n('适用商品消费满')}}</div>
													<el-form-item
														:rules="form.data.strategy === 'BY_AMOUNT' ? form.amountRules : form.qtyRules"
														:prop="'stepValues.' + scope.$index + '.threshold'"
														style="height: 40px;margin: 0 8px"
													>
														<AutoFixInput :disabled="disabled"
															style="width: 80px"
															size="small"
															@change="refresh"
															v-if="form.data.strategy === 'BY_AMOUNT'"
															:min="0.01"
															:max="999999"
															:fixed="2"
															v-model="scope.row.threshold"
														/>
														<AutoFixInput :disabled="disabled"
															style="width: 80px"
															size="small"
															@change="refresh"
															v-if="form.data.strategy === 'BY_QTY'"
															:min="1"
															:max="999999"
															:fixed="0"
															v-model="scope.row.threshold"
														/>
													</el-form-item>
													<div>{{i18n('元')}}</div>
												</div>
											</template>
										</el-table-column>
										<el-table-column :label="i18n('折扣')">
											<template slot-scope="scope">
												<div style="display: flex;align-items: center;min-height: 80px">
													<el-form-item
														:rules="form.discountRules"
														:prop="'stepValues.' + scope.$index + '.value'"
														style="height: 40px;margin: 0 8px"
													>
														<AutoFixInput :disabled="disabled" v-model="scope.row.value" size="small" :min="0.1" :max="9.9" :fixed="1" style="width: 80px"></AutoFixInput>
													</el-form-item>
													<div>折</div>
												</div>
											</template>
										</el-table-column>
										<el-table-column label="操作">
											<template slot-scope="scope">
												<el-button :disabled="disabled" type="error" @click="removeGradeSameItem(scope.$index)">删除</el-button>
											</template>
										</el-table-column>
									</el-table>
								</el-form>
								<div style="display: flex;margin-top: 20px">
									<el-button :disabled="disabled" type="primary" @click="addGradeSameItem">添加折扣</el-button>
									<div style="margin-left: 8px">
										至少有一个阶梯；当订单满足多个阶梯时，按折扣力度最大的折扣优惠
									</div>
								</div>
							</el-row>
							<!-- 单个 不同等级不同规则 -->
							<el-row v-if="form.data.favType === 'step' && form.data.favRule === 'gradeDiff' && gradeList.length > 0" class="rule-table">
								<el-row class="rule-table-header">
									<el-col :span="4">会员等级</el-col>
									<el-col :span="17">储值支付优惠规则</el-col>
									<el-col :span="3"></el-col>
								</el-row>
								<el-row class="rule-table-line" v-for="(stepValue, index) of form.data.gradeDifferentStepValue" :key="index">
									<el-col :span="4" no-i18n>{{ "[" + stepValue.grade + "] " + stepValue.gradeName }}</el-col>
									<el-col :span="17">
										<el-checkbox :disabled="disabled" v-model="stepValue.checked" @change="$forceUpdate()">{{ i18n("可参与储值支付优惠") }}</el-checkbox>
										<br />
										<div v-if="stepValue.checked">
											<i18n
												advance
												:k="
													`/储值/会员储值/储值支付活动/编辑页面/${
														form.data.strategy === 'BY_AMOUNT' ? '适用商品消费满{0}元，打{1}折' : '适用商品消费满{0}件，打{1}折'
													}`
												"
											>
												<template slot-scope="{ items }">
													<div style="float: left">{{ items.s0.prefix }}&nbsp;</div>
													<el-form-item
														:rules="form.data.strategy === 'BY_AMOUNT' ? form.amountRules : form.qtyRules"
														:prop="`gradeDifferentStepValue[${index}].threshold`"
														style="float: left;height: 40px;"
													>
														<AutoFixInput :disabled="disabled"
															style="width: 80px"
															size="small"
															@change="refresh"
															v-if="form.data.strategy === 'BY_AMOUNT'"
															:min="0.01"
															:max="999999"
															:fixed="2"
															v-model="stepValue.threshold"
														/>
														<AutoFixInput :disabled="disabled"
															style="width: 80px"
															size="small"
															@change="refresh"
															v-if="form.data.strategy === 'BY_QTY'"
															:min="1"
															:max="999999"
															:fixed="0"
															v-model="stepValue.threshold"
														/>
													</el-form-item>
													<div style="float: left">&nbsp;{{ items.s0.suffix }}&nbsp;</div>
													<el-form-item :rules="form.discountRules" style="float: left" :prop="`gradeDifferentStepValue[${index}].value`">
														<AutoFixInput :disabled="disabled"
															style="width: 80px"
															size="small"
															@change="refresh"
															:min="0.1"
															:max="9.9"
															:fixed="1"
															v-model="stepValue.value"
														></AutoFixInput>
													</el-form-item>
													<div style="float: left">&nbsp;{{ items.s1.suffix }}</div>
												</template>
											</i18n>
										</div>
									</el-col>
									<el-col :span="3" v-if="stepValue.checked">
										<a v-show="!disabled" @click="doCopy(stepValue.grade, index)"
											><i class="el-icon-document-copy"></i>&nbsp;
											<span>复制给</span>
										</a>
									</el-col>
								</el-row>
								<el-form-item label="" prop="gradeDifferentStepValue"></el-form-item>
							</el-row>
							<!-- 阶梯 不同等级不同规则 -->
							<el-row v-if="form.data.favType === 'stairs' && form.data.favRule === 'gradeDiff' && gradeList.length > 0" class="rule-table">
								<el-row class="rule-table-header">
									<el-col :span="4">会员等级</el-col>
									<el-col :span="17">储值支付优惠规则</el-col>
									<el-col :span="3"></el-col>
								</el-row>
								<el-row class="rule-table-line" v-for="(stepValue, index) of form.data.gradeDifferentStepValue" :key="index">
									<el-col :span="4" no-i18n>{{ "[" + stepValue.grade + "] " + stepValue.gradeName }}</el-col>
									<el-col :span="17">
										<el-checkbox :disabled="disabled" v-model="stepValue.checked" @change="$forceUpdate()">{{ i18n("可参与储值支付优惠") }}</el-checkbox>
										<br />
										<div v-if="stepValue.checked">
											<el-form :model="stepValue" :ref="'stepValuesForm' + index">
												<el-table :data="stepValue.stepValues">
													<el-table-column label="阶梯" type="index"></el-table-column>
													<el-table-column label="优惠条件">
														<template slot-scope="scope">
															<div style="display: flex;align-items: center;min-height: 80px">
																<div>适用商品消费满</div>
																<el-form-item
																	:rules="form.data.strategy === 'BY_AMOUNT' ? form.amountRules : form.qtyRules"
																	:prop="'stepValues.' + scope.$index + '.threshold'"
																	style="height: 40px;margin: 0 8px"
																>
																	<AutoFixInput :disabled="disabled"
																		style="width: 80px"
																		size="small"
																		@change="refresh"
																		v-if="form.data.strategy === 'BY_AMOUNT'"
																		:min="0.01"
																		:max="999999"
																		:fixed="2"
																		v-model="scope.row.threshold"
																	/>
																	<AutoFixInput :disabled="disabled"
																		style="width: 80px"
																		size="small"
																		@change="refresh"
																		v-if="form.data.strategy === 'BY_QTY'"
																		:min="1"
																		:max="999999"
																		:fixed="0"
																		v-model="scope.row.threshold"
																	/>
																</el-form-item>
																<div>元</div>
															</div>
														</template>
													</el-table-column>
													<el-table-column label="折扣">
														<template slot-scope="scope">
															<div style="display: flex;align-items: center;min-height: 80px;">
																<el-form-item
																	:rules="form.discountRules"
																	:prop="'stepValues.' + scope.$index + '.value'"
																	style="height: 40px;margin: 0 8px"
																>
																	<AutoFixInput :disabled="disabled"
																		v-model="scope.row.value"
																		size="small"
																		:min="0.1"
																		:max="9.9"
																		:fixed="1"
																		style="width: 80px"
																	></AutoFixInput>
																</el-form-item>
																<div>折</div>
															</div>
														</template>
													</el-table-column>
													<el-table-column label="操作">
														<template slot-scope="scope">
															<el-button :disabled="disabled" type="error" @click="removeGradeDifferItem(index, scope.$index)">删除</el-button>
														</template>
													</el-table-column>
												</el-table>
											</el-form>
											<div style="display: flex;margin-top: 20px">
												<el-button :disabled="disabled" type="primary" @click="addGradeDifferItem(index)">添加折扣</el-button>
												<div style="margin-left: 8px">
													至少有一个阶梯；当订单满足多个阶梯时，按折扣力度最大的折扣优惠
												</div>
											</div>
										</div>
									</el-col>
									<el-col :span="3" v-if="stepValue.checked">
										<a v-show="!disabled" @click="doCopy(stepValue.grade, index)"
											><i class="el-icon-document-copy"></i>&nbsp;
											<span>复制给</span>
										</a>
									</el-col>
								</el-row>
								<el-form-item label="" prop="gradeDifferentStepValue"></el-form-item>
							</el-row>
						</el-form-item>
						<el-form-item label="叠加促销">
							<el-radio-group :disabled="disabled" v-model="form.data.excludePromotion">
								<el-radio :label="true">{{ i18n("是") }}</el-radio>
								<el-radio :label="false">{{ i18n("否") }}</el-radio>
							</el-radio-group>
						</el-form-item>
						 <el-form-item label="是否需要整单储值支付">
							<el-radio-group :disabled="disabled"  v-model="form.data.fullBalancePay">
								<el-radio :label="true">{{i18n('是')}}</el-radio>
								<el-radio :label="false">{{i18n('否')}}</el-radio>
							</el-radio-group>
							</el-form-item>
					</div>
				</div>

        <div class="split"></div>

        <!-- 营销预算 -->
        <MarketingBudgetEdit :disabled="disabled" v-model="budget" ref="marketingBudget" activityType="MemberBalanceDiscountActivityRule">
        </MarketingBudgetEdit>
			</el-form>
		</div>
		<CopyStepValue no-i18n ref="copyStepValue" @confirm="copyStepValue"></CopyStepValue>
	</div>
</template>

<script lang="ts" src="./MemberBalancePromotionDiscountEdit.ts"></script>

<style lang="scss">
.el-form-item__error {
	white-space: nowrap !important;
}
.member-balance-promotion-edit {
	background-color: white;
	height: 100%;
	width: 100%;
	overflow: auto;

	.current-page {
		height: calc(100% - 80px) !important;
		overflow: auto;

		.panel {
			.header {
				font-weight: 500;
				padding: 20px 20px 0 20px;
				font-size: 18px;
			}

			.content {
				padding: 20px;
			}
		}

		.split {
			height: 20px;
			background-color: #eeeff1;
		}

		.el-range__icon {
			line-height: 26px;
		}

		.el-range-separator {
			line-height: 26px;
		}

		.el-range__close-icon {
			line-height: 26px;
		}

		.rule-table {
			width: 70%;

			.rule-table-header {
				padding: 0 10px 0 10px;
				background-color: #e6e6e6;
				border: 1px solid #e6e6e6;
			}

			.rule-table-line {
				padding: 10px 10px 20px 10px;
				border: 1px solid #e6e6e6;
			}
		}

		.favor-row {
			display: flex;
			align-items: center;
			font-size: 12px;
			.el-radio {
				margin-right: 0 !important;
			}
			.el-radio__label {
				padding-left: 8px !important;
			}
		}

		.float-left-i18n-line {
			.text {
				height: 41px;
				float: left;
			}

			.el-form-item {
				float: left;
			}
		}
	}
}
</style>
