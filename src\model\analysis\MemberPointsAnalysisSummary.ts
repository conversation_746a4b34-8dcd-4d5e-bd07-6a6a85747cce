/*
 * @Author: haiding <EMAIL>
 * @Date: 2025-01-06 17:12:01
 * @LastEditors: haiding <EMAIL>
 * @LastEditTime: 2025-01-07 16:49:39
 * @FilePath: \start\src\model\analysis\MemberPointsAnalysisSummary.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// 会员分析报表概览
export default class MemberPointsAnalysisSummary {
  // 发放积分
  occurredPoints: Nullable<number> = null
  // 消耗积分
  consumePoints: Nullable<number> = null
  // 调整增加积分
  adjustAddPoints: Nullable<number> = null
  // 调整减少积分
  adjustSubPoints: Nullable<number> = null
  // 过期积分
  expiredPoints: Nullable<number> = null
  // 可用积分（截止查询日期结束日期）
  usablePoints: Nullable<number> = null
  // 可用积分
  sumUsablePoints: Nullable<number> = null
  // 临期积分
  sumLastExpiredPoints: Nullable<number> = null
}