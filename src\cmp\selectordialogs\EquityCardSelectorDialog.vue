<template>
  <el-dialog :title="i18n('选择权益卡')" class="select-equity-card-dialog" :close-on-click-modal="false" :visible.sync="dialogShow">
    <div class="wrap">
      <el-row>
        <el-form :label-width="labelWidth">
          <el-row>
            <el-col :span="8">
              <el-form-item :label="i18n('卡名称')">
                <el-input v-model="listFilter.nameLikes" :placeholder="i18n('类似于')" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item>
                <el-button type="primary" @click="search()">{{ i18n('查询') }}</el-button>
                <el-button @click="doReset()">{{ i18n('重置') }}</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-row>
      <el-row>
        <el-col :span="model === 'multiple' ? 17 : 24">
          <el-row class="table-wrap" v-loading="loading.query">
            <div class="thead" style="display: flex;">
              <div style="flex: 1;">
                <el-checkbox style="margin-left: 8px;" v-if="model === 'multiple'" @change="doCheckAll($event)" v-model="checkAll" />
                <span v-else>&nbsp;</span>
              </div>
              <div style="flex: 4">{{ i18n('卡号/卡名称') }}</div>
              <div style="flex: 3">{{ i18n('有效期') }}</div>
            </div>
            <el-row class="tbody" v-if="!loading.query" style="overflow-x:auto">
              <template v-if="currentList && currentList.length > 0">
                <div v-for="(item, index) of currentList" :key="index" style="display: flex;" class="trow">
                  <!-- 勾选框 -->
                  <div style="flex: 1;">
                    <div class="height-set">
                      <span style="position: relative;top: 18px;left: 8px;">
                        <el-checkbox v-model="checkboxList[index]" @change="doCheck($event, index)" />
                      </span>
                    </div>
                  </div>
                  <!-- 卡号/卡名称 -->
                  <div style="flex: 4;">
                    <div class="height-set">
                      <div>{{ item.id | strFormat }}</div>
                      <div :title="item.name">
                        {{ item.name }}
                      </div>
                    </div>
                  </div>
                  <!-- 有效期 -->
                  <div style="flex: 3;height:auto" class="height-set line-height-set">
                    <el-tooltip placement="top" v-if="item.expiryRules && item.expiryRules.length > 1" effect="light">
                      <div slot="content">
                        <span v-for="(val,ind) in item.expiryRules" :key="ind" style="display:flex;flex-direction:column">
                          <span>{{ValidityTime(val)}}</span>
                        </span>
                      </div>
                      <span style="cursor:pointer">{{ValidityTime(item.expiryRules[0])}}<span style="font-size:20px">...</span></span>
                    </el-tooltip>
                    <span v-else>{{ValidityTime(item.expiryRules[0])}}</span>
                  </div>
                </div>
              </template>
              <el-row v-if="!currentList || currentList.length === 0" class="trow" style="text-align: center;color: #909399">
                {{ i18n('暂无数据') }}
              </el-row>
            </el-row>
          </el-row>
        </el-col>
        <el-col :span="model === 'multiple' ? 7 : 0" style="padding-left: 5px">
          <el-row class="right-table">
            <el-row class="thead">
              {{ i18n('已选卡模板：') }}{{
                selected ? (selected.filter(e => e.id)).length : 0
              }}
            </el-row>
            <el-row style="padding: 5px;">
              <el-input style="width: 100%;" @keyup.enter.native="filterSelected()" @change="filterSelected()" v-model="selectedFilter" clearable
                @clear="filterSelected()" :placeholder="i18n('请输入卡名称')"></el-input>
            </el-row>
            <el-row class="tbody">
              <template v-if="filteredSelected && filteredSelected.length > 0">
                <el-row class="trow" style="position: relative;display: flex;align-items: center" v-for="(item, index) of filteredSelected"
                  :key="index" :title="{id:item.id,name:item.name}|idName">
                  <div class="left">{{ {id: item.id, name: item.name}|idName }}</div>
                  <div class="clear-btn" style="display: none">
                    <span class="span-btn" style="font-size:13px;margin-right:10px" @click="delItem(item, index)"
                      v-if="!alwaysSelectedIds.includes(item.id)">{{ formatI18n('/公用/公共组件/品牌选择弹框组件/表格/清除') }}</span>
                  </div>
                </el-row>
              </template>
              <el-row v-if="!filteredSelected || filteredSelected.length === 0" class="trow" style="text-align: center;color: #909399">
                {{ formatI18n('/公用/提示/暂无数据') }}
              </el-row>
            </el-row>
          </el-row>
        </el-col>
      </el-row>
    </div>
    <!-- <div class="page" style="margin-top: 15px">
      <el-pagination :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
        @current-change="handleCurrentChange($event)" @size-change="handleSizeChange($event)" background
        layout="total, prev, pager, next, sizes,  jumper">
      </el-pagination>
    </div> -->
    <div slot="footer" class="dialog-footer" style="position: relative;top: -80px;">
      <el-button size="small" @click="doCancel()">{{ formatI18n('/公用/按钮/取消') }}</el-button>
      <el-button size="small" type="primary" @click="doModalClose()">{{ formatI18n('/公用/按钮/确定') }}</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./EquityCardSelectorDialog.ts"/>

<style lang="scss" scoped>
.select-equity-card-dialog {
  display: flex;
  align-items: center;
  justify-content: center;
  @import "SelectorDialogCommon";

  .height-set {
    height: 68px;
    border-bottom: 1px solid #eeeeee;
  }

  .line-height-set {
    line-height: 68px;
    padding-left: 7px;
  }

  .tbody .trow .left {
    width: 220px !important;
  }

  .wrap {
    height: 630px;
  }

  .wrap .table-wrap .tbody .trow {
    height: 68px;
    line-height: 34px;
  }

  .overflow_text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }
}
</style>
<style lang="scss" scoped>
.short-label {
  ::v-deep .el-form-item__label {
    width: 100px !important;
  }
}
.select-equity-card-dialog {
  ::v-deep .el-dialog {
    width: 1324px !important;
    height: 720px !important;
    margin-top: 0 !important;
  }
}
.table-wrap {
  height: 450px !important;
}
.right-table {
  height: 450px !important;
}
</style>
