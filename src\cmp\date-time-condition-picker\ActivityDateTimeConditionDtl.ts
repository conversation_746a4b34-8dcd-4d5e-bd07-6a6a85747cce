import {Component, Prop} from 'vue-property-decorator'
import DateTimeConditionPickerParent from './DateTimeConditionPickerParent'
import ActivityDateTimeCondition from "model/common/ActivityDateTimeCondition";

// 先不搞，貌似没必要
@Component({
  name: 'ActivityDateTimeConditionDtl',
})
export default class ActivityDateTimeConditionDtl extends DateTimeConditionPickerParent {
  @Prop()
  modelValue: ActivityDateTimeCondition
  $refs: any
}
