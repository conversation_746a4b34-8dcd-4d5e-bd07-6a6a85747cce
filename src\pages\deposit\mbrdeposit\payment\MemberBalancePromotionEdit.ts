import { Component, Vue } from 'vue-property-decorator'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import MemberBalancePromotionForm from './MemberBalancePromotionForm'
import GoodsScopeEx from 'cmp/goodsscope/GoodsScopeEx.vue'
import MemberBalancePromotionApi from 'http/payment/member/MemberBalancePromotionApi'
import RSGrade from 'model/common/RSGrade'
import DateUtil from 'util/DateUtil'
import ActivityTopicApi from "http/v2/controller/points/topic/ActivityTopicApi";
import ActivityTopic from "model/v2/controller/points/topic/ActivityTopic";
import GradeStepValue from "model/common/GradeSameReduction";
import EditType from "common/EditType";
import ActiveStore from "cmp/activestore/ActiveStore";
import I18nPage from "common/I18nDecorator";
import MemberBalancePromotionPermissions from "./MemberBalancePromotionPermissions";
import CopyStepValue from "pages/deposit/prepaycard/cmp/copystepvalue/CopyStepValue";
import MarketingBudgetEdit from 'cmp/MarketingBudget/MarketingBudgetEdit'
import MarketBudget from 'model/promotion/MarketBudget'
import CommonUtil from 'util/CommonUtil'
import ActivityMgr from 'mgr/ActivityMgr'
import { MarketBudgetActivityEnum } from 'model/promotion/MarketBudgetActivityEnum'
import ActivityDateTimeConditionPicker from "cmp/date-time-condition-picker/ActivityDateTimeConditionPicker";
import SysConfigApi from "http/config/SysConfigApi";
import BrowserMgr from "mgr/BrowserMgr";

@Component({
  name: "MemberBalancePromotionEdit",
  components: {
    BreadCrume,
    GoodsScopeEx,
    ActiveStore,
    CopyStepValue,
    MarketingBudgetEdit,
    ActivityDateTimeConditionPicker,
  },
})
@I18nPage({
  prefix: ["/储值/会员储值/储值支付活动/编辑页面", "/公用/按钮", "/公用/券模板"],
})
export default class MemberBalancePromotionEdit extends Vue {
  goodsMatchRuleMode: string = "barcode"
  i18n: I18nFunc;
  permission = new MemberBalancePromotionPermissions();
  $refs: any;
  panelArray:any = [];
  form: MemberBalancePromotionForm = new MemberBalancePromotionForm();
  editType: EditType = EditType.CREATE;
  activityId: Nullable<string> = null;
  gradeList: RSGrade[] = [];
  themes: ActivityTopic[] = [];
  disabled: boolean = false;
  budget: Nullable<MarketBudget> = null

  created() {
    let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
		if (sysConfig && this.goodsMatchRuleMode) {
			this.goodsMatchRuleMode = sysConfig.goodsMatchRuleMode;
		}
    this.form.init(this);
    this.getConfig();
  }

  getConfig() {
    SysConfigApi.get().then((resp: any) => {
      if (resp && resp.data) {
        this.form.data.excludePromotion = resp.data.activityJoinPromotion
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  get dateRangeOption() {
    return {
      disabledDate(time: any) {
        return time.getTime() < DateUtil.nowDayTime();
      },
    };
  }

  get isOaActivity() {
    return ActivityMgr.isOaActivity(MarketBudgetActivityEnum.MemberBalanceReductionActivityRule)
  }

  mounted() {
    this.editType = this.$route.query.editType as EditType;
    this.activityId = this.$route.query.activityId as string;
    this.panelArray = [
      {
        name: this.i18n("储值支付优惠"),
        url: "member-balance-promotion-list",
      },
    ]
    if (this.editType === EditType.EDIT) {
      this.getGradeList(this.getDetail);
      this.panelArray.push({
        name: this.i18n("修改储值支付立减活动"),
        url: "",
      });
    }
    if (this.editType === EditType.COPY) {
      this.getGradeList(this.getDetail);
      this.panelArray.push({
        name: this.i18n("新建储值支付立减活动"),
        url: "",
      });
    }
    if (this.editType === EditType.CREATE) {
      this.getGradeList();
      this.panelArray.push({
        name: this.i18n("新建储值支付立减活动"),
        url: "",
      });
    }
    this.getTheme();
  }

  doSave(cb: any) {

    Promise.all([this.$refs.storeScope.validate(), this.$refs.activityDateTimeConditionPicker.validate(),
      this.$refs.goodsScope.validate(), this.$refs.form.validate(), this.$refs.marketingBudget.doValidate()]).then((res: any[]) => {
      if (res.filter((e) => !e).length === 0) {
        let method: any = null;
        let requestParams = this.form.toParams();
        if (this.editType === EditType.CREATE) {
          method = MemberBalancePromotionApi.create;
          // this.form.data.gradeSameStepValue = new GradeStepValue();
        }
        if (this.editType === EditType.COPY) {
          method = MemberBalancePromotionApi.create;
        }
        if (this.editType === EditType.EDIT) {
          method = MemberBalancePromotionApi.modify;
          if (requestParams.body) {
            requestParams.body.activityId = this.activityId;
          }
        }
        if (method === null) {
          return;
        }
        if (requestParams.body) {
          requestParams.body.budget = this.budget
        }
        method(requestParams)
          .then((res: any) => {
            if (res.code === 2000) {
              this.$message.success(this.i18n("保存成功"));
              if (cb) {
                cb(res);
              } else {
                this.$router.push({ name: "member-balance-promotion-dtl", query: { activityId: res.data } });
              }
            } else {
              this.$message.error(res.msg);
            }
          })
          .catch((reason: any) => {
            this.$message.error(reason.msg);
          });
      }
    });
  }

  doSaveAudit() {
    this.doSave((res: any) => {
      MemberBalancePromotionApi.audit(res.data)
        .then((res: any) => {
          if (res.code === 2000) {
            this.$message.success(this.i18n("审核成功"));
          } else {
            this.$message.error(this.i18n("审核失败，原因：") + res.msg);
          }
        })
        .catch((reason: any) => {
          this.$message.error(this.i18n("审核失败，原因：") + reason.message);
        })
        .finally(() => {
          this.$router.push({ name: "member-balance-promotion-dtl", query: { activityId: res.data } });
        });
    });
  }

  doCancel() {
    this.$router.back();
  }

  changeStrategy() {
    this.$refs.form.clearValidate();
    this.form.data.gradeSameStepValue = new GradeStepValue();
    for (let item of this.form.data.gradeDifferentStepValue) {
      (item as any).value = null;
      (item as any).threshold = null;
    }
    this.$forceUpdate();
  }

  doCopy(originGradeCode: string, index: number) {
    if (!this.form.data.strategy || !this.form.data.gradeDifferentStepValue) {
      return;
    }
    let reduction:any = this.form.data.gradeDifferentStepValue[index];
    if (!reduction.value || !reduction.threshold) {
      this.$message.warning(this.i18n("规则不完整，请完整维护规则后再复制"));
      return;
    }
    let gradeListCopy = JSON.parse(JSON.stringify(this.gradeList));
    let reductionCopy = JSON.parse(JSON.stringify(reduction));
    this.$refs.copyStepValue.show(originGradeCode, gradeListCopy, this.form.data.strategy, reductionCopy);
  }

  copyStepValue(checkedGradeCodeList: string[], gradeStepValue: GradeStepValue) {
    if (!this.form.data.gradeDifferentStepValue) {
      return;
    }
    let filteredReductions = this.form.data.gradeDifferentStepValue.filter((e: GradeStepValue) => checkedGradeCodeList.indexOf(e.grade + "") > -1);
    for (let item of filteredReductions) {
      item.checked = true;
      (item as any).threshold = (gradeStepValue as any).threshold;
      (item as any).value = (gradeStepValue as any).value;
    }
  }

  private getGradeList(cb: Nullable<any> = null) {
    MemberBalancePromotionApi.gradeList().then((res: any) => {
      if (res.code === 2000) {
        this.gradeList = res.data;
        this.gradeList.sort((a: any, b: any) => {
          if (a.type !== b.type) {
            let typeMap: any = {
              FREE: 1,
              PAID: 2,
              SPECIAL: 3,
            };
            return typeMap[a.type] - typeMap[b.type];
          } else {
            return a.no - b.no;
          }
        });
        if (this.form.data.gradeDifferentStepValue) {
          for (let grade of this.gradeList) {
            let gradeStepValue = new GradeStepValue();
            gradeStepValue.grade = grade.code;
            gradeStepValue.gradeName = grade.name;
            gradeStepValue.checked = false;
            (gradeStepValue as any).threshold = null;
            (gradeStepValue as any).value = null;
            this.form.data.gradeDifferentStepValue.push(gradeStepValue);
          }
        }
        if (cb) {
          cb(res.data);
        }
      }
    });
  }

  private getDetail(gradeList: RSGrade[]) {
    if (!this.activityId) {
      return;
    }
    const loading = CommonUtil.Loading()
    MemberBalancePromotionApi.info(this.activityId)
      .then((res: any) => {
        if (res.code === 2000) {
          this.form.of(res.data, gradeList);
          this.disabled = ['UNSTART', 'PROCESSING'].indexOf(res.data.body.state) > -1 && this.editType === EditType.EDIT
          this.budget = res.data?.body?.budget || null
        } else {
          this.$message.error(res.msg);
        }
      })
      .catch((reason: any) => {
        this.$message.error(reason.message);
      }).finally(()=> {
        loading.close()
      })
  }

  private validateGoodsScope() {
    this.$refs.form.validateField("goods");
  }

  private getTheme() {
    ActivityTopicApi.listTopic().then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.themes = resp.data;
      }
    });
  }

  private refresh(val: any) {
    this.form.data.gradeDifferentStepValue = JSON.parse(JSON.stringify(this.form.data.gradeDifferentStepValue));
  }

  private changeFavThresholdLimit() {
    this.form.data.favThreshold = null;
    this.$refs.form.validateField("favThreshold");
  }
}