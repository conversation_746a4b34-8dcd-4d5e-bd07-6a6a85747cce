import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import emitter from 'util/emitter';
import { FormMode } from 'model/local/FormMode';
import FormDefUtil from 'util/FormDefUtil';
import I18nPage from 'common/I18nDecorator';

@Component({
  name: 'ShowTime',
  mixins: [emitter],
  // components: { FormDetail },
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/公用/菜单',
    '/页面/页面管理',
    '/公用/过滤器',
  ],
  auto: true
})
export default class ShowTime extends Vue {
  @Prop({ type: Boolean, default: false })
  readonly: boolean; //
  @Prop({ type: String, default: 'TitleFontColor' })
  validateName: string; // 组件校验名称，editPage通过该名称进行组件校验及校验信息清除
  @Prop({ type: Object })
  value: any; // 数据模型
  @Prop({ type: String, default: '标题字体色' })
  label: string; // label名
  @Prop({ type: String })
  prefixDescribe: string; // 前置描述
  @Prop({ type: String })
  suffixDescribe: string; // 后置描述

  @Prop({ type: Boolean, default: true })
  required: boolean; // 是否必填
  @Prop({ type: String })
  relativeValidateName: string; // 联动校验名称
  @Prop()
  formKey: any;
  @Prop({
    type: Object,
    default: () => { },
  })
  config: any; // 配置项
  FormModeType = FormMode;
  $refs: any;
  labelPosition: string = 'left';

  rules = {
    placeName: [{ required: false, message: this.i18n('请输入'), trigger: ['blur', 'change'] }],
  };

  get formMode() {
    if (this.validateName === 'ShowTime') {
      return FormDefUtil.getFormMode(this.readonly, this.validateName, this.formKey);
    } else {
      return FormDefUtil.getFormMode(this.readonly, this.validateName + 'ShowTime', this.formKey);
    }
  }

  pickerOptions = {
    disabledDate: (time) => {
      let nowData = new Date();
      nowData = new Date(nowData.setDate(nowData.getDate() - 1));
      return time < nowData;
    },
  };

  handleChange() {
    if (!this.value.propDateRange) this.value.propDateRange = []

    this.$emit('input', this.value);
    this.$emit('change', this.value);
    this.validate(() => { });
  }
  handleChanges() {
    if (!this.value.propCycle) {
      this.value.propCycleConditions = []
    } else {
      this.value.propCycleConditions = [
        {
          beginTime: "00:00:00",
          endTime: "23:59:59",
        },
      ]
    }
    this.$emit('input', this.value);
    this.$emit('change', this.value);
    this.validate(() => { });
  }
  mounted() {
    if (this.validateName) {
      this['dispatch']('EditPage', 'nf.edit.addForm', [this]);
    }
  }

  beforeDestroy() {
    this['dispatch']('EditPage', 'nf.edit.removeForm', [this]);
  }

  validate(callback) {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.validate(callback);
    }
  }

  clearValidate() {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.clearValidate();
    }
  }
}
