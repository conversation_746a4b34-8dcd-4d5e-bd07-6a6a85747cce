<template>
  <div class="cardtpl-view">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button v-if="permission.anyEditable" type="primary" @click="add">{{ formatI18n('/储值/预付卡/卡模板/列表页面/新建卡模板') }}</el-button>
      </template>
    </BreadCrume>
    <div class="current-page">
      <el-row class="query">
        <el-col :span="12">
          <div style="width: 900px">
            <el-tabs v-model="cardTemplateType" @tab-click="changeTab">
              <el-tab-pane name="ALL" :label="allCount" v-if="permission.allViewable">
              </el-tab-pane>
              <el-tab-pane
                      :label="giftCardCount"
                      name="GIFT_CARD"
                      v-if="permission.GIFT_CARD_viewable"
                      >
              </el-tab-pane>
              <el-tab-pane
                      :label="imprestCardCount"
                      name="IMPREST_CARD"
                      v-if="permission.IMPREST_CARD_viewable">
              </el-tab-pane>
              <el-tab-pane
                      :label="rechargeableCardCount"
                      name="RECHARGEABLE_CARD"
                      v-if="permission.RECHARGEABLE_CARD_viewable">
              </el-tab-pane>
              <el-tab-pane
                      :label="countCardCount"
                      name="COUNTING_CARD"
                      v-if="permission.COUNTING_CARD_viewable">
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-col>
        <el-col :span="12" style="text-align: right;min-width: 530px">
          <el-form :inline="true">
            <el-form-item label="" v-if="enableMultipleAccount">
              <el-select @change="getList" v-model="query.accountTypeIdEquals">
                <el-option :label="formatI18n('/储值/预付卡/卡模板/列表页面/全部账户类型')" :value="null">
                  {{ formatI18n('/储值/预付卡/卡模板/列表页面/全部账户类型') }}
                </el-option>
                <el-option v-for="account of accounts" :label="'[' + account.id + '] ' + account.name"
                          :value="account.id" :key="account.id"/>
              </el-select>
            </el-form-item>
            <el-form-item label="">
              <el-input
                  :placeholder="formatI18n('/储值/预付卡/卡模板/列表页面/搜索卡模板号/名称')"
                  @change="doSearch"
                  v-model="query.numberOrNameStartsWith"
                  suffix-icon="el-icon-search"
                  style="width: 280px"
              />
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <el-row class="table">
        <el-table
          :data="queryData"
          style="width: 100%;margin-top: 20px"
        >
          <el-table-column
              fixed
              :label="formatI18n('/储值/预付卡/卡模板/列表页面/卡模板号/名称')"
              prop="name"
              width="200"
          >
            <template slot-scope="scope">
              <p style="color: rgba(51, 51, 51, 0.***************);">{{scope.row.number}}</p>
              <a href="javascript:void(0)" :title="scope.row.name" @click="gotoDtl(scope.row)">{{scope.row.name}}</a>
            </template>
          </el-table-column>
          <el-table-column
              fixed
              :label="formatI18n('/储值/预付卡/卡模板/列表页面/账户类型')"
              width="150"
              prop="accountType"
              v-if="enableMultipleAccount"
          >
            <template slot-scope="scope">
              {{dataUtil.showIdName(scope.row.accountType)}}
            </template>
          </el-table-column>
          <el-table-column
            fixed
            :label="formatI18n('/储值/预付卡/卡模板/列表页面/卡类型')"
            width="150"
          >
            <template slot-scope="scope">
              <span :title="formatI18n('/储值/预付卡/卡模板/公共/卡类型/充值卡')" v-if="scope.row.cardTemplateType === 'IMPREST_CARD'">{{
                  formatI18n('/储值/预付卡/卡模板/公共/卡类型/充值卡')
                }}</span>
              <span :title="i18n('礼品卡')" v-if="scope.row.cardTemplateType === 'GIFT_CARD'">
                {{i18n('礼品卡')}}
              </span>
              <span :title="formatI18n('/储值/预付卡/卡模板/公共/卡类型/储值卡')" v-if="scope.row.cardTemplateType === 'RECHARGEABLE_CARD'">{{
                  formatI18n('/储值/预付卡/卡模板/公共/卡类型/储值卡')
                }}</span>
              <span :title="i18n('次卡')" v-if="scope.row.cardTemplateType === 'COUNTING_CARD'">{{
                i18n('次卡')
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
              fixed
              :label="i18n('卡介质')"
              width="200"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.cardMedium === 'online'">{{i18n('电子卡')}}</span>
              <span v-if="scope.row.cardMedium === 'bar'">{{i18n('条码卡')}}</span>
              <span v-if="scope.row.cardMedium === 'mag'">{{i18n('磁条卡')}}</span>
              <span v-if="scope.row.cardMedium === 'rfic'">{{i18n('rfic卡')}}</span>
              <span v-if="scope.row.cardMedium === 'ic'">{{i18n('ic卡')}}</span>
            </template>
          </el-table-column>
          <el-table-column
              fixed
              :label="formatI18n('/储值/预付卡/卡模板/列表页面/有效期')"
              width="200"
          >
            <template slot-scope="scope">
              <span :title="validatyInfo(scope.row)">{{validatyInfo(scope.row)}}</span>
            </template>
          </el-table-column>
          <el-table-column
              fixed
              :label="formatI18n('/储值/预付卡/卡模板/列表页面/适用商品')"
              width="280"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.goods.limit === null">-</span>
              <template v-if="scope.row.cardTemplateType === 'IMPREST_CARD'">
                <span>-</span>
              </template>
              <template v-else-if="scope.row.cardTemplateType === 'COUNTING_CARD'">
                <span>{{formatI18n('/储值/预付卡/卡模板/列表页面/指定商品适用')}}</span>
              </template>
              <template v-else>
                <span :title="formatI18n('/储值/预付卡/卡模板/列表页面/指定商品适用')" v-if="scope.row.goods.limit && !scope.row.goods.excludePrecondition">{{
                    formatI18n('/储值/预付卡/卡模板/列表页面/指定商品适用')
                  }}</span>
                <span :title="formatI18n('/储值/预付卡/卡模板/列表页面/指定商品不适用')" v-else-if="scope.row.goods.limit && scope.row.goods.excludePrecondition">{{
                    formatI18n('/储值/预付卡/卡模板/列表页面/指定商品不适用')
                  }}</span>
                <span :title="formatI18n('/储值/预付卡/卡模板/列表页面/全部商品适用')" v-else-if="scope.row.goods.limit === false">{{ formatI18n('/储值/预付卡/卡模板/列表页面/全部商品适用') }}</span>
              </template>
            </template>
          </el-table-column>
          <el-table-column
              fixed
              :label="formatI18n('/储值/预付卡/卡模板/列表页面/适用门店')"
              prop="cardTemplateType"
              width="200"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.stores.storeRangeType === null">-</span>
              <template v-if="scope.row.cardTemplateType === 'IMPREST_CARD'">
                <span>-</span>
              </template>
              <template v-else>
                <span :title="formatI18n('/储值/预付卡/卡模板/列表页面/全部门店')" v-if="scope.row.stores.storeRangeType === 'ALL'">{{ formatI18n('/储值/预付卡/卡模板/列表页面/全部门店') }}</span>
                <span :title="formatI18n('/储值/预付卡/卡模板/列表页面/部分门店')" v-if="scope.row.stores.storeRangeType === 'PART'">{{ formatI18n('/储值/预付卡/卡模板/列表页面/部分门店') }}</span>
                <span :title="formatI18n('/储值/预付卡/卡模板/列表页面/指定门店不适用')" v-if="scope.row.stores.storeRangeType === 'EXCLUDE'">{{ formatI18n('/储值/预付卡/卡模板/列表页面/指定门店不适用') }}</span>
              </template>
              </template>
          </el-table-column>
          <el-table-column
              fixed
              :label="formatI18n('/储值/预付卡/卡模板/列表页面/操作')"
          >
            <template slot-scope="scope">
              <el-button type="text" v-if="permission.editable(scope.row.cardTemplateType)" @click="copy(scope.row.number, scope.row.cardTemplateType)">{{ formatI18n('/公用/按钮/复制') }}</el-button>
              <el-button type="text" v-if="permission.editable(scope.row.cardTemplateType)" @click="edit(scope.row.number, scope.row.cardTemplateType)">{{ formatI18n('/公用/按钮/修改') }}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-pagination
        :current-page="page.currentPage"
        :page-size="page.size"
        :page-sizes="[10, 20, 30, 40]"
        :total="page.total"
        @current-change="onHandleCurrentChange"
        @size-change="onHandleSizeChange"
        background
        layout="total, prev, pager, next, sizes,  jumper"
        class="pagin"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script lang="ts" src="./PrepayCardTpl.ts">
</script>

<style lang="scss">
.cardtpl-view {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: auto;
  .total {
    margin: 20px;
  }

  .current-page {
    height: calc(100% - 80px);
    overflow: auto;
    .el-select {
      width: 100%;
    }
    .query {
      padding: 20px 20px 0 20px;
      white-space: nowrap;
    }
    .table {
      padding: 0 20px 20px 20px;

      .cell {
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .list {
      height: calc(100% - 150px);
      overflow: hidden;

      .el-col {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    .pagin {
      padding: 20px;
    }

    .el-form-item {
      margin-bottom: 0;
    }
  }

  .el-tabs__nav-wrap::after {
    background-color: white;
  }
}
</style>
