<template>
  <div class="cash-coupon">
    <el-form :model="ruleForm" :rules="rules" class="demo-ruleForm" label-width="120px" ref="ruleForm">
      <div v-if="$route.query.from === 'edit' && !wxScanForCoupon"
        style="height: 48px;line-height: 48px;background-color: #3366ff19;margin: 0 20px;padding-left: 20px;margin-bottom: 10px">
        <img style="position: relative;top: 5px;" src="~assets/image/auth/info3.png" alt="" />{{
					formatI18n("/权益/券/券模板/编辑界面/用券规则下的提示/券模板修改后，已发出的券的有效期仍以原来的为准，其余用券规则以修改后的为准。")
				}}
      </div>
      <div class="setting-container">
        <!-- 基础信息 -->
        <div class="setting-block">
          <div class="section-title">{{i18n('基础信息')}}</div>
          <!-- 券名称 -->
          <CouponName :ruleForm="ruleForm" :copyFlag='copyFlag'>
            <template slot="slot">
              <el-input maxlength="128" style="width: 390px" v-model="ruleForm.name" @change="doFormItemChange" :placeholder="i18n('请输入')"></el-input>
            </template>
          </CouponName>
          <!-- 券图标 -->
          <CouponTemplateLogo :original-logo-url="ruleForm.logoUrl" @logoUrlCallBack="logoUrlCallBack"></CouponTemplateLogo>
          <!-- 使用须知 -->
          <UseCouponDesc :ruleForm="ruleForm" :isShowTips="false">
            <template slot="slot">
              <el-input :maxlength="remarkMaxlength" style="width: 390px;" type="textarea" v-model="ruleForm.couponProduct"
                :placeholder="remarkPlaceholder" @change="doFormItemChange">
              </el-input>
            </template>
          </UseCouponDesc>
        </div>

        <!-- 优惠设置 -->
        <div class="setting-block">
          <div class="section-title">{{i18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/优惠设置')}}</div>
          <!-- 券面额范围 -->
          <el-form-item :label="formatI18n('/公用/券模板', '券面额范围')">
            <div style="display: inline-block">
              <el-form-item class="cur-day" prop="minFaceAmount" style="display: inline-block">
                <el-input @change="doMinFaceAmountChange" style="width: 120px" v-model="ruleForm.minFaceAmount">
                  <template slot="append">{{ formatI18n("/公用/券模板", "元") }}</template>
                </el-input>
              </el-form-item>
              &nbsp;{{i18n('至')}}&nbsp;
              <el-form-item class="cur-day" prop="maxFaceAmount" style="display: inline-block">
                <el-input @change="doMaxFaceAmountChange" style="width: 120px" v-model="ruleForm.maxFaceAmount">
                  <template slot="append">{{ formatI18n("/公用/券模板", "元") }}</template>
                </el-input>
              </el-form-item>
            </div>
          </el-form-item>
          <!--用券门槛-->
          <el-form-item class="coupon-step" :label="formatI18n('/公用/券模板', '用券门槛')">
            <UseCouponStep :showQty="false" :max="options.maxUseThreshold" :amount="ruleForm.amount" @change="doFormItemChange"
              @limitChange="limitChange" ref="useCouponStep" v-model="ruleForm.type" :type="formatI18n('/公用/券模板', '全场现金券')">
            </UseCouponStep>
          </el-form-item>
          <!-- 券承担方 -->
          <el-form-item :label="formatI18n('/公用/券模板', '券承担方')">
            <CouponBear ref="CouponBear" :state="curState" v-model="ruleForm.couponUnder" @change="doFormItemChange"></CouponBear>
          </el-form-item>
          <!-- 用券记录方式 -->
          <RecordWay ref="recordWay" :copyFlag="copyFlag" v-model="ruleForm" :baseFieldEditable="baseFieldEditable" labelWidth="120px"
            :specialGoods="specialGoods" @change="doFormItemChange" @changeSpecialGoods="changeSpecialGoods">
          </RecordWay>
          <!-- 叠加用券-->
          <GroupMutexTemplate ref="groupMutexTemplate" v-model="ruleForm.groupMutex" @change="doFormItemChange" @checkLimit="checkLimit">
          </GroupMutexTemplate>
          <!-- 随机金额券没有叠加促销？？ -->
        </div>

        <!-- 用券时间 -->
        <div class="setting-block">
          <div class="section-title">{{i18n('/营销/券礼包活动/券查询/用券时间')}}</div>
          <!-- 券有效期 -->
          <CouponEffectPeriod ref="couponEffectPeriod" v-model="ruleForm" :options="options" @change="doFormItemChange"></CouponEffectPeriod>
          <!-- 用券时段 -->
          <el-form-item :label="formatI18n('/公用/券模板', '用券时段')" class="cur-form-item">
            <TimeRange :fixedAll="options.fixedTime" @change="doFormItemChange" v-model="ruleForm.time" ref="timeRange"> </TimeRange>
          </el-form-item>
        </div>

        <!-- 用券范围 -->
        <div class="setting-block">
          <div class="section-title">{{i18n('用券范围')}}</div>
          <!-- 用券渠道 -->
          <el-form-item :label="formatI18n('/公用/券模板', '用券渠道')" class="cur-from-item" required>
            <el-radio-group @change="doUseFromChange" v-model="ruleForm.useFrom">
              <el-radio label="step2" style="display: block">
                <span>{{ formatI18n("/公用/券模板/用券渠道", "指定渠道适用") }}</span>
                <div style="display:inline-block;margin-left:10px">
                  <el-form-item class="cur-from-item" prop="useFrom">
                    <el-select style="width: 250px" :disabled="ruleForm.useFrom === 'step1'" multiple @change="doFromChange" v-model="ruleForm.from"
                      :placeholder="formatI18n('/公用/券模板/用券渠道', '请至少选择一个渠道')">
                      <el-option v-for="(item, index) in channels" :key="`channel${index}`" :label="item.name"
                        :value="item.channel.typeId">{{ item.name }}
                      </el-option>
                    </el-select>
                  </el-form-item>
                </div>
              </el-radio>
              <el-radio label="step1">
                {{ formatI18n("/公用/券模板/用券渠道", "全部渠道") }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <!-- 用券门店 -->
          <el-form-item :label="formatI18n('/公用/券模板', '用券门店')" prop="storeRange">
            <ActiveStore :isOldActivity="false" ref="activeStore" :sameStore="sameStore" :enableStore="enableStore" v-model="ruleForm.storeRange" @change="doStoreChange">
            </ActiveStore>
          </el-form-item>
          <!-- 用券商品 -->
          <el-form-item :label="formatI18n('/公用/券模板', '用券商品')" class="cur-form-item" prop="goodsScope">
            <GoodsScopeEx ref="goodsScope" :importNumber="20000" v-model="ruleForm.useCouponGood" :goodsMatchRuleMode="goodsMatchRuleMode" @change="doGoodsRange" type="cash"> </GoodsScopeEx>
          </el-form-item>
        </div>
      </div>

      <div class="setting-container">
        <div class="setting-block">
          <div class="section-title">
            <span>{{i18n('高级设置')}}</span>
            <span class="telescoping" @click="telescopingChange">
              <template v-if="!telescoping">{{i18n('/公用/查询条件/收起')}}<i class="el-icon-arrow-up"></i></template>
              <template v-else>{{i18n('/公用/查询条件/展开')}}<i class="el-icon-arrow-down"></i></template>
            </span>
            <span class="gray-tips" style="margin-left:12px">{{i18n('券码生成规则、能否转赠、标签等')}}</span>
          </div>
          <div v-show="!telescoping">
            <!-- 券码生成规则 -->
            <CouponCodeRules :ruleForm="ruleForm" :copyFlag="copyFlag" :wxScanForCoupon="wxScanForCoupon">
              <template slot="slot">
                <el-input :placeholder="formatI18n('/营销/券礼包活动/券礼包活动/新建导出券码发券', '请输入6位以内的数字或字母')" maxlength="6" style="width: 325px"
                  v-model="ruleForm.prefix" @change="doFormItemChange">
                </el-input>
              </template>
            </CouponCodeRules>
            <!-- 能否转赠 -->
            <el-form-item :label="formatI18n('/公用/券模板', '能否转赠')">
              <el-radio-group v-model="ruleForm.transferable" @change="doFormItemChange">
                <el-radio :label="true">{{ formatI18n("/公用/券模板", "是") }}</el-radio>
                <el-radio :label="false">{{ formatI18n("/公用/券模板", "否") }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <!-- 标签 -->
            <CouponTemplateLabel class="cur-from-item" v-model="ruleForm.templateTag" :templateId="ruleForm.templateId" @change="doFormItemChange" />
            <!-- 价格 -->
            <el-form-item :label="i18n('/公用/券模板/价格')">
              <AutoFixInput :min="0.01" :max="99999.99" :fixed="2" @change="doFormItemChange" v-model="ruleForm.price" style="width: 148px"
                :appendTitle="formatI18n('/券/购券管理','元')" />
            </el-form-item>
            <!-- 账款项目 -->
            <el-form-item :label="i18n('账款项目')" prop="termsModel">
              <div v-if="queryCostParyRange=='cost_party'">
                <SelectCostParty v-model="ruleForm.termsModel" :isOnlyId="true" :hideAll="true" width="20%" :placeholder="formatI18n('/公用/下拉框/提示', '请选择')" >
                </SelectCostParty>
              </div>
              <el-input v-if="queryCostParyRange=='customize'" :placeholder="i18n('/储值/预付卡/卡模板/详情页面/请输入{0}位以内的数字或字母',['128'])" maxlength="128" style="width: 325px"
                v-model="ruleForm.termsModel" @change="doFormItemChange">
              </el-input>
            </el-form-item>
            <!-- C端我的券角标 -->
            <el-form-item :label="formatI18n('/公用/券模板', 'C端我的券角标')">
              <el-radio-group v-model="ruleForm.couponSubscriptType" @change="doFormItemChange">
                <el-radio :label="'COMMON'">{{ formatI18n("/公用/券模板", "通用券") }}</el-radio>
                <el-radio :label="'ONLINE'">{{ formatI18n("/公用/券模板", "线上券") }}</el-radio>
                <el-radio :label="'OFFLINE'">{{ formatI18n("/公用/券模板", "线下券") }}</el-radio>
                <el-radio :label="'OUTSIDE'">{{ formatI18n("/公用/券模板", "外部券") }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <!-- 核销链接 -->
            <el-form-item :label="formatI18n('/公用/券模板', '核销链接')">
              <div class="gray-tips">{{formatI18n('/公用/券模板/外部对接使用，比如客户自研小程序上点击用券跳转自定义链接去核销券')}}</div>
              <el-input v-model="ruleForm.writeOffLink" type="textarea" :placeholder="formatI18n('/储值/会员储值/储值充值活动/编辑页面','请输入不超过500个字')" :rows="10"
                        maxlength="500" style="width: 500px;" @change="doFormItemChange" />
            </el-form-item>
          </div>
        </div>
      </div>
    </el-form>
    <!-- 设置特殊商品 -->
    <SelectGoodsDialog ref="SelectGoodsDialog" :title="i18n('设置')" @submit="specialGoodsSubmit"></SelectGoodsDialog>
    <!-- 查看特殊商品 -->
    <SpecialGoodsDialog ref="SpecialGoodsDialog" :data="specialGoods"></SpecialGoodsDialog>
  </div>
</template>

<script lang="ts" src="./RandomCashCoupon.ts"></script>

<style lang="scss">
.cash-coupon {
  padding-bottom: 30px;
  .setting-container {
    .setting-block {
      .section-title {
        .telescoping {
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #007eff;
          margin-left: 12px;
          cursor: pointer;
          i {
            color: #242633;
          }
        }
      }
    }
  }

  .coupon-step,
  .cur-form-item {
    .el-form-item__label {
      &:before {
        content: "*";
        color: #ef393f;
        margin-right: 4px;
      }
    }
  }

  .rule-table {
    margin-top: 10px;
    width: 70%;

    .rule-table-header {
      padding: 0 10px;
      background-color: #e6e6e6;
      border: 1px solid #e6e6e6;
    }

    .rule-table-line {
      padding: 10px;
      border: 1px solid #e6e6e6;
      border-top: 0;
    }

    .opt-col {
      a + a {
        margin-left: 10px;
      }
    }
  }

  .cur-from-item {
    height: auto !important;

    .el-radio {
      height: auto !important;
      line-height: 40px;
    }
  }
}
</style>
