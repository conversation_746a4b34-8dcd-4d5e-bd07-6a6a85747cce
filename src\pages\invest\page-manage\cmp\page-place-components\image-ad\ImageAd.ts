import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import draggable from 'vuedraggable';
import VueDraggableResizable from 'vue-draggable-resizable';
import PlaceTemplateMixins from '../PlaceTemplateMixins';
import 'vue-draggable-resizable/dist/VueDraggableResizable.css';
import I18nPage from 'common/I18nDecorator';

@Component({
  name: 'ImageAd',
  components: { draggable, VueDraggableResizable },
  mixins: [PlaceTemplateMixins],
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理',
  ],
  auto: true
})
export default class ImageAd extends Vue {
  @Prop()
  componentItem: any;
  marginBottom = 10;
  currentIndex = 0;
  propImageHeight = 0;
  currentItem: any = {};
  preClickItem: any = {};
  currentClickItem: any = null;
  flag = false;
  $refs: any;

  form: any = null;

  created() {
    // this.$bus.on('cms.edit.imageAd', (form) => {
    //   this.form = form;
    // });
  }

  // 页面关闭 卸载事件监听
  beforeDestroy() {
    // this.$bus.off('cms.edit.imageAd');
  }

  mounted() {
    this.imageOnload();
  }
  get localProperty() {
    this.componentItem.props.propItems?.forEach((item: any) => {
      if (!item.validateResult) {
        this.$set(item, 'validateResult', false);
      }
    });
    return this.componentItem.props;
  }

  get defaultUrl() {
    return 'pic_emptypic.png';
  }

  onDragStartCallback(ev: any) {
    ev.preventDefault();
  }

  onDragstop(x: any, y: any) {
    this.currentItem.leftVertexMarginLeft = x;
    this.currentItem.leftVertexMarginTop = y;
    this.localPropertyChanged();
  }

  onResizeStop(x: any, y: any, width: any, height: any) {
    this.currentItem.leftVertexMarginLeft = x;
    this.currentItem.leftVertexMarginTop = y;
    this.currentItem.hotZoneWidth = width;
    this.currentItem.hotZoneHeight = height;
    this.localPropertyChanged();
  }

  onActivated(item: object, index: number) {
    this.currentItem = item;
    this.$store.commit('clickSpotSetData', {
      item,
      index,
    });
    // 记录上一次点击的热区
    this.preClickItem = JSON.parse(JSON.stringify(this.currentClickItem));
    // 记录当前活跃的热区
    this.currentClickItem = item;
    // 删除当前点击的热区加d标识
    this.componentItem.props.propItems?.forEach((item: any) => {
      delete item.isClick;
    });
    // 给当前点击的热区加个标识
    this.$set(item, 'isClick', true);

    // 热区校验
    this.validateHotPlace();
  }
  /**
   * 三种态 （1、当前点击，2、校验失败，3、校验成功）
   */
  get className() {
    return (result: any, item: any) => {
      // 如果当前点击的有点击标识则返回点击需要的 class 类名
      if (item.isClick) {
        return 'drag-area-active';
      }
      // 如果当前点击的有校验成功或者失败则返回对应的class名
      if (result) {
        return 'image-ad-hot-zone-success';
      } else {
        return 'image-ad-hot-zone-fail';
      }
    };
  }

  // 校验方法
  validateHotPlace() {
    // 如果上次被点击没有记录则停止校验
    if (!this.preClickItem) return;
    // 获取点击的上次数据
    const item1 = this.componentItem.props.propItems.find((item: { zIndex: any; }) => {
      return this.preClickItem.zIndex === item.zIndex;
    });

    console.log('item1', item1);
    console.log('preClickItem', this.preClickItem);
    // 修改上次点击的校验结果
    if (!item1.targetPage) {
      if (item1) {
        item1.validateResult = false;
      }
    } else {
      if (!item1) return;
      // if (item1.targetPage === '活动页面') {
      //   if (item1.pageParams.templateId) {
      //     item1.validateResult = true;
      //   } else {
      //     item1.validateResult = false;
      //   }
      // } else if (item1.targetPage === '商品详情') {
      //   if (item1.pageParams.skuInputCode && item1.pageParams.skuInputCode.length > 0) {
      //     item1.validateResult = true;
      //   } else {
      //     item1.validateResult = false;
      //   }
      // } else if (item1.targetPage === '外部链接') {
      //   if (item1.pageParams.extLinkType) {
      //     item1.validateResult = true;
      //   } else {
      //     item1.validateResult = false;
      //   }
      // } else if (item1.targetPage === '外部小程序') {
      //   if (item1.pageParams.appId && item1.pageParams.originId && item1.pageParams.address) {
      //     item1.validateResult = true;
      //   } else {
      //     item1.validateResult = false;
      //   }
      // } else if (item1.targetPage === '储值详情页面') {
      //   if (item1.pageParams.rechargeType) {
      //     item1.validateResult = true;
      //   } else {
      //     item1.validateResult = false;
      //   }
      // } else if (item1.targetPage === '作品详情') {
      //   if (item1.pageParams.videoActivityId) {
      //     item1.validateResult = true;
      //   } else {
      //     item1.validateResult = false;
      //   }
      // } else if (item1.targetPage === '外部地址') {
      //   if (item1.pageParams.address) {
      //     item1.validateResult = true;
      //   } else {
      //     item1.validateResult = false;
      //   }
      // } else if (item1.targetPage === '视频号直播间') {
      //   if (item1.pageParams.channelLiveId) {
      //     item1.validateResult = true;
      //   } else {
      //     item1.validateResult = false;
      //   }
      // } else {
      //   item1.validateResult = true;
      // }
    }
  }

  // get classNameActive() {

  // }

  localPropertyChanged() {
    this['activeTemplate'];
  }

  imageOnload() {
    let backgroundImg = this.$refs['backgroundImg' + this['activeIndex']];
    if (backgroundImg) {
      backgroundImg.onload = () => {
        this.flag = true;
        console.log('backgroundImg ==>', backgroundImg.offsetHeight, backgroundImg.offsetWidth);
        this.$store.commit('setImageAdProp', {
          propImageWidth: backgroundImg.offsetWidth,
          propImageHeight: backgroundImg.offsetHeight,
        });
        this.propImageHeight = backgroundImg!.offsetHeight;
      };
    }
  }

  doRemoveHotZone(index: number, item: any) {
    if (this.localProperty.currentEditItem && this.localProperty.currentEditItem.zIndex === index) {
      this.localProperty.currentEditItem = {};
    }
    this.localProperty.propItems.splice(index, 1);
    for (let i = 0; i < this.localProperty.propItems.length; i++) {
      this.localProperty.propItems[i].zIndex = i;
    }
    this.componentItem.props.propItems = this.localProperty.propItems;

    if (index !== 0) {
      this.onActivated(this.localProperty.propItems[0], 0);
    }
    this.localPropertyChanged();
  }
}
