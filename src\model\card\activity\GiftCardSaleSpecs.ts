import GiftInfo from 'model/common/GiftInfo'
import ValidityInfo from 'model/common/ValidityInfo'

export default class GiftCardSaleSpecs {
    // 卡模板号
    cardTemplateNumber: Nullable<string> = null
    // 卡模板名称
    cardTemplateName: Nullable<string> = null
    // 充值面额
    faceAmount: Nullable<number> = null
    // 售价
    price: Nullable<number> = null
    // 折扣
    discount: Nullable<number> = null
    // 模板设置的售价
    templatePrice: Nullable<number> = null
    // 每人最多限购
    maxBuyQtyPerMan?: Nullable<number> = null
    // 活动总限量，null表示不限
    total: Nullable<number> = null
    // 每人限量，null表示不限
    totalPerMan: Nullable<number> = null
    // 赠礼
    gift: Nullable<GiftInfo> = new GiftInfo()
    // 卡类型
    cardTemplateType: Nullable<string> = null
    // 次卡次数
    count: Nullable<number> = null
    validityInfo: Nullable<ValidityInfo> = new ValidityInfo()
}