import ApiClient from "http/ApiClient"
import Response from 'model/common/Response'
import MemberBlacklist from "model/promotion/bigWheelActivity/MemberBlacklist"
import MemberBlacklistFilter from "model/promotion/bigWheelActivity/MemberBlacklistFilter"
import SaveMemberBlacklistRequest from "model/promotion/bigWheelActivity/SaveMemberBlacklistRequest"

export default class MemberBlacklistApi {
  /**
   * 批量删除会员黑名单
   * 批量删除会员黑名单。
   * 
   */
  static batchRemove(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/member/blacklist/batch/remove`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量导出会员黑名单
   * 批量导出会员黑名单。
   * 
   */
  static exportMemberBlacklist(body: MemberBlacklistFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/member/blacklist/exportMemberBlacklist`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量导入会员黑名单
   * 批量导入会员黑名单。
   * 
   */
  static importMemberBlacklist(body: any): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/member/blacklist/importMemberBlacklist`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询会员黑名单
   * 查询会员黑名单。
   * 
   */
  static query(body: MemberBlacklistFilter): Promise<Response<MemberBlacklist[]>> {
    return ApiClient.server().post(`/v1/member/blacklist/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除会员黑名单
   * 删除会员黑名单。
   * 
   */
  static remove(id: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/member/blacklist/remove`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存会员黑名单
   * 保存会员黑名单。
   * 
   */
  static save(body: SaveMemberBlacklistRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/member/blacklist/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
