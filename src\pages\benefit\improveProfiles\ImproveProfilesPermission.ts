import PermissionMgr from "mgr/PermissionMgr";

export default class ImproveProfilesPermission {
  prefix = '/营销/营销/会员成长/完善资料有礼'
  
  get editable() {
    return PermissionMgr.hasOptionPermission(this.prefix, '活动维护');
  }

  get auditable() {
    return PermissionMgr.hasOptionPermission(this.prefix, '活动审核');
  }

  get terminable() {
    return PermissionMgr.hasOptionPermission(this.prefix, '活动终止');
  }

  get viewable() {
    return PermissionMgr.hasOptionPermission(this.prefix, '活动查看');
  }
}