import {Component} from 'vue-property-decorator'
import AbstractSelectDialog from './AbstractSelectDialog'
import PointsActivityApi from 'http/points/activity/PointsActivityApi'
import PointsActivityFilter from "model/points/activity/PointsActivityFilter";
import DateUtil from "util/DateUtil";
import ActivityState from "cmp/activitystate/ActivityState";
import GoodsGainPointsSpeedActivityApi from "http/points/activity/goodsgainpointsspeed/GoodsGainPointsSpeedActivityApi";
import GoodsScopeDtl from "cmp/goodsscope/GoodsScopeDtl.vue";

@Component({
  name: 'PointsActivitySelectorDialog',
  components: {
    ActivityState,
    GoodsScopeDtl,
  }
})
export default class PointsActivitySelectorDialog extends AbstractSelectDialog<any> {
  filter: PointsActivityFilter = new PointsActivityFilter()
  timeRange: Array<Nullable<Date>> = [null, null]

  goodsDialog: {
    visible: boolean,
    activity: any
  } = {
    visible: false,
    activity: {}
  }

  created() {
    this.filter.typeEquals = 'GOODS_GAIN_POINTS_SPEED'
  }

  reset() {
    Object.assign(this.filter, new PointsActivityFilter())
    this.timeRange = []
    this.filter.typeEquals = 'GOODS_GAIN_POINTS_SPEED'
  }

  getId(ins: any): string {
    return ins.activityId;
  }

  getName(ins: any): string {
    return ins.name;
  }

  getResponseData(response: any): any {
    return response.data.list
  }

  queryFun(): Promise<any> {
    let filter = JSON.parse(JSON.stringify(this.filter))
    filter.page = this.page.currentPage - 1
    filter.pageSize = this.page.size
    if (this.timeRange && this.timeRange[1]) {
      let endDate = this.timeRange[1]
      endDate.setHours(23)
      endDate.setMinutes(59)
      endDate.setSeconds(59)
      filter.end = DateUtil.format(endDate)
    }
    if (this.timeRange && this.timeRange[0]) {
      filter.begin = DateUtil.format(this.timeRange[0])
    }
    if (filter.stateEquals === null) {
      filter.stateNotEquals = 'STOPED'
    }
    return PointsActivityApi.query(filter)
  }

  showGoodsDialog(type: string, activityId: string) {
    if (type === 'GOODS_GAIN_POINTS_SPEED') { // 商品满额积分加倍
      GoodsGainPointsSpeedActivityApi.detail(activityId).then((res: any) => {
        if (res.code === 2000) {
          if (res.data.activityBody && res.data.activityBody.activityId) {
            Object.assign(this.goodsDialog.activity, res.data)
            this.goodsDialog.visible = true
          } else {
            this.$message.error(this.formatI18n('/公用/公共组件/积分活动选择弹框组件/提示/活动不存在'))
          }
        }
      })
    }
  }
}
