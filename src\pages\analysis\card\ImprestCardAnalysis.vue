<template>
  <div class="imprest-card-analysis-container">
    <BreadCrume :panelArray="panelArray">
    </BreadCrume>
    <div class="sum-amount">
      <div class="search-block1">
        <div>
          {{ i18n('可用余额') }}
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              <div style="color: #36445A;line-height: 20px;font-size: 13px;">
                <div style="font-size: 15px;margin-bottom:8px">{{ i18n('/储值/预付卡/电子礼品卡活动/效果评估/指标说明') }}</div>
                <div>
                  {{ i18n('可用余额：截止昨日，会员可用余额总和') }}；<br />
                </div>
              </div>
            </div>
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </div>
        <div style="font-size: 20px">
          <B>{{ getEndUsableTotalAmount }}</B>
        </div>
      </div>
      <div class="search-block1">
        <div>
          <!--          {{i18n('/储值/预付卡/电子礼品卡活动/效果评估/可用积分')}}-->
          {{ i18n('可用本金') }}
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              <div style="color: #36445A;line-height: 20px;font-size: 13px;">
                <div style="font-size: 15px;margin-bottom:8px">{{ i18n('/储值/预付卡/电子礼品卡活动/效果评估/指标说明') }}</div>
                <div>
                  {{ i18n('可用本金：截止昨日，会员可用本金总和') }}；<br />
                </div>
              </div>
            </div>
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </div>
        <div style="font-size: 20px">
          <B>{{ getEndUsableAmount }}</B>
        </div>
      </div>
      <div class="search-block1">
        <div>
          <!--          {{i18n('/储值/预付卡/电子礼品卡活动/效果评估/可用积分')}}-->
          {{ i18n('可用赠金') }}
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              <div style="color: #36445A;line-height: 20px;font-size: 13px;">
                <div style="font-size: 15px;margin-bottom:8px">{{ i18n('/储值/预付卡/电子礼品卡活动/效果评估/指标说明') }}</div>
                <div>
                  {{ i18n('可用赠金：截止昨日，会员可用赠金总和') }}；<br />
                </div>
              </div>
            </div>
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </div>
        <div style="font-size: 20px">
          <B>{{ getEndUsableGiftAmount }}</B>
        </div>
      </div>
    </div>
    <div class="search-block">
      <MyQueryCmp @reset="doReset" @search="onSearch" :showExpand="false">
        <el-row>
          <el-col :span="16">
            <AnalysisDateSelector ref="analysisDateSelector" :label="i18n('日期粒度')" @change="doDateChange">
            </AnalysisDateSelector>
          </el-col>
          <el-col :span="8">
            <FormItem :label="i18n('门店')">
              <SelectStores v-model="filter.store" :isOnlyId="true" :hideAll="false" width="100%"
                :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
              </SelectStores>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <FormItem :label="i18n('/公用/公共组件/卡模板选择弹框组件/查询/卡模板')">
              <el-input :placeholder="i18n('/储值/预付卡/卡模板/编辑页面校验/请输入卡模板号')" v-model="filter.templateId" />
            </FormItem>
          </el-col>
        </el-row>
      </MyQueryCmp>
    </div>
    <!-- 数据概览 -->
    <div class="overview-block" v-if="detailSum">
      <div class="overview-title">
        <div>
          {{ i18n('/储值/预付卡/电子礼品卡活动/效果评估/数据概览') }}
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              <div style="color: #36445A;line-height: 20px;font-size: 13px;">
                <div style="font-size: 15px;margin-bottom:8px">{{ i18n('/储值/预付卡/电子礼品卡活动/效果评估/指标说明') }}</div>
                <div>
                  {{ i18n('买卡：查询条件下，买卡金额，包括本金和赠金金额') }}；<br />
                  {{ i18n('转出：查询条件下，卡的钱转到会员储值账户，包括本金和赠金金额') }}；<br />
                  {{ i18n('作废：查询条件下，作废卡后卡的钱清零，包括本金和赠金金额') }}；<br />

                  {{ i18n('可用余额：截止查询日期结束日期，所有充值卡 总可用余额，可用余额等于可用本金+可用赠金') }}；<br />
                  {{ i18n('可用本金：截止查询日期结束日期，所有充值卡 期初+本金增加-本金减少') }}；<br />
                  {{ i18n('可用赠金：截止查询日期结束日期，所有充值卡 期初+赠金增加-赠金减少') }}；<br />
                </div>
              </div>
            </div>
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </div>
        <div>
          <el-button style="margin-right: 20px" v-if="hasOptionPermission('/数据/资产/会员充值卡分析', '数据导出')" @click="doExport"
            size="large" type="primary">
            {{ formatI18n('/储值/预付卡/充值卡制售单/列表页面', '导出') }}
          </el-button>
        </div>
      </div>
      <div class="overview-info">
        <div class="info-item" v-for="(item, index) in summaryViewArr" :key="index">
          <div class="item-title">{{ item.label }}</div>
          <div class="item-number">{{ item.value }}</div>
        </div>
      </div>
    </div>

    <div class="chart-block">
      <MemberLineChart :legendNames="legendNames" :xAxisArray="xAxisArray" :dateType="detail.dateUnit"
        :valueArray="valueArray" :showPercentName="showPercentName">
      </MemberLineChart>
    </div>
    <DownloadCenterDialog :dialogvisiable="downloadCenterFlag" :showTip="true" @dialogClose="doDownloadDialogClose">
    </DownloadCenterDialog>
  </div>
</template>

<script lang="ts" src="./ImprestCardAnalysis.ts">
</script>

<style lang="scss" scoped>
.imprest-card-analysis-container {
  width: 100%;
  padding-bottom: 30px;
  overflow: auto;

  .search-block {
    width: 100%;
    border-radius: 8px;
    background-color: #fff;
    padding: 24px;
  }

  .sum-amount {
    height: 100px;
    display: flex;
    justify-content: space-between;

    .search-block1 {
      display: flex;
      justify-content: space-between;
      width: 33%;
      border-radius: 8px;
      background-color: #fff;
      padding: 24px;
      margin-bottom: 20px;
    }
  }

  .overview-block {
    width: 100%;
    border-radius: 8px;
    background-color: #fff;
    padding: 24px 0 0 24px;
    margin-top: 16px;

    .overview-title {
      display: flex;
      align-items: center;
      font-weight: 600;
      font-family: PingFangSC, PingFang SC;
      font-size: 16px;
      color: #111111;
      line-height: 24px;
      margin-bottom: 20px;
      justify-content: space-between;

      .el-icon-warning-outline {
        cursor: pointer;
        margin-left: 4px;

        &:hover {
          color: #2878ff;
        }
      }
    }

    .overview-info {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
    }

    .info-item {
      width: 250px;
      margin-bottom: 24px;

      .item-title {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #898fa3;
        line-height: 20px;
        text-align: left;
      }

      .item-number {
        font-family: DINAlternate, DINAlternate;
        font-weight: bold;
        font-size: 20px;
        color: #111111;
        line-height: 24px;
        text-align: left;
        margin-top: 2px;
      }

      .item-trend {
        display: flex;
        align-items: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #969799;
        line-height: 17px;
        margin: 12px 4px 0 0;
      }
    }
  }

  .chart-block {
    padding: 0px 24px 24px;
    background: #ffffff;
    border-radius: 8px;
    margin-top: 16px;
  }
}
</style>