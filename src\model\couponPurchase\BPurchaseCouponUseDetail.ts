import PurchaseCouponTradeLine from './PurchaseCouponTradeLine'

// 购券交易使用明细记录
export default class BPurchaseCouponUseDetail {
  // 交易号
  tradeId: Nullable<string> = null
  // 活动号
  activityNumber: Nullable<string> = null
  // 转赠数量
  presentCount: Nullable<number> = null
  // 已用券数
  usedCount: Nullable<number> = null
  // 过期券数
  expiredCount: Nullable<number> = null
  // 已用金额
  usedAmount: Nullable<number> = null
  // 应退金额
  refundAmount: Nullable<number> = null
  // 会员id
  memberId: Nullable<string> = null
  // 当前时间
  occurredTime: Nullable<Date> = null
  // 活动分摊的购券金额
  purchaseAmount: Nullable<number> = null
  // 购券数量
  total: Nullable<number> = null
  // 交易命名空间
  tradeIdNamespace: Nullable<string> = null
  // tradeUuid
  tradeUuid: Nullable<string> = null
  // 活动类型
  activityType: Nullable<string> = null
  // 活动子类型
  subType: Nullable<string> = null
  // 活动名
  activityName: Nullable<string> = null
  /**
   * 活动开始时间
   * id存number
   */
  activityStartTime: Nullable<Date> = null
  /**
   * 活动结束时间
   * id存number
   */
  activityEndTime: Nullable<Date> = null
  // 交易明细行
  lines: PurchaseCouponTradeLine[] = []
}
