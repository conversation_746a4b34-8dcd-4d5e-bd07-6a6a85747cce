import { ApportionType } from "./ApportionType"
import { CouponPayAmountType } from "./CouponPayAmountType"
import SpecialGoodsAmount from "./SpecialGoodsAmount"


export default class SubjectApportion {
  // 券记录方式：FAV——优惠方式； PAY——支付方式； COLLOCATION——组合方式；
  subjectApprotionType: Nullable<string> = null
  // 记录类型：PROPORTION——按比例； AMOUNT——按金额；
  recordType: Nullable<ApportionType> = null
  // 优惠比例
  favValue: Nullable<number> = null
  // 支付比例
  payValue: Nullable<number> = null
  // 券支付金额方式
  amountPayments: CouponPayAmountType[] = []
  // 承担方
  parties: string[] = []
  // 特殊商品设置
  specialGoodsAmounts: SpecialGoodsAmount[] = []
}