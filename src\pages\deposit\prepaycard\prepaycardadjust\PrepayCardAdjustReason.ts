import {Component, Vue} from 'vue-property-decorator'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import FormItem from 'cmp/formitem/FormItem.vue'
import AdjustBillReason from 'model/prepay/adjustbill/AdjustBillReason'
import StoreValueAdjustReasonAdd from 'pages/deposit/prepaycard/prepaycardadjust/dialog/StoreValueAdjustReasonAdd.vue'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import CustomizeContentApi from 'http/customizecontent/CustomizeContentApi'
import CustomizeContentFilter from 'model/customizecontent/CustomizeContentFilter'
import I18nPage from "common/I18nDecorator";

@Component({
  name: 'StoreValueAdjustReason',
  components: {
    SubHeader,
    FormItem,
    StoreValueAdjustReasonAdd,
    BreadCrume
  }
})
@I18nPage({
  prefix: [
    '/储值/预付卡/预付卡调整单/预付卡调整原因设置', '/公用/按钮', '/公用/提示'
  ],
})
export default class PrepayCardAdjustReason extends Vue {
  i18n: (str: string, params?: string[]) => string
  panelArray: any
  query: CustomizeContentFilter = new CustomizeContentFilter()
  reason = ''
  data: any = ''
  searchContent = ''
  $refs: any
  dialogShow = false
  selectedArr: AdjustBillReason[] = []
  tableData: AdjustBillReason[] = []
  recordTableCount = 0
  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  created() {
    this.panelArray = [
      {
        name: this.i18n('预付卡调整单'),
        url: 'prepay-card-adjust'
      },
      {
        name: this.i18n('预付卡调整原因设置'),
        url: ''
      }
    ]
    this.getStoreValueAdjustReason()
  }
  doAddReason() {
    if (!this.reason) {
      this.$message.warning(this.i18n('原因不能为空'))
      this.$refs.reason.focus()
      return
    }
    if (this.recordTableCount < 20) {
      // this.recordTableCount++
      this.submitStoreValueAdjustReason()
    } else {
      this.$message.warning(this.i18n('最多只能创建20条原因'))
      return;
    }

  }
  doClear() {
    this.reason = ''
    this.getStoreValueAdjustReason()
  }
  doDialogClose() {
    this.dialogShow = false
    this.getStoreValueAdjustReason()
  }
  doBatchDelete() {
    if (this.selectedArr.length <= 0) {
      this.$message.warning(this.i18n('请先勾选要删除的预付卡调整原因'))
      return
    }
    this.$confirm(this.i18n('确认批量删除?'), this.i18n('提示'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消'),
      type: 'warning'
    }).then(() => {
      let arr: string[] = []
      this.selectedArr.forEach((item: any) => {
        arr.push(item.id)
      })
      CustomizeContentApi.remove(arr).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message({
            type: 'success',
            message: this.i18n('批量删除成功!')
          })
          this.getStoreValueAdjustReason()
        }
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    })
  }
  doSearchByKey() {
    let obj: CustomizeContentFilter = new CustomizeContentFilter()
    obj.type = 'cardAdjustReason'
    obj.page = 0
    obj.pageSize = this.page.size
    obj.reasonLikes = this.searchContent
    CustomizeContentApi.list(obj).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.tableData = resp.data
        this.page.total = resp.total
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  doDelete(row: any) {
    this.$confirm(this.i18n('确认删除?'), this.i18n('/公用/提示/提示'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消'),
      type: 'warning'
    }).then(() => {
      CustomizeContentApi.remove([row.id]).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message({
            type: 'success',
            message: this.i18n('删除成功!')
          })
          this.getStoreValueAdjustReason()
        }
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    })
  }
  doEdit(row: any) {
    this.data = row
    this.dialogShow = true
  }
  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.getStoreValueAdjustReason()
  }
  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.currentPage = 1
    this.page.size = val
    this.getStoreValueAdjustReason()
  }
  handleSelectionChange(val: any) {
    this.selectedArr = val
  }
  private getStoreValueAdjustReason() {
    this.query.page = this.page.currentPage - 1
    this.query.pageSize = this.page.size
    this.query.type = 'cardAdjustReason'
    CustomizeContentApi.list(this.query).then((resp: any) => {
      if (resp && resp.data) {
        this.tableData = resp.data
        this.page.total = resp.total
        this.recordTableCount = resp.total
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  private submitStoreValueAdjustReason() {
    CustomizeContentApi.save(this.reason, 'cardAdjustReason').then((resp: any) => {
      if (resp && resp.data) {
        this.$message.success(this.i18n('新建成功'))
        this.reason = ''
        this.getStoreValueAdjustReason()
      } else {
        throw new Error(resp.msg)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  private submitBatchDelete() {
    // PrePayAdjustBillApi.
  }
}
