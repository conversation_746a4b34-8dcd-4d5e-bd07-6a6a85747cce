import { Component } from "vue-property-decorator";
import FormItem from "cmp/formitem/FormItem.vue";
import AbstractSelectDialog from "./AbstractSelectDialog";
import MarketingCenterApi from "http/marketingcenter/MarketingCenterApi";
import RSMarketingCenterFilter from "model/common/RSMarketingCenterFilter";
import RSMarketingCenter from "model/common/RSMarketingCenter";
import store from "store/global";

@Component({
	name: "PromotionCenterSelectorDialog",
	components: {
		FormItem,
	},
})
// todo  这里需要换类型 RSOrg
export default class PromotionCenterSelectorDialog extends AbstractSelectDialog<RSMarketingCenter> {
	orgFilter: RSMarketingCenterFilter = new RSMarketingCenterFilter();

	reset() {
		this.orgFilter = new RSMarketingCenterFilter();
	}

	// todo 这里需要换类型
	getId(ins: RSMarketingCenter): string {
		// @ts-ignore
		return ins.marketingCenter.id;
	}

	getName(ins: RSMarketingCenter): string {
		// @ts-ignore
		return ins.marketingCenter.name;
	}

	getResponseData(response: any): any {
		return response.data;
	}

	// todo 这里需要换Api
	queryFun(): Promise<any> {
		this.orgFilter.page = this.page.currentPage - 1;
		this.orgFilter.pageSize = this.page.size;
		this.orgFilter.marketingCenterIdOrder = true;
		// return MarketingCenterApi.query(this.orgFilter)
		const account = JSON.parse(sessionStorage.getItem("vuex") as string)?.loginInfo.user?.account;

		return MarketingCenterApi.queryUserMarketCenter(account);
	}
}
