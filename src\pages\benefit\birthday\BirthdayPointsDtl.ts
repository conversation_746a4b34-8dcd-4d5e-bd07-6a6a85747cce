import {Component, Vue} from 'vue-property-decorator'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import BirthdayBenefitApi from 'http/benefit/BirthdayBenefitApi'
import PointsRule from 'model/benefit/PointsRule'
import FormItem from 'cmp/formitem/FormItem.vue'

@Component({
  name: 'BirthdayPointsDtl',
  components: {
    BreadCrume,
    FormItem
  }
})
export default class BirthdayPointsDtl extends Vue {
  $refs: any
  panelArray: any = []
  pointsRule: PointsRule = new PointsRule()
  pointsItems: any = []

  created() {
    this.panelArray = [
      {
        name: this.formatI18n('/公用/菜单', '节日有礼'),
        url: 'score-init'
      },
			{
				name: this.formatI18n("/权益/生日权益初始化/生日权益初始化", "生日积分加倍"),
				url: "",
			},
		];
  }

  mounted() {
    this.getRule()
  }

  doModify() {
    this.$router.push({name: 'birthday-points-add', query: {from: 'edit'}})
  }

  getPointsTimes(value: any) {
    if (value) {
      let str = this.formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/会员生日权益', '享{0}倍积分')
      // 每月 号给当月过生日的会员送礼包
      return str.replace(/\{0\}/g, value);
    }
    return ''
  }

  switchState() {
    BirthdayBenefitApi.switchRule(true)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/详情/启用禁用/修改成功'))
        }
        this.getRule()
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
    })
  }

  get getLabel() {
    let label = (sessionStorage.getItem('locale') === 'zh_CN') ? "：" : ":"
    return this.formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍', '会员生日权益').concat(label)
  }

  private getRule() {
    BirthdayBenefitApi.pointsRuleDetail()
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.pointsRule = resp.data
          if (this.pointsRule.differentPointsTimes) {
            this.getGradePoints()
          }
        }
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
    })
  }

  private getGradePoints() {
    if (this.pointsRule && this.pointsRule.differentPointsTimes) {
      let check = []
      this.pointsItems = []
      if (this.pointsRule.gradeList) {
        let gradeList = this.pointsRule.gradeList;
        for (let key of gradeList) {
          let v = this.pointsRule.differentPointsTimes[key.code as string]
          if (v) {
            let data: any = {
              name: key.name,
              value: v
            }
            check.push(key.code)
            this.pointsItems.push(data)
          }
        }
      }

      for (let key in this.pointsRule.differentPointsTimes) {
        if (!check.includes(key)) {
          let data: any = {
            name: key,
            value: this.pointsRule.differentPointsTimes[key]
          }
          this.pointsItems.push(data)
        }
      }
    }
  }
}