export default class MemberPersonalDataConfigLine {
  // 排序号
  sequence: Nullable<number> = null
  // 是否系统定义
  systemDefined: Nullable<boolean> = null
  // 字段名称
  fieldName: Nullable<string> = null
  // 字段格式 [text,date,select,image]
  fieldType: Nullable<string> = null
  // 字段选项
  fieldOptions: string[] = []
  // 默认选中字段
  defaultFieldOption: Nullable<string> = null
  // 提示文案
  tips: Nullable<string> = null
  // 是否必填
  required: Nullable<boolean> = null
  // 是否可修改
  modifiable: Nullable<boolean> = null
  // 启用状态
  enabled: Nullable<boolean> = null
  // 字段来源 custom-自定义 system-系统
  source: Nullable<'custom' | 'system'> = null
  // 自定义字段名称
  customFieldName: Nullable<string> = null
}