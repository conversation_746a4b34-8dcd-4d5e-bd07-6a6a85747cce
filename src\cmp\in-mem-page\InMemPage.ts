/*
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:02
 * @LastEditTime: 2023-06-30 11:40:29
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\in-mem-page\InMemPage.ts
 * 记得注释
 */
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'

@Component({
  name: 'InMemPage'
})
export default class InMemPage extends Vue {
  @Prop({
    type: Array,
    default: () => []
  })
  data: []
  @Prop()
  query: any
  @Prop()
  sort: any
  @Prop({ type: Boolean, default: true })
  isShowTotal: Boolean;
  filter: any = {}
  currentPage: any[] = []
  page: any = {
    currentPage: 1,
    total: 0,
    size: 40,
    records: []
  }

  @Watch("data", { deep: true, immediate: true })
  watchData() {
    this.buildPage(this.data)
  }

  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.currentPage = this.page.records[val - 1]
  }

  doSearch() {
    this.buildPage(this.query(this.data, this.filter))
  }

  doReset() {
    this.filter.codeName = ''
    this.buildPage(this.data)
  }

  private buildPage(data: any[]) {
    if (this.sort) {
      // 排序会修改绑定对象，需要深拷贝
      data = JSON.parse(JSON.stringify(data)).sort(this.sort)
    }
    this.page.total = data.length
    let totalPage = Math.ceil(data.length / this.page.size)
    this.page.records = []
    for (let i = 0; i < totalPage; i++) {
      let start = i * this.page.size
      let end = Math.min(start + this.page.size, data.length)
      this.page.records.push(data.slice(start, end))
    }
    this.page.currentPage = 1
    this.currentPage = this.page.records[0]
  }
}
