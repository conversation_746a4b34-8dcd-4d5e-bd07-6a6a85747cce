export default class PrepayAccountFilter {
  //
  ownerIdEquals: Nullable<string> = null
  //
  ownerNamespaceEquals: Nullable<string> = null
  //
  nameEquals: Nullable<string> = null
  //
  memberCodeLikes: Nullable<string> = null
  //
  amountEgt: Nullable<number> = null
  //
  amountElt: Nullable<number> = null
  //
  stateEquals: Nullable<string> = null
  //
  openTimeOrder: Nullable<boolean> = null
  //
  page: Nullable<number> = null
  //
  pageSize: Nullable<number> = null
}
