<!--
 * @Author: 黎钰龙
 * @Date: 2023-07-27 11:40:26
 * @LastEditTime: 2024-04-24 15:20:51
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\coupontenplate\FormItemCmp\RecordWay\RecordWay.vue
 * 记得注释
-->
<template>
  <div>
    <el-form ref="form" :model="ruleForm" :rules="rules">
      <el-form-item :label="formatI18n('/公用/券模板', '用券记录方式')" required :label-width="labelWidth">
        <div v-if="copyFlag === 'edit' && !baseFieldEditable">
          <div v-if="ruleForm.recordWay === 'FAV'">{{ formatI18n('/公用/券模板', '优惠方式') }}</div>
          <div v-else-if="ruleForm.recordWay === 'PAY'">{{ formatI18n('/公用/券模板', '支付方式') }}</div>
          <div v-else>{{ formatI18n('/公用/券模板', '组合方式') }},<span v-html="getFavValue(ruleForm.discountWay, ruleForm.payWay)"></span>
            <br>
            <el-button @click="viewSpecialGoods" v-if="specialGoods && specialGoods.length" type="text">
              查看特殊商品
            </el-button>
          </div>
        </div>
        <div v-else>
          <el-radio-group v-model="ruleForm.recordWay" @change="doRecordWayChange">
            <el-radio label="FAV">{{ formatI18n('/公用/券模板', '优惠方式') }}</el-radio>
            <el-radio label="PAY">{{ formatI18n('/公用/券模板', '支付方式') }}</el-radio>
            <el-radio label="COLLOCATION">{{ formatI18n('/公用/券模板', '组合方式') }}</el-radio>
          </el-radio-group>
          <div class="cur_record" v-if="ruleForm.recordWay === 'COLLOCATION'">
            <el-radio-group v-model="ruleForm.recordType" @change="doFormItemChange">
              <el-radio-button label="PROPORTION">{{ formatI18n('/公用/券模板', '按比例') }}</el-radio-button>
              <el-radio-button label="AMOUNT">{{ formatI18n('/公用/券模板', '按金额') }}</el-radio-button>
            </el-radio-group>
            <!-- 按比例 -->
            <div v-if="ruleForm.recordType === 'PROPORTION'">
              <div style="color: #79879E">{{ formatI18n('/营销/券礼包活动/核销第三方券', '若未升级jpos版本,则设置的比例不生效') }}</div>
              <el-form-item prop="discountWay" style="display: inline-block">
                <el-input size="mini" @change="doDiscountWay" style="width: 120px" v-model="ruleForm.discountWay">
                  <template slot="append">%</template>
                </el-input>
              </el-form-item>
              {{ formatI18n('/公用/券模板', '优惠方式') }}&nbsp; + &nbsp;
              <el-form-item prop="payWay" style="display: inline-block">
                <el-input size="mini" @change="doPayWay" style="width: 120px" v-model="ruleForm.payWay">
                  <template slot="append">%</template>
                </el-input>
              </el-form-item>
              {{ formatI18n('/公用/券模板', '支付方式') }}
            </div>
            <div v-else>
              <el-form-item prop="amountPayments" style="display: inline-block">
                <el-checkbox-group style="margin-top: 10px" @change="changeAmountPayments" v-model="ruleForm.amountPayments">
                  <el-checkbox label="CUSTOM_AMOUNTS">
                     {{i18n('自定义金额')}}
                  </el-checkbox>
                  <el-input size="mini" @change="doPayWayByAmount" style="width: 100px;margin-left: -20px; margin-top: -5px" v-model="ruleForm.payWay">
                    <template slot="append">{{i18n('元')}}</template>
                  </el-input>
                  <el-checkbox style="margin-left: 30px" label="BUY_COUPON_AMOUNTS">{{i18n('购券金额')}}</el-checkbox>
                  <el-checkbox v-if="showBearAmounts" label="BEAR_AMOUNTS">
                    {{i18n('承担方承担金额')}}
                  </el-checkbox>
                  <el-select class="record_way_bear_select" v-model="ruleForm.parties" @change="doChangeSelect" multiple style="width: 200px;margin-left: -20px;text-overflow: ellipsis;white-space: nowrap;" v-if="showBearAmounts && showPartSelect">
                    <el-option v-for="item in getCanSelectParty" :key="item.costParty.id" :label="item.costParty.name" :value="item.costParty.id"></el-option>
                  </el-select>
                </el-checkbox-group>
<!--                <el-input size="mini" @change="doPayWayByAmount" style="width: 120px" v-model="ruleForm.payWay">-->
<!--                  <template slot="append">{{i18n('元')}}</template>-->
<!--                </el-input>-->
              </el-form-item>
              <div>
                &nbsp;{{i18n('以上勾选的金额之和记为券支付金额，剩余算优惠')}}
              </div>
<!--              <div style="display:inline-block;margin-left:5px">-->
<!--                <i18n k="/公用/券模板/已设置{0}个特殊商品">-->
<!--                  <span style="color:#FFAA00;font-weight: 600;" slot="0">{{ruleForm.specialGoods.length}}</span>-->
<!--                </i18n>-->
<!--                <el-button type="text" @click="setSpecialGoods" style="margin-left:5px">-->
<!--                  <span v-if="!ruleForm.specialGoods.length">{{ i18n('选择商品') }}</span>-->
<!--                  <span v-else>{{ i18n('修改') }}</span>-->
<!--                </el-button>-->
<!--              </div>-->
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>
<!--     设置特殊商品 -->
    <SelectGoodsDialog ref="SelectGoodsDialog" :title="i18n('设置')" @submit="specialGoodsSubmit"></SelectGoodsDialog>
    <!-- 查看特殊商品 -->
<!--    <SpecialGoodsDialog ref="SpecialGoodsDialog" :data="ruleForm.specialGoods"></SpecialGoodsDialog>-->
  </div>
</template>

<script lang="ts" src="./RecordWay.ts">
</script>

<style lang="scss" scoped>
.cur_record {
  padding: 0 8px 8px;
  background-color: #f7f9fc;
  max-width: 700px;
}
.el-radio-button .el-radio-button__inner {
  display: flex;
  align-items: center;
  height: 28px !important;
}

//::v-deep .record_way_bear_select .el-select__tags-text {
//  display: inline-flex;
//  max-width: 125px;
//  overflow: hidden;
//  white-space: nowrap;
//  text-overflow: ellipsis;
//}

::v-deep .record_way_bear_select .el-select__tags-text {
  display: inline-block;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

::v-deep .el-select .el-tag__close.el-icon-close {
  top: -7px;
}

</style>