import ApiClient from 'http/ApiClient'
import GiftCardFilter from 'model/prepay/report/card/GiftCardFilter'
import GiftCardCardHst from 'model/prepay/report/card/GiftCardCardHst'
import Response from 'model/common/Response'
import CardReportSum from 'model/prepay/report/card/CardReportSum'
import GiftCardConsumeOrRefundHst from 'model/prepay/report/card/GiftCardConsumeOrRefundHst'

export default class RechargeCardReportApi {
  /**
   * @description 充值卡售卡流水
   * @static
   * @param {GiftCardFilter} body
   * @return {*}  {Promise<Response<GiftCardCardHst[]>>}
   * @memberof RechargeCardReportApi
   */
  static queryCardHst(body: GiftCardFilter): Promise<Response<GiftCardCardHst[]>> {
    return ApiClient.server().post(`/v1/prepay/card/report/imprestCard/hst/query`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * @description 充值卡退卡流水
   * @static
   * @param {GiftCardFilter} body
   * @return {*}  {Promise<Response<GiftCardCardHst[]>>}
   * @memberof RechargeCardReportApi
   */
  static queryRefundCardHst(body: GiftCardFilter): Promise<Response<GiftCardCardHst[]>> {
    return ApiClient.server().post(`/v1/prepay/card/report/imprestCard/refund/hst/query`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * @description 充值卡售卡流水汇总
   * @static
   * @param {GiftCardFilter} body
   * @return {*}  {Promise<Response<CardReportSum>>}
   * @memberof RechargeCardReportApi
   */
  static cardHstSum(body: GiftCardFilter): Promise<Response<CardReportSum>> {
    return ApiClient.server().post(`/v1/prepay/card/report/imprestCard/hst/sum`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * @description 充值卡售卡流水报表导出
   * @static
   * @param {GiftCardFilter} body
   * @return {*}  {Promise<Response<GiftCardConsumeOrRefundHst[]>>}
   * @memberof RechargeCardReportApi
   */
  static exportSalesHst(body: GiftCardFilter): Promise<Response<GiftCardConsumeOrRefundHst[]>> {
    return ApiClient.server().post(`/v1/prepay/card/report/imprestCard/exportSales`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * @description 充值卡退卡流水报表导出
   * @static
   * @param {GiftCardFilter} body
   * @return {*}  {Promise<Response<GiftCardConsumeOrRefundHst[]>>}
   * @memberof RechargeCardReportApi
   */
  static exportRefundCard(body: GiftCardFilter): Promise<Response<GiftCardConsumeOrRefundHst[]>> {
    return ApiClient.server().post(`/v1/prepay/card/report/imprestCard/exportRefundCard`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * @description 充值卡余额转出流水
   * @static
   * @param {GiftCardFilter} body
   * @return {*}  {Promise<Response<GiftCardCardHst[]>>}
   * @memberof RechargeCardReportApi
   */
  static queryConsumeCardHst(body: GiftCardFilter): Promise<Response<GiftCardCardHst[]>> {
    return ApiClient.server().post(`/v1/prepay/card/report/imprestCard/consume/hst/query`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * @description 充值卡余额转出流水汇总
   * @static
   * @param {GiftCardFilter} body
   * @return {*}  {Promise<Response<CardReportSum>>}
   * @memberof RechargeCardReportApi
   */
  static consumeCardHstSum(body: GiftCardFilter): Promise<Response<CardReportSum>> {
    return ApiClient.server().post(`/v1/prepay/card/report/imprestCard/consume/hst/sum`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * @description 充值卡余额转出流水报表导出
   * @static
   * @param {GiftCardFilter} body
   * @return {*}  {Promise<Response<GiftCardConsumeOrRefundHst[]>>}
   * @memberof RechargeCardReportApi
   */
  static exportConsumeHst(body: GiftCardFilter): Promise<Response<GiftCardConsumeOrRefundHst[]>> {
    return ApiClient.server().post(`/v1/prepay/card/report/imprestCard/exportConsume`, body, {}).then((res) => {
      return res.data
    })
  }
}