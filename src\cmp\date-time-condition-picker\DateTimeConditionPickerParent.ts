import {Vue} from 'vue-property-decorator'

export default class DateTimeConditionPicker extends Vue {
  i18nPrefix = ''

  translateWeek(i: number) {
    switch (i) {
      case 1:
        return this.formatI18n('/公用/券模板', '周一')
      case 2:
        return this.formatI18n('/公用/券模板', '周二')
      case 3:
        return this.formatI18n('/公用/券模板', '周三')
      case 4:
        return this.formatI18n('/公用/券模板', '周四')
      case 5:
        return this.formatI18n('/公用/券模板', '周五')
      case 6:
        return this.formatI18n('/公用/券模板', '周六')
      case 7:
        return this.formatI18n('/公用/券模板', '周日')
    }
  }
}
