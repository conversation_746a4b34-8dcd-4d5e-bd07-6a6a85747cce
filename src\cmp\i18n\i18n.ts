import {Component, Prop, Vue, Watch} from 'vue-property-decorator';
import I18nTool from "common/I18nTool";
import EnvUtil from "util/EnvUtil";

class Item {
  slotName = ''
  prefix: Nullable<string> = null
  suffix: Nullable<string> = null
}

@Component({
  name: 'i18n',
  components: {},
})
export default class I18n extends Vue {
  @Prop()
  k: string
  hasNamedSlot = false

  items: Item[] = []
  itemsObj: any

  created() {
    this.hasNamedSlot = Object.keys(this.$slots).length > 0
  }

  @Watch('k', {immediate: true})
  watchKey() {
    let res = null
    let content = I18nTool.match(this.k, [], [], EnvUtil.debug)
    this.items = []
    while (res = /\{\w+}/gi.exec(content)) {
      let item = new Item()
      let split = content.split(res + '')
      item.prefix = split[0]
      item.suffix = this.getSuffix(split[1])
      content = split[1]
      item.slotName = (res + '').replace('{', '').replace('}', '')
      this.items.push(item)
    }
    this.buildItemsObj()
  }

  buildItemsObj() {
    this.itemsObj = {}
    for (let item of this.items) {
      if (isNaN(Number.parseFloat(item.slotName + ''))) {
        this.itemsObj[item.slotName] = item
      } else {
        this.itemsObj['s' + item.slotName] = item
      }
    }
  }

  private getSuffix(content: string) {
    let res = /\{\w+}/gi.exec(content)
    if (res) {
      return content.split(res + '')[0]
    }
    return content
  }
}