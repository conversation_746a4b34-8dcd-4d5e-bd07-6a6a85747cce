<template>
  <div class="page-member-complete-profile-edit page-member-code-edit">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button size="large" type="primary"
          v-if="hasOptionPermission('/设置/小程序装修/页面管理', '编辑') && hasOptionPermission('/设置/小程序装修/页面管理', '发布')"
          @click="preserve(true)">
          {{ i18n("保存并发布") }}
        </el-button>
        <el-button size="large" @click="preserve(false)">{{ i18n("/公用/按钮/保存") }}</el-button>
        <el-button size="large" @click="goBack">{{ i18n("取消") }}</el-button>
      </template>
    </BreadCrume>
    <div class="panel-header">
      <div class="score-header">
        <el-tabs class="main-tabs" v-model="activeTab" style="width: 100%" @tab-click="changeTab">
          <el-tab-pane :label="i18n('完善资料有礼')" name="gift">
          </el-tab-pane>
          <el-tab-pane :label="i18n('赠礼弹窗')" name="window">
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <!-- 设置 -->
    <div class="panel">
      <div class="panel-left">
        <el-form :model="ruleForm" :rules="rules" ref="form" label-width="140px">
          <!-- 页面设置 -->
          <div class="backDecoration">
            <div class="title">{{ i18n("页面设置") }}</div>
            <div v-if="activeTab === 'gift'" class="content">
              <el-form-item :label="i18n('页面名称')" prop="name">
                <el-input v-model="ruleForm.name" maxlength="8" style="width: 300px" />
              </el-form-item>
              <el-form-item :label="i18n('顶部背景')" prop="propTopBackgroundImage">
                <div class="gray-tips">
                  <i18n k="/公用/券模板/建议尺寸{0}像素，支持{1}，大小不超过{2}">
                    <template slot="0">750*240</template>
                    <template slot="1">jpg/jpeg/png</template>
                    <template slot="2">1M</template>
                  </i18n>
                </div>
                <UploadImg v-model="ruleForm.propTopBackgroundImage" @change="uploadImgChange('propTopBackgroundImage')"
                  :isShowKb="false" :maximum="1"></UploadImg>
              </el-form-item>
              <el-form-item :label="i18n('赠礼角标')" prop="propGiftIcon">
                <div class="gray-tips">
                  <i18n k="/公用/券模板/建议尺寸{0}像素，支持{1}，大小不超过{2}">
                    <template slot="0">32*32</template>
                    <template slot="1">jpg/jpeg/png</template>
                    <template slot="2">1M</template>
                  </i18n>
                </div>
                <UploadImg v-model="ruleForm.propGiftIcon" @change="uploadImgChange('propGiftIcon')" :isShowKb="false"
                  :maximum="1"></UploadImg>
              </el-form-item>
            </div>
            <div v-else class="content">
              <el-form-item :label="i18n('弹窗赠礼背景')" prop="propPopGiftBackgroundImage">
                <div class="gray-tips">
                  <i18n k="/公用/券模板/建议尺寸{0}像素，支持{1}，大小不超过{2}">
                    <template slot="0">750*240</template>
                    <template slot="1">jpg/jpeg/png</template>
                    <template slot="2">1M</template>
                  </i18n>
                </div>
                <UploadImg v-model="ruleForm.propPopGiftBackgroundImage"
                  @change="uploadImgChange('propPopGiftBackgroundImage')" :isShowKb="false" :maximum="1"></UploadImg>
              </el-form-item>
              <el-form-item :label="i18n('赠礼图标')" prop="propCouponIcon">
                <div class="gray-tips">
                  <i18n k="/公用/券模板/建议尺寸{0}像素，支持{1}，大小不超过{2}">
                    <template slot="0">32*32</template>
                    <template slot="1">jpg/jpeg/png</template>
                    <template slot="2">1M</template>
                  </i18n>
                </div>
                <div class="giftIcon">
                  <div class="giftIcon-item">
                    <UploadImg v-model="ruleForm.propCouponIcon" @change="uploadImgChange('propCouponIcon')"
                      :isShowKb="false" :maximum="1"></UploadImg>
                    <span class="giftIcon-text">{{ i18n('/会员/等级管理/优惠券') }}</span>
                  </div>
                  <div class="giftIcon-item">
                    <UploadImg v-model="ruleForm.propScoreIcon" @change="uploadImgChange('propCouponIcon')"
                      :isShowKb="false" :maximum="1"></UploadImg>
                    <span class="giftIcon-text">{{ i18n('/会员/会员资料/积分') }}</span>
                  </div>
                  <div class="giftIcon-item">
                    <UploadImg v-model="ruleForm.propGrowthIcon" @change="uploadImgChange('propCouponIcon')"
                      :isShowKb="false" :maximum="1"></UploadImg>
                    <span class="giftIcon-text">{{ i18n('/会员/会员资料/成长值') }}</span>
                  </div>
                </div>
              </el-form-item>

              <el-form-item :label="i18n('赠礼背景')" prop="propGiftBgColor">
                <div class="giftBgColor">
                  <span class="giftBgColor-text">{{ i18n('背景色') }}</span>
                  <el-color-picker style="width: 100px" v-model="ruleForm.propGiftBgColor" @change="changeGiftBgColor"
                    size="small"></el-color-picker>
                </div>
                <div class="giftBgColor">
                  <span class="giftBgColor-text">{{ i18n('文字颜色') }}</span>
                  <el-color-picker style="width: 100px" v-model="ruleForm.propGiftBgFontColor"
                    @change="changeGiftBgFontColor" size="small"></el-color-picker>
                </div>
              </el-form-item>
            </div>
          </div>
        </el-form>
      </div>
      <div class="panel-right">
        <div class="pageBox page-place">
          <div style="height: 20px;background-color: #fff;"></div>
          <div class="header-bar" style="display: flex;justify-content: space-between;background-color: #fff;">
            <i class="iconfont ic-left"></i>
            <el-tooltip v-if="showAllGiftTips" :value="true" class="item" effect="dark" :content="i18n('页面名称')" placement="top">
              <div>{{ ruleForm.name }}</div>
            </el-tooltip>
            <el-tooltip v-else-if="isGiftTabs" class="item" effect="dark" :content="i18n('页面名称')"  placement="top">
              <div>{{ ruleForm.name }}</div>
            </el-tooltip>
            <div v-else>{{ ruleForm.name }}</div>
            <div>
                  <img style="width: 65px; height: 24px;" src="@/assets/image/setting/completeProfile/ic-share.png" />
            </div>
          </div>

          <div v-if="ruleForm.propGiftIcon">
            <el-tooltip v-if="showAllGiftTips" :value="true" class="item" effect="dark" :content="i18n('顶部背景')"
              placement="bottom">
              <img style="width: 100%; height: 120px;background-size: 100%;"
                :src="ruleForm.propTopBackgroundImage" />
            </el-tooltip>
            <el-tooltip v-else-if="isGiftTabs" class="item" effect="dark" :content="i18n('顶部背景')" placement="bottom">
              <img style="width: 100%; height: 120px;background-size: 100%;"
                :src="ruleForm.propTopBackgroundImage" />
            </el-tooltip>
            <img v-else style="width: 100%; height: 120px;background-size: 100%;"
              :src="ruleForm.propTopBackgroundImage" />
          </div>

          <div class="content-item">
            <ul>
              <li>
                <div>{{ i18n('/设置/系统设置/头像') }}</div>
                <div>
                  <img style="width: 30px; height: 30px;" src="@/assets/image/setting/completeProfile/SketchPng.png" />
                </div>
              </li>
              <li>
                <div>{{ i18n('/会员/会员资料/会员号') }}</div>
                <div>34324234234</div>
              </li>
              <li>
                <div>{{ i18n('/会员/会员资料/昵称') }}</div>
                <div>{{ i18n('微信昵称') }}</div>
              </li>
              <li>
                <div class="item-label">
                  <el-tooltip v-if="ruleForm.propGiftIcon" class="item" effect="dark" :content="i18n('赠礼角标')"
                    placement="bottom">
                    <img :src="ruleForm.propGiftIcon" />
                  </el-tooltip>
                  <span class="icon-important-after">{{ i18n('/会员/会员资料/姓名') }}</span>
                </div>
                <div class="text-gray">{{ i18n('请输入') }}</div>
              </li>
              <li>
                <div class="item-label">
                  <template v-if="ruleForm.propGiftIcon">
                    <el-tooltip v-if="showAllGiftTips" :value="true" class="item" effect="dark" :content="i18n('赠礼角标')"
                      placement="bottom">
                      <img :src="ruleForm.propGiftIcon" />
                    </el-tooltip>
                    <el-tooltip v-else-if="isGiftTabs" class="item" effect="dark" :content="i18n('赠礼角标')"
                      placement="bottom">
                      <img :src="ruleForm.propGiftIcon" />
                    </el-tooltip>
                    <img v-else :src="ruleForm.propGiftIcon" />
                  </template>
                  {{ i18n('/设置/系统设置/生日') }}
                </div>
                <div class="text-gray">
                  {{ i18n('/公用/菜单/请选择') }}
                  <i class="iconfont ic-right"></i>
                </div>
              </li>
              <li>
                <div class="item-label">
                  <el-tooltip v-if="ruleForm.propGiftIcon" class="item" effect="dark" :content="i18n('赠礼角标')"
                    placement="bottom">
                    <img :src="ruleForm.propGiftIcon" />
                  </el-tooltip>
                  {{ i18n('/设置/系统设置/性别') }}
                </div>
                <div class="text-gray">
                  {{ i18n('/公用/菜单/请选择') }}
                  <i class="iconfont ic-right"></i>
                </div>
              </li>
              <li>
                <div>{{ i18n('/公用/菜单/身份证号') }}</div>
                <div class="text-gray">
                  {{ i18n('请完善身份信息') }}
                  <i class="iconfont ic-right"></i>
                </div>
              </li>
            </ul>
          </div>
          <div class="btn-confirm">
            {{ i18n('/公用/按钮/确认修改') }}
          </div>
          <div class="bottom-bar"></div>

          <template v-if="!isGiftTabs">
            <div class="bg-cover"></div>
            <div class="pop-gift">
              <div class="popGiftBackgroundImage">
                <el-tooltip v-if="showAllWindowTips" :value="true" class="item" effect="dark" :content="i18n('赠礼弹窗背景')"
                  placement="top">
                  <img :src="ruleForm.propPopGiftBackgroundImage" />
                </el-tooltip>
                <el-tooltip v-else class="item" effect="dark" :content="i18n('赠礼弹窗背景')" placement="top">
                  <img :src="ruleForm.propPopGiftBackgroundImage" />
                </el-tooltip>
              </div>
              <div class="gift-icon-list">
                <ul>
                  <li :style="{ background: ruleForm.propGiftBgColor ? ruleForm.propGiftBgColor : '#FF3B3D' }">
                    <div :style="{ color: ruleForm.propGiftBgFontColor ? ruleForm.propGiftBgFontColor : '#fff' }">
                      <el-tooltip v-if="showAllWindowTips" :value="true" class="item" effect="dark"
                        :content="i18n('赠礼图标')" placement="top">
                        <div class="icon-container">
                          <img :src="ruleForm.propScoreIcon" />
                        </div>
                      </el-tooltip>
                      <el-tooltip v-else class="item" effect="dark" :content="i18n('赠礼图标')" placement="top">
                        <div class="icon-container">
                          <img :src="ruleForm.propScoreIcon" />
                        </div>
                      </el-tooltip>
                      {{ i18n('/会员/会员资料/积分') }}
                    </div>

                    <el-tooltip v-if="showAllWindowTips" :value="true" class="item" effect="dark"
                      :content="i18n('赠礼背景色')" placement="top">
                      <div style="flex:1 ;height: 100%"></div>
                    </el-tooltip>
                    <el-tooltip v-else class="item" effect="dark" :content="i18n('赠礼背景色')" placement="top">
                      <div style="flex:1 ;height: 100%"></div>
                    </el-tooltip>

                    <el-tooltip v-if="showAllWindowTips" :value="true" class="item" effect="dark"
                      :content="i18n('赠礼文案颜色')" placement="top">
                      <div :style="{ color: ruleForm.propGiftBgFontColor ? ruleForm.propGiftBgFontColor : '#fff' }">
                        12000</div>
                    </el-tooltip>
                    <el-tooltip v-else class="item" effect="dark" :content="i18n('赠礼文案颜色')" placement="top">
                      <div :style="{ color: ruleForm.propGiftBgFontColor ? ruleForm.propGiftBgFontColor : '#fff' }">
                        12000</div>
                    </el-tooltip>
                  </li>
                  <li :style="{ background: ruleForm.propGiftBgColor ? ruleForm.propGiftBgColor : '#FF3B3D' }">
                    <div :style="{ color: ruleForm.propGiftBgFontColor ? ruleForm.propGiftBgFontColor : '#fff' }">
                      <div class="icon-container">
                        <img :src="ruleForm.propGrowthIcon" />
                      </div>
                      {{ i18n('/会员/会员资料/成长值') }}
                    </div>
                    <el-tooltip class="item" effect="dark" :content="i18n('赠礼背景色')" placement="top">
                      <div style="flex:1 ;height: 100%"></div>
                    </el-tooltip>
                    <div></div>
                  </li>
                  <li :style="{ background: ruleForm.propGiftBgColor ? ruleForm.propGiftBgColor : '#FF3B3D' }">
                    <div :style="{ color: ruleForm.propGiftBgFontColor ? ruleForm.propGiftBgFontColor : '#fff' }">
                      <div class="icon-container">
                        <img :src="ruleForm.propCouponIcon" />
                      </div>
                      {{ i18n('/会员/等级管理/优惠券') }}
                    </div>
                    <el-tooltip class="item" effect="dark" :content="i18n('赠礼背景色')" placement="top">
                      <div style="flex:1 ;height: 100%"></div>
                    </el-tooltip>
                    <div></div>
                  </li>
                </ul>
              </div>
              <div class=""
                style="bottom: 0;position: absolute; background-color:#fff; width: 100%;border-top: 1px solid #eeeeee;">
                <div class="btn-confirm" style="opacity: 1;height: 32px;line-height: 32px;">
                  {{ i18n('/公用/按钮/确认修改') }}
                </div>
                <div class="bottom-bar" style="bottom: 10px;"></div>
              </div>
            </div>
          </template>
        </div>

        <div class="show-tips">
          <span class="show-tips-text">{{ i18n('字段位置提示') }}</span>
          <el-switch style="display: block" v-model="showTips" active-color="#007EFF"
            inactive-color="#D7DFEB "></el-switch>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./PageMemberCompleteProfileEdit.ts"></script>

<style lang="scss" scoped>
.page-member-complete-profile-edit {
  width: 100%;

  .panel {
    border-radius: 8px;
    display: flex;
    height: calc(100% - 120px);

    .panel-left {
      width: 850px;
      min-height: 720px;
      height: 100%;
      margin-right: 12px;
      padding: 26px 20px 40px;
      margin-bottom: 12px;
      border-radius: 8px;
      background: #ffffff;

      .giftIcon {
        display: flex;

        &-item {
          margin-left: 16px;
          text-align: center;
        }

        &-text {
          &:before {
            content: "*";
            color: #ef393f;
            margin-right: 4px;
          }
        }
      }

      .giftBgColor {
        display: flex;
        align-items: center;

        &-text {
          min-width: 80px;
        }
      }
    }

    .panel-right {
      flex-shrink: 0;
      position: sticky;
      top: 0;
      /* 吸附到页面顶部 */
      border-radius: 8px;
      background: #ffffff;
      padding: 30px 25px;
      width: 350px;
      height: 720px;
      box-sizing: border-box;

      .icon-important-after {
        &:after {
          content: "*";
          color: #ef393f;
          margin-left: 4px;
        }
      }

      .pageBox {
        width: 100%;
        height: calc(100% - 30px);
        background: #f0f2f6;
        border: 8px solid #000000;
        border-radius: 50px;
        box-sizing: border-box;
        overflow: hidden;
        overflow: auto;

        &::-webkit-scrollbar {
          display: none;
          /* 隐藏滚动条 */
        }

        .bottom-bar {
          position: relative;
          bottom: -50px;
          left: 100px;
          width: 101px;
          height: 4px;
          background: #2c3036;
          border-radius: 2px;
        }
      }

      .page-place {
        position: relative;

        .bg-img {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          width: 100%;
          height: 100%;
          object-fit: cover;
          z-index: 1;
        }
      }

      .content-item {
        background-color: #fff;
        padding: 10px;
        padding-bottom: 0;

        ul {
          li {
            height: 36px;
            line-height: 36px;
            color: #333333;

            &:not(:last-child) {
              border-bottom: 1px solid #eeeeee;
            }

            display: flex;
            justify-content: space-between;

            .text-gray {
              color: #9f9f9f;
            }

            .item-label {
              img {
                width: 20px;
                height: 20px;
                position: relative;
                top: 5px;
              }
            }
          }
        }
      }

      .btn-confirm {
        margin: 20px 12px;
        height: 30px;
        line-height: 30px;
        background: linear-gradient(#ff3b3d 0%, #ff5413 100%);
        border-radius: 15px;
        opacity: 0.4;
        text-align: center;
        color: #fff;
      }

      .bg-cover {
        width: 100%;
        height: 100%;
        background-color: #636465;
        position: absolute;
        top: 0;
        z-index: 10;
        opacity: 0.5;
      }

      .pop-gift {
        background-color: #fff;
        width: 100%;
        height: calc(100% - 190px);
        position: absolute;
        // top: 190px;
        bottom: 0;
        z-index: 11;

        .popGiftBackgroundImage {
          img {
            width: 100%;
            max-height: 240px;
            background-size: 100%;
          }
        }

        .gift-icon-list {
          position: absolute;
          top: 120px;
          width: 100%;

          ul {
            margin: 20px;
            list-style: none;
            width: calc(100% - 40px);

            li {
              height: 44px;
              //     background: ;
              box-shadow: 0px 12px 24px -11px rgba(255, 59, 61, 0.72);
              border-radius: 6px;
              margin-bottom: 10px;
              color: #fff;
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding-left: 12px;
              padding-right: 12px;

              .icon-container {
                display: inline-block;
                width: 20px;
                height: 20px;
                position: relative;
                top: 5px;
                margin-right: 8px;
                background: #fff;
                border-radius: 50%;

                img {
                  width: 100%;
                  height: 100%;
                  border-radius: 50%;
                }
              }
            }
          }
        }
      }
    }

    .show-tips {
      margin: 20px;
      text-align: center;
      font-size: 13px;
      line-height: 20px;
      color: #36445a;
      display: flex;
      justify-content: center;

      &-text {
        margin-right: 8px;
      }
    }
  }

  .panel-header {
    .score-header {
      width: 100%;
      display: flex;
      justify-content: flex-start;

      // padding-bottom: 20px;
      .header-block {
        margin: 25px 0 0 30px;
        padding: 0 0 20px;
      }

      .header-bottom {
        color: #333333;
        border-bottom: 2px solid #333333;
      }

      ::v-deep .main-tabs {
        .el-tabs__nav-wrap::after {
          display: none;
        }

        &>.el-tabs__header {
          display: flex;
          align-items: center;
          background-color: #ffffff;
          height: 56px;
          border-radius: 8px;
          padding-top: 6px;
          margin-bottom: 12px;

          .el-tabs__item.is-active {
            color: #007eff !important;
            font-weight: 600;
          }

          .el-tabs__item {
            font-weight: 400;
          }
        }

        &>.el-tabs__content {
          background-color: #ffffff;
          border-radius: 8px;
        }
      }
    }
  }

  .header-bar {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 20px;
    height: 44px;
    z-index: 2;
    font-size: 20px;
    font-weight: 500;
    color: #262626;
  }
}
</style>
