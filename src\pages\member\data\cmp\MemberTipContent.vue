<script lang="ts">
import { Vue, Component, Prop, Watch } from "vue-property-decorator";

@Component({})
export default class MemberTipContent extends Vue {
  @Prop()
  content: string;

  hasEllipsis: boolean = false;

  @Watch("content", { immediate: true })
  onContentChange() {
    this.$nextTick(() => {
      const el = this.$refs.content;
      // @ts-ignore
      this.hasEllipsis = el.offsetWidth < el.scrollWidth;
    });
  }
}
</script>

<template>
  <el-tooltip placement="top-start"
              :content="content"
              :disabled="!hasEllipsis">
    <div ref="content"
         class="text-overflow-ellipsis">{{ content }}
    </div>
  </el-tooltip>
</template>

<style scoped
       lang="scss">

</style>