<template>
  <el-dialog width="640px" :close-on-click-modal="false" :visible.sync="visible" append-to-body>
    <FormItem :label="titleList[index]" v-for="(item,index) in list" :key="index">
      <CustomRangeForm v-model="list[index]" ref="customRangeForm" :editable="editable">
      </CustomRangeForm>
    </FormItem>
    <div style="text-align: right; margin: 0" v-if="editable">
      <el-button size="mini" type="text" @click="visible = false">{{ i18n("/公用/按钮/取消") }}</el-button>
      <el-button @click="submitSelectGroupSelections" type="primary" size="mini">{{ i18n("/公用/按钮/确定") }}</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./CustomRangeDialog.ts">
</script>

<style lang="scss" scoped>
</style>