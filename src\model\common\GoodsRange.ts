/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-04-26 17:54:36
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\common\GoodsRange.ts
 * 记得注释
 */
import IdName from 'model/common/IdName'
import GoodsCodeInfo from 'model/common/GoodsCodeInfo'

export default class GoodsRange {
  // 是否限制商品范围
  limit: Nullable<boolean> = null
  // 是否为不适用前置，不适用前置+exclude代表不适用的且条件
  excludePrecondition: Nullable<boolean> = null
  // 单品属于
  includeGoods: IdName[] = []
  // 单品不属于
  excludeGoods: IdName[] = []
  // 品牌属于
  includeBrands: IdName[] = []
  // 品牌不属于
  excludeBrands: IdName[] = []
  // 品类属于
  includeCategories: IdName[] = []
  // 品类不属于
  excludeCategories: IdName[] = []
  // 各条件间的逻辑：可选值（and / or）
  relation: string = 'and'
  // 商品标签属于
  includeTags: IdName[] = []
  // 商品标签不属于
  excludeTags: IdName[] = []
  // 商品代码属于
  includeCodes: GoodsCodeInfo[] = []
  // 商品代码不属于
  excludeCodes: GoodsCodeInfo[] = []

  static isEmpty(goodsRange: GoodsRange) {
    return !goodsRange.includeGoods.length && !goodsRange.excludeGoods.length && !goodsRange.includeBrands.length && !goodsRange.excludeBrands.length && !goodsRange.includeCategories.length && !goodsRange.excludeCategories.length 
    && !goodsRange.includeTags.length && !goodsRange.excludeTags.length && !goodsRange.includeCodes.length && !goodsRange.excludeCodes.length
  }
}