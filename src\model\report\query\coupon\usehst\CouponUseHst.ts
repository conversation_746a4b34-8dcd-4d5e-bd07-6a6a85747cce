/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2023-06-13 15:22:30
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\report\query\coupon\usehst\CouponUseHst.ts
 * 记得注释
 */
import MemberIdent from 'model/common/member/MemberIdent'
import CostPartyInfo from '../costPartyInfos'

export default class CouponUseHst extends MemberIdent {
  // 用券日期
  useDate: Nullable<Date> = null
  // 用券门店
  useStore: Nullable<string> = null
  // 券名称
  name: Nullable<string> = null
  // 券码
  code: Nullable<string> = null
  // 券摘要
  summary: Nullable<string> = null
  // 交易金额
  amount: Nullable<number> = null
  // 抵扣金额
  deductAmount: Nullable<number> = null
  // 交易号
  tranNo: Nullable<string> = null
  // 活动代码
  activityNumber: Nullable<string> = null
  // 活动名称
  activityName: Nullable<string> = null
  // 券模板号
  templateNumber: Nullable<string> = null
  // 用券渠道名称
  channelName: Nullable<string> = null
  // 承担方信息
  costPartyInfos: CostPartyInfo[] = []
  // 用券会员uuid
  occurredMemberId: Nullable<string> = null
  // 用券会员手机号
  occurredMobile: Nullable<string> = null
  // 用券会员号
  occurredCrmCode: Nullable<string> = null
  // 券类型
  couponType: Nullable<string> = null

}