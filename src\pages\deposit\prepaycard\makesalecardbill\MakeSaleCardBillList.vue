<template>
  <div class="make-sale-card-bill-list">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button v-if="hasOptionPermission('/卡/卡管理/制售单', '单据新建')" type="primary" @click="add">新建制售单</el-button>
      </template>
    </BreadCrume>
    <div class="current-page">
      <div class="query">
        <el-form label-width="100px" style="margin-right: 30px">
          <el-row >
              <el-col :span="6">
                <el-form-item label="单号">
                  <el-input v-model="query.billNumberEquals" :placeholder="i18n('请输入单号')"/>
                </el-form-item>
              </el-col>
            <el-col :span="6">
              <el-form-item :label="i18n('状态')">
                <el-select placeholder="不限" v-model="query.stateEquals" style="width:100%">
                  <el-option :label="i18n('全部')" :value="null"></el-option>
                  <el-option :label="i18n('未审核')" value="INITIAL"></el-option>
                  <el-option :label="i18n('已审核')" value="AUDITED"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
              <el-col :span="6">
                <el-form-item label="卡模板">
                  <el-input placeholder="请输入卡模板号或名称" v-model="query.cardTemplateNumberEqualsOrCardTemplateNameEquals"/>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="卡面额">
                  <el-input v-model="query.cardFaceAmountEquals"/>
                </el-form-item>
              </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label=" ">
                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button @click="doReset">重置</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <el-row class="table">
        <el-table
          :data="queryData"
          style="width: 100%;"
        >
          <el-table-column
            label="制售单单号"
            fixed
            prop="billNumber"
            width="200"
          >
            <template slot-scope="scope">
              <el-button no-i18n type="text" @click="gotoDtl(scope.row)">{{scope.row.billNumber}}</el-button>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('状态')" width="120">
            <template slot-scope="scope">
              <div style="display:flex;align-items:center">
                <span class="dot" :style="{background: computeState(scope.row.state).color}"></span>
                <span>{{computeState(scope.row.state).state}}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            :label="i18n('创建时间')"
            prop="created"
            width="150"
          >
            <template slot-scope="scope">
              <span >{{formatData(scope.row.created)}}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="i18n('最后修改时间')"
            prop="lastModified"
            width="150"
          >
            <template slot-scope="scope">
              <span no-i18n>{{formatData(scope.row.lastModified)}}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="i18n('创建人')"
            prop="creator"
            width="140"
          >
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.creator}}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="i18n('最后修改人')"
            prop="lastModifier"
            width="140"
          >
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.lastModifier}}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="卡模板"
            prop="cardTemplate"
            width="200"
          >
            <template slot-scope="scope">
              <el-button no-i18n type="text" @click="gotoTplDtl(scope.row)">{{scope.row.cardTemplateName}}</el-button>
            </template>
          </el-table-column>
          <el-table-column
            :label="i18n('/卡/卡管理/售卡单/卡面额/次数')"
            prop="cardTemplate"
            width="140"
          >
            <template slot-scope="scope">
              <template v-if="scope.row.cardCount !== null">{{scope.row.cardCount}}{{i18n('/营销/券礼包活动/券礼包活动/次')}}</template>
              <template v-else-if="scope.row.cardFaceAmount !== null">{{scope.row.cardFaceAmount}}{{i18n('元')}}</template>
            </template>
          </el-table-column>
          <el-table-column
            :label="i18n('/储值/预付卡/充值卡制售单/编辑页面/制售数量')"
            prop="cardTemplate"
            width="140"
          >
            <template slot-scope="scope">
              {{scope.row.makeSaleQty}}{{i18n('张')}}
            </template>
          </el-table-column>
          <el-table-column
            :label="i18n('操作')"
            prop="cardTemplate"
            width="140"
          >
            <template slot-scope="scope">
              <div v-if="scope.row.state === 'INITIAL'">
                <el-button type="text" @click="audit(scope.row.billNumber)" v-if="hasOptionPermission('/卡/卡管理/制售单', '单据审核')">审核</el-button>
                <el-button type="text" @click="remove(scope.row.billNumber)" v-if="hasOptionPermission('/卡/卡管理/制售单', '单据维护')">删除</el-button>
              </div>
              <div v-if="scope.row.state === 'AUDITED'">
                <el-button type="text"  @click="queryCard(scope.row)">卡查询</el-button>
                <el-button type="text" @click="exportCard(scope.row)" v-if="hasOptionPermission('/卡/卡管理/制售单', '卡密导出')">导出</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-pagination
          no-i18n
        :current-page="page.currentPage"
        :page-size="page.size"
        :page-sizes="[10, 20, 30, 40]"
        :total="page.total"
        @current-change="onHandleCurrentChange"
        @size-change="onHandleSizeChange"
        background
        layout="total, prev, pager, next, sizes,  jumper"
        class="pagin"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script lang="ts" src="./MakeSaleCardBillList.ts">
</script>

<style lang="scss">
.make-sale-card-bill-list {
  background-color: white;
  height: 100%;
  width: 100%;
  .total {
    margin: 20px;
  }

  .current-page {
    height: calc(100% - 48px);
    overflow: auto;
    .query {
      margin-top: 25px;
      .el-row {
        background-color: white;
      }
    }

    .el-select {
      width: 100%;
    }

    .row {
      padding: 20px 20px 0 20px;
    }

    .table {
      padding: 0 20px 20px 20px;
    }

    .el-form-item {
      margin-bottom: 0;
    }
  }
}
</style>
