import ApiClient from "http/ApiClient";
import CouponWriteoffBill from "model/coupon/CouponWriteOff/CouponWriteoffBill";
import Response from "model/common/Response";
import CouponWriteoffFilter from "model/coupon/CouponWriteOff/CouponWriteOffFilter";
import CouponWriteOffRequest from "model/coupon/CouponWriteOff/CouponWriteOffRequest";

export default class CouponWriteoffBillApi {
  /**
   * 单号查询核销单
   * 单号查询核销单
   *
   */
  static detail(number: string): Promise<Response<CouponWriteoffBill>> {
    return ApiClient.server()
      .get(`/v1/couponWriteoffBill/detail`, {
        params: {
          number: number,
        },
      })
      .then((res) => {
        return res.data;
      });
  }
  /**
   * 券核销单列表
   *
   */
  static query(
    params: CouponWriteoffFilter
  ): Promise<Response<CouponWriteoffBill[]>> {
    return ApiClient.server()
      .post(`/v1/couponWriteoffBill/query`, params)
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 券核销单列表总计
   *
   */
   static statistics(
    params: CouponWriteoffFilter
  ): Promise<Response<any>> {
    return ApiClient.server()
      .post(`/v1/couponWriteoffBill/statistics`, params)
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 券核销单新增
   *
   */
  static create(params: CouponWriteOffRequest): Promise<Response<any>> {
    return ApiClient.server()
      .post(`/v1/couponWriteoffBill/saveNew`, params)
      .then((res) => {
        return res.data;
      });
  }
  /**
   * 券核销单审核
   *
   */
  static audit(number: string): Promise<Response<any>> {
    return ApiClient.server()
      .post(`/v1/couponWriteoffBill/effect`, {
        number: number
      })
      .then((res) => {
        return res.data;
      });
  }
  /**
   * 券核销单新增
   *
   */
   static getLog(number: string): Promise<Response<CouponWriteoffBill[]>> {
    return ApiClient.server()
      .get(`/v1/couponWriteoffBill/operatorLog`, {
        params: {
          number: number,
        },
      })
      .then((res) => {
        return res.data;
      });
  }
}
