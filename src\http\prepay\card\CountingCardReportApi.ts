import ApiClient from 'http/ApiClient'
import CardReportSum from 'model/prepay/report/card/CardReportSum'
import Response from 'model/common/Response'
import GiftCardCardHst from 'model/prepay/report/card/GiftCardCardHst'
import GiftCardFilter from 'model/prepay/report/card/GiftCardFilter'
import PrepayCardExchangeGoodsFilter from 'model/prepay/report/card/PrepayCardExchangeGoodsFilter'
import PrepayCardExchangeGoods from 'model/prepay/report/card/PrepayCardExchangeGoods'

export default class CountingCardReportApi {
  /**
   * 售卡流水汇总
   * 售卡流水汇总。
   * 
   */
  static cardHstSum(body: GiftCardFilter): Promise<Response<CardReportSum>> {
    return ApiClient.server().post(`/v1/prepay/counting/card/report/card/hst/sum`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 消费流水导出
   * 消费流水导出。
   * 
   */
  static exportConsumeHst(body: GiftCardFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/prepay/counting/card/report/exportConsume`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 退款流水导出
   * 退款流水导出。
   * 
   */
  static exportRefundHst(body: GiftCardFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/prepay/counting/card/report/exportRefund`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 售卡流水导出
   * 售卡流水导出。
   * 
   */
  static exportSalesHst(body: GiftCardFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/prepay/counting/card/report/exportSales`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 退卡流水导出
   * 退卡流水导出。
   *
   */
  static exportRefundCardHst(body: GiftCardFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/prepay/counting/card/report/exportRefundCard`, body, {
    }).then((res) => {
      return res.data
    })
  }


  /**
   * 售卡流水
   * 售卡流水。
   * 
   */
  static queryCardHst(body: GiftCardFilter): Promise<Response<GiftCardCardHst[]>> {
    return ApiClient.server().post(`/v1/prepay/counting/card/report/card/hst/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 退卡流水
   * 退卡流水。
   *
   */
  static queryRefundCardHst(body: GiftCardFilter): Promise<Response<GiftCardCardHst[]>> {
    return ApiClient.server().post(`/v1/prepay/counting/card/report/refund/card/hst/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 消费流水
   * 消费流水。
   * 
   */
  static queryConsumeHst(body: GiftCardFilter): Promise<Response<GiftCardCardHst[]>> {
    return ApiClient.server().post(`/v1/prepay/counting/card/report/consume/hst/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 消费退款流水
   * 消费退款流水。
   * 
   */
  static queryConsumeRefundHst(body: GiftCardFilter): Promise<Response<GiftCardCardHst[]>> {
    return ApiClient.server().post(`/v1/prepay/counting/card/report/consume/refund/hst/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 兑换商品查询
   * 兑换商品查询。
   * 
   */
  static queryExchangeGoods(body: PrepayCardExchangeGoodsFilter): Promise<Response<PrepayCardExchangeGoods[]>> {
    return ApiClient.server().post(`/v1/prepay/counting/card/report/exchange/goods/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
