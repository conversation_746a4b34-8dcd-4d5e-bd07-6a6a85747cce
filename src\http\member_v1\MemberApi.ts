import ApiClient from 'http/ApiClient'
import Response from 'model/response/Response'
import MemberListResult from 'model/member/MemberListResult'
import MemberDetailResult from 'model/member/MemberDetailResult'
import MemberCountResult from 'model/member/MemberCountResult'
import <PERSON>zeenResult from 'model/member/FreezeenResult'

const qs = require('qs');
export default class MemberApi {

  static getMemberLevel(): Promise<Response<MemberListResult>> {

    return ApiClient.server().post(`mbr-web/member/memberGrade.hd`, {}, {

    }).then((res) => {
      return res.data
    })
  }
  static getMemberList(params: any, body: any): Promise<Response<MemberListResult>> {

    return ApiClient.server().post(`mbr-web/v2/member/query.hd`, body, {
      params: params
    }).then((res) => {
      return res.data
    })
  }
  static getMemberAccount(params: any): Promise<Response<MemberCountResult>> {

    return ApiClient.server().post(`mbr-web/member/getMemberAccount.hd`, qs.stringify(params), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }).then((res) => {
      return res.data
    })
  }
  static getHdcardCardNos(params: any): Promise<Response<MemberCountResult>> {

    return ApiClient.server().post(`mbr-web/member/getHdcardCardNos.hd`, qs.stringify(params), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }).then((res) => {
      return res.data
    })
  }
  static getCouponList(params: any): Promise<Response<[]>> {

    return ApiClient.server().post(`mbr-web/member/queryCoupons.hd`, qs.stringify(params), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }).then((res) => {
      return res.data
    })
  }
  static getMemberDetail(params: any): Promise<Response<MemberDetailResult>> {
    return ApiClient.server().post(`mbr-web/member/member.hd`, qs.stringify(params), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }).then((res) => {
      return res.data
    })
  }
  static freezeen(params: any): Promise<Response<FreezeenResult>> {
    return ApiClient.server().post(`mbr-web/member/freezeMember.hd`, qs.stringify(params), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }).then((res) => {
      return res.data
    })
  }
  static unFreezeen(params: any): Promise<Response<FreezeenResult>> {
    return ApiClient.server().post(`mbr-web/member/unfreezeMember.hd`, qs.stringify(params), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }).then((res) => {
      return res.data
    })
  }
  static editChildInfo(params: any): Promise<Response<FreezeenResult>> {
    return ApiClient.server().post(`mbr-web/member/member/updateChildren.hd`, params, {
    }).then((res) => {
      return res.data
    })
  }
  static editMember(params: any): Promise<Response<FreezeenResult>> {
    return ApiClient.server().post(`mbr-web/member/member/update.hd`, qs.stringify(params), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }).then((res) => {
      return res.data
    })
  }
}
