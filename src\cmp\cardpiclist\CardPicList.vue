<template>
  <div class="cardpic-list">
    <div class="pic" v-for="(pic, index) of picList">
      <div class="pic-content">
        <div v-if="!readonly" class="pic-cancel" @click="del(index)">×</div>
        <img :src="pic"/>
      </div>
      <div v-if="!readonly && index !== 0" class="pic-title" @click="forward(index)">{{formatI18n('/储值/预付卡/卡模板/编辑页面/前移')}}</div>
      <div v-if="readonly || index === 0" class="pic-title">&nbsp;</div>
    </div>
  </div>
</template>

<script lang="ts" src="./CardPicList.ts">
</script>

<style lang="scss" scoped>
.cardpic-list {
  display: flex;
  .pic {
    width: 100px;
    height: 120px;
    margin-right: 10px;
    display: inline-block;
    .pic-content {
      position: relative;
      width: 100px;
      height: 100px;
      background-color: #DDDDDD;
      .pic-cancel {
        position: absolute;
        right: 0;
        top: 0;
        color: white;
        background-color: black;
        font-size: 22px;
        line-height: 15px;
      }
      img {
        width: 100%;
        height: 100%;
      }
    }
    .pic-title {
      width: 100%;
      height: 30px;
      line-height: 30px;
      text-align: center
    }
  }
}
</style>