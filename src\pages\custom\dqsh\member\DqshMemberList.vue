<template>
  <div class="dqsh-member-list">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button @click="doBatchImport" v-if="hasOptionPermission('/会员/会员管理/会员资料（大桥石化）', '批量导入')">批量导入</el-button>
        <el-button @click="doBatchExport" v-if="hasOptionPermission('/会员/会员管理/会员资料（大桥石化）', '批量导出')">批量导出</el-button>
      </template>
    </BreadCrume>
    <ListWrapper class="current-page">
      <template slot="query">
        <MyQueryCmp @reset="onReset" @search="onSearch" @toggle="doToggle">
          <el-row>
            <el-col :span="16">
              <form-item label="会员">
                <el-input placeholder="请输入手机号/会员号/IC卡内部卡号/IC卡卡面卡号" v-model="query.identCodeEquals"></el-input>
              </form-item>
            </el-col>
            <el-col :span="8">
              <form-item label="姓名">
                <el-input placeholder="请输入姓名" v-model="query.nameLikes"></el-input>
              </form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <form-item label="会员等级">
                <el-select placeholder="全部" v-model="query.gradeEquals">
                  <el-option label="全部" value="">全部</el-option>
                  <el-option :label="item.name" :value="item.code" :key="item.code" v-for="item in memberLevel">[{{item.code}}]{{item.name}}</el-option>
                </el-select>
              </form-item>
            </el-col>
            <el-col :span="8">
              <form-item label="会员状态">
                <el-select placeholder="全部" v-model="query.stateEquals">
                  <el-option label="全部" value="">全部</el-option>
                  <el-option label="使用中" value="Using">使用中</el-option>
                  <el-option label="已冻结" value="Blocked">已冻结</el-option>
                  <el-option label="未激活" value="Unactivated">未激活</el-option>
                </el-select>
              </form-item>
            </el-col>
            <el-col :span="8">
              <form-item label="归属门店">
                <SelectStores v-model="query.ownerStoreIdEquals" :isOnlyId="true" :hideAll="false" width="100%"
                  :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
                </SelectStores>
              </form-item>
            </el-col>
          </el-row>
          <template slot="opened">
            <el-row>
              <el-col :span="8">
                <form-item label="注册渠道">
                  <el-select placeholder="全部" v-model="query.registerChannelEquals">
                    <el-option label="全部" value="">全部</el-option>
                    <el-option label="第三方" value="third">第三方</el-option>
                    <el-option label="支付宝" value="alipay">支付宝</el-option>
                    <el-option label="微信" value="weixin">微信</el-option>
                    <el-option label="门店注册" value="store">门店注册</el-option>
                    <el-option label="CRM" value="phoenix">CRM</el-option>
                  </el-select>
                </form-item>
              </el-col>
              <el-col :span="8">
                <form-item label="招募方式">
                  <el-select placeholder="全部" v-model="query.recruitmentMethodEquals">
                    <el-option label="全部" value="">全部</el-option>
                    <el-option label="支付即会员" value="支付即会员">支付即会员</el-option>
                    <el-option label="扫码即会员" value="扫码即会员">扫码即会员</el-option>
                    <el-option label="注册" value="注册">注册</el-option>
                    <el-option label="小程序" value="小程序">小程序</el-option>
                  </el-select>
                </form-item>
              </el-col>
              <el-col :span="8">
                <form-item label="邀请人会员">
                  <el-input placeholder="请输入邀请人手机号/会员号" v-model="query.refereeCodeEquals"></el-input>
                </form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <form-item label="注册日期">
                  <el-date-picker end-placeholder="结束日期" format="yyyy-MM-dd" range-separator="-" start-placeholder="开始日期" type="daterange"
                    v-model="registerDate" value-format="yyyy-MM-dd">
                  </el-date-picker>
                </form-item>
              </el-col>
              <el-col :span="8">
                <form-item label="激活日期">
                  <el-date-picker end-placeholder="结束日期" format="yyyy-MM-dd" range-separator="-" start-placeholder="开始日期" type="daterange"
                    v-model="activeDate" value-format="yyyy-MM-dd">
                  </el-date-picker>
                </form-item>
              </el-col>
              <el-col :span="8">
                <form-item label="创建日期">
                  <el-date-picker end-placeholder="结束日期" format="yyyy-MM-dd" range-separator="-" start-placeholder="开始日期" type="daterange"
                    v-model="createDate" value-format="yyyy-MM-dd">
                  </el-date-picker>
                </form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <form-item label="性别">
                  <el-select placeholder="不限" v-model="query.genderEquals">
                    <el-option label="不限" value="">不限</el-option>
                    <el-option label="男" value="男">男</el-option>
                    <el-option label="女" value="女">女</el-option>
                  </el-select>
                </form-item>
              </el-col>
              <el-col :span="8">
                <form-item label="生日">
                  <el-date-picker end-placeholder="结束日期" format="MM-dd" range-separator="-" start-placeholder="开始日期" type="daterange"
                    v-model="birthdayDate" value-format="MM-dd">
                  </el-date-picker>
                </form-item>
              </el-col>
              <el-col :span="8">
                <form-item label="邮箱">
                  <el-input placeholder="请输入邮箱" v-model="query.mailLikes"></el-input>
                </form-item>
              </el-col>
            </el-row>
            <!-- <el-row>
                            <el-col :span="8">
                                <form-item label="归属营销中心">
                                  <el-select
                                  value-key	= "id"
                                  v-model="fromMarketingCenter"
                                  :placeholder="formatI18n('/资料/渠道/请选择')"
                                  >
                                    <el-option
                                    :value="null"
                                    label="全部"
                                    >全部</el-option>
                                    <el-option
                                    v-for="(value,index) in marketingCenterData"
                                    :key="value.id"
                                    :value="value"
                                    :label="'['+value.id+']'+value.name"
                                    >[{{value.id}}]{{value.name}}</el-option>
                                  </el-select>
                                </form-item>
                            </el-col>
                        </el-row> -->
          </template>
        </MyQueryCmp>
      </template>
      <template slot="list">
        <el-table :data="tableData" style="width: 100%">
          <el-table-column fixed label="会员号" prop="crmCode" width="240">
            <template slot-scope="scope">
              <el-button @click="doGoDtl(scope.row)" type="text" v-if="hasOptionPermission('/会员/会员管理/会员资料（大桥石化）', '会员资料查看')">{{ scope.row.crmCode }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column fixed label="手机号" prop="mobile" width="110">
            <template slot-scope="scope">
              {{scope.row.mobile | strFormat}}
            </template>
          </el-table-column>
          <el-table-column label="姓名" prop="name" width="120">
            <template slot-scope="scope">
              {{scope.row.name | strFormat}}
            </template>
          </el-table-column>
          <el-table-column label="会员等级" prop="gradeName" width="120">
            <template slot-scope="scope">
              {{scope.row.gradeName | strFormat}}
            </template>
          </el-table-column>
          <el-table-column label="会员状态" prop="state" width="120">
            <template slot-scope="scope">
              <el-tag size="small" type="success" v-if="scope.row.state === 'Using'">使用中</el-tag>
              <el-tag size="small" type="danger" v-if="scope.row.state === 'Blocked'">已冻结</el-tag>
              <el-tag size="small" type="warning" v-if="scope.row.state === 'Unactivated'">未激活</el-tag>
              <el-tag size="small" type="info" v-if="scope.row.state === 'Canceled'">已注销</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="归属门店" prop="ownStore.name" width="270">
            <template slot-scope="scope">
              <el-tooltip v-if="scope.row.ownStore && scope.row.ownStore.id" class="item" effect="dark"
                :content="'['+scope.row.ownStore.id+']'+scope.row.ownStore.name" placement="right-end">
                <div style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">
                  [{{scope.row.ownStore.id}}]{{scope.row.ownStore.name}}
                </div>
              </el-tooltip>
              <el-tooltip v-else class="item" effect="dark" :content="'--'" placement="right-end">
                <div>--</div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="注册日期" prop="registerTime" width="150">
            <template slot-scope="scope">
              <div>{{scope.row.registerTime | dateFormate3}}</div>
            </template>
          </el-table-column>
          <el-table-column label="激活日期" prop="activateTime" width="150">
            <template slot-scope="scope">
              <div>{{scope.row.activateTime | dateFormate3}}</div>
            </template>
          </el-table-column>
          <el-table-column label="创建日期" prop="created" width="150">
            <template slot-scope="scope">
              <div>{{scope.row.created | dateFormate3}}</div>
            </template>
          </el-table-column>
          <el-table-column :formatter="formatter" label="注册渠道" prop="registerChannel" width="100">
          </el-table-column>
          <el-table-column label="招募方式" prop="registerScene" width="100">
            <template slot-scope="scope">
              <div>{{scope.row.registerScene | strFormat}}</div>
            </template>
          </el-table-column>
          <el-table-column label="性别" prop="gender" width="100">
            <template slot-scope="scope">
              <div v-if="scope.row.gender">{{scope.row.gender}}</div>
              <div v-else>未知</div>
            </template>
          </el-table-column>
          <el-table-column label="生日" prop="birthday" width="100">
            <template slot-scope="scope">
              <div>{{scope.row.birthday | dateFormate2}}</div>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <!--分页栏-->
      <template slot="page">
        <el-pagination :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
          @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background layout="total, prev, pager, next, sizes,  jumper">
        </el-pagination>
      </template>
    </ListWrapper>
    <UploadFileModal :dialogShow="uploadDialogShow" @dialogClose="doDialogClose" @upload-success="doUploadSuccess">

    </UploadFileModal>
    <DownloadCenterDialog :dialogvisiable="fileDialogvisiable" :showTip="showTip" @dialogClose="doDownloadDialogClose"></DownloadCenterDialog>
    <ExportConfirm :dialogShow="exportDialogShow" @dialogClose="doExportDialogClose" @summit="doSummit"></ExportConfirm>
  </div>
</template>

<script lang="ts" src="./DqshMemberList.ts">
</script>

<style lang="scss">
.dqsh-member-list {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: hidden;
  .title {
    height: 60px;
    margin-top: 20px;
    line-height: 60px;
    padding-left: 20px;
    border-bottom: 1px solid #dfe2e5;
    font-size: 12px;
  }
  .current-page {
    height: calc(100% - 48px);
    overflow: auto;
    .el-select {
      width: 100%;
    }
  }

  .el-range-editor.el-input__inner {
    width: 100%;
  }
}
</style>