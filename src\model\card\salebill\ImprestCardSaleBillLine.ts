/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-04-22 15:53:35
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\card\salebill\ImprestCardSaleBillLine.ts
 * 记得注释
 */
export default class ImprestCardSaleBillLine {
  // 卡面额
  faceAmount: Nullable<number> = null
  // 售价
  price: Nullable<number> = null
  // 制售数量
  total: Nullable<number> = null
  // 次数
  count: Nullable<number> = null
  // 起始卡号
  startCardCode: Nullable<string> = null
  // 结束卡号
  endCardCode: Nullable<string> = null
}