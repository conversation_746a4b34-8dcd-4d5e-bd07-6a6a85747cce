<template>
  <div class="pay-method-container">
    <BreadCrume :panelArray="panelArray">
    </BreadCrume>
    <div class="current-page">
      <div style="margin: 20px;line-height: 40px" v-if="hasOptionPermission('/设置/资料/支付方式', '支付方式维护')">
        <el-row>
          <el-col :span="2" style="min-width: 100px">{{i18n('支付方式代码')}}</el-col>
          <el-col :span="4">
            <el-input :placeholder="i18n('请输入支付方式拼音或英文名')" maxlength="50" v-model="newIns.id" style="width: 280px" />
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="2" style="min-width: 100px">{{i18n('支付方式名称')}}</el-col>
          <el-col :span="4">
            <el-input v-model="newIns.name" maxlength="50" style="width: 280px" />
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="2" style="min-width: 100px">&nbsp;</el-col>
          <el-col :span="8">
            <i class="el-icon-warning" /> {{i18n('代码不允许与已有支付方式重复')}}
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="2" style="min-width: 100px">&nbsp;</el-col>
          <el-col :span="4">
            <el-button class="btn-search" @click="add" type="primary">{{formatI18n('/营销/积分活动/新建门店积分兑换活动/表格/添加')}}</el-button>
            <el-button class="btn-reset" type="normal" @click="clear">{{formatI18n('/资料/券承担方/清空')}}</el-button>
          </el-col>
        </el-row>
      </div>
      <ListWrapper style="overflow: initial" :showQuery="hasOptionPermission('/设置/资料/支付方式', '支付方式维护')">
        <template slot="list">
          <FloatBlock refClass="current-page" :top="95" style="padding: 5px;">
            <template slot="ctx">
              <el-row>
                <el-col :span="12" style="line-height: 36px; min-height: 36px;">
                  <div v-if="hasOptionPermission('/设置/资料/支付方式', '支付方式维护')">
                    <el-checkbox v-model="checkedAll" style="padding-left: 10px;" @change="checkedAllRow" />
                    {{formatI18n('/资料/券承担方/已选择')}}
                    <span class="number-text">{{selected.length}}</span>
                    {{i18n('个支付方式')}}
                    <el-button @click="deleteBatch" type="danger" style="margin-left:6px">{{formatI18n('/资料/券承担方/批量删除')}}
                    </el-button>
                  </div>
                </el-col>
                <el-col :span="12" style="text-align: right">
                  <el-input :placeholder="i18n('搜索支付方式代码或名称')" @change="doSearch" v-model="query.idNameLikes" suffix-icon="el-icon-search"
                    style="width: 280px" />
                </el-col>
              </el-row>
            </template>
          </FloatBlock>
          <el-table ref="table" :data="queryData" style="width: 100%;margin-top: 10px;" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" v-if="hasOptionPermission('/设置/资料/支付方式', '支付方式维护')"></el-table-column>
            <el-table-column fixed :label="i18n('支付方式代码')" prop="id" />
            <el-table-column fixed :label="i18n('支付方式名称')" prop="name" />
            <el-table-column fixed :label="formatI18n('/资料/券承担方/操作')" v-if="hasOptionPermission('/设置/资料/支付方式', '支付方式维护')">
              <template slot-scope="scope">
                <span class="span-btn" @click="showUpdateDialog(scope.row)" v-if="hasOptionPermission('/设置/资料/支付方式', '支付方式维护')">
                  {{formatI18n('/资料/券承担方/修改')}}
                </span>
                <span class="span-btn" style="margin-left:8px" @click="remove(scope.row.id)" v-if="hasOptionPermission('/设置/资料/支付方式', '支付方式维护')">
                  {{formatI18n('/资料/券承担方/删除')}}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </ListWrapper>
    </div>
    <el-dialog :title="formatI18n('/资料/券承担方/修改')" :visible.sync="dialogVisible" class="cosparty-dialog-center" width="30%">
      <div style="margin: 20px;">
        <el-row>
          <el-col :span="8" style="height: 50px; line-height: 30px">{{i18n('支付方式代码')}}</el-col>
          <el-col :span="16">
            <el-input :placeholder="i18n('请输入支付方式拼音或英文名')" maxlength="50" v-model="updateIns.id" />
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8" style="height: 50px; line-height: 30px">{{i18n('支付方式名称')}}</el-col>
          <el-col :span="16">
            <el-input maxlength="50" v-model="updateIns.name" />
          </el-col>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{formatI18n('/资料/券承担方/取 消')}}</el-button>
        <el-button type="primary" @click="update">{{formatI18n('/资料/券承担方/确 定')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script lang="ts" src="./PayMethodList.ts">
</script>

<style lang="scss" scoped>
.pay-method-container {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: hidden;
  .cosparty-dialog-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .total {
    margin: 20px;
  }

  .current-page {
    height: calc(100% - 77px);
    overflow: auto;

    .el-select {
      width: 100%;
    }
  }
}
</style>