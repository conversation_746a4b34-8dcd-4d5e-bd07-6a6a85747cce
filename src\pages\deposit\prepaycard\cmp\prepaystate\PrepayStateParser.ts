/*
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:03
 * @LastEditTime: 2023-12-08 14:04:04
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\prepaycard\cmp\prepaystate\PrepayStateParser.ts
 * 记得注释
 */
import I18nTool from "common/I18nTool";

export default class PrepayStateParser {
  parseState(state: string) {
    switch (state) {
      case 'UNACTIVATED':
        return I18nTool.match('/储值/预付卡/预付卡查询/列表页面/未激活');
      case 'USING':
        return I18nTool.match('/储值/预付卡/预付卡查询/列表页面/使用中');
      case 'USED':
        return I18nTool.match('/储值/预付卡/预付卡查询/列表页面/已使用');
      case 'PRESENTING':
        return I18nTool.match('/储值/预付卡/预付卡查询/列表页面/转赠中');
      case 'CANCELLED':
        return I18nTool.match('/储值/预付卡/预付卡查询/列表页面/已作废');
      case 'LOST':
        return I18nTool.match('/储值/预付卡/预付卡查询/列表页面/已挂失');
      case 'FROZEN':
        return I18nTool.match('/储值/预付卡/预付卡查询/列表页面/已冻结');
      case 'RECOVER':
        return I18nTool.match('/储值/预付卡/预付卡查询/列表页面/已回收');
      case 'MADE':
        return I18nTool.match('/储值/预付卡/预付卡查询/列表页面/已制卡');
      case 'BLANK':
        return I18nTool.match('/储值/预付卡/预付卡查询/列表页面/空白卡');
      case 'BROKEN':
        return I18nTool.match('/储值/预付卡/预付卡查询/列表页面/坏卡');
      default:
        return state;
    }
  }

  parseStateColor(state: string) {
    switch (state) {
      case 'UNACTIVATED':
        return '#FF9933';
      case I18nTool.match('/储值/预付卡/预付卡查询/列表页面/未激活'):
        return '#FF9933';
      case 'USING':
        return '#33CC00';
      case I18nTool.match('/储值/预付卡/预付卡查询/列表页面/使用中'):
        return '#33CC00';
      case 'USED':
        return '#7F36C1';
      case I18nTool.match('/储值/预付卡/预付卡查询/列表页面/已使用'):
        return '#7F36C1';
      case 'PRESENTING':
        return '#3366FF';
      case I18nTool.match('/储值/预付卡/预付卡查询/列表页面/转赠中'):
        return '#3366FF';
      case 'CANCELLED':
        return 'rgba(0, 0, 0, 0.25)';
      case I18nTool.match('/储值/预付卡/预付卡查询/列表页面/已作废'):
        return 'rgba(0, 0, 0, 0.25)';
      case 'LOST':
        return '#FFCC33';
      case I18nTool.match('/储值/预付卡/预付卡查询/列表页面/已挂失'):
        return '#FFCC33';
      case 'FROZEN':
        return '#CC0033';
      case I18nTool.match('/储值/预付卡/预付卡查询/列表页面/已回收'):
        return '#FF9933';
      case 'RECOVER':
        return '#FF9933';
      case I18nTool.match('/储值/预付卡/预付卡查询/列表页面/已冻结'):
        return '#CC0033';
      case 'MADE':
        return '#7F36C1';
      case 'BLANK':
        return '#FF9933';
      case I18nTool.match('/储值/预付卡/预付卡查询/列表页面/空白卡'):
        return '#FF9933';
      case 'BROKEN':
        return '#CC0033';
      case I18nTool.match('/储值/预付卡/预付卡查询/列表页面/坏卡'):
        return '#CC0033';
    }
  }
}
