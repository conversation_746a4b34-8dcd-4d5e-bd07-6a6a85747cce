import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import MemberDetail from "model/member_standard/MemberDetail";
import I18nPage from "common/I18nDecorator";
import MemberFormItem from "pages/member/data/cmp/MemberFormItem";
import MemberCardTitle from "pages/member/data/cmp/MemberCardTitle";

@Component({
  name: "MemberInfoBase",
  components: { MemberCardTitle, MemberFormItem },
})
@I18nPage({
  prefix: [
    "/会员/会员资料",
  ],
  auto: true,
})
export default class MemberInfoBase extends Vue {
  @Prop()
  dtl: MemberDetail;
  @Prop({ default: () => [] })
  customMemberAttr: any[];
  @Prop()
  showMobileAndEmailCheckInfo: boolean;

  get isStandardMember() {
    return this.$route.fullPath.indexOf("/standard-member") != -1;
  }

  get permissionResourceId() {
    return this.isStandardMember ? "/会员/会员管理/会员资料" : "/会员/会员管理/营销中心会员";
  }

  get dtlAddress() {
    let dtlAddress = "";
    if (!this.dtl) return dtlAddress;
    if (this.dtl.province) {
      if (this.dtl.address) {
        dtlAddress = (this.dtl.province ? this.dtl.province.name + "/" : "") + (this.dtl.city ? this.dtl.city!.name + "/" : "") + (this.dtl.district ? this.dtl.district!.name + "/" : "") + (this.dtl.street ? this.dtl.street!.name + "/" : "") + this.dtl.address;
      } else {
        dtlAddress = (this.dtl.province ? this.dtl.province.name + "/" : "") + (this.dtl.city ? this.dtl.city!.name + "/" : "") + (this.dtl.district ? this.dtl.district!.name + "/" : "") + (this.dtl.street ? this.dtl.street!.name + "/" : "");
      }
    } else {
      if (this.dtl.address) {
        dtlAddress = this.dtl.address;
      } else {
        dtlAddress = "";
      }
    }
    return dtlAddress;
  }

  @Watch("dtl", { immediate: true })
  dtlChanged() {
    if (!(this.dtl && this.dtl.memberId)) return;
  }


  onEdit() {
    this.$emit("edit");
  }
}
