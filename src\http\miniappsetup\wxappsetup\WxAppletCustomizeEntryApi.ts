/*
 * @Author: 黎钰龙
 * @Date: 2022-11-15 17:07:29
 * @LastEditTime: 2022-12-01 15:25:45
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\miniappsetup\wxappsetup\WxAppletCustomizeEntryApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import SaveCustomizeEntryRequest from 'model/miniappsetup/aliappsetup/SaveCustomizeEntryRequest'  //数据模型也要改
import CustomizeEntryRequest from 'model/miniappsetup/aliappsetup/CustomizeEntryRequest'

export default class WxAppletCustomizeEntryApi {
  /**
   * 更新
   * 更新。
   * 
   */
  static modify(body: SaveCustomizeEntryRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/weixin-applet/customize/entry/modify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询
   * 查询。
   * 
   */
  static query(): Promise<Response<CustomizeEntryRequest>> {
    return ApiClient.server().post(`/v1/weixin-applet/customize/entry/query`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除
   * 删除。
   * 
   */
  static remove(id: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/weixin-applet/customize/entry/remove`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存
   * 保存。
   * 
   */
  static save(body: SaveCustomizeEntryRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/weixin-applet/customize/entry/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
