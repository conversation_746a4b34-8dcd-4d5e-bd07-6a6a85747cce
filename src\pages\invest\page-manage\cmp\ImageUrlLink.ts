import EnvUtil from "util/EnvUtil";
import { ImageUrlLinkVO } from "./ImageUrlLinkVO";

// let ossImageBaseUrl = EnvUtil.getServiceUrl() + 'rumba-oss-server/rs/oss/v1/phoenix/o/images_config_weixin_'
const ossImageBaseUrl = 'https://phoenix-test.hd123.com/rumba-oss-server/rs/oss/v1/phoenix/o/images_config_weixin_'
function generateImgeUrl () {
    let obj:any = {}
    Object.keys(ImageUrlLinkVO).forEach((key: string) => {
        obj[key] = ossImageBaseUrl + key + '.png'
    })
    return obj
}
const imageUrlLink = generateImgeUrl()
export default imageUrlLink