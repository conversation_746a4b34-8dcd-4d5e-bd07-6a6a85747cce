import IdName from 'model/common/IdName'
import Payment from "model/common/Payment";

export default class CardDepositBillSaveRequest {
  // 单号
  billNumber: Nullable<string> = null
  // 起始卡号
  startCardCode: Nullable<string> = null
  // 起始卡类型
  startCardType: Nullable<string> = null
  // 截至卡号
  endCardCode: Nullable<string> = null
  // 状态INITIAL：未审核；AUDITED：已审核
  state: Nullable<string> = null
  // 来源：impt-导入；create-界面新建
  source: Nullable<string> = null
  // 发生组织
  occurredOrg: Nullable<IdName> = null
  // 营销中心
  marketingCenter: Nullable<string> = null
  // 审核时间
  audited: Nullable<Date> = null
  // 审核人
  auditor: Nullable<string> = null
  // 备注
  remark: Nullable<string> = null
  // 明细备注只用于展示： 返回格式：增加卡余额额数，扣减卡余额数；
  detailRemark: string[] = []
  // 创建时间
  created: Nullable<Date> = null
  // 最后修改时间
  lastModified: Nullable<Date> = null
  // 原实充金额
  oldAmount: Nullable<number> = null
  // 原返现金额
  oldGiftAmount: Nullable<number> = null
  // 调整实充金额
  occurAmount: Nullable<number> = null
  // 调整返现金额
  occurGiftAmount: Nullable<number> = 0.00
  // 储值调整金额只用于前端展示
  totalAmount: Nullable<number> = null
  // 调整原因
  reason: Nullable<string> = null
  // 卡账户类型
  accountType: Nullable<IdName> = null
  // 充值类型
  depositType: Nullable<string> = 'DEPOSIT'
  // 客户
  clientId: Nullable<string> = null
  // 优惠金额
  favAmount: Nullable<number> = 0.00
  // 支付方式
  payments: Payment[] = []
  }