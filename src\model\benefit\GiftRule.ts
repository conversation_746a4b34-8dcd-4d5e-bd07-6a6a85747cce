import GiftInfo from 'model/common/GiftInfo'
import Grade from "model/grade/Grade";
import GiftDay from './GiftDay';

export default class GiftRule {
  // 是否已经启用
  stopped: Nullable<boolean> = null
  // 活动号
  number: Nullable<string> = null
  // 每月x号送礼
  giftDay: Nullable<GiftDay> = new GiftDay()
  // 不同等级送相同礼包
  allSameGiftBag: Nullable<GiftInfo> = null
  // 不同等级送不同礼包
  differentGiftBag: any
  // 等级名称
  gradeList: Grade[] = []
}