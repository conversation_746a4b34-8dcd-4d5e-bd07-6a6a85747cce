/*
 * @Author: 黎钰龙
 * @Date: 2023-10-25 11:57:37
 * @LastEditTime: 2023-12-06 13:34:20
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\limitActivity\BMemberGoodsQuatoActivity.ts
 * 记得注释
 */

import BBaseActivity from "model/common/BBaseActivity";

// 会员商品活动
export default class BMemberGoodsQuatoActivity extends BBaseActivity {
  // 每人每日购买次数（PHX-10161 废弃）
  maxDailyBuyTimes: Nullable<number> = null
}