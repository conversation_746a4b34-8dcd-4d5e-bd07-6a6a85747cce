<!--
 * @Author: 黎钰龙
 * @Date: 2025-05-22 10:03:41
 * @LastEditTime: 2025-05-23 15:38:01
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\cmp\release-channel-dialog\ReleaseChannelDialog.vue
 * 记得注释
-->
<template>
  <el-dialog :visible.sync="visible" :title="i18n('装修投放渠道')" width="50%">
    <FormItem :label="i18n('投放渠道')" class="formItem-require">
      <div style="line-height: 36px;">
        <el-checkbox-group v-model="selectedChannel">
          <el-checkbox v-for="item in channelOptions" :key="item" :label="item">{{getLabel(item)}}</el-checkbox>
        </el-checkbox-group>
      </div>
    </FormItem>
    <div slot="footer">
      <el-button @click="doCancel">{{ i18n('取消') }}</el-button>
      <el-button @click="doConfirm" size="small" type="primary">{{i18n('确定')}}</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import FormItem from "cmp/formitem/FormItem";
import I18nPage from "common/I18nDecorator";
import { CmsConfigChannel, CmsConfigUtils } from "model/template/CmsConfig";
import { Component, Prop, Vue } from "vue-property-decorator";
@Component({
  name: "ReleaseChannelDialog",
  components: {
    FormItem,
  },
})
@I18nPage({
  prefix: ["/公用/券模板", "/公用/按钮", "/页面/页面管理", "/设置/页面管理"],
  auto: true,
})
export default class ReleaseChannelDialog extends Vue {
  @Prop({ type: Array, default: () => [] }) channelOptions: string[]; // 渠道选项
  visible: boolean = false;
  selectedChannel: string[] = [];

  open() {
    this.visible = true;
    this.selectedChannel = [];
  }

  getLabel(channel: string) {
    return CmsConfigUtils.getLabel(channel as CmsConfigChannel);
  }

  doConfirm() {
    if (!this.selectedChannel.length) {
      return this.$message.warning(this.i18n("/公用/券模板/用券渠道/请至少选择一个渠道"));
    }
    this.$emit("submit", this.selectedChannel);
    this.visible = false;
  }

  doCancel() {
    this.visible = false;
    this.selectedChannel = [];
  }
}
</script>

<style>
</style>