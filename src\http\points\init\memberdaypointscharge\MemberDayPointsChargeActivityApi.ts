import ApiClient from 'http/ApiClient'
import MemberDayPointsChargeRule from 'model/points/init/memberdaypointscharge/MemberDayPointsChargeRule'
import Response from 'model/common/Response'

export default class MemberDayPointsChargeActivityApi {
  /**
   * 详情
   * 详情。
   *
   */
  static detail(): Promise<Response<MemberDayPointsChargeRule>> {
    return ApiClient.server().get(`/v1/activity/member-day-points-charge/detail`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 修改或保存
   * 修改或保存。
   *
   */
  static saveOrModify(body: MemberDayPointsChargeRule): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/activity/member-day-points-charge/saveOrModify`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 启用或禁用会员日积分加速抵现V1
   * 启用或禁用会员日积分加速抵现V1
   *
   */
  static switchMbrDayPtsChargeRule(stop: boolean): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/activity/member-day-points-charge/switchMbrDayPtsChargeRule/${stop}`, {}, {}).then((res) => {
      return res.data
    })
  }

}
