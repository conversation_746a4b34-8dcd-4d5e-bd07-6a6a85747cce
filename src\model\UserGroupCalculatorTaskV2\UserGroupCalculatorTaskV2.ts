import { UpdateCycle } from "model/autoTag/UpdateCycle"
import { State } from "./State"
import { UpdateMode } from "./UpdateMode"
import UserGroupCalculatorRule from "./UserGroupCalculatorRule"
import { DayOfWeek } from "model/autoTag/DayOfWeek"
import { ExecuteState } from "./ExecuteState"

export default class UserGroupCalculatorTaskV2 {
  // uuid
  uuid: Nullable<string> = null
  // 客群uuid
  userGroupUuid: Nullable<string> = null
  // 客群名称
  userGroupName: Nullable<string> = null
  // 客群分类uuid
  userGroupCategory: Nullable<string> = null
  // 覆盖人数
  coveredCount: Nullable<number> = null
  // 计算基准时间
  baseTime: Nullable<Date> = null
  // 覆盖率
  coveredPercentage: Nullable<number> = null
  // 状态
  state: Nullable<State> = null
  // 计算规则
  rule: Nullable<UserGroupCalculatorRule> = null
  // 更新方式 [手动更新，周期跟新]
  updateMode: Nullable<UpdateMode> = null
  // 更新周期
  updateCycle: Nullable<UpdateCycle> = null
  // 每周更新日期
  dayOfWeek: Nullable<DayOfWeek> = null
  // 每月更新日期
  dayOfMonth: Nullable<number> = null
  // 是否是每月最后一天
  lastOfMonth: Nullable<boolean> = null
  // 状态[未执行，执行中，执行成功，执行失败]
  executeState: Nullable<ExecuteState> = null
  // 失败原因
  failReason: Nullable<string> = null
  // 最后执行结束时间
  lastEndTime: Nullable<Date> = null
  // 说明
  remark: Nullable<string> = null
  // 创建时间
  created: Nullable<Date> = null
  // 创建人标识
  creator: Nullable<string> = null
  // 最后修改时间
  lastModified: Nullable<Date> = null
  // 最后修改人
  lastModifier: Nullable<string> = null
}