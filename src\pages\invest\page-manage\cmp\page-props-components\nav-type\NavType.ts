import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import emitter from 'util/emitter';
import { FormMode } from 'model/local/FormMode';
import FormDefUtil from 'util/FormDefUtil';
import I18nPage from 'common/I18nDecorator';

@Component({
  name: 'NavType',
  mixins: [emitter],
  components: {},
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理',
    '/页面/导航设置',
  ],
  auto: true
})
export default class NavType extends Vue {
  @Prop({ type: Boolean, default: false })
  readonly: boolean; //
  @Prop({ type: String, default: 'NavType' })
  validateName: string; // 组件校验名称，editPage通过该名称进行组件校验及校验信息清除
  @Prop({ type: Object })
  value: any; // 数据模型
  @Prop({ type: String, default: '页面标题名称' })
  label: string; // label名
  @Prop({ type: String })
  prefixDescribe: string; // 前置描述
  @Prop({ type: String })
  suffixDescribe: string; // 后置描述

  @Prop({ type: Boolean, default: true })
  required: boolean; // 是否必填
  @Prop({ type: String })
  relativeValidateName: string; // 联动校验名称
  @Prop()
  formKey: any;
  @Prop({
    type: Object,
    default: () => {
      return {
        isShowColor: true,
      };
    },
  })
  config: any; // 配置项
  FormModeType = FormMode;
  $refs: any;
  labelPosition: string = 'top';
  imageList: any[] = [];
  options = [
    { caption: this.i18n('页面内定位'), key: 'inside' },
  ];
  rules = {
    propTitle: [{ required: true, message: this.i18n('请输入页面标题名称'), trigger: ['blur', 'change'] }],
  };

  get formMode() {
    if (this.validateName === 'mainTitle') {
      return FormDefUtil.getFormMode(this.readonly, this.validateName, this.formKey);
    } else {
      return FormDefUtil.getFormMode(this.readonly, this.validateName + 'mainTitle', this.formKey);
    }
  }
  get signatureResult() {
    return this.$store.state.credential;
  }
  handleChange() {
    this.$emit('input', this.value);
    this.$emit('change', this.value);
  }

  mounted() {
    console.log('this.value ==>', this.value)
  }

  beforeDestroy() {
    // this.$bus.off('cms-addForm');
  }

  validate(callback: any) {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.validate(callback);
    }
  }

  clearValidate() {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.clearValidate();
    }
  }
}
