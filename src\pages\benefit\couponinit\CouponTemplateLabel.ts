/*
 * @Description:
 * @Version: 1.0
 * @Autor: 司浩
 * @Date: 2021-12-08 16:54:07
 * @LastEditors: 黎钰龙
 * @LastEditTime: 2024-11-06 14:05:22
 */
import { Component, Vue } from 'vue-property-decorator'
import BreadCrume from 'cmp/bread-crumb/BreadCrume'
import commodityLabel from './components/commodityLabel.vue'
import CouponTemplateTag from 'model/coupon/CouponTemplateTag'
import CouponTemplateTagFilter from 'model/coupon/CouponTemplateTagFilter'
import CouponTemplateTagApi from 'http/coupon/template/CouponTemplateTagApi'
import CommonUtil from "util/CommonUtil";
interface CouponTemplateTagType extends CouponTemplateTag {
  type: string
}
@Component({
  name: 'CouponTemplateLabel',
  components: { commodityLabel, BreadCrume }
})
export default class CouponTemplateLabel extends Vue {
  labelList: CouponTemplateTagType[] = []
  valueEquals: string = ''
  panelArray: any = [
    {
      name: this.formatI18n("/权益/券/券模板/券模板"),
      url: 'coupon-template-list'
    },
    {
      name: this.formatI18n("/权益/券/券模板/券模板标签管理"),
      url: ''
    }
  ]

  get labelNumStr(): string {
    let str: any = this.formatI18n("/权益/券/券模板", "共{0}个标签");
    const num = this.labelList.filter(item => item.type !== 'add').length
    str = str.replace(/\{0\}/g, num);
    return str
  }

  created() {
    this.initPage()
  }

  async initPage() {
    const params = new CouponTemplateTagFilter()
    params.page = 0
    params.pageSize = 0
    params.valueLikes = this.valueEquals
    try {
      const resData = await CouponTemplateTagApi.query(params)
      let labelList = resData.data || []
      this.labelList = labelList.reduce((acc: any, cur: any) => {
        acc.push({
          type: 'normal',
          ...cur
        })
        return acc
      }, [])
      this.hasAdd()
    } catch (error) {
      this.labelList = []
      this.hasAdd()
    }
  }

  // 确认后添加新增按钮
  hasAdd() {
    // const flag = this.labelList.some(item => {
    //   return item.type === 'add'
    // })
    // if (!flag && this.labelList.length < 20) {
    this.labelList.unshift({
      type: 'add',
      value: '',
      uuid: CommonUtil.uuid()
    })
    // }
  }

  // 编辑成功
  editSuccess() {
    this.initPage()
  }

  doChange() {
    this.initPage()
  }
}
