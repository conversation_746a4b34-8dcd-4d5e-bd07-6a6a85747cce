/*
 * @Author: 黎钰龙
 * @Date: 2024-06-26 10:12:06
 * @LastEditTime: 2024-07-11 18:02:55
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\template\UpdateRequest.ts
 * 记得注释
 */
import { ContentTemplateState } from 'model/template/ContentTemplateState'

// 修改内容模板请求
export default class UpdateRequest {
  // id
  id: Nullable<string> = null
  // 版本号
  version: Nullable<number> = null
  // 名称
  name: Nullable<string> = null
  // 图片
  image: Nullable<string> = null
  // 资源位名称
  placeName: Nullable<string> = null
  // 备注
  remark: Nullable<string> = null
  // 内容
  content: any
  // 页面状态
  state: Nullable<ContentTemplateState> = null
  // type 
  type: Nullable<string> = null
  // 是否为草稿
  hasDraft: Nullable<boolean> = null
}