import {Component, Vue} from 'vue-property-decorator'
import BreadCrume from 'cmp/bread-crumb/BreadCrume'
import CouponInitialApi from 'http/v2/coupon/init/CouponInitialApi'
import CouponConfig from 'model/v2/coupon/init/CouponConfig'

@Component({
  name: 'CouponInit',
  components: {
    BreadCrume
  }
})
export default class CouponInit extends Vue {
  panelArray: any = []
  dtl: CouponConfig = new CouponConfig()
  created() {
    this.panelArray = [
      {
        name: this.formatI18n('/权益/券/券初始化', '券初始化'),
        url: ''
      }
    ]
    this.getDtl()
  }
  getLimit(limit: number) {
    let str = this.formatI18n('/权益/券/初始化/用券配置', '一笔交易限用{0}张券')
    str = str.replace(/\{0\}/g, limit + '')
    return str
  }

  getCostPartyDefaultValue() {
    console.log("1111" + this.dtl.costPartyConfig)
    if (this.dtl.costPartyConfig === "none") {
      let str = this.formatI18n('/公用/券模板', '不记录')
      return str
    }
    if (this.dtl.costPartyConfig === "amount") {
      let str = this.formatI18n('/公用/券模板', '按金额')
      return str
    }
    if (this.dtl.costPartyConfig === "proportion") {
      let str = this.formatI18n('/公用/券模板', '按比例')
      return str
    }
  }

  getCostPartyOptionsValue() {
    console.log("1111" + this.dtl.costPartyConfigOptions)
    let str = "";
    if (this.dtl.costPartyConfigOptions && this.dtl.costPartyConfigOptions.indexOf("none") > -1) {
      str = str + this.formatI18n('/公用/券模板', '不记录') + "、"
    }
    if (this.dtl.costPartyConfigOptions && this.dtl.costPartyConfigOptions.indexOf("amount") > -1) {
      str = str + this.formatI18n('/公用/券模板', '按金额') + "、"
    }
    if (this.dtl.costPartyConfigOptions && this.dtl.costPartyConfigOptions.indexOf("proportion") > -1) {
      str = str + this.formatI18n('/公用/券模板', '按比例') + "、"
    }
    if (str.length > 0) {
      return str.slice(0, -1);
    } else {
      return str;
    }

  }

  private getDtl() {
    CouponInitialApi.get().then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.dtl = resp.data
      }
    })
  }

  private getCouponPrefix(type: string){
    if(!this.dtl || !this.dtl.couponCodePrefixes) {
      return "--";
    }

    const coupon = this.dtl.couponCodePrefixes.find(
      item => item.couponType === type
    );
    return coupon ? coupon.prefix : "--";   
  }

}