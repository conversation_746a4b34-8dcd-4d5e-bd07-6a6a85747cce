/*
 * @Author: 黎钰龙
 * @Date: 2024-07-02 14:36:40
 * @LastEditTime: 2024-07-02 15:05:51
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\public\buildjob.js
 * 记得注释
 */
const fs = require('fs')
const path = require('path')

// 读取package.json配置，并将jenkins输入的参数version作为CRM制品的版本号，显示在项目中
fs.readFile('package.json', 'utf8', (err, data) => {
  if (err) {
    console.error('读取pages.json失败', err)
    return
  }
  console.log('ci参数', process.argv);
  // console.log('读取package.json成功', data);
  let packageConfig = JSON.parse(data)
  const versionStr = process.argv.find((item) => item.indexOf('version') > -1)
  let version = '1.0.0' //jen<PERSON>编译过程中入参 版本号
  if (versionStr) {
    version = versionStr.split("=")[1]
  }
  console.log('看看版本号', version);

  if (packageConfig.vue.version) {
    packageConfig.vue.version = version
  }

  // 将修改后的对象转换回JSON字符串
  let updatedPagesConfig = JSON.stringify(packageConfig, null, 2)

  // 写回package.json
  fs.writeFile('package.json', updatedPagesConfig, 'utf8', (err) => {
    if (err) {
      console.error(err)
      return
    }
    console.log('package.json文件已更新')
  })
})