/*
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:03
 * @LastEditTime: 2023-11-01 17:27:36
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\custom\dqsh\member\cmp\UploadFileModal.ts
 * 记得注释
 */
import {Component, Prop, Vue} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import EnvUtil from 'util/EnvUtil'
import UploadApi from "http/upload/UploadApi";

@Component({
  name: 'UploadFile',
  components: {
    FormItem
  }
})
export default class UploadFileModal extends Vue {
  $refs: any
  @Prop({
    type: Boolean,
    default: false
  })
  dialogShow: boolean
  fileCount = 0
  uploadHeaders: any = {}
  // 会员资料模板
  get member() {
    if (location.href.indexOf('localhost') === -1) {
      return 'template_member_dqsh.xlsx'
    } else {
      return 'template_member_dqsh.xlsx'
    }

  }
  get getUploadUrl() {
    return EnvUtil.getServiceUrl() + `v1/custom/member/importMember?card=false`
  }
  created() {
    let locale = sessionStorage.getItem('locale')
    this.uploadHeaders = {
			locale: locale ? locale : "zh_CN",
			time_zone: new Date().getTimezoneOffset(),
			marketingCenter: sessionStorage.getItem("marketCenter"),
		};
    const authorization = EnvUtil.getUniAuthorization()
    if (authorization) {
      this.uploadHeaders.authorization = authorization
    }
  }
  doModalClose(type: string) {
    if (type === 'confirm') {
      if (this.fileCount > 0) {
        this.$refs.upload.submit();
      } else {
        this.$message.warning('请先选择文件')
      }
    } else {
      this.$emit('dialogClose')
    }
  }
  doHandleChange(file: any, fileList: any) {
    if (fileList.length > 0) {
      this.fileCount++
    }
  }
  doBeforeClose(done: any) {
    this.$emit('dialogClose')
    done()
  }
  getSuccessInfo(a: any, b: any, c: any) {
    if (a && a.code === 2000) {
      this.$refs.upload.clearFiles()
      this.fileCount = 0
      this.$emit('dialogClose')
      this.$emit('upload-success')
    }
  }
  getErrorInfo(a: any, b: any, c: any) {
    this.$message.error('导入失败，请重新导入')
    this.fileCount = 0
    this.$refs.upload.clearFiles()
  }

  downloadTemplate() {
    UploadApi.getUrl(this.member).then((resp: any) => {
      if (resp && resp.data) {
        window.open(resp.data);
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
}