<template>
  <el-dialog :title="titleString" append-to-body :close-on-click-modal="false" :visible.sync="dialogShow" @close="canel" width="75%">
    <ListWrapper class="current-page">
      <template slot="query">
        <el-row>
          <el-col :span="8">
            <form-item :label="i18n('活动名称')">
              <el-input :placeholder="i18n('请输入活动名称')" v-model="query.nameLike" style="width: 90%" />
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="i18n('活动号')">
              <el-input :placeholder="i18n('请输入活动号')" clearable v-model="query.numberEquals" style="width: 90%" />
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="i18n('活动状态')">
              <el-select style="width: 180px" class="cur-sel" v-model="query.stateEquals" :placeholder="i18n('请选择')">
                <el-option v-for="item in ContentTemplateState" :value="item.value" :label="item.label" :key="item.value"></el-option>
              </el-select>
            </form-item>
          </el-col>
        </el-row>
        <!-- <el-row style="margin-top: 10px">
          <el-col :span="8">
            <form-item label="i18n('活动主题">
              <el-input :placeholder="i18n('请输入活动主题" v-model="query.topicNameLikes" style="width: 90%" />
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="i18n('外部活动号">
              <el-input placeholder="" v-model="query.outerNumberIdLike" style="width: 90%" />
            </form-item>
          </el-col>
        </el-row> -->
        <el-row style="margin-top: 10px">
          <el-col :span="8">
            <form-item label="">
              <el-button class="btn-search" @click.stop="doSearch" size="small" type="primary">{{ i18n('查询')}}</el-button>
              <el-button class="btn-reset" @click.stop="doReset" size="small">{{ i18n('重置')}}</el-button>
            </form-item>
          </el-col>
        </el-row>
      </template>
      <template slot="list">
        <el-table
          v-if="dialogShow"
          :data="tableData"
          :row-key="getRowKey"
          ref="table"
          @select="handleSelectionChange"
          @select-all="handleSelectAll"
          style="width: 100%; margin-top: 12px; height: 397px; overflow: auto"
        >
          <el-table-column type="selection" width="60" reserve-selection></el-table-column>
          <el-table-column :label="i18n('活动名称')">
            <template slot-scope="scope">
              {{ scope.row.body ? scope.row.body.name || "--" : scope.row.name || "--" }}
            </template>
          </el-table-column>
          <el-table-column :label="i18n('活动号')">
            <template slot-scope="scope">
              {{ scope.row.body ? scope.row.body.activityId || "--" : scope.row.activityId || "--" }}
            </template>
          </el-table-column>
          <el-table-column :label="i18n('/券/延期申请/活动时间')">
            <template v-slot="scope">
                  <div v-if="scope.row.beginDate && scope.row.endDate">
                    {{ scope.row.beginDate | dateFormate2 }}
                    {{ formatI18n("/营销/券礼包活动/券礼包活动", "至") }}
                    {{ scope.row.endDate | dateFormate2 }}
                  </div>
                  <div v-else>--</div>
                </template>
          </el-table-column>
          <el-table-column :label="i18n('活动状态')" align="center" prop="state" >
            <template slot-scope="scope">
                  <ActivityStateTag :stateEquals="scope.row.state"></ActivityStateTag>
                </template>
          </el-table-column>
          <!-- <el-table-column :label="i18n('活动主题')">
            <template slot-scope="scope">
              {{ scope.row.body ? scope.row.body.topicName || "--" : scope.row.topicName || "--" }}
            </template>
          </el-table-column>
          <el-table-column :label="i18n('券批次号')">
            <template slot-scope="scope">
              {{ scope.row.body ? scope.row.body.batchNumber || "--" : scope.row.batchNumber || "--" }}
            </template>
          </el-table-column> -->
        </el-table>
      </template>
      <template slot="page">
        <el-pagination
          :current-page.sync="page.currentPage"
          :page-size="page.size"
          :page-sizes="[10, 20, 30, 40]"
          :total="page.total"
          background
          layout="total, prev, pager, next, sizes,  jumper"
          @current-change="onHandleCurrentChange"
          @size-change="onHandleSizeChange"
          style="margin-top: 20px"
        ></el-pagination>
      </template>
    </ListWrapper>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click.stop="canel">{{ i18n('取消')}}</el-button>
      <el-button size="small" type="primary" @click.stop="confirm">{{ i18n('确定')}}</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./ElasticLayerPage.ts"></script>

<style lang="scss" scoped>
.current-page {
  // ::v-deep .el-input__suffix {
  //   right: -15px;
  //   top: -1px;
  // }
  ::v-deep .el-input__validateIcon {
    width: 0;
    &::before {
      content: "";
      display: none !important;
    }
  }

  ::v-deep .el-table::before {
      background-color: #fff !important;
    }
  
}
</style>
