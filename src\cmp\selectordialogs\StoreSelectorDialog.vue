<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:03
 * @LastEditTime: 2024-04-27 15:45:34
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\selectordialogs\StoreSelectorDialog.vue
 * 记得注释
-->
<template>
  <el-dialog :title="title" class="select-store-dialog" append-to-body :close-on-click-modal="false" :visible.sync="dialogShow">
    <div class="wrap">
      <el-form label-width="80px">
        <el-row class="query">
          <el-col :span="8">
            <el-form-item :label="formatI18n('/公用/公共组件/门店选择弹框组件/查询/门店')">
              <el-input v-model="orgFilter.idNameLikes" :placeholder="formatI18n('/公用/公共组件/门店选择弹框组件/查询/请输入门店代码/名称')" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-button type="primary" @click="doSearch()">{{formatI18n('/公用/按钮/查询')}}</el-button>
              <el-button @click="doReset()">{{formatI18n('/公用/按钮/重置')}}</el-button>
              <el-button type="primary" @click="doImport()" v-if="hasImport">{{formatI18n('/公用/券模板/导入')}}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-row>
        <el-col :span="18">
          <el-row class="table-wrap" v-loading="loading.query">
            <el-row class="thead">
              <el-col :span="1">
                <el-checkbox @change="doCheckAll($event)" v-model="checkAll" />
              </el-col>
              <el-col :span="11">{{formatI18n('/公用/公共组件/门店选择弹框组件/表格/门店代码')}}</el-col>
              <el-col :span="12">{{formatI18n('/公用/公共组件/门店选择弹框组件/表格/门店名称')}}</el-col>
            </el-row>
            <el-row class="tbody" v-if="!loading.query">
              <template v-if="currentList && currentList.length > 0">
                <el-row v-for="(item, index) of currentList" :key="index" class="trow">
                  <el-col :span="1">
                    <el-checkbox v-model="checkboxList[index]" @change="doCheck($event, index)" />
                  </el-col>
                  <el-col @click.native="doCheckRow(index)" :span="11" :title="item.org.id">{{ item.org.id }}</el-col>
                  <el-col @click.native="doCheckRow(index)" :span="12" :title="item.org.name">{{ item.org.name }}</el-col>
                </el-row>
              </template>
              <el-row v-if="!currentList || currentList.length === 0" class="trow" style="text-align: center;color: #909399">
                {{ formatI18n('/公用/提示/暂无数据') }}
              </el-row>
            </el-row>
          </el-row>
        </el-col>
        <el-col :span="6" style="padding-left: 5px">
          <el-row class="right-table">
            <el-row class="thead">
              {{formatI18n('/公用/公共组件/门店选择弹框组件/表格/已选门店：')}}{{selected?(selected.filter(e=>e.org.id)).length: 0}}
              <span @click="delAllItem()" class="span-btn" style="margin-left: 80px;">
                {{ formatI18n('/公用/按钮/清空') }}
              </span>
            </el-row>
            <el-row style="padding: 5px;">
              <el-input style="width: 100%;" @keyup.enter.native="filterSelected()" @change="filterSelected()" v-model="selectedFilter" clearable
                @clear="filterSelected()" :placeholder="formatI18n('/公用/公共组件/门店选择弹框组件/查询/请输入门店代码/名称')"></el-input>
            </el-row>
            <el-row class="tbody">
              <template v-if="filteredSelected && filteredSelected.length > 0">
                <el-row class="trow" style="position: relative;display: flex;align-items: center" :key="index"
                  v-for="(item, index) of filteredSelected.filter(e=>e.org.id)" :title="item.org|idName">
                  <div class="left">{{ item.org|idName }}</div>
                  <div class="clear-btn" style="display: none"><a @click="delItem(item, index)">{{ formatI18n('/公用/公共组件/品牌选择弹框组件/表格/清除') }}</a>
                  </div>
                </el-row>
              </template>
              <el-row v-if="!filteredSelected || filteredSelected.length === 0" class="trow" style="text-align: center;color: #909399">
                {{ formatI18n('/公用/提示/暂无数据') }}
              </el-row>
            </el-row>
          </el-row>
        </el-col>
      </el-row>
    </div>
    <div class="page" style="margin-top: 15px">
      <el-pagination :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
        @current-change="handleCurrentChange($event)" @size-change="handleSizeChange($event)" background
        layout="total, prev, pager, next, sizes,  jumper">
      </el-pagination>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="dialogShow = false">{{formatI18n('/公用/按钮/取消')}}</el-button>
      <el-button size="small" type="primary" @click="doModalClose()">{{formatI18n('/公用/按钮/确定')}}</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./StoreSelectorDialog.ts"/>

<style lang="scss" scoped>
.select-store-dialog {
  display: flex;
  align-items: center;
  justify-content: center;
  @import "SelectorDialogCommon";
}
</style>