/*
 * @Author: 黎钰龙
 * @Date: 2023-07-05 11:04:16
 * @LastEditTime: 2024-03-26 16:45:03
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\couponTemplateDtlItem\couponThresholdDtl\CouponThresholdDtl.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import CouponItem from 'model/common/CouponItem';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  name: 'CouponThresholdDtl'
})
@I18nPage({
  prefix: [
    '/公用/券模板/单品折扣券/用券门槛',
    '/公用/券模板'
  ],
  auto: true
})
export default class CouponThresholdDtl extends Vue {
  @Prop()
  data: CouponItem;

  get formateThresholdStr() {
    let str = ''
    str = this.i18n('元减{0}元，最多可减')
    str = str.replace(/\{0\}/g, String(this.data?.coupons?.cashCouponAttribute?.faceAmount) || '--');
    return str
  }
};