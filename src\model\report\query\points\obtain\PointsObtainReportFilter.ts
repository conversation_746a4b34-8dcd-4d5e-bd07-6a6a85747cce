import BaseReportFilter from 'model/report/query/BaseReportFilter'

export default class PointsObtainReportFilter extends BaseReportFilter {
  // 会员识别码等于
  identCodeEquals: Nullable<string> = null
  // 发生组织等于
  occurredOrgIdEquals: Nullable<string> = null
  // 活动名称类似于
  activityNameLikes: Nullable<string> = null
  // 活动名称起始于
  activityNameStartsWith: Nullable<string> = null
  // 交易号类似于
  transNoLikes: Nullable<string> = null
  // 交易号等于
  transNoEquals: Nullable<string> = null
  // 发生积分小于或等于
  occurredLessOrEquals: Nullable<number> = null
  // 发生积分大于或等于
  occurredGreaterOrEquals: Nullable<number> = null
  // 原因类似于
  reasonLikes: Nullable<string> = null
  // 过期时间位于···之间
  expireTimeBetween: Date[] = []
  // 交易金额小于等于
  amountLessOrEquals: Nullable<number> = null
  // 交易金额大于等于
  amountGreaterOrEquals: Nullable<number> = null
  // 积分类型等于
  typeEquals: Nullable<string> = null
  // 积分类型等于
  typeIn: string[] = []
  // 交易时间位于
  tranTimeBetween: Date[] = []
  // 积分场景等于
  sceneIn: string[] = []
}