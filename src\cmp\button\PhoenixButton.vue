<template>
    <button :disabled="disabled" class="p-button" @click="doClick"><slot></slot></button>
</template>

<script lang="ts" src="./PhoenixButton.ts">
</script>

<style lang="scss">
.p-button{
    height: 56px;
    width: 100%;
    background:linear-gradient(90deg,rgba(0,172,255,1) 0%,rgba(1,108,255,1) 100%);
    box-shadow:0px 4px 20px 0px rgba(0,126,255,0.4);
    border-radius:8px;
    border: none;
    cursor: pointer;
    outline: none;
    font-size:20px;
    font-family:PingFangSC-Medium,PingFang SC;
    font-weight:500;
    color:rgba(255,255,255,1);
    letter-spacing:4px;
    &:hover{
        background:linear-gradient(90deg,rgba(77,197,255,1) 0%,rgba(77,152,255,1) 100%);
    }
    &:active{
        background:linear-gradient(90deg,rgba(0,145,215,1) 0%,rgba(0,91,214,1) 100%);
    }
    &:disabled{
        background:linear-gradient(90deg,rgba(0,172,255,1) 0%,rgba(1,108,255,1) 100%);;
        cursor: not-allowed;
        opacity: 0.45;
    }
}
</style>