export default class PointsStoreReportLine {
  // 发生组织
  occurredOrg: Nullable<string> = null
  // 得积分
  obtain: Nullable<number> = null
  // 消费得积分
  consumeObtain: Nullable<number> = null
  // 消费得积分笔数
  consumeObtainTimes: Nullable<number> = null
  // 用积分
  use: Nullable<number> = null
  // 兑换积分
  exchange: Nullable<number> = null
  // 兑换积分笔数
  exchangeSendTimes: Nullable<number> = null
  // 抵扣积分
  deduct: Nullable<number> = null
  // 抵扣积分笔数
  deductSendTimes: Nullable<number> = null
}