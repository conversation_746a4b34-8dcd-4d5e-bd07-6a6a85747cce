<template>
  <div class="prepay-card-adjust-add">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button :loading="loading" v-if="hasOptionPermission('/卡/卡管理/预付卡调整单', '单据维护')" @click="doSave" type="primary">保存</el-button>
        <el-button :loading="loading" v-if="hasOptionPermission('/卡/卡管理/预付卡调整单', '单据审核')" @click="doSaveAndAudit">保存并审核</el-button>
        <el-button v-if="hasOptionPermission('/卡/卡管理/预付卡调整单', '单据维护')" @click="doCancel">取消</el-button>
      </template>
    </BreadCrume>
    <div class="content">
      <div style="width: 100%;position: relative">
        <el-tabs v-model="activeName" type="card" :closable="tabNames.length > 1" @tab-remove="removeTab" @tab-click="handleTabClick">
          <el-tab-pane :label="i18n('卡') + (index + 1)" :name="name" :key="index" v-for="(name, index) in tabNames"></el-tab-pane>
        </el-tabs>
        <el-button type="primary" style="position: absolute;right: 0;top: 0;" @click="handleAddCard" :disabled="tabNames.length >= 10">+
          {{i18n('添加预付卡')}}
        </el-button>
      </div>
      <FormItem label="卡号" :required="true">
        <el-input @blur="doMemberChange" class="width-298" v-model="formData.member" :disabled="!hasOptionPermission('/卡/卡管理/预付卡调整单', '单据维护')">
        </el-input>
      </FormItem>
      <FormItem label="账户类型" v-if="switchFlag">
        <div style="line-height: 36px;padding-left: 10px" no-i18n v-if="memberMap[formData.member] && memberMap[formData.member].accountType">
          [{{memberMap[formData.member].accountType.id}}]{{memberMap[formData.member].accountType.name}}
        </div>
      </FormItem>
      <FormItem label="卡类型">
        <div style="line-height: 36px;padding-left: 10px" v-if="memberMap[formData.member]">
          <template v-if="isCountingCard">{{i18n('次卡')}}</template>
          <template v-else>{{memberMap[formData.member].cardType}}</template>
        </div>
      </FormItem>
      <FormItem label="卡余额调整" :required="true">
        <div class="inner-content">
          <div style="line-height: 36px">
            <i18n v-if="isCountingCard" k="/储值/预付卡/预付卡调整单/编辑页面/当前卡可用次数{0}次">
              <span slot="0" style="font-weight:600"> {{memberMap[formData.member].remainderTimes}} </span>
            </i18n>
            <span v-else>
              <i18n k="/储值/预付卡/预付卡调整单/编辑页面/当前卡余额{0}元=实充余额{1}元+返现余额{2}元">
                <template slot="0">&nbsp;<span
                    class="weight">{{formData.isDisabled || !memberMap[formData.member] ? '-': memberMap[formData.member].account.total}}</span>&nbsp;</template>
                <template slot="1">&nbsp;<span
                    class="weight">{{formData.isDisabled || !memberMap[formData.member] ? '-': memberMap[formData.member].account.balance}}</span>&nbsp;</template>
                <template slot="2">&nbsp;<span
                    class="weight">{{formData.isDisabled || !memberMap[formData.member] ? '-': memberMap[formData.member].account.giftBalance}}</span>&nbsp;</template>
              </i18n>
            </span>
          </div>
          <FormItem :label="i18n('/储值/预付卡/预付卡调整单/编辑页面/次数调整')" v-if="isCountingCard">
            <div style="line-height: 36px">
              <AutoFixInput :min="-999999" :max="999999" :disabled="formData.isDisabled" :fixed="0" v-model="formData.count" :appendTitle="i18n('次')"
                style="width:132px">
              </AutoFixInput>
            </div>
          </FormItem>
          <FormItem label="储值调整" v-else>
            <div style="line-height: 36px">
              <i18n k="/储值/预付卡/预付卡调整单/编辑页面/实充调整{0}元+返现调整{1}元=余额调整{2}元">
                <template slot="0">
                  &nbsp;<el-input :disabled="formData.isDisabled" @change="doOccurAmountChange()" class="width-78"
                    v-model="formData.occurAmount"></el-input>&nbsp;
                </template>
                <template slot="1">
                  &nbsp;<el-input :disabled="formData.isDisabled" @change="doGiftOccurAmountChange()" class="width-78"
                    v-model="formData.occurGiftAmount"></el-input>&nbsp;
                </template>
                <template slot="2">
                  &nbsp;<el-input class="width-78" disabled v-model="getTotal">
                  </el-input>&nbsp;
                </template>
              </i18n>
            </div>
          </FormItem>
          <FormItem v-if="isCountingCard" class="gray-tips">
            <div style="margin-bottom:10px">
              <i class="iconfont  ic-info" style="font-size: 16px;color: #A1A6AE"></i>
              <span>{{i18n('输入正数表示增加次数，负数表示扣减次数')}}</span>
            </div>
          </FormItem>
          <FormItem v-else class="gray-tips">
            <div>
              <i class="iconfont  ic-info" style="font-size: 16px;color: #A1A6AE"></i>
              <span>输入正数表示增加余额，负数表示扣减余额，支持输入两位小数</span>
            </div>
            <div style="padding-left: 17px">如：实充输入50，表示实充增加50元；实充输入-50，表示实充扣减50元</div>
            <div style="padding-left: 17px">扣减余额不得大于当前余额</div>
          </FormItem>
          <FormItem :label="isCountingCard ? i18n('卡次数调整原因') : i18n('卡余额调整原因')">
            <el-select :disabled="formData.isDisabled" class="width-298" placeholder="请选择" v-model="formData.reason">
              <el-option no-i18n :label="item.content" :value="item.content" v-for="item in reasons" :key="item.id">{{item.content}}</el-option>
            </el-select>
          </FormItem>
          <FormItem v-if="showOrg" :label="formatI18n('/权益/积分/新建积分调整单/条目', '发生组织')">
            <SelectStores v-model="formData.occurredOrg.id" :appendAttr="getOrgAppendAttr"
              :disabled="formData.isDisabled||formData.useMemberOwnerStore" :isOnlyId="true" :hideAll="true" width="298px"
              :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
            </SelectStores>
          </FormItem>
          <FormItem v-if="showOrg">
            <el-checkbox v-if="!isMoreMarketing" :disabled="formData.isDisabled" @change="changeOrg" v-model="formData.useMemberOwnerStore">
              {{formatI18n('/储值/预付卡/预付卡调整单/编辑页面', '使用发卡门店作为发生组织')}}
            </el-checkbox>
          </FormItem>
          <FormItem label="说明">
            <el-input :disabled="formData.isDisabled" class="width-298" maxlength="200" type="textarea" v-model="formData.remark"></el-input>
          </FormItem>
        </div>
      </FormItem>
    </div>
  </div>
</template>

<script lang="ts" src="./PrepayCardAdjustAdd.ts">
</script>

<style lang="scss">
.prepay-card-adjust-add {
  width: 100%;
  height: 100%;
  background-color: white;
  overflow: auto;
  .content {
    padding: 30px;
  }
  .width-78 {
    width: 78px;
  }
  .width-298 {
    width: 298px;
  }
  .el-textarea__inner {
    height: 100px;
  }
  .inner-content {
    background-color: rgba(249, 249, 249, 1);
    padding-left: 10px;
    margin: -7px 20px 20px 15px;
    height: auto;
    padding-bottom: 20px;
  }
  .el-checkbox {
    margin-right: 10px;
  }
  .weight {
    font-weight: 600;
    color: #515151;
  }
  .qf-form-item .qf-form-content {
    position: relative;
    margin-left: 105px !important;
  }
  .qf-form-item .qf-form-label {
    width: 110px !important;
  }
}
</style>