import { Component, Vue } from 'vue-property-decorator'
import I18nPage from "common/I18nDecorator";
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import RechargeableCardReportApi from "http/prepay/report/card/RechargeableCardReportApi";
import GiftCardFilter from 'model/prepay/report/card/GiftCardFilter'
import CountRefundReport from './refund/CountRefundReport';
import CountConsumCardsReport from './consume/CountConsumCardsReport';
import CountSalesCardsReport from './salescards/CountSalesCardsReport';
import DownloadCenterDialog from 'pages/main/dowmload_center_dialog/DownloadCenterDialog';
import RSOrgFilter from 'model/common/RSOrgFilter';
import OrgApi from 'http/org/OrgApi';
import RSOrg from 'model/common/RSOrg';
import ZoneApi from 'http/area/ZoneApi';
import ZoneFilter from 'model/datum/zone/ZoneFilter';
import Zone from 'model/datum/zone/Zone';
import CountCardReportExport from '../cmp/export/countCard/CountCardReportExport';
import CountingCardReportApi from 'http/prepay/card/CountingCardReportApi';
import CountRefundCardReport from "pages/deposit/prepaycard/countCardReport/refundcard/CountRefundCardReport";


@Component({
  name: 'DepositCardReport',
  components: {
    CountSalesCardsReport,
    CountConsumCardsReport,
    CountRefundReport,
    CountRefundCardReport,
    BreadCrume,
    DownloadCenterDialog,
    CountCardReportExport
  }
})
@I18nPage({
  prefix: [
    '/储值/预付卡/实体礼品卡报表',
    '/储值/预付卡/电子礼品卡报表',
    '/公用/提示',
    '/公用/查询条件',
    '/公用/按钮',
    '/储值/预付卡/储值卡报表',
    '/公用/菜单',
  ],
  auto: false
})
export default class DepositCardReport extends Vue {
  activeName: string = '售卡流水'
  panelArray: any = []
  exportDialogShow = false  //导出弹窗
  fileDialogVisible = false //文件中心弹窗
  stores: RSOrg[] = [] //发生组织列表
  areaData: Zone[] = []
  showTip = false
  created() {
    this.panelArray = [
      {
        name: this.i18n('次卡报表'),
        url: ''
      }
    ]
    this.getStore()
    this.getAreaList()
    // PHX-14618需求：会员资料 -> 会员资产 -> 可用预付卡 -> 明细按钮
    if (this.$route.query.from == "member-asset") {
      this.activeName = "消费流水";
    }
  }

  get getExportDialogShow() {
    return this.exportDialogShow
  }

  doExportDialogClose() {
    this.exportDialogShow = false
  }

  doBatchExport() {
    this.exportDialogShow = true
  }

  doDownloadDialogClose() {
    this.showTip = false
    this.fileDialogVisible = false
  }

  doExportSubmit(type: string, filter: GiftCardFilter) {
    if (!type || !filter) {
      return
    }
    if (type === 'SALES_HST') { //售卡流水导出
      CountingCardReportApi.exportSalesHst(filter).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.exportAfter()
        }
      }).catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    } else if (type === 'CONSUME_HST') {  //消费流水导出
      CountingCardReportApi.exportConsumeHst(filter).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.exportAfter()
        }
      }).catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    } else if (type === 'REFUND_HST') { //退款流水导出
      CountingCardReportApi.exportRefundHst(filter).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.exportAfter()
        }
      }).catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    } else if (type === 'REFUND_CARD_HST') { //退款流水导出
      CountingCardReportApi.exportRefundCardHst(filter).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.exportAfter()
        }
      }).catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    }
  }

  exportAfter() {
    this.showTip = true
    this.fileDialogVisible = true
  }

  //发生组织列表
  private getStore() {
    let params: RSOrgFilter = new RSOrgFilter()
    params.page = 0
    params.pageSize = 0
    OrgApi.query(params).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.stores = resp.data || []
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  //发生区域组织
  getAreaList() {
    const params = new ZoneFilter()
    ZoneApi.query(params).then((res) => {
      if (res.code === 2000) {
        this.areaData = res.data || []
      } else {
        this.$message.error(res.msg as string)
      }
    })
  }
}
