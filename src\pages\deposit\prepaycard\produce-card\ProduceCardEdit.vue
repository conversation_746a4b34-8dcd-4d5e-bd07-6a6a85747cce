<!--
 * @Author: 黎钰龙
 * @Date: 2023-10-09 15:39:14
 * @LastEditTime: 2024-08-14 10:53:06
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\prepaycard\produce-card\ProduceCardEdit.vue
 * 记得注释
-->
<template>
  <div class="produce-card-container">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button type="primary" v-if="hasOptionPermission('/卡/卡管理/制卡单', '单据审核')" @click="saveAndAudit">
          {{ i18n('保存并审核') }}
        </el-button>
        <el-button @click="save">
          {{ formatI18n('/储值/预付卡/卡模板/编辑页面/保存') }}
        </el-button>
        <el-button @click="$router.go(-1)">
          {{ formatI18n('/公用/按钮', '取消') }}
        </el-button>
      </template>
    </BreadCrume>
    <el-form :model="ruleForm" :rules="rules" class="demo-ruleForm" label-width="120px" ref="form">
      <div class="setting-container">
        <div class="section-block" style="padding-bottom:100px">
          <el-form-item :label="i18n('卡模板')" prop="cardNumber">
            <CardTplItem no-i18n @submit="selectTpl" :isShowFitGoods="!cardAttributeFix" :canSelectCenter="false"
              :number="ruleForm.cardTemplateNumber" :cardMedium="['bar','mag','online','rfic','ic']" :type="null">
            </CardTplItem>
          </el-form-item>
          <el-form-item :label="i18n('制卡数量')" prop="makeQty">
            <AutoFixInput :min="1" :max="20000" :fixed="0" :appendTitle="i18n('张')" style="width: 180px" v-model="ruleForm.makeQty"
              :placeholder="i18n('1-20000')">
            </AutoFixInput>
          </el-form-item>
          <el-form-item :label="i18n('起始卡号')" prop="startCardCode">
            <el-input v-model="ruleForm.startCardCode" style="width:300px" @change="doStartCodeChange" :placeholder="computeStartNumber()"></el-input>
            <span class="span-btn" style="margin-left:12px" @click="autoMakeStartCode">{{i18n('自动生成')}}</span>
          </el-form-item>
          <el-form-item :label="i18n('结束卡号')" prop="endCardCode">
            <el-input v-model="ruleForm.endCardCode" style="width:300px" disabled></el-input>
          </el-form-item>
          <el-form-item :label="i18n('/公用/券核销/发生组织')" prop="occurredOrg" v-if="!cardAttributeFix">
            <SelectStores v-model="ruleForm.occurredOrg" :isOnlyId="true" :hideAll="true" width="300px"
              :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
            </SelectStores>
          </el-form-item>
          <el-form-item :label="i18n('写卡类型')" v-if="hasWriteType" prop="writeCardType">
            <el-radio-group v-model="ruleForm.writeCardType">
              <el-radio label="SYS">{{i18n('商家写卡')}}</el-radio>
              <el-radio label="MANUFACTOR">{{i18n('厂家写卡')}}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input type="textarea" placeholder="请输入不超过140个字" v-model="ruleForm.remark" maxlength="140" show-word-limit style="width: 400px" />
          </el-form-item>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script src="./ProduceCardEdit.ts">
</script>

<style lang="scss" scoped>
.produce-card-container {
  width: 100%;
  overflow: auto;
}
</style>