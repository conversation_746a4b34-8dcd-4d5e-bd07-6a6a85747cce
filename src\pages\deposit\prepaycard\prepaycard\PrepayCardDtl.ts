import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import PrePayCardApi from 'http/prepay/card/PrePayCardApi';
import PrePayCard from 'model/prepay/card/PrePayCard';
import PrePayCardPresent from 'model/prepay/card/PrePayCardPresent';
import PrePayCardFilter from 'model/prepay/card/PrePayCardFilter';
import PrePayCardStateHst from 'model/prepay/card/PrePayCardStateHst';
import FormItem from 'cmp/formitem/FormItem.vue'
import PrepayState from '../cmp/prepaystate/PrepayState.vue'
import PrePayCardTransaction from 'model/prepay/card/PrePayCardTransaction';
import DataUtil from '../common/DataUtil';
import PrePayConfigApi from 'http/prepay/config/PrePayConfigApi';
import PrepayStateParser from "pages/deposit/prepaycard/cmp/prepaystate/PrepayStateParser";
import I18nPage from "common/I18nDecorator";
import BPrepayCardHistoryHstFilter from "model/prepay/card/BPrepayCardHistoryHstFilter";
import QueryCountCardHstRequest from 'model/card/template/QueryCountCardHstRequest';

@Component({
  name: 'PrepayCardDtl',
  components: {
    SubHeader,
    FormItem,
    PrepayState
  }
})
@I18nPage({
  prefix: [
    '/储值/预付卡/预付卡查询/详情页面',
    '/公用/提示',
    '/公用/按钮',
  ],
})
export default class PrepayCardDtl extends Vue {
  i18n: (str: string, params?: string[]) => string
  detail: PrePayCard = new PrePayCard()
  prePayCardTransaction: PrePayCardTransaction[] = []
  recoverBeforeTransaction: PrePayCardTransaction[] = []
  prePayCardPresent: PrePayCardPresent[] = []
  prePayCardStateHst: PrePayCardStateHst[] = []
  dataUtil: DataUtil = new DataUtil()
  parser: PrepayStateParser = new PrepayStateParser()
  enableMultipleAccount: boolean = false
  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  historyPage = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  activeName: string = '账户流水'
  @Prop()
  code: string
  @Prop()
  defaultTab: string

  mounted() {
    this.tabChange()
    this.query()
  }

  @Watch('code')
  onCodeChange() {
    this.query()
  }

  query() {
    this.detail = new PrePayCard()
    this.prePayCardTransaction = []
    this.recoverBeforeTransaction = []
    this.prePayCardPresent = []
    this.prePayCardStateHst = []
    if (this.code && this.code != '') {
      let code = this.code as string
      this.getPrePermission()
      this.getDetail(code)  //卡详情
    }
  }

  @Watch('defaultTab')
  onDefaultTabChange() {
    this.tabChange()
  }

  private tabChange() {
    if (this.defaultTab && this.defaultTab != '') {
      this.activeName = this.defaultTab
    } else {
      this.activeName = '账户流水'
    }
  }

  //获取预付卡详情
  private getDetail(code: string) {
    let filter: PrePayCardFilter = new PrePayCardFilter()
    filter.codeEquals = code
    filter.page = 0
    filter.pageSize = 1
    PrePayCardApi.query(filter).then((resp: any) => {
      if (resp && resp.code === 2000 && resp.data && resp.data.length > 0) {
        this.detail = resp.data[0]
        if (this.detail.cardType === this.i18n('次卡')) {
          this.countCardQueryHst(this.detail.code as string, this.detail.buyerMemberId as string)  //账户流水
          this.countCardQueryHistory(this.detail.code as string, this.detail.buyerMemberId as string)  //历史流水
          this.countCardQueryOption(this.detail.code as string) //卡操作日志
        } else {
          this.queryHst(this.detail.accountUuid as string)  //账户流水
          this.queryHistoryHst(this.detail.code as string, this.detail.accountUuid as string) //历史流水
          this.getCardStateHst(this.detail.code as string)  //卡操作日志
        }
        this.getCardPresent(this.detail.code as string) //转赠过程
      } else {
        this.$message.error(resp.msg)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private getPrePermission() {
    PrePayConfigApi.get().then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.enableMultipleAccount = resp.data.enableMultipleAccount
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  //账户流水
  private queryHst(account: string) {
    PrePayCardApi.queryHst(account, this.page.currentPage - 1, this.page.size).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.prePayCardTransaction = resp.data
        this.page.total = resp.total
      } else {
        this.$message.error(resp.msg)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  //次卡-账户流水
  private async countCardQueryHst(code: string, memberId: string) {
    const hstParams = new QueryCountCardHstRequest()
    hstParams.codeEquals = code
    hstParams.memberIdEquals = memberId
    hstParams.page = this.page.currentPage - 1
    hstParams.pageSize = this.page.size
    try {
      const hstRes = await PrePayCardApi.queryCountingCardHst(hstParams)  //账户流水
      if (hstRes.code === 2000) {
        this.prePayCardTransaction = hstRes.data || []
        this.page.total = hstRes.total
      } else {
        this.$message.error(hstRes.msg || this.i18n('获取账户流水失败'))
      }
    } catch (error: any) {
      this.$message.error(error.message || this.i18n('获取账户流水失败'))
    }
  }

  //次卡-历史流水
  private async countCardQueryHistory(code: string, memberId: string) {
    const historyParams = new QueryCountCardHstRequest()
    historyParams.codeEquals = code
    historyParams.memberIdNotEquals = memberId
    historyParams.page = this.historyPage.currentPage - 1
    historyParams.pageSize = this.historyPage.size
    try {
      const historyRes = await PrePayCardApi.queryCountingCardHst(historyParams)  //历史流水
      if (historyRes.code === 2000) {
        this.recoverBeforeTransaction = historyRes.data || []
        this.historyPage.total = historyRes.total
      } else {
        this.$message.error(historyRes.msg || this.i18n('获取历史流水失败'))
      }
    } catch (error: any) {
      this.$message.error(error.message || this.i18n('获取历史流水失败'))
    }
  }

  //次卡-卡操作日志
  private async countCardQueryOption(code: string) {
    try {
      const res = await PrePayCardApi.countCardStateHst(code)
      if (res.code === 2000) {
        this.prePayCardStateHst = res.data || []
      } else {
        this.$message.error(res.msg || this.i18n('获取卡操作日志失败'))
      }
    } catch (error: any) {
      this.$message.error(error.message || this.i18n('获取卡操作日志失败'))
    }
  }

  //获取历史流水
  private queryHistoryHst(code: string, account: string) {
    let filter: BPrepayCardHistoryHstFilter = new BPrepayCardHistoryHstFilter()
    filter.codeEquals = code
    filter.accountNotEquals = account
    filter.page = this.historyPage.currentPage - 1
    filter.pageSize = this.historyPage.size
    PrePayCardApi.queryHistoryHst(filter).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.recoverBeforeTransaction = resp.data
        this.historyPage.total = resp.total
      } else {
        this.$message.error(resp.msg)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  //获取转赠过程（只有电子礼品卡才有）
  private getCardPresent(code: string) {
    if (this.detail.cardType != this.i18n('电子礼品卡')) return
    PrePayCardApi.getCardPresent(code).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.prePayCardPresent = resp.data
      } else {
        this.$message.error(resp.msg)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  //卡操作日志
  private getCardStateHst(code: string) {
    PrePayCardApi.getCardStateHst(code).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.prePayCardStateHst = resp.data
      } else {
        this.$message.error(resp.msg)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private getRowClass(row: any) {
    if (row.row.current) {
      return 'high-light'
    }
  }

  private getRowClass1(row: any) {
    if (row.rowIndex === this.prePayCardStateHst.length - 1) {
      return 'high-light'
    }
  }

  /**
* 账户流水-每页多少条的回调
* @param val
*/
  onHandleSizeChange(val: number) {
    this.page.size = val
    if (this.detail.cardType === this.i18n('次卡')) {
      this.countCardQueryHst(this.detail.code as string, this.detail.buyerMemberId as string)  //账户流水
    } else {
      this.queryHst(this.detail.accountUuid as string)
    }
  }
  /**
   * 历史流水-分页页码改变的回调
   * @param val
   */
  onHandleCurrentChangeForBeforeHst(val: number) {
    this.historyPage.currentPage = val
    if (this.detail.cardType === this.i18n('次卡')) {
      this.countCardQueryHistory(this.detail.code as string, this.detail.buyerMemberId as string)  //历史流水
    } else {
      this.queryHistoryHst(this.detail.code as string, this.detail.accountUuid as string)
    }
  }
  /**
   * 历史流水-每页多少条的回调
   * @param val
   */
  onHandleSizeChangeForBeforeHst(val: number) {
    this.historyPage.size = val
    if (this.detail.cardType === this.i18n('次卡')) {
      this.countCardQueryHistory(this.detail.code as string, this.detail.buyerMemberId as string)  //历史流水
    } else {
      this.queryHistoryHst(this.detail.code as string, this.detail.accountUuid as string)
    }
  }

  /**
   * 账户流水-分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    if (this.detail.cardType === this.i18n('次卡')) {
      this.countCardQueryHst(this.detail.code as string, this.detail.buyerMemberId as string)  //账户流水
    } else {
      this.queryHst(this.detail.accountUuid as string)
    }
  }
}
