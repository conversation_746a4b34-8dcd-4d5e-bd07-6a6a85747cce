import ApiClient from 'http/ApiClient'
import BGiftCardAnalysisReportGraphic from 'model/analysis/BGiftCardAnalysisReportGraphic'
import BPrepayCardAnalysisFilter from 'model/analysis/BPrepayCardAnalysisFilter'
import BGiftCardAnalysisReport from 'model/analysis/BGiftCardAnalysisReport'
import Response from 'model/default/Response'

export default class GiftCardAnalysisContorllerApi {


  
   /**
   * 礼品卡汇总分析
   * 充值卡分析报表
   * 
   */
    static querySum(body: BPrepayCardAnalysisFilter): Promise<Response<BGiftCardAnalysisReport>> {
      return ApiClient.server().post(`/v1/analysis-report/gift-card/querySum`, body, {
      }).then((res) => {
        return res.data
      })
    }

  /**
   * 导出
   * 导出
   * 
   */
  static export(body: BPrepayCardAnalysisFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/analysis-report/gift-card/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 充值卡分析报表
   * 充值卡分析报表
   * 
   */
  static query(body: BPrepayCardAnalysisFilter): Promise<Response<BGiftCardAnalysisReportGraphic>> {
    return ApiClient.server().post(`/v1/analysis-report/gift-card/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
