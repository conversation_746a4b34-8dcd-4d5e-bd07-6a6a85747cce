import {Component, Vue, Watch} from 'vue-property-decorator'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import FormItem from 'cmp/formitem/FormItem.vue'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import CardAdjustBillApi from 'http/card/adjustbill/CardAdjustBillApi'
import IdName from 'model/common/IdName'
import I18nPage from "common/I18nDecorator";
import CardDepositBillApi from 'http/card/depositbill/CardDepositBillApi'
import CardDepositBillSaveRequest from 'model/card/depositbill/CardDepositBillSaveRequest'
import AmountToFixUtil from 'util/AmountToFixUtil'
import SelectStores from 'cmp/selectStores/SelectStores'
import SystemConfigApi from "http/systemConfig/SystemConfigApi";
import Client from "model/card/client/Client";
import Client<PERSON>pi from "http/card/client/ClientApi";
import ClientFilter from "model/card/client/ClientFilter";
import SelectClient from "cmp/selectclient/SelectClient";

class PrepayCardAdjustAddFormData {
  member = ''
  occurAmount = ''
  occurGiftAmount = ''
  reason = ''
  remark = ''
  occurredOrg: IdName = new IdName()
  useMemberOwnerStore = false
  isDisabled = true
}

@Component({
  name: 'PrepayCardPayAdd',
  components: {
    SubHeader,
    FormItem,
    BreadCrume,
    SelectStores,
    SelectClient
  }
})
@I18nPage({
  prefix: [
    '/储值/预付卡/预付卡调整单/编辑页面',
    '/公用/按钮',
    '/储值/预付卡/预付卡充值单'
  ],
})
export default class PrepayCardPayAdd extends Vue {
  i18n: (str: string, params?: string[]) => string
  panelArray: any
  isDisabled = true
  dtlParams: any = {}

  timer = 0
  memberMap: any = {} // string -> CardAccount
  memberErrMap: any = {} // string -> error string
  reasons: any[] = []
  noUse = ''
  switchFlag = false
  loading = false
  activeName = '1'
  tabNames: string[] = []
  formData = new PrepayCardAdjustAddFormData()
  isMoreMarketing: boolean = false // 是否开启多营销中心 true 开启 false 不开启
  formDataMap: any = {} // activeName -> PrepayCardAdjustAddFormData
  isEmpty: Boolean = true
  isExist: Boolean = false
  isDeposit: Boolean = false
  isUsing: Boolean = false
  cardType: Nullable<string> = null

  isEndEmpty: Boolean = true
  isEndExist: Boolean = false
  isEndDeposit: Boolean = false
  isEndUsing: Boolean = false
  endCardType: Nullable<string> = null

  params: CardDepositBillSaveRequest = new CardDepositBillSaveRequest()
  // 卡数量
  cardCount: Nullable<number> = null
  // 客户信息
  queryClientData: Client[] = []  //当前列表展示数据
  originClientData: Client[] = [] //原始表单数据
  // 支付方式信息
  queryPaymentData: IdName[] = []  //当前列表展示数据
  originPaymentData: IdName[] = [] //原始表单数据
  clientRequired: boolean = false  // 是否开启客户必填
  selectLoading: boolean = false


  @Watch('params.clientId', { deep: true })
  handle(value:any) {
    console.log(11111)
  }

  // 本金总金额
  get totalOccurAmount() {
    if (this.params.occurAmount && this.cardCount) {
      return (Number(this.params.occurAmount) * Number(this.cardCount)).toFixed(2)
    }
    return 0;
  }
  // 赠金总金额
  get totalGiftOccurAmount() {
    if (this.params.occurGiftAmount && this.cardCount) {
      return (Number(this.params.occurGiftAmount) * Number(this.cardCount)).toFixed(2)
    }
    return 0;
  }
  // 应付金额/应退金额
  get dueAmountOrRefundAmount() {
    if (this.totalOccurAmount) {
      return ((Number(this.totalOccurAmount) * 100 - Number(this.params.favAmount) * 100) / 100 ).toFixed(2)
    }
    return 0;
  }
  // 支付总金额
  get totalPayAmount() {
    if (this.params.payments.length > 0) {
      return this.params.payments.reduce((totalPayAmount, payment) => {
        if (payment.paymentAmount) {
          return (Number(totalPayAmount) + Number(payment.paymentAmount)).toFixed(2)
        }
        return totalPayAmount;
      }, 0)
    } else {
      return 0;
    }
  }
  get getTotal() {
    if (this.formData.occurAmount && this.formData.occurGiftAmount) {
      return (Number(this.formData.occurAmount) + Number(this.formData.occurGiftAmount)).toFixed(2)
    }
    return ''
  }

  get sumAmount() {
    return Number(Number(this.params.occurAmount) + Number(this.params.occurGiftAmount)).toFixed(2)
  }

  get getOrgAppendAttr() {
    const obj: any = {}
    obj.orgTypeEquals = 'PHX'
    if (this.isMoreMarketing) {
      obj.marketingCenterIdEquals = sessionStorage.getItem('marketCenter')
    }
    return obj
  }

  get otherTip() {
    let str = this.i18n('该卡为{0}，不可进行充值')
    if (this.cardType === 'ImprestCard') {
      str = str.replace('{0}', this.i18n('充值卡'))
    } else if (this.cardType === 'OfflineGiftCard') {
      str = str.replace('{0}', this.i18n('实体礼品卡'))
    } else if (this.cardType === 'OnlineGiftCard') {
      str = str.replace('{0}', this.i18n('电子礼品卡'))
    }
    return str
  }

  get otherEndTip() {
    let str = this.i18n('该卡为{0}，不可进行充值')
    if (this.endCardType === 'ImprestCard') {
      str = str.replace('{0}', this.i18n('充值卡'))
    } else if (this.endCardType === 'OfflineGiftCard') {
      str = str.replace('{0}', this.i18n('实体礼品卡'))
    } else if (this.endCardType === 'OnlineGiftCard') {
      str = str.replace('{0}', this.i18n('电子礼品卡'))
    }
    return str
  }

  created() {
    this.getPaymentList()
    this.getConfig()
    // this.getClientInfo('')
    if (sessionStorage.getItem('isMultipleMC') == '1') {
      this.isMoreMarketing = true
    } else {
      this.isMoreMarketing = false
    }
    this.panelArray = [
      {
        name: this.i18n('预付卡充值单'),
        url: 'prepay-card-pay'
      },
      {
        name: this.i18n('新建预付卡充值单'),
        url: ''
      }
    ]
    this.panelArray[1].name = this.$route.query.from === 'edit' ? this.i18n('编辑预付卡充值单') : this.i18n('新建预付卡充值单')
    if (this.$route.query.from === 'edit') {
        this.getModify()
    }

  }

  // 获取客户
  getClientInfo(key:string) {
    const params = new  ClientFilter()
    if (key) {
      params.key = key
    }
    params.page = 0
    params.pageSize = 1000
    this.selectLoading = true
    ClientApi.query(params).then(res =>{
      if (res.data) {
        this.queryClientData = res.data
        this.originClientData = res.data
      }
    }).catch((error: any) => {
      this.$message.error(error.message)
    }).finally(() => this.selectLoading = false)
  }
  // 查询支付方式
  getPaymentList() {
    SystemConfigApi.getPayMethodConfig().then((resp) => {
      if (resp.code === 2000) {
        this.queryPaymentData = resp.data?.payMethods || []
        this.originPaymentData = resp.data?.payMethods || []
      } else {
        throw new Error(resp.msg!)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  // 查询储值卡配置
  getConfig() {
    CardDepositBillApi.getConfig().then((resp) => {
      if (resp.code === 2000) {
        this.clientRequired = resp.data?.clientRequired || false
      } else {
        throw new Error(resp.msg!)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  queryClient(value:any) {
    this.getClientInfo(value)
  }

  handleSelectPayment(row:any) {
    const selectedOption = this.queryPaymentData.find(option => option.id === row.paymentId);
    if (selectedOption) {
      row.paymentName = selectedOption.name;
    } else {
      row.paymentName = '';
    }
  }

  doBack() {
    this.$router.back()
  }

  getCardInfo() {
    if (this.params.startCardCode) {
      this.isEmpty = false
      CardDepositBillApi.getCardInfo(this.params.startCardCode as string).then(res => {
        if (res.data) {
          this.params.accountType = res.data.accountType
          this.params.accountType!.name = res.data.cardType
          this.params.startCardType = res.data.cardTypeCode
          if (this.params.startCardType === 'RechargeableCard') {
            this.isDeposit = true
          } else {
            this.cardType = res.data.cardTypeCode
            this.isDeposit = false
          }
          if (res.data.account) {
            this.params.oldAmount = res.data.account.balance
            this.params.oldGiftAmount = res.data.account.giftBalance
            if (res.data.cardState === 'USING') {
              this.isUsing = true
            } else {
              this.isUsing = false
            }
          }
          this.isExist = true
        } else {
          this.isExist = false
          this.isDeposit = false
          this.isUsing = false
          this.cardType = ''
        }
      })
      if (this.params.endCardCode) {
        this.getCartCount();
      } else {
        CardDepositBillApi.calculateCartCount(this.params).then(res =>{
          if (res.code === 2000) {
            if (res.data) {
              this.cardCount = Number(res.data)
            } else {
              this.cardCount = Number(0)
            }
          } else {
            this.cardCount = 0
            this.$message.warning(res.msg || '-')
          }
        }).catch((error: any) => {
          this.$message.error(error.message)
        })
      }
    } else {
      this.isEmpty = true
      this.isExist = false
      this.isDeposit = false
      this.isUsing = false
      this.cardType = ''
      this.cardCount = 0
    }

  }

  addPaymentRow() {
    if (this.params.payments.length < 10) {
      // 添加新行到cashData数组
      this.params.payments.push({
        paymentId: '', // 默认支付方式为空字符串
        paymentName:'',
        paymentAmount: null  // 默认金额为null
      });
    } else {
      this.$message.warning(this.i18n('最多支持添加10个支付方式'));
    }
  }
  deletePaymentRow(index:number) {
    this.params.payments.splice(index, 1);
  }


  getEndCardInfo() {
    if (this.params.endCardCode) {
      this.isEndEmpty = false
      CardDepositBillApi.getCardInfo(this.params.endCardCode as string).then(res => {
        if (res.data) {
          this.isEndExist = true
          if (res.data.cardTypeCode === 'RechargeableCard') {
            this.isEndDeposit = true
          } else {
            this.isEndDeposit = false
            this.endCardType = res.data.cardTypeCode
          }
          if (res.data.cardState === 'USING') {
            this.isEndUsing = true
          } else {
            this.isEndUsing = false
          }
        } else {
          this.isEndExist = false
          this.isEndUsing = false
          this.endCardType = ''
        }
      })
      if (this.params.startCardCode) {
        this.getCartCount()
      } else {
        this.cardCount = 0
      }
    } else {
      this.isEndEmpty = true
      this.isEndExist = false
      this.isEndEmpty = true
      this.isEndUsing = false
      this.endCardType = ''
      if (this.params.startCardCode) {
        CardDepositBillApi.calculateCartCount(this.params).then(res =>{
          if (res.code === 2000) {
            if (res.data) {
              this.cardCount = Number(res.data)
            } else {
              this.cardCount = Number(0)
            }
          } else {
            this.cardCount = 0
            this.$message.warning(res.msg || '-')
          }
        }).catch((error: any) => {
          this.$message.error(error.message)
        })
      } else {
        this.cardCount = 0
      }
    }
  }
  getCartCount() {
    if (this.params.startCardCode && this.params.endCardCode){
      if (this.params.endCardCode.length == this.params.startCardCode.length) {
        CardDepositBillApi.calculateCartCount(this.params).then(res =>{
          if (res.code === 2000) {
            if (res.data) {
              this.cardCount = Number(res.data)
            } else {
              this.cardCount = Number(0)
            }
          } else {
            this.cardCount = 0
            this.$message.warning(res.msg || '-')
          }
        }).catch((error: any) => {
          this.$message.error(error.message)
        })
      }
      else {
        this.cardCount = 0
        this.$message.warning(this.i18n('结束卡号和起始卡号长度需一致'))
      }
    }
  }

  private isNumeric(str:string) {
    return /^\d+$/.test(str);
  }

  doSaveAndAudit() {
    if (!this.doValidate()) {
      return
    }
    this.params.marketingCenter = sessionStorage.getItem('marketCenter')
    if (this.$route.query.from === 'edit') {
      this.loading = true
      CardDepositBillApi.modify(this.params).then((resp: any) => {
        if (resp && resp.code === 2000) {
          CardDepositBillApi.audit(this.$route.query.id as string).then((resp: any) => {
            if (resp && resp.code === 2000) {
              this.$message.success(this.i18n('保存并审核成功'))
              this.$router.push({ name: 'prepay-card-pay-dtl', query: { id: this.$route.query.id } })
            }
          }).catch((error) => {
            if (error && error.message) {
              this.$message.error(error.message)
            }
          })
        }
      }).catch((error: any) => {
        this.$message.error(error.message)
      }).finally(() => {
        this.loading = false
      })
    } else {
      this.loading = true
      CardDepositBillApi.saveAndAudit(this.params).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.i18n('保存并审核成功'))
          this.$router.push({ name: 'prepay-card-pay-dtl', query: { id: resp.data } })
        } else {
          this.$message.error(resp.msg)
        }
      }).catch((error) => {
        if (error && error.msg) {
          this.$message.error(error.msg)
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }

  doValidate() {
    if (!this.params.startCardCode) {
      this.$message.warning(this.i18n('起始卡号不能为空'))
      return false
    }
    if (!this.isExist) {
      this.$message.warning(this.i18n('卡号不存在'))
      return false
    }
    if (!this.isEndExist && !this.isEndEmpty) {
      this.$message.warning(this.i18n('结束卡号不存在'))
      return false
    }
    if (!this.isDeposit) {
      return false
    }
    if (!this.isEndDeposit && this.isEndExist && !this.isEndEmpty) {
      return false
    }
    if (this.isUsing === false) {
      this.$message.warning(this.i18n('只能给使用中的储值卡进行充值'))
      return false
    }
    if (this.isEndDeposit && this.isEndExist && !this.isEndEmpty && this.isEndUsing === false) {
      this.$message.warning(this.i18n('只能给使用中的储值卡进行充值'))
      return false
    }
    if (this.params.startCardCode.length !== 0 && !this.isEndEmpty && this.params.startCardCode.length !== this.params.endCardCode!.length) {
      this.$message.warning(this.i18n('结束卡号和起始卡号长度需一致'))
      return false
    }
    if (!this.isEndEmpty && this.params.startCardCode >= this.params.endCardCode!) {
      this.$message.warning(this.i18n('结束卡号需大于起始卡号'))
      return false
    }
    if (!this.params.occurAmount) {
      this.$message.warning(this.i18n('请填写实充金额'))
      return false
    }
    if (this.params.payments.length >= 0 && (!this.validatePayment())) {
        return false;
    }
    if (Number(this.params.favAmount) > Number(this.totalOccurAmount)) {
      if (this.params.depositType == 'DEPOSIT') {
        this.$message.warning(this.i18n('优惠金额不能超过本金总金额'))
      } else {
        this.$message.warning(this.i18n('优惠金额不能超过本金退总金额'))
      }
      return false
    }
    if (this.dueAmountOrRefundAmount != this.totalPayAmount) {
      if (this.params.depositType == 'DEPOSIT') {
        this.$message.warning(this.i18n('支付总金额与应付金额不一致'))
      } else {
        this.$message.warning(this.i18n('支付总金额与应退金额不一致'))
      }
      return false
    }
    if (!this.params.clientId && this.clientRequired) {
      this.$message.warning(this.i18n('请选择客户'))
      return false
    }
    if (!this.params.occurredOrg) {
      this.$message.warning(this.i18n('请选择发生组织'))
      return false
    }
    if (this.params.occurAmount == 0 && this.params.occurGiftAmount == 0) {
      this.$message.warning(this.i18n('实充金额和返现金额不能同时为零'))
      return false
    }
    return true
  }

  private validatePayment() {
      for (let i = 0; i < this.params.payments.length; i++) {
        const item = this.params.payments[i];
          if (item.paymentId == null || item.paymentId  == undefined || item.paymentId  == '') {
            this.$message.warning(this.i18n('请选择支付方式'))
            return false;
          }
          if (item.paymentAmount == null || item.paymentAmount  == undefined) {
            this.$message.warning(this.i18n('请输入支付金额'))
            return false;
          }
      }
      return true; // 所有元素都检查完毕，没有发现空值

  }

  doSave() {
    if (!this.doValidate()) {
      return
    }
    this.params.marketingCenter = sessionStorage.getItem('marketCenter')
    if (this.$route.query.from === 'edit') {
      this.loading = true

      CardDepositBillApi.modify(this.params).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.i18n('修改成功'))
          this.$router.push({ name: 'prepay-card-pay-dtl', query: { id: this.$route.query.id } })
        }
      }).catch((error: any) => {
        this.$message.error(error.message)
      }).finally(() => {
        this.loading = false
      })
    } else {
      this.loading = true
      CardDepositBillApi.save(this.params).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.i18n('保存成功'))
          this.$router.push({ name: 'prepay-card-pay-dtl', query: { id: resp.data } })
        } else {
          this.$message.error(resp.msg)
        }
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }

  doCancel() {
    this.$router.back()
  }

  private getModify() {
      CardDepositBillApi.get(this.$route.query.id as string).then((resp: any) => {
          if (resp && resp.code === 2000) {
              // if (resp.data.startCardCode) {
              //     this.isEmpty = false
              //     this.isExist = true
              //     this.isDeposit = true
              // }
              // if (resp.data.endCardCode) {
              //     this.isEndEmpty = false
              //     this.isEndExist = true
              //     this.isEndDeposit = true
              // }
              this.params.billNumber = resp.data.billNumber
              this.params.startCardCode = resp.data.startCardCode
              this.params.endCardCode = resp.data.endCardCode
              this.params.state = resp.data.state
              this.params.source = resp.data.source
              this.params.occurredOrg = resp.data.occurredOrg
              this.params.marketingCenter = resp.data.marketingCenter
              this.getCardInfo()
              this.params.detailRemark = resp.data.detailRemark
              this.params.favAmount = resp.data.favAmount
              this.params.payments = resp.data.payments
              this.params.clientId = resp.data.clientId
              this.params.depositType = resp.data.depositType
              CardDepositBillApi.queryDetail(this.$route.query.id.toString(), 0, 999999).then((res: any) => {
                  if (res && res.code === 2000) {
                      if (res.data && res.data.length > 0) {
                          this.params.oldAmount = res.data[0].oldAmount
                          this.params.oldGiftAmount = res.data[0].oldGiftAmount
                          this.params.occurAmount = res.data[0].occurAmount
                          this.params.occurGiftAmount = res.data[0].occurGiftAmount
                          this.params.remark = res.data[0].remark
                      }
                  }
              }).catch((error) => {
                  if (error && error.message) {
                      this.$message.error(error.message)
                  }
              })
          }
      }).catch((error: any) => {
          this.$message.error(error.message)
      })
  }

  private amountChange() {
    this.params.occurAmount = AmountToFixUtil.formatAmount(this.params.occurAmount, 9999999.99, 0, '')
  }

  private giftAmountChange() {
    if (!this.params.occurGiftAmount) {
      this.params.occurGiftAmount = 0.00
    }
    this.params.occurGiftAmount = AmountToFixUtil.formatAmount(this.params.occurGiftAmount, 9999999.99, 0, '')
  }

  private discountAmountChange() {
    this.params.favAmount = AmountToFixUtil.formatAmount(this.params.favAmount, 9999999.99, 0, '')
  }
  private paymentAmountChange(scope:any) {
    scope.row.paymentAmount = AmountToFixUtil.formatAmount(scope.row.paymentAmount, 99999999.99, 0.01, '')
  }
}
