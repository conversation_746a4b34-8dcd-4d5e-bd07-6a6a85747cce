<template>
  <div class="select-group-cmp">
    <el-radio-group v-if="!detail" :disabled="loading" v-model="groupRange" @change="doRangeChange">
      <el-radio label="ALL" v-if="!hideAll">
        {{ i18n('全部人群') }}
      </el-radio>
      <el-radio label="PART">
        {{ i18n('指定人群参与') }}
        <span class="span-btn" :class="{'cursor-disable': loading}" v-if="groupRange === 'PART'" @click="doSelect" style="margin-left: 4px">
          {{ i18n('选择') }}
        </span>
      </el-radio>
      <el-radio label="EXCLUDE">
        {{ i18n('指定人群不参与') }}
        <span class="span-btn" :class="{'cursor-disable': loading}" v-if="groupRange === 'EXCLUDE'" @click="doSelect" style="margin-left: 4px">
          {{ i18n('选择') }}
        </span>
      </el-radio>
    </el-radio-group>
    <!-- 详情展示 -->
    <div v-else>
      <template v-if="groupRange === 'ALL'">{{ i18n('全部人群') }}</template>
      <template v-if="groupRange === 'PART'">{{ i18n('指定人群参与') }}</template>
      <template v-if="groupRange === 'EXCLUDE'">{{ i18n('指定人群不参与') }}</template>
    </div>
    <div class="dtl-block" v-if="value && value.memberRangeType !== 'ALL'">
      <div class="dtl-item" v-if="hasDtlTagValue">
        <div class="select-head">
          {{ i18n('标签') }}
          <span v-if="value.memberTagOption.optionType === 'ANY'" style="font-size: 12px; color: #5A5F66">
            （{{ i18n('满足任一') }}）
          </span>
          <span v-else-if="value.memberTagOption.optionType === 'ALL'" style="font-size: 12px; color: #5A5F66">
            （{{ i18n('满足全部') }}）
          </span>
        </div>
        <div class="select-dtl" v-for="item in value.memberTagOption.tags" :key="item.tagId">
          {{ getTagValueStr(item) }}
        </div>
      </div>
      <div class="dtl-item" v-if="hasDtlGroupValue">
        <div class="select-head">
          {{ i18n('/会员/洞察/客群/列表页/客群') }}
          <span v-if="value.userGroupOption.optionType === 'ANY'" style="font-size: 12px; color: #5A5F66">
            （{{ i18n('满足任一') }}）
          </span>
          <span v-else-if="value.userGroupOption.optionType === 'ALL'" style="font-size: 12px; color: #5A5F66">
            （{{ i18n('满足全部') }}）
          </span>
        </div>
        <div style="font-weight: 400;color: #323233;font-size: 13px;">{{ getGroupValueStr }}</div>
      </div>
      <div class="dtl-item" v-if="hasDtlMemberValue">
        <div class="select-head">
          {{ i18n('/会员/洞察/客群管理/新建页/条件抽屉/会员属性') }}
          <span style="font-size: 12px; color: #5A5F66">
            （{{ i18n('同时满足') }}）
          </span>
        </div>
        <div v-if="attrInitLoadFlag" style="font-weight: 400;color: #323233;font-size: 13px; white-space: break-spaces;">
          <div v-for="(item) in value.memberRule.props" :key="item.prop">
            {{  getMemberRuleText(item) }}
          </div>
        </div>
      </div>
    </div>
    <el-dialog :title="i18n('选择人群')" width="1050px" :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false"
      :visible.sync="visible" class="select-group-dialog">
      <div class="container-block">
        <div class="left-tabs">
          <div class="tab-item" :style="{'word-break': isEnglish ? 'keep-all' : 'normal'}" :class="{ 'tab_active': tabSelect === item.value }" v-for="(item, index) in selectTabs" :key="index"
            @click="changeTabSelect(item.value)">
            {{ item.name }}
          </div>
        </div>
        <div class="middle-form">
          <!-- 标签选区 -->
          <template v-if="tabSelect === 'label'">
            <div class="top-form">
              <el-radio-group v-model="labelForm.fulfillType" :disabled="loading">
                <el-radio label="one">{{ i18n('满足任一') }}</el-radio>
                <el-radio label="all">{{ i18n('满足全部') }}</el-radio>
              </el-radio-group>
            </div>
            <div class="search-block">
              <ChannelSelect v-model="labelForm.filterInfo.labelType" @change="queryTag" :clearable="false" :multiple="false" :isShowAll="true"
                width="144px">
              </ChannelSelect>
              <el-select v-model="labelForm.filterInfo.dataType" @change="queryTag" style="width: 144px;margin-left: 8px">
                <el-option :value="null" :label="i18n('全部类型')"></el-option>
                <el-option value="checkbox" :label="i18n('多选')"></el-option>
                <el-option value="singleChoice" :label="i18n('单选')"></el-option>
                <el-option value="text" :label="i18n('/会员/洞察/标签管理/手工标签/新建页/文本')"></el-option>
                <el-option value="date" :label="i18n('/会员/洞察/标签管理/手工标签/新建页/日期')"></el-option>
                <el-option value="number" :label="i18n('/会员/洞察/标签管理/手工标签/新建页/数值')"></el-option>
              </el-select>
              <el-input :placeholder="i18n('按回车搜索标签')" v-model="labelForm.filterInfo.inputValue" @keydown.native="onEnter($event, 'label')"
                style="flex: 1;margin-left: 8px">
                <i slot="suffix" @click="queryTag" class="el-icon-search"></i>
              </el-input>
            </div>
            <div class="form-block">
              <div class="label-item" v-for="(item) in tagList" :key="item.uuid">
                <div class="label-type" @click="doExpendTag(item)">
                  <div class="label-icon">
                    <i class="el-icon-caret-right" v-if="!item.isExpand" />
                    <i class="el-icon-caret-bottom" v-else />
                  </div>
                  {{item.name}}
                </div>
                <div class="label-content" v-if="item.isExpand">
                  <!-- 单选 -->
                  <template v-if="item.tagType === 'singleChoice'">
                    <div class="check-item" :title="tagItem" :class="{ selected: item.tagValue && item.tagValue.includes(tagItem) }"
                      v-for="(tagItem, ind) in item.tagValues" :key="ind" @click="doSelectLabel('multi', item.uuid, tagItem)">
                      {{ tagItem }}
                    </div>
                  </template>
                  <!-- 多选 -->
                  <template v-else-if="item.tagType === 'checkbox'">
                    <div class="check-item" :title="tagItem" :class="{ selected: item.tagValue && item.tagValue.includes(tagItem) }" :key="ind"
                      v-for="(tagItem, ind) in item.tagValues" @click="doSelectLabel('multi', item.uuid, tagItem)">
                      {{ tagItem }}
                    </div>
                  </template>
                  <!-- 时间 -->
                  <template v-else-if="item.tagType === 'date'">
                    <el-date-picker type="daterange" range-separator="-" v-model="item.tagValue" @change="updateOriginData($event, item)"
                      value-format="yyyy-MM-dd" format="yyyy-MM-dd" :start-placeholder="formatI18n('/公用/查询条件/提示/开始日期')"
                      :end-placeholder="formatI18n('/公用/查询条件/提示/结束日期')">
                    </el-date-picker>
                  </template>
                  <!-- 文本 -->
                  <template v-else-if="item.tagType === 'text'">
                    <div class="check-item" :title="tagItem" :class="{ selected: item.tagValue && item.tagValue.includes(tagItem) }" :key="ind"
                      v-for="(tagItem, ind) in item.tagValues" @click="doSelectLabel('text', item.uuid, tagItem)">
                      {{ tagItem }}
                    </div>
                  </template>
                  <!-- 数值 -->
                  <template v-else-if="item.tagType === 'number'">
                    <div style="display: flex; align-items: center">
                      <AutoFixInput v-model="item.tagValue[0]" :min="0" :max="99999999.99" :fixed="2" :placeholder="i18n('/公用/菜单/请输入')"
                        @change="updateOriginData($event, item)" @blur="checkNumberMinMax(item, 'min')" style="width: 120px" /> -
                      <AutoFixInput v-model="item.tagValue[1]" :min="Number(item.tagValue[0]) || 0" :max="99999999.99" :fixed="2"
                        @change="updateOriginData($event, item)" @blur="checkNumberMinMax(item, 'max')" :placeholder="i18n('/公用/菜单/请输入')"
                        style="width: 120px" />
                    </div>
                  </template>
                </div>
              </div>
            </div>
          </template>
          <!-- 客群选区 -->
          <template v-else-if="tabSelect === 'group'">
            <div class="top-form">
              <el-radio-group v-model="groupForm.fulfillType" :disabled="loading">
                <el-radio label="one">{{ i18n('满足任一') }}</el-radio>
                <el-radio label="all">{{ i18n('满足全部') }}</el-radio>
              </el-radio-group>
            </div>
            <div class="search-block">
              <el-input v-model="groupForm.filterInfo.inputValue" @keydown.native="onEnter($event, 'group')" :placeholder="i18n('按回车搜索客群')"
                style="width: 300px">
                <i slot="suffix" @click="queryGroup" class="el-icon-search"></i>
              </el-input>
            </div>
            <div class="form-block">
              <el-checkbox-group v-model="groupForm.filterInfo.groupList">
                <el-checkbox v-for="(item, index) in groupArr" :label="item.uuid" :key="index">{{ item.name
                  }}</el-checkbox>
              </el-checkbox-group>
            </div>
          </template>
          <!-- 会员属性 -->
          <div v-show='tabSelect === "member"'>
            <MemberPropSelect v-model="memberRule" ref="memberPropSelectRef" :loading="loading"></MemberPropSelect>
          </div>
        </div>
        <div class="right-plat">
          <div class="top-line">
            <span>{{ i18n('/会员/选择人群/已选{0}项', [totalSelectNum]) }}</span>
            <span class="span-btn" @click="doClear">{{ i18n('/公用/按钮/清空') }}</span>
          </div>
          <div class="select-content">
            <div class="select-block">
              <!-- 标签已选 -->
              <template>
                <div class="select-head" v-if="haveSelectTag">
                  {{ i18n('标签') }}
                  <span v-if="labelForm.fulfillType === 'one'" style="font-size: 12px; color: #5A5F66">
                    （{{ i18n('满足任一') }}）
                  </span>
                  <span v-else-if="labelForm.fulfillType === 'all'" style="font-size: 12px; color: #5A5F66">
                    （{{ i18n('满足全部') }}）
                  </span>
                </div>
                <div class="select-text" v-for="(item) in selectTagList" :key="item.uuid">
                  <el-tooltip effect="dark" placement="top-start" :content="getTagText(item)">
                    <span class="ellipsis" style="display: inline-block; width: 190px">{{ getTagText(item) }}</span>
                  </el-tooltip>
                  <i class="el-icon-delete" style="cursor: pointer" @click="doRemoveTag(item)"></i>
                </div>
              </template>
              <!-- 客群已选 -->
              <template>
                <div class="select-head" v-if="groupForm.filterInfo.groupList.length">
                  {{ i18n('/会员/洞察/客群/列表页/客群') }}
                  <span v-if="groupForm.fulfillType === 'one'" style="font-size: 12px; color: #5A5F66">
                    （{{ i18n('满足任一') }}）
                  </span>
                  <span v-else-if="groupForm.fulfillType === 'all'" style="font-size: 12px; color: #5A5F66">
                    （{{ i18n('满足全部') }}）
                  </span>
                </div>
                <div class="select-text" v-for="(item) in selectGroupList" :key="item.uuid">
                  <el-tooltip effect="dark" placement="top-start" :content="item.name">
                    <span class="ellipsis" style="display: inline-block; width: 190px">{{ item.name }}</span>
                  </el-tooltip>
                  <i class="el-icon-delete" style="cursor: pointer" @click="doRemoveGroup(item)"></i>
                </div>
              </template>
              <!-- 会员属性已选 -->
              <template>
                <div class="select-head" v-if="haveSelectMember">
                  {{ i18n('/会员/洞察/客群管理/新建页/条件抽屉/会员属性') }}
                  <span style="font-size: 12px; color: #5A5F66">
                    （{{ i18n('同时满足') }}）
                  </span>
                </div>
                <div class="select-text" v-for="(item,index) in memberRule.props" :key="item.prop">
                  <el-tooltip effect="dark" placement="top-start" :content="getMemberRuleText(item)">
                    <span class="ellipsis" style="display: inline-block; width: 190px">{{ getMemberRuleText(item) }}</span>
                  </el-tooltip>
                  <i class="el-icon-delete" style="cursor: pointer" @click="doRemoveMember(index)"></i>
                </div>
              </template>
            </div>
          </div>
          <div class="bottom-tip" :style="{'word-break': isEnglish ? 'keep-all' : 'normal'}">
            {{ i18n('注：标签、客群、会员属性，这3者之间是“或”的关系，“满足任一”组即满足条件') }}
          </div>
        </div>
      </div>
      <div class="footer">
        <el-button @click="doModalClose('cancel')" :loading="loading" size="large">
          {{ i18n('/公用/按钮/取消') }}
        </el-button>
        <el-button @click="doModalClose('confirm')" :loading="loading" type="primary" size="large">
          {{ i18n('/公用/按钮/确定') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" src="./SelectGroupCmp.ts">
</script>

<style lang="scss">
.select-group-cmp {
  position: relative;
  font-family: PingFangSC, PingFang SC;

  .select-head {
    display: flex;
    align-items: center;
    font-size: 13px;
    color: #323233;
    margin-bottom: 8px;
  }

  .select-dtl {
    display: flex;
    align-items: center;
    width: 100%;
    min-height: 22px;
    line-height: 22px;
    font-weight: 400;
    color: #323233;
    font-size: 13px;
    padding-left: 6px;
    word-break: break-all;
  }

  .dtl-block {
    display: flex;
    margin-bottom: 8px;

    .dtl-item {
      width: 400px;
      border-radius: 2px;
      background: #f7f8fa;
      margin-right: 8px;
      padding: 12px;
    }
  }

  .select-group-dialog {
    display: flex;
    align-items: center;
    justify-content: center;

    .container-block {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 1000px;
      height: 612px;

      .left-tabs {
        width: 72px;
        height: 100%;
        border: 1px solid #dde2eb;
        padding: 9px 0;
        box-sizing: border-box;

        .tab-item {
          position: relative;
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 40px;
          font-weight: 400;
          color: #36445a;

          &:nth-of-type(3) {
            line-height: 16px;
          }

          &.tab_active {
            color: #007eff;

            &::after {
              content: "";
              position: absolute;
              right: 0;
              width: 2px;
              height: 40px;
              background: #007eff;
            }
          }

          &:hover {
            cursor: pointer;
          }
        }
      }

      .middle-form {
        flex: 1;
        display: flex;
        flex-direction: column;
        height: 100%;
        border: 1px solid #dde2eb;
        margin-left: -1px;
        padding: 12px;
        box-sizing: border-box;
        overflow-y: auto;

        .top-form {
          display: flex;
          align-items: center;
          height: 32px;
          background: #f7f8fa;
          padding: 0 12px;
        }

        .search-block {
          display: flex;
          align-items: center;
          margin-top: 8px;

          .el-input__suffix {
            display: flex;
            align-items: center;
            cursor: pointer;
          }
        }

        .form-block {
          flex: 1;
          padding: 20px 0;
          box-sizing: border-box;
          overflow-y: scroll;

          .el-checkbox-group {
            display: flex;
            flex-wrap: wrap;
          }

          .el-checkbox {
            flex-basis: 50%;
            margin-right: 0;
            margin-bottom: 8px;
          }
        }

        .label-item {
          display: flex;
          flex-direction: column;
          margin-bottom: 12px;

          .label-type {
            display: flex;
            align-items: center;
            cursor: pointer;

            .label-icon {
              font-size: 12px;
              margin-right: 4px;
            }
          }

          .label-content {
            display: flex;
            flex-wrap: wrap;
            padding-left: 16px;
            margin-top: 8px;

            .check-item {
              max-width: 500px;
              height: 24px;
              line-height: 24px;
              text-align: center;
              padding: 0 12px;
              font-weight: 400;
              color: #242633;
              border-radius: 2px;
              background: #f7f9fc;
              border: 1px solid #d7dfeb;
              margin-right: 4px;
              margin-bottom: 3px;
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
              cursor: pointer;

              &.selected {
                border: 1px solid #007eff;
                background: #e6f2ff;
                color: #007eff;
              }
            }
          }
        }
      }

      .right-plat {
        display: flex;
        flex-direction: column;
        width: 250px;
        height: 100%;
        margin-left: 8px;
        background: #f7f8fa;
        border-radius: 2px;
        padding: 12px;
        box-sizing: border-box;

        .top-line {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }

        .select-content {
          flex: 1;
          overflow-y: scroll;

          .select-block {
            margin-top: 12px;

            .select-text {
              display: flex;
              align-items: center;
              width: 100%;
              height: 28px;
              font-weight: 400;
              color: #323233;
              font-size: 13px;
              padding-left: 6px;
              word-break: break-all;
            }
          }
        }

        .bottom-tip {
          width: 100%;
          border-top: 1px solid #dde2eb;
          font-weight: 400;
          color: #5a5f66;
          font-size: 12px;
          padding-top: 7px;
          box-sizing: border-box;
          word-break: keep-all;
          word-break: break-all;
        }
      }
    }

    .footer {
      display: flex;
      justify-content: flex-end;
      margin-top: 12px;
    }

    .el-dialog {
      height: auto !important;
    }
  }

  .cursor-disable {
    &:hover {
      cursor: no-drop;
    }
  }
}
</style>