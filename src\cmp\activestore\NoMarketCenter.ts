import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import ImportDialog from "cmp/importdialog/ImportDialog.vue";
import IdName from "model/entity/IdName";
import StoreSelectorDialog from "cmp/selectordialogs/StoreSelectorDialog.vue";
import RSOrg from "model/common/RSOrg";
import ImportResultDialog from "pages/deposit/mbrdeposit/active/dialog/ImportResultDialog.vue";
import PromotionCenterSelectorDialog from "cmp/selectordialogs/PromotionCenterSelectorDialog.vue";
import StoreRange from "model/common/StoreRange";
import BrowserMgr from "mgr/BrowserMgr";
import RSMarketingCenter from "model/common/RSMarketingCenter";
import StoreMulPromotionSelectorDialog from "cmp/selectordialogs/StoreMulPromotionSelectorDialog.vue";
import LimitedMarketingCenter from "model/common/LimitedMarketingCenter";
import I18nPage from "common/I18nDecorator";
import ActiveStoreArea from "./ActiveStoreArea";

class CustomStore {
	// 控制营销中心展示
	promotionCenter: boolean;
	storeRange: StoreRange;
}

class ExportResult {
	importResult: boolean;
	backUrl: string;
	errorCount: number;
	ignoreCount: number;
	successCount: number;
}

export { ExportResult };

@Component({
	name: "NoMarketCenter",
	components: {
		ImportDialog,
		StoreSelectorDialog,
		ImportResultDialog,
		PromotionCenterSelectorDialog,
		StoreMulPromotionSelectorDialog,
    ActiveStoreArea
	},
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/权益/积分/积分初始化/未初始化状态/得积分规则/基础得积分/点击立即设置'
  ],
  auto: true
})
export default class NoMarketCenter extends Vue {
	promotionCenter = false; // 是否开启营销中心
	$refs: any;
	store: StoreRange = new StoreRange();
	currentMarketCenter: LimitedMarketingCenter = new LimitedMarketingCenter();
	importDialogShow = false;
	importUrl = "v1/org/importExcel";
	@Prop()
	showTip: boolean;
	// 是否展示与活动门店一致
	@Prop({
		type: Boolean,
		default: false,
	})
	sameStore: boolean;
	reloadList = false;
	inputValueRules: any;
	marketCenterRules: any;

	@Prop({
		type: Boolean,
		default: true,
	})
	internalValidate: boolean;
	@Prop()
	value: CustomStore;
	@Prop()
	values: any;
	importResult: ExportResult = new ExportResult();
	importResultDialogShow = false;

	@Watch("values", { deep: true, immediate: true })
	onValueChange(value: StoreRange) {
		console.log('从父组件拿到的数据',value);

		if (!value.storeRangeType) {
			this.store = new StoreRange();
			this.store.storeRangeType = "ALL";
			this.store.storeRangeLimitType = "STORE";
		} else {
			this.store = JSON.parse(JSON.stringify(value));
      if(value.storeRangeType === 'PART' && value.zones?.length) {
        this.store.storeRangeType = "ZONE"
      }
			this.doSetIdName();
		}
	}
	get marketCenterId() {
		if (this.currentMarketCenter && this.currentMarketCenter.marketingCenter) {
			return this.currentMarketCenter.marketingCenter.id;
		}
		return null;
	}
	get marketCenterName() {
		if (this.currentMarketCenter && this.currentMarketCenter.marketingCenter) {
			return this.currentMarketCenter.marketingCenter.name;
		}
		return null;
	}
	get templatePath() {
		if (location.href.indexOf("localhost") === -1) {
			return "template_specify_stores.xlsx";
		} else {
			return "template_specify_stores.xlsx";
		}
	}
	created() {
		if (this.sameStore) {
			this.store.storeRangeType = "SAME";
		}
		let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
		if (sysConfig) {
			this.promotionCenter = sysConfig.enableMultiMarketingCenter;
			// this.promotionCenter = false
			// this.promotionCenter = true
		}
		this.inputValueRules = [
			{
				validator: (rule: any, value: string, callback: any) => {
					if (this.promotionCenter) {
						if (this.store.storeRangeType === "ALL" || this.store.storeRangeType === "SAME") {
							callback();
							return;
						}
						if (!value || value.length === 0) {
							callback(new Error(this.formatI18n("/营销/积分活动/积分活动/商品满额加送积分/表单校验/提示信息/请选择门店范围")));
							return;
						}
					} else {
            if (this.store.storeRangeType !== "ALL" && this.store.stores.length === 0 && this.store.zones.length === 0) {
							callback(new Error(this.formatI18n("/营销/积分活动/积分活动/商品满额加送积分/表单校验/提示信息/请选择门店范围")));
						}
					}
					callback();
				},
				trigger: ["change", "blur"],
			},
		];
		this.marketCenterRules = [
			{
				validator: (rule: any, value: string, callback: any) => {
					if (this.promotionCenter) {
						if (!this.store.marketingCenters || this.store.marketingCenters.length === 0) {
							callback(new Error(this.formatI18n("/营销/积分活动/积分活动/商品满额加送积分/表单校验/提示信息/请选择营销中心范围")));
							return;
						}
					}
					callback();
				},
				trigger: ["change", "blur"],
			},
		];
	}

	validate() {
    return Promise.all([this.$refs.form.validate(), this.$refs.activeStoreArea?.doValidate()]);
	}

	doClearPromCenter(marketCenter: LimitedMarketingCenter) {
		marketCenter.storesValue = "";
		marketCenter!.stores!.stores = [];
		this.submit();
		this.$forceUpdate();
	}

	doImport() {
		this.importDialogShow = true;
	}
	doSelect() {
		let goodsData: RSOrg[] = [];
		if (this.store && this.store.stores && this.store.stores.length > 0) {
			this.store.stores.forEach((item: IdName) => {
				let obj: RSOrg = new RSOrg();
				obj.org = new IdName();
				obj.org.id = item.id;
				obj.org.name = item.name;
				goodsData.push(obj);
			});
		}
		this.$refs.selectGoodsScopeDialog.open(goodsData, "multiple");
	}
	openGoodsDialog(marketCenter: LimitedMarketingCenter) {
		this.currentMarketCenter = marketCenter;
		let selected: RSOrg[] = [];
		if (marketCenter.storesValue) {
			let arr = marketCenter.storesValue.split(";");
			for (let i = 0; i < arr.length; i++) {
				if (arr[i]) {
					let obj: RSOrg = new RSOrg();
					obj.org = new IdName();
					if (arr[i].split("[") && arr[i].split("[")[1]) {
						let subArr = arr[i].split("[")[1].split("]")[0];
						obj.org.name = subArr;
					}
					obj.org.id = arr[i].split("[")[0];
					selected.push(obj);
				}
			}
		}
		this.$refs.mulPromotionStore.open(selected, "multiple");
	}
	openPromotionCenterDialog() {
		// todo 这里缺少  营销中心类型  需要调整
		this.$refs.selectPromotionCenterSelectorDialog.open(this.store.marketingCenters, "multiple");
	}
	doPromotionSubmitGoods(arr: RSMarketingCenter[]) {
		// todo 这里缺少  营销中心类型  需要调整
		let marketingCentersMap: any = {};
		for (let item of this.store.marketingCenters) {
			if (item!.marketingCenter!.id) {
				marketingCentersMap[item!.marketingCenter!.id] = item;
			}
		}
		let result = [];
		for (let mc of arr) {
			let id = mc!.marketingCenter!.id;
			if (!id) {
				continue;
			}
			if (marketingCentersMap[id]) {
				// 没选就添加
				result.push(marketingCentersMap[id]);
			} else {
				let center = new LimitedMarketingCenter();
				center.stores = new StoreRange();
				center.marketingCenter = mc.marketingCenter;
				result.push(center);
			}
		}
		this.store.marketingCenters = result;
		this.reloadList = true;
		this.$nextTick(() => {
			this.doEmitInput();
		});
		setTimeout(() => (this.reloadList = false), 10);
	}
	doMulStoreSubmitGoods(arr: RSOrg[]) {
		this.doSubmitGoods(arr);
	}
	doSubmitGoods(arr: RSOrg[]) {
		let stores: IdName[] = [];
		let str = "";
		if (arr && arr.length > 0) {
			arr.forEach((item: any) => {
				if (item && item.org && item.org.id) {
					str += item.org.id + `[${item.org.name}];`;
					let obj: IdName = new IdName();
					obj.id = item.org.id;
					obj.name = item.org.name;
					stores.push(obj);
				}
				if (item && item.id) {
					str += item.id + `[${item.name}];`;
					let obj: IdName = new IdName();
					obj.id = item.id;
					obj.name = item.name;
					stores.push(obj);
				}
			});
		}
		if (this.promotionCenter) {
			if (!this.currentMarketCenter.stores) {
				this.currentMarketCenter.stores = new StoreRange();
			}
			this.currentMarketCenter.stores.stores = stores;
			this.$nextTick(() => {
				this.currentMarketCenter.storesValue = str;
				this.$forceUpdate();
				this.doEmitInput();
			});
		} else {
			this.store.stores = stores;
			this.$nextTick(() => {
				this.doEmitInput();
			});
		}
		this.$refs.form.validate();
	}
	doStoreRange() {
		if (this.store.storeRangeType === "ALL") {
			this.$emit("commit", {});
			this.$emit("change");
		} else if (this.store.storeRangeType === "PART") {
			this.store = new StoreRange();
			this.store.storeRangeType = "PART";
			this.doEmitInput();
		} else if (this.store.storeRangeType === "EXCLUDE") {
			this.store = new StoreRange();
			this.store.storeRangeType = "EXCLUDE";
			this.doEmitInput();
    } else if (this.store.storeRangeType === "ZONE") {
      this.store = new StoreRange();
      this.store.storeRangeType = "ZONE";
      this.doEmitInput();
    } else {
			this.store = new StoreRange();
			this.store.storeRangeType = "SAME";
			this.doEmitInput();
		}
		this.$refs.form.validate();
	}
	doPromCenterStoreRange(marketCenter: LimitedMarketingCenter) {
		let storeRangeType = marketCenter!.stores!.storeRangeType;
		marketCenter.storesValue = "";
		marketCenter.stores = new StoreRange();
		marketCenter.stores.storeRangeType = storeRangeType;
		marketCenter.stores.stores = [];
		this.doEmitInput();
		this.$refs.form.validate();
	}
	/**
	 * 导入成之后
	 * @param response
	 */
	doUploadSuccess(response: any) {
		if (response.response.code === 2000) {
			this.importResultDialogShow = true;
			this.importResult = new ExportResult();
			if (response.response.data) {
				this.importResult.importResult = response.response.data.success;
				this.importResult.backUrl = response.response.data.backUrl;
				this.importResult.errorCount = response.response.data.errorCount;
				this.importResult.ignoreCount = response.response.data.ignoreCount;
				this.importResult.successCount = response.response.data.successCount;
				this.doSubmitGoods(response.response.data.orgs);
			}
		} else {
			this.$message.error(response.response.msg);
		}
	}
	getStoreCount(count: number) {
		let str: any = this.formatI18n("/公用/券模板", "已选择{0}家门店");
		str = str.replace(/\{0\}/g, count);
		return str;
	}
	private doSetIdName() {
		if (!this.store.marketingCenters) {
			return;
		}
		for (let marketingCenter of this.store.marketingCenters) {
			let arr = marketingCenter!.stores!.stores;
			let str = "";
			if (arr && arr.length > 0) {
				if (arr && arr.length > 0) {
					arr.forEach((item: any) => {
						if (item && item.org && item.org.id) {
							str += item.org.id + `[${item.org.name}];`;
						}
						if (item && item.id) {
							str += item.id + `[${item.name}];`;
						}
					});
				}
			}
			marketingCenter.storesValue = str;
		}
	}
	private doEmitInput() {
		this.doSetStoreRangeLimitType();
		this.submit();
	}
	private submit() {
		console.log(this.store);
		this.$emit("commit", this.store);
		this.$emit("change");
	}
  getAreaData(data: StoreRange) {
    if (data.marketingCenters[0]?.stores?.storeRangeLimitType === 'ZONE') {
      //ActiveStoreArea组件提交的数据中，zones是存放在data.marketingCenters[0].stores.zones里的
      data.storeRangeType = "ZONE"
      data.zones = data.marketingCenters[0]?.stores?.zones || []
      data.marketingCenters = []
      data.storeRangeLimitType = "ZONE"
    }
    this.store = JSON.parse(JSON.stringify(data))
    data.storeRangeType = "PART"
    console.log('NoMarketCenter收到的数据',data,this.store);
    this.$refs.form.validate();
    this.$emit("commit", data);
    this.$emit("change");
  }
	private doSetStoreRangeLimitType() {
		if (this.promotionCenter) {
			this.store.storeRangeLimitType = "MARKETING_CENTER";
		} else {
			this.store.storeRangeLimitType = "STORE";
		}
		if (this.store.marketingCenters && this.store.marketingCenters.length > 0) {
			for (let item of this.store.marketingCenters) {
				item.stores!.storeRangeLimitType = "STORE";
			}
		}
	}
}
