<!--
 * @Description:
 * @Version: 1.0
 * @Autor: 司浩
 * @Date: 2021-10-18 19:41:13
 * @LastEditors: 苏国友 <EMAIL>
 * @LastEditTime: 2023-04-10 11:45:08
-->
<template>
  <div
    class="commodity_label"
    :class="{
      add: tagType === 'add',
      normal: tagType === 'normal',
      edit: tagType === 'edit',
      editFocus: tagType === 'edit' && isFocus
    }"
    @click.stop="newLabel"
  >
    <span v-if="tagType === 'add'">+ {{ formatI18n('/权益/券/券模板/新增标签') }}</span>
    <div class="handel" v-if="tagType === 'normal'">
      <span class="one_txt_cut">{{ labelName }}</span>
      <i class="el-icon-edit-outline icon_ic_edit" @click.stop="editLabel"></i>
      <i class="el-icon-delete icon_ic_delete" @click.stop="deleteLabel"></i>
    </div>
    <div class="edit" v-if="tagType === 'edit'">
      <input
        ref="input"
        type="text"
        v-model.trim="labelText"
        :placeholder="formatI18n('/权益/券/券模板/32个字符以内')"
        :maxlength="32"
        @focus="isFocus = true"
        @blur="isFocus = false"
        @input="handelChange"
      />
      <span @click.stop="sureEdit">{{ formatI18n('/权益/券/券模板/确认') }}</span>
      <span @click.stop="cancelEdit">{{ formatI18n('/权益/券/券模板/取消') }}</span>
    </div>
  </div>
</template>

<script lang="ts" src="./commodityLabel.ts"></script>

<style scoped lang="scss">
  .commodity_label {
    position: relative;
    width: 200px;
    height: 40px;
    border-radius: 100px 0 0 100px;
    border: 1px solid #d7dfeb;
    box-sizing: border-box;
    padding-left: 32px;
    display: flex;
    align-items: center;
    cursor: pointer;
    background: #f7f9fc;
    color: #79879e;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: 12px;
      display: inline-block;
      width: 10px;
      height: 10px;
      background: #fff;
      border: 1px solid #d7dfeb;
      border-radius: 100%;
      box-sizing: border-box;
    }

    .handel {
      display: flex;
      align-items: center;
      width: 100%;

      .one_txt_cut {
        color: #36445a;
        flex: 1;
      }

      .icon_ic_edit,
      .icon_ic_delete {
        color: #a1b0c8;
        width: 16px;
        height: 16px;
        margin-right: 8px;
        display: none;
      }
    }

    .edit {
      display: flex;
      align-items: center;
      width: 100%;

      input {
        outline: none;
        border: none;
        height: 30px;
        background-color: transparent;
        width: 80px;
        padding: 0;
        margin-right: 10px;

        &::-webkit-input-placeholder {
          /* WebKit, Blink, Edge */
          color: #a1b0c8;
        }
        &:-moz-placeholder {
          /* Mozilla Firefox 4 to 18 */
          color: #a1b0c8;
        }
        &:-ms-input-placeholder {
          /* Internet Explorer 10-11 */
          color: #a1b0c8;
        }
      }

      span:nth-child(2) {
        user-select: none;
        color: #309af6;
        margin-right: 8px;
      }

      span:nth-child(3) {
        user-select: none;
        color: #36445a;
      }
    }

    &.add {
      background: #fff;
      border: 1px dashed #d7dfeb;

      &::before {
        border: 1px dashed #d7dfeb;
      }

      &:hover {
        border-color: #309af6;
        color: #309af6;

        .one_txt_cut {
          color: #309af6;
        }

        &::before {
          border-color: #309af6;
        }
      }
    }

    &.normal {
      &:hover {
        box-shadow: 0 1px 0 0 #a1b0c8;

        &::before {
          box-shadow: 0 2px 0 0 #a1b0c8;
          border: 1px solid #a1b0c8;
        }

        .handel {
          .icon_ic_edit,
          .icon_ic_delete {
            display: inline-block;
          }

          .icon_ic_edit:hover {
            color: #309af6;
          }

          .icon_ic_delete:hover {
            color: #fa464c;
          }
        }
      }
    }

    &.edit.editFocus {
      box-shadow: 0 1px 0 0 #77bdf9;
      border: 1px solid rgba(48, 154, 246, 0.64);

      input {
        caret-color: #309af6;
      }

      &::before {
        box-shadow: 0 2px 0 0 #77bdf9;
        border: 1px solid #77bdf9;
      }
    }
  }
</style>
