import ConstantMgr from "mgr/ConstantMgr";
import JumpPageInfo from "model/navigation/JumpPageInfo";

class DefaultPagePlaceProperty {
  // 头部
  static pageTitle() {
    return {
      propShareImageUrl: "",
      propShareStrategy: "support",
      propShowCart: true,
      propShowTop: true,
      propTitle: "",
      styBgColor: "#F5F6F5",
      styCustomBgColor: "",
      id: "titleCmp",
    }
  }
  // 图片组件
  static shouyeimage() {
    return {
      id: "gif",
      type: "image",
      uuid: "",
      name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "单栏图片"),
      propBegin: null,
      propEnd: null,
      propDateRange: [],
      propCycle: false,
      propCycleConditions: [],
      styMarginTop: 0,
      styMarginBottom: 20,
      styMarginLeft: 24,
      styMarginRight: 24,
      propImages: [
        {
          imageUrl: "",
          jumpPageInfo: new JumpPageInfo(),
        }
      ],
    }
  }
  //双栏图片
  static doubleColumnImage() {
    return {
      id: "doubleColumnImage",
      type: "image",
      uuid: "",
      name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "双栏图片"),
      propBegin: null,
      propEnd: null,
      propDateRange: [],
      propCycle: false,
      propCycleConditions: [],
      styMarginTop: 0,
      styMarginBottom: 20,
      styMarginLeft: 24,
      styMarginRight: 24,
      propImages: [
        {
          imageUrl: "",
          jumpPageInfo: new JumpPageInfo(),
        },
        {
          imageUrl: "",
          jumpPageInfo: new JumpPageInfo(),
        }
      ],
    }
  }
  //多栏图片
  static multiColumnImage() {
    return {
      id: "multiColumnImage",
      type: "image",
      uuid: "",
      name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "多栏图片"),
      propBegin: null,
      propEnd: null,
      propDateRange: [],
      propCycle: false,
      propCycleConditions: [],
      styMarginTop: 0,
      styMarginBottom: 20,
      styMarginLeft: 24,
      styMarginRight: 24,
      propImages: [
        {
          imageUrl: "",
          jumpPageInfo: new JumpPageInfo(),
        },
        {
          imageUrl: "",
          jumpPageInfo: new JumpPageInfo(),
        },
        {
          imageUrl: "",
          jumpPageInfo: new JumpPageInfo(),
        }
      ],
    }
  }
  // 轮播图组件
  static swiperImage() {
    return {
      id: "rotation",
      type: "image",
      uuid: "",
      name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "轮播图"),
      propBegin: null,
      propEnd: null,
      propDateRange: [],
      propCycle: false,
      propCycleConditions: [],
      // 轮播图间隔时间
      propInterval: '3',
      styMarginTop: 0,
      styMarginBottom: 20,
      styMarginLeft: 0,
      styMarginRight: 0,
      propImages: [
        {
          imageUrl: "",
          jumpPageInfo: new JumpPageInfo(),
        },
      ],
    }
  }
  // 会员轮播图
  static membersSwiperImage() {
    return {
      id: "memberRotation",
      type: "member",
      uuid: "",
      name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "会员组件+轮播图"),
      propBegin: null,
      propEnd: null,
      propDateRange: [],
      propCycle: false,
      propCycleConditions: [],
      styMarginTop: 0,
      styMarginBottom: 20,
      styMarginLeft: 0,
      styMarginRight: 0,
      propShowAvatar: true,
      propShowNickname: true,
      propShowPhoneNumber: false,
      propShowEditPhoneNumber: false,
      propShowMemberLevel: false,
      propShowMemberCode: false,
      propShowPoint: false,
      propPointText: new ConstantMgr.MenusFuc().format("/页面/页面管理", "积分"),
      propShowBalance: false,
      propBalanceText: new ConstantMgr.MenusFuc().format("/页面/页面管理", "储值"),
      propShowCoupon: false,
      propCouponText: new ConstantMgr.MenusFuc().format("/页面/页面管理", "优惠券"),
      propShowPrepaidCard: false,
      propPrepaidCardText: new ConstantMgr.MenusFuc().format("/页面/页面管理", "预付卡"),
      propMemberRightsOrder: ['point', 'balance', 'coupon', 'prepaidCard'],
      propshowPaidMember: false,
      propPaidMemberCards: [],
      propPaidMemberText: new ConstantMgr.MenusFuc().format("/页面/页面管理", "立即开卡，尊享超值权益"),
      propShowRotation: false,
      propImages: [{
        imageUrl: '',
        jumpPageInfo: new JumpPageInfo()
      }],
      propMemberCodeJumpPageInfo: new JumpPageInfo(),
      propBalanceJumpPageInfo: new JumpPageInfo(),
      propAvatarIcon: '', //头像图标
      propPointIcon: '',  //积分图标
      propBalanceIcon: '', //储值图标
      propCouponIcon: '', //优惠券图标
      propPrepaidCardIcon: '', //预付卡图标
    }
  }

  static equityCard(){
    return {
        id: 'equityCard',
        type: "member",
        uuid: "",
        name: "",
        styMarginTop: 0,
        styMarginBottom: 20,
        styMarginLeft: 0,
        styMarginRight: 0,
        propEquityCard: '',  // 权益卡
        propOpenCardText:  new ConstantMgr.MenusFuc().format("/页面/页面管理", "立即开卡，尊享超值权益"),//'立即开卡，尊享超值权益', //  开卡文案
        propSelectColorStyle: 'default', // 内容颜色设置方式（默认、自定义）  default、 custom
        styContentBgColor: '#FDD3A0', // 内容背景色
        styContentTextColor: '#4F4E5D', // 内容文本色
        styContentButtonColor:'#5E5E6C', // 内容按钮色
    }
  }
  // 优惠券
  static coupon() {
    return {
      id: 'crmCoupon',
      type: 'promotion',
      uuid: '',
      name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "优惠券"),
      propBegin: null,
      propEnd: null,
      propDateRange: [],
      propCycle: false,
      propCycleConditions: [],
      styMarginTop: 0,
      styMarginBottom: 20,
      styMarginLeft: 24,
      styMarginRight: 24,
      propActivityType: '',
      propActivityRange: 'all',
      propActivityIds: [],
      propBuyBtn: 'grabPurchase',
      propShowStyle: '1',
      propLayoutStyle: '1',
      propShowClaimedAndAvailableQty: false,
    }
  }
  // 电子卡
  static electroniccard() {
    return {
      id: "electronicCard",
      type: "promotion",
      uuid: "",
      name: "",
      propBegin: null,
      propEnd: null,
      propDateRange: [],
      propCycle: false,
      propCycleConditions: [],
      styMarginTop: 0,
      styMarginBottom: 20,
      styMarginLeft: 24,
      styMarginRight: 24,
      propActivityRange: "all",
      propActivityIds: [],
    }
  }
  // 集点
  static collectionPoint() {
    return {
      id: "collectionPoint",
      type: "promotion",
      uuid: "",
      name: "",
      propBegin: null,
      propEnd: null,
      propDateRange: [],
      propCycle: false,
      propCycleConditions: [],
      styMarginTop: 0,
      styMarginBottom: 20,
      styMarginLeft: 24,
      styMarginRight: 24,
      propActivityRange: "all",
      propActivityIds: [],
    }
  }
  // 大转盘
  static roulette() {
    return {
      id: "bigTurntable",
      type: "promotion",
      uuid: "",
      name: "",
      propBegin: null,
      propEnd: null,
      propDateRange: [],
      propCycle: false,
      propCycleConditions: [],
      styMarginTop: 0,
      styMarginBottom: 20,
      styMarginLeft: 24,
      styMarginRight: 24,
      propActivityRange: 'all',
      propActivityIds: []
    }
  }
  // 拼团抽奖
  static groupBooking() {
    return {
      id: "shareGroupLottery",
      type: "promotion",
      uuid: "",
      name: "",
      propBegin: null,
      propEnd: null,
      propDateRange: [],
      propCycle: false,
      propCycleConditions: [],
      styMarginTop: 0,
      styMarginBottom: 20,
      styMarginLeft: 24,
      styMarginRight: 24,
      propActivityRange: 'all',
      propActivityIds: []
    }
  }
  // 图片广告组件
  static imagead() {
    return {
      id: 'imageAd',
      type: 'image',
      uuid: '',
      name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "图片广告"),
      propBegin: null,
      propEnd: null,
      propDateRange: [],
      propCycle: false,
      propCycleConditions: [],
      styMarginTop: 0,
      styMarginBottom: 20,
      styMarginLeft: 24,
      styMarginRight: 24,
      propImageUrl: '',
      propImageWidth: '',
      propImageHeight: '',
      propItems: []
    }
    // {
    //   hotZoneWidth: '100',
    //   hotZoneHeight: '100',
    //   leftVertexMarginTop: '',
    //   leftVertexMarginLeft: '',
    //   zIndex: '',
    //   jumpPageInfo: new JumpPageInfo()
    // }
  }
  // 页面导航
  static pageNavigation() {
    return {
      id: 'pageNavigation',
      type: 'other',
      uuid: '',
      name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "页面导航"),
      propBegin: null,
      propEnd: null,
      propDateRange: [],
      propCycle: false,
      propCycleConditions: [],
      styMarginTop: 0,
      styMarginBottom: 20,
      styMarginLeft: 0,
      styMarginRight: 0,
      styNavigationBgColor: '#ffffff',
      styColor: 'rgb(83, 80, 80)',
      styActiveColor: 'rgb(0, 101, 255)',
      styActiveUnderlineColor: 'rgb(0, 101, 255)',
      styActiveBgColor: 'rgb(0, 101, 255)',
      propEnlargeActiveFont: false,
      propNavigationType: 'inside',
      propNavigationList: [
        {
          componentUuid: '',
          navigationStyle: 'text',
          name: '',
          image: '',
          icon: '',
          jumpPageInfo: new JumpPageInfo()
        },
        {
          componentUuid: '',
          navigationStyle: 'text',
          name: '',
          image: '',
          icon: '',
          jumpPageInfo: new JumpPageInfo()
        },
      ],
    }
  }
  // pageNavigation: {
  //   propDateRange: [],
  //   propCycle: false,
  //   propCycleConditions: [
  //     {
  //       beginTime: '',
  //       endTime: '',
  //     },
  //   ],
  //   propHideSoldOut: false,
  //   propImportShopSkus: [],
  //   propIsPackUp: false,
  //   propIsPackUpLine: undefined,
  //   propMarginBottom: 20,
  //   propTargetOperator: 'INCLUDE',
  //   propTitle: '',
  //   propWidgetName: '页面导航',
  //   name: '页面导航',
  //   styBottomAngle: 'smallFillet',
  //   styBtnBgColor: '#A8A8B6',
  //   styBtnColor: '#FFFFFF',
  //   styListBgColor: '#FFFFFF',
  //   styProductSubTitleColor: '#A8A8B6',
  //   styProductTitleColor: '#212224',
  //   stySeeMoreColor: '#000000',
  //   styShow: 'one',
  //   styShowSetting: '',
  //   styTitleColor: '#212224',
  //   styTopAngle: 'smallFillet',
  //   styNavigationBgColor: '#ffffff',
  //   styColor: 'rgb(83, 80, 80)',
  //   styActiveColor: 'rgb(0, 101, 255)',
  //   styActiveUnderlineColor: 'rgb(0, 101, 255)',
  //   styActiveBgColor: 'rgb(0, 101, 255)',
  //   propEnlargeActiveFont: false,
  //   propNavigationType: 'inside',
  //   // propActiveStyle: 'underline',
  //   propNavigationList: [
  //     {
  //       componentUuid: '',
  //       navigationStyle: 'text',
  //       name: '',
  //       image: '',
  //       targetPage: '',
  //       pageParams: {},
  //     },
  //     {
  //       componentUuid: '',
  //       navigationStyle: 'text',
  //       name: '',
  //       image: '',
  //       targetPage: '',
  //       pageParams: {},
  //     },
  //   ],
  // },
  // 切换门店
  static switchStores() {
    return {
      id: 'switchStore',
      type: 'other',
      uuid: '',
      name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "切换门店"),
      propBegin: null,
      propEnd: null,
      propDateRange: [],
      propCycle: false,
      propCycleConditions: [],
      styMarginTop: 0,
      styMarginBottom: 20,
      styMarginLeft: 0,
      styMarginRight: 0,
      propAuxiliaryText: '',
      styContentColor: '#353535',
      styBackgroundColor: '#FFFFFF',
      propShowStyle: '1'
    }
  }
  // 文本
  static textFron() {
    return {
      id: 'text',
      type: 'text',
      uuid: '',
      name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "文本"),
      propBegin: null,
      propEnd: null,
      propDateRange: [],
      propCycle: false,
      propCycleConditions: [],
      styMarginTop: 0,
      styMarginBottom: 20,
      styMarginLeft: 0,
      styMarginRight: 0,
      propText: '',
      styFontSize: 'large',
      styFontColor: '#000000',
      styTextAlign: 'center'
    }
  }
  // 我的优惠券
  static myCoupon() {
    return {
      id: 'myCoupon',
      type: 'other',
      uuid: '',
      name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "我的优惠券"),
      propBegin: null,
      propEnd: null,
      propDateRange: [],
      propCycle: false,
      propCycleConditions: [],
      styMarginTop: 0,
      styMarginBottom: 20,
      styMarginLeft: 0,
      styMarginRight: 0,
      propTabInfos: [
        {
          type: 'MY_COUPON',  //我的优惠券tab
          customText: new ConstantMgr.MenusFuc().format("/页面/页面管理", "我的券"),
          show: true,
          couponSceneType: 'all',
          couponScenes: []
        },
        {
          type: 'PAY_COUPON',  //支付券tab
          customText: new ConstantMgr.MenusFuc().format("/页面/页面管理", "支付券"),
          show: true,
          couponSceneType: 'all',
          couponScenes: []
        },
        {
          type: 'PLAT_COUPON',  //平台券tab
          customText: new ConstantMgr.MenusFuc().format("/页面/页面管理", "平台券"),
          show: true,
          couponSceneType: 'all',
          couponScenes: []
        },
        {
          type: 'PRESENT_RECORD',  //赠送记录tab
          customText: new ConstantMgr.MenusFuc().format("/页面/页面管理", "赠送记录"),
          show: true,
          couponSceneType: 'all',
          couponScenes: []
        },
      ],
      propCouponStyle: '1'
    }
  }
  // 菜单
  static menu() {
    return {
      id: 'menu',
      type: 'other',
      uuid: '',
      name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "菜单"),
      propDateRange: [],
      styMarginTop: 0,
      styMarginBottom: 20,
      styMarginLeft: 0,
      styMarginRight: 0,
      propCustomMenus: [
        {
          id: 'personalData',
          name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "个人资料"),
          icon: require('@/assets/image/fellow/ic_gerenziliao.png'),
          enable: true,
          systemMenu: true,
          jumpPageInfo: null,
        },
        {
          id: 'accountSettings',
          name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "账户设置"),
          icon: require('@/assets/image/fellow/ic_account.png'),
          enable: true,
          systemMenu: true,
          jumpPageInfo: null,
        },
        {
          id: 'myPoints',
          name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "我的积分"),
          icon: require('@/assets/image/fellow/ic_jifenshangcheng.png'),
          enable: true,
          systemMenu: true,
          jumpPageInfo: null,
        },
        {
          id: 'myStoredValue',
          name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "我的储值"),
          icon: require('@/assets/image/fellow/ic_chuzhichongzhi.png'),
          enable: true,
          systemMenu: true,
          jumpPageInfo: new JumpPageInfo(),
        },
        {
          id: 'myCoupon',
          name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "我的优惠券"),
          icon: require('@/assets/image/fellow/ic_lingquanzhongxin.png'),
          enable: true,
          systemMenu: true,
          jumpPageInfo: null,
        },
        {
          id: 'myGiftCard',
          name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "我的礼品卡"),
          icon: require('@/assets/image/fellow/ic_lipinkashangcheng.png'),
          enable: true,
          systemMenu: true,
          jumpPageInfo: null,
        },
        {
          id: 'exchangeCode',
          name: new ConstantMgr.MenusFuc().format("/页面/页面管理", "兑换码"),
          icon: require('@/assets/image/fellow/ic_duihuanma.png'),
          enable: true,
          systemMenu: true,
          jumpPageInfo: null,
        },
      ],
    }
  }
};

export default DefaultPagePlaceProperty
