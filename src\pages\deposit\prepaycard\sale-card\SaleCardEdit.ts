import AutoFixInput from 'cmp/autofixinput/AutoFixInput';
import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import FormItem from 'cmp/formitem/FormItem';
import SelectStores from 'cmp/selectStores/SelectStores';
import EditType from 'common/EditType';
import I18nPage from 'common/I18nDecorator';
import Employee<PERSON><PERSON> from 'http/employee/EmployeeApi';
import OrgApi from 'http/org/OrgApi';
import SaleCardBillApi from 'http/prepay/card/SaleCardBillApi';
import SystemConfigApi from 'http/systemConfig/SystemConfigApi';
import RoutePermissionMgr from 'mgr/RoutePermissionMgr';
import CardTemplate from 'model/card/template/CardTemplate';
import IdName from 'model/common/IdName';
import RSEmployeeFilter from 'model/common/RSEmployeeFilter';
import RSOrg from 'model/common/RSOrg';
import RSOrgFilter from 'model/common/RSOrgFilter';
import SaleCardBill from 'model/prepay/card/SaleCardBill';
import AmountToFixUtil from 'util/AmountToFixUtil';
import CommonUtil from 'util/CommonUtil';
import { Component, Vue } from 'vue-property-decorator';
@Component({
  name: 'SaleCardEdit',
  components: {
    BreadCrume,
    AutoFixInput,
    FormItem,
    SelectStores
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/卡/卡管理/售卡单',
    '/储值/预付卡/预付卡充值单',
    '/储值/预付卡/预付卡查询/列表页面',
    '/储值/预付卡/卡模板/编辑页面',
    '/储值/预付卡/卡模板/列表页面',
    '/营销/券礼包活动/券礼包活动',
    '/会员/洞察/公共/最近消费属性',
    '/公用/按钮'
  ],
  auto: true
})
export default class SaleCardEdit extends Vue {
  stores: RSOrg[] = []
  editType: string = EditType.CREATE
  ruleForm: SaleCardBill = new SaleCardBill()
  currentCardTemplate: Nullable<CardTemplate> = null
  panelArray: any = []
  rules: any = {}
  $refs: any
  isExist: Boolean = false
  // 支付方式信息
  queryPaymentData: IdName[] = []  //当前列表展示数据
  originPaymentData: IdName[] = [] //原始表单数据
  querySalesclerkData: IdName[] = []  //当前列表展示数据
  originSalesclerkData: IdName[] = [] //原始表单数据
  salesclerkId: Nullable <string> = null

  created() {
    this.initPanelArray()
    this.initRules()
    this.getStore()
    this.initEditType()
    this.getPaymentList()
    this.getSalesclerk()
  }

  private initPanelArray() {
    this.panelArray = [
      {
        name: this.i18n('售卡单'),
        url: 'sale-card-list'
      },
      {
        name: this.i18n('新建售卡单'),
        url: ''
      }
    ]
  }

  private initEditType() {
    let editType = this.$route.query.editType as string
    if (editType) {
      this.editType = editType as string
    }
    if (editType === 'edit') {
      this.panelArray[1].name = this.i18n('编辑售卡单')
      this.getDetail(this.$route.query.billNumber as string)
    }
  }

  // 获取员工
  getSalesclerk() {
    const params = new  RSEmployeeFilter()
    params.page = 0
    params.pageSize = 1000
    EmployeeApi.query(params).then(res =>{
      if (res.data) {
        // console.log("结果：",res.data)
        this.querySalesclerkData = (res.data || []).map((item) => item.employee as any)
        this.originSalesclerkData = (res.data || []).map((item) => item.employee as any)
      }
      // console.log("查询：" , this.querySalesclerkData)
      // console.log("原始：" , this.originSalesclerkData)
    }).catch((error: any) => {
      this.$message.error(error.message)
    })
  }

  // 查询支付方式
  getPaymentList() {
    SystemConfigApi.getPayMethodConfig().then((resp) => {
      if (resp.code === 2000) {
        this.queryPaymentData = resp.data?.payMethods || []
        this.originPaymentData = resp.data?.payMethods || []
      } else {
        throw new Error(resp.msg!)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  //起始卡号输入完毕后，查询对应的卡模板
  doSearchCardTemplate() {
    this.ruleForm.startCardCode = this.ruleForm.startCardCode?.replace(/[^a-zA-Z0-9]/g, '')
    if (!this.ruleForm.startCardCode) return this.currentCardTemplate = null
    SaleCardBillApi.getByStartCode(this.ruleForm.startCardCode || '').then((res)=>{
      if(res.code === 2000) {
        this.currentCardTemplate = res.data || null
        this.isExist = true
      } else {
        this.isExist = false
        this.currentCardTemplate = null
        this.$message.error(res.msg || this.i18n('查询券模板失败'))
      }
    }).catch((error) => this.$message.error(error.message || this.i18n('查询券模板失败')))
  }

  gotoTplDtl() {
    RoutePermissionMgr.openBlank({ name: 'prepay-card-tpl-dtl', query: { number: this.currentCardTemplate?.number } })
  }

  //自动计算总售价
  doComputeTotal() {
    let price = Number(this.ruleForm.price) //售价
    let qty = Number(this.ruleForm.makeQty) //数量
    if (price >= 0 && qty >=0) {
      price = price > 99999999 ? 99999999 : price
      qty = qty > 9999 ? 9999 : qty === 0 ? 1 : qty
      this.ruleForm.total = price * qty
    } else {
      this.ruleForm.total = 0
    }
  }

  handleSelectPayment(row:any) {
    console.log("row",row)
    const selectedOption = this.queryPaymentData.find(option => option.id === row.paymentId);
    if (selectedOption) {
      row.paymentName = selectedOption.name;
    } else {
      row.paymentName = '';
    }
  }

  addPaymentRow() {
    if (this.ruleForm.payments.length < 10) {
      // 添加新行到cashData数组
      this.ruleForm.payments.push({
        paymentId: '', // 默认支付方式为空字符串
        paymentName:'',
        paymentAmount: null  // 默认金额为null
      });
    } else {
      this.$message.warning(this.i18n('最多支持添加10个支付方式'));
    }
  }

  deletePaymentRow(index:number) {
    console.log(index)
    this.ruleForm.payments.splice(index, 1);
  }

  //自动计算售价
  doComputePrice() {
    let total = Number(this.ruleForm.total) //总售价
    let qty = Number(this.ruleForm.makeQty) //数量
    let faceAmount = Number(this.currentCardTemplate.faceAmounts[0]) //面额
    if (total >= 0 && qty >=0) {
      total = total > 99999999 ? 99999999 : total
      qty = qty > 9999 ? 9999 : qty === 0 ? 1 : qty
      let temp = (total/qty).toFixed(3)
      this.ruleForm.price =  Number(temp.substring(0, temp.length-1))
      if (faceAmount && faceAmount != 0) {
        this.ruleForm.discount = (this.ruleForm.price/faceAmount).toFixed(4)
      }
    } else {
      this.ruleForm.price = 0
    }
  }

  querySalesclerk(value:any) {
    if (!value) {
      this.querySalesclerkData = this.originSalesclerkData;
    } else {
      this.querySalesclerkData = this.originSalesclerkData.filter((option) =>
        (option.id.includes(value) || option.name.includes(value))
      );
    }
  }

  // 支付总金额
  get totalPayAmount() {
    if (this.ruleForm.payments.length > 0) {
      return this.ruleForm.payments.reduce((totalPayAmount, payment) => {
        if (payment.paymentAmount) {
          return (Number(totalPayAmount) + Number(payment.paymentAmount)).toFixed(2)
        }
        return totalPayAmount;
      }, 0)
    } else {
      return 0;
    }
  }

  private validatePayment() {
    for (let i = 0; i < this.ruleForm.payments.length; i++) {
      const item = this.ruleForm.payments[i];
        if (item.paymentId == null || item.paymentId  == undefined || item.paymentId  == '') {
          this.$message.warning(this.i18n('请选择支付方式'))
          return false;
        }
        if (item.paymentAmount == null || item.paymentAmount  == undefined) {
          this.$message.warning(this.i18n('请输入支付金额'))
          return false;
        }
    }
    return true; // 所有元素都检查完毕，没有发现空值
}

  private paymentAmountChange(scope:any) {
    scope.row.paymentAmount = AmountToFixUtil.formatAmount(scope.row.paymentAmount, 99999999, 0.00, '')
  }

  private getStore() {
    let params: RSOrgFilter = new RSOrgFilter()
    params.page = 0
    params.pageSize = 0
    OrgApi.query(params).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.stores = resp.data
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  getDetail(billNumber: string) {
    const loading = CommonUtil.Loading()
    SaleCardBillApi.get(billNumber).then((res)=>{
      if(res.code === 2000) {
        this.ruleForm = res.data || new SaleCardBill()
        this.ruleForm.occurredOrg = res.data?.occurredOrg?.id as any
        this.ruleForm.salesclerk = res.data?.salesclerk
        this.salesclerkId = res.data?.salesclerk?.id as any
        this.doSearchCardTemplate()
      } else {
        this.$message.error(res.msg || this.i18n('获取单据属性失败'))
      }
    }).catch((error) => this.$message.error(error.message || this.i18n('内部异常')))
      .finally(() => loading.close())
  }

  handleSelectStores(id: any) {
    if (!id) return null
    const idName = new IdName()
    idName.id = id
    idName.name = this.stores.find(item => item.org.id === id)?.org.name
    return idName
  }

  initRules() {
    this.rules = {
      startCardCode: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
      makeQty: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
      total: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
      // payments: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
    }
  }

  //保存并审核
  saveAndAudit() {
    this.$refs.form.validate().then(() => {
      if (!this.doValidate()) {
        return
      }
      const body = this.doTransParams(this.ruleForm)
      SaleCardBillApi.saveAndAudit(body).then((res) => {
        if (res.code === 2000) {
          this.$message.success(this.i18n('保存并审核成功'))
          this.$router.push({ name: 'sale-card-dtl', query: { billNumber: res.data as any } })
        } else {
          this.$message.error(res.msg || this.i18n('保存并审核失败'))
        }
      }).catch((error) => this.$message.error(error.message || this.i18n('内部异常')))
    })
  }

  //保存
  save(){
    this.$refs.form.validate().then(() => {
      if (!this.doValidate()) {
        return
      }
      const body = this.doTransParams(this.ruleForm)
      const saveFunc = this.editType === 'edit' ? SaleCardBillApi.saveModify : SaleCardBillApi.save
      saveFunc(body).then((res) => {
        if (res.code === 2000) {
          this.$message.success(this.i18n('保存成功'))
          this.$router.push({ name: 'sale-card-dtl', query: { billNumber: this.ruleForm.billNumber || res.data as any } })
        } else {
          this.$message.error(res.msg || this.i18n('保存失败'))
        }
      }).catch((error) => this.$message.error(error.message || this.i18n('内部异常')))
    })
  }

  doTransParams(val: SaleCardBill) {
    const body: SaleCardBill = JSON.parse(JSON.stringify(val))
    body.cardTemplateNumber = this.currentCardTemplate?.number
    body.cardTemplateName = this.currentCardTemplate?.name
    body.occurredOrg = this.handleSelectStores(this.ruleForm.occurredOrg as any)
    if (this.salesclerkId) {
      body.salesclerk = new IdName()
      body.salesclerk.id = this.salesclerkId
      body.salesclerk.name = this.querySalesclerkData.find((item) => item.id === this.salesclerkId)?.name
      }
    return body
  }

  doValidate() {
    if (!this.ruleForm.startCardCode) {
      this.$message.warning(this.i18n('起始卡号不能为空'))
      return false
    }
    if (!this.isExist) {
      this.$message.warning(this.i18n('卡模板不存在'))
      return false
    }
    if (!this.ruleForm.makeQty) {
      this.$message.warning(this.i18n('请填写售卡数量'))
      return false
    }
    if(this.ruleForm.total === null || this.ruleForm.total === undefined || this.ruleForm.total === '') {
      this.$message.warning(this.i18n('请填写总售价'))
      return false
    }
    if (this.ruleForm.payments.length >= 0 && (!this.validatePayment())) {
        return false;
    }
    if (this.totalPayAmount != this.ruleForm.total) {
      this.$message.warning(this.i18n('支付金额与总售价不一致'))
      return false
    }
    return true
  }
};