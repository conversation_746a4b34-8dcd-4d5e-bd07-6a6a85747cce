<template>
  <el-form inline :model="value" ref="form" :label-position="labelPosition">
    <el-form-item :label="label" style="width: 100%; margin-bottom: 0;"></el-form-item>
    <draggable v-model="value.propCustomMenus" @change="handleChange">
      <el-form-item class="item" :class="{isMyStoredValue: item.id === 'myStoredValue'}"  v-for="(item, key) in value.propCustomMenus" :key="key">
        <div class="item-normal">
            <div class="title">{{ item.name }}</div>
          <el-switch v-model="item.enable" @change="handleChange(key)" />
          <el-button class="set" type="text" @click="editForm(item, key)">{{ i18n('编辑') }}</el-button>
          <el-button class="delete" v-if="!item.systemMenu" type="text" @click="deleteForm(key)">{{ i18n('删除') }}</el-button>
          <img class="point" src="@/assets/image/fellow/ic_point.png" />
        </div>
        <div  v-if="item.id === 'myStoredValue' && item.enable && item.jumpPageInfo && hasOptionPermission('/小程序定制页/百联储值/百联储值', '开通')" style="display: flex; margin-left: 20px;">
          <span>跳转页面</span>
          <el-select v-model="item.jumpPageInfo.templateId" @change="changeStoredJumpValue(item.jumpPageInfo)" size="mini">
              <el-option :label="formatI18n('/储值/会员储值/百联储值')" value="BaiLianStoredValue"></el-option>
              <el-option :label="formatI18n('/储值/会员储值/储值')" value="StoredValue"></el-option>
            </el-select>
          </div>
      </el-form-item>
    </draggable>
    <div @click="addDialogShow" class="add_btn">
      +{{ i18n('添加菜单') }}
    </div>

    <el-dialog :title="typeof editIndex == 'number' ? i18n('编辑菜单') : i18n('新建菜单')" :visible.sync="dialogVisible"
      class="inner-dialog-center" append-to-body width="600px">
      <el-form :model="addFormData" ref="addForm" label-width="120px" :rules="rules">
        <el-form-item :label="i18n('名称')" prop="name">
          <el-input style="width: 400px;" type="text" show-word-limit v-model="addFormData.name" :placeholder="i18n('请输入菜单名称')"
            maxlength="10"></el-input>
        </el-form-item>
        <el-form-item :label="i18n('图标')" prop="icon">
          <div class="tips">{{ i18n('建议尺寸210px*210px，支持jpg/jpeg/png，大小不超过200KB') }}</div>
          <upload-img v-model="addFormData.icon" :isShowKb="true" maximum="204.8"></upload-img>
        </el-form-item>
        <el-form-item :label="i18n('链接')" prop="jumpPageInfo" v-if="!addFormData.systemMenu">
          <page-jump ref="jumpPage" :advertiseChannel="advertiseChannel" v-model="addFormData.jumpPageInfo" :showTitile="true" :isMenuSet="true"
            style="margin-top: 12px;" @change="jumpPageChange"></page-jump>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ i18n('取消') }}</el-button>
        <el-button type="primary" @click="addFormSubmit">{{ i18n('确定') }}</el-button>
      </div>
    </el-dialog>
  </el-form>
</template>

<script lang="ts" src="./MenuSet.ts"></script>

<style lang="scss" scoped>
.item {
  width: 100%;
  height: 40px;
  background: #F0F2F6;
  border-radius: 4px;

  &.isMyStoredValue {
    min-height: 40px;
    height: auto;
  }


  &-normal {
    display: flex;
     position: relative;
     justify-content: center;
     align-items: center;
  }

  ::v-deep .el-form-item__content {
//    display: flex;
  //  align-items: center;
    font-size: 14px;
    color: #24272B;
    width: 100%;

    .title {
      margin: 0 8px 0 12px;
    }

    .el-switch {
      flex: 1;
    }

    .el-button--text {
      padding: 0 !important;
    }

    .point {
      margin-left: 5px;
      width: 25px;
    }

    .set,
    .delete {
      display: none;
    }

    &:hover {

      .set,
      .delete {
        display: block;
      }
    }
  }
}

.add_btn {
  width: 100%;
  height: 36px;
  background: #FFFFFF;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #007EFF;
  cursor: pointer;

}
</style>