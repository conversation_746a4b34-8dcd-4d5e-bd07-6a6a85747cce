/*
 * @Author: 黎钰龙
 * @Date: 2025-04-08 15:14:03
 * @LastEditTime: 2025-04-08 15:15:01
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\coupon\WeiXinPayActivity.ts
 * 记得注释
 */
import BaseCouponActivity from 'model/coupon/activity/BaseCouponActivity'
import DateTimeCondition from 'model/common/DateTimeCondition'
import { WeiXinCouponType } from 'model/weixin/weixinIssueCouponActivity/WeiXinCouponType'
import WeiXinMerchantCoupon from 'model/weixin/weixinIssueCouponActivity/WeiXinMerchantCoupon'
import { AdvertisingPurposeType } from './AdvertisingPurposeType'

export default class WeiXinPayActivity extends BaseCouponActivity {
  // 副标题
  subtitle: Nullable<string> = null
  // 商户LOGO
  logo: Nullable<string> = null
  // 背景颜色
  backgroundColor: Nullable<string> = null
  // 投放目的
  advertisingPurpose: Nullable<AdvertisingPurposeType> = null
  // 小程序APPID
  miniProgramAppId: Nullable<string> = null
  // 跳转路径
  jumpUrl: Nullable<string> = null
  // 活动总限量
  maxTime: Nullable<number> = null
  // 参与人群 填写 小程序或公众号APPID
  participateCrowd: Nullable<string> = null
  // 消费门槛
  consumptionThreshold: Nullable<number> = null
  // 微信券类型
  weiXinCouponType: Nullable<WeiXinCouponType> = null
  // 券信息
  merchantCoupons: WeiXinMerchantCoupon[] = []
  // 是否修改活动库存
  modifyStock: Nullable<boolean> = null
  // 操作时间
  create: Nullable<Date> = null
  // 操作人信息
  operator: Nullable<string> = null
  // 活动时间条件
  dateTimeCondition: Nullable<DateTimeCondition> = null
  // 操作人信息
  platformAuditFailMsg: Nullable<string> = null
}