<template>
  <div class="member-vip-analysis">
    <div class="vip-analysis-header">
      <BreadCrume :panelArray="panelArray"></BreadCrume>
      <el-button v-if="hasOptionPermission('/数据/数据洞察/会员分析', '数据维护')" class="create-button" type="primary" @click="doGoDtl('create', null)">
        {{i18n('/数据/数据洞察/列表页/新建会员分析')}}
      </el-button>
    </div>
    <div class="setting-container">
      <MyQueryCmp @reset="doReset" @search="onSearch" :showExpand="false">
        <el-row>
          <el-col :span="8">
            <FormItem :label="i18n('报表名称')">
              <el-input v-model="nameLikes" :placeholder="formatI18n('/公用/查询条件/提示/类似于')"></el-input>
            </FormItem>
          </el-col>
        </el-row>
      </MyQueryCmp>
    </div>
    <div class="setting-container">
      <el-table :data="analysisList" style="width: 100%">
        <el-table-column :label="i18n('报表名称')" prop="name">
          <template slot-scope="scope">
            <span class="span-btn" style="font-size: 14px" @click="doGoDtl('dtl', scope.row)">{{ scope.row.name }}</span>
            <i class="el-icon-edit" v-if="hasOptionPermission('/数据/数据洞察/会员分析', '数据维护')" @click.stop="handleChangeName(scope.row)"
              style="margin-left: 8px;cursor: pointer">
            </i>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('/页面/页面管理/最后修改时间')" prop="name">
          <template slot-scope="scope">
            <span no-i18n>{{ scope.row.lastModified | dateFormate3 }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="formatI18n('/储值/预付卡/充值卡制售单/列表页面', '创建人')" prop="creator">
          <template slot-scope="scope">
            {{ scope.row.creator || '--' }}
          </template>
        </el-table-column>
        <el-table-column :label="formatI18n('/储值/预付卡/充值卡制售单/列表页面', '最后修改人')" prop="lastModifier">
          <template slot-scope="scope">
            {{ scope.row.lastModifier || '--' }}
          </template>
        </el-table-column>
        <el-table-column :label="i18n('操作')" width="136">
          <template slot-scope="scope">
            <el-button type="text" v-if="hasOptionPermission('/数据/数据洞察/会员分析', '数据维护')" @click.stop="doGoDtl('edit', scope.row)">
              {{i18n('编辑')}}
            </el-button>
            <el-button type="text" v-if="hasOptionPermission('/数据/数据洞察/会员分析', '数据维护')" @click.stop="removeCard(scope.row)">
              {{i18n('删除')}}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- <div class="vip-content" style="display: flex;flex-wrap:wrap;height: 90%;overflow: auto;">
        <template v-if="analysisList.length > 0">
          <div class="card-part" v-for="(item, index) in analysisList" :key="index">
            <div class="vip-card">
              <div class="card-header">
                <img src="~assets/image/icons/img_chart.png" style="width: 32px;height: 32px;margin-right: 12px;">
                <div class="item-title" @click="doGoDtl('dtl', item)">{{ item.name }}</div>
                <i class="el-icon-edit" v-if="hasOptionPermission('/数据/数据洞察/会员分析', '数据维护')" @click.stop="handleChangeName(item)"
                  style="line-height: 25px;margin-left: 8px">
                </i>
              </div>
              <div class="card-footer">
                <el-button class="fo-button" @click.stop="doGoDtl('edit', item)" v-if="hasOptionPermission('/数据/数据洞察/会员分析', '数据维护')">
                  {{i18n('/数据/数据洞察/列表页/编辑')}}
                </el-button>
                <el-button class="fo-button" @click.stop="removeCard(item)" v-if="hasOptionPermission('/数据/数据洞察/会员分析', '数据维护')">
                  {{ formatI18n("/公用/按钮","删除")}}
                </el-button>
              </div>
            </div>
          </div>
        </template>
        <div v-else class="empty">
          <img src="~assets/image/auth/ct_empty.png" alt="">
          <div>
            {{ i18n('/会员/会员资料/暂无数据') }}
          </div>
        </div>
      </div> -->
      <div style="height: 50px;margin-top: 16px;margin-right: 16px;">
        <!--分页栏-->
        <el-pagination :current-page="page.page+1" :page-size="page.pageSize" :page-sizes="[10, 20, 30, 40]" @current-change="onHandleCurrentChange"
          @size-change="onHandleSizeChange" :total="page.total" background layout="total, prev, pager, next, sizes,  jumper">
        </el-pagination>
      </div>
      <el-dialog :title="i18n('/数据/数据洞察/列表页/报表名称')" :visible.sync="dialogVisible" width="640px" :before-close="handleClose">
        <el-form :model="labelForm" ref="labelForm" label-width="120px" :rules="dialogRules">
          <el-form-item :label="i18n('/数据/数据洞察/列表页/报表名称')" prop="labelName" required>
            <el-input v-model="labelForm.labelName" :placeholder="i18n('/公用/菜单/请输入')" :maxLength="40" show-word-limit type="text"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false" class="fo-button">
            {{ formatI18n("/公用/按钮", "取消")}}
          </el-button>
          <el-button type="primary" class="fo-button" @click="handleChangeNameSubmit">
            {{ formatI18n("/公用/按钮","确定")}}
          </el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script lang="ts" src="./VipAnalysis.ts">
</script>

<style lang="scss">
.member-vip-analysis {
  width: 100%;
  height: 95%;

  .vip-analysis-header {
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: space-between;

    .create-button {
      height: 36px;
      border-radius: 4px;
      padding: 8px 12px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      line-height: 20px;
    }
  }

  .vip-analysis-container {
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    width: 100%;
    border-radius: 8px;
    height: 100%;

    .card-part {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      // align-items: center;
      justify-content: space-evenly;
      width: calc(33.3333%);
      padding: 24px;

      .vip-card {
        width: 100%;
        height: 144px;
        background: linear-gradient(0, rgba(255, 255, 255, 0.2) 0%, rgba(215, 215, 234, 0.2) 100%);
        border-radius: 4px;

        .card-header {
          padding: 16px;
          display: flex;
          align-items: center;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 18px;
          color: #222222;
          line-height: 25px;
          text-align: left;
          font-style: normal;

          .item-title {
            max-width: 252px;
            font-size: 18px;
            color: #222222;
            line-height: 25px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;

            &:hover {
              cursor: pointer;
              color: #007eff;
            }
          }

          .el-icon-edit:hover {
            cursor: pointer;
            color: #007eff;
          }

          // .chart-icon {
          //     margin-right: 12px;
          //     width: 32px;
          //     height: 32px;
          // }

          // .icon-ic_edit {
          //     margin-left: 6px;
          // }

          // .icon-ic_edit:hover {
          //     color: #007EFF;
          // }

          // span:hover {
          //     color: #007EFF;
          // }
        }

        .card-footer {
          padding: 20px;
          display: flex;
          justify-content: flex-end;
        }
      }
    }

    .fo-button {
      width: 72px;
      height: 36px;
      padding: 8px 12px;
      font-size: 14px;
      line-height: 20px;
    }

    .empty {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
    }
  }
}
</style>