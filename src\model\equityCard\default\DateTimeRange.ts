export default class DateTimeRange {
  // 日期时段类型：ALL——全部； USEABLE——可用; UNUSEABLE——不可用；
  dateTimeRangeType: Nullable<string> = null
  // 时段循环类型：DAYS——每天； WEEKS——每周; MONTHS——每月；
  cycleType: Nullable<string> = null
  // 选中日期
  days: Nullable<Array<number>> = null
  // 开始时间
  beginTime: Nullable<string> = null
  // 结束时间
  endTime: Nullable<string> = null
  /**
   * 时间段
   * 每个集合填俩个时间，起始和结束
   */
  timeFrames: string[][] = []
}