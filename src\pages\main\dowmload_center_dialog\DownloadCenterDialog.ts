import {Component, Prop, Vue, Watch} from 'vue-property-decorator'
import DownloadCenter<PERSON>pi from 'http/downloadcenter/DownloadCenterApi'
import UploadApi from "http/upload/UploadApi";
import FileRecordFilter from "model/downloadcenter/FileRecordFilter";

@Component({
  name: 'DownloadCenterDialog',
  components: {}
})
export default class DownloadCenterDialog extends Vue {
  flag = false
  progress = ''
  tableData = []
  currentPage = 1
  pageSize = 10
  pageTotal = 0
  timer: any = 0
  fileArray: any[] = []
  @Prop()
  dialogvisiable: boolean
  @Prop({
    type: Boolean,
    default: false
  })
  showTip: boolean
  @Prop({ type: Boolean, default: false }) appendToBody: boolean;

  @Watch('dialogvisiable')
  onDialogvisiableChange(val: boolean) {
    this.flag = val
    if (this.flag) {
      if (this.showTip) {
        setTimeout(() => {
          this.$message.success(this.formatI18n('/公用/预约文件列表/js提示信息', '预约成功'))
        }, 1000)
      }
      this.progress = ''
      this.currentPage = 1
      this.getFileList()
      this.setIntervalTimer()
    }
  }

  mounted() {
    this.getFileList()
  }

  handleSizeChange(size: number) {
    this.pageSize = size
    this.getFileList()
  }

  handleCurrentChange(page: number) {
    this.currentPage = page
    this.getFileList()
  }

  doRefresh() {
    this.getFileList()
  }

  doProgressChange() {
    this.currentPage = 1
    this.getFileList()
  }

  doDownload(key: string) {
    UploadApi.getUrl(key).then((resp: any) => {
      if (resp && resp.data) {
        window.open(resp.data);
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  doDelete(id: string) {
    DownloadCenterApi.delete(id).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(this.formatI18n('/营销/券礼包活动/券礼包活动', '删除成功'))
        this.getFileList()
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  doBeforeClose(done: any) {
    clearInterval(this.timer)
    this.$emit('dialogClose')
    done()
  }

  /**
   * 获取文件列表
   */
  getFileList() {
    let params = new FileRecordFilter()
    params.page = this.currentPage - 1
    params.pageSize = this.pageSize
    params.state = this.progress
    DownloadCenterApi.query(params)//
      .then((resp: any) => {
        if (resp) {
          this.pageTotal = resp.total
          this.fileArray = resp.data
        }
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
        clearInterval(this.timer)
    })
  }

  destroyed() {
    clearInterval(this.timer)
  }

  /**
   * 定时获取列表
   */
  setIntervalTimer() {
    this.timer = setInterval(() => {
      this.getFileList()
    }, 5000)
  }
}
