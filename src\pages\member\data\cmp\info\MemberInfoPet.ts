import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import MemberDetail from "model/member_standard/MemberDetail";
import I18nPage from "common/I18nDecorator";
import MemberCustomGroup from "model/member_v2/member/MemberCustomGroup";
import MemberCardTitle from "pages/member/data/cmp/MemberCardTitle";
import EmptyData from "pages/member/data/cmp/EmptyData";
import MemberFormItem from "pages/member/data/cmp/MemberFormItem";
import CommonUtil from "util/CommonUtil";


@Component({
  name: "MemberInfoPet",
  components: { MemberCardTitle, EmptyData, MemberFormItem },
})
@I18nPage({
  prefix: [
    "/会员/会员资料",
  ],
  auto: true,
})
export default class MemberInfoPet extends Vue {
  @Prop()
  dtl: MemberDetail;

  @Watch("dtl", { immediate: true })
  dtlChanged() {
    if (!(this.dtl && this.dtl.memberId)) return;
    // @ts-ignore
    this.memberCustomGroups = this.dtl.memberCustomGroups;
    this.getGroupedData();
  }

  memberCustomGroups: MemberCustomGroup[] = [];
  groupedData: any = [];

  private getGroupedData() {
    console.log("会员自定义分组数据", this.memberCustomGroups);
    const tempGroupedData: { [key: string]: { fieldId: string, fieldKey: string, value: string }[] } = {};
    if (this.memberCustomGroups == null || this.memberCustomGroups.length == 0) {
      this.groupedData = [];
      // this.groupedData = [{ fields: [{ fieldKey: "哈哈哈哈", value: "111" },{ fieldKey: "哈哈哈哈", value: "111" },{ fieldKey: "哈哈哈哈", value: "111" },{ fieldKey: "哈哈哈哈", value: "111" }] }];
      return;
    }
    this.memberCustomGroups.forEach((item) => {
      const groupNum = item.groupNum;
      if (groupNum == null) {
        return;
      }
      if (!tempGroupedData[groupNum]) {
        tempGroupedData[groupNum] = [];
      }
      const fieldId = item.fieldId;
      const fieldKey = item.fieldKey;
      const value = item.value;
      if (fieldId && fieldKey && value) {
        // 将字段名称和字段值添加到对应组的对象中
        tempGroupedData[groupNum].push({ fieldId, fieldKey, value });
      }
    });
    this.groupedData = Object.entries(tempGroupedData).map(([groupNum, fields]) => ({
      groupNum,
      fields,
    }));
    console.log("分组数据", this.groupedData);
  }

  // 是否为图片url
  isImgStr(str: string) {
    return CommonUtil.isImgStr(str);
  }
}
