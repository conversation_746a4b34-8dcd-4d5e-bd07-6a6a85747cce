import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import PushPlanActivity from 'model/precisionmarketing/pushplan/PushPlanActivity'
import PushPlanActivityFilter from 'model/precisionmarketing/pushplan/PushPlanActivityFilter'
import MemberRule from "model/precisionmarketing/tag/tagrule/customize/member/MemberRule";
import PushPlanSum from "model/precisionmarketing/pushplan/PushPlanSum";

export default class PushPlanActivityApi {
  /**
   * 审核推送计划
   * 审核推送计划。
   * 
   */
  static audit(activityId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/pushplan-activity/audit/${activityId}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量审核推送计划
   * 批量审核推送计划。
   * 
   */
  static batchAudit(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/pushplan-activity/batch/audit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量删除推送计划
   * 批量删除推送计划。
   * 
   */
  static batchRemove(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/pushplan-activity/batch/remove`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量终止推送计划
   * 批量终止推送计划。
   * 
   */
  static batchStop(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/pushplan-activity/batch/stop`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 推送计划详情
   * 推送计划详情
   * 
   */
  static getPushPlan(id: string): Promise<Response<PushPlanActivity>> {
    return ApiClient.server().get(`/v1/pushplan-activity/getPushPlan/${id}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询推送计划
   * 查询推送计划
   * 
   */
  static query(body: PushPlanActivityFilter): Promise<Response<PushPlanActivity[]>> {
    return ApiClient.server().post(`/v1/pushplan-activity/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 刷新
   * 刷新
   * 
   */
  static refreshMemberCount(body: MemberRule): Promise<Response<number>> {
    return ApiClient.server().post(`/v1/pushplan-activity/refreshMemberCount`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除推送计划
   * 删除推送计划。
   * 
   */
  static remove(activityId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/pushplan-activity/remove/${activityId}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建修改推送计划
   * 新建修改推送计划
   * 
   */
  static savePushPlan(body: PushPlanActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/pushplan-activity/savePushPlan`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 终止推送计划
   * 终止推送计划。
   * 
   */
  static stop(activityId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/pushplan-activity/stop/${activityId}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 汇总查询推送计划组信息
   *
   */
  static summary(body: PushPlanActivityFilter): Promise<Response<PushPlanSum>> {
    return ApiClient.server().post(`/v1/pushplan-activity/summary`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
