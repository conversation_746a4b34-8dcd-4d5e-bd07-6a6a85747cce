export default class CardLedger {
  // 核销门店id
  occurredOrgId: Nullable<string> = null
  // 核销门店名称
  occurredOrgName: Nullable<string> = null
  // 渠道id
  channelId: Nullable<string> = null
  // 渠道type
  channelType: Nullable<string> = null
  // 结算时间
  settleTime: Nullable<string> = null
  // 发生时间
  occurredTime: Nullable<string> = null
  // 业务类型
  category: Nullable<string> = null
  // 支付金额
  originalAmount: Nullable<number> = null
  // 本金支付金额
  amount: Nullable<number> = null
  // 赠金支付金额
  giftAmount: Nullable<number> = null
  // 优惠金额
  favAmount: Nullable<number> = null
  // 来源平台
  platform: Nullable<string> = null
  // 支付收入
  totalAmount: Nullable<number> = null
  // 技术服务费
  serviceAmount: Nullable<number> = null
  // 结算金额
  settleAmount: Nullable<number> = null
  // 支付ID
  transIdId: Nullable<string> = null
  // 交易号
  tradeNo: Nullable<string> = null
}