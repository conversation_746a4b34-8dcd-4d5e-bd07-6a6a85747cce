import ApiClient from 'http/ApiClient'
import GoodsGroupGainByQtyPointsActivity
  from 'model/points/activity/goodsgroupgainbyqtypoints/GoodsGroupGainByQtyPointsActivity'
import Response from 'model/common/Response'

export default class GoodsGroupGainPointsByQtyActivityApi {
  /**
   * 详情
   * 详情。
   *
   */
  static detail(activityId: string): Promise<Response<GoodsGroupGainByQtyPointsActivity>> {
    return ApiClient.server().get(`/v1/points-activity/goods-group-gain-additional-points-by-qty/detail/${activityId}`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 修改或保存
   * 修改或保存。
   *
   */
  static saveOrModify(body: GoodsGroupGainByQtyPointsActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/points-activity/goods-group-gain-additional-points-by-qty/saveOrModify`, body, {}).then((res) => {
      return res.data
    })
  }

}
