/*
 * @Author: 黎钰龙
 * @Date: 2023-10-11 17:51:13
 * @LastEditTime: 2023-10-16 14:48:50
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\prepay\card\SaleCardBillFilter.ts
 * 记得注释
 */

export default class SaleCardBillFilter {
  // 卡模板号类似于
  cardTemplateNumberLikes: Nullable<string> = null
  // 卡模板号等于
  cardTemplateNumberEquals: Nullable<string> = null
  // 卡模板名称类似于
  cardTemplateNameLikes: Nullable<string> = null
  // 卡模板名称等于
  cardTemplateNameEquals: Nullable<string> = null
  // 单号等于
  billNumberEquals: Nullable<string> = null
  // 单号类似于
  billNumberLikes: Nullable<string> = null
  // 状态等于 INITIAL-未审核 AUDITED-已审核
  stateEquals: Nullable<string> = null
  // 创建时间：created:(,]
  createdBetweenOpenClosed: Nullable<Date[]> = null
  // 创建时间：created:[,)
  createdBetweenClosedOpen: Nullable<Date[]> = []
  // 创建时间：created:[,]
  createdBetweenClosedClosed: Nullable<Date[]> = null
  // 最后修改时间：lastModified:(,]
  lastModifiedBetweenOpenClosed: Nullable<Date[]> = null
  // 最后修改时间：lastModified:[,)
  lastModifiedBetweenClosedOpen: Nullable<Date[]> = []
  // 最后修改时间：lastModified:[,]
  lastModifiedBetweenClosedClosed: Nullable<Date[]> = null
  // 营销中心等于
  marketingCenterEquals: Nullable<string> = null
  // 页码
  page: Nullable<number> = null
  // 页面大小，大于0
  pageSize: Nullable<number> = null
}