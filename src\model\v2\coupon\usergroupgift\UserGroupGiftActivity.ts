import GiftInfo from 'model/common/GiftInfo'
import BaseCouponActivity from "model/v2/coupon/BaseCouponActivity";
import IdName from 'model/common/IdName';
import PushGroup from 'model/precisionmarketing/pushplan/PushGroup';

export default class UserGroupGiftActivity extends BaseCouponActivity {
  // 客群
  userGroup: Nullable<IdName> = null
  // 发券时间
  handOutTime: Nullable<Date> = null
  // 礼包信息
  giftInfo: Nullable<GiftInfo> = null
  // 客群最后一次计算时间
  calDate: Nullable<Date> = null
  // 参与人群
  rule: Nullable<PushGroup> = null
}