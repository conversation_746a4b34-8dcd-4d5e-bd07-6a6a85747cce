import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import User from 'model/user/User'

export default class UserApi {
  /**
   * 复制角色信息到其他用户
   *
   */
  static copyRole(fromUserId: string, toUserId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/user/copyRole`, {}, {
      params: {
        fromUserId: fromUserId,
        toUserId: toUserId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 启禁用用户
   *
   */
  static enableOrDisable(userId: string, enable: boolean): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/user/enableOrDisable`, {}, {
      params: {
        userId: userId,
        enable: enable
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询用户列表信息
   *
   */
  static list(page: number, pageSize: number, keyWord?: string): Promise<Response<User[]>> {
    return ApiClient.server().get(`/v1/user/list`, {
      params: {
        keyWord: keyWord,
        page: page,
        pageSize: pageSize
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建用户
   *
   */
  static save(body: User): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/user/save`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 修改用户
   *
   */
  static saveModify(body: User): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/user/saveModify`, body, {}).then((res) => {
      return res.data
    })
  }

}
