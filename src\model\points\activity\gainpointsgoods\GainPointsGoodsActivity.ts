import ActivityBody from 'model/common/ActivityBody'
import GoodsRange from 'model/common/GoodsRange'
import ChannelRange from "model/common/ChannelRange";

// 不积分商品活动规则
export default class GainPointsGoodsActivity {
  // true表示保存或修改，false表示保存并审核
  justSave: Nullable<boolean> = null
  // 活动信息
  activityBody: Nullable<ActivityBody> = null
  // 商品范围信息
  goodsRange: Nullable<GoodsRange> = null
  //渠道范围
  channelRange: Nullable<ChannelRange> = null
}