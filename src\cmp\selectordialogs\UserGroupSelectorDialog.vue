<template>
  <el-dialog :title="i18n('选择客群')" class="select-store-dialog" append-to-body width="80%" :close-on-click-modal="false" :visible.sync="dialogShow">
    <div class="wrap" style="height: 550px">
      <el-form label-width="120px">
        <el-row class="query">
          <el-col :span="8">
            <el-form-item :label="i18n('客群名称')" :title="i18n('客群名称')">
              <el-input v-model="userGroupFilter.nameLikes" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="i18n('创建方式')" :title="i18n('创建方式')">
              <el-select v-model="userGroupFilter.sourceEquals" style="width: 100%" :placeholder="i18n('/公用/券模板/请选择')">
                <el-option v-for="(value, key) of sourceMap" :label="value" :value="key" :key="key">{{ value }}</el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item :label="i18n('更新频次')" :title="i18n('更新频次')">
              <el-select v-model="userGroupFilter.scheduleTypeEquals" style="width: 100%" :placeholder="i18n('/公用/券模板/请选择')">
                <el-option v-for="(value, key) of scheduleTypeMap" :label="value" :value="key" :key="key">{{ value }}</el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="i18n('客群状态')" :title="i18n('客群状态')">
              <el-select v-model="userGroupFilter.stateEquals" :disabled="true" style="width: 100%" :placeholder="i18n('/公用/券模板/请选择')">
                <el-option v-for="(value, key) of stateMap" :label="value" :value="key" :key="key">{{ value }}</el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="i18n('最近计算状态')" :title="i18n('最近计算状态')">
              <el-select v-model="userGroupFilter.lastExecuteStateEquals" :disabled="true" style="width: 100%" :placeholder="i18n('/公用/券模板/请选择')">
                <el-option v-for="(value, key) of lastExecuteStateMap" :label="value" :value="key" :key="key">{{
                    value
                  }}
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item :label="i18n('标记包含')" :title="i18n('标记包含')">
              <el-select v-model="userGroupFilter.tagValueLike" style="width: 100%" :placeholder="i18n('/公用/券模板/请选择')">
                <el-option :label="mark.name" :value="mark.name" v-for="mark of marks" :key="mark.name">{{mark.name}}</el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-button type="primary" @click="doSearch()">{{ i18n('/公用/按钮/查询') }}</el-button>
              <el-button @click="doReset()">{{ i18n('/公用/按钮/重置') }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-row>
        <el-row class="table-wrap" v-loading="loading.query">
          <el-row class="thead">
            <el-col :span="4">{{ i18n('客群名称') }}</el-col>
            <el-col :span="4">{{ i18n('创建方式') }}</el-col>
            <el-col :span="4">{{ i18n('会员数/占比') }}</el-col>
            <el-col :span="4">{{ i18n('最近计算时间') }}</el-col>
            <el-col :span="4">{{ i18n('更新频次') }}</el-col>
            <el-col :span="4">{{ i18n('标记') }}</el-col>
          </el-row>
          <el-row class="tbody" v-if="!loading.query">
            <el-row v-if="currentList && currentList.length > 0" v-for="(item, index) of currentList" :key="index" class="trow"
              :style="{backgroundColor: isObjEqual(selected,item)?'#EEEEEE':''}" @click.native="doCheckRow(index)">
              <el-col :span="4" :title="item.name">{{ item.name }}</el-col>
              <el-col :span="4">{{ sourceMap[item.source] }}</el-col>
              <el-col :span="4">{{ item.numberCount }} {{i18n('人')}}| {{ item.memberRate ? item.memberRate.toFixed(2): 0 }}%</el-col>
              <el-col :span="4">
                <LastExecuteState :value="item.lastExecuteState"></LastExecuteState>
              </el-col>
              <el-col :span="4">
                <span v-if="item.monthDay">
                  <i18n k="/公用/公共组件/客群选择弹框组件/按月（{0}日）">
                    <template slot="0">
                      {{ item.monthDay }}
                    </template>
                  </i18n>
                </span>
                <span v-else>{{i18n('按日')}}</span>
              </el-col>
              <el-col :span="4" :title="item.tags">
                <span v-if="item.tags && item.tags.length > 0" :title="item.tags ? item.tags.join(','): ''">
                  <el-tag v-for="tag of item.tags" :key="tag">{{ tag }}</el-tag>
                </span>
              </el-col>
            </el-row>
            <el-row v-if="!currentList || currentList.length === 0" class="trow" style="text-align: center;color: #909399">
              {{ i18n('/公用/提示/暂无数据') }}
            </el-row>
          </el-row>
        </el-row>
      </el-row>
    </div>
    <div class="page" style="margin-top: 15px">
      <el-pagination :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
        @current-change="handleCurrentChange($event)" @size-change="handleSizeChange($event)" background
        layout="total, prev, pager, next, sizes,  jumper">
      </el-pagination>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="dialogShow = false">{{ i18n('/公用/按钮/取消') }}</el-button>
      <el-button size="small" type="primary" @click="doModalClose()">{{ i18n('/公用/按钮/确定') }}</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./UserGroupSelectorDialog.ts"/>

<style lang="scss" scoped>
.select-store-dialog {
  display: flex;
  align-items: center;
  justify-content: center;
  @import "SelectorDialogCommon";

  ::v-deep .el-dialog {
    width: 1024px;
    height: 750px;
  }
}
</style>