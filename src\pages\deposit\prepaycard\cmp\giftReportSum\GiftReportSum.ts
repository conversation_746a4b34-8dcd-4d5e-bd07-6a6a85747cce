import { Component, Prop, Vue } from "vue-property-decorator";
import I18nPage from "common/I18nDecorator";
import PrePayCardApi from "http/prepay/card/PrePayCardApi";
import BalanceAccountSummary from "model/prepay/card/BalanceAccountSummary";

@Component({
	name: "GiftReportSum",
	components: {},
})
@I18nPage({
	prefix: ["/储值/会员储值/会员储值报表/列表页面", "/储值/预付卡/电子礼品卡报表", "/公用/按钮"],
})
export default class GiftReportSum extends Vue {
	$refs: any;
	summary: BalanceAccountSummary = new BalanceAccountSummary();
	@Prop({
		type: String,
		default: "",
	})
	type: string; //电子礼品卡 type : OnlineGiftCard   实体礼品卡type: OfflineGiftCard
	created() {}

	mounted() {
		this.getSummary();
	}

	private getSummary() {
		const marketingCenter = sessionStorage.getItem("marketCenter") || "";
		PrePayCardApi.balanceAccountSum(this.type, marketingCenter)
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					this.summary = resp.data;
				}
			})
			.catch((error) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}
}
