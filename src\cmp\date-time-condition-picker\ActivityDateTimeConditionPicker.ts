/*
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:02
 * @LastEditTime: 2025-04-18 13:40:04
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\date-time-condition-picker\ActivityDateTimeConditionPicker.ts
 * 记得注释
 */
import {Component, Prop, Watch} from 'vue-property-decorator'
import DateTimeCondition from "model/common/DateTimeCondition";
import DateTimeConditionPickerParent from "cmp/date-time-condition-picker/DateTimeConditionPickerParent";
import ActivityDateTimeCondition from "model/common/ActivityDateTimeCondition";
import DateUtil from "util/DateUtil";
import DateTimeConditionPicker from "cmp/date-time-condition-picker/DateTimeConditionPicker";


class ActivityDateTimeConditionPickerForm {
  timeRange: Array<Nullable<Date>> = [null, null]
  // 时间条件
  dateTimeCondition: DateTimeCondition = new DateTimeCondition()
}

@Component({
  name: 'ActivityDateTimeConditionPicker',
  components: {
    DateTimeConditionPicker
  },
  model: {
    prop: 'modelValue',
    event: 'change'
  }
})
export default class ActivityDateTimeConditionPicker extends DateTimeConditionPickerParent {
  @Prop({ type: Number}) rangeLimit: number;  //选择的时间 区间跨度限制
  @Prop()
  modelValue: ActivityDateTimeCondition
  form = {
    data: new ActivityDateTimeConditionPickerForm()
  }
  $refs: any
  dateRangeRules: any
  selectData: string = ''
  @Prop({ type: Boolean }) disabled: boolean; //是否展示
  @Prop({ type: Boolean,default: false }) isHideWeek: boolean; //是否隐藏按周
  @Prop({ type: Boolean,default: false }) isHideMonth: boolean; //是否隐藏按月

  created() {
    this.dateRangeRules = [
      {
        validator: (rule: any, value: any, callback: any) => {
          if (!value || value.length === 0 || value[0] === null || value[1] === null) {
            callback(this.formatI18n('/营销/积分活动/积分活动/商品满额加送积分/表单校验/提示信息/请选择活动时间'))
          }
          callback()
        },
        trigger: ['change', 'blur']
      }
    ]
  }

  @Watch("modelValue", {deep: true, immediate: true})
  watchModelValue(value: ActivityDateTimeCondition) {
    this.form.data.timeRange = [value.beginDate ? new Date(value.beginDate) : null, value.endDate ? new Date(value.endDate) : null]
    this.form.data.dateTimeCondition = value.dateTimeCondition
  }

  validate() {
    return Promise.all([
      this.$refs.dateTimeConditionPicker.validate(),
      this.$refs.form.validate()
    ])
  }

  submit() {
    let result = new ActivityDateTimeCondition();
    if (this.form.data.timeRange && this.form.data.timeRange[1]) {
      let endDate = this.form.data.timeRange[1]
      endDate.setHours(23)
      endDate.setMinutes(59)
      endDate.setSeconds(59)
      result.endDate = DateUtil.format(endDate)
    }
    if (this.form.data.timeRange && this.form.data.timeRange[0]) {
      result.beginDate = DateUtil.format(this.form.data.timeRange[0])
    }
    result.dateTimeCondition = this.form.data.dateTimeCondition
    this.$emit('change', result)
  }

  get dateRangeOption() {
    const _this = this
    return {
      onPick: ({ maxDate, minDate }: any) => {
        this.selectData = minDate.getTime();
        if (maxDate) {
          // 解除限制
          this.selectData = "";
        }
      },
      disabledDate(time: any) {
        if (_this.rangeLimit) {
          if (_this.selectData) {
            const curDate = _this.selectData;
            const three = _this.rangeLimit * 24 * 3600 * 1000;
            const endDate = curDate + three; // 开始时间+跨度天数
            return (time.getTime() < curDate || time.getTime() > endDate);
          } else {
            return time.getTime() < DateUtil.nowDayTime() || time.getTime() > new Date('2038-01-01 23:59:59').getTime();
          }
        } else {
          return time.getTime() < DateUtil.nowDayTime() || time.getTime() > new Date('2038-01-01 23:59:59').getTime();
        }
      }
    }
  }
}
