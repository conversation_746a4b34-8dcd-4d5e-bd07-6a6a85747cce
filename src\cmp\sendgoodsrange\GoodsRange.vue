<template>
    <div class="goods-range">
        <div class="top" v-if="showDesc">{{formatI18n('/公用/券模板', '商品范围')}}<el-button @click="doImport" class="btn" type="text">{{formatI18n('/公用/券模板', '导入')}}</el-button></div>
        <div class="content">
            <el-form :model="dynamicValidateForm" class="demo-dynamic" label-width="100px" ref="dynamicValidateForm">
                <el-form-item
                        :key="index"
                        :prop="'domains.' + index + '.value'"
                        class="cur-goods-range"
                        style="margin-bottom: 0px !important;"
                        v-for="(domain, index) in dynamicValidateForm.domains"
                >
                    <el-select @change="doTypeChange(index, domain.brand)" class="width-150" v-model="domain.brand">
                        <el-option :label="item" :value="item" v-for="item in selectType">{{item}}</el-option>
                    </el-select>
                    <el-select @change="doBelongChange(index, domain.brand, domain.belong)" class="width-150" v-model="domain.belong">
                        <el-option :label="formatI18n('/公用/券模板', '属于')" :value="formatI18n('/公用/券模板', '属于')">{{formatI18n('/公用/券模板', '属于')}}</el-option>
                        <el-option :label="formatI18n('/公用/券模板', '不属于')" :value="formatI18n('/公用/券模板', '不属于')">{{formatI18n('/公用/券模板', '不属于')}}</el-option>
                    </el-select>
                    <el-input :placeholder=getPlaceholder(domain.brand)
                              @focus="doFocus(index, domain.brand)"
                              class="width-auto"
                              v-model="domain.value">
                    </el-input>
                    &nbsp;&nbsp;
                    <span style="width: 150px;display: inline-block">
                            <el-button @click="doClearDomain(index)" type="text">{{formatI18n('/公用/按钮', '清空')}}</el-button>
                        <el-button @click="doDeleteDomain(index)" type="text" v-show="dynamicValidateForm.domains.length > 1">{{formatI18n('/公用/按钮', '删除')}}</el-button>
                        <el-button @click="doAddDomain(index, domain.brand)" type="text" v-show="dynamicValidateForm.domains.length - 1 === index && index !== 2">{{formatI18n('/公用/券模板', '增加')}}</el-button></span>
                </el-form-item>
            </el-form>
        </div>
        <!--:type="ruleForm.brand"-->
        <SelectGoodsRangeDialog
                :data="goodsData"
                :dialogShow="dialogShow"
                :type="recordName"
                @dialogClose="doDialogClose"
                @summit="doSummit">
        </SelectGoodsRangeDialog>
        <ImportDialog
                :dialogShow="importDialogShow"
                :importUrl="importUrl"
                :templateName="formatI18n('/公用/券模板/全场现金券/用券商品/指定不可用商品/点击导入', '导入商品范围模板')"
                :templatePath="templatePath"
                :title="formatI18n('/公用/券模板', '导入')"
                @dialogClose="doImportDialogClose" @upload-success="doUploadSuccess">
        </ImportDialog>
        <ImportResultDialog
                :data="importResultData"
                :dialogShow="importResultDialogClose"
                @importResultDialogClose="doImportResultDialogClose">
        </ImportResultDialog>
    </div>
</template>

<script lang="ts" src="./GoodsRange.ts">
</script>

<style lang="scss">
    .goods-range{
        width: 700px;
        .top{
            position: relative;
            height: 32px;
            line-height: 32px;
            background: #ced0da;
            padding-left: 20px;
            .btn{
                position: absolute;
                right: 20px;
                top: 2px;
            }
        }
        .content{
            border: 1px solid #EEEEEE;
            text-align: center;
            padding-top: 10px;
            padding-bottom: 10px;
            .width-150{
                width: 100px !important;
                margin-right: 15px;
            }
            .width-auto{
                width: 250px;
            }
            .clear{
                margin-left: 20px;
                cursor: pointer;
            }
        }
        .el-form-item__content{
            margin-bottom: 15px !important;
        }
        .el-form-item__error{
            margin-left: 345px !important;
        }
        .cur_form{
            .el-form-item__content{
                margin-left: 0 !important;
            }
        }
        .cur-goods-range{
            margin-bottom: 0px !important;
            height: 57px !important;
            .el-form-item__content{
                margin-left: 30px !important;
            }
        }
    }
</style>