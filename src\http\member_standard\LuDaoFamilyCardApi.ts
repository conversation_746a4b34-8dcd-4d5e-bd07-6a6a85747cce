import ApiClient from 'http/ApiClient'
import LuDaoFamilyCardFilter from 'model/common/LuDaoFamilyCardFilter'
import LuDaoFamilyCardInfo from 'model/common/LuDaoFamilyCardInfo'
import Response from 'model/common/Response'

export default class LuDaoFamilyCardApi {
  /**
   * 查询家庭卡信息
   * 查询家庭卡信息。
   * 
   */
  static query(body: LuDaoFamilyCardFilter): Promise<Response<LuDaoFamilyCardInfo>> {
    return ApiClient.server().post(`/v1/ludao/family-card/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
