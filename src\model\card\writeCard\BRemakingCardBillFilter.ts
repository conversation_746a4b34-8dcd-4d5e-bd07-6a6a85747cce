/*
 * @Author: 黎钰龙
 * @Date: 2025-05-13 09:59:30
 * @LastEditTime: 2025-05-13 10:17:01
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\card\writeCard\BRemakingCardBillFilter.ts
 * 记得注释
 */
import PageRequest from 'model/default/PageRequest'

// 制卡单查询
export default class BRemakingCardBillFilter extends PageRequest {
  // 
  code: Nullable<string> = null
  // 
  createdBetweenClosedOpen: Nullable<Date[]> = null
}