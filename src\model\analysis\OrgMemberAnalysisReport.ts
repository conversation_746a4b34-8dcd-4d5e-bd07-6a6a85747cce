/*
 * @Author: 黎钰龙
 * @Date: 2024-03-06 14:50:11
 * @LastEditTime: 2024-03-06 14:52:17
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\analysis\OrgMemberAnalysisReport.ts
 * 记得注释
 */
import AnalysisChartData from "./AnalysisChartData"
import { AnalysisReportDateUnit } from "./AnalysisReportDateUnit"
import OrgMemberAnalysisSummary from "./OrgMemberAnalysisSummary"

// 门店会员分析报表响应
export default class OrgMemberAnalysisReport {
  // 时间维度
  dateUnit: Nullable<AnalysisReportDateUnit> = null
  // 数据概览
  summary: Nullable<OrgMemberAnalysisSummary> = null
  // 新会员数指标
  newMemberData: AnalysisChartData[] = []
  // 有手机新会员数指标
  newMemberHasPhoneData: AnalysisChartData[] = []
  // 会员数指标
  memberData: AnalysisChartData[] = []
  // 有手机会员数指标
  memberHasPhoneData: AnalysisChartData[] = []
  // 有消会员数指标
  consumedMemberData: AnalysisChartData[] = []
  // 有消新会员数指标
  firstConsumedMemberData: AnalysisChartData[] = []
  // 有消老会员数指标
  secondConsumedMemberData: AnalysisChartData[] = []
  // 复购会员数指标
  repurchasedMemberData: AnalysisChartData[] = []
}