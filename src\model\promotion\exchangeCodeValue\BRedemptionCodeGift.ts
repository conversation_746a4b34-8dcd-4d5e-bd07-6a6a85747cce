/*
 * @Author: hl-cool <EMAIL>
 * @Date: 2024-08-01 13:54:08
 * @LastEditors: hl-cool <EMAIL>
 * @LastEditTime: 2024-08-01 13:59:32
 * @FilePath: \phoenix-web-ui\src\model\promotion\exchangeCodeValue\BRedemptionCodeGift.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import IdName from "model/common/IdName"

// 奖品明细
export default class BRedemptionCodeGift {
  // 模板
  idName: Nullable<IdName> = null
  // 数量
  qty: Nullable<number> = null
  // 码值
  codes: string[] = []
}