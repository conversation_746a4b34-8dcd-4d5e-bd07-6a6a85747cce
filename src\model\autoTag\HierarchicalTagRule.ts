/*
 * @Author: 黎钰龙
 * @Date: 2024-11-21 19:31:52
 * @LastEditTime: 2024-11-25 17:15:04
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\autoTag\HierarchicalTagRule.ts
 * 记得注释
 */
import { ConnectiveType } from "./ConnectiveType"
import MemberActionRule from "./MemberActionRule"
import MemberAttributeRule from "./MemberAttributeRule"

export default class HierarchicalTagRule {
  // 标签名称
  tagName: Nullable<string> = null
  // 标签值
  tagValue: Nullable<string> = null
  // 关系连接符：或者/ 且
  connective: Nullable<ConnectiveType> = ConnectiveType.and
  // 用户属性规则
  memberAttributeRule: Nullable<MemberAttributeRule> = new MemberAttributeRule()
  // 用户行为规则
  memberActionRule: Nullable<MemberActionRule> = new MemberActionRule()
}