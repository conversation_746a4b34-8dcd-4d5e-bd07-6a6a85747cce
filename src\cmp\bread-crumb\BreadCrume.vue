<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:02
 * @LastEditTime: 2023-10-09 10:16:58
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\bread-crumb\BreadCrume.vue
 * 记得注释
-->
<template>
    <div class="bread-crume-view">
        <el-row>
            <el-col :span="15">
                <div class="panel">
                    <!--<span v-if="panelArray" @click="doBack"-->
                          <!--style="cursor: pointer;display: inline;border: 1px solid #d7dfeb;padding: 5px;margin-right: 10px;"><i-->
                        <!--class="iconfont ic-left"></i>-->
                    <!--</span>-->
                    <!--<span v-else style="visibility: hidden">&nbsp;&quot;</span>-->
                    <span @click="onToView($event)" v-html="bindPanel"></span>
                </div>
            </el-col>
            <el-col :span="9">
                <div class="text-right">
                    <slot name="operate"></slot>
                </div>
            </el-col>
        </el-row>
        <el-row>
          <slot name="warning"></slot>
        </el-row>
    </div>
</template>
<script lang='ts' src='./BreadCrume.ts'>
</script>
<style lang='scss'>
    .bread-crume-view{
        background: #f2f2f2 !important;
        padding-bottom: 15px;
        line-height: 30px;
        min-width: 550px;
        padding: 10px 0 ;
        .panel {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            /*background: white;*/
        }
        .text-right {
            text-align: right;
            white-space: nowrap;
            /*padding-right: 60px;*/
        }
        .title{
            /*height: 46px;*/
            background: white;
            font-size: 20px;
            padding-left: 20px;
            font-weight: bold;
            .pro-logo{
                width: 30px;
                height: 30px;
                border-radius: 100%;
                position: relative;
                top: 8px;
                margin-right: 10px;
            }
            .desc{
                font-size: 14px;
                font-weight: normal;
                padding-left: 40px;
                padding-bottom: 20px;
            }
        }
        .selected{
            color: rgba(0, 0, 0, 0.647058823529412);
            font-size: 15px;
        }
        .unSelected{
            color: #007EFF;
            cursor: pointer;
            font-size: 15px;
        }
    }
</style>