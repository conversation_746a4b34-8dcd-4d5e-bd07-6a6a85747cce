<template>
  <el-dialog :title="formatI18n('/公用/公共组件/品类选择弹框组件/标题/选择品类')" class="select-category-dialog" append-to-body :close-on-click-modal="false"
    :visible.sync="dialogShow">
    <div class="wrap">
      <el-row>
        <el-form label-width="80px">
          <el-col :span="8">
            <el-form-item :label="formatI18n('/公用/公共组件/品类选择弹框组件/查询/品类')">
              <el-input v-model="categoryFilter.key" @keyup.enter.native="doSearch" :placeholder="formatI18n('/公用/公共组件/品类选择弹框组件/查询/请输入品类代码/名称')" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="formatI18n('/公用/公共组件/商品选择弹框组件/查询/品类来源')" v-if="headquarters === true || (showAll && enableMultiMarketingCenter)">
              <!-- <el-form-item label="品类来源" v-if="headquarters === true"> -->
              <el-select v-model="marketCenter" :placeholder="formatI18n('/资料/渠道/请选择')" style="width: 200px" @change="doSearch()">
                <el-option v-for="(value,index) in marketingCentersList" :key="index" :value="value.marketingCenter.id"
                  :label="'['+value.marketingCenter.id+']'+value.marketingCenter.name">[{{value.marketingCenter.id}}]{{value.marketingCenter.name}}</el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-button type="primary" @click="doSearch()">{{formatI18n('/公用/按钮/查询')}}</el-button>
              <el-button @click="doReset()">{{formatI18n('/公用/按钮/重置')}}</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      <el-row>
        <el-col :span="18">
          <el-row class="table-wrap" v-loading="loading.query">
            <el-row class="thead">
              <el-col :span="1">
                <el-checkbox @change="doCheckAll($event)" v-model="checkAll" />
              </el-col>
              <el-col :span="11">{{formatI18n('/公用/公共组件/品类选择弹框组件/表格/品类代码')}}</el-col>
              <el-col :span="12">{{formatI18n('/公用/公共组件/品类选择弹框组件/表格/品类名称')}}</el-col>
            </el-row>
            <el-row class="tbody" v-if="!loading.query">
              <template v-if="currentList && currentList.length > 0">
                <el-row v-for="(item, index) of currentList" :key="index" class="trow">
                  <el-col :span="1">
                    <el-checkbox v-model="checkboxList[index]" @change="doCheck($event, index)" />
                  </el-col>
                  <el-col @click.native="doCheckRow(index)" :span="11" :title="item.category.id">{{
                    item.category.id
                  }}
                  </el-col>
                  <el-col @click.native="doCheckRow(index)" :span="12" :title="item.category.name">{{
                    item.category.name
                  }}
                  </el-col>
                </el-row>
              </template>
              <el-row v-if="!currentList || currentList.length === 0" class="trow" style="text-align: center;color: #909399">
                {{ formatI18n('/公用/提示/暂无数据') }}
              </el-row>
            </el-row>
          </el-row>
        </el-col>
        <el-col :span="6" style="padding-left: 5px">
          <el-row class="right-table">
            <el-row class="thead">
              {{formatI18n('/公用/公共组件/品类选择弹框组件/表格/已选品类：')}}{{selected?(selected.filter(e=>e.category.id)).length: 0}}
            </el-row>
            <el-row style="padding: 5px;">
              <el-input style="width: 100%;" @keyup.enter.native="filterSelected()" @change="filterSelected()" v-model="selectedFilter" clearable
                @clear="filterSelected()" :placeholder="formatI18n('/公用/公共组件/品类选择弹框组件/查询/请输入品类代码/名称')"></el-input>
            </el-row>
            <el-row class="tbody">
              <template v-if="filteredSelected && filteredSelected.length > 0">
                <el-row class="trow" style="position: relative;display: flex;align-items: center" :key="index"
                  v-for="(item, index) of filteredSelected" :title="item.category|idName">
                  <div class="left">{{ item.category|idName }}</div>
                  <div class="clear-btn" style="display: none"><a @click="delItem(item, index)">{{ formatI18n('/公用/公共组件/品牌选择弹框组件/表格/清除') }}</a>
                  </div>
                </el-row>
              </template>
              <el-row v-if="!filteredSelected || filteredSelected.length === 0" class="trow" style="text-align: center;color: #909399">
                {{ formatI18n('/公用/提示/暂无数据') }}
              </el-row>
            </el-row>
          </el-row>
        </el-col>
      </el-row>
    </div>
    <div class="page" style="margin-top: 15px">
      <el-pagination :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
        @current-change="handleCurrentChange($event)" @size-change="handleSizeChange($event)" background
        layout="total, prev, pager, next, sizes,  jumper">
      </el-pagination>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="dialogShow = false">{{formatI18n('/公用/按钮/取消')}}</el-button>
      <el-button size="small" type="primary" @click="doModalClose()">{{formatI18n('/公用/按钮/确定')}}</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./CatogorySelectorDialog.ts"/>

<style lang="scss" scoped>
.select-category-dialog {
  display: flex;
  align-items: center;
  justify-content: center;
  @import "SelectorDialogCommon";
}
</style>
