<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:02
 * @LastEditTime: 2023-07-12 17:52:44
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\coupontenplate\cmp\RexCouponInfo.vue
 * 记得注释
-->
<template>
  <div class="rex-coupon-info">
    <div class="baseSettingFlag">{{ formatI18n("/权益/券/券模板/REX平台券") }}</div>
    <div>
      <el-form
        ref="couponRef"
        label-width="120px"
        :model="value"
        :rules="rules"
      >
        <el-form-item :label="formatI18n('/权益/券/券模板/净值')" prop="netWorth">
          <el-input
            :placeholder="formatI18n('/营销/券礼包活动/核销第三方券', '请输入')"
            style="width: 200px"
            v-model="value.netWorth"
            @change="handleNetWorthBlur"
            :controls="false"
          >
          <template slot="append"><span>{{formatI18n('/公用/券模板/元')}}</span></template>
          </el-input>
        </el-form-item>
        <el-form-item :label="formatI18n('/权益/券/券模板/单笔订单限用')" prop="limitedUse">
          <el-input
            :placeholder="formatI18n('/营销/券礼包活动/核销第三方券', '请输入')"
            style="width: 200px"
            v-model="value.limitedUse"
            @change="handlelimitedUseBlur"
            :controls="false"
          >
          <template slot="append"><span>{{formatI18n("/营销/券礼包活动/券礼包活动/张")}}</span></template>
          </el-input>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts" src="./RexCouponInfo.ts"></script>

<style lang="scss">
.rex-coupon-info {
  .flex {
    display: flex;
  }
}
.gray-tip {
  font-size: 14px;
  width: 600px;
  color: #9699a7;
}
.free-case {
  width: 800px;
}
</style>
