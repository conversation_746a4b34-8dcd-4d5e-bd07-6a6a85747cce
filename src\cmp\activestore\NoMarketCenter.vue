<template>
  <span class="active-store-view">
    <el-form :model="store" ref="form">
      <el-radio-group @change="doStoreRange" v-model="store.storeRangeType">
        <el-radio label="SAME" v-if="sameStore">{{ i18n("与活动门店一致") }}</el-radio>
        <el-radio label="ALL">{{ i18n("全部门店") }}</el-radio>
        <el-radio label="PART">{{ i18n("指定门店适用") }}</el-radio>
        <el-radio label="EXCLUDE">{{ i18n("指定门店不适用") }}</el-radio>
        <el-radio label="ZONE">{{ formatI18n("/公用/门店组件/指定区域适用") }}</el-radio>
      </el-radio-group>
      <span style="margin-left: 20px" v-if="store.storeRangeType === 'PART' || store.storeRangeType === 'EXCLUDE'">
        <el-button type="primary" @click="doSelect">{{ formatI18n("/公用/券模板", "选择") }}</el-button>
        <el-button type="primary" @click="doImport">{{ formatI18n("/公用/券模板", "导入") }}</el-button>
      </span>
      <div v-if="store.storeRangeType === 'ZONE'">
        <ActiveStoreArea v-model="store" @change="getAreaData" ref="activeStoreArea"> </ActiveStoreArea>
      </div>
      <div v-if="store.storeRangeType === 'PART' || store.storeRangeType === 'EXCLUDE'">
        <span v-if="store && store.stores">{{ getStoreCount(store.stores.length) }}</span>
      </div>
      <el-form-item v-if="internalValidate" prop="inputValue" :rules="inputValueRules" class="auto-expand-form-item"></el-form-item>
    </el-form>
    <StoreSelectorDialog ref="selectGoodsScopeDialog" @summit="doSubmitGoods"></StoreSelectorDialog>
    <StoreMulPromotionSelectorDialog :marketCenterId="marketCenterId" :marketCenterName="marketCenterName" ref="mulPromotionStore"
      @summit="doMulStoreSubmitGoods"></StoreMulPromotionSelectorDialog>
    <PromotionCenterSelectorDialog ref="selectPromotionCenterSelectorDialog" @summit="doPromotionSubmitGoods"></PromotionCenterSelectorDialog>
    <ImportDialog :dialogShow.sync="importDialogShow" :importUrl="importUrl" :templatePath="templatePath" @dialogClose="importDialogShow = false"
      @upload-success="doUploadSuccess" :templateName="formatI18n('/公用/券模板', '门店模板')" :title="formatI18n('/公用/券模板', '导入')">
    </ImportDialog>
    <ImportResultDialog :data="importResult" :dialogShow="importResultDialogShow" @importResultDialogClose="importResultDialogShow = false">
    </ImportResultDialog>
  </span>
</template>

<script lang="ts" src="./NoMarketCenter.ts"></script>

<style lang="scss" scoped>
.active-store-view {
  .promotion-center-store {
    max-width: 800px;
  }

  .title {
    padding-left: 10px;
    height: 32px;
    line-height: 32px;
    background-color: #ced0da;
  }

  .rows {
    padding: 10px;
    border: 1px solid #ced0da;
    border-top: none;
  }
}
</style>
