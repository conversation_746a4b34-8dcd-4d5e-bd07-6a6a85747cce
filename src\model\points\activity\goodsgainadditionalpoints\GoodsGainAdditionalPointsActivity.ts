import ActivityBody from "model/common/ActivityBody";
import GoodsRange from "model/common/GoodsRange";
import NumberCalculatorItem from "model/points/activity/goodsgainadditionalpoints/NumberCalculatorItem";
import DateTimeCondition from "model/common/DateTimeCondition";
import ChannelRange from "model/common/ChannelRange";
import GradesRange from "model/common/GradeRange";
import PushGroup from "model/precisionmarketing/pushplan/PushGroup";

// 商品积分加倍规则
export default class GoodsGainAdditionalPointsActivity {
	needAudit: Nullable<boolean> = null;
	// 活动信息
	activityBody: Nullable<ActivityBody> = new ActivityBody();
	// 活动时间限制
	dateTimeCondition = new DateTimeCondition();
	// 商品范围信息
	goodsRange: Nullable<GoodsRange> = new GoodsRange();
	// 每人每天限享
	dailyTimesPerMember: Nullable<number> = null;
	// stair
	stairCalculator: NumberCalculatorItem[] = [];
	// step
	stepCalculator: Nullable<NumberCalculatorItem> = null;
	//渠道范围
	channelRange: Nullable<ChannelRange> = null;
	// 参与会员等级
	gradeRange: Nullable<GradesRange> = null;
	// 客群
	rule: Nullable<PushGroup> = null
	// 参与叠加促销
	joinPromotion: Nullable<boolean> = false;
	// 排除优惠商品
	excludeFavourGoodTypes: string[] = []
	// 排除优惠金额
	excludeFavourAmountTypes: string[] = ["PROMOTION","COUPON","MEMBER","OTHER"]
}
