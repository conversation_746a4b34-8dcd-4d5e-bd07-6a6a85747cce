<template>
    <div class="empty-warp" :style="{height: height + 'px'}">
        <img class="empty-img" src="~assets/image/auth/coupon-empty.png" alt="">
        <div class="empty-text" v-if="emptyText">{{ emptyText }}</div>
        <slot></slot>
    </div>
</template>
<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
@Component({
    components: {},
})
export default class Index extends Vue {
    @Prop({
        default: 400
    })
    height: number
    @Prop({
        default: ''
    })
    emptyText: string
}
</script>
<style lang="scss" scoped>
.empty-warp {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .empty-img {
        width: 80px;
        height: 80px;
        margin-bottom: 8px;
    }
    .empty-text {
        width: 100%;
        color: #626973;
        font-size: 14px;
        text-align: center;

    }
}
</style>