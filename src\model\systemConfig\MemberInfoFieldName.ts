import {BenefitCardState} from "model/benefitCard/BenefitCardState";

export enum MemberInfoFieldName {
    PHONE = 'PHONE',
    NAME = 'NAME',
    ANNUAL_INCOME = 'ANNUAL_INCOME',
    HOBBIES = 'HOBBIES',
    INDUSTRY = 'INDUSTRY',
    EDUCATION = 'EDUCATION',
    GENDER = 'GENDER',
    BIRTHDAY = 'BIRTHDAY',
    EMAIL = 'EMAIL',
    ID_CARD = 'ID_CARD',
    AVATAR = 'AVATAR',
    NICK_NAME = 'NICK_NAME',
    ADDRESS = 'ADDRESS'
}

export const MemberInfoFieldNameMap = {
    [MemberInfoFieldName.PHONE]: '手机号',
    [MemberInfoFieldName.NAME]: '姓名',
    [MemberInfoFieldName.ANNUAL_INCOME]: '年收入',
    [MemberInfoFieldName.HOBBIES]: '兴趣爱好',
    [MemberInfoFieldName.INDUSTRY]: '行业',
    [MemberInfoFieldName.EDUCATION]: '学历',
    [MemberInfoFieldName.GENDER]: '性别',
    [MemberInfoFieldName.BIRTHDAY]: '生日',
    [MemberInfoFieldName.EMAIL]: '邮箱',
    [MemberInfoFieldName.ID_CARD]: '身份证号码',
    [MemberInfoFieldName.AVATAR]: '头像',
    [MemberInfoFieldName.NICK_NAME]: '昵称',
    [MemberInfoFieldName.ADDRESS]: '地址'
}

