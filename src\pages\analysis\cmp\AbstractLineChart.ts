/*
 * @Author: 黎钰龙
 * @Date: 2024-03-08 11:16:41
 * @LastEditTime: 2024-05-29 16:34:50
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\analysis\cmp\AbstractLineChart.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import AnalysisChartData from 'model/analysis/AnalysisChartData';
import { Vue } from 'vue-property-decorator';

class DataItem {
  name: string = '' // 数据名
  data: any[] = []  // 数据值
  yAxisIndex: 0 | 1 = 0  // 是否为右坐标轴
}

@I18nPage({
  prefix: [
    '/公用/券模板'
  ],
  auto: true
})
//双y轴折线图抽象类
export default abstract class AbstractLineChart extends Vue {

  // 折线图数据名
  get legendNames() {
    return this.valueArray.map((item) => item.name)
  }

  // 横坐标分段点
  get xAxisArray() {
    if (this.valueArray?.length) {
      return this.valueArray[0].data.map((item) => item.date) //选用第一组数据
    } else {
      return []
    }
  }

  // 图表数据
  abstract get valueArray(): DataItem[]

  // 获取图表series项数据
  // 入参：[[数据名，数据值，是否为右坐标轴], ...]
  doTransValueArray(originValueArr: any[][]) {
    return originValueArr.map((item) => {
      // 公共配置项
      const itemObj = {
        type: 'line',
        lineStyle: {
          width: 1
        },
      }
      // 数据相关项
      const dataObj = {
        name: item[0],
        data: item[1],
        yAxisIndex: item[2],
        isInteger: item[3],
      }
      return {
        ...itemObj,
        ...dataObj
      }
    })
  }

  //两个数组每项相除，每项保留四位小数
  doDivideGetPercent(value1: AnalysisChartData[], value2: AnalysisChartData[]) {
    const arr = []
    for (let index = 0; index < value1.length; index++) {
      const item1 = value1[index].value
      const item2 = value2[index].value
      const computeRes = item2 != 0 ? (Number(item1) / Number(item2)).toFixed(4) : 0
      const resItem = new AnalysisChartData()
      resItem.index = index.toString()
      resItem.date = value1[index].date || value2[index].date
      resItem.value = computeRes as any
      arr.push(resItem)
    }
    return arr
  }
};