import { Component, Vue } from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import ListWrapper from 'cmp/list/ListWrapper.vue'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import PrePayAdjustBillApi from 'http/prepay/adjustbill/PrePayAdjustBillApi'
import PrepayAdjustBillFilter from 'model/prepay/adjustbill/PrepayAdjustBillFilter'
import PrepayAdjustBill from 'model/prepay/adjustbill/PrepayAdjustBill'
import ImportDialog from 'cmp/importdialog/ImportDialog.vue'
import ConstantMgr from 'mgr/ConstantMgr'
import PrePayConfigApi from 'http/prepay/config/PrePayConfigApi'
import I18nPage from 'common/I18nDecorator'
import StoreValueAdjustPermission from 'pages/deposit/mbrdeposit/adjust/StoreValueAdjustPermission'
import DownloadCenterDialog from 'pages/main/dowmload_center_dialog/DownloadCenterDialog.vue'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import RSOrg from "model/common/RSOrg";
import RSOrgFilter from "model/common/RSOrgFilter";
import OrgApi from "http/org/OrgApi";
import SysConfigApi from "http/config/SysConfigApi";
import CommonUtil from 'util/CommonUtil'
import RejectDialog from './dialog/RejectDialog.vue'
import BPrepayAdjustBillOperatorRequest from 'model/default/BPrepayAdjustBillOperatorRequest'

enum StateEnum {
  INITIAL = 'INITIAL', //未提交
  SUBMIT = 'SUBMIT', //已提交
  AUDITED = 'AUDITED', //已审核
  REJECTED = 'REJECTED', //已驳回
}

@Component({
  name: 'StoreValueAdjust',
  components: {
    FormItem,
    ListWrapper,
    SubHeader,
    ImportDialog,
    DownloadCenterDialog,
    BreadCrume,
    RejectDialog
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/储值/会员储值/储值调整单/列表',
    '/公用/按钮',
    '/公用/过滤器'
  ]
})
export default class StoreValueAdjust extends Vue {
  i18n: I18nFunc
  query: PrepayAdjustBillFilter = new PrepayAdjustBillFilter()
  selectedArr: PrepayAdjustBill[] = []
  permission = new StoreValueAdjustPermission()
  activeName: StateEnum | 'ALL' = 'ALL'
  switchFlag = false
  $refs: any
  importUrl = 'v1/prepay-adjust-bill/importExcel' // 导入文件
  selectAll = ''
  types: any = []
  total = {
    all: 0,
    initial: 0,
    submit: 0,
    audit: 0,
    rejected: 0
  }
  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  tableData: any[] = []
  dialogShow = false
  fileDialogvisiable = false
  panelArray: any = []
  showOrg = false // 控制模态框的展示
  orgs: RSOrg[] = []
  hasOaPermission: boolean = false // 控制OA审批按钮的展示
  operateRowId: string = '' // 操作的单据id

  get templatePath() {
    if (location.href.indexOf('localhost') === -1) {
      if (this.switchFlag) { // 多账户模板
        return 'template_multiple_account_balance_adjust.xlsx'
      } else {
        return 'template_balance_adjust.xlsx'
      }
    } else {
      if (this.switchFlag) { // 多账户模板
        return 'template_multiple_account_balance_adjust.xlsx'
      } else {
        return 'template_balance_adjust.xlsx'
      }
    }
  }
  get getAllCount() {
    return `${this.i18n('全部')}(${this.total.all})`
  }
  get getNoAudit() {
    return `${this.i18n('未提交')}(${this.total.initial})`
  }
  get getSubmit() {
    return `${this.i18n('已提交')}(${this.total.submit})`
  }
  get getRejected() {
    return `${this.i18n('已驳回')}(${this.total.rejected})`
  }
  get getAudit() {
    return `${this.i18n('已审核')}(${this.total.audit})`
  }
  // 批量审核权限
  get batchAuditPermission() {
    return this.permission.auditable && !this.hasOaPermission
  }
  // 批量驳回权限
  get batchRejectPermission() {
    return this.permission.rejectable && !this.hasOaPermission
  }
  // 批量提交权限
  get batchSubmitPermission() {
    return this.permission.submitable
  }
  created() {
    this.panelArray = [
      {
        name: this.formatI18n('/公用/菜单/储值调整单'),
        url: ''
      }
    ]
    this.getState()
    this.getStoreValueList()
    this.getPrePermission()
    this.getAcountTypes()
    this.getOrg()
    this.getConfig()
  }
  doSelectAll() {
    if (this.selectAll) {
      for (let row of this.tableData) {
        this.$refs.table.toggleRowSelection(row, true)
      }
    } else {
      this.$refs.table.clearSelection();
    }
  }
  doDialogClose() {
    this.dialogShow = false
  }
  doDownloadDialogClose() {
    this.fileDialogvisiable = false
  }
  doUploadSuccess() {
    this.getStoreValueList()
    this.fileDialogvisiable = true
  }
  // 批量导入
  doBatchImport() {
    this.dialogShow = true
  }
  doStoreValueAdd() {
    this.$router.push({ name: 'store-value-adjust-Add', query: { from: 'add' } })
  }
  doStoreValueReason() {
    this.$router.push({ name: 'store-value-adjust-reason' })
  }
  doBatchDelete() {
    if (this.selectedArr.length <= 0) {
      this.$message.warning(this.i18n('请先勾选要删除的单据'))
      return
    }
    this.$confirm(this.i18n('是否批量删除这些单据?'), this.i18n('批量删除'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消')
    }).then(() => {
      this.submitBatchDelete()
    })
  }
  doBatchAudit() {
    if (this.selectedArr.length <= 0) {
      this.$message.warning(this.i18n('请先勾选要删除的单据'))
      return
    }
    this.$confirm(this.i18n('是否批量审核这些单据?'), this.i18n('批量审核'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消')
    }).then(() => {
      this.submitBatchAudit()
    })
  }
  doHandleClick() {
    this.page.currentPage = 1
    if (this.activeName === 'ALL') {
      this.query.stateEquals = null
    } else if (this.activeName === StateEnum.INITIAL) {
      this.query.stateEquals = StateEnum.INITIAL
    } else if (this.activeName === StateEnum.SUBMIT) {
      this.query.stateEquals = StateEnum.SUBMIT
    } else if (this.activeName === StateEnum.AUDITED) {
      this.query.stateEquals = StateEnum.AUDITED
    } else if (this.activeName === StateEnum.REJECTED) {
      this.query.stateEquals = StateEnum.REJECTED
    }
    this.getStoreValueList()
  }
  /**
   * 查询
   */
  doSearch() {
    this.page.currentPage = 1
    this.getState()
    this.getStoreValueList()
  }
  handleSelectionChange(val: any) {
    this.selectedArr = val
  }
  /**
   * 重置
   */
  doReset() {
    this.query = new PrepayAdjustBillFilter()
    this.getState()
    this.getStoreValueList()
  }

  /**
   * 去详情
   */
  doGoDtl(row: any) {
    this.$router.push({ name: 'store-value-adjust-dtl', query: { id: row.billNumber } })
  }
  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.getStoreValueList()
  }
  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.getStoreValueList()
  }

  private getStoreValueList() {
    const loading = CommonUtil.Loading()
    this.query.page = this.page.currentPage - 1
    this.query.pageSize = this.page.size
    PrePayAdjustBillApi.query(this.query).then((resp: any) => {
      if (resp && resp.data) {
        this.tableData = resp.data
        this.page.total = resp.total
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    }).finally(() => {
      loading.close()
    })
  }
  private submitBatchDelete() {
    let ids: string[] = []
    if (this.selectedArr && this.selectedArr.length > 0) {
      this.selectedArr.forEach((item) => {
        // if (item.state !== 'AUDITED') {
        //   ids.push(item.billNumber!)
        // }
        ids.push(item.billNumber!)
      })
    }
    PrePayAdjustBillApi.batchRemove(ids).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(resp.data)
        this.getState()
        this.getStoreValueList()
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  private getAcountTypes() {
    PrePayAdjustBillApi.getAccountTypes().then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.types = resp.data
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  private submitBatchAudit() {
    let ids: string[] = []
    if (this.selectedArr && this.selectedArr.length > 0) {
      this.selectedArr.forEach((item) => {
        ids.push(item.billNumber!)
      })
    }
    PrePayAdjustBillApi.batchAudit(ids).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(resp.data)
        this.getState()
        this.getStoreValueList()
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  private getState() {
    this.query.stateEquals = null
    this.activeName = 'ALL'
    PrePayAdjustBillApi.stats(this.query).then((resp) => {
      if (resp.code === 2000 && resp.data) {
        this.total.all = resp.data.total || 0
        this.total.initial = resp.data.sumInitial || 0
        this.total.audit = resp.data.sumAudited || 0
        this.total.submit = resp.data.sumSubmit || 0
        this.total.rejected = resp.data.sumRejected || 0
      } else {
        throw new Error(resp.msg!)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  private getPrePermission() {
    const loading = this.$loading(ConstantMgr.loadingOption)
    PrePayConfigApi.get().then((resp: any) => {
      if (resp && resp.code === 2000) {
        loading.close()
        if (resp.data && resp.data.enableMultipleAccount) { // 开启多账户
          this.switchFlag = resp.data.enableMultipleAccount
        } else {
          this.switchFlag = false // 未开启多账户
        }
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
      loading.close()
    })
  }

  private getConfig() {
    // 获取储值调整单oa配置
    PrePayAdjustBillApi.getOaConfig().then((resp) => {
      if (resp.code === 2000) {
        this.hasOaPermission = resp.data || false
      } else {
        throw new Error(resp.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    })

    // 获取系统配置
    SysConfigApi.get().then((resp: any) => {
      if (resp && resp.data) {
        this.showOrg = resp.data.enableMultiMarketingCenter
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private getOrg() {
    let params: RSOrgFilter = new RSOrgFilter()
    params.orgTypeEquals = "PHX"
    params.page = 0
    params.pageSize = 0
    OrgApi.query(params).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.orgs = resp.data
      }
    }).catch((error: any) => {
      this.$message.error(error.message)
    })
  }

  // 批量驳回触发
  doBatchReject() {
    if (this.selectedArr.length <= 0) {
      this.$message.warning(this.i18n('请先勾选单据'))
      return
    }
    this.$refs.rejectDialog.visible = true
  }

  // 提交驳回原因
  doSubmitReject(remark: string) {
    if (this.operateRowId) {
      this.doRejectRow(this.operateRowId, remark)
    } else {
      this.submitBatchReject(remark)
    }
    this.operateRowId = ''
  }

  // 取消单据操作
  doCancelOperateRow() {
    this.operateRowId = ''
  }

  // 批量驳回
  private submitBatchReject(remark: string) {
    let ids: string[] = []
    if (this.selectedArr && this.selectedArr.length > 0) {
      this.selectedArr.forEach((item) => {
        ids.push(item.billNumber!)
      })
    }
    PrePayAdjustBillApi.batchRejected({ billNumbers: ids, remark: remark }).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(resp.data)
        this.getState()
        this.getStoreValueList()
      } else {
        throw new Error(resp.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    })
  }

  // 批量提交触发
  doBatchSubmit() {
    if (this.selectedArr.length <= 0) {
      this.$message.warning(this.i18n('请先勾选单据'))
      return
    }
    this.$confirm(this.i18n('是否批量提交这些单据?'), this.i18n('批量提交'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消')
    }).then(() => {
      this.submitBatchSubmit()
    })
  }

  // 批量提交
  private submitBatchSubmit() {
    let ids: string[] = []
    if (this.selectedArr && this.selectedArr.length > 0) {
      this.selectedArr.forEach((item) => {
        ids.push(item.billNumber!)
      })
    }
    PrePayAdjustBillApi.batchSubmit(ids).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(resp.data)
        this.getState()
        this.getStoreValueList()
      } else {
        throw new Error(resp.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    })
  }

  // 修改
  doModify(row: PrepayAdjustBill) {
    this.$router.push({ name: 'store-value-adjust-Add', query: { id: row.billNumber!, from: 'edit' } })
  }

  // 提交
  doSubmit(row: PrepayAdjustBill) {
    return new Promise<void>((resolve, reject) => {
      const operatorRequest: BPrepayAdjustBillOperatorRequest = new BPrepayAdjustBillOperatorRequest()
      operatorRequest.billNum = row.billNumber!
      PrePayAdjustBillApi.submit(operatorRequest).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.i18n('操作成功'))
          resolve()
          this.getStoreValueList()
        } else {
          throw new Error(resp.msg!)
        }
      }).catch((error) => {
        this.$message.error(error.message)
        reject()
      })
    })
  }

  // 提交并审核
  async doSubmitAndAudit(row: PrepayAdjustBill) {
    await this.doSubmit(row)
    await this.doAudit(row)
  }

  // 审核
  doAudit(row: PrepayAdjustBill) {
    return new Promise<void>((resolve, reject) => {
      PrePayAdjustBillApi.audit(row.billNumber!).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.i18n('操作成功'))
          resolve()
          this.getStoreValueList()
        } else {
          throw new Error(resp.msg!)
        }
      }).catch((error) => {
        reject()
        this.$message.error(error.message)
      })
    })
  }

  // 驳回
  doReject(row: PrepayAdjustBill) {
    this.operateRowId = row.billNumber!
    this.$refs.rejectDialog.visible = true
  }

  // 驳回单据
  doRejectRow(id: string, remark: string) {
    const operatorRequest: BPrepayAdjustBillOperatorRequest = new BPrepayAdjustBillOperatorRequest()
    operatorRequest.billNum = id
    operatorRequest.remark = remark
    PrePayAdjustBillApi.rejected(operatorRequest).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(this.i18n('操作成功'))
        this.getStoreValueList()
      } else {
        throw new Error(resp.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    })
  }

}