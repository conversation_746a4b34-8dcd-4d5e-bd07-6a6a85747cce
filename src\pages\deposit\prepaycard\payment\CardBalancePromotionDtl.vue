<template>
  <div class="card-balance-promotion-dtl">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button key="1" @click="audit" type="primary" v-if="permission.auditable && data.body.state === 'INITAIL'">审核</el-button>
        <el-button key="2" @click="copy" v-if="permission.editable">复制</el-button>
        <el-button key="3" @click="stop" v-if="permission.terminable && ['UNSTART', 'PROCESSING'].indexOf(data.body.state) > -1">终止</el-button>
        <el-button key="4" @click="edit" v-if="permission.editable && data.body.state === 'INITAIL'">修改</el-button>
        <el-button key="5" @click="del" v-if="permission.editable && data.body.state === 'INITAIL'" type="danger">删除</el-button>
      </template>
    </BreadCrume>
    <div style="overflow: auto;height: 95%">
      <div style="margin: 20px">
        <el-row>
          <el-col :span="8" style="padding-right: 20px">
            <p class="text-secondary"><span>活动号：</span>{{data.body.activityId}}</p>
            <p class="text-primary" :title="data.body.name" no-i18n>{{data.body.name}}</p>
          </el-col>
          <el-col :span="8">
            <p class="text-secondary">状态</p>
            <p class="text-primary">
              <ActivityState :state="data.body.state" />
            </p>
          </el-col>
          <el-col :span="8">
            <p class="text-secondary">所属主题</p>
            <p class="text-primary" no-i18n>
              {{data.body.topicName|nullable}}
            </p>
          </el-col>
        </el-row>
        <hr />
        <el-row>
          <!-- <span class="text-secondary">活动时间：</span>{{ data.body.beginDate|dateFormate2 }} {{i18n('至')}}
          {{ data.body.endDate|dateFormate2 }} -->
          <el-col style="width: 70px;" class="text-secondary">
            {{ formatI18n('/营销/积分活动/积分活动/商品满额加送积分/详情页面/活动信息/活动时间：') }}
          </el-col>
          <el-col :span="18" style="display: flex;">
            <div>
              {{ data.body.beginDate|dateFormate2 }}
              {{ formatI18n('/营销/积分活动/积分活动/商品满额加送积分/新建页面/活动信息/至') }}
              {{ data.body.endDate|dateFormate2 }}
            </div>
            <DateTimeConditionDtl style="margin-left: 30px" :value="data.dateTimeCondition"></DateTimeConditionDtl>
          </el-col>
        </el-row>
        <el-row style="display: flex;margin-top: 25px">
          <div class="text-secondary" style="min-width: 70px">{{ i18n('/营销/积分活动/积分活动/商品满额加送积分/详情页面/活动信息/活动门店：') }}</div>
          <ActiveStoreDtl style="width: 60%;min-width: 800px;margin-top: -8px;" no-i18n :data="data.body.stores"></ActiveStoreDtl>
        </el-row>
        <el-row style="display: flex;margin-top: 25px">
          <div class="text-secondary" style="min-width: 70px">{{ i18n('/营销/券礼包活动/券礼包活动/活动次数限制') + '：' }}</div>
          <div>{{activityLimitStr}}</div>
        </el-row>
      </div>
      <div style="background-color: #eeeff1;height: 15px;"></div>
      <div style="margin: 20px">
        <div style="font-weight: 600">支付优惠规则</div>
        <div style="margin-top: 20px;padding-left: 20px">
          <el-row>
            <el-col :span="3" class="text-secondary">适用卡模板：</el-col>
            <el-col :span="12" v-if="data.cardType === 'none'">
              <div style="margin-bottom: 10px">指定卡模板</div>
              <el-row v-for="(item) of data.cardTemplates" style="margin-bottom: 10px" :key="item.id">
                <el-button no-i18n type="text" @click="gotoTplDtl(item.id)" style="padding: 0">{{item.name}}</el-button>
              </el-row>
            </el-col>
            <el-col :span='12' v-else-if="data.cardType === 'all'">
              全部预付卡（储值卡和礼品卡）
            </el-col>
            <el-col :span='12' v-else-if="data.cardType === 'rechargeableCard'">
              全部储值卡
            </el-col>
          </el-row>
          <el-row style="margin-top: 25px">
            <el-col :span="3" class="text-secondary">适用商品：</el-col>
            <el-col :span="12">
              <GoodsScopeDtl no-i18n :goodsMatchRuleMode="goodsMatchRuleMode" :goods="data.goods" />
            </el-col>
          </el-row>
          <el-row style="margin-top: 25px">
            <el-col :span="3" class="text-secondary">优惠门槛：</el-col>
            <el-col :span="12" v-if="data.favThreshold">
              <i18n k="/储值/预付卡/预付卡支付活动/详情页面/适用商品消费满{0}元及以上">
                <template slot="0">
                  <span style="font-weight: 600">&nbsp;{{data.favThreshold|amount}}&nbsp;</span>
                </template>
              </i18n>
            </el-col>
            <el-col :span="12" v-else>
              不限制
            </el-col>
          </el-row>
          <el-row style="margin-top: 25px">
            <el-col :span="3" class="text-secondary">{{i18n('优惠计算')}}：</el-col>
            <el-col :span="12">
              <span v-if="data.newStrategy === 'BY_FULL_AMOUNT'">{{i18n('商品满金额设置')}}</span>
              <span v-else-if="data.newStrategy === 'BY_FULL_QTY'">{{i18n('商品满数量设置')}}</span>
              <span v-else-if="data.newStrategy === 'BY_AMOUNT'">{{i18n('商品每满金额设置')}}</span>
              <span v-else-if="data.newStrategy === 'BY_QTY'">{{i18n('商品每满数量设置')}}</span>
            </el-col>
          </el-row>
          <el-row style="margin-top: 25px">
            <el-col :span="3" class="text-secondary">优惠规则设置：</el-col>
            <template v-if="data.gradeSameStepValue">
              <el-col :span="12">
                <el-row v-for="(item,index) in data.gradeSameStepValue.stepValues" :key="index">
                  <i18n advance :k="sameGradeRuleStr">
                    <template slot-scope="{items}">
                      <div style="float: left">{{ items.s0.prefix }}</div>
                      <div style="float: left;margin:0 4px;line-height:19px">{{item.threshold|amount}}</div>
                      <div style="float: left">{{ items.s0.suffix }}</div>
                      <div style="float: left;margin:0 4px;line-height:19px">{{item.value|amount}}</div>
                      <div style="float: left">{{ items.s1.suffix }}</div>
                    </template>
                  </i18n>
                </el-row>
              </el-col>
            </template>
            <el-col :span="12" v-if="data.gradeDifferentStepValue" class="rule-table">
              {{i18n('/储值/预付卡/预付卡支付活动/编辑页面/不同等级会员适用 不同规则')}} <br /><br />
              <el-row class="rule-table-header">
                <el-col :span="4">会员等级</el-col>
                <el-col :span="20">预付卡支付优惠规则</el-col>
              </el-row>
              <el-row class="rule-table-line" v-for="(grade,index) of gradeList" :key="index">
                <el-col :span="4">{{'[' + grade.code + '] ' + grade.name}}</el-col>
                <el-col :span="20" v-if="gradeDifferentStepValueMap.hasOwnProperty(grade.code) && gradeDifferentStepValueMap[grade.code]">
                  <el-row v-for="(diffItem,ind) in gradeDifferentStepValueMap[grade.code].stepValues" :key="ind">
                    <i18n advance :k="sameGradeRuleStr">
                      <template slot-scope="{items}">
                        <div style="float: left">{{ items.s0.prefix }}</div>
                        <div style="float: left;margin:0 4px;line-height:19px">
                          {{diffItem.threshold|amount}}
                        </div>
                        <div style="float: left">{{ items.s0.suffix }}</div>
                        <div style="float: left;margin:0 4px;line-height:19px">
                          {{diffItem.value|amount}}
                        </div>
                        <div style="float: left">{{ items.s1.suffix }}</div>
                      </template>
                    </i18n>
                  </el-row>
                </el-col>
                <el-col :span="20" v-if="!gradeDifferentStepValueMap.hasOwnProperty(grade.code)">
                  不可参与预付卡支付优惠
                </el-col>
              </el-row>
            </el-col>
          </el-row>
          <el-row style="margin-top: 25px">
            <el-col :span="3" class="text-secondary">{{i18n('叠加促销')}}：</el-col>
            <el-col :span="21">{{data.excludePromotion?i18n('是'):i18n('否')}}</el-col>
          </el-row>
        </div>
      </div>
    </div>

  </div>
</template>

<script lang="ts" src="./CardBalancePromotionDtl.ts">
</script>

<style lang="scss">
.card-balance-promotion-dtl {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: hidden;

  .text-primary {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    font-weight: 500;
    font-style: normal;
    font-size: 20px;
    color: #515151;
  }

  .text-secondary {
    /*text-overflow: ellipsis;*/
    /*white-space: nowrap;*/
    /*overflow: hidden;*/
    color: rgba(51, 51, 51, 0.65);
  }

  .rule-table {
    width: 70%;

    .rule-table-header {
      padding: 10px;
      background-color: #e6e6e6;
      border: 1px solid #e6e6e6;
    }

    .rule-table-line {
      padding: 10px;
      border: 1px solid #e6e6e6;
    }
  }
}
</style>
