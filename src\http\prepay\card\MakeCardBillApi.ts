import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import MakeCardBill from 'model/prepay/card/MakeCardBill'
import MakeCardBillFilter from 'model/prepay/card/MakeCardBillFilter'
import MakeCardFailReason from 'model/prepay/card/MakeCardFailReason'
import MakeCardFailReasonFilter from 'model/prepay/card/MakeCardFailReasonFilter'
import MakeCardMessage from 'model/prepay/card/MakeCardMessage'
import MakeCardMessageFilter from 'model/prepay/card/MakeCardMessageFilter'

export default class MakeCardBillApi {
  /**
   * 审核制卡单
   * 审核制卡单。
   * 
   */
  static audit(billNumber: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/make-card-bill/audit/${billNumber}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量审核制卡单
   * 批量审核制卡单。
   * 
   */
  static batchAudit(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/make-card-bill/batch/audit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 作废制卡单
   * 作废制卡单。
   * 
   */
  static cancel(billNumber: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/make-card-bill/cancel/${billNumber}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取制卡单详情
   * 获取制卡单详情。
   * 
   */
  static get(billNumber: string): Promise<Response<MakeCardBill>> {
    return ApiClient.server().get(`/v1/make-card-bill/get/${billNumber}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询预付卡充值单
   * 分页查询预付卡充值单。
   * 
   */
  static query(body: MakeCardBillFilter): Promise<Response<MakeCardBill[]>> {
    return ApiClient.server().post(`/v1/make-card-bill/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除制卡单
   * 删除制卡单。
   * 
   */
  static remove(billNumber: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/make-card-bill/remove/${billNumber}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建预付卡制卡单
   * 新建预付卡制卡单。
   * 
   */
  static save(body: MakeCardBill): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/make-card-bill/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建并审核制卡单
   * 新建并审核制卡单。
   * 
   */
  static saveAndAudit(body: MakeCardBill): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/make-card-bill/saveAndAudit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改制卡单 -- 需要回传log字段
   * 修改制卡单 -- 需要回传log字段。
   * 
   */
  static saveModify(body: MakeCardBill): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/make-card-bill/saveModify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 导出卡列表信息
  * 导出卡列表信息
  * 
  */
  static exportCard(billNumber: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/make-card-bill/exportCard`, {}, {
      params: {
        billNumber: billNumber
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取excel密码
   * 获取excel密码
   * 
   */
  static getExcelPassword(billNumber: string): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/make-card-bill/getExcelPassword`, {}, {
      params: {
        billNumber: billNumber
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
     * 制卡完成
     * 制卡完成。
     * 
     */
  static finish(billNumber: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/make-card-bill/finish/${billNumber}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
     * 自动生成起始卡号
     * 自动生成起始卡号
     *
     */
  static autoMaxCardCode(length: number): Promise<Response<string>> {
    return ApiClient.server().get(`/v1/make-card-bill/autoMaxCardCode/${length}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询制卡失败信息
   * 分页查询制卡失败信息。
   * 
   */
  static queryFailReason(body: MakeCardFailReasonFilter): Promise<Response<MakeCardFailReason[]>> {
    return ApiClient.server().post(`/v1/make-card-bill/queryFailReason`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询制卡信息
   * 分页查询制卡信息。
   * 
   */
  static queryMessage(body: MakeCardMessageFilter): Promise<Response<MakeCardMessage[]>> {
    return ApiClient.server().post(`/v1/make-card-bill/queryMessage`, body, {
    }).then((res) => {
      return res.data
    })
  }
}
