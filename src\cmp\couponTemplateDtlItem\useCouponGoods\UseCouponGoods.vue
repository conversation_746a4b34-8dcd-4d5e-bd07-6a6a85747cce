<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-30 15:58:30
 * @LastEditTime: 2025-02-20 18:15:34
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\couponTemplateDtlItem\useCouponGoods\UseCouponGoods.vue
 * 记得注释
-->
<template>
  <FormItem :label="i18n('用券商品') + '：'" v-if="isGoodsCoupon || isSomeCoupon || isExchangeCoupon">
    <!--提货券-->
    <template v-if="isGoodsCoupon">
      <div style="padding-top:7px">
        <div style="margin-bottom:6px">
          <span v-if="!data.coupons.enablePayApportion">
            {{ formatI18n("/公用/券模板/提货券/用券商品", "按优惠方式记流水") }}，
          </span>
          <span v-else>
            {{i18n("按支付方式记流水，记账总金额") + "："}}
            <span style="font-weight: 600;">{{getAllAmount}}</span>
            {{formatI18n("/营销/积分活动/积分活动/积分抵现活动/立即新建/单笔抵现上限/元")}}，
          </span>
          <span>
            <span>{{ formatI18n("/资料/门店", "共") + data.coupons.pickUpCouponAttribute.pickUpGoods.length +
							formatI18n("/资料/员工", "项") }}，</span>
            <span style="margin-left: 10px">{{
							data.coupons.pickUpCouponAttribute.pickUpGoods.length +
							formatI18n("/公用/券模板/提货券/用券商品/选") +
							data.coupons.pickUpCouponAttribute.pickQty
						}}
            </span>
          </span>
        </div>
        <el-table :data="data.coupons.pickUpCouponAttribute.pickUpGoods" style="width: 800px" stripe>
          <el-table-column :label="formatI18n('/营销/积分活动/积分活动/单品满数量加送积分活动/编辑页面/积分加送规则', '商品')" fixed prop="goods.name" width="264">
            <template slot-scope="scope">
              <span class="overflow-text" :title="scope.row.goods.name">{{ scope.row.goods.name }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="formatI18n('/公用/券模板/提货券/用券商品', '售价(元)')" fixed prop="price">
            <template slot-scope="scope">
              {{ scope.row.price | fmt }}
            </template>
          </el-table-column>
          <el-table-column v-if="data.coupons.enablePayApportion == true" :label="i18n('记账售价(元)')" fixed prop="bookPayPrice">
            <template slot-scope="scope">
              {{ scope.row.bookPayPrice | fmt }}
            </template>
          </el-table-column>
          <el-table-column :label="formatI18n('/公用/券模板/提货券/用券商品', '商品数量(件)')" fixed prop="qty">
            <template slot-scope="scope">
              {{ scope.row.qty }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </template>
    <!--非运费券、兑换券、积分券-->
    <template v-if="isSomeCoupon">
      <div style="width: 60%;margin-top:20px">
        <GoodsScopeDtl :goods="data.coupons.useGoods" />
      </div>
    </template>
    <!-- 兑换券 -->
    <template v-if="isExchangeCoupon">
      <div class="goods-tab-block">
        <el-tabs v-if="hasUseGoods" @tab-click="handleClick" v-model="currentGoodsTab" type="card">
          <el-tab-pane v-for="(item,index) in data.coupons.exchangeGoodsCouponAttribute.pickUpGoodsGroups" :key="index" :name="index.toString()"
            :label="i18n('第') + item.sequence + i18n('组')">
            <div>
              <span>
                {{formatI18n("/资料/门店", "共") + item.exchangeGoods.length + formatI18n("/资料/员工", "项")}}
              </span>
              <span style="margin: 0 10px">
                <template v-if="isAppreciationGoods || isCategory(item.exchangeGoods)">
                  {{ item.exchangeGoods.length + formatI18n("/公用/券模板/提货券/用券商品/选") + item.exchangeQty}}
                </template>
                <i18n k="/公用/券模板/任选{0}项" v-else>
                  <template slot="0">{{item.exchangeQty}}</template>
                </i18n>
              </span>
              <span v-if="item.enableExchangeSameGood">{{i18n('可重复选')}}</span>
              <span v-else>{{i18n('不可重复选')}}</span>
            </div>
            <el-table :data="currentData" style="width: 800px">
              <el-table-column :label="formatI18n('/营销/积分活动/积分活动/单品满数量加送积分活动/编辑页面/积分加送规则', '商品')" v-if="currentData[0].goods" fixed prop="goods.name">
                <template slot-scope="scope">
                  <div :title="scope.row.goods.name">[{{ scope.row.goods.id }}]{{ scope.row.goods.name }}</div>
                </template>
              </el-table-column>
              <el-table-column :label="i18n('品类')" v-if="currentData[0].category" fixed prop="category.name">
                <template slot-scope="scope">
                  <div :title="scope.row.category.name">[{{ scope.row.category.id }}]{{ scope.row.category.name }}</div>
                </template>
              </el-table-column>
              <el-table-column :label="formatI18n('/公用/券模板/提货券/用券商品', '售价(元)')" v-if="currentData[0].price" fixed prop="price">
                <template slot-scope="scope">
                  {{ scope.row.price | fmt }}
                </template>
              </el-table-column>
              <el-table-column v-if="data.coupons.enablePayApportion == true" :label="formatI18n('/公用/券模板/提货券/用券商品', '记账金额(元)')" fixed
                prop="bookPayPrice">
                <template slot-scope="scope">
                  {{ scope.row.bookPayPrice | fmt }}
                </template>
              </el-table-column>
              <el-table-column :label="formatI18n('/公用/券模板/提货券/用券商品', '商品数量')" fixed prop="qty">
                <template slot-scope="scope">
                  {{ scope.row.qty }}&nbsp;{{ scope.row.isDisp ? formatI18n("/公用/券模板/单品折扣券/用券门槛/千克") : formatI18n("/公用/券模板/单品折扣券/用券门槛/件") }}
                </template>
              </el-table-column>
            </el-table>
            <el-pagination v-if="item.exchangeGoods.length > page.pageSize" @current-change="onHandleCurrentChange" :page-size="page.pageSize"
              :current-page="page.currentPage" layout="total, prev, pager, next, jumper" :total="item.exchangeGoods.length" small>
            </el-pagination>
            <div style="margin-top: 10px;margin-bottom: 10px;"><span><b> {{i18n('赠品') + ' :'}}</b></span></div>
            <el-table :data="getGiftGoods()" style="width: 800px">
              <el-table-column :label="formatI18n('/营销/积分活动/积分活动/单品满数量加送积分活动/编辑页面/积分加送规则', '商品')" v-if="isGiftGoods" fixed prop="goods.name">
                <template slot-scope="scope">
                  <div :title="scope.row.goods.name">[{{ scope.row.goods.id }}]{{ scope.row.goods.name }}</div>
                </template>
              </el-table-column>
              <el-table-column :label="i18n('品类')" v-if="isGiftCategory" fixed prop="category.name">
                <template slot-scope="scope">
                  <div :title="scope.row.category.name">[{{ scope.row.category.id }}]{{ scope.row.category.name }}</div>
                </template>
              </el-table-column>
              <el-table-column :label="formatI18n('/公用/券模板/提货券/用券商品', '售价(元)')" v-if="currentData[0].price" fixed prop="price">
                <template slot-scope="scope">
                  {{ scope.row.price | fmt }}
                </template>
              </el-table-column>
              <el-table-column v-if="data.coupons.enablePayApportion == true" :label="formatI18n('/公用/券模板/提货券/用券商品', '记账金额(元)')" fixed
                prop="bookPayPrice">
                <template slot-scope="scope">
                  {{ scope.row.bookPayPrice | fmt }}
                </template>
              </el-table-column>
              <el-table-column :label="formatI18n('/公用/券模板/提货券/用券商品', '商品数量')" fixed prop="qty">
                <template slot-scope="scope">
                  {{ scope.row.qty }}&nbsp;{{ scope.row.isDisp ? formatI18n("/公用/券模板/单品折扣券/用券门槛/千克") : formatI18n("/公用/券模板/单品折扣券/用券门槛/件") }}
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
        <div v-else style="line-height:35px">--</div>
      </div>
    </template>
  </FormItem>
</template>

<script lang="ts" src="./UseCouponGoods.ts">
</script>

<style lang="scss" scoped>
.overflow-text {
  width: 264px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.goods-tab-block {
  padding-top: 1px;
}
::v-deep .el-tabs__item {
  font-size: 14px;
  font-weight: 500;
  line-height: 40px;
}
::v-deep .is-active {
  color: #409eff !important;
}
::v-deep .el-table th > .cell {
  font-size: 13px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #242633;
}
</style>