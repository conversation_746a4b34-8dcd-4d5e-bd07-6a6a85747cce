<template>
  <div class="member-balance-promotion-list">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
      </template>
    </BreadCrume>
    <div style="flex: 1;overflow: auto">
      <div class="current-page">
        <div class="create-activity-block" v-if="permission.viewable || permission.discountViewable">
          <el-row style="padding: 25px;">
            <el-col :span="8" v-if="permission.viewable">
              <div class="btn-item">
                <p class="btn-item-title">储值支付立减</p>
                <p class="btn-item-small-title">购买商品满金额或满数量，储值支付享立减</p>
                <br>
                <el-button v-if="permission.editable" type="primary" size="small" @click="createMbp">新建活动</el-button>
              </div>
            </el-col>
            <el-col :span="8" v-if="permission.discountViewable">
              <div class="btn-item">
                <p class="btn-item-title">储值支付折扣</p>
                <p class="btn-item-small-title">购买商品满金额，储值支付享折扣</p>
                <br>
                <el-button v-if="permission.discountEditable" type="primary" size="small" @click="createMbpd">新建活动</el-button>
              </div>
            </el-col>
          </el-row>
        </div>
        <div style="height: 15px;background-color: #EEEFF1" v-if="permission.viewable || permission.discountViewable"></div>
        <ListWrapper>
          <template slot="query">
            <el-form :inline="true" label-width="130px" class="queryForm">
              <el-row>
                <el-col :span="8">
                  <el-form-item label="活动名称">
                    <el-input v-model="query.nameLikes"/>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="所属主题">
                    <el-select
                            clearable
                            placeholder="请选择"
                            v-model="query.topicNameLikes">
                      <el-option no-i18n :label="item.name"
                                :value="item.name"
                                v-for="(item, index) in themes"
                                :key="index">{{ item.name }}
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="活动号">
                    <el-input v-model="query.activityIdLikes"/>
                  </el-form-item>
                </el-col>
                <el-col :span="8"  v-if="permission.viewable && permission.discountViewable">
                  <el-form-item label="活动类型">
                    <el-select
                            clearable
                            placeholder="请选择"
                            v-model="query.activityTypeEquals">
                      <el-option no-i18n :label="item.label"
                                 :value="item.code"
                                 v-for="(item, index) in activityTypes"
                                :key="index">{{ item.label }}
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="商品条码">
                    <el-input v-model="query.barCode"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-form-item label=" ">
                  <el-button type="primary" @click="doSearch">查询</el-button>
                  <el-button @click="doReset">重置</el-button>
                </el-form-item>
              </el-row>
            </el-form>
          </template>
          <template slot="list">
            <el-row class="row">
              <el-tabs v-model="tabName" @tab-click="handleTabClick">
                <el-tab-pane :label="getAllCount" name="first">
                </el-tab-pane>
                <el-tab-pane :label="getNoAudit" name="second">
                </el-tab-pane>
                <el-tab-pane :label="getAuditing" v-if="isOaActivityAny" name="auditing">
                </el-tab-pane>
                <el-tab-pane :label="getReject" v-if="isOaActivityAny" name="reject">
                </el-tab-pane>
                <el-tab-pane :label="getNoStart" name="third">
                </el-tab-pane>
                <el-tab-pane :label="getDoing" name="forth">
                </el-tab-pane>
                <el-tab-pane :label="getEnd" name="five">
                </el-tab-pane>
              </el-tabs>
            </el-row>
            <FloatBlock :top="290" refClass="list-wrapper" v-if="tabName !== 'five'">
              <template slot="ctx">
                <el-row class="row">
                  <el-checkbox v-model="checkedAll" @change="checkedAllRow" style="margin-left: 15px"/>
                  <i18n k="/储值/会员储值/储值支付活动/列表页面/已选择{0}个活动">
                    <template slot="0">
                      {{ selected.length }}
                    </template>
                  </i18n>
                  <span style="margin-left: 30px"></span>
                  <el-button key="audit" @click="auditBatch"
                            v-if="(permission.auditable && permission.discountAuditable) && ['first', 'second'].indexOf(tabName) > -1 && !isOaActivityAny">批量审核
                  </el-button>
                  <el-button key="stop" @click="stopBatch" v-if="(permission.terminable && permission.discountTerminable) && ['first', 'third', 'forth'].indexOf(tabName) > -1">批量终止
                  </el-button>
                  <el-button key="del" type="danger" @click="delBatch" v-if="(permission.editable && permission.discountEditable) && ['first', 'second'].indexOf(tabName) > -1">批量删除
                  </el-button>
                </el-row>
              </template>
            </FloatBlock>
            <el-row class="row">
              <el-table
                      :data="queryData"
                      style="width: 100%;margin-top: 20px"
                      ref="table"
                      @selection-change="handleSelectionChange"
              >
                <el-table-column
                        v-if="tabName !== 'five'"
                        type="selection"
                        width="55"/>
                <el-table-column
                        fixed
                        :label="activityTitle"
                        width="250"
                >
                  <template slot-scope="scope">
                    <div class="div-flex">
                      <a no-i18n href="javascript: void(0)" :title="scope.row.body.activityId" @click="gotoDtl(scope.row)" style="color:var(--font-color-primary)">{{ scope.row.body.activityId }}</a>
                      <a no-i18n href="javascript: void(0)" @click="gotoDtl(scope.row)"
                        :title="scope.row.body.name">{{ scope.row.body.name }}</a>
                    </div>
                    
                  </template>
                </el-table-column>
                <el-table-column
                        label="所属主题"
                        prop="body.topicName"
                        width="150"
                >
                  <template slot-scope="scope">
                    <span no-i18n>{{ scope.row.body.topicName|nullable }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                        label="活动时间"
                        width="200"
                >
                  <template slot-scope="scope">
                    <span no-i18n>{{ activityTime(scope.row) }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                        label="状态"
                        width="150"
                >
                  <template slot-scope="scope">
                    <ActivityStateTag :stateEquals="scope.row.body.state"></ActivityStateTag>
                  </template>
                </el-table-column>
                <el-table-column
                        label="活动类型"
                        :width="activityTypeWidth"
                        prop="type"
                >
                  <template slot-scope="scope">
                    <span>{{ i18n(scope.row.type) }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                        :min-width="activityOptWidth"
                        label="操作"
                >
                  <template slot-scope="scope">
                    <!-- 折扣 -->
                    <div v-if="scope.row.body.type === 'MEMBER_BALANCE_DISCOUNT'">
                      <el-button key="audit" type="text" @click="audit(scope.row.body.activityId)" class="opt-btn"
                              v-if="permission.discountAuditable && scope.row.body.state === 'INITAIL' && !isOaActivityDiscount">审核
                      </el-button>
                      <el-button key="copy" type="text" v-if="permission.discountEditable" @click="copy(scope.row.body.activityId,scope.row.body.type)" class="opt-btn">复制
                      </el-button>
                      <el-button key="stop" type="text" v-if="permission.discountTerminable && ['UNSTART', 'PROCESSING'].indexOf(scope.row.body.state) > -1"
                                class="opt-btn"
                                @click="stop(scope.row.body.activityId)">终止
                      </el-button>
                      <el-button key="modify" type="text" v-if="permission.discountEditable && ['INITAIL','REJECTED','UNSTART', 'PROCESSING'].indexOf(scope.row.body.state) > -1" class="opt-btn"
                                @click="edit(scope.row.body.activityId,scope.row.body.type)">修改
                      </el-button>
                      <el-button key="del" type="text" v-if="permission.discountEditable && ['INITAIL','REJECTED'].indexOf(scope.row.body.state) > -1" class="opt-btn"
                                @click="del(scope.row.body.activityId)">删除
                      </el-button>
                    </div>
                    <!-- 立减 -->
                    <div v-else>
                      <el-button key="audit" type="text" @click="audit(scope.row.body.activityId)" class="opt-btn"
                            v-if="permission.auditable && scope.row.body.state === 'INITAIL' && !isOaActivityBalance">审核
                      </el-button>
                      <el-button key="copy" type="text" v-if="permission.editable" @click="copy(scope.row.body.activityId,scope.row.body.type)" class="opt-btn">复制
                      </el-button>
                      <el-button key="stop" type="text" v-if="permission.terminable && ['UNSTART', 'PROCESSING'].indexOf(scope.row.body.state) > -1"
                                class="opt-btn"
                                @click="stop(scope.row.body.activityId)">终止
                      </el-button>
                      <el-button key="modify" type="text" v-if="permission.editable && ['INITAIL','REJECTED','UNSTART', 'PROCESSING'].indexOf(scope.row.body.state) > -1" class="opt-btn"
                                @click="edit(scope.row.body.activityId,scope.row.body.type)">修改
                      </el-button>
                      <el-button key="del" type="text" v-if="permission.editable && ['INITAIL','REJECTED'].indexOf(scope.row.body.state) > -1" class="opt-btn"
                                @click="del(scope.row.body.activityId)">删除
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </el-row>
          </template>
          <template slot="page">
            <el-pagination
                    no-i18n
                    :current-page="page.currentPage"
                    :page-size="page.size"
                    :page-sizes="[10, 20, 30, 40]"
                    :total="page.total"
                    @current-change="onHandleCurrentChange"
                    @size-change="onHandleSizeChange"
                    background
                    layout="total, prev, pager, next, sizes,  jumper"
                    class="pagin"
            >
            </el-pagination>
          </template>
        </ListWrapper>
      </div>
    </div>

  </div>
</template>

<script lang="ts" src="./MemberBalancePromotionList.ts">
</script>

<style lang="scss">
  .member-balance-promotion-list {
    background-color: white;
    height: 100%;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .total {
      margin: 20px;
    }

    .current-page {
      height: 100%;
      overflow: auto;
      display: flex;
      flex-direction: column;
      .opt-btn {
        margin-left: 0 !important;
        margin-right: 15px !important;
      }

      .create-activity-block {
        background-color: white;

        .btn-item {
          width: 95%;
          background-color: #F9F9F9;
          padding: 20px;

          .btn-item-title {
            font-weight: 600;
          }

          .btn-item-small-title {
            color: #797979;
          }
        }
      }

      .el-form-item {
        margin-bottom: 0;
      }

      .el-select {
        width: 100%;
      }

      .query {
        padding: 20px 20px 0 20px;
      }

      .row {
        padding: 0 20px;

        .state {
          width: 7px;
          height: 7px;
          border-radius: 10px;
          float: left;
          margin-top: 7px;
          margin-right: 5px;
        }
      }

      .list {
        height: calc(100% - 150px);
        overflow: hidden;

        .el-col {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }

      .cell {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .el-form-item__label {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .pagin {
        padding: 20px;
      }
    }
    .div-flex{
      display: flex;
      flex-direction: column
    }
  }
</style>
