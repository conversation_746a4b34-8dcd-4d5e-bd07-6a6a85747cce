/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-07-18 16:20:56
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\v2\controller\points\topic\ActivityTopicApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import ActivityTopic from 'model/v2/controller/points/topic/ActivityTopic'
import Response from 'model/common/Response'
import TopicFilter from 'model/datum/topic/TopicFilter'
import Topic from 'model/datum/topic/Topic'

export default class ActivityTopicApi {
  /**
   * 查询活动主题列表 
   *
   */
  static listTopic(): Promise<Response<ActivityTopic[]>> {
    let body = new TopicFilter()
    body.page = 0
    body.pageSize = 0
    body.marketingCenterEquals = sessionStorage.getItem('marketCenter')
    return ApiClient.server().post(`/v1/topic/query`, body).then((res) => {
      let resp = res
      let data: ActivityTopic[] = []
      res.data.data.forEach((item: Topic)=>{
        let a = new ActivityTopic()
        a.code = item.topic.id
        a.name = item.topic.name
        data.push(a)
      })
      resp.data.data = data
      return resp.data
    })
  }

}
