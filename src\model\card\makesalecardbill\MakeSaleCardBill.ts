export default class MakeSaleCardBill {
  // 单号
  billNumber: Nullable<string> = null
  // 卡模板号
  cardTemplateNumber: Nullable<string> = null
  // 卡模板名称
  cardTemplateName: Nullable<string> = null
  // 起始卡号
  startCardCode: Nullable<string> = null
  // 结束卡号
  endCardCode: Nullable<string> = null
  // 卡次数
  cardCount: Nullable<number> = null
  // 卡面额
  cardFaceAmount: Nullable<number> = null
  // 卡售价
  cardPrice: Nullable<number> = null
  // 制售数量
  makeSaleQty: Nullable<number> = null
  // 总售价
  total: Nullable<number> = null
  // 营销中心
  marketingCenter: Nullable<string> = null
  // 状态INITIAL：未审核；AUDITED：已审核
  state: Nullable<string> = null
  // 备注
  remark: Nullable<string> = null
  // 创建时间
  created: Nullable<Date> = null
  // 创建人
  creator: Nullable<string> = null
  // 最后修改时间
  lastModified: Nullable<Date> = null
  // 最后修改人
  lastModifier: Nullable<string> = null
  /**
   * 卡类型
   * 用于跳转卡查询页面使用
   */
  cardType: Nullable<string> = null
}