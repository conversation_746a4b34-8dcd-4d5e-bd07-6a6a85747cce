import PageRequest from 'model/default/PageRequest'
import Channel from 'model/common/Channel';
import { TagTypeEnum } from "model/common/TagTypeEnum";

// 标签查询
export default class BTagFilter extends PageRequest {
  // 渠道
  channel: Nullable<Channel> = null
  /**
   * 标签类型  singleChoice("单选"),
   *   checkbox("多选"),
   *   date("日期"),
   *   text("文本"),
   *   number("数值");
   */
  tagType: Nullable<TagTypeEnum> = null
  //
  tagName: Nullable<string> = null
}
