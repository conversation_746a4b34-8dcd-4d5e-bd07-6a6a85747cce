<!--
 * @Author: 黎钰龙
 * @Date: 2025-04-16 18:51:54
 * @LastEditTime: 2025-04-17 11:39:33
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\mbrdeposit\adjust\dialog\RejectDialog.vue
 * 记得注释
-->
<template>
  <el-dialog :title="i18n('/券/延期申请/驳回')" :visible.sync="visible" width="30%" :close-on-click-modal="false" :show-close="false" :close-on-press-escape="false">
    <el-form :model="form" :rules="rules" label-width="120px" ref="form">
      <el-form-item :label="i18n('/券/延期申请/驳回原因')" prop="remark">
        <el-input v-model="form.remark" maxlength="200" type="textarea" :rows="4"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="doCancel">{{ i18n("取消") }}</el-button>
      <el-button type="primary" @click="submit">{{ i18n("确定") }}</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import I18nPage from "common/I18nDecorator";
import { Component, Vue } from "vue-property-decorator";

@Component({
  name: "RejectDialog",
})
@I18nPage({
  prefix: ["/公用/券模板", "/公用/按钮"],
  auto: true,
})
export default class RejectDialog extends Vue {
  $refs: any;
  visible = false;
  form = {
    remark: "",
  };
  rules = {
    remark: [{ required: true, message: this.i18n("请填写必填项") }],
  };

  submit() {
    this.$refs.form.validate().then(() => {
      this.$emit("submit", this.form.remark);
      this.visible = false;
      this.form.remark = "";
    });
  }

  doCancel() {
    this.visible = false;
    this.form.remark = "";
    this.$refs.form.clearValidate();
    this.$emit("cancel");
  }
}
</script>

<style>
</style>