<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:02
 * @LastEditTime: 2023-07-05 16:55:44
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\in-mem-page\InMemPage.vue
 * 记得注释
-->
<template>
  <div class="in-mem-page">
    <div style="height: calc(100% - 30px);overflow-y: auto">
      <slot name="filter" :filter="filter" :search="doSearch" :reset="doReset"></slot>
      <slot name="data" :data="currentPage"></slot>
    </div>
    <el-pagination
        small
        :current-page="page.currentPage"
        :page-size="page.size"
        :page-sizes="[10, 20, 30, 40]"
        :total="page.total"
        @current-change="onHandleCurrentChange"
        :background="isShowTotal"
        :layout="isShowTotal ? 'total, sizes, prev, pager, next' : 'prev, pager, next'"
        :class="page.total > page.size ? '': 'single'"
    >
    </el-pagination>
  </div>
</template>

<script lang="ts" src="./InMemPage.ts">
</script>

<style lang="scss" scoped>
  .in-mem-page {
    position: relative;
    height: 100%;
    overflow: hidden;
    ::v-deep .el-pagination__sizes {
      display: none;
    }

    .single {
      ::v-deep .btn-prev {
        display: none;
      }

      ::v-deep .el-pager {
        display: none;
      }

      ::v-deep .btn-next {
        display: none;
      }

      ::v-deep .el-pagination__jump {
        display: none;
      }
    }
  }
</style>
