import AliCouponInfo from './AliCouponInfo'
import { ExpireRefundType } from './ExpireRefundType'
import { RefundAlgorithm } from './RefundAlgorithm'

export default class AliMerchantCouponInfo {
  // 是否允许用户申请退款
  enableUserApplyRefund: Nullable<boolean> = false
  // 是否允许延期
  enableApplyDelay: Nullable<boolean> = false
  // 价格
  price: Nullable<number> = null
  // 使用须知
  notice: Nullable<string> = null
  // 服务商id
  isvAppId: Nullable<string> = null
  // 券模板号和券批次号映射表
  aliCouponInfos: AliCouponInfo[] = []
  // 券包图片
  image: Nullable<string> = null
}