import IdName from "model/common/IdName";
import CouponSuperposition from "model/coupon/CouponSuperposition/CouponSuperposition";
import { SuperpositionLevel } from "model/coupon/CouponSuperposition/SuperpositionLevel";
import { SuperpositionType } from "model/coupon/CouponSuperposition/SuperpositionType";

export default class GroupMutexTemplateData {
  constructor(defaultVal?: Boolean) {
    if (defaultVal) {
      if (
        JSON.parse(localStorage.getItem("sysConfig") as string)
          .enableActivityExclusion
      ) {
        this.couponSuperposition!.superpositionLevel = SuperpositionLevel.TRADE;
        this.couponSuperposition!.superpositionType = "NONREUSEABLE";
      } else {
        this.couponSuperposition!.groupMutexTemplates = [];
        this.couponSuperposition!.nonSuperpositionTypeValue = null;
        this.couponSuperposition!.superpositionLevel = SuperpositionLevel.TRADE;
        this.couponSuperposition!.superpositionType = "REUSEABLE";
        this.couponSuperposition!.superpositionTypeValue = [
          SuperpositionType.CURRENT_COUPON,
          SuperpositionType.OTHER_COUPON,
        ];
      }
    }
  }
  // 更新数据
  firstInit = true;
  // 模板id
  templateId: Nullable<string> = null;
  // 券名称
  name: Nullable<string> = null;
  // 不可与以下优惠券叠加，同一单品只可按最大优惠力度使用其中一种券
  groupMutexFlag: Nullable<boolean> = null;
  // 商品限制叠加模板信息
  groupMutexTemplates: IdName[] = [];
  // 6.29新叠加用券参数
  couponSuperposition: Nullable<
    CouponSuperposition
  > = new CouponSuperposition();
}
