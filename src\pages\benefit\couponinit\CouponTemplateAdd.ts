import { Component, Vue } from 'vue-property-decorator'
import CouponTemplateWrap from 'cmp/coupontenplate/CouponTemplateWrap.vue'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import BreadCrume from 'cmp/bread-crumb/BreadCrume'
import CouponTemplateApi from 'http/coupon/template/CouponTemplateApi'
import CouponInfo from 'model/common/CouponInfo'
import CouponItem from 'model/common/CouponItem'
import CouponTemplateDataDict from "pages/benefit/couponinit/CouponTemplateDataDict";
import I18nTool from "common/I18nTool";
import { LocalStorage } from 'mgr/BrowserMgr'
import I18nPage from 'common/I18nDecorator'
import ConstantMgr from 'mgr/ConstantMgr'
import CouponTemplateOuterRelation from "model/coupon/template/CouponTemplateOuterRelation";
import Channel from "model/common/Channel";
import RSChannelManagement from "model/common/RSChannelManagement";
import CouponTemplate from "model/coupon/template/CouponTemplate";
import MemberBlacklistApi from "http/promotion/MemberBlacklistApi";

@Component({
  name: 'CouponTemplateAdd',
  components: {
    CouponTemplateWrap,
    SubHeader,
    BreadCrume
  }
})

@I18nPage({
  prefix: [
    '/公用/券模板'
  ],
})
export default class CouponTemplateAdd extends Vue {

  static setDefaultName(params: CouponInfo) {
    let defaultNameDict: any = CouponTemplateDataDict.getDefaultNameDict()
    let handleAmount = (amount: number) => {
      amount = Number(amount)
      if (Number.isInteger(amount)) {
        return amount.toFixed(0)
      }
      return amount + ''
    }
    if (['all_cash', 'freight', 'goods_cash'].indexOf(params.couponBasicType + '') > -1) {
      if (!params.name) {
        if (params.cashCouponAttribute && params.cashCouponAttribute.faceAmount) {
          params.name = I18nTool.match(defaultNameDict[params.couponBasicType as string], [],
            [handleAmount(params.cashCouponAttribute.faceAmount)])
        }
      }
    }
    if (['all_discount', 'rfm_type', 'goods_discount'].indexOf(params.couponBasicType + '') > -1) {
      if (!params.name) {
        if (params.discountCouponAttribute && params.discountCouponAttribute.discount !== null) {
          params.name = I18nTool.match(defaultNameDict[params.couponBasicType as string], [],
            [handleAmount(params.discountCouponAttribute.discount!)])
        }
      }
    }
    if (['random_cash'].indexOf(params.couponBasicType + '') > -1) {
      if (!params.name) {
        params.name = '随机金额现金券'
      }
    }
    if (['exchange_goods'].indexOf(params.couponBasicType + '') > -1) {
      if (!params.name) {
        params.name = '兑换券'
      }
    }
    if (['special_price'].indexOf(params.couponBasicType + '') > -1) {
      if (!params.name) {
        params.name = '特价券'
      }
    }
    if (['equity'].indexOf(params.couponBasicType + '') > -1) {
      if (!params.name) {
        params.name = '权益券'
      }
    }
    // todo 提货券
  }

  copyFlag = 'add'
  baseFieldEditable = false
  $refs: any
  panelArray: any = [
    {
      name: '',
      url: 'coupon-template-list'
    },
    {
      name: '',
      url: ''
    }
  ]
  coupon: CouponItem = new CouponItem()
  dtl: CouponInfo = new CouponInfo()
  types: string[] = ['all_cash', 'goods_cash', 'all_discount', 'rfm_type', 'goods_discount', 'goods', 'freight', 'exchange_goods', 'special_price']
  auditPermission: boolean = false  //是否具有审核权限
  couponTypeArray: any = [] //选择券模板类型
  currentStep: 1 | 2 | 3 = 1  //当前创建进度
  currentCouponType: Nullable<string> = null  //当前选择新建的券类型
  currentCouponTemplateNumber: Nullable<string> = null
  couponState: Nullable<string> = null //新建券模板完成后，当前券模板的状态

  get currentCouponName() {
    let str = this.i18n('欢迎使用创建券模板功能')
    return str
  }

  get currentCouponLabel() {
    return this.couponTypeArray.find((item: any) => item.couponType === this.currentCouponType)?.label
  }

  get barNavTest() {
    let str = ''
    str += this.i18n('欢迎使用{0}{1}')
    str = str.replace(/\{0\}/g, this.$route.query.from === 'edit' ? this.i18n('修改') : this.i18n('新建'))
    str = str.replace(/\{1\}/g, (!this.currentCouponLabel || this.currentStep === 1) ? this.i18n('券模板功能') : '')
    return str
  }

  //建券成功后，根据不同的券类型，展示不同的标签
  get couponFaceInfo() {
    let str = ''
    if (this.currentCouponType === 'all_cash') {
      str = this.coupon.coupons?.cashCouponAttribute?.faceAmount as any + this.i18n('元')
    } else if (this.currentCouponType === 'all_discount') {
      str = this.coupon.coupons?.discountCouponAttribute?.discount as any + this.i18n('折')
    } else if (this.currentCouponType === 'special_price') {
      str = this.i18n('特价金额') + this.coupon.coupons?.specialPriceCouponAttribute?.specialPrice + this.i18n('元')
    } else if (this.currentCouponType === 'freight') {
      str = this.coupon.coupons?.cashCouponAttribute?.faceAmount + this.i18n('元')
    } else if (this.currentCouponType === 'random_cash') {
      str = this.coupon.coupons?.randomCouponAttribute?.minFaceAmount + '—' + this.coupon.coupons?.randomCouponAttribute?.maxFaceAmount + this.i18n('元')
    } else {
      str = ''
    }
    return str
  }

  get couponFaceState() {
    let obj = {
      str: '',
      class: ''
    }
    if (this.couponState === 'EFFECTED') {
      obj.str = this.i18n('/权益/券/券模板/已生效')
      obj.class = 'label-success'
    } else if (this.couponState === 'NOT_EFFECTED') {
      obj.str = this.i18n('/权益/券/券模板/未生效')
      obj.class = 'label-warning'
    } else if (this.couponState === 'CANCELLED') {
      obj.str = this.i18n('/营销/券礼包活动/券查询/券状态下拉选项/已作废')
      obj.class = 'label-cancel'
    } else if (this.couponState === 'INITIAL') {
      obj.str = this.i18n('/营销/券礼包活动/券礼包活动/未审核')
      obj.class = 'label-warning'
    } else if (this.couponState === 'EXPIRED') {
      obj.str = this.i18n('/储值/预付卡/预付卡查询/列表页面/已过期')
      obj.class = 'label-cancel'
    }
    return obj
  }

  created() {
    this.panelArray[0].name = this.formatI18n('/权益/券/券模板/券模板')
    if (this.$route.query.from === 'edit') {
      this.copyFlag = 'edit'
      this.panelArray[1].name = this.formatI18n('/权益/券/券模板/编辑界面/面包屑/修改券模板')
      this.currentStep = 2
      this.getDtl()
    } else if (this.$route.query.from === 'copy') {
      this.copyFlag = 'copy'
      this.panelArray[1].name = this.formatI18n('/权益/券/券模板/新建券模板')
      this.currentStep = 2
      this.getDtl()
    } else {
      this.copyFlag = 'add'
      this.panelArray[1].name = this.formatI18n('/权益/券/券模板/新建券模板')
    }
    if (LocalStorage.getItem('sysConfig') && LocalStorage.getItem('sysConfig').enableRandomCashCouponDisplay) {
      this.types.push('random_cash')
    }
    if (LocalStorage.getItem('sysConfig') && LocalStorage.getItem('sysConfig').enablePointsExchangeCouponDisplay) {
      this.types.push('points')
    }
    if (LocalStorage.getItem('sysConfig') && LocalStorage.getItem('sysConfig').enableEquityCouponDisplay) {
      this.types.push('equity')
    }
    this.getAuditConfig()
    this.initCouponType()
    this.getOuterTemplateCode()
  }

  save() {
    if (this.dtl && this.dtl.state === 'EFFECTED') {
      if (this.auditPermission) {  //开启审核权限的话，保存已生效券模板，调用saveOrModify
        this.doSaveWithCheckOuterRelations(false, 'save')
      } else {
        this.doSaveWithCheckOuterRelations(true, 'save')
      }
    } else {
      this.doSaveWithCheckOuterRelations(false, 'save')
    }
  }

  saveEffect() {
    this.doSaveWithCheckOuterRelations(true, 'saveEffect')
  }

  doSaveWithCheckOuterRelations(effective: boolean = false, from: string) {
    if (this.coupon.coupons?.outerRelations?.length) {
      if (this.$route.query.from === 'copy') {
        this.coupon.coupons.templateId = ''
      }
      this.coupon.coupons.outerRelations.forEach((sub: CouponTemplateOuterRelation) => {
        if (sub.channel) {
          let numbers: string[] = [];
          CouponTemplateApi.queryRelation(sub.channel.id as string, sub.outerNumber as string, sub.channel.type as string).then((resp: any) => {
            if (resp && resp.code === 2000) {
              numbers = [...resp.data];
            }

            let conflictNumbers: string[] = [];
            numbers.forEach((number: string) => {
              if (number && this.coupon.coupons && this.coupon.coupons.templateId && number != this.coupon.coupons.templateId) {
                conflictNumbers.push(number);
              } else if (number && this.coupon.coupons && (this.coupon.coupons.templateId === null || this.coupon.coupons.templateId === '')) {
                conflictNumbers.push(number);
              }
            })
            if (conflictNumbers.length) {
              let conflictNumbersStr: string = '';
              conflictNumbersStr = conflictNumbers.join(",")
              this.$confirm(this.i18n('该外部券模板号已被{0}券模板引用，保存此券模板则会作废原券模板，并清空原券模板里的外部券模板号', [conflictNumbersStr]), '', {
                confirmButtonText: this.formatI18n('/权益/券/券模板/确定'),
                cancelButtonText: this.formatI18n('/权益/券/券模板/取消')
              }).then(() => {
                this.doSave(effective, from)
              })
              return;
            } else {
              this.doSave(effective, from)
            }
          }).catch((error: any) => {
            if (error && error.message) {
              this.$message.error(error.message);
            }
          });
        }
      })

    } else {
      this.doSave(effective, from)
    }
  }

  doSave(effective: boolean = false, from: string) {
    Promise.all([this.$refs.couponTemplate.doValidate()]).then((resp: any) => {
      let params: CouponInfo = this.coupon.coupons as CouponInfo
      console.log(params)
      if (params.couponBasicType === 'exchange_goods') {
        params.useGoods = null
      }
      CouponTemplateAdd.setDefaultName(params)
      if (this.$route.query.from === 'copy') {
        params.templateId = ''
      }
      if (this.$route.query.from === 'edit') {
        params.state = this.dtl.state
        params.outerNumberId = this.dtl.outerNumberId
        params.outerNumberNamespace = this.dtl.outerNumberNamespace
      }
      if (params.rexCoupon) {
        // 把null 和空字符串 转换为 0，其他数字还是数字
        params.rexCoupon.netWorth = Number(params.rexCoupon.netWorth)
      }
      if (this.auditPermission && from === 'save' && this.dtl.state === 'EFFECTED') { //如果拥有审核权限，并且“已生效”的券模板点击“保存”，参数就传“未审核”
        params.state = 'INITIAL'
      }
      let method = effective ? CouponTemplateApi.saveOrEffect : CouponTemplateApi.saveOrModify
      method(params).then((resp: any) => {
        if (resp && resp.code === 2000) {
          if (params.templateId) {
            this.$message.success(this.formatI18n('/会员/会员资料', '编辑成功'))
          } else {
            this.$message.success(this.formatI18n('/公用/js提示信息', '保存成功'))
          }
          this.currentStep = 3
          this.currentCouponTemplateNumber = resp.data
          this.queryCurrentDtl()
        } else {
          this.$message.error(resp.msg)
        }
      }).catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    })
  }

  //保存并审核
  saveAudit() {
    this.doSaveWithCheckOuterRelations(true, 'saveAudit')
  }

  changeCouponType(couponType: string) {
    this.currentCouponType = couponType
  }

  doCancel() {
    this.$router.back()
  }

  private getDtl() {
    CouponTemplateApi.detail(this.$route.query.id as string, false).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.dtl = resp.data
        let couponItem: CouponItem = new CouponItem()
        couponItem.coupons = this.dtl
        this.coupon = couponItem
        this.baseFieldEditable = true //PHX-8530 支持修改 叠加促销、用券记录方式
        this.currentCouponType = this.coupon.coupons?.couponBasicType
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private queryCurrentDtl() {
    const loading = this.$loading(ConstantMgr.loadingOption)
    CouponTemplateApi.detail(this.currentCouponTemplateNumber as string, false).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.couponState = resp.data.state
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    }).finally(() => {
      loading.close()
    })
  }

  //获取是否具有审核权限配置
  getAuditConfig() {
    CouponTemplateApi.getEnabledAuditTemplateConfig().then(res => {
      if (res.code === 2000) {
        this.auditPermission = res.data || false
      } else {
        this.$message.error(res.msg || this.i18n('获取审核权限失败'))
      }
    }).catch((err) => {
      this.$message.error(err.message)
    }).finally(() => { })
  }

  //券模板表单页是否展示“外部券模板号”字段
  async getOuterTemplateCode() {
    let flag = 'false'
    try {
      const { data } = await CouponTemplateApi.getShowOuterRelationsConfig()
      flag = data ? 'true' : 'false'
    } catch {}
    sessionStorage.setItem('showOuterRelationsConfig', flag)
  }

  doCreateCouponTemplate(item: any) {
    this.currentCouponType = item.couponType
    this.currentStep = 2
  }

  backToFirstStep() {
    this.currentStep = 1
    this.coupon = new CouponItem()
    this.$router.replace({
      name: 'coupon-template-add'
    })
    this.$route.query.id = ''
    this.$route.query.from = ''
    this.copyFlag = 'add'
  }

  gotoDtl() {
    this.$router.push({ name: 'coupon-template-dtl', query: { id: this.currentCouponTemplateNumber } })
  }

  backToList() {
    this.$router.push({ name: 'coupon-template-list' })
  }

  private initCouponType() {
    this.couponTypeArray = [
      {
        couponType: 'all_cash',
        label: this.i18n('现金券'),
        desc: this.i18n('消费用券抵扣现金'),
        imgUrl: require("assets/image/coupon/img_coupon_template_cash.png")
      },
      {
        couponType: 'all_discount',
        label: this.i18n('折扣券'),
        desc: this.i18n('消费用券享受折扣'),
        imgUrl: require("assets/image/coupon/img_coupon_template_discount.png")
      },
      {
        couponType: 'exchange_goods',
        label: this.i18n('兑换券'),
        desc: this.i18n('用券兑换商品'),
        imgUrl: require("assets/image/coupon/img_coupon_template_exchange.png")
      },
      {
        couponType: 'special_price',
        label: this.i18n('特价券'),
        desc: this.i18n('用券享受特价购买指定商品'),
        imgUrl: require("assets/image/coupon/img_coupon_template_bargain.png")
      },
      {
        couponType: 'goods',
        label: this.i18n('提货券'),
        desc: this.i18n('用券到店提货'),
        imgUrl: require("assets/image/coupon/img_coupon_template_take_delivery.png")
      },
      {
        couponType: 'freight',
        label: this.i18n('运费券'),
        desc: this.i18n('用券抵扣运费'),
        imgUrl: require("assets/image/coupon/img_coupon_template_freight.png")
      },
      {
        couponType: 'random_cash',
        label: this.i18n('随机金额券'),
        desc: this.i18n('发券时随机生成券面额，消费时用券抵扣现金券'),
        imgUrl: require("assets/image/coupon/img_coupon_template_random_amount.png"),
        shouldHide: !(LocalStorage.getItem("sysConfig") && LocalStorage.getItem("sysConfig").enableRandomCashCouponDisplay)
      },
      {
        couponType: 'points',
        label: this.i18n('积分券'),
        desc: this.i18n('核券兑换一定积分'),
        imgUrl: require("assets/image/coupon/img_coupon_template_accumulate_points.png"),
        shouldHide: !(LocalStorage.getItem("sysConfig") && LocalStorage.getItem("sysConfig").enablePointsExchangeCouponDisplay)
      },
      {
        couponType: 'equity',
        label: this.i18n('权益券'),
        desc: this.i18n('购买商品资格券'),
        imgUrl: require("assets/image/coupon/img_coupon_template_quity_coupons.png"),
        shouldHide: !(LocalStorage.getItem("sysConfig") && LocalStorage.getItem("sysConfig").enableEquityCouponDisplay)
      },
    ]
  }
}