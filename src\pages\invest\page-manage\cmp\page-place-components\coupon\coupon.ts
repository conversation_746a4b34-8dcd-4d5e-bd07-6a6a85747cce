/*
 * @Author: haiding <EMAIL>
 * @Date: 2025-02-19 10:03:52
 * @LastEditors: haiding <EMAIL>
 * @LastEditTime: 2025-03-13 11:33:09
 * @FilePath: \new-kequn\src\pages\invest\page-manage\cmp\page-place-components\coupon\coupon.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Vue, Component, Prop } from 'vue-property-decorator';
import PlaceTemplateMixins from '../PlaceTemplateMixins';
import I18nPage from 'common/I18nDecorator';

@Component({
  name: 'Coupon',
  components: {},
  mixins: [PlaceTemplateMixins],
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理',
    '/页面/导航设置'
  ],
  auto: true
})
export default class Coupon extends Vue {
  @Prop()
  componentItem: any;
  mounted() {
    console.log(this.componentItem, 'componentItem');
  }
  get bgUrl() {
    return   'delivery/home_page_top_new.png';
  }
  get localProperty() {
    return this.componentItem.props;
  }
  // toolbarClick(e) {
  //   this.$emit('toolBarClick', {
  //     clickName: e,
  //     activeIndex: this.activeIndex,
  //   });
  // }
}
