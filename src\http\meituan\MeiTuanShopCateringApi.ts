import ApiClient from 'http/ApiClient'
import MeiTuanShopCateringAuthRequest from 'model/meituanshopcatering/MeiTuanShopCateringAuthRequest'
import Response from 'model/default/Response'

export default class MeiTuanShopCateringApi {
  /**
   * 获取美团餐饮门店映射链接
   * 获取美团餐饮门店映射链接。
   * 
   */
  static getAuthUrl(body: MeiTuanShopCateringAuthRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/meituan/shop/catering/getAuthUrl`, body, {
    }).then((res) => {
      return res.data
    })
  }


  static getUnBindAuthUrl(body: MeiTuanShopCateringAuthRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/meituan/shop/catering/getUnBindAuthUrl`, body, {
    }).then((res) => {
      return res.data
    })
  }
}
