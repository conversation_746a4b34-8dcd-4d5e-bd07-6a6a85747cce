import BCardEquity from 'model/equityCard/BCardEquity'
import BEquityCardExpiryRule from 'model/equityCard/BEquityCardExpiryRule'
import Channel from 'model/common/Channel'
import GiftBag from 'model/grade/upgradegift/GiftBag'

// 权益卡模板名称
export default class BEquityCardTemplate {
  // 卡模板号
  number: Nullable<string> = null
  // 卡模板名称
  name: Nullable<string> = null
  // 卡号前缀，为null或者空串时忽略
  codePrefix: Nullable<string> = null
  // 使用须知
  remark: Nullable<string> = null
  // 营销中心
  marketingCenter: Nullable<string> = null
  // 领卡渠道
  channels: Channel[] = []
  // 领卡奖励
  giftBag: Nullable<GiftBag> = null
  // 卡权益
  equity: Nullable<BCardEquity> = null
  // 有效期
  expiryRules: BEquityCardExpiryRule[] = []
  // 卡模板状态：stop : 停用 ,start : 启用
  state: Nullable<string> = null
}