<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>H5登录注册页面测试 - 更新版</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .pageBox {
            width: 350px;
            height: 600px;
            background: #f0f2f6;
            border: 8px solid #000000;
            border-radius: 50px;
            box-sizing: border-box;
            overflow: hidden;
            margin: 0 auto;
        }
        .header-bar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            height: 74px;
            font-size: 18px;
            font-weight: 600;
            color: #262626;
        }
        .back-btn {
            font-size: 20px;
            cursor: pointer;
            width: 30px;
        }
        .header-title {
            flex: 1;
            text-align: center;
        }
        .header-right {
            width: 30px;
        }
        .login-form-container {
            width: 280px;
            margin: 20px auto 0;
            padding: 40px 30px 30px;
            background-color: #ffffff;
            border-radius: 20px;
        }
        .login-type-tabs {
            display: flex;
            margin-bottom: 40px;
            background-color: #f8f8f8;
            border-radius: 8px;
            padding: 4px;
        }
        .tab-item {
            flex: 1;
            text-align: center;
            padding: 12px 0;
            font-size: 16px;
            color: #666;
            cursor: pointer;
            border-radius: 6px;
            transition: all 0.3s ease;
        }
        .tab-item.active {
            color: #ff4444;
            font-weight: 600;
            background-color: #ffffff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .input-group {
            margin-bottom: 40px;
        }
        .country-selector {
            margin-bottom: 20px;
        }
        .country-select {
            width: 100%;
            height: 48px;
            padding: 0 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            outline: none;
            box-sizing: border-box;
            background-color: #ffffff;
            color: #333;
        }
        .country-select:focus {
            border-color: #ff4444;
        }
        .input-item {
            position: relative;
            margin-bottom: 20px;
        }
        .login-input {
            width: 100%;
            height: 48px;
            padding: 0 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            outline: none;
            box-sizing: border-box;
        }
        .login-input:focus {
            border-color: #ff4444;
        }
        .login-btn {
            width: 100%;
            height: 48px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-bottom: 30px;
            background-color: #000000;
            color: #FFFFFF;
            transition: all 0.3s ease;
        }
        .login-btn:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }
        .login-btn:active {
            transform: translateY(0);
        }
        .description {
            font-size: 12px;
            color: #999;
            line-height: 1.5;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>H5登录注册页面预览 - 根据设计图优化</h1>
        <div class="pageBox">
            <div class="header-bar">
                <div class="back-btn">←</div>
                <div class="header-title">登录/注册</div>
                <div class="header-right"></div>
            </div>
            
            <div class="login-form-container">
                <div class="login-type-tabs">
                    <div class="tab-item active" onclick="switchTab('mobile')">
                        手机号
                    </div>
                    <div class="tab-item" onclick="switchTab('email')">
                        邮箱
                    </div>
                </div>
                
                <div class="input-group">
                    <div class="country-selector" id="countrySelector">
                        <select class="country-select">
                            <option value="+886">+886 Taiwan, China</option>
                            <option value="+86">+86 中国大陆</option>
                            <option value="+852">+852 香港</option>
                            <option value="+853">+853 澳门</option>
                            <option value="+1">+1 美国</option>
                        </select>
                    </div>
                    <div class="input-item">
                        <input type="text" id="loginInput" placeholder="请输入手机号码" class="login-input" />
                    </div>
                </div>
                
                <button class="login-btn">继续</button>
                
                <div class="description">
                    点击继续即表示您同意我们的<a href="#" style="color: #ff4444;">服务条款</a>和<a href="#" style="color: #ff4444;">隐私政策</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        function switchTab(type) {
            const tabs = document.querySelectorAll('.tab-item');
            const loginInput = document.getElementById('loginInput');
            const countrySelector = document.getElementById('countrySelector');
            
            tabs.forEach(tab => tab.classList.remove('active'));
            
            if (type === 'mobile') {
                tabs[0].classList.add('active');
                loginInput.placeholder = '请输入手机号码';
                countrySelector.style.display = 'block';
            } else {
                tabs[1].classList.add('active');
                loginInput.placeholder = '请输入邮箱地址';
                countrySelector.style.display = 'none';
            }
        }
    </script>
</body>
</html>
