/*
 * @Author: 黎钰龙
 * @Date: 2023-03-27 18:13:02
 * @LastEditTime: 2023-07-12 13:45:09
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\miniappsetup\aliappsetup\BWeixinAppletConfig.ts
 * 记得注释
 */
// 微信小程序配置信息
export default class BWeixinAppletConfig {
  // uuid
  uuid: Nullable<string> = null
  // 分享图
  shareImage: Nullable<string> = null
  // 是否展示付费等级入口
  showPaidGrade: Nullable<boolean> = false
  // 微信小程序会员权益列表  MEMBER_CODE会员码,POINTS积分,BALANCE储值,COUPON券,PREPAY_CARD礼品卡
  equities: string[] = []
  // 是否调起微信支付
  callWxPay: Nullable<boolean> = false
  //是否开启修改手机号配置
  showChangeMobile: Nullable<boolean> = false
  // 是否开启领券白名单
  issueCouponWhiteList: Nullable<boolean> = false
  // 会员属性   AVATAR头像,NICK_NAME昵称,MOBILE手机号,GRADE会员等级
  memberInfo: string[] = []
  //区分  首页"HOME"  我的"MINE" 模块
  type: string = 'HOME'
  //是否开启配置  "start" 开启  "stop" 不开启
  state: string = 'stop'
}