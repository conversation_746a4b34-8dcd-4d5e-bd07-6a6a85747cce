<template>
  <el-container class="main">
    <NavNew ref="navNew" @download="doDownload" @refresh="refresh"></NavNew>
    <el-container>
      <el-header class="navbar">
        <div class="top-left">
          <el-button v-if="$route.query.from !== 'login'" class="back-btn" @click="doBack"
            style="padding: 0;background: #F2f2f2;border: none;color: #5A5F66"><i
              class="iconfont ic-left"></i>&nbsp;&nbsp;{{ formatI18n('/营销/积分活动/积分活动/效果评估/返回') }}</el-button>
        </div>
        <div class="top-right">
          <!-- 版本号 -->
          <div class="nav-item">
            <span class="item-text">{{projectVersion}}</span>
          </div>
          <!-- 消息提醒 -->
          <div class="nav-item">
            <img src="~assets/image/home/<USER>" style="width: 24px;height: 24px">
            <span class="item-text" @click="doMessageOpen">{{formatI18n('/公用/系统/上导航/消息提醒')}}</span>
          </div>
          <!-- 语言切换 -->
          <span style="display: contents" v-if="languageFlag">
            <img src="~assets/image/home/<USER>" style="width: 17px;height: 17px">
            <el-select @change="doChangeLan" class="cur-sel" placeholder="" style="width: 100px;line-height: 32px" v-model="language">
              <el-option :label="item.value" :value="item.key" :key="item.key" v-for="item in languageNames">{{item.value}}</el-option>
            </el-select>
          </span>
          <!-- 营销中心 -->
          <div class="top-shop">
            <div class="shop-name">{{customer | strFormat}}</div>
          </div>
          <!-- 组织 -->
          <span style="display: contents" v-if="promotionCenter">
            <img src="~assets/image/home/<USER>" style="width: 17px;height: 17px">
            <el-select @change="doChangeOrg" class="cur-sel" placeholder="" style="width: 100px;line-height: 32px" v-model="org">
              <el-option :label="item.marketingCenter.name" :value="item.marketingCenter.id" :key="item.marketingCenter.id"
                v-for="item in orgList">{{item.marketingCenter.name}}
              </el-option>
            </el-select>
          </span>
          <div class="option">
            <el-popover v-model="popoverFlag" popper-class="cur-popper" placement="bottom" width="90" trigger="click">
              <div class="option-list">
                <div class="icon-name change-pwd" @click="openChangePwd">{{formatI18n('/公用/系统/上导航/密码修改')}}</div>
                <div class="icon-name logout" @click="onMenuClick">{{formatI18n('/公用/系统/上导航/退出')}}</div>
              </div>
              <div slot="reference" class="icon-name user-name">
                {{user.name | fmt}}
                <i v-if="popoverFlag" class="el-icon-arrow-up"></i>
                <i v-else class="el-icon-arrow-down"></i>
              </div>
            </el-popover>
          </div>
          <change-pwd :visible="showPwdDialog" @change="changeCall"></change-pwd>
        </div>
      </el-header>
      <el-main class="replace-view" id="replace-view">
        <router-view v-if="!refreshing">
        </router-view>
      </el-main>
    </el-container>
    <DownloadCenterDialog :dialogvisiable="dialogvisiable" @dialogClose="doDialogClose"></DownloadCenterDialog>
    <RenewMessageDialog ref="renewMessageDialog"></RenewMessageDialog>
  </el-container>
</template>
<script lang="ts" src="./Main.ts"></script>

<style lang="scss">
/*@import '~assets/styles/mixin.scss';*/
.main {
  height: 100%;
  .nav-wrapper {
    /*width: 200px;*/
    /*background-color: #1E2337;*/
    overflow: auto;
  }
  /*侧边栏*/
  .el-submenu .el-menu-item {
    color: white;
  }
  .el-menu-item:hover,
  .el-menu-item:focus {
    background-color: #354052;
  }
  .el-submenu__title:hover {
    background-color: #354052;
  }
  .el-submenu__title {
    color: white;
  }
  .el-menu {
    border: none;
    background-color: inherit;
    color: white;
    .nav-li-name {
      color: white;
    }
    .nav-li-group {
      background-color: rgb(30, 35, 55);
      color: white;
    }
  }
  .nav-title {
    width: 56px;
    height: 56px;
    text-align: center;
    line-height: 56px;
    /*color: white;*/
    background-color: white;
  }
  /* 头部导航*/
  .navbar {
    height: 48px !important;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;
    .collapse-btn {
      cursor: pointer;
    }
    .icon-name {
      font-size: 12px;
      color: #5a5f66;
      background-size: 24px;
      padding-left: 30px;
      background-position-x: left;
      background-position-y: center;
      background-repeat: no-repeat;
      cursor: pointer;
      &:hover {
        color: #007eff;
      }
    }
    .back-icon {
      width: 32px;
      height: 32px;
      background-size: 32px;
      margin-right: 8px;
      background-image: url(~assets/image/nav/ic_back_normal.svg);
      &:hover {
        background-image: url(~assets/image/nav/ic_back_hover.svg);
      }
    }
    .pre-disabled {
      width: 32px;
      height: 32px;
      background-size: 32px;
      margin-right: 8px;
      cursor: not-allowed;
      background-image: url(~assets/image/nav/ic_back_disable.svg);
    }
    .top-left {
      display: flex;
      align-items: center;
    }
    .top-right {
      display: flex;
      align-items: center;
      .nav-item {
        display: flex;
        align-items: center;
        margin-right: 24px;
        .item-text {
          margin-left:4px;
          line-height: 32px;
          cursor: pointer;
        }
      }
      .download-center {
        margin: 0 22px;
        background-image: url(~assets/image/nav/ic_download_normal.svg);
        &:hover {
          background-image: url(~assets/image/nav/ic_download_selected.svg);
        }
      }
      .user-name {
        max-width: 200px;
        //@include ellipsis();
        background-image: url(~assets/image/nav/ic_yonghuxingming_normal.svg);
      }
      .top-shop {
        margin: 0 22px;
        .shop-name {
          font-size: 14px;
          color: #5a5f66;
          background-size: 24px;
          padding-left: 30px;
          max-width: 250px;
          //@include ellipsis();
          background: url(~assets/image/nav/ic_shanghumingcheng_normal.svg) left center no-repeat;
        }
      }

      .top-org {
        margin: 0 22px;
      }

      .option {
        margin-left: 22px;
      }
      .option:hover {
        .user-name {
          font-size: 12px;
          color: #20a0ff;
          background-image: url(~assets/image/nav/ic_yonghuxingming_hover.svg);
        }
        .option-list {
          display: block;
        }
      }
    }
  }
  .replace-view {
    padding: 0 24px 24px 24px;
    display: flex;
  }
  .cur-sel {
    .el-input__inner {
      background-color: transparent;
      border: none;
    }
    .el-input__suffix {
      right: 15px;
      top: -1px;
    }
    .el-input--suffix .el-input__inner {
      padding-right: 0px;
      color: #5a5f66;
    }
  }
  .back-btn {
    font-size: 18px;
    &:hover {
      color: #007eff !important;
    }
  }
}
.option-list {
  margin: 0;
  .icon-name {
    height: 32px;
    line-height: 32px;
    background-position-x: 8px;
    padding-left: 33px;
    color: #5a5f66;
    font-size: 14px;
    cursor: pointer;
  }
  .change-pwd {
    background: url(~assets/image/nav/ic_password_normal.svg) 6px center white no-repeat;
    &:hover {
      color: #007eff;
      /*background-color: #EFF7FF;*/
      background: url(~assets/image/nav/ic_password_hover.svg) 6px center #eff7ff no-repeat;
    }
  }
  .logout {
    background: url(~assets/image/nav/ic_exit_normal.svg) 6px center white no-repeat;
    &:hover {
      color: #fc0049;
      background: url(~assets/image/nav/ic_exit_hover.svg) 6px center #fff1f5 no-repeat;
    }
  }
}
.cur-popper {
  padding: 12px 0 !important;
}
</style>
<style lang="scss" scoped>
::v-deep .el-select .el-input,
.el-select-dropdown .el-select-dropdown__item {
  font-size: 14px;
}
::v-deep .el-input__suffix {
  right: 10px !important;
}
</style>
