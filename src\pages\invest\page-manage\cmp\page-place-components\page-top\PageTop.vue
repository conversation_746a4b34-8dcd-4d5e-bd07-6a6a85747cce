<template>
  <div
    class="page-top"
    :style="{
      background: localProperty.titleBarBackgroundColor,
    }"
    @click="activeTemplate"
  >
    <div class="toolBar">
      <setting-toolbar :btns="toolBarBtns" @click="toolbarClick"></setting-toolbar>
    </div>
    <div class="page-top-bg" :style="{ backgroundImage: `url(${bgUrl})` }">
      <p class="page-top-title">{{ localProperty.placeTitle }}</p>
    </div>
  </div>
</template>

<script lang="ts" src="./PageTop.ts"></script>

<style lang="scss" scoped>
.page-top {
  width: 100%;
  height: 150px;
  position: relative;
  margin-bottom: 15px;
  &-bg {
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-size: 100%;
  }
  &-title {
    text-align: center;
    padding-top: 42px;
    font-size: 20px;
    color: #fff;
  }
  .toolBar {
    position: absolute;
    top: 0;
    right: 1px;
    z-index: 999;
    cursor: pointer;
  }
}
</style>
