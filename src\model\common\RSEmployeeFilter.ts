export default class RSEmployeeFilter {
  //
  idNameLikes: Nullable<string> = null
  //
  employeeIdEquals: Nullable<string> = null
  //
  employeeIdIsNull: Nullable<boolean> = null
  //
  employeeIdIn: string[] = []
  //
  employeeIdStartsWith: Nullable<string> = null
  //
  employeeIdEndsWith: Nullable<string> = null
  //
  employeeIdLikes: Nullable<string> = null
  //
  employeeNameEquals: Nullable<string> = null
  //
  employeeNameIsNull: Nullable<boolean> = null
  //
  employeeNameIn: string[] = []
  //
  employeeNameStartsWith: Nullable<string> = null
  //
  employeeNameEndsWith: Nullable<string> = null
  //
  employeeNameLikes: Nullable<string> = null
  //
  remarkEquals: Nullable<string> = null
  //
  remark$IsNull: Nullable<boolean> = null
  //
  remarkIn: string[] = []
  //
  remarkStartsWith: Nullable<string> = null
  //
  remarkEndsWith: Nullable<string> = null
  //
  remarkLikes: Nullable<string> = null
  //
  mobileEquals: Nullable<string> = null
  //
  mobile$IsNull: Nullable<boolean> = null
  //
  mobileIn: string[] = []
  //
  mobileStartsWith: Nullable<string> = null
  //
  mobileEndsWith: Nullable<string> = null
  //
  mobileLikes: Nullable<string> = null
  //
  affiliatedOrgCodeEquals: Nullable<string> = null
  //
  affiliatedOrgCode$IsNull: Nullable<boolean> = null
  //
  affiliatedOrgCodeIn: string[] = []
  //
  affiliatedOrgCodeStartsWith: Nullable<string> = null
  //
  affiliatedOrgCodeEndsWith: Nullable<string> = null
  //
  affiliatedOrgCodeLikes: Nullable<string> = null
  //
  employeeIdOrder: Nullable<boolean> = null
  //
  employeeNameOrder: Nullable<boolean> = null
  //
  remarkOrder: Nullable<boolean> = null
  //
  mobileOrder: Nullable<boolean> = null
  //
  affiliatedOrgCodeOrder: Nullable<boolean> = null
  //
  page: Nullable<number> = null
  //
  pageSize: Nullable<number> = null
}