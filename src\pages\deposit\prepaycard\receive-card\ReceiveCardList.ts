import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import FormItem from 'cmp/formitem/FormItem';
import MyQueryCmp from 'cmp/querycondition/MyQueryCmp';
import I18nPage from 'common/I18nDecorator';
import {Component, Vue} from "vue-property-decorator";
import IdName from "model/common/IdName";
import MakeCardBill from "model/prepay/card/MakeCardBill";
import SelectStores from 'cmp/selectStores/SelectStores';
import {ReceiveCardBillFilter} from "model/card/receivebill/ReceiveCardBillFilter";
import ReceiveCardBillApi from "http/card/receivebill/ReceiveCardBillApi";
import ReceiveCardBill from "model/card/receivebill/ReceiveCardBill";
@Component({
    name: 'ReceiveCardList',
    components: {
        BreadCrume,
        MyQueryCmp,
        FormItem,
        SelectStores
    }
})
@I18nPage({
    prefix: [
        '/公用/券模板',
        '/卡/卡管理/领卡单/领卡单列表',
        '/卡/卡管理/领卡单/领卡单详情',
        '/卡/卡管理/领卡单/领卡单编辑',
        '/公用/查询条件/提示',
        '/营销/券礼包活动/券礼包活动',
        '/公用/过滤器',
        '/公用/下拉框/提示',
        '/储值/预付卡/卡模板/编辑页面',
        '/公用/券核销',
        '/储值/预付卡/卡模板/列表页面',
        '/公用/按钮',
        '/公用/活动/提示信息'
    ],
    auto: true
})

export default class ReceiveCardList extends Vue {
    $refs: any
    panelArray: any = []
    occurredOrg: Nullable<IdName> = null
    query: ReceiveCardBillFilter = new ReceiveCardBillFilter()
    tableData:ReceiveCardBill[] = []
    outOrg: IdName = new IdName()
    inOrg: IdName = new IdName()

    page: any = {
        pageSize: 10,
        page: 1,
        total: 0
    }

    created() {
        this.panelArray = [
            {
                name: this.i18n("领卡单"),
                url: "",
            },
        ]

        this.getList()
    }
    // 获取领卡单信息
    getList() {
        const params = new ReceiveCardBillFilter()
        params.page = this.page.page - 1
        params.pageSize = this.page.pageSize
        params.probePages = -1
        params.billNumberEquals = this.query.billNumberEquals
        params.cardCodeEquals = this.query.cardCodeEquals
        if (this.outOrg) {
            params.outOrgIdEquals = this.outOrg.id || null
        } else {
            params.outOrgIdEquals = null
        }
        if (this.inOrg) {
            params.inOrgIdEquals = this.inOrg.id || null
        } else {
            params.inOrgIdEquals = null
        }
        params.stateEquals = this.query.stateEquals
        ReceiveCardBillApi.query(params).then(res => {
            if (res.code === 2000) {
                this.tableData = res.data || []
                this.page.total = res.total
            } else {
                this.$message.error(res.msg || this.i18n('查询领卡单列表失败'))
            }
        }).catch((error) => {
            this.$message.error(error.message || this.i18n('查询领卡单列表失败'))
        })
    }

    doAudit(billNumber: string) {
        this.$confirm(
            this.i18n("确定审核当前领卡单吗？"),
            this.i18n("审核"),
            {
                confirmButtonText: this.formatI18n("/公用/按钮", "确定"),
                cancelButtonText: this.formatI18n("/公用/按钮", "取消"),
            }
        ).then(() => {
            ReceiveCardBillApi.audit(billNumber).then((res) => {
                if (res.code === 2000) {
                    this.$message.success(this.i18n('操作成功'))
                    this.getList()
                } else {
                    this.$message.error(res.msg || this.i18n('操作失败'))
                }
            }).catch((error) => {
                this.$message.error(error.message || this.i18n('操作失败'))
            })
        });
    }

    // 前往编辑页面
    doEdit(billNumber: string) {
        this.$router.push({
            name: 'receive-card-edit',
            query: {
                editType: 'edit',
                billNumber: billNumber
            }
        })
    }

    doCancel(billNumber: string) {

    }

    batchAudit() {

    }
    // 跳转详情页面
    goDtl(billNumber: string) {
        this.$router.push({
            name: 'receive-card-dtl',
            query: { billNumber: billNumber }
        })
    }

    onReset() {
        this.page.page = 1
        this.page.pageSize = 10
        this.inOrg = new IdName()
        this.outOrg = new IdName()
        this.query = new ReceiveCardBillFilter()
        this.getList()
    }

    onSearch() {
        this.page.page = 1
        this.getList()
    }

    // 跳转新增领卡单页面
    doCreate() {
        this.$router.push({
            name: 'receive-card-edit',
            query: {
                editType: 'create'
            }
        })
    }

    onHandleCurrentChange(val: number) {
        this.page.page = val
        this.getList()
    }

    onHandleSizeChange(val: number) {
        this.page.pageSize = val
        this.getList()
    }

    computeState(state: string) {
        let str = '-'
        let color = '#A1B0C8'
        if (state === 'INITIAL') {
            str = this.i18n('未审核')
            color = '#FFAA00'
        } else if (state === 'AUDITED') {
            str = this.i18n('已审核')
            color = '#1597FF'
        }
        return {
            state: str,
            color: color
        }
    }
}