import BreadCrume from 'cmp/bread-crumb/BreadCrume'
import I18nPage from 'common/I18nDecorator'
import { Component, Vue } from 'vue-property-decorator'
import UploadImg from '@/cmp/upload-imgs/UploadImg.vue'
import ContentTemplateFilter from 'model/template/ContentTemplateFilter'
import ContentTemplateApi from 'http/template/ContentTemplateApi'
import ContentTemplate from 'model/template/ContentTemplate'
import CreateRequest from 'model/template/CreateRequest'
import UpdateRequest from 'model/template/UpdateRequest'
import PublishRequest from 'model/template/PublishRequest'
import ConstantMgr from 'mgr/ConstantMgr'
import CommonUtil from 'util/CommonUtil'
import { BigWheelImageVO } from './BigWheelImageVO'
import bigWheelImageLink from './BigWheelImageUrlLink'
const backgroundImage = bigWheelImageLink[BigWheelImageVO.bigwheel_bg] || require('@/assets/image/fellow/img_bg.png')
const baseBackgroundImage = bigWheelImageLink[BigWheelImageVO.img_zhuanpan] || require('@/assets/image/fellow/img_zhuanpandizuo.png')
const turntablePointerImage = bigWheelImageLink[BigWheelImageVO.img_chou] ||  require('@/assets/image/fellow/img_chou.png')
const turntableWinArea = bigWheelImageLink[BigWheelImageVO.img_zhuanpanjiangpin_normal1] || require('@/assets/image/fellow/img_zhuanpanjiangpin_normal1.png')
const turntableWinAreas = bigWheelImageLink[BigWheelImageVO.img_zhuanpanjiangpin_normal2] ||  require('@/assets/image/fellow/img_zhuanpanjiangpin_normal2.png')
const turntableNoWinArea = bigWheelImageLink[BigWheelImageVO.img_zhuanpanjiangpin_normal3] ||  require('@/assets/image/fellow/img_zhuanpanjiangpin_normal3.png')
const clickHandImage = bigWheelImageLink[BigWheelImageVO.img_finger] ||  require('@/assets/image/fellow/img_finger.png')
const myPrize =  bigWheelImageLink[BigWheelImageVO.ic_dazhuanpan_gift] ||require('@/assets/image/fellow/ic_dazhuanpan_gift.png')
const obtainOpportunity = bigWheelImageLink[BigWheelImageVO.cutting] || require('@/assets/image/fellow/cutting.png')
const buttonBg = bigWheelImageLink[BigWheelImageVO.btn_start] || require('@/assets/image/fellow/btn_start.png')
const promptBg = bigWheelImageLink[BigWheelImageVO.bg_tips] || require('@/assets/image/fellow/bg_tips.png')
@Component({
  name: 'PageTurntableEdit',
  components: {
    BreadCrume,
    UploadImg
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理',
    '/设置/页面管理'
  ],
  auto: true
})
export default class PageTurntableEdit extends Vue {

  ContentTemplateFilter: ContentTemplateFilter = new ContentTemplateFilter()
  ContentTemplate: ContentTemplate[] = []// 活动模型
  // 保存参数
  saveParams: CreateRequest = new CreateRequest()
  // 编辑参数
  updateParams: UpdateRequest = new UpdateRequest()
  // 发布参数
  publishRequestId: PublishRequest = new PublishRequest()
  editId: any = ''
  page = {
    currentPage: 1,
    total: 0,
    size: 20
  }
  // 接收属性值
  widgets: any = []
  imgData: any = {
    id: "bigWheel",
    type: 'custom',
    uuid: '',
    propBackgroundImage: backgroundImage,
    propBaseBackgroundImage: baseBackgroundImage,
    propTurntablePointerImage: turntablePointerImage,
    propTurntableWinArea: {
      backgroundImages: [turntableWinArea],
      image: '',
      fontColor: '#FFFFFF',
      backgroundColor: '',
    },
    propTurntableNoWinArea: {
      backgroundImages: [turntableWinAreas, turntableNoWinArea],
      image: '',
      fontColor: '#F64602',
      backgroundColor: '',
    },
    propRuleButton: {
      backgroundImages: [],
      image: '',
      fontColor: '#784313',
      backgroundColor: '#FFFFFF',
    },
    propShareButton: {
      backgroundImages: [],
      image: '',
      fontColor: '#784313',
      backgroundColor: '#FFFFFF',
    },
    propClickHandImage: clickHandImage,
    propLotteryButton: {
      backgroundImages: [buttonBg],
      image: '',
      fontColor: '#FFFFFF',
      backgroundColor: '',
    },
    propTimesPrompt: {
      backgroundImages: [promptBg],
      image: '',
      fontColor: '#242633',
      backgroundColor: '',
    },
    propMyPrize: {
      backgroundImages: [],
      image: myPrize,
      fontColor: '#784313',
      backgroundColor: '#FFF0C4',
    },
    propObtainOpportunity: {
      backgroundImages: [],
      image: obtainOpportunity,
      fontColor: '#784313',
      backgroundColor: '#FFF0C4',
    },
  }
  rules: any = {
    propBackgroundImage: [
      { required: true, message: this.i18n('请选择背景图片'), trigger: 'change' },
    ],
    propBaseBackgroundImage: [
      { required: true, message: this.i18n('请选择底座图片'), trigger: 'change' },
    ],
    propTurntablePointerImage: [
      { required: true, message: this.i18n('请选择转盘指针图片'), trigger: 'change' },
    ],
    'propRuleButton.fontColor': [
      { required: true, message: this.i18n('请填写字体颜色'), trigger: 'blur' }
    ],
    'propRuleButton.backgroundColor': [
      { required: true, message: this.i18n('请填写背景颜色'), trigger: 'blur' }
    ],
    'propShareButton.fontColor': [
      { required: true, message: this.i18n('请填写字体颜色'), trigger: 'blur' }
    ],
    'propShareButton.backgroundColor': [
      { required: true, message: this.i18n('请填写背景颜色'), trigger: 'blur' }
    ],
    propClickHandImage: [
      { required: true, message: this.i18n('请选择小手图片'), trigger: 'change' },
    ],
    'propLotteryButton.backgroundImages[0]': [
      { required: true, message: this.i18n('请选择抽奖按钮图片'), trigger: 'blur' }
    ],
    'propTimesPrompt.backgroundImages[0]': [
      { required: true, message: this.i18n('请选择次数提示图片'), trigger: 'blur' }
    ],
    'propLotteryButton.fontColor': [
      { required: true, message: this.i18n('请填写抽奖按钮文字颜色'), trigger: 'blur' }
    ],
    'propTimesPrompt.fontColor': [
      { required: true, message: this.i18n('请填写次数提示文字颜色'), trigger: 'blur' }
    ],
    'propMyPrize.image': [
      { required: true, message: this.i18n('请选择我的奖品图标'), trigger: 'blur' }
    ],
    'propMyPrize.backgroundColor': [
      { required: true, message: this.i18n('请选择按钮颜色'), trigger: 'blur' }
    ],
    'propMyPrize.fontColor': [
      { required: true, message: this.i18n('请选择文案颜色'), trigger: 'blur' }
    ],
    'propObtainOpportunity.image': [
      { required: true, message: this.i18n('请选择获取机会图标'), trigger: 'blur' }
    ],
    'propObtainOpportunity.backgroundColor': [
      { required: true, message: this.i18n('请选择按钮颜色'), trigger: 'blur' }
    ],
    'propObtainOpportunity.fontColor': [
      { required: true, message: this.i18n('请选择文案颜色'), trigger: 'blur' }
    ],
  }
  $refs: any
  imgFormat: any = ['gif', 'jpg', 'jpeg', 'png'];

  handleChange() {
    // this.$emit('input', value);
    // this.$emit('change', value);

  }
  handleValidate() {
    
  }

  created() {
    if (this.$route.query.id) {
      this.editId = this.$route.query.id as string
      this.loadData()
    }
  }

  get isEnglish() {
    return sessionStorage.getItem("locale") === "en_US";
  }
  
  get credential() {
    return this.$store.state.credential;
  }

  get panelArray() {
    return [
      {
        name: this.i18n('编辑大转盘'),
        url: ''
      }
    ]
  }
  // 编辑
  async doUpdate() {
    this.widgets[1].props = this.imgData;
    this.widgets[1].type = 'custom';
    const loading = CommonUtil.Loading()
    try {
      let content: any = JSON.parse(this.updateParams.content);
      this.updateParams.type = 'system';
      content.widgets = this.widgets;
      this.updateParams.name = this.i18n('大转盘');
      this.updateParams.content = content;
      let res = await ContentTemplateApi.update(this.updateParams);
      if (res.code === 2000) {
        this.$message.success(this.i18n('保存成功'))
        this.$router.push({
          name: 'page-manage',
          query: { activeName: 'sys-manage' }
        });
      } else {
        this.$message.error(res.msg || this.i18n('保存失败'))
      }
    } catch (error) {
      this.$message.error((error as Error).message || this.i18n('保存失败'))
    } finally {
      loading.close()
    }
  }
  // 调用发布接口
  publish() {
    this.publishRequestId.id = this.editId
    ContentTemplateApi.publish(this.publishRequestId).then((res) => {
      // console.log(res);
      if (res.code === 2000) {
        this.$message.success(this.i18n('发布成功'))
        this.$router.push({
          name: 'page-manage',
          query: { activeName: 'sys-manage' }
        });
      } else {
        this.$message.error(res.msg || this.i18n('发布失败'))
      }
    }).catch(error => {
      let e = error as any;
      this.$message.error(e.message || this.i18n('发布失败'));
    })
  }
  // 取消返回
  goBack() {
    this.$router.push({
      name: 'page-manage',
      query: { activeName: 'sys-manage' }
    });
  }
  preserve(isPublish: any) {
    this.$refs.form.validate(async (valid: any) => {
      if(valid) {
        await this.doUpdate()
        if(isPublish) {
         this.publish()
        }
      }else {
        this.$message.error(this.i18n('请完善装修信息！'));
      }
    });
  }
  // 编辑get参数
  loadData() {
    const loading = this.$loading(ConstantMgr.loadingOption)
    ContentTemplateApi.get(this.editId).then(res => {
      if (res.data) {
        console.log('res.data ==>', JSON.parse(res.data.content))
        this.updateParams = res.data as any
        if (res.data.content) {
          const details = JSON.parse(res.data.content)
          this.widgets = details.widgets
          // 当传过来有为自定义的就赋值
          this.widgets.forEach((item: any) => {
            if (item.type == 'custom') {
              if(!item.props?.propShareButton) {
                this.$set(item.props, 'propShareButton', {
                  backgroundImages: [],
                  image: '',
                  fontColor: '#784313',
                  backgroundColor: '#FFFFFF',
                })
              }
              this.imgData = item.props
            }
          })
          // 当他只有一个值，并且为默认值时，得添加一个自定义
          if (this.widgets.length == 1 && this.widgets[0].type == 'default') {
            let obj = {}
            this.widgets.forEach((item: any) => {
              obj = item
            })
            this.widgets.push({ ...obj });
          }
        }
      }
    }).catch((err) => {
      this.$message.error(err.message || this.i18n('数据加载失败'))
    }).finally(() => {
      loading.close()
    })
  }
};