import ApiClient from 'http/ApiClient'
import Recover<PERSON><PERSON>Bill from 'model/card/cardRecycle/RecoverCardBill'
import RecoverCardBillFilter from 'model/card/cardRecycle/RecoverCardBillFilter'
import RecoverCardBillLine from 'model/card/cardRecycle/RecoverCardBillLine'
import RecoverCardBillLineFilter from 'model/card/cardRecycle/RecoverCardBillLineFilter'
import SaveRecoverCardBillRequest from 'model/card/cardRecycle/SaveRecoverCardBillRequest'
import Response from 'model/common/Response'
import PrePayCard from 'model/prepay/card/PrePayCard'
export default class RecoverCardBillApi {
  /**
   * 审核卡回收单
   * 审核卡回收单。
   * 
   */
  static audit(billNumber: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/recover-card-bill/audit/${billNumber}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 删除卡回收单
  * 删除卡回收单。
  * 
  */
  static remove(billNumber: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/recover-card-bill/remove/${billNumber}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量审核卡回收单
   * 批量审核卡回收单。
   * 
   */
  static batchAudit(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/recover-card-bill/batch/audit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量删除卡回收单
   * 批量删除卡回收单。
   * 
   */
  static batchRemove(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/recover-card-bill/batch/remove`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导出全部卡号信息
   * 导出全部卡号信息
   * 
   */
  static exportCard(billNumber: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/recover-card-bill/exportCard`, {}, {
      params: {
        billNumber: billNumber
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导出差异数据
   * 导出差异数据
   * 
   */
  static exportDifferenceCard(billNumber: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/recover-card-bill/exportDifferenceCard`, {}, {
      params: {
        billNumber: billNumber
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取卡回收单详情
   * 获取卡回收单详情。
   * 
   */
  static get(billNumber: string): Promise<Response<RecoverCardBill>> {
    return ApiClient.server().get(`/v1/recover-card-bill/get/${billNumber}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询预付卡回收单
   * 分页查询预付卡回收单。
   * 
   */
  static query(body: RecoverCardBillFilter): Promise<Response<RecoverCardBill[]>> {
    return ApiClient.server().post(`/v1/recover-card-bill/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询预付卡回收单差异信息
   * 分页查询预付卡回收单差异信息。
   * 
   */
  static queryDifferenceInfo(body: RecoverCardBillLineFilter): Promise<Response<RecoverCardBillLine[]>> {
    return ApiClient.server().post(`/v1/recover-card-bill/queryDifferenceInfo`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询预付卡回收单匹配信息
   * 分页查询预付卡回收单匹配信息。
   * 
   */
  static queryMatchInfo(body: RecoverCardBillLineFilter): Promise<Response<PrePayCard[]>> {
    return ApiClient.server().post(`/v1/recover-card-bill/queryMatchInfo`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建预付卡回收单
   * 新建预付卡回收单。
   * 
   */
  static save(body: SaveRecoverCardBillRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/recover-card-bill/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建并审核卡回收单
   * 新建并审核卡回收单。
   * 
   */
  static saveAndAudit(body: SaveRecoverCardBillRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/recover-card-bill/saveAndAudit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
 * 录入卡号查询卡信息
 * 录入卡号查询卡信息
 * 
 */
  static queryCard(code: string): Promise<Response<PrePayCard>> {
    return ApiClient.server().post(`/v1/recover-card-bill/queryCard/${code}`, {
    }).then((res) => {
      return res.data
    })
  }

}
