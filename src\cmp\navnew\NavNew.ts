import { Component, Vue, Watch } from 'vue-property-decorator'
import I18nPage from "common/I18nDecorator";
import PermissionMgr from "mgr/PermissionMgr";
import store from "store/index";
import FNode from 'model/authorize/FNode';
import AuthorizeApi from 'http/authorize/AuthorizeApi';
import CommonUtil from 'util/CommonUtil';

@Component({
  name: 'NavNew',
  components: {}
})
@I18nPage({
  auto: false,
  prefix: [
    '/公用/菜单'
  ],
})
export default class NavNew extends Vue {
  // 菜单列表
  menus: any = null
  selectedMajorMenu: any = null
  selectedMinorMenu: any = null
  // 是否展示二级菜单
  subNavVisiable = true
  // 控制图标的展示
  iconVisiable = true
  $refs: any
  $route: any
  majorHash: string
  minorHash: string

  @Watch('subNavVisiable')
  onSubNavVisiableChange(value: boolean) {
    this.subNavVisiable = value
    localStorage.setItem('subNavVisiable', value ? 'true' : 'false')
  }

  @Watch('$route.name', { immediate: true })
  watchRoute() {
    this.setCurrentNav()
  }

  created() {
    this.setCurrentNavVisible()
    this.loadMenus()
  }

  setCurrentNavVisible() {
    // 记录用户二级菜单是展开还是隐藏状态
    let visible = localStorage.getItem('subNavVisiable')
    if (visible) {
      this.subNavVisiable = visible === 'true'
      this.iconVisiable = this.subNavVisiable
    }
  }

  setCurrentNav() {
    if (!this.menus || this.menus.length === 0 //
      || !this.menus[0].children[0] || this.menus[0].children[0].length === 0 //
      || !this.menus[0].children[0].children[0] || this.menus[0].children[0].children[0].length === 0) {
      return
    }
    this.majorHash = this.menus[0].hash
    this.minorHash = this.menus[0].children[0].children[0].hash
    let pathSplit = this.$route.path.split('/')
    if (pathSplit.length < 3) {
      return
    }
    this.majorHash = pathSplit[1]
    this.minorHash = pathSplit[2]
    this.selectedMajorMenu = this.menus.find((e: any) => e.hash === this.majorHash)
    if (this.selectedMajorMenu) {
      for (let child of this.selectedMajorMenu.children) {
        let finish = false
        child.fold = false  //是否折叠,默认不折叠
        for (let child1 of child.children) {
          if (child1.hash === this.minorHash) {
            this.selectedMinorMenu = child1
            finish = true
            break
          }
        }
        if (finish) {
          break
        }
      }
    }
  }

  /**
   * 隐藏二级菜单
   */
  doHideSubNav() {
    this.subNavVisiable = false
    this.iconVisiable = false
  }

  //根据subTitle子元素的数量计算高度
  getMenuHeight(index: number) {
    return this.selectedMajorMenu.children[index].children.length * 36 + 'px'
  }

  /**majorHash
   * 展示二级菜单
   */
  doShowSubNav() {
    this.subNavVisiable = true
    this.iconVisiable = true
  }

  /**
   * 鼠标点击
   */
  doMouseClick(index: number) {
    if (this.menus[index].hash === this.majorHash) {
      // 如果已经在当前主菜单下， 不允许在点击
      return
    }
    this.majorHash = this.menus[index].hash
    this.selectedMajorMenu = this.menus[index]
    this.selectedMajorMenu.children.forEach((item: any) => { item.fold = false })  //切换菜单时，将该菜单下的所有subTitle都展开
    // 需要默认打开下面的第一个字菜单
    if (this.menus.length > 0 && this.menus[index].children && this.menus[index].children.length > 0) {
      let sub = this.menus[index].children[0]
      if (sub.children && sub.children.length > 0) {
        let child = sub.children[0]
        this.doGoView(child)
      }
    }
    this.$forceUpdate()
  }

  /**
   * 鼠标移入
   * @param index
   */
  doMouseOver(index: number) {
    if (!this.subNavVisiable) {
      if (this.$refs && this.$refs.popper) {
        this.$refs.popper[index] && this.$refs.popper[index].doShow()
        setTimeout(() => {
          let popperHeight = this.$refs.popper[index].$el.children[0].clientHeight
          let height = (index + 1) * 80 + popperHeight + 64
          if (height >= document.body.clientHeight) {
            this.$refs.popper[index].$el.children[0].style.height = (popperHeight - (height - document.body.clientHeight) + 64) + 'px'
            this.$refs.popper[index].$el.children[0].style.overflow = 'auto'
          }
        }, 50)
      }
    }
  }

  /**
   * 鼠标移除
   * @param index
   */
  doMouseLeave(index: number) {
    if (!this.subNavVisiable) {
      this.$refs.popper[index] && this.$refs.popper[index].doClose()
    }
  }

  //点击切换折叠/展开
  doClickFold(index: number) {
    this.selectedMajorMenu.children[index].fold = !this.selectedMajorMenu.children[index].fold
    this.$forceUpdate()
  }

  /**
   * 跳转路由
   */
  doGoView(sub: any) {
    this.selectedMinorMenu = sub
    if (this.$route.name === sub.hash) {
      this.$emit('refresh')
    } else {
      this.$router.push({ name: sub.hash }).catch(((e) => {
        if (e.name === 'NavigationDuplicated') {
          this.$emit('refresh')
        }
      }))
    }
  }

  /**
   * 进入预约文件列表
   */
  onToFileEntry() {
    this.$emit('download')
  }

  private loadMenus() {
    PermissionMgr.refreshPermission().then((menus: any[]) => {
      this.sortMenus(JSON.parse(JSON.stringify(menus))).then((res: any[]) => {
        this.menus = res
        this.setCurrentNav()
      })
    })
  }

  // 根据getTree接口返回的菜单树结构，重排菜单顺序
  private async sortMenus(menus: any[]) {
    let res: any[] = []
    try {
      const { data } = await AuthorizeApi.getTree()
      let treeInfo: FNode[] = data || []
      const regex = /\/([^\/]+)$/; // 匹配最后一个/后的内容
      // 遍历treeInfo，根据treeInfo中的树结构 重新对menus进行排序
      treeInfo.forEach((item1: FNode) => {
        const menu1Index = menus.findIndex((menu: any) => menu.name === item1.resourceId?.match(regex)?.[1])  //一级菜单，name和resourceId一致
        if (menu1Index !== -1) {
          const menu1 = JSON.parse(JSON.stringify(menus[menu1Index]))
          menu1.children = []
          // 开始匹配二级菜单
          item1?.children?.forEach((item2: FNode) => {
            const menu2Index = menus[menu1Index]?.children.findIndex((menu: any) => menu.subTitle === item2.resourceId?.match(regex)?.[1])  //二级菜单，subTitle和resourceId一致
            if (menu2Index !== -1) {
              const menu2 = JSON.parse(JSON.stringify(menus[menu1Index]?.children[menu2Index]))
              menu2.children = []
              // 开始匹配三级菜单
              item2?.children?.forEach((item3: FNode) => {
                let menu3Index = menus[menu1Index].children[menu2Index].children.findIndex((menu: any) => menu.name === item3.resourceId?.match(regex)?.[1])  //三级菜单，name和resourceId一致
                // 会员资料（大桥石化）的resourceId为/会员/会员管理/会员资料（大桥石化），需要特殊处理
                if (item3.resourceId?.match(regex)?.[1] === '会员资料（大桥石化）') {
                  menu3Index = menus[menu1Index].children[menu2Index].children.findIndex((menu: any) => menu.hash === 'dqsh-member')
                }
                // 等级管理1.0的resourceId为/会员/等级管理1.0，需要特殊处理
                if (item3.resourceId?.match(regex)?.[1] === '等级管理1.0') {
                  menu3Index = menus[menu1Index].children[menu2Index].children.findIndex((menu: any) => menu.hash === 'level')
                }
                // 等级管理2.0的resourceId为/会员/会员体系/等级管理2.0，需要特殊处理
                if (item3.resourceId?.match(regex)?.[1] === '等级管理2.0') {
                  menu3Index = menus[menu1Index].children[menu2Index].children.findIndex((menu: any) => menu.hash === 'level2')
                }
                if (menu3Index !== -1) {
                  const menu3 = JSON.parse(JSON.stringify(menus[menu1Index].children[menu2Index].children[menu3Index]))
                  menu2.children.push(menu3)
                }
              })
              menu1.children.push(menu2)
            }
          })
          if (menu1) {
            res.push(menu1)
          }
        }
      })
      // 根据treeInfo中的树结构 重新对menus进行排序
    } catch (error) {
      console.error('获取树结构失败', error)
      res = menus || []
    }
    console.log('菜单树结构重排', res);
    return res
  }
}