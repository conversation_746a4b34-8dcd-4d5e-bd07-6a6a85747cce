import DownGradeCondition from "./DownGradeCondition"
import GradeCondition from "./GradeCondition"


export default class GradeRule {
  // 序号
  seq: Nullable<number> = null
  // 等级代码
  gradeCode: Nullable<string> = null
  // 等级名称
  gradeName: Nullable<string> = null
  // c端是否展示
  display: Nullable<boolean> = null
  // 是否可以付费升级
  purchasable: Nullable<boolean> = null
  // 升级条件
  condition: Nullable<GradeCondition> = null
  // 评定规则, RE_RATING:重算等级; DOWN_ONE_GRADE:降一级;KEEP:保持降级
  downGradeCondition : Nullable<DownGradeCondition> = null
}