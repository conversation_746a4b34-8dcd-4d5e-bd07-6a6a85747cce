/*
 * @Author: 黎钰龙
 * @Date: 2024-04-27 09:51:30
 * @LastEditTime: 2024-04-29 16:31:39
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\member\equity\EquityCenterDtl.ts
 * 记得注释
 */
import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import FormItem from 'cmp/formitem/FormItem';
import ListWrapper from 'cmp/list/ListWrapper';
import MyQueryCmp from 'cmp/querycondition/MyQueryCmp';
import I18nPage from 'common/I18nDecorator';
import BenefitConfigApi from 'http/grade/equityCenter/BenefitConfigApi';
import BenefitConfig from 'model/member/BenefitConfig';
import BenefitConfigFilter from 'model/member/BenefitConfigFilter';
import CommonUtil from 'util/CommonUtil';
import { Component, Vue } from 'vue-property-decorator';
@Component({
  name: 'EquityCenterDtl',
  components: {
    <PERSON>read<PERSON><PERSON><PERSON>,
    ListWrapper,
    MyQueryCmp,
    FormItem
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/会员/权益中心',
    '/公用/按钮'
  ],
  auto: true
})
export default class EquityCenterDtl extends Vue {
  tableData: BenefitConfig[] = []
  query: any = {
    nameLikes: ''
  }
  page: any = {
    currentPage: 1,
    size: 10,
    total: 0,
  }

  get panelArray() {
    return [{
      name: this.i18n("/公用/菜单/权益中心"),
      url: "",
    },]
  }

  created() {
    this.queryList()
  }

  queryList() {
    const params = new BenefitConfigFilter()
    params.page = this.page.currentPage - 1;
    params.pageSize = this.page.size;
    params.nameLikes = this.query.nameLikes || null
    const loading = CommonUtil.Loading()
    BenefitConfigApi.query(params).then((res) => {
      if (res.code === 2000) {
        console.log('data呢', res);

        this.tableData = res.data || []
        this.page.total = res.total || 0
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    }).finally(() => {
      loading.close()
    })
  }

  doSearch() {
    this.page.currentPage = 1
    this.queryList()
  }

  doReset() {
    this.page.currentPage = 1
    this.query.nameLikes = ''
    this.queryList()
  }

  doAdd() {
    this.$router.push({
      name: 'equity-center-edit',
      query: {
        editType: 'create'
      }
    })
  }

  doRemove(uuid: string, version: string) {
    this.$confirm(
      this.formatI18n("/营销/券礼包活动/新建注册发大礼包/详情界面/点击删除提示信息", "删除后不可恢复，确定要删除吗？") as string,
      this.formatI18n("/公用/按钮", "删除") as string,
      {
        confirmButtonText: this.formatI18n("/公用/按钮", "确定") as string,
        cancelButtonText: this.formatI18n("/公用/按钮", "取消") as string,
      }
    ).then(() => {
      BenefitConfigApi.delete(uuid, version).then((res) => {
        if (res.code === 2000) {
          this.$message.success(this.i18n('/公用/预约文件列表/删除成功'))
          this.doReset()
        } else {
          throw new Error(res.msg!)
        }
      }).catch((error) => {
        this.$message.error(error.message)
      })
    })
  }

  doModify(uuid: string) {
    this.$router.push({
      name: 'equity-center-edit',
      query: {
        editType: 'edit',
        id: uuid
      }
    })
  }

  onHandleCurrentChange = (val: number) => {
    this.page.currentPage = val;
    this.queryList();
  }

  onHandleSizeChange = (val: number) => {
    this.page.size = val;
    this.page.currentPage = 1
    this.queryList();
  }
};