<template>
    <div class="prepay-card-adjust-reason">
        <!--<SubHeader title="储值调整原因设置"></SubHeader>-->
        <BreadCrume :panelArray="panelArray"></BreadCrume>
        <div class="add">
            <FormItem label="预付卡调整原因">
                <el-input
                        maxlength="50"
                        placeholder="请输入不超过50个字"
                        ref="reason"
                        show-word-limit
                        style="width: 315px"
                        type="textarea"
                        v-model="reason"></el-input>
            </FormItem>
            <FormItem style="margin-left: 10px">
                <i class="iconfont  ic-info" style="font-size: 18px;color: #20A0FF"></i>&nbsp;<span>不允许与已有预付卡调整原因重复，最多可创建20个。</span>
            </FormItem>
            <FormItem style="margin-left: -9px">
                <el-button v-if="hasOptionPermission('/卡/卡管理/预付卡调整单', '单据维护')" @click="doAddReason" style="margin-left: 20px" type="primary">添加</el-button>
                <el-button v-if="hasOptionPermission('/卡/卡管理/预付卡调整单', '单据维护')" @click="doClear" style="margin-left: 20px">清空</el-button>
            </FormItem>
        </div>
        <div class="table">
            <div style="margin-bottom: 10px;margin-top: 20px">
              <i18n k="/储值/预付卡/预付卡调整单/预付卡调整原因设置/已选择{0}个原因">
                <template slot="0">
                  &nbsp;{{selectedArr.length}}&nbsp;
                </template>
              </i18n>
              &nbsp;&nbsp;
              <el-button @click="doBatchDelete" style="color: red">批量删除</el-button>
<!--            <el-input placeholder="搜索储值调整原因" style="width:300px;float: right" v-model="searchContent"> -->
<!--                <el-button @click="doSearchByKey" icon="el-icon-search" slot="append"></el-button></el-input>-->
<!--            </div>-->
            <el-input @keydown.native.13="doSearchByKey"
                      placeholder="搜索预付卡调整原因"
                      style="width:300px;float: right"
                      v-model="searchContent">
                      <i class="el-icon-search" slot="suffix" style="line-height:32px;cursor:pointer" @click="doSearchByKey"></i>
            </el-input>
            </div>
            <el-table :data="tableData" @selection-change="handleSelectionChange" >
                <el-table-column
                        type="selection"
                        width="55"></el-table-column>
                <el-table-column label="预付卡调整原因" prop="content" width="200">
                    <template slot-scope="scope">
                        <div :title="scope.row.content" no-i18n style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">{{scope.row.content}}</div>
                    </template>
                </el-table-column>
                <el-table-column align="left" label="创建时间" prop="created">
                    <template slot-scope="scope">
                        <div style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;" no-i18n>{{scope.row.created | dateFormate3}}</div>
                    </template>
                </el-table-column>
                <el-table-column align="left" label="操作" prop="">
                    <template slot-scope="scope">
                        <el-button @click="doEdit(scope.row)" type="text">修改</el-button>
                        <div style="height: 10px;
                            width: 2px;
                            background-color: rgba(51, 51, 51, 0.247);
                            display: inline-block;
                            margin-left: 5px;
                            margin-right: 5px;
                            position: relative;
                            top: 1px;"></div>
                        <el-button @click="doDelete(scope.row)" type="text">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="store-value-page">
            <el-pagination
                no-i18n
                    :current-page="page.currentPage"
                    :page-size="page.size"
                    :page-sizes="[10, 20, 30, 40]"
                    :total="page.total"
                    @current-change="onHandleCurrentChange"
                    @size-change="onHandleSizeChange"
                    background
                    layout="total, prev, pager, next, sizes,  jumper">
            </el-pagination>
        </div>
        <StoreValueAdjustReasonAdd
            no-i18n
                :data="data"
                :dialogShow="dialogShow"
                @dialogClose="doDialogClose"
                reasonType="cardAdjustReason"></StoreValueAdjustReasonAdd>
    </div>
</template>

<script lang="ts" src="./PrepayCardAdjustReason.ts">
</script>

<style lang="scss">
.prepay-card-adjust-reason{
    width: 100%;
    height: 100%;
    background-color: white;
    overflow: auto;
    .add{
        padding-left: 30px;
        padding-top: 30px;
    }
    .table{
        padding-left: 30px;
        padding-top: 30px;
        padding-right: 30px;
    }
    .store-value-page{
        margin-top: 10px;
        padding-right: 20px;
        padding-bottom: 30px;
    }
    .el-table__body .el-table__row td {
        border-bottom: 1px solid #d7dfeb !important;
    }
    .qf-form-item .qf-form-label{
        width: 110px !important;
    }
}
</style>