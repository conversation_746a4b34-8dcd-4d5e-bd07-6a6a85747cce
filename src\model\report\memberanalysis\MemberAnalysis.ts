/*
 * @Author: haiding <EMAIL>
 * @Date: 2025-02-19 09:25:09
 * @LastEditors: haiding <EMAIL>
 * @LastEditTime: 2025-02-21 10:18:00
 * @FilePath: \new\src\model\report\memberanalysis\MemberAnalysis.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import MemberAnalysisCondition from 'model/default/MemberAnalysisCondition'
import MemberAnalysisDimension from 'model/default/MemberAnalysisDimension'
import MemberAnalysisMetrics from 'model/default/MemberAnalysisMetrics'

export default class MemberAnalysis {
  // 报表名称
  name: Nullable<string> = null
  // 
  metrics: MemberAnalysisMetrics[] = []
  // 
  dimensions: MemberAnalysisDimension[] = []
  // 
  conditions: MemberAnalysisCondition[] = []
  //创建时间
  created: Nullable<string> = null
  //最后修改时间
  lastModified: Nullable<string> = null
  //创建人
  creator: Nullable<string> = null
  //最后修改人
  lastModifier: Nullable<string> = null
  // 
  uuid: Nullable<string> = null
}