import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue';
import FormItem from 'cmp/formitem/FormItem.vue';
import MyQueryCmp from 'cmp/querycondition/MyQueryCmp.vue';
import I18nPage from 'common/I18nDecorator';
import { Component } from 'vue-property-decorator';
import AnalysisDateSelector from '../cmp/AnalysisDateSelector/AnalysisDateSelector.vue';
import ChannelSelect from 'cmp/channelselect/ChannelSelect.vue';
import SelectStores from 'cmp/selectStores/SelectStores.vue';
import MemberLineChart from '../cmp/MemberLineChart/MemberLineChart.vue';
import SelectEmployees from 'cmp/selectEmployees/selectEmployees.vue';
import OrgMemberAnalysisReportQuery from 'model/analysis/OrgMemberAnalysisReportQuery';
import AnalysisReportApi from 'http/analysis/AnalysisReportApi';
import CommonUtil from 'util/CommonUtil';
import DecimalFormatterUtil from 'util/DecimalFormatterUtil';
import AbstractLineChart from '../cmp/AbstractLineChart';
import DownloadCenterDialog from 'pages/main/dowmload_center_dialog/DownloadCenterDialog.vue';
import RechargeableCardAnalysisContorllerApi from 'http/analysis/RechargeableCardAnalysisContorllerApi';
import CardSummaryAnalysisContorllerApi from 'http/analysis/CardSummaryAnalysisContorllerApi';
import BRechargeableCardAnalysisReportGraphic from 'model/analysis/BRechargeableCardAnalysisReportGraphic';
import BPrepayCardAnalysisFilter from 'model/analysis/BPrepayCardAnalysisFilter';
import BPrepayCardAnalysisSummaryReport from 'model/analysis/BPrepayCardAnalysisSummaryReport';
import BRechargeableCardAnalysisReport from 'model/analysis/BRechargeableCardAnalysisReport'


class Filter {
  dataRange: any = null  //时间
  store: any = null  //门店
  templateId: any = null  //模板号
}

@Component({
  name: 'RechargeableCardAnalysis',
  components: {
    BreadCrume,
    MyQueryCmp,
    FormItem,
    AnalysisDateSelector,
    ChannelSelect,
    SelectStores,
    MemberLineChart,
    SelectEmployees,
    DownloadCenterDialog
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/数据/会员增长分析',
    '/数据/会员积分分析',
    '/数据/会员储值分析'
  ],
  auto: true
})
export default class RechargeableCardAnalysis extends AbstractLineChart {
  $refs: any
  panelArray: any = []
  filter: Filter = new Filter()
  detail: BRechargeableCardAnalysisReportGraphic = new BRechargeableCardAnalysisReportGraphic()
  detailSum: BRechargeableCardAnalysisReport = new BRechargeableCardAnalysisReport();
  summary: BPrepayCardAnalysisSummaryReport = new BPrepayCardAnalysisSummaryReport()
  downloadCenterFlag: boolean = false; //文件下载中心弹窗

  /* 每个item的第一项：数据名
  /  第二项：数据值
  /  第三项：是否展示在右y轴上 */
  get valueArray() {
    const arr = [
      [this.i18n('消费'), this.detail.pay, 0],
      [this.i18n('充值'), this.detail.deposit, 0],
      [this.i18n('转入'), this.detail.transferIn, 0],
      [this.i18n('转出'), this.detail.transferOut, 0],
      [this.i18n('调整加'), this.detail.adjustAdd, 0],
      [this.i18n('调整减'), this.detail.adjustSub, 0],
      [this.i18n('作废'), this.detail.cancel, 0],

      [this.i18n('可用余额'), this.detail.usableTotalAmount, 0],
      [this.i18n('可用本金'), this.detail.usableAmount, 0],
      [this.i18n('可用赠金'), this.detail.usableGiftAmount, 0]
    ]
    return this.doTransValueArray(arr)
  }

  get summaryViewArr() {
    return [
      {
        label: this.i18n('消费'),
        value: (this.detailSum?.pay) ? DecimalFormatterUtil.formatNumber(this.detailSum.pay) : '0.00',
      },
      {
        label: this.i18n('充值'),
        value: (this.detailSum?.deposit) ? DecimalFormatterUtil.formatNumber(this.detailSum.deposit) : '0.00',
      },

      {
        label: this.i18n('转入'),
        value: (this.detailSum?.transferIn) ? DecimalFormatterUtil.formatNumber(this.detailSum.transferIn) : '0.00',
      }
      ,
      {
        label: this.i18n('转出'),
        value: (this.detailSum?.transferOut) ? DecimalFormatterUtil.formatNumber(this.detailSum.transferOut) : '0.00',
      },
      {
        label: this.i18n('调整加'),
        value: (this.detailSum?.adjustAdd) ? DecimalFormatterUtil.formatNumber(this.detailSum.adjustAdd) : '0.00',
      },
      {
        label: this.i18n('调整减'),
        value: (this.detailSum?.adjustSub) ? DecimalFormatterUtil.formatNumber(this.detailSum.adjustSub) : '0.00',
      },
      {
        label: this.i18n('作废'),
        value: (this.detailSum?.cancel) ? DecimalFormatterUtil.formatNumber(this.detailSum.cancel) : '0.00',
      },
      {
        label: this.i18n('可用余额'),
        value: (this.detailSum?.usableTotalAmount) ? DecimalFormatterUtil.formatNumber(this.detailSum.usableTotalAmount) : '0.00',
      },
      {
        label: this.i18n('可用本金'),
        value: (this.detailSum?.usableAmount) ? DecimalFormatterUtil.formatNumber(this.detailSum.usableAmount) : '0.00',
      },
      {
        label: this.i18n('可用赠金'),
        value: (this.detailSum?.usableGiftAmount) ? DecimalFormatterUtil.formatNumber(this.detailSum.usableGiftAmount) : '0.00',
      }

    ]
  }

  get getEndUsableTotalAmount() {
    return this.summary.endUsableTotalAmount ? DecimalFormatterUtil.formatNumber(this.summary.endUsableTotalAmount) : '0.00'
  }

  get getEndUsableAmount() {
    return this.summary.endUsableAmount ? DecimalFormatterUtil.formatNumber(this.summary.endUsableAmount) : '0.00'
  }

  get getEndUsableGiftAmount() {
    return this.summary.endUsableGiftAmount ? DecimalFormatterUtil.formatNumber(this.summary.endUsableGiftAmount) : '0.00'
  }


  created() {
    this.panelArray = [
      {
        name: this.i18n('/公用/菜单/会员储值卡分析'),
        url: ""
      }
    ]
  }

  mounted() {
    this.onSearch()
    this.getSummary()
  }

  queryDetailSum() {
    const body = this.doFilterParams()
    const loading = CommonUtil.Loading()
    RechargeableCardAnalysisContorllerApi.querySum(body).then((res) => {
      if (res.code === 2000) {
        this.detailSum = res.data || new BRechargeableCardAnalysisReport()
      } else {
        throw new Error(res.msg as any)
      }
    }).catch((error) => {
      this.$message.error(error.message || this.i18n('内部异常'))
    }).finally(() => {
      loading.close()
    })
  }

  getSummary() {
    const body = this.doFilterParams()
    const loading = CommonUtil.Loading()
    CardSummaryAnalysisContorllerApi.query(body).then((res) => {
      if (res.code === 2000) {
        this.summary = res.data || new BPrepayCardAnalysisSummaryReport()
      } else {
        throw new Error(res.msg as any)
      }
    }).catch((error) => {
      this.$message.error(error.message || this.i18n('内部异常'))
    }).finally(() => {
      loading.close()
    })
  }


  doExport() {
    this.$confirm(this.i18n("将根据当前查询条件生成报表，确认导出吗？"), this.i18n('/储值/预付卡/充值卡制售单/列表页面/导出'), {
      confirmButtonText: this.formatI18n("/公用/按钮", "确定") as string,
      cancelButtonText: this.formatI18n("/公用/按钮", "取消") as string,
    }).then(() => {
      const body = this.doFilterParams()
      RechargeableCardAnalysisContorllerApi.export(body).then((res) => {
        if (res.code === 2000) {
          this.downloadCenterFlag = true;
        } else {
          throw new Error(res.msg!)
        }
      }).catch((error) => {
        this.$message.error(error.message)
      })
    });
  }

  doReset() {
    this.filter = new Filter()
    this.$refs.analysisDateSelector.doReset()
    this.onSearch()
    this.getSummary()
    this.queryDetailSum();
  }

  onSearch() {
    const body = this.doFilterParams()
    const loading = CommonUtil.Loading()
    RechargeableCardAnalysisContorllerApi.query(body).then((res) => {
      if (res.code === 2000) {
        this.detail = res.data || new BRechargeableCardAnalysisReportGraphic()
      } else {
        throw new Error(res.msg as any)
      }
    }).catch((error) => {
      this.$message.error(error.message || this.i18n('内部异常'))
    }).finally(() => {
      loading.close()
    })
    this.queryDetailSum();
  }

  // 查询条件
  doFilterParams() {
    const params = new BPrepayCardAnalysisFilter()
    params.dateUnitEquals = this.filter.dataRange?.type
    if (this.filter.dataRange?.date?.length) {
      params.startDate = this.filter.dataRange.date[0]
      params.endDate = this.filter.dataRange.date[1]
    }
    params.orgId = this.filter.store
    params.reportType = "rechargeableCard"
    params.templateId = this.filter.templateId
    return params
  }

  doDateChange(value: any) { // {type: 'DAY' | 'WEEK' | 'MONTH', date:['2024-03-05','2024-03-06']}
    this.filter.dataRange = value
  }

  doDownloadDialogClose() {
    this.downloadCenterFlag = false;
  }

  // 需要显示百分号的数据名称
  get showPercentName() {
    return [this.i18n('新会员有消占比'), this.i18n('复购率')]
  }
};