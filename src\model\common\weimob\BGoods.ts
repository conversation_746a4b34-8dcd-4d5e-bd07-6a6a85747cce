import BGoodsPrice from 'model/common/weimob/BGoodsPrice'
import BGoodsStock from 'model/common/weimob/BGoodsStock'

export default class BGoods {
  // 商品 ID
  goodsId: Nullable<number> = null
  // 商品名称
  title: Nullable<string> = null
  // 副标题
  subTitle: Nullable<string> = null
  // 归属门店 ID
  createVid: Nullable<number> = null
  // 默认图片
  defaultImageUrl: Nullable<string> = null
  // 外部商品编码，商户自有 ERP 系统的商品编码。
  outerGoodsCode: Nullable<string> = null
  // 商品库存
  goodsStock: Nullable<BGoodsStock> = null
  // 商品价格
  goodsPrice: Nullable<BGoodsPrice> = null
  // 总销量：商品初始销量+商品支付量。
  realSaleNum: Nullable<number> = null
  // 销售类型。类型包括：1-现货；2-预售；3-抽签购。
  soldType: Nullable<number> = null
  // 一级商品类型。类型包括：1-实物商品；2-虚拟商品。
  goodsType: Nullable<number> = null
  // 二级商品类型。类型包括：101-实物物流；102-海淘；201-虚拟无需配送；202-付费卷。
  subGoodsType: Nullable<number> = null
  // 商品是否多规格。true-多规格；false-单规格。
  isMultiSku: Nullable<boolean> = null
  // 商品是否上架。true-上架；false-下架。
  isOnline: Nullable<boolean> = null
  // 商品是否可售。true-可售；false-不可售。
  isCanSell: Nullable<boolean> = null
  // 商品排序值
  sort: Nullable<number> = null
  // 上下架时间。时间戳格式，单位毫秒。
  onlineTime: Nullable<number> = null
}
