/*
 * @Autor: 司浩
 * @Description:
 * @Date: 2023-02-08 18:45:22
 * @LastEditors: 司浩
 * @LastEditTime: 2023-02-08 18:45:36
 * @FilePath: \phoenix-web-ui\src\model\v2\coupon\videonumberActivity\BProductAttr.ts
 */
// 商品参数
export default class BProductAttr {
  // 类目必填项名称
  id: Nullable<string> = null
  // 类目必填项类型，string为自定义，select_one为多选一
  type: Nullable<string> = null
  // 类目必填项值,type=select_one时多个value以;分割
  name: Nullable<string> = null
  // 是否类目必填项
  is_required: Nullable<boolean> = null
}
