/*
 * @Author: 黎钰龙
 * @Date: 2025-05-23 13:54:46
 * @LastEditTime: 2025-05-23 17:27:37
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\template\MemberCodeDecorationModal.ts
 * 记得注释
 */
import bigWheelImageLink from '../BigWheelImageUrlLink'
import { BigWheelImageVO } from '../BigWheelImageVO'

export default class MemberCodeDecorationModal {
  id: string = 'memberPayCode'
  uuid: Nullable<string> = ""
  // 组件名称
  name: Nullable<string> = '会员码'
  // 背景图
  propBackgroundImage: Nullable<string> = bigWheelImageLink[BigWheelImageVO.bg_huiyuanma]
  // LOGO
  propLogo: Nullable<string> = bigWheelImageLink[BigWheelImageVO.default_avatar]
  // 其他支付方式 积分-point 储值-balance
  propOtherPayTypes: string[] = ['point', 'balance']
  // 倒计时展示
  propShowCountDown: Nullable<boolean> = false
  // 倒计时展示文案
  propCountDownText: Nullable<String> = '秒后自动更新，请在店内消费使用'
}