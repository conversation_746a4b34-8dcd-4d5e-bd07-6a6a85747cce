import BPickUpGoodsGroup from 'model/equityCard/default/BPickUpGoodsGroup'

export default class ExchangeGoodsCouponAttribute {
  // 是否限制商品范围
  limitGoods: Nullable<boolean> = null
  // 是否启用商品代码，启用商品代码后，商品判断代码取代条码
  enableGoodsCodes: Nullable<boolean> = null
  // 券后价
  afterUseAmount: Nullable<number> = null
  // 兑换商品组
  pickUpGoodsGroups: BPickUpGoodsGroup[] = []
  // 赠品商品组
  giftGoodsGroups: BPickUpGoodsGroup[] = []
}