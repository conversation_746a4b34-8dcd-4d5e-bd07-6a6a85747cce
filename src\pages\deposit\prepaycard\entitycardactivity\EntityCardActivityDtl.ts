import CardPicList from 'cmp/cardpiclist/CardPicList.vue'
import FormItem from 'cmp/formitem/FormItem.vue'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import GiftCardActivity from 'model/card/activity/GiftCardActivity'
import { Component, Vue } from 'vue-property-decorator'
import CardTplItem from '../cmp/cardtplitem/CardTplItem'
import DateUtil from 'util/DateUtil'
import SelectStoreActiveDtlDialog from 'pages/deposit/mbrdeposit/active/dialog/SelectStoreActiveDtlDialog.vue'
import DataUtil from '../common/DataUtil'
import ActivityState from "cmp/activitystate/ActivityState";
import I18nPage from "common/I18nDecorator";
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import ActiveStoreDtl from "cmp/activestoredtl/ActiveStoreDtl";
import RoutePermissionMgr from "mgr/RoutePermissionMgr";
import ActivePresentCardTable from 'cmp/active-present-card/ActivePresentCardTable'
import CommonUtil from 'util/CommonUtil'
import EntityCardActivityApi from 'http/card/activity/EntityCardActivityApi'
import DateTimeConditionDtl from "cmp/date-time-condition-picker/DateTimeConditionDtl";

@Component({
  name: 'EntityCardActivityDtl',
  components: {
    SubHeader,
    FormItem,
    CardPicList,
    CardTplItem,
    SelectStoreActiveDtlDialog,
    ActivityState,
    BreadCrume,
    ActiveStoreDtl,
    ActivePresentCardTable,
    DateTimeConditionDtl
  }
})
@I18nPage({
  prefix: [
    '/储值/预付卡/电子礼品卡活动/详情页面',
    '/公用/活动/状态',
    '/公用/活动/活动信息',
    '/公用/活动/提示信息',
    '/公用/按钮',
    '/储值/预付卡/电子礼品卡活动/编辑页面',
    '/卡/卡管理/售卡单',
    '/卡/卡活动/实体卡售卡活动'
  ],
})
export default class EntityCardActivityDtl extends Vue {
  i18n: (str: string, params?: string[]) => string
  $refs: any
  detail: GiftCardActivity = new GiftCardActivity()
  dataUtil: DataUtil = new DataUtil()
  loading = false
  panelArray: any = []

  created() {
    this.panelArray = [
      {
        name: this.i18n("实体卡售卡活动"),
        url: 'entity-card-activity'
      },
      {
        name: this.i18n('实体卡售卡活动详情'),
        url: ''
      }
    ]
    this.getDetail()
  }

  getDetail() {
    let activityId = this.$route.query.activityId as string
    this.loading = true
    EntityCardActivityApi.info(activityId).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.detail = resp.data
      } else {
        throw new Error(resp.msg)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    }).finally(() => {
      this.loading = false
    })
  }

  get activityTime() {
    if (this.detail.body) {
      return `${DateUtil.format(this.detail.body.beginDate, 'yyyy-MM-dd')}${this.i18n('至')}${DateUtil.format(this.detail.body.endDate, 'yyyy-MM-dd')}`
    }
    return '-'
  }

  audit(activityId: string) {
    this.$alert(this.i18n('确认要审核吗？'), this.i18n('提示'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消'),
      type: 'warning',
      callback: (action) => {
        if (action === 'confirm') {
          EntityCardActivityApi.audit(activityId).then((resp: any) => {
            if (resp && resp.code === 2000) {
              this.$message.success(this.i18n('审核成功'))
              this.getDetail()
            } else {
              throw new Error(resp.msg)
            }
          }).catch((error) => {
            if (error && error.message) {
              this.$message.error(error.message)
            }
          })
        }
      }
    });
  }

  stop(activityId: string) {
    this.$alert(this.i18n('确认要停止吗？'), this.i18n('提示'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消'),
      type: 'warning',
      callback: (action) => {
        if (action === 'confirm') {
          EntityCardActivityApi.stop(activityId).then((resp: any) => {
            if (resp && resp.code === 2000) {
              this.$message.success(this.i18n('停止成功'))
              this.getDetail()
            } else {
              throw new Error(resp.msg)
            }
          }).catch((error) => {
            if (error && error.message) {
              this.$message.error(error.message)
            }
          })
        }
      }
    });
  }

  del(activityId: string) {
    this.$alert(this.i18n('确认要删除吗？'), this.i18n('提示'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消'),
      type: 'warning',
      callback: (action) => {
        if (action === 'confirm') {
          EntityCardActivityApi.remove(activityId).then((resp: any) => {
            if (resp && resp.code === 2000) {
              this.$message.success(this.i18n('删除成功'))
              this.$router.push({ name: 'entity-card-activity' })
            } else {
              throw new Error(resp.msg)
            }
          }).catch((error) => {
            if (error && error.message) {
              this.$message.error(error.message)
            }
          })
        }
      }
    });
  }

  copy() {
    if (!this.detail.body) return
    this.$router.push({
      name: 'entity-card-activity-edit',
      query: { activityId: this.detail.body.activityId, editType: '复制' }
    })
  }

  edit() {
    if (!this.detail.body) {
      return
    }
    this.$router.push({
      name: 'entity-card-activity-edit',
      query: { activityId: this.detail.body.activityId, editType: '修改' }
    })
  }

  // 有效期
  validatyInfo(item: any) {
    if (!item.validityInfo) {
      return '-'
    }
    if (item.validityInfo.validityType === 'FIXED') {
      return DateUtil.format(item.validityInfo.endDate as Date, 'yyyy-MM-dd')
    }
    if (item.validityInfo.validityType === 'RALATIVE') {
      switch (item.cardTemplateType) {
        case 'GIFT_CARD':
          return this.formatI18n('/储值/预付卡/卡模板/列表页面/制卡后{0}内有效', null, [this.getValidNum(item.validityInfo)])
        case 'OFFLINE_GIFT_CARD':
          return this.formatI18n('/储值/预付卡/卡模板/列表页面/制卡后{0}内有效', null, [this.getValidNum(item.validityInfo)])
        case 'IMPREST_CARD':
          return this.formatI18n('/储值/预付卡/卡模板/列表页面/制卡后{0}内有效', null, [this.getValidNum(item.validityInfo)])
        case 'ONLINE_GIFT_CARD':
          return this.formatI18n('/储值/预付卡/卡模板/列表页面/激活后{0}内有效', null, [this.getValidNum(item.validityInfo)])
        case 'RECHARGEABLE_CARD':
          return this.formatI18n('/储值/预付卡/卡模板/列表页面/末次充值后{0}内有效', null, [this.getValidNum(item.validityInfo)])
      }
    }
  }
  getValidNum(validityInfo: any) {
    if (validityInfo.validityDays !== null) {
      return `${validityInfo.validityDays}${this.i18n('天')}`
    }
    if (validityInfo.validityYears !== null) {
      return `${validityInfo.validityYears}${this.i18n('年')}`
    }
  }

  getStateObj(state: string) {
    return CommonUtil.computeState(
      state,
      [
        ['INITAIL', this.formatI18n('/公用/过滤器/未审核'), 'orange'],
        ['UNSTART', this.formatI18n('/公用/过滤器/未开始'), 'blue'],
        ['PROCESSING', this.formatI18n('/公用/过滤器/进行中'), 'green'],
        ['STOPED', this.formatI18n('/公用/过滤器/已结束'), 'grey'],
        ['SUSPEND', this.formatI18n('/公用/过滤器/暂停中'), 'grey']
      ]
    )
  }
}
