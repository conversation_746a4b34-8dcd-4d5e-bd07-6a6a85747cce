/*
 * @Author: 黎钰龙
 * @Date: 2024-11-21 19:31:52
 * @LastEditTime: 2024-12-11 16:34:47
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\autoTag\TagCalculatorRule.ts
 * 记得注释
 */
import FirstAndLastTagRule from "./FirstAndLastTagRule"
import HierarchicalTagRule from "./HierarchicalTagRule"
import PreferenceTagRule from "./PreferenceTagRule"
import RmfTagRule from "./RmfTagRule"
import StatsTagRule from "./StatsTagRule"
import { TagCalculatorTaskType } from "./TagCalculatorTaskType"

export default class TagCalculatorRule {
  // 标签任务类型
  type: Nullable<TagCalculatorTaskType> = null
  // 首末次标签规则
  firstAndLastTagRule: Nullable<FirstAndLastTagRule> = null
  // 统计标签规则
  statsTagRule: Nullable<StatsTagRule> = null
  // 偏好类标签规则
  preferenceTagRule: Nullable<PreferenceTagRule> = null
  // RFM标签规则
  rfmTagRule: Nullable<RmfTagRule> = null
  // 分层标签
  hierarchicalTagRules: HierarchicalTagRule[] = []
}