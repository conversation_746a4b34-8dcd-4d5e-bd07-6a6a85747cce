/*
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:03
 * @LastEditTime: 2025-04-01 16:34:20
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\mbrdeposit\active\StoreValueActiveAdd.ts
 * 记得注释
 */
import { Component, Vue } from "vue-property-decorator";
import SubHeader from "cmp/subheader/SubHeader.vue";
import SelectStoreDialog from "pages/deposit/mbrdeposit/active/dialog/SelectStoreDialog.vue";
import ImportDialog from "cmp/importdialog/ImportDialog.vue";
import FormItem from "cmp/formitem/FormItem.vue";
import CouponTemplateDialog from "pages/deposit/mbrdeposit/active/dialog/CouponTemplateDialog.vue";
import ConstantMgr from "mgr/ConstantMgr";
import PrePayConfigApi from "http/prepay/config/PrePayConfigApi";
import DepositActivityApi from "http/deposit/activity/DepositActivityApi";
import DepositActivity from "model/deposit/activity/DepositActivity";
import ActivityBody from "model/common/ActivityBody";
import Channel from "model/common/Channel";
import StoreRange from "model/common/StoreRange";
import DepositRuleLine from "model/deposit/activity/DepositRuleLine";
import GiftInfo from "model/common/GiftInfo";
import ActiveStore from "cmp/activestore/ActiveStore.vue";
import CouponItem from "model/common/CouponItem";
import BreadCrume from "cmp/bread-crumb/BreadCrume.vue";
import ImportResultDialog from "pages/deposit/mbrdeposit/active/dialog/ImportResultDialog.vue";
import DateUtil from "util/DateUtil";
import I18nPage from "common/I18nDecorator";
import ExportConfirm from "cmp/exportconfirm/ExportConfirm";
import CouponTemplateSelectorDialog from "cmp/selectordialogs/CouponTemplateSelectorDialog.vue";
import CouponTemplateFilter from "model/coupon/template/CouponTemplateFilter";
import CouponTemplate from "model/coupon/template/CouponTemplate";
import CouponInfo from "model/common/CouponInfo";
import CouponTemplateApi from "http/coupon/template/CouponTemplateApi";
import SelectStoreActiveDtlDialog from "pages/deposit/mbrdeposit/active/dialog/SelectStoreActiveDtlDialog.vue";
import ChannelSelect from "cmp/channelselect/ChannelSelect";
import LimitedMarketingCenter from "model/common/LimitedMarketingCenter";
import GradesRange from "model/common/GradeRange";
import SelectGrade from "cmp/selectGrade/SelectGrade";
import EnvUtil from "util/EnvUtil";
import CardPicList from "cmp/cardpiclist/CardPicList";
import MarketingBudgetEdit from "cmp/MarketingBudget/MarketingBudgetEdit";
import MarketBudget from "model/promotion/MarketBudget";
import CommonUtil from "util/CommonUtil";
import ActivityMgr from "mgr/ActivityMgr";
import { MarketBudgetActivityEnum } from "model/promotion/MarketBudgetActivityEnum";
import EditType from "common/EditType";
import ActivityDateTimeConditionPicker from "cmp/date-time-condition-picker/ActivityDateTimeConditionPicker";
import ActivityDateTimeCondition from "model/common/ActivityDateTimeCondition";
import SelectBenefitCardDialog from './dialog/SelectBenefitCardDialog.vue'
import GiftInfoBenefitCard from "model/equityCard/default/GiftInfoBenefitCard";
@Component({
  name: "StoreValueActiveAdd",
  components: {
    SubHeader,
    SelectStoreDialog,
    ImportDialog,
    FormItem,
    CouponTemplateDialog,
    ImportResultDialog,
    BreadCrume,
    ActiveStore,
    ExportConfirm,
    CouponTemplateSelectorDialog,
    SelectStoreActiveDtlDialog,
    ChannelSelect,
    SelectGrade,
    CardPicList,
    MarketingBudgetEdit,
    ActivityDateTimeConditionPicker,
    SelectBenefitCardDialog
  },
})
@I18nPage({
  prefix: ["/储值/会员储值/储值充值活动/编辑页面", "/公用/按钮", "/公用/菜单"],
})
export default class StoreValueActiveAdd extends Vue {
  child: CouponItem = new CouponItem();
  exportDialogShow = false;
  couponDialogShow = false;
  cardTemplateFilter: CouponTemplateFilter = new CouponTemplateFilter();
  i18n: any;
  panelArray: any = [];
  importResultData: any = {};
  importResultDialogClose = false;
  title = "";
  couponInfo = {
    from: "add",
    parentIndex: 0,
    childIndex: 0,
  };
  templateData: any = "";
  dialogShow = false;
  importDialogShow = false;
  couponTemplateDialogShow = false;
  selectedStores: any[] = [];
  importUrl = "v1/org/importExcel"; // 文件上传地址
  faceAmount: number[] = [];
  faceAmountFlag: boolean[] = [];
  selecteArray: any[] = [];
  score: any[] = [];
  receiveParamsArray: any[] = [];
  receiveNameArray: any[] = [];
  saleAmountArray: any[] = [];
  couponIndex = 0;
  $refs: any;
  ruleForm: any = {
    gradeRange: new GradesRange(),
  };
  rules: any = {};
  warnMsg = "";
  depositRemarks: any[] = [];
  uploadHeaders: any = {}
  pictureUrls: any[] = []
  linePictureUrl: any[] = []
  lineCornerImageUrl: Nullable<string>[] = []
  budget: Nullable<MarketBudget> = null
  disabled: boolean = false;
  // 充值赠礼选择的
  paidBenefitCards:  Array<Nullable<GiftInfoBenefitCard>[]> = []

  get isOaActivity() {
    return ActivityMgr.isOaActivity(MarketBudgetActivityEnum.PrepayDepositActivityRule)
  }

  get dateRangeOption() {
    return {
      disabledDate(time: any) {
        return time.getTime() < DateUtil.nowDayTime();
      },
    };
  }

  get templatePath() {
    if (location.href.indexOf("localhost") === -1) {
      return "template_specify_stores.xlsx";
    } else {
      return "template_specify_stores.xlsx";
    }
  }

  get uploadUrl() {
    return EnvUtil.getServiceUrl() + 'v1/upload/upload'
  }

  created() {
    this.initUploadHeaders()
    this.panelArray = [
      {
        name: this.i18n("充值有礼"),
        url: "store-value-active-list",
      },
      {
        name: this.i18n("新建储值充值活动"),
        url: "",
      },
    ];
    this.ruleForm = {
      name: "",
      remark: "",
      // theme: '',
      //date: [new Date(), new Date()],
      useHdPosChannel: true,
      resource: [],
      range: {},
      state: '',
      activityDateTimeCondition:new ActivityDateTimeCondition(),
    };
    this.rules = {
      name: [
        { required: true, message: this.i18n("请输入活动名称"), trigger: "blur" },
        { min: 1, max: 80, message: this.i18n("长度在80个字符以内"), trigger: "blur" },
      ],
      resource: [
        {
          required: true,
          validator: (rule: any, value: any, callback: any) => {
            if (!this.ruleForm.useHdPosChannel && (!this.ruleForm.resource || this.ruleForm.resource.length === 0)) {
              callback(this.i18n("请选择活动渠道"));
            }
            callback();
          },
          trigger: "blur",
        },
      ],
    };
    if (this.$route.query.id) {
      // 编辑或者复制
      const loading = CommonUtil.Loading()
      DepositActivityApi.info(this.$route.query.id as string)
        .then((resp: any) => {
          if (resp && resp.code === 2000) {
            console.log("---------1" + this.couponInfo.from);
            this.disabled = ['UNSTART', 'PROCESSING'].indexOf(resp.data.body.state) > -1 && this.$route.query.from === "edit"
            console.log("---------2" + this.disabled);
            this.ruleForm.name = resp.data.body.name;
            this.ruleForm.remark = resp.data.body.remark;
            this.ruleForm.activityDateTimeCondition.dateTimeCondition = resp.data.dateTimeCondition;
            this.ruleForm.activityDateTimeCondition.beginDate = resp.data.body.beginDate;
            this.ruleForm.activityDateTimeCondition.endDate = resp.data.body.endDate;
            let firstChannel = resp.data.body.channels[0];
            if (firstChannel.type === "store" && firstChannel.id === "-") {
              this.ruleForm.useHdPosChannel = true;
            } else {
              this.ruleForm.useHdPosChannel = false;
              this.ruleForm.resource = resp.data.body.channels;
            }
            console.log(this.ruleForm.resource);
            this.ruleForm.range = resp.data.body.stores;
            this.ruleForm.gradeRange = resp.data.body.gradeRange
            this.ruleForm.state = resp.data.body.state;
            this.pictureUrls = resp.data.body.pictureUrls
            if (resp.data.lines && resp.data.lines.length > 0) {
              resp.data.lines.forEach((item: any, index: number) => {
                this.faceAmount.push(item.faceAmount);
                this.saleAmountArray.push(item.price);
                this.selecteArray.push([false, false, false]);
                this.paidBenefitCards.push([])
                this.score.push(["", "", ""]);
                this.depositRemarks.push(item.depositRemark);
                this.linePictureUrl.push(item.pictureUrl)
                this.lineCornerImageUrl.push(item.cornerImageUrl)
                if (index === 0) {
                  this.faceAmountFlag[index] = true;
                } else {
                  this.faceAmountFlag[index] = true;
                }
                // todo 这里需要处理
                // 如果赠送积分
                if (item.gift.points) {
                  this.selecteArray[index][0] = true;
                  this.score[index][0] = item.gift.points;
                }
                // 如果赠送返现
                if (item.gift.rebateAmount) {
                  this.selecteArray[index][1] = true;
                  this.score[index][1] = item.gift.rebateAmount;
                }
                // 如果赠送优惠券
                if (item.gift.couponItems && item.gift.couponItems.length > 0) {
                  item.gift.couponItems.forEach((subItem: any, subIndex: number) => {
                    this.score[index][subIndex + 2] = subItem.qty;
                    this.selecteArray[index][subIndex + 2] = true;
                  });
                }

                // 如果赠送付费会员卡
                if(item.gift.paidBenefitCards && item.gift.paidBenefitCards.length>0){
                  this.paidBenefitCards[index] = item.gift.paidBenefitCards
                  this.selecteArray[index][3] = true
                }
                // this.score.push([item.gift.points, item.gift.rebateAmount, 1])
                // 初始化券
                // if (item.gift && item.gift.couponItems && item.gift.couponItems.length > 0) {
                //   item.gift.couponItems.forEach((subItem: any, subIndex: number) => {
                //     this.receiveParamsArray[index] = subItem
                //   })
                // }
                if (item.gift.couponItems.length > 0) {
                  this.receiveParamsArray[index] = item.gift.couponItems;
                } else {
                  // 处理没有添加面额的情况
                  // 初始化券
                  this.receiveParamsArray[index] = [];
                  this.receiveParamsArray[index].push("");
                  this.receiveNameArray[index] = [];
                  // this.saleAmountArray.push(item.faceAmount)
                }

                // this.receiveParamsArray[index] = []
                // this.receiveParamsArray[index].push('')
                if (item.gift && item.gift.couponItems && item.gift.couponItems.length > 0) {
                  this.receiveNameArray[index] = [];
                  item.gift.couponItems.forEach((subItem: any, subIndex: number) => {
                    // let couponStr = ''
                    // if (subItem.coupons.couponBasicType === 'all_cash') {
                    //   let str: any = this.formatI18n('/会员/等级/等级管理/已初始化状态的免费等级/表格/等级权益/等级月礼/有全场现金券赠送', '{0}元全场现金券')
                    //   str = str.replace(/\{0\}/g, Number(subItem.coupons.cashCouponAttribute.faceAmount))
                    //   couponStr = str
                    // } else if (subItem.coupons.couponBasicType === 'goods_cash') {
                    //   let str: any = this.formatI18n('/会员/等级/等级管理/已初始化状态的免费等级/表格/等级权益/等级月礼/有商品现金券赠送', '{0}元商品现金券')
                    //   str = str.replace(/\{0\}/g, Number(subItem.coupons.cashCouponAttribute.faceAmount))
                    //   couponStr = str
                    // } else if (subItem.coupons.couponBasicType === 'all_discount') {
                    //   let str: any = this.formatI18n('/会员/等级/等级管理/已初始化状态的免费等级/表格/等级权益/等级月礼/有全场折扣券赠送', '{0}折全场折扣券')
                    //   str = str.replace(/\{0\}/g, Number(subItem.coupons.discountCouponAttribute.discount))
                    //   couponStr = str
                    // } else if (subItem.coupons.couponBasicType === 'goods_discount') {
                    //   let str: any = this.formatI18n('/会员/等级/等级管理/已初始化状态的免费等级/表格/等级权益/等级月礼/有单品折扣券赠送', '{0}折单品折扣券')
                    //   str = str.replace(/\{0\}/g, Number(subItem.coupons.discountCouponAttribute.discount))
                    //   couponStr = str
                    // } else if (subItem.coupons.couponBasicType === 'rfm_type') {
                    //   let str: any = this.formatI18n('/会员/等级/等级管理/已初始化状态的免费等级/表格/等级权益/等级月礼/有商品折扣券赠送', '{0}折商品折扣券')
                    //   str = str.replace(/\{0\}/g, Number(subItem.coupons.discountCouponAttribute.discount))
                    //   couponStr = str
                    // } else {
                    //   couponStr = subItem.coupons.name
                    // }
                    let couponStr = subItem.coupons.name;
                    this.receiveNameArray[index].push(couponStr);
                  });
                }
              });
            }
            this.budget = resp.data.body.budget
          }
        })
        .catch((error) => {
          if (error && error.message) {
            this.$message.error(error.message);
          }
        }).finally(()=>{
          loading.close()
        })
    } else {
      // 新建
      this.getFaceAmount();
    }
  }

  mounted() {
    if (this.$route.query.from === "edit") {
      this.title = this.i18n("编辑储值充值活动");
      this.panelArray[1].name = this.i18n("编辑储值充值活动");
    } else if (this.$route.query.from === "copy") {
      this.title = this.i18n("新建储值充值活动");
      this.panelArray[1].name = this.i18n("新建储值充值活动");
    } else {
      this.title = this.i18n("新建储值充值活动");
      this.panelArray[1].name = this.i18n("新建储值充值活动");
    }
  }

  private initUploadHeaders() {
    let locale = sessionStorage.getItem('locale')
    this.uploadHeaders = {
      locale: locale ? locale : "zh_CN",
      time_zone: new Date().getTimezoneOffset(),
      marketingCenter: sessionStorage.getItem("marketCenter"),
    };
    const authorization = EnvUtil.getUniAuthorization()
    if (authorization) {
      this.uploadHeaders.authorization = authorization
    }
  }

  doCouponDialogClose() {
    this.couponDialogShow = false;
  }

  doConfirmSummit(flag: any) {
    if (flag) {
      const loading = this.$loading(ConstantMgr.loadingOption);
      let params = this.setParams();
      DepositActivityApi.saveAndAudit(params)
        .then((resp: any) => {
          if (resp && resp.code === 2000) {
            this.$message.success(this.i18n("保存并审核成功"));
            this.$router.push({
              name: "store-value-active-dtl",
              query: {
                id: resp.data,
              },
            });
            loading.close();
          }
        })
        .catch((error) => {
          loading.close();
          this.$message.error(error.message);
        });
    }
  }

  doExportDialogClose() {
    this.exportDialogShow = false;
  }

  doStoreChange() {
    if (this.$refs["ruleForm"]) {
      this.$refs["ruleForm"].validateField("range");
    }
  }

  doCancel() {
    this.$router.back();
  }

  doImport() {
    this.importDialogShow = true;
  }

  doDialogClose() {
    this.dialogShow = false;
  }

  doImportDialogClose() {
    this.importDialogShow = false;
  }

  doCouponTemplateDialogClose() {
    this.couponTemplateDialogShow = false;
  }

  doCouponTemplateSummit(val: any) {
    // todo
  }

  doAddCoupon(index: number, subIndex: number) {
    if (subIndex === undefined) {
      subIndex = 2;
    } else {
      subIndex = subIndex + 3;
    }
    this.couponInfo = {
      from: "add",
      parentIndex: index,
      childIndex: subIndex,
    };
    this.couponIndex = index;
    // todo
    let arr: CouponTemplate[] = [];
    if (this.receiveParamsArray[this.couponIndex] && this.receiveParamsArray[this.couponIndex].length > 0) {
      this.receiveParamsArray[this.couponIndex].forEach((item: CouponItem) => {
        if (item) {
          let obj: CouponTemplate = new CouponTemplate();
          obj.number = item.coupons!.templateId;
          obj.name = item.coupons!.name;
          arr.push(obj);
        }
      });
    }
    this.$refs.couponTemplate.open(arr, "multiple");
  }

  doUploadSuccess(response: any) {
    // todo
    if (response.response.code === 2000) {
      this.selectedStores = response.response.data.orgs;
      this.importResultDialogClose = true;
      this.importResultData = {
        importResult: response.response.data.success, // 导入结果
        backUrl: response.response.data.backUrl,
        errorCount: response.response.data.errorCount,
        ignoreCount: response.response.data.ignoreCount,
        successCount: response.response.data.successCount,
      };
    } else {
      this.$message.error(response.response.msg);
    }
  }

  doImportResultDialogClose() {
    this.importResultDialogClose = false;
  }

  doSummit(arr: any) {
    let copyArr: any = [];
    if (arr && arr.length > 0) {
      arr.forEach((item: any) => {
        copyArr.push(item.org);
      });
    }
    this.selectedStores = copyArr;
  }

  /**
   * 选择门店
   */
  doSelectStore() {
    this.dialogShow = true;
  }

  setParams() {
    let params: DepositActivity = new DepositActivity(); // 活动主题
    params.body = new ActivityBody();
    params.body.name = this.ruleForm.name; // 名称
    params.body.remark = this.ruleForm.remark; // 名称
    params.dateTimeCondition = this.ruleForm.activityDateTimeCondition.dateTimeCondition
    params.body.beginDate = this.ruleForm.activityDateTimeCondition.beginDate
    params.body.endDate = this.ruleForm.activityDateTimeCondition.endDate
    if (this.ruleForm.useHdPosChannel) {
      let channel = new Channel();
      channel.id = "-";
      channel.type = "store";
      params.body.channels = [channel];
      params.body.stores = new StoreRange();
      params.body.stores = this.ruleForm.range as any;
      console.log(params.body.stores);
    } else {
      // 默认参数
      const store: StoreRange = new StoreRange();
      const mc: LimitedMarketingCenter = new LimitedMarketingCenter();
      mc.stores = new StoreRange();
      mc.marketingCenter = {
        id: sessionStorage.getItem("marketCenter"),
        name: sessionStorage.getItem("marketCenterName"),
      };
      const arr: LimitedMarketingCenter[] = [];
      mc.stores.storeRangeType = "ALL";
      arr.push(mc);
      store.marketingCenters = arr;
      store.storeRangeLimitType = 'MARKETING_CENTER'
      console.log(store);
      params.body.stores = store;
      params.body.channels = this.ruleForm.resource;
    }
    params.body.pictureUrls = this.pictureUrls
    params.lines = []; // 活动规则
    if (this.faceAmount && this.faceAmount.length > 0) {
      this.faceAmount.forEach((item: number, index: number) => {
        let line: DepositRuleLine = new DepositRuleLine();
        line.faceAmount = this.faceAmount[index];
        if (this.ruleForm.resource === "门店") {
          line.price = this.faceAmount[index];
        } else {
          line.price = this.saleAmountArray[index];
        }
        line.gift = new GiftInfo();
        line.depositRemark = this.depositRemarks[index];
        line.pictureUrl = this.linePictureUrl[index]
        line.cornerImageUrl = this.lineCornerImageUrl[index]
        params.lines.push(line);
      });
    }
    // 处理赠送积分、赠送返现、赠优惠券
    if (this.selecteArray && this.selecteArray.length > 0) {
      for (let i = 0; i < this.selecteArray.length; i++) {
        for (let j = 0; j < this.selecteArray[i].length; j++) {
          if (this.selecteArray[i][j]) {
            if (j === 0) {
              params.lines[i].gift!.points = this.score[i][0];
            } else if (j === 1) {
              params.lines[i].gift!.rebateAmount = this.score[i][1];
            } else {
              // 赠送优惠券
              if (this.receiveParamsArray && this.receiveParamsArray[i] && this.receiveParamsArray[i].length > 0) {
                this.receiveParamsArray[i].forEach((item: any, position: number) => {
                  if (item) {
                    item.qty = this.score[i][position + 2];
                  }
                  // 处理与活动门店一致
                  if (item && item.coupons && item.coupons.useStores && item.coupons.useStores.storeRangeType === "SAME") {
                    item.coupons.useStores = this.ruleForm.range;
                  }
                });
              }
              params.lines[i].gift!.couponItems = this.receiveParamsArray[i]?.filter((item: any) => item) || [];
            }
          }
          if(this.paidBenefitCards[i]){
            if(this.paidBenefitCards[i].length>0){
              params.lines[i].gift!.paidBenefitCards = this.paidBenefitCards[i] as any
            }else {
              params.lines[i].gift!.paidBenefitCards = []
            }
          }
        }
      }
    }
    params.body.gradeRange = this.ruleForm.gradeRange
    params.body.budget = this.budget
    return params;
  }

  /**
   * 保存并审核
   */
  doSaveAndAudit() {
    let arr = [this.$refs["ruleForm"].validate(), this.$refs.activityDateTimeConditionPicker.validate(),this.$refs.selectGrade.validate()];
    if (this.$refs.activeStore) {
      arr.push(this.$refs.activeStore.validate());
    }
    if (this.$refs.marketingBudget) {
      arr.push(this.$refs.marketingBudget.doValidate())
    }
    Promise.all(arr).then(async (valid: any) => {
      if (valid) {
        let errInfo = await this.doValidate()
        if (errInfo) {
          this.$message.error(this.formatI18n("/储值/会员储值/储值充值活动/编辑页面", errInfo))
          return
        }
        const loading = this.$loading(ConstantMgr.loadingOption);
        let params = this.setParams();
        DepositActivityApi.checkConflict(params)
          .then((resp: any) => {
            if (resp && resp.code === 2000) {
              if (resp.data) {
                // 有冲突
                this.warnMsg = resp.data;
                this.exportDialogShow = true;
              } else {
                // 无冲突
                DepositActivityApi.saveAndAudit(params)
                  .then((resp: any) => {
                    if (resp && resp.code === 2000) {
                      this.$message.success(this.i18n("保存并审核成功"));
                      this.$router.push({
                        name: "store-value-active-dtl",
                        query: {
                          id: resp.data,
                        },
                      });
                    } else {
                      this.$message.error(resp.msg)
                    }
                  })
                  .catch((error) => {
                    this.$message.error(error.message);
                  }).finally(() => {
                    loading.close();
                  })
              }
            }
          })
          .catch((error) => {
            loading.close();
            this.$message.error(error.message);
          });
      }
    });
  }

  doScoreChange(index: number, position: number) {
    if (position === 1) {
      let regex = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/g;
      if (regex.test(this.score[index][position])) {
        if (Number(this.score[index][position]) < 0.01) {
          this.score[index][position] = 0.01;
          this.$forceUpdate();
        }
        if (Number(this.score[index][position]) > 999999) {
          this.score[index][position] = 999999;
          this.$forceUpdate();
        }
      } else {
        this.score[index][position] = 0.01;
        this.$forceUpdate();
      }
    } else {
      let regex = /^[1-9]\d*$/g;
      if (regex.test(this.score[index][position])) {
        if (Number(this.score[index][position]) < 1) {
          this.score[index][position] = 1;
          this.$forceUpdate();
        }
        if (Number(this.score[index][position]) > 999999) {
          this.score[index][position] = 999999;
          this.$forceUpdate();
        }
      } else {
        this.score[index][position] = 1;
        this.$forceUpdate();
      }
    }
  }

  doToggle(index: number) {
    this.$set(this.faceAmountFlag, index, !this.faceAmountFlag[index]);
  }

  doReceiveParams(params: CouponItem, name: string, couponInfo: any) {
    if (couponInfo.from === "add") {
      this.receiveNameArray[this.couponIndex].push(name);
      if (this.receiveParamsArray && this.receiveParamsArray[this.couponIndex]) {
        for (let i = 0; i < this.receiveParamsArray[this.couponIndex].length; i++) {
          if (!this.receiveParamsArray[this.couponIndex][i]) {
            this.receiveParamsArray[this.couponIndex].splice(i, 1);
          }
        }
        this.receiveParamsArray[this.couponIndex].push(params);
        this.score[this.couponIndex][couponInfo.childIndex] = 1;
      }
    } else if (couponInfo.from === "edit") {
      this.receiveNameArray[couponInfo.parentIndex][couponInfo.childIndex] = name;
      this.receiveParamsArray[couponInfo.parentIndex][couponInfo.childIndex] = params;
    } else {
      // 复制
      // todo
    }
  }

  doEditStoreValueActive(index: number, subIndex: number) {
    this.templateData = this.receiveParamsArray[index][subIndex];
    CouponTemplateApi.detail(this.templateData.coupons.templateId as string)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.child.coupons = resp.data;
          this.couponDialogShow = true;
        }
      })
      .catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
  }

  doDeleteCoupon(index: number, subIndex: number) {
    this.$nextTick(() => {
      this.receiveParamsArray[index].splice(subIndex, 1);
      this.receiveNameArray[index].splice(subIndex, 1);
      if (subIndex === 0) {
        this.receiveParamsArray[index].push("");
      }
      this.$forceUpdate();
    })
  }

  doCardTemplateSelected(arr: CouponTemplate[]) {
    if (arr && arr.length > 0) {
      this.receiveParamsArray[this.couponIndex] = [];
      this.receiveNameArray[this.couponIndex] = [];
      arr.forEach((item: CouponTemplate, itemIndex: number) => {
        let couponItem: CouponItem = new CouponItem();
        couponItem.coupons = new CouponInfo();
        couponItem.coupons.name = item.name;
        couponItem.coupons.templateId = item.number;
        if (!this.score[this.couponIndex][this.couponInfo.childIndex + itemIndex]) {
          this.score[this.couponIndex][this.couponInfo.childIndex + itemIndex] = 1;
        }
        // couponItem.qty = 1
        this.receiveNameArray[this.couponIndex].push(item.name);
        if (this.receiveParamsArray && this.receiveParamsArray[this.couponIndex]) {
          for (let i = 0; i < this.receiveParamsArray[this.couponIndex].length; i++) {
            if (!this.receiveParamsArray[this.couponIndex][i]) {
              this.receiveParamsArray[this.couponIndex].splice(i, 1);
            }
          }
          this.receiveParamsArray[this.couponIndex].push(couponItem);
          // this.score[this.couponIndex][this.couponInfo.childIndex] = 1
        }
      });
      this.$forceUpdate();
      // this.$emit('input', this.receiveParamsArray)
    }
  }

  /**
   * 保存
   */
  doSave() {
    let arr = [
    this.$refs.activityDateTimeConditionPicker.validate(),
    this.$refs["ruleForm"].validate(), 
    this.$refs.selectGrade.validate()];
    if (this.$refs.activeStore) {
      arr.push(this.$refs.activeStore.validate());
    }
    if (this.$refs.marketingBudget) {
      arr.push(this.$refs.marketingBudget.doValidate())
    }
    Promise.all(arr).then(async (valid: any) => {
      if (valid) {
        let errInfo = await this.doValidate()
        if (errInfo) {
          this.$message.error(this.formatI18n("/储值/会员储值/储值充值活动/编辑页面", errInfo))
          return
        }
        const loading = this.$loading(ConstantMgr.loadingOption);
        let params: DepositActivity = new DepositActivity(); // 活动主题
        params.body = new ActivityBody();
        params.body.name = this.ruleForm.name; // 名称
        params.body.remark = this.ruleForm.remark; // 名称
        if (this.$route.query.id) {
          params.body.activityId = this.$route.query.id as any;
        }
        params.body.beginDate = this.ruleForm.activityDateTimeCondition.beginDate
        params.body.endDate = this.ruleForm.activityDateTimeCondition.endDate
        params.dateTimeCondition = this.ruleForm.activityDateTimeCondition.dateTimeCondition
        if (this.ruleForm.useHdPosChannel) {
          params.body.stores = this.ruleForm.range as any;
          let channel = new Channel();
          channel.id = "-";
          channel.type = "store";
          params.body.channels[0] = channel;
        } else {
          // 默认参数
          const store: StoreRange = new StoreRange();
          const mc: LimitedMarketingCenter = new LimitedMarketingCenter();
          mc.stores = new StoreRange();
          mc.marketingCenter = {
            id: sessionStorage.getItem("marketCenter"),
            name: sessionStorage.getItem("marketCenterName"),
          };
          const arr: LimitedMarketingCenter[] = [];
          mc.stores.storeRangeType = "ALL";
          arr.push(mc);
          store.marketingCenters = arr;
          store.storeRangeLimitType = 'MARKETING_CENTER'
          console.log(store);
          params.body.stores = store;
          params.body.channels = this.ruleForm.resource;
        }
        params.body.pictureUrls = this.pictureUrls
        params.lines = []; // 活动规则range
        if (this.faceAmount && this.faceAmount.length > 0) {
          this.faceAmount.forEach((item: number, index: number) => {
            let line: DepositRuleLine = new DepositRuleLine();
            line.faceAmount = this.faceAmount[index];
            if (this.ruleForm.resource === "门店") {
              line.price = this.faceAmount[index];
            } else {
              line.price = this.saleAmountArray[index];
            }
            line.gift = new GiftInfo();
            line.depositRemark = this.depositRemarks[index];
            line.pictureUrl = this.linePictureUrl[index]
            line.cornerImageUrl = this.lineCornerImageUrl[index]
            params.lines.push(line);
          });
        }
        // 处理赠送积分、赠送返现、赠优惠券
        if (this.selecteArray && this.selecteArray.length > 0) {
          for (let i = 0; i < this.selecteArray.length; i++) {
            for (let j = 0; j < this.selecteArray[i].length; j++) {
              if (this.selecteArray[i][j]) {
                if (j === 0) {
                  params.lines[i].gift!.points = this.score[i][0];
                } else if (j === 1) {
                  params.lines[i].gift!.rebateAmount = this.score[i][1];
                } else {
                  // 赠送优惠券
                  if (this.receiveParamsArray && this.receiveParamsArray[i] && this.receiveParamsArray[i].length > 0) {
                    this.receiveParamsArray[i].forEach((item: any, position: number) => {
                      if (item) {
                        item.qty = this.score[i][position + 2];
                      }
                      // 处理与活动门店一致
                      if (item && item.coupons && item.coupons.useStores && item.coupons.useStores.storeRangeType === "SAME") {
                        item.coupons.useStores = this.ruleForm.range;
                      }
                    })
                  }
                  params.lines[i].gift!.couponItems = this.receiveParamsArray[i]?.filter((item: any) => item) || [];
                }
              }
            }
            if(this.paidBenefitCards[i]){
              if(this.paidBenefitCards[i].length>0){
                params.lines[i].gift!.paidBenefitCards = this.paidBenefitCards[i] as any
              }else {
                params.lines[i].gift!.paidBenefitCards = []
              }
            }
          }
        }
        params.body.gradeRange = this.ruleForm.gradeRange
        params.body.budget = this.budget
        if (this.$route.query.from === "edit") {
          DepositActivityApi.modify(params)
            .then((resp: any) => {
              if (resp && resp.code === 2000) {
                this.$message.success(this.i18n("编辑成功"));
                this.$router.push({ name: "store-value-active-dtl", query: { id: resp.data } });
              } else {
                this.$message.error(resp.msg)
              }
            })
            .catch((error) => {
              this.$message.error(error.message);
            }).finally(() => {
              loading.close();
            })
        } else {
          DepositActivityApi.create(params)
            .then((resp: any) => {
              if (resp && resp.code === 2000) {
                this.$message.success(this.i18n("保存成功"));
                this.$router.push({
                  name: "store-value-active-dtl",
                  query: {
                    id: resp.data,
                  },
                });
              } else {
                this.$message.error(resp.msg)
              }
            })
            .catch((error) => {
              this.$message.error(error.message);
            }).finally(() => {
              loading.close();
            })
        }
      }
    });
  }

  private getFaceAmount() {
    const loading = this.$loading(ConstantMgr.loadingOption);
    PrePayConfigApi.getFaceAmounts()
      .then((resp: any) => {
        if (resp && resp.data) {
          loading.close();
          this.faceAmount = resp.data;
          if (this.faceAmount && this.faceAmount.length > 0) {
            this.faceAmount.forEach((item: number, index: number) => {
              this.depositRemarks.push(null);
              if (index === 0) {
                this.faceAmountFlag[index] = true;
              } else {
                this.faceAmountFlag[index] = true;
              }
              this.selecteArray.push([false, false, false, false]);
              this.paidBenefitCards.push([]);
              this.score.push(["", "", ""]);
              // 初始化券
              this.receiveParamsArray[index] = [];
              this.receiveParamsArray[index].push("");
              this.receiveNameArray[index] = [];
              this.saleAmountArray.push(item);
            });
          }
        }
      })
      .catch((error) => {
        loading.close();
        this.$message.error(error.message);
      });
  }

  private changeChannelType() {
    if (this.ruleForm.useHdPosChannel) {
      this.ruleForm.resource = [];
      this.saleAmountArray = JSON.parse(JSON.stringify(this.faceAmount));
    } else {
      this.ruleForm.range = {

      }
    }
    this.$refs["ruleForm"].validateField("resource");
  }

  canEditRemark() {
    if (
      this.ruleForm.state === "" ||
      this.ruleForm.state === "INITAIL" ||
      this.ruleForm.state === "UNSTART" ||
      this.ruleForm.state === "PROCESSING"
    ) {
      return true;
    } else {
      return false
    }
  }
  private gradeChange(e: GradesRange) {
    this.ruleForm.gradeRange = e;
  }
  private beforeAvatarUpload(file: any) {
    const isJPG = ['image/jpeg', 'image/png'].indexOf(file.type) > -1;
    const isLt2M = file.size / 1024 < 300;

    if (!isJPG) {
      this.$message.error(this.formatI18n('/储值/预付卡/卡模板/编辑页面/上传图片只能是JPG/PNG/JPEG格式!'));
      return false
    }
    if (!isLt2M) {
      this.$message.error(this.formatI18n('/储值/预付卡/卡模板/编辑页面/上传图片大小不能超过300KB'));
      return false
    }
    return true
  }
  private onUploadSuccess(response: any, file: any, fileList: any) {
    if (response && response.code === 2000) {
      this.$message.success(this.formatI18n('/储值/预付卡/卡模板/编辑页面/上传成功'))
      this.pictureUrls.push(response.data.url)
      this.$refs.detail.validateField('cardPictureUrls')
    } else {
      this.$message.error(response.msg)
    }
  }

  private onLineUploadSuccess(response: any, file: any, fileList: any, index: number) {
    if (response && response.code === 2000) {
      this.$message.success(this.formatI18n('/储值/预付卡/卡模板/编辑页面/上传成功'))
      this.linePictureUrl[index] = response.data.url
      this.$forceUpdate()
      this.$refs.detail.validateField('cardPictureUrls')
    } else {
      this.$message.error(response.msg)
    }
  }

  private onLineCornerUploadSuccess(response: any, file: any, fileList: any, index: number) {
    if (response && response.code === 2000) {
      this.$message.success(this.formatI18n('/储值/预付卡/卡模板/编辑页面/上传成功'))
      this.lineCornerImageUrl[index] = response.data.url
      this.$forceUpdate()
    } else {
      this.$message.error(response.msg)
    }
  }

  onRemove(index: any) {
    console.log(this.linePictureUrl, this.linePictureUrl[index])
    this.linePictureUrl[index] = null
    this.$forceUpdate()
  }

  onCornerRemove(index: any) {
    console.log(this.lineCornerImageUrl, this.lineCornerImageUrl[index])
    this.lineCornerImageUrl[index] = null
    this.$forceUpdate()
  }

  //取消勾选赠券时，要清空已选择的券
  doSendCouponChange(index: number, value: boolean) {
    if (!value) {
      this.$set(this.receiveParamsArray, index, [''])
      this.$set(this.receiveNameArray, index, [])
      this.score[index].splice(2, this.score.length - 2, '')
    }
  }

  doValidate() {
    return new Promise((resolve, reject) => {
      this.faceAmount.forEach((item, index) => {
        let selecteArray = this.selecteArray[index]
        selecteArray.forEach((selecte: boolean, selecteIndex: number) => {
          if (selecte) {
            if (selecteIndex == 0 || selecteIndex == 1) {
              if (!this.score[index][selecteIndex]) {
                let str = selecteIndex == 0 ? '请填写积分' : '请填写返现'
                resolve(str)
              }
            }
            //  else {
            // 	this.receiveParamsArray[index].forEach((receiveParams: any) => {
            // 		if (!receiveParams) {
            // 			resolve('请添加券')
            // 		}
            // 	})
            // }
          }
        })
      })
      resolve('')
    })
  }


  doSaleAmountChange(index: number) {
    // 如果面额不等于售价，则不允许赠送优惠
    if (this.saleAmountArray[index] != this.faceAmount[index]) {
      this.$set(this.selecteArray[index], 1, false)
    }
  }
  
  openBenefitCard(index: number) {
    this.$refs.selectBenefitCardDialogRef?.open(index, this.paidBenefitCards)
  }

  deletePaidBenefitCards(index: number){
    this.paidBenefitCards.splice(index, 1, [])
  }
}
