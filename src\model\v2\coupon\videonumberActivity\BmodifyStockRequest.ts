/*
 * @Autor: 司浩
 * @Description:
 * @Date: 2023-02-06 15:50:15
 * @LastEditors: 司浩
 * @LastEditTime: 2023-02-07 13:43:43
 * @FilePath: \phoenix-web-ui\src\model\v2\coupon\videonumberActivity\BmodifyStockRequest.ts
 */
// 修改库存请求
export default class BmodifyStockRequest {
  // 活动号
  number: Nullable<string> = null
  // 活动原始库存
  oldStock: Nullable<number> = null
  // 库存变化量
  addStock: Nullable<number> = null
  // 库存变化类型 true 增加 false 减少
  add: boolean = true
}
