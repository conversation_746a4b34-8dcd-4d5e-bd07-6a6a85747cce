import { Component, Vue } from 'vue-property-decorator'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import GoodsScopeEx from 'cmp/goodsscope/GoodsScopeEx.vue'
import RSGrade from "model/common/RSGrade";
import CardBalancePromotionForm from "./CardBalancePromotionForm";
import CardTemplateSelectorDialog from "cmp/selectordialogs/CardTemplateSelectorDialog";
import CardTemplate from "model/card/template/CardTemplate";
import IdName from "model/common/IdName";
import CardBalancePromotionApi from "http/payment/card/CardBalancePromotionApi";
import CardTemplateFilter from "model/card/template/CardTemplateFilter";
import DateUtil from "util/DateUtil";
import ActivityTopicApi from "http/v2/controller/points/topic/ActivityTopicApi";
import ActivityTopic from "model/v2/controller/points/topic/ActivityTopic";
import GradeStepValue from "model/common/GradeSameReductionNew";
import EditType from 'common/EditType';
import ActiveStore from "cmp/activestore/ActiveStore";
import I18nPage from "common/I18nDecorator";
import CardBalancePromotionPermission from "./CardBalancePromotionPermission";
import CopyStepValue from "pages/deposit/prepaycard/cmp/copystepvalue/CopyStepValue";
import RoutePermissionMgr from "mgr/RoutePermissionMgr";
import GradeStepValues from 'model/common/GradeStepValues';
import CardBalancePromotionFormNew from './CardBalancePromotionFormNew';
import SelectCardTemplate from 'cmp/selectCardTemplate/SelectCardTemplate';
import ActivityDateTimeConditionPicker from "cmp/date-time-condition-picker/ActivityDateTimeConditionPicker";
import SysConfigApi from "http/config/SysConfigApi";
import BrowserMgr from "mgr/BrowserMgr";

@Component({
  name: "CardBalancePromotionDiscountEdit",
  components: {
    BreadCrume,
    GoodsScopeEx,
    ActiveStore,
    CopyStepValue,
    CardTemplateSelectorDialog,
    SelectCardTemplate,
    ActivityDateTimeConditionPicker
  },
})
@I18nPage({
  prefix: [
    "/储值/预付卡/预付卡支付活动/编辑页面",
    "/储值/会员储值/储值支付活动/编辑页面",
    "/公用/活动/状态",
    "/公用/活动/活动信息",
    "/公用/活动/提示信息",
    "/公用/js提示信息",
    "/公用/按钮",
    "/公用/菜单",
    "/营销/券礼包活动/券礼包活动"
  ],
})
export default class CardBalancePromotionDiscountEdit extends Vue {
  goodsMatchRuleMode: string = "barcode"
  get dateRangeOption() {
    return {
      disabledDate(time: any) {
        return time.getTime() < DateUtil.nowDayTime();
      },
    };
  }
  i18n: (str: string, params?: string[]) => string;
  $refs: any;
  panelArray: any = [
    {
      name: "预付卡支付优惠",
      url: "card-balance-promotion-list",
    },
    {
      name: "",
      url: "",
    },
  ];
  form: CardBalancePromotionFormNew = new CardBalancePromotionFormNew();
  editType: EditType = EditType.CREATE;
  activityId: Nullable<string> = null;
  gradeList: RSGrade[] = [];
  cardTemplateFilter: CardTemplateFilter = new CardTemplateFilter();
  prevSelectedCardTemplates: CardTemplate[] = [];
  permission = new CardBalancePromotionPermission();
  themes: ActivityTopic[] = [];
  created() {
    let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
		if (sysConfig && this.goodsMatchRuleMode) {
			this.goodsMatchRuleMode = sysConfig.goodsMatchRuleMode;
		}
    this.getConfig();
    this.editType = this.$route.query.editType;
    this.panelArray[0].name = this.i18n("预付卡支付优惠");
    if (this.editType === EditType.EDIT) {
      this.getGradeList(this.getDetail);
      this.panelArray[1].name = this.i18n("修改预付卡支付折扣活动");
    }
    if (this.editType === EditType.COPY) {
      this.getGradeList(this.getDetail);
      this.panelArray[1].name = this.i18n("新建预付卡支付折扣活动");
    }
    if (this.editType === EditType.CREATE) {
      this.getGradeList();
      this.panelArray[1].name = this.i18n("新建预付卡支付折扣活动");
    }
    this.form.init(this);
  }

  getConfig() {
    SysConfigApi.get().then((resp: any) => {
      if (resp && resp.data) {
        this.form.data.excludePromotion = resp.data.activityJoinPromotion
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  mounted() {
    this.cardTemplateFilter.typeIn = ["OFFLINE_GIFT_CARD", "ONLINE_GIFT_CARD", "RECHARGEABLE_CARD"];
    this.editType = this.$route.query.editType as EditType;
    this.activityId = this.$route.query.activityId as string;
    this.getTheme();
  }

  isPriceRepeat(arr: any[]) {
    var hash: any = {};
    for (var i in arr) {
      arr[i].threshold = arr[i].threshold ? Number(arr[i].threshold).toFixed(2) : '';
      if (hash[arr[i].threshold] && arr[i].threshold !== '') {
        return true;
      }
      // 不存在该元素，则赋值为true，可以赋任意值，相应的修改if判断条件即可
      hash[arr[i].threshold] = true;
    }
    return false;
  }

  checkSameDifferRule() {
    let validateArray = [];
    // 不同等级适用不同规则校验
    if (this.form.data.favRule === "gradeDiff") {
      for (let i = 0; i < this.form.data.gradeDifferentStepValue.length; i++) {
        const item = this.form.data.gradeDifferentStepValue[i];
        if (item.checked) {
          if (item.stepValues.length === 0) {
            this.$message.warning(this.i18n("请至少添加一个折扣"));
            return Promise.reject();
          }
          if (this.isPriceRepeat(item.stepValues)) {
            this.$message.warning(this.i18n("适用商品消费金额不允许重复"));
            return Promise.reject();
          }
          validateArray.push(this.$refs["stepValuesForm" + i]);
        }
      }
      console.log(validateArray);
      return Promise.all(
        validateArray.map((item: any) => {
          return item[0].validate();
        })
      );
    } else {
      if (this.form.data.gradeSameStepValue.stepValues.length === 0) {
        this.$message.warning(this.i18n("请至少添加一个折扣"));
        return Promise.reject();
      }
      if (this.isPriceRepeat(this.form.data.gradeSameStepValue.stepValues)) {
        this.$message.warning(this.i18n("适用商品消费金额不允许重复"));
        return Promise.reject();
      }
      return this.$refs.stepValuesForm.validate();
    }
  }

  doSave(cb: any) {
    Promise.all([this.checkSameDifferRule(), this.$refs.storeScope.validate(), this.$refs.activityDateTimeConditionPicker.validate(),
      this.$refs.goodsScope.validate(), this.$refs.form.validate(), this.$refs.selectCardTemplate.doValidate()]).then(
      (res: any[]) => {
        if (res.filter((e) => !e).length === 0) {
          let method: any = null;
          let requestParams = this.form.toParams();
          if (this.editType === EditType.CREATE || this.editType === EditType.COPY) {
            method = CardBalancePromotionApi.createDiscount;
          }
          if (this.editType === EditType.EDIT) {
            method = CardBalancePromotionApi.modifyDiscount;
            if (requestParams.body) {
              requestParams.body.activityId = this.activityId;
            }
          }
          if (method === null) {
            return;
          }
          method(requestParams)
            .then((res: any) => {
              if (res.code === 2000) {
                if (cb) {
                  cb(res);
                } else {
                  this.$message.success(this.i18n("保存成功"));
                  this.$router.push({ name: "card-balance-promotion-discount-dtl", query: { activityId: res.data } });
                }
              } else {
                this.$message.error(res.msg);
              }
            })
            .catch((reason: any) => {
              this.$message.error(reason.message);
            });
        }
      }
    );
  }

  doSaveAudit() {
    this.doSave((res: any) => {
      CardBalancePromotionApi.audit(res.data)
        .then((res: any) => {
          if (res.code === 2000) {
            this.$message.success(this.i18n("审核成功"));
          } else {
            this.$message.error(this.i18n("审核失败，原因：") + res.msg);
          }
        })
        .catch((reason: any) => {
          this.$message.error(this.i18n("审核失败，原因：") + reason.message);
        })
        .finally(() => {
          this.$router.push({ name: "card-balance-promotion-discount-dtl", query: { activityId: res.data } });
        });
    });
  }

  doCancel() {
    this.$router.back();
  }

  changeStrategy() {
    this.$refs.form.clearValidate();
    this.form.data.gradeSameStepValue = new GradeStepValue();
    for (let item of this.form.data.gradeDifferentStepValue) {
      for (const item1 of item.stepValues) {
        item1.value = null;
        item1.threshold = null;
      }
    }
    this.$forceUpdate();
  }

  doCopy(originGradeCode: string, index: number) {
    if (!this.form.data.strategy || !this.form.data.gradeDifferentStepValue) {
      return;
    }
    let reduction = this.form.data.gradeDifferentStepValue[index];
    console.log(reduction);
    for (const item of reduction.stepValues) {
      if (!item.value || !item.threshold) {
        this.$message.warning(this.i18n("规则不完整，请完整维护规则后再复制"));
        return;
      }
    }

    let gradeListCopy = JSON.parse(JSON.stringify(this.gradeList));
    let reductionCopy = JSON.parse(JSON.stringify(reduction));
    this.$refs.copyStepValue.show(originGradeCode, gradeListCopy, this.form.data.strategy, reductionCopy, true);
  }

  copyStepValue(checkedGradeCodeList: string[], gradeStepValue: GradeStepValues) {
    if (!this.form.data.gradeDifferentStepValue) {
      return;
    }
    let filteredReductions = this.form.data.gradeDifferentStepValue.filter((e: GradeStepValue) => checkedGradeCodeList.indexOf(e.grade + "") > -1);
    for (let item of filteredReductions) {
      item.checked = true;
      item.stepValues = gradeStepValue.stepValues;
    }
  }

  doCardTemplateSelected(arr: CardTemplate[]) {
    this.prevSelectedCardTemplates = arr;
    this.form.data.cardTemplates = [];
    for (let tpl of arr) {
      let idName = new IdName();
      idName.id = tpl.number;
      idName.name = tpl.name;
      this.form.data.cardTemplates.push(idName);
    }
  }

  private validateGoodsScope() {
    this.$refs.form.validateField("goods");
  }

  private getGradeList(cb: Nullable<any> = null) {
    CardBalancePromotionApi.gradeList().then((res: any) => {
      if (res.code === 2000) {
        this.gradeList = res.data;
        this.gradeList.sort((a: any, b: any) => {
          if (a.type !== b.type) {
            let typeMap: any = {
              FREE: 1,
              PAID: 2,
              SPECIAL: 3,
            };
            return typeMap[a.type] - typeMap[b.type];
          } else {
            return a.no - b.no;
          }
        });
        if (this.form.data.gradeDifferentStepValue) {
          for (let grade of this.gradeList) {
            let gradeStepValue = new GradeStepValues();
            gradeStepValue.grade = grade.code;
            gradeStepValue.gradeName = grade.name;
            gradeStepValue.checked = false;
            gradeStepValue.stepValues = [];
            this.form.data.gradeDifferentStepValue.push(gradeStepValue);
          }
        }
        if (cb) {
          cb(res.data);
        }
      }
    });
  }

  private getDetail(gradeList: RSGrade[]) {
    if (!this.activityId) {
      return;
    }
    CardBalancePromotionApi.infoDiscount(this.activityId)
      .then((res: any) => {
        if (res.code === 2000) {
          this.form.of(res.data, gradeList);
          this.prevSelectedCardTemplates = [];
          this.form.data.strategy = "BY_AMOUNT";
          for (let item of this.form.data.cardTemplates) {
            let cardTpl = new CardTemplate();
            cardTpl.number = item.id;
            cardTpl.name = item.name;
            this.prevSelectedCardTemplates.push(cardTpl);
          }
        } else {
          this.$message.error(res.msg);
        }
      })
      .catch((reason: any) => {
        this.$message.error(reason.message);
      });
  }

  private gotoTplDtl(num: string) {
    RoutePermissionMgr.openBlank({ name: "prepay-card-tpl-dtl", query: { number: num } });
  }

  private getTheme() {
    ActivityTopicApi.listTopic().then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.themes = resp.data;
      }
    });
  }

  private refresh(val: any) {
    this.form.data.gradeDifferentStepValue = JSON.parse(JSON.stringify(this.form.data.gradeDifferentStepValue));
  }

  private changeFavThresholdLimit() {
    this.form.data.favThreshold = null;
    this.$refs.form.validateField("favThreshold");
  }

  private addGradeSameItem() {
    if (this.form.data.gradeSameStepValue.stepValues.length === 10) {
      this.$message.warning("最多只能添加十条阶梯折扣设置");
      return;
    }
    this.form.data.gradeSameStepValue.stepValues.push({
      threshold: null,
      value: null,
    });
  }

  private removeGradeSameItem(index: number) {
    this.form.data.gradeSameStepValue.stepValues.splice(index, 1);
  }

  private addGradeDifferItem(index: number) {
    if (this.form.data.gradeDifferentStepValue[index].stepValues.length === 10) {
      this.$message.warning("最多只能添加十条阶梯折扣设置");
      return;
    }
    this.form.data.gradeDifferentStepValue[index].stepValues.push({
      threshold: null,
      value: null,
    });
  }

  private removeGradeDifferItem(index: number, indexInner: number) {
    this.form.data.gradeDifferentStepValue[index].stepValues.splice(indexInner, 1);
  }

  private selectCardChange({ cardType, cardTemplates }: any) {
    console.log(cardTemplates);
    this.form.data.cardType = cardType;
    this.form.data.cardTemplates = cardTemplates
  }
}
