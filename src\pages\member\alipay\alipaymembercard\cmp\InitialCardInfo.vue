<template>
    <div>
        <div class="basic-flex">
            <div class="basic-left">{{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/必填信息')}}</div>
            <div>
                <span>{{getMbrAttribute(requiredInfo)}}</span>
            </div>
        </div>
        <div class="basic-flex">
            <div class="basic-left">{{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/选填信息')}}</div>
            <div>
                <span>{{getMbrAttribute(optionalInfo)}}</span>
            </div>
        </div>
        <el-button @click="doSetCardInfo" style="margin: 0 20px 30px;">{{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/开卡信息设置')}}</el-button>
        <InitialCardSetting
            ref="cardSetting"
            :dialogShow="dialogCardInfo"
            @close="doCardInfoClose"
            @submit="doCardInfoSubmit"
        ></InitialCardSetting>
    </div>
</template>

<script lang="ts" src="./InitialCardInfo.ts">
</script>

<style lang="scss">

</style>