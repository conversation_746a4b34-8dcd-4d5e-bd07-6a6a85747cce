<template>
    <div class="wechat-step">
        <div class="step-title step-bottom1">1. 微信授权</div>
        <div class="step-sign"> > </div>
        <div class="step-title step-bottom2">2. 创建微信会员卡</div>
        <div class="step-sign"> > </div>
        <div class="step-title step-bottom3">3. 投放微信会员卡</div>
<!--        <div class="step-sign"> > </div>-->
<!--        <div class="step-title step-bottom4">4. 创建微信支付即会员规则</div>-->
    </div>
</template>

<style lang="scss">
    .wechat-step {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        font-size: 13px;
        font-family: '微软雅黑', Helvetica, Arial, sans-serif;
        font-weight: 500;
        line-height: 1.6;
        color: #999999;
        border-bottom: 1px solid #dfe2e5;
        margin: 10px 75px 0;
        padding: 0 20px;
        .step-title {
            padding: 10px 20px;
        }
        .step-sign {
            padding: 10px 5px;
        }
    }
</style>