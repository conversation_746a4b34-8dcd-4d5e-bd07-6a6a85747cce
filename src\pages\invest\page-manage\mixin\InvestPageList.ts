import ContentTemplateApi from 'http/template/ContentTemplateApi';
import ContentTemplate from 'model/template/ContentTemplate';
import ContentTemplateExtInfoUpdate from 'model/template/ContentTemplateExtInfoUpdate';
import CommonUtil from 'util/CommonUtil';
import { Vue } from 'vue-property-decorator';
import AppShareInfo from "model/template/AppShareInfo";
import I18nPage from 'common/I18nDecorator';


@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理'
  ],
  auto: true
})
export default abstract class InvestPageList extends Vue {
  $refs: any
  selected: ContentTemplate[] = []// 已勾选数据

  abstract getList(): void


  handleSelectionChange = (e: any[]) => {
    this.selected = e
    console.log('看看勾选的数据', this.selected);
  }

  // 批量修改分享信息
  batchEditShareInfo = () => {
    if (!this.selected.length) {
      return this.$message.warning(this.i18n('请先勾选要修改的页面'))
    }
    const ids = this.selected.map((item) => item.id) || []
    this.$refs.shareConfigDialog.open(ids)
  }

  // 修改单个分享信息
  doEditShare = (row: ContentTemplate) => {
    const ids = [row.id]
    const formValue = JSON.parse(JSON.stringify(row.extInfo))
    this.$refs.shareConfigDialog.open(ids, 'edit', formValue)
  }

  // 确认批量修改分享信息
  doChangeShareInfo = (ids: string[], shareInfo: any) => {
    const params = new ContentTemplateExtInfoUpdate()
    params.extInfo = shareInfo
    params.ids = ids
    params.operator = JSON.parse(sessionStorage.getItem("vuex") as string)?.loginInfo?.user?.account
    const loading = CommonUtil.Loading()
    ContentTemplateApi.updateExtInfo(params).then((res) => {
      if (res.code === 2000) {
        this.$message.success(this.i18n('操作成功'))
        this.$refs.shareConfigDialog.close()
        this.getList()
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    }).finally(() => {
      loading.close()
    })
  }
  // 修改导航栏是否显示
  doChangeNavigationShow(row: ContentTemplate, navigationShowed : boolean) {
    const params = new ContentTemplateExtInfoUpdate()
    if (row.extInfo) {
      params.extInfo = JSON.parse(JSON.stringify(row.extInfo))
    } else {
      params.extInfo = new AppShareInfo()
    }
    params.extInfo.navigationShowed = navigationShowed
    params.ids = row.id ? [row.id.toString()] : []
    params.operator = JSON.parse(sessionStorage.getItem("vuex") as string)?.loginInfo?.user?.account
    const loading = CommonUtil.Loading()
    ContentTemplateApi.updateExtInfo(params).then((res) => {
      if (res.code === 2000) {
        this.$message.success(this.i18n('操作成功'))
        this.$refs.shareConfigDialog.close()
        this.getList()
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    }).finally(() => {
      loading.close()
    })
  }
}