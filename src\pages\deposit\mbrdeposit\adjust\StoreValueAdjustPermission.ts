/*
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:03
 * @LastEditTime: 2025-04-18 11:02:37
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\mbrdeposit\adjust\StoreValueAdjustPermission.ts
 * 记得注释
 */
import {Vue} from 'vue-property-decorator'
import PermissionMgr from 'mgr/PermissionMgr'

export default class StoreValueAdjustPermission extends Vue {
  prefix = '/储值/储值管理/储值调整单'

  get editable() {
    return PermissionMgr.hasOptionPermission(this.prefix, '单据维护')
  }

  get auditable() {
    return PermissionMgr.hasOptionPermission(this.prefix, '单据审核')
  }

  get reasonEditable() {
    return PermissionMgr.hasOptionPermission(this.prefix, '调整原因设置')
  }

  get viewable() {
    return PermissionMgr.hasOptionPermission(this.prefix, '单据查看')
  }

  get exportLine() {
    return PermissionMgr.hasOptionPermission(this.prefix, '导出明细')
  }

  get rejectable() {
    return PermissionMgr.hasOptionPermission(this.prefix, '单据驳回')
  }

  get submitable() {
    return PermissionMgr.hasOptionPermission(this.prefix, '单据提交')
  }
}
