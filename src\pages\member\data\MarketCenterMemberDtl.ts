import { Component, Vue, Watch } from "vue-property-decorator";
import Bread<PERSON>rume from "cmp/bread-crumb/BreadCrume.vue";
import FormItem from "cmp/formitem/FormItem.vue";
import EditDataDialog from "pages/member/data/dialog/EditDataDialog.vue";
import AdjustMemberLevelDialog from "pages/member/data/dialog/AdjustMemberLevelDialog.vue";
import ResetPasswordDialog from "pages/member/data/dialog/ResetPasswordDialog.vue";
import MemberA<PERSON> from "http/member_standard/MemberApi";
import MemberDetail from "model/member_v2/member/MemberDetail";
import EditTagInfoDialog from "pages/member/data/dialog/EditTagInfoDialog.vue";
import CheckCouponDialog from "pages/member/data/dialog/CheckCouponDialog.vue";
import CheckPrePayCardDialog from "pages/member/data/dialog/CheckPrePayCardDialog.vue";
import CheckWater from "pages/member/data/dialog/CheckWater.vue";
import PrePayConfigApi from "http/prepay/config/PrePayConfigApi";
import UnBindDialog from "pages/member/data/dialog/UnBindDialog.vue";
import ConstantMgr from "mgr/ConstantMgr";
import MemberOptLogDrawer from "pages/member/data/drawer/MemberOptLogDrawer";
import MemberAssetsDrawer from "pages/member/data/drawer/MemberAssetsDrawer";
import SingleModifyStoreDialog from "pages/member/data/cmp/SingleModifyStoreDialog.vue";
import PageConfigApi from "http/pageConfig/PageConfigApi";
import LuDaoConfig from "model/common/LuDaoConfig";
import LuDaoFamilyCardFilter from "model/common/LuDaoFamilyCardFilter";
import LuDaoFamilyCardApi from "http/member_standard/LuDaoFamilyCardApi";
import DeleteMemberIdentRequest from "model/member_standard/DeleteMemberIdentRequest";
import MainAndSubCard from "model/member_standard/MainAndSubCard";
import UnbindSubMemberRequest from "model/member_standard/UnbindSubMemberRequest";
import UnBindSubDialog from "./dialog/UnBindSubDialog";
import MemberLeftInfo from "pages/member/data/cmp/MemberLeftInfo";
import MemberNavbar from "pages/member/data/cmp/MemberNavbar";
import MemberCardDetail from "pages/member/data/cmp/MemberCardDetail";
import MemberInfoDetail from "pages/member/data/cmp/MemberInfoDetail";
import MemberTradeDetail from "pages/member/data/cmp/MemberTradeDetail";
import Tools from "util/Tools";

@Component({
	name: "MarketCenterMemberDtl",
	components: {
		BreadCrume,
		FormItem,
		EditDataDialog,
		AdjustMemberLevelDialog,
		ResetPasswordDialog,
		EditTagInfoDialog,
		CheckCouponDialog,
		CheckPrePayCardDialog,
		CheckWater,
		UnBindDialog,
		MemberOptLogDrawer,
		MemberAssetsDrawer,
		SingleModifyStoreDialog,
		UnBindSubDialog,
		MemberLeftInfo,
		MemberNavbar,
		MemberCardDetail,
		MemberInfoDetail,
		MemberTradeDetail
	},
})
export default class MarketCenterMemberDtl extends Vue {
	get getRegisterChannel() {
		// 如果type存在【third，alipay，weixin，store，phoenix】，根据type去取id
		if (this.dtl.registerChannel && this.dtl.registerChannel.type) {
			if (this.dtl.registerChannel.type === "third") {
				return this.formatI18n("/会员/会员资料", "第三方");
			} else if (this.dtl.registerChannel.type === "alipay") {
				return this.formatI18n("/会员/会员资料", "支付宝");
			} else if (this.dtl.registerChannel.type === "weixin") {
				return this.formatI18n("/会员/会员资料", "微信");
			} else if (this.dtl.registerChannel.type === "store") {
				return this.formatI18n("/会员/会员资料", "门店注册");
			} else if (this.dtl.registerChannel.type === "phoenix") {
				return "CRM";
			} else if (this.dtl.registerChannel.type === "weixinApp") {
				return this.formatI18n("/会员/会员资料", "海鼎会员小程序");
			} else {
				if (this.dtl.registerChannel && this.dtl.registerChannel.id) {
					return this.dtl.registerChannel.id;
				} else {
					return "--";
				}
			}
		} else {
			return "--";
		}
		// 如果type不存在，但是id存在，直接显示id
	}
	get getInvited() {
		if (this.dtl.referee && this.dtl.referee.mobile) {
			return this.dtl.referee.mobile;
		} else if (this.dtl.referee && this.dtl.referee.crmCode) {
			return this.dtl.referee.crmCode;
		} else {
			return "";
		}
	}
	get getDay() {
		if (this.dtl) {
			if (this.dtl.lastConsumeDay === 0) {
				return this.formatI18n("/会员/会员资料", "今天");
			} else if (this.dtl.lastConsumeDay === 1) {
				return this.formatI18n("/会员/会员资料", "昨天");
			} else {
				let str: any = this.formatI18n("/会员/会员资料", "{0}天前");
				str = str.replace(/\{0\}/g, this.dtl.lastConsumeDay);
				return str;
			}
		} else {
			return "";
		}
	}
	mainAndSubCard: any = [];
	isMainMember = false;
	unBindFlag = false;
	waterFlag = false;
	couponFlag = false;
	prepayCardFlag = false;
	tags: any = [];
	cars: any = [];
	adjustParams: any = {};
	dtl: MemberDetail = new MemberDetail();
	icCards: any = [];
	editDataFlag = false;
	adjustMemberLevelFlag = false;
	resetPasswordFlag = false;
	editTagFlag = false;
	originalMemberId: string = "";
	// 分页
	page = {
		currentPage: 1,
		total: 0,
		size: 10,
	};
	panelArray: any = [];
	remarkDialog = {
		visible: false,
		remark: "",
	};
	$refs: any;
	switchFlag = false;
	bindUuid: string = "";
	dtlAddress = "";
	singleStoreDialogShow: boolean = false;
	isLuDao: Boolean = false

	unBindSubFlag: Boolean = false
	currentSubCard: Nullable<MainAndSubCard> = null
	customMemberAttr: any[] = []
	created() {
		this.panelArray = [
			{
				name: this.formatI18n("/会员/会员资料", "营销中心会员"),
				url: "marketcenter-member-list",
			},
			{
				name: this.formatI18n("/会员/会员资料", "会员详情"),
				url: "",
			},
		];
		this.getDtl();
		this.getPrePermission();
	}
	doEdit() {
		this.editDataFlag = true;
	}
	doAdjust() {
		this.adjustMemberLevelFlag = true;
	}
	doReset() {
		this.resetPasswordFlag = true;
	}
	@Watch("$route.query.id")
	watchMemberIdChanged() {
		this.getDtl();
	}
	doInviteMember() {
		this.$router.push({ name: "marketcenter-member-dtl", query: { id: this.dtl.referee!.memberId } });
	}
	doUnbindClose() {
		this.unBindFlag = false;
		this.getDtl();
	}
	doUnBind(id: string) {
		this.unBindFlag = true;
		this.bindUuid = id;
	}
	doFreezon() {
		let str: any = this.formatI18n("/会员/会员资料", "会员冻结后将无法继续使用和获得权益。请确认是否冻结此会员？");
		if (this.dtl.state === "Blocked") {
			str = this.formatI18n("/会员/会员资料", "会员解冻后将可正常使用和获得权益。请确认是否解冻此会员？");
		}
		this.$confirm(str, this.formatI18n("/公用/弹出模态框提示标题", "提示") as any, {
			confirmButtonText:
				this.dtl.state === "Blocked" ? (this.formatI18n("/会员/会员资料", "解冻") as any) : (this.formatI18n("/会员/会员资料", "冻结") as any),
			cancelButtonText: this.formatI18n("/公用/按钮", "取消") as any,
		}).then(() => {
			if (this.dtl.state === "Blocked") {
				MemberApi.unBlock(this.$route.query.id as string)
					.then((resp: any) => {
						if (resp && resp.code === 2000) {
							this.$message.success(this.formatI18n("/会员/会员资料", "解冻成功") as any);
							this.getDtl();
						}
					})
					.catch((error: any) => {
						if (error && error.message) {
							this.$message.error(error.message);
						}
					});
			} else {
				MemberApi.block(this.$route.query.id as string)
					.then((resp: any) => {
						if (resp && resp.code === 2000) {
							this.$message.success(this.formatI18n("/会员/会员资料", "冻结成功") as any);
							this.getDtl();
						}
					})
					.catch((error: any) => {
						if (error && error.message) {
							this.$message.error(error.message);
						}
					});
			}
		});
	}
	doCardTemplate(cardId: string) {
		this.$router.push({ name: "prepay-card-tpl-dtl", query: { number: cardId } });
	}
	doCheckCoupon() {
		this.couponFlag = true;
	}
	doCheckPrePayCard() {
		this.prepayCardFlag = true;
	}
	doEditTag() {
		this.editTagFlag = true;
	}
	doWaterClose() {
		this.waterFlag = false;
	}
	doCheckWater() {
		if (this.switchFlag) {
			// 开启多账户
			this.$router.push({ name: "store-value-query", query: { id: this.dtl.crmCode } });
		} else {
			this.waterFlag = true;
		}
	}

	doResetPwdClose() {
		this.resetPasswordFlag = false;
	}
	doEditTagClose(tags: any) {
		this.editTagFlag = false;
		this.getDtl();
	}
	doCouponClose() {
		this.couponFlag = false;
	}
	doPrepayCardClose() {
		this.prepayCardFlag = false;
	}
	doEditDataClose() {
		this.editDataFlag = false;
		this.getDtl();
	}
	doEditAjustMemberLevelClose() {
		this.adjustMemberLevelFlag = false;
		this.getDtl();
	}
	/**
	 * 分页页码改变的回调
	 * @param val
	 */
	onHandleCurrentChange(val: number) {
		this.page.currentPage = val;
	}
	/**
	 * 每页多少条的回调
	 * @param val
	 */
	onHandleSizeChange(val: number) {
		this.page.size = val;
	}
	getGrade(gradeCode: string, gradeName: string) {
		if (gradeCode) {
			return `[${gradeCode}]${gradeName}`;
		} else {
			return "--";
		}
	}
	// 打开批量修改门店弹框
	private doSinglkeModifyStore() {
		this.singleStoreDialogShow = true;
	}

	private singleStoreDialogClose() {
		this.singleStoreDialogShow = false;
	}

	private getCRMConfig() {
		return new Promise((resolve, reject)=>{
		  PageConfigApi.getConfig().then(res=>{
			if (res.code === 2000 && res.data) {
			  resolve(res.data.luDao)
			} else {
			  resolve(null)
			}
		  })
		})
	  }

	  getMainAndSubCardInfo(originalMemberId: string){
		this.getCRMConfig().then((luDao: LuDaoConfig)=>{
		  // 鹿岛家庭卡
		  if (luDao && luDao.showFamilyCard) {
			this.isLuDao = luDao.showFamilyCard
			let params = new LuDaoFamilyCardFilter()
			params.memberId = originalMemberId
			params.page = 0
			params.pageSize = 10
			LuDaoFamilyCardApi.query(params).then(res=>{
			  if (res.code === 2000 && res.data) {
				this.isMainMember = res.data.isMaster as boolean
				this.mainAndSubCard = res.data.infos
			  }
			})
		  } else {
			// 小象主副卡
			this.isLuDao = false
			MemberApi.getMainAndSubCard(originalMemberId).then((resp: any) => {
			  if (resp && resp.code === 2000) {
				if(resp.data != null && resp.data.length > 0) {
				  this.isMainMember = resp.data[0].mainCard
				  this.mainAndSubCard = resp.data
				}
			  }
			}).catch((error: any) => {
			  if (error && error.message) {
				this.$message.error(error.message)
			  }
			})
		  }
		})
	  }

	private singleStoreChangeSuccess() {
		this.getDtl();
		this.singleStoreDialogShow = false;
	}

	private getDtl() {
		const loading = this.$loading(ConstantMgr.loadingOption);
		let originalMemberId = this.$route.query.id;
		MemberApi.detail(this.$route.query.id as string).then(async (resp: any) => {
			if (resp && resp.code === 2000) {
				this.$route.query.id = resp.data.memberId;
				this.dtl = resp.data;
				if (this.dtl.province) {
					if (this.dtl.address) {
						this.dtlAddress =
							this.dtl.province.name +
							"/" +
							this.dtl.city!.name +
							"/" +
							(this.dtl.district ? this.dtl.district!.name + "/" : "") +
							this.dtl.street!.name +
							"/" +
							this.dtl.address;
					} else {
						this.dtlAddress =
							this.dtl.province.name +
							"/" +
							this.dtl.city!.name +
							"/" +
							(this.dtl.district ? this.dtl.district!.name + "/" : "") +
							"/" +
							this.dtl.street!.name;
					}
				} else {
					if (this.dtl.address) {
						this.dtlAddress = this.dtl.address;
					} else {
						this.dtlAddress = "";
					}
				}
				this.cars = resp.data.cars;
				this.tags = [];
				loading.close();
				this.customMemberAttr = await Tools.getMemberExtInfo(this.dtl.extObj!)
				this.adjustParams = {
					level: this.dtl.gradeCode ? `[${this.dtl.gradeCode}]${this.dtl.gradeName}` : "",
					date: this.dtl.gradeValidate,
				};
			}
			// MemberApi.getMainAndSubCard(originalMemberId as string)
			// 	.then((resp: any) => {
			// 		if (resp && resp.code === 2000) {
			// 			if (resp.data != null) {
			// 				this.isMainMember = resp.data.mainCard;
			// 				this.mainAndSubCard = [resp.data];
			// 			}
			// 		}
			// 	})
			// 	.catch((error: any) => {
			// 		if (error && error.message) {
			// 			this.$message.error(error.message);
			// 		}
			// 	});
			this.getMainAndSubCardInfo(originalMemberId as string)
		});
		// .catch((error: any) => {
		//   loading.close()
		//   this.$message.error(error.message)
		// })
	}
	private getPrePermission() {
		PrePayConfigApi.get()
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					if (resp.data && resp.data.enableMultipleAccount) {
						// 开启多账户
						this.switchFlag = true;
					} else {
						this.switchFlag = false; // 未开启多账户
					}
				}
			})
			.catch((error) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}

	private editRemark() {
		this.remarkDialog.remark = this.dtl.remark as string;
		this.remarkDialog.visible = true;
	}

	private updateRemark() {
		MemberApi.editRemark(this.dtl.memberId as string, this.remarkDialog.remark)
			.then((res) => {
				this.$message.success(this.formatI18n("/会员/会员资料/编辑成功"));
				this.remarkDialog.visible = false;
				this.dtl.remark = this.remarkDialog.remark;
			})
			.catch((error) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}

	private showOptLog() {
		this.$refs.memberOptLogDrawer.show(this.dtl.memberId);
	}

	private showAssets() {
		this.$refs.memberAssetsDrawer.show(this.dtl);
	}

	private deleteMobile(phone: string) {
		this.$confirm(this.formatI18n('/会员/会员资料/确定删除吗？'), this.formatI18n('/公用/弹出模态框提示标题/提示'), {
		  confirmButtonText: this.formatI18n('/公用/按钮/确定'),
		  cancelButtonText: this.formatI18n('/公用/按钮/取消'),
		}).then(() => {
		  let params: DeleteMemberIdentRequest = new DeleteMemberIdentRequest()
		  params.memberId = this.dtl.memberId as string
		  params.ident.push({
			id: phone,
			type: 'mobile',
			source: null
		  })
		  MemberApi.deleteMemberIdent(params).then(res=>{
			if(res.code === 2000) {
			  this.$message({
				type: 'success',
				message: this.formatI18n('/公用/活动/提示信息/操作成功')
			  });
			} else {
			  this.$message.error(res.msg as string)
			}
		  })
		}).catch(() => {

		});
	}

	doUnbindSubClose() {
		this.unBindSubFlag = false
	}

	private unbindSub(row: MainAndSubCard) {
		this.currentSubCard = row
		this.unBindSubFlag = true
	}

	unbindSubConfirm() {
		let params: UnbindSubMemberRequest = new UnbindSubMemberRequest()
		params.mainMemberId = this.$route.query.id as string
		params.subMemberId = this.currentSubCard!.subMemberId

		MemberApi.unbindSubMember(params).then(res=>{
			if (res.code === 2000) {
			this.$message.success(this.formatI18n('/公用/活动/提示信息/操作成功'))
			this.doUnbindSubClose()
			this.getMainAndSubCardInfo(this.$route.query.id as string)
			} else {
			this.$message.error(res.msg as string)
			}
		})
	}
	// tab页面索引
	currentTabIndex: number = 0;

	onTabChange(index: number) {
		this.currentTabIndex = index;
	}
}
