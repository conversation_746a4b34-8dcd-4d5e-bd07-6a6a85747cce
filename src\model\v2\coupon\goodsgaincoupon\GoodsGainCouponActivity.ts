import BaseCouponActivity from 'model/v2/coupon/BaseCouponActivity'
import CouponItem from 'model/common/CouponItem'
import CouponThreshold from 'model/common/CouponThreshold'
import GoodsRange from 'model/common/GoodsRange'
import DateTimeCondition from "model/common/DateTimeCondition";
import PushGroup from 'model/precisionmarketing/pushplan/PushGroup';

export default class GoodsGainCouponActivity extends BaseCouponActivity {
  // 发券门槛
  issueThreshold: Nullable<CouponThreshold> = null
  // 发券商品
  goodsRange: Nullable<GoodsRange> = null
  // 券礼包
  giftInfo: Nullable<CouponItem> = null
  // 活动时间限制
  dateTimeCondition = new DateTimeCondition()
  // 客群
  rule: Nullable<PushGroup> = null
  // 参与叠加促销
  joinPromotion: Nullable<boolean> = false;
  // 排除优惠商品
  excludeFavourGoodTypes: string[] = []
  // 排除优惠金额
  excludeFavourAmountTypes: string[] = ["PROMOTION","COUPON","MEMBER","OTHER"]
  // 活动总限N张券
  maxIssueQtyByCoupon: Nullable<number> = null;
  // 每人限领N张券
  maxPerIssueQtyByCoupon: Nullable<number> = null;
}