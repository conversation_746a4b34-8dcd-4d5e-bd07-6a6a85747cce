/*
 * @Author: 黎钰龙
 * @Date: 2025-01-22 15:02:39
 * @LastEditTime: 2025-02-27 17:05:02
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\analysis\BCustomerProfileLine.ts
 * 记得注释
 */
import BCustomerProfileDimension from "./BCustomerProfileDimension"
import BCustomerProfileTarget from "./BCustomerProfileTarget"
import CalculationResult from "./CalculationResult"
import { SortEnum } from "./SortEnum"

// 客户画像卡片明细
export default class BCustomerProfileLine {
  // uuid
  uuid: Nullable<string> = null
  // 画像名称
  name: Nullable<string> = null
  // 画像id
  customerProfileId: Nullable<string> = null
  // 计算规则类型
  ruleType: Nullable<PortraitInfoType> = null
  // 排序枚举 ；默认按目标人群的覆盖人数降序； coverageAsc, // 覆盖人数升序
  sort: Nullable<SortEnum> = null
  // 默认 “ 前7项 ” ，可以选择 前10项 or 15 or 20 or 25
  range: Nullable<7 | 10 | 15 | 20 | 25> = null
  // 维度1（横坐标）
  dimension1: Nullable<BCustomerProfileDimension> = null
  // 维度2（颜色区分）
  dimension2: Nullable<BCustomerProfileDimension> = null
  // 指标（纵坐标）
  target: Nullable<BCustomerProfileTarget> = null
  // 结果
  calculationResult: Nullable<CalculationResult> = null
  // 客群id
  customerId: Nullable<string> = null
  // 覆盖率
  coveredRate: Nullable<string> = null
}

// 计算规则类型枚举
export enum PortraitInfoType {
  // 属性分布
  AttributeDistributionCalculationRule = 'AttributeDistributionCalculationRule',
  // 属性统计
  AttributeStatisticsCalculationRule = 'AttributeStatisticsCalculationRule',
  // 交叉分布
  CrossDistributionCalculationRule = 'CrossDistributionCalculationRule',
  // 交叉统计
  CrossStatisticsCalculationRule = 'CrossStatisticsCalculationRule'
}