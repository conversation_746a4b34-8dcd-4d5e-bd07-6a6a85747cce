/*
 * @Author: 黎钰龙
 * @Date: 2024-11-21 19:31:52
 * @LastEditTime: 2024-12-06 16:47:16
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\autoTag\FirstAndLastTagRule.ts
 * 记得注释
 */
import EventBehaviorRule from "./EventBehaviorRule"
import { FirstAndLast } from "./FirstAndLast"
import { IndicatorType } from "./IndicatorType"

export default class FirstAndLastTagRule {
  // 标签名称
  tagName: Nullable<string> = null
  // 事件行为规则
  eventBehaviorRule: Nullable<EventBehaviorRule> = null
  // 事件行为指标条件：首次，末次
  metricsCondition: Nullable<FirstAndLast> = FirstAndLast.last
  // 事件行为指标类型
  metricsType: Nullable<IndicatorType> = null
  // 事件行为指标属性
  metricsProp: Nullable<string> = null
}