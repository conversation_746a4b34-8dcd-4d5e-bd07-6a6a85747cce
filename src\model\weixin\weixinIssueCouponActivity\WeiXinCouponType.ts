/*
 * @Author: 黎钰龙
 * @Date: 2024-01-24 14:07:06
 * @LastEditTime: 2025-04-08 15:18:26
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\weixin\weixinIssueCouponActivity\WeiXinCouponType.ts
 * 记得注释
 */
export enum WeiXinCouponType {
  // 单券（代金券）
  PAY_COUPON_SINGLE = 'PAY_COUPON_SINGLE',
  // 单券（其他服务商代金券）
  ISV_PAY_COUPON_SINGLE = 'ISV_PAY_COUPON_SINGLE',
  // 券包（代金券）
  PAY_COUPON_BAG = 'PAY_COUPON_BAG',
  // 券包（商家券）
  MERCHANT_COUPON_BAG = 'MERCHANT_COUPON_BAG',
  // 微信支付有礼-单券
  MERCHANT_COUPON_SINGLE = 'MERCHANT_COUPON_SINGLE'
}