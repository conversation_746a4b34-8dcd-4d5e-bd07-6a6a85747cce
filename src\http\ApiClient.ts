/**
 * created by ckj 2018/10/30
 */
import axios from 'axios'
import store from '../store'

import ShortcutMgr from 'mgr/ShortcutMgr'
import Vue from 'vue'
import EnvUtil from 'util/EnvUtil.js'
import ConstantMgr from 'mgr/ConstantMgr'
import CommonUtil from "util/CommonUtil";
import LoginApi from './login/LoginApi'
import BrowserMgr from 'mgr/BrowserMgr'

//正则
function cutUrl(url: string) {
  return url.replace(/http(s)?:\/\/.*\/(uat|dev|pre)/g, '').replace(/\?.*/g, '')
}

const qs = require('qs');
let logouting = false
axios.defaults.paramsSerializer = (params) => {
  return qs.stringify(params, { arrayFormat: 'repeat' });
};
axios.defaults.timeout = 60_000;

export default class ApiClient {
  public static file(baseUrl: string) {
    return axios.create({
      baseURL: baseUrl,
    });
  }

  public static server() {
    // 可以在这里拦截
    const baseUrl = EnvUtil.getServiceUrl();
    // baseUrl = 'http://localhost:8769/alphamo'
    return ApiClient.create(baseUrl);
  }

  // 访问本机写卡服务时调用的接口
  public static localhostServer() {
    const baseUrl = 'http://localhost:17083/'
    return ApiClient.create(baseUrl);
  }

  public static create(baseUrl: string) {
    const instance = axios.create({
      baseURL: baseUrl,
      withCredentials: true,
    });

    //请求拦截
    instance.interceptors.request.use(function (config) {
      let traceId;
      config.headers = {}
      config.headers["content-type"] = "application/json;charset=UTF-8";

      //生成当前用户的唯一id
      // if (store.state.user) {
      //   traceId = store.state.user.id + '_' + new Date().getTime();
      // } else {
      //   traceId = CommonUtil.uuid();
      // }
      const memberIdStr = store.state.loginInfo?.user?.uuid ? store.state.loginInfo?.user?.uuid : '-'
      traceId = 'web-' + memberIdStr + '-' + new Date().getTime() + '-' + CommonUtil.uuid().slice(0, 8) //PHX-10086，trace_id更换为时间戳+8位uuid
      config.headers.bizinfo = traceId
      config.headers['traceparent'] = CommonUtil.generateTraceParent()
      //设定当前语言
      if (sessionStorage.getItem('locale')) {
        config.headers.locale = sessionStorage.getItem('locale')
      } else {
        config.headers.locale = 'zh_CN'
      }

      // uni登录
      if (sessionStorage.getItem('at') && config.url !== '/v1/uni/token') {
        config.headers.authorization = sessionStorage.getItem('at')
      }

      // 计算服务器与用户PC的时间差
      if (config.headers['server-time']) {
        const currentTime = Math.floor(new Date().getTime()) // 当前时间戳,向下取整
        const diffTime = (config.headers['server-time'] - currentTime) // 时间差
        store.dispatch('serverDiffTime', diffTime)
      }
      //对接uni
      if (store.state.token && store.state.token.accessToken) {
        //向后端发起所有api请求时，如果当前store保存了token信息
        const tokenArr: string[] = store.state.token.accessToken.split('.')
        if (tokenArr.length === 3) {
          const tokenStr = tokenArr[1].replace(/-/g, '+').replace(/_/g, '/')
          const tokenInfo = JSON.parse(window.atob(tokenStr))
          const currentTime = Math.floor(new Date().getTime()) // 当前时间戳,向下取整
          // 判断token失效时间，若失效则将token替换成refreshToken
          if (tokenInfo.exp * 1000 < currentTime + store.state.serverDiffTime) {
            // token失效
            config.headers.authorization = store.state.token.refreshToken
          } else {
            // 未过失效时间
            config.headers.authorization = store.state.token.accessToken
          }
        } else {
          config.headers.authorization = store.state.token.accessToken
        }
      }

      // 营销中心
      config.headers.marketingCenter = sessionStorage.getItem("marketCenter");

      config.headers.time_zone = new Date().getTimezoneOffset()
      if (config.url && store.state.user) {
        config.url = config.url.replace('{merchant}', store.state.user.merchant)
      } else {
        const user = JSON.parse(sessionStorage.getItem('user')!)
        if (config.url && user) {
          config.url = config.url.replace('{merchant}', user.merchant)
        }
      }
      return config;
    }, function (error) {
      return Promise.reject(error)
    });

    //响应拦截
    instance.interceptors.response.use(function (response) {
      // 更新token
      if (response.headers['access-token'] || response.headers['refresh-token']) {
        const token = store.state.token
        if (response.headers['access-token']) {
          token.accessToken = response.headers['access-token']
        }
        if (response.headers['refresh-token']) {
          token.refreshToken = response.headers['refresh-token']
        }
        store.dispatch('updateToken', token)
      }

      if (response.data instanceof ArrayBuffer) {
        return response
      }
      if (response && response.data && response.data.message) { // 兼容不同的response结构
        const error = new Error()
        error.message = response.data.message
          (error as any).response = response.data
        throw error;
      }
      if (!response.data.msg) {
        return response
      } else {
        const error = new Error()
        if (response.data.msg) {
          error.message = response.data.msg
        } else {
          error.message = response.status + new ConstantMgr.MenusFuc().format('/公用/系统', '服务器内部异常')
        }
        (error as any).response = response.data
        return response
      }
    }, function (error) {
      console.log('看看error长啥样', error);
      if (!error.response) {
        error.message = new ConstantMgr.MenusFuc().format('/公用/系统', '请检查网络设置')
        console.log('请检查网络设置', error);
        return Promise.reject(error)
      }
      switch (error.response.status) {
        case 101:
          break;
        case 401:
          if (!logouting) {
            logouting = true
            Vue.prototype.$confirm(new ConstantMgr.MenusFuc().format('/公用/系统', '长时间未登录，需要重新登录'), new ConstantMgr.MenusFuc().format('/公用/弹出模态框提示标题', '提示'), {
              confirmButtonText: new ConstantMgr.MenusFuc().format('/公用/按钮', '确定'),
              cancelButtonText: new ConstantMgr.MenusFuc().format('/公用/按钮', '取消'),
              type: 'warning'
            }).then(() => {
              const sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
              if (sysConfig?.portalHomeUrl) {
                // portal登录方式时，跳转到portal登录页
                ShortcutMgr.clearStorage();
                store.commit('updateToken', {  //uni登录方式时，接入的token请求头
                  accessToken: '',
                  refreshToken: '',
                  temporaryToken: ''
                })
                window.location.href = sysConfig?.portalHomeUrl;
              } else if (store.state.token?.accessToken) {  //uni的重新登录：跳转到uni登录页
                ShortcutMgr.clearStorage();
                LoginApi.getUniHome().then((res) => {
                  if (res.code === 2000 && res.data) {
                    store.commit('updateToken', {  //uni登录方式时，接入的token请求头
                      accessToken: '',
                      refreshToken: '',
                      temporaryToken: ''
                    })
                    window.location.href = res.data;
                  }
                })
              } else {
                ShortcutMgr.logout()
                return Promise.resolve()
              }
            }).finally(() => {
              logouting = false
            })
          }
          break;
        case 403:
          if (error && error.response && error.response.data.message === 'licenseInvalid') {
            ShortcutMgr.noLicense()
          } else {
            error.message = new ConstantMgr.MenusFuc().format('/公用/系统', '禁止访问')
          }
          break;
        case 429:
          error.message = new ConstantMgr.MenusFuc().format('/公用/系统', '请求过多，请稍后重试')
          break;
        case 503:
          error.message = new ConstantMgr.MenusFuc().format('/公用/系统', '服务器升级中')
          break;
        case 500:
          error.message = new ConstantMgr.MenusFuc().format('/公用/系统', '服务内部异常')
          break;
        default:
          error.message = new ConstantMgr.MenusFuc().format('/公用/系统', '未知错误')
      }
      if (error.message === 'Request failed with status code 401') {
        error.message = null
      }
      return Promise.reject(error)
    })
    return instance
  }
}
