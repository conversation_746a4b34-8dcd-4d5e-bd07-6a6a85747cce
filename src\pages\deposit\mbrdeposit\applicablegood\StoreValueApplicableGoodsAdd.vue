<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:03
 * @LastEditTime: 2023-07-18 15:58:07
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\mbrdeposit\applicablegood\StoreValueApplicableGoodsAdd.vue
 * 记得注释
-->
<template>
  <div class="grow-value-rule-setting">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button @click="doSave" type="primary" v-if="hasOptionPermission('/储值/储值管理/储值适用商品设置', '账户信息维护')">{{formatI18n('/公用/按钮', '保存')}}</el-button>
        <el-button @click="doCancel" v-if="hasOptionPermission('/储值/储值管理/储值适用商品设置', '账户信息维护')">{{formatI18n('/公用/按钮', '取消')}}</el-button>

      </template>
    </BreadCrume>
    <div class="current-page">
      <el-form :model="form" :rules="rules" ref="ruleForm" label-width="120px" v-loading="loading">
        <div class="panel">
        <div class="content">
          <el-form-item :label="formatI18n('/储值/会员储值/储值适用商品设置/适用商品')" :required="true">
            <GoodsScopeEx  v-model="form.data.goodsRange" :goodsMatchRuleMode="goodsMatchRuleMode" ref="goodsScope"/>
          </el-form-item>
          <el-form-item :label="i18n('储值使用须知')">
            <el-input v-model.trim="form.data.remark" :placeholder="i18n('请输入不超过20个字')" maxlength="20" style="width: 300px">
            </el-input>
          </el-form-item>
        </div>
        </div>
      </el-form>
    </div>

  </div>
</template>

<script lang="ts" src="./StoreValueApplicableGoodsAdd.ts">
</script>

<style lang="scss">
  .grow-value-rule-setting {
    background-color: white;
    height: 100%;
    width: 100%;
    overflow: hidden;

    .current-page {
      height: calc(100% - 80px) !important;
      overflow-y: scroll;

      .panel {
        .header {
          font-weight: 500;
          padding: 20px 20px 0 20px;
          font-size: 18px;
        }

        .content {
          padding: 20px;
        }
      }

      .split {
        height: 20px;
        background-color: #EEEFF1;
      }
    }
  }
</style>
