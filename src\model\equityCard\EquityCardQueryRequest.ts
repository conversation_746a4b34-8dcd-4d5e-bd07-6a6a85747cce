/*
 * @Autor: 司浩
 * @Description:
 * @Date: 2023-03-06 17:21:49
 * @LastEditors: 司浩
 * @LastEditTime: 2023-03-08 16:56:23
 * @FilePath: \phoenix-web-ui\src\model\equityCard\EquityCardQueryRequest.ts
 */
import { State } from 'model/common/State'

// 权益卡查询
export default class EquityCardQueryRequest {
  // 是否限制营销中心
  limitMarketingCenter: Nullable<boolean> = null
  // 会员标识类似于
  memberCodeLikes: Nullable<string> = null
  // 营销中心
  marketingCenterEquals: Nullable<string> = null
  // 会员id
  memberIdEquals: Nullable<string> = null
  // 卡名称
  nameEquals: Nullable<string> = null
  // 卡名称模糊查询
  nameLikes: Nullable<string> = null
  // 卡状态
  stateEquals: Nullable<State> = null
  // 卡号
  codeEquals: Nullable<string> = null
  // 发卡门店id
  issueOrgIdEquals: Nullable<string> = null
  // 发卡时间范围起始
  issueTimeBegin: Nullable<string> = null
  // 发卡时间范围结束
  issueTimeEnd: Nullable<string> = null
  // 排序，key表示排序的字段，可选值：code, issueTime；value表示排序方向，可选值为：asc, desc
  sorters: any
  //
  page: Nullable<number> = null
  //
  pageSize: Nullable<number> = null
}
