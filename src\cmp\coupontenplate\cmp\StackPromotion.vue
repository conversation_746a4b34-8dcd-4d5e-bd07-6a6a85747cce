<template>
  <div class="group-mutex-template">
    <el-form-item :label="i18n('叠加优惠')" required>
      <el-form :model="ruleForm" :rules="rules" ref="StackPromotion">
        <div>
          <span class="gray-tips">{{promotionType ==='Card' ? i18n('指参与前台促销后，能否使用当前预付卡') : i18n('指参与前台促销后，能否适用当前优惠券')}}</span>
        </div>
        <el-form-item prop="promotionState" :label="i18n('促销')">
          <el-radio-group v-model="ruleForm.promotionState" @input="doChange">
            <el-radio label="1">{{ formatI18n('/公用/券模板详情/全部促销活动叠加') }}</el-radio>
            <el-radio label="2">{{ formatI18n('/公用/券模板详情/全部促销活动不叠加') }}</el-radio>
            <el-radio label="3">
              <span style="margin-right: 8px;">{{ formatI18n('/公用/券模板详情/指定促销活动叠加') }}</span>
              <el-tooltip v-if="promotionType !=='Card'" content="Bottom center" effect="light" placement="bottom">
                <i class="iconfont  ic-info" style="font-size: 18px;color: #20A0FF;position: relative;top: 2px;"></i>
                <template slot="content">
                  <div style="width: 300px;color: #888">
                    {{formatI18n('/公用/券模板详情/指定促销活动叠加，其它促销活动不叠加，不叠加优先。比如同一个商品参与多个促销活动，有的与券互斥，有的与券可叠加，则该商品不能用券')}}
                  </div>
                </template>
              </el-tooltip>
              <span v-if="ruleForm.promotionState == '3' && ruleForm.promotionList.length == 0" style="color: #1597FF"
                @click.stop="doSelect">{{ formatI18n('/公用/券模板详情/选择促销单') }}</span>
              <span v-if="ruleForm.promotionState == '3' && ruleForm.promotionList.length > 0">{{ formatI18n('/公用/券模板详情/已选择') }}
                <span class="number-text">{{ ruleForm.promotionList.length }}</span>
                {{ formatI18n('/公用/券模板详情/个促销单') }}
                <span class="span-btn" @click.stop="doSelect" style="font-size:13px">
                  {{i18n('修改')}}
                </span>
              </span>
            </el-radio>
            <el-radio label="4">
              <span style="margin-right: 8px;">{{ formatI18n('/公用/券模板详情/指定促销活动不叠加') }}</span>
              <el-tooltip  v-if="promotionType !=='Card'"  content="Bottom center" effect="light" placement="bottom">
                <i class="iconfont  ic-info" style="font-size: 18px;color: #20A0FF;position: relative;top: 2px;"></i>
                <template slot="content">
                  <div style="width: 300px;color: #888">
                    {{formatI18n('/公用/券模板详情/指定促销活动不叠加，其它促销活动叠加，不叠加优先。比如同一个商品参与多个促销活动，有的与券互斥，有的与券可叠加，则该商品不能用券')}}
                  </div>
                </template>
              </el-tooltip>
              <span v-if="ruleForm.promotionState == '4' && ruleForm.promotionList.length == 0" style="color: #1597FF"
                @click.stop="doSelect">{{ formatI18n('/公用/券模板详情/选择促销单') }}</span>
              <span v-if="ruleForm.promotionState == '4' && ruleForm.promotionList.length > 0">{{ formatI18n('/公用/券模板详情/已选择') }}
                <span class="number-text">{{ ruleForm.promotionList.length }}</span>
                {{ formatI18n('/公用/券模板详情/个促销单') }}
                <span class="span-btn" @click.stop="doSelect" style="font-size:13px">
                  {{i18n('修改')}}
                </span>
              </span>
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="memberPrice" :label="i18n('/公用/券模板/微盟适用商品/会员价')">
          <el-radio-group v-model="ruleForm.memberPrice">
            <el-radio :label="false">{{i18n('不叠加')}}</el-radio>
            <el-radio :label="true">{{i18n('叠加')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="artificialDiscount" :label="i18n('人工折扣')">
          <el-radio-group v-model="ruleForm.artificialDiscount">
            <el-radio :label="false">{{i18n('不叠加')}}</el-radio>
            <el-radio :label="true">{{i18n('叠加')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="otherDiscount" :label="i18n('其他优惠')">
          <el-radio-group v-model="ruleForm.otherDiscount">
            <el-radio :label="false">{{i18n('不叠加')}}</el-radio>
            <el-radio :label="true">{{i18n('叠加')}}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </el-form-item>
    <PromotionSelectorDialog :dialogShow.sync="promotionDialogShow" @dialogClose="promotionDialogShow = false" :promotionList="promotionList"
      @save="doSave"></PromotionSelectorDialog>
  </div>
</template>
<script lang="ts">
import { Vue, Component, Prop, Watch } from "vue-property-decorator";
import PromotionShowDialog from "./PromotionShowDialog.vue";
import PromotionSelectorDialog from "./PromotionSelectorDialog.vue";
import CouponInitialApi from "http/coupon/template/CouponTemplateApi";
import I18nPage from "common/I18nDecorator";
import CouponInfo from "model/common/CouponInfo";
import { GoodsFavType } from "model/common/GoodsFavRule";
import CardTemplateApi from "http/card/template/CardTemplateApi";

@Component({
  components: { PromotionShowDialog, PromotionSelectorDialog },
})
@I18nPage({
  prefix: ["/公用/券模板", "/公用/券模板详情"],
  auto: true,
})
export default class Index extends Vue {
  @Prop()
  value: any;
  @Prop({
    type: String,
    default: "add",
  })
  copyFlag: string;
  @Prop({
    type: String,
    default: "",
  })
  templateId: string;
  @Prop({
    type: String,
    required: false, 
    default: "Coupon"  //Coupon | Card  券或者卡模板用,
  })
  promotionType: string;
  $refs: any;
  ruleForm: any = {
    promotionState: "2",
    promotionList: [],
    memberPrice: false,
    artificialDiscount: false,
    otherDiscount: false,
  };
  rules: any;
  promotionShowDialogShow: boolean = false;
  promotionDialogShow: boolean = false;
  firstFlag: boolean = true;

  @Watch("templateId", { deep: true, immediate: true })
  onTemplateIdChange(value: any) {
    if (this.templateId) {
      this.queryPromotion(this.templateId);
    }
  }

  @Watch("value", { deep: true, immediate: true })
  onValueChange(value: CouponInfo) {
    console.log("onValueChange", value);
    if (value) {
      if (value.excludePromotion) {
        if (value.promotionSuperpositionType == "PART") {
          this.ruleForm.promotionState = "3";
        } else {
          //新建券模板时，默认勾选“全部促销活动不叠加”
          if(this.promotionType === 'Card' ) {
            // this.ruleForm.promotionState = this.firstFlag ? "2" : "1";
              if(!this.$route.query.editType ||  ["copy", "edit"].indexOf(this.$route.query.editType as string) < 0) {
                this.ruleForm.promotionState = this.firstFlag ? "2" : "1";
              }else {
                this.ruleForm.promotionState = '1'
              }

              this.firstFlag = false;
          }else if (["copy", "edit"].indexOf(this.$route.query.from as string) < 0 && this.promotionType === 'Coupon' ) {
            this.ruleForm.promotionState = this.firstFlag ? "2" : "1";
            this.firstFlag = false;
          } else {
            //复制和新建时，不默认勾选
            this.ruleForm.promotionState = "1";
          }
        }
      } else {
        if (value.promotionSuperpositionType == "PART") {
          this.ruleForm.promotionState = "4";
        } else {
          this.ruleForm.promotionState = "2";
        }
      }
      if (value.goodsFavRules?.length) {
        // 会员价
        this.ruleForm.memberPrice = value.goodsFavRules.find((item) => item.favType === GoodsFavType.MEMBER_PRICE)?.superimposed || false;
        // 人工折扣
        this.ruleForm.artificialDiscount = value.goodsFavRules.find((item) => item.favType === GoodsFavType.MANUAL_DISCOUNT)?.superimposed || false;
        // 其他优惠
        this.ruleForm.otherDiscount = value.goodsFavRules.find((item) => item.favType === GoodsFavType.OTHER)?.superimposed || false;
      }
    }
  }

  get promotionList() {
    return this.ruleForm.promotionList;
  }
  created() {
    this.rules = {
      promotionState: {
        validator: (rule: any, value: string, callback: any) => {
          if ((this.ruleForm.promotionState == "3" || this.ruleForm.promotionState == "4") && this.ruleForm.promotionList.length == 0) {
            callback(new Error(this.formatI18n("/公用/券模板详情/请选择促销单")));
          }
          callback();
        },
        trigger: ["change", "blur"],
      },
    };
  }
  doSelect() {
    this.promotionDialogShow = true;
  }
  doChange() {
    this.ruleForm.promotionList = [];
    this.validate();
    this.$emit("input", this.doParams());
    this.$emit("change");
  }
  doSave(selectedList: any) {
    this.ruleForm.promotionList = selectedList;
    this.promotionDialogShow = false;
    this.validate();
    this.$emit("input", this.doParams());
    this.$emit("change");
  }
  validate() {
    this.$emit("input", this.doParams());
    this.$emit("change");
    return this.$refs.StackPromotion?.validate();
  }
  doParams() {
    let object: any = {
      excludePromotion: this.ruleForm.promotionState == "1" || this.ruleForm.promotionState == "3",
      promotionSuperpositionType: this.ruleForm.promotionState == "1" || this.ruleForm.promotionState == "2" ? "ALL" : "PART",
      promotion: {
        templateNumber: this.templateId,
        proNums: this.ruleForm.promotionList,
        append: false,
      },
      goodsFavRules: [
        {
          favType: GoodsFavType.MANUAL_DISCOUNT, //人工价格
          superimposed: this.ruleForm.artificialDiscount,
        },
        {
          favType: GoodsFavType.MEMBER_PRICE, //会员价
          superimposed: this.ruleForm.memberPrice,
        },
        {
          favType: GoodsFavType.OTHER, //其他优惠
          superimposed: this.ruleForm.otherDiscount,
        },
      ],
    };
    return object;
  }
  async queryPromotion(templateId: string) {
    try {
      const resp: any = this.promotionType === 'Coupon' ? await CouponInitialApi.getCouponTemplatePromotion(templateId) :   await CardTemplateApi.getCardTemplatePromotion(templateId);
      if (resp.code == 2000 || resp.data) {
        this.ruleForm.promotionList = resp.data || [];
        this.validate();
      } else {
        this.$message.error(resp.msg);
      }
    } catch (err) {
      this.$message.error((err as any).message);
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep.el-radio__input.is-checked + .el-radio__label {
  color: #24272b;
}

::v-deep.el-form-item {
  margin-bottom: 8px !important;
} 
</style>