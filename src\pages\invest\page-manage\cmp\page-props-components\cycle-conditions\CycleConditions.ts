import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import emitter from 'util/emitter';
import { FormMode } from 'model/local/FormMode';
import FormDefUtil from 'util/FormDefUtil';
import I18nPage from 'common/I18nDecorator';

@Component({
  name: 'CycleConditions',
  mixins: [emitter],
  components: {},
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理',
    '/页面/导航设置',
  ],
  auto: true
})
export default class CycleConditions extends Vue {
  @Prop({ type: Boolean, default: false })
  readonly: boolean; // 是否是只读模式
  @Prop({ type: String, default: 'platform' })
  validateName: string; // 组件校验名称，editPage通过该名称进行组件校验及校验信息清除
  @Prop()
  value: any; // 数据模型
  @Prop({ type: String, default: '' })
  label: string; // label名

  @Prop({ type: Boolean, default: true })
  required: boolean; // 是否必填
  @Prop({ type: String })
  relativeValidateName: string; // 联动校验名称
  @Prop()
  formKey: any;
  FormModeType = FormMode;
  $refs: any;
  form: any = {
    timeCycles: [{ times: ['00:00:00', '23:59:59'] }],
  };

  get formMode() {
    if (this.validateName === 'usePlace') {
      return FormDefUtil.getFormMode(this.readonly, this.validateName, this.formKey);
    } else {
      return FormDefUtil.getFormMode(this.readonly, this.validateName + 'usePlace', this.formKey);
    }
  }
  get isShow() {
    return this.value.propCycle;
  }

  handleChange() {
    let { propCycleConditions, ...others } = this.value;
    propCycleConditions = this.toModel();
    this.$emit('input', { propCycleConditions, ...others });
    this.$emit('change', { propCycleConditions, ...others });
    this.validate(() => {});
  }

  @Watch('value')
  handleValue(val:any) {
    if (this.value) {
      this.form.timeCycles = this.toView();
    }
  }

  mounted() {
    if (this.value) {
      this.form.timeCycles = this.toView();
    }
  }
  toView() {
    let arr: Array<any> = [];
    if (this.value.propCycleConditions && this.value.propCycleConditions.length !== 0) {
      arr = this.value.propCycleConditions.map((item:any) => {
        if (item && item.beginTime && item.endTime) {
          return {
            times: [item.beginTime, item.endTime],
          }
        } else {
          return {
            times: ['00:00:00', '23:59:59']
          }
        }
      });
    } else {
      arr = [{ times: ['00:00:00', '23:59:59'] }];
    }

    return arr;
  }
  toModel() {
    let arr: Array<any> = [];
    arr = this.form.timeCycles.map((obj:any) => {
      const times = obj.times
      console.log(times,'timestimes')
      if (times && times !== null) {
        return {
          beginTime: times[0],
          endTime: times[1],
        }
      }
    });    
    return arr;
  }
  delTimeCycle(index) {
    this.form.timeCycles.splice(index, 1);
    // this.$emit('change', this.form.timeCycles);
    this.handleChange();
  }
  addTimeCycle() {
    this.form.timeCycles.push({ times: ['00:00:00', '23:59:59'] });
    // this.$emit('change', this.form.timeCycles);
    this.handleChange();
  }
  beforeDestroy() {
    this['dispatch']('EditPage', 'nf.edit.removeForm', [this]);
  }

  validate(callback) {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.validate(callback);
    }
  }

  clearValidate() {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.clearValidate();
    }
  }
}
