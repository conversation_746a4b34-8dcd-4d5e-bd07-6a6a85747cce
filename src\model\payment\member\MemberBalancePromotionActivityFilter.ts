export default class MemberBalancePromotionActivityFilter {
  // 名称类似于
  nameLikes: Nullable<string> = null
  // 活动主题名称类似与
  topicNameLikes: Nullable<string> = null
  // 活动id类似与
  activityIdLikes: Nullable<string> = null
  // 活动类型等于
  activityTypeEquals: Nullable<string> = null
  // 渠道类型等于
  channelTypeEquals: Nullable<string> = null
  // 状态等于：INITAIL——未审核；UNSTART——未开始；PROCESSING——进行中；STOPED——已结束
  stateEquals: Nullable<string> = null
  // 活动起始日期大于等于
  beginDateGreaterOrEquals: Nullable<Date> = null
  // 活动截止日期小于
  endDateLess: Nullable<Date> = null
  // 页码
  page: Nullable<number> = null
  // 页面大小，大于0
  pageSize: Nullable<number> = null
  // 排序字段
  sortKey: Nullable<string> = null
  // 是否倒序
  desc: Nullable<boolean> = null
  // 商品条码
  barCode: Nullable<string> = null
}