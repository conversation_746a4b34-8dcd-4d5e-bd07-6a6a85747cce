import { Watch } from 'vue-property-decorator';
import { Component, Prop, Vue } from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import EnvUtil from 'util/EnvUtil'
import I18nPage from 'common/I18nDecorator'
import SelectStores from 'cmp/selectStores/SelectStores';
import UploadApi from "http/upload/UploadApi";

@Component({
  name: 'UploadPaidMemberCard',
  components: {
    FormItem,
    SelectStores
  }
})
@I18nPage({
  prefix: [
    "/公用/券模板",
    '/会员/会员资料',
    '/会员/会员资料/会员资料导入',
    '/公用/导入',
    '/公用/按钮'
  ],
  auto: true
})
export default class UploadPaidMemberCard extends Vue {
  $refs: any
  @Prop({
    type: Boolean,
    default: false
  })
  dialogShow: boolean
  fileCount = 0
  uploadHeaders: any = {}
  orgId: string = "";
  // 会员资料模板
  get member() {
    if (location.href.indexOf('localhost') === -1) {
      return 'template_member_paid_card.xlsx'
    } else {
      return 'template_member_paid_card.xlsx'
    }
  }

  get getUploadUrl() {
    return EnvUtil.getServiceUrl() + `v1/member/paidBenefitCardImportExcel?orgId=${this.orgId}`
  }

  @Watch('dialogShow')
  onDialogShowChange() {
    this.orgId = ''
    this.$refs?.upload?.clearFiles()
  }

  created() {
    let locale = sessionStorage.getItem('locale')
    this.uploadHeaders = {
      locale: locale ? locale : "zh_CN",
      time_zone: new Date().getTimezoneOffset(),
      marketingCenter: sessionStorage.getItem("marketCenter"),
    };
    const authorization = EnvUtil.getUniAuthorization()
    if (authorization) {
      this.uploadHeaders.authorization = authorization
    }
  }
  doModalClose(type: string) {
    if (type === 'confirm') {
      if (!this.orgId) {
				this.$message.warning(this.formatI18n("/公用/查询条件/提示", "请选择发生组织") as string);
				return;
			}
      if (this.fileCount > 0) {
        this.$refs.upload.submit();
      } else {
        this.$message.warning(this.i18n('请先选择文件'))
      }
    } else {
      this.$emit('dialogClose')
    }
  }
  doHandleChange(file: any, fileList: any) {
    if (fileList.length > 0) {
      this.fileCount++
    }
  }
  doBeforeClose(done: any) {
    this.orgId = ''
    this.$emit('dialogClose')
    done()
  }
  getSuccessInfo(a: any, b: any, c: any) {
    if (a && a.code === 2000) {
      this.$refs.upload.clearFiles()
      this.fileCount = 0
      this.$emit('dialogClose')
      this.$emit('upload-success')
    }else{
      this.$message.error(a.msg ? a.msg: this.i18n('导入失败，请重新导入'))
    }
  }
  getErrorInfo(a: any, b: any, c: any) {
    this.$message.error(this.i18n('导入失败，请重新导入'))
    this.fileCount = 0
    this.$refs.upload.clearFiles()
  }

  downloadTemplate() {
    UploadApi.getUrl(this.member).then((resp: any) => {
      if (resp && resp.data) {
        window.open(resp.data);
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
}