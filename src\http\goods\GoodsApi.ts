import ApiClient from 'http/ApiClient'
import RSGoods from 'model/common/RSGoods'
import RSGoodsFilter from 'model/common/RSGoodsFilter'
import RSSaveBatchGoodsRequest from 'model/common/RSSaveBatchGoodsRequest'
import Response from 'model/common/Response'
import GoodsImportResult from 'model/goods/GoodsImportResult';
import BarcodeImportResult from "model/goods/BarcodeImportResult";
import BrandCategoryImportResult from "model/goods/BrandCategoryImportResult";
import IdName from 'model/common/IdName'
import GoodsTagFilter from 'model/goods/GoodsTagFilter'

export default class GoodsApi {
  /**
   * 下载导入文件
   *
   */
  static getExcel(): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/goods/getExcel`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入商品条码
   *
   */
  static importBarcodes(body: any): Promise<Response<BarcodeImportResult>> {
    return ApiClient.server().post(`/v1/goods/importBarcodes`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入商品条码
   *
   */
  static importBrandCategory(body: any): Promise<Response<BrandCategoryImportResult>> {
    return ApiClient.server().post(`/v1/goods/importBrandCategory`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入商品
   *
   */
  static importExcel(body: any): Promise<Response<GoodsImportResult>> {
    return ApiClient.server().post(`/v1/goods/importExcel`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询商品
   *
   */
  static query(body: RSGoodsFilter): Promise<Response<RSGoods[]>> {
    return ApiClient.server().post(`/v1/goods/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除商品
   *
   */
  static remove(id: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/goods/remove`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量保存
   *
   */
  static saveBatch(body: RSSaveBatchGoodsRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/goods/saveBatch`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入全场券商品
   *
   */
  static importOverallExcel(body: any): Promise<Response<GoodsImportResult>> {
    return ApiClient.server().post(`/v1/goods/importOverallExcel`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询商品标签(后端暂不支持根据id/name过滤)
   * 
   */
   static getGoodsTag(body: GoodsTagFilter): Promise<Response<IdName[]>> {
    return ApiClient.server().post(`/v1/goods/getGoodsTag`, body, {
    }).then((res) => {
      return res.data
    })
  }
}
