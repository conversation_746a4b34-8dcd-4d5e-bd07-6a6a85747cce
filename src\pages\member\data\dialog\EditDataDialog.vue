<template>
  <div>
    <el-dialog :before-close="doBeforeClose" :close-on-click-modal="false" :title="title" :visible.sync="dialogShow" append-to-body
      class="edit-data-dialog">
      <div class="wrap">
        <el-form :model="ruleForm" :rules="rules" class="demo-ruleForm" label-width="100px" ref="ruleForm">
          <el-row>
            <el-col :span="12">
              <el-form-item :label="formatI18n('/会员/会员资料', '手机号')" prop="mobile">
                <el-input :placeholder="formatI18n('/会员/会员资料', '请输入手机号')" maxlength="18" v-model="ruleForm.mobile"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="!isShowFirstAndLastName">
              <el-form-item :label="formatI18n('/会员/会员资料', '姓名')" prop="name">
                <el-input :placeholder="formatI18n('/会员/会员资料', '请输入姓名')" :maxlength="nameMaxLength ? nameMaxLength : 50"
                  v-model="ruleForm.name"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="isShowFirstAndLastName">
            <el-col :span="12">
              <el-form-item :label="i18n('名')" prop="lastName">
                <el-input :placeholder="i18n('请输入')" maxlength="50" v-model="ruleForm.name"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="i18n('姓')" prop="firstName">
                <el-input :placeholder="i18n('请输入')" maxlength="32" v-model="ruleForm.lastName"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item :label="formatI18n('/会员/会员资料', '性别')" prop="gender">
                <el-select :placeholder="formatI18n('/会员/会员资料', '请选择')" clearable v-model="ruleForm.gender">
                  <el-option :label="formatI18n('/会员/会员资料', '男')" value="男">{{formatI18n('/会员/会员资料', '男')}}</el-option>
                  <el-option :label="formatI18n('/会员/会员资料', '女')" value="女">{{formatI18n('/会员/会员资料', '女')}}</el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="formatI18n('/会员/会员资料', '生日')" prop="birthday">
                <el-date-picker :placeholder="formatI18n('/会员/会员资料', '请选择日期')" format="yyyy-MM-dd" style="width: 100%;" v-model="ruleForm.birthday"
                  value-format="yyyy-MM-dd">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item :label="formatI18n('/会员/会员资料', '身份证号')" prop="idCard">
                <el-input :placeholder="formatI18n('/会员/会员资料', '请输入身份证号')" maxlength="30" v-model="ruleForm.idCard">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="hasMemberOption()">
              <el-form-item :label="formatI18n('/会员/会员资料', '国籍')" prop="nationality">
                <el-input :placeholder="formatI18n('/会员/会员资料/编辑/国籍', '请输入国籍')" maxlength="60" v-model="ruleForm.nationality">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="!hasMemberOption()">
              <el-form-item :label="formatI18n('/会员/会员资料', '学历')" prop="education">
                <el-select :placeholder="formatI18n('/会员/会员资料', '请选择')" clearable v-model="ruleForm.education">
                  <el-option :label="formatI18n('/会员/会员资料', '研究生')" value="研究生">{{formatI18n('/会员/会员资料', '研究生')}}</el-option>
                  <el-option :label="formatI18n('/会员/会员资料', '大学')" value="大学">{{formatI18n('/会员/会员资料', '大学')}}</el-option>
                  <el-option :label="formatI18n('/会员/会员资料', '大专')" value="大专">{{formatI18n('/会员/会员资料', '大专')}}</el-option>
                  <el-option :label="formatI18n('/会员/会员资料', '高中')" value="高中">{{formatI18n('/会员/会员资料', '高中')}}</el-option>
                  <el-option :label="formatI18n('/会员/会员资料', '初中')" value="初中">{{formatI18n('/会员/会员资料', '初中')}}</el-option>
                  <el-option :label="formatI18n('/会员/会员资料', '小学')" value="小学">{{formatI18n('/会员/会员资料', '小学')}}</el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12" v-if="hasMemberOption()">
              <el-form-item :label="formatI18n('/会员/会员资料', '宗教')" prop="religion">
                <el-input :placeholder="formatI18n('/会员/会员资料/编辑/宗教', '请输入宗教')" maxlength="60" v-model="ruleForm.religion">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="hasMemberOption()">
              <el-form-item :label="formatI18n('/会员/会员资料', '学历')" prop="education">
                <el-select :placeholder="formatI18n('/会员/会员资料', '请选择')" clearable v-model="ruleForm.education">
                  <el-option :label="formatI18n('/会员/会员资料', '研究生')" value="研究生">{{formatI18n('/会员/会员资料', '研究生')}}</el-option>
                  <el-option :label="formatI18n('/会员/会员资料', '大学')" value="大学">{{formatI18n('/会员/会员资料', '大学')}}</el-option>
                  <el-option :label="formatI18n('/会员/会员资料', '大专')" value="大专">{{formatI18n('/会员/会员资料', '大专')}}</el-option>
                  <el-option :label="formatI18n('/会员/会员资料', '高中')" value="高中">{{formatI18n('/会员/会员资料', '高中')}}</el-option>
                  <el-option :label="formatI18n('/会员/会员资料', '初中')" value="初中">{{formatI18n('/会员/会员资料', '初中')}}</el-option>
                  <el-option :label="formatI18n('/会员/会员资料', '小学')" value="小学">{{formatI18n('/会员/会员资料', '小学')}}</el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item :label="formatI18n('/会员/会员资料', '行业')" prop="industry">
                <el-select :placeholder="formatI18n('/会员/会员资料', '请选择')" clearable v-model="ruleForm.industry">
                  <el-option :label="formatI18n('/会员/会员资料', '人事/行政/管理')" value="人事/行政/管理">{{formatI18n('/会员/会员资料', '人事/行政/管理')}}</el-option>
                  <el-option :label="formatI18n('/会员/会员资料', '建筑/房产/物业')" value="建筑/房产/物业">{{formatI18n('/会员/会员资料', '建筑/房产/物业')}}</el-option>
                  <el-option :label="formatI18n('/会员/会员资料', '消费品/贸易/物流')" value="消费品/贸易/物流">{{formatI18n('/会员/会员资料', '消费品/贸易/物流')}}</el-option>
                  <el-option :label="formatI18n('/会员/会员资料', '咨询/法律/认证')" value="咨询/法律/认证">{{formatI18n('/会员/会员资料', '咨询/法律/认证')}}</el-option>
                  <el-option :label="formatI18n('/会员/会员资料', '生产/制造/营运/采购')" value="生产/制造/营运/采购">{{formatI18n('/会员/会员资料', '生产/制造/营运/采购')}}</el-option>
                  <el-option :label="formatI18n('/会员/会员资料', '生物/制药/医疗/护理')" value="生物/制药/医疗/护理">{{formatI18n('/会员/会员资料', '生物/制药/医疗/护理')}}</el-option>
                  <el-option :label="formatI18n('/会员/会员资料', '教育/培训/翻译')" value="教育/培训/翻译">{{formatI18n('/会员/会员资料', '教育/培训/翻译')}}</el-option>
                  <el-option :label="formatI18n('/会员/会员资料', '科研/环保/休闲/其他')" value="科研/环保/休闲/其他">{{formatI18n('/会员/会员资料', '科研/环保/休闲/其他')}}</el-option>
                  <el-option :label="formatI18n('/会员/会员资料', 'IT/互联网/通信/电子')"
                    value="IT/互联网/通信/电子">{{formatI18n('/会员/会员资料', 'IT/互联网/通信/电子')}}</el-option>
                  <el-option :label="formatI18n('/会员/会员资料', '金融/投资/财会')" value="金融/投资/财会">{{formatI18n('/会员/会员资料', '金融/投资/财会')}}</el-option>
                  <el-option :label="formatI18n('/会员/会员资料', '广告/媒体/出版/艺术')" value="广告/媒体/出版/艺术">{{formatI18n('/会员/会员资料', '广告/媒体/出版/艺术')}}</el-option>
                  <el-option :label="formatI18n('/会员/会员资料', '市场/销售/客服')" value="市场/销售/客服">{{formatI18n('/会员/会员资料', '市场/销售/客服')}}</el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="formatI18n('/会员/会员资料', '年收入')" prop="annualIncome">
                <el-select :placeholder="formatI18n('/会员/会员资料', '请选择')" clearable v-model="ruleForm.annualIncome">
                  <el-option :label="formatI18n('/会员/会员资料', '5万以下')" value="5万以下">{{formatI18n('/会员/会员资料', '5万以下')}}</el-option>
                  <el-option :label="formatI18n('/会员/会员资料', '5万-15万')" value="5万-15万">{{formatI18n('/会员/会员资料', '5万-15万')}}</el-option>
                  <el-option :label="formatI18n('/会员/会员资料', '15万-30万')" value="15万-30万">{{formatI18n('/会员/会员资料', '15万-30万')}}</el-option>
                  <el-option :label="formatI18n('/会员/会员资料', '30万以上')" value="30万以上">{{formatI18n('/会员/会员资料', '30万以上')}}</el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item :label="formatI18n('/会员/会员资料', '爱好')" prop="hobbies">
                <el-input :placeholder="formatI18n('/会员/会员资料', '请输入爱好')" maxlength="120" v-model="ruleForm.hobbies"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="formatI18n('/会员/会员资料', '备用手机号')" prop="spareMobile">
                <el-input :placeholder="formatI18n('/会员/会员资料', '请输入备用手机号')" maxlength="18" v-model="ruleForm.spareMobile"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item :label="formatI18n('/会员/会员资料', '邮箱')" prop="email">
                <el-input :placeholder="formatI18n('/会员/会员资料', '请输入邮箱')" :disabled="!editConfig.emailEnable" maxlength="64"
                  v-model="ruleForm.email"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item :label="formatI18n('/会员/会员资料', '地址')" prop="address">
                <AddressSelector style="width: 100%" :full="true" value-type="IdName" v-model="ruleForm.address"></AddressSelector>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="cur-address">
                <el-input :placeholder="formatI18n('/会员/会员资料', '请输入地址')" maxlength="254" v-model="ruleForm.addressInfo">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 自定义字段 -->
          <el-row>
            <el-col :span="12" v-for="(item,index) in enableCustomMemberAttr" :key="index">
              <el-form-item :label="item.customFieldName">
                <!-- 输入框 -->
                <el-input v-if="item.fieldType === 'text'" :placeholder="item.tips" v-model="customFieldValues[item.customFieldName]"></el-input>
                <!-- 选择框 -->
                <el-select v-else-if="item.fieldType === 'select'" @change="$forceUpdate()" :placeholder="item.tips" v-model="customFieldValues[item.customFieldName]">
                  <el-option v-for="(optionItem, ind) in item.fieldOptions" :key="ind" :label="optionItem" :value="optionItem"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row v-if="hasMemberOption()">
            <el-row>
              <el-col :span="12">
                <el-form-item :label="formatI18n('/会员/会员资料', '员工号')" prop="employeeID">
                  <el-input :placeholder="formatI18n('/会员/会员资料/编辑/员工号', '请输入员工号')" maxlength="60" v-model="ruleForm.employeeID"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="formatI18n('/会员/会员资料', '职位')" prop="office">
                  <el-input :placeholder="formatI18n('/会员/会员资料/编辑/职位', '请输入职位')" maxlength="60" v-model="ruleForm.office"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item :label="formatI18n('/会员/会员资料', '变更说明')" prop="remark">
                <el-input :placeholder="formatI18n('/会员/会员资料', '请输入不超过50个字')" maxlength="50" type="textarea" v-model="ruleForm.remark"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="dialog-footer" slot="footer">
        <el-button @click="doCancel">{{formatI18n('/公用/按钮', '取消')}}</el-button>
        <el-button @click="doModalClose" size="small" type="primary">{{formatI18n('/公用/按钮', '确定')}}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" src="./EditDataDialog.ts">
</script>

<style lang="scss">
.edit-data-dialog {
  display: flex;
  align-items: center;
  justify-content: center;
  .el-dialog {
    height: 800px !important;
    overflow: auto !important;
  }
  .wrap {
    margin: 30px;
    .el-row {
      margin-top: 10px !important;
    }
    .el-select {
      width: 100%;
    }
    .multi-address .el-col {
      &:first-child {
        padding-left: 0 !important;
      }
      &:last-child {
        padding-right: 0 !important;
      }
    }
  }
  .cur-address {
    .el-form-item__content {
      margin-left: 20px !important;
    }
  }
}
</style>