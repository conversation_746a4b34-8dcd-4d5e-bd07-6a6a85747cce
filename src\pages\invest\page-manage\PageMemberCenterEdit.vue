<template>
  <div class="page-paidMember-edit">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button size="large" type="primary"
          v-if="hasOptionPermission('/设置/小程序装修/页面管理', '编辑') && hasOptionPermission('/设置/小程序装修/页面管理', '发布')"
          @click="preserve(true)">
          {{ i18n('保存并发布') }}
        </el-button>
        <el-button size="large" @click="preserve(false)">{{ i18n('/公用/按钮/保存') }}</el-button>
        <el-button size="large" @click="goBack">{{ i18n('取消') }}</el-button>
      </template>
    </BreadCrume>
    <!-- 设置 -->
    <div class="panel">
      <div class="panel-left">
        <el-form :model="imgData" :rules="rules" ref="form" label-width="120px">
          <!-- 页面设置 -->
          <div class="backDecoration">
            <div class="title">{{ i18n('页面设置') }}</div>
            <div class="content">
              <el-form-item :label="i18n('页面名称')" prop="propTitle">
                <el-input v-model="imgData.propTitle" maxlength="30" placeholder="请输入页面名称" />
              </el-form-item>
              <el-form-item :label="i18n('卡面元素')" prop="propCardElement">
                <!-- <el-checkbox v-model="showCardNumber" @change="(val) => cardElementChange(val, 'showCardNumber')">
                  {{ i18n('隐藏卡号') }}
                </el-checkbox> -->
                <el-checkbox v-model="showGrowth" @change="(val) => cardElementChange(val, 'showGrowth')">
                  {{ i18n('隐藏成长值') }}
                </el-checkbox>
                <el-checkbox v-model="showPeriod" @change="(val) => cardElementChange(val, 'showPeriod')">
                  {{ i18n('隐藏有效期') }}
                </el-checkbox>
              </el-form-item>
              <el-form-item :label="i18n('卡背景展示')" prop="propBackgroundType">
                <el-radio-group v-model="imgData.propBackgroundType" @change="propBackgroundTypeChange">
                  <el-radio label="unified"> {{ i18n('统一背景') }}</el-radio>
                  <el-radio label="byGrade"> {{ i18n('按等级设置背景') }}</el-radio>
                </el-radio-group>
                <div class="carousel" v-if="imgData.propBackgroundType == 'unified'">
                  <div>
                    <el-form-item label="背景图片" prop="propUnifiedCardBackground.backgroundImage">
                      <upload-img class="uploader-imgbox" v-model="imgData.propUnifiedCardBackground.backgroundImage"
                        :signature-result="credential" width="80px" height="80px" :imgFormat="imgFormat"></upload-img>
                      <div style="line-height: 26px;">{{ i18n('支持.jpg .png格式，推荐尺寸1041*593px，大小不超过1MB') }}</div>
                    </el-form-item>

                    <el-form-item label="文字颜色" prop="propUnifiedCardBackground.fontColor"
                      style="margin-left: 8px; margin-top: 20px;">
                      <el-color-picker style="width: 100px" v-model="imgData.propUnifiedCardBackground.fontColor"
                        size="small"></el-color-picker>
                    </el-form-item>
                  </div>
                </div>
                <div v-else-if="imgData.propBackgroundType == 'byGrade'">
                  <div style="display: flex;" v-for="(item, index) in imgData.propByGradeCardBackgrounds" :key="index">
                    <div class="gradeName">{{ gradeItems[index] && gradeItems[index].name || '' }}：</div>
                    <div class="carousel">
                      <div>
                        <el-form-item label="背景图片" :rules="rules.propByGradeCardBackgroundsBackgroundImage"
                          :prop="`propByGradeCardBackgrounds.${index}.backgroundImage`">
                          <upload-img v-model="item.backgroundImage" :signature-result="credential" width="80px"
                            height="80px" :imgFormat="imgFormat"></upload-img>
                          <div style="line-height: 26px;">
                            {{ i18n('支持.jpg .png格式，推荐尺寸1041*593px，大小不超过1MB') }}
                            <!-- <el-button type="text">示例</el-button> -->
                          </div>
                        </el-form-item>

                        <el-form-item label="文字颜色" :rules="rules.propByGradeCardBackgroundsFontColor"
                          :prop="`propByGradeCardBackgrounds.${index}.fontColor`"
                          style="margin-left: 8px; margin-top: 20px;">
                          <el-color-picker style="width: 100px" v-model="item.fontColor" size="small"></el-color-picker>
                        </el-form-item>
                      </div>
                    </div>
                  </div>
                </div>
              </el-form-item>
              <el-form-item :label="i18n('会员动态码')">
                <el-checkbox v-model="imgData.propMemberCode.showBarcode">
                  {{ i18n('条形码') }}
                </el-checkbox>
                <el-checkbox v-model="imgData.propMemberCode.showQrcode">
                  {{ i18n('二维码') }}
                </el-checkbox>
                <div style="color: #a1a6ae;">{{ i18n('不选则不展示会员动态码') }}</div>
              </el-form-item>
              <el-form-item :label="i18n('会员权益')" prop="propMemberBenefits.imageType">
                <template v-slot:label>
                  {{ i18n("会员权益") }}
                  <el-tooltip placement="top-start" effect="light" style="padding:0 !important;">
                    <div slot="content">
                      {{ i18n("1. 默认图片：展示当前等级关联的权益，包括权益图标和权益名称。") }}
                      <br />
                      {{ i18n("2. 自定义图片：展示自定义图片，不展示当前等级关联的权益") }}
                    </div>
                    <el-button style="padding: 0; border: none"><i class="el-icon-warning"
                        style="color: #999999" /></el-button>
                  </el-tooltip>
                </template>
                <el-radio-group v-model="imgData.propMemberBenefits.imageType" @change="imageTypeChange">
                  <el-radio label="default"> {{ i18n('默认图片') }}</el-radio>
                  <el-radio label="custom"> {{ i18n('自定义图片') }}</el-radio>
                </el-radio-group>
                <div v-if="imgData.propMemberBenefits.imageType === 'custom'">
                  <div>{{ i18n('建议尺寸1041*480px，支持.jpg .gif .png格式') }}</div>
                  <div style="display: flex;justify-content: space-between; flex-wrap: wrap;">
                    <el-form-item :prop="`propMemberBenefits.gradeImages.${index}.imageUrl`"
                      :rules="rules.propMemberBenefitsGradeImages"
                      v-for="(item, index) in imgData.propMemberBenefits.gradeImages" :key="index" style="width: 100px;
                      margin-bottom: 10px;">
                      <div style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{{ gradeItems[index] && gradeItems[index].name || '' }}：
                      </div>
                      <upload-img v-model="item.imageUrl" :signature-result="credential" width="80px" height="80px"
                        :imgFormat="imgFormat"></upload-img>
                    </el-form-item>
                  </div>
                </div>
              </el-form-item>
              <el-form-item :label="i18n('可见等级配置')">
                <el-radio-group v-model="imgData.propVisibleLevel">
                  <el-radio label="all"> {{ i18n('全部等级') }}</el-radio>
                  <el-radio label="only"> {{ i18n('仅当前等级') }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
          </div>
          <!-- 推荐位设置 -->
          <div class="backDecoration">
            <div class="title">{{ i18n('营销位设置') }}</div>
            <div class="featured_first">
              <div class="switch">
                <el-switch v-model="imgData.propMarketingPosition.enabled">
                </el-switch>
                {{ i18n('启动推荐位') }}
              </div>
              <div v-show="imgData.propMarketingPosition.enabled">
                <el-button plain size="medium" @click="doAddFeaturedFirst"
                  v-if="imgData.propMarketingPosition.items.length < 10">+添加推荐位</el-button>
                <span class="position-tip">{{ i18n('最多添加10个推荐位') }}</span>
                <el-table :data="imgData.propMarketingPosition.items" style="width: 100%; margin-top: 12px">
                  <el-table-column prop="name" :label="i18n('推荐位名称')">
                    <template slot-scope="scope">
                      <el-form-item :prop="`propMarketingPosition.items.${scope.$index}.name`"
                        :rules="imgData.propMarketingPosition.enabled && rules.propMarketingPositionName || []">
                        <el-input v-model="scope.row.name" placeholder="请输入" maxlength="6" />
                      </el-form-item>

                    </template>
                  </el-table-column>
                  <el-table-column prop="prompt" :label="i18n('提示语')">
                    <template slot-scope="scope">
                      <el-form-item :prop="`propMarketingPosition.items.${scope.$index}.prompt`"
                        :rules="imgData.propMarketingPosition.enabled && rules.propMarketingPositionPrompt || []">
                        <el-input v-model="scope.row.prompt" placeholder="请输入" maxlength="6" />
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column :label="i18n('跳转链接')">
                    <template slot-scope="scope">
                      <div class="link-box">
                        <el-form-item :prop="`propMarketingPosition.items.${scope.$index}.jumpPageInfo`"
                          :rules="imgData.propMarketingPosition.enabled && rules.propMarketingPositionJumpPageInfo || []">
                          <page-jump ref="jumpPage" v-model="scope.row.jumpPageInfo" @change="changeLink($event, scope.row)" :showTitile="true"></page-jump>
                        </el-form-item>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column :label="i18n('操作')">
                    <template slot-scope="scope">
                      <el-button type="text" size="small" 
                     :disabled="imgData.propMarketingPosition.items.length <= 1" @click="doRemoveProject(scope.$index)">{{ i18n('移除')
                      }}</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

            </div>
          </div>
        </el-form>
      </div>
      <div class="panel-right">
        <div class="pageBox page-place">
          <div class="page-place-box-top" :style="{ backgroundImage: pageTopImage }">
            {{ imgData.propTitle ? imgData.propTitle : i18n('会员中心') }}
          </div>
          <div class="carousel-container">
            <div class="carousel-track" :style="trackStyle" @mousedown="handleMouseDown" @mousemove="handleMouseMove"
              @mouseup="handleMouseUp" @mouseleave="handleMouseUp">
              <div v-for="(item, index) in gradeItems" :key="index" class="carousel-item"
                :class="{ active: currentIndex === index }" :style="getItemStyle(index)">
                <div class="item-content">
                  <div class="card-info"
                    :style="{ backgroundImage: `url('${imgData.propBackgroundType === 'unified' ? imgData.propUnifiedCardBackground.backgroundImage : imgData.propByGradeCardBackgrounds[index] && imgData.propByGradeCardBackgrounds[index].backgroundImage}')`, color: imgData.propBackgroundType === 'unified' ? imgData.propUnifiedCardBackground.fontColor : imgData.propByGradeCardBackgrounds[index] && imgData.propByGradeCardBackgrounds[index].fontColor }">
                    <div class="card-name">
                      <div class="name">{{ item.name }}</div>
                      <div class="level-box">
                        <div class="level"
                          :style="{ backgroundColor: imgData.propBackgroundType === 'unified' ? imgData.propUnifiedCardBackground.fontColor : imgData.propByGradeCardBackgrounds[index] && imgData.propByGradeCardBackgrounds[index].fontColor }">
                          {{ i18n('当前等级') }}
                        </div>
                        <span>{{ i18n('当前等级') }}</span>
                      </div>
                    </div>
                    <div class="card-number">
                      <div v-if="imgData.propCardElement.showCardNumber">84946515164466</div>
                      <div v-if="imgData.propCardElement.showPeriod">有效期至2025-12-31</div>
                    </div>
                    <div class="growth" v-if="imgData.propCardElement.showGrowth">
                      <span>当前成长值200，距升级还需100 &nbsp; ></span>
                      <div class="progress-container">
                        <div class="progress-bar"
                          :style="{ width: '50%', backgroundColor: imgData.propBackgroundType === 'unified' ? imgData.propUnifiedCardBackground.fontColor : imgData.propByGradeCardBackgrounds[index] && imgData.propByGradeCardBackgrounds[index].fontColor }">
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="card-main">
            <!-- 续费优惠 -->
            <div class="equity-box">
              <div class="equity-title">可享权益</div>
              <div class="equity-container">
                <img src="@/assets/image/invest/entitlement_img.png" alt=""
                  v-if="imgData.propMemberBenefits.imageType === 'default'">
                <img :src="imgData.propMemberBenefits.gradeImages[currentIndex] && imgData.propMemberBenefits.gradeImages[currentIndex].imageUrl" alt="" v-else>
              </div>
            </div>
            <div class="member-code" v-if="imgData.propMemberCode.showBarcode || imgData.propMemberCode.showQrcode">
              <div class="image" v-if="imgData.propMemberCode.showBarcode">
                <img src="@/assets/image/invest/img_barcode.png" alt="">
              </div>
              <div class="img_qrcode" v-if="imgData.propMemberCode.showQrcode">
                <img src="@/assets/image/invest/img_qrcode.png" alt="">
              </div>
            </div>

            <!-- 推荐位 -->
            <div class="notice"
              v-if="imgData.propMarketingPosition.enabled && imgData.propMarketingPosition.items.length">
              <div class="notice-item" v-for="(item, index) in imgData.propMarketingPosition.items" :key="index">
                <span class="name">{{ item.name }}</span>
                <span class="hint">{{ item.prompt }} ></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./PageMemberCenterEdit.ts">
</script>

<style lang="scss" scoped>
.page-paidMember-edit {
  width: 100%;

  .panel {
    border-radius: 8px;
    padding: 24px;
    display: flex;

    .panel-left {
      width: 850px;
      margin-right: 12px;

      .featured_first {
        .switch {
          font-size: 14px;
          color: #24272B;
          margin: 12px 0 8px;
        }
        .position-tip {
          font-size: 12px;
          color: #bbb;
          margin-left: 10px;
        }
        .el-form-item {
          transform: translateY(6px);
        }
        ::v-deep .el-form-item__content {
          margin-left: 0 !important;
        }
      }

      .uploader-imgbox {
        display: flex;
        flex-wrap: wrap;
        background: transparent;
        ::v-deep .imgbox-wrap {
          position: relative;
          width: 80px;
          height: 80px;
          border-radius: 4px;
          overflow: hidden;
          margin-right: 12px;
          margin-bottom: 12px;
          .imgbox-img {
            width: 80px;
            height: 80px;
          }
        }
      }
    }

    .title {
      font-weight: 600;
      font-size: 16px;
      color: #242633;
      line-height: 24px;
    }

    .backDecoration {
      padding: 26px 20px 40px;
      margin-bottom: 12px;
      border-radius: 8px;
      background: #ffffff;
    }

    .content {
      margin-left: 8px;
      margin-top: 6px;
    }

    .gradeName {
      width: 110px;
      text-align: right;
      margin-top: 15px;
    }

    .carousel {
      background: #f0f2f6;
      border-radius: 4px;
      padding: 12px;
      margin-top: 20px;
      font-weight: 400;
      font-size: 13px;
      color: #a1a6ae;
      line-height: 18px;
    }

    .panel-right {
      position: sticky;
      top: 0;
      /* 吸附到页面顶部 */
      border-radius: 8px;
      background: #ffffff;
      padding: 30px 25px;
      width: 400px;
      height: 764px;
      box-sizing: border-box;

      .pageBox {
        width: 100%;
        height: 100%;
        background: #f0f2f6;
        border: 8px solid #000000;
        border-radius: 50px;
        box-sizing: border-box;
        overflow: hidden;
        overflow: auto;

        &::-webkit-scrollbar {
          display: none;
          /* 隐藏滚动条 */
        }
      }

      .page-place {
        display: flex;
        width: 100%;
        height: 100%;
        flex-direction: column;


        &-box {
          flex: 1;
          width: 400px;
          margin: auto;
          overflow: hidden;
          height: 100%;

          &-top {
            height: 74px;
            color: #242633;
            font-size: 16px;
            font-weight: 600;
            line-height: 111px;
            text-align: center;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-color: #fff;
            white-space: nowrap;
          }
        }
      }


      .card-scroll {
        margin-top: 16px;
      }

      .card-main {
        padding: 12px;
        padding-top: 0;
      }

      .member-code {
        width: 100%;
        padding: 16px;
        padding-bottom: 5px;
        box-sizing: border-box;
        background: #FFFFFF;
        border-radius: 16px;
        margin-top: 12px;
        text-align: center;

        .image {
          width: 100%;
          margin-bottom: 5px;

          img {
            width: 100%;
          }
        }

        .img_qrcode {
          img {
            width: 192px;

          }
        }


      }

      .equity-box {
        padding: 10px;
        background: #fff;
        border-radius: 10px;

        .equity-title {
          font-size: 15px;
          font-weight: 600;
        }

        // 可享权益
        .equity-container {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 90px;
          width: 100%;
          overflow-x: auto;

          img {
            height: 100%;
            pointer-events: none;
          }

          // &::-webkit-scrollbar {
          //   width: 3px;
          // }
        }
      }


      .carousel-container {
        overflow: hidden;
        position: relative;
        width: 334px;
        min-height: 170px;
        margin: 0 auto;
      }

      .carousel-track {
        display: flex;
        height: 100%;
        align-items: center;
        position: relative;
      }

      .carousel-item {
        position: absolute;
        border-radius: 8px;
        transition: all 0.3s ease;
        cursor: pointer;
        will-change: transform;
      }

      .carousel-item.active {
        z-index: 2;
      }

      .item-content {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: #333;
        pointer-events: none;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .card-info {
        width: 290px;
        min-height: 130px;
        padding: 15px;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        position: relative;
        border-radius: 10px;

        .card-name {
          display: flex;
          align-items: center;

          .name {
            font-size: 19px;
            font-weight: 600;
          }

          .level-box {
            position: relative;
            margin-left: 8px;
            border-radius: 15px;

            span {
              font-size: 13px;
              position: absolute;
              top: 45%;
              left: 50%;
              transform: translate(-50%, -50%);
              opacity: 0.7;
              white-space: nowrap;
            }
          }

          .level {
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 13px;
            opacity: 0.2;
          }
        }

        .card-number {
          font-size: 12px;
          line-height: 16px;
          margin-bottom: 20px;
          opacity: 0.5;
        }

        .growth {
          font-size: 12px;
          position: absolute;
          bottom: 12px;
          font-weight: 300;
          width: 85%;

          span {
            line-height: 18px;
          }

          .progress-container {
            width: 100%;
            height: 8px;
            background-color: rgba(0, 0, 0, .2);
            border-radius: 15px;
            overflow: hidden;
          }

          .progress-bar {
            height: 100%;
            border-radius: 5px;
            transition: width 0.3s ease-in-out;
          }
        }
      }

      // 使用须知
      .notice {
        background: #fff;
        border-radius: 10px;
        padding: 0 12px;

        margin-top: 10px;

        .notice-item {
          display: flex;
          justify-content: space-between;
          padding: 15px 0;
          font-size: 14px;
          border-bottom: 1px solid #f2f2f2;

          .hint {
            color: #999;
          }
        }

        .notice-item:last-child {
          border-bottom: none;
        }
      }
    }
  }
}
</style>