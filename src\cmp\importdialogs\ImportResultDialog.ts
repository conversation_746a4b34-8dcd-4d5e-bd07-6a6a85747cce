import {Component, Vue} from 'vue-property-decorator'

@Component({
  name: 'ImportResultDialog',
  components: {}
})
export default class ImportResultDialog extends Vue {
  data: any = {}
  dialogShow: boolean = false

  show(data: any) {
    this.data = data
    this.dialogShow = true
  }

  doModalClose() {
    this.dialogShow = false
  }

  doDownload() {
    window.open(this.data.backUrl, '_blank')
  }
}
