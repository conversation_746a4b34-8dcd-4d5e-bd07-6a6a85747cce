import { Component, Inject, Prop } from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import AbstractSelectDialog from './AbstractSelectDialog'
import CouponTemplate from 'model/coupon/template/CouponTemplate'
import CouponTemplateFilter from 'model/coupon/template/CouponTemplateFilter'
import CouponTemplateApi from 'http/coupon/template/CouponTemplateApi'
import ChannelRange from 'model/common/ChannelRange'
import SelectStoreActiveDtlDialog from 'pages/deposit/mbrdeposit/active/dialog/SelectStoreActiveDtlDialog.vue'
import CouponItem from 'model/common/CouponItem'
import BrowserMgr, { SessionStorage } from 'mgr/BrowserMgr'
import Channel from 'model/common/Channel'
import RSChannelManagement from 'model/common/RSChannelManagement'
import CommonUtil from 'util/CommonUtil'
import CouponTemplateTagApi from 'http/coupon/template/CouponTemplateTagApi'
import CouponTemplateTagFilter from 'model/coupon/CouponTemplateTagFilter'
import CouponTemplateTag from 'model/coupon/CouponTemplateTag'
import CouponTemplateTagRelation from 'model/coupon/CouponTemplateTagRelation'
import RSChannelManagementFilter from 'model/common/RSChannelManagementFilter'
import ChannelManagementApi from 'http/channelmanagement/ChannelManagementApi'

@Component({
  name: 'CouponTemplateSelectorDialog',
  components: {
    FormItem,
    SelectStoreActiveDtlDialog
  }
})
export default class CouponTemplateSelectorDialog extends AbstractSelectDialog<
  CouponTemplate
> {
  child: CouponItem = new CouponItem()
  couponDialogShow = false
  @Prop({ default: () => new CouponTemplateFilter() })
  filter: CouponTemplateFilter
  @Prop()
  couponType: string
  @Prop({
    default: false
  })
  hideSource: Boolean
  @Prop({
    default: false
  })
  showActivityReference: Boolean
  @Prop({
    default: false
  })
  showEquityCoupon: Boolean
  @Inject({
    from: 'isShowTag',
    default: false
  })
  isShowTag: boolean; //table是否展示"标签"
  @Inject({
    from: 'hideState',
    default: false
  })
  hideState: boolean; //是否去除校验“券状态”

  @Prop({
    type: Array,
    default() {
      return []
    }
  })
  excludedTypes: string[]

  @Prop({ type: Array, default() { return [] } }) selectCouponTypesIn: string[] //可以选择哪些类型的券，这个字段的优先级比excludedTypes高

  labelWidthMap: any = {
    zh_CN: '100px',
    en_US: '180px',
    default: '180px'
  }
  isRandomCoupon: boolean = false
  enablePointsExchangeCouponDisplay: any = false
  enableEquityCouponDisplay: any = false
  mustHaveTagUuids: string[] = []
  tagList: CouponTemplateTag[] = []
  channels: RSChannelManagement[] = [];

  created() {
    console.log("1111" + this.showEquityCoupon);
    let sysConfig = BrowserMgr.LocalStorage.getItem('sysConfig')
    if (sysConfig) {
      this.isRandomCoupon = sysConfig.enableRandomCashCouponDisplay
    }
    this.filter.stateEquals = 'EFFECTED'
    this.enablePointsExchangeCouponDisplay = JSON.parse(
      localStorage.getItem('sysConfig') as string
    ).enablePointsExchangeCouponDisplay
    this.enableEquityCouponDisplay = JSON.parse(
      localStorage.getItem('sysConfig') as string
    ).enableEquityCouponDisplay
    this.getChannels();
    this.getTagList()
  }

  get labelWidth() {
    return this.labelWidthMap[
      CommonUtil.getLocale('locale') as string
    ] as string
  }

  storeRangeStr(row: CouponTemplate) {
    if (row.storeRangeType === 'ALL' || (row.storeRangeType === "PART" && row.allMarketingCenterStores)) {
      return this.formatI18n('/公用/券模板', '全部门店')
    } else {
      return this.formatI18n("/储值/预付卡/卡模板/列表页面/部分门店")
    }
  }

  checkable(event: boolean, index: number) {
    if (
      this.couponType === 'wxCoupon' &&
      ((this.currentList[index] as any).type === 'goods' ||
        (this.currentList[index] as any).type === 'goods_discount')
    ) {
      return false
    }
    return true
  }

  reset(): void {
    this.filter.nameLikes = null
    this.filter.numberEquals = null
    this.filter.typeEquals = null
    this.filter.mustHaveTagUuids = []
    this.mustHaveTagUuids = []
  }

  getId(ins: CouponTemplate): string {
    // @ts-ignore
    return ins.number
  }

  getName(ins: CouponTemplate): string {
    // @ts-ignore
    return ins.name
  }

  getResponseData(response: any): any {
    let channel = SessionStorage.getItem('channels')
    if (channel) {
      if (
        response &&
        response.data &&
        response.data.templateList &&
        response.data.templateList.length > 0
      ) {
        response.data.templateList.forEach((item: CouponTemplate) => {
          if (
            item &&
            item.channels &&
            item.channels.channels &&
            item.channels.channels.length > 0
          ) {
            item.channels.channels.forEach((child: Channel) => {
              if (channel && channel.length > 0) {
                channel.forEach((sub: RSChannelManagement) => {
                  if (
                    sub.channel &&
                    child.type === sub.channel.type &&
                    sub.channel.id === child.id
                  ) {
                    child.type = sub.name
                  }
                })
              }
            })
          }
        })
      }
    }
    return response.data.templateList
  }

  getChannel(channel: ChannelRange) {
    let str: string = "";
    if (channel.channelRangeType === 'ALL') {
      str = this.formatI18n('/公用/券模板/用券渠道', '全部渠道')
      return str
    } else {
      if (this.channels && this.channels.length > 0) {
        if (channel?.channels?.length > 0) {
          channel.channels.forEach((sub: Channel) => {
            this.channels.forEach((item: RSChannelManagement) => {
              if (item && item.channel && sub.id === item.channel.id && sub.type === item.channel.type) {
                str += `${item.name}，`;
              }
            });
          });
        }
      }
      str = str.substring(0, str.length - 1);
    }
    return str;
  }

  getTypeTotalCamel(type: any) {
    let str = ''
    switch (type) {
      case 'all_cash':
        str = 'allCashTotal'
        break
      case 'goods_cash':
        str = 'goodsCashTotal'
        break
      case 'all_discount':
        str = 'allDiscountTotal'
        break
      case 'goods_discount':
        str = 'goodsDiscountTotal'
        break
      case 'rfm_type':
        str = 'rfmTypeTotal'
        break
      case 'exchange_goods':
        str = 'exchangeGoodsTotal'
        break
      case 'special_price':
        str = 'specialCouponTotal'
        break
      case 'goods':
        str = 'goodsTotal'
        break
      case 'freight':
        str = 'freightTotal'
        break
      case 'random_cash':
        str = 'randomTotal'
        break
      case 'points':
        str = 'pointExchangeTotal'
        break
      default:
        break
    }
    return str
  }

  getResponseTotal(response: any): any {
    // if (this.excludedTypes && this.excludedTypes.length > 0) {
    // 	let total = response.data.stats.total
    // 	this.excludedTypes.forEach((item: any) => {
    // 		total = total - response.data.stats[this.getTypeTotalCamel(item)]
    // 	})
    // 	return total
    // } else {
    if (this.filter.typeIn && this.filter.typeIn.length > 0) {
      let total = 0
      this.filter.typeIn.forEach((item: any) => {
        total += response.data.stats[this.getTypeTotalCamel(item)]
      })
      return total
    } else {
      return response.data.stats.total
    }

    // }
  }

  queryFun(): Promise<any> {
    this.filter.dialogQuery = true
    this.filter.page = this.page.currentPage - 1
    this.filter.pageSize = this.page.size
    this.filter.mustHaveTagUuids = this.mustHaveTagUuids ? this.mustHaveTagUuids : []
    if (this.excludedTypes && this.excludedTypes.length > 0) {
      this.filter.typeExcluded = [...this.excludedTypes] // 后端只支持一个，期待有缘人接盘
    }
    // 系统涉及到券的地方都要过滤随机金额券，除了叠加促销
    if (!this.isRandomCoupon) {
      this.filter.typeExcluded = Array.from(
        new Set([...this.excludedTypes, 'random_cash'])
      )
    }
    console.log('-----------------' + this.isRandomCoupon)
    console.log(this.enablePointsExchangeCouponDisplay)
    if (this.enablePointsExchangeCouponDisplay === false) {
      // this.filter.typeExcluded!.push('points')
      this.filter.typeExcluded = Array.from(
        new Set([...this.excludedTypes, 'points'])
      )
    }
    if (this.enableEquityCouponDisplay === false) {
      // this.filter.typeExcluded!.push('points')
      if (this.filter.typeExcluded && this.filter.typeExcluded.length > 0) {
        this.filter.typeExcluded.push('equity') // 后端只支持一个，期待有缘人接盘
      } else {
        this.filter.typeExcluded = Array.from(
          new Set([...this.excludedTypes, 'equity'])
        )
      }
    }
    if (this.showEquityCoupon === false) {
      // this.filter.typeExcluded!.push('points')
      if (this.filter.typeExcluded && this.filter.typeExcluded.length > 0) {
        this.filter.typeExcluded.push('equity') // 后端只支持一个，期待有缘人接盘
      } else {
        this.filter.typeExcluded = Array.from(
          new Set([...this.excludedTypes, 'equity'])
        )
      }
    }
    let oldMarketCenter = sessionStorage.getItem('marketCenter') || ''
    sessionStorage.setItem('marketCenter', this.marketCenter)

    let params = JSON.parse(JSON.stringify(this.filter))
    if (this.filter.typeEquals === 'all_cash') {
      params.typeIn = this.filter.typeIn = ['all_cash', 'goods_cash']
      params.typeEquals = null
    } else if (this.filter.typeEquals === 'all_discount') {
      params.typeIn = this.filter.typeIn = [
        'all_discount',
        'rfm_type',
        'goods_discount'
      ]
      params.typeEquals = null
    } else {
      params.typeIn = this.filter.typeIn = null
    }
    if (params.typeIn && params.typeIn.length > 0) {
      params.typeExcluded = null
    }
    params.allowHdIssueEquals = true //不允许海鼎发券的券模板 不能被券活动查到
    if (this.hideState) {
      this.filter.stateEquals = null
    }
    if (this.selectCouponTypesIn?.length > 0) {
      params.typeIn = this.selectCouponTypesIn
      params.typeExcluded = null
    }
    return CouponTemplateApi.query(params).finally(() => {
      sessionStorage.setItem('marketCenter', oldMarketCenter as string)
    })
  }

  doCouponDialogClose() {
    this.couponDialogShow = false
  }

  doToDtl(id: string) {
    CouponTemplateApi.detail(id)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.child.coupons = resp.data
          this.couponDialogShow = true
        }
      })
      .catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
  }

  getUnit(amount: number, type: string) {
    if (type === 'all_cash' || type === 'goods_cash' || type === 'freight') {
      let str: any = this.formatI18n(
        '/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级套餐',
        '{0}元'
      )
      str = str.replace(/\{0\}/g, Number(amount).toFixed(2))
      return str
    } else if (
      type === 'all_discount' ||
      type === 'rfm_type' ||
      type === 'goods_discount'
    ) {
      let str: any = this.formatI18n(
        '/会员/等级/等级管理/已初始化状态的免费等级/表格/折扣',
        '{0}折'
      )
      str = str.replace(/\{0\}/g, Number(amount).toFixed(1))
      return str
    } else {
      return '--'
    }
  }

  showThisType(type: string) {
    if (this.selectCouponTypesIn?.length > 0) {
      return this.selectCouponTypesIn.includes(type)
    }
    return !this.excludedTypes.includes(type)
  }

  showEquityType(type: string) {
    return !this.excludedTypes.includes(type) && this.showEquityCoupon
  }

  //获取标签列表
  getTagList() {
    const params = new CouponTemplateTagFilter()
    params.page = 0
    params.pageSize = 0
    CouponTemplateTagApi.query(params).then((res) => {
      if (res.code === 2000) {
        this.tagList = res.data || []
      } else {
        this.$message.error(res.msg || '')
      }
    }).catch((err) => {
      this.$message.error(err.message)
    }).finally(() => { })
  }

  getTemplateTag(templateTag: CouponTemplateTagRelation[]) {
    let str = ''
    if (templateTag) {
      str = templateTag[0].tagValue || ''
    } else {
      str = '--'
    }
    return str
  }

  private getChannels() {
    let param: RSChannelManagementFilter = new RSChannelManagementFilter();
    ChannelManagementApi.query(param)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.channels = resp.data;
        }
      })
      .catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
  }
}
