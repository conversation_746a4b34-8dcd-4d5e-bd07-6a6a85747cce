export default class PointsAdjustBillFilter {
  // 会员标识类似于
  memberCodeLikes: Nullable<string> = null
  // 单号等于
  billNumberEquals: Nullable<string> = null
  // 单号类似于
  billNumberLike: Nullable<string> = null
  // 状态等于
  stateEquals: Nullable<string> = null
  // 创建时间范围[]
  createdBetweenClosedClosed: Date[] = []
  // 最后修改时间范围[]
  lastModifiedBetweenClosedClosed: Date[] = []
  // 页码
  page: Nullable<number> = null
  // 页面大小，大于0
  pageSize: Nullable<number> = null
}