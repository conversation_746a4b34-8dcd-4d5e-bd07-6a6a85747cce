export default class MemberIncreaseStats {
  // 新增门店会员数
  newStoreMemberCount: Nullable<number> = null
  // 新增会员数
  newMemberCount: Nullable<number> = null
  // 新增手机号会员数
  newMobileMemberCount: Nullable<number> = null
  // 新增手机号会员占比
  mobileMemberRate: Nullable<number> = null
  // 消费会员数
  consumeCount: Nullable<number> = null
  // 首次消费会员数量
  consumeFirstCount: Nullable<number> = null
  // 消费2次以上会员
  consumeGE2Count: Nullable<number> = null
  // 首次消费会员占比
  consumeFirstRate: Nullable<number> = null
}