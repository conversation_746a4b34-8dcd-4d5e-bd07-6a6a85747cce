/*
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:05
 * @LastEditTime: 2023-10-09 10:34:08
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\prepaycard\prepaycardtpl\PrepayCardTplPermission.ts
 * 记得注释
 */
import { Vue } from 'vue-property-decorator'
import PermissionMgr from "mgr/PermissionMgr";

export default class PrepayCardTplPermission extends Vue {
  get IMPREST_CARD_editable() {
    return PermissionMgr.hasOptionPermission('/卡/卡管理/卡模板/充值卡', '卡模板维护');
  }

  get IMPREST_CARD_viewable() {
    return PermissionMgr.hasOptionPermission('/卡/卡管理/卡模板/充值卡', '卡模板查看');
  }

  get GIFT_CARD_editable() {
    return PermissionMgr.hasOptionPermission('/卡/卡管理/卡模板/礼品卡', '卡模板维护');
  }

  get GIFT_CARD_viewable() {
    return PermissionMgr.hasOptionPermission('/卡/卡管理/卡模板/礼品卡', '卡模板查看');
  }

  get RECHARGEABLE_CARD_editable() {
    return PermissionMgr.hasOptionPermission('/卡/卡管理/卡模板/储值卡', '卡模板维护');
  }

  get RECHARGEABLE_CARD_viewable() {
    return PermissionMgr.hasOptionPermission('/卡/卡管理/卡模板/储值卡', '卡模板查看');
  }

  get COUNTING_CARD_editable() {
    return PermissionMgr.hasOptionPermission('/卡/卡管理/卡模板/次卡', '卡模板维护');
  }

  get COUNTING_CARD_viewable() {
    return PermissionMgr.hasOptionPermission('/卡/卡管理/卡模板/次卡', '卡模板查看');
  }

  get anyEditable() {
    return this.IMPREST_CARD_editable || this.GIFT_CARD_editable || this.RECHARGEABLE_CARD_editable || this.COUNTING_CARD_editable
  }

  get allViewable() {
    return this.IMPREST_CARD_viewable && this.GIFT_CARD_viewable && this.RECHARGEABLE_CARD_viewable && this.COUNTING_CARD_viewable
  }

  editableMap: any = {
    IMPREST_CARD: this.IMPREST_CARD_editable,
    GIFT_CARD: this.GIFT_CARD_editable,
    RECHARGEABLE_CARD: this.RECHARGEABLE_CARD_editable,
    COUNTING_CARD: this.COUNTING_CARD_editable
  }

  viewMap: any = {
    IMPREST_CARD: this.IMPREST_CARD_viewable,
    GIFT_CARD: this.GIFT_CARD_viewable,
    RECHARGEABLE_CARD: this.RECHARGEABLE_CARD_viewable,
    COUNTING_CARD: this.COUNTING_CARD_viewable
  }

  editable(cardTemplateType: 'GIFT_CARD' | 'IMPREST_CARD' | 'RECHARGEABLE_CARD' | 'COUNTING_CARD') {
    if (this.editableMap[cardTemplateType]) {
      return this.editableMap[cardTemplateType]
    }
    return false
  }

  viewable(cardTemplateType: 'GIFT_CARD' | 'IMPREST_CARD' | 'RECHARGEABLE_CARD' | 'COUNTING_CARD') {
    if (this.viewMap[cardTemplateType]) {
      return this.viewMap[cardTemplateType]
    }
    return false
  }
}
