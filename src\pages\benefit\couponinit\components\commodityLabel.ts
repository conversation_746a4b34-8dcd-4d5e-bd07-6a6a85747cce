/*
 * @Description:
 * @Version: 1.0
 * @Autor: 司浩
 * @Date: 2021-12-08 17:02:21
 * @LastEditors: 黎钰龙
 * @LastEditTime: 2024-11-07 13:01:05
 */
import { Component, Prop, Vue, Emit, Watch } from 'vue-property-decorator'
import CouponTemplateTag from 'model/coupon/CouponTemplateTag'
import CouponTemplateTagApi from 'http/coupon/template/CouponTemplateTagApi'
import CouponTemplateFilter from 'model/coupon/template/CouponTemplateFilter'
import CouponTemplateApi from 'http/coupon/template/CouponTemplateApi'
import I18nPage from 'common/I18nDecorator'

@Component({
  name: 'commodityLabel',
  components: {}
})
@I18nPage({
  prefix: [
    '/公用/券模板'
  ],
  auto: false
})
export default class CommodityLabel extends Vue {
  @Prop({ type: String, default: '' }) readonly labelId!: string
  @Prop({ type: String, default: '' }) readonly labelName!: string
  @Prop({ type: String, default: 'normal' }) readonly tagType?: string

  $refs: any
  labelText: string = ''
  isFocus: boolean = false
  addEdit: boolean = false

  @Watch('value')
  onValueChange(newVal: any, oldVal: any) {
    this.$emit('update:labelName', newVal)
  }

  created() {
    this.labelText = this.labelName
  }

  mounted() {
    document.body.addEventListener('click', this.cancelEdit)
  }

  beforeDestroy() {
    document.body.removeEventListener('click', this.cancelEdit)
  }

  // 编辑标签
  editLabel() {
    this.labelText = this.labelName
    this.$emit('update:tagType', 'edit')
    this.$nextTick(() => {
      this.$refs.input.focus()
    })
  }

  // 新增标签
  newLabel() {
    if (this.tagType === 'add') {
      this.addEdit = true
      this.$emit('update:tagType', 'edit')
    }
  }

  // 删除标签
  deleteLabel() {
    this.queryListByTag().then((res: string) => {
      const desc = res ? this.i18n('该标签已经被{0}引用，删除标签后，券模板里该标签也将自动删除', [res]) : this.formatI18n('/权益/券/券模板/删除标签之后将无法使用该标签，确认要删除标签吗？')
      this.$confirm(desc, this.formatI18n('/权益/券/券模板/删除'), {
        confirmButtonText: this.formatI18n('/权益/券/券模板/确定'),
        cancelButtonText: this.formatI18n('/权益/券/券模板/取消')
      }).then(() => {
        CouponTemplateTagApi.remove(this.labelId).then((resp) => {
          if (resp.code === 2000) {
            this.$emit('editSuccess')
          } else {
            throw new Error(resp.msg!)
          }
        }).catch((error) => {
          this.$message.error(error.message)
        })
      })
    })
  }

  // 查询当前标签被哪些券模板引用了
  queryListByTag() {
    const params = new CouponTemplateFilter()
    params.page = 0
    params.pageSize = 5
    params.allowHdIssueEquals = true
    params.mustHaveTagUuids = [this.labelId]
    return new Promise((resolve, reject) => {
      CouponTemplateApi.query(params).then((res) => {
        if (res.code === 2000) {
          if (res.data?.templateList?.length) {
            let templateListStr = res.data.templateList.reduce((prev, cur) => {
              return prev + cur.number + '，' + cur.name + '；'
            }, '[')
            if (Number(res.data.stats?.total) > res.data?.templateList?.length) {
              templateListStr = templateListStr.slice(0, -1) + '...]'
            } else {
              templateListStr = templateListStr.slice(0, -1) + ']'
            }
            resolve(templateListStr)
          } else {
            resolve('')
          }
        } else {
          throw new Error(res.msg!)
        }
      }).catch((error) => {
        this.$message.error(error.message)
        reject()
      })
    })
  }

  // 确认编辑
  async sureEdit() {
    if (!this.labelText) {
      Vue.prototype.$message.warning({
        message: this.formatI18n('/权益/券/券模板/请输入标签名称！')
      })
      return
    }
    let resData: any = {}
    if (this.addEdit) {
      const params = new CouponTemplateTag()
      params.value = this.labelText
      params.uuid = this.labelId
      resData = await CouponTemplateTagApi.save(params)
    } else {
      const params = new CouponTemplateTag()
      params.value = this.labelText
      params.uuid = this.labelId
      resData = await CouponTemplateTagApi.modify(params)
    }
    if (resData.code === 2000) {
      this.$emit('editSuccess')
      this.addEdit = false
    } else {
      this.$message.error(resData.msg)
      // this.$message.error(this.addEdit ? this.formatI18n('/权益/券/券模板/创建标签失败！') : this.formatI18n('/权益/券/券模板/编辑标签失败！'))
    }
  }

  // 取消编辑
  cancelEdit() {
    if (!this.labelText && !this.labelName) {
      this.$emit('update:tagType', 'add')
      return
    }
    if (this.addEdit) {
      this.labelText = ''
      this.$emit('update:tagType', 'add')
    } else {
      this.labelText = this.labelName
      this.$emit('update:tagType', 'normal')
    }
  }

  // 输入框值改变
  handelChange(e: any) {
    if (e.target.value === ' ') {
      e.target.value = ''
    } else {
      e.target.value = e.target.value.trim()
    }
  }
}
