import {Component, Provide, Vue} from 'vue-property-decorator'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import AccountBody from 'model/account/initial/AccountBody'
import AccountInitialApi from 'http/account/initial/AccountInitialApi'
import GoodsScopeEx from 'cmp/goodsscope/GoodsScopeEx.vue'
import ActiveStore from "cmp/activestore/ActiveStore";
import I18nPage from "common/I18nDecorator";
import GoodsRange from "model/common/GoodsRange";
import StoreRange from "model/common/StoreRange";
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import AutoFixInput from 'cmp/autofixinput/AutoFixInput'
import BrowserMgr from "mgr/BrowserMgr";

@Component({
  name: 'StoreValueAccountEdit',
  components: {
    SubHeader,
    GoodsScopeEx,
    ActiveStore,
    BreadCrume,
    AutoFixInput
  }
})
@I18nPage({
  prefix: ['/储值/会员储值/会员储值账户管理/详情页面', '/公用/提示', '/公用/按钮']
})
export default class StoreValueAccountEdit extends Vue {
  goodsMatchRuleMode: string = "barcode"
  @Provide('showAll')
	showAll: Boolean = true
  
  data: AccountBody = new AccountBody()
  checkArr: any[] = []
  $refs: any
  loading = false
  panelArray: any = []
  created() {
    let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
		if (sysConfig && this.goodsMatchRuleMode) {
			this.goodsMatchRuleMode = sysConfig.goodsMatchRuleMode;
		}
    this.panelArray = [
      {
        name: this.i18n('/公用/菜单/多储值账户'),
        url: ''
      }
    ]
    this.query()
    this.query()
  }

  query() {
    this.loading = true
    AccountInitialApi.list().then((resp: any) => {
      if (resp && resp.data) {
        this.data = resp.data
        for (let i = 0; i < this.data.accounts.length; i++) {
          this.checkArr.push({
            goodsInvalid: false,
            storeInvalid: false
          })
          if (!this.data.accounts[i].useGoods) {
            this.data.accounts[i].useGoods = new GoodsRange()
          }
          if (!this.data.accounts[i].useStores) {
            this.data.accounts[i].useStores = new StoreRange()
          }
        }
        this.$forceUpdate()
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    }).finally(() => {
      this.loading = false
    })
  }

  private save() {
    let validators = []
    for (let i = 0; i < this.data.accounts.length; i++) {
      if (this.$refs[`goods${i}`]) {
        validators.push(this.$refs[`goods${i}`].validate())
      }
      if (this.$refs[`store${i}`]) {
        validators.push(this.$refs[`store${i}`].validate())
      }
    }

    Promise.all(validators).then((res: any[]) => {
      if (res.filter((e) => !e).length === 0) {
        this.loading = true
        AccountInitialApi.save(this.data.accounts).then((resp: any) => {
          if (resp && resp.data) {
            this.$message.success(this.formatI18n('/公用/js提示信息', '保存成功'))
            this.$router.go(-1)
          }
        }).catch((error) => {
          if (error && error.message) {
            this.$message.error(error.message)
          }
        }).finally(() => {
          this.loading = false
        })
      }
    })
  }
}
