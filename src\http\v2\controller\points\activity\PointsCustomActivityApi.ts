import ApiClient from 'http/ApiClient'
import ChargeActivityV2 from 'model/v2/controller/points/activity/ChargeActivityV2'
import Response from 'model/common/Response'

export default class PointsCustomActivityApi {
  /**
   * 查询积分抵现活动
   *
   */
  static getPointsChargeActivity(num: string): Promise<Response<ChargeActivityV2>> {
    return ApiClient.server().get(`/v1/custom/points-activity/getPointsChargeActivity/${num}`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 保存积分抵现活动
   *
   */
  static savePointsChargeActivity(body: ChargeActivityV2): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/custom/points-activity/savePointsChargeActivity`, body, {}).then((res) => {
      return res.data
    })
  }

}
