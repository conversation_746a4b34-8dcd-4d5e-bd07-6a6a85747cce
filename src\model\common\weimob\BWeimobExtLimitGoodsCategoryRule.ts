import WeimobExtLimitGoodsCategoryRuleInfo from 'model/common/weimob/BWeimobExtLimitGoodsCategoryRuleInfo'
import IdName from 'model/common/IdName'

export default class BWeimobExtLimitGoodsCategoryRule {
  // 是否限制是否不可用商品
  existExcludeGoods: Nullable<boolean> = false
  // 不可用商品
  excludeGoodsIds: IdName[] = []
  // 是否包含下级自建商品
  includeChildGoods: Nullable<boolean> = null
  // 子类别
  ruleInfos: WeimobExtLimitGoodsCategoryRuleInfo[] = []
}