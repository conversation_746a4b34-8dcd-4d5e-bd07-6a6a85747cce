import {CycleType} from 'model/common/CycleType'
import {DateTimeRangeType} from 'model/common/DateTimeRangeType'

export default class DateTimeRange {
  // 日期时段类型：ALL——全部； USEABLE——可用; UNUSEABLE——不可用；
  dateTimeRangeType: Nullable<DateTimeRangeType> = null
  // 时段循环类型：DAYS——每天； WEEKS——每周; MONTHS——每月；
  cycleType: Nullable<CycleType> = null
  // 选中日期
  days: Nullable<number[]> = null
  // 开始时间 例：16:00
  beginTime: Nullable<string> = null
  // 结束时间
  endTime: Nullable<string> = null

  /**
   * 时间段
   * 每个集合填俩个时间，起始和结束
   */
   timeFrames?: string[][] = []
}
