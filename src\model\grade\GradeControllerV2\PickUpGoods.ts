import IdName from './IdName'

export default class PickUpGoods {
  // 品牌
  brand: Nullable<IdName> = null
  // 品类
  category: Nullable<IdName> = null
  // 商品代码
  code: Nullable<string> = null
  // 规格
  qpcStr: Nullable<string> = null
  // 商品
  goods: Nullable<IdName> = null
  // 价格
  price: Nullable<number> = null
  // 提货数量
  qty: Nullable<number> = null
  // 记账金额
  bookPayPrice: Nullable<number> = null
  /**
   * 是否是称重
   * 默认false，表示件
   */
  isDisp: Nullable<boolean> = null
  // 
  appreciationGoods: Nullable<boolean> = null
}