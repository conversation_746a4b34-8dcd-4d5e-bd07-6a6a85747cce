import { Component, Vue } from 'vue-property-decorator'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import FormItem from 'cmp/formitem/FormItem.vue'
import CardPicList from 'cmp/cardpiclist/CardPicList.vue'
import GoodsScopeDtl from 'cmp/goodsscope/GoodsScopeDtl.vue'
import CardTemplateApi from 'http/card/template/CardTemplateApi'
import CardTemplate from 'model/card/template/CardTemplate'
import DataUtil from '../common/DataUtil'
import PrePayConfigApi from 'http/prepay/config/PrePayConfigApi'
import ActiveStoreDtl from "cmp/activestoredtl/ActiveStoreDtl";
import EditType from "common/EditType";
import DateUtil from "util/DateUtil";
import PrepayCardTplPermission from "pages/deposit/prepaycard/prepaycardtpl/PrepayCardTplPermission";
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import I18nPage from 'common/I18nDecorator'
import ConstantMgr from 'mgr/ConstantMgr'
import PromotionShowDialog from 'cmp/coupontenplate/cmp/PromotionShowDialog.vue';
import BrowserMgr from "mgr/BrowserMgr";
import { GoodsFavType } from 'model/common/GoodsFavRule'

@Component({
  name: 'PrepayCardTplDtl',
  components: {
    SubHeader,
    FormItem,
    CardPicList,
    GoodsScopeDtl,
    ActiveStoreDtl,
    BreadCrume,
    PromotionShowDialog
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/储值/预付卡/卡模板/公共/卡类型',
    '/卡/卡管理/卡介质',
    '/储值/预付卡/卡模板/详情页面',
    '/储值/预付卡/预付卡查询/列表页面',
    '/营销/券礼包活动/新建商品满额发券活动/发券数限制',
    '/储值/预付卡/卡模板/编辑页面',
    '/公用/券模板/单品折扣券/用券门槛'
  ],
  auto: true
})
export default class PrepayCardTplDtl extends Vue {
  goodsMatchRuleMode: string = "barcode"
  $refs: any
  number: string
  detail: CardTemplate = new CardTemplate()
  dataUtil: DataUtil = new DataUtil()
  enableMultipleAccount: boolean = false
  permission = new PrepayCardTplPermission()
  panelArray: any = []

  get isNeedPayPwd() {
    const flag1 = ['GIFT_CARD', 'RECHARGEABLE_CARD', 'COUNTING_CARD'].indexOf(this.detail.cardTemplateType!) > -1
    const flag2 = this.detail.cardMedium !== 'online'
    return flag1 && flag2
  }

  get isNeedTransPwd() {
    const flag1 = ['GIFT_CARD', 'RECHARGEABLE_CARD'].indexOf(this.detail.cardTemplateType!) > -1
    const flag2 = this.detail.cardMedium !== 'online'
    return flag1 && flag2
  }

     // 判断是否是礼品卡或者储值卡
  get isGiftOrRechargeable() {
    return ['GIFT_CARD', 'RECHARGEABLE_CARD'].indexOf(this.detail.cardTemplateType!) > -1
  }

  get isShowLength() {
    if (this.detail.cardTemplateType === 'IMPREST_CARD') {
      return false
    } else {
      return true
    }
  }



    // 会员价叠加
    get memberPriceFav() {
      return this.detail.goodsFavRules?.find((item) => item.favType === GoodsFavType.MEMBER_PRICE)?.superimposed || false
    }
    // 人工折扣
    get manualDiscountFav() {
      return this.detail.goodsFavRules?.find((item) => item.favType === GoodsFavType.MANUAL_DISCOUNT)?.superimposed || false
    }
    // 其他优惠
    get otherDiscountFav() {
      return this.detail.goodsFavRules?.find((item) => item.favType === GoodsFavType.OTHER)?.superimposed || false
    }

  created() {
    let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
		if (sysConfig && this.goodsMatchRuleMode) {
			this.goodsMatchRuleMode = sysConfig.goodsMatchRuleMode;
		}
    this.panelArray = [
      {
        name: this.formatI18n('/储值/预付卡/卡模板/列表页面/卡模板'),
        url: 'prepay-card-tpl'
      },
      {
        name: this.formatI18n('/储值/预付卡/卡模板/详情页面/卡模板详情'),
        url: ''
      }
    ]
    this.getPrePermission()
    this.number = this.$route.query.number as any
    this.getDetail()
  }

  validatyInfo(row: CardTemplate) {
    if (!row.validityInfo) {
      return '-'
    }
    if (row.validityInfo.validityType === 'FIXED') {
      if (row.cardMedium === 'online' && row.cardTemplateType === 'GIFT_CARD') {
        return this.formatI18n('/储值/预付卡/卡模板/编辑页面/自激活起有效期至') + ' ' + DateUtil.format(row.validityInfo.endDate as Date, 'yyyy-MM-dd')
      } else {
        return this.formatI18n('/储值/预付卡/卡模板/编辑页面/自发售起有效期至') + ' ' + DateUtil.format(row.validityInfo.endDate as Date, 'yyyy-MM-dd')
      }
    }
    if (row.validityInfo.validityType === 'RALATIVE') {
      if (row.cardMedium === 'online' && row.cardTemplateType === 'GIFT_CARD') {
        return this.formatI18n('/储值/预付卡/卡模板/列表页面/激活后{0}内有效', null, [this.dataUtil.getValidNum(row.validityInfo)])
      } else {
        return this.formatI18n('/储值/预付卡/卡模板/编辑页面/发售后{0}内有效', null, [this.dataUtil.getValidNum(row.validityInfo)])
      }
    }
  }

  private getDetail() {
    const loading = this.$loading(ConstantMgr.loadingOption);
    CardTemplateApi.info(this.number).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.detail = resp.data
      } else {
        this.$message.error(resp.msg || this.i18n('获取卡模板信息失败'))
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message || this.i18n('获取卡模板信息失败'))
      }
    }).finally(() => {
      loading.close()
    })
  }

  getCardPromotionInfo(detail: CardTemplate) {
    if (detail.excludePromotion) {
      return detail.promotionSuperpositionType == 'PART' ? this.formatI18n('/公用/券模板详情/指定促销活动叠加') : this.formatI18n('/公用/券模板详情/全部促销活动叠加')
    } else {
      return detail.promotionSuperpositionType == 'PART' ? this.formatI18n('/公用/券模板详情/指定促销活动不叠加') : this.formatI18n('/公用/券模板详情/全部促销活动不叠加')
    }
  }

  private edit() {
    this.$router.push({ name: 'prepay-card-tpl-edit', query: { number: this.detail.number, editType: EditType.EDIT, cardType: this.detail.cardTemplateType } })
  }

  private copy() {
    this.$router.push({ name: 'prepay-card-tpl-edit', query: { number: this.detail.number, editType: EditType.COPY, cardType: this.detail.cardTemplateType } })
  }

  private createActivity() {
    this.$router.push({ name: 'gift-card-activity-edit', query: { cardNumber: this.detail.number, editType: '活动发售' } })
  }

  private getPrePermission() {
    PrePayConfigApi.get().then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.enableMultipleAccount = resp.data.enableMultipleAccount
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  openPromotionShow() {
    this.$refs.promotionShow.open()
  }
}
