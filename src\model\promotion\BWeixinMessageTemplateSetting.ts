/*
 * @Author: 黎钰龙
 * @Date: 2023-11-28 10:51:11
 * @LastEditTime: 2023-11-28 10:51:26
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\promotion\BWeixinMessageTemplateSetting.ts
 * 记得注释
 */
import BWeixinNotifyContent from "./BWeixinNotifyContent"

export default class BWeixinMessageTemplateSetting {
  // 模板标题
  title: Nullable<string> = null
  // 模板id
  templateId: Nullable<string> = null
  // 规则生效的开始时间 或 服务类目
  category: Nullable<string> = null
  // 模板内容
  content: BWeixinNotifyContent[] = []
  // 模板示例
  example: string[] = []
  // appid
  appid: Nullable<string> = null
  // 消息类型:offiaccount:公众号,applet:小程序
  type: Nullable<'offiaccount' | 'applet'> = null
  // 小程序或公众号名称
  appName: Nullable<string> = null
}