import { Vue, Component, Prop } from 'vue-property-decorator';
import PlaceTemplateMixins from '../PlaceTemplateMixins';
const testImg = require('@/assets/image/ic_danlantupian.png')
@Component({
  name: 'SwiperImage',
  components: {},
  mixins: [PlaceTemplateMixins],
})
export default class Rotation extends Vue {
  @Prop()
  componentItem: any;
  flag: boolean = false
  mounted() {
    this.flag = true
    console.log(this.componentItem, 'componentItem');
  }
  get localProperty() {
    return this.componentItem.props;
  }

  // selectImage(index) {
  //   if (index < 0 || index >= this.localProperty.propImages.length || index === this.currentIndex) {
  //     return;
  //   }
  //   this.currentIndex = index;
  // }

  // 展示图片数据
  get imgList() {
    this.flag = false
    this.$nextTick(() => {
      this.flag = true
    })
    if (this.localProperty.propImages.length > 0) {
      return this.localProperty.propImages;
    } else {
      return [
        {
          id: 1,
        },
      ];
    }
  }

  // get ossSourceUrl() {
  //   return this.$store.state.credential.host + '/-/cms/thumbnail/';
  // }
  get imgUrl() {
    return testImg
  }
  toolbarClick(e) {
    this.$emit('toolBarClick', {
      clickName: e,
      activeIndex: this.activeIndex,
    });
  }
}
