/*
 * @Author: 申鹏渤
 * @Date: 2023-11-29 14:05:25
 * @LastEditTime: 2023-12-25 18:04:19
 * @LastEditors: 申鹏渤
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\selectordialogs\GroupSelectorDialog.ts
 * 记得注释
 */
import { Component, Inject, Prop, Vue, Watch } from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import TagTemplateApi from 'http/precisionmarketing/tag/TagTemplateApi'
import TagTemplateFilter from 'model/precisionmarketing/tag/TagTemplateFilter'
import { TagTypeEnum } from "model/common/TagTypeEnum"
import TagInfo from 'model/common/TagInfo'

// 前端的复选框组的每一项的类
class GroupItem extends TagInfo {
  id: number
  // groupName: string
  checkBoxList: any[] = []
  checkList: any[] = []
}

@Component({
  name: 'GroupSelectorDialog',
  components: {
    FormItem,
  }
})
export default class GroupSelectorDialog extends Vue {
  dialogShow: boolean = false
  selectType: TagTypeEnum = TagTypeEnum.singleChoice
  checkGroup: any = {
    group: []
  }
  isFirst: boolean = true // 是否第一次打开对话框(用来后续修改数据)
  // 主动选择的数据，同时也对应着右侧展示数据
  selected: any = []
  // 传出数据标签个数
  outNum: number = 0
  // 查询参数
  query: TagTemplateFilter = new TagTemplateFilter();
  // 每个标签的类
  groupItem: GroupItem = new GroupItem()
  // 标签名称
  tagName: string = ''
  // 从父组件来的标题(可能不同界面用的不一样)
  @Prop()
  title: string;

  // 展示已选项的数目
  get checkedNum() {
    return this.selected.length
  }

  created() {
  }

  // 监听对话框打开的事件
  open(isClear: boolean, tags: any) {
    // 测试数据，接口来的时候要删除
    this.query.tagTypeEquals = this.selectType
    TagTemplateApi.query(this.query)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          if (isClear === true) {
            this.checkGroup.group = []
            this.selected = []
            resp.data.forEach((item: any, index: number) => {
              this.groupItem.id = index
              this.groupItem.name = item.name
              // 当数据结构为tagValues：[]时，和数据结构为tagValue：string时
              if (item.ruleData.manualTagRule) {
                this.groupItem.tagValues = item.ruleData.manualTagRule.tagValues
              }
              else if (item.ruleData.customizedTagRule) {
                let tagValues: any[] = []
                item.ruleData.customizedTagRule.ruleValues.forEach((item: any) => {
                  tagValues.push(item.tagValue)
                })
                this.groupItem.tagValues = tagValues
              }
              this.groupItem.checkList = []
              this.groupItem.tagId = item.uuid
              this.groupItem.esFieldName = item.esFieldName
              this.groupItem.isManual = item.tagModel == "Manual" ? true : false
              this.groupItem.tagType = this.selectType
              this.checkGroup.group.push(this.groupItem)
              this.groupItem = new GroupItem()
            });
            this.dialogShow = true
            this.isFirst = false
          }
          else {
            this.doReset()
            let selectTag: GroupItem = new GroupItem()
            this.dialogShow = true
            this.selected = []
            tags.forEach((item: any) => {
              selectTag.name = item.name
              selectTag.tagId = item.tagId
              selectTag.tagValues = JSON.parse(JSON.stringify(item.tagValues))
              selectTag.isManual = item.isManual
              selectTag.tagType = item.tagType
              selectTag.esFieldName = item.esFieldName
              this.selected.push(selectTag)
              this.getCheckList(item.tagId)
              selectTag = new GroupItem()
            })
          }
          console.log(this.checkGroup);
        } else {
          this.$message.error('接口异常');
        }
      })
      .catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message + '11');
        }
      })

  }

  doSearch() {
    this.checkGroup.group = []
    this.query.tagTypeEquals = this.selectType
    this.query.nameLikes = this.tagName
    TagTemplateApi.query(this.query)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          resp.data.forEach((item: any, index: number) => {
            this.groupItem.id = index
            this.groupItem.name = item.name
            // 当数据结构为tagValues: []时，和数据结构为tagValue: string时
            if (item.ruleData.manualTagRule) {
              this.groupItem.tagValues = item.ruleData.manualTagRule.tagValues
            } else if (item.ruleData.customizedTagRule) {
              let tagValues: any[] = []
              item.ruleData.customizedTagRule.ruleValues.forEach((item: any) => {
                tagValues.push(item.tagValue)
              })
              this.groupItem.tagValues = tagValues
            }
            // this.groupItem.checkList = []
            this.groupItem.tagId = item.uuid
            this.groupItem.esFieldName = item.esFieldName
            this.groupItem.isManual = item.tagModel == "Manual" ? true : false
            this.groupItem.tagType = this.selectType
            this.groupItem.checkList = this.getCheckList(item.uuid)
            this.checkGroup.group.push(this.groupItem)
            this.groupItem = new GroupItem()
          });
          console.log(this.checkGroup);
        }
      })
      .catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message + '11');
        }
      })
  }
  doReset() {
    this.selectType = TagTypeEnum.singleChoice
    this.tagName = ''
    this.doSearch()
  }
  onTypeChange() {
    this.doSearch()
  }
  doCancel() {
    this.$emit("dialogClose");
    this.dialogShow = false
  }

  // 确定按钮
  doModalClose() {
    // this.showSelected(this.selected)
    this.$emit("dialogConfirm", this.selected);
    this.dialogShow = false
  }

  // 清空按钮
  doClearAll() {
    this.checkGroup.group.forEach((item: any) => {
      item.checkList = []
    })
    this.selected = []
  }

  // 多选组变化的事件：id可以理解为数组的第{id}项，groupName是标签名，e是选中的标签
  handleCheckGroupChange(id: number, groupName: string, e: any) {
    let selectTag: GroupItem = new GroupItem()
    
    if (e.length != 0) {
      if (this.findSelected(groupName) >= 0) {
        let index = this.findSelected(groupName)
        this.selected[index].tagValues = e
      } else {
        selectTag.name = groupName
        selectTag.tagValues = e
        selectTag.id = id
        selectTag.tagId = this.checkGroup.group[id].tagId
        selectTag.esFieldName = this.checkGroup.group[id].esFieldName
        selectTag.isManual = this.checkGroup.group[id].isManual
        selectTag.tagType = this.checkGroup.group[id].tagType
        this.selected.push(selectTag)
      }
    } else {
      let index = this.findSelected(groupName)

      this.selected.splice(index, 1)
    }
    // this.showSelected(this.selected)
    console.log(this.selected);
  }

  // 删除按钮方法
  delItems(group: string, groupName: string) {
    let index = this.findSelected(groupName)
    this.selected[index].tagValues.splice(
      this.selected[index].tagValues.indexOf(group), 1
    )
    this.getCheckList(this.selected[index].tagId)
    if(this.selected[index].tagValues.length <= 0) {
      this.selected.splice(index, 1)
    }
    console.log(this.selected);
  }
  // 点击关闭icon的事件
  handleClose(done: any) {
    // this.$emit("dialogClose");
    this.$emit("dialogClose");
    done();
  }

  // 获取左侧复选框选择情况
  getCheckList(tagId: string) {
    if (this.selected) {
      let length = this.selected.length
      for (let i = 0; i < length; i++) {
        if (tagId == this.selected[i].tagId) {
          return this.selected[i].tagValues
        }
      }
    } else {
      return []
    }
    return []
  }

  // 查找元素是否已经被选择过
  findSelected(groupName: string) {
    if(this.selected.length === 0) {
      return -1
    } else if (this.selected.length > 0) {
      let isFind = this.selected.findIndex((item: any) => {
        return item.name == groupName
      })
      return isFind
    }
    return -1
  }
}