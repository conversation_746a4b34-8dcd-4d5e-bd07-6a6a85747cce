import {Component, Prop, Vue, Watch} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import PrePayAdjustBillApi from 'http/prepay/adjustbill/PrePayAdjustBillApi'
import I18nPage from 'common/I18nDecorator'

@Component({
  name: 'StoreValueAdjustReasonAdd',
  components: {
    FormItem
  }
})
@I18nPage({
  prefix: ['/储值/会员储值/储值调整单/调整原因设置', '/公用/提示', '/公用/按钮']
})
export default class StoreValueAdjustReasonAdd extends Vue {
  i18n: any
  flag = false
  id = ''
  $refs: any
  @Prop()
  data: any

  @Prop({
    type: Boolean,
    default: false
  })
  dialogShow: boolean
  reason = ''

  @Watch('data')
  onDataChange(value: any) {
    if (value) {
      this.reason = value.content
      this.id = value.id
    }
  }

  mounted() {
    this.reason = this.data.content
  }
  doBeforeClose(done: any) {
    this.$emit('dialogClose')
    done()
  }
  doModalClose(type: string) {
    if (type === 'confirm') {
      if (!this.reason) {
        // this.$message.warning('储值原因不能为空')
        this.$refs.reason.focus()
        return
      }
      PrePayAdjustBillApi.modifyReason(this.id, this.reason).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.i18n('修改成功'))
          this.$emit('dialogClose')
        }
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    } else {
      this.$emit('dialogClose')
    }
  }
  doBlur() {
    if (!this.reason) {
      this.flag = true
    } else {
      this.flag = false
    }
  }
}
