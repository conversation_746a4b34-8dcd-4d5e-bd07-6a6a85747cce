import {Component, Prop, Vue, Watch} from 'vue-property-decorator'
import MemberApi from 'http/member_standard/MemberApi'

@Component({
  name: 'CheckCouponDialog',
  components: {}
})
export default class CheckCouponDialog extends Vue {
  coupons: any = []
  @Prop()
  data: any
  @Prop()
  title: any

  @Prop({
    type: Boolean,
    default: false
  })
  dialogShow: boolean

  @Watch('dialogShow')
  watchDialogShow() {
    MemberApi.getCoupon(this.$route.query.id as string).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.coupons = resp.data
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  doBeforeClose(done: any) {
    this.$emit('dialogClose')
    done()
  }
  doModalClose() {
    this.$emit('dialogClose')
  }
  doCancel() {
    this.$emit('dialogClose')
  }
  doCheckGoods() {
    // todo
  }
}