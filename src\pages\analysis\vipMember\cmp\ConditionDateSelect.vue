<template>
  <el-dialog width="640px" :close-on-click-modal="false" :visible.sync="visible" append-to-body>
    <div class="dialog-content">
      <span>{{ propName }}</span>
      <div>
        <el-radio-group v-model="displayIntervalType" size="medium" class="radio-group-two" :disabled="!editable">
          <el-radio label="YEAR">{{ i18n('/数据/数据洞察/详情页/数据分桶/按年') }}</el-radio>
          <el-radio label="MONTH">{{ i18n('/数据/数据洞察/详情页/数据分桶/按月') }}</el-radio>
          <el-radio label="DAY">{{ i18n('/数据/数据洞察/详情页/数据分桶/按日') }}</el-radio>
        </el-radio-group>
      </div>

    </div>

    <div style="text-align: right; margin: 0" v-if="editable">
      <el-button size="mini" type="text" @click="visible = false">{{ i18n("取消") }}</el-button>
      <el-button @click="submitDateChoice" type="primary" size="mini">{{ i18n("确定") }}</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./ConditionDateSelect.ts">
</script>

<style lang="scss" scoped>
.dialog-content {
  display: flex;
  align-items: flex-start;
  justify-content: center;

  .radio-group-two {
    margin-left: 16px;
  }
}
</style>