import CardPicList from 'cmp/cardpiclist/CardPicList.vue'
import FormItem from 'cmp/formitem/FormItem.vue'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import CardTemplate from 'model/card/template/CardTemplate'
import EnvUtil from 'util/EnvUtil'
import { Component, Vue } from 'vue-property-decorator'
import CardTplItem from '../cmp/cardtplitem/CardTplItem'
import GiftCardActivityApi from 'http/card/activity/GiftCardActivityApi'
import GiftCardActivity from 'model/card/activity/GiftCardActivity'
import { GiftCardActivityEditForm, SaleSpecFormData } from './GiftCardActivityEditForm'
import CouponItem from 'model/common/CouponItem'
import CouponTemplateSelectorDialog from 'cmp/selectordialogs/CouponTemplateSelectorDialog.vue'
import SelectStoreActiveDtlDialog from 'pages/deposit/mbrdeposit/active/dialog/SelectStoreActiveDtlDialog.vue'
import DateUtil from "util/DateUtil";
import I18nPage from "common/I18nDecorator";
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import CouponTemplateFilter from 'model/coupon/template/CouponTemplateFilter'
import CouponTemplate from 'model/coupon/template/CouponTemplate'
import CouponInfo from 'model/common/CouponInfo'
import CouponTemplateApi from 'http/coupon/template/CouponTemplateApi'
import ActiveAddCoupon from 'cmp/activeaddcoupon/ActiveAddCoupon.vue'
import ActiveStore from "cmp/activestore/ActiveStore";
import ActivityTopic from 'model/v2/controller/points/topic/ActivityTopic'
import ActivityTopicApi from 'http/v2/controller/points/topic/ActivityTopicApi'
import ActiveStoreDtl from 'cmp/activestoredtl/ActiveStoreDtl'
import UploadApi from 'http/upload/UploadApi'
import RoutePermissionMgr from "mgr/RoutePermissionMgr";
import { CardMedium } from 'model/default/CardMedium'
import ActivePresentCard from 'cmp/active-present-card/ActivePresentCard'
import ActivityDateTimeConditionPicker from "cmp/date-time-condition-picker/ActivityDateTimeConditionPicker";


enum cardType {
  // 礼品卡 
  GiftCard = 'ONLINE_GIFT_CARD',
  // 储值卡 
  RechargeableCard = 'RECHARGEABLE_CARD',
  // 充值卡 
  ImprestCard = 'IMPREST_CARD',
  // 次卡 
  CountingCard = 'COUNTING_CARD'
}

@Component({
  name: 'GiftCardActivityEdit',
  components: {
    SubHeader,
    FormItem,
    CardPicList,
    CardTplItem,
    CouponTemplateSelectorDialog,
    SelectStoreActiveDtlDialog,
    BreadCrume,
    ActiveStore,
    ActiveAddCoupon,
    ActiveStoreDtl,
    ActivePresentCard,
    ActivityDateTimeConditionPicker
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/储值/预付卡/卡模板/列表页面',
    '/储值/预付卡/电子礼品卡活动/编辑页面',
    '/储值/预付卡/电子礼品卡活动/编辑页面/选中的卡模板信息组件',
    '/公用/活动/状态',
    '/公用/活动/活动信息',
    '/公用/活动/提示信息',
    '/公用/js提示信息',
    '/公用/按钮',
    '/卡/卡管理/售卡单',
    '/储值/会员储值/储值支付活动/编辑页面',
  ],
})
export default class GiftCardActivityEdit extends Vue {
  cardTemplateFilter: CouponTemplateFilter = new CouponTemplateFilter()
  child: CouponItem = new CouponItem()
  couponDialogShow = false
  i18n: (str: string, params?: string[]) => string
  uploadHeaders: any = {}
  panelArray: any = []
  coupon: any = []

  get uploadUrl() {
    return EnvUtil.getServiceUrl() + 'v1/upload/batchUpload'
  }

  activityId: string = ''
  editType: string = '' // 新建,修改,复制,活动发售
  $refs: any
  cardNumber: Nullable<string> = null
  form: GiftCardActivityEditForm = new GiftCardActivityEditForm()
  detail: GiftCardActivity = new GiftCardActivity() // 用于编辑复制等回传的
  tempSaleSpecs: SaleSpecFormData[] = [] // 用于修改时临时存放现有的面额
  couponCmp = { // 添加券组件
    qty: null,
    couponInfo: {
      from: 'add',
      parentIndex: 0,
      childIndex: 0
    },
    templateData: '',
    couponTemplateDialogShow: false,
    specIndex: 0,
    couponIndex: 0,
    editType: 'create' // create,update
  }
  picSelectorDialog = {
    dialogVisible: false,
    tplPicList: [] // 选中卡模板的卡样
  }
  loading = false
  themes: ActivityTopic[] = [];
  editFlag: Boolean = false

  // 上传时间
  uploadTimer: any = null
  // 多选模板数组
  saleSpecs: any[] = []
  specTmplNo: string[] = []
  CardMedium = CardMedium

  cardType = cardType
  created() {
    this.form.init(this)
    this.editType = '新建'
    let locale = sessionStorage.getItem('locale')
    this.uploadHeaders = {
      locale: locale ? locale : "zh_CN",
      time_zone: new Date().getTimezoneOffset(),
      marketingCenter: sessionStorage.getItem("marketCenter"),
    };
    const authorization = EnvUtil.getUniAuthorization()
    if (authorization) {
      this.uploadHeaders.authorization = authorization
    }
    let editType = this.$route.query.editType
    this.panelArray = [
      {
        name: this.formatI18n("/公用/菜单/电子卡售卡活动"),
        url: "gift-card-activity",
      },
      {
        name:
          editType === "新建" || editType === "复制" || !editType
            ? this.formatI18n("/储值/预付卡/电子礼品卡活动/编辑页面/新建电子礼品卡活动")
            : this.formatI18n("/储值/预付卡/电子礼品卡活动/编辑页面/修改电子礼品卡活动"),
        url: "",
      },
    ];
    this.getTheme()
  }

  mounted() {
    this.activityId = this.$route.query.activityId as string
    let editType = this.$route.query.editType
    if (editType) {
      this.editType = editType as string
    }
    if (['修改', '复制'].indexOf(this.editType) > -1) {
      this.getDetail()
    }
    if (this.editType === '活动发售') { // 活动发售时，卡模板由礼品卡模板详情页面带入
      this.cardNumber = this.$route.query.cardNumber as string
    }
  }

  private getTheme() {
    ActivityTopicApi.listTopic().then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.themes = resp.data;
      }
    });
  }

  beforeAvatarUpload(file: any) {
    const isJPG = ['image/jpeg', 'image/png', 'image/gif'].indexOf(file.type) > -1;
    const isLt2M = file.size / 1024 < 300;

    if (!isJPG) {
      this.$message.error(this.i18n('上传图片只能是JPG/PNG/JPEG/GIF格式!'));
      return false
    }
    if (!isLt2M) {
      this.$message.error(this.i18n('上传图片大小不能超过300KB'));
      return false
    }
    return true;
  }

  onUploadChange(file: any, filelist: any) {
    if (this.uploadTimer) {
      clearTimeout(this.uploadTimer)
      this.uploadTimer = null
    }
    this.uploadTimer = setTimeout(() => {
      clearTimeout(this.uploadTimer)
      this.uploadTimer = null
      if (filelist.length + this.form.data.themePictureUrls.length > 8) {
        this.$message.error(this.i18n('上传图片最多8张'));
        this.$refs.upload.clearFiles()
        return false
      }
      const isJPG = filelist.some((x: any) => {
        return ['image/jpeg', 'image/png', 'image/gif'].indexOf(x.raw.type) > -1
      })
      if (!isJPG) {
        this.$message.error(this.i18n('上传图片只能是JPG/PNG/JPEG/GIF格式!'));
        this.$refs.upload.clearFiles()
        return false
      }
      const isLt2M = filelist.some((x: any) => {
        return x.size / 1024 < 300
      });
      if (!isLt2M) {
        this.$message.error(this.i18n('上传图片大小不能超过300KB'));
        this.$refs.upload.clearFiles()
        return false
      }
      // 定时自动批量上传
      let param = new FormData()
      filelist.forEach((item: any) => {
        param.append('files', item.raw)
      })
      this.loading = true
      this.$refs.upload.clearFiles()
      UploadApi.batchUpload(param).then(res => {
        if (res.data && res.data.length) {
          let urls: string[] = []
          res.data.forEach((x: any) => {
            urls.push(x.url)
          })
          this.$set(this.form.data, 'themePictureUrls', [...urls, ...this.form.data.themePictureUrls])
          this.$refs['form'].validateField('themePictureUrls')
        }
      }).catch(e => {
        this.$refs.upload.clearFiles()
        if (e && e.message) {
          this.$message.error(e.message)
        }
      })
        .finally(() => {
          this.$refs.upload.clearFiles()
          this.loading = false
        })
    }, 500);
  }

  deleteImg(index: number) {
    ; (this.form.data.themePictureUrls as any || []).splice(index, 1)
  }
  doCouponDialogClose() {
    this.couponDialogShow = false
  }

  doCardTemplateSelected(arr: CouponTemplate[]) {
    if (arr) {
      let tempQty: any = {}
      for (let item of this.form.data.saleSpecs[this.couponCmp.specIndex].gift!.couponItems) {
        // for (let item of (this.couponCmp.specIndex as any).gift!.couponItems) {
        if (item!.coupons!.templateId) {
          tempQty[item!.coupons!.templateId] = item!.qty
        }
      }
      this.form!.data!.saleSpecs[this.couponCmp.specIndex].gift!.couponItems = []
      for (let i = 0; i < arr.length; i++) {
        let item = arr[i]
        let couponItem: CouponItem = new CouponItem()
        couponItem.coupons = new CouponInfo()
        couponItem.coupons.name = item.name
        couponItem.coupons.templateId = item.number
        couponItem.qty = couponItem!.coupons!.templateId && tempQty[couponItem!.coupons!.templateId] ? tempQty[couponItem!.coupons!.templateId] : 1
        let spec = this.form.data.saleSpecs[this.couponCmp.specIndex]
        spec.gift!.couponItems.push(couponItem)
      }
      for (let i = 0; i < this.form.data.saleSpecs.length; i++) {
        this.$refs.form.validateField(`saleSpecs[${i}].gift.couponItems`)
      }
      this.$forceUpdate()
    }
  }

  private getDetail() {
    this.loading = true
    GiftCardActivityApi.info(this.activityId).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.form.data.saleSpecs = []
        this.detail = resp.data
        this.form.of(resp.data)
        this.cardNumber = resp.data.detail.cardTemplateNumber
        if (this.editType === '修改' && this.detail.body!.state !== 'INITAIL') {
          this.editFlag = true
        }
        let arr: any[] = [...this.form.data.saleSpecs]
        this.form.data.saleSpecs = []
        arr.forEach((x: any) => {
          let spec = new SaleSpecFormData()
          spec.cardTemplateNumber = x.cardTemplateNumber
          spec.cardTemplateName = x.cardTemplateName
            ; (spec as any).number = x.cardTemplateNumber
            ; (spec as any).name = x.cardTemplateName
          spec.discount = x.discount
          spec.templatePrice = x.templatePrice
          spec.maxBuyQtyPerMan = x.maxBuyQtyPerMan
          spec.total = x.total
          spec.totalPerMan = x.totalPerMan
          spec.gift = x.gift
          spec.faceAmount = x.faceAmount
          spec.checked = false // 是否选中
          spec.limit = x.limit // 发售限制
          spec.givePoints = x.givePoints // 赠送积分
          spec.giveCoupons = x.giveCoupons
          // 定价取面额值
          spec.price = x.price
          spec.count = x.count
          spec.cardTemplateType = x.cardTemplateType
          this.form.data.saleSpecs.push(spec)
        })
        this.$forceUpdate()
        this.$refs.activePresentCard?.setValue(this.form.data.saleSpecs)
      } else {
        this.$message.error(resp.msg)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    }).finally(() => {
      this.loading = false
    })
  }

  private save() {
    const promiseArr = []
    promiseArr.push(this.$refs.form?.validate())
    promiseArr.push(this.$refs.activePresentCard?.doValidate())
    promiseArr.push(this.$refs.activityDateTimeConditionPicker.validate())
    Promise.all(promiseArr).then((res) => {
      let body = this.form.toParams()
      let saveMethod = GiftCardActivityApi.create
      if (['修改'].indexOf(this.editType) > -1) {
        saveMethod = GiftCardActivityApi.modify
        // @ts-ignore
        body.body.activityId = this.detail.body.activityId
      }
      this.loading = true
      saveMethod(body).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.i18n('保存成功'))
          this.$router.push({ name: 'gift-card-activity-dtl', query: { activityId: resp.data } })
        } else {
          this.$message.error(resp.msg)
        }
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
        .finally(() => {
          this.loading = false
        })
    })
  }

  private saveAndAudit() {
    let m = GiftCardActivityApi.create
    let body = this.form.toParams()
    if (['修改'].indexOf(this.editType) > -1) {
      m = GiftCardActivityApi.modify
      // @ts-ignore
      body.body.activityId = this.detail.body.activityId
    }
    const promiseArr = []
    promiseArr.push(this.$refs.form?.validate())
    promiseArr.push(this.$refs.activePresentCard?.doValidate())
    Promise.all(promiseArr).then((res) => {
      this.loading = true
      m(body).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.audit(resp.data)
        } else {
          this.$message.error(resp.msg)
        }
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      }).finally(() => {
        this.loading = false
      })
    })
  }

  private audit(activityId: string) {
    GiftCardActivityApi.audit(activityId).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(this.i18n('保存并审核成功'))
        this.$router.push({ name: 'gift-card-activity-dtl', query: { activityId: activityId } })
      } else {
        this.$message.error(resp.msg)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private onUploadSuccess(response: any, file: any, fileList: any) {
    if (response && response.code === 2000) {
      this.$message.success(this.i18n('上传成功'))
      this.form.data.themePictureUrls = response.data.url || []
    } else {
      this.$message.error(response.msg)
    }
    this.$refs['form'].validateField('themePictureUrls')
  }

  private selectPic(picUrl: string) {
  }

  get dateRangeOption() {
    return {
      disabledDate(time: any) {
        return time.getTime() < DateUtil.nowDayTime()
      }
    }
  }
  // 跳转模板详情
  gotoTplDtl(item: any) {
    RoutePermissionMgr.openBlank({ name: 'prepay-card-tpl-dtl', query: { number: item.number } })
  }
  //卡类型切换后，已选择的卡模板要清空
  cardTypeChange() {
    this.form.data.saleSpecs = []
    this.$refs.activePresentCard?.setValue([])
  }
}
