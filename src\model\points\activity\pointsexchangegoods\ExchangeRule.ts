import IdName from 'model/common/IdName'

export default class ExchangeRule {
  // 积分数
  points: Nullable<number> = null
  // 附加钱数
  cost: Nullable<number> = null
  // 兑换数量
  qty: Nullable<number> = 1
  // 兑换商品信息
  goods: Nullable<IdName> = null
  // 商品最大总库存数
  maxStockQty: Nullable<number> = null
  // 每个门店最多兑换多少个
  maxStoreQuotaQty: Nullable<number> = null
  // 每个会员最多兑换多少个
  maxMemberQuotaQty: Nullable<number> = null
}