/*
 * @Author: 黎钰龙
 * @Date: 2024-02-21 13:37:42
 * @LastEditTime: 2024-04-26 11:11:15
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\member\growth\MembershipGrowth.ts
 * 记得注释
 */
import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import I18nPage from 'common/I18nDecorator';
import UrlParamUtil from 'util/UrlParamUtil';
import { Component, Vue } from 'vue-property-decorator';
import MembershipGrowthList from './MembershipGrowthList';
@Component({
  name: 'MembershipGrowth',
  components: {
    BreadCrume,
    MembershipGrowthList
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    "/公用/菜单",
    '/公用/按钮'
  ],
  auto: true
})
export default class MembershipGrowth extends Vue {
  panelArray: any = []
  activeName: string;

  created() {
    this.activeName = UrlParamUtil.get("activeName", "active-manage-add" as any);
    this.panelArray = [{
      name: this.i18n("会员成长"),
      url: "",
    },]
  }

  handleTabClick() {
    UrlParamUtil.store("activeName", this.activeName);
  }

  doActiveAdd(routeName: string) {
    this.$router.push({ name: routeName });
  }
};