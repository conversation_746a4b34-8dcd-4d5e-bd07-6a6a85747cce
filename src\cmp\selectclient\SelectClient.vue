<template>
  <el-select v-model="selectClient" size="medium"
             value-key="id"
             :style="{width: width}"
             :loading="selectLoading"
             :disabled="disabled"
             remote
             clearable
             filterable
             :placeholder="i18n('请选择')"
             :remote-method="doRemoteMethod">
    <el-option
        v-for="item in clients"
        :key="item.clientId"
        :label="'['+item.clientCode +']'+item.clientName"
        :value="item.clientId">
    </el-option>
  </el-select>
</template>

<script lang="ts" src="./SelectClient.ts"></script>

<style>
</style>