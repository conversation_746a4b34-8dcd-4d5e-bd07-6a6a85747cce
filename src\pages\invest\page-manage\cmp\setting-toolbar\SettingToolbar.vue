<template>
  <div class="delivery-toolbar" :style="{ ...position }">
    <div
      :class="['delivery-toolbar-item', item.enabled ? '' : 'delivery-toolbar-item-disabled']"
      v-for="item in btns"
      :key="item.action"
      @click.stop="onClick(item.action)"
    >
      <span
        :class="['iconfont', item.icon]"
        :style="{ color: item.enabled ? item.color : item.disabledColor }"
      ></span>
    </div>
  </div>
</template>

<script lang="ts" src="./SettingToolbar.ts"></script>

<style lang="scss" scoped>
.delivery-toolbar {
  position: absolute;
  height: 32px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 8px 0;
  border-radius: 4px;
  box-sizing: border-box;

  &-item {
    height: 32px;
    padding: 0 3px;
    text-align: center;
    line-height: 32px;
    background-color: #000000;
    box-sizing: border-box;
  }

  &-item:hover {
    color: #4d63ec;
  }

  &-item:active {
    color: #4d63ec;
  }

  &-item-disabled:hover {
    background-color: #000000;
    cursor: not-allowed;
  }

  &-item-disabled:active {
    color: #c5cbd6;
  }

  .action {
    font-size: 12px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #fff;
    background-color: #7182f0;
    border-radius: 0 0 0 10px;
    width: 72px;
    height: 32px;
  }
}
</style>
