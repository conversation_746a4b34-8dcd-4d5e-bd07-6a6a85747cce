<!--
 * @Author: 黎钰龙
 * @Date: 2025-01-06 20:02:17
 * @LastEditTime: 2025-04-27 17:35:06
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\cmp\page-props-components\single-coupon-style\SingleCouponStyle.vue
 * 记得注释
-->
<template>
  <div class="coupon-style" v-if="value.propLayoutStyle === '1'">
    <el-form label-position="left" :model="value" :rules="rules" ref="form">
      <el-form-item prop="propShowStyle" label-width="100px">
        <template slot="label">
          {{ i18n('单券样式') }}
          <el-tooltip effect="dark" :content="i18n('仅对单券活动生效')" placement="top">
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </template>
        <el-radio-group v-model="value.propShowStyle" @change="handleChange">
          <el-radio label="1">{{ i18n('样式一') }}</el-radio>
          <el-radio label="2">{{ i18n('样式二') }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <img class="coupon-img" v-if="value.propShowStyle === '1'" src="@/assets/image/fellow/single_coupon_style1.png" />
      <img class="coupon-img" v-else src="@/assets/image/fellow/single_coupon_style2.png" alt="">
    </el-form>
  </div>
</template>

<script lang="ts" src="./SingleCouponStyle.ts">
</script>

<style lang="scss" scoped>
.coupon-style {
  padding: 12px;
  background: #f0f2f6;
  border-radius: 4px;
  margin-bottom: 20px;

  .coupon-img {
    width: 300px;
    height: 88px;
  }
}
</style>