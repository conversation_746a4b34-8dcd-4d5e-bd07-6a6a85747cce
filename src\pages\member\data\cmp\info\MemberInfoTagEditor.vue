<template>
  <el-dialog :before-close="beforeClose"
             :close-on-click-modal="false"
             :title="i18n('/会员/智能打标/选择标签')"
             :visible.sync="value"
             width="1050px">
    <el-form inline>
      <el-form-item :label="i18n('渠道来源')">
        <ChannelSelect v-model="channels"
                       @change="queryTag"
                       :clearable="false"
                       :multiple="false"
                       :isShowAll="true"
                       width="144px">
        </ChannelSelect>
      </el-form-item>
      <el-form-item :label="i18n('数据类型')">
        <el-select v-model="filter.tagType"
                   @change="queryTag"
                   style="width: 144px;margin-left: 8px">
          <el-option :value="null"
                     :label="i18n('全部类型')"></el-option>
          <el-option value="checkbox"
                     :label="i18n('多选')"></el-option>
          <el-option value="singleChoice"
                     :label="i18n('单选')"></el-option>
          <el-option value="text"
                     :label="i18n('/会员/洞察/标签管理/手工标签/新建页/文本')"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="i18n('标签名称')">
        <el-input :placeholder="i18n('按回车搜索标签')"
                  v-model="filter.tagName"
                  @keydown.native="onEnter($event, 'label')"
                  style="flex: 1;margin-left: 8px">
          <i slot="suffix"
             @click="queryTag"
             class="el-icon-search"></i>
        </el-input>
      </el-form-item>
    </el-form>
    <div class="tag-editor">
      <div class="tag-list">
        <div class="label-item"
             v-for="(item,index) in tagsTree"
             :key="index">
          <div class="label-type"
               @click="doExpendTag(item)">
            <div class="label-icon">
              <i class="el-icon-caret-right"
                 v-if="!item.expand" />
              <i class="el-icon-caret-bottom"
                 v-else />
            </div>
            {{ item.tag.tagId }}
          </div>
          <div class="label-content"
               v-if="item.expand">
            <!-- 单选 -->
            <template v-if="item.tag.tagType === 'singleChoice'">
            <div class="check-item"
                 v-for="(tagItem, ind) in item.tag.tagValues"
                 :key="ind"
                 :class="{ selected: item.tagValue==tagItem }"
                 @click="onSelectLabel(item,tagItem)">
              {{ tagItem }}
            </div>
            </template>
            <!-- 多选 -->
            <template v-else-if="item.tag.tagType === 'checkbox'">
            <div class="check-item"
                 :title="tagItem"
                 :key="ind"
                 v-for="(tagItem, ind) in item.tag.tagValues"
                 :class="{ selected: item.tagValue && item.tagValue.includes(tagItem) }"
                 @click="onSelectLabel(item,tagItem,false)">
              {{ tagItem }}
            </div>
            </template>
            <!-- 文本 -->
            <template v-else-if="item.tag.tagType === 'text'">
            <el-input v-model="item.tagValue"
                      @input="onInputValue(item)"></el-input>
            </template>
          </div>
        </div>

      </div>
      <div class="tag-selected-list">
        <div class="tag-selected-list-header">{{ i18n("已选标签：{0}", [selectTagsLocal.length + ""]) }}</div>
        <div class="tag-selected-list-content">
          <div class="tag-selected-list-item"
               v-for="item in selectTagsLocal"
               :key="item.tagId">
            <div class="selected-label"
                 :title="item.tagId+':'+item.tagValues.join('、')">
              {{ item.tagId }}:{{ item.tagValues.join("、") }}
            </div>
            <el-button class="selected-btn"
                       type="text"
                       @click="onRemoveTag(item)">{{ i18n("清除") }}
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <div slot="footer">
      <el-button @click="beforeClose"
                 :loading="saveLoading"
                 size="large">
        {{ i18n("/公用/按钮/取消") }}
      </el-button>
      <el-button @click="onSave"
                 :loading="saveLoading"
                 type="primary"
                 size="large">
        {{ i18n("/公用/按钮/确定") }}
      </el-button>
    </div>
  </el-dialog>
</template>
<script lang="ts"
        src="./MemberInfoTagEditor.ts">
</script>
<style lang="scss"
       scoped>
.tag-editor {
  display: flex;
  height: 400px;
  gap: 16px;

  & > * {
    border: 1px solid #D7DFEB;
  }

  .tag-list {
    width: calc(70% - 16px);
    overflow-y: auto;
    padding: 16px;

    .label-item {
      display: flex;
      flex-direction: column;
      margin-bottom: 12px;

      .label-type {
        display: flex;
        align-items: center;
        cursor: pointer;

        .label-icon {
          font-size: 12px;
          margin-right: 4px;
        }
      }

      .label-content {
        display: flex;
        flex-wrap: wrap;
        padding-left: 16px;
        margin-top: 8px;
        width: 50%;

        .check-item {
          max-width: 500px;
          height: 24px;
          line-height: 24px;
          text-align: center;
          padding: 0 12px;
          font-weight: 400;
          color: #242633;
          border-radius: 2px;
          background: #f7f9fc;
          border: 1px solid #d7dfeb;
          margin-right: 4px;
          margin-bottom: 3px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          cursor: pointer;

          &.selected {
            border: 1px solid #007eff;
            background: #e6f2ff;
            color: #007eff;
          }
        }
      }
    }
  }

  .tag-selected-list {
    width: 30%;

    .tag-selected-list-header {
      padding: 0 8px;
      background: #D2D9E5;
      height: 36px;
      line-height: 36px;
    }

    .tag-selected-list-content {
      overflow-y: auto;
      height: calc(100% - 36px);
    }

    .tag-selected-list-item {
      padding: 4px 8px;
      display: flex;
      align-items: center;

      .selected-label {
        flex-grow: 1;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }

      .selected-btn {
        flex-shrink: 0;
        margin-left: 12px;
      }
    }
  }
}
</style>
