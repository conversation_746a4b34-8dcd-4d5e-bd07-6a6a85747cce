import ApiClient from "http/ApiClient";
import CouponInfo from "model/common/CouponInfo";
import CouponTemplateFilter from "model/coupon/template/CouponTemplateFilter";
import CouponTemplateQueryData from "model/coupon/template/CouponTemplateQueryData";
import Response from "model/common/Response";
import IdName from "model/common/IdName";
import CouponCancel from "model/coupon/template/CouponCancel";
import BatchAddOrgRequest from "model/coupon/template/BatchAddOrgRequest";
import CouponTemplateProperties from "model/coupon/template/CouponTemplateProperties";
import PromotionBill from 'model/coupon/template/PromotionBill'
import PromotionQueryParams from 'model/coupon/template/PromotionQueryParams'

export default class CouponTemplateApi {

  /**
   * 获取券模板配置属性
   * 获取券模板配置属性
   * 
   */
  static getCouponTemplateProperties(): Promise<Response<CouponTemplateProperties>> {
    return ApiClient.server().get(`/v1/coupon-template/getCouponTemplateProperties`, {
    }).then((res) => {
      return res.data
    })
  }

    /**
   * 是否pms 计算器
   *  
   * 
   */
     static pmsPayEngine(): Promise<Response<Boolean>> {
      return ApiClient.server().get(`/v1/coupon-template/pmsPayEngine`, {
      }).then((res) => {
        return res.data
      })
    }

  /**
   * 获取指定组中的全部券模板信息
   * 获取指定组中的全部券模板信息
   *
   */
  static getTemplatesGroup(body: string[]): Promise<Response<IdName[]>> {
    return ApiClient.server()
      .post(`/v1/coupon-template/getTemplatesGroup`, body, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 券模板详情查询
   * 券模板详情查询
   * 参数lastEffected：如果为true，查询“已审核”的最新券模板；为false，查询最新券模板
   * 
   */
  static detail(templateId: string, lastEffected: boolean = true): Promise<Response<CouponInfo>> {
    return ApiClient.server()
      .post(`/v1/coupon-template/detail/${templateId}?lastEffected=${lastEffected}`, {}, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 券模板列表查询
   * 券模板列表查询
   *
   */
  static query(body: CouponTemplateFilter): Promise<Response<CouponTemplateQueryData>> {
    return ApiClient.server()
      .post(`/v1/coupon-template/query`, body, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 保存并生效券模板，以券模板号为标志位判断
   * 保存并生效券模板，以券模板号为标志位判断
   *
   */
  static saveOrEffect(body: CouponInfo): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-template/saveOrEffect`, body, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 新建或修改券模板，以券模板号为标志位判断
   * 新建或修改券模板，以券模板号为标志位判断
   *
   */
  static saveOrModify(body: CouponInfo): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-template/saveOrModify`, body, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
 * 批量审核券模板
 * 批量审核券模板
 * 参数为：numbers: Array<string> = 券模板号数组   operator:string = 操作人
 * 返回值为 成功的券模板号数组
 */
  static batchAudit(body: any): Promise<Response<string[]>> {
    let operator = JSON.parse(sessionStorage.getItem('vuex') as string)?.loginInfo.user?.account
    body.operator = operator
    return ApiClient.server()
      .post(`/v1/coupon-template/batchAudit`, body, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 作废券
   *
   */
  static cancel(body: CouponCancel): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-template/cancel/${body.templateNum}`, {}, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 批量导出
   * 批量导出
   *
   */
  static export(body: CouponTemplateFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/coupon-template/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量增加适用门店
   * 批量增加适用门店
   * 
   */
  static batchAddOrg(body: BatchAddOrgRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/coupon-template/batchAddOrg`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取启用审核模板配置
   * 获取启用审核模板配置
   * 
   */
  static getEnabledAuditTemplateConfig(): Promise<Response<boolean>> {
    return ApiClient.server().get(`/v1/coupon-template/getEnabledAuditTemplateConfig`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询促销单列表
   * 
   */
  static queryPromotion(body: PromotionQueryParams): Promise<Response<PromotionBill[]>> {
    return ApiClient.server().post(`/v1/promotionbill/query`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
    * 券模板促销单编辑查询
    * 
    */
  static getCouponTemplatePromotion(templateNumber: string): Promise<Response<PromotionBill[]>> {
    return ApiClient.server().post(`/v1/coupon-template/couponTemplatePromotion/get/${templateNumber}`, {}, {}).then((res) => {
      return res.data
    })
  }
  /**
   * 券模板促销单详情查询
   * 
   */
  static queryCouponTemplatePromotion(templateNumber: string): Promise<Response<PromotionBill[]>> {
    return ApiClient.server().post(`/v1/promotionbill/query/${templateNumber}`, {}, {}).then((res) => {
      return res.data
    })
  }
  /**
   * 文件处理结果
   * 
   */
  static getUploadResult(uk: string): Promise<Response<any>> {
    return ApiClient.server().post(`/v1/upload/getUploadResult/${uk}`, {}, {}).then((res) => {
      return res.data
    })
  }
  /**
   * 导入促销单查询
   * 
   */
  static queryImport(uk: string): Promise<Response<PromotionBill[]>> {
    return ApiClient.server().post(`/v1/promotionbill/import/query/${uk}`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
  * 展示外部券模板字段配置
  * 
  */
  static getShowOuterRelationsConfig(): Promise<Response<Boolean>> {
    return ApiClient.server().get(`/v1/coupon-template/getShowOuterRelationsConfig`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 券模板关系查询
   *
   */
  static queryRelation(channelId: string, outerNumber: string, channelType?: string): Promise<Response<string[]>> {
    return ApiClient.server().post(`/v1/coupon-template/queryRelation`, {}, {
      params: {
        channelId: channelId,
        channelType: channelType,
        outerNumber: outerNumber
      }
    }).then((res) => {
      return res.data
    })
  }
}
