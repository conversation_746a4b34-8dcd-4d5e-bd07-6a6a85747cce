import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import FormItem from 'cmp/formitem/FormItem';
import I18nPage from 'common/I18nDecorator';
import { Component, Vue, Watch } from 'vue-property-decorator';
import Echart from 'echarts/lib/echarts'
import 'echarts/lib/chart/treemap'
import 'echarts/lib/component/tooltip';
import TagCalculatorTaskV2Api from 'http/tagv2/TagCalculatorTaskV2Api';
import TagCalculatorTaskV2Filter from 'model/autoTag/TagCalculatorTaskV2Filter';
import { TagCalculatorTaskType } from 'model/autoTag/TagCalculatorTaskType';
import TagCalculatorTaskV2 from 'model/autoTag/TagCalculatorTaskV2';
import RfmUserDetail from 'model/analysis/RfmUserDetail';
import QueryMemberConsumeDataFilter from 'model/analysis/QueryMemberConsumeDataFilter';
import RfmAnalysisApi from 'http/analysis/RfmAnalysisApi';
import RfmUserCompose from 'model/analysis/RfmUserCompose';
import CommonUtil from 'util/CommonUtil';
import { TagCalculatorTaskExecuteState } from 'model/autoTag/TagCalculatorTaskExecuteState';
import DownloadCenterDialog from "pages/main/dowmload_center_dialog/DownloadCenterDialog";

@Component({
  name: 'RfmAnalysis',
  components: {
    DownloadCenterDialog,
    BreadCrume,
    FormItem
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/数据/数据洞察'
  ],
  auto: true
})
export default class RfmAnalysis extends Vue {
  $echarts: any
  options: any = {}
  treeChart: any
  taskInfo: Nullable<TagCalculatorTaskV2> = null //rfm标签任务id
  rfmTaskList: TagCalculatorTaskV2[] = [] //rfm标签任务列表
  customerList: RfmUserDetail[] = [] //客户列表
  memberCompose: RfmUserCompose[] = [] //会员数据构成
  currentTagValue: string = '' //当前鼠标悬停的tagValue
  tableLoading: boolean = false
  downloadCenterFlag: boolean = false; //文件下载中心弹窗
  page = {
    page: 1,
    pageSize: 10,
    total: 0
  }

  @Watch('currentTagValue')
  handle() {
    this.doResetCustomerList()
  }

  get panelArray() {
    return [
      {
        name: this.i18n('/公用/菜单/RFM分析'),
        url: ''
      }
    ]
  }

  // 图表是否有数据
  get hasChartData() {
    return this.memberCompose.some((item) => item.coveredCount)
  }

  saveQueryDefinition(taskInfo: Nullable<TagCalculatorTaskV2>) {
     if (taskInfo) {
       sessionStorage.setItem('rfmAnalysisQd', JSON.stringify(taskInfo))
     }
  }

  loadQueryDefinition() {
    return sessionStorage.getItem('rfmAnalysisQd');
  }
  created() {
    this.$echarts = Echart
    this.doQueryRfmTask()
    this.options = {
      series: [{
        type: 'treemap',
        // id: 'myEcharts'
        nodeClick: false,
        roam: 'move',
        colorMappingBy: 'value',
        colorAlpha: [0.3, 1],
        itemStyle: {
          borderWidth: 1,
          borderColor: '#fff'
        },
        breadcrumb: {
          show: false
        }
      }],
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(255,255,255,0.8)',
        textStyle: {
          color: '#36445A'
        },
        formatter: (params: any) => {
          console.log('看看params', params);
          if (!params.data.name) return '';
          const name = params.name.split('：')[0]
          if (name !== this.currentTagValue && !this.tableLoading) {
            this.currentTagValue = name
          }
          let result = '';
          const infoItem = this.memberCompose.find((item) => item.tagValue === params.name.split('：')[0])
          result += `<div style="display:flex;justify-content:space-between"><span>${this.i18n('/会员/洞察/客群/列表页/覆盖人数')}：</span>${Number(infoItem?.coveredCount)}</div>`
          result += `<div style="display:flex;justify-content:space-between"><span>${this.i18n('/会员/洞察/客群/列表页/覆盖率')}：</span>${Number(infoItem?.coveredPercentage)}%</div>`
          result += `<div style="display:flex;justify-content:space-between"><span>${this.i18n('人均消费间隔')}：</span>${Number(infoItem?.avgTransInterval)}</div>`
          result += `<div style="display:flex;justify-content:space-between"><span>${this.i18n('消费次数')}：</span>${Number(infoItem?.totalTradeQty)}</div>`
          result += `<div style="display:flex;justify-content:space-between"><span>${this.i18n('人均消费次数')}：</span>${Number(infoItem?.avgTradeQty)}</div>`
          result += `<div style="display:flex;justify-content:space-between"><span>${this.i18n('消费金额')}：</span>${Number(infoItem?.totalTradeAmount)}</div>`
          result += `<div style="display:flex;justify-content:space-between"><span>${this.i18n('人均消费金额')}：</span>${Number(infoItem?.avgTradeAmount)}</div>`
          return result;
        }
      },
      color: [
        'rgba(0,126,255, 0.55)',
        'rgba(0,126,255, 0.6)',
        'rgba(0,126,255, 0.65)',
        'rgba(0,126,255, 0.7)',
        'rgba(0,126,255, 0.75)',
        'rgba(0,126,255, 0.8)',
        'rgba(0,126,255, 0.85)',
        'rgba(0,126,255, 0.9)',
        'rgba(0,126,255, 0.95)',
        'rgba(0,126,255, 1)',
      ]
    }

    // 从缓存中取上次分析的taskInfo
    const cachedQd = this.loadQueryDefinition()
    if (cachedQd) {
      this.taskInfo = CommonUtil.copy(JSON.parse(cachedQd))
      this.doSearch()
    }
  }

  mounted() {
    this.doDrawEchart()
  }

  // 生成图表
  doDrawEchart() {
    const element = document.getElementById('myEcharts')
    this.treeChart = this.$echarts.init(element)
    if (this.treeChart) {
      this.treeChart.setOption(this.options)
    }
  }

  // 查询rfm标签任务
  doQueryRfmTask(value?: string) {
    const params = new TagCalculatorTaskV2Filter()
    params.page = 0
    params.pageSize = 100
    params.tagNameLikes = value || null
    params.typeEquals = TagCalculatorTaskType.rfm_tag
    params.executeStateEquals = TagCalculatorTaskExecuteState.success
    TagCalculatorTaskV2Api.query(params).then((res) => {
      if (res.code === 2000) {
        this.rfmTaskList = res.data?.tasks.filter((item) => item.tagName)
            .reduce((acc, item) => {
              if (!acc.tagNames.has(item.tagName)) {
                acc.tagNames.add(item.tagName)
                acc.tasks.push(item)
              }
              return acc;
            }, {tagNames: new Set<string>(), tasks: []}).tasks
          || []
      } else {
        throw new Error(res.msg as any)
      }
    }).catch((err) => {
      this.$message.error(err.message)
    })
  }

  // 查询会员构成、列表
  async doSearch() {
    const loading = CommonUtil.Loading()
    try {
      this.saveQueryDefinition(this.taskInfo)
      await this.doQueryMemberCompose()
    } catch (err: any) {
      this.$message.error(err.message)
    }
    loading.close()
  }

  doResetCustomerList() {
    this.page.page = 1
    this.page.total = 0
    return this.doQueryCustomerList()
  }

  // 查询会员数据构成
  doQueryMemberCompose() {
    return RfmAnalysisApi.listUserCompose(this.taskInfo?.uuid!).then((res) => {
      if (res.code === 2000) {
        this.memberCompose = res.data || []
        this.currentTagValue = res.data?.find((item) => item.coveredCount)?.tagValue || ''
        this.$nextTick(() => {
          this.updateTreeData(this.memberCompose)
        })
      } else {
        throw new Error(res.msg as any)
      }
    }).catch((err) => {
      this.$message.error(err.message)
    })
  }

  // 更新矩形图的数据
  updateTreeData(arr: RfmUserCompose[]) {
    this.treeChart?.setOption(
      {
        series: {
          ...this.options.series,
          data: arr.map((item) => {
            return {
              name: `${item.tagValue}：${Number(item.coveredCount)}`,
              value: item.coveredCount
            }
          })
        }
      },
      {
        replaceMerge: ['series']
      }
    )
  }

  // 查询客户列表
  doQueryCustomerList() {
    this.tableLoading = true
    const params = new QueryMemberConsumeDataFilter()
    params.page = this.page.page - 1
    params.pageSize = this.page.pageSize
    params.tagId = this.taskInfo?.tagTemplateUuid
    params.tagValue = this.currentTagValue || null
    return RfmAnalysisApi.listUserDetail(params).then((res) => {
      if (res.code === 2000) {
        this.customerList = res.data || []
        this.page.total = res.total || 0
      } else {
        throw new Error(res.msg as any)
      }
    }).catch((err) => {
      this.$message.error(err.message)
    }).finally(() => {
      this.tableLoading = false
    })
  }

  onHandleCurrentChange(val: number) {
    this.page.page = val;
    this.doQueryCustomerList();
  }

  onHandleSizeChange(val: number) {
    this.page.page = 1;
    this.page.pageSize = val;
    this.doQueryCustomerList();
  }

  // 获取消费总金额占比
  getAmountPercent(value: number) {
    const total = this.memberCompose.find((item) => item.tagValue === this.currentTagValue)?.totalTradeAmount
    return total ? (value / total * 100) : 0
  }

  // 获取消费次数占比
  getQtyPercent(value: number) {
    const total = this.memberCompose.find((item) => item.tagValue === this.currentTagValue)?.totalTradeQty
    return total ? (value / total * 100) : 0
  }

  // 获取消费间隔占比
  getIntervalPercent(value: number) {
    const total = this.memberCompose.find((item) => item.tagValue === this.currentTagValue)?.totalTransInterval
    return total ? (value / total * 100) : 0
  }

  doExport() {
    this.$confirm(this.formatI18n("/数据/数据洞察/RFM分析/将根据当前查询条件生成报表，确认导出吗？"), this.formatI18n('/数据/数据洞察/RFM分析/联动明细导出'), {
      confirmButtonText: this.formatI18n("/公用/按钮", "确定") as string,
      cancelButtonText: this.formatI18n("/公用/按钮", "取消") as string,
    }).then(() => {
      const params = new QueryMemberConsumeDataFilter()
      params.tagId = this.taskInfo?.tagTemplateUuid
      params.tagValue = this.currentTagValue || null
      RfmAnalysisApi.exportUserDetail(params).then((res) => {
        if (res.code === 2000) {
          this.downloadCenterFlag = true;
        } else {
          throw new Error(res.msg!)
        }
      }).catch((error) => {
        this.$message.error(error.message)
      })
    });
  }

  doDownloadDialogClose() {
    this.downloadCenterFlag = false;
  }
};