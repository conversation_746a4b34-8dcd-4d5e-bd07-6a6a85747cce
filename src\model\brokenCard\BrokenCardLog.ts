/*
 * @Author: 黎钰龙
 * @Date: 2024-10-24 10:48:45
 * @LastEditTime: 2024-10-24 10:48:56
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\brokenCard\BrokenCardLog.ts
 * 记得注释
 */
export default class BrokenCardLog {
  // 卡号
  cardCode: Nullable<string> = null
  // 操作类型
  type: Nullable<string> = null
  // 操作人
  operator: Nullable<string> = null
  // 操作时间
  created: Nullable<Date> = null
}