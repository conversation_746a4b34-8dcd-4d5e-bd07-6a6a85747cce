/*
 * @Author: 黎钰龙
 * @Date: 2024-05-14 19:13:40
 * @LastEditTime: 2024-05-27 18:02:56
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\MarketingBudget\MarketingBudgetEdit.ts
 * 记得注释
 */
import FormItem from 'cmp/formitem/FormItem';
import I18nPage from 'common/I18nDecorator';
import BrowserMgr from 'mgr/BrowserMgr';
import MarketBudget from 'model/promotion/MarketBudget';
import { MarketBudgetActivityEnum } from 'model/promotion/MarketBudgetActivityEnum';
import { Component, Model, Prop, Vue, Watch } from 'vue-property-decorator';
@Component({
  name: 'MarketingBudgetEdit',
  components: {
    FormItem
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/营销/营销申请'
  ],
  auto: true
})
export default class MarketingBudgetEdit extends Vue {
  $refs: any
  @Model('change') value: MarketBudget
  @Prop({ type: String }) activityType: MarketBudgetActivityEnum; //活动类型
  @Prop({ type: Boolean }) disabled: boolean; //是否展示

  currentVal: Nullable<MarketBudget> = new MarketBudget()

  @Watch('value', { deep: true, immediate: true })
  handleValue() {
    this.currentVal = this.value || new MarketBudget()
  }

  @Watch('currentVal', { deep: true })
  handle(value: MarketBudget) {
    const res = this.isPermit ? value : null
    console.log('营销预算提交的res', res);
    this.$emit('change', res)
  }

  //是否拥有营销预算权限
  get isPermit() {
    const OAActivities = BrowserMgr.LocalStorage.getItem('sysConfig')?.platformAuditActivity || []
    return OAActivities.indexOf(this.activityType) > -1
  }

  get rules() {
    return {
      headquartersCost: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
      orgProperties: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
      estimatedSales: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
      estimatedDiscountCost: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
      estimatedDiscountRate: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
    }
  }

  doValidate() {
    if (this.isPermit) {
      return this.$refs.ruleForm.validate()
    } else {
      return new Promise(resolve => {
        resolve(true)
      })
    }
  }

  //自动计算折扣率
  doComputeRate() {
    if (this.currentVal?.estimatedSales) {
      this.currentVal.estimatedDiscountRate = ((Number(this.currentVal.estimatedDiscountCost || 0) / Number(this.currentVal.estimatedSales)) * 100).toFixed(2) as any
    }
  }
};