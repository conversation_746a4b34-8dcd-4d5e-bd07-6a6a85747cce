import ApiClient from 'http/ApiClient'
import CardAccount from 'model/card/adjustbill/CardAccount'
import CardDepositBillConfig from 'model/card/depositbill/CardDepositBillConfig'
import CardDepositBill from 'model/card/depositbill/CardDepositBill'
import CardDepositBillFilter from 'model/card/depositbill/CardDepositBillFilter'
import CardDepositBillLine from 'model/card/depositbill/CardDepositBillLine'
import CardDepositBillSaveRequest from 'model/card/depositbill/CardDepositBillSaveRequest'
import CardDepositBillStats from 'model/card/depositbill/CardDepositBillStats'
import Response from 'model/common/Response'
import CardDepositBillAmountSummary from "model/card/depositbill/CardDepositBillAmountSummary";

export default class CardDepositBillApi {
  /**
   * 审核预付卡充值单
   * 审核预付卡充值单。
   * 
   */
  static audit(billNumber: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/card-deposit-bill/audit/${billNumber}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 审核预付卡充值单
   * 审核预付卡充值单。
   * 
   */
  static batchAudit(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/card-deposit-bill/batch/audit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除预付卡充值单
   * 删除预付卡充值单。
   * 
   */
  static batchRemove(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/card-deposit-bill/batch/remove`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 计算卡数量
   * 计算卡数量。
   *
   */
  static calculateCartCount(body: CardDepositBillSaveRequest): Promise<Response<number>> {
    return ApiClient.server().post(`/v1/card-deposit-bill/cartCount/calculate`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取预付卡充值单详情
   * 获取预付卡充值单详情。
   * 
   */
  static get(billNumber: string): Promise<Response<CardDepositBill>> {
    return ApiClient.server().get(`/v1/card-deposit-bill/get/${billNumber}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取储值卡充值单配置
   * 获取储值卡充值单配置。
   *
   */
  static getConfig(): Promise<Response<CardDepositBillConfig>> {
    return ApiClient.server().get(`/v1/card-deposit-bill/getConfig`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询卡账户信息
   * 查询卡账户信息。
   * 
   */
  static getCardInfo(code: string): Promise<Response<CardAccount>> {
    return ApiClient.server().get(`/v1/card-deposit-bill/getCardInfo/${code}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取预付卡充值单修改详情
   * 获取预付卡充值单修改详情。
   * 
   */
  static getModify(billNumber: string): Promise<Response<CardDepositBill>> {
    return ApiClient.server().get(`/v1/card-deposit-bill/get/modify/${billNumber}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量预付卡充值单
   * 批量预付卡充值单。
   * 
   */
  static importExcel(body: any, orgId?: string): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/card-deposit-bill/importExcel`, body, {
      params: {
        orgId: orgId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询预付卡充值单
   * 分页查询预付卡充值单。
   * 
   */
  static query(body: CardDepositBillFilter): Promise<Response<CardDepositBill[]>> {
    return ApiClient.server().post(`/v1/card-deposit-bill/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取预付卡充值单明细详情
   * 获取预付卡充值单明细详情
   * 
   */
  static queryDetail(billNumber: string, page: number, pageSize: number): Promise<Response<CardDepositBillLine[]>> {
    return ApiClient.server().get(`/v1/card-deposit-bill/queryDetail/${billNumber}`, {
      params: {
        page: page,
        pageSize: pageSize
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取预付卡充值单明细总计充值金额
   * 获取预付卡充值单明细总计充值金额。
   *
   */
  static getAmountSummary(billNumber: string): Promise<Response<CardDepositBillAmountSummary>> {
    return ApiClient.server().get(`/v1/card-deposit-bill/queryDetailTotalAmount/${billNumber}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改预付卡充值单
   * 修改预付卡充值单。
   *
   */
  static modify(body: CardDepositBillSaveRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/card-deposit-bill/modify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建预付卡充值单
   * 新建预付卡充值单。
   * 
   */
  static save(body: CardDepositBillSaveRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/card-deposit-bill/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建并审核预付卡充值单
   * 新建并审核预付卡充值单。
   * 
   */
  static saveAndAudit(body: CardDepositBillSaveRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/card-deposit-bill/saveAndAudit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 预付卡调单汇总
   * 预付卡调单汇总。
   * 
   */
  static stats(body: CardDepositBillFilter): Promise<Response<CardDepositBillStats>> {
    return ApiClient.server().post(`/v1/card-deposit-bill/stats`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
