import { DayOfWeek } from "./DayOfWeek"
import TagCalculatorRule from "./TagCalculatorRule"
import { TagCalculatorTaskType } from "./TagCalculatorTaskType"
import { TagCalculatorTaskUpdateMode } from "./TagCalculatorTaskUpdateMode"
import { UpdateCycle } from "./UpdateCycle"

export default class TagCalculatorTaskV2SaveOrModifyRequest {
  // uuid
  uuid: Nullable<string> = null
  // 创建方式
  type: Nullable<TagCalculatorTaskType> = null
  // 标签模版Id
  tagTemplateUuid: Nullable<string> = null
  // 计算规则
  rule: Nullable<TagCalculatorRule> = null
  // 更新方式 [手动更新，周期跟新]
  updateMode: Nullable<TagCalculatorTaskUpdateMode> = null
  // 更新周期
  updateCycle: Nullable<UpdateCycle> = null
  // 每周更新日期
  dayOfWeek: Nullable<DayOfWeek> = null
  // 每月更新日期
  dayOfMonth: Nullable<number> = null
  // 是否是每月最后一天
  lastOfMonth: Nullable<boolean> = null
  // 说明
  remark: Nullable<string> = null
}