import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import CollectPointsActivity from "model/v2/coupon/collectpoints/CollectPointsActivity"

export default class CollectPointsApi {
  /**
 * 集点活动详情
 * 集点活动详情。
 * 
 */
  static getCollectPoints(id: string): Promise<Response<CollectPointsActivity>> {
    return ApiClient.server().get(`/v1/coupon-activity/getCollectPoints/${id}`, {
    }).then((res) => {
      return res.data
    })
  }


  /**
   * 新建或修改集点活动
   * 新建或修改集点活动
   * 
   */
  static saveCollectPoints(body: CollectPointsActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/coupon-activity/saveCollectPoints`, body, {
    }).then((res) => {
      return res.data
    })
  }

}