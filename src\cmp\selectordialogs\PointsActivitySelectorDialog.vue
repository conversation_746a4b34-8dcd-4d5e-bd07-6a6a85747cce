<template>
  <el-dialog :title="formatI18n('/公用/公共组件/积分活动选择弹框组件/标题/选择活动')" class="points-activity-selector-dialog" append-to-body width="70%"
    :close-on-click-modal="false" :visible.sync="dialogShow">
    <div class="wrap">
      <el-form label-width="150px" class="query">
        <el-row>
          <el-col :span="8">
            <el-form-item :label="formatI18n('/公用/公共组件/积分活动选择弹框组件/查询/活动名称')" :title="formatI18n('/公用/公共组件/积分活动选择弹框组件/查询/活动名称')">
              <el-input v-model="filter.nameLike" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="formatI18n('/公用/公共组件/积分活动选择弹框组件/查询/活动号')" :title="formatI18n('/公用/公共组件/积分活动选择弹框组件/查询/活动号')">
              <el-input v-model="filter.numberLike" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="formatI18n('/公用/公共组件/积分活动选择弹框组件/查询/活动时间')" title="formatI18n('/公用/公共组件/积分活动选择弹框组件/查询/活动时间')">
              <el-date-picker type="daterange" clearable range-separator="-" v-model="timeRange" :start-placeholder="formatI18n('/公用/查询条件/提示/开始日期')"
                :end-placeholder="formatI18n('/公用/查询条件/提示/结束日期')" style="width: 100%"></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item :label="formatI18n('/公用/公共组件/积分活动选择弹框组件/查询/活动类型')" title="formatI18n('/公用/公共组件/积分活动选择弹框组件/查询/活动类型')">
              <el-select :placeholder="formatI18n('/公用/下拉框/提示', '全部')" v-model="filter.typeEquals" style="width: 100%" :disabled="true">
                <el-option :label="formatI18n('/公用/过滤器', '商品满额积分加倍')" value="GOODS_GAIN_POINTS_SPEED">{{ formatI18n('/公用/过滤器', '商品满额积分加倍') }}
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="formatI18n('/公用/公共组件/积分活动选择弹框组件/查询/活动状态')">
              <el-select v-model="filter.stateEquals" style="width: 100%">
                <el-option :label="formatI18n('/公用/下拉框/提示/全部')" :value="null">
                </el-option>
                <el-option :label="formatI18n('/公用/过滤器/未审核')" value="INITAIL">
                </el-option>
                <el-option :label="formatI18n('/公用/过滤器/未开始')" value="UNSTART">
                </el-option>
                <el-option :label="formatI18n('/公用/过滤器/进行中')" value="PROCESSING">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-button type="primary" @click="doSearch()">{{formatI18n('/公用/按钮/查询')}}</el-button>
              <el-button @click="doReset()">{{formatI18n('/公用/按钮/重置')}}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-row>
        <el-col :span="18">
          <el-row class="table-wrap" v-loading="loading.query">
            <el-row class="thead">
              <el-col :span="1">
                <el-checkbox @change="doCheckAll($event)" v-model="checkAll" />
              </el-col>
              <el-col :span="5">{{formatI18n('/公用/公共组件/积分活动选择弹框组件/表格/活动号')}}</el-col>
              <el-col :span="5">{{formatI18n('/公用/公共组件/积分活动选择弹框组件/表格/活动名称')}}</el-col>
              <el-col :span="5">{{formatI18n('/公用/公共组件/积分活动选择弹框组件/表格/活动时间')}}</el-col>
              <el-col :span="4">{{formatI18n('/公用/公共组件/积分活动选择弹框组件/表格/状态')}}</el-col>
              <el-col :span="4">{{formatI18n('/公用/公共组件/积分活动选择弹框组件/表格/活动商品')}}</el-col>
            </el-row>
            <el-row class="tbody" v-if="!loading.query">
              <template v-if="currentList && currentList.length > 0">
                <el-row v-for="(item, index) of currentList" :key="item.activityId" class="trow">
                  <el-col :span="1">
                    <el-checkbox :disabled="isAlwaysSelected(item.activityId)" @change="doCheck($event, index)" v-model="checkboxList[index]" />
                  </el-col>
                  <el-col @click.native="doCheckRow(index)" :span="5" :title="item.activityId">{{
                    item.activityId
                  }}
                  </el-col>
                  <el-col @click.native="doCheckRow(index)" :span="5" :title="item.name">{{ item.name }}</el-col>
                  <el-col @click.native="doCheckRow(index)" :span="5"
                    :title="`${$options.filters.dateFormate2(item.beginDate)} ${formatI18n('/公用/公共组件/积分活动选择弹框组件/表格/至')} ${$options.filters.dateFormate2(item.endDate)}`">
                    {{ item.beginDate|dateFormate2 }}
                    {{ formatI18n('/公用/公共组件/积分活动选择弹框组件/表格/至') }}
                    {{ item.endDate|dateFormate2 }}
                  </el-col>
                  <el-col @click.native="doCheckRow(index)" :span="4">
                    <ActivityState :state="item.state" />
                  </el-col>
                  <el-col @click.native="doCheckRow(index)" :span="4">
                    <el-button type="text" @click.stop="showGoodsDialog(item.type, item.activityId)">
                      {{ formatI18n('/公用/公共组件/积分活动选择弹框组件/表格/查看') }}
                    </el-button>
                  </el-col>
                </el-row>
              </template>
              <el-row v-if="!currentList || currentList.length === 0" class="trow" style="text-align: center;color: #909399">
                {{ formatI18n('/公用/提示/暂无数据') }}
              </el-row>
            </el-row>
          </el-row>
        </el-col>
        <el-col :span="6" style="padding-left: 5px">
          <el-row class="right-table">
            <el-row class="thead">
              {{formatI18n('/公用/公共组件/积分活动选择弹框组件/表格/已选活动：')}}{{selected?(selected.filter(e=>e.activityId)).length: 0}}
            </el-row>
            <el-row style="padding: 5px;">
              <el-input style="width: 100%;" @keyup.enter.native="filterSelected()" @change="filterSelected()" v-model="selectedFilter" clearable
                @clear="filterSelected()" :placeholder="formatI18n('/公用/公共组件/积分活动选择弹框组件/查询/请输入活动号/名称')"></el-input>
            </el-row>
            <el-row class="tbody">
              <template v-if="filteredSelected && filteredSelected.length > 0">
                <el-row class="trow" :key="item.activityId" style="position: relative;display: flex;align-items: center"
                  v-for="(item, index) of filteredSelected" :title="{id:item.activityId, name:item.name}|idName">
                  <div class="left">{{ {id: item.activityId, name: item.name}|idName }}</div>
                  <div class="clear-btn" style="display: none;cursor: pointer" v-if="!isAlwaysSelected(item.activityId)"><a
                      @click="delItem(item, index)">{{ formatI18n('/公用/公共组件/品牌选择弹框组件/表格/清除') }}</a>
                  </div>
                </el-row>
              </template>
              <el-row v-if="!filteredSelected || filteredSelected.length === 0" class="trow" style="text-align: center;color: #909399">
                {{ formatI18n('/公用/提示/暂无数据') }}
              </el-row>
            </el-row>
          </el-row>
        </el-col>
      </el-row>
    </div>
    <div class="page" style="margin-top: 15px">
      <el-pagination :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
        @current-change="handleCurrentChange($event)" @size-change="handleSizeChange($event)" background
        layout="total, prev, pager, next, sizes,  jumper">
      </el-pagination>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="dialogShow = false">{{formatI18n('/公用/按钮/取消')}}</el-button>
      <el-button size="small" type="primary" @click="doModalClose()">{{formatI18n('/公用/按钮/确定')}}</el-button>
    </div>
    <el-dialog :title="formatI18n('/公用/公共组件/积分活动选择弹框组件/活动商品/活动商品')" append-to-body :close-on-click-modal="false" :visible.sync="goodsDialog.visible">
      <el-row v-if="goodsDialog.activity && goodsDialog.activity.activityBody">
        <el-row style="margin-top: 15px">
          <el-col :span="4" class="text-secondary">{{formatI18n('/公用/公共组件/积分活动选择弹框组件/活动商品/活动号：')}}</el-col>
          <el-col :span="20">{{goodsDialog.activity.activityBody.activityId}}</el-col>
        </el-row>
        <el-row style="margin-top: 15px">
          <el-col :span="4" class="text-secondary">{{formatI18n('/公用/公共组件/积分活动选择弹框组件/活动商品/活动名称：')}}</el-col>
          <el-col :span="20">{{goodsDialog.activity.activityBody.name}}</el-col>
        </el-row>
        <el-row style="margin-top: 15px">
          <el-col :span="4" class="text-secondary">{{formatI18n('/公用/公共组件/积分活动选择弹框组件/活动商品/适用商品：')}}</el-col>
          <el-col :span="20">
            <GoodsScopeDtl :goods="goodsDialog.activity.goodsRange" />
          </el-col>
        </el-row>
      </el-row>
      <div slot="footer">
        <el-button @click="goodsDialog.visible = false" size="small" type="primary">{{
            formatI18n('/资料/门店/确 定')
          }}
        </el-button>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script lang="ts" src="./PointsActivitySelectorDialog.ts"/>

<style lang="scss" scoped>
.points-activity-selector-dialog {
  @import "SelectorDialogCommon";

  .table-wrap {
    border: none !important;

    .trow {
      border-bottom: 1px solid var(--border-color);
    }
  }

  .text-secondary {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    color: rgba(51, 51, 51, 0.65);
  }
}
</style>