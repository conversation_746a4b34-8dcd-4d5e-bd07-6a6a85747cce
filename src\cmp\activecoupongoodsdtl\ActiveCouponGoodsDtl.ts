import {Component, Prop, Vue, Watch} from 'vue-property-decorator'

@Component({
  name: 'ActiveCouponGoodsDtl',
  components: {}
})
export default class ActiveCouponGoodsDtl extends Vue {
  title = ''
  constructData: any = []
  @Prop()
  data: any
  @Watch('data')
  onDataChange(value: any) {
    if (value) {
      this.setBindValue(value)
    }
  }
  setBindValue(data: any) {
    if (data) {
      if (!data.limit) {
        this.title = this.formatI18n('/营销/券礼包活动/新建商品满额发券活动', '全部商品可用')
      } else {
        if (data.limit && data.excludePrecondition) {
          this.title = this.formatI18n('/营销/券礼包活动/新建商品满额发券活动', '指定不适用商品')
        } else {
          this.title = this.formatI18n('/营销/券礼包活动/新建商品满额发券活动', '指定适用商品')
        }
      }
      if (data && data.includeGoods.length > 0 || data.excludeGoods.length > 0) {
        this.constructData.push({
          name: this.formatI18n('/公用/券模板', '单品'),
          belong: data.includeGoods.length > 0 ?  this.formatI18n('/公用/券模板', '属于') : this.formatI18n('/公用/券模板', '不属于'),
          range: ''
        })
        let str = ''
        data.includeGoods.forEach((item: any) => {
          str += item.id + `[${item.name}];`
        })
        data.excludeGoods.forEach((item: any) => {
          str += item.id + `[${item.name}];`
        })
        this.constructData[this.constructData.length - 1].range = str
      }
      if (data.includeBrands.length > 0 || data.excludeBrands.length > 0) {
        this.constructData.push({
          name: this.formatI18n('/公用/券模板', '品牌'),
          belong: data.includeBrands.length > 0 ? this.formatI18n('/公用/券模板', '属于') : this.formatI18n('/公用/券模板', '不属于'),
          range: ''
        })
        let str = ''
        data.includeBrands.forEach((item: any) => {
          str += item.id + `[${item.name}];`
        })
        data.excludeBrands.forEach((item: any) => {
          str += item.id + `[${item.name}];`
        })
        this.constructData[this.constructData.length - 1].range = str
      }
      if (data.includeCategories.length > 0 || data.excludeCategories.length > 0) {
        this.constructData.push({
          name: this.formatI18n('/公用/券模板', '品类'),
          belong: data.includeCategories.length > 0 ? this.formatI18n('/公用/券模板', '属于') : this.formatI18n('/公用/券模板', '不属于'),
          range: ''
        })
        let str = ''
        data.includeCategories.forEach((item: any) => {
          str += item.id + `[${item.name}];`
        })
        data.excludeCategories.forEach((item: any) => {
          str += item.id + `[${item.name}];`
        })
        this.constructData[this.constructData.length - 1].range = str
      }
    }
  }
}