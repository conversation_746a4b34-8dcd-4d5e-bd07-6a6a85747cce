import { Component, Prop, Vue, Watch } from "vue-property-decorator";

@Component({
  name: "MemberFormItem",
  components: {},
})
export default class MemberFormItem extends Vue {
  @Prop()
  label: string;
  @Prop()
  prefixIcon: string;
  @Prop()
  contentAlign: string;
  @Prop({ default: true })
  tooltip: boolean;

  get alignRight() {
    return this.contentAlign == "right";
  }

  hasEllipsis: boolean = false;
  observer: MutationObserver;

  mounted() {
    if (!this.tooltip) return;
    const observer = new MutationObserver(this.contentChange);
    // @ts-ignore
    observer.observe(this.$refs.content, {
      childList: true,
      subtree: true,
      characterData: true,
    });
    this.observer = observer;
  }

  beforeUnmount() {
    this.observer && this.observer.disconnect();
  }

  contentChange() {
    this.$nextTick(() => {
      const el = this.$refs.content;
      // @ts-ignore
      this.hasEllipsis = el.offsetWidth < el.scrollWidth;
    });
  }
}
