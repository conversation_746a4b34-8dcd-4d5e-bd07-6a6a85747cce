<template>
  <div class="address-selector">
    <div style="position: relative" @mouseover="doCascadeOver"
         @mouseleave="doCascadeLeave">
      <i @click.stop="doClearAddress" v-if="clearable && clearFlag" style="position: absolute;right: 10px;top: 12px;z-index: 3000;cursor: pointer" class="el-icon-error"></i>
      <el-cascader
          style="width: 100%"
          :placeholder="i18n('/公用/券模板/请选择')"
          v-model="valueCopy"
          :options="options"
          @change="doHandleChange"
          @active-item-change="doHandleChange">
      </el-cascader>
    </div>
  </div>
</template>

<script lang="ts" src="./AddressSelector.ts">
</script>

<style lang="scss" scoped>
  .address-selector {
  }
</style>
