/*
 * @Author: haiding <EMAIL>
 * @Date: 2025-03-06 14:30:50
 * @LastEditors: 黎钰龙
 * @LastEditTime: 2025-05-22 13:59:20
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\cmp\render-page-props\RenderPageProps.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/*
 * @Author: haiding <EMAIL>
 * @Date: 2025-02-28 15:20:38
 * @LastEditors: haiding <EMAIL>
 * @LastEditTime: 2025-03-06 11:52:44
 * @FilePath: \new-kequn\src\pages\invest\page-manage\cmp\render-page-props\RenderPageProps.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import { FormMode } from 'model/local/FormMode';
import I18nPage from 'common/I18nDecorator';
import '../../cmp/page-props-components/index';
import { CmsConfigChannel } from 'model/template/CmsConfig';
@Component({
  name: 'RenderPageProps',
  mixins: [],
  components: {},
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理'
  ],
  auto: true
})
export default class RenderPageProps extends Vue {
  @Prop({ type: Boolean, default: false })
  readonly: boolean; //
  @Prop({ type: String, default: 'pagePropsComponents' })
  validateName: string; // 组件校验名称，editPage通过该名称进行组件校验及校验信息清除
  @Prop({ type: String, default: '展示样式' })
  label: string; // label名
  @Prop({ type: String })
  prefixDescribe: string; // 前置描述
  @Prop({ type: String })
  suffixDescribe: string; // 后置描述
  @Prop({ type: String, default: 'top' })
  labelPosition: string; // 对齐方式

  @Prop({ type: Boolean, default: true })
  required: boolean; // 是否必填
  @Prop({ type: String })
  relativeValidateName: string; // 联动校验名称
  @Prop()
  formKey: any;
  FormModeType = FormMode;
  $refs: any;

  @Prop()
  header: string;
  @Prop()
  activeProps: any;
  @Prop()
  activeIndex: number;
  @Prop({ type: Array })
  showComponentProp: any[];
  @Prop({ type: Array })
  renderTemplateList: any[];
  @Prop()
  activeUuid: string
  @Prop() advertiseChannel: Nullable<CmsConfigChannel[]>; //当前页面可用的投放渠道
  localModel: any = {};
  activeName: string = 'content'
  mounted() {
    this.localModel = {
      ...this.activeProps,
    };
  }
  get filtershowComponents() {
    if (this.activeIndex === -11) {
      return this.showComponentProp
    }
    return this.showComponentProp.filter(item => item.type === this.activeName)
  }
  get globalInvestTitle() {
    return this.$store.state.globalInvestTitle
  }
  @Watch('activeProps', { deep: true })
  handleActiveProps(value: any) {
    console.log('propsChange ---')
    this.localModel = {
      ...value,
    };
    this.validate()
  }
  @Watch('activeIndex', { deep: true })
  handleActiveIndex(value: any) {
    if (value !== -11) {
      this.activeName = 'content'
    }
  }
  handleClick(tab: any, e: any) {
    this.validate()
  }
  handleChange() {
    this.$emit('propsChange', { activeProps: this.localModel, activeIndex: this.activeIndex });
  }

  validate() {
    setTimeout(() => {
      this.$nextTick(() => {
        let validateArr: any[] = []
        this.filtershowComponents.forEach((item, index) => {
          console.log('组件，       item ==>', item, item.component)
          if (this.$refs[index + item.component]) {
            if (this.$refs[index + item.component][0].validate) {
              validateArr.push(this.$refs[index + item.component][0].validate())
            } else {
              console.log(new Error(this.i18n('组件未暴露出校验方法')))
            }
          }
        })
        console.log('validateArr ==>', validateArr)
        if (validateArr.length !== 0) {
          Promise.all(validateArr).then(res => {
            this.$emit('validate', { cmpUuid: this.activeUuid, validateRes: false })
          }).catch(() => {
            this.$emit('validate', { cmpUuid: this.activeUuid, validateRes: true })
          })
        } else {
          this.$emit('validate', { cmpUuid: this.activeUuid, validateRes: false })
        }
        // 子组件validate了
        console.log('子组件validate了');
      })
    }, 100);

  }

  clearValidate() {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.clearValidate();
    }
  }
}
