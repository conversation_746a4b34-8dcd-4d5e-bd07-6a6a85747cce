import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import FileRecordFilter from "model/downloadcenter/FileRecordFilter";
import FileRecord from "model/downloadcenter/FileRecord";

export default class DownloadCenterApi {
  /**
   * 分页查询下载中心数据信息
   *
   */
  static query(body: FileRecordFilter): Promise<Response<FileRecord[]>> {
    return ApiClient.server().post(`/v1/download-center/query`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 删除数据
   *
   */
  static delete(uuid: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/download-center/delete/${uuid}`, {}, {}).then((res) => {
      return res.data
    })
  }
}
