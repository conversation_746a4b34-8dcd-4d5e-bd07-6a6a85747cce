import ApiClient from 'http/ApiClient'
import Response from 'model/response/Response'
import LoginResult from 'model/auth/LoginResult'
import DeleteRerult from 'model/download/DeleteRerult'
import UrlResult from 'model/download/UrlResult'

const qs = require('qs');
export default class DownLoadApi {
  /**
   * 获取验证码 username=test01&password=123456&captcha=9999&redirectUrl=null
   */
  static getDownloadFile(params: any): Promise<Response<LoginResult>> {
    // ?page=${params.page}&pageSize=${params.pageSize}&state=${params.state}
    return ApiClient.server().post(`mbr-web/downloadcenter/query.hd`, qs.stringify(params), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }).then((res) => {
      return res.data
    })
  }

  static delete(id: any): Promise<Response<DeleteRerult>> {

    return ApiClient.server().post(`crm-web/downloadcenter/delete.hd?fileId=${id}`, {}, {}).then((res) => {
      return res.data
    })
  }

  static getUrl(key: string): Promise<Response<UrlResult>> {

    return ApiClient.server().get(`crm-web/oss/url.hd?key=${key}`, {}, ).then((res) => {
      return res.data
    })
  }
}
