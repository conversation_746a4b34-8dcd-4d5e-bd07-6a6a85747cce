<template>
  <div class="jump-page">
    <slot></slot>
    <div class="right-link" :style="showTitile ? 'margin-left:0;' : ''">
      <div class="link-tips" style="width: 26px" v-if="!showTitile">{{ i18n('链接') }}</div>
      <el-form :model="jumpPageInfo" :rules="rules" ref="ruleForm">
        <el-form-item prop="firstLevelPage" style="line-height: 17px !important">
          <div class="link-box">
            <div class="link-name" @click="changeLinkDialog(true)" :title="linkTitleStr"
              :style="showTitile ? 'width:222px;margin-left:0;' : (linkWidth ? `width: ${linkWidth}px` : '')">
              {{ linkTitleStr }}
            </div>
            <i class="el-icon-delete" style="margin-left: 4px" v-if="jumpPageInfo && jumpPageInfo.firstLevelPage" @click="clearFrom(true)"></i>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <el-dialog :title="i18n('跳转页面')" append-to-body :visible.sync="dialogShow" width="1000px" :show-close="false" @close="changeLinkDialog(false)"
      :close-on-click-modal="false">
      <div class="jump-page-content">
        <div class="content-dialog">
          <div class="content-left">
            <div class="stair-list">
              <div class="stair-item" v-for="(item, index) in JumpPageDatas" :key="index">
                <div @click="onClickStair(item, index)" class="stair-item-name" :class="{ active: index == stairIndex }">
                  <span class="ellipsis" :title="item.name">{{ item.name }}</span>
                  <i v-if="item.children && !isOpened[item.type]" class="el-icon-arrow-down"></i>
                  <i v-if="item.children && isOpened[item.type]" class="el-icon-arrow-up"></i>
                </div>

                <template v-if="item.children">
                  <div @click="onClickChildItem(child, index)" class="stair-item-child"
                    :class="{ hide: !isOpened[item.type], active: child.type === activeChildType }" v-for="child in item.children" :key="child.name">
                    <span class="ellipsis" :title="child.name">{{ child.name }}</span>
                  </div>
                </template>
              </div>
            </div>
            <div class="second-list">
              <template v-for="(item, index) in activeSub">
                <div class="second-item" :class="{ active: index == secondIndex }" :title="item.name" :key="item.type"
                  @click="onClickSecond(item, index)">
                  {{ item.name }}
                </div>
              </template>
            </div>
          </div>
          <div class="content-right">
            <template v-if="levelOneData.name === i18n('页面') && activeSubItem.name === i18n('功能页面')">
              <el-form ref="form" class="plat-form-edit" style="flex: 1; max-height: 472px; overflow: auto">
                <div style="margin-bottom: 12px">
                  <el-row type="flex" align="middle">
                    <el-col :span="6">
                      <el-input :placeholder="i18n('请输入页面名称')" v-model="featurePageNameInput"></el-input>
                    </el-col>
                    <el-col :span="2" style="margin-left: 12px">
                      <el-button type="primary" size="medium" @click="doSearch">{{ i18n('搜索') }}</el-button>
                    </el-col>
                    <el-col :span="2" style="margin-left: 30px">
                      <el-button type="primary" size="medium" @click="featurePageName = ''">{{ i18n('重置') }}</el-button>
                    </el-col>
                  </el-row>
                </div>
                <el-table :data="FeaturePageList.filter((item) => item.name.indexOf(featurePageName) !== -1)" stripe>
                  <el-table-column :label="i18n('页面名称')">
                    <template slot-scope="scope">
                      <el-radio v-model="featuresNameRadio" :label="scope.row.name">
                        {{ scope.row.name }}
                      </el-radio>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form>
            </template>

            <template v-if="levelOneData.name === i18n('页面') && activeSubItem.name === i18n('自定义页面')">
              <el-form ref="form" class="plat-form-edit" style="flex: 1; max-height: 472px; overflow: auto">
                <div style="margin-bottom: 12px">
                  <el-row type="flex" align="middle">
                    <el-col :span="6">
                      <el-input :placeholder="i18n('请输入页面名称')" v-model="ContentTemplateFilter.nameLike"></el-input>
                    </el-col>
                    <el-col :span="2" style="margin-left: 12px">
                      <el-button type="primary" size="medium" @click="doSearch">{{ i18n('搜索') }}</el-button>
                    </el-col>
                    <el-col :span="2" style="margin-left: 30px">
                      <el-button type="primary" size="medium" @click="doReset">{{ i18n('重置') }}</el-button>
                    </el-col>
                  </el-row>
                </div>
                <el-table :data="ContentTemplate" stripe>
                  <el-table-column :label="i18n('页面名称')">
                    <template slot-scope="scope">
                      <el-radio v-model="radio" :label="scope.row.id">
                        {{ scope.row.name }}
                      </el-radio>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form>
              <el-pagination :current-page.sync="page.currentPage" :page-size="page.size" :page-sizes="[20, 30, 40]" :total="page.total" background
                layout="total, prev, pager, next, sizes,  jumper" @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange"
                style="margin-top: 20px"></el-pagination>
            </template>
            <template v-if="levelOneData.name === i18n('页面') && (activeSubItem.name === formatI18n('/页面/页面管理/定制页面'))">
              <el-form ref="form" class="plat-form-edit" style="flex: 1; max-height: 472px; overflow: auto">
                <div style="margin-bottom: 12px">
                  <el-row type="flex" align="middle">
                    <el-col :span="6">
                      <el-input :placeholder="i18n('请输入页面名称')" v-model="customizationPageName"></el-input>
                    </el-col>
                    <el-col :span="2" style="margin-left: 12px">
                      <el-button type="primary" size="medium" @click="doSearch">{{ i18n('搜索') }}</el-button>
                    </el-col>
                    <el-col :span="2" style="margin-left: 30px">
                      <el-button type="primary" size="medium" @click="customizationPageName = ''">{{ i18n('重置')
                      }}</el-button>
                    </el-col>
                  </el-row>
                </div>
                <el-table :data="customizationList.filter((item) => item.name.indexOf(customizationPageName) !== -1)" stripe>
                  <el-table-column :label="i18n('页面名称')">
                    <template slot-scope="scope">
                      <el-radio v-model="customizationPageNameRadio" :label="scope.row.name">
                        {{ scope.row.name }}
                      </el-radio>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form>
            </template>

            <el-form-item v-if="filterActivity()">
              <el-radio v-for="item in options" :key="item.key" :label="item.key" v-model="activityRange" @change="handleChange">
                {{ item.caption }}
              </el-radio>
              <span style="font-size: 12px; color: #a1a6ae; line-height: 16px" v-show="activityRange === 'part'">
                {{ i18n('已选择') }}
                <span>{{ jumpPageInfo.activityIds.length }}</span>
                {{ i18n('个活动') }}
              </span>
              <div>
                <el-button style="margin-left: 110px" v-show="activityRange === 'part'" type="primary" @click.stop="goUp">{{
                    i18n('选择') }}</el-button>
              </div>
            </el-form-item>

            <template v-if="activeSubItem.name === i18n('活动详情')">
              <el-form ref="form" class="plat-form-edit" style="overflow: auto; padding-right: 20px;">
                <div style="margin-bottom: 12px">
                  <el-row type="flex" align="middle" :gutter="6">
                    <el-col :span="6">
                      <el-input v-model="filter.activeNo" :placeholder="i18n('请输入活动号')" />
                    </el-col>
                    <el-col :span="6">
                      <el-input v-model="filter.keyWords" :placeholder="i18n('请输入活动名称')" />
                    </el-col>
                    <el-col :span="6">
                      <el-select v-model="filter.state" @change="queryActiveDetailList()">
                        <el-option value="UNSTART" :label="i18n('未开始')"></el-option>
                        <el-option value="PROCESSING" :label="i18n('进行中')"></el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="2" style="margin-left: 12px">
                      <el-button type="primary" size="medium" @click="doSearch">{{ i18n('搜索') }}</el-button>
                    </el-col>
                    <el-col :span="2" style="margin-left: 30px">
                      <el-button type="primary" size="medium" @click="doReset">{{ i18n('重置') }}</el-button>
                    </el-col>
                  </el-row>
                </div>
                <el-table :data="activityList" stripe v-loading="activityListLoading">
                  <el-table-column :label="formatI18n('/营销/券礼包活动/券礼包活动', '活动号/活动名称')" fixed="left" prop="activityId">
                    <template v-slot="scope">
                      <div class="activity-list-radio">
                        <el-radio v-model="activityListRadio" @change="activityListRadioChange" :label="scope.row.activityId">
                          <div></div>
                        </el-radio>
                        <div style="display: flex;flex-direction: column;">
                          <div>{{ scope.row.activityId | strFormat }}</div>
                          <div style="display: block;"> {{ scope.row.name }}</div>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column :label="formatI18n('/营销/券礼包活动/券礼包活动', '活动时间')" width="180">
                    <template v-slot="scope">
                      <div v-if="scope.row.beginDate && scope.row.endDate">
                        {{ scope.row.beginDate | dateFormate2 }}
                        {{ formatI18n("/营销/券礼包活动/券礼包活动", "至") }}
                        {{ scope.row.endDate | dateFormate2 }}
                      </div>
                      <div v-else>--</div>
                    </template>
                  </el-table-column>
                  <el-table-column :label="i18n('活动状态')" align="center" prop="state" width="150">
                    <template slot-scope="scope">
                      <ActivityStateTag :stateEquals="scope.row.state"></ActivityStateTag>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form>
              <el-pagination :current-page.sync="page.currentPage" :page-size="page.size" :page-sizes="[20, 30, 40]" :total="page.total" background
                layout="total, prev, pager, next, sizes,  jumper" @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange"
                style="margin-top: 20px"></el-pagination>
            </template>

            <div class="sys-page" v-if="filterSysPage()">
              <div class="sys-page-top">
                <div class="line"></div>
                <div class="text">{{ i18n('邀请有礼') }}</div>
                <div class="right">
                  <el-checkbox v-model="sysPageChecked"></el-checkbox>
                </div>
              </div>
              <div class="sys-page-buttom">
                <div class="text">- {{ i18n('邀请有礼页面') }}</div>
              </div>
            </div>
            <div class="sys-page" v-if="activeSubItem.name === i18n('H5链接')">
              <div class="sys-page-top">
                <div class="line"></div>
                <div class="text">{{ activeSubItem.name }}</div>
              </div>
              <div class="h5-bottom">
                <div class="text">{{ i18n('H5链接') }}</div>
                <el-input v-model="jumpPageInfo.h5Link" :placeholder="i18n('请填写H5链接')" style="width: 402px"></el-input>
              </div>
            </div>
            <div class="sys-page" v-if="activeSubItem.name === i18n('小程序路径')">
              <div class="sys-page-top">
                <div class="line"></div>
                <div class="text">{{ activeSubItem.name }}</div>
              </div>
              <div class="mobile-bottom">
                <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
                  <el-form-item prop="appJumpType">
                    <div class="item">
                      <div class="label" :style="{'word-break': isEnglish ? 'keep-all' : 'normal'}">{{ i18n('跳转方式') }}</div>
                      <div style="margin-top: 4px">
                        <el-radio v-model="jumpPageInfo.appJumpType" label="regular" size="small">{{ i18n('常规跳转')
                        }}</el-radio>
                        <!-- <el-radio v-model="jumpPageInfo.appJumpType" label="halfScreen" size="small">微信半屏小程序</el-radio> -->
                      </div>
                      <div class="tips" v-if="jumpPageInfo.appJumpType === 'halfScreen'">{{ i18n('打开半屏小程序需先前往微信公众平台 - 设置 - 第三方设置') }}
                      </div>
                      <div class="tips" v-if="jumpPageInfo.appJumpType === 'halfScreen'">-{{ i18n('半屏小程序管理申请，申请通过后才可使用') }}</div>
                    </div>
                  </el-form-item>
                  <el-form-item prop="appId">
                    <div class="item">
                      <div class="label">APPID</div>
                      <el-input v-model="jumpPageInfo.appId" :placeholder="i18n('请填写小程序的APPID')" style="width: 402px"></el-input>
                    </div>
                  </el-form-item>
                  <el-form-item prop="appPath">
                    <div class="item">
                      <div class="label">{{ i18n('路径') }}</div>
                      <el-input v-model="jumpPageInfo.appPath" :placeholder="i18n('请填写跳转的小程序路径')" style="width: 402px"></el-input>
                    </div>
                  </el-form-item>
                  <el-form-item prop="appParams">
                    <div class="item">
                      <div class="label" :style="{'word-break': isEnglish ? 'keep-all' : 'normal'}">{{ i18n('传参选择') }}</div>
                      <div style="margin-top: 4px">
                        <el-checkbox-group v-model="jumpPageInfo.appParams">
                          <el-checkbox label="memberId">{{ i18n('会员') }}MemberID</el-checkbox>
                          <el-checkbox label="storeCode">{{ i18n('门店代码') }}</el-checkbox>
                        </el-checkbox-group>
                      </div>
                    </div>
                  </el-form-item>
                </el-form>
              </div>
            </div>
            <!-- 异业合作 -->
            <div class="sys-page" v-if="activeSubItem.name === i18n('异业合作')">
              <div class="h5-bottom">
                <div class="text">{{ i18n('跳转外部H5') }}</div>
                <el-radio-group v-model="jumpPageInfo.h5Link" style="width: auto">
                  <el-radio label="FSL_MOVIE_TIKET">{{ i18n('福司令电影票H5') }}</el-radio>
                  <el-radio label="MEITUAN">{{ i18n('百联美团H5') }}</el-radio>
                </el-radio-group>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="doCancel">{{ i18n('取消') }}</el-button>
        <el-button size="small" type="primary" @click="doModalClose()">{{ i18n('确定') }}</el-button>
      </div>
    </el-dialog>
    <ElasticLayerPage ref="childRef" @change="handleChange" :activityType="jumpPageInfo.firstLevelPage" @submit="doSubmit" />
  </div>
</template>

<script lang="ts" src="./PageJump.ts"></script>

<style lang="scss" scoped>
.jump-page {
  .right-link {
    cursor: pointer;
    margin-left: 16px;
    display: flex;
    font-weight: 400;
    font-size: 13px;
    line-height: 18px;
    height: 18px;

    .link-tips {
      color: #5a5f66;
    }

    .link-box {
      display: flex;
      align-items: center;
    }

    .link-name {
      line-height: 17px;
      color: #007eff;
      margin-left: 12px;
      width: 120px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }

  &-content {
    width: 100%;

    .content-dialog {
      display: flex;
      width: 100%;
      background: #ffffff;
      border-radius: 2px;
      border: 1px solid #d7dfeb;

      .content-left {
        display: flex;
        width: 310px;
        border-right: 1px solid #dde2eb;
        padding: 12px;

        .stair-list {
          width: 50%;
          // padding: 8px;
          background: #f9fafc;
          border-radius: 4px;
          height: 500px;
          overflow-y: auto;

          // height: 472px;
          .stair-item {
            width: 120px;
            // height: 40px;
            line-height: 40px;
            border-radius: 4px;
            font-weight: 400;
            font-size: 14px;
            color: #36445a;
            text-align: left;
            box-sizing: border-box;
            // padding-left: 20px;
            cursor: pointer;

            .hide {
              display: none;
            }

            &-name {
              width: 120px;
              height: 40px;
              line-height: 40px;
              border-radius: 4px;
              font-weight: 400;
              font-size: 14px;
              color: #36445a;
              text-align: left;
              box-sizing: border-box;
              padding-left: 12px;
              cursor: pointer;
              white-space: nowrap;
              overflow: hidden;
            }

            &:hover {
              cursor: pointer;
            }

            &-child {
              height: 40px;
              line-height: 40px;
              border-radius: 4px;
              font-weight: 400;
              font-size: 14px;
              color: #36445a;
              text-align: left;
              box-sizing: border-box;
              padding-left: 20px;
              cursor: pointer;
              white-space: nowrap;
              overflow: hidden;

              &.active {
                background: #eaf3ff;
                border-radius: 4px;
                color: #318bff;
              }

              &:hover {
                cursor: pointer;
              }
            }
          }

          .active {
            background: #007eff;
            border-radius: 4px;
            color: #fff;
          }

          .ellipsis {
            display: inline-block;
            max-width: 100%;
            text-overflow: ellipsis;
            overflow: hidden;
            vertical-align: middle;
          }
        }

        .second-list {
          flex: 1;
          width: 50%;
          padding: 8px;
          border-radius: 4px;

          // height: 472px;
          .second-item {
            width: 100%;
            height: 40px;
            line-height: 40px;
            border-radius: 4px;
            font-weight: 400;
            font-size: 14px;
            color: #36445a;
            text-align: center;
            box-sizing: border-box;
            cursor: pointer;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .active {
            background: #eaf3ff;
            border-radius: 4px;
            color: #318bff;
          }
        }
      }

      .content-right {
        flex: 1;
        height: 496px;
        padding: 12px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .activity-list-radio {
          display: flex;
          align-items: center;

          ::v-deep .el-radio {
            margin-right: 0;
          }
        }

        .title-search {
          display: flex;
        }

        .pages-title {
          width: 100%;
          height: 32px;
          line-height: 32px;
          font-weight: 600;
          font-size: 13px;
          color: #020203;
          background-color: #dde2eb;
          margin-top: 12px;
        }

        .el-radio-group {
          width: 100%;
        }

        .pages-item {
          // display: flex;
          // align-items: center;
          width: 100%;
          height: 28px;

          &:nth-child(2n) {
            background-color: #f4f6fa;
          }
        }

        .sys-page {
          // display: flex;
          width: 100%;

          .sys-page-top {
            width: 100%;
            display: flex;
            align-items: center;

            .line {
              width: 4px;
              height: 16px;
              background: #007eff;
            }

            .text {
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              font-size: 16px;
              color: #111111;
              margin-left: 8px;
            }

            .right {
              flex: 1;
              display: flex;
              justify-content: flex-end;
            }
          }

          .sys-page-buttom {
            height: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            color: #a1a6ae;
            margin-top: 8px;
          }

          .h5-bottom {
            display: flex;
            align-items: center;

            .text {
              text-indent: 42px;
              margin-right: 12px;
              line-height: 15px;
            }
          }

          .mobile-bottom {
            width: 100%;

            .item {
              display: flex;
              align-items: center;
              flex-wrap: wrap;
              width: 100%;

              // margin-top: 12px;
              .label {
                width: 92px;
                text-align: right;
                font-weight: 400;
                font-size: 14px;
                color: #5a5f66;
                margin-right: 12px;
              }

              .tips {
                width: 100%;
                text-indent: 204px;
                font-weight: 400;
                font-size: 14px;
                line-height: 20px;
                color: #a1a6ae;
              }
            }
          }
        }
      }

      // ::v-deep(.content-right) {
      .el-button--medium {
        font-size: 13px;
        padding: 8px 20px !important;
        height: 28px;
      }

      // }
    }
  }
}
</style>
