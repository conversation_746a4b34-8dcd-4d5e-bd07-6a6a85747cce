import { Component, Vue } from "vue-property-decorator";
import SubHeader from "cmp/subheader/SubHeader.vue";
import SalesCardsReport from "./salescards/SalesCardsReport";
import ComsumeReport from "./consume/ComsumeReport";
import RefundReport from "./refund/RefundReport";
import DailyReport from "./dailyReport/DailyReport";
import I18nPage from "common/I18nDecorator";
import BreadCrume from "cmp/bread-crumb/BreadCrume.vue";
import GiftCardFilter from "model/prepay/report/card/GiftCardFilter";
import GiftCardReportApi from "http/prepay/report/card/GiftCardReportApi";
// import PointsReportExportConfirm
//     from "pages/deposit/prepaycard/cmp/export/exchangeablecard/PointsReportExportConfirm.vue";
import GiftCardExportConfirm from "pages/deposit/prepaycard/giftcardreport/cmp/GiftCardExportConfirm.vue";
import DownloadCenterDialog from "pages/main/dowmload_center_dialog/DownloadCenterDialog";
import RSChannelManagementFilter from "model/common/RSChannelManagementFilter";
import ChannelManagementApi from "http/channelmanagement/ChannelManagementApi";
import { SessionStorage } from "mgr/BrowserMgr";
import OffLineGiftCardReportApi from "http/prepay/report/card/OffLineGiftCardReportApi";
import GiftCardDailyReportFilter from "model/prepay/report/card/GiftCardDailyReportFilter";
import GiftReportSum from "../cmp/giftReportSum/GiftReportSum";
import RefundCardReport from "pages/deposit/prepaycard/giftcardreport/refundcard/RefundCardReport";

@Component({
	name: "GiftCardReportReport",
	components: {
		SubHeader,
		SalesCardsReport,
		ComsumeReport,
		RefundReport,
		RefundCardReport,
		BreadCrume,
		// PointsReportExportConfirm,
		GiftCardExportConfirm,
		DownloadCenterDialog,
		DailyReport,
		GiftReportSum,
	},
})
@I18nPage({
	prefix: ["/储值/预付卡/电子礼品卡报表", "/公用/提示", "/公用/按钮"],
	auto: false,
})
export default class GiftCardReportReport extends Vue {
	activeName: string = "售卡流水";
	panelArray: any = [];
	exportDialogShow: boolean = false;
	fileDialogVisible = false;
	showTip = false;
	created() {
		this.panelArray = [
			{
				name: this.formatI18n("/公用/菜单/电子礼品卡报表"),
				url: "",
			},
		];
		this.getChannels();
		// PHX-14618需求：会员资料 -> 会员资产 -> 可用预付卡 -> 明细按钮
		if (this.$route.query.from == "member-asset") {
			this.activeName = "消费流水";
		}
	}

	private getChannels() {
		let param: RSChannelManagementFilter = new RSChannelManagementFilter();
		ChannelManagementApi.query(param)
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					SessionStorage.setItem("channels", resp.data);
				}
			})
			.catch((error: any) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}

	doBatchExport() {
		this.exportDialogShow = true;
	}

	doDownloadDialogClose() {
		this.showTip = false;
		this.fileDialogVisible = false;
	}

	get getExportDialogShow() {
		return this.exportDialogShow;
	}

	doExportDialogClose() {
		this.exportDialogShow = false;
	}

	doExportSubmit(type: string, filter: GiftCardFilter | GiftCardDailyReportFilter) {
		if (!type || !filter) {
			return;
		}
		if (type === "SALES_HST") {
			GiftCardReportApi.exportSalesHst(filter as GiftCardFilter)
				.then((resp: any) => {
					if (resp && resp.code === 2000) {
						this.exportAfter();
					}
				})
				.catch((error: any) => {
					if (error && error.message) {
						this.$message.error(error.message);
					}
				});
		} else if (type === "CONSUME_HST") {
			GiftCardReportApi.exportConsumeHst(filter as GiftCardFilter)
				.then((resp: any) => {
					if (resp && resp.code === 2000) {
						this.exportAfter();
					}
				})
				.catch((error: any) => {
					if (error && error.message) {
						this.$message.error(error.message);
					}
				});
		} else if (type === "REFUND_HST") {
			GiftCardReportApi.exportRefundHst(filter as GiftCardFilter)
				.then((resp: any) => {
					if (resp && resp.code === 2000) {
						this.exportAfter();
					}
				})
				.catch((error: any) => {
					if (error && error.message) {
						this.$message.error(error.message);
					}
				});
		} else if (type === "REFUND_CARD_HST") {
			GiftCardReportApi.exportRefundCardHst(filter as GiftCardFilter)
				.then((resp: any) => {
					if (resp && resp.code === 2000) {
						this.exportAfter();
					}
				})
				.catch((error: any) => {
					if (error && error.message) {
						this.$message.error(error.message);
					}
				});
		} else if (type === "DAY_HST") {
			GiftCardReportApi.exportCardDailyReport(filter as GiftCardDailyReportFilter)
				.then((resp: any) => {
					if (resp && resp.code === 2000) {
						this.exportAfter();
					}
				})
				.catch((error: any) => {
					if (error && error.message) {
						this.$message.error(error.message);
					}
				});
		}
	}

	exportAfter() {
		this.showTip = true;
		this.fileDialogVisible = true;
	}
}
