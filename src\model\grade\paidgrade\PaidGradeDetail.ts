import BasicBenefit from 'model/grade/BasicBenefit'
import Grade from 'model/grade/Grade'
import GradeFeeSet from 'model/grade/paidgrade/GradeFeeSet'
import MonthBenefit from 'model/grade/MonthBenefit'

export default class PaidGradeDetail {
    // 等级基础资料
    grade: Nullable<Grade> = null
    // 基础权益信息展示使用
    basicBenefit: Nullable<BasicBenefit> = null
    //   等级月礼展示使用
    monthBenefit: Nullable<MonthBenefit> = null
    // 付费等级升级规则套餐
    gradeFeeSets: GradeFeeSet[] = []
}