<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:03
 * @LastEditTime: 2024-01-12 09:43:25
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\prepaycard\cmp\cardtplitem\CardTplItem.vue
 * 记得注释
-->
<template>
  <div class="cardtpl">
    <div v-if="!readonly">
      <el-input style="width:298px" @focus="showDialog" suffix-icon="el-icon-menu" v-model="detail.name" />&nbsp;
      <el-button type="text" style="margin-left:0" @click="refresh(number)">刷新
      </el-button>
      <template v-if="canCreateCardTemplate">
        <span style="color: rgba(51, 51, 51, 0.24)">&nbsp;|&nbsp;</span>
        <el-button type="text" style="margin-left:0" @click="newTpl">新建卡模板</el-button>
      </template>
      <template v-if="canManageCardTemplate">
        <span style="color: rgba(51, 51, 51, 0.24)">&nbsp;|&nbsp;</span>
        <el-button type="text" style="margin-left:0" @click="tplList">管理卡模板</el-button>
      </template>
    </div>
    <div v-if="detail.name"><el-button type="text" @click="gotoTplDtl" style="font-size: 15px" no-i18n>{{detail.name}}</el-button></div>
    <div class="detail" v-if="detail.name || readonly">
      <el-row>
        <el-col :span="4">卡样：</el-col>
        <el-col :span="18" v-if="!detail.cardPictureUrls || detail.cardPictureUrls.length  === 0">
          未设置卡样
        </el-col>
        <el-col :span="18" v-if="detail.cardPictureUrls && detail.cardPictureUrls.length  > 0">
          <CardPicList :picList="detail.cardPictureUrls" :readonly="true" />
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="4">有效期：</el-col>
        <el-col :span="18">
          <span class="text">
            <span no-i18n>{{validatyInfo}}</span>
          </span>
        </el-col>
      </el-row>
      <template v-if="isShowFitGoods">
        <el-row v-if="detail.cardTemplateType == 'COUNTING_CARD' && detail.countingCardGoodsGroups[0]">
          <el-col :span="4" class="label">{{ i18n('适用商品：') }}</el-col>
          <el-col :span="18">
            <div>
              <span>
                {{formatI18n("/资料/门店", "共") + detail.countingCardGoodsGroups[0].exchangeGoods.length + formatI18n("/资料/员工", "项")}}
              </span>
              <span style="margin: 0 10px">
                {{ detail.countingCardGoodsGroups[0].exchangeGoods.length + formatI18n("/公用/券模板/提货券/用券商品/选") + detail.countingCardGoodsGroups[0].exchangeQty}}
              </span>
              <span v-if="detail.countingCardGoodsGroups[0].enableExchangeSameGood">{{i18n('/公用/券模板/可重复选')}}</span>
              <span v-else>{{i18n('/公用/券模板/不可重复选')}}</span>
            </div>
            <el-table :data="detail.countingCardGoodsGroups[0].exchangeGoods" style="width: 800px">
              <el-table-column :label="formatI18n('/营销/积分活动/积分活动/单品满数量加送积分活动/编辑页面/积分加送规则', '商品')" fixed prop="goods.name">
                <template slot-scope="scope">
                  <div v-if="scope.row.goods" :title="scope.row.goods.name">{{ scope.row.goods.name }}</div>
                </template>
              </el-table-column>
              <el-table-column :label="formatI18n('/公用/券模板/提货券/用券商品', '售价(元)')" fixed prop="price">
                <template slot-scope="scope">
                  {{ scope.row.price | fmt }}
                </template>
              </el-table-column>
              <el-table-column :label="formatI18n('/公用/券模板/提货券/用券商品', '商品数量')" fixed prop="qty">
                <template slot-scope="scope">
                  {{ scope.row.qty }}&nbsp;{{ scope.row.isDisp ? formatI18n("/公用/券模板/单品折扣券/用券门槛/千克") : formatI18n("/公用/券模板/单品折扣券/用券门槛/件") }}
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
        <el-row v-else-if="detail.cardTemplateType !== 'IMPREST_CARD'">
          <el-col :span="4">适用商品：</el-col>
          <el-col :span="18">
            <GoodsScopeDtl no-i18n :goods="detail.goods" />
          </el-col>
        </el-row>
      </template>
      <el-row v-if="detail.cardTemplateType !== 'IMPREST_CARD'">
        <el-col :span="4">适用门店：</el-col>
        <el-col :span="18">
          <ActiveStoreDtl style="width: 60%;min-width: 800px;" no-i18n :data="detail.stores"></ActiveStoreDtl>
        </el-col>
      </el-row>
    </div>
    <!--选择卡模板弹框-->
    <!-- <CardTplDialog no-i18n :data="tpl" :type="type" :selectNo="selectNo" :cardMedium="cardMedium" :dialogShow="dialogShow" @summit="doSummit" @dialogClose="doDialogClose" ref="cardTplDialog"> -->
    <CardTplDialog no-i18n :canSelectCenter="canSelectCenter" :maxSel="maxSel" :isSelectMultiple="isSelectMultiple" :data="tpl" :type="type"
      :selectNo="isSelectMultiple ? selectNo : [number]" :cardMedium="cardMedium" :dialogShow="dialogShow" @summit="doSummit"
      @dialogClose="doDialogClose" ref="cardTplDialog">
    </CardTplDialog>
  </div>
</template>

<script lang="ts" src="./CardTplItem.ts">
</script>

<style lang="scss" scoped>
.cardtpl {
  .detail {
    margin-top: 10px;
    font-size: 14px;
    color: #606266;
    line-height: 50px;
    .text {
      line-height: 35px;
    }
    .faceAmount {
      background-color: #f9f9f9;
      padding: 0 30px;
      float: left;
      margin-right: 15px;
      margin-top: 8px;
      border: 1px solid #e8e8e8;
      line-height: 35px;
      height: 35px;
    }
    .goods {
      border: 1px solid #f0f0f0;
      width: 100%;
      header {
        height: 35px;
      }
    }
  }
}
</style>
