import BenefitCardTemplateGainFreeRule from 'model/equityCard/BenefitCardTemplateGainFreeRule'
import BenefitCardTemplateGainPaidRule from 'model/equityCard/BenefitCardTemplateGainPaidRule'

// 权益卡模版领卡规则
export default class BenefitCardTemplateGainRule {
  // 付费规则，用于付费会员卡
  paid: Nullable<BenefitCardTemplateGainPaidRule> = null
  // 免费规则，用于免费权益会员卡
  free: Nullable<BenefitCardTemplateGainFreeRule> = null
}