import BaseCouponActivity from 'model/coupon/activity/BaseCouponActivity'
import IdName from "model/common/IdName";
import GiftInfo from "model/common/GiftInfo";
import {DayOfWeek} from "model/autoTag/DayOfWeek";
import PushGroup from "model/precisionmarketing/pushplan/PushGroup";


export default class GroupTargetedGiftsActivity extends BaseCouponActivity {
  // 发生组织
  occurredOrg: Nullable<IdName> = null
  // 券礼包
  giftInfo: Nullable<GiftInfo> = null
  // 发放周期
  issueCycle: Nullable<string> = null
  // 每周发放日期
  dayOfWeek: Nullable<DayOfWeek> = null
  // 每月发放日期
  dayOfMonth: Nullable<number> = null
  // 是否是每月最后一天
  lastOfMonth: Nullable<boolean> = null
  // 发券时间-时
  hourIssueTime: Nullable<string> = null
  // 发券时间-分钟
  minutesIssueTime: Nullable<string> = null
  // 适用客群
  rule: Nullable<PushGroup> = null
}