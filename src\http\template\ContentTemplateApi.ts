import ApiClient from 'http/ApiClient'
import ContentTemplate from 'model/template/ContentTemplate'
import ContentTemplateFilter from 'model/template/ContentTemplateFilter'
import CopyRequest from 'model/template/CopyRequest'
import CreateRequest from 'model/template/CreateRequest'
import PublishRequest from 'model/template/PublishRequest'
import RemoveRequest from 'model/template/RemoveRequest'
import Response from 'model/default/Response'
import UpdateRequest from 'model/template/UpdateRequest'
import PopularizeResponse from 'model/template/PopularizeResponse'
import ContentTemplateExtInfoUpdate from 'model/template/ContentTemplateExtInfoUpdate'
import CmsConfig, { CmsConfigChannel } from 'model/template/CmsConfig'

export default class ContentTemplateApi {
  /**
   * 复制内容模板
   * 
   */
  static copy(body: CopyRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/cms/content/template/copy`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存内容模板
   * 
   */
  static create(body: CreateRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/cms/content/template/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据id获取内容模板
   * 
   */
  static get(id: string): Promise<Response<ContentTemplate>> {
    return ApiClient.server().get(`/v1/cms/content/template/${id}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 发布内容模板
   * 
   */
  static publish(body: PublishRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/cms/content/template/publish`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询内容模板
   * 
   */
  static query(body: ContentTemplateFilter): Promise<Response<ContentTemplate[]>> {
    return ApiClient.server().post(`/v1/cms/content/template/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据id删除内容模板
   * 
   */
  static remove(body: RemoveRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/cms/content/template/remove`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 编辑内容模板
   * 
   */
  static update(body: UpdateRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/cms/content/template/update`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 内容模板推广
   * 
   */
  static popularize(id: string): Promise<Response<PopularizeResponse>> {
    return ApiClient.server().get(`/v1/cms/content/template/popularize/${id}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 编辑活动模板扩展信息
  * 
  */
  static updateExtInfo(body: ContentTemplateExtInfoUpdate): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/cms/content/template/updateExtInfo`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 获取系统链接推广信息
  * 
  */
  static sysPopularize(pagePath: string): Promise<Response<PopularizeResponse>> {
    return ApiClient.server().get(`/v1/cms/content/template/system/popularize?pagePath=${pagePath}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
 * 获取自定义推广信息
 * 
 */
  static customPopularize(pagePath: string, channel: CmsConfigChannel): Promise<Response<PopularizeResponse>> {
    return ApiClient.server().post(`/v1/cms/content/template/custom/popularize`, {
      pagePath,
      channel
    }, {
    }).then((res) => {
      return res.data
    })
  }

  static getConfig(): Promise<Response<CmsConfig>> {
    return ApiClient.server().post(`/v1/systemConfig/cmsConfig/get`, {}, {
    }).then((res) => {
      return res.data
    })
  }
}
