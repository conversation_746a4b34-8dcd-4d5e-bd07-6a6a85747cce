export default class FileRecord {
  // uuid
  id: Nullable<string> = null
  // 创建时间
  created: Nullable<Date> = null
  // 上传、下载类型
  type: Nullable<string> = null
  // 模块类型
  module: Nullable<string> = null
  // 状态
  state: Nullable<string> = null
  // 处理结果
  processResult: Nullable<string> = null
  // 进度
  process: Nullable<number> = null
  // 下载地址
  downUrl: Nullable<string> = null
  // 下载OSS Key
  downOssKey: Nullable<string> = null
  // 上传url
  upUrl: Nullable<string> = null
  // 备注
  remark: Nullable<string> = null
}