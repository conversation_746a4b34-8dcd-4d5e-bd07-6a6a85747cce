<!--
 * @Author: 黎钰龙
 * @Date: 2023-07-07 15:42:16
 * @LastEditTime: 2024-04-24 17:02:08
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\coupontenplate\cmp\CouponTemplateLabel.vue
 * 记得注释
-->
<template>
    <div class="coupon-template-label">
        <el-form-item :label="formatI18n('/权益/券/券模板', '标签')">
            <el-select size="small" v-model="templateTag" filterable multiple :placeholder="i18n('输入匹配/点击下拉选择')" style="width: 450px" @change="doChange">
					<el-option
					v-for="item in labelList"
					:key="item.uuid"
					:label="item.value"
					:value="item.uuid">
					</el-option>
				</el-select>
        </el-form-item>
    </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import CouponTemplateTag from 'model/coupon/CouponTemplateTag'
import CouponTemplateTagFilter from 'model/coupon/CouponTemplateTagFilter'
import CouponTemplateTagApi from 'http/coupon/template/CouponTemplateTagApi'
import CouponTemplateTagRelation from 'model/coupon/CouponTemplateTagRelation'
import I18nPage from 'common/I18nDecorator'

@Component({
    components: {}
})
@I18nPage({
  prefix: [
    "/权益/券/券模板",
    "/公用/券模板"
  ],
  auto: true
})
export default class CouponTemplateLabel extends Vue {
    @Prop({
        default: []
    })
    value: CouponTemplateTagRelation[]
    @Prop({
        default: ''
    })
    templateId: string
    labelList: CouponTemplateTag[] = []
    templateTag: any = []

    @Watch('value', {
        deep: true,
        immediate: true
    })
    onValueChange(value: any) {
        if (value) {
            this.templateTag = value.reduce((acc: string[], cur: CouponTemplateTagRelation) => {
                acc.push(cur.tagUuid!)
                return acc
            }, [])
        }
    }

    created() {
        this.queryLabel()
    }
    async queryLabel() {
        const params = new CouponTemplateTagFilter()
        try {
          const resData = await CouponTemplateTagApi.query(params)
          this.labelList = resData.data || []
        } catch (error) {
          this.labelList = []
        }
    }
    doChange(e: any) {
        this.$emit('input', this.doLabelParams())
        this.$emit('change')
    }
    doLabelParams() {
        return this.templateTag.reduce((acc: CouponTemplateTagRelation[], cur: string) => {
            const labelInfo = this.labelList.find(item => item.uuid == cur)
            if (labelInfo) {
                acc.push({
                    tagUuid: labelInfo.uuid,
                    tagValue: labelInfo.value,
                    templateNumber: this.templateId
                })
            }
            return acc
        }, [])
    }
}
</script>

<style lang="scss" scoped>
.coupon-template-label {
    width: 100%;
}
</style>