import { Component, Vue } from 'vue-property-decorator'
import ListWrapper from "cmp/list/ListWrapper";
import SubHeader from 'cmp/subheader/SubHeader.vue'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import FloatBlock from 'cmp/floatblock/FloatBlock.vue'
import ActivityStateCountResult from 'model/common/ActivityStateCountResult';
import DateUtil from 'util/DateUtil';
import CardBalancePromotionActivity from "model/payment/card/CardBalancePromotionActivity";
import CardBalancePromotionActivityFilter from "model/payment/card/CardBalancePromotionActivityFilter";
import ActivityTopicApi from "http/v2/controller/points/topic/ActivityTopicApi";
import ActivityTopic from "model/v2/controller/points/topic/ActivityTopic";
import ActivityState from "cmp/activitystate/ActivityState";
import EditType from "common/EditType";
import I18nPage from "common/I18nDecorator";
import PrepayCardChargeGiftPermission from "./PrepayCardChargeGiftPermission";
import PrepayCardTplPermission from "pages/deposit/prepaycard/prepaycardtpl/PrepayCardTplPermission";
import RoutePermissionMgr from 'mgr/RoutePermissionMgr';
import CardDepositActivityApi from 'http/cardDepositActivity/CardDepositActivityApi';
import CardDepositActivityFilter from 'model/cardDepositActivity/CardDepositActivityFilter';
import CardDepositActivityQueryResult from 'model/cardDepositActivity/CardDepositActivityQueryResult';

@Component({
  name: "PrepayCardChargeGiftList",
  components: {
    ListWrapper,
    SubHeader,
    BreadCrume,
    FloatBlock,
    ActivityState,
  },
})
@I18nPage({
  prefix: [
    "/储值/预付卡/预付卡支付活动/列表页面",
    "/公用/活动/状态",
    "/公用/活动/活动信息",
    "/公用/活动/提示信息",
    "/公用/按钮",
    "/公用/提示",
    "/公用/菜单",
    "/储值/预付卡/预付卡充值有礼"
  ],
})
export default class PrepayCardChargeGiftList extends Vue {
  i18n: (str: string, params?: string[]) => string;
  query: CardDepositActivityFilter = new CardDepositActivityFilter();
  queryData: CardDepositActivityQueryResult[] = [];
  countResult: ActivityStateCountResult = new ActivityStateCountResult();
  tabName: string = "ALL";
  $refs: any;
  checkedAll: boolean = false;
  selected: CardBalancePromotionActivity[] = [];
  permission = new PrepayCardChargeGiftPermission();
  prepayCardTplPermission = new PrepayCardTplPermission();

  panelArray: any;

  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10,
  };

  themes: ActivityTopic[] = [];
  activityOptWidth: number = 150;

  created() {
    this.panelArray = [
      {
        name: this.i18n("预付卡充值有礼"),
        url: "",
      },

    ];
    let lang = sessionStorage.getItem("locale");
    if (lang === "zh_CN") {
      this.activityOptWidth = 200;
    } else {
      this.activityOptWidth = 240;
    }
    this.getList();
    this.getTheme();
  }

  doSearch() {
    this.page.currentPage = 1;
    this.getList();
  }

  doReset() {
    this.query = new CardDepositActivityFilter();
    this.tabName = "ALL";
    this.page.currentPage = 1;
    this.getList();
  }

  /**
   * 查询
   */
  onSearch() {
    this.page.currentPage = 1;
    this.getList();
  }

  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val;
    this.getList();
  }

  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val;
    this.getList();
  }

  /**
   * 表格排序条件
   */
  onSortChange({ column, prop, order }: any) {
    // todo
  }

  private getList() {
    this.query.page = this.page.currentPage - 1;
    this.query.pageSize = this.page.size;
    CardDepositActivityApi.query(this.query)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.countResult = resp.data.countResult;
          this.queryData = resp.data.result;
          this.page.total = resp.data.total;
        }
      })
      .catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
  }

  private add() {
    this.$router.push({ name: "prepay-card-charge-gift-edit" });
  }

  private gotoDtl(row: any) {
    this.$router.push({ name: "prepay-card-charge-gift-dtl", query: { activityId: row.body.activityId } });
  }

  private del(activityId: string) {
    this.$alert(this.i18n("确认要删除吗？"), this.i18n("提示"), {
      confirmButtonText: this.i18n("确定"),
      cancelButtonText: this.i18n("取消"),
      type: "warning",
      callback: (action) => {
        if (action === "confirm") {
          CardDepositActivityApi.remove(activityId)
            .then((resp: any) => {
              if (resp && resp.code === 2000) {
                this.$message.success(this.i18n("删除成功"));
                this.getList();
              }
            })
            .catch((error) => {
              if (error && error.message) {
                this.$message.error(error.message);
              }
            });
        }
      },
    });
  }

  private delBatch() {
    if (this.selectedActivityIdList.length === 0) {
      this.$message.error(this.i18n("请先勾选要删除的记录"));
      return;
    }
    this.$alert(this.i18n("确认要删除吗？"), this.i18n("提示"), {
      confirmButtonText: this.i18n("确定"),
      cancelButtonText: this.i18n("取消"),
      type: "warning",
      callback: (action) => {
        if (action === "confirm") {
          CardDepositActivityApi.batchRemove(this.selectedActivityIdList as any)
            .then((resp: any) => {
              if (resp && resp.code === 2000) {
                this.$message.success(resp.data);
                this.getList();
              }
            })
            .catch((error) => {
              if (error && error.message) {
                this.$message.error(error.message);
              }
            });
        }
      },
    });
  }

  private audit(activityId: string) {
    this.$alert(this.i18n("确认要审核吗？"), this.i18n("提示"), {
      confirmButtonText: this.i18n("确定"),
      cancelButtonText: this.i18n("取消"),
      type: "warning",
      callback: (action) => {
        if (action === "confirm") {
          CardDepositActivityApi.audit(activityId)
            .then((resp: any) => {
              if (resp && resp.code === 2000) {
                this.$message.success(this.i18n("审核成功"));
                this.getList();
              } else {
                this.$message.error(resp.msg)
              }
            })
            .catch((error) => {
              if (error && error.message) {
                this.$message.error(error.message);
              }
            });
        }
      },
    });
  }

  private auditBatch() {
    if (this.selectedActivityIdList.length === 0) {
      this.$message.error(this.i18n("请先勾选要审核的记录"));
      return;
    }
    this.$alert(this.i18n("确认要审核吗？"), this.i18n("提示"), {
      confirmButtonText: this.i18n("确定"),
      cancelButtonText: this.i18n("取消"),
      type: "warning",
      callback: (action) => {
        if (action === "confirm") {
          CardDepositActivityApi.batchAudit(this.selectedActivityIdList as any)
            .then((resp: any) => {
              if (resp && resp.code === 2000) {
                this.$message.success(resp.data);
                this.getList();
              }
            })
            .catch((error) => {
              if (error && error.message) {
                this.$message.error(error.message);
              }
            });
        }
      },
    });
  }

  private stop(activityId: string) {
    this.$alert(this.i18n("确认要停止吗？"), this.i18n("提示"), {
      confirmButtonText: this.i18n("确定"),
      cancelButtonText: this.i18n("取消"),
      type: "warning",
      callback: (action) => {
        if (action === "confirm") {
          CardDepositActivityApi.stop(activityId)
            .then((resp: any) => {
              if (resp && resp.code === 2000) {
                this.$message.success(this.i18n("停止成功"));
                this.getList();
              }
            })
            .catch((error) => {
              if (error && error.message) {
                this.$message.error(error.message);
              }
            });
        }
      },
    });
  }

  private stopBatch() {
    if (this.selectedActivityIdList.length === 0) {
      this.$message.error(this.i18n("请先勾选要停止的记录"));
      return;
    }
    this.$alert(this.i18n("确认要停止吗？"), this.i18n("提示"), {
      confirmButtonText: this.i18n("确定"),
      cancelButtonText: this.i18n("取消"),
      type: "warning",
      callback: (action) => {
        if (action === "confirm") {
          CardDepositActivityApi.batchStop(this.selectedActivityIdList as any)
            .then((resp: any) => {
              if (resp && resp.code === 2000) {
                this.$message.success(resp.data);
                this.getList();
              }
            })
            .catch((error) => {
              if (error && error.message) {
                this.$message.error(error.message);
              }
            });
        }
      },
    });
  }

  private copy(activityId: string, type: string) {
    this.$router.push({
      name: "prepay-card-charge-gift-edit",
      query: { activityId: activityId, editType: EditType.COPY },
    });
  }

  private edit(activityId: string, type: string) {
    this.$router.push({
      name: "prepay-card-charge-gift-edit",
      query: { activityId: activityId, editType: EditType.EDIT },
    });
  }

  private checkedAllRow() {
    if (this.checkedAll) {
      for (let row of this.queryData) {
        this.$refs.table.toggleRowSelection(row, true);
      }
    } else {
      this.$refs.table.clearSelection();
    }
  }

  private handleSelectionChange(val: any) {
    this.selected = val;
  }

  get selectedActivityIdList() {
    return this.selected.map((e) => (e.body ? e.body.activityId : null));
  }

  get allTab() {
    return `${this.i18n("全部")}(${this.countResult.sum})`;
  }

  get initialTab() {
    return `${this.i18n("未审核")}(${this.countResult.initail})`;
  }

  get unstartTab() {
    return `${this.i18n("未开始")}(${this.countResult.unstart})`;
  }

  get processingTab() {
    return `${this.i18n("进行中")}(${this.countResult.processing})`;
  }

  get stopedTab() {
    return `${this.i18n("已结束")}(${this.countResult.stoped})`;
  }

  private activityTime(row: any) {
    return `${DateUtil.format(row.body.beginDate, "yyyy-MM-dd")}${this.i18n("至")}${DateUtil.format(row.body.endDate, "yyyy-MM-dd")}`;
  }

  private handleTabClick(tab: any, event: any) {
    this.query.stateEquals = tab.name === "ALL" ? null : tab.name;
    this.doSearch();
  }

  private getTheme() {
    ActivityTopicApi.listTopic().then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.themes = resp.data;
      }
    });
  }

  private gotoCardTplDtl(num: string) {
    RoutePermissionMgr.openBlank({ name: "prepay-card-tpl-dtl", query: { number: num } });
  }
}
