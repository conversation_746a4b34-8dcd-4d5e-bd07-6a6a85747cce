/*
 * @Author: 黎钰龙
 * @Date: 2023-12-01 13:44:04
 * @LastEditTime: 2023-12-12 14:41:48
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\prepaycard\countCardReport\exchangeGoodsDialog\ExchangeGoodsDialog.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import CountingCardReportApi from 'http/prepay/card/CountingCardReportApi';
import PrepayCardExchangeGoods from 'model/prepay/report/card/PrepayCardExchangeGoods';
import PrepayCardExchangeGoodsFilter from 'model/prepay/report/card/PrepayCardExchangeGoodsFilter';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
@Component({
  name: 'ExchangeGoodsDialog'
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/储值/预付卡/次卡报表',
    '/公用/券模板/微盟适用商品',
    '/会员/洞察/公共/周期消费属性',
    '/营销/积分活动/积分报表/积分抵现流水/查询条件',
    '/会员/等级/付费等级流水报表/列表页面'
  ],
  auto: true
})
export default class ExchangeGoodsDialog extends Vue {
  listData: PrepayCardExchangeGoods[] = []
  exchangeDialogShow: boolean = false
  transNo: Nullable<string> = null
  tableLoading:boolean = false
  page:any =  {
    currentPage: 1,
    size: 10,
    total: 0
  }

  open(transNo:string) {
    this.listData = []
    this.exchangeDialogShow = true
    this.transNo = transNo
    this.getList()
  }

  close() {
    this.exchangeDialogShow = false
  }

  getList() {
    this.tableLoading = true
    const params = new PrepayCardExchangeGoodsFilter()
    params.transIdIdEquals = this.transNo
    params.page = this.page.currentPage - 1
    params.pageSize = this.page.size
    CountingCardReportApi.queryExchangeGoods(params).then((res)=>{
      if(res.code === 2000) {
        this.listData = res.data || []
        this.page.total = res.total
      } else {
        this.$message(res.msg || this.i18n('查询兑换商品失败'))
      }
    }).catch((err)=>{
      this.$message(err.message || this.i18n('查询兑换商品失败'))
    }).finally(()=>{
      this.tableLoading = false
    })
  }

  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.getList()
  }
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.getList()
  }
};