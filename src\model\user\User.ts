/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2025-05-07 11:09:37
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\user\User.ts
 * 记得注释
 */
export default class User {
    // 用户uuid
    uuid: Nullable<string> = null
    // 名称
    name: Nullable<string> = null
    // 是否超级管理员
    admin: Nullable<boolean> = null
    // 状态
    state: Nullable<string> = null
    // 帐户代码
    account: Nullable<string> = null
    // 手机号
    mobile: Nullable<string> = null
    // 密码
    password: Nullable<string> = null
    // 是否开启会员敏感信息脱敏
    enableDesensitization: Nullable<boolean> = null
    // 员工编码
    code: Nullable<string> = null
}