import IdName from 'model/common/IdName'
import MemberIdent from 'model/member_v2/common/MemberIdent'
import Channel from "model/common/Channel";

export default class Member extends MemberIdent {
  // 姓名
  name: Nullable<string> = null
  // 等级代码
  gradeCode: Nullable<string> = null
  // 等级名称
  gradeName: Nullable<string> = null
  // 状态 取值：Using-使用中；Blocked-已冻结；Unactivated-未激活；Canceled-已注销
  state: Nullable<string> = null
  // 归属门店
  ownStore: Nullable<IdName> = null
  // 注册日期
  registerTime: Nullable<Date> = null
  // 激活日期
  activateTime: Nullable<Date> = null
  // 创建日期
  created: Nullable<Date> = null
  // 注册渠道
  registerChannel: Nullable<Channel> = null
  // 招募方式
  registerScene: Nullable<string> = null
  // 性别
  gender: Nullable<string> = null
  // 生日
  birthday: Nullable<Date> = null
}