import ApiClient from 'http/ApiClient'
import PointsAccountReportFilter from "model/report/query/points/account/PointsAccountReportFilter";
import PointsAccountReportResult from "model/report/query/points/account/PointsAccountReportResult";
import Response from 'model/common/Response'
import PointsExchangeReportResult from "model/report/query/points/exchange/PointsExchangeReportResult";
import PointsExchangeReportFilter from "model/report/query/points/exchange/PointsExchangeReportFilter";
import PointsHstReportFilter from "model/report/query/points/hst/PointsHstReportFilter";
import PointsHstReportResult from "model/report/query/points/hst/PointsHstReportResult";
import PointsHstReportSum from "model/report/query/points/hst/PointsHstReportSum";
import PointsObtainReportFilter from "model/report/query/points/obtain/PointsObtainReportFilter";
import PointsObtainReportResult from "model/report/query/points/obtain/PointsObtainReportResult";
import PointsObtainReportSum from "model/report/query/points/obtain/PointsObtainReportSum";
import PointsStoreReportFilter from "model/report/query/points/store/PointsStoreReportFilter";
import PointsStoreReportResult from "model/report/query/points/store/PointsStoreReportResult";
import PointsStoreReportSum from "model/report/query/points/store/PointsStoreReportSum";
import PointsUseReportFilter from "model/report/query/points/use/PointsUseReportFilter";
import PointsUseReportResult from "model/report/query/points/use/PointsUseReportResult";
import PointsUseReportSum from "model/report/query/points/use/PointsUseReportSum";
import PointsExportFilter from "model/report/query/points/account/PointsExportFilter";
import PointsAccountSumResult from "model/report/query/points/account/PointsAccountSumResult";


export default class PointsReportApi {

  /**
   * 积分统计
   * 积分统计。
   *
   */
  static sumPoints(): Promise<Response<PointsAccountSumResult>> {
    return ApiClient.server().get(`/v1/points/report/sumPoints`,  {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询积分账户报表
   * 查询积分账户报表。
   *
   */
  static queryAccount(body: PointsAccountReportFilter): Promise<Response<PointsAccountReportResult>> {
    return ApiClient.server().post(`/v1/points/report/queryAccount`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询积分兑换报表
   * 查询积分兑换报表。
   *
   */
  static queryExchange(body: PointsExchangeReportFilter): Promise<Response<PointsExchangeReportResult>> {
    return ApiClient.server().post(`/v1/points/report/queryExchange`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询积分流水报表
   * 查询积分流水报表。
   *
   */
  static queryHst(body: PointsHstReportFilter): Promise<Response<PointsHstReportResult>> {
    return ApiClient.server().post(`/v1/points/report/queryHst`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询积分流水报表统计
   * 查询积分流水报表统计。
   *
   */
  static queryHstSum(body: PointsHstReportFilter): Promise<Response<PointsHstReportSum>> {
    return ApiClient.server().post(`/v1/points/report/queryHstSum`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询得积分流水报表
   * 查询得积分流水报表。
   *
   */
  static queryObtain(body: PointsObtainReportFilter): Promise<Response<PointsObtainReportResult>> {
    return ApiClient.server().post(`/v1/points/report/queryObtain`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询得积分流水报表统计
   * 查询得积分流水报表统计。
   *
   */
  static queryObtainSum(body: PointsObtainReportFilter): Promise<Response<PointsObtainReportSum>> {
    return ApiClient.server().post(`/v1/points/report/queryObtainSum`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询门店积分流水报表
   * 查询门店积分流水报表。
   *
   */
  static queryStore(body: PointsStoreReportFilter): Promise<Response<PointsStoreReportResult>> {
    return ApiClient.server().post(`/v1/points/report/queryStore`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询门店积分流水报表统计
   * 查询门店积分流水报表统计。
   *
   */
  static queryStoreSum(body: PointsStoreReportFilter): Promise<Response<PointsStoreReportSum>> {
    return ApiClient.server().post(`/v1/points/report/queryStoreSum`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询用积分流水报表
   * 查询得积分流水报表。
   *
   */
  static queryUse(body: PointsUseReportFilter): Promise<Response<PointsUseReportResult>> {
    return ApiClient.server().post(`/v1/points/report/queryUse`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询得积分流水报表统计
   * 查询得积分流水报表统计。
   *
   */
  static queryUseSum(body: PointsUseReportFilter): Promise<Response<PointsUseReportSum>> {
    return ApiClient.server().post(`/v1/points/report/queryUseSum`, body, {}).then((res) => {
      return res.data
    })
  }


  /**
   * 导出积分账户报表
   * 导出积分账户报表。
   *
   */
  static exportAccount(): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/points/report/exportAccount`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 导出积分兑换流水
   * 导出积分兑换流水。
   *
   */
  static exportAgg(body: PointsStoreReportFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/points/report/exportAgg`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 导出积分兑换流水
   * 导出积分兑换流水。
   *
   */
  static exportAggWithParams(body: PointsStoreReportFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/points/report/exportAggWithParams`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 导出积分兑换流水
   * 导出积分兑换流水。
   *
   */
  static exportExchange(body: PointsExchangeReportFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/points/report/exportExchange`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 导出得积分流水报表
   * 导出得积分流水报表。
   *
   */
  static exportObtain(body: PointsObtainReportFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/points/report/exportObtain`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 导出用积分流水报表
   * 导出用积分流水报表。
   *
   */
  static exportUse(body: PointsUseReportFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/points/report/exportUse`, body, {}).then((res) => {
      return res.data
    })
  }
}
