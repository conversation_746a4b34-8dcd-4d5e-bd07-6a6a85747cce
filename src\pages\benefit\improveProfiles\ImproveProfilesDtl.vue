<template>
  <div class="mini-program-gain-coupon-activity-dtl">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button key="2" v-if="permission.auditable && state === 'INITAIL'" type="primary" @click="audit">{{i18n('审核')}}</el-button>
        <el-button key="3" v-if="permission.editable" @click="copy(detail.body.activityId)">{{i18n('复制')}}</el-button>
        <el-button key="4" v-if="permission.editable && ['UNSTART', 'INITAIL', 'PROCESSING'].indexOf(state) > -1"
          @click="edit">{{i18n('修改')}}</el-button>
        <el-button key="5" v-if="permission.terminable && ['UNSTART', 'PROCESSING'].indexOf(state) > -1" @click="stop">{{i18n('终止')}}</el-button>
        <el-button key="6" v-if="permission.editable && state === 'INITAIL'" @click="doDelete">{{i18n('删除')}}</el-button>
      </template>
    </BreadCrume>
    <div style="overflow: auto;height: 95%;">
      <el-row v-loading="loading" class="current-page">
        <el-form label-width="170px" v-if="detail && detail.body">
          <div class="panel">
            <div class="content">
              <el-row style="white-space: nowrap">
                <el-col :span="1" style="min-width: 60px;">
                  <div
                    style="background-color: rgba(242, 242, 242, 1);border-radius: 37px;height: 50px;width: 50px;display: flex;align-items: center">
                    <img style="width: 25px;height: 25px;margin: auto" src="~assets/image/storevalue/back.png" />
                  </div>
                </el-col>
                <el-col :span="21">
                  <el-row class="banner">
                    <el-col :span="8">
                      <el-row class="secondary">
                        <span>{{i18n('活动号：')}} </span>{{detail.body.activityId}}
                      </el-row>
                      <div class="primary" :title="detail.body.name">
                        {{detail.body.name}}
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <el-row class="secondary">
                        {{i18n('状态')}}
                      </el-row>
                      <div class="primary">
                        <ActivityState :state="detail.body.state" />
                      </div>
                    </el-col>
                  </el-row>
                  <hr />
                  <el-row>
                    <el-form-item :label="i18n('活动时间：')">
                      {{activityTime}}
                    </el-form-item>
                    <el-form-item :label="i18n('活动门店') + '：'">
                      <div style="line-height:36px" v-if="detail.body">
                        <ActiveStoreDtl style="width: 600px" :data="detail.body.stores">
                        </ActiveStoreDtl>
                      </div>
                    </el-form-item>
                    <el-form-item :label="formatI18n('/营销/积分活动/积分活动/商品满额加送积分/新建页面/活动信息/参与人群')+ '：'">
                      <template v-if="detail.rule">
                        <SelectGroupCmp ref="selectGroupCmp" :detail="true" v-model="detail.rule.participateMember"></SelectGroupCmp>
                      </template>
                      <template v-else>--</template>
                    </el-form-item>
                    <el-form-item :label="formatI18n('/公用/菜单/需完善资料')+'：'">
                      <div class="tag-form" v-if="detail.needImprove.length">
                        <el-tag v-for="(tag) of detail.needImprove" :key="tag" style="margin-right: 8px">
                          {{ i18n(transMemberInfoName(tag)) }}
                        </el-tag>
                        <el-tag v-for="(tag) of detail.needImproveOfCustom" :key="tag" style="margin-right: 8px">
                          {{ tag }}
                        </el-tag>
                      </div>
                    </el-form-item>
                  </el-row>
                </el-col>
              </el-row>
            </div>
          </div>
          <div class="split"></div>
          <div class="panel">
            <div class="header">
              {{i18n('礼包设置')}}
            </div>
            <div class="content upgrade-gift-edit" style="padding-left: 20px">
              <el-form label-width="70px">
                <el-form-item :label="i18n('赠礼')+'：'">
                  <div class="inner-checks" v-if="UpgradeLines.pointCheck && UpgradeLines.gift ">
                    {{ i18n("赠送积分") }}
                    {{ UpgradeLines.gift.points }}
                    {{ i18n('个') }}
                  </div>
                  <div class="inner-checks" v-if="UpgradeLines.couponCheck && UpgradeLines.gift && UpgradeLines.gift.couponItems">
                    <div style="display:flex;flex-direction:row;">
                      <span>
                        {{ i18n("赠送券") }}
                      </span>
                      <div style="flex:1;padding-left:12px;width:90%;">
                        <div v-for="(item, index) in UpgradeLines.gift.couponItems" :key="index">
                          <el-button type="text" @click="doEditStoreValueActive(item.coupons)">
                            {{ item.coupons.name }}
                          </el-button>
                          {{ item.qty }}
                          {{ formatI18n('/会员/等级/等级管理/点击付费等级tab页/未初始化状态下/点击立即开始付费等级初始化/输入所有必填项点击下一步/等级月礼下/表格/送券/点击添加券输入必填项点击保存/送', '张') }}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="inner-checks" v-if="UpgradeLines.growthValueCheck">
                    {{ i18n("赠送成长值") }}
                    {{ UpgradeLines.growthValue }}
                    {{i18n('个')}}
                  </div>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </el-form>
      </el-row>
    </div>
    <SelectStoreActiveDtlDialog :baseSettingFlag="false" :child="child" ref="couponTemplateDtlDialog" @dialogClose="doDialogClose"
      :dialogShow="couponTemplateDtlDialogFlag">
    </SelectStoreActiveDtlDialog>
  </div>
</template>

<script lang="ts" src="./ImproveProfilesDtl.ts">
</script>

<style lang="scss">
.mini-program-gain-coupon-activity-dtl {
  width: 100%;
  height: 100%;
  background-color: white;
  overflow: hidden;

  a {
    cursor: pointer;
  }

  .current-page {
    height: calc(100% - 20px) !important;
    overflow-y: scroll;

    .el-form-item__label {
      text-align: left;
    }

    .banner {
      .primary {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        font-weight: 500;
        font-style: normal;
        font-size: 20px;
        color: #515151;

        .state {
          width: 7px;
          height: 7px;
          border-radius: 10px;
          float: left;
          margin-top: 11px;
          margin-right: 11px;
        }
      }
      .secondary {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        color: rgba(51, 51, 51, 0.647058823529412);
      }
    }
    .panel {
      .header {
        font-weight: 500;
        padding: 20px 20px 0 30px;
        font-size: 18px;
        position: relative;
        &::before {
          content: "";
          display: block;
          width: 3px;
          height: 12px;
          position: absolute;
          top: 27px;
          left: 20px;
          background: #3189fd;
        }
      }
      .content {
        padding: 20px;
        .el-range-separator {
          padding: 0;
          line-height: 25px;
        }
        .el-form-item {
          margin-bottom: 0;
        }
      }
    }

    .split {
      height: 20px;
      background-color: #eeeff1;
    }
  }
  .active-store-dtl .content {
    border: none;
    padding: 0 !important;
  }
  .long-label .el-form-item__label {
    white-space: normal;
    float: left;
  }
}

// 礼包
.upgrade-gift-edit {
  background: #f5f7fa;
  margin-left: 30px;
  margin-top: 30px;
  padding: 0;
  width: 580px;
  //height: 100%;
  overflow: auto;
  .content {
    padding: 30px;
  }
  .width-78 {
    width: 78px;
  }
  .width-298 {
    width: 298px;
  }
  .el-textarea__inner {
    height: 100px;
  }
  .inner-content {
    background-color: rgba(249, 249, 249, 1);
    padding-left: 10px;
    margin: -7px 20px 20px 30px;
    height: auto;
    padding-bottom: 20px;
    width: 50%;
  }
  .el-checkbox {
    margin-right: 10px;
  }
  .weight {
    font-weight: 600;
    color: #515151;
  }
  .qf-form-item .qf-form-content {
    position: relative;
    margin-left: 105px !important;
  }
  .qf-form-item .qf-form-label {
    width: 110px !important;
  }
  .red {
    color: #f56c6c;
  }
  .short {
    width: 100px;
  }
  .inputs-area {
    margin-left: 40px;
    padding-top: 20px;
  }
  .inner-checks {
    margin-top: 15px;
    margin-left: 10px;
  }
}
</style>
