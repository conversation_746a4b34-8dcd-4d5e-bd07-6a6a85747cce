import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import FormItem from 'cmp/formitem/FormItem';
import MyQueryCmp from 'cmp/querycondition/MyQueryCmp';
import CardTemplateSelectorDialog from 'cmp/selectordialogs/CardTemplateSelectorDialog';
import I18nPage from 'common/I18nDecorator';
import RemakingCardBillApi from 'http/card/oldCard/RemakingCardBillApi';
import WriteMachineApi from 'http/card/writeCard/WriteMachineApi';
import CardTemplate from 'model/card/template/CardTemplate';
import CardTemplateFilter from 'model/card/template/CardTemplateFilter';
import BRemakingCardBill from 'model/card/writeCard/BRemakingCardBill';
import BRemakingCardBillFilter from 'model/card/writeCard/BRemakingCardBillFilter';
import RemakingCardRequest from 'model/card/writeCard/RemakingCardRequest';
import WriteCardMachineRequest from 'model/card/writeCard/WriteCardMachineRequest';
import IdName from 'model/common/IdName';
import PrePayCard from 'model/prepay/card/PrePayCard';
import CommonUtil from 'util/CommonUtil';
import { Component, Vue } from 'vue-property-decorator';

class Form {
  card: Nullable<PrePayCard> = null; // 已选卡
  defaultCardTemplate: Nullable<boolean> = true; //是否默认卡模板
  cardTemplate: Nullable<IdName> = null //卡模板
}

@Component({
  name: 'OldCardReset',
  components: {
    BreadCrume,
    FormItem,
    MyQueryCmp,
    CardTemplateSelectorDialog
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/卡/坏卡重制',
    "/卡/写卡",
    "/卡/卡管理/卡介质"
  ],
  auto: true
})
export default class OldCardReset extends Vue {
  $refs: any
  form: Form = new Form();
  state: 'UNSTART' | 'RESETTING' | 'FAILED' | 'SUCCESS' = 'UNSTART'
  topicText: string = ''
  cardList: PrePayCard[] = [] //查到的卡号列表
  currentWriteCard: Nullable<string> = null; // 当前读取的实体卡sn号
  logList: BRemakingCardBill[] = [] // 操作日志列表
  cardCodeLikes: string = ''  //操作日志 卡号类似于
  operateDateRange: Date[] = [] //操作日志 时间范围
  loading: boolean = false
  successCode: string = ''  //当前重制成功的卡号
  selectLoading: boolean = false
  billNumber: Nullable<string> = null; // 制卡单号
  page: any = {
    currentPage: 1,
    size: 10,
    total: 0
  }

  get panelArray() {
    return [
      {
        name: this.i18n("/公用/菜单/旧卡重制"),
        url: "",
      },
    ];
  }

  get rules() {
    return {
      card: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: ["blur", "change"] }],
      defaultCardTemplate: [
        {
          required: false,
          validator: (rule: any, val: any, callback: any) => {
            if (!this.form.defaultCardTemplate && !this.form.cardTemplate?.id) {
              callback(new Error(this.i18n('/储值/预付卡/电子礼品卡活动/编辑页面/请选择卡模板')));
            }
            callback();
          },
          trigger: ["blur", "change"],
        },
      ]
    };
  }

  get cardTemplateFilter() {
    const filter = new CardTemplateFilter()
    if (this.form.card) {
      filter.cardMediumIn = [this.form.card.cardMedium!]
      filter.cardTemplateTypeEquals = this.form.card.cardType
    }
    return filter
  }

  get formDisable() {
    return this.state === 'RESETTING';
  }

  get isEnglish() {
    return sessionStorage.getItem("locale") === "en_US";
  }

  created() {
    this.queryLogList()
  }

  // 开始重制
  startReset() {
    this.$refs.form.validate().then(() => {
      const loading = CommonUtil.Loading();
      const params = new BRemakingCardBill();
      params.code = this.form.card?.code || null;
      params.operator = sessionStorage.getItem("userName");
      params.occurredTime = new Date();
      params.defaultCardTemplate = this.form.defaultCardTemplate;
      if (this.form.defaultCardTemplate === false) {
        params.cardTemplateNumber = this.form.cardTemplate?.id;
        params.cardTemplateName = this.form.cardTemplate?.name;
      }
      RemakingCardBillApi.save(params)
        .then((res) => {
          if (res.code === 2000) {
            this.billNumber = res.data;
            // 重制流程：打开卡机 => 校验是否有卡 => 检验是否为空卡 => 清卡(接着校验是否空卡) => 制卡 => 读卡
            this.openMachine().then(this.resetProcess)
          } else {
            throw new Error(res.msg!);
          }
        })
        .catch((error) => {
          this.$message.error(error.message);
        })
        .finally(() => {
          loading.close();
        });
    })
  }

  doReset() {
    this.page.currentPage = 1
    this.page.size = 10
    this.cardCodeLikes = ''
    this.operateDateRange = []
    this.queryLogList()
  }

  doSearch() {
    this.page.currentPage = 1
    this.queryLogList()
  }

  // 选择卡模板
  doSelectCardTemplate() {
    if (this.state === 'RESETTING') {
      const route = this.$router.resolve({ name: 'prepay-card-tpl-dtl', query: { number: this.form.cardTemplate?.id } })
      window.open(route.href, '_blank')
      return
    }
    if (!this.form.card?.code) {
      return this.$message.warning(this.i18n("请先填写卡号"));
    }
    const obj = new CardTemplate()
    obj.number = this.form.cardTemplate?.id
    obj.name = this.form.cardTemplate?.name
    const cardArr = this.form.cardTemplate?.id ? [obj] : []
    this.$refs.cardTemplateSelectorDialog.open(cardArr, 'single')
  }

  // 查询操作日志
  queryLogList() {
    const params = new BRemakingCardBillFilter()
    params.page = this.page.currentPage - 1
    params.pageSize = this.page.size
    params.code = this.cardCodeLikes || null
    params.createdBetweenClosedOpen = this.operateDateRange?.length ? this.operateDateRange : null
    this.loading = true
    RemakingCardBillApi.query(params).then((res) => {
      if (res.code === 2000) {
        this.logList = res.data || []
        this.page.total = res.total || 0
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    }).finally(() => {
      this.loading = false
    })
  }

  async resetProcess() {
    this.topicText = this.form.card?.cardMedium === 'mag' ? this.i18n('请刷磁卡') : this.i18n("写卡中，请等待");
    const hasCard = await this.checkHasCard() // 校验是否有卡，拿到sn
    if (hasCard) {
      if (this.form.card?.cardMedium === 'mag') {
        //磁条卡无法判断空卡，所以直接进写卡流程
        return this.makeCardProcess()
      }
      const emptyRes = await this.checkIsEmpty()  // 校验是否空卡
      if (!emptyRes) {
        // 非空卡
        this.$confirm(this.i18n("该卡非空卡，确定清卡重制吗？"), this.i18n("重制"), {
          confirmButtonText: this.i18n("确认"),
          cancelButtonText: this.i18n("取消"),
        })
          .then(() => {
            this.clearAndMake()
          })
          .catch(() => {
            this.state = 'FAILED'
            this.topicText = this.i18n('重制失败')
          });
      } else {
        // rfic卡、ic卡如果是空卡，直接进写卡流程
        return this.makeCardProcess()
      }
    } else {
      this.state = 'FAILED'
      this.topicText = this.i18n("请在卡机放置新卡或检查卡是否已经放好");
    }
  }

  // 清卡并重新写卡
  async clearAndMake() {
    const clearRes = await this.clearCard()
    if (clearRes) {
      // 清卡成功且为空卡
      this.makeCardProcess()
    } else {
      // 清卡失败
      this.$confirm(this.i18n("清卡失败，确定重试吗？"), this.i18n("重制"), {
        confirmButtonText: this.i18n("确认"),
        cancelButtonText: this.i18n("取消"),
      })
        .then(() => {
          this.clearAndMake()
        })
        .catch(() => {
          this.state = 'FAILED'
          this.topicText = this.i18n('重制失败')
        });
    }
  }

  //写卡
  async makeCardProcess() {
    try {
      const makeRes = await this.makeCard(); //制卡
      const readCardRes = await this.readCard(); //读卡
      if (makeRes && readCardRes) {
        // 制卡+读卡完成后 通知后端
        this.resetSuccess(1);
      }
    } catch (error) {
      // 写卡失败
      this.$confirm(this.i18n("写卡失败，确定重试吗？"), this.i18n("重制"), {
        confirmButtonText: this.i18n("确认"),
        cancelButtonText: this.i18n("取消"),
      }).then(() => {
        this.clearAndMake()
      }).catch(() => {
        this.topicText = this.i18n('重制失败')
        this.state = 'FAILED'
      });
    }
  }

  doRemoteQuery(value: string) {
    if (value) {
      this.selectLoading = true
      RemakingCardBillApi.getRecover(value).then((res) => {
        if (res.code === 2000) {
          this.cardList = res.data || []
        } else {
          throw new Error(res.msg!)
        }
      }).catch((error) => {
        this.$message.error(error.message)
      }).finally(() => {
        this.selectLoading = false
      })
    }
  }

  // 打开并初始化卡机
  openMachine() {
    return new Promise<void>((resolve, reject) => {
      WriteMachineApi.open(this.form.card?.cardMedium!)
        .then((res) => {
          if (res.code === 2000) {
            this.state = 'RESETTING';
            resolve()
          } else {
            throw new Error(res.msg || this.i18n("打开卡机失败，请检查"));
          }
        })
        .catch((error) => {
          let msg = error.message
          if (msg.indexOf('请检查网络设置') > -1) {
            msg = this.i18n("打开卡机失败，请检查")
          }
          this.$message.error(msg)
          this.topicText = this.i18n("打开卡机失败，请检查");
          reject()
        })
    })
  }

  // 检查卡机是否有卡，并返回实体卡号
  checkHasCard() {
    return new Promise((resolve, reject) => {
      WriteMachineApi.checkAndGet()
        .then((res) => {
          if (res.code === 2000) {
            if (res.data === 0) {
              // 0：未放置卡片
              resolve(false);
            } else {
              // 返回了sn号
              this.currentWriteCard = String(res.data);
              resolve(true);
            }
          } else {
            throw new Error(res.msg!);
          }
        })
        .catch((error) => {
          this.$message.error(this.i18n("检查是否有卡失败"));
          resolve(false);
        });
    });
  }

  // 清卡
  clearCard() {
    return new Promise((resolve, reject) => {
      WriteMachineApi.clear(this.currentWriteCard!)
        .then((res) => {
          if (res.code === 2000 && res.data) {
            // 清卡成功后，立刻校验空卡
            this.checkIsEmpty().then((readRes) => {
              if (readRes) {
                resolve(true);
              }
            });
          } else {
            throw new Error();
          }
        })
        .catch((error) => {
          this.$message.error(this.i18n("清卡失败"));
          resolve(false);
        });
    });
  }

  // 检测是否为空卡
  checkIsEmpty() {
    return new Promise((resolve, reject) => {
      if (!this.currentWriteCard) {
        reject();
        return;
      }
      WriteMachineApi.isEmpty(this.currentWriteCard)
        .then((res) => {
          if (res.code === 2000) {
            if (res.data === true) {
              // 空卡
              resolve(true);
            } else {
              resolve(false);
            }
          } else {
            throw new Error(res.msg!);
          }
        })
        .catch((error) => {
          this.$message.error(this.i18n("检查空卡失败"));
          reject(error);
        });
    });
  }

  // 开始写卡
  makeCard() {
    return new Promise((resolve, reject) => {
      const params = new WriteCardMachineRequest();
      params.sn = Number(this.currentWriteCard);
      params.innerCode = this.form.card?.code;
      params.code = this.form.card?.code;
      WriteMachineApi.make(params)
        .then((res) => {
          if (res.code === 2000 && res.data) {
            resolve(true);
          } else {
            throw new Error(res.msg!);
          }
        })
        .catch((error) => {
          this.$message.error(this.i18n("制卡失败"));
          reject(error);
        });
    });
  }

  // 读卡
  readCard() {
    return new Promise((resolve, reject) => {
      if (!this.currentWriteCard) {
        reject();
        return;
      }
      WriteMachineApi.read(this.currentWriteCard)
        .then((res) => {
          if (res.code === 2000) {
            resolve(true);
          } else {
            throw new Error(res.msg!);
          }
        })
        .catch((error) => {
          this.$message.error(this.i18n("读卡失败"));
          reject(error);
        });
    });
  }

  // 通知后端重制成功
  resetSuccess(state: number) {
    const params = new RemakingCardRequest();
    params.billNumber = this.billNumber || null;
    params.cardCode = this.form.card?.code || null;
    params.operator = sessionStorage.getItem("userName");
    params.currentState = state;
    RemakingCardBillApi.remakingCard(params).then(async (res) => {
      if (res.code === 2000) {
        this.state = 'SUCCESS'
        this.successCode = this.form.card?.code || ''
        this.form.card = null
        this.cardList = []
        try {
          await RemakingCardBillApi.finish(this.billNumber!)
        } catch (error: any) {
          this.$message.error(error.message)
        }
        this.queryLogList()
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
      this.state = 'FAILED'
      this.topicText = this.i18n('重制失败')
    })
  }

  onHandleCurrentChange(val: number) {
    this.page.currentPage = val;
    this.queryLogList();
  }

  onHandleSizeChange(val: number) {
    this.page.size = val;
    this.queryLogList();
  }

  doCardTemplateSelected(arr: CardTemplate[]) {
    this.form.cardTemplate = new IdName()
    this.form.cardTemplate.id = arr[0].number
    this.form.cardTemplate.name = arr[0].name
    this.$refs.form.validateField('defaultCardTemplate')
  }

  defaultCardTemplateChange() {
    this.form.cardTemplate = null
    this.$refs.form.validateField('defaultCardTemplate')
  }
};