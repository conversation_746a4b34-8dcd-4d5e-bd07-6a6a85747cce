
import Response from 'model/default/Response'
import ApiClient from "http/ApiClient";
import ReceiveCardBillSaveRequest from "model/card/receivebill/ReceiveCardBillSaveRequest";
import ReceiveCardBill from "model/card/receivebill/ReceiveCardBill";
import ReceiveCardBillPrepareResponse from "model/card/receivebill/ReceiveCardBillPrepareResponse";
import {ReceiveCardBillFilter} from "model/card/receivebill/ReceiveCardBillFilter";
import ReceiveCardBillModifyRequest from "model/card/receivebill/ReceiveCardBillModifyRequest";
import ReceiveCardBillLine from "model/card/receivebill/ReceiveCardBillLine";
import ReceiveCardBillLineFilter from "model/card/receivebill/ReceiveCardBillLineFilter";

export default class ReceiveCardBillApi {
  /**
   * 审核领卡单
   * 审核领卡单。
   * 
   */
  static audit(billNumber: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/receive-card-bill/audit/${billNumber}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 计算卡数量
   * 计算卡数量。
   * 
   */
  static calculateCartCount(body: ReceiveCardBillSaveRequest): Promise<Response<number>> {
    return ApiClient.server().post(`/v1/receive-card-bill/cartCount/calculate`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取领卡单详情
   * 获取领卡单详情。
   * 
   */
  static get(billNumber: string): Promise<Response<ReceiveCardBill>> {
    return ApiClient.server().get(`/v1/receive-card-bill/${billNumber}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 准备新建领卡单
   * 准备新建领卡单。
   * 
   */
  static prepare(body: ReceiveCardBillSaveRequest): Promise<Response<ReceiveCardBillPrepareResponse>> {
    return ApiClient.server().post(`/v1/receive-card-bill/saveNew/prepare`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询领卡单
   * 分页查询领卡单。
   * 
   */
  static query(body: ReceiveCardBillFilter): Promise<Response<ReceiveCardBill[]>> {
    return ApiClient.server().post(`/v1/receive-card-bill/query`, body, {
    }).then((res) => {
      return res.data
    })
  }
  /**
   * 分页查询领卡单明细行
   * 分页查询领卡单明细行。
   *
   */
  static queryLine(body: ReceiveCardBillLineFilter): Promise<Response<ReceiveCardBillLine[]>> {
    return ApiClient.server().post(`/v1/receive-card-bill/line/query`, body, {
    }).then((res) => {
      return res.data
    })
  }


  /**
   * 新建领卡单
   * 新建领卡单。
   * 
   */
  static save(body: ReceiveCardBillSaveRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/receive-card-bill/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建并审核领卡单
   * 新建并审核领卡单。
   * 
   */
  static saveAndAudit(body: ReceiveCardBillSaveRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/receive-card-bill/saveAndAudit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改领卡单
   * 修改领卡单。
   * 
   */
  static saveModify(body: ReceiveCardBillModifyRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/receive-card-bill/saveModify`, body, {
    }).then((res) => {
      return res.data
    })
  }


  /**
   * 导出明细
   * 导出明细
   *
   */
  static export(billNumber: string): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/receive-card-bill/export/${billNumber}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

}
