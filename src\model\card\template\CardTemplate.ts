
/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-07-30 15:46:43
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\card\template\CardTemplate.ts
 * 记得注释
 */
import GoodsRange from 'model/common/GoodsRange'
import IdName from 'model/common/IdName'
import StoreRange from 'model/common/StoreRange'
import TemplateValidity from 'model/card/template/TemplateValidity';
import CountingCardGoodsGroup from './CountingCardGoodsGroup';
import CardTemplateExtInfo from './CardTemplateExtInfo';
import BCouponTemplatePromotion from 'model/common/BCouponTemplatePromotion';
import GoodsFavRule from 'model/common/GoodsFavRule';

export default class CardTemplate {
  // 卡模板号
  number: Nullable<string> = null
  // 卡模板名称
  name: Nullable<string> = null
  // 账户类型
  accountType: Nullable<IdName> = null
  // 卡类型：ONLINE_GIFT_CARD——电子礼品卡；OFFLINE_GIFT_CARD——实体礼品卡；IMPREST_CARD——充值卡；RECHARGEABLE_CARD——储值卡；COUNTING_CARD——次卡
  cardTemplateType: Nullable<string> = null
  // 卡样图片
  cardPictureUrls: string[] = []
  // 卡面额
  faceAmounts: Array<Nullable<number>> = []
  // 有效期
  validityInfo: Nullable<TemplateValidity> = new TemplateValidity()
  // 适用商品
  goods: Nullable<GoodsRange> = new GoodsRange()
  // 适用门店
  stores: Nullable<StoreRange> = new StoreRange()
  // 支付是否开启密码
  enablePayPassword: Nullable<boolean> = true
  // 转出是否开启密码
  enableTransferOutPassword: Nullable<boolean> = true
  // 是否允许转赠
  allowPresent: Nullable<boolean> = true
  // 卡模板
  remark: Nullable<string> = null
  //是否一次性消费
  enableOneTimeConsume: Nullable<boolean> = false
  //整单支付是否享受会员价
  enjoyMembershipPrice: Nullable<boolean> = false
  //能否绑为会员识别码
  allowBind: Nullable<boolean> = true
  // 次数
  count: Nullable<number> = null
  // 兑换商品组
  countingCardGoodsGroups: CountingCardGoodsGroup[] = []
  // 卡号长度
  cardCodeLength: Nullable<number> = null
  // 卡价格
  price: Nullable<string> = null
  // 卡介质  online-电子卡、bar-条码卡、mag-磁条卡
  cardMedium: Nullable<string> = null
  // 扩展信息
  extInfo: Nullable<CardTemplateExtInfo> = null
  // 是否参与促销叠加，true表示叠加优惠，false表示不可参与叠加
  excludePromotion: Nullable<boolean> = null;
  // 促销叠加类型 ALL("全部"),PART("部分")
  promotionSuperpositionType: Nullable<"ALL" | "PART"> = null;
  // 促销单信息
  promotion: Nullable<BCouponTemplatePromotion> = null;
  // 卡号前缀
  codePrefix: Nullable<string> = null
  // 卡数量(前端使用)
  qty?: Nullable<number> = null
  // 是否支持得积分，默认true
  payObtainPoints?:  Nullable<boolean> = true
  // 叠加优惠， 字段类型与取值同券模版-现金券
  goodsFavRules:  Nullable<GoodsFavRule[]> = null
}