import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import I18nPage from 'common/I18nDecorator';
import { Component } from 'vue-property-decorator';
import ContentTemplateApi from 'http/template/ContentTemplateApi'
import ContentTemplateFilter from 'model/template/ContentTemplateFilter'
import ContentTemplate from 'model/template/ContentTemplate'
import ListWrapper from 'cmp/list/ListWrapper.vue'
import FormItem from 'cmp/formitem/FormItem.vue'
import { EditMode } from 'model/local/EditMode';
import { ContentTemplateState } from 'model/template/ContentTemplateState'
import PageSpreadDialog from './cmp/page-spread-dialog/PageSpreadDialog';
import CommonUtil from 'util/CommonUtil';
import PopularizeResponse from 'model/template/PopularizeResponse';
import ShareConfigDialog from './cmp/share-config-dialog/ShareConfigDialog';
import InvestPageList from './mixin/InvestPageList';
import EmployeeDialog from './cmp/employee-dialog/EmployeeDialog';
import DownloadCenterDialog from 'pages/main/dowmload_center_dialog/DownloadCenterDialog';
import CmsConfig, { CmsConfigChannel, CmsConfigUtils } from 'model/template/CmsConfig';
import ReleaseChannelDialog from './cmp/release-channel-dialog/ReleaseChannelDialog.vue';
import StoreCodeDialog from "pages/invest/page-manage/cmp/store-code-dialog/StoreCodeDialog";

@Component({
  name: 'PageManageEdit',
  components: {
    BreadCrume,
    ListWrapper,
    FormItem,
    PageSpreadDialog,
    ShareConfigDialog,
    EmployeeDialog,
    StoreCodeDialog,
    DownloadCenterDialog,
    ReleaseChannelDialog
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理',
    '/设置/页面管理',
  ],
  auto: true
})
export default class PageManageEdit extends InvestPageList {
  ContentTemplate: ContentTemplate[] = []// 活动模型
  ContentTemplateFilter: ContentTemplateFilter = new ContentTemplateFilter()
  ContentTemplateState = ContentTemplateState
  downloadShow: boolean = false
  cmsConfig: CmsConfig = new CmsConfig()
  page = {
    currentPage: 1,
    total: 0,
    size: 20
  }
  get panelArray() {
    return [
      {
        name: this.i18n('/公用/菜单/页面管理'),
        url: ''
      }
    ]
  }

  // 创建自定义页面时是否需要展示选择渠道弹窗
  get showChannelDialog() {
    return this.cmsConfig.publishedChannels && this.cmsConfig.publishedChannels.length > 1
  }

  doCreate() {
    if (this.showChannelDialog) {
      this.$refs.releaseChannelDialog.open()
    } else {
      // 如果渠道只有一个，则直接前往新建页面
      this.$router.push({
        name: "page-manage-edit",
        query: {
          editModel: EditMode.create,
          channel: JSON.stringify(this.cmsConfig.publishedChannels)
        },
      });
    }
  }

  openDownload() {
    console.log(2222)
    this.downloadShow = true;
  }

  doDialogClose() {
    this.downloadShow = false
  }


  mounted() {
    this.getConfig()
    this.getList()
  }

  getConfig() {
    ContentTemplateApi.getConfig().then(res => {
      if (res.code === 2000) {
        this.cmsConfig = res.data || new CmsConfig()
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    })
  }

  // 渠道选择完成，前往新建页面
  doSelectChannel(channel: string[]) {
    this.$router.push({
      name: "page-manage-edit",
      query: {
        editModel: EditMode.create,
        channel: JSON.stringify(channel)
      }
    })
  }

  /**
    * 获取列表数据
    */
  getList() {
    this.ContentTemplateFilter.page = this.page.currentPage - 1
    this.ContentTemplateFilter.pageSize = this.page.size
    ContentTemplateApi.query(this.ContentTemplateFilter).then(res => {
      if (res.code === 2000) {
        this.ContentTemplate = res.data || []
        this.page.total = res.total
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  /**
   * 查询
   */
  doSearch() {
    this.page.currentPage = 1
    this.getList()
  }

  /**
   * 重置
   */
  doReset() {
    this.ContentTemplateFilter = new ContentTemplateFilter()
    this.getList()
    // for (let item of this.queryData) {
    //   this.$refs.dataTable.toggleRowExpansion(item, false)
    // }
  }
  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.getList()
  }
  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.getList()
  }
  /**
   * 编辑
   */
  doEdit(row: any) {
    const loading = CommonUtil.Loading()
    const query: any = {
      editId: row.id,
      editModel: EditMode.edit,
      channel: row.channels ? JSON.stringify(row.channels) : JSON.stringify([CmsConfigChannel.WEIXIN])
    }
    this.$router.push({ name: "page-manage-edit", query: query, },
      () => {
        loading.close()
      }
    );
  }
  /**
  * 发布 
  */
  doPublish(row: any) {
    ContentTemplateApi.publish({ id: row.id }).then(res => {
      if (res && res.code === 2000) {
        this.$message.success(this.i18n('发布成功'))
        this.getList()
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  /**
  * 推广
  */
  doSpread(row: ContentTemplate) {
    const loading = CommonUtil.Loading()
    ContentTemplateApi.popularize(row.id!).then((res) => {
      if (res.code === 2000) {
        this.$refs.pageSpreadDialog.open(row, res.data || [], 'ALL')
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    }).finally(() => {
      loading.close()
    })
  }
  /**
  * 删除
  */
  doRemove(row: any) {
    this.$confirm(this.i18n('是否确认删除？'), this.i18n('提示'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消'),
      type: 'warning'
    }).then(() => {
      ContentTemplateApi.remove({ id: row.id }).then(res => {
        if (res && res.code === 2000) {
          this.$message.success(this.i18n('删除成功'))
          this.getList()
        }
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    });
  }

  // 员工推广码
  showEmployeeCode(row: any) {
    this.$refs.employeeDialog.open(row.id, row.channels)
  }

  // 员工推广码
  showStoreCode(row: any) {
    this.$refs.storeCodeDialog.open(row.id, row.channels)
  }

  getLabel(channels: CmsConfigChannel[]) {
    let str = ''
    if (channels?.length) {
      channels.forEach(channel => {
        str += CmsConfigUtils.getLabel(channel) + '、'
      })
      return str.slice(0, -1)
    } else {
      return '--'
    }
  }

  // 是否可以设置导航栏
  canSetNavigation(row: ContentTemplate) {
    const isUse = row.usedNavChannels?.length //是否被引用为导航页面
    const isPublish = row.state === ContentTemplateState.published  //是否发布
    const isEnableNav = row.extInfo?.navigationShowed //是否启用导航栏
    const enableFlag1 = isPublish && isUse && !isEnableNav  //已发布 已引用 未启用
    const enableFlag2 = isPublish && !isUse && !isEnableNav //已发布 未引用 未启用
    const stopableFlag1 = isPublish && !isUse && isEnableNav //已发布 未引用 已启用
    return {
      enable: enableFlag1 || enableFlag2,
      stopable: stopableFlag1
    }
  }
};