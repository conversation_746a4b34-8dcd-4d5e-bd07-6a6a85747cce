<!--
 * @Author: 黎钰龙
 * @Date: 2024-07-05 13:53:06
 * @LastEditTime: 2025-05-28 17:16:43
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\cmp\page-spread-dialog\PageSpreadDialog.vue
 * 记得注释
-->
<template>
  <el-dialog :title="i18n('/页面/页面管理/推广')" width="800px" :close-on-click-modal="false" :visible.sync="visible" :before-close="handleClose"
    class="page-spread-dialog">
    <el-radio-group v-model="selectChannel" v-if="channel === 'ALL' && channelOptions.length" :label="i18n('/设置/页面管理/投放渠道')">
      <el-radio-button v-for="item in channelOptions" :key="item" :label="item">
        {{getLabel(item)}}
      </el-radio-button>
    </el-radio-group>
    <div class="block-title">
      <div class="section-title">{{i18n(isH5 ? 'H5二维码下载' : '小程序码下载')}}</div>
    </div>
    <div class="qrcode-block">
      <VueQrcode v-if="isH5 && h5Url" id="qrcode" :value="h5Url"></VueQrcode>
      <template v-else>
        <img class="qrcode-img" v-if="currentLinkInfo.qrCodeResponse" :src="currentLinkInfo.qrCodeResponse.qrCodeUrl" />
      </template>
      <div class="qrcode-download">
        <div class="gray-tips">{{i18n('如需直接推广，请使用白底二维码')}}</div>
        <div style="margin-top:12px">
          <el-button size="medium" @click="downloadCode">{{i18n('白底下载')}}</el-button>
          <!-- <el-button size="medium">{{i18n('透明底下载')}}</el-button> -->
        </div>
      </div>
    </div>
    <div class="block-title">
      <div class="section-title">{{i18n('路径复制')}}</div>
    </div>
    <div v-if="isH5" class="path-block">
      <div class="path-line">
        <div>{{i18n('适用于浏览器访问')}}</div>
        <span class="span-btn" @click="copyH5Path">{{i18n('复制路径')}}</span>
      </div>
    </div>
    <div v-else class="path-block">
      <div class="path-line">
        <div v-if="isWeixin">{{i18n('适用于公众号/小程序互跳等场景')}}</div>
        <div v-if="isAliApplet">{{i18n('适用于小程序互跳等场景')}}</div>
        <span class="span-btn" @click="copyPath">{{i18n('复制路径')}}</span>
      </div>
      <div class="path-line" v-if="isWeixin">
        <div>{{i18n('适用于微信内会话/朋友圈等场景')}}</div>
        <span class="span-btn" @click="copyShortLink">{{i18n('复制短链接')}}</span>
      </div>
      <div class="path-line" v-if="isWeixin">
        <div>{{i18n('适用于短信/邮件/微信外网页等场景')}}</div>
        <span class="span-btn" @click="copyAppLink">{{i18n('复制小程序链接')}}</span>
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./PageSpreadDialog.ts">
</script>

<style lang="scss" scoped>
.page-spread-dialog {
  height: 700px;
  ::v-deep .is-active {
    .el-radio-button__inner {
      color: #fff;
      background: #20a0ff;
    }
  }
  .block-title {
    display: flex;
    align-items: center;
    padding-left: 0;
    &::before {
      content: "";
      background-color: #007eff;
      width: 4px;
      height: 16px;
      margin-right: 8px;
    }
  }
  .qrcode-block {
    display: flex;
    align-items: center;
    width: 490px;
    height: 218px;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #d7dfeb;
    padding: 16px;
    box-sizing: border-box;
    .qrcode-img {
      width: 200px;
      height: 200px;
      margin-right: 24px;
    }
  }
  .path-block {
    background-color: #f9fbfc;
    padding: 4px 12px;
    border-radius: 4px;
    .path-line {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 32px;
      font-size: 12px;
    }
  }
}
</style>