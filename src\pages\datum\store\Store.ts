import { Component, Vue } from "vue-property-decorator";
import FormItem from "cmp/formitem/FormItem.vue";
import ListWrapper from "cmp/list/ListWrapper.vue";
import SubHeader from "cmp/subheader/SubHeader.vue";
import RSOrg from "model/common/RSOrg";
import OrgApi from "http/org/OrgApi";
import OrgFilter from "model/datum/org/OrgFilter";
import RSOrgFilter from "model/common/RSOrgFilter";
import FloatBlock from "cmp/floatblock/FloatBlock.vue";
import RSSaveBatchOrgRequest from "model/common/RSSaveBatchOrgRequest";
import UploadFileModal from "pages/datum/store/UploadFileModal";
import DownloadCenterDialog from 'pages/main/dowmload_center_dialog/DownloadCenterDialog'
import BreadCrume from "cmp/bread-crumb/BreadCrume";
import MarketingCenterApi from "http/marketingcenter/MarketingCenterApi";
import RSMarketingCenterFilter from "model/common/RSMarketingCenterFilter";
import RS<PERSON>ark<PERSON><PERSON><PERSON> from "model/common/RSMarketingCenter";
import IdName from "model/common/IdName";
import ZoneApi from "http/area/ZoneApi";
import SysConfigApi from "http/config/SysConfigApi";
import ZoneFilter from "model/datum/zone/ZoneFilter";
import BrowserMgr from "mgr/BrowserMgr";
import platOrgInfos from "model/common/PlatOrgInfos";
import { State } from "vuex-class";
import UserLoginResult from "model/login/UserLoginResult";
import UploadFileModalPlatform from "./UploadFileModalPlatform";
import AddressSelector from "cmp/addressselector/AddressSelector";
import Address from "model/common/Address";
import DistrictFilter from "model/district/DistrictFilter";
import DistrictApi from "http/district/DistrictApi";
import CommonUtil from 'util/CommonUtil'
import I18nPage from "common/I18nDecorator";
import PlatOrgInfo from "model/common/PlatOrgInfo";
import QueryPlatFormStoreRequest from "model/common/QueryPlatFormStoreRequest";
import {OrgState, OrgStateMap} from "model/common/OrgState";
import MeiTuanShopCateringApi from 'http/meituan/MeiTuanShopCateringApi';
import MeiTuanShopCateringAuthRequest from "model/meituanshopcatering/MeiTuanShopCateringAuthRequest";
import MeiTuanPTAuthApi from "http/meituan/MeiTuanPTAuthApi";

@Component({
  name: "Store",
  components: {
    FormItem,
    SubHeader,
    BreadCrume,
    UploadFileModal,
    UploadFileModalPlatform,
    DownloadCenterDialog
  },
})
@I18nPage({
  auto: false,
  prefix: [
    '/资料/渠道',
    '/公用/按钮',
    '/设置/权限/用户管理/功基本信息'
  ],
})
export default class Store extends Vue {
  i18n: I18nFunc
  @State("loginInfo")
  loginInfo: UserLoginResult;
  panelArray: any = [];

  activeTab: string = 'all'
  enableMultiMarketingCenter: boolean = false;
  marketingCenters: IdName[] = [];
  marketingCenterFilter: RSMarketingCenterFilter = new RSMarketingCenterFilter();
  rsMarketingCenter: RSMarketingCenter[];
  showNoMarketingCenter: Nullable<boolean> = null; // 只展现没有区域的门店
  isMoreMarketing: boolean = false; // 是否开启多营销中心 true 开启 false 不开启
  areaSelectItem: any = null;
  updateAreaSelectItem: any = null; // 修改所属区域
  updateAreaDataId: any = null; // 修改所属区域
  updateOrgsForCenter: boolean = false; // 是否是批量修改门店所属营销中心
  areaData: any = []; // 区域查询
  ZoneFilter: ZoneFilter = new ZoneFilter();
  query: RSOrgFilter = new RSOrgFilter();
  loading: boolean = false
  shopData: any[] = [];
  showAuthState: boolean = false;
  meiTuanPT: boolean = false;
  page = {
    currentPage: 1,
    total: 0,
    size: 10,
  };
  selected: RSOrg[] = [];
  checkedAll: boolean = false;
  updateCenterDialogVisible: boolean = false;
  newIns: RSOrg = new RSOrg();
  updateAddressForm = {
    address: [],
    addressInfo: '',
    // 纬度
    lat: null,
    // 经度
    lng: null
  }
  updateIns: RSOrg = new RSOrg();
  marketingCenter2: IdName = new IdName(); // 弹窗的选择框
  modifyLoading = false;
  modifyDialogVisible = false;
  // 修改参数
  updatePlatformList: any[] = []

  queryPlat: QueryPlatFormStoreRequest = new QueryPlatFormStoreRequest()
  plateData: PlatOrgInfo[] = []
  platformList: any[] = [];

  orgStateItem: any = 'enable';
  OrgState = OrgState
  OrgStateMap = OrgStateMap

  handelOrgState(state: OrgState) {
    const mapObj: {
      [key: string]: string
    } = {
      [OrgState.enable]: 'enable',
      [OrgState.disable]: 'disable'
    }
    return mapObj[state]
  }

  created() {
    // this.platOrgInfos!.push(new platOrgInfos());
    let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
    if (sysConfig) {
      this.isMoreMarketing = sysConfig.enableMultiMarketingCenter;
    }
    if (sessionStorage.getItem("isMultipleMC") == "1") {
      this.enableMultiMarketingCenter = true;
    } else {
      this.enableMultiMarketingCenter = false;
    }
    this.panelArray = [
      {
        name: this.formatI18n("/公用/菜单/门店"),
        url: "",
      },
    ];
    this.getConfig();
    this.getAreaList()
    this.getList();
    this.getMarketingCenterList();
    this.getPlatform();
  }

  doChangeCenter() {
    this.page.currentPage = 1
    this.doShopSearch();
  }

  doChangeTab(tab: any, event: any) {
    console.log(this.activeTab, tab, event)
    if(this.activeTab == 'MeiTuanShopCatering' || this.activeTab == 'MeiTuanPT' ) {
      this.showAuthState = true;
    }else{
      this.showAuthState = false;
    }
    if(this.activeTab == 'MeiTuanPT') {
      this.meiTuanPT = true;
    } else {
      this.meiTuanPT = false;
    }
    this.page.currentPage = 1
    if (this.activeTab == 'all') {
      this.getList()
    } else {
      this.queryPlat = new QueryPlatFormStoreRequest();
      this.getPlatList()
    }
  }

  doPlatSearch() {
    this.page.currentPage = 1
    this.getPlatList();
  }
  doPlatReast() {
    this.page.currentPage = 1
    this.queryPlat = new QueryPlatFormStoreRequest()
    this.getPlatList();
  }

  doShopSearch() {
    this.page.currentPage = 1
    this.getList();
  }

  doStateSearch() {
    this.page.currentPage = 1
    this.getList();
  }

  getList() {
    this.query.page = this.page.currentPage - 1;
    this.query.pageSize = this.page.size;
    this.query.sorters = { lastModifyInfotime: "desc" };
    this.query.zoneIdEquals = this.areaSelectItem;
    this.query.queryNoZone = this.showNoMarketingCenter;
    this.query.orgStateEquals = this.orgStateItem;
    this.loading = true
    OrgApi.query(this.query)
      .then((resp: any) => {
        this.shopData = resp.data;
        this.page.total = resp.total;
      })
      .catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      }).finally(() => {
        this.loading = false
      })
  }

  getPlatList() {
    this.queryPlat.page = this.page.currentPage - 1
    this.queryPlat.pageSize = this.page.size
    this.queryPlat.channelTypeEquals = this.activeTab
    this.loading = true
    OrgApi.queryPlatFormStore(this.queryPlat)
      .then((resp: any) => {
        this.plateData = resp.data;
        this.page.total = resp.total;
      })
      .catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      }).finally(() => {
        this.loading = false
      })
  }

  /**
 * 查询区域
 */
  getAreaList() {
    ZoneApi.query(this.ZoneFilter).then((res) => {
      if (res.code === 2000) {
        this.areaData = res.data;
      } else {
        this.$message.error(res.msg as string);
      }
    });
  }

  getConfig() {
    SysConfigApi.get()
      .then((resp: any) => {
        if (resp && resp.data) {
          this.enableMultiMarketingCenter =
            resp.data.enableMultiMarketingCenter;
        }
      })
      .catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
  }

  /**
 * 分页页码改变的回调
 * @param val
 */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val;
    if (this.activeTab == 'all') {
      this.getList()
    } else {
      this.getPlatList()
    }
  }

  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val;
    if (this.activeTab == 'all') {
      this.getList()
    } else {
      this.getPlatList()
    }
  }

  handleSelectionChange(val: any) {
    this.selected = val.map((item: any) => {
      item.zone = { id: null, name: null };
      return item;
    });
  }

  selectAll() {
    this.checkedAll = !this.checkedAll;
  }

  buildStoreCoordinate(lat: number, lng: number) {
    let str = '-';
    if (lat && lng) {
      str = this.formatI18n("/资料/门店/经度：") + lng + ' ' + this.formatI18n("/资料/门店/纬度：") + lat;
    }
    return str;
  }

  buildChannelType(platOrgInfos: any) {
    let str = ''
    if (platOrgInfos && platOrgInfos.length > 0) {
      str = platOrgInfos.reduce((acc: any, cur: any) => {
        return cur.platformName ? acc + cur.platformName + '；' : acc
      }, '')
    }
    return str
  }

  buildStoreAddress(address: Address) {
    let str = '';
    str = str + this.nullToEmpty(address.country) + this.nullToEmpty(address.province) +
      this.nullToEmpty(address.city) + this.nullToEmpty(address.district) + this.nullToEmpty(address.street) +
      this.nullToEmpty(address.address);
    if (str.length == 0) {
      str = '-'
    }
    return str
  }
  nullToEmpty(str: any) {
    return str == null ? "" : str;
  }

  doAddShop() {
    this.$router.push({ name: 'StoreAdd', query: { storeFlag: "add", enableMultiMarketingCenter: this.enableMultiMarketingCenter ? '1' : '0' } })
  }
  showUpdateCenterDialog() {
    if (this.selected.length == 0) {
      this.$message.error(this.formatI18n("/资料/渠道/请选择至少一个门店"));
      return;
    }
    // this.updateIns = JSON.parse(JSON.stringify(row))
    this.updateCenterDialogVisible = true;
  }

  clear(type: string) {
    if (type === "modifyOrgMarketingCenter") {
      this.marketingCenter2.id = null;
    }
  }
  updateOrgsForMarketingCenter() {
    this.updateOrgsForCenter = true;
    for (let index in this.selected) {
      console.log("updateAreaDataId", this.updateAreaDataId);
      if (this.updateAreaDataId !== null) {
        this.selected[index].zone!.id = this.updateAreaDataId.id;
        this.selected[index].zone!.name = this.updateAreaDataId.name;
      } else if (this.updateAreaDataId === null) {
        this.selected[index].zone!.id = null;
        this.selected[index].zone!.name = null;
      }
    }
    this.modify();
  }
  cancelUpdateCenter() {
    this.marketingCenter2 = new IdName();
    this.updateCenterDialogVisible = false;
  }
  modify(type: Nullable<string> = null) {
    if (type && type === "modifyOrg") {
      if (!this.updateIns.org.name) {
        this.$message.error(this.formatI18n("/资料/门店/请输入门店名称"));
        return;
      }
      if (this.updateAddressForm.address && this.updateAddressForm.address.length > 0) {
        let address = new Address();
        let idName = new IdName();
        if (this.updateAddressForm.address[0]) {
          idName = this.updateAddressForm.address[0];
          address.province = idName.name
        }
        if (this.updateAddressForm.address[1]) {
          idName = this.updateAddressForm.address[1];
          address.city = idName.name
        }
        if (this.updateAddressForm.address[2]) {
          idName = this.updateAddressForm.address[2];
          address.district = idName.name
        }
        if (this.updateAddressForm.address[3]) {
          idName = this.updateAddressForm.address[3];
          address.street = idName.name
        }
        if (this.updateAddressForm.addressInfo) {
          address.address = this.updateAddressForm.addressInfo;
        }
        this.updateIns.address = address;
      }
      if (this.updateAddressForm.lat) {
        this.updateIns.lat = this.updateAddressForm.lat;
      }
      if (this.updateAddressForm.lng) {
        this.updateIns.lng = this.updateAddressForm.lng;
      }
      if (this.updateIns.platOrgInfos && this.updateIns.platOrgInfos.length > 0) {
        let pltCount = 0;
        let idCount = 0;
        this.updateIns.platOrgInfos.forEach((item: platOrgInfos) => {
          if (!item.platformCode) {
            pltCount++;
          }
          if (!item.platformStoreId) {
            idCount++;
          }
          this.updatePlatformList.forEach((item1: any) => {
            if (item.platformCode === item1.code) {
              item.platformName = item1.name
            }
          })
        });
        if (pltCount > 0) {
          this.$message.error(this.formatI18n("/资料/门店/请选择平台"));
          return;
        }
        if (idCount > 0) {
          this.$message.error(this.formatI18n("/资料/门店/请填写平台门店id"));
          return;
        }
      }
    }
    if (this.updateAreaSelectItem !== null) {
      this.updateIns.zone!.id = this.updateAreaSelectItem.id;
      this.updateIns.zone!.name = this.updateAreaSelectItem.name;
    } else {
      this.updateIns.zone!.id = null;
      this.updateIns.zone!.name = null;
    }
    this.modifyLoading = true;
    let req = new RSSaveBatchOrgRequest();
    if (this.updateOrgsForCenter) {
      req.list = this.selected;
    } else {
      req.list = [this.updateIns];
    }
    req.operator = this.loginInfo.user!.account
    OrgApi.saveBatch(req)
      .then((resp: any) => {
        this.$message.success(this.formatI18n("/资料/门店/修改成功"));
        this.page.currentPage = 1
        this.getList();
        this.updateIns = new RSOrg();
        this.modifyDialogVisible = false;
      })
      .catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      })
      .finally(() => {
        this.modifyLoading = false;
        this.updateCenterDialogVisible = false;
        this.updateOrgsForCenter = false;
        this.updateAreaDataId = null
      });
  }
  goShopDetails(data: any) {
    this.$router.push({ name: 'StoreDtl', query: { orgId: data.org.id, enableMultiMarketingCenter: this.enableMultiMarketingCenter ? '1' : '0' } })
  }
  goShopDetailsEdit(data: any) {
    this.$router.push({ name: 'StoreAdd', query: { orgId: data.org.id, storeFlag: 'edit', enableMultiMarketingCenter: this.enableMultiMarketingCenter ? '1' : '0' } })
  }

  // 授权
  authorization(data: any){

  }

  //解除授权
  secureAuthorization(data: any){
    MeiTuanShopCateringApi.getUnBindAuthUrl({
      orgId: data.orgId,
      orgName: data.orgName,
      channelType: data.channelType,
      channelId: data.channelId,
    }).then((res) => {
      if(res.code === 2000 && res.data) {
        window.open(res.data, '_self');
      } else {
        throw new Error(res.msg || '授权失败')
      }
    }).catch((error) => this.$message.error(error.message || '内部异常'))

  }


  getMarketingCenterList() {
    MarketingCenterApi.query(this.marketingCenterFilter)
      .then((resp: any) => {
        this.rsMarketingCenter = resp.data;
        for (let value in this.rsMarketingCenter) {
          this.marketingCenters.push(
            this.rsMarketingCenter[value].marketingCenter as IdName
          );
        }
      })
      .catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
  }
  fileDialogVisible = false
  showTip = false
  // 关闭文件中心弹框
  doDownloadDialogClose() {
    this.showTip = false
    this.fileDialogVisible = false
  }
  // 批量导出
  async exportFile() {
    if (this.activeTab == 'all') {
      const loading = CommonUtil.Loading()
      let params = new OrgFilter()
      params = { ...this.query, orgIdIn: null }
      try {
        const { code, msg } = await OrgApi.export(params)
        loading.close()
        if (code === 2000) {
          this.showTip = true
          this.fileDialogVisible = true
        } else {
          this.$message.error(msg as string)
        }
      } catch (error) {
        loading.close()
        this.$message.error((error as Error).message)
      }
    } else {
      const loading = CommonUtil.Loading()
      try {
        const { code, msg } = await OrgApi.exportPlatFormStore(this.queryPlat)
        loading.close()
        if (code === 2000) {
          this.showTip = true
          this.fileDialogVisible = true
        } else {
          this.$message.error(msg as string)
        }
      } catch (error) {
        loading.close()
        this.$message.error((error as Error).message)
      }
    }
  }

  getPlatform() {
    OrgApi.getPlatform().then((res: any) => {
      console.log(res.data);
      this.platformList = []
      if (res.data && res.data.length > 0) {
        this.platformList = res.data.reduce((acc: any[], cur: any) => {
          acc.push(cur)
          return acc
        }, [])
      }
    });
  }

  enableShop(row: any) {
    this.$confirm('确认启用该门店').then(() => {
      OrgApi.enableOrg(row.org.id)
          .then((res) => {
            if (res.code === 2000) {
              this.$message.success('启用成功');
              this.getList();
            } else {
              throw new Error(res.msg!)
            }
          })
          .catch((err) => {
            this.$message.error(err.message);
          });
    });
  }

  disableShop(row: any) {
    this.$confirm('确认停用该门店').then(() => {
      OrgApi.disableOrg(row.org.id)
          .then((res) => {
            if (res.code === 2000) {
              this.$message.success('停用成功');
              this.getList();
            } else {
              throw new Error(res.msg!)
            }
          })
          .catch((err) => {
            this.$message.error(err.message);
          });
    });
  }

  revokeAuthorization(data: any) {
    MeiTuanPTAuthApi.getUnBindAuthUrl({
      orgId: data.orgId,
      orgName: data.orgName,
      channelType: data.channelType,
      channelId: data.channelId,
      businessId: "58"
    }).then((res) => {
      if(res.code === 2000 && res.data) {
        window.open(res.data, '_blank');
      } else {
        throw new Error(res.msg || '解除授权失败')
      }
    }).catch((error) => this.$message.error(error.message || '内部异常'))
  }
}
