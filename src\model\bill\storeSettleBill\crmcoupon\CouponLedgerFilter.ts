export default class CouponLedgerFilter {
  // 发生门店Id等于
  occurredOrgIdEquals: Nullable<string> = null
  // 结算时间 >=
  settleTimeGreaterOrEquals: Nullable<string> = null
  // 结算时间 <=
  settleTimeLessOrEquals: Nullable<string> = null
  // 平台In
  platformIn: string[] = []
  // 当前页，从0开始
  page: Nullable<number> = null
  // 每页查询数量，最大支持1000
  pageSize: Nullable<number> = null
  // 
  probePages: Nullable<number> = null
}