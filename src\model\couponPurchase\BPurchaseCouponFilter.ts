/*
 * @Author: 黎钰龙
 * @Date: 2023-01-11 16:33:16
 * @LastEditTime: 2023-03-14 18:09:20
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\couponPurchase\BPurchaseCouponFilter.ts
 * 记得注释
 */
import { OrderType } from 'model/common/OrderType'

export default class BPurchaseCouponFilter {
  // 交易类型
  orderType: Nullable<OrderType> = null
  // 身份识别码
  identCodeEquals: Nullable<string> = null
  // 会员id
  memberIdEquals: Nullable<string> = null
  // 交易状态
  payStateEquals: Nullable<string> = null
  // 商户订单号
  tradeNoEquals: Nullable<string> = null
  // 支付订单号
  payOrderNoEquals: Nullable<string> = null
  // 活动类型
  activityTypeEquals: Nullable<string> = null
  // 活动子类型
  activitySubTypeIn: string[] = []
  // 活动子类型
  activitySubTypeNotIn: string[] = []
  // 活动号
  activityNumberEquals: Nullable<string> = null
  // 活动名
  activityNameEquals: Nullable<string> = null
  // 购买渠道
  channelTypeEquals: Nullable<string> = null
  // 购买渠道
  channelIdEquals: Nullable<string> = null
  // 发生时间
  occurredTimeEquals: Nullable<Date> = null
  // 发生时间
  occurredTimeAfterOrEqual: Nullable<Date> = null
  // 发生时间
  occurredTimeBefore: Nullable<Date> = null
  // 发生时间
  occurredTimeBetweenClosedOpen: Date[] = []
  // 发生时间
  occurredTimeBetweenOpenClosed: Date[] = []
  // 发券状态
  issueSuccess: Nullable<boolean> = null
  // 发生组织
  occurredOrgIdEquals: Nullable<string> = null
  // 营销中心
  marketingCenterEquals: Nullable<string> = null
  // 排序，key表示排序的字段，可选值：occurredTime；value表示排序方向，可选值为：asc, desc
  sorters: any
  // 页码
  page: Nullable<number> = null
  // 翻页大小
  pageSize: Nullable<number> = null
  // 商户订单号 
  originalTradeNoEquals: Nullable<string> = null
  // 筛选未支付的订单
  amountGreater: number = 0
}