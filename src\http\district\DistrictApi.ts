import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import DistrictFilter from 'model/district/DistrictFilter';
import District from 'model/district/District';

export default class DistrictApi {
  /**
   * 地址查询
   * 地址查询。
   * 
   */
  static query(body: DistrictFilter): Promise<Response<District[]>> {
    return ApiClient.server().post(`/v1/district/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
