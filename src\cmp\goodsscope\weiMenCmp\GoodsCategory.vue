<template>
    <div class="goods-category">
        <div class="category-box" :style="{ height: height + 'px' }" v-if="categoryList.length > 0">
            <div class="box-li" v-for="item in categoryList" :key="item.categoryId"
                :class="{ active: item.categoryId == firstCategoryInfo.categoryId }" @click="doChilkFirst(item)">
                <div class="li-content">{{ item.categoryName }}</div>
                <i class="el-icon-arrow-right li-icon" />
            </div>
        </div>
        <div class="category-box" :style="{ height: height + 'px' }" v-if="categoryList.length > 0" v-loading="loading">
            <div class="box-li" v-for="item in secondCategoryList" :key="item.categoryId"
                :class="{ active: item.categoryId == secondCategoryInfo.categoryId }" @click="doChilkSecond(item)">
                <div class="li-content">{{ item.categoryName }}</div>
            </div>
        </div>
        <empty :height="height" v-if="categoryList.length == 0" :emptyText="formatI18n('/公用/提示/暂无数据')" />
    </div>
</template>
<script lang="ts">
import { Vue, Component, Prop, Watch } from "vue-property-decorator";
import BWeimobExtLimitGoodsCategoryRule from 'model/common/weimob/BWeimobExtLimitGoodsCategoryRule'
import BQueryCategoryRequest from 'model/common/weimob/BQueryCategoryRequest'
import BCategory from 'model/common/weimob/BCategory'
import WeimobApi from 'http/coupon/template/WeimobApi'

import empty from './empty.vue'
@Component({
    components: { empty },
})
export default class GoodsCategory extends Vue {
    @Prop({
        default: 400
    })
    height: Number
    @Prop()
    categoryList: BCategory[]
    @Prop()
    limitGoodsCategoryTypeRule: BWeimobExtLimitGoodsCategoryRule
    @Prop({
        type: String,
        default: ''
    })
    org: string

    secondCategoryList: BCategory[] = []
    firstCategoryInfo: any = {}
    secondCategoryInfo: any = {}
    loading: boolean = false

    @Watch("limitGoodsCategoryTypeRule", {deep: true})
    onValueChange(value: BWeimobExtLimitGoodsCategoryRule) {
        this.doBindValue()
    }
    mounted() {
       this.doBindValue()
    }

    doBindValue() {
        if (this.limitGoodsCategoryTypeRule && this.limitGoodsCategoryTypeRule.ruleInfos && this.limitGoodsCategoryTypeRule.ruleInfos.length > 0) {
            this.firstCategoryInfo = this.limitGoodsCategoryTypeRule.ruleInfos[0]
            if (this.firstCategoryInfo && this.firstCategoryInfo.childs && this.firstCategoryInfo.childs.length > 0) {
                this.secondCategoryInfo = this.firstCategoryInfo.childs[0]
                this.queryCategoryList()
            }
        } else {
            this.firstCategoryInfo = {} 
            this.secondCategoryInfo = {} 
        }
    }

    doChilkFirst(item: BCategory) {
        this.firstCategoryInfo = {
                categoryId: item.categoryId,
                categoryName: item.categoryName,
                parentCategoryId: item.parentCategoryId,
                categoryLevel: null,
                isLeaf: item.isLeaf,
                childs: []
            }
        this.secondCategoryList = []
        this.secondCategoryInfo = {}
        this.queryCategoryList()
        this.$emit('change', this.firstCategoryInfo)
    }
    doChilkSecond(item: BCategory) {
        if (item.categoryId == this.secondCategoryInfo.categoryId) return
        this.secondCategoryInfo = {
                categoryId: item.categoryId,
                categoryName: item.categoryName,
                parentCategoryId: item.parentCategoryId,
                categoryLevel: null,
                isLeaf: item.isLeaf,
                childs: []
            }
        this.firstCategoryInfo.childs = [this.secondCategoryInfo]
        this.$emit('change', this.firstCategoryInfo)
    }

    async queryCategoryList() {
        const params = new BQueryCategoryRequest()
        if (this.org) params.marketingCenter = this.org
        if (!this.firstCategoryInfo.categoryId) return
        params.parentCategoryId = this.firstCategoryInfo.categoryId
        this.loading = true
        try {
            const {data} = await WeimobApi.queryCategoryList(params)
            this.secondCategoryList = data || []
            this.loading = false
        } catch (error) {
            this.$message.error((error as Error).message);
            this.loading = false
        }
    }
}
</script>
<style lang="scss" scoped>
.goods-category {
    width: 100%;
    display: flex;
    justify-content: space-between;

    .category-box {
        flex: 1;
        overflow-y: auto;
        border-right: 1px solid #e3e2e5;

        .box-li {
            width: 100%;
            height: 36px;
            padding: 0 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            &:hover {
                background: #f5f7fa;
            }

            &.active>.li-content {
                color: #006aff;
            }

            .li-content {
                color: #1e2226;
                font-size: 14px;
            }

            .li-icon {
                color: #b2aebc;
                font-size: 14px;
            }
        }
    }
}
</style>