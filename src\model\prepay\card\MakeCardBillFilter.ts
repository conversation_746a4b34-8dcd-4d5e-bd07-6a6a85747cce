/*
 * @Author: 黎钰龙
 * @Date: 2023-10-09 11:45:53
 * @LastEditTime: 2023-10-09 13:47:21
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\prepay\card\MakeCardBillFilter.ts
 * 记得注释
 */

export default class MakeCardBillFilter {
  // 卡号类似于
  cardTemplateNumberLikes: Nullable<string> = null
  // 卡号等于
  cardTemplateNumberEquals: Nullable<string> = null
  // 卡名称类似于
  cardTemplateNameLikes: Nullable<string> = null
  // 卡名称等于
  cardTemplateNameEquals: Nullable<string> = null
  // 单号等于
  billNumberEquals: Nullable<string> = null
  // 单号类似于
  billNumberLikes: Nullable<string> = null
  // 状态等于 INITIAL:未审核，AUDITED:已审核:FINISH:制卡完成:CANCELED:已作废
  stateEquals: Nullable<string> = null
  // 创建时间：created:(,]
  createdBetweenOpenClosed: Date[] = []
  // 创建时间：created:[,)
  createdBetweenClosedOpen: Date[] = []
  // 创建时间：created:[,]
  createdBetweenClosedClosed: Date[] = []
  // 最后修改时间：lastModified:(,]
  lastModifiedBetweenOpenClosed: Date[] = []
  // 最后修改时间：lastModified:[,)
  lastModifiedBetweenClosedOpen: Date[] = []
  // 最后修改时间：lastModified:[,]
  lastModifiedBetweenClosedClosed: Date[] = []
  // 营销中心等于
  marketingCenterEquals: Nullable<string> = null
  // 页码
  page: Nullable<number> = null
  // 页面大小，大于0
  pageSize: Nullable<number> = null
}