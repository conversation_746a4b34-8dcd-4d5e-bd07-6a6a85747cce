/*
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:02
 * @LastEditTime: 2025-04-18 13:39:40
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\date-time-condition-picker\DateTimeConditionPicker.ts
 * 记得注释
 */
import {Component, Prop, Watch} from 'vue-property-decorator'
import DateTimeCondition from "model/common/DateTimeCondition";
import DateTimeConditionForm, {
  MonthLineFormData,
  WeekLineFormData
} from "cmp/date-time-condition-picker/DateTimeConditionForm";
import DateTimeConditionPickerParent from "cmp/date-time-condition-picker/DateTimeConditionPickerParent";
import DateUtil from "util/DateUtil";

@Component({
  name: 'DateTimeConditionPicker',
  model: {
    prop: 'modelValue',
    event: 'change'
  }
})
export default class DateTimeConditionPicker extends DateTimeConditionPickerParent {
  @Prop()
  modelValue: DateTimeCondition
  form: DateTimeConditionForm = new DateTimeConditionForm()
  $refs: any
  @Prop({ type: Boolean }) disabled: boolean; //是否展示
  @Prop({ type: Boolean,default: false }) isHideWeek: boolean; //是否隐藏周
  @Prop({ type: Boolean,default: false }) isHideMonth: boolean; //是否隐藏月

  @Watch('modelValue', {deep: true, immediate: true})
  watchModelValue(value: DateTimeCondition) {
    this.form.of(value)
  }

  created() {
    this.form.init(this)
  }

  mounted() {
    this.onChange()
  }

  async validate() {
    return this.$refs.form.validate()
  }

  private onChangeType() {
    this.form.data.dayLines = [[DateUtil.nowDayTime(), new Date(DateUtil.nowDayTime().getTime() + 24 * 60 * 60 * 1000 - 1)]]
    this.form.data.weekLines = [new WeekLineFormData()]
    this.form.data.monthLines = [new MonthLineFormData()]
    this.onChange()
  }


  private onChange() {
    this.$emit('change', this.form.toParams())
  }

  get dateRangeOption() {
    return {
      format: 'HH:mm'
    }
  }

  private delLine(items: any[], index: number) {
    items.splice(index, 1)
    this.onChange()
  }

  private addDayLine() {
    this.form.data.dayLines.push([DateUtil.nowDayTime(), new Date(DateUtil.nowDayTime().getTime() + 24 * 60 * 60 * 1000 - 1)])
    this.onChange()
  }

  private addWeekLine() {
    this.form.data.weekLines.push(new WeekLineFormData())
    this.onChange()
  }

  private addMonthLine() {
    this.form.data.monthLines.push(new MonthLineFormData())
    this.onChange()
  }

  private range(min: number, max: number) {
    let i = min
    let result = []
    while (i <= max) {
      result.push(i++)
    }
    return result
  }

  private formatDay(day: number) {
    return day < 10 ? '0' + day : day
  }
}
