import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'

export default class MeiTuanInitApi {
    /**
     * 跳转商家授权UI组件
     *
     */
    static getAuthUrl(): Promise<Response<string>> {
        return ApiClient.server().get(`/v1/meituan-init/getAuthUrl`, {
        }).then((res) => {
            return res.data
        })
    }

    /**
     * 跳转商家授权UI组件
     *
     */
    static preAuthorize(): Promise<Response<number>> {
        return ApiClient.server().get(`/v1/meituan-init/preAuthorize`, {
        }).then((res) => {
            return res.data
        })
    }
}
