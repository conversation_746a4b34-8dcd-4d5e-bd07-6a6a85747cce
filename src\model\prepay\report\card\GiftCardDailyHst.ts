import Channel from 'model/common/Channel'
import IdName from 'model/common/IdName'

export default class GiftCardDailyHst {
  // 发生日期
  occurredDate: Nullable<Date> = null
  // 发生组织
  occurredOrg: Nullable<IdName> = null
  // 发生区域
  zone: Nullable<IdName> = null
  // 发生渠道
  channel: Nullable<Channel> = null
  // 卡模板号
  templateNumber: Nullable<string> = null
  // 卡模板名
  templateName: Nullable<string> = null
  // 卡类型
  cardType: Nullable<string> = null
  // 期初余额
  beginningBalance: Nullable<number> = null
  // 期初实充
  beginningAmount: Nullable<number> = null
  // 期初返现
  beginningGiftAmount: Nullable<number> = null
  // 期末余额
  endingBalance: Nullable<number> = null
  // 期末实充
  endingAmount: Nullable<number> = null
  // 期末返现
  endingGiftAmount: Nullable<number> = null
  // 售卡
  sale: Nullable<number> = null
  // 售卡实充
  saleAmount: Nullable<number> = null
  // 售卡返现
  saleGiftAmount: Nullable<number> = null
  // 退卡
  saleRefund: Nullable<number> = null
  // 退卡实充
  saleRefundAmount: Nullable<number> = null
  // 退卡返现
  saleRefundGiftAmount: Nullable<number> = null
  // 消费
  pay: Nullable<number> = null
  // 消费实充
  payAmount: Nullable<number> = null
  // 消费返现
  payGiftAmount: Nullable<number> = null
  // 退款
  payRefund: Nullable<number> = null
  // 退款实充
  payRefundAmount: Nullable<number> = null
  // 退款返现
  payRefundGiftAmount: Nullable<number> = null
  // 调整
  adjust: Nullable<number> = null
  // 调整实充
  adjustAmount: Nullable<number> = null
  // 调整返现
  adjustGiftAmount: Nullable<number> = null
  // 作废
  cancel: Nullable<number> = null
  // 作废实充
  cancelAmount: Nullable<number> = null
  // 作废返现
  cancelGiftAmount: Nullable<number> = null
  different :Nullable<number> = 0 // 差异
  inactiveTotalBalance :Nullable<number> = 0 // 未激活余额
  inactiveBalance :Nullable<number> = 0 // 未激活实充
  inactiveGiftBalance :Nullable<number> = 0 // 未激活赠送
  presentingTotalBalance :Nullable<number> = 0 // 转赠中余额
  presentingBalance :Nullable<number> = 0 // 转赠中实充
  presentingGiftBalance :Nullable<number> = 0 // 转赠中赠送
  usingTotalBalance :Nullable<number> = 0 // 使用中余额
  usingBalance :Nullable<number> = 0 // 使用中实充
  usingGiftBalance :Nullable<number> = 0 // 使用中赠送
  frozenTotalBalance :Nullable<number> = 0 // 已冻结余额
  frozenBalance :Nullable<number> = 0 // 已冻结实充
  frozenGiftBalance :Nullable<number> = 0 // 已冻结赠送
}