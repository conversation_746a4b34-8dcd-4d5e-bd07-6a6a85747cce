import ApiClient from 'http/ApiClient'
import CardBalancePromotionActivity from 'model/payment/card/CardBalancePromotionActivity'
import CardBalancePromotionActivityFilter from 'model/payment/card/CardBalancePromotionActivityFilter'
import CardBalancePromotionActivityQueryResult from 'model/payment/card/CardBalancePromotionActivityQueryResult'
import RSGrade from 'model/common/RSGrade'
import Response from 'model/common/Response'

export default class CardBalancePromotionApi {
  /**
   * 审核活动
   *
   */
  static audit(activityId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/card-balance-promotion/audit/${activityId}`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 批量审核预付卡支付活动
   *
   */
  static batchAudit(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/card-balance-promotion/batch/audit`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 批量删除预付卡支付活动
   *
   */
  static batchRemove(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/card-balance-promotion/batch/remove`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 批量终止预付卡支付活动
   *
   */
  static batchStop(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/card-balance-promotion/batch/stop`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 创建活动
   *
   */
  static create(body: CardBalancePromotionActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/card-balance-promotion/create`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 创建折扣活动
   *
   */
  static createDiscount(body: CardBalancePromotionActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/card-balance-promotion/createDiscount`, body, {}).then((res) => {
      return res.data
    })
  }


  //
  // /**
  //  * 活动效果评估
  //  *
  //  */
  // static evaluate(activityId: string): Promise<Response<DepositEvaluation>> {
  //   return ApiClient.server().get(`/v1/card-balance-promotion/evaluate/${activityId}`, {}).then((res) => {
  //     return res.data
  //   })
  // }

  /**
   * 查询等级列表
   *
   */
  static gradeList(): Promise<Response<RSGrade[]>> {
    return ApiClient.server().get(`/v1/card-balance-promotion/gradeList`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询活动
   *
   */
  static info(activityId: string): Promise<Response<CardBalancePromotionActivity>> {
    return ApiClient.server().get(`/v1/card-balance-promotion/info/${activityId}`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询折扣活动
   *
   */
  static infoDiscount(activityId: string): Promise<Response<CardBalancePromotionActivity>> {
    return ApiClient.server().get(`/v1/card-balance-promotion/infoDiscount/${activityId}`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 修改活动
   *
   */
  static modify(body: CardBalancePromotionActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/card-balance-promotion/modify`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 修改折扣活动
   *
   */
  static modifyDiscount(body: CardBalancePromotionActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/card-balance-promotion/modifyDiscount`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询活动
   *
   */
  static query(body: CardBalancePromotionActivityFilter): Promise<Response<CardBalancePromotionActivityQueryResult>> {
    return ApiClient.server().post(`/v1/card-balance-promotion/query`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 删除活动
   *
   */
  static remove(activityId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/card-balance-promotion/remove/${activityId}`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 保存并审核活动
   *
   */
  static saveAndAudit(body: CardBalancePromotionActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/card-balance-promotion/saveAndAudit`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 终止活动
   *
   */
  static stop(activityId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/card-balance-promotion/stop/${activityId}`, {}, {}).then((res) => {
      return res.data
    })
  }

}
