/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2023-09-13 09:57:05
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\common\ExchangeGoodsCouponAttribute.ts
 * 记得注释
 */
import BPickUpGoodsGroup from './BPickUpGoodsGroup'

export default class ExchangeGoodsCouponAttribute {
  // 是否限制商品范围
  limitGoods: Nullable<boolean> = true
  // 兑换商品组
  pickUpGoodsGroups: BPickUpGoodsGroup[] = []
  // 赠品商品组
  giftGoodsGroups: BPickUpGoodsGroup[] = []
  //券后价
  afterUseAmount: Nullable<number> = 0
}