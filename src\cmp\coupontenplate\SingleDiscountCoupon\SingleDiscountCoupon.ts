import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import CouponItem from "model/common/CouponItem";
import AmountToFixUtil from "util/AmountToFixUtil";
import TimeRange from "cmp/coupontenplate/cmp/TimeRange.vue";
import GoodsScopeEx from "cmp/goodsscope/GoodsScopeEx.vue";
import GoodsSelectorDialog from "cmp/selectordialogs/GoodsSelectorDialog.vue";
import UseCouponStep from "cmp/coupontenplate/cmp/UseCouponStep.vue";
import CouponBear from "cmp/coupontenplate/cmp/CouponBear.vue";
import CouponInfo from "model/common/CouponInfo";
import ValidityInfo from "model/common/ValidityInfo";
import DateTimeRange from "model/common/DateTimeRange";
import SubjectApportion from "model/common/SubjectApportion";
import DiscountCouponAttribute from "model/common/DiscountCouponAttribute";
import CouponThreshold from "model/common/CouponThreshold";
import StoreRange from "model/common/StoreRange";
import GoodsRangeView from "cmp/coupontenplate/cmp/GoodsRange.vue";
import RSGoods from "model/common/RSGoods";
import IdName from "model/entity/IdName";
import GoodsRange from "model/common/GoodsRange";
import DateUtil from "util/DateUtil";
import CostParty from "model/common/CostParty";
import ChannelRange from "model/common/ChannelRange";
import CouponInitialApi from "http/v2/coupon/init/CouponInitialApi";
import RSCostPartyFilter from "model/common/RSCostPartyFilter";
import CostPartyApi from "http/costparty/CostPartyApi";
import GroupMutexTemplate from "cmp/coupontenplate/cmp/GroupMutexTemplate";
import GroupMutexTemplateData from "cmp/coupontenplate/cmp/GroupMutexTemplateData";
import CouponTemplateLogo from "cmp/coupontenplate/cmp/CouponTemplateLogo";
import ActiveStore from "cmp/activestore/ActiveStore";
import ImportDialog from "cmp/importdialogs/ImportDialog.vue";
import I18nTool from "common/I18nTool";
import EnvUtil from "util/EnvUtil";
import CouponConfig from 'model/v2/coupon/init/CouponConfig'

class CostPartyDetail {
  // 承担方
  party: Nullable<string> = null
  // 承担百分比/金额
  value: Nullable<number> = null
}
class GoodsScopeFormRow {
	cond: string = "";
	text: string = "";
	items: any[] = [];
}
@Component({
	name: "SingleDiscountCoupon",
	components: {
		TimeRange,
		GoodsScopeEx,
		UseCouponStep,
		CouponBear,
		ActiveStore,
		GoodsRangeView,
		GoodsSelectorDialog,
		GroupMutexTemplate,
		CouponTemplateLogo,
		ImportDialog,
	},
})
export default class SingleDiscountCoupon extends Vue {
	dtl: CouponConfig = new CouponConfig()
	singleGoods: any[] = [];
	ruleForm: any = {
		discount: "",
		name: "",
		dateType: "RALATIVE",
		dateFrom: "",
		dateTo: "",
		dateFix: "",
		useDate: "",
		storeRange: "{}",
		useCouponGood: "",
		promotion: true,
		recordWay: "FAV",
		discountWay: "",
		payWay: "",
		couponOrder: "",
		couponGoodsDesc: "",
		couponProduct: "",
		otherStep: "",
		otherStepValue: "",
		time: "",
		couponUnder: {},
		templateId: "",
		useFrom: "step2",
		from: [],
		sychChannel: null,
		logoUrl: "",
		groupMutex: new GroupMutexTemplateData(),
		prefix: "",
		state: "",
		transferable: true, // 是否可转赠
	};
	$refs: any;
	rules: any = {};
	curState = "";
	importNumber: number = 1;
	getImportUrl: any = "/v1/goods/importGoodDiscountExcel";
	templatePath: any = "template_single_discount_goods.xlsx";
	isClear: Boolean = true;

	@Prop()
	sameStore: boolean; // 与活动门店一致
	@Prop()
	state: string;
	@Prop()
	channels: any;
	@Prop()
	value: CouponItem;
	@Prop({
		type: Boolean,
		default: false,
	})
	baseSettingFlag: boolean;
	@Prop({
		type: String,
		default: "add",
	})
	copyFlag: string;

	@Prop({
		type: Boolean,
		default: false,
	})
	baseFieldEditable: false; // 叠加促销、用券记录方式、券承担方、用券顺序 是否可编辑
	@Prop({
		type: String,
		default: '400'
	})
	remarkMaxlength: string
	parties: any = [];

	// 是否是复制\新建\编辑
	@Watch("state")
	onStateChange(value: string) {
		this.curState = value;
	}

	get dateRangeOption() {
		return {
			disabledDate(time: any) {
				return time.getTime() < DateUtil.nowDayTime();
			},
		};
	}

	get remarkPlaceholder() {
		let str = this.formatI18n('/营销/积分活动/积分活动/积分兑换券/编辑页面', '请输入不超过{0}个字符')
		return str.replace(/\{0\}/g, this.remarkMaxlength);
	  }

	created() {
		this.rules = {
			useCouponGood: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
			otherStep: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
			otherStepValue: [
				{
					required: true,
					validator: (rule: any, value: any, callback: any) => {
						if (value) {
							if (Number(value) > Number(this.ruleForm.otherStep)) {
								callback(this.formatI18n("/公用/券模板/单品折扣券/用券门槛", "优惠数量必须小于门槛数量"));
							} else {
								callback();
							}
						} else {
							callback(this.formatI18n("/公用/券模板", "请输入必填项"));
						}
					},
					trigger: "blur",
				},
			],
			discount: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
			dateFrom: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
			dateTo: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
			dateFix: [
				// { required: true, message: this.formatI18n('/公用/券模板', '请输入必填项'), trigger: 'blur' },
				{
					required: true,
					validator: (rule: any, value: any, callback: any) => {
						if (value && value.length > 0 && value[0] !== "--") {
							let start = DateUtil.parseDate(DateUtil.format(DateUtil.parseDate(value[0]), "yyyy-MM-dd")).getTime();
							let end = DateUtil.parseDate(DateUtil.format(DateUtil.parseDate(value[1]), "yyyy-MM-dd")).getTime();
							let today = DateUtil.parseDate(DateUtil.format(new Date(), "yyyy-MM-dd")).getTime();
							if (start < today) {
								callback(new Error(this.formatI18n("/营销/券礼包活动/核销第三方券", "开始时间不允许小于今天") as string));
							} else if (start > end) {
								callback(new Error(this.formatI18n("/营销/券礼包活动/核销第三方券", "开始时间不允许大于结束时间") as string));
							} else {
								callback();
							}
						} else {
							callback(new Error(this.formatI18n("/公用/券模板", "请输入必填项") as string));
						}
					},
					trigger: "blur",
				},
			],
			couponUnder: [
				{
					validator: (rule: any, value: any, callback: any) => {
						if (value) {
              if (value.bearType == 'unset') {
                callback();
              }
							if (value.costPartyDetails && value.costPartyDetails.length <= 0) {
                if (value.bearType == 'unset') {
                  callback();
                } else {
                  callback('请选择承运商');
                }
							} else {
								let flag = false;
                let count: number = 0
                if (value.bearType == 'PROPORTION') {
                  // 按比例
                  (value.costPartyDetails || []).forEach((item: CostPartyDetail) => {
                    if (!item.value) {
                      flag = true;
                    } else {
                      count += parseFloat(item.value as any)
                    }
                    if (!item.party) {
                      flag = true;
                    }
                  });

                } else if (value.bearType == 'AMOUNT') {
                  // 按金额
                  (value.costPartyDetails || []).forEach((item: CostPartyDetail, index: number) => {
                    if (!item.value && value.amountType == 'part' && (index != value.costPartyDetails.length - 1 || index == 0)) {
                      flag = true;
                    }
                    if (!item.party) {
                      flag = true;
                    }
                  });
                } else {
                  // 不设置
                  flag = false;
                }
								if (flag) {
									callback(this.formatI18n("/公用/券模板", "请输入必填项"));
								} else {
                  if (value.bearType == 'PROPORTION') {
                    if (count != 100) {
                      callback('所有承担方合计承担100%，请重新填写');
                    } else {
                      callback();
                    } 
                  } else {
                    callback();
                  }
								}
							}
						}
					},
					trigger: "blur",
				},
			],
			useFrom: [
				{
					validator: (rule: any, value: any, callback: any) => {
						if (value) {
							if (value === "step1") {
								callback();
							} else {
								if (this.ruleForm.from && this.ruleForm.from.length > 0) {
									callback();
								} else {
									callback(this.formatI18n("/公用/券模板", "请输入必填项"));
								}
							}
						}
					},
					trigger: "blur",
				},
			],
			couponOrder: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
			couponProduct: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
			discountWay: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
			payWay: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
			prefix: [
				{
					validator: (rule: any, value: any, callback: any) => {
						let re = /^[0-9a-zA-Z]*$/g; // 判断字符串是否为数字和字母组合
						if (!re.test(value)) {
							callback(this.formatI18n("/资料/渠道", "请输入数字或字母"));
						} else {
							callback();
						}
					},
					tirgger: "blur",
				},
			],
		};
		if (this.state) {
			this.curState = this.state;
		}
		if (this.value && this.value.coupons && this.value.coupons.couponBasicType === "goods_discount" && this.value.coupons.remark) {
			this.doBindValue(JSON.parse(JSON.stringify(this.value)));
		}
		if (this.copyFlag) {
			this.getCostParty();
		}
		if (!this.$route.query.id) {
			// 新建时使用配置的用券记录方式
			this.getPayWayDtl();
		}
		//this.getCouponPrefix("discount");
	}

	private getCouponPrefix(type: string){
		if (["edit", "copy"].includes(this.copyFlag)) {
			return;
		}
		CouponInitialApi.get().then((resp: any) => {
		  if (resp && resp.code === 2000) {
			this.dtl = resp.data;
			if(!this.dtl || !this.dtl.couponCodePrefixes) {
				return "";
			  }
			  const coupon = this.dtl.couponCodePrefixes.find(
				item => item.couponType === type
			);
			this.ruleForm.prefix = coupon ? coupon.prefix : "";
		  }
		})
	}



	getCostPartr(party: any, percent: any) {
		let str: any = this.formatI18n(
			"/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送券的时候/点击券名称/券承担方",
			"{0}承担用券金额{1}%"
		);
		str = str.replace(/\{0\}/g, this.getPartyNameById(party));
		str = str.replace(/\{1\}/g, `&nbsp;<span style="font-weight: bold">${percent}</span>&nbsp;`);
		return str;
	}

	getPartyNameById(id: string) {
		let str = "";
		if (this.parties && this.parties.length > 0) {
			this.parties.forEach((item: any) => {
				if (item.costParty.id === id) {
					str = item.costParty.name;
				}
			});
		}
		return str;
	}

	doSingleCountDiscount() {
		this.ruleForm.otherStep = AmountToFixUtil.formatNumber(this.ruleForm.otherStep, 99999999, 1);
		this.$emit("input", this.doTransParams());
		this.$emit("change", this.channels);
	}

	doSingleCountDiscountValue() {
		this.ruleForm.otherStepValue = AmountToFixUtil.formatNumber(this.ruleForm.otherStepValue, 99999999, 1);
		this.$emit("input", this.doTransParams());
		this.$emit("change", this.channels);
	}

	getDiscountRate(discountRate: number) {
		// scope.row.discountRate > 0 ? scope.row.discountRate.toFixed(1) + '折' : '无折扣'
		let str: any = "";
		str = this.formatI18n("/会员/等级/等级管理/已初始化状态的免费等级/表格/折扣", "{0}折");
		str = str.replace(/\{0\}/g, discountRate);
		return str;
	}

	logoUrlCallBack(url: any) {
		this.ruleForm.logoUrl = url;
		this.$emit("input", this.doTransParams());
		this.$emit("change", this.channels);
	}

	doFocus() {
		if (this.ruleForm.useCouponGood) {
			let arr: RSGoods[] = [];
			let subItem = this.ruleForm.useCouponGood.split(";");
			if (subItem && subItem.length > 0) {
				subItem.forEach((item: any) => {
					if (item) {
						let child = item.split("]");
						let idName: RSGoods = new RSGoods();
						idName.barcode = child[0].substring(1, child[0].length);
						idName.name = child[1];
						arr.push(idName);
					}
				});
			}
			console.log(arr);
			arr = arr.filter((item, index) => {
				undefined;

				return arr.findIndex((item1) => item1.barcode == item.barcode) == index;
			});
			this.$refs.goodsSelect.open(arr, "multiple");
		} else {
			this.$refs.goodsSelect.open([], "multiple");
		}
	}

	doGoodsSelectSummit(arr: RSGoods[]) {
		console.log(arr);
		this.singleGoods = [];
		let str = "";
		if (arr && arr.length > 0) {
			arr.forEach((item: RSGoods) => {
				str += `[${item.barcode}]${item.name};`;
				let idName: IdName = new IdName();
				idName.id = item.barcode;
				idName.name = item.name;
				this.singleGoods.push(idName);
			});
		}
		this.doUseCouponGoodsChange();
		this.ruleForm.useCouponGood = str;
		this.$refs.ruleForm.validateField("useCouponGood");
	}

	getFaceAmount(amount: number) {
		let str: any = this.formatI18n("/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级套餐", "{0}元");
		str = str.replace(/\{0\}/g, Number(amount).toFixed(2));
		return str;
	}

	getGoodsDiscount(threshold: any, discount: any, thresholdValue: any) {
		let str: any = this.formatI18n(
			"/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送单品折扣券的时候/用券门槛/用券商品满{0}件可享受其中{1}件{2}折"
		);
		str = str.replace(/\{0\}/g, `&nbsp;<span style="font-weight: bold">${threshold}</span>&nbsp;`);
		str = str.replace(/\{1\}/g, `&nbsp;<span style="font-weight: bold">${thresholdValue}</span>&nbsp;`);
		str = str.replace(/\{2\}/g, `&nbsp;<span style="font-weight: bold">${discount}</span>&nbsp;`);
		return str;
	}

	doDateFocus() {
		this.ruleForm.dateFix = [DateUtil.format(new Date(), "yyyy-MM-dd"), DateUtil.format(new Date(), "yyyy-MM-dd")];
	}

	doSychChannelChange(){
		console.log('ruleForm.sychChannel', this.ruleForm.sychChannel)
		this.$refs.ruleForm.validateField('useFrom')
		this.$emit('input', this.doTransParams())
		this.$emit('change', this.channels)
	   }
	

	doTimeChange() {
		this.$emit("input", this.doTransParams());
		this.$emit("change", this.channels);
	}

	doFromChange() {
		this.$refs.ruleForm.validateField("useFrom");
		this.$emit("input", this.doTransParams());
		this.$emit("change", this.channels);
	}

	doDiscount() {
		this.ruleForm.discount = AmountToFixUtil.formatNumberWithInteger(this.ruleForm.discount, 9.9, 0, 1);
		if (this.ruleForm.discount === "0.0") {
			this.ruleForm.discount = 0;
		}
		this.$emit("input", this.doTransParams());
		this.$emit("change", this.channels);
	}

	doNameChange() {
		this.$emit("input", this.doTransParams());
		this.$emit("change", this.channels);
	}

	doPrefixChange() {
		this.$emit("input", this.doTransParams());
		this.$emit("change", this.channels);
	}

	doCouponValidateChange() {
		if (this.ruleForm.dateType === "RALATIVE") {
			this.ruleForm.dateFix = [];
		} else {
			this.ruleForm.dateFrom = "";
			this.ruleForm.dateTo = "";
		}
	}

	getAllCash(threshold: any) {
		let str: any = this.formatI18n(
			"/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送全场现金券的时候/用券门槛/可叠加使用",
			"可叠加使用，全场消费每满{0}元可用1张券"
		);
		str = str.replace(/\{0\}/g, threshold);
		return str;
	}

	getFavValue(favValue: any, payValue: any) {
		let str: any = this.formatI18n(
			"/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送券的时候/点击券名称/用券记录方式为组合方式",
			"{0}%优惠方式+{1}%支付方式"
		);
		str = str.replace(/\{0\}/g, `&nbsp;<span style="font-weight: bold">${favValue}</span>&nbsp;`);
		str = str.replace(/\{1\}/g, `&nbsp;<span style="font-weight: bold">${payValue}</span>&nbsp;`);
		return str;
	}

	getAllCashNo(threshold: any) {
		let str: any = this.formatI18n(
			"/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送全场现金券的时候/用券门槛/不可叠加使用",
			"不可叠加使用，全场消费满{0}元及以上可用1张券"
		);
		str = str.replace(/\{0\}/g, threshold);
		return str;
	}

	doCouponChange(type: number) {
		if (type === 1) {
			this.ruleForm.dateTo = AmountToFixUtil.formatNumber(this.ruleForm.dateTo, 36500, 1);
		} else {
			this.ruleForm.dateFrom = AmountToFixUtil.formatNumber(this.ruleForm.dateFrom, 365, 0);
		}
		this.$emit("input", this.doTransParams());
		this.$emit("change", this.channels);
	}

	doDateFixChange() {
		this.$emit("input", this.doTransParams());
		this.$emit("change", this.channels);
	}

	doTimeParam(value: any) {
		this.$emit("input", this.doTransParams());
		this.$emit("change", this.channels);
	}

	doPromotionChange() {
		this.$emit("input", this.doTransParams());
		this.$emit("change", this.channels);
	}

	doTransferableChange() {
		this.$emit("input", this.doTransParams());
		this.$emit("change", this.channels);
	}

	doStepChange() {
		this.$emit("input", this.doTransParams());
		this.$emit("change", this.channels);
	}

	doGoodsRange() {
		this.$emit("input", this.doTransParams());
		this.$emit("change", this.channels);
	}

	doStoreChange() {
		this.$emit("input", this.doTransParams());
		this.$emit("change", this.channels);
		this.$refs.ruleForm.validateField("storeRange");
		// this.$forceUpdate()
	}

	doUseFromChange() {
		if (this.ruleForm.useFrom === "step1") {
			this.ruleForm.from = [];
			this.$emit("input", this.doTransParams());
			this.$emit("change", this.channels);
		}
		this.$refs.ruleForm.validateField("useFrom");
	}

	doRecordWayChange() {
		if (this.ruleForm.recordWay === "COLLOCATION") {
			this.ruleForm.discountWay = Number(100).toFixed(2);
			this.ruleForm.payWay = Number(0).toFixed(2);
		} else {
			this.ruleForm.discountWay = "";
			this.ruleForm.payWay = "";
		}
		this.$emit("input", this.doTransParams());
		this.$emit("change", this.channels);
	}

	doDiscountWay() {
		this.ruleForm.discountWay = AmountToFixUtil.formatAmount(this.ruleForm.discountWay, 100, 0, "");
		this.ruleForm.payWay = (100 - Number(this.ruleForm.discountWay)).toFixed(2);
		this.$emit("input", this.doTransParams());
		this.$emit("change", this.channels);
	}

	doPayWay() {
		this.ruleForm.payWay = AmountToFixUtil.formatAmount(this.ruleForm.payWay, 100, 0, "");
		this.ruleForm.discountWay = (100 - Number(this.ruleForm.payWay)).toFixed(2);
		this.$emit("input", this.doTransParams());
		this.$emit("change", this.channels);
	}

	doBearChange() {
		this.$emit("input", this.doTransParams());
		this.$emit("change", this.channels);
	}

	doUseCouponGoodsChange() {
		this.$emit("input", this.doTransParams());
		this.$emit("change", this.channels);
	}

	doThisOrder() {
		this.ruleForm.couponOrder = AmountToFixUtil.formatNumber(this.ruleForm.couponOrder, 99, 1);
		this.$emit("input", this.doTransParams());
		this.$emit("change", this.channels);
	}

	doRemarkChange() {
		this.$emit("input", this.doTransParams());
		this.$emit("change", this.channels);
	}

	doGoodsRemarkChange() {
		this.$emit("input", this.doTransParams());
		this.$emit("change", this.channels);
	}

	doSingleDiscountMutexTemplate() {
		this.$emit("input", this.doTransParams());
		this.$emit("change", this.channels);
	}

	getAllDiscountCoupon(threshold: any) {
		let str: any = this.formatI18n(
			"/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送全场折扣券的时候/用券门槛",
			"全场消费满{0}元及以上可以用1张券"
		);
		str = str.replace(/\{0\}/g, threshold);
		return str;
	}

	doValidate() {
		let arr: any = [];
		let p0 = new Promise((resolve, reject) => {
			this.$refs.ruleForm.validate((valid: any) => {
				if (valid) {
					resolve(null);
				}
			});
		});
		arr.push(this.$refs.activeStore.validate());
		arr.push(p0);
    // 券承担方
    if (this.$refs.CouponBear) {
      arr.push(this.$refs.CouponBear.formValiPromise())
    }
		// 用券门槛
		// 用券时段
		if (this.$refs.timeRange) {
			let p2 = this.$refs.timeRange.doValidate();
			arr.push(p2);
		}

		// 券叠加组
		if (this.$refs.singleDiscountMutexTemplate) {
			arr.push(this.$refs.singleDiscountMutexTemplate.doValidate());
		}
		return arr;
	}

	private doTransParams() {
		let params: CouponItem = new CouponItem();
		params.coupons = new CouponInfo();
		params.coupons.couponBasicType = "goods_discount" as any;
		params.coupons.name = this.ruleForm.name;
		params.coupons.templateId = this.ruleForm.templateId;
		params.coupons.discountCouponAttribute = new DiscountCouponAttribute();
		params.coupons.discountCouponAttribute.discount = this.ruleForm.discount;
		// 券有效期
		params.coupons.validityInfo = new ValidityInfo();
		params.coupons.validityInfo.validityType = this.ruleForm.dateType;
		if (this.ruleForm.dateType === "RALATIVE") {
			params.coupons.validityInfo.delayEffectDays = this.ruleForm.dateFrom;
			params.coupons.validityInfo.validityDays = this.ruleForm.dateTo;
		} else {
			if (this.ruleForm.dateFix && this.ruleForm.dateFix[0]) {
				params.coupons.validityInfo.beginDate = (this.ruleForm.dateFix[0] + " 00:00:00") as any;
			}
			if (this.ruleForm.dateFix && this.ruleForm.dateFix[1]) {
				params.coupons.validityInfo.endDate = (this.ruleForm.dateFix[1] + " 23:59:59") as any;
			}
		}
		// 用券时段
		params.coupons.useTimeRange = new DateTimeRange();
		if (this.ruleForm.time) {
			// params.coupons.useTimeRange = this.timeTemplate
			params.coupons.useTimeRange = this.ruleForm.time;
		} else {
			params.coupons.useTimeRange = new DateTimeRange();
			params.coupons.useTimeRange.dateTimeRangeType = "ALL" as any;
		}
		// todo 用券渠道
		params.coupons.useChannels = new ChannelRange();
		if (this.ruleForm.useFrom === "step1") {
			params.coupons.useChannels.channelRangeType = "ALL" as any;
			params.coupons.useChannels.channels = [];
		} else {
			params.coupons.useChannels.channelRangeType = "PART" as any;
			params.coupons.useChannels.channels = this.ruleForm.from;
		}
		//同步渠道  
		params.coupons.sychChannel = this.ruleForm.sychChannel
		// 用券门店
		if (this.ruleForm.storeRange === "{}") {
			let storeRange: StoreRange = new StoreRange();
			if (this.sameStore) {
				storeRange.storeRangeType = "SAME" as any;
			} else {
				storeRange.storeRangeType = "ALL" as any;
			}
			params.coupons.useStores = storeRange;
		} else {
			params.coupons.useStores = this.ruleForm.storeRange;
		}
		// 用券商品
		params.coupons.useGoods = new GoodsRange();
		params.coupons.useGoods.limit = true;
		params.coupons.useGoods.includeGoods = this.singleGoods;
		// 用券门槛
		params.coupons.useThreshold = new CouponThreshold();
		params.coupons.useThreshold.threshold = this.ruleForm.otherStep;
		params.coupons.useThreshold.thresholdType = "NONREUSEABLE" as any;
		params.coupons.useThreshold.value = this.ruleForm.otherStepValue;
		// 叠加促销
		params.coupons.excludePromotion = this.ruleForm.promotion;
		// 是否支持转赠
		params.coupons.transferable = this.ruleForm.transferable;
		// 用券记录方式
		params.coupons.useApporion = new SubjectApportion();
		params.coupons.useApporion.subjectApprotionType = this.ruleForm.recordWay;
		if (this.ruleForm.recordWay === "COLLOCATION") {
			params.coupons.useApporion.favValue = this.ruleForm.discountWay;
			params.coupons.useApporion.payValue = this.ruleForm.payWay;
		}
		// 券承担方
		// params.coupons.costParties = [];
		// params.coupons.costParties = this.ruleForm.couponUnder;
    ;(params.coupons.costParty as any) = {};
    if (this.ruleForm.couponUnder.bearType == 'unset') {
      this.ruleForm.couponUnder.bearType = null
      this.ruleForm.couponUnder.amountType = null
      this.ruleForm.couponUnder.costPartyDetails = null
    }
		params.coupons.costParty = this.ruleForm.couponUnder;
		// 用券顺序
		params.coupons.priority = this.ruleForm.couponOrder;
		// 用券商品说明
		params.coupons.goodsRemark = this.ruleForm.couponGoodsDesc;
		// 用券说明
		params.coupons.remark = this.ruleForm.couponProduct;
		// 券叠加促销
		params.coupons.groupMutexFlag = this.ruleForm.groupMutex.groupMutexFlag;
		params.coupons.groupMutexTemplates = this.ruleForm.groupMutex.groupMutexTemplates;
		// 券logo
		params.coupons.logoUrl = this.ruleForm.logoUrl;
		// 券码前缀
		params.coupons.codePrefix = this.ruleForm.prefix;
		return params;
	}

	private doBindValue(value: CouponItem) {
		if (value && value.coupons) {
			let coupon: CouponInfo = value.coupons;
			this.ruleForm.name = coupon.name;
			this.ruleForm.templateId = coupon.templateId;
			this.ruleForm.state = coupon.state
			this.ruleForm.discount = coupon.discountCouponAttribute!.discount;
			this.ruleForm.dateType = coupon.validityInfo!.validityType;
			if (this.ruleForm.dateType === "RALATIVE") {
				this.ruleForm.dateFrom = coupon.validityInfo!.delayEffectDays;
				this.ruleForm.dateTo = coupon.validityInfo!.validityDays;
			} else {
				this.ruleForm.dateFix = [
					DateUtil.format(coupon.validityInfo!.beginDate, "yyyy-MM-dd"),
					DateUtil.format(coupon.validityInfo!.endDate, "yyyy-MM-dd"),
				];
			}
			this.ruleForm.sychChannel = coupon.sychChannel
			// todo 用券渠道
			if (coupon.useChannels && coupon.templateId) {
				if (coupon.useChannels.channelRangeType === "ALL") {
					this.ruleForm.useFrom = "step1";
					this.ruleForm.from = [];
				} else {
					this.ruleForm.useFrom = "step2";
					if (coupon.useChannels.channels && coupon.useChannels.channels.length > 0) {
						let arrs: string[] = [];
						coupon.useChannels.channels.forEach((item: any) => {
							if (item.id || item.type) {
								if (item.id && item.id !== "-") {
									arrs.push(item.type + item.id);
								} else {
									arrs.push(item.type);
								}
							} else {
								arrs.push(item);
							}
						});
						this.ruleForm.from = arrs;
					}
				}
			}
			// 用券时段
			// this.ruleForm.useDate = coupon.useTimeRange!.dateTimeRangeType
			this.$nextTick(() => {
				if (coupon.useTimeRange && coupon.useTimeRange.beginTime === null) {
					coupon.useTimeRange.beginTime = "";
				}
				if (coupon.useTimeRange && coupon.useTimeRange.endTime === null) {
					coupon.useTimeRange.endTime = "";
				}
				this.ruleForm.time = coupon.useTimeRange;
			});
			// 用券门店
			this.ruleForm.storeRange = coupon.useStores;
			// 用券商品
			let str = "";
			this.singleGoods = [];
			if (coupon.useGoods && coupon.useGoods.includeGoods && coupon.useGoods.includeGoods.length > 0) {
				coupon.useGoods.includeGoods.forEach((item: IdName) => {
					str += `[${item.id}]${item.name};`;
					this.singleGoods.push(item);
				});
			}
			this.ruleForm.useCouponGood = str;
			// 用券门槛
			this.ruleForm.otherStep = coupon.useThreshold!.threshold;
			this.ruleForm.otherStepValue = coupon.useThreshold!.value;
			// 叠加促销
			if (coupon.excludePromotion || coupon.excludePromotion === false) {
				this.ruleForm.promotion = coupon.excludePromotion;
			} else {
				this.ruleForm.promotion = true;
			}

			// 用券记录方式
			if (coupon.useApporion && coupon.useApporion!.subjectApprotionType) {
				this.ruleForm.recordWay = coupon.useApporion!.subjectApprotionType;
			} else {
				this.ruleForm.recordWay = "FAV";
			}

			if (this.ruleForm.recordWay === "COLLOCATION") {
				this.ruleForm.discountWay = coupon.useApporion!.favValue;
				this.ruleForm.payWay = coupon.useApporion!.payValue;
			}
			// 券承担方
			// this.ruleForm.couponUnder = coupon.costParties;
      this.ruleForm.couponUnder = coupon.costParty;
			// 用券顺序
			this.ruleForm.couponOrder = coupon.priority;
			// 用券商品说明
			this.ruleForm.couponGoodsDesc = coupon.goodsRemark;
			// 用券说明
			this.ruleForm.couponProduct = coupon.remark;
			// 券logo
			this.ruleForm.logoUrl = coupon.logoUrl;
			// 券码前缀
			this.ruleForm.prefix = coupon.codePrefix;
			// 是否可以叠加用券
			this.$nextTick(() => this.$refs.singleDiscountMutexTemplate.initValue2(coupon, this.copyFlag));
			// 是否支持转赠
			this.ruleForm.transferable = coupon.transferable;
		}
	}

	private getPayWayDtl() {
		CouponInitialApi.get().then((resp: any) => {
			if (resp && resp.code === 2000) {
				if (resp.data && resp.data.subjectApportion === "pay") {
					this.ruleForm.recordWay = "PAY";
        } else if (resp.data && resp.data.subjectApportion === "fav") {
          this.ruleForm.recordWay = "FAV";
        } else if (resp.data && resp.data.subjectApportion === "collection") {
          this.ruleForm.recordWay = "COLLOCATION";
          this.ruleForm.recordType = "AMOUNT"
          this.ruleForm.payWay = 0
        }
			}
		});
	}

	private getCostParty() {
		let params: RSCostPartyFilter = new RSCostPartyFilter();
		params.page = 0;
		params.pageSize = 0;
		CostPartyApi.query(params)
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					this.parties = resp.data;
				}
			})
			.catch((error: any) => {
				this.$message.error(error.message);
			});
	}

	doImport() {
		this.$refs.importDialog.show();
	}

	doUploadSuccess(res: any) {
		if (res.response.code === 2000) {
			console.log(res.response.data);
			this.parseGoods(res.response.data.includeGoods);
			this.doUseCouponGoodsChange();
		} else {
			this.$message.error(res.response.msg);
		}
	}

	private parseGoods(items: any[]) {
		let row = new GoodsScopeFormRow();
		row.cond = I18nTool.match("/公用/券模板/单品") + (EnvUtil.isZh_Cn() ? "" : " ") + I18nTool.match("/公用/券模板/属于");
		items.forEach((item: any) => {
			let str = "[" + item.id + "]" + item.name;
			row.text += str + ";";
			let ins: any = {
				id: item.id,
				name: item.name,
			};
			row.items.push(ins);
		});
		if (this.isClear) {
			this.ruleForm.useCouponGood = row.text;
			this.singleGoods = row.items;
		} else {
			this.ruleForm.useCouponGood += row.text;
			if (row.items) {
				this.singleGoods = this.singleGoods.concat(row.items);
			}
		}
	}

	clearChange(isClear: Boolean) {
		this.isClear = isClear;
	}

	doClear() {
		this.ruleForm.useCouponGood = ''
	}
}
