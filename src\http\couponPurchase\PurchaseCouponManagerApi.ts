/*
 * @Autor: 司浩
 * @Description:
 * @Date: 2023-01-09 13:31:39
 * @LastEditors: 苏国友 <EMAIL>
 * @LastEditTime: 2023-05-05 17:26:55
 * @FilePath: /phoenix-web-ui/src/http/couponPurchase/PurchaseCouponManagerApi.ts
 */
import ApiClient from 'http/ApiClient'
import BPurchaseCouponFilter from 'model/couponPurchase/BPurchaseCouponFilter'
import BPurchaseCouponTrade from 'model/couponPurchase/BPurchaseCouponTrade'
import PurchaseCouponManagerAuditRequest from 'model/couponPurchase/PurchaseCouponManagerAuditRequest'
import Response from 'model/common/Response'
import MutableNsid from 'model/common/MutableNsid'
import BPurchaseCouponTradeLog from 'model/couponPurchase/BPurchaseCouponTradeLog'
import BPurchaseCouponStats from 'model/couponPurchase/BPurchaseCouponStats'
import BSystemRefundResponse from 'model/couponPurchase/BSystemRefundResponse'
import AddMaterialProofRequest from 'model/couponPurchase/AddMaterialProofRequest'
import PurchaseQueryLogRequest from 'model/couponPurchase/PurchaseQueryLogRequest'

export default class PurchaseCouponManagerApi {
  /**
   * 审核
   * 审核
   *
   */
    static batchAudit(
      body: PurchaseCouponManagerAuditRequest
    ): Promise<Response<string>> {
      return ApiClient.server()
        .post(`/v1/purchase-coupon-manager/batchAudit`, body, {})
        .then((res) => {
          return res.data
        })
    }
  /**
   * 审核
   * 审核
   *
   */
  static audit(
    body: PurchaseCouponManagerAuditRequest
  ): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/purchase-coupon-manager/audit`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 退款
   *
   */
  static refund(
    body: PurchaseCouponManagerAuditRequest
  ): Promise<Response<void>> {
    return ApiClient.server()
      .post(`/v1/purchase-coupon-manager/system/refund`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 购券交易查询
   * 购券交易查询
   *
   */
  static query(
    body: BPurchaseCouponFilter
  ): Promise<Response<BPurchaseCouponTrade[]>> {
    return ApiClient.server()
      .post(`/v1/purchase-coupon-manager/query`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 购券交易导出
   * 购券交易导出
   *
   */
  static export(body: BPurchaseCouponFilter): Promise<Response<any>> {
    return ApiClient.server()
      .post(`/v1/purchase-coupon-manager/export`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 购券交易详情
   * 购券交易详情
   *
   */
  static detail(body: MutableNsid): Promise<Response<BPurchaseCouponTrade>> {
    return ApiClient.server()
      .post(`/v1/purchase-coupon-manager/detail`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 操作日志
   * 操作日志
   *
   */
  static log(
    body: PurchaseQueryLogRequest
  ): Promise<Response<BPurchaseCouponTradeLog[]>> {
    return ApiClient.server()
      .post(`/v1/purchase-coupon-manager/queryLog`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 售后单退款总条数，应退金额
   * 售后单退款总条数，应退金额
   *
   */
  static stats(
    body: BPurchaseCouponFilter
  ): Promise<Response<BPurchaseCouponStats>> {
    return ApiClient.server()
      .post(`/v1/purchase-coupon-manager/stats`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 获取应退金额
   * 获取应退金额
   */
  static returnPrepare(
    body: PurchaseCouponManagerAuditRequest
  ): Promise<Response<BSystemRefundResponse[]>> {
    return ApiClient.server()
      .post(`/v1/purchase-coupon-manager/system/returnPrepare`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 纠纷商家留言
   * 纠纷商家留言
   */
  static addMerchantMaterial(
    body: AddMaterialProofRequest
  ): Promise<Response<void>> {
    return ApiClient.server()
      .post(`/v1/purchase-coupon-manager/complaint/addMaterial`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 纠纷商家举证
   * 纠纷商家举证
   */
  static addProof(body: AddMaterialProofRequest): Promise<Response<void>> {
    return ApiClient.server()
      .post(`/v1/purchase-coupon-manager/complaint/addProof`, body, {})
      .then((res) => {
        return res.data
      })
  }
}
