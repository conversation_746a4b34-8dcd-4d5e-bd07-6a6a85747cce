/*
 * @Author: 黎钰龙
 * @Date: 2025-05-23 13:54:46
 * @LastEditTime: 2025-05-26 16:15:02
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\template\BalanceCodeDecorationModal.ts
 * 记得注释
 */
import bigWheelImageLink from '../BigWheelImageUrlLink'
import { BigWheelImageVO } from '../BigWheelImageVO'

export default class BalanceCodeDecorationModal {
  id: string = 'depositPayCode'
  uuid: Nullable<string> = ""
  // 组件名称
  name: Nullable<string> = '储值支付'
  // 背景图
  propBackgroundImage: Nullable<string> = bigWheelImageLink[BigWheelImageVO.bg_huiyuanma]
  // LOGO
  propLogo: Nullable<string> = bigWheelImageLink[BigWheelImageVO.default_avatar]
  // 其他支付方式 积分-point 储值-balance
  propOtherPayTypes: string[] = ['point']
}