/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:19
 * @LastEditTime: 2024-01-19 10:08:21
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\card\template\CardTemplateApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import CardTemplate from 'model/card/template/CardTemplate'
import CardTemplateFilter from 'model/card/template/CardTemplateFilter'
import CardTemplateQueryResult from 'model/card/template/CardTemplateQueryResult'
import Response from 'model/common/Response'
import PromotionBill from 'model/coupon/template/PromotionBill'

export default class CardTemplateApi {
  /**
   * 分配模板号
   *
   */
  static allocateTemplateNumber(): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/card-template/allocateTemplateNumber`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询卡模板
   *
   */
  static info(num: string): Promise<Response<CardTemplate>> {
    return ApiClient.server().get(`/v1/card-template/info/${num}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改卡模板
   *
   */
  static modify(body: CardTemplate): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/card-template/modify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询卡模板
   *
   */
  static query(body: CardTemplateFilter): Promise<Response<CardTemplateQueryResult>> {
    return ApiClient.server().post(`/v1/card-template/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建卡模板
   *
   */
  static saveNew(body: CardTemplate): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/card-template/saveNew`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 卡模板促销单查询
  * 
  */
  static getCardTemplatePromotion(templateNumber: string): Promise<Response<PromotionBill[]>> {
    return ApiClient.server().post(`/v1/card-template/cardTemplatePromotion/get/${templateNumber}`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
  * 查看卡模板促销单
  * 
  */
  static queryCardTemplatePromotion(templateNumber: string): Promise<Response<PromotionBill[]>> {
    return ApiClient.server().post(`/v1/promotionbill/card/query/${templateNumber}`, {}, {}).then((res) => {
      return res.data
    })
  }

}
