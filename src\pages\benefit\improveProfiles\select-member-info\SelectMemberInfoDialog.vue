<!--
 * @Author: 黎钰龙
 * @Date: 2025-04-02 17:58:10
 * @LastEditTime: 2025-06-10 10:19:27
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\benefit\improveProfiles\select-member-info\SelectMemberInfoDialog.vue
 * 记得注释
-->
<template>
  <el-dialog :title="i18n('个人资料')" :visible.sync="visible" width="50%">
    <div class="content">
      <!-- 左侧选择表单 -->
      <div class="left-form">
        <div class="section-title">{{i18n('基础信息')}}</div>
        <div class="form-item">
          <el-checkbox-group v-model="checkList" style="width: 100%">
            <el-checkbox v-for="item in memberInfoList" :key="item.source === 'custom' ? item.customFieldName : item.fieldName"
              :label="item.source === 'custom' ? item.customFieldName : item.fieldName" :disabled="disabledPhone && item.fieldName == 'PHONE'">
              <span v-if="item.source === 'custom'">{{item.customFieldName}}</span>
              <span v-else>{{i18n(transIdName(item.fieldName))}}</span>
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
      <!-- 右侧回显已选数据 -->
      <div class="right-bar">
        <div style="display: flex; justify-content: space-between; align-items: center;margin-bottom: 12px">
          <span>{{i18n('/会员/智能打标/已选择{0}项',[checkList.length])}}</span>
          <span class="span-btn" @click="doClear">{{i18n('/资料/券承担方/清空')}}</span>
        </div>
        <div class="select-item" v-for="item in checkList" :key="item">
          <span>{{transIdName(item) ? i18n(transIdName(item)) : item}}</span>
          <i v-if="!(disabledPhone && item == 'PHONE')" @click="doDelete(item)" class="el-icon-error"></i>
        </div>
      </div>
    </div>
    <div class="footer">
      <div class="span-btn" @click="doEditInfo">{{i18n('管理资料')}}</div>
      <div>
        <el-button @click="doCancel">{{i18n('/公用/按钮/取消')}}</el-button>
        <el-button type="primary" @click="doSubmit">{{i18n('/公用/按钮/确定')}}</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./SelectMemberInfoDialog.ts"></script>

<style lang="scss" scoped>
.content {
  display: flex;
  justify-content: space-between;
  height: 500px;
  .left-form {
    border: 1px solid #ced0da;
    width: 60%;
    height: 100%;
    padding: 0 12px 12px;
    overflow-y: auto;
    .form-item {
      display: flex;
      flex-wrap: wrap;
      .el-checkbox {
        width: 30%;
        margin-right: 20%;
        margin-bottom: 12px;
      }
    }
  }
  .right-bar {
    flex: 1;
    margin-left: 12px;
    background-color: #f5f7fa;
    height: 100%;
    padding: 12px;
    .select-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 26px;
      font-size: 13px;
      .el-icon-error {
        color: #b9b8b8;
        font-size: 14px;
        &:hover {
          cursor: pointer;
        }
      }
    }
  }
}
.footer {
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
}
</style>