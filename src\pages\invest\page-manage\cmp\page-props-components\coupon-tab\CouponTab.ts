import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import { FormMode } from 'model/local/FormMode';
import SelectPopUp from '../../select-pop-up/SelectPopUp.vue';
import draggable from 'vuedraggable';
import I18nPage from 'common/I18nDecorator';

enum TabType {
  MY_COUPON = 'MY_COUPON', //我的券
  PAY_COUPON = 'PAY_COUPON',  //支付券
  PLAT_COUPON = 'PLAT_COUPON',  //平台券
  PRESENT_RECORD = 'PRESENT_RECORD',  //赠送记录
  CUSTOM_TAB = 'CUSTOM_TAB'  //自定义tab
}

class TabItem {
  type: TabType = TabType.CUSTOM_TAB // tab类型
  show: boolean = true  //是否展示
  customText: string = ''  //自定义名称
  couponSceneType: string = 'all' //发券场景
  couponScenes: string[] = []  //部分场景
}

@Component({
  name: 'CouponTab',
  mixins: [],
  components: { SelectPopUp, draggable },
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理',
    '/页面/导航设置',
  ],
  auto: true
})
export default class CouponTab extends Vue {
  @Prop() config: any;
  @Prop() componentItem: any;
  @Prop({ type: Boolean, default: false }) readonly: boolean; //
  @Prop({ type: String, default: 'CouponTab' }) validateName: string; // 组件校验名称，editPage通过该名称进行组件校验及校验信息清除
  @Prop({ type: Object }) value: any; // 数据模型
  @Prop({ type: String, default: '' }) label: string; // label名
  @Prop({ type: String }) prefixDescribe: string; // 前置描述
  @Prop({ type: String }) suffixDescribe: string; // 后置描述
  @Prop({ type: Boolean, default: true }) required: boolean; // 是否必填
  @Prop({ type: String }) relativeValidateName: string; // 联动校验名称
  @Prop() formKey: any;
  @Prop({ type: Array }) renderTemplateList: any[];
  @Prop() activeIndex: number;
  dialogShow: boolean = false
  FormModeType = FormMode;
  $refs: any;
  labelPosition: string = 'left';
  text: string = '';
  ruleForm: any = {
    name: '我的券',
  }

  // 是否展示开关
  isShowSwitch(item: TabItem) {
    return item.type !== TabType.CUSTOM_TAB && item.type !== TabType.MY_COUPON
  }

  // 是否展示删除按钮
  canRemove(item: TabItem) {
    return item.type === TabType.CUSTOM_TAB
  }

  // 是否展示发券场景
  isShowScene(item: TabItem) {
    return item.type === TabType.MY_COUPON || item.type === TabType.CUSTOM_TAB
  }

  // tab说明
  tabDesc(item: TabItem) {
    if (item.type === TabType.PAY_COUPON) {
      return this.i18n('展示顾客在微信小程序里领的微信支付代金券')
    } else if (item.type === TabType.PLAT_COUPON) {
      return this.i18n('展示顾客在各平台购买或领取的优惠券')
    } else if (item.type === TabType.PRESENT_RECORD) {
      return this.i18n('展示顾客在微信小程序里的赠送券记录')
    } else {
      return ''
    }
  }

  // 固定tab的rules
  get textRules() {
    return [{ required: true, message: this.i18n('请输入'), trigger: 'blur' }]
  }

  // 发券场景的rules
  get sendCouponRules() {
    return [{
      trigger: 'change', validator: (rule: any, value: any, callback: any) => {
        if (value.couponSceneType == 'part' && value.couponScenes.length == 0) {
          callback(new Error(this.i18n('请选择指定场景')));
        } else {
          callback();
        }
      },
    }]
  }

   // 检测重复的自定义文本
   duplicateTextRules(index: number) {
    return {
      validator: (rule: any, value: string, callback: any) => {
        if(!value) callback(new Error(this.i18n('输入框不能为空')));
        const isDuplicate = this.value.propTabInfos.some((item: TabItem, i: number) => {
          return item.customText === value && i !== index;
        });
        if (isDuplicate) {
          callback(new Error(this.i18n('输入的内容不能重复')));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    };
  }

  // 获取校验规则
  getValidationRules(index: number) {
    return [this.duplicateTextRules(index)];
  }

  get myCouponTitle() {
    return this.value.propTabInfos.find((item: any) => item.type === TabType.MY_COUPON)?.customText || '我的券'
  }


  handleChange() {
    this.handleTabScene()
    this.$emit('input', this.value);
    this.$emit('change', this.value);
    this.validate(() => { });
  }
  add() {
    let obj = new TabItem()
    this.value.propTabInfos.push(obj)
  }
  remove(index: number) {
    this.value.propTabInfos.splice(index, 1);
    this.$nextTick(() => {
      this.handleChange();
    })
  }
  goUp(index: number) {
    const arr = this.value.propTabInfos[index].couponScenes || []
    console.log('看看传进去的场景', arr);
    this.$refs.selectRef.open(arr, index)
  }
  changechecked(val: any, index: number) {
    console.log('看看拿到的场景', val);
    this.value.propTabInfos[index].couponScenes = [...new Set(val)];
    this.$emit('input', this.value);
    this.$emit('change', this.value);
  }

  validate(callback: any) {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.validate(callback);
    }
  }

  clearValidate() {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.clearValidate();
    }
  }
  handleTabScene() {
    this.value.propTabInfos.forEach((item: TabItem) => {
      if (item.couponSceneType === 'all') {
        item.couponScenes = []
      }
    })
  }
  // 拖拽事件
  handleDragChange(res: any) {
    console.log('看看拖拽数据', res);
    if (res?.moved) {
      console.log('看看拖拽完的数据', this.value.propTabInfos);
      this.handleChange()
    } else {
      this.$message.error(this.i18n('拖拽信息获取失败'))
    }
  }
}
