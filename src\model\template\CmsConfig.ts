import ConstantMgr from "mgr/ConstantMgr"

/*
 * @Author: 黎钰龙
 * @Date: 2025-05-21 18:52:18
 * @LastEditTime: 2025-05-22 15:50:55
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\template\CmsConfig.ts
 * 记得注释
 */
export default class CmsConfig {
  // 当前系统启用渠道
  publishedChannels: Nullable<CmsConfigChannel[]> = null
}

// 渠道枚举
export enum CmsConfigChannel {
  // 微信小程序
  WEIXIN = 'weixinApp',
  // 支付宝小程序
  ALIPAY = 'aliApplet',
  // H5
  H5 = 'h5'
}

// 渠道选项
export class CmsConfigUtils {
  static getLabel(channel: CmsConfigChannel) {
    const i18nFunc = new ConstantMgr.MenusFuc()
    switch (channel) {
      case CmsConfigChannel.WEIXIN:
        return i18nFunc.format("/设置/渠道", "微信小程序")
      case CmsConfigChannel.ALIPAY:
        return i18nFunc.format("/设置/渠道", "支付宝小程序")
      case CmsConfigChannel.H5:
        return "H5"
    }
  }
}



