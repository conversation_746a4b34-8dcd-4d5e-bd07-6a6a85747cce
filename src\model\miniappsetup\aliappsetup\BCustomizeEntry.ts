// import { WeixinAppletEntryType } from 'model/default/WeixinAppletEntryType'

// 微信小程序功能入口
export default class BCustomizeEntry {
  // uuid
  uuid: Nullable<string> = null
  // 入口类型；APPLET——小程序跳转；SUBPAGE——子页面；INTERNAl_PAGE——内部页面；NOTHING——不跳转
  type: Nullable<string> = null
  // 标题
  title: Nullable<string> = null
  // 介绍
  remark: Nullable<string> = null
  // 图片
  image: Nullable<string> = null
  // 子页面图片
  subpageImage: Nullable<string> = null
  // 目标小程序appId
  appId: Nullable<string> = null
  // 目标小程序路径
  path: Nullable<string> = null
  // 排序
  sort: Nullable<number> = null
  // 样式
  style: Nullable<number> = null
}