/*
 * @Author: 黎钰龙
 * @Date: 2024-03-18 10:30:08
 * @LastEditTime: 2024-03-18 10:37:14
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\couponTemplateDtlItem\couponTemplateDtlBtn\CouponDtlBtn.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import CouponTemplateApi from 'http/coupon/template/CouponTemplateApi';
import CouponItem from 'model/common/CouponItem';
import SelectStoreActiveDtlDialog from 'pages/deposit/mbrdeposit/active/dialog/SelectStoreActiveDtlDialog';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  name: 'CouponDtlBtn',
  components: {
    SelectStoreActiveDtlDialog
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板'
  ],
  auto: true
})
export default class CouponDtlBtn extends Vue {
  @Prop({ type: String, default: '-' }) title: string;
  @Prop({ type: String }) templateId: string;

  child: CouponItem = new CouponItem()
  dialogShow: boolean = false

  doViewCouponDetail() {
    CouponTemplateApi.detail(this.templateId)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.child = new CouponItem();
          this.child.coupons = resp.data;
          this.dialogShow = true;
        }
      })
      .catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
  }

  doDialogClose() {
    this.dialogShow = false
  }
};