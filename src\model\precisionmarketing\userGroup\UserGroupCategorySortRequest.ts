/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-09-13 14:41:16
 * @LastEditTime: 2024-09-13 14:41:32
 * @LastEditors: fang<PERSON><PERSON><PERSON>
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\precisionmarketing\userGroup\UserGroupCategorySortRequest.ts
 * 记得注释
 */
import SortedCategory from "./SortedCategory";

export default class UserGroupCategorySortRequest {
  // 客群分类排序
  sortedCategories: SortedCategory[] = []
}