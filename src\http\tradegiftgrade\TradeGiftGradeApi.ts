import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import TradeGiftGradeActivity from 'model/tradegiftgrade/TradeGiftGradeActivity'

export default class TradeGiftGradeApi {
  /**
   * 获取单笔消费提升等级激励规则详情
   * 获取单笔消费提升等级激励规则详情。
   *
   */
  static get(): Promise<Response<TradeGiftGradeActivity>> {
    return ApiClient.server().post(`/v1/trade-gift-grade/get`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 初始保存单笔消费提升等级激励规则
   * 初始保存单笔消费提升等级激励规则。
   *
   */
  static initialSave(body: TradeGiftGradeActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/trade-gift-grade/initialSave`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 保存或修改单笔消费提升等级激励规则
   * 保存或修改单笔消费提升等级激励规则。
   *
   */
  static save(body: TradeGiftGradeActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/trade-gift-grade/save`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 启用或禁用单笔消费升级激励
   * 启用或禁用单笔消费升级激励。
   *
   */
  static switchRule(stop: boolean): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/trade-gift-grade/switch/${stop}`, {}, {}).then((res) => {
      return res.data
    })
  }
}
