/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:19
 * @LastEditTime: 2024-04-29 16:56:17
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\grade\paidgrade\PaidGradeApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import GradeBenefitRule from 'model/grade/GradeBenefitRule'
import PaidGrade from 'model/grade/paidgrade/PaidGrade'
import PaidGradeDetailBody from 'model/grade/paidgrade/PaidGradeDetailBody'
import Response from 'model/common/Response'

export default class PaidGradeApi {
  /**
   * 追加付费等级信息
   *
   */
  static appendRule(body: PaidGrade): Promise<Response<void>> {
      return ApiClient.server().post(`/v1/grade/paid/appendRule`, body, {}).then((res) => {
          return res.data
      })
  }

  /**
   * 查询付费等级权益规则
   *
   */
  static getGradeBenefitRule(): Promise<Response<GradeBenefitRule>> {
      return ApiClient.server().get(`/v1/grade/paid/getGradeBenefitRule`, {}).then((res) => {
          return res.data
      })
  }

  /**
   * 查询付费等级信息
   *
   */
  static getPaidGradeRule(): Promise<Response<PaidGrade>> {
      return ApiClient.server().get(`/v1/grade/paid/getPaidGradeRule`, {}).then((res) => {
          return res.data
      })
  }

    /**
     * 查询付费等级信息
     *
     */
    static getRule(): Promise<Response<PaidGradeDetailBody>> {
        return ApiClient.server().get(`/v1/grade/paid/get`, {}).then((res) => {
            return res.data
        })
    }

  /**
   * 修改免费等级权益规则
   *
   */
  static modifyBenefitRule(body: GradeBenefitRule): Promise<Response<void>> {
      return ApiClient.server().post(`/v1/grade/paid/modifyBenefitRule`, body, {}).then((res) => {
          return res.data
      })
  }

  /**
   * 追加付费等级前置信息
   *
   */
  static preAppendRule(): Promise<Response<PaidGrade>> {
      return ApiClient.server().get(`/v1/grade/paid/preAppendRule`, {}).then((res) => {
          return res.data
      })
  }

  /**
   * 查询付费等级信息
   *
   */
  static savePaidGradeRule(body: PaidGrade): Promise<Response<void>> {
      return ApiClient.server().post(`/v1/grade/paid/savePaidGradeRule`, body, {}).then((res) => {
          return res.data
      })
  }

  /**
   * 保存付费等级信息
   *
   */
  static saveRule(body: PaidGrade): Promise<Response<boolean>> {
      return ApiClient.server().post(`/v1/grade/paid/save`, body, {}).then((res) => {
          return res.data
      })
  }

}
