import CardPicList from 'cmp/cardpiclist/CardPicList.vue'
import FormItem from 'cmp/formitem/FormItem.vue'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import GiftCardActivityApi from 'http/card/activity/GiftCardActivityApi'
import GiftCardActivity from 'model/card/activity/GiftCardActivity'
import { Component, Vue } from 'vue-property-decorator'
import CardTplItem from '../cmp/cardtplitem/CardTplItem'
import DateUtil from 'util/DateUtil'
import SelectStoreActiveDtlDialog from 'pages/deposit/mbrdeposit/active/dialog/SelectStoreActiveDtlDialog.vue'
import DataUtil from '../common/DataUtil'
import ActivityState from "cmp/activitystate/ActivityState";
import I18nPage from "common/I18nDecorator";
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import ActiveStoreDtl from "cmp/activestoredtl/ActiveStoreDtl";
import RoutePermissionMgr from "mgr/RoutePermissionMgr";
import ActivePresentCardTable from 'cmp/active-present-card/ActivePresentCardTable'
import DateTimeConditionDtl from "cmp/date-time-condition-picker/DateTimeConditionDtl";

@Component({
  name: 'GiftCardActivityDtl',
  components: {
    SubHeader,
    FormItem,
    CardPicList,
    CardTplItem,
    SelectStoreActiveDtlDialog,
    ActivityState,
    BreadCrume,
    ActiveStoreDtl,
    ActivePresentCardTable,
    DateTimeConditionDtl
  }
})
@I18nPage({
  prefix: [
    '/储值/预付卡/电子礼品卡活动/详情页面',
    '/公用/活动/状态',
    '/公用/活动/活动信息',
    '/公用/活动/提示信息',
    '/公用/按钮',
    '/储值/预付卡/电子礼品卡活动/编辑页面',
    '/卡/卡管理/售卡单'
  ],
})
export default class GiftCardActivityDtl extends Vue {
  i18n: (str: string, params?: string[]) => string
  $refs: any
  detail: GiftCardActivity = new GiftCardActivity()
  dataUtil: DataUtil = new DataUtil()
  loading = false
  panelArray: any = []
  couponDialog = {
    dialogShow: false,
    parent: {},
    child: {}
  }

  created() { // 电子礼品卡活动详情
    this.panelArray = [
      {
        name: this.formatI18n("/公用/菜单/电子卡售卡活动"),
        url: 'gift-card-activity'
      },
      {
        name: this.formatI18n('/储值/预付卡/电子礼品卡活动/详情页面/电子礼品卡活动详情'),
        url: ''
      }
    ]
    this.getDetail()
  }

  private getDetail() {
    let activityId = this.$route.query.activityId as string
    this.loading = true
    GiftCardActivityApi.info(activityId).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.detail = resp.data
      } else {
        this.$message.error(resp.msg)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    }).finally(() => {
      this.loading = false
    })
  }

  get activityTime() {
    if (this.detail.body) {
      return `${DateUtil.format(this.detail.body.beginDate, 'yyyy-MM-dd')}${this.i18n('至')}${DateUtil.format(this.detail.body.endDate, 'yyyy-MM-dd')}`
    }
    return '-'
  }

  private audit(activityId: string) {
    this.$alert(this.i18n('确认要审核吗？'), this.i18n('提示'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消'),
      type: 'warning',
      callback: (action) => {
        if (action === 'confirm') {
          GiftCardActivityApi.audit(activityId).then((resp: any) => {
            if (resp && resp.code === 2000) {
              this.$message.success(this.i18n('审核成功'))
              this.getDetail()
            } else {
              throw new Error(resp.msg || this.i18n('操作失败'))
            }
          }).catch((error) => {
            if (error && error.message) {
              this.$message.error(error.message)
            }
          })
        }
      }
    });
  }

  private stop(activityId: string) {
    this.$alert(this.i18n('确认要停止吗？'), this.i18n('提示'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消'),
      type: 'warning',
      callback: (action) => {
        if (action === 'confirm') {
          GiftCardActivityApi.stop(activityId).then((resp: any) => {
            if (resp && resp.code === 2000) {
              this.$message.success(this.i18n('停止成功'))
              this.getDetail()
            } else {
              throw new Error(resp.msg || this.i18n('操作失败'))
            }
          }).catch((error) => {
            if (error && error.message) {
              this.$message.error(error.message)
            }
          })
        }
      }
    });
  }

  private del(activityId: string) {
    this.$alert(this.i18n('确认要删除吗？'), this.i18n('提示'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消'),
      type: 'warning',
      callback: (action) => {
        if (action === 'confirm') {
          GiftCardActivityApi.remove(activityId).then((resp: any) => {
            if (resp && resp.code === 2000) {
              this.$message.success(this.i18n('删除成功'))
              this.$router.push({ name: 'gift-card-activity' })
            } else {
              throw new Error(resp.msg || this.i18n('操作失败'))
            }
          }).catch((error) => {
            if (error && error.message) {
              this.$message.error(error.message)
            }
          })
        }
      }
    });
  }

  private copy() {
    if (!this.detail.body) {
      return
    }
    this.$router.push({
      name: 'gift-card-activity-edit',
      query: { activityId: this.detail.body.activityId, editType: '复制' }
    })
  }

  private edit() {
    if (!this.detail.body) {
      return
    }
    this.$router.push({
      name: 'gift-card-activity-edit',
      query: { activityId: this.detail.body.activityId, editType: '修改' }
    })
  }

  private gotoEvaluatePage() {
    if (!this.detail.body) {
      return
    }
    this.$router.push({ name: 'gift-card-activity-evaluate', query: { activityId: this.detail.body.activityId } })
  }

  private doDialogClose() {
    this.couponDialog.dialogShow = false
  }

  private doCheckDtl(parent: any, child: any) {
    this.couponDialog.parent = parent
    this.couponDialog.child = child
    this.couponDialog.dialogShow = true
  }
  // 有效期
  validatyInfo(item: any) {
    if (!item.validityInfo) {
      return '-'
    }
    if (item.validityInfo.validityType === 'FIXED') {
      return DateUtil.format(item.validityInfo.endDate as Date, 'yyyy-MM-dd')
    }
    if (item.validityInfo.validityType === 'RALATIVE') {
      switch (item.cardTemplateType) {
        case 'GIFT_CARD':
          return this.formatI18n('/储值/预付卡/卡模板/列表页面/制卡后{0}内有效', null, [this.getValidNum(item.validityInfo)])
        case 'OFFLINE_GIFT_CARD':
          return this.formatI18n('/储值/预付卡/卡模板/列表页面/制卡后{0}内有效', null, [this.getValidNum(item.validityInfo)])
        case 'IMPREST_CARD':
          return this.formatI18n('/储值/预付卡/卡模板/列表页面/制卡后{0}内有效', null, [this.getValidNum(item.validityInfo)])
        case 'ONLINE_GIFT_CARD':
          return this.formatI18n('/储值/预付卡/卡模板/列表页面/激活后{0}内有效', null, [this.getValidNum(item.validityInfo)])
        case 'RECHARGEABLE_CARD':
          return this.formatI18n('/储值/预付卡/卡模板/列表页面/末次充值后{0}内有效', null, [this.getValidNum(item.validityInfo)])
      }
    }
  }
  getValidNum(validityInfo: any) {
    if (validityInfo.validityDays !== null) {
      return `${validityInfo.validityDays}天`
    }
    if (validityInfo.validityYears !== null) {
      return `${validityInfo.validityYears}年`
    }
  }
  // 跳转模板详情
  gotoTplDtl(item: any) {
    RoutePermissionMgr.openBlank({ name: 'prepay-card-tpl-dtl', query: { number: item.cardTemplateNumber } })
  }
  parseState(state: string) {
    switch (state) {
      case 'INITAIL':
        return this.formatI18n('/公用/过滤器/未审核');
      case 'UNSTART':
        return this.formatI18n('/公用/过滤器/未开始');
      case 'PROCESSING':
        return this.formatI18n('/公用/过滤器/进行中');
      case 'STOPED':
        return this.formatI18n('/公用/过滤器/已结束');
      case 'SUSPEND':
        return this.formatI18n('/公用/过滤器/暂停中');
    }
  }

  parseStateColor(state: string) {
    switch (state) {
      case 'INITAIL':
        return 'orange';
      case 'UNSTART':
        return 'blue';
      case 'PROCESSING':
        return 'green';
      case 'SUSPEND':
        return 'grey';
      case 'STOPED':
        return 'grey';
    }
  }
}
