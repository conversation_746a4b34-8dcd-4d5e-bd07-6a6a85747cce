import FormItem from 'cmp/formitem/FormItem';
import I18nPage from 'common/I18nDecorator';
import BrowserMgr from 'mgr/BrowserMgr';
import MarketBudget from 'model/promotion/MarketBudget';
import { MarketBudgetActivityEnum } from 'model/promotion/MarketBudgetActivityEnum';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  name: 'MarketingBudgetDtl',
  components: {
    FormItem
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/营销/营销申请'
  ],
  auto: true
})
export default class MarketingBudgetDtl extends Vue {
  @Prop() budget: Nullable<MarketBudget>;
  @Prop({ type: String }) activityType: MarketBudgetActivityEnum; //活动类型

  //是否拥有营销预算权限
  get isPermit() {
    const OAActivities = BrowserMgr.LocalStorage.getItem('sysConfig')?.platformAuditActivity || []
    return OAActivities.indexOf(this.activityType) > -1
  }
};