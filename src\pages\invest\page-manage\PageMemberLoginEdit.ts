import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import I18nPage from 'common/I18nDecorator';
import { Component, Vue } from 'vue-property-decorator';
import SignAndLoginModal from './template/SignAndLoginModal';
import UploadImg from 'cmp/upload-img/UploadImg';
import RichText from 'cmp/rich-text/RichText.vue';
import ContentTemplateApi from 'http/template/ContentTemplateApi';
import CommonUtil from 'util/CommonUtil';
import PublishRequest from 'model/template/PublishRequest';
import UpdateRequest from 'model/template/UpdateRequest';
import ContentTemplate from 'model/template/ContentTemplate';

@Component({
  name: 'PageMemberLoginEdit',
  components: {
    BreadCrume,
    UploadImg,
    RichText
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理',
    '/设置/页面管理',
    '/会员/付费会员'
  ],
  auto: true
})
export default class PageMemberLoginEdit extends Vue {
  $refs: any
  ruleForm: SignAndLoginModal = new SignAndLoginModal()
  pageDetail: ContentTemplate = new ContentTemplate()
  dynamicValidateForm: any = {
    agreement: ''
  }
  currentLoginType: string = 'mobile' // 当前选中的登录方式

  get panelArray() {
    return [
      {
        name: this.i18n("编辑H5登录注册页面"),
        url: "",
      },
    ];
  }

  get rules() {
    return {
      topTitle: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: ["blur", "change"] }],
      loginTypes: [{ required: true, message: this.formatI18n("/公用/券模板", "请选择登录方式"), trigger: ["blur", "change"] }],
      buttonBgColor: [{ required: true, message: this.formatI18n("/公用/券模板", "请选择按钮颜色"), trigger: ["blur", "change"] }],
      buttonFontColor: [{ required: true, message: this.formatI18n("/公用/券模板", "请选择按钮字体颜色"), trigger: ["blur", "change"] }]
    };
  }

  created() {
    // 初始化默认登录方式
    this.currentLoginType = this.ruleForm.loginTypes && this.ruleForm.loginTypes.length > 0
      ? this.ruleForm.loginTypes[0]
      : 'mobile'

    if (this.$route.query.id) {
      this.loadData();
    }
  }

  loadData() {
    const loading = CommonUtil.Loading();
    ContentTemplateApi.get(this.$route.query.id as string)
      .then((res) => {
        if (res.code === 2000 && res.data) {
          this.pageDetail = res.data;
          const content = JSON.parse(this.pageDetail.content)
          console.log('content', content);
          if (content.widgets.length > 1) {
            const valueObj = content.widgets[1].props
            this.ruleForm.topTitleStyle = valueObj.topTitleStyle || 'text'
            this.ruleForm.topTitle = valueObj.topTitle || ''
            this.ruleForm.topImage = valueObj.topImage || ''
            this.ruleForm.loginTypes = valueObj.loginTypes || ['mobile']
            this.ruleForm.buttonBgColor = valueObj.buttonBgColor || '#007AFF'
            this.ruleForm.buttonFontColor = valueObj.buttonFontColor || '#FFFFFF'
            this.ruleForm.buttonText = valueObj.buttonText || '继续'
            this.ruleForm.sendCodeTipFontColor = valueObj.sendCodeTipFontColor || '#999999'
            this.ruleForm.activeColor = valueObj.activeColor || '#ff4444'
            this.ruleForm.showCountrySelector = valueObj.showCountrySelector !== undefined ? valueObj.showCountrySelector : true
            this.ruleForm.description = valueObj.description || ''
            this.ruleForm.id = content.widgets[1].id
            this.dynamicValidateForm.agreement = valueObj.description || ''
            // 设置默认登录方式
            this.currentLoginType = this.ruleForm.loginTypes && this.ruleForm.loginTypes.length > 0
              ? this.ruleForm.loginTypes[0]
              : 'mobile'
          }
        } else {
          throw new Error(res.msg || String(res.code));
        }
      })
      .catch((error) => {
        this.$message.error((error as Error).message);
      })
      .finally(() => {
        loading.close();
      })
  }

  // 取消返回
  goBack() {
    this.$router.push({
      name: "page-manage",
      query: { activeName: "sys-manage" },
    });
  }

  preserve(isPublish: boolean) {
    this.$refs.form.validate(async (valid: any) => {
      if (valid) {
        await this.doUpdate(isPublish);
        if (isPublish) {
          this.publish();
        }
      } else {
        this.$message.error(this.i18n("请完善装修信息！"));
      }
    });
  }

  // 调用发布接口
  publish() {
    const params = new PublishRequest()
    params.id = this.$route.query.id as string;
    console.log('发布', this.$route.query);
    ContentTemplateApi.publish(params)
      .then((res) => {
        if (res.code === 2000) {
          this.$message.success(this.i18n("发布成功"));
          this.$router.push({
            name: "page-manage",
            query: { activeName: "sys-manage" },
          });
        } else {
          this.$message.error(res.msg || this.i18n("发布失败"));
        }
      })
      .catch((error) => {
        let e = error as any;
        this.$message.error(e.message || this.i18n("发布失败"));
      });
  }

  // 编辑
  async doUpdate(isStopNavigate: boolean = true) {
    const loading = CommonUtil.Loading();
    try {
      const params = JSON.parse(JSON.stringify(this.pageDetail))
      params.type = "system"
      params.name = this.i18n("H5登录注册")
      // 将富文本内容同步到ruleForm
      this.ruleForm.description = this.dynamicValidateForm.agreement
      const customWidget = {
        id: 'signAndLogin',
        name: this.i18n("H5登录注册"),
        type: "custom",
        props: this.ruleForm, //保存时多传了几个参数，后端解析会忽略，不影响最终数据
        uuid: ""
      }
      params.content = JSON.parse(params.content)
      params.content.widgets = [JSON.parse(this.pageDetail.content).widgets[0], customWidget]
      let res = await ContentTemplateApi.update(params);
      if (res.code === 2000) {
        this.$message.success(this.i18n("保存成功"));
        if (!isStopNavigate) {
          this.$router.push({
            name: "page-manage",
            query: { activeName: "sys-manage" },
          });
        }
      } else {
        this.$message.error(res.msg || this.i18n("保存失败"));
      }
    } catch (error) {
      this.$message.error((error as Error).message || this.i18n("保存失败"));
    } finally {
      loading.close();
    }
  }

  uploadImgChange(type: string) {
    this.$refs.form.validateField(type)
  }
};