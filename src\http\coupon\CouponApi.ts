import ApiClient from 'http/ApiClient'
import Response from 'model/response/Response'
import CouponResult from 'model/coupon/CouponResult'
import StoreResult from 'model/coupon/StoreResult'

const qs = require('qs');
export default class CouponApi {

  static getCouponList(params: any): Promise<Response<CouponResult>> {

    return ApiClient.server().post(`crm-web/coupon/queryCoupon.hd`, qs.stringify(params), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }).then((res) => {
      return res.data
    })
  }
  static getStoreList(params: any): Promise<Response<StoreResult>> {

    return ApiClient.server().post(`crm-web/app/queryStores.hd`, qs.stringify(params), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }).then((res) => {
      return res.data
    })
  }
  static getPresentHst(params: any): Promise<Response<[]>> {

    return ApiClient.server().post(`crm-web/coupon/getPresentHst.hd`, qs.stringify(params), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }).then((res) => {
      return res.data
    })
  }
}
