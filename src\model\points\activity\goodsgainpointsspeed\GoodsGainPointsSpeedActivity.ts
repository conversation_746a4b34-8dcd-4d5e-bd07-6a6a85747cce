import ActivityBody from 'model/common/ActivityBody'
import GoodsRange from 'model/common/GoodsRange'
import GradeStepValue from "model/common/GradeSameReduction";
import DateTimeCondition from "model/common/DateTimeCondition";
import ActivityGroupLine from "model/common/ActivityGroupLine";
import ChannelRange from "model/common/ChannelRange";
import PushGroup from 'model/precisionmarketing/pushplan/PushGroup';

// 商品积分加倍规则
export default class GoodsGainPointsSpeedActivity {
  // 是否同时审核
  needAudit: Nullable<boolean> = null
  // 活动信息
  activityBody: Nullable<ActivityBody> = new ActivityBody()
  // 活动时间限制
  dateTimeCondition = new DateTimeCondition()
  // 活动最大次数
  activityMaxTimes: Nullable<number> = null
  // 会员最大参与次数
  memberMaxGainPointsTimes: Nullable<number> = null
  // 会员每日最大参与次数
  memberDailyMaxGainPointsTimes: Nullable<number> = null;
  // 商品范围信息
  goodsRange: Nullable<GoodsRange> = new GoodsRange()
  // 不同等级规则
  gradeDifferentStepValues: Nullable<GradeStepValue[]> = null
  // 相同等级规则
  gradeSameStepValue: Nullable<GradeStepValue> = null
  // 活动组明细
  activityGroupLines: ActivityGroupLine[] = []
  //渠道范围
  channelRange: Nullable<ChannelRange> = null
  // 客群
  rule: Nullable<PushGroup> = null
  // 参与叠加促销
  joinPromotion: Nullable<boolean> = false;
  // 排除优惠商品
  excludeFavourGoodTypes: string[] = []
  // 排除优惠金额
  excludeFavourAmountTypes: string[] = ["PROMOTION","COUPON","MEMBER","OTHER"]

}