/*
 * @Author: 申鹏渤
 * @Date: 2023-11-06 16:10:16
 * @LastEditTime: 2023-12-06 11:29:25
 * @LastEditors: 申鹏渤
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\precisionmarketing\tag\TagTemplateFilter.ts
 * 记得注释
 */
import Channel from "model/common/Channel"
import { TagTypeEnum } from "model/common/TagTypeEnum"

// 精准营销-会员标签模板查询过滤器
export default class TagTemplateFilter {
  // 标签名称类似于
  nameLikes: Nullable<string> = null
  // 打标方式等于：Manual-手动；Auto-自动
  tagModelEquals: Nullable<string> = null
  // 状态等于：Enable-启用；Disable-禁用
  stateEquals: Nullable<string> = null
  // 更新频次：ByDay-按日；ByMonth-按月
  scheduleTypeEquals: Nullable<string> = null
  // 分类uuid等于
  categoryIdEquals: Nullable<string> = null
  // 创建人等于
  creatorEquals: Nullable<string> = null
  //最后执行状等于：Processing-进行中；Success-计算成功；Fail-计算失败，unStart-未开始
  lastExecuteStateEquals: Nullable<string> = null
  // 标签来源
  channel: Nullable<Channel> = null
  // 页数>=0
  page: number = 0
  // 页面大小>0
  pageSize: number = 0
  // 标签类型
  tagTypeEquals: TagTypeEnum
}