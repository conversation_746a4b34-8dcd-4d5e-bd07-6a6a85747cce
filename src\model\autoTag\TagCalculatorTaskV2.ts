import { TagTypeEnum } from "model/common/TagTypeEnum"
import { DayOfWeek } from "./DayOfWeek"
import TagCalculatorRule from "./TagCalculatorRule"
import { TagCalculatorTaskExecuteState } from "./TagCalculatorTaskExecuteState"
import { TagCalculatorTaskState } from "./TagCalculatorTaskState"
import { TagCalculatorTaskType } from "./TagCalculatorTaskType"
import { TagCalculatorTaskUpdateMode } from "./TagCalculatorTaskUpdateMode"
import { UpdateCycle } from "./UpdateCycle"

export default class TagCalculatorTaskV2 {
  // uuid
  uuid: Nullable<string> = null
  // 标签主键
  tagTemplateUuid: Nullable<string> = null
  // 标签名称
  tagName: Nullable<string> = null
  // 标签值
  tagValues: string[] = []
  // 覆盖人数
  coveredCount: Nullable<number> = null
  // 计算基准时间
  baseTime: Nullable<Date> = null
  // 覆盖率
  coveredPercentage: Nullable<number> = null
  // 状态
  state: Nullable<TagCalculatorTaskState> = null
  // 创建方式
  type: Nullable<TagCalculatorTaskType> = null
  // 计算规则
  rule: Nullable<TagCalculatorRule> = null
  // 更新方式 [手动更新，周期跟新]
  updateMode: Nullable<TagCalculatorTaskUpdateMode> = null
  // 更新周期
  updateCycle: Nullable<UpdateCycle> = null
  // 每周更新日期
  dayOfWeek: Nullable<DayOfWeek> = null
  // 每月更新日期
  dayOfMonth: Nullable<number> = null
  // 是否每月最后一天
  lastOfMonth: Nullable<boolean> = null
  // 状态[未执行，执行中，执行成功，执行失败]
  executeState: Nullable<TagCalculatorTaskExecuteState> = null
  // 失败原因
  failReason: Nullable<string> = null
  // 最后执行结束时间
  lastEndTime: Nullable<Date> = null
  // 说明
  remark: Nullable<string> = null
  // 创建时间
  created: Nullable<Date> = null
  // 创建人标识
  creator: Nullable<string> = null
  // 最后修改时间
  lastModified: Nullable<Date> = null
  // 最后修改人
  lastModifier: Nullable<string> = null
  // 标签类型
  tagType: Nullable<TagTypeEnum> = null
}