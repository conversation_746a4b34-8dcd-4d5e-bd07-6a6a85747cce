import ConstantMgr from "mgr/ConstantMgr";
import { PageVO } from "./PageVO"
import BrowserMgr from "mgr/BrowserMgr";
import { CmsConfigChannel } from "model/template/CmsConfig";

export default class Data {
  static data(channel: Nullable<CmsConfigChannel[]>) {
    const i18nFunc = new ConstantMgr.MenusFuc()
    const data = [
      {
        name: i18nFunc.format("/页面/页面管理", "页面"),
        type: PageVO.Page,
        sub: [{
          name: i18nFunc.format("/页面/页面管理", "功能页面")
        },
        {
          name: i18nFunc.format("/页面/页面管理", "自定义页面")
        },
        {
          name: i18nFunc.format("/页面/页面管理", "定制页面")
        }],
      },
      {
        name: i18nFunc.format("/页面/页面管理", "券活动"),
        type: PageVO.Coupon,
        children: []
      },
      {
        name: i18nFunc.format("/页面/页面管理", "卡活动"),
        type: PageVO.Card,
        sub: [
          {
            name: i18nFunc.format("/页面/页面管理", "活动列表")
          },
          {
            name: i18nFunc.format("/页面/页面管理", "活动详情")
          }
        ]
      },
      {
        name: i18nFunc.format("/页面/页面管理", "营销活动"),
        type: PageVO.Promotion,
        children: [
          {
            name: i18nFunc.format("/页面/页面管理", "大转盘"),
            type: PageVO.BigWheel,
            sub: [
              {
                name: i18nFunc.format("/页面/页面管理", "活动列表")
              },
              {
                name: i18nFunc.format("/页面/页面管理", "活动详情")
              }
            ]
          },
          {
            name: i18nFunc.format("/页面/页面管理", "集点活动"),
            type: PageVO.CollectPointsActivity,
            sub: [
              {
                name: i18nFunc.format("/页面/页面管理", "活动列表")
              },
              {
                name: i18nFunc.format("/页面/页面管理", "活动详情")
              }
            ]
          },
          {
            name: i18nFunc.format("/页面/页面管理", "抽奖团"),
            type: PageVO.GroupBooking,
            sub: [
              {
                name: i18nFunc.format("/页面/页面管理", "活动列表")
              },
              {
                name: i18nFunc.format("/页面/页面管理", "活动详情")
              }
            ]
          },
          {
            name: i18nFunc.format("/页面/页面管理", "抽锦鲤"),
            type: PageVO.LuckyDraw,
            sub: [
              {
                name: i18nFunc.format("/页面/页面管理", "活动详情")
              }
            ]
          },
          {
            name: i18nFunc.format("/页面/页面管理", "邀请有礼"),
            type: PageVO.InvitedGift,
            sub: [
              {
                name: i18nFunc.format("/页面/页面管理", "邀请有礼"),
                templateId: 'pagesSub/collect-point/CollectPointList'
              }
            ]
          },
        ]
      },
      {
        name: i18nFunc.format("/页面/页面管理", "自定义链接"),
        type: PageVO.CustomLink,
        sub: [
          {
            name: i18nFunc.format("/页面/页面管理", "H5链接"),
            type: PageVO.H5Link,
            children: [
              {
                templateName: i18nFunc.format("/页面/页面管理", "H5链接页面"),
                type: PageVO.H5Link,
                templateId: 'pages/h5-page/H5Page',
                h5Url: ''
              }
            ]
          },
          {
            name: i18nFunc.format("/页面/页面管理", "小程序路径"),
            type: PageVO.WeappLink,
            children: [
              {
                templateName: i18nFunc.format("/页面/页面管理", "小程序路径"),
                type: PageVO.WeappLink,
                templateId: '',
                appid: ''
              }
            ]
          }
        ]
      }
    ]
    let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
    if (sysConfig?.horizontalAlliances) {
      data.push({
        name: i18nFunc.format("/设置/页面管理", "异业合作"),
        type: PageVO.Cooperation,
        sub: [
          {
            name: i18nFunc.format("/设置/页面管理", "异业合作"),
          },
        ]
      })
    }
    const couponActivity = [
      {
        name: i18nFunc.format("/页面/页面管理", "积分兑换券"),
        type: PageVO.ExchangeCoupon,
        sub: [
          {
            name: i18nFunc.format("/页面/页面管理", "活动列表")
          },
          {
            name: i18nFunc.format("/页面/页面管理", "活动详情")
          }
        ]
      },
      {
        name: i18nFunc.format("/页面/页面管理", "小程序领券"),
        type: PageVO.GetCoupon,
        sub: [
          {
            name: i18nFunc.format("/页面/页面管理", "活动列表")
          },
          {
            name: i18nFunc.format("/页面/页面管理", "活动详情")
          }
        ]
      },
    ]
    if (channel?.length === 1 && channel[0] === CmsConfigChannel.WEIXIN) {
      couponActivity.push({
        name: i18nFunc.format("/页面/页面管理", "小程序领微信券"),
        type: PageVO.GetWeiXinCoupon,
        sub: [
          {
            name: i18nFunc.format("/页面/页面管理", "活动列表")
          },
          {
            name: i18nFunc.format("/页面/页面管理", "活动详情")
          }
        ]
      })
    }
    if (channel?.length === 1 && channel[0] === CmsConfigChannel.ALIPAY) {
      couponActivity.push({
        name: i18nFunc.format("/设置/系统设置", "小程序领支付宝券"),
        type: PageVO.GetAliCoupon,
        sub: [
          {
            name: i18nFunc.format("/页面/页面管理", "活动列表")
          },
          {
            name: i18nFunc.format("/页面/页面管理", "活动详情")
          }
        ]
      })
    }
    data.forEach(item => {
      if (item.type === PageVO.Coupon) {
        item.children = couponActivity
      }
    })
    return data
  }
}
