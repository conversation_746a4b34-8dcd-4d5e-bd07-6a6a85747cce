<template>
    <div class="alipay-member-card-edit">
        <!--<WechatHeader v-if="$route.query.from === 'edit'" headerTitle="支付宝会员卡"></WechatHeader>-->
        <BreadCrume :panelArray="panelArray">
            <template slot="operate">
                <el-button type="primary" v-if="$route.query.from === 'edit' && hasOptionPermission('/设置/渠道/支付宝会员设置/支付宝会员初始化', '配置维护')" @click="goWechatMemberCard">{{formatI18n('/储值/预付卡/卡模板/编辑页面/保存')}}</el-button>
                <el-button v-if="$route.query.from === 'edit'" @click="toWechatMemberCard">{{formatI18n('/公用/按钮', '取消')}}</el-button>
            </template>
        </BreadCrume>
        <div style="height: 100%;overflow: auto">
            <div style="margin-top: 30px" v-if="!$route.query.from">
                <el-steps :active="active" :align-center="true" finish-status="success" process-status="finish">
                    <el-step title="支付宝应用申请"></el-step>
                    <el-step title="创建会员卡"></el-step>
                    <el-step title="会员卡投放"></el-step>
                </el-steps>
            </div>
            <span style="position: absolute;top: 80px;right: 50px">
        </span>

            <div class="flex-member">
                <div class="member-left" style="width: 300px">
                    <img src="~assets/image/alipay/alipay.png" style="width: 100%; height: 600px">
                </div>
                <div>
                    <div class="member-right" style="width: 640px;">
                        <div class="member-basic" style="border-bottom: 1px dashed rgb(195, 195, 195);padding-bottom: 20px">{{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/基本信息')}}</div>

                        <div class="basic-flex">
                            <MbrCardTitle v-model="mbrCardTitle" ref="mbrCardTitle"></MbrCardTitle>

                        </div>
                        <div class="basic-flex">
                            <div class="basic-left tip-symbol">{{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/商户logo')}}</div>
                            <div>
                                <div class="basic-hint">
                                    上传建议：不小于500*500px的正方形，bmp、png、jpeg、jpg、gif 格式，1M以内
                                </div>
                                <ImgUpload v-model="merchangeLogo" :limit="1"></ImgUpload>
                            </div>
                        </div>
                        <div class="basic-flex">
                            <div class="basic-left tip-symbol">会员卡封面</div>
                            <div>
                                <div class="basic-hint">
                                    <div>上传建议：不小于1020*643px，bmp、png、jpeg、jpg、gif 格式，2M以内；</div>
                                    <div> 图片不得有圆角，不得拉伸变形</div>
                                </div>
                                <ImgUpload v-model="mbrCardLogo" :limit="2"></ImgUpload>

                            </div>
                        </div>

                        <div class="member-basic">会员识别方式</div>
                        <div style="border-top: 1px dashed #c3c3c3; margin: 30px 0;"></div>
                        <div class="basic-flex">
                            <div class="basic-left tip-symbol">{{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/会员卡识别方式')}}</div>
                            <div class="spot-flex">
                                <div class="spot-right">
                                    <el-radio v-model="codeType" label="CODE_TYPE_QRCODE">{{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/卡号和二维码')}}</el-radio><br /><br />
                                    <img src="~assets/image/member/qrcode.jpg" width="100px" height="57px" />
                                </div>
                                <div class="spot-right">
                                    <el-radio v-model="codeType" label="CODE_TYPE_BARCODE">{{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/卡号和条形码')}}</el-radio><br /><br />
                                    <img src="~assets/image/member/barcode.jpg" width="100px" height="57px" />
                                </div>
                            </div>
                        </div>

                        <div class="member-basic">栏位</div>
                        <div style="border-top: 1px dashed #c3c3c3; margin: 30px 0;"></div>
                        <div class="basic-flex">
                            <div class="basic-left">{{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/会员权益')}}</div>
                            <div class="spot-flex">
                                <el-checkbox-group v-model="memberRights">
                                    <el-checkbox :disabled="getDisabled('score')" label="score">{{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/积分')}}</el-checkbox>
                                    <el-checkbox  label="coupon">{{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/优惠券')}}</el-checkbox>
                                    <el-checkbox :disabled="getDisabled('mbrgrade')"label="mbrgrade">{{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/等级')}}</el-checkbox>
                                </el-checkbox-group>
                            </div>
                        </div>
                        <div class="basic-flex">
                            <div class="basic-left tip-symbol">
                                会员权益说明
                            </div>
                            <div class="spot-flex">
                                <div style="font-size: 13px">展示会员权益说明栏位，供用户查看权益详情
                                    <el-popover
                                            placement="top"
                                            width="420"
                                            trigger="hover">
                                        <div style="font-size: 16px">普通会员无等级权益</div>
                                        <div>升级到银牌会员，赠送50个积分，到店消费可享9.5折折扣</div>
                                        <div>升级到黄金会员，赠送100个积分，到店消费可享9折折扣</div>
                                        <div>升级到钻石会员，赠送500个积分，到店消费可享8.8折折扣</div>
                                        <el-button type="text" slot="reference"><i class="el-icon-warning-outline"></i>示例文字</el-button>
                                    </el-popover>
                                </div>
                            </div>
                        </div>
                        <div class="basic-flex">
                            <div class="basic-left">

                            </div>
                            <div class="spot-flex" style="flex: 1">
                                <div style="width: 100%">
                                    <el-form :model="ruleForm" :rules="rules" label-width="0px"
                                             ref="ruleForm">
                                        <el-form-item prop="benefitRemark">
                                            <el-input maxlength="255" v-model="ruleForm.benefitRemark" type="textarea" placeholder="请描述会员权益，以展示在支付宝会员卡上…"></el-input>
                                        </el-form-item>
                                    </el-form>

                                </div>
                            </div>
                        </div>

                        <div style="border-top: 1px dashed #c3c3c3; margin: 30px 0;"></div>
                        <div class="member-basic">{{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/开卡填写信息')}}</div>
                        <InitialCardInfo v-model="cardInfo"></InitialCardInfo>
                    </div>
                    <div style="text-align: center;padding-bottom: 20px">
                        <el-button v-if="hasOptionPermission('/设置/渠道/支付宝会员设置/支付宝会员初始化', '配置维护') && !isEdit"  @click="toWechatAuthorizeAfter">{{formatI18n('/会员/等级/等级管理/未初始化状态/点击立即开始免费等级初始化/输入所有必填项点击下一步/底部按钮/上一步')}}</el-button>
                        <el-button type="primary" v-if="$route.query.from !== 'edit' && hasOptionPermission('/设置/渠道/支付宝会员设置/支付宝会员初始化', '配置维护')" @click="goWechatMemberCard">{{formatI18n('/储值/预付卡/卡模板/编辑页面/保存')}}</el-button>
                        <el-button type="primary" v-if="hasOptionPermission('/设置/渠道/支付宝会员设置/支付宝会员初始化', '配置维护') && !isEdit"  @click="toWechatMemberCode">保存并到下一步</el-button>
                    </div>
                </div>
            </div>
        </div>


        <ModifyConfirm ref="modifyConfirm" @confirm="doConfirm" @cancel="doCancel"></ModifyConfirm>
    </div>
</template>

<script lang="ts" src="./AlipayMemberCardEdit.ts">
</script>

<style lang="scss">
    .alipay-member-card-edit {
        width: 100%;
        height: 100%;
        background-color: white;
        padding: 0;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        .step-bottom2 {
            border-bottom: 2px solid #3189fd;
        }
        .member-cancel {
            width: 62px;
            float: right;
            margin: -50px 60px 0 0;
            font-size: 14px;
            text-align: center;
            color: #fff;
            background-color: #3189fd;
            border-radius: 5px;
            padding: 5px 10px;
            cursor: pointer;
        }
        .flex-member {
            flex: 1;
            overflow: auto;
            display: flex;
            justify-content: flex-start;
            padding: 30px 30px 50px;
            flex-direction: row;
            .member-left {
                width: 30%;
                margin-right: 30px;
            }
            .member-right {
                width: 65%;
                .member-define {
                    width: 80%;
                    margin: 8px 0px 30px;
                    padding: 10px;
                    font-size: 13px;
                    color: #333333;
                    background-color: #f5fbf9;
                    border: 2px solid #e2f7f0;
                }
                .member-basic {
                    font-size: 16px;
                    font-weight: 700;
                    margin: 10px 0 0;
                }
                    .info-icon {
                        width: 80%;
                        margin: 30px 0px;
                        padding: 10px;
                        font-size: 13px;
                        color: #333333;
                        background-color: #e2e2e2;
                        .ic-infofill {
                            width: 16px;
                            color: #fff !important;
                            background-color: #e2e2e2;
                            margin-right: 5px;
                            border-radius: 50%;
                            padding-top: 5px;
                        }
                    }
                    .level-item {
                        display: flex;
                        justify-content: flex-start;
                        margin-bottom: 20px;
                        .level-title {
                            font-weight: 500;
                            margin-right: 20px;
                        }
                        .img-opinion {
                            font-size: 12px;
                            font-weight: 500;
                            color: #999999;
                        }
                    }
                
                .item-block {
                    padding: 30px 0;
                    border-top: 1px dashed #c3c3c3;
                    .item-title {
                        font-size: 16px;
                        font-weight: 700;
                        margin: 0 0 20px;
                    }
                    .item-flex {
                        display: flex;
                        justify-content: flex-start;
                        margin: 20px 0 20px 20px;
                    }
                    .item-around {
                        margin-right: 50px;
                    }
                    .item-left {
                        margin: 0 0 10px 20px;
                        color: #666;
                    }
                    .el-textarea__inner {
                        width: 360px;
                        height: 150px;
                        padding: 5px;
                    }
                }
                .entry-block {
                    font-size: 13px;
                    .entry-flex {
                        display: flex;
                        justify-content: space-between;
                        border-bottom: 1px solid #e2e2e2;
                        padding: 30px 0 10px;
                    }
                    .entry-item {
                        display: flex;
                        justify-content: flex-start;    
                        margin: 30px 0 30px 20px;
                        .entry-left {
                            width: 100px;
                            margin-right: 30px;
                        }
                    }
                }
                .spot-flex {
                    display: flex;
                    justify-content: flex-start;
                    .spot-left {
                        margin: 0px 30px 0px 20px;
                    }
                    .spot-right {
                        width: 100px;
                        margin-right: 15px;
                    }
                }
            }
        }
        .basic-flex {
            display: flex;
            justify-content: flex-start;
            margin: 30px 20px;
            .basic-left {
                width: 160px;
                text-align: right;
                padding-right: 50px;
            }
            .basic-hint {
                font-size: 12px;
                color: #999999;
                margin-top: 5px;
            }
           
           
        }
        .el-radio__label {
            font-size: 13px;
            line-height: 1.8;
        }
        .el-radio__input.is-checked+.el-radio__label {
            color: #666666 !important; 
        }
        .img-opinion {
            color: #555555;
        }
        .member-button {
            display: flex;
            justify-content: center;
            margin: 0px auto 120px;
            font-size: 14px;
            line-height: 1.6;
            text-align: center;
            .member-back {
                width: 100px;
                color: #333333;
                background-color: #fff;
                border: 1px solid #e2e2e2;
                border-radius: 5px;
                padding: 5px 20px;
                cursor: pointer;
            }
            .member-next {
                width: 100px;
                color: #fff;
                background-color: #3189fd;
                border: 1px solid #3189fd;
                border-radius: 5px;
                padding: 5px 20px;
                cursor: pointer;
            }
            .member-submit {
                margin: 0px 15px;
            }
        }
        .avatar-uploader .el-upload {
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        .avatar-uploader .el-upload:hover {
            border-color: #409EFF;
        }
        .avatar-uploader-icon {
            font-size: 24px;
            color: #8c939d;
            width: 120px;
            height: 120px;
            line-height: 120px;
            text-align: center;
        }
        .avatar {
            width: 120px;
            height: 120px;
            display: block;
        }
        .tip-symbol{
            &:before{
                content: '*';
                color: #EF393F;
                margin-right: 4px;
            }
        }
        .el-step__head.is-success{
            color: #20a0ff !important;
            border-color: #20a0ff !important;
        }
        .el-step__title.is-success{
            color: #a0abbc !important;
        }
        .el-step__title.is-finish{
            color: #a0abbc !important;
        }
        .wechat-header{
            margin: 0 !important;
        }
    }
</style>

