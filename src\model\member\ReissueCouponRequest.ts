/*
 * @Author: 黎钰龙
 * @Date: 2024-05-17 14:30:13
 * @LastEditTime: 2024-05-17 14:35:43
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\member\ReissueCouponRequest.ts
 * 记得注释
 */
class TransBody {
  namespace: string = 'phoenix'
  // 交易id，前端生成，最大长度64
  id: Nullable<string> = null
}

export default class ReissueCouponRequest {
  transId: Nullable<TransBody> = new TransBody()
  //交易号，前端生成，可用transId.id"
  transNo: Nullable<string> = null
  //会员id
  memberId: Nullable<string> = null
  //券模板号
  templateNumber: Nullable<string> = null
  //发券数量
  couponCount: Nullable<number> = null
}