/*
 * @Author: 黎钰龙
 * @Date: 2024-11-21 19:31:52
 * @LastEditTime: 2024-12-10 14:34:59
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\autoTag\MemberAttributeRule.ts
 * 记得注释
 */
import { ConnectiveType } from "./ConnectiveType"
import MemberAttributeConditionGroup from "./MemberAttributeConditionGroup"

export default class MemberAttributeRule {
  // 会员属性类型
  attributeType: Nullable<string> = null
  // 关系连接符：或者 / 且
  connective: Nullable<ConnectiveType> = ConnectiveType.and
  // 会员属性条件组
  memberAttributeConditionGroups: MemberAttributeConditionGroup[] = []
}