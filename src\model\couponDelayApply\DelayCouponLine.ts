/*
 * @Author: 黎钰龙
 * @Date: 2023-03-23 18:01:47
 * @LastEditTime: 2023-03-23 18:01:54
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\couponDelayApply\DelayCouponLine.ts
 * 记得注释
 */
// 延期券明细
export default class DelayCouponLine {
  // 券码
  code: Nullable<string> = null
  // 名称
  name: Nullable<string> = null
  // 有效期开始
  beginDate: Nullable<Date> = null
  // 有效期结束
  endDate: Nullable<Date> = null
}