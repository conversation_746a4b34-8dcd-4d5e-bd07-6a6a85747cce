import ApiClient from 'http/ApiClient'
import GiftCardActivity from 'model/card/activity/GiftCardActivity'
import GiftCardActivityFilter from 'model/card/activity/GiftCardActivityFilter'
import GiftCardActivityQueryResult from 'model/card/activity/GiftCardActivityQueryResult'
import Response from 'model/common/Response'
import GiftCardEvaluation from 'model/card/activity/GiftCardEvaluation';

export default class EntityCardActivityApi {
  /**
   * 审核活动
   *
   */
  static audit(activityId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/giftcard-activity/offline/audit/${activityId}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量审核活动
   *
   */
  static batchAudit(body: string[]): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/giftcard-activity/offline/batch/audit`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 批量删除活动
   *
   */
  static batchRemove(body: string[]): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/giftcard-activity/offline/batch/remove`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 批量终止活动
   *
   */
  static batchStop(body: string[]): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/giftcard-activity/offline/batch/stop`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 创建活动
   *
   */
  static create(body: GiftCardActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/giftcard-activity/offline/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 活动效果评估
   *
   */
  static evaluate(activityId: string): Promise<Response<GiftCardEvaluation>> {
    return ApiClient.server().get(`/v1/giftcard-activity/offline/evaluate/${activityId}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询活动
   *
   */
  static info(activityId: string): Promise<Response<GiftCardActivity>> {
    return ApiClient.server().get(`/v1/giftcard-activity/offline/info/${activityId}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改活动
   *
   */
  static modify(body: GiftCardActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/giftcard-activity/offline/modify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询活动
   *
   */
  static query(body: GiftCardActivityFilter): Promise<Response<GiftCardActivityQueryResult>> {
    return ApiClient.server().post(`/v1/giftcard-activity/offline/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除活动
   *
   */
  static remove(activityId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/giftcard-activity/offline/remove/${activityId}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存并审核活动
   *
   */
  static saveAndAudit(body: GiftCardActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/giftcard-activity/offline/saveAndAudit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 终止活动
   *
   */
  static stop(activityId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/giftcard-activity/offline/stop/${activityId}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

}
