<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 60 (88103) - https://sketch.com -->
    <title>ic_shanghumingcheng_hover</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#00ACFF" offset="0%"></stop>
            <stop stop-color="#016CFF" offset="100%"></stop>
        </linearGradient>
        <path d="M8,5 C9.1045695,5 10,5.8954305 10,7 C10,7.66679355 9.67369136,8.25737363 9.17204861,8.62076573 L9.84739226,10.9630096 C9.93929875,11.2816128 9.82512884,11.6238892 9.56036007,11.8235196 L9.56036007,11.8235196 L8.48162557,12.6368639 C8.19652094,12.8518271 7.80347906,12.8518271 7.51837443,12.6368639 L7.51837443,12.6368639 L6.44394668,11.8267668 C6.17704656,11.6255294 6.06339703,11.2795645 6.15896892,10.959255 L6.15896892,10.959255 L6.85093444,8.63716937 C6.33629821,8.27529969 6,7.67692317 6,7 C6,5.8954305 6.8954305,5 8,5 Z" id="path-2"></path>
        <filter x="-175.0%" y="-64.1%" width="450.0%" height="279.5%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.196377841 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="🔪icon" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="切图" transform="translate(-190.000000, -128.000000)">
            <g id="ic_shanghumingcheng_hover" transform="translate(190.000000, 128.000000)">
                <g id="cutting/ic_shanghumingcheng_hover">
                    <g>
                        <g id="编组-3" transform="translate(4.000000, 4.000000)">
                            <path d="M8.72329177,0.27753816 L15.6489999,6.46629141 C16.0960345,6.86575764 16.1345962,7.5519821 15.73513,7.99901664 C15.5291896,8.22948032 15.2347734,8.36122931 14.9257023,8.36122931 L14.513,8.361 L14.5130557,14.9144897 C14.5130557,15.5140005 14.0270562,16 13.4275454,16 L2.57244293,16 C1.97293217,16 1.48693268,15.5140005 1.48693268,14.9144897 L1.486,8.361 L1.07428604,8.36122931 C0.474775285,8.36122931 -0.0112242086,7.87522981 -0.0112242086,7.27571906 C-0.0112242086,6.96664796 0.12052478,6.67223177 0.350988459,6.46629141 L7.2766966,0.27753816 C7.68864119,-0.0905719825 8.31134718,-0.0905719825 8.72329177,0.27753816 Z" id="形状结合" fill="url(#linearGradient-1)"></path>
                            <g id="形状结合" opacity="0.595354353">
                                <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                                <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-2"></use>
                            </g>
                        </g>
                        <rect x="0" y="0" width="24" height="24"></rect>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>