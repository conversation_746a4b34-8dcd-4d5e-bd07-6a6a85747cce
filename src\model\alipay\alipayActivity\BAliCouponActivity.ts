import AliMerchantCouponInfo from './AliMerchantCouponInfo'
import AliPayCouponInfo from './AliPayCouponInfo'
import AliPromoteCouponInfo from './AliPromoteCouponInfo'
import BaseCouponActivity from 'model/v2/coupon/BaseCouponActivity'
import { AliCouponType } from './AliCouponType'

export default class BAliCouponActivity extends BaseCouponActivity {
  // 每人每天限量
  maxMemberPerDayTime: Nullable<number> = null
  // 是否提前预告
  advanceNotice: Nullable<boolean> = false
  // 预告天数
  advanceDay: Nullable<number> = null
  // 封面
  image: Nullable<string> = null
  // 详细页面
  detailImage: Nullable<string> = null
  // 客服电话
  servicePhone: Nullable<string> = null
  // 显示排序
  sequence: Nullable<number> = null
  // 是否修改活动库存
  modifyStock: Nullable<boolean> = null
  // 券类型
  aliCouponType: Nullable<AliCouponType> = null
  // 支付券
  payCouponInfo: Nullable<AliPayCouponInfo> = null
  // 商家券
  merchantCouponInfo: Nullable<AliMerchantCouponInfo> = null
  // 促销券
  promoteCouponInfo: Nullable<AliPromoteCouponInfo> = null
  //外部活动号
  outerNumberId: Nullable<string> = null;
}