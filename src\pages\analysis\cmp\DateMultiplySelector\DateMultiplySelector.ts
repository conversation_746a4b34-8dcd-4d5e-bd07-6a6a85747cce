/*
 * @Author: 黎钰龙
 * @Date: 2024-02-29 14:11:17
 * @LastEditTime: 2024-11-06 10:41:07
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\analysis\cmp\DateMultiplySelector\DateMultiplySelector.ts
 * 记得注释
 */
import FormItem from 'cmp/formitem/FormItem';
import I18nPage from 'common/I18nDecorator';
import DateUtil from 'util/DateUtil';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  name: 'DateMultiplySelector',
  components: {
    FormItem
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/公用/日期'
  ],
  auto: true
})
export default class DateMultiplySelector extends Vue {
  @Prop({ type: String }) label: string;
  @Prop({ type: Object, default: () => { return {} } }) dayOptions: object;
  @Prop({ type: Object, default: () => { return {} } }) weekOptions: object;
  @Prop({ type: Object, default: () => { return {} } }) monthOptions: object;
  @Prop({ type: Array, default: null }) defaultDayRange: Nullable<string[]>;
  @Prop({ type: Date, default: '' }) defaultWeekRange: Nullable<string>;
  @Prop({ type: Date, default: '' }) defaultMonthRange: Nullable<string>;

  dateType: 'day' | 'week' | 'month' = 'day'
  dayRange: Nullable<string[]> = null
  weekRangeBegin: string = '' //选择周 开始时间
  weekRangeEnd: string = '' //选择周 结束时间
  monthRangeBegin: string = '' //选择月 开始时间
  monthRangeEnd: string = ''  //选择月 结束时间

  created() {
    this.doReset()
  }

  // 选择周的开始时间范围
  get weekRangeBeginFormat() {
    if (!this.weekRangeBegin) {
      return null
    } else {
      return [DateUtil.format(DateUtil.getWeekDatesOne(this.weekRangeBegin)[0], 'yyyy-MM-dd'), DateUtil.format(DateUtil.getWeekDatesOne(this.weekRangeBegin)[1], 'yyyy-MM-dd')]
    }
  }

  // 选择周的结束时间范围
  get weekRangeEndFormat() {
    if (!this.weekRangeEnd) {
      return null
    } else {
      return [DateUtil.format(DateUtil.getWeekDatesOne(this.weekRangeEnd)[0], 'yyyy-MM-dd'), DateUtil.format(DateUtil.getWeekDatesOne(this.weekRangeEnd)[1], 'yyyy-MM-dd')]
    }
  }

  // 周时间范围的返回结果
  get getWeekRangeRes() {
    let dataRes
    if (!this.weekRangeBeginFormat && this.weekRangeEndFormat) {
      // 选了结束时间 但没选开始时间
      dataRes = this.weekRangeEndFormat
    } else if (this.weekRangeBeginFormat && !this.weekRangeEndFormat) {
      // 选了开始时间 但没选结束时间
      dataRes = this.weekRangeBeginFormat
    } else if (this.weekRangeBeginFormat && this.weekRangeEndFormat) {
      // 开始时间和结束时间都选了
      dataRes = [this.weekRangeBeginFormat[0], this.weekRangeEndFormat[1]]
    } else {
      // 开始时间和结束时间都没选
      dataRes = null
    }
    return dataRes
  }

  // 选择月的开始时间范围
  get monthRangeBeginFormat() {
    if (!this.monthRangeBegin) {
      return null
    } else {
      const date = new Date(this.monthRangeBegin)
      const year = date.getFullYear()
      const month = date.getMonth()
      const firstDay = new Date(year, month, 1)
      const lastDay = new Date(year, month + 1, 0)
      return [DateUtil.format(firstDay, 'yyyy-MM-dd'), DateUtil.format(lastDay, 'yyyy-MM-dd')]
    }
  }

  // 选择月的结束时间范围
  get monthRangeEndFormat() {
    if (!this.monthRangeEnd) {
      return null
    } else {
      const date = new Date(this.monthRangeEnd)
      const year = date.getFullYear()
      const month = date.getMonth()
      const firstDay = new Date(year, month, 1)
      const lastDay = new Date(year, month + 1, 0)
      return [DateUtil.format(firstDay, 'yyyy-MM-dd'), DateUtil.format(lastDay, 'yyyy-MM-dd')]
    }
  }

  // 月时间范围的返回结果
  get getMonthRangeRes() {
    let dataRes
    if (!this.monthRangeBeginFormat && this.monthRangeEndFormat) {
      // 选了结束时间 但没选开始时间
      dataRes = this.monthRangeEndFormat
    } else if (this.monthRangeBeginFormat && !this.monthRangeEndFormat) {
      // 选了开始时间 但没选结束时间
      dataRes = this.monthRangeBeginFormat
    } else if (this.monthRangeBeginFormat && this.monthRangeEndFormat) {
      // 开始时间和结束时间都选了
      dataRes = [this.monthRangeBeginFormat[0], this.monthRangeEndFormat[1]]
    } else {
      // 开始时间和结束时间都没选
      dataRes = null
    }
    return dataRes
  }

  doChange() {
    let res: any = {
      type: '',
      date: null
    }
    if (this.dateType === 'day') {
      res.date = this.dayRange
      res.type = 'DAY'
    } else if (this.dateType === 'week') {
      res.date = this.getWeekRangeRes
      res.type = 'WEEK'
    } else if (this.dateType === 'month') {
      res.date = this.getMonthRangeRes
      res.type = 'MONTH'
    }
    this.$emit('change', res)
  }

  doReset() {
    this.dayRange = this.defaultDayRange
    this.weekRangeBegin = this.defaultWeekRange as any
    this.weekRangeEnd = this.defaultWeekRange as any
    this.monthRangeBegin = this.defaultMonthRange as any
    this.monthRangeEnd = this.defaultMonthRange as any
    this.dateType = 'day'
    this.doChange()
  }

  doValidate() {
    return new Promise<void>((resolve, reject) => {
      if (this.dateType === 'day') {
        if (this.dayRange) {
          resolve()
        } else {
          this.$message.warning(this.i18n('/储值/会员储值/储值充值活动/编辑页面/请选择日期'))
          reject()
        }
      } else if (this.dateType === 'week') {
        if (this.getWeekRangeRes) {
          resolve()
        } else {
          this.$message.warning(this.i18n('/储值/会员储值/储值充值活动/编辑页面/请选择日期'))
          reject()
        }
      } else if (this.dateType === 'month') {
        if (this.getMonthRangeRes) {
          resolve()
        } else {
          this.$message.warning(this.i18n('/储值/会员储值/储值充值活动/编辑页面/请选择日期'))
          reject()
        }
      }
    })
  }
};