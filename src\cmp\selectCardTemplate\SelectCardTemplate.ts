import CardTemplateSelectorDialog from 'cmp/selectordialogs/CardTemplateSelectorDialog';
import I18nPage from 'common/I18nDecorator'
import RoutePermissionMgr from 'mgr/RoutePermissionMgr';
import CardTemplate from 'model/card/template/CardTemplate';
import CardTemplateFilter from 'model/card/template/CardTemplateFilter';
import IdName from 'model/common/IdName';
import { CardType } from 'model/payment/member/CardType';
import {Component, Prop, Vue, Watch} from 'vue-property-decorator'

@I18nPage({
	prefix: ["/储值/预付卡/预付卡支付活动/编辑页面", "/公用/活动/状态", "/公用/活动/活动信息", "/公用/活动/提示信息", "/公用/js提示信息", "/公用/按钮"],
})
@Component({
    name: 'SelectCardTemplate',
    components: {
        CardTemplateSelectorDialog
    }
})
export default class SelectCardTemplate extends Vue {
    @Prop()
    title: string
    @Prop()
    cardTypeProp: CardType
    @Prop()
    cardTemplatesProp: Nullable<IdName[]>

    @Watch('cardTypeProp')
    onCardTypePropChange(val: CardType){
        console.log(val);
        
        this.cardType = val
    }

    @Watch('cardTemplatesProp', {
        deep: true
    })
    onCardTemplatesPropChange(val: Nullable<IdName[]>){
        console.log(val);
        this.cardTemplates = val
        this.prevSelectedCardTemplates = []
        if (this.cardTemplates) {
            for (let item of this.cardTemplates) {
                let cardTpl = new CardTemplate();
                cardTpl.number = item.id;
                cardTpl.name = item.name;
                this.prevSelectedCardTemplates.push(cardTpl);
            }
        }
        
    }
    form: any = {}
    cardType: CardType = CardType.none
    prevSelectedCardTemplates: CardTemplate[] = [];
	cardTemplateFilter: CardTemplateFilter = new CardTemplateFilter();
    cardTemplates: Nullable<IdName[]> = []
    $refs: any
    rules: any = {}

    created() {
        this.rules = {
            check: [
                {
                    validator: (rule: any, value: any, callback: any) => {
                        console.log('validator in');
                        if (this.cardType === 'none' && (this.cardTemplates === null || this.cardTemplates!.length === 0)) {
                            callback(this.i18n('/公用/js提示信息/请填写必填项'))
                        } else {
                            callback()
                        }
                    }, trigger: ['change', 'blur']
                }
            ]
        }
		this.cardTemplateFilter.typeIn = ["OFFLINE_GIFT_CARD", "ONLINE_GIFT_CARD", "RECHARGEABLE_CARD"];
        this.cardType = this.cardTypeProp
        this.cardTemplates = this.cardTemplatesProp
    }

    doCardTemplateSelected(arr: CardTemplate[]) {
		this.prevSelectedCardTemplates = arr;
		this.cardTemplates = [];
		for (let tpl of arr) {
			let idName = new IdName();
			idName.id = tpl.number;
			idName.name = tpl.name;
			this.cardTemplates.push(idName);
		}
        this.$refs.cardForm.validate()
        this.$emit('change', {cardType: this.cardType, cardTemplates: this.cardTemplates})
	}

    private gotoTplDtl(num: string) {
		RoutePermissionMgr.openBlank({ name: "prepay-card-tpl-dtl", query: { number: num } });
	}

    cardTypeChange() {
        this.prevSelectedCardTemplates = [] 
        this.cardTemplates = []
        this.$nextTick(()=>{
            this.$refs.cardForm.validate()
            this.$emit('change', {cardType: this.cardType, cardTemplates: this.cardTemplates})
        })
    }

    doValidate() {
        return new Promise((resolve, reject)=>{
            this.$refs.cardForm.validate((valid: any)=> {
                console.log(valid);
                if (valid) {
                    resolve(true)
                } else {
                    reject()
                }
            })
        })
        
    }
}
