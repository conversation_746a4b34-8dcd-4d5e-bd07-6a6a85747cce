<template>
  <img :src="src">
</template>

<script>
export default {
  name: "MemberChannelIcon",
  props: {
    type: { default: "weiXin" },
  },
  data() {
    return {
      images: {
        "weiXin": require("~assets/image/member/<EMAIL>"),
        "aliPay": require("~assets/image/member/<EMAIL>"),
        "weimob": require("~assets/image/member/<EMAIL>"),
        "douYin": require("~assets/image/member/<EMAIL>"),
        "qiWei": require("~assets/image/member/<EMAIL>"),
        "youZan": require("~assets/image/member/<EMAIL>"),
      },
    };
  },
  computed: {
    src() {
      return this.images[this.type];
    },
  },
};
</script>

<style lang="scss">

</style>
