/*
 * @Author: 黎钰龙
 * @Date: 2024-05-16 11:06:17
 * @LastEditTime: 2024-07-31 10:34:32
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\activity-state-tag\ActivityStateTag.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import { StateEqualsType } from 'model/v2/coupon/videonumberActivity/StateEqualsType';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  name: 'ActivityStateTag',
  components: {}
})
@I18nPage({
  prefix: [
    '/公用/券模板'
  ],
  auto: true
})
export default class ActivityStateTag extends Vue {
  @Prop() stateEquals: StateEqualsType;

  get computeType() {
    const stateMap = [
      [StateEqualsType.INITAIL, 'warning'],
      [StateEqualsType.INITIAL, 'warning'],
      [StateEqualsType.UNSTART, ''],
      [StateEqualsType.PROCESSING, 'success'],
      [StateEqualsType.SUSPEND, 'info'],
      [StateEqualsType.STOPED, 'info'],
      [StateEqualsType.AUDITING, 'warning'],
      [StateEqualsType.REJECTED, 'info'],
      [StateEqualsType.AUDITED, 'success']
    ]
    const target = stateMap.find((item) => this.stateEquals === item[0])
    return target?.length ? target[1] : null
  }
};