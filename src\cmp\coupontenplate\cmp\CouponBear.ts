import {Component, Prop, Vue, Watch} from 'vue-property-decorator'
import AmountToFixUtil from 'util/AmountToFixUtil'
// import CostParty from 'model/common/CostParty'
import RSCostPartyFilter from 'model/common/RSCostPartyFilter'
import CostPartyApi from 'http/costparty/CostPartyApi'
// import { ApportionType } from 'model/common/ApportionType'
import {throttle} from 'lodash'
import I18nPage from "common/I18nDecorator";
import CBearSpecialGoods from "cmp/coupontenplate/cmp/cBearSpecialGoods.vue";
import CouponInitialApi from "http/v2/coupon/init/CouponInitialApi";

class CostPartyDetail {
  // 承担方
  party: Nullable<string> = null
  // 承担百分比/金额
  value: Nullable<number> = null
}
@Component({
  name: 'CouponBear',
  components: {
    CBearSpecialGoods
  }
})
@I18nPage({
  prefix: ["/公用/券模板",'/营销/券礼包活动/核销第三方券'],
  auto: true
})
export default class CouponBear extends Vue {
  $refs: any
  copyParties: any = []
  costParties: any = []
  partySelectArray: any[] = []
  partyInputArray: any[] = []
  parties: any[] = []
  // 承担类型：PROPORTION——按比例； AMOUNT——按金额
  typeCparties: string = 'unset'
  costAll: string = 'all'
  costPartyConfig: string = 'unset'
  visibleUnset: Boolean = true
  visibleProportion: Boolean = true
  visibleAmount: Boolean = true
  costPartyConfigOptions: string[] = []
  // 承担方对象
  costObj: any
  throThisAmount = throttle(this.doThisAmount, 1000)
  // 特殊商品
  specialGoods: any[] = []
  @Prop()
  value: any
  @Prop()
  state: string
  @Prop({
    default: false
  })
  isTiktok: Boolean

  @Watch('partySelectArray', { deep: true })
  onPartySelectArrayChange(value: any) {}

  @Watch('value', { deep: true })
  onValueChange(value: any) {
    this.costObj = {...value}
    this.typeCparties = this.costObj.bearType && this.costObj.bearType != 'NONE' ? this.costObj.bearType : 'unset'
    this.costAll = this.costObj.bearType == 'AMOUNT' ? this.costObj.amountType : null
    if (value && value.costPartyDetails && value.costPartyDetails.length > 0) {
      this.costParties = [...value.costPartyDetails]
      this.partySelectArray = []
      this.partyInputArray = []
      if (this.costParties && this.costParties.length > 0) {
        this.costParties.forEach((item: any, index: number) => {
          this.partySelectArray[index] = item.party
          this.partyInputArray[index] = item.value
          this.parties[index] = this.copyParties
          if (this.costAll == 'part') {
            this.partyInputArray[index] = index < this.costParties.length -1 ? item.value < 0 ? 1 : item.value : -1
          }
        })
      }
      // 特殊商品设置
      if (value && value.specialGoodsCostParties && value.specialGoodsCostParties.length > 0) {
        this.specialGoods = [...value.specialGoodsCostParties]
      }
    } else {
      // 无承担方，则无特殊商品
      this.partySelectArray = []
      this.partyInputArray = []
      this.costParties = []
      this.specialGoods = []
    }
  }

  showDelBtn(index:any) {
    let show: any = true
    if (this.costParties.length == 1) {
      show = false
    }
    // 按金额
    if (this.typeCparties == 'AMOUNT') {
      if (this.costAll == 'all') {
        show = false
      } else if (this.costAll == 'part') {
        show = this.costParties.length == 2 ? false : (index != 0 && index < this.costParties.length - 1)
      }
    }
    return show
  }
  async created() {
    this.getCostParty()
    await this.getCostPartyConfig()
    this.costObj = {...this.value}
    if (this.costObj.bearType && this.costObj.bearType != 'NONE') {
      //　非不记录的值回显
      this.typeCparties = this.costObj.bearType;
    } else if (this.$route.query.from === 'edit' || this.$route.query.from === 'copy') {
      // 不记录的值为NONE，保证修改复制回显正确
      this.typeCparties = 'unset'
    } else {
      // 新增设置默认值
      this.typeCparties = this.costPartyConfig
      this.typeCpartiesChange(this.typeCparties)
    }
    // this.typeCparties = this.costObj.bearType && this.costObj.bearType != 'NONE' ? this.costObj.bearType : this.costPartyConfig
    if (this.costPartyConfigOptions.indexOf(this.typeCparties) === -1) {
      this.typeCparties = this.costPartyConfig
      this.typeCpartiesChange(this.typeCparties)
    }
    this.costAll = this.costObj.bearType == 'AMOUNT' ? this.costObj.amountType : null
    if (this.value && this.value.costPartyDetails && this.value.costPartyDetails.length > 0) {
      this.costParties = [...this.value.costPartyDetails]
      this.partySelectArray = []
      this.partyInputArray = []
      if (this.costParties && this.costParties.length > 0) {
        this.costParties.forEach((item: any, index: number) => {
          this.partySelectArray[index] = item.party
          this.partyInputArray[index] = item.value
          this.parties[index] = this.copyParties
        })
      }
      // 特殊商品设置
      if (this.value && this.value.specialGoodsCostParties && this.value.specialGoodsCostParties.length > 0) {
        this.specialGoods = [...this.value.specialGoodsCostParties]
      }
    }
  }
  doThisAmount(index: number) {
    if (isNaN(parseFloat(this.partyInputArray[index]))) {
      this.partyInputArray[index] = 0.01
    } else {
      this.partyInputArray[index] = parseFloat(this.partyInputArray[index])
    }
    if (this.typeCparties === 'AMOUNT') {
      // this.partyInputArray[index] = AmountToFixUtil.formatAmount(this.partyInputArray[index], 999999, 1, 2)
      this.$set(this.partyInputArray, index, AmountToFixUtil.formatAmount(this.partyInputArray[index], 999999.99, 0.01, 2))
    } else {
      // this.partyInputArray[index] = AmountToFixUtil.formatAmount(this.partyInputArray[index], 100, 1, '')
      this.$set(this.partyInputArray, index, AmountToFixUtil.formatAmount(this.partyInputArray[index], 100, 1, ''))
    }
    
    // this.$forceUpdate()
    // 校验
    this.formVali()
    this.$emit('input', this.transParams())
    this.$emit('change')
  }
  bearTypeChange() {
    this.costParties.forEach((item: any, index: number)=>{
      item.bearType = this.costParties[0].bearType
      if (isNaN(parseFloat(this.partyInputArray[index]))) {
        this.partyInputArray[index] = 1
      } else {
        this.partyInputArray[index] = parseFloat(this.partyInputArray[index])
      }
      if (item.bearType === 'AMOUNT') {
        this.partyInputArray[index] = AmountToFixUtil.formatAmount(this.partyInputArray[index], 999999, 1, 2)
      } else {
        this.partyInputArray[index] = AmountToFixUtil.formatAmount(this.partyInputArray[index], 100, 1, 0)
      }
    })
    this.$emit('input', this.transParams())
    this.$emit('change')
  }
  doDeleteCostParties(index: number) {
    this.partySelectArray.splice(index, 1)
    this.parties.splice(index, 1)
    this.costParties.splice(index, 1)
    this.partyInputArray.splice(index, 1)
    // 校验
    this.formVali()
    this.$emit('input', this.transParams())
    this.$emit('change')
  }
  doAddCostParties() {
    let item: CostPartyDetail = new CostPartyDetail()
    // if (this.costParties && this.costParties.length > 0) {
    //   item.bearType = this.costParties[this.costParties.length - 1].bearType
    // } else {
    //   item.bearType = ApportionType.PROPORTION
    // }

    this.costParties.push(item)
    this.partySelectArray.push('')
    this.partyInputArray.push(1)
    // 校验
    this.formVali()
    this.$emit('input', this.transParams())
    this.$emit('change')
    if (this.typeCparties === 'AMOUNT' && this.costAll == 'part') {
      this.partyInputArray.forEach((item, index) => {
        if (item < 0 && index != this.partyInputArray.length -1 && index!= 0) {
          item = 1
        }
      })
    }
  }
  doPartiesChange(pos: number) {
    // 校验
    this.formVali()
    this.$emit('input', this.transParams())
    this.$emit('change')
  }
  doFilterPartys(pos: number) {
    let record: any = JSON.parse(JSON.stringify(this.copyParties))
    if (this.partySelectArray && this.partySelectArray.length > 0) {
      this.partySelectArray.forEach((sub: any) => {
        if (record && record.length > 0) {
          record.forEach((item: any, index: number) => {
            if (item.costParty.id === sub && item.costParty.id !== this.partySelectArray[pos]) {
              record.splice(index, 1)
            }
          })
        }
      })
      this.parties[pos] = record
      this.$forceUpdate()
    }
  }

  private getCostPartyConfig() {
    return CouponInitialApi.get().then((resp: any) => {
      if (resp && resp.code === 2000) {
        if (resp.data && resp.data.costPartyConfigOptions) {
          this.costPartyConfigOptions = resp.data.costPartyConfigOptions;
        }
        if (resp.data && resp.data.costPartyConfig === "none") {
          this.costPartyConfig = "unset";
        } else if (resp.data && resp.data.costPartyConfig === "amount") {
          this.costPartyConfig = "AMOUNT";
        } else if (resp.data && resp.data.costPartyConfig === "proportion") {
          this.costPartyConfig = "PROPORTION";
        }
        if (resp.data && resp.data.costPartyConfigOptions && resp.data.costPartyConfigOptions.indexOf("none") === -1) {
          this.visibleUnset = false;
        } else if (resp.data && resp.data.costPartyConfigOptions) {
          this.costPartyConfigOptions.push("unset")
        }
        if (resp.data && resp.data.costPartyConfigOptions && resp.data.costPartyConfigOptions.indexOf("amount") === -1) {
          this.visibleAmount = false;
        } else if (resp.data && resp.data.costPartyConfigOptions) {
          this.costPartyConfigOptions.push("AMOUNT")
        }
        if (resp.data && resp.data.costPartyConfigOptions && resp.data.costPartyConfigOptions.indexOf("proportion") === -1) {
          this.visibleProportion = false;
        } else if (resp.data && resp.data.costPartyConfigOptions) {
          this.costPartyConfigOptions.push("PROPORTION")
        }
      }
    }).catch((error: any) => {
      this.$message.error(error.message)
    })
  }

  private getCostParty() {
    let params: RSCostPartyFilter = new RSCostPartyFilter()
    params.page = 0
    params.pageSize = 0
    CostPartyApi.query(params).then((resp: any) => {
      if (resp && resp.code === 2000) {
        let dataTmp = resp.data.map((x: any) => {
          return {
            ...x,
            id: x.costParty.id,
            name: x.costParty.name
          }
        })
        this.parties.push(dataTmp)
        this.copyParties = dataTmp
      }
    }).catch((error: any) => {
      this.$message.error(error.message)
    })
  }
  private transParams() {
    let arr: CostPartyDetail[] = []
    if (this.costParties && this.costParties.length > 0) {
      this.costParties.forEach((item: any, index: number) => {
        let cost: CostPartyDetail = new CostPartyDetail()
        cost.party = this.partySelectArray[index]
        cost.value = parseFloat(this.partyInputArray[index] as any)
        // cost.percent = this.partyInputArray[index] as any
        // cost.bearType = item.bearType
        arr.push(cost)
      })
    }
    // 按金额 类型， 值为0 是全部券抵扣金额，值为-1 是剩余券抵扣金额
    if (this.typeCparties == 'AMOUNT') {
      if (this.costAll == 'all') {
        arr = arr.length ? [arr[0]] : [{party: '', value: 1}]
        arr[0].value = 0
      } else if (this.costAll == 'part') {
        arr[arr.length - 1].value = -1
      }
    }
    return {
      // 承担类型：PROPORTION——按比例； AMOUNT——按金额；
      bearType: this.typeCparties,
      // 按金额类型：all-全部；part-部分
      amountType: this.typeCparties == 'AMOUNT' ? this.costAll : null,
      // 成本承担方信息
      costPartyDetails: arr,
      specialGoodsCostParties: [...this.specialGoods]
    }
  }
  // 类型选项变化
  typeCpartiesChange(val: any) {
    this.typeCparties = val
    let item: CostPartyDetail = new CostPartyDetail()
    if (val == 'AMOUNT') {
      this.costAll = this.costAll || 'all'
      // 
      if (!this.costParties.length && this.costAll == 'all') {
        this.costParties = [item]
        this.partySelectArray = ['']
        this.partyInputArray = [1]
      } else if ((!this.costParties.length || this.costParties.length == 1) && this.costAll == 'part') {
        if (!this.costParties.length) {
          this.costParties = [item, item]
          this.partySelectArray = ['', '']
          this.partyInputArray = [1, 1]
        } else if (this.costParties.length == 1) {
          this.costParties = [item]
          this.partySelectArray = ['']
          this.partyInputArray = [1]
        }
      }
    }
    if (val == 'PROPORTION' && !this.costParties.length) {
      this.costParties = [item]
      this.partySelectArray = ['']
      this.partyInputArray = [1]
    } else if (val == 'PROPORTION' && this.costParties.length) {
      let arr: any = []
      this.partyInputArray.forEach(inputVal => {
        arr.push(inputVal < 0 ? 1 : inputVal)
      })
      this.partyInputArray = arr
    }
    // 校验
    this.formVali()
    this.$emit('input', this.transParams())
    this.$emit('change')
  }
  // 按金额是否全部承担
  costAllChange(val: any) {
    // 全部，只留一个
    if (val == 'all') {
      this.costParties = this.costParties.length ? [this.costParties[0]] : [{party: '', value: 1}]
    }
    // 部分，必须有俩
    if (val == 'part' && this.costParties.length < 2) {
      this.costParties.push({
        party: '', value: 1
      })
    }
    // 校验
    this.formVali()
    // 重置数据
    this.$emit('input', this.transParams())
    this.$emit('change')
  }

  // 校验各个表单
  formVali() {
    // this.$nextTick(() => {
    // })
    setTimeout(() => {
      this.formValiPromise()
    }, 300);
  }
  formValiPromise() {
    return new Promise((resolve, reject) => {
      if (this.typeCparties != 'AMOUNT' && this.typeCparties != 'PROPORTION') {
        resolve(true)
      } else {
        this.costParties.forEach((item: any, index: any) => {
          console.log(this,'-----' , this.$refs, this.$refs.valiForm0);
          if (this.typeCparties == 'PROPORTION') {
            ;(this.$refs['valiForm'+index] as any)[0].validateField('costparty'+index)
            ;(this.$refs['valiForm'+index] as any)[0].validateField('PROPORTIONVAL'+index)
          } else if (this.typeCparties == 'AMOUNT') {
            ;(this.$refs['valiForm'+index] as any)[0].validateField('costparty'+index)
            if (index == 0) {
              ;this.$refs['costType'+index] && (this.$refs['valiForm'+index] as any)[0].validateField('costType'+index)
            }
            if (this.costAll == 'part') {
              ;this.$refs['AMOUNT'+index] && this.$refs['AMOUNT'+index][0] && (this.$refs['valiForm'+index] as any)[0].validateField('AMOUNT'+index)
            }
          }
        })
        let validateMessage = false
        for (let index = 0; index < this.costParties.length; index++) {
          let strCParty = ''
          let strPVal = ''
          let strAVal = ''
          if (this.typeCparties == 'PROPORTION') {
            // 'costparty'+index
            strCParty = this.$refs['costparty'+index] && this.$refs['costparty'+index][0] && this.$refs['costparty'+index][0].validateMessage ? this.$refs['costparty'+index][0].validateMessage : ''
            // 'PROPORTIONVAL'+index        
            strPVal = this.$refs['PROPORTIONVAL'+index] && this.$refs['PROPORTIONVAL'+index][0] && this.$refs['PROPORTIONVAL'+index][0].validateMessage ? this.$refs['PROPORTIONVAL'+index][0].validateMessage : ''
          } else if (this.typeCparties == 'AMOUNT') {
            // 'costparty'+index
            strCParty = this.$refs['costparty'+index] && this.$refs['costparty'+index][0] && this.$refs['costparty'+index][0].validateMessage ? this.$refs['costparty'+index][0].validateMessage : ''
            // 'costType'+index(不用校验，已写死，有值)
            if (this.costAll == 'part') {
            // 'AMOUNT'+index
            strAVal = this.$refs['AMOUNT'+index] && this.$refs['AMOUNT'+index][0] && this.$refs['AMOUNT'+index][0].validateMessage ? this.$refs['AMOUNT'+index][0].validateMessage : ''
            }
          }
          if (strCParty || strPVal || strAVal) {
            validateMessage = true
            break
          }       
        }
        if (validateMessage) {
          reject()
        } else {
          resolve(true)
        }
      }
    })
  }
  // 校验方法
  costpartyRule(rule: any, value: any, callback: any) {
    // console.log('222222', rule ,value);
    let filed = this. getIndex(rule.field, 'costparty')
    const val = this.partySelectArray[filed]
    // console.log('111111', filed, val);
    if (!val) {
      callback(this.i18n('请选择承担方！'))
    }
    callback()
  }
  propormitionValRule(rule: any, value: any, callback: any) {
    let filed = this. getIndex(rule.field, 'PROPORTIONVAL')
    const val = this.partyInputArray[filed]
    if (!val) {
      callback(this.i18n('请输入承担用券比例（大于0）！'))
    } else {
      // 是否都有值，都有值判断和是否等于100
      const tVal = this.partyInputArray.reduce((total, item) => {
        return total + parseFloat(item)
      }, 0)
      if (tVal != 100) {
        callback(this.i18n('所有承担方合计承担100%，请重新填写！'))
      }
    }
    callback()
  }
  costTypeRule(rule: any, value: any, callback: any) {
    // costAll
    callback()
  }
  amountValRule(rule: any, value: any, callback: any) {
    let filed = this. getIndex(rule.field, 'AMOUNT')
    const val = this.partyInputArray[filed]
    if (!val) {
      callback(this.i18n('请填写承担用券金额（大于0）！'))
    }
    callback()
  }
  // 获取是哪一行数据
  getIndex(str: string, field: string) {
    return parseInt(str.replace(field, '') || '0')
  }
  // 用券方式 设置特殊商品
  setSpecialGoods() {
    this.$refs.CBearSpecialGoods.open(this.specialGoods)
  }
  // 接受特殊商品参数
  specialGoodsSubmit(goods: any[]) {
    this.specialGoods = [...goods]
    // this.$emit("input", this.doTransParams());
		// this.$emit("change", this.channels);
    this.$emit('input', this.transParams())
    this.$emit('change')
  }
  viewSpecialGoods() {
    ;(this.$refs.CBearSpecialGoodsDtl as any).open()
  }
}