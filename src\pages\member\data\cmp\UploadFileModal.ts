import { Component, Vue, Prop } from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import EnvUtil from 'util/EnvUtil'
import PermissionMgr from 'mgr/PermissionMgr'
import BrowserMgr from 'mgr/BrowserMgr';
import UploadApi from "http/upload/UploadApi";
@Component({
  name: "UploadFile",
  components: {
    FormItem,
  },
})
export default class UploadFileModal extends Vue {
  $refs: any;
  @Prop({
    type: Boolean,
    default: false,
  })
  dialogShow: boolean;
  @Prop({ type: Boolean, default: false }) readonly isMarketCenter!: boolean;
  templateType = "member_upload";
  fileCount = 0;
  uploadHeaders: any = {};
  // 会员资料模板
  get member() {
    if (location.href.indexOf("localhost") === -1) {
      if (PermissionMgr.hasMemberOption()) {
        if (BrowserMgr.SessionStorage.getItem("isShowFirstAndLastName")) {
          return "template_member_with_last_name.xlsx"
        } else {
          return "template_member.xlsx";
        }
      } else {
        return "template_member_standard.xlsx";
      }
    } else {
      if (PermissionMgr.hasMemberOption()) {
        if (BrowserMgr.SessionStorage.getItem("isShowFirstAndLastName")) {
          return "template_member_with_last_name.xlsx"
        } else {
          return "template_member.xlsx";
        }
      } else {
        return "template_member_standard.xlsx";
      }
    }
  }
  // 卡模板
  get cardMember() {
    if (location.href.indexOf("localhost") === -1) {
      return "template_card.xlsx";
    } else {
      return "template_card.xlsx";
    }
  }
  get getUploadUrl() {
    if (this.isMarketCenter === true) {
      if (this.templateType === "card_member_upload") {
        return EnvUtil.getServiceUrl() + `v1/marketingCenterMember/importMember?card=true`;
      } else {
        return EnvUtil.getServiceUrl() + `v1/marketingCenterMember/importMember?card=false`;
      }
    } else {
      if (this.templateType === "card_member_upload") {
        return EnvUtil.getServiceUrl() + `v1/member/importMember?card=true`;
      } else {
        return EnvUtil.getServiceUrl() + `v1/member/importMember?card=false`;
      }
    }

  }
  get getLimitInfo() {
    let str: any = this.formatI18n("/公用/导入", "为保障上传成功，建议每次最多上传{0}条信息");
    str = str.replace(/\{0\}/g, "5000");
    return str;
  }
  created() {
    let locale = sessionStorage.getItem("locale");
    this.uploadHeaders = {
      locale: locale ? locale : "zh_CN",
      time_zone: new Date().getTimezoneOffset(),
      marketingCenter: sessionStorage.getItem("marketCenter"),
    };
    const authorization = EnvUtil.getUniAuthorization()
    if (authorization) {
      this.uploadHeaders.authorization = authorization
    }
  }
  doModalClose(type: string) {
    if (type === "confirm") {
      if (this.fileCount > 0) {
        this.$refs.upload.submit();
      } else {
        this.$message.warning(this.formatI18n("/公用/导入/请先选择文件") as any);
      }
      this.$emit("dialogClose");
    } else {
      this.$emit("dialogClose");
    }
  }
  doHandleChange(file: any, fileList: any) {
    if (fileList.length > 0) {
      this.fileCount++;
    }
  }
  doBeforeClose(done: any) {
    this.$emit("dialogClose");
    done();
  }
  getSuccessInfo(a: any, b: any, c: any) {
    if (a && a.code === 2000) {
      this.$refs.upload.clearFiles();
      this.fileCount = 0;
      this.$emit("dialogClose");
      this.$emit("upload-success");
    } else {
      this.$message.error(a.msg)
    }
  }
  getErrorInfo(a: any, b: any, c: any) {
    this.$message.error(this.formatI18n("/公用/导入", "导入失败，请重新导入") as any);
    this.fileCount = 0;
    this.$refs.upload.clearFiles();
  }

  downloadTemplate(templatePath: string) {
    UploadApi.getUrl(templatePath).then((resp: any) => {
      if (resp && resp.data) {
        window.open(resp.data);
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
}