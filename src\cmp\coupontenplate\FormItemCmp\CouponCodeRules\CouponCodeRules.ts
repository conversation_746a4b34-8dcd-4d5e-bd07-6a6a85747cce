import I18nPage from 'common/I18nDecorator';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  name: 'CouponCodeRules'
})
@I18nPage({
  prefix: [
    "/公用/券模板",
    '/营销/券礼包活动/核销第三方券'
  ],
  auto: true
})
export default class CouponCodeRules extends Vue {
  @Prop()
  ruleForm: any;

  @Prop()
  copyFlag: 'add' | 'edit' | 'copy';

  @Prop({
    type: Boolean,
    default: false,
  })
  wxScanForCoupon: boolean;
};