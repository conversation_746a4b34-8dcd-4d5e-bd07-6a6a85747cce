import IdName from 'model/equityCard/default/IdName'
import { GoodsRange } from 'model/equityCard/default/GoodsRange'

// 限制商品规则
export default class BWeimobExtLimitGoodsRule {
  // 商品范围：all 全部商品 ,part 指定商品
  goodsRange: Nullable<GoodsRange> = null
  // 可用商品
  includeGoodsIds: IdName[] = []
  // 是否限制是否不可用商品
  existExcludeGoods: Nullable<boolean> = null
  // 不可用商品
  excludeGoodsIds: IdName[] = []
  // 是否包含下级自建商品
  includeChildGoods: Nullable<boolean> = null
}