<template>
  <!-- 我的优惠券 中间展示效果组件 -->
  <div class="my-coupon" :style="{
      padding:
        componentItemProps.styMarginTop +
        'px ' +
        componentItemProps.styMarginRight +
        'px ' +
        componentItemProps.styMarginBottom +
        'px ' +
        componentItemProps.styMarginLeft +
        'px',
    }" :class="[{ activeCom: activeIndex === index }]" @click="activeTemplate">
    <div class="toolBar" v-if="activeIndex === index">
      <setting-toolbar :btns="toolBarBtns" @click="toolbarClick"></setting-toolbar>
    </div>
    <div class="tabs">
      <div class="tabs-item" v-for="(item,index) in componentItemProps.propTabInfos" :key="index"
        @click="activeTab(item.customText)" v-show="item.show">
        <span>
          {{ item.customText }}
        </span>
        <div v-show="tabName == item.customText" class="underLine"></div>
      </div>
    </div>
    <div class="image">
      <ul class="coupon-type-list">
        <li>{{ i18n('/券/券查询/未使用') }}</li>
        <li>{{ i18n('已使用') }}</li>
        <li>{{ i18n('已过期') }}</li>
        <li>{{ i18n('已失效') }}</li>
      </ul>
      <div class="coupon-style2" v-if="componentItemProps.propCouponStyle === '2'">
        <img src="@/assets/image/fellow/img_youhuiquan_two.png" />
        <div class="coupon-style2-item"  v-for="item in 2" :key="item">
          <div class="coupon-btn-use">
            {{ i18n('去使用') }} 
            <img src="@/assets/image/fellow/ic_time_line.png" />
          </div>
          <div  class="coupon-style2-item-left">
            <div class="coupon-style2-item-left-title text-overflow">{{ i18n('立减5元') }}</div>
            <div class="coupon-style2-item-left-text text-overflow">{{ i18n('满39元使用') }}</div>
          </div>
          <div class="coupon-style2-item-right">
            <div class="coupon-title text-overflow">{{ item == 1 ? i18n('光明鲜奶满20减5优惠券') : i18n('光明鲜奶满20减5优惠券光明鲜奶满20减5优惠券光明鲜奶满20减5优惠券') }}</div>
            <div class="coupon-time text-overflow">2020.06.06 00:00 {{ i18n('/会员/洞察/公共/操作符/至') }} 2020.06.06 23:59</div>
            <div class="coupon-btn-share text-overflow">
              {{ i18n('赠送好友') }}  
              <img src="@/assets/image/fellow/img_youhuiquan_two_icon.png" />
            </div>
          </div>
        </div>
      </div>

      <div v-else class="coupon-style1">
          <img src="@/assets/image/fellow/img_youhuiquan.png" />
          <div  class="coupon-style1-item" v-for="item in 2" :key="item">
              <div class="coupon-title text-overflow ">{{ i18n('光明鲜奶满20减5优惠券') }}</div>
              <div class="coupon-time text-overflow">2020.06.06 00:00 {{ i18n('/会员/洞察/公共/操作符/至') }} 2020.06.06 23:59</div>
              <div class="coupon-btn-share text-overflow">
                {{ i18n('赠送好友') }}  
                <img src="@/assets/image/fellow/img_youhuiquan_two_icon.png" />
              </div>
              <div class="coupon-btn-use">{{ i18n('去使用') }} </div>
      </div>
    </div>
    </div>
  </div>
</template>

<script lang="ts" src="./MyCoupon.ts"></script>

<style lang="scss" scoped>
.my-coupon {
  width: 100%;
  position: relative;
  background: #fff;
  .toolBar {
    position: absolute;
    top: 0;
    right: 1px;
    z-index: 999;
    cursor: pointer;
  }
  .nav-2 {
    .el-tabs__nav::v-deep {
      width: 100%;
      .el-tabs__active-bar {
        width: 50% !important ;
      }
      .el-tabs__item {
        width: 50%;
        text-align: center;
      }
    }
  }
  .nav-3 {
    .el-tabs__nav::v-deep {
      width: 100%;
      .el-tabs__active-bar {
        width: 25% !important ;
      }
      .el-tabs__item {
        width: 33.33%;
        text-align: center;
      }
    }
  }
  .nav-4 {
    .el-tabs__nav::v-deep {
      width: 100%;
      .el-tabs__active-bar {
        width: 20% !important ;
      }
      .el-tabs__item {
        width: 25%;
        text-align: center;
      }
    }
  }

  .coupon-style1 {
    // position: a;
    position: relative;

      &-item {
        position: absolute;
        left: 150px;
        height: 100px;
        &:nth-of-type(1) {
          top: 55px;
        }

        &:nth-of-type(2) {
          top: 165px;
        }

        .coupon-title {
          font-size: 15px;
          font-weight: bold;
          margin-bottom: 6px;
          width: 230px;
        }

        .coupon-time {
          width: 230px;
          font-size: 12px;
          color: #acacab;
        }
        .coupon-btn-share {
          background-color: #ff6f5d;
          color: #fff;
          display: inline-block;
          padding: 2px 8px;
          border-radius: 12px;
          margin-top: 6px;
          img {
            height: 16px;
            width: 16px;
            position: relative;
            top: 3px;
          }
        }

        .coupon-btn-use {
          color: #ff3b3d;
          font-size: 15px;
          font-weight: bold;
          padding: 3px 12px ;
          border: 1px solid #fc591a;
          border-radius: 12px;
          display: inline-block;
          bottom: 10px;
          position: absolute;
          right: 8px;
        }
      }
  }

  .coupon-style2 {
    position: relative;

      &-item {
        position: absolute;
        left: 10px;
        height: 100px;
        width: 370px;
        display: flex;
        &:nth-of-type(1) {
          top: 55px;
        }

        &:nth-of-type(2) {
          top: 165px;
        }

        &-left {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 90px;
          text-align: center;

          &-title {
            font-size: 15px;
            font-weight: bold;
            width: 100%;
            margin-bottom: 6px;
          }

          &-text {
            width: 100%;
            font-size: 12px;
          }
        }

        &-right {
          width: 250px;
          padding-left: 10px;

        .coupon-title {
          font-size: 15px;
          font-weight: bold;
          line-clamp:2;
          margin-bottom: 6px;
          width: 100%;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          margin-top: 16px;
        }

        }

      
        .coupon-time {
          width: 100%;
          font-size: 12px;
          color: #acacab;
        }
        .coupon-btn-share {
          background-color: #ff6f5d;
          color: #fff;
          display: inline-block;
          padding: 2px 8px;
          border-radius: 12px;
          margin-top: 6px;
          img {
            height: 16px;
            width: 16px;
            position: relative;
            top: 3px;
          }
        }

        .coupon-btn-use {
          position: absolute;
          color: #ff3b3d;
          font-size: 14px;
          writing-mode: tb;
          right: 5px;
          height: 100%;
          text-align: center;
          img {
          transform: rotate(-135deg);
          }
        }
      }

  }

  .coupon-type-list {
    display: flex;
    position: absolute;
    z-index: 1;
    // width: 100%;
    width: calc(100% - 25px);
    margin-left: 15px;
    justify-content: space-around;
    list-style: none;

    li {
      height: 35px;
      // width: 25%;
      flex: 1;
      text-align: center;
      padding: 6px 12px;

      &:nth-of-type(1) {
        color: #ff4f51;
        border: 2px solid #ffafa7;
        border-radius: 16px;
        font-weight: bold;
      }

    }
  }
}
.tabs {
  display: flex;
  line-height: 50px;
  height: 65px;
  font-size: 14px;
  text-align: center;
  overflow-x: auto; /* 允许横向滚动 */
  white-space: nowrap; /* 防止换行 */
  
  .tabs-item {
    flex: 0 0 auto; /* 使每个标签项不自动缩小 */
    position: relative;
    font-weight: 600;
    padding: 0 15px; /* 增加左右内边距 */
  }
}

.underLine {
  width: 50%;
  height: 2px;
  position: absolute;
  bottom: 10px;
  left: 50%;
  z-index: 10;
  transform: translate(-50%);
  background: #fc5312;
}
.option {
  display: flex;
  justify-content: space-between;
  height: 32px;
  border-radius: 16px;
  background: #efefef;
  .item {
    flex: 1;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    line-height: 32px;
    text-align: center;
    border-radius: 16px;
  }
  .active {
    border: 1px solid #ff432f;
    color: #ff3b3d;
    font-weight: 600;
    font-size: 14px;
    background: #fff6f6;
  }
}
.image {
  img {
    width: 100%;
  }
}
.text-overflow {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
</style>