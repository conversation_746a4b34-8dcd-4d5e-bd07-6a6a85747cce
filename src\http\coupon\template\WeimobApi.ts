import ApiClient from "http/ApiClient";
import Response from "model/common/Response";
import BQueryGoodsRequest from 'model/common/weimob/BQueryGoodsRequest'
import BGoods from 'model/common/weimob/BGoods'
import BQueryCategoryRequest from 'model/common/weimob/BQueryCategoryRequest'
import BCategory from 'model/common/weimob/BCategory'
import BQueryGoodsClassifyRequest from 'model/common/weimob/BQueryGoodsClassifyRequest'
import BWeimobClassify from 'model/common/weimob/BWeimobClassify'
import QueryOrgRequest from 'model/common/weimob/BQueryOrgRequest'
import WeiMobCloudOrg from 'model/common/weimob/WeiMobCloudOrg'



export default class WeimobApi {

    
  /**
   * 获取门店信息
   * 获取门店信息
   * 
   */
  static orgList(body: QueryOrgRequest): Promise<Response<WeiMobCloudOrg[]>> {
    return ApiClient.server().post(`/v1/coupon-template/weimob/org/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

    /**
     * 微盟商品查询
     *
     */
    static queryGoodsList(body: BQueryGoodsRequest): Promise<Response<BGoods[]>> {
        return ApiClient.server()   
            .post(`/v1/coupon-template/weimob/goood/list`, body, {})
            .then((res) => {
                return res.data;
            });
    }

    /**
     * 微盟商品类别查询
     *
     */
    static queryCategoryList(body: BQueryCategoryRequest): Promise<Response<BCategory[]>> {
        return ApiClient.server()
            .post(`/v1/coupon-template/weimob/goods/category/getList`, body, {})
            .then((res) => {
                return res.data;
            });
    }

    /**
     * 微盟商品类别查询
     *
     */
    static queryGroupList(body: BQueryGoodsClassifyRequest): Promise<Response<BWeimobClassify[]>> {
        return ApiClient.server()
            .post(`/v1/coupon-template/weimob/goods/classify/getList`, body, {})
            .then((res) => {
                return res.data;
            });
    }



}
