import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import I18nPage from "common/I18nDecorator";
import ChannelSelect from "cmp/channelselect/ChannelSelect";
import Channel from "model/common/Channel";
import { TagTypeEnum } from "model/common/TagTypeEnum";
import BTagFilter from "model/tag/BTagFilter";
import TagV2Api from "http/tag/TagV2Api";
import TagOption from "model/tag/TagOption";
import MemberA<PERSON> from "http/member_standard/MemberApi";

class TagTreeItem {
  tag: TagOption;
  tagValue: any;
  expand: boolean = false;
}

@Component({
  name: "MemberInfoTagEditor",
  components: { ChannelSelect },
})
@I18nPage({
  prefix: [
    "/会员/会员资料",
    "/会员/选择人群",
    "/公用/券模板",
  ],
  auto: true,
})
export default class MemberInfoTagEditor extends Vue {

  @Prop({
    type: Boolean,
    default: false,
  })
  value: boolean;
  @Prop({ default: () => [] })
  selectTags: TagOption[];
  @Prop()
  memberId: string;

  // 渠道
  channels: Channel[] = [];

  filter: BTagFilter = new BTagFilter();
  tagsTree: TagTreeItem[] = [];
  saveLoading: boolean = false;

  selectTagsLocal: TagOption[] = [];

  @Watch("selectTags", { immediate: true })
  onSelectTagChange() {
    this.selectTagsLocal = this.selectTags.map(e => {
      return { ...e };
    });
    this.syncSelectTag2Tree();
  }

  @Watch("value")
  onShow() {
    if (this.value)
      this.queryTag();
  }

  queryTag() {
    this.filter.channel = this.channels.length > 0 ? this.channels[0] : null;
    if (!this.filter.tagName)
      this.filter.tagName = null;
    TagV2Api.filterList(this.filter).then((resp) => {
      if (resp && resp.code === 2000) {
        this.tagsTree = resp.data!.filter(e =>
          e.tagType == TagTypeEnum.checkbox ||
          e.tagType == TagTypeEnum.singleChoice ||
          e.tagType == TagTypeEnum.text).map(e => {
          return { tag: e, tagValue: null, expand: false };
        });
        this.syncSelectTag2Tree();
      }
    })
      .catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
  }

  syncSelectTag2Tree() {
    this.tagsTree.forEach(e => {
      let tagValue = null;
      let find = this.selectTagsLocal.find(selected => selected.tagId == e.tag.tagId);
      if (find) {
        switch (e.tag.tagType) {
          case TagTypeEnum.singleChoice:
          case TagTypeEnum.text:
            if (find.tagValues && find.tagValues.length > 0)
              tagValue = find.tagValues[0];
            break;
          default:
            tagValue = find.tagValues;
        }
        e.tagValue = tagValue;
      }
    });
  }

  syncTree2SelectTag(tagTree: TagTreeItem) {
    const index = this.selectTagsLocal.findIndex(e => e.tagId == tagTree.tag.tagId);
    if (index == -1) {
      const tag = { ...tagTree.tag };
      tag.tagName = tag.tagId;
      tag.tagValues = Array.isArray(tagTree.tagValue) ? tagTree.tagValue : [tagTree.tagValue];
      this.selectTagsLocal.push(tag);
    } else {
      if (tagTree.tagValue) {
        this.selectTagsLocal[index].tagValues = Array.isArray(tagTree.tagValue) ? tagTree.tagValue : [tagTree.tagValue];
      } else
        this.selectTagsLocal.splice(index, 1);
    }
  }

  onSelectLabel(tagTree: TagTreeItem, value: string, single: boolean = true) {
    if (single)
      tagTree.tagValue = tagTree.tagValue == value ? null : value;
    else {
      if (!tagTree.tagValue)
        tagTree.tagValue = [];
      const index = tagTree.tagValue.indexOf(value);
      if (index == -1)
        tagTree.tagValue.push(value);
      else
        tagTree.tagValue.splice(index, 1);
      if (tagTree.tagValue.length == 0)
        tagTree.tagValue = null;
    }
    this.syncTree2SelectTag(tagTree);
  }

  onInputValue(tagTree: TagTreeItem) {
    this.syncTree2SelectTag(tagTree);
  }

  onRemoveTag(item: TagOption) {
    this.selectTagsLocal.splice(this.selectTagsLocal.indexOf(item), 1);
    const removed = this.tagsTree.find(e => e.tag.tagId == item.tagId);
    if (removed)
      removed.tagValue = null;
  }

  // 输入回车键
  onEnter(event: any) {
    if (event.which === 13) {
      this.queryTag();
    }
  }

  // 展开/收起标签分类
  doExpendTag(val: TagTreeItem) {
    val.expand = !val.expand;
  }

  beforeClose() {
    this.$emit("input", false);
  }

  onSave() {
    this.saveLoading = true;
    MemberApi.saveMemberTag({ memberId: this.memberId, tags: this.selectTagsLocal }).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(this.i18n("保存成功"));
        this.$emit("saved");
        this.beforeClose();
      } else {
        this.$message.error(resp.msg);
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message);
      }
    }).finally(() => {
      this.saveLoading = false;
    });
  }
}
