/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2023-07-19 13:39:55
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\v2\controller\points\activity\PointsChargeActivityRuleV2.ts
 * 记得注释
 */
import GoodsRange from 'model/common/GoodsRange'
import MemberDayPointsChargeRule from "model/v2/controller/points/activity/MemberDayPointsChargeRule";
import UpperPayLimit from "model/v2/controller/points/activity/UpperPayLimit";
import ChannelRange from "model/common/ChannelRange";

export default class PointsChargeActivityRuleV2 {
  // 每使用积分数
  points: Nullable<number> = null
  // 可抵现金额数
  amount: Nullable<number> = null
  // 抵现类型,空表示不限制,BY_BALANCE-按积分余额,BY_AMOUNT-按消费
  chargeType: Nullable<string> = null
  // 抵现条件
  consumeMinValue: Nullable<number> = null
  // 积分余额满
  pointsBalanceValue: Nullable<number> = null
  // 每人每天积分抵现次数,空表示不限制
  dailyPayTimes: Nullable<number> = null
  // 每人每月积分抵现次数
  monthPayTimes: Nullable<number> = null
  // 每人每天积分抵现积分,空表示不限制
  dailyPayPoints: Nullable<number> = null
  // 单笔抵现信息
  upperPayLimit: Nullable<UpperPayLimit> = null
  // 商品范围
  goodsRange: Nullable<GoodsRange> = null
  // 抵现规则
  pointsChargeRule: Nullable<MemberDayPointsChargeRule> = null
  //渠道范围
  channelRange: Nullable<ChannelRange> = null
}