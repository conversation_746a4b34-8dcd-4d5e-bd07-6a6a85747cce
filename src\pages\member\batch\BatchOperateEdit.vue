<template>
  <div class="batch-operate-edit">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button
          :loading="saveLoading"
          v-if="hasOptionPermission('/会员/会员管理/会员批量操作单', '单据维护')"
          @click="doSave"
          type="primary"
        >
          {{ formatI18n("/公用/按钮", "保存") }}
        </el-button>
        <el-button
          :loading="saveAndAuditLoading"
          v-if="
            hasOptionPermission('/会员/会员管理/会员批量操作单', '单据维护') &&
            hasOptionPermission('/会员/会员管理/会员批量操作单', '单据审核')
          "
          @click="doSaveAndAudit"
        >
          {{ formatI18n("/公用/按钮", "保存并审核") }}
        </el-button>
        <el-button @click="doCancel">{{
          formatI18n("/公用/按钮", "取消")
        }}</el-button>
      </template>
    </BreadCrume>
    <div >
      <div class="content">
        <FormItem :label="formatI18n('/权益/积分/新建积分调整单/条目', '会员')">
          <el-input
            @change="doMemberChange"
            class="width-298"
            v-model="member"
            :placeholder="
              i18n('手机号/会员号/实体卡号/IC卡卡号/车牌号')
            "
          ></el-input>
          <div style="color: red" v-show="memberFlag">
            {{ memberFlagContent }}
          </div>
        </FormItem>
        <FormItem
          :label="formatI18n('/权益/积分/新建积分调整单/条目', '会员姓名')"
        >
          <el-input
            class="width-298"
            disabled
            v-model="memberObj.name"
          ></el-input>
        </FormItem>

        <FormItem :label="i18n('会员当前状态')">
          <el-tag
            size="small"
            type="success"
            v-if="memberObj.state === 'Using'"
          >
            {{ formatI18n("/会员/会员资料", "使用中") }}
          </el-tag>
          <el-tag
            size="small"
            type="danger"
            v-if="memberObj.state === 'Blocked'"
          >
            {{ formatI18n("/会员/会员资料", "已冻结") }}
          </el-tag>
          <el-tag
            size="small"
            type="warning"
            v-if="memberObj.state === 'Unactivated'"
          >
            {{ formatI18n("/会员/会员资料", "未激活") }}
          </el-tag>
          <el-tag
            size="small"
            type="info"
            v-if="memberObj.state === 'Canceled'"
          >
            {{ formatI18n("/会员/会员资料", "已注销") }}
          </el-tag>
        </FormItem>
        <FormItem :label="i18n('会员状态调整为')">
          <el-select v-model="afterState">
            <el-option
              v-for="(item, index) in stateList"
              :key="index"
              :value="item.value"
              :label="item.label"
            ></el-option>
          </el-select>
        </FormItem>
        <FormItem :label="i18n('储值金额')">
          <div
          style="margin-top: 10px"
            v-for="(item, index) in memberDepositList"
            :key="index"
          >
            {{ item.key }}：{{item.value}}
          </div>
        </FormItem>
        <FormItem :label="i18n('/权益/积分/新建积分调整单/条目/当前积分余额')">
          {{ memberObj.points }}
        </FormItem>
        <FormItem :label="i18n('可用券')">
          <div v-if="memberObj.coupons" style="width: 400px">
            {{ memberCouponsList.length
            }}{{ formatI18n("/营销/券礼包活动/券礼包活动/张") }}
            <el-row>
              <el-col
                v-for="(item, index) in memberCouponsList"
                :key="index"
                :span="12"
                >{{ item.name }}</el-col
              >
            </el-row>
          </div>
        </FormItem>
        <FormItem :label="i18n('操作组织')" required>
          <SelectStores v-model="orgId" :appendAttr="getOrgAppendAttr" :isOnlyId="false" :hideAll="true" width="214px"
            :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
          </SelectStores>
        </FormItem>
        <FormItem :label="i18n('操作原因')">
          <el-input type="textarea" style="width: 400px" v-model="remark" :maxlength="200"></el-input>
          <div style="color: #888">
            {{ i18n("说明") }}： <br />
            {{ i18n("操作原因：200个字符，字符形式不限") }}
          </div>
        </FormItem>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./BatchOperateEdit.ts">
</script>

<style lang="scss">
.batch-operate-edit {
  width: 100%;
  // height: 100%;
  overflow: auto;
  background-color: white;
  overflow: auto;
  .qf-form-label {
    width: 120px !important;
  }
  .qf-form-content {
    margin-left: 135px !important;
    line-height: 35px;
  }
  .content {
    padding-top: 30px;
    padding-left: 30px;
  }
  .width-78 {
    width: 78px;
  }
  .width-298 {
    width: 298px;
  }
  .el-textarea__inner {
    height: 100px;
  }
  .inner-content {
    background-color: rgba(249, 249, 249, 1);
    padding-left: 20px;
    margin: 20px 20px 20px 0;
    height: auto;
    margin-top: -7px;
  }
  .el-checkbox {
    margin-right: 10px;
  }
  .weight {
    font-weight: 600;
    color: #515151;
  }
  .qf-form-item .qf-form-content {
    position: relative;
  }
}
</style>