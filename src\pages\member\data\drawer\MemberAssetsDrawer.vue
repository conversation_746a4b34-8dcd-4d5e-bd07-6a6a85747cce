<template>
  <div style="width: 100%">
    <Drawerx ref="drawer" :visible.sync="visible" :title="i18n('转移权益资产')" width="50%"
             contentStyle="background-color: #F2F2F2" class="member-opt-log-drawer">
      <el-form label-width="180px">
        <div style="background-color: white;padding: 20px">
          <el-row style="font-size: 20px;font-weight: 600;margin-bottom: 10px">{{ i18n('当前会员姓名') }}: {{
              dtl.name
            }}
          </el-row>
          <el-row style="color: #888888">
            <el-col :span="8">
              <span>{{ i18n('会员号') }}: </span>
              <span>{{ dtl.crmCode | nullable }}</span>
            </el-col>
            <el-col :span="8">
              <span>{{ i18n('手机号') }}: </span>
              <span>{{ dtl.mobile | nullable }}</span>
            </el-col>
            <el-col :span="8">
              <span>{{ i18n('实体卡号') }}: </span>
              <span style="display: inline-block;margin-top: -9px;"
                    v-if="dtl.hdCardCardNumList && dtl.hdCardCardNumList.length > 0">
              <p v-for="(item, index) in dtl.hdCardCardNumList">
                  <span>{{ item | nullable }}</span>&nbsp;&nbsp;<br/>
              </p>
            </span>
              <span v-else>--</span>
            </el-col>
          </el-row>
          <div style="padding: 10px;background-color: #EBF0FE;margin-top: 10px;">
            <i class="iconfont  ic-info" style="font-size: 18px;color: #20A0FF;position: relative;top: 2px;"></i>
            {{ i18n('确认转移后，此会员积分和储值将调减为零，可用券将全部转赠，电子礼品卡的持卡人将变更为目标会员。') }}
          </div>
          <el-form-item :label="i18n('请选择要转移的权益资产')" required>
            <div style="color: #888888">- 为空的权益资产不允许转移</div>
            <el-checkbox-group style="margin-right: 10px"
                               v-model="form.arr">
              <el-checkbox key="POINTS" label="POINTS" :disabled="availableArr.indexOf('POINTS') === -1">
                {{ i18n('积分') }}: {{ dtl.points | amount }}
              </el-checkbox>
              <br/>
              <el-checkbox key="COUPON" label="COUPON" :disabled="availableArr.indexOf('COUPON') === -1">
                {{ i18n('可用券(张)') }}: {{ dtl.couponCount }}
              </el-checkbox>
              <br/>
              <el-checkbox key="PREPAY" label="PREPAY" :disabled="availableArr.indexOf('PREPAY') === -1">
                {{ i18n('储值余额(元)') }}: {{ dtl.balance | amount }}
              </el-checkbox>
              <br/>
              <el-checkbox key="CARD" label="CARD" :disabled="availableArr.indexOf('CARD') === -1">
                {{ i18n('电子礼品卡(张)') }}: {{ dtl.cardCount }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>

        <div style="text-align: center;position: relative">
          <img src="~assets/image/member/property_move.png" style="height: 80px"/>
          <div style="position: absolute;top:0;width: 100%;height: 100%;text-align: center;line-height: 75px">{{i18n('将权益资产转移至')}}
          </div>
        </div>

        <div style="background-color: white;padding: 20px;line-height: 25px">
          <el-row style="font-size: 20px;font-weight: 600;margin-bottom: 10px;display: flex;align-items: center">
            {{ i18n('目标会员姓名') }}: {{ targetMember.name }}
            <el-button type="primary" style="margin-left: 10px" @click="showMemberSelector">
              <template v-if="targetMember.name">{{i18n('重新选择')}}</template>
              <template v-else>{{i18n('选择会员')}}</template>
            </el-button>
          </el-row>
          <el-row style="color: #888888">
            <el-col :span="8">
              <span>{{ i18n('会员号') }}: </span>
              <span>{{ targetMember.crmCode | nullable }}</span>
            </el-col>
            <el-col :span="8">
              <span>{{ i18n('手机号') }}: </span>
              <span>{{ targetMember.mobile | nullable }}</span>
            </el-col>
            <el-col :span="8">
              <span>{{ i18n('实体卡号') }}: </span>
              <span style="display: inline-block;margin-top: -9px;"
                    v-if="targetMember.hdCardCardNumList && targetMember.hdCardCardNumList.length > 0">
              <p v-for="(item, index) in targetMember.hdCardCardNumList">
                  <span>{{ item | nullable }}</span>&nbsp;&nbsp;<br/>
              </p>
            </span>
              <span v-else>-</span>
            </el-col>
          </el-row>
          <el-row style="color: #888888">
            <el-col :span="8">
              <span>{{ i18n('积分') }}: </span>
              <span>{{ targetMember.points | nullable }}</span>
            </el-col>
            <el-col :span="8">
              <span>{{ i18n('可用券(张)') }}: </span>
              <span>{{ targetMember.couponCount | nullable }}</span>
            </el-col>
            <el-col :span="8">
              <span>{{ i18n('储值余额(元)') }}: </span>
              <span>{{ targetMember.balance | nullable }}</span>
            </el-col>
          </el-row>
          <el-row style="color: #888888">
            <el-col :span="8">
              <span>{{ i18n('电子礼品卡(张)') }}: </span>
              <span>{{ targetMember.cardCount | nullable }}</span>
            </el-col>
          </el-row>
        </div>

        <div style="padding: 20px;line-height: 25px;width: 100%;text-align: center">
          <el-button @click="close">{{i18n('取消')}}</el-button>
          <el-button type="primary" @click="transferMemberBenefit">{{i18n('确认转移')}}</el-button>
        </div>
      </el-form>
      <MemberSelectorDialog ref="memberSelectorDialog" @summit="doSubmitGoods"></MemberSelectorDialog>
    </Drawerx>
    <el-dialog :visible.sync="confirmDialog.visible" style="z-index: 999" width="400px" :title="i18n('提示')">
      <div style="display: flex;align-items: center">
        <img alt="" src="~assets/image/member/warn.png">
        <div style="margin-left: 15px;word-break: break-word">
          <p style="color: red">{{i18n('转移权益资产为不可逆操作，请务必仔细核对。')}}</p>
          {{i18n('请确认是否执行资产转移？')}}
        </div>
      </div>
      <div class="dialog-footer" slot="footer">
        <el-button @click="confirmDialog.visible = false">{{ i18n('取消') }}</el-button>
        <el-button @click="doConfirm('confirm')" size="small" type="primary">
          {{ i18n('已核对，确认转移') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" src="./MemberAssetsDrawer.ts">
</script>

<style lang="scss">
.member-assets-drawer {
}
</style>
