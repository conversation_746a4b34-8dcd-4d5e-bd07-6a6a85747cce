import ApiClient from 'http/ApiClient'
import GainPointsMetaRule from 'model/v2/controller/points/meta/GainPointsMetaRule'
import MemberDayPointsSpeedRule from 'model/v2/controller/points/MemberDayPointsSpeedRule'
import PointsActivityRule from 'model/v2/controller/points/PointsActivityRule'
import Response from 'model/common/Response'

export default class PointsCustomInitializeApi {

  /**
   * DEFAULT-消费金额不足部分不计积分;
   * POINTS_DOWN-全部消费金额都纳入积分计算（应算尽算），去尾取整;
   * POINTS_HALF_UP-全部消费金额都纳入积分计算（应算尽算），四舍五入取整 ；
   * POINTS_DOWN_SCALE_ONE-全部消费金额都纳入积分计算（应算尽算），去尾保留一位小数;
   * POINTS_HALF_UP_SCALE_ONE-全部消费金额都纳入积分计算（应算尽算），四舍五入保留一位小数
   */
  static getGainPointsMetaConfig(): Promise<Response<Nullable<string>>> {
    return ApiClient.server().get(`/v1/custom/points-initialize/getGainPointsMetaConfig`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询基础积分活动规则
   *
   */
  static getGainPointsMeta(): Promise<Response<GainPointsMetaRule>> {
    return ApiClient.server().get(`/v1/custom/points-initialize/getGainPointsMeta`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询基础积分活动规则
   *
   */
  static getMemberDayPointsSpeedRule(): Promise<Response<MemberDayPointsSpeedRule>> {
    return ApiClient.server().get(`/v1/custom/points-initialize/getMemberDayPointsSpeedRule`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 首页积分初始化首页查询规则信息
   *
   */
  static getRules(): Promise<Response<PointsActivityRule>> {
    return ApiClient.server().get(`/v1/custom/points-initialize/getRules`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 保存基础积分活动规则
   *
   */
  static saveGainPointsMeta(body: GainPointsMetaRule): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/custom/points-initialize/saveGainPointsMeta`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 保存会员日积分加速规则
   *
   */
  static saveMemberDayPointsSpeedRule(body: MemberDayPointsSpeedRule): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/custom/points-initialize/saveMemberDayPointsSpeedRule`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 启用或禁用会员日积分加速规则
   *
   */
  static switchMbrDayPtsSdRule(stop: boolean): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/custom/points-initialize/switchMbrDayPtsSdRule/${stop}`, {}, {}).then((res) => {
      return res.data
    })
  }
}
