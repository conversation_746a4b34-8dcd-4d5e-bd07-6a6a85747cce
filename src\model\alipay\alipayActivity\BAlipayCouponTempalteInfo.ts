import { AliCouponType } from './AliCouponType'

// 支付宝券模板信息
export default class BAlipayCouponTempalteInfo {
  // 支付宝小程序券类型
  aliCouponType: Nullable<AliCouponType> = null
  // 批次号
  batchNumber: Nullable<string> = null
  // 售价
  price: Nullable<number> = null
  // 批次名
  batchName: Nullable<string> = null
  // 券类型,满减券：fix_voucher、折扣券：discount_voucher 特价券：special_voucher 兑换券：exchange_voucher
  couponType: Nullable<string> = null
  // 券有效期类型、ABSOLUTE：绝对时间 RELATIVE 相对时间
  validPeriodType: Nullable<string> = null
  // 特殊可选: 券生效后N天内可以使用。 可以配合wait_days_after_receive字段使用
  validDaysAfterReceive: Nullable<number> = null
  // 特殊可选: 用户领券后需要等待N天，券才可以生效。默认用户领券后立刻生效。 限制： 1、type为RELATIVE时可选。
  waitDaysAfterReceive: Nullable<number> = null
  // 有效期
  beginTime: Nullable<Date> = null
  // 有效期
  endTime: Nullable<Date> = null
  // 面额
  faceAmount: Nullable<number> = null
  // 门槛
  threshold: Nullable<number> = null
  // 核销次数
  writtenTime: Nullable<number> = null
  // 核销周期
  writtenCycle: Nullable<string> = null
  // 封顶金额
  maxAmount: Nullable<number> = null
}