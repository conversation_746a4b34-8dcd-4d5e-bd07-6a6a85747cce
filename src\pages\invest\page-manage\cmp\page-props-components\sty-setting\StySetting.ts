import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import emitter from 'util/emitter';
import { FormMode } from 'model/local/FormMode';
import FormDefUtil from 'util/FormDefUtil';
import I18nPage from 'common/I18nDecorator';

@Component({
  name: 'StySetting',
  mixins: [emitter],
  components: {  },
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/公用/菜单',
    '/页面/页面管理',
  ],
  auto: true
})
export default class StySetting extends Vue {
  @Prop({ type: Boolean, default: false })
  readonly: boolean; //
  @Prop({ type: String, default: 'PlaceTitle' })
  validateName: string; // 组件校验名称，editPage通过该名称进行组件校验及校验信息清除
  @Prop({ type: Object })
  value: any; // 数据模型
  formKey: any;
  FormModeType = FormMode;
  $refs: any;
  labelPosition: string = 'top';

  rules = {
    placeName: [{ required: false, message: this.i18n('请输入'), trigger: ['blur', 'change'] }],
  };

  get formMode() {
    if (this.validateName === 'placeTitle') {
      return FormDefUtil.getFormMode(this.readonly, this.validateName, this.formKey);
    } else {
      return FormDefUtil.getFormMode(this.readonly, this.validateName + 'placeTitle', this.formKey);
    }
  }

  handleChange() {
    this.$emit('input', this.value);
    this.$emit('change', this.value);
    this.validate(() => {});
  }

  mounted() {
    if (this.validateName) {
      // this['dispatch']('EditPage', 'nf.edit.addForm', [this]);
    }
  }

  beforeDestroy() {
    // this['dispatch']('EditPage', 'nf.edit.removeForm', [this]);
  }

  validate(callback: () => void) {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.validate(callback);
    }
  }

  clearValidate() {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.clearValidate();
    }
  }
}
