import {Component, Vue} from 'vue-property-decorator'
import WechatHeader from 'cmp/wechatheader/WechatHeader.vue'
import WechatStep from 'cmp/wechatstep/WechatStep.vue'
import StoreCodeDownload from 'pages/member/wx/wechatinit/cmp/StoreCodeDownload.vue'
import AlipayInitApi from 'http/aliPay/v2/AlipayInitApi'
import VueQrcode from 'vue-qrcode'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import I18nPage from 'common/I18nDecorator'

@Component({
  name: 'AlipayMemberQrCode',
  components: {
    WechatHeader,
    WechatStep,
    StoreCodeDownload,
    VueQrcode,
    BreadCrume
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板'
  ],
  auto: true
})
export default class AlipayMemberQrCode extends Vue {
  qrCodeUrl = ''
  active = 2
  loginInfo: any // 登录返回数据
  code: Nullable<string> = null // 登录进去的id
  $refs: any
  panelArray:any = []
  created() {
    this.panelArray = [
      {
        name: this.i18n('支付宝会员设置'),
        url: 'ali-member-setting'
      },
      {
        name: '支付宝会员初始化',
        url: ""
      }
    ]
  }
  mounted() {
    this.loginInfo = localStorage.getItem('ucenterUser'); // 从localStorage中获取登录返回数据
    if (this.loginInfo) {
      this.loginInfo = JSON.parse(this.loginInfo);
      this.code = this.loginInfo.id
    }
  }

  // 下载全部门店二维码
  goAllStoresQrCode() {
    this.loginInfo = localStorage.getItem('ucenterUser'); // 从localStorage中获取登录返回数据
    if (this.loginInfo) {
      this.loginInfo = JSON.parse(this.loginInfo);
      this.code = this.loginInfo.uuid
    }
    AlipayInitApi.getStoreQrCode().then((res: any) => {
      if (res && res.code === 2000) {
        // window.open(res.data, '_blank');
        this.qrCodeUrl = res.data
        setTimeout(() => {
          this.doDownLoadQRCode()
        }, 100)
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  // 下载指定门店二维码
  goStoreQrCode() {
    // (this.$refs.downloadDialog as any).open()
    AlipayInitApi.downLoadStoreQrCodes().then((res: any) => {
      if (res && res.code === 2000) {
        // window.open(res.data, '_blank');
        this.$message.success('已成功加入预约文件列表，请前往查看')
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  // 上一步
  toWechatMemberCard() {
    this.$router.push({name: 'alipay-member-card-edit'})
  }

  // 下一步
  toWechatPayRuleEdit() {
    this.$router.push({name: 'alipay-member-card-edit'})
  }
  doCompleted() {
    this.$router.push({ name: 'ali-member-setting'})
  }
  doDownLoadQRCode() {
    /* 下载二维码*/
    let img: any = document.getElementById('qrcode')
    let link = document.createElement('a')
    let url = img.getAttribute('src')
    link.setAttribute('href', url as any)
    link.setAttribute('download', '支付宝卡包领卡链接二维码.png')
    link.click()
  }
}