import {Component, Prop, Watch} from 'vue-property-decorator'
import DateTimeCondition from "model/common/DateTimeCondition";
import DateTimeConditionForm from "cmp/date-time-condition-picker/DateTimeConditionForm";
import DateTimeConditionPickerParent from "cmp/date-time-condition-picker/DateTimeConditionPickerParent";

@Component({
  name: 'DateTimeConditionDtl',
})
export default class DateTimeConditionDtl extends DateTimeConditionPickerParent {
  @Prop({
    default: new DateTimeCondition()
  })
  value: DateTimeCondition

  form: DateTimeConditionForm = new DateTimeConditionForm()

  @Watch('value', {deep: true, immediate: true})
  watchModelValue(value: DateTimeCondition) {
    this.form.of(value)
  }
}
