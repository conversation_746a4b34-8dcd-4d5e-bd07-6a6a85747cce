import { Vue, Component, Prop } from 'vue-property-decorator';

@Component({
  name: 'DomDialog',
  components: {},
})
export default class DomDialog extends Vue {
  @Prop({ type: String, default: '提示' }) title: string; // 提示文案
  @Prop() className: string; //定制样式
  @Prop({ type: String, default: '确定' }) confirmText: string; // 确定按钮文案

  @Prop({ type: Boolean, default: true }) showClose: boolean; // 是否显示右上角X

  @Prop({ type: Boolean, default: true }) isConfirmShow: boolean; // 确定按钮显示
  @Prop({ type: Boolean, default: false }) isConfirmDisabled: boolean; // 确定按钮是否禁用

  @Prop({ type: String, default: '取消' }) cancelText: string; // 取消按钮文案
  @Prop({ type: Boolean, default: true }) isCancelShow: boolean; // 取消按钮显示

  @Prop({ type: Boolean, default: false }) isDialogShow: boolean; // 显示模态框
  @Prop({ type: String, default: '800px' }) width: string; // 模态框宽度
  @Prop({ type: Boolean, default: false }) closeOnClickModal: boolean; // 点击周围是否可以关闭弹框
  @Prop({ type: Boolean, default: true }) destroyOnClose: boolean; //关闭时销毁 Dialog 中的元素
  @Prop({ type: String, default: 'left' }) otherFloat: string; // 其他对其方式
  @Prop({ type: Boolean, default: false }) buttonLoading: boolean;
  @Prop({ type: String, default: 'primary' }) confirmType: string;
  @Prop({ type: Boolean, default: false }) appendToBody: string; //否插入至 body 元素上

  /**当存在before-close的时候，会阻止原hide()函数的执行导致无法关闭，故需要外部执行关闭调用*/
  doBeforeClose() {
    this.$emit('before-close');
  }

  doCancel() {
    this.$emit('cancel');
  }

  doConfirm() {
    this.$emit('confirm');
  }
}
