import MemberDayPointsChargeRule from 'model/points/init/MemberDayPointsChargeRule'
import MemberDayPointsSpeedRule from 'model/points/init/MemberDayPointsSpeedRule'
import PointsChargeMetaRule from 'model/points/init/PointsChargeMetaRule'
import GainPointsMetaRule from "model/v2/controller/points/meta/GainPointsMetaRule";

export default class PointsActivityRule {
    // 基础积分活动规则
    gainPointsMetaRule: Nullable<GainPointsMetaRule> = null
    // 会员日积分加速规则
    memberDayPointsSpeedRule: Nullable<MemberDayPointsSpeedRule> = null
    // 积分抵现规则
    pointsChargeMetaRule: Nullable<PointsChargeMetaRule> = null
    // 会员日积分加速抵现规则
    memberDayPointsChargeRule: Nullable<MemberDayPointsChargeRule> = null
}