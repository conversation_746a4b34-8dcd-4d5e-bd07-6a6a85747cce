import GiftInfo from "model/common/GiftInfo";

export default class CardDepositRuleLine {
  // 充值金额
  faceAmount: Nullable<number> = null
  // 折扣
  discount: Nullable<number> = null
  // 返现
  rebateAmount: Nullable<number> = null
  // 是否选中返现
  rebateCheck: Nullable<Boolean> = false
  // 百分比率
  rebatePercentage: Nullable<number> = null
  // 充值赠礼类型
  rebateType: Nullable<'amount' | 'percentage'> = 'amount'
  // 赠礼
  gift: Nullable<GiftInfo> = null
}