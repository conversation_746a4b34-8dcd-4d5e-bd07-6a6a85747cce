import CustomerProfileGoupSection from 'model/default/CustomerProfileGoupSection'
import { AnalysisPropType } from 'model/default/AnalysisPropType'
import { IntervalType } from 'model/default/IntervalType'

export default class MemberAnalysisDimension {
  // 
  type: Nullable<AnalysisPropType> = null
  // 
  memberProp: Nullable<string> = null
  //
  memberPropName: Nullable<string> = null
  // 
  tagId: Nullable<string> = null
  //
  tagName: Nullable<string> = null
  // 
  displayIntervalType: Nullable<IntervalType> = null
  // 
  displayIntervals: CustomerProfileGoupSection[] = []
  // 
  value: Nullable<string> = null

  // 
  fieldType: Nullable<string> = null
}