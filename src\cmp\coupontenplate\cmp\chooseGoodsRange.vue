<template>
  <div class="choose-goods-range">
    <div v-if="typeValue === 'PART'">
      <div>
        <span class="plain-btn-blue" @click="doAdd" v-if="isChooseGoods !== false || isSelectSpecialGoods" style="margin-right:8px">
          {{i18n('添加商品')}}
        </span>
        <span class="plain-btn-blue" @click="doAddCategory" v-if="isChooseGoods !== true && canAddCategory">
          {{i18n('添加品类')}}
        </span>
        <span class="plain-btn-blue" v-if="isImport && isChooseGoods !== false" @click="doImport" style="margin-left:8px">
          {{ i18n('导入商品') }}
        </span>
        <span class="gray-tips" style="magin-left:10px">
          {{i18n('请至少选择1件商品/品类；同一商品不允许重复选')}}
        </span>
      </div>
      <el-table v-if="data.length > 0 && !isSelectByCode" :data="currentTableData" style="width: 100%" stripe>
        <el-table-column :label="isChooseGoods === false ? i18n('品类') : i18n('商品')" fixed prop="goods">
          <template slot-scope="scope">
            <template v-if="isChooseGoods === true">[{{scope.row.goods.id}}]{{scope.row.goods.name}}</template>
            <template v-else-if="isChooseGoods === false">[{{scope.row.category.id}}]{{scope.row.category.name}}</template>
          </template>
        </el-table-column>
        <el-table-column :label="formatI18n('/公用/券模板/提货券/用券商品', '售价(元)')" fixed prop="price" v-if="isChooseGoods === true">
          <template slot-scope="scope">
            <div v-if="scope.row.price">￥{{scope.row.price | fmt}}</div>
            <div v-else>--</div>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('商品数量(件)')" fixed prop="qty">
          <template slot-scope="scope">
            <div style="display: flex; justify-content: flex-start;">
              <el-input style="width: 80px" class="number-type" v-model="scope.row.qty" type="number" @change="doChange(scope.$index)">
              </el-input>
              <el-select v-model="scope.row.isDisp" style="width: 70px" @change="doChange(scope.$index)">
                <el-option v-for="item in unitList" :key="item.label" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="formatI18n('/设置/权限/角色管理/包含用户','操作')" fixed prop="option">
          <template slot-scope="scope">
            <el-button type="text" @click="doClear(scope.$index)">{{formatI18n('/公用/按钮','清空')}}</el-button>
            <el-button type="text" @click="doDelete(scope.$index)">{{formatI18n('/公用/按钮','删除')}}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 特殊商品，不展示售价、规格 -->
      <el-table v-else-if="isSelectByCode" :data="currentTableData" style="width: 100%" stripe>
        <el-table-column :label="i18n('商品代码')" fixed prop="goods.id">
          <template slot-scope="scope">
            <span class="overflow-text" :title="scope.row.goods.id">{{ scope.row.code }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('商品名称')" fixed prop="goods.name" width="264">
          <template slot-scope="scope">
            <span class="overflow-text" :title="scope.row.goods.name">{{ scope.row.goods.name }}</span>
          </template>
        </el-table-column>
        <el-table-column fixed prop="limit" width="126">
          <template slot="header">
            <div style="display:flex;align-items:center">
              <span>{{i18n('/营销/限量抢购活动/限量')}}</span>
              <el-tooltip class="item" effect="dark" :content="i18n('每人每天最多购买数量')" placement="top-start">
                <i class="el-icon-warning-outline" style="margin-left:4px"></i>
              </el-tooltip>
            </div>
          </template>
          <template slot-scope="scope">
            <AutoFixInput :min="1" :max="9999" v-model="scope.row.qty" :fixed="0" style="width: 100px" :appendTitle="i18n('件')"
              :placeholder="i18n('请输入')">
            </AutoFixInput>
          </template>
        </el-table-column>
        <el-table-column :label="formatI18n('/设置/权限/角色管理/包含用户','操作')" fixed prop="option">
          <template slot-scope="scope">
            <span class="span-btn" @click="doDelete(scope.$index)">{{formatI18n('/公用/按钮','删除')}}</span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination v-if="data.length > page.pageSize" @current-change="onHandleCurrentChange" :page-size="page.pageSize"
        :current-page.sync="page.currentPage" layout="total, prev, pager, next,  jumper" :total="data.length" small>
      </el-pagination>
    </div>
    <GoodsSelectorDialog :appreciationGoods="appreciationGoods" :chooseGoodType="chooseGoodType" :isSelectSpecialGoods="isSelectSpecialGoods" :goodsMatchRuleMode="goodsMatchRuleMode" ref="selectGoodsScopeDialog"
      @summit="doSubmitGoods" />
    <CatogorySelectorDialog ref="selectCatogoryScopeDialog" @summit="doSubmitCategorys" />
    <ImportDialog ref="importDialog" @upload-success="doUploadSuccess" :importNumber="20000" :showClear="true" @clearChange="clearChange"
      :importUrl="getImportUrl" :templatePath="getTemplateFilePath" :appreciationGoods="appreciationGoods"
      :templateName="formatI18n('/公用/券模板/全场现金券/用券商品/指定不可用商品/点击导入/导入商品范围模板')" :title="formatI18n('/公用/券模板/导入')">
    </ImportDialog>
  </div>
</template>

<script lang="ts" src="./chooseGoodsRange.ts">
</script>

<style lang="scss">
.choose-goods-range {
  width: 740px;

  .el-table th {
    background-color: #f0f2f6;
    & > .cell {
      font-size: 13px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #242633;
    }
  }
}
.goods-range-top {
  position: relative;
  height: 32px;
  line-height: 32px;
  background: #ced0da;
  padding-left: 20px;

  .btn {
    position: absolute;
    right: 20px;
    top: 2px;
  }
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

input[type="number"] {
  -moz-appearance: textfield;
}

.custom-switch {
  .el-switch__core {
    width: 54px;
  }
  .el-switch__core::before {
    content: "禁用";
    position: absolute;
    top: 0;
    right: 5px;
    color: #fff;
  }
  .is-checked .el-switch__core::before {
    content: "启用";
    position: absolute;
    top: 0;
    left: 5px;
    color: #fff;
  }
}
</style>