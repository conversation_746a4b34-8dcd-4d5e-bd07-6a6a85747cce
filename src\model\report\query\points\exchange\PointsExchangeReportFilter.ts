import BaseReportFilter from 'model/report/query/BaseReportFilter'

export default class PointsExchangeReportFilter extends BaseReportFilter {
  // 会员识别码等于
  identCodeEquals: Nullable<string> = null
  // 活动名称类似于
  activityNameLikes: Nullable<string> = null
  // 交易时间在···之间
  tranTimeBetween: Date[] = []
  // 组织id等于
  occurredOrgIdEquals: Nullable<string> = null
  // 交易号类似于
  transNoLikes: Nullable<string> = null
  // 兑换对象类似于
  exchangeObjLikes: Nullable<string> = null
  // 类型等于
  typeEquals: Nullable<string> = null
  // 会员识别码类似于(大桥石化IC卡)
  identIdLikes: Nullable<string> = null
}