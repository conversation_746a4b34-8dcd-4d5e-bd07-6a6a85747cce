<!--
 * @Author: 黎钰龙
 * @Date: 2025-02-13 10:28:56
 * @LastEditTime: 2025-02-28 16:08:01
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\analysis\cmp\SumCycleDialog\SumCycleDialog.vue
 * 记得注释
-->
<template>
  <el-dialog width="640px" :close-on-click-modal="false" :visible.sync="visible" append-to-body>
    <FormItem :label="titleList[index]" v-for="(item,index) in dataList" :key="index">
      <div style="line-height: 36px;">
        <el-radio-group v-model="dataList[index]">
          <el-radio label="year">{{i18n('按年汇总')}}</el-radio>
          <el-radio label="month">{{i18n('按月汇总')}}</el-radio>
          <el-radio label="date">{{i18n('按日汇总')}}</el-radio>
        </el-radio-group>
      </div>
    </FormItem>
    <div style="text-align: right; margin: 0">
      <el-button size="mini" type="text" @click="visible = false">{{ i18n("/公用/按钮/取消") }}</el-button>
      <el-button @click="doSubmit" type="primary" size="mini">{{ i18n("/公用/按钮/确定") }}</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./SumCycleDialog.ts">
</script>

<style lang="scss" scoped>
::v-deep .qf-form-label{
  width: 180px !important;
}
</style>