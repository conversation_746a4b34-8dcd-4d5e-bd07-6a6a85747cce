<template>
  <div class="prepay-card-adjust-dtl">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button key="1" type="primary" @click="doAudit" v-if="billDtl.state !== 'AUDITED' && hasOptionPermission('/卡/卡管理/预付卡充值单', '单据审核')">
          {{i18n('审核')}}
        </el-button>
        <el-button key="2" @click="doModify"
          v-if="billDtl.source === 'create' && billDtl.state !== 'AUDITED' && hasOptionPermission('/卡/卡管理/预付卡充值单', '单据维护')">
          {{i18n('修改')}}
        </el-button>
        <el-button key="3" v-if="billDtl.source === 'impt' && billDtl.state !== 'AUDITED' && hasOptionPermission('/卡/卡管理/预付卡充值单', '单据维护')"
          @click="doImportModify">
          {{i18n('导入修改')}}
        </el-button>
      </template>
    </BreadCrume>
    <div class="top-wrap">
      <div class="left">
        <div class="back">
          <img src="~assets/image/storevalue/back.png" />
        </div>
      </div>
      <div class="right">
        <div class="top">
          <div class="item1">
            <div class="bill"><span>{{i18n('单号')}}：</span>{{ billDtl.billNumber }}</div>
            <div class="name">{{i18n('预付卡充值单')}}</div>
          </div>
          <div class="item2">
            <div class="desc">{{i18n('状态')}}</div>
            <div class="state">
              <el-tag type="success" v-if="billDtl.state === 'AUDITED'">{{
                i18n("已审核")
              }}</el-tag>
              <el-tag type="warning" v-if="billDtl.state !== 'AUDITED'">{{
                i18n("未审核")
              }}</el-tag>
            </div>
          </div>
        </div>
        <div class="bottom">
          <div class="bottom-card-adjust-dtl">{{i18n('客户')}}：&emsp; {{clientInfo || '-'}}</div>
          <div class="bottom-card-adjust-dtl">{{i18n('充值类型')}}：&emsp;
            <template v-if="billDtl.depositType === 'DEPOSIT'">
              {{i18n('充值')}}
            </template>
            <template v-else-if="billDtl.depositType === 'REFUND'">
              {{i18n('充值退')}}
            </template>
          </div>
          <template v-if="billDtl.depositType === 'DEPOSIT'">
            <div class="account-info" v-if="queryDtl && queryDtl.length !== 0">
              {{i18n('本金总金额')}}：&emsp; {{queryDtlTotalAmount.totalOccurAmount}}{{i18n('元')}};&emsp;{{i18n('赠金总金额')}}：&emsp;
              {{queryDtlTotalAmount.totalOccurGiftAmount}} {{i18n('元')}}
            </div>
            <div class="bottom-card-adjust-dtl">{{i18n('优惠总金额')}}：&emsp; {{billDtl.favAmount || 0}} {{i18n('元')}}</div>
            <div class="bottom-card-adjust-dtl">{{i18n('应付金额')}}：&emsp; {{queryDtlTotalAmount.totalOccurAmount - billDtl.favAmount}} {{i18n('元')}}
            </div>
          </template>
          <template v-else>
            <div class="account-info" v-if="queryDtl && queryDtl.length !== 0">
              {{i18n('本金退总金额')}}：&emsp; {{queryDtlTotalAmount.totalOccurAmount}}{{i18n('元')}};&emsp;{{i18n('赠金退总金额')}}：&emsp;
              {{queryDtlTotalAmount.totalOccurGiftAmount}} {{i18n('元')}}
            </div>
            <div class="bottom-card-adjust-dtl">{{i18n('优惠退总金额')}}：&emsp; {{billDtl.favAmount || 0}} {{i18n('元')}}</div>
            <div class="bottom-card-adjust-dtl">{{i18n('应退金额')}}：&emsp; {{queryDtlTotalAmount.totalOccurAmount - billDtl.favAmount}} {{i18n('元')}}
            </div>
          </template>
          <div class="bottom-card-adjust-dtl">
            <div style="display: flex;">
              <span>{{i18n('支付方式')}}：</span>
              <el-table :data="billDtl.payments" style="margin-left: 30px; margin-top: 10px;" stripe :empty-text="i18n('暂无支付记录')">
                <el-table-column prop="payMethod" :label="i18n('支付方式')" width="200">
                  <template slot-scope="scope">
                    {{scope.row.paymentName}}
                  </template>
                </el-table-column>
                <el-table-column prop="payAmount" :label="i18n('支付金额')+'('+ i18n('元') +')'" width="156">
                  <template slot-scope="scope">
                    {{scope.row.paymentAmount}}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="bottom-card-adjust-dtl">{{i18n('支付总金额')}}：&emsp; {{ totalPayAmount }} {{i18n('元')}}</div>
          <!--          <div class="bottom-card-adjust-dtl">{{i18n('说明')}}：&emsp; {{billDtl.remark}} </div>-->

        </div>
      </div>
    </div>
    <div style="height: 20px; background-color: rgba(242, 242, 242, 1)"></div>
    <div class="center-wrap">
      <div>
        <p class="sub-item">{{i18n('明细')}}</p>
        <el-table :data="queryDtl">
          <el-table-column fixed :label="i18n('序号')" prop="lineNo">
            <template slot-scope="scope">
              <span no-i18n>{{ scope.row.lineNo }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed :label="i18n('卡号')" prop="accountOwner" width="200">
            <template slot-scope="scope">
              <span v-if="scope.row.accountOwner && scope.row.accountOwner.id" no-i18n>{{ scope.row.accountOwner.id }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed :label="i18n('卡类型')" prop="cardType">
            <template slot-scope="scope">
              <span no-i18n>{{ scope.row.cardType }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('实充')" prop="cardType">
            <template slot-scope="scope">
              <span no-i18n>{{ scope.row.occurAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('返现')" prop="cardType">
            <template slot-scope="scope">
              <span no-i18n>{{ scope.row.occurGiftAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('发生组织')" prop="cardType">
            <span no-i18n v-if="billDtl.occurredOrg">
              <span v-if="billDtl.occurredOrg.id">[{{billDtl.occurredOrg.id}}] </span>
              {{ billDtl.occurredOrg.name }}
            </span>
          </el-table-column>
          <!-- <el-table-column :label="i18n('调整原因')" prop="cardType">
            <template slot-scope="scope">
              <span no-i18n>{{ scope.row.reason | strFormat }}</span>
            </template>
          </el-table-column> -->
          <el-table-column :label="i18n('说明')" prop="remark">
            <template slot-scope="scope">
              <div no-i18n :title="scope.row.remark" style="
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                ">
                {{ scope.row.remark | strFormat }}
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div style="margin-top: 15px">
          <el-pagination no-18n :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
            @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background layout="total, prev, pager, next, sizes,  jumper">
          </el-pagination>
        </div>
      </div>
    </div>
    <div class="row-height"></div>
    <div class="foot-wrap">
      <div>
        <p class="sub-item">{{i18n('操作日志')}}</p>
        <el-table :data="billDtl.logs">
          <el-table-column :label="i18n('操作类型')" prop="type">
            <template slot-scope="scope">
              <span no-i18n>{{ scope.row.type }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('操作人')" prop="operator">
            <template slot-scope="scope">
              <span no-i18n>{{ scope.row.operator }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('操作时间')" prop="occurredTime">
            <template slot-scope="scope">
              <span no-i18n>{{ scope.row.occurredTime | dateFormate3 }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <PrepayCardImportDialog ref="prepayCardImportDialog" @uploadSuccess="doUploadSuccess" :clientRequired="clientRequired">
    </PrepayCardImportDialog>
    <DownloadCenterDialog :dialogvisiable="fileDialogvisiable" :showTip="true" @dialogClose="doDownloadDialogClose">
    </DownloadCenterDialog>
  </div>
</template>

<script lang="ts" src="./PrepayCardPayDtl.ts">
</script>

<style lang="scss">
.prepay-card-adjust-dtl {
  width: 100%;
  height: 100%;
  background-color: white;
  overflow: auto;
  .top-wrap {
    display: flex;
    flex-direction: row;
    .left {
      width: 80px;
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
      .back {
        width: 48px;
        height: 48px;
        border-radius: 100%;
        background-color: rgba(242, 242, 242, 1);
        img {
          width: 24px;
          height: 24px;
          position: relative;
          top: 13px;
          left: 12px;
        }
      }
    }
    .right {
      display: flex;
      flex: 1;
      flex-direction: column;
      position: relative;
      .top {
        display: flex;
        height: 105px;
        border-bottom: 1px solid rgba(242, 242, 242, 1);
        margin-right: 20px;
        .item1 {
          .bill {
            margin-top: 16px;
            color: rgba(51, 51, 51, 0.***************);
          }
          .name {
            font-weight: 500;
            margin-top: 8px;
            font-size: 20px;
          }
        }
        .item2 {
          padding-left: 70px;
          padding-top: 16px;
          .desc {
            color: rgba(51, 51, 51, 0.***************);
          }
          .state {
            font-weight: 500;
            margin-top: 8px;
            font-size: 20px;
          }
        }
      }
      .bottom {
        .bottom-card-adjust-dtl {
          margin-top: 10px;
          .el-table::before {
            height: 0;
          }
          .el-table__body .el-table__row td {
            border-bottom: 0 !important;
          }
        }
        padding-bottom: 20px;
        .account-info {
          margin-top: 10px;
        }
        .red {
          color: red;
        }
        .green {
          color: #008000;
        }
      }
    }
  }
  .row-height {
    height: 20px;
    background-color: rgba(242, 242, 242, 1);
  }
  .center-wrap,
  .foot-wrap {
    padding: 20px;
  }
  .sub-item {
    font-size: 16px;
    padding-top: 20px;
    margin-bottom: 10px;
  }
  .el-table__body .el-table__row td {
    border-bottom: 1px solid #d7dfeb !important;
  }
}
</style>