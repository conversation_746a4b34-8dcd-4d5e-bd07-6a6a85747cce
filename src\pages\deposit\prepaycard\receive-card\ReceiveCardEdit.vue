<!--
 * @Author: 袁智鹏
 * @Date: 2024-07-29 10:40:06
 * @LastEditTime: 2025-05-07 09:56:14
 * @LastEditors: 黎钰龙
 * @Description:
 * @FilePath: \phoenix-web-ui\src\pages\deposit\prepaycard\receive-card\ReceiveCardEdit.vue
 * 记得注释
-->
<template>
  <div class="receive-card-edit-container">
    <BreadCrume :panelArray="panelArray" :fontSize="18">
      <template slot="operate">
        <el-button
            v-if="hasOptionPermission('/卡/卡管理/领卡单', '单据维护')"
            @click="doSave"
            type="primary"
        >保存
        </el-button
        >
        <el-button
            v-if="hasOptionPermission('/卡/卡管理/领卡单', '单据审核')"
            @click="doSaveAndAudit"
        >保存并审核
        </el-button
        >
        <el-button
            v-if="hasOptionPermission('/卡/卡管理/领卡单', '单据维护')"
            @click="doCancel"
        >取消
        </el-button
        >
      </template>
    </BreadCrume>
    <div class="content">
      <FormItem label="起始卡号" :required="true">
        <el-input
            @change="doStartCodeChange"
            class="width-298"
            v-model="params.startCardCode"
        ></el-input>
        <div style="height: 20px;">
          <div v-if="isEmpty" class="red">{{i18n('卡号不能为空')}}</div>
          <div v-else-if="isExist === false" class="red">{{i18n('卡号不存在')}}</div>
          <div v-else-if="isMade === false" class="red">{{i18n('/卡/领卡单/卡状态只能为已制卡/空白卡/已回收/坏卡')}}</div>
        </div>
      </FormItem>
      <FormItem label="结束卡号">
        <el-input
            @change="doEndCodeChange"
            class="width-298"
            v-model="params.endCardCode"
        ></el-input>
        <div style="height: 20px;">
          <div class="red" v-if="!isEmpty && !isEndEmpty && !lengthEquals">{{i18n('结束卡号和起始卡号长度需一致')}}</div>
          <div class="red" v-if="!isEndExist && !isEndEmpty">{{i18n('卡号不能为空')}}</div>
          <div v-else-if="isEndExist && !isEndEmpty && !isEndMade" class="red">{{i18n('卡为非已制作卡')}}</div>
        </div>
      </FormItem>
      <FormItem label="卡数量" style="margin-bottom: 20px">
<!--        <el-input-->
<!--            class="width-298"-->
<!--            disabled-->
<!--            v-model="cardCount"-->
<!--        ></el-input>-->
        <div style="padding-top: 10px">{{cardCount}}</div>
      </FormItem>
      <FormItem label="领出组织" :required="true" style="margin-bottom: 20px">
        <SelectStores v-model="params.outOrg" :disabled="!isExist"
                      :isOnlyId="false" :hideAll="true" width="298px"
                      :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
        </SelectStores>
      </FormItem>
      <FormItem label="领入组织" :required="true" style="margin-bottom: 20px">
        <SelectStores v-model="params.inOrg" :disabled="!isExist"
                      :isOnlyId="false" :hideAll="true" width="298px"
                      :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
        </SelectStores>
      </FormItem>
      <FormItem label="备注" style="margin-bottom: 20px">
        <el-input type="textarea" :disabled="!isExist" :placeholder="i18n('请输入不超过200个字')"
                  v-model="params.remark" maxlength="200" show-word-limit style="width: 400px"/>
      </FormItem>
    </div>
  </div>
</template>


<script lang="ts" src="./ReceiveCardEdit.ts">
</script>

<style lang="scss"scoped>
.receive-card-edit-container {
  width: 100%;
  height: 100%;
  background-color: white;
  overflow: auto;

  .content {
    padding: 30px;
  }

  .width-78 {
    width: 78px;
  }

  .width-298 {
    width: 298px;
  }

  .el-textarea__inner {
    height: 100px;
  }

  .inner-content {
    width: 400px;
    background-color: rgba(249, 249, 249, 1);
    margin-left: 40px;
    height: auto;
    padding: 10px 0;
  }

  .el-checkbox {
    margin-right: 10px;
  }

  .weight {
    font-weight: 600;
    color: #515151;
  }

  .qf-form-item .qf-form-content {
    position: relative;
    margin-left: 105px !important;
  }

  .qf-form-item .qf-form-label {
    width: 110px !important;
  }

  .red {
    color: #f56c6c;
  }

  .short {
    width: 100px;
  }

  .inputs-area {
    margin-left: 20px;
    margin-bottom: 20px;
    padding-top: 3px;

  }
}

.form-item-extra {
  display: flex;
  flex-direction: column;
  height: auto;
  overflow: hidden;
}
</style>