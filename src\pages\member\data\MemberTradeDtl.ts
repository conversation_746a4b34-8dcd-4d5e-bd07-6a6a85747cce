/*
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:05
 * @LastEditTime: 2025-02-21 14:37:20
 * @LastEditors: 黎钰龙
 * @Description:
 * @FilePath: \phoenix-web-ui\src\pages\member\data\StandardMemberDtl.ts
 * 记得注释
 */
import { Component, Vue, Watch } from "vue-property-decorator";
import BreadCrume from "cmp/bread-crumb/BreadCrume.vue";
import FormItem from "cmp/formitem/FormItem.vue";
import MemberApi from "http/member_standard/MemberApi";
import ConstantMgr from "mgr/ConstantMgr";
import I18nPage from "common/I18nDecorator";
import BMemberTrade from "model/member/BMemberTrade";
import BTradeDetailFilter from "model/member/BTradeDetailFilter";
import MemberTab from "pages/member/data/cmp/MemberTab";
import MutableNsid from "model/common/MutableNsid";
import BTradeGoods from "model/member/BTradeGoods";

@Component({
  name: "MemberTradeDtl",
  components: {
    BreadCrume,
    FormItem,
    MemberTab,
  },
})
@I18nPage({
  prefix: [
    "/公用/券模板",
    "/会员/会员资料",
    "/券/延期申请",
  ],
  auto: true,
})
export default class MemberTradeDtl extends Vue {

  panelArray: any = [];

  created() {

  }

  @Watch("$route", { immediate: true })
  onRouteChanged() {
    this.panelArray = [
      {
        name: this.formatI18n("/会员/会员资料", this.isStandardMember ? "会员资料" : "营销中心会员"),
        url: this.isStandardMember ? "standard-member-list" : "marketcenter-member-list",
      },
      {
        name: this.formatI18n("/会员/会员资料", "会员详情"),
        url: this.isStandardMember ? "standard-member-dtl" : "marketcenter-member-dtl",
        query: { id: this.memberId },
      },
      {
        name: this.i18n(this.isSale ? "销售单" : "售后单") + this.i18n("详情"),
        url: "",
      },
    ];
    this.currentTabIndex = this.isSale ? 0 : 1;
    this.getDtl();
  }

  get isStandardMember() {
    return this.$route.fullPath.indexOf("/standard-member") != -1;
  }

  get permissionResourceId() {
    return this.isStandardMember ? "/会员/会员管理/会员资料" : "/会员/会员管理/营销中心会员";
  }

  get memberId() {
    return this.$route.query.memberId as string || "";
  }

  get isSale() {
    return this.$route.query.sale == "true";
  }

  dtl: BMemberTrade = new BMemberTrade();

  getDtl() {
    const loading = this.$loading(ConstantMgr.loadingOption);
    const filter = new BTradeDetailFilter();
    filter.tradeId = { id: this.$route.query.tradeId as string, namespace: this.$route.query.namespace as string };
    MemberApi.tradeDetail(filter).then((resp: any) => {
      if (resp?.code === 2000) {
        this.dtl = resp.data;
        //this.dtl.sourceTradeId = {id:'798737938102-06110007',namespace:'-'}
      } else {
        this.$message.error(resp.msg);
      }
    }).catch((error) => {
      this.$message.error(error.message);
    }).finally(() => {
      loading.close();
    });
    // this.mockData();
  }

  currentTabIndex: number = 0;
  tabs: Array<string> = [this.i18n("支付明细"), this.i18n("交易商品明细"), this.i18n("交易优惠明细")];

  onTabChange(index: number) {
    this.currentTabIndex = index;
  }

  onTradeNoClick(source: MutableNsid) {
    this.$router.push({
      name: this.isStandardMember ? "standard-member-trade-dtl" : "marketcenter-member-trade-dtl",
      query: { memberId: this.memberId, tradeId: source.id, namespace: source.namespace, sale: "true" },
    });
  }

  getTableAmount(item: BTradeGoods) {
    if (this.isSale) return item.stdAmount;
    const stdAmount = item.stdAmount || 0;
    const favAmount = item.favAmount || 0;
    return stdAmount - favAmount;
  }

  mockData() {
    this.dtl = {
      uuid: "fdsf111",
      tranTime: new Date(),
      tradeNo: "12342",
      channel: { id: "1", type: "meituan", typeId: "meituan1" },
      channelName: "meituan1",
      occurredOrgId: "12",
      occurredOrgName: "hheh",
      posNo: "pos111",
      stdAmount: 100,
      total: 60,
      favAmount: 40,
      sourceTradeId: { id: "223", namespace: null },
      payLines: [{ channelName: "支付宝", amount: 20 }, { channelName: "微信", amount: 10 }],
      goods: [{ barcode: "213333", name: "哇哈哈", qpcStr: "1*12", price: 4, qty: 2, stdAmount: 8, favAmount: 2 }],
      favLines: [{ name: "哇哈哈", favourType: "前台促销", favAmount: 5 }],
    };
  }
}
