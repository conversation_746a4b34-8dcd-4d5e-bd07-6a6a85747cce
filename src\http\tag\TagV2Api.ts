import ApiClient from "http/ApiClient";
import Response from "model/common/Response";
import TagOption from "model/tag/TagOption";
import BTagFilter from "model/tag/BTagFilter";

export default class TagV2Api {
  /**
   * 查询会员标签
   * 查询会员标签。
   *
   */
  static list(): Promise<Response<TagOption[]>> {
    return ApiClient.server().post(`/v2/member/tag/list`, {}, {}).then((res) => {
      return res.data;
    });
  }

  /**
   * 查询会员标签
   * 查询会员标签。
   *
   */
  static filterList(filter: BTagFilter): Promise<Response<TagOption[]>> {
    return ApiClient.server().post(`/v2/member/tag/list`, filter, {}).then((res) => {
      return res.data;
    });
  }
}
