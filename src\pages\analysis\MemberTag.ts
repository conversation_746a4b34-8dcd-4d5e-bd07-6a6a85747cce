import {Component, Vue} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import ListWrapper from 'cmp/list/ListWrapper.vue'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import CodeInput from 'pages/analysis/cmp/CodeInput.vue'
import TagApi from 'http/tag/TagApi'
import TagOption from 'model/tag/TagOption'
import TagOptionFilter from 'model/tag/TagOptionFilter'
import TagEditDialog from 'pages/analysis/cmp/TagEditDialog.vue'
import BreadCrume from 'cmp/bread-crumb/BreadCrume'

@Component({
  name: 'MemberTag',
  components: {
    FormItem,
    ListWrapper,
    SubHeader,
    CodeInput,
    TagEditDialog,
    BreadCrume
  }
})
export default class MemberTag extends Vue {
  id = ''
  dialogShow = false
  dialogData: any = {}
  tags = []
  searchContent = ''
  values: any = []
  selectedArr: any[] = []
  singleAll = false
  $refs: any
  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  tableData: any[] = []
  panelArray: any = []
  created() {
    this.panelArray = [
      {
        name: this.formatI18n('/公用/菜单/自定义标签'),
        url: ''
      }
    ]
    this.getMemberTagList()
  }

  checkedAllRow() {
    if (this.singleAll) {
      for (let row of this.tableData) {
        this.$refs.table.toggleRowSelection(row, true)
      }
    } else {
      this.$refs.table.clearSelection();
    }
  }

  onSelectCode(arr: any) {
    this.values = arr
  }

  doAdd() {
    if (!this.id) {
      this.$message.warning(this.formatI18n('/分析/自定义标签/自定义标签/添加/提示/标签名称不能为空'))
      return
    }
    if (!this.values || this.values.length <= 0) {
      this.$message.warning(this.formatI18n('/分析/自定义标签/自定义标签/添加/提示/标签值不能为空'))
      return
    }
    let params: TagOption = new TagOption()
    params.tagId = this.id
    params.tagValues = this.values
    TagApi.save(params).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(this.formatI18n('/分析/自定义标签/自定义标签/添加/提示/添加成功'))
        this.getMemberTagList()
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  doBatchDelete() {
    if (this.selectedArr.length <= 0) {
      this.$message.warning(this.formatI18n('/分析/自定义标签/自定义标签/批量删除/请先勾选要删除的标签'))
      return
    }
    this.$confirm(this.formatI18n('/分析/自定义标签/自定义标签/删除/提示/是否批量删除这些标签?'), this.formatI18n('/公用/按钮/批量删除'), {
      confirmButtonText: this.formatI18n('/公用/按钮/确定'),
      cancelButtonText: this.formatI18n('/公用/按钮/取消')
    }).then(() => {
      this.submitBatchDelete()
    })
  }

  doEdit(row: any) {
    this.dialogShow = true
    this.dialogData = row
  }

  doSearchByKey() {
    this.page.currentPage = 1
    this.getMemberTagList()
  }

  doDialogClose() {
    this.dialogShow = false
    this.getMemberTagList()
  }

  doDelete(row: any) {
    this.$confirm(this.formatI18n('/分析/自定义标签/自定义标签/删除/提示/若删除该标签，已经打在会员资料上的该类标签也会一起删除，仍要删除该标签吗?'), this.formatI18n('/公用/按钮/删除'), {
      confirmButtonText: this.formatI18n('/公用/按钮/确定'),
      cancelButtonText: this.formatI18n('/公用/按钮/取消')
    }).then(() => {
      this.submitDelete(row.tagId)
    })
  }

  /**
   * 查询
   */
  doSearch() {
    this.page.currentPage = 1
    this.getMemberTagList()
  }

  handleSelectionChange(val: any) {
    this.selectedArr = val
  }

  /**
   * 重置
   */
  doReset() {
    this.id = ''
    this.tags = []
    // this.page.currentPage = 1
    // this.query = new DepositActivityFilter()
    this.getMemberTagList()
  }

  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.getMemberTagList()
  }

  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.getMemberTagList()
  }

  /**
   * 表格排序条件
   */
  onSortChange({column, prop, order}: any) {
    // todo
  }

  selectLabel(total: any) {
    let str: any = this.formatI18n('/分析/自定义标签/自定义标签/已选择{0}个自定义标签')
    return str.replace(/\{0\}/g, total)
  }

  formatter(row: any, column: any) {
    let tags = row.tagValues
    let str: any = ''
    if (tags && tags.length > 0) {
      tags.forEach((item: string) => {
        str += item + ','
      })
    }
    return str.substring(0, str.length - 1)
  }

  private submitDelete(id: string) {
    TagApi.remove(id).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(this.formatI18n('/公用/预约文件列表/删除成功'))
        this.getMemberTagList()
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private submitBatchDelete() {
    let ids: string[] = []
    if (this.selectedArr && this.selectedArr.length > 0) {
      this.selectedArr.forEach((item) => {
        ids.push(item.tagId!)
      })
    }
    TagApi.batchRemove(ids).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(this.formatI18n('/公用/预约文件列表/删除成功'))
        this.getMemberTagList()
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private getMemberTagList() {
    let query: TagOptionFilter = new TagOptionFilter()
    query.keyWordsLikes = this.searchContent
    query.page = this.page.currentPage - 1
    query.pageSize = this.page.size
    TagApi.query(query).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.tableData = resp.data
        this.page.total = resp.total
        this.id = ''
        this.tags = []
        this.values = []
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
}
