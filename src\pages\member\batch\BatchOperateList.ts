import {Component, Vue} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import ListWrapper from 'cmp/list/ListWrapper.vue'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import PointsAdjustBillApi from 'http/points/adjustbill/PointsAdjustBillApi'
import DownloadCenterDialog from 'pages/main/dowmload_center_dialog/DownloadCenterDialog.vue'
import RSOrgFilter from 'model/common/RSOrgFilter'
import OrgApi from 'http/org/OrgApi'
import RSOrg from 'model/common/RSOrg'
import SysConfigApi from "http/config/SysConfigApi";
import MemberOperationBillFilter from 'model/member/MemberOperationBillFilter'
import MemberOperationBillApi from 'http/batchOperate/MemberOperationBillApi'
import BatchOperateImportDialog from './dialog/BatchOperateImportDialog'
import I18nPage from 'common/I18nDecorator'
@I18nPage({
    auto: false,
    prefix: [
        '/会员/会员批量操作单'
    ]
  })

@Component({
    name: 'ScoreAdjust',
    components: {
        FormItem,
        ListWrapper,
        SubHeader,
		BatchOperateImportDialog,
        BreadCrume,
        DownloadCenterDialog
    }
})
export default class BatchOperateList extends Vue {

    get templatePath() {
        if (location.href.indexOf('localhost') === -1) {
            return 'template_member_operation_bill.xlsx'
        } else {
            return 'template_member_operation_bill.xlsx'
        }
    }

    get getAllCount() {
        return this.formatI18n('/公用/下拉框/提示', '全部') + `(${this.total.all})`
    }

    get getNoAudit() {
        return this.formatI18n('/公用/过滤器', '未审核') + `(${this.total.initial})`
    }

    get getAudit() {
        return this.formatI18n('/公用/过滤器', '已审核') + `(${this.total.audit})`
    }

    dialogvisiable = false
    query: MemberOperationBillFilter = new MemberOperationBillFilter()
    selectedArr: any[] = []
    activeName = 'first'
    switchFlag = false
    $refs: any
    importUrl = 'v1/custom/member-Operation-Bill/importExcel' // 导入文件
    selectAll = ''
    types: any = []
    total = {
        all: 0,
        initial: 0,
        audit: 0
    }
    // 分页
    page = {
        currentPage: 1,
        total: 0,
        size: 10
    }
    tableData: any[] = []
    dialogShow = false
    panelArray: any = []
    showOrg = false // 控制模态框的展示
    orgs: RSOrg[] = []

    created() {
        this.panelArray = [
            {
                name: this.i18n('会员批量操作单'),
                url: ''
            }
        ]
        this.getScoreBillList()
        this.getTotal()
        this.getOrg()
        this.getConfig()
    }

    doEdit() {
        // todo
    }

    doSelectAll() {
        if (this.selectAll) {
            for (let row of this.tableData) {
                this.$refs.table.toggleRowSelection(row, true)
            }
        } else {
            this.$refs.table.clearSelection();
        }
    }

    doDelete() {
        // todo
    }

    doExport() {
        // todo
    }

    doDialogClose() {
        this.dialogShow = false
    }

    doUploadSuccess() {
        // todo
        this.dialogvisiable = true
        this.getScoreBillList()
    }

    // 批量导入
    doBatchImport() {
        this.dialogShow = true
    }

    doStoreValueAdd() {
        this.$router.push({name: 'batch-operate-edit', query: {from: 'add'}})
    }

    doStoreValueReason() {
        this.$router.push({name: 'score-adjust-reason'})
    }

    doBatchDelete() {
        if (this.selectedArr.length <= 0) {
            this.$message.warning(this.formatI18n('/权益/积分/积分调整单/列表/按钮/点击批量删除/请先勾选要删除的单据') as string)
            return
        }
        this.$confirm(this.formatI18n('/权益/积分/积分调整单/列表/按钮/点击批量删除/是否批量删除这些单据?') as string, this.formatI18n('/权益/积分/积分调整单/列表/按钮/批量删除') as string, {
            confirmButtonText: this.formatI18n('/公用/按钮', '确定') as any,
            cancelButtonText: this.formatI18n('/公用/按钮', '取消') as any
        }).then(() => {
            this.submitBatchDelete()
        })
    }

    doBatchAudit() {
        if (this.selectedArr.length <= 0) {
            this.$message.warning(this.formatI18n('/权益/积分/积分调整单/列表/按钮/点击批量审核/请先勾选要审核的单据') as string)
            return
        }
        this.$confirm(this.formatI18n('/权益/积分/积分调整单/列表/按钮/点击批量审核/是否批量审核这些单据?') as string, this.formatI18n('/权益/积分/积分调整单/列表/按钮/批量审核') as string, {
            confirmButtonText: this.formatI18n('/公用/按钮', '确定') as any,
            cancelButtonText: this.formatI18n('/公用/按钮', '取消') as any
        }).then(() => {
            this.submitBatchAudit()
        })
    }

    doHandleClick() {
        this.page.currentPage = 1
        if (this.activeName === 'first') {
            this.query.stateEquals = null
        } else if (this.activeName === 'second') {
            this.query.stateEquals = 'INITIAL'
        } else {
            this.query.stateEquals = 'AUDITED'
        }
        this.getScoreBillList()
    }

    /**
     * 查询
     */
    doSearch() {
        this.page.currentPage = 1
        this.getTotal()
        this.getScoreBillList()
    }

    handleSelectionChange(val: any) {
        this.selectedArr = val
    }

    /**
     * 重置
     */
    doReset() {
        this.query = new MemberOperationBillFilter()
        this.activeName = 'first'
        this.getScoreBillList()
    }

    /**
     * 去详情
     */
    doGoDtl(row: any) {
        this.$router.push({name: 'batch-operate-dtl', query: {id: row.billNumber}})
    }

    /**
     * 分页页码改变的回调
     * @param val
     */
    onHandleCurrentChange(val: number) {
        this.page.currentPage = val
        this.getScoreBillList()
    }

    /**
     * 每页多少条的回调
     * @param val
     */
    onHandleSizeChange(val: number) {
        this.page.size = val
        this.getScoreBillList()
    }

    /**
     * 表格排序条件
     */
    onSortChange({column, prop, order}: any) {
        // todo
    }

    getBillLength(length: number) {
      let str: any = this.formatI18n('/储值/预付卡/预付卡调整单/列表页面', '已选择{0}张单据')
        str = str.replace(/\{0\}/g, length)
        return str
    }

    private getTotal() {
        MemberOperationBillApi.getTotal(this.query).then((resp: any) => {
            if (resp && resp.data) {
                this.total.all = resp.data.total
                this.total.initial = resp.data.sumInitial
                this.total.audit = resp.data.sumAudited
            }
        }).catch((error) => {
            if (error && error.message) {
                this.$message.error(error.message)
            }
        })
    }

    private getScoreBillList() {
        this.query.page = this.page.currentPage - 1
        this.query.pageSize = this.page.size
        MemberOperationBillApi.query(this.query).then((resp: any) => {
            if (resp && resp.data) {
                this.tableData = resp.data
                this.page.total = resp.total
            }
        }).catch((error) => {
            if (error && error.message) {
                this.$message.error(error.message)
            }
        })
    }

    private submitBatchDelete() {
        let ids: string[] = []
        if (this.selectedArr && this.selectedArr.length > 0) {
            this.selectedArr.forEach((item) => {
                // if (item.state !== 'AUDITED') {
                //   ids.push(item.billNumber!)
                // }
                ids.push(item.billNumber!)
            })
        }
        MemberOperationBillApi.batchRemove(ids).then((resp: any) => {
            if (resp && resp.code === 2000) {
                this.$message.success(resp.data)
                
                this.getScoreBillList()
            }
        }).catch((error) => {
            if (error && error.message) {
                this.$message.error(error.message)
            }
        })
    }

    private submitBatchAudit() {
        let ids: string[] = []
        if (this.selectedArr && this.selectedArr.length > 0) {
            this.selectedArr.forEach((item) => {
                // if (item.state !== 'AUDITED') {
                //   ids.push(item.billNumber!)
                // }
                ids.push(item.billNumber!)
            })
        }
        MemberOperationBillApi.batchAudit(ids).then((resp: any) => {
            if (resp && resp.code === 2000) {
                this.$message.success(resp.data)
                
                this.getScoreBillList()
            }
        }).catch((error) => {
            if (error && error.message) {
                this.$message.error(error.message)
            }
        })
    }

    // private getState() {
    //     PointsAdjustBillApi.stats(this.query).then((resp: any) => {
    //         if (resp && resp.data) {
    //             this.total.all = resp.data.total
    //             this.total.initial = resp.data.sumInitial
    //             this.total.audit = resp.data.sumAudited
    //         }
    //     }).catch((error) => {
    //         if (error && error.message) {
    //             this.$message.error(error.message)
    //         }
    //     })
    // }

    private getConfig() {
        SysConfigApi.get().then((resp: any) => {
            if (resp && resp.data) {
                this.showOrg = resp.data.enableMultiMarketingCenter
            }
        }).catch((error) => {
            if (error && error.message) {
                this.$message.error(error.message)
            }
        })
    }

    private getOrg() {
        let params: RSOrgFilter = new RSOrgFilter()
        params.orgTypeEquals = "PHX"
        params.page = 0
        params.pageSize = 0
        OrgApi.query(params).then((resp: any) => {
            if (resp && resp.code === 2000) {
                this.orgs = resp.data
            }
        }).catch((error: any) => {
            this.$message.error(error.message)
        })
    }
}
