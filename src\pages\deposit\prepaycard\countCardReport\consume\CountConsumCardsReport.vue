<template>
  <div class="consume-cards-report">
    <div class="current-page">
      <el-form label-width="140px">
        <el-row class="query">
          <TimeRange no-i18n @submit="handleTimeRange" ref="timeRange"></TimeRange>
        </el-row>
        <el-row class="query" style="margin-top: 8px">
          <el-col :span="8">
            <form-item label="购卡人">
              <el-input placeholder="输入手机号/会员号" v-model="query.memberIdEquals" />
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item label="交易号">
              <el-input :placeholder="i18n('/公用/查询条件/提示/等于')" v-model="query.transNoEquals" />
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="i18n('卡模板')">
              <el-input :placeholder="i18n('/公用/查询条件/提示/等于')" v-model="query.templateEquals" />
            </form-item>
          </el-col>
        </el-row>
        <el-row class="query" style="margin-top: 8px" v-show="expandQuery">
          <el-col :span="8">
            <form-item :label="i18n('卡号')">
              <el-input :placeholder="i18n('/公用/查询条件/提示/等于')" v-model="query.codeEquals" />
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="i18n('发生组织')">
              <SelectStores v-model="query.orgIdEquals" :isOnlyId="true" :hideAll="false" width="100%" :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
              </SelectStores>
            </form-item>
          </el-col>
          <el-col :span="8" v-if="isMoreMarketing">
            <form-item :label="formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水/发生区域')">
              <el-select placeholder="不限" v-model="query.zoneIdEquals">
                <el-option :label="formatI18n('/公用/查询条件/下拉列表/不限')" :value="null">{{ formatI18n('/公用/查询条件/下拉列表/不限') }}</el-option>
                <el-option :key="item.zone.id" :label="'['+item.zone.id+']'+item.zone.name" :value="item.zone.id"
                  v-for="item in areaData">[{{item.zone.id}}]{{item.zone.name}}</el-option>
              </el-select>
            </form-item>
          </el-col>
        </el-row>
        <el-row class="query" style="margin-top: 8px">
          <el-col :span="8">
            <form-item label=" ">
              <el-button class="btn-search" type="primary" @click="doSearch">查询</el-button>
              <el-button class="btn-reset" @click="doReset">重置</el-button>
              <el-button type="text" @click="expandQuery=!expandQuery">
                <span v-if="!expandQuery" key="1">
                  <i class="el-icon-arrow-down"></i>
                  {{formatI18n('/公用/查询条件/展开')}}
                </span>
                <span v-if="expandQuery" key="1">
                  <i class="el-icon-arrow-up"></i>
                  {{formatI18n('/公用/查询条件/收起')}}
                </span>
              </el-button>
            </form-item>
          </el-col>
        </el-row>
      </el-form>
      <hr />
      <el-row class="table">
        <el-table :data="queryData" border v-loading="tableLoading" @header-dragend="tableDragend" style="width: 100%;margin-top: 20px">
          <el-table-column label="卡号" prop="code" :width="getColumnWidth('code', 180)"></el-table-column>
          <el-table-column label="消费时间" prop="occurredTime" :width="getColumnWidth('occurredTime', 140)" align="center">
            <template slot-scope="scope">
              <span no-i18n>{{dataUtil.format(scope.row.occurredTime, 'yyyy-MM-dd HH:mm:ss')}}</span>
            </template>
          </el-table-column>
          <el-table-column label="发生组织" prop="occurredOrg" :width="getColumnWidth('occurredOrg', 200)" align="center">
            <template slot-scope="scope">
              <span no-i18n :title="dataUtil.showIdName(scope.row.occurredOrg)">{{dataUtil.showIdName(scope.row.occurredOrg)}}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="isMoreMarketing" prop="moreOrg" :width="getColumnWidth('moreOrg')" :label="formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水/发生区域')"
            align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.zone!==null && scope.row.zone!==''">
                <el-tooltip class="item" effect="light" placement="right-end">
                  <div> {{'['+scope.row.zone.id+']'}}{{scope.row.zone.name}}</div>
                  <div slot="content">
                    {{'['+scope.row.zone.id+']'}}{{scope.row.zone.name}}
                  </div>
                </el-tooltip>
              </div>
              <div v-else>-</div>
            </template>
          </el-table-column>
          <el-table-column label="购卡人" prop="memberId" :width="getColumnWidth('memberId',100)" align="center">
            <template slot-scope="scope">
              <span no-i18n :title="dataUtil.showMemberId(scope.row)">{{dataUtil.showMemberId(scope.row)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="卡模板" prop="cardTemplate" :width="getColumnWidth('cardTemplate',150)" align="center">
            <template slot-scope="scope">
              <el-button no-i18n type="text" @click="gotoTplDtl(scope.row.templateNumber)">{{scope.row.templateName}}</el-button>
            </template>
          </el-table-column>
          <el-table-column prop="count" :width="getColumnWidth('count',140)" align="center">
            <template slot="header">
              <span>{{i18n('原次数')}}</span>
              <el-tooltip class="item" effect="dark" :content="i18n('本笔交易前卡剩余次数')" placement="top-start">
                <i class="el-icon-warning" style="margin-left:4px" />
              </el-tooltip>
            </template>
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.times.toFixed(0)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="核销次数" :width="getColumnWidth('useCount',140)" prop="useCount" align="center">
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.occurredTimes.toFixed(0)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="交易号" prop="transNo" :width="getColumnWidth('transNo')" align="center">
            <template slot-scope="scope">
              <span no-i18n :title="scope.row.transNo">{{scope.row.transNo}}</span>
            </template>
          </el-table-column>
          <el-table-column label="兑换商品" prop="payType" :width="getColumnWidth('payType',200)" align="center">
            <template slot-scope="scope">
              <span class="span-btn" @click="doOpenGoodsDialog(scope.row.transNo)">{{i18n('查看详情')}}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-pagination no-i18n :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
        @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background :layout="getPageLayout('total, prev, pager, next, sizes, jumper',page.probeEnabled)"
        class="pagin"></el-pagination>
    </div>
    <ExchangeGoodsDialog ref="exchangeGoodsDialog"></ExchangeGoodsDialog>
  </div>
</template>

<script lang="ts" src="./CountConsumCardsReport.ts">
</script>

<style lang="scss" scoped>
.consume-cards-report {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: auto;
  .total {
    margin: 20px;
  }

  div.el-range-input {
    flex: 1;
  }

  .current-page {
    height: calc(100% - 150px);
    padding: 0 20px 20px 20px;
    .el-select {
      width: 100%;
    }

    .query {
      .el-form-item {
        margin-bottom: 0;
      }
    }

    .pagin {
      margin-top: 25px;
    }

    .el-col {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    tbody {
      .cell {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
</style>
