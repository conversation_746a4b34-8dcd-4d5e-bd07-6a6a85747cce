/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2025-05-06 15:43:16
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\v2\coupon\CouponActivityApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import CouponAbortSum from 'model/v2/coupon/CouponAbortSum'
import CouponActivityBody from 'model/v2/coupon/CouponActivityBody'
import CouponActivityDetail from 'model/v2/coupon/CouponActivityDetail'
import CouponActivityFilter from 'model/v2/coupon/CouponActivityFilter'
import InviteGiftBagEvaluation from 'model/v2/coupon/InviteGiftBagEvaluation'
import SendCouponResult from 'model/v2/coupon/SendCouponResult'
import MemberRegisterGiftActivity from 'model/v2/coupon/registergift/MemberRegisterGiftActivity'
import GoodsGainCouponActivity from 'model/v2/coupon/goodsgaincoupon/GoodsGainCouponActivity'
import GoodsGainCouponByQtyActivity from 'model/v2/coupon/goodsgaincouponbyqty/GoodsGainCouponByQtyActivity'
import GoodsAmountGiftActivity from 'model/v2/coupon/goodsgift/GoodsAmountGiftActivity'
import ManualHandOutFilter from 'model/v2/coupon/manualhandoutgift/ManualHandOutFilter'
import ManualHandOutIssueResult from 'model/v2/coupon/manualhandoutgift/ManualHandOutIssueResult'
import ManualHandOutGiftActivity from 'model/common/ManualHandOutGiftActivity'
import ExcelImportResult from 'model/v2/coupon/manualhandoutgift/ExcelImportResult'
import WeixinActiveGiftActivity from 'model/v2/coupon/weixinactivegift/WeixinActiveGiftActivity'
import InviteRegisterGiftActivity from 'model/v2/coupon/inviteregistergift/InviteRegisterGiftActivity'
import MiniProgramGainCouponActivity from 'model/v2/coupon/miniprogramgaincoupon/MiniProgramGainCouponActivity'
import ExportCouponCodeActivityRule from 'model/v2/coupon/exportcouponcode/ExportCouponCodeActivityRule'
import WeiXinQRCode from 'model/v2/coupon/weixincoupon/WeiXinQRCode'
import WeiXinCouponActivity from 'model/v2/coupon/weixincoupon/WeiXinCouponActivity'
import ThirdPartyIssueCouponActivity from 'model/v2/coupon/thirdparty/ThirdPartyIssueCouponActivity'
import UserGroupGiftActivity from 'model/v2/coupon/usergroupgift/UserGroupGiftActivity'
import UserGroupGiftCountFilter from 'model/v2/coupon/usergroupgift/UserGroupGiftCountFilter'
import UserGroupGiftCount from 'model/v2/coupon/usergroupgift/UserGroupGiftCount'
import UserGroupGiftResultFilter from 'model/v2/coupon/usergroupgift/UserGroupGiftResultFilter'
import UserGroupGift from 'model/v2/coupon/usergroupgift/UserGroupGift'
import WeiXinCouponActivityV2 from 'model/v2/coupon/weixincoupon/v2/WeiXinCouponActivityV2'
import PrecisionIssueCouponActivity from 'model/v2/coupon/precisionissuecoupon/PrecisionIssueCouponActivity'
import UseCouponGiftActivity from 'model/v2/coupon/useCouponSendCoupon/UseCouponGiftActivity'
import BWeChatVideoActivity from 'model/v2/coupon/videonumberActivity/BWeChatVideoActivity'
import BmodifyStockRequest from 'model/v2/coupon/videonumberActivity/BmodifyStockRequest'
import ActivityAddStoresRequest from 'model/v2/coupon/ActivityAddStoresRequest'
import BWeChatVideoWindowProductFilter from 'model/v2/coupon/videonumberActivity/BWeChatVideoWindowProductFilter'
import WeChatVideoWindowProduct from 'model/v2/coupon/videonumberActivity/WeChatVideoWindowProduct'
import BPutAwayRequest from 'model/v2/coupon/videonumberActivity/BPutAwayRequest'
import BSoldOutRequest from 'model/v2/coupon/videonumberActivity/BSoldOutRequest'
import BProductAttr from 'model/v2/coupon/videonumberActivity/BProductAttr'
import DirectionalIssueCouponActivity from 'model/v2/coupon/directionalIssueCoupon/DirectionalIssueCouponActivity'
import ImproveProfilesGiftActivity from 'model/v2/coupon/improveProfiles/ImproveProfilesGiftActivity'
import UpdateSequenceRequest from 'model/common/UpdateSequenceRequest'
import WeiXinBrandResponse from 'model/v2/coupon/videonumberActivity/WeiXinBrandResponse'
import CollectPointsActivity from "model/v2/coupon/collectpoints/CollectPointsActivity"
import CollectPointsRewardFilter from 'model/v2/coupon/collectpoints/CollectPointsRewardFilter'
import CollectPointsReward from 'model/v2/coupon/collectpoints/CollectPointsReward'
import PltGroupCouponActivity from 'model/v2/coupon/aliGroupBuyCoupon/PltGroupCouponActivity'
import GroupTargetedGiftsActivity from "model/v2/coupon/groupTargetedGifts/GroupTargetedGiftsActivity";
import GroupTargetedGiftsResult from "model/v2/coupon/groupTargetedGifts/GroupTargetedGiftsResult";
import GroupTargetedGiftsResultFilter from "model/v2/coupon/groupTargetedGifts/GroupTargetedGiftsResultFilter";
import WeiXinPayActivity from 'model/coupon/WeiXinPayActivity'
import CollectPointsDetailFilter from 'model/v2/coupon/collectpoints/CollectPointsDetailFilter'
import CollectPointsDetail from 'model/v2/coupon/collectpoints/CollectPointsDetail'
import CollectPointsAccountFilter from 'model/v2/coupon/collectpoints/CollectPointsAccountFilter'
import CollectPointsAccount from 'model/v2/coupon/collectpoints/CollectPointsAccount'
import WeiXinPayMarketingActivity from 'model/coupon/WeiXinPayMarketingActivity'

export default class CouponActivityApi {
  /**
   * 是否允许选择多张券
   * 是否允许选择多张券
   *
   */
  static showCouponBag(): Promise<Response<boolean>> {
    return ApiClient.server()
      .get(`/v1/coupon-activity/showCouponBag`, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 下载二维码
   * 下载二维码
   *
   */
  static downLoadQrCode(activityNumber: string): Promise<Response<string>> {
    return ApiClient.server()
      .get(`/v1/coupon-activity/downLoadQrCode/${activityNumber}`, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 新建或修改微信商家券
   * 新建或修改微信商家券
   *
   */
  static saveWeiXinCouponActivityV2(
    body: WeiXinCouponActivityV2
  ): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/saveWeiXinCouponActivityV2`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 微信商家券
   * 微信商家券。
   *
   */
  static getWeiXinCouponActivityV2(
    id: string
  ): Promise<Response<WeiXinCouponActivityV2>> {
    return ApiClient.server()
      .get(`/v1/coupon-activity/saveWeiXinCouponActivityV2/${id}`, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 新建或修改第三方发券活动
   * 新建或修改第三方发券活动
   *
   */
  static saveThirdPartyIssueCoupon(
    body: ThirdPartyIssueCouponActivity
  ): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/saveThirdPartyIssueCoupon`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 查询第三方发券活动详情
   * 查询第三方发券活动详情。
   *
   */
  static getThirdPartyIssueCoupon(
    id: string
  ): Promise<Response<ThirdPartyIssueCouponActivity>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/getThirdPartyIssueCoupon/${id}`, {}, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 小程序领券详情
   * 小程序领券详情。
   *
   */
  static getMiniProgramGainCouponActivity(
    id: string
  ): Promise<Response<MiniProgramGainCouponActivity>> {
    return ApiClient.server()
      .get(`/v1/coupon-activity/getMiniProgramGainCouponActivity/${id}`, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 新建或修改小程序领券
   * 新建或修改小程序领券
   *
   */
  static saveMiniProgramGainCouponActivity(
    body: MiniProgramGainCouponActivity
  ): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/saveMiniProgramGainCouponActivity`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 新建或修改邀请发大礼包
   * 新建或修改邀请发大礼包
   *
   */
  static saveInviteRegisterGift(
    body: InviteRegisterGiftActivity
  ): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/saveInviteRegisterGift`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 邀请发大礼包详情
   * 邀请发大礼包详情。
   *
   */
  static getInviteRegisterGift(
    id: string
  ): Promise<Response<InviteRegisterGiftActivity>> {
    return ApiClient.server()
      .get(`/v1/coupon-activity/getInviteRegisterGift/${id}`, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 导入群发券发券对象
   * 导入群发券发券对象。
   *
   */
  static importManualHandObj(
    body: any,
    activityNumber: string,
    isMobile: boolean
  ): Promise<Response<ExcelImportResult>> {
    return ApiClient.server()
      .post(
        `/v1/coupon-activity/importManualHandObj/${activityNumber}/${isMobile}`,
        body,
        {}
      )
      .then((res) => {
        return res.data
      })
  }

  /**
   * 群发券发券发券结果查询
   * 群发券发券发券结果查询。
   *
   */
  static queryManualHandIssueResult(
    body: ManualHandOutFilter
  ): Promise<Response<ManualHandOutIssueResult[]>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/queryManualHandIssueResult`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 群发券详情
   * 群发券详情。
   *
   */
  static getManualHandOut(
    id: string
  ): Promise<Response<ManualHandOutGiftActivity>> {
    return ApiClient.server()
      .get(`/v1/coupon-activity/getManualHandOut/${id}`, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 新建或修改群发券
   * 新建或修改群发券
   *
   */
  static saveManualHandOut(
    body: ManualHandOutGiftActivity
  ): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/saveManualHandOut`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 商品满数量发券详情
   * 商品满数量发券详情。
   *
   */
  static getGoodsGainCouponByQty(
    id: string
  ): Promise<Response<GoodsGainCouponByQtyActivity>> {
    return ApiClient.server()
      .get(`/v1/coupon-activity/getGoodsGainCouponByQty/${id}`, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 新建或修改商品满数量发券
   * 新建或修改商品满数量发券
   *
   */
  static saveGoodsGainCouponByQty(
    body: GoodsGainCouponByQtyActivity
  ): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/saveGoodsGainCouponByQty`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 新建或修改满额发券活动
   * 新建或修改满额发券活动
   *
   */
  static saveGoodsGainCoupon(
    body: GoodsGainCouponActivity
  ): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/saveGoodsGainCoupon`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 满额发券活动详情
   * 满额发券活动详情。
   *
   */
  static getGoodsGainCoupon(
    id: string
  ): Promise<Response<GoodsGainCouponActivity>> {
    return ApiClient.server()
      .get(`/v1/coupon-activity/getGoodsGainCoupon/${id}`, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 注册发大礼包详情
   * 注册发大礼包详情。
   *
   */
  static getMemberRegisterGift(
    id: string
  ): Promise<Response<MemberRegisterGiftActivity>> {
    return ApiClient.server()
      .get(`/v1/coupon-activity/getMemberRegisterGift/${id}`, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 新建或修改注册发大礼包
   * 新建或修改注册发大礼包
   *
   */
  static saveMemberRegisterGift(
    body: MemberRegisterGiftActivity
  ): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/saveMemberRegisterGift`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 新建修改微信激活发大礼包
   * 新建修改微信激活发大礼包
   *
   */
  static saveWeixinActiveGift(
    body: WeixinActiveGiftActivity
  ): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/saveWeixinActiveGift`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 微信激活发大礼包详情
   * 微信激活发大礼包详情
   *
   */
  static getWeixinActiveGift(
    id: string
  ): Promise<Response<WeixinActiveGiftActivity>> {
    return ApiClient.server()
      .get(`/v1/coupon-activity/getWeixinActiveGift/${id}`, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 客群赠礼详情
   * 客群赠礼详情
   *
   */
  static getUserGroupGift(
    id: string
  ): Promise<Response<UserGroupGiftActivity>> {
    return ApiClient.server()
      .get(`/v1/coupon-activity/getUserGroupGift/${id}`, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 商品满额发大礼包详情
   * 商品满额发大礼包详情。
   *
   */
  static getGoodsAmountGift(
    id: string
  ): Promise<Response<GoodsAmountGiftActivity>> {
    return ApiClient.server()
      .get(`/v1/coupon-activity/getGoodsAmountGift/${id}`, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 新建或修改商品满额发大礼包
   * 新建或修改商品满额发大礼包
   *
   */
  static saveGoodsAmountGift(
    body: GoodsAmountGiftActivity
  ): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/saveGoodsAmountGift`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 作废券活动
   * 作废券活动。
   *
   */
  static abort(activityId: string): Promise<Response<void>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/abort/${activityId}`, {}, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 审核券活动
   * 审核券活动。
   *
   */
  static audit(activityId: string): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/audit/${activityId}`, {}, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 批量审核券活动
   * 批量审核券活动。
   *
   */
  static batchAudit(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/batch/audit`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 批量删除券活动
   * 批量删除券活动。
   *
   */
  static batchRemove(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/batch/remove`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 批量终止券活动
   * 批量终止券活动。
   *
   */
  static batchStop(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/batch/stop`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 核销第三方券详情
   * 核销第三方券详情。
   *
   */
  static get(id: string): Promise<Response<CouponActivityDetail>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/get/${id}`, {}, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 获取作废券信息。
   * 获取作废券信息
   *
   */
  static getCouponAbortSum(
    activityUuid: string
  ): Promise<Response<CouponAbortSum>> {
    return ApiClient.server()
      .get(`/v1/coupon-activity/getCouponAbortSum/${activityUuid}`, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 群发券发券结果-进行中、和已结束展示。
   * 群发券发券结果-进行中、和已结束展示。
   *
   */
  static getCouponSendResult(
    activityUuid: string
  ): Promise<Response<SendCouponResult>> {
    return ApiClient.server()
      .get(`/v1/coupon-activity/couponSendResult/${activityUuid}`, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 邀请有礼效果评估。
   * 邀请有礼效果评估
   *
   */
  static getInviteGiftBagEvaluation(
    activityUuid: string
  ): Promise<Response<InviteGiftBagEvaluation>> {
    return ApiClient.server()
      .get(`/v1/coupon-activity/getInviteGiftBagEvaluation/${activityUuid}`, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 券活动分页查询
   * 券活动分页查询。
   *
   */
  static query(
    body: CouponActivityFilter
  ): Promise<Response<CouponActivityBody>> {
    return ApiClient.server()
      .post(`/v1/web/activity/query`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 查询活动组
   * 查询活动组。
   *
   */
  static queryGroup(): Promise<Response<any>> {
    return ApiClient.server()
      .post(`/v1/web/activity/queryActivityGroup`, {}, {})
      .then((res) => {
        return res.data
      })
  }
  /**
   * 导入核销券码
   * 导入核销券码。
   *
   */
  static importMember(
    body: any,
    activityNumber: string
  ): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/import/${activityNumber}`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 删除券活动
   * 删除券活动。
   *
   */
  static remove(activityId: string): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/remove/${activityId}`, {}, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 新建或修改核销第三方券
   * 新建或修改核销第三方券
   *
   */
  static save(body: CouponActivityDetail): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/save`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 终止券活动
   * 终止券活动。
   *
   */
  static stop(activityId: string): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/stop/${activityId}`, {}, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 下载导出券码发券活动券码
   * 下载导出券码发券活动券码
   *
   */
  static downLoadExportCouponCode(activityId: string): Promise<Response<void>> {
    return ApiClient.server()
      .post(
        `/v1/coupon-activity/downLoadExportCouponCode/${activityId}`,
        {},
        {}
      )
      .then((res) => {
        return res.data
      })
  }

  /**
   * 导出券码发券活动详情
   * 导出券码发券活动详情。
   *
   */
  static getExportCouponCodeActivity(
    id: string
  ): Promise<Response<ExportCouponCodeActivityRule>> {
    return ApiClient.server()
      .get(`/v1/coupon-activity/getExportCouponCodeActivity/${id}`, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 新建或修改导出券码发券活动
   * 新建或修改导出券码发券活动
   *
   */
  static saveExportCouponCodeActivity(
    body: ExportCouponCodeActivityRule
  ): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/saveExportCouponCodeActivity`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 微信投放券活动详情
   *
   */
  static createQrCode(id: string): Promise<Response<WeiXinQRCode>> {
    return ApiClient.server()
      .get(`/v1/coupon-activity/createQrCode/${id}`, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 新建或修改微信投放券活动
   */
  static saveWeiXinCouponActivity(
    body: WeiXinCouponActivity
  ): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/saveWeiXinCouponActivity`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 新建修改客群赠礼
   * 新建修改客群赠礼
   *
   */
  static saveUserGroupGift(
    body: UserGroupGiftActivity
  ): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/saveUserGroupGift`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 微信投放券活动活动详情
   */
  static getWeiXinCouponActivity(
    id: string
  ): Promise<Response<WeiXinCouponActivity>> {
    return ApiClient.server()
      .get(`/v1/coupon-activity/getWeiXinCouponActivity/${id}`, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 新建或修改微信投放券库存
   * 新建或修改微信投放券库存
   *
   */
  static modifyWeiXinCouponStock(
    id: string,
    occur: number
  ): Promise<Response<void>> {
    return ApiClient.server()
      .post(
        `/v1/coupon-activity/modifyWeiXinCouponStock/${id}`,
        {},
        {
          params: {
            occur: occur
          }
        }
      )
      .then((res) => {
        return res.data
      })
  }

  /**
   * 客群赠礼人数
   * 客群赠礼人数
   *
   */
  static queryUserGroupCount(
    body: UserGroupGiftCountFilter
  ): Promise<Response<UserGroupGiftCount>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/queryUserGroupCount`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 客群赠礼结果
   * 客群赠礼结果
   *
   */
  static queryUserGroupResult(
    body: UserGroupGiftResultFilter
  ): Promise<Response<UserGroupGift>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/queryUserGroupResult`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 新建或修改精准发券活动
   * 新建或修改精准发券活动。
   *
   */
  static savePrecisionIssueCouponActivity(
    body: PrecisionIssueCouponActivity
  ): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/savePrecisionIssueCouponActivity`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 精准发券详情
   * 精准发券详情。
   *
   */
  static getPrecisionIssueCouponActivity(
    id: string
  ): Promise<Response<PrecisionIssueCouponActivity>> {
    return ApiClient.server()
      .get(`/v1/coupon-activity/getPrecisionIssueCouponActivity/${id}`, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 新建或修改用券赠礼活动
   * 新建或修改用券发大礼包
   *
   */
  static saveUseCouponGift(
    body: UseCouponGiftActivity
  ): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/saveUseCouponGift`, body, {})
      .then((res) => {
        return res.data
      })
  }
  /**
   * 用券礼包活动详情
   * 用券礼包活动详情。
   *
   */
  static getUseCouponGift(
    id: string
  ): Promise<Response<UseCouponGiftActivity>> {
    return ApiClient.server()
      .get(`/v1/coupon-activity/getUseCouponGift/${id}`, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 恢复券活动
   * 恢复券活动。
   *
   */
  static recovery(activityId: string): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/recovery/${activityId}`, {}, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 暂停券活动
   * 暂停券活动。
   *
   */
  static suspend(activityId: string): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/suspend/${activityId}`, {}, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 编辑修改视频号发券
   * 编辑修改视频号发券
   *
   */
  static saveWeChatVideoIssueCouponActivity(
    body: BWeChatVideoActivity
  ): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/saveWeChatVideoIssueCouponActivity`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 视频号发券详情
   * 视频号发券详情
   *
   */
  static getWeChatVideoIssueCouponActivity(
    activityId: string
  ): Promise<Response<BWeChatVideoActivity>> {
    return ApiClient.server()
      .get(
        `/v1/coupon-activity/getWeChatVideoIssueCouponActivity/${activityId}`,
        {}
      )
      .then((res) => {
        return res.data
      })
  }

  /**
   * 视频号发券列表
   * 视频号发券列表
   *
   */
  static queryVideoCouponActivity(
    body: CouponActivityFilter
  ): Promise<Response<CouponActivityBody>> {
    return ApiClient.server()
      .post(`/v1/video-coupon-activity/query`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 修改视频号发券活动总库存
   * 修改视频号发券活动总库存
   *
   */
  static modifyStock(body: BmodifyStockRequest): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/video-coupon-activity/modifyStock`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 获取全部品牌
   * 获取全部品牌
   * 
   */
  static getAllBrand(nextKey: Nullable<string>): Promise<Response<WeiXinBrandResponse>> {
    return ApiClient.server().post(`/v1/video-coupon-activity/getAllBrand`, {}, {
      params: {
        nextKey: nextKey
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 微信小程序发券活动追加门店
   * 微信小程序发券活动追加门店
   *
   */
  static addStores(body: ActivityAddStoresRequest): Promise<Response<void>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/addStores`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 橱窗商品查询
   * 橱窗商品查询
   *
   */
  static queryVideoWindowProduct(
    body: BWeChatVideoWindowProductFilter
  ): Promise<Response<WeChatVideoWindowProduct[]>> {
    return ApiClient.server()
      .post(`/v1/video-window-product/query`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 橱窗商品上架
   * 橱窗商品上架
   *
   */
  static putAway(body: BPutAwayRequest): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/video-window-product/putAway`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 橱窗商品下架
   * 橱窗商品下架
   *
   */
  static soldOut(body: BSoldOutRequest): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/video-window-product/soldOut`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 获取商品品类
   * 获取商品品类
   *
   */
  static getGoodsParams(catId: string): Promise<Response<BProductAttr[]>> {
    return ApiClient.server()
      .get(`/v1/video-coupon-activity/getGoodsParams/${catId}`, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 活动投放获取二维码
   * 活动投放获取二维码。
   *
   */
  static getQrCode(activityNumber: string): Promise<Response<string>> {
    return ApiClient.server()
      .get(
        `/v1/weixin-Applet-qrcode/createWeiXinAppletActivityQrCode/${activityNumber}`,
        {}
      )
      .then((res) => {
        return res.data
      })
  }

  /**
   * 新建/修改/审核定向发券活动
   * 新建/修改/审核定向发券活动
   * 
   */
  static saveDirectionalIssueActivity(body: DirectionalIssueCouponActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/coupon-activity/saveDirectionalIssueCouponActivity`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询改定向发券活动详情
   * 查询改定向发券活动详情。
   * 
   */
  static getDirectionalIssueCouponActivity(number: string): Promise<Response<DirectionalIssueCouponActivity>> {
    return ApiClient.server().get(`/v1/coupon-activity/getDirectionalIssueCouponActivity/${number}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**  
   * 同步上下架状态
   * 同步上下架状态
   *
   */
  static syncState(operator: string): Promise<Response<void>> {
    return ApiClient.server()
      .post(`/v1/video-window-product/syncState?operator=${operator}`, {}, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 新建完善资料有礼活动
   * 
   * 
   */
  static saveImproveProfilesActivity(body: ImproveProfilesGiftActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/coupon-activity/saveImproveProfilesActivity`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 完善资料有礼活动详情
   * 
   */
  static getImproveProfilesActivity(number: string): Promise<Response<DirectionalIssueCouponActivity>> {
    return ApiClient.server().get(`/v1/coupon-activity/getImproveProfilesActivity/${number}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 群发券发券单个会员结果查询
   * 群发券发券单个会员结果查询。
   * 
   */
  static queryMemberCoupon(body: any): Promise<Response<string[]>> {
    return ApiClient.server().post(`/v1/coupon-activity/queryMemberCoupon`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
 * 修改显示排序
 * 修改显示排序
 * 
 */
  static updateSequence(body: UpdateSequenceRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/web/activity/updateSequence`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询微信小程序券包/单券活动
   * 查询微信小程序券包/单券活动。
   * 
   * 是否是券包
      merchan: Nullable<boolean> = null
      page: Nullable<number> = null
      pageSize: Nullable<number> = null
      活动名称：
      nameLikes;
      活动号：
      numberEquals;
   */
  static queryWeixinAppletCouponActivities(body: any): Promise<Response<any>> {
    return ApiClient.server().post(`/v1/weixin-applet/activity/queryWeixinAppletCouponActivities`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
* 集点活动详情
* 集点活动详情。
* 
*/
  static getCollectPoints(id: string): Promise<Response<CollectPointsActivity>> {
    return ApiClient.server().get(`/v1/coupon-activity/getCollectPoints/${id}`, {
    }).then((res) => {
      return res.data
    })
  }


  /**
   * 新建或修改集点活动
   * 新建或修改集点活动
   * 
   */
  static saveCollectPoints(body: CollectPointsActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/coupon-activity/saveCollectPoints`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 集点奖励管理
   * 集点奖励管理
   * 
   */
  static queryCollectPointsReward(body: CollectPointsRewardFilter): Promise<Response<CollectPointsReward[]>> {
    return ApiClient.server().post(`/v1/coupon-activity/queryCollectPointsReward`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 集点活动 参与记录明细
  * 集点活动 参与记录明细
  * 
  */
  static queryCollectPointsDetail(body: CollectPointsDetailFilter): Promise<Response<CollectPointsDetail[]>> {
    return ApiClient.server().post(`/v1/coupon-activity/queryCollectPointsDetail`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 集点活动 参与记录
  * 集点活动 参与记录
  * 
  */
  static queryCollectPointsAccount(body: CollectPointsAccountFilter): Promise<Response<CollectPointsAccount[]>> {
    return ApiClient.server().post(`/v1/coupon-activity/queryCollectPointsAccount`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /** 
   * 批量导出集点奖品管理 
   * 批量导出集点奖品管理。
   * 
   */
  static exportCollectPointsReward(body: CollectPointsRewardFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/coupon-activity/exportCollectPointsReward`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 批量导出集点参与记录
  * 批量导出集点参与记录。
  * 
  */
  static exportCollectPointsAccount(activityNumber: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/coupon-activity/exportCollectPointsAccount`, {}, {
      params: {
        activityNumber: activityNumber
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 审核支付宝平台团购券活动
  * 审核支付宝平台团购券活动。
  * 
  */
  static aliPltGroupCouponActivityAudit(activityId: string): Promise<Response<void>> {
    return ApiClient.server().get(`/v1/coupon-activity/aliPltGroupCouponActivityAudit/${activityId}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 支付宝平台团购券活动详情
   * 支付宝平台团购券活动详情。
   * 
   */
  static getPltGroupCouponActivity(id: string): Promise<Response<PltGroupCouponActivity>> {
    return ApiClient.server().get(`/v1/coupon-activity/getPltGroupCouponActivity/${id}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建或修改支付宝平台团购券活动
   * 新建或修改支付宝平台团购券活动
   * 
   */
  static savePltGroupCouponActivity(body: PltGroupCouponActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/coupon-activity/savePltGroupCouponActivity`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导出消费有礼结果
   *
   */
  static export(id: string): Promise<Response<string>> {
    return ApiClient.server().get(`/v1/coupon-activity/collectPointsActivity/goodsExport/${id}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 群发券 查询导入是否完成
   * 
   */
  static checkManualHandOutImport(number: string): Promise<Response<string>> {
    return ApiClient.server().get(`/v1/coupon-activity/checkManualHandOutImport/${number}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 客群定向发礼活动详情
   * 客群定向发礼活动详情。
   *
   */
  static getGroupTargetedGifts(id: string): Promise<Response<GroupTargetedGiftsActivity>> {
    return ApiClient.server().get(`/v1/coupon-activity/getGroupTargetedGifts/${id}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建或修改客群定向发礼
   * 新建或修改客群定向发礼
   *
   */
  static saveGroupTargetedGifts(body: GroupTargetedGiftsActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/coupon-activity/saveGroupTargetedGifts`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 客群定向发礼活动发券结果查询
   * 客群定向发礼活动发券结果查询。
   *
   */
  static queryGroupTargetedGiftsResult(body: GroupTargetedGiftsResultFilter): Promise<Response<GroupTargetedGiftsResult[]>> {
    return ApiClient.server().post(`/v1/coupon-activity/queryGroupTargetedGiftsResult`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
 * 微信支付活动详情
 * 微信支付活动详情。
 * 
 */
  static getWeiXinPayActivity(id: string): Promise<Response<WeiXinPayActivity>> {
    return ApiClient.server().get(`/v1/coupon-activity/getWeiXinPayActivity/${id}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
 * 新建或修改微信支付活动
 * 新建或修改微信支付活动
 * 
 */
  static saveWeiXinPayActivity(body: WeiXinPayActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/coupon-activity/saveWeiXinPayActivity`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
 * 微信支付营销活动详情
 * 微信支付营销活动详情。
 * 
 */
  static getWeiXinPayMarketingActivity(id: string): Promise<Response<WeiXinPayMarketingActivity>> {
    return ApiClient.server().get(`/v1/coupon-activity/getWeiXinPayMarketingActivity/${id}`, {
    }).then((res) => {
      return res.data
    })
  }


  /**
   * 新建或修改微信支付营销活动
   * 新建或修改微信支付营销活动
   * 
   */
  static saveWeiXinPayMarketingActivity(body: WeiXinPayMarketingActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/coupon-activity/saveWeiXinPayMarketingActivity`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
