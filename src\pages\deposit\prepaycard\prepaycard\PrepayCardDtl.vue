<template>
  <div class="prepay-card-dtl">
    <el-row class="header" style="border-bottom: 1px solid #eeeeee">
      <el-col :span="8">
        <el-row class="primary" :title="detail.code">
          <el-row class="card">
            <el-col :span="20" class="number"><span>卡号：</span>{{detail.code}}</el-col>
            <el-col :span="2" class="state" :style="{backgroundColor: parser.parseStateColor(detail.state)}">
              {{parser.parseState(detail.state)}}
            </el-col>
          </el-row>
        </el-row>
        <el-row class="secondary">
          <el-col :span="12">
            <span>卡类型：</span>{{detail.cardType}}
          </el-col>
          <el-col :span="12" v-if="enableMultipleAccount" no-i18n>
            <span>{{i18n('账户类型：')}}</span><span v-if="detail.account">[{{detail.account.id}}]{{detail.account.name}}</span>
            <span v-if="!detail.account">-</span>
          </el-col>
        </el-row>
        <el-row class="secondary">
          <span>持卡人：</span>{{dataUtil.showMemberId1(detail.ownerMobile,detail.ownerHdCardMbrId,detail.ownerHdCardCardNum)}}
        </el-row>
      </el-col>
      <el-col :span="1">
        <div class="vertical-line"></div>
      </el-col>
      <el-col :span="1">&nbsp;</el-col>
      <el-col :span="5" v-if="detail.cardType === i18n('次卡')">
        <el-row class="primary">
          <span>{{i18n('期初次数')}}：</span>
          {{detail.totalTimes || '-'}}
          <span>{{i18n('次')}}</span>
        </el-row>
      </el-col>
      <el-col :span="5" v-else>
        <el-row class="primary">
          <span>期初余额：</span>
          {{dataUtil.showAmount(detail.openingTotal)}}
          <span>元</span>
        </el-row>
        <el-row class="secondary">
          <span>期初实充：</span>
          {{dataUtil.showAmount(detail.openingBalance)}}
          <span>元</span>
        </el-row>
        <el-row class="secondary">
          <span>期初返现：</span>
          {{dataUtil.showAmount(detail.openingGiftBalance)}}
          <span>元</span>
        </el-row>
      </el-col>
      <el-col :span="1">
        <div class="vertical-line"></div>
      </el-col>
      <el-col :span="1">&nbsp;</el-col>
      <el-col :span="5" v-if="detail.cardType === i18n('次卡')">
        <el-row class="primary">
          <span>{{i18n('当前次数')}}：</span>
          {{detail.remainderTimes}}
          <span>{{i18n('次')}}</span>
        </el-row>
      </el-col>
      <el-col :span="5" v-else>
        <el-row class="primary">
          <span>当前余额：</span>
          {{dataUtil.showAmount(detail.total)}}
          <span>元</span>
        </el-row>
        <el-row class="secondary">
          <span>当前实充：</span>
          {{dataUtil.showAmount(detail.balance)}}
          <span>元</span>
        </el-row>
        <el-row class="secondary">
          <span>当前返现：</span>
          {{dataUtil.showAmount(detail.giftBalance)}}
          <span>元</span>
        </el-row>
      </el-col>
    </el-row>
    <el-row style="margin-top: 10px">
      <el-tabs v-model="activeName" no-i18n>
        <el-tab-pane name="账户流水" :label="i18n('账户流水')" />
        <el-tab-pane name="历史流水" :label="i18n('历史流水')">
          <el-tooltip slot="label" effect="dark" :content="i18n('卡回收之前的流水')" placement="right">
            <span>{{i18n('历史流水')}}</span>
          </el-tooltip>
        </el-tab-pane>
        <el-tab-pane name="转赠过程" :label="i18n('转赠过程')" v-if="detail.cardType === formatI18n('电子礼品卡')" />
        <el-tab-pane name="卡操作日志" :label="i18n('卡操作日志')" />
      </el-tabs>
    </el-row>

    <div class="current-page" v-if="activeName === '账户流水'" no-i18n>
      <el-table :data="prePayCardTransaction" border style="width: 100%;">
        <el-table-column fixed :label="i18n('交易时间')" prop="occurredTime" key="occurredTime">
          <template slot-scope="scope">
            <span>{{scope.row.occurredTime | yyyyMMddHHmmss}}</span>
          </template>
        </el-table-column>
        <el-table-column fixed :label="i18n('发生组织')" prop="occurredOrz" key="occurredOrz">
          <template slot-scope="scope">
            <el-tooltip class="item" effect="dark" :content="'['+scope.row.occurredOrg.id+']'+ scope.row.occurredOrg.name" placement="right">
              <span v-if="scope.row.occurredOrg">[{{scope.row.occurredOrg.id}}] {{scope.row.occurredOrg.name}}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column fixed :label="i18n('交易类型')" prop="category" key="category">
          <template slot-scope="scope">
            <el-tooltip class="item" effect="dark" :content="scope.row.category" placement="right">
              <span>{{scope.row.category}}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <template>
          <el-table-column fixed v-if="detail.cardType === i18n('次卡')" :label="i18n('卡原次数(次)')" prop="originTimes" key="originTimes">
            <template slot-scope="scope">
              <span>{{dataUtil.showAmount(scope.row.originTimes)}}</span>
            </template>
          </el-table-column>
          <el-table-column fixed v-else :label="i18n('卡原余额(元)')" prop="originAmount" key="originAmount">
            <template slot-scope="scope">
              <span>{{dataUtil.showAmount(scope.row.originAmount)}}</span>
            </template>
          </el-table-column>
        </template>
        <template>
          <el-table-column fixed v-if="detail.cardType === i18n('次卡')" :label="i18n('发生次数(次)')" prop="occurTimes" key="occurTimes">
            <template slot-scope="scope">
              <span>{{dataUtil.showAmount(scope.row.occurTimes)}}</span>
            </template>
          </el-table-column>
          <el-table-column fixed v-else :label="i18n('发生金额(元)')" prop="occurAmount" key="occurAmount">
            <template slot-scope="scope">
              <span>{{dataUtil.showAmount(scope.row.occurAmount)}}</span>
            </template>
          </el-table-column>
        </template>
        <template>
          <el-table-column fixed v-if="detail.cardType === i18n('次卡')" :label="i18n('发生后次数(次)')" prop="occurredTimes" key="occurredTimes">
            <template slot-scope="scope">
              <span>{{dataUtil.showAmount(scope.row.occurredTimes)}}</span>
            </template>
          </el-table-column>
          <el-table-column fixed v-else :label="i18n('发生后余额(元)')" prop="occurredAmount" key="occurredAmount">
            <template slot-scope="scope">
              <span>{{dataUtil.showAmount(scope.row.occurredAmount)}}</span>
            </template>
          </el-table-column>
        </template>
        <el-table-column fixed :label="i18n('交易流水号')" prop="transNo" key="transNo">
          <template slot-scope="scope">
            <span :title="scope.row.transNo">{{scope.row.transNo}}</span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination no-i18n :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
        @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background layout="total, prev, pager, next, sizes,  jumper">
      </el-pagination>
    </div>

    <div class="current-page" v-if="activeName === '历史流水'" no-i18n>
      <el-table :data="recoverBeforeTransaction" border style="width: 100%;">
        <el-table-column fixed :label="i18n('交易时间')" prop="occurredTime">
          <template slot-scope="scope">
            <span>{{scope.row.occurredTime | yyyyMMddHHmmss}}</span>
          </template>
        </el-table-column>
        <el-table-column fixed :label="i18n('发生组织')" prop="occurredTime">
          <template slot-scope="scope">
            <el-tooltip class="item" effect="dark" :content="'['+scope.row.occurredOrg.id+']'+ scope.row.occurredOrg.name" placement="right">
              <span v-if="scope.row.occurredOrg">[{{scope.row.occurredOrg.id}}] {{scope.row.occurredOrg.name}}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column fixed :label="i18n('交易类型')" prop="category">
          <template slot-scope="scope">
            <el-tooltip class="item" effect="dark" :content="scope.row.category" placement="right">
              <span>{{scope.row.category}}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <template>
          <el-table-column fixed v-if="detail.cardType === i18n('次卡')" :label="i18n('卡原次数(次)')" prop="originTimes" key="originTimes">
            <template slot-scope="scope">
              <span>{{dataUtil.showAmount(scope.row.originTimes)}}</span>
            </template>
          </el-table-column>
          <el-table-column fixed v-else :label="i18n('卡原余额(元)')" prop="originAmount" key="originAmount">
            <template slot-scope="scope">
              <span>{{dataUtil.showAmount(scope.row.originAmount)}}</span>
            </template>
          </el-table-column>
        </template>
        <template>
          <el-table-column fixed v-if="detail.cardType === i18n('次卡')" :label="i18n('发生次数(次)')" prop="occurTimes" key="occurTimes">
            <template slot-scope="scope">
              <span>{{dataUtil.showAmount(scope.row.occurTimes)}}</span>
            </template>
          </el-table-column>
          <el-table-column fixed v-else :label="i18n('发生金额(元)')" prop="occurAmount" key="occurAmount">
            <template slot-scope="scope">
              <span>{{dataUtil.showAmount(scope.row.occurAmount)}}</span>
            </template>
          </el-table-column>
        </template>
        <template>
          <el-table-column fixed v-if="detail.cardType === i18n('次卡')" :label="i18n('发生后次数(次)')" prop="occurredTimes" key="occurredTimes">
            <template slot-scope="scope">
              <span>{{dataUtil.showAmount(scope.row.occurredTimes)}}</span>
            </template>
          </el-table-column>
          <el-table-column fixed v-else :label="i18n('发生后余额(元)')" prop="occurredAmount" key="occurredAmount">
            <template slot-scope="scope">
              <span>{{dataUtil.showAmount(scope.row.occurredAmount)}}</span>
            </template>
          </el-table-column>
        </template>
        <el-table-column fixed :label="i18n('交易流水号')" prop="transNo">
          <template slot-scope="scope">
            <span :title="scope.row.transNo">{{scope.row.transNo}}</span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination no-i18n :current-page="historyPage.currentPage" :page-size="historyPage.size" :page-sizes="[10, 20, 30, 40]"
        :total="historyPage.total" @current-change="onHandleCurrentChangeForBeforeHst" @size-change="onHandleSizeChangeForBeforeHst" background
        layout="total, prev, pager, next, sizes,  jumper">
      </el-pagination>
    </div>

    <div class="current-page" v-if="activeName === '转赠过程'" no-i18n>
      <el-table key="转赠过程" :data="prePayCardPresent" :row-class-name="getRowClass" style="width: 100%;">
        <el-table-column fixed :label="i18n('持卡人')" prop="hdCardMbrId">
          <template slot-scope="scope">
            <span>{{dataUtil.showMemberId1(scope.row.mobile,scope.row.hdCardMbrId,scope.row.hdCardCardNum)}}</span>
          </template>
        </el-table-column>
        <el-table-column fixed :label="i18n('领取时间')" prop="receiveDate">
          <template slot-scope="scope">
            <span>{{dataUtil.format(scope.row.receiveDate)}}</span>
          </template>
        </el-table-column>
        <el-table-column fixed :label="i18n('转赠时间')" prop="presentDate">
          <template slot-scope="scope">
            <span>{{dataUtil.format(scope.row.presentDate)}}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="current-page" v-if="activeName === '卡操作日志'" no-i18n>
      <el-table key="卡操作日志" :data="prePayCardStateHst" :row-class-name="getRowClass1" style="width: 100%;">
        <el-table-column fixed :label="i18n('操作类型')" prop="category">
          <template slot-scope="scope">
            <span v-if="scope.row.category !== '卡补发'">{{ scope.row.category }}</span>
            <div v-if="scope.row.category === '卡补发'">
              <span>{{ scope.row.category }}</span>
              <p style="color: #999999">{{ i18n('旧卡：') }}{{ scope.row.oldCode }}</p>
            </div>
            <div v-else-if="scope.row.category ===i18n('卡有效期调整') ">
              <p style="color: #999999">{{ i18n('卡操作日志/原有效期：') }}&nbsp;{{ scope.row.oldExpireDate }}</p>
            </div>
          </template>
        </el-table-column>
        <el-table-column fixed :label="i18n('操作人')" prop="operator">
          <template slot-scope="scope">
            <span>{{scope.row.operator}}</span>
          </template>
        </el-table-column>
        <el-table-column fixed :label="i18n('操作时间')" prop="occurredTime">
          <template slot-scope="scope">
            <span>{{dataUtil.format(scope.row.occurredTime)}}</span>
          </template>
        </el-table-column>
        <el-table-column fixed :label="i18n('卡所属门店')" prop="occurredOrz">
          <template slot-scope="scope">
            <el-tooltip class="item" effect="dark" :content="scope.row.ownerOrg ? '['+scope.row.ownerOrg.id+']'+ scope.row.ownerOrg.name : '--'" placement="right">
              <span v-if="scope.row.ownerOrg">[{{scope.row.ownerOrg.id}}] {{scope.row.ownerOrg.name}}</span>
              <span v-else>--</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column fixed :label="i18n('卡状态')" prop="state">
          <template slot-scope="scope">
            <PrepayState :state="scope.row.state" />
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script lang="ts" src="./PrepayCardDtl.ts">
</script>

<style lang="scss">
.prepay-card-dtl {
  width: 100%;
  height: 100%;
  background-color: white;
  overflow: auto;

  .header {
    padding: 20px 0;

    .vertical-line {
      display: inline-block;
      margin-top: 20px;
      width: 1px;
      height: 60px;
      background-color: #dfe2e5;
    }

    .primary {
      font-weight: 600;
      font-style: normal;
      font-size: 20px;
      color: #515151;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      .card {
        white-space: nowrap;
        .number {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 270px;
        }
        .state {
          font-weight: 500;
          height: 23px;
          width: 50px;
          min-width: 50px;
          max-width: 50px;
          border-radius: 2px;
          font-size: 14px;
          line-height: 23px;
          text-align: center;
          color: white;
        }
      }
    }
    .secondary {
      color: rgba(51, 51, 51, 0.647058823529412);
      line-height: 25px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .current-page {
    height: calc(100% - 350px);

    .el-select {
      width: 100%;
    }

    .cell {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .high-light {
      font-weight: 600;
      background-color: #eeeeee;
    }
  }

  .el-tabs__header {
    margin: 0;
  }
  .el-tabs__nav-wrap::after {
    background-color: white;
  }
}
</style>
