import ApiClient from 'http/ApiClient'
import Response from 'model/default/Response'
import BenefitCardCancelReq from "model/benefitCard/BenefitCardCancelReq";
import BenefitCardCancelRes from "model/benefitCard/BenefitCardCancelRes";
import BenefitCardQueryReq from "model/benefitCard/BenefitCardQueryReq";
import BenefitCard from "model/benefitCard/BenefitCard";

export default class BenefitCardApi {
  /**
   * 作废付费会员卡
   * 作废付费会员卡。
   * 
   */
  static cancel(body: BenefitCardCancelReq): Promise<Response<BenefitCardCancelRes>> {
    return ApiClient.server().post(`/v1/benefitCard/card/cancel`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量导出
   * 批量导出。
   * 
   */
  static export(body: BenefitCardQueryReq): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/benefitCard/card/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询付费会员卡
   * 查询付费会员卡。
   * 
   */
  static query(body: BenefitCardQueryReq): Promise<Response<BenefitCard[]>> {
    return ApiClient.server().post(`/v1/benefitCard/card/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
