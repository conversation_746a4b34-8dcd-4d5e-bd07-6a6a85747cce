<template>
    <el-dialog :before-close="doBeforeClose"
               append-to-body
               :close-on-click-modal="false" :title="title" :visible.sync="dialogShow" class="import-dialog-view">
        <div class="wrap">
            <div class="left">{{formatI18n('/公用/导入', '实例模板')}}：
                <a style="line-height: 12px; color: #318BFF;font-size: 12px;text-decoration: none"
                   class="action-hover_download" @click="downloadTemplate">{{templateName}}</a>
                <!--                <el-button @click="doDownLoadTemplate" type="text">{{templateName}}</el-button>-->
            </div>
            <div class="left">{{getImportDesc()}}</div>
            <div class="left" v-if="showOrg">
                {{formatI18n('/会员/会员批量操作单/操作组织')}}：
                <SelectStores v-model="orgId" @change="$forceUpdate()" :isOnlyId="true" :hideAll="true" width="190px"
                  :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
                </SelectStores>
            </div>
            <el-upload
                    :headers="uploadHeaders"
                    :action="url"
                    :auto-upload="false"
                    :on-change="doHandleChange"
                    :on-error="getErrorInfo"
                    :on-success="getSuccessInfo"
                    :with-credentials="true"
                    :before-upload="beforeUpload"
                    class="upload-demo"
                    ref="upload">
                <el-button size="small" slot="trigger" type="default">{{formatI18n('/公用/导入', '选择文件')}}</el-button>
            </el-upload>
        </div>
        <div class="dialog-footer" slot="footer">
            <el-button @click="doModalClose('cancel')">{{formatI18n('/公用/按钮', '取消')}}</el-button>
            <el-button @click="doModalClose('confirm')" size="small" type="primary">{{formatI18n('/公用/按钮', '确定')}}
            </el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts" src="./BatchOperateImportDialog.ts">
</script>

<style lang="scss">
    .import-dialog-view {
        display: flex;
        align-items: center;
        justify-content: center;
        .el-dialog {
            width: 500px;
            height: 310px;
            margin-top: 0px !important;
            .el-dialog__body {
                height: 200px;
                word-break: keep-all;
            }
        }
        .wrap {
            padding-top: 15px;
            padding-left: 70px;
            .left {
                text-align: left;
                margin-bottom: 10px;
            }
        }
    }

.action-hover_download {
  cursor: pointer;
  /* 添加手型光标 */
}
</style>