<template>
  <el-form class="activity-time-frame" ref="form" :model="form.data">
    <el-row>
      <el-radio-group :disabled="disabled" v-model="form.data.type" @change="onChangeType">
        <el-radio label="all">{{formatI18n('/公用/公共组件/时间条件控件/表单/全部时间')}}</el-radio>
        <el-radio label="day">{{formatI18n('/公用/公共组件/时间条件控件/表单/按天')}}</el-radio>
        <el-radio v-if="!isHideWeek" label="week">{{formatI18n('/公用/公共组件/时间条件控件/表单/按周')}}</el-radio>
        <el-radio v-if="!isHideMonth" label="month">{{formatI18n('/公用/公共组件/时间条件控件/表单/按月')}}</el-radio>
      </el-radio-group>
    </el-row>
    <el-row class="day-row" v-if="form.data.type === 'day'">
      <div class="left">{{formatI18n('/公用/公共组件/时间条件控件/表单/每天')}}</div>
      <div class="right">
        <div class="right-row" v-for="(line, index) of form.data.dayLines" :key="index">
          <el-form-item :rules="form.rules.timePickerRules" :prop="`dayLines[${index}]`" class="auto-expand-form-item">
            <el-time-picker
                :disabled="disabled"
                style="width: 220px"
                is-range
                format="HH:mm"
                @change="onChange"
                v-model="form.data.dayLines[index]"
                :picker-options="dateRangeOption"
                range-separator="-"
                :start-placeholder="formatI18n('/公用/公共组件/时间条件控件/表单/开始时间')"
                :end-placeholder="formatI18n('/公用/公共组件/时间条件控件/表单/结束时间')"
                :placeholder="formatI18n('/公用/公共组件/时间条件控件/表单/选择时间范围')">
            </el-time-picker>
          </el-form-item>
          &nbsp;&nbsp;<a v-show="!disabled" v-if="form.data.dayLines.length > 1" @click="delLine(form.data.dayLines, index)"
                         style="color: red">{{formatI18n('/公用/公共组件/时间条件控件/表单/删除')}}</a>
        </div>
        <el-row stye="margin-top: 5px">
          <el-button :disabled="disabled" type="text" @click="addDayLine" v-if="form.data.dayLines.length < 10">
            +{{ formatI18n('/公用/公共组件/时间条件控件/表单/添加') }}
          </el-button>
        </el-row>
      </div>
    </el-row>
    <el-row class="week-row" v-if="form.data.type === 'week'">
      <div class="left">{{formatI18n('/公用/公共组件/时间条件控件/表单/每周')}}</div>
      <div class="right">
        <div class="right-row" v-for="(line, index) of form.data.weekLines" :key="index">
          <el-form-item :rules="form.rules.arrRules" :prop="`weekLines[${index}].weeks`" class="auto-expand-form-item">
            <el-checkbox-group :disabled="disabled" v-model="form.data.weekLines[index].weeks" @change="onChange">
              <el-checkbox-button v-for="i in range(1, 7)" :label="i" :key="i">{{translateWeek(i)}}</el-checkbox-button>
            </el-checkbox-group>
          </el-form-item>
          &nbsp;&nbsp;
          <el-form-item :rules="form.rules.timePickerRules" :prop="`weekLines[${index}].dateRange`"
                        class="auto-expand-form-item">
            <el-time-picker
                :disabled="disabled"
                style="width: 220px"
                is-range
                format="HH:mm"
                @change="onChange"
                :picker-options="dateRangeOption"
                v-model="form.data.weekLines[index].dateRange"
                range-separator="-"
                :start-placeholder="formatI18n('/公用/公共组件/时间条件控件/表单/开始时间')"
                :end-placeholder="formatI18n('/公用/公共组件/时间条件控件/表单/结束时间')"
                :placeholder="formatI18n('/公用/公共组件/时间条件控件/表单/选择时间范围')">
            </el-time-picker>
          </el-form-item>
          &nbsp;&nbsp;<a v-show="!disabled" v-if="form.data.weekLines.length > 1" @click="delLine(form.data.weekLines, index)"
                         style="color: red">{{formatI18n('/公用/公共组件/时间条件控件/表单/删除')}}</a>
        </div>
        <el-row>
          <el-button :disabled="disabled" type="text" @click="addWeekLine" v-if="form.data.weekLines.length < 10">
            +{{ formatI18n('/公用/公共组件/时间条件控件/表单/添加') }}
          </el-button>
        </el-row>
      </div>
    </el-row>
    <el-row class="month-row" v-if="form.data.type === 'month'">
      <div class="left">{{formatI18n('/公用/公共组件/时间条件控件/表单/每月')}}</div>
      <div class="right">
        <div class="right-row" v-for="(line, index) of form.data.monthLines" :key="index" style="margin-top: 5px">
          <el-form-item :rules="form.rules.arrRules" :prop="`monthLines[${index}].days`" class="auto-expand-form-item">
            <el-select :disabled="disabled" style="width: 406px" @change="onChange" v-model="form.data.monthLines[index].days" multiple
                       clearable
                       :placeholder="formatI18n('/公用/公共组件/时间条件控件/表单/请选择')">
              <el-option
                  v-for="i in range(1, 31)"
                  :key="i"
                  :label="formatDay(i)"
                  :value="i">
              </el-option>
            </el-select>
          </el-form-item>
          &nbsp;&nbsp;
          <el-form-item :rules="form.rules.timePickerRules" :prop="`monthLines[${index}].dateRange`"
                        class="auto-expand-form-item">
            <el-time-picker
                :disabled="disabled"
                style="width: 220px;height: 40px"
                is-range
                format="HH:mm"
                @change="onChange"
                :picker-options="dateRangeOption"
                v-model="form.data.monthLines[index].dateRange"
                range-separator="-"
                :start-placeholder="formatI18n('/公用/公共组件/时间条件控件/表单/开始时间')"
                :end-placeholder="formatI18n('/公用/公共组件/时间条件控件/表单/结束时间')"
                :placeholder="formatI18n('/公用/公共组件/时间条件控件/表单/选择时间范围')">
            </el-time-picker>
            &nbsp;&nbsp;<a v-show="!disabled" v-if="form.data.monthLines.length > 1" @click="delLine(form.data.monthLines, index)"
                           style="color: red">{{formatI18n('/公用/公共组件/时间条件控件/表单/删除')}}</a>
          </el-form-item>
        </div>
        <el-row>
          <el-button :disabled="disabled" type="text" @click="addMonthLine" v-if="form.data.monthLines.length < 10">
            +{{ formatI18n('/公用/公共组件/时间条件控件/表单/添加') }}
          </el-button>
        </el-row>
      </div>
    </el-row>
  </el-form>
</template>

<script lang="ts" src="./DateTimeConditionPicker.ts">
</script>

<style lang="scss" scoped>
  .activity-time-frame {
    .day-row {
      display: flex;

      .left {
        width: 90px;
      }

      .right {
        .right-row {
          display: flex;
          align-items: center;
        }
      }
    }

    .week-row {
      display: flex;

      .left {
        width: 90px;
      }

      .right {
        .right-row {
          display: flex;
        }
      }
    }

    .month-row {
      display: flex;

      .left {
        width: 90px;
      }

      .right {
        .right-row {
          display: flex;
        }
      }

      ::v-deep .el-date-editor {
        .el-range__icon, .el-range-separator, .el-range__close-icon {
          line-height: 30px !important;
        }
      }
    }

    .el-checkbox-button + .el-checkbox-button {
      margin-left: 6px;
    }

    ::v-deep .el-checkbox-button__inner {
      border: 1px solid #E0E3E8 !important;
      border-radius: 3px;
    }

    ::v-deep .el-checkbox-button.is-checked .el-checkbox-button__inner {
      color: white;
      background-color: var(--color-primary);
      border: 1px solid #4AA0F8 !important;
    }

    ::v-deep .el-date-editor {
      .el-range__icon, .el-range-separator, .el-range__close-icon {
        line-height: 24px !important;
      }
    }
  }
</style>
