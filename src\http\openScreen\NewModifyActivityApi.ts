/*
 * @Author: 黎钰龙
 * @Date: 2022-11-29 17:26:07
 * @LastEditTime: 2022-12-01 10:30:44
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\openScreen\NewModifyActivityApi.ts
 * 记得注释
 */
import NewModifyActivity from 'model/open-promotion/new-modify-activity/NewModifyActivity'  //(新建、修改活动)接口的数据模型
import Response from 'model/common/Response'
import ApiClient from 'http/ApiClient'

export default class NewModifyActivityApi {
  /**
 * 保存
 *
 */
  static save(body: NewModifyActivity): Promise<Response<string>> {
    return ApiClient.server().post('/v1/coupon-activity/saveWeixinBrandActivity', body, {
    })
      .then(res => {
        return res.data
      })
  }
}