<!--
 * @Author: 黎钰龙
 * @Date: 2023-02-24 16:33:58
 * @LastEditTime: 2024-04-24 13:35:33
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\benefit\equity-card\edit\EditEquityCard.vue
 * 记得注释
-->
<template>
  <div class="edit-container">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button @click="doSave" type="primary">
          {{ formatI18n('/公用/按钮', '保存') }}
        </el-button>
        <el-button @click="doCancel">
          {{ formatI18n('/公用/按钮', '取消') }}
        </el-button>
      </template>
    </BreadCrume>

    <el-form :model="form.data" :rules="form.rules" ref="form" label-width="150px" v-loading="loading.detail">
      <div class="basic-settings">
        <div class="header">{{  i18n('/储值/预付卡/卡模板/编辑页面/基础设置')  }}</div>
        <el-form-item :label="i18n('/储值/预付卡/卡模板/编辑页面/卡模板名称')" prop="name">
          <el-input maxlength="20" :placeholder="i18n('/公用/表单校验/请输入不超过{0}个字符').replace(/\{0\}/g,20)" style="width: 350px" v-model="form.data.name" />
          <span class="gray-tips" style="margin-left:10px">{{i18n('必须跟微盟卡名称保持一致，如果没上微盟可忽略')}}</span>
        </el-form-item>
        <el-form-item :label="i18n('卡号规则')" prop="number">
          <div class="gray-tips">-{{i18n('为空表示系统随机生成，卡模板保存后此字段不可修改')}}</div>
          <span style="margin-right:10px">{{i18n('/营销/券礼包活动/核销第三方券/固定开头')}}</span>
          <el-input maxlength="6" :disabled="$route.query.type === 'edit'" :placeholder="i18n('请输入六位以内的数字或字母')" style="width: 350px"
            v-model="form.data.codePrefix" />
        </el-form-item>
        <el-form-item :label="i18n('有效期设置')" prop="availableDate">
          <table>
            <tr class="form-tr">
              <td>{{i18n('/储值/预付卡/卡模板/编辑页面/有效期')}}</td>
              <td>{{i18n('/会员/等级/付费等级流水报表/列表页面/支付金额')}}</td>
              <td>{{i18n('续费价格(不填则是原价)')}}</td>
            </tr>
            <tr class="form-tr tr-info" v-for="(item,index) in availableDate">
              <th class="form-th">
                <AutoFixInput :min="1" :max="autoInputValidityNum(index)" style="width: 100px" v-model="item.validityInfo.validityDays"
                  placeholder="请输入">
                </AutoFixInput>
                <el-select v-model="item.validityInfo.expiryType" style="width: 70px" @change="changeSelect(index)">
                  <el-option :label="i18n('/储值/预付卡/卡模板/编辑页面/天')" value="DAYS">
                  </el-option>
                  <el-option :label="i18n('/公用/券模板/月')" value="MONTHS">
                  </el-option>
                  <el-option :label="i18n('/储值/预付卡/卡模板/编辑页面/年')" value="YEARS">
                  </el-option>
                </el-select>
              </th>
              <th class="form-th">
                <AutoFixInput :min="0.01" :max="99999.99" :fixed="2" style="width: 140px" v-model="item.price" :appendTitle="i18n('/公用/券模板/元')">
                </AutoFixInput>
              </th>
              <th class="form-th">
                <AutoFixInput :min="0.01" :max="99999.99" :fixed="2" style="width: 140px" v-model="item.renewPrice" :appendTitle="i18n('/公用/券模板/元')">
                </AutoFixInput>
                <el-button type="text" style="margin-left: 15px" @click="doRemoveProject(index)">{{i18n('/公用/预约文件列表/删除')}}</el-button>
              </th>
            </tr>
            <div>
              <el-button @click="doAddProject">+ {{i18n('/权益/积分/积分初始化/未初始化状态/得积分规则/基础得积分/点击立即设置/添加规则')}}</el-button>
              <span style="margin-left:10px;color:rgb(153, 153, 153)">{{i18n('最多设置10个档位')}}</span>
            </div>
          </table>
        </el-form-item>
        <el-form-item :label="i18n('/储值/预付卡/卡模板/编辑页面/使用须知')" prop="remark">
          <el-input type="textarea" v-model="form.data.remark" :rows="10" style="width: 500px;" />
        </el-form-item>
      </div>
      <!-- <el-divider></el-divider> -->
      <!-- <div class="card-present" style="display:none">
        <div class="header" style="padding-top:0">{{  i18n('领卡奖励')  }}</div>
        <div class="gray-tips" style="margin-top:-20px;margin-left:100px">对应渠道领卡后立即发放奖励</div>
        <el-form-item :label="i18n('领卡渠道')" prop="channel" style="margin-top:10px">
          <el-select v-model="selectChannel" placeholder="请选择渠道" style="width: 170px" multiple>
            <el-option :label="item.name" :value="item.channel.id + item.channel.type" v-for="(item,index) in receiveCardChannel" :key="index">
            </el-option>
          </el-select>
        </el-form-item>
        <FormItem style="position: relative;">
          <div style="width: 70%;margin-top:10px">
            <div class="item">
              <el-checkbox v-model="giftBag.points.isSelect">
                {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/新建/赠送积分') }}&nbsp;&nbsp;
              </el-checkbox>
              <AutoFixInput style="width: 80px" :min="1" :max="99999" :fixed="0" v-model="giftBag.points.value"
                :disabled="!giftBag.points.isSelect" />
              {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/新建/赠送积分/个') }}
            </div>
            <div class="item" style="margin-top:10px">
              <el-checkbox v-model="giftBag.coupons.isSelect" @change="clearCoupons">
                {{ i18n('赠优惠券') }}
              </el-checkbox>
              <el-button @click="doAddCoupon('addCoupon')" type="text" v-show="giftBag.coupons.isSelect && !giftBag.coupons.list.length">
                +{{ formatI18n('/会员/等级/等级管理/未初始化状态/点击立即开始免费等级初始化/输入所有必填项点击下一步/然后输入所有必填项点击下一步/等级月礼下的表格', '添加券') }}
              </el-button>
              <div v-show="giftBag.coupons.isSelect">
                <ActiveAddCoupon ref="addCoupon" v-model="giftBag.coupons.list" :maxLimit="10" :excludedTypes="excludedTypes"></ActiveAddCoupon>
              </div>
            </div>
          </div>
        </FormItem>
      </div> -->
      <el-divider></el-divider>
      <div class="card-equity">
        <div class="header" style="padding-top:0">{{  i18n('领卡权益')  }}</div>
        <el-form-item :label="i18n('/营销/大转盘活动/优惠券')">
          <el-button @click="doAddCoupon('addEquityCoupon')" type="text" v-if="!cardEquity.coupons.length">
            {{ formatI18n('/会员/等级/等级管理/未初始化状态/点击立即开始免费等级初始化/输入所有必填项点击下一步/然后输入所有必填项点击下一步/等级月礼下的表格', '添加券') }}
          </el-button>
          <ActiveAddCoupon ref="addEquityCoupon" v-model="cardEquity.coupons" :maxLimit="10" :excludedTypes="excludedTypes">
          </ActiveAddCoupon>
        </el-form-item>
        <el-form-item :label="i18n('发放时间')" prop="equity" v-show="cardEquity.coupons.length">
          <el-tooltip placement="top-start" effect="light">
            <div slot="content">
              {{i18n('eg：每3个自然月发放一次，一共发3次。')}}<br />
              {{i18n('某顾客2022.11.30日领卡有效期为1年， 发3次赠礼，分别 是2022.2.28（因为2月没有30号则是2月最后一天发）发一次，2022.5.30发一次，2022.8.30 发一次。')}}<br />
              {{i18n('某顾客2022.11.30日领卡有效期为6个月，发3次赠礼，分别是2022.2.28（因为2月没有30号则是2月最后一天发）发一次，2022.5.30发一次，此时卡过期，还剩一次不再发放')}}<br />
              {{i18n('每1周周四发放一次，一共发3次。')}}<br />
              {{i18n('某顾客2022.11.30 （周三）日领卡有效期为1年， 发3次赠礼，分别 是2022.12.8 （周四）发一次，2022.12.15发一次，2022.12.22 发一次。')}}</div>
            <el-button style="padding:0;border:none"><i class="el-icon-warning" style="color: #999999" /></el-button>
          </el-tooltip>
          <i18n k="/会员/权益卡设置/领卡后每{0}发放一次，一共发{1}次">
            <template slot="0">
              <AutoFixInput style="width: 80px;margin-right:10px" :min="1" :max="provideInputValidityNum()" :fixed="0"
                v-model.number="cardEquity.intervalVal" />
              <el-select v-model="cardEquity.intervalType" style="width: 100px;margin-right:10px" @change="changeProvideSelect()">
                <el-option :label="i18n('个自然月')" value="MONTH">
                </el-option>
                <el-option :label="i18n('/储值/预付卡/卡模板/编辑页面/天')" value="DAY">
                </el-option>
                <el-option :label="i18n('/公用/日期/周')" value="WEEK">
                </el-option>
              </el-select>
              <el-select v-model="cardEquity.weekDay" style="width: 100px;margin-right:10px" v-if="cardEquity.intervalType === 'WEEK'">
                <el-option :label="i18n('周一')" :value="1">
                </el-option>
                <el-option :label="i18n('周二')" :value="2">
                </el-option>
                <el-option :label="i18n('周三')" :value="3">
                </el-option>
                <el-option :label="i18n('周四')" :value="4">
                </el-option>
                <el-option :label="i18n('周五')" :value="5">
                </el-option>
                <el-option :label="i18n('周六')" :value="6">
                </el-option>
                <el-option :label="i18n('周日')" :value="7">
                </el-option>
              </el-select>
            </template>
            <template slot="1">
              <AutoFixInput style="width: 80px" :min="1" :max="99999" :fixed="0" v-model.number="cardEquity.total" />
            </template>
          </i18n>。
          <i18n k="/会员/权益卡设置/领卡当{0}不发领卡权益设置的优惠券">
            <template
              slot="0">{{cardEquity.intervalType === 'MONTH' ? i18n('/公用/券模板/月') : cardEquity.intervalType === 'DAY' ? i18n('/公用/券模板/天') : i18n('/公用/日期/周')}}</template>
          </i18n>
          <div class="gray-tips">{{i18n('如果赠礼次数未满但卡已过期，则不再继续发放赠礼')}}</div>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script lang="ts" src="./EditEquityCard.ts">
</script>

<style lang="scss">
.edit-container {
  width: 100%;
  height: 100%;
  background: white;
  overflow: auto;

  .header {
    font-weight: 500;
    padding: 20px 20px 0 20px;
    font-size: 18px;
  }
  .basic-settings {
    .form-tr {
      display: flex;
      width: 600px;
      justify-content: space-between;
      align-items: center;
      border: 1px solid #f2f2f2;

      td {
        flex: 1;
        text-align: center;
        background-color: #f2f2f2;
      }
      .form-th {
        flex: 1;
        display: flex;
        justify-content: center;
      }
    }
    .tr-info {
      height: 60px;
    }
  }

  .gray-tips {
    color: rgb(153, 153, 153);
    margin-top: -10px;
    margin-bottom: -5px;
  }
  .active-add-coupon {
    position: relative;
    top: -23px;
  }
}
</style>