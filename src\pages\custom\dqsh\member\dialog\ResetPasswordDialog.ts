import {Component, Prop, Vue} from 'vue-property-decorator'
import MemberApi from 'http/member_v2/MemberApi'
import MemberPass from 'model/member_v2/member/MemberPass'

@Component({
  name: 'ResetPasswordDialog',
  components: {}
})
export default class ResetPasswordDialog extends Vue {
  $refs: any
  ruleForm = {
    password: '',
    confirmPassword: ''
  }
  rules = {
    password: [
      { required: true, validator: (rule: any, value: any, callback: any) => {
          if (value) {
            let regex = /^[0-9]\d*$/g
            if (regex.test(value)) {
              if (value.length < 6) {
                callback(new Error('密码为6为数字'))
              } else {
                callback()
              }
            } else {
              callback(new Error('输入不合法，密码为6为数字'))
            }
          } else {
            callback(new Error('请输入名称'))
          }
        }, trigger: 'change' }
    ],
    confirmPassword: [
      { required: true, validator: (rule: any, value: any, callback: any) => {
          if (value) {
            let regex = /^[0-9]\d*$/g
            if (regex.test(value)) {
              if (value.length < 6) {
                callback(new Error('密码为6为数字'))
              } else {
                callback()
              }
            } else {
              callback(new Error('输入不合法，密码为6为数字'))
            }
          } else {
            callback(new Error('请输入名称'))
          }
        }, trigger: 'change' }
    ]
  }
  @Prop()
  data: any
  @Prop()
  title: any

  @Prop({
    type: Boolean,
    default: false
  })
  dialogShow: boolean

  doBeforeClose(done: any) {
    this.$emit('dialogClose')
    done()
  }
  doModalClose() {
    if(this.ruleForm.password !== this.ruleForm.confirmPassword) {
      this.$message.warning('密码不一致，请重新输入')
      return
    }
    this.$refs['ruleForm'].validate((valid: any) => {
      if (valid) {
        let param: MemberPass = new MemberPass()
        param.memberId = this.$route.query.id as string
        param.password = this.ruleForm.password
        MemberApi.modifyPayPass(param).then((resp: any) => {
          if (resp && resp.code === 2000) {
            this.$message.success('重置密码成功')
            this.$emit('dialogClose')
          }
        }).catch((error: any) => {
          if (error && error.message) {
            this.$message.error(error.message)
          }
        })
      } else {
        return false;
      }
    })
  }
  doCancel() {
    this.$emit('dialogClose')
  }
  doCheckGoods() {
    // todo
  }
}