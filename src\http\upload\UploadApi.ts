import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import UploadResult from 'model/upload/UploadResult'

export default class UploadApi {
  /**
   * 上传
   *
   */
  static upload(body: any): Promise<Response<UploadResult>> {
    return ApiClient.server().post(`/v1/upload/upload`, body, {}).then((res) => {
      return res.data
    })
  }

    /**
   * 批量上传
   *
   */
     static batchUpload(body: any): Promise<Response<UploadResult[]>> {
      return ApiClient.server().post(`/v1/upload/batchUpload`, body, {}).then((res) => {
        return res.data
      })
    }

  /**
   * 获取下载全路径
   *
   */
  static getUrl(ossKey: string): Promise<Response<string>> {
    return ApiClient.server().get(`/v1/upload/get`, {
      params: {
        ossKey: ossKey
      }
    }).then((res) => {
      return res.data
    })
  }
}
