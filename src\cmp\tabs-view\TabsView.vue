<template>
  <div class="tab-view-container">
    <div class="tabs-container">
      <div class="tabs-header">
        <div :class="currentGoodsTab == index ? 'tabs-item active' : 'tabs-item'" :key="index" v-for="(item,index) in tabList"
          @click="doClickTabs(index)">
          <span>{{i18n('第{0}组').replace(/\{0\}/g, index + 1)}}</span>
        </div>
      </div>
      <div v-for="(item,index) in tabList" :key="index">
        <slot name="body" :row="tabList[currentGoodsTab]" :index="index" v-if="index == currentGoodsTab"></slot>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./TabsView.ts">
</script>

<style lang="scss" scoped>
.tab-view-container {
  .tabs-container {
    .tabs-header {
      display: flex;
      align-items: center;
      min-width: 710px;
      border-bottom: 1px solid #d7dfeb;
    }
    .tabs-item {
      display: flex;
      justify-content: center;
      align-items: center;
      border: 1px solid #d7dfeb;
      background-color: rgb(247 249 252);
      width: 90px;
      height: 34px;
      margin-bottom: -1px;
      cursor: pointer;

      .el-icon-close {
        margin-left: 4px;
        color: #d7dfeb;
        font-size: 16px;
        &:hover {
          color: #20a0ff;
        }
      }

      .el-icon-circle-plus-outline {
        font-size: 14px;
      }
    }
    .active {
      border-bottom: 1px solid #ffffff;
      color: #007eff;
      background-color: #ffffff;
    }
  }
}
</style>