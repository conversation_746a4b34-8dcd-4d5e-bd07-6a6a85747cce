<template>
  <div class="SGoodsForm">
    <div v-for="(item, index) in costParties" :key="index">
      <el-form label-width="0" :ref="'valiForm'+index" style="height: 60px;">
        <el-form-item style="display:inline-block;height: 56px;margin-bottom:0px;" :prop="'costparty'+index"
          :ref="'costparty'+index"
          :rules="[
            {
              validator: costpartyRule,
              require: true,
              trigger: ['change', 'blur']
            }
          ]"
        >
          <el-select @change="doPartiesChange(index)" :key="index" @visible-change="doFilterPartys(index)" style="width: 150px" value-key="label" v-model="partySelectArray[index]">
            <el-option v-for="item in partiesTmp[index]" :key="item.id" :label="item.name"  :value="item.id"  >{{item.name}}</el-option>
          </el-select>
        </el-form-item>
        <template>
          &nbsp;&nbsp;
          {{formatI18n('/营销/券礼包活动/核销第三方券', '承担用券金额')}}
          &nbsp;&nbsp;
          <template v-if="index == 0">
            <el-form-item style="display:inline-block;height: 56px;margin-bottom:0px;" :prop="'costType'+index"
              :ref="'costType'+index"
              :rules="[
                {
                  validator: costTypeRule,
                  require: true,
                  trigger: ['change', 'blur']
                }
              ]"
            >
              <el-select v-model="costAll" style="width: 150px" @change="costAllChange">
                <el-option :label='i18n("全部券抵扣金额")' value="all"></el-option>
                <el-option :label='i18n("部分券抵扣金额")' value="part"></el-option>
              </el-select>
            </el-form-item>
          </template>
          <template v-if="costAll == 'part' && (index != costParties.length - 1 || index == 0)">
            &nbsp;&nbsp;
            <span>{{ i18n('最多') }}</span>
            &nbsp;&nbsp;
            <el-form-item style="display:inline-block;height: 56px;margin-bottom:0px;width: 260px;" :prop="'AMOUNT'+index"
              :ref="'AMOUNT'+index"
              :rules="[
                {
                  validator: amountValRule,
                  require: true,
                  trigger: ['change', 'blur']
                }
              ]"
            >
              <el-input  @blur="doThisAmount(index)"  :placeholder="formatI18n('/营销/券礼包活动/核销第三方券/请输入')" style="width: 150px" v-model="partyInputArray[index]"></el-input>
              &nbsp;&nbsp;
              <span>{{ formatI18n('/公用/券模板/元') }}</span>
            </el-form-item>
          </template>
          <template v-else-if="costAll == 'part'">
            &nbsp;&nbsp;
            <span>{{ i18n('剩余券抵扣金额') }}</span>
            &nbsp;&nbsp;
          </template>
        </template>
        &nbsp;&nbsp; &nbsp;&nbsp;
        <el-button v-if="showDelBtn(index)" @click="doDeleteCostParties(index)" style="color: red" type="text">{{formatI18n('/公用/按钮', '删除')}}</el-button>
      </el-form>
    </div>
    <div>
      <el-button @click="doAddCostParties()" type="text">+{{formatI18n('/公用/券模板', '添加')}}</el-button>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Provide, Vue, Watch } from "vue-property-decorator";
import I18nPage from 'common/I18nDecorator'
import AmountToFixUtil from "util/AmountToFixUtil";

class CostPartyDetail {
  // 承担方
  party: Nullable<string> = null
  // 承担百分比/金额
  value: Nullable<number> = null
}
@Component({
	name: "SGoodsForm",
	components: {}
})
@I18nPage({
  prefix: ['/储值/会员储值/储值充值活动/列表页面','/营销/券礼包活动/券礼包活动', '/公用/按钮',"/公用/券模板"],
})
export default class SGoodsForm extends Vue {

  $refs: any;
  @Prop()
	value: any;
  @Prop()
	parties: any[]; // 承担方
  // 承担金额表单
  copyParties: any = []
  costParties: any = []
  partiesTmp: any =[]
  partySelectArray: any[] = []
  partyInputArray: any[] = []
  // parties: any[] = []
  // 承担类型：PROPORTION——按比例； AMOUNT——按金额
  typeCparties: string = 'AMOUNT'
  costAll: string = 'all'
  // 承担方对象
  costObj: any
  get showDelBtn() {
    let show: any
    return (index: any) => {
      if (this.costParties.length == 1) {
        show = false
      }
      // 按比例
      if (this.typeCparties == 'PROPORTION') {
        show = index != 0
      }
      // 按金额
      if (this.typeCparties == 'AMOUNT') {
        if (this.costAll == 'all') {
          show = false
        } else if (this.costAll == 'part') {
          show = this.costParties.length == 2 ? false : (index != 0 && index < this.costParties.length - 1)
        }
      }
      return show
    }
  }

  // 绑定的是数组
  @Watch('value', { deep: true })
  onValueChange(value: any) {
    // console.log('pppppppp', value);
    // this.costObj = {...value}
    // this.typeCparties = this.costObj.bearType && this.costObj.bearType != 'NONE' ? this.costObj.bearType : 'unset'
    // this.costAll = this.costObj.bearType == 'AMOUNT' ? this.costObj.amountType : null
    this.costAll = value && value.length > 0 ? value[0].value == 0 && value.length == 1 ? 'all' : 'part' : 'all'
    if (value && value.length > 0) {
      this.costParties = [...value]
      this.partySelectArray = []
      this.partyInputArray = []
      if (this.costParties && this.costParties.length > 0) {
        this.costParties.forEach((item: any, index: number) => {
          this.partySelectArray[index] = item.party
          this.partyInputArray[index] = item.value
          this.partiesTmp[index] = this.copyParties
          if (this.costAll == 'part') {
            this.partyInputArray[index] = index < this.costParties.length -1 ? item.value < 0 ? 1 : item.value : -1
          }
        })
      }
    } else {
      this.partySelectArray = []
      this.partyInputArray = []
      this.costParties = []
    }
  }
  @Watch('partySelectArray', { deep: true })
  onPartySelectArrayChange(value: any) {}

  @Watch('parties', { deep: true })
  onPartiesChange(value: any) {
    this.partiesTmp = [...this.parties]
    this.copyParties = [...this.parties[0]]
  }

  mounted() {
    this.partiesTmp = [...this.parties]
    this.copyParties = [...this.parties[0]]
    if(this.value && this.value.length > 0) {
      // 判断条件
      this.costAll = this.value.length == 1 && this.value[0].value == 0 ? 'all' : 'part'
      this.costParties = [...this.value]
      this.partySelectArray = []
      this.partyInputArray = []
      if (this.costParties && this.costParties.length > 0) {
        this.costParties.forEach((item: any, index: number) => {
          this.partySelectArray[index] = item.party
          this.partyInputArray[index] = item.value
          this.partiesTmp[index] = this.copyParties
        })
      }
    } else {
      this.costAll = 'all'
      this.partiesTmp = [...this.parties]
      this.copyParties = [...this.parties[0]]
      let item: CostPartyDetail = new CostPartyDetail()
      this.costParties.push(item)
      this.partySelectArray.push('')
      this.partyInputArray.push(1)
      this.$emit('input', this.transParams())
      this.$emit('change')
      this.$nextTick(() => {
        this.formVali()
      })
    }
  }

  // 表单校验 --
  // 校验方法
  costpartyRule(rule: any, value: any, callback: any) {
    // console.log('222222', rule ,value);
    let filed = this. getIndex(rule.field, 'costparty')
    const val = this.partySelectArray[filed]
    // console.log('111111', filed, val);
    if (!val) {
      callback(this.i18n('请选择承担方！'))
    }
    callback()
  }
  doPartiesChange(pos: number) {
    // 校验
    this.formVali()
    this.$emit('input', this.transParams())
    this.$emit('change')
  }
  doDeleteCostParties(index: number) {
    this.partySelectArray.splice(index, 1)
    this.partiesTmp.splice(index, 1)
    this.costParties.splice(index, 1)
    this.partyInputArray.splice(index, 1)
    // 校验
    this.formVali()
    this.$emit('input', this.transParams())
    this.$emit('change')
  }
  doAddCostParties() {
    if (this.typeCparties === 'AMOUNT' && this.costAll == 'all') {
      return
    }
    let item: CostPartyDetail = new CostPartyDetail()
    this.costParties.push(item)
    this.partySelectArray.push('')
    this.partyInputArray.push(1)
    // 校验
    this.formVali()
    this.$emit('input', this.transParams())
    this.$emit('change')
    if (this.typeCparties === 'AMOUNT' && this.costAll == 'part') {
      this.partyInputArray.forEach((item, index) => {
        if (item < 0 && index != this.partyInputArray.length -1 && index!= 0) {
          item = 1
        }
      })
    }
  }
  doThisAmount(index: number) {
    if (isNaN(parseFloat(this.partyInputArray[index]))) {
      this.partyInputArray[index] = 1
    } else {
      this.partyInputArray[index] = parseFloat(this.partyInputArray[index])
    }
    if (this.typeCparties === 'AMOUNT') {
      this.$set(this.partyInputArray, index, AmountToFixUtil.formatAmount(this.partyInputArray[index], 999999.99, 0.01, 2))
    } else {
      this.$set(this.partyInputArray, index, AmountToFixUtil.formatAmount(this.partyInputArray[index], 100, 1, ''))
    }
    // 校验
    this.formVali()
    this.$emit('input', this.transParams())
    this.$emit('change')
  }
  doFilterPartys(pos: number) {
    console.log('aaaaaaa');
    let record: any = JSON.parse(JSON.stringify(this.copyParties))
    if (this.partySelectArray && this.partySelectArray.length > 0) {
      this.partySelectArray.forEach((sub: any) => {
        if (record && record.length > 0) {
          record.forEach((item: any, index: number) => {
            if (item.costParty.id === sub && item.costParty.id !== this.partySelectArray[pos]) {
              record.splice(index, 1)
            }
          })
        }
      })
      this.partiesTmp[pos] = record
      this.$forceUpdate()
    }
  }
  costTypeRule(rule: any, value: any, callback: any) {
    // costAll
    callback()
  }
  amountValRule(rule: any, value: any, callback: any) {
    let filed = this.getIndex(rule.field, 'AMOUNT')
    const val = this.partyInputArray[filed]
    if (!val) {
      callback(this.i18n('请填写承担用券金额（大于0）！'))
    }
    callback()
  }

  
  // 按金额是否全部承担
  costAllChange(val: any) {
    // 全部，只留一个
    if (val == 'all') {
      this.costParties = this.costParties.length ? [{party: this.costParties[0].part || '', value: 1}] : [{party: '', value: 1}]
    }
    // 部分，必须有俩
    if (val == 'part' && this.costParties.length < 2) {
      this.costParties.push({
        party: '', value: 1
      })
    }
    // 校验
    this.formVali()
    // 重置数据
    this.$emit('input', this.transParams())
    this.$emit('change')
  }
  // 获取是哪一行数据
  getIndex(str: string, field: string) {
    return parseInt(str.replace(field, '') || '0')
  }
  // 校验各个表单
  formVali() {
    // this.$nextTick(() => {
    // })
    setTimeout(() => {
      this.formValiPromise()
    }, 300);
  }
  formValiPromise() {
    return new Promise((resolve, reject) => {
      if (this.typeCparties != 'AMOUNT' && this.typeCparties != 'PROPORTION') {
        resolve(true)
      } else {
        this.costParties.forEach((item: any, index: any) => {
          // console.log(this,'-----' , this.$refs, this.$refs.valiForm0);
          if (this.typeCparties == 'PROPORTION') {
            ;(this.$refs['valiForm'+index] as any)[0].validateField('costparty'+index)
            ;(this.$refs['valiForm'+index] as any)[0].validateField('PROPORTIONVAL'+index)
          } else if (this.typeCparties == 'AMOUNT') {
            ;(this.$refs['valiForm'+index] as any)[0].validateField('costparty'+index)
            ;this.$refs['costType'+index] && (this.$refs['valiForm'+index] as any)[0].validateField('costType'+index)
            if (this.costAll == 'part') {
              ;this.$refs['AMOUNT'+index] && (this.$refs['valiForm'+index] as any)[0].validateField('AMOUNT'+index)
            }
          }
        })
        let validateMessage = false
        for (let index = 0; index < this.costParties.length; index++) {
          let strCParty = ''
          let strPVal = ''
          let strAVal = ''
          if (this.typeCparties == 'PROPORTION') {
            // 'costparty'+index
            strCParty = this.$refs['costparty'+index] && this.$refs['costparty'+index][0] && this.$refs['costparty'+index][0].validateMessage ? this.$refs['costparty'+index][0].validateMessage : ''
            // 'PROPORTIONVAL'+index        
            strPVal = this.$refs['PROPORTIONVAL'+index] && this.$refs['PROPORTIONVAL'+index][0] && this.$refs['PROPORTIONVAL'+index][0].validateMessage ? this.$refs['PROPORTIONVAL'+index][0].validateMessage : ''
          } else if (this.typeCparties == 'AMOUNT') {
            // 'costparty'+index
            strCParty = this.$refs['costparty'+index] && this.$refs['costparty'+index][0] && this.$refs['costparty'+index][0].validateMessage ? this.$refs['costparty'+index][0].validateMessage : ''
            // 'costType'+index(不用校验，已写死，有值)
            if (this.costAll == 'part') {
            // 'AMOUNT'+index
            strAVal = this.$refs['AMOUNT'+index] && this.$refs['AMOUNT'+index][0] && this.$refs['AMOUNT'+index][0].validateMessage ? this.$refs['AMOUNT'+index][0].validateMessage : ''
            }
          }
          if (strCParty || strPVal || strAVal) {
            validateMessage = true
            break
          }       
        }
        if (validateMessage) {
          reject()
        } else {
          resolve(true)
        }
      }
    })
  }
  private transParams() {
    let arr: CostPartyDetail[] = []
    if (this.costParties && this.costParties.length > 0) {
      this.costParties.forEach((item: any, index: number) => {
        let cost: CostPartyDetail = new CostPartyDetail()
        cost.party = this.partySelectArray[index]
        cost.value = parseFloat(this.partyInputArray[index] as any)
        // cost.percent = this.partyInputArray[index] as any
        // cost.bearType = item.bearType
        arr.push(cost)
      })
    }
    // 按金额 类型， 值为0 是全部券抵扣金额，值为-1 是剩余券抵扣金额
    if (this.typeCparties == 'AMOUNT') {
      if (this.costAll == 'all') {
        arr = arr.length ? [arr[0]] : [{party: '', value: 1}]
        arr[0].value = 0
      } else if (this.costAll == 'part') {
        arr[arr.length - 1].value = -1
      }
    }
    return arr
  }
}
</script>
<style lang="scss" scoped>
.SGoodsForm {
  text-align: left;
  padding-left: 20px;
}
</style>