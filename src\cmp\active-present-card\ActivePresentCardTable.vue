<!--
 * @Author: 黎钰龙
 * @Date: 2023-12-13 18:01:06
 * @LastEditTime: 2024-01-11 10:41:15
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\active-present-card\ActivePresentCardTable.vue
 * 记得注释
-->
<template>
  <div>
    <el-table ref="storeTable" :data="tableData.detail.saleSpecs" stripe style="width: 100%;">
      <el-table-column label="卡模板" key="1" min-width="120px" prop="name" fixed="left">
        <template slot-scope="scope">
          <span @click="gotoTplDtl(scope.row)" style="font-size: 13px;color:#20A0FF;cursor:pointer;" no-i18n>
            {{scope.row.cardTemplateName}}
          </span>
        </template>
      </el-table-column>
      <el-table-column :label="formatI18n('/卡/卡管理/售卡单','卡面额/次数')" key="2" min-width="150px" prop="faceAmount">
        <template slot-scope="scope">
          <span style="margin:0 4px" v-if="scope.row.cardTemplateType === 'COUNTING_CARD'">
            {{ scope.row.count }}{{formatI18n('/储值/预付卡/预付卡查询/列表页面','次')}}
          </span>
          <i18n k="/储值/预付卡/电子礼品卡活动/编辑页面/卡面额{0}元" v-else>
            <template slot="0">
              <span style="margin:0 4px">
                {{ scope.row.faceAmount }}
              </span>
            </template>
          </i18n>
        </template>
      </el-table-column>
      <el-table-column v-if="tableData.detail.favType == 'discount'" key="3" label="折扣" min-width="100px" prop="discount">
        <template slot-scope="scope">
          <span>{{ scope.row.discount }}{{formatI18n('/储值/预付卡/预付卡充值有礼','折')}}</span>
        </template>
      </el-table-column>
      <el-table-column label="指定售价" key="4" min-width="100px" prop="price">
        <template slot-scope="scope">
          <template>
            <span>{{ Number(scope.row.price.toFixed(2)) }}{{formatI18n('/券/购券管理','元')}}</span>
          </template>
        </template>
      </el-table-column>
      <el-table-column label="购卡赠礼" key="5" min-width="300px" prop="gift" v-if="isShowGift">
        <template slot-scope="scope">
          <div v-if="!scope.row.gift.points && !scope.row.gift.rebateAmount && scope.row.gift.couponItems.length === 0">
            {{formatI18n('/储值/预付卡/电子礼品卡活动/详情页面','暂无')}}
          </div>
          <el-row v-if="scope.row.gift.points" style="margin-top: 0">
            {{ scope.row.gift.points }} <span>积分</span>
          </el-row>
          <el-row v-for="(coupon, index) of scope.row.gift.couponItems" :key="index"
            style="margin-top: 0;overflow: hidden;text-overflow: ellipsis;white-space: nowrap">
            <template v-if="coupon">
              {{ coupon.qty }} <span>张</span>
              <a @click="doCheckDtl(scope.row, coupon)" :title="coupon.coupons.name" no-i18n>{{ coupon.coupons.name }}</a>
            </template>
          </el-row>
        </template>
      </el-table-column>
      <el-table-column label="发售限制" key="6" min-width="230px" prop="total" v-if="isShowLimit">
        <template slot-scope="scope">
          {{saleLimitStr(scope.row)}}
        </template>
      </el-table-column>
    </el-table>
    <SelectStoreActiveDtlDialog :data="tableData" :parent="couponDialog.parent" :child="couponDialog.child" :dialogShow="couponDialog.dialogShow"
      @dialogClose="doDialogClose"></SelectStoreActiveDtlDialog>
  </div>
</template>

<script lang="ts" src="./ActivePresentCardTable.ts">
</script>

<style lang="scss" scoped>
</style>