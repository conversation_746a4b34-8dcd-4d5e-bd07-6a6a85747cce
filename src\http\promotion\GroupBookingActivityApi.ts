import ApiClient from 'http/ApiClient'
import Response from 'model/default/Response'
import GroupBookingRecordFilter from "model/promotion/groupBookingActivity/GroupBookingRecordFilter";
import GroupBookingWonRecordFilter from "model/promotion/groupBookingActivity/GroupBookingWonRecordFilter";
import GroupBookingActivity from "model/promotion/groupBookingActivity/GroupBookingActivity";
import GroupBookingJoinedRecordFilter from "model/promotion/groupBookingActivity/GroupBookingJoinedRecordFilter";
import GroupBookingJoinedRecord from "model/promotion/groupBookingActivity/GroupBookingJoinedRecord";
import GroupBookingRecord from "model/promotion/groupBookingActivity/GroupBookingRecord";
import GroupBookingWonRecord from "model/promotion/groupBookingActivity/GroupBookingWonRecord";

export default class GroupBookingActivityApi {
  /**
   * 批量导出拼团记录
   * 批量导出拼团记录。
   * 
   */
  static exportGroupBookingRecord(body: GroupBookingRecordFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/group-booking-activity/exportGroupBookingRecord`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量导出拼团记录
   * 批量导出拼团记录。
   * 
   */
  static exportGroupBookingWonRecord(body: GroupBookingWonRecordFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/group-booking-activity/exportGroupBookingWonRecord`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 拼团活动详情
   * 拼团活动详情。
   * 
   */
  static getGroupBookingActivity(id: string): Promise<Response<GroupBookingActivity>> {
    return ApiClient.server().get(`/v1/group-booking-activity/getGroupBookingActivity/${id}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询参团记录
   * 查询参团记录
   * 
   */
  static queryGroupBookingJoinedRecord(body: GroupBookingJoinedRecordFilter): Promise<Response<GroupBookingJoinedRecord[]>> {
    return ApiClient.server().post(`/v1/group-booking-activity/queryGroupBookingJoinedRecord`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询拼团记录
   * 查询拼团记录
   * 
   */
  static queryGroupBookingRecord(body: GroupBookingRecordFilter): Promise<Response<GroupBookingRecord[]>> {
    return ApiClient.server().post(`/v1/group-booking-activity/queryGroupBookingRecord`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询中奖记录
   * 查询中奖记录
   * 
   */
  static queryGroupBookingWonRecord(body: GroupBookingWonRecordFilter): Promise<Response<GroupBookingWonRecord[]>> {
    return ApiClient.server().post(`/v1/group-booking-activity/queryGroupBookingWonRecord`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建或修改拼团活动
   * 新建或修改拼团活动
   * 
   */
  static saveGroupBookingActivity(body: GroupBookingActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/group-booking-activity/saveGroupBookingActivity`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
