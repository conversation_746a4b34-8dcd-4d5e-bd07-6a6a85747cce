import {Component, Provide, Vue} from 'vue-property-decorator'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import GoodsScopeEx from 'cmp/goodsscope/GoodsScopeEx.vue'
import BDepositUseLimitActivity from "model/deposit/store/BDepositUseLimitActivity";
import DepositUseLimitActivityApi from "http/prepay/applicableGoods/DepositUseLimitActivityApi";
import I18nPage from 'common/I18nDecorator';
import BrowserMgr from "mgr/BrowserMgr";

class StoreValueApplicableGoodsForm {
  limit: boolean = false
  data: BDepositUseLimitActivity = new BDepositUseLimitActivity()
}

@Component({
  name: 'StoreValueApplicableGoodsAdd',
  components: {
    BreadCrume,
    GoodsScopeEx
  }
})
@I18nPage({
  prefix: ['/储值/会员储值/储值适用商品设置'],
})
export default class StoreValueApplicableGoodsAdd extends Vue {
  goodsMatchRuleMode: string = "barcode"
  @Provide('showAll')
	showAll: Boolean = true

  $refs: any
  panelArray: any = []
  form: StoreValueApplicableGoodsForm = new StoreValueApplicableGoodsForm()
  rules: any = {}
  buyRules: any
  loading = false
  created() {
    let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
		if (sysConfig && this.goodsMatchRuleMode) {
			this.goodsMatchRuleMode = sysConfig.goodsMatchRuleMode;
		}
    this.panelArray = [
      {
        name: this.formatI18n('/储值/会员储值', '储值适用商品设置'),
        url: 'grow-value-rule-dtl'
      },
      {
        name: this.formatI18n('/储值/会员储值/储值适用商品设置', '修改储值适用商品'),
        url: ''
      }
    ]
  }

  mounted() {
    this.getDetail()
  }

  validateAll() {
    let all: any[] = []
    all.push(this.$refs.ruleForm.validate())
    return Promise.all(all)
  }

  doSave() {
    this.validateAll().then((res: any[]) => {
      let errCount = res.filter((e) => !e).length
      if (errCount === 0) {
        this.save()
      }
    })
  }

  save() {
    let body = this.parseReqBody()
    DepositUseLimitActivityApi.saveOrModify(body).then((res: any) => {
      if (res.code === 2000) {
        this.$message.success(this.formatI18n('/公用/js提示信息', '保存成功'))
        this.$router.push({name: 'store-value-applicable-goods-dtl', query: {id: ''}})
      } else {
        throw new Error(res.msg)
      }
    }).catch((reason: any) => {
      this.$message.error(reason.message);
    });
  }

  doCancel() {
    this.$router.back()
  }

  private getDetail() {
    this.loading = true
    DepositUseLimitActivityApi.byType().then((res: any) => {
      if (res.code === 2000) {
        this.form.data = res.data ? res.data : new BDepositUseLimitActivity()
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    }).finally(() => {
      this.loading = false
    })
  }

  private parseReqBody() {
    let body: BDepositUseLimitActivity = JSON.parse(JSON.stringify(this.form.data))
    return body
  }
}