<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:03
 * @LastEditTime: 2023-10-12 11:01:58
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\prepaycard\cmp\prepaystate\PrepayState.vue
 * 记得注释
-->
<template>
  <div class="prepay-state">
    <div class="prepay-state-state" :style="{backgroundColor: parser.parseStateColor(state)}"></div>&nbsp;
    {{parser.parseState(state)}}
  </div>
</template>

<script lang="ts" src="./PrepayState.ts">
</script>

<style lang="scss" scoped>
  .prepay-state {
    display: flex;
    /*justify-content:center;*/
    align-items: center;

    .prepay-state-state {
      height: 5px;
      width: 5px;
      border-radius: 10px;
      color: red;
      margin-right: 4px;
    }
  }
</style>
