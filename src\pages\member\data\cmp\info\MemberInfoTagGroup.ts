import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import MemberDetail from "model/member_standard/MemberDetail";
import I18nPage from "common/I18nDecorator";
import MemberCardTitle from "pages/member/data/cmp/MemberCardTitle";
import MemberTag from "pages/member/data/cmp/info/MemberTag";
import MemberApi from "http/member_standard/MemberApi";
import EmptyData from "pages/member/data/cmp/EmptyData";
import UserGroupV2 from "model/precisionmarketing/userGroup/UserGroupV2";
import BUserGroupRemoveRequest from "model/member/BUserGroupRemoveRequest";
import TagOption from "model/tag/TagOption";
import ConstantMgr from "mgr/ConstantMgr";
import MemberInfoTagEditor from "pages/member/data/cmp/info/MemberInfoTagEditor";

class Tag {
  data: any;
  value: string;
}

@Component({
  name: "MemberInfoTagGroup",
  components: { MemberCardTitle, MemberTag, EmptyData, MemberInfoTagEditor },
})
@I18nPage({
  prefix: [
    "/会员/会员资料",
  ],
  auto: true,
})
export default class MemberInfoTagGroup extends Vue {
  @Prop()
  dtl: MemberDetail;

  @Watch("dtl", { immediate: true })
  dtlChanged() {
    if (!(this.dtl && this.dtl.memberId)) return;
    this.getTags();
    this.getGroups();
  }

  tags: Tag[] = [];
  originTags: TagOption[] = [];

  getTags() {
    if (this.hasOptionPermission("/会员/标签客群/标签", "标签查看")) {
      MemberApi.getMemberTag(this.dtl.memberId!).then((res) => {
        if (res.data && res.data && res.data.length > 0) {
          this.originTags = res.data;
          this.tags = [];
          res.data.forEach((item: any) => {
            let obj: any = {};
            if (item && item.tagValues && item.tagValues.length > 0) {
              item.tagValues.forEach((subItem: any) => {
                obj = {
                  value: subItem,
                  data: item,
                };
                this.tags.push(obj);
              });
            }
          });
          this.checkHasMoreTags();
        }
      });
    }
  }

  groups: Tag[] = [];

  getGroups() {
    MemberApi.getUserGroup(this.dtl.memberId!).then((res) => {
      if (res.data && res.data && res.data.length > 0) {
        this.groups = res.data.map(e => {
          return { data: e, value: e.name! };
        });
        this.checkHasMoreGroups();
      }
    });
  }

  hasMoreTags: boolean = false;

  checkHasMoreTags() {
    this.$nextTick(() => {
      const el = this.$refs.tags;
      // @ts-ignore
      this.hasMoreTags = el.offsetHeight < el.scrollHeight;
    });
  }

  hasMoreGroups: boolean = false;

  checkHasMoreGroups() {
    this.$nextTick(() => {
      const el = this.$refs.groups;
      // @ts-ignore
      this.hasMoreGroups = el.offsetHeight < el.scrollHeight;
    });
  }

  tagDialogVisible: boolean = false;
  dialogTagType: number = 1;

  showMoreTag(type: number) {
    this.dialogTagType = type;
    this.tagDialogVisible = true;
  }

  get dialogTags() {
    return this.dialogTagType == 1 ? this.tags : this.groups;
  }

  get tagDialogTitle() {
    return this.dialogTagType == 1 ? this.i18n("拥有标签") : this.i18n("所属客群");
  }

  onRemoveDialogTag(data: Tag) {
    if (this.dialogTagType == 1)
      this.onRemoveTag(data);
    else
      this.onRemoveGroup(data.data);
  }

  onRemoveTag(data: Tag) {
    const tag = data.data as TagOption;
    console.log(tag);
    tag.tagValues.splice(tag.tagValues.indexOf(data.value), 1);
    console.log(tag.tagValues);
    if (tag.tagValues.length == 0) {
      const index = this.originTags.findIndex(e => e.tagId == tag.tagId);
      this.originTags.splice(index, 1);
    }
    const loading = this.$loading(ConstantMgr.loadingOption);
    MemberApi.saveMemberTag({ memberId: this.dtl.memberId!, tags: this.originTags }).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(this.i18n("删除标签成功"));
        this.getTags();
      } else {
        this.$message.error(resp.msg);
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message);
      }
    }).finally(() => {
      loading.close();
    });
  }

  onRemoveGroup(group: UserGroupV2) {
    const req = new BUserGroupRemoveRequest();
    req.memberId = this.dtl.memberId;
    req.userGroupId = [group.uuid!];
    const loading = this.$loading(ConstantMgr.loadingOption);
    MemberApi.removeUserGroup(req).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(this.i18n("删除客群成功"));
        this.getGroups();
      } else {
        this.$message.error(resp.msg);
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message);
      }
    }).finally(() => {
      loading.close();
    });
    ;
  }

  editDialogVisible: boolean = false;

  onEditTag() {
    this.editDialogVisible = true;
  }
}
