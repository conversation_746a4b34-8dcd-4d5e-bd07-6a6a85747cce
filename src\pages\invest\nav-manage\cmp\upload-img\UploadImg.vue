<!--
 * @Author: 黎钰龙
 * @Date: 2024-04-08 17:00:46
 * @LastEditTime: 2024-04-27 10:25:49
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\upload-img\UploadImg.vue
 * 记得注释
-->
<template>
  <div class="uploader-imgbox">
    <div class="imgbox-wrap" :class="{'circular-box': isCircular}" v-if="curImgUrl">
      <img class="imgbox-img" :src="curImgUrl" alt="">
      <div class="imgbox-delete" @click="deleteImg">删除</div>
    </div>
    <el-upload accept=".jpg, .png, .jpeg, .gif" v-if="!curImgUrl" :class="{'circular-style': isCircular}" :headers="uploadHeaders" :action="uploadUrl" :with-credentials="true"
      :show-file-list="false" :on-success="onImageUploadSuccess" :before-upload="beforeAvatarUpload">
      <div class="uploader-box">
        <i class="el-icon-plus avatar-uploader-icon"></i>
      </div>
    </el-upload>
  </div>
</template>

<script lang="ts" src="./UploadImg.ts">
</script>

<style lang="scss">
.uploader-imgbox {
  display: flex;
  flex-wrap: wrap;
  background: #fff;


  .imgbox-wrap {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: 4px;
    overflow: hidden;

    .imgbox-img {
      width: 80px;
      height: auto;
    }

    .imgbox-delete {
      display: none;
      position: absolute;
      width: 80px;
      height: 80px;
      top: 0;
      left: 0;
      background: rgba(0, 0, 0, 0.5);
      text-align: center;
      line-height: 80px;
      color: white;
      cursor: pointer;
      user-select: none;
    }

    &:hover {
      .imgbox-delete {
        display: block;
      }
    }
  }
  .uploader-box {
    position: relative;
    border: 1px solid #d7dfeb;
    border-radius: 4px;

    &:hover {
      border: 1px dashed #409eff;
      .avatar-uploader-icon {
        color: #409eff;
      }
    }
    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 80px;
      height: 80px;
      line-height: 80px;
      text-align: center;
    }
  }
  .circular-style {
    .uploader-box {
      border-radius: 50%;
      overflow: hidden;
    }
  }
}
.circular-box {
  .imgbox-img {
    border-radius: 50%;
    overflow: hidden;
  }
  .imgbox-delete {
    border-radius: 50%;
    overflow: hidden;
  }
}
</style>