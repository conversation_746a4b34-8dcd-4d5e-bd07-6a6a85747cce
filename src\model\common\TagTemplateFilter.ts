import Channel from 'model/common/Channel'
import { TagTypeEnum } from 'model/common/TagTypeEnum'

// 精准营销-会员标签模板查询过滤器
export default class TagTemplateFilter {
  // 标签类型等于
  tagTypeEquals: Nullable<TagTypeEnum> = null
  // 标签名称类似于
  nameLikes: Nullable<string> = null
  // 打标方式等于：Manual-手动；Auto-自动
  tagModelEquals: Nullable<string> = null
  // 状态等于：Enable-启用；Disable-禁用
  stateEquals: Nullable<string> = null
  // 更新频次：ByDay-按日；ByMonth-按月
  scheduleTypeEquals: Nullable<string> = null
  // 最后执行状等于：Processing-进行中；Success-计算成功；Fail-计算失败，unStart-未开始
  lastExecuteStateEquals: Nullable<string> = null
  // 分类uuid等于
  categoryIdEquals: Nullable<string> = null
  // 创建人等于
  creatorEquals: Nullable<string> = null
  // 渠道
  channel: Nullable<Channel> = null
  // 页数>=0
  page: number = 0
  // 页面大小>0
  pageSize: number = 0
}