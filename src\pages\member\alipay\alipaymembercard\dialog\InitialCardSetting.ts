import { Component, Prop, Vue } from 'vue-property-decorator'
import CardTpl from "pages/member/entities/CardTpl";

@Component({
  name: 'InitialCardSetting',
  components: {}
})
export default class InitialCardSetting extends Vue {
  @Prop()
  dialogShow: boolean
  requiredInfo: CardTpl[] = []
  optionalInfo: CardTpl[] = []
  requiredInfoCopy: string[] = []
  optionalInfoCopy: string[] = []
  selected: number[] = []
  cardData: CardTpl[] = []
  recordCardData: any[] = []
  status: number = 1
  created() {
    const versionType = sessionStorage.getItem('versionType')
    console.log("versionType",versionType);
    
    if (versionType == 'V1' || versionType === null) {
      this.status = 1
    } else {
      this.status = 2
    }
    this.initData()
  }
  initData() {
    console.log(this.status,"this.status");
    
    if (this.status === 1) {
      this.cardData = [{
        cardChecked: true,
        cardName: this.formatI18n('/会员/会员资料/手机号'),
        cardCode: 'OPEN_FORM_FIELD_MOBILE',
        cardSelected: 1
      }, {
        cardChecked: true,
        cardName: this.formatI18n('/会员/会员资料/性别'),
        cardCode: 'OPEN_FORM_FIELD_GENDER',
        cardSelected: 2
      }, {
        cardChecked: true,
        cardName: this.formatI18n('/会员/会员资料/姓名'),
        cardCode: 'OPEN_FORM_FIELD_NAME',
        cardSelected: 2
      },{
        cardChecked: true,
        cardName: this.formatI18n('/会员/会员资料/昵称'),
        cardCode: 'OPEN_FORM_FIELD_NICKNAME',
        cardSelected: 2
      },  {
        cardChecked: true,
        cardName: this.formatI18n('/会员/会员资料/生日'),
        cardCode: 'OPEN_FORM_FIELD_BIRTHDAY_WITH_YEAR',
        cardSelected: 2
      }, {
        cardChecked: false,
        cardName: this.formatI18n('/会员/会员资料/地址'),
        cardCode: 'OPEN_FORM_FIELD_ADDRESS',
        cardSelected: 2
      }, {
        cardChecked: false,
        cardName: '城市',
        cardCode: 'OPEN_FORM_FIELD_CITY',
        cardSelected: 2
      }, {
        cardChecked: false,
        cardName: this.formatI18n('/会员/会员资料/邮箱'),
        cardCode: 'OPEN_FORM_FIELD_EMAIL',
        cardSelected: 2
      }, {
        cardChecked: false,
        cardName: '是否学生认证',
        cardCode: 'OPEN_FORM_FIELD_IS_STUDENT',
        cardSelected: 2
      }]
    } else {
      this.cardData = [{
        cardChecked: true,
        cardName: this.formatI18n('/会员/会员资料/性别'),
        cardCode: 'OPEN_FORM_FIELD_GENDER',
        cardSelected: 2
      }, {
        cardChecked: true,
        cardName: this.formatI18n('/会员/会员资料/姓名'),
        cardCode: 'OPEN_FORM_FIELD_NAME',
        cardSelected: 2
      }, {
        cardChecked: true,
        cardName: this.formatI18n('/会员/会员资料/生日'),
        cardCode: 'OPEN_FORM_FIELD_BIRTHDAY_WITH_YEAR',
        cardSelected: 2
      }, {
        cardChecked: false,
        cardName: this.formatI18n('/会员/会员资料/地址'),
        cardCode: 'OPEN_FORM_FIELD_ADDRESS',
        cardSelected: 2
      }, {
        cardChecked: false,
        cardName: '城市',
        cardCode: 'OPEN_FORM_FIELD_CITY',
        cardSelected: 2
      }, {
        cardChecked: false,
        cardName: this.formatI18n('/会员/会员资料/邮箱'),
        cardCode: 'OPEN_FORM_FIELD_EMAIL',
        cardSelected: 2
      }]
    }

  }
  cardSelectedChanged(index: number) {
    if (this.cardData[index].cardChecked) {
      if (index <= 0) {
        this.cardData[index].cardSelected = 1
        // this.requiredInfo.push(this.cardData[index])
      } else {
        this.cardData[index].cardSelected = 2
        // this.optionalInfo.push(this.cardData[index])
      }
    } else {
      if (index <= 1) {
        this.requiredInfo = this.requiredInfo.filter((x) => x.cardName !== this.cardData[index].cardName)
      } else {
        this.optionalInfo = this.optionalInfo.filter((x) => x.cardName !== this.cardData[index].cardName)
      }
      this.cardData[index].cardSelected = 0
    }
  }
  doOptionChange(index: number) {
    // console.log(this.cardData)
  }
  doBeforeClose(done: any) {
    this.doTransInitSubmitParams()
    this.initData()
    this.$emit('submit', this.requiredInfo, this.optionalInfo)
    this.$emit('close')
    done()
  }
  doConfirmClose() {
    this.doTransSubmitParams()
    this.$emit('submit', this.requiredInfo, this.optionalInfo)
    this.$emit('close')
  }
  doCancel() {
    this.doTransInitSubmitParams()
    this.initData()
    this.$emit('submit', this.requiredInfo, this.optionalInfo)
    this.$emit('close')
  }
  doTransInitSubmitParams() {
    this.requiredInfo = []
    this.optionalInfo = []
    if (this.recordCardData && this.recordCardData.length > 0) {
      this.recordCardData.forEach((item: any, index: number) => {
        if (item.cardChecked) {
          if (item.cardSelected === 1) {
            this.requiredInfo.push(item)
          }
          if (item.cardSelected === 2) {
            this.optionalInfo.push(item)
          }
        }
      })
    }
  }
  doTransSubmitParams() {
    this.requiredInfo = []
    this.optionalInfo = []
    if (this.cardData && this.cardData.length > 0) {
      this.cardData.forEach((item: any, index: number) => {
        if (item.cardChecked) {
          if (item.cardSelected === 1) {
            this.requiredInfo.push(item)
          }
          if (item.cardSelected === 2) {
            this.optionalInfo.push(item)
          }
        }
      })
    }
  }
  doSetArray(requiredInfo: any, optionalInfo: any) {
    this.requiredInfo = requiredInfo
    this.optionalInfo = optionalInfo
    console.log(requiredInfo, optionalInfo);
    
    this.requiredInfoCopy = JSON.parse(JSON.stringify(requiredInfo))
    this.optionalInfoCopy = JSON.parse(JSON.stringify(optionalInfo))
    if (this.$route.query.from === 'edit') {
      this.cardData.forEach((item: any) => {
        item.cardChecked = false
      })
    }
    if (this.cardData && this.cardData.length > 0) {
      this.cardData.forEach((item: any) => {
        requiredInfo.forEach((subItem: any) => {
          if (item.cardName === subItem.cardName) {
            item.cardChecked = true
            item.cardSelected = 1
          }
        })
        optionalInfo.forEach((subItem: any) => {
          if (item.cardName === subItem.cardName) {
            item.cardChecked = true
            item.cardSelected = 2
          }
        })
      })
    }
    // this.$forceUpdate()
    this.recordCardData = JSON.parse(JSON.stringify(this.cardData))
  }
}