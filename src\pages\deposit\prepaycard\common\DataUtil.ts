import TemplateValidity from 'model/card/template/TemplateValidity';
import DateUtil from 'util/DateUtil';
import IdName from 'model/common/IdName';
import I18nTool from "common/I18nTool";

export default class DataUtil {
    getValidNum(validityInfo: TemplateValidity) {
        if (validityInfo.validityDays !== null) {
            return `${validityInfo.validityDays}` + I18nTool.match('/储值/预付卡/卡模板/编辑页面/天')
        }
        if (validityInfo.validityYears !== null) {
            return `${validityInfo.validityYears}` + I18nTool.match('/储值/预付卡/卡模板/编辑页面/年')
        }
    }

    format(date: Date, fmt: string) {
        return DateUtil.format(date, fmt)
    }

    showIdName(idname: IdName) {
        if (!idname) {
            return '-'
        }
        let id = idname.id ? idname.id : '-'
        let name = idname.name ? idname.name : '-'
        return `[${id}] ${name}`
    }

    // 级手机号>会员号>实体卡号
    showMemberId(row: any) {
        if (row.mobile) {
            return row.mobile
        }
        if (row.hdCardMbrId) {
            return row.hdCardMbrId
        }
        if (row.hdCardCardNum) {
            return row.hdCardCardNum
        }
        return '-'
    }

    // 级手机号>会员号>实体卡号
    showMemberId1(mobile: any, hdCardMbrId: any, hdCardCardNum: any, memberId: any = null,cardTypeEquals: Nullable<string> = null,hasBuyerMemberId: Nullable<boolean> = null) {
        if (mobile) {
            return mobile
        }
        if (hdCardMbrId) {
            return hdCardMbrId
        }
        if (hdCardCardNum) {
            return hdCardCardNum
        }
        if (memberId && cardTypeEquals !== 'OnlineGiftCard' && (hasBuyerMemberId != null && !hasBuyerMemberId)) {
            return memberId
        }
        return '-'
    }

    showAmount(amount: number) {
        if (amount === null || amount === undefined) {
            return '-'
        }
        return amount.toFixed(2)
    }

    showTotalAmount(amount: number) {
        if (amount === null || amount === undefined) {
            return '0.00'
        }
        return amount.toFixed(2)
    }
}
