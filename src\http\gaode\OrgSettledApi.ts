import ApiClient from 'http/ApiClient'
import BGaoDeOrgSettledQualificationsConfig from 'model/gaode/BGaoDeOrgSettledQualificationsConfig'
import BOrgSettledQualifications from 'model/gaode/BOrgSettledQualifications'
import BOrgSettledQualificationsQuery from 'model/gaode/BOrgSettledQualificationsQuery'
import BSyncGaoDeBatchRequest from 'model/gaode/BSyncGaoDeBatchRequest'
import Response from 'model/default/Response'
import BSyncPlatformResult from "../../model/gaode/BSyncPlatformResult";

export default class OrgSettledApi {
  /**
   * 批量同步高德
   * 批量同步高德。
   * 
   */
  static batchSyncGaoDe(body: BSyncGaoDeBatchRequest): Promise<Response<BSyncPlatformResult>> {
    return ApiClient.server().post(`/v1/org-settled/batchSyncGaoDe`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 默认资质保存修改
   * 默认资质保存修改。
   * 
   */
  static gaoDeQualifications(body: BGaoDeOrgSettledQualificationsConfig): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/org-settled/gaoDeQualifications/saveOrModify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 默认资质查询
   * 默认资质查询。
   * 
   */
  static getGaoDeQualifications(): Promise<Response<BGaoDeOrgSettledQualificationsConfig>> {
    return ApiClient.server().post(`/v1/org-settled/gaoDeQualifications/get`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 门店资质查询
   * 门店资质查询。
   * 
   */
  static getOrgQualifications(body: BOrgSettledQualificationsQuery): Promise<Response<BOrgSettledQualifications>> {
    return ApiClient.server().post(`/v1/org-settled/orgQualifications/get`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 门店资质保存修改
   * 门店资质保存修改。
   * 
   */
  static orgQualifications(body: BOrgSettledQualifications): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/org-settled/orgQualifications/saveOrModify`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
