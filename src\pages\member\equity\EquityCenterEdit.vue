<!--
 * @Author: 黎钰龙
 * @Date: 2024-04-28 10:09:59
 * @LastEditTime: 2024-04-30 16:24:31
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\member\equity\EquityCenterEdit.vue
 * 记得注释
-->
<template>
  <div class="equity-center-edit-container">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button type="primary" v-if="hasOptionPermission('/会员/会员体系/权益中心','权益维护')" size="large" @click="doSave">
          {{i18n('保存')}}
        </el-button>
        <el-button size="large" @click="doCancel">
          {{i18n('取消')}}
        </el-button>
      </template>
    </BreadCrume>
    <el-form :model="ruleForm" :rules="rules" label-width="120px" ref="form">
      <!-- 基础信息 -->
      <div class="setting-container" style="min-height: 80vh;">
        <div class="section-title">{{i18n('基础信息')}}</div>
        <el-form-item :label="i18n('权益图标')" prop="iconUrl">
          <div class="gray-tips">
            <i18n k="/公用/券模板/图片比例支持{0}（建议尺寸{1}像素），支持jpg/jpeg，大小不超过{2}">
              <template slot="0">1:1</template>
              <template slot="1">300*300</template>
              <template slot="2">300KB</template>
            </i18n>
          </div>
          <UploadImg v-model="ruleForm.iconUrl" :isCircular="true" @change="uploadImgChange('iconUrl')" :maximum="300"></UploadImg>
        </el-form-item>
        <el-form-item :label="i18n('权益名称')" prop="equityName">
          <el-input maxlength="10" :placeholder="i18n('用于小程序端等级权益展示，最多{0}个字').replace(/\{0\}/g, 10)" style="width: 350px"
            v-model.trim="ruleForm.equityName">
          </el-input>
        </el-form-item>
        <el-form-item :label="i18n('/会员/等级/等级管理/点击付费等级tab页/未初始化状态下/点击立即开始付费等级初始化/表格/权益说明')" prop="remark">
          <el-input maxlength="500" :rows="5" type="textarea" :placeholder="i18n('用于小程序端等级权益展示，最多{0}个字').replace(/\{0\}/g, 500)" style="width: 350px"
            v-model.trim="ruleForm.remark">
          </el-input>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script lang="ts" src="./EquityCenterEdit.ts">
</script>

<style lang="scss" scoped>
.equity-center-edit-container {
  width: 100%;
}
</style>