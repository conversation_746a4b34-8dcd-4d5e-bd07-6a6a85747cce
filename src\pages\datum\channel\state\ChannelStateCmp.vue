<template>
  <div class="payment-state">
    <div class="payment-state-state" :style="{backgroundColor: parseStateColor(state)}"></div>&nbsp;
    {{parseState(state)}}
  </div>
</template>

<script lang="ts" src="./ChannelStateCmp.ts">
</script>

<style lang="scss" scoped>
  .payment-state {
    display: flex;
    /*justify-content:center;*/
    align-items: center;

    .payment-state-state {
      height: 5px;
      width: 5px;
      border-radius: 10px;
      color: red;
    }
  }
</style>
