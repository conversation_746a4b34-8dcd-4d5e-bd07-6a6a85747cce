<template>
    <div class="member-growth-analysis-container">
      <BreadCrume :panelArray="panelArray">
        <template slot="operate">
          <el-button v-if="hasOptionPermission('/数据/分析/会员交易分析','数据导出')" @click="doExport" size="large">
            {{formatI18n('/储值/预付卡/充值卡制售单/列表页面','导出')}}
          </el-button>
        </template>
      </BreadCrume>
      <div class="search-block">
        <MyQueryCmp @reset="doReset" @search="onSearch" :showExpand="false">
          <el-row>
            <el-col :span="16">
              <AnalysisDateSelector ref="analysisDateSelector" :label="i18n('日期粒度')" @change="doDateChange">
              </AnalysisDateSelector>
            </el-col>
            <el-col :span="8">
              <FormItem :label="i18n('/资料/渠道/渠道')">
                <ChannelSelect v-model="filter.channel" :isShowAll="true" width="100%"></ChannelSelect>
              </FormItem>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <FormItem :label="i18n('门店')">
                <SelectStores v-model="filter.store" :isOnlyId="true" :hideAll="false" width="100%" :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
                </SelectStores>
              </FormItem>
            </el-col>
            <el-col :span="8">
              <FormItem :label="i18n('/资料/门店/区域主管')">
                <SelectEmployees v-model="filter.areaLeaderEquals" width="100%"></SelectEmployees>
              </FormItem>
            </el-col>
            <el-col :span="8">
              <FormItem :label="i18n('/资料/门店/营运经理')">
                <SelectEmployees v-model="filter.operationEquals" width="100%"></SelectEmployees>
              </FormItem>
            </el-col>
          </el-row>
        </MyQueryCmp>
      </div>
  
      <!-- 数据概览 -->
      <div class="overview-block" v-if="detail.summary">
        <div class="overview-title">
          {{i18n('/储值/预付卡/电子礼品卡活动/效果评估/数据概览')}}
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              <div style="color: #36445A;line-height: 20px;font-size: 13px;">
                <div style="font-size: 15px;margin-bottom:8px">{{i18n('/储值/预付卡/电子礼品卡活动/效果评估/指标说明')}}</div>
                <div>
                  {{i18n('交易金额：在查询条件下，消费金额-退货金额')}}；<br />
                  {{i18n('交易数量：在查询条件下，消费数量-退货数量')}}；<br />
                  {{i18n('客单数：在查询条件下，消费单数-退货单数')}}；<br />
                  {{i18n('客单价：在查询条件下，交易金额/客单数')}}；<br />
                  {{i18n('客单件：在查询条件下，交易数量/客单数')}}；<br />
                  {{i18n('件单价：在查询条件下，交易金额/交易数量')}}；<br />
                  {{i18n('会员交易金额：在查询条件下，会员消费金额-会员退货金额')}}；<br />
                  {{i18n('会员交易金额占比：在查询条件下，会员交易金额/交易金额')}}；<br />
                  {{i18n('会员交易数量：在查询条件下，会员消费数量-会员退货数量')}}；<br />
                  {{i18n('会员客单数：在查询条件下，会员消费单数-会员消费退货单数')}}；<br />
                  {{i18n('会员客单数占比：在查询条件下，会员交易笔数/交易笔数')}}；<br />
                  {{i18n('会员客单价：在查询条件下，会员交易金额/会员客单数')}}；<br />
                  {{i18n('会员客单件：在查询条件下，会员交易数量/会员客单数')}}；<br />
                  {{i18n('会员件单价：在查询条件下，会员交易金额/会员交易数')}}；<br />
                </div>
              </div>
            </div>
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </div>
        <div class="overview-info">
          <div class="info-item" v-for="(item,index) in summaryViewArr" :key="index">
            <div class="item-title">{{item.label}}</div>
            <div class="item-number">{{item.value}}{{ ['会员交易金额占比','会员客单数占比'].includes(item.label) ? '%' : '' }}</div>
            <div class="item-trend" v-if="!(['会员交易金额占比','会员客单数占比'].includes(item.label))">
              <span style="margin-right:4px">{{i18n('环比')}}</span>
              <span style="color: #00D29B" v-if="item.ringValue > 0">
                <i class="el-icon-sort-up"></i>
                +{{(item.ringValue * 100).toFixed(2)}}%
              </span>
              <span style="color: #FA5050" v-else>
                <i class="el-icon-sort-down"></i>
                {{(item.ringValue * 100).toFixed(2)}}%
              </span>
            </div>
            <div class="item-trend"></div>
          </div>
        </div>
      </div>
  
      <div class="chart-block">
        <MemberLineChart :legendNames="legendNames" :xAxisArray="xAxisArray" :dateType="detail.dateUnit" :valueArray="valueArray" :showPercentName="showPercentName">
        </MemberLineChart>
      </div>
      <DownloadCenterDialog :dialogvisiable="downloadCenterFlag" :showTip="true" @dialogClose="doDownloadDialogClose">
      </DownloadCenterDialog>
    </div>
  </template>
  
  <script lang="ts" src="./memberTransactionsView.ts">
  </script>
  
  <style lang="scss" scoped>
  .member-growth-analysis-container {
    width: 100%;
    padding-bottom: 30px;
    overflow: auto;
    .search-block {
      width: 100%;
      border-radius: 8px;
      background-color: #fff;
      padding: 24px;
    }
    .overview-block {
      width: 100%;
      border-radius: 8px;
      background-color: #fff;
      padding: 24px 0 0 24px;
      margin-top: 16px;
      .overview-title {
        display: flex;
        align-items: center;
        font-weight: 600;
        font-family: PingFangSC, PingFang SC;
        font-size: 16px;
        color: #111111;
        line-height: 24px;
        margin-bottom: 20px;
        .el-icon-warning-outline {
          cursor: pointer;
          margin-left: 4px;
          &:hover {
            color: #2878ff;
          }
        }
      }
      .overview-info {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
      }
      .info-item {
        width: 15%;
        margin-bottom: 24px;
        min-height: 87px;
        .item-title {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #898fa3;
          line-height: 20px;
          text-align: left;
        }
        .item-number {
          font-family: DINAlternate, DINAlternate;
          font-weight: bold;
          font-size: 20px;
          color: #111111;
          line-height: 24px;
          text-align: left;
          margin-top: 2px;
        }
        .item-trend {
          display: flex;
          align-items: center;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #969799;
          line-height: 17px;
          margin: 12px 4px 0 0;
        }
      }
    }
    .chart-block {
      padding: 0px 24px 24px;
      background: #ffffff;
      border-radius: 8px;
      margin-top: 16px;
    }
  }
  </style>