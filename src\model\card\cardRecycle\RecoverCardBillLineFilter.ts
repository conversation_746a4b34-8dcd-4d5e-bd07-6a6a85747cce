/*
 * @Author: 黎钰龙
 * @Date: 2023-11-06 14:47:17
 * @LastEditTime: 2023-11-08 14:13:33
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\card\cardRecycle\RecoverCardBillLineFilter.ts
 * 记得注释
 */

import RecoverCardState from "./RecoverCardState"

export default class RecoverCardBillLineFilter {
  // 卡号
  cardCodeEquals: Nullable<string> = null
  // 单号
  billNumberEquals: Nullable<string> = null
  // 状态INITIAL-未审核AUDITED-已审核
  stateEquals: Nullable<RecoverCardState> = null
  // 状态in
  stateIn: Nullable<RecoverCardState[]> = null
  // 
  page: Nullable<number> = null
  // 
  pageSize: Nullable<number> = null
}