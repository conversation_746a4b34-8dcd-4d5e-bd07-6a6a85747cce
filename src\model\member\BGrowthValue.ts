export default class BGrowthValue {
  //
  marketingCenter: Nullable<string> = null
  //
  occurredOrgId: Nullable<string> = null
  //
  occurredOrgName: Nullable<string> = null
  //
  occurredTime: Nullable<Date> = null
  //
  channelId: Nullable<string> = null
  //
  channelType: Nullable<string> = null
  //
  transIdId: Nullable<string> = null
  //
  transIdNamespace: Nullable<string> = null
  //
  transNo: Nullable<string> = null
  //
  sourceTransIdId: Nullable<string> = null
  //
  sourceTransIdNamespace: Nullable<string> = null
  //
  memberId: Nullable<string> = null
  //
  growthValue: Nullable<number> = null
  //
  event: Nullable<string> = null
  //
  remark: Nullable<string> = null
  //
  monthTimes: Nullable<number> = null
  //变动原因
  reason: Nullable<string> = null
}
