/*
 * @Author: 黎钰龙
 * @Date: 2024-04-29 14:22:54
 * @LastEditTime: 2024-07-01 17:58:22
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\selectEquity\SelectEquity.ts
 * 记得注释
 */
import EquitySelectDialog from 'cmp/selectordialogs/EquitySelectDialog';
import I18nPage from 'common/I18nDecorator';
import BenefitConfig from 'model/member/BenefitConfig';
import { Component, Model, Prop, Provide, Vue } from 'vue-property-decorator';
import draggable from 'vuedraggable';
@Component({
  name: 'SelectEquity',
  components: {
    EquitySelectDialog,
    draggable
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/会员/权益中心'
  ],
  auto: false
})
export default class SelectEquity extends Vue {
  $refs: any
  @Model('change') equityList: any[]
  @Prop({ type: Boolean, default: true }) isShowList: boolean;  //是否展示权益列表
  @Provide('maxLimit') maxLimit = 10
  @Provide('isFilterOnlyName') isFilterOnlyName = true

  get listValue() {
    return this.equityList
  }

  set listValue(val) {
    const res = val?.length ? val : null
    this.$emit('change', res)
  }

  get isEmpty() {
    return !this.listValue || this.listValue.length === 0
  }

  doAdd() {
    this.$refs.equitySelectDialog.open(this.listValue || [], "multiple");
  }

  doSubmit(arr: BenefitConfig[]) {
    console.log('看看选中的权益', arr);
    this.listValue = arr
  }

  // 拖拽事件
  handleDragChange(res: any) {
    console.log('看看拖拽数据', res);
    if (res?.moved) {
      const optionItem = JSON.parse(JSON.stringify(this.listValue.find((item: BenefitConfig) => item.uuid === res.moved.element)))
      const beforeIndex = res.moved.oldIndex
      const targetIndex = res.moved.newIndex
      if (beforeIndex === targetIndex) return
      if (beforeIndex > targetIndex) {
        // 从后往前
        this.listValue.splice(beforeIndex, 1)
        this.listValue.splice(targetIndex, 0, optionItem)
      } else {
        // 从前往后
        this.listValue.splice(targetIndex, 0, optionItem)
        this.listValue.splice(beforeIndex, 1)
      }
    } else {
      this.$message.error(this.i18n('拖拽信息获取失败'))
    }
  }
};