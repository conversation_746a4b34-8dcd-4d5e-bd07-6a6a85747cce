import ApiClient from 'http/ApiClient'
import Grade from 'model/grade/Grade'
import GradeInitData from 'model/grade/grade/GradeInitData'
import Response from 'model/common/Response'

export default class GradeApi {
  /**
   * 检查等级基础资料代码是否存在
   *
   */
  static checkGradeExist(type: string, code: string): Promise<Response<boolean>> {
    return ApiClient.server().get(`/v1/grade/common/${type}/checkGradeExist`, {
      params: {
        code: code
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询等级资料
   *
   */
  static listGrade(type: string): Promise<Response<Grade[]>> {
    return ApiClient.server().get(`/v1/grade/common/listGrade`, {
      params: {
        type: type
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改等级基础资料type类型：FREE：免费等级；PAID：付费等级；SPECIAL：特殊等级
   *
   */
  static modifyGrade(body: Grade[], type: string): Promise<Response<void>> {
      return ApiClient.server().post(`/v1/grade/common/${type}/modifyGrade`, body, {}).then((res) => {
          return res.data
      })
  }

  /**
   * 检查等级基础资料代码是否存在
   *
   */
  static stats(): Promise<Response<GradeInitData>> {
      return ApiClient.server().get(`/v1/grade/common/stats`, {}).then((res) => {
          return res.data
      })
  }

}
