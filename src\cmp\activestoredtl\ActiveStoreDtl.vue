<template>
  <div class="active-store-dtl">
    <!-- <span v-if="data.storeRangeType === 'ALL'" class="title">{{formatI18n('/公用/券模板/全部门店')}}</span> -->
    <!-- 单营销中心，部分门店 -->
    <span class="title" v-if="data.storeRangeType !== 'ALL' && (!data.storeRangeLimitType || data.storeRangeLimitType === 'STORE')">
      <div class="active-store-dtl-content">
        <div class="suit header-block" v-if="data.storeRangeType === 'PART'">
          <span>{{ i18n('适用门店') }}</span>
          <span class="total-shop">{{ i18n('/公用/券模板/微盟适用商品/共') + " " + data.stores.length + " " + i18n('/公用/券模板/微盟适用商品/条')}}</span>
        </div>
        <div class="unsuited header-block" v-else>
          <span>{{ i18n('不适用门店') }}</span>
          <span class="total-shop">{{  i18n('/公用/券模板/微盟适用商品/共') + " " + data.stores.length + " " + i18n('/公用/券模板/微盟适用商品/条')}}</span>
        </div>
        <el-row class="store-tables">
          <div style="width:100%">
            <InMemPage :isShowTotal="false" :data="data.stores">
              <template slot="data" slot-scope="{data}">
                <div class="line-container">
                  <span class="line-item" v-for="line of data" :key="line.id">
                    [{{line.id}}] {{line.name}};
                  </span>
                </div>
              </template>
            </InMemPage>
          </div>
        </el-row>
      </div>
      <!-- {{getStoreLength(data.stores.length, data.storeRangeType)}}&nbsp;&nbsp;
      <StoresDtl :data="data.stores" /> -->
    </span>
    <!-- 多营销中心 -->
    <span style="width: 100%" v-if="data.storeRangeType !== 'ALL' && data.storeRangeLimitType === 'MARKETING_CENTER'">
      <div v-for="(marketingCenter,ind) of data.marketingCenters" :key="marketingCenter.marketingCenter.id" class="shop-item">
        <div style="display: flex;line-height:36px">
          <div>{{i18n('组织')}}：{{data.marketingCenters[ind].marketingCenter.name}}</div>
          <div class="store-range-type">{{getStoreUse(data,ind)}}</div>
        </div>
        <div class="active-store-dtl-content" v-if="!isMultipleMarketingCentersAllShop(marketingCenter)">
          <div class="suit header-block"
            v-if="marketingCenter.stores.storeRangeType === 'PART' && marketingCenter.stores.storeRangeLimitType !== 'ZONE'">
            <span>{{ i18n('适用门店') }}</span>
            <span class="total-shop">{{ getTotal(marketingCenter) }}</span>
          </div>
          <div class="unsuited header-block" v-if="marketingCenter.stores.storeRangeType === 'EXCLUDE'">
            <span>{{ i18n('不适用门店') }}</span>
            <span class="total-shop">{{ getTotal(marketingCenter) }}</span>
          </div>
          <el-row class="store-tables">
            <div v-if="marketingCenter.stores.storeRangeType !== 'ALL'" style="width:100%">
              <InMemPage v-if="marketingCenter.stores.storeRangeLimitType !== 'ZONE'" :isShowTotal="false" :data="marketingCenter.stores.stores">
                <template slot="data" slot-scope="{data}">
                  <div class="line-container">
                    <span class="line-item" v-for="line of data" :key="line.id">
                      [{{line.id}}] {{line.name}};
                    </span>
                  </div>
                </template>
              </InMemPage>
              <!-- 区域 -->
              <el-row
                v-if="marketingCenter.stores.storeRangeLimitType === 'ZONE' && marketingCenter.stores.zones && marketingCenter.stores.zones.length > 0">
                <div v-for="(zone, indexZone) in marketingCenter.stores.zones" :key="indexZone">
                  <div :class="zone.stores.storeRangeType !== 'EXCLUDE' ? 'suit header-block' : 'unsuited header-block'"
                    style="justify-content: flex-start;">
                    <span>{{formatI18n('/公用/菜单/区域')}}：</span>
                    <span>[{{zone.zones.id}}] {{zone.zones.name}}</span>
                    <span style="margin-left:6px">
                      <span v-if="zone.stores.storeRangeType !== 'EXCLUDE'">{{i18n('适用门店')}}</span>
                      <span v-else>{{i18n('不适用门店')}}</span>
                    </span>
                    <span class="total-shop" style="margin-left: auto">{{ getTotal(zone) }}</span>
                  </div>
                  <div class="zone-text">
                    <InMemPage v-if="zone.stores.storeRangeType !== 'ALL'" :isShowTotal="false" :data="zone.stores.stores">
                      <template slot="data" slot-scope="{data}">
                        <div class="line-container">
                          <div class="line-item" v-for="(zoneStore, indexZoneStore) in data" :key="indexZoneStore">
                            <div>[{{zoneStore.id}}] {{zoneStore.name}};</div>
                          </div>
                        </div>
                      </template>
                    </InMemPage>
                    <div v-else>
                      {{formatI18n('/公用/券模板/全部门店')}}
                    </div>
                  </div>
                </div>
              </el-row>
            </div>
          </el-row>
        </div>
      </div>
    </span>
    <!-- 单营销中心，全部门店 -->
    <span v-if="data.storeRangeType === 'ALL' && data.storeRangeLimitType === 'STORE' && data.stores" style="line-height:36px">
      {{i18n('全部门店适用')}}
    </span>
    <!-- 单营销中心，区域 -->
    <el-row v-if="data.storeRangeLimitType === 'ZONE' && data.zones">
      <div v-for="(zone, indexZone) in data.zones" :key="indexZone">
        <div :class="zone.stores.storeRangeType !== 'EXCLUDE' ? 'suit header-block' : 'unsuited header-block'" style="justify-content: flex-start;">
          <span>{{formatI18n('/公用/菜单/区域')}}：</span>
          <span>[{{zone.zones.id}}] {{zone.zones.name}}</span>
          <span style="margin-left:6px">
            <span v-if="zone.stores.storeRangeType !== 'EXCLUDE'">{{i18n('适用门店')}}</span>
            <span v-else>{{i18n('不适用门店')}}</span>
          </span>
          <span class="total-shop" style="margin-left: auto">{{ getTotal(zone) }}</span>
        </div>
        <div class="zone-text">
          <InMemPage v-if="zone.stores.storeRangeType !== 'ALL'" :isShowTotal="false" :data="zone.stores.stores">
            <template slot="data" slot-scope="{data}">
              <div class="line-container">
                <div class="line-item" v-for="(zoneStore, indexZoneStore) in data" :key="indexZoneStore">
                  <div>[{{zoneStore.id}}] {{zoneStore.name}};</div>
                </div>
              </div>
            </template>
          </InMemPage>
          <div v-else>
            {{formatI18n('/公用/券模板/全部门店')}}
          </div>
        </div>
      </div>
    </el-row>
  </div>
</template>

<script lang="ts" src="./ActiveStoreDtl.ts">
</script>

<style lang="scss">
.active-store-dtl {
  display: inline-block;
  width: 100%;
  line-height: 20px;
  font-size: 13px;
  .title {
    display: inline-block;
    line-height: 36px;
    width: 100%;
  }
  .store-range-type {
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #242633;
    margin-left: 8px;
    line-height: 34px;
  }

  .shop-item {
    margin-top: 12px;
    &:nth-of-type(1) {
      margin-top: 0;
    }
  }

  .active-store-dtl-content {
    width: 100%;
    background-color: white;
    border: 1px solid #e6e6e6;
    padding: 12px;

    .store-tables {
      padding: 10px;
      display: flex;
      align-items: center;
    }
  }

  .line-container {
    display: flex;
    flex-wrap: wrap;
    .line-item {
      display: inline-block;
      width: 50%;
      line-height: 20px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #242633;
    }
  }

  .zone-text {
    padding: 12px;
    font-size: 13px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #242633;
  }
  .suit {
    background: #e7f9f0;
    color: #0cc66d;
    &::before {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      content: "";
      width: 8px;
      height: 28px;
      background: #0cc66d;
    }
  }

  .unsuited {
    background: #fee6ed;
    color: #fc0049;
    &::before {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      content: "";
      width: 8px;
      height: 28px;
      background: #fc0049;
    }
  }

  .header-block {
    position: relative;
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 28px;
    line-height: 30px;
    font-size: 12px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    padding: 0 16px;

    .total-shop {
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #36445a;
    }
  }

  .flex-wrap {
    display: flex;
    .width-100 {
      display: flex;
      width: 200px;
      justify-content: center;
      align-items: center;
    }
    .flex-item {
    }
    .flex-1 {
      flex: 1;
    }
  }
}
</style>