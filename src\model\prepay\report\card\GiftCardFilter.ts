export default class GiftCardFilter {
  // 发生时间开始
  occurredTimeAfterOrEqual: Nullable<Date> = null
  // 发生时间结束
  occurredTimeBefore: Nullable<Date> = null
  // 卡号类似于
  codeLikes: Nullable<string> = null
  // 卡号等于
  codeEquals: Nullable<string> = null
  // 发生组织
  orgIdEquals: Nullable<string> = null
  // 购卡人/持卡人
  memberIdEquals: Nullable<string> = null
  // 交易号
  transNoLikes: Nullable<string> = null
  // 交易号等于
  transNoEquals: Nullable<string> = null
  // 卡面额大于等于
  faceAmountGreaterOrEquals: Nullable<number> = null
  // 卡面额小于等于
  faceAmountLessOrEquals: Nullable<number> = null
  // 售价大于等于
  priceGreaterOrEquals: Nullable<number> = null
  // 售价小于等于
  priceLessOrEquals: Nullable<number> = null
  // 卡模板
  templateLikes: Nullable<string> = null
  // 卡模板
  templateEquals: Nullable<string> = null
  // 卡模板起始于
  templateStartsWith: Nullable<string> = null
  // 区域
  zoneIdEquals: Nullable<string> = null
  // 活动id或名称类似于
  activityLikes: Nullable<string> = null
  // 余额减少大于等于
  balanceGreaterOrEquals: Nullable<number> = null
  // 余额减少小于等于
  balanceLessOrEquals: Nullable<number> = null
  // 原交易号
  originalTransNoLikes: Nullable<string> = null
  // 原交易号等于
  originalTransNoEquals: Nullable<string> = null
  // 页码
  page: Nullable<number> = null
  // 页面大小
  pageSize: Nullable<number> = null
  // 排序：调整时间，true 升序，false 降序 ，null不排序
  occurredTimeOrder: Nullable<boolean> = false
  // 实充 >=
  amountGreaterOrEquals: Nullable<number> = null
  // 实充 <
  amountLessOrEquals: Nullable<number> = null
  // 发生渠道id
  channelIdEquals: Nullable<string> = null
  // 发生渠道type
  channelTypeEquals: Nullable<string> = null
}
