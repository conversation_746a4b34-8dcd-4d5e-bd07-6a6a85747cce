<template>
    <div class="product-list">
        <BreadCrume :panelArray="panelArray">
            <template slot="operate">
            </template>
        </BreadCrume>
        <ListWrapper class="current-page">
            <template slot="query">
                <el-row>
                    <el-col :span="8">
                        <form-item :label="formatI18n('/资料/商品/商品')">
                            <el-input :placeholder="formatI18n('/资料/商品/请输入商品代码/条码/名称')" v-model="query.key"
                                      style="width: 90%"/>
                        </form-item>
                    </el-col>
                    <el-col :span="8">
                        <form-item :label="formatI18n('/资料/商品/品牌')">
                            <el-input v-model="query.brandIdLikes" style="width:90%"/>
                        </form-item>
                    </el-col>
                    <el-col :span="8">
                        <form-item :label="formatI18n('/资料/商品/品类')">
                            <el-input v-model="query.categoryIdLikes" style="width:90%"/>
                        </form-item>
                    </el-col>
                </el-row>
                <el-row style="margin-top: 10px">
                    <el-col :span="8">
                        <form-item label="">
                            <el-button class="btn-search" @click="doSearch" size="small" type="primary">{{formatI18n('/资料/商品/查询')}}
                            </el-button>
                            <el-button class="btn-reset" @click="doReset" size="small">{{formatI18n('/资料/商品/重置')}}</el-button>
                        </form-item>
                    </el-col>
                </el-row>
            </template>
            <template slot="btn">
                {{formatI18n('/资料/商品/共')}} <span style="font-weight: 500;">{{page.total}}</span>
                {{formatI18n('/资料/商品/个商品')}}
            </template>
            <template slot="list">
                <el-table
                    :data="queryData"
                    ref="dataTable"
                    row-key="barcode"
                    style="width: 100%;"
                    class="table">
                    <el-table-column fixed type="expand">
                        <template slot-scope="props">
                            <el-form label-position="left" inline class="demo-table-expand" style="padding:0 10px;">
                                <el-row style="line-height: 28px;">
                                    <el-col :span="8">{{formatI18n('/资料/商品/商品代码')}}: {{props.row.code}}</el-col>
                                    <el-col :span="8">{{formatI18n('/资料/商品/条码')}}: {{props.row.barcode}}</el-col>
                                    <el-col :span="8">{{formatI18n('/资料/商品/商品名称')}}: {{props.row.name}}</el-col>
                                    <el-col :span="8">{{formatI18n('/资料/商品/规格')}}: {{props.row.qpcStr}}</el-col>
                                    <el-col :span="8">{{formatI18n('/资料/商品/单价')}}: {{props.row.price}}</el-col>
                                    <el-col :span="8">{{formatI18n('/资料/商品/品牌')}}: {{props.row.brandId}}[{{props.row.brandName}}]</el-col>
                                    <el-col :span="8">{{formatI18n('/资料/商品/品类')}}: {{props.row.categoryId}}[{{props.row.categoryName}}]</el-col>
                                    <el-col :span="8" :title="props.row.remark">{{formatI18n('/资料/商品/备注')}}:
                                        {{props.row.remark}}
                                    </el-col>
                                </el-row>
                            </el-form>
                        </template>
                    </el-table-column>
                    <el-table-column fixed :label="formatI18n('/资料/商品/商品代码')" prop="code"/>
                    <el-table-column fixed :label="formatI18n('/资料/商品/条码')" prop="barcode"/>
                    <el-table-column fixed :label="formatI18n('/资料/商品/商品名称')" prop="name"/>
                </el-table>
            </template>
            <template slot="page">
                <el-pagination
                        :current-page="page.currentPage"
                        :page-size="page.size"
                        :page-sizes="[10, 20, 30, 40]"
                        :total="page.total"
                        @current-change="onHandleCurrentChange"
                        @size-change="onHandleSizeChange"
                        background
                        layout="total, prev, pager, next, sizes,  jumper">
                </el-pagination>
            </template>
        </ListWrapper>
    </div>
</template>

<script lang="ts" src="./Product.ts">
</script>

<style lang="scss">
    .product-list{
        background-color: white;
        height: 100%;
        width: 100%;
        overflow: hidden;
        .current-page{
            height: calc(100% - 150px);
            .el-select{
                width: 100%;
            }

            .table {
                .el-col {
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
            }

        }
    }
</style>
