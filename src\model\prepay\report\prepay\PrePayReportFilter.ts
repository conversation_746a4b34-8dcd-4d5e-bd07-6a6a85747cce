import Channel from "model/common/Channel"

export default class PrePayReportFilter {
  // 账户类型id
  accountIdEquals: Nullable<string> = null
  // 会员标识
  memberCode: Nullable<string> = null
  // 门店代码等于
  storeIdEquals: Nullable<string> = null
  // 平台类似于
  platFormLikes: Nullable<string> = null
  // 平台等于
  platFormEquals: Nullable<string> = null
  // 充值面额上限
  rechargeDenominationBegin: Nullable<number> = null
  // 充值面额下限
  rechargeDenominationEnd: Nullable<number> = null
  // 实充增加下限
  amountBegin: Nullable<number> = null
  // 区域
  zoneIdEquals: Nullable<number> = null
  // 实充增加上限
  amountEnd: Nullable<number> = null
  // 交易号类似于
  transNoLikes: Nullable<string> = null
  // 交易号等于
  transNoEquals: Nullable<string> = null
  // 原交易号类似于
  sourceTransNoLikes: Nullable<string> = null
  // 原交易号等于
  sourceTransNoEquals: Nullable<string> = null
  // 交易类型等于
  transTypeEquals: Nullable<string> = null
  // 活动名称类似于
  activityNameLikes: Nullable<string> = null
  // 活动名称类似于
  activityNameStartsWith: Nullable<string> = null
  // 充值卡号类似于
  payCardNoLikes: Nullable<string> = null
  // 充值卡号等于
  payCardNoEquals: Nullable<string> = null
  // 充值类型等于
  depositTypeEquals: Nullable<string> = null
  // 退款方式类似于
  rechargeRefundTypeLikes: Nullable<string> = null
  // 退款方式等于
  rechargeRefundTypeEquals: Nullable<string> = null
  // 付款方式类似于
  payTypeLikes: Nullable<string> = null
  // 付款方式类等于
  payTypeEquals: Nullable<string> = null
  // 储值变动范围下限
  balanceChangeBegin: Nullable<number> = null
  // 储值变动范围上限
  balanceChangeEnd: Nullable<number> = null
  // 调整单号类似于
  adjustNumberLikes: Nullable<string> = null
  // 调整单号类似于
  adjustNumberEquals: Nullable<string> = null
  // 调整原因类似于
  reasonLikes: Nullable<string> = null
  // 调整原因类似于
  reasonEquals: Nullable<string> = null
  // 发生时间开始
  occurredTimeBegin: Nullable<Date> = null
  // 发生时间结束
  occurredTimeEnd: Nullable<Date> = null
  // 页码
  page: Nullable<number> = null
  // 页面大小
  pageSize: Nullable<number> = null
  // 排序：调整时间，true 升序，false 降序 ，null不排序
  occurredTimeOrder: Nullable<boolean> = null
  // 排序：调整单号，true 升序，false 降序 ，null不排序
  adjustNumberOrder: Nullable<boolean> = null
  // 排序：实充金额，true 升序，false 降序 ，null不排序
  amountOrder: Nullable<boolean> = null
  // 排序：返现金额，true 升序，false 降序 ，null不排序
  giftAmountOrder: Nullable<boolean> = null
  // 排序：储值调整金额，true 升序，false 降序 ，null不排序
  totalAmountOrder: Nullable<boolean> = null
  // 发生渠道
  channelEquals: Nullable<Channel> = null
}