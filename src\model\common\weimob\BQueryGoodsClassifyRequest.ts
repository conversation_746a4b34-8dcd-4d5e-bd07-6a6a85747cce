// 查询微盟商品
export default class BQueryGoodsClassifyRequest {
    // 分页页码
    pageNum: Nullable<number> = null
    // 每页包含的数据条数。
    pageSize: Nullable<number> = null
    // 上级分组 ID。不传入则查询一级分组，传入分组 ID 则查询该分组下的子分组。可以通过 weimob_shop/goods/classify/getList 接口获取该 ID。
    parentId: Nullable<string> = null
    // 是否展示系统分组，不传入默认false。
    isSystemClassifyShow: boolean = true
    // 是否显示系统隐藏分组，不传入默认false。
    hiddenSysClassifyShow: boolean = false
    // 营销中心
    marketingCenter: Nullable<string> = null
  }
  