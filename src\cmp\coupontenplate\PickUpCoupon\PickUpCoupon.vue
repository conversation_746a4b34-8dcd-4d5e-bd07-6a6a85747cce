<template>
  <div class="pick-up-coupon">
    <el-form :model="ruleForm" :rules="rules" class="demo-ruleForm" label-width="120px" ref="ruleForm">
      <div v-if="$route.query.from === 'edit'"
        style="height: 48px;line-height: 48px;background-color: #3366ff19;margin: 0 20px;padding-left: 20px;margin-bottom: 10px">
        <img style="position: relative;top: 5px;" src="~assets/image/auth/info3.png"
          alt="">{{ formatI18n('/权益/券/券模板/编辑界面/用券规则下的提示/券模板修改后，已发出的券的有效期仍以原来的为准，其余用券规则以修改后的为准。') }}
      </div>
      <div class="setting-container">
        <!-- 基础信息 -->
        <div class="setting-block">
          <div class="section-title">{{i18n('基础信息')}}</div>
          <!-- 券名称 -->
          <CouponName :ruleForm="ruleForm" :copyFlag='copyFlag'>
            <template slot="slot">
              <el-input maxlength="128" style="width: 390px" v-model="ruleForm.name" @change="doFormItemChange" :placeholder="i18n('请输入')"></el-input>
            </template>
          </CouponName>
          <!-- 券图标 -->
          <CouponTemplateLogo :original-logo-url="ruleForm.logoUrl" @logoUrlCallBack="logoUrlCallBack"></CouponTemplateLogo>
          <!-- 使用须知 -->
          <UseCouponDesc :ruleForm="ruleForm" :isShowTips="false">
            <template slot="slot">
              <el-input :maxlength="remarkMaxlength" style="width: 390px;" type="textarea" v-model="ruleForm.couponProduct"
                :placeholder="remarkPlaceholder" @change="doFormItemChange">
              </el-input>
            </template>
          </UseCouponDesc>
        </div>

        <!-- 优惠设置 -->
        <div class="setting-block">
          <div class="section-title">{{i18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/优惠设置')}}</div>
          <!-- 预约提货 -->
          <el-form-item :label="formatI18n('/公用/券模板', '预约提货')">
            <el-radio-group v-model="ruleForm.reserve" @change="doFormItemChange">
              <el-radio :label="false" style="display: inline-block;margin:13px 0 10px">
                {{ formatI18n('/公用/券模板', '不需要线上预约') }}
              </el-radio>
              <el-radio :label="true" style="display:block;height:40px;line-height:40px">
                {{ formatI18n('/公用/券模板', '需要线上预约') }}
                <div v-if="ruleForm.reserve" style="display:inline-block">
                  ，{{formatI18n('/公用/券模板', '提货时间')}}：
                  {{formatI18n('/公用/券模板', '预约后第')}}
                  <el-form-item style="display: inline-block" prop="reserveStartDay">
                    <AutoFixInput :min="0" :max="ruleForm.reserveEndDay ? Number(ruleForm.reserveEndDay) : 100" :fixed="0"
                      style="width: 100px;margin: 0 10px" v-model="ruleForm.reserveStartDay" :appendTitle="i18n('天')" @change="doFormItemChange">
                    </AutoFixInput>
                  </el-form-item>
                  {{i18n('至')}}
                  <el-form-item style="display: inline-block" prop="reserveEndDay">
                    <AutoFixInput :min="ruleForm.reserveStartDay ? Number(ruleForm.reserveStartDay) : 0" :max="100" :fixed="0"
                      style="width: 100px;margin: 0 10px" v-model="ruleForm.reserveEndDay" :appendTitle="i18n('天')" @change="doFormItemChange">
                    </AutoFixInput>
                  </el-form-item>
                  {{i18n('之间到店提货')}}
                </div>
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>

        <!-- 用券时间 -->
        <div class="setting-block">
          <div class="section-title">{{i18n('/营销/券礼包活动/券查询/用券时间')}}</div>
          <!-- 券有效期 -->
          <CouponEffectPeriod ref="couponEffectPeriod" v-model="ruleForm" :options="options" @change="doFormItemChange">
          </CouponEffectPeriod>
          <!-- 用券时段 -->
          <el-form-item :label="formatI18n('/公用/券模板', '用券时段')" class="cur-form-item">
            <TimeRange @change="doFormItemChange" v-model="ruleForm.time" ref="timeRange">
            </TimeRange>
          </el-form-item>
        </div>

        <!-- 用券范围 -->
        <div class="setting-block">
          <div class="section-title">{{i18n('用券范围')}}</div>
          <!-- 用券渠道 -->
          <el-form-item :label="formatI18n('/公用/券模板', '用券渠道')" class="cur-from-item" required>
            <el-radio-group @change="doUseFromChange" v-model="ruleForm.useFrom">
              <el-radio label="step2" style="display: block">
                <span>{{formatI18n('/公用/券模板/用券渠道', '指定渠道适用')}}</span>
                <div style="display:inline-block;margin-left:10px">
                  <el-form-item class="cur-from-item" prop="useFrom">
                    <el-select style="width: 250px" :disabled="ruleForm.useFrom === 'step1'" multiple @change="doFromChange" v-model="ruleForm.from"
                      :placeholder="formatI18n('/公用/券模板/用券渠道', '请至少选择一个渠道')">
                      <el-option v-for="(item, index) in channels" :key="'channel' + index" :label="item.name"
                        :value="item.channel.typeId">{{item.name}}</el-option>
                    </el-select>
                  </el-form-item>
                </div>
              </el-radio>
              <el-radio label="step1">
                {{formatI18n('/公用/券模板/用券渠道', '全部渠道')}}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <!-- 用券门店 -->
          <el-form-item :label="formatI18n('/公用/券模板', '用券门店')" prop="storeRange">
            <ActiveStore ref="activeStore" :isOldActivity="false" :sameStore="sameStore" :enableStore="enableStore" v-model="ruleForm.storeRange" @change="doStoreChange">
            </ActiveStore>
          </el-form-item>
          <!-- 用券商品-->
          <el-form-item :label="formatI18n('/公用/券模板', '用券商品')" class="cur-form-item" prop="useCouponGood">
            <GetGoodsRange v-model="ruleForm.useCouponGood" @change="doUseCouponGoodsChange" @changeSwitch="changeSwitch"
              :enablePayApportion="enablePayApportion" :goodsMatchRuleMode="goodsMatchRuleMode" :isAutoFixQty="true"></GetGoodsRange>
            <div class="flex">
              <span>{{formatI18n('/资料/门店', '共')+ruleForm.useCouponGood.length+formatI18n('/资料/员工', '项')}}</span>
              <el-select v-model="ruleForm.pickQty" style="margin-left: 10px" @change="doSelectPickQtyChange">
                <el-option v-for="(item, index) in canChooseItems" :key="index" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </div>
          </el-form-item>
        </div>
          <!-- 用券限量 --> 
        <!-- <div class="setting-block">
           <div class="section-title">{{formatI18n('/公用/券模板','用券限量')}}</div>
            <el-form-item :label="formatI18n('/公用/券模板','用券限量')">
              <div>
                <span>{{formatI18n('/公用/券模板','每人每天限用')}}&nbsp;&nbsp; </span>
                <el-form-item class="no-margin" prop="limit" style="display: inline-block;">
                  <AutoFixInput :min="1" :max="99" :fixed="0" style="width: 50px" v-model="ruleForm.maxDailyMemberQuotaQty"></AutoFixInput>
                </el-form-item>
                <span>&nbsp;{{formatI18n('/公用/券模板', '张')}}。</span>    <span style="color: #79879E">{{formatI18n('/公用/券模板','不填则不限量')}}</span>
              </div> 
            </el-form-item>
        </div>         -->
      </div>

      <div class="setting-container">
        <div class="setting-block">
          <div class="section-title">
            <span>{{i18n('高级设置')}}</span>
            <span class="telescoping" @click="telescopingChange">
              <template v-if="!telescoping">{{i18n('/公用/查询条件/收起')}}<i class="el-icon-arrow-up"></i></template>
              <template v-else>{{i18n('/公用/查询条件/展开')}}<i class="el-icon-arrow-down"></i></template>
            </span>
            <span class="gray-tips" style="margin-left:12px">{{i18n('券码生成规则、能否转赠、标签等')}}</span>
          </div>
          <div v-show="!telescoping">
            <!-- 券码生成规则 -->
            <CouponCodeRules :ruleForm="ruleForm" :copyFlag="copyFlag">
              <template slot="slot">
                <el-input :placeholder="formatI18n('/营销/券礼包活动/券礼包活动/新建导出券码发券', '请输入6位以内的数字或字母')" maxlength="6" style="width: 325px"
                  v-model="ruleForm.prefix" @change="doFormItemChange">
                </el-input>
              </template>
            </CouponCodeRules>
            <!-- 能否转赠 -->
            <el-form-item :label="formatI18n('/公用/券模板', '能否转赠')">
              <el-radio-group v-model="ruleForm.transferable" @change="doFormItemChange">
                <el-radio :label="true">{{ formatI18n('/公用/券模板', '是') }}</el-radio>
                <el-radio :label="false">{{ formatI18n('/公用/券模板', '否') }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <!-- 标签 -->
            <CouponTemplateLabel class="cur-from-item" v-model="ruleForm.templateTag" :templateId="ruleForm.templateId" @change="doFormItemChange" />
            <!-- 价格 -->
            <el-form-item :label="i18n('/公用/券模板/价格')">
              <AutoFixInput :min="0.01" :max="99999.99" :fixed="2" @change="doFormItemChange" v-model="ruleForm.price" style="width: 148px"
                :appendTitle="formatI18n('/券/购券管理','元')" />
            </el-form-item>
            <!-- 账款项目 -->
            <el-form-item :label="i18n('账款项目')" prop="termsModel">
              <div v-if="queryCostParyRange=='cost_party'">
                <SelectCostParty v-model="ruleForm.termsModel" :isOnlyId="true" :hideAll="true" width="20%" :placeholder="formatI18n('/公用/下拉框/提示', '请选择')" >
                </SelectCostParty>
              </div>
              <el-input v-if="queryCostParyRange=='customize'" :placeholder="i18n('/储值/预付卡/卡模板/详情页面/请输入{0}位以内的数字或字母',['128'])" maxlength="128" style="width: 325px"
                v-model="ruleForm.termsModel" @change="doFormItemChange">
              </el-input>
            </el-form-item>

            <!-- C端我的券角标 -->
            <el-form-item :label="formatI18n('/公用/券模板', 'C端我的券角标')">
              <el-radio-group v-model="ruleForm.couponSubscriptType" @change="doFormItemChange">
                <el-radio :label="'COMMON'">{{ formatI18n("/公用/券模板", "通用券") }}</el-radio>
                <el-radio :label="'ONLINE'">{{ formatI18n("/公用/券模板", "线上券") }}</el-radio>
                <el-radio :label="'OFFLINE'">{{ formatI18n("/公用/券模板", "线下券") }}</el-radio>
                <el-radio :label="'OUTSIDE'">{{ formatI18n("/公用/券模板", "外部券") }}</el-radio>
              </el-radio-group>
            </el-form-item>
           <!-- 备注 -->
            <el-form-item :label="formatI18n('/公用/券模板', '备注')">
              <el-input v-model="ruleForm.notes" type="textarea" :placeholder="formatI18n('/公用/券模板','请输入不超过1000个字')" :rows="10"
                        maxlength="1000" style="width: 500px;" @change="doFormItemChange"/>
            </el-form-item>
            <!-- 核销链接 -->
            <el-form-item :label="formatI18n('/公用/券模板', '核销链接')">
              <div class="gray-tips">{{formatI18n('/公用/券模板/外部对接使用，比如客户自研小程序上点击用券跳转自定义链接去核销券')}}</div>
              <el-input v-model="ruleForm.writeOffLink" type="textarea" :placeholder="formatI18n('/储值/会员储值/储值充值活动/编辑页面','请输入不超过500个字')" :rows="10"
                        maxlength="500" style="width: 500px;" @change="doFormItemChange"/>
            </el-form-item>
          </div>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script lang="ts" src="./PickUpCoupon.ts">
</script>

<style lang="scss">
.pick-up-coupon {
  padding-bottom: 30px;
  .setting-container {
    .setting-block {
      .section-title {
        .telescoping {
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #007eff;
          margin-left: 12px;
          cursor: pointer;
          i {
            color: #242633;
          }
        }
      }
    }
  }

  .coupon-step,
  .cur-form-item {
    .el-form-item__label {
      &:before {
        content: "*";
        color: #ef393f;
        margin-right: 4px;
      }
    }
  }
  .cur-from-item {
    height: auto !important;
    .el-radio {
      height: auto !important;
      line-height: 40px;
    }
  }
  .flex {
    margin-top: 10px;
  }
}
</style>