import {Component, Prop, Vue, Watch} from 'vue-property-decorator'
import GoodsSelectorDialog from 'cmp/selectordialogs/GoodsSelectorDialog.vue'
import RSGoods from 'model/common/RSGoods'
import AmountToFixUtil from 'util/AmountToFixUtil'
import I18nPage from 'common/I18nDecorator';
import PickUpGoods from "model/common/PickUpGoods";
import IdName from "model/common/IdName";

@Component({
    name: 'GetGiftGoodsRange',
    components: {
        GoodsSelectorDialog
    }
})
@I18nPage({
    prefix: [
        "/公用/券模板",
        '/资料/商品',
        '/公用/券模板/提货券/用券商品'
    ],
    auto: true
})
export default class GetGoodsRange extends Vue {
    $refs: any
    data: PickUpGoods[] = [];
    switchFlag = false
    // 商品单位
    unitList: any[] = [{
        value: true,
        label: this.formatI18n('/公用/券模板/单品折扣券/用券门槛/千克')
    },
        {
            value: false,
            label: this.formatI18n('/公用/券模板/单品折扣券/用券门槛/件')
        }]
    @Prop({
        type: String,
        default: "barcode",
    })
    goodsMatchRuleMode: "barcode" | "code";
    reCordIndex = 0
    @Prop()
    value: any

    @Prop({ type: Boolean, default: false })
    appreciationGoods: boolean;
    @Prop({ type: String, default: 'normal' })
    chooseGoodType: String;
    @Watch('value', {deep: true})
    onValueChange(value: any) {
        if (value && value.length > 0) {
            this.data = value[0].data
        }
    }

    doChange(index: number) {
        this.data[index].qty = AmountToFixUtil.formatNumber(this.data[index].qty, 9999, 1)
        this.$emit('input', this.data)
        this.$emit("change", this.data);
    }

    doClear(index: number) {
        this.data[index].qty = 0
        this.$emit('input', this.data)
        this.$emit("change", this.data);
    }

    doDelete(index: number) {
        this.data.splice(index, 1)
        this.$emit('input', this.data)
        this.$emit("change", this.data);
    }

    doAdd() {
        let arr: RSGoods[] = []
        if (this.data && this.data.length > 0) {
            this.data.forEach((item: any) => {
                let obj: RSGoods = new RSGoods()
                if (this.goodsMatchRuleMode == 'code') {
                    obj.code = item.code
                    obj.qpcStr = item.qpcStr
                } else {
                    obj.barcode = item.goods.id;
                }
                obj.name = item.goods.name;
                obj.price = item.price;
                arr.push(obj)
            })
        }
        console.log('新增时传递的已有数据arr',arr)
        this.$refs.selectGoodsScopeDialog.open(arr)
    }

    doSubmitGoods(arr: RSGoods[]) {
        console.log('dialog选择的商品信息11',arr);
        console.log('11的data',this.data);
        let recordDataArray: any = JSON.parse(JSON.stringify(this.data));
        this.data = [];
        if (arr && arr.length > 0) {
            for (let i = 0; i < arr.length; i++) {
                if (recordDataArray && recordDataArray.length > 0) {
                    let count = 0;
                    for (let j = 0; j < recordDataArray.length; j++) {
                        if (arr[i].barcode === recordDataArray[j].goods.id) {
                            this.data.push(recordDataArray[j]);
                            break;
                        } else {
                            count++;
                        }
                        if (count === recordDataArray.length) {
                            let obj: PickUpGoods = new PickUpGoods();
                            obj.goods = new IdName();
                            obj.code = arr[i].code
                            obj.goods.id = arr[i].barcode;
                            obj.goods.name = arr[i].name;
                            obj.price = arr[i].price;
                            obj.qpcStr = arr[i].qpcStr;
                            obj.qty = 1;
                            obj.isDisp = false;
                            obj.appreciationGoods = arr[i].appreciationGoods
                            this.data.push(obj);
                        }
                    }
                } else {
                    let obj: PickUpGoods = new PickUpGoods();
                    obj.goods = new IdName();
                    obj.code = arr[i].code
                    obj.goods.id = arr[i].barcode;
                    obj.goods.name = arr[i].name;
                    obj.price = arr[i].price;
                    obj.qpcStr = arr[i].qpcStr;
                    obj.qty = 1;
                    obj.isDisp = false;
                    obj.appreciationGoods = arr[i].appreciationGoods
                    this.data.push(obj);
                }
            }
        }
        console.log('最后提交给父组件的信息 :>> ', this.data );
        console.log('刚提交时',this.data);
        if (this.data && this.data.length > 5) {
            this.data = this.data.splice(0, 5)
            this.$message.warning(this.formatI18n('/公用/券模板/提货券/用券商品/点击添加/选择超过10条数据点击确定js提示信息/最多能选择5个商品'))
        }
        // this.$emit("input", this.data);
        this.$emit("change",this.data);
    }

    // doFocus(index: number) {
    //   this.reCordIndex = index
    //   this.$refs.selectGoodsScopeDialog.open([], 'single')
    // }
    // private setAnGoods() {
    //     let obj = {
    //         goodsId: '',
    //         goodsName: '',
    //         goodsSaleAmount: '',
    //         qty: ''
    //     }
    //     this.data.push(obj)
    // }

    getDefaultGoodValue(value: PickUpGoods[]) {
        this.data = value || []
    }
}