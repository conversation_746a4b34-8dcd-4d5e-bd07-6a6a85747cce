/*
 * @Author: 黎钰龙
 * @Date: 2024-05-14 13:33:08
 * @LastEditTime: 2024-05-14 13:33:20
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\promotion\marketApply\MarketingApplyActivityBody.ts
 * 记得注释
 */
import ActivityStateCountResult from 'model/common/ActivityStateCountResult'
import MarketingApplyActivity from './MarketingApplyActivity'

// 营销申请列表
export default class MarketingApplyActivityBody {
  // 积分活动汇总
  summary: Nullable<ActivityStateCountResult> = null
  // 营销申请活动列表
  list: MarketingApplyActivity[] = []
}