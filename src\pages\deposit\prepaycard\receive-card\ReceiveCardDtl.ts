import Bread<PERSON>rume from 'cmp/bread-crumb/BreadCrume';
import FormItem from 'cmp/formitem/FormItem';
import I18nPage from 'common/I18nDecorator';
import MakeCardBillApi from 'http/prepay/card/MakeCardBillApi';
import {Component, Vue, Watch} from 'vue-property-decorator';
import CardTplItem from '../cmp/cardtplitem/CardTplItem';
import MemberOptLogDrawer from 'pages/member/data/drawer/MemberOptLogDrawer';
import DownloadCenterDialog from 'pages/main/dowmload_center_dialog/DownloadCenterDialog';
import MakeCardMessageFilter from 'model/prepay/card/MakeCardMessageFilter';
import MakeCardFailReasonFilter from 'model/prepay/card/MakeCardFailReasonFilter';
import PrepayAccountApi from 'http/prepay/account/PrepayAccountApi';
import ReceiveCardBill from "model/card/receivebill/ReceiveCardBill";
import ReceiveCardBillApi from "http/card/receivebill/ReceiveCardBillApi";
import ReceiveCardBillLine from "model/card/receivebill/ReceiveCardBillLine";
import ReceiveCardBillLineFilter from "model/card/receivebill/ReceiveCardBillLineFilter";
@Component({
    name: 'ReceiveCardDtl',
    components: {
        BreadCrume,
        FormItem,
        CardTplItem,
        MemberOptLogDrawer,
        DownloadCenterDialog
    }
})
@I18nPage({
    prefix: [
        '/卡/卡管理/领卡单/领卡单列表',
        '/卡/卡管理/领卡单/领卡单详情',
        '/卡/卡管理/领卡单/领卡单编辑',
        '/公用/按钮',
        '/公用/券模板',
        '/卡/卡管理/售卡单',
        '/会员/洞察/标签管理/列表页',
        '/资料/品牌',
        '/公用/菜单',
        '/储值/预付卡/预付卡充值单',
        '/会员/洞察/公共/最近消费属性',
        '/公用/券核销',
        '/公用/活动/提示信息',
        '/营销/券礼包活动/新建注册发大礼包/赠送券',
        '/公用/活动/状态',
        '/储值/预付卡/充值卡制售单/列表页面'
    ],
    auto: true
})
export default class ReceiveCardDtl extends Vue {
    billDtl: ReceiveCardBill = new ReceiveCardBill()
    billLines: ReceiveCardBillLine[] = []
    lineCount: number = 0 // 卡数量
    fileDialogVisible: boolean = false
    showTip: boolean = false
    page: any = {
        pageSize: 10,
        page: 1,
        total: 0
    }
    panelArray: any = [
        {
            name: this.i18n('领卡单'),
            url: 'receive-card-list'
        },
        {
            name: this.i18n('领卡单详情'),
            url: ''
        }
    ]

    created() {
        this.getDtl()
        this.queryCardBillLine()
    }


    getDtl() {
        ReceiveCardBillApi.get(this.$route.query.billNumber as string).then((res) => {
            if (res.code === 2000) {
                this.billDtl = res.data || new ReceiveCardBill()
            } else {
                this.$message.error(this.i18n(res.msg!) || this.i18n('获取领卡单详情失败'))
            }
        }).catch((error) => {
            this.$message.error(this.i18n(error.message) || this.i18n('获取领卡单详情失败'))
        })
    }

    queryCardBillLine() {
        const params = new ReceiveCardBillLineFilter()
        params.billNumberEquals = this.$route.query.billNumber.toString()
        params.page = this.page.page - 1
        params.pageSize = this.page.pageSize
        params.probePages = -1
        ReceiveCardBillApi.queryLine(params).then((res) => {
            if (res.code === 2000) {
                this.billLines = res.data || []
                this.lineCount = res.total || 0
                this.page.total = res.total || 0
            } else {
                this.$message.error(this.i18n(res.msg!) || this.i18n('获取领卡单明细失败'))
            }
        }).catch((error) => {
            this.$message.error(this.i18n(error.message) || this.i18n('获取领卡单明细失败'))
        })
    }

    //审核
    doAudit() {
        this.$confirm(
            this.i18n("确定审核当前领卡单吗？"),
            this.i18n("审核"),
            {
                confirmButtonText: this.formatI18n("/公用/按钮", "确定"),
                cancelButtonText: this.formatI18n("/公用/按钮", "取消"),
            }
        ).then(() => {
            ReceiveCardBillApi.audit(this.billDtl.billNumber!).then((res) => {
                if (res.code === 2000) {
                    this.$message.success(this.i18n('操作成功'))
                    this.getDtl()
                    this.queryCardBillLine()
                } else {
                    this.$message.error(this.i18n(res.msg!) || this.i18n('操作失败'))
                }
            }).catch((error) => {
                this.$message.error(this.i18n(error.message) || this.i18n('操作失败'))
            })
        });
    }

    // 导出明细行
    doExport() {
        ReceiveCardBillApi.export(this.billDtl.billNumber!).then((res) => {
            if (res.code === 2000) {
                this.exportAfter()
            } else {
                this.$message.error(this.i18n(res.msg!) || this.i18n('导出失败'))
            }
        }).catch((error) => {
            this.$message.error(this.i18n(error.message) || this.i18n('导出失败'))
        })
    }

    doEdit() {
        this.$router.push({
            name: 'receive-card-edit',
            query: {
                editType: 'edit',
                billNumber: this.billDtl.billNumber
            }
        })
    }

    onHandleCurrentChange(val: number) {
        this.page.page = val
        this.queryCardBillLine()
    }

    onHandleSizeChange(val: number) {
        this.page.pageSize = val
        this.queryCardBillLine()
    }

    computeState(state: string) {
        let str = '-'
        let color = '#A1B0C8'
        if (state === 'INITIAL') {
            str = this.i18n('待领出')
            color = '#984de8'
        } else if (state === 'SUCCESS') {
            str = this.i18n('成功')
            color = '#3bde3e'
        } else if (state === 'FAIL') {
            str = this.i18n('失败')
            color = '#e0154b'
        }
        return {
            state: str,
            color: color
        }
    }

    exportAfter() {
        this.showTip = true;
        this.fileDialogVisible = true;
    }

    doDownloadDialogClose() {
        this.showTip = false;
        this.fileDialogVisible = false;
        this.getDtl()
        this.queryCardBillLine()
    }


};