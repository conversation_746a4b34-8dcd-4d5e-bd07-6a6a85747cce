// 大转盘参与记录
export default class BigWheelTakePartRecord {
  // uuid
  uuid: Nullable<string> = null
  // 活动号
  activityNumber: Nullable<string> = null
  // 会员id
  memberId: Nullable<string> = null
  // 首次参与时间
  firstOccurredTime: Nullable<Date> = null
  // 最后参与时间
  lastOccurredTime: Nullable<Date> = null
  // 参与次数
  takePartCount: Nullable<number> = null
  // 奖励
  reward: string[] = []
  // 版本
  version: Nullable<string> = null
  // 会员标识
  memberCode: Nullable<string> = null
}