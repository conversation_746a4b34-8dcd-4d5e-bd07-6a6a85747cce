import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import MemberApi from 'http/member_standard/MemberApi'

@Component({
    name: 'CheckCouponDialog',
    components: {}
})
export default class CouponsDialog extends Vue {
    coupons: any[] = []
    @Prop()
    memberId: any
    @Prop()
    title: any

    @Prop({
        type: Boolean,
        default: false
    })
    dialogShow: boolean
    @Watch('memberId')
    watchMemberId() {
        if (this.memberId) {
            MemberApi.getCoupon(this.memberId as string).then((resp: any) => {
                if (resp && resp.code === 2000) {
                    this.coupons = resp.data
                }
            }).catch((error: any) => {
                if (error && error.message) {
                    this.$message.error(error.message)
                }
            })
        }

    }
    doBeforeClose(done: any) {
        this.$emit('dialogClose')
        done()
    }
    doModalClose() {
        this.$emit('dialogClose')
    }
    doCancel() {
        this.$emit('dialogClose')
    }
    doCheckGoods() {
        // todo
    }
}