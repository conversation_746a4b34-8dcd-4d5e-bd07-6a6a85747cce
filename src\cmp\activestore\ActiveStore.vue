<template>
  <span class="active-store-view">
    <div v-if="showTip &&(store.storeRangeType === i18n('指定门店适用') ||store.storeRangeType === i18n('指定门店不适用'))" style="color: #999c9e">
      -{{i18n("如果活动门店为部分门店，则微信领卡二维码必须不同门店使用不同二维码")}}
    </div>
    <!-- 多营销中心或活动追加门店 -->
    <el-form :model="store" ref="form" v-if="promotionCenter || isAddStores">
      <!-- 总部、非总部 卡券模板以外的活动 -->
      <div v-if="isOldActivity === true">
        <el-radio-group @change="doStoreRange" v-model="storeRangeType" v-if="justPart === false">
          <el-radio label="ALL">{{ formatI18n("/公用/券模板", "当前营销中心")+formatI18n("/公用/券模板", "全部门店") }}</el-radio>
          <el-radio label="PART">
            {{ formatI18n("/权益/积分/积分初始化/未初始化状态/得积分规则/基础得积分/点击立即设置", "指定门店适用") }}
          </el-radio>
          <el-radio label="EXCLUDE">
            {{formatI18n("/权益/积分/积分初始化/未初始化状态/得积分规则/基础得积分/点击立即设置", "指定门店不适用")}}
          </el-radio>
          <el-radio label="ZONE" v-if="!isHideZone">
            {{ formatI18n("/公用/门店组件/指定区域适用") }}
          </el-radio>
        </el-radio-group>
        <div v-if="storeRangeType === 'PART' || storeRangeType === 'EXCLUDE' || isAddStores">
          <el-input style="width: 375px" v-model="store.marketingCenters[0].storesValue" @focus="doSelect"></el-input>
          <el-button style="margin-left: 20px" type="text" @click="doClearStore">{{formatI18n('/公用/导入/清空')}}</el-button>
          <span style="margin-left: 20px" v-if="store && store.stores">{{ getStoreCount(store.marketingCenters[0].stores.stores.length) }}</span>
          <el-form-item v-if="internalValidate" prop="inputValue" :rules="inputValueRules" class="auto-expand-form-item"></el-form-item>
          <el-button type="primary" @click="doImport">{{ formatI18n("/公用/券模板", "导入") }}</el-button>
        </div>
        <div v-if="storeRangeType === 'ZONE'">
          <ActiveStoreArea v-model="store" @change="getAreaData" ref="activeStoreArea"> </ActiveStoreArea>
        </div>
      </div>
      <!-- 卡券模板，区分总部非总部情况 -->
      <div v-else>
        <div v-if="headquarters === 'false' && showAll === false">
          <el-radio-group @change="doStoreRange" v-model="storeRangeType">
            <el-radio label="ALL">{{ formatI18n("/公用/券模板", "当前营销中心")+formatI18n("/公用/券模板", "全部门店") }}</el-radio>
            <el-radio label="PART">
              {{ formatI18n("/权益/积分/积分初始化/未初始化状态/得积分规则/基础得积分/点击立即设置", "指定门店适用") }}
              <template v-if="storeRangeType === 'PART'">
                <span @click="doSelect" v-if=" !store.marketingCenters[0].stores.stores.length" style="color:#20A0FF">
                  {{i18n('选择门店')}}
                </span>
                <span v-else>
                  {{i18n('已选择')}}
                  <span class="number-text">{{store.marketingCenters[0].stores.stores.length}}</span>
                  {{i18n('家门店')}}
                  <span style="color:#20A0FF" @click="doSelect">{{i18n('修改')}}</span>
                </span>
              </template>
            </el-radio>
            <el-radio label="EXCLUDE">
              {{formatI18n("/权益/积分/积分初始化/未初始化状态/得积分规则/基础得积分/点击立即设置", "指定门店不适用")}}
              <template v-if="storeRangeType === 'EXCLUDE'">
                <span @click="doSelect" v-if=" !store.marketingCenters[0].stores.stores.length" style="color:#20A0FF">
                  {{i18n('选择门店')}}
                </span>
                <span v-else>
                  {{i18n('已选择')}}
                  <span class="number-text">{{store.marketingCenters[0].stores.stores.length}}</span>
                  {{i18n('家门店')}}
                  <span style="color:#20A0FF" @click="doSelect">{{i18n('修改')}}</span>
                </span>
              </template>
            </el-radio>
            <el-radio label="ZONE" v-if="!isHideZone">
              {{ formatI18n("/公用/门店组件/指定区域适用") }}
            </el-radio>
          </el-radio-group>
          <div v-if="storeRangeType === 'ZONE'">
            <ActiveStoreArea v-model="store" @change="getAreaData" ref="activeStoreArea"> </ActiveStoreArea>
          </div>
          <template v-if="storeRangeType === 'PART' || storeRangeType === 'EXCLUDE'">
            <el-form-item v-if="internalValidate " prop="inputValue" :rules="inputValueRules">
            </el-form-item>
          </template>
        </div>
        <!-- 为总部时： -->
        <div v-else-if="headquarters === 'true' || showAll === true">
          <el-radio-group @change="doStoreRange1" v-model="storeRangeType">
            <el-radio label="ALL">{{ i18n("全部门店") }}</el-radio>
            <el-radio label="MARKER_CENTER">{{ i18n("指定营销中心适用") }}
              <template v-if="storeRangeType === 'MARKER_CENTER'">
                <span @click="doSelectMarkerCenter" v-if=" !store.marketingCenters.length" style="color:#20A0FF">
                  {{i18n('选择营销中心')}}
                </span>
                <span v-else>
                  {{i18n('已选择')}}
                  <span class="number-text">{{store.marketingCenters.length}}</span>
                  {{i18n('家营销中心')}}
                  <span style="color:#20A0FF" @click="doSelectMarkerCenter">{{i18n('修改')}}</span>
                </span>
              </template>
            </el-radio>
            <el-radio label="INCLUDE_ORG">
              {{ formatI18n("/权益/积分/积分初始化/未初始化状态/得积分规则/基础得积分/点击立即设置", "指定门店适用") }}
              <template v-if="storeRangeType === 'INCLUDE_ORG'">
                <span @click="doSelect" v-if=" !store.stores && !store.stores.length" style="color:#20A0FF">
                  {{i18n('选择门店')}}
                </span>
                <span v-else>
                  {{i18n('已选择')}}
                  <span class="number-text">{{store.stores.length}}</span>
                  {{i18n('家门店')}}
                  <span style="color:#20A0FF" @click="doSelect">{{i18n('修改')}}</span>
                </span>
              </template>
            </el-radio>
            <el-radio label="EXCLUDE_ORG">
              {{formatI18n("/权益/积分/积分初始化/未初始化状态/得积分规则/基础得积分/点击立即设置", "指定门店不适用")}}
              <template v-if="storeRangeType === 'EXCLUDE_ORG'">
                <span @click="doSelect" v-if=" !store.stores && !store.stores.length" style="color:#20A0FF">
                  {{i18n('选择门店')}}
                </span>
                <span v-else>
                  {{i18n('已选择')}}
                  <span class="number-text">{{store.stores.length}}</span>
                  {{i18n('家门店')}}
                  <span style="color:#20A0FF" @click="doSelect">{{i18n('修改')}}</span>
                </span>
              </template>
            </el-radio>
            <!-- <el-radio label="ZONE">
              {{ formatI18n("/公用/门店组件/指定区域适用") }}
            </el-radio> -->
          </el-radio-group>
          <!-- <div v-if="storeRangeType === 'ZONE'">
            <ActiveStoreArea v-model="store" @change="getAreaData" ref="activeStoreArea"> </ActiveStoreArea>
          </div> -->
        </div>
      </div>

    </el-form>
    <NoMarketCenter ref="noMarket" v-else :showTip="showTip" :sameStore="sameStore" :internalValidate="internalValidate" :values="comeValue"
      @commit="commitNoMC" @change="parentChange" />
    <StoreSelectorDialog ref="selectGoodsScopeDialog" :hasImport="true" :enableStore="enableStore" :queryByMarketingCenter="!queryAllOrg"
      @doImport="doImport" @summit="doSubmitGoods"></StoreSelectorDialog>
    <MarketCenterSelectorDialog ref="selectMarketCenterScopeDialog" @summit="doSubmitMarketCenter"></MarketCenterSelectorDialog>
    <StoreMulPromotionSelectorDialog :marketCenterId="marketCenterId" :marketCenterName="marketCenterName" ref="mulPromotionStore"
      @summit="doMulStoreSubmitGoods"></StoreMulPromotionSelectorDialog>
    <PromotionCenterSelectorDialog ref="selectPromotionCenterSelectorDialog" @summit="doPromotionSubmitGoods"></PromotionCenterSelectorDialog>
    <ImportDialog :dialogShow.sync="importDialogShow" :importUrl="importUrl" :templatePath="templatePath" @dialogClose="importDialogShow = false"
      @upload-success="doUploadSuccess" :templateName="formatI18n('/公用/券模板', '门店模板')" :title="formatI18n('/公用/券模板', '导入')" :isSingle="true">
    </ImportDialog>
    <ImportResultDialog :data="importResult" :dialogShow="importResultDialogShow" @importResultDialogClose="importResultDialogClose">
    </ImportResultDialog>
  </span>
</template>

<script lang="ts" src="./ActiveStore.ts"></script>

<style lang="scss" scoped>
.active-store-view {
  .promotion-center-store {
    max-width: 800px;
  }

  .title {
    padding-left: 10px;
    height: 32px;
    line-height: 32px;
    background-color: #ced0da;
  }

  .rows {
    padding: 10px;
    border: 1px solid #ced0da;
    border-top: none;
  }

  ::v-deep.el-radio__input.is-checked + .el-radio__label {
    color: #242633;
  }
}
</style>
