export default class UserGroup {
  // uuid
  uuid: Nullable<string> = null
  // 客群名称
  name: Nullable<string> = null
  // 创建方式：impt-导入创建;create-规则创建
  source: Nullable<string> = null
  // 调度类型：DailyScheduleType-按天；MonthlyScheduleType-按月
  scheduleType: Nullable<string> = null
  // 按月执行的天
  monthDay: Nullable<number> = null
  // 客群状态：Enable-启用；Disable-禁用；
  state: Nullable<string> = null
  // 最后执行状态：Processing-执行中;Success-就成功;Fail-计算失败
  lastExecuteState: Nullable<string> = null
  // 最后执行开始时间
  lastBeginTime: Nullable<Date> = null
  // 最后执行结束时间
  lastEndTime: Nullable<Date> = null
  // 会员数
  numberCount: Nullable<number> = null
  // 会员总数
  numberTotalCount: Nullable<number> = null
  // 总量占比
  memberRate: Nullable<number> = null
  // 备注
  remark: Nullable<string> = null
  // 备注
  executeRemark: Nullable<string> = null
  // 创建人
  creator: Nullable<string> = null
  // 创建时间
  created: Nullable<Date> = null
  // 最后修改人
  lastModifier: Nullable<string> = null
  // 最后修改时间
  lastModified: Nullable<Date> = null
  // 标记
  tags: string[] = []
}