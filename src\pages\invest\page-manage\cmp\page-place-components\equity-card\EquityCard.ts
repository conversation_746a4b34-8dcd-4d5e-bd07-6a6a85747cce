import { Vue, Component, Prop, Watch } from "vue-property-decorator";

import I18nPage from "common/I18nDecorator";

import PlaceTemplateMixins from "../PlaceTemplateMixins";
import { enefitCardTemplateStatus } from "model/equityCard/default/enefitCardTemplateStatus";
import BenefitCardTemplateFilter from "model/equityCard/default/BenefitCardTemplateFilter";
import { enefitCardTemplateType } from "model/equityCard/default/enefitCardTemplateType";
import BenefitCardTemplateApi from "http/equityCard/BenefitCardTemplateApi";
import BenefitCardTemplate from "model/equityCard/BenefitCardTemplate";
@Component({
    name: "EquityCard",
    mixins: [PlaceTemplateMixins],
    components: {},
})
@I18nPage({
    prefix: ["/公用/券模板", "/页面/页面管理", "/页面/导航设置"],
    auto: true,
})
export default class EquityCard extends Vue {
    @Prop()
    config: any;
    @Prop()
    componentItem: any;
    @Prop({ type: Boolean, default: false })
    readonly: boolean; //
    @Prop({ type: String, default: "EquityCard" })
    validateName: string; // 组件校验名称，editPage通过该名称进行组件校验及校验信息清除
    @Prop({ type: Object })
    value: any; // 数据模型
    @Prop({ type: String, default: "组件名称" })
    label: string; // label名

    loadingEquityCardTemplateList: boolean = false;
    equityCardTemplateList: BenefitCardTemplate[] = [];

    get localProperty() {
        return this.componentItem.props;
    }

    mounted(){
        this.getEquityCardTemplateList()
    }

    getEquityCardName(){
        let result =  '--'
        if(this.localProperty.propEquityCard){
            const find = this.equityCardTemplateList.find((item)=>{
                return item.code === this.localProperty.propEquityCard
            })
            if(find){
                return find.name 
            }
        }

        return result
    }

    getEquityCardTemplateList() {
        const params = new BenefitCardTemplateFilter();
        // 查询全部
        params.page = 0;
        params.pageSize = 0;
        // 仅已启用
        params.statusEquals = enefitCardTemplateStatus.start;
        params.typeEquals = enefitCardTemplateType.free;
        params.marketingCenterEquals = sessionStorage.getItem("marketCenter");
        this.loadingEquityCardTemplateList = true;
        BenefitCardTemplateApi.query(params)
            .then((response) => {
                if (response.data?.length) {
                    this.equityCardTemplateList = response.data;
                } else {
                    this.equityCardTemplateList = [];
                }
            })
            .finally(() => {
                this.loadingEquityCardTemplateList = false;
            });
    }
}
