import {Component, Vue} from 'vue-property-decorator'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import FormItem from 'cmp/formitem/FormItem.vue'
import BirthdayBenefitApi from 'http/benefit/BirthdayBenefitApi'
import PointsRule from 'model/benefit/PointsRule'
import GradeApi from "http/grade/grade/GradeApi";
import BirthdayPointsItem from "pages/benefit/birthday/BirthdayPointsItem";

@Component({
  name: 'BirthdayPointsAdd',
  components: {
    BreadCrume,
    FormItem,
  }
})
export default class BirthdayPointsAdd extends Vue {

  get getLabel() {
    let label = (sessionStorage.getItem('locale') === 'zh_CN') ? "：" : ":"
    return this.formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍', '会员生日权益').concat(label)
  }
  isActive = false
  $refs: any
  panelArray: any = []
  pointsRule: PointsRule = new PointsRule()
  giftType = 'same'
  limitType = 'UN_LIMIT'
  sameRules: any = {}
  pointsItemsBody: any = {
    data: []
  }

  created() {
    if (sessionStorage.getItem('locale') === 'zh_CN') {
      this.isActive = true
    } else {
      this.isActive = false
    }
    this.panelArray = [
      {
        name: this.formatI18n('/公用/菜单', '节日有礼'),
        url: 'score-init'
      },
      {
        name: this.formatI18n('/权益/生日权益初始化/生日权益初始化', '生日积分加倍'),
        url: ''
      }
    ]
    this.sameRules = {
      samePointsTimes: [
        {required: true, message: this.formatI18n('/权益/生日权益初始化/生日权益初始化', '该字段不能为空'), trigger: 'blur'}
      ],
    }
  }

  mounted() {
    if (this.$route.query.from === 'edit') {
      this.resetData()
    } else {
      this.pointsRule.effectType = 'BY_DAY'
      this.getGrade()
    }
  }

  doModify() {
    if (!this.checkParams()) {
      return
    }
    this.buildParams()
    BirthdayBenefitApi.saveOrModifyPointsRule(this.pointsRule)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.formatI18n('/公用/js提示信息', '保存成功'))
          this.$router.push({name: 'birthday-points-dtl'})
        }
      }).catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
    })
  }

  doGiftTypeChange() {

  }

  limitTypeChange() {
    if (this.limitType == 'UN_LIMIT') {
      this.pointsRule.memberMaxGainPointsTimes = null
    }
  }

  doBack() {
    this.$router.back()
  }

  private checkParams() {
    let validResult = true
    if (this.giftType === 'same') {
      this.$refs['sameForm'].validate((valid: any) => {
        if (!valid) {
          validResult = false;
        }
      });
    } else if (this.giftType === 'different') {
      this.$refs['diffForm'].validate((valid: any) => {
        if (!valid) {
          validResult = false;
        }
      });
    }
    return validResult
  }

  private buildParams() {
    if (this.giftType === 'same') {
      this.pointsRule.differentPointsTimes = null
    } else if (this.giftType === 'different') {
      this.pointsRule.samePointsTimes = null;
      let pointsMap: any = {}
      for (let item of this.pointsItemsBody.data) {
        if (item.code) {
          pointsMap[item.code] = item.pointsTimes
        }
      }
      this.pointsRule.differentPointsTimes = pointsMap
    }
  }

  private resetData() {
    BirthdayBenefitApi.pointsRuleDetail()
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.pointsRule = resp.data
          if (this.pointsRule.samePointsTimes) {
            this.giftType = 'same'
          } else if (this.pointsRule.differentPointsTimes) {
            this.giftType = 'different'
          }
          if (this.pointsRule.memberMaxGainPointsTimes) {
            this.limitType = 'LIMIT'
          } else {
            this.limitType = 'UN_LIMIT'
          }
          if (resp.data) {
            this.getGrade()
          }
        }
      }).catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
    })
  }

  private getGrade() {
    GradeApi.listGrade('')
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.initGiftData(resp.data)
        }
      }).catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
    })
  }

  private initGiftData(gradeData: any[]) {
    let pointsItems: BirthdayPointsItem[] = []
    if (gradeData) {
      for (let item of gradeData) {
        let data: BirthdayPointsItem = new BirthdayPointsItem()
        data.code = item.code
        data.name = item.name
        pointsItems.push(data)
      }
    }
    if (this.pointsRule.differentPointsTimes) {
      for (let item of pointsItems) {
        if (item.code) {
          item.pointsTimes = this.pointsRule.differentPointsTimes[item.code]
        }
      }
    }
    this.pointsItemsBody['data'] = pointsItems
  }
}