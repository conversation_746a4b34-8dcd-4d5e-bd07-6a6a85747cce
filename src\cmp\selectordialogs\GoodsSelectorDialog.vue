<template>
  <el-dialog :title="formatI18n('/公用/公共组件/商品选择弹框组件/标题/选择单品')" class="select-goods-dialog" append-to-body :close-on-click-modal="false"
    :visible.sync="dialogShow">
    <div class="wrap">
      <el-row>
        <el-form label-width="80px">
          <el-col :span="8">
            <el-form-item :label="formatI18n('/公用/公共组件/商品选择弹框组件/查询/代码')">
              <el-input v-model="goodsFilter.codeLikes" @keyup.enter.native="doSearch()" :placeholder="formatI18n('/公用/公共组件/商品选择弹框组件/查询/请输入商品代码')" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item v-if="goodsMatchRuleMode == 'barcode'" :label="formatI18n('/公用/公共组件/商品选择弹框组件/查询/条码')">
              <el-input v-model="goodsFilter.barcodeLikes" @keyup.enter.native="doSearch()"
                :placeholder="formatI18n('/公用/公共组件/商品选择弹框组件/查询/请输入商品条码')" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="formatI18n('/公用/公共组件/商品选择弹框组件/查询/名称')">
              <el-input v-model="goodsFilter.nameLikes" @keyup.enter.native="doSearch()" :placeholder="formatI18n('/公用/公共组件/商品选择弹框组件/查询/请输入商品名称')" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="headquarters === true || (showAll && enableMultiMarketingCenter)">
            <el-form-item :label="formatI18n('/公用/公共组件/商品选择弹框组件/查询/单品来源')" label-width="120px">
              <!-- <el-form-item label="单品来源"> -->
              <el-select v-model="marketCenter" :placeholder="formatI18n('/资料/渠道/请选择')" style="width: 200px" @change="doSearch()">
                <el-option v-for="(value,index) in marketingCentersList" :key="index" :value="value.marketingCenter.id"
                  :label="'['+value.marketingCenter.id+']'+value.marketingCenter.name">[{{value.marketingCenter.id}}]{{value.marketingCenter.name}}</el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label=" ">
              <el-button type="primary" @click="doSearch()">{{ formatI18n("/公用/按钮/查询") }}</el-button>
              <el-button @click="doReset()">{{ formatI18n("/公用/按钮/重置") }}</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      <el-row>
        <el-col :span="16">
          <el-row class="table-wrap" v-loading="loading.query">
            <el-row class="thead">
              <el-col :span="1">
                <el-checkbox :disabled="!allSelectable && !checkAll" @change="doCheckAll($event)" v-model="checkAll" v-if="model === 'multiple'" />
                <span v-if="model === 'single'">&nbsp;</span>
              </el-col>
              <el-col :span="tableRowSpan">{{ formatI18n("/公用/公共组件/商品选择弹框组件/表格/商品代码") }}</el-col>
              <el-col v-if="goodsMatchRuleMode == 'barcode'" :span="6">{{ formatI18n("/公用/公共组件/商品选择弹框组件/表格/商品条码") }}</el-col>
              <el-col :span="tableRowSpan">{{ formatI18n("/公用/公共组件/商品选择弹框组件/表格/商品名称") }}</el-col>
              <el-col :span="5">{{ formatI18n("/公用/公共组件/商品选择弹框组件/表格/规格") }}</el-col>
            </el-row>
            <el-row class="tbody" v-if="!loading.query">
              <template v-if="currentList && currentList.length > 0">
                <el-row v-for="(item, index) of currentList" :key="index" class="trow">
                  <el-col :span="1">
                    <el-checkbox :disabled="!selectable && !checkboxList[index]" v-model="checkboxList[index]" @change="doCheck($event, index)"
                      v-if="model === 'multiple'" />
                    <span v-if="model === 'single'">&nbsp;</span>
                  </el-col>
                  <el-col @click.native="clickRow(item, index)" :span="tableRowSpan" :title="item.code">{{ item.code || '-' }}</el-col>
                  <el-col @click.native="clickRow(item, index)" v-if="goodsMatchRuleMode == 'barcode'" :span="6" :title="item.barcode">{{ item.barcode || '-' }}
                  </el-col>
                  <el-col @click.native="clickRow(item, index)" :span="tableRowSpan" :title="item.name">{{ item.name || '-' }}</el-col>
                  <el-col @click.native="clickRow(item, index)" :span="5"
                    :title="item.qpcStr">{{ item.qpcStr || '-' }}</el-col>
                </el-row>
              </template>
              <el-row v-if="!currentList || currentList.length === 0" class="trow" style="text-align: center;color: #909399">
                {{ formatI18n("/公用/提示/暂无数据") }}
              </el-row>
            </el-row>
          </el-row>
        </el-col>
        <el-col :span="8" style="padding-left: 5px">
          <el-row class="right-table">
            <el-row class="thead">
              {{ formatI18n("/公用/公共组件/商品选择弹框组件/表格/已选商品：") }}{{ (filteredSelected || []).length }}
            </el-row>
            <el-row style="padding: 5px;">
              <el-input style="width: 100%;" @keyup.enter.native="filterSelected()" @change="filterSelected()" v-model="selectedFilter" clearable
                @clear="filterSelected()"
                :placeholder="goodsMatchRuleMode == 'code' ? i18n('请输入商品代码/名称') : formatI18n('/公用/公共组件/商品选择弹框组件/查询/请输入商品条码/名称')"></el-input>
            </el-row>
            <el-row class="tbody">
              <template v-if="tableFakeData && tableFakeData.length > 0">
                <el-row class="trow" style="position: relative;display: flex;align-items: center" :key="index" v-for="(item, index) of tableFakeData"
                  :title="{ id: goodsMatchRuleMode == 'code' ? item.code : item.barcode, name: item.name } | idName">
                  <div class="left">
                    [{{(goodsMatchRuleMode == 'code' ? item.code : item.barcode) || '-'}}]{{item.name || '-'}}{{(goodsMatchRuleMode == 'code' ? [item.qpcStr] : '') || '-'}}
                  </div>
                  <div class="clear-btn" style="display: none">
                    <a @click="delItem(item, index)">{{ formatI18n("/公用/公共组件/品牌选择弹框组件/表格/清除") }}</a>
                  </div>
                </el-row>
              </template>
              <!-- <virtual-list style="height: 100%;overflow-y: auto" :data-key="'barcode'" :data-sources="filteredSelected" :data-component="itemComponent" > -->
              <el-row v-if="!filteredSelected || filteredSelected.length === 0" class="trow" style="text-align: center;color: #909399">
                {{ formatI18n("/公用/提示/暂无数据") }}
              </el-row>
              <div class="fixed-page">
                <el-pagination small @current-change="handleFakeCurrentChange" :current-page.sync="currentFakePage" :page-size="pageFakeSize"
                  layout="prev, pager, next,  jumper" :total="totalFake">
                </el-pagination>
              </div>
            </el-row>
          </el-row>
        </el-col>
      </el-row>
    </div>
    <div class="page" style="margin-top: 15px">
      <el-pagination :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
        @current-change="handleCurrentChange($event)" @size-change="handleSizeChange($event)" background
        layout="total, prev, pager, next, sizes,  jumper">
      </el-pagination>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="dialogShow = false">{{ formatI18n("/公用/按钮/取消") }}</el-button>
      <el-button size="small" type="primary" @click="doModalClose()">{{ formatI18n("/公用/按钮/确定") }}</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./GoodsSelectorDialog.ts" />

<style lang="scss" scoped>
.select-goods-dialog {
  display: flex;
  align-items: center;
  justify-content: center;
  @import "SelectorDialogCommon";
  ::v-deep .el-dialog {
    height: 750px !important;
  }
  .dialog-footer {
    margin-top: -15px;
  }
}
</style>
