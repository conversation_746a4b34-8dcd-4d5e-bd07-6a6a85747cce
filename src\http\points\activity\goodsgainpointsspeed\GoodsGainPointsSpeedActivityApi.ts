import ApiClient from 'http/ApiClient'
import GoodsGainPointsSpeedActivity from 'model/points/activity/goodsgainpointsspeed/GoodsGainPointsSpeedActivity'
import Response from 'model/common/Response'
import ActivityGroupLine from "model/common/ActivityGroupLine";

export default class GoodsGainPointsSpeedActivityApi {
  /**
   * 详情
   * 详情。
   *
   */
  static detail(activityId: string): Promise<Response<GoodsGainPointsSpeedActivity>> {
    return ApiClient.server().get(`/v1/points-activity/goods-gain-points-speed/detail/${activityId}`, {}).then((res) => {
      return res.data
    })
  }

  static listGroupLineByActivity(activityId: string[]): Promise<Response<ActivityGroupLine[]>> {
    return ApiClient.server().post(`/v1/points-activity/goods-gain-points-speed/listGroupLineByActivity`, activityId, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 修改或保存
   * 修改或保存。
   *
   */
  static saveOrModify(body: GoodsGainPointsSpeedActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/points-activity/goods-gain-points-speed/saveOrModify`, body, {}).then((res) => {
      return res.data
    })
  }

}
