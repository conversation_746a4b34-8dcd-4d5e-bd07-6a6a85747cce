<!--
 * @Author: 黎钰龙
 * @Date: 2023-07-11 10:49:51
 * @LastEditTime: 2024-04-24 16:18:02
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\coupontenplate\FormItemCmp\CouponEffectPeriod\CouponEffectPeriod.vue
 * 记得注释
-->
<template>
  <div>
    <el-form ref="form" :model="ruleForm" :rules="rules">
      <el-form-item v-if="!specialMode" :label="i18n('券有效期')" class="cur-form-item" label-width="120px">
        <el-radio-group @change="doCouponValidateChange" v-model="ruleForm.dateType">
          <el-radio label="RALATIVE" style="display:block">
            <span>{{i18n("用户领券后")}}</span>
            <el-form-item prop="selectModel" style="display:inline-block">
              <el-select v-model="ruleForm.selectModel" @change="doSelectChange" style="width:120px" :disabled="!isDisabled('RALATIVE')">
                <el-option value="NOW" :label="i18n('立即生效')"></el-option>
                <el-option value="CUSTOM" :label="i18n('/公用/日期/自定义')"></el-option>
              </el-select>
            </el-form-item>
            <template v-if="ruleForm.selectModel === 'CUSTOM'">
              <el-form-item prop="dateFrom" style="display: inline-block">
                <el-input @change="doCouponChange(0)" :disabled="!isDisabled('RALATIVE')" style="width: 120px" v-model="ruleForm.dateFrom">
                  <template slot="prepend">{{i18n('第')}}</template>
                  <template slot="append">{{i18n('天')}}</template>
                </el-input>
              </el-form-item>
              {{ i18n("生效，有效期为") }}
            </template>
            <template v-else-if="ruleForm.selectModel === 'NOW'">
              {{i18n('且有效期为')}}
            </template>
            <el-popover placement="top-start" trigger="hover">
              <div class="tip-content">
                <div>{{i18n('按天数')}}：</div>
                <div style="margin-left:12px">-{{i18n('配置设置相对时间，即截止时间根据领取时间点计算（phoenix-coupon-core.coupon.relativeEffectiveTimeConfig: baseGetTime）：')}}</div>
                <div style="margin-left:24px">
                  ·{{i18n('如领券后【2】天生效，有效期【按天数5天】。用户2023年7月1日13:25:50领券：生效时间为2023年7月3日13:25:50（含），失效时间为2023年7月8日13:25:50（含）；如领券后【0】天生效，有效期【按天数5天】。用户2023年7月1日13:25:50领券：生效时间为“立即生效”，失效时间为2023年7月6日13:25:50（含）')}}
                </div>
                <div style="margin-left:12px">-{{i18n('配置设置最后时间，即截止时间是23:59:59（phoenix-coupon-core.coupon.relativeEffectiveTimeConfig：dayLastTime）：')}}</div>
                <div style="margin-left:24px">
                  ·{{i18n('如领券后【2】天生效，有效期【按天数5天】。用户2023年7月1日13:25:50领券：生效时间为2023年7月3日13:25:50（含），失效时间为2023年7月8日23:59:59（含）；如领券后【0】天生效，有效期【按天数5天】。用户2023年7月1日13:25:50领券：生效时间为“立即生效”，失效时间为2023年7月6日23:59:59（含）')}}
                </div>
                <div>
                  {{i18n('当月：如领券后【2】天生效，有效期【当月】。用户2023年7月1日13:25:50领券：生效时间为2023年7月3日13:25:50（含），失效时间为7月31日23:59:59（含）；如领券后【0】天生效，有效期【当月】。用户2023年7月1日13:25:50领券：生效时间为“立即生效”，失效时间为2023年7月31日23:59:59（含）')}}
                </div>
                <div>
                  {{i18n('按月数：如领券后【2】天生效，有效期【按月数2月】。用户2023年7月1日13:25:50领券：生效时间为2023年7月3日13:25:50（含）失效时间为2023年9月30日23:59:59（含）；如领券后【0】天生效，有效期【按月数2月】。用户2023年7月1日13:25:50领券：生效时间为“立即生效”，失效时间为2023年9月30日23:59:59（含）')}}
                </div>
                <div>
                  {{i18n('按自然日：如领券后【2】天生效，有效期【按自然日5天】。用户2023年7月1日13:25:50领券：生效时间为2023年7月3日00:00:00（含），失效时间为2023年7月7日23:59:59（含）；如领券后【0】天生效，有效期【按自然日5天】用户2023年7月1日13:25:50领券：生效时间为“立即生效”，失效时间为2023年7月5日23:59:59（含）')}}
                </div>
              </div>
              <i slot="reference" class="iconfont  ic-info icon-tip" style="margin:-2px 4px 0 -4px"></i>
            </el-popover>
            <template v-if="['CUSTOM','NOW'].indexOf(ruleForm.selectModel) > -1">
              <el-select :placeholder="formatI18n('/公用/券模板', '请选择')" @change="doFormItemChange" v-model="ruleForm.expiryType"
                :disabled="!isDisabled('RALATIVE')" style="width: 130px">
                <el-option :label="i18n('按自然日')" value="NATURAL_DAY"></el-option>
                <el-option :label="formatI18n('/公用/券模板', '按天数')" value="DAYS"></el-option>
                <el-option :label="formatI18n('/公用/券模板', '当月')" value="CURRENT_MONTH"></el-option>
                <el-option :label="formatI18n('/公用/券模板', '按月数')" value="MONTHS"></el-option>
                <el-option :label="formatI18n('/公用/券模板', '永久有效')" value="FOREVER"></el-option>
              </el-select>
              <el-form-item prop="dateTo" style="display: inline-block" v-if="['DAYS','MONTHS','NATURAL_DAY'].indexOf(ruleForm.expiryType) > -1">
                <el-input @change="doCouponChange(1)" style="width: 100px" :disabled="!isDisabled('RALATIVE')" v-model="ruleForm.dateTo"
                  :placeholder="i18n('请输入')">
                  <template slot="append">{{getDateType}}</template>
                </el-input>
                <span class="warning-form" style="top:100%" v-if="isExceedDateLimit && ruleForm.dateType === 'RALATIVE'">
                  {{warnDataLimitText}}
                </span>
              </el-form-item>
            </template>
          </el-radio>
          <el-radio label="FIXED" class="date-radio">
            <span>{{i18n('固定时间段')}}</span>
            <div style="display:inline-block">
              <el-form-item prop="dateFix">
                <el-date-picker :disabled="!isDisabled('FIXED')" @change="doFormItemChange" :end-placeholder="formatI18n('/公用/券模板', '结束日期')"
                  :picker-options="dateRangeOption" format="yyyy-MM-dd HH:mm:ss" :range-separator="i18n('至')" size="small"
                  :start-placeholder="formatI18n('/公用/券模板', '开始日期')" type="datetimerange" v-model="ruleForm.dateFix"
                  value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00', '23:59:59']">
                </el-date-picker>
                <span class="warning-form" style="top:100%" v-if="isExceedDateLimit && ruleForm.dateType === 'FIXED'">
                  {{warnDataLimitText}}
                </span>
              </el-form-item>
            </div>
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 有些券需要固定单位：例如提货券、运费券 -->
      <el-form-item v-if="specialMode" :label="formatI18n('/公用/券模板', '券有效期')" class="cur-form-item" label-width="120px">
        <el-radio-group v-model="ruleForm.dateType" @change="doCouponValidateChange">
          <el-radio label="RALATIVE" style="display:block">
            {{i18n('用户领券后')}}
            <el-form-item class="cur-day" prop="dateFrom" style="display: inline-block">
              <el-input @change="doCouponChange(0)" :disabled="ruleForm.dateType !== 'RALATIVE'" style="width: 100px" v-model="ruleForm.dateFrom">
                <template slot="append">{{formatI18n('/公用/券模板', '天')}}</template>
              </el-input>
            </el-form-item>
            {{i18n('生效，有效期为')}}
            <el-form-item class="cur-day" prop="dateTo" style="display: inline-block">
              <el-input @change="doCouponChange(1)" :disabled="ruleForm.dateType !== 'RALATIVE'" style="width: 100px" v-model="ruleForm.dateTo">
                <template slot="append">{{formatI18n('/公用/券模板', '天')}}</template>
              </el-input>
              <span class="warning-form" style="top:100%" v-if="isExceedDateLimit && ruleForm.dateType === 'RALATIVE'">
                {{warnDataLimitText}}
              </span>
            </el-form-item>

          </el-radio>
          <el-radio label="FIXED" style="margin-top:15px">
            <el-form-item class="fix_content" prop="dateFix" style="display:inline-block">
              {{i18n('固定时间段')}}
              <el-date-picker @change="doDateFixChange" :end-placeholder="formatI18n('/公用/券模板', '结束日期')" :picker-options="dateRangeOption"
                format="yyyy-MM-dd HH:mm:ss" :disabled="ruleForm.dateType !== 'FIXED'" range-separator="-" size="small"
                :start-placeholder="formatI18n('/公用/券模板', '开始日期')" type="datetimerange" v-model="ruleForm.dateFix" value-format="yyyy-MM-dd HH:mm:ss"
                :default-time="['00:00:00', '23:59:59']">
              </el-date-picker>
              <span class="warning-form" style="top:100%" v-if="isExceedDateLimit && ruleForm.dateType === 'FIXED'">
                {{warnDataLimitText}}
              </span>
            </el-form-item>
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" src="./CouponEffectPeriod.ts">
</script>

<style lang="scss" scoped>
.cur-form-item {
  .el-form-item__label {
    &:before {
      content: "*";
      color: #ef393f;
      margin-right: 4px;
    }
  }
  .date-radio {
    margin-top: 20px;
  }
  ::v-deep.el-radio__input.is-checked + .el-radio__label {
    color: #242633;
  }
  .el-form-item {
    margin: 0 6px;
  }
  ::v-deep.el-input__inner {
    height: 32px !important;
  }
}
</style>