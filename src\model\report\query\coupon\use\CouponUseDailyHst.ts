/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2023-06-13 15:22:04
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\report\query\coupon\use\CouponUseDailyHst.ts
 * 记得注释
 */
import CostPartyInfo from "../costPartyInfos"

export default class CouponUseDailyHst {
  // 用券日期
  useDate: Nullable<Date> = null
  // 用券门店
  store: Nullable<string> = null
  // 券名称
  name: Nullable<string> = null
  // 用券数量
  useCount: Nullable<number> = null
  // 用券人数
  memberCount: Nullable<number> = null
  // 券抵用金额
  deductAmount: Nullable<number> = null
  // 交易金额
  amount: Nullable<number> = null
  // 活动代码
  activityNumber: Nullable<string> = null
  // 活动名称
  activityName: Nullable<string> = null
  // 券模板号
  templateNumber: Nullable<string> = null
  // 承担方信息
  costPartyInfos: CostPartyInfo[] = []
}