import IdName from 'model/common/IdName'
import ImprestCardSaleBillLine from 'model/card/salebill/ImprestCardSaleBillLine'
import TemplateValidity from 'model/card/template/TemplateValidity'

export default class ImprestCardSaleBill {
  // 单号
  billNumber: Nullable<string> = null
  // 卡模板
  cardTemplate: Nullable<IdName> = new IdName()
  // 卡样图片
  cardPictureUrl: Nullable<string> = null
  // 制售明细
  lines: ImprestCardSaleBillLine[] = []
  // 有效期
  validityInfo: Nullable<TemplateValidity> = null
  // 备注
  remark: Nullable<string> = null
  // 制售时间
  saleTime: Nullable<Date> = null
  // 创建人
  creator: Nullable<string> = null
  // 卡类型
  cardType: Nullable<string> = null
}