<template>
  <div class="group-mutex-template">
    <el-form-item :label="i18n('叠加促销')" required>
      <el-form :model="ruleForm" :rules="rules" ref="StackPromotion">
        <el-form-item prop="promotionState">
          <div>
            <span class="gray-tips">{{i18n('指参与前台促销后，能否使用当前预付卡')}}</span>
          </div>
          <el-radio-group v-model="ruleForm.promotionState" @input="doChange">
            <el-radio label="1">{{ i18n('全部促销活动叠加') }}</el-radio>
            <el-radio label="2">{{ i18n('全部促销活动不叠加') }}</el-radio>
            <el-radio label="3">
              <span style="margin-right: 8px;">{{ i18n('指定促销活动叠加') }}</span>
              <span v-if="ruleForm.promotionState == '3' && ruleForm.promotionList.length == 0" style="color: #1597FF" @click.stop="doSelect">
                {{ i18n('选择促销单') }}
              </span>
              <span v-if="ruleForm.promotionState == '3' && ruleForm.promotionList.length > 0">{{ i18n('已选择') }}
                <span class="number-text">{{ ruleForm.promotionList.length }}</span>
                {{ i18n('个促销单') }}
                <span class="span-btn" @click.stop="doSelect" style="font-size:13px">
                  {{i18n('修改')}}
                </span>
              </span>
            </el-radio>
            <el-radio label="4">
              <span style="margin-right: 8px;">{{ i18n('指定促销活动不叠加') }}</span>
              <span v-if="ruleForm.promotionState == '4' && ruleForm.promotionList.length == 0" style="color: #1597FF"
                @click.stop="doSelect">{{ i18n('选择促销单') }}</span>
              <span v-if="ruleForm.promotionState == '4' && ruleForm.promotionList.length > 0">{{ i18n('已选择') }}
                <span class="number-text">{{ ruleForm.promotionList.length }}</span>
                {{ i18n('个促销单') }}
                <span class="span-btn" @click.stop="doSelect" style="font-size:13px">
                  {{i18n('修改')}}
                </span>
              </span>
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </el-form-item>
    <PromotionSelectorDialog :dialogShow.sync="promotionDialogShow" @dialogClose="promotionDialogShow = false" :promotionList="promotionList"
      @save="doSave">
    </PromotionSelectorDialog>
  </div>
</template>
<script lang="ts">
import { Vue, Component, Prop, Watch } from "vue-property-decorator";
import PromotionSelectorDialog from "cmp/coupontenplate/cmp/PromotionSelectorDialog.vue";
import I18nPage from "common/I18nDecorator";
import BCouponTemplatePromotion from "model/common/BCouponTemplatePromotion";
import EditType from "common/EditType";
import CardTemplateApi from "http/card/template/CardTemplateApi";

//接口需要的参数模型
class ParamsObj {
  // 是否参与促销叠加，true表示叠加优惠，false表示不可参与叠加
  excludePromotion: Nullable<boolean> = null;
  // 促销叠加类型 ALL("全部"),PART("部分")
  promotionSuperpositionType: Nullable<"ALL" | "PART"> = null;
  // 促销单信息
  promotion: Nullable<BCouponTemplatePromotion> = null;
}

@Component({
  name: "StackCardPromotion",
  components: { PromotionSelectorDialog },
})
@I18nPage({
  prefix: ["/公用/券模板", "/公用/券模板详情"],
  auto: true,
})
export default class StackCardPromotion extends Vue {
  @Prop() value: any; //v-model绑定的值
  @Prop({ type: String, default: "" }) templateId: string; //编辑的时候，当前卡模板号
  @Prop({ type: String, default: EditType.CREATE }) editType: EditType;
  $refs: any;
  ruleForm: any = {
    promotionState: "2",
    promotionList: [],
  };
  rules: any;
  promotionShowDialogShow: boolean = false;
  promotionDialogShow: boolean = false;
  isFirstSearch: boolean = true

  @Watch("templateId")
  onTemplateIdChange(value: any) {
    console.log('模板id变了',this.templateId);
    if (this.templateId && this.isFirstSearch) {
      this.queryPromotion(this.templateId);
      this.isFirstSearch = false
    }
    this.validate();
  }

  //在父组件中回绑接口数据时，主动调用该方法
  doBindValue(value: ParamsObj) {
    if (value) {
      if (value.excludePromotion) {
        //参与叠加
        if (value.promotionSuperpositionType == "PART") {
          //部分参与
          this.ruleForm.promotionState = "3";
        } else {
          //全部参与
          //默认勾选“全部促销活动叠加”
          this.ruleForm.promotionState = "1";
        }
      } else {
        //不参与叠加
        if (value.promotionSuperpositionType == "PART") {
          this.ruleForm.promotionState = "4";
        } else {
          this.ruleForm.promotionState = "2";
        }
      }
    }
  }

  get promotionList() {
    return this.ruleForm.promotionList;
  }
  created() {
    this.initRules();
  }
  doSelect() {
    this.promotionDialogShow = true;
  }
  doChange() {
    this.ruleForm.promotionList = [];
    this.validate();
    this.$emit("input", this.doParams());
    this.$emit("change");
  }
  doSave(selectedList: any) {
    this.ruleForm.promotionList = selectedList;
    this.promotionDialogShow = false;
    this.validate();
    this.$emit("input", this.doParams());
    this.$emit("change");
  }
  validate() {
    this.$emit("input", this.doParams());
    this.$emit("change");
    return this.$refs.StackPromotion?.validate();
  }
  doParams() {
    let object: ParamsObj = {
      excludePromotion: this.ruleForm.promotionState == "1" || this.ruleForm.promotionState == "3",
      promotionSuperpositionType: this.ruleForm.promotionState == "1" || this.ruleForm.promotionState == "2" ? "ALL" : "PART",
      promotion: {
        templateNumber: this.templateId,
        proNums: this.ruleForm.promotionList,
        append: false,
      },
    };
    console.log('促销单模板id',object.promotion?.templateNumber, this.templateId);
    return object;
  }
  //编辑、复制时，根据已有的卡模板号查询对应的促销单
  async queryPromotion(templateId: string) {
    try {
      const resp: any = await CardTemplateApi.getCardTemplatePromotion(templateId);
      if (resp.code == 2000 || resp.data) {
        this.ruleForm.promotionList = resp.data || [];
        this.validate();
      } else {
        this.$message.error(resp.msg);
      }
    } catch (err) {
      this.$message.error((err as any).message);
    }
  }

  initRules() {
    this.rules = {
      promotionState: {
        validator: (rule: any, value: string, callback: any) => {
          if ((this.ruleForm.promotionState == "3" || this.ruleForm.promotionState == "4") && this.ruleForm.promotionList.length == 0) {
            callback(new Error(this.i18n("请选择促销单")));
          }
          callback();
        },
        trigger: ["change", "blur"],
      },
    };
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-radio__input.is-checked + .el-radio__label {
  color: #24272b !important;
}
</style>