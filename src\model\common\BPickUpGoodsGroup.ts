/*
 * @Author: 黎钰龙
 * @Date: 2023-09-05 17:58:01
 * @LastEditTime: 2023-09-14 16:02:37
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\common\BPickUpGoodsGroup.ts
 * 记得注释
 */
import PickUpGoods from 'model/common/PickUpGoods'

export default class BPickUpGoodsGroup {
  /**
 * 类型
 * single——单品，category——按品类，brandAndCategory——按品牌、品类
 */
  type: Nullable<string> = null
  // 兑换商品数组
  exchangeGoods: PickUpGoods[] = []
  // 兑换数量，默认为1
  exchangeQty: Nullable<number> = null
  // 商品组序号
  sequence: Nullable<number> = null
  // 是否允许兑换相同商品
  enableExchangeSameGood: Nullable<boolean> = false
}