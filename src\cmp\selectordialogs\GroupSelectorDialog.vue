<!--
 * @Author: 申鹏渤
 * @Date: 2023-11-29 14:05:04
 * @LastEditTime: 2023-12-25 17:35:16
 * @LastEditors: 申鹏渤
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\selectordialogs\GroupSelectorDialog.vue
 * 记得注释
-->
<template>
  <el-dialog :title="title" class="select-group-dialog" append-to-body :close-on-click-modal="false" :visible.sync="dialogShow"
    :before-close="handleClose">
    <div class="tips">
      {{ formatI18n('/营销/券礼包活动/群发券新建界面', '支持选择多个标签，如果选择多个，则会员只需要满足其中一个标签即可') }}
    </div>
    <div class="wrap">
      <el-row>
        <el-col :span="17" class="left">
          <el-row class="top">
            <el-col :span="10">
              <div class="option">
                <span class="option-type">
                  {{ formatI18n('/营销/券礼包活动/群发券新建界面', '标签类型') }}
                </span>
                <el-select :placeholder="formatI18n('/公用/下拉框/提示', '请选择')" style="width: 250px; margin-right: 20px" clearable v-model="selectType"
                  @change="onTypeChange">
                  <el-option :label="formatI18n('/营销/券礼包活动/群发券新建界面', '多选项')" value="checkbox">
                    {{ formatI18n('/营销/券礼包活动/群发券新建界面', '多选项') }}
                  </el-option>
                  <el-option :label="formatI18n('/营销/券礼包活动/群发券新建界面', '单选项')" value="singleChoice">
                    {{ formatI18n('/营销/券礼包活动/群发券新建界面', '单选项') }}
                  </el-option>
                  <el-option :label="formatI18n('/营销/券礼包活动/群发券新建界面', '文本')" value="text">
                    {{ formatI18n('/营销/券礼包活动/群发券新建界面', '文本') }}
                  </el-option>
                </el-select>
              </div>
            </el-col>
            <el-col :span="12" class="search">
              <el-input :placeholder="formatI18n('/营销/券礼包活动/群发券新建界面', '请输入标签名称')" v-model="tagName" style="width: 250px; margin-right: 20px" />
              <el-button type="primary" @click="doSearch()" style="height: 32px; width: 80px">{{ formatI18n('/公用/菜单/搜索') }}</el-button>
              <el-button @click="doReset()" style="height: 32px; width: 80px">{{ formatI18n('/公用/按钮/重置') }}</el-button>
            </el-col>
          </el-row>
          <div class="body">
            <div class="check-group" v-for="item in checkGroup.group">
              <div class="title">{{ item.name }}</div>
              <el-checkbox-group v-model="item.checkList" @change="handleCheckGroupChange(item.id, item.name, $event)" class="checkbox">
                <!-- <el-tooltip effect="dark" content="11111" placement="top"> -->
                <div v-for="box in item.tagValues" :key="box">
                  <el-tooltip effect="dark" :content="box" placement="top">
                    <el-checkbox :label="box">
                      {{ box }}
                    </el-checkbox>
                  </el-tooltip>
                </div>
                <!-- </el-tooltip> -->
              </el-checkbox-group>
            </div>
          </div>
        </el-col>
        <el-col :span="7" class="right">
          <el-row class="right-table">
            <el-row class="thead">
              <span>
                <i18n k="/营销/券礼包活动/群发券新建界面/已选中{0}项">
                  <template slot="0">&nbsp;{{ checkedNum }}&nbsp;</template>
                </i18n>
              </span>
              <div class="empty"></div>
              <el-button type="text" style="line-height: 24px" @click="doClearAll">{{ formatI18n('/公用/导入/清空') }}</el-button>
            </el-row>
            <el-row class="tbody">
              <el-row v-if="selected && selected.length > 0 && checkedNum > 0" class="trow" style=" color: #909399">
                <div class="select-tag" v-for="item in selected" :key="item.name">
                  <div class="tagtitle">{{ item.name }}</div>
                  <!-- 此处用了grid布局，如果有更好的后续再改吧，flex和常规布局实现有点困难 -->
                  <div class="tagbody">
                    <div class="groups" v-for="group in item.tagValues">
                      <el-tooltip class="item" effect="dark" :content="group" placement="top">
                        <div class="text">{{group}}</div>
                      </el-tooltip>
                      <div class="icon" @click="delItems(group, item.name)">
                        <div></div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-row>
              <el-row v-if="!selected || selected.length === 0 || checkedNum === 0" class="trow2" style="text-align: center;color: #909399">
                {{ formatI18n("/公用/提示/暂无数据") }}
              </el-row>
            </el-row>
          </el-row>
        </el-col>
      </el-row>
    </div>

    <div slot="footer" class="dialog-footer" style="position: relative;top: -27px;">
      <el-button style="height: 32px; width: 80px" @click="doCancel()">{{ formatI18n('/公用/按钮/取消') }}</el-button>
      <el-button style="height: 32px; width: 80px" type="primary" @click="doModalClose()">{{ formatI18n('/公用/按钮/确定') }}</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./GroupSelectorDialog.ts"/>

<style lang="scss" scoped>
.select-group-dialog {
  display: flex;
  align-items: center;
  justify-content: center;
  @import "SelectorDialogCommon";
  .tips {
    padding-left: 20px;
    width: 100%;
    height: 36px;
    line-height: 36px;
    background: #eaf3ff;
    border-radius: 2px;
    border: 1px solid #318bff;
    font-weight: 400;
    color: #36445a;
  }
  .wrap {
    width: 100%;
    height: 500px;
    margin-top: 20px;
    .left {
      height: 500px;
      overflow: auto;
      .option {
        display: flex;
      }
      .option-type {
        width: 100px;
        text-align: start;
        font-size: 12px;
        line-height: 32px;
      }
      .search {
        display: flex;
        justify-content: space-around;
      }
      .body {
        margin-top: 30px;
        .check-group {
          margin: 20px 0;
          ::v-deep .el-checkbox__input {
            vertical-align: top;
          }
          ::v-deep .el-checkbox__label {
            margin-top: 0px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            width: 80px;
          }
          .checkbox {
            display: flex;
            flex-wrap: wrap;
          }
        }
        .title {
          margin-bottom: 10px;
          height: 20px;
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          line-height: 20px;
        }
      }
    }
    .right {
      height: 500px;
      overflow: auto;
      padding-left: 15px;
      border-left: 2px solid #dde2eb;
      .right-table {
        border: none;
        height: 500px;
      }
      .thead {
        background-color: #fff;
        display: flex;
        justify-content: space-between;
        .empty {
          flex: 1;
        }
      }
      .tbody {
        .trow {
          height: 35px;
          line-height: 34px;
          &:hover {
            background-color: #fff;
          }
        }
        .trow2 {
          height: 35px;
          line-height: 34px;
          &:hover {
            background-color: #f8f9fc;
          }
        }
        .tagtitle {
          text-align: start;
        }
        .tagbody {
          display: grid;
          grid-template-columns: repeat(2, 124px);
          /*  声明行间距和列间距  */
          grid-gap: 10px;
          /*  声明行的高度  */
          grid-auto-rows: 32px;
          .groups {
            display: flex;
            align-items: center;
            color: #020203;
            font-size: 12px;
            padding-left: 12px;
            background-color: #f2f4f8;
            line-height: 32px;
            .text {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              width: 80px;
            }
            .icon {
              display: flex;
              justify-content: center;
              align-items: center;
              width: 18px;
              height: 18px;
              border-radius: 18px;
              background-color: #fff;
              &:hover {
                cursor: pointer;
              }
              div {
                width: 9px;
                height: 2px;
                background-color: #1597ff;
              }
            }
          }
        }
      }
    }
  }
}
</style>
<style>
.short-label .el-form-item__label {
  width: 100px !important;
}
.select-group-dialog .el-dialog {
  width: 1024px !important;
  height: 700px !important;
  margin-top: 0 !important;
}
</style>
