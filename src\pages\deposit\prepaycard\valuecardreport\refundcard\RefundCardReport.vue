<template>
  <div class="sales-cards-report">
    <div class="current-page">
      <el-form label-width="140px">
        <el-row class="query">
          <TimeRange no-i18n @submit="handleTimeRange" ref="timeRange"></TimeRange>
        </el-row>
        <el-row class="query" style="margin-top:8px">
          <el-col :span="8">
            <form-item label="卡模板">
              <el-input placeholder="等于" v-model="query.templateEquals" />
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item label="卡号">
              <el-input placeholder="等于" v-model="query.codeEquals" />
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item label="交易号">
              <el-input placeholder="等于" v-model="query.transNoEquals" />
            </form-item>
          </el-col>
        </el-row>
        <el-row class="query" style="margin-top:8px" v-show="expandQuery">
          <el-col :span="8">
            <form-item label="卡面额">
              <el-input style="width: calc(50% - 7px)" v-model="query.faceAmountGreaterOrEquals" />-
              <el-input style="width: calc(50% - 6px)" v-model="query.faceAmountLessOrEquals" />
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item label="发生组织">
              <SelectStores v-model="query.orgIdEquals" :isOnlyId="true" :hideAll="false" width="100%" :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
              </SelectStores>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水/发生区域')">
              <el-select placeholder="不限" v-model="query.zoneIdEquals">
                <el-option :label="formatI18n('/公用/查询条件/下拉列表/不限')" :value="null">{{ formatI18n('/公用/查询条件/下拉列表/不限') }}</el-option>
                <el-option :key="item.zone.id" :label="'['+item.zone.id+']'+item.zone.name" :value="item.zone.id"
                           v-for="item in areaData">[{{item.zone.id}}]{{item.zone.name}}</el-option>
              </el-select>
            </form-item>
          </el-col>
        </el-row>
        <el-row class="query" style="margin-top:8px" v-show="expandQuery">
          <el-col :span="8">
            <form-item :label="formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水/发生渠道')">
              <el-select v-model="channelEquals" :placeholder="formatI18n('/资料/渠道/请选择')" clearable filterable>
                <el-option v-for="(item,index) in channelTypes" :key="index" :value="getKey(item.channel)" :label="item.name">
                  <!-- <span style="float: left">{{ item.label }}</span> -->
                </el-option>
              </el-select>
            </form-item>
          </el-col>
        </el-row>
        <el-row class="query" style="margin-top: 8px">
          <el-col :span="8">
            <form-item label=" ">
              <el-button class="btn-search" type="primary" @click="doSearch">查询</el-button>
              <el-button class="btn-reset" @click="doReset">重置</el-button>
              <el-button type="text" @click="expandQuery=!expandQuery">
                <span v-if="!expandQuery" key="1">
                  <i class="el-icon-arrow-down"></i>
                  {{formatI18n('/公用/查询条件/展开')}}
                </span>
                <span v-if="expandQuery" key="1">
                  <i class="el-icon-arrow-up"></i>
                  {{formatI18n('/公用/查询条件/收起')}}
                </span>
              </el-button>
            </form-item>
          </el-col>
        </el-row>
      </el-form>
      <hr />
<!--      <el-row style="line-height: 35px" v-if="isShowSum">-->
<!--        <i class="el-icon-warning" />-->
<!--        <i18n k="/储值/预付卡/储值卡报表/售卡流水/共售出{0}张储值卡，销售额为{1}元">-->
<!--          <template slot="0">-->
<!--            &nbsp;-->
<!--            <span style="color: red">{{sum.qty}}</span>&nbsp;-->
<!--          </template>-->
<!--          <template slot="1">-->
<!--            &nbsp;-->
<!--            <span style="color: red">{{dataUtil.showTotalAmount(sum.totalAmount)}}</span>&nbsp;-->
<!--          </template>-->
<!--        </i18n>-->
<!--      </el-row>-->
      <el-row class="table">
        <el-table :data="queryData" border @header-dragend="tableDragend" style="width: 100%;margin-top: 20px">
          <el-table-column label="卡号" prop="code" :width="getColumnWidth('code', 250)"></el-table-column>
          <el-table-column label="退卡时间" prop="occurredTime" :width="getColumnWidth('occurredTime', 140)">
            <template slot-scope="scope">
              <span no-i18n>{{dataUtil.format(scope.row.occurredTime, 'yyyy-MM-dd HH:mm:ss')}}</span>
            </template>
          </el-table-column>
          <el-table-column label="发生组织" prop="occurredOrg" :width="getColumnWidth('occurredOrg', 200)">
            <template slot-scope="scope">
              <span no-i18n :title="dataUtil.showIdName(scope.row.occurredOrg)">{{dataUtil.showIdName(scope.row.occurredOrg)}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水/发生区域')" v-if="isMoreMarketing" prop="zone" :width="getColumnWidth('zone', 200)">
            <template slot-scope="scope">
              <div v-if="scope.row.zone!==null && scope.row.zone!==''">
                <el-tooltip class="item" effect="light" placement="right-end">
                  <div> {{'['+scope.row.zone.id+']'}}{{scope.row.zone.name}}</div>
                  <div slot="content">
                    {{'['+scope.row.zone.id+']'}}{{scope.row.zone.name}}
                  </div>
                </el-tooltip>
              </div>
              <div v-else>-</div>
            </template>
          </el-table-column>
          <el-table-column :label="formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水/发生渠道')" prop="channelName" :width="getColumnWidth('channelName', 200)">
            <template slot-scope="scope">
              <span no-i18n>{{ scope.row.channelName | nullable }}</span>
            </template>
          </el-table-column>
          <el-table-column label="卡面额(元)" prop="faceAmount" align="right" :width="getColumnWidth('faceAmount', 140)">
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.faceAmount.toFixed(2)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="退卡本金(元)" prop="refundDepositAmount" align="right" :width="getColumnWidth('refundDepositAmount', 100)">
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.refundDepositAmount ? scope.row.refundDepositAmount.toFixed(2) : 0}}</span>
            </template>
          </el-table-column>
          <el-table-column label="退卡赠金(元)" prop="refundGiftAmount" align="right" :width="getColumnWidth('refundGiftAmount', 100)">
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.refundGiftAmount ? scope.row.refundGiftAmount.toFixed(2) : 0}}</span>
            </template>
          </el-table-column>
          <el-table-column label="退卡金额(元)" prop="refundAmount" align="right" :width="getColumnWidth('refundAmount', 140)">
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.refundAmount ? scope.row.refundAmount.toFixed(2) : 0}}</span>
            </template>
          </el-table-column>
          <el-table-column label="交易号" prop="transNo" :width="getColumnWidth('transNo')">
            <template slot-scope="scope">
              <span no-i18n :title="scope.row.transNo">{{scope.row.transNo}}</span>
            </template>
          </el-table-column>
          <el-table-column label="卡模板" prop="cardTemplate" :width="getColumnWidth('cardTemplate',150)">
            <template slot-scope="scope">
              <el-button no-i18n type="text" @click="gotoTplDtl(scope.row.templateNumber)">{{scope.row.templateName}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="卡模板号" prop="cardTemplateNumber" :width="getColumnWidth('cardTemplateNumber', 100)">
            <template slot="header">
              <span title="卡模板号">卡模板号</span>
            </template>
            <template slot-scope="scope">
              <span no-i18n :title="scope.row.templateNumber">{{scope.row.templateNumber}}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-pagination no-i18n :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
        @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background :layout="getPageLayout('total, prev, pager, next, sizes, jumper',page.probeEnabled)"
        class="pagin"></el-pagination>
    </div>
  </div>
</template>

<script lang="ts" src="./RefundCardReport.ts">
</script>

<style lang="scss">
.sales-cards-report {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: auto;
  .total {
    margin: 20px;
  }

  div.el-range-input {
    flex: 1;
  }

  .current-page {
    height: calc(100% - 150px);
    padding: 0 20px 20px 20px;
    .el-select {
      width: 100%;
    }

    .query {
      .el-form-item {
        margin-bottom: 0;
      }
    }

    .pagin {
      margin-top: 25px;
    }

    .el-col {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    tbody {
      .cell {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
</style>
