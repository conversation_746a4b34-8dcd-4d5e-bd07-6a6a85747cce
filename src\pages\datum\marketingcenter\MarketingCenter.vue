<template>
  <div class="marketing-center-view">
    <BreadCrume :panelArray="panelArray">
    </BreadCrume>
    <div class="content">
      <div class="left">
        <ListWrapper style="height: 100%" :show-query="false" :showQuery="false">
          <template slot="list">
            <FloatBlock refClass="current-page" :top="95" style="padding: 10px 10px;">
              <template slot="ctx">
                <el-row>
                  <el-col :span="15">
                    <form-item>
                      <el-input :placeholder="formatI18n('/资料/营销中心/搜索营销中心代码\\/名称')" @change="onSearch" v-model="query.idNameLikes"
                        suffix-icon="el-icon-search" style="width: 300px" />
                    </form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-button v-if="hasOptionPermission('/设置/资料/营销中心', '资料维护')" @click="showCreateDialog" type="primary">
                      {{formatI18n('/资料/营销中心/新建营销中心')}}</el-button>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12" style="line-height: 36px">
                    {{formatI18n('/资料/门店/共')}}
                    <span class="number-text">{{page.total}}</span>
                    {{formatI18n('/资料/营销中心/个营销中心')}}
                  </el-col>
                </el-row>
              </template>
            </FloatBlock>
            <el-table :data="queryData" highlight-current-row @current-change="handleCurrentChange" ref="leftTable">
              <el-table-column fixed :label="formatI18n('/资料/营销中心/营销中心代码')" prop="marketingCenter.id" />
              <el-table-column fixed :label="formatI18n('/资料/营销中心/营销中心名称')" prop="marketingCenter.name" />
              <el-table-column fixed :label="formatI18n('/资料/营销中心/总部')" prop="headquarters">
                <template slot-scope="scope">
                  <span v-if="scope.row.headquarters">{{formatI18n("/公用/券模板/是")}}</span>
                  <span v-else>{{ formatI18n("/公用/券模板/否") }}</span>
                </template>
              </el-table-column>
              <el-table-column fixed :label="formatI18n('/资料/门店/操作')" v-if="hasOptionPermission('/设置/资料/营销中心', '资料维护')">
                <template slot-scope="scope">
                  <el-button type="text" @click="showModifyDialog(scope.row)">{{formatI18n('/资料/门店/修改')}}</el-button>
                </template>
              </el-table-column>
            </el-table>
          </template>
          <!--{{formatI18n('/资料/门店/分页栏')}}-->
          <template slot="page">
            <el-pagination :current-page="page.currentPage" :page-size="page.size" :total="page.total" @current-change="onHandleCurrentChange"
              @size-change="onHandleSizeChange" background layout="total, prev, pager, next,  jumper">
            </el-pagination>
          </template>
        </ListWrapper>
      </div>
      <div class="right">
        <ListWrapper style="height: 100%;" :show-query="false" :showQuery="false">
          <template slot="list">
            <el-row>
              <el-col style="text-align: left">
                <i18n k="/资料/营销中心/{0}的门店（共{1}家）" style="text-align: left">
                  <template slot="0">
                    <span v-if="selectingMarketingCenter.marketingCenter.name"
                      style="font-weight: 500;">{{selectingMarketingCenter.marketingCenter.name}}</span>
                    <span v-else style="font-weight: 500;">{{formatI18n("/资料/门店/所有")}}</span>
                  </template>
                  <template slot="1">
                    <span style="font-weight: 500;">{{orgPage.total}}</span>
                  </template>
                </i18n>
              </el-col>
              <el-col style="text-align: right">
                <el-button @click="importOrg" v-if="hasOptionPermission('/设置/资料/营销中心', '资料维护')">
                  {{formatI18n('/资料/门店/批量导入')}}
                </el-button>
                <el-button type="primary" @click="showSelectOrgDialog" v-if="hasOptionPermission('/设置/资料/营销中心', '资料维护')">{{formatI18n('/资料/门店/选择添加')}}
                </el-button>
              </el-col>
            </el-row>
            <FloatBlock refClass="current-page" :top="95" style="padding: 10px 10px;">
              <template slot="ctx">
                <el-row>
                  <el-col :span="12" style="line-height: 36px" v-if="!enableMultiMarketingCenter">
                    {{formatI18n('/资料/门店/共')}} <span class="number-text">{{page.total}}</span>
                    {{formatI18n('/资料/门店/家门店')}}
                  </el-col>
                  <el-col style="line-height: 36px" v-else>
                    <el-checkbox :disabled="loading" v-model="checkedAll" style="padding-left: 10px;" @change="checkedAllRow" />
                    <i18n k="/公用/券模板/已选择{0}家门店">
                      <template slot="0">
                        <span class="number-text">{{selected.length}}</span>
                      </template>
                    </i18n>&nbsp;&nbsp;
                    <el-button @click="showUpdateCenterDialog" v-if="hasOptionPermission('/设置/资料/营销中心', '资料维护')">
                      {{formatI18n('/资料/门店/修改所属营销中心')}}
                    </el-button>
                  </el-col>
                </el-row>
              </template>
            </FloatBlock>
            <div class="table-warpper">
              <el-table v-loading="loading" height="450px" ref="table" :data="queryOrgData" style="width: 100%;margin-top: 10px;"
                @selection-change="handleSelectionChange">
                <el-table-column v-if="isEnableMultiMarketingCenter" type="selection" width="55"></el-table-column>
                <el-table-column fixed :label="formatI18n('/资料/门店/门店代码')" prop="org.id" />
                <el-table-column fixed :label="formatI18n('/资料/门店/门店名称')" prop="org.name" />
              </el-table>
            </div>
          </template>
          <!--{{formatI18n('/资料/门店/分页栏')}}-->
          <template slot="page">
            <el-pagination :current-page="orgPage.currentPage" :page-size="orgPage.size" :page-sizes="[10, 20, 30, 40]" :total="orgPage.total"
              @current-change="onHandleCurrentChangeForOrg" @size-change="onHandleSizeChangeForOrg" background
              layout="total, prev, pager, next, sizes,  jumper">
            </el-pagination>
          </template>
        </ListWrapper>
      </div>
    </div>
    <el-dialog :title="formatI18n('/资料/门店/修改')" :visible.sync="modifyDialogVisible" class="store-dialog-center" width="30%">
      <div style="margin: 20px;">
        <el-row>
          <el-col :span="8" style="height: 50px; line-height: 30px">{{formatI18n('/资料/营销中心/营销中心代码')}}</el-col>
          <el-col :span="16">
            <el-input :disabled="true" v-model="updateIns.marketingCenter.id" />
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8" style="height: 50px; line-height: 30px">{{formatI18n('/资料/营销中心/营销中心名称')}}</el-col>
          <el-col :span="16">
            <el-input v-model="updateIns.marketingCenter.name" />
          </el-col>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="modifyDialogVisible = false">{{formatI18n('/资料/门店/取 消')}}</el-button>
        <el-button type="primary" @click="modify" :loading="modifyLoading">{{formatI18n('/资料/门店/确 定')}}</el-button>
      </span>
    </el-dialog>
    <el-dialog :title="formatI18n('/资料/营销中心/新建营销中心')" :visible.sync="createDialogVisible" class="store-dialog-center" @close="resetForm" width="30%">
      <div style="margin: 10px;line-height: 40px" v-if="hasOptionPermission('/设置/资料/营销中心', '资料维护')">
        <div
          style="margin: 10px 10px;border: 1px solid rgb(213, 234, 251);background-color: rgb(234, 245, 253);padding: 5px;color: #495060;line-height: 23px;width: 100%">
          <span style="position: relative;top: 5px;"><img src="~assets/image/coupon/info3.png"></span>
          <i18n k="/资料/营销中心/许可证允许的营销中心数量上限为{0}个，还可以新增{1}个。">
            <template slot="0">&nbsp;<span style="color: blue">{{limitOfMarketingCenter}}</span>&nbsp;</template>
            <template slot="1">&nbsp;<span style="color: blue">{{limitOfMarketingCenter-page.total}}</span>&nbsp;</template>
          </i18n>
        </div>
        <el-row>
          <el-col :span="2" style="min-width: 100px">{{formatI18n('/资料/营销中心/营销中心代码')}}</el-col>
          <el-col :span="6">
            <el-input :placeholder="formatI18n('/资料/券承担方/请输入营销中心拼音或英文名')" v-model="newIns.marketingCenter.id" style="width: 280px" />
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="2" style="min-width: 100px">{{formatI18n('/资料/营销中心/营销中心名称')}}</el-col>
          <el-col :span="6">
            <el-input v-model="newIns.marketingCenter.name" style="width: 280px" />
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="2" style="min-width: 100px">&nbsp;</el-col>
          <el-col :span="18">
            <i class="el-icon-warning" />
            <span>{{formatI18n('/资料/营销中心/代码不允许与已有营销中心重复')}}。</span>
          </el-col>
        </el-row>
        <el-row>
          <el-col style="text-align: right">
            <el-button type="normal" @click="clear('createMarketingCenter')">{{formatI18n('/资料/门店/清空')}}</el-button>
            <el-button @click="add(false)" type="primary">{{formatI18n('/资料/营销中心/保存并继续新建')}}</el-button>
            <el-button @click="add(true)" type="primary">{{formatI18n('/资料/营销中心/保存并关闭')}}</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
    <el-dialog :title="formatI18n('/资料/门店/修改所属营销中心')" :visible.sync="updateCenterDialogVisible" class="store-dialog-center" width="30%"
      @close="clear('modifyOrg')">
      <div style="margin: 20px;">
        <el-row>
          <el-col :span="7" style="height: 50px; line-height: 30px">{{formatI18n('/资料/门店/所属营销中心')}}</el-col>
          <el-col :span="10">
            <el-select v-model="marketingCenter.id" :placeholder="formatI18n('/资料/渠道/请选择')" style="width: 200px">
              <el-option v-for="(value,index) in marketingCentersForOrg" :key="index" :value="value.id" :label="'['+value.id+']'+value.name">
                [{{value.id}}]{{value.name}}</el-option>
            </el-select>
          </el-col>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click=cancelUpdateCenter>{{formatI18n('/资料/门店/取 消')}}</el-button>
        <el-button type="primary" @click="updateOrgsForMarketingCenter" :loading="modifyLoading">{{formatI18n('/资料/门店/确 定')}}</el-button>
      </span>
    </el-dialog>

    <UploadFileModal :target-marketing-center="selectingMarketingCenter.marketingCenter.id" :from-marketing-center="true" @getOrgList="getOrgList"
      ref="uploadFileModal"></UploadFileModal>
    <SelectStore :marketingCentersForChooseOrg="marketingCentersForChooseOrg" :filterMarketingCenter="filterMarketingCenter"
      :targetMarketingCenter="selectingMarketingCenter.marketingCenter" :marketingCenters="marketingCenters" @modifyMarketingCenter="doSubmitOrgs"
      @getOrgList="getOrgList" ref="selectStores"></SelectStore>

  </div>
</template>

<script lang="ts" src="./MarketingCenter.ts">
</script>

<style lang="scss">
.list-wrapper-query {
  margin: 0 !important;
}
.line-blank {
  height: 0 !important;
}

.marketing-center-view {
  height: 100%;
  width: 100%;

  .store-dialog-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .total {
    margin: 20px;
  }

  .content {
    width: 100%;
    height: calc(100% - 50px);
    overflow: hidden;
    display: flex;

    .el-pagination {
      .el-pagination__sizes {
        width: 100px !important;

        .el-select {
          width: 100px !important;

          .el-input {
            width: 100px !important;
          }
        }
      }
    }

    .left {
      width: 40%;
      padding: 15px;
      background-color: white;

      .list-wrapper {
        height: auto !important;
      }

      .list {
        .el-table {
          width: calc(100% - 20px);
          margin: 0 10px;
        }
      }

      .page {
        padding: 0 10px;
      }

      .el-select {
        width: 100%;
      }

      .el-col {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .right {
      height: 100%;
      width: 60%;
      flex: 1;
      background-color: white;
      margin-left: 5px;
      border-top-right-radius: 10px;
      border-bottom-right-radius: 10px;

      .list-wrapper {
        height: auto !important;
      }

      .table-warpper {
        height: 100%;
        overflow: auto;
      }
      .el-table {
        height: 100%;
      }

      .query {
      }

      .list {
        height: 100%;
        .el-table {
          height: 100%;
          overflow: auto;
          margin: 0 10px;
        }
      }

      .page {
        padding: 0 10px;
      }

      .el-select {
        width: 100%;
      }

      .el-col {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }

  .el-range-editor.el-input__inner {
    width: 100%;
  }
  .qf-form-item .qf-form-content {
    margin-left: 0 !important;
  }
}
</style>
