import ChannelSelect from 'cmp/channelselect/ChannelSelect';
import I18nPage from 'common/I18nDecorator';
import UserGroupV2Api from 'http/precisionmarketing/userGroup/UserGroupV2Api';
import TagTemplateV2Api from 'http/tagv2/TagTemplateV2Api';
import Channel from 'model/common/Channel';
import IdName from 'model/common/IdName';
import { TagTypeEnum } from 'model/common/TagTypeEnum';
import { MemberRangeType } from 'model/member/MemberRangeType';
import MemberTagOption from 'model/member/MemberTagOption';
import { OptionType } from 'model/member/OptionType';
import ParticipateMember from 'model/member/ParticipateMember';
import UserGroupOption from 'model/member/UserGroupOption';
import UserGroup from 'model/precisionmarketing/group/UserGroup';
import MemberRule from 'model/precisionmarketing/tag/tagrule/customize/member/MemberRule';
import TagTemplateFilterV2 from 'model/precisionmarketing/tagv2/TagTemplateFilterV2';
import TagTemplateV2 from 'model/precisionmarketing/tagv2/TagTemplateV2';
import UserGroupV2 from 'model/precisionmarketing/userGroup/UserGroupV2';
import UserGroupV2Filter from 'model/precisionmarketing/userGroup/UserGroupV2Filter';
import TagOption from 'model/tag/TagOption';
import TagDataDict from 'pages/member/insight/common/TagDataDict';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import MemberPropSelect from "./MemberPropSelect/MemberPropSelect.vue";
import MemberProp from 'model/precisionmarketing/tag/tagrule/customize/member/MemberProp';
import DateUtil from 'util/DateUtil';
import MemberBalancePromotionApi from 'http/payment/member/MemberBalancePromotionApi';
import RSGrade from 'model/common/RSGrade';
import BenefitCardTemplateApi from 'http/equityCard/BenefitCardTemplateApi';
import BenefitCardTemplateFilter from 'model/equityCard/default/BenefitCardTemplateFilter';
import { enefitCardTemplateStatus } from 'model/equityCard/default/enefitCardTemplateStatus';
import { enefitCardTemplateType } from 'model/equityCard/default/enefitCardTemplateType';
import BenefitCardTemplate from 'model/equityCard/BenefitCardTemplate';


class LabelFilterInfo {
  labelType: Nullable<Channel[]> = null  //来源渠道
  dataType: Nullable<TagTypeEnum> = null //数据类型
  inputValue: string = '' //输入搜索
}

class GroupFilterInfo {
  inputValue: string = '' //输入搜索
  groupList: string[] = [] //已选择的客群列表uuid集合
}

class FormLabel {
  fulfillType: 'one' | 'all' = 'one'  //满足条件：任一/全部
  filterInfo: LabelFilterInfo = new LabelFilterInfo() //筛选条件

}

class FormGroup {
  fulfillType: 'one' | 'all' = 'one'  //满足条件：任一/全部
  filterInfo: GroupFilterInfo = new GroupFilterInfo() //筛选条件
}

class TagValueItem extends TagTemplateV2 {
  tagValue: Nullable<(string | number | null)[]> = null //提供给v-model绑定的值
}

class TagItem extends TagValueItem {
  isExpand: boolean = false //是否展开
}

@Component({
  name: 'SelectGroupCmp',
  components: {
    ChannelSelect,
    MemberPropSelect
  }
})
@I18nPage({
  prefix: [
    '/会员/会员资料',
    '/公用/券模板',
    '/会员/选择人群'
  ],
  auto: true
})
export default class SelectGroupCmp extends Vue {
  @Prop() value: Nullable<ParticipateMember>;
  @Prop({ type: Boolean, default: false }) detail: boolean;
  @Prop({ type: Boolean, default: false }) hideAll: boolean; //是否隐藏全部人群按钮
  visible: boolean = false
  groupRange: MemberRangeType = MemberRangeType.ALL //人群范围类型
  tabSelect: 'label' | 'group' | 'member' = 'label'
  labelForm: FormLabel = new FormLabel()  //标签表单
  groupForm: FormGroup = new FormGroup()  //客群表单
  groupArr: UserGroupV2[] = []  //当前可选客群列表
  originGroupList: UserGroupV2[] = [] //客群总列表
  tagList: TagItem[] = [] //当前展示的标签列表
  originTagList: TagValueItem[] = [] //总标签列表
  loading: boolean = false
  memberRule: MemberRule = new MemberRule()
  attrInitLoadFlag: boolean = false //是否加载过会员属性前置数据
  memberStateMap = TagDataDict.getMemberStateMap()

  gradeList: RSGrade[] = []

  gradeListMap: Map<Nullable<string>, Nullable<string>> = new Map([])

  registerChannel = TagDataDict.getRegisterChannelMap()

  // 付费会员列表
  benefitCardTemplateList: BenefitCardTemplate[] = [];

  lstDateOptions = {
    lstSeven: this.i18n('/会员/洞察/公共/时间阶段/近7天'),
    lstThirty: this.i18n('/会员/洞察/公共/时间阶段/近30天'),
    lstThreeMonth: this.i18n('/会员/洞察/公共/时间阶段/近3个月'),
    lstHalfYear: this.i18n('/会员/洞察/公共/时间阶段/近半年'),
    lstYear: this.i18n('/会员/洞察/公共/时间阶段/近一年'),
    thisMonth: this.i18n('/会员/洞察/公共/时间阶段/本月'),
    prevMonth: this.i18n('/会员/洞察/公共/时间阶段/上月'),
    thisYear: this.i18n('/会员/洞察/公共/时间阶段/今年'),
  }

  typeMap = {
    store: this.i18n('/会员/洞察/公共/门店范围/单店'),
    marketingCenter: this.i18n('/会员/洞察/公共/门店范围/营销中心'),
    circle: this.i18n('/会员/洞察/公共/门店范围/商圈'),
    area: this.i18n('/会员/洞察/公共/门店范围/地区')
  }

  @Watch('value', { deep: true })
  bindWatch(val: Nullable<ParticipateMember>) {
    if (val?.memberTagOption || val?.userGroupOption || val?.memberRule?.props?.length) {
      this.queryAndInit()
    }
  }

  get existsProps() {
    return this.memberRule?.props.map((e) => e.prop + '')
  }

  // 类型tab
  get selectTabs() {
    return [
      {
        name: this.i18n('标签'),
        value: 'label'
      },
      {
        name: this.i18n('客群'),
        value: 'group'
      },
      {
        name: this.i18n('/会员/洞察/客群管理/新建页/条件抽屉/会员属性'),
        value: 'member'
      }
    ]
  }

  // dialog已选择总数量
  get totalSelectNum() {
    return this.groupForm.filterInfo.groupList.length + this.selectTagList.length + (this.memberRule?.props?.length ? this.memberRule.props.length : 0)
  }

  // dialog已选择的客群列表（完整item），无法展示总客群列表不存在的客群
  get selectGroupList() {
    return this.groupForm.filterInfo.groupList.map((uuid: string) => {
      return this.originGroupList.find(item => item.uuid === uuid)
    }).filter((item) => item)
  }

  // dialog是否选择了标签数据
  get haveSelectTag() {
    return this.originTagList.some((item) => {
      return (!Array.isArray(item.tagValue) && item.tagValue) || (item.tagValue?.length && item.tagValue.some((item) => item))
    })
  }

  // dialog是否选择了会员属性
  get haveSelectMember() {
    return this.memberRule?.props?.length > 0
  }

  // dialog已选择的标签数据 右侧展示
  get selectTagList() {
    return this.originTagList.filter(item => {
      if (Array.isArray(item.tagValue) && item.tagValue?.length) {
        return item.tagValue[0] !== null || item.tagValue[1] !== null
      } else {
        return Array.isArray(item.tagValue) ? false : item.tagValue
      }
    })
  }

  // 是否存在非成对的数值类型标签
  get hasNotDoubleValue() {
    return this.originTagList.find((item) => {
      if (item.tagType === TagTypeEnum.number) {
        // @ts-ignore
        const value1 = item.tagValue[0] || item.tagValue[0] === '0.00' || item.tagValue[0] === 0
        // @ts-ignore
        const value2 = item.tagValue[1] || item.tagValue[1] === '0.00' || item.tagValue[1] === 0
        return (!value1 && value2) || (value1 && !value2)
      }
    })
  }

  // 详情 是否存在标签数据
  get hasDtlTagValue() {
    return this.value?.memberTagOption?.tags?.length
  }

  // 详情 是否存在客群数据
  get hasDtlGroupValue() {
    return this.value?.userGroupOption?.userGroups?.length
  }

  // 详情 是否存在
  get hasDtlMemberValue() {
    return this.value?.memberRule?.props?.length
  }

  get isEnglish() {
    return sessionStorage.getItem("locale") === "en_US";
  }

  // 标签数据 详情信息
  getTagValueStr(item: TagOption) {
    if (!item.tagId || !item.tagName) return ''
    let str = ''
    str += `${item.tagName}：`
    if (item.tagType === TagTypeEnum.date) {
      str += `${item.tagValues[0]} - ${item.tagValues[1]}`
    } else if (item.tagType === TagTypeEnum.number) {
      str += `${this.i18n('/储值/预付卡/预付卡查询/列表页面/最小值')}：${item.tagValues[0]} `
      str += `${this.i18n('/储值/预付卡/预付卡查询/列表页面/最大值')}：${item.tagValues[1]}`
    } else {
      // 单选 多选 文本
      item.tagValues.forEach((tagVal) => {
        str += `${tagVal}、`
      })
      str = str.slice(0, -1)
    }
    return str
  }

  // 客群数据 详情信息
  get getGroupValueStr() {
    let str = ''
    this.value?.userGroupOption?.userGroups.forEach((item) => {
      str += item.name + '、'
    })
    str = str.slice(0, -1)
    return str
  }



  created() {
    this.memberRule.connective = 'AND'
    this.doBindGroupRange()
    this.doBindMemberAttr()
    this.initAttrList()
  }

  async initAttrList() {
    try {
      await this.getGradeList();
      await this.getMemberPayCardList()
    } catch (error: any) {
      this.$message.error(error)
    }
    this.attrInitLoadFlag = true
  }

  queryAndInit() {
    this.loading = true
    this.doBindGroupRange() //人群范围不等接口返回，先回显
    this.doBindMemberAttr()
    Promise.all([this.queryGroup(true), this.queryTag(true)]).finally(() => {
      this.doBindValue()
      this.loading = false
    })
  }

  // 查询客群列表
  queryGroup(isOrigin: boolean = false) {
    const params = new UserGroupV2Filter()
    params.nameLikes = this.groupForm.filterInfo.inputValue || null
    return UserGroupV2Api.query(params).then((res) => {
      if (res.code === 2000) {
        this.groupArr = res.data || []
        if (isOrigin === true) {
          this.originGroupList = res.data || []
        }
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    })
  }

  // 查询标签列表
  queryTag(isOrigin: boolean = false) {
    const params = new TagTemplateFilterV2()
    params.page = 0
    params.pageSize = 0
    params.nameLikes = this.labelForm.filterInfo.inputValue || null
    params.tagTypeEquals = this.labelForm.filterInfo.dataType
    if (this.labelForm.filterInfo.labelType?.length) {
      params.channelEquals = this.labelForm.filterInfo.labelType[0]
    }
    return TagTemplateV2Api.query(params).then((res) => {
      if (res.code === 2000) {
        if (isOrigin === true) {
          this.originTagList = res.data?.map((item) => {
            Object.assign(item, {
              tagValue: this.getEmptyState(item.tagType!)
            })
            return item as TagValueItem
          }) || []
        }
        this.tagList = res.data?.map((item) => {
          let targetObj = this.originTagList.find(orinItem => item.uuid === orinItem.uuid)
          if (!targetObj) { // 查出来了总列表中没有的数据：则向总列表中添加该项
            targetObj = Object.assign(item, { tagValue: this.getEmptyState(item.tagType!) })
            this.originTagList.push(JSON.parse(JSON.stringify(targetObj)))
          }
          Object.assign(item, {
            isExpand: false,
            tagValue: JSON.parse(JSON.stringify(targetObj))?.tagValue
          })
          return item as TagItem
        }) || [];
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    })
  }

  // 输入回车键
  onEnter(event: any, type: 'label' | 'group') {
    if (event.which === 13) {
      if (type == 'group') {
        this.queryGroup()
      } else if (type === 'label') {
        this.queryTag()
      }
    }
  }

  // 已选
  getTagText(item: TagValueItem) {
    let str = `${item.name}：`
    if ([TagTypeEnum.checkbox, TagTypeEnum.text, TagTypeEnum.singleChoice].includes(item.tagType!)) {
      (item.tagValue as string[] || []).forEach((val) => {
        str += `${val}、`
      })
      str = str.slice(0, -1)
    } else if (item.tagType === TagTypeEnum.date && item.tagValue) {
      str += `${item.tagValue[0]} - ${item.tagValue[1]}`
    } else if (item.tagType === TagTypeEnum.number && item.tagValue?.length) {
      if (item.tagValue[0] !== null) {
        str += `${this.i18n('/储值/预付卡/预付卡查询/列表页面/最小值')}：${item.tagValue[0]} `
      }
      if (item.tagValue[1] !== null) {
        str += `${this.i18n('/储值/预付卡/预付卡查询/列表页面/最大值')}：${item.tagValue[1]}`
      }
    }
    return str
  }

  // 展开/收起标签分类
  doExpendTag(val: TagItem) {
    this.tagList.forEach((item) => {
      if (item.uuid === val.uuid) {
        item.isExpand = !item.isExpand
        this.$forceUpdate()
      }
    })
  }

  // 清空
  doClear() {
    if (this.loading) {
      return this.$message.warning(this.i18n('数据加载中，请勿操作'))
    }
    this.groupForm.filterInfo.groupList = []
    this.originTagList.forEach((item) => {
      item.tagValue = this.getEmptyState(item.tagType!)
    })
    this.tagList.forEach((item) => {
      item.tagValue = this.getEmptyState(item.tagType!)
    })
    if (this.memberRule?.props) {
      this.memberRule.props = []
    }
  }

  // 获取标签空状态值
  getEmptyState(tagType: TagTypeEnum) {
    if (tagType === TagTypeEnum.number) {
      return [null, null]
    } else {
      return [TagTypeEnum.checkbox, TagTypeEnum.singleChoice, TagTypeEnum.text].indexOf(tagType) > -1 ? [] : null
    }
  }

  // 删除标签
  doRemoveTag(val: TagValueItem) {
    const targetUuid = val.uuid
    this.originTagList.some((item, index) => {
      if (item.uuid === targetUuid) {
        this.$set(this.originTagList[index], 'tagValue', this.getEmptyState(item.tagType!))
        return true
      }
    })
  }

  // 删除客群
  doRemoveGroup(val: UserGroup) {
    this.groupForm.filterInfo.groupList = this.groupForm.filterInfo.groupList.filter((uuid: string) => {
      return uuid !== val.uuid
    }) || []
  }

  // 勾选标签（单选、多选、文本）
  doSelectLabel(type: 'multi' | 'text', uuid: string, value: string) {
    if (this.loading) return
    if (type === 'multi' || type === 'text') {
      // 勾选/取消勾选 多选标签
      this.tagList.forEach((item) => {
        if (item.uuid === uuid) {
          const valueIndex = (item.tagValue as any).indexOf(value)
          if (valueIndex > -1) {
            item.tagValue?.splice(valueIndex, 1)
          } else {
            item.tagValue?.push(value)
          }
          this.updateOriginData(null, item)
        }
      })
    }
  }

  // 更新originTagList中的数据
  updateOriginData($e: any, val: TagValueItem) {
    this.originTagList.forEach((item) => {
      if (item.uuid === val.uuid) {
        item.tagValue = val.tagValue
      }
    })
  }

  // 检查数值类型的最小值和最大值关系
  checkNumberMinMax(val: TagValueItem, type: 'min' | 'max') {
    setTimeout(() => {
      this.originTagList.forEach((item) => {
        if (item.uuid === val.uuid) {
          // @ts-ignore
          if (Number(item.tagValue[0]) > Number(item.tagValue[1]) && item.tagValue[1]) {
            // 数值类型，且最小值 > 最大值
            // @ts-ignore
            const newVal = type === 'min' ? item.tagValue[0] : item.tagValue[1]
            item.tagValue = [newVal, newVal]
            this.tagList.forEach((nowItem) => {
              if (nowItem.uuid === item.uuid) {
                nowItem.tagValue = [newVal, newVal]
              }
            })
          }
        }
      })
    }, 100);
  }

  doSelect() {
    if (this.loading) return
    this.visible = true
    this.$nextTick(() => {
      this.queryAndInit()
    })
  }

  doEmitChange() {
    const res = this.doParams()
    this.$emit('input', res)
    this.$emit("change", res);
  }

  async changeTabSelect(tab: 'label' | 'group' | 'member') {
    if (this.tabSelect === 'member') {
      let flag = false
      if (this.memberRule?.props && this.memberRule?.props.length > 0) {
        const $member = this.$refs.memberPropSelectRef as any
        await $member?.validate().catch(() => {
          flag = true
        })
      }
      if (flag) {
        return this.$message.warning(this.i18n('/会员/选择人群/请填写完整会员属性'))
      }
    }

    this.tabSelect = tab
  }

  async doModalClose(type: string) {
    if (type === "confirm") {
      let flag = false
      if (this.memberRule?.props && this.memberRule?.props.length > 0) {
        const $member = this.$refs.memberPropSelectRef as any
        await $member?.validate().catch(() => {
          flag = true
        })
      }
      if (flag) {
        return this.$message.warning(this.i18n('/会员/选择人群/请填写完整会员属性'))
      }
      if (this.hasNotDoubleValue) {
        const tagName = this.hasNotDoubleValue.name
        return this.$message.warning(this.i18n('标签{0}需填写完整', [`"${tagName}"`]))
      }
      this.doEmitChange()
    }
    this.visible = false
    this.closeInit()
  }

  doRangeChange() {
    this.doClear()
    this.doEmitChange()
  }

  // 关闭弹窗后，dialog内部数据恢复到初始化状态
  closeInit() {
    this.tabSelect = 'label'
    this.labelForm = new FormLabel()
    this.groupForm = new FormGroup()
    this.groupArr = []
    this.originGroupList = []
    this.tagList = []
    this.originTagList = []
  }

  doValidate() {
    const noSelect = !this.hasDtlTagValue && !this.hasDtlGroupValue && !this.hasDtlMemberValue // 既没有选择标签也没有选择客群
    if (this.groupRange !== MemberRangeType.ALL && noSelect) {
      this.$message.warning(this.i18n('请选择人群'))
      return Promise.reject()
    } else {
      return Promise.resolve(true)
    }
  }

  doParams() {
    // 将本地数据转换为标准模型
    const res: ParticipateMember = new ParticipateMember()
    res.memberRangeType = this.groupRange
    if (this.selectTagList.length) {
      res.memberTagOption = new MemberTagOption()
      res.memberTagOption.optionType = this.labelForm.fulfillType === 'all' ? OptionType.ALL : OptionType.ANY
      res.memberTagOption.tags = this.selectTagList.map((item: TagValueItem) => {
        const obj = new TagOption()
        obj.tagId = item.uuid
        obj.tagName = item.name
        obj.tagType = item.tagType
        obj.tagValues = (Array.isArray(item.tagValue) ? item.tagValue : [item.tagValue]) as any //是数组直接用，不是数组就包一层
        return obj
      })
    }
    if (this.selectGroupList.length) {
      res.userGroupOption = new UserGroupOption()
      res.userGroupOption.optionType = this.groupForm.fulfillType === 'all' ? OptionType.ALL : OptionType.ANY
      res.userGroupOption.userGroups = this.selectGroupList.map((item) => {
        const obj = new IdName()
        obj.id = item?.uuid
        obj.name = item?.name
        return obj
      })
    }

    if (this.memberRule?.props) {
      res.memberRule = new MemberRule()
      res.memberRule.connective = 'AND'
      res.memberRule.props = this.memberRule.props
    }

    console.log('看看最终提交出去的数据', res);
    return res
  }

  // 回显人群范围
  doBindGroupRange() {
    this.groupRange = this.value?.memberRangeType || MemberRangeType.ALL
    if (this.hideAll && this.groupRange === MemberRangeType.ALL) {
      this.groupRange = MemberRangeType.PART
    }
  }

  // 回显会员属性
  doBindMemberAttr() {
    if (this.value?.memberRule) {
      this.memberRule = JSON.parse(JSON.stringify(this.value.memberRule))
    } else {
      this.memberRule.connective = 'AND';
      this.memberRule.props = []
    }
  }

  // 回显
  doBindValue() {
    this.labelForm.fulfillType = this.value?.memberTagOption?.optionType === OptionType.ALL ? 'all' : 'one'
    this.value?.memberTagOption?.tags.forEach((item) => {
      this.originTagList.forEach((oriItem) => {
        if (item.tagId === oriItem.uuid) {
          oriItem.tagValue = JSON.parse(JSON.stringify(item.tagValues))
          if (oriItem.tagType === TagTypeEnum.number && oriItem.tagValue?.length === 1) {
            // 只填了最小值，需要把最大值的null补上
            (oriItem.tagValue as any).push(null)
          }
        }
      })
    })
    this.groupForm.fulfillType = this.value?.userGroupOption?.optionType === OptionType.ALL ? 'all' : 'one'
    this.value?.userGroupOption?.userGroups.forEach((item) => {
      if (!this.groupForm.filterInfo.groupList.includes(item.id!)) {
        this.groupForm.filterInfo.groupList.push(item.id!)
      }
    })
  }

  getMemberRuleText(item: MemberProp) {
    let result = '';

    if (item.prop === 'store') {
      result += `${this.i18n('/会员/洞察/公共/会员属性/归属门店')}:`
      if (item.operator === '=null') {
        result += this.i18n('/会员/洞察/公共/操作符/为空')
      } else {
        result += item.storeProp?.stores.map((item) => {
          // @ts-ignore
          let str = this.typeMap[item.type] + ':'
          if (item.operator === 'in') {
            str += this.i18n('属于') + ':'
          } else if (item.operator === '!in') {
            str += this.i18n('不属于') + ':'
          }

          if (item.type === 'area') {
            return str + item.stores?.map((store) => {
              return this.i18n(store.name!)
            }).join('/')
          } else {
            return str + item.stores?.map((store) => {
              return this.i18n(store.name!)
            }).join('、')
          }

        })
      }
    }

    if (item.prop === 'registerTime' || item.prop === 'firstConsumeTime') {
      if (item.prop === 'registerTime') {
        result += `${this.i18n('/会员/洞察/公共/会员属性/注册时间')}:`
      } else {
        result += `${this.i18n('/会员/洞察/公共/会员属性/首次消费时间')}:`
      }

      if (item.operator === 'relative') {
        // @ts-ignore
        result += item.timeProp?.lstDate ? this.lstDateOptions[item.timeProp?.lstDate] : '';
      } else {
        result += item.timeProp?.date.map((dateItem: any) => {
          return DateUtil.format(new Date(dateItem), 'yyyy-MM-dd') // dateItem && dateItem.length>10 ? dateItem.substring(0,10) : dateItem
        }).join('~')
      }
    }

    if (item.prop === 'registerScene') {
      result += `${this.i18n('/会员/洞察/公共/会员属性/招募方式')}：`;
      if (item.operator === '=null') {
        result += this.i18n('/会员/洞察/公共/操作符/为空')
      } else if (item.operator === '!=null') {
        result += this.i18n('/会员/洞察/公共/操作符/不为空')
      } else {
        if (item.operator === 'in') {
          result += this.i18n('属于') + ':'
        } else if (item.operator === '!in') {
          result += this.i18n('不属于') + ':'
        }
        result += item.strProp?.values.map((item) => this.i18n(item)).join('、')
      }
    }

    if (item.prop === 'grade') {
      result += `${this.i18n('/会员/洞察/公共/会员属性/等级')}:`
      if (item.operator === '=null') {
        result += this.i18n('/会员/洞察/公共/操作符/为空')
      } else if (item.operator === '!=null') {
        result += this.i18n('/会员/洞察/公共/操作符/不为空')
      } else {

        if (item.operator === 'in') {
          result += this.i18n('属于') + ':'
        } else if (item.operator === '!in') {
          result += this.i18n('不属于') + ':'
        }

        result += item.strProp?.values.map((item) => {
          return this.gradeListMap.get(item)
        }).join('、')
      }
    }

    if (item.prop === "registerChannelType") {
      result += `${this.i18n('/会员/洞察/公共/会员属性/注册渠道')}:`
      if (item.operator === '=null') {
        result += this.i18n('/会员/洞察/公共/操作符/为空')
      } else if (item.operator === '!=null') {
        result += this.i18n('/会员/洞察/公共/操作符/不为空')
      } else if (item.operator === 'in') {
        result += this.i18n('属于') + ':'
      } else if (item.operator === '!in') {
        result += this.i18n('不属于') + ':'
      }

      if (item.operator === 'in' || item.operator === '!in') {
        result += item.strProp?.values.map((item) => {
          // @ts-ignore
          return this.registerChannel[item]
        }).join('、')
      }
    }

    if (item.prop === 'gender') {
      result += `${this.i18n('/会员/会员资料/性别')}:`
      if (item.operator === '=null') {
        result += this.i18n('/会员/洞察/公共/操作符/为空')
      } else if (item.operator === '!=null') {
        result += this.i18n('/会员/洞察/公共/操作符/不为空')
      } else if (item.operator === 'in') {
        result += this.i18n('属于') + ':'
      } else if (item.operator === '!in') {
        result += this.i18n('不属于') + ':'
      }

      if (item.operator === 'in' || item.operator === '!in') {
        result += item.strProp?.values.map((item) => this.i18n(item)).join('、')
      }
    }

    if (item.prop === 'birthday') {
      result += `${this.i18n('/会员/会员资料/生日')}:`
      if (item.operator === 'by_date') {
        result += `${item.birthdayProp?.dateBegin ? item.birthdayProp?.dateBegin : ''} - ${item.birthdayProp?.dateEnd ? item.birthdayProp?.dateEnd : ''}`
      } else {
        result += `${item.birthdayProp?.yearBegin ? item.birthdayProp?.yearBegin : ''} - ${item.birthdayProp?.yearEnd ? item.birthdayProp?.yearEnd : ''}`
      }
    }

    // 付费会员卡
    if (item.prop === 'benefitCardTemplateCodes' || item.prop === 'paidBenefitCardTemplateCodes') {
      result += this.i18n('/会员/权益卡/权益卡') + '/' + this.i18n('/会员/付费会员/付费会员卡') + ':';
      if (item.operator === 'in') {
        result += this.i18n('属于') + ':'
      } else if (item.operator === '!in') {
        result += this.i18n('不属于') + ':'
      }
      result += item.strProp?.values?.map((item) => {
        const find = this.benefitCardTemplateList.find((card) => card.code == item)
        if (find) {
          return find.name;
        } else {
          return name;
        }
      }).join('、')
    }

    return result;
  }

  getGradeList() {
    return MemberBalancePromotionApi.gradeList().then((response) => {
      if (response.data) {
        this.gradeList = response.data
        response.data.forEach((item) => {
          this.gradeListMap.set(item.code, item.name)
        })
      }
    })
  }

  doRemoveMember(index: number) {
    const $member = this.$refs.memberPropSelectRef as any
    $member?.handleDelete(index)
  }

  // 查询付款会员卡列表
  getMemberPayCardList() {
    const params = new BenefitCardTemplateFilter();
    // 查询全部
    params.page = 0;
    params.pageSize = 0;
    // 仅已启用
    params.statusEquals = enefitCardTemplateStatus.start;
    // params.typeEquals = enefitCardTemplateType.paid;
    params.marketingCenterEquals = sessionStorage.getItem("marketCenter");
    return BenefitCardTemplateApi.query(params).then((response) => {
      if (response.data?.length) {
        this.benefitCardTemplateList = response.data;
      } else {
        this.benefitCardTemplateList = [];
      }
    })
  }
};