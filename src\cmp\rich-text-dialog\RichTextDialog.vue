<!--
 * @Author: 黎钰龙
 * @Date: 2024-01-02 16:47:49
 * @LastEditTime: 2024-05-07 16:58:05
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\rich-text-dialog\RichTextDialog.vue
 * 记得注释
-->
<template>
  <el-dialog :title="title" :visible.sync="dialogVisible" width="70%" :before-close="handleClose">
    <div v-html="richContent"></div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">{{ i18n("/会员/洞察/客群管理/列表页/关闭") }}</el-button>
    </span>
  </el-dialog>
</template>

<script lang="ts" src="./RichTextDialog.ts">
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  overflow: hidden;
}
</style>