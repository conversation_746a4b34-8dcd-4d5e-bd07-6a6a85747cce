import ConstantMgr from "mgr/ConstantMgr"

/*
 * @Author: 黎钰龙
 * @Date: 2024-11-21 19:31:52
 * @LastEditTime: 2025-02-21 10:28:27
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\autoTag\OperatorType.ts
 * 记得注释
 */
export enum OperatorType {
  // 等于
  eq = 'eq',
  // 不等于
  ne = 'ne',
  // 包含
  in = 'in',
  // 不包含
  not_in = 'not_in',
  // 有值
  value = 'value',
  // 没值
  no_value = 'no_value',
  // 正则匹配
  regular_matching = 'regular_matching',
  // 非正则匹配
  no_regular_matching = 'no_regular_matching',
  // 大于
  gt = 'gt',
  // 大于等于
  ge = 'ge',
  // 小于
  lt = 'lt',
  // 小于等于
  le = 'le',
  // 区间
  between = 'between',
  // 是
  yes = 'yes',
  // 否
  no = 'no',
  // 绝对时间
  absolute_time = 'absolute_time',
  // 相对当前时间点
  relative_time_point = 'relative_time_point',
  // 相对当前时间区间
  relative_time_range = 'relative_time_range'
}

export class OperatorMethods {
  // 字符串类型 选项
  static getStrList() {
    return [
      {
        label: new ConstantMgr.MenusFuc().format("/会员/等级/付费等级流水报表/列表页面", "等于"),
        value: OperatorType.eq
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/洞察/公共/操作符", "不等于"),
        value: OperatorType.ne
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/智能打标", "包含"),
        value: OperatorType.in
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/智能打标", "不包含"),
        value: OperatorType.not_in
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/智能打标", "有值"),
        value: OperatorType.value
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/智能打标", "没值"),
        value: OperatorType.no_value
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/智能打标", "正则匹配"),
        value: OperatorType.regular_matching
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/智能打标", "正则不匹配"),
        value: OperatorType.no_regular_matching
      },
    ]
  }

  // 时间类型列表
  static getDateType() {
    return [
      {
        label: new ConstantMgr.MenusFuc().format("/会员/智能打标", "绝对时间"),
        value: OperatorType.absolute_time
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/智能打标", "相对当前时间点"),
        value: OperatorType.relative_time_point
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/智能打标", "相对当前时间区间"),
        value: OperatorType.relative_time_range
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/智能打标", "有值"),
        value: OperatorType.value
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/智能打标", "没值"),
        value: OperatorType.no_value
      }
    ]
  }

  // 时间类型 绝对时间 选项
  static getDateList() {
    return [
      {
        label: new ConstantMgr.MenusFuc().format("/会员/等级/付费等级流水报表/列表页面", "等于"),
        value: OperatorType.eq
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/洞察/公共/操作符", "不等于"),
        value: OperatorType.ne
      },
      {
        label: new ConstantMgr.MenusFuc().format("/储值/会员储值/门店预付卡管理", "小于"),
        value: OperatorType.lt
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/智能打标", "大于"),
        value: OperatorType.gt
      },
      {
        label: new ConstantMgr.MenusFuc().format("/储值/会员储值/门店预付卡管理", "小于等于"),
        value: OperatorType.le
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/智能打标", "大于等于"),
        value: OperatorType.ge
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/标签客群/标签", "区间"),
        value: OperatorType.between
      },
    ]
  }

  // 数据类型列表
  static getNumberList() {
    return [
      {
        label: new ConstantMgr.MenusFuc().format("/会员/等级/付费等级流水报表/列表页面", "等于"),
        value: OperatorType.eq
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/洞察/公共/操作符", "不等于"),
        value: OperatorType.ne
      },
      {
        label: new ConstantMgr.MenusFuc().format("/储值/会员储值/门店预付卡管理", "小于"),
        value: OperatorType.lt
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/智能打标", "大于"),
        value: OperatorType.gt
      },
      {
        label: new ConstantMgr.MenusFuc().format("/储值/会员储值/门店预付卡管理", "小于等于"),
        value: OperatorType.le
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/智能打标", "大于等于"),
        value: OperatorType.ge
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/标签客群/标签", "区间"),
        value: OperatorType.between
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/智能打标", "有值"),
        value: OperatorType.value
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/智能打标", "没值"),
        value: OperatorType.no_value
      },
    ]
  }

  // 布尔类型列表
  static getBoolList() {
    return [
      {
        label: new ConstantMgr.MenusFuc().format("/公用/券模板", "是"),
        value: OperatorType.yes
      },
      {
        label: new ConstantMgr.MenusFuc().format("/公用/券模板", "否"),
        value: OperatorType.no
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/智能打标", "有值"),
        value: OperatorType.value
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/智能打标", "没值"),
        value: OperatorType.no_value
      },
    ]
  }

  // 分层标签 事件行为操作符选项
  static layerActionOperatorList() {
    return [
      {
        label: new ConstantMgr.MenusFuc().format("/会员/等级/付费等级流水报表/列表页面", "等于"),
        value: OperatorType.eq
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/洞察/公共/操作符", "不等于"),
        value: OperatorType.ne
      },
      {
        label: new ConstantMgr.MenusFuc().format("/储值/会员储值/门店预付卡管理", "小于"),
        value: OperatorType.lt
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/智能打标", "大于"),
        value: OperatorType.gt
      },
      {
        label: new ConstantMgr.MenusFuc().format("/储值/会员储值/门店预付卡管理", "小于等于"),
        value: OperatorType.le
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/智能打标", "大于等于"),
        value: OperatorType.ge
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/标签客群/标签", "区间"),
        value: OperatorType.between
      }
    ]
  }
}