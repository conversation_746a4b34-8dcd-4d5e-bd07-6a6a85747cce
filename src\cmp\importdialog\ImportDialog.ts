import {Component, Prop, Vue} from 'vue-property-decorator'
import EnvUtil from 'util/EnvUtil'
import ImportResultDialog from 'cmp/importdialog/ImportResultDialog.vue'
import SelectStores from 'cmp/selectStores/SelectStores';
import SelectClient from "cmp/selectclient/SelectClient";
import UploadApi from "http/upload/UploadApi";

@Component({
	name: "ImportDialog",
	components: {
		ImportResultDialog,
    SelectStores,
		SelectClient
	},
})
export default class ImportDialog extends Vue {
	$refs: any;

	@Prop()
	title: string; // dialog标题

	@Prop({
		type: Number,
		default: 5000,
	})
	importNumber: string; // dialog标题

	@Prop()
	templateName: string; // 模板名称

	@Prop()
	templatePath: string; // 模板路径

	@Prop()
	importUrl: string; // 导入文件接口的url

	@Prop({
		type: Boolean,
		default: false,
	})
	dialogShow: boolean; // 控制模态框的展示
	importResultDialogShow = false;
	uploadHeaders: any = {};

	@Prop({
		type: Boolean,
		default: false,
	})
	showOrg: boolean; // 控制模态框的展示
	@Prop({
		type: Boolean,
		default: false,
	})
	showClient: boolean; // 控制模态框的展示
	@Prop({
		type: Boolean,
		default: false,
	})
	clientRequired: boolean; // 控制客户是否必填
	@Prop({
		type: Boolean,
		default: false,
	})
	showDepositType: boolean; // 控制模态框的展示
	@Prop({
		type: Array,
		default: () => {
			return [];
		},
	})
	orgs: [];
	@Prop({
		type: Boolean,
		default: false,
	})
	isSingle: boolean; // 控制模态框的展示
	orgId: string = "";
	clientId: string = "";
	depositType: string = "DEPOSIT";
	fileList: any[] = [];

	// 控制结果模态框的展示
	get getUploadUrl() {
		let url = EnvUtil.getServiceUrl() + this.importUrl + "?orgId=" + this.orgId
		if (this.showDepositType) {
			url = url  + "&depositType=" + this.depositType;
		}
		if (this.showClient) {
			url = url  + "&clientId=" + this.clientId;
		}
		return url
	}
	created() {
		let locale = sessionStorage.getItem("locale");
		this.uploadHeaders = {
			locale: locale ? locale : "zh_CN",
			time_zone: new Date().getTimezoneOffset(),
			marketingCenter: sessionStorage.getItem("marketCenter"),
		};
    const authorization = EnvUtil.getUniAuthorization()
    if (authorization) {
      this.uploadHeaders.authorization = authorization
    }
	}
	getImportDesc() {
		let str: any = this.formatI18n("/公用/导入", "为保障上传成功，建议每次最多上传{0}条信息");
		str = str.replace(/\{0\}/g, this.importNumber ? this.importNumber : 5000);
		return str;
	}
	getSuccessInfo(a: any, b: any, c: any) {
		if (a && a.code === 2000) {
			this.$refs.upload.clearFiles();
			this.$emit("dialogClose");
			this.$emit("upload-success", b);
		} else {
			this.$message.error(
				a.msg
					.replace("java.lang.Exception:", "")
					.replace("com.hd123.phoenix.crm.api.CrmException:", "")
					.trimEnd()
			);
      this.$refs.upload.clearFiles();
		}
	}
	getErrorInfo(a: any, b: any, c: any) {
		this.$message.error(this.formatI18n("/公用/导入", "导入失败，请重新导入") as string);
		this.$refs.upload.clearFiles();
	}
	doHandleChange(file: any, fileList: any) {
		// console.log('fileList', fileList)
    if (this.isSingle === true) {
      if (fileList.length > 0) {
				// this.fileCount++;
				this.fileList = [fileList[fileList.length - 1]];
			}
		}
		
	}
	doBeforeClose(done: any) {
		this.$emit("dialogClose");
		done();
	}
	doModalClose(type: string) {
		if (type === "confirm") {
			// this.importResultDialogShow = true
			if (this.$refs.upload.uploadFiles.length <= 0) {
				this.$message.warning(this.formatI18n("/公用/导入", "请先选择文件") as string);
				return;
			}
			if (!this.orgId && this.showOrg) {
				this.$message.warning(this.formatI18n("/公用/查询条件/提示", "请选择发生组织") as string);
				return;
			}
			if (!this.clientId && this.showClient && this.clientRequired) {
				this.$message.warning(this.formatI18n("/公用/查询条件/提示", "请选择客户") as string);
				return;
			}
			this.$refs.upload.submit();
		} else {
			this.$refs.upload.clearFiles();
		}
		this.$emit("dialogClose", type);
	}
	doImportResultDialogClose() {
		this.importResultDialogShow = false;
		this.$emit("dialogClose");
	}

	downloadTemplate() {
		UploadApi.getUrl(this.templatePath).then((resp: any) => {
			if (resp && resp.data) {
				window.open(resp.data);
			}
		}).catch((error) => {
			if (error && error.message) {
				this.$message.error(error.message)
			}
		})
	}
}
