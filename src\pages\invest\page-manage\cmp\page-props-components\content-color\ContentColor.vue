<template>
  <div class="box">
    <el-form :label-position="labelPosition" :model="value" ref="form" label-width="100px">
      <p class="set">
        <span>{{ i18n('内容颜色') }}</span>
      </p>
      <el-form-item label-width="0px">
        <el-row :gutter="20" class="margin-left-common">
          <el-col :span="9" :key="item.id" v-for="item in options">
            <div class="nightfury-flex-left-center">
              <div class="label">{{ item.label }}</div>
              <el-color-picker style="width: 32px" @change="handleChange" v-model="value[item.id]" size="small"></el-color-picker>
            </div>
          </el-col>
        </el-row>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" src="./ContentColor.ts"></script>

<style lang="scss" scoped>
.box {
  padding: 12px;
  background: #f0f2f6;
  border-radius: 2px;
  margin-bottom: 20px;
  .set {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    span {
      font-weight: 600;
      font-size: 14px;
      color: #24272b;
      line-height: 20px;
    }
  }
  .nightfury-flex-left-center {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .label {
      font-weight: 400;
      font-size: 14px;
      color: #5a5f66;
      line-height: 20px;
    }
  }
  .el-form-item {
    margin-bottom: 0;
  }
  .el-col-9 {
    width: 100%;
  }
}
</style>
