<!--
 * @Author: 黎钰龙
 * @Date: 2024-02-27 13:44:49
 * @LastEditTime: 2024-02-29 10:26:06
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\prepaycard\card-recharge-limit\CardRechargeLimitDtl.vue
 * 记得注释
-->
<template>
  <div class="recharge-limit-dtl-container">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button v-if="hasOptionPermission('/卡/卡管理/卡充值限额设置','配置维护') && hasRule" @click="doEdit" type="primary">
          {{i18n('修改')}}
        </el-button>
      </template>
    </BreadCrume>
    <template v-if="hasRule">
      <div class="rule-block" v-for="(item,index) in dtl" :key="index">
        <div class="block-title">
          <i18n k="/卡/卡充值限额设置/设置{0}">
            <template slot="0">{{index + 1}}</template>
          </i18n>
        </div>
        <FormItem :label="i18n('充值渠道') + '：'">
          <div style="line-height:36px">{{getChannelName(index)}}</div>
        </FormItem>
        <FormItem :label="i18n('是否有持卡人') + '：'">
          <div style="line-height:36px">{{item.body.existCardholder ? i18n('是') : i18n('否')}}</div>
        </FormItem>
        <FormItem :label="i18n('单次充值限额') + '：'">
          <div style="line-height:36px">{{getChargeLimit(index)}}</div>
        </FormItem>
      </div>
    </template>
    <div class="empty" v-else-if="hasRule === false">
      <img src="~assets/image/auth/ct_empty.png" alt="">
      <div class="empty-desc">{{i18n('暂无卡充值限额规则，请前往设置')}}</div>
      <el-button type="primary" @click="doSet" v-if="hasOptionPermission('/卡/卡管理/卡充值限额设置','配置维护')">
        {{formatI18n("/权益/积分/积分初始化/未初始化状态/得积分规则/基础得积分", "立即设置")}}
      </el-button>
    </div>
  </div>
</template>

<script lang="ts" src="./CardRechargeLimitDtl.ts">
</script>

<style lang="scss" scoped>
.recharge-limit-dtl-container {
  width: 100%;
  padding-bottom: 20px;
  .rule-block {
    width: 100%;
    background: #ffffff;
    border-radius: 8px;
    margin-bottom: 16px;
    padding: 24px;
    .block-title {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 16px;
      color: #222222;
      line-height: 20px;
      margin-bottom: 12px;
    }
  }
  .empty {
    position: absolute;
    top: 150px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    img {
      width: 160px;
      height: 160px;
    }
    .empty-desc {
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      color: #36445a;
      margin-bottom: 12px;
    }
  }
  ::v-deep .qf-form-item .qf-form-label {
		text-align: left;
		width: 110px;
	}
}
</style>