import { Component, Vue } from "vue-property-decorator";
import QueryCondition from "cmp/querycondition/QueryCondition.vue";
import FormItem from "cmp/formitem/FormItem.vue";
import ListWrapper from "cmp/list/ListWrapper.vue";
import Member<PERSON><PERSON> from "http/member_standard/MemberApi";
import Dot from "cmp/dot/Dot.vue";
import DownloadCenterDialog from "pages/main/dowmload_center_dialog/DownloadCenterDialog.vue";
import BreadCrume from "cmp/bread-crumb/BreadCrume.vue";
import MemberFilter from "model/member_v2/member/MemberFilter";
import RSOrgFilter from "model/common/RSOrgFilter";
import OrgApi from "http/org/OrgApi";
import GradeApi from "http/grade/grade/GradeApi";
import UploadFileModal from "pages/member/data/cmp/UploadFileModal.vue";
import ExportConfirm from "cmp/exportconfirm/ExportConfirm";
import DateUtil from "util/DateUtil";
import UrlParamUtil from "util/UrlParamUtil";
import ModifyStoreDialog from "pages/member/data/cmp/ModifyStoreDialog.vue";
import MarketingCenterApi from "http/marketingcenter/MarketingCenterApi";
import RSMarketingCenterFilter from "model/common/RSMarketingCenterFilter";
import IdName from "model/common/IdName";
import RSChannelManagementFilter from "model/common/RSChannelManagementFilter";
import ChannelManagementApi from "http/channelmanagement/ChannelManagementApi";
import I18nPage from "common/I18nDecorator";
import UploadMemberTags from "pages/custom/dqsh/member/cmp/UploadMemberTags";
import UploadPaidMemberCard from "pages/custom/dqsh/member/cmp/UploadPaidMemberCard";
import PageConfigApi from "http/pageConfig/PageConfigApi";
import BrowserMgr from "mgr/BrowserMgr";
import MyQueryCmp from "cmp/querycondition/MyQueryCmp";
import UploadFreezeDialog from "./cmp/UploadFreezeDialog";

@Component({
	name: "StandardMemberList",
	components: {
    MyQueryCmp,
		FormItem,
		ListWrapper,
		Dot,
		UploadFileModal,
		DownloadCenterDialog,
		BreadCrume,
		ExportConfirm,
		ModifyStoreDialog,
		UploadMemberTags,
		UploadFreezeDialog,
		UploadPaidMemberCard
	},
})
@I18nPage({
  prefix: ["/会员/会员资料",'/公用/菜单'],
  auto: true
})
export default class StandardMemberList extends Vue {
  $refs:any
	uploadDialogShow = false;
  	uploadTagsDialogShow: boolean = false;
	uploadPaidMemberDialogShow: boolean = false;
	storeDialogShow: boolean = false;
	exportDialogShow = false;
	stores: any = [];
	$router: any;
	panelArray = [
		{
			name: "",
			url: "",
		},
	];
	query: MemberFilter = new MemberFilter();
	identType = "all";
	identCode = null;
	identPlaceholder = "";
	birthdayType = "date";
	registerDate: any = [];
	createDate: any = [];
	activeDate: any = [];
	birthdayDate: any = [];
	birthdayEnd: any = null;
	birthdayBegin: any = null;
	rsMarketingCenterFilter: RSMarketingCenterFilter = new RSMarketingCenterFilter();
	fromMarketingCenter: any = null; // 所属营销中心
	marketingCenterData: any = []; // 所有营销中心
	childDate: any = [];
	tableHeight = 450;
	fileDialogvisiable = false;
	memberLevel: any = [];
	// 分页
	page = {
		currentPage: 1,
		total: 0,
		size: 10,
	};
	tableData: any[] = [];
	dialogShow = false;
	showTip: boolean = false;
	channels: any[] = [];
	searchChannel: any = {};
  isShowFirstAndLastName: boolean = false //是否将姓和名拆开显示

	created() {
		this.panelArray[0].name = this.formatI18n("/会员/会员资料", "会员资料");
		this.query.stateEquals = "Using";
		this.birthdayType = "date";
		this.identPlaceholder = this.formatI18n("/会员/会员资料", "请输入手机号/会员号/实体卡号");
		this.getChannels();
		this.recoverQuery();
		this.getMemberList();
		this.getMemberLevel();
		this.getStore("");
		this.getMarketingCenter();
    this.getConfig()
	}

  getConfig() {
    PageConfigApi.getConfig().then(res => {
      if (res.code === 2000 && res.data) {
        this.isShowFirstAndLastName = res.data.showFirstNameWithLastName || false
        BrowserMgr.SessionStorage.setItem('isShowFirstAndLastName', res.data.showFirstNameWithLastName)
      }
    })
  }

	channelChange(e: any) {
		console.log(e);
		if (e) {
			this.query.registerChannelEquals = e.channel.typeId;
			this.query.registerChannelIdEquals = e.channel.id;
		} else {
			this.query.registerChannelEquals = null;
			this.query.registerChannelIdEquals = null;
		}
	}

	// 查询营销中心
	getMarketingCenter() {
		MarketingCenterApi.query(this.rsMarketingCenterFilter).then((res) => {
			if (res.code === 2000) {
				const newMarketingCenterData: any = res.data;
				for (let value in newMarketingCenterData) {
					this.marketingCenterData.push(newMarketingCenterData[value].marketingCenter as IdName);
				}
				console.log("marketingCenterData", this.marketingCenterData);
			}
		});
	}

	storeQuery() {
		UrlParamUtil.store("query", this.query);
		UrlParamUtil.store("birthdayType", this.birthdayType);
		UrlParamUtil.store("identType", this.identType);
		UrlParamUtil.store("identCode", this.identCode);
		UrlParamUtil.store("searchChannel", this.searchChannel);
	}

	recoverQuery() {
		UrlParamUtil.recover("query", this.query);
		this.identCode = UrlParamUtil.get("identCode", "");
		this.identType = UrlParamUtil.get("identType", "all");
		this.birthdayType = UrlParamUtil.get("birthdayType", "date");
		this.searchChannel = UrlParamUtil.get("searchChannel", null);
		if (this.query.createdGreaterOrEquals && this.query.createdLess) {
			this.createDate = this.formatDayArr(this.query.createdGreaterOrEquals, this.query.createdLess);
		}
		if (this.query.activeTimeGreaterOrEquals && this.query.activeTimeLess) {
			this.activeDate = this.formatDayArr(this.query.activeTimeGreaterOrEquals, this.query.activeTimeLess);
		}
		if (this.query.registerTimeGreaterOrEquals && this.query.registerTimeLess) {
			this.registerDate = this.formatDayArr(this.query.registerTimeGreaterOrEquals, this.query.registerTimeLess);
		}
		if (this.query.birthMonthDayGreaterOrEquals && this.query.birthMonthDayLessOrEquals) {
			this.birthdayDate = [this.query.birthMonthDayGreaterOrEquals, this.query.birthMonthDayLessOrEquals];
		}
		if (this.query.birthDayLessOrEquals) {
			this.birthdayEnd = this.query.birthDayLessOrEquals;
		}
		if (this.query.birthDayGreaterOrEquals) {
			this.birthdayBegin = this.query.birthDayGreaterOrEquals;
		}
		if (this.query.memberChannelTypeIn) {
			this.memberChannelTypeFilter = this.memberChannelTypes
				.filter(e => this.query.memberChannelTypeIn!.indexOf(e.code) != -1);
		}
	}

	identTypeChange() {
		if (this.identType == "mobile") {
			// 手机号码
			this.identPlaceholder = this.formatI18n("/会员/会员资料", "请输入手机号");
		} else if (this.identType == "hdCardCardNumber") {
			// 实体卡号
			this.identPlaceholder = this.formatI18n("/会员/会员资料", "请输入实体卡号");
		} else if (this.identType == "crmCode") {
			// 会员号
			this.identPlaceholder = this.formatI18n("/会员/会员资料", "请输入会员号");
		} else {
			this.identPlaceholder = this.formatI18n("/会员/会员资料", "请输入手机号/会员号/实体卡号");
		}
		this.identCode = null;
	}

	formatDayArr(d1: Date, d2: Date) {
		return [DateUtil.format(d1, "yyyy-MM-dd"), DateUtil.format(d2, "yyyy-MM-dd")];
	}

	birthdayTypeChange() {
		if (this.birthdayType == "year") {
			this.birthdayDate = [];
			this.query.birthMonthDayLessOrEquals = null;
			this.query.birthMonthDayGreaterOrEquals = null;
		} else {
			this.query.birthDayLessOrEquals = null;
			this.query.birthDayGreaterOrEquals = null;
			this.birthdayBegin = null;
			this.birthdayEnd = null;
		}
	}

	doEdit() {
		// todo
	}

	doDelete() {
		// todo
	}

	doBatchImport() {
		this.uploadDialogShow = true;
	}

  doUploadTags() {
    this.uploadTagsDialogShow = true
  }
	doUploadPaidMember() {
		this.uploadPaidMemberDialogShow = true
	}

	doUploadSuccess() {
		this.uploadDialogShow = false;
		this.fileDialogvisiable = true;
		this.showTip = true;
	}

  doUploadTagsSuccess() {
    this.uploadTagsDialogShow = false;
    this.fileDialogvisiable = true;
    this.showTip = true;
  }

  doUploadPaidMemberSuccess() {
    this.uploadPaidMemberDialogShow = false;
    this.fileDialogvisiable = true;
    this.showTip = true;
  }

	doDownloadDialogClose() {
		this.fileDialogvisiable = false;
	}

	doDialogClose() {
		this.uploadDialogShow = false;
	}

	doTagsDialogClose() {
		this.uploadTagsDialogShow = false
    }
	doUploadPaidMemberDialogClose() {
		this.uploadPaidMemberDialogShow = false
    }

	doRemoteMethod(value: string) {
		this.getStore(value);
	}

	doToggle(flag: boolean) {
		if (flag) {
			this.tableHeight = 195;
		} else {
			this.tableHeight = 450;
		}
	}

	doExport() {
		this.dialogShow = true;
	}

	/**
	 * 查询
	 */
	onSearch() {
		this.page.currentPage = 1;
		if (this.fromMarketingCenter !== null) {
			this.query.marketingCenterEquals = this.fromMarketingCenter.id;
		} else {
			this.query.marketingCenterEquals = null;
		}
		this.getMemberList();
	}

	/**
	 * 重置
	 */
	onReset() {
		this.fromMarketingCenter = null;
		this.identCode = null;
		this.registerDate = "";
		this.createDate = [];
		this.activeDate = "";
		this.birthdayDate = "";
		this.childDate = "";
		this.birthdayBegin = null;
		this.birthdayEnd = null;
		this.identType = "all";
		this.birthdayType = "date";
		this.query = new MemberFilter();
		this.query.stateEquals = "Using";
		this.identPlaceholder = this.formatI18n("/会员/会员资料", "请输入手机号/会员号/实体卡号");
		this.searchChannel = {}
		// PHX-14757 fix：重置清除会员渠道
		this.memberChannelTypeFilter = []
		this.getMemberList();
		this.getStore("");
	}

	/**
	 * 去详情
	 */
	doGoDtl(row: any) {
		this.$router.push({ name: "standard-member-dtl", query: { id: row.memberId } });
	}

	/**
	 * 分页页码改变的回调
	 * @param val
	 */
	onHandleCurrentChange(val: number) {
		this.page.currentPage = val;
		this.getMemberList();
	}

	/**
	 * 每页多少条的回调
	 * @param val
	 */
	onHandleSizeChange(val: number) {
		this.page.size = val;
		this.getMemberList();
	}

	/**
	 * 表格排序条件
	 */
	onSortChange({ column, prop, order }: any) {
		// todo
	}

	doDateChange() {
		this.$forceUpdate();
	}

	doExportDialogClose() {
		this.exportDialogShow = false;
	}

	doSummit(flag: any) {
		this.transParams();
		this.query.page = this.page.currentPage - 1;
		this.query.pageSize = this.page.size;
		MemberApi.exportMember(this.query)
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					this.showTip = true;
					this.fileDialogvisiable = true;
				}
			})
			.catch((error: any) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}

	doBatchExport() {
		this.exportDialogShow = true;
	}

	getMemberList() {
		if (this.page.currentPage > 1000) {
			this.$message.warning(
				this.formatI18n("/会员/会员资料/列表/查询1000条以后的/会员分页查询上限为10000条，若想获取完整结果，请使用批量导出功能 或 输入更多查询条件。")
			);
			return;
		}
		this.transParams();
		this.query.page = this.page.currentPage - 1;
		this.query.pageSize = this.page.size;
		this.storeQuery();
		MemberApi.query(this.query)
			.then((resp: any) => {
        if(resp.code === 2000) {
          this.page.total = resp.total;
          this.tableData = resp.data;
        } else {
          throw new Error(resp.msg)
        }
			})
			.catch((error: any) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}

	formatter(row: any, column: any) {
		if (row.registerChannel && row.registerChannel.type) {
			if (row.registerChannel.type === "third") {
				return this.formatI18n("/会员/会员资料", "第三方");
			} else if (row.registerChannel.type === "alipay") {
				return this.formatI18n("/会员/会员资料", "支付宝");
			} else if (row.registerChannel.type === "weixin") {
				return this.formatI18n("/会员/会员资料", "微信");
			} else if (row.registerChannel.type === "store") {
				return this.formatI18n("/会员/会员资料", "门店注册");
			} else if (row.registerChannel.type === "phoenix") {
				return "CRM";
			} else if (row.registerChannel.type === "weixinApp") {
				return this.formatI18n("/会员/会员资料", "海鼎会员小程序");
			} else {
				if (row.registerChannel && row.registerChannel.id) {
					return row.registerChannel.id;
				} else {
					return "--";
				}
			}
		} else {
			return "--";
		}
	}

	// 打开批量修改门店弹框
	private doModifyStore(): void {
		this.storeDialogShow = true;
	}

	private storeDialogClose(): void {
		this.storeDialogShow = false;
	}

	private getMemberLevel() {
		GradeApi.listGrade("")
			.then((resp: any) => {
				this.memberLevel = resp.data;
			})
			.catch((error: any) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}

	private storeUploadSuccess() {
		this.fileDialogvisiable = true;
		this.showTip = true;
	}

	private birthdayCheck() {
		if (this.birthdayType === "year") {
			if (this.birthdayBegin != null && this.birthdayEnd != null) {
				if (DateUtil.clearTime(new Date(this.birthdayBegin)).getTime() > DateUtil.clearTime(new Date(this.birthdayEnd)).getTime()) {
					this.$message.error(this.formatI18n("/会员/会员资料/生日/开始年份不能小于结束年份"));
					return false;
				}
			}
		}
		return true;
	}

	/**
	 * 参数转换
	 */
	private transParams() {
		if (!this.birthdayCheck()) {
			return;
		}
		if (!this.childDate) {
			this.childDate = [];
		}
		if (this.createDate && this.createDate.length > 0) {
			this.query.createdGreaterOrEquals = new Date(this.createDate[0] ? this.createDate[0] + " 00:00:00" : "");
			this.query.createdLess = new Date(this.createDate[1] ? this.createDate[1] + " 23:59:59" : "");
		} else {
			this.query.createdGreaterOrEquals = null;
			this.query.createdLess = null;
		}
		if (this.activeDate && this.activeDate.length > 0) {
			this.query.activeTimeGreaterOrEquals = new Date(this.activeDate[0] ? this.activeDate[0] + " 00:00:00" : "");
			this.query.activeTimeLess = new Date(this.activeDate[1] ? this.activeDate[1] + " 23:59:59" : "");
		} else {
			this.query.activeTimeGreaterOrEquals = null;
			this.query.activeTimeLess = null;
		}
		if (this.registerDate && this.registerDate.length > 0) {
			this.query.registerTimeGreaterOrEquals = new Date(this.registerDate[0] ? this.registerDate[0] + " 00:00:00" : "");
			this.query.registerTimeLess = new Date(this.registerDate[1] ? this.registerDate[1] + " 23:59:59" : "");
		} else {
			this.query.registerTimeGreaterOrEquals = null;
			this.query.registerTimeLess = null;
		}

		if (this.birthdayDate && this.birthdayDate.length > 0) {
			this.query.birthMonthDayGreaterOrEquals = this.birthdayDate[0] ? this.birthdayDate[0] : null;
			this.query.birthMonthDayLessOrEquals = this.birthdayDate[1] ? this.birthdayDate[1] : null;
		} else {
			this.query.birthMonthDayGreaterOrEquals = null;
			this.query.birthMonthDayLessOrEquals = null;
		}

		if (this.birthdayBegin) {
			this.query.birthDayGreaterOrEquals = this.birthdayBegin;
		}

		if (this.birthdayEnd) {
			let day = DateUtil.getDayCountOfYear(new Date(this.birthdayEnd).getFullYear());
			let date = DateUtil.addDate(new Date(this.birthdayEnd), day - 1);
			this.query.birthDayLessOrEquals = DateUtil.modifyTime(date, 23, 59, 59);
		}

		this.query.identCode = null;
		this.query.mobileEquals = null;
		this.query.identCodeEquals = null;
		this.query.hdCardCardNumberEquals = null;
		this.query.crmCodeEquals = null;

		if (this.identCode != null) {
			if (this.identType == "mobile") {
				// 手机号码
				this.query.mobileEquals = this.identCode;
			} else if (this.identType == "hdCardCardNumber") {
				// 实体卡号
				this.query.hdCardCardNumberEquals = this.identCode;
			} else if (this.identType == "crmCode") {
				// 会员号
				this.query.crmCodeEquals = this.identCode;
			} else {
				this.query.identCodeEquals = this.identCode;
			}
		}

		this.channels.forEach((item: any) => {
			if (this.query.registerChannelEquals === item.channel.typeId) {
				this.query.registerChannelEquals = item.channel.type;
			}
		});
	}

	private getStore(value: string) {
		let params: RSOrgFilter = new RSOrgFilter();
		params.idNameLikes = value;
		params.page = 0;
		params.pageSize = 0;
		OrgApi.query(params)
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					this.stores = resp.data;
				}
			})
			.catch((error) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}

	private getChannels() {
		let param: RSChannelManagementFilter = new RSChannelManagementFilter();
		ChannelManagementApi.query(param)
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					this.channels = resp.data;
				}
			})
			.catch((error: any) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}

  // 批量冻结
  doBatchFreeze() {
    this.$refs.uploadFreeze.open()
  }

  uploadSuccess() {
    this.onReset()
    this.fileDialogvisiable = true
  }

  // --- PHX-14618：360画像UI调整需求 ---
	memberChannelTypes: Array<any> = [{
		name: this.i18n("微信"),
		code: "weiXin",
	}, {
		name: this.i18n("支付宝"),
		code: "aliPay",
	}, {
		name: this.i18n("微盟"),
		code: "weimob",
	}, {
		name: this.i18n("抖音"),
		code: "douYin",
	}, {
		name: this.i18n("企微"),
		code: "qiWei",
	}, {
		name: this.i18n("有赞"),
		code: "youZan",
	}];
	memberChannelTypeFilter: Array<any> = []

	onMemberChannelTypeChange(e: Array<any>) {
		console.log(e);
		if (e && e.length > 0) {
			this.query.memberChannelTypeIn = e.map(v => v.code);
		} else {
			this.query.memberChannelTypeIn = null;
			this.query.memberChannelIdEquals = null;
		}
	}

	getMemberChannelLabel(channels: Array<string>) {
		if (!channels || channels.length == 0) return "--";
		return this.memberChannelTypes.filter(e => channels.indexOf(e.code) != -1).map(e => e.name).join(",");
	}
}
