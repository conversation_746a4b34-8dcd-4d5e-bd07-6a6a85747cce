import {Component, Prop, Vue} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'

@Component({
    name: 'CheckStoreDialog',
    components: {
        FormItem
    }
})
export default class CheckStoreDialog extends Vue {
    @Prop()
    data: any
    @Prop({
        type: Boolean,
        default: false
    })
    dialogShow: boolean

    get getOrder() {
        let str: any = '序号'
        return str
    }
    doBeforeClose(done: any) {
        this.$emit('dialogClose')
        done()
    }
    doModalClose() {
        this.$emit('dialogClose')
    }
}
