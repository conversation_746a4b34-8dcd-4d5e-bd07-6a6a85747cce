<template>
  <div class="get-goods-range">
    <div class="custom-switch">
      <el-radio-group v-model="switchFlag" @change="changeSwitch">
        <el-radio :label="false">{{i18n('按优惠方式记流水')}}</el-radio>
        <el-radio :label="true">
          <span v-if="!switchFlag">{{i18n('按支付方式记流水')}}</span>
          <span v-else>{{i18n('按支付方式记流水-记账总金额')}}：{{getAllAmount}}{{i18n('元')}}</span>
        </el-radio>
      </el-radio-group>
      <!-- <el-switch v-model="switchFlag" :width="80" @change="changeSwitch"></el-switch>
      <span style="padding-left: 10px;color:#999;" v-if="switchFlag">{{formatI18n('/公用/券模板/提货券/用券商品', '关闭后将按优惠方式记流水')}}</span>
      <span style="padding-left: 10px;color:#999;" v-if="!switchFlag">{{formatI18n('/公用/券模板/提货券/用券商品', '开启后将按支付方式记流水')}}</span>
      <p v-if="!switchFlag">{{formatI18n('/公用/券模板/提货券/用券商品', '按优惠方式记流水')}}</p>
      <p v-else>{{formatI18n('/公用/券模板/提货券/用券商品', '按支付方式记流水-记账总金额')}}：{{getAllAmount}}{{formatI18n('/营销/积分活动/积分活动/积分抵现活动/立即新建/单笔抵现上限/元')}}</p> -->
    </div>
    <div>
      <span class="plain-btn-blue" @click="doAdd" v-if="data.length <= 9">
        {{i18n('添加商品')}}
      </span>
      <span class="gray-tips" style="margin-left:10px">
        {{i18n('请至少选择1件商品，最多能选择10个；同一商品不允许重复选择')}}
      </span>
    </div>
    <div v-if="data.length > 0">
      <el-table :data="data" style="width: 100%" stripe>
        <el-table-column :label="i18n('商品')" fixed prop="useDate">
          <template slot-scope="scope">
            [{{scope.row.goods.id}}]{{scope.row.goods.name}}
          </template>
        </el-table-column>
        <el-table-column :label="formatI18n('/公用/券模板/提货券/用券商品', '售价(元)')" fixed prop="useDate">
          <template slot-scope="scope">
            <div v-if="scope.row.price">￥{{scope.row.price | fmt}}</div>
            <div v-else>--</div>
          </template>
        </el-table-column>
        <el-table-column :label="formatI18n('/公用/券模板/提货券/用券商品', '记账金额(元)')" fixed prop="useDate" v-if="switchFlag">
          <template slot-scope="scope">
            <el-input style="width: 90px" v-model="scope.row.bookPayPrice" @change="doChange(scope.$index)" class="number-type" type="number"
              :min="0.01" :max="999999.00">
            </el-input>
          </template>
        </el-table-column>
        <el-table-column :label="formatI18n('/公用/券模板/提货券/用券商品', '商品数量(件)')" fixed prop="useDate">
          <template slot-scope="scope">
            <el-input style="width: 80px" class="number-type" v-model="scope.row.qty" type="number" :min="0.01" :max="999999.00"
              @change="doChange(scope.$index)">
              <template slot="append">{{formatI18n('/营销/积分活动/积分活动/商品组合满数量加送积分活动/编辑页面/加送积分规则/件')}}</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column :label="formatI18n('/设置/权限/角色管理/包含用户','操作')" fixed prop="useDate">
          <template slot-scope="scope">
            <el-button type="text" @click="doClear(scope.$index)">{{formatI18n('/公用/按钮','清空')}}</el-button>
            <el-button type="text" @click="doDelete(scope.$index)">{{formatI18n('/公用/按钮','删除')}}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <GoodsSelectorDialog :goodsMatchRuleMode="goodsMatchRuleMode" ref="selectGoodsScopeDialog" @summit="doSubmitGoods" />
  </div>
</template>

<script lang="ts" src="./GetGoodsRange.ts">
</script>

<style lang="scss">
.get-goods-range {
  width: 740px;

  .el-table th {
    background-color: #f0f2f6;
    & > .cell {
      font-size: 13px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #242633;
    }
  }
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

input[type="number"] {
  -moz-appearance: textfield;
}

.custom-switch {
  .el-switch__core {
    width: 54px;
  }
  .el-switch__core::before {
    content: "禁用";
    position: absolute;
    top: 0;
    right: 5px;
    color: #fff;
  }
  .is-checked .el-switch__core::before {
    content: "启用";
    position: absolute;
    top: 0;
    left: 5px;
    color: #fff;
  }
}
</style>