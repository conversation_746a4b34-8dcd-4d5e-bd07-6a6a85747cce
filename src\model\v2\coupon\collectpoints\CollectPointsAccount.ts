/*
 * @Author: 黎钰龙
 * @Date: 2025-04-24 10:48:15
 * @LastEditTime: 2025-04-24 10:48:43
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\v2\coupon\collectpoints\CollectPointsAccount.ts
 * 记得注释
 */
import MemberIdent from "model/common/member/MemberIdent"

export default class CollectPointsAccount extends MemberIdent {
  // 活动号
  activityNumber: Nullable<string> = null
  // 活动名称
  activityName: Nullable<string> = null
  // 会员id
  memberId: Nullable<string> = null
  // 集点
  collectPoints: Nullable<number> = null
  // 参与次数
  takePartTimes: Nullable<number> = null
}