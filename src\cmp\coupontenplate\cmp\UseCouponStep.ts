import { Component, Inject, Prop, Vue, Watch } from "vue-property-decorator";
import SelectGoodsRangeDialog from "pages/deposit/mbrdeposit/active/dialog/SelectGoodsRangeDialog.vue";
import ImportDialog from "cmp/importdialog/ImportDialog.vue";
import AmountToFixUtil from "util/AmountToFixUtil";
import CouponThreshold from "model/common/CouponThreshold";
import I18nPage from "common/I18nDecorator";
import BrowserMgr from "mgr/BrowserMgr";

@Component({
  name: "UseCouponStep",
  components: {
    SelectGoodsRangeDialog,
    ImportDialog,
  },
})
@I18nPage({
  prefix: [
    '/公用/券模板/单品折扣券/用券门槛',
    '/公用/券模板',
    '/营销/券礼包活动/核销第三方券新建界面',
    '/储值/预付卡/卡模板/编辑页面'
  ]
})
export default class UseCouponStep extends Vue {
  $refs: any;
  ruleForm: any = {
    useCouponStep: "step1",
    step1: "",
    step2: "",
    step4: {
      thresholdAmount: '',
      maxAmount: ''
    },
    useThresholdType: "AMOUNT",
  };
  $eventHub: any;
  rules: any = {};
  @Prop({
    default: true,
    type: Boolean
  })
  showQty: Boolean
  @Prop({
    type: Boolean,
    default: false
  })
  duplicateDiscount: Boolean;
  @Prop()
  amount: string;
  @Prop()
  type: string;
  @Prop()
  value: any;
  @Prop({ default: 99999999 })
  max: number;
  @Prop({ default: true })
  isCheckAmountAndThreshold: boolean  //是否校验券面额和用券门槛的大小关系，配置控制，默认开启
  @Watch("value")
  onDataChange(value: CouponThreshold) {
    if (value && value.thresholdType) {
      if (value.useThresholdType === "AMOUNT" && value.threshold) {
        this.ruleForm.useCouponStep = "step2";
        this.ruleForm.step2 = value.threshold;
      } else if (value.useThresholdType === "QTY") {
        this.ruleForm.useCouponStep = "step3";
        this.ruleForm.step1 = value.threshold;
      } else if (value.useThresholdType === "DISCOUNT_PER_AMOUNT" && this.hasPermissionEveryfullreduction) {
        this.ruleForm.useCouponStep = "step4";
        this.ruleForm.step4.thresholdAmount = value.threshold;
        this.ruleForm.step4.maxAmount = value.value;
      } else {
        this.ruleForm.useCouponStep = "step1";
      }
    } else {
      this.ruleForm.useCouponStep = "step1";
    }
    this.commitLimitChange();
  }
  doTypeStepChange() {
    this.ruleForm.step2 = "";
    this.ruleForm.step1 = "";
    this.ruleForm.step4 = {
      thresholdAmount: '',
      maxAmount: ''
    }
    if (this.ruleForm.useCouponStep === 'step2') {
      this.ruleForm.step2 = this.ruleForm.step2 ? this.ruleForm.step2 : 0.01
    }
    this.$refs.ruleForm.validate()
    this.commitLimitChange();
    this.$emit("input", this.translateParams());
    this.$emit("change");
  }
  commitLimitChange() {
    setTimeout(() => {
      let noLimit = false;
      // PHX-11218 无门槛 也可以选择 叠加用券-订单级别-不可叠加
      // if (this.ruleForm.useCouponStep === "step1") {
      //   noLimit = true;
      // } else {
      //   noLimit = false;
      // }
      this.$eventHub.$emit("limitChange", noLimit);
    }, 1);
  }
  mounted() {
    this.commitLimitChange();
  }
  created() {
    let value = this.value;
    if (value && value.thresholdType) {
      if (value.useThresholdType === "AMOUNT") {
        this.ruleForm.useCouponStep = "step2";
        this.ruleForm.step2 = value.threshold;
      } else if (value.useThresholdType === "QTY") {
        this.ruleForm.useCouponStep = "step3";
        this.ruleForm.step1 = value.threshold;
      } else if (value.useThresholdType === "DISCOUNT_PER_AMOUNT" && this.hasPermissionEveryfullreduction) {
        this.ruleForm.useCouponStep = "step4";
        this.ruleForm.step4.thresholdAmount = value.threshold
        this.ruleForm.step4.maxAmount = value.value
      } else {
        this.ruleForm.useCouponStep = "step1";
      }
    } else {
      this.ruleForm.useCouponStep = "step1";
    }
    this.rules = {
      step3: [
        {
          required: true,
          validator: (rule: any, value: any, callback: any) => {
            if (this.ruleForm.useCouponStep === "step3") {
              if (!this.ruleForm.step1) {
                callback(this.formatI18n("/公用/券模板", "请输入必填项"));
              } else {
                callback();
              }
            } else {
              callback();
            }
          },
          trigger: "change",
        },
      ],
      step2: [
        {
          required: true,
          validator: (rule: any, value: any, callback: any) => {
            if (this.ruleForm.useCouponStep === "step2") {
              if (value) {
                if (this.amount && Number(value) < Number(this.amount) && this.isCheckAmountAndThreshold) {
                  callback(
                    this.formatI18n(
                      "/营销/券礼包活动/核销第三方券新建界面/当用券门槛小于券面额/点击保存js提示信息",
                      "用券门槛必须大于等于券面额"
                    )
                  );
                } else {
                  callback();
                }
              } else {
                callback(this.formatI18n("/公用/券模板", "请输入必填项"));
              }
            } else {
              callback();
            }
          },
          trigger: "change",
        },
      ],
      step4_thresholdAmount: [
        {
          required: true,
          validator: (rule: any, value: any, callback: any) => {
            if (this.ruleForm.useCouponStep === "step4") {
              if (this.ruleForm.step4.thresholdAmount) {
                if (Number(this.ruleForm.step4.thresholdAmount) < Number(this.amount) && this.isCheckAmountAndThreshold) {
                  callback(
                    this.formatI18n(
                      "/营销/券礼包活动/核销第三方券新建界面/当用券门槛小于券面额/点击保存js提示信息",
                      "用券门槛必须大于等于券面额"
                    )
                  );
                } else {
                  callback();
                }
              } else {
                callback(this.formatI18n("/公用/券模板", "请输入必填项"));
              }
            } else {
              callback();
            }
          },
          trigger: "change",
        },
      ],
      step4_maxAmount: [
        {
          required: true,
          validator: (rule: any, value: any, callback: any) => {
            if (this.ruleForm.useCouponStep === "step4") {
              if (this.ruleForm.step4.maxAmount) {
                if (Number(this.ruleForm.step4.maxAmount) < Number(this.amount) && this.isCheckAmountAndThreshold) {
                  callback(
                    this.i18n("最多减免金额需大于等于券面额")
                  );
                } else {
                  callback();
                }
              } else {
                callback(this.formatI18n("/公用/券模板", "请输入必填项"));
              }
            } else {
              callback();
            }
          },
          trigger: "change",
        },
      ],
    };
  }
  destroyed() {
    this.ruleForm.useCouponStep = "step1" as any;
    if (this.$refs["ruleForm"]) {
      this.$refs["ruleForm"].resetFields();
    }
  }
  doStepChange(pos: number) {

    if (pos === 1) {
      this.ruleForm.step1 = AmountToFixUtil.formatNumber(
        this.ruleForm.step1,
        this.max,
        1
      );
    } else if (pos === 2) {
      this.ruleForm.step2 = AmountToFixUtil.formatAmount(
        this.ruleForm.step2,
        this.max,
        0.01,
        ""
      );
    } else if (pos === 4) {
      this.ruleForm.step4.thresholdAmount = AmountToFixUtil.formatAmount(
        this.ruleForm.step4.thresholdAmount,
        this.max,
        0.01,
        ""
      );
      this.ruleForm.step4.maxAmount = AmountToFixUtil.formatAmount(
        this.ruleForm.step4.maxAmount,
        this.max,
        0.01,
        ""
      );
    }
    this.$emit("input", this.translateParams());
    this.$emit("change");
  }

  //将表单数据转换为父组件需要的格式
  translateParams() {
    let obj: CouponThreshold = new CouponThreshold();
    if (this.ruleForm) {
      if (this.ruleForm.useCouponStep === "step1") {
        obj.value = null;
        obj.threshold = null;
        obj.thresholdType = "NONE" as any;
        obj.useThresholdType = null;
      } else if (this.ruleForm.useCouponStep === "step2") {
        obj.value = 1;
        obj.threshold = this.ruleForm.step2;
        obj.thresholdType = "REUSEABLE" as any;
        obj.useThresholdType = "AMOUNT";
      } else if (this.ruleForm.useCouponStep === "step3") {
        obj.value = 1;
        obj.threshold = this.ruleForm.step1;
        obj.thresholdType = "NONREUSEABLE" as any;
        obj.useThresholdType = "QTY";
      } else if (this.ruleForm.useCouponStep === "step4") {
        if(this.hasPermissionEveryfullreduction) {  //如果有step4的权限
          obj.threshold = this.ruleForm.step4.thresholdAmount;
          obj.value = this.ruleForm.step4.maxAmount;
          obj.thresholdType = "NONREUSEABLE" as any;
          obj.useThresholdType = "DISCOUNT_PER_AMOUNT";
        } else {
          obj.value = null;
          obj.threshold = null;
          obj.thresholdType = "NONE" as any;
          obj.useThresholdType = null;
        }
      }
    }
    return obj;
  }
  doValidate() {
    let p0 = new Promise<void>((resolve, reject) => {
      this.$refs.ruleForm.validate((valid: any) => {
        if (valid) {
          resolve();
        } else {
          reject();
        }
      });
    });
    return p0;
  }
  get formateThresholdStr() {
    let str = ''
    str = this.i18n('减{0}元，最多可减')
    str = str.replace(/\{0\}/g, this.amount || '--');
    return str
  }

  /**
   * 根据系统配置获取用券门槛重复满减权限
   */
  get hasPermissionEveryfullreduction() {
    let permission = BrowserMgr.LocalStorage.getItem('sysConfig')
    if (permission.enableCouponEveryfullreduction) {
      return true
    } else {
      return false
    }
  }
}
