import PageRequest from 'model/default/PageRequest'
import Channel from "model/common/Channel";

// 标签查询
export default class TradeFilter {
  // 会员id
  memberId: Nullable<string> = null
  //门店Id
  storeIdEquals: Nullable<string> = null
  // 交易渠道
  channel: Nullable<Channel> = null
  //交易号
  tradeNo: Nullable<string> = null
  //交易时间在[,)之间
  tranTimeBetweenClosedOpen:  Date[] = []
  //销售单类型
  saleType:  string[] = []
  // 页码
  page: Nullable<number> = null;
  // 页面大小
  pageSize: Nullable<number> = null;
}
