<!--
 * @Description:
 * @Version: 1.0
 * @Autor: 司浩
 * @Date: 2021-10-18 16:11:04
 * @LastEditors: 苏国友 <EMAIL>
 * @LastEditTime: 2023-04-11 18:30:48
-->
<template>
  <div class="coupon-template-label">
    <BreadCrume :panelArray="panelArray">
    </BreadCrume>
    <div class="label-main">
      <div class="main-header">
        <div class="header-label">{{ labelNumStr }}</div>
        <!-- <div class="header-input">
          <el-input :placeholder="formatI18n('/权益/券/券模板/请输入标签名称')" suffix-icon="el-icon-search" v-model="valueEquals" @change="doChange">
          </el-input>
        </div> -->
      </div>
      <div class="labelManage_content_tag">
        <commodityLabel class="labelManage_content_tag_item" v-for="v in labelList" :key="v.uuid" :label-id="v.uuid"
          :label-name.sync="v.value" :tag-type.sync="v.type" @editSuccess="editSuccess"></commodityLabel>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./CouponTemplateLabel.ts"></script>

<style lang="scss" scoped>
.coupon-template-label {
  width: 100%;
}

.label-main {
  width: 100%;
  height: 95%;
  overflow-y: auto;
  overflow-x: hidden;
  background: #ffffff;
  padding: 24px 32px;

  .main-header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding-bottom: 16px;

    .header-label {
      flex: 1;
      height: 20px;
      font-size: 14px;
      font-weight: 600;
      color: #242633;
      line-height: 20px;
    }

    .header-input {
      width: 272px;
      height: 32px;
    }
  }
  .labelManage_content_tag {
    display: flex;
    flex-wrap: wrap;
    &_item {
      margin-right: 16px;
      margin-bottom: 16px;
    }
  }
}
</style>