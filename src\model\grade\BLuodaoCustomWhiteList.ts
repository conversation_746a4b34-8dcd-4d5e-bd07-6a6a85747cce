/*
 * @Author: 黎钰龙
 * @Date: 2024-04-15 19:34:05
 * @LastEditTime: 2024-04-16 11:19:48
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\grade\BLuodaoCustomWhiteList.ts
 * 记得注释
 */
import IdName from "model/common/IdName"

export default class BLuodaoCustomWhiteList {
  //uuid
  uuid: Nullable<string> = null
  // 门店列表
  orgList: IdName[] = []
  // 券状态 UN_WRITTEN_OFF:未核销；WRITTEN_OFF:已核销
  states: string[] = []
}