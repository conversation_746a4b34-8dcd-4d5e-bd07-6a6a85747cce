/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-09-18 14:21:32
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\weixin\qrcode\WeixinQrCodeApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import EmployeePromotionCode from 'model/member/EmployeePromotionCode'
import StorePromotionCode from "model/member/StorePromotionCode";

export default class WeixinQrCodeApi {
  /**
   * 下载全部门店二维码
   *
   */
  static downloadAllStoresQrCode(code: string): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/weixin-qrcode/downloadAllStoresQrCode`, {
      params: {
        code: code
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 下载指定门店二维码
   *
   */
  static downloadStoreQrCode(code: string): Promise<Response<string>> {
    return ApiClient.server().get(`/v1/weixin-qrcode/downloadStoreQrCode`, {
      params: {
        code: code
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 取得领卡链接
   *
   */
  static getReceiveCardUrl(): Promise<Response<string>> {
    return ApiClient.server().get(`/v1/weixin-qrcode/getReceiveCardUrl`, {
    }).then((res) => {
      return res.data
    })
  }


    /**
   * 下载全部员工二维码
   *
   */
     static downloadAllEmployeeStoresQrCode(code: string): Promise<Response<string>> {
      return ApiClient.server().post(`v1/weixin-qrcode/downAllEmployeeStoreQrCode`, {
        params: {
          code: code
        }
      }).then((res) => {
        return res.data
      })
    }
  

  /**
   * 下载指定门店二维码
   *
   */
  static downloadEmployeeQrCode(code: string): Promise<Response<string>> {
    return ApiClient.server().get(`/v1/weixin-qrcode/downloadEmployeeQrCode`, {
      params: {
        code: code
      }
    }).then((res) => {
      return res.data
    })
  }


  

    /**
   * 微信会员卡投放:员工二维码下载全部
   *
   */
     static downloadAllWeixinCardEmployeeQrCode(code: string): Promise<Response<string>> {
      return ApiClient.server().post(`v1/weixin-qrcode/downloadAllEmployeeQrCode`, {
        params: {
          code: code
        }
      }).then((res) => {
        return res.data
      })
    }

 
/**
   * 小程序二维码投放:（门店员工小程序二维码下载）下载全部
   *
   */
 static downloadAllStoreEmployeeInviteGiftQrCode(code: string): Promise<Response<string>> {
  return ApiClient.server().post(`v1/weixin-Applet-qrcode/download/all/employeeInviteGiftQrCode`, {
    params: {
      code: code
    }
  }).then((res) => {
    return res.data
  })
}



  /**
   * 门店员工储值二维码:下载全部
   *
   */
    static downloadAllStoreEmployeeQrCode(code: string): Promise<Response<string>> {
      return ApiClient.server().post(`v1/weixin-qrcode/downAllEmployeeStoreQrCode`, {
        params: {
          code: code
        }
      }).then((res) => {
        return res.data
      })
    }
  

  /**
   * 下载指员工储值二维码
   *
   */
  static downloadEmployeeStoreQrCode(code: string): Promise<Response<string>> {
    return ApiClient.server().get(`/v1/weixin-qrcode/downloadEmployeeStoreQrCode`, {
      params: {
        code: code
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 下载指员工小程序二维码
   *
   */
  static downloadEmployeeInviteGiftQrCode(code: string): Promise<Response<string>> {
    return ApiClient.server().get(`/v1/weixin-Applet-qrcode/download/employeeInviteGiftQrCode`, {
      params: {
        code: code
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 下载员工推广码
  * 下载员工推广码
  * 
  */
  static downloadEmployeePromotionCode(body: EmployeePromotionCode): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/weixin-Applet-qrcode/downloadEmployeePromotionCode`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 下载门店推广码
   * 下载门店推广码
   *
   */
  static downloadStorePromotionCode(body: StorePromotionCode): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/weixin-Applet-qrcode/downloadStorePromotionCode`, body, {
    }).then((res) => {
      return res.data
    })
  }
}
