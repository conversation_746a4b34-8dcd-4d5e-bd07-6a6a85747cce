import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import FormItem from 'cmp/formitem/FormItem';
import MyQueryCmp from 'cmp/querycondition/MyQueryCmp';
import I18nPage from 'common/I18nDecorator';
import Broken<PERSON><PERSON><PERSON>perate<PERSON><PERSON> from 'http/card/brokenCard/BrokenCardOperateApi';
import WriteMachine<PERSON><PERSON> from 'http/card/writeCard/WriteMachineApi';
import BrokenCardLog from 'model/brokenCard/BrokenCardLog';
import BrokenCardLogFilter from 'model/brokenCard/BrokenCardLogFilter';
import WriteCardMachineRequest from 'model/card/writeCard/WriteCardMachineRequest';
import PrePayCard from 'model/prepay/card/PrePayCard';
import { Component, Vue } from 'vue-property-decorator';

class Form {
  card: Nullable<PrePayCard> = null; // 已选卡
}

@Component({
  name: 'BadCardReset',
  components: {
    BreadCrume,
    FormItem,
    MyQueryCmp
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/卡/坏卡重制'
  ],
  auto: true
})
export default class BadCardReset extends Vue {
  $refs: any
  form: Form = new Form();
  state: 'UNSTART' | 'RESETTING' | 'FAILED' | 'SUCCESS' = 'UNSTART'
  topicText: string = ''
  cardList: PrePayCard[] = [] //查到的卡号列表
  currentWriteCard: Nullable<string> = null; // 当前读取的实体卡sn号
  logList: BrokenCardLog[] = []
  cardCodeLikes: string = ''  //操作日志 卡号类似于
  operateDateRange: Date[] = [] //操作日志 时间范围
  loading: boolean = false
  successCode: string = ''  //当前重制成功的卡号
  selectLoading: boolean = false
  page: any = {
    currentPage: 1,
    size: 10,
    total: 0
  }

  get panelArray() {
    return [
      {
        name: this.i18n("/公用/菜单/坏卡重制"),
        url: "",
      },
    ];
  }

  get rules() {
    return {
      card: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: ["blur", "change"] }]
    };
  }

  created() {
    this.queryLogList()
  }

  startReset() {
    this.$refs.form.validate().then(() => {
      // 重制流程：打开卡机 => 校验是否有卡 => 检验是否为空卡 => 清卡(接着校验是否空卡) => 制卡 => 读卡
      this.openMachine().then(this.resetProcess)
    })
  }

  doReset() {
    this.page.currentPage = 1
    this.page.size = 10
    this.cardCodeLikes = ''
    this.operateDateRange = []
    this.queryLogList()
  }

  doSearch() {
    this.page.currentPage = 1
    this.queryLogList()
  }

  // 查询操作日志
  queryLogList() {
    const params = new BrokenCardLogFilter()
    params.page = this.page.currentPage - 1
    params.pageSize = this.page.size
    params.cardCodeLikes = this.cardCodeLikes || null
    params.createdBetweenClosedClosed = this.operateDateRange?.length ? this.operateDateRange : null
    this.loading = true
    BrokenCardOperateApi.queryLog(params).then((res) => {
      if (res.code === 2000) {
        this.logList = res.data || []
        this.page.total = res.total || 0
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    }).finally(() => {
      this.loading = false
    })
  }

  async resetProcess() {
    this.topicText = this.form.card?.cardMedium === 'mag' ? this.i18n('请刷磁卡') : this.i18n("写卡中，请等待");
    const hasCard = await this.checkHasCard() // 校验是否有卡，拿到sn
    if (hasCard) {
      if (this.form.card?.cardMedium === 'mag') {
        //磁条卡无法判断空卡，所以直接进写卡流程
        return this.makeCardProcess()
      }
      const emptyRes = await this.checkIsEmpty()  // 校验是否空卡
      if (!emptyRes) {
        // 非空卡
        this.$confirm(this.i18n("该卡非空卡，确定清卡重制吗？"), this.i18n("重制"), {
          confirmButtonText: this.i18n("确认"),
          cancelButtonText: this.i18n("取消"),
        })
          .then(() => {
            this.clearAndMake()
          })
          .catch(() => {
            this.state = 'FAILED'
            this.topicText = this.i18n('重制失败')
          });
      } else {
        // rfic卡、ic卡如果是空卡，直接进写卡流程
        return this.makeCardProcess()
      }
    } else {
      this.state = 'FAILED'
      this.topicText = this.i18n("请在卡机放置新卡或检查卡是否已经放好");
    }
  }

  // 清卡并重新写卡
  async clearAndMake() {
    const clearRes = await this.clearCard()
    if (clearRes) {
      // 清卡成功且为空卡
      this.makeCardProcess()
    } else {
      // 清卡失败
      this.$confirm(this.i18n("清卡失败，确定重试吗？"), this.i18n("重制"), {
        confirmButtonText: this.i18n("确认"),
        cancelButtonText: this.i18n("取消"),
      })
        .then(() => {
          this.clearAndMake()
        })
        .catch(() => {
          this.state = 'FAILED'
          this.topicText = this.i18n('重制失败')
        });
    }
  }

  //写卡
  async makeCardProcess() {
    try {
      const makeRes = await this.makeCard(); //制卡
      const readCardRes = await this.readCard(); //读卡
      if (makeRes && readCardRes) {
        // 制卡+读卡完成后 通知后端
        this.resetSuccess();
      }
    } catch (error) {
      // 写卡失败
      this.$confirm(this.i18n("写卡失败，确定重试吗？"), this.i18n("重制"), {
        confirmButtonText: this.i18n("确认"),
        cancelButtonText: this.i18n("取消"),
      }).then(() => {
        this.clearAndMake()
      }).catch(() => {
        this.topicText = this.i18n('重制失败')
        this.state = 'FAILED'
      });
    }
  }

  doRemoteQuery(value: string) {
    if (value) {
      this.selectLoading = true
      BrokenCardOperateApi.getBroken(value).then((res) => {
        if (res.code === 2000) {
          this.cardList = res.data || []
        } else {
          throw new Error(res.msg!)
        }
      }).catch((error) => {
        this.$message.error(error.message)
      }).finally(() => {
        this.selectLoading = false
      })
    }
  }

  // 打开并初始化卡机
  openMachine() {
    return new Promise<void>((resolve,reject) => {
      WriteMachineApi.open(this.form.card?.cardMedium!)
        .then((res) => {
          if (res.code === 2000) {
            this.state = 'RESETTING';
            resolve()
          } else {
            throw new Error(res.msg || this.i18n("打开卡机失败，请检查"));
          }
        })
        .catch((error) => {
          let msg = error.message
          if (msg.indexOf('请检查网络设置') > -1) {
            msg = this.i18n("打开卡机失败，请检查")
          }
          this.$message.error(msg)
          this.topicText = this.i18n("打开卡机失败，请检查");
          reject()
        })
    })
  }

  // 检查卡机是否有卡，并返回实体卡号
  checkHasCard() {
    return new Promise((resolve, reject) => {
      WriteMachineApi.checkAndGet()
        .then((res) => {
          if (res.code === 2000) {
            if (res.data === 0) {
              // 0：未放置卡片
              resolve(false);
            } else {
              // 返回了sn号
              this.currentWriteCard = String(res.data);
              resolve(true);
            }
          } else {
            throw new Error(res.msg!);
          }
        })
        .catch((error) => {
          this.$message.error(this.i18n("检查是否有卡失败"));
          resolve(false);
        });
    });
  }

  // 清卡
  clearCard() {
    return new Promise((resolve, reject) => {
      WriteMachineApi.clear(this.currentWriteCard!)
        .then((res) => {
          if (res.code === 2000 && res.data) {
            // 清卡成功后，立刻校验空卡
            this.checkIsEmpty().then((readRes) => {
              if (readRes) {
                resolve(true);
              }
            });
          } else {
            throw new Error();
          }
        })
        .catch((error) => {
          this.$message.error(this.i18n("清卡失败"));
          resolve(false);
        });
    });
  }

  // 检测是否为空卡
  checkIsEmpty() {
    return new Promise((resolve, reject) => {
      if (!this.currentWriteCard) {
        reject();
        return;
      }
      WriteMachineApi.isEmpty(this.currentWriteCard)
        .then((res) => {
          if (res.code === 2000) {
            if (res.data === true) {
              // 空卡
              resolve(true);
            } else {
              resolve(false);
            }
          } else {
            throw new Error(res.msg!);
          }
        })
        .catch((error) => {
          this.$message.error(this.i18n("检查空卡失败"));
          reject(error);
        });
    });
  }

  // 开始写卡
  makeCard() {
    return new Promise((resolve, reject) => {
      const params = new WriteCardMachineRequest();
      params.sn = Number(this.currentWriteCard);
      params.innerCode = this.form.card?.code;
      params.code = this.form.card?.code;
      WriteMachineApi.make(params)
        .then((res) => {
          if (res.code === 2000 && res.data) {
            resolve(true);
          } else {
            throw new Error(res.msg!);
          }
        })
        .catch((error) => {
          this.$message.error(this.i18n("制卡失败"));
          reject(error);
        });
    });
  }

  // 读卡
  readCard() {
    return new Promise((resolve, reject) => {
      if (!this.currentWriteCard) {
        reject();
        return;
      }
      WriteMachineApi.read(this.currentWriteCard)
        .then((res) => {
          if (res.code === 2000) {
            resolve(true);
          } else {
            throw new Error(res.msg!);
          }
        })
        .catch((error) => {
          this.$message.error(this.i18n("读卡失败"));
          reject(error);
        });
    });
  }

  // 通知后端重制成功
  resetSuccess() {
    BrokenCardOperateApi.remakeCard(this.form.card?.code!).then((res) => {
      if (res.code === 2000) {
        this.state = 'SUCCESS'
        this.successCode = this.form.card?.code || ''
        this.form.card = null
        this.cardList = []
        this.queryLogList()
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
      this.state = 'FAILED'
      this.topicText = this.i18n('重制失败')
    })
  }

  onHandleCurrentChange(val: number) {
    this.page.currentPage = val;
    this.queryLogList();
  }

  onHandleSizeChange(val: number) {
    this.page.size = val;
    this.queryLogList();
  }
};