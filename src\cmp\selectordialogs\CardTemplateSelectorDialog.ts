/*
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:02
 * @LastEditTime: 2024-07-30 15:40:48
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\selectordialogs\CardTemplateSelectorDialog.ts
 * 记得注释
 */
import {Component, Prop} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import AbstractSelectDialog from './AbstractSelectDialog';
import CardTemplateFilter from "model/card/template/CardTemplateFilter";
import CardTemplateApi from "http/card/template/CardTemplateApi";
import CardTemplate from "model/card/template/CardTemplate";

@Component({
  name: 'CardTemplateSelectorDialog',
  components: {
    FormItem
  }
})
export default class CardTemplateSelectorDialog extends AbstractSelectDialog<CardTemplate> {
  colVal: any = 12

  @Prop({ default: () => new CardTemplateFilter() })
  filter: CardTemplateFilter

  created() {
    if (sessionStorage.getItem('locale') === 'en_US') {
      this.colVal = 24 
    }
  }

  reset(): void {
    this.filter.numberOrNameLikes = null
  }

  getId(ins: CardTemplate): string {
    // @ts-ignore
    return ins.number;
  }

  getName(ins: CardTemplate): string {
    // @ts-ignore
    return ins.name;
  }

  getResponseData(response: any): any {
    return response.data.result
  }

  getResponseTotal(response: any): any {
    return response.data.total
  }

  queryFun(): Promise<any> {
    this.filter.page = this.page.currentPage - 1
    this.filter.pageSize = this.page.size
    let oldMarketCenter = sessionStorage.getItem('marketCenter') || ''
		sessionStorage.setItem('marketCenter', this.marketCenter)
    return CardTemplateApi.query(this.filter).finally(()=>{
			sessionStorage.setItem('marketCenter', oldMarketCenter as string)
		});
  }
}
