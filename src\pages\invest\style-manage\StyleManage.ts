import {Component, Vue} from "vue-property-decorator";
import BreadCrume from "cmp/bread-crumb/BreadCrume.vue";
import FormItem from "cmp/formitem/FormItem";;
import I18nPage from "common/I18nDecorator";
import UploadImg from "cmp/upload-img/UploadImg";
import StyleManageApi from "http/stylemanage/StyleManageApi";
import StyleManageConfig from "model/stylemanage/StyleManageConfig";

@Component({
    name: "StyleManage",
    components: { BreadCrume, FormItem,UploadImg},
})

@I18nPage({
    prefix: [
        '/公用/菜单',
        '/公用/按钮',
        '/设置/小程序装修',
        '/设置/风格管理',
    ],
})
export default class StyleManage extends Vue {
    $refs: any;
    panelArray: any = [];
    // config: StyleManageConfig = this.defaultConfig();
    config: StyleManageConfig = new StyleManageConfig();
    rules: any = {
        themeColor: [
            {
                required: true,
                message: this.i18n("主题颜色必填"),
                trigger: "blur",
            },
        ],
        auxiliaryColor: [
            {
                required: true,
                message: this.i18n("辅助颜色必填"),
                trigger: "blur",
            },
        ],
        loadAnimation: [
            {
                required: true,
                message: this.i18n("加载动画必填"),
                trigger: "blur",
            },
        ],
    }
    created() {
        this.panelArray = [
            {
                name: this.formatI18n("/设置/风格管理", "风格管理"),
                url: "",
            },
        ];
        this.getStyleManageConfig();
    }
    defaultConfig(): StyleManageConfig {
        return {
            themeColor: "#FF3B3D",
            auxiliaryColor: "#FFEBEB",
            loadAnimation: "https://mtj-dev-cos-1345538464.cos.ap-shanghai.myqcloud.com/tencent-oss-server/rs/oss/v1/phoenix/o/0618_854577612672356352.gif"
        };
    }

    uploadImgChange(field: string) {
        this.$refs.form.validateField(field);
    }

    uploadColorChange(field: string) {
        this.$refs.form.validateField(field);
    }

    doSave() {
        this.$refs.form.validate((flag: boolean) => {
            if (flag) {
                StyleManageApi.save(this.config,false)
                    .then((res) => {
                        this.$message.success(this.i18n("保存成功"));
                        this.getStyleManageConfig();
                    })
                    .catch((rej) => {
                        this.$message.error(rej.message);
                    });
            }
        })
    }

    doRestoreDefault() {
        StyleManageApi.save(this.config,true)
            .then((res) => {
                this.$message.success(this.i18n('恢复成功'));
                this.getStyleManageConfig();
            })
            .catch((rej) => {
                this.$message.error(rej.message);
            });
    }

    doChangeTab() {}

    getStyleManageConfig() {
        StyleManageApi.get()
            .then((res) => {
                if (res.data) {
                    this.config = res.data;
                }
            })
            .catch((rej) => {
                this.$message.error(rej.message);
            });
    }
}
