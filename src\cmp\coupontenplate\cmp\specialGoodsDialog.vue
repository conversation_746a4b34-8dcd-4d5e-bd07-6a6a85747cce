<!--
 * @Author: mazheng<PERSON> <EMAIL>
 * @Date: 2023-02-22 16:38:25
 * @LastEditors: mazhengfa <EMAIL>
 * @LastEditTime: 2023-02-24 21:15:39
 * @FilePath: \phoenix-web-ui\src\cmp\coupontenplate\cmp\specialGoodsDialog.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-dialog append-to-body :close-on-click-modal="false" :title="i18n('特殊商品')" :visible.sync="dialogShow">
    <div style="min-width: 500px;">
      <el-row class="sgdHeader">
        <el-col :span="5" class="sgdCell">{{ i18n('商品条码') }}</el-col>
        <el-col :span="19" class="sgdCell">{{ i18n('用券记录方式') }}</el-col>
      </el-row>
      <div class="tableDataCon">
        <template>
          <el-row v-for="(item, index) in data" :key="index">
            <el-col :span="5" class="sgdCell">
              <div>
                {{ item.barcode }}
              </div>
            </el-col>
            <el-col :span="19" class="sgdCell">
              {{ setAmounStr(item.amount) }}
              <!-- 最多{{item.amount}}元算支付方式，剩余算优惠方式 -->
            </el-col>
          </el-row>
        </template>
      </div>
    </div>
  </el-dialog>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import I18nPage from "common/I18nDecorator";

@Component({
	name: "SpecialGoodsDialog",
	components: {}
})
@I18nPage({
  prefix: ["/公用/券模板"],
  auto: true
})
export default class SpecialGoodsDialog extends Vue {
	dialogShow: boolean = false;
  @Prop()
  data: any[];
  open() {
    this.dialogShow = true
  }
  get setAmounStr() {
    return (amount: any) => {
      return this.i18n('最多{0}元算支付方式，剩余算优惠方式').replace(/\{0\}/g, String(amount))
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog {
  min-width: 1000px;
}
.sgdHeader {
  margin-top: 10px;
  border-right: 1px solid #aaa;
  //border-top: 1px solid #aaa;
}
.sgdCell {
  line-height: 30px;
  text-align: center;
  border-left: 1px solid #aaa;
  border-top: 1px solid #aaa;
}
.tableDataCon {
  border-right: 1px solid #aaa;
  max-height: 500px;
  //overflow: auto;
  overflow: overlay;
  border-bottom: 1px solid #aaa;
  .sgdCell {
    //height: 36px;
    line-height: 36px;
  }
}
</style>