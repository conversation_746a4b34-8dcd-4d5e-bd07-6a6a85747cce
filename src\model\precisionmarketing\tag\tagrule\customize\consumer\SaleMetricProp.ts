import MetricProp from 'model/precisionmarketing/tag/tagrule/customize/consumer/MetricProp'
import StorePropValue from 'model/precisionmarketing/tag/tagrule/customize/member/props/StorePropValue'
import TimeProp from "model/precisionmarketing/tag/tagrule/customize/consumer/TimeProp";

export default class SaleMetricProp {
  // 时间条件：lstSeven-近7天;lstThirty-近30天;lstThreeMonth-近3个月;lstHalfYear-近半年;lstYear-近一年;thisMonth-本月;prevMonth-上月;thisYear-今年;
  prop: Nullable<string> = null
  // 发生门店满足，可选筛选条件
  store: Nullable<StorePropValue> = null
  // 发生时间满足，可选筛选条件
  timeProp: Nullable<TimeProp> = null
  // 指标类型以及区间
  metricProp: Nullable<MetricProp> = null
}