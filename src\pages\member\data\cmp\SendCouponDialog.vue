<!--
 * @Author: 黎钰龙
 * @Date: 2024-05-17 16:43:00
 * @LastEditTime: 2024-05-20 13:43:57
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\member\data\cmp\SendCouponDialog.vue
 * 记得注释
-->
<template>
  <div class="send-coupon-container">
    <el-dialog append-to-body :close-on-click-modal="false" :title="i18n('/会员/会员资料/详情界面/会员资产转移/发券')" :visible.sync="couponSendQtyFlag">
      <div @click="open" class="plain-btn-blue">
        {{i18n('修改')}}
      </div>
      <el-table :data="couponList" style="width:100%;margin-top:8px">
        <el-table-column :label="i18n('/权益/券/券模板/券模板')" width="264">
          <template slot-scope="tableScope">
            {{tableScope.row.coupons.name}}
          </template>
        </el-table-column>
        <el-table-column :label="i18n('/营销/大转盘活动/赠送数量')" width="166">
          <template slot-scope="tableScope">
            <AutoFixInput :min="1" :max="100" :fixed="0" style="width: 126px" v-model="tableScope.row.qty"
              :appendTitle="i18n('/储值/预付卡/电子礼品卡活动/编辑页面/张')">
            </AutoFixInput>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('/储值/预付卡/预付卡查询/列表页面/操作')">
          <template slot-scope="tableScope">
            <span class="span-btn" @click="removeCoupon(tableScope.$index)">
              {{i18n('删除')}}
            </span>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer" style="position: relative;top: -10px;">
        <el-button size="small" @click="doCancel()">{{ formatI18n('/公用/按钮/取消') }}</el-button>
        <el-button size="small" type="primary" @click="doConfirm()">{{ formatI18n('/公用/按钮/确定') }}</el-button>
      </div>
    </el-dialog>
    <CouponTemplateSelectorDialog ref="couponTemplate" @summit="doCardTemplateSelected">
    </CouponTemplateSelectorDialog>
  </div>
</template>

<script lang="ts" src="./SendCouponDialog.ts">
</script>

<style lang="scss" scoped>
.send-coupon-container {
}
</style>