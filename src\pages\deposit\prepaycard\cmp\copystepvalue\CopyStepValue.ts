import {Component, Vue} from 'vue-property-decorator'
import RSGrade from 'model/common/RSGrade'
import {Strategy} from 'model/payment/card/CardBalancePromotionActivity'
import GradeStepValue from "model/common/GradeSameReduction";
import I18nPage from "common/I18nDecorator";
import GradeStepValues from 'model/common/GradeStepValues';


@Component({
  name: 'CopyStepValue',
})
@I18nPage({
  prefix: ['/储值/会员储值/储值支付活动/编辑页面/复制规则', '/储值/会员储值/储值支付活动/编辑页面', '/公用/按钮'],
})
export default class CopyStepValue extends Vue {
  i18n: (str: string, params?: string[]) => string
  originGradeCode: string = ''
  gradeList: RSGrade[] = []
  reduction: GradeStepValues = new GradeStepValues()
  strategy: Strategy = 'BY_AMOUNT'
  dialogShow = false
  gradeTree: any[] = []
  defaultExpandedKeys: string[] = ['FREE', 'PAID', 'SPECIAL']
  defaultCheckedKeys: string[] = []
  $refs: any
  isDiscount: boolean = false

  show(originGradeCode: string, gradeList: RSGrade[], strategy: Strategy, reduction: GradeStepValues, isDiscount: boolean = false) {
    this.originGradeCode = originGradeCode
    this.gradeList = gradeList
    this.strategy = strategy
    this.isDiscount = isDiscount
    this.reduction = reduction
    this.dialogShow = true
    this.gradeListToTree()
  }

  gradeListToTree() {
    this.gradeTree = []
    let free: any = {
      id: 'FREE',
      label: this.i18n('免费等级'),
      children: []
    }
    let paid: any = {
      id: 'PAID',
      label: this.i18n('付费等级'),
      children: []
    }
    let special: any = {
      id: 'SPECIAL',
      label: this.i18n('特殊等级'),
      children: []
    }
    for (let grade of this.gradeList) {
      let temp: any
      if (grade.type === 'FREE') {
        temp = free
      }
      if (grade.type === 'PAID') {
        temp = paid
      }
      if (grade.type === 'SPECIAL') {
        temp = special
      }
      let item: any = {
        id: grade.code,
        label: grade.name
      }
      if (grade.code === this.originGradeCode) {
        item.disabled = true
        this.defaultCheckedKeys = [grade.code]
      }
      temp.children.push(item)
    }
    if (free.children.length > 0) {
      this.gradeTree.push(free)
    }
    if (paid.children.length > 0) {
      this.gradeTree.push(paid)
    }
    if (special.children.length > 0) {
      this.gradeTree.push(special)
    }
  }

  confirm() {
    let checkedGradeCodeList = this.$refs.gradeTree.getCheckedKeys().filter((e: string) => this.defaultExpandedKeys.indexOf(e) === -1)
    this.$emit('confirm', checkedGradeCodeList, this.reduction)
    this.dialogShow = false
  }
}
