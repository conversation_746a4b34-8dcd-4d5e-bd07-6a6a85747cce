import BaseCouponActivity from 'model/v2/coupon/BaseCouponActivity'
import IdName from 'model/common/IdName'
import { AliCouponType } from './AliCouponType'

// 支付宝支付有礼活动
export default class BAliConsumeGiftActivity extends BaseCouponActivity {
  // 门槛
  threshold: Nullable<number> = null
  // 券类型
  couponType: Nullable<AliCouponType> = null
  // 券模板
  couponTemplates: IdName[] = []
  // 促销主题
  promoteTopic: Nullable<string> = null
  // 促销类型
  promoteType: Nullable<string> = null
  // 促销单号
  promoteNumber: Nullable<string> = null
  // 促销说明
  promoteRemark: Nullable<string> = null
  // 总库存
  maxTime: Nullable<number> = null
  // 促销单批次号
  promoteBatchNumber: Nullable<string> = null
  // 支付宝券批次号
  batchNumber: Nullable<string> = null
  // 是否修改库存
  modifyStock: Nullable<number> = null
}