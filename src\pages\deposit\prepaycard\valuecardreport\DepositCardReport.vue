<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:05
 * @LastEditTime: 2023-08-07 14:50:52
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\prepaycard\valuecardreport\DepositCardReport.vue
 * 记得注释
-->
<template>
  <div class="value-card-report">
    <BreadCrume :panelArray="panelArray">
        <template slot="operate">
          <el-button size="large" @click="doBatchExport" v-if="hasOptionPermission('/数据/报表/储值卡报表','报表导出')">
            {{ formatI18n('/会员/会员资料', '批量导出') }}
          </el-button>
        </template>
    </BreadCrume>
    <div class="current-container">
      <el-tabs v-model="activeName" class="tabs">
        <el-tab-pane :label="i18n('售卡流水')" name="售卡流水"/>
        <el-tab-pane :label="i18n('消费流水')" name="消费流水"/>
        <el-tab-pane :label="i18n('退款流水')" name="退款流水"/>
        <el-tab-pane :label="i18n('充值流水')" name="充值流水"/>
        <el-tab-pane :label="i18n('转出流水')" name="转出流水"/>
        <el-tab-pane :label="i18n('退卡流水')" name="退卡流水" />
      </el-tabs>
      <SalesCardsReport v-if="activeName === '售卡流水'"/>
      <ComsumeReport v-if="activeName === '消费流水'"/>
      <RefundReport v-if="activeName === '退款流水'"/>
      <RechargeCardsReport v-if="activeName === '充值流水'"/>
      <TransferReport v-if="activeName === '转出流水'"/>
      <RefundCardReport v-if="activeName === '退卡流水'"></RefundCardReport>
    </div>

    <DownloadCenterDialog
        :dialogvisiable="fileDialogVisible"
        :showTip="showTip"
        @dialogClose="doDownloadDialogClose">
    </DownloadCenterDialog>
    <PointsReportExportConfirm
        :dialogShow="getExportDialogShow"
        @dialogClose="doExportDialogClose"
        @doSubmit="doExportSubmit">
    </PointsReportExportConfirm>
  </div>
</template>

<script lang="ts" src="./DepositCardReport.ts">
</script>

<style lang="scss">
  .value-card-report {
    background-color: white;
    height: 100%;
    width: 100%;
    overflow: auto;

    .current-container {
      height: calc(100% - 150px);
      padding: 20px 0 0;
      .tabs {
        padding: 0 20px;
      }
    }
  }
</style>