<template>
    <div class="login-view">
        <div class="content">
            <div class="form">
                 <!-- <div class="logo" style="position: relative">  
                    <div class="image-container">  
                         <img :src="~assets/image/nav/logo_navgation.svg">
                    </div>  
                    <div class="title-container" v-if="webAppearanceConfig && webAppearanceConfig.title" style="font-size: 40px; font-family:PingFangSC-Regular,PingFang SC;font-weight:400;">  
                         {{ webAppearanceConfig.title }} 
                    </div>  
                    <div class="button-container">  
                         <span style="position: absolute;right: -20px" v-if="languageFlag">
                        <img src="~assets/image/home/<USER>" style="width: 20px;height: 20px;    position: relative;top: 5px;right: 10px;">
                        <el-select @change="doChangeLan" class="cur-sel" placeholder="" style="width: 100px" v-model="language">
                            <el-option :label="item.value" :value="item.key" :key="index" v-for="(item,index) in languageNames">{{item.value}}</el-option>
                        </el-select>
                        </span>
                    </div>  
                </div>    -->

                <div class="logo" style="position: relative">
                    <div v-if="(webAppearanceConfig && webAppearanceConfig.logo) || (webAppearanceConfig && webAppearanceConfig.title)">
                        <div v-if="webAppearanceConfig.logo" style="display: inline-block;"><img :src="webAppearanceConfig.logo"></div> 
                        <div v-if="webAppearanceConfig.title" class="text-container" style="display: inline-block;font-size: 40px; font-family:PingFangSC-Regular,PingFang SC;font-weight:400; text-align: center;"> {{ webAppearanceConfig.title }}</div>
                    </div>
                     <div v-else> 
                         <div style="display: inline-block;"><img src="~assets/image/nav/logo_navgation.svg"></div> 
                    </div>                 
                    <span style="position: absolute;right: -20px" v-if="languageFlag">
                        <img src="~assets/image/home/<USER>" style="width: 20px;height: 20px;    position: relative;top: 5px;right: 10px;">
                    <el-select @change="doChangeLan" class="cur-sel" placeholder="" style="width: 100px" v-model="language">
                        <el-option :label="item.value" :value="item.key" :key="index" v-for="(item,index) in languageNames">{{item.value}}</el-option>
                    </el-select>
                    </span>
                </div>   
                <div class="input">
                    <div class="item">
                        <el-input :placeholder="getUserNameI18n()" @blur="doBlur('name')" @focus="doFocus('name')" @keydown.native="onConfirm($event, 'name')" ref="name" v-model="name"></el-input>
                        <img class="img-icon" v-if="!showNameImage" src="~assets/image/nav/ic_user_normal.svg">
                        <img class="img-icon" v-if="showNameImage" src="~assets/image/nav/ic_user_selected.svg">
                        <img class="img-delete" v-show="getNameFlag" @click="doDeleteName" src="~assets/image/auth/ic_delete.svg">
                    </div>
                    <div class="item check">
                        <el-input :placeholder="getPwdI18n()" @blur="doBlur('password')" @focus="doFocus('password')" @keydown.native="onConfirm($event, 'pwd')" ref="pwd" type="password" v-if="!showEyeImage" v-model="password"></el-input>
                        <el-input :placeholder="getPwdI18n()" @blur="doBlur('password')" @focus="doFocus('password')" @keydown.native="onConfirm($event, 'pwd')" ref="pwd" v-if="showEyeImage" v-model="password"></el-input>
                        <img class="img-icon" v-if="!showPasswordImage" src="~assets/image/nav/ic_password_normal(1).svg">
                        <img class="img-icon" v-if="showPasswordImage" src="~assets/image/nav/ic_password_selected.svg">
                        <img class="img-eye" @click="doCheckPwd" v-if="!showEyeImage" src="~assets/image/nav/ic_hide.svg">
                        <img class="img-eye" @click="doCheckPwd" v-if="showEyeImage" src="~assets/image/nav/ic_show.svg">
                        <img class="img-delete img-pwd" v-show="getPwdFlag" @click="doDeletePassword" src="~assets/image/auth/ic_delete.svg">
                    </div>
                    <div class="item">
                        <el-input :placeholder="getCodeI18n()" @blur="doBlur('code')" @focus="doFocus('code')" @keydown.native="onConfirm($event, 'code')" class="modify-input" ref="code" v-model="code" maxlength="6"></el-input>
                        <img class="img-icon" v-if="!showCodeImage" src="~assets/image/nav/ic_yanzhengma_normal.svg">
                        <img class="img-icon" v-if="showCodeImage" src="~assets/image/nav/ic_yanzhengma_selected.svg">
                        <div class="code"><img :src="imgPath" @click="doChangeImg"></div>
                        <!--<img class="modify-code" src="~assets/image/nav/ic_user.svg" >-->
                    </div>
                    <div class="tip" v-show="showWarning && !showTip">
                        <img style="position: relative;top: 3px;" src="~assets/image/nav/ic_jingshi.svg">&nbsp;{{warnMsg}}
                    </div>
                    <phoenix-button :disabled="!name && !code && !password" @click="onLogin" ref="login" style="margin-top: 60px">{{getLoginI18n()}}</phoenix-button>
                </div>
            </div>
            <p class="foot">{{projectVersion}} Copyright &#169; 1997-{{getYear}} {{getHdI18n()}}</p>
            <p class="foot-icp" v-if="sysConfig.ICP || sysConfig.PSBFTR">
              <span v-if="sysConfig.ICP">ICP备案：<a :href="sysConfig.ICPUrl" target="_blank">{{sysConfig.ICP}}</a>&nbsp;</span>
              <span v-if="sysConfig.PSBFTR"><img style="position: relative;top: 4px;" src="~assets/image/police.png"/>公安备案：<a target="_blank" :href="sysConfig.PSBFTRUrl">{{sysConfig.PSBFTR}}</a></span>
            </p>
        </div>
      <ChangePwd :visible.sync="showPwdDialog" @change="changeCall" :expiredFlag="true" :user="name" :use-strict="useStrict" :reset-token="resetToken"></ChangePwd>
    </div>
</template>

<script lang="ts" src="./Login.ts"></script>

<style lang="scss">
    .logo {  
        display: flex;  
        align-items: center; /* 垂直居中 */  
    }  
        
    .image-container {  
        /* 您可以为图片设置具体的宽度和高度 */  
        height: 68px;; /* 示例高度 */ 
    }  
    
    .text-container {  
        flex: 1; /* 占据剩余空间 */  
        align-items: center; /* 垂直居中 */  
        text-align: center; /* 水平居中 */  
    }  
    
    .button-container {  
        /* 可以为按钮容器设置具体的宽度，或者保持 flex: 0 0 auto 以保持其自动宽度 */  
        margin-left: auto; /* 右侧对齐按钮 */  
    }  

    .login-view {
        width: 100%;
        height: 100%;
        background-image: url("~assets/image/auth/bg_login_big.svg");
        background-repeat: no-repeat;
        background-position: center center;
        background-size: cover;
        display: flex;
        align-items: center;
        justify-content: center;
        .content{
            display: flex;
            width: 1080px;
            height: 680px;
            /*min-width: 716px;*/
            /*min-height: 680px;*/
            background: url("~assets/image/auth/bg_illustration_login.svg") #ffffff 100% 50% no-repeat;
            position: relative;
            box-shadow:0px 8px 44px 0px rgba(0,0,0,0.2);
            border-radius:20px;
            .foot{
                position: absolute;
                bottom: 40px;
                width: 100%;
                text-align: center;
                font-size:14px;
                font-family:PingFangSC-Regular,PingFang SC;
                font-weight:400;
                color:rgba(90,95,102,1);
            }
            .foot-icp{
                position: absolute;
                bottom: 20px;
                width: 100%;
                text-align: center;
                font-size:14px;
                font-family:PingFangSC-Regular,PingFang SC;
                font-weight:400;
                color:rgba(90,95,102,1);
            }
            .form{
                position: absolute;
                width: 400px;
                height: 500px;
                left: 60px;
                top: 100px;
                .logo{
                    img{
                        height: 68px;
                    }
                   
                }
                .input{
                    margin-top: 30px;
                    .item{
                        position: relative;
                        .img-icon{
                            position: absolute;
                            top: 33px;
                            left: 5px;
                            width: 32px;
                            height: 32px;
                        }
                        .img-delete{
                            position: absolute;
                            right: 12px;
                            top: 35px;
                            cursor: pointer;
                            width: 25px;
                        }
                        .img-pwd{
                            right: 48px;
                        }
                        .modify-input{
                            width: 250px;
                        }
                        .code{
                            width: 132px;
                            position: absolute;
                            right: 0;
                            height: 45px;
                            top: 20px;
                            img{
                                position: unset;
                                width: 132px;
                                height: 56px;
                            }
                        }
                        .img-eye{
                            position: absolute;
                            right: 10px;
                            top: 31px;
                            cursor: pointer;
                        }
                    }
                    .tip{
                        margin-top: 15px;
                        color: #FC0049;
                    }
                }
                .el-input{
                    margin-top: 20px;
                }
                .el-input .el-input__inner{
                    height: 56px;
                    background:rgba(241,245,249,1);
                    padding-left: 50px;
                    font-size: 16px;
                    &:focus{
                        background: white;
                    }
                }
                .el-button--primary{
                    width: 100%;
                    height: 56px;
                    margin-top: 50px;
                    background:linear-gradient(90deg,rgba(0,172,255,1) 0%,rgba(1,108,255,1) 100%);
                    box-shadow:0px 4px 20px 0px rgba(0,126,255,0.4);
                    border-radius:8px;
                }
            }
        }
        .cur-sel{
            .el-input__inner{
                background-color: white !important;
                border: none !important;
                height: 32px !important;
                font-size: 14px !important;
            }
            .el-input__suffix{
                right: 15px !important;
                top: 0px !important;
            }
            .el-input--suffix .el-input__inner{
                padding-right: 0px;
                padding-left: 0px !important;
            }
        }
    }
</style>