/*
 * @Autor: 司浩
 * @Description:
 * @Date: 2023-01-09 13:31:39
 * @LastEditors: 司浩
 * @LastEditTime: 2023-01-17 16:57:18
 * @FilePath: \phoenix-web-ui\src\model\couponPurchase\PurchaseCouponTradeLine.ts
 */
export default class PurchaseCouponTradeLine {
  // 券模板
  templateNumber: Nullable<string> = null
  // 券模板
  templateName: Nullable<string> = null
  // 总数
  qty: Nullable<number> = null
  // 转赠
  presentQty: Nullable<number> = null
  // 已使用
  usedQty: Nullable<number> = null
  // 未使用
  unUseQty: Nullable<number> = null
  // 过期
  overTimeQty: Nullable<number> = null
  // 作废
  cancelQty: Nullable<number> = null
  // 抵扣金额
  deductionAmount: Nullable<number> = null
  //券码数组
  couponCodes: Nullable<number[]> = null
}
