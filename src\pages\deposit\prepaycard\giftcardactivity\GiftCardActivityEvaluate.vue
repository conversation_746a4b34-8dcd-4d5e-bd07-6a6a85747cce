<template>
  <div class="gitf-card-activity-evaluate">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button @click="$router.go(-1)">返回</el-button>
      </template>
    </BreadCrume>
    <div class="current-page">
      <div class="panel">
        <div class="content">
          <el-row style="white-space: nowrap">
            <el-col :span="2">
              <ActivityState :state="detail.body.state" style="font-size: 15px;line-height: 29px"/>
            </el-col>
            <el-col :span="10">
              <div class="name" no-i18n>{{detail.body.name}}</div>
              <!--              <div>统计时间: {{detail.body.name}}至{{detail.body.name}} ，共{{detail.body.name}}天</div>-->
              <div class="secondary">
                <i18n k="/储值/预付卡/电子礼品卡活动/效果评估/活动时间:{0}至{1}，共{2}天">
                  <template slot="0">{{begin|yyyyMMdd}}</template>
                  <template slot="1">{{end|yyyyMMdd}}</template>
                  <template slot="2">{{days}}</template>
                </i18n>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
      <div class="panel">
        <div class="header">
          <span>数据概览</span>

          <el-popover
              placement="bottom-start"
              title="指标说明"
              trigger="hover">
            <p>销售额：礼品卡总销售额</p><br/>
            <p>卡面额：售出的礼品卡总面额</p><br/>
            <p>发卡数：总销售礼品卡张数</p><br/>
            <p>购卡客单价：销售额/购卡人数</p><br/>
            <span class="small" slot="reference"><span>指标说明</span><i class="el-icon-question"></i></span>
          </el-popover>
        </div>
        <div class="content">
          <el-row style="white-space: nowrap; border: 1px solid #dddddd">
            <el-col :span="5">
              <div class="amount" no-i18n>{{evaluate.saleAmount|amount}}</div>
              <div class="title">销售额（元）</div>
            </el-col>
            <el-col :span="1">
              <div class="vertical-line"></div>
            </el-col>
            <el-col :span="5">
              <div class="amount" no-i18n>{{evaluate.faceAmount|amount}}</div>
              <div class="title">卡面额（元）</div>
            </el-col>
            <el-col :span="1">
              <div class="vertical-line"></div>
            </el-col>
            <el-col :span="5">
              <div class="amount" no-i18n>{{evaluate.issueTime}}</div>
              <div class="title">发卡数（张）</div>
            </el-col>
            <el-col :span="1">
              <div class="vertical-line"></div>
            </el-col>
            <el-col :span="5">
              <div class="amount" no-i18n>{{evaluate.avtAmount|amount}}</div>
              <div class="title">购卡客单价（元）</div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./GiftCardActivityEvaluate.ts">
</script>

<style lang="scss">
  .gitf-card-activity-evaluate {
    width: 100%;
    height: 100%;
    background-color: white;

    .current-page {
      height: calc(100% - 80px) !important;

      .vertical-line {
        display: inline-block;
        margin-top: 20px;
        width: 1px;
        height: 60px;
        background-color: #dfe2e5;
      }

      .panel {
        .header {
          font-weight: 500;
          padding: 20px 20px 0 20px;
          font-size: 18px;

          .small {
            font-size: 14px;
          }
        }

        .content {
          padding: 20px;

          .name {
            line-height: 30px;
            font-size: 20px;
          }

          .state {
            width: 64px;
            height: 30px;
            border-radius: 3px;
            float: left;
            text-align: center;
            line-height: 30px;
            color: white;
          }

          .amount {
            text-align: center;
            font-weight: 600;
            font-size: 25px;
            line-height: 60px;
          }

          .title {
            text-align: center;
            color: rgba(51, 51, 51, 0.64);
            line-height: 40px;
          }

          .secondary {
            color: rgba(51, 51, 51, 0.64);
          }
        }
      }
    }
  }
</style>
