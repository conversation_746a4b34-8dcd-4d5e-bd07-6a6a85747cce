import {Component, Prop, Vue, Watch} from 'vue-property-decorator'
import SpecialTpl from "pages/member/entities/SpecialTpl";

@Component({
  name: 'MbrCardDtl',
  components: {}
})
export default class MbrCardDtl extends Vue {
  $refs: any
  ruleForm: any = {
    remark: '',
    tip: ''
  }
  rules: any = {}
  @Prop()
  value: SpecialTpl
  @Watch('value')
  onValueChange(value: SpecialTpl) {
    if (value) {
      this.ruleForm.tip = value.remark
      this.ruleForm.remark = value.notice
    }
  }
  created() {
    this.rules = {
      remark: [
        { required: true, message:  this.formatI18n('/公用/券模板', '请输入必填项'), trigger: 'blur' }
      ]
    }
  }
  doChange() {
    let special: SpecialTpl = new SpecialTpl()
    special.notice = this.ruleForm.remark
    special.remark = this.ruleForm.tip
    this.$emit('input', special)
  }
}