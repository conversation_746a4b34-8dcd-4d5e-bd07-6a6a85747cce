<template>
  <el-dialog :before-close="doBeforeClose" :close-on-click-modal="false" :title="formatI18n('/会员/会员资料/详情界面', '修改会员归属门店')" :visible="dialogShow" class="singl-modif-store-dialog">
    <div class="wrap">
      <el-form :model="ruleForm" :rules="rules" class="demo-ruleForm" label-width="100px" ref="ruleForm">
        <el-form-item :label="formatI18n('/会员/会员资料/详情界面', '归属门店')" prop=storeId>
          <el-select v-model="ruleForm.storeId" filterable>
            <el-option :label="`[${v.org.id}]${v.org.name}`" :value="v.org.id" v-for="v in queryData" :key="v.org.id">{{`[${v.org.id}]${v.org.name}`}}</el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="formatI18n('/会员/会员资料', '变更说明')" prop="remark">
          <el-input :placeholder="formatI18n('/会员/会员资料', '请输入不超过50个字')"
          maxlength="50"
          type="textarea" v-model="ruleForm.remark"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div class="dialog-footer" slot="footer">
      <el-button @click="doModalClose('cancel')">{{formatI18n('/公用/按钮', '取消')}}</el-button>
      <el-button @click="doModalClose('confirm')" size="small" type="primary">{{formatI18n('/公用/按钮', '确认修改')}}</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./SingleModifyStoreDialog.ts">
</script>

<style lang="scss" scoped>
.singl-modif-store-dialog {
  display: flex;
  justify-content: center;
  align-items: center;
  .wrap {
    margin-top: 30px;
  }
  & ::v-deep .el-dialog {
    width: 500px;
    margin: 0 !important;
  }
}
</style>