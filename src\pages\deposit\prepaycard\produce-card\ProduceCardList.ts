import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import FormItem from 'cmp/formitem/FormItem';
import MyQueryCmp from 'cmp/querycondition/MyQueryCmp';
import I18nPage from 'common/I18nDecorator';
import MakeCardBillApi from 'http/prepay/card/MakeCardBillApi';
import MakeCardBill from 'model/prepay/card/MakeCardBill';
import MakeCardBillFilter from 'model/prepay/card/MakeCardBillFilter';
import MakeCardBillLog from 'model/prepay/card/MakeCardBillLog';
import DownloadCenterDialog from 'pages/main/dowmload_center_dialog/DownloadCenterDialog';
import DateUtil from 'util/DateUtil';
import { Component, Vue } from 'vue-property-decorator';
@Component({
  name: 'ProduceCardList',
  components: {
    BreadCrume,
    MyQueryCmp,
    FormItem,
    DownloadCenterDialog
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/卡/卡管理/制卡单/制卡单列表',
    '/公用/查询条件/提示',
    '/营销/券礼包活动/券礼包活动',
    '/公用/过滤器',
    '/公用/下拉框/提示',
    '/储值/预付卡/卡模板/编辑页面',
    '/公用/券核销',
    '/储值/预付卡/卡模板/列表页面',
    '/公用/按钮',
    '/公用/活动/提示信息'
  ],
  auto: true
})
export default class ProduceCardList extends Vue {
  $refs: any
  panelArray: any = []
  tableData: MakeCardBill[] = []
  selected: MakeCardBill[] = []
  selectCreateData: any = ''
  selectLastModifyData: any = ''
  query: MakeCardBillFilter = new MakeCardBillFilter()
  logsData: MakeCardBillLog[] = []
  logsDialogVisible: boolean = false  //是否展示操作日志dialog
  fileDialogVisible: boolean = false
  showTip: boolean = false
  page: any = {
    pageSize: 10,
    page: 1,
    total: 0
  }
  pickerOptions: any = {
    //依据开始时间给出可选的三个月时间范畴
    onPick: ({ maxDate, minDate }: any) => {
      this.selectCreateData = minDate.getTime();
      if (maxDate) {
        this.selectCreateData = "";
      }
    },
    disabledDate: (time: any) => {
      if (this.selectCreateData) {
        const curDate = this.selectCreateData;
        const three = 90 * 24 * 3600 * 1000; // 3个月
        const threeMonths = curDate + three; // 开始时间+3个月
        return time.getTime() < curDate || time.getTime() > threeMonths;
      }
    },
  };
  pickerLastModifyOptions: any = {
    //依据开始时间给出可选的三个月时间范畴
    onPick: ({ maxDate, minDate }: any) => {
      this.selectLastModifyData = minDate.getTime();
      if (maxDate) {
        this.selectLastModifyData = "";
      }
    },
    disabledDate: (time: any) => {
      if (this.selectLastModifyData) {
        const curDate = this.selectLastModifyData;
        const three = 90 * 24 * 3600 * 1000; // 3个月
        const threeMonths = curDate + three; // 开始时间+3个月
        return time.getTime() < curDate || time.getTime() > threeMonths;
      }
    },
  };

  created() {
    this.panelArray = [
      {
        name: this.i18n("制卡单"),
        url: "",
      },
    ]
    this.query.stateEquals = 'AUDITED'
    this.getList()
  }

  getList() {
    const params = new MakeCardBillFilter()
    params.page = this.page.page - 1
    params.pageSize = this.page.pageSize
    params.billNumberLikes = this.query.billNumberLikes
    params.billNumberEquals = this.query.billNumberEquals
    params.stateEquals = this.query.stateEquals
    params.cardTemplateNameLikes = this.query.cardTemplateNameLikes
    params.cardTemplateNumberLikes = this.query.cardTemplateNumberLikes
    params.cardTemplateNumberEquals = this.query.cardTemplateNumberEquals
    if (this.query.createdBetweenClosedClosed?.length) {
      params.createdBetweenClosedClosed = [DateUtil.format(this.query.createdBetweenClosedClosed[0], "yyyy-MM-dd"), DateUtil.format(this.query.createdBetweenClosedClosed[1], "yyyy-MM-dd")]
    }
    if (this.query.lastModifiedBetweenClosedClosed?.length) {
      params.lastModifiedBetweenClosedClosed = [DateUtil.format(this.query.lastModifiedBetweenClosedClosed[0], "yyyy-MM-dd"), DateUtil.format(this.query.lastModifiedBetweenClosedClosed[1], "yyyy-MM-dd")]
    }
    MakeCardBillApi.query(params).then((res) => {
      if (res.code === 2000) {
        this.tableData = res.data || []
        this.page.total = res.total
      } else {
        this.$message.error(res.msg || this.i18n('查询制卡单列表失败'))
      }
    }).catch((error) => {
      this.$message.error(error.message || this.i18n('查询制卡单列表失败'))
    })
  }

  computeState(state: string) {
    let str = '-'
    let color = '#A1B0C8'
    if (state === 'INITIAL') {
      str = this.i18n('未审核')
      color = '#FFAA00'
    } else if (state === 'AUDITED') {
      str = this.i18n('已审核')
      color = '#1597FF'
    } else if (state === 'MAKING') {
      str = this.i18n('制卡中')
      color = '#FFAA00'
    } else if (state === 'FINISH') {
      str = this.i18n('已制卡')
      color = '#0CC66D'
    } else if (state === 'CANCELED') {
      str = this.i18n('已作废')
      color = '#A1B0C8'
    }
    return {
      state: str,
      color: color
    }
  }

  handleSelectionChange(e: MakeCardBill[]) {
    this.selected = e
  }

  onHandleCurrentChange(val: number) {
    this.page.page = val
    this.getList()
  }

  onHandleSizeChange(val: number) {
    this.page.pageSize = val
    this.getList()
  }

  doAudit(billNumber: string) {
    this.$confirm(
      this.i18n("确定审核选中制卡单吗？"),
      this.i18n("审核"),
      {
        confirmButtonText: this.formatI18n("/公用/按钮", "确定"),
        cancelButtonText: this.formatI18n("/公用/按钮", "取消"),
      }
    ).then(() => {
      MakeCardBillApi.audit(billNumber).then((res) => {
        if (res.code === 2000) {
          this.$message.success(this.i18n('操作成功'))
          this.getList()
        } else {
          this.$message.error(res.msg || this.i18n('操作失败'))
        }
      }).catch((error) => {
        this.$message.error(error.message || this.i18n('操作失败'))
      })
    });
  }

  doEdit(billNumber: string) {
    this.$router.push({
      name: 'produce-card-edit',
      query: {
        editType: 'edit',
        billNumber: billNumber
      }
    })
  }

  doCancel(billNumber: string) {
    this.$confirm(
      this.i18n("作废将使本次单据生成的卡密失效"),
      this.i18n("作废"),
      {
        confirmButtonText: this.formatI18n("/公用/按钮", "确定"),
        cancelButtonText: this.formatI18n("/公用/按钮", "取消"),
      }
    ).then(() => {
      MakeCardBillApi.cancel(billNumber).then((res) => {
        if (res.code === 2000) {
          this.$message.success(this.i18n('操作成功'))
          this.getList()
        } else {
          this.$message.error(res.msg || this.i18n('操作失败'))
        }
      }).catch((error) => {
        this.$message.error(error.message || this.i18n('操作失败'))
      })
    });
  }

  doRemove(billNumber: string) {
    this.$confirm(
      this.i18n("确定删除选中制卡单吗？"),
      this.i18n("删除"),
      {
        confirmButtonText: this.formatI18n("/公用/按钮", "确定"),
        cancelButtonText: this.formatI18n("/公用/按钮", "取消"),
      }
    ).then(() => {
      MakeCardBillApi.remove(billNumber).then((res) => {
        if (res.code === 2000) {
          this.$message.success(this.i18n('操作成功'))
          this.getList()
        } else {
          this.$message.error(res.msg || this.i18n('操作失败'))
        }
      }).catch((error) => {
        this.$message.error(error.message || this.i18n('操作失败'))
      })
    });
  }

  //导出卡号密码
  exportCodePwd(row: MakeCardBill) {
    function doExport(_this: any) {
      MakeCardBillApi.exportCard(row.billNumber!).then((res) => {
        if (res?.code === 2000) {
          _this.exportAfter();
          _this.getList()
        } else {
          _this.$message.error(res.msg || _this.i18n('导出失败'))
        }
      }).catch(error => _this.$message.error(error.message || _this.i18n('导出失败')))
    }
    if(row.exportFinish) {
      this.$confirm(
        this.i18n("本操作不是首次导出，原有的密码将失效，以本次导出密码为准，确定导出卡密吗？"),
        this.i18n("导出卡密"),
        {
          confirmButtonText: this.formatI18n("/公用/按钮", "确定"),
          cancelButtonText: this.formatI18n("/公用/按钮", "取消"),
        }
      ).then(() => {
        doExport(this)
      });
    } else {
      doExport(this)
    }
  }

  //查看加密密码
  viewPwd(billNumber: string) {
    MakeCardBillApi.getExcelPassword(billNumber).then((res) => {
      if(res.code === 2000) {
        this.$alert(this.i18n('当前制卡单密码为：') + res.data, this.i18n('查看加密密码'), { confirmButtonText: this.formatI18n("/公用/按钮", "确定") })
      } else {
        this.$message.error(res.msg || this.i18n('获取卡密失败'))
      }
    }).catch(error => this.$message.error(error.message || this.i18n('获取卡密失败')))
  }

  exportAfter() {
    this.showTip = true;
    this.fileDialogVisible = true;
  }

  doDownloadDialogClose() {
    this.showTip = false;
    this.fileDialogVisible = false;
  }

  batchAudit() {
    if (!this.selected?.length) return this.$message.warning(this.i18n('请先选择制卡单'))
    this.$confirm(
      this.i18n("确定批量审核选中制卡单吗？"),
      this.i18n("批量审核"),
      {
        confirmButtonText: this.formatI18n("/公用/按钮", "确定"),
        cancelButtonText: this.formatI18n("/公用/按钮", "取消"),
      }
    ).then(() => {
      const stringArr = this.selected.map(item => item.billNumber!)
      MakeCardBillApi.batchAudit(stringArr).then((res) => {
        if (res.code === 2000 && res.data) {
          this.$message.success(res.data)
          this.$refs.table?.clearSelection()
          this.getList()
        } else {
          this.$message.error(res.msg || this.i18n('批量审核失败'))
        }
      }).catch((error) => {
        this.$message.error(error.message || this.i18n('批量审核失败'))
      })
    });
  }

  isShowLogs(row: MakeCardBill) {
    return ['AUDITED', 'FINISH', 'CANCELED'].indexOf(row.state!) > -1 && row.logs.length > 0
  }

  goDtl(billNumber: string) {
    this.$router.push({
      name: 'produce-card-dtl',
      query: { billNumber: billNumber }
    })
  }

  doFinishCard(row: MakeCardBill) {
    let msg = this.i18n('制卡完成，则本单据所有卡将为"已制卡"状态，卡可以进行发售')
    if ((row.cardMedium === 'mag' && row.writeCardType === 'SYS') || row.cardMedium === 'rfic') {
      msg = this.i18n('制卡完成，则本单据所有卡将为"空白卡"状态，可以操作写卡')
    }
    this.$confirm(
      msg,
      this.i18n("制卡完成"),
      {
        confirmButtonText: this.formatI18n("/公用/按钮", "确定"),
        cancelButtonText: this.formatI18n("/公用/按钮", "取消"),
      }
    ).then(() => {
      MakeCardBillApi.finish(row.billNumber!).then((res) => {
        if (res.code === 2000) {
          this.$message.success(this.i18n('操作成功'))
          this.getList()
        } else {
          this.$message.error(res.msg || this.i18n('操作失败'))
        }
      }).catch(error => this.$message.error(error.message || this.i18n('操作失败')))
    });
  }

  doViewLogs(logs: MakeCardBillLog[]) {
    this.logsData = logs || []
    this.logsDialogVisible = true
  }

  //前往卡模板详情页
  goCardDtl(billNumber: string) {
    this.$router.push({ name: 'prepay-card-tpl-dtl', query: { number: billNumber }})
  }

  onReset() {
    this.page.page = 1
    this.page.pageSize = 10
    this.query = new MakeCardBillFilter()
    this.getList()
  }

  onSearch() {
    this.page.page = 1
    this.getList()
  }

  doCreate() {
    this.$router.push({
      name: 'produce-card-edit',
      query: {
        editType: 'create'
      }
    })
  }
};