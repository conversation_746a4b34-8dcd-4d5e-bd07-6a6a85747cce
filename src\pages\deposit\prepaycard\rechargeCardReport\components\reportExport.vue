<template>
  <el-dialog 
    :visible="dialogShow"
    :before-close="doBeforeClose" 
    :close-on-click-modal="false"
    :title="'充值卡报表导出'"
    append-to-body 
    class="coupon-report-export-confirm">
    <div class="content-wrap">
      <el-alert
        :closable="false"
        style="margin-bottom: 15px"
        type="warning"
        title=""
        show-icon>
        <strong style="color: red">大数据量导出可能会影响系统稳定，请务必在业务非高峰期执行！</strong>
      </el-alert>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" class="dialog-form-class">
        <el-form-item label="充值卡报表" label-width="120px" prop="type">
          <el-row>
            <el-select @change="doTypeChange" v-model="ruleForm.type">
              <el-option :label="i18n('售卡流水')" value="SALES_HST"></el-option>
              <el-option :label="i18n('转出流水')" value="CONSUME_HST"></el-option>
              <el-option :label="i18n('退卡流水')" value="REFUND_CARD_HST"></el-option>
            </el-select>
          </el-row>
        </el-form-item>
        <el-form-item class="is-required" label="导出方式" label-width="120px">
          <el-radio-group v-model="dateType" @change="clearValidate">
            <el-radio label="month"><span>按月份</span></el-radio>
            <el-radio label="day">
              <span>按日期</span>
              <div v-show="dateType=='day'" style="color:#7B7B7B;position:absolute;top:22px"><span>-</span><span>日期跨度不能超过32天</span></div>
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item class="is-required" v-show="dateType=='month'" label="导出月份" label-width="120px" prop="month">
          <el-date-picker
              size="small"
              :editable="false"
              type="month"
              v-model="ruleForm.month"
              placeholder="请选择月份">
          </el-date-picker>
        </el-form-item>
        <el-form-item class="is-required" v-show="dateType=='day'" label="导出日期" label-width="120px" prop="dateRange">
          <el-date-picker
            size="small"
            :editable="false"
            type="daterange"
            range-separator="-"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            v-model="ruleForm.dateRange"
            :picker-options="dateRangeOption"
            placeholder="请选择月份">
          </el-date-picker>
        </el-form-item>
      </el-form>
    </div>
    <div class="dialog-footer" slot="footer">
      <el-button @click="doModalClose" size="small">取消</el-button>
      <el-button @click="doModalConfirm" size="small" type="primary">确认导出</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./reportExport.ts">
</script>

<style lang="scss">
.coupon-report-export-confirm {
  display: flex;
  align-items: center;
  justify-content: center;
  .el-dialog {
    width: 670px;
  }

  .dialog-form-class {
    .el-date-editor.el-input, .el-date-editor.el-input__inner {
      width: 90%;
    }

    .el-alert--info.is-light {
      border: none;
      background-color: white;
      color: var(--font-color-primary);
    }

    .el-select {
      width: 90%;
    }
  }
}
</style>