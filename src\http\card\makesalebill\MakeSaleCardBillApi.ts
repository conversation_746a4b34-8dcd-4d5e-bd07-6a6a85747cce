import ApiClient from 'http/ApiClient'
import MakeSaleCardBill from 'model/MakeSaleCardBill'
import MakeSaleCardBillCreateRequest from 'model/MakeSaleCardBillCreateRequest'
import MakeSaleCardBillFilter from 'model/MakeSaleCardBillFilter'
import Response from 'model/default/Response'

export default class MakeSaleCardBillApi {
  /**
   * 审核制卡单
   * 审核制卡单。
   * 
   */
  static audit(billNumber: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/make-sale-bill/audit/${billNumber}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取卡号长度的最大卡号
   * 获取卡号长度的最大卡号。
   * 
   */
  static autoMaxCardCode(length: number): Promise<Response<string>> {
    return ApiClient.server().get(`/v1/make-sale-bill/autoMaxCardCode/${length}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导出
   * 导出
   * 
   */
  static export(billNumber: string): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/make-sale-bill/export/${billNumber}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取制售单详情
   * 获取制售单详情。
   * 
   */
  static get(billNumber: string): Promise<Response<MakeSaleCardBill>> {
    return ApiClient.server().get(`/v1/make-sale-bill/get/${billNumber}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询制售单
   * 分页查询制售单。
   * 
   */
  static query(body: MakeSaleCardBillFilter): Promise<Response<MakeSaleCardBill[]>> {
    return ApiClient.server().post(`/v1/make-sale-bill/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取发售单已售卡
   * 获取发售单已售卡
   * 
   */
  static queryCards(billNumber: string): Promise<Response<string[]>> {
    return ApiClient.server().get(`/v1/make-sale-bill/queryCards/${billNumber}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除制卡单
   * 删除制卡单。
   * 
   */
  static remove(billNumber: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/make-sale-bill/remove/${billNumber}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存制售单
   * 保存制售单。
   * 
   */
  static save(body: MakeSaleCardBillCreateRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/make-sale-bill/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建并审核制售单
   * 新建并审核制售单。
   * 
   */
  static saveAndAudit(body: MakeSaleCardBillCreateRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/make-sale-bill/saveAndAudit`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
