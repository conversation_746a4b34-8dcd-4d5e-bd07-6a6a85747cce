<!--
 * @Author: 黎钰龙
 * @Date: 2025-01-23 11:09:32
 * @LastEditTime: 2025-02-28 11:15:52
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\analysis\CustomerProfileEdit.vue
 * 记得注释
-->
<template>
  <div class="customer-profile-edit">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button v-if="!isOnlyView && hasOptionPermission('/数据/数据洞察/客群画像', '数据维护')" type="primary" @click="doSave">
          {{i18n('/公用/按钮/保存')}}
        </el-button>
        <el-button v-if="hasOptionPermission('/数据/数据洞察/客群画像', '数据导出')" @click="doExport">
          {{i18n('/数据/快手券账单/导出')}}
        </el-button>
      </template>
    </BreadCrume>
    <div class="setting-container" ref="contentToExport">
      <div class="section-title" style="display:flex;justify-content:space-between">
        {{i18n('画像人群')}}
        <template v-if="!isOnlyView">
          <el-button @click="doRemoveCompareCustomer" v-if="compareCustomerInfo.uuid">{{i18n('移除对比人群')}}</el-button>
          <el-button @click="doAddCompareCustomer" v-else>{{i18n('添加对比人群')}}</el-button>
        </template>
      </div>
      <div class="group-block">
        <div class="group-item" style="flex: 1">
          <el-tag size="medium">{{i18n('目标客群')}}</el-tag>
          <i18n k="/数据/客群画像/{0}{1}人" class="group-desc">
            <span slot="0">{{targetCustomerInfo.name}}</span>
            <span slot="1" @click="doGroupDetail(targetCustomerInfo.uuid)" class="number">
              {{targetCustomerInfo.coveredMemberCount}}
            </span>
          </i18n>
          <span class="group-desc">{{i18n('/数据/客群画像/覆盖率{0}%', [targetCustomerInfo.coveredMemberRate])}}</span>
        </div>
        <div class="group-item" v-if="compareCustomerInfo.uuid" style="flex: 1; margin-left: 12px">
          <el-tag size="medium" type="warning">{{i18n('对比客群')}}</el-tag>
          <i18n k="/数据/客群画像/{0}{1}人" class="group-desc">
            <span slot="0">{{compareCustomerInfo.name}}</span>
            <span slot="1" @click="doGroupDetail(compareCustomerInfo.uuid)" class="number">
              {{compareCustomerInfo.coveredMemberCount}}
            </span>
          </i18n>
          <span class="group-desc">{{i18n('/数据/客群画像/覆盖率{0}%', [compareCustomerInfo.coveredMemberRate])}}</span>
        </div>
      </div>

      <div class="section-title" style="display:flex;justify-content:space-between;margin-top: 36px">
        {{i18n('画像信息')}}
        <el-button v-if="!isOnlyView && chartList.length < 10" @click="doAddPortraitInfo">{{i18n('添加画像信息')}}</el-button>
      </div>
      <div class="chart-div">
        <div class="chart-item" :style="{width: compareCustomerInfo.uuid ? '100%' : '49%'}" v-for="(item) in chartList" :key="item.uuid">
          <!-- 客群卡片 -->
          <PortraitDrawChart :chartData="item" :customerName="targetCustomerInfo.name" :compareCustomerName="compareCustomerInfo.name"
            ref="portraitDrawChart" @change="doChartChange($event, item.uuid)" :compareData="compareItemData(item.uuid)" :isOnlyView="isOnlyView"
            @remove="doChartRemove" @edit="doChartEdit">
          </PortraitDrawChart>
        </div>
      </div>
    </div>
    <el-dialog :title="i18n('添加对比人群')" width="640px" :close-on-click-modal="false" :visible.sync="groupVisible">
      <FormItem :label="i18n('对比客群')">
        <SelectCustomerGroup v-model="temCompareCustomerId" :hideAll="true" :width="'440px'" :placeholder="i18n('请选择/输入客群')">
        </SelectCustomerGroup>
      </FormItem>
      <div class="footer">
        <el-button size="large" @click="closeGroupDialog">{{i18n('/公用/按钮/取消')}}</el-button>
        <el-button type="primary" size="large" @click="confirmGroupCreate">{{i18n('/公用/按钮/确定')}}</el-button>
      </div>
    </el-dialog>
    <AddPortraitInfoDialog @doSubmit="portraitSubmit" :leftCardNum="leftCardNum" ref="addPortraitInfoDialog"></AddPortraitInfoDialog>
  </div>
</template>

<script lang="ts" src="./CustomerProfileEdit.ts">
</script>

<style  lang="scss" scoped>
.customer-profile-edit {
  width: 100%;
  .group-block {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .group-item {
      display: flex;
      align-items: center;
      height: 44px;
      border-radius: 4px;
      border: 1px solid #d7dfeb;
      padding: 10px;
      box-sizing: border-box;
      .group-desc {
        margin-left: 12px;
        .number {
          color: #007eff;
          margin-left: 8px;
          cursor: pointer;
        }
      }
    }
  }
  .chart-div {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .chart-item {
      flex-shrink: 0;
      width: 49%;
      margin-bottom: 12px;
    }
  }
  .footer {
    display: flex;
    justify-content: end;
    margin-top: 50px;
  }
}
</style>