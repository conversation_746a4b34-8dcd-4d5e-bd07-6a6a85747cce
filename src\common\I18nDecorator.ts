import I18nTool from "common/I18nTool";
import BrowserMgr from "mgr/BrowserMgr";
import EnvUtil from "util/EnvUtil";

class Config {
  /**
   * 国际化前缀，数组，可以传多个前缀
   * 常用前缀：
   * /公用/活动/活动信息，包含活动时间、活动信息、活动名称等
   * /公用/活动/提示信息，删除审核启用禁用的提示信息。
   * /公用/表单校验，包含请填写必填项、请选择必选项、长度在{0}个字符以内
   * /公用/按钮，包含操作、保存、修改、复制等
   */
  prefix: string[]
  /** 自动对页面内容进行国际化 */
  auto?: boolean = true
  /** css选择器，数组，可传多个，默认为遍历当前组件的所有子元素，当传入选择器后，会先根据选择器选择相应的元素进行国际化，选择范围是当前组件 */
  selectors?: string[] | null = null
  /** 是否递归遍历子元素 */
  handleChild?: boolean = true
  /** 调试模式，设置为true时会将匹配并替换后的国际化字符串追加√或×符号，表示键值在vuex中存储的国际化资源是否存在 */
  debug?: boolean = false
}

export default function I18nPage(config: Config) {
  const sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
  let auto = sysConfig?.enableI18nAutoTranslation //config中的auto不再生效
  config.debug = EnvUtil.debug || config.debug
  // config.auto = config.auto === undefined ? true : config.auto
  return function(target: any) {
    if (auto) {
      let key = 'mounted'
      let descriptor = Object.getOwnPropertyDescriptor(target.prototype, key)
      if (descriptor) {
        let newFun = function(...arg: any) {
          // @ts-ignore
          I18nTool.render(this.$el, config.prefix, config.selectors, config.handleChild, config.debug)
          // @ts-ignore
          I18nTool.addMutationObserver(this.$el, config.prefix, config.selectors, config.handleChild, config.debug)
          // @ts-ignore
          descriptor.value.apply(this, arg)
        }
        Object.defineProperty(target.prototype, key, {
          ...descriptor,
          value: newFun
        })
      } else {
        target.prototype.mounted = function() {
          I18nTool.render(this.$el, config.prefix, config.selectors, config.handleChild, config.debug)
          I18nTool.addMutationObserver(this.$el, config.prefix, config.selectors, config.handleChild, config.debug)
        }
      }
    }
    /**
     * 将str按顺序与params前缀拼接后匹配国际化资源，除了params提供的前缀外，该方法还会直接将str作为完整的key值进行匹配
     */
    target.prototype.i18n = (str: string, params?: string[]) => {
      return I18nTool.match(str, config.prefix, params, config.debug)
    }
  }
}
