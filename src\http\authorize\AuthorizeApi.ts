import ApiClient from "http/ApiClient";
import FNode from "model/authorize/FNode";
import Permission from "model/authorize/Permission";
import Response from "model/common/Response";
import Role from "model/authorize/Role";
import UserRole from "model/authorize/UserRole";
import User from "model/user/User";
import RolePermission from "model/authorize/RolePermission";
import RoleUser from "model/authorize/RoleUser";

export default class AuthorizeApi {
	/**
	 * 复制角色到另一角色
	 *
	 */
	static copyUserRole(fromUserId: string | null | undefined, toUserId: string): Promise<Response<void>> {
		return ApiClient.server()
			.post(
				`/v1/authorization/copyUserRole`,
				{},
				{
					params: {
						fromUserId: fromUserId,
						toUserId: toUserId,
					},
				}
			)
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 用户追加角色关系
	 *
	 */
	static appendUserRoles(body: RoleUser): Promise<Response<void>> {
		return ApiClient.server()
			.post(`/v1/authorization/appendUserRoles`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 更新角色权限
	 *
	 */
	static saveRolePermissions(body: RolePermission): Promise<Response<void>> {
		return ApiClient.server()
			.post(`/v1/authorization/saveRolePermissions`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 查询角色对应用户信息
	 *
	 */
	static listRoleUser(roleId: string): Promise<Response<User[]>> {
		return ApiClient.server()
			.get(`/v1/authorization/listRoleUser`, {
				params: {
					roleId: roleId,
				},
			})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 复制角色权限到另一角色
	 *
	 */
	static copyRolePermissions(fromRoleId: string | null | undefined, toRoleId: string): Promise<Response<void>> {
		return ApiClient.server()
			.post(
				`/v1/authorization/copyRolePermissions`,
				{},
				{
					params: {
						fromRoleId: fromRoleId,
						toRoleId: toRoleId,
					},
				}
			)
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 删除角色
	 *
	 */
	static deleteRole(roleId: string): Promise<Response<void>> {
		return ApiClient.server()
			.post(
				`/v1/authorization/deleteRole`,
				{},
				{
					params: {
						roleId: roleId,
					},
				}
			)
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 删除用户角色关系
	 *
	 */
	static deleteUserRole(roleId: string, userId: string): Promise<Response<void>> {
		return ApiClient.server()
			.post(
				`/v1/authorization/deleteUserRole`,
				{},
				{
					params: {
						roleId: roleId,
						userId: userId,
					},
				}
			)
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 查询角色对应权限信息
	 *
	 */
	static getRolePermission(roleId: string): Promise<Response<Permission[]>> {
		return ApiClient.server()
			.get(`/v1/authorization/getRolePermission`, {
				params: {
					roleId: roleId,
				},
			})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 查询授权树
	 *
	 */
	static getTree(): Promise<Response<FNode[]>> {
		return ApiClient.server()
			.get(`/v1/authorization/getTree`, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 查询用户权限
	 *
	 */
	static getUserPermission(userId?: string): Promise<Response<Permission[]>> {
		return ApiClient.server()
			.get(`/v1/authorization/getUserPermission`, {
				params: {
					userId: userId,
				},
			})
			.then((res) => {
				return res.data;
			});
	}

	static getUserPermission2(userId?: string): Promise<Response<Permission[]>> {
		return ApiClient.server()
			.get(`/v1/authorization/getUserPermission2`, {
				params: {
					userId: userId,
				},
			})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 查询用户角色信息
	 *
	 */
	static getUserRole(userId: string): Promise<Response<Role[]>> {
		return ApiClient.server()
			.get(`/v1/authorization/getUserRole`, {
				params: {
					userId: userId,
				},
			})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 查询角色信息
	 *
	 */
	static listRole(keyWord?: string): Promise<Response<Role[]>> {
		return ApiClient.server()
			.get(`/v1/authorization/listRole`, {
				params: {
					keyWord: keyWord,
				},
			})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 修改角色信息
	 *
	 */
	static modifyRole(body: Role): Promise<Response<void>> {
		return ApiClient.server()
			.post(`/v1/authorization/modifyRole`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 新建角色信息
	 *
	 */
	static saveRole(body: Role): Promise<Response<void>> {
		return ApiClient.server()
			.post(`/v1/authorization/saveRole`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 新建用户角色关系
	 *
	 */
	static saveUserRole(body: UserRole): Promise<Response<void>> {
		return ApiClient.server()
			.post(`/v1/authorization/saveUserRole`, body, {})
			.then((res) => {
				return res.data;
			});
	}
}
