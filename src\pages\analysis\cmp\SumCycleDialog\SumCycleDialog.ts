/*
 * @Author: 黎钰龙
 * @Date: 2025-02-18 16:29:31
 * @LastEditTime: 2025-02-24 16:28:56
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\analysis\cmp\SumCycleDialog\SumCycleDialog.ts
 * 记得注释
 */
import FormItem from 'cmp/formitem/FormItem';
import I18nPage from 'common/I18nDecorator';
import { DateGroupType } from 'model/analysis/DateGroupType';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  name: 'SumCycleDialog',
  components: {
    FormItem
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/数据/客群画像'
  ],
  auto: true
})
export default class SumCycleDialog extends Vue {
  @Prop() cycleList: DateGroupType[] //汇总周期
  @Prop() titleList: string[];
  visible: boolean = false
  dataList: DateGroupType[] = []

  open(count: number) {
    this.visible = true
    this.dataList.length = count
    this.dataList.fill(DateGroupType.year)
    if (this.cycleList?.length) {
      this.dataList = JSON.parse(JSON.stringify(this.cycleList))
    }
  }

  doSubmit() {
    this.$emit('submit', this.dataList)
    this.visible = false
  }

  getCycleName(val: DateGroupType) {
    switch (val) {
      case DateGroupType.year:
        return this.i18n('按年汇总')
      case DateGroupType.month:
        return this.i18n('按月汇总')
      case DateGroupType.date:
        return this.i18n('按日汇总')
      default:
        break;
    }
  }
};