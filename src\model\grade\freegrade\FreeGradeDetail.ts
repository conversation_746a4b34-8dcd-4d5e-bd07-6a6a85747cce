import GiftInfo from 'model/common/GiftInfo'
import Grade from 'model/grade/Grade'

export default class FreeGradeDetail {
  // 等级基础资料
  grade: Nullable<Grade> = null
  // 评定日期,每月第几天
  ratingDate: Nullable<number> = null
  // 等级有效期
  validMonth: Nullable<number> = null
  // 达标最小成长值
  minGrowthValue: Nullable<number> = null
  // 评定周期:每天:EVERY_DAY;每月: EVERY_MONTH
  ratingCycle: Nullable<string> = null
  // 降级规则:降为下一等级:DOWN_ONE_GRADE;重新评定: RE_RATING
  downRule: Nullable<string> = null
  // 是否参与消费得积分
  obtainPoints: Nullable<boolean> = null
  // 积分加倍倍率
  pointsRate: Nullable<number> = null
  // 是否参与会员促销
  promotion: Nullable<boolean> = null
  // 会员促销折扣率
  discountRate: Nullable<number> = null
  // 券详情信息
  giftBagDetail: Nullable<GiftInfo> = null
}