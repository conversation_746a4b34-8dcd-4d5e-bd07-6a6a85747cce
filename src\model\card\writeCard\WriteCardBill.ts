/*
 * @Author: 黎钰龙
 * @Date: 2025-01-06 20:02:16
 * @LastEditTime: 2025-05-06 09:51:39
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\card\writeCard\WriteCardBill.ts
 * 记得注释
 */
import { CardMedium } from "model/default/CardMedium"
import WriteCardBillLine from "./WriteCardBillLine"

export default class WriteCardBill {
  // 单号
  billNumber: Nullable<string> = null
  // 起始卡号
  startCardCode: Nullable<string> = null
  // 截止卡号
  endCardCode: Nullable<string> = null
  // 写卡数量
  qty: Nullable<number> = null
  // 备注
  remark: Nullable<string> = null
  // 发生营销中心
  marketingCenter: Nullable<string> = null
  // 卡介质
  cardMedium: Nullable<CardMedium> = null
  // 状态
  state: Nullable<string> = null
  // 最后写卡卡号
  lastWriteNumber: Nullable<string> = null
  // 写卡明细
  lines: WriteCardBillLine[] = []
  // 是否默认卡模板
  defaultCardTemplate: Nullable<boolean> = true
  // 指定卡模板号
  cardTemplateNumber: Nullable<string> = null
  // 指定卡模板名称
  cardTemplateName: Nullable<string> = null
}