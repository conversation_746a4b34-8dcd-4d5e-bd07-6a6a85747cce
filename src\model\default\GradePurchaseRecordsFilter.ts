/*
 * @Author: 黎钰龙
 * @Date: 2025-04-10 16:11:17
 * @LastEditTime: 2025-04-10 16:16:26
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\default\GradePurchaseRecordsFilter.ts
 * 记得注释
 */
import PageRequest from 'model/default/PageRequest'
import { GradePayType } from './GradePayType'

export default class GradePurchaseRecordsFilter extends PageRequest {
  // 交易时间-范围开始
  payTimeBegin: Nullable<Date> = null
  // 交易时间-范围结束
  payTimeEnd: Nullable<Date> = null
  // 会员信息-支持手机号/会员号/实体卡号
  memberCodeEquals: Nullable<string> = null
  // 升级方式
  payTypeEquals: Nullable<GradePayType> = null
  // 
  memberIdEquals: Nullable<string> = null
}