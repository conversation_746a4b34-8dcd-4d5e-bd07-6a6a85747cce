import { Vue, Component, Prop } from 'vue-property-decorator';
import PlaceTemplateMixins from '../PlaceTemplateMixins';

@Component({
  name: 'PageTop',
  components: {},
  mixins: [PlaceTemplateMixins],
})
export default class PageTop extends Vue {
  @Prop()
  componentItem: any;
  mounted() {
    console.log(this.componentItem, 'componentItem');
  }
  get bgUrl() {
    return 'delivery/home_page_top_new.png';
  }
  get localProperty() {
    return this.componentItem.props;
  }
  // toolbarClick(e) {
  //   this.$emit('toolBarClick', {
  //     clickName: e,
  //     activeIndex: this.activeIndex,
  //   });
  // }
}
