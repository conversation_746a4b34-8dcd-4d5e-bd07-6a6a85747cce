export default class TradeGiftGradeActivity {
  // true: 保存,false:保存并生效,第一次保存时候使用,其他场景无效
  justSave: Nullable<boolean> = null
  // 原等级代码
  sourceGrade: Nullable<string> = null
  // 原等级类型，免费，付费，特殊
  sourceGradeType: Nullable<string> = null
  // 目标等级代码
  targetGrade: Nullable<string> = null
  // 目标等级类型，免费，付费，特殊
  targetGradeType: Nullable<string> = null
  // 等级有效期
  validDate: Nullable<number> = null
  // 消费最低金额
  amount: Nullable<number> = null
  // 是否已启用规则,只做展示
  enable: Nullable<boolean> = null
}