<template>
  <div class="select-container">
    <el-popover v-model="popVisible" placement="bottom" width="400" trigger="manual">
      <div class="popover-block">
        <el-input v-model="filterValue" v-loading="searchLoading" @blur="searchBlur" :placeholder="i18n('搜索')">
          <i slot="prefix" class="el-icon-search" @click.stop="doSearch" :clearable="false"></i>
        </el-input>
        <div style="display: flex; margin-top: 12px">
          <div class="check-item" :class="{selected: tagFilter === 'memberAttr'}" @click="doFilter('memberAttr')">
            {{i18n('用户属性')}}
          </div>
          <div class="check-item" :class="{selected: tagFilter === 'memberTag'}" @click="doFilter('memberTag')">
            {{i18n('用户标签')}}
          </div>
          <div class="check-item" :class="{selected: tagFilter === 'memberCount'}" @click="doFilter('memberCount')" v-if="isShowMemberCount">
            {{i18n('用户数')}}
          </div>
        </div>
        <!-- 用户属性 -->
        <div class="list-block" v-if="showAttrList">
          <div class="list-title">{{i18n('用户属性')}}</div>
          <div class="child-block">
            <template v-for="item in attrList">
              <!-- 数值型用户属性 -->
              <el-popover v-if="item.type === 'num' && type === 'target'" placement="right" width="160" trigger="hover" :key="item.id">
                <div class="child-option" @click="doSelectAttrChild('sum', item)">{{i18n('总和')}}</div>
                <div class="child-option" @click="doSelectAttrChild('average', item)">{{i18n('均值')}}</div>
                <div class="child-option" @click="doSelectAttrChild('max', item)">{{i18n('最大值')}}</div>
                <div class="child-option" @click="doSelectAttrChild('min', item)">{{i18n('最小值')}}</div>
                <div class="child-item" style="padding-left: 0" slot="reference">
                  <div class="child-name">{{item.name}}</div>
                  <i class="el-icon-arrow-right"></i>
                </div>
              </el-popover>
              <!-- 非数值型用户标签 -->
              <div class="child-item" style="padding-left: 0" v-else @click="selectAttr(item)" :key="item.id">
                <div class="child-name">{{item.name}}</div>
              </div>
            </template>
          </div>
        </div>
        <!-- 用户标签 -->
        <div class="list-block" v-if="showTagList">
          <div class="list-title">{{i18n('用户标签')}}</div>
          <template v-for="(item, index) in tagList">
            <el-row class="list-category" v-loading="item.loading" :key="index">
              <template v-if="item.isFold">
                <i class="el-icon-caret-right" @click="doUnfold(item.uuid, index)"></i>
                <div class="category-name" @click="doUnfold(item.uuid, index)">{{item.name}}</div>
              </template>
              <template v-else>
                <i class="el-icon-caret-bottom" @click="doFold(index)"></i>
                <div class="category-name" @click="doFold(index)">{{item.name}}</div>
              </template>
            </el-row>
            <div class="child-block" :key="item.uuid" v-if="!item.isFold">
              <template v-for="(tagItem) in item.children">
                <!-- 数值型用户标签 -->
                <el-popover v-if="tagItem.tagType === 'number' && type === 'target'" placement="right" width="160" trigger="hover"
                  :key="tagItem.uuid">
                  <div class="child-option" @click="doSelectTagChild('sum', tagItem)">{{i18n('总和')}}</div>
                  <div class="child-option" @click="doSelectTagChild('average', tagItem)">{{i18n('均值')}}</div>
                  <div class="child-option" @click="doSelectTagChild('max', tagItem)">{{i18n('最大值')}}</div>
                  <div class="child-option" @click="doSelectTagChild('min', tagItem)">{{i18n('最小值')}}</div>
                  <div class="child-item" slot="reference">
                    <div class="child-name">{{tagItem.name}}</div>
                    <i class="el-icon-arrow-right"></i>
                  </div>
                </el-popover>
                <!-- 非数值型用户标签 -->
                <div class="child-item" v-else @click="selectTag(tagItem)" :key="tagItem.uuid">
                  <div class="child-name">{{tagItem.name}}</div>
                </div>
              </template>
            </div>
          </template>
        </div>
        <!-- 用户数 -->
        <div class="list-block" v-if="showMemberLabel">
          <div class="list-title">{{i18n('用户数')}}</div>
          <div class="child-block">
            <div class="child-item" style="padding-left: 0" @click="selectMemberCount">
              <div class="child-name">{{i18n('用户数')}}</div>
            </div>
          </div>
        </div>
      </div>
      <el-button :class="['span-btn', where === 'vipAnalysis' ? 'analysis-button' : '']" slot="reference" @click="popVisible = !popVisible"
        type="text">{{title}}</el-button>
    </el-popover>
  </div>
</template>

<script lang="ts" src="./PortraitIndicatorSelect.ts">
</script>

<style  lang="scss" scoped>
.select-container {
  position: relative;
}
.popover-block {
  max-height: 400px;
  overflow-y: scroll;
  .check-item {
    max-width: 500px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    padding: 0 12px;
    font-weight: 400;
    color: #242633;
    border-radius: 2px;
    background: #f7f9fc;
    border: 1px solid #d7dfeb;
    margin-right: 4px;
    margin-bottom: 3px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    cursor: pointer;
    &.selected {
      border: 1px solid #007eff;
      background: #e6f2ff;
      color: #007eff;
    }
  }
  .list-title {
    display: flex;
    align-items: center;
    font-weight: 400;
    font-size: 12px;
    color: #a8a8b6;
    line-height: 17px;
    margin-top: 12px;
    &::after {
      content: "";
      flex: 1;
      height: 0;
      border: 1px solid #f1f5f9;
      margin-left: 6px;
    }
  }
  .list-category {
    display: flex;
    align-items: center;
    font-size: 13px;
    color: #36445a;
    line-height: 24px;
    margin-top: 8px;
    &:hover {
      color: #007eff;
      cursor: pointer;
    }
    i {
      padding-right: 6px;
    }
    .category-name {
      max-width: 350px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .child-block {
    width: 100%;
    line-height: 32px;
    font-size: 14px;
    .child-item {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-left: 20px;
      &:hover {
        color: #1597ff;
        cursor: pointer;
      }
      .child-name {
        max-width: 320px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px; /* 滚动条宽度 */
  }
  &::-webkit-scrollbar-track {
    background: transparent; /* 滚动条轨道颜色 */
  }
  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1); /* 滚动条滑块颜色 */
    border-radius: 3px; /* 滑块圆角 */
  }
  &::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3); /* 滑块鼠标悬停颜色 */
  }
}
.span-btn {
  display: inline-block;
}
.child-option {
  font-weight: 400;
  font-size: 14px;
  line-height: 32px;
  &:hover {
    cursor: pointer;
    color: #1597ff;
  }
}
::v-deep .el-input__prefix {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-left: 6px;
}

.analysis-button {
  border: 1px solid #007eff;
}
</style>