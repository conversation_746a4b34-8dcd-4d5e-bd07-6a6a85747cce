/*
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:02
 * @LastEditTime: 2023-12-12 11:12:09
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\activestoredtl\ActiveStoreDtl.ts
 * 记得注释
 */
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import StoreRange from 'model/common/StoreRange'
import StoresDtl from "cmp/activestoredtl/StoresDtl"
import CheckStoreDialog from 'pages/deposit/mbrdeposit/active/dialog/CheckStoreDialog.vue'
import InMemPage from "cmp/in-mem-page/InMemPage";
import I18nPage from 'common/I18nDecorator';
import LimitedMarketingCenter from 'model/common/LimitedMarketingCenter';

@Component({
  name: 'ActiveStoreDtl',
  components: {
    StoresDtl,
    CheckStoreDialog,
    InMemPage,
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/公用/门店组件',
    '/储值/预付卡/卡模板/列表页面',
    '/权益/积分/积分初始化/未初始化状态/得积分规则/基础得积分/点击立即设置'
  ],
  auto: true
})
export default class ActiveStoreDtl extends Vue {
  stores: any[] = []
  storeLength = 0
  page = {
    currentPage: 1,
    total: 0,
    size: 40
  }
  checkdialogShow = false
  @Prop()
  data: StoreRange
  recordData: any = []

  @Watch('data', { deep: true, immediate: true })
  watchData() {
    this.initData()
  }

  //是否为多营销中心的全部门店
  isMultipleMarketingCentersAllShop(marketingCenter: LimitedMarketingCenter) {
    return marketingCenter.stores?.storeRangeType === 'ALL'
  }

  initData() {
    if (!this.data) {
      return
    }
    this.recordData = JSON.parse(JSON.stringify(this.data.stores))
    this.page.total = this.data.stores.length
    if (this.recordData && this.recordData.length > 0) {
      this.stores = []
      for (let i = 0; i < 40; i++) {
        if (this.recordData[i]) {
          this.stores.push(this.recordData[i])
        }
      }
    }
  }
  doCheckStore() {
    this.checkdialogShow = true
  }

  doCheckDialogClose() {
    this.checkdialogShow = false
  }
  getStoreLength(length: number, storeRangeType: string) {
    if (storeRangeType === 'PART') {
      let str: any = this.formatI18n('/权益/积分/积分初始化/已初始化状态/得积分规则/基础得积分/适用门店/已选{0}家门店适用')
      str = str.replace(/\{0\}/g, length)
      return str
    } else {
      let str: any = this.formatI18n('/权益/积分/积分初始化/已初始化状态/得积分规则/基础得积分/适用门店/已选{0}家门店不适用')
      str = str.replace(/\{0\}/g, length)
      return str
    }
  }
  getStoreUse(data: StoreRange,index: any) {
    if (data.marketingCenters[index].stores?.storeRangeType === 'ALL') {
      return this.i18n('全部门店适用')
    }
    if (data.marketingCenters[index].stores?.storeRangeLimitType === 'STORE' && data.marketingCenters[index].stores?.storeRangeType === 'PART') {
      return this.i18n('指定门店适用')
    }
    if (data.marketingCenters[index].stores?.storeRangeLimitType === 'STORE' && data.marketingCenters[index].stores?.storeRangeType === 'EXCLUDE') {
      return this.i18n('指定门店不适用')
    }
    if (data.marketingCenters[index].stores?.storeRangeLimitType === 'ZONE') {
      return this.i18n('指定区域适用')
    }
  }
  /**
   * 分页页码改变的回调
   */
  onHandleCurrentChange(marketingCenter: any, val: number) {
    marketingCenter.page.currentPage = val
    if (marketingCenter.recordData && marketingCenter.recordData.length > 0) {
      marketingCenter.stores = []
      for (let i = (val - 1) * 40; i < (val - 1) * 40 + 40; i++) {
        if (this.recordData[i]) {
          marketingCenter.stores.push(marketingCenter.recordData[i])
        }
      }
    }
  }
  getTotal(marketingCenter: any) {
    let str: any = this.formatI18n('/会员/会员资料/共{0}条')
    str = marketingCenter.stores.stores.length ? str.replace(/\{0\}/g, marketingCenter.stores.stores.length || 0) : ''
    return str
  }
}