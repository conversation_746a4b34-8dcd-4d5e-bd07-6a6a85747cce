<template>
    <el-dialog :before-close="doBeforeClose" :close-on-click-modal="false"
               :title="title"
               :visible.sync="dialogShow" append-to-body class="unbind-dialog">
        <div class="wrap" v-if="params">
            <div class="flex-wrap">
                <div>{{formatI18n('/会员/会员资料', '副卡会员号')}}</div>
                <div class="value">{{params.crmCode}}</div>
            </div>
            <div class="flex-wrap">
                <div>{{formatI18n('/会员/会员资料', '副卡手机号')}}</div>
                <div class="value">{{params.mobile}}</div>
            </div>
        </div>
        <div class="dialog-footer" slot="footer">
            <el-button @click="doCancel">{{formatI18n('/公用/按钮', '取消')}}</el-button>
            <el-button @click="doModalClose" size="small" type="primary">{{formatI18n('/会员/会员资料', '确认解绑')}}</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts" src="./UnBindSubDialog.ts">
</script>

<style lang="scss">
.unbind-dialog{
    display: flex;
    align-items: center;
    justify-content: center;
    .wrap{
        height: 70px;
        .item{
            width: 228px;
            height: 108px;
            border-radius: 5px;
            display: inline-block;
            margin-bottom: 24px;
            background: rgba(0, 0, 0, 0.0470588235294118);
            margin-right: 10px;
            .content{
                text-align: center;
            }
        }
    }
    .el-dialog{
        width: 440px !important;
        height: 228px !important;
    }
    .flex-wrap {
        display: flex;
        align-items: center;
        margin-top: 10px;
    }
    .value {
        margin-left: 20px;
    }
}
</style>