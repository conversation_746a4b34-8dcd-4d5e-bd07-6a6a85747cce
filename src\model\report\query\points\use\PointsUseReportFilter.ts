import Channel from 'model/common/Channel'
import BaseReportFilter from 'model/report/query/BaseReportFilter'

export default class PointsUseReportFilter extends BaseReportFilter {
  // 会员识别码等于
  identCodeEquals: Nullable<string> = null
  // 发生组织id等于
  occurredOrgIdEquals: Nullable<string> = null
  // 活动名称类似于
  activityNameLikes: Nullable<string> = null
  // 交易号类似于
  transNoLikes: Nullable<string> = null
  // 发生积分小于等于
  occurredLessOrEquals: Nullable<number> = null
  // 发生积分大于等于
  occurredGreaterOrEquals: Nullable<number> = null
  // 抵扣金额小于等于
  amountLessOrEquals: Nullable<number> = null
  // 抵扣金额大于等于
  amountGreaterOrEquals: Nullable<number> = null
  // 类型等于
  typeEquals: Nullable<string> = null
  // 交易时间位于
  tranTimeBetween: Date[] = []
  // 发生渠道
  channelEquals: Nullable<Channel> = null
  // 会员识别码类似于(大桥石化IC卡)
  identIdLike: Nullable<string> = null
}