<template>
  <el-dialog :title="formatI18n('/储值/会员储值/储值充值活动/编辑页面', '选择付费会员卡')" class="select-benefit-card-dialog" append-to-body :close-on-click-modal="false"
    :visible.sync="dialogShow" :before-close="doBeforeClose">
    <div class="wrap">
      <el-row>
        <el-col :span="8">
          <form-item :label="formatI18n('/会员/付费会员','卡名称')">
            <el-input :placeholder="formatI18n('/公用/查询条件/提示', '类似于')"
              v-model="key"></el-input>
          </form-item>
        </el-col>
        <el-col :span="8">
          <form-item>
            <el-button @click="doSearch" type="primary">{{ formatI18n("/公用/券模板", "查询") }}</el-button>
            <el-button @click="doReset">{{ formatI18n("/公用/券模板", "重置") }}</el-button>
          </form-item>
        </el-col>
      </el-row>
      <el-table :data="data" border stripe ref="curTable" height="400px"
        style="width: 100%;margin-top: 20px" v-loading="loading">
        <el-table-column :label="formatI18n('/会员/付费会员/付费会员卡代码','名称')">
          <template slot-scope="scope">
            <el-radio v-model="radio" :label="scope.row.uuid" @change="changeRadio(scope.row)">
              <p> {{ scope.row.benefitCardTemplate ? scope.row.benefitCardTemplate.id : "--" }}</p>
              <p> {{ scope.row.benefitCardTemplate ? scope.row.benefitCardTemplate.name : "--" }}</p>
            </el-radio>
          </template>
        </el-table-column>
        <el-table-column :label="formatI18n('/储值/预付卡/卡模板/列表页面', '有效期')">
          <template slot-scope="scope">
            <div v-if="scope.row.payRule">
              <span style="margin-left: 10px">{{ scope.row.payRule.pay }}{{formatI18n('/公用/券模板', '元')}}，</span>
              <span>{{ scope.row.payRule.value }}</span>
              <span>{{ scope.row.payRule.type | dateTypeName }}</span>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- <div class="page" style="margin-top: 15px">
            <el-pagination
                    :current-page="page.currentPage"
                    :page-size="page.size"
                    :page-sizes="[10, 20, 30, 40]"
                    :total="page.total"
                    @current-change="onHandleCurrentChange"
                    @size-change="onHandleSizeChange"
                    background
                    layout="total, prev, pager, next, sizes,  jumper">
            </el-pagination>
        </div> -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogShow = false">{{ formatI18n('/资料/区域', '取\ 消') }}</el-button>
      <el-button @click="doModalClose" size="small" type="primary">{{ formatI18n("/公用/按钮", "确定") }}</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./SelectBenefitCardDialog.ts"></script>

<style lang="scss" scoped>
.select-benefit-card-dialog {
  display: flex;
  align-items: center;
  justify-content: center;
  ::v-deep .el-dialog {
    width: 1024px;
    height: 640px;
    margin: 0 !important;

      .el-radio {
        display: flex;
        align-items: center;
      }

  }
  // .wrap{
  //     height: 440px;
  //     overflow: auto;
  //     .item{
  //         width: 228px;
  //         height: 108px;
  //         border: 1px solid #c7c7c7;
  //         border-radius: 10px;
  //         display: inline-block;
  //         margin-bottom: 24px;

  //         &:nth-child(odd) {
  //             margin-right: 12px;

  //         }
  //         &:nth-child(even) {
  //             margin-left: 12px;
  //         }
  //     }
  // }
}
</style>
