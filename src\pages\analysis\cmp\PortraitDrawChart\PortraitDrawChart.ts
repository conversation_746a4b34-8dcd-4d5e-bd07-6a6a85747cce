/*
 * @Author: 黎钰龙
 * @Date: 2025-02-11 17:56:13
 * @LastEditTime: 2025-03-06 14:34:51
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\analysis\cmp\PortraitDrawChart\PortraitDrawChart.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import BCustomerProfileLine from 'model/analysis/BCustomerProfileLine';
import { SortEnum } from 'model/analysis/SortEnum';
import CustomRangeDialog from '../CustomRangeDialog/CustomRangeDialog';
import { CustomerProfileType } from 'model/analysis/CustomerProfileType';
import { DateGroupType } from 'model/analysis/DateGroupType';
import Echart from 'echarts/lib/echarts'
import 'echarts/lib/chart/bar'
import 'echarts/lib/chart/pie'
import 'echarts/lib/component/legend'
import 'echarts/lib/component/legendScroll';
import 'echarts/lib/component/tooltip';
import 'echarts/lib/component/dataZoom';
import CommonUtil from 'util/CommonUtil';
import BCustomerProfileDimension from 'model/analysis/BCustomerProfileDimension';
import SumCycleDialog from '../SumCycleDialog/SumCycleDialog';
import CalculationResult from 'model/analysis/CalculationResult';
import CrossCalculationResult from 'model/analysis/CrossCalculationResult';
import CustomRangeInfo from 'model/default/CustomRangeInfo';
import { ProfileTargetTypeUtil } from 'model/analysis/BCustomerProfileTarget';

export enum ChartType {
  strip = 'strip', // 条形图（横向柱状图）
  bar = 'bar', // 柱状图
  ring = 'ring',  // 环形图
  table = 'table',  // 表格
  pie = 'pie',  // 饼图
}

@Component({
  name: 'PortraitDrawChart',
  components: {
    CustomRangeDialog,
    SumCycleDialog
  }
})
@I18nPage({
  prefix: [
    '/公用/公共组件/客群选择弹框组件',
    '/公用/券模板',
    '/数据/客群画像'
  ],
  auto: true
})
export default class PortraitDrawChart extends Vue {
  $refs: any
  @Prop() chartData: BCustomerProfileLine;  //目标客群图表数据
  @Prop({ type: String }) customerName: string; //目标客群名称
  @Prop({ type: String }) compareCustomerName: string; //对比客群名称
  @Prop() compareData: Nullable<BCustomerProfileLine>;  //对比客群图表数据
  @Prop({ type: Boolean }) isOnlyView: boolean;  //是否只查看
  chartType: ChartType = ChartType.strip  //图表类型
  dataCount: 7 | 10 | 15 | 20 | 25 = 7  //展示多少条数据
  sortType: SortEnum = SortEnum.coverageDesc  //排序方式
  rangeList: CustomRangeInfo[] = []  //维度区间
  sumCycleList: DateGroupType[] = []  //汇总周期
  $echarts: any
  $compareCharts: any
  targetChart: any  //目标客群图表
  compareChart: any  //对比客群图表
  editTitleVisible: boolean = false //编辑标题的弹窗
  newTitle: string = '' //新标题

  @Watch('chartData.calculationResult', { deep: true })
  redrawChart() {
    // 检测到表单数据发生变化，重新绘制图表
    this.doDrawChart()
  }

  // 是否 多维度图表
  get isMultiDimension() {
    return this.chartData.dimension1 && this.chartData.dimension2
  }

  // 是否存在 数值型属性/标签
  get isNumType() {
    return this.chartData.dimension1?.type === CustomerProfileType.num || this.chartData.dimension2?.type === CustomerProfileType.num
  }

  // 是否双维度都为 数值型属性/标签
  get isBothNum() {
    return this.chartData.dimension1?.type === CustomerProfileType.num && this.chartData.dimension2?.type === CustomerProfileType.num
  }

  // 是否存在 日期型属性/标签
  get isDateType() {
    return this.chartData.dimension1?.type === CustomerProfileType.date || this.chartData.dimension2?.type === CustomerProfileType.date
  }

  // 是否双维度都为 日期型属性/标签
  get isBothData() {
    return this.chartData.dimension1?.type === CustomerProfileType.date && this.chartData.dimension2?.type === CustomerProfileType.date
  }

  // 是否存在 对比客群
  get hasCompare() {
    return !!this.compareData
  }

  get getRangeText() {
    const range1 = this.chartData.dimension1?.useDefaultGoupSection ? this.i18n('/会员/标签客群/标签/使用默认区间') : this.i18n('/会员/标签客群/标签/使用自定义区间')
    const range2 = this.chartData.dimension2?.useDefaultGoupSection ? this.i18n('/会员/标签客群/标签/使用默认区间') : this.i18n('/会员/标签客群/标签/使用自定义区间')
    if (this.isMultiDimension) {
      if (this.chartData.dimension1?.type === CustomerProfileType.num && this.chartData.dimension2?.type === CustomerProfileType.num) {
        return `${range1} X ${range2}`
      } else if (this.chartData.dimension1?.type === CustomerProfileType.num) {
        return range1
      } else {
        return range2
      }
    } else {
      return range1
    }
  }

  get getCycleText() {
    const cycle1 = this.getCycleName(this.chartData.dimension1?.dateGroupType!)
    const cycle2 = this.getCycleName(this.chartData.dimension2?.dateGroupType!)
    if (this.isMultiDimension) {
      if (this.chartData.dimension1?.type === CustomerProfileType.date && this.chartData.dimension2?.type === CustomerProfileType.date) {
        return `${cycle1} X ${cycle2}`
      } else if (this.chartData.dimension1?.type === CustomerProfileType.date) {
        return cycle1
      } else {
        return cycle2
      }
    } else {
      return cycle1
    }
  }

  // 维度标题列表
  get dimensionTitleList() {
    const list: string[] = []
    const dimension1Name = this.chartData.dimension1?.tagName || this.chartData.dimension1?.memberPropName
    if (dimension1Name) {
      list.push(dimension1Name)
    }
    const dimension2Name = this.chartData.dimension2?.tagName || this.chartData.dimension2?.memberPropName
    if (dimension2Name) {
      list.push(dimension2Name)
    }
    return list
  }

  // 数值维度标题列表
  get numTitleList() {
    const list: string[] = []
    const dimension1Name = this.chartData.dimension1?.tagName || this.chartData.dimension1?.memberPropName
    if (dimension1Name && this.chartData.dimension1?.type === CustomerProfileType.num) {
      list.push(dimension1Name)
    }
    const dimension2Name = this.chartData.dimension2?.tagName || this.chartData.dimension2?.memberPropName
    if (dimension2Name && this.chartData.dimension2?.type === CustomerProfileType.num) {
      list.push(dimension2Name)
    }
    return list
  }

  // 日期维度标题列表
  get dataTitleList() {
    const list: string[] = []
    const dimension1Name = this.chartData.dimension1?.tagName || this.chartData.dimension1?.memberPropName
    if (dimension1Name && this.chartData.dimension1?.type === CustomerProfileType.date) {
      list.push(dimension1Name)
    }
    const dimension2Name = this.chartData.dimension2?.tagName || this.chartData.dimension2?.memberPropName
    if (dimension2Name && this.chartData.dimension2?.type === CustomerProfileType.date) {
      list.push(dimension2Name)
    }
    return list
  }

  get getChartTitle() {
    const name = this.chartData.name
    const dimension1 = this.chartData.dimension1?.tagName || this.chartData.dimension1?.memberPropName
    const dimension2 = this.chartData.dimension2?.tagName || this.chartData.dimension2?.memberPropName
    return name || (dimension2 ? `${dimension1} X ${dimension2}` : dimension1 || '--')
  }


  get tableData() {
    if (!this.isMultiDimension) {
      // 单维度图表
      const targetData = this.chartData.calculationResult?.normalCalculationResults || []
      const compareData = this.compareData?.calculationResult?.normalCalculationResults || []
      const targetTotal = targetData.reduce((total: number, cur) => total + Number(cur.value), 0) || 0
      const compareTotal = compareData.reduce((total: number, cur) => total + Number(cur.value), 0) || 0
      return [
        ...targetData.map((item) => {
          return { ...item, title: this.customerName, percent: ((Number(item.value) / targetTotal) * 100).toFixed(2) }
        }),
        ...compareData.map((item) => {
          return { ...item, title: this.compareCustomerName, percent: ((Number(item.value) / compareTotal) * 100).toFixed(2) }
        })
      ]
    } else {
      // 多维度图表
      const targetTotal = this.chartData.calculationResult?.crossCalculationResults.reduce((sum, item) => {
        return sum + item.dimensionValue.reduce((subSum, dv) => {
          return subSum + (parseFloat(dv.value!) || 0); // 将 value 转换为数值并累加
        }, 0);
      }, 0) || 0
      const compareTotal = this.compareData?.calculationResult?.crossCalculationResults.reduce((sum, item) => {
        return sum + item.dimensionValue.reduce((subSum, dv) => {
          return subSum + (parseFloat(dv.value!) || 0); // 将 value 转换为数值并累加
        }, 0);
      }, 0) || 0
      const targetData = this.multiTableFormat(this.chartData.calculationResult?.crossCalculationResults!, targetTotal, this.customerName)
      const compareData = this.multiTableFormat(this.compareData?.calculationResult?.crossCalculationResults!, compareTotal, this.compareCustomerName)
      return [...targetData, ...compareData]
    }
  }

  get tableValueLabel() {
    let str = this.chartData.target?.memberPropName || this.chartData.target?.tagName
    if (this.chartData.target?.type) {
      str += '：' + ProfileTargetTypeUtil.getTypeName(this.chartData.target?.type)
    }
    return str || this.i18n('用户数')
  }

  // 是否存在自定义指标
  get hasCustomTarget() {
    return !!this.chartData.target
  }

  // 多维度图表 table数据格式化
  multiTableFormat(val: CrossCalculationResult[], total: number, title: string) {
    const arr: any[] = []
    val?.forEach((item) => {
      item.dimensionValue.forEach((val) => {
        arr.push({
          dimension: item.dimension,  //维度1
          dimension2: val.dimension, //维度2
          title: title,  //人群名称
          value: val.value, //数量
          percent: ((Number(val.value) / total) * 100).toFixed(2)  //数量占比
        })
      })
    })
    return arr
  }

  // 当前图表类型对应的配置项
  getChartOption(type: 'target' | 'compare') {
    switch (this.chartType) {
      case ChartType.strip:
        return this.stripChartOption(type)
      case ChartType.bar:
        return this.barChartOption(type)
      case ChartType.pie:
        return this.pieChartOption(type)
      case ChartType.ring:
        return this.ringChartOption(type)
      default:
        break;
    }
  }

  // 横向柱状图 配置项
  stripChartOption(type: 'target' | 'compare') {
    const chartInfo = type === 'target' ? this.chartData.calculationResult : this.compareData?.calculationResult
    const yAxisNameList = chartInfo?.normalCalculationResults.map((item) => item.dimension)
    const xAxisValueList = chartInfo?.normalCalculationResults.map((item) => item.value)
    const total = chartInfo?.normalCalculationResults.reduce((total: number, cur) => total + Number(cur.value), 0) || 0
    return {
      tooltip: {
        show: true,
        trigger: 'item',
        backgroundColor: 'rgba(0,0,0,0.8)',
        textStyle: {
          color: '#ffffff'
        },
        formatter: (params: any) => {
          let result = '';
          result += `<div>${params.name}</div>`
          result += `<div>${this.hasCustomTarget ? this.i18n('总数（百分比）') : this.i18n('总人数（百分比）')}：${params.value}（${total > 0 ? ((Number(params.value) / total) * 100).toFixed(2) + '%' : '--'}）</div>`
          return result;
        }
      },
      xAxis: {
        axisLine: {
          show: false
        },
        axisLabel: {
          show: true
        },
        axisTick: {
          show: false
        },
        type: 'value' // 这里是value，表示X轴是数值类型，因为是横向柱状图，所以X轴是数值轴，Y轴是类别轴
      },
      yAxis: {
        axisLine: {
          show: false
        },
        axisLabel: {
          show: true,
          interval: 0, // 显示所有标签
          formatter: function (value: any) {
            // 设置最大显示字符长度（中文算2个长度）
            var maxLength = 8;
            // 如果文字长度超过最大长度，则进行截断并添加省略号
            if (value.length > CommonUtil.truncateString(value.trim(), maxLength).length) {
              return CommonUtil.truncateString(value.trim(), maxLength) + '...';
            } else {
              // 如果没有超过最大长度，则正常显示
              return value;
            }
          },
        },
        axisTick: {
          show: false
        },
        type: 'category', // 这里Y轴表示类别类型
        data: yAxisNameList || []
      },
      series: [{
        type: 'bar',
        barMinHeight: 5, // 最小高度
        data: xAxisValueList || [], // 这里的数据对应Y轴上的类别，因为是横向柱状图，所以这里是X轴上的值
        barWidth: '27px', // 条形图宽度
        itemStyle: { // 设置条形图的样式
          normal: {
            color: function (params: any) {
              let colorList = CommonUtil.getColorList();
              return colorList[params.dataIndex];
            },
            label: {
              show: true,
              position: "right",  // 展示在柱子的右侧
              textStyle: {
                color: "#898FA3",
                fontSize: 12
              },
              formatter: (params: any) => {
                let result = '';
                if (params.value > 0) {
                  result += `${params.value} ${this.hasCustomTarget ? '' : this.i18n('人')} ${total > 0 ? ((Number(params.value) / total) * 100).toFixed(2) + '%' : '--'}`
                } else {
                  result += `0`
                }
                return result;
              }
            }
          }
        }
      }],
      dataZoom: [
        {
          type: "slider",
          show: true,
          orient: 'vertical',
          width: 20
        },
      ],
    }
  }

  // 纵向柱状图 配置项
  barChartOption(type: 'target' | 'compare') {
    const chartInfo = type === 'target' ? this.chartData.calculationResult : this.compareData?.calculationResult
    if (this.isMultiDimension) {
      // 多维度图表
      const xAxisNameList = chartInfo?.crossCalculationResults.map((item) => item.dimension)  //横坐标
      const dimensionNameList = Array.from(new Set(chartInfo?.crossCalculationResults.flatMap(item => item.dimensionValue.map(dv => dv.dimension))));
      const seriesData = dimensionNameList.map(dimension => {
        return {
          name: dimension,
          type: 'bar',
          data: chartInfo?.crossCalculationResults.map(item => {
            const dimensionValue = item.dimensionValue.find(dv => dv.dimension === dimension);
            return dimensionValue ? parseFloat(dimensionValue.value!) : 0;
          }),
          barWidth: '20px', // 条形图宽度
          label: {
            show: true,
            position: "top",  // 展示在柱子的顶部
            textStyle: {
              color: "#898FA3",
              fontSize: 12
            },
            formatter: (params: any) => {
              let result = '';
              if (params.value > 0) {
                result += `${params.value} ${this.hasCustomTarget ? '' : this.i18n('人')} ${total > 0 ? ((Number(params.value) / total) * 100).toFixed(2) + '%' : '--'}`
              } else {
                result += `0`
              }
              return result;
            }
          }
        }
      });
      let total = chartInfo?.crossCalculationResults.reduce((sum, item) => {
        return sum + item.dimensionValue.reduce((subSum, dv) => {
          return subSum + (parseFloat(dv.value!) || 0); // 将 value 转换为数值并累加
        }, 0);
      }, 0) || 0
      const option = {
        tooltip: {
          show: true,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          backgroundColor: 'rgba(0,0,0,0.8)',
          textStyle: {
            color: '#ffffff'
          },
          formatter: (params: any) => {
            let result = '';
            params.forEach((item: any, index: number) => {
              if (index === 0) {
                result += `<div>${item.name}：</div>`
              }
              result += `<div>${item.seriesName}：${item.value}（${total > 0 ? ((Number(item.value) / total) * 100).toFixed(2) + '%' : '--'}）</div>`
            })
            return result;
          }
        },
        legend: {
          data: dimensionNameList,
        },
        xAxis: {
          axisLine: {
            show: false
          },
          axisLabel: {
            show: true,
            interval: 0, // 显示所有标签
            formatter: function (value: any) {
              // 设置最大显示长度
              var maxLength = Number(xAxisNameList?.length) > 7 ? 10 : 16;
              // 如果文字长度超过最大长度，则进行截断并添加省略号
              if (value.length > CommonUtil.truncateString(value.trim(), maxLength).length) {
                return CommonUtil.truncateString(value.trim(), maxLength) + '...';
              } else {
                // 如果没有超过最大长度，则正常显示
                return value;
              }
            },
            rotate: Number(xAxisNameList?.length) > 7 ? 45 : 0 // 调整倾斜角度
          },
          axisTick: {
            show: false
          },
          type: 'category',
          data: xAxisNameList,
          minInterval: 10 // 设置最小间隔为10
        },
        yAxis: {
          axisLine: {
            show: false
          },
          axisLabel: {
            show: true
          },
          axisTick: {
            show: false
          },
          type: 'value',
        },
        series: seriesData,
        dataZoom: [
          {
            type: "slider",
            show: true,
            height: 20
          },
        ],
      }
      console.log('看看结果', option)
      return option
    } else {
      // 单维度图表
      const xAxisNameList = chartInfo?.normalCalculationResults.map((item) => item.dimension)
      const yAxisValueList = chartInfo?.normalCalculationResults.map((item) => item.value)
      const total = chartInfo?.normalCalculationResults.reduce((total: number, cur) => total + Number(cur.value), 0) || 0
      return {
        tooltip: {
          show: true,
          trigger: 'item',
          backgroundColor: 'rgba(0,0,0,0.8)',
          textStyle: {
            color: '#ffffff'
          },
          formatter: (params: any) => {
            let result = '';
            result += `<div>${params.name}</div>`
            result += `<div>${this.hasCustomTarget ? this.i18n('总数（百分比）') : this.i18n('总人数（百分比）')}：${params.value}（${total > 0 ? ((Number(params.value) / total) * 100).toFixed(2) + '%' : '--'}）</div>`
            return result;
          }
        },
        xAxis: {
          axisLine: {
            show: false
          },
          axisLabel: {
            show: true,
            interval: 0, // 显示所有标签
            formatter: function (value: any) {
              // 设置最大显示长度
              var maxLength = Number(xAxisNameList?.length) > 7 ? 10 : 16;
              // 如果文字长度超过最大长度，则进行截断并添加省略号
              if (value.length > CommonUtil.truncateString(value.trim(), maxLength).length) {
                return CommonUtil.truncateString(value.trim(), maxLength) + '...';
              } else {
                // 如果没有超过最大长度，则正常显示
                return value;
              }
            },
            rotate: Number(xAxisNameList?.length) > 7 ? 45 : 0 // 调整倾斜角度
          },
          axisTick: {
            show: false
          },
          type: 'category',
          data: xAxisNameList || []
        },
        yAxis: {
          axisLine: {
            show: false
          },
          axisLabel: {
            show: true
          },
          axisTick: {
            show: false
          },
          type: 'value'
        },
        series: [{
          type: 'bar',
          barMinHeight: 5, // 最小高度
          data: yAxisValueList || [], // 这里的数据对应x轴上类别的值
          barWidth: '27px', // 条形图宽度
          itemStyle: { // 设置条形图的样式
            normal: {
              color: function (params: any) {
                let colorList = CommonUtil.getColorList();
                return colorList[params.dataIndex];
              },
              label: {
                show: true,
                position: "top",  // 展示在柱子的顶部
                textStyle: {
                  color: "#898FA3",
                  fontSize: 12
                },
                formatter: (params: any) => {
                  let result = '';
                  if (params.value > 0) {
                    result += `${params.value} ${this.hasCustomTarget ? '' : this.i18n('人')} ${total > 0 ? ((Number(params.value) / total) * 100).toFixed(2) + '%' : '--'}`
                  } else {
                    result += `0`
                  }
                  return result;
                }
              }
            }
          }
        }],
        dataZoom: [
          {
            type: "slider",
            show: true,
            height: 20
          },
        ],
      }
    }
  }

  // 环形图 配置项
  ringChartOption(type: 'target' | 'compare') {
    const chartInfo = type === 'target' ? this.chartData.calculationResult : this.compareData?.calculationResult
    const formateDataList = chartInfo?.normalCalculationResults.map((item) => {
      return {
        name: item.dimension,
        value: Number(item.value)
      }
    })
    const valueTotal = formateDataList?.reduce((data: number, next) => {
      return data + next.value
    }, 0) || 0
    return {
      legend: {
        type: 'scroll',
        show: true,
        top: 'top',
        selectedMode: false,
        data: formateDataList?.map((item) => item.name) || []
      },
      yAxis: {
        show: false
      },
      tooltip: {
        show: true,
        trigger: 'item',
        backgroundColor: 'rgba(0,0,0,0.8)',
        textStyle: {
          color: '#ffffff'
        },
        formatter: (params: any) => {
          let result = '';
          result += `<div>${params.name}</div>`
          result += `<div>${this.hasCustomTarget ? this.i18n('总数（百分比）') : this.i18n('总人数（百分比）')}：${params.value}（${valueTotal > 0 ? ((Number(params.value) / valueTotal) * 100).toFixed(2) + '%' : '--'}）</div>`
          return result;
        }
      },
      series: [
        {
          type: 'pie',
          radius: ['40%', '55%'], // 设置环形大小，第一个值是内圆半径，第二个值是外圆半径
          avoidLabelOverlap: true,
          // minAngle: 10,
          padAngle: 4,
          label: {
            show: true,
            position: 'outside',
            formatter: (params: any) => {
              let result = '';
              result += `${params.data.name}：${valueTotal > 0 ? ((params.data.value / valueTotal) * 100).toFixed(2) + '%' : '--'}`
              return result;
            }
          },
          labelLine: {
            show: true
          },
          data: formateDataList || [],
          itemStyle: { // 设置条形图的样式
            normal: {
              color: function (params: any) {
                let colorList = CommonUtil.getColorList();
                return colorList[params.dataIndex];
              }
            }
          }
        }
      ]
    };
  }

  // 饼图 配置项
  pieChartOption(type: 'target' | 'compare') {
    const chartInfo = type === 'target' ? this.chartData.calculationResult : this.compareData?.calculationResult
    const formateDataList = chartInfo?.normalCalculationResults.map((item) => {
      return {
        name: item.dimension,
        value: Number(item.value)
      }
    })
    const valueTotal = formateDataList?.reduce((data: number, next) => {
      return data + next.value
    }, 0) || 0
    return {
      legend: {
        type: 'scroll',
        show: true,
        top: 'top',
        selectedMode: false,
        data: formateDataList?.map((item) => item.name) || []
      },
      yAxis: {
        show: false
      },
      tooltip: {
        show: true,
        trigger: 'item',
        backgroundColor: 'rgba(0,0,0,0.8)',
        textStyle: {
          color: '#ffffff'
        },
        formatter: (params: any) => {
          let result = '';
          result += `<div>${params.name}</div>`
          result += `<div>${this.hasCustomTarget ? this.i18n('总数（百分比）') : this.i18n('总人数（百分比）')}：${params.value}（${valueTotal > 0 ? ((Number(params.value) / valueTotal) * 100).toFixed(2) + '%' : '--'}）</div>`
          return result;
        }
      },
      series: [
        {
          type: 'pie',
          avoidLabelOverlap: true,
          // minAngle: 10,
          padAngle: 4,
          label: {
            show: true,
            position: 'outside',
            formatter: (params: any) => {
              let result = '';
              result += `${params.data.name}：${valueTotal > 0 ? ((params.data.value / valueTotal) * 100).toFixed(2) + '%' : '--'}`
              return result;
            }
          },
          labelLine: {
            show: true
          },
          data: formateDataList || [],
          itemStyle: { // 设置条形图的样式
            normal: {
              color: function (params: any) {
                let colorList = CommonUtil.getColorList();
                return colorList[params.dataIndex];
              }
            }
          }
        }
      ]
    };
  }

  // 当前维度占客群总数的百分比
  getDimensionPercent(type: 'target' | 'compare') {
    const num = type === 'target' ? this.chartData.coveredRate : this.compareData?.coveredRate
    const rate = Number(num) * 100
    if (rate) {
      return rate.toFixed(2) + '%'
    } else {
      return '--'
    }
  }

  created() {
    this.$echarts = Echart
    this.$compareCharts = Echart
  }

  mounted() {
    if (this.isMultiDimension) {
      this.chartType = ChartType.bar
    }
    this.doDrawChart()
  }

  // 编辑画像卡片
  doEdit() {
    this.$emit('edit', this.chartData.uuid)
  }

  // 删除画像卡片
  doRemove() {
    this.$confirm(
      this.i18n("确定删除该图表吗?"),
      this.i18n("/公用/按钮/删除"),
      {
        confirmButtonText: this.formatI18n("/公用/按钮", "确定") as string,
        cancelButtonText: this.formatI18n("/公用/按钮", "取消") as string,
      }
    ).then(() => {
      this.$emit('remove', this.chartData.uuid)
    });
  }

  doDrawChart() {
    this.$nextTick(() => {
      const ele = document.getElementById('myEcharts' + '_target_' + this.chartData.uuid)
      this.targetChart = this.$echarts.init(ele)
      if (this.targetChart) {
        this.targetChart.setOption(this.getChartOption('target'), true)
      }
      if (this.hasCompare) {
        const compareEle = document.getElementById('myEcharts' + '_compare_' + this.chartData.uuid)
        this.compareChart = this.$compareCharts.init(compareEle)
        if (this.compareChart) {
          this.compareChart.setOption(this.getChartOption('compare'), true)
        }
      }
    })
  }

  getChartName(val: ChartType) {
    switch (val) {
      case ChartType.strip:
        return this.i18n('条形图')
      case ChartType.bar:
        return this.i18n('柱状图')
      case ChartType.ring:
        return this.i18n('环形图')
      case ChartType.table:
        return this.i18n('表格')
      case ChartType.pie:
        return this.i18n('饼图')
      default:
        break;
    }
  }

  getCountName(val: 7 | 10 | 15 | 20 | 25) {
    switch (val) {
      case 7:
        return this.i18n('前{0}项', ['7']) + (this.isMultiDimension ? ` X ${this.i18n('前{0}项', ['7'])}` : '')
      case 10:
        return this.i18n('前{0}项', ['10']) + (this.isMultiDimension ? ` X ${this.i18n('前{0}项', ['10'])}` : '')
      case 15:
        return this.i18n('前{0}项', ['15']) + (this.isMultiDimension ? ` X ${this.i18n('前{0}项', ['15'])}` : '')
      case 20:
        return this.i18n('前{0}项', ['20']) + (this.isMultiDimension ? ` X ${this.i18n('前{0}项', ['20'])}` : '')
      case 25:
        return this.i18n('前{0}项', ['25']) + (this.isMultiDimension ? ` X ${this.i18n('前{0}项', ['25'])}` : '')
      default:
        break;
    }
  }

  getSortName(val: SortEnum) {
    switch (val) {
      case SortEnum.coverageAsc:
        return this.hasCustomTarget ? this.i18n('按指标值升序') : this.i18n('按目标人群的覆盖人数升序')
      case SortEnum.coverageDesc:
        return this.hasCustomTarget ? this.i18n('按指标值降序') : this.i18n('按目标人群的覆盖人数降序')
      case SortEnum.dimensionAsc:
        return this.i18n('按维度值升序')
      case SortEnum.dimensionDesc:
        return this.i18n('按维度值降序')
      default:
        break;
    }
  }

  getCycleName(val: DateGroupType) {
    switch (val) {
      case DateGroupType.year:
        return this.i18n('按年汇总')
      case DateGroupType.month:
        return this.i18n('按月汇总')
      case DateGroupType.date:
        return this.i18n('按日汇总')
      default:
        break;
    }
  }

  changeChartType(type: ChartType) {
    if (type !== this.chartType) {
      this.chartType = type
      if (type !== ChartType.table) {
        this.doDrawChart()
      }
    }
  }

  changeChartCount(count: 7 | 10 | 15 | 20 | 25) {
    this.dataCount = count
    this.regenerateChart()
  }

  changeChartSort(sort: SortEnum) {
    this.sortType = sort
    this.regenerateChart()
  }

  // 编辑区间内容
  doEditRange() {
    if (this.isOnlyView) return
    this.rangeList = []
    if (this.chartData.dimension1?.type === CustomerProfileType.num && this.chartData.dimension1?.customerProfileGoupSection?.length) {
      const obj = new CustomRangeInfo()
      obj.type = this.chartData.dimension1?.useDefaultGoupSection ? 'default' : 'custom'
      obj.customArr = this.chartData.dimension1?.customerProfileGoupSection as any || []
      this.rangeList.push(obj)
    }
    if (this.chartData.dimension2?.type === CustomerProfileType.num && this.chartData.dimension2?.customerProfileGoupSection?.length) {
      const obj = new CustomRangeInfo()
      obj.type = this.chartData.dimension2?.useDefaultGoupSection ? 'default' : 'custom'
      obj.customArr = this.chartData.dimension2?.customerProfileGoupSection as any || []
      this.rangeList.push(obj)
    }
    this.$nextTick(() => {
      this.$refs.customRangeDialog.open(this.isMultiDimension && this.isBothNum ? 2 : 1)
    })
  }

  // 编辑汇总周期内容
  doEditCycle() {
    if (this.isOnlyView) return
    this.sumCycleList = []
    if (this.chartData.dimension1?.dateGroupType && this.chartData.dimension1.type === CustomerProfileType.date) {
      this.sumCycleList.push(this.chartData.dimension1?.dateGroupType)
    }
    if (this.chartData.dimension2?.dateGroupType && this.chartData.dimension2.type === CustomerProfileType.date) {
      this.sumCycleList.push(this.chartData.dimension2?.dateGroupType)
    }
    this.$nextTick(() => {
      this.$refs.sumCycleDialog.open(this.isMultiDimension && this.isBothData ? 2 : 1)
    })
  }

  // 确认提交区间内容
  doSubmitRange(val: CustomRangeInfo[]) {
    this.rangeList = val || []
    this.regenerateChart()
  }

  doSubmitCycle(cycle: DateGroupType[]) {
    this.sumCycleList = cycle
    this.regenerateChart()
  }

  // 重新生成图表(数据改动)
  regenerateChart(newName?: string) {
    const res: BCustomerProfileLine = JSON.parse(JSON.stringify(this.chartData))
    if (newName) {
      res.name = newName
    }
    res.range = this.dataCount
    res.sort = this.sortType
    // 区间范围
    if (this.rangeList?.length) {
      if (this.isBothNum) {
        // 如果两个维度都是数值类型，则按顺序赋值区间
        res.dimension1!.customerProfileGoupSection = this.rangeList[0].customArr as any || []
        if (this.rangeList.length > 1) {
          res.dimension2!.customerProfileGoupSection = this.rangeList[1].customArr as any || []
        }
      } else {
        // 如果只有一个维度是数值类型，则给该维度赋值区间
        if (this.chartData.dimension1?.type === CustomerProfileType.num) {
          res.dimension1!.customerProfileGoupSection = this.rangeList[0].customArr as any || []
        } else if (this.chartData.dimension2?.type === CustomerProfileType.num) {
          res.dimension2!.customerProfileGoupSection = this.rangeList[0].customArr as any || []
        }
      }
    }
    if (res.dimension1) {
      // 是否使用了默认区间
      res.dimension1.useDefaultGoupSection = !res.dimension1.customerProfileGoupSection?.length
    }
    if (res.dimension2) {
      // 是否使用了默认区间
      res.dimension2.useDefaultGoupSection = !res.dimension2.customerProfileGoupSection?.length
    }
    // 汇总周期
    if (this.sumCycleList?.length) {
      if (this.isBothData) {
        // 如果两个维度都是日期类型，则按顺序赋值
        res.dimension1!.dateGroupType = this.sumCycleList[0] || null
        if (this.sumCycleList.length > 1) {
          res.dimension2!.dateGroupType = this.sumCycleList[1] || null
        }
      } else {
        // 如果只有一个维度是日期类型，则给该维度赋值
        if (this.chartData.dimension1?.type === CustomerProfileType.date) {
          res.dimension1!.dateGroupType = this.sumCycleList[0] as any || null
        } else if (this.chartData.dimension2?.type === CustomerProfileType.date) {
          res.dimension2!.dateGroupType = this.sumCycleList[0] as any || null
        }
      }
    }
    console.log('提交的数据', res);
    this.$emit('change', res)
  }

  confirmChangeTitle() {
    this.editTitleVisible = false
    this.regenerateChart(this.newTitle)
    this.newTitle = ''
  }
}