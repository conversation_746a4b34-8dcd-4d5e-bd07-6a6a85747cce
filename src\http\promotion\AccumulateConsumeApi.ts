import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import BCumulativeConsumeActivity from 'model/promotion/BCumulativeConsumeActivity'

export default class AccumulateConsumeApi {
  /**
   * 新建累计消费有礼活动
   * 新建累计消费有礼活动
   * 
   */
  static saveConsumeCalendarActivity(body: BCumulativeConsumeActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/coupon-activity/saveCumulativeConsumeActivity`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 累计消费有礼活动详情
   * 累计消费有礼活动详情。
   * 
   */
  static getConsumeCalendarActivity(id: string): Promise<Response<BCumulativeConsumeActivity>> {
    return ApiClient.server().get(`/v1/coupon-activity/getCumulativeConsumeActivity/${id}`, {
    }).then((res) => {
      return res.data
    })
  }
}