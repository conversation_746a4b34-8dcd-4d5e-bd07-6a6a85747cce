<!--
 * @Author: haiding <EMAIL>
 * @Date: 2025-02-19 10:03:52
 * @LastEditors: haiding <EMAIL>
 * @LastEditTime: 2025-03-13 13:24:52
 * @FilePath: \new-kequn\src\pages\invest\page-manage\cmp\page-place-components\text-from\textFron.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div
    class="coupon"
    :style="{
      padding:
        localProperty.styMarginTop +
        'px ' +
        localProperty.styMarginRight +
        'px ' +
        localProperty.styMarginBottom +
        'px ' +
        localProperty.styMarginLeft +
        'px',
    }"
    @click="activeTemplate"
  >
    <div class="toolBar" v-if="activeIndex === index">
      <setting-toolbar :btns="toolBarBtns" @click="toolbarClick"></setting-toolbar>
    </div>
    <div
      class="text"
      v-if="localProperty.propText"
      :style="{ fontSize: localProperty.styFontSize, color: localProperty.styFontColor, textAlign: localProperty.styTextAlign }"
      v-text="localProperty.propText"
    ></div>
    <div class="text" v-else>
      <span>{{i18n('请输入文本内容')}}…</span>
    </div>
  </div>
</template>
  
  <script lang="ts" src="./textFron.ts"></script>
  
  <style lang="scss" scoped>
.coupon {
  width: 100%;
  background: #f9f9f9;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  border: 2px solid transparent;
  position: relative;
  margin-bottom: 10px;
  .text {
    min-height: 156px;
    box-sizing: border-box;
    padding: 12px;
    white-space: pre-wrap;
    word-break: break-all;
    span {
      font-size: 14px;
      color: #a1a6ae;
      line-height: 20px;
    }
  }
}
</style>
  