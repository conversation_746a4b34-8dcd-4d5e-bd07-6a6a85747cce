import ApiClient from 'http/ApiClient'
import MemberPointsHst from 'model/report/query/points/memberpoints/MemberPointsHst'
import MemberPointsHstFilter from 'model/report/query/points/memberpoints/MemberPointsHstFilter'
import MemberPointsStats from 'model/report/query/points/memberpoints/MemberPointsStats'
import Response from 'model/common/Response'

export default class MemberPointsHstApi {
  /**
   * 会员积分流水分页查询
   * 会员积分流水分页查询
   *
   */
  static query(body: MemberPointsHstFilter): Promise<Response<MemberPointsHst[]>> {
    return ApiClient.server().post(`/v1/points/member-hst/query`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 会员积分汇总
   * 会员积分汇总
   *
   */
  static stats(memberId: string): Promise<Response<MemberPointsStats>> {
    return ApiClient.server().get(`/v1/points/member-hst/stats/${memberId}`, {}).then((res) => {
      return res.data
    })
  }

}
