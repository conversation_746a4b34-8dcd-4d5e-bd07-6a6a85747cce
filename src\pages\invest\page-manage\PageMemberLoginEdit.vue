<!--
 * @Author: 黎钰龙
 * @Date: 2025-05-23 14:31:12
 * @LastEditTime: 2025-07-17 14:42:52
 * @LastEditors: shikailei
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\PageMemberLoginEdit.vue
 * 记得注释
-->
<template>
  <div class="page-member-code-edit">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button size="large" type="primary" v-if="hasOptionPermission('/设置/小程序装修/页面管理', '编辑') && hasOptionPermission('/设置/小程序装修/页面管理', '发布')"
          @click="preserve(true)">
          {{ i18n('保存并发布') }}
        </el-button>
        <el-button size="large" @click="preserve(false)">{{ i18n('/公用/按钮/保存') }}</el-button>
        <el-button size="large" @click="goBack">{{ i18n('取消') }}</el-button>
      </template>
    </BreadCrume>
    <!-- 设置 -->
    <div class="panel">
      <div class="panel-left">
        <el-form :model="ruleForm" :rules="rules" ref="form" label-width="140px">
          <!-- 页面设置 -->
          <div class="backDecoration">
            <div class="title">{{ i18n('页面设置') }}</div>
            <div class="content">
              <el-form-item :label="i18n('顶部标题形式')">
                <el-radio-group v-model="ruleForm.topTitleStyle">
                  <el-radio label="text">{{i18n("文字")}}</el-radio>
                  <el-radio label="image">{{i18n("图片")}}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item :label="i18n('顶部标题内容')" v-if="ruleForm.topTitleStyle === 'text'" prop="topTitle">
                <el-input v-model="ruleForm.topTitle" maxlength="20" style="width: 300px" placeholder="请输入标题内容" />
              </el-form-item>
              <el-form-item :label="i18n('顶部图片')" v-if="ruleForm.topTitleStyle === 'image'">
                <UploadImg v-model="ruleForm.topImage" @change="uploadImgChange('topImage')" />
              </el-form-item>
              <el-form-item :label="i18n('登录方式')" prop="loginTypes">
                <el-checkbox-group v-model="ruleForm.loginTypes">
                  <el-checkbox label="mobile">{{i18n('手机号')}}</el-checkbox>
                  <el-checkbox label="email">{{i18n('邮箱')}}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item label="按钮背景色" prop="buttonBgColor">
                <el-color-picker style="width: 100px" v-model="ruleForm.buttonBgColor"
                  size="small"></el-color-picker>
              </el-form-item>
              <el-form-item label="按钮字体色" prop="buttonFontColor">
                <el-color-picker style="width: 100px" v-model="ruleForm.buttonFontColor"
                  size="small"></el-color-picker>
              </el-form-item>
              <el-form-item label="按钮文字" prop="buttonText">
                <el-input v-model="ruleForm.buttonText" maxlength="10" style="width: 200px" placeholder="请输入按钮文字" />
              </el-form-item>
              <el-form-item label="选中状态颜色" prop="activeColor">
                <el-color-picker style="width: 100px" v-model="ruleForm.activeColor"
                  size="small"></el-color-picker>
              </el-form-item>
              <el-form-item label="是否显示国家选择">
                <el-radio-group v-model="ruleForm.showCountrySelector">
                  <el-radio :label="true">{{i18n("显示")}}</el-radio>
                  <el-radio :label="false">{{i18n("隐藏")}}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="说明内容">
                <div style="padding-left:20px">
                  <RichText v-model="dynamicValidateForm.agreement"></RichText>
                </div>
              </el-form-item>
            </div>
          </div>
        </el-form>
      </div>
      <div class="panel-right">
        <div class="pageBox page-place">
          <!-- 顶部标题区域 -->
          <div class="header-bar">
            <div class="back-btn">←</div>
            <div class="header-title">
              <div v-if="ruleForm.topTitleStyle === 'text'">{{ ruleForm.topTitle || '登录/注册' }}</div>
              <img v-else-if="ruleForm.topTitleStyle === 'image' && ruleForm.topImage" :src="ruleForm.topImage" class="header-image" />
              <div v-else>登录/注册</div>
            </div>
            <div class="header-right"></div>
          </div>

          <!-- 登录表单区域 -->
          <div class="login-form-container">
            <!-- 登录方式切换 -->
            <div class="login-type-tabs" v-if="ruleForm.loginTypes && ruleForm.loginTypes.length > 1">
              <div
                class="tab-item"
                :class="{ active: currentLoginType === 'mobile' }"
                :style="currentLoginType === 'mobile' ? { color: ruleForm.activeColor } : {}"
                v-if="ruleForm.loginTypes.includes('mobile')"
                @click="currentLoginType = 'mobile'"
              >
                手机号
              </div>
              <div
                class="tab-item"
                :class="{ active: currentLoginType === 'email' }"
                :style="currentLoginType === 'email' ? { color: ruleForm.activeColor } : {}"
                v-if="ruleForm.loginTypes.includes('email')"
                @click="currentLoginType = 'email'"
              >
                邮箱
              </div>
            </div>

            <!-- 输入框区域 -->
            <div class="input-group">
              <!-- 国家/地区选择 (仅手机号登录且开启时显示) -->
              <div class="country-selector" v-if="currentLoginType === 'mobile' && ruleForm.showCountrySelector">
                <select class="country-select" :style="{ borderColor: ruleForm.activeColor + '33' }">
                  <option value="+886">+886 Taiwan, China</option>
                  <option value="+86">+86 中国大陆</option>
                  <option value="+852">+852 香港</option>
                  <option value="+853">+853 澳门</option>
                  <option value="+1">+1 美国</option>
                </select>
              </div>

              <div class="input-item">
                <input
                  type="text"
                  :placeholder="currentLoginType === 'mobile' ? '请输入手机号码' : '请输入邮箱地址'"
                  class="login-input"
                  :style="{ '--focus-color': ruleForm.activeColor }"
                />
              </div>
            </div>

            <!-- 继续按钮 -->
            <button
              class="login-btn"
              :style="{
                backgroundColor: ruleForm.buttonBgColor,
                color: ruleForm.buttonFontColor
              }"
            >
              {{ ruleForm.buttonText || '继续' }}
            </button>

            <!-- 说明内容 -->
            <div class="description" v-if="dynamicValidateForm.agreement" v-html="dynamicValidateForm.agreement"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./PageMemberLoginEdit.ts">
</script>

<style lang="scss" scoped>
.page-member-code-edit {
  width: 100%;
  .panel {
    border-radius: 8px;
    padding: 24px;
    display: flex;

    .panel-left {
      width: 850px;
      margin-right: 12px;
      .backDecoration {
        padding: 26px 20px 40px;
        margin-bottom: 12px;
        border-radius: 8px;
        background: #ffffff;
      }
    }

    .title {
      font-weight: 600;
      font-size: 16px;
      color: #242633;
      line-height: 24px;
    }

    .content {
      margin-left: 8px;
      margin-top: 6px;
    }

    .panel-right {
      flex-shrink: 0;
      position: sticky;
      top: 0;
      /* 吸附到页面顶部 */
      border-radius: 8px;
      background: #ffffff;
      padding: 30px 25px;
      width: 400px;
      height: 764px;
      box-sizing: border-box;

      .pageBox {
        width: 100%;
        height: 100%;
        background: #f0f2f6;
        border: 8px solid #000000;
        border-radius: 50px;
        box-sizing: border-box;
        overflow: hidden;
        overflow: auto;

        &::-webkit-scrollbar {
          display: none;
          /* 隐藏滚动条 */
        }
      }

      .page-place {
        position: relative;
        .bg-img {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          width: 100%;
          height: 100%;
          object-fit: cover;
          z-index: 1;
        }
        .header-bar {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 20px;
          height: 74px;
          z-index: 2;
          font-size: 18px;
          font-weight: 500;
          color: #262626;

          .back-btn {
            font-size: 20px;
            cursor: pointer;
            width: 30px;
          }

          .header-title {
            flex: 1;
            text-align: center;
            font-size: 18px;
            font-weight: 600;
          }

          .header-right {
            width: 30px;
          }
        }
        .header-image {
          max-width: 200px;
          max-height: 40px;
          object-fit: contain;
        }

        .login-form-container {
          position: relative;
          width: 320px;
          margin: 20px auto 0;
          padding: 40px 30px 30px;
          background-color: #ffffff;
          border-radius: 20px;
          z-index: 2;

          .login-type-tabs {
            display: flex;
            margin-bottom: 40px;
            background-color: #f8f8f8;
            border-radius: 8px;
            padding: 4px;

            .tab-item {
              flex: 1;
              text-align: center;
              padding: 12px 0;
              font-size: 16px;
              color: #666;
              cursor: pointer;
              position: relative;
              border-radius: 6px;
              transition: all 0.3s ease;

              &.active {
                font-weight: 600;
                background-color: #ffffff;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
              }
            }
          }

          .input-group {
            margin-bottom: 40px;

            .country-selector {
              margin-bottom: 20px;

              .country-select {
                width: 100%;
                height: 48px;
                padding: 0 15px;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                font-size: 16px;
                outline: none;
                box-sizing: border-box;
                background-color: #ffffff;
                color: #333;

                &:focus {
                  border-color: #ff4444;
                }
              }
            }

            .input-item {
              position: relative;
              margin-bottom: 20px;

              .login-input {
                width: 100%;
                height: 48px;
                padding: 0 15px;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                font-size: 16px;
                outline: none;
                box-sizing: border-box;

                &:focus {
                  border-color: var(--focus-color, #ff4444);
                }
              }

            }
          }

          .login-btn {
            width: 100%;
            height: 48px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-bottom: 30px;
            transition: all 0.3s ease;

            &:hover {
              opacity: 0.9;
              transform: translateY(-1px);
            }

            &:active {
              transform: translateY(0);
            }
          }

          .description {
            font-size: 12px;
            color: #999;
            line-height: 1.5;
            text-align: center;
          }
        }

      }
    }
  }
}
</style>