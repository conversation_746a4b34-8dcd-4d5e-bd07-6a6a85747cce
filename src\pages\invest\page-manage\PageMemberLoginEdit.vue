<!--
 * @Author: 黎钰龙
 * @Date: 2025-05-23 14:31:12
 * @LastEditTime: 2025-07-17 14:27:17
 * @LastEditors: shikailei
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\PageMemberLoginEdit.vue
 * 记得注释
-->
<template>
  <div class="page-member-code-edit">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button size="large" type="primary" v-if="hasOptionPermission('/设置/小程序装修/页面管理', '编辑') && hasOptionPermission('/设置/小程序装修/页面管理', '发布')"
          @click="preserve(true)">
          {{ i18n('保存并发布') }}
        </el-button>
        <el-button size="large" @click="preserve(false)">{{ i18n('/公用/按钮/保存') }}</el-button>
        <el-button size="large" @click="goBack">{{ i18n('取消') }}</el-button>
      </template>
    </BreadCrume>
    <!-- 设置 -->
    <div class="panel">
      <div class="panel-left">
        <el-form :model="ruleForm" :rules="rules" ref="form" label-width="140px">
          <!-- 页面设置 -->
          <div class="backDecoration">
            <div class="title">{{ i18n('页面设置') }}</div>
            <div class="content">
              <el-form-item :label="i18n('顶部标题形式')">
                <el-radio-group v-model="ruleForm.propShowCountDown">
                  <el-radio :label="false">{{i18n("文字")}}</el-radio>
                  <el-radio :label="true">{{i18n("图片")}}</el-radio>
                </el-radio-group>
                <el-input v-model="ruleForm.name" maxlength="8" style="width: 300px" />
              </el-form-item>
              <el-form-item :label="i18n('登录方式')">
                <el-checkbox-group v-model="ruleForm.propOtherPayTypes">
                  <el-checkbox label="point">{{i18n('手机号')}}</el-checkbox>
                  <el-checkbox label="balance">{{i18n('邮箱')}}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item label="按钮" prop="propUnifiedCardBackground.fontColor">
                <el-color-picker style="width: 100px" v-model="imgData.propUnifiedCardBackground.fontColor"
                  size="small"></el-color-picker>
              </el-form-item>
              <el-form-item label="登录方式选中颜色" prop="propUnifiedCardBackground.fontColor">
                <el-color-picker style="width: 100px" v-model="imgData.propUnifiedCardBackground.fontColor"
                  size="small"></el-color-picker>
              </el-form-item>
              <el-form-item label="文字颜色" prop="propUnifiedCardBackground.fontColor">
                <el-color-picker style="width: 100px" v-model="imgData.propUnifiedCardBackground.fontColor"
                  size="small"></el-color-picker>
              </el-form-item>
              <el-form-item label="发送验证码提示" prop="propUnifiedCardBackground.fontColor">
                文字颜色<el-color-picker style="width: 100px" v-model="imgData.propUnifiedCardBackground.fontColor"
                  size="small"></el-color-picker>
              </el-form-item>
              <el-form-item label="说明内容">
                <div style="padding-left:20px">
                  <RichText v-model="dynamicValidateForm.agreement"></RichText>
                </div>
              </el-form-item>
            </div>
          </div>
        </el-form>
      </div>
      <div class="panel-right">
        <div class="pageBox page-place">
          <img class="bg-img" mode="aspectFill" v-if="ruleForm.propBackgroundImage" :src="ruleForm.propBackgroundImage" />
          <div class="header-bar">
            <div>{{ ruleForm.name }}</div>
          </div>
          <div class="member-code-box">
            <div class="member-avatar">
              <img v-if="ruleForm.propLogo" :src="ruleForm.propLogo" />
            </div>
            <div class="code-str">{{i18n('我的会员号')}}：886101335244107552</div>
            <div class="bar-qr-code">
              <div class="image">
                <img src="@/assets/image/invest/img_barcode.png" alt="">
              </div>
              <div class="img_qrcode">
                <img src="@/assets/image/invest/img_qrcode.png" alt="">
              </div>
              <div v-if="ruleForm.propShowCountDown">
                <i class="el-icon-refresh-right" /> n {{ruleForm.propCountDownText}}
              </div>
            </div>

          </div>
          <div class="other-header" v-if="ruleForm.propOtherPayTypes && ruleForm.propOtherPayTypes.length > 0">{{i18n('使用其他支付方式')}}</div>
          <div class="other-pay-box">
            <div class="other-pay-item" v-if="ruleForm.propOtherPayTypes && ruleForm.propOtherPayTypes.includes('balance')">
              <img src="@/assets/image/icons/ic_weixinzhifu.png" alt="">
              <div class="text">{{i18n('/会员/付费会员/储值支付')}}</div>
            </div>
            <div class="other-pay-item" v-if="ruleForm.propOtherPayTypes && ruleForm.propOtherPayTypes.includes('point')">
              <img src="@/assets/image/icons/ic_jifenzhifu.png" alt="">
              <div class="text">{{i18n('/会员/付费会员/积分支付')}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./PageMemberLoginEdit.ts">
</script>

<style lang="scss" scoped>
.page-member-code-edit {
  width: 100%;
  .panel {
    border-radius: 8px;
    padding: 24px;
    display: flex;

    .panel-left {
      width: 850px;
      margin-right: 12px;
      .backDecoration {
        padding: 26px 20px 40px;
        margin-bottom: 12px;
        border-radius: 8px;
        background: #ffffff;
      }
    }

    .title {
      font-weight: 600;
      font-size: 16px;
      color: #242633;
      line-height: 24px;
    }

    .content {
      margin-left: 8px;
      margin-top: 6px;
    }

    .panel-right {
      flex-shrink: 0;
      position: sticky;
      top: 0;
      /* 吸附到页面顶部 */
      border-radius: 8px;
      background: #ffffff;
      padding: 30px 25px;
      width: 400px;
      height: 764px;
      box-sizing: border-box;

      .pageBox {
        width: 100%;
        height: 100%;
        background: #f0f2f6;
        border: 8px solid #000000;
        border-radius: 50px;
        box-sizing: border-box;
        overflow: hidden;
        overflow: auto;

        &::-webkit-scrollbar {
          display: none;
          /* 隐藏滚动条 */
        }
      }

      .page-place {
        position: relative;
        .bg-img {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          width: 100%;
          height: 100%;
          object-fit: cover;
          z-index: 1;
        }
        .header-bar {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 20px;
          height: 74px;
          z-index: 2;
          font-size: 20px;
          font-weight: 500;
          color: #262626;
        }
        .member-code-box {
          position: relative;
          width: 320px;
          height: 400px;
          z-index: 2;
          margin: 30px auto 0;
          background-color: #ffffff;
          border-radius: 20px;
          &::before {
            content: "";
            position: absolute;
            top: -40px;
            left: 50%;
            width: 80px;
            height: 80px;
            transform: translateX(-50%);
            background-color: #ffffff;
            border-radius: 50%;
          }
          .member-avatar {
            position: relative;
            top: -35px;
            left: 50%;
            width: 70px;
            height: 70px;
            transform: translateX(-50%);
            border-radius: 50%;
            overflow: hidden;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .code-str {
            width: 100%;
            text-align: center;
            font-size: 13px;
            color: #666;
            margin-top: -20px;
          }
          .bar-qr-code {
            width: 100%;
            padding: 16px;
            padding-bottom: 5px;
            box-sizing: border-box;
            background: #ffffff;
            border-radius: 16px;
            text-align: center;
            .image {
              width: 100%;
              margin-bottom: 5px;
              img {
                width: 100%;
              }
            }
            .img_qrcode {
              img {
                width: 192px;
              }
            }
          }
        }
        .other-header {
          position: relative;
          width: 120px;
          height: 45px;
          font-size: 13px;
          color: #353535;
          line-height: 45px;
          text-align: center;
          margin: 0 auto;
          position: relative;
          z-index: 2;
          &::before {
            content: "";
            width: 52px;
            height: 1px;
            background: #353535;
            opacity: 0.5;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            right: -52px;
          }
          &::after {
            content: "";
            width: 52px;
            height: 1px;
            background: #353535;
            opacity: 0.5;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            left: -52px;
          }
        }
        .other-pay-box {
          position: relative;
          display: flex;
          justify-content: center;
          width: 100%;
          box-sizing: border-box;
          z-index: 2;
          .other-pay-item {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 10px;
            height: 30px;
            background-color: #ffffff;
            border-radius: 6px;
            margin: 0 4px;
            img {
              width: 16px;
              height: 16px;
              margin-right: 4px;
            }
          }
        }
      }
    }
  }
}
</style>