<!--
 * @Author: haiding <EMAIL>
 * @Date: 2025-02-19 10:03:52
 * @LastEditors: 黎钰龙
 * @LastEditTime: 2025-04-28 10:19:38
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\cmp\page-place-components\coupon\coupon.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="coupon"
    :style="{ padding: localProperty.styMarginTop + 'px ' + localProperty.styMarginRight + 'px ' + localProperty.styMarginBottom + 'px ' + localProperty.styMarginLeft + 'px' }"
    @click="activeTemplate">
    <div class="toolBar" v-if="activeIndex === index">
      <setting-toolbar :btns="toolBarBtns" @click="toolbarClick"></setting-toolbar>
    </div>
    <!-- <div class="page-top-bg" :style="{ backgroundImage: `url(${bgUrl})` }">
      <p class="page-top-title">{{ localProperty.placeTitle }}</p>
    </div> -->
    <div class="list">
      <div class="item" v-if="localProperty.propLayoutStyle === '1' || !localProperty.propLayoutStyle">
        <div class="item-left">
          <img src="@/assets/image/fellow/img_quan.png" alt="" />
          <div class="activity">
            <div class="name ellipsis" :title="`${i18n('活动名称')}｜${i18n('活动活动活动名称')}`">{{ i18n('活动名称') }}｜{{
              i18n('活动活动活动名称') }}</div>
            <div class="num">{{ i18n('共11张') }}</div>
          </div>
        </div>
        <div class="item-right">
          <div class="money">
            <span>¥</span>
            50
          </div>
          <div class="text">
            <span>{{ i18n('抢购') }}</span>
            <!-- <img src="@/assets/image/fellow/ic_go.png" alt="" /> -->
          </div>
        </div>
      </div>
    </div>
    <div v-if="localProperty.propLayoutStyle === '2'">
      <img class="coupon-img" mode="widthFix" src="@/assets/image/fellow/coupon_style_line_2.png" alt="" />
    </div>
    <div v-if="localProperty.propLayoutStyle === '3'">
      <img class="coupon-img" mode="widthFix" src="@/assets/image/fellow/coupon_style_line_three.png" alt="" />
    </div>
  </div>
</template>

<script lang="ts" src="./coupon.ts"></script>

<style lang="scss" scoped>
.coupon {
  width: 100%;
  background: #f9f9f9;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  border: 2px solid transparent;
  position: relative;
  margin-bottom: 10px;

  &-bg {
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-size: 100%;
  }

  &-title {
    text-align: center;
    padding-top: 42px;
    font-size: 20px;
    color: #fff;
  }

  .toolBar {
    position: absolute;
    top: 0;
    right: 1px;
    z-index: 999;
    cursor: pointer;
  }

  .list {
    .item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 350px;
      height: 102px;
      background: url("@/assets/image/fellow/img_quanbao_bg.png");
      background-size: 100% 100%;
      margin: 0 auto 10px;

      .item-left {
        display: flex;
        align-items: center;
        margin-top: 6px;

        img {
          width: 120px;
          height: 90px;
          background: #ffffff;
          border-radius: 6px;
        }

        .activity {
          width: 105px;
          margin-left: 12px;

          .name {
            height: 44px;
            font-weight: 500;
            font-size: 15px;
            color: #111111;
            line-height: 21px;
            overflow: hidden;
            white-space: nowrap;
          }

          .ellipsis {
            display: inline-block;
            max-width: 100%;
            text-overflow: ellipsis;
            overflow: hidden;
            vertical-align: middle;
          }

          .num {
            height: 20px;
            font-weight: 500;
            font-size: 14px;
            color: #ff3b3d;
            line-height: 20px;
            margin-top: 14px;
          }
        }
      }

      .item-right {
        margin-right: 20px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .money {
          font-weight: 600;
          font-size: 24px;
          color: #ff3b3d;
          line-height: 26px;

          span {
            font-weight: 600;
            font-size: 14px;
            color: #ff3b3d;
            line-height: 20px;
          }
        }

        .text {
          font-weight: 500;
          font-size: 16px;
          color: #ff3b3d;
          line-height: 22px;
          display: flex;
          align-content: center;

          img {
            width: 22px;
            height: 22px;
            margin-left: 4px;
          }
        }
      }
    }
  }
  .coupon-img {
    width: 100%;
  }
}
</style>
