import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import FormDetail from 'components/basic/form-details/FormDetail.vue';
import emitter from 'util/emitter';
import { FormMode } from 'model/local/FormMode';
import FormDefUtil from 'util/FormDefUtil';
import ObjectUtil from 'util/ObjectUtil';
import TargetPage from 'model/local/TargetPage';
import UploadImg from '@/cmp/upload-img/UploadImg.vue';
import draggable from 'vuedraggable';
import I18nPage from 'common/I18nDecorator';

@Component({
  name: 'NavigationSettings',
  mixins: [emitter],
  components: { UploadImg, draggable },
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理',
    '/页面/导航设置',
  ],
  auto: true
})
export default class NavigationSettings extends Vue {
  @Prop({ type: Boolean, default: false })
  readonly: boolean; //
  @Prop({ type: String, default: 'NavigationSettings' })
  validateName: string; // 组件校验名称，editPage通过该名称进行组件校验及校验信息清除
  @Prop({ type: Object })
  value: any; // 数据模型
  @Prop({ type: String, default: '导航设置' })
  label: string; // label名
  @Prop({ type: String })
  prefixDescribe: string; // 前置描述
  @Prop({ type: String })
  suffixDescribe: string; // 后置描述

  @Prop({ type: Boolean, default: true })
  required: boolean; // 是否必填
  @Prop({ type: String })
  relativeValidateName: string; // 联动校验名称
  @Prop()
  formKey: any;
  @Prop()
  activityWidgets: any;
  @Prop()
  renderTemplateList: any

  FormModeType = FormMode;
  $refs: any;
  labelPosition: string = 'left';
  optionsOut: string[] = [this.i18n('活动页面')];
  imgFormat: any = ['gif', 'jpg', 'jpeg', 'png'];

  form: any = {
    propNavigationType: 'inside',
    insideData: [
      {
        name: '',
        componentUuid: '',
        id: ObjectUtil.s4(),
        navigationStyle: 'text',
        image: '',
        icon: '',
      },
      {
        name: '',
        componentUuid: '',
        id: ObjectUtil.s4(),
        navigationStyle: 'text',
        image: '',
        icon: '',
      },
    ],
    outsideData: [
      {
        name: '',
        targetPage: new TargetPage(),
        id: ObjectUtil.s4(),
        navigationStyle: 'text',
        image: '',
        icon: '',
      },
      {
        name: '',
        targetPage: new TargetPage(),
        id: ObjectUtil.s4(),
        navigationStyle: 'text',
        image: '',
        icon: '',
      },
    ],
  };

  handleValidate() { }

  get credential() {
    return this.$store.state.credential;
  }

  get options() {
    return [
      {
        id: 'styNavigationBgColor',
        label: this.i18n('背景色'),
      },
      {
        id: 'styColor',
        label: this.i18n('正常字体色'),
      },
      {
        id: 'styActiveColor',
        label: this.i18n('选中字体色'),
      },
      {
        id: 'styActiveUnderlineColor',
        label: this.i18n('选中下划线色'),
      },
    ];
  }

  @Watch('value', { immediate: true, deep: true })
  handleValue(value) {
    const valueCopy = ObjectUtil.clone(value);
    if (!valueCopy.propNavigationList) {
      return;
    }
    if (valueCopy.propNavigationList.length === 0) {
      return;
    }
    if (valueCopy.propNavigationType === 'inside') {
      const form = {
        ...valueCopy,
        propNavigationType: valueCopy.propNavigationType,
        insideData: valueCopy.propNavigationList,
      };
      this.form = form;
    } else {
      const form = {
        ...this.value,
        propNavigationType: this.value.propNavigationType,
        outsideData: null,
      };
      const outsideData: any = [];
      valueCopy.propNavigationList.forEach((res) => {
        const resClone = ObjectUtil.clone(res);
        delete res.targetPage;
        delete res.pageParams;
        if (!res.id) {
          res.id = ObjectUtil.s4();
        }
        const targetPage = new TargetPage();
        targetPage.targetPage = this.i18n('活动页面');
        targetPage.pageParams = resClone.pageParams;
        const obj: any = {
          ...res,
          targetPage,
        };
        outsideData.push(obj);
      });
      form.outsideData = outsideData;
      this.form = form;
    }
    this.$nextTick(() => {
      this.validate(() => { });
    });
  }

  rules = {
    propNavigationType: [{ required: true, message: this.i18n('请选择'), trigger: ['blur', 'change'] }],

    navigationItem: [
      {
        required: true,
        trigger: ['blur', 'change'],
        validator: (rule, value, callback) => {
          if (value.navigationStyle === 'text') {
            if (!value.name) {
              return callback(new Error(this.i18n('请输入')));
            }
            if (!this.value.propNavigationList) return;
            const result = this.value.propNavigationList.filter((res) => res.name === value.name);
            if (result.length > 1) {
              return callback(new Error(this.i18n('导航名称不能重复!')));
            }
            return callback();
          } else {
            if (!value.image) {
              return callback(new Error(this.i18n('请上传图片')));
            }
            return callback();
          }
        },
      },
    ],
  };

  newArrFn(arr) {
    // .new Set方法，返回是一个类数组，需要结合 ...运算符，转成真实数组
    return [...new Set(arr)];
  }

  get formMode() {
    if (this.validateName === 'propMarginBottom') {
      return FormDefUtil.getFormMode(this.readonly, this.validateName, this.formKey);
    } else {
      return FormDefUtil.getFormMode(
        this.readonly,
        this.validateName + 'propMarginBottom',
        this.formKey
      );
    }
  }

  handleChangeSelect() {
    if (this.form.propNavigationType === 'inside') {
      // 重置菜单项
      this.form.insideData = [
        {
          name: '',
          componentUuid: '',
          id: ObjectUtil.s4(),
          navigationStyle: 'text',
          image: '',
          icon: '',
        },
        {
          name: '',
          componentUuid: '',
          id: ObjectUtil.s4(),
          navigationStyle: 'text',
          image: '',
          icon: '',
        },
      ];
    } else {
      const outsideData = [
        {
          name: '',
          targetPage: new TargetPage(),
          id: ObjectUtil.s4(),
          navigationStyle: 'text',
          image: '',
          icon: '',
        },
        {
          name: '',
          targetPage: new TargetPage(),
          id: ObjectUtil.s4(),
          navigationStyle: 'text',
          image: '',
          icon: '',
        },
      ];
      this.$set(this.form, 'outsideData', outsideData);
    }
    this.handleChange();
  }

  handleChange() {
    // this.$bus.emit('cms.edit.addForm', this);
    if (this.form.propNavigationType === 'inside') {
      const value = {
        ...this.value,
        propNavigationType: this.form.propNavigationType,
        propNavigationList: this.form.insideData,
      };
      this.$emit('input', value);
      this.$emit('change', value);
    } else {
      const value = {
        ...this.value,
        propNavigationType: this.form.propNavigationType,
        propNavigationList: null,
      };
      const outsideData: any = [];
      this.form.outsideData.forEach((res) => {
        const obj: any = {
          ...res,
          targetPage: res.targetPage.targetPage,
          pageParams: res.targetPage.pageParams,
        };
        outsideData.push(obj);
      });
      value.propNavigationList = outsideData;
      this.$emit('input', value);
      this.$emit('change', value);
    }
    this.$nextTick(() => {
      this.validate(() => { });
    });
  }

  // 监听页面内定位
  handleAddInside() {
    this.form.insideData.push({
      name: '',
      componentUuid: '',
      id: ObjectUtil.s4(),
      navigationStyle: 'text',
      image: '',
    });
    this.handleChange();
  }

  // 删除
  deleteInsideData(index, data) {
    data.splice(index, 1);
    this.handleChange();
  }
  // 删除
  deleteOutsideData(index, data) {
    data.splice(index, 1);
    this.handleChange();
  }
  // 移动触发
  handleDragEnd(data: any) {
    this.value.propNavigationList = data
    this.handleChange();
  }
  // 上移
  upInsideData(index: any, data: any) {
    const temp = data[index];
    data.splice(index, 1, data[index - 1]);
    data.splice(index - 1, 1, temp);

    this.value.propNavigationList = data
    this.handleChange();
  }
  // 下移
  downInsideData(index: any, data: any) {
    const temp = data[index];
    data.splice(index, 1, data[index + 1]);
    data.splice(index + 1, 1, temp);

    this.value.propNavigationList = data
    this.handleChange();
  }
  // 监听下啦
  /**
   *
   * @param val 选中id
   * @param item 选中item
   * @param index 选中index
   */

  handleSelectChange(val, item, index) {
    this.form.insideData.forEach((item, index1) => {
      if (index !== index1) {
        if (item.componentUuid === val) {
          item.componentUuid = '';
        }
      }
    });
    this.handleChange();
  }
  // 监听页面切换
  handleAddOutside() {
    this.form.outsideData.push({
      name: '',
      targetPage: new TargetPage(),
      id: ObjectUtil.s4(),
      navigationStyle: 'text',
    });
    this.handleChange();
  }

  mounted() {
    console.log(this.renderTemplateList, 'ssssssssssssssssssssssssssssssssssssss');

    // this.$bus.emit('cms.edit.addForm', this);
    if (this.form.propNavigationType === 'outside') return;
    this.form.insideData.forEach((f) => {
      const newItem = this.renderTemplateList.find((v) => {
        return f.componentUuid === v.uuid;
      });
      if (!newItem) {
        f.componentUuid = '';
      }
    });
  }

  validate(callback) {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.validate(callback);
    }
  }

  clearValidate() {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.clearValidate();
    }
  }
}
