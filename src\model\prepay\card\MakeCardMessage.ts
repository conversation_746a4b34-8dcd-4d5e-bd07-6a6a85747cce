/*
 * @Author: 黎钰龙
 * @Date: 2023-10-13 14:44:11
 * @LastEditTime: 2023-10-13 14:45:23
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\prepay\card\MakeCardMessage.ts
 * 记得注释
 */
export default class MakeCardMessage {
  // 制卡单号
  billNumber: Nullable<string> = null
  // 起始卡号
  startCardCode: Nullable<string> = null
  // 结束卡号
  endCardCode: Nullable<string> = null
  // 制卡数量
  madeCount: Nullable<number> = null
  // 制卡失败数量
  madeFailCount: Nullable<number> = null
  // 操作人
  operator: Nullable<string> = null
  // 操作
  operate: Nullable<string> = null
  // 制卡时间
  makeTime: Nullable<Date> = null
}