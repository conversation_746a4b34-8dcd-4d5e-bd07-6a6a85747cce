/*
 * @Author: 黎钰龙
 * @Date: 2025-01-06 20:02:16
 * @LastEditTime: 2025-05-23 10:15:08
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\template\PopularizeResponse.ts
 * 记得注释
 */
import { CmsConfigChannel } from "./CmsConfig"
import QrCodeResponse from "./QrCodeResponse"
import ShortLinkResponse from "./ShortLinkResponse"
import UrlSchemeResponse from "./UrlSchemeResponse"

// 页面推广
export default class PopularizeResponse {
  // 小程序路径
  appletPath: Nullable<string> = null
  // 白底小程序码
  qrCodeResponse: Nullable<QrCodeResponse> = null
  // 小程序短链接
  shortLinkResponse: Nullable<ShortLinkResponse> = null
  // 小程序URLScheme
  urlSchemeResponse: Nullable<UrlSchemeResponse> = null
  // 推广渠道
  channel: Nullable<CmsConfigChannel> = null
}