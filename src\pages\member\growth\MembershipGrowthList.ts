import AbstractUnionList from 'cmp/abstract-union-list/AbstractUnionList';
import ActivityStateTag from 'cmp/activity-state-tag/ActivityStateTag';
import FormItem from 'cmp/formitem/FormItem';
import ListWrapper from 'cmp/list/ListWrapper';
import MyQueryCmp from 'cmp/querycondition/MyQueryCmp';
import I18nPage from 'common/I18nDecorator';
import UpgradeGiftActivityApi from 'http/grade/upgradeGift/UpgradeGiftActivityApi';
import UnionActivityApi from 'http/promotion/UnionActivityApi';
import CouponActivityApi from 'http/v2/coupon/CouponActivityApi';
import ActivityMgr from 'mgr/ActivityMgr';
import ConstantMgr from 'mgr/ConstantMgr';
import ActivityBody from 'model/common/ActivityBody';
import { MarketBudgetActivityEnum } from 'model/promotion/MarketBudgetActivityEnum';
import UnionActivityQuery from 'model/promotion/UnionActivityQuery';
import CommonUtil from 'util/CommonUtil';
import { Component, Vue } from 'vue-property-decorator';

class Query {
  nameLike: string = ''  //活动名称
  numberLike: string = '' //活动号
  topicNameLikes: string = ''  //所属主题
  stateEquals: Nullable<string> = null  //状态
  typeEquals: Nullable<string> = null //活动类型
}

@Component({
  name: 'MembershipGrowthList',
  components: {
    ListWrapper,
    MyQueryCmp,
    FormItem,
    ActivityStateTag
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板'
  ],
  auto: true
})
export default class MembershipGrowthList extends AbstractUnionList<ActivityBody> {
  query: Query = {
    nameLike: '', //活动名称
    numberLike: '', //活动号
    topicNameLikes: '', //所属主题
    stateEquals: null, //活动状态
    typeEquals: null  //活动类型
  }
  selectDate: any = []  //活动时间
  activeName = "first"; //状态筛选
  singleAll: boolean = false;  //是否全选
  tableData: ActivityBody[] = []
  loading: boolean = false

  //OA注册发大礼包
  get isOaActivity() {
    return ActivityMgr.isOaActivity(MarketBudgetActivityEnum.MemberRegisterGiftActivityRule)
  }

  created() {
    // 活动数据初始化
    this.initActivityInfo([
      // 邀请有礼
      {
        label: this.formatI18n('/营销/券礼包活动/券礼包活动/邀请有礼'),
        value: 'MemberInviteRegisterGiftActivityRule',
        activityType: 'MEMBER_INVITE_REGISTER_GIFT',
        viewAble: this.hasOptionPermission('/营销/营销/会员成长/邀请有礼', '活动查看'),
        modifyAble: this.hasOptionPermission('/营销/营销/会员成长/邀请有礼', '活动维护'),
        auditAble: this.hasOptionPermission('/营销/营销/会员成长/邀请有礼', '活动审核'),
        stopAble: this.hasOptionPermission('/营销/营销/会员成长/邀请有礼', '活动终止'),
        removeAble: this.hasOptionPermission('/营销/营销/会员成长/邀请有礼', '活动维护'),
        goToDtl: (row: any) => {
          this.$router.push({ name: 'invite-send-gift-dtl', query: { id: row.activityId } })
        },
        goToCopy: (row: any) => {
          this.$router.push({ name: 'invite-send-gift', query: { list: "true", id: row.activityId, from: "copy" } })
        },
        goToModify: (row: any) => {
          this.$router.push({ name: 'invite-send-gift', query: { list: "true", id: row.activityId, from: "edit" } })
        },
      },
      // 升级有礼
      {
        label: this.formatI18n('/营销/升级有礼/升级有礼'),
        value: 'UpgradeGiftActivityRule',
        activityType: 'UPGRADE_GIFT',
        viewAble: this.hasOptionPermission('/营销/营销/会员成长/升级有礼', '活动查看'),
        modifyAble: this.hasOptionPermission('/营销/营销/会员成长/升级有礼', '活动维护'),
        auditAble: this.hasOptionPermission('/营销/营销/会员成长/升级有礼', '活动审核'),
        stopAble: this.hasOptionPermission('/营销/营销/会员成长/升级有礼', '活动终止'),
        removeAble: this.hasOptionPermission('/营销/营销/会员成长/升级有礼', '活动维护'),
        goToDtl: (row: any) => {
          this.$router.push({ name: 'upgrade-gift-dtl', query: { id: row.activityId } })
        },
        goToCopy: (row: any) => {
          this.$router.push({ name: 'upgrade-gift-edit', query: { id: row.activityId, from: 'copy' } })
        },
        goToModify: (row: any) => {
          this.$router.push({ name: 'upgrade-gift-edit', query: { id: row.activityId, from: 'edit' } })
        },
      },
      // 注册发大礼包
      {
        label: this.formatI18n('/营销/券礼包活动/券礼包活动/注册发大礼包'),
        value: 'MemberRegisterGiftActivityRule',
        activityType: 'MEMBER_REGISTER_GIFT',
        viewAble: this.hasOptionPermission('/营销/营销/会员成长/注册发大礼包', '活动查看'),
        modifyAble: this.hasOptionPermission('/营销/营销/会员成长/注册发大礼包', '活动维护'),
        auditAble: this.hasOptionPermission('/营销/营销/会员成长/注册发大礼包', '活动审核') && !this.isOaActivity,
        stopAble: this.hasOptionPermission('/营销/营销/会员成长/注册发大礼包', '活动终止'),
        removeAble: this.hasOptionPermission('/营销/营销/会员成长/注册发大礼包', '活动维护'),
        goToDtl: (row: any) => {
          this.$router.push({ name: 'register-send-gift-dtl', query: { id: row.activityId } })
        },
        goToCopy: (row: any) => {
          this.$router.push({ name: 'register-send-gift', query: { list: "true", id: row.activityId, from: "copy" } })
        },
        goToModify: (row: any) => {
          this.$router.push({ name: 'register-send-gift', query: { list: "true", id: row.activityId, from: "edit" } })
        },
      },
      // 微信激活发大礼包
      {
        label: this.formatI18n('/营销/券礼包活动/券礼包活动/微信激活发大礼包'),
        value: 'WeixinActiveGiftActivityRule',
        activityType: 'WEIXIN_ACTIVATION_GIFT',
        viewAble: this.hasOptionPermission('/营销/营销/会员成长/微信激活发大礼包', '活动查看'),
        modifyAble: this.hasOptionPermission('/营销/营销/会员成长/微信激活发大礼包', '活动维护'),
        auditAble: this.hasOptionPermission('/营销/营销/会员成长/微信激活发大礼包', '活动审核'),
        stopAble: this.hasOptionPermission('/营销/营销/会员成长/微信激活发大礼包', '活动终止'),
        removeAble: this.hasOptionPermission('/营销/营销/会员成长/微信激活发大礼包', '活动维护'),
        goToDtl: (row: any) => {
          this.$router.push({ name: 'wx-active-send-gift-dtl', query: { id: row.activityId } })
        },
        goToCopy: (row: any) => {
          this.$router.push({ name: 'wx-active-send-gift', query: { list: "true", id: row.activityId, from: "copy" } })
        },
        goToModify: (row: any) => {
          this.$router.push({ name: 'wx-active-send-gift', query: { list: "true", id: row.activityId, from: "edit" } })
        },
      },
      // 完善资料有礼
      {
        label: this.formatI18n('/公用/菜单/完善资料有礼'),
        value: 'ImproveProfilesGiftRule',
        activityType: 'IMPROVE_PROFILES_GIFT',
        viewAble: this.hasOptionPermission('/营销/营销/会员成长/完善资料有礼', '活动查看'),
        modifyAble: this.hasOptionPermission('/营销/营销/会员成长/完善资料有礼', '活动维护'),
        auditAble: this.hasOptionPermission('/营销/营销/会员成长/完善资料有礼', '活动审核'),
        stopAble: this.hasOptionPermission('/营销/营销/会员成长/完善资料有礼', '活动终止'),
        removeAble: this.hasOptionPermission('/营销/营销/会员成长/完善资料有礼', '活动维护'),
        goToDtl: (row: any) => {
          this.$router.push({ name: 'improveProfiles-dtl', query: { id: row.activityId } })
        },
        goToCopy: (row: any) => {
          this.$router.push({ name: 'improveProfiles-add', query: { list: "true", id: row.activityId, from: "copy" } })
        },
        goToModify: (row: any) => {
          this.$router.push({ name: 'improveProfiles-add', query: { list: "true", id: row.activityId, from: "edit" } })
        },
      },
    ])
    this.queryList()
  }

  // 获取当前列表页活动类型
  getActivityTypes() {
    let arr: string[] = []
    this.activitiesInfo.forEach((item) => {
      if (item.viewAble) {
        arr.push(item.value)
      }
    })
    if (this.query.typeEquals) {
      arr = arr.filter((item) => item === this.query.typeEquals)
    }
    return arr
  }

  doSearch() {
    this.page.currentPage = 1;
    this.queryList();
  }

  doReset() {
    this.activeName = "first";
    this.selectDate = [];
    this.query = new Query();
    this.queryList()
  }

  doEffectEvaluate(row: any) {
    this.$router.push({ name: "effect-evaluate", query: { id: row.activityId } });
  }

  //全选
  checkedAllRow() {
    if (this.singleAll) {
      for (let row of this.tableData) {
        this.$refs.table.toggleRowSelection(row, true);
      }
    } else {
      this.$refs.table.clearSelection();
    }
  }

  doHandleClick() {
    this.page.currentPage = 1;
    this.query.stateEquals = this.computeStateEquals(this.activeName)
    this.queryList();
  }

  doAudit(row: ActivityBody) {
    this.$confirm(this.formatI18n("/公用/活动/提示信息/确认要审核吗？") as string, this.formatI18n('/公用/按钮/审核'), {
      confirmButtonText: this.formatI18n("/公用/按钮", "确定") as string,
      cancelButtonText: this.formatI18n("/公用/按钮", "取消") as string,
    }).then(() => {
      CouponActivityApi.audit(row.activityId!).then((res) => {
        if (res.code === 2000) {
          this.$message.success('操作成功')
          this.queryList()
        } else {
          throw new Error(res.msg || this.i18n('操作失败'))
        }
      }).catch((error) => this.$message.error(error.message))
    });
  }

  doStop(row: ActivityBody) {
    this.$confirm(this.formatI18n("/公用/活动/提示信息/确认要停止吗？") as string, this.formatI18n('/公用/按钮/终止'), {
      confirmButtonText: this.formatI18n("/公用/按钮", "确定") as string,
      cancelButtonText: this.formatI18n("/公用/按钮", "取消") as string,
    }).then(() => {
      CouponActivityApi.stop(row.activityId!).then((res) => {
        if (res.code === 2000) {
          this.$message.success('操作成功')
          this.queryList()
        } else {
          throw new Error(res.msg || this.i18n('操作失败'))
        }
      }).catch((error) => this.$message.error(error.message))
    });
  }

  doDelete(row: ActivityBody) {
    this.$confirm(this.formatI18n("/公用/活动/提示信息/确认要删除吗？") as string, this.formatI18n('/公用/券模板/删除'), {
      confirmButtonText: this.formatI18n("/公用/按钮", "确定") as string,
      cancelButtonText: this.formatI18n("/公用/按钮", "取消") as string,
    }).then(() => {
      CouponActivityApi.remove(row.activityId!).then((res) => {
        if (res.code === 2000) {
          this.$message.success('操作成功')
          this.queryList()
        } else {
          throw new Error(res.msg || this.i18n('操作失败'))
        }
      }).catch((error) => this.$message.error(error.message))
    });
  }

  //提交批量删除
  submitBatchDelete(ids: string[]) {
    UnionActivityApi.batchRemove(ids)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(resp.data);
          this.queryList();
          this.singleAll = false;
        }
      })
      .catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
  }

  //提交批量审核
  submitBatchAudit(ids: string[]) {
    UnionActivityApi.batchAudit(ids)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(resp.data);
          this.queryList();
          this.singleAll = false;
        }
      })
      .catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
  }

  //提交批量终止
  submitBatchEnd(ids: string[]) {
    UnionActivityApi.batchStop(ids)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(resp.data);
          this.queryList();
          this.singleAll = false;
        } else {
          this.$message.error(resp.msg);
        }
      })
      .catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
  }

  // 查询活动列表
  queryList() {
    const params = new UnionActivityQuery()
    params.topicNameLikes = this.query.topicNameLikes ? this.query.topicNameLikes : null;
    params.nameLike = this.query.nameLike ? this.query.nameLike : null
    params.numberLike = this.query.numberLike ? this.query.numberLike : null
    // 活动时间
    if (this.selectDate && this.selectDate.length > 0) {
      params.begin = this.selectDate[0];
      params.end = this.selectDate[1];
    } else {
      params.begin = null;
      params.end = null;
    }
    params.page = this.page.currentPage - 1;
    params.pageSize = this.page.size;
    params.stateEquals = this.query.stateEquals ? this.query.stateEquals : null
    params.typeIn = this.getActivityTypes()
    this.loading = true
    UnionActivityApi.queryByType(params).then((res) => {
      if (res.code === 2000) {
        this.tableData = res.data?.list || []
        this.page.total = res.total
        this.handleSumAmount(res.data!.summary!)
      } else {
        throw new Error(res.msg || this.i18n('查询失败'))
      }
    }).catch((error) => {
      this.$message.error(error.message)
    }).finally(() => {
      this.loading = false
    })
  }

  get dateRangeOption() {
    return {
      disabledDate(time: any) {
        return time.getTime() > new Date('2038-01-01 23:59:59').getTime();
      },
    };
  }
};