<!--
 * @Author: 黎钰龙
 * @Date: 2024-01-24 14:07:05
 * @LastEditTime: 2025-05-14 14:22:21
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\couponTemplateDtlItem\useCouponRecordMethod\UseCouponRecordMethod.vue
 * 记得注释
-->
<template>
  <div>
    <div style="height: 36px; line-height: 36px" v-if="data && data.coupons && data.coupons.useApporion.subjectApprotionType === 'FAV'">
      {{ i18n("优惠方式") }}
    </div>
    <div style="height: 36px; line-height: 36px" v-if="data && data.coupons && data.coupons.useApporion.subjectApprotionType === 'PAY'">
      {{ i18n("支付方式") }}
    </div>
    <div style="line-height: 36px" v-if="data && data.coupons && data.coupons.useApporion.subjectApprotionType === 'COLLOCATION'">
      {{ i18n("组合方式") }}{{':'}} <span v-html="getFavValue()"></span>
<!--      <el-button @click="viewSpecialGoods"-->
<!--        v-if="data.coupons && data.coupons.useApporion && data.coupons.useApporion.recordType === 'AMOUNT' && data.coupons.useApporion.specialGoodsAmounts && data.coupons.useApporion.specialGoodsAmounts.length"-->
<!--        type="text">-->
<!--        {{ i18n('查看特殊商品') }}-->
<!--      </el-button>-->
    </div>
    <!-- 查看特殊商品 -->
<!--    <SpecialGoodsDialog ref="SpecialGoodsDialog" :data="data.coupons && data.coupons.useApporion ? data.coupons.useApporion.specialGoodsAmounts : []">-->
<!--    </SpecialGoodsDialog>-->
  </div>
</template>

<script lang="ts" src="./UseCouponRecordMethod.ts">
</script>

<style>
</style>