/*
 * @Author: 黎钰龙
 * @Date: 2023-12-13 18:01:06
 * @LastEditTime: 2023-12-18 10:12:03
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\active-present-card\ActivePresentCardTable.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import RoutePermissionMgr from 'mgr/RoutePermissionMgr';
import GiftCardActivity from 'model/card/activity/GiftCardActivity';
import GiftCardSaleSpecs from 'model/card/activity/GiftCardSaleSpecs';
import SelectStoreActiveDtlDialog from 'pages/deposit/mbrdeposit/active/dialog/SelectStoreActiveDtlDialog';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  name: 'ActivePresentCardTable',
  components: {
    SelectStoreActiveDtlDialog
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/储值/预付卡/电子礼品卡活动/编辑页面'
  ],
  auto: true
})
export default class ActivePresentCardTable extends Vue {
  @Prop() tableData: GiftCardActivity;
  @Prop({ type: Boolean, default: true }) isShowGift: boolean; //是否展示购卡赠礼
  @Prop({ type: Boolean, default: true }) isShowLimit: boolean; //是否展示发售限制
  couponDialog = {
    dialogShow: false,
    parent: {},
    child: {}
  }

  // 跳转模板详情
  gotoTplDtl(item: any) {
    RoutePermissionMgr.openBlank({ name: 'prepay-card-tpl-dtl', query: { number: item.cardTemplateNumber } })
  }

  private doCheckDtl(parent: any, child: any) {
    this.couponDialog.parent = parent
    this.couponDialog.child = child
    this.couponDialog.dialogShow = true
  }

  private doDialogClose() {
    this.couponDialog.dialogShow = false
  }

  private saleLimitStr(row: GiftCardSaleSpecs) {
    if (row.totalPerMan && row.total) {
      let str: any = this.formatI18n(
        "/营销/券礼包活动/新建商品满额发券活动/详情界面/发券数限制",
        "每人限{0}张，活动总限{1}张"
      );
      str = str.replace(/\{0\}/g, row.totalPerMan);
      str = str.replace(/\{1\}/g, row.total);
      return str
    }
    if (row.totalPerMan && !row.total) {
      let str: any = this.formatI18n(
        "/营销/券礼包活动/新建商品满额发券活动/详情界面/发券数限制",
        "每人限{0}张，不限制活动总张数"
      );
      str = str.replace(/\{0\}/g, row.totalPerMan);
      return str
    }
    if (!row.totalPerMan && row.total) {
      let str: any = this.formatI18n(
        "/营销/券礼包活动/新建商品满额发券活动/详情界面/发券数限制",
        "不限制每人领取张数，活动总限{0}张"
      );
      str = str.replace(/\{0\}/g, row.total);
      return str
    }
    if (!row.totalPerMan && !row.total) {
      let str: any = this.formatI18n(
        "/营销/券礼包活动/新建商品满额发券活动/详情界面/发券数限制",
        "不限制每人领取张数，不限制活动总张数"
      );
      return str
    }
  }
};