import { Component, Vue, Watch } from 'vue-property-decorator'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import FormItem from 'cmp/formitem/FormItem.vue'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import MemberPointsAccount from 'model/points/adjustbill/MemberPointsAccount'
import PointsAdjustBillReason from 'model/points/adjustbill/PointsAdjustBillReason'
import PointsAdjustBill from 'model/points/adjustbill/PointsAdjustBill'
import PointsAdjustBillLine from 'model/points/adjustbill/PointsAdjustBillLine'
import PointsAdjustBillApi from 'http/points/adjustbill/PointsAdjustBillApi'
import PointsReasonFilter from 'model/points/adjustbill/PointsReasonFilter'
import RSOrgFilter from 'model/common/RSOrgFilter'
import OrgApi from 'http/org/OrgApi'
import IdName from 'model/common/IdName'
import RSOrg from 'model/common/RSOrg'
import SysConfigApi from "http/config/SysConfigApi";
import MemberOperationBillApi from 'http/batchOperate/MemberOperationBillApi'
import OperateMemberInfo from 'model/member/OperateMemberInfo'
import I18nPage from 'common/I18nDecorator'
import MemberOperationBill from 'model/member/MemberOperationBill'
import MemberOperationBillLine from 'model/member/MemberOperationBillLine'
import DateUtil from 'util/DateUtil'
import SelectStores from 'cmp/selectStores/SelectStores'

@Component({
  name: 'ScoreAdjustAdd',
  components: {
    SubHeader,
    FormItem,
    BreadCrume,
    SelectStores
  }
})
@I18nPage({
  auto: false,
  prefix: [
    '/会员/会员批量操作单'
  ]
})
export default class ScoreAdjustAdd extends Vue {
  saveLoading = false
  saveAndAuditLoading = false
  panelArray = [
    {
      name: '',
      url: 'batch-operate-list'
    },
    {
      name: '',
      url: ''
    }
  ]
  isDisabled = true
  member = ''
  timer = 0
  memberObj: OperateMemberInfo = new OperateMemberInfo()
  memberCouponsList: IdName[] = []
  memberDepositList: any[] = []
  memberFlag = false
  memberFlagContent = ''
  reasons: PointsAdjustBillReason[] = []
  accountTotalArray: number[] = []
  accountSelectArray: boolean[] = []
  noUse = ''
  isMoreMarketing: boolean = true // 是否开启多营销中心 true 开启 false 不开启
  showOrg = false // 控制模态框的展示
  useMemberOwnerStore = false
  orgId: any = ''
  stateList: any[] = [
    {
      label: this.formatI18n("/会员/会员资料", "使用中"),
      value: 'Using'
    },
    {
      label: this.formatI18n("/会员/会员资料", "已冻结"),
      value: 'Blocked'
    }
  ]
  afterState: any = 'Using'
  remark: any = ''

  @Watch('member')
  onMmeberChange(value: string) {
    if (!value) {
      this.memberFlag = true
      this.memberFlagContent = this.formatI18n('/权益/积分/新建积分调整单/会员框为空/点击保存', '会员不能为空') as string
    }
  }

  get getOrgAppendAttr() {
    const obj:any = {}
    obj.orgTypeEquals = "PHX"
    if (this.isMoreMarketing) {
      obj.marketingCenterIdEquals = sessionStorage.getItem('marketCenter')
    } else {
      obj.marketingCenterIdEquals = null
    }
    return obj
  }

  created() {
    if (sessionStorage.getItem('isMultipleMC') == '1') {
      this.isMoreMarketing = true
    } else {
      this.isMoreMarketing = false
    }
    this.panelArray[0].name = this.i18n('会员批量操作单')
    this.panelArray[1].name = this.formatI18n('新建操作单')
    let line: PointsAdjustBillLine = new PointsAdjustBillLine()
    line.occurAmount = '' as any
    line.lineNo = 1
    let idName = new IdName();
    idName.id = '';
    this.getPrePermission()
    this.getPointsAdjustReason()
    this.getConfig()
  }

  mounted() {
    this.panelArray[1].name = this.$route.query.from === 'edit' ? this.i18n('编辑调整单') : this.formatI18n('新建操作单')
  }

  doBack() {
    this.$router.back()
  }

  doMemberChange() {
    if (!this.member) {
      this.clearMember()
      return
    }
    this.doQuerySearchAsync()
  }

  doSaveAndAudit() {
    if (!this.member) {
      this.memberFlag = true
      this.memberFlagContent = this.formatI18n('/权益/积分/新建积分调整单/会员框为空/点击保存', '会员不能为空') as string
      return
    }
    if (this.member && this.memberFlag) {
      return
    }
    if (!this.orgId) {
      this.$message.warning(this.formatI18n('/公用/查询条件/提示', '请选择发生组织'))
      return
    }
    this.saveAndAuditLoading = true
    if (this.$route.query.from === 'edit') {
      MemberOperationBillApi.saveModify(this.getParams()).then((resp: any) => {
        if (resp && resp.code === 2000) {
          MemberOperationBillApi.audit(this.$route.query.id as string).then((resp: any) => {
            if (resp && resp.code === 2000) {
              this.$message.success(this.formatI18n('/权益/积分/新建积分调整单/点击保存并审核', '保存并审核成功') as string)
              this.$router.push({ name: 'batch-operate-dtl', query: { id: this.$route.query.id } })
            }
          }).catch((error) => {
            if (error && error.message) {
              this.$message.error(error.message)
            }
          })
        }
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    } else {
      MemberOperationBillApi.saveAndAudit(this.getParams()).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.formatI18n('/权益/积分/新建积分调整单/点击保存并审核', '保存并审核成功') as string)
          this.$router.push({ name: 'batch-operate-dtl', query: { id: resp.data } })
        }
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    }
  }

  doSave() {
    if (!this.member) {
      this.memberFlag = true
      this.memberFlagContent = this.formatI18n('/权益/积分/新建积分调整单/会员框为空/点击保存', '会员不能为空') as string
      return
    }
    if (this.member && this.memberFlag) {
      return
    }
    if (!this.orgId) {
      this.$message.warning(this.formatI18n('/公用/查询条件/提示', '请选择发生组织'))
      return
    }
    this.saveLoading = true
    if (this.$route.query.from === 'edit') {
      MemberOperationBillApi.saveModify(this.getParams()).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.formatI18n('/权益/积分/新建积分调整单/点击保存', '保存成功') as string)
          this.$router.push({ name: 'batch-operate-dtl', query: { id: this.$route.query.id } })
        }
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    } else {
      MemberOperationBillApi.save(this.getParams()).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.formatI18n('/权益/积分/新建积分调整单/点击保存', '保存成功') as string)
          this.$router.push({ name: 'batch-operate-dtl', query: { id: resp.data } })
        }
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    }
  }

  doCancel() {
    this.$router.back()
  }

  doQuerySearchAsync() {
    MemberOperationBillApi.getMemberAccount(this.member).then((resp: any) => {
      if (resp && resp.code === 2000) {
        if (resp.data) {
          this.isDisabled = false
          this.memberObj = resp.data
          this.memberObj.coupons
          this.memberCouponsList = []
          this.memberDepositList = []
          Object.keys(this.memberObj.coupons).forEach((item: any) => {
            this.memberCouponsList.push({ id: item, name: this.memberObj.coupons[item] })
          })
          Object.keys(this.memberObj.accountBalances).forEach((item: any) => {
            this.memberDepositList.push({ key: item, value: this.memberObj.accountBalances[item] })
          })
          this.$nextTick(() => {
            this.memberFlag = false
          })
        } else {
          this.clearMember()
        }
      } else {
        this.clearMember()
      }
    }).catch((error) => {
      if (error && error.message) {
        this.clearMember()
      }
    })
  }

  clearMember() {
    this.memberFlag = true
    this.memberFlagContent = this.formatI18n('/权益/积分/新建积分调整单/会员框输入会员不存在', '不存在该会员') as string
    this.memberObj = new OperateMemberInfo()
    this.memberCouponsList = []
    this.memberDepositList = []
    this.remark = ''
    this.afterState = 'Using'
  }

  private getPointsAdjustReason() {
    let filter: PointsReasonFilter = new PointsReasonFilter()
    filter.page = 0
    filter.pageSize = 0
    PointsAdjustBillApi.listReason(filter).then((resp: any) => {
      if (resp && resp.data) {
        this.reasons = resp.data
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private getPrePermission() {
    if (this.$route.query.id) {
      this.getModify()
    }
  }

  private getConfig() {
    SysConfigApi.get().then((resp: any) => {
      if (resp && resp.data) {
        this.showOrg = resp.data.enableMultiMarketingCenter
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }


  private getParams() {
    let params = new MemberOperationBill()
    if (this.$route.query.id) {
      params.billNumber = this.$route.query.id as string
    }
    params.source = 'create'
    params.occurredOrg = this.orgId
    params.created = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
    let line = new MemberOperationBillLine()
    line = new MemberOperationBillLine()
    line.memberId = this.memberObj.memberId
    line.name = this.memberObj.name
    line.oldState = this.memberObj.state
    line.newState = this.afterState
    line.accountBalances = this.memberDepositList.map((item: any) => {
      return {
        accountName: item.key,
        balance: item.value
      }
    })
    line.category = this.afterState === 'Blocked'?'BLOCK': 'UNBLOCK'
    // @ts-ignore
    line.points = this.memberObj.points
    line.coupons = this.memberCouponsList
    line.remark = this.remark
    params.lines = []
    params.lines.push(line)
    return params
  }

  private getModify() {
    MemberOperationBillApi.get(this.$route.query.id as string).then((resp: any) => {
      if (resp && resp.code === 2000) {
        MemberOperationBillApi.queryDetail(this.$route.query.id as string, 0, 99999).then((res: any)=> {
          this.member = res.data[0].mobile || res.data[0].hdCardMbrId || res.data[0].hdCardCardNum
          this.orgId = resp.data.occurredOrg
          this.remark = res.data[0].remark
          this.afterState = res.data[0].newState
          // this.memberObj = res.data[0]
          // this.memberCouponsList = res.data[0].coupons
          // this.memberDepositList = res.data[0].accountBalances
          this.doQuerySearchAsync()
        })

        
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
}
