/*
 * @Author: 黎钰龙
 * @Date: 2024-05-17 13:32:11
 * @LastEditTime: 2024-05-17 15:15:53
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\member\data\cmp\SendCouponDialog.ts
 * 记得注释
 */
import CouponTemplateSelectorDialog from 'cmp/selectordialogs/CouponTemplateSelectorDialog';
import I18nPage from 'common/I18nDecorator';
import CouponInfo from 'model/common/CouponInfo';
import CouponItem from 'model/common/CouponItem';
import CouponTemplate from 'model/coupon/template/CouponTemplate';
import { Component, Vue } from 'vue-property-decorator';
@Component({
  name: 'SendCouponDialog',
  components: {
    CouponTemplateSelectorDialog
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板'
  ],
  auto: true
})
export default class SendCouponDialog extends Vue {
  $refs: any
  couponSendQtyFlag: boolean = false  //优惠券数量弹窗
  couponList: CouponItem[] = [] //优惠券列表

  open() {
    const arr = this.couponList.map((item) => {
      const obj = new CouponTemplate()
      obj.number = item.coupons?.templateId
      obj.name = item.coupons?.name
      return obj
    }) || []
    this.$refs.couponTemplate.open(arr, "single");
  }

  //选择优惠券
  doCardTemplateSelected(arr: CouponTemplate[]) {
    this.couponList = arr.map((item) => {
      const couponObj = new CouponItem()
      couponObj.qty = 1
      couponObj.coupons = new CouponInfo()
      couponObj.coupons.name = item.name
      couponObj.coupons.templateId = item.number
      return couponObj
    })
    this.$nextTick(() => {
      this.couponSendQtyFlag = true
    })
  }

  //删除
  removeCoupon(index: number) {
    this.couponList.splice(index, 1)
  }

  //取消
  doCancel() {
    this.couponSendQtyFlag = false
    this.couponList = []
  }

  //确定发券
  doConfirm() {
    if(this.couponList.some(item => !item.qty)) {
      return this.$message.warning(this.i18n('/会员/会员资料/详情界面/会员资产转移/请填写发券数量'))
    }
    if (!this.couponList?.length) {
      return this.$message.warning(this.i18n('/储值/预付卡/电子礼品卡活动/编辑页面/请添加券'))
    }
    this.$emit('doSend', this.couponList)
    this.$nextTick(()=>{
      this.doCancel()
    })
  }
};