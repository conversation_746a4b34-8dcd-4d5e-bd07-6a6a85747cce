/*
 * @Author: 黎钰龙
 * @Date: 2024-05-14 14:13:29
 * @LastEditTime: 2024-05-20 13:52:09
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\promotion\MarketingApplyActivityApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import MarketingActivitySubmitAuditRequest from 'model/promotion/marketApply/MarketingActivitySubmitAuditRequest'
import MarketingApplyActivityBody from 'model/promotion/marketApply/MarketingApplyActivityBody'
import MarketingApplyActivityFilter from 'model/promotion/marketApply/MarketingApplyActivityFilter'
import MarketingApplyBill from 'model/promotion/marketApply/MarketingApplyBill'
import MarketingApplyBillFilter from 'model/promotion/marketApply/MarketingApplyBillFilter'


export default class MarketingApplyActivityApi {
  /**
   * 查询活动
   * 查询活动。
   * 
   */
  static queryActivity(body: MarketingApplyActivityFilter): Promise<Response<MarketingApplyActivityBody>> {
    return ApiClient.server().post(`/v1/web/marketing-apply/queryActivity`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询申请列表活动
   * 查询申请列表活动。
   * 
   */
  static queryApplyInfo(body: MarketingApplyBillFilter): Promise<Response<MarketingApplyBill[]>> {
    return ApiClient.server().post(`/v1/web/marketing-apply/queryApplyInfo`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 提交审核
   * 提交审核。
   * 
   */
  static submitAudit(body: MarketingActivitySubmitAuditRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/web/marketing-apply/submitAudit`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
