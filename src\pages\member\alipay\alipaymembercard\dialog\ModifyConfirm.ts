import {Component, Vue} from 'vue-property-decorator'

@Component({
  name: 'ModifyConfirm',
  components: {}
})
export default class ModifyConfirm extends Vue {
  dialogShow: boolean = false
  created() {
    this.initData()
  }
  initData() {

  }
  doBeforeClose(done: any) {
    this.dialogShow = false
    done()
  }
  doConfirmClose() {
    this.dialogShow = false
    this.$emit('confirm')
  }
  doCancel() {
    this.dialogShow = false
    this.$emit('cancel')
  }
  open() {
    this.dialogShow = true
  }
  close() {
    this.dialogShow = false
  }
}