/*
 * @Author: 黎钰龙
 * @Date: 2023-10-25 11:58:23
 * @LastEditTime: 2023-10-25 11:58:32
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\limitActivity\BMemberGoodsQuatoActivityResult.ts
 * 记得注释
 */
import ActivityStateCountResult from 'model/common/ActivityStateCountResult'
import BMemberGoodsQuatoActivity from './BMemberGoodsQuatoActivity'

// 活动结果查询
export default class BMemberGoodsQuatoActivityResult {
  // 计数结果
  countResult: Nullable<ActivityStateCountResult> = null
  // 总数
  total: Nullable<number> = null
  // 总页数
  pageCount: Nullable<number> = null
  // 页码
  page: Nullable<number> = null
  // 页面大小，大于0
  pageSize: Nullable<number> = null
  // 活动
  result: BMemberGoodsQuatoActivity[] = []
}