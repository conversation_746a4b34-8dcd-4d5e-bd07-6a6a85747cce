<!--
 * @Author: 黎钰龙
 * @Date: 2024-06-26 10:12:07
 * @LastEditTime: 2024-07-02 14:13:51
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\cmp\page-props-components\cycle-conditions\CycleConditions.vue
 * 记得注释
-->
<template>
  <div class="time-cycle">
    <el-form :model="form" ref="form" v-if="isShow">
      <el-form-item
        :label="label"
        label-width="0px"
        v-for="(cycle, index) in form.timeCycles"
        :key="index"
        :prop="`timeCycles[${index}].times`"
        :rules="{ required: true, message: i18n('请选择时间范围'), trigger: ['change'] }"
      >
        <div class="time-cycle-content">
          <el-time-picker
            @change="handleChange"
            is-range
            :clearable="false"
            v-model="cycle.times"
            :range-separator="i18n('至')"
            :start-placeholder="i18n('开始时间')"
            :end-placeholder="i18n('结束时间')"
            :placeholder="i18n('请选择时间范围')"
            value-format="HH:mm:ss"
            format="HH:mm:ss"
          >
          </el-time-picker>
          <p class="time-cycle-btns">
            <span
              v-if="index === form.timeCycles.length - 1 && index !== 4"
              @click="addTimeCycle(index)"
              >+ {{ i18n('添加') }}</span
            >
            <span v-else @click="delTimeCycle(index)">{{ i18n('删除') }}</span>
          </p>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" src="./CycleConditions.ts"></script>

<style lang="scss" scoped>
.time-cycle {
  &-content {
    display: flex;
    width: 100%;
  }
  &-btns {
    width: 60px;
    margin-left: 10px;
    span {
      width: 50px;
      text-align: right;
      color: #4d63ec;
      cursor: pointer;
    }
  }
}

::v-deep .el-date-editor .el-range-separator {
  width: 24px !important;
}
</style>
