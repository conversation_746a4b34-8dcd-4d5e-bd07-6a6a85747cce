import ApiClient from 'http/ApiClient'
import GoodsGainByQtyPointsActivity from 'model/points/activity/goodsgainbyqtypoints/GoodsGainByQtyPointsActivity'
import Response from 'model/common/Response'
import CalculateScoreMultipleParams from "model/points/activity/goodsgainbyqtypoints/CalculateScoreMultipleParams";
import ExcelRuleLine from "model/points/activity/goodsgainbyqtypoints/ExcelRuleLine";
import RuleLine from "model/points/activity/goodsgainbyqtypoints/RuleLine";
import GoodsImportResult from "model/points/activity/goodsgainbyqtypoints/GoodsImportResult";

export default class GoodsGainPointsByQtyActivityApi {
  /**
   * 批量计算积分倍数
   * 批量计算积分倍数。
   *
   */
  static batchCalculateScoreMultiple(body: ExcelRuleLine[], channelId?: string, channelType?: string): Promise<Response<RuleLine[]>> {
    return ApiClient.server().post(`/v1/points-activity/goods-gain-additional-points-by-qty/batchCalculateScoreMultiple/${channelId}/${channelType}`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 计算积分倍数
   * 计算积分倍数。
   *
   */
  static calculateScoreMultiple(body: CalculateScoreMultipleParams): Promise<Response<number>> {
    return ApiClient.server().post(`/v1/points-activity/goods-gain-additional-points-by-qty/calculateScoreMultiple`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 详情
   * 详情。
   *
   */
  static detail(activityId: string): Promise<Response<GoodsGainByQtyPointsActivity>> {
    return ApiClient.server().get(`/v1/points-activity/goods-gain-additional-points-by-qty/detail/${activityId}`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 导入商品行
   * 导入商品行。
   *
   */
  static importGoods(body: any, channelId?: string, channelType?: string): Promise<Response<GoodsImportResult>> {
    return ApiClient.server().post(`/v1/points-activity/goods-gain-additional-points-by-qty/importGoods/${channelId}/${channelType}`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 修改或保存
   * 修改或保存。
   *
   */
  static saveOrModify(body: GoodsGainByQtyPointsActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/points-activity/goods-gain-additional-points-by-qty/saveOrModify`, body, {}).then((res) => {
      return res.data
    })
  }


}
