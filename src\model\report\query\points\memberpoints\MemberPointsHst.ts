export default class MemberPointsHst {
  // 发生时间
  occurredTime: Nullable<Date> = null
  // 发生组织
  org: Nullable<string> = null
  // 交易类型
  category: Nullable<string> = null
  // 会员识别码
  identId: Nullable<string> = null
  // 原积分余额
  originalBalance: Nullable<number> = null
  // 发生积分
  occurredPoints: Nullable<number> = null
  // 发生后积分
  overPoints: Nullable<number> = null
  // 交易号
  transNo: Nullable<string> = null
  // 积分场景
  scene: Nullable<string> = null

}