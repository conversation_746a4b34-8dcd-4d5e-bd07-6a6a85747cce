import {Component, Vue} from 'vue-property-decorator'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import FormItem from 'cmp/formitem/FormItem.vue'
import Account from 'model/prepay/adjustbill/Account'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import CustomizeContent<PERSON>pi from 'http/customizecontent/CustomizeContentApi'
import CustomizeContentFilter from 'model/customizecontent/CustomizeContentFilter'
import CardAdjustBillApi from 'http/card/adjustbill/CardAdjustBillApi'
import CardAccount from 'model/card/adjustbill/CardAccount'
import CardAdjustBill from 'model/card/adjustbill/CardAdjustBill'
import CardAdjustBillLine from 'model/card/adjustbill/CardAdjustBillLine'
import MutableNsid from 'model/common/MutableNsid'
import IdName from 'model/common/IdName'
import ConstantMgr from 'mgr/ConstantMgr';
import PrePayConfigApi from 'http/prepay/config/PrePayConfigApi';
import I18nPage from "common/I18nDecorator";
import SysConfigApi from "http/config/SysConfigApi";
import SelectStores from 'cmp/selectStores/SelectStores'

class PrepayCardAdjustAddFormData {
  member = ''
  occurAmount = ''
  occurGiftAmount = ''
  count = ''
  reason = ''
  remark = ''
  occurredOrg: IdName = new IdName()
  useMemberOwnerStore = false
  isDisabled = true
}

@Component({
  name: 'PrepayCardAdjustAdd',
  components: {
    SubHeader,
    FormItem,
    BreadCrume,
    SelectStores
  }
})
@I18nPage({
  prefix: [
    '/储值/预付卡/预付卡调整单/编辑页面',
    '/公用/按钮',
    '/储值/预付卡/预付卡查询/列表页面'
  ],
})
export default class PrepayCardAdjustAdd extends Vue {
  i18n: (str: string, params?: string[]) => string
  panelArray: any
  isDisabled = true
  dtlParams: any = {}

  timer = 0
  memberMap: any = {} // string -> CardAccount
  memberErrMap: any = {} // string -> error string
  reasons: any[] = []
  noUse = ''
  switchFlag = false
  loading = false
  showOrg = false // 控制模态框的展示
  activeName = '1'
  tabNames: string[] = []
  formData = new PrepayCardAdjustAddFormData()
  isMoreMarketing: boolean = false // 是否开启多营销中心 true 开启 false 不开启
  formDataMap: any = {} // activeName -> PrepayCardAdjustAddFormData

  //是否为次卡
  get isCountingCard() {
    return this.memberMap[this.formData.member]?.cardTypeCode === "CountingCard"
  }

  checkCountingCard(key:string) {
    return this.memberMap[key]?.cardTypeCode === "CountingCard"
  }

  get getTotal() {
    if (this.formData.occurAmount && this.formData.occurGiftAmount) {
      return (Number(this.formData.occurAmount) + Number(this.formData.occurGiftAmount)).toFixed(2)
    }
    return ''
  }

  get getOrgAppendAttr() {
    const obj:any = {}
    obj.orgTypeEquals = 'PHX'
    if (this.isMoreMarketing) {
      obj.marketingCenterIdEquals = sessionStorage.getItem('marketCenter')
    }
    return obj
  }

  created() {
    if(sessionStorage.getItem('isMultipleMC') == '1') {
      this.isMoreMarketing = true
    } else {
      this.isMoreMarketing = false
    }
    this.panelArray = [
      {
        name: this.i18n('预付卡调整单'),
        url: 'prepay-card-adjust'
      },
      {
        name: this.i18n('新建预付卡调整单'),
        url: ''
      }
    ]
    this.panelArray[1].name = this.$route.query.from === 'edit' ? this.i18n('编辑预付卡调整单') : this.i18n('新建预付卡调整单')
    let account: Account = new Account()
    account.id = ''
    account.balance = 0
    account.giftBalance = 0
    this.initForm()
    this.getStoreValueAdjustReason()
    this.getModify()
    this.getPrePermission()
    this.getConfig()
  }

  initForm() {
    if (!this.$route.query.from || this.$route.query.from === 'add') {
      this.activeName = new Date().getTime() + ''
      this.tabNames = [this.activeName]
      this.formData = new PrepayCardAdjustAddFormData()
      this.formDataMap[this.activeName] = this.formData
    }
  }

  handleTabClick(tab: any, event: any) {
    this.formData = this.formDataMap[tab.name]
  }

  handleAddCard() {
    this.doAddCard(new PrepayCardAdjustAddFormData())
  }

  doAddCard(formData: PrepayCardAdjustAddFormData) {
    let name = new Date().getTime() + ''
    while (this.tabNames.indexOf(name) > -1) {
      name = new Date().getTime() + ''
    }
    this.tabNames.push(name)
    this.activeName = name
    this.formData = formData
    this.formDataMap[name] = formData
  }

  removeTab(targetName: string) {
    let index = this.tabNames.indexOf(targetName)
    let nextIndex = index === 0 ? 1 : index - 1
    delete this.formDataMap[targetName]
    this.activeName = this.tabNames[nextIndex]
    this.formData = this.formDataMap[this.activeName]
    this.tabNames.splice(index, 1)
    console.log(this.formDataMap)
  }

  doOccurAmountChange() {
    let regex = /^(-)?(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/g
    let ownerAmont = Number(this.memberMap[this.formData.member].account!.balance)
    if (regex.test((this.formData.occurAmount as any))) {
      if (Number(this.formData.occurAmount) > 999999) {
        this.formData.occurAmount = 999999 as any
        this.$forceUpdate()
      }
      if (Number(this.formData.occurAmount) < 0) {
        if (Math.abs(Number(this.formData.occurAmount)) > ownerAmont) {
          this.formData.occurAmount = -ownerAmont as any
        }
        this.$forceUpdate()
      }
    } else {
      this.formData.occurAmount = ''
      this.$forceUpdate()
    }
    this.formData.occurAmount = (Number(this.formData.occurAmount).toFixed(2)) as any
    this.$forceUpdate()
    if (this.formData.occurGiftAmount) {
      // todo
      this.$forceUpdate()
    }

  }

  doGiftOccurAmountChange() {
    let regex = /^(-)?(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/g
    let ownerAmont = Number(this.memberMap[this.formData.member].account!.giftBalance)
    if (regex.test((this.formData.occurGiftAmount as any))) {
      if (Number(this.formData.occurGiftAmount) > 999999) {
        this.formData.occurGiftAmount = 999999 as any
        this.$forceUpdate()
      }
      if (Number(this.formData.occurGiftAmount) < 0 && Math.abs(Number(this.formData.occurGiftAmount)) > ownerAmont) {
        this.formData.occurGiftAmount = -ownerAmont as any
      }
      this.$forceUpdate()
    } else {
      this.formData.occurGiftAmount = ''
    }
    this.formData.occurGiftAmount = (Number(this.formData.occurGiftAmount).toFixed(2)) as any
    this.$forceUpdate()
    if (this.formData.occurAmount) {
      // todo
      this.$forceUpdate()
    }
  }

  doBack() {
    this.$router.back()
  }

  doMemberChange() {
    if (!this.formData.member) {
      this.isDisabled = true
      return
    }
    if (this.memberErrMap[this.formData.member]) {
      this.$message.warning(this.memberErrMap[this.formData.member])
      return
    }
    let count = 0
    for (let name of this.tabNames) {
      if (this.formDataMap[name].member === this.formData.member) {
        count++
        if (count > 1) {
          this.$message.warning(this.i18n('卡号已存在'))
          this.formData.member = ''
          return
        }
      }
    }
    this.doQuerySearchAsync(this.formData.member)
  }

  doSaveAndAudit() {
    for (let activeName of this.tabNames) {
      let success = this.doValidate(this.i18n('卡') + (this.tabNames.indexOf(activeName) + 1), this.formDataMap[activeName])
      if (!success) {
        return
      }
    }
    if (this.$route.query.from === 'edit') {
      this.loading = true
      CardAdjustBillApi.saveModify(this.getParams()).then((resp: any) => {
        if (resp && resp.code === 2000) {
          CardAdjustBillApi.audit(this.$route.query.id as string).then((resp: any) => {
            if (resp && resp.code === 2000) {
              this.$message.success(this.i18n('保存并审核成功'))
              this.$router.push({name: 'prepay-card-adjust-dtl', query: {id: this.$route.query.id}})
            } else {
              throw new Error(resp.msg || this.i18n('操作失败'))
            }
          }).catch((error) => { this.$message.error(error.message || this.i18n('内部异常'))})
        } else {
          throw new Error(resp.msg || this.i18n('操作失败'))
        }
      }).catch((error: any) => {
        this.$message.error(error.message || this.i18n('内部异常'))
      }).finally(() => {
        this.loading = false
      })
    } else {
      this.loading = true
      CardAdjustBillApi.saveAndAudit(this.getParams()).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.i18n('保存并审核成功'))
          this.$router.push({name: 'prepay-card-adjust-dtl', query: {id: resp.data}})
        } else {
          throw new Error(resp.msg || this.i18n('操作失败'))
        }
      }).catch((error) => { this.$message.error(error.message) || this.i18n('内部异常') })
      .finally(() => {
        this.loading = false
      })
    }
  }

  doValidate(name: string, formData: PrepayCardAdjustAddFormData) {
    if (!formData.member) {
      this.$message.warning(name + this.i18n('卡号不能为空'))
      return false
    }
    if (formData.member && this.memberErrMap[formData.member]) {
      this.$message.warning(name + ": " + this.memberErrMap[formData.member])
      return false
    }
    if (this.checkCountingCard(formData.member) && !formData.count) {
      this.$message.warning(name + this.i18n('请填写必填项'))
      return false
    }
    if (!this.checkCountingCard(formData.member)) {
      if ((!formData.occurAmount && formData.occurAmount !== '0') && (!formData.occurGiftAmount && formData.occurGiftAmount !== '0')) {
        this.$message.warning(name + this.i18n('实充调整和返现调整不能同时为空'))
        return false
      }
      if (formData.occurAmount === '0' && formData.occurGiftAmount === '0') {
        this.$message.warning(name + this.i18n('实充调整和返现调整不能同时为0'))
        return false
      }
      if (!formData.occurAmount || !formData.occurGiftAmount) {
        this.$message.warning(name + this.formatI18n('/公用/js提示信息', '请填写必填项'))
        return false
      }
    }
    if ((!formData.occurredOrg || !formData.occurredOrg.id) && this.showOrg) {
      this.$message.warning(name + this.formatI18n('/公用/查询条件/提示', '请选择发生组织'))
      return false
    }
    return true
  }

  doSave() {
    for (let activeName of this.tabNames) {
      let success = this.doValidate(this.i18n('卡') + (this.tabNames.indexOf(activeName) + 1), this.formDataMap[activeName])
      if (!success) {
        return
      }
    }
    if (this.$route.query.from === 'edit') {
      this.loading = true
      CardAdjustBillApi.saveModify(this.getParams()).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.i18n('保存成功'))
          this.$router.push({name: 'prepay-card-adjust-dtl', query: {id: this.$route.query.id}})
        } else {
          throw new Error(resp.msg || this.i18n('操作失败'))
        }
      }).catch((error: any) => {
        this.$message.error(error.message || this.i18n('内部异常'))
      }).finally(() => {
        this.loading = false
      })
    } else {
      this.loading = true
      CardAdjustBillApi.save(this.getParams()).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.i18n('保存成功'))
          this.$router.push({name: 'prepay-card-adjust-dtl', query: {id: resp.data}})
        } else {
          throw new Error(resp.msg || this.i18n('操作失败'))
        }
      }).catch((error) => {
        this.$message.error(error.message || this.i18n('内部异常'))
      }).finally(() => {
        this.loading = false
      })
    }
  }

  doCancel() {
    this.$router.back()
  }

  doQuerySearchAsync(cardNo: string) {
    if (this.memberMap[cardNo] && ['USING', 'USED'].indexOf(this.memberMap[cardNo].cardState)) {
      this.formData.isDisabled = false
      return
    }
    CardAdjustBillApi.getCardInfo(cardNo).then((resp: any) => {
      if (resp && resp.code === 2000) {
        if (resp.data) {
          let member = resp.data
          this.$set(this.memberMap, cardNo, member)
          if (resp.data.imprestCard) {
            this.memberErrMap[this.formData.member] = this.i18n('该卡为充值卡，不可进行余额调整')
            this.$message.warning(this.memberErrMap[this.formData.member])
            return
          }
          if (resp.data.cardState === 'USING' || resp.data.cardState === 'USED') {
            this.formData.isDisabled = false
          } else {
            let state = ''
            if (resp.data.cardState === 'UNACTIVATED') {
              state = this.i18n('未激活')
            }
            if (resp.data.cardState === 'PRESENTING') {
              state = this.i18n('转赠中')
            }
            if (resp.data.cardState === 'CANCELLED') {
              state = this.i18n('已作废')
            }
            if (resp.data.cardState === 'FROZEN') {
              state = this.i18n('已冻结')
            }
            if (resp.data.cardState === 'LOST') {
              state = this.i18n('已挂失')
            }
            if (resp.data.cardState === 'RECOVER') {
              state = this.i18n('已回收')
            }
            if (resp.data.cardState === 'MADE') {
              state = this.i18n('已制卡')
            }
            this.memberErrMap[this.formData.member] = this.i18n('当前卡状态为{0}，不可进行余额调整', [state])
            this.$message.warning(this.memberErrMap[this.formData.member])
            return
          }
        } else {
          this.memberErrMap[this.formData.member] = this.i18n('卡号不存在')
          this.$message.warning(this.memberErrMap[this.formData.member])
          this.memberMap[this.formData.member] = new CardAccount()
          this.formData.occurAmount = ''
          this.formData.occurGiftAmount = ''
          this.formData.reason = ''
          this.formData.remark = ''
          this.formData.count = ''
          return
        }
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private getParams() {
    let params: CardAdjustBill = new CardAdjustBill()
    if (this.$route.query.from === 'edit') {
      params.billNumber = this.dtlParams.billNumber
      params.state = this.dtlParams.state
      params.source = this.dtlParams.source
      
      params.marketingCenter = this.dtlParams.marketingCenter
      params.audited = this.dtlParams.audited
      params.auditor = this.dtlParams.auditor
      params.remark = this.dtlParams.remark
      params.detailRemark = this.dtlParams.detailRemark
      params.created = this.dtlParams.created
      params.lastModified = this.dtlParams.lastModified
      params.logs = this.dtlParams.logs
    }
    params.lines = []
    let lineNo = 0
    for (let activeName of this.tabNames) {
      let formData = this.formDataMap[activeName]
      let member = this.memberMap[formData.member]
      let line: CardAdjustBillLine = new CardAdjustBillLine()
      if (this.$route.query.from === 'add') {
        line.lineNo = lineNo++
      }
      line.accountOwner = new MutableNsid()
      line.accountOwner.id = formData.member
      line.accountOwner.namespace = member.cardTypeCode
      if (member && member.account && member.account.balance) {
        line.oldAmount = member.account!.balance
      } else {
        line.oldAmount = 0
      }
      if (member && member.account && member.account.giftBalance) {
        line.oldGiftAmount = member.account.giftBalance
      } else {
        line.oldGiftAmount = 0
      }
      if (this.checkCountingCard(formData.member)) {
        line.occurAmount = formData.count
        line.occurGiftAmount = 0
      } else {
        line.occurAmount = formData.occurAmount as any
        line.occurGiftAmount = formData.occurGiftAmount as any
      }
      line.reason = formData.reason
      line.remark = formData.remark
      if (member && member.accountType && member.accountType.id) {
        line.accountType = member.accountType
      } else {
        line.accountType = new IdName()
      }
      if (member && member.cardType) {
        line.cardType = member.cardType
      } else {
        line.cardType = ''
      }
      line.occurredOrg = formData.occurredOrg
      params.occurredOrg = formData.occurredOrg;
      params.lines.push(line)
    }
    return params
  }

  private getStoreValueAdjustReason() {
    let filter: CustomizeContentFilter = new CustomizeContentFilter()
    filter.page = 0
    filter.pageSize = 0
    filter.type = 'cardAdjustReason'
    CustomizeContentApi.list(filter).then((resp: any) => {
      if (resp && resp.data) {
        this.reasons = resp.data
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private getModify() {
    CardAdjustBillApi.getModify(this.$route.query.id as string).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.dtlParams = resp.data
        if (resp.data && resp.data.lines.length > 0) {
          let lines = resp.data.lines
          lines.sort((e1: any, e2: any) => e1.lineNo - e2.lineNo)
          for (let line of lines) {
            let formData = new PrepayCardAdjustAddFormData()
            formData.member = line.accountOwner.id
            formData.occurAmount = line.occurAmount.toFixed(2)
            formData.count = line.occurAmount.toFixed(0)
            formData.occurGiftAmount = line.occurGiftAmount.toFixed(2)
            formData.reason = line.reason
            formData.remark = line.remark
            formData.occurredOrg = line.occurredOrg
            formData.isDisabled = false
            this.doAddCard(formData);
          }
          this.activeName = this.tabNames[0]
          this.formData = this.formDataMap[this.activeName]
          for (let name of this.tabNames) {
            this.doQuerySearchAsync(this.formDataMap[name].member)
          }
        }
      }
    }).catch((error: any) => {
      this.$message.error(error.message)
    })
  }

  private getPrePermission() {
    const loading = this.$loading(ConstantMgr.loadingOption)
    PrePayConfigApi.get().then((resp: any) => {
      if (resp && resp.code === 2000) {
        loading.close()
        if (resp.data && resp.data.enableMultipleAccount) { // 开启多账户
          this.switchFlag = resp.data.enableMultipleAccount
        } else {
          this.switchFlag = false // 未开启多账户
        }
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
      loading.close()
    })
  }

  private getConfig() {
    SysConfigApi.get().then((resp: any) => {
      if (resp && resp.data) {
        this.showOrg = resp.data.enableMultiMarketingCenter
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private changeOrg() {
    if (this.formData.useMemberOwnerStore) {
      let member = this.memberMap[this.formData.member]
      if (!member.ownStore || !member.ownStore.id) {
        this.$message.error(this.formatI18n('/权益/积分/新建积分调整单/点击使用会员所属门店作为发生组织', '会员所属门店为空！'))
        this.formData.useMemberOwnerStore = false
        return
      }
      this.formData.occurredOrg.id = member.ownStore.id;
      this.formData.occurredOrg.name = member.ownStore.name;
    }
  }
}
