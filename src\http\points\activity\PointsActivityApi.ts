import ApiClient from "http/ApiClient";
import PointsActivityBody from "model/points/activity/PointsActivityBody";
import PointsActivityFilter from "model/points/activity/PointsActivityFilter";
import Response from "model/common/Response";
import ScoreActivityEffect from "model/points/activity/ScoreActivityEffect";

export default class PointsActivityApi {
	/**
	 * 审核活动
	 *
	 */
	static audit(activityId: string): Promise<Response<void>> {
		return ApiClient.server()
			.post(`/v1/points-activity/audit/${activityId}`, {}, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 批量审核储值活动
	 *
	 */
	static batchAudit(body: Array<string>): Promise<Response<string>> {
		return ApiClient.server()
			.post(`/v1/points-activity/batch/audit`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 批量删除储值活动
	 *
	 */
	static batchRemove(body: Array<string>): Promise<Response<string>> {
		return ApiClient.server()
			.post(`/v1/points-activity/batch/remove`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 批量终止储值活动
	 *
	 */
	static batchStop(body: Array<string>): Promise<Response<string>> {
		return ApiClient.server()
			.post(`/v1/points-activity/batch/stop`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 积分活动分页查询
	 *
	 */
	// static query(body: PointsActivityFilter): Promise<Response<PointsActivityBody>> {
	// 	return ApiClient.server()
	// 		.post(`/v1/points-activity/query`, body, {})
	// 		.then((res) => {
	// 			return res.data;
	// 		});
	// }

	/**
	 * 积分活动分页查询
	 *
	 */
	static query(body: PointsActivityFilter): Promise<Response<PointsActivityBody>> {
		return ApiClient.server()
			.post(`/v1/web/activity/query`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 全类型活动分页查询
	 *
	 */
	static queryAll(body: PointsActivityFilter): Promise<Response<PointsActivityBody>> {
		return ApiClient.server()
			.post(`/v1/web/activity/queryAll`, body, {})
			.then((res) => {
				return res.data;
			});
	}
	/**
	 * 积分活动效果评估
	 * 积分活动效果评估。
	 *
	 */
	static queryEffect(activity_id: string): Promise<Response<ScoreActivityEffect>> {
		return ApiClient.server()
			.post(
				`/v1/points-activity/queryEffect`,
				{},
				{
					params: {
						activity_id: activity_id,
					},
				}
			)
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 删除活动
	 *
	 */
	static remove(activityId: string): Promise<Response<void>> {
		return ApiClient.server()
			.post(`/v1/points-activity/remove/${activityId}`, {}, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 终止活动
	 *
	 */
	static stop(activityId: string): Promise<Response<void>> {
		return ApiClient.server()
			.post(`/v1/points-activity/stop/${activityId}`, {}, {})
			.then((res) => {
				return res.data;
			});
	}
}
