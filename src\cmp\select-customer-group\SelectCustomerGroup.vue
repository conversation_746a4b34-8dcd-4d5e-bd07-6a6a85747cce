<template>
  <el-select
    v-model="selectGroup"
    value-key="id"
    :style="{width: width}"
    :loading="selectLoading"
    :disabled="disabled"
    clearable
    filterable
    remote
    :remote-method="doRemoteMethod"
    :placeholder="placeholder ? placeholder : i18n('请选择/输入客群')"
  >
    <el-option v-if="!hideAll" :label="i18n('/公用/活动/状态/全部')" :value="null">
      {{ i18n("/公用/活动/状态/全部") }}
    </el-option>
    <template v-if="isOnlyId">
      <el-option :label="item.name" :value="item.uuid" v-for="(item, index) in groups" :key="index">
        [{{ item.uuid }}]{{ item.name }}
      </el-option>
    </template>
    <template v-else>
      <el-option :label="item.name" :value="item" v-for="(item, index) in groups" :key="index">
        [{{ item.uuid }}]{{ item.name }}
      </el-option>
    </template>
  </el-select>
</template>

<script lang="ts" src="./SelectCustomerGroup.ts">
</script>

<style>

</style>