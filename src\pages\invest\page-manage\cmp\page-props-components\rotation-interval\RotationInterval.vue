<!--
 * @Author: haiding <EMAIL>
 * @Date: 2025-02-28 10:19:48
 * @LastEditors: haiding <EMAIL>
 * @LastEditTime: 2025-02-28 16:14:32
 * @FilePath: \new-kequn\src\pages\invest\page-manage\cmp\page-props-components\rotation-interval\RotationInterval.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!--
 * @Author: haiding <EMAIL>
 * @Date: 2025-02-28 10:19:48
 * @LastEditors: haiding <EMAIL>
 * @LastEditTime: 2025-02-28 11:50:42
 * @FilePath: \new-kequn\src\pages\invest\page-manage\cmp\rotation-interval\RotationInterval.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <el-form inline v-if="formMode === FormModeType.form" :label-position="labelPosition" :model="value" :rules="rules"
      ref="form" style="width: 100%">
      <el-form-item :label="label" style="width: 100%" prop="propInterval">
        <el-select v-model="value.propInterval" @change="handleChange">
          <el-option :label="i18n('不自动轮播')" value="0"></el-option>
          <el-option label="1s" value="1"></el-option>
          <el-option label="2s" value="2"></el-option>
          <el-option label="3s" value="3"></el-option>
          <el-option label="4s" value="4"></el-option>
          <el-option label="5s" value="5"></el-option>
          <el-option label="10s" value="10"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" src="./RotationInterval.ts"></script>

<style lang="scss" scoped>
.el-select,
.el-input__inner {
  width: 100%;
}
.el-select-dropdown__item {
  text-align: center;
}
</style>