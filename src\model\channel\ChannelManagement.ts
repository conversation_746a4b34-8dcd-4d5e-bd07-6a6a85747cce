import Channel from 'model/common/Channel'
import {ChannelState} from 'model/channel/ChannelState'

export default class ChannelManagement {
    // 渠道标识，type+id唯一
    channel: Channel = new Channel()
    // 渠道名称
    name: string = ''
    // 状态,ENABLED——启用；DISABLED——禁用
    state: Nullable<ChannelState> = ChannelState.ENABLED
    // 默认门店代码
    defaultOrgId: Nullable<String> = ''
    // 微盟商户id
    defaultOuterOrgId: Nullable<String> = ''
}