<?xml version="1.0" encoding="UTF-8"?>
<svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 60 (88103) - https://sketch.com -->
    <title>ic_set_selected</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <circle id="path-1" cx="16" cy="16" r="4"></circle>
        <filter x="-87.5%" y="-62.5%" width="275.0%" height="275.0%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0.494117647   0 0 0 0 1  0 0 0 0.301491477 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="🔪icon" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="切图" transform="translate(-457.000000, -189.000000)">
            <g id="ic_set_selected" transform="translate(457.000000, 189.000000)">
                <g id="cutting/ic_set_selected">
                    <g>
                        <path d="M18,4.15470054 L25.2583302,8.34529946 C26.4959346,9.05983064 27.2583302,10.3803387 27.2583302,11.8094011 L27.2583302,20.1905989 C27.2583302,21.6196613 26.4959346,22.9401694 25.2583302,23.6547005 L18,27.8452995 C16.7623957,28.5598306 15.2376043,28.5598306 14,27.8452995 L6.74166975,23.6547005 C5.50406544,22.9401694 4.74166975,21.6196613 4.74166975,20.1905989 L4.74166975,11.8094011 C4.74166975,10.3803387 5.50406544,9.05983064 6.74166975,8.34529946 L14,4.15470054 C15.2376043,3.44016936 16.7623957,3.44016936 18,4.15470054 Z" id="多边形" fill="#FFFFFF"></path>
                        <g id="椭圆形">
                            <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                            <use fill="#007EFF" fill-rule="evenodd" xlink:href="#path-1"></use>
                        </g>
                        <rect x="0" y="0" width="32" height="32"></rect>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>