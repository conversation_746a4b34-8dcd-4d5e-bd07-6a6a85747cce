<template>
    <el-dialog :before-close="doBeforeClose" :close-on-click-modal="false"
               :title="title"
               :visible.sync="dialogShow" append-to-body class="check-prepay-card-dialog">
        <div class="wrap">
            <div class="flex-wrap" style="    height: 450px;
    overflow: auto;">
                <div class="item" v-for="(item,index) in prePayCards" :key="index">
                    <div class="content" style="font-size: 16px;font-weight: 500;margin: 20px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;text-align: left">
                        
                        <el-row>
                            <el-col :span="16">
                                <el-tooltip :content="item.name" placement="top" effect="light">
                                    <div class="text-hidden">{{item.name}}</div>
                                </el-tooltip>
                            </el-col>
                            <el-col :span="8">
                                <span class="inner" style="background-color: #ff9933;text-align: center" v-if="item.state === 'UNACTIVATED'">{{formatI18n('/会员/会员资料', '未激活')}}</span>
                                <span class="inner" style="background-color: #36f;text-align: center" v-if="item.state === 'PRESENTING'">{{formatI18n('/营销/券礼包活动/券查询/券状态下拉选项', '转赠中')}}</span>
                                <span class="inner"
                                    style="background-color: #3c0;;text-align: center"
                                    v-if="item.state === 'USING'">{{formatI18n('/会员/会员资料', '使用中')}}</span>
                                <span class="inner"
                                    style="background-color: #b7b7b7;;text-align: center" v-if="item.state === 'CANCELLED'">{{formatI18n('/营销/券礼包活动/券查询/券状态下拉选项', '已作废')}}</span>
                                <span class="inner"
                                    style="background-color: #7f36c1;text-align: center"
                                    v-if="item.state === 'USED'">{{formatI18n('/营销/券礼包活动/券查询/券状态下拉选项', '已使用')}}</span>
                                <span class="inner"
                                    style="background-color: #ffcc33;text-align: center"
                                    v-if="item.state === 'LOST'">{{formatI18n('/会员/会员资料/详情界面/会员资产/如果有预付卡的点击预付卡的数字', '已挂失')}}</span>
                                <span class="inner"
                                    style="background-color: #CC0033;text-align: center"
                                    v-if="item.state === 'FROZEN'">{{formatI18n('/会员/会员资料', '已冻结')}}</span>
                            </el-col>
                        </el-row>
                    </div>
                    <div class="content">
                        <div style="text-align: left;padding-left: 20px">{{formatI18n('/会员/会员资料/详情界面/会员资产/如果有预付卡的点击预付卡的数字', '卡号')}}：{{item.code}}</div>
                        <div style="text-align: left;padding-left: 20px">{{formatI18n('/会员/会员资料/详情界面/会员资产/如果有预付卡的点击预付卡的数字', '有效期至')}}：{{item.expireDate | dateFormate2}}
                            <span style="color: green" v-if="!item.expired">{{formatI18n('/会员/会员资料/详情界面/会员资产/如果有预付卡的点击预付卡的数字', '未过期')}}</span>
                            <span style="color: red" v-else>{{formatI18n('/营销/券礼包活动/券查询/券状态下拉选项', '已过期')}}</span></div>
                        <div style="text-align: left;padding-left: 20px">{{formatI18n('/会员/会员资料/详情界面/会员资产/如果有预付卡的点击预付卡的数字', '余额')}}：{{item.balance}}</div>
                        <div style="text-align: left;padding-left: 20px">
                            <el-row>
                                <el-col :span="16">
                                    <el-tooltip :content="item.issuedOrg" placement="bottom" effect="light">
                                        <div class="text-hidden">
                                            {{formatI18n('/会员/会员资料/详情界面/会员资产/如果有预付卡的点击预付卡的数字', '发卡门店')}}：
                                            {{item.issuedOrg}}
                                        </div>
                                    </el-tooltip>
                                </el-col>
                                <el-col :span="8">
                                    <span class="inner" style="background-color: #b7b7b7;;text-align: center" v-if="item.defaultCard === true">{{formatI18n('/会员/会员资料/详情界面/会员资产/如果有预付卡的点击预付卡的数字', '默认卡')}}</span>
                                </el-col>
                            </el-row>
                        </div>
                        <!-- <div style="text-align: left;padding-left: 20px">余额：{{item.balance}}</div>
                        <div style="text-align: left;padding-left: 20px">发卡门店：{{item.issuedOrg}}
                            <span class="inner" style="background-color: #b7b7b7;;text-align: center" v-if="item.defaultCard === true">默认卡</span>
                        </div> -->
                    </div>
                </div>
            </div>
        </div>
        <div class="dialog-footer" slot="footer">
            <el-button @click="doCancel">{{formatI18n('/公用/按钮', '取消')}}</el-button>
            <el-button @click="doModalClose" size="small" type="primary">{{formatI18n('/公用/按钮', '确定')}}</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts" src="./CheckPrePayCardDialog.ts">
</script>

<style lang="scss" scoped>
    .check-prepay-card-dialog{
        display: flex;
        align-items: center;
        justify-content: center;
        .wrap{
            // height: 445px;
            .item{
                width: 280px;
                // height: 108px;
                padding-bottom: 20px;
                border-radius: 5px;
                display: inline-block;
                margin-bottom: 24px;
                background: rgba(0, 0, 0, 0.0470588235294118);
                margin-right: 10px;
                .content{
                    text-align: center;
                    .inner{
                        display: inline-block;
                        width: 80px;
                        height: 20px;
                        font-size: 14px;
                        color: white
                    }
                }
            }
        }
        ::v-deep .el-dialog{
            width: 660px !important;
            height: 600px !important;
        }
        .text-hidden{
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
</style>