import {Component, Prop, Vue, Watch} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import BrandApi from 'http/brand/BrandApi'
import RSBrandFilter from 'model/common/RSBrandFilter'
import GoodsApi from 'http/goods/GoodsApi'
import RSGoodsFilter from 'model/common/RSGoodsFilter'
import CategoryApi from 'http/category/CategoryApi'
import RSCategoryFilter from 'model/common/RSCategoryFilter'

@Component({
    name: 'SelectGoodsRangeDialog',
    components: {
        FormItem
    }
})
export default class SelectGoodsRangeDialog extends Vue {
    key = ''
    idNameLikes = ''
    selected: any[] = []
    datas: any[] = []
    // curType = '品牌'
    $refs: any
    // 分页
    page = {
        currentPage: 1,
        total: 0,
        size: 10
    }
    @Prop()
    data: any

    @Prop({
        type: Boolean,
        default: false
    })
    dialogShow: boolean

    @Prop()
    type: string

    get getTitle() {
        if (this.type === this.formatI18n('/公用/券模板', '品牌')) {
            return this.formatI18n('/公用/券模板', '设置用券品牌')
        } else if (this.type === this.formatI18n('/公用/券模板', '品类')) {
            return this.formatI18n('/公用/券模板', '设置用券品类')
        } else {
            return this.formatI18n('/公用/券模板', '设置用券单品')
        }

    }

    @Watch('dialogShow')
    onDialogShowChange(value: string) {
        // this.curType = this.type
        this.idNameLikes = ''
        this.key = ''
        if (value && this.data) {
            this.requestTransfer()
        }
    }
    doSearch() {
        this.page.currentPage = 1
        this.requestTransfer()
    }
    doReset() {
        this.page.currentPage = 1
        this.idNameLikes = ''
        this.key = ''
        this.requestTransfer()
    }
    doBeforeClose(done: any) {
        this.$emit('dialogClose')
        done()
    }
    doModalClose() {
        this.$emit('summit', this.selected, this.type)
        this.$emit('dialogClose')
    }
    handleSelectionChange(val: any) {
        this.selected = val
    }
    /**
     * 分页页码改变的回调
     * @param val
     */
    onHandleCurrentChange(val: number) {
        this.page.currentPage = val
        this.requestTransfer()
    }
    /**
     * 每页多少条的回调
     * @param val
     */
    onHandleSizeChange(val: number) {
        this.page.size = val
        this.requestTransfer()
    }
    /**
     * 品牌
     */

    private getBrand() {
        let query: RSBrandFilter = new RSBrandFilter()
        query.key = this.idNameLikes ? this.idNameLikes : null
        query.page = this.page.currentPage - 1
        query.pageSize = this.page.size
        query.brandIdOrder = true
        BrandApi.query(query).then((resp: any) => {
            if (resp && resp.code === 2000) {
                let arr: any = []
                if (resp.data && resp.data.length > 0) {
                    for (let i = 0; i < resp.data.length; i++) {
                        let obj = {
                            brand: {
                                id: resp.data[i].brand.id,
                                name: resp.data[i].brand.name
                            }
                        }
                        arr.push(obj)
                        if (resp.data[i].children && resp.data[i].children.length > 0) {
                            for (let j = 0; j < resp.data[i].children.length; j++) {
                                let obj = {
                                    brand: {
                                        id: resp.data[i].children[j].brand.id,
                                        name: resp.data[i].children[j].brand.name
                                    }
                                }
                                arr.push(obj)
                            }
                        }
                    }
                }
                this.datas = arr
                this.page.total = resp.total
                this.setSeleted()
            }
        }).catch((error) => {
            if (error && error.message) {
                this.$message.error(error.message)
            }
        })
    }
    /**
     * 单品
     */
    private getSingle() {
        let query: RSGoodsFilter = new RSGoodsFilter()
        query.key = this.idNameLikes ? this.idNameLikes : null
        query.page = this.page.currentPage - 1
        query.pageSize = this.page.size
        query.codeOrder = true
        GoodsApi.query(query).then((resp: any) => {
            if (resp && resp.code === 2000) {
                this.datas = resp.data
                this.page.total = resp.total
                this.setSeleted()
            }
        }).catch((error) => {
            if (error && error.message) {
                this.$message.error(error.message)
            }
        })
    }
    /**
     * 品类
     */
    private getType() {
        let query: RSCategoryFilter = new RSCategoryFilter()
        query.key = this.key ? this.key : null
        query.page = this.page.currentPage - 1
        query.pageSize = this.page.size
        CategoryApi.query(query).then((resp: any) => {
            if (resp && resp.code === 2000) {
                this.datas = resp.data
                this.page.total = resp.total
                this.setSeleted()
            }
        }).catch((error) => {
            if (error && error.message) {
                this.$message.error(error.message)
            }
        })
    }
    private requestTransfer() {
       if (this.type === this.formatI18n('/公用/券模板', '品牌')) {
            this.getBrand()
        } else if (this.type === this.formatI18n('/公用/券模板', '单品')) {
            this.getSingle()
        } else if (this.type === this.formatI18n('/公用/券模板', '品类')) {
            this.getType()
        } else {
            // todo
        }
    }
    private setSeleted() {
        let that = this
        setTimeout(() => {
            if (that.data && that.data.length > 0) {
                that.datas.forEach((item: any) => {
                    that.data.forEach((subItem: any) => {
                        if (this.type === this.formatI18n('/公用/券模板', '品牌')) {
                            if (item.brand.id === subItem.id) {
                                if (that.$refs.curTable) {
                                    that.$refs.curTable.toggleRowSelection(item, true)
                                }
                            }
                        }
                        if (this.type === this.formatI18n('/公用/券模板', '单品')) {
                            if (item.barcode === subItem.id) {
                                if (that.$refs.curTable) {
                                    that.$refs.curTable.toggleRowSelection(item, true)
                                }
                            }
                        }
                        if (this.type === this.formatI18n('/公用/券模板', '品类')) {
                            if (item.category.id === subItem.id) {
                                if (that.$refs.curTable) {
                                    that.$refs.curTable.toggleRowSelection(item, true)
                                }
                            }
                        }
                    })
                })
            }

        }, 100)

    }
}