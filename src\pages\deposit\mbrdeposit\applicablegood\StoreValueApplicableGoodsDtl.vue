<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:03
 * @LastEditTime: 2024-04-24 14:27:41
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\mbrdeposit\applicablegood\StoreValueApplicableGoodsDtl.vue
 * 记得注释
-->
<template>
  <div class="grow-value-rule-dtl">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button @click="doModify" type="primary" v-if="hasOptionPermission('/储值/储值管理/储值适用商品设置', '账户信息维护')">
          {{ formatI18n('/公用/按钮', '修改') }}
        </el-button>
        <!--<el-button @click="doBack">取消</el-button>-->
      </template>
    </BreadCrume>
    <div style="overflow: auto;height: 95%">
      <el-row v-loading="loading">
        <div style="margin: 20px">
          <el-col :span="3" class="text-secondary" :title="formatI18n('/储值/会员储值/储值适用商品设置/适用商品：')">{{ formatI18n('/储值/会员储值/储值适用商品设置/适用商品：') }}</el-col>
          <el-col :span="12">
            <GoodsScopeDtl :showAll="true" :goodsMatchRuleMode="goodsMatchRuleMode" :goods="data.goodsRange"/>
          </el-col>
        </div>
      </el-row>
      <el-row v-loading="loading">
        <div style="margin: 20px">
          <el-col :span="3" class="text-secondary" :title="i18n('储值使用须知')">{{ i18n('储值使用须知') }}：</el-col>
          <el-col :span="12">
            <template v-if="data.remark">{{data.remark}}</template>
            <template v-else>-</template>
          </el-col>
        </div>
      </el-row>
    </div>

  </div>
</template>

<script lang="ts" src="./StoreValueApplicableGoodsDtl.ts">
</script>

<style lang="scss">
  .grow-value-rule-dtl {
    background-color: white;
    height: 100%;
    width: 100%;
    overflow: hidden;

    .goods-range .content {
      width: 900px;
    }

    .text-secondary {
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      color: rgba(51, 51, 51, 0.65);
    }

    .goods-scope {
      margin-bottom: 15px;

      .el-row {
        padding: 15px;
      }
    }
  }
</style>