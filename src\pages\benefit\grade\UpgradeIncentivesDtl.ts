import {Component, Vue} from 'vue-property-decorator'
import BreadCrume from 'cmp/bread-crumb/BreadCrume'
import I18nPage from "common/I18nDecorator";
import TradeGiftGradeApi from "http/tradegiftgrade/TradeGiftGradeApi";
import TradeGiftGradeActivity from "model/tradegiftgrade/TradeGiftGradeActivity";
import RSGrade from "model/common/RSGrade";
import MemberBalancePromotionApi from "http/payment/member/MemberBalancePromotionApi";
import UpgradeIncentivesPermissions from "pages/benefit/grade/UpgradeIncentivesPermissions";

@Component({
  name: 'UpgradeIncentives',
  components: {
    BreadCrume
  }
})
@I18nPage({
  prefix: [
    '/权益/等级/单笔消费升级激励/详情页',
    '/权益/等级/单笔消费升级激励/编辑页',
    '/公用/按钮',
  ]
})
export default class UpgradeIncentivesDtl extends Vue {
  i18n: I18nFunc
  data: TradeGiftGradeActivity = new TradeGiftGradeActivity()
  gradeList: RSGrade[] = []
  gradeMap: any = {}
  panelArray: any = []
  loading = {
    tradeGift: false,
    grade: false,
  }
  permissions = new UpgradeIncentivesPermissions()

  get anyLoading() {
    return this.loading.tradeGift || this.loading.grade
  }

  created() {
    this.panelArray = [
      {
        name: this.formatI18n("/公用/菜单", "消费有礼"),
        url: "coupon-active-manage",
      },
      {
        name: this.i18n('单笔消费升级激励'),
        url: ''
      }
    ]
    this.loadData()
  }

  loadData() {
    this.loading.tradeGift = true
    TradeGiftGradeApi.get().then((res: any) => {
      if (res.code === 2000 && res.data) {
        this.data = res.data
      } else {
        throw new Error(res.msg)
      }
    }).catch((e: any) => {
      this.$message.error(e.message)
    }).finally(() => {
      this.loading.tradeGift = false
    })

    this.loading.grade = true
    MemberBalancePromotionApi.gradeList().then((res: any) => {
      if (res.code === 2000 && res.data) {
        this.gradeMap = {}
        for (let grade of res.data) {
          if (grade && grade.code) {
            this.gradeMap[grade.code] = grade
          }
        }
      }
    }).catch((e: any) => {
      this.$message.error(e.message)
    }).finally(() => {
      this.loading.grade = false
    })
  }

  modify() {
    this.$router.push({name: 'upgrade-incentives'})
  }

  disable() {
    this.$alert(this.i18n('请确认是否禁用单笔消费升级激励？'), this.i18n('提示'), {
      showCancelButton: true,
      confirmButtonText: this.i18n('确认禁用'),
      cancelButtonText: this.i18n('取消'),
      confirmButtonClass: 'upgrade-incentives-dtl-red-button',
      type: 'warning',
      callback: (action) => {
        if (action === 'confirm') {
          TradeGiftGradeApi.switchRule(true).then((res: any) => {
            this.$message.success(this.i18n('禁用成功'))
            this.data.enable = false
          }).catch((e: any) => {
            this.$message.error(e.message)
          })
        }
      }
    });
  }

  enable() {
    this.$alert(this.i18n('请确认是否启用单笔消费升级激励？'), this.i18n('提示'), {
      showCancelButton: true,
      confirmButtonText: this.i18n('确认启用'),
      confirmButtonClass: 'upgrade-incentives-dtl-green-button',
      cancelButtonText: this.i18n('取消'),
      type: 'warning',
      callback: (action) => {
        if (action === 'confirm') {
          TradeGiftGradeApi.switchRule(false).then((res: any) => {
            this.$message.success(this.i18n('启用成功'))
            this.data.enable = true
          }).catch((e: any) => {
            this.$message.error(e.message)
          })
        }
      }
    });
  }
}