<template>
  <el-form label-position="top" :model="from" :rules="rules" ref="form" class="upload-img">
    <el-form-item :label="label" prop="imgValue" label-width="100px" :rules="{ required: true, message: i18n('请上传图片'), trigger: ['change', 'blur'] }">
      <div class="tips">
        {{ config.prefixDescribe }}
      </div>
      <div class="uploader-imgbox">
        <div class="up-box">
          <div class="imgbox-wrap" :class="{ 'circular-box': isCircular }" v-if="imgValue">
            <img class="imgbox-img" :src="imgValue" alt="" />
            <div class="imgbox-delete">
              <div @click="deleteImg">{{ i18n('删除') }}</div>
              <div @click="replaceImg">{{ i18n('替换') }}</div>
            </div>
          </div>
          <!-- v-show="!imgValue" -->
          <el-upload
            v-show="!imgValue"
            v-model="from.imgValue"
            :class="{ 'circular-style': isCircular }"
            :headers="uploadHeaders"
            :action="uploadUrl"
            :with-credentials="true"
            :show-file-list="false"
            :on-success="onImageUploadSuccess"
            :before-upload="beforeAvatarUpload"
            ref="upload"
          >
            <div class="uploader-box">
              <i class="el-icon-plus avatar-uploader-icon"></i>
            </div>
          </el-upload>

          <page-jump ref="jumpPage" v-model="JumpPageInfo" :advertiseChannel="advertiseChannel" @change="changeLink" v-if="!imgUrl.propItems">
            <div style="font-weight: 500; font-size: 14px; color: #24272b; line-height: 16px;text-indent:16px;margin-bottom:8px" v-if="!showIndex">
              {{ i18n('图片') }}{{ index + 1 }}
            </div>
          </page-jump>
        </div>
      </div>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" src="./UploadImg.ts">
</script>

<style lang="scss">
.el-form--label-top .el-form-item__label {
  padding: 0;
}
.upload-img {
  .tips {
    // margin-top: 40px;
    // margin-left: -20px;
    font-weight: 400;
    font-size: 13px;
    color: #a1a6ae;
    line-height: 18px;
  }
}
.uploader-imgbox {
  display: flex;
  flex-wrap: wrap;

  .imgbox-wrap {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: 4px;
    overflow: hidden;
    margin-right: 12px;
    margin-bottom: 12px;

    .imgbox-img {
      width: 80px;
      height: 80px;
    }

    .imgbox-delete {
      display: none;
      position: absolute;
      width: 80px;
      height: 80px;
      top: 0;
      left: 0;
      background: rgba(0, 0, 0, 0.5);
      text-align: center;
      line-height: 80px;
      color: white;
      cursor: pointer;
      user-select: none;
    }

    &:hover {
      .imgbox-delete {
        display: flex;
        justify-content: space-around;
      }
    }
  }
  .uploader-box {
    position: relative;
    border: 1px solid #d7dfeb;
    border-radius: 4px;

    &:hover {
      border: 1px dashed #409eff;
      .avatar-uploader-icon {
        color: #409eff;
      }
    }
    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 80px;
      height: 80px;
      line-height: 80px;
      text-align: center;
    }
  }
  .circular-style {
    .uploader-box {
      border-radius: 50%;
      overflow: hidden;
    }
  }
  .up-box {
    width: 100%;
    background-color: #f0f2f6;
    padding: 12px;
    display: flex;
    flex-wrap: nowrap;
    .right-link {
      cursor: pointer;
      margin-left: 16px;
      display: flex;
      font-weight: 400;
      font-size: 13px;
      line-height: 18px;
      height: 18px;
      .link-tips {
        color: #5a5f66;
      }
      .link-name {
        color: #007eff;
        margin-left: 12px;
      }
    }
  }
}
.circular-box {
  .imgbox-img {
    border-radius: 50%;
    overflow: hidden;
  }
  .imgbox-delete {
    border-radius: 50%;
    overflow: hidden;
  }
}
</style>