import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import ChannelSelect from 'cmp/channelselect/ChannelSelect';
import I18nPage from 'common/I18nDecorator';
import { Component, Vue } from 'vue-property-decorator';
import Channel from "model/common/Channel";
import AutoFixInput from 'cmp/autofixinput/AutoFixInput';
import BPrepayCardRechargeLimit from 'model/prepay/rechargeLimit/BPrepayCardRechargeLimit';
import CardRechargeLimitRule from 'model/prepay/rechargeLimit/CardRechargeLimitRule';
import PrepayCardRechargeLimitApi from 'http/card/rechargeLimit/PrepayCardRechargeLimitApi';
import BPrepayCardRechargeLimitCreateRequest from 'model/prepay/rechargeLimit/BPrepayCardRechargeLimitCreateRequest';
import CommonUtil from 'util/CommonUtil';

//页面使用
class FormItem {
  rechargeChannels: Channel[] = []  //充值渠道
  hasCardholder: boolean = true //是否有持卡人
  singleRechargeLimit: any = {
    minLimit: {
      hasMinLimit: false, //是否有最小充值金额
      minAmount: null,  //最小充值金额
    },
    maxLimit: {
      hasMaxLimit: false, //是否有最大充值金额
      maxAmount: null //最大充值金额
    }
  }
}


@Component({
  name: 'CardRechargeLimitEdit',
  components: {
    BreadCrume,
    ChannelSelect,
    AutoFixInput
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/卡/卡充值限额设置',
    '/公用/表单校验',
    '/公用/按钮'
  ],
  auto: true
})
export default class CardRechargeLimitEdit extends Vue {
  $refs: any
  panelArray: any = []
  editType: 'create' | 'edit' = 'create'
  ruleForm: any = {
    data: [new FormItem()]
  }
  rules: any = {}

  created() {
    this.editType = this.$route.query.editType === 'edit' ? 'edit' : 'create'
    if (this.editType === 'create') {
      this.panelArray = [
        {
          name: this.i18n('/公用/菜单/卡充值限额设置'),
          url: ''
        }
      ]
    } else {
      this.panelArray = [
        {
          name: this.i18n('/公用/菜单/卡充值限额设置'),
          url: 'card-recharge-limit-dtl'
        },
        {
          name: this.i18n('修改卡充值限额设置'),
          url: ''
        },
      ]
      this.getDetail()
    }
    this.initRules()
  }

  doAdd() {
    this.ruleForm.data.push(new FormItem())
  }

  doRemove(index: number) {
    this.ruleForm.data.splice(index, 1)
  }

  //其他组已经选择了的充值渠道
  selectedChannels(index: number) {
    const arr: string[] = []
    this.ruleForm.data.forEach((item: FormItem, ind: number) => {
      if (index == ind) return  //当前组已经选择的，不用过滤
      if (item.hasCardholder !== this.ruleForm.data[index].hasCardholder) return
      item.rechargeChannels.forEach((channel) => arr.push(channel.type! + channel.id!))
    })
    return arr
  }

  doSave() {
    this.$refs.form.validate().then(() => {
      const params = this.doParams(this.ruleForm.data)
      PrepayCardRechargeLimitApi.saveNew(params).then((res) => {
        if (res.code === 2000) {
          this.$message.success(this.i18n('保存成功'))
          this.doToDtl()
        } else {
          throw new Error(res.msg!)
        }
      }).catch((error) => this.$message.error(error.message))
    })
  }

  doCancel() {
    this.doToDtl()
  }

  doToDtl() {
    this.$router.push({
      name: 'card-recharge-limit-dtl'
    })
  }

  getDetail() {
    PrepayCardRechargeLimitApi.query().then((res) => {
      if (res.code === 2000) {
        this.doBindValue(res.data!)
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => this.$message.error(error.message))
  }

  //在切换“是否有持卡人”字段时，如果其他设置组的“充值渠道”存在与当前组“充值渠道”重复的已选项（且“是否有持卡人”字段也相同），则需要清空当前充值渠道
  doCardholderChange(index: number) {
    const currentChannel = this.ruleForm.data[index].rechargeChannels
    const flag = this.ruleForm.data.some((item: FormItem, ind: number) => {
      if (index === ind || item.hasCardholder !== this.ruleForm.data[index].hasCardholder) return false
      if (item.rechargeChannels.some((val) => { //判断是否有重复的充值渠道
        return currentChannel.some((v: Channel) => {
          return (val.id! + val.type!) === (v.id! + v.type!)
        })
      })) {
        return true
      }
    })
    if (flag) {
      this.$message.warning(this.i18n('"是否有持卡人"相同时，充值渠道不能与其他设置重复'))
      this.ruleForm.data[index].rechargeChannels = []
    }
  }

  doCheckLimitChange(index: number, mode: 'min' | 'max') {
    if (mode === 'max' && !this.ruleForm.data[index].singleRechargeLimit.maxLimit.hasMaxLimit) {
      this.ruleForm.data[index].singleRechargeLimit.maxLimit.maxAmount = null
    }
    if (mode === 'min' && !this.ruleForm.data[index].singleRechargeLimit.minLimit.hasMinLimit) {
      this.ruleForm.data[index].singleRechargeLimit.minLimit.minAmount = null
    }
    this.$refs.form.validateField(`data[${index}].singleRechargeLimit`)
    this.$refs.form.validateField(`data[${index}].singleRechargeLimit.maxLimit`)
    this.$refs.form.validateField(`data[${index}].singleRechargeLimit.minLimit`)
  }

  doBindValue(value: BPrepayCardRechargeLimit[]) {
    const loading = CommonUtil.Loading()
    this.ruleForm.data = []
    value.forEach((item) => {
      const ruleFormItem = new FormItem()
      ruleFormItem.rechargeChannels = item.body?.rechargeChannels as any || []
      ruleFormItem.hasCardholder = item.body?.existCardholder === false ? false : true
      if (item.body?.minAmount) {
        ruleFormItem.singleRechargeLimit.minLimit.hasMinLimit = true
        ruleFormItem.singleRechargeLimit.minLimit.minAmount = item.body.minAmount
      }
      if (item.body?.maxAmount) {
        ruleFormItem.singleRechargeLimit.maxLimit.hasMaxLimit = true
        ruleFormItem.singleRechargeLimit.maxLimit.maxAmount = item.body.maxAmount
      }
      this.ruleForm.data.push(ruleFormItem)
    })
    loading.close()
  }

  doParams(value: FormItem[]) {
    const params: BPrepayCardRechargeLimitCreateRequest = new BPrepayCardRechargeLimitCreateRequest()
    value.forEach((item, index) => {
      const resItem = new BPrepayCardRechargeLimit()
      resItem.sequence = index
      resItem.body = new CardRechargeLimitRule()
      resItem.body.rechargeChannels = item.rechargeChannels.map((val) => {
        return {
          id: val.id,
          type: val.type
        }
      })
      resItem.body.existCardholder = item.hasCardholder
      resItem.body.minAmount = item.singleRechargeLimit.minLimit.hasMinLimit ? item.singleRechargeLimit.minLimit.minAmount : null
      resItem.body.maxAmount = item.singleRechargeLimit.maxLimit.hasMaxLimit ? item.singleRechargeLimit.maxLimit.maxAmount : null
      params.list.push(resItem)
    })
    return params
  }

  initRules() {
    this.rules = {
      rechargeChannels: {
        validator: (rule: any, value: Channel[], callback: any) => {
          if (!value?.length) {
            callback(new Error(this.i18n("请填写必填项")));
          }
          callback();
        },
        required: true,
        trigger: ["change", "blur"],
      },
      singleRechargeLimit: {
        validator: (rule: any, value: any, callback: any) => {
          if (!value.minLimit.hasMinLimit && !value.maxLimit.hasMaxLimit) {
            callback(new Error(this.i18n("请至少勾选一种限额")));
          }
          if (value.minLimit.hasMinLimit && value.maxLimit.hasMaxLimit && Number(value.minLimit.minAmount) > Number(value.maxLimit.maxAmount)) {
            callback(new Error(this.i18n('最小金额不能大于最大金额')))
          }
          callback();
        },
        required: true,
        trigger: ["change", "blur"],
      },
      minAmount: {
        validator: (rule: any, value: any, callback: any) => {
          if (value.hasMinLimit && !value.minAmount) {
            callback(new Error(this.i18n("请填写必填项")))
          }
          callback();
        },
        trigger: ["change", "blur"],
      },
      maxAmount: {
        validator: (rule: any, value: any, callback: any) => {
          if (value.hasMaxLimit && !value.maxAmount) {
            callback(new Error(this.i18n("请填写必填项")))
          }
          callback();
        },
        trigger: ["change", "blur"],
      }
    }
  }
};