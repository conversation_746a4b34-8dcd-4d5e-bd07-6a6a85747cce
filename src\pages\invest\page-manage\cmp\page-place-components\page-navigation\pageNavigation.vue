<!--
 * @Author: haiding <EMAIL>
 * @Date: 2025-02-19 10:03:52
 * @LastEditors: haiding <EMAIL>
 * @LastEditTime: 2025-03-13 13:06:27
 * @FilePath: \new-kequn\src\pages\invest\page-manage\cmp\page-place-components\page-navigation\pageNavigation.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
<div style="position: relative;">
    <div
    class="page-navigation"
    :style="{
      backgroundColor: componentItemProps.styNavigationBgColor,
      padding:
        componentItemProps.styMarginTop +
        'px ' +
        componentItemProps.styMarginRight +
        'px ' +
        componentItemProps.styMarginBottom +
        'px ' +
        componentItemProps.styMarginLeft +
        'px',
    }"
    :class="[{ activeCom: activeIndex === index }]"
    @click="activeTemplate"
  >
    <div class="toolBar" v-if="activeIndex === index">
      <setting-toolbar :btns="toolBarBtns" @click="toolbarClick"></setting-toolbar>
    </div>
    <div class="tabs">
      <div
        v-for="(item, index) in localProperty"
        :key="item.componentUuid ? item.componentUuid : item.id"
        class="tabs-item"
        @click="handleClick(index)"
        :style="{
          color: index === tabsActiveIndex ? componentItemProps.styActiveColor : componentItemProps.styColor,
          fontSize: index === tabsActiveIndex && componentItemProps.propEnlargeActiveFont ? '16px' : '14px',
          backgroundColor:
            index === tabsActiveIndex && componentItemProps.propActiveStyle === 'background' ? componentItemProps.styActiveBgColor : '',
          backgroundImage: item.navigationStyle === 'image' ? `url(${item.image})` : '',
          width: item.navigationStyle === 'image' ? '158rpx' : '',
          height: item.navigationStyle === 'image' ? '50rpx' : '',
          backgroundSize: '100%,100%',
        }"
      >
        <span>
          {{ item.name ? item.name : `${i18n('导航名称')}${index + 1}` }}
        </span>
        <div
          class="underLine"
          :style="{
            backgroundColor: index === tabsActiveIndex ? componentItemProps.styActiveUnderlineColor : '',
          }"
        ></div>
      </div>
      <!-- <el-tabs @tab-click="handleClick">
        <el-tab-pane
          v-for="(item, index) in localProperty"
          :key="item.componentUuid ? item.componentUuid : item.id"
          :label="item.name ? item.name : `导航名称${index + 1}`"
        ></el-tab-pane>
      </el-tabs> -->
    </div>
  </div>
</div>
</template>

<script lang="ts" src="./pageNavigation.ts"></script>

<style lang="scss" scoped>
.page-navigation {
  overflow-x: auto;
  .toolBar {
    position: absolute;
    top: 0;
    right: 1px;
    z-index: 999;
    cursor: pointer;
  }
  .nav-2 {
    .el-tabs__nav::v-deep {
      width: 100%;
      .el-tabs__active-bar {
        width: 50% !important ;
      }
      .el-tabs__item {
        width: 50%;
        text-align: center;
      }
    }
  }
  .nav-3 {
    .el-tabs__nav::v-deep {
      width: 100%;
      .el-tabs__active-bar {
        width: 25% !important ;
      }
      .el-tabs__item {
        width: 33.33%;
        text-align: center;
      }
    }
  }
  .nav-4 {
    .el-tabs__nav::v-deep {
      width: 100%;
      .el-tabs__active-bar {
        width: 20% !important ;
      }
      .el-tabs__item {
        width: 25%;
        text-align: center;
      }
    }
  }
}
.tabs {
  display: flex;
  line-height: 50px;
  height: 50px;
  font-size: 14px;
  text-align: center;
  .tabs-item {
    flex-shrink: 0;
    height: 50px;
    position: relative;
    font-weight: 600;
    margin: 0 5px;
  }
}
.underLine {
  width: 50%;
  height: 2px;
  position: absolute;
  bottom: 10px;
  left: 50%;
  z-index: 10;
  transform: translate(-50%);
}
</style>