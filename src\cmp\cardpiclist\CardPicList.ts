import {Component, Prop, Vue} from 'vue-property-decorator'


@Component({
    name: 'CardPicList',
    components: {}
})
export default class CardPicList extends Vue {
    @Prop({
        type: Boolean,
        default: false
    })
    readonly: boolean
    @Prop()
    picList: []

    private del(index: number) {
        this.picList.splice(index, 1)
        this.$emit('delete')
    }
    private forward(index: number) {
        if (index === 0) {
            return
        }
        let temp = this.picList[index - 1]
        this.picList[index - 1] = this.picList[index]
        this.picList[index] = temp
        this.$forceUpdate()
    }
}