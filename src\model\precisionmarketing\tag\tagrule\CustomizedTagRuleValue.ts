import GoodsMetricRule from 'model/precisionmarketing/tag/tagrule/GoodsMetricRule'
import LastMetricRule from 'model/precisionmarketing/tag/tagrule/customize/last/LastMetricRule'
import MemberRule from 'model/precisionmarketing/tag/tagrule/customize/member/MemberRule'
import SaleMetricRule from 'model/precisionmarketing/tag/tagrule/SaleMetricRule'

export default class CustomizedTagRuleValue {
  // 标签值
  tagValue: Nullable<string> = null
  // 会员属性条件
  memberRule: Nullable<MemberRule> = null
  // 周期内商品消费条件
  goodsMetricRule: Nullable<GoodsMetricRule> = null
  // 周期内消费条件
  saleMetricRule: Nullable<SaleMetricRule> = null
  // 最后消费条件
  lastRule: Nullable<LastMetricRule> = null
  // 条件连接符
  connective: Nullable<string> = null
}