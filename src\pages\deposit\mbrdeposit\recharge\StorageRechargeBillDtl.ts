/*
 * @Author: 黎钰龙
 * @Date: 2024-07-16 15:23:37
 * @LastEditTime: 2024-07-22 10:41:51
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\mbrdeposit\recharge\StorageRechargeBillDtl.ts
 * 记得注释
 */
import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import FormItem from 'cmp/formitem/FormItem';
import I18nPage from 'common/I18nDecorator';
import MemberBalanceRechargeBillApi from 'http/deposit/activity/MemberBalanceRechargeBillApi';
import BMemberBalanceRechargeBill from 'model/deposit/activity/BMemberBalanceRechargeBill';
import BMemberBalanceRechargeBillLine from 'model/deposit/activity/BMemberBalanceRechargeBillLine';
import BMemberBalanceRechargeBillLineFilter from 'model/deposit/activity/BMemberBalanceRechargeBillLineFilter';
import BMemberBalanceRechargeBillLog from 'model/deposit/activity/BMemberBalanceRechargeBillLog';
import BMemberBalanceRechargeBillLogFilter from 'model/deposit/activity/BMemberBalanceRechargeBillLogFilter';
import { Component, Vue } from 'vue-property-decorator';
import ImportStorageRechargeDialog from './import-storage-recharge-dialog/ImportStorageRechargeDialog';
import DownloadCenterDialog from 'pages/main/dowmload_center_dialog/DownloadCenterDialog';
import CommonUtil from 'util/CommonUtil';
@Component({
  name: 'StorageRechargeBillDtl',
  components: {
    BreadCrume,
    FormItem,
    ImportStorageRechargeDialog,
    DownloadCenterDialog
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/储值/会员储值/储值充值单'
  ],
  auto: true
})
export default class StorageRechargeBillDtl extends Vue {
  $refs: any
  dtl: BMemberBalanceRechargeBill = new BMemberBalanceRechargeBill()
  lines: BMemberBalanceRechargeBillLine[] = []  //充值明细
  logList: BMemberBalanceRechargeBillLog[] = [] //操作日志
  uploadDialogShow: boolean = false  //会员导入弹窗
  downloadShow: boolean = false //文件中心弹窗
  linePage: any = {
    currentPage: 1,
    total: 0,
    pageSize: 10
  }
  logPage: any = {
    currentPage: 1,
    total: 0,
    pageSize: 10
  }

  // 是否需要导入文件
  get isShouldImport() {
    return this.dtl.rechargeType === 'file' && this.dtl.state === 'INITIAL'
  }

  get panelArray() {
    return [
      {
        name: this.formatI18n('/公用/菜单/储值充值单'),
        url: 'storage-recharge-bill-list'
      },
      {
        name: this.i18n('储值充值单详情'),
        url: ''
      }
    ]
  }

  // get getImportCount() {
  //   const count = this.linePage.total
  //   let str: any = this.i18n('当前已导入{0}个会员，每个活动最多支持导入20000个会员。')
  //   str = str.replace(/\{0\}/g, ' ' + count + ' ')
  //   return str
  // }

  queryNew() {
    const loading = CommonUtil.Loading()
    this.getDtl().finally(() => {
      loading.close()
    })
  }

  created() {
    this.queryNew()
  }

  getDtl() {
    return MemberBalanceRechargeBillApi.detail(this.$route.query.billNumber as string).then((res) => {
      if (res.code === 2000) {
        this.dtl = res.data || new BMemberBalanceRechargeBill()
        this.queryRechargeLine()
        this.queryLogList()
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    })
  }

  // 查询充值明细
  queryRechargeLine() {
    const params = new BMemberBalanceRechargeBillLineFilter()
    params.page = this.linePage.currentPage - 1
    params.pageSize = this.linePage.pageSize
    params.numberEquals = this.dtl.billNumber
    params.memberIdIn = null as any
    return MemberBalanceRechargeBillApi.queryLines(params).then((res) => {
      if (res.code === 2000) {
        this.lines = res.data || []
        this.linePage.total = res.total || 0
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    })
  }

  // 查询操作日志列表
  queryLogList() {
    const params = new BMemberBalanceRechargeBillLogFilter()
    params.page = this.logPage.currentPage - 1
    params.pageSize = this.logPage.pageSize
    params.numberEquals = this.dtl.billNumber
    return MemberBalanceRechargeBillApi.queryLogs(params).then((res) => {
      if (res.code === 2000) {
        this.logList = res.data || []
        this.logPage.total = res.total || 0
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    })
  }

  // 审核
  doAudit() {
    if (this.isShouldImport && !this.linePage.total) {
      return this.$message.warning(this.i18n('请先导入充值会员'))
    }
    const loading = CommonUtil.Loading()
    MemberBalanceRechargeBillApi.audit(this.dtl.billNumber!).then((res) => {
      if (res.code === 2000) {
        this.$message.success(this.i18n('操作成功'))
        this.queryNew()
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    }).finally(() => {
      loading.close()
    })
  }

  // 修改
  doModify() {
    this.$router.push({
      name: 'storage-recharge-bill-edit',
      query: {
        billNumber: this.dtl.billNumber
      }
    })
  }

  // 删除
  doRemove() {
    this.$confirm(
      this.i18n("/会员/会员资料/确定删除吗？"),
      this.i18n("删除"),
      {
        confirmButtonText: this.formatI18n("/公用/按钮", "确定"),
        cancelButtonText: this.formatI18n("/公用/按钮", "取消"),
      }
    ).then(() => {
      const loading = CommonUtil.Loading()
      return MemberBalanceRechargeBillApi.remove(this.dtl.billNumber!).then((resp: any) => {
        if (resp.code === 2000) {
          this.$message.success(this.i18n('操作成功'))
          this.$router.push({
            name: 'storage-recharge-bill-list'
          })
        } else {
          this.$message.error(resp.msg)
        }
      }).catch((error) => {
        this.$message.error(error.message)
      }).finally(() => {
        loading.close()
      })
    });
  }

  // 导入充值会员
  doImport() {
    const params = {
      billNumber: this.dtl.billNumber
    }
    this.$refs.importStorageRechargeDialog.open(params)
  }

  uploadSuccess() {
    this.downloadShow = true
  }

  doDialogClose() {
    this.downloadShow = false
    this.queryNew()
  }

  /**
 * 分页页码改变的回调
 * @param val
 */
  onPageLineChange(val: number) {
    this.linePage.currentPage = val
    this.queryRechargeLine()
  }

  /**
   * 每页多少条的回调
   * @param val
   */
  onLineSizeChange(val: number) {
    this.linePage.pageSize = val
    this.queryRechargeLine()
  }

  /**
  * 分页页码改变的回调
  * @param val
  */
  onPageLogChange(val: number) {
    this.logPage.currentPage = val
    this.queryLogList()
  }

  /**
   * 每页多少条的回调
   * @param val
   */
  onLogSizeChange(val: number) {
    this.logPage.pageSize = val
    this.queryLogList()
  }
};