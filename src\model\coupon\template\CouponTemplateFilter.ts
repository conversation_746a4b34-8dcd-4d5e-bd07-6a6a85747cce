/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-03-21 14:20:47
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\coupon\template\CouponTemplateFilter.ts
 * 记得注释
 */
export default class CouponTemplateFilter {
	// 券名称类似于
	nameLikes: Nullable<string> = null;
	// 券模板号等于
	numberEquals: Nullable<string> = null;
	// 外部券模板号id等于
	outerNumberIdEquals: Nullable<string> = null;
	// 外部券模板号namespace等于
	outerNumberNamespaceEquals: Nullable<string> = null;
	// 券类型等于
	typeEquals: Nullable<string> = null;
	// 券类型包括
	typeIn: Nullable<string[]> = null;
	// 券模板状态等于
	stateEquals: Nullable<string> = null;
	// 券类型不包括
	typeExcluded: Nullable<string[]> = null;
	// 页码
	page: Nullable<number> = null;
	// 页面大小
	pageSize: Nullable<number> = null;
	//是否为弹框查询
	dialogQuery = false;
	// 用券说明类似于
	remarkLikes: Nullable<string> = null;
	// 创建人类似于
	creatorIdLikes: Nullable<string> = null;
	// 门店id
	storeLikes: Nullable<string> = null
	// 区域id
	zoneLikes: Nullable<string> = null
	// 商品id
	goodsLikes: Nullable<string> = null
	// 商品类型  CATEGORY 类别, GOODS  商品, BRAND 品牌
	goodsTypeEquals: Nullable<'CATEGORY' | 'GOODS' | 'BRAND'> = null
  //标签 等于(传标签uuid)
  mustHaveTagUuids: Nullable<string[]> = null
  // 是否允许海鼎发券
  allowHdIssueEquals: Nullable<boolean> = null
}
