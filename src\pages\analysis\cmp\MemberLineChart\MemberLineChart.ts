/*
 * @Author: 黎钰龙
 * @Date: 2024-03-01 11:04:58
 * @LastEditTime: 2025-01-15 13:33:33
 * @LastEditors: haiding <EMAIL>
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\analysis\cmp\MemberLineChart\MemberLineChart.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import Echart from 'echarts/lib/echarts'
import 'echarts/lib/chart/line'
import 'echarts/lib/component/legend'
import 'echarts/lib/component/tooltip';
import 'echarts/lib/component/grid';
// import 'echarts/lib/component/toolbox';
import DateUtil from 'util/DateUtil';
import DecimalFormatterUtil from 'util/DecimalFormatterUtil';
import { AnalysisReportDateUnit } from 'model/analysis/AnalysisReportDateUnit';
import AnalysisChartData from 'model/analysis/AnalysisChartData';


@Component({
  name: 'MemberLineChart',
  components: {}
})
@I18nPage({
  prefix: [
    '/公用/券模板'
  ],
  auto: true
})
export default class MemberLineChart extends Vue {
  @Prop({ type: Array }) legendNames: string[]; //数据名称
  @Prop({ type: Array }) xAxisArray: string[];  //横坐标分段点
  @Prop({ type: Array }) valueArray: any;  //图表数据
  @Prop({ default: 'DAY' }) dateType: AnalysisReportDateUnit;  //时间维度
  @Prop({ type: Array }) showPercentName: string[]; //显示百分比的数据名称列表

  $echarts: any
  options: any = {}
  lineChart: any

  @Watch('valueArray', { deep: true })
  handle(value: any) {  //如果数据有变化
    this.lineChart?.setOption({
      series: value.map((item: any) => this.getDataValue(item)),
      xAxis: { ...this.xAxisConfig, data: this.xAxisArray }
    }, {
      replaceMerge: ['series', 'xAxis']
    })
    setTimeout(() => {
      console.log('option看看', this.lineChart.getOption());
    }, 2000);
  }

  // 左右两个y轴共用配置
  get yAxisConfig() {
    return {
      axisLine: {
        lineStyle: {
          color: '#898FA3',
          opacity: 0
        }
      },
      type: 'value',
      splitLine: {
        lineStyle: {
          color: '#D7DFEB'
        }
      }
    }
  }


  // 横坐标分割间隔
  get getXInterval() {
    if (this.dateType === AnalysisReportDateUnit.DAY) {
      return 3600 * 24 * 1000
    } else if (this.dateType === AnalysisReportDateUnit.WEEK) {
      return 3600 * 24 * 1000 * 7
    } else {
      // return 3600 * 24 * 1000 * 30
      let startDate = new Date(this.xAxisArray[0]);
      let endDate = new Date(this.xAxisArray[this.xAxisArray.length - 1]);
      let intervals = [];
      let currentDate = new Date(startDate);
      while (currentDate < endDate) {
        let nextMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1);
        intervals.push(DateUtil.dateDiffInMillis(currentDate, nextMonth));
        currentDate = nextMonth;
      }
      // 最后一个月的间隔单独计算
      intervals.push(DateUtil.dateDiffInMillis(currentDate, endDate));
      return intervals;
    }
  }

  get xAxisConfig() {
    let interval = this.calculateInterval()
    return {
      type: this.xAxisArray.length === 1 ? 'category' : 'time',
      boundaryGap: false,
      data: this.xAxisArray,
      axisLabel: {
        formatter: (value: any) => {
          return this.formateDateStr(value)
        }
      },
      axisLine: {
        lineStyle: {
          color: '#898FA3'
        }
      },
      splitLine: {
        show: false
      },
      axisTick: {
        alignWithLabel: false
      },
      splitNumber: this.xAxisArray.length < 8 ? this.xAxisArray.length : 8, //横坐标点数量
      interval: interval,
    }
  }



  created() {
    this.$echarts = Echart
    this.options = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255,255,255,0.8)',
        textStyle: {
          color: '#36445A'
        },
        formatter: (params: any) => {
          let result = '';
          result += `<div>${this.formateDateStr(params[0].value[0])}</div>`
          params.forEach((item: any) => {
            const seriesName = item.seriesName
            let value: any = DecimalFormatterUtil.formatNumber(Number(item.value[1]),item.value[2])
            if (this.showPercentName.indexOf(item.seriesName) > -1) { //需要展示百分比的数据
              value = (value * 100).toFixed(2) + '%'
            }
            result += `<div style="display:flex;justify-content:space-between"><span>${item.marker}${seriesName}：</span>${value}</div>`
          });
          return result;
        }
      },
      legend: {
        left: 'center',
        bottom: '0',
        itemGap: 20,
        icon: 'roundRect',
        data: this.legendNames
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '16%',
        containLabel: true
      },
      xAxis: this.xAxisConfig,
      yAxis: [this.yAxisConfig, {
        ...this.yAxisConfig,
        axisLabel: {
          formatter: (value: any) => {
            return value * 100 + '%'
          }
        }
      }],
      series: this.valueArray.map((item: any) => this.getDataValue(item))
    }
  }

  mounted() {
    this.doDrawEchart()
  }


  calculateInterval() {
    let interval
    if (this.xAxisArray.length === 1) {
      interval = null
    } else {
      if (this.xAxisArray.length < 14) {
        interval = this.getXInterval
      }
    }
    return interval
  }

  //格式化时间字符串
  formateDateStr(val: any) {
    console.log('日期日期日期val', val);
    let xDate = new Date(val);

    console.log('-----0=0-0-======xDate', xDate, this.dateType);
    if (this.dateType === 'DAY') {
      return `${xDate.getFullYear()}-${xDate.getMonth() + 1}-${xDate.getDate()}`; //按日展示
    }
    else if (this.dateType === 'WEEK') {
      let str = this.i18n('/公用/日期/yyyy第WW周')
      str = str.replace(/yyyy/g, DateUtil.getDateWeekSort(xDate).year.toString());
      str = str.replace(/WW/g, DateUtil.getDateWeekSort(xDate).week.toString());
      return str
    }
    else if (this.dateType === 'MONTH') {
      console.log('按月展示', `${xDate.getFullYear()}-${xDate.getMonth() + 1}`);
      return `${xDate.getFullYear()}-${xDate.getMonth() + 1}` //按月展示
    }
  }

  // 生成图表
  doDrawEchart() {
    const element = document.getElementById('myEcharts')
    this.lineChart = this.$echarts.init(element)
    if (this.lineChart) {
      this.lineChart.setOption(this.options)
    }
  }

  getDataValue(itemOld: any) {
    const res = JSON.parse(JSON.stringify(itemOld))
    if (this.xAxisArray.length === 1) {
      res.data = res.data.map((item: AnalysisChartData) => [item.date, item.value, res.isInteger])
      return res
    } else {
      const newData = res.data.map((item: AnalysisChartData) => [item.date + ' 00:00:00', item.value, res.isInteger])
      delete res.data
      return {
        ...res,
        data: newData
      }
    }
  }
};