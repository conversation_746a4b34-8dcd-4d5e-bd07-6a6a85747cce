import MemberIdent from "model/common/member/MemberIdent";

export default class PointsExchangeReportLine extends MemberIdent {
  // 交易时间
  tranTime: Nullable<Date> = null
  // 门店
  occurredOrg: Nullable<string> = null
  // 使用积分
  points: Nullable<number> = null
  // 使用金额
  amount: Nullable<number> = null
  // 兑换对象
  exchangeObj: Nullable<string> = null
  // 数量
  qty: Nullable<number> = null
  // 活动名称
  activityName: Nullable<string> = null
  // 积分类型
  type: Nullable<string> = null
  // 交易号
  transNo: Nullable<string> = null
  // 会员识别码(大桥石化IC卡)
  identId: Nullable<string> = null
}