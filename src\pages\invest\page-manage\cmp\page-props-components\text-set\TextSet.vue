<template>
  <el-form inline :label-position="labelPosition" :model="value" :rules="rules" ref="form">
    <el-form-item :label="label" prop="propText" style="width: 100%" class="text-input">
      <el-input
        maxlength="260"
        class="textarea"
        :placeholder="i18n('请输入内容')"
        type="textarea"
        show-word-limit
        v-model="value.propText"
        @change="handleChange"
        style="width: 100%,white-space: pre-wrap;word-break: break-all;"
      ></el-input>
    </el-form-item>
  </el-form>
</template>
  
  <script lang="ts" src="./TextSet.ts"></script>
  
  <style lang="scss" scoped>
.text-input {
  margin-bottom: 10px;
  ::v-deep .el-form-item__content {
    width: 100% !important;
  }
  ::v-deep .el-textarea__inner {
    height: 80px;
  }
}
</style>
  