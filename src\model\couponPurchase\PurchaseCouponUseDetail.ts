/*
 * @Author: 黎钰龙
 * @Date: 2023-02-13 13:13:17
 * @LastEditTime: 2023-02-13 13:13:27
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\couponPurchase\PurchaseCouponUseDetail.ts
 * 记得注释
 */
// 购券交易使用明细记录
export default class PurchaseCouponUseDetail {
  // 交易号
  tradeId: Nullable<string> = null
  // 活动号
  activityNumber: Nullable<string> = null
  // 活动分摊的购券金额
  purchaseAmount: Nullable<number> = null
  // 转赠数量
  presentCount: Nullable<number> = null
  // 已用券数
  usedCount: Nullable<number> = null
  // 过期券数
  expiredCount: Nullable<number> = null
  // 已用金额
  usedAmount: Nullable<number> = null
  // 应退金额
  refundAmount: Nullable<number> = null
}