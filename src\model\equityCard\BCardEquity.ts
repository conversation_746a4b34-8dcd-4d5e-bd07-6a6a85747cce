/*
 * @Author: 黎钰龙
 * @Date: 2023-03-02 17:56:11
 * @LastEditTime: 2023-03-03 11:43:13
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\equityCard\BCardEquity.ts
 * 记得注释
 */
import GiftBagCoupon from 'model/common/GiftBagCoupon'
import { DateType } from 'model/weixin/weixinIssueCouponActivity/DateType'

// 卡权益
export default class BCardEquity {
  // 卡权益
  couponItems: GiftBagCoupon[] = []
  // 数量限制日期类型：DAY, WEEK, MONTH, YEAR;
  limitPartakeDateType: Nullable<DateType> = null
  // 日期类型下，参数次数总限制
  limitMemberPerTime: Nullable<number> = null
  // 周几发送赠礼,当 limitPartakeDateType = WEEK 适用
  weekDay: Nullable<number> = null
  //发放周期，每limitPartakeDateType发放dateInterval次
  dateInterval: Nullable<number> = null
}