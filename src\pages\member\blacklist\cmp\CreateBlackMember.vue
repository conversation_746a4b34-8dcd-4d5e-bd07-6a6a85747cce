<!--
 * @Author: 黎钰龙
 * @Date: 2024-04-10 17:11:00
 * @LastEditTime: 2024-04-19 15:20:04
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\member\blacklist\cmp\CreateBlackMember.vue
 * 记得注释
-->
<template>
  <el-dialog :before-close="doBeforeClose" :close-on-click-modal="false" :visible.sync="dialogShow" :title="i18n('新建黑名单')" width="40%"
    class="blacklist-dialog">
    <FormItem :label="i18n('/会员/会员资料/手机号')">
      <el-input v-model="ruleForm.mobile" :placeholder="i18n('/会员/会员资料/请输入手机号')" style="width:70%">
      </el-input>
    </FormItem>
    <FormItem :label="i18n('/会员/会员资料/会员号')">
      <el-input v-model="ruleForm.memberId" :placeholder="i18n('/会员/会员资料/请输入会员号')" style="width:70%">
      </el-input>
    </FormItem>
    <FormItem :label="i18n('原因')">
      <el-input v-model="ruleForm.reason" :placeholder="i18n('/会员/会员资料/请输入不超过50个字')" type="textarea" :rows="3" maxlength="50" style="width: 70%;"
        resize="none">
      </el-input>
    </FormItem>
    <div class="footer-block">
      <el-button type="primary" @click="doConfirm" style="margin-left:12px">
        {{i18n('确定')}}
      </el-button>
      <el-button @click="doClose">
        {{i18n('取消')}}
      </el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import FormItem from "cmp/formitem/FormItem";
import I18nPage from "common/I18nDecorator";
import MemberBlacklistApi from "http/promotion/MemberBlacklistApi";
import UserLoginResult from "model/login/UserLoginResult";
import SaveMemberBlacklistRequest from "model/promotion/bigWheelActivity/SaveMemberBlacklistRequest";
import CommonUtil from "util/CommonUtil";
import { Component, Vue } from "vue-property-decorator";
import { State } from "vuex-class";

class Form {
  mobile: Nullable<string> = null; //手机号
  memberId: Nullable<string> = null; //会员号
  reason: Nullable<string> = null; //原因
}

@Component({
  name: "CreateBlackMember",
  components: {
    FormItem,
  },
})
@I18nPage({
  prefix: ["/公用/券模板",'/会员/黑名单','/公用/按钮'],
  auto: true,
})
export default class CreateBlackMember extends Vue {
  @State("loginInfo") loginInfo: UserLoginResult;
  dialogShow: boolean = false;
  ruleForm: Form = new Form();

  open() {
    this.dialogShow = true;
  }

  doConfirm() {
    if (!this.ruleForm.memberId && !this.ruleForm.mobile) {
      return this.$message.error(this.i18n("手机号、会员号请至少填写一项"));
    }
    // const rex = /^1[3456789]\d{9}$/;
    // if (this.ruleForm.mobile && !rex.test(this.ruleForm.mobile)) {
    //   return this.$message.error(this.i18n("/储值/预付卡/预付卡查询/列表页面/请输入正确的手机号码"));
    // }
    const params = new SaveMemberBlacklistRequest();
    params.operator = this.loginInfo.user?.account;
    params.blacklist.crmCode = this.ruleForm.memberId;
    params.blacklist.mobile = this.ruleForm.mobile;
    params.blacklist.reason = this.ruleForm.reason;
    const loading = CommonUtil.Loading();
    MemberBlacklistApi.save(params)
      .then((res) => {
        if (res.code === 2000) {
          this.$message.success(this.i18n("保存成功"));
          this.$emit('confirm')
          this.doClose();
        } else {
          throw new Error(res.msg!);
        }
      })
      .catch((error) => {
        this.$message.error(error.message);
      })
      .finally(() => {
        loading.close();
      });
  }

  doClose() {
    this.dialogShow = false;
    this.ruleForm = new Form();
  }

  doBeforeClose(done: any) {
    done();
  }
}
</script>

<style lang="scss">
.blacklist-dialog {
  .footer-block {
    width: 100%;
    height: 60px;
    display: flex;
    align-items: center;
    flex-direction: row-reverse;
  }
  .qf-form-label {
    width: 140px !important;
  }
}
</style>