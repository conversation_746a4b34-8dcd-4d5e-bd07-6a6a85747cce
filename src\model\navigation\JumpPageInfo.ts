/*
 * @Author: 黎钰龙
 * @Date: 2025-01-06 20:02:16
 * @LastEditTime: 2025-04-17 15:49:31
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\navigation\JumpPageInfo.ts
 * 记得注释
 */
// 跳转页
export default class JumpPageInfo {
  // 页面路径，由后端解析生成供小程序端使用
  path: Nullable<string> = null
  // 一级页面：页面、积分兑换券、小程序领券、小程序领微信券、卡活动、大转盘、集点活动、抽奖团、邀请有礼、自定义链接、异业合作
  firstLevelPage: string = ''
  // 二级页面:功能页面[页面]、自定义页面[页面]、活动列表[积分兑换券、小程序领券、小程序领微信券、卡活动、大转盘、集点活动、抽奖团]、活动详情[积分兑换券、小程序领券、小程序领微信券、卡活动、大转盘、集点活动、抽奖团]、邀请有礼[邀请有礼]、H5链接[自定义链接]、小程序路径[自定义链接]、异业合作[异业合作]
  secondLevelPage: string = ''
  // 页面模板ID
  templateId: Nullable<string> = null
  // 页面模板名称
  templateName: Nullable<string> = null
  // 活动范围：all(全部活动)、part(指定活动)
  activityRange: Nullable<string> = null
  // 活动id
  activityIds: string[] = []
  // h5链接
  h5Link: Nullable<string> = null
  // 异业合作
  cooperation: Nullable<string> = null
  // 小程序跳转方式：regular(常规跳转)、halfScreen(微信半屏小程序)
  appJumpType: Nullable<string> = null
  // 小程序appId
  appId: Nullable<string> = null
  // 小程序路径
  appPath: Nullable<string> = null
  // 小程序传参选择：memberId(会员MemberID)、storeCode(门店代码)
  appParams: string[] = []
  // // id
  // id: Nullable<string> = null
  // // 代码
  // code: Nullable<string> = null
  // // 名称
  // name: Nullable<string> = null
}