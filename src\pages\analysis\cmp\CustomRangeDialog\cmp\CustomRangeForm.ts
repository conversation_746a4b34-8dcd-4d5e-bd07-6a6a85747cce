/*
 * @Author: 黎钰龙
 * @Date: 2025-02-12 15:39:18
 * @LastEditTime: 2025-03-05 15:04:31
 * @LastEditors: haiding <EMAIL>
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\analysis\cmp\CustomRangeDialog\cmp\CustomRangeForm.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import CustomRangeInfo from 'model/default/CustomRangeInfo';
import GroupSection from 'model/default/GroupSection';
import { Component, Model, Vue, Watch, Prop } from 'vue-property-decorator';
@Component({
  name: 'CustomRangeForm',
  components: {}
})
@I18nPage({
  prefix: [
    '/公用/券模板'
  ],
  auto: true
})
export default class CustomRangeForm extends Vue {
  @Model('change') value: CustomRangeInfo
  @Prop({ type: Boolean, default: true }) editable: Boolean
  isDefaultGroup: boolean = true //是否为默认区间
  currentSelectGroupSections: GroupSection[] = [];  // 自定义分组数据
  groupSections: GroupSection[] = [ // 初始自定义分组
    {
      tagValueGroupEnd: "",
      tagValueGroupStart: "-∞",
    },
    {
      tagValueGroupEnd: "+∞",
      tagValueGroupStart: "",
    },
  ];
  rangeInfo: CustomRangeInfo = new CustomRangeInfo()

  @Watch('value', { deep: true, immediate: true })
  bindValue(val: CustomRangeInfo) {
    if (val.type === 'custom') {
      this.isDefaultGroup = false
      this.currentSelectGroupSections = JSON.parse(JSON.stringify(val.customArr))
    }
  }

  // 属性是否缺项
  get isLackAttr() {
    return this.currentSelectGroupSections.find((item) => !item.tagValueGroupEnd);
  }

  showDelete(index: number) {
    return this.currentSelectGroupSections.length > 2 && index !== 0 && index !== this.currentSelectGroupSections.length - 1
  }

  // 删除自定义区间分组
  removeCurrentSelectGroupSectionsByIndex(index: number) {
    this.currentSelectGroupSections.splice(index, 1)
    this.doFormChange()
  }


  // 添加自定义区间分组
  addGroupSection() {
    this.currentSelectGroupSections.splice(this.currentSelectGroupSections.length - 1, 0, {
      tagValueGroupEnd: "",
      tagValueGroupStart: "",
    });
  }

  changeDefaultGroup(value: boolean) {
    // 自定义
    if (!value) {
      this.currentSelectGroupSections = JSON.parse(JSON.stringify(this.groupSections));
    }
    this.doFormChange()
  }

  doValidate() {
    return new Promise<void>((resolve, reject) => {
      if (this.isDefaultGroup) {
        resolve()
      } else {
        if (this.isLackAttr) {
          reject()
        } else {
          resolve()
        }
      }

    })
  }

  doFormChange() {
    if (this.isLackAttr) return
    this.currentSelectGroupSections.forEach((item, index) => {
      if (index !== 0) {
        item.tagValueGroupStart = this.currentSelectGroupSections[index - 1].tagValueGroupEnd;
      }
    });
    this.rangeInfo.type = this.isDefaultGroup ? 'default' : 'custom'
    this.rangeInfo.customArr = this.isDefaultGroup ? [] : this.currentSelectGroupSections
    const res = JSON.parse(JSON.stringify(this.rangeInfo))
    this.$emit('change', res)
  }


  created() {
    console.log(this.value,'this.editable',this.editable)
  }
};