import { ExpiryType } from "./ExpiryType"

export default class ValidityInfo {
  // 有效期类型：FIXED——固定有效期； RALATIVE——相对有效期;
  validityType: Nullable<string> = null
  // 相对有效期下的具体有效类型
  expiryType: Nullable<ExpiryType> = null
  // 延迟生效天数
  delayEffectDays: Nullable<number> = null
  // 有效天数
  validityDays: Nullable<number> = null
  // 起始日期
  beginDate: Nullable<LocalDateTime> = null
  // 截止日期
  endDate: Nullable<LocalDateTime> = null
  // 有效月数
  months: Nullable<number> = null
}