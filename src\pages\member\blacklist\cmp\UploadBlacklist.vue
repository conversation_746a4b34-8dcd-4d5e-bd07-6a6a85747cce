<!--
 * @Author: 黎钰龙
 * @Date: 2024-04-10 16:30:50
 * @LastEditTime: 2024-04-17 11:18:35
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\member\blacklist\cmp\UploadBlacklist.vue
 * 记得注释
-->
<template>
  <el-dialog :before-close="doBeforeClose" :close-on-click-modal="false" :visible.sync="dialogShow" class="upload-member-tag-dialog"
    :title="i18n('导入黑名单')">
    <div class="wrap">
      <FormItem :label="i18n('实例模板')">
        <a class="action-hover_download" @click="downloadTemplate" class="download-link">{{i18n("黑名单模板")}}</a>
      </FormItem>
      <FormItem :label="i18n('选择文件')">
        <div style="line-height:36px">
          <div class="gray-tips">{{i18n('/储值/会员储值/门店储值管理/为保障上传成功，建议每次最多上传5000条信息')}}</div>
          <el-upload :headers="uploadHeaders" :action="getUploadUrl" :auto-upload="false" :on-change="doHandleChange" :on-error="getErrorInfo"
            :on-success="getSuccessInfo" :with-credentials="true" :limit="1" :multiple="false" class="upload-demo" ref="upload">
            <el-button slot="trigger" type="default">{{i18n('选取文件')}}</el-button>
          </el-upload>
        </div>
      </FormItem>
    </div>
    <div class="dialog-footer" slot="footer">
      <el-button @click="doModalClose('cancel')">{{i18n("取消")}}</el-button>
      <el-button @click="doModalClose('confirm')" type="primary">{{i18n("确认导入")}}</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts">
/*
 * @Author: 黎钰龙
 * @Date: 2023-08-09 14:53:12
 * @LastEditTime: 2023-11-01 17:27:52
 * @LastEditors: 黎钰龙
 * @Description:
 * @FilePath: \phoenix-web-ui\src\pages\custom\dqsh\member\cmp\UploadMemberTags.ts
 * 记得注释
 */
import { Component } from "vue-property-decorator";
import FormItem from "cmp/formitem/FormItem.vue";
import EnvUtil from "util/EnvUtil";
import I18nPage from "common/I18nDecorator";
import AbstractImportDialog from "cmp/abstract-import-dialog/AbstractImportDialog";
import UploadApi from "http/upload/UploadApi";

@Component({
  name: "UploadBlacklist",
  components: {
    FormItem,
  },
})
@I18nPage({
  prefix: ["/公用/券模板", "/会员/会员资料", "/会员/会员资料/会员资料导入", "/公用/导入", "/公用/按钮", "/会员/黑名单"],
  auto: true,
})
export default class UploadBlacklist extends AbstractImportDialog {
  // 文件模板
  get templateHref() {
    if (location.href.indexOf("localhost") === -1) {
      return "template_member_blacklist.xlsx";
    } else {
      return "template_member_blacklist.xlsx";
    }
  }

  downloadTemplate() {
    UploadApi.getUrl(this.templateHref).then((resp: any) => {
      if (resp && resp.data) {
        window.open(resp.data);
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  get getUploadUrl() {
    return EnvUtil.getServiceUrl() + `v1/member/blacklist/importMemberBlacklist`;
  }

  uploadSuccess() {
    this.$emit("uploadSuccess");
  }

  close() {
    this.dialogShow = false;
  }
}
</script>

<style lang="scss">
.upload-member-tag-dialog {
  .wrap {
    margin-top: 30px;
    .download-link {
      color: #318bff;
      font-size: 13px;
      text-decoration: none;
      line-height: 36px;
    }
  }
  .el-dialog {
    width: 650px;
    height: 350px;
  }
  .el-dialog .el-dialog__body {
    height: 250px;
  }
}
.action-hover_download {
  cursor: pointer;
  /* 添加手型光标 */
}
</style>