/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:19
 * @LastEditTime: 2023-08-02 14:15:10
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\bread-crumb\BreadCrume.ts
 * 记得注释
 */
import {Component, Prop, Vue} from 'vue-property-decorator'

@Component({
  name: 'BreadCrume',
  components: {}
})
export default class BreadCrume extends Vue {
  @Prop() panelArray: any
  @Prop() proLogo: string
  @Prop({default: true}) backable: boolean
  @Prop({ type: Number, default: 18 }) fontSize: number;

  get bindPanel() {
    if (!this.panelArray) {
      return ''
    }
    let bindPanel = ''
    this.panelArray.forEach((item: any) => {
      if (item.url) {
        bindPanel += `<a style="font-size:${this.fontSize}px" title="${item.name}" :url="${item.url}" class="unSelected">${item.name}</a>&nbsp;<span style="font-size:${this.fontSize}px">/</span>&nbsp;`
      } else {
        bindPanel += `<a style="font-size:${this.fontSize}px;color:#242633" title="${item.name}" class="selected">${item.name}</a>&nbsp;/&nbsp;`
      }
    })
    bindPanel = bindPanel.substring(0, bindPanel.lastIndexOf('/'))
    return bindPanel
  }
  onToView(event: any) {
    let arr = this.panelArray.filter((item: any) => {
      return item.name === event.target.text
    })
    arr[0] && arr[0].url && this.$router.push({ name: arr[0].url, query: arr[0].query })
  }
  doBack() {
    if (this.backable) {
      if (this.panelArray[0].back) {
        this.$router.push({name: this.panelArray[0].back, query: this.panelArray[0].query})
      } else {
        this.$router.back()
      }
    }
  }
}