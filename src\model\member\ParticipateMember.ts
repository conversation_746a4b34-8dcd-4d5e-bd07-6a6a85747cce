/*
 * @Author: 黎钰龙
 * @Date: 2024-10-08 10:00:30
 * @LastEditTime: 2024-10-09 11:41:56
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\member\ParticipateMember.ts
 * 记得注释
 */
import MemberRule from "model/precisionmarketing/tag/tagrule/customize/member/MemberRule"
import { MemberRangeType } from "./MemberRangeType"
import MemberTagOption from "./MemberTagOption"
import UserGroupOption from "./UserGroupOption"

export default class ParticipateMember {
  // 人群范围
  memberRangeType: Nullable<MemberRangeType> = MemberRangeType.ALL
  // 会员标签
  memberTagOption: Nullable<MemberTagOption> = null
  // 客群
  userGroupOption: Nullable<UserGroupOption> = null
  // 会员属性
  memberRule: Nullable<MemberRule> = null
}