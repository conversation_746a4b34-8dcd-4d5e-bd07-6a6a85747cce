/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-04-29 13:51:18
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\grade\GradeBenefitRule.ts
 * 记得注释
 */
import BasicBenefit from 'model/grade/BasicBenefit'
import MonthBenefit from 'model/grade/MonthBenefit'
import GradeRuleBenefitConfig from './GradeRuleBenefitConfig'

export default class GradeBenefitRule {
    // 权益发放日
    benefitDay: Nullable<number> = null
    // 基础权益信息
    basicBenefits: BasicBenefit[] = []
    // 等级月礼
    monthBenefits: MonthBenefit[] = []
    // 权益设置
    gradeRuleBenefitConfigs: GradeRuleBenefitConfig[] = []
}