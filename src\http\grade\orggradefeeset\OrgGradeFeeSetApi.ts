import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import OrgGradeFeeSet from "model/grade/orggradefeeset/OrgGradeFeeSet";
import OrgGradeFeeSetFilter from "model/grade/orggradefeeset/OrgGradeFeeSetFilter";
import OrgGradeFeeSetSaveRequest from "model/grade/orggradefeeset/OrgGradeFeeSetSaveRequest";

export default class OrgGradeFeeSetApi {
  /**
   * 查询等级套餐名称
   * 查询等级套餐名称
   * 
   */
  static listFeeSetName(): Promise<Response<string[]>> {
    return ApiClient.server().get(`/v1/org-grade-fee-set/listFeeSetName`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询门店等级套餐设置
   * 查询门店等级套餐设置
   * 
   */
  static query(body: OrgGradeFeeSetFilter): Promise<Response<OrgGradeFeeSet[]>> {
    return ApiClient.server().post(`/v1/org-grade-fee-set/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 清空门店等级套餐
   * 清空门店等级套餐
   * 
   */
  static removeByOrgId(orgId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/org-grade-fee-set/removeByOrgId`, {}, {
      params: {
        orgId: orgId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 设置门店等级套餐
   * 设置门店等级套餐
   * 
   */
  static save(body: OrgGradeFeeSetSaveRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/org-grade-fee-set/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
