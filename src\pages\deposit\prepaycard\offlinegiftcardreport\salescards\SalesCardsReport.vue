<template>
  <div class="sales-cards-report">
    <div class="current-page">
      <el-form label-width="140px">
        <el-row class="query">
          <TimeRange no-i18n @submit="handleTimeRange" :da1qiaoPermission="da1qiaoPermission" ref="timeRange"></TimeRange>
        </el-row>
        <el-row class="query" style="margin-top: 8px">
          <el-col :span="8">
            <form-item label="购卡人">
              <el-input placeholder="输入手机号/会员号" v-model="query.memberIdEquals"/>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item label="交易号">
              <el-input placeholder="类似于" v-model="query.transNoLikes"/>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item label="卡面额">
              <div class="el-date-editor" style="display:flex">
                <el-input :placeholder="formatI18n('/储值/预付卡/预付卡查询/列表页面/最小值')" class="el-range-input" v-model="query.faceAmountGreaterOrEquals"/>
                <span class="el-range-separator" style="padding:0px 15px;height:32px;">-</span>
                <el-input :placeholder="formatI18n('/储值/预付卡/预付卡查询/列表页面/最大值')" class="el-range-input" v-model="query.faceAmountLessOrEquals"/>
              </div>
            </form-item>
          </el-col>
        </el-row>
        <el-row class="query" style="margin-top: 8px" v-if="expandQuery">
          <el-col :span="8">
            <form-item :label="i18n('售价')">
              <div class="el-date-editor" style="display:flex">
                <el-input :placeholder="formatI18n('/储值/预付卡/预付卡查询/列表页面/最小值')" class="el-range-input" v-model="query.priceGreaterOrEquals"/>
                <span class="el-range-separator" style="padding:0px 15px;height:32px;">-</span>
                <el-input :placeholder="formatI18n('/储值/预付卡/预付卡查询/列表页面/最大值')" class="el-range-input" v-model="query.priceLessOrEquals"/>
              </div>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="i18n('卡模板')">
              <el-input placeholder="类似于" v-model="query.templateLikes"/>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="i18n('卡号')">
              <el-input placeholder="类似于" v-model="query.codeLikes"/>
            </form-item>
          </el-col>
        </el-row>
        <el-row class="query" style="margin-top: 8px" v-if="expandQuery">
          <el-col :span="8">
            <form-item :label="i18n('发生组织')">
              <SelectStores v-model="query.orgIdEquals" :isOnlyId="true" :hideAll="false" width="100%"
                :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
              </SelectStores>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="i18n('发生区域')">
              <el-select placeholder="不限" v-model="query.zoneIdEquals">
                <el-option
                  :label="formatI18n('/公用/查询条件/下拉列表/不限')"
                  :value="null"
                >{{ formatI18n('/公用/查询条件/下拉列表/不限') }}</el-option>
                <el-option
                  :key="item.zone.id"
                  :label="'['+item.zone.id+']'+item.zone.name"
                  :value="item.zone.id"
                  v-for="item in areaData"
                >[{{item.zone.id}}]{{item.zone.name}}</el-option>
              </el-select>
            </form-item>
          </el-col>
        </el-row>
        <el-row class="query" style="margin-top: 8px">
          <el-col :span="8">
            <form-item label=" ">
              <el-button class="btn-search" type="primary" @click="doSearch">查询</el-button>
              <el-button class="btn-reset" @click="doReset">重置</el-button>
              <el-button type="text" @click="expandQuery=!expandQuery">
                <span v-if="!expandQuery" key="1"><i class="el-icon-arrow-down"></i>{{formatI18n('/公用/查询条件/展开')}}</span>
                <span v-if="expandQuery" key="1"><i class="el-icon-arrow-up"></i>{{formatI18n('/公用/查询条件/收起')}}</span>
              </el-button>
            </form-item>
          </el-col>
        </el-row>
      </el-form>
      <hr/>
      <el-row style="line-height: 35px" v-if="isShowSum">
        <i class="el-icon-warning"/>
        <i18n k="/储值/预付卡/实体礼品卡报表/售卡流水/共售出{0}张实体礼品卡，销售额为{1}元">
          <template slot="0">&nbsp;<span style="color: red">{{sum.qty}}</span>&nbsp;</template>
          <template slot="1">&nbsp;<span style="color: red">{{dataUtil.showTotalAmount(sum.totalAmount)}}</span>&nbsp;</template>
        </i18n>
      </el-row>
      <el-row class="table">
        <el-table
            :data="queryData"
            style="width: 100%;margin-top: 20px"
        >
          <el-table-column
              label="卡号"
              prop="code"
              width="250"
          >
          </el-table-column>
          <el-table-column
              label="售卡时间"
              prop="occurredTime"
              width="140"
          >
            <template slot-scope="scope">
              <span  no-i18n>{{dataUtil.format(scope.row.occurredTime, 'yyyy-MM-dd HH:mm:ss')}}</span>
            </template>
          </el-table-column>
          <el-table-column
              label="发生组织"
              width="200"
              prop="occurredOrg"
          >
            <template slot-scope="scope">
              <span  no-i18n :title="dataUtil.showIdName(scope.row.occurredOrg)">{{dataUtil.showIdName(scope.row.occurredOrg)}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水/发生区域')" prop="zone" >
                             <template slot-scope="scope">
                                <div v-if="scope.row.zone!==null && scope.row.zone!==''">
                                    <el-tooltip class="item" effect="light"  placement="right-end">
                                    <div> {{'['+scope.row.zone.id+']'}}{{scope.row.zone.name}}</div>
                                    <div slot="content">
                                         {{'['+scope.row.zone.id+']'}}{{scope.row.zone.name}}
                                    </div>
                                    </el-tooltip>
                                </div>
                                <div v-else>-</div>
                                </template>
          </el-table-column>
          <el-table-column
              label="购卡人"
              prop="memberId"
              width="100"
          >
            <template slot-scope="scope">
              <span  no-i18n :title="dataUtil.showMemberId(scope.row)">{{dataUtil.showMemberId(scope.row)}}</span>
            </template>
          </el-table-column>
          <el-table-column
              label="卡面额(元)"
              width="140"
              prop="faceAmount"
              align="right"
          >
            <template slot-scope="scope">
              <span  no-i18n>{{scope.row.faceAmount.toFixed(2)}}</span>
            </template>
          </el-table-column>
          <el-table-column
              label="售价(元)"
              width="140"
              prop="price"
              align="right"
          >
            <template slot-scope="scope">
              <span  no-i18n>{{scope.row.price.toFixed(2)}}</span>
            </template>
          </el-table-column>
          <el-table-column
              label="返现(元)"
              width="140"
              prop="giftAmount"
              align="right"
          >
            <template slot-scope="scope">
              <span  no-i18n>{{scope.row.giftAmount.toFixed(2)}}</span>
            </template>
          </el-table-column>
          <el-table-column
              label="交易号"
              prop="transNo"
          >
            <template slot-scope="scope">
              <span no-i18n :title="scope.row.transNo">{{scope.row.transNo}}</span>
            </template>
          </el-table-column>
          <el-table-column
              label="支付方式"
              prop="payType"
              width="200"
          >
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.payType}}</span>
            </template>
          </el-table-column>
          <el-table-column
              label="卡模板"
              prop="templateNumber"
              width="150"
          >
            <template slot-scope="scope">
              <el-button no-i18n type="text" @click="gotoTplDtl(scope.row.templateNumber)">{{scope.row.templateName}}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-pagination
          no-i18n
          :current-page="page.currentPage"
          :page-size="page.size"
          :page-sizes="[10, 20, 30, 40]"
          :total="page.total"
          @current-change="onHandleCurrentChange"
          @size-change="onHandleSizeChange"
          background
          layout="total, prev, pager, next, sizes,  jumper"
          class="pagin"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script lang="ts" src="./SalesCardsReport.ts">
</script>

<style lang="scss" scoped>
.sales-cards-report {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: auto;
  .total {
    margin: 20px;
  }

  div.el-range-input {
    flex: 1;
    input.el-input__inner {
      border: none;
      padding: 0px;
      line-height: 1;
      height: 100%;
    }
  }

  .current-page {
    height: calc(100% - 150px);
    padding: 0 20px 20px 20px;
    .el-select {
      width: 100%;
    }

    .query {
      .el-form-item {
        margin-bottom: 0;
      }
    }

    .pagin {
      margin-top: 25px;
    }

    .el-col {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    tbody {
      .cell {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
</style>
