import {Component, Vue} from 'vue-property-decorator'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import FormItem from 'cmp/formitem/FormItem.vue'
import PrePayAdjustBillApi from 'http/prepay/adjustbill/PrePayAdjustBillApi'
import AdjustBillReason from 'model/prepay/adjustbill/AdjustBillReason'
import PrePayReasonFilter from 'model/prepay/adjustbill/PrePayReasonFilter'
import StoreValueAdjustReasonAdd from 'pages/deposit/mbrdeposit/adjust/dialog/StoreValueAdjustReasonAdd.vue'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import I18nPage from 'common/I18nDecorator'
import StoreValueAdjustPermission from 'pages/deposit/mbrdeposit/adjust/StoreValueAdjustPermission'

@Component({
  name: 'StoreValueAdjustReason',
  components: {
    SubHeader,
    FormItem,
    StoreValueAdjustReasonAdd,
    BreadCrume
  }
})
@I18nPage({
  prefix: ['/储值/会员储值/储值调整单/调整原因设置', '/公用/按钮']
})
export default class StoreValueAdjustReason extends Vue {
  i18n: I18nFunc
  permission = new StoreValueAdjustPermission()
  panelArray = [
    {
      name: this.i18n('储值调整单'),
      url: 'store-value-adjust'
    },
    {
      name: this.i18n('储值调整原因设置'),
      url: ''
    }
  ]
  query: PrePayReasonFilter = new PrePayReasonFilter()
  reason = ''
  data: any = ''
  searchContent = ''
  $refs: any
  dialogShow = false
  selectedArr: AdjustBillReason[] = []
  tableData: AdjustBillReason[] = []
  recordTableCount = 0
  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  created() {
    this.getStoreValueAdjustReason()
  }
  doAddReason() {
    if (!this.reason) {
      this.$message.warning(this.i18n('原因不能为空'))
      this.$refs.reason.focus()
      return
    }
    this.recordTableCount++
    if (this.recordTableCount <= 20) {
      this.submitStoreValueAdjustReason()
    } else {
      this.$message.warning(this.i18n('最多只能创建20条原因'))
      return
    }

  }
  doClear() {
    this.reason = ''
    this.getStoreValueAdjustReason()
  }
  doDialogClose() {
    this.dialogShow = false
    this.getStoreValueAdjustReason()
  }
  doBatchDelete() {
    if (this.selectedArr.length <= 0) {
      this.$message.warning(this.i18n('请先勾选要删除的储值调整原因'))
      return
    }
    this.$confirm(this.i18n('确认批量删除?'), this.i18n('提示'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消'),
      type: 'warning'
    }).then(() => {
      let arr: string[] = []
      this.selectedArr.forEach((item: any) => {
        arr.push(item.id)
      })
      PrePayAdjustBillApi.removeReason(arr).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message({
            type: 'success',
            message: this.i18n('批量删除成功!')
          })
          this.getStoreValueAdjustReason()
        }
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    })
  }
  doSearchByKey() {
    let obj: PrePayReasonFilter = new PrePayReasonFilter()
    obj.page = this.page.currentPage - 1
    obj.pageSize = this.page.size
    obj.reasonLikes = this.searchContent
    PrePayAdjustBillApi.listReason(obj).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.tableData = resp.data
        this.page.total = resp.total
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  doDelete(row: any) {
    this.$confirm(this.i18n('确认删除?'), this.i18n('提示'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消'),
      type: 'warning'
    }).then(() => {
      PrePayAdjustBillApi.removeReason([row.id]).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message({
            type: 'success',
            message: this.i18n('删除成功!')
          })
          this.getStoreValueAdjustReason()
        }
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    })
  }
  doEdit(row: any) {
    this.data = row
    this.dialogShow = true
  }
  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.getStoreValueAdjustReason()
  }
  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.getStoreValueAdjustReason()
  }
  handleSelectionChange(val: any) {
    this.selectedArr = val
  }
  private getStoreValueAdjustReason() {
    this.query.page = this.page.currentPage - 1
    this.query.pageSize = this.page.size
    PrePayAdjustBillApi.listReason(this.query).then((resp: any) => {
      if (resp && resp.data) {
        this.tableData = resp.data
        this.page.total = resp.total
        this.recordTableCount = resp.total
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  private submitStoreValueAdjustReason() {
    PrePayAdjustBillApi.saveReason(this.reason).then((resp: any) => {
      if (resp && resp.data) {
        this.$message.success(this.i18n('新建成功'))
        this.reason = ''
        this.getStoreValueAdjustReason()
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  private submitBatchDelete() {
    // PrePayAdjustBillApi.
  }
}
