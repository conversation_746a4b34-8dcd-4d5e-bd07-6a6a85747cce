<template>
  <el-dialog :before-close="doBeforeClose" :close-on-click-modal="false" :visible.sync="dialogShow" class="upload-dialog"
    :title="i18n('导入')">
    <div class="wrap">
      <FormItem :label="i18n('实例模板')">
        <a @click="downloadTemplate" class="action-hover_download download-link">{{i18n("批量修改会员状态模板")}}</a>
      </FormItem>
      <FormItem :label="i18n('选择文件')">
        <div style="line-height:36px">
          <div class="gray-tips">{{i18n('/储值/会员储值/门店储值管理/为保障上传成功，建议每次最多上传5000条信息')}}</div>
          <el-upload :headers="uploadHeaders" :action="getUploadUrl" :auto-upload="false" :on-change="doHandleChange" :on-error="getErrorInfo"
            :on-success="getSuccessInfo" :with-credentials="true" :limit="1" :multiple="false" class="upload-demo" ref="upload">
            <el-button slot="trigger" type="default">{{i18n('选取文件')}}</el-button>
          </el-upload>
        </div>
      </FormItem>
    </div>
    <div class="dialog-footer" slot="footer">
      <el-button @click="doModalClose('cancel')">{{i18n("取消")}}</el-button>
      <el-button @click="doModalClose('confirm')" type="primary">{{i18n("确认导入")}}</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./UploadFreezeDialog.ts">
</script>

<style lang="scss">
.upload-dialog {
  .wrap {
    margin-top: 30px;
    .download-link {
      color: #318bff;
      font-size: 13px;
      text-decoration: none;
      line-height: 36px;
    }
  }
  .el-dialog {
    width: 650px;
    height: 350px;
  }
  .el-dialog .el-dialog__body {
    height: 250px;
  }
}
</style>