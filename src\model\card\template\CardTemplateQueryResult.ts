import CardTemplate from 'model/card/template/CardTemplate'
import TemplateTypeCountResult from 'model/card/template/TemplateTypeCountResult'

export default class CardTemplateQueryResult {
  // 计数统计
  countResult: Nullable<TemplateTypeCountResult> = null
  // 总数
  total: Nullable<number> = null
  // 总页数
  pageCount: Nullable<number> = null
  // 页码
  page: Nullable<number> = null
  // 页面大小，大于0
  pageSize: Nullable<number> = null
  // 活动
  result: CardTemplate[] = []
}