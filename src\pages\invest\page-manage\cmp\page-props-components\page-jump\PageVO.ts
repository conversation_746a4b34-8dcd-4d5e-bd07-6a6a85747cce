/*
 * @Author: 黎钰龙
 * @Date: 2024-05-29 18:17:12
 * @LastEditTime: 2025-04-17 15:45:13
 * @LastEditors: 黎钰龙
 * @Description:
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\cmp\page-props-components\page-jump\PageVO.ts
 * 记得注释
 */
export enum PageVO {
    Page = 'Page', // 页面一级
    DeliveryPage = 'Page.DeliveryPage', // 页面二级
    Coupon = 'Coupon', // 券
    GetCoupon = 'Coupon.GetCoupon', // 小程序领券
    GetWeiXinCoupon = 'Coupon.GetWeiXinCoupon', // 小程序领微信券
    GetAliCoupon = 'Coupon.GetAliCoupon', // 小程序领支付宝券
    MyCoupon = 'Coupon.MyCoupon', // 我的券列表
    Points = 'Points', // 积分
    ExchangeCoupon = 'Points.ExchangeCoupon', // 积分兑换券
    Prepay = 'Prepay', // 储值
    DepositGift = 'Prepay.DepositGift', // 充值有礼
    Card = 'Card', // 卡
    CardDeposit = 'Card.CardDeposit', // 卡充值
    GiftCardActivity = 'Card.GiftCardActivity', // 电子卡发售活动
    Promotion = 'Promotion', // 营销活动
    CollectPointsActivity = 'Promotion.CollectPointsActivity', // 集点活动
    BigWheel = 'Promotion.BigWheel', // 大转盘
    GroupBooking = 'Promotion.GroupBooking',  //拼团抽奖
    LuckyDraw = 'Promotion.LuckyDraw',  //抽锦鲤
    InvitedGift = 'Promotion.InvitedGift', // 邀请有礼
    ExchangeCode = 'Promotion.ExchangeCode',  //兑换码
    MemberCode = 'MemberCode', // 会员/支付码
    MemberCodeQr = 'MemberCode.MemberCodeQr', // 会员码
    WeiXinPay = 'MemberCode.WeiXinPay', // 微信支付
    WeiXinCard = 'MemberCode.WeiXinCard', //微信会员卡
    PrepayPayCode = 'MemberCode.PrepayPayCode', // 储值付款码
    PointsPayCode = 'MemberCode.PointsPayCode', // 积分付款码
    CustomLink = 'CustomLink', // 自定义链接
    H5Link = 'H5Link', // H5链接
    WeappLink = 'WeappLink', // 小程序路径
    sysPage = 'sysPage', // 系统页面
    template = 'template', // 投放模版页面
    activity = 'activity', // 活动页面
    PullWeiXinPay = 'PullWeiXinPay', // 拉起微信支付
    Cooperation = 'Cooperation', // 异业合作
  }
