import { Vue, Component, Prop, Watch } from "vue-property-decorator";
import { FormMode } from "model/local/FormMode";
import I18nPage from "common/I18nDecorator";
import SysConfigApi from "http/config/SysConfigApi";
import JumpPageInfo from "model/navigation/JumpPageInfo";
import BenefitCardTemplateApi from "http/equityCard/BenefitCardTemplateApi";
import BenefitCardTemplateFilter from "model/equityCard/default/BenefitCardTemplateFilter";
import { enefitCardTemplateStatus } from "model/equityCard/default/enefitCardTemplateStatus";
import BenefitCardTemplate from "model/equityCard/BenefitCardTemplate";
import { enefitCardTemplateType } from "model/equityCard/default/enefitCardTemplateType";
import UploadImg from 'cmp/upload-img/UploadImg';
import FormItem from "cmp/formitem/FormItem";

@Component({
  name: "MemberCard",
  mixins: [],
  components: {
    UploadImg,
    FormItem
  },
})
@I18nPage({
  prefix: ["/公用/券模板", "/页面/页面管理", "/页面/导航设置", '/会员/会员资料', '/公用/菜单'],
  auto: true,
})
export default class MemberCard extends Vue {
  @Prop()
  config: any;
  @Prop()
  componentItem: any;
  @Prop({ type: Boolean, default: false })
  readonly: boolean; //
  @Prop({ type: String, default: "MemberCard" })
  validateName: string; // 组件校验名称，editPage通过该名称进行组件校验及校验信息清除
  @Prop({ type: Object })
  value: any; // 数据模型
  @Prop({ type: String, default: "组件名称" })
  label: string; // label名
  @Prop({ type: String })
  prefixDescribe: string; // 前置描述
  @Prop({ type: String })
  suffixDescribe: string; // 后置描述

  @Prop({ type: Boolean, default: true })
  required: boolean; // 是否必填
  @Prop({ type: String })
  relativeValidateName: string; // 联动校验名称
  @Prop()
  formKey: any;
  @Prop({ type: Array })
  renderTemplateList: any[];
  @Prop()
  activeIndex: number;

  FormModeType = FormMode;
  $refs: any;
  labelPosition: string = "left";
  text: string = "";

  rules: any = {}; //表单验证

  baiLianPayCode: boolean = false;

  baiLianStore: boolean = false;

  // 付费会员列表
  benefitCardTemplateList: BenefitCardTemplate[] = [];
  loadingBenefitCardTemplateList: boolean = false;

  get hideAddBenefitCard (){
    return this.value && this.value.propPaidMemberCards && this.value.propPaidMemberCards.length >= this.benefitCardTemplateList.length
  }

  created() {
    this.rules = {
      propShowAvatar: [
        {
          required: true,
          trigger: ["blur", "change"],
          validator: (rule: any, value: string, callback: Function) => {
            if (!value) {
              return callback(new Error(this.i18n("请输入组件名称")));
            }
            // if (this.filterActivityWidgets.some((item) => item.props.name === value)) {
            //   return callback(new Error('组件名称不能重复'));
            // }
            return callback();
          },
        },
      ],
      propShowNickname: [{ required: false, message: "", trigger: "change" }],
      propShowPhoneNumber: [{ required: false, message: "", trigger: "change" }],
      propShowEditPhoneNumber: [{ required: false, message: "", trigger: "change" }],
      propShowMemberLevel: [{ required: false, message: "", trigger: "change" }],
      propShowMemberCode: [{ required: false, message: "", trigger: "change" }],
      propShowPoint: [{ required: false, message: "", trigger: "change" }],
      propPointText: [{ required: false, message: "", trigger: "change" }],
      propShowBalance: [{ required: false, message: "", trigger: "change" }],
      propBalanceText: [{ required: false, message: "", trigger: "change" }],
      propShowCoupon: [{ required: false, message: "", trigger: "change" }],
      propCouponText: [{ required: false, message: "", trigger: "change" }],
      propShowPrepaidCard: [{ required: false, message: "", trigger: "change" }],
      propPrepaidCardText: [{ required: false, message: "", trigger: "change" }],
      propshowPaidMember: [{ required: false, message: "", trigger: "change" }],
      propPaidMemberText: [{ required: true, message: this.i18n('请输入'), trigger: "change" }],
    };
  }

  get filterActivityWidgets() {
    return this.renderTemplateList.filter((item, index) => index !== this.activeIndex);
  }

  @Watch("value.propMemberCodeJumpPageInfo.templateId")
  valuePropMemberCodeJumpPageInfoChange() {
    this.setBaiLianDefaultValue();
  }

  @Watch("value.propBalanceJumpPageInfo.templateId")
  valuePropBalanceJumpPageInfoChange() {
    this.setBaiLianDefaultValue();
  }


  addBenefitCard() {
    if(!this.value.propPaidMemberCards ) {
      this.$set(this.value, 'propPaidMemberCards', [])
    }
    
    this.value.propPaidMemberCards.push('');
  }

  removeBenefitCard(index: number){
    this.value.propPaidMemberCards.splice(index, 1)
  }

  handleChange() {
    this.$emit("input", this.value);
    this.$emit("change", this.value);
    this.validate(() => {});
  }
  handleBalenceJumpPageInfoChange(jumpPageInfo: JumpPageInfo) {
    if (jumpPageInfo.templateId === "BaiLianStoredValue") {
      jumpPageInfo.templateName = "百联储值";
    } else {
      jumpPageInfo.templateName = "储值";
    }
    this.handleChange();
  }
  handleMemberCodeJumpPageInfo(jumpPageInfo: JumpPageInfo) {
    if (jumpPageInfo.templateId === "BaiLianStoredPayCode") {
      jumpPageInfo.templateName = "百联储值付款码";
    } else {
      jumpPageInfo.templateName = "会员动态码";
    }
    this.handleChange();
  }
  mounted() {
    if (!this.value.propMemberCodeJumpPageInfo) {
      this.value.propMemberCodeJumpPageInfo = new JumpPageInfo();
    }

    if (!this.value.propBalanceJumpPageInfo) {
      this.value.propBalanceJumpPageInfo = new JumpPageInfo();
    }
    SysConfigApi.get().then((response) => {
      if (response?.data?.baiLianPayCode) {
        this.baiLianPayCode = true;
      }

      if (response?.data?.baiLianStore) {
        this.baiLianStore = true;
      }

      this.setBaiLianDefaultValue();
    });

    this.getMemberPayCardList();
  }

  setBaiLianDefaultValue() {
    if (this.baiLianPayCode) {
      if (!this.value.propMemberCodeJumpPageInfo?.templateId) {
        this.value.propMemberCodeJumpPageInfo.templateId = "BaiLianStoredPayCode";
        this.value.propMemberCodeJumpPageInfo.templateName = "百联储值付款码";
      }

      if (!this.value.propBalanceJumpPageInfo?.templateId) {
        this.value.propBalanceJumpPageInfo.templateId = "BaiLianStoredValue";
        this.value.propBalanceJumpPageInfo.templateName = "百联储值";
      }
    } else {
      if (!this.value?.propMemberCodeJumpPageInfo?.templateId) {
        this.value.propMemberCodeJumpPageInfo.templateId = "MemberDynamicCode";
        this.value.propMemberCodeJumpPageInfo.templateName = "会员动态码";
      }

      if (!this.value.propBalanceJumpPageInfo?.templateId) {
        this.value.propBalanceJumpPageInfo.templateId = "StoredValue";
        this.value.propBalanceJumpPageInfo.templateName = "储值";
      }
    }
    this.handleChange();
  }

  beforeDestroy() {}

  validate(callback: Function) {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.validate(callback);
    }
  }

  clearValidate() {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.clearValidate();
    }
  }

  // 查询付款会员卡列表
  getMemberPayCardList() {
    const params = new BenefitCardTemplateFilter();
    // 查询全部
    params.page = 0;
    params.pageSize = 0;
    // 仅已启用
    params.statusEquals = enefitCardTemplateStatus.start;
    params.typeEquals = enefitCardTemplateType.paid;
    params.marketingCenterEquals = sessionStorage.getItem("marketCenter");
    this.loadingBenefitCardTemplateList= true
    BenefitCardTemplateApi.query(params).then((response) => {
      if (response.data?.length) {
        this.benefitCardTemplateList = response.data;
      } else {
        this.benefitCardTemplateList = [];
      }
    }).finally(()=>{
      this.loadingBenefitCardTemplateList= false
    })
  }

  isBenefitCardDisabled(selectCode: string, code: string){
    return this.value.propPaidMemberCards?.find((item: string)=>item == code && item!==selectCode) ? true : false
  }

}
