/*
 * @Author: 黎钰龙
 * @Date: 2023-12-13 14:42:51
 * @LastEditTime: 2024-05-10 16:02:32
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\member\meituan\MeituanRestaurantAuth.ts
 * 记得注释
 */
import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import FormItem from 'cmp/formitem/FormItem';
import SelectStores from 'cmp/selectStores/SelectStores';
import I18nPage from 'common/I18nDecorator';
import IdName from 'model/common/IdName';
import { Component, Vue } from 'vue-property-decorator';
import ChannelSelect from "cmp/channelselect/ChannelSelect";
import DouYinAuthApi from "http/douyin/DouYinAuthApi";
@Component({
  name: 'DouYinMember',
  components: {
    BreadCrume,
    SelectStores,
    ChannelSelect,
    FormItem
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/设置/抖音会员授权'
  ],
  auto: true
})
export default class DouYinMember extends Vue {
  selectChannel: any = null
  solutionKey: any = '4'
  get panelArray() {
    return [
      {
        url: '',
        name: this.i18n('/公用/菜单/抖音会员授权')
      }
    ]
  }

  doAuth() {
    if (!this.selectChannel?.length) return this.$message.warning(this.i18n('/会员/微信会员初始化/未授权/初始化第一步微信授权/请选择授权渠道'))
    if (!this.solutionKey?.length) return this.$message.warning(this.i18n('/设置/抖音会员授权/请选择解决方案'))

    DouYinAuthApi.getAuthUrl({
      channelType: this.selectChannel[0]?.type,
      channelId: this.selectChannel[0]?.id,
      solutionKey: this.solutionKey
    }).then((res) => {
      if(res.code === 2000 && res.data) {
        window.open(res.data, '_blank');
      } else {
        throw new Error(res.msg || '授权失败')
      }
    }).catch((error) => this.$message.error(error.message || '内部异常'))
  }
};