import {Component, Inject, Prop, Vue, Watch} from 'vue-property-decorator'
import CouponItem from 'model/common/CouponItem'
import AmountToFixUtil from 'util/AmountToFixUtil'
import CouponTemplateSelectorDialog from 'cmp/selectordialogs/CouponTemplateSelectorDialog.vue'
import CouponTemplate from 'model/coupon/template/CouponTemplate'
import CouponTemplateFilter from 'model/coupon/template/CouponTemplateFilter'
import SelectStoreActiveDtlDialog from 'pages/deposit/mbrdeposit/active/dialog/SelectStoreActiveDtlDialog.vue'
import CouponTemplateApi from 'http/coupon/template/CouponTemplateApi'
import CouponInfo from 'model/common/CouponInfo'
import I18nPage from 'common/I18nDecorator'

@Component({
	name: "ActiveAddCoupon",
	components: {
		CouponTemplateSelectorDialog,
		SelectStoreActiveDtlDialog,
	},
})
@I18nPage({
  prefix: [
    '/公用/券模板'
  ],
  auto: true
})
export default class ActiveAddCoupon extends Vue {
	@Inject({
		from: 'maxCouponItemLimit',
		default: 99999999
	})
	maxCouponItemLimit: Number
	@Prop({
		default: false,
	})
	justShowIdName: Boolean
	@Prop({
		default: null
	})
	maxLimit: Nullable<Number>
  @Prop() propMaxLimit: Nullable<Number>;
	@Prop()
	value: any;
	@Prop()
	state: string;
	@Prop({
		type: Boolean,
		default: false,
	})
	baseSettingFlag: boolean;
	@Prop({
		type: Boolean,
		default: false,
	})
	qtyDisabled: boolean;
	@Prop({
		default: () => [],
	})
	excludedTypes: any[];
	@Prop({
		type: Boolean,
		default: false,
	})
	showDelay: boolean
  @Prop({
    default: false
  })
  showActivityReference: Boolean

  @Prop({
    type: Boolean,
    default: false,
  })
  showTemplatePrice: boolean;

  @Prop({
    type: String,
    default: 'copy',
  })
  editType: string;

	child: CouponItem = new CouponItem();
	cardTemplateFilter: CouponTemplateFilter = new CouponTemplateFilter();
	couponTemplateDtlDialogFlag = false;
	couponIndex = 0;
	formData: any = {
		receiveParamsArray: [],
	};

	receiveNameArray: any[] = [];
	couponTemplateDialogShow = false;
  firstShowTemplatePrice: boolean = false
	$refs: any;
	templateData: any = "";
	rules: any[] = [];
	@Watch("value")
	onValueChange(value: any) {
		if (value && value.length > 0) {
			this.formData.receiveParamsArray = [];
			this.receiveNameArray = [];
			value.forEach((item: CouponItem) => {
				if (item) {
					if (item.coupons && item.coupons.name) {
						this.receiveNameArray.push(item.coupons.name);
					}
					this.formData.receiveParamsArray.push(item);
				}
			});
		} else {
			this.formData.receiveParamsArray = [];
			this.receiveNameArray = [];
			// this.$refs.couponTemplate.setResetCouponTemplate()
		}
	}

  get canTemplatePriceEdit() {
    return this.state != '' && this.state !== 'INITAIL' && (this.state as any) !== 'PLATFORM_AUDIT_FAIL' && this.editType !== 'copy' && this.firstShowTemplatePrice
  }

  get availableState() {
    return this.state !== 'UNSTART' && this.state !== 'PROCESSING'
  }

	created() {
		let validatePass = (rule: any, value: any, callback: any) => {
			if (!value) {
				callback(new Error(this.formatI18n("/储值/会员储值/储值支付活动/编辑页面/请输入数量")));
			} else {
				callback();
			}
		};
		this.rules = [
			{
				validator: validatePass,
				trigger: "change",
			},
		];
		if (this.value && this.value.length > 0) {
			this.formData.receiveParamsArray = [];
			this.receiveNameArray = [];
			this.value.forEach((item: CouponItem) => {
				this.receiveNameArray.push(item.coupons!.name);
				this.formData.receiveParamsArray.push(item);
			});
		}
    this.firstShowTemplatePrice = this.showTemplatePrice
	}
	validate1() {
		let p0 = new Promise<void>((resolve, reject) => {
			this.$refs.addCoupons.validate((valid: any) => {
				if (valid) {
					console.log(valid);
					resolve();
				}
			});
		});
		return p0;
	}
	doCardTemplateSelected(arr: CouponTemplate[]) {
		console.log(arr);

		if (arr && arr.length > 0) {
			this.receiveNameArray = [];
      const newCouponArr: CouponItem[] = []
			arr.forEach((item: CouponTemplate) => {
				let couponItem: CouponItem = new CouponItem();
				couponItem.coupons = new CouponInfo();
				couponItem.coupons.name = item.name;
				couponItem.coupons.templateId = item.number;
				// @ts-ignore
				couponItem.qty = item.qty || 1;
				// @ts-ignore
				couponItem.enableApplyDelay = item.enableApplyDelay === undefined? true : item.enableApplyDelay
        couponItem.templatePrice = this.formData.receiveParamsArray?.find((val: CouponItem) => val.coupons?.templateId === item.number)?.templatePrice || null
        newCouponArr.push(couponItem);
				this.receiveNameArray.push(item.name);
			});
      this.formData.receiveParamsArray = newCouponArr
			this.$emit("input", this.formData.receiveParamsArray);
			this.$emit("change", this.formData.receiveParamsArray);
		}
	}
	doEditStoreValueActive(index: number) {
		CouponTemplateApi.detail(this.formData.receiveParamsArray[index].coupons.templateId as string)
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					this.child.coupons = resp.data;
					this.couponTemplateDtlDialogFlag = true;
				}
			})
			.catch((error: any) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}
	doAddCoupon(index: number, selectType: any = 'multiple') {
		console.log(this.value);
		let arr: CouponTemplate[] = [];
		if (this.value && this.value.length > 0) {
			this.value.forEach((item: CouponItem) => {
				let obj: CouponTemplate = new CouponTemplate();
				obj.number = item.coupons!.templateId;
				obj.name = item.coupons!.name;
				// @ts-ignore
				obj.qty = item.qty;
				// @ts-ignore
				obj.enableApplyDelay = item.enableApplyDelay
				arr.push(obj);
			});
		}
		this.$refs.couponTemplate.open(arr, selectType);
	}
	doQtyChange(item: any, index: number) {
		item.qty = AmountToFixUtil.formatNumber(item.qty, this.maxCouponItemLimit, 1);
	}
	doDialogClose() {
		this.couponTemplateDtlDialogFlag = false;
	}
	doDelete(index: number) {
		this.formData.receiveParamsArray.splice(index, 1);
		this.$emit("input", this.formData.receiveParamsArray);
		this.$emit("change", this.formData.receiveParamsArray);
	}

	commitChange() {
		this.$emit("input", this.formData.receiveParamsArray);
		this.$emit("change", this.formData.receiveParamsArray);
	}
}