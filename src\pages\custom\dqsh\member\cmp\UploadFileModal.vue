<template>
    <el-dialog :before-close="doBeforeClose" :close-on-click-modal="false"  :visible.sync="dialogShow" class="upload-file-dialog" title="导入会员">
        <div class="wrap">
            <FormItem label="实例模板">
                <!--<el-select v-model="templateType">-->
                    <!--<el-option label="会员资料" value="member_upload">会员资料</el-option>-->
                    <!--<el-option label="实体卡号" value="card_member_upload">实体卡号</el-option>-->
                <!--</el-select>-->
                <div style="line-height: 36px">会员资料</div>
            </FormItem>
            <FormItem label="">
                实例模板&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <a
                    class="action-hover_download" @click="downloadTemplate"
                        style="line-height: 12px; color: #318BFF;font-size: 12px;text-decoration: none">会员资料模板</a>
            </FormItem>
            <FormItem label="选择文件">
                <div style="position: relative;top: 9px; margin-bottom: 20px;color: #9a9fa8;
    font-size: 12px;">为保障上传成功，建议每次最多上传5000条信息</div>
                <el-upload
                        :headers="uploadHeaders"
                        :action="getUploadUrl"
                        :auto-upload="false"
                        :on-change="doHandleChange"
                        :on-error="getErrorInfo"
                        :on-success="getSuccessInfo"
                        :with-credentials="true"
                        class="upload-demo"
                        ref="upload">
                    <el-button size="small" slot="trigger" type="default">选取文件</el-button>
                </el-upload>
            </FormItem>
        </div>
        <div class="dialog-footer" slot="footer">
            <el-button @click="doModalClose('cancel')">取 消</el-button>
            <el-button @click="doModalClose('confirm')" size="small" type="primary">确认导入</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts" src="./UploadFileModal.ts">
</script>

<style lang="scss">
.upload-file-dialog{
    display: flex;
    justify-content: center;
    align-items: center;
    .wrap{
        margin-top: 30px;
    }
    .el-dialog{
        width: 600px;
        height: 400px;
        margin: 0 !important;
    }
    .el-dialog .el-dialog__body{
        height: 255px;
    }
}

.action-hover_download {
  cursor: pointer;
  /* 添加手型光标 */
}
</style>