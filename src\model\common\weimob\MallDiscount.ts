/*
 * @Author: 黎钰龙
 * @Date: 2023-07-26 16:10:24
 * @LastEditTime: 2023-07-26 16:12:55
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\common\weimob\MallDiscount.ts
 * 记得注释
 */
export default function MallDiscount (i18n: Function) {
  return [
    {
      key: i18n('满减满折'),
      value: 1
    },
    {
      key: i18n('第X件X折'),
      value: 2
    },
    {
      key: i18n('N元N件'),
      value: 6
    },
    {
      key: i18n('订单满赠'),
      value: 9
    },
    {
      key: i18n('直播价'),
      value: 26
    },
    {
      key: i18n('满减邮'),
      value: 11
    },
    {
      key: i18n('限时折扣'),
      value: 12
    },
    {
      key: i18n('限量抢购'),
      value: 14
    },
    {
      key: i18n('定金膨胀'),
      value: 15
    },
    {
      key: i18n('阶梯价'),
      value: 16
    },
    {
      key: i18n('特权价'),
      value: 19
    },
    {
      key: i18n('企业内购'),
      value: 20
    },
    {
      key: i18n('拼团'),
      value: 21
    },
    {
      key: i18n('砍价'),
      value: 22
    },
    {
      key: i18n('会员价'),
      value: 18
    },
    {
      key: i18n('单品换购'),
      value: 17
    },
    {
      key: i18n('订单换购'),
      value: 10
    },
    {
      key: i18n('固定套装'),
      value: 7
    },
    {
      key: i18n('搭配套装'),
      value: 8
    },
    {
      key: i18n('裂变内购'),
      value: 31
    },
    {
      key: i18n('买单优惠'),
      value: 32
    },
    {
      key: i18n('买M付N'),
      value: 35
    },
  ]
}