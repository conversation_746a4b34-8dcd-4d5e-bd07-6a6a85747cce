import MutableNsid from 'model/common/MutableNsid'

export default class PrepayAdjustBillLine {
    // 会员手机号
    mobile: Nullable<string> = null
    // 会员号
    hdCardMbrId: Nullable<string> = null
    // 实体卡号
    hdCardCardNum: Nullable<string> = null
    // 行号
    lineNo: Nullable<number> = null
    // 所属会员信息
    accountOwner: Nullable<MutableNsid> = null
    // 账户名id
    accountId: Nullable<string> = null
    // 账户名
    accountName: Nullable<string> = null
    // 原实充金额
    oldAmount: Nullable<number> = null
    // 原返现金额
    oldGiftAmount: Nullable<number> = null
    // 调整实充金额
    occurAmount: Nullable<number> = null
    // 调整返现金额
    occurGiftAmount: Nullable<number> = null
  // 储值调整金额只用于前端展示
  totalAmount: Nullable<number> = null
  // 调整原因
  reason: Nullable<string> = null
  // 备注
  remark: Nullable<string> = null
  // 状态INITIAL：未审核；SUCCESS：成功；FAIL：失败
  state: Nullable<string> = null
}