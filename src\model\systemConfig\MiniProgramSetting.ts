import AccountSettingConfig from './AccountSettingConfig'
import BMemberFreezeConfig from './BMemberFreezeConfig'
import MemberAgreementConfig from '../system/BMemberAgreementConfig'
import PersonalDataConfig from './PersonalDataConfig'
import CmsConfig from './CmsConfig'
import MemberRegisterConfig from './MemberRegisterConfig'

export default class MiniProgramSetting {
  // 账户设置
  accountSettingConfig: Nullable<AccountSettingConfig> = null
  // 个人资料设置
  // personalDataConfig: Nullable<PersonalDataConfig> = null
  // 会员冻结
  memberFreezeConfig: Nullable<BMemberFreezeConfig> = null
  // 会员协议
  memberAgreementConfig: Nullable<MemberAgreementConfig> = null
  // 是否对接短信平台
  hasSmsPlatform: Nullable<boolean> = null
    // 注册设置
  memberRegisterConfig: Nullable<MemberRegisterConfig> = null
   // 投放设置
  cmsConfig: Nullable<CmsConfig> = null
}