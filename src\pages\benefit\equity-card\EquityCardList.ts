/*
 * @Author: liyulong <EMAIL>
 * @Date: 2023-03-03 22:01:01
 * @LastEditors: 黎钰龙
 * @LastEditTime: 2024-04-24 13:36:32
 * @FilePath: \phoenix-web-ui\src\pages\benefit\equity-card\EquityCardList.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import ListWrapper from 'cmp/list/ListWrapper';
import I18nPage from 'common/I18nDecorator';
import ChannelManagementApi from 'http/channelmanagement/ChannelManagementApi';
import CouponTemplateApi from 'http/coupon/template/CouponTemplateApi';
import EquityCardApi from 'http/equityCard/equityCardApi';
import BEquityCardTemplate from 'model/benefit/BEquityCardTemplate';
import RSChannelManagement from 'model/common/RSChannelManagement';
import RSChannelManagementFilter from 'model/common/RSChannelManagementFilter';
import BEquityCardExpiryRule from 'model/equityCard/BEquityCardExpiryRule';
import SelectStoreActiveDtlDialog from 'pages/deposit/mbrdeposit/active/dialog/SelectStoreActiveDtlDialog';
import { Component, Vue } from 'vue-property-decorator';
import EquityCardPermission from './EquityCardPermission';
@Component({
  name: 'EquityCardList',
  components: {
    BreadCrume,
    ListWrapper,
    SelectStoreActiveDtlDialog
  }
})

  @I18nPage({
    auto: false,
    prefix: [
      '/会员/权益卡',
      '/会员/权益卡设置'
    ]
  })
export default class EquityCardList extends Vue {
  panelArray: Array<any> = []
  cardList: BEquityCardTemplate[] = [] //权益卡列表
  dialogShow: Boolean = false //券模板详细弹窗
  child: any
  permission = new EquityCardPermission()
  receiveCardChannel: RSChannelManagement[] = []  //领卡渠道列表

  created() {
    this.panelArray = [
      {
        name: this.i18n('/公用/菜单/权益卡设置'),
        url: ''
      }
    ]
    this.queryList()
    this.getCardChannel()
  }

  queryList() {
    EquityCardApi.query().then(res => {
      if (res && res.code === 2000) {
        this.cardList = res.data || []
      }
    })
  }

  //新建权益卡
  doCreateCard() {
    this.$router.push({
      name: 'equity-card-edit'
    })
  }

  //有效期设置字段
  ValidityTime(item: BEquityCardExpiryRule) {
    let str = ''
    const priedType = item.validityInfo?.expiryType
    str += `${item.price} ${this.i18n('/公用/券模板/元')}，`
    str += item.validityInfo?.validityDays! + (priedType === "DAYS" ? ` ${this.i18n('/公用/券模板/天')}，` : priedType === "MONTHS" ? ` ${this.i18n('/公用/券模板/月')}，` : ` ${this.i18n('/储值/预付卡/卡模板/编辑页面/年')}，`)
    str += (item.renewPrice == item.price ? this.i18n('续费无优惠') : this.i18n('续费{0}元').replace(/\{0\}/g, String(item.renewPrice)))
    return str
  }

  //是否有领卡奖励
  hasGiftBag(item:any) {
    return item.channels.length > 0 || item.giftBag.points > 0 || item.giftBag.coupons.length > 0
  }

  //查询领卡渠道
  getCardChannel() {
    let param: RSChannelManagementFilter = new RSChannelManagementFilter();
    ChannelManagementApi.query(param).then((res) => {
      if (res.code === 2000) {
        this.receiveCardChannel = res.data!
      } else {
        throw new Error(res.msg!)
      }
    }).catch(err => {
      this.$message.error(err.message)
    })
  }

  getChannelName(typeId: string) {
    const channel = this.receiveCardChannel.find(item=>{
      return String(item.channel?.id) + String(item.channel?.type) === typeId
    })
    return channel?.name
  }

  //状态
  showState(state: string) {
    let str = ''
    switch (state) {
      case 'start':
        str = this.i18n('/公用/按钮/启用')
        break;
      case 'stop':
        str = this.i18n('/设置/权限/用户管理/功基本信息/停用')
        break;
      default:
        break;
    }
    return str
  }

  limitPartakeDateType(type: string,weekDay:any,interval: string) {
    let weekCurDay
    switch (weekDay) {
      case 1:
        weekCurDay = this.i18n('/公用/券模板/周一')
        break;
      case 2:
        weekCurDay = this.i18n('/公用/券模板/周二')
        break;
      case 3:
        weekCurDay = this.i18n('/公用/券模板/周三')
        break;
      case 4:
        weekCurDay = this.i18n('/公用/券模板/周四')
        break;
      case 5:
        weekCurDay = this.i18n('/公用/券模板/周五')
        break;
      case 6:
        weekCurDay = this.i18n('/公用/券模板/周六')
        break;
      case 7:
        weekCurDay = this.i18n('/公用/券模板/周日')
        break;
      default:
        break;
    }
    return type === 'MONTH' ? interval + this.i18n('个自然月') : type === 'DAY' ? interval + this.i18n('/公用/券模板/天') : type === 'WEEK' ? interval + this.i18n('/公用/日期/周') + ' ' +weekCurDay : ''
  }

  //编辑权益卡
  doEdit(index: any) {
    this.$router.push({
      name: `equity-card-edit`,
      query: {
        type: 'edit',
        item: JSON.stringify(this.cardList[index])
      }
    })
  }

  //启用、禁用权益卡
  doSwitchState(index: any) {
    const item = this.cardList[index]
    const state = item.state
    const msg = state === 'start' ? this.i18n('停用后，顾客将不能继续领取该卡，但不影响已领卡的会员享受权益') : this.i18n('启用后，顾客将能继续领取该卡')
    const title = state === 'start' ? this.i18n('/设置/权限/用户管理/功基本信息/停用') : this.i18n('/设置/权限/用户管理/功基本信息/启用')
    this.$confirm(msg, title, {
      confirmButtonText: this.i18n('/公用/按钮/确定'),
      cancelButtonText: this.i18n('/公用/按钮/取消'),
    }).then(()=>{
      EquityCardApi.invocate(item.number!, state!).then((res) => {
        if (res.code === 2000 && state === 'stop') {
          this.$message.success(this.i18n('/公用/活动/提示信息/启用成功'))
        } else if (res.code === 2000 && state === 'start') {
          this.$message.success(this.i18n('/公用/活动/提示信息/禁用成功'))
        }
        this.queryList()
      }).catch(err => {
        this.$message.error(err.message)
      })
    })
  }

  doShowDialog(child:any) {
    CouponTemplateApi.detail(child.couponTemplateNumber).then(res=>{
      if(res.code === 2000) {
        this.child = {
          coupons: res.data,
          qty: child.qty
        }
        this.dialogShow = true
      }
    }).catch((err)=>{
      this.$message.error(err.message)
    })
  }

  doDialogClose() {
    this.dialogShow = false
  }
};