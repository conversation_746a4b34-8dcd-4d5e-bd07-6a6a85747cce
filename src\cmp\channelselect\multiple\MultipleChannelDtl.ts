import {Component, Prop, Vue} from "vue-property-decorator";
import ChannelRange from "model/common/ChannelRange";
import Channel from "model/common/Channel";
import ChannelManagementFilter from "model/channel/ChannelManagementFilter";
import {ChannelState} from "model/channel/ChannelState";
import ChannelManagement<PERSON>pi from "http/channelmanagement/ChannelManagementApi";
import {ChannelRangeType} from "model/common/ChannelRangeType";


@Component({
  name: 'MultipleChannelDtl',
  components: {}
})
export default class MultipleChannelDtl extends Vue {
  $refs: any
  channelMap: any = {}
  @Prop()
  source: ChannelRange

  @Prop({
    required: false,
    default: false
  })
  ignoreType: boolean


  created() {
    this.getChannelList()
  }

  getChannelList() {
    let query = new ChannelManagementFilter()
    query.stateEquals = ChannelState.ENABLED
    ChannelManagementApi.query(query).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.channelMap = {}
        for (let item of resp.data) {
          this.channelMap[item.channel.type + item.channel.id] = item.name
        }
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  get getType() {
    if (this.source) {
      let type = this.source.channelRangeType
      if (type === ChannelRangeType.ALL) {
        return this.formatI18n('/公用/券模板/用券渠道', '全部渠道')
      } else if (type === ChannelRangeType.PART) {
        return this.formatI18n('/公用/券模板/用券渠道', '指定渠道适用')
      } else if (type === ChannelRangeType.EXCLUDE) {
        return this.formatI18n('/公用/券模板/用券渠道', '指定渠道不适用')
      }
    }
    return ''
  }

  get getSource() {
    let str = ''
    if (this.channelMap && this.source && this.source.channels.length > 0) {
      this.source.channels.forEach((item: Channel) => {
        if (item.type) {
          str += this.channelMap[item.type + item.id] + '、'
        } else {
          str += '--'
        }
      })
      str = str.substring(0, str.length - 1)
    }
    return str
    // else {
    //   return this.formatI18n('/资料/渠道/线下POS')
    // }
  }

}