import {Component, Prop, Vue} from 'vue-property-decorator'

@Component({
    name: 'FloatBlock'
})
export default class FloatBlock extends Vue {
    isFloat: boolean = false

    @Prop({
        type: Number,
        default: 64
    })
    top: number // 浮动后相对于浏览器顶部的距离

    @Prop({
        type: String
    })
    refClass: string // 监听滚动的元素类名

    @Prop({
        type: String
    })
    refId: string // 监听滚动的ID

    mounted() {
        window.onresize = () => {
            this.autoFloat()
        };
        document.getElementById('foo')!.style.height = document.getElementById('float-block')!.clientHeight + 'px'
        if (this.refId) {
            let element = document.getElementById(this.refId)
            if (element) {
                element.addEventListener('scroll', this.autoFloat)
            }
        } else if (this.refClass) {
            let element = document.getElementsByClassName(this.refClass)[0]
            if (element) {
                element.addEventListener('scroll', this.autoFloat)
            }
        }
    }

    autoFloat() {
        let block = document.getElementById('float-block') as any
        if (!block) {
            return
        }
        block.style.width = block.parentNode.getBoundingClientRect().width + 'px'
        if (block.getBoundingClientRect().y < this.top) {
            this.isFloat = true
        }
        if (block.parentNode.getBoundingClientRect().y > this.top) {
            this.isFloat = false
        }

    }
}
