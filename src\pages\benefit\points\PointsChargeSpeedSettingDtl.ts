import {Component, Vue} from 'vue-property-decorator'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import FormItem from 'cmp/formitem/FormItem.vue'
import MemberDayPointsChargeActivityApi from 'http/points/init/memberdaypointscharge/MemberDayPointsChargeActivityApi'
import MemberDayPointsChargeRule from 'model/points/init/memberdaypointscharge/MemberDayPointsChargeRule'
import PointsChargeMetaActivityApi from 'http/points/init/pointschargemeta/PointsChargeMetaActivityApi'
import ConstantMgr from 'mgr/ConstantMgr'

@Component({
  name: 'PointsChargeSpeedSettingDtl',
  components: {
    BreadCrume,
    FormItem
  }
})
export default class PointsChargeSpeedSettingDtl extends Vue {
  id = ''
  score = ''
  amount = ''
  dtl: MemberDayPointsChargeRule = new MemberDayPointsChargeRule()
  panelArray: any = []
  weeks: string[] = []
  created() {
    this.panelArray = [
			{
        name: this.formatI18n("/公用/菜单", "节日有礼"),
				url: "score-init",
				back: "score-init"
			},
			{
				name: this.formatI18n("/权益/积分/积分初始化/未初始化状态/会员日积分抵现加速"),
				url: "",
			},
		];
    this.weeks = [
      this.formatI18n('/公用/券模板', '周一'),
      this.formatI18n('/公用/券模板', '周二'),
      this.formatI18n('/公用/券模板', '周三'),
      this.formatI18n('/公用/券模板', '周四'),
      this.formatI18n('/公用/券模板', '周五'),
      this.formatI18n('/公用/券模板', '周六'),
      this.formatI18n('/公用/券模板', '周日')
    ]
    this.getDtl()
    this.getInitDtl()
  }
  doEdit() {
    this.$router.push({ name: 'points-charge-speed-setting', query: { from: 'edit' } })
  }
  getDtl() {
    MemberDayPointsChargeActivityApi.detail().then((resp: any) => {
      if (resp && resp.code === 2000) {
        if (resp.data) {
          this.dtl = resp.data
        }
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  getInitDtl() {
    PointsChargeMetaActivityApi.detail().then((resp: any) => {
      if (resp && resp.code === 2000) {
        if (resp.data) {
          this.score = resp.data.points
          this.amount = resp.data.amount
        }
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  doBack() {
    this.$router.push({name: 'score-init'})
  }
  doEnabled() {
    const loading = this.$loading(ConstantMgr.loadingOption)
    MemberDayPointsChargeActivityApi.switchMbrDayPtsChargeRule(false).then((resp: any) => {
      if (resp && resp.code === 2000) {
        loading.close()
        this.getDtl()
      }
      this.$message.success(this.formatI18n('/设置/权限/用户管理/功基本信息/启用', '启用成功') as string)
    }).catch((error: any) => {
      loading.close()
      this.$message.error(error.message)
    })
  }
  getTip(score: string, amount: string) {
    let str = this.formatI18n('/权益/积分/积分初始化/未初始化状态/积分抵现规则/会员日积分加速抵现/点击立即设置/当前基础积分抵现权益：每使用{0}积分，抵现{1}元，不满{2}积分不能抵现')
    str = str.replace(/\{0\}/g, score)
    str = str.replace(/\{1\}/g, Number(amount).toFixed(2))
    str = str.replace(/\{2\}/g, score)
    return str
  }
  getSettingByMonth(day: string, points: string, amount: string) {
    let str = this.formatI18n('/权益/积分/积分初始化/未初始化状态/积分抵现规则/会员日积分加速抵现/点击立即设置/抵现方式/按月/每月{0}日，每使用{1}积分，抵现{2}元')
    str = str.replace(/\{0\}/g, day)
    str = str.replace(/\{1\}/g, points)
    str = str.replace(/\{2\}/g, Number(amount).toFixed(2))
    return str
  }
  getSettingByWeek(day: string, points: string, amount: string) {
    let str = this.formatI18n('/权益//权益/积分/积分初始化/未初始化状态/积分抵现规则/会员日积分加速抵现/点击立即设置/抵现方式/按周/每周{0}日，每使用{1}积分，抵现{2}元')
    str = str.replace(/\{0\}/g, day)
    str = str.replace(/\{1\}/g, points)
    str = str.replace(/\{2\}/g, Number(amount).toFixed(2))
    return str
  }
  doStoped() {
    const loading = this.$loading(ConstantMgr.loadingOption)
    MemberDayPointsChargeActivityApi.switchMbrDayPtsChargeRule(true).then((resp: any) => {
      if (resp && resp.code === 2000) {
        loading.close()
        this.getDtl()
        this.$message.success(this.formatI18n('/设置/权限/用户管理/功基本信息/停用/停用成功'))
      }
    }).catch((error: any) => {
      loading.close()
      this.$message.error(error.message)
    })
  }
}