import ApiClient from 'http/ApiClient'
import PrepayAccount from 'model/prepay/balance/PrepayAccount'
import PrepayAccountFilter from 'model/prepay/balance/PrepayAccountFilter'
import PrepayAccountHst from 'model/prepay/balance/PrepayAccountHst'
import PrepayAccountTransaction from 'model/prepay/balance/PrepayAccountTransaction'
import PrepayAccountTransactionFilter from 'model/prepay/balance/PrepayAccountTransactionFilter'
import Response from 'model/common/Response'

export default class PrePayBalanceApi {
  /**
   * 查询
   *
   */
  static query(body: PrepayAccountFilter): Promise<Response<PrepayAccount[]>> {
    return ApiClient.server().post(`/v1/prepay/balance/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询会员储值流水
   *
   */
  static queryHst(body: PrepayAccountTransactionFilter): Promise<Response<PrepayAccountTransaction[]>> {
    return ApiClient.server().post(`/v1/prepay/balance/queryHst`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询会员储值汇总
   *
   */
  static querySum(uuid: string): Promise<Response<PrepayAccountHst>> {
    return ApiClient.server().post(`/v1/prepay/balance/querySum`, {}, {
      params: {
        uuid: uuid,
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导出储值账户报表
   *
   */
  static exportAccount(body: PrepayAccountFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/prepay/balance/exportAccount`, body, {}).then((res) => {
      return res.data
    })
  }
}
