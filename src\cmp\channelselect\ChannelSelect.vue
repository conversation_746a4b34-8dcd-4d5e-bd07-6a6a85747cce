<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:02
 * @LastEditTime: 2024-10-10 11:55:00
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\channelselect\ChannelSelect.vue
 * 记得注释
-->
<template>
  <div>
    <el-select :disabled="disabled" v-model="multiSelected" v-if="multiple" multiple :style="{width: width}"
      :placeholder="placeholder ? placeholder : formatI18n('/资料/渠道/请至少选择一个渠道')" @change="onChange">
      <el-option v-for="channel of channels" :key="channel.channel.type + channel.channel.id" :value="channel.channel.type + channel.channel.id"
        :label="channel.name" v-show="filterHideId.indexOf(channel.channel.type + channel.channel.id) === -1">
        {{channel.name}}
      </el-option>
    </el-select>
    <el-select :clearable="clearable" :disabled="disabled" v-model="selected" v-else :style="{width: width}"
      :placeholder="placeholder ? placeholder : formatI18n('/资料/渠道/请选择活动渠道')" @change="onChange">
      <el-option :value="null" :label="formatI18n('/公用/下拉框/提示/全部')" v-if="isShowAll"></el-option>
      <el-option v-for="channel of channels" :key="channel.channel.type + channel.channel.id" :value="channel.channel.type + channel.channel.id"
        :label="channel.name" v-show="filterHideId.indexOf(channel.channel.type + channel.channel.id) === -1">
        {{channel.name}}
      </el-option>
    </el-select>
  </div>
</template>

<script lang="ts" src="./ChannelSelect.ts">
</script>

<style lang="scss">
</style>