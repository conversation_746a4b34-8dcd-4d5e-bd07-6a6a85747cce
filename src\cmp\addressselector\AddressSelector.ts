import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import DistrictFilter from "model/district/DistrictFilter";
import DistrictApi from "http/district/DistrictApi";
import District from "model/district/District";
import IdName from "model/common/IdName";
import I18nPage from "common/I18nDecorator";

class Option {
  label: string = ''
  value: string = ''
  type: string = ''
  parent: string = ''
  children: Option[] | null = null
}

@Component({
  name: 'AddressSelector',
})
@I18nPage({
  auto: false,
  prefix: [
  ],
})
export default class AddressSelector extends Vue {
  @Prop({
    default: false
  })
  full: boolean // 选满
  @Prop({
    default: true
  })
  clearable: boolean
  @Prop({
    required: true,
    default: 'IdName'
  })
  valueType: 'District' | 'IdName'
  @Prop({
    default: 'STREET'
  })
  lastType: string // 只能选到某个级别
  @Prop()
  value: any // 绑定的值支持District和IdName类型，但是最终转为valueCopy时应该都为District类型
  private valueCopy: string[] = [] // 组件内部的主数据
  private options: Option[] = [] // 选项数据
  private valueHandler = new Map<string, any>()
  private nodeIndex = new Map<string, Option>() // 已加载的节点索引，code->Option
  private districtIndex = new Map<string, District>() // 已加载的节点索引，code->Option
  private clearFlag = false // 为True时显示清除按钮
  private execFlag = false // 初始化方法只执行一次

  @Watch('value')
  watchValue(value: IdName[]) {
    this.getValueHandler().watch(value)
    this.bindValue()
  }

  created() {
    this.initValueHandler()
    // 初始化省份，也就是一级节点
    this.initProvince()
  }

  // 回显地址信息
  async bindValue() {
    const value = this.value
    if (value && value.length > 0) {
      const levels = [
        { id: value[0]?.id, type: 'CITY' },
        { id: value[1]?.id, type: 'COUNTY' },
        { id: value[2]?.id, type: null }
      ];
      let currentOptions = this.options;  // 当前加载的节点
      for (let i = 0; i < levels.length; i++) {
        const { id, type } = levels[i];
        if (!id) break;
        const index = currentOptions.findIndex(e => e.value === id);
        if (index === -1) break;
        const currentNode = currentOptions[index];
        if (currentNode.children?.length) {
          // 已经加载过，直接进入下一层
          currentOptions = currentNode.children;
          continue;
        }
        // 加载下一级节点
        const nodes = await this.fetchNodes(id, type);
        currentNode.children = nodes as Option[];
        currentOptions = nodes as Option[];
      }
    }
  }

  private initProvince() {
    this.fetchNodes(null, 'PROVINCE').then((nodes: Option[]) => {
      this.options = nodes
      this.getValueHandler().watch(this.value)
    })
  }

  /** 为每种数据类型初始化值处理器，包含监听处理器和数据提交处理器 */
  private initValueHandler() {
    let that = this
    this.valueHandler.set('IdName', {
      watch(value: any[]) {
        let idNames: IdName[] = JSON.parse(JSON.stringify(value))
        // idNames[0]是type=PROVINCE的根节点，在此之前已经加载了。
        if (idNames && idNames.length > 1) {
          let selectedCodes = idNames.map((e) => e?.id + '')
          selectedCodes.splice(selectedCodes.length - 1, 1)
          // that.initSelectedOptionsRoute(selectedCodes).then(() => {
          that.valueCopy = idNames.map((e) => e?.id) as any
          // })
        } else {
          that.valueCopy = idNames?.map((e) => e?.id) as any
        }
      },
      submit(codes: string[]) {
        return codes.map((e) => {
          let district = that.districtIndex.get(e) as District
          return {
            id: district.code,
            name: district.name
          }
        })
      }
    })
    this.valueHandler.set('District', {
      watch(value: any[]) {
        this.valueCopy = JSON.parse(JSON.stringify(value))
      },
      submit(codes: string[]) {
        return codes.map((e) => that.nodeIndex.get(e))
      }
    })
  }

  /** 根据已选择的节点初始化相关的数据，整条路线的数据要加载出来，才能正常回显已选择的地址。 */
  private initSelectedOptionsRoute(selectedCodes: string[]) {
    if (this.execFlag) {
      return Promise.resolve()
    }
    return new Promise<void>((resolve, reject) => {
      Promise.all(selectedCodes.map((e) => this.fetchNodes(e, null))).then((res: Option[][]) => {
        for (let child of res) {
          if (child && child.length > 0) {
            let pnode = this.nodeIndex.get(child[0].parent)
            if (pnode) {
              pnode.children = child
            }
          }
        }
        this.execFlag = true
        resolve()
      }).catch((e) => {
        reject(e)
      })
    })
  }

  private getValueHandler() {
    if (!this.valueHandler.has(this.valueType)) {
      throw new Error(this.valueType + '的值处理器未声明')
    }
    return this.valueHandler.get(this.valueType)
  }

  private submit() {
    this.$emit('input', this.getValueHandler().submit(this.valueCopy))
  }

  private doCascadeOver() {
    if (this.valueCopy && this.valueCopy.length > 0) {
      this.clearFlag = true
    }
  }

  private doCascadeLeave() {
    this.clearFlag = false
  }

  private doClearAddress() {
    this.valueCopy = []
    this.submit()
  }

  /**
   * 初始化某个节点的子节点
   * @param parent 父节点，初始化根节点时传"0"
   * @param type 类型等于，取值：PROVINCE——；CITY——市；COUNTY——区；STREET——街道
   */
  private fetchNodes(parent: string | null, type: string | null) {
    if (type === this.lastType) {
      return Promise.resolve()
    }
    return new Promise((resolve, reject) => {
      let params: DistrictFilter = new DistrictFilter()
      params.parentEquals = parent
      params.typeEquals = type
      DistrictApi.query(params).then((resp) => {
        if (resp && resp.code === 2000 && resp.data) {
          let nodes: Option[] = resp.data.map((item) => {
            let node = {
              parent: item.parent + '',
              label: item.name + '',
              value: item.code + '',
              type: item.type + '',
              children: item.type === this.lastType ? null : []
            }
            if (item.code) { // 索引已加载节点
              this.nodeIndex.set(item.code, node)
              this.districtIndex.set(item.code, item)
            }
            return node
          })
          resolve(nodes)
        }
      }).catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message)
          reject(error)
        }
      })
    })
  }

  private doHandleChange(value: string[]) {
    if (value && value.length > 0) {
      let lastNodeCode = value[value.length - 1]
      const node = this.nodeIndex.get(lastNodeCode)
      if (node && (!node.children || node.children.length === 0) && this.lastType !== node.type) {
        // 当选中的节点无子节点时，尝试加载子节点
        this.fetchNodes(lastNodeCode, null).then((childNodes: Option[]) => {
          if (childNodes && childNodes.length > 0) {
            node.children = childNodes
          }
        })
      }
      if (this.full && value.length === 4) {
        this.valueCopy = value
        this.submit()
      }
      if (!this.full) {
        this.valueCopy = value
        this.submit()
      }
    }
  }
}
