import Channel from "model/common/Channel";

export default class CouponFilter {
	// 日期范围时间段类型:TODAY-今天，LST_SEVEN_DAY-近七天，LST_THIRTY_DAY-近30天，LST_NINETY_DAY-近90天
	dateRangeType: Nullable<string> = null;
	// 券号等于
	codeEquals: Nullable<string> = null;
	// 外部券号等于
	outerCouponCodeEquals: Nullable<string> = null;
	// 外部券号等于
	outerIdEquals: Nullable<string> = null;
	// 券号类似于
	codeLikes: Nullable<string> = null;
	// 券名称类似于
	couponNameLikes: Nullable<string> = null;
	// 券名称起始于
	couponNameStartsWith: Nullable<string> = null;
	// 会员号，手机号，实体卡号等于
	memberIdentEquals: Nullable<string> = null;
	// 发券时间开始
	dateBegin: Nullable<Date> = null;
	// 区域
	zoneIdEquals: Nullable<Date> = null;
	// 发券时间结束
	dateEnd: Nullable<Date> = null;
	// 用券时间开始
	useBegin: Nullable<Date> = null;
	// 用券时间结束
	useEnd: Nullable<Date> = null;
	// 用券时间开始
	useCouponBegin: Nullable<Date> = null;
	// 用券时间结束
	useCouponEnd: Nullable<Date> = null;
	// 活动代码类似于
	activityNumberLikes: Nullable<string> = null;
	// 活动代码等于
	activityNumberEquals: Nullable<string> = null;
	// 活动名称类似于
	activityNameLikes: Nullable<string> = null;
	// 活动名称起始于
	activityNameStartsWith: Nullable<string> = null;
	// 状态等于
	stateEquals: Nullable<string> = null;
	// 用券交易号、交易号等于
	tranNoEquals: Nullable<string> = null;
	// 用券交易号、交易号类似于
	tranNoLikes: Nullable<string> = null;
	// 用券门店、发生组织等于
	storeEquals: Nullable<string> = null;
	// 用券门店名称、发生组织名称等于
	storeNameLikes: Nullable<string> = null;
	// 发券数量大于等于
	issueCountGraterOrEqual: Nullable<number> = null;
	// 发券门店等于
	issueOrgIdEquals: Nullable<string> = null;
	// 发券数量小于等于
	issueCountLessOrEqual: Nullable<number> = null;
  //购券交易号等于
  issueNumberEquals: Nullable<string> = null;
	// 用券数量大于等于
	useCountGraterOrEqual: Nullable<number> = null;
	// 用券数量小于等于
	useCountLessOrEqual: Nullable<number> = null;
	// 发券交易额、券抵用金额、发生交易额大于等于
	amountGraterOrEqual: Nullable<number> = null;
	// 发券交易额、券抵用金额、发生交易额小于等于
	amountLessOrEqual: Nullable<number> = null;
	// 抵扣金额大于等于
	deductAmountGraterOrEqual: Nullable<number> = null;
	// 抵扣金额小于等于
	deductAmountLessOrEqual: Nullable<number> = null;
	// 页码
	page: Nullable<number> = null;
	// 页面大小
	pageSize: Nullable<number> = null;
	//冲账类型等于USE/RETURN
	typeEquals: Nullable<string> = null;
	//券模板号等于
	templateNumberEquals: Nullable<string> = null;
	// 券渠道
	channelEquals: Nullable<Channel> = null;
	// 外部券码
	outCodeEquals: Nullable<string> = null;
	// 外部券码类似
	outCodeLikes: Nullable<string> = null;
	// 券类型
	couponTypeEquals: Nullable<string> = null
	// 不包含券类型
	typeExcluded?: Nullable<string[]> = null
  // 是否付费
  queryFree:  Nullable<boolean> = null
  // 券模板号包含
  templateNumberIn: Nullable<string[]> = null
	// 活动号包含
	activityNumberIn: Nullable<string[]> = null
}
