/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2023-09-18 17:51:03
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\mgr\ShortcutMgr.ts
 * 记得注释
 */
import router from '../router'
import {SessionStorage} from 'mgr/BrowserMgr.js'
import {LocalStorage} from 'mgr/BrowserMgr'
import store from 'store/index'

export default class ShortcutMgr {

  /**
   * 统一注销方法
   */
  public static clearStorage() {
    LocalStorage.clearItem('ucenterUser')
    LocalStorage.clearItem('permission')
    LocalStorage.clearItem('sysConfig')
    LocalStorage.clearItem('menus')
    LocalStorage.clearItem('fileBaseUrl')
    SessionStorage.clearItem('i18n')
    LocalStorage.clearItem('customer')
    LocalStorage.clearItem('subNavVisiable')
    LocalStorage.clearItem('locale')
    SessionStorage.clearItem("marketCenter")
    SessionStorage.clearItem("marketCenterName");
  }
  public static logout() {
    router.push('/login').then(() => {
      this.clearStorage()
    })
  }
  public static noLicense() {
    router.push('/no-lisence')
  }
}
