import Bread<PERSON>rume from 'cmp/bread-crumb/BreadCrume';
import FormItem from 'cmp/formitem/FormItem';
import ListWrapper from 'cmp/list/ListWrapper';
import MyQueryCmp from 'cmp/querycondition/MyQueryCmp';
import I18nPage from 'common/I18nDecorator';
import { Component, Vue } from 'vue-property-decorator';
import UploadBlacklist from './cmp/UploadBlacklist.vue';
import CreateBlackMember from './cmp/CreateBlackMember.vue';
import MemberBlacklistApi from 'http/promotion/MemberBlacklistApi';
import MemberBlacklistFilter from 'model/promotion/bigWheelActivity/MemberBlacklistFilter';
import MemberBlacklist from 'model/promotion/bigWheelActivity/MemberBlacklist';
import DownloadCenterDialog from 'pages/main/dowmload_center_dialog/DownloadCenterDialog';
import CommonUtil from 'util/CommonUtil';
@Component({
  name: 'Blacklist',
  components: {
    Bread<PERSON>rume,
    ListWrapper,
    MyQueryCmp,
    FormItem,
    UploadBlacklist,
    CreateBlackMember,
    DownloadCenterDialog
  }
})
@I18nPage({
  prefix: [
    '/公用/菜单',
    '/公用/券模板',
    '/会员/会员资料',
    '/会员/黑名单'
  ],
  auto: true
})
export default class Blacklist extends Vue {
  $refs: any
  selectedArr: MemberBlacklist[] = []
  tableData: MemberBlacklist[] = []
  query: MemberBlacklistFilter = new MemberBlacklistFilter()
  downloadShow: boolean = false
  page: any = {
    currentPage: 1,
    size: 20,
    total: 0,
  }

  get panelArray() {
    return [
      {
        name: this.i18n('黑名单')
      }
    ]
  }

  created() {
    this.queryList()
  }

  handleSelectionChange(val: any) {
    this.selectedArr = val
  }

  doReset() {
    this.$refs.table.clearSelection()
    this.page.currentPage = 1
    this.query = new MemberBlacklistFilter()
    this.queryList()
  }

  uploadSuccess() {
    this.doReset()
    this.downloadShow = true
  }

  doSearch() {
    this.page.currentPage = 1
    this.queryList()
  }

  queryList() {
    const params: MemberBlacklistFilter = JSON.parse(JSON.stringify(this.query))
    params.page = this.page.currentPage - 1
    params.pageSize = this.page.size
    const loading = CommonUtil.Loading()
    MemberBlacklistApi.query(params).then((res) => {
      if (res.code === 2000) {
        this.tableData = res.data || []
        this.page.total = res.total || 0
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    }).finally(() => {
      loading.close()
    })
  }

  doCreate() {
    this.$refs.createBlackMember.open()
  }

  doImport() {
    this.$refs.uploadBlacklist.open()
  }

  doRemove(uuid: string) {
    this.$confirm(this.i18n('移出后将无法恢复，顾客将有中奖机会，确定移出吗？'), this.i18n('移出黑名单'), {
      confirmButtonText: this.formatI18n('/权益/券/券模板/确定'),
      cancelButtonText: this.formatI18n('/权益/券/券模板/取消')
    }).then(() => {
      MemberBlacklistApi.remove(uuid).then((res) => {
        if (res.code === 2000) {
          this.$message.success(this.i18n('操作成功'))
          this.doReset()
        } else {
          throw new Error(res.msg!)
        }
      }).catch((error) => {
        this.$message.error(error.message)
      })
    })
  }

  doBatchExport() {
    const params: MemberBlacklistFilter = JSON.parse(JSON.stringify(this.query))
    MemberBlacklistApi.exportMemberBlacklist(params).then((res) => {
      if (res.code === 2000) {
        this.downloadShow = true
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    })
  }

  doBatchRemove() {
    if (!this.selectedArr.length) {
      return this.$message.warning(this.i18n('请先勾选单据'))
    }
    this.$confirm(this.i18n('移出后将无法恢复，顾客将有中奖机会，确定移出吗？'), this.i18n('移出黑名单'), {
      confirmButtonText: this.formatI18n('/权益/券/券模板/确定'),
      cancelButtonText: this.formatI18n('/权益/券/券模板/取消')
    }).then(() => {
      const arr = this.selectedArr.map((item) => item.uuid!)
      MemberBlacklistApi.batchRemove(arr).then((res) => {
        if (res.code === 2000) {
          this.$message.success(this.i18n('操作成功'))
          this.doReset()
        } else {
          throw new Error(res.msg!)
        }
      }).catch((error) => {
        this.$message.error(error.message)
      })
    })
  }

  doDialogClose() {
    this.downloadShow = false
  }

  onHandleCurrentChange = (val: number) => {
    this.page.currentPage = val;
    this.queryList();
  }

  onHandleSizeChange = (val: number) => {
    this.page.size = val;
    this.queryList();
  }
};