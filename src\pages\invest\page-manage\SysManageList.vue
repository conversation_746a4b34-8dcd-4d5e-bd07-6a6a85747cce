<!--
 * @Author: 黎钰龙
 * @Date: 2024-06-13 11:46:07
 * @LastEditTime: 2025-05-23 19:23:53
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\SysManageList.vue
 * 记得注释
-->
<template>
  <div style="width: 100%">
    <div class="content">
      <ListWrapper class="current-page">
        <template slot="btn">
          <!-- <el-button size="large" @click="batchEditShareInfo" v-if="hasOptionPermission('/设置/小程序装修/页面管理','编辑')">
            {{i18n('批量修改分享信息')}}
          </el-button> -->
        </template>
        <template slot="list">
          <!-- @selection-change="handleSelectionChange" -->
          <el-table :data="ContentTemplate" row-key="templateId"  ref="table"
            style="width: 100%; margin-top: 12px">
            <!-- <el-table-column type="selection" width="55"></el-table-column> -->
            <el-table-column :label="i18n('页面名称')">
              <template slot-scope="scope">
                {{ scope.row.name || "--" }}
              </template>
            </el-table-column>
            <el-table-column :label="i18n('页面状态')">
              <template slot="header">
                {{ i18n("页面状态") }}
                <el-tooltip class="item" effect="dark" :content="i18n('点击发布后，小程序端是5分钟内生效，不是立即生效')" placement="top">
                  <i class="el-icon-warning"></i>
                </el-tooltip>
              </template>
              <template slot-scope="scope">
                <span class="dot" :class="scope.row.state === 'unpublished' ? 'orange' : 'green'"></span>
                {{ scope.row.state === "unpublished" ? i18n('未发布') : i18n('已发布') || "--" }}
                <el-tooltip class="item" effect="dark" :content="i18n('存在页面内容已保存但未发布')" placement="top-start">
                  <i class="el-icon-warning" v-if="scope.row.hasDraft"></i>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column :label="i18n('最后修改时间')">
              <template slot-scope="scope">
                {{ scope.row.lastModified || "--" }}
              </template>
            </el-table-column>
            <el-table-column :label="i18n('操作')">
              <template slot-scope="scope">
                <span class="span-btn" @click.stop="doEdit(scope.row)" v-if="hasOptionPermission('/设置/小程序装修/页面管理','编辑')">
                  {{ i18n("编辑") }}
                </span>
                <span class="span-btn" style="margin-left: 4px" @click.stop="doPublish(scope.row)"
                  v-if="(scope.row.hasDraft === 'unpublished' || scope.row.hasDraft) && hasOptionPermission('/设置/小程序装修/页面管理','发布')">
                  {{ i18n("发布") }}
                </span>
                <span class="span-btn" style="margin-left: 10px" @click.stop="reset(scope.row)" v-if="hasOptionPermission('/设置/小程序装修/页面管理','编辑')">
                  {{ i18n("重置为系统默认") }}
                </span>
                <span @click.stop="doEditShare(scope.row)" v-if="hasOptionPermission('/设置/小程序装修/页面管理','编辑') && scope.row.placeName != 'memberCenter'" style="margin-left: 8px"
                  class="span-btn">
                  {{i18n("分享信息")}}
                </span>
                <!-- <span class="span-btn" style="margin-left:12px" @click.stop="doSpread(scope.row)" v-if="scope.row.state !== 'unpublished'">{{ i18n("推广") }}</span> -->
                <!-- <span class="span-btn" style="margin-left: 8px" @click.stop="doRemove(scope.row)">
                  {{ i18n("删除") }}
                </span> -->
              </template>
            </el-table-column>
          </el-table>
        </template>
        <template slot="page">
          <el-pagination :current-page.sync="page.currentPage" :page-size="page.size" :page-sizes="[20, 30, 40]" :total="page.total" background
            layout="total, prev, pager, next, sizes,  jumper" @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange"
            style="margin-top: 20px"></el-pagination>
        </template>
      </ListWrapper>
    </div>
    <ShareConfigDialog ref="shareConfigDialog" @submit="doChangeShareInfo"></ShareConfigDialog>
  </div>
</template>

<script lang="ts" src="./SysManageList.ts">
</script>

<style lang="scss" scoped>
.content {
  ::v-deep .list-wrapper-query {
    display: none;
  }
  ::v-deep .line-blank {
    display: none;
  }
  .dot {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }
  .orange {
    background: #ef821e;
  }
  .green {
    background: #58b929;
  }
}
</style>