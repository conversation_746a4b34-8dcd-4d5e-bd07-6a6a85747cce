import ApiClient from 'http/ApiClient'
import MemberExportFilter from 'model/v2/report/MemberExportFilter'
import OrgCycleReportExportFilter from 'model/v2/report/OrgCycleReportExportFilter'
import Response from 'model/common/Response'

export default class ReportExportApi {
  /**
   * 导出会员增长信息
   * 导出会员增长信息。
   *
   */
  static exportMemberIncrease(
    body: MemberExportFilter
  ): Promise<Response<void>> {
    return ApiClient.server()
      .post(`/v1/report/export/exportMemberIncrease`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 导出会员交易信息
   * 导出会员交易信息。
   *
   */
  static exportStoreMemberTrade(
    body: MemberExportFilter
  ): Promise<Response<void>> {
    return ApiClient.server()
      .post(`/v1/report/export/exportStoreMemberTrade`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 导出交易组织周期报表
   * 导出交易组织周期报表。
   *
   */
  static exportOrgCycleConsumption(
    body: OrgCycleReportExportFilter
  ): Promise<Response<void>> {
    return ApiClient.server()
      .post(`/v1/report/export/exportOrgCycleConsumption`, body, {})
      .then((res) => {
        return res.data
      })
  }
}
