import ApiClient from 'http/ApiClient'
import Response from 'model/default/Response'
import UserGroupCalculatorResult from 'model/UserGroupCalculatorTaskV2/UserGroupCalculatorResult'
import UserGroupCalculatorTaskV2 from 'model/UserGroupCalculatorTaskV2/UserGroupCalculatorTaskV2'
import UserGroupCalculatorTaskV2<PERSON>atchResult from 'model/UserGroupCalculatorTaskV2/UserGroupCalculatorTaskV2BatchResult'
import UserGroupCalculatorTaskV2Filter from 'model/UserGroupCalculatorTaskV2/UserGroupCalculatorTaskV2Filter'
import UserGroupCalculatorTaskV2SaveOrModifyRequest from 'model/UserGroupCalculatorTaskV2/UserGroupCalculatorTaskV2SaveOrModifyRequest'

export default class UserGroupCalculatorTaskV2Api {
  /**
   * 审核客群计算任务
   * 
   */
  static audit(uuid: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v2/precision-marketing/user-group/calculator/audit/${uuid}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量手动更新客群计算任务
   * 
   */
  static batchManualUpdate(body: Array<string>): Promise<Response<UserGroupCalculatorTaskV2BatchResult>> {
    return ApiClient.server().post(`/v2/precision-marketing/user-group/calculator/batchManualUpdate`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量恢复客群计算任务
   * 
   */
  static batchRecover(body: Array<string>): Promise<Response<UserGroupCalculatorTaskV2BatchResult>> {
    return ApiClient.server().post(`/v2/precision-marketing/user-group/calculator/batchRecover`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量删除客群计算任务
   * 
   */
  static batchRemove(body: Array<string>): Promise<Response<UserGroupCalculatorTaskV2BatchResult>> {
    return ApiClient.server().post(`/v2/precision-marketing/user-group/calculator/batchRemove`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取客群计算任务详情
   * 
   */
  static get(uuid: string): Promise<Response<UserGroupCalculatorTaskV2>> {
    return ApiClient.server().get(`/v2/precision-marketing/user-group/calculator/get/${uuid}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 手动更新客群计算任务
   * 
   */
  static manualUpdate(uuid: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v2/precision-marketing/user-group/calculator/manualUpdate/${uuid}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /** 
   * 分页查询客群计算任务
   * 
   */
  static query(body: UserGroupCalculatorTaskV2Filter): Promise<Response<UserGroupCalculatorResult>> {
    return ApiClient.server().post(`/v2/precision-marketing/user-group/calculator/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 恢复客群计算任务
   * 
   */
  static recover(uuid: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v2/precision-marketing/user-group/calculator/recover/${uuid}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除客群计算任务
   * 
   */
  static remove(uuid: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v2/precision-marketing/user-group/calculator/delete/${uuid}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存并审核客群计算任务
   * 
   */
  static saveAndAudit(body: UserGroupCalculatorTaskV2SaveOrModifyRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v2/precision-marketing/user-group/calculator/saveAndAudit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改或保存客群计算任务
   * 
   */
  static saveOrUpdate(body: UserGroupCalculatorTaskV2SaveOrModifyRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v2/precision-marketing/user-group/calculator/saveOrUpdate`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 停止客群计算任务
   * 
   */
  static stop(uuid: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v2/precision-marketing/user-group/calculator/stop/${uuid}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

}
