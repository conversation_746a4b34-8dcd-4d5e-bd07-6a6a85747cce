/*
 * @Author: 黎钰龙
 * @Date: 2025-01-22 15:01:34
 * @LastEditTime: 2025-04-30 14:19:18
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\analysis\BCustomerProfileFilter.ts
 * 记得注释
 */
import PageRequest from 'model/default/PageRequest'

// 客户画像查询过滤
export default class BCustomerProfileFilter extends PageRequest {
  // uuid IN 
  uuidIn: Nullable<string[]> = null
  // name等于
  nameEquals: Nullable<string> = null
  // name类似于
  nameLikes: Nullable<string> = null
  // 目标客群等于
  targetCustomerEquals: Nullable<string> = null
  // 对比客群等于
  compareCustomerEquals: Nullable<string> = null
}