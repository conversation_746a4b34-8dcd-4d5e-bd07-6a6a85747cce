/*
 * @Author: 黎钰龙
 * @Date: 2024-09-25 16:54:59
 * @LastEditTime: 2024-09-27 16:39:08
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\mbrdeposit\recharge\import-storage-recharge-dialog\ImportStorageRechargeDialog.ts
 * 记得注释
 */
import AbstractImportDialog from 'cmp/abstract-import-dialog/AbstractImportDialog';
import FormItem from 'cmp/formitem/FormItem';
import I18nPage from 'common/I18nDecorator';
import EnvUtil from 'util/EnvUtil';
import { Component } from 'vue-property-decorator';
import UploadApi from "http/upload/UploadApi";
@Component({
  name: 'ImportStorageRechargeDialog',
  components: {
    FormItem
  }
})
@I18nPage({
  prefix: [
    "/公用/券模板",
    "/会员/会员资料",
    "/会员/会员资料/会员资料导入",
    "/公用/导入",
    "/公用/按钮",
    "/会员/黑名单",
    '/储值/会员储值/储值充值单'
  ],
  auto: true,
})
export default class ImportStorageRechargeDialog extends AbstractImportDialog {
  // 文件模板
  get templateHref() {
    if (location.href.indexOf("localhost") === -1) {
      return "template_storage_recharge_bill.xlsx";
    } else {
      return "template_storage_recharge_bill.xlsx";
    }
  }

  get getUploadUrl() {
    return EnvUtil.getServiceUrl() + `v1/member/balance/recharge/bill/importMember`;
  }

  uploadSuccess() {
    this.$emit("uploadSuccess");
  }

  close() {
    this.dialogShow = false;
  }

  downloadTemplate() {
    UploadApi.getUrl(this.templateHref).then((resp: any) => {
      if (resp && resp.data) {
        window.open(resp.data);
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
};