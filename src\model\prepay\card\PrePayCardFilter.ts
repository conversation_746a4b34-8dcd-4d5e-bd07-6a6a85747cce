/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-05-15 14:31:49
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\prepay\card\PrePayCardFilter.ts
 * 记得注释
 */
export default class PrePayCardFilter {
	// 卡类型等于:储值卡-RechargeableCard,电子礼品卡-OnlineGiftCard,实体礼品卡-OfflineGiftCard,充值卡-ImprestCard,次卡-CountingCard
	cardTypeEquals: Nullable<string> = null;
	// 卡面卡号类似于
	codeLikes: Nullable<string> = null;
  // 卡面卡号起始于
  codeStartsWith: Nullable<string> = null;
	// 卡面卡号等于
	codeEquals: Nullable<string> = null;
	// 购卡人等于
	buyMemberEquals: Nullable<string> = null;
	// 持卡人等于
	ownerMemberEquals: Nullable<string> = null;
	// 购卡人或持卡人等于
	buyOrOwnerMemberEquals: Nullable<string> = null;
	// 卡状态=
	stateEquals: Nullable<string> = null;
	// 账户类型=
	accountTypeIdEquals: Nullable<string> = null;
	// 卡面额范围
	faceAmountGreaterOrEquals: Nullable<number> = null;
	// 卡面额范围
	faceAmountLessOrEquals: Nullable<number> = null;
	// 卡余额范围
	balanceGreaterOrEquals: Nullable<number> = null;
	// 卡余额范围
	balanceLessOrEquals: Nullable<number> = null;
	// 有效期时间范围[]
	expiredAfterOrEquals: Nullable<Date> = null;
	// 有效期时间范围[]
	expiredBefore: Nullable<Date> = null;
	// 卡模板号或名称类似于
	templateNameOrNumberLikes: Nullable<string> = null;
	// 活动id或名称类似于
	activityIdOrNameLikes: Nullable<string> = null;
	// 充值卡预售单号
	issueTransIdIdEquals: Nullable<string> = null;
	// 页号
	page: Nullable<number> = 0;
	// 页面大小
	pageSize: Nullable<number> = 10;
	// 门店
	issueOrgIdEquals: Nullable<string> = null;
}
