import {Component, Prop, Vue, Watch} from 'vue-property-decorator'

@Component({
  name: 'CodeInput',
  components: {}
})
export default class CodeInput extends Vue {
  code = ''
  codeString = ''
  bindArray: any = []
  bindId = ''
  isShowIcon = true
  isShowTip = false
  @Prop()
  recieveArray: any
  @Prop()
  currentId: any
  @Prop()
  disabled: any
  @Prop()
  maxLen: number

  @Watch('recieveArray', {deep: true})
  onRecieveArrayChange(value: any) {
    if (value) {
      this.bindArray = value
      if (value && value.length === 0) {
        this.code = ''
      }
    } else {
      this.code = ''
    }
  }

  @Watch('currentId', {deep: true})
  onCurrentIdChange(value: any) {
    if (value) {
      this.bindId = value
    }
  }

  @Watch('disabled', {deep: true})
  onDisabledChange(value: any) {
    if (value) {
      this.disabled = value
    }
  }

  mounted() {
    if (this.recieveArray) {
      this.bindArray = this.recieveArray
    } else {
      this.bindArray = []
    }
  }

  onAdd() {
    // 小于20个可以增加标签
    if (this.bindArray.length >= 0 && this.bindArray.length < 20) {
      let oArr = this.bindArray.filter((item: any) => {
        return this.code === item
      })
      if (oArr.length > 0) {
        this.$message.warning(this.formatI18n('/分析/自定义标签/自定义标签/添加/提示/标签值重复'))
      } else {
        if (this.checkBarcode(this.code)) {
          this.code && this.bindArray.push(this.code) && (this.code = '')
          this.$emit('selectCode', this.bindArray)
        }
      }
    } else {
      if (this.code) {
        this.$message.warning(this.formatI18n('/分析/自定义标签/自定义标签/添加/提示/最多只能输入20个标签'))
        this.code = ''
      }
    }
  }

  onFocus() {
    // this.$emit('focus')
  }

  onBlur() {
    this.$emit('blur')
  }

  onClose(index: any) {
    if (this.bindArray[index] === this.codeString) {
      this.isShowTip = false
    }
    this.bindArray.splice(index, 1)
    this.$emit('selectCode', this.bindArray)
  }

  checkBarcode(barcode: string) {
    if (this.maxLen && this.maxLen < barcode.length) {
      this.$message.error(this.formatI18n('/分析/自定义标签/自定义标签/添加/提示/最多只能输入12个字符'))
      return false
    }
    return true
  }
}


