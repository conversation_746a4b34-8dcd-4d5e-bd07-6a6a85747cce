/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-03-14 15:05:53
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\grade\paidgrade\GradeFeeSet.ts
 * 记得注释
 */
import GiftInfo from "model/common/GiftInfo";

export default class GradeFeeSet {
	// 行号
	no: Nullable<number> = null;
	// 套餐名称
	name: string = "";
	// 套餐有效期(天)
	validDay: number = 0;
	// 套餐定价
	price: number = 0;
	// 套餐礼包信息
	giftBagDetail: Nullable<GiftInfo> = null;
	// 续费价格
	renewPrice: number = 0;
	// 续费礼包信息
	renewGiftBagDetail: Nullable<GiftInfo> = null;
	// 权益说明
	equityDesc: string = "";
	//  是否能自动续费
	enableAutoRenew: Nullable<Boolean> = null;
}
