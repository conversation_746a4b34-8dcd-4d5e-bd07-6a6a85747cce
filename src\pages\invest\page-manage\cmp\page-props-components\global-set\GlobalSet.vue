<!--
 * @Author: 黎钰龙
 * @Date: 2024-08-01 16:15:02
 * @LastEditTime: 2024-10-17 15:07:59
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\cmp\page-props-components\global-set\GlobalSet.vue
 * 记得注释
-->
<template>
  <el-form :label-position="labelPosition" :model="localValue" :rules="rules" ref="form" label-width="100px" class="global-set">
    <!-- <el-form-item label="页面名称" prop="propTitle">
      <el-input
        v-model="localValue.propTitle"
        @change="handleChange"
        placeholder="请输入标题名称"
        :maxlength="15"
        show-word-limit
      ></el-input>
    </el-form-item>
    <el-form-item label-position="left">
      <p>{{localValue.propShowTop ? '启用':'不启用'}}</p>
      <el-switch
        @change="handleChange"
        v-model="localValue.propShowTop"
        active-color="#13ce66"
      >
      </el-switch>
    </el-form-item> -->
    <el-form-item :label="i18n('背景色')">
      <el-color-picker @change="handleChange" v-model="localValue.styBgColor"></el-color-picker>
    </el-form-item>
    <el-form-item>
      <div slot="label">
        {{ i18n('自定义颜色') }}
        <i class="el-icon-warning" @click="showCustomAlert"></i>
      </div>
      <el-input v-model="localValue.styCustomBgColor" type="textarea" :rows="8" resize="none" :placeholder="customPlaceholderStr">
      </el-input>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" src="./GlobalSet.ts"></script>

<style lang="scss">
.global-set {
  padding: 0 30px 0 0px;
  .el-form-item__label {
    padding: 0 !important;
  }
  .el-icon-warning {
    cursor: pointer;
  }
}
</style>
