import IdName from 'model/common/IdName'
export default class PrePayCardStateHst {
  // 操作人
  operator: Nullable<string> = null
  // 操作类型
  category: Nullable<string> = null
  // 操作时间
  occurredTime: Nullable<Date> = null
  // 卡状态名称
  stateName: Nullable<string> = null
  // 卡状态    UNACTIVATED-未激活,  PRESENTING-转赠中,USING-使用中,CANCELLED-已作废
  state: Nullable<string> = null
  // 是否当前最新记录
  current: Nullable<boolean> = null
  // 原有效期
  oldExpireDate: Nullable<string> = null
  // 卡所属门店
  ownerOrg: Nullable<IdName> = null
}