/*
 * @Author: 黎钰龙
 * @Date: 2025-01-15 10:22:58
 * @LastEditTime: 2025-01-15 11:53:14
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\analysis\RfmAnalysisApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import QueryMemberConsumeDataFilter from 'model/analysis/QueryMemberConsumeDataFilter'
import RfmUserCompose from 'model/analysis/RfmUserCompose'
import RfmUserDetail from 'model/analysis/RfmUserDetail'
import Response from 'model/default/Response'

export default class RfmAnalysisApi {
  /**
   * RFM数据会员构成
   * RFM数据会员构成
   * 
   */
  static listUserCompose(taskId: string): Promise<Response<RfmUserCompose[]>> {
    return ApiClient.server().post(`/v1/rfm-data-analysis/listUserCompose`, {}, {
      params: {
        taskId: taskId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 会员联动明细
   * 会员联动明细
   * 
   */
  static listUserDetail(body: QueryMemberConsumeDataFilter): Promise<Response<RfmUserDetail[]>> {
    return ApiClient.server().post(`/v1/rfm-data-analysis/listUserDetail`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 会员联动明细导出
   *
   */
  static exportUserDetail(body: QueryMemberConsumeDataFilter): Promise<Response<RfmUserDetail[]>> {
    return ApiClient.server().post(`/v1/rfm-data-analysis/exportUserDetail`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
