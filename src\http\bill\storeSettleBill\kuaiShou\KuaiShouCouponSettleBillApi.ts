import ApiClient from 'http/ApiClient'
import Response from 'model/default/Response'
import KuaiShouCouponSettleBillFilter
  from "model/bill/storeSettleBill/kuaiShou/KuaiShouCouponSettleBillFilter";
import KuaiShouCouponSettleBill from "model/bill/storeSettleBill/kuaiShou/KuaiShouSettleBill";

export default class KuaiShouCouponSettleBillApi {
  /**
   * 分页查询快手券账单
   * 查询快手券账单。
   * 
   */
  static query(body: KuaiShouCouponSettleBillFilter): Promise<Response<KuaiShouCouponSettleBill[]>> {
    return ApiClient.server().post(`/v1/platform-ledger/coupon/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导出快手券账单
   * 导出快手券账单。
   *
   */
  static export(body: KuaiShouCouponSettleBillFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/platform-ledger/coupon/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
