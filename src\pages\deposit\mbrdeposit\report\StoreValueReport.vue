<template>
  <div class="store-value-report">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button size="large" @click="doBatchExport" v-if="hasOptionPermission('/数据/报表/会员储值报表','报表导出')">
          {{ formatI18n('/会员/会员资料', '批量导出') }}
        </el-button>
      </template>
    </BreadCrume>
    <div style="height: 95%;overflow: auto">
      <div class="all-total">
        <FormItem :class="[isActive?'multy-account':'multy-account-en']" label="请选择会员储值账户" v-if="switchFlag">
          <el-select @change="doAccountChange" v-model="account">
            <el-option no-i18n :label="item.name" :value="item.id" :key="item.id" v-for="item in accounts">
              [{{ item.id }}]{{ item.name }}
            </el-option>
          </el-select>
        </FormItem>
        <!-- <el-block-panel>
          <el-block-panel-item>
            <div class="height-80">
              <div class="amount-desc">
                <span>储值总余额</span>
                <el-tooltip content="Bottom center" effect="light" placement="bottom">
                  <i class="iconfont  ic-info" style="font-size: 18px;color: #20A0FF"></i>
                  <template slot="content">
                    <div>{{ i18n('储值总余额=实充总余额+返现总余额') }}</div>
                  </template>
                </el-tooltip>
              </div>
              <div v-if="summary.total && hasOptionPermission('/数据/报表/会员储值报表','储值余额查看')" class="amount"><span no-i18n>{{ summary.total | fmt }}</span>&nbsp;<span>元</span></div>
              <div v-else class="amount"><span> -- </span></div>
            </div>
          </el-block-panel-item>
          <el-block-panel-item>
            <div class="height-80">
              <div class="amount-desc"><span>实充总余额</span>
                <el-tooltip content="Bottom center" effect="light" placement="bottom">
                  <i class="iconfont  ic-info" style="font-size: 18px;"></i>
                  <template slot="content">
                    <div>{{ i18n('实充总余额=首次启用储值功能后（充值+绑卡+消费冲帐+调整）实充增加-（消费+调整）实充减少') }}</div>
                  </template>
                </el-tooltip>

              </div>
              <div v-if="summary.amount && hasOptionPermission('/数据/报表/会员储值报表','储值余额查看')" class="amount color-default"><span no-i18n>{{ summary.amount | fmt }}</span>&nbsp;<span>元</span></div>
              <div v-else class="amount"><span> -- </span></div>
            </div>
          </el-block-panel-item>
          <el-block-panel-item>
            <div class="height-80">
              <div class="amount-desc"><span>返现总余额</span>
                <el-tooltip content="Bottom center" effect="light" placement="bottom">
                  <i class="iconfont  ic-info" style="font-size: 18px;color: #20A0FF"></i>
                  <template slot="content">
                    <div>{{ i18n('返现总余额=首次启用储值功能后（充值+绑卡+消费冲账+调整）返现增加-（消费+调整）返现减少') }}</div>
                  </template>
                </el-tooltip>

              </div>
              <div v-if="summary.giftAmount && hasOptionPermission('/数据/报表/会员储值报表','储值余额查看')" class="amount"><span no-i18n>{{ summary.giftAmount | fmt }}</span>&nbsp;<span>元</span></div>
              <div v-else class="amount"><span> -- </span></div>
            </div>
          </el-block-panel-item>
        </el-block-panel> -->
      </div>
      <div class="cur-tab">
        <el-tabs v-model="bindTab" @tab-click="doTabClick">
          <el-tab-pane label="充值流水">
            <RechargeStream no-i18n :account="account" ref='rechargeStreamFlag'></RechargeStream>
          </el-tab-pane>
          <el-tab-pane label="充值退款流水">
            <RechargeReturnStream no-i18n :account="account" ref='rechargeReturnStreamFlag'></RechargeReturnStream>
          </el-tab-pane>
          <el-tab-pane label="消费流水">
            <ConsumeFlow no-i18n :account="account" ref='consumeFlowFlag'></ConsumeFlow>
          </el-tab-pane>
          <el-tab-pane label="消费退款流水">
            <ConsumeReturnFlow no-i18n :account="account" ref='consumeReturnFlowFlag'></ConsumeReturnFlow>
          </el-tab-pane>
          <el-tab-pane label="调整流水">
            <AdjustFlow no-i18n :account="account" ref='adjustFlowFlag'></AdjustFlow>
          </el-tab-pane>
          <el-tab-pane label="充值流水-按支付方式">
            <RechargeFlowByPay no-i18n :account="account" ref='rechargeFlowByPayFlag'></RechargeFlowByPay>
          </el-tab-pane>
          <el-tab-pane label="充值退款流水-按支付方式">
            <RechargeReturnFlowByPay no-i18n :account="account"
                                     ref='rechargeReturnFlowByPayFlag'></RechargeReturnFlowByPay>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <DownloadCenterDialog
        :dialogvisiable="fileDialogVisible"
        :showTip="showTip"
        @dialogClose="doDownloadDialogClose">
    </DownloadCenterDialog>
    <MbrStoreValueReportExportDialog
        :enableMultipleAccount="switchFlag"
        :accountName="chooseAccount"
        :dialogShow="getExportDialogShow"
        @dialogClose="doExportDialogClose"
        @doSubmit="doExportSubmit">
    </MbrStoreValueReportExportDialog>
  </div>
</template>

<script lang="ts" src="./StoreValueReport.ts">
</script>

<style lang="scss">
.store-value-report {
  width: 100%;
  height: 100%;
  overflow: hidden;

  .cur-tab {
    padding: 20px 20px 0 20px;
    margin-top: 20px;
    background-color: #ffffff;
  }

  .all-total {
    .multy-account {
      margin-top: 20px;
      margin-left: 5px;

      .qf-form-label {
        width: 180px !important;
      }
    }

    .multy-account-en {
      margin-top: 20px;
      margin-left: 5px;

      .qf-form-label {
        width: 250px !important;
      }
    }

    .height-80 {
      height: 80px;

      .amount {
        font-size: 24px;
        font-weight: 500;
        display: block;
        margin-top: 10px;
      }

      .amount-desc {
        margin-top: 10px;
      }

      .color-default {

      }
    }
  }
}
</style>