import { Component, Prop, Provide, Vue, Watch } from "vue-property-decorator";
import CouponItem from "model/common/CouponItem";
import AmountToFixUtil from "util/AmountToFixUtil";
import TimeRange from "cmp/coupontenplate/cmp/TimeRange.vue";
import GoodsScopeEx from "cmp/goodsscope/GoodsScopeEx.vue";
import GoodsRange from "model/common/GoodsRange";
import UseCouponStep from "cmp/coupontenplate/cmp/UseCouponStep.vue";
import CouponBear from "cmp/coupontenplate/cmp/CouponBear.vue";
import CouponInfo from "model/common/CouponInfo";
import CashCouponAttribute from "model/common/CashCouponAttribute";
import ValidityInfo from "model/common/ValidityInfo";
import DateTimeRange from "model/common/DateTimeRange";
import SubjectApportion from "model/common/SubjectApportion";
import StoreRange from "model/common/StoreRange";
import DateUtil from "util/DateUtil";
import ChannelRange from "model/common/ChannelRange";
import CouponInitialApi from "http/v2/coupon/init/CouponInitialApi";
import RSCostPartyFilter from "model/common/RSCostPartyFilter";
import CostPartyApi from "http/costparty/CostPartyApi";
import CouponTemplateSelectorDialog from "cmp/selectordialogs/CouponTemplateSelectorDialog";
import GroupMutexTemplate from "cmp/coupontenplate/cmp/GroupMutexTemplate";
import GroupMutexTemplateData from "cmp/coupontenplate/cmp/GroupMutexTemplateData";
import CouponTemplateLogo from "cmp/coupontenplate/cmp/CouponTemplateLogo";
import ActiveStore from "cmp/activestore/ActiveStore";
import BRandomCouponAttribute from "model/common/BRandomCouponAttribute";
import GoodsCountLimit from "../cmp/GoodsCountLimit";
import { ExpiryType } from "model/common/ExpiryType";
import CouponThreshold from "model/common/CouponThreshold";
import SpecialPriceCouponAttribute from "model/common/SpecialPriceCouponAttribute";
import WeimobCouponInfo from "../cmp/WeimobCouponInfo";
import Channel from "model/common/Channel";
import RSChannelManagement from "model/common/RSChannelManagement";
import RexCoupon from "model/coupon/template/RexCoupon";
import RexCouponInfo from "../cmp/RexCouponInfo.vue";
import SelectGoodsDialog from "cmp/coupontenplate/cmp/selectGoodsDialog.vue";
import RSGoods from 'model/common/RSGoods'
import SpecialGoodsDialog from 'cmp/coupontenplate/cmp/specialGoodsDialog.vue'
import I18nPage from "common/I18nDecorator";
import CouponTemplateLabel from "cmp/coupontenplate/cmp/CouponTemplateLabel.vue"
import StackPromotion from "./../cmp/StackPromotion.vue"
import CouponName from "../FormItemCmp/CouponName/CouponName";
import UseCouponDesc from "../FormItemCmp/UseCouponDesc/UseCouponDesc";
import RecordWay from "../FormItemCmp/RecordWay/RecordWay";
import CouponEffectPeriod from "../FormItemCmp/CouponEffectPeriod/CouponEffectPeriod";
import CouponCodeRules from "../FormItemCmp/CouponCodeRules/CouponCodeRules";
import OuterCouponTemCode from "../FormItemCmp/OuterCouponTemCode/OuterCouponTemCode";
import BrowserMgr, { LocalStorage } from "mgr/BrowserMgr";
import SelectCostParty from 'cmp/selectCostParty/SelectCostParty';
import CouponConfig from 'model/v2/coupon/init/CouponConfig'

class CostPartyDetail {
  // 承担方
  party: Nullable<string> = null
  // 承担百分比/金额
  value: Nullable<number> = null
}
@Component({
  name: "CashCoupon",
  components: {
    CouponName,
    UseCouponDesc,
    RecordWay,
    CouponEffectPeriod,
    CouponCodeRules,
    TimeRange,
    ActiveStore,
    GoodsScopeEx,
    UseCouponStep,
    CouponBear,
    CouponTemplateSelectorDialog,
    GroupMutexTemplate,
    CouponTemplateLogo,
    GoodsCountLimit,
    RexCouponInfo,
    WeimobCouponInfo,
    SelectGoodsDialog,
    SpecialGoodsDialog,
    CouponTemplateLabel,
    StackPromotion,
    OuterCouponTemCode,
    SelectCostParty
  },
})
@I18nPage({
  prefix: ["/公用/券模板"],
  auto: true
})
export default class CashCoupon extends Vue {
  dtl: CouponConfig = new CouponConfig()
  queryCostParyRange:string = 'customize'
  goodsMatchRuleMode: string = "barcode"
  timeParam: any = "";
  noLimit: Boolean = true;
  ruleForm: any = {
    amount: "",
    name: "",
    dateType: "RALATIVE",
    dateFrom: "",
    dateTo: "",
    dateFix: "",
    useDate: "",
    storeRange: "{}",
    useCouponGood: new GoodsRange(),
    promotionInfo: {
      excludePromotion: true,
      promotionSuperpositionType: null,
      promotion: {}
    },
    recordWay: "FAV",
    discountWay: "",
    payWay: "",
    couponOrder: "",
    couponGoodsDesc: "",
    couponProduct: "",
    type: "",
    time: "",
    couponUnder: {},
    templateId: "",
    useFrom: "step2",
    from: [],
    sychChannel: null,
    groupMutex: new GroupMutexTemplateData(true),
    logoUrl: "",
    prefix: "",
    minFaceAmount: "",
    maxFaceAmount: "",
    transferable: true, // 是否可转赠
    useThresholdType: "", // AMOUNT或者QTY
    expiryType: "NATURAL_DAY",
    specialPriceCouponAttribute: new SpecialPriceCouponAttribute(),
    weimobCouponAndTotal: {
      total: "",
      weimobCoupon: null,
    },
    rexCoupon: new RexCoupon(),
    rexId: [],
    weimobId: [],
    recordType: 'PROPORTION', //PROPORTION 按比例 AMOUNT 按金额
    templateTag: [],
    outerRelations: {
      outerNumber: null,
      channel: new Channel()
    },
    amountPayments: [],   // 券支付金额方式
    parties: [],   // 承担方
    price: null,
    termsModel: null,
    couponSubscriptType: "COMMON",
    notes:"",
    maxDailyMemberQuotaQty: null,
    writeOffLink: "",
  };
  // 特殊商品
  specialGoods: any[] = []
  $refs: any;
  rules: any = {};
  curState = "";
  telescoping: boolean = true  //为true收起高级设置，为false展开
  @Prop()
  sameStore: boolean; // 与活动门店一致
  @Prop()
  state: string;
  @Prop()
  channels: RSChannelManagement[];
  @Prop()
  value: CouponItem;
  @Prop({
    type: Boolean,
    default: false,
  })
  baseSettingFlag: boolean;
  @Prop({
    type: String,
    default: "add",
  })
  copyFlag: string;

  @Prop({
    type: String,
    default: "400",
  })
  remarkMaxlength: string;

  @Prop({
    type: Boolean,
    default: false,
  })
  baseFieldEditable: false; // 叠加促销、用券记录方式、券承担方、用券顺序 是否可编辑

  @Prop({
    type: Boolean,
    default: false,
  })
  isPmsPayEngine: boolean; //是否pms计算模式

  @Prop({
    type: Boolean,
    default: false,
  })
  enableStore: boolean;

  @Prop({
    default: () => {
      return {
        maxAmount: 99999999,
        maxValidDay: 36500,
        maxUseThreshold: 99999999,
        fixedTime: false,
      };
    },
  })
  options: {
    // 指定最大券面额，可选配置，用于微信扫码领券
    maxAmount: number; // 指定最大券面额
    maxValidDay: number; // 指定最大券有效天数
    maxUseThreshold: number; // 指定最大用券门槛
    fixedTime: boolean; // 固定用券时段为全部时段
  };

  @Prop({
    type: Boolean,
    default: false,
  })
  wxScanForCoupon: boolean;

  // @Prop({
  // 	type: String,
  // 	default: 'special_price'
  // })
  // couponType: string;

  parties: any = [];

  disabledEdit: Boolean = false;

  // 是否是复制\新建\编辑
  @Watch("state")
  onStateChange(value: string) {
    this.curState = value;
  }

  // 最终的同步渠道
  get sychChannel() {
    return this.channels.filter(item => [...this.ruleForm.weimobId, ...this.ruleForm.rexId].includes(this.channelId(item.channel!))).map(item => item.channel!)
  }

  get hasWeimobChannel() {
    return this.ruleForm.weimobId.length > 0
  }

  get hasRexChannel() {
    return this.ruleForm.rexId.length > 0
  }
  // 可选的同步渠道
  get sychChannels() {
    return this.channels.filter((item) => ["weimob", 'rex'].includes(item.channel!.type || ''));
  }
  get weimobChannels() {
    return this.channels.filter((item) => ["weimob"].includes(item.channel!.type || ''));
  }
  get rexChannels() {
    return this.channels.filter((item) => ["rex"].includes(item.channel!.type || ''));
  }

  get showOuterRelationsConfig() {
    const flag = sessionStorage.getItem('showOuterRelationsConfig')
    return flag === 'true'
  }

  get accountItemRange() {
    if (this.queryCostParyRange) {
      return this.queryCostParyRange;
    }
      return 'customize';
  }

  created() {
    this.queryCostParyRange = LocalStorage.getItem("accountItemRange");
    let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
		if (sysConfig && this.goodsMatchRuleMode) {
			this.goodsMatchRuleMode = sysConfig.goodsMatchRuleMode;
		}
    this.disabledEdit = (this.$route.query.from === 'edit' && this.state !== 'NOT_EFFECTED')
    this.ruleForm.type = new CouponThreshold();
    this.ruleForm.type.value = 1;
    this.ruleForm.type.useThresholdType = "AMOUNT";
    this.rules = {
      name: [
        {
          required: true,
          message: this.formatI18n("/公用/券模板", "请输入必填项"),
          trigger: "blur",
        },
      ],
      "type.threshold": [
        {
          required: true,
          message: this.formatI18n("/公用/券模板", "请输入必填项"),
          trigger: "change",
        },
      ],
      "specialPriceCouponAttribute.favSegment": [
        {
          required: true,
          message: this.formatI18n("/公用/券模板", "请输入必填项"),
          trigger: "change",
        },
      ],
      "specialPriceCouponAttribute.specialPrice": [
        {
          required: true,
          message: this.formatI18n("/公用/券模板", "请输入必填项"),
          trigger: "change",
        },
      ],
      amount: [
        {
          required: true,
          message: this.formatI18n("/公用/券模板", "请输入必填项"),
          trigger: "blur",
        },
      ],
      useFrom: [
        {
          validator: (rule: any, value: any, callback: any) => {
            if (value) {
              if (value === "step1") {
                callback();
              } else {
                if (this.ruleForm.from && this.ruleForm.from.length > 0) {
                  callback();
                } else {
                  callback(this.formatI18n("/公用/券模板", "请输入必填项"));
                }
              }
            }
          },
          trigger: "blur",
        },
      ],
      couponProduct: [
        {
          required: true,
          message: this.formatI18n("/公用/券模板", "请输入必填项"),
          trigger: "blur",
        },
      ],
      discountWay: [
        {
          required: true,
          message: this.formatI18n("/公用/券模板", "请输入必填项"),
          trigger: "blur",
        },
      ],
      payWay: [
        {
          required: true,
          message: this.formatI18n("/公用/券模板", "请输入必填项"),
          trigger: "blur",
        },
      ],
      prefix: [
        {
          validator: (rule: any, value: any, callback: any) => {
            let re = /^[0-9a-zA-Z]*$/g; // 判断字符串是否为数字和字母组合
            if (!re.test(value)) {
              callback(this.i18n("请输入数字或字母"));
            } else {
              callback();
            }
          },
          tirgger: "blur",
        },
      ],
      minFaceAmount: [
        {
          validator: (rule: any, value: any, callback: any) => {
            if (!value && this.ruleForm.maxFaceAmount) {
              callback(this.formatI18n("/公用/券模板", "请填写最小值"));
            } else {
              callback();
            }
          },
          tirgger: "blur",
        },
      ],
      maxFaceAmount: [
        {
          validator: (rule: any, value: any, callback: any) => {
            if (!value && this.ruleForm.minFaceAmount) {
              callback(this.formatI18n("/公用/券模板", "请填写最大值"));
            } else {
              callback();
            }
          },
          tirgger: "blur",
        },
      ],
    };
    if (this.copyFlag) {
      this.getCostParty();
    }
    if (!this.$route.query.id) {
      // 新建时使用配置的用券记录方式
      this.getPayWayDtl();
    }

    if (
      this.value &&
      this.value.coupons &&
      this.value.coupons.couponBasicType === "special_price" &&
      this.value.coupons.remark
    ) {

      this.doBindValue(JSON.parse(JSON.stringify(this.value)));
    }
    this.getCouponPrefix("special_price");
  }

  
	private getCouponPrefix(type: string){
    if (["edit", "copy"].includes(this.copyFlag)) {
			return;
		}
		CouponInitialApi.get().then((resp: any) => {
		  if (resp && resp.code === 2000) {
			this.dtl = resp.data;
			if(!this.dtl || !this.dtl.couponCodePrefixes) {
				return "";
			  }
        const coupon = this.dtl.couponCodePrefixes.find(
          item => item.couponType === type
        );
        this.ruleForm.prefix = coupon ? coupon.prefix : "";
		  }
		})
	}


  doFormItemChange() {
    this.$emit("input", this.doTransParams());
    this.$emit("change", this.channels);
  }

  doUseFromChange() {
    if (this.ruleForm.useFrom === "step1") {
      this.ruleForm.from = [];
      this.doFormItemChange()
    }
    this.$refs.ruleForm.validateField("useFrom");
  }

  formatNumber(type: number) {
    if (type === 0) {
      this.ruleForm.type.threshold = AmountToFixUtil.formatAmount(
        this.ruleForm.type.threshold,
        99999999.99,
        0,
        2
      );
    } else if (type === 1) {
      this.ruleForm.specialPriceCouponAttribute.favSegment = AmountToFixUtil.formatNumber(
        this.ruleForm.specialPriceCouponAttribute.favSegment,
        99999999,
        1
      );
    } else if (type === 2) {
      this.ruleForm.specialPriceCouponAttribute.specialPrice = AmountToFixUtil.formatAmount(
        this.ruleForm.specialPriceCouponAttribute.specialPrice,
        99999999.99,
        0,
        2
      );
    }
    this.doFormItemChange()
  }

  doFromChange() {
    this.$refs.ruleForm.validateField("useFrom");
    this.doFormItemChange()
  }

  logoUrlCallBack(url: any) {
    this.ruleForm.logoUrl = url;
    this.doFormItemChange()
  }

  weimobChange(info: any) {
    this.ruleForm.weimobCouponAndTotal.total = info.total;
    this.ruleForm.weimobCouponAndTotal.weimobCoupon = info.weimobCoupon;
    this.doFormItemChange()
  }

  rexChange(info: any) {
    this.ruleForm.rexCoupon = info
    this.doFormItemChange()
  }

  doGoodsRange() {
    this.doFormItemChange()
    if (
      this.$refs["ruleForm"] &&
      (this.$refs["ruleForm"] as any).validateField
    ) {
      (this.$refs["ruleForm"] as any).validateField("goodsScope");
    }
  }

  doStoreChange() {
    this.doFormItemChange()
    if (
      this.$refs["ruleForm"] &&
      (this.$refs["ruleForm"] as any).validateField
    ) {
      (this.$refs["ruleForm"] as any).validateField("storeRange");
    }
    // this.$forceUpdate()
  }

  get remarkPlaceholder() {
    let str = this.formatI18n(
      "/营销/积分活动/积分活动/积分兑换券/编辑页面",
      "请输入不超过{0}个字符"
    );
    return str.replace(/\{0\}/g, this.remarkMaxlength);
  }

  doValidate() {
    this.telescoping = false
    let arr: any = [];
    let p0 = new Promise((resolve, reject) => {
      this.$refs.ruleForm.validate((valid: any) => {
        if (valid) {
          resolve(null);
        }
      });
    });
    arr.push(p0);
    // 券承担方
    if (this.$refs.CouponBear) {
      arr.push(this.$refs.CouponBear.formValiPromise())
    }
    arr.push(this.$refs.activeStore.validate());
    // 用券门槛
    if (this.$refs.useCouponStep) {
      let p1 = this.$refs.useCouponStep.doValidate();
      arr.push(p1);
    }
    // 用券记录方式校验
    if (this.$refs.recordWay) {
      arr.push(this.$refs.recordWay.validate());
    }
    // 用券时段
    if (this.$refs.timeRange) {
      let p2 = this.$refs.timeRange.doValidate();
      arr.push(p2);
    }
    // 用券商品
    if (this.$refs.goodsScope) {
      let p3 = this.$refs.goodsScope.validate();
      arr.push(p3);
    }
    // 叠加促销
    if (this.$refs.stackPromotion) {
      arr.push(this.$refs.stackPromotion.validate());
    }
    // 券叠加组
    if (this.$refs.groupMutexTemplate) {
      arr.push(this.$refs.groupMutexTemplate.doValidate());
    }
    //券有效期校验
    if (this.$refs.couponEffectPeriod) {
      arr.push(this.$refs.couponEffectPeriod.validate());
    }
    // 微盟校验
    if (this.$refs.weimobCouponInfo) {
      arr.push(this.$refs.weimobCouponInfo.doValidate());
    }
    // rex校验
    if (this.$refs.rexCouponInfo) {
      arr.push(this.$refs.rexCouponInfo.doValidate());
    }
    //外部券模板号
    if (this.$refs.outerCouponTemCode) {
      arr.push(this.$refs.outerCouponTemCode.doValidate())
    }
    return arr;
  }

  private doTransParams() {
    let params: CouponItem = new CouponItem();
    params.coupons = new CouponInfo();
    params.coupons.name = this.ruleForm.name;
    params.coupons.couponBasicType = "special_price" as any;
    params.coupons.templateId = this.ruleForm.templateId;
    params.coupons.cashCouponAttribute = new CashCouponAttribute();
    params.coupons.cashCouponAttribute.faceAmount = this.ruleForm.amount;
    params.coupons.randomCouponAttribute = new BRandomCouponAttribute();
    params.coupons.randomCouponAttribute.maxFaceAmount = this.ruleForm.maxFaceAmount;
    params.coupons.randomCouponAttribute.minFaceAmount = this.ruleForm.minFaceAmount;
    // 特价设置
    params.coupons.specialPriceCouponAttribute = this.ruleForm.specialPriceCouponAttribute;
    // 券有效期
    params.coupons.validityInfo = new ValidityInfo();
    params.coupons.validityInfo.validityType = this.ruleForm.dateType;
    if (this.ruleForm.dateType === "RALATIVE") {
      params.coupons.validityInfo.delayEffectDays = this.ruleForm.dateFrom;
      params.coupons.validityInfo.expiryType = this.ruleForm.expiryType;
      if ([ExpiryType.DAYS, ExpiryType.NATURAL_DAY].indexOf(this.ruleForm.expiryType) > -1) {
        params.coupons.validityInfo.validityDays = this.ruleForm.dateTo;
        params.coupons.validityInfo.months = null;
      } else if (this.ruleForm.expiryType === ExpiryType.MONTHS) {
        params.coupons.validityInfo.months = this.ruleForm.dateTo;
        params.coupons.validityInfo.validityDays = null;
      } else {
        params.coupons.validityInfo.months = null;
        params.coupons.validityInfo.validityDays = null;
      }
    } else {
      // 固定有效期
      if (this.ruleForm.dateFix && this.ruleForm.dateFix[0]) {
        params.coupons.validityInfo.beginDate = (this.ruleForm.dateFix[0]) as any;
      }
      if (this.ruleForm.dateFix && this.ruleForm.dateFix[1]) {
        params.coupons.validityInfo.endDate = (this.ruleForm.dateFix[1]) as any;
      }
    }
    // 用券时段
    params.coupons.useTimeRange = new DateTimeRange();
    if (this.ruleForm.time) {
      // params.coupons.useTimeRange = this.timeTemplate
      params.coupons.useTimeRange = this.ruleForm.time;
    } else {
      params.coupons.useTimeRange = new DateTimeRange();
      params.coupons.useTimeRange.dateTimeRangeType = "ALL" as any;
    }

    params.coupons.useChannels = new ChannelRange();
    if (this.ruleForm.useFrom === "step1") {
      params.coupons.useChannels.channelRangeType = "ALL" as any;
      params.coupons.useChannels.channels = [];
    } else {
      params.coupons.useChannels.channelRangeType = "PART" as any;
      params.coupons.useChannels.channels = this.ruleForm.from;
    }
    //同步渠道
    params.coupons.sychChannel = this.ruleForm.sychChannel;
    // 用券门店

    if (this.ruleForm.storeRange === "{}") {
      let storeRange: StoreRange = new StoreRange();
      if (this.sameStore) {
        storeRange.storeRangeType = "SAME" as any;
      } else {
        storeRange.storeRangeType = "ALL" as any;
      }
      params.coupons.useStores = storeRange;
    } else {
      params.coupons.useStores = this.ruleForm.storeRange;
    }
    // 用券商品
    params.coupons.useGoods = this.ruleForm.useCouponGood;
    // 用券门槛
    params.coupons.useThreshold = this.ruleForm.type;
    params.coupons.useThresholdType = this.ruleForm.type.useThresholdType;
    // 叠加促销
    params.coupons.excludePromotion = this.ruleForm.promotionInfo.excludePromotion;
    params.coupons.promotionSuperpositionType = this.ruleForm.promotionInfo.promotionSuperpositionType;
    params.coupons.promotion = this.ruleForm.promotionInfo.promotion;
    params.coupons.goodsFavRules = this.ruleForm.promotionInfo.goodsFavRules
    // 是否支持转赠
    params.coupons.transferable = this.ruleForm.transferable;
    // 用券记录方式
    params.coupons.useApporion = new SubjectApportion();
    params.coupons.useApporion.subjectApprotionType = this.ruleForm.recordWay;
    params.coupons.useApporion.recordType = this.ruleForm.recordType
    if (this.ruleForm.recordWay === "COLLOCATION") {
      if (this.ruleForm.recordType === 'PROPORTION') {
        params.coupons.useApporion.favValue = parseFloat(this.ruleForm.discountWay);
        params.coupons.useApporion.payValue = parseFloat(this.ruleForm.payWay);
      } else {
        params.coupons.useApporion.payValue = this.ruleForm.payWay;
        params.coupons.useApporion.parties = this.ruleForm.parties
        params.coupons.useApporion.amountPayments = this.ruleForm.amountPayments
        // 用券方式，按金额，特殊商品
        if (this.specialGoods.length) {
          params.coupons.useApporion.specialGoodsAmounts = this.specialGoods.map((item: any) => {
            return {
              barcode: item.barcode,
              amount: item.price != undefined ? item.price : item.amount
            }
          })
        }
      }

    } else {
      params.coupons.useApporion.favValue = 0;
      params.coupons.useApporion.payValue = 0;
      // params.coupons.useApporion.recordType = "PROPORTION";
      params.coupons.useApporion.specialGoodsAmounts = null;
      params.coupons.useApporion.recordType = null
    }
    // 券承担方
    // params.coupons.costParties = [];
    // params.coupons.costParties = this.ruleForm.couponUnder;
    ; (params.coupons.costParty as any) = {};
    if (this.ruleForm.couponUnder.bearType == 'unset') {
      this.ruleForm.couponUnder.bearType = null
      this.ruleForm.couponUnder.amountType = null
      this.ruleForm.couponUnder.costPartyDetails = null
    }
    params.coupons.costParty = this.ruleForm.couponUnder;
    // 用券顺序
    params.coupons.priority = this.ruleForm.couponOrder;
    // 用券商品说明
    params.coupons.goodsRemark = this.ruleForm.couponGoodsDesc;
    // 用券说明
    params.coupons.remark = this.ruleForm.couponProduct;
    // 券叠加促销
    // params.coupons.groupMutexFlag = this.ruleForm.groupMutex.groupMutexFlag;
    // params.coupons.groupMutexTemplates = this.ruleForm.groupMutex.groupMutexTemplates;

    params.coupons.couponSuperposition = this.ruleForm.groupMutex.couponSuperposition;
    // 券logo
    params.coupons.logoUrl = this.ruleForm.logoUrl;
    // 券码前缀
    params.coupons.codePrefix = this.ruleForm.prefix;
    // 剩余库存
    if (this.ruleForm.weimobId?.length > 0) {
      params.coupons.total = this.ruleForm.weimobCouponAndTotal.total;
    }
    params.coupons.templateTag = this.ruleForm.templateTag
    // 同步渠道参数
    this.hasWeimobChannel && (params.coupons.weimobCoupon = this.ruleForm.weimobCouponAndTotal.weimobCoupon)
    this.hasRexChannel && (params.coupons.rexCoupon = this.ruleForm.rexCoupon)
    params.coupons.sychChannel = this.sychChannel
    //外部券模板号
    params.coupons.outerRelations = this.ruleForm.outerRelations && this.ruleForm.outerRelations.outerNumber ? [this.ruleForm.outerRelations] : null
    //价格
    params.coupons.salePrice = this.ruleForm.price
    //账款项目
    params.coupons.termsModel = this.ruleForm.termsModel
    //券角标
    params.coupons.couponSubscriptType = this.ruleForm.couponSubscriptType
    //核销链接
    params.coupons.writeOffLink = this.ruleForm.writeOffLink
    //备注
    params.coupons.notes = this.ruleForm.notes
    //每人每天限量
    params.coupons.maxDailyMemberQuotaQty = this.ruleForm.maxDailyMemberQuotaQty
    
    return params;
  }

  private doBindValue(value: CouponItem) {
    if (value && value.coupons) {
      let coupon: CouponInfo = value.coupons;

      this.ruleForm.templateId = coupon.templateId;
      this.ruleForm.name = coupon.name;
      this.ruleForm.amount = coupon.cashCouponAttribute!.faceAmount;
      if (coupon.randomCouponAttribute) {
        this.ruleForm.minFaceAmount = coupon.randomCouponAttribute!.minFaceAmount;
        this.ruleForm.maxFaceAmount = coupon.randomCouponAttribute!.maxFaceAmount;
      }

      const weimobChannel = coupon.sychChannel.filter(item => item.type == 'weimob')[0]
      this.ruleForm.weimobId = weimobChannel ? [this.channelId(weimobChannel)] : []
      const rexChannel = coupon.sychChannel.filter(item => item.type == 'rex')[0]
      this.ruleForm.rexId = rexChannel ? [this.channelId(rexChannel)] : []

      // 特价设置
      this.ruleForm.specialPriceCouponAttribute =
        coupon.specialPriceCouponAttribute;

      this.ruleForm.sychChannel = coupon.sychChannel;

      this.ruleForm.dateType = coupon.validityInfo!.validityType;
      if (this.ruleForm.dateType === "RALATIVE") {
        this.ruleForm.dateFrom = coupon.validityInfo!.delayEffectDays;
        this.ruleForm.expiryType = coupon.validityInfo!.expiryType;
        this.ruleForm.dateTo =
          coupon.validityInfo!.validityDays || coupon.validityInfo!.months;
      } else {
        this.ruleForm.dateFix = [
          DateUtil.format(coupon.validityInfo!.beginDate, "yyyy-MM-dd HH:mm:ss"),
          DateUtil.format(coupon.validityInfo!.endDate, "yyyy-MM-dd HH:mm:ss"),
        ];
      }
      // 用券门店
      this.ruleForm.storeRange = coupon.useStores;
      // 用券商品
      this.ruleForm.useCouponGood = coupon.useGoods;
      // 用券时段
      this.ruleForm.time = coupon.useTimeRange;
      // todo 用券渠道
      if (coupon.useChannels && coupon.templateId) {
        if (coupon.useChannels.channelRangeType === "ALL") {
          this.ruleForm.useFrom = "step1";
          this.ruleForm.from = [];
        } else {
          this.ruleForm.useFrom = "step2";
          if (
            coupon.useChannels.channels &&
            coupon.useChannels.channels.length > 0
          ) {
            let arrs: string[] = [];
            coupon.useChannels.channels.forEach((item: any) => {
              if (item.id || item.type) {
                if (item.id && item.id !== "-") {
                  arrs.push(item.type + item.id);
                } else {
                  arrs.push(item.type);
                }
              } else {
                arrs.push(item);
              }
            });
            this.ruleForm.from = arrs;
          }
        }
      }
      // 用券门槛类型

      this.ruleForm.type = coupon.useThreshold;
      if (this.ruleForm.type) {
        this.ruleForm.type.useThresholdType = coupon.useThresholdType;
      }

      // 叠加促销
      if (coupon.excludePromotion || coupon.excludePromotion === false) {
        this.ruleForm.promotionInfo.excludePromotion = coupon.excludePromotion;
      } else {
        this.ruleForm.promotionInfo.excludePromotion = true;
      }
      this.ruleForm.promotionInfo.promotionSuperpositionType = coupon.promotionSuperpositionType
      this.ruleForm.promotionInfo.promotion = coupon.promotion
      this.ruleForm.promotionInfo.goodsFavRules = coupon.goodsFavRules
      // 用券记录方式
      if (coupon.useApporion && coupon.useApporion!.subjectApprotionType) {
        this.ruleForm.recordWay = coupon.useApporion!.subjectApprotionType;
      } else {
        this.ruleForm.recordWay = "FAV";
      }

      if (this.ruleForm.recordWay === "COLLOCATION") {
        if (coupon.useApporion!.recordType === 'AMOUNT') {
          this.ruleForm.payWay = coupon.useApporion!.payValue;
          this.specialGoods = coupon.useApporion!.specialGoodsAmounts || []
          this.ruleForm.parties = coupon.useApporion!.parties || []
          this.ruleForm.amountPayments = coupon.useApporion!.amountPayments || []
        } else {
          this.ruleForm.discountWay = coupon.useApporion!.favValue;
          this.ruleForm.payWay = coupon.useApporion!.payValue;
        }

      }
      this.ruleForm.recordType = coupon.useApporion!.recordType
      // 券承担方
      // this.ruleForm.couponUnder = coupon.costParties;
      this.ruleForm.couponUnder = coupon.costParty;
      // 用券顺序
      this.ruleForm.couponOrder = coupon.priority;
      // 用券商品说明
      this.ruleForm.couponGoodsDesc = coupon.goodsRemark;
      // 用券说明
      this.ruleForm.couponProduct = coupon.remark;
      // 是否可以叠加用券
      setTimeout(() => {
        this.$refs.groupMutexTemplate.initValue2(coupon, this.copyFlag);
      }, 0);
      // 券logo
      this.ruleForm.logoUrl = coupon.logoUrl;
      // 券码前缀
      this.ruleForm.prefix = coupon.codePrefix;
      // 是否支持转赠
      this.ruleForm.transferable = coupon.transferable;
      // 剩余库存
      this.ruleForm.weimobCouponAndTotal.total = coupon.total;
      // 微盟券参数
      this.ruleForm.weimobCouponAndTotal.weimobCoupon = coupon.weimobCoupon;
      this.ruleForm.templateTag = coupon.templateTag
      coupon.rexCoupon && (this.ruleForm.rexCoupon = coupon.rexCoupon)
      //外部券模板号
      if (coupon.outerRelations && coupon.outerRelations[0] && (this.showOuterRelationsConfig || this.$route.query.from === 'edit') && this.$route.query.from !== 'copy') {
        // 配置不展示外部券模板号时，编辑券模板保留该字段，复制不保留该字段
        this.ruleForm.outerRelations = coupon.outerRelations[0]
      }
      //价格
      this.ruleForm.price = coupon.salePrice
      //账款项目
      this.ruleForm.termsModel = coupon.termsModel
      //券角标
      this.ruleForm.couponSubscriptType = coupon.couponSubscriptType
      //核销链接
      this.ruleForm.writeOffLink = coupon.writeOffLink
      //备注
      this.ruleForm.notes =coupon.notes
      //每人每天限量
      this.ruleForm.maxDailyMemberQuotaQty = coupon.maxDailyMemberQuotaQty

    }
  }

  private channelId(item: Channel) {
    return `${item.type}${item.id}`
  }

  private getPayWayDtl() {
    CouponInitialApi.get().then((resp: any) => {
      if (resp && resp.code === 2000) {
        if (resp.data && resp.data.subjectApportion === "pay") {
          this.ruleForm.recordWay = "PAY";
        } else if (resp.data && resp.data.subjectApportion === "fav") {
          this.ruleForm.recordWay = "FAV";
        } else if (resp.data && resp.data.subjectApportion === "collection") {
          this.ruleForm.recordWay = "COLLOCATION";
          this.ruleForm.recordType = "AMOUNT"
          this.ruleForm.payWay = 0
        }
      }
    });
  }

  private getCostParty() {
    let params: RSCostPartyFilter = new RSCostPartyFilter();
    params.page = 0;
    params.pageSize = 0;
    CostPartyApi.query(params)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.parties = resp.data;
        }
      })
      .catch((error: any) => {
        this.$message.error(error.message);
      });
  }

  // 接受特殊商品参数
  specialGoodsSubmit(goods: RSGoods[]) {
    this.specialGoods = [...goods]
    this.doFormItemChange()
  }

  changeSpecialGoods(goods: any[]) {
    this.specialGoods = goods || []
    this.doFormItemChange()
  }

  telescopingChange() {
    this.telescoping = !this.telescoping
  }

  doSynChannelChange(newValue: any[]) {
    if (this.ruleForm.weimobId && this.ruleForm.weimobId.length >= 2) {
      this.ruleForm.weimobId = this.ruleForm.weimobId.slice(-1)
    } else if (this.ruleForm.rexId && this.ruleForm.rexId.length >= 2) {
      this.ruleForm.rexId = this.ruleForm.rexId.slice(-1)
    }
    this.doFormItemChange()
  }

  doOuterTemplateChange(data: any) {
    this.ruleForm.outerRelations = data
    this.doFormItemChange()
  }
}
