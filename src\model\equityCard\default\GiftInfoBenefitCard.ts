import { enefitCardTemplateType } from './enefitCardTemplateType'
import GiftInfoBenefitCardFreeRule from './GiftInfoBenefitCardFreeRule'
import GiftInfoBenefitCardPaidRule from './GiftInfoBenefitCardPaidRule'
import IdName from './IdName'

export default class GiftInfoBenefitCard {
  // 卡模版代码与名称
  benefitCardTemplate: Nullable<IdName> = null
  // 模版类型
  type: Nullable<enefitCardTemplateType> = null
  // 活动赠礼付费卡定义
  payRule: Nullable<GiftInfoBenefitCardPaidRule> = null
  // 活动赠礼权益卡定义
  freeRule: Nullable<GiftInfoBenefitCardFreeRule> = null
}