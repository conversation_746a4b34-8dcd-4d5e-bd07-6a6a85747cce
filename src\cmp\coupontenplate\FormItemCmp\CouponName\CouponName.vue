<!--
 * @Author: 黎钰龙
 * @Date: 2023-08-25 11:54:09
 * @LastEditTime: 2024-04-24 14:58:08
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\coupontenplate\FormItemCmp\CouponName\CouponName.vue
 * 记得注释
-->
<template>
  <div>
    <el-form-item :label="formatI18n('/公用/券模板', '券名称')" prop="name" :rules='rules'>
      <div>
        <div style="color: #79879E">- {{ i18n('同步至微盟的券最多9个字') }}。</div>
        <!-- 由于v-model不适合直接绑子组件，所以暴露给父组件用slot绑 -->
        <slot name="slot"></slot>
      </div>
    </el-form-item>
  </div>
</template>

<script lang="ts" src="./CouponName.ts">
</script>

<style>
</style>