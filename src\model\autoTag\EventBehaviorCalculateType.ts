import ConstantMgr from "mgr/ConstantMgr"

/*
 * @Author: 黎钰龙
 * @Date: 2024-11-21 19:31:52
 * @LastEditTime: 2025-02-14 15:49:48
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\autoTag\EventBehaviorCalculateType.ts
 * 记得注释
 */
export enum EventBehaviorCalculateType {
  // 次数最多
  most_times = 'most_times',
  // 数值最大
  max_value = 'max_value',
  // 次数大于
  count_gt = 'count_gt',
  // 数值大于
  value_gt = 'value_gt',
  // 总和/求和
  sum = 'sum',
  // 均值
  avg = 'avg',
  // 最大值
  max = 'max',
  // 最小值
  min = 'min',
  // 去重数
  deduplication = 'deduplication',
  // 总次数
  count = 'count'
}

export class EventBehaviorCalculateTypeUtil {
  // 偏好类标签 数值/时间行为事件类型
  static getPreferNumberLabelList() {
    return [
      {
        label: new ConstantMgr.MenusFuc().format("/会员/智能打标", "数值最大"),
        value: EventBehaviorCalculateType.max_value
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/智能打标", "数值大于"),
        value: EventBehaviorCalculateType.value_gt
      },
    ]
  }

  // 偏好类标签 字符串行为事件类型
  static getPreferStringLabelList() {
    return [
      {
        label: new ConstantMgr.MenusFuc().format("/会员/智能打标", "次数最多"),
        value: EventBehaviorCalculateType.most_times
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/智能打标", "次数大于"),
        value: EventBehaviorCalculateType.count_gt
      }
    ]
  }

  // 分层标签 数值型事件类型
  static getLayerNumberLabelList() {
    return [
      {
        label: new ConstantMgr.MenusFuc().format("/会员/智能打标", "总和"),
        value: EventBehaviorCalculateType.sum
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/智能打标", "均值"),
        value: EventBehaviorCalculateType.avg
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/智能打标", "最大值"),
        value: EventBehaviorCalculateType.max
      },
      {
        label: new ConstantMgr.MenusFuc().format("/会员/智能打标", "最小值"),
        value: EventBehaviorCalculateType.min
      }
    ]
  }

  // 分层标签 非数值型事件类型
  static getLayerLabelList() {
    return [
      {
        label: new ConstantMgr.MenusFuc().format("/会员/智能打标", "去重数"),
        value: EventBehaviorCalculateType.deduplication
      }
    ]
  }
}