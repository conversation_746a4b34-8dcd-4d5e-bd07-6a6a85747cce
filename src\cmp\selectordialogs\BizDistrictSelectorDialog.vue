<template>
  <el-dialog :title="i18n('选择商圈')" class="select-store-dialog"
             append-to-body
             :close-on-click-modal="false" :visible.sync="dialogShow">
    <div class="wrap">
      <el-form label-width="130px">
        <el-row class="query">
          <el-col :span="16">
            <el-form-item :label="i18n('商圈')">
              <el-input v-model="circleFilter.idNameLikes"
                        :placeholder="i18n('请输入商圈代码/名称')"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-button type="primary" @click="doSearch()">{{i18n('/公用/按钮/查询')}}</el-button>
              <el-button @click="doReset()">{{i18n('/公用/按钮/重置')}}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-row>
        <el-col :span="18">
          <el-row class="table-wrap" v-loading="loading.query">
            <el-row class="thead">
              <el-col :span="1">
                <el-checkbox @change="doCheckAll($event)" v-model="checkAll"/>
              </el-col>
              <el-col :span="11">{{i18n('商圈代码')}}</el-col>
              <el-col :span="12">{{i18n('商圈名称')}}</el-col>
            </el-row>
            <el-row class="tbody" v-if="!loading.query">
              <el-row v-if="currentList && currentList.length > 0" v-for="(item, index) of currentList" :key="index" class="trow">
                <el-col :span="1">
                  <el-checkbox v-model="checkboxList[index]" @change="doCheck($event, index)"/>
                </el-col>
                <el-col @click.native="doCheckRow(index)" :span="11" :title="item.circle.id">{{ item.circle.id }}</el-col>
                <el-col @click.native="doCheckRow(index)" :span="12" :title="item.circle.name">{{ item.circle.name }}</el-col>
              </el-row>
              <el-row v-if="!currentList || currentList.length === 0" class="trow"
                      style="text-align: center;color: #909399">
                {{ i18n('/公用/提示/暂无数据') }}
              </el-row>
            </el-row>
          </el-row>
        </el-col>
        <el-col :span="6" style="padding-left: 5px">
          <el-row class="right-table">
            <el-row class="thead">
              {{i18n('已选商圈：')}}{{selected?(selected.filter(e=>e.circle.id)).length: 0}}
            </el-row>
            <el-row style="padding: 5px;">
              <el-input style="width: 100%;" @keyup.enter.native="filterSelected()" @change="filterSelected()"
                        v-model="selectedFilter"
                        clearable
                        @clear="filterSelected()"
                        :placeholder="i18n('请输入商圈代码/名称')"></el-input>
            </el-row>
            <el-row class="tbody">
              <el-row class="trow" style="position: relative;display: flex;align-items: center"
                      v-if="filteredSelected && filteredSelected.length > 0"
                      v-for="(item, index) of filteredSelected.filter(e=>e.circle.id)" :key="index" :title="item.circle|idName">
                <div class="left">{{ item.circle|idName }}</div>
                <div class="clear-btn" style="display: none"><a
                    @click="delItem(item, index)">{{ i18n('清除') }}</a>
                </div>
              </el-row>
              <el-row v-if="!filteredSelected || filteredSelected.length === 0" class="trow"
                      style="text-align: center;color: #909399">
                {{ i18n('/公用/提示/暂无数据') }}
              </el-row>
            </el-row>
          </el-row>
        </el-col>
      </el-row>
    </div>
    <div class="page" style="margin-top: 15px">
      <el-pagination
          :current-page="page.currentPage"
          :page-size="page.size"
          :page-sizes="[10, 20, 30, 40]"
          :total="page.total"
          @current-change="handleCurrentChange($event)"
          @size-change="handleSizeChange($event)"
          background
          layout="total, prev, pager, next, sizes,  jumper">
      </el-pagination>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="dialogShow = false">{{i18n('/公用/按钮/取消')}}</el-button>
      <el-button size="small" type="primary" @click="doModalClose()">{{i18n('/公用/按钮/确定')}}</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./BizDistrictSelectorDialog.ts"/>

<style lang="scss" scoped>
  .select-store-dialog {
    display: flex;
    align-items: center;
    justify-content: center;
    @import "SelectorDialogCommon";
  }
</style>