
import MenuSetting from 'model/navigation/MenuSetting'
import NavigationSetting from 'model/navigation/NavigationSetting'
import { IconColorStyle } from 'model/navigation/IconColorStyle'
import { NavigationStyle } from 'model/navigation/NavigationStyle'

// 根据接口所需字段拓展前端展示所需字段
class customMenuSetting extends MenuSetting {
    showRepealce?: boolean // 已选择情况
    isUseIcon?: boolean = true // 是否使用的 icon
    // 默认左侧回显不选中
    isActive: boolean = false
  }
  
  class AddMenuData extends NavigationSetting {
    // 菜单设置
    menuSettings: customMenuSetting[] = []
  }

//   初始化数据
export default class NavigationSettingInit {
  // 导航样式
  navigationType: Nullable<NavigationStyle> = NavigationStyle.normal
  // 图标配色
  iconColorType: Nullable<IconColorStyle> = IconColorStyle.custom
  // 选中的颜色
  selectedColor: Nullable<string> = '#FC5312'
  // 未选中的颜色
  unselectedColor: Nullable<string> = '#A9A9A9'
  // 选中的文本颜色
  selectedFontColor: Nullable<string> = '#FC5312'
  // 选中的图标颜色
  selectedIconColor: Nullable<string> = '#FC5312'
  // 未选中的文本颜色
  unselectedFontColor: Nullable<string> = '#9F9F9F'
  // 未选中的图标颜色
  unselectedIconColor: Nullable<string> = '#A9A9A9'
  // 菜单数量
  menuQuantity: Nullable<number> = 3
  // 菜单设置
  menuSettings: customMenuSetting[] = []
}