import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'

export default class WeixinAuthV2Api {
  /**
   * 微信授权页面加载控制
   * 微信授权页面加载控制。
   *
   */
  static authorize(): Promise<Response<void>> {
    return ApiClient.server().post(`/v2/weixin-auth/authorize`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 检测是否授权信息
   * 检测是否授权信息。
   *
   */
  static checkAuthorize(): Promise<Response<string>> {
    return ApiClient.server().post(`/v2/weixin-auth/checkAuthorize`, {}, {
    }).then((res) => {
      return res.data
    })
  }

}
