<template>
  <!-- 我的优惠券 右侧表单组件 -->
  <div>
  <el-form ref="form" :model="value">
    <draggable v-model="value.propTabInfos" handle=".move-icon" animation="300" @change="handleDragChange">
      <div class="CouponOptions" v-for="(item, index) in value.propTabInfos" :key="index">
        <el-form-item :prop="`propTabInfos[${index}].customText`" :rules="getValidationRules(index)">
          <div class="title">
            {{ item.customText }}
            <el-switch v-if="isShowSwitch(item)" @change="handleChange" v-model="item.show" inactive-color="#D0D4DA" style="margin-left: 8px">
            </el-switch>
            <img class="move-icon" src="~assets/image/icons/ic_sixcircle.png">
          </div>
          <div class="Copywriting" v-if="tabDesc(item)">{{tabDesc(item)}}</div>
          <el-input type="text" :placeholder="i18n('请输入')" v-model="item.customText" maxlength="5" @blur="handleChange"></el-input>
        </el-form-item>
        <el-form-item v-if="isShowScene(item)" :prop="`propTabInfos[${index}].couponSceneType`" :rules="sendCouponRules">
          <div class="text">{{ i18n('发券场景') }}: </div>
          <el-radio v-model="item.couponSceneType" @change="handleChange" label="all">
            {{ i18n('全部场景') }}
            <el-tooltip placement="top">
              <div slot="content">
                {{ i18n('除微信支付代金券和平台券以外的') }}
                <br />
                {{ i18n('CRM发的全部优惠券') }}
              </div>
              <i class="el-icon-warning"/>
            </el-tooltip>
          </el-radio>
          <el-radio v-model="item.couponSceneType" @change="handleChange" label="part">{{ i18n('指定场景') }}</el-radio>
          <el-button v-show="item.couponSceneType == 'part'" type="primary" @click="goUp(index)">{{ i18n('选择') }}</el-button>
          <div class="wz" v-show="item.couponSceneType == 'part'">
            {{ i18n('已选择') }}
            <span>{{ item.couponScenes.length }}</span>
            {{ i18n('个场景') }}
          </div>
        </el-form-item>
        <el-button class="delete" type="text" @click="remove(index)" v-if="canRemove(item)">{{ i18n('删除') }}</el-button>
      </div>
      <el-button class="btn" @click="add" v-show="value.propTabInfos.length < 9">+{{ i18n('添加tab') }}</el-button>
    </draggable>
  </el-form>
  <SelectPopUp ref="selectRef" :value="value" @change="changechecked" />
</div>

</template>

<script lang="ts" src="./CouponTab.ts"></script>

<style lang="scss" scoped>
.CouponOptions {
  background: #f0f2f6;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 12px;
  position: relative;
  .title {
    position: relative;
    font-weight: 600;
    font-size: 14px;
    color: #24272b;
    line-height: 14px;
    .move-icon {
      position: absolute;
      right: 0;
      width: 20px;
      height: 20px;
      cursor: move;
    }
  }
  .text {
    font-weight: 400;
    font-size: 14px;
    color: #5a5f66;
    line-height: 20px;
  }
  .img {
    position: absolute;
    left: 76px;
    top: 33px;
    width: 16px;
    height: 16px;
    cursor: pointer;
  }
  .Copywriting {
    font-weight: 400;
    font-size: 13px;
    color: #a1a6ae;
    line-height: 18px;
    margin-top: 12px;
  }
  .el-input {
    margin-top: 24px;
  }
  .el-form-item {
    margin-bottom: 20px;
  }
  .delete {
    position: absolute;
    bottom: 12px;
    right: 12px;
  }
  .wz {
    margin-left: 130px;
    font-weight: 400;
    font-size: 12px;
    color: #a1a6ae;
    line-height: 16px;
    span {
      color: #ffaa00;
    }
  }
}
.btn {
  width: 100%;
  height: 36px;
  background: #ffffff;
  border-radius: 4px;
  font-weight: 400;
  font-size: 12px;
  color: #007eff;
}
</style>
