<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:02
 * @LastEditTime: 2023-08-03 17:41:42
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\list\ListWrapper.vue
 * 记得注释
-->
<template>
  <div class="list-wrapper">
    <div class="list-wrapper-query" v-if="showQuery">
      <slot name="query" v-if="$slots.query"></slot>
    </div>
    <div class="line-blank" style="height: 15px;background-color: #EEEFF1" v-if="showQuery"></div>
    <div :style="'padding: 20px;background:white;' + `border-radius: ${showQuery ? '8px;' : '0'}`">
      <div class="btn-group" v-if="$slots.btn">
        <slot name="btn"></slot>
      </div>
      <div class="btn-group" v-if="$slots.toolbar">
        <slot name="toolbar"></slot>
      </div>
      <div class="list" v-if="$slots.list">
        <div>
          <slot name="list"></slot>
        </div>
      </div>
      <div class="page" v-if="$slots.page">
        <slot name="page"></slot>
      </div>
    </div>
  </div>
</template>

<script lang='ts' src='./ListWrapper.ts'/>

<style lang='scss'>
.list-wrapper {
  flex: 1;
  /*overflow-y: auto;*/
  /*margin: 20px;*/
  background-color: rgb(238, 239, 241);
  overflow: auto;

  .list-wrapper-query {
    background: #ffffff;
    border-radius: 8px;
    padding: 24px;
  }

  .qf-form-item {
    margin-top: 0 !important;
  }

  .page {
    margin-top: 20px;
  }
  .btn-group {
    margin-top: 20px;
    margin-bottom: 20px;
  }
  .list {
    .el-table__body {
      .el-table__expanded-cell {
        border-bottom: 1px solid #d7dfeb !important;
      }
      .el-table__row {
        td {
          border-bottom: 1px solid #d7dfeb !important;
        }
      }
    }
  }
}
</style>