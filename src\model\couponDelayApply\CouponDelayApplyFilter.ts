export default class CouponDelayApplyFilter {
  // 会员标识类似于
  memberCodeLikes: Nullable<string> = null
  // 营销中心等于
  marketingCenterEquals: Nullable<string> = null
  // 购券门店id等于
  purchaseOrgIdEquals: Nullable<string> = null
  // 状态in
  stateIn: string[] | null = []
  // 状态等于
  stateEquals: Nullable<string> = null
  // 延期时间在(,]之间
  delayApplyTimeBetweenOpenClosed: Date[] = []
  // 延期时间在[,)之间
  delayApplyTimeBetweenClosedOpen: Date[] = []
  // 会员id等于
  memberIdEquals: Nullable<string> = null
  // 活动号等于
  // activityNumberEquals?: Nullable<string> = null
  // 活动名称等于
  // activityNameEquals?: Nullable<string> = null
  // 活动类型等于
  activityTypeEquals: Nullable<string> = null
  // 活动形式等于
  activityFormEquals: Nullable<string> = null
  // 券模板号等于
  templateNumberEquals: Nullable<string> = null
  // 券模板名称等于
  // templateNameEquals?: Nullable<string> = null

  activityNameLikes: Nullable<string> = null
  activityNumberLikes: Nullable<string> = null
  templateNameLikes: Nullable<string> = null
  // 页数>=0
  page: number = 0
  // 页面大小>0
  pageSize: number = 0
}