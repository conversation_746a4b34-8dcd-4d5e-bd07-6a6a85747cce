/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2023-01-12 17:06:24
 * @LastEditors: 黎钰龙
 * @Description:
 * @FilePath: \phoenix-web-ui\src\model\member_v2\member\MemberFilter.ts
 * 记得注释
 */
import TagData from "model/member_standard/TagData";

export default class MemberFilter {
  // 身份识别码等于
  identCode: Nullable<string> = null;
  // 身份识别码等于
  identCodeEquals: Nullable<string> = null;
  // 手机号等于
  mobileEquals: Nullable<string> = null;
  // 会员号等于
  crmCodeEquals: Nullable<string> = null;
  // 实体卡号等于
  hdCardCardNumberEquals: Nullable<string> = null;
  // 姓名类似于
  nameLikes: Nullable<string> = null;
  // 归属营销中心
  marketingCenterEquals: Nullable<string> = null;
  // 等级代码等于
  gradeEquals: Nullable<string> = null;
  // 会员状态等于 取值：Using-使用中；Blocked-已冻结；Unactivated-未激活；Canceled-已注销
  stateEquals: Nullable<string> = null;
  // 归属门店等于
  ownerStoreIdEquals: Nullable<string> = null;
  // 注册门店等于
  registerStoreIdEquals: Nullable<string> = null;
  // 注册渠道等于
  registerChannelEquals: Nullable<string> = null;
  // 招募方式等于
  recruitmentMethodEquals: Nullable<string> = null;
  // 邀请人会员等于
  refereeCodeEquals: Nullable<string> = null;
  // 邀请人员工等于
  referredEmployeeIdEquals: Nullable<string> = null;
  // 注册日期大于等于
  registerTimeGreaterOrEquals: Nullable<Date> = null;
  // 注册日期小于
  registerTimeLess: Nullable<Date> = null;
  // 激活日期大于等于
  activeTimeGreaterOrEquals: Nullable<Date> = null;
  // 激活日期小于
  activeTimeLess: Nullable<Date> = null;
  // 创建日期大于等于
  createdGreaterOrEquals: Nullable<Date> = null;
  // 创建日期小于
  createdLess: Nullable<Date> = null;
  // 生日大于等于MM-dd
  birthMonthDayGreaterOrEquals: Nullable<string> = null;
  // 生日小于等于MM-dd
  birthMonthDayLessOrEquals: Nullable<string> = null;
  // 生日大于等于yyyy-MM-dd
  birthDayGreaterOrEquals: Nullable<Date> = null;
  // 生日小于等于yyyy-MM-dd
  birthDayLessOrEquals: Nullable<Date> = null;
  // 性别等于 取值：男,女
  genderEquals: Nullable<string> = null;
  // 邮箱类似于
  mailLikes: Nullable<string> = null;
  // 标签
  mustHaveTags: Nullable<TagData[]> = null;
  // 标签
  userGroupNameEquals: Nullable<string> = null;
  // 页码
  page: Nullable<number> = null;
  // 页面大小
  pageSize: Nullable<number> = null;
  // 渠道ID
  registerChannelIdEquals: Nullable<string> = null
  // 名称类似于
  nickNameLikes: Nullable<string> = null
  // 会员渠道类型In
  memberChannelTypeIn: Nullable<Array<string>> = null
  // 会员渠道Id等于
  memberChannelIdEquals: Nullable<string> = null
}
