import {Component, Prop, Vue} from 'vue-property-decorator'
import LoginApi from 'http/login/LoginApi'
import ChangePassword from 'model/login/ChangePassword'
import BrowserMgr, {LocalStorage} from 'mgr/BrowserMgr'
import {State} from 'vuex-class'
import UserLoginResult from "model/login/UserLoginResult";

@Component({
  name: 'ChangePwd',
  components: {}
})
export default class ChangePwd extends Vue {

  get changeRules() {
    let pwdStrictFormat = BrowserMgr.LocalStorage.getItem('sysConfig')?.enableStrongPasswordVerification
    let strictReg = /^(?=.*\d)(?=.*[A-Z])(?=.*[a-z])(?=.*[\x21-\x2f\x3a-\x40\x5b-\x60\x7B-\x7F])[\da-zA-Z\x21-\x2f\x3a-\x40\x5b-\x60\x7B-\x7F]{8,16}$/;
    
    let rules = {
      oldPassword: [{required: true, message: this.formatI18n('/系统/上导航/密码修改弹框/原密码/提示/请输入原密码'), trigger: 'change'}, {
        min: 4,
        max: 16,
        message: this.formatI18n('/系统/上导航/密码修改弹框/原密码/提示/长度应在{0}到{1}之间', null, ['6', '16']),
        trigger: 'change'
      }],
      newPassword: [{
        required: true, trigger: 'change',
        validator: (rule: string, value: string = '', callback: (e?: any) => {}) => {
          let minLength = this.loginInfo.useStrict ? 8 : 6
          if (pwdStrictFormat) {
            if (value === '') {
              callback(new Error(this.formatI18n('/系统/上导航/密码修改弹框/新密码/提示/请输入新密码')))
            } else if (!strictReg.test(value)) {
              callback(new Error(this.formatI18n('密码长度应在8到16位之间，且包含大小写字母，数字，特殊字符')))
            }else {
              callback()
            }
          } else {
            if (value === '') {
              callback(new Error(this.formatI18n('/系统/上导航/密码修改弹框/新密码/提示/请输入新密码')))
            } else if (this.loginInfo.useStrict && !ChangePwd.pwdRegex.test(value)) {
              callback(new Error(this.formatI18n('/系统/上导航/密码修改弹框/原密码/密码需包括数字、字母、特殊字符3种字符')))
            } else if (value.length < minLength || value.length > 16) {
              callback(new Error(this.formatI18n('/系统/上导航/密码修改弹框/原密码/提示/长度应在{0}到{1}之间', null, [minLength + '', '16'])))
            // } else if (value === this.ruleForm.oldPassword) {
            //   callback(new Error(this.formatI18n('/系统/上导航/密码修改弹框/原密码和新密码输入相同/提示/新密码不应和旧密码一样')))
            } else {
              callback()
            }
          }
        }
      }],
      confirmNewPassword: [{
        required: true, trigger: 'change',
        validator: (rule: string, value: string = '', callback: (e?: any) => {}) => {
          let minLength = this.loginInfo.useStrict ? 8 : 6
          if (pwdStrictFormat) {
            if (value === '') {
              callback(new Error(this.formatI18n('/系统/上导航/密码修改弹框/新密码/提示/请输入新密码')))
            } else if (!strictReg.test(value)) {
              callback(new Error(this.formatI18n('密码长度应在8到16位之间，且包含大小写字母，数字，特殊字符')))
            } else if (value !== this.ruleForm.newPassword) {
              callback(new Error(this.formatI18n('/系统/上导航/密码修改弹框/新密码和确认新密码输入不一致/提示/两次输入的密码应保持一致')))
            } else {
              callback()
            }
          } else {
            if (value === '') {
              callback(new Error(this.formatI18n('/系统/上导航/密码修改弹框/确认新密码/提示/请再次输入新密码')))
            } else if (this.loginInfo.useStrict && !ChangePwd.pwdRegex.test(value)) {
              callback(new Error(this.formatI18n('/系统/上导航/密码修改弹框/原密码/密码需包括数字、字母、特殊字符3种字符')))
            } else if (value.length < minLength || value.length > 16) {
              callback(new Error(this.formatI18n('/系统/上导航/密码修改弹框/原密码/提示/长度应在{0}到{1}之间', null, [minLength + '', '16'])))
            } else if (value !== this.ruleForm.newPassword) {
              callback(new Error(this.formatI18n('/系统/上导航/密码修改弹框/新密码和确认新密码输入不一致/提示/两次输入的密码应保持一致')))
            } else {
              callback()
            }
          }
          
        }
      }]
    }
    return rules
  }
  static pwdRegex = new RegExp('(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9])');
  static pwdStrictRegex = new RegExp('/^(?=.*\d)(?=.*[A-Z])(?=.*[a-z])(?=.*[\x21-\x2f\x3a-\x40\x5b-\x60\x7B-\x7F])[\da-zA-Z\x21-\x2f\x3a-\x40\x5b-\x60\x7B-\x7F]{8,16}$/')
  @State('loginInfo')
  loginInfo: UserLoginResult
  // 弹框的隐藏
  @Prop({ type: Boolean, default: false }) visible: boolean
  $refs: any
  ruleForm: ChangePasswordForm = new ChangePasswordForm()

  created() {
  }

  onCancel() {
    this.$refs.changePwdForm.resetFields()
    this.$emit('change', false)
    this.$emit('update:visible', false)
  }

  onConfirm() {
    this.$refs.changePwdForm.validate((valid: any) => {
      if (valid) {
        if (this.loginInfo.resetToken) {
          let modifyParams: ChangePassword = new ChangePassword()
          modifyParams.account = this.loginInfo.user!.account
          modifyParams.newPassword = this.ruleForm.newPassword
          modifyParams.password = this.ruleForm.oldPassword
          modifyParams.resetToken = this.loginInfo.resetToken
          LoginApi.expireChangePassword(modifyParams).then((resp: any) => {
            if (resp && resp.code === 2000) {
              this.$message.success(this.formatI18n('/系统/上导航/密码修改弹框/密码修改成功'))
              this.$emit('change', true)
            } else {
              this.$message.error(resp.msg)
            }
          }).catch((error) => {
            if (error && error.message) {
              this.$message.error(error.message)
            }
          })
        } else {
          let info: any = LocalStorage.getItem('ucenterUser')
          let modifyParams: ChangePassword = new ChangePassword()
          modifyParams.account = info.account
          modifyParams.newPassword = this.ruleForm.newPassword
          modifyParams.password = this.ruleForm.oldPassword
          LoginApi.changePassword(modifyParams).then((resp: any) => {
            if (resp && resp.code === 2000) {
              this.$message.success(this.formatI18n('/系统/上导航/密码修改弹框/密码修改成功'))
              this.$emit('change', true)
            } else {
              this.$message.error(resp.msg)
            }
          }).catch((error) => {
            if (error && error.message) {
              this.$message.error(error.message)
            }
          })
        }
      } else {
        return false
      }
    })
  }

}

class ChangePasswordForm {
  oldPassword: string = ''
  newPassword: string = ''
  confirmNewPassword: string = ''
}
