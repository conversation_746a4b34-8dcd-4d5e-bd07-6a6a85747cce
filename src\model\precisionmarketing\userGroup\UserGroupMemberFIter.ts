/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-09-18 19:44:46
 * @LastEditTime: 2024-09-18 19:45:03
 * @LastEditors: fang<PERSON><PERSON><PERSON>
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\precisionmarketing\userGroup\UserGroupMemberFIter.ts
 * 记得注释
 */
export default class UserGroupMemberFIter {
  // 客群id等于
  userGroupIdEquals: Nullable<string> = null
  // 身份识别码等于
  identCodeEquals: Nullable<string> = null
  // 页数
  page: number = 0
  // 页面大小
  pageSize: number = 0
}