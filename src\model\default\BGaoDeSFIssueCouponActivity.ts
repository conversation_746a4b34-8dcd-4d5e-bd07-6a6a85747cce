import DateTimeCondition from "model/common/DateTimeCondition"
import IdName from "model/common/IdName"
import BaseCouponActivity from "model/v2/coupon/BaseCouponActivity"

export default class BGaoDeSFIssueCouponActivity extends BaseCouponActivity {
  // 券类型
  couponType: Nullable<GaodeCouponType> = null
  // 券模板
  couponTemplate: Nullable<IdName> = null
  // 券数量
  qty: Nullable<number> = null
  // 原价
  price: Nullable<number> = null
  // 售价
  salePrice: Nullable<number> = null
  // 商品类目
  goodsCategory: Nullable<GoodsCategory> = null
  // 商品头部图片
  headPicUrl: Nullable<string> = null
  // 商品详情图片
  picUrls: string[] = []
  // 支持限量
  supportLimit: Nullable<boolean> = null
  // 活动时间条件
  dateTimeCondition: Nullable<DateTimeCondition> = null
  // 是否修改活动库存
  modifyStock: Nullable<boolean> = null
  // 商品描述
  goodsDescription: Nullable<string> = null
}

export enum GaodeCouponType {
  // 团购
  groupBuy = 'groupBuy',
  // 代金券
  voucher = 'voucher',
}

export enum GoodsCategory {
  // 商超通用券
  UNIVERSAL_VOUCHER = 'UNIVERSAL_VOUCHER',
  // 家庭清洁
  HOME_CLEANING = 'HOME_CLEANING',
  // 乳饮酒水
  MILK_ALCOHOLIC = 'MILK_ALCOHOLIC',
  // 粮油副食
  GRAIN_OIL = 'GRAIN_OIL',
  // 母婴用品
  MATERNAL_CHILD_PRODUCTS = 'MATERNAL_CHILD_PRODUCTS',
  // 美妆个护
  BEAUTY_CARE = 'BEAUTY_CARE',
  // 冲调食品
  BREWING_FOOD = 'BREWING_FOOD',
  // 休闲零食
  CASUAL_SNACKS = 'CASUAL_SNACKS',
  // 保健品
  HEALTH_PRODUCTS = 'HEALTH_PRODUCTS',
  // 肉蛋水产
  MEAT_EGGS_SEAFOOD = 'MEAT_EGGS_SEAFOOD',
  // 烘焙熟食
  BAKED_COOKED_FOOD = 'BAKED_COOKED_FOOD',
  // 家居日用
  HOME_DAILY_USE = 'HOME_DAILY_USE',
  // 文体商品
  CULTURAL_SPORTS_PRODUCTS = 'CULTURAL_SPORTS_PRODUCTS',
  // 水果蔬菜
  FRUITS_VEGETABLES = 'FRUITS_VEGETABLES',
  // 冷藏冷冻
  FREEZING = 'FREEZING',
  // 数码电器
  DIGITAL_APPLIANCES = 'DIGITAL_APPLIANCES',
  // 服饰鞋包
  CLOTHING_SHOES_BAGS = 'CLOTHING_SHOES_BAGS',
  // 家用电器
  HOUSEHOLD_ELECTRIC = 'HOUSEHOLD_ELECTRIC',
  // 运动户外
  SPORTS_OUTDOOR = 'SPORTS_OUTDOOR',
}

export class GoodsCategoryOption {
  static list() {
    return [
      { label: '商超通用券', value: GoodsCategory.UNIVERSAL_VOUCHER },
      { label: '家庭清洁', value: GoodsCategory.HOME_CLEANING },
      { label: '乳饮酒水', value: GoodsCategory.MILK_ALCOHOLIC },
      { label: '粮油副食', value: GoodsCategory.GRAIN_OIL },
      { label: '母婴用品', value: GoodsCategory.MATERNAL_CHILD_PRODUCTS },
      { label: '美妆个护', value: GoodsCategory.BEAUTY_CARE },
      { label: '冲调食品', value: GoodsCategory.BREWING_FOOD },
      { label: '休闲零食', value: GoodsCategory.CASUAL_SNACKS },
      { label: '保健品', value: GoodsCategory.HEALTH_PRODUCTS },
      { label: '肉蛋水产', value: GoodsCategory.MEAT_EGGS_SEAFOOD },
      { label: '烘焙熟食', value: GoodsCategory.BAKED_COOKED_FOOD },
      { label: '家居日用', value: GoodsCategory.HOME_DAILY_USE },
      { label: '文体商品', value: GoodsCategory.CULTURAL_SPORTS_PRODUCTS },
      { label: '水果蔬菜', value: GoodsCategory.FRUITS_VEGETABLES },
      { label: '冷藏冷冻', value: GoodsCategory.FREEZING },
      { label: '数码电器', value: GoodsCategory.DIGITAL_APPLIANCES },
      { label: '服饰鞋包', value: GoodsCategory.CLOTHING_SHOES_BAGS },
      { label: '家用电器', value: GoodsCategory.HOUSEHOLD_ELECTRIC },
      { label: '运动户外', value: GoodsCategory.SPORTS_OUTDOOR },
    ]
  }
}

