import BaseReportFilter from 'model/report/query/BaseReportFilter'

export default class PointsStoreReportFilter extends BaseReportFilter {
  // 发生组织id等于
  occurredOrgIdEquals: Nullable<string> = null
  // 得积分小于等于
  obtainLessOrEquals: Nullable<number> = null
  // 得积分大于等于
  obtainGreaterOrEquals: Nullable<number> = null
  // 消费得积分小于等于
  consumeObtainLessOrEquals: Nullable<number> = null
  // 消费得积分大于等于
  consumeObtainGreaterOrEquals: Nullable<number> = null
  // 用积分小于等于
  useLessOrEquals: Nullable<number> = null
  // 用积分大于等于
  useGreaterOrEquals: Nullable<number> = null
  // 兑换积分小于等于
  exchangeLessOrEquals: Nullable<number> = null
  // 兑换积分大于等于
  exchangeGreaterOrEquals: Nullable<number> = null
  // 抵扣积分小于等于
  deductLessOrEquals: Nullable<number> = null
  // 抵扣积分大于等于
  deductGreaterOrEquals: Nullable<number> = null
  // 交易时间位于
  tranTimeBetween: Date[] = []
}