import AbstractImportDialog from 'cmp/abstract-import-dialog/AbstractImportDialog';
import FormItem from 'cmp/formitem/FormItem';
import I18nPage from 'common/I18nDecorator';
import EnvUtil from 'util/EnvUtil';
import { Component } from 'vue-property-decorator';
@Component({
  name: 'UploadBuyerDialog',
  components: {
    FormItem
  }
})
@I18nPage({
  prefix: ["/公用/券模板", "/会员/会员资料", "/会员/会员资料/会员资料导入", "/公用/导入", "/公用/按钮", "/会员/黑名单"],
  auto: true,
})
export default class UploadBuyerDialog extends AbstractImportDialog {
  // 文件模板
  get templateHref() {
    if (location.href.indexOf("localhost") === -1) {
      return "./file/modifyCardBuyer.xlsx";
    } else {
      return "../file/modifyCardBuyer.xlsx";
    }
  }

  get getUploadUrl() {
    return EnvUtil.getServiceUrl() + `v1/prepay-card/importCardBuyer`;
  }

  uploadSuccess() {
    this.$emit("uploadSuccess");
  }

  close() {
    this.dialogShow = false;
  }
};