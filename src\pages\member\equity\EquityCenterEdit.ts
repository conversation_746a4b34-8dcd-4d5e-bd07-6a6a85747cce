import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import UploadImg from 'cmp/upload-img/UploadImg';
import I18nPage from 'common/I18nDecorator';
import BenefitConfigApi from 'http/grade/equityCenter/BenefitConfigApi';
import BenefitConfig from 'model/member/BenefitConfig';
import CommonUtil from 'util/CommonUtil';
import { Component, Vue } from 'vue-property-decorator';

class Form {
  iconUrl: string = ''
  equityName: string = ''
  remark: string = ''
}

@Component({
  name: 'EquityCenterEdit',
  components: {
    BreadCrume,
    UploadImg
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/会员/权益中心',
    '/公用/按钮'
  ],
  auto: true
})
export default class EquityCenterEdit extends Vue {
  $refs: any
  editType: 'create' | 'edit' = 'create'
  ruleForm: Form = new Form()
  rules: any = {}
  uuid: Nullable<string> = null //权益uuid
  version: Nullable<number> = null  //权益版本

  created() {
    this.editType = this.$route.query.editType === 'edit' ? 'edit' : 'create'
    if (this.editType === 'edit') {
      this.getDtl()
    }
    this.initRules()
  }

  get panelArray() {
    return [
      {
        name: this.i18n("/公用/菜单/权益中心"),
        url: "equity-center-dtl",
      },
      {
        name: this.editType === 'create' ? this.i18n("新增权益") : this.i18n('编辑权益'),
        url: ""
      }
    ]
  }

  getDtl() {
    const loading = CommonUtil.Loading()
    BenefitConfigApi.get(this.$route.query.id as string).then((res) => {
      if (res.code === 2000 && res.data) {
        this.doBind(res.data)
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    }).finally(() => {
      loading.close()
    })
  }

  doSave() {
    this.$refs.form.validate().then(() => {
      const body = this.doParams()
      const loading = CommonUtil.Loading()
      const saveFunc = this.editType === 'create' ? BenefitConfigApi.save : BenefitConfigApi.modify
      saveFunc(body).then((res) => {
        if (res.code === 2000) {
          this.$message.success(this.i18n('保存成功'))
          this.$router.push({
            name: 'equity-center-dtl'
          })
        } else {
          throw new Error(res.msg!)
        }
      }).catch((error: any) => {
        this.$message.error(error.message)
      }).finally(() => {
        loading.close()
      })
    })
  }

  doParams() {
    const params = new BenefitConfig()
    params.name = this.ruleForm.equityName
    params.imagePath = this.ruleForm.iconUrl
    params.remark = this.ruleForm.remark
    params.uuid = this.editType === 'edit' ? this.uuid : null
    params.version = this.version ? this.version : null
    return params
  }

  doBind(data: BenefitConfig) {
    this.ruleForm.equityName = data.name || ''
    this.ruleForm.iconUrl = data.imagePath || ''
    this.ruleForm.remark = data.remark || ''
    this.uuid = data.uuid
    this.version = data.version
  }

  doCancel() {
    this.$router.back()
  }

  uploadImgChange() {
    this.$refs.form.validateField('iconUrl')
  }

  initRules() {
    this.rules = {
      iconUrl: [{
        required: true, message: this.i18n("请上传权益图标"), trigger: 'change'
      }],
      equityName: [{
        required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: ['change', 'blur']
      }],
    }
  }
};