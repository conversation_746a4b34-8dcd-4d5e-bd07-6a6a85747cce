import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import GoodsSelectorDialog from 'cmp/selectordialogs/GoodsSelectorDialog.vue'
import ImportDialog from "cmp/importdialogs/ImportDialog.vue";
import RSGoods from 'model/common/RSGoods'
import AmountToFixUtil from 'util/AmountToFixUtil'
import PickUpGoods from 'model/common/PickUpGoods'
import IdName from 'model/common/IdName'
import NumberUtil from 'util/NumberUtil'
import I18nPage from 'common/I18nDecorator';
import CatogorySelectorDialog from 'cmp/selectordialogs/CatogorySelectorDialog';
import RSCategory from 'model/common/RSCategory';
import AutoFixInput from 'cmp/autofixinput/AutoFixInput';

@Component({
  name: "ChooseGoodsRange",
  components: {
    GoodsSelectorDialog,
    ImportDialog,
    CatogorySelectorDialog,
    AutoFixInput
  },
})
@I18nPage({
  prefix: [
    "/公用/券模板",
    '/资料/商品',
    '/公用/券模板/提货券/用券商品'
  ],
  auto: true
})
export default class ChooseGoodsRange extends Vue {
  $refs: any;
  data: PickUpGoods[] = [];
  currentData: PickUpGoods[] = [];	//dat数据过大时，使用分页查询
  page: any = {
    currentPage: 1,
    pageSize: 10
  }
  typeValue: 'ALL' | 'PART' = 'PART'
  switchFlag = false;
  @Prop()
  type: 'ALL' | 'PART'
  @Prop()
  enablePayApportion: boolean;
  @Prop()
  value: any;
  @Prop({ type: Boolean, default: false })
  isChooseAllGoods: boolean;
  @Prop({ type: Boolean, default: true }) isImport: boolean;
  @Prop({ type: Boolean, default: true }) canAddCategory: boolean; //是否能够添加品类
  @Prop({ type: Boolean, default: false }) isSelectSpecialGoods: boolean; //是否选择特殊商品
  @Prop({ type: Boolean, default: false }) isSelectByCode: boolean; //是否根据商品code作为唯一标识(为false则以barcode作为唯一标识)
  @Prop({
    type: String,
    default: "barcode",
  })
  goodsMatchRuleMode: "barcode" | "code";
  @Prop({ type: Boolean, default: false })
  appreciationGoods: boolean;
  @Prop({ type: String, default: 'normal' })
  chooseGoodType: String;
  @Watch("type", { deep: true })
  onTypeChange(value: any) {
    this.typeValue = value
  }
  // @Watch('data', { immediate: true })
  // handle(value: PickUpGoods[]) {
  //   this.currentData = value.slice(this.page.currentPage - 1, this.page.pageSize) || []
  // }
  @Watch('value.length', { immediate:  true })
  watchValue() {
    if (this.value && this.value.length > 0) {
      this.data = this.value;
      this.data.forEach((item) => {
        item.bookPayPrice = NumberUtil.format(item.bookPayPrice);
      });
    }
  }

  @Watch('chooseGoodType')
  handle(value: string, oldValue: string) {
    // 普通商品/增值商品类型修改了，需要清空
    if (value !== oldValue) {
      this.data = []
    }
  }


  isClear: Boolean = true;
  // 商品单位
  unitList: any[] = [{
    value: true,
    label: this.formatI18n('/公用/券模板/单品折扣券/用券门槛/千克')
  },
  {
    value: false,
    label: this.formatI18n('/公用/券模板/单品折扣券/用券门槛/件')
  }]

  //null-可以选择品类和单品、true-只能选择单品、false-只能选择品类
  get isChooseGoods() {
    if (this.appreciationGoods && this.chooseGoodType === 'appreciation') {
      return true
    }
    if (this.data.length === 0) {
      return null //可以选择品类、单品
    } else if (this.data[0].goods?.id || (this.data[0].code && this.data[0].qpcStr)) {
      return true
    } else {
      return false
    }
  }

  get getTemplateFilePath() {
    return this.goodsMatchRuleMode == 'code' ? 'template_code_with_amount.xlsx' : 'template_barcode_with_amount.xlsx';
  }

  get getImportUrl() {
    return this.goodsMatchRuleMode == 'code' ? 'v1/goods/importCodes' : 'v1/goods/importBarcodes';
  }
  get currentTableData() {
    const startIndex = (this.page.currentPage - 1) * this.page.pageSize
    const endIndex = (this.page.currentPage) * this.page.pageSize
    return this.data.slice(startIndex, endIndex)
  }

  getDefaultValue(value: PickUpGoods[]) {
    console.log('父组件给1传信息',value)
    this.data = value || []
  }
  changeSwitch(flag: any) {
    this.$emit("changeSwitch", flag);
  }
  doChange(index: number) {
    const dataIndex = index + (this.page.currentPage - 1) * this.page.pageSize
    if (this.data[dataIndex].isDisp) {
      //千克
      this.data[dataIndex].qty = AmountToFixUtil.formatNumberWithInteger(this.data[dataIndex].qty, 999999.999, 0.001, 3);
    } else{
      //件
      this.data[dataIndex].qty = AmountToFixUtil.formatNumber(this.data[dataIndex].qty, 999999, 1);
    }
    this.data[index].bookPayPrice = AmountToFixUtil.formatAmount(this.data[dataIndex].bookPayPrice, 999999.0, 0.01, 2);
    this.$emit("input", this.data);
    this.$emit("change", this.data);
  }
  doClear(index: number) {
    const dataIndex = index + (this.page.currentPage - 1) * this.page.pageSize
    this.data[dataIndex].qty = null;
    this.data[dataIndex].bookPayPrice = null;
    this.$emit("input", this.data);
    this.$emit("change", this.data);
  }
  doDelete(index: number) {
    const dataIndex = index + (this.page.currentPage - 1) * this.page.pageSize
    this.data.splice(dataIndex, 1);
    if (this.data.length <= 10) {
      this.page.currentPage = 1
    }
    this.$emit("input", this.data);
    this.$emit("change", this.data);
  }
  doAdd() {
    let arr: RSGoods[] = [];
    if (this.data && this.data.length > 0) {
      this.data.forEach((item: any) => {
        let obj: RSGoods = new RSGoods();
        if (this.goodsMatchRuleMode == 'code') {
          obj.code = item.code
          obj.qpcStr = item.qpcStr
        } else {
          obj.barcode = item.goods.id;
        }
        obj.name = item.goods.name;
        obj.price = item.price;
        arr.push(obj);
      });
    }
    this.$refs.selectGoodsScopeDialog.open(arr);
  }
  doSubmitGoods(arr: RSGoods[]) {
    console.log('dialog选择的商品信息',arr);
    let recordDataArray: any = JSON.parse(JSON.stringify(this.data));
    this.data = [];
    if (arr && arr.length > 0) {
      for (let i = 0; i < arr.length; i++) {
        let flagIndex = recordDataArray.findIndex((item: any) => arr[i].barcode === item.goods.id)
        if (flagIndex > -1 && this.goodsMatchRuleMode == 'barcode') {
          this.data.push(recordDataArray[flagIndex]);
        } else {
          let obj: PickUpGoods = new PickUpGoods();
          obj.goods = new IdName();
          obj.goods.id = this.goodsMatchRuleMode === 'barcode' ? arr[i].barcode : arr[i].code;
          obj.code = arr[i].code
          obj.goods.name = arr[i].name;
          obj.price = arr[i].price;
          obj.qty = 1;
          obj.qpcStr = arr[i].qpcStr;
          obj.isDisp = false
          obj.appreciationGoods = arr[i].appreciationGoods
          this.data.push(obj);
        }
      }
    }
    console.log('最后提交给父组件的信息 :>> ', this.data );
    this.$emit("input", this.data);
    this.$emit("change", this.data);
  }
  doAddCategory() {
    let arr: RSCategory[] = [];
    if (this.data && this.data.length > 0) {
      this.data.forEach((item: any) => {
        let obj: RSCategory = new RSCategory();
        obj.category = new IdName()
        obj.category.id = item.category.id;
        obj.category.name = item.category.name;
        arr.push(obj);
      });
    }
    this.$refs.selectCatogoryScopeDialog.open(arr, 'multiple')
  }
  doSubmitCategorys(arr: RSCategory[]) {
    console.log('选择的品类', arr);
    let recordDataArray: any = JSON.parse(JSON.stringify(this.data));
    this.data = [];
    if (arr && arr.length > 0) {
      for (let i = 0; i < arr.length; i++) {
        let flagIndex = recordDataArray.findIndex((item: any) => arr[i].category?.id === item.category.id)
        if (flagIndex > -1) {
          this.data.push(recordDataArray[flagIndex]);
        } else {
          let obj: PickUpGoods = new PickUpGoods();
          obj.category = new IdName();
          obj.category.id = arr[i].category?.id;
          obj.category.name = arr[i].category?.name;
          obj.qty = null;
          obj.isDisp = false
          this.data.push(obj);
        }
      }
    }
    this.$emit("input", this.data);
    this.$emit("change", this.data);
  }
  formatNum(index: any) {
    let obj = this.data[index].bookPayPrice ? this.data[index].bookPayPrice!.toString() : "";

    obj = obj.replace(/[^\d.]/g, ""); // 清除"数字"和"."以外的字符
    obj = obj.replace(/^\./g, ""); // 验证第一个字符是数字
    obj = obj.replace(/\.{2,}/g, "."); // 只保留第一个, 清除多余的
    obj = obj
      .replace(".", "$#$")
      .replace(/\./g, "")
      .replace("$#$", ".");
    obj = obj.replace(/^(\-)*(\d+)\.(\d\d).*$/, "$1$2.$3"); // 只能输入两个小数
    this.data[index].bookPayPrice = Number(obj);
  }

  doImport() {
    this.$refs.importDialog.show();
  }

  doUploadSuccess(res: any) {
    if (res.response.code === 2000) {
      const arr = res.response.data.importResults || []
      if (this.isClear) {
        this.data = []
      }
      let recordDataArray: any = JSON.parse(JSON.stringify(this.data));
      arr.map((item: any, index: number) => {
        let flagIndex = recordDataArray.findIndex((good: any) => (this.goodsMatchRuleMode == 'code' ? (item.code === good.code && item.qpcStr === good.qpcStr) : item.barcodes === good.goods.id))
        if (flagIndex > -1) {
          if (this.goodsMatchRuleMode == 'code') {
            this.data[index].code = item.code
            this.data[index].qpcStr = item.qpcStr
          } else {
            this.data[index].goods!.id = item.barcodes
          }
          this.data[index].goods!.name = item.goodsName
          this.data[index].price = item.price;
          this.data[index].qty = item.qty;
          this.data[index].isDisp = false
        } else {
          let obj: PickUpGoods = new PickUpGoods();
          obj.goods = new IdName();

          if (this.goodsMatchRuleMode == 'code') {
            obj.goods.id = item.code;
            obj.code = item.code;
            obj.qpcStr = item.qpcStr;
          } else {
            obj.goods.id = item.barcodes;
          }
          obj.goods.name = item.goodsName;
          obj.price = item.price;
          obj.qty = item.qty;
          obj.isDisp = false;
          this.data.push(obj);
        }
      })
      this.$emit("input", this.data);
      this.$emit("change", this.data);
    } else {
      this.$message.error(res.response.msg);
    }
  }
  clearChange(isClear: Boolean) {
    this.isClear = isClear;
  }
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
  }
}