<template>
  <div class="prepay-card-view">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button @click="doBatchImport" v-if="hasOptionPermission('/卡/卡管理/卡查询/卡查询','导入购卡人')">{{ formatI18n('/卡/卡管理', '导入购卡人') }}</el-button>
        <el-button @click="doBatchExport" v-if="hasOptionPermission('/卡/卡管理/卡查询/卡查询','预付卡查看')">{{ formatI18n('/会员/会员资料', '批量导出') }}</el-button>
      </template>
    </BreadCrume>
    <div class="current-page">
      <el-row>
        <el-tabs @tab-click="changeTabs" v-model="query.cardTypeEquals">
          <el-tab-pane v-if="hasOptionPermission('/卡/卡管理/卡模板/礼品卡','卡模板查看')" label="礼品卡" name="GiftCard" />
          <el-tab-pane v-if="hasOptionPermission('/卡/卡管理/卡模板/充值卡','卡模板查看')" label="充值卡" name="ImprestCard" />
          <el-tab-pane v-if="hasOptionPermission('/卡/卡管理/卡模板/储值卡','卡模板查看')" label="储值卡" name="RechargeableCard" />
          <el-tab-pane v-if="hasOptionPermission('/卡/卡管理/卡模板/次卡','卡模板查看')" label="次卡" name="CountingCard" />
        </el-tabs>
      </el-row>
      <div class="query">
        <el-form label-width="180px" v-show="queryMode === 'simple'">
          <el-row>
            <el-col :span="8">
              <el-form-item :label="i18n('卡面卡号')">
                <el-input v-model="query.codeStartsWith" :placeholder="i18n('卡面卡号起始于')" style="width: 80%" />
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="showCardBuyerOwnerAndInnerCode">
              <el-form-item :label="i18n('购卡人/持卡人')">
                <el-input :placeholder="i18n('请输入会员手机号/会员号/非会员购卡人')"
                  v-model="query.buyOrOwnerMemberEquals" style="width: 80%"
                  :title="i18n('请输入会员手机号/会员号/非会员购卡人')" />
              </el-form-item>
            </el-col>
            <el-col :span="8" v-else>
              <el-form-item :label="i18n('卡状态')">
                <el-select v-model="query.stateEquals" style="width: 80%">
                  <el-option :label="i18n('全部')" :value="null"></el-option>
                  <el-option :label="i18n('未激活')" value="UNACTIVATED"></el-option>
                  <el-option :label="i18n('转赠中')" value="PRESENTING"></el-option>
                  <el-option :label="i18n('使用中')" value="USING"></el-option>
                  <el-option :label="i18n('已挂失')" value="LOST"></el-option>
                  <el-option :label="i18n('已冻结')" value="FROZEN"></el-option>
                  <el-option :label="i18n('已使用')" value="USED"></el-option>
                  <el-option :label="i18n('已作废')" value="CANCELLED"></el-option>
                  <el-option :label="i18n('已回收')" value="RECOVER"></el-option>
                  <el-option :label="i18n('已制卡')" value="MADE"></el-option>
                  <el-option :label="i18n('空白卡')" value="BLANK"></el-option>
                  <el-option :label="i18n('坏卡')" value="BROKEN"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label=" ">
                <el-button type="primary" :loading="tableLoading" @click="onSearch" style="width:70px">
                  查询
                </el-button>
                <el-button :loading="tableLoading" @click="doReset" style="width:70px">
                  重置
                </el-button>
                <el-button type="text" @click="queryMode = 'senior'">{{i18n('/公用/查询条件/展开')}}<i class="el-icon-arrow-down"></i></el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-form label-width="180px" v-show="queryMode === 'senior'" style="box-shadow: 2px 4px 6px #bbbbbb;">
          <div class="query-content">
            <div class="query-item" v-if="showCardBuyerOwnerAndInnerCode">
              <el-form-item :label="i18n('内部卡号')">
                <el-input v-model="query.innerCodeLikes" :placeholder="i18n('内部卡号类似于')" style="width: 80%" />
              </el-form-item>
            </div>
            <div class="query-item">
              <el-form-item :label="i18n('卡面卡号')">
                <el-input v-model="query.codeStartsWith" :placeholder="i18n('卡面卡号起始于')" style="width: 80%" />
              </el-form-item>
            </div>
            <div class="query-item" v-if="showCardBuyerOwnerAndInnerCode">
              <el-form-item :label="i18n('购卡人')">
                <el-input :placeholder="i18n('请输入会员手机号/会员号/非会员购卡人')"
                  v-model="query.buyMemberEquals" style="width: 80%"
                  :title="i18n('请输入会员手机号/会员号/非会员购卡人')" />
              </el-form-item>
            </div>
            <div class="query-item" v-if="showCardBuyerOwnerAndInnerCode">
              <el-form-item :label="i18n('持卡人')">
                <el-input :placeholder="i18n('请输入手机号/会员号')" v-model="query.ownerMemberEquals" style="width: 80%" :title="i18n('请输入手机号/会员号')" />
              </el-form-item>
            </div>
            <div class="query-item">
              <el-form-item :label="i18n('卡状态')">
                <el-select v-model="query.stateEquals" style="width: 80%">
                  <el-option :label="i18n('全部')" :value="null"></el-option>
                  <el-option :label="i18n('未激活')" value="UNACTIVATED"></el-option>
                  <el-option :label="i18n('转赠中')" value="PRESENTING"></el-option>
                  <el-option :label="i18n('使用中')" value="USING"></el-option>
                  <el-option :label="i18n('已挂失')" value="LOST"></el-option>
                  <el-option :label="i18n('已冻结')" value="FROZEN"></el-option>
                  <el-option :label="i18n('已使用')" value="USED"></el-option>
                  <el-option :label="i18n('已作废')" value="CANCELLED"></el-option>
                  <el-option :label="i18n('已回收')" value="RECOVER"></el-option>
                  <el-option :label="i18n('已制卡')" value="MADE"></el-option>
                  <el-option :label="i18n('空白卡')" value="BLANK"></el-option>
                  <el-option :label="i18n('坏卡')" value="BROKEN"></el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="query-item" v-if="enableMultipleAccount">
              <el-form-item :label="i18n('账户类型')">
                <el-select v-model="query.accountTypeIdEquals" style="width: 80%">
                  <el-option :value="null" :label="i18n('全部')"></el-option>
                  <el-option no-i18n v-for="account of accounts" :key="account.id" :value="account.id" :label="account.name"></el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="query-item" v-if="['CountingCard'].indexOf(query.cardTypeEquals) === -1">
              <el-form-item :label="i18n('卡面额')">
                <div class="el-date-editor el-range-editor el-input__inner el-date-editor--daterange" style="width: 80%;">
                  <el-input placeholder="最小值" class="el-range-input" v-model="query.faceAmountGreaterOrEquals" />
                  <span class="el-range-separator" style="padding-left: 15px;padding-right: 15px;">-</span>
                  <el-input placeholder="最大值" class="el-range-input" v-model="query.faceAmountLessOrEquals" />
                </div>
              </el-form-item>
            </div>
            <div class="query-item" v-if="['RechargeableCard','CountingCard'].indexOf(query.cardTypeEquals) > -1">
              <el-form-item :label="i18n('发卡门店')">
                <SelectStores v-model="query.issueOrgIdEquals" :appendAttr="{orgTypeEquals: 'PHX'}" :isOnlyId="true" :hideAll="false" width="80%"
                  :placeholder="i18n('门店代码/名称')">
                </SelectStores>
              </el-form-item>
            </div>
            <div class="query-item" v-if="['CountingCard'].indexOf(query.cardTypeEquals) === -1">
              <el-form-item :label="i18n('卡余额')">
                <div class="el-date-editor el-range-editor el-input__inner el-date-editor--daterange" style="width: 80%;">
                  <el-input placeholder="最小值" class="el-range-input" v-model="query.balanceGreaterOrEquals" />
                  <span class="el-range-separator" style="padding-left: 15px;padding-right: 15px;">-</span>
                  <el-input placeholder="最大值" class="el-range-input" v-model="query.balanceLessOrEquals" />
                </div>
              </el-form-item>
            </div>
            <div class="query-item">
              <el-form-item :label="i18n('有效期至')">
                <el-date-picker style="width: 80%" v-model="dataRange" type="daterange" range-separator="-" start-placeholder="开始日期"
                  end-placeholder="结束日期">
                </el-date-picker>
              </el-form-item>
            </div>
            <div class="query-item">
              <el-form-item :label="i18n('卡模板')">
                <el-input :placeholder="i18n('请输入卡模板号或名称')" :title="i18n('请输入卡模板号或名称')" v-model="query.templateNameOrNumberLikes"
                  style="width: 80%" />
              </el-form-item>
            </div>
            <div class="query-item">
              <el-form-item :label="i18n('/储值/预付卡/充值卡制售单/列表页面/制售单')">
                <el-input placeholder="请输入单号" v-model="query.issueTransIdIdEquals" style="width: 80%" />
              </el-form-item>
            </div>
          </div>
          <el-row>
            <el-col :span="8">
              <el-form-item label=" ">
                <el-button type="primary" :loading="tableLoading" @click="onSearch" style="width:70px">
                  查询
                </el-button>
                <el-button :loading="tableLoading" @click="doReset" style="width:70px">
                  重置
                </el-button>
                <el-button type="text" @click="queryMode = 'simple'">{{i18n('/公用/查询条件/收起')}}<i class="el-icon-arrow-up"></i></el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <el-row class="table" id="table">
        <el-table :data="queryData" v-loading="tableLoading" style="width: 100%;margin-top: 20px" height="544" ref="multipleTable">
          <el-table-column fixed :label="i18n('内部卡号')" v-if="showCardBuyerOwnerAndInnerCode" prop="code" width="176" key="innerCode">
            <template slot-scope="scope">
              <!-- <el-button type="text" no-i18n @click="showHst(scope.row.code, '账户流水')"> -->
                {{ scope.row.innerCode || '--' }}
              <!-- </el-button> -->
            </template>
          </el-table-column>
          <el-table-column :label="i18n('卡面卡号')" prop="code" width="176" key="code">
            <template slot-scope="scope">
              <el-button type="text" no-i18n @click="showHst(scope.row.code, '账户流水')">
              {{ scope.row.code }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="卡状态" prop="state" width="126" key="state">
            <template slot-scope="scope">
              <PrepayState :state="scope.row.state" />
            </template>
          </el-table-column>
          <el-table-column label="购卡人/持卡人" v-if="showCardBuyerOwnerAndInnerCode" prop="buyerHdCardMbrId_ownerHdCardMbrId" width="250" key="buyer">
            <template slot-scope="scope">
              <el-row>
                <el-col :span="20" v-if="scope.row.buyerMemberNamespace === 'other'"
                  style="white-space: nowrap;text-overflow: ellipsis;overflow: hidden">
                  <span class="grey">购卡人：</span><span no-i18n
                    :title="dataUtil.showMemberId1(scope.row.buyerMobile,scope.row.buyerHdCardMbrId,scope.row.buyerHdCardCardNum,scope.row.buyerMemberId,query.cardTypeEquals,scope.row.hasBuyerMemberId)">{{
                    dataUtil.showMemberId1(scope.row.buyerMobile,scope.row.buyerHdCardMbrId,scope.row.buyerHdCardCardNum,scope.row.buyerMemberId,query.cardTypeEquals,scope.row.hasBuyerMemberId)
                  }}</span><br />
                  <span class="grey">持卡人：</span><span no-i18n
                    :title="dataUtil.showMemberId1(scope.row.ownerMobile,scope.row.ownerHdCardMbrId,scope.row.ownerHdCardCardNum)">{{
                    dataUtil.showMemberId1(scope.row.ownerMobile, scope.row.ownerHdCardMbrId, scope.row.ownerHdCardCardNum)
                  }}</span>
                </el-col>
                <el-col :span="20" v-if="scope.row.buyerMemberNamespace !== 'other'"
                  style="white-space: nowrap;text-overflow: ellipsis;overflow: hidden">
                  <span class="grey">购卡人：</span><span no-i18n
                    :title="dataUtil.showMemberId1(scope.row.buyerMobile,scope.row.buyerHdCardMbrId,scope.row.buyerHdCardCardNum,scope.row.buyerMemberId,query.cardTypeEquals,scope.row.hasBuyerMemberId)">{{
                    dataUtil.showMemberId1(scope.row.buyerMobile, scope.row.buyerHdCardMbrId, scope.row.buyerHdCardCardNum,scope.row.buyerMemberId,query.cardTypeEquals,scope.row.hasBuyerMemberId)
                  }}</span><br />
                  <span class="grey">持卡人：</span><span no-i18n
                    :title="dataUtil.showMemberId1(scope.row.ownerMobile,scope.row.ownerHdCardMbrId,scope.row.ownerHdCardCardNum)">{{
                    dataUtil.showMemberId1(scope.row.ownerMobile, scope.row.ownerHdCardMbrId, scope.row.ownerHdCardCardNum)
                  }}</span>
                </el-col>
                <el-col :span="4">
                  <!-- scope.row.hasBuyerMemberId为null时，此数据是会员且未被注销 -->
                  <el-button type="text" style="padding: 0 5px" @click="openChangeBuyerMemberDialog(scope.row)" v-if="scope.row.state != 'RECOVER' && scope.row.cardType !== '电子礼品卡' &&
                             (scope.row.buyerMemberNamespace !== 'member' ||
                             (scope.row.hasBuyerMemberId !== null &&
                             (scope.row.hasBuyerMemberId === true || scope.row.hasBuyerMemberId === false)))">{{ i18n('修改') }}
                  </el-button>
                </el-col>
              </el-row>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('有效期')" prop="expireDate" width="130" key="timeRange">
            <template slot-scope="scope">
              <p no-i18n>{{ format(scope.row.expireDate) }}</p>
              <span no-i18n v-if="scope.row.expireDate !== null && !isNewer(scope.row.expireDate)" style="color: green">{{ i18n('未过期') }}</span>
              <span no-i18n v-if="scope.row.expireDate !== null && isNewer(scope.row.expireDate)" style="color: red">{{ i18n('已过期') }}</span>
              <span no-i18n v-if="scope.row.expireDate === null">--</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('发卡组织/卡所属组织')" width="200" v-if="['RechargeableCard','CountingCard'].indexOf(query.cardTypeEquals) > -1"
            key="store">
            <template slot-scope="scope">
              <div>
                [{{scope.row.issueOrg.id}}]{{scope.row.issueOrg.name}}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="账户类型" prop="account" width="200" v-if="enableMultipleAccount" key="countType">
            <template slot-scope="scope">
              <span v-if="scope.row.account" no-i18n>[{{ scope.row.account.id }}] {{ scope.row.account.name }}</span>
              <span v-if="!scope.row.account" no-i18n>-</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('卡模板')" prop="body.state" width="200" key="cardTem">
            <template slot-scope="scope">
              <p class="grey" no-i18n>{{ scope.row.templateNumber }}</p>
              <el-button no-i18n type="text" @click="gotoCardTplDtl(scope.row.templateNumber, scope.row.cardType)">
                {{ scope.row.templateName }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('次数')" prop="totalTimes" width="156" key="totalTimes" v-if="query.cardTypeEquals === 'CountingCard'">
            <template slot-scope="scope">
              <div style="width: 190px">{{ scope.row.totalTimes.toFixed(0)}}</div>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('剩余可用次数')" prop="remainTimes" width="156" key="remainTimes" v-if="query.cardTypeEquals === 'CountingCard'">
            <template slot-scope="scope">
              <div style="width: 190px">{{ scope.row.remainderTimes.toFixed(0)}}</div>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('卡面额(元)')" prop="faceAmount" width="200" key="faceAmount" v-if="query.cardTypeEquals !== 'CountingCard'">
            <template slot-scope="scope">
              <div style="width: 190px">{{ scope.row.faceAmount.toFixed(2)}}</div>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('卡余额(元)')" prop="total" width="200" key="balance" v-if="query.cardTypeEquals !== 'CountingCard'">
            <template slot-scope="scope">
              <span no-i18n>{{ scope.row.total ? scope.row.total.toFixed(2) : '0.00' }} </span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('实充余额/返现余额(元)')" prop="balance/giftBalance" width="250" key="balance/giftBalance"
            v-if="query.cardTypeEquals !== 'CountingCard'">
            <template slot-scope="scope">
              <span class="grey">实充余额：</span>{{ scope.row.balance ? scope.row.balance.toFixed(2) : '0.00' }}<br />
              <span class="grey">返现余额：</span>{{ scope.row.giftBalance ? scope.row.giftBalance.toFixed(2) : '0.00' }}
            </template>
          </el-table-column>
          <el-table-column :label="i18n('充值卡制售单')" prop="body.state" width="200" v-if="query.cardTypeEquals === 'ImprestCard'" key="salesOrder">
            <template slot-scope="scope">
              <div no-i18n>
                <el-button type="text" @click="gotoImprestCardDtl(scope.row.preSaleOrderNumber)">
                  {{ scope.row.preSaleOrderNumber }}
                </el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column fixed="right" :label="i18n('操作')" width="240" key="option">
            <template slot-scope="scope">
              <el-button key="1" class="opt-btl" type="text" v-if="scope.row.hasPresent" @click="showHst(scope.row.code, '转赠过程')">
                转赠过程
              </el-button>
              <el-button key="2" class="opt-btl" type="text" v-if="canShowLoss(scope)" @click="openLossDialog(scope.row)">
                挂失
              </el-button>
              <el-button key="3" class="opt-btl" type="text" v-if="canShowRemoveLoss(scope)" @click="openUnLossDialog(scope.row)">
                解挂
              </el-button>
              <el-button key="4" class="opt-btl" type="text" v-if="canShowSupply(scope)" @click="openReissueDialog(scope.row)">
                补发
              </el-button>
              <el-button key="5" class="opt-btl" type="text" v-if="scope.row.state === 'USING' && hasOptionPermission('/卡/卡管理/卡查询/卡查询', '冻结/解冻')"
                @click="openFreezeDialog(scope.row)">
                冻结
              </el-button>
              <el-button key="6" class="opt-btl" type="text" v-if="scope.row.state === 'FROZEN' && hasOptionPermission('/卡/卡管理/卡查询/卡查询', '冻结/解冻')"
                @click="openUnfreezeDialog(scope.row)">
                解冻
              </el-button>
              <el-button key="7" class="opt-btl" type="text" @click="openResetPasswdDialog(scope.row)" v-if="canShowResetCode(scope)">
                重置密码
              </el-button>
              <el-button key="8" class="opt-btl" type="text" v-if="scope.row.state === 'USING' && hasOptionPermission('/卡/卡管理/卡查询/卡查询', '调整有效期')"
                @click="openAdjustDialog(scope.row)">
                调整有效期
              </el-button>
              <el-button key="9" class="opt-btl" type="text" v-if="canShowCancel(scope)" @click="invalid(scope.row.code)">
                作废
              </el-button>
              <el-button key="9" class="opt-btl" type="text" v-if="canShowBad(scope)" @click="registerBadCard(scope.row.code)">
                {{i18n('/卡/坏卡重制/登记坏卡')}}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-pagination no-i18n :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
        @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background layout="total, prev, pager, next, sizes,  jumper"
        class="pagin">
      </el-pagination>
    </div>
    <el-dialog title="预付卡详情" width="80%" class="prepay-dialog-center" :visible.sync="dtlDialog.dialogVisible">
      <PrepayCardDtl :code="dtlDialog.tmpCode" :defaultTab="dtlDialog.defaultTab"></PrepayCardDtl>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showHst('', dtlDialog.defaultTab)">取消</el-button>
        <el-button type="primary" @click="showHst('', dtlDialog.defaultTab)">确定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="预付卡转赠" class="prepay-dialog-center" :visible.sync="presentDialog.dialogVisible" width="30%">
      <el-form :inline="true" ref="presentForm" :rules="presentRules" :model="presentDialog" label-width="100" style="text-align: center">
        <el-form-item label="转赠会员" :required="true" prop="memberMobile">
          <el-input placeholder="请输入会员手机号码" v-model="presentDialog.memberMobile" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="presentDialog.dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="presentCard">确定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="卡挂失" :visible.sync="lossDialog.dialogVisible" width="30%" class="opt-dialog prepay-dialog-center">
      <el-row>
        <el-col :span="4" class="opt-dialog-icon">
          <i class="el-icon-warning"></i>
        </el-col>
        <el-col :span="18">
          <p class="primary-line">{{ lossDialog.row.cardType }}：{{ lossDialog.row.code }}</p>
          <p class="normal-line"><span class="normal-line-left">持卡人：</span>{{
              dataUtil.showMemberId1(lossDialog.row.ownerMobile, lossDialog.row.ownerHdCardMbrId, lossDialog.row.ownerHdCardCardNum)
            }}
          </p>
          <p class="normal-line"><span class="normal-line-left">卡余额：</span>{{
              lossDialog.row.total ?
                  lossDialog.row.total.toFixed(2) : '0.00'
            }}</p>
          <br />
          <p class="message-line">请确认是否挂失此卡？</p>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="lossDialog.dialogVisible = false">取消</el-button>
        <el-button :loading="lossDialog.confirmLoading" type="primary" @click="loss(lossDialog.row.code)">挂失</el-button>
      </span>
    </el-dialog>
    <el-dialog title="卡解挂" :visible.sync="unLossDialog.dialogVisible" width="30%" class="opt-dialog prepay-dialog-center">
      <el-row>
        <el-col :span="4" class="opt-dialog-icon">
          <i class="el-icon-warning"></i>
        </el-col>
        <el-col :span="18">
          <p class="primary-line">{{ unLossDialog.row.cardType }}：{{ unLossDialog.row.code }}</p>
          <p class="normal-line"><span class="normal-line-left">持卡人：</span>{{
              dataUtil.showMemberId1(unLossDialog.row.ownerMobile, unLossDialog.row.ownerHdCardMbrId, unLossDialog.row.ownerHdCardCardNum)
            }}
          </p>
          <p class="normal-line"><span class="normal-line-left">卡余额：</span>{{
              lossDialog.row.total ?
                  lossDialog.row.total.toFixed(2) : '0.00'
            }}</p>
          <br />
          <p class="message-line">请确认是否解挂此卡？</p>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="unLossDialog.dialogVisible = false">取消</el-button>
        <el-button :loading="unLossDialog.confirmLoading" type="primary" @click="unLoss(unLossDialog.row.code)">解挂</el-button>
      </span>
    </el-dialog>
    <el-dialog title="提示" :visible.sync="freezeDialog.dialogVisible" width="30%" class="opt-dialog prepay-dialog-center">
      <el-row>
        <el-col :span="4" class="opt-dialog-icon">
          <i class="el-icon-warning"></i>
        </el-col>
        <el-col :span="18">
          <p class="message-line">请确认是否冻结此卡？</p>
          <br />
          <p class="primary-line">{{ freezeDialog.row.cardType }}：{{ freezeDialog.row.code }}</p>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="freezeDialog.dialogVisible = false">取消</el-button>
        <el-button :loading="freezeDialog.confirmLoading" type="danger" @click="freeze(freezeDialog.row.code)">冻结</el-button>
      </span>
    </el-dialog>
    <el-dialog title="提示" :visible.sync="unfreezeDialog.dialogVisible" width="30%" class="opt-dialog prepay-dialog-center">
      <el-row>
        <el-col :span="4" class="opt-dialog-icon">
          <i class="el-icon-warning"></i>
        </el-col>
        <el-col :span="18">
          <p class="message-line">请确认是否解冻此卡？</p>
          <br />
          <p class="primary-line">{{ unfreezeDialog.row.cardType }}：{{ unfreezeDialog.row.code }}</p>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="unfreezeDialog.dialogVisible = false">取消</el-button>
        <el-button :loading="unfreezeDialog.confirmLoading" type="primary" @click="unfreeze(unfreezeDialog.row.code)">解冻</el-button>
      </span>
    </el-dialog>
    <el-dialog title="卡补发" :visible.sync="reissueDialog.dialogVisible" width="40%" class="opt-dialog prepay-dialog-center">
      <el-row>
        <el-form label-width="120px" ref="reissueForm" :rules="reissueRules" :model="reissueDialog">
          <el-form-item label="旧卡卡号">
            <el-input :disabled="true" :value="reissueDialog.row.code"></el-input>
          </el-form-item>
          <el-form-item label="新卡卡号" :required="true" prop="newCode">
            <el-input placeholder="请输入新卡卡号" v-model="reissueDialog.newCode"></el-input>
          </el-form-item>
          <el-form-item label="">
            <i class="el-icon-warning"></i><span style="word-break: break-word">补发卡除卡号外，其余卡属性与旧卡一致，历史记录均保留。</span>
          </el-form-item>
        </el-form>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="reissueDialog.dialogVisible = false">取消</el-button>
        <el-button :loading="reissueDialog.confirmLoading" type="primary" @click="reissue">确定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="重置支付密码" :visible.sync="resetPasswdDialog.dialogVisible" width="40%" class="opt-dialog prepay-dialog-center">
      <el-row>
        <el-form ref="resetPasswdForm" label-width="120px" :model="resetPasswdDialog" :rules="passwdRules">
          <el-form-item label="卡号">
            <el-input :disabled="true" :value="resetPasswdDialog.row.code"></el-input>
          </el-form-item>
          <el-form-item label="新支付密码" :required="true" prop="passwd">
            <el-input placeholder="请输入新支付密码，6位数字" v-model="resetPasswdDialog.passwd" type="password"></el-input>
          </el-form-item>
          <el-form-item label="确认新支付密码" :required="true" prop="passwdConfirm">
            <el-input placeholder="请再次输入新支付密码，6位数字" v-model="resetPasswdDialog.passwdConfirm" type="password"></el-input>
          </el-form-item>
        </el-form>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="resetPasswdDialog.dialogVisible = false">取消</el-button>
        <el-button :loading="resetPasswdDialog.confirmLoading" type="primary" @click="resetPasswd">确定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="修改购卡人" :visible.sync="changeBuyerMemberDialog.dialogVisible" width="40%" class="opt-dialog prepay-dialog-center">
      <el-row>
        <el-form ref="resetPasswdForm" label-width="120px" :model="changeBuyerMemberDialog">
          <el-form-item label="卡号">
            <el-input :disabled="true" v-model="changeBuyerMemberDialog.code" style="width: 80%"></el-input>
          </el-form-item>
          <el-form-item label="购卡人">
            <el-input maxlength="30" v-model="changeBuyerMemberDialog.name" style="width: 80%" placeholder="请输入非会员购卡人信息"></el-input>
          </el-form-item>
        </el-form>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="changeBuyerMemberDialog.dialogVisible = false">取消</el-button>
        <el-button :loading="changeBuyerMemberDialog.confirmLoading" type="primary" @click="doChangeBuyerMember">确定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="调整有效期" :visible.sync="adjustDialog.dialogVisible" width="40%" class="opt-dialog prepay-dialog-center">
      <el-row>
        <el-form ref="adjustForm" label-width="120px" :rules="adjustRules" :model="adjustDialog">
          <el-form-item label="弹框/当前卡有效期">
            <el-input :disabled="true" v-model="adjustDialog.originExpire" style="width: 80%"></el-input>
          </el-form-item>
          <el-form-item label="弹框/调整后卡有效期" required prop="expire">
            <el-date-picker :picker-options="dateRangeOption" style="width: 80%" v-model="adjustDialog.expire" type="date"
              placeholder="弹框/placeholder/选择日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="弹框/调整说明">
            <el-input type="textarea" maxlength="140" resize="none" v-model="adjustDialog.remark" style="width: 80%;" show-word-limit
              placeholder="弹框/请输入不超过140个字">
            </el-input>
          </el-form-item>
        </el-form>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="adjustDialog.dialogVisible = false">取消</el-button>
        <el-button :loading="adjustDialog.confirmLoading" type="primary" @click="doAdjustSubmit">确定</el-button>
      </span>
    </el-dialog>
    <DownloadCenterDialog :dialogvisiable="fileDialogVisible" :showTip="showTip" @dialogClose="doDownloadDialogClose">
    </DownloadCenterDialog>
    <UploadBuyerDialog ref="uploadCardBuyer" @uploadSuccess="uploadSuccess"></UploadBuyerDialog>

  </div>
</template>

<script lang="ts" src="./PrepayCardList.ts">
</script>

<style lang="scss">
.prepay-card-view {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: hidden;

  .query-content {
    background-color: #ffffff;
    display: flex;
    flex-wrap: wrap;
    .query-item {
      width: calc((100% - 10px) / 3);
    }
  }

  .el-textarea__inner {
    height: 100px;
  }

  .prepay-dialog-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .total {
    margin: 20px;
  }

  .opt-btl {
    margin-left: 0 !important;
    margin-right: 15px;
  }

  .current-page {
    // background-color: red;
    height: calc(100% - 267px);
    position: relative;

    .query {
      .el-row {
        background-color: white;
      }

      z-index: 999;
      position: absolute;
      height: 80px;
      width: 100%;

      .el-form-item {
        margin-bottom: 5px;
      }

      .el-range__icon {
        line-height: 26px;
      }

      .el-range-separator {
        line-height: 26px;
      }

      .el-range__close-icon {
        line-height: 26px;
      }
    }

    .el-select {
      width: 100%;
    }

    .row {
      padding: 20px 20px 0 20px;
    }

    .table {
      padding: 0 20px 20px 20px;
      margin-top: 50px;
      height: calc(100% - 40px);
      // background-color: aqua;
      .grey {
        color: #999999;
      }

      .state {
        width: 7px;
        height: 7px;
        border-radius: 10px;
        float: left;
        margin-top: 7px;
        margin-right: 5px;
      }
    }

    .pagin {
      padding: 40px 20px;
    }

    .el-tabs__nav-scroll {
      padding: 5px 20px;
    }
  }

  div.el-range-input {
    input.el-input__inner {
      border: none;
      padding: 0px;
      line-height: 1;
      height: 100%;
    }
  }

  .opt-dialog {
    .opt-dialog-icon {
      font-size: 30px;
      height: 120px;
      text-align: center;
      line-height: 70px;
    }

    .primary-line {
      font-weight: 600;
      font-size: 18px;
    }

    .normal-line {
      .normal-line-left {
        color: #999999;
      }
    }

    .message-line {
    }
  }
}
</style>
