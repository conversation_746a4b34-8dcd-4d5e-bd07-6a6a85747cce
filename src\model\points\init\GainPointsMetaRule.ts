import BasePointsRule from 'model/points/init/BasePointsRule'
import GoodsGroup from 'model/points/init/GoodsGroup'

export default class GainPointsMetaRule extends BasePointsRule {
    // 每销费多少金额或数量，不满不积分
    cost: Nullable<number> = null
    // 获得多少积分
    points: Nullable<number> = null
    // 每人每天得积分次数上限
    maxDailyGainPointsTimes: Nullable<number> = null
    //  超过每天得积分次数上限，是否冻结会员，true为冻结，false不冻结
    blockMemberExceedMaxDailyGainPointsTimes: Nullable<boolean> = null
    // 特殊商品组列表
    goodsGroupList: GoodsGroup[] = []
}