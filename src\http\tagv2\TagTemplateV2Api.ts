import ApiClient from "http/ApiClient";
import Response from "model/default/Response";
import TagTemplateFilterV2 from "model/precisionmarketing/tagv2/TagTemplateFilterV2";
import TagTemplateMemberExportFilterV2 from "model/precisionmarketing/tagv2/TagTemplateMemberExportFilterV2";
import TagTemplateMemberFilterV2 from "model/precisionmarketing/tagv2/TagTemplateMemberFilterV2";
import TagTemplateMemberV2 from "model/precisionmarketing/tagv2/TagTemplateMemberV2";
import TagTemplateMetricsFilter from "model/precisionmarketing/tagv2/TagTemplateMetricsFilter";
import TagTemplateMetricsV2 from "model/precisionmarketing/tagv2/TagTemplateMetricsV2";
import TagTemplateV2 from "model/precisionmarketing/tagv2/TagTemplateV2";

export default class TagTemplateV2Api {
  /**
   * 根据标签模板uuid删除标签模板
   *
   */
  static delete(tagTemplateUuid: string): Promise<Response<void>> {
    return ApiClient.server()
      .post(
        `/v2/precision-marketing/tag-template/delete`,
        {},
        {
          params: {
            tagTemplateUuid: tagTemplateUuid,
          },
        },
      )
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 批量导出会员信息
   * 批量导出会员信息。
   *
   */
  static exportTagMember(body: TagTemplateMemberExportFilterV2): Promise<Response<void>> {
    return ApiClient.server()
      .post(`/v2/precision-marketing/tag-template/exportTagMember`, body, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 根据uuid查询标签模板信息
   *
   */
  static get(tagTemplateUuid: string): Promise<Response<TagTemplateV2>> {
    return ApiClient.server()
      .get(`/v2/precision-marketing/tag-template/get/${tagTemplateUuid}`, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 获取标签分布信息
   *
   */
  static getTagTemplateMetrics(body: TagTemplateMetricsFilter): Promise<Response<TagTemplateMetricsV2>> {
    return ApiClient.server()
      .post(`/v2/precision-marketing/tag-template/getTagTemplateMetrics`, body, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 分页查询标签模板
   *
   */
  static query(body: TagTemplateFilterV2): Promise<Response<TagTemplateV2[]>> {
    return ApiClient.server()
      .post(`/v2/precision-marketing/tag-template/query`, body, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 查询标签模板会员信息
   * 查询标签模板会员信息。
   *
   */
  static queryTagTemplateMember(body: TagTemplateMemberFilterV2): Promise<Response<TagTemplateMemberV2[]>> {
    return ApiClient.server()
      .post(`/v2/precision-marketing/tag-template/queryTagTemplateMember`, body, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 修改或保存标签模板，以uuid区分新建或修改,返回uuid
   *
   */
  static saveOrUpdate(body: TagTemplateV2): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v2/precision-marketing/tag-template/saveOrUpdate`, body, {})
      .then((res) => {
        return res.data;
      });
  }
}
