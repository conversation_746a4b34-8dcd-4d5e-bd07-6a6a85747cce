// import { WeixinAppCouponEntryEnum } from 'model/default/WeixinAppCouponEntryEnum'

export default class BCouponEntry {
  /**
   * 入口类型： MY_COUPON, // 我的券
   *   PLAT_COUPON, // 平台券
   *   PAY_COUPON, // 支付券
   *   PRESENT_RECORD, // 赠送记录
   */
  couponType: Nullable<string> = null
  // 入口名
  title: Nullable<string> = null
  // 备注
  remark: Nullable<string> = null
  // 顺序
  sort: Nullable<number> = null
  // 是否开启：start 开启,stop 关闭
  tableState: Nullable<string> = null
}