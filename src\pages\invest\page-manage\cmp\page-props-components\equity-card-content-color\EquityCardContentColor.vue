<template>
  <div class="box">
    <el-form :label-position="labelPosition" :model="value" ref="form" label-width="100px">
      <p class="set">
        <span>{{ i18n('内容颜色') }}</span>
      </p>
      <el-radio v-model="value.propSelectColorStyle" label="default">{{ i18n('/会员/会员资料/默认') }}</el-radio>
      <div style="min-height: 12px;">
        <el-form-item v-if="value.propSelectColorStyle === 'default'"  label-width="24px" style="margin-bottom: 12px;">
          <el-row :gutter="20" class="margin-left-common margin-left-common-default">
            <el-col :span="8" :key="item.id" v-for="item in options">
              <div class="nightfury-flex-left-center">
                <div class="label">{{ item.label }}</div>
                <el-color-picker style="width: 32px" @change="handleChange" disabled v-model="value[item.id]"
                  size="small"></el-color-picker>
              </div>
            </el-col>
          </el-row>
        </el-form-item>
      </div>
      <el-radio v-model="value.propSelectColorStyle" label="custom">{{ i18n('/设置/微信小程序装修/自定义') }}</el-radio>
      <el-form-item v-if="value.propSelectColorStyle === 'custom'" label-width="24px">
        <el-row :gutter="20" class="margin-left-common">
          <el-col :span="8" :key="item.id" v-for="item in options">
            <div class="nightfury-flex-left-center">
              <div class="label">{{ item.label }}</div>
              <el-color-picker style="width: 32px" @change="handleChange" v-model="value[item.id]"
                size="small"></el-color-picker>
            </div>
          </el-col>
        </el-row>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" src="./EquityCardContentColor.ts"></script>

<style lang="scss" scoped>
.box {
  padding: 12px;
  background: #f0f2f6;
  border-radius: 2px;
  margin-bottom: 20px;

  .set {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    span {
      font-weight: 600;
      font-size: 14px;
      color: #24272b;
      line-height: 20px;
    }
  }

  .nightfury-flex-left-center {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .label {
      font-weight: 400;
      font-size: 14px;
      color: #5a5f66;
      line-height: 20px;
    }
  }

  .el-form-item {
    margin-bottom: 0;
  }

 .margin-left-common-default {
  ::v-deep .el-color-picker__mask {
    display: none;
  }
 }
}
</style>
