<template>
    <el-dialog :before-close="doBeforeClose" :close-on-click-modal="false"  :visible.sync="dialogShow" class="store-value-adjust-reason-add" title="修改储值调整原因">
        <div class="wrap">
            <FormItem label="储值调整原因">
                <el-input @blur="doBlur" ref="reason" v-model="reason"></el-input>
                <div style="color: red" v-if="flag">储值原因不能为空</div>
            </FormItem>
        </div>
        <div class="dialog-footer" slot="footer">
            <el-button @click="doModalClose('cancel')">取消</el-button>
            <el-button @click="doModalClose('confirm')" size="small" type="primary">确定</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts" src="./StoreValueAdjustReasonAdd.ts">
</script>

<style lang="scss">
    .store-value-adjust-reason-add{
        display: flex;
        align-items: center;
        justify-content: center;
        .el-dialog{
            width: 582px;
            height: 226px;
            margin: 0 !important;
        }
        .wrap{
            width: 560px;
            margin: 0 auto;
            padding: 20px 30px 30px 30px;
            overflow: auto;
            .item{
                width: 228px;
                height: 108px;
                border: 1px solid #c7c7c7;
                border-radius: 10px;
                display: inline-block;
                margin-bottom: 24px;

                &:nth-child(odd) {
                    margin-right: 12px;

                }
                &:nth-child(even) {
                    margin-left: 12px;
                }
            }
        }
        .el-dialog__body{
            height: 119px;
        }
    }
</style>