import ApiClient from 'http/ApiClient'
import DepositEvaluation from 'model/deposit/activity/DepositEvaluation'
import MemberBalancePromotionActivity from 'model/payment/member/MemberBalancePromotionActivity'
import MemberBalancePromotionActivityFilter from 'model/payment/member/MemberBalancePromotionActivityFilter'
import MemberBalancePromotionActivityQueryResult from 'model/payment/member/MemberBalancePromotionActivityQueryResult'
import RSGrade from 'model/common/RSGrade'
import Response from 'model/common/Response'
import MemberBalancePromotionActivityNew from 'model/payment/member/MemberBalancePromotionActivityNew'

export default class MemberBalancePromotionApi {
  /**
   * 审核活动
   *
   */
  static audit(activityId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/member-balance-promotion/audit/${activityId}`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 批量审核储值支付活动
   *
   */
  static batchAudit(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/member-balance-promotion/batch/audit`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 批量删除储值支付活动
   *
   */
  static batchRemove(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/member-balance-promotion/batch/remove`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 批量终止储值支付活动
   *
   */
  static batchStop(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/member-balance-promotion/batch/stop`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 创建活动
   *
   */
  static create(body: MemberBalancePromotionActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/member-balance-promotion/create`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 创建折扣活动
   *
   */
  static createDiscount(body: MemberBalancePromotionActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/member-balance-promotion/createDiscount`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 活动效果评估
   *
   */
  static evaluate(activityId: string): Promise<Response<DepositEvaluation>> {
    return ApiClient.server().get(`/v1/member-balance-promotion/evaluate/${activityId}`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询等级列表
   *
   */
  static gradeList(): Promise<Response<RSGrade[]>> {
    return ApiClient.server().get(`/v1/member-balance-promotion/gradeList`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询活动
   *
   */
  static info(activityId: string): Promise<Response<MemberBalancePromotionActivity>> {
    return ApiClient.server().get(`/v1/member-balance-promotion/info/${activityId}`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询折扣活动详情
   *
   */
  static infoDiscount(activityId: string): Promise<Response<MemberBalancePromotionActivity>> {
    return ApiClient.server().get(`/v1/member-balance-promotion/infoDiscount/${activityId}`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 修改活动
   *
   */
  static modify(body: MemberBalancePromotionActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/member-balance-promotion/modify`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 修改折扣活动
   *
   */
  static modifyDiscount(body: MemberBalancePromotionActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/member-balance-promotion/modifyDiscount`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询活动
   *
   */
  static query(body: MemberBalancePromotionActivityFilter): Promise<Response<MemberBalancePromotionActivityQueryResult>> {
    return ApiClient.server().post(`/v1/member-balance-promotion/query`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 删除活动
   *
   */
  static remove(activityId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/member-balance-promotion/remove/${activityId}`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 保存并审核活动
   *
   */
  static saveAndAudit(body: MemberBalancePromotionActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/member-balance-promotion/saveAndAudit`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 保存并审核折扣活动
   *
   */
  static saveAndAuditDiscount(body: MemberBalancePromotionActivityNew): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/member-balance-promotion/saveAndAuditDiscount`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 终止活动
   *
   */
  static stop(activityId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/member-balance-promotion/stop/${activityId}`, {}, {}).then((res) => {
      return res.data
    })
  }

}
