import ApiClient from 'http/ApiClient'
import AuditResult from 'model/couponDelayApply/AuditResult'
import CouponDelayApply from 'model/couponDelayApply/CouponDelayApply'
import CouponDelayApplyFilter from 'model/couponDelayApply/CouponDelayApplyFilter'
import CouponDelayApplyLog from 'model/couponDelayApply/CouponDelayApplyLog'
import CouponDelayApplyRequest from 'model/couponDelayApply/CouponDelayApplyRequest'
import MutableNsid from 'model/common/MutableNsid'
import Response from 'model/common/Response'
import DelayCouponLine from 'model/couponDelayApply/DelayCouponLine'
import QueryDelayCouponLineRequest from 'model/couponDelayApply/QueryDelayCouponLineRequest'

export default class CouponDelayApplyApi {
  /**
   * 券延期申请审核
   * 券延期申请审核
   * 
   */
  static audit(body: CouponDelayApplyRequest): Promise<Response<AuditResult>> {
    return ApiClient.server().post(`/v1/coupon-delay-apply/audit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 券延期申请详情
   * 券延期申请详情
   * 
   */
  static detail(uuid: string): Promise<Response<CouponDelayApply>> {
    return ApiClient.server().post(`/v1/coupon-delay-apply/detail/${uuid}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量导出
   * 批量导出。
   * 
   */
  static export(body: CouponDelayApplyFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/coupon-delay-apply/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 券延期申请列表查询
   * 券延期申请列表查询
   * 
   */
  static query(body: CouponDelayApplyFilter): Promise<Response<CouponDelayApply[]>> {
    return ApiClient.server().post(`/v1/coupon-delay-apply/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 券延期申请日志查询
   * 券延期申请日志查询
   * 
   */
  static queryLogsByTradeId(body: MutableNsid): Promise<Response<CouponDelayApplyLog[]>> {
    return ApiClient.server().post(`/v1/coupon-delay-apply/queryLogsByTradeId`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 券延期申请驳回
   * 券延期申请驳回
   * 
   */
  static reject(body: CouponDelayApplyRequest): Promise<Response<boolean>> {
    return ApiClient.server().post(`/v1/coupon-delay-apply/reject`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
 * 延期券明细
 * 延期券明细
 * 
 */
  static queryCoupons(body: QueryDelayCouponLineRequest): Promise<Response<DelayCouponLine[]>> {
    return ApiClient.server().post(`/v1/coupon-delay-apply/queryCoupons`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
