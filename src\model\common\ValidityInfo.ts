import { ValidityType } from "model/common/ValidityType";
import { ExpiryType } from "./ExpiryType";

export default class ValidityInfo {
	// 有效期类型：FIXED——固定有效期； RALATIVE——相对有效期;
	validityType: Nullable<ValidityType> = null;
	// 延迟生效天数
	delayEffectDays: Nullable<number> = null;
	// 有效天数
	validityDays: Nullable<number> = null;
	// 起始日期
	beginDate: Nullable<Date> = null;
	// 截止日期
	endDate: Nullable<Date> = null;
	// 日期类型
	expiryType: Nullable<ExpiryType> = null;
	// 有效月数
	months: Nullable<number> = null;
  // 自然日
  dayLastTime: Nullable<number> = null;
}
