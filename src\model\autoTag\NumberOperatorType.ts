/*
 * @Author: 黎钰龙
 * @Date: 2024-12-10 14:41:36
 * @LastEditTime: 2024-12-10 14:41:50
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\autoTag\NumberOperatorType.ts
 * 记得注释
 */
export enum NumberOperatorType {
  // 
  eq = 'eq',
  // 
  ne = 'ne',
  // 
  gt = 'gt',
  // 
  ge = 'ge',
  // 
  lt = 'lt',
  // 
  le = 'le',
  // 
  between = 'between',
  // 
  value = 'value',
  // 
  no_value = 'no_value'
}