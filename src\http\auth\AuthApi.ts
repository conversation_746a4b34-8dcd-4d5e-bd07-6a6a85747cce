import ApiClient from 'http/ApiClient'
import Response from 'model/response/Response'
import LoginResult from 'model/auth/LoginResult'
import PermissonResult from 'model/auth/PermissonResult'

export default class ProjectApi {
  /**
   * 获取验证码 username=test01&password=123456&captcha=9999&redirectUrl=null
   */
  static login(username: string, password: string, captcha: string, redirectUrl: string): Promise<Response<LoginResult>> {
    return ApiClient.server().post(`login-server/login.hd`, {
      username: username,
      password: password,
      captcha: captcha
    }, {}).then((res) => {
      return res.data
    })
  }

  static permissions(): Promise<Response<PermissonResult>> {
    return ApiClient.server().post(`crm-web/app/getPermissions.hd`, {}, {
      params: {}
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改密码
   */
  static navChangePassword(params: any): Promise<Response<any>> {
    return ApiClient.server().post(`login-server/sys/saveModifyUser.hd`, params).then((res) => {
      return res.data
    })
  }
  // uni登录 交换token
  static changeToken(corsToken: Nullable<string>): Promise<any> {
		return ApiClient.server()
			.get(`/v1/uni/token`, {
				params: {
					'cors-token': corsToken,
          action: 'change-token'
				}
			})
			.then((res) => {
				return res
			});
	}
}