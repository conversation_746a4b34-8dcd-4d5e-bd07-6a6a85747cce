import {Component, Vue} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import ListWrapper from 'cmp/list/ListWrapper.vue'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import FloatBlock from 'cmp/floatblock/FloatBlock.vue'
import BreadCrume from "cmp/bread-crumb/BreadCrume";
import I18nPage from "common/I18nDecorator";
import SaveChannelRequest from "model/channel/SaveChannelRequest";
import ChannelManagementApi from "http/channelmanagement/ChannelManagementApi";
import ChannelManagement from "model/channel/ChannelManagement";
import Channel from "model/common/Channel";
import ChannelManagementFilter from "model/channel/ChannelManagementFilter";
import JsonBeautifyUtil from 'util/JsonBeautifyUtil'
import ChannelBatchRequest from "model/channel/ChannelBatchRequest";
import ChannelStateCmp from "pages/datum/channel/state/ChannelStateCmp";
import {ChannelState} from "model/channel/ChannelState";

@Component({
  name: 'Channel',
  components: {
    ChannelStateCmp,
    FormItem,
    ListWrapper,
    SubHeader,
    FloatBlock,
    BreadCrume,
  }
})
@I18nPage({
  auto: false,
  prefix: [
    '/资料/渠道',
    '/公用/按钮',
  ],
})
export default class ChannelList extends Vue {
  i18n: I18nFunc
  query: ChannelManagementFilter = new ChannelManagementFilter()
  queryData: ChannelManagement[] = []
  selected: ChannelManagement[] = []
  $refs: any
  hasOptionPermission: any
  panelArray: any = []
  checkedAll: boolean = false
  hideDisabled: boolean = false
  newIns: SaveChannelRequest = new SaveChannelRequest()
  updateIns: SaveChannelRequest = new SaveChannelRequest()
  loading = false
  isWeimob:boolean = false
  kgDia:boolean = false
  extendInfo = {
    defaultOrgId:null,
    defaultOuterOrgId:null,
    visible:false,
  }
  modifyDialog = {
    visible: false,
  }
  infoDialog: any = {
    row: {},
    visible: false
  }
  channelTypes: any
  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }

  get editable() {
    return this.hasOptionPermission('/设置/渠道/渠道', '资料维护')
  }

  created() {
    this.panelArray = [
      {
        name: this.i18n('渠道'),
        url: ''
      },
    ]
    this.getChannelType().then(()=>{
      this.getList()
    })
  }
  extendDialog(row:any) {
    this.extendInfo.visible = true
    this.extendInfo.defaultOrgId = row.defaultOrgId
    this.extendInfo.defaultOuterOrgId = row.defaultOuterOrgId
  }

  handleClose(close:any) {
    close()
  }
  isWeimobFn() {
    if(this.newIns.channelManagement!.channel.type == 'weimob') {
      this.isWeimob = true
    }else {
      this.isWeimob = false
    }
  }

  doSearch() {
    this.page.currentPage = 1
    this.getList()
  }

  doReset() {
    this.query = new ChannelManagementFilter()
    this.page.currentPage = 1
    this.getList()
  }

  /**
   * 查询
   */
  onSearch() {
    this.page.currentPage = 1
    this.getList()
  }

  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.getList()
  }

  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.getList()
  }

  /**
   * 表格排序条件
   */
  onSortChange({column, prop, order}: any) {
    // todo
  }

  handleSelectionChange(val: any) {
    this.selected = val
  }

  private getList() {
    let query = JSON.parse(JSON.stringify(this.query))
    query.page = this.page.currentPage - 1
    query.pageSize = this.page.size
    if (this.hideDisabled) {
      query.stateEquals = ChannelState.ENABLED
    }
    this.loading = true
    ChannelManagementApi.query(query).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.queryData = resp.data
        this.page.total = resp.total
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    }).finally(() => {
      this.loading = false
    })
  }

  //查询渠道类型
  private getChannelType() {
    return ChannelManagementApi.getChannelTypes().then((res)=>{
      this.channelTypes = {}
      if(res && res.code === 2000) {
        res.data?.forEach((item)=>{
          if(item.id && item.name) {
            const objItem = {
              [item.id]: this.i18n(item.name)
            }
            this.channelTypes = {...this.channelTypes,...objItem}
          }
        })
      } else {
        this.$message.error(this.i18n('查询渠道类型失败'))
      }
    }).catch(()=>{
      this.$message.error(this.i18n('查询渠道类型失败'))
    })
  }

  private add() {
    if (!this.newIns.channelManagement || !this.newIns.channelManagement.channel || !this.newIns.channelManagement.channel.type) {
      this.$message.error(this.i18n('请选择渠道类型'))
      return
    }
    if (!this.newIns.channelManagement || !this.newIns.channelManagement.channel || !this.newIns.channelManagement.channel.id) {
      this.$message.error(this.i18n('请填写渠道ID'))
      return
    }
    if (!new RegExp(/(^[A-Za-z0-9]+$)/).test(this.newIns.channelManagement.channel.id) && this.newIns.channelManagement.channel.id !== '-') {
      this.$message.error(this.i18n('渠道ID仅支持字母、数字或 -'))
      return
    }
    if (!this.newIns.channelManagement || !this.newIns.channelManagement.name) {
      this.$message.error(this.i18n('请填写渠道名称'))
      return
    }
    ChannelManagementApi.save(this.newIns).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(this.i18n('添加成功'))
        this.getList()
        this.clear()
      } else {
        this.$message.error(resp.msg)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private showInfoDialog(row: ChannelManagement) {
    this.infoDialog.row = row
    this.infoDialog.visible = true
  }

  private showUpdateDialog(row: ChannelManagement) {
    this.updateIns.channelManagement = JSON.parse(JSON.stringify(row))
    this.modifyDialog.visible = true
  }

  private update() {
    if (!this.updateIns.channelManagement || !this.updateIns.channelManagement.channel || !this.updateIns.channelManagement.channel.id) {
      this.$message.error(this.i18n('请填写渠道ID'))
      return
    }
    if (!this.updateIns.channelManagement || !this.updateIns.channelManagement.name) {
      this.$message.error(this.i18n('请填写渠道名称'))
      return
    }
    ChannelManagementApi.modify(this.updateIns).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(this.i18n('修改成功'))
        this.getList()
        this.modifyDialog.visible = false
      } else {
        this.$message.error(resp.msg)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private toggleState(enable: boolean) {
    if (!this.selected || this.selected.length === 0) {
      this.$message.warning(this.i18n(enable ? '请选择需要启用的记录': '请选择需要禁用的记录'))
      return
    }
    let req = new ChannelBatchRequest();
    req.list = JSON.parse(JSON.stringify(this.selected))
    let func = enable ? ChannelManagementApi.enable : ChannelManagementApi.disable
    func(req).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(enable ? this.i18n('批量启用成功') : this.i18n('批量禁用成功'))
        this.checkedAll = false
        this.getList()
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private clear() {
    this.newIns.channelManagement = new ChannelManagement()
    this.newIns!.channelManagement!.channel = new Channel()
  }

  private checkedAllRow() {
    if (this.checkedAll) {
      for (let row of this.queryData) {
        this.$refs.table.toggleRowSelection(row, true)
      }
    } else {
      this.$refs.table.clearSelection();
    }
  }

  private formatJson() {
    if (this.infoDialog.row && this.infoDialog.row.channel) {
      return JsonBeautifyUtil.beautifyJson(JSON.stringify({
        channel: {
          type: this.infoDialog.row.channel.type,
          id: this.infoDialog.row.channel.id,
        },
        namespace: this.infoDialog.row.channel.typeId
      }, null, '\t'))
    }
    return ''
  }

  private formatJsonExtend() {
    if (this.extendInfo.defaultOrgId) {
      return JsonBeautifyUtil.beautifyJson(JSON.stringify({
        defaultOrgId:this.extendInfo.defaultOrgId,
        defaultOuterOrgId:this.extendInfo.defaultOuterOrgId
      }, null, '\t'))
    }
    return ''
  }


  private copyAndClose() {
    navigator.clipboard.writeText(this.formatJson().replace(/<br\/>/g, '\n').replace(/&nbsp;/g, ' '))
    this.infoDialog.visible = false
  }

  private copyAndCloseExtend() {
    navigator.clipboard.writeText(this.formatJsonExtend().replace(/<br\/>/g, '\n').replace(/&nbsp;/g, ' '))
    this.extendInfo.visible = false
  }

  private changeDisable() {
    this.doSearch()
  }

  private editButtonIsDisabled(row: ChannelManagement) {
    return row!.channel!.type && ['weixin', 'weixinApp', 'alipay', 'store'].indexOf(row!.channel!.type) > -1 && row!.channel!.id === '-'
  }
}
