import WeimobExtLimitGoodsGroupRuleInfo from 'model/common/weimob/WeimobExtLimitGoodsGroupRuleInfo'
import IdName from 'model/common/IdName'

export default class BWeimobExtLimitGoodsGroupRule {
  // 设置商品分组明细
  ruleInfos: WeimobExtLimitGoodsGroupRuleInfo[] = []
  // 是否包含下级自建商品
  includeChildGoods: Nullable<boolean> = null
  // 是否限制是否不可用商品
  existExcludeGoods: Nullable<boolean> = false
  // 不可用商品
  excludeGoodsIds: IdName[] = []
}
