<template>
    <div class="points-charge-speed-setting-dtl">
        <BreadCrume :panelArray="panelArray">
            <template slot="operate">
                <el-button @click="doEdit" type="primary" v-if="hasOptionPermission('/营销/营销/节日有礼/会员日积分加速抵现', '规则维护')">{{formatI18n('/会员/会员资料/编辑')}}</el-button>
                <el-button @click="doEnabled" type="success" v-if="hasOptionPermission('/营销/营销/节日有礼/会员日积分加速抵现', '规则维护') && dtl.stopped">{{formatI18n('/设置/权限/用户管理/功基本信息','启用')}}</el-button>
                <el-button @click="doStoped" type="danger" v-if="hasOptionPermission('/营销/营销/节日有礼/会员日积分加速抵现', '规则维护') && !dtl.stopped">{{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/详情', '禁用') }}</el-button>
                <el-button @click="doBack" v-if="hasOptionPermission('/营销/营销/节日有礼/会员日积分加速抵现', '规则维护')">{{ formatI18n('/公用/按钮/返回') }}</el-button>
            </template>
        </BreadCrume>
        <div style="height: 95%;overflow: auto">
            <div style="margin: 30px">
                <div style="height: 46px;line-height: 46px;border: 1px solid #1cbd1c;background: #e7faf5;padding-left: 10px;margin-bottom: 20px">&#8226 {{getTip(score, amount)}}</div>
                <FormItem :label="formatI18n('/营销/积分活动/积分活动/积分抵现活动/立即新建', '抵现方式')">
                    <div style="line-height: 36px" v-if="dtl.dayType === 'BY_MONTH'">
                        {{getSettingByMonth(dtl.day, dtl.points, dtl.amount)}}
                    </div>
                    <div style="line-height: 36px" v-else>
                        {{getSettingByWeek(weeks[dtl.day - 1], dtl.points, dtl.amount)}}
                    </div>
                    <div style="color: #909399">
                        - {{formatI18n('/权益/积分/积分初始化/未初始化状态/积分抵现规则/会员日积分加速抵现/点击立即设置/如果每月3号，每使用10积分，抵现2元，当会员3号到店消费，有28个积分，那么他最多可使用20个积分抵现4元。')}}
                    </div>
                </FormItem>
            </div>
        </div>

    </div>
</template>

<script lang="ts" src="./PointsChargeSpeedSettingDtl.ts">
</script>

<style lang="scss">
.points-charge-speed-setting-dtl{
    background-color: white;
    overflow: hidden;
    height: 100%;
    width: 100%;
    .qf-form-item .qf-form-label{
        width: 170px !important;
    }
    .qf-form-item .qf-form-content{
        margin-left: 170px !important;
    }
}
</style>