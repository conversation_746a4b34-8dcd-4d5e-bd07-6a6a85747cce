import ApiClient from 'http/ApiClient'
import Response from 'model/response/Response'
import Use<PERSON><PERSON><PERSON><PERSON> from 'model/user_v1/UserResult'
import SaveNewUserResult from 'model/user_v1/SaveNewUserResult'
import SaveModifyUserResult from 'model/user_v1/SaveModifyUserResult'
import Switch<PERSON>esult from 'model/user_v1/SwitchResult'
const qs = require('qs');
export default class UserApi {

  static getUserList(body: any): Promise<Response<UserResult>> {

    return ApiClient.server().post(`login-server/sys/queryUser.hd`, body, {
    }).then((res) => {
      return res.data
    })
  }
  static saveNewUser(body: any): Promise<Response<SaveNewUserResult>> {

    return ApiClient.server().post(`login-server/sys/saveNewUser.hd`, body, {
    }).then((res) => {
      return res.data
    })
  }
  static saveModifyUser(body: any): Promise<Response<SaveModifyUserResult>> {

    return ApiClient.server().post(`login-server/sys/saveModifyUser.hd`, body, {
    }).then((res) => {
      return res.data
    })
  }

  static switch(body: any): Promise<Response<SwitchResult>> {

    return ApiClient.server().post(`login-server/sys/user/switch.hd`, body, {
    }).then((res) => {
      return res.data
    })
  }
  static getUserInfo(params: any): Promise<Response<SwitchResult>> {

    return ApiClient.server().post(`login-server/sys/getUser.hd`, {}, {
      params: params
    }).then((res) => {
      return res.data
    })
  }
}