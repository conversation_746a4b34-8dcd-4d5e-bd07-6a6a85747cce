import BWeimobExtGoodsUseRule from './BWeimobExtGoodsUseRule'
import CanUseDiscount from './CanUseDiscount'
import UseScene from './UseScene'
import WeimobOrgUseRule from './WeimobOrgUseRule'
import { FreeChannel } from './FreeChannel'
import { IssueChannel } from './IssueChannel'
import { StoreRangeType } from './StoreRangeType'

// 微盟平台券
export default class BWeimobCoupon {
  /**
   * 发放渠道
   * 可选值：PAY，FREE
   */
  issueChannel: Nullable<IssueChannel> = null
  /**
   * 免费渠道
   * issueChannel为FREE时必填
   */
  freeChannels: FreeChannel[] = []
  /**
   * 适用商品类型
   * 1-全部商品适用；2-部分商品适用；3-部分分组适用；4-部分类目适用
   */
  limitGoodsType: Nullable<number> = null
  // 微盟适用商品ids
  includeGoodsIds: string[] = []
  // 是否存在排除商品
  existExcludeGoods: Nullable<boolean> = null
  // 排除商品ids
  excludeGoodsIds: string[] = []
  // 每人限量
  perLimit: Nullable<number> = null
  // 每天限量
  dayLimit: Nullable<number> = null
  // 核销场景
  useScene: Nullable<UseScene> = null
  // 是否开启推荐，默认false，发放场景为店铺直接领取时生效
  enableRecommend: Nullable<boolean> = null
  // 推荐领取开始时间
  recommendStartTime: Nullable<Date> = null
  // 推荐领取结束时间
  recommendEndTime: Nullable<Date> = null
  // 商品适用规则
  goodsUseRule: Nullable<BWeimobExtGoodsUseRule> = null
  // 可共享的优惠
  canUseDiscount: Nullable<CanUseDiscount> = null
  // 可领券时间类型，类型包括：1-无限制；2-固定时间。
  sendTimeType: Nullable<number> = null
  // 可领券的开始时间
  sendStartDate: Nullable<Date> = null
  // 可领券的结束时间
  sendEndDate: Nullable<Date> = null
  /**
   * 适用门店类型
   * 全部门店可用
   */
  limitOrgType: Nullable<StoreRangeType> = null
  // 门店适用规则
  orgsUseRule: Nullable<WeimobOrgUseRule> = null
}