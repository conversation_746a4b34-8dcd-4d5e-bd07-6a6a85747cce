<template>
  <div class="member-simple-card"
       :class="{benefit:benefit}">
    <img class="bg-icon"
         :src="benefit?require('~assets/image/member/ic_crown_bg.png'):require('~assets/image/member/ic_appreciation_bg.png')">
    <el-tooltip ref="content" class="content"
                placement="top-start"
                :content="card.name"
                :disabled="!hasEllipsis">
      <div>{{ card.name }}</div>
    </el-tooltip>
    <div class="cover"></div>
  </div>
</template>
<script lang="ts"
        src="./MemberSimpleCard.ts">
</script>
<style lang="scss"
       scoped>
.member-simple-card {
  width: 128px;
  height: 72px;
  background: linear-gradient(135deg, #F5E0A6 0%, #D6A65E 100%);
  box-shadow: 0px 8px 16px -8px rgba(61, 33, 0, 0.24);
  border-radius: 8px;
  position: relative;
  font-weight: 600;
  font-size: 14px;
  color: #FFFFFF;
  line-height: 22px;
  padding: 8px 12px 20px 12px;
  overflow: hidden;

  &.benefit {
    background: linear-gradient(135deg, #47CEFF 0%, #006DEB 100%);
    box-shadow: 0px 8px 16px -8px rgba(0, 48, 102, 0.24);
  }

  .bg-icon {
    position: absolute;
    right: -16px;
    top: -12px;
    width: 64px;
    height: 64px;
  }

  .content {
    display: -webkit-box;
    -webkit-line-clamp: 2; /* 限制显示的行数 */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .cover {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 16px;
    background: rgba(255, 255, 255, 0.2);
  }
}
</style>
