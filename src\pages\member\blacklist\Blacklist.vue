<!--
 * @Author: 黎钰龙
 * @Date: 2024-04-10 15:04:43
 * @LastEditTime: 2024-04-15 15:26:30
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\member\blacklist\Blacklist.vue
 * 记得注释
-->
<template>
  <div class="blacklist-container">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button type="primary" v-if="hasOptionPermission('/会员/会员管理/黑名单','维护')" size="large" @click="doCreate">
          {{i18n('新建')}}
        </el-button>
        <el-button size="large" v-if="hasOptionPermission('/会员/会员管理/黑名单','维护')" @click="doImport">
          {{i18n('导入')}}
        </el-button>
        <el-button @click="doBatchExport" size="large">
          {{i18n('批量导出')}}
        </el-button>
      </template>
    </BreadCrume>
    <ListWrapper class="current-page">
      <template slot="query">
        <MyQueryCmp @reset="doReset" @search="doSearch" :showExpand="false">
          <el-row>
            <el-col :span="8">
              <FormItem :label="formatI18n('/会员/会员资料', '会员')">
                <el-input v-model="query.memberCodeEquals" :placeholder="formatI18n('/储值/会员储值/会员储值查询/列表页面','请输入会员号/手机号')">
                </el-input>
              </FormItem>
            </el-col>
          </el-row>
        </MyQueryCmp>
      </template>
      <template slot="toolbar">
        <i18n k="/营销/限量抢购活动/已选中{0}条">
          <span slot="0" class="select-num">
            {{selectedArr.length}}
          </span>
        </i18n>
        <el-button v-if="hasOptionPermission('/会员/会员管理/黑名单','维护')" @click="doBatchRemove">
          {{i18n('批量移除')}}
        </el-button>
      </template>
      <template slot="list">
        <el-table :data="tableData" row-key="uuid" ref="table" @selection-change="handleSelectionChange" style="width: 100%;margin-top: 10px">
          <el-table-column reserve-selection type="selection" width="55"></el-table-column>
          <el-table-column :label="i18n('会员号')" width="276">
            <template slot-scope="scope">
              {{scope.row.crmCode || '--'}}
            </template>
          </el-table-column>
          <el-table-column :label="i18n('手机号')" width="276">
            <template slot-scope="scope">
              {{scope.row.mobile || '--'}}
            </template>
          </el-table-column>
          <el-table-column :label="i18n('原因')" width="400">
            <template slot-scope="scope">
              {{scope.row.reason || '--'}}
            </template>
          </el-table-column>
          <el-table-column :label="i18n('操作')">
            <template slot-scope="scope">
              <span class="span-btn" v-if="hasOptionPermission('/会员/会员管理/黑名单','维护')" @click="doRemove(scope.row.uuid)">
                {{i18n('移出')}}
              </span>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <!--分页栏-->
      <template slot="page">
        <el-pagination :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
          @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background layout="total, prev, pager, next, sizes,  jumper">
        </el-pagination>
      </template>
    </ListWrapper>
    <CreateBlackMember ref="createBlackMember" @confirm="doReset"></CreateBlackMember>
    <UploadBlacklist ref="uploadBlacklist" @uploadSuccess="uploadSuccess"></UploadBlacklist>
    <DownloadCenterDialog :dialogvisiable="downloadShow" :showTip="true" @dialogClose="doDialogClose">
    </DownloadCenterDialog>
  </div>
</template>

<script lang="ts" src="./Blacklist.ts">
</script>

<style lang="scss" scoped>
.blacklist-container {
  width: 100%;
}
</style>