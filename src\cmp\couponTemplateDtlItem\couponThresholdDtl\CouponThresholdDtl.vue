<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-29 16:47:29
 * @LastEditTime: 2024-03-26 16:44:49
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\couponTemplateDtlItem\couponThresholdDtl\CouponThresholdDtl.vue
 * 记得注释
-->
  <template>
  <div>
    <template v-if="data && data.coupons && data.coupons.couponBasicType && ['random_cash', 'goods_cash', 'exchange_goods', 'all_cash', 'all_discount', 'goods_discount', 'rfm_type'].includes(data.coupons.couponBasicType)">
      <div v-if="data.coupons.useThresholdType === 'AMOUNT'">
        <div class="item-height" v-if="data.coupons.useThreshold.thresholdType !== 'NONE'">
          <span>{{ (data.coupons.couponBasicType === 'exchange_goods' ? i18n('订单满') : formatI18n("/公用/券模板/单品折扣券/用券门槛/用券商品满")) + data.coupons.useThreshold.threshold +
							formatI18n("/公用/券模板/单品折扣券/用券门槛/元及以上可用") }}
          </span>
        </div>
        <div class="item-height" v-else>
          <span>{{ formatI18n("/公用/券模板/无门槛") }}</span>
        </div>
      </div>
      <div v-else-if="data.coupons.useThresholdType === 'QTY'">
        <div class="item-height" v-if="data.coupons.useThreshold.thresholdType !== 'NONE'">
          <span>{{ formatI18n("/公用/券模板/单品折扣券/用券门槛/用券商品满") + data.coupons.useThreshold.threshold +
							formatI18n("/公用/券模板/单品折扣券/用券门槛/件及以上可用") }}</span>
        </div>
        <div class="item-height" v-else>
          <span>{{ formatI18n("/公用/券模板/无门槛") }}</span>
        </div>
      </div>
      <div v-else-if="data.coupons.useThresholdType === 'DISCOUNT_PER_AMOUNT'">
        <div class="item-height" v-if="data.coupons.useThreshold.thresholdType !== 'NONE'">
          <span>{{ i18n("用券商品每满") + ' ' + data.coupons.useThreshold.threshold + ' ' + formateThresholdStr +
							data.coupons.useThreshold.value + i18n("元") }}</span>
        </div>
        <div class="item-height" v-else>
          <span>{{ formatI18n("/公用/券模板/无门槛") }}</span>
        </div>
      </div>
      <div v-else>
        <div class="item-height">
          <span>{{ formatI18n("/公用/券模板/无门槛") }}</span>
        </div>
      </div>
    </template>
    <!-- 运费券需要单独判断 -->
    <template v-if="data && data.coupons && data.coupons.couponBasicType && ['freight'].includes(data.coupons.couponBasicType)">
      <div v-if="data.coupons.useThresholdType === 'AMOUNT'">
        <div style="height: 36px;line-height: 36px" v-if="data.coupons.useThreshold.thresholdType !== 'NONE'">
          <span>{{ formatI18n("/公用/券模板/订单待支付金额满") + data.coupons.useThreshold.threshold +
							formatI18n("/公用/券模板/单品折扣券/用券门槛/元及以上可用") }}</span>
        </div>
        <div style="height: 36px;line-height: 36px" v-else>
          <span>{{ formatI18n("/公用/券模板/无门槛") }}</span>
        </div>
      </div>
      <div v-else>
        <div style="height: 36px;line-height: 36px">
          <span>{{ formatI18n("/公用/券模板/无门槛") }}</span>
        </div>
      </div>
    </template>
  </div>
</template>

<script lang="ts" src="./CouponThresholdDtl.ts">
</script>

<style>
</style>