import ApiClient from 'http/ApiClient'
import GoodsGainAdditionalPointsActivity
  from 'model/points/activity/goodsgainadditionalpoints/GoodsGainAdditionalPointsActivity'
import Response from 'model/common/Response'

export default class GoodsGainAdditionalPointsActivityApi {
  /**
   * 详情
   * 详情。
   *
   */
  static detail(activityId: string): Promise<Response<GoodsGainAdditionalPointsActivity>> {
    return ApiClient.server().get(`/v1/points-activity/goods-gain-additional-points/detail/${activityId}`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 修改或保存
   * 修改或保存。
   *
   */
  static saveOrModify(body: GoodsGainAdditionalPointsActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/points-activity/goods-gain-additional-points/saveOrModify`, body, {}).then((res) => {
      return res.data
    })
  }

}
