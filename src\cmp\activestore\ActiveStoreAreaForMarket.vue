<template>
  <div class="area-active-store-for-market-view">
    <el-form :model="store" ref="form">
      <el-row style="background: #F0F2F6">
        <el-col :span="6" class="title">
          {{i18n('区域名称')}}
        </el-col>
        <el-col :span="16" class="title">
          {{i18n('门店范围')}}
        </el-col>
        <el-col :span="2" class="title">
          {{i18n('操作')}}
        </el-col>
      </el-row>
      <el-row v-for="(item, index) in store.zones" :key="index" class="zebra-row">
        <el-col :span="6">
          <div class="ellipse">{{item.zones.name}}</div>
        </el-col>
        <el-col :span="16">
          <el-radio-group @change="doStoreRange(index)" v-model="item.stores.storeRangeType">
            <el-radio label="ALL">{{ formatI18n("/公用/券模板", "全部门店") }}</el-radio>
            <el-radio label="PART">
              {{ formatI18n("/权益/积分/积分初始化/未初始化状态/得积分规则/基础得积分/点击立即设置", "指定门店适用") }}
              <template v-if="item.stores.storeRangeType === 'PART'">
                <span @click="doSelect(index)" v-if="!item.stores.stores.length" style="color:#20A0FF">
                  {{i18n('选择门店')}}
                </span>
                <span v-else>
                  <i18n k="/公用/券模板/已选择{0}家门店">
                    <template slot="0">
                      <span class="number-text">{{item.stores.stores.length}}</span>
                    </template>
                  </i18n>
                  <span style="color:#20A0FF;margin-left:2px" @click="doSelect(index)">{{i18n('/资料/门店/修改')}}</span>
                </span>
              </template>
            </el-radio>
            <el-radio label="EXCLUDE">
              {{formatI18n("/权益/积分/积分初始化/未初始化状态/得积分规则/基础得积分/点击立即设置", "指定门店不适用")}}
              <template v-if="item.stores.storeRangeType === 'EXCLUDE'">
                <span @click="doSelect(index)" v-if="!item.stores.stores.length" style="color:#20A0FF">
                  {{i18n('选择门店')}}
                </span>
                <span v-else>
                  <i18n k="/公用/券模板/已选择{0}家门店">
                    <template slot="0">
                      <span class="number-text">{{item.stores.stores.length}}</span>
                    </template>
                  </i18n>
                  <span style="color:#20A0FF;margin-left:2px" @click="doSelect(index)">{{i18n('/资料/门店/修改')}}</span>
                </span>
              </template>
            </el-radio>
          </el-radio-group>

          <!-- <div v-if="item.stores.storeRangeType === 'PART' || item.stores.storeRangeType === 'EXCLUDE'">
            <el-input style="width: 375px" v-model="item.storesValue" @focus="doSelect(index)"></el-input>
            <el-button style="margin-left: 20px" type="text" @click="doClearStore(index)">{{formatI18n('/公用/导入/清空')}}</el-button>
            <span style="margin-left: 20px" v-if="item.stores && item.stores.stores">{{ getStoreCount(item.stores.stores.length) }}</span>
          </div> -->
          <StoreSelectorDialog ref="selectGoodsScopeDialog" @summit="doSubmitGoods($event, index)" :zoneId="item.zones.id"
            :queryByMarketingCenter="headquarters==='true'?false: true"></StoreSelectorDialog>
          <ImportDialog :dialogShow.sync="importDialogShow" :importUrl="importUrl" :templatePath="templatePath"
            @dialogClose="importDialogShow = false" @upload-success="doUploadSuccess" :templateName="formatI18n('/公用/券模板', '门店模板')"
            :title="formatI18n('/公用/券模板', '导入')" :isSingle="true">
          </ImportDialog>
        </el-col>
        <el-col :span="2">
          <el-button type="text" style="color: #007EFF;margin-left:8px" @click="deleteZone(index)">
            {{i18n('移除')}}
          </el-button>
        </el-col>
      </el-row>
    </el-form>

    <ZoneSelectorDialog ref="zoneSelectorDialog" @summit="doSubmitZones" :marketCenterSearch="marketCenter"> </ZoneSelectorDialog>
    <PromotionCenterSelectorDialog ref="selectPromotionCenterSelectorDialog" @summit="doPromotionSubmitGoods"></PromotionCenterSelectorDialog>

    <ImportResultDialog :data="importResult" :dialogShow="importResultDialogShow" @importResultDialogClose="importResultDialogShow = false">
    </ImportResultDialog>
  </div>
</template>

<script lang="ts" src="./ActiveStoreAreaForMarket.ts"></script>

<style lang="scss" scoped>
.area-active-store-for-market-view {
  width: 100%;
  border: solid 1px #f2f2f2;
  .promotion-center-store {
    max-width: 800px;
  }

  .zebra-row {
    display: flex;
    align-items: center;
  }

  .title {
    padding-left: 10px;
    height: 32px;
    line-height: 32px;
    font-size: 13px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #242633;
  }

  .rows {
    padding: 10px;
    border: 1px solid #ced0da;
    border-top: none;
  }
  .ellipse {
    width: 100%;
    text-overflow: ellipsis;
    overflow: hidden;
    height: 32px;
    line-height: 32px;
    margin-left: 10px;
  }
}
</style>
