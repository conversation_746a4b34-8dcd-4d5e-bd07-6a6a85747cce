import {Component, Vue} from 'vue-property-decorator'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import BirthdayBenefitApi from 'http/benefit/BirthdayBenefitApi'
import GiftRule from 'model/benefit/GiftRule'
import FormItem from 'cmp/formitem/FormItem.vue'
import SelectStoreActiveDtlDialog from 'pages/deposit/mbrdeposit/active/dialog/SelectStoreActiveDtlDialog.vue'

@Component({
  name: 'BirthdayGiftDtl',
  components: {
    BreadCrume,
    FormItem,
    SelectStoreActiveDtlDialog
  }
})
export default class BirthdayGiftDtl extends Vue {
  $refs: any
  panelArray: any = []
  giftRule: GiftRule = new GiftRule()
  giftBagList: any = []
  dialogShow = false
  parent: any = {}
  child: any = {}

  created() {
    this.panelArray = [
      {
        name: this.formatI18n('/公用/菜单', '节日有礼'),
        url: 'score-init'
      },
			{
				name: this.formatI18n("/权益/生日权益初始化/生日权益初始化/title", "生日送礼"),
				url: "",
			},
		];
  }

  mounted() {
    this.getRule()
  }

  doModify() {
    this.$router.push({name: 'birthday-gift-add', query: {from: 'edit'}})
  }

  switchState() {
    BirthdayBenefitApi.switchRule(false)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/详情/启用禁用/修改成功'))
        }
        this.getRule()
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
    })
  }

  doCheckDtl(parent: any, child: any) {
    console.log('child',child);
    
    this.parent = parent
    this.child = child
    this.dialogShow = true
  }

  doDialogClose() {
    this.dialogShow = false
  }

  get giftDayI18n() {
    if (this.giftRule && this.giftRule.giftDay) {
      let day = (this.giftRule && this.giftRule.giftDay!.dayNo) ? this.giftRule.giftDay!.dayNo : '-'
      let time = (this.giftRule && this.giftRule.giftDay!.time) ? this.giftRule.giftDay!.time : '-'
      let str: any
      if (this.giftRule.giftDay!.type === 'MONTH_DAY') { //每月x天
        str = this.formatI18n('/权益/生日权益初始化/生日权益初始化/生日当月{0}号{1}')
      } else if (this.giftRule.giftDay!.type === 'BIRTHDAY') { //生日当天
        str = this.formatI18n('/权益/生日权益初始化/生日权益初始化/生日当天{1}')
      } else if (this.giftRule.giftDay!.type === 'BEFORE_DAY') { //生日前x天
        str = this.formatI18n('/权益/生日权益初始化/生日权益初始化/生日前{0}天的{1}')
      }
      str = str.replace(/\{0\}/g, day)
      str = str.replace(/\{1\}/g, time)
      return str
    }
  }


  giftPointCountI18n(value: any) {
    if (value) {
      let str = this.formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/详情/礼包设置', '赠送积分：{0}个')
      return str.replace(/\{0\}/g, value)
    }
    return ''
  }

  get couponItems() {
    if (this.giftRule.allSameGiftBag && this.giftRule.allSameGiftBag.couponItems) {
      return this.giftRule.allSameGiftBag.couponItems
    }
    return []
  }

  get pointsCount() {
    if (this.giftRule) {
      if (this.giftRule.allSameGiftBag) {
        return this.giftRule.allSameGiftBag.points
      }
    }
    return ''
  }

  private getRule() {
    BirthdayBenefitApi.giftRuleDetail()
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.giftRule = resp.data
          if (this.giftRule && this.giftRule.differentGiftBag) {
            this.extractBagList(this.giftRule.differentGiftBag)
          }
        }
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
    })
  }

  private extractBagList(giftBags: any) {
    // 以等级为顺序添加信息
    this.giftBagList = []
    let check = []
    let gradeList = this.giftRule.gradeList;
    if (gradeList) {
      for (let grade of gradeList) {
        let data: any = {}
        data.gradeName = grade.name
        data.gift = giftBags[grade.code as string]
        if (data.gift) {
          check.push(grade.code)
          this.giftBagList.push(data)
        }
      }
    }
    // 以为等级礼包为顺序过滤删除等级礼包
    for (let key in giftBags) {
      if (!check.includes(key)) {
        let data: any = {}
        data.gradeName = key
        data.gift = giftBags[key]
      }
    }
  }
}