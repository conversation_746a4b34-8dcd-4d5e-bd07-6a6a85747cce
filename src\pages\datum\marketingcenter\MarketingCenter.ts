import { Component, Vue } from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import ListWrapper from 'cmp/list/ListWrapper.vue'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import RSMarketingC<PERSON> from 'model/common/RSMarketingCenter'
import RSMarketingCenterFilter from 'model/common/RSMarketingCenterFilter'
import MarketingCenterApi from 'http/marketingcenter/MarketingCenterApi'
import FloatBlock from 'cmp/floatblock/FloatBlock.vue'
import RSSaveBatchMarketingCenterRequest from 'model/common/RSSaveBatchMarketingCenterRequest'
import UploadFileModal from 'pages/datum/store/UploadFileModal'
import Bread<PERSON>rume from 'cmp/bread-crumb/BreadCrume'
import IdName from 'model/common/IdName'
import RSOrg from 'model/common/RSOrg'
import RSOrgFilter from 'model/common/RSOrgFilter'
import Org<PERSON><PERSON> from 'http/org/OrgApi'
import SelectStore from 'pages/datum/marketingcenter/SelectStore'
import RSSaveBatchOrgRequest from 'model/common/RSSaveBatchOrgRequest'
import SysConfig<PERSON>pi from 'http/config/SysConfigApi'
import { State } from 'vuex-class'
import UserLoginResult from 'model/login/UserLoginResult'

@Component({
  name: 'marketing-center',
  components: {
    FormItem,
    ListWrapper,
    SubHeader,
    FloatBlock,
    UploadFileModal,
    BreadCrume,
    SelectStore
  }
})
export default class MarketingCenter extends Vue {
  @State('loginInfo')
  loginInfo: UserLoginResult
  query: RSMarketingCenterFilter = new RSMarketingCenterFilter()
  queryOrg: RSOrgFilter = new RSOrgFilter()
  queryOrgData: RSOrg[] = []
  queryData: RSMarketingCenter[] = []
  tableHeight: number = 0
  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  orgPage = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  newIns: RSMarketingCenter = new RSMarketingCenter()
  updateIns: RSMarketingCenter = new RSMarketingCenter()
  updateOrgIns: RSOrg = new RSOrg()
  modifyDialogVisible = false
  modifyLoading = false
  panelArray: any = []
  limitOfMarketingCenter: Nullable<number> = 0
  marketingCenters: IdName[] = []
  marketingCenter: IdName = new IdName()
  enableMultiMarketingCenter: boolean = true
  loading = false
  checkedAll: boolean = false
  $refs: any
  selected: RSOrg[] = []
  showNoMarketingCenter: boolean = false // 只展现没有所属营销中心的门店
  updateCenterDialogVisible: boolean = false
  updateOrgsForCenter: boolean = false // 是否是批量修改门店所属营销中心
  createDialogVisible: boolean = false // 是否显示新建营销中心弹出框
  selectingMarketingCenter: RSMarketingCenter = new RSMarketingCenter()
  marketingCentersForChooseOrg: string[] = []
  filterMarketingCenter: IdName[] = []
  marketingCentersForOrg: IdName[] = []

  created() {
    this.panelArray = [
      {
        name: this.formatI18n('/资料/营销中心/营销中心'),
        url: ''
      }
    ]
    this.getConfig()
    this.getList(true)
    this.getOrgList()
  }

  doSearch() {
    this.getList()
  }

  doReset() {
    this.query = new RSMarketingCenterFilter()
    this.page.currentPage = 1
    this.getList()
  }

  /**
   * 查询
   */
  onSearch() {
    this.page.currentPage = 1
    this.getList()
  }

  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.getList()
  }

  onHandleCurrentChangeForOrg(val: number) {
    this.orgPage.currentPage = val
    this.getOrgList()
  }

  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.getList()
  }

  onHandleSizeChangeForOrg(val: number) {
    this.orgPage.size = val
    this.getOrgList()
  }

  /**
   * 表格排序条件
   */
  onSortChange({ column, prop, order }: any) {
    // todo
  }

  modify() {
    this.modifyLoading = true
    this.marketingCenters = []
    let req = new RSSaveBatchMarketingCenterRequest()
    req.list = [this.updateIns]
    MarketingCenterApi.saveBatch(req)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.formatI18n('/资料/门店/修改成功'))
          this.getList()
          this.updateIns = new RSMarketingCenter()
          this.modifyDialogVisible = false
        } else {
          this.$message.error(resp.msg)
        }
      })
      .catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
      .finally(() => {
        this.modifyLoading = false
      })
  }

  modifyOrg() {
    this.modifyLoading = true
    let req = new RSSaveBatchOrgRequest()
    req.operator = this.loginInfo.user?.account
    this.updateOrgIns.marketingCenter = this.marketingCenter.id
    if (this.updateOrgsForCenter) {
      req.list = this.selected
    } else {
      req.list = [this.updateOrgIns]
    }
    OrgApi.saveBatch(req)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.formatI18n('/资料/门店/修改成功'))
          this.getOrgList()
          this.updateOrgIns = new RSOrg()
        } else {
          this.$message.error(resp.msg)
        }
      })
      .catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
      .finally(() => {
        this.modifyLoading = false
        this.updateCenterDialogVisible = false
        this.selected = []
        this.updateOrgsForCenter = false
      })
  }

  doSubmitOrgs(orgs: RSOrg[]) {
    this.modifyLoading = true
    let req = new RSSaveBatchOrgRequest()
    req.list = orgs
    req.operator = this.loginInfo.user!.account
    OrgApi.saveBatch(req)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.formatI18n('/资料/门店/修改成功'))
          this.getOrgList()
          // this.updateOrgIns = new RSOrg()
          // this.modifyOrgDialogVisible = false
        } else {
          this.$message.error(resp.msg)
        }
      })
      .catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
      .finally(() => {
        this.modifyLoading = false
        this.updateCenterDialogVisible = false
        this.selected = []
      })
  }

  handleSelectionChange(val: any) {
    this.selected = val
  }

  private clear(type: string) {
    if (type === 'createMarketingCenter') {
      this.newIns = new RSMarketingCenter()
    }
    if (type === 'modifyOrg') {
      this.marketingCenter.id = null
    }
  }

  private getList(create: boolean = false) {
    this.query.page = this.page.currentPage - 1
    this.query.pageSize = this.page.size
    this.query.sorters = { lastModifyInfotime: 'desc' }
    MarketingCenterApi.query(this.query)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.queryData = resp.data
          this.page.total = resp.total
          // tslint:disable-next-line:forin
          for (let value in this.queryData) {
            this.marketingCenters.push(
              this.queryData[value].marketingCenter as IdName
            )
          }
          if (create) {
            this.handleCurrentChange(this.queryData[0])
            this.$refs.leftTable.setCurrentRow(this.queryData[0])
          }
        }
      })
      .catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
  }

  private getOrgList() {
    this.queryOrg.page = this.orgPage.currentPage - 1
    this.queryOrg.pageSize = this.orgPage.size
    this.queryOrg.sorters = { lastModifyInfotime: 'desc' }
    if (this.showNoMarketingCenter) {
      this.queryOrg.queryNoMarketingCenter = true
    } else {
      this.queryOrg.queryNoMarketingCenter = false
    }
    OrgApi.query(this.queryOrg)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.queryOrgData = resp.data
          this.orgPage.total = resp.total
        }
      })
      .catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
  }

  private add(close: boolean) {
    if (
      this.limitOfMarketingCenter &&
      this.page.total >= this.limitOfMarketingCenter
    ) {
      this.$message.error(
        this.formatI18n('/资料/营销中心/许可证允许的营销中心数量已达上限')
      )
      return
    }
    if (
      !this.newIns.marketingCenter ||
      !this.newIns.marketingCenter.id ||
      !this.newIns.marketingCenter.name
    ) {
      this.$message.error(
        this.formatI18n('/资料/营销中心/请输入营销中心代码和名称')
      )
      return
    }
    let req = new RSSaveBatchMarketingCenterRequest()
    req.list = [this.newIns]
    let filter = new RSMarketingCenterFilter()
    filter.marketingCenterIdEquals = this.newIns.marketingCenter.id
    MarketingCenterApi.query(filter).then((res: any) => {
      if (res.data && res.data.length > 0) {
        this.$message.error(this.formatI18n('/资料/营销中心/营销中心已存在'))
        return
      } else {
        MarketingCenterApi.saveBatch(req)
          .then((resp: any) => {
            if (resp && resp.code === 2000) {
              this.$message.success(this.formatI18n('/资料/门店/添加成功'))
              if (close) {
                this.createDialogVisible = false
              }
              this.getList()
              this.newIns = new RSMarketingCenter()
            } else {
              this.$message.error(resp.msg)
            }
          })
          .catch((error) => {
            if (error && error.message) {
              this.$message.error(error.message)
            }
          })
      }
    })
  }

  private showModifyDialog(row: RSMarketingCenter) {
    this.updateIns = JSON.parse(JSON.stringify(row))
    this.modifyDialogVisible = true
  }

  private showCreateDialog() {
    if (
      this.limitOfMarketingCenter &&
      this.page.total >= this.limitOfMarketingCenter
    ) {
      this.$message.error(
        this.formatI18n('/资料/营销中心/许可证允许的营销中心数量已达上限')
      )
      return
    } else {
      this.createDialogVisible = true
    }
  }

  private changeDisable() {
    this.doSearch()
  }

  private showSelectOrgDialog() {
    // @ts-ignore
    if (!this.selectingMarketingCenter.marketingCenter.id) {
      this.$message.error(
        this.formatI18n('/资料/营销中心/请至少选中一个营销中心')
      )
      return
    }
    this.$refs.selectStores.open([], 'multiple')
  }

  private showUpdateCenterDialog() {
    if (this.selected.length === 0) {
      this.$message.error(this.formatI18n('/资料/渠道/请选择至少一个门店'))
      return
    }
    this.marketingCentersForOrg = []
    for (let index in this.marketingCenters) {
      // @ts-ignore
      if (
        this.selectingMarketingCenter.marketingCenter!.id !==
        this.marketingCenters[index].id
      ) {
        this.marketingCentersForOrg.push(this.marketingCenters[index])
      }
    }
    this.updateCenterDialogVisible = true
  }

  private checkedAllRow() {
    if (this.checkedAll) {
      for (let row of this.queryOrgData) {
        this.$refs.table.toggleRowSelection(row, true)
      }
    } else {
      this.$refs.table.clearSelection()
    }
  }

  private isEnableMultiMarketingCenter() {
    return this.enableMultiMarketingCenter
  }

  private cancelUpdateCenter() {
    this.marketingCenter = new IdName()
    this.updateCenterDialogVisible = false
  }

  private updateOrgsForMarketingCenter() {
    this.updateOrgsForCenter = true
    if (this.selected.length == 0) {
      this.$message.error(this.formatI18n('/资料/渠道/请选择至少一个门店'))
      this.updateCenterDialogVisible = false
    } else {
      // tslint:disable-next-line:forin
      for (let index in this.selected) {
        this.selected[index].marketingCenter = this.marketingCenter.id
      }
      this.modifyOrg()
    }
  }

  private getMarketingCentersForChooseOrg() {
    for (let index in this.marketingCenters) {
      // @ts-ignore
      if (
        this.selectingMarketingCenter.marketingCenter!.id !=
        this.marketingCenters[index].id
      ) {
        this.marketingCentersForChooseOrg.push(
          this.marketingCenters[index].id as string
        )
        this.filterMarketingCenter.push(this.marketingCenters[index])
      }
    }
  }

  private importOrg() {
    // @ts-ignore
    if (!this.selectingMarketingCenter.marketingCenter.id) {
      this.$message.error(
        this.formatI18n('/资料/营销中心/请至少选中一个营销中心')
      )
      return
    }
    this.$refs.uploadFileModal.show(this.marketingCenters, true)
  }

  private handleCurrentChange(val: RSMarketingCenter) {
    this.filterMarketingCenter = []
    this.marketingCentersForChooseOrg = []
    // @ts-ignore
    this.queryOrg.marketingCenterIdEquals = val.marketingCenter.id
    this.selectingMarketingCenter = val
    this.getMarketingCentersForChooseOrg()
    this.getOrgList()
  }

  private resetForm() {
    this.newIns = new RSMarketingCenter()
  }

  private getConfig() {
    SysConfigApi.get()
      .then((resp: any) => {
        if (resp && resp.data) {
          this.limitOfMarketingCenter = resp.data.marketingCenterQty
        }
      })
      .catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
  }
}
