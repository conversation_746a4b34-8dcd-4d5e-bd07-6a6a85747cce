/*
 * @Author: 黎钰龙
 * @Date: 2023-10-20 16:30:45
 * @LastEditTime: 2023-10-26 17:24:49
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\promotion\LimitSaleApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import BMemberGoodsQuatoActivity from 'model/limitActivity/BMemberGoodsQuatoActivity'
import BMemberGoodsQuatoActivityFilter from 'model/limitActivity/BMemberGoodsQuatoActivityFilter'
import BMemberGoodsQuatoActivityResult from 'model/limitActivity/BMemberGoodsQuatoActivityResult'

export default class LimitSaleApi {
  /**
     * 审核活动
     * 审核活动。
     * 
     */
  static audit(activityId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/member-goods-quato-activity/audit/${activityId}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量审核活动
   * 批量审核活动。
   * 
   */
  static batchAudit(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/member-goods-quato-activity/batch/audit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量删除活动
   * 批量删除活动。
   * 
   */
  static batchRemove(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/member-goods-quato-activity/batch/remove`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量终止活动
   * 批量终止活动。
   * 
   */
  static batchStop(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/member-goods-quato-activity/batch/stop`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询活动详情
   * 查询活动详情。
   * 
   */
  static info(activityId: string): Promise<Response<BMemberGoodsQuatoActivity>> {
    return ApiClient.server().get(`/v1/member-goods-quato-activity/info/${activityId}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改活动
   * 修改活动。
   * 
   */
  static modify(body: BMemberGoodsQuatoActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/member-goods-quato-activity/modify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询活动列表
   * 查询活动列表。
   * 
   */
  static query(body: BMemberGoodsQuatoActivityFilter): Promise<Response<BMemberGoodsQuatoActivityResult>> {
    return ApiClient.server().post(`/v1/member-goods-quato-activity/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除活动
   * 删除活动。
   * 
   */
  static remove(activityId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/member-goods-quato-activity/remove/${activityId}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存并审核活动
   * 保存并审核活动。
   * 
   */
  static saveAndAudit(body: BMemberGoodsQuatoActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/member-goods-quato-activity/saveAndAudit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建会员商品限量活动
   * 新建会员商品限量活动
   * 
   */
  static saveAndModify(body: BMemberGoodsQuatoActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/member-goods-quato-activity/saveAndModify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 终止活动
   * 终止活动。
   * 
   */
  static stop(activityId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/member-goods-quato-activity/stop/${activityId}`, {}, {
    }).then((res) => {
      return res.data
    })
  }
}