/*
 * @Author: 黎钰龙
 * @Date: 2023-10-25 17:22:23
 * @LastEditTime: 2023-10-26 17:07:51
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\common\BBaseActivity.ts
 * 记得注释
 */
import ActivityBody from "./ActivityBody"
import ChannelRange from "./ChannelRange"
import GradeRange from "./GradeRange"
import IdName from "./IdName"
import PickUpGoods from "./PickUpGoods"
import StoreRange from "./StoreRange"


// 基础活动
export default class BBaseActivity {
  // true表示只保存，false表示保存并审核
  justSave: Nullable<boolean> = null
  // 营销中心
  marketingCenter: Nullable<string> = null
  // 商品范围
  limitGoods: Nullable<PickUpGoods> = null
  // 会员等级范围
  gradeRange: Nullable<GradeRange> = null
  // 渠道范围
  channelRange: Nullable<ChannelRange> = null
  // 门店范围
  storeRange: Nullable<StoreRange> = null
  // 活动类型
  body: Nullable<ActivityBody> = new ActivityBody()
}