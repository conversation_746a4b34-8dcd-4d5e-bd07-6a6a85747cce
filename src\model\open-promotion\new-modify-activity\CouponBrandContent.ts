/*
 * @Author: 黎钰龙
 * @Date: 2022-11-29 11:27:07
 * @LastEditTime: 2023-02-17 11:25:47
 * @LastEditors: mazhengfa <EMAIL>
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\open-promotion\new-modify-activity\CouponBrandContent.ts
 * 记得注释
 */
export default class WeixinCouponBrandContent {
  activityName: Nullable<string> = null
  activityNumber: Nullable<string> = null
  brandImage?: Nullable<string> = null
}