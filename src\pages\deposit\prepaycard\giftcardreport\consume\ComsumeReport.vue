<template>
  <div class="gift-activity-comsume-report">
    <div class="current-page">
      <el-form label-width="120px">
        <el-row class="query">
          <TimeRange no-i18n @submit="handleTimeRange" :da1qiaoPermission="da1qiaoPermission" ref="timeRange"></TimeRange>
        </el-row>
        <el-row class="query" style="margin-top:8px">
          <el-col :span="8">
            <form-item label="卡号">
              <el-input placeholder="等于" v-model="query.codeEquals" />
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item label="发生组织">
              <SelectStores v-model="query.orgIdEquals" :isOnlyId="true" :hideAll="false" width="100%" :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
              </SelectStores>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水/发生区域')">
              <el-select placeholder="不限" v-model="query.zoneIdEquals">
                <el-option :label="formatI18n('/公用/查询条件/下拉列表/不限')" :value="null">{{ formatI18n('/公用/查询条件/下拉列表/不限') }}</el-option>
                <el-option :key="item.zone.id" :label="'['+item.zone.id+']'+item.zone.name" :value="item.zone.id"
                  v-for="item in areaData">[{{item.zone.id}}]{{item.zone.name}}</el-option>
              </el-select>
            </form-item>
          </el-col>
        </el-row>
        <el-row class="query" style="margin-top:8px" v-show="expandQuery">
          <el-col :span="8">
            <form-item label="余额减少">
              <el-input style="width: calc(50% - 7px)" v-model="query.balanceGreaterOrEquals" />-
              <el-input style="width: calc(50% - 7px)" v-model="query.balanceLessOrEquals" />
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item label="卡模板">
              <el-input placeholder="等于" v-model="query.templateEquals" />
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item label="交易号">
              <el-input placeholder="等于 " v-model="query.transNoEquals" />
            </form-item>
          </el-col>
        </el-row>
        <el-row class="query" style="margin-top:8px" v-if="expandQuery">
          <el-col :span="8">
            <form-item :label="formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水/发生渠道')">
              <el-select v-model="channelEquals" :placeholder="formatI18n('/资料/渠道/请选择')" style="width: 100%" clearable filterable>
                <el-option v-for="(item,index) in channelTypes" :key="index" :value="getKey(item.channel)" :label="item.name">
                  <!-- <span style="float: left">{{ item.label }}</span> -->
                </el-option>
              </el-select>
            </form-item>
          </el-col>
        </el-row>
        <el-row class="query" style="margin-top:8px">
          <el-col :span="16">
            <form-item label=" ">
              <el-button class="btn-search" type="primary" @click="doSearch">查询</el-button>
              <el-button class="btn-reset" @click="doReset">重置</el-button>
              <el-button type="text" @click="expandQuery=!expandQuery">
                <span v-if="!expandQuery">
                  <i class="el-icon-arrow-down"></i>
                  {{i18n('/公用/查询条件/展开')}}
                </span>
                <span v-if="expandQuery">
                  <i class="el-icon-arrow-up"></i>
                  {{i18n('/公用/查询条件/收起')}}
                </span>
              </el-button>
            </form-item>
          </el-col>
        </el-row>
      </el-form>
      <hr />
      <el-row style="line-height: 35px" v-if="isShowSum">
        <i class="el-icon-warning" />
        <i18n k="/储值/预付卡/电子礼品卡报表/消费流水/礼品卡支付{0}元，实充减少{1}元，返现减少{2}元">
          <template slot="0">
            &nbsp;
            <span style="color: red">{{dataUtil.showTotalAmount(sum.totalAmount)}}</span>&nbsp;
          </template>
          <template slot="1">
            &nbsp;
            <span style="color: red">{{dataUtil.showTotalAmount(sum.amount)}}</span>&nbsp;
          </template>
          <template slot="2">
            &nbsp;
            <span style="color: red">{{dataUtil.showTotalAmount(sum.giftAmount)}}</span>&nbsp;
          </template>
        </i18n>
      </el-row>
      <el-row class="table">
        <el-table :data="queryData" border @header-dragend="tableDragend" style="width: 100%;margin-top: 20px">
          <el-table-column label="卡号" prop="code" :width="getColumnWidth('code', 230)">
            <template slot="header">
              <span title="卡号">卡号</span>
            </template>
            <template slot-scope="scope">
              <span no-i18n :title="scope.row.code">{{scope.row.code}}</span>
            </template>
          </el-table-column>
          <el-table-column label="消费时间" prop="occurredTime" :width="getColumnWidth('occurredTime', 140)">
            <template slot="header">
              <span title="消费时间">消费时间</span>
            </template>
            <template slot-scope="scope">
              <span no-i18n
                :title="dataUtil.format(scope.row.occurredTime, 'yyyy-MM-dd HH:mm:ss')">{{dataUtil.format(scope.row.occurredTime, 'yyyy-MM-dd HH:mm:ss')}}</span>
            </template>
          </el-table-column>
          <el-table-column label="发生组织" prop="occurredOrg" :width="getColumnWidth('occurredOrg')">
            <template slot="header">
              <span title="发生组织">发生组织</span>
            </template>
            <template slot-scope="scope">
              <span no-i18n :title="dataUtil.showIdName(scope.row.occurredOrg)">{{dataUtil.showIdName(scope.row.occurredOrg)}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水/发生区域')" prop="zone" :width="getColumnWidth('zone',100)">
            <template slot-scope="scope">
              <div v-if="scope.row.zone!==null && scope.row.zone!==''">
                <el-tooltip class="item" effect="light" placement="right-end">
                  <div> {{'['+scope.row.zone.id+']'}}{{scope.row.zone.name}}</div>
                  <div slot="content">
                    {{'['+scope.row.zone.id+']'}}{{scope.row.zone.name}}
                  </div>
                </el-tooltip>
              </div>
              <div v-else>-</div>
            </template>
          </el-table-column>
          <el-table-column :label="formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水/发生渠道')" prop="channelName" :width="getColumnWidth('channelName')">
            <template slot-scope="scope">
              <span no-i18n>{{ scope.row.channelName | nullable }}</span>
            </template>
          </el-table-column>
          <el-table-column label="持卡人" prop="holder" :width="getColumnWidth('holder')">
            <template slot="header">
              <span title="持卡人">持卡人</span>
            </template>
            <template slot-scope="scope">
              <span no-i18n :title="dataUtil.showMemberId(scope.row)">{{dataUtil.showMemberId(scope.row)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="卡模板" prop="templateName" :width="getColumnWidth('templateName')">
            <template slot="header">
              <span title="卡模板">卡模板</span>
            </template>
            <template slot-scope="scope">
              <a href="javascript: void(0)" :title="scope.row.templateName" no-i18n
                @click="gotoTplDtl(scope.row.templateNumber)">{{scope.row.templateName}}</a>
            </template>
          </el-table-column>
          <el-table-column prop="cardTemplateNumber" :width="getColumnWidth('cardTemplateNumber',100)">
            <template slot="header">
              <span title="卡模板号">卡模板号</span>
            </template>
            <template slot-scope="scope">
              <span no-i18n :title="scope.row.templateNumber">{{scope.row.templateNumber}}</span>
            </template>
          </el-table-column>
          <el-table-column label="卡面额(元)" prop="faceAmount" align="right" :width="getColumnWidth('faceAmount',130)">
            <template slot="header">
              <span title="卡面额(元)">卡面额(元)</span>
            </template>
            <template slot-scope="scope">
              <span no-i18n :title="scope.row.faceAmount.toFixed(2)">{{scope.row.faceAmount.toFixed(2)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="余额变动(元)" prop="totalAmount" align="right" :width="getColumnWidth('totalAmount',130)">
            <template slot="header">
              <span title="余额变动(元)">余额变动(元)</span>
            </template>
            <template slot-scope="scope">
              <span no-i18n :title="scope.row.totalAmount.toFixed(2)">{{scope.row.totalAmount.toFixed(2)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="实充变动(元)" prop="amount" align="right" :width="getColumnWidth('amount',170)">
            <template slot="header">
              <span title="实充变动(元)">实充变动(元)</span>
            </template>
            <template slot-scope="scope">
              <span no-i18n :title="scope.row.amount.toFixed(2)">{{scope.row.amount.toFixed(2)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="返现变动(元)" prop="giftAmount" align="right" :width="getColumnWidth('giftAmount',180)">
            <template slot="header">
              <span title="返现变动(元)">返现变动(元)</span>
            </template>
            <template slot-scope="scope">
              <span no-i18n :title="scope.row.giftAmount.toFixed(2)">{{scope.row.giftAmount.toFixed(2)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="交易号" prop="transNo" :width="getColumnWidth('transNo')">
            <template slot="header">
              <span title="交易号">交易号</span>
            </template>
            <template slot-scope="scope">
              <span no-i18n :title="scope.row.transNo">{{scope.row.transNo}}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-pagination no-i18n :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
        @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background :layout="getPageLayout('total, prev, pager, next, sizes, jumper',page.probeEnabled)"
        class="pagin"></el-pagination>
    </div>
  </div>
</template>

<script lang="ts" src="./ComsumeReport.ts">
</script>

<style lang="scss">
.gift-activity-comsume-report {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: auto;
  .total {
    margin: 20px;
  }

  .current-page {
    height: calc(100% - 150px);
    padding: 0 20px 20px 20px;
    .el-select {
      width: 100%;
    }

    .query {
      .el-form-item {
        margin-bottom: 0;
      }
    }

    .pagin {
      margin-top: 25px;
    }

    .cell {
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }

    .el-form-item__label {
      //overflow: hidden;
      //text-overflow: ellipsis;
      white-space: normal;
    }
  }
}
</style>
