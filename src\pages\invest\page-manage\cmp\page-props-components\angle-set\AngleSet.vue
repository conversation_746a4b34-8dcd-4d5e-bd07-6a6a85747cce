<template>
  <div>
    <el-form inline :label-position="labelPosition" :model="value" :rules="rules" ref="form" class="global-set">
      <el-form-item :label="i18n('投放活动类型')" prop="propActivityType" style="width: 100%" class="activity-input">
        <el-select @change="handleChanges" v-model="value.propActivityType" :placeholder="i18n('请选择')">
          <el-option v-for="item in option" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="投放内容" prop="content">
        <el-radio v-for="item in options" :key="item.key" :label="item.key" v-model="value.propActivityRange" @change="handleChange">
          {{ item.caption }}
        </el-radio>
        <span class="text" v-show="value.propActivityRange === 'part'">
          {{ i18n('已选择')}}
          <span>{{ value.propActivityIds.length }}</span>
           {{ i18n('个活动')}}
        </span>
        <el-button style="margin-left: 110px" v-show="value.propActivityRange === 'part'" type="primary" @click="goUp">选择</el-button>
      </el-form-item>
    </el-form>
    <ElasticLayer
      ref="childRef"
      v-if="value.propActivityType"
      @submit="changechecked"
      :activityType="value.propActivityType"
    />
  </div>
</template>

<script lang="ts" src="./AngleSet.ts"></script>

<style lang="scss" scoped>
.global-set {
  .activity-input {
    margin-bottom: 10px;
    ::v-deep .el-form-item__content {
      width: 100% !important;
      .el-select {
        width: 100%;
      }
    }
  }
  .text {
    font-size: 12px;
    color: #a1a6ae;
    line-height: 16px;
  }
}
</style>
