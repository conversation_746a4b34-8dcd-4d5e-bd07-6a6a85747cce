<template>
  <div class="sale-card-dtl-container">
    <BreadCrume :panelArray="panelArray" :fontSize="18">
      <template slot="operate">
        <el-button @click="doExport" v-if="['FINISH'].indexOf(dtl.state) > -1 && hasOptionPermission('/卡/卡管理/售卡单', '单据导出')">
          {{ i18n('导出') }}
        </el-button>
        <el-button @click="doAudit" v-if="['INITIAL'].indexOf(dtl.state) > -1 && hasOptionPermission('/卡/卡管理/售卡单', '单据审核')">
          {{i18n('审核')}}
        </el-button>
        <el-button @click="doEdit" v-if="['INITIAL'].indexOf(dtl.state) > -1 && hasOptionPermission('/卡/卡管理/售卡单', '单据维护')">
          {{i18n('修改')}}
        </el-button>
        <el-button @click="doRemove" v-if="['INITIAL'].indexOf(dtl.state) > -1 && hasOptionPermission('/卡/卡管理/售卡单', '单据维护')">
          {{i18n('删除')}}
        </el-button>
      </template>
    </BreadCrume>
    <div style="overflow: auto">
      <div class="top-wrap">
        <div class="top-header">
          <div class="avatar">
            <el-image v-if="dtl.logoUrl" class="avatar-img" :src="dtl.logoUrl" fit="fill">
            </el-image>
            <div class="back" v-else>
              <img src="~assets/image/storevalue/img_default_no_picture.png">
            </div>
          </div>
          <div class="header-info">
            <div class="header-title">
              <div class="coupon-name" :title="dtl.cardTemplateName" v-if="dtl.cardTemplateName">
                {{ dtl.cardTemplateName }}
              </div>
              <template v-if="dtl && dtl.state">
                <div class="state-block" v-if="dtl.state === 'INITIAL'" style="background:#FFAA00">
                  {{ i18n('未审核') }}
                </div>
                <div class="state-block" v-if="dtl.state === 'SALEING'" style="background:#1597FF">
                  {{ i18n('售卡中') }}
                </div>
                <div class="state-block" v-if="dtl.state === 'FINISH'" style="background:#0CC66D">
                  {{ i18n('售卡完成') }}
                </div>
              </template>
            </div>
            <div class="coupon-number" v-if="dtl.billNumber">
              {{ i18n('售卡单号') }}：{{ dtl.billNumber }}
            </div>
          </div>
        </div>
        <el-row class="top-body">
          <el-col :span="5" class="body-section">
            <div class="body-container">
              <div class="body-title">{{ i18n('售卡数量') }}</div>
              <div class="body-info">{{ dtl.makeQty || '--' }}</div>
            </div>
          </el-col>
          <el-col :span="5" class="body-section">
            <div class="body-container">
              <div class="body-title">{{ i18n('总售价') }}</div>
              <div class="body-info">{{dtl.total | fmt}}{{i18n('元')}}</div>
            </div>
          </el-col>
          <el-col :span="10" class="body-section">
            <div class="body-container">
              <div class="body-title">{{ i18n('创建信息') }}</div>
              <div class="body-info">
                <span style="margin-right:12px">{{dtl.operator || '--'}}</span>
                <span>{{dtl.created | dateFormate3}}</span>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row style="margin-top: 16px;margin-left: 10px">
          <FormItem :label="i18n('备注') + '：'">
          <div style="line-height:36px">{{ dtl.remark || '--' }}</div>
        </FormItem>
        </el-row>
      </div>
      <div class="setting-container" style="margin-top: 16px">
        <FormItem :label="i18n('卡模板') + '：'" v-if="currentCardTemplate.number">
          <div style="line-height:36px">
            <span class="span-btn" @click="gotoTplDtl">{{currentCardTemplate.name}}</span>
            <FormItem :label="i18n('次数') + ':'" v-if="currentCardTemplate.cardTemplateType === 'COUNTING_CARD'">
              <div style="line-height:36px">{{currentCardTemplate.count}}</div>
            </FormItem>
            <FormItem :label="i18n('卡面额') + ':'" v-else>
              <div style="line-height:36px">{{currentCardTemplate.faceAmounts[0] | fmt}}{{i18n('元')}}</div>
            </FormItem>
            <FormItem :label="i18n('价格') + ':'">
              <div style="line-height:36px">{{currentCardTemplate.price | fmt}}{{i18n('元')}}</div>
            </FormItem>
            <FormItem :label="i18n('有效期') + ':'">
              <div style="line-height:36px">{{currentCardTemplate | cardTemplateValidityTime}}</div>
            </FormItem>
          </div>
        </FormItem>
        <FormItem :label="i18n('起始卡号') + '：'">
          <div style="line-height:36px">{{dtl.startCardCode || '--'}}</div>
        </FormItem>
        <FormItem :label="i18n('总售价') + '：'">
          <div style="line-height:36px">{{dtl.total | fmt}}{{i18n('元')}}</div>
        </FormItem>
        <FormItem :label="i18n('售价') + '：'">
          <div style="line-height:36px">{{dtl.price | fmt}}{{i18n('元')}}</div>
        </FormItem>
        <FormItem :label="i18n('售卡数量') + '：'">
          <div style="line-height:36px">{{dtl.makeQty || '--'}}{{i18n('张')}}</div>
        </FormItem>
        <FormItem :label="i18n('折扣') + '：'">
          <div style="line-height:36px">
            <template v-if="!dtl.discount">--</template>
            <template v-else>{{dtl.discount*100 | fmt}}%</template>
          </div>
        </FormItem>
        <div class="bottom-card-adjust-dtl">
            <div style="display: flex;">
              <span>{{i18n('支付方式')}}：</span>
              <el-table :data="dtl.payments" style="width: 200px;margin-left: 50px; margin-top: -10px;" stripe>
                <el-table-column prop="payMethod" :label="i18n('支付方式')" width="150">
                  <template slot-scope="scope">
                    {{scope.row.paymentName}}
                  </template>
                </el-table-column>
                <el-table-column prop="payAmount" :label="i18n('支付金额')+'('+ i18n('元') +')'" width="150">
                  <template slot-scope="scope">
                    {{scope.row.paymentAmount}}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <FormItem :label="i18n('支付总金额') + '：'" >
            <div style="line-height:36px">
            <template v-if="totalPayAmount === null || totalPayAmount === undefined || totalPayAmount === ''">0{{i18n('元')}}</template>
            <template v-else>{{ totalPayAmount }} {{i18n('元')}}</template>
          </div>
        </FormItem>
        <FormItem :label="i18n('发生门店') + '：'" v-if="dtl.occurredOrg">
          <div style="line-height:36px">
            <template v-if="!dtl.occurredOrg">{{i18n('不限')}}</template>
            <template v-else>{{`[${dtl.occurredOrg.id}]${dtl.occurredOrg.name}`}}</template>
          </div>
        </FormItem>
        <FormItem :label="i18n('购买客户') + '：'">
          <div style="line-height:36px">
            <template v-if="!dtl.buyer">--</template>
            <template v-else>{{dtl.buyer}}</template>
          </div>
        </FormItem>
        <FormItem :label="i18n('售卖员工') + '：'" >
          <div style="line-height:36px">
            <template v-if="!dtl.salesclerk">--</template>
            <template v-else>{{`[${dtl.salesclerk.id}]${dtl.salesclerk.name}`}}</template>
          </div>
        </FormItem>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./SaleCardDtl.ts">
</script>

<style lang="scss" scoped>
.sale-card-dtl-container {
  width: 100%;
  overflow: auto;

  ::v-deep .qf-form-item .qf-form-label {
    text-align: left !important;
    width: 130px !important;
  }

  .top-wrap {
    border-radius: 8px;
    background-color: white;
    padding: 24px;

    .top-header {
      display: flex;
      .avatar {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 56px;
        height: 56px;
        border-radius: 4px;
        overflow: hidden;
        margin-right: 16px;
        .avatar-img {
          width: 48px;
          height: 48px;
          border-radius: 4px;
        }
        .back {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 56px;
          height: 56px;
          background-color: rgba(242, 242, 242, 1);
          img {
            width: 56px;
            height: 56px;
          }
        }
      }
      .header-info {
        .header-title {
          display: flex;
          align-items: center;
          .coupon-name {
            max-width: 300px;
            height: 28px;
            font-size: 20px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #242633;
            line-height: 28px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
        .state-block {
          display: flex;
          justify-content: center;
          padding: 0 8px;
          height: 22px;
          border-radius: 4px;
          margin-left: 4px;
          font-size: 14px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #ffffff;
          line-height: 22px;
        }
        .coupon-number {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #79879e;
          line-height: 22px;
          margin-top: 8px;
        }
      }
    }

    .top-body {
      display: flex;
      width: 100%;
      height: 80px;
      background: #f7f9fc;
      border-radius: 4px;
      margin-top: 26px;

      .body-section {
        padding: 12px 0 16px 16px;
        &:nth-last-child(1) {
          .body-container {
            border: none !important;
          }
        }

        .body-container {
          border-right: 1px solid #d7dfeb;

          .body-title {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #79879e;
            line-height: 22px;
          }
          .body-info {
            font-size: 18px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #242633;
            line-height: 24px;
            margin-top: 6px;
          }
        }
      }
    }

    .top-footer {
      margin-top: 20px;
      .overtext {
        font-size: 13px;
        line-height: 22px;
        clear: both;
        position: relative;
        top: -30px;
        width: 530px;
      }
    }

    .right {
      margin-top: 13px;
      display: flex;
      flex: 1;
      flex-direction: column;
      position: relative;

      .top {
        padding: 15px 0;
        display: flex;
        border-bottom: 1px solid rgba(242, 242, 242, 1);

        .bill {
          color: rgba(51, 51, 51, 0.***************);
        }

        .name {
          font-weight: 500;
          margin-top: 8px;
          font-size: 20px;
        }

        .desc {
          color: rgba(51, 51, 51, 0.***************);
        }

        .state {
          font-weight: 500;
          margin-top: 8px;
          font-size: 20px;
        }
      }

      .bottom {
        
        padding-bottom: 20px;

        .account-info {
          margin-top: 10px;
        }

        .red {
          color: red;
        }

        .green {
          color: #008000;
        }
      }
    }
  }
  .bottom-card-adjust-dtl {
          margin-top: 10px;
          .el-table::before {
            height: 0;
          }
          .el-table__body .el-table__row td {
            border-bottom: 0 !important;
          }
        }
}
</style>