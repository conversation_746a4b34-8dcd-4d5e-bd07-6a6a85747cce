export default class MemberQuery {
  activateTimeEnd: Nullable<string>
  activateTimeStart: Nullable<string>
  birthdateEnd: Nullable<string>
  birthdateStart: Nullable<string>
  channel: Nullable<string>
  code: Nullable<string>
  createdEnd: Nullable<string>
  createdStart: Nullable<string>
  gender: Nullable<string>
  grade: Nullable<string>
  grades: string[] = []
  hdcardCardNum: Nullable<string>
  mbrChildrenBeginBirthday: Nullable<string>
  mbrChildrenEndBirthday: Nullable<string>
  mbrChildrenName: Nullable<string>
  mobile: Nullable<string>
  name: Nullable<string>
  page: number = 0
  pageSize: number = 0
  registerStoreId: Nullable<string>
  registerStoreIds: string[] = []
  registerTimeEnd: Nullable<string>
  registerTimeStart: Nullable<string>
  reportId: Nullable<string>
  source: Nullable<string>
  state: Nullable<string>
}
