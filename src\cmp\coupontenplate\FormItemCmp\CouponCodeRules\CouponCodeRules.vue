<!--
 * @Author: 黎钰龙
 * @Date: 2023-07-11 18:30:58
 * @LastEditTime: 2023-07-11 18:41:40
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\coupontenplate\FormItemCmp\CouponCodeRules\CouponCodeRules.vue
 * 记得注释
-->
<template>
  <el-form-item :label="formatI18n('/营销/券礼包活动/核销第三方券', '券码生成规则')" v-if="!wxScanForCoupon">
    <div v-if="copyFlag === 'edit'">
      <span v-if="ruleForm.prefix">{{ formatI18n("/营销/券礼包活动/核销第三方券/固定开头") }} {{ ruleForm.prefix }}</span>
      <span v-else>{{ formatI18n("/权益/券/券模板/券码前缀/系统随机生成") }}</span>
    </div>
    <div v-else>
      <div class="gray-tips">{{ i18n("输入的内容将成为生成券码前的固定开头，为空表示系统随机生成，券模板保存后此字段不可修改") }}</div>
      <el-form-item prop="prefix">
        <slot name="slot"></slot>
      </el-form-item>
    </div>
  </el-form-item>
</template>

<script lang="ts" src="./CouponCodeRules.ts">
</script>

<style>
</style>