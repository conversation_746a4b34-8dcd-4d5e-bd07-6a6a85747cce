import BigWheelAward from "./BigWheelAward"

// 大转盘参与记录
export default class BigWheelAwardManagement {
  // uuid
  uuid: Nullable<string> = null
  // 活动号
  activityNumber: Nullable<string> = null
  // 会员id
  memberId: Nullable<string> = null
  // 发生时间
  occurredTime: Nullable<Date> = null
  // 奖品类型
  awardType: Nullable<string> = null
  // 奖品id
  awardId: Nullable<string> = null
  // 奖品名称
  awardName: Nullable<string> = null
  // 奖品值
  awardValue: Nullable<BigWheelAward> = null
  // 收货人姓名
  consigneeName: Nullable<string> = null
  // 收货人手机号
  consigneePhone: Nullable<string> = null
  // 收货人地址
  consigneeAddress: Nullable<string> = null
  // 版本
  version: Nullable<string> = null
  // 会员标识
  memberCode: Nullable<string> = null
}