/*
 * @Author: l<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-11-25 11:57:55
 * @LastEditors: 黎钰龙
 * @LastEditTime: 2024-04-28 20:06:01
 * @FilePath: \phoenix-web-ui\src\cmp\selectordialogs\CouponAppletDialog.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import I18nPage from "common/I18nDecorator";
import { Component, Vue, Watch } from 'vue-property-decorator';
import AbstractSelectDialog from './AbstractSelectDialog';
import CouponActivityFilter from "model/v2/coupon/CouponActivityFilter";
import CouponAppletDialogFilter from "model/open-promotion/dialog/CouponAppletDialogFilter";
import CouponActivityApi from "http/v2/coupon/CouponActivityApi";
import UserGroupMark from "model/precisionmarketing/group/mark/UserGroupMark";
import message from "src/locale/lang/en/component";



@I18nPage({
  auto: false,
  prefix: [
    '/公用/公共组件/客群选择弹框组件',
    '/公用/活动/活动信息',
    '/储值/预付卡/电子礼品卡报表/消费流水'
  ],
})

@Component({
  name: 'CouponAppletDialog'
})
export default class CouponAppletDialog extends AbstractSelectDialog<CouponActivityFilter>{
  i18n: I18nFunc
  couponActivityFilter: CouponAppletDialogFilter = new CouponAppletDialogFilter()
  marks: any = []
  activityType: any = "WEIXIN_ISSUE_COUPON"
  typeEquals: any = "WEI_XIN_APPLET_ISSUE_COUPON"
  tableData: any
  maxSelectCount: number = 10 //最多选择十条活动
  created() {
    this.initFilter() //初始化搜索框的状态
    this.getMarks() //从后端获取数据
  }

  @Watch('checkboxList', { deep: true })  //保存之前选择的行，并默认勾选
  handle() {
    if (this.dialogShow === true) {
      this.$nextTick(() => {
        for (let i = 0; i < this.checkboxList.length; i++) {
          this.$refs.table.toggleRowSelection(this.currentList[i], this.checkboxList[i])
        }
      })
    }
  }

  reset() { //重置搜索框
    this.couponActivityFilter = new CouponAppletDialogFilter()
    this.initFilter()
  }

  getId(ins: any): string {
    if (ins.activityNumber) {
      return ins.activityNumber as string;
    } else {
      return ins.activityId as string;
    }

  }

  getName(ins: any) {
    return ins.activityName as string;
  }

  queryFun() {
    let filter = JSON.parse(JSON.stringify(this.couponActivityFilter))
    filter.page = this.page.currentPage - 1
    filter.pageSize = this.page.size
    filter.groupType = this.activityType
    filter.typeEquals = this.typeEquals
    return CouponActivityApi.query(filter)
  }

  getResponseData(response: any): any { //要看父构造函数内部是怎么调用的，把queryfun的resp赋值给this.currentList
    return response.data.list
  }

  getRowKey(row: any) {
    // console.log(this.selected);
    return row.activityId
  }

  private getMarks() {
    let filter = new CouponActivityFilter()
    filter.groupType = this.activityType
    filter.typeEquals = this.typeEquals
    filter.page = this.page.currentPage - 1
    filter.pageSize = this.page.size
    filter.stateEquals = "PROCESSING"
    return CouponActivityApi.query(filter).then((res) => {
      if (res.data) {
        this.marks = res.data
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private initFilter() {
    this.couponActivityFilter.stateEquals = "PROCESSING"
  }

  handleSelectionChange(e: any) {
    this.selected = e
    if (this.selected.length >= this.maxSelectCount) { //已经存了十条信息
      this.selected.splice(10, this.selected.length)
      return this.$message.warning('最多选择十条信息')
    }
  }

  selectChange(row: any, index: any) {
    if (this.selected.length >= this.maxSelectCount //如果当前已选择的行数为10，且当前行没有被选中，就禁用当前行
      && !this.selected.some((item: any, index: any) => {
        return item.activityId === row.activityId
      })) {
      return false
    } else {
      return true
    }
  }

  cellClass(row: any) {
    if (row.columnIndex === 0) {
      return 'disabledCheck'
    }
  }
}
