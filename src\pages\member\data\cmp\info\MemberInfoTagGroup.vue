<template>
  <div class="member-card">
    <member-card-title :icon="require('~assets/image/member/<EMAIL>')"
                       :title="i18n('拥有标签')">
      <template slot="right"  v-if="hasOptionPermission('/会员/标签客群/标签', '标签查看')">
      <el-button @click="showMoreTag(1)"
                 v-if="hasMoreTags">{{ i18n("查看更多") }}
      </el-button>
      <el-button type="primary"
                 style="margin-left: 8px"
                 @click="onEditTag">{{ i18n("打标签") }}
      </el-button>
      </template>
    </member-card-title>
    <div ref="tags"
         class="member-tag-container inline"
         v-if="tags.length > 0">
      <member-tag v-for="(tag,index) in tags"
                  :key="index"
                  :text="tag.value"
                  @close="onRemoveTag(tag)"></member-tag>
    </div>
    <empty-data v-else></empty-data>
    <div class="divider"></div>
    <member-card-title :icon="require('~assets/image/member/<EMAIL>')"
                       :title="i18n('所属客群')">
      <el-button slot="right"
                 @click="showMoreTag(2)"
                 v-if="hasMoreGroups">{{ i18n("查看更多") }}
      </el-button>
    </member-card-title>
    <div ref="groups"
         class="member-tag-container inline"
         v-if="groups.length > 0">
      <member-tag v-for="tag in groups"
                  :key="tag.uuid"
                  :text="tag.value"
                  @close="onRemoveGroup(tag.data)"></member-tag>
    </div>
    <empty-data v-else></empty-data>

    <el-dialog :title="tagDialogTitle"
               :visible.sync="tagDialogVisible"
               class="cosparty-dialog-center"
               width="550px">
      <div class="member-tag-container">
        <member-tag v-for="(tag,index) in dialogTags"
                    :key="index"
                    :text="tag.value"
                    @close="onRemoveDialogTag(tag)"></member-tag>
      </div>
    </el-dialog>
    <member-info-tag-editor v-model="editDialogVisible"
                            :select-tags="originTags"
                            @saved="getTags" :member-id="dtl.memberId"></member-info-tag-editor>
  </div>
</template>
<script lang="ts"
        src="./MemberInfoTagGroup.ts">
</script>
<style lang="scss"
       scoped>
.member-tag-container {
  &.inline {
    max-height: 110px;
    overflow: hidden;
  }

  ::v-deep .member-tag {
    margin-bottom: 4px;
  }

  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}
</style>
