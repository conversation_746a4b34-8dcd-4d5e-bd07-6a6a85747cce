import {Component, Prop, Vue, Watch} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import Brand<PERSON><PERSON> from 'http/brand/BrandApi'
import RSBrandFilter from 'model/common/RSBrandFilter'
import GoodsApi from 'http/goods/GoodsApi'
import RSGoodsFilter from 'model/common/RSGoodsFilter'
import CategoryApi from 'http/category/CategoryApi'
import RSCategoryFilter from 'model/common/RSCategoryFilter'
import BenefitCardTemplateApi from 'http/equityCard/BenefitCardTemplateApi'
import BenefitCardTemplateFilter from 'model/equityCard/default/BenefitCardTemplateFilter'
import { enefitCardTemplateType } from 'model/equityCard/default/enefitCardTemplateType'
import GiftInfoBenefitCardPaidRule from 'model/equityCard/default/GiftInfoBenefitCardPaidRule'
import GiftInfoBenefitCard from 'model/equityCard/default/GiftInfoBenefitCard'
import CommonUtil from 'util/CommonUtil'
import { enefitCardTemplateStatus } from 'model/equityCard/default/enefitCardTemplateStatus'

@Component({
    name: 'SelectBenefitCardDialog',
    components: {
        FormItem
    }
})
export default class SelectBenefitCardDialog extends Vue {
    key = ''
    idNameLikes = ''

    $refs: any
    // 分页
    page = {
        currentPage: 1,
        total: 0,
        size: 10
    }


    data: GiftInfoBenefitCard[] = []

    dialogShow: boolean  = false

    loading: boolean = false

    radio: string = ''

    activityIndex: number=0

    paidBenefitCards: Array<Nullable<GiftInfoBenefitCard>[]> = []

    doSearch() {
        this.page.currentPage = 1
        this.getBenefitCardList()
    }
    doReset() {
        this.page.currentPage = 1
        this.key = ''
        this.getBenefitCardList()
    }
    doBeforeClose(done: any) {
        this.dialogShow = false
        done()
    }
    doModalClose() {
        // this.$emit('summit', this.selected)
        if(this.radio){
            const find= this.data.find((item: any) =>{
                return item.uuid === this.radio
            })

            if(find){
                this.$set(this.paidBenefitCards[this.activityIndex], 0 , find)
            }
        }
        this.dialogShow = false
    }
    /**
     * 分页页码改变的回调
     * @param val
     */
    onHandleCurrentChange(val: number) {
        this.page.currentPage = val
        this.getBenefitCardList()
    }
    /**
     * 每页多少条的回调
     * @param val
     */
    onHandleSizeChange(val: number) {
        this.page.size = val
        this.getBenefitCardList()
    }


    private setSeleted() {
        let that = this
        setTimeout(() => {
            if (that.data && that.data.length > 0) {
                that.data.forEach((item: any) => {
                    that.data.forEach((subItem: any) => {
                        // if (this.type === this.formatI18n('/公用/券模板', '品牌')) {
                        //     if (item.brand.id === subItem.id) {
                        //         if (that.$refs.curTable) {
                        //             that.$refs.curTable.toggleRowSelection(item, true)
                        //         }
                        //     }
                        // }
                    })
                })
            }

        }, 100)

    }

    open(index: number, paidBenefitCards: any){
        this.activityIndex = index
        this.paidBenefitCards = paidBenefitCards
        this.dialogShow = true
        this.getBenefitCardList()
    }
    // 查询付费会员卡列表
    getBenefitCardList(){
        let query: BenefitCardTemplateFilter = new BenefitCardTemplateFilter()
        query.key = this.key ? this.key : null
        // query.page = this.page.currentPage - 1
        // query.pageSize = this.page.size
        query.page = 0
        query.pageSize = 0
        query.marketingCenterEquals = sessionStorage.getItem('marketCenter')
        query.typeEquals = enefitCardTemplateType.paid
        query.statusEquals = enefitCardTemplateStatus.start
        this.loading = true
        BenefitCardTemplateApi.queryGiftInfoBenefitCard(query).then((resp) => {
            if (resp && resp.code === 2000 && resp.data) {
                this.data = resp.data
                .map((item: any)=>{
                    // 生成一个uuid
                    item.uuid= CommonUtil.uuid();
                    return item
                } )
                this.page.total = resp.total

                const item = this.paidBenefitCards[this.activityIndex]
                if(item && item.length > 0) {
                    const first = item[0]
                    const find= this.data.find((dataItem)=>{
                        return first?.type == dataItem.type && 
                        first?.benefitCardTemplate?.id == dataItem.benefitCardTemplate?.id && 
                        dataItem.payRule?.payType == first?.payRule?.payType && 
                        dataItem.payRule?.pay == first?.payRule?.pay &&
                        dataItem.payRule?.type == first?.payRule?.type &&
                        dataItem.payRule?.value == first?.payRule?.value
                    }) as any
                    if(find){
                        this.radio = find.uuid
                    }
                }
            }
        }).catch((error) => {
            if (error && error.message) {
                this.$message.error(error.message)
            }
        }).finally(()=>{
            this.loading = false
        })
    }

    changeRadio(row: GiftInfoBenefitCard) {
        console.log('row', row)

    }
}