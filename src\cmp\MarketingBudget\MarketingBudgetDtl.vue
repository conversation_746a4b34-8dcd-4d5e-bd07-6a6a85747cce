<!--
 * @Author: 黎钰龙
 * @Date: 2024-05-15 10:18:56
 * @LastEditTime: 2024-05-15 15:39:39
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\MarketingBudget\MarketingBudgetDtl.vue
 * 记得注释
-->
<template>
  <div class="setting-container" v-if="budget && isPermit">
    <div class="setting-block">
      <div class="section-title">{{i18n('营销预算')}}</div>
      <FormItem :label="i18n('是否总部费用')">
        <div style="height: 36px; line-height: 36px">
          {{budget.headquartersCost === true ? i18n('是') : i18n('否')}}
        </div>
      </FormItem>
      <FormItem :label="i18n('门店属性')">
        <div style="height: 36px; line-height: 36px">
          <span v-if="budget.orgProperties === 'DIRECT_SALES'">{{i18n('直营')}}</span>
          <span v-else-if="budget.orgProperties === 'FRANCHISE'">{{i18n('特许')}}</span>
          <span v-else>--</span>
        </div>
      </FormItem>
      <FormItem :label="i18n('预计销售额')">
        <div style="height: 36px; line-height: 36px">
          {{budget.estimatedSales}} {{i18n('元')}}
        </div>
      </FormItem>
      <FormItem :label="i18n('预计折扣费用')">
        <div style="height: 36px; line-height: 36px">
          {{budget.estimatedDiscountCost}} {{i18n('元')}}
        </div>
      </FormItem>
      <FormItem :label="i18n('预计折扣率')">
        <div style="height: 36px; line-height: 36px">
          {{budget.estimatedDiscountRate}} %
        </div>
      </FormItem>
      <FormItem :label="i18n('预估费用')">
        <div style="height: 36px; line-height: 36px">
          <template v-if="budget.estimatedCost">
            {{budget.estimatedCost}} {{i18n('元')}}
          </template>
          <template v-else>--</template>
        </div>
      </FormItem>
    </div>
  </div>
</template>

<script lang="ts" src="./MarketingBudgetDtl.ts">
</script>

<style lang="scss" scoped>
</style>