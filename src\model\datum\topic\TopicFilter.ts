export default class TopicFilter {
  // id或name类似于
  idOrNameLikes: Nullable<string> = null
  // id等于
  topicIdEquals: Nullable<string> = null
  // id in
  topicIdIn: Nullable<string[]> = null
  // 名称等于
  topicNameEquals: Nullable<string> = null
  // 名称类似于
  topicNameLikes: Nullable<string> = null
  // 营销中心等于
  marketingCenterEquals: Nullable<string> = null
  // 排序，key表示排序的字段，可选值：zoneId、lastModifyInfoTime；value 表示排序方向，可选值为：asc, desc
  sorters: any
  // 页数>=0
  page: Nullable<number> = null
  // 页面大小>=0
  pageSize: Nullable<number> = null
}