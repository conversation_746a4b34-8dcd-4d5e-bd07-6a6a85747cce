<template>
  <div class="activity_place_name">
    <el-form inline :label-position="labelPosition" :model="value" :rules="rules" ref="form">
      <el-form-item :label="label" prop="name" style="width: 100%" class="activity-input">
        <el-input maxlength="40" show-word-limit v-model.trim="value.name" @change="handleChange" style="width: 100%"></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" src="./ActivityPlaceName.ts"></script>

<style lang="scss" scoped>
.activity_place_name {
  .activity-input {
    margin-bottom: 10px;
    ::v-deep .el-form-item__content {
      width: 100% !important;
    }
  }
}
</style>
