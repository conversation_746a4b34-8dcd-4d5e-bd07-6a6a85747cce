<template>
    <span>
        <span v-if="constructData.length === 0">{{formatI18n('/公用/券模板', '全部商品可用')}}</span>
        <span class="active-coupon-goods-dtl" v-else>
            <div class="cur_item item_back">{{title}}</div>
            <div class="cur_item item_border" v-for="(item, index) in constructData">
                <span class="item_color"  style="width: 70px;display: inline-block;    position: relative;
        top: -12px;"><span v-show="index > 0">{{formatI18n('/公用/公共组件/商品选择弹框组件/表格', '并且')}}</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <span style="display: inline-block;position: relative;
        top: -12px;">{{item.name}}</span>
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <span style="display: inline-block;position: relative;top: -12px;">{{item.belong}}</span>
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <div :title="item.range" style="display: inline-block;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 475px">{{item.range}}</div>
            </div>
        </span>
    </span>

</template>

<script lang="ts" src="./ActiveCouponGoodsDtl.ts">
</script>

<style lang="scss">
.active-coupon-goods-dtl{
    .cur_item{
        width: 771px;
        height: 36px;
        line-height: 36px;
        padding-left: 10px;
    }
    .item_back{
        background-color: rgb(230, 230, 230);
    }
    .item_color{
        color: rgba(51, 51, 51, 0.247058823529412);
    }
    .item_border{
        border-left: 1px solid rgb(230, 230, 230);
        border-right: 1px solid rgb(230, 230, 230);
        border-bottom: 1px solid rgb(230, 230, 230);
        padding-left: 60px;
    }
}
</style>