import {Component, Prop, Vue, Watch} from 'vue-property-decorator'
// import PointsExportFilter from "model/report/query/points/account/PointsExportFilter";
import GiftCardFilter from 'model/prepay/report/card/GiftCardFilter'
import DateUtil from 'util/DateUtil'

@Component({
  name: 'PointsReportExportConfirm',
  components: {}
})
export default class PointsReportExportConfirm extends Vue {
  @Prop({
    type: Boolean,
    default: false
  })
  dialogShow: boolean
  $refs: any
  rules: any = {}
  minDate: Date|null = null
  ruleForm = {
    type: '',
    dateRange: [],
    month:''
  }
  dateType: string = 'month'

  @Watch('dialogShow')
  resetParams() {
    if (!this.dialogShow) {
      this.ruleForm.type = null as any
      this.ruleForm.dateRange = []
      this.ruleForm.month = ''
      this.dateType = 'month'
      this.minDate = null
      this.$refs.ruleForm.clearValidate()
    }
  }
  @Watch("ruleForm", { immediate: true, deep: true })
    onClear(newVal: any, oldVal: any) {
        console.log(newVal);
        if (newVal.dateRange === null) {
            this.ruleForm.dateRange = [];
        }
        if (newVal.month === null) {
            this.ruleForm.month = "";
        }
    }

  get dateRangeOption() {
    let selectDate: string|number
    return {
        onPick({maxDate, minDate}: any) {
            selectDate = minDate.getTime();
            if (maxDate) {
              selectDate = ''
            }
        },
        disabledDate(time: any) {
            if (selectDate !== '') {
                const one = 31 * 24 * 3600 * 1000;
                const minTime = selectDate as number - one;
                const maxTime = selectDate as number + one;
                return time.getTime() < minTime || time.getTime() > maxTime
            }
        }
    }
  }

  created() {
    const dateRangeValidate = (rule: any, value: any, callback: any)=> {
      if (value.length == 0 && this.dateType == "day") {
          return callback(new Error(this.formatI18n('/储值/会员储值/储值充值活动/编辑页面/请选择日期')))
      } else {
          return callback()
      }
    }
    const monthValidate = (rule: any, value: any, callback: any)=> {
        if (value.length == 0 && this.dateType == "month") {
            return callback(new Error(this.formatI18n('/营销/券报表/批量导出/券报表/导出月份/请选择月份')))
        } else {
            return callback()
        }
    }
    this.rules = {
        type: [
            {
                required: true,
                message: this.formatI18n('/营销/券报表/批量导出/券报表/校验/请选择要导出的报表'),
                trigger: 'blur'
            }
        ],
        dateRange: [
            {
                trigger: 'blur',
                validator: dateRangeValidate
            }
        ],
        month:[
            {
                trigger: 'blur',
                validator: monthValidate
            }
        ]
    }
  }

  doModalClose() {
    this.$emit('dialogClose')
  }

  doTypeChange() {
    // this.ruleForm.dateRange = []
    this.$refs.ruleForm.clearValidate()
  }

  doBeforeClose(done: any) {
    this.$emit('dialogClose')
    done()
  }

  doModalConfirm() {
    Promise.all([
      this.$refs.ruleForm.validate()
    ]).then((res: any[]) => {
      if (res.filter((e) => !e).length === 0) {
        this.$emit('doSubmit', this.ruleForm.type, this.getParams())
        this.$emit('dialogClose')
      }
    })
  }

  getParams() {
    let filter = new GiftCardFilter();
    if (this.dateType === "month") {
      let date = new Date(this.ruleForm.month.valueOf());
      let beginDate = this.ruleForm.month
      let end = date.setMonth(date.getMonth() + 1)
      filter.occurredTimeAfterOrEqual = DateUtil.format(beginDate)
      filter.occurredTimeBefore = DateUtil.format(end)
    } else {
      filter.occurredTimeAfterOrEqual = this.ruleForm.dateRange[0] + ' 00:00:00' as any
      let endDate = new Date(this.ruleForm.dateRange[1] + ' 00:00:00')
      let end = endDate.setDate(endDate.getDate() + 1)
      filter.occurredTimeBefore = DateUtil.format(end)
    }
    return filter;
  }

  clearValidate() {
    this.ruleForm.dateRange = [];
    this.ruleForm.month = ""
    this.$refs.ruleForm.clearValidate()
  }
}