import Grade from "model/grade/Grade";

export default class PointsRule {
  // 是否已经启用
  stopped: Nullable<boolean> = null
  // 活动号
  number: Nullable<string> = null
  // 权益生效时间：BAY_DAY-当天；BAY_WEEK-当周；BY_MONTH-当月
  effectType: Nullable<string> = null
  // 不同等级会员享相同权益
  samePointsTimes: Nullable<number> = null
  // 每次生日获取积分最大次数null 表示不限制
  memberMaxGainPointsTimes: Nullable<number> = null
  // 不同等级会员享不同权益
  differentPointsTimes: any
  // 等级名称
  gradeList: Grade[] = []
}