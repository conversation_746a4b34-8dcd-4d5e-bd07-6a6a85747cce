import { Component, Vue } from "vue-property-decorator";
import B<PERSON><PERSON>rume from "cmp/bread-crumb/BreadCrume.vue";
import SystemConfigApi from "http/systemConfig/SystemConfigApi";
import ShopEntryRuleConfig from "model/systemConfig/ShopEntryRuleConfig";
import FormItem from "cmp/formitem/FormItem";
@Component({
  name: "ShopEntryRule",
  components: { BreadCrume, FormItem,},
})
export default class Set extends Vue {
  $refs: any;
  panelArray: any = [];
  modelType: string = "view";
  config: ShopEntryRuleConfig = this.defaultConfig();
  rules: any = {}
  created() {
    this.panelArray = [
      {
        name: this.formatI18n("/设置/小程序装修", "进店规则"),
        url: "",
      },
    ];
    this.getShopEntryRule();
  }
  defaultConfig(): ShopEntryRuleConfig {
    return {
      lbsEnabled: true,
      multipleStores: true
    };
  }

  doSave() {
    SystemConfigApi.saveShopEntryRule(this.config)
      .then((res) => {
        this.$message.success("保存成功");
        this.modelType = "view";
        this.getShopEntryRule();
      })
      .catch((rej) => {
        this.$message.error(rej.message);
      });
  }

  doUpdate() {
    this.modelType = "update";
  }

  doCancel() {
    this.modelType = "view";
    this.getShopEntryRule();
  }

  doChangeTab() {}

  getShopEntryRule() {
    SystemConfigApi.getShopEntryRule()
      .then((res) => {
        if (res.data) {
          this.config = res.data;
        } else {
          this.modelType = "create";
        }
      })
      .catch((rej) => {
        this.$message.error(rej.message);
      });
  }
}
