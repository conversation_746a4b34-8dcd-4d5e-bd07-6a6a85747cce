// 微信小程序导航个性化配置
export default class WeixinAppNavigation {
  // uuid
  uuid: Nullable<string> = null
  // 入口类型 底部导航:bottom
  type: Nullable<string> = null
  // 导航名称
  name: Nullable<string> = null
  // 导航页面：微信支付-wxpay、微信会员卡-wxcard、会员码-mbrQRCode、储值付款码-balancePayCode、积分付款码-pointsPayCode
  subPage: Nullable<string> = null
  // 导航样式：舵式样式-rudder,通用样式-normal
  navigationStyle: Nullable<string> = null
  //是否开启配置  "start" 开启  "stop" 不开启
  state: string = 'stop'
}