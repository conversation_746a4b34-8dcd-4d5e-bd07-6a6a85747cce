<!--
 * @Author: 黎钰龙
 * @Date: 2023-11-29 17:06:42
 * @LastEditTime: 2023-11-29 17:11:36
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\show-length\ShowLength.vue
 * 记得注释
-->
<template>
  <span class="length-show">{{currentNumber}}/{{maxLength}}</span>
</template>

<script lang="ts">
import I18nPage from "common/I18nDecorator";
import { Component, Prop, Vue } from "vue-property-decorator";
@Component({
  name: "ShowLength"
})
@I18nPage({
  prefix: ["/公用/券模板"],
  auto: true,
})
export default class ShowLength extends Vue {
  @Prop() computeData: any;
  @Prop({ type: Number}) maxLength: number;

  get currentNumber() {
    return this.computeData?.length || 0
  }
}
</script>

<style lang="scss" scoped>
.length-show {
  font-size: 14px;
  font-family: Avenir, Avenir;
  font-weight: normal;
  color: #a1a6ae;
}
</style>