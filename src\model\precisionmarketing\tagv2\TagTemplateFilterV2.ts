import Channel from "model/common/Channel";
import { TagTypeEnum } from "model/common/TagTypeEnum";
import PageRequest from "model/default/PageRequest";

// 精准营销-会员标签模板查询过滤器V2
export default class TagTemplateFilterV2 extends PageRequest {
  // 标签类型等于
  tagTypeEquals: Nullable<TagTypeEnum> = null;
  // 标签名称类似于
  nameLikes: Nullable<string> = null;
  // 分类uuid等于
  categoryIdEquals: Nullable<string> = null;
  // 来源渠道
  channelEquals: Nullable<Channel> = null;
  // 同步渠道
  syncChannelEquals: Nullable<Channel> = null;
  // 标签类型包含
  tagTypeIn: Nullable<TagTypeEnum[]> = null
  // 标签uuid等于
  uuidEquals: Nullable<string> = null;
}
