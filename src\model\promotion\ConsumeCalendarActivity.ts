import GoodsRange from 'model/common/GoodsRange'
import StairGift from 'model/common/StairGift'
import ActivityBody from 'model/common/ActivityBody'
import StoreRange from 'model/v2/controller/common/StoreRange'

export default class ConsumeCalendarActivity {
  // 适用商品
  goodsRange: Nullable<GoodsRange> = null
  // 人群
  takePartMemberType: Nullable<TakePartMemberType> = null
  // 累计消费次数
  totalConsumeTimes: Nullable<number> = null
  // 累计消费金额
  totalConsumeAmount: Nullable<number> = null
  // 累计消费件数
  totalConsumeQty: Nullable<number> = null
  // 单次消费金额
  singleConsumeAmount: Nullable<number> = null
  // 单次消费数量
  singleConsumeQty: Nullable<number> = null
  // 礼包信息
  gifts: StairGift[] = []
  // true表示只保存，false表示保存并审核
  justSave: Nullable<boolean> = null
  // 活动类型
  body: Nullable<ActivityBody> = null
  // 营销中心
  marketingCenter: Nullable<string> = null
  // 活动门店
  stores: Nullable<StoreRange> = new StoreRange()
}

enum TakePartMemberType {
  // 全部参与
  ALL = 'ALL',
  // 指定人群参与
  INCLUDE = 'INCLUDE',
  // 指定人群不参与
  EXCLUDE = 'EXCLUDE'
}