<!--
 * @Author: 黎钰龙
 * @Date: 2024-01-30 16:30:35
 * @LastEditTime: 2024-08-09 13:52:27
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\member\growth\MembershipGrowthList.vue
 * 记得注释
-->
<template>
  <div class="membership-growth-list-container">
    <ListWrapper class="current-page">
      <template slot="query">
        <MyQueryCmp @reset="doReset" @search="doSearch">
          <el-row>
            <el-col :span="8">
              <form-item :label="formatI18n('/营销/券礼包活动/券礼包活动', '活动名称')">
                <el-input v-model="query.nameLike"></el-input>
              </form-item>
            </el-col>
            <el-col :span="8">
              <form-item :label="formatI18n('/营销/券礼包活动/券礼包活动', '活动号')">
                <el-input v-model="query.numberLike"></el-input>
              </form-item>
            </el-col>
            <el-col :span="8">
              <form-item :label="formatI18n('/营销/券礼包活动/券礼包活动', '所属主题')">
                <el-input v-model="query.topicNameLikes"></el-input>
              </form-item>
            </el-col>
          </el-row>
          <template slot="opened">
            <el-row>
              <el-col :span="8">
                <form-item :label="formatI18n('/营销/券礼包活动/券礼包活动', '活动时间')">
                  <el-date-picker :end-placeholder="formatI18n('/营销/券礼包活动/券礼包活动', '结束时间')" style="width: 100%" format="yyyy-MM-dd" range-separator="-"
                    ref="selectDate" size="small" :start-placeholder="formatI18n('/营销/券礼包活动/券礼包活动', '开始时间')" type="daterange" v-model="selectDate"
                    value-format="yyyy-MM-dd" :picker-options="dateRangeOption">
                  </el-date-picker>
                </form-item>
              </el-col>
              <el-col :span="8">
                <form-item :label="formatI18n('/营销/券礼包活动/券礼包活动', '活动类型')">
                  <el-select :placeholder="formatI18n('/公用/下拉框/提示', '全部')" style="width: 100%" v-model="query.typeEquals">
                    <el-option :label="formatI18n('/公用/下拉框/提示', '全部')" value="">
                      {{ formatI18n("/公用/下拉框/提示", "全部") }}
                    </el-option>
                    <el-option v-for="(item, index) in activitiesInfo" :key="index" :label="item.label" :value="item.value" v-show="item.viewAble">
                      {{ item.label }}
                    </el-option>
                  </el-select>
                </form-item>
              </el-col>
            </el-row>
          </template>
        </MyQueryCmp>
      </template>
      <template slot="list">
        <el-tabs @tab-click="doHandleClick" v-model="activeName">
          <el-tab-pane :label="getAllCount" name="first">
            <el-checkbox @change="checkedAllRow" style="margin-left: 14px;margin-right: 10px" v-model="singleAll"> </el-checkbox>{{ getSelectActive }}
            <el-button v-if="batchAuditShow" @click="doBatchAudit()">
              {{ formatI18n("/营销/券礼包活动/券礼包活动", "批量审核") }}
            </el-button>
            <el-button v-if="batchStopShow" @click="doBatchEnd()">
              {{ formatI18n("/营销/券礼包活动/券礼包活动", "批量终止") }}
            </el-button>
            <el-button v-if="batchDeleteShow" @click="doBatchDelete()" style="color: red">
              {{formatI18n("/营销/券礼包活动/券礼包活动", "批量删除")}}
            </el-button>
          </el-tab-pane>
          <el-tab-pane :label="getNoAudit" name="second">
            <el-checkbox @change="checkedAllRow" style="margin-left: 14px;margin-right: 10px" v-model="singleAll"> </el-checkbox>{{ getSelectActive }}
            <el-button v-if="batchAuditShow" @click="doBatchAudit()">
              {{ formatI18n("/营销/券礼包活动/券礼包活动", "批量审核") }}
            </el-button>
            <el-button v-if="batchDeleteShow" @click="doBatchDelete()" style="margin-left: 20px;color: red">
              {{formatI18n("/营销/券礼包活动/券礼包活动", "批量删除")}}
            </el-button>
          </el-tab-pane>
          <el-tab-pane :label="getAuditing" v-if="isOaActivity" name="auditing">
          </el-tab-pane>
          <el-tab-pane :label="getReject" v-if="isOaActivity" name="reject">
            <el-checkbox @change="checkedAllRow" style="margin-left: 14px;margin-right: 10px" v-model="singleAll"> </el-checkbox>
            {{ getSelectActive }}
            <el-button v-if="batchDeleteShow" @click="doBatchDelete()" style="margin-left: 20px;color: red">
              {{formatI18n("/营销/券礼包活动/券礼包活动", "批量删除")}}
            </el-button>
          </el-tab-pane>
          <el-tab-pane :label="getNoStart" name="third">
            <el-checkbox @change="checkedAllRow" style="margin-left: 14px;margin-right: 10px" v-model="singleAll"> </el-checkbox>{{ getSelectActive }}
            <el-button v-if="batchStopShow" @click="doBatchEnd()">
              {{ formatI18n("/营销/券礼包活动/券礼包活动", "批量终止") }}
            </el-button>
          </el-tab-pane>
          <el-tab-pane :label="getDoing" name="forth">
            <el-checkbox @change="checkedAllRow" style="margin-left: 14px;margin-right: 10px" v-model="singleAll">
            </el-checkbox>{{ getSelectActive }}&nbsp;
            <el-button v-if="batchStopShow" @click="doBatchEnd()">
              {{ formatI18n("/营销/券礼包活动/券礼包活动", "批量终止") }}
            </el-button>
          </el-tab-pane>
          <el-tab-pane :label="getEnd" name="five">
          </el-tab-pane>
        </el-tabs>
        <el-table :data="tableData" v-loading="loading" ref="table" @selection-change="handleSelectionChange($event)" style="width: 100%;margin-top: 10px">
          <el-table-column v-if="activeName !== 'five'" type="selection" width="55"> </el-table-column>
          <el-table-column :label="formatI18n('/营销/券礼包活动/券礼包活动', '活动号/活动名称')" fixed="left" prop="activityId" width="170">
            <template slot-scope="scope">
              <div>{{ scope.row.activityId | strFormat }}</div>
              <div v-if="hasCheckPermission(scope.row.type)" :title="scope.row.name">
                <span class="span-btn" @click="doDtl(scope.row)">
                  {{ scope.row.name | strFormat }}
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column :label="formatI18n('/营销/券礼包活动/券礼包活动', '活动类型')" prop="type" width="130">
            <template slot-scope="scope">
              <div>{{ scope.row.type | couponTypeFmt }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="formatI18n('/储值/会员储值/储值调整单/列表/最后修改时间')" prop="lastModified" width="170">
            <template slot-scope="scope">
              <span no-i18n>{{ scope.row.lastModified | dateFormate3 }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="formatI18n('/营销/券礼包活动/券礼包活动', '所属主题')" prop="topicName" width="130">
            <template slot-scope="scope">
              <div :title="scope.row.topicName" style="text-align: left;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 130px;">
                {{ scope.row.topicName | strFormat }}
              </div>
            </template>
          </el-table-column>
          <el-table-column :label="formatI18n('/营销/券礼包活动/券礼包活动', '活动时间')" width="180">
            <template slot-scope="scope">
              <div v-if="scope.row.beginDate && scope.row.endDate">
                {{ scope.row.beginDate | dateFormate2 }}
                {{ formatI18n("/营销/券礼包活动/券礼包活动", "至") }}
                {{scope.row.endDate | dateFormate2}}
              </div>
              <div v-else>--</div>
            </template>
          </el-table-column>
          <el-table-column :label="formatI18n('/营销/券礼包活动/券礼包活动', '状态')" align="center" prop="state" width="150">
            <template slot-scope="scope">
              <ActivityStateTag :stateEquals="scope.row.state"></ActivityStateTag>
            </template>
          </el-table-column>
          <el-table-column :label="formatI18n('/营销/券礼包活动/券礼包活动', '操作')" fixed="right" min-width="300">
            <template slot-scope="scope">
              <span class="span-btn" @click="doAudit(scope.row)" v-if="scope.row.state === 'INITAIL' && hasAuditPermission(scope.row.type)">
                {{ formatI18n("/营销/券礼包活动/券礼包活动", "审核") }}
              </span>
              <span class="span-btn" @click="doCopy(scope.row)" v-if="hasModifyPermission(scope.row.type)">
                {{ formatI18n("/营销/券礼包活动/券礼包活动", "复制") }}
              </span>
              <span class="span-btn" @click="doModify(scope.row)" v-if="
                hasModifyPermission(scope.row.type) && (['INITAIL','REJECTED'].indexOf(scope.row.state) > -1 ||
								(scope.row.state === 'UNSTART' && scope.row.type !== 'MEMBER_INVITE_REGISTER_GIFT') ||
								(scope.row.state === 'PROCESSING' && scope.row.type !== 'MEMBER_INVITE_REGISTER_GIFT'))">
                {{ formatI18n("/营销/券礼包活动/券礼包活动", "修改") }}
              </span>
              <span class="span-btn" @click="doDelete(scope.row)"
                v-if="['INITAIL','REJECTED'].indexOf(scope.row.state) > -1 && hasModifyPermission(scope.row.type)">
                {{ formatI18n("/营销/券礼包活动/券礼包活动", "删除") }}
              </span>
              <span class="span-btn" @click="doStop(scope.row)" v-if="
								(['UNSTART','PROCESSING'].indexOf(scope.row.state) > -1 ||
								(scope.row.type == 'MEMBER_INVITE_REGISTER_GIFT' && ['INITAIL','STOPED'].indexOf(scope.row.state) == -1 )) && 
                hasStopPermission(scope.row.type)
              ">
                {{ formatI18n("/营销/券礼包活动/券礼包活动", "终止") }}
              </span>
              <span class="span-btn" @click="doEffectEvaluate(scope.row)" v-if="
								scope.row.type === 'MEMBER_INVITE_REGISTER_GIFT' &&
								['PROCESSING','STOPED'].indexOf(scope.row.state) > -1 &&
								hasOptionPermission('/营销/营销/会员成长/邀请有礼', '活动查看')
              ">
                {{ formatI18n("/营销/券礼包活动/券礼包活动", "效果评估") }}
              </span>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <!--分页栏-->
      <template slot="page">
        <el-pagination :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
          @current-change="onHandleCurrentChange($event)" @size-change="onHandleSizeChange($event)" background
          layout="total, prev, pager, next, sizes,  jumper">
        </el-pagination>
      </template>
    </ListWrapper>
  </div>
</template>

<script lang="ts" src="./MembershipGrowthList.ts">
</script>

<style lang="scss" scoped>
.membership-growth-list-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  .span-btn {
    margin-right: 4px;
  }
}
</style>