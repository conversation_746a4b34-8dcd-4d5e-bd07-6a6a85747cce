<template>
	<div class="prepay-card-store">
		<BreadCrume :panelArray="panelArray">
			<template slot="operate"> </template>
		</BreadCrume>
		<ListWrapper>
			<template slot="query">
				<el-form :inline="true" label-width="120px" class="queryForm">
					<el-row>
						<el-col :span="8">
							<el-form-item :label="i18n('区域代码/名称：')">
								<!-- <el-input v-model="queryFilter.orgKey" :placeholder="i18n('搜索门店代码/名称')" clearable /> -->
								<el-select value-key="id" v-model="queryFilter.zoneId" placeholder="所属区域不限" style="width: 200px" clearable>
									<!-- <el-option label="所属区域不限" :value="null"></el-option> -->
									<el-option
										v-for="(value, index) in areaData"
										:key="value.zone.id"
										:value="value.zone.id"
										:label="'[' + value.zone.id + ']' + value.zone.name"
										>[{{ value.zone.id }}]{{ value.zone.name }}</el-option
									>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item :label="i18n('门店代码/名称：')">
                <SelectStores v-model="queryFilter.orgIdIn" :isOnlyId="true" :hideAll="false" width="100%"
                  :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
                </SelectStores>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
				<el-button type="primary" @click="doSearch">{{ i18n("查询") }}</el-button>
				<el-button @click="doReset">{{ i18n("/公用/按钮/重置") }}</el-button>
			</template>

			<template slot="list">
				<FloatBlock :top="95" refClass="list-wrapper">
					<template slot="ctx">
						<i18n k="/资料/营销中心/已选择{0}个门店">
							<template slot="0"> {{ selected.length }} </template>
						</i18n>
						&nbsp;
						<el-button @click="openEdit" type="primary" v-if="hasOptionPermission('/卡/卡管理/退卡设置', '配置维护')">{{
							i18n("新增")
						}}</el-button>
						<el-button @click="openImport" v-if="hasOptionPermission('/卡/卡管理/退卡设置', '批量导入')">{{ i18n("批量导入") }}</el-button>
						<el-button @click="openBatch" v-if="hasOptionPermission('/卡/卡管理/退卡设置', '配置维护')">{{ i18n("批量设置") }}</el-button>

						<el-button @click="openDialog" type="primary">显示弹框</el-button>
					</template>
				</FloatBlock>
				<el-table :data="data" style="width: 100%;margin-top: 20px" ref="table" @selection-change="handleSelectionChange">
					<el-table-column type="selection" width="55" />
					<el-table-column :label="i18n('门店代码')" prop="orgId"></el-table-column>
					<el-table-column :label="i18n('门店名称')" prop="orgName"></el-table-column>
					<el-table-column :label="i18n('所属区域')">
						<template slot-scope="scope">
							<div v-if="scope.row.zoneId && scope.row.zoneName">
								[{{ scope.row.zoneId }}]
								{{ scope.row.zoneName }}
							</div>
							<div v-else>
								-
							</div>
						</template>
					</el-table-column>
					<el-table-column :label="i18n('卡回收退款条件')" width="180px">
						<template slot-scope="scope">
							{{ i18n("余额") }} {{ scope.row.recycleScope === "LESS" ? i18n("小于") : i18n("小于等于") }} {{ scope.row.moneyScope }} {{ i18n("元") }}
						</template>
					</el-table-column>
					<el-table-column :label="i18n('卡回收款项')">
						<template slot-scope="scope">
							{{ JSON.parse(JSON.stringify(scope.row)).recycleType === "RECHARGE_BALANCE" ? i18n("实充余额") : i18n("总余额") }}
						</template>
					</el-table-column>
					<el-table-column :label="i18n('操作')" fixed="right">
						<template slot-scope="scope">
							<el-button @click="openSetup(scope.row)" type="text" v-if="hasOptionPermission('/卡/卡管理/退卡设置', '配置维护')">{{
								i18n("设置")
							}}</el-button>
						</template>
					</el-table-column>
				</el-table>
			</template>
			<template slot="page">
				<el-pagination
					no-i18n
					:current-page="page.currentPage"
					:page-size="page.size"
					:page-sizes="[10, 20, 30, 40]"
					:total="page.total"
					@current-change="onHandleCurrentChange"
					@size-change="onHandleSizeChange"
					background
					layout="total, prev, pager, next, sizes,  jumper"
					class="pagin"
				>
				</el-pagination>
			</template>
		</ListWrapper>
		<!-- 新增弹框 -->
		<StoreSelectorDialog ref="storeSelectorDialog" :dialogTitle="i18n('新增')" @summit="selectStore"></StoreSelectorDialog>

		<!-- 新增弹框 -->
		<Redemption ref="Redemption" ></Redemption>

		<!-- 设置弹框 -->
		<el-dialog class="inner-dialog-center" :title="i18n('设置')" :visible="setupShow" width="50%" :before-close="handleSetupClose">
			<div>
				<el-form label-width="120px" label-position="left" :model="currentSetup" ref="setupForm">
					<el-form-item :label="i18n('卡回收条件')">
						<span>余额</span>
						<el-select v-model="currentSetup.recycleScope" style="width: 100px;margin-left: 15px">
							<el-option :label="i18n('小于')" value="LESS"></el-option>
							<el-option :label="i18n('小于等于')" value="LESS_EQUALS"></el-option>
						</el-select>
						<AutoFixInput style="width: 100px;margin: 0 15px" :min="0" :max="99999.99" :fixed="2" v-model="currentSetup.moneyScope" />
						<span>元</span>
					</el-form-item>
					<el-form-item :label="i18n('卡回收款项')">
						<el-select v-model="currentSetup.recycleType">
							<el-option :label="i18n('实充余额')" value="RECHARGE_BALANCE"></el-option>
							<el-option :label="i18n('总余额')" value="BALANCE"></el-option>
						</el-select>
						<!-- <div class="gray-text">不填表示不管卡余额为多少，都可以回收。</div> -->
					</el-form-item>
				</el-form>
			</div>
			<span slot="footer" class="dialog-footer">
				<el-button @click="handleSetupClose">{{ i18n("取 消") }}</el-button>
				<el-button type="primary" @click="handleSetupConfirm">{{ i18n("确 定") }}</el-button>
			</span>
		</el-dialog>

		<!-- 批量设置弹框 -->
		<el-dialog class="inner-dialog-center" :title="i18n('批量设置')" :visible="batchSetupShow" width="50%" :before-close="handleBatchSetupClose">
			<div>
				<el-form label-width="120px" label-position="left" :model="batchSetup" ref="setupForm">
					<el-form-item :label="i18n('卡回收条件')">
						<span>余额</span>
						<el-select v-model="batchSetup.recycleScope" style="width: 100px;margin-left: 15px">
							<el-option :label="i18n('小于')" value="LESS"></el-option>
							<el-option :label="i18n('小于等于')" value="LESS_EQUALS"></el-option>
						</el-select>
						<AutoFixInput style="width: 100px;margin: 0 15px" :min="0" :max="99999.99" :fixed="2" v-model="batchSetup.moneyScope" />
						<span>元</span>
					</el-form-item>
					<el-form-item :label="i18n('卡回收款项')">
						<el-select v-model="batchSetup.recycleType">
							<el-option :label="i18n('实充余额')" value="RECHARGE_BALANCE"></el-option>
							<el-option :label="i18n('总余额')" value="BALANCE"></el-option>
						</el-select>
						<!-- <div class="gray-text">不填表示不管卡余额为多少，都可以回收。</div> -->
					</el-form-item>
				</el-form>
				<i18n k="/储值/会员储值/门店预付卡管理/一共选择{0}个门店">
					<template slot="0"> {{ selected.length }} </template>
				</i18n>
			</div>
			<span slot="footer" class="dialog-footer">
				<el-button @click="handleBatchSetupClose">{{ i18n("取 消") }}</el-button>
				<el-button type="primary" @click="handleBatchSetupConfirm">{{ i18n("确 定") }}</el-button>
			</span>
		</el-dialog>
		<!-- 批量导入弹框 -->
		<el-dialog class="inner-dialog-center" :title="i18n('批量导入')" :visible="importShow" width="40%" :before-close="handleImportClose">
			<div>
				<el-form label-width="120px" label-position="left">
					<el-form-item :label="i18n('示例模板')">
						<a class="action-hover_download" @click="downloadTemplate">{{ i18n("导入卡回收设置") }}</a>
					</el-form-item>
					<el-form-item :label="i18n('选择文件')">
						<div>{{ i18n("为保障上传成功，建议每次最多上传5000条信息") }}</div>
						<el-upload
							:limit="1"
							:multiple="false"
							:headers="uploadHeaders"
							:action="uploadUrl"
							:auto-upload="false"
							:on-change="doHandleChange"
							:on-error="getErrorInfo"
							:on-success="getSuccessInfo"
							:with-credentials="true"
							class="upload-demo"
							ref="upload"
						>
							<el-button size="small" slot="trigger" type="default">{{ i18n("/公用/导入/选择文件") }}</el-button>
						</el-upload>
					</el-form-item>
				</el-form>
			</div>
			<span slot="footer" class="dialog-footer">
				<el-button @click="handleImportClose">{{ i18n("取 消") }}</el-button>
				<el-button type="primary" @click="handleImportConfirm">{{ i18n("确 定") }}</el-button>
			</span>
		</el-dialog>
		<DownloadCenterDialog :dialogvisiable="fileDialogVisible" :showTip="showTip" @dialogClose="doDownloadDialogClose"> </DownloadCenterDialog>
	</div>
</template>

<script lang="ts" src="./PrepayCardStore.ts"></script>

<style lang="scss">
.prepay-card-store {
	width: 100%;
	height: 100%;
	background-color: white;
	overflow: auto;
	.current-page {
		height: calc(100% - 10px);
		overflow: auto;
		padding: 20px;

		.el-table {
			td {
				border-bottom: 1px solid #eceef5;
			}

			.cell {
				.el-row {
					margin-top: 15px;

					.label {
						min-width: 120px;
						max-width: 120px;
					}
				}
			}
		}
	}
	.inner-dialog-center {
		display: flex;
		align-items: center;
		justify-content: center;
	}
	.inner-dialog-center .el-dialog {
		margin-top: 0px !important;
	}
	.gray-text {
		color: #888;
	}
}

.action-hover_download {
  cursor: pointer;
  /* 添加手型光标 */
}
</style>
