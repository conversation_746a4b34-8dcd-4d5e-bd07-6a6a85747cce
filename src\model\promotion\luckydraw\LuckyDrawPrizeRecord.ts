

// 中奖记录
import { PrizeType } from "model/promotion/luckydraw/PrizeType";
import PrizeValue from "model/promotion/luckydraw/PrizeValue";
import MutableNsid from "model/common/MutableNsid";

export default class LuckyDrawPrizeRecord {
  // uuid
  uuid: Nullable<string> = null
  // 活动号
  activityNumber: Nullable<string> = null
  // 活动名称
  activityName: Nullable<string> = null
  // 会员id
  memberId: Nullable<string> = null
  // 发生时间
  occurredTime: Nullable<Date> = null
  // 奖品类型
  prizeType: Nullable<PrizeType> = null
  // 奖品id
  prizeId: Nullable<string> = null
  // 奖品名称
  prizeName: Nullable<string> = null
  // 收货人姓名
  consigneeName: Nullable<string> = null
  // 收货人手机号
  consigneePhone: Nullable<string> = null
  // 收货人手机号
  consigneeAddress: Nullable<string> = null
  // 版本
  version: Nullable<number> = null
  // 奖品图片
  image: Nullable<string> = null
  // 奖品说明
  remark: Nullable<string> = null
  // 奖品值
  prizeValue: Nullable<PrizeValue> = null
  // 交易id
  tradeId: Nullable<MutableNsid> = null
  // 中奖轮次
  wonNo: Nullable<number> = null
  // 会员号
  crmCode: Nullable<string> = null
  // 手机号
  mobile: Nullable<string> = null
}
