/*
 * @Author: 黎钰龙
 * @Date: 2023-07-26 15:31:47
 * @LastEditTime: 2023-07-26 16:39:26
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\common\weimob\CanUseDiscount.ts
 * 记得注释
 */
export default class CanUseDiscount {
  // 是否与其他活动共享优惠
  canUseWithOtherDiscount: Nullable<boolean> = false
  /**
   * 商城
   * 当设置可与其他活动共享优惠时该字段必填，1-满减满折；2-第X件X折；6-N元N件；9-订单满赠；
   * 26-直播价；11-满减邮；12-限时折扣；14-限量抢购；15-定金膨胀；16-阶梯价；19-特权价；
   * 20-企业内购；21-拼团；22-砍价；18-会员价；17-单品换购；10-订单换购；7-固定套装；8-搭配套装；
   * 31-裂变内购；32-买单优惠；35-买M付N
   */
  shoppingMallDiscount: Nullable<number[]> = null
  // 堂食
  forHereDiscount: Nullable<any> = null
  // 外卖
  takeOutDiscount: Nullable<any> = null
  // 酒旅
  wineTourDiscount: Nullable<any> = null
}