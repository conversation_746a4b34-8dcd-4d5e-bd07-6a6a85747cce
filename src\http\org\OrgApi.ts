import ApiClient from "http/ApiClient";
import RSOrg from "model/common/RSOrg";
import RSOrgFilter from "model/common/RSOrgFilter";
import PlatOrgInfo from "model/common/PlatOrgInfo";
import QueryPlatFormStoreRequest from "model/common/QueryPlatFormStoreRequest";
import RSSaveBatchOrgRequest from "model/common/RSSaveBatchOrgRequest";
import Response from "model/common/Response";
import OrgImportResult from "model/org/OrgImportResult";
import OrgMarketingCenterConfig from "model/org/OrgMarketingCenterConfig";
import OrgFilter from "model/datum/org/OrgFilter";

export default class OrgApi {
  /**
   * 查询是否开启多营销中心
   *
   */
  static getConfig(): Promise<Response<OrgMarketingCenterConfig>> {
    return ApiClient.server()
      .get(`/v1/org/getConfig`, {})
      .then((res) => {
        return res.data;
      });
  }
  /**
   * 导入门店
   *
   */
  static importExcel(
    body: any,
    marketingCenter?: string
  ): Promise<Response<OrgImportResult>> {
    return ApiClient.server()
      .post(`/v1/org/importExcel`, body, {
        params: {
          marketingCenter: marketingCenter,
        },
      })
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 查询组织
   *
   */
  static query(body: RSOrgFilter): Promise<Response<RSOrg[]>> {
    return ApiClient.server()
      .post(`/v1/org/query`, body, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
    * 查询平台门店列表
    * 查询平台门店列表。
    * 
    */
  static queryPlatFormStore(body: QueryPlatFormStoreRequest): Promise<Response<PlatOrgInfo[]>> {
    return ApiClient.server().post(`/v1/org/queryPlatFormStore`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 组织详情
   * 组织详情。
   * 
   */
  static detail(orgId: string): Promise<Response<any>> {
    return ApiClient.server().get(`/v1/org/detail`, {
      params: {
        orgId: orgId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询不在当前营销中心的组织
   */
  static queryNotInCurrentCenter(
    body: RSOrgFilter
  ): Promise<Response<RSOrg[]>> {
    return ApiClient.server()
      .post(`/v1/org/queryNotInCurrentCenter`, body, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 删除组织
   *
   */
  static remove(id: string): Promise<Response<void>> {
    return ApiClient.server()
      .post(
        `/v1/org/remove`,
        {},
        {
          params: {
            id: id,
          },
        }
      )
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 批量保存
   *
   */
  static saveBatch(body: RSSaveBatchOrgRequest): Promise<Response<void>> {
    return ApiClient.server()
      .post(`/v1/org/saveBatch`, body, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 判断组织是否存在
   * 判断组织是否存在,忽略营销中心
   *
   */
  static isExistOrg(body: RSOrgFilter): Promise<Response<void>> {
    return ApiClient.server()
      .post(`/v1/org/isExistOrg`, body, {})
      .then((res) => {
        return res.data;
      });
  }

  static getPlatform(): Promise<Response<void>> {
    return ApiClient.server()
      .post(`/v1/org/queryPlatform`, {}, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 批量导出
   * 批量导出。
   * 
   */
  static export(body: OrgFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/org/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量导出平台门店
   * 批量导出平台门店。
   * 
   */
  static exportPlatFormStore(body: QueryPlatFormStoreRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/org/exportPlatFormStore`, body, {
    }).then((res) => {
      return res.data
    })
  }

    /**
   * 获取默认门店
   * 获取默认门店
   * 
   */
     static getDefaultOrg(): Promise<Response<any>> {
      return ApiClient.server().post(`/v1/org/getDefaultOrg`, {}, {
      }).then((res) => {
        return res.data
      })
    }

  /**
   * 查询门店标签数量限制
   * 查询门店标签数量限制
   */
  static getTagLimitCount(): Promise<Response<number>> {
    return ApiClient.server().get(`/v1/org/getTagLimitCount`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 禁用组织
   * 禁用组织。
   *
   */
  static disableOrg(orgId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/org/disableOrg`, {}, {
      params: {
        orgId: orgId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 启用组织
   * 启用组织。
   *
   */
  static enableOrg(orgId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/org/enableOrg`, {}, {
      params: {
        orgId: orgId
      }
    }).then((res) => {
      return res.data
    })
  }
}
