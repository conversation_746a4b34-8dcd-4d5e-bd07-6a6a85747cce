/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:19
 * @LastEditTime: 2022-12-12 13:52:33
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\selectGrade\SelectGrade.ts
 * 记得注释
 */
import GradeApi from "http/grade/grade/GradeApi";
import GradesRange from "model/common/GradeRange";
import Grade from "model/grade/Grade";
import { Component, Prop, Vue, Watch } from "vue-property-decorator";

/**
 * 这个时候fant还没支持抽屉组件，等fant升新版本后就不要用这个组件了。
 */
@Component({
  name: "SelectGrade",
  components: {},
})
export default class SelectGrade extends Vue {
  @Prop()
  selected: GradesRange;
  @Prop({ type: Boolean }) disabled: boolean; //是否展示

  isAll: Boolean = true;
  model: any = {
    selectedGrade: [],
  };
  memberLevel: Grade[] = [];
  rule: any = {};
  $refs: any

  @Watch("selected")
  selectedCome(val: GradesRange) {
    if (val.type === "ALL") {
      this.model.selectedGrade = [];
      this.isAll = true;
    } else {
      this.isAll = false;
      this.model.selectedGrade = val.gradeCodes;
    }
  }
  created() {
    const validate = (rule: any, value: any, callback: any) => {
      if (this.isAll === false && this.model.selectedGrade.length === 0) {
        callback(new Error(this.formatI18n("/权益/等级/单笔消费升级激励/编辑页/请选择等级")));
      } else {
        callback();
      }
    };
    this.rule = {
      selectedGrade: [{ validator: validate, trigger: "change" }],
    };
    this.getGradeList();
  }

  getGradeList() {
    GradeApi.listGrade("")
      .then((resp: any) => {
        this.memberLevel = resp.data;
      })
      .catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
  }

  levelChange(e: any) {
    this.submit();
  }

  isAllChange(e: any) {
    if (this.isAll) {
      this.model.selectedGrade = [];
    }
    this.$refs.gradeForm.validate();
    this.submit();
  }

  submit() {
    let val = new GradesRange();
    if (this.isAll) {
      val.type = "ALL";
      val.gradeCodes = [];
    } else {
      val.gradeCodes = this.model.selectedGrade;
      val.type = "PART";
    }
    console.log(val);
    this.$emit("change", val);
  }

  validate() {
    return this.$refs.gradeForm.validate();
  }
}
