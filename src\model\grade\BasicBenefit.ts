export default class BasicBenefit {
    // 等级代码
    grade: string = ''
    // 等级名称
    gradeName: Nullable<string> = null
    // 等级序号
    gradeNo: Nullable<number> = null
    // 是否参与消费得积分
    obtainPoints: Nullable<boolean> = null
    // 积分加倍倍率
    pointsRate: Nullable<number> = null
    // 是否参与会员促销
    promotion: Nullable<boolean> = null
    // 会员促销折扣率
    discountRate: Nullable<number> = null
    // 等级有效期只做修改时展示
    validMonth: Nullable<number> = null
    // 达标最小成长值只做修改时展示
    minGrowthValue: Nullable<number> = null
}