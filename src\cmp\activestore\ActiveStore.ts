import { Component, Inject, Prop, Provide, Vue, Watch } from "vue-property-decorator";
import ImportDialog from "cmp/importdialog/ImportDialog.vue";
import IdName from "model/entity/IdName";
import StoreSelectorDialog from "cmp/selectordialogs/StoreSelectorDialog.vue";
import MarketCenterSelectorDialog from "cmp/selectordialogs/MarketCenterSelectorDialog.vue";
import RSOrg from "model/common/RSOrg";
import ImportResultDialog from "pages/deposit/mbrdeposit/active/dialog/ImportResultDialog.vue";
import PromotionCenterSelectorDialog from "cmp/selectordialogs/PromotionCenterSelectorDialog.vue";
import StoreRange from "model/common/StoreRange";
import BrowserMgr from "mgr/BrowserMgr";
import RSMarketingCenter from "model/common/RSMarketingCenter";
import StoreMulPromotionSelectorDialog from "cmp/selectordialogs/StoreMulPromotionSelectorDialog.vue";
import LimitedMarketingCenter from "model/common/LimitedMarketingCenter";
import NoMarketCenter from "./NoMarketCenter";
import ActiveStoreArea from "./ActiveStoreArea";
import ActiveStoreMarket from "./ActiveStoreMarket";
import I18nPage from "common/I18nDecorator";

class CustomStore {
	// 控制营销中心展示
	promotionCenter: boolean;
	storeRange: StoreRange;
}

class ExportResult {
	importResult: boolean;
	backUrl: string;
	errorCount: number;
	ignoreCount: number;
	successCount: number;
}

export { ExportResult };

@Component({
	name: "ActiveStore",
	components: {
		ImportDialog,
		StoreSelectorDialog,
		ImportResultDialog,
		PromotionCenterSelectorDialog,
		StoreMulPromotionSelectorDialog,
		MarketCenterSelectorDialog,
		NoMarketCenter,
		ActiveStoreArea,
		ActiveStoreMarket
	},
})
@I18nPage({
  prefix: [
    "/公用/券模板",
    '/权益/积分/积分初始化/未初始化状态/得积分规则/基础得积分/点击立即设置',
    '/营销/券礼包活动/微信激活发券/新建页面/活动门店选择非全部门店',
    '/公用/公共组件/门店选择弹框组件/标题',
    '/营销/券礼包活动/券礼包活动',
    '/资料/券承担方',
    '/资料/门店'
  ],
  auto: true
})
export default class ActiveStore extends Vue {
	promotionCenter = false; // 是否开启多营销中心
	$refs: any;
	store: StoreRange = new StoreRange();
	currentMarketCenter: LimitedMarketingCenter = new LimitedMarketingCenter();
	importDialogShow = false;
	importUrl = "v1/org/importExcel";
	@Prop()
	showTip: boolean;
	// 是否展示与活动门店一致
	@Prop({
		type: Boolean,
		default: false,
	})
	sameStore: boolean;
	reloadList = false;
	inputValueRules: any;
	marketCenterRules: any;
	comeValue: any = {};

	@Prop({
		type: Boolean,
		default: true,
	})
	internalValidate: boolean;
	@Prop()
	value: CustomStore;

	@Prop({
		type: Boolean,
		default: false
	})
	isOldActivity: Boolean

	@Inject({
		from: 'showAll',
		default: false
	})
	showAll: Boolean

	@Prop({
		type: Boolean,
		default: false
	})
	justPart: Boolean

  @Prop({
    type: Boolean,
    default: false
  })
  isAddStores: Boolean  //活动追加门店
  @Prop({
	type: Boolean,
	default: false,
  })
  enableStore: boolean; // 是否只查询可用门店
  @Prop({ type: Boolean, default: false }) isHideZone: boolean; // 是否隐藏区域选择
  queryAllOrg: boolean = false;

  @Provide('maxLimit') maxLimit = 999999999

	importResult: ExportResult = new ExportResult();
	importResultDialogShow = false;
	headquarters: Nullable<string> = null
	storeRangeType: any = 'ALL' //门店范围：ALL-全部门店、PART-部分门店适用、EXCLUDE-部分门店不适用
	isZone: Boolean = false

	@Watch("value", { deep: true, immediate:true})
	onValueChange(value: StoreRange) {
		if (!value && this.justPart === true) {
			this.isZone = false
			this.store = new StoreRange();
			this.setMarketCenterForPart();
			this.store.storeRangeType = "PART";
      if (this.store.marketingCenters.length) {
        this.store.marketingCenters[0].stores!.storeRangeType = "PART";
      }
			value = this.store
		}
		this.headquarters = sessionStorage.getItem('headquarters') 
		if(this.headquarters === 'true'){
			this.queryAllOrg = true;
		}
		this.comeValue = value;
		let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
		if (sysConfig) {
			this.promotionCenter = sysConfig.enableMultiMarketingCenter;
		}
		if (this.promotionCenter || this.isAddStores) {
			if (!value.storeRangeType) {
				if ((this.headquarters === 'true' || this.showAll) && this.isOldActivity === false) {
					this.store = new StoreRange();
					this.store.storeRangeType = "ALL";
				} else {
					this.store = new StoreRange();
					this.store.storeRangeType = "PART";
				}
				
			} else {
				this.store = JSON.parse(JSON.stringify(value));
				this.setMarketCenter()
				this.doSetIdName();
				this.doSetType()
			}
			this.setMarketCenter();
		}
	}
	get marketCenterId() {
		if (this.currentMarketCenter && this.currentMarketCenter.marketingCenter) {
			return this.currentMarketCenter.marketingCenter.id;
		}
		return null;
	}
	get marketCenterName() {
		if (this.currentMarketCenter && this.currentMarketCenter.marketingCenter) {
			return this.currentMarketCenter.marketingCenter.name;
		}
		return null;
	}
	get templatePath() {
		if (location.href.indexOf("localhost") === -1) {
			return "template_specify_stores.xlsx";
		} else {
			return "template_specify_stores.xlsx";
		}
	}
	created() {
		if (this.sameStore) {
			this.store.storeRangeType = "SAME";
		}
		let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
		if (sysConfig) {
			this.promotionCenter = sysConfig.enableMultiMarketingCenter;
		}
		this.headquarters = sessionStorage.getItem('headquarters');
		this.inputValueRules = [
			{
				validator: (rule: any, value: string, callback: any) => {
					if (this.store.storeRangeType !== "ALL" && this.store.marketingCenters[0]?.stores?.stores?.length === 0) {
						callback(new Error(this.formatI18n("/营销/积分活动/积分活动/商品满额加送积分/表单校验/提示信息/请选择门店范围")));
					}
					callback();
				},
				trigger: ["change", "blur"],
			},
		];
		this.marketCenterRules = [
			{
				validator: (rule: any, value: string, callback: any) => {
					if (this.promotionCenter || this.isAddStores) {
						if (!this.store.marketingCenters || this.store.marketingCenters.length === 0) {
							callback(new Error(this.formatI18n("/营销/积分活动/积分活动/商品满额加送积分/表单校验/提示信息/请选择营销中心范围")));
							return;
						}
					}
					callback();
				},
				trigger: ["change", "blur"],
			},
		];
		if(this.isAddStores) {
			this.setMarketCenter()
		}
		this.headquarters = sessionStorage.getItem('headquarters');
		if(this.headquarters === 'true' || this.showAll){
			this.queryAllOrg = true;
			this.storeRangeType = 'ALL';
		}	
	}

	validate() {
		if(this.headquarters === 'true'){
			if(this.storeRangeType === "MARKER_CENTER" && (!this.store.marketingCenters || this.store.marketingCenters.length == 0)) {
				this.$message.warning(this.formatI18n("/营销/积分活动/积分活动/商品满额加送积分/表单校验/提示信息/请选择营销中心范围"));
				return Promise.reject();
			}else if((this.storeRangeType === "INCLUDE_ORG" || this.storeRangeType === "EXCLUDE_ORG")&& (!this.store.stores || this.store.stores.length == 0)) {
				this.$message.warning(this.formatI18n("/营销/积分活动/积分活动/商品满额加送积分/表单校验/提示信息/请选择门店范围"));
				return Promise.reject();
			}		
      return Promise.resolve(true)
		}else {
			if (this.promotionCenter || this.isAddStores) {
				let promiseAll = []
				promiseAll.push(this.$refs.form.validate())
				if ((this.headquarters === 'true'  || this.showAll) && this.$refs.activeStoreMarket && this.isOldActivity === false) {
					promiseAll.push(this.$refs.activeStoreMarket.doValidate())
				}
				if (this.headquarters === 'false' && this.$refs.activeStoreArea) {
					promiseAll.push(this.$refs.activeStoreArea.doValidate())
				}
				return Promise.all(promiseAll)
			} else {
				return this.$refs.noMarket.validate();
			}	
		}	
	}

	doClearStore() {
		this.store.stores = [];
		this.store.marketingCenters[0].stores!.stores = []
		this.store.marketingCenters[0].storesValue = "";
    this.doEmitInput()
	}

	setMarketCenter() {
		if ((this.headquarters === 'true'  || this.showAll) && this.isOldActivity === false) {
			return
		}
		this.setMarketCenterForPart()
	}

  initMarketingCenters() {
    const mc: LimitedMarketingCenter = new LimitedMarketingCenter();
    mc.stores = new StoreRange();
    mc.marketingCenter = {
      id: sessionStorage.getItem("marketCenter"),
      name: sessionStorage.getItem("marketCenterName"),
    };
    const arr: LimitedMarketingCenter[] = [];
    mc.stores.storeRangeType = "ALL";
    arr.push(mc);
    this.store.marketingCenters = arr;
  }

	setMarketCenterForPart() {
		// created方法会比这个晚 所以先取一遍
		let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
		if (sysConfig) {
			this.promotionCenter = sysConfig.enableMultiMarketingCenter;
		}
		if (this.promotionCenter || this.isAddStores) {
			if (!this.store.marketingCenters || this.store.marketingCenters.length === 0) {
				this.initMarketingCenters()
				this.doEmitInput();
			}
		}
	}

	doClearPromCenter(marketCenter: LimitedMarketingCenter) {
		marketCenter.storesValue = "";
		marketCenter!.stores!.stores = [];
		this.submit();
		this.$forceUpdate();
	}

	doImport() {
		this.importDialogShow = true;
	}
	doSelect() {
		let goodsData: RSOrg[] = [];
		if(this.headquarters === 'true' || this.showAll) {
			if (this.store && this.store.stores && this.store.stores.length > 0) {
				this.store.stores.forEach((item: IdName) => {
					let obj: RSOrg = new RSOrg();
					obj.org = new IdName();
					obj.org.id = item.id;
					obj.org.name = item.name;
					goodsData.push(obj);
				});
			}	
		}else {
			if (this.store && this.store.marketingCenters[0].stores!.stores && this.store.marketingCenters[0].stores!.stores.length > 0) {
				this.store.marketingCenters[0].stores!.stores.forEach((item: IdName) => {
					let obj: RSOrg = new RSOrg();
					obj.org = new IdName();
					obj.org.id = item.id;
					obj.org.name = item.name;
					goodsData.push(obj);
				});
			}
		}
		this.$refs.selectGoodsScopeDialog.open(goodsData, "multiple");
	}
	openGoodsDialog(marketCenter: LimitedMarketingCenter) {
		this.currentMarketCenter = marketCenter;
		let selected: RSOrg[] = [];
		if (marketCenter.storesValue) {
			let arr = marketCenter.storesValue.split(";");
			for (let i = 0; i < arr.length; i++) {
				if (arr[i]) {
					let obj: RSOrg = new RSOrg();
					obj.org = new IdName();
					if (arr[i].split("[") && arr[i].split("[")[1]) {
						let subArr = arr[i].split("[")[1].split("]")[0];
						obj.org.name = subArr;
					}
					obj.org.id = arr[i].split("[")[0];
					selected.push(obj);
				}
			}
		}
		this.$refs.mulPromotionStore.open(selected, "multiple");
	}
	openPromotionCenterDialog() {
		// todo 这里缺少  营销中心类型  需要调整
		this.$refs.selectPromotionCenterSelectorDialog.open(this.store.marketingCenters, "multiple");
	}
	doPromotionSubmitGoods(arr: RSMarketingCenter[]) {
		// todo 这里缺少  营销中心类型  需要调整
		let marketingCentersMap: any = {};
		for (let item of this.store.marketingCenters) {
			if (item!.marketingCenter!.id) {
				marketingCentersMap[item!.marketingCenter!.id] = item;
			}
		}
		let result = [];
		for (let mc of arr) {
			let id = mc!.marketingCenter!.id;
			if (!id) {
				continue;
			}
			if (marketingCentersMap[id]) {
				// 没选就添加
				result.push(marketingCentersMap[id]);
			} else {
				let center = new LimitedMarketingCenter();
				center.stores = new StoreRange();
				center.marketingCenter = mc.marketingCenter;
				result.push(center);
			}
		}
		this.store.marketingCenters = result;
		this.reloadList = true;
		this.$nextTick(() => {
			this.doEmitInput();
		});
		setTimeout(() => (this.reloadList = false), 10);
	}
	doMulStoreSubmitGoods(arr: RSOrg[]) {
		this.doSubmitGoods(arr);
	}
	doSubmitGoods(arr: RSOrg[]) {
		let stores: IdName[] = [];
		let str = "";
		if (arr && arr.length > 0) {
			arr.forEach((item: any) => {
				if (item && item.org && item.org.id) {
					str += item.org.id + `[${item.org.name}];`;
					let obj: IdName = new IdName();
					obj.id = item.org.id;
					obj.name = item.org.name;
					stores.push(obj);
				}
				if (item && item.id) {
					str += item.id + `[${item.name}];`;
					let obj: IdName = new IdName();
					obj.id = item.id;
					obj.name = item.name;
					stores.push(obj);
				}
			});
		}
		if(this.headquarters === 'true' || this.showAll === true){
			this.store.stores = stores;
		}else {
			this.store.marketingCenters[0].stores!.stores = stores;
			this.store.marketingCenters[0].storesValue = str;
		}
		this.$nextTick(() => {
			this.doEmitInput();
		});
		this.$refs.form.validate();
	}
	doStoreRange() {
		if (this.storeRangeType === "ALL") {
			this.isZone = false
			this.store = new StoreRange();
			this.setMarketCenter();
			this.store.storeRangeType = "PART";
			this.store.marketingCenters[0].stores!.storeRangeType = "ALL";
			// this.$emit("input", {});
			// this.$emit("change");
			this.doEmitInput();
		} else if (this.storeRangeType === "PART") {
			this.isZone = false
			this.store = new StoreRange();
			this.setMarketCenter();
			this.store.storeRangeType = "PART";
			this.store.marketingCenters[0].stores!.storeRangeType = "PART";
			this.doEmitInput();
		} else if (this.storeRangeType === "EXCLUDE") {
			this.isZone = false
			this.store = new StoreRange();
			this.setMarketCenter();
			this.store.storeRangeType = "PART";
			this.store.marketingCenters[0].stores!.storeRangeType = "EXCLUDE";
			this.doEmitInput();
		} else if (this.storeRangeType === "ZONE") {
			this.isZone = true
			this.store = new StoreRange();
			this.setMarketCenter();
			this.store.storeRangeType = "PART";
			this.store.marketingCenters[0].stores!.storeRangeType = "PART";
			this.doEmitInput();
		} else {
			this.store = new StoreRange();
			this.setMarketCenter();
			this.store.storeRangeType = "SAME";
			this.doEmitInput();
		}
		this.$refs.form.validate();
	}
	doStoreRange1() {
		if (this.storeRangeType === "ALL") {
			this.isZone = false
			this.store = new StoreRange();
			this.initMarketingCenters()
			this.store.storeRangeType = "ALL";
			this.store.marketingCenters[0].stores!.storeRangeType = "ALL";
			this.doEmitInput();
		} else if (this.storeRangeType === "MARKER_CENTER") {
			this.isZone = false
			this.store = new StoreRange();
			this.store.storeRangeType = "PART";
			this.store.storeRangeLimitType = "MARKETING_CENTER";
			this.doEmitInput();
		}else if (this.storeRangeType === "INCLUDE_ORG") {
			this.isZone = false
			this.store = new StoreRange();
			// this.setMarketCenter();
      this.initMarketingCenters()
			this.store.storeRangeType = "PART";
			this.store.storeRangeLimitType = "STORE";
			this.doEmitInput();
		}else if (this.storeRangeType === "EXCLUDE_ORG") {
			this.isZone = false
			this.store = new StoreRange();
			// this.setMarketCenter();
      this.initMarketingCenters()
			this.store.storeRangeType = "EXCLUDE";
			this.store.storeRangeLimitType = "STORE";
			this.doEmitInput();
		}else if (this.storeRangeType === "ZONE") {
			this.isZone = true
			this.store = new StoreRange();
			this.setMarketCenter();
			this.store.storeRangeType = "PART";
			this.store.storeRangeLimitType = "ZONE";
			this.doEmitInput();
		}
		this.$refs.form.validate();
	}
	doPromCenterStoreRange(marketCenter: LimitedMarketingCenter) {
		let storeRangeType = marketCenter!.stores!.storeRangeType;
		marketCenter.storesValue = "";
		marketCenter.stores = new StoreRange();
		marketCenter.stores.storeRangeType = storeRangeType;
		marketCenter.stores.stores = [];
		this.doEmitInput();
		this.$refs.form.validate();
	}
	/**
	 * 导入成之后
	 * @param response
	 */
	doUploadSuccess(response: any) {
		if (response.response.code === 2000) {
			this.importResultDialogShow = true;
			this.importResult = new ExportResult();
			if (response.response.data) {
				this.importResult.importResult = response.response.data.success;
				this.importResult.backUrl = response.response.data.backUrl;
				this.importResult.errorCount = response.response.data.errorCount;
				this.importResult.ignoreCount = response.response.data.ignoreCount;
				this.importResult.successCount = response.response.data.successCount;
				this.doSubmitGoods(response.response.data.orgs);
			}
		} else {
			this.$message.error(response.response.msg);
		}
	}
	getStoreCount(count: number) {
		let str: any = this.formatI18n("/公用/券模板", "已选择{0}家门店");
		str = str.replace(/\{0\}/g, count);
		return str;
	}
	private doSetType() {
    console.log('doSetType', this.store);
		if (this.isZone === false && this.headquarters === 'false' && this.justPart === false) {
			this.storeRangeType = this.store.marketingCenters[0]?.stores!.storeRangeType
			this.isZone = false
		}
		// if (this.isZone === false && this.justPart === true) {
		// 	this.storeRangeType = 'PART'
		// 	this.isZone = false
		// }
		if (this.isZone === true) {
			this.storeRangeType = 'ZONE'
			this.isZone = true
		}
		if (this.headquarters === 'true' || this.showAll) {
			if(this.store.storeRangeLimitType === 'MARKETING_CENTER' && this.store.storeRangeType === "PART"){
				this.storeRangeType = "MARKER_CENTER";	
			}else if(this.store.storeRangeLimitType === 'STORE' && this.store.storeRangeType === "PART"){
				this.storeRangeType = "INCLUDE_ORG";	
			}else if(this.store.storeRangeLimitType === 'STORE' && this.store.storeRangeType === "EXCLUDE"){
				this.storeRangeType = "EXCLUDE_ORG";	
			}else if(this.store.storeRangeLimitType === 'ZONE' && this.store.storeRangeType === "PART"){
				this.storeRangeType = "ZONE";	
			}else {
				this.storeRangeType = this.store.storeRangeType;
			}
		}
		// 非总部, 赋值
		for (const item of this.store.marketingCenters) {
      if (item.stores!.zones.length > 0 && item.stores!.storeRangeLimitType === 'ZONE' && this.headquarters === 'false' && this.showAll === false) {
				this.storeRangeType = 'ZONE'
				this.isZone = true
			}
		}
		// 总部,且非券模板
		for (const item of this.store.marketingCenters) {
			if (item.stores!.zones.length > 0 && item.stores!.storeRangeLimitType === 'ZONE' && (this.headquarters === 'true' || this.showAll) && this.isOldActivity === true) {
				this.storeRangeType = 'ZONE'
				this.isZone = true
			}
		}
	}
	private doSetIdName() {
		if (!this.store.marketingCenters) {
			return;
		}
		for (let marketingCenter of this.store.marketingCenters) {
			let arr = marketingCenter!.stores!.stores;
			let str = "";
			if (arr && arr.length > 0) {
				if (arr && arr.length > 0) {
					arr.forEach((item: any) => {
						if (item && item.org && item.org.id) {
							str += item.org.id + `[${item.org.name}];`;
						}
						if (item && item.id) {
							str += item.id + `[${item.name}];`;
						}
					});
				}
			}
			marketingCenter.storesValue = str;
		}
	}

  /**
   * 全部门店：storeRangeType-"ALL"、storeRangeLimitType-null
   * 指定营销中心适用：storeRangeType-"PART"、storeRangeLimitType-"MARKETING_CENTER"
   * 指定门店适用：storeRangeType-"PART"、storeRangeLimitType-"STORE"
   * 指定门店不适用：storeRangeType-"EXCLUDE"、storeRangeLimitType-"STORE"
   * 指定区域适用：storeRangeType-"PART"、storeRangeLimitType-"MARKETING_CENTER"
   */
	private doEmitInput() {
		this.doSetStoreRangeLimitType();
		this.submit();
	}
	private submit() {
		if (this.promotionCenter || this.isAddStores) {
			this.$emit("input", this.store);
		} else {
			this.$emit("input", this.comeValue);
		}
		this.$emit("change");
	}
	private doSetStoreRangeLimitType() {
    if (this.headquarters === 'true' || this.showAll){ // 总部直接跳过
			return;	
		}
		if ((this.promotionCenter || this.isAddStores) && !this.showAll) {
      this.store.storeRangeLimitType = "MARKETING_CENTER";  //当showAll=true时，传出的storeRangeLimitType应该为STORE
		} else {
			this.store.storeRangeLimitType = "STORE";
		}
		if (this.store.marketingCenters && this.store.marketingCenters.length > 0) {
			for (let item of this.store.marketingCenters) {
				item.stores!.storeRangeLimitType = "STORE";
			}
		}
	}

	private parentChange() {
		this.$emit("change");
	}

	commitNoMC(val: any) {
		this.$emit('input', val)
	}
	getAreaData(data: StoreRange) {
		this.$emit("input", data);
		this.$emit("change");
	}

	getMarketData(data: StoreRange) {
		this.$emit("input", data);
		this.$emit("change");
	}

  importResultDialogClose() {
    this.importResultDialogShow = false
    this.$nextTick(()=>{
      this.doSelect()
      this.$refs.selectGoodsScopeDialog.query()
    })
  }

  doSelectMarkerCenter(){
	this.$refs.selectMarketCenterScopeDialog.open(this.store.marketingCenters, "multiple");
  }

	doSubmitMarketCenter(arr: LimitedMarketingCenter[]){
		let result: LimitedMarketingCenter[] = []
		arr.forEach((item: LimitedMarketingCenter) => {
			if (!item.stores) {
				item.stores = new StoreRange()
				const mc: LimitedMarketingCenter = new LimitedMarketingCenter();
				mc.stores = new StoreRange();
				mc.stores.storeRangeType = "ALL";
				mc.stores.storeRangeTypeFront = 'ALL'
				mc.stores.isZone = false
				mc.stores.storeRangeLimitType = "MARKETING_CENTER";
				mc.marketingCenter = {
					id: item.marketingCenter!.id,
					name: item.marketingCenter!.name,
				};
				
				result.push(mc);
				this.doEmitInput();
			} else {
				result.push(item)
			}
		})
		this.store.marketingCenters = result
		this.doCommitData()
	}

	doCommitData() {
		this.$emit('change', this.store)
	}

}
