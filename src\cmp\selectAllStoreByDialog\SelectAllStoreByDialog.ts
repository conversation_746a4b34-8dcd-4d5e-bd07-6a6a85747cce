/*
 * @Author: 黎钰龙
 * @Date: 2024-03-12 11:01:21
 * @LastEditTime: 2024-03-18 18:34:43
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\selectAllStoreByDialog\SelectAllStoreByDialog.ts
 * 记得注释
 */
import FormItem from 'cmp/formitem/FormItem';
import I18nPage from 'common/I18nDecorator';
import { Component, Prop, Provide, Vue, Watch } from 'vue-property-decorator';
import StoreRange from 'model/common/StoreRange'
import ActiveStore from 'cmp/activestore/ActiveStore';

@Component({
  name: 'SelectAllStoreByDialog',
  components: {
    FormItem,
    ActiveStore
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板'
  ],
  auto: true
})
export default class SelectAllStoreByDialog extends Vue {
  $refs: any
  @Provide('showAll') showAll: Boolean = true
  @Prop({ type: String }) label: string;
  @Prop() storeData: StoreRange;
  @Prop({ type: Boolean, default: false }) canEdit: boolean;

  dialogShow: boolean = false

  bindStoreValue: any = ''

  @Watch('storeData', { immediate: true, deep: true })
  handle(value: StoreRange) {
    this.bindStoreValue = JSON.parse(JSON.stringify(value))
  }

  //是否为全部门店
  get isAllStore() {
    return this.storeData.storeRangeType === 'ALL' && this.storeData.storeRangeLimitType === 'STORE' && this.storeData.stores?.length === 0
  }

  doEdit() {
    this.dialogShow = true
  }

  doCancel() {
    this.bindStoreValue = this.storeData
    this.dialogShow = false
  }

  doConfirm() {
    this.$refs.store.validate().then(() => {
      this.$emit('confirm', this.bindStoreValue)
      this.dialogShow = false
    })
  }
};