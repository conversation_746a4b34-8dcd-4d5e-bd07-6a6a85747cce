/*
 * @Author: 黎钰龙
 * @Date: 2024-08-11 13:51:59
 * @LastEditTime: 2024-08-11 13:54:30
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\default\CardState.ts
 * 记得注释
 */
export enum CardState {
  // 未激活
  UNACTIVATED = 'UNACTIVATED',
  // 使用中
  USING = 'USING',
  // 已使用
  USED = 'USED',
  // 转赠中
  PRESENTING = 'PRESENTING',
  // 已作废
  CANCELLED = 'CANCELLED',
  // 已挂失
  LOST = 'LOST',
  // 已冻结
  FROZEN = 'FROZEN',
  // 已回收
  RECOVER = 'RECOVER',
  // 已制卡
  MADE = 'MADE'
}