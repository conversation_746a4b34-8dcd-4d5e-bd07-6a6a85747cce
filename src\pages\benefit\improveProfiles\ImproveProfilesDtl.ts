import { Component, Vue } from 'vue-property-decorator'
import ActivityState from "cmp/activitystate/ActivityState";
import I18nPage from "common/I18nDecorator";
import CouponActivityApi from "http/v2/coupon/CouponActivityApi";
import DateUtil from "util/DateUtil";
import BreadCrume from "cmp/bread-crumb/BreadCrume";
import EditType from "common/EditType";
import DirectionalIssueCouponActivity from 'model/v2/coupon/directionalIssueCoupon/DirectionalIssueCouponActivity'
import ImproveProfilesPermission
  from "pages/benefit/improveProfiles/ImproveProfilesPermission";
import ActiveAddCoupon from 'cmp/activeaddcoupon/ActiveAddCoupon'
import UpgradeLine from 'model/grade/upgradegift/UpgradeLine'
import CouponItem from 'model/common/CouponItem'
import SelectStoreActiveDtlDialog from 'pages/deposit/mbrdeposit/active/dialog/SelectStoreActiveDtlDialog.vue'
import CouponTemplateApi from 'http/coupon/template/CouponTemplateApi'
import { MemberInfoFieldName, MemberInfoFieldNameMap } from 'model/systemConfig/MemberInfoFieldName';
import ActiveStoreDtl from 'cmp/activestoredtl/ActiveStoreDtl';
import SelectGroupCmp from 'cmp/select-group-dialog/SelectGroupCmp';

@Component({
  name: "ImproveProfilesDtl",
  components: {
    BreadCrume,
    ActivityState,
    ActiveAddCoupon,
    SelectStoreActiveDtlDialog,
    ActiveStoreDtl,
    SelectGroupCmp
  },
})
@I18nPage({
  prefix: [
    "/营销/券礼包活动/小程序领券/详情页面",
    "/营销/券礼包活动/小程序领券/编辑页面",
    "/营销/积分活动/积分活动/商品组合满数量加送积分活动/编辑页面/活动信息",
    "/营销/券礼包活动/券礼包活动",
    "/公用/活动/活动信息",
    "/公用/券模板",
    "/公用/按钮",
    "/公用/菜单",
    '/储值/预付卡/预付卡调整单/编辑页面',
    '/储值/预付卡/预付卡充值单',
    '/营销/升级有礼',
    '/设置/系统设置'
  ],
  auto: false,
})
export default class ImproveProfilesDtl extends Vue {
  get activityTime() {
    if (this.detail.body) {
      return `${DateUtil.format(this.detail.body.beginDate, "yyyy-MM-dd HH:mm:ss")}${this.i18n("至")}${DateUtil.format(
        this.detail.body.endDate,
        "yyyy-MM-dd HH:mm:ss"
      )}`;
    }
    return "-";
  }
  i18n: (str: string, params?: string[]) => string;
  activityId = "";
  $refs: any;
  state: string = "";
  detail: any = new DirectionalIssueCouponActivity();
  permission: ImproveProfilesPermission = new ImproveProfilesPermission();
  loading = false;
  panelArray: any[];

  couponDialog = {
    dialogShow: false,
    parent: {},
    child: {},
  };
  selectType: string = "single";
  levelLabelWidth: string = '120px'
  UpgradeLines: UpgradeLine = new UpgradeLine()
  couponTemplateDtlDialogFlag = false;
  child: CouponItem = new CouponItem();

  created() {
    if (sessionStorage.getItem("locale") === "en_US") {
      this.levelLabelWidth = "180px";
    }
    this.getDetail();
    this.panelArray = [
      {
        name: this.formatI18n("/公用/菜单", "会员成长"),
        url: "membership-growth",
      },
      {
        name: this.i18n("完善资料有礼详情"),
        url: "",
      },
    ];
  }

  doDelete() {
    this.$confirm(
      this.formatI18n("/营销/券礼包活动/新建注册发大礼包/详情界面/点击删除提示信息", "删除后不可恢复，确定要删除吗？"),
      this.formatI18n("/公用/按钮", "删除"),
      {
        confirmButtonText: this.formatI18n("/公用/按钮", "确定"),
        cancelButtonText: this.formatI18n("/公用/按钮", "取消"),
      }
    ).then(() => {
      CouponActivityApi.remove(this.$route.query.id as string)
        .then((resp: any) => {
          if (resp && resp.code === 2000) {
            this.$message.success(this.formatI18n("/资料/券承担方/删除成功"));
            this.$router.push({ name: "membership-growth" });
          }
        })
        .catch((error: any) => {
          this.$message.error(error.message);
        });
    });
  }

  getDetail() {
    this.activityId = this.$route.query.id as string;
    this.loading = true;
    CouponActivityApi.getImproveProfilesActivity(this.activityId)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.detail = resp.data;
          // if (this.detail.giftBag) {
          this.selectType = "multiple";
          // }
          this.state = resp.data.body.state;

          if (resp.data.points) {
            this.UpgradeLines.pointCheck = true
            this.UpgradeLines.gift!.points = resp.data.points
          }
          if (resp.data.coupons && resp.data.coupons.length) {
            this.UpgradeLines.couponCheck = true
            this.UpgradeLines.gift!.couponItems = resp.data.coupons
          }
          if (resp.data.growthValue) {
            this.UpgradeLines.growthValueCheck = true
            this.UpgradeLines.growthValue = resp.data.growthValue
          }
        } else {
          this.$message.error(resp.msg);
        }
      })
      .catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      })
      .finally(() => {
        this.loading = false;
      });
  }

  copy(id: any) {
    this.$router.push({
      name: "improveProfiles-add",
      query: { list: "true", id: this.activityId, from: EditType.COPY },
    });
  }

  edit() {
    this.$router.push({
      name: "improveProfiles-add",
      query: { list: "true", id: this.activityId, from: EditType.EDIT },
    });
  }

  audit() {
    this.$confirm(this.formatI18n("/营销/券礼包活动/券礼包活动", "是否确定审核该单据?"), this.formatI18n("/公用/按钮", "审核"), {
      confirmButtonText: this.formatI18n("/公用/按钮", "确定"),
      cancelButtonText: this.formatI18n("/公用/按钮", "取消"),
    }).then(() => {
      CouponActivityApi.audit(this.activityId)
        .then((resp: any) => {
          if (resp && resp.code === 2000) {
            this.$message.success(this.formatI18n("/营销/券礼包活动/核销第三方券", "审核成功"));
            this.getDetail();
          } else {
            this.$message.error(resp.msg);
          }
        })
        .catch((error) => {
          if (error && error.message) {
            this.$message.error(error.message);
          }
        });
    });
  }

  toUserGroupDtl(id: string) {
    this.$router.push({
      name: 'user-group-dtl',
      query: {
        uuid: id
      }
    })
  }

  stop() {
    this.$confirm(
      this.formatI18n("/营销/券礼包活动/券礼包活动", "是否确定终止该单据?") as string,
      this.formatI18n("/营销/券礼包活动/券礼包活动", "终止") as string,
      {
        confirmButtonText: this.formatI18n("/公用/按钮", "确定") as any,
        cancelButtonText: this.formatI18n("/公用/按钮", "取消") as any,
      }
    ).then(() => {
      CouponActivityApi.stop(this.activityId)
        .then((resp: any) => {
          if (resp && resp.code === 2000) {
            this.$message.success(this.formatI18n("/营销/券礼包活动/核销第三方券", "终止成功") as string);
            this.getDetail();
          }
        })
        .catch((error) => {
          this.$message.error(error.message);
        });
    });
  }
  doDialogClose() {
    this.couponTemplateDtlDialogFlag = false;
  }
  doEditStoreValueActive(item: any) {
    CouponTemplateApi.detail(item.templateId as string)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.child.coupons = resp.data;
          this.couponTemplateDtlDialogFlag = true;
        }
      })
      .catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
  }
  transMemberInfoName(id: MemberInfoFieldName) {
    return MemberInfoFieldNameMap[id]
  }
}
