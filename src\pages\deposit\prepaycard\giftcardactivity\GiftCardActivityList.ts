import { Component, Vue } from "vue-property-decorator";
import ListWrapper from "cmp/list/ListWrapper.vue";
import SubHeader from "cmp/subheader/SubHeader.vue";
import FloatBlock from "cmp/floatblock/FloatBlock.vue";
import GiftCardActivityApi from "http/card/activity/GiftCardActivityApi";
import GiftCardActivityFilter from "model/card/activity/GiftCardActivityFilter";
import GiftCardActivity from "model/card/activity/GiftCardActivity";
import ActivityStateCountResult from "model/common/ActivityStateCountResult";
import DateUtil from "util/DateUtil";
import DataUtil from "../common/DataUtil";
import I18nPage from "common/I18nDecorator";
import ActivityState from "cmp/activitystate/ActivityState";
import BreadCrume from "cmp/bread-crumb/BreadCrume.vue";
import PrepayCardTplPermission from "../prepaycardtpl/PrepayCardTplPermission";
import FormItem from "cmp/formitem/FormItem";
import MyQueryCmp from "cmp/querycondition/MyQueryCmp";

@Component({
  name: "GiftCardActivity",
  components: {
    FormItem,
    ListWrapper,
    SubHeader,
    FloatBlock,
    ActivityState,
    BreadCrume,
    MyQueryCmp
  },
})
@I18nPage({
  prefix: [
    "/储值/预付卡/电子礼品卡活动/列表页面",
    "/公用/活动/状态",
    "/公用/活动/活动信息",
    "/公用/活动/提示信息",
    "/公用/按钮",
    '/储值/预付卡/卡模板/列表页面'
  ],
})
export default class GiftCardActivityList extends Vue {
  i18n: (str: string, params?: string[]) => string;
  query: GiftCardActivityFilter = new GiftCardActivityFilter();
  queryData: GiftCardActivity[] = [];
  countResult: ActivityStateCountResult = new ActivityStateCountResult();
  tabName: string = "ALL";
  $refs: any;
  checkedAll: boolean = false;
  selected: GiftCardActivity[] = [];
  prepayCardTplPermission = new PrepayCardTplPermission();
  dataUtil: DataUtil = new DataUtil();
  panelArray: any = [];
  tableLoading: boolean = false
  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10,
  };

  created() {
    this.panelArray = [
      {
        name: this.formatI18n("/公用/菜单/电子卡售卡活动"),
        url: "",
      },
    ];
    this.getList();
  }

  doSearch() {
    this.getList();
  }

  doReset() {
    this.query = new GiftCardActivityFilter();
    this.tabName = "ALL";
    this.page.currentPage = 1;
    this.getList();
  }

  /**
   * 查询
   */
  onSearch() {
    this.page.currentPage = 1;
    this.getList();
  }

  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val;
    this.getList();
  }

  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val;
    this.getList();
  }

  /**
   * 表格排序条件
   */
  onSortChange({ column, prop, order }: any) {
    // todo
  }

  private getList(page?: number) {
    if (page) {
      this.page.currentPage = page
    }
    this.query.page = this.page.currentPage - 1;
    this.query.pageSize = this.page.size;
    this.tableLoading = true
    GiftCardActivityApi.query(this.query)
      .then((resp: any) => {
        this.tableLoading = false
        if (resp && resp.code === 2000) {
          this.countResult = resp.data.countResult;
          this.queryData = resp.data.result;
          this.page.total = resp.data.total;
        }
      })
      .catch((error) => {
        this.tableLoading = false
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
  }

  private add() {
    this.$router.push({ name: "gift-card-activity-edit" });
  }

  private gotoDtl(row: any) {
    this.$router.push({ name: "gift-card-activity-dtl", query: { activityId: row.body.activityId } });
  }

  private gotoEvaluatePage(activityId: any) {
    this.$router.push({ name: "gift-card-activity-evaluate", query: { activityId: activityId } });
  }

  private del(activityId: string) {
    this.$alert(this.i18n("确认要删除吗？"), this.i18n("提示"), {
      confirmButtonText: this.i18n("确定"),
      cancelButtonText: this.i18n("取消"),
      type: "warning",
      callback: (action) => {
        if (action === "confirm") {
          GiftCardActivityApi.remove(activityId)
            .then((resp: any) => {
              if (resp && resp.code === 2000) {
                this.$message.success(this.i18n("删除成功"));
                this.getList();
              } else {
                throw new Error(resp.msg || this.i18n('操作失败'))
              }
            })
            .catch((error) => {
              if (error && error.message) {
                this.$message.error(error.message);
              }
            });
        }
      },
    });
  }

  private delBatch() {
    if (this.selectedActivityIdList.length === 0) {
      this.$message.error(this.i18n("请先勾选要删除的记录"));
      return;
    }
    this.$alert(this.i18n("确认要删除吗？"), this.i18n("提示"), {
      confirmButtonText: this.i18n("确定"),
      cancelButtonText: this.i18n("取消"),
      type: "warning",
      callback: (action) => {
        if (action === "confirm") {
          GiftCardActivityApi.batchRemove(this.selectedActivityIdList as any)
            .then((resp: any) => {
              if (resp && resp.code === 2000) {
                this.$message.success(resp.data);
                this.getList();
              } else {
                throw new Error(resp.msg || this.i18n('操作失败'))
              }
            })
            .catch((error) => {
              if (error && error.message) {
                this.$message.error(error.message);
              }
            });
        }
      },
    });
  }

  private audit(activityId: string) {
    this.$alert(this.i18n("确认要审核吗？"), this.i18n("提示"), {
      confirmButtonText: this.i18n("确定"),
      cancelButtonText: this.i18n("取消"),
      type: "warning",
      callback: (action) => {
        if (action === "confirm") {
          GiftCardActivityApi.audit(activityId)
            .then((resp: any) => {
              if (resp && resp.code === 2000) {
                this.$message.success(this.i18n("审核成功"));
                this.getList();
              } else {
                this.$message.error(resp.msg)
              }
            })
            .catch((error) => {
              if (error && error.message) {
                this.$message.error(error.message);
              }
            });
        }
      },
    });
  }

  private auditBatch() {
    if (this.selectedActivityIdList.length === 0) {
      this.$message.error(this.i18n("请先勾选要审核的记录"));
      return;
    }
    this.$alert(this.i18n("确认要审核吗？"), this.i18n("提示"), {
      confirmButtonText: this.i18n("确定"),
      cancelButtonText: this.i18n("取消"),
      type: "warning",
      callback: (action) => {
        if (action === "confirm") {
          GiftCardActivityApi.batchAudit(this.selectedActivityIdList as any)
            .then((resp: any) => {
              if (resp && resp.code === 2000) {
                this.$message.success(resp.data);
                this.getList();
              } else {
                throw new Error(resp.msg || this.i18n('操作失败'))
              }
            })
            .catch((error) => {
              if (error && error.message) {
                this.$message.error(error.message);
              }
            });
        }
      },
    });
  }

  private stop(activityId: string) {
    this.$alert(this.i18n("确认要停止吗？"), this.i18n("提示"), {
      confirmButtonText: this.i18n("确定"),
      cancelButtonText: this.i18n("取消"),
      type: "warning",
      callback: (action) => {
        if (action === "confirm") {
          GiftCardActivityApi.stop(activityId)
            .then((resp: any) => {
              if (resp && resp.code === 2000) {
                this.$message.success(this.i18n("停止成功"));
                this.getList();
              } else {
                throw new Error(resp.msg || this.i18n('操作失败'))
              }
            })
            .catch((error) => {
              if (error && error.message) {
                this.$message.error(error.message);
              }
            });
        }
      },
    });
  }

  private stopBatch() {
    if (this.selectedActivityIdList.length === 0) {
      this.$message.error(this.i18n("请先勾选要停止的记录"));
      return;
    }
    this.$alert(this.i18n("确认要停止吗？"), this.i18n("提示"), {
      confirmButtonText: this.i18n("确定"),
      cancelButtonText: this.i18n("取消"),
      type: "warning",
      callback: (action) => {
        if (action === "confirm") {
          GiftCardActivityApi.batchStop(this.selectedActivityIdList as any)
            .then((resp: any) => {
              if (resp && resp.code === 2000) {
                this.$message.success(resp.data);
                this.getList();
              } else {
                throw new Error(resp.msg || this.i18n('操作失败'))
              }
            })
            .catch((error) => {
              if (error && error.message) {
                this.$message.error(error.message);
              }
            });
        }
      },
    });
  }

  private copy(activityId: string) {
    this.$router.push({ name: "gift-card-activity-edit", query: { activityId: activityId, editType: "复制" } });
  }

  private edit(activityId: string) {
    this.$router.push({ name: "gift-card-activity-edit", query: { activityId: activityId, editType: "修改" } });
  }

  private handleSelectionChange(val: any) {
    this.selected = val;
  }

  get selectedActivityIdList() {
    return this.selected.map((e) => (e.body ? e.body.activityId : null));
  }

  get allTab() {
    return `${this.i18n("全部")}(${this.countResult.sum})`;
  }

  get initialTab() {
    return `${this.i18n("未审核")}(${this.countResult.initail})`;
  }

  get unstartTab() {
    return `${this.i18n("未开始")}(${this.countResult.unstart})`;
  }

  get processingTab() {
    return `${this.i18n("进行中")}(${this.countResult.processing})`;
  }

  get stopedTab() {
    return `${this.i18n("已结束")}(${this.countResult.stoped})`;
  }

  private activityTime(row: any) {
    return `${DateUtil.format(row.body.beginDate, "yyyy-MM-dd")}${this.i18n("至")}${DateUtil.format(row.body.endDate, "yyyy-MM-dd")}`;
  }

  private handleTabClick(tab: any, event: any) {
    this.query.stateEquals = tab.name === "ALL" ? null : tab.name;
    this.getList();
  }

  private gotoCardTplDtl(num: string) {
    const route = this.$router.resolve({
      name: 'prepay-card-tpl-dtl',
      query: {
        number: num
      }
    })
    window.open(route.href, '_blank')
  }
}
