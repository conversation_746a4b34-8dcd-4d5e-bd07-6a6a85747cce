import GradeCardStyle from 'model/weixin/card/GradeCardStyle'
import WxEntry from 'model/weixin/card/WxEntry'

export default class WeixinCard {
  // 商户名称
  merchantName: Nullable<string> = null
  // 商户logo
  logo: Nullable<string> = null
  // 会员卡标题
  title: Nullable<string> = null
  // 会员权益:SCORE——积分,COUPON——优惠券,GRADE——等级,DEPOSIT——储值
  memberRights: string[] = []
  // 是否同步特权说明
  syncRemark: Nullable<boolean> = null
  // 特权说明
  remark: Nullable<string> = null
  // 使用须知
  notice: Nullable<string> = null
  // 开卡必填信息：MOBILE——手机号,SEX——性别,NAME——姓名,BIRTHDAY——生日,IDCARD——身份证号,EMAIL——邮箱,LOCATION——详细地址,EDUCATION——教育背景,CAREER——职业,INDUSTRY——行业,INCOME——收入,HABIT——兴趣爱好
  requiredInfo: string[] = []
  // 开卡选填信息：MOBILE——手机号,SEX——性别,NAME——姓名,BIRTHDAY——生日,IDCARD——身份证号,EMAIL——邮箱,LOCATION——详细地址,EDUCATION——教育背景,CAREER——职业,INDUSTRY——行业,INCOME——收入,HABIT——兴趣爱好
  optionalInfo: string[] = []
  // 普通会员卡样,若无普通卡样，存在等级卡样，则放入等级卡样的第一个作为普通会员卡样
  image: Nullable<string> = null
  // 等级卡样
  gradeCardStyles: GradeCardStyle[] = []
  // 会员号展示类型:CODE_TYPE_TEXT——仅卡号,CODE_TYPE_QRCODE——卡号和二维码,CODE_TYPE_BARCODE——卡号和条形码
  codeType: Nullable<string> = null
  // 微信是否使用线上积分
  weixinUseCloudScore: Nullable<boolean> = null
  // 微信是否使用线上等级
  weixinUseCloudGrade: Nullable<boolean> = null
  // 积分消息模板配置 引导语
  pointLead: Nullable<string> = null
  // 积分消息模板配置 小程序原始ID
  pointAppId: Nullable<string> = null
  // 积分消息模板配置 小程序路径
  pointPath: Nullable<string> = null
  // 积分消息模板配置 网页链接
  pointNetUrl: Nullable<string> = null
  // 等级权益
  gradeBenefitStr: Nullable<string> = null
  // 等级评定规则
  gradeRatingStr: Nullable<string> = null
  // 自定义入口
  entries: WxEntry[] = []
  // 会员卡中部是否显示会员码
  showQrCode: Nullable<boolean> = false
}