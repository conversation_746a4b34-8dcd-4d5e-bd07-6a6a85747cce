/*
 * @Author: mazheng<PERSON> <EMAIL>
 * @Date: 2023-03-01 16:20:35
 * @LastEditors: 黎钰龙
 * @LastEditTime: 2025-06-10 10:42:01
 * @FilePath: \phoenix-web-ui\src\pages\benefit\improveProfiles\ImproveProfilesAddForm.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import ActivityBody from "model/common/ActivityBody";
import ImproveProfilesGiftActivity from 'model/v2/coupon/improveProfiles/ImproveProfilesGiftActivity'
import CouponItem from "model/common/CouponItem";
import PushGroup from 'model/precisionmarketing/pushplan/PushGroup'
import StoreRange from 'model/common/StoreRange';
import { MemberInfoFieldName } from "model/systemConfig/MemberInfoFieldName";
import ParticipateMember from "model/member/ParticipateMember";

class ImproveProfilesAddForm {
	// 活动id
	activityId: Nullable<string> = null;
	// 名称
	name: Nullable<string> = null;
	// 活动时间
	dateRange: Array<Nullable<Date>> = [];
  groups?: Nullable<ParticipateMember> = new ParticipateMember()  //参与人群
  needImprove: MemberInfoFieldName[] = []
  points: Nullable<number> = null
  coupons: CouponItem[] = []
  growthValue: Nullable<number> = null
  stores: StoreRange = new StoreRange() //活动门店
}

export default class MiniProgramDirectCouponActivityAddForm {
  data: ImproveProfilesAddForm = new ImproveProfilesAddForm()
  master: any
  rules: any
  amountRules: any
  qtyRules: any


  init(master: any) {
    this.master = master;
    this.rules = {
      name: [
        {
          required: true,
          message: master.formatI18n('/营销/积分活动/积分活动/商品满额加送积分/表单校验/提示信息/请输入活动名称'),
          trigger: ['change', 'blur']
        },
        {
          min: 1,
          max: 20,
          message: master.formatI18n('/营销/积分活动/积分活动/商品满额加送积分/表单校验/提示信息', '长度在{0}个字符以内', [20]),
          trigger: ['change', 'blur']
        }
      ],
      dateRange: [
        {
          validator: (rule: any, value: any, callback: any) => {
            if (!value || value.length === 0 || value[0] === null || value[1] === null) {
              callback(master.formatI18n('/营销/积分活动/积分活动/商品满额加送积分/表单校验/提示信息/请选择活动时间'))
            }
            callback()
          },
          trigger: ['change', 'blur']
        }
      ],
      // groups: [
      //   {
      //     required: true,
      //     message: master.i18n('请选择参与人群！'),
      //     trigger: ['change', 'blur'],
      //     type: 'array'
      //   }
      // ]
      needImprove: [
        {
          required: true,
          message: master.i18n('请选择'),
          trigger: ['change', 'blur'],
          type: 'array'
        }
      ]
    }
  }


  toParams() {
    let formData: ImproveProfilesAddForm = JSON.parse(JSON.stringify(this.data))
    let params = new ImproveProfilesGiftActivity()
    // 系统定义的需完善资料
    const sysField = formData.needImprove.filter((item: MemberInfoFieldName | string) => item in MemberInfoFieldName)
    // 自定义的需完善资料
    const customField = formData.needImprove.filter((item: MemberInfoFieldName | string) => !(item in MemberInfoFieldName))
    params.needImprove = sysField || []
    params.needImproveOfCustom = customField || []
    if(this.master.UpgradeLines.pointCheck) {
      params.points = this.master.UpgradeLines.gift.points
    }
    if(this.master.UpgradeLines.couponCheck) {
      params.coupons = this.master.UpgradeLines.gift.couponItems.map((x: any) => {
        return {
          qty: x.qty,
          coupons: x.coupons
        }
      })
    }
    if(this.master.UpgradeLines.growthValueCheck) {
      params.growthValue = this.master.UpgradeLines.growthValue
    }
    params.body = new ActivityBody()
    params.body.stores = formData.stores
    params.body.name = formData.name
    if (formData.dateRange && formData.dateRange.length > 0) {
      params.body.beginDate = formData.dateRange[0]
      params.body.endDate = formData.dateRange[1]
    }
    // 参与人群
    params.rule = new PushGroup()
    params.rule.type = 'USER_GROUP_AND_MEMBER_TAG'
    params.rule.participateMember = formData.groups
    return params
  }

  of(activity: ImproveProfilesGiftActivity) {
    // 参与人群
    this.data.groups = activity.rule?.participateMember || new ParticipateMember()

    this.data.needImprove = [...(activity.needImprove || []), ...(activity.needImproveOfCustom as any || [])]
    if(activity.points) {
      this.master.UpgradeLines.pointCheck = true
      this.master.UpgradeLines.gift.points = activity.points
    }
    if(activity.coupons && activity.coupons.length) {
      this.master.UpgradeLines.couponCheck = true
      this.master.UpgradeLines.gift.couponItems = activity.coupons
    }
    if(activity.growthValue) {
      this.master.UpgradeLines.growthValueCheck = true
      this.master.UpgradeLines.growthValue = activity.growthValue
    }
    if (activity.body) {
      this.data.name = activity.body.name
      this.data.dateRange = [new Date(activity.body.beginDate as any), new Date(activity.body.endDate as any)]
      this.data.stores = activity.body.stores || new StoreRange()
    }
  }
}