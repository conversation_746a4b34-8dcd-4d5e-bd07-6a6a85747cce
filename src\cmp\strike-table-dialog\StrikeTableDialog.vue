<template>
  <el-dialog title="交易冲账" :visible="visible" @close="doClose" :close-on-click-modal="false">
    <!-- 冲账流水 -->
    <el-table :data="value" border style="width: 100%" height="500">
      <el-table-column :label="formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水/发生日期')" fixed prop="issueDate" width="135">
        <template slot-scope="scope">
          <div>{{ scope.row.issueDate | dateFormate3 }}</div>
        </template>
      </el-table-column>
      <el-table-column :label="formatI18n('/营销/积分活动/积分查询/列表/查看流水弹出框/列名', '发生组织')" fixed prop="store" />
      <el-table-column :label="formatI18n('/营销/券礼包活动/券查询', '会员')" fixed prop="mobile">
        <template slot-scope="scope">
          {{
            scope.row.mobile ? scope.row.mobile : (scope.row.crmCode ? scope.row.crmCode : (scope.row.hdCardCardNum ? scope.row.hdCardCardNum : '--'))
          }}
        </template>
      </el-table-column>
      <el-table-column :label="formatI18n('/公用/券模板', '券名称')" prop="name">
        <template slot-scope="scope">
          <div>{{ scope.row.name | nullable }}</div>
        </template>
      </el-table-column>
      <el-table-column :label="formatI18n('/营销/券礼包活动/券查询', '券号')" prop="code" width="160">
        <template slot-scope="scope">
          <div>{{ scope.row.code | nullable }}</div>
        </template>
      </el-table-column>
      <el-table-column :label="formatI18n('/营销/券礼包活动/券报表/发券流水/表格', '券摘要')" prop="summary">
        <template slot-scope="scope">
          <div>{{ scope.row.summary | nullable }}</div>
        </template>
      </el-table-column>
      <el-table-column :label="formatI18n('/公用/预约文件列表', '类型')" prop="type" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.type | nullable }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="formatI18n('/营销/券礼包活动/券报表', '发券渠道')" prop="channelName">
        <template slot-scope="scope">
          <div>{{ isCancel(scope.row.channelName, scope.row.type) | nullable }}</div>
        </template>
      </el-table-column>
      <el-table-column :label="formatI18n('/营销/券礼包活动/券查询/券模板号/券模板号')" prop="templateNumber">
        <template slot-scope="scope">
          <div>{{ scope.row.templateNumber | nullable }}</div>
        </template>
      </el-table-column>
      <el-table-column :label="formatI18n('/权益/券/券模板/外部券模板号')" prop="outTemplateNumber">
        <template slot-scope="scope">
          <div>{{ scope.row.outTemplateNumber | nullable }}</div>
        </template>
      </el-table-column>
      <el-table-column :label="formatI18n('/营销/券礼包活动/券查询/券模板号/用券记录方式')" prop="templateNumber">
        <template slot-scope="scope">
          <div v-if="scope.row.useApporion.subjectApprotionType">
            {{  isCancel(scope.row.useApporion.subjectApprotionType, scope.row.type) | subjectApprotionTypeFilter}}</div>
        </template>
      </el-table-column>
      <el-table-column :label="formatI18n('/营销/券礼包活动/券报表/发券流水/是否付费券')" prop="needPay">
        <template slot-scope="scope">
          <div>{{ scope.row.needPay ? formatI18n('/营销/券礼包活动/券报表/发券流水/付费券') : formatI18n('/营销/券礼包活动/券报表/发券流水/免费券') }}</div>
        </template>
      </el-table-column>
      <el-table-column :label="formatI18n('/营销/券礼包活动/券报表/发券日报/表格', '发券交易额（元）')" prop="amount">
        <template slot-scope="scope">
          <div>{{ isCancel(scope.row.amount, scope.row.type, scope.row.points)  | fmt }}</div>
        </template>
      </el-table-column>
      <el-table-column :label="formatI18n('/营销/积分活动/积分查询/列表/查看流水弹出框/列名', '交易号')" prop="tranNo">
        <template slot-scope="scope">
          <div>{{ isCancel(scope.row.tranNo, scope.row.type) | nullable }}</div>
        </template>
      </el-table-column>
      <el-table-column :label="formatI18n('/营销/券礼包活动/券报表/发券日报/点击展开', '活动代码')" prop="activityNumber">
        <template slot-scope="scope">
          <div>{{ isCancel(scope.row.activityNumber, scope.row.type) | nullable }}</div>
        </template>
      </el-table-column>
      <el-table-column :label="formatI18n('/营销/券礼包活动/券查询', '活动名称')" prop="activityName">
        <template slot-scope="scope">
          <div>{{ isCancel(scope.row.activityName, scope.row.type) | nullable }}</div>
        </template>
      </el-table-column>
      <el-table-column v-if="isMoreMarketing" fixed :label="formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水/发生区域')">
        <template slot-scope="scope">
          <div v-if="scope.row.zone!==null && scope.row.zone!==''">
            <el-tooltip class="item" effect="light" placement="right-end">
              <div>{{scope.row.zone}}</div>
              <div slot="content">
                {{scope.row.zone}}
              </div>
            </el-tooltip>
          </div>
          <div v-else>-</div>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script src="./StrikeTableDialog.ts">
</script>

<style lang="scss" scoped>
</style>