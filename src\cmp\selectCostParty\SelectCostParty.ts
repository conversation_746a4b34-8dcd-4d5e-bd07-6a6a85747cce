/*
 * @Author: L
 * @Date: 2024-10-16 16:45:58
 * @LastEditTime: 2024-10-17 18:05:10
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\selectCostParty\SelectCostParty.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import CostPartyApi from 'http/costparty/CostPartyApi';
import IdName from 'model/common/IdName';
import RSCostParty from 'model/common/RSCostParty';
import RSCostPartyFilter from 'model/common/RSCostPartyFilter';
import { Component, Model, Prop, Vue, Watch } from 'vue-property-decorator';
@Component({
  name: 'SelectCostParty',
  components: {}
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/公用/下拉框/提示'
  ],
  auto: true
})
export default class SelectCostParty extends Vue {
  @Model('change') selectCostPartyIdName: IdName | string
  @Prop({ type: String, default: '400px' }) width: string;
  @Prop() placeholder: any;
  @Prop({ type: Boolean, default: false }) hideAll: boolean; //是否隐藏“全部”选项
  @Prop({ type: Boolean, default: true}) isOnlyId: boolean; //是否只绑定id
  @Prop({ type: Boolean,  default:false}) disabled: boolean;
  selectLoading: boolean = false
  costParties: RSCostParty[] = []

  @Watch('selectCostParty', { deep: true })
  handle(value:any) {
    if(!value) {
      this.getCostParty('')
    }
  }

  get selectCostParty() {
    return this.selectCostPartyIdName
  }
  set selectCostParty(value: any) {
    const res = value ?? null
    this.$emit('change', res)
  }

  created() {
    this.getCostParty()
  }
  doRemoteMethod(value: string) {
    this.getCostParty(value);
  }
  getCostParty(value?: string) {
    let params: RSCostPartyFilter = new RSCostPartyFilter();
    params.idNameLikes = value ?? null;
    params.page = 0;
    params.pageSize = 0;
    this.selectLoading = true
    CostPartyApi.query(params)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.costParties = resp.data;
        } else {
          throw new Error(resp.msg || this.i18n('查询承担方列表失败'))
        }
      })
      .catch((error) => {
        this.$message.error(error.message || this.i18n('内部异常'));
      }).finally(() => this.selectLoading = false)
  }

};