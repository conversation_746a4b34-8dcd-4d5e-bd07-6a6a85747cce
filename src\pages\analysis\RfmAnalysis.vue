<!--
 * @Author: 黎钰龙
 * @Date: 2025-01-15 09:59:12
 * @LastEditTime: 2025-02-18 16:22:34
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\analysis\RfmAnalysis.vue
 * 记得注释
-->
<template>
  <div class="rfm-analysis-container">
    <BreadCrume :panelArray="panelArray">
    </BreadCrume>
    <div class="setting-container">
      <el-row>
        <el-col :span="8">
          <FormItem :label="i18n('RFM标签')">
            <el-select v-model="taskInfo" value-key="uuid" filterable remote :remote-method="doQueryRfmTask" :placeholder="i18n('请选择')"
              style="width: 100%;" @change="doSearch">
              <el-option v-for="item in rfmTaskList" :key="item.uuid" :label="item.tagName" :value="item">
              </el-option>
            </el-select>
          </FormItem>
        </el-col>
      </el-row>
    </div>
    <div class="setting-container">
      <div class="section-title">{{i18n('RFM用户构成')}}</div>
      <div style="height: 400px; width: 750px; margin: auto; position: relative;">
        <div id="myEcharts" style="width: 100%; height: 100%"></div>
        <div class="empty" v-if="!hasChartData">
          <img src="~assets/image/auth/ct_empty.png" />
          <div style="margin-top:-24px">{{i18n('/会员/会员资料/暂无数据')}}</div>
        </div>
      </div>
    </div>
    <div class="setting-container">
      <div class="section-header">
        <div class="section-title">{{formatI18n('/数据/数据洞察/RFM分析/联动明细')}}{{currentTagValue ? '-' + currentTagValue : ''}}</div>
        <div class="section-title">
          <template>
            <el-button v-if="hasOptionPermission('/数据/数据洞察/RFM分析','数据导出')" @click="doExport" size="large">
              {{formatI18n('/数据/数据洞察/RFM分析/联动明细导出')}}
            </el-button>
          </template>
        </div>
      </div>
      <el-table :data="customerList" v-loading="tableLoading" ref="table" style="width: 100%">
        <el-table-column :label="i18n('/会员/会员资料/会员号')" min-width="160px" prop="crmCode">
          <template slot-scope="scope">
            {{scope.row.crmCode || '--'}}
          </template>
        </el-table-column>
        <el-table-column :label="i18n('/会员/会员资料/手机号')" min-width="160px" prop="mobile">
          <template slot-scope="scope">
            {{scope.row.mobile || '--'}}
          </template>
        </el-table-column>
        <el-table-column :label="i18n('消费金额（元）')" min-width="160px" prop="totalTradeAmount">
          <template slot-scope="scope">
            <div class="center-center">
              {{scope.row.totalTradeAmount || '--'}}
              <el-progress :percentage="getAmountPercent(scope.row.totalTradeAmount)" :show-text="false" :stroke-width="10"
                style="margin-left: 8px; flex: 1">
              </el-progress>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('消费次数（次）')" min-width="160px" prop="totalTradeQty">
          <template slot-scope="scope">
            <div class="center-center">
              {{scope.row.totalTradeQty || '--'}}
              <el-progress :percentage="getQtyPercent(scope.row.totalTradeQty)" :show-text="false" :stroke-width="10"
                style="margin-left: 8px; flex: 1">
              </el-progress>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('最近一次消费间隔（天）')" min-width="160px" prop="transInterval">
          <template slot-scope="scope">
            <div class="center-center">
              {{scope.row.transInterval || '--'}}
              <el-progress :percentage="getIntervalPercent(scope.row.transInterval)" :show-text="false" :stroke-width="10"
                style="margin-left: 8px; flex: 1">
              </el-progress>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!--分页栏-->
      <el-pagination :current-page="page.page" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
        @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background layout="total, prev, pager, next, sizes,  jumper">
      </el-pagination>
    </div>
    <DownloadCenterDialog :dialogvisiable="downloadCenterFlag" :showTip="true" @dialogClose="doDownloadDialogClose">
    </DownloadCenterDialog>
  </div>
</template>

<script lang="ts" src="./RfmAnalysis.ts">
</script>

<style lang="scss" scoped>
.rfm-analysis-container {
  width: 100%;
  .empty {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: gray;
    image {
      width: 60px;
      height: 60px;
    }
  }
  .space-between {
    display: flex;
    justify-content: space-between;
  }
  .center-center {
    display: flex;
    align-items: center;
  }
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>