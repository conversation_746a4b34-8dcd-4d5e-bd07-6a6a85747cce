export default class GroupTargetedGiftsResultFilter {
  // 活动号等于
  numberEquals: Nullable<string> = null
  // 会员识别码类型
  memberIdentityType: Nullable<string> = null
  // 会员识别Id
  memberIdentityId: Nullable<string> = null
  // 活动开始日期小于
  issueCouponTimeEnd: Nullable<Date> = null
  // 活动开始日期大于等于
  issueCouponTimeBegin: Nullable<Date> = null
  // 页码
  page: Nullable<number> = null
  // 页面大小
  pageSize: Nullable<number> = null
  // 抓取字段
  fetchParts: string[] = []
}