<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:02
 * @LastEditTime: 2024-05-07 11:23:44
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\coupontenplate\cmp\GroupMutexTemplate.vue
 * 记得注释
-->
<template>
  <div class="group-mutex-template">
    <el-form-item :label="formatI18n('/权益/券/券模板/新建券模板/叠加用券:')" required>
      <el-form :model="ruleForm" :rules="rules" ref="groupMutexTemplateRuleForm">

        <el-radio-group v-model="ruleForm.superpositionLevel" @change="levelChange">
          <el-radio :label="'TRADE'">
            {{formatI18n('/公用/券模板/订单级别')}}
            <el-popover placement="top-start" trigger="hover">
              <div class="tip-content">{{i18n('指本笔交易能否叠加使用多张券')}}</div>
              <i slot="reference" class="iconfont  ic-info icon-tip"></i>
            </el-popover>
          </el-radio>
          <el-radio :label="'GOODS'" v-if="isDiscount && !isSpecial && superpositionVersion == 'V1'">
            {{formatI18n('/公用/券模板/单品级别')}}
            <el-popover placement="top-start" trigger="hover">
              <div class="tip-content">{{i18n('指同一单品能否叠加使用多张券')}}</div>
              <i slot="reference" class="iconfont  ic-info icon-tip"></i>
            </el-popover>
          </el-radio>
        </el-radio-group>
        <el-form-item prop="groupMutexFlag">
          <el-radio-group v-model="ruleForm.superpositionType" @change="doTypeChange">
            <el-radio :label="'NONREUSEABLE'"
              v-if="(!(ruleForm.superpositionLevel === 'TRADE' && noLimit && isSpecial === false) || isSpecial === true)">
              {{i18n('不可叠加')}}
              <template
                v-if="(ruleForm.superpositionType === 'NONREUSEABLE' && noLimit)|| (ruleForm.superpositionType === 'NONREUSEABLE' && ruleForm.multipleNonSuperpositionTypeValue.indexOf('OTHER_COUPON') > -1 && noLimit === false)">
                <div class="select-coupon-text" v-if="ruleForm.groupMutexTemplates.length">
                  <span>{{i18n('已选择')}}</span>
                  <span style="color:#FFAA00; margin:0 4px">{{ruleForm.groupMutexTemplates.length}}</span>
                  <span>{{i18n('个券模板')}}</span>
                </div>
                <span @click.stop="doSelectCouponTemplate" style="color:#20A0FF;margin-left:4px">
                  <template v-if="!ruleForm.groupMutexTemplates.length">{{i18n('选择券模板')}}</template>
                  <template v-else>{{i18n('修改')}}</template>
                </span>
              </template>
            </el-radio>
            <el-radio :label="'REUSEABLE'">
              {{formatI18n('/公用/券模板/可叠加')}}
            </el-radio>
          </el-radio-group>
          <el-form-item prop="multipleNonSuperpositionTypeValue"
            v-if="ruleForm.superpositionType === 'NONREUSEABLE' && ruleForm.superpositionLevel === 'GOODS' && !noLimit">
            <el-checkbox-group v-model="ruleForm.multipleNonSuperpositionTypeValue" @change="doMultipleNonSuperpositionTypeValueChange">
              <el-checkbox label="CURRENT_COUPON">{{formatI18n('/公用/券模板/不可与当前券模板叠加')}}</el-checkbox>
              <el-checkbox label="OTHER_COUPON">{{formatI18n('/公用/券模板/不可与其他券模板叠加')}}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item prop="superpositionTypeValue">
            <el-checkbox-group v-model="ruleForm.superpositionTypeValue"
              v-if="ruleForm.superpositionLevel === 'TRADE' && ruleForm.superpositionType === 'REUSEABLE'" @change="superpositionTypeValueChange">
              <el-checkbox v-if="showCurrentCoupon" :disabled="canPileSelfCoupon" label="CURRENT_COUPON">{{formatI18n('/公用/券模板/可与当前券模板叠加')}}</el-checkbox>
              <el-checkbox :disabled="canPileOtherCoupon" label="OTHER_COUPON">{{formatI18n('/公用/券模板/可与其他券模板叠加')}}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form-item>
      </el-form>
    </el-form-item>
    <CouponTemplateSelectorDialog ref="couponTemplate" :excludedTypes="excludedTypes" :filter="templateFilter" @summit="doSetSelectedTemplates">
    </CouponTemplateSelectorDialog>
  </div>
</template>

<script lang="ts" src="./GroupMutexTemplate.ts">
</script>

<style lang="scss" scoped>
.group-mutex-template {
  .rule-table {
    width: 70%;

    .rule-table-header {
      margin-top: 10px;
      padding: 10px;
      background-color: #e6e6e6;
      border: 1px solid #e6e6e6;
    }

    .rule-table-line {
      padding: 10px;
      border: 1px solid #e6e6e6;
      border-top: 0;
    }
  }
  .select-coupon-text {
    display: inline-block;
    color: #242633;
    margin: 0 4px;
  }
  ::v-deep.el-radio__input.is-checked + .el-radio__label {
    color: #24272b;
  }
}
</style>