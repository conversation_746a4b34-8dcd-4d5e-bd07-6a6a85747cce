export default class OrgFilter {
  // 
  orgTypeEquals: Nullable<string> = null
  // 
  orgIdEquals: Nullable<string> = null
  // 
  orgIdIn: Nullable<string[]> = null
  // 
  idNameLikes: Nullable<string> = null
  // 
  marketingCenterIdNameLikes: Nullable<string> = null
  // 
  orgNameEquals: Nullable<string> = null
  // 
  orgNameLikes: Nullable<string> = null
  // 
  queryNoMarketingCenter: Nullable<boolean> = null
  // 
  marketingCenterIdNotEquals: Nullable<string> = null
  // 
  marketingCenterIdEquals: Nullable<string> = null
  // 
  queryNoZone: Nullable<boolean> = null
  // 
  zoneIdNotEquals: Nullable<string> = null
  // 
  zoneIdEquals: Nullable<string> = null
  // 排序，key表示排序的字段，可选值：orgId；value表示排序方向，可选值为：asc, desc
  sorters: any
  // 
  page: Nullable<number> = null
  // 
  pageSize: Nullable<number> = null
}