/*
 * @Author: 黎钰龙
 * @Date: 2025-01-22 15:04:59
 * @LastEditTime: 2025-01-22 15:09:33
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\analysis\CalculationResult.ts
 * 记得注释
 */
import CrossCalculationResult from "./CrossCalculationResult"
import NormalCalculationResult from "./NormalCalculationResult"

export default class CalculationResult {
  // 单维度-normal  双维度-cross
  type: Nullable<'normal' | 'cross'> = null
  // 
  crossCalculationResults: CrossCalculationResult[] = []
  // 
  normalCalculationResults: NormalCalculationResult[] = []
}