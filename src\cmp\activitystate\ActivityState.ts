import {Component, Prop, Vue} from 'vue-property-decorator'


@Component({
    name: 'PaymentState',
})
export default class ActivityState extends Vue {
    @Prop()
    state: string

    parseState(state: string) {
        switch (state) {
            case 'INITAIL':
                return this.formatI18n('/公用/过滤器/未审核');
            case 'UNSTART':
                return this.formatI18n('/公用/过滤器/未开始');
            case 'PROCESSING':
                return this.formatI18n('/公用/过滤器/进行中');
            case 'STOPED':
                return this.formatI18n('/公用/过滤器/已结束');
            case 'SUSPEND':
              return this.formatI18n('/公用/过滤器/暂停中');
            case 'AUDITING':
              return this.formatI18n('/营销/营销申请/审核中');    
        }
    }

    parseStateColor(state: string) {
        switch (state) {
            case 'INITAIL':
                return 'orange';
            case 'UNSTART':
                return 'blue';
            case 'PROCESSING':
                return 'green';
            case 'SUSPEND':
              return 'grey';
            case 'STOPED':
                return 'grey';
            case 'AUDITING':
                return 'orange';
        }
    }
}
