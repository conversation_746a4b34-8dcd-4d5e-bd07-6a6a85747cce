import GiftReportBaseData from 'model/prepay/report/card/GiftReportBaseData'
import IdName from "model/entity/IdName";

export default class CardDepositHst extends GiftReportBaseData {
    //
    originalBalance: Nullable<number> = null
    //充值余额
    balance: Nullable<number> = null
    //
    occurredBalance: Nullable<number> = null
    //
    occurredGiftBalance: Nullable<number> = null
    //
    occurredTotalBalance: Nullable<number> = null
    //
    activity: Nullable<IdName> = null;
}