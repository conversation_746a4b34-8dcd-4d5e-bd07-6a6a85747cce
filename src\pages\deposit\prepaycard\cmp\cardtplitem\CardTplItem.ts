import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import CardTplDialog from '../cardtpldialog/CardTplDialog'
import CardPicList from 'cmp/cardpiclist/CardPicList.vue'
import CardTemplate from 'model/card/template/CardTemplate'
import CardTemplateApi from 'http/card/template/CardTemplateApi'
import TemplateValidity from 'model/card/template/TemplateValidity'
import GoodsScopeDtl from 'cmp/goodsscope/GoodsScopeDtl.vue'
import DateUtil from 'util/DateUtil'
import I18nPage from "common/I18nDecorator";
import ActiveStoreDtl from "cmp/activestoredtl/ActiveStoreDtl";
import RoutePermissionMgr from "mgr/RoutePermissionMgr";


@Component({
  name: 'CardTplItem',
  components: {
    CardTplDialog,
    CardPicList,
    GoodsScopeDtl,
    ActiveStoreDtl
  }
})
@I18nPage({
  prefix: [
    '/储值/预付卡/电子礼品卡活动/编辑页面/选中的卡模板信息组件',
    '/公用/按钮',
    '/储值/预付卡/卡模板/编辑页面'
  ],
})
export default class CardTplItem extends Vue {
  tpl: any = {}
  dialogShow: boolean = false
  selected: CardTemplate = new CardTemplate()
  detail: CardTemplate = new CardTemplate()
  $refs: any
  @Prop({
    type: String,
    default: null
  })
  number: string
  @Prop({
    type: Boolean,
    default: false
  })
  readonly: boolean

  @Prop({ type: Boolean, default:false})
  isSelectMultiple: any;  //是否能多选

  @Prop({ type: Array, default: () => { return [] } })
  cardMedium: string[];  //需要查询的卡介质

  @Prop()
  type: any

  @Prop({ type: Array, default: () => {return []} })
  selectNo: string[];  //选中的模板

  @Prop({ type: Boolean, default: true })
  canSelectCenter: any; //是否可以选择其他营销中心的卡模板
  @Prop({
    type: Number,
    default: 0
  })
  maxSel: number

  @Prop({ type: Boolean, default: true }) isShowFitGoods: boolean; //是否展示适用商品
  mounted() {
    if (this.number) {
      this.getDetail(this.number)
    }
  }

  private showDialog() {
      this.dialogShow = true
  }
  private doDialogClose() {
      this.dialogShow = false
  }
  private doSummit(val: CardTemplate[], val2: string[]) {
    if(!this.isSelectMultiple) {
      this.$emit('submit', val[0])
      let num = val[0].number!
      this.getDetail(num)
    } else {
      this.$emit('submit', val, val2)
    }
  }    
  @Watch('number')
  onNumberChange() {
    if (this.number) {
      this.getDetail(this.number)
    }
  }

  //是否可以新建卡模板
  get canCreateCardTemplate() {
    return this.hasOptionPermission('/卡/卡管理/卡模板/礼品卡', '卡模板维护') || this.hasOptionPermission('/卡/卡管理/卡模板/充值卡', '卡模板维护') || this.hasOptionPermission('/卡/卡管理/卡模板/储值卡', '卡模板维护')
  }

  //是否可以管理卡模板
  get canManageCardTemplate() {
    return this.hasOptionPermission('/卡/卡管理/卡模板/礼品卡', '卡模板维护') || this.hasOptionPermission('/卡/卡管理/卡模板/充值卡', '卡模板维护') || this.hasOptionPermission('/卡/卡管理/卡模板/储值卡', '卡模板维护')
  }

  // private showDialog() {
  //   this.dialogShow = true
  // }
  // private doDialogClose() {
  //   this.dialogShow = false
  // }
  // private doSummit(val: CardTemplate) {
  //   this.selected = val
  //   let num = this.selected.number as string
  //   this.getDetail(num)
  // }

  private getDetail(num: string) {
    if (!num && !this.detail.number) {
      return
    }
    let numb = num ? num : this.detail.number as string
    CardTemplateApi.info(numb).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.detail = resp.data
        // this.$emit('submit', this.detail)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private refresh(num: string) {
    this.getDetail(num)
    this.$refs.cardTplDialog.getList()
  }

  get validatyInfo() {
    if (!this.detail.validityInfo) {
      return '-'
    }
    if (this.detail.validityInfo.validityType === 'FIXED') {
      return DateUtil.format(this.detail.validityInfo.endDate as Date, 'yyyy-MM-dd')
    }
    if (this.detail.validityInfo.validityType === 'RALATIVE') {
      if (this.detail.cardMedium === 'online' && this.detail.cardTemplateType === 'GIFT_CARD') {
        return this.formatI18n('/储值/预付卡/卡模板/列表页面/激活后{0}内有效', null, [this.getValidNum(this.detail.validityInfo)])
      } else {
        return this.formatI18n('/储值/预付卡/卡模板/编辑页面/发售后{0}内有效', null, [this.getValidNum(this.detail.validityInfo)])
      }
    }
  }

  private getValidNum(validityInfo: TemplateValidity) {
    if (validityInfo.validityDays !== null) {
      return `${validityInfo.validityDays}${this.i18n('天')}`
    }
    if (validityInfo.validityYears !== null) {
      return `${validityInfo.validityYears}${this.i18n('年')}`
    }
  }

  private gotoTplDtl() {
    RoutePermissionMgr.openBlank({ name: 'prepay-card-tpl-dtl', query: { number: this.detail.number } })
  }

  private newTpl() {
    RoutePermissionMgr.openBlank({ name: 'prepay-card-tpl-edit', query: { cardTemplateType: this.type } })
  }

  private tplList() {
    RoutePermissionMgr.openBlank({ name: 'prepay-card-tpl' })
  }
}
