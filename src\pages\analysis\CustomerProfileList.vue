<!--
 * @Author: 黎钰龙
 * @Date: 2025-04-02 18:43:10
 * @LastEditTime: 2025-05-09 15:02:11
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\analysis\CustomerProfileList.vue
 * 记得注释
-->
<template>
  <div class="page-container">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button type="primary" v-if="hasOptionPermission('/数据/数据洞察/客群画像', '数据维护')" size="large" @click="doCreate">{{i18n('新建客群画像')}}</el-button>
      </template>
    </BreadCrume>
    <div class="setting-container">
      <MyQueryCmp @reset="doReset" @search="onSearch" :showExpand="false">
        <el-row>
          <el-col :span="8">
            <FormItem :label="i18n('画像名称')">
              <el-input v-model="nameLikes" :placeholder="formatI18n('/公用/查询条件/提示/类似于')"></el-input>
            </FormItem>
          </el-col>
        </el-row>
      </MyQueryCmp>
    </div>
    <div class="setting-container">
      <el-table :data="profileList" style="width: 100%">
        <el-table-column :label="i18n('画像名称')" prop="name">
          <template slot-scope="scope">
            <span class="span-btn" style="font-size: 14px" @click="doToDtl(scope.row.uuid)">{{ scope.row.name }}</span>
            <i class="el-icon-edit" v-if="hasOptionPermission('/数据/数据洞察/客群画像', '数据维护')" @click="doEditTitle(scope.row.uuid)"
              style="margin-left: 8px; cursor: pointer" />
          </template>
        </el-table-column>
        <el-table-column :label="i18n('/页面/页面管理/最后修改时间')" prop="name">
          <template slot-scope="scope">
            <span no-i18n>{{ scope.row.lastModified | dateFormate3 }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="formatI18n('/储值/预付卡/充值卡制售单/列表页面', '创建人')" prop="creator">
          <template slot-scope="scope">
            {{ scope.row.creator || '--' }}
          </template>
        </el-table-column>
        <el-table-column :label="formatI18n('/储值/预付卡/充值卡制售单/列表页面', '最后修改人')" prop="lastModifier">
          <template slot-scope="scope">
            {{ scope.row.lastModifier || '--' }}
          </template>
        </el-table-column>
        <el-table-column :label="i18n('操作')" width="136">
          <template slot-scope="scope">
            <el-button type="text" v-if="hasOptionPermission('/数据/数据洞察/客群画像', '数据维护')" @click="doEdit(scope.row.uuid)">
              {{i18n('/会员/会员资料/编辑')}}
            </el-button>
            <el-button type="text" v-if="hasOptionPermission('/数据/数据洞察/客群画像', '数据维护')" @click="doDelete(scope.row.uuid)">
              {{i18n('删除')}}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--分页栏-->
      <el-pagination :current-page="page.page" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total" background
        @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" layout="total, prev, pager, next, sizes,  jumper">
      </el-pagination>
    </div>
    <el-dialog :title="addFlag ? i18n('新建客群画像') : i18n('修改画像名称')" width="640px" :close-on-click-modal="false" :visible.sync="createVisible">
      <el-form :model="createForm" :rules="createRules" ref="form" label-width="130px">
        <el-form-item :label="i18n('画像名称')" prop="name">
          <el-input v-model="createForm.name" maxlength="40" style="width: 440px" :placeholder="i18n('请输入')"></el-input>
        </el-form-item>
        <el-form-item v-if="addFlag" :label="i18n('目标客群')" prop="targetCustomerId">
          <SelectCustomerGroup v-model="createForm.targetCustomerId" :hideAll="true" :width="'440px'" :placeholder="i18n('请选择/输入客群')">
          </SelectCustomerGroup>
        </el-form-item>
        <el-form-item v-if="addFlag" :label="i18n('对比客群')" prop="compareCustomerId">
          <SelectCustomerGroup v-model="createForm.compareCustomerId" :hideAll="true" :width="'440px'" :placeholder="i18n('请选择/输入客群')">
          </SelectCustomerGroup>
        </el-form-item>
      </el-form>
      <div class="footer">
        <el-button size="large" @click="closeDialog">{{i18n('/公用/按钮/取消')}}</el-button>
        <el-button type="primary" size="large" @click="confirmCreate">{{i18n('/公用/按钮/确定')}}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" src="./CustomerProfileList.ts">
</script>

<style lang="scss" scoped>
.page-container {
  width: 100%;
  .footer {
    display: flex;
    justify-content: end;
    margin-top: 120px;
  }
}
</style>