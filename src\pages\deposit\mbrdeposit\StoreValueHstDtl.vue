<template>
  <div class="store-value-hst-dtl">
    <BreadCrume :panelArray="panelArray"></BreadCrume>
    <div style="overflow: auto;height: 95%">
      <el-row class="header">
        <el-col :span="12">
          <div class="primary">
            <span>姓名：</span> {{ detail.mbrName }}
          </div>
          <el-row class="secondary">
            <el-col :span="12">
              <span>会员号：</span> {{ detail.crmCode }}
            </el-col>
            <el-col :span="12">
              <span>手机号：</span> {{ detail.mobile }}
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="12">
          <div class="primary">
            <span no-i18n v-if="enableMultipleAccount">{{ account|idName }} &nbsp;&nbsp; </span><span>当前储值余额：</span>
            {{ detail.total|amount }}
          </div>
          <el-row class="secondary">
            <el-col :span="12">
              <span>实充余额：</span> {{ detail.balance|amount }}
            </el-col>
            <el-col :span="12">
              <span>返现余额：</span> {{ detail.giftBalance|amount }}
            </el-col>
          </el-row>
        </el-col>
      </el-row>

      <div class="all-total">
        <el-block-panel>
          <el-block-panel-item>
            <div class="height-80">
              <p class="primary"><span>累计充值：</span>{{ detail.totalRecharge|amount }}<span>元</span></p>
              <p class="secondary"><span>实充增加：</span>{{ detail.realRecharge|amount }}<span>元</span></p>
              <p class="secondary"><span>返现增加：</span>{{ detail.giftRecharge|amount }}<span>元</span></p>
            </div>
          </el-block-panel-item>
          <el-block-panel-item>
            <div class="height-80">
              <p class="primary">
                <span>累计消费：</span>{{ Math.abs(detail.totalConsume)|amount }}<span>元</span>
              </p>
              <p class="secondary">
                <span>实充扣减：</span>{{ Math.abs(detail.realConsume)|amount }}<span>元</span>
              </p>
              <p class="secondary">
                <span>返现扣减：</span>{{ Math.abs(detail.giftConsume)|amount }}<span>元</span>
              </p>
            </div>
          </el-block-panel-item>
          <el-block-panel-item>
            <div class="height-80">
              <p class="primary">
                <span>累计消费退款：</span>{{ Math.abs(detail.totalRefund)|amount }}<span>元</span>
              </p>
              <p class="secondary">
                <span>实充增加：</span>{{ Math.abs(detail.realRefund)|amount }}<span>元</span>
              </p>
              <p class="secondary">
                <span>返现增加：</span>{{ Math.abs(detail.giftRefund)|amount }}<span>元</span>
              </p>
            </div>
          </el-block-panel-item>
        </el-block-panel>
      </div>

      <ListWrapper class="current-page" style="overflow: hidden">
        <template slot="list">
          <el-table :data="transactions" style="width: 100%;margin-top: 20px">
            <el-table-column fixed label="交易时间" prop="occurredTime">
              <template slot-scope="scope">
                <span no-i18n>{{ scope.row.occurredTime|yyyyMMddHHmmss }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed label="发生组织" prop="occurredOrg.id">
              <template slot-scope="scope">
                <span no-i18n>{{ scope.row.occurredOrg | idName }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed label="交易类型" prop="category">
              <template slot-scope="scope">
                <span no-i18n>{{ parseCategory(scope.row.category) }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed label="原余额(元)" prop="originalAmount" width="200" align="right">
              <template slot-scope="scope">
                <template v-if="hasOptionPermission('/小程序定制页/百联储值/百联储值', '开通')">--</template>
                <span no-i18n v-else>{{ scope.row.originalAmount + scope.row.originalGiftAmount|amount }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed label="发生金额(元)" prop="totalAmount" width="200" align="right">
              <template slot-scope="scope">
                <span no-i18n style="color: red" v-if="scope.row.totalAmount >= 0">+{{ scope.row.totalAmount|amount }}</span>
                <span no-i18n style="color: green" v-if="scope.row.totalAmount < 0">{{ scope.row.totalAmount|amount }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed label="发生后余额(元)" prop="amount" width="200" align="right">
              <template slot-scope="scope">
                <template v-if="hasOptionPermission('/小程序定制页/百联储值/百联储值', '开通')">--</template>
                <span no-i18n v-else>{{
                                scope.row.originalAmount + scope.row.originalGiftAmount +
                                    scope.row.totalAmount|amount
                              }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed label="交易流水号" prop="transNo">
              <template slot-scope="scope">
                <span no-i18n>{{ scope.row.transNo }}</span>
              </template>
            </el-table-column>
          </el-table>
        </template>
        <!--分页栏-->
        <template slot="page">
          <el-pagination no-i18n :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
            @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background layout="total, prev, pager, next, sizes,  jumper">
          </el-pagination>
        </template>
      </ListWrapper>
    </div>
  </div>
</template>

<script lang="ts" src="./StoreValueHstDtl.ts">
</script>

<style lang="scss">
.store-value-hst-dtl {
  width: 100%;
  height: 100%;
  background-color: white;
  overflow: hidden;

  .current-page {
    .el-select {
      width: 100%;
    }
    .cell {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .header {
    padding: 20px;
    .primary {
      font-weight: 500;
      font-style: normal;
      font-size: 20px;
      color: #515151;
    }
    .secondary {
      color: rgba(51, 51, 51, 0.647058823529412);
    }
  }

  .all-total {
    .height-80 {
      .primary {
        font-weight: 600;
        color: #515151;
      }
      .secondary {
        color: rgba(51, 51, 51, 0.647058823529412);
      }
    }
  }
}
</style>
