/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-10-21 14:26:33
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\points\activity\goodsgroupgainbyqtypoints\GoodsGroupGainByQtyPointsActivity.ts
 * 记得注释
 */
import ActivityBody from "model/common/ActivityBody";
import GoodsGroupLine from "model/points/activity/goodsgroupgainbyqtypoints/GoodsGroupLine";
import DateTimeCondition from "model/common/DateTimeCondition";
import ChannelRange from "model/common/ChannelRange";
import GradesRange from "model/common/GradeRange";
import PushGroup from "model/precisionmarketing/pushplan/PushGroup";

// 商品组合满数量加送积分活动
export default class GoodsGroupGainByQtyPointsActivity {
	// 是否同时审核
	needAudit: Nullable<boolean> = null;
	// 活动信息
	activityBody: Nullable<ActivityBody> = new ActivityBody();
	// 活动时间限制
	dateTimeCondition = new DateTimeCondition();
	// 活动最大次数
	activityMaxTimes: Nullable<number> = null;
	// 会员最大参与次数
	memberMaxGainPointsTimes: Nullable<number> = null;
	// 会员每日最大参与次数
	memberDailyMaxGainPointsTimes: Nullable<number> = null;
	// 规则明细
	lines: GoodsGroupLine[] = [];
	//渠道范围
	channelRange: Nullable<ChannelRange> = null;
	// 参与会员等级
	gradeRange: Nullable<GradesRange> = null;
	// 参与叠加促销
	joinPromotion: Nullable<boolean> = false;
	// 排除优惠商品
	excludeFavourGoodTypes: string[] = []
	// 排除优惠金额
	excludeFavourAmountTypes: string[] = ["PROMOTION","COUPON","MEMBER","OTHER"]
  // 参与人群
  rule: Nullable<PushGroup> = null
}
