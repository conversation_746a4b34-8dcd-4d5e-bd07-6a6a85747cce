/*
 * @Autor: 司浩
 * @Description:
 * @Date: 2023-02-06 15:50:15
 * @LastEditors: 黎钰龙
 * @LastEditTime: 2023-09-19 13:37:55
 * @FilePath: \phoenix-web-ui\src\model\v2\coupon\videonumberActivity\BWeChatVideoActivity.ts
 */
import BaseCouponActivity from 'model/v2/coupon/BaseCouponActivity'
import WeChatVideoCouponBagInfo from 'model/v2/coupon/videonumberActivity/WeChatVideoCouponBagInfo'
import { DateType } from 'model/weixin/weixinIssueCouponActivity/DateType'
import WeiXinBrand from './WeiXinBrand'

// 视频号发券
export default class BWeChatVideoActivity extends BaseCouponActivity {
  // 数量限制日期类型
  limitPartakeDateType: Nullable<DateType> = null
  // 日期类型下，参数次数限制
  limitMemberPerTime: Nullable<number> = null
  // 商品图片最多9张
  goodsImages: string[] = []
  // 商品详情最多10张
  goodsDetailImages: string[] = []
  // 商品描述
  goodsDesc: Nullable<string> = null
  // 商品类目
  goodsCategory1: Nullable<string> = null
  // 商品类目
  goodsCategory2: Nullable<string> = null
  // 商品类目
  goodsCategory3: Nullable<string> = null
  // 售价
  price: Nullable<number> = null
  // 购券须知
  notice: Nullable<string> = null
  // 券包信息
  couponBagInfos: WeChatVideoCouponBagInfo[] = []
  // 操作时间
  create: Nullable<Date> = null
  // 操作人信息
  operator: Nullable<string> = null
  // 活动描述
  activityDesc: Nullable<string> = null
  // 商品参数
  goodsParams: { id: string; name: string }[] = []
  // 审核失败原因
  auditReason: Nullable<string> = null
  // 商品品牌
  goodsBrand: Nullable<WeiXinBrand> = null
}
