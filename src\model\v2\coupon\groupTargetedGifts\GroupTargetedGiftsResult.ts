import IdName from "model/common/IdName";

export default class GroupTargetedGiftsResult {
  // 会员Id
  memberId: Nullable<string> = null
  // 会员号
  crmCode: Nullable<string> = null
  // 会员手机号
  mobile: Nullable<string> = null
  // 实体卡号
  hdCardCardNum: Nullable<string> = null
  // 发放时间
  sendTime: Nullable<Date> = null
  // 发券时间
  issueCouponTime: Nullable<Date> = null
  // 发券门店
  occurredOrg: Nullable<IdName> = null
  // 发券数量
  issueCouponNum: Nullable<number> = null
  // 状态
  state: Nullable<String> = null
  // 失败原因
  failReason: Nullable<string> = null
}