import StorePropValue from 'model/precisionmarketing/tag/tagrule/customize/member/props/StorePropValue'
import TimePropValue from "model/precisionmarketing/tag/tagrule/customize/member/props/TimePropValue";

export default class LastMetricProp {
  // occurredDate-发生时间;orderStdAmount-订单实付金额;store-发生门店
  prop: Nullable<string> = null
  // 距今天数、订单实付金额操作符号
  operator: Nullable<string> = null
  // 距今天数、订单实付金额区间开始
  start: Nullable<number> = null
  // 距今天数、订单实付金额区间结束
  end: Nullable<number> = null
  // 门店条件
  store: Nullable<StorePropValue> = null
  // 发生时间信息
  timeRange: Nullable<TimePropValue> = null
}