<template>
  <div class="member-detail-card"
       :class="{benefit:benefit}">
    <div class="header">
      <img class="bg-icon"
           :src="benefit?require('~assets/image/member/ic_crown_bg_2.png'):require('~assets/image/member/ic_appreciation_bg_2.png')">
      {{ card.name }}
    </div>
    <div class="content">
      <member-form-item :label="i18n('卡状态') + '：'">
        <span class="card-status"
              :style="{color:stateColor}">
          <span class="card-status-bg"
                :style="{'background-color':stateColor}"></span>
          <span class="card-status-border"
                :style="{'border-color':stateColor}"></span>
          {{ stateLabel }}
        </span>
      </member-form-item>
      <member-form-item :label="i18n('卡号') + '：'">
        {{ card.cardNo }}
      </member-form-item>
      <member-form-item :label="i18n('开卡门店') + '：'">
        <template v-if="card.issueOrg && card.issueOrg.id">
        [{{ card.issueOrg.id }}]{{ card.issueOrg.name }}
        </template>
      </member-form-item>
      <member-form-item :label="i18n('有效期') + '：'">
        {{ card.expireStartTime | dateFormate3 }} - {{ card.expireStopTime | dateFormate3 }}
      </member-form-item>
      <member-form-item :label="i18n('开卡时间') + '：'">
        {{ card.openCardTime | dateFormate3 }}
      </member-form-item>
    </div>
  </div>
</template>
<script lang="ts"
        src="./MemberDetailCard.ts">
</script>
<style lang="scss"
       scoped>
.member-detail-card {
  width: calc(50% - 8px);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #D7DFEB;

  .header {
    padding: 9px 12px;
    position: relative;
    font-weight: 600;
    font-size: 14px;
    color: #FFFFFF;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    background: linear-gradient(135deg, #F5E0A6 0%, #D6A65E 100%);

    .bg-icon {
      position: absolute;
      right: 0px;
      top: -12px;
      width: 64px;
      height: 64px;
    }
  }

  .content {
    padding: 12px;
    background: white;

    ::v-deep .member-form-item {
      font-size: 12px;
      line-height: 18px;
    }
  }

  .card-status {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 20px;
    font-size: 12px;
    padding: 0 10px;
    border-radius: 16px;
    position: relative;
    overflow: hidden;

    .card-status-bg {
      display: inline-block;
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      opacity: 0.09;
    }
    .card-status-border {
      display: inline-block;
      position: absolute;
      border-radius: 16px;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      border: 1px solid;
      opacity: 0.4;
    }
  }

  &.benefit {
    .header {
      background: linear-gradient(135deg, #47CEFF 0%, #006DEB 100%);
    }
  }
}
</style>
