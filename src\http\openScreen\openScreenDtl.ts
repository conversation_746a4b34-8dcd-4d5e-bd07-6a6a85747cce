/*
 * @Author: 黎钰龙
 * @Date: 2022-12-01 14:18:12
 * @LastEditTime: 2022-12-01 14:26:19
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\openScreen\openScreenDtl.ts
 * 记得注释
 */
import Response from 'model/common/Response'
import ApiClient from 'http/ApiClient'

export default class NewModifyActivityApi {
  static getDtl(id: any): Promise<Response<string>> {
    return ApiClient.server().get(`/v1/coupon-activity/getWeixinBrandActivity/${id}`, {}).then(res => {
      return res.data
    })
  }
}