<template>
  <div class="upgrade-incentives-dtl">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button size="small" v-if="permissions.editable" @click="modify" type="primary">
          {{ i18n('修改') }}
        </el-button>
        <el-button size="small" v-if="permissions.editable && !anyLoading && data.enable" @click="disable" type="danger">
          {{ i18n('禁用') }}
        </el-button>
        <el-button size="small" v-if="permissions.editable && !anyLoading && !data.enable" @click="enable" type="primary" style="background-color: #52c41a !important;border-color: #52c41a !important;">
          {{ i18n('启用') }}
        </el-button>
      </template>
    </BreadCrume>
    <div style="margin: 20px">
      <div class="tip">
        <el-row style="padding: 15px">
          <el-col :span="2" style="max-width: 40px;text-align: center;line-height: 24px">
            <i class="el-icon-warning"/>
          </el-col>
          <el-col :span="22">
            <p>• 系统将在满足升级激励规则的交易上传之后，实时更新升级为新等级，并从升级当日起计算等级到期日。</p>
            <p>• 升级的新等级有效期到期后，仍将按照累计成长值进行等级评定。</p>
          </el-col>
        </el-row>
      </div>
      <div class="content">
        <el-row>
          <el-col :span="4" style="max-width: 180px;min-width: 180px;color: rgba(51, 51, 51, 0.64);">升级激励规则：</el-col>
          <el-col :span="20">
            <i18n k="/权益/等级/单笔消费升级激励/编辑页/当前等级为{0}的会员，单笔消费满{1}元及以上，可直接升级至{2}">
              <template slot="0">
                &nbsp;<span>{{ gradeMap[data.sourceGrade] ? gradeMap[data.sourceGrade].name : '--' }}</span>&nbsp;
              </template>
              <template slot="1">
                &nbsp;<span style="font-weight: 600">{{ data.amount | amount | nullable}}</span>&nbsp;
              </template>
              <template slot="2">
                &nbsp;<span style="font-weight: 600">{{ gradeMap[data.targetGrade] ? gradeMap[data.targetGrade].name : '--' }}</span>&nbsp;
              </template>
            </i18n>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="4" style="max-width: 180px;min-width: 180px;color: rgba(51, 51, 51, 0.64);">升级后等级有效期：</el-col>
          <el-col :span="20">
            <span style="font-weight: 600">{{ data.validDate | nullable}}</span>&nbsp;<span>天</span>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./UpgradeIncentivesDtl.ts">
</script>

<style lang="scss">
.upgrade-incentives-dtl-red-button {
  border-color: red!important;
  background-color: red!important;
}
.upgrade-incentives-dtl-green-button {
  border-color: green!important;
  background-color: green!important;
}

.upgrade-incentives-dtl {
  width: 100%;
  height: 100%;
  background: white;
  overflow: auto;

  .tip {
    background-color: rgba(51, 102, 255, 0.098);
  }

  .content {
    margin-top: 25px;
    line-height: 40px;
  }
}
</style>