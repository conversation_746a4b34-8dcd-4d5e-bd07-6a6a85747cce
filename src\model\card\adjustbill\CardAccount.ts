/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-04-02 11:16:55
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\card\adjustbill\CardAccount.ts
 * 记得注释
 */
import Account from 'model/card/adjustbill/Account'
import IdName from 'model/common/IdName'

export default class CardAccount {
  // 卡状态:UNACTIVATED-未激活; PRESENTING-转赠中;USING-使用中; CANCELLED-已作废;USED-已使用
  cardState: Nullable<string> = null
  // 卡号
  code: Nullable<string> = null
  // 卡账户类型
  accountType: Nullable<IdName> = null
  // 卡类型
  cardType: Nullable<string> = null
  // 卡类型代码
  cardTypeCode: Nullable<string> = null
  // 是否为充值卡类型
  isImprestCard: Nullable<boolean> = null
  // 卡账户信息
  account: Nullable<Account> = null
  // 会员所属组织
  ownStore: Nullable<IdName> = null
  // 次数
  totalTimes: Nullable<number> = null
  // 剩余次数
  remainderTimes: Nullable<number> = null
}