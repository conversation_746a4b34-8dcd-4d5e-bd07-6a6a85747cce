import ApiClient from 'http/ApiClient'
import OrgCardSetting from 'model/prepay/prepaycardStore/OrgCardSetting'
import OrgCardSettingCreateRequest from 'model/prepay/prepaycardStore/OrgCardSettingCreateRequest'
import OrgCardSettingFilter from 'model/prepay/prepaycardStore/OrgCardSettingFilter'
import OrgCardSettingModifyRequest from 'model/prepay/prepaycardStore/OrgCardSettingModifyRequest'
import Response from "model/common/Response";

export default class OrgCardSettingApi {
  /**
   * 新建门店预付卡设置
   * 新建门店预付卡设置。
   * 
   */
  static create(body: OrgCardSettingCreateRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/orgCard/setting/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入门店预付卡设置
   * 导入门店预付卡设置。
   * 
   */
  static importConfig(body: any): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/orgCard/setting/importConfig`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改门店预付卡设置
   * 修改门店预付卡设置。
   * 
   */
  static modify(body: OrgCardSettingModifyRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/orgCard/setting/modify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询门店预付卡设置
   * 查询门店预付卡设置。
   * 
   */
  static query(body: OrgCardSettingFilter): Promise<Response<OrgCardSetting[]>> {
    return ApiClient.server().post(`/v1/orgCard/setting/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
