/*
 * @Author: 黎钰龙
 * @Date: 2024-03-05 14:14:02
 * @LastEditTime: 2024-05-31 10:16:33
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\selectEmployees\selectEmployees.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import EmployeeApi from 'http/employee/EmployeeApi';
import IdName from 'model/common/IdName';
import RSEmployee from 'model/common/RSEmployee';
import RSEmployeeFilter from 'model/common/RSEmployeeFilter';
import { Component, Model, Prop, Vue, Watch } from 'vue-property-decorator';
@Component({
  name: 'SelectEmployees',
  components: {}
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/公用/下拉框/提示'
  ],
  auto: true
})
export default class SelectEmployees extends Vue {
  @Model('change') selectEmployeeIdName: IdName | string
  @Prop({ type: String, default: '400px' }) width: string;
  @Prop() placeholder: any;
  @Prop({ type: Boolean, default: false }) disabled: boolean;
  @Prop({ default: () => { return {} } }) appendAttr: any; //查询员工列表时，需要追加的参数
  selectLoading: boolean = false
  employees: RSEmployee[] = []  //员工列表

  @Watch('selectEmployee', { deep: true })
  handle(value: any) {
    if (!value) {
      this.getList('')
    }
  }

  get selectEmployee() {
    return this.selectEmployeeIdName
  }
  set selectEmployee(value: any) {
    const res = value ?? null
    this.$emit('change', res)
  }

  created() {
    this.getList()
  }
  doRemoteMethod(value: string) {
    this.getList(value);
  }
  getList(value?: string) {
    let params: RSEmployeeFilter = new RSEmployeeFilter();
    params.idNameLikes = value ?? null;
    params.page = 0;
    params.pageSize = 200;
    params = { ...params, ...this.appendAttr }
    this.selectLoading = true
    EmployeeApi.query(params)
      .then((resp: any) => {
        if (resp?.code === 2000) {
          this.employees = resp.data;
        } else {
          throw new Error(resp.msg || this.i18n('查询员工列表失败'))
        }
      })
      .catch((error) => {
        this.$message.error(error.message || this.i18n('内部异常'));
      }).finally(() => this.selectLoading = false)
  }
};