<template>
  <div class="all-cash-coupon">
    <el-form :model="ruleForm" :rules="rules" class="demo-ruleForm" label-width="120px" ref="ruleForm">
      <div v-if="$route.query.from === 'edit' && !wxScanForCoupon" style="
          height: 48px;
          line-height: 48px;
          background-color: #3366ff19;
          margin: 0 20px;
          padding-left: 20px;
          margin-bottom: 10px;
        ">
        <img style="position: relative; top: 5px" src="~assets/image/auth/info3.png" alt="" />{{
          formatI18n(
            "/权益/券/券模板/编辑界面/用券规则下的提示/券模板修改后，已发出的券的有效期仍以原来的为准，其余用券规则以修改后的为准。"
          )
        }}
      </div>
      <div class="setting-container">
        <div class="setting-block">
          <div class="section-title">{{i18n('基础信息')}}</div>
          <!-- 券名称 -->
          <CouponName :ruleForm="ruleForm" :copyFlag='copyFlag'>
            <template slot="slot">
              <el-input maxlength="128" style="width: 390px" v-model="ruleForm.name" @change="doFormItemChange" :placeholder="i18n('请输入')"></el-input>
            </template>
          </CouponName>
          <!-- 券图标 -->
          <CouponTemplateLogo :original-logo-url="ruleForm.logoUrl" @logoUrlCallBack="logoUrlCallBack"></CouponTemplateLogo>
          <!-- 使用须知 -->
          <UseCouponDesc :ruleForm="ruleForm" :isShowTips="true">
            <template slot="slot">
              <el-input :maxlength="remarkMaxlength" style="width: 390px;" type="textarea" v-model="ruleForm.couponProduct"
                :placeholder="remarkPlaceholder" @change="doFormItemChange">
              </el-input>
            </template>
          </UseCouponDesc>
        </div>

        <div class="setting-block">
          <div class="section-title">{{i18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/优惠设置')}}</div>
          <!-- 折扣力度 -->
          <el-form-item :label="formatI18n('/公用/券模板/商品折扣券', '折扣力度')" prop="discount">
            <div v-if="copyFlag === 'edit'">
              {{ getDiscountRate(ruleForm.discount) }}
            </div>
            <div v-else>
              <div style="color: #79879E">
                -{{formatI18n("/公用/券模板/商品折扣券/折扣力度后边的文案","请填写0~9.9之间的数字，精确到小数点后1位")}}
              </div>
              <div class="input-box">
                <el-input @change="doDiscount" style="width: 120px" v-model="ruleForm.discount" :placeholder="formatI18n('/营销/券礼包活动/核销第三方券/请输入')">
                  <template slot="append">{{formatI18n("/公用/券模板/商品折扣券/折扣力度/折")}}</template>
                </el-input>
                &nbsp;&nbsp;{{ formatI18n("/公用/券模板详情/最多保留1位小数") }}
              </div>
            </div>
          </el-form-item>
          <!-- 优惠上限 -->
          <el-form-item :label="formatI18n('/公用/券模板/商品折扣券', '优惠上限')">
            <!-- <div v-if="copyFlag === 'edit' && ruleForm.state === 'EFFECTED'">
              {{ getAmount(ruleForm.discountAmount, ruleForm.discountQty) }}
            </div> -->
            <div>
              <el-radio-group v-model="ruleForm.discountType" class="discount-max" @change="discountTypeChange">
                <el-radio label="noLimit">
                  {{formatI18n("/公用/券模板/商品折扣券/优惠上限", "无限制")}}
                </el-radio><br />
                <el-radio label="amount" v-if="ruleForm.type.useThresholdType == 'AMOUNT'">
                  {{ formatI18n("/公用/券模板/商品折扣券/优惠上限", "最多优惠") }}
                  <el-form-item prop="discountLimit" style="display: inline-block">
                    <el-input @change="doDiscountAmountChange" style="width: 100px" v-model="ruleForm.discountAmount" maxlength="11"
                      :disabled="ruleForm.discountType !== 'amount'">
                      <template slot="append">{{ formatI18n("/公用/券模板", "元") }}</template>
                    </el-input>
                  </el-form-item>
                </el-radio>
                <el-radio label="qty" v-if="ruleForm.type.useThresholdType == 'QTY'">
                  {{ formatI18n("/公用/券模板/商品折扣券/优惠上限", "最多") }}
                  <el-form-item prop="discountLimit1" style="display: inline-block">
                    <el-input @change="doDiscountQtyChange" style="width: 100px" v-model="ruleForm.discountQty" maxlength="11"
                      :disabled="ruleForm.discountType !== 'qty'">
                    </el-input>
                  </el-form-item>
                  &nbsp;&nbsp;{{formatI18n("/公用/券模板/商品折扣券/优惠上限", "件享受折扣")}}
                </el-radio>
              </el-radio-group>
            </div>
          </el-form-item>
          <!--用券门槛-->
          <el-form-item class="coupon-step" :label="formatI18n('/公用/券模板', '用券门槛')">
            <UseCouponStep @change="doStepChange" ref="useCouponStep" v-model="ruleForm.type" @limitChange="limitChange"
              :type="formatI18n('/公用/券模板', '商品现金券')">
            </UseCouponStep>
          </el-form-item>
          <!-- 券承担方 -->
          <el-form-item :label="formatI18n('/公用/券模板', '券承担方')">
            <CouponBear ref="CouponBear" :state="curState" v-model="ruleForm.couponUnder" @change="doFormItemChange"></CouponBear>
          </el-form-item>
          <!-- 用券记录方式 -->
          <RecordWay ref="recordWay" :copyFlag="copyFlag" v-model="ruleForm" :baseFieldEditable="baseFieldEditable" labelWidth="120px"
            :specialGoods="specialGoods" @change="doFormItemChange" @changeSpecialGoods="changeSpecialGoods">
          </RecordWay>
          <!-- 叠加用券-->
          <GroupMutexTemplate :isDiscount="true" :showCurrentCoupon="showCurrentCoupon" ref="allDiscountMutexTemplate" v-model="ruleForm.groupMutex" @change="doFormItemChange" @checkLimit="checkLimit">
          </GroupMutexTemplate>
          <!-- 叠加促销 -->
          <StackPromotion v-model="ruleForm.promotionInfo" :copyFlag="copyFlag" :templateId="ruleForm.templateId" @change="doFormItemChange"
            ref="stackPromotion" />
        </div>

        <div class="setting-block">
          <div class="section-title">{{i18n('/营销/券礼包活动/券查询/用券时间')}}</div>
          <!-- 券有效期 -->
          <CouponEffectPeriod ref="couponEffectPeriod" v-model="ruleForm" :options="options" @change="doFormItemChange"></CouponEffectPeriod>
          <!-- 用券时段 -->
          <el-form-item :label="formatI18n('/公用/券模板', '用券时段')" class="cur-form-item">
            <div style="display:flex">
              <span style="margin-right:8px">{{i18n('券可用时间内')}}</span>
              <TimeRange :fixedAll="options.fixedTime" @change="doFormItemChange" v-model="ruleForm.time" ref="timeRange"> </TimeRange>
            </div>
          </el-form-item>
        </div>

        <div class="setting-block">
          <div class="section-title">{{i18n('用券范围')}}</div>
          <!-- 用券渠道 -->
          <el-form-item :label="formatI18n('/公用/券模板', '用券渠道')" class="cur-from-item" required>
            <el-radio-group @change="doUseFromChange" v-model="ruleForm.useFrom">
              <el-radio label="step2" style="display: block">
                <span>{{formatI18n("/公用/券模板/用券渠道", "指定渠道适用")}}</span>
                <div style="display:inline-block;margin-left:10px">
                  <el-form-item class="cur-from-item" prop="useFrom">
                    <el-select style="width: 250px" :disabled="ruleForm.useFrom === 'step1'" multiple @change="doFromChange" v-model="ruleForm.from"
                      :placeholder="formatI18n('/公用/券模板/用券渠道', '请至少选择一个渠道')">
                      <el-option v-for="(item, index) in channels" :key="'channel' + index" :label="item.name"
                        :value="item.channel.typeId">{{ item.name }}
                      </el-option>
                    </el-select>
                  </el-form-item>
                </div>
              </el-radio>
              <el-radio label="step1">
                {{ formatI18n("/公用/券模板/用券渠道", "全部渠道") }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <!-- 用券门店 -->
          <el-form-item :label="formatI18n('/公用/券模板', '用券门店')" prop="storeRange">
            <ActiveStore ref="activeStore" :isOldActivity="false" :sameStore="sameStore" :enableStore="enableStore" v-model="ruleForm.storeRange" @change="doStoreChange">
            </ActiveStore>
          </el-form-item>
          <!-- 用券商品 -->
          <el-form-item :label="formatI18n('/公用/券模板', '用券商品')" class="cur-form-item" prop="goodsScope">
            <GoodsScopeEx ref="goodsScope" :importNumber="20000" v-model="ruleForm.useCouponGood" :goodsMatchRuleMode="goodsMatchRuleMode" @change="doFormItemChange" type="discount">
            </GoodsScopeEx>
          </el-form-item>
        </div>
          <!-- 用券限量 -->
          <div class="setting-block" v-if="!weixinCouponHideFiled && isPmsPayEngine" >
           <div class="section-title">{{formatI18n('/公用/券模板','用券限量')}}</div>
            <el-form-item :label="formatI18n('/公用/券模板','用券限量')">
              <div>
                <span>{{formatI18n('/公用/券模板','每人每天限用')}}&nbsp;&nbsp; </span>
                <el-form-item class="no-margin" prop="limit" style="display: inline-block;">
                  <AutoFixInput :min="1" :max="99" :fixed="0" style="width: 50px" v-model="ruleForm.maxDailyMemberQuotaQty"></AutoFixInput>
                </el-form-item>
                <span>&nbsp;{{formatI18n('/公用/券模板', '张')}}。</span>    <span style="color: #79879E">{{formatI18n('/公用/券模板','不填则不限量')}}</span>
              </div> 
            </el-form-item>
        </div>
      </div>

      <div class="setting-container">
        <div class="setting-block">
          <div class="section-title">
            <span>{{i18n('高级设置')}}</span>
            <span class="telescoping" @click="telescopingChange">
              <template v-if="!telescoping">{{i18n('/公用/查询条件/收起')}}<i class="el-icon-arrow-up"></i></template>
              <template v-else>{{i18n('/公用/查询条件/展开')}}<i class="el-icon-arrow-down"></i></template>
            </span>
            <span class="gray-tips" style="margin-left:12px">{{i18n('券码生成规则、能否转赠、标签、同步渠道等')}}</span>
          </div>
          <div v-show="!telescoping">
            <!-- 券码生成规则 -->
            <CouponCodeRules :ruleForm="ruleForm" :copyFlag="copyFlag" :wxScanForCoupon="wxScanForCoupon">
              <template slot="slot">
                <el-input :placeholder="formatI18n('/营销/券礼包活动/券礼包活动/新建导出券码发券', '请输入6位以内的数字或字母')" maxlength="6" style="width: 325px"
                  v-model="ruleForm.prefix" @change="doFormItemChange">
                </el-input>
              </template>
            </CouponCodeRules>
            <!-- 外部券模板号 -->
            <OuterCouponTemCode v-if="showOuterRelationsConfig && hideOptions.outerTemplateCode" ref="outerCouponTemCode" :copyFlag="copyFlag"
              v-model="ruleForm.outerRelations" @change="doOuterTemplateChange">
            </OuterCouponTemCode>
            <!-- 能否转赠 -->
            <el-form-item :label="formatI18n('/公用/券模板', '能否转赠')">
              <el-radio-group v-model="ruleForm.transferable" @change="doFormItemChange">
                <el-radio :label="true">
                  {{formatI18n("/公用/券模板", "是")}}
                </el-radio>
                <el-radio :label="false">
                  {{formatI18n("/公用/券模板", "否")}}
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <!-- 标签 -->
            <CouponTemplateLabel v-if="hideOptions.tags" class="cur-from-item" v-model="ruleForm.templateTag" :templateId="ruleForm.templateId"
              @change="doFormItemChange" />
            <!-- 同步渠道 -->
            <el-form-item :label="formatI18n('/公用/券模板', '同步渠道')" class="cur-from-item" v-if="!wxScanForCoupon && sychChannels.length > 0">
              <div class="gray-tips">{{formatI18n('/公用/券模板/用于控制当前券模板同步给哪些渠道')}}</div>
              <el-form-item :label="formatI18n('/权益/券/券模板/微盟')" label-width="auto" v-if="weimobChannels.length > 0">
                <el-checkbox-group v-model="ruleForm.weimobId" :disabled="disabledEdit || state === 'NOT_EFFECTED'" @change="doSynChannelChange">
                  <el-checkbox v-for="item in weimobChannels" :key="channelId(item.channel)"
                    :label="channelId(item.channel)">{{ `[${item.channel.id}]${item.name}` }}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item :label="formatI18n('/权益/券/券模板/REX')" label-width="auto" v-if="rexChannels.length > 0">
                <el-checkbox-group v-model="ruleForm.rexId" :disabled="disabledEdit || state === 'NOT_EFFECTED'" @change="doSynChannelChange">
                  <el-checkbox v-for="item in rexChannels" :key="channelId(item.channel)"
                    :label="channelId(item.channel)">{{ `[${item.channel.id}]${item.name}` }}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-form-item>
            <!-- 价格 -->
            <el-form-item :label="i18n('/公用/券模板/价格')">
              <AutoFixInput :min="0.01" :max="99999.99" :fixed="2" @change="doFormItemChange" v-model="ruleForm.price" style="width: 148px"
                :appendTitle="formatI18n('/券/购券管理','元')" />
            </el-form-item>
            <!-- 账款项目 -->
            <el-form-item :label="i18n('账款项目')" prop="termsModel">
              <div v-if="queryCostParyRange=='cost_party'">
                <SelectCostParty v-model="ruleForm.termsModel" :isOnlyId="true" :hideAll="true" width="20%" :placeholder="formatI18n('/公用/下拉框/提示', '请选择')" >
                </SelectCostParty>
              </div>
              <el-input v-if="queryCostParyRange=='customize'" :placeholder="i18n('/储值/预付卡/卡模板/详情页面/请输入{0}位以内的数字或字母',['128'])" maxlength="128" style="width: 325px"
                v-model="ruleForm.termsModel" @change="doFormItemChange">
              </el-input>
            </el-form-item>

            <!-- C端我的券角标 -->
            <el-form-item :label="formatI18n('/公用/券模板', 'C端我的券角标')">
              <el-radio-group v-model="ruleForm.couponSubscriptType" @change="doFormItemChange">
                <el-radio :label="'COMMON'">{{ formatI18n("/公用/券模板", "通用券") }}</el-radio>
                <el-radio :label="'ONLINE'">{{ formatI18n("/公用/券模板", "线上券") }}</el-radio>
                <el-radio :label="'OFFLINE'">{{ formatI18n("/公用/券模板", "线下券") }}</el-radio>
                <el-radio :label="'OUTSIDE'">{{ formatI18n("/公用/券模板", "外部券") }}</el-radio>
              </el-radio-group>
            </el-form-item>
           <!-- 备注 -->
            <el-form-item  v-if="!weixinCouponHideFiled"  :label="formatI18n('/公用/券模板', '备注')">
              <el-input v-model="ruleForm.notes" type="textarea" :placeholder="formatI18n('/公用/券模板','请输入不超过1000个字')" :rows="10"
                        maxlength="1000" style="width: 500px;" @change="doFormItemChange"/>
            </el-form-item>
            <!-- 核销链接 -->
            <el-form-item :label="formatI18n('/公用/券模板', '核销链接')">
              <div class="gray-tips">{{formatI18n('/公用/券模板/外部对接使用，比如客户自研小程序上点击用券跳转自定义链接去核销券')}}</div>
              <el-input v-model="ruleForm.writeOffLink" type="textarea" :placeholder="formatI18n('/储值/会员储值/储值充值活动/编辑页面','请输入不超过500个字')" :rows="10"
                        maxlength="500" style="width: 500px;" @change="doFormItemChange"/>
            </el-form-item>
          </div>
        </div>
      </div>

      <div class="setting-container" v-if="hasWeimobChannel">
        <WeimobCouponInfo ref="weimobCouponInfo" :disabled="disabledEdit" :autoCheckall="true" v-model="ruleForm.weimobCouponAndTotal"
          @change="weimobChange" />
      </div>

      <div class="setting-container" v-if="hasRexChannel">
        <RexCouponInfo ref="rexCouponInfo" v-model="ruleForm.rexCoupon" @change="rexChange" />
      </div>
    </el-form>
    <!-- 设置特殊商品 -->
    <SelectGoodsDialog ref="SelectGoodsDialog" :title="i18n('设置')" @submit="specialGoodsSubmit"></SelectGoodsDialog>
    <!-- 查看特殊商品 -->
    <SpecialGoodsDialog ref="SpecialGoodsDialog" :data="specialGoods"></SpecialGoodsDialog>
  </div>
</template>

<script lang="ts" src="./DiscountCoupon.ts"></script>

<style lang="scss">
.all-cash-coupon {
  padding-bottom: 30px;
  .setting-container {
    .setting-block {
      .section-title {
        .telescoping {
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #007eff;
          margin-left: 12px;
          cursor: pointer;
          i {
            color: #242633;
          }
        }
      }
    }
  }

  .coupon-step,
  .cur-form-item {
    .el-form-item__label {
      &:before {
        content: "*";
        color: #ef393f;
        margin-right: 4px;
      }
    }
  }

  .discount-max {
    .el-radio {
      line-height: 50px;
    }
  }
  .cur-from-item {
    height: auto !important;

    .el-radio {
      height: auto !important;
      line-height: 40px;
    }
  }
}
</style>
