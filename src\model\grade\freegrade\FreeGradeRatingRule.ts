import FreeGradeUpgradeRule from "model/grade/freegrade/FreeGradeUpgradeRule";

export default class FreeGradeRatingRule {
	// 等级有效期
	validMonth: Nullable<number> = null;
	// 评定日期,每月第几天
	ratingDay: number = 0;
	// 评定周期:每天:EVERY_DAY;每月: EVERY_MONTH
	ratingCycle: Nullable<string> = null;
	// 降级规则:降为下一等级:DOWN_ONE_GRADE;重新评定: RE_RATING
	downRule: Nullable<string> = null;
	// 升级规则
	upgradeRules: FreeGradeUpgradeRule[] = [];
}
