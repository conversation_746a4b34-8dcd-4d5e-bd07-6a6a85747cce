/*
 * @Author: 黎钰龙
 * @Date: 2025-05-28 14:11:43
 * @LastEditTime: 2025-06-12 10:24:30
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\systemConfig\PrepayCardConfig.ts
 * 记得注释
 */
import { BalancePayType } from "./BalancePayType"

export default class PrepayCardConfig {
  // 是否开启卡锁定
  enableCardLock: Nullable<boolean> = false
  // 卡密码输入错误次数
  passwordErrorTimes: Nullable<number> = null
  // 锁定分钟
  lockMinute: Nullable<number> = null
  // 卡号密码输入方式
  cardPasswordInputType: Nullable<string> = null
  // 是否露出绑卡入口
  showBindCard: Nullable<boolean> = true
  // 储值支付模式
  balancePayType: BalancePayType = BalancePayType.onlyMemberBalance
  // 前台能否手动输入静态码
  supportInputCardCode: Nullable<boolean> = false
  // 允许售卖非本组织预付卡
  allowSaleOtherOrgCard: Nullable<boolean> = false
  // 是否露出礼品卡转储值
  showGiftCardTransfer: Nullable<boolean> = true
  // 是否露出储值卡转储值
  showRechargeCardTransfer: Nullable<boolean> = true
}