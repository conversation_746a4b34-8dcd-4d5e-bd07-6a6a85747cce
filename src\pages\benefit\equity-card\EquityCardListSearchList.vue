<template>
  <div class="main">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button @click="exportFile" v-if="permission.exportable">
          批量导出
        </el-button>
      </template>
    </BreadCrume>
    <div style="flex: 1; overflow: auto" class="current-page-parent" v-loading="tableLoading">
      <div class="current-page">
        <ListWrapper style="overflow: hidden">
          <template slot="query">
            <el-form label-width="100px" class="queryForm label-inline">
              <el-row>
                <el-col :span="8">
                  <el-form-item label="会员信息">
                    <el-input v-model="query.memberCodeLikes" placeholder="请输入手机号/会员号/实体卡号" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="发卡门店">
                    <SelectStores v-model="query.issueOrgIdEquals" :isOnlyId="true" :hideAll="false" width="100%"
                      :placeholder="i18n('请输入发卡门店')">
                    </SelectStores>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="权益卡名称">
                    <el-input v-model="query.nameLikes" placeholder="请输入" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="权益卡号">
                    <el-input v-model="query.codeEquals" placeholder="请输入" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="开卡时间">
                    <el-date-picker v-model="selectDate" style="width: 100%" end-placeholder="结束时间" format="yyyy-MM-dd" range-separator="-"
                      :clearable="false" ref="selectDate" size="small" start-placeholder="开始时间" type="daterange" value-format="yyyy-MM-dd">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-form-item>
                  <el-button type="primary" @click="doSearch">查询</el-button>
                  <el-button @click="doReset">重置</el-button>
                </el-form-item>
              </el-row>
            </el-form>
          </template>
          <template slot="list">
            <el-row class="row" v-if="permission.voidable">
              <span>{{ getActiveCount(selected.length) }}</span>
              <el-button key="2" @click="doBatchVoid" style="margin-left: 14px;">批量作废</el-button>
            </el-row>
            <el-row class="row">
              <el-table class="table" :data="tableData" row-key="code" style="width: 100%; margin-top: 20px;" ref="table"
                @selection-change="handleSelectionChange">
                <el-table-column v-if="permission.voidable" fixed type="selection" reserve-selection width="55" />
                <el-table-column label="所属会员" width="260">
                  <template v-slot="{ row }">
                    {{ row.mobile || row.crmCode || '--' }}
                  </template>
                </el-table-column>
                <el-table-column label="权益卡状态" width="120">
                  <template v-slot="{ row }">
                    {{ EquityCardStateMap[row.state] }}
                  </template>
                </el-table-column>
                <el-table-column label="发卡门店" width="150">
                  <template v-slot="{ row }">
                    {{ `[${row.issueOrgId || '--'}]${row.issueOrgName || '--'}` }}
                  </template>
                </el-table-column>
                <el-table-column label="开卡时间" width="150">
                  <template v-slot="{ row }">
                    {{ row.issueTime | dateFormate3 }}
                  </template>
                </el-table-column>
                <el-table-column label="权益卡号" width="260">
                  <template v-slot="{ row }">
                    {{ row.code || '--' }}
                  </template>
                </el-table-column>
                <el-table-column label="权益卡名称" width="150">
                  <template v-slot="{ row }">
                    {{ row.name || '--' }}
                  </template>
                </el-table-column>
                <el-table-column label="权益卡有效期" width="300">
                  <template v-slot="{ row }">
                    {{ row.beginTimeInclusive | dateFormate3 }} - {{ row.endTimeExclusive | dateFormate3 }}
                  </template>
                </el-table-column>
                <el-table-column label="交易号" width="260">
                  <template v-slot="{ row }">
                    {{ row.issueTransIdId || '--' }}
                  </template>
                </el-table-column>
                <el-table-column fixed="right" label="操作" width="110" v-if="permission.voidable">
                  <template v-slot="{ row }">
                    <el-button v-if="row.state !== EquityCardState.CANCEL" type="text" @click="doVoid(row)">作废</el-button>
                    <span v-else>--</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-row>
          </template>
          <template slot="page">
            <el-pagination :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
              @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background layout="total, prev, pager, next, sizes,  jumper"
              class="pagin">
            </el-pagination>
          </template>
        </ListWrapper>
      </div>
    </div>
    <DownloadCenterDialog :dialogvisiable="fileDialogVisible" :showTip="showTip" @dialogClose="doDownloadDialogClose">
    </DownloadCenterDialog>
  </div>
</template>

<script lang='ts' src="./EquityCardListSearchList.ts"></script>

<style scoped lang='scss'>
.main {
  height: 100%;
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
</style>