/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-10-08 11:22:52
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\precisionmarketing\pushplan\PushGroup.ts
 * 记得注释
 */
import MemberRule from "model/precisionmarketing/tag/tagrule/customize/member/MemberRule";
import IdName from "model/common/IdName";
import ParticipateMember from "model/member/ParticipateMember";

export default class PushGroup {
  // USER_GROUP_AND_MEMBER_TAG：按客群和会员标签、USER_GROUP：人群
  type: Nullable<string> = null
  // 
  memberRule: Nullable<MemberRule> = null
  // 
  userGroupRule: Nullable<IdName> = null
  // type=USER_GROUP_AND_MEMBER_TAG时，传入的参数
  participateMember: Nullable<ParticipateMember> = null
}