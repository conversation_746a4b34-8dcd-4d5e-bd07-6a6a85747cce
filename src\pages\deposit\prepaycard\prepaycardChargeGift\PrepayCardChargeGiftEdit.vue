<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:05
 * @LastEditTime: 2024-09-29 15:05:55
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\prepaycard\prepaycardChargeGift\PrepayCardChargeGiftEdit.vue
 * 记得注释
-->
<template>
  <div class="prepay-card-charge-gift-edit">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button key="1" v-if="permission.editable" @click="doSave(null)" type="primary">保存</el-button>
        <el-button key="2" v-if="permission.editable && permission.auditable" @click="doSaveAudit" type="primary">
          保存并审核
        </el-button>
        <el-button key="3" @click="doCancel">取消</el-button>
      </template>
    </BreadCrume>
    <div class="current-page">
      <el-form :model="form.data" :rules="form.rules" ref="form" label-width="150px">
        <div class="panel">
          <div class="header">活动信息</div>
          <div class="content">
            <el-form-item label="活动名称" prop="name">
              <el-input maxlength="80" placeholder="请输入不超过80个字符" style="width: 350px" v-model="form.data.name" :disabled="editable === false">
              </el-input>
            </el-form-item>
            <el-form-item label="所属主题" prop="topicCode">
              <el-select clearable placeholder="请选择" style="width: 350px" v-model="form.data.topicCode">
                <el-option no-i18n :label="item.name" :value="item.code" v-for="item in themes" :key="item.code">{{ item.name }}
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="活动时间" :required="true">
              <ActivityDateTimeConditionPicker v-model="form.data.dateTimeCondition" ref="activityDateTimeConditionPicker">
              </ActivityDateTimeConditionPicker>
            </el-form-item>
            <el-form-item label="活动门店" :required="true">
              <ActiveStore no-i18n ref="storeScope" :enableStore="true" :sameStore="false" v-model="form.data.stores" />
            </el-form-item>
          </div>
        </div>

        <div class="split"></div>

        <div class="panel">
          <div class="header">{{locale === 'en_US'?"Recharge gift rules":i18n('充值规则')}}</div>
          <div class="content">
            <el-form-item label="适用卡模板" :required="true" prop="cardTemplates">
              <el-button type="primary" @click="
                  $refs.cardTemplateSelectorDialog.open(
                    JSON.parse(JSON.stringify(prevSelectedCardTemplates))
                  )
                " :disabled="editable === false">
                选择 </el-button>&nbsp;&nbsp;&nbsp;&nbsp;
              <span style="color: #8f8f8f"><i class="el-icon-warning" />&nbsp;{{
                  i18n("只可选择储值卡")
                }}</span>
              <el-row v-for="(item, index) of form.data.cardTemplates" :key="item.id">
                <el-button type="text" no-i18n @click="gotoTplDtl(item.id)">{{
                  item.name
                }}</el-button>&nbsp;&nbsp;
                <i v-if="editable === true" class="el-icon-close" @click="
                    form.data.cardTemplates.splice(index, 1);
                    prevSelectedCardTemplates.splice(index, 1);
                  "></i>
              </el-row>
            </el-form-item>
            <el-form-item label="赠礼规则" :required="true">
              <el-tooltip placement="right" effect="light">
                <i class="el-icon-warning" />
                <div style="color: #8f8f8f; line-height: 40px" slot="content">
                  <div style="margin-left: 17px" v-if="locale === 'zh_CN'">
                    {{
                      i18n(
                        "比如充值满100元享受9折，返现不填，每充值1元送1积分， 则顾客充值100元（不录入会员），只需支付90元，卡余额增加100元；顾客充值100元（录入会员），只需支付90元，卡余额增加100元，赠送90积分给会员"
                      )
                    }}
                  </div>
                  <div style="margin-left: 17px" v-else>
                    For example, if you recharge 100 yuan or more, you can enjoy a 10% discount. If you do not fill in the cash return, you can get 1
                    point for every 1 yuan you recharge. If you recharge 100 yuan (no member is entered), you only need to pay 90 yuan, and the card
                    balance will increase by 100 yuan; Customers recharge 100 yuan (enter the member), only need to pay 90 yuan, the card balance
                    increases by 100 yuan, and 90 points will be given to the member
                  </div>
                  <div style="margin-left: 17px">
                    {{
                      i18n(
                        "充值满100元，折扣不填，返现10元，每充值1元送1积分，则顾客充值100元（不录入会员），需支付100元，卡余额增加110元；顾客充值100元（录入会员），需支付100元，卡余额增加100元，返现10元给会员储值账户，赠送100积分给会员"
                      )
                    }}
                  </div>
                  <div style="margin-left: 17px">
                    {{
                      i18n(
                        "充值满100元，享受9折，返现2元，每充值1元送1积分，则顾客充值100元（不录入会员），只需支付90元，卡余额增加102元；顾客充值100元（录入会员），只需支付90元，卡余额增加100元，返现2元给会员储值账户，赠送90积分给会员"
                      )
                    }}
                  </div>
                  <div style="margin-left: 17px">
                    {{
                      i18n(
                        "充值满100元，享受9折，返现4%（4%是按照实充金额算），每充值1元送1积分，则顾客充值100元（不录入会员），只需支付90元，卡余额增加103.6元；顾客充值100元（录入会员），只需支付90元，卡余额增加100元，返现3.6元给会员储值账户，赠送90积分给会员"
                      )
                    }}
                  </div>
                </div>
              </el-tooltip>
              <div v-for="(item, index) in form.data.lines" :key="index">
                <div class="send-gift">
                  <div class="discount">
                    {{i18n('充值满')}}&nbsp;&nbsp;
                    <el-form-item class="line-form-item" :prop="'lines.'+ index + '.faceAmount'" :rules="customRules">
                      <AutoFixInput size="small" class="short-input" :min="0.01" :max="999999.99" :fixed="2" v-model="item.faceAmount" />
                    </el-form-item>
                    &nbsp;&nbsp;{{i18n('元')}}
                    <div v-if="showPrepayCardChargeGiftActivityDiscount">
                      ，{{i18n('享受')}}&nbsp;&nbsp;
                      <el-form-item class="line-form-item" :prop="'lines.'+ index + '.discount'" :rules="customDiscountRule(index)">
                        <AutoFixInput size="small" class="short-input" :min="0.1" :max="9.9" :fixed="1" v-model="item.discount" />
                      </el-form-item>
                      &nbsp;&nbsp;{{i18n('折')}}
                      <span style="color: #8f8f8f">{{i18n('不填不享受折扣')}}</span>
                    </div>
                    <div v-else>
                      ，{{i18n('赠送返现')}}
                      <el-form-item v-if="form.data.rebateType === 'amount'" class="line-form-item" :prop="'lines.'+ index + '.rebateAmount'"
                                    :rules="customAmountRules(index)">
                        <AutoFixInput size="small" class="short-input" :min="0.01" :max="999999.99" :fixed="2"
                                      v-model="item.rebateAmount" />
                      </el-form-item>
                      <el-form-item v-if="form.data.rebateType === 'percentage'" class="line-form-item" :prop="'lines.'+ index + '.rebatePercentage'"
                                    :rules="customPerRules(index)">
                        <AutoFixInput  size="small" class="short-input" :min="1" :max="100" :fixed="0"
                                      v-model="item.rebatePercentage" />
                      </el-form-item>
                      &nbsp;&nbsp;
                      <!-- {{i18n('元')}} -->
                      <el-select v-model="form.data.rebateType" style="width: 80px" @change="rebateTypeChange">
                        <el-option :label="i18n('元')" value="amount"></el-option>
                        <el-option label="%" value="percentage"></el-option>
                      </el-select>
                      <span style="color: #8f8f8f" v-if="form.data.rebateType === 'amount'">{{ i18n('不填不返现') }}</span>
                      <span style="color: #8f8f8f"
                            v-if="form.data.rebateType === 'percentage'">{{locale === 'zh_CN'? i18n('不填不赠金, %是指本金金额%'): 'No cash back if not filled in,% refers to the paid in amount%' }}</span>
                    </div>
                  </div>
                  <el-button type="text" style="margin-left: 20px; margin-top: 6px" @click="removeLine(index)">移除</el-button>
                </div>
                <el-form-item label="赠礼设置">
                  <div>

<!--                  <span @click="doAddCoupon(index)" class="span-btn" v-if="giftData[index].coupon.length <= 0">-->
                    <span @click="doAddCoupon(index)" class="span-btn" v-if="giftData[index].coupon.length <= 0">
                    {{formatI18n('/会员/等级/等级管理/未初始化状态/点击立即开始免费等级初始化/输入所有必填项点击下一步/然后输入所有必填项点击下一步/等级月礼下的表格', '添加券')}}
                  </span>
                    <ActiveAddCoupon :baseSettingFlag="true" :state="state" ref="addCoupon" v-model="giftData[index].coupon"></ActiveAddCoupon>

                  </div>
<!--          <div class="margin-bottom-10" v-for="(sub, subIndex) in receiveParamsArray[index]" :key="subIndex">-->
<!--            <el-checkbox :disabled="disabled" v-if="subIndex === 0" v-model="selecteArray[index][2]" @change="doSendCouponChange(index,$event)"></el-checkbox>-->
<!--            <i18n advance k="/储值/会员储值/储值充值活动/编辑页面/赠优惠券{0}张">-->
<!--              <template slot-scope="{ items }">-->
<!--            <span :style="{ marginLeft: subIndex === 0 ? '0px' : '48px' }">{{-->
<!--                items.s0.prefix }}</span>&nbsp;&nbsp;-->
<!--                <el-input v-if="receiveParamsArray[index] && receiveParamsArray[index].length > 0" v-model="score[index][subIndex + 2]"-->
<!--                          @change="doScoreChange(index, subIndex + 2)" style="width: 80px" :disabled="disabled || !selecteArray[index][2]">-->
<!--                </el-input>&nbsp;&nbsp; <span v-if="receiveParamsArray[index] && receiveParamsArray[index].length > 0">{{-->
<!--                  items.s0.suffix }}</span>&nbsp;&nbsp; &nbsp;&nbsp;-->
<!--                <el-button v-if="receiveParamsArray[index] && receiveParamsArray[index].length === 1 && !receiveParamsArray[index][0]" type="text"-->
<!--                           @click="doAddCoupon(index)" :disabled="disabled || !selecteArray[index][2]">-->
<!--                  + {{ i18n("添加优惠券") }}-->
<!--                </el-button>-->
<!--                &nbsp;&nbsp;-->
<!--              </template>-->
<!--            </i18n>-->
<!--            <a :title="receiveNameArray[index][subIndex]" type="text"-->
<!--               style="width: 100px;cursor: pointer;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;display: inline-block;position: relative;top: 4px;"-->
<!--               @click="doEditStoreValueActive(index, subIndex)">{{ receiveNameArray[index][subIndex] }}</a>-->
<!--            &nbsp;&nbsp;-->
<!--            <el-button :disabled="disabled" v-if="receiveNameArray[index].length > 0" @click="doDeleteCoupon(index, subIndex)" type="text" style="color: red">删除-->
<!--            </el-button>-->
<!--            &nbsp;&nbsp;-->
<!--            <el-button :disabled="disabled" v-if="subIndex === receiveParamsArray[index].length - 1 && receiveNameArray[index].length > 0" type="text"-->
<!--                       @click="doAddCoupon(index, subIndex)">添加</el-button>-->
<!--          </div>-->
<!--                  <el-checkbox v-model="item.rebateCheck" @change="checkChange(item.rebateCheck, index)">{{i18n('赠送返现')}}</el-checkbox>-->
<!--                  &nbsp;&nbsp;-->
<!--                  <el-form-item v-if="form.data.rebateType === 'amount'" class="line-form-item" :prop="'lines.'+ index + '.rebateAmount'"-->
<!--                    :rules="customAmountRules(index)">-->
<!--                    <AutoFixInput :disabled="!item.rebateCheck" size="small" class="short-input" :min="0.01" :max="999999.99" :fixed="2"-->
<!--                      v-model="item.rebateAmount" />-->
<!--                  </el-form-item>-->
<!--                  <el-form-item v-if="form.data.rebateType === 'percentage'" class="line-form-item" :prop="'lines.'+ index + '.rebatePercentage'"-->
<!--                    :rules="customPerRules(index)">-->
<!--                    <AutoFixInput :disabled="!item.rebateCheck" size="small" class="short-input" :min="1" :max="100" :fixed="0"-->
<!--                      v-model="item.rebatePercentage" />-->
<!--                  </el-form-item>-->
<!--                  &nbsp;&nbsp;-->
<!--                  &lt;!&ndash; {{i18n('元')}} &ndash;&gt;-->
<!--                  <el-select v-model="form.data.rebateType" style="width: 80px" @change="rebateTypeChange">-->
<!--                    <el-option :label="i18n('元')" value="amount"></el-option>-->
<!--                    <el-option label="%" value="percentage"></el-option>-->
<!--                  </el-select>-->
<!--                  &nbsp;&nbsp;-->
<!--                  <span style="color: #8f8f8f" v-if="form.data.rebateType === 'amount'">{{ i18n('不填不返现') }}</span>-->
<!--                  <span style="color: #8f8f8f"-->
<!--                    v-if="form.data.rebateType === 'percentage'">{{locale === 'zh_CN'? i18n('不填不返现, %是指实充金额%'): 'No cash back if not filled in,% refers to the paid in amount%' }}</span>-->
                </el-form-item>
              </div>

              <div class="dash-border">
                <span>{{ i18n('积分设置') }}</span>
                &nbsp;
                <el-checkbox style="margin: 0" v-model="form.data.pointCheck"></el-checkbox>
                {{ i18n('每充值') }}<el-form-item class="line-form-item" prop="amount">
                  <AutoFixInput class="sm-input" :min="1" :max="99999" :fixed="0" v-model="form.data.amount"></AutoFixInput>
                </el-form-item>
                {{ i18n('元，送') }}<el-form-item class="line-form-item" prop="points">
                  <AutoFixInput :min="1" :max="99999" :fixed="0" class="sm-input" v-model="form.data.points"></AutoFixInput>{{ i18n('积分') }}
                </el-form-item>
                <span style="color: #8f8f8f">{{ i18n('赠送的积分是根据实充金额进行计算') }}</span>
              </div>

              <div>
                <el-button type="primary" @click="addLine">添加</el-button>
                <span style="color: #8f8f8f; margin-left: 10px">至少有一个阶梯；当充值金额满足多个阶梯时，按照最大的折扣规则算；折扣规则一样，按照最大的返现金额返现</span>
              </div>
            </el-form-item>
            <el-form-item prop="description" :label="i18n('充值说明')">
              <el-input v-model="form.data.description" maxlength="1000" :autosize="{ minRows: 6 }" :placeholder="i18n('请输入不超过1000个字')" style="width: 350px"
                type="textarea">
              </el-input>
            </el-form-item>
            <el-form-item prop="agreement" :label="i18n('充值协议')">
              <RichText v-model="form.data.agreement"></RichText>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>
    <CardTemplateSelectorDialog no-i18n ref="cardTemplateSelectorDialog" :filter="cardTemplateFilter" @summit="doCardTemplateSelected">
    </CardTemplateSelectorDialog>

  </div>
</template>

<script lang="ts" src="./PrepayCardChargeGiftEdit.ts">
</script>

<style lang="scss">
.prepay-card-charge-gift-edit {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: auto;
  .short-input {
    width: 150px;
  }

  .current-page {
    height: calc(100% - 48px) !important;
    overflow: auto;

    .panel {
      .header {
        font-weight: 500;
        padding: 20px 20px 0 20px;
        font-size: 18px;
      }

      .content {
        padding: 20px;
        .send-gift {
          display: flex;
        }
        .discount {
          display: flex;
        }
      }
    }

    .split {
      height: 20px;
      background-color: #eeeff1;
    }

    .el-range__icon {
      line-height: 26px;
    }

    .el-range-separator {
      line-height: 26px;
    }

    .el-range__close-icon {
      line-height: 26px;
    }

    .rule-table {
      width: 70%;

      .rule-table-header {
        padding: 0 10px 0 10px;
        background-color: #e6e6e6;
        border: 1px solid #e6e6e6;
      }

      .rule-table-line {
        padding: 10px 10px 20px 10px;
        border: 1px solid #e6e6e6;
      }
    }

    .favor-row {
      display: flex;
      align-items: center;
      font-size: 12px;
      .el-radio {
        margin-right: 0 !important;
      }
      .el-radio__label {
        padding-left: 8px !important;
      }
    }

    .float-left-i18n-line {
      .text {
        height: 41px;
        float: left;
      }

      .el-form-item {
        float: left;
      }
    }
  }

  .dash-border {
    border-top: dashed 1px #cccccc;
    margin-top: 20px;
    padding-top: 20px;
  }
  .sm-input {
    width: 80px;
    margin: 0 10px;
  }
  .line-form-item {
    display: inline-block;
    margin-bottom: 15px;
  }
}
</style>
