/*
 * @Author: 黎钰龙
 * @Date: 2024-04-28 16:39:06
 * @LastEditTime: 2024-04-30 16:29:21
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\selectordialogs\EquitySelectDialog.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import { Component, Vue } from 'vue-property-decorator';
import AbstractSelectDialog from './AbstractSelectDialog';
import BenefitConfig from 'model/member/BenefitConfig';
import BenefitConfigFilter from 'model/member/BenefitConfigFilter';
import BenefitConfigApi from 'http/grade/equityCenter/BenefitConfigApi';
@Component({
  name: 'EquitySelectDialog',
  components: {}
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/会员/权益中心'
  ],
  auto: true
})
export default class EquitySelectDialog extends AbstractSelectDialog<BenefitConfig> {
  equityFilter: BenefitConfigFilter = new BenefitConfigFilter()

  created() {
    this.equityFilter = new BenefitConfigFilter()
  }
  
  reset() {
    this.equityFilter = new BenefitConfigFilter()
  }

  getId(ins: BenefitConfig): string {
    return ins.uuid!;
  }

  getName(ins: BenefitConfig): string {
    return ins.name!;
  }

  getResponseData(response: any): any {
    return response.data
  }

  queryFun(): Promise<any> {
    const params = JSON.parse(JSON.stringify(this.equityFilter))
    params.page = this.page.currentPage - 1
    params.pageSize = this.page.size
    return BenefitConfigApi.query(params)
  }
};