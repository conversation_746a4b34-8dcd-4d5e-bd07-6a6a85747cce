import BCouponTemplatePromotion from './BCouponTemplatePromotion'
import BPointsCouponAttribute from './BPointsCouponAttribute'
import BWeimobCoupon from './BWeimobCoupon'
import CashCouponAttribute from './CashCouponAttribute'
import Channel from './Channel'
import ChannelRange from './ChannelRange'
import CostParty from './CostParty'
import CouponGoodsFavRule from './CouponGoodsFavRule'
import CouponSuperposition from './CouponSuperposition'
import CouponTemplateOuterRelation from './CouponTemplateOuterRelation'
import CouponTemplateTagRelation from './CouponTemplateTagRelation'
import CouponThreshold from './CouponThreshold'
import DateTimeRange from './DateTimeRange'
import DiscountCouponAttribute from './DiscountCouponAttribute'
import ExchangeGoodsCouponAttribute from './ExchangeGoodsCouponAttribute'
import IdName from './IdName'
import PickUpCouponAttribute from './PickUpCouponAttribute'
import RandomCouponAttribute from './RandomCouponAttribute'
import RexCoupon from './RexCoupon'
import SpecialPriceCouponAttribute from './SpecialPriceCouponAttribute'
import StoreRange from './StoreRange'
import SubjectApportion from './SubjectApportion'
import ValidityInfo from './ValidityInfo'
import WriteOffScene from './WriteOffScene'
import { CouponSubscriptType } from './CouponSubscriptType'
import { PromotionSuperpositionType } from './PromotionSuperpositionType'
import { UseThresholdType } from './UseThresholdType'
import GoodsRange from 'model/common/GoodsRange'

export default class BCouponInfo {
  // 模板id
  templateId: Nullable<string> = null
  // 券名称
  name: Nullable<string> = null
  // 券基础类型：all_cash——全场现金券； goods_cash——商品现金券； all_discount——全场折扣券； rfm_type——商品折扣券； goods_discount——单品折扣券;goods——提货券；
  couponBasicType: Nullable<string> = null
  // 券有效期
  validityInfo: Nullable<ValidityInfo> = null
  // 用券时段
  useTimeRange: Nullable<DateTimeRange> = null
  // 用券门店
  useStores: Nullable<StoreRange> = null
  // 用券商品
  useGoods: Nullable<GoodsRange> = null
  // 用券门槛
  useThreshold: Nullable<CouponThreshold> = null
  // 是否叠加POM优惠，true表示叠加优惠，false表示不可参与叠加 
  excludePromotion: Nullable<boolean> = null
  // POM优惠叠加类型 ALL("全部"),PART("部分")
  promotionSuperpositionType: Nullable<PromotionSuperpositionType> = null
  // 叠加优惠列表
  goodsFavRules: CouponGoodsFavRule[] = []
  // 是否支持转赠
  transferable: Nullable<boolean> = null
  // 用券记录方式
  useApporion: Nullable<SubjectApportion> = null
  // 券承担方
  costParty: Nullable<CostParty> = null
  // 用券顺序
  priority: Nullable<number> = null
  // 用券商品说明
  goodsRemark: Nullable<string> = null
  // 用券说明
  remark: Nullable<string> = null
  // 现金券属性
  cashCouponAttribute: Nullable<CashCouponAttribute> = null
  // 折扣券属性
  discountCouponAttribute: Nullable<DiscountCouponAttribute> = null
  // 提货券属性
  pickUpCouponAttribute: Nullable<PickUpCouponAttribute> = null
  // 兑换券属性
  exchangeGoodsCouponAttribute: Nullable<ExchangeGoodsCouponAttribute> = null
  // 特价券属性
  specialPriceCouponAttribute: Nullable<SpecialPriceCouponAttribute> = null
  // 券码前缀，为null或者空串时忽略
  codePrefix: Nullable<string> = null
  // 用券渠道
  useChannels: Nullable<ChannelRange> = null
  // 同步渠道
  sychChannel: Channel[] = []
  // 不可与以下优惠券叠加，同一单品只可按最大优惠力度使用其中一种券
  groupMutexFlag: Nullable<boolean> = null
  // 商品限制叠加模板信息
  groupMutexTemplates: IdName[] = []
  // 券logo地址
  logoUrl: Nullable<string> = null
  // 券模板状态
  state: Nullable<string> = null
  // 外部券模板号id
  outerNumberId: Nullable<string> = null
  // 外部券模板号namespace
  outerNumberNamespace: Nullable<string> = null
  // 应用范围，为weixin时不可编辑
  scope: Nullable<string> = null
  // 是否开启将支付方式记流水
  enablePayApportion: Nullable<boolean> = null
  // 营销中心
  marketingCenter: Nullable<string> = null
  // 券模板外部关系
  outerRelations: CouponTemplateOuterRelation[] = []
  // 积分券属性
  pointsCouponAttribute: Nullable<BPointsCouponAttribute> = null
  // 随机金额券属性
  randomCouponAttribute: Nullable<RandomCouponAttribute> = null
  // 用券门槛类型
  useThresholdType: Nullable<UseThresholdType> = null
  // 用券叠加
  couponSuperposition: Nullable<CouponSuperposition> = null
  // 微盟平台券
  weimobCoupon: Nullable<BWeimobCoupon> = null
  // REX平台券
  rexCoupon: Nullable<RexCoupon> = null
  // 剩余库存
  total: Nullable<number> = null
  // 标签关系
  templateTag: CouponTemplateTagRelation[] = []
  // 促销单信息
  promotion: Nullable<BCouponTemplatePromotion> = null
  // 价格/售价
  salePrice: Nullable<number> = null
  // 账款项目
  termsModel: Nullable<string> = null
  // 券角标
  couponSubscriptType: Nullable<CouponSubscriptType> = null
  // 是否增值商品，默认为false表示普通商品
  appreciationGoods: Nullable<boolean> = null
  // 核销链接
  writeOffLink: Nullable<string> = null
  // 核销场景
  writeOffScene: Nullable<WriteOffScene> = null
}