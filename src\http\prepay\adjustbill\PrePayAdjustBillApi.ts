import ApiClient from 'http/ApiClient'
import AdjustBillReason from 'model/prepay/adjustbill/AdjustBillReason'
import AdjustBillStats from 'model/prepay/adjustbill/AdjustBillStats'
import IdName from 'model/common/IdName'
import MemberAccount from 'model/prepay/adjustbill/MemberAccount'
import PrePayReasonFilter from 'model/prepay/adjustbill/PrePayReasonFilter'
import PrepayAdjustBill from 'model/prepay/adjustbill/PrepayAdjustBill'
import PrepayAdjustBillFilter from 'model/prepay/adjustbill/PrepayAdjustBillFilter'
import PrepayAdjustBillLine from 'model/prepay/adjustbill/PrepayAdjustBillLine'
import Response from 'model/common/Response'
import GiftCardFilter from "model/prepay/report/card/GiftCardFilter";
import BBatchPrepayAdjustBillOperatorRequest from 'model/default/BBatchPrepayAdjustBillOperatorRequest'
import BPrepayAdjustBillOperatorRequest from 'model/default/BPrepayAdjustBillOperatorRequest'

export default class PrePayAdjustBillApi {
  /**
   * 审核储值调整单
   *
   */
  static audit(billNumber: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/prepay-adjust-bill/audit/${billNumber}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 调整单明细导出
   * 调整单明细导出。
   *
   */
  static exportLine(billNumber: String): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/prepay-adjust-bill/exportLine?billNumber=${billNumber}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 审核储值调整单
   *
   */
  static batchAudit(body: string[]): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/prepay-adjust-bill/batch/audit`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 删除储值调整单
   *
   */
  static batchRemove(body: string[]): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/prepay-adjust-bill/batch/remove`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 获取储值调整单详情
   *
   */
  static get(billNumber: string): Promise<Response<PrepayAdjustBill>> {
    return ApiClient.server().get(`/v1/prepay-adjust-bill/get/${billNumber}`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询多账户账户信息
   *
   */
  static getAccount(): Promise<Response<IdName[]>> {
    return ApiClient.server().get(`/v1/prepay-adjust-bill/getAccount`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 获取账户类型列表
   *
   */
  static getAccountTypes(): Promise<Response<IdName[]>> {
    return ApiClient.server().get(`/v1/prepay-adjust-bill/getAccountTypes`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 下载导入文件
   *
   */
  static getExcel(): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/prepay-adjust-bill/getExcel`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询会员账户信息
   *
   */
  static getMemberAccount(identCode: string): Promise<Response<MemberAccount>> {
    return ApiClient.server().get(`/v1/prepay-adjust-bill/getMemberAccount/${identCode}`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 获取储值调整单修改详情
   *
   */
  static getModify(billNumber: string): Promise<Response<PrepayAdjustBill>> {
    return ApiClient.server().get(`/v1/prepay-adjust-bill/get/modify/${billNumber}`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 批量导入储值调整单
   *
   */
  static importExcel(body: any): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/prepay-adjust-bill/importExcel`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询储值调整原因
   *
   */
  static listReason(body: PrePayReasonFilter): Promise<Response<AdjustBillReason[]>> {
    return ApiClient.server().post(`/v1/prepay-adjust-bill/listReason`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 修改储值调原因
   *
   */
  static modifyReason(id: string, reason: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/prepay-adjust-bill/${id}/modifyReason`, {}, {
      params: {
        reason: reason
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询储值调整单
   *
   */
  static query(body: PrepayAdjustBillFilter): Promise<Response<PrepayAdjustBill[]>> {
    return ApiClient.server().post(`/v1/prepay-adjust-bill/query`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 获取储值调整单明细详情
   *
   */
  static queryDetail(billNumber: string, page: number, pageSize: number): Promise<Response<PrepayAdjustBillLine[]>> {
    return ApiClient.server().get(`/v1/prepay-adjust-bill/queryDetail/${billNumber}`, {
      params: {
        page: page,
        pageSize: pageSize
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除储值调原因
   *
   */
  static removeReason(body: string[]): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/prepay-adjust-bill/removeReason`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 新建储值调整单
   *
   */
  static save(body: PrepayAdjustBill): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/prepay-adjust-bill/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建并审核储值调整单
   *
   */
  static saveAndAudit(body: PrepayAdjustBill): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/prepay-adjust-bill/saveAndAudit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改储值调整单
   *
   */
  static saveModify(body: PrepayAdjustBill): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/prepay-adjust-bill/saveModify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建储值调原因
   *
   */
  static saveReason(reason: string): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/prepay-adjust-bill/saveReason`, {}, {
      params: {
        reason: reason
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 储值调单汇总
   *
   */
  static stats(body: PrepayAdjustBillFilter): Promise<Response<AdjustBillStats>> {
    return ApiClient.server().post(`/v1/prepay-adjust-bill/stats`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
  * 获取储值调整单oa配置
  * 获取储值调整单oa配置。
  * 
  */
  static getOaConfig(): Promise<Response<boolean>> {
    return ApiClient.server().post(`/v1/prepay-adjust-bill/getOaConfig`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量驳回储值调整单
   *
   */
  static batchRejected(body: BBatchPrepayAdjustBillOperatorRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/prepay-adjust-bill/batch/rejected`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量提交储值调整单
   *
   */
  static batchSubmit(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/prepay-adjust-bill/batch/submit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存并提交储值调整单
   * 保存并提交储值调整单。
   * 
   */
  static saveAndSubmit(body: PrepayAdjustBill): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/prepay-adjust-bill/saveAndSubmit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 提交储值调整单
  * 提交储值调整单。
  * 
  */
  static submit(body: BPrepayAdjustBillOperatorRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/prepay-adjust-bill/submit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 驳回储值调整单
  * 驳回储值调整单。
  * 
  */
  static rejected(body: BPrepayAdjustBillOperatorRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/prepay-adjust-bill/rejected`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
