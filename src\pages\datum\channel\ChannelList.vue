<template>
  <div class="channel-view">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate"></template>
    </BreadCrume>
    <div class="current-page">
      <div style="margin: 20px;line-height: 40px" v-if="editable">
        <el-row>
          <el-col
            :span="2"
            style="min-width: 100px;text-align: right;padding-right: 10px;"
          >{{i18n('渠道类型')}}</el-col>
          <el-col :span="4">
            <el-select
              v-model="newIns.channelManagement.channel.type"
              :placeholder="i18n('请选择')"
              style="width: 250px"
              @change="isWeimobFn"
            >
              <el-option
                v-for="(value,key) in channelTypes"
                :key="key"
                :value="key"
                :label="value"
              >
                <span style="float: left">{{ value }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ key }}</span>
              </el-option>
            </el-select>
          </el-col>
        </el-row>
        <el-row>
          <el-col
            :span="2"
            style="min-width: 100px;text-align: right;padding-right: 10px;"
          >{{i18n('渠道ID')}}</el-col>
          <el-col :span="18">
            <el-input
              v-model="newIns.channelManagement.channel.id"
              :placeholder="i18n(`请输入数字、字母或-`)"
              style="width: 250px"
            ></el-input>&nbsp;&nbsp;
            <span style="color: #666666;font-size: 13px">
              <i class="el-icon-warning" />
              {{i18n('类型相同的渠道，渠道ID不允许重复')}}
            </span>
          </el-col>
        </el-row>
        <el-row>
          <el-col
            :span="2"
            style="min-width: 100px;text-align: right;padding-right: 10px;"
          >{{i18n('渠道名称')}}</el-col>
          <el-col :span="18">
            <el-input v-model="newIns.channelManagement.name" style="width: 250px"></el-input>
          </el-col>
        </el-row>
        <el-row v-if="isWeimob">
          <el-col
            :span="2"
            style="min-width: 100px;text-align: right;padding-right: 10px;"
          >{{i18n('默认门店代码')}}</el-col>
          <el-col :span="18">
            <el-input v-model="newIns.channelManagement.defaultOrgId" style="width: 250px"></el-input>
          </el-col>
        </el-row>
        <el-row v-if="isWeimob">
          <el-col
            :span="2"
            style="min-width: 100px;text-align: right;padding-right: 10px;"
          >{{i18n('微盟商家店id')}}</el-col>
          <el-col :span="18">
            <el-input v-model="newIns.channelManagement.defaultOuterOrgId" style="width: 250px"></el-input>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="2" style="min-width: 100px">&nbsp;</el-col>
          <el-col :span="18">
            <el-button class="btn-search" @click="add" type="primary">{{i18n('添加')}}</el-button>
            <el-button class="btn-reset" type="normal" @click="clear">{{i18n('清空')}}</el-button>
          </el-col>
        </el-row>
      </div>
      <ListWrapper style="overflow: initial" :showQuery="editable">
        <template slot="list">
          <FloatBlock refClass="current-page" :top="95" style="padding: 5px;">
            <template slot="ctx">
              <el-row>
                <el-col :span="12" style="line-height: 36px" v-if="editable">
                  <el-checkbox
                    :disabled="loading"
                    v-model="checkedAll"
                    style="padding-left: 10px;"
                    @change="checkedAllRow"
                  />
                  <i18n k="/资料/渠道/已选择{0}个渠道">
                    <template slot="0">
                      <span class="number-text">{{selected.length}}</span>
                    </template>
                  </i18n>&nbsp;&nbsp;
                  <el-button
                    type="success"
                    @click="toggleState(true)"
                  >{{i18n('批量启用')}}</el-button>
                  <el-button
                    type="danger"
                    @click="toggleState(false)"
                  >{{i18n('批量禁用')}}</el-button>
                </el-col>
                <el-col :span="12" style="line-height: 36px" v-if="!editable">&nbsp;</el-col>
                <el-col :span="12">
                  <div style="width: 480px;float: right;display: flex;align-items: center">
                    <el-select
                      style="width: 180px"
                      v-model="query.channelTypeEquals"
                      @change="doSearch"
                    >
                      <el-option :value="null" :label="i18n('全部渠道类型')">{{i18n('全部渠道类型')}}</el-option>
                      <el-option
                        v-for="(value,key) in channelTypes"
                        :value="key"
                        :label="value"
                        :key="value"
                      >{{value}}</el-option>
                    </el-select>&nbsp;&nbsp;&nbsp;
                    <el-input
                      :placeholder="i18n('搜索渠道ID/渠道名称')"
                      @change="doSearch"
                      v-model="query.idNameLikes"
                      suffix-icon="el-icon-search"
                      style="width: 310px"
                    />&nbsp;&nbsp;&nbsp;
                    <el-checkbox v-model="hideDisabled" @change="changeDisable">{{i18n('不看禁用渠道')}}</el-checkbox>
                  </div>
                </el-col>
              </el-row>
            </template>
          </FloatBlock>
          <el-table
            v-loading="loading"
            ref="table"
            :data="queryData"
            style="width: 100%;margin-top: 10px;"
            @selection-change="handleSelectionChange"
          >
            <el-table-column v-if="editable" type="selection" width="55"></el-table-column>
            <el-table-column fixed :label="i18n('渠道类型')" prop="channel.type">
              <template slot-scope="{row}">{{channelTypes[row.channel.type]}}</template>
            </el-table-column>
            <el-table-column fixed :label="i18n('渠道ID')" prop="channel.id" />
            <el-table-column fixed :label="i18n('渠道名称')" prop="name" />
            <el-table-column fixed :label="i18n('状态')" prop="state">
              <template slot-scope="{row}">
                <ChannelStateCmp :state="row.state"></ChannelStateCmp>
              </template>
            </el-table-column>
            <el-table-column fixed :label="i18n('操作')">
              <template slot-scope="scope">
                <span class="span-btn" style="margin-right:8px" @click="showInfoDialog(scope.row)">{{i18n('查看参数')}}</span>
                <span class="span-btn" style="margin-right:8px" v-if="scope.row.channel.type === 'weimob'" @click="extendDialog(scope.row)">{{i18n('查看扩展参数')}}</span>
                <span class="span-btn"
                  @click="showUpdateDialog(scope.row)"
                  v-if="editable && !editButtonIsDisabled(scope.row)"
                >{{i18n('修改')}}</span>
              </template>
            </el-table-column>
          </el-table>
        </template>
        <!--{{i18n('分页栏')}}-->
        <template slot="page">
          <el-pagination
            :current-page="page.currentPage"
            :page-size="page.size"
            :page-sizes="[10, 20, 30, 40]"
            :total="page.total"
            @current-change="onHandleCurrentChange"
            @size-change="onHandleSizeChange"
            background
            layout="total, prev, pager, next, sizes,  jumper"
          ></el-pagination>
        </template>
      </ListWrapper>
    </div>
    <el-dialog
      :title="i18n('修改')"
      :visible.sync="modifyDialog.visible"
      class="cosparty-dialog-center"
      width="750px"
    >
      <div style="margin: 20px;">
        <el-row>
          <el-col :span="2" style="min-width: 100px">{{i18n('渠道类型')}}</el-col>
          <el-col :span="18">{{channelTypes[updateIns.channelManagement.channel.type]}}</el-col>
        </el-row>
        <el-row style="margin-top: 20px">
          <el-col :span="2" style="min-width: 100px">{{i18n('渠道ID')}}</el-col>
          <el-col :span="18">{{updateIns.channelManagement.channel.id}}</el-col>
        </el-row>
        <el-row style="margin-top: 20px">
          <el-col :span="2" style="min-width: 100px">{{i18n('渠道名称')}}</el-col>
          <el-col :span="18">
            <el-input v-model="updateIns.channelManagement.name" style="width: 250px"></el-input>
          </el-col>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="modifyDialog.visible = false">{{i18n('取消')}}</el-button>
        <el-button type="primary" @click="update">{{i18n('确定')}}</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :title="i18n('渠道参数')"
      :visible.sync="infoDialog.visible"
      class="cosparty-dialog-center"
      width="30%"
    >
      <div style="margin: 20px;">
        <el-row style="display: flex;">
          <div style="width: 100px; color: #999999">{{i18n('渠道名称：')}}</div>
          <div style="width: calc(100% - 100px)">{{ infoDialog.row.name }}</div>
        </el-row>
        <el-row style="display: flex;margin-top: 10px">
          <div style="width: 100px; color: #999999">{{i18n('渠道参数：')}}</div>
          <div style="width: calc(100% - 100px)" v-html="formatJson(infoDialog.row)"></div>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="copyAndClose">{{i18n('复制参数并关闭')}}</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :title="i18n('扩展参数')"
      :visible.sync="extendInfo.visible"
      width="30%"
      :before-close="handleClose"
    >
      <div style="margin: 20px;">
        <el-row style="display: flex;">
          <div style="width: 100px; color: #999999">{{i18n('默认门店代码')}}:</div>
          <div style="width: calc(100% - 100px)">{{ extendInfo.defaultOrgId }}</div>
        </el-row>
        <el-row style="display: flex;margin-top: 10px">
          <div style="width: 100px; color: #999999">{{i18n('微盟商家店id')}}:</div>
          <div style="width: calc(100% - 100px)">{{ extendInfo.defaultOuterOrgId }}</div>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="copyAndCloseExtend">{{i18n('复制参数并关闭')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script lang="ts" src="./ChannelList.ts">
</script>

<style lang="scss">
.channel-view {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: hidden;
  .cosparty-dialog-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .total {
    margin: 20px;
  }

  .current-page {
    height: calc(100% - 77px);
    overflow-y: scroll;

    .el-select {
      width: 100%;
    }
  }
}
</style>
