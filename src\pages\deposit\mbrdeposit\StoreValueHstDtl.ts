import {Component, Vue} from 'vue-property-decorator'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import ListWrapper from 'cmp/list/ListWrapper.vue'
import PrePayBalanceApi from 'http/prepay/balance/PrePayBalanceApi'
import PrepayAccountHst from 'model/prepay/balance/PrepayAccountHst'
import PrepayAccountTransaction from 'model/prepay/balance/PrepayAccountTransaction'
import PrepayAccountTransactionFilter from 'model/prepay/balance/PrepayAccountTransactionFilter'
import PrePayConfigApi from 'http/prepay/config/PrePayConfigApi'
import IdName from 'model/entity/IdName'
import I18nPage from "common/I18nDecorator";
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'

@Component({
  name: 'StoreValueHstDtl',
  components: {
    SubHeader,
    ListWrapper,
    BreadCrume
  }
})
@I18nPage({
  prefix: ['/储值/会员储值/会员储值账户管理/流水页面', '/储值/会员储值/会员储值查询/流水页面']
})
export default class StoreValueHstDtl extends Vue {
  i18n: any
  query: {
    page: 0,
    pageSize: 0
  }
  detail: PrepayAccountHst = new PrepayAccountHst()
  transactions: PrepayAccountTransaction[] = []
  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  uuid: string = ''
  enableMultipleAccount: boolean = false
  tableHeight: number = 0
  account: IdName = new IdName()
  panelArray: any = []
  created() {
    this.panelArray = [
      {
        name: this.formatI18n('/会员/会员资料/详情界面/会员资产/储值余额有值时点击/标题/会员储值流水'),
        url: ''
      }
    ]
    this.uuid = this.$route.query.uuid as any
    this.account.id = this.$route.query.id as any
    this.account.name = this.$route.query.name as any
    this.getDetail()
    this.getList()
    this.getPrePermission()
  }

  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.getList()
  }

  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.getList()
  }

  /**
   * 表格排序条件
   */
  onSortChange({column, prop, order}: any) {
    // todo
  }

  private getDetail() {
    PrePayBalanceApi.querySum(this.uuid).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.detail = resp.data
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private parseCategory(category: string) {
    switch (category) {
      case 'DEPOSIT':
        return this.i18n('充值')
      case 'DEPOSIT_REFUND':
        return this.i18n('充值退款')
      case 'PAY':
        return this.i18n('支付')
      case 'PAY_REFUND':
        return this.i18n('支付退款')
      case 'PAY_ROLLBACK':
        return this.i18n('支付回滚')
      case 'TRANSFER_IN':
        return this.i18n('转账出')
      case 'TRANSFER_OUT':
        return this.i18n('转账入')
      case 'ADJUST':
        return this.i18n('调整')
      case 'MERGE':
        return this.i18n('会员合并')
    }
  }

  private getList() {
    let filter = new PrepayAccountTransactionFilter()
    filter.page = this.page.currentPage - 1
    filter.pageSize = this.page.size
    filter.accountEquals = this.uuid
    PrePayBalanceApi.queryHst(filter).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.transactions = resp.data
        this.page.total = resp.total
        this.setTableSize()
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private setTableSize() {
    let table = document.getElementsByClassName('current-page')[0] as any
    if (table) {
      this.tableHeight = table.offsetHeight - 80
    }
  }

  private getPrePermission() {
    PrePayConfigApi.get().then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.enableMultipleAccount = resp.data.enableMultipleAccount
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
}
