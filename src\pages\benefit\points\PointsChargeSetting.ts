import {Component, Vue} from 'vue-property-decorator'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import PointsChargeMetaActivityApi from 'http/points/init/pointschargemeta/PointsChargeMetaActivityApi'
import PointsChargeMetaActivity from 'model/points/init/pointschargemeta/PointsChargeMetaActivity'
import ActivityBody from 'model/common/ActivityBody'
import AmountToFixUtil from 'util/AmountToFixUtil'
import ConstantMgr from 'mgr/ConstantMgr'

@Component({
  name: 'PointsChargeSetting',
  components: {
    BreadCrume
  }
})
export default class PointsChargeSetting extends Vue {
  $refs: any
  panelArray: any = []
  scoreCopy = ''
  ruleForm: any = {
    score: '',
    amount: '',
    option: 'first',
    overAmount: '',
    single: 'first',
    singleAmount: '',
    singlePercent: '',
    withAmount: '',
    withPercent: '',
    number: 'first',
    limit: ''
  }
  rules: any = []

  created() {
    this.panelArray = [
			{
        name: this.formatI18n("/权益/积分/积分初始化/未初始化状态/积分抵现规则", "积分抵现"),
        url: "points-charge-setting-dtl",
			},
			{
				name: this.formatI18n("/权益/积分/积分初始化/未初始化状态/积分抵现规则/积分抵现/点击立即设置/面包屑", "积分抵现设置"),
				url: "",
			},
		];
    this.rules = {
      score: [
        {required: true, message: this.formatI18n('/公用/券模板', '请输入必填项'), trigger: 'blur'}
      ],
      amount: [
        {required: true, message: this.formatI18n('/公用/券模板', '请输入必填项'), trigger: 'blur'}
      ],
      overAmount: [
        {
          required: true, validator: (rule: any, value: any, callback: any) => {
            if (this.ruleForm.option === 'first') {
              callback()
            } else {
              if (value) {
                callback()
              } else {
                callback(this.formatI18n('/公用/券模板', '请输入必填项'))
              }
            }
          }, trigger: 'blur'
        }
      ],
      limit: [
        {
          required: true, validator: (rule: any, value: any, callback: any) => {
            if (this.ruleForm.number === 'first') {
              callback()
            } else {
              if (value) {
                callback()
              } else {
                callback(this.formatI18n('/公用/券模板', '请输入必填项'))
              }
            }
          }, trigger: 'blur'
        }
      ],
      singleAmount: [
        {
          required: true, validator: (rule: any, value: any, callback: any) => {
            if (this.ruleForm.single === 'first') {
              callback()
            } else {
              if (this.ruleForm.single === 'second') {
                if (value) {
                  callback()
                } else {
                  callback(this.formatI18n('/公用/券模板', '请输入必填项'))
                }
              } else {
                callback()
              }
            }
          }, trigger: 'blur'
        }
      ],
      singlePercent: [
        {
          required: true, validator: (rule: any, value: any, callback: any) => {
            if (this.ruleForm.single === 'first') {
              callback()
            } else {
              if (this.ruleForm.single === 'third') {
                if (value) {
                  callback()
                } else {
                  callback(this.formatI18n('/公用/券模板', '请输入必填项'))
                }
              } else {
                callback()
              }
            }
          }, trigger: 'blur'
        }
      ],

      withAmount: [
        {
          required: true, validator: (rule: any, value: any, callback: any) => {
            if (this.ruleForm.single === 'first') {
              callback()
            } else {
              if (this.ruleForm.single === 'fourth') {
                if (value) {
                  callback()
                } else {
                  callback(this.formatI18n('/公用/券模板', '请输入必填项'))
                }
              } else {
                callback()
              }
            }
          }, trigger: 'blur'
        }
      ],
      withPercent: [
        {
          required: true, validator: (rule: any, value: any, callback: any) => {
            if (this.ruleForm.single === 'first') {
              callback()
            } else {
              if (this.ruleForm.single === 'fourth') {
                if (value) {
                  callback()
                } else {
                  callback(this.formatI18n('/公用/券模板', '请输入必填项'))
                }
              } else {
                callback()
              }
            }
          }, trigger: 'blur'
        }
      ]
    }
    if (this.$route.query.id) {
      this.getDtl()
    }
  }

  getDtl() {
    PointsChargeMetaActivityApi.detail().then((resp: any) => {
      if (resp && resp.code === 2000) {
        if (resp.data) {
          this.ruleForm.score = resp.data.points
          this.ruleForm.amount = resp.data.amount
          this.scoreCopy = resp.data.points
          if (resp.data.consumeType === 'UN_LIMIT') {
            this.ruleForm.option = 'first'
          } else {
            this.ruleForm.option = 'second'
            this.ruleForm.overAmount = resp.data.consumeAmount
          }
          if (resp.data.upperPayType === 'UN_LIMIT') {
            this.ruleForm.single = 'first'
          } else if (resp.data.upperPayType === 'LIMIT') {
            this.ruleForm.single = 'second'
            this.ruleForm.singleAmount = resp.data.upperPayValue
          } else if (resp.data.upperPayType === 'LIMIT_AMOUNT_RATE') {
            this.ruleForm.single = 'fourth'
            this.ruleForm.withAmount = resp.data.upperPayValue
            this.ruleForm.withPercent = resp.data.upperPayRate
          } else {
            this.ruleForm.single = 'third'
            this.ruleForm.singlePercent = resp.data.upperPayRate
          }
          if (resp.data.memberDailyPayTimesType === 'UN_LIMIT') {
            this.ruleForm.number = 'first'
          } else {
            this.ruleForm.number = 'second'
            this.ruleForm.limit = resp.data.memberDailyPayTimes
          }
        }
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  doSave() {
    this.$refs.ruleForm.validate((resp: any) => {
      if (!resp) {
        return
      }
      this.$confirm(this.formatI18n('/权益/积分初始化/积分抵现/保存后，新的积分抵现规则将立即生效。请确认是否保存？'), this.formatI18n('/公用/弹出模态框提示标题', '提示'), {
        confirmButtonText: this.formatI18n('/公用/按钮', '确定'),
        cancelButtonText: this.formatI18n('/公用/按钮', '取消')
      }).then(() => {
        const loading = this.$loading(ConstantMgr.loadingOption)
        let params: PointsChargeMetaActivity = new PointsChargeMetaActivity()
        params.body = new ActivityBody()
        if (this.$route.query.id) {
          params.body.activityId = this.$route.query.id as any
        }
        params.points = this.ruleForm.score
        params.amount = this.ruleForm.amount
        if (this.ruleForm.option === 'first') {
          params.consumeType = 'UN_LIMIT' as any
        } else {
          params.consumeType = 'LIMIT' as any
          params.consumeAmount = this.ruleForm.overAmount
        }
        if (this.ruleForm.single === 'first') {
          params.upperPayType = 'UN_LIMIT' as any
        } else if (this.ruleForm.single === 'second') {
          params.upperPayType = 'LIMIT' as any
          params.upperPayValue = this.ruleForm.singleAmount
        } else if (this.ruleForm.single === 'fourth') {
          params.upperPayType = 'LIMIT_AMOUNT_RATE' as any
          params.upperPayValue = this.ruleForm.withAmount
          params.upperPayRate = this.ruleForm.withPercent
        } else {
          params.upperPayType = 'PERCENT_RATE' as any
          params.upperPayRate = this.ruleForm.singlePercent
        }

        if (this.ruleForm.number === 'first') {
          params.memberDailyPayTimesType = 'UN_LIMIT' as any
        } else {
          params.memberDailyPayTimesType = 'LIMIT' as any
          params.memberDailyPayTimes = this.ruleForm.limit
        }
        PointsChargeMetaActivityApi.saveOrModify(params).then((resp: any) => {
          if (resp && resp.code === 2000) {
            loading.close()
            this.$message.success(this.formatI18n('/设置/权限/用户管理/功基本信息/保存', '保存成功'))
            this.$router.push({name: 'points-charge-setting-dtl'})
          }
        }).catch((error: any) => {
          loading.close()
          this.$message.error(error.message)
        })
      })
    })
  }

  doScoreChange() {
    this.ruleForm.score = AmountToFixUtil.formatNumber(this.ruleForm.score, 99999999, 1)
  }

  doAmountChange() {
    this.ruleForm.amount = AmountToFixUtil.formatAmount(this.ruleForm.amount, 99999999, 0.01, '')
  }

  doOverAmountChange() {
    this.ruleForm.overAmount = AmountToFixUtil.formatAmount(this.ruleForm.overAmount, 99999999, 0.01, '')
  }

  doSingleAmountChange() {
    this.ruleForm.singleAmount = AmountToFixUtil.formatAmount(this.ruleForm.singleAmount, 99999999, 0.01, '')
  }

  doSinglePercentChange() {
    this.ruleForm.singlePercent = AmountToFixUtil.formatAmount(this.ruleForm.singlePercent, 100, 0.01, '')
  }

  doWithAmountChange() {
    this.ruleForm.withAmount = AmountToFixUtil.formatAmount(this.ruleForm.withAmount, 99999999, 0.01, '')
  }

  doWithPercentChange() {
    this.ruleForm.withPercent = AmountToFixUtil.formatAmount(this.ruleForm.withPercent, 100, 0.01, '')
  }


  doLimitChange() {
    this.ruleForm.limit = AmountToFixUtil.formatNumber(this.ruleForm.limit, 99999999, 1)
  }

  doCancel() {
    this.$router.back()
  }

  doScoreBlur() {
    this.scoreCopy = this.ruleForm.score
  }

  doOptionChange() {
    if (this.ruleForm.option === 'first') {
      this.ruleForm.overAmount = ''
      this.$refs.ruleForm.validateField('overAmount')
    }
  }

  doSingleChange() {
    if (this.ruleForm.single === 'first' || this.ruleForm.single === 'fourth') {
      this.ruleForm.singleAmount = ''
      this.ruleForm.singlePercent = ''
      this.ruleForm.withPercent = ''
      this.ruleForm.withAmount = ''
    } else if (this.ruleForm.single === 'second') {
      this.ruleForm.singlePercent = ''
      this.ruleForm.withPercent = ''
      this.ruleForm.withAmount = ''
    } else {
      this.ruleForm.singleAmount = ''
      this.ruleForm.withPercent = ''
      this.ruleForm.withAmount = ''
    }
    this.$refs.ruleForm.validateField('singleAmount')
    this.$refs.ruleForm.validateField('singlePercent')
    this.$refs.ruleForm.validateField('withAmount')
    this.$refs.ruleForm.validateField('withPercent')
  }

  doNumberChange() {
    if (this.ruleForm.number === 'first') {
      this.ruleForm.limit = ''
      this.$refs.ruleForm.validateField('limit')
    }
  }
}