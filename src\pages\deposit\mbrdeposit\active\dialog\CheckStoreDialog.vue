<template>
    <el-dialog :before-close="doBeforeClose"
               :close-on-click-modal="false"
               :visible.sync="dialogShow"
               :title="formatI18n('/营销/券礼包活动/核销第三方券', '查看门店')" append-to-body class="check-store-dialog">
        <div class="wrap">
            <div class="table-wrap">
                <el-table
                        :data="data"
                        border
                        style="width: 100%;margin-top: 20px">
                    <el-table-column :label="getOrder" type="index" width="100"></el-table-column>
                    <el-table-column :label="formatI18n('/公用/券模板', '代码')" prop="id"/>
                    <el-table-column :label="formatI18n('/公用/券模板', '名称')" prop="name">
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <div class="dialog-footer" slot="footer">
            <!--<el-button @click="dialogFormVisible = false">取 消</el-button>-->
            <el-button @click="doModalClose" size="small" type="primary">{{formatI18n('/公用/按钮', '确定')}}</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts" src="./CheckStoreDialog.ts">
</script>

<style lang="scss">
    .check-store-dialog{
        display: flex;
        align-items: center;
        justify-content: center;
        .el-dialog{
            width: 1024px;
            height: 593px;
            margin: 0 !important;
        }
        .wrap{
            height: 440px;
            overflow: auto;
            .item{
                width: 228px;
                height: 108px;
                border: 1px solid #c7c7c7;
                border-radius: 10px;
                display: inline-block;
                margin-bottom: 24px;

                &:nth-child(odd) {
                    margin-right: 12px;

                }
                &:nth-child(even) {
                    margin-left: 12px;
                }
            }
        }
    }
</style>