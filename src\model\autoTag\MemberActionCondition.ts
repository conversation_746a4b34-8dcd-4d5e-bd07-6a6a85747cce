/*
 * @Author: 黎钰龙
 * @Date: 2024-12-10 14:35:59
 * @LastEditTime: 2024-12-11 17:27:10
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\autoTag\MemberActionCondition.ts
 * 记得注释
 */
import EventBehaviorAttribute from "./EventBehaviorAttribute"
import { EventBehaviorCalculateType } from "./EventBehaviorCalculateType"
import EventBehaviorRule from "./EventBehaviorRule"
import { NumberOperatorType } from "./NumberOperatorType"
import { PropType } from "./PropType"

export default class MemberActionCondition {
  // 事件行为规则
  eventBehaviorRule: Nullable<EventBehaviorRule> = null
  // 事件行为指标类型：字符串、数字、日期、布尔
  metricsType: Nullable<PropType> = null
  // 事件行为指标属性
  metricsProp: Nullable<string> = null
  // 事件行为指标计算方式
  metricsCalculation: Nullable<EventBehaviorCalculateType> = null
  // 运算符
  metricsOperator: Nullable<NumberOperatorType> = null
  // 事件行为指标属性值
  metricsValue: number[] = []
  // 事件行为指标属性可选列表（仅前端使用）
  metricsList: EventBehaviorAttribute[] = []
}