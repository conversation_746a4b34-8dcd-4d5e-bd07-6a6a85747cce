/*
 * @Author: 黎钰龙
 * @Date: 2024-01-24 14:07:05
 * @LastEditTime: 2024-03-28 17:03:04
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\miniappsetup\aliappsetup\BWeixinAppletSettingRequest.ts
 * 记得注释
 */
import BSaveCouponEntryRequest from './BSaveCouponEntryRequest'
import BSaveCustomizeEntryRequest from './BSaveCustomizeEntryRequest'
import BWeixinAppletConfig from './BWeixinAppletConfig'
import SaveBannerEntryRequest from './SaveBannerEntryRequest'
import SaveActivityDeliveryRequest from './SaveActivityDeliveryRequest'
import WeixinAppNavigation from './WeixinAppNavigation'

// ued 请求
export default class BWeixinAppletSettingRequest {
  // 功能入口
  customizeEntry: Nullable<BSaveCustomizeEntryRequest> = null
  // 轮播图
  bSaveBannerEntry: Nullable<SaveBannerEntryRequest> = null
  // 卡片
  weixinAppletConfig: Nullable<BWeixinAppletConfig> = null
  // 券入口
  couponEntry: Nullable<BSaveCouponEntryRequest> = null
  // 活动投放
  activityDeliveryRequest: Nullable<SaveActivityDeliveryRequest> = null
  // 小程序导航
  appNavigation: Nullable<WeixinAppNavigation> = null
  // 配置类型:HOME 首页，MINE:我的
  type: Nullable<string> = null
}