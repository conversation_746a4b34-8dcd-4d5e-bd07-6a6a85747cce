import {Component, Prop} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import OrgApi from 'http/org/OrgApi';
import RSOrgFilter from 'model/common/RSOrgFilter';
import AbstractSelectDialog from './AbstractSelectDialog';
import RSOrg from 'model/common/RSOrg';
import {OrgState} from "model/common/OrgState";

@Component({
  name: 'StoreSelectorDialog',
  components: {
    FormItem
  }
})
export default class StoreSelectorDialog extends AbstractSelectDialog<RSOrg> {
  orgFilter: RSOrgFilter = new RSOrgFilter()
  title: String = this.formatI18n('/公用/公共组件/门店选择弹框组件/标题/选择门店')
  @Prop({
    required: false,
    default: null
  })
  zoneId: Nullable<string>

  @Prop({
    required: false,
    default: null
  })
  marketingCenterId: Nullable<string>

  @Prop({
    type: Boolean,
    default: true
  })
  queryByMarketingCenter: Boolean

  @Prop({ 
    type: Boolean,
    default: false
  })
  hasImport: boolean;

  @Prop({
    type: Boolean,
    default: false,
  })
  enableStore: boolean; // 是否只查询可用门店

  reset() {
    this.orgFilter = new RSOrgFilter()
  }

  getId(ins: RSOrg): string {
    // @ts-ignore
    return ins.org.id;
  }

  getName(ins: RSOrg): string {
    // @ts-ignore
    return ins.org.name;
  }

  getResponseData(response: any): any {
    return response.data
  }

  doImport() {
    this.$emit('doImport')
  }

  queryFun(): Promise<any> {
    this.orgFilter.page = this.page.currentPage - 1
    this.orgFilter.pageSize = this.page.size
    this.orgFilter.sorters.orgId = 'asc'
    this.orgFilter.zoneIdEquals = this.zoneId
    this.orgFilter.queryByMarketingCenter = this.queryByMarketingCenter
    if(this.enableStore){
      this.orgFilter.orgStateEquals = OrgState.enable;
    }
    let oldMarketCenter = sessionStorage.getItem('marketCenter') || ''
    if (this.marketingCenterId) {
		  sessionStorage.setItem('marketCenter', this.marketingCenterId)
    }
    return OrgApi.query(this.orgFilter).finally(()=>{
			sessionStorage.setItem('marketCenter', oldMarketCenter as string)
		});
  }
}
