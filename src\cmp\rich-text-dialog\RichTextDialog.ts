import I18nPage from 'common/I18nDecorator';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  name: 'RichTextDialog',
  components: {}
})
@I18nPage({
  prefix: [
    '/公用/券模板'
  ],
  auto: true
})
export default class RichTextDialog extends Vue {
  @Prop({ type: String}) title: string;
  dialogVisible: boolean = false;
  richContent: string = '';  //富文本内容

  open(value:string){
    this.richContent = value
    this.dialogVisible = true
  }

  handleClose(done: Function) {
    this.richContent = ''
    done();
  }
};