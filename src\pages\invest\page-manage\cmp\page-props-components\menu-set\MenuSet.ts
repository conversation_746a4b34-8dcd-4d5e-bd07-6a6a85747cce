import { Vue, Component, Prop, Watch } from "vue-property-decorator";
import { FormMode } from "model/local/FormMode";
import draggable from "vuedraggable";
import JumpPageInfo from "model/navigation/JumpPageInfo";
import uploadImg from "@/cmp/upload-img/UploadImg.vue";
import I18nPage from "common/I18nDecorator";
import SysConfigApi from "http/config/SysConfigApi";
import { CmsConfigChannel } from "model/template/CmsConfig";

@Component({
  name: "MenuSet",
  mixins: [],
  components: { draggable, uploadImg },
})
@I18nPage({
  prefix: ["/公用/券模板", "/页面/页面管理", "/页面/导航设置"],
  auto: true,
})
export default class MenuSet extends Vue {
  @Prop({ type: Boolean, default: false })
  readonly: boolean; //
  @Prop({ type: String, default: "Text" })
  validateName: string; // 组件校验名称，editPage通过该名称进行组件校验及校验信息清除
  @Prop({ type: Object })
  value: any; // 数据模型
  @Prop({ type: String, default: "文本内容" })
  label: string; // label名
  @Prop({ type: String })
  prefixDescribe: string; // 前置描述
  @Prop({ type: String })
  suffixDescribe: string; // 后置描述

  @Prop({ type: Boolean, default: true })
  required: boolean; // 是否必填
  @Prop({ type: String })
  relativeValidateName: string; // 联动校验名称
  @Prop()
  formKey: any;
  @Prop({ type: Array })
  renderTemplateList: any[];
  @Prop()
  activeIndex: number;
  @Prop() advertiseChannel: Nullable<CmsConfigChannel[]>; //当前页面可用的投放渠道
  FormModeType = FormMode;
  $refs: any;
  labelPosition: string = "left";

  baiLianStore: boolean =false

  rules = {
    name: [
      {
        required: true,
        trigger: ["blur", "change"],
        message: this.i18n("请输入菜单名称"),
      },
    ],
    icon: [
      {
        required: true,
        trigger: ["blur", "change"],
        message: this.i18n("请上传图标"),
      },
    ],
    jumpPageInfo: [
      {
        required: true,
        trigger: ["blur", "change"],
        message: this.i18n("请选择跳转链接"),
      },
    ],
  };

  dialogVisible: boolean = false;
  editIndex: any = null;
  addFormData: any = {
    id: "",
    name: "",
    icon: "",
    enable: true,
    systemMenu: false,
    jumpPageInfo: null,
  };

  // 勾选个数
  get listNum() {
    return this.value.propCustomMenus.reduce((data: any, next: any) => {
      if (next.enable) data++;
      return data;
    }, 0);
  }

  get filterActivityWidgets() {
    return this.renderTemplateList.filter((item, index) => index !== this.activeIndex);
  }

  @Watch('value')
  valueChange(){
    const find = this.value.propCustomMenus?.find((item: any) => item.id === "myStoredValue");
    if (!find?.jumpPageInfo) {
      find.jumpPageInfo = new JumpPageInfo();
      find.JumpPageInfo.templateId = "StoredValue";
      find.JumpPageInfo.templateName = "储值";
    }

    if (!find?.jumpPageInfo?.templateId) {
      if (this.baiLianStore) {
        find.jumpPageInfo.templateId = "BaiLianStoredValue";
        find.jumpPageInfo.templateName = "百联储值";
      } else {
        find.jumpPageInfo.templateId = "StoredValue";
        find.jumpPageInfo.templateName = "储值";
      }
    }
  }

  mounted() {
    const find = this.value.propCustomMenus?.find((item: any) => item.id === "myStoredValue");
    if (!find?.jumpPageInfo) {
      find.jumpPageInfo = new JumpPageInfo();
      find.JumpPageInfo.templateId = "StoredValue";
      find.JumpPageInfo.templateName = "储值";
    }

    SysConfigApi.get().then((response) => {
      if(response?.data?.baiLianStore){
        this.baiLianStore = true;
      }
      if (!find.jumpPageInfo?.templateId) {
        if (response?.data?.baiLianStore) {
          find.jumpPageInfo.templateId = "BaiLianStoredValue";
          find.jumpPageInfo.templateName = "百联储值";
        } else {
          find.jumpPageInfo.templateId = "StoredValue";
          find.jumpPageInfo.templateName = "储值";
        }
      }
    }).finally(()=>{
      if (this.value) {
        this.handleChange();
      }
    })

    // 新建
  
  }

  handleChange(key?: any) {
    if (typeof key === "number") {
      if (this.value.propCustomMenus[key].enable === true && this.listNum > 10) {
        this.$message.error("最多只能勾选10个");
        this.value.propCustomMenus[key].enable = false;
      }
      if (this.value.propCustomMenus[key].enable === false && this.listNum < 1) {
        this.$message.error("至少勾选1个菜单");
        this.value.propCustomMenus[key].enable = true;
      }
    }

    this.$emit("input", JSON.parse(JSON.stringify(this.value)));
    this.$emit("change", JSON.parse(JSON.stringify(this.value)));
    this.validate(() => {});
  }

  validate(callback: () => void) {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.validate(callback);
    }
  }

  clearValidate() {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.clearValidate();
    }
  }

  addDialogShow() {
    this.dialogVisible = true;
    this.editIndex = null;
    this.addFormData = {
      id: "",
      name: "",
      icon: "",
      enable: true,
      systemMenu: false,
      jumpPageInfo: null,
    };
    this.$refs["addForm"].resetFields();
  }

  editForm(item: any, index: any) {
    this.addFormData = JSON.parse(JSON.stringify({ ...item }));
    this.dialogVisible = true;
    this.editIndex = index;
    this.$refs["addForm"].resetFields();
  }

  jumpPageChange(value: any) {
    if (value.firstLevelPage) {
      this.addFormData.jumpPageInfo = value;
    } else {
      this.addFormData.jumpPageInfo = null;
    }
  }

  deleteForm(index: any) {
    this.value.propCustomMenus.splice(index, 1);
  }

  generateRandomNumbers(length: any) {
    let result = "";
    const characters = "0123456789";
    const charactersLength = characters.length;
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
  }

  addFormSubmit() {
    this.$refs["addForm"].validate((valid: any) => {
      if (valid) {
        const data = { ...this.addFormData };
        if (typeof this.editIndex == "number") {
          this.value.propCustomMenus[this.editIndex] = data;
        } else {
          if (this.listNum == 10) data.enable = false;
          data.id = this.generateRandomNumbers(8);
          this.value.propCustomMenus.push(data);
        }
        this.dialogVisible = false;
        this.$refs["addForm"].resetFields();
        this.handleChange();
      } else {
        let text = [];
        if (!this.addFormData.name) text.push("名称");
        if (!this.addFormData.icon) text.push("图标");
        if (!this.addFormData.jumpPageInfo && !this.addFormData.systemMenu) text.push("链接");
        this.$message.error(`${text.join("、")}未填写`);
        return false;
      }
    });
  }

  changeStoredJumpValue(jumpPageInfo: JumpPageInfo) {
    if (jumpPageInfo.templateId === "BaiLianStoredValue") {
      jumpPageInfo.templateName = "百联储值";
    } else {
      jumpPageInfo.templateName = "储值";
    }
  }
}
