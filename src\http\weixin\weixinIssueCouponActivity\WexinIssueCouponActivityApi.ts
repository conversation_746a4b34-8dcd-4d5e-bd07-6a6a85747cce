import ApiClient from "http/ApiClient";
import Response from "model/common/Response";
import { RefundAlgorithm } from "model/weixin/weixinIssueCouponActivity/RefundAlgorithm";
import WeiXinCouponTemplateInfo from "model/weixin/weixinIssueCouponActivity/WeiXinCouponTemplateInfo";
import WeiXinIssueCouponActivity from "model/weixin/weixinIssueCouponActivity/WeiXinIssueCouponActivity";

export default class WexinIssueCouponActivityApi {
  /**
   * 小程序发微信券活动活动详情
   *
   */
  static getWeiXinAppletIssueCouponActivity(
    id: string
  ): Promise<Response<WeiXinIssueCouponActivity>> {
    return ApiClient.server()
      .get(`/v1/coupon-activity/getWeiXinAppletIssueCouponActivity/${id}`, {})
      .then((res) => {
        return res.data;
      });
  }
  /**
   * 新建或修改微信小程序发券活动
   * 新建或修改微信小程序发券活动
   *
   */
  static saveWeiXinAppletIssueCouponActivity(
    body: WeiXinIssueCouponActivity
  ): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/saveWeiXinAppletIssueCouponActivity`, body, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 获取微信小程序支付券信息
   * 获取微信小程序支付券信息
   *
   */
  static getWeiXinAppletPaymentCouponInfo(
    activityNum: string
  ): Promise<Response<WeiXinCouponTemplateInfo[]>> {
    return ApiClient.server()
      .get(
        `/v1/coupon-activity/getWeiXinAppletPaymentCouponInfo/${activityNum}`,
        {}
      )
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 根据批次号获取微信小程序支付券信息
   * 根据批次号获取微信小程序支付券信息
   *
   */
  static getPayCouponInfoByStockId(
    stockId: string,
    merchantId?: string
  ): Promise<Response<WeiXinCouponTemplateInfo>> {
    if (merchantId) {
      stockId = stockId + `?merchantId=${merchantId}`
    }
    return ApiClient.server()
      .get(`/v1/coupon-activity/getPayCouponInfoByStockId/${stockId}`, {})
      .then((res) => {
        return res.data;
      });
  }
  /**
   * 小程序发微信券活动活动详情
   *
   */
  static getPurchaseCouponRefundAlgorithm(): Promise<
    Response<RefundAlgorithm>
  > {
    return ApiClient.server()
      .post(`/v1/coupon-activity/getPurchaseCouponRefundAlgorithm`, {})
      .then((res) => {
        return res.data;
      });
  }
}
