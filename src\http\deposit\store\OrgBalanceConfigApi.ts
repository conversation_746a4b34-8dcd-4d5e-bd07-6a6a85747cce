import ApiClient from 'http/ApiClient'
import OrgBalanceConfig from 'model/deposit/store/OrgBalanceConfig'
import OrgBalanceConfigCreateRequest from 'model/deposit/store/OrgBalanceConfigCreateRequest'
import OrgBalanceConfigFilter from 'model/deposit/store/OrgBalanceConfigFilter'
import OrgBalanceConfigModifyRequest from 'model/deposit/store/OrgBalanceConfigModifyRequest'
import Response from 'model/common/Response'

let operator = JSON.parse(sessionStorage.getItem('vuex') as string)?.loginInfo.user?.account
export default class OrgBalanceConfigApi {
  /**
   * 新建门店储值设置
   * 新建门店储值设置。
   * 
   */
  static create(body: OrgBalanceConfigCreateRequest): Promise<Response<void>> {
    body.operator = operator
    return ApiClient.server().post(`/v1/orgBalance/config/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入门店储值设置
   * 导入门店储值设置。
   * 
   */
  static importConfig (body: any): Promise<Response<void>> {
    body.operator = operator
    return ApiClient.server().post(`/v1/orgBalance/config/importConfig`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改门店储值设置
   * 修改门店储值设置。
   * 
   */
  static modify(body: OrgBalanceConfigModifyRequest): Promise<Response<void>> {
    body.operator = operator
    return ApiClient.server().post(`/v1/orgBalance/config/modify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询门店储值设置
   * 查询门店储值设置。
   * 
   */
  static query(body: OrgBalanceConfigFilter): Promise<Response<OrgBalanceConfig[]>> {
    return ApiClient.server().post(`/v1/orgBalance/config/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
