import MarketingCenterApi from 'http/marketingcenter/MarketingCenterApi'
import RSMarketingCenter from 'model/common/RSMarketingCenter'
import RSMarketingCenterFilter from 'model/common/RSMarketingCenterFilter'
import { Inject, Prop, Vue, Watch } from 'vue-property-decorator'

export default abstract class AbstractSelectDialog<T> extends Vue {
  // 用户勾选的记录
  selected: T[] = []
  alwaysSelectedIds: string[] = []
  filteredSelected: T[] = []
  selectedFilter: string = ''
  checkboxList: boolean[] = []
  checkAll: boolean = false
  currentList: T[] = []
  dialogShow: boolean = false
  $refs: any
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  loading = {
    query: false
  }
  model: 'single' | 'multiple' = 'multiple'
  isSuperposition: boolean = false
  headquarters: Boolean =
    sessionStorage.getItem('headquarters') === 'true' ? true : false
  marketingCentersList: RSMarketingCenter[] = []
  marketCenter: any = sessionStorage.getItem('marketCenter')
  enableMultiMarketingCenter: Boolean =
    sessionStorage.getItem('isMultipleMC') == '1'

  @Inject({
    default: 999999999,
    from: 'maxLimit'
  })
  maxLimit: Number

  @Inject({
    from: 'showAll',
    default: false
  })
  showAll: Boolean

  @Inject({
    from: 'onlyLimitCoupon',
    default: false
  })
  onlyLimitCoupon: Boolean //maxLimit是否只限制券选择框

  @Inject({
    from: 'isFilterOnlyName',
    default: false
  })
  isFilterOnlyName: Boolean //右侧搜索框是否只根据name过滤

  @Prop({ default: null}) propMaxLimit: Nullable<number>;  //以prop形式传入的数量限制，优先级比maxLimit高

  @Watch('dialogShow')
  watchDialogShow(value: boolean) {
    if (value) {
      this.getMarketCenter()
      this.doSearch()
    } else {
      this.doReset()
      this.selectedFilter = ''
      this.selected = []
      this.checkAll = false
      for (let i = 0; i < this.checkboxList.length; i++) {
        this.checkboxList[i] = false
      }
    }
  }

  getMarketCenter() {
    let query = new RSMarketingCenterFilter()
    MarketingCenterApi.query(query)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.marketingCentersList = resp.data
          if (this.headquarters === false && this.showAll === false) {
            let filteredMarketCenters: RSMarketingCenter[] = []
            // @ts-ignore
            this.marketingCentersList.forEach((item: RSMarketingCenter) => {
              if (item.headquarters === true) {
                filteredMarketCenters.push(item)
              } else if (
                item.headquarters === false &&
                item.marketingCenter!.id === this.marketCenter
              ) {
                filteredMarketCenters.push(item)
              }
            })
            this.marketingCentersList = filteredMarketCenters
          }
        }
      })
      .catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
  }

  filterSelected() {
    if (this.selectedFilter) {
      this.filteredSelected = this.selected.filter((e: T) => {
        return (
          (this.isFilterOnlyName ? false : this.getId(e).indexOf(this.selectedFilter) > -1) ||
          this.getName(e).indexOf(this.selectedFilter) > -1
        )
      })
    } else {
      this.filteredSelected = this.selected
    }
  }

  delItem(item: T, index: number) {
    this.selected.splice(this.selected.indexOf(item), 1)
    this.setSelected()
    this.filterSelected()
  }

  delAllItem() {
    this.selected.splice(0, this.selected.length)
    this.setSelected()
    this.filterSelected()
  }

  open(
    selected: T[],
    model: 'single' | 'multiple' = 'multiple',
    alwaysSelectedIds: T[] = [],
    isSuperposition: boolean
  ) {
    this.model = model
    this.selected = JSON.parse(JSON.stringify(selected))
    this.alwaysSelectedIds = JSON.parse(JSON.stringify(alwaysSelectedIds))
    this.isSuperposition = isSuperposition
    this.dialogShow = true
  }

  doSearch() {
    this.page.currentPage = 1
    this.query()
  }

  doReset() {
    this.page.currentPage = 1
    this.reset()
    this.query()
  }

  doModalClose() {
    this.dialogShow = false
    this.$emit('summit', this.selected)
  }

  doCancel() {
    this.dialogShow = false
    this.selected = []
  }

  handleCurrentChange(val: number) {
    this.page.currentPage = val
    this.query()
  }

  handleSizeChange(val: number) {
    this.page.size = val
    this.query()
  }

  abstract reset(): void

  abstract getId(ins: T): string

  abstract getName(ins: T): string

  abstract queryFun(): Promise<any>

  abstract getResponseData(response: any): any

  checkable(event: boolean, index: number) {
    // 判断这个选项能不能被勾选, 交给子类重写
    return true
  }

  getResponseTotal(response: any) {
    return response.total
  }

  query() {
    this.loading.query = true
    this.queryFun()
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.currentList = this.getResponseData(resp)
          this.page.total = this.getResponseTotal(resp)
          this.setSelected()
          this.filterSelected()
        } else {
          throw new Error(resp.msg)
        }
      })
      .catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
      .finally(() => {
        this.loading.query = false
      })
  }

  setSelected() {
    let ids = this.selected.map((e: any) => this.getId(e))
    this.checkboxList = []
    for (let i = 0; i < this.currentList.length; i++) {
      this.checkboxList.push(ids.indexOf(this.getId(this.currentList[i])) > -1)
    }
    this.checkAll =
      this.checkboxList.length > 0 &&
      this.checkboxList.filter((e: boolean) => e).length ===
        this.checkboxList.length
  }

  doCheckRow(index: number) {
    let event = (this.checkboxList[index] = !this.checkboxList[index])
    this.doCheck(event, index)
  }

  doCheck(event: boolean, index: number) {
    if (
      this.alwaysSelectedIds.indexOf(this.getId(this.currentList[index])) > -1
    ) {
      return
    }
    if (!this.checkable(event, index)) {
      return
    }
    let ids = this.selected.map((e) => this.getId(e))
    if (event) {
      if (ids.indexOf(this.getId(this.currentList[index])) === -1) {
        if (this.model === 'multiple') {
          let trueCount = this.selected.length
          if (
            this.model === 'multiple' &&
            trueCount as Number >= (this.propMaxLimit ? this.propMaxLimit :this.maxLimit) &&
            ((this.onlyLimitCoupon === true &&
              this.$vnode.data!.ref === 'couponTemplate') ||
              this.onlyLimitCoupon === false)
          ) {
            let msg = this.formatI18n('最多选择{0}个')
            msg = msg.replace(/\{0\}/g, ' ' + (this.propMaxLimit ? this.propMaxLimit : this.maxLimit).toString() + ' ')
            this.$message.warning(msg)
            this.$set(this.checkboxList, index, false)
          } else {
            this.selected.push(this.currentList[index])
          }
        } else {
          this.selected = []
          this.selected.push(this.currentList[index])
          if (this.checkboxList && this.checkboxList.length > 0) {
            this.checkboxList.forEach((item: any, pos: number) => {
              if (index !== pos) {
                this.$set(this.checkboxList, pos, false)
              }
            })
          }
        }
      }
    } else {
      this.selected = this.selected.filter(
        (e: T) => this.getId(e) !== this.getId(this.currentList[index])
      )
    }
    this.checkAll =
      this.checkboxList.filter((e: boolean) => e).length ===
      this.checkboxList.length
    this.filterSelected()
  }

  doCheckAll(event: boolean) {
    for (let i = 0; i < this.checkboxList.length; i++) {
      if (
        this.alwaysSelectedIds &&
        this.alwaysSelectedIds.length > 0 &&
        this.alwaysSelectedIds.indexOf(this.getId(this.currentList[i])) > -1
      ) {
        continue
      }
      this.checkboxList[i] = event
      this.doCheck(event, i)
    }
    this.filterSelected()
    this.$forceUpdate()
  }

  private isAlwaysSelected(id: string) {
    return this.alwaysSelectedIds.indexOf(id) > -1
  }
}
