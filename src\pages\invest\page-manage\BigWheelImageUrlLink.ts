/*
 * @Author: 黎钰龙
 * @Date: 2024-06-26 13:46:05
 * @LastEditTime: 2024-06-26 13:51:02
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\BigWheelImageUrlLink.ts
 * 记得注释
 */
import EnvUtil from "util/EnvUtil";
import { BigWheelImageVO } from "./BigWheelImageVO";

let ossImageBaseUrl = EnvUtil.getOssUrl() + 'rumba-oss-server/rs/oss/v1/phoenix/o/images_config_weixin_bigwheel_'
// const ossImageBaseUrl = 'https://phoenix-test.hd123.com/rumba-oss-server/rs/oss/v1/phoenix/o/images_config_weixin_bigwheel_'
function generateImgeUrl () {
    let obj:any = {}
    Object.keys(BigWheelImageVO).forEach((key: string) => {
        obj[key] = ossImageBaseUrl + key + '.png'
    })
    return obj
}
const bigWheelImageLink = generateImgeUrl()
export default bigWheelImageLink