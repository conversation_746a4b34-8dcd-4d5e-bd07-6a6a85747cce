// 交易分析报表概览
export default class OrgTradeAnalysisSummary {
  // 交易金额
  tradeTotal: Nullable<number> = null
  // 交易金额环比
  tradeTotalRingGrowth: Nullable<number> = null
  // 交易数量
  tradeGoodsQty: Nullable<number> = null
  // 交易金额环比
  tradeGoodsQtyRingGrowth: Nullable<number> = null
  // 客单数
  tradeQty: Nullable<number> = null
  // 客单数环比
  tradeQtyRingGrowth: Nullable<number> = null
  // 客单价
  perPrice: Nullable<number> = null
  // 客单价环比
  perPriceRingGrowth: Nullable<number> = null
  // 客单件
  perQty: Nullable<number> = null
  // 客单件环比
  perQtyRingGrowth: Nullable<number> = null
  // 件单价
  perQtyPrice: Nullable<number> = null
  // 客单件环比
  perQtyPriceRingGrowth: Nullable<number> = null
  // 会员交易金额
  memberTradeTotal: Nullable<number> = null
  // 会员交易金额环比
  memberTradeTotalRingGrowth: Nullable<number> = null
  // 会员交易金额占比
  memberTradeTotalRatio: Nullable<number> = null
  // 会员交易金额占比环比
  memberTradeTotalRatioRingGrowth: Nullable<number> = null
  // 会员交易数量
  memberTradeGoodsQty: Nullable<number> = null
  // 会员交易数量环比
  memberTradeGoodsQtyRingGrowth: Nullable<number> = null
  // 会员客单数
  memberTradeQty: Nullable<number> = null
  // 会员客单数环比
  memberTradeQtyRingGrowth: Nullable<number> = null
  // 会员客单数
  memberTradeQtyRatio: Nullable<number> = null
  // 会员客单数环比
  memberTradeQtyRatioRingGrowth: Nullable<number> = null
  // 会员客单价
  memberPerPrice: Nullable<number> = null
  // 会员客单价环比
  memberPerPriceRingGrowth: Nullable<number> = null
  // 会员客单件
  memberPerQty: Nullable<number> = null
  // 会员客单件环比
  memberPerQtyRingGrowth: Nullable<number> = null
  // 会员件单价
  memberPerQtyPrice: Nullable<number> = null
  // 会员客单件环比
  memberPerQtyPriceRingGrowth: Nullable<number> = null
}