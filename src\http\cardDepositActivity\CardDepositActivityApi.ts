import ApiClient from 'http/ApiClient'
import CardDepositActivity from 'model/cardDepositActivity/CardDepositActivity'
import CardDepositActivityFilter from 'model/cardDepositActivity/CardDepositActivityFilter'
import CardDepositActivityQueryResult from 'model/cardDepositActivity/CardDepositActivityQueryResult'
import DepositEvaluation from 'model/deposit/activity/DepositEvaluation'
import Response from 'model/common/Response'

export default class CardDepositActivityApi {
  /**
   * 审核活动
   * 审核活动。
   * 
   */
  static audit(activityId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/card-deposit-activity/audit/${activityId}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量审核储值活动
   * 批量审核储值活动。
   * 
   */
  static batchAudit(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/card-deposit-activity/batch/audit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量删除储值活动
   * 批量删除储值活动。
   * 
   */
  static batchRemove(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/card-deposit-activity/batch/remove`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量终止储值活动
   * 批量终止储值活动。
   * 
   */
  static batchStop(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/card-deposit-activity/batch/stop`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 检查冲突
   * 检查冲突。
   * 
   */
  static checkConflict(body: CardDepositActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/card-deposit-activity/checkConflict`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 创建活动
   * 创建活动。
   * 
   */
  static create(body: CardDepositActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/card-deposit-activity/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 活动效果评估
   * 活动效果评估。
   * 
   */
  static evaluate(activityId: string): Promise<Response<DepositEvaluation>> {
    return ApiClient.server().get(`/v1/card-deposit-activity/evaluate/${activityId}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询活动
   * 查询活动。
   * 
   */
  static info(activityId: string): Promise<Response<CardDepositActivity>> {
    return ApiClient.server().get(`/v1/card-deposit-activity/info/${activityId}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改活动
   * 修改活动。
   * 
   */
  static modify(body: CardDepositActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/card-deposit-activity/modify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询活动
   * 查询活动。
   * 
   */
  static query(body: CardDepositActivityFilter): Promise<Response<CardDepositActivityQueryResult>> {
    return ApiClient.server().post(`/v1/card-deposit-activity/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除活动
   * 删除活动。
   * 
   */
  static remove(activityId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/card-deposit-activity/remove/${activityId}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存并审核活动
   * 保存并审核活动。
   * 
   */
  static saveAndAudit(body: CardDepositActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/card-deposit-activity/saveAndAudit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 终止活动
   * 终止活动。
   * 
   */
  static stop(activityId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/card-deposit-activity/stop/${activityId}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

}
