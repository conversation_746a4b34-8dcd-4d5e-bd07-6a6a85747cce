import { Component, Vue, Watch } from 'vue-property-decorator'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import FormItem from 'cmp/formitem/FormItem.vue'
import CardPicList from 'cmp/cardpiclist/CardPicList.vue'
import GoodsScopeEx from 'cmp/goodsscope/GoodsScopeEx.vue'
import CardTemplate from 'model/card/template/CardTemplate'
import CardTemplateApi from 'http/card/template/CardTemplateApi'
import PrepayAccountApi from 'http/prepay/account/PrepayAccountApi'
import IdName from 'model/common/IdName'
import TemplateValidity from 'model/card/template/TemplateValidity'
import EnvUtil from 'util/EnvUtil'
import PrePayConfigApi from 'http/prepay/config/PrePayConfigApi'
import DateUtil from 'util/DateUtil'
import { StoreRangeType } from 'model/common/StoreRangeType'
import StoreRange from 'model/common/StoreRange'
import ActiveStore from "cmp/activestore/ActiveStore";
import EditType from "common/EditType";
import PrepayCardTplPermission from "pages/deposit/prepaycard/prepaycardtpl/PrepayCardTplPermission";
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import I18nPage from 'common/I18nDecorator'
import ChooseGoodsRange from 'cmp/coupontenplate/cmp/chooseGoodsRange'
import CountingCardGoods from 'model/card/template/CountingCardGoods'
import CountingCardGoodsGroup from 'model/card/template/CountingCardGoodsGroup'
import PageConfigApi from 'http/pageConfig/PageConfigApi'
import CardTemplateExtInfo from 'model/card/template/CardTemplateExtInfo'
import CouponThreshold from 'model/common/CouponThreshold'
import { ThresholdType } from 'model/common/ThresholdType'
import StackCardPromotion from 'cmp/cardTemplate/StackCardPromotion.vue';
import BCouponTemplatePromotion from 'model/common/BCouponTemplatePromotion'
import BrowserMgr from "mgr/BrowserMgr";
import StackPromotion from "cmp/coupontenplate/cmp/StackPromotion.vue"
import GoodsFavRule from 'model/common/GoodsFavRule'

class TempValidityInfo {
  type: string = 'RALATIVE'
  unit: string = '天'
  num: Nullable<number> = null
  endDate: Nullable<Date> = null
}

class PromoInfo {
  // 是否参与促销叠加，true表示叠加优惠，false表示不可参与叠加
  excludePromotion: Nullable<boolean> = true;
  // 促销叠加类型 ALL("全部"),PART("部分")
  promotionSuperpositionType: Nullable<"ALL" | "PART"> = "ALL";
  // 促销单信息
  promotion: Nullable<BCouponTemplatePromotion> = new BCouponTemplatePromotion();
   // 叠加优惠， 字段类型与取值同券模版-现金券
  goodsFavRules:  Nullable<GoodsFavRule[]> = null;
}

@I18nPage({
  prefix: [
    '/公用/券模板',
    '/储值/预付卡/卡模板/公共/卡类型',
    '/储值/预付卡/卡模板/编辑页面',
    '/储值/预付卡/卡模板/编辑页面校验',
    '/储值/预付卡/卡模板/详情页面',
    '/卡/卡管理/卡介质',
    '/公用/表单校验',
    '/营销/积分活动/积分活动/商品满额加送积分/表单校验/提示信息',
    '/储值/预付卡/预付卡查询/列表页面',
    '/营销/券礼包活动/新建注册发大礼包'
  ],
  auto: true
})

@Component({
  name: 'PrepayCardTplEdit',
  components: {
    SubHeader,
    FormItem,
    CardPicList,
    GoodsScopeEx,
    ActiveStore,
    BreadCrume,
    ChooseGoodsRange,
    StackCardPromotion,
    StackPromotion
  }
})
export default class PrepayCardTplEdit extends Vue {
  goodsMatchRuleMode: string = "barcode"
  isMoreMarketing: boolean = false // 是否开启多营销中心 true 开启 false 不开启
  uploadHeaders: any = {}
  panelArray: any = []
  get uploadUrl() {
    return EnvUtil.getServiceUrl() + 'v1/upload/upload'
  }
  bizTable: any = {}
  hasOptionPermission: any
  editType: string = EditType.CREATE
  number: string = ''
  detail: CardTemplate = new CardTemplate()
  enableMultipleAccount: boolean = false
  permission = new PrepayCardTplPermission()
  canChooseItems: any[] = []
  goodsRange: CountingCardGoodsGroup = new CountingCardGoodsGroup() //适用商品
  // --临时变量--
  faceAmount: Nullable<number> = null
  accounts: IdName[] = []
  validityInfo: TempValidityInfo = new TempValidityInfo()
  cardAttributeFix: boolean = false //适用商品、适用门店、支付是否需要密码、转出是否需要密码，是否传固定值（后端固定）
  showCardExtRule: boolean = false  //是否展示拓展规则
  $refs: any
  rules: any = {}
  promotionInfo: PromoInfo = new PromoInfo()


  @Watch('goodsRange', { deep: true })
  handle(value: CountingCardGoodsGroup) {
    if (!this.detail.countingCardGoodsGroups?.length) {
      this.detail.countingCardGoodsGroups = []
      this.detail.countingCardGoodsGroups.push(new CountingCardGoodsGroup())
    }
    this.detail.countingCardGoodsGroups[0] = JSON.parse(JSON.stringify(value))
    this.doCanChooseItemChange()
  }

  pickerOptions: any = {
    disabledDate(time: any) {
      return time.getTime() < Date.now();
    },
  }

  get isNeedPayPwd() {
    const flag1 = ['GIFT_CARD', 'RECHARGEABLE_CARD', 'COUNTING_CARD'].indexOf(this.detail.cardTemplateType!) > -1
    const flag2 = this.detail.cardMedium !== 'online'
    return flag1 && flag2
  }

  get isNeedTransPwd() {
    const flag1 = ['GIFT_CARD', 'RECHARGEABLE_CARD'].indexOf(this.detail.cardTemplateType!) > -1
    const flag2 = this.detail.cardMedium !== 'online'
    return flag1 && flag2
  }

  // 判断是否是礼品卡或者储值卡
  get isGiftOrRechargeable() {
    return ['GIFT_CARD', 'RECHARGEABLE_CARD'].indexOf(this.detail.cardTemplateType!) > -1
  }

  get isShowLength() {
    if (this.detail.cardTemplateType === 'IMPREST_CARD') {
      return false
    } else {
      return true
    }
  }

  created() {
    let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
    if (sysConfig && this.goodsMatchRuleMode) {
      this.goodsMatchRuleMode = sysConfig.goodsMatchRuleMode;
    }
    if (sessionStorage.getItem('isMultipleMC') == '1') {
      this.isMoreMarketing = true
    } else {
      this.isMoreMarketing = false
    }
    this.getConfig()
    this.initBizTable()
    this.initPanelArray()
    this.initUploadHeaders()
    this.initEditType()
    this.initData()
    this.getPrePermission()
    this.listEnableAccountType()
    this.initRules()
  }
  mounted() {
    if (sessionStorage.getItem('headquarters') === 'true') {
      this.detail.stores!.storeRangeType = 'ALL'
    }
    this.$refs.stackPromotion?.doBindValue(this.promotionInfo)
  }

  getConfig() {
    PrepayAccountApi.cardAttributeFix().then((res) => {
      this.cardAttributeFix = res.data || false
    })
    PageConfigApi.getConfig().then((res) => {
      if (res.code === 2000) {
        this.showCardExtRule = res.data?.showCardExtRule || false
        if (this.showCardExtRule) {
          this.initThreshold()
        }
      }
    })
  }

  get isCardAttributeFix() {
    return this.cardAttributeFix && ['GIFT_CARD', 'RECHARGEABLE_CARD'].indexOf(this.detail.cardTemplateType || '') > -1
  }

  private initBizTable() {
    this.bizTable = [
      {
        name: this.i18n('礼品卡'),
        pay: true,
        encharge: false,
        out: true,
        in: false,
        isShow: this.hasOptionPermission('/卡/卡管理/卡模板/礼品卡', '卡模板维护'),
      },
      {
        name: this.formatI18n('/储值/预付卡/卡模板/公共/卡类型/充值卡'),
        pay: false,
        encharge: false,
        out: true,
        in: false,
        isShow: this.hasOptionPermission('/卡/卡管理/卡模板/充值卡', '卡模板维护'),
      },
      {
        name: this.formatI18n('/储值/预付卡/卡模板/公共/卡类型/储值卡'),
        pay: true,
        encharge: true,
        out: true,
        in: true,
        isShow: this.hasOptionPermission('/卡/卡管理/卡模板/储值卡', '卡模板维护'),
      },
      {
        name: this.i18n('次卡'),
        pay: true,
        encharge: false,
        out: false,
        in: false,
        isShow: this.hasOptionPermission('/卡/卡管理/卡模板/次卡', '卡模板维护'),
      }
    ].filter(item => item.isShow)
  }

  private initPanelArray() {
    this.panelArray = [
      {
        name: this.formatI18n('/储值/预付卡/卡模板/列表页面/卡模板'),
        url: 'prepay-card-tpl'
      },
      {
        name: this.editType === 'edit' ? this.formatI18n('/储值/预付卡/卡模板/编辑页面/修改卡模板') : this.formatI18n('/储值/预付卡/卡模板/编辑页面/新建卡模板'),
        url: ''
      }
    ]
  }

  private hasCardMediumPermission(type: 'online' | 'bar' | 'mag' | 'rfic'| 'ic') {
    const sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
    const permissionList = sysConfig.cardtype || []
    return permissionList.indexOf(type) > -1
  }

  private initUploadHeaders() {
    let locale = sessionStorage.getItem('locale')
    this.uploadHeaders = {
      locale: locale ? locale : "zh_CN",
      time_zone: new Date().getTimezoneOffset(),
      marketingCenter: sessionStorage.getItem("marketCenter"),
    };
    const authorization = EnvUtil.getUniAuthorization()
    if (authorization) {
      this.uploadHeaders.authorization = authorization
    }
  }

  private initEditType() {
    let editType = this.$route.query.editType as string
    if (editType) {
      this.editType = editType as string
    }
    if (editType === 'edit') {
      this.panelArray[1].name = this.formatI18n('/储值/预付卡/卡模板/编辑页面', '修改卡模板')
    }
  }

  private initRules() {
    this.rules = {
      number: [
        { required: true, message: this.formatI18n('/储值/预付卡/卡模板/编辑页面校验/请输入卡模板号'), trigger: ['change', 'blur'] },
        { min: 1, max: 80, message: this.formatI18n('/储值/预付卡/卡模板/编辑页面校验/长度在80个字符以内'), trigger: ['change', 'blur'] },
        {
          validator: (rule: any, value: string, callback: any) => {
            if (value.search(/[^\d]/g) !== -1) {
              callback(new Error(this.formatI18n('/权益/等级/单笔消费升级激励/编辑页/请输入数字')))
              return
            }
            callback()
          }
        }
      ],
      name: [
        { required: true, message: this.formatI18n('/储值/预付卡/卡模板/编辑页面校验/请输入卡模板名称'), trigger: ['change', 'blur'] },
        { min: 1, max: 20, message: this.formatI18n('/储值/预付卡/卡模板/编辑页面校验/长度在20个字符以内'), trigger: ['change', 'blur'] }
      ],
      count: [
        { required: true, message: this.i18n('请输入次数'), trigger: ['change', 'blur'] },
      ],
      accountType: [
        { required: true, message: this.formatI18n('/储值/预付卡/卡模板/编辑页面校验/请选择账户类型'), trigger: ['change', 'blur'] }
      ],
      cardTemplateType: [
        { required: true, message: this.formatI18n('/储值/预付卡/卡模板/编辑页面校验/请选择卡类型'), trigger: ['change', 'blur'] }
      ],
      cardPictureUrls: [
        {
          validator: (rule: any, value: any, callback: any) => {
            if (this.detail.cardTemplateType === 'ONLINE_GIFT_CARD' && this.detail.cardPictureUrls.length === 0) {
              callback(new Error(this.formatI18n('/储值/预付卡/卡模板/编辑页面校验/请上传卡样')))
            }
            callback()
          }, trigger: ['change', 'blur']
        }
      ],
      faceAmounts: [
        {
          validator: (rule: any, value: [], callback: any) => {
            if (value.length === 0) {
              callback(new Error(this.formatI18n('/储值/预付卡/卡模板/编辑页面校验/请输入卡面额')))
              return
            }
            for (let item of value) {
              let it = item as any
              if (it === null || it === '') {
                callback(new Error(this.formatI18n('/储值/预付卡/卡模板/编辑页面校验/请补全卡面额')))
                return
              }
            }
            let valueStrArr: string[] = [] // 查详情时是数字，但输入是字符串，需要统一
            value.forEach((e) => valueStrArr.push(e + ''))
            if (new Set(valueStrArr).size !== value.length) {
              callback(new Error(this.formatI18n('/储值/预付卡/卡模板/编辑页面校验/卡面额不能重复')))
              return
            }
            callback()
          }, trigger: ['change', 'blur']
        }
      ],
      days: [
        {
          validator: (rule: any, value: StoreRange, callback: any) => {
            if (this.validityInfo.type === 'RALATIVE' && !this.validityInfo.num) {
              callback(new Error(this.i18n('请填写必填项')))
            }
            callback()
          }, trigger: ['change', 'blur']
        }
      ],
      timeRange: [
        {
          validator: (rule: any, value: StoreRange, callback: any) => {
            if (this.validityInfo.type === 'FIXED' && !this.validityInfo.endDate) {
              callback(new Error(this.i18n('请填写必填项')))
            }
            callback()
          }, trigger: ['change', 'blur']
        }
      ],
      stores: [
        {
          validator: (rule: any, value: StoreRange, callback: any) => {
            if (value.storeRangeType === StoreRangeType.PART && value.stores.length === 0) {
              callback(new Error(this.formatI18n('/营销/积分活动/积分活动/商品满额加送积分/表单校验/提示信息/请选择门店范围')))
            }
            callback()
          }, trigger: ['change', 'blur']
        }
      ],
      useGoods: [
        {
          validator: (rule: any, value: StoreRange, callback: any) => {
            if (this.detail.cardTemplateType !== 'COUNTING_CARD') callback()
            if (this.goodsRange.exchangeGoods.some(item => { return !item.qty }) || !this.goodsRange.exchangeGoods.length || !this.goodsRange.exchangeQty) {
              callback(new Error(this.i18n('请填写必填项')))
            }
            callback()
          }, trigger: ['change', 'blur']
        }
      ],
      cardCodeLength: [
        {
          validator: (rule: any, value: number, callback: any) => {
            if (!value) {
              callback(new Error(this.i18n('请填写必填项')))
            }
            callback()
          }, trigger: ['change', 'blur']
        }
      ],
      prefix: [
        {
          validator: (rule: any, value: any, callback: any) => {
            let re = /^[0-9a-zA-Z]*$/g; // 判断字符串是否为数字和字母组合
            if (!re.test(value)) {
              callback(this.i18n("请输入数字或字母"));
            } else {
              callback();
            }
          },
          tirgger: "blur",
        },
      ],
      price: [
        {
          required: true,
          validator: (rule: any, value: any, callback: any) => {
            if (!value) {
              callback(new Error(this.i18n('请填写必填项')))
            }
            if (this.detail.faceAmounts.length && Number(value) > Number(this.detail.faceAmounts[0]) && this.detail.cardTemplateType !== 'COUNTING_CARD') {
              callback(new Error(this.i18n('价格不能大于卡面额')))
            }
            callback()
          },
          trigger: ['change', 'blur']
        }
      ],
      cardMedium: [
        { required: true, message: this.i18n('请选择卡介质'), trigger: ['change', 'blur'] }
      ]
    }
  }

  private initData() {
    this.detail = new CardTemplate()
    this.detail.faceAmounts.push(null)
    this.detail.cardCodeLength = 12
    this.validityInfo = new TempValidityInfo()
  }

  private initThreshold() {
    this.detail.extInfo = new CardTemplateExtInfo()
    this.detail.extInfo.threshold = new CouponThreshold()
    this.detail.extInfo.threshold.thresholdType = ThresholdType.NONE  //初始化数据时是无门槛
  }

  private clearThreshold() {
    this.detail.extInfo = null
  }

  private handleThresholdChange(value: number) {
    if (this.detail.extInfo?.threshold) {
      if (value > 0) {
        this.detail.extInfo.threshold.thresholdType = ThresholdType.NONREUSEABLE
      } else {
        this.detail.extInfo.threshold.thresholdType = ThresholdType.NONE
        this.detail.extInfo.threshold.value = null
        this.detail.extInfo.threshold.threshold = null
      }
    }
  }

  private beforeAvatarUpload(file: any) {
    const isJPG = ['image/jpeg', 'image/png', 'image/gif'].indexOf(file.type) > -1;
    const isLt2M = file.size / 1024 < 300;

    if (!isJPG) {
      this.$message.error(this.formatI18n('/储值/预付卡/卡模板/编辑页面/上传图片只能是JPG/PNG/JPEG/GIF格式!'));
      return false
    }
    if (!isLt2M) {
      this.$message.error(this.formatI18n('/储值/预付卡/卡模板/编辑页面/上传图片大小不能超过300KB'));
      return false
    }
    return true
  }

  private checkValidityInfoNum(validityInfo: any) {
    if (validityInfo.num < 1) {
      validityInfo.num = 1
    }
  }

  private listEnableAccountType() {
    PrepayAccountApi.listEnableAccountType().then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.accounts = resp.data
        if (this.editType !== EditType.CREATE) {
          this.number = this.$route.query.number as any
          this.getDetail()
        } else {
          this.initCardTemplateType()
          this.detail.accountType = this.accounts[0]
          this.detail.cardMedium = this.checkCardMediumPermission()
        }
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  //根据当前卡介质的权限，给卡介质默认值
  checkCardMediumPermission() {
    const sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
    const permissionList = sysConfig.cardtype || []
    if (permissionList.indexOf('mag') > -1) {
      return 'mag'
    } else if (permissionList.indexOf('online') > -1) {
      return 'online'
    } else if (permissionList.indexOf('bar') > -1) {
      return 'bar'
    } else if (permissionList.indexOf('rfic') > -1) {
      return 'rfic'
    } else if (permissionList.indexOf('ic') > -1) {
      return 'ic'
    }
  }

  private initCardTemplateType() {
    if (this.hasOptionPermission('/卡/卡管理/卡模板/储值卡', '卡模板维护') && (this.$route.query.cardType === 'RECHARGEABLE_CARD' || this.$route.query.cardType === 'ALL')) {
      this.detail.cardTemplateType = 'RECHARGEABLE_CARD'
      return
    }
    if (this.hasOptionPermission('/卡/卡管理/卡模板/礼品卡', '卡模板维护') && this.$route.query.cardType === 'GIFT_CARD') {
      this.detail.cardTemplateType = 'GIFT_CARD'
      return
    }
    if (this.hasOptionPermission('/卡/卡管理/卡模板/充值卡', '卡模板维护') && this.$route.query.cardType === 'IMPREST_CARD') {
      this.detail.cardTemplateType = 'IMPREST_CARD'
      return
    }
    if (this.hasOptionPermission('/卡/卡管理/卡模板/次卡', '卡模板维护') && this.$route.query.cardType === 'COUNTING_CARD') {
      this.detail.cardTemplateType = 'COUNTING_CARD'
      return
    }
  }

  private getDetail() {
    return CardTemplateApi.info(this.number).then((resp: any) => {
      if (resp.code === 2000) {
        Object.assign(this.detail, resp.data)
        if (this.detail.countingCardGoodsGroups) {
          this.goodsRange = this.detail.countingCardGoodsGroups[0] || new CountingCardGoodsGroup()  //次卡的适用商品
        }
        //储值卡 兼容extInfo为null的情况
        if (this.detail.cardTemplateType === 'RECHARGEABLE_CARD' && !this.detail.extInfo) {
          this.initThreshold()
        }
        this.$forceUpdate()
        if (this.editType === EditType.COPY) {
          this.generateNumber()
        }
        // 写入账户类型
        for (let account of this.accounts) {
          if (this.detail.accountType && this.detail.accountType.id === account.id) {
            this.detail.accountType = account
          }
        }
        // 写入有效期
        if (this.detail.validityInfo) {
          this.validityInfo.type = this.detail.validityInfo.validityType as any
          if (this.validityInfo.type === 'FIXED') {
            this.validityInfo.endDate = this.detail.validityInfo.endDate as any
          } else if (this.validityInfo.type === 'RALATIVE') {
            if (this.detail.validityInfo.validityDays !== null) {
              this.validityInfo.unit = '天'
              this.validityInfo.num = this.detail.validityInfo.validityDays as any
            }
            if (this.detail.validityInfo.validityYears !== null) {
              this.validityInfo.unit = '年'
              this.validityInfo.num = this.detail.validityInfo.validityYears as any
            }
          }
        }
        this.initPromotionInfo()
      } else {
        throw new Error(resp.msg)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  //写入叠加促销
  initPromotionInfo() {
    this.promotionInfo.excludePromotion = this.detail.excludePromotion
    this.promotionInfo.promotion = this.detail.promotion
    this.promotionInfo.promotionSuperpositionType = this.detail.promotionSuperpositionType
    this.promotionInfo.goodsFavRules = this.detail.goodsFavRules
    this.$refs.stackPromotion?.doBindValue(this.promotionInfo)
  }

  //叠加促销恢复默认值
  resetPromotionInfo() {
    this.detail.excludePromotion = true
    this.detail.promotion = new BCouponTemplatePromotion()
    this.detail.promotion.append = false
    this.detail.promotion.proNums = []
    this.detail.promotion.templateNumber = this.detail.number
    this.detail.promotionSuperpositionType = 'ALL'
    this.detail.goodsFavRules = null
  }

  private onUploadSuccess(response: any, file: any, fileList: any) {
    if (response && response.code === 2000) {
      this.$message.success(this.formatI18n('/储值/预付卡/卡模板/编辑页面/上传成功'))
      this.detail.cardPictureUrls.push(response.data.url)
      this.$refs.detail.validateField('cardPictureUrls')
    } else {
      this.$message.error(response.msg)
    }
  }

  private handleValidInfo() {
    this.detail.validityInfo = new TemplateValidity()
    this.detail.validityInfo.delayEffectDays = 0
    this.detail.validityInfo.validityType = this.validityInfo.type
    if (this.validityInfo.type === 'FIXED') {
      this.detail.validityInfo.beginDate = DateUtil.format(DateUtil.nowDayTime(), 'yyyy-MM-dd HH:mm:ss')
      // @ts-ignore
      let endDate = DateUtil.parseDate(DateUtil.format(this.validityInfo.endDate, 'yyyy-MM-dd'))
      this.detail.validityInfo.endDate = DateUtil.format(endDate.getTime() + 24 * 60 * 60 * 1000 - 1000, 'yyyy-MM-dd HH:mm:ss')
    }
    if (this.validityInfo.type === 'RALATIVE') {
      if (this.validityInfo.unit === '天') {
        this.detail.validityInfo.validityDays = this.validityInfo.num
      }
      if (this.validityInfo.unit === '年') {
        this.detail.validityInfo.validityYears = this.validityInfo.num
      }
    }
  }

  handlePromotionInfo() {
    this.$refs.stackPromotion?.validate()
    this.detail.promotion = JSON.parse(JSON.stringify(this.promotionInfo.promotion))
    this.detail.excludePromotion = this.promotionInfo.excludePromotion
    this.detail.promotionSuperpositionType = this.promotionInfo.promotionSuperpositionType
    this.detail.goodsFavRules = this.promotionInfo.goodsFavRules
    if (['COUNTING_CARD', 'IMPREST_CARD'].indexOf(this.detail.cardTemplateType!) > -1) {
      this.resetPromotionInfo()
      this.detail.payObtainPoints = null
    }
  }

  private handlePasswd() {
    //如果页面上不显示‘支付时是否要输密码’或‘转出时是否需要密码’，则保存时对应的字段都传false
    if (!this.isNeedPayPwd) {
      this.detail.enablePayPassword = false
    }
    if (!this.isNeedTransPwd) {
      this.detail.enableTransferOutPassword = false
    }
  }


  private save() {
    this.handlePasswd()
    this.handleValidInfo()
    this.handlePromotionInfo()
    if (this.detail.cardTemplateType === 'IMPREST_CARD') {
      this.detail.enableTransferOutPassword = true
    }
    Promise.all([
      this.$refs.storeScope ? this.$refs.storeScope.validate() : true,
      this.$refs.goodsScope ? this.$refs.goodsScope.validate() : true,
      this.$refs.stackPromotion ? this.$refs.stackPromotion.validate() : true,
      this.$refs.detail.validate()
    ]).then((res: any[]) => {
      if (res.filter((e) => !e).length === 0) {
        let mtd = this.editType === EditType.EDIT ? CardTemplateApi.modify : CardTemplateApi.saveNew
        if (!this.enableMultipleAccount) {
          this.detail.accountType = null
        }
        console.log('保存时的参数', this.detail);
        mtd(this.detail).then((resp: any) => {
          if (resp && resp.code === 2000) {
            this.$router.push({ name: 'prepay-card-tpl-dtl', query: { number: this.detail.number, cardType: this.detail.cardTemplateType } })
          } else {
            this.$message.error(resp.msg)
          }
        }).catch((error) => {
          if (error && error.message) {
            this.$message.error(error.message)
          }
        })
      }
    })
  }

  private generateNumber() {
    CardTemplateApi.allocateTemplateNumber().then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.detail.number = resp.data
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private addFaceAmount() {
    this.detail.faceAmounts.push(null)
  }

  private deleteFaceAmount(index: number) {
    this.detail.faceAmounts.splice(index, 1);
  }

  private getPrePermission() {
    PrePayConfigApi.get().then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.enableMultipleAccount = resp.data.enableMultipleAccount
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private onTypeChange() {
    this.$refs['detail'].validate()
    if (this.editType === EditType.CREATE) {
      let cardTemplateType = this.detail.cardTemplateType
      this.detail.cardTemplateType = cardTemplateType // 只保留卡类型
    }
    if (this.detail.cardTemplateType === 'RECHARGEABLE_CARD') {
      this.initThreshold()
    } else {
      this.clearThreshold()
    }
  }

  //当卡面额变化时，卡价格默认等于卡面额
  doChangeFaceAmount(amount: string) {
    this.detail.price = Number(amount) <= ******** && Number(amount) >= 0 ? Number(amount).toFixed(2) : Number(amount) < 0 ? '0.00' : '********.00'
  }

  private doUseCouponGoodsChange(data: CountingCardGoods[]) {
    this.goodsRange.exchangeGoods = data
  }

  //计算用券商品"X选N"下拉框
  doCanChooseItemChange() {
    this.canChooseItems = []
    this.goodsRange.exchangeGoods.forEach((item: any, index: number) => {
      const goodsLength = this.goodsRange.exchangeGoods.length
      if (index === 0) {
        this.canChooseItems.push({
          label: this.formatI18n('/公用/券模板/提货券/用券商品/全部可选'),
          value: goodsLength
        })
      } else {
        this.canChooseItems.push({
          label: `${goodsLength}${this.formatI18n('/公用/券模板/提货券/用券商品/选')}${goodsLength - index}`,
          value: goodsLength - index
        })
      }
    })
  }
  doRelativeChange() {
    this.validityInfo.endDate = '' as any
    this.$refs.detail.validateField('timeRange')
    this.$refs.detail.validateField('days')
  }

  doFixedChange() {
    this.validityInfo.num = undefined
    this.$refs.detail.validateField('timeRange')
    this.$refs.detail.validateField('days')
  }

  changeStackPromotion(params:PromoInfo){
    this.promotionInfo.excludePromotion = params.excludePromotion;
    this.promotionInfo.promotion = params.promotion;
    this.promotionInfo.goodsFavRules = params.goodsFavRules;
    this.promotionInfo.promotionSuperpositionType = params.promotionSuperpositionType;
  }
}
