import { Component, Vue } from "vue-property-decorator";
import FormItem from "cmp/formitem/FormItem.vue";
import ListWrapper from "cmp/list/ListWrapper.vue";
import I18nPage from "common/I18nDecorator";
import BreadCrume from "cmp/bread-crumb/BreadCrume";
import CouponTemplateFilter from "model/coupon/template/CouponTemplateFilter";
import StoreSelectorDialog from "cmp/selectordialogs/StoreSelectorDialog";
import CouponWriteoffFilter from "model/coupon/CouponWriteOff/CouponWriteOffFilter";
import CouponWriteoffBillApi from "http/coupon/CouponWriteOff/CouponWriteoffBillApi";

@Component({
  name: "CouponTemplateList",
  components: {
    FormItem,
    ListWrapper,
    BreadCrume,
    StoreSelectorDialog,
  },
})
@I18nPage({
  prefix: [
    "/储值/会员储值/储值充值活动/列表页面",
    "/营销/券礼包活动/券礼包活动",
    "/公用/按钮",
    "/公用/券核销",
  ],
})
export default class CouponTemplateList extends Vue {
  i18n: any;
  query: CouponWriteoffFilter = new CouponWriteoffFilter();
  selectedArr: any[] = [];
  activeName = "all";
  panelArray: any = [];
  total: number = 0;
  $refs: any;
  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10,
  };
  createBetween: Nullable<any[]> = [];
  modifyBetween: Nullable<any[]> = [];
  tableData: any = [];
  loading: Boolean = false;
  singleAll = false;
  noAuditTotal: Nullable<number> = 0
  auditTotal: Nullable<number> = 0

  get getAllCount() {
    let str = "";
    str = this.i18n("全部") + "(" + this.page.total + ")";
    return str;
  }

  get getNoAudit() {
    let str = "";
    str = this.i18n("未审核") + "(" + this.noAuditTotal + ")";
    return str;
  }

  get getAudit() {
    let str = "";
    str = this.i18n("已审核") + "(" + this.auditTotal + ")";
    return str;
  }

  get getSelectActive() {
    let str: any = this.formatI18n(
      "/营销/券礼包活动/券礼包活动",
      "已选择{0}个单据"
    );
    str = str.replace(/\{0\}/g, this.selectedArr.length);
    return str;
  }

  created() {
    this.panelArray = [
      {
        name: this.i18n("券核销单"),
        url: "",
      },
    ];
    this.queryList();
  }

  doAdd() {
    this.$router.push({ path: "coupon-write-off-edit" });
  }

  doToDtl(number: any) {
    this.$router.push({
      path: "coupon-write-off-dtl",
      query: { number: number },
    });
  }

  getStatistics() {
	CouponWriteoffBillApi.statistics(this.query).then((res) => {
		this.noAuditTotal = res.data.initalNum || 0
		this.auditTotal = res.data.effectedNum || 0
	  });
  }

  queryList() {
    this.query.createLess = this.createBetween ? this.createBetween[1] : null;
    this.query.createGreaterOrEquals = this.createBetween
      ? this.createBetween[0]
      : null;
    this.query.lastModifiedLess = this.modifyBetween
      ? this.modifyBetween[1]
      : null;
    this.query.lastModifiedGreaterOrEquals = this.modifyBetween
      ? this.modifyBetween[0]
      : null;
    this.query.page = this.page.currentPage - 1;
    this.query.pageSize = this.page.size;
    if (this.query.couponCodeEquals == "" || this.query.couponCodeEquals == null) {
      this.query.couponCodeEquals = null
    }
    if (this.query.numberEquals == "" || this.query.numberEquals == null) {
      this.query.numberEquals = null
    }
    switch (this.activeName) {
      case "all":
        this.query.stateEquals = null;
        break;
      case "noAudit":
        this.query.stateEquals = "INITIAL";
        break;
      case "audit":
        this.query.stateEquals = "EFFECTED";
        break;
      default:
        break;
    }
    console.log("111111111111112222222222222222222222",this.query)
    CouponWriteoffBillApi.query(this.query).then((res) => {
      this.tableData = res.data;
      this.page.total = res.total;
    });
	this.getStatistics()
  }

  doHandleClick() {
    this.selectedArr = [];
    this.$refs.table.clearSelection();
    this.page.currentPage = 1;
    this.queryList();
  }

  doSearch() {
    this.page.currentPage = 1;
    this.queryList();
  }

  doReset() {
    this.query = new CouponWriteoffFilter();
    this.createBetween = [];
    this.modifyBetween = [];
    this.doSearch();
  }

  handleSelectionChange(val: any) {
    this.selectedArr = val;
  }

  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val;
    this.queryList();
  }

  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val;
    this.queryList();
  }

  checkedAllRow() {
    if (this.singleAll) {
      for (let row of this.tableData) {
        this.$refs.table.toggleRowSelection(row, true);
      }
    } else {
      this.$refs.table.clearSelection();
    }
  }
}
