<template>
  <div
    class="membersSwiperImage"
    @click="activeTemplate"
    :style="{
      padding:
        localProperty.styMarginTop +
        'px ' +
        localProperty.styMarginRight +
        'px ' +
        localProperty.styMarginBottom +
        'px ' +
        localProperty.styMarginLeft +
        'px',
    }"
  >
    <div class="toolBar" v-if="activeIndex === index">
      <setting-toolbar :btns="toolBarBtns" @click="toolbarClick"></setting-toolbar>
    </div>
    <div v-show="localProperty.propShowRotation" class="swiper">
      <div v-show="localProperty.propImages.length === 0" class="defaul-image">
        <img src="@/assets/image/icons/cutting_pic_empty.png" style="width: 84px; height: 72px" />
        <div class="no-image-tip">{{ i18n('请在右侧添加图片') }}</div>
      </div>
      <div v-if="localProperty.propImages.length !== 0 && flag" class="template-rotation">
        <el-carousel height="200px">
          <el-carousel-item v-for="item in imgList" :key="item.id">
            <el-image style="width: 100%; height: 200px" :src="item.imageUrl ? item.imageUrl : imgUrl" fit="contain"></el-image>
          </el-carousel-item>
        </el-carousel>
      </div>
    </div>
    <div class="info">
      <div class="top">
        <div class="name">
          <div class="user">
            <img class="people" :src="avatarUrl" alt="" />
            <div class="nick">
              <div class="nick-top">
                <div class="wz">{{i18n('用户昵称')}}</div>
                <div class="phone" v-show="localProperty.propShowPhoneNumber">138****9394</div>
                <img v-show="localProperty.propShowPhoneNumber" src="@/assets/image/fellow/ic_back_dark.png" alt="" />
              </div>
              <div v-show="localProperty.propShowMemberLevel" class="grade">
                <span>{{ i18n('会员等级')}}</span>
                <img src="@/assets/image/fellow/ic_back_gold.png" alt="" />
              </div>
            </div>
          </div>
          <div v-show="localProperty.propShowMemberCode" class="ewm">
            <img src="@/assets/image/fellow/ic_menbercode.png" alt="" />
            <span>{{ i18n('会员码')}}</span>
          </div>
        </div>
        <div
          class="list"
          v-if="localProperty.propShowPoint && localProperty.propShowBalance && localProperty.propShowCoupon && localProperty.propShowPrepaidCard"
        >
          <div class="item first-item">
            <div class="item-top">0</div>
            <div class="item-bottom">
              <img :src="pointIcon" alt="" />
              <span>{{ localProperty.propPointText }}</span>
            </div>
          </div>
          <div class="item">
            <div class="item-top">0.00</div>
            <div class="item-bottom">
              <img :src="balanceIcon" alt="" />
              <span>{{ localProperty.propBalanceText }}</span>
            </div>
          </div>
          <div class="item">
            <div class="item-top">0</div>
            <div class="item-bottom">
              <img :src="couponIcon" alt="" />
              <span>{{ localProperty.propCouponText }}</span>
            </div>
          </div>
          <div class="item">
            <div class="item-top">0</div>
            <div class="item-bottom">
              <img :src="prepaidCardIcon" alt="" />
              <span>{{ localProperty.propPrepaidCardText }}</span>
            </div>
          </div>
        </div>
        <div class="list" v-else>
          <div class="ele" v-show="localProperty.propShowPoint">
            <div class="item-bottom">
              <img :src="pointIcon" alt="" />
              <span>{{ localProperty.propPointText }}</span>
            </div>
            <div class="item-top">0</div>
          </div>
          <div class="ele" v-show="localProperty.propShowBalance">
            <div class="item-bottom">
              <img :src="balanceIcon" alt="" />
              <span>{{ localProperty.propBalanceText }}</span>
            </div>
            <div class="item-top">0.00</div>
          </div>
          <div class="ele" v-show="localProperty.propShowCoupon">
            <div class="item-bottom">
              <img :src="couponIcon" alt="" />
              <span>{{ localProperty.propCouponText }}</span>
            </div>
            <div class="item-top">0</div>
          </div>
          <div class="ele" v-show="localProperty.propShowPrepaidCard">
            <div class="item-bottom">
              <img :src="prepaidCardIcon" alt="" />
              <span>{{ localProperty.propPrepaidCardText }}</span>
            </div>
            <div class="item-top">0</div>
          </div>
        </div>
      </div>
      <div v-loading="loadingBenefitCardTemplateList" v-if="localProperty.propshowPaidMember && localProperty.propPaidMemberCards && localProperty.propPaidMemberCards.length > 0" class="bottom" :style="{ width:localProperty.propPaidMemberCards.length * 200 + 'px' }">
        <!-- <div class="text">
          <img class="icon" src="@/assets/image/fellow/ic_supermember_big.png" alt="" />
          <div class="wz">{{ localProperty.propPaidMemberText }}</div>
        </div>
        <button class="btn">{{i18n('立即开通')}}</button> -->
          <div class="bottom-item" v-for="(item, index) in localProperty.propPaidMemberCards" :key="index">
              <div class="bottom-item-name">{{ getEquityCardNameByCode(item) }}</div>
              <div class="bottom-item-text">
                {{ localProperty.propPaidMemberText}}
              </div>
          </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./membersSwiperImage.ts"></script>

<style lang="scss" scoped>
.membersSwiperImage {
  width: 100%;
  background: #f9f9f9;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  position: relative;
  margin-bottom: 15px;
  overflow: hidden;
  &-bg {
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-size: 100%;
  }
  &-title {
    text-align: center;
    padding-top: 42px;
    font-size: 20px;
    color: #fff;
  }
  .toolBar {
    position: absolute;
    top: 0;
    right: 1px;
    z-index: 999;
    cursor: pointer;
  }
  .defaul-image {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    text-align: center;
    img {
      margin-top: 25px;
    }
    .no-image-tip {
      width: 100%;
      margin-top: 12px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #a1a6ae;
    }
  }
  .template-rotation {
    width: 100%;
    position: relative;
    margin-bottom: -40px;
    // &-img {
    //   width: 100%;
    //   object-fit: contain;
    // }
    .toolBar {
      position: absolute;
      top: 0;
      right: 1px;
      z-index: 999;
      cursor: pointer;
    }
    .image-widget {
      width: 100%;
      height: 100%;
      position: relative;

      &-img {
        width: 100%;
        display: block;
      }

      .index-border {
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: center;
        position: absolute;
        bottom: 20px;

        .image-index {
          position: relative;
          margin-left: 2px;
          margin-right: 2px;
          border: ghostwhite 1px solid;
          width: 16px;
          height: 4px;
          background-color: #fff;
          border-radius: 2px;
          opacity: 0.6;
        }
      }
    }
  }
  .activeCom {
    border: 2px solid #4d63ec;
  }
  .el-carousel__item h3 {
    color: #475669;
    font-size: 14px;
    opacity: 0.75;
    line-height: 200px;
    margin: 0;
  }
  .info {
    // background: linear-gradient(180deg, #383838 0%, #2d2d2d 100%);
    border-radius: 12px;
    z-index: 998;
    position: relative;

    .top {
      padding: 12px;
      background: #ffffff;
      border-radius: 12px;

      .name {
        display: flex;
        justify-content: space-between;

        .user {
          display: flex;
          .people {
            width: 44px;
            height: 44px;
          }
          .nick {
            margin-left: 10px;
            .nick-top {
              display: flex;
              align-items: center;
              .wz {
                font-weight: 500;
                font-size: 16px;
                color: #111111;
                line-height: 22px;
              }

              .phone {
                font-weight: 400;
                font-size: 12px;
                color: #666666;
                line-height: 16px;
                margin-left: 6px;
              }

              img {
                width: 12px;
                height: 12px;
              }
            }
            .grade {
              display: inline-block;
              padding: 0 3px;
              // align-items: center;
              // justify-content: center;
              // width: 62px;
              width: auto;
              height: 17px;
              background: #ffecc0;
              box-shadow: inset 0px 1px 1px 0px rgba(0, 0, 0, 0.1);
              border-radius: 2px 2px 7px 2px;
              font-weight: 500;
              font-size: 11px;
              color: #cd7700;
              line-height: 16px;
              text-shadow: inset 0px 1px 1px rgba(0, 0, 0, 0.1);

              img {
                width: 12px;
                height: 12px;
                position: relative;
                top: 3px;
              }
            }
          }
        }
        .ewm {
          width: 44px;
          height: 44px;
          background: #f4f4f4;
          border-radius: 8px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;

          img {
            width: 18px;
            height: 18px;
          }
          span {
            font-weight: 400;
            font-size: 9px;
            color: #353535;
            line-height: 12px;
          }
        }
      }
      .list {
        display: flex;
        justify-content: space-between;

        .item,
        .ele {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          margin-top: 16px;

          .item-top {
            height: 21px;
            font-weight: bold;
            font-size: 18px;
            color: #111111;
            line-height: 21px;
          }
          .item-bottom {
            display: flex;
            align-items: center;

            span {
              height: 16px;
              font-weight: 400;
              font-size: 12px;
              color: #666666;
              line-height: 16px;
            }

            img {
              width: 14px;
              height: 14px;
            }
          }
        }
        .ele {
          flex: 1;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          border-radius: 6px;
          background: #f4f4f4;
          height: 39px;
          padding: 0 10px;
          margin-left: 8px;
        }
        .first-item {
          margin-left: 10px;
        }
      }
    }

    .bottom {
      // width: 100%;
      // height: 48px;
      padding: 0 13px;
      overflow-x: auto;
      margin-top: 12px;

      &-item {
        width: 169px;
        height: 66px;
        background-image: url("@/assets/image/member/<EMAIL>");
        background-repeat: no-repeat;
        background-size: 100%;
        display: inline-block;
        margin-right: 6px;
        padding: 10px 12px;
        vertical-align: top;

        &-name {
          text-overflow:ellipsis;
          max-width: 90px;
          white-space: nowrap;
          overflow: hidden;
          margin-bottom: 8px;
          font-weight: 600;
        }

        &-text {
          text-overflow:ellipsis;
          max-width: 100%;
          white-space: nowrap;
          overflow: hidden;
          font-size: 12px;
          font-weight: 400;
          color: #55392E;
          opacity: 0.6;
        }
      }
      // display: flex;
    //   justify-content: space-between;
    //   align-items: center;
    //   .text {
    //     display: flex;
    //     align-items: center;
    //     .icon {
    //       width: 22px;
    //       height: 22px;
    //     }
    //     .wz {
    //       margin-left: 4px;
    //       font-weight: 500;
    //       font-size: 14px;
    //       color: #fff5d3;
    //       line-height: 20px;
    //     }
    //   }
    //   .btn {
    //     width: 82px;
    //     height: 28px;
    //     background: linear-gradient(180deg, #fff9e5 1%, #ffe288 100%);
    //     border-radius: 14px 14px 14px 4px;
    //     font-weight: 500;
    //     font-size: 13px;
    //     color: #2d2d2d;
    //     text-align: center;
    //   }
    }
  }
}
</style>
