/*
 * @Author: 黎钰龙
 * @Date: 2024-08-07 14:52:31
 * @LastEditTime: 2024-08-07 14:54:40
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\template\ContentTemplateExtInfoUpdate.ts
 * 记得注释
 */
import AppShareInfo from "./AppShareInfo"

// 内容模板扩展信息更新
export default class ContentTemplateExtInfoUpdate {
  // 扩展信息(B端怎么传，C端就怎么返回)
  extInfo: AppShareInfo = new AppShareInfo()
  // 模板id
  ids: string[] = []
  // 操作人
  operator: Nullable<string> = null
}