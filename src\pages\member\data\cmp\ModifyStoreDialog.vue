<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:05
 * @LastEditTime: 2023-12-01 16:57:46
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\member\data\cmp\ModifyStoreDialog.vue
 * 记得注释
-->
<template>
  <el-dialog :before-close="doBeforeClose" :close-on-click-modal="false" :title="formatI18n('/会员/会员资料', '批量修改归属门店')" :visible="dialogShow" class="modify-store-dialog">
    <div class="wrap">
      <el-form :model="radioForm" class="demo-ruleForm" label-width="110px" ref="radioForm">
        <el-form-item :label="formatI18n('/会员/会员资料', '修改方式')">
          <el-radio v-model="radioForm.radio" label="1">{{formatI18n('/会员/会员资料', '批量导入')}}</el-radio>
          <el-radio v-model="radioForm.radio" label="2">{{formatI18n('/会员/会员资料', '全量修改')}}</el-radio>
        </el-form-item>
        <template v-if="radioForm.radio == '1'">
          <el-form-item :label="formatI18n('/公用/导入', '实例模板')">
            <a
                class="action-hover_download" @click="downloadTemplate(belongingStore)"
              style="line-height: 36px; color: #318BFF;font-size: 12px;text-decoration: none">
              {{formatI18n('/会员/会员资料', '修改会员归属门店模板')}}
            </a>
          </el-form-item>
          <el-form-item :label="formatI18n('/公用/导入', '选择文件')">
            <div class="limit_info">{{getLimitInfo}}</div>
            <el-upload
              :action="getUploadUrl"
              :data="{remark: storeForm.remark}"
              :auto-upload="false"
              :on-change="doHandleChange"
              :on-error="getErrorInfo"
              :headers="uploadHeaders"
              :on-success="getSuccessInfo"
              :with-credentials="true"
              class="upload-demo"
              ref="upload">
              <el-button size="small" slot="trigger" type="default">{{formatI18n('/公用/导入', '选择文件')}}</el-button>
            </el-upload>
          </el-form-item>
        </template>
      </el-form>
      <el-form v-if="radioForm.radio == '2'" :model="storeForm" :rules="rules" class="demo-ruleForm" label-width="110px" ref="storeForm">
        <el-form-item :label="formatI18n('/会员/会员资料', '门店全量会员')" prop=sourceStoreId>
          <SelectStores v-model="storeForm.sourceStoreId" :isOnlyId="true" :hideAll="true" width="300px"
            :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
          </SelectStores>
        </el-form-item>
        <el-form-item :label="formatI18n('/会员/会员资料', '归属新门店')" prop=targetStoreId>
          <SelectStores v-model="storeForm.targetStoreId" :isOnlyId="true" :hideAll="true" width="300px"
            :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
          </SelectStores>
        </el-form-item>
      </el-form>
      <el-form :model="storeForm" class="demo-ruleForm" label-width="110px">
        <el-form-item :label="formatI18n('/会员/会员资料', '变更说明')" prop="remark">
          <el-input :placeholder="formatI18n('/会员/会员资料', '请输入不超过50个字')"
          maxlength="50"
          type="textarea" v-model="storeForm.remark"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div class="dialog-footer" slot="footer">
      <el-button @click="doModalClose('cancel')">{{formatI18n('/公用/按钮', '取消')}}</el-button>
      <el-button @click="doModalClose('confirm')" size="small" type="primary">{{formatI18n('/公用/按钮', '确认修改')}}</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./ModifyStoreDialog.ts">
</script>

<style lang="scss" scoped>
.modify-store-dialog {
  display: flex;
  justify-content: center;
  align-items: center;

  .wrap {
    margin-top: 30px;
  }

  .limit_info {
    color: #9a9fa8;
    font-size: 12px;
    word-break: break-word;
  }

  & ::v-deep .el-dialog {
    width: 600px;
    margin: 0 !important;
  }

  & ::v-deep .el-form > .el-form-item {
    margin-bottom: 15px;
  }
}
</style>