/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-10-21 14:33:59
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\v2\coupon\goodsgaincouponbyqty\GoodsGainCouponByQtyActivity.ts
 * 记得注释
 */
import BaseCouponActivity from "model/v2/coupon/BaseCouponActivity";
import CouponItem from "model/common/CouponItem";
import CouponThreshold from "model/common/CouponThreshold";
import GoodsRange from "model/common/GoodsRange";
import DateTimeCondition from "model/common/DateTimeCondition";
import GradesRange from "model/common/GradeRange";
import PushGroup from "model/precisionmarketing/pushplan/PushGroup";

export default class GoodsGainCouponByQtyActivity extends BaseCouponActivity {
	// 商品范围
	goodsRange: Nullable<GoodsRange> = null;
	// 发券门槛
	threshold: Nullable<CouponThreshold> = null;
	// 券礼包
	giftInfo: Nullable<CouponItem> = null;
	// 活动时间限制
	dateTimeCondition = new DateTimeCondition();
	// 参与会员等级
	gradeRange: Nullable<GradesRange> = null;
	// 参与叠加促销
	joinPromotion: Nullable<boolean> = false;
	// 排除优惠商品
	excludeFavourGoodTypes: string[] = []
	// 排除优惠金额
	excludeFavourAmountTypes: string[] = ["PROMOTION","COUPON","MEMBER","OTHER"]
  // 参与人群
  rule: Nullable<PushGroup> = null
	// 活动总限N张券
	maxIssueQtyByCoupon: Nullable<number> = null;
	// 每人限领N张券
	maxPerIssueQtyByCoupon: Nullable<number> = null;
}
