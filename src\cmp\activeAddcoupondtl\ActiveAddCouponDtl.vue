<template>
	<div class="active-add-coupon-dtl">
		<div class="wrap" v-if="data && data.coupons && data.coupons.templateId">
			<div class="top-wrap" v-if="isTopPanel">
				<div class="left">
					<div v-if="data.coupons && data.coupons.logoUrl">
						<el-image style="
	                width: 48px;
	                height: 48px;
	                border-radius: 50%;
	                top: 20px;
	                position: relative;
	              " :src="data.coupons.logoUrl" fit="cover">
						</el-image>
					</div>
					<div v-else>
						<div class="back">
							<img src="~assets/image/storevalue/back.png" />
						</div>
					</div>
				</div>
				<div class="right">
					<div class="top">
						<div class="item1">
							<div class="bill" v-if="data && data.coupons && data.coupons.templateId">
								{{ formatI18n("/权益/券/券模板/券模板号") }}：{{ data.coupons.templateId }}
							</div>
							<div :title="data.coupons.name" style="
	                  width: 300px;
	                  white-space: nowrap;
	                  overflow: hidden;
	                  text-overflow: ellipsis;
	                " class="name" v-if="data && data.coupons && data.coupons.name">
								{{ data.coupons.name }}
							</div>
						</div>
						<div class="item2">
							<div class="desc" :style="{'word-break': isEnglish ? 'keep-all' : 'normal'}">
								{{ formatI18n("/营销/券礼包活动/核销第三方券", "券类型") }}
							</div>
							<div class="state" v-if="data && data.coupons && data.coupons.couponBasicType" :style="{'word-break': isEnglish ? 'keep-all' : 'normal'}">
								{{ data.coupons.couponBasicType | lineCouponType }}
							</div>
						</div>
						<div class="item1" style="margin-left: 100px"
							v-if="data && data.coupons && ['all_cash', 'goods_cash', 'freight'].includes(data.coupons.couponBasicType)">
							<div class="bill">{{ formatI18n("/公用/券模板", "券面额") }}</div>
							<div class="name"
								v-if="data && data.coupons && data.coupons.cashCouponAttribute && data.coupons.cashCouponAttribute.faceAmount">
								{{ data.coupons.cashCouponAttribute.faceAmount | fmt }}{{ formatI18n("/公用/券模板", "元") }}
							</div>
							<div class="name" v-else>--</div>
						</div>

						<div class="item1" style="margin-left: 100px"
							v-if="data && data.coupons && ['random_cash'].includes(data.coupons.couponBasicType)">
							<div class="bill" v-if="
								data &&
								data.coupons &&
								data.coupons.randomCouponAttribute &&
								data.coupons.randomCouponAttribute.minFaceAmount &&
								data.coupons.randomCouponAttribute.maxFaceAmount
							" :style="{'word-break': isEnglish ? 'keep-all' : 'normal'}">
								{{ formatI18n("/公用/券模板", "券面额范围") }}
							</div>
							<div class="bill" v-else>{{ formatI18n("/公用/券模板", "券面额") }}</div>
							<div class="name" v-if="
								data &&
								data.coupons &&
								data.coupons.randomCouponAttribute &&
								data.coupons.randomCouponAttribute.minFaceAmount &&
								data.coupons.randomCouponAttribute.maxFaceAmount
							">
								{{ data.coupons.randomCouponAttribute.minFaceAmount }}{{ formatI18n("/公用/券模板", "元") }}
								-
								{{ data.coupons.randomCouponAttribute.maxFaceAmount }}{{ formatI18n("/公用/券模板", "元") }}
							</div>
							<div class="name" v-else>--</div>
						</div>
						<div class="item1" style="margin-left: 100px" v-if="
							data &&
							data.coupons &&
							(data.coupons.couponBasicType === 'all_discount' ||
								data.coupons.couponBasicType === 'goods_discount' ||
								data.coupons.couponBasicType === 'rfm_type')
						">
							<div class="bill">
								{{ formatI18n("/公用/券模板/商品折扣券", "折扣力度") }}
							</div>
							<div class="name" v-if="
								data &&
								data.coupons &&
								data.coupons.discountCouponAttribute &&
								(data.coupons.discountCouponAttribute.discount || data.coupons.discountCouponAttribute.discount === 0)
							">
								{{ Number(data.coupons.discountCouponAttribute.discount).toFixed(1) }}{{
									formatI18n("/公用/券模板/商品折扣券/折扣力度/折") }}
							</div>
							<div class="name" v-else>--</div>
						</div>
					</div>
				</div>
			</div>

			<div v-if="!options.hideTitle" class="baseSettingFlag">
				{{ formatI18n("/权益/券/券模板/编辑界面/用券规则") }}
			</div>
			<FormItem v-if="!options.hideType" :label="formatI18n('/公用/券模板详情/券类型：')">
				<div style="height: 36px; line-height: 36px;" v-if="data && data.coupons && data.coupons.couponBasicType">
					{{ data.coupons.couponBasicType | lineCouponType }}
				</div>
			</FormItem>
			<FormItem :label="formatI18n('/公用/券模板详情/券面额：')" v-if="
				data &&
				data.coupons &&
				data.coupons.couponBasicType &&
				['all_cash', 'goods_cash', 'freight'].includes(data.coupons.couponBasicType) &&
				!options.hideFaceAmount
			">
				<div style="height: 36px; line-height: 36px">
					{{ data.coupons.cashCouponAttribute.faceAmount | fmt }}
					{{ formatI18n("/公用/券模板", "元") }}
				</div>
			</FormItem>
			<FormItem :label="formatI18n('/公用/券模板/券面额范围')" v-if="
				data && data.coupons && data.coupons.couponBasicType && ['random_cash'].includes(data.coupons.couponBasicType) && !options.hideFaceAmount
			">
				<div style="height: 36px; line-height: 36px" v-if="
					data &&
					data.coupons &&
					data.coupons.randomCouponAttribute &&
					data.coupons.randomCouponAttribute.minFaceAmount &&
					data.coupons.randomCouponAttribute.maxFaceAmount
				">
					{{ data.coupons.randomCouponAttribute.minFaceAmount }}{{ formatI18n("/公用/券模板", "元") }}
					-
					{{ data.coupons.randomCouponAttribute.maxFaceAmount }}{{ formatI18n("/公用/券模板", "元") }}
				</div>
				<div style="height: 36px; line-height: 36px" v-else>--</div>
			</FormItem>
			<FormItem :label="formatI18n('/公用/券模板详情/券状态：')" v-if="data && data.coupons && !options.hideState">
				<div style="height: 36px; line-height: 36px">
					<span v-if="data.coupons.state === 'EFFECTED'">
						{{ formatI18n("/权益/券/券模板/已生效") }}
					</span>
					<span v-if="data.coupons.state === 'NOT_EFFECTED'">
						{{ formatI18n("/权益/券/券模板/未生效") }}
					</span>
					<span v-if="data.coupons.state === 'CANCELLED'">
						{{ formatI18n("/权益/券/券模板/券模板状态下拉选项/已作废") }}
					</span>
					<span v-if="data.coupons.state === 'INITIAL'">
						{{ formatI18n('/公用/过滤器/未审核') }}
					</span>
					<span v-if="data.coupons.state === 'EXPIRED'">
						{{ formatI18n("/营销/券礼包活动/券查询/券状态下拉选项/已过期") }}
					</span>
				</div>
			</FormItem>
			<FormItem :label="formatI18n('/公用/券模板详情/折扣力度：')" v-if="
				data &&
				data.coupons &&
				data.coupons.couponBasicType &&
				(data.coupons.couponBasicType === 'all_discount' ||
					data.coupons.couponBasicType === 'goods_discount' ||
					data.coupons.couponBasicType === 'rfm_type') &&
				!options.hideFaceAmount
			">
				<div style="height: 36px; line-height: 36px">
					{{ data.coupons.discountCouponAttribute.discount }}
					{{ formatI18n("/公用/券模板/商品折扣券/折扣力度", "折") }}
				</div>
			</FormItem>
			<FormItem v-if="!options.hideName" :label="formatI18n('/公用/券模板详情/券名称：')">
				<div style="
	            line-height: 36px;
	            width: 80%;
	            text-overflow: ellipsis;
	            white-space: nowrap;
	            overflow: hidden;
	          " v-if="data && data.coupons" :title="data.coupons.name">
					{{ data.coupons.name }}
				</div>
			</FormItem>
			<FormItem v-if="activityDtl" :label="formatI18n('/公用/券模板/新建券模板/券图标：')">
				<div style="
	            line-height: 36px;
	            width: 80%;
	            text-overflow: ellipsis;
	            white-space: nowrap;
	            overflow: hidden;
	          " v-if="data && data.coupons.logoUrl">
					<img :src="data.coupons.logoUrl" style="margin-top: 12px; width: 100px; height: 100px" />
				</div>
				<div v-else>-</div>
			</FormItem>
			<FormItem :label="formatI18n('/营销/券礼包活动/核销第三方券/券码生成规则：')"
				v-if="!options.hideOuterNumberNamespace && ['freight'].indexOf(data.coupons.couponBasicType) === -1 && !wxScanForCoupon">
				<div style="height: 36px; line-height: 36px" v-if="data && data.coupons && data.coupons.codePrefix">
					{{ formatI18n("/营销/券礼包活动/核销第三方券/固定开头") }}-{{ data.coupons.codePrefix }}
				</div>
				<div style="height: 36px; line-height: 36px" v-else>
					{{ formatI18n("/权益/券/券模板/券码前缀/系统随机生成") }}
				</div>
			</FormItem>
			<FormItem :label="formatI18n('/权益/券/券模板/外部券模板号：')"
				v-if="!options.hideOuterNumberNamespace && ['freight'].indexOf(data.coupons.couponBasicType) === -1">
				<div style="line-height: 34px">
					<span v-if="data.coupons.outerNumberNamespace === 'weimob'">{{ formatI18n("/权益/券/券模板/微盟") }}</span>
					<span v-else-if="data.coupons.outerNumberNamespace === 'wxMerchant'">{{ formatI18n("/权益/券/券模板/微信")
					}}</span>
					<span v-else>{{ data.coupons.outerNumberNamespace }}</span>
					<span> - {{ data.coupons.outerNumberId }}</span>
				</div>
			</FormItem>
			<FormItem :label="formatI18n('/公用/券模板/特价设置') + '：'" v-if="data.coupons.couponBasicType === 'special_price'">
				<div style="height: 36px; line-height: 36px">
					{{ getSpecialSetup }}
				</div>
			</FormItem>
			<FormItem :label="formatI18n('/公用/券模板详情/券有效期：')">
				<div style="height: 36px; line-height: 36px"
					v-if="data && data.coupons && data.coupons.validityInfo && data.coupons.validityInfo.validityType === 'RALATIVE'">
					{{ getValidateDate(data.coupons.validityInfo.delayEffectDays, data.coupons.validityInfo.validityDays,
						data.coupons.validityInfo.expiryType, data.coupons.validityInfo.months) }}
				</div>
				<div style="height: 36px; line-height: 36px"
					v-if="data && data.coupons && data.coupons.validityInfo && data.coupons.validityInfo.validityType !== 'RALATIVE'">
					{{ formatI18n("/公用/券模板", "固定有效期") }}，{{ data.coupons.validityInfo.beginDate | dateFormate3
					}}{{ formatI18n("/营销/券礼包活动/券礼包活动", "至") }}{{ data.coupons.validityInfo.endDate | dateFormate3 }}
				</div>
			</FormItem>
			<FormItem :label="formatI18n('/公用/券模板详情/用券时段：')"
				v-if="['freight', 'points'].indexOf(data.coupons.couponBasicType) === -1">
				<div style="line-height: 36px"
					v-if="data && data.coupons && data.coupons.useTimeRange && data.coupons.useTimeRange.dateTimeRangeType === 'ALL'">
					{{ formatI18n("/公用/券模板", "全部时段") }}
				</div>
				<div
					v-if="data && data.coupons && data.coupons.useTimeRange && data.coupons.useTimeRange.dateTimeRangeType === 'USEABLE'">
					<div style="height: 36px; line-height: 36px">
						{{ formatI18n("/公用/券模板", "指定时段适用") }}
					</div>
					<div v-if="data && data.coupons && data.coupons.useTimeRange.cycleType === 'DAYS'">
						<div v-html="getDayTime(data.coupons.useTimeRange.beginTime, data.coupons.useTimeRange.endTime)">
						</div>
					</div>
					<div v-if="data && data.coupons && data.coupons.useTimeRange.cycleType === 'MONTHS'">
						<div
							v-html="getMonthTime(data.coupons.useTimeRange.days, data.coupons.useTimeRange.beginTime, data.coupons.useTimeRange.endTime)">
						</div>
					</div>
					<div v-if="data && data.coupons && data.coupons.useTimeRange.cycleType === 'WEEKS'">
						<div
							v-html="getWeekTime(data.coupons.useTimeRange.days, data.coupons.useTimeRange.beginTime, data.coupons.useTimeRange.endTime)">
						</div>
					</div>
				</div>
				<div
					v-if="data && data.coupons && data.coupons.useTimeRange && data.coupons.useTimeRange.dateTimeRangeType === 'UNUSEABLE'">
					<div style="height: 36px; line-height: 36px">
						{{ formatI18n("/公用/券模板", "指定时段不适用") }}
					</div>
					<div v-if="data && data.coupons && data.coupons.useTimeRange.cycleType === 'DAYS'">
						<div v-html="getDayTime(data.coupons.useTimeRange.beginTime, data.coupons.useTimeRange.endTime)">
						</div>
					</div>
					<div v-if="data && data.coupons && data.coupons.useTimeRange.cycleType === 'MONTHS'">
						<div
							v-html="getMonthTime(data.coupons.useTimeRange.days, data.coupons.useTimeRange.beginTime, data.coupons.useTimeRange.endTime)">
						</div>
					</div>
					<div v-if="data && data.coupons && data.coupons.useTimeRange.cycleType === 'WEEKS'">
						<div
							v-html="getWeekTime(data.coupons.useTimeRange.days, data.coupons.useTimeRange.beginTime, data.coupons.useTimeRange.endTime)">
						</div>
					</div>
				</div>
			</FormItem>
			<FormItem :label="formatI18n('/公用/券模板详情/用券渠道：')" v-if="['points'].indexOf(data.coupons.couponBasicType) === -1">
				<div v-if="data && data.coupons && data.coupons.useChannels"
					style="line-height: 36px; word-break: break-word">
					{{ getChannel(data.coupons.useChannels) }}
				</div>
			</FormItem>
			<FormItem :label="formatI18n('/公用/券模板详情/用券门店：')">
				<ActiveStoreDtl style="width: 60%; min-width: 600px" v-if="data && data.coupons && data.coupons.useStores"
					:data="data.coupons.useStores"></ActiveStoreDtl>
			</FormItem>
			<!-- 用券商品 -->
			<UseCouponGoods :data="data"></UseCouponGoods>
			<FormItem :label="formatI18n('/公用/券模板/可兑换数量') + ': '"
				v-if="data && data.coupons && data.coupons.couponBasicType && data.coupons.couponBasicType === 'exchange_goods' && data.coupons.exchangeGoodsCouponAttribute.limitGoods === false">
				<div style="height: 36px; line-height: 36px">
					{{ data.coupons.exchangeGoodsCouponAttribute.exchangeQty }} {{ formatI18n("/公用/券模板/单品折扣券/用券门槛/件") }}
				</div>
			</FormItem>
			<FormItem :label="formatI18n('/公用/券模板详情/用券门槛：')"
				v-if="data && data.coupons && data.coupons.couponBasicType && ['random_cash', 'goods_cash', 'exchange_goods', 'all_cash', 'all_discount', 'goods_discount', 'rfm_type'].includes(data.coupons.couponBasicType)">
				<!-- <div v-if="data.coupons.useThresholdType === 'AMOUNT' || !data.coupons.useThresholdType">
					<div style="height: 36px; line-height: 36px" v-if="data.coupons.useThreshold.thresholdType === 'NONREUSEABLE'">
						<div v-html="getAllCashNo(data.coupons.useThreshold.threshold)"></div>
					</div>
					<div style="height: 36px; line-height: 36px" v-if="data.coupons.useThreshold.thresholdType === 'REUSEABLE'">
						<div v-html="getAllCash(data.coupons.useThreshold.threshold)"></div>
					</div>
					<div style="height: 36px; line-height: 36px" v-if="data.coupons.useThreshold.thresholdType === 'NONE'">
						<span v-if="data && data.coupons && data.coupons.couponBasicType && data.coupons.couponBasicType !== 'goods_cash' && data.coupons.couponBasicType !== 'goods_discount'">{{
							formatI18n("/公用/券模板/无门槛，全场消费满任意金额且不限制用券张数")
						}}</span>
						<span v-if="data && data.coupons && data.coupons.couponBasicType && (data.coupons.couponBasicType === 'goods_cash' || data.coupons.couponBasicType === 'goods_discount')">{{
							formatI18n("/公用/券模板/商品现金券/无门槛，用券商品消费满任意金额且不限制用券张数")
						}}</span>
					</div>
				</div>
				<div v-else>
					<div style="height: 36px;line-height: 36px" v-if="data.coupons.useThreshold.thresholdType === 'NONE'">
						<span>{{ formatI18n("/营销/积分活动/积分活动/商品满额加送积分/新建页面/积分加送规则/不限制") }}</span>
					</div>
					<div style="height: 36px;line-height: 36px;padding-top: 3px" v-if="data.coupons.useThreshold.thresholdType === 'NONREUSEABLE'">
						<div v-html="getGoodsNo(data.coupons.useThreshold.threshold)"></div>
					</div>
				</div> -->


				<div v-if="data.coupons.useThresholdType === 'AMOUNT'">
					<div style="height: 36px;line-height: 36px" v-if="data.coupons.useThreshold.thresholdType !== 'NONE'">
						<span>{{ formatI18n("/公用/券模板/单品折扣券/用券门槛/用券商品满") + data.coupons.useThreshold.threshold +
							formatI18n("/公用/券模板/单品折扣券/用券门槛/元及以上可用") }}</span>
					</div>
					<div style="height: 36px;line-height: 36px" v-else>
						<span>{{ formatI18n("/公用/券模板/无门槛") }}</span>
					</div>
				</div>
				<div v-else-if="data.coupons.useThresholdType === 'QTY'">
					<div style="height: 36px;line-height: 36px" v-if="data.coupons.useThreshold.thresholdType !== 'NONE'">
						<span>{{ formatI18n("/公用/券模板/单品折扣券/用券门槛/用券商品满") + data.coupons.useThreshold.threshold +
							formatI18n("/公用/券模板/单品折扣券/用券门槛/件及以上可用") }}</span>
					</div>
					<div style="height: 36px;line-height: 36px" v-else>
						<span>{{ formatI18n("/公用/券模板/无门槛") }}</span>
					</div>
				</div>
				<div v-else-if="data.coupons.useThresholdType === 'DISCOUNT_PER_AMOUNT'">
					<div style="height: 36px;line-height: 36px" v-if="data.coupons.useThreshold.thresholdType !== 'NONE'">
						<span>{{ i18n("用券商品每满") + ' ' + data.coupons.useThreshold.threshold + ' ' + formateThresholdStr +
							data.coupons.useThreshold.value + i18n("元") }}</span>
					</div>
					<div style="height: 36px;line-height: 36px" v-else>
						<span>{{ formatI18n("/公用/券模板/无门槛") }}</span>
					</div>
				</div>
				<div v-else>
					<div style="height: 36px;line-height: 36px">
						<span>{{ formatI18n("/公用/券模板/无门槛") }}</span>
					</div>
				</div>
			</FormItem>
			<FormItem :label="formatI18n('/公用/券模板详情/用券门槛：')"
				v-if="data && data.coupons && data.coupons.couponBasicType && ['freight'].includes(data.coupons.couponBasicType)">
				<div v-if="data.coupons.useThresholdType === 'AMOUNT'">
					<div style="height: 36px;line-height: 36px" v-if="data.coupons.useThreshold.thresholdType !== 'NONE'">
						<span>{{ formatI18n("/公用/券模板/订单待支付金额满") + data.coupons.useThreshold.threshold +
							formatI18n("/公用/券模板/单品折扣券/用券门槛/元及以上可用") }}</span>
					</div>
					<div style="height: 36px;line-height: 36px" v-else>
						<span>{{ formatI18n("/公用/券模板/无门槛") }}</span>
					</div>
				</div>
				<div v-else>
					<div style="height: 36px;line-height: 36px">
						<span>{{ formatI18n("/公用/券模板/无门槛") }}</span>
					</div>
				</div>
			</FormItem>
			<!-- <FormItem
				:label="formatI18n('/公用/券模板详情/用券门槛：')"
				v-if="data && data.coupons && data.coupons.couponBasicType && data.coupons.couponBasicType === 'goods_cash'"
			>
				<div style="height: 36px; line-height: 36px" v-if="data.coupons.useThreshold.thresholdType === 'NONREUSEABLE'">
					<div v-html="getGoodsCashNo(data.coupons.useThreshold.threshold)"></div>
				</div>
				<div style="height: 36px; line-height: 36px" v-if="data.coupons.useThreshold.thresholdType === 'REUSEABLE'">
					<div v-html="getGoodsCash(data.coupons.useThreshold.threshold)"></div>
				</div>
				<div style="height: 36px; line-height: 36px" v-if="data.coupons.useThreshold.thresholdType === 'NONE'">
					<span v-if="data && data.coupons && data.coupons.couponBasicType && ['random_cash', 'all_cash'].includes(data.coupons.couponBasicType)">{{
						formatI18n("/公用/券模板/无门槛，全场消费满任意金额且不限制用券张数")
					}}</span>
					<span v-if="data && data.coupons && data.coupons.couponBasicType && data.coupons.couponBasicType === 'goods_cash'">{{
						formatI18n("/公用/券模板/商品现金券/无门槛，用券商品消费满任意金额且不限制用券张数")
					}}</span>
				</div>
			</FormItem> -->
			<!-- <FormItem
				:label="formatI18n('/公用/券模板详情/用券门槛：')"
				v-if="data && data.coupons && data.coupons.couponBasicType && data.coupons.couponBasicType === 'all_discount'"
			>
				<div style="height: 36px; line-height: 36px" v-html="getAllDiscountCoupon(data.coupons.useThreshold.threshold)"></div>
			</FormItem>
			<FormItem
				:label="formatI18n('/公用/券模板详情/用券门槛：')"
				v-if="data && data.coupons && data.coupons.couponBasicType && data.coupons.couponBasicType === 'goods_discount'"
			>
				<div
					style="height: 36px; line-height: 36px"
					v-html="
						getGoodsDiscount(data.coupons.useThreshold.threshold, data.coupons.discountCouponAttribute.discount, data.coupons.useThreshold.value)
					"
				></div>
			</FormItem> -->
			<!-- <FormItem
				:label="formatI18n('/公用/券模板详情/用券门槛：')"
				v-if="data && data.coupons && data.coupons.couponBasicType && data.coupons.couponBasicType === 'rfm_type'"
			>
				<div style="height: 36px; line-height: 36px" v-html="getRfmTypeStep(data.coupons.useThreshold.threshold)"></div>
			</FormItem> -->
			<!-- <FormItem
				:label="formatI18n('/公用/券模板详情/用券门槛：')"
				v-if="data && data.coupons && data.coupons.couponBasicType && data.coupons.couponBasicType === 'exchange_goods'"
			>
				<div style="height: 36px; line-height: 36px" v-html="getMenkan(data.coupons.useThreshold)"></div>
			</FormItem> -->
			<!-- <FormItem :label="formatI18n('/公用/券模板详情/促销叠加：')"
				v-if="data && data.coupons && data.coupons.couponBasicType && ['freight', 'goods', 'points'].indexOf(data.coupons.couponBasicType) === -1">
				<div style="height: 36px; line-height: 36px">
					{{ data.coupons.excludePromotion ? formatI18n("/公用/券模板", "是") : formatI18n("/公用/券模板", "否") }}
				</div>
			</FormItem> -->
			<FormItem :label="formatI18n('/公用/券模板详情/叠加促销：')"
				v-if="data && data.coupons && data.coupons.couponBasicType && ['freight', 'goods', 'points', 'random_cash'].indexOf(data.coupons.couponBasicType) === -1">
				<div style="height: 36px; line-height: 36px">
					{{ getCouponPromotionInfo(data.coupons) }}
					<span @click.stop="openPromotionShow" style="color: #1597FF; cursor: pointer;"
						v-if="data.coupons.promotionSuperpositionType == 'PART'">{{ formatI18n('/公用/券模板详情/查看促销单') }}</span>
				</div>
			</FormItem>
			<GroupMutexTemplateDtl v-if="
				data &&
				data.coupons &&
				data.coupons.couponBasicType &&
				['freight', 'goods', 'exchange_goods', 'points'].indexOf(data.coupons.couponBasicType) === -1
			" :info="data.coupons" :deep-dialog="deepDialog">
			</GroupMutexTemplateDtl>

			<FormItem :label="formatI18n('/公用/券模板详情/优惠上限：')" v-if="
				data &&
				data.coupons &&
				data.coupons.couponBasicType &&
				(data.coupons.couponBasicType === 'all_discount' || data.coupons.couponBasicType === 'rfm_type')
			">
				<div style="height: 36px; line-height: 36px">
					{{ getMoney(data.coupons.discountCouponAttribute.maxDiscountAmount,
						data.coupons.discountCouponAttribute.maxDiscountQty, data.coupons.useThresholdType) }}
				</div>
			</FormItem>
			<FormItem :label="formatI18n('/公用/券模板详情/用券记录方式：')" :labelWidth="labelWidth + ' !important'"
				v-if="data && data.coupons && data.coupons.couponBasicType && ['freight', 'goods', 'points'].indexOf(data.coupons.couponBasicType) === -1">
				<div style="height: 36px; line-height: 36px"
					v-if="data && data.coupons && data.coupons.useApporion.subjectApprotionType === 'FAV'">
					{{ formatI18n("/公用/券模板", "优惠方式") }}
				</div>
				<div style="height: 36px; line-height: 36px"
					v-if="data && data.coupons && data.coupons.useApporion.subjectApprotionType === 'PAY'">
					{{ formatI18n("/公用/券模板", "支付方式") }}
				</div>
				<div style="line-height: 36px"
					v-if="data && data.coupons && data.coupons.useApporion.subjectApprotionType === 'COLLOCATION'">
					{{ formatI18n("/公用/券模板", "组合方式") }} <span
						v-html="getFavValue()"></span>
					<br>
					<el-button @click="viewSpecialGoods"
						v-if="data.coupons && data.coupons.useApporion && data.coupons.useApporion.recordType === 'AMOUNT' && data.coupons.useApporion.specialGoodsAmounts && data.coupons.useApporion.specialGoodsAmounts.length"
						type="text">
						{{ i18n('查看特殊商品') }}
					</el-button>
				</div>
			</FormItem>
			<FormItem :label="formatI18n('/公用/券模板详情/券承担方：')" :labelWidth="labelWidth + ' !important'" v-if="
				data &&
				data.coupons &&
				data.coupons.couponBasicType &&
				['freight', 'goods', 'points'].indexOf(data.coupons.couponBasicType) === -1
			">
				<div style="height: 36px; line-height: 36px"
					v-if="!data.coupons.costParty || !data.coupons.costParty.costPartyDetails || data.coupons.costParty.costPartyDetails.length === 0">
					-
				</div>
				<div style="padding-top: 8px" v-else>
					<div v-for="item in data.coupons.costParty.costPartyDetails" :key="item.party" class="cur-party">
						<div v-html="getCostPartr(item.party, item.value)"></div>
					</div>
					<el-button @click="viewCBSpecialGoods"
						v-if="data.coupons && data.coupons.costParty && data.coupons.costParty.specialGoodsCostParties && data.coupons.costParty.specialGoodsCostParties.length"
						type="text">
						{{ i18n('查看特殊商品') }}
					</el-button>
				</div>
			</FormItem>
			<!-- <FormItem
				:label="formatI18n('/公用/券模板详情/用券顺序：')"
				v-if="data && data.coupons && data.coupons.couponBasicType && ['freight', 'goods', 'points'].indexOf(data.coupons.couponBasicType) === -1"
			>
				<div style="height: 36px; line-height: 36px">
					{{ data.coupons.priority }}
				</div>
			</FormItem> -->
			<!--<FormItem :label="formatI18n('/营销/券礼包活动/核销第三方券', '用券商品说明')">-->
			<!--<div style="line-height: 36px">-->
			<!--{{data.coupons.goodsRemark | strFormat}}-->
			<!--</div>-->
			<!--</FormItem>-->
			<FormItem class="label-value">
				<template slot="label">
					<div style="width: 300px!important">{{ getPointsAmount }}</div>
				</template>
			</FormItem>
			<FormItem :label="formatI18n('/公用/券模板详情/用券说明：')">
				<el-tooltip effect="light" placement="top-start">
					<div slot="content" v-if="data.coupons.remark" v-html="xss(data.coupons.remark.replace(/\n/g, '<br/>'))">

					</div>
					<div class="overtext" style="
								line-height: 22px;
								clear: both;
								position: relative;
								top: -30px;
								width: 800px;
							" v-if="data && data.coupons && data.coupons.remark" v-html="xss(data.coupons.remark.replace(/\n/g, '<br/>'))">
					</div>
				</el-tooltip>
			</FormItem>
			<FormItem :label="formatI18n('/公用/券模板/能否转赠')" v-if="['points'].indexOf(data.coupons.couponBasicType) === -1">
				<div style="
							line-height: 22px;
							clear: both;
							position: relative;
							top: -30px;
							width: 800px;
						" v-if="data && data.coupons && data.coupons.transferable != null" class="overtext"
					v-html="data.coupons.transferable ? formatI18n('/公用/券模板/是') : formatI18n('/公用/券模板/否')"></div>
			</FormItem>
			<FormItem :label="formatI18n('/权益/券/券模板', '标签')">
				<div v-if="getTemplateLabel().length > 0" style="line-height: 36px; word-break: break-word">
					{{ getTemplateLabel() }}
				</div>
				<div style="line-height: 36px; word-break: break-word" v-else>
					-
				</div>
			</FormItem>
			<FormItem :label="formatI18n('/公用/券模板/同步渠道')"
				v-if="['points', 'random_cash'].indexOf(data.coupons.couponBasicType) === -1">
				<div v-if="getSychChannel().length > 0" style="line-height: 36px; word-break: break-word">
					{{ getSychChannel() }}
				</div>
				<div style="line-height: 36px; word-break: break-word" v-else>
					-
				</div>
			</FormItem>
			<FormItem :label="formatI18n('/公用/券模板/预约提货') + '：'" v-if="data.coupons.couponBasicType === 'goods'">
				<div style="
							line-height: 22px;
							clear: both;
							position: relative;
							top: -30px;
							width: 800px;
						" v-if="data && data.coupons && data.coupons.pickUpCouponAttribute != null" class="overtext">
					{{ data.coupons.pickUpCouponAttribute.reserve ? formatI18n('/公用/券模板', '需要线上预约') : formatI18n('/公用/券模板',
						'不需要线上预约') }}
				</div>
			</FormItem>
			<FormItem :label="formatI18n('/公用/券模板/提货时间') + '：'"
				v-if="data.coupons.couponBasicType === 'goods' && data && data.coupons && data.coupons.pickUpCouponAttribute != null && data.coupons.pickUpCouponAttribute.reserve === true">
				<div style="
							line-height: 22px;
							clear: both;
							position: relative;
							top: -30px;
							width: 800px;
						" v-if="data && data.coupons && data.coupons.pickUpCouponAttribute != null" class="overtext">
					{{ formatI18n('/公用/券模板', '预约后第') }}
					{{ data.coupons.pickUpCouponAttribute.reserveStartDay }}
					{{ formatI18n('/公用/券模板', '天至') }}
					{{ data.coupons.pickUpCouponAttribute.reserveEndDay }}
					{{ formatI18n('/公用/券模板', '天之间到店提货') }}
				</div>
			</FormItem>
			<div v-if="data && data.coupons && data.coupons.weimobCoupon || data && data.coupons && data.coupons.total">
				<div class="baseSettingFlag">
					{{ formatI18n('/公用/券模板/微盟平台券') }}
				</div>
				<FormItem :label="formatI18n('/营销/券礼包活动/核销第三方券', '剩余库存')">
					<div style="
								line-height: 22px;
								clear: both;
								position: relative;
								top: -30px;
								width: 800px;
							" v-if="data && data.coupons" class="overtext"
						v-html="data.coupons.total || formatI18n('/储值/预付卡/电子礼品卡活动/编辑页面/不限制')"></div>
				</FormItem>
				<FormItem :label="formatI18n('/公用/券模板', '每人限领')">
					<div style="
								line-height: 22px;
								clear: both;
								position: relative;
								top: -30px;
								width: 800px;
							" v-if="data && data.coupons && data.coupons.weimobCoupon" class="overtext" v-html="getLimitReceiveText"></div>
				</FormItem>
				<FormItem :label="formatI18n('/公用/券模板', '发放渠道')">
					<div style="
								line-height: 22px;
								clear: both;
								position: relative;
								top: -30px;
								width: 800px;
							" v-if="data && data.coupons && data.coupons.weimobCoupon" class="overtext" v-html="getIssueChannel"></div>
				</FormItem>
				<FormItem :label="'核销场景'">
					<div style="
								line-height: 22px;
								clear: both;
								position: relative;
								top: -30px;
								width: 800px;
							" v-if="data && data.coupons && data.coupons.weimobCoupon" class="overtext" v-html="getUseScene"></div>
				</FormItem>
				<FormItem :label="'微盟适用商品'">
					<div style="
								line-height: 22px;
								clear: both;
								position: relative;
								top: -30px;
								width: 800px;
							" v-if="data && data.coupons && data.coupons.weimobCoupon" class="overtext">
							<!-- <span>{{formatI18n("/公用/公共组件/商品范围控件/表单/全部商品")}}</span> -->
							<span v-if="goodsInfo.type == 'none'">{{ goodsInfo.str }}</span>
							<el-tooltip class="item" effect="light" placement="top" v-if="goodsInfo.type == 'tips'">
								<div slot="content">
								<div style="width: 100%; max-height: 300px; overflow-y: auto;">
									<span v-for="(item, index) in goodsInfo.list" :key="index">{{ item.name }} <br /></span>
								</div>
								</div>
								<span style="border-bottom: 1px dashed rgb(182, 186, 191); cursor: pointer;">{{ goodsInfo.str }}</span> 
							</el-tooltip>
							<span v-if="excludedGoodsList.length > 0">，</span>
							<el-tooltip class="item" effect="light" placement="top" v-if="excludedGoodsList.length > 0">
							<div slot="content">
								<div style="width: 100%; max-height: 300px; overflow-y: auto;">
								<span v-for="(item, index) in excludedGoodsList" :key="index">{{ item.name }} <br /></span>
								</div>
							</div>
							<span style="border-bottom: 1px dashed rgb(182, 186, 191); cursor: pointer;">{{ formatI18n('/公用/券模板', '排除')
							}}{{ excludedGoodsList.length }}{{ formatI18n('/公用/券模板', '项商品') }}</span>
							</el-tooltip>
						</div>
				</FormItem>
			</div>

			<div v-if="data && data.coupons && data.coupons.rexCoupon">
				<div class="baseSettingFlag">
					{{ formatI18n("/权益/券/券模板/REX平台券") }}
				</div>
				<FormItem :label="formatI18n('/权益/券/券模板/净值')">
					<div style="height: 36px; line-height: 36px">
						{{ data.coupons.rexCoupon.netWorth }}&nbsp;&nbsp;
						{{ formatI18n('/公用/券模板/元') }}
					</div>
				</FormItem>
				<FormItem :label="formatI18n('/权益/券/券模板/单笔订单限用')">
					<div style="height: 36px; line-height: 36px">
						{{ data.coupons.rexCoupon.limitedUse }}&nbsp;&nbsp;
						{{ formatI18n("/营销/券礼包活动/券礼包活动/张") }}
					</div>
				</FormItem>
			</div>
		</div>
		<!-- 查看特殊商品 -->
		<SpecialGoodsDialog ref="SpecialGoodsDialog"
			:data="data.coupons && data.coupons.useApporion ? data.coupons.useApporion.specialGoodsAmounts : []">
		</SpecialGoodsDialog>
		<!-- 承担方 -->
		<CBearSpecialGoodsDtl ref="CBearSpecialGoodsDtl" :parties="parties"
			:data="data.coupons && data.coupons.costParty ? (data.coupons.costParty.specialGoodsCostParties || []) : []">
		</CBearSpecialGoodsDtl>
		<PromotionShowDialog ref="promotionShow" :templateId="data.coupons && data.coupons.templateId || ''">
		</PromotionShowDialog>
	</div>
</template>

<script lang="ts" src="./ActiveAddCouponDtl.ts"></script>

<style lang="scss">
.el-tooltip__popper {
	max-width: 80% !important;
}

.active-add-coupon-dtl {
	.wrap {
		// margin-bottom: 100px;
	}

	.overtext {
		text-overflow: -o-ellipsis-lastline;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 4;
		line-clamp: 4;
		-webkit-box-orient: vertical;
	}

	.goods-range-dtl {
		.text {
			line-height: 36px;
		}
	}

	.qf-form-item .qf-form-label {
		text-align: left !important;
		width: 110px !important;
	}

	.qf-form-item .qf-form-content {
		margin-left: 110px !important;
	}

	.baseSettingFlag {
		font-size: 16px;
		font-weight: 500;
		padding: 20px;
		padding-left: 0px;
	}

	.top-wrap {
		display: flex;
		flex-direction: row;

		.left {
			width: 80px;
			height: 80px;
			display: flex;
			align-items: center;
			justify-content: center;

			.back {
				width: 48px;
				height: 48px;
				border-radius: 100%;
				background-color: rgba(242, 242, 242, 1);
				margin-top: 30px;
				margin-left: 10px;

				img {
					width: 24px;
					height: 24px;
					position: relative;
					top: 13px;
					left: 12px;
				}
			}
		}

		.right {
			margin-top: 13px;
			display: flex;
			flex: 1;
			flex-direction: column;
			position: relative;

			.top {
				margin-bottom: 20px;
				display: flex;
				height: 105px;
				border-bottom: 1px solid rgba(242, 242, 242, 1);

				.item1 {
					.bill {
						margin-top: 16px;
						color: rgba(51, 51, 51, 0.***************);
					}

					.name {
						font-weight: 500;
						margin-top: 8px;
						font-size: 20px;
					}
				}

				.item2 {
					padding-left: 70px;
					padding-top: 16px;

					.desc {
						color: rgba(51, 51, 51, 0.***************);
					}

					.state {
						font-weight: 500;
						margin-top: 8px;
						font-size: 20px;
					}
				}
			}

			.bottom {
				padding-bottom: 20px;

				.account-info {
					margin-top: 10px;
				}

				.red {
					color: red;
				}

				.green {
					color: #008000;
				}
			}
		}
	}

	.active-store-dtl {
		display: table-cell;
	}

	.cur-party {
		&:nth-child(n + 3) {
			/*padding-left: 13px;*/
		}
	}
}
</style>
