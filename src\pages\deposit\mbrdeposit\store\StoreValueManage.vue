<template>
	<div class="store-value-manage">
		<BreadCrume :panelArray="panelArray">
			<template slot="operate"> </template>
		</BreadCrume>
		<ListWrapper>
			<template slot="query">
				<el-form :inline="true" label-width="120px" class="queryForm">
					<el-row>
						<el-col :span="8">
							<el-form-item :label="i18n('门店代码/名称：')">
                <SelectStores v-model="queryFilter.orgKey" @change="doStoreChange" :isOnlyId="true" :hideAll="false" width="100%"
                  :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
                </SelectStores>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item :label="i18n('门店状态：')">
								<el-select :placeholder="i18n('门店状态')" v-model="queryFilter.state" clearable>
									<el-option :key="null" :value="null" :label="formatI18n('/储值/会员储值/会员储值报表/消费流水/不限')"></el-option>
									<el-option :key="state.normal" :value="state.normal" :label="i18n('正常')"></el-option>
									<el-option :key="state.abnormal" :value="state.abnormal" :label="i18n('异常')"></el-option>
								</el-select>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
				<el-button type="primary" @click="doSearch">{{ i18n("查询") }}</el-button>
				<el-button @click="doReset">{{ i18n("/公用/按钮/重置") }}</el-button>
			</template>

			<template slot="list">
				<FloatBlock :top="95" refClass="list-wrapper">
					<template slot="ctx">
						<i18n k="/资料/营销中心/已选择{0}个门店">
							<template slot="0"> {{ selected.length }} </template>
						</i18n>
						&nbsp;
						<el-button @click="openEdit" type="primary" v-if="hasOptionPermission('/卡/卡管理/卡限额设置', '配置维护')">{{
							i18n("新增")
						}}</el-button>
						<el-button @click="openImport" v-if="hasOptionPermission('/卡/卡管理/卡限额设置', '导入限额')">{{ i18n("导入限额") }}</el-button>
						<el-button @click="openBatch" v-if="hasOptionPermission('/卡/卡管理/卡限额设置', '配置维护')">{{ i18n("批量修改") }}</el-button>
					</template>
				</FloatBlock>
				<el-table :data="data" style="width: 100%;margin-top: 20px" ref="table" @selection-change="handleSelectionChange">
					<el-table-column type="selection" width="55" />
					<el-table-column :label="i18n('门店代码')" prop="orgId"></el-table-column>
					<el-table-column :label="i18n('门店名称')" prop="orgName"></el-table-column>
					<el-table-column :label="i18n('门店状态')">
						<template slot-scope="scope">
							{{ scope.row.state == "normal" ? i18n("正常") : i18n("异常") }}
						</template>
					</el-table-column>
					<el-table-column :label="i18n('限额（元）')">
						<template slot-scope="scope">
							{{ JSON.parse(JSON.stringify(scope.row)).limitBalance }}
						</template>
					</el-table-column>
					<el-table-column :label="i18n('操作')" fixed="right">
						<template slot-scope="scope">
							<el-button @click="openLimit(scope.row)" type="text" v-if="hasOptionPermission('/卡/卡管理/卡限额设置', '配置维护')">{{
								i18n("修改限额")
							}}</el-button>
							<el-button
								v-if="scope.row.state === 'normal' && hasOptionPermission('/卡/卡管理/卡限额设置', '状态变更')"
								@click="openState(scope.row, false)"
								type="text"
								>{{ i18n("设为异常") }}</el-button
							>
							<el-button
								v-if="scope.row.state === 'abnormal' && hasOptionPermission('/卡/卡管理/卡限额设置', '状态变更')"
								@click="openState(scope.row, true)"
								type="text"
								>{{ i18n("设为正常") }}</el-button
							>
						</template>
					</el-table-column>
				</el-table>
			</template>
			<template slot="page">
				<el-pagination
					no-i18n
					:current-page="page.currentPage"
					:page-size="page.size"
					:page-sizes="[10, 20, 30, 40]"
					:total="page.total"
					@current-change="onHandleCurrentChange"
					@size-change="onHandleSizeChange"
					background
					layout="total, prev, pager, next, sizes,  jumper"
					class="pagin"
				>
				</el-pagination>
			</template>
		</ListWrapper>
		<!-- edit弹框 -->
		<StoreSelectorDialog ref="storeSelectorDialog" :dialogTitle="i18n('新增')" @summit="selectStore"></StoreSelectorDialog>

		<!-- 设为正常/异常弹框 -->
		<el-dialog class="inner-dialog-center" :title="stateTile" :visible="stateShow" width="30%" :before-close="handleStateClose">
			<div v-html="stateContent"></div>
			<span slot="footer" class="dialog-footer">
				<el-button @click="handleStateClose">{{ i18n("取 消") }}</el-button>
				<el-button type="primary" @click="handleStateConfirm">{{ i18n("确 定") }}</el-button>
			</span>
		</el-dialog>
		<!-- 修改限额 弹框 -->
		<el-dialog class="inner-dialog-center" :title="i18n('修改限额')" :visible="limitShow" width="30%" :before-close="handleLimitClose">
			<div>
				<el-form label-width="120px" label-position="left" ref="limitEdit" :rules="limitEditRule" :model="currentStore">
					<el-form-item :label="i18n('当前门店')">
						<div>[{{ currentStore.orgId }}]{{ currentStore.orgName }}</div>
					</el-form-item>
					<el-form-item :label="i18n('储值余额限制')" required prop="limitBalance">
						<AutoFixInput :min="0.01" :max="99999999" :fixed="2" style="width: 100px" v-model="currentStore.limitBalance" />
						<span> {{ i18n("元") }}</span>
					</el-form-item>
				</el-form>
			</div>
			<span slot="footer" class="dialog-footer">
				<el-button @click="handleLimitClose">{{ i18n("取 消") }}</el-button>
				<el-button type="primary" @click="handleLimitConfirm">{{ i18n("确 定") }}</el-button>
			</span>
		</el-dialog>
		<!-- 批量修改 -->
		<el-dialog class="inner-dialog-center" :title="i18n('批量修改')" :visible="batchShow" width="30%" :before-close="handleBatchClose">
			<div>
				<el-form label-width="140px" label-position="left" ref="limitBatch" :rules="limitBatchRule" :model="batchParams">
					<el-form-item :label="i18n('门店状态')">
						<el-radio-group v-model="batchState">
							<el-radio :label="state.normal">{{ i18n("正常") }}</el-radio>
							<el-radio :label="state.abnormal">{{ i18n("异常") }}</el-radio>
						</el-radio-group>
					</el-form-item>
					<el-form-item :label="i18n('储值余额限制')" required prop="balanceLimit">
						<AutoFixInput :min="0.01" :max="99999999" :fixed="2" style="width: 100px" v-model="batchParams.balanceLimit" />
						<span> 元</span>
					</el-form-item>
					<el-form-item :label="`${i18n('一共选择')}${selected.length}${i18n('个门店')}`"></el-form-item>
				</el-form>
			</div>
			<span slot="footer" class="dialog-footer">
				<el-button @click="handleBatchClose">{{ i18n("取 消") }}</el-button>
				<el-button type="primary" @click="handleBatchConfirm">{{ i18n("确 定") }}</el-button>
			</span>
		</el-dialog>
		<!-- 批量导入弹框 -->
		<el-dialog class="inner-dialog-center" :title="i18n('批量导入')" :visible="importShow" width="40%" :before-close="handleImportClose">
			<div>
				<el-form label-width="120px" label-position="left">
					<el-form-item :label="i18n('示例模板')">
						<a class="action-hover_download" @click="downloadTemplate">{{ i18n("修改储值余额限额") }}</a>
					</el-form-item>
					<el-form-item :label="i18n('选择文件')">
						<div>{{ i18n("为保障上传成功，建议每次最多上传5000条信息") }}</div>
						<el-upload
							:limit="1"
							:multiple="false"
							:headers="uploadHeaders"
							:action="uploadUrl"
							:auto-upload="false"
							:on-change="doHandleChange"
							:on-error="getErrorInfo"
							:on-success="getSuccessInfo"
							:with-credentials="true"
							class="upload-demo"
							ref="upload"
						>
							<el-button size="small" slot="trigger" type="default">{{ i18n("/公用/导入/选择文件") }}</el-button>
						</el-upload>
					</el-form-item>
				</el-form>
			</div>
			<span slot="footer" class="dialog-footer">
				<el-button @click="handleImportClose">{{ i18n("取 消") }}</el-button>
				<el-button type="primary" @click="handleImportConfirm">{{ i18n("确 定") }}</el-button>
			</span>
		</el-dialog>
		<DownloadCenterDialog :dialogvisiable="fileDialogVisible" :showTip="showTip" @dialogClose="doDownloadDialogClose"> </DownloadCenterDialog>
	</div>
</template>

<script lang="ts" src="./StoreValueManage.ts"></script>

<style lang="scss">
.store-value-manage {
	width: 100%;
	height: 100%;
	background-color: white;
	overflow: auto;
	.current-page {
		height: calc(100% - 10px);
		overflow: auto;
		padding: 20px;

		.el-table {
			td {
				border-bottom: 1px solid #eceef5;
			}

			.cell {
				.el-row {
					margin-top: 15px;

					.label {
						min-width: 120px;
						max-width: 120px;
					}
				}
			}
		}
	}
	.inner-dialog-center {
		display: flex;
		align-items: center;
		justify-content: center;
	}
	.inner-dialog-center .el-dialog {
		margin-top: 0px !important;
	}
}

.action-hover_download {
  cursor: pointer;
  /* 添加手型光标 */
}
</style>
