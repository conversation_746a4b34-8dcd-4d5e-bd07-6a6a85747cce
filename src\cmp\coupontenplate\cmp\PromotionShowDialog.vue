<template>
  <div class="promotion-show-dialog">
    <el-dialog :visible.sync="dialogShow" :close-on-click-modal="false" :show-close="false" width="828px">
      <template slot="title">
        <div class="header">
          <div class="header-text">{{ formatI18n('/公用/券模板详情/已选择') }} {{ tableData.length }} {{
            formatI18n('/公用/券模板详情/个促销单') }}</div>
          <i class="el-icon-close ic-back" @click.stop="doBack"></i>
        </div>
      </template>
      <div class="dialog-content">
        <el-table :data="tableData" v-loading="loading" :row-class-name="tableRowClassName" style="width: 100%" height="430"
          :header-cell-style="{ background: '#DDE2EB', fontWeight: '600', color: '#020203' }" tooltip-effect="light">
          <el-table-column prop="billNumber" :label="formatI18n('/公用/券模板详情/促销单号')" width="140" show-overflow-tooltip>
          </el-table-column>
          <el-table-column :label="formatI18n('/公用/券模板详情/促销主题')" width="140" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{scope.row.topicName || '--'}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="formatI18n('/公用/券模板详情/活动描述')" min-width="160" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{scope.row.activityDesc || '--'}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="formatI18n('/公用/券模板详情/促销日期范围')" width="180">
            <template slot-scope="scope">
              <span>{{ timeFrame(scope.row) }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="formatI18n('/公用/券模板详情/单据状态')" width="80">
            <template slot-scope="scope">
              <div v-if="getStatus(scope.row).name == 'hybrid'">
                <div class="state-line" v-for="item in getStatus(scope.row).list" :key="item.color">
                  <div class="state-border" :style="{ background: item.color }"></div>
                  <div>{{ item.name }}</div>
                </div>
              </div>
              <div class="state-line" v-else>
                <div class="state-border" :style="{ background: getStatus(scope.row).color }"></div>
                <div>{{ getStatus(scope.row).name }}</div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>
<script lang="ts">
import { Vue, Component, Prop, Watch } from "vue-property-decorator";
import PromotionBill from "model/coupon/template/PromotionBill";
import DateUtil from "util/DateUtil";
import CouponInitialApi from "http/coupon/template/CouponTemplateApi";
import CardTemplateApi from "http/card/template/CardTemplateApi";

@Component({
  name: "PromotionShowDialog",
  components: {},
})
export default class PromotionShowDialog extends Vue {
  @Prop({
    type: String,
    default: "",
  })
  templateId: string;
  @Prop({
    type: String,
    default: "coupon",
  })
  billType: "coupon" | "card"; // 促销单类型（券模板/卡模板）

  dialogShow: boolean = false; // 控制模态框的展示
  searchValue: string = "";
  loading: boolean = false;
  tableData: PromotionBill[] = [];
  page = {
    currentPage: 1,
    total: 0,
  };

  @Watch("dialogShow")
  watchDialogShow(value: boolean) {
    if (value) {
      this.queryPromotion(this.templateId);
    }
  }

  handleCurrentChange(val: number) {
    this.page.currentPage = val;
  }

  doSearch() {
    console.log("搜索");
  }

  async queryPromotion(templateId: string) {
    this.loading = true;
    try {
      const method = this.billType === "coupon" ? CouponInitialApi.queryCouponTemplatePromotion : CardTemplateApi.queryCardTemplatePromotion;
      const resp: any = await method(templateId);
      if (resp.code == 2000 && resp.data) {
        this.tableData = resp.data || [];
      } else {
        this.$message.error(resp.msg);
      }
      this.loading = false;
    } catch (err) {
      this.loading = false;
      this.$message.error((err as any).message);
    }
  }

  timeFrame(row: PromotionBill) {
    let start = row.start || "";
    let end = row.finish || "";
    const startTime = DateUtil.format(start, "yyyy-MM-dd");
    const endTime = DateUtil.format(end, "yyyy-MM-dd");
    return `${startTime} ${this.formatI18n("/公用/公共组件/积分活动选择弹框组件/表格/至")} ${endTime}`;
  }

  getStatus(row: PromotionBill) {
    const state = row.execState;
    if (state == "yet") {
      return {
        name: this.formatI18n("/公用/券模板详情/未开始"),
        color: "#FFAA00",
      };
    } else if (state == "finish") {
      return {
        name: this.formatI18n("/公用/券模板详情/已结束"),
        color: "#A1B0C8",
      };
    } else if (state == "sleeping") {
      return {
        name: this.formatI18n("/公用/券模板详情/休眠中"),
        color: "#A1B0C8",
      };
    } else if (state == "hybrid") {
      return {
        name: "hybrid",
        color: "#58B929",
        list: [
          {
            name: this.formatI18n("/公用/券模板详情/执行中"),
            color: "#58B929",
          },
          {
            name: this.formatI18n("/公用/券模板详情/休眠中"),
            color: "#A1B0C8",
          },
        ],
      };
    } else {
      return {
        name: this.formatI18n("/公用/券模板详情/执行中"),
        color: "#58B929",
      };
    }
  }

  tableRowClassName(row: any) {
    const { rowIndex } = row;
    if (rowIndex % 2 == 0) {
      return "black-row";
    } else {
      return "normal-row";
    }
  }

  doBack() {
    this.dialogShow = false;
  }
  open() {
    this.dialogShow = true;
  }
}
</script>
<style lang="scss" scoped>
.promotion-show-dialog {
  .header {
    padding-top: 10px;
    display: flex;
    justify-content: space-between;
    align-content: center;

    .header-text {
      flex: 1;
      height: 22px;
      font-size: 14px;
      font-weight: 600;
      color: #020203;
      line-height: 22px;
    }

    .ic-back {
      color: #8a9099;
      font-size: 16px;
      font-weight: 700;
      line-height: 22px;
      cursor: pointer;
    }
  }

  .dialog-content {
    width: 100%;

    .search-input {
      width: 200px;
      height: 32px;
      margin-bottom: 8px;
    }

    .state-line {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .state-border {
      display: block;
      width: 6px;
      height: 6px;
      margin-right: 8px;
      border-radius: 50%;
    }

    .btn-delete {
      color: #1597ff;
      cursor: pointer;
    }
  }

  .dialog-page {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-content: center;
    margin-top: 24px;
  }
}

::v-deep.el-dialog__body {
  padding-top: 12px;
}

::v-deep.black-row {
  height: 28px;
  background: #f4f6fa;
  font-size: 12px;
  color: #020203;
  line-height: 18px;
  cursor: pointer;
}

::v-deep.normal-row {
  height: 28px;
  font-size: 12px;
  color: #020203;
  line-height: 18px;
  cursor: pointer;
}
</style>