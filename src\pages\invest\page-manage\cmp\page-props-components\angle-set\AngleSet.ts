import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import emitter from 'util/emitter';
import { FormMode } from 'model/local/FormMode';
import FormDefUtil from 'util/FormDefUtil';
import ElasticLayer from '../../elastic-layer/ElasticLayer.vue';
import I18nPage from 'common/I18nDecorator';
import { CmsConfigChannel } from 'model/template/CmsConfig';

@Component({
  name: 'AngleSet',
  mixins: [emitter],
  components: { ElasticLayer },
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理',
    '/页面/导航设置',
  ],
  auto: true
})
export default class AngleSet extends Vue {
  @Prop({ type: Boolean, default: false })
  readonly: boolean; //
  @Prop({ type: String, default: 'AngleSet' })
  validateName: string; // 组件校验名称，editPage通过该名称进行组件校验及校验信息清除
  @Prop({ type: Object })
  value: any; // 数据模型
  @Prop({ type: String, default: '页面标题名称' })
  label: string; // label名
  @Prop({ type: String })
  prefixDescribe: string; // 前置描述
  @Prop({ type: String })
  suffixDescribe: string; // 后置描述

  @Prop({ type: Boolean, default: true })
  required: boolean; // 是否必填
  @Prop({ type: String })
  relativeValidateName: string; // 联动校验名称
  @Prop()
  formKey: any;
  @Prop({
    type: Object,
    default: () => {
      return {
        isShowColor: true,
      };
    },
  })
  config: any; // 配置项
  @Prop() advertiseChannel: Nullable<CmsConfigChannel[]>; //当前页面可用的投放渠道
  FormModeType = FormMode;
  $refs: any;
  labelPosition: string = 'top';
  imageList: any[] = [];
  options = [
    { caption: this.i18n('全部活动'), key: 'all' },
    { caption: this.i18n('指定活动'), key: 'part' },
  ];
  rules = {
    content: [{
      trigger: 'change', validator: (rule: any, value: any, callback: any) => {

        if (this.value.propActivityRange == 'part' && this.value.propActivityIds.length == 0) {
          callback(new Error(this.i18n('请选择指定活动')));
        } else {
          callback();
        }
      },
    }],
    propActivityType: [
      { required: true, message: this.i18n('请选择'), trigger: 'change' }
    ],
  };
  oldVal: string = ''
  @Watch("value.propActivityRange")
  onActivityRange() {
    this.validate(() => { });
  }
  @Watch("value.propActivityType")
  onActivityType(newValue: any, oldValue: any) {
    this.oldVal = oldValue
  }
  get formMode() {
    if (this.validateName === 'mainTitle') {
      return FormDefUtil.getFormMode(this.readonly, this.validateName, this.formKey);
    } else {
      return FormDefUtil.getFormMode(this.readonly, this.validateName + 'mainTitle', this.formKey);
    }
  }
  get signatureResult() {
    return this.$store.state.credential;
  }

  get option() {
    const arr = [
      {
        value: 'PointsExchangeCouponActivityRule',
        label: this.i18n('积分兑换券')
      },
      {
        value: 'MiniProgramGainCouponActivityRule',
        label: this.i18n('小程序领券')
      }
    ]
    if (this.advertiseChannel?.length === 1 && this.advertiseChannel[0] === CmsConfigChannel.WEIXIN) {
      arr.push({
        value: 'WeiXinAppletIssueCouponActivityRule',
        label: this.i18n('小程序领微信券')
      })
    }
    if (this.advertiseChannel?.length === 1 && this.advertiseChannel[0] === CmsConfigChannel.ALIPAY) {
      arr.push({
        value: 'AliAppletIssueCouponActivityRule',
        label: this.i18n('/设置/系统设置/小程序领支付宝券')
      })
    }
    return arr
  }

  changechecked(val: any) {
    this.value.propActivityIds = [...new Set(val)];
    this.$emit('input', this.value);
    this.$emit('change', this.value);
  }
  handleChange() {
    if (this.value.propActivityType === '') {
      this.$alert(this.i18n('请选择投放活动类型'), this.i18n('提示'), {
        confirmButtonText: this.i18n('确定'),
        callback: action => {
          this.value.propActivityRange = 'all'
        }
      });
      return
    }
    if (this.value.propActivityRange == 'all') {
      this.value.propActivityIds = []
      console.log(this.value.propActivityIds, 'fsafassasgasg');

    }
    this.$emit('input', this.value);
    this.$emit('change', this.value);
    this.validate(() => { });
  }
  handleChanges() {
    if (this.value.propActivityIds.length !== 0) {
      this.$confirm(this.i18n('切换投放活动类型，将清空原有的投放内容'), this.i18n('切换投放活动类型'), {
        confirmButtonText: this.i18n('确定'),
        cancelButtonText: this.i18n('取消'),
      }).then(() => {
        this.value.propActivityIds = []
        this.$emit('input', this.value);
        this.$emit('change', this.value);
      }).catch(() => {
        this.value.propActivityType = this.oldVal
        this.$emit('input', this.value);
        this.$emit('change', this.value);
      });
    }
    this.$emit('input', this.value);
    this.$emit('change', this.value);
    this.validate(() => { });
  }
  goUp() {
    this.$refs.childRef.open(this.value.propActivityIds || [])
  }

  validate(callback: any) {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.validate(callback);
    }
  }

  clearValidate() {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.clearValidate();
    }
  }
}
