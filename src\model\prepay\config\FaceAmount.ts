/*
 * @Author: 黎钰龙
 * @Date: 2024-05-08 14:50:01
 * @LastEditTime: 2024-05-08 14:52:01
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\prepay\config\FaceAmount.ts
 * 记得注释
 */
export default class FaceAmount {
  // uuid
  uuid: Nullable<string> = null
  // 储值面额
  faceAmountList: number[] = []
  // 充值须知
  depositNotice: Nullable<string> = null
  // 充值协议
  depositAgreement: Nullable<string> = null
  // 是否展示自定义金额,0——不展示，1——展示
  showCustomizedAmount: Nullable<boolean> = null
  // 自定义充值基数，范围0.01-99999.99
  customizedBase: Nullable<number> = null
  // 默认充值金额
  appletDefaultDepositAmount: Nullable<number> = null
}