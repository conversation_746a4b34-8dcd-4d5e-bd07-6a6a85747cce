import MutableNsid from "model/common/MutableNsid";
import Channel from "model/common/Channel";
import BTradeFavLine from "model/member/BTradeFavLine";
import BTradePayLine from "model/member/BTradePayLine";
import BTradeGoods from "model/member/BTradeGoods";

export default class BMemberTrade {
  // crm交易uuid
  uuid: Nullable<string> = null
  // 交易id
  tradeId: Nullable<MutableNsid> = null
  // 渠道
  channel: Nullable<Channel> = null
  // 渠道名称
  channelName: Nullable<string> = null
  // 交易时间
  tranTime: Nullable<Date> = null
  // 交易号
  tradeNo: Nullable<string> = null
  //
  occurredOrgId: Nullable<string> = null
  // 发生组织名称。
  occurredOrgName: Nullable<string> = null
  // pos号
  posNo: Nullable<string> = null
  // 原总金额
  stdAmount: Nullable<number> = null
  // 实付金额
  total: Nullable<number> = null
  // 优惠金额
  favAmount: Nullable<number> = null
  // 关联原单
  sourceTradeId: Nullable<MutableNsid> = null
  // 交易优惠明细
  favLines: BTradeFavLine[] = []
  // 交易支付明细
  payLines: BTradePayLine[] = []
  // 交易商品
  goods: BTradeGoods[] = []
}
