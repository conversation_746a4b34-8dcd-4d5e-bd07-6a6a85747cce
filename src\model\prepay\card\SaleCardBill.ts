/*
 * @Author: 黎钰龙
 * @Date: 2023-10-11 18:02:52
 * @LastEditTime: 2023-10-16 09:56:30
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\prepay\card\SaleCardBill.ts
 * 记得注释
 */
import IdName from "model/common/IdName"
import Payment from "model/common/Payment"

export default class SaleCardBill {
  // 售卡单号
  billNumber: Nullable<string> = null
  // 起始卡号
  startCardCode: Nullable<string> = null
  // 截至卡号
  endCardCode: Nullable<string> = null
  // 售卡数量
  makeQty: Nullable<number> = null
  // 备注
  remark: Nullable<string> = null
  // 发生组织
  occurredOrg: Nullable<IdName> = null
  // 营销中心
  marketingCenter: Nullable<string> = null
  // 卡模板号
  cardTemplateNumber: Nullable<string> = null
  // 卡模板名称
  cardTemplateName: Nullable<string> = null
  // 状态INITIAL：未审核； SALEING, // 已审核(售卡中) ；FINISH：制卡完成
  state: Nullable<string> = null
  // 售价
  price: Nullable<number> = null
  // 总售价
  total: Nullable<number> = null
  // 折扣
  discount: Nullable<number> = null
  // 购买客户
  buyer: Nullable<string> = null
  // 售卖员工
  salesclerk: Nullable<IdName> = null
  // 支付方式
  payments: Payment[] = []
  //售卡明细导出日志下载key
  downOssKey: Nullable<string> = null
  // 创建时间
  created: Nullable<Date> = null
  // 最后修改时间
  lastModified: Nullable<Date> = null
  // 操作人
  operator: Nullable<string> = null
}