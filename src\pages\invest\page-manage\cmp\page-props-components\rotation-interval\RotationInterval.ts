import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import emitter from 'util/emitter';
import { FormMode } from 'model/local/FormMode';
import FormDefUtil from 'util/FormDefUtil';
import I18nPage from 'common/I18nDecorator';


@Component({
  name: 'RotationInterval',
  mixins: [emitter],
  // components: { FormDetail },
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理'
  ],
  auto: true
})

export default class RotationInterval extends Vue {
  @Prop({ type: Boolean, default: false })
  readonly: boolean; //
  @Prop({ type: String, default: 'TitleFontColor' })
  validateName: string; // 组件校验名称，editPage通过该名称进行组件校验及校验信息清除
  @Prop({ type: Object })
  value: any; // 数据模型
  @Prop({ type: String, default: '标题字体色' })
  label: string; // label名
  @Prop({ type: String })
  prefixDescribe: string; // 前置描述
  @Prop({ type: String })
  suffixDescribe: string; // 后置描述

  @Prop({ type: Boolean, default: true })
  required: boolean; // 是否必填
  @Prop({ type: String })
  relativeValidateName: string; // 联动校验名称
  @Prop()
  formKey: any;
  @Prop({
    type: Object,
    default: () => { },
  })
  config: any; // 配置项
  FormModeType = FormMode;
  $refs: any;
  labelPosition: string = 'top';

  rules = {
    placeName: [{ required: false, message: this.i18n('请输入'), trigger: ['blur', 'change'] }],
  };

  get formMode() {
    if (this.validateName === 'RotationInterval') {
      return FormDefUtil.getFormMode(this.readonly, this.validateName, this.formKey);
    } else {
      return FormDefUtil.getFormMode(this.readonly, this.validateName + 'RotationInterval', this.formKey);
    }
  }


  created() {
    //
    console.log('找到轮播时间组件，RotationInterval created');
    if(!this.value.propInterval) this.value.propInterval = '3'
  }


  handleChange() {
    if (!this.value.propInterval || this.value.propInterval === '' || this.value.propInterval === null || this.value.propInterval === undefined) {
      this.value.propInterval = '3'
    } 

    this.$emit('input', this.value);
    this.$emit('change', this.value);
    this.validate(() => { });
  }


  mounted() {
    if (this.validateName) {
      this['dispatch']('EditPage', 'nf.edit.addForm', [this]);
    }
  }

  beforeDestroy() {
    this['dispatch']('EditPage', 'nf.edit.removeForm', [this]);
  }

  validate(callback: () => void) {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.validate(callback);
    }
  }

  clearValidate() {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.clearValidate();
    }
  }
}
