<template>
    <div class="member-tag">
        <BreadCrume :panelArray="panelArray">
            <template slot="operate">
            </template>
        </BreadCrume>
        <ListWrapper class="current-page" :showQuery="hasOptionPermission('/分析/自定义标签','标签维护')" style="height: 95%">
            <template slot="query">
                <el-row>
                    <el-col :span="24">
                      <form-item :label="formatI18n('/分析/自定义标签/自定义标签/标签名称')">
                        <el-input maxlength="128" style="width: 400px" v-model="id"></el-input>
                        &nbsp;&nbsp;&nbsp;&nbsp;
                        <i class="iconfont ic-info"></i>{{
                          formatI18n('/分析/自定义标签/自定义标签/标签名称/提示/如：职业。最多可输入128个字，不可与已有标签名称重复。')
                        }}
                      </form-item>
                    </el-col>
                </el-row>
                <el-row style="margin-top: 10px">
                    <el-col :span="24">
                      <form-item :label="formatI18n('/分析/自定义标签/自定义标签/标签值')">
                        <CodeInput
                            :recieveArray="tags"
                            @selectCode="onSelectCode"
                            style="width: 400px">
                        </CodeInput>
                        &nbsp;&nbsp;&nbsp;&nbsp;
                        <i class="iconfont ic-info"></i>{{
                          formatI18n('/分析/自定义标签/自定义标签/标签值/提示/如：老师，公务员。最多可设置20个标签值，每个最多可输入128个字。')
                        }}
                      </form-item>
                    </el-col>
                </el-row>
                <el-row style="margin-top: 10px">
                    <el-col :span="8">
                      <form-item>
                        <el-button @click="doAdd" type="primary">
                          {{ formatI18n('/分析/自定义标签/自定义标签/添加') }}
                        </el-button>
                        <el-button @click="doReset">
                          {{ formatI18n('/分析/自定义标签/自定义标签/清空') }}
                        </el-button>
                      </form-item>
                    </el-col>
                </el-row>

            </template>
          <template slot="btn">
            <el-checkbox
                @change="checkedAllRow"
                style="margin-left: 14px;margin-right: 10px"
                v-model="singleAll">
            </el-checkbox>
            {{ selectLabel(selectedArr.length) }}
            <el-button @click="doBatchDelete" style="color: red" v-if="hasOptionPermission('/分析/自定义标签','标签维护')">
              {{ formatI18n('/分析/自定义标签/自定义标签/批量删除') }}
            </el-button>
            <el-input v-if="hasOptionPermission('/分析/自定义标签','标签查看')" @change="doSearchByKey"
                      :placeholder="formatI18n('/分析/自定义标签/自定义标签/搜索标签名称\\/标签值')"
                      style="width:300px;float: right"
                      suffix-icon="el-icon-search"
                      v-model="searchContent"/>
          </template>
            <template slot="list">
                <el-table
                        :data="tableData"
                        @selection-change="handleSelectionChange"
                        ref="table"
                        style="width: 100%;margin-top: 10px">
                    <el-table-column
                            type="selection"
                            width="55">
                    </el-table-column>
                  <el-table-column :label="formatI18n('/分析/自定义标签/自定义标签/标签名称')" prop="tagId">

                  </el-table-column>
                  <el-table-column :formatter="formatter" :label="formatI18n('/分析/自定义标签/自定义标签/标签值')" prop="tagValues">
                  </el-table-column>
                  <el-table-column align="center" :label="formatI18n('/会员/会员资料/操作')">
                    <template slot-scope="scope">
                      <el-button @click="doEdit(scope.row)" type="text" v-if="hasOptionPermission('/分析/自定义标签','标签维护')">
                        {{
                          formatI18n('/营销/券礼包活动/券礼包活动/修改')
                        }}
                      </el-button>
                      <el-button @click="doDelete(scope.row)" type="text"
                                 v-if="hasOptionPermission('/分析/自定义标签','标签维护')">{{
                          formatI18n('/营销/券礼包活动/券礼包活动/删除')
                        }}
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
            </template>
            <!--分页栏-->
            <template slot="page">
                <el-pagination
                        :current-page="page.currentPage"
                        :page-size="page.size"
                        :page-sizes="[10, 20, 30, 40]"
                        :total="page.total"
                        @current-change="onHandleCurrentChange"
                        @size-change="onHandleSizeChange"
                        background
                        layout="total, prev, pager, next, sizes,  jumper">
                </el-pagination>
            </template>
        </ListWrapper>
        <TagEditDialog
                :data="dialogData"
                :dialogShow="dialogShow"
                @dialogClose="doDialogClose">
        </TagEditDialog>
    </div>
</template>

<script lang="ts" src="./MemberTag.ts">
</script>

<style lang="scss">
    .member-tag{
        background-color: white;
        height: 100%;
        width: 100%;
        overflow: hidden;
        .current-page{
            .el-select{
                width: 100%;
            }
        }

        .el-range-editor.el-input__inner{
            width: 100%;
        }
    }
</style>