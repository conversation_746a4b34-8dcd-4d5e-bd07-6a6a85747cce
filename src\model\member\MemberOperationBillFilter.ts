class orderMap {
    key: Nullable<'occurredTime' | 'created' | 'value'> = null
    value: 'asc' | 'desc'
}
export default class MemberOperationBillFilter {
    // 单号
    billNumberIn: Nullable<string[]> = null;
    // 状态INITIAL：未审核；AUDITED：已审核
    stateEquals: Nullable<string> = null;
    // 来源：impt-导入；create-界面新建
    sourceEquals: Nullable<string> = null;
    // 发生组织
    occurredOrgIdEquals: Nullable<string> = null;
    // 发生区域
    zoneIdEquals: Nullable<string> = null;
    // 营销中心
    marketingCenterEquals: Nullable<string> = null;
    // 审核时间
    auditedEquals: Nullable<string> = null;
    // 审核人
    auditorEquals: Nullable<string> = null;
    // [排序字段]
    // 排序，key表示排序的字段，可选值：occurredTime、created；value表示排序方向，可选值为：asc, desc
    sorters: Nullable<orderMap[]> = null
    // 页数>=0
    page: Nullable<number> = 0;
    // 页面大小>0
    pageSize: Nullable<number> = 10;
    // 创建时间范围[]
    createdBetweenClosedClosed: Nullable<string[]> = null;
    // 最后修改时间范围[]
    lastModifiedBetweenClosedClosed: Nullable<string[]> = null;
    // 身份识别码
    identityId: Nullable<String> = null
}