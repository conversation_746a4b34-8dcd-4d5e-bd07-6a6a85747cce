/*
 * @Author: 黎钰龙
 * @Date: 2023-03-02 18:53:09
 * @LastEditTime: 2023-03-03 10:11:29
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\equityCard\equityCardApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import BEquityCardTemplate from 'model/benefit/BEquityCardTemplate'

export default class EquityCardApi {
  /**
   * 权益卡保存
   *
   */
  static save(body: BEquityCardTemplate): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/equityCardTemplate/saveNew`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 权益卡修改
   *
   */
  static modify(body: BEquityCardTemplate): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/equityCardTemplate/modify`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 权益卡停用启用
   *
   */
  static invocate(number: string, state: string): Promise<Response<void>> {
    return ApiClient.server().get(`/v1/equityCardTemplate/startOrStop?number=${number}&state=${state}`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 权益卡列表查询
   *
   */
  static query(): Promise<Response<BEquityCardTemplate[]>> {
    return ApiClient.server().post(`/v1/equityCardTemplate/query`, {},).then((res) => {
      return res.data
    })
  }

}
