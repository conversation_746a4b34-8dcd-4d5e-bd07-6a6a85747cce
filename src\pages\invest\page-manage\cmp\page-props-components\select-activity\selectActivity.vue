<!--
 * @Author: 黎钰龙
 * @Date: 2024-07-25 15:18:01
 * @LastEditTime: 2024-07-31 11:44:14
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\cmp\page-props-components\select-activity\selectActivity.vue
 * 记得注释
-->
<template>
  <div>
    <el-form inline :label-position="labelPosition" :model="value" :rules="rules" ref="form" class="global-set">
      <!-- <el-form-item label="投放活动类型" prop="region" style="width: 100%" class="activity-input">
        <el-select @change="handleChanges" v-model="value.propActivityType" placeholder="请选择">
          <el-option v-for="item in option" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item :label="i18n('投放内容')" prop="content">
        <el-radio v-for="item in options" :key="item.key" :label="item.key" v-model="value.propActivityRange" @change="handleChange">
          {{ item.caption }}
        </el-radio>
        <span class="text" v-show="value.propActivityRange === 'part'">
          {{ i18n('已选择') }}
          <span>{{ value.propActivityIds.length }}</span>
          {{ i18n('个活动') }}
        </span>
        <el-button style="margin-left: 110px" v-show="value.propActivityRange === 'part'" type="primary" @click="goUp">{{ i18n('选择') }}</el-button>
      </el-form-item>
    </el-form>
    <ElasticLayer ref="childRef" @submit="handleSubmit" :activityType="config.id" />
  </div>
</template>

<script lang="ts" src="./selectActivity.ts"></script>

<style lang="scss" scoped>
.global-set {
  .activity-input {
    margin-bottom: 10px;
    ::v-deep .el-form-item__content {
      width: 100% !important;
      .el-select {
        width: 100%;
      }
    }
  }
  .text {
    font-size: 12px;
    color: #a1a6ae;
    line-height: 16px;
  }
}
</style>
