<template>
  <div class="chart-block">
    <div class="header">
      <div style="flex: 1">
        <div>
          <span class="draw-title">{{ getChartTitle }}</span>
          <el-popover v-if="!isOnlyView" placement="top" width="260" v-model="editTitleVisible">
            <p>{{i18n('重命名')}}</p>
            <el-input type="textarea" :rows="4" v-model="newTitle" maxlength="50" resize="none"></el-input>
            <div style="text-align: right; margin-top: 12px">
              <el-button size="mini" type="text" @click="editTitleVisible = false">{{i18n('/公用/按钮/取消')}}</el-button>
              <el-button type="primary" size="mini" @click="confirmChangeTitle">{{i18n('/公用/按钮/确定')}}</el-button>
            </div>
            <i slot="reference" class="el-icon-edit"></i>
          </el-popover>
        </div>
        <div class="gray-tips" style="margin-top: 2px" v-if="!isMultiDimension">
          <i18n k="/数据/客群画像/画像目标客群中{0}拥有{1}">
            <template slot="0">
              <span>
                {{ getDimensionPercent('target') }}
              </span>
            </template>
            <template slot="1">
              <span style="margin:0 2px">
                {{ dimensionTitleList[0] }}
              </span>
            </template>
          </i18n>
          <i18n k="/数据/客群画像/画像对比客群中{0}拥有{1}" v-if="hasCompare" style="margin-left: 8px">
            <template slot="0">
              <span>
                {{ getDimensionPercent('compare') }}
              </span>
            </template>
            <template slot="1">
              <span style="margin:0 2px">
                {{ dimensionTitleList[0] }}
              </span>
            </template>
          </i18n>
        </div>
      </div>
      <div v-if="!isOnlyView">
        <span class="span-btn" @click="doEdit">{{i18n('/会员/会员资料/编辑')}}</span>
        <span class="span-btn" style="margin-left: 6px" @click="doRemove">{{i18n('/公用/按钮/删除')}}</span>
      </div>
    </div>
    <div class="option-line">
      <!-- 图表类型 -->
      <el-dropdown trigger="click" @command="changeChartType">
        <span class="drop-down">
          {{ getChartName(chartType) }}
          <i class="el-icon-arrow-down el-icon--right"></i>
        </span>
        <el-dropdown-menu v-if="!isOnlyView" slot="dropdown">
          <el-dropdown-item command="strip" v-if="!isMultiDimension">
            {{getChartName('strip')}}
          </el-dropdown-item>
          <el-dropdown-item command="bar">
            {{getChartName('bar')}}
          </el-dropdown-item>
          <el-dropdown-item command="ring" v-if="!isMultiDimension">
            {{getChartName('ring')}}
          </el-dropdown-item>
          <el-dropdown-item command="table">
            {{getChartName('table')}}
          </el-dropdown-item>
          <el-dropdown-item command="pie" v-if="!isMultiDimension">
            {{getChartName('pie')}}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>

      <!-- 展示数据数量 -->
      <el-dropdown style="margin-left: 8px" trigger="click" @command="changeChartCount">
        <span class="drop-down">
          {{getCountName(chartData.range)}}
          <i class="el-icon-arrow-down el-icon--right"></i>
        </span>
        <el-dropdown-menu v-if="!isOnlyView" slot="dropdown">
          <el-dropdown-item :command="7">
            {{getCountName(7)}}
          </el-dropdown-item>
          <el-dropdown-item :command="10">
            {{getCountName(10)}}
          </el-dropdown-item>
          <el-dropdown-item :command="15">
            {{getCountName(15)}}
          </el-dropdown-item>
          <el-dropdown-item :command="20">
            {{getCountName(20)}}
          </el-dropdown-item>
          <el-dropdown-item :command="25">
            {{getCountName(25)}}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>

      <!-- 排序方式 -->
      <el-dropdown style="margin-left: 8px" trigger="click" @command="changeChartSort">
        <span class="drop-down">
          {{getSortName(chartData.sort)}}
          <i class="el-icon-arrow-down el-icon--right"></i>
        </span>
        <el-dropdown-menu v-if="!isOnlyView" slot="dropdown">
          <el-dropdown-item command="coverageDesc">
            {{getSortName('coverageDesc')}}
          </el-dropdown-item>
          <el-dropdown-item command="coverageAsc">
            {{getSortName('coverageAsc')}}
          </el-dropdown-item>
          <!-- <el-dropdown-item command="dimensionAsc">
            {{getSortName('dimensionAsc')}}
          </el-dropdown-item>
          <el-dropdown-item command="dimensionDesc">
            {{getSortName('dimensionDesc')}}
          </el-dropdown-item> -->
        </el-dropdown-menu>
      </el-dropdown>

      <!-- 区间展示规则 -->
      <span class="drop-down" style="margin-left: 8px" @click="doEditRange" v-if="isNumType">
        {{getRangeText}}
        <i class="el-icon-arrow-down el-icon--right"></i>
      </span>

      <!-- 按年/月/日展示 -->
      <span class="drop-down" style="margin-left: 8px" @click="doEditCycle" v-if="isDateType">
        {{getCycleText}}
        <i class="el-icon-arrow-down el-icon--right"></i>
      </span>
    </div>
    <template v-if="chartType !== 'table'">
      <div class="echart-block" :style="{width: hasCompare ? '48%' : '100%'}" :id="'myEcharts' + '_target_' + chartData.uuid"
        :ref="'myEcharts' + '_target_' + chartData.uuid">
      </div>
      <div v-if="hasCompare" style="width: 45%" class="echart-block" :id="'myEcharts' + '_compare_' + chartData.uuid"
        :ref="'myEcharts' + '_compare_' + chartData.uuid">
      </div>
    </template>
    <el-table :data="tableData" height="410" class="echart-block" v-else border>
      <el-table-column prop="title" :label="i18n('画像人群')" />
      <el-table-column prop="dimension" :label="dimensionTitleList[0]" />
      <el-table-column prop="dimension2" v-if="dimensionTitleList[1]" :label="dimensionTitleList[1]" />
      <el-table-column prop="value" :label="tableValueLabel" />
      <el-table-column :label="i18n('占比')">
        <template slot-scope="scope">
          <template v-if="!isNaN(scope.row.percent)">
            {{scope.row.percent}}%
          </template>
          <template v-else>--</template>
        </template>
      </el-table-column>
    </el-table>
    <CustomRangeDialog ref="customRangeDialog" :rangeList="rangeList" :titleList="numTitleList" @submit="doSubmitRange">
    </CustomRangeDialog>
    <SumCycleDialog ref="sumCycleDialog" :cycleList="sumCycleList" :titleList="dataTitleList" @submit="doSubmitCycle">
    </SumCycleDialog>
  </div>
</template>

<script lang="ts" src="./PortraitDrawChart.ts">
</script>

<style lang="scss" scoped>
.chart-block {
  width: 100%;
  height: 530px;
  border-radius: 4px;
  border: 1px solid #d7dfeb;
  .header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 12px;
    box-sizing: border-box;
    border-bottom: 1px solid #d7dfeb;
    .draw-title {
      color: #222222;
      margin-right: 6px;
    }
    .el-icon-edit {
      color: #a8a8b6;
      cursor: pointer;
    }
  }
  .option-line {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    .drop-down {
      display: flex;
      align-items: center;
      padding: 4px;
      background: #f1f5f9;
      border-radius: 2px;
      font-size: 12px;
      font-weight: 400;
      color: #212738;
      cursor: pointer;
    }
  }
  .echart-block {
    display: inline-block;
    height: 410px;
    width: 100%;
    margin-top: 16px;
  }
}
</style>