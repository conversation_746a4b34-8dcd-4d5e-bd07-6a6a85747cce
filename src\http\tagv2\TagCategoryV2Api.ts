import ApiClient from "http/ApiClient";
import Response from "model/default/Response";
import TagCategory from "model/precisionmarketing/tagcategory/TagCategory";
import TagCategoryDeleteCheckResultV2 from "model/precisionmarketing/tagv2/TagCategoryDeleteCheckResultV2";
import TagCategoryFilterV2 from "model/precisionmarketing/tagv2/TagCategoryFilterV2";
import TagCategoryV2 from "model/precisionmarketing/tagv2/TagCategoryV2";

export default class TagCategoryV2Api {
  /**
   * 修改标签分类排序，changedUuid为移动的分类节点uuid,nextUuid为移动后此节点的下一个节点uuid
   *
   */
  static changeOrder(changedUuid: string, nextUuid: string): Promise<Response<void>> {
    return ApiClient.server()
      .get(`/v2/precision-marketing/tag-template-category/changeOrder/${changedUuid}/${nextUuid}`, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 删除标签分类信息
   *
   */
  static delete(uuid: string): Promise<Response<void>> {
    return ApiClient.server()
      .post(`/v2/precision-marketing/tag-template-category/delete/${uuid}`, {}, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 删除标签分类信息校验
   *
   */
  static deleteCheck(uuid: string): Promise<Response<TagCategoryDeleteCheckResultV2>> {
    return ApiClient.server()
      .post(`/v2/precision-marketing/tag-template-category/delete/check/${uuid}`, {}, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 根据名称查询分类信息
   *
   */
  static getBy(name: string): Promise<Response<TagCategory>> {
    return ApiClient.server()
      .get(`/v2/precision-marketing/tag-template-category/getBy/${name}`, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 查询标签分类信息
   *
   */
  static query(body: TagCategoryFilterV2): Promise<Response<TagCategoryV2[]>> {
    return ApiClient.server()
      .post(`/v2/precision-marketing/tag-template-category/query`, body, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 修改或保存标签分类，以uuid区分新建或修改,返回uuid
   *
   */
  static saveOrUpdate(body: TagCategoryV2): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v2/precision-marketing/tag-template-category/saveOrUpdate`, body, {})
      .then((res) => {
        return res.data;
      });
  }
}
