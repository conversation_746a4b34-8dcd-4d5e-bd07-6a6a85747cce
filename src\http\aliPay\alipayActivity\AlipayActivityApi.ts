import ApiClient from "http/ApiClient";
import BAliCouponActivity from "model/alipay/alipayActivity/BAliCouponActivity";
import BAlipayCouponTempalteInfo from "model/alipay/alipayActivity/BAlipayCouponTempalteInfo";
import Response from "model/common/Response";

export default class AlipayActivityApi {
  /**
   * 新建或修改支付包活动
   * 新建或修改阿里活动
   *
   */
  static saveWeiXinCouponActivityV2(
    body: BAliCouponActivity
  ): Promise<Response<any>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/saveAppletIssueCouponActivity`, body, {})
      .then((res) => {
        return res.data;
      });
  }
  /**
   * 支付包活动详情
   *
   */
  static getDtl(id: any): Promise<Response<any>> {
    return ApiClient.server()
      .get(`/v1/coupon-activity/getAppletIssueCouponActivity/${id}`, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 支付包活动审核
   *
   */
  static audit(id: any): Promise<Response<any>> {
    return ApiClient.server()
      .get(`/v1/coupon-activity/aliAppletIssueCouponActivityAudit/${id}`, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 支付包活动终止
   *
   */
  static stop(id: any): Promise<Response<any>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/stop/${id}`, {}, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 支付包活动 生成投放二维码
   *
   */
  static getQrCode(number: any): Promise<Response<any>> {
    return ApiClient.server()
      .get(`/v1/coupon-activity/createAliAppletQrCode/${number}`, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 获取支付宝券信息
   *
   */
  static getAliCouponInfo(
    activityNum: any
  ): Promise<Response<Nullable<BAlipayCouponTempalteInfo>>> {
    return ApiClient.server()
      .get(
        `/v1/coupon-activity/getAliAppletPaymentCouponInfo/${activityNum}`,
        {}
      )
      .then((res) => {
        return res.data;
      });
  }
}
