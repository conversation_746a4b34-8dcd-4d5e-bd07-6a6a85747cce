import GoodsRange from 'model/common/GoodsRange'
import MetricProp from "model/precisionmarketing/tag/tagrule/customize/consumer/MetricProp";

export default class UserConsumerItem {
  // 时间条件：lstSeven-近7天;lstThirty-近30天;lstThreeMonth-近3个月;lstHalfYear-近半年;lstYear-近一年;thisMonth-本月;prevMonth-上月;thisYear-今年;
  prop: Nullable<string> = null
  // 指标
  metricProp: Nullable<MetricProp> = null
  // 商品
  goods: Nullable<GoodsRange> = null
}