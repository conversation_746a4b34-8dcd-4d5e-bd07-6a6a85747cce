<template>
    <div>
        <el-dialog :title="formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/点击下一步按钮/弹框/标题/修改信息保存')" :visible.sync="dialogShow" :before-close="doBeforeClose" class="modify-confirm">
            <div class="wrap">
                {{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/点击下一步按钮/弹框/修改的信息需要再次提交才会生效，确定提交并前往下一页吗？')}}
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="doConfirmClose">{{formatI18n('/公用/按钮', '确定')}}</el-button>
                <el-button @click="doCancel">{{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/点击下一步按钮/弹框/直接进入下一页')}}</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script lang="ts" src="./ModifyConfirm.ts">
</script>

<style lang="scss">
.modify-confirm{
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>