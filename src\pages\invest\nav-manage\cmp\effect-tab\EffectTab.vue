<template>
    <div class="effect-box">
        <ul>
            <li v-for="(item, index) in currentData.menuSettings" :key="index" @click="doAheckedActive(index)">
                <!-- 中心菜单未选中 -->
                <div class="tab-box rudder-tab"
                    v-if="currentData.navigationType === 'rudder' && !item.isActive && ((currentData.menuSettings.length === 5 && index === 2) || (currentData.menuSettings.length === 3 && index === 1))">
                    <div class="box-top">
                        <img v-if="item.unselectedIcon" :src="item.unselectedIcon" class="center-img">
                    </div>
                    <span class="tab-title" :style="{ 'color': currentData.iconColorType === 'custom' ? currentData.unselectedFontColor : currentData.unselectedColor }">{{ item.name }}</span>
                </div>
                 <!-- 中心菜单选中 -->
                 <div class="tab-box rudder-tab"
                    v-else-if="currentData.navigationType === 'rudder' && item.isActive && ((currentData.menuSettings.length === 5 && index === 2) || (currentData.menuSettings.length === 3 && index === 1))">
                    <div class="box-top">
                        <img v-if="item.selectedIcon" :src="item.selectedIcon" class="center-img">
                    </div>
                    <span class="tab-title" :style="{ 'color': currentData.iconColorType === 'custom' ? currentData.selectedFontColor : currentData.selectedColor }">{{ item.name }}</span>
                </div>
                <!-- 普通菜单选中 -->
                <div class="tab-box normal-tab" v-else-if="item.isActive">
                    <div class="box-top">
                        <i v-if="item.isUseIcon" :class="['iconfont', item.selectedIcon]"
                            :style="{'font-size': '24px', 'color':  currentData.iconColorType === 'custom' ? currentData.selectedIconColor : currentData.selectedColor }"></i>
                        <img v-else :src="item.selectedIcon" class="icon-img">
                    </div>
                    <span class="tab-title" :style="{'color': currentData.iconColorType === 'custom' ? currentData.selectedFontColor: currentData.selectedColor}">{{ item.name }}</span>
                </div>
                <!-- 普通菜单未选中 -->
                <div class="tab-box normal-tab" v-else>
                    <div class="box-top">
                        <i v-if="item.isUseIcon" :class="['iconfont', item.unselectedIcon]"
                            :style="{'font-size': '24px', 'color': currentData.iconColorType === 'custom' ? currentData.unselectedIconColor : currentData.unselectedColor }"></i>
                        <img v-else :src="item.unselectedIcon" class="icon-img"> 
                    </div>
                    <span class="tab-title" :style="{'color': currentData.iconColorType === 'custom' ? currentData.unselectedFontColor: currentData.unselectedColor}">{{ item.name }}</span>
                </div>
            </li>
        </ul>
    </div>
</template>


<script lang="ts" src="./EffectTab.ts">
</script>

<style lang="scss" scoped>
.effect-box {
    width: 100%;
    height: 50px;
    background: #FFFFFF;

    ul {
        display: flex;

        li {
            list-style: none;
            flex: 1;
            cursor:pointer;

            .tab-box {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: space-around;

                .icon-img {
                    width: 24px;
                    height: 24px;
                }
            }

            .rudder-tab {
                .box-top {
                    border-radius: 50%;
                    width: 52px;
                    height: 52px;
                    background: #fff;
                    margin-top: -27px;
                }
                .center-img {
                    border-radius: 50%;
                    width: 52px;
                    height: 52px;
                }
            }

            .tab-title {
                font-weight: 500;
                font-size: 10px;
                color: #9F9F9F;
            }
        }
    }
}
</style>