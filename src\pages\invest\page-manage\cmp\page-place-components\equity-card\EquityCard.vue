<template>
    <div class="entitlement-card" :style="{
        padding:
            localProperty.styMarginTop +
            'px ' +
            localProperty.styMarginRight +
            'px ' +
            localProperty.styMarginBottom +
            'px ' +
            localProperty.styMarginLeft +
            'px',
    }" @click="activeTemplate">
        <div class="toolBar" v-if="activeIndex === index">
            <setting-toolbar :btns="toolBarBtns" @click="toolbarClick"></setting-toolbar>
        </div>
        <div :style="{ backgroundColor: localProperty.styContentBgColor }" class="entitlement-card-container">
            <div>
                <div v-loading='loadingEquityCardTemplateList' class="entitlement-card-container-title"
                    :style="{ color: localProperty.styContentTextColor }">{{ getEquityCardName() }}</div>
                <div class="entitlement-card-container-text" :style="{ color: localProperty.styContentTextColor }">
                    {{ localProperty.propOpenCardText }}
                </div>
            </div>

            <div>
                <el-button style="color: #fff;"
                    :style="{ backgroundColor: localProperty.styContentButtonColor }">立即开通</el-button>
            </div>

        </div>
    </div>

</template>

<script lang="ts" src="./EquityCard.ts"></script>

<style lang="scss" scoped>
.entitlement-card {
    width: 100%;
    background: #f9f9f9;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
    position: relative;
    margin-bottom: 15px;
    overflow: hidden;
    // height: 92px;

    .toolBar {
        position: absolute;
        top: 0;
        right: 1px;
        z-index: 999;
        cursor: pointer;
    }


    &-container {
        border-radius: 8px;
        height: 70px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px;

        &-title {
            padding-bottom: 12px;
            line-height: 20px;
            font-weight: 600;
            font-size: 14px;
        }

        &-text {
            font-weight: 400;
            font-size: 12px;
            line-height: 16px;
            opacity: 0.6;
        }
    }
}
</style>