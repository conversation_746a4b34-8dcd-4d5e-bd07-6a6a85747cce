<template>
  <Drawerx ref="drawer" :visible.sync="visible" :title="i18n('操作日志')" width="50%"
           contentStyle="background-color: white" class="member-opt-log-drawer">
    <el-row style="background-color: #E6E6E6;line-height: 40px">
      <el-col :span="1">&nbsp;</el-col>
      <el-col :span="7">{{ i18n('操作类型') }}</el-col>
      <el-col :span="8">{{ i18n('操作人') }}</el-col>
      <el-col :span="8">{{ i18n('操作时间') }}</el-col>
    </el-row>
    <div style="height: calc(100% - 120px);overflow-y: auto">
      <div v-for="row of logs">
        <el-row style="border-bottom: 1px solid #DEDEDE;line-height: 40px">
          <el-col :span="1" style="text-align: center">
            <div style="width: 100%;cursor: pointer" @click="expand(row)"
                 v-if="row.modifyField && row.modifyField.length > 0">
              <i class="el-icon-arrow-right" v-if="!row.expanded"/>
              <i class="el-icon-arrow-down" v-if="row.expanded"/>
            </div>
            <div v-else>&nbsp;</div>
          </el-col>
          <el-col :span="7">
            {{ row.operateTypeName | nullable }}
          </el-col>
          <el-col :span="8">
            {{ row.operator | nullable }}
          </el-col>
          <el-col :span="8">
            {{ row.created | yyyyMMddHHmmss }}
          </el-col>
        </el-row>
        <div v-if="row.expanded" style="width: 100%; background-color: #F6F6F6;border-bottom: 1px solid #DEDEDE;">
          <el-row style="line-height: 40px">
            <el-col :span="1">
              &nbsp;
            </el-col>
            <el-col :span="23">
              <div style="display: flex;width: 100%;padding: 10px 0">
                <div style="width: 150px;padding-top: 5px;">
                  {{ i18n('变更内容：') }}
                </div>
                <div style="width: calc(100% - 150px)">
                  <div style="width: 100%;display: flex;padding: 5px 0;border-bottom: 1px solid #DEDEDE"
                       v-for="field of row.modifyField">
                    <div style="width: 100px">
                      {{ field.fieldName }}
                    </div>
                    <div style="width: calc(100% - 100px)">
                      <span style="color: #888888">{{ i18n('原值：') }}</span>{{ field.originValue }}<br/>
                      <span style="color: #888888">{{ i18n('新值：') }}</span>{{ field.value }}
                    </div>
                  </div>
                </div>
              </div>
              <div style="display: flex;width: 100%;padding: 10px 0">
                <div style="width: 150px;">
                  {{ i18n('变更说明：') }}
                </div>
                <div style="width: calc(100% - 150px)">
                  {{ row.remark | nullable }}
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
    <br/>
    <el-pagination
        :current-page="page.currentPage"
        :page-size="page.size"
        :page-sizes="[10, 20, 30, 40]"
        :total="page.total"
        @current-change="onHandleCurrentChange"
        @size-change="onHandleSizeChange"
        background
        layout="total, prev, pager, next, sizes,  jumper">
    </el-pagination>
  </Drawerx>
</template>

<script lang="ts" src="./MemberOptLogDrawer.ts">
</script>

<style lang="scss">
.member-opt-log-drawer {
}
</style>
