import { Component, Prop, Provide, Vue, Watch } from "vue-property-decorator";
import GoodsSelectorDialog from 'cmp/selectordialogs/GoodsSelectorDialog.vue'
import ImportDialog from 'cmp/importdialogs/ImportDialog.vue'
import RSGoods from 'model/common/RSGoods'
import AmountToFixUtil from "util/AmountToFixUtil";
// import PickUpGoods from 'model/common/PickUpGoods'
// import IdName from "model/entity/IdName";
import I18nPage from 'common/I18nDecorator'

@Component({
	name: "SelectGoodsDialog",
	components: {
		GoodsSelectorDialog,
    ImportDialog
	}
})
@I18nPage({
  prefix: ['/储值/会员储值/储值充值活动/列表页面','/营销/券礼包活动/券礼包活动', '/公用/按钮',"/公用/券模板"],
})
export default class SelectGoodsDialog extends Vue {
  $refs: any;
	@Prop()
	title: string; // dialog标题
  // @Prop({
	// 	type: Boolean,
	// 	default: false,
	// })
	// dialogShow: boolean; // 控制模态框的展示


  dialogShow: boolean = false
  // 选中的数据
  selectData: RSGoods[] = []
  importDialogShow: boolean = false
  importUrl: string = 'v1/goods/importBarcodes'
  checkedAll: boolean = false
  checked: boolean[] = []
  deleteSelectData: any[] = []
  get templatePath() {
    if (location.href.indexOf('localhost') === -1) {
        return 'template_barcodes.xlsx'
    } else {
        return 'template_barcodes.xlsx'
    }
  }

  get getSelecRowStr() {
    return this.i18n('已选{0}行').replace(/\{0\}/g, String(this.checked.filter(x => x).length))
  }
  // 导入开启
  openUploadGood() {
    this.importDialogShow = true
    this.$refs.ImportDialogGoods.show()
  }
  // 导入关闭
  doImportDialogClose() {
    this.importDialogShow = false
  }
  // 导入成功
  doUploadSuccess(value: any) {
    // console.log('上传成功', value);
    if (value.response.code === 2000) {
      if (value.response.data && value.response.data.barcodes && value.response.data.barcodes.length) {
        value.response.data.barcodes.forEach((item: any) => {
          // 不存在，则加入
          if (!this.selectData.find((i: any) => i.barcode == item)) {
            let good: RSGoods = new RSGoods()
            good.barcode = item
            good.price = 0.01
            this.selectData.push(good)
          }
        })
      }
    } else {
      this.$message.error(value.response.msg)
    }
  }
  // 输入最多金额变化
  doPayWayByAmount(index: number) {
    let maxPrice: any = this.selectData[index].price
    maxPrice = parseFloat(maxPrice) || 0.01
    maxPrice = AmountToFixUtil.formatAmount(maxPrice, 999999.99, 0, 2);
    this.$set(this.selectData[index], 'price', maxPrice)
  }
  // 打开选择商品
  open(arr: RSGoods[]) {
    this.checked = []
    ;(arr || []).forEach((item: any) => {
      item.price = item.price || item.amount
      this.checked.push(false)
    })
    this.selectData = [...arr]
    // this.selectDataTmp = [...arr]
    this.dialogShow = true
  }
  // 弹框操作
  doBeforeClose(done: any) {
		this.$emit("dialogClose");
		done();
	}
  // 确认按钮
	doModalClose(type: string) {
		if (type === "confirm") {
			// 确认校验数据
      this.selectData.forEach((item: any, index: number) => {
        this.$refs.specialGoodsForm && this.$refs.specialGoodsForm.validateField(index + '')
      })
      let validateMessage = false
      for (let index = 0; index < this.selectData.length; index++) {
        let str = this.$refs['tr'+index] && this.$refs['tr'+index][0] && this.$refs['tr'+index][0].validateMessage ? this.$refs['tr'+index][0].validateMessage : ''
        if (str) {
          validateMessage = true
          break
        }       
      }
      if (validateMessage) {
        return
      }
      // 校验通过 返回数据
      this.$emit('submit', this.selectData);
		} else {
			// 取消，清除数据(可不清，重新打开时，数据会重置)
		}
    this.dialogShow = false
	}

  // 打开弹框
  doAdd() {
		this.$refs.selectGoodsScopeDialog.open(this.selectData);
	}
  // 接受选择的商品
  doSubmitGoods(arr: RSGoods[]) {
    console.log('选择返回', arr);
		if (arr && arr.length > 0) {
      let arrTmp: any = []
			arr.forEach((item: any) => {
        let obj = this.selectData.find((i: any) => i.barcode == item.barcode)
        if(obj) {
          arrTmp.push(obj)
        } else {
          item.price = 0.01
          arrTmp.push(item)
        }
      })
      this.selectData = [...arrTmp]
		} else {
      this.selectData = []
    }
	}
  // 单个删除商品
  deleteGood(index: number) {
    this.selectData.splice(index, 1)
  }
  // 校验规则
  trRule(rule: any, value: any, callback: any) {
    if(!this.selectData[rule.field]) {
      callback(this.i18n('此项必填！'))
    } else {
      // 0.01-99999.99
      if (parseFloat(this.selectData[rule.field].price as any) >= 0.01 && parseFloat(this.selectData[rule.field].price as any) <= 99999.99) {
        callback()
      } else {
        callback(this.i18n('输入值区间为：0.01-99999.99'))
      }
    }
  }
  // 全选变化
  changeAll(e: any) {
    console.log('全选', e);
    if (e) {
      this.checked = this.selectData.map(i => {
        return true
      })
    } else {
      this.checked = this.selectData.map(i => {
        return false
      })
    }
  }
  // 单选
  changeCheck(e: any) {
    console.log('单选', e, this.checked);
    if (this.checked.filter(x => x).length == this.selectData.length) {
      this.checkedAll = true
    } else {
      this.checkedAll = false
    }
    
  }
  // 批量删除
  deleteBatch() {
    let arr: any = []
    this.checked.forEach((item: any, index: number) => {
      if (item) {
        arr.push({...this.selectData[index], index: index})
      }
    })
    if (!arr.length) {
      this.$message.warning(this.i18n('请先勾选要删除的单据'))
    } else if (arr.length == this.selectData.length) {
      this.selectData = []
      this.checked = []
    } else {
      arr.forEach((item: any) => {
        let index = this.selectData.findIndex(i => i.barcode == item.barcode)
        if (index > -1) {
          this.selectData.splice(index, 1)
        }
      })
      this.checked = this.selectData.map(i => {
        return false
      })
    }
    this.checkedAll = false
  }
}