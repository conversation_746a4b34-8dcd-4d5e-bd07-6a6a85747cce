import ApiClient from 'http/ApiClient'
import Response from 'model/response/Response'
import AnalysisResult from 'model/analysis/AnalysisResult'
import AnalysisCountResult from 'model/analysis/AnalysisCountResult'
import AnalysisTranResult from 'model/analysis/AnalysisTranResult'
import AnalysisTranCountResult from 'model/analysis/AnalysisTranCountResult'

const qs = require('qs');
export default class AnalysisApi {

  static getAnalysisList(params: any): Promise<Response<AnalysisResult>> {

    return ApiClient.server().post(`crm-web/analysis/queryStoreMemberData.hd`, qs.stringify(params), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }).then((res) => {
      return res.data
    })
  }
  static getAnalysisCount(params: any): Promise<Response<AnalysisCountResult>> {

    return ApiClient.server().post(`crm-web/analysis/queryStoreMemberData.hd`, qs.stringify(params), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }).then((res) => {
      return res.data
    })
  }
  static getAnalysisAllCount(params: any): Promise<Response<AnalysisCountResult>> {

    return ApiClient.server().post(`crm-web/analysis/queryStoreMemberData.hd`, qs.stringify(params), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }).then((res) => {
      return res.data
    })
  }
  static getAnalysisTranList(params: any): Promise<Response<AnalysisTranResult>> {

    return ApiClient.server().post(`crm-web/analysis/queryStoreTranData.hd`, qs.stringify(params), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }).then((res) => {
      return res.data
    })
  }
  static getAnalysisTranCount(params: any): Promise<Response<AnalysisTranCountResult>> {

    return ApiClient.server().post(`crm-web/analysis/queryStoreTranData.hd`, qs.stringify(params), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }).then((res) => {
      return res.data
    })
  }
}
