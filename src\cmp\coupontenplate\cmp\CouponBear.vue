<template>
  <div class="coupon-bear">
    <div v-if="state === 'UNSTART' || state === 'PROCESSING'">
      <div v-if="costParties && costParties.length > 0">
        <div v-for="(item, index) in costParties" :key="index">
          <div v-if="item && item.party">{{item.party}}{{formatI18n('/营销/券礼包活动/核销第三方券', '承担用券金额')}}{{item.percent}}%</div>
        </div>
      </div>
      <div v-else>--</div>
    </div>
    <div v-else>
      <div>
        <el-radio-group v-model="typeCparties" @change="typeCpartiesChange">
          <el-radio label="unset" v-show="this.visibleUnset">{{ i18n('不记录') }}</el-radio>
          <el-radio label="PROPORTION" v-show="this.visibleProportion">{{ i18n('按比例') }}</el-radio>
          <el-radio label="AMOUNT" v-show="this.visibleAmount">
            {{ i18n('按金额') }}
            <el-popover placement="top-start" width="500" trigger="hover">
              <div>
                <div class="tip-content">
                  {{ i18n('如果设置总部承担部分券抵扣金额最多10元，门店承担最多5元，供应商承担剩余券抵扣金额。') }}
                  <br />
                  1. {{ i18n('该模板的券a核销时抵扣了15元，则由总部承担10元，门店承担5元，供应商承担0') }}
                  <br />
                  2. {{ i18n('该券模板的券b核销时抵扣了8元，则由总部承担8元') }}
                  <br />
                  3. {{ i18n('该券模板的券c核销时抵扣了19元，则由总部承担10元，门店承担5元，供应商承担4元') }}
                </div>
              </div>
              <i slot="reference" class="iconfont  ic-info icon-tip"></i>
            </el-popover>
          </el-radio>
        </el-radio-group>
      </div>
      <div v-if="typeCparties != 'unset'" class="bear-container">
        <div v-for="(item, index) in costParties" :key="index+'costParties'">
          <el-form label-width="0" :ref="'valiForm'+index" style="height:54px">
            <el-form-item style="display:inline-block;height: 56px;" :prop="'costparty'+index" :ref="'costparty'+index" :rules="[
                      {
                        validator: costpartyRule,
                        require: true,
                        trigger: ['change', 'blur']
                      }
                    ]">
              <el-select @change="doPartiesChange(index)" :key="index" @visible-change="doFilterPartys(index)" style="width: 200px" value-key="label"
                v-model="partySelectArray[index]">
                <el-option v-for="item in parties[index]" :key="item.id" :label="item.name" :value="item.id">{{item.name}}</el-option>
              </el-select>
            </el-form-item>
            <template v-if="typeCparties == 'PROPORTION'">
              &nbsp;&nbsp;
              {{formatI18n('/营销/券礼包活动/核销第三方券', '承担用券金额')}}
              &nbsp;&nbsp;
              <el-form-item style="display:inline-block;white-space:nowrap" :prop="'PROPORTIONVAL'+index" :ref="'PROPORTIONVAL'+index" :rules="[
                        {
                          validator: propormitionValRule,
                          require: true,
                          trigger: ['change', 'blur']
                        }
                      ]">
                <el-input @change="doThisAmount(index)" :placeholder="formatI18n('/公用/券模板/券承担方/输入框文案', '1至100')" style="width: 150px"
                  v-model="partyInputArray[index]">
                  <template slot="append">%</template>
                </el-input>
              </el-form-item>
            </template>
            <template v-if="typeCparties == 'AMOUNT'">
              <i18n k="/公用/券模板/承担券用券金额的{0}">
                <template slot="0">
                  <template v-if="index == 0">
                    <el-form-item style="display:inline-block;height: 56px;" :prop="'costType'+index" :ref="'costType'+index" :rules="[
                          {
                            validator: costTypeRule,
                            require: true,
                            trigger: ['change', 'blur']
                          }
                        ]">
                      <el-select v-model="costAll" style="width: 80px" @change="costAllChange">
                        <el-option :label='i18n("/公用/活动/状态/全部")' value="all"></el-option>
                        <el-option :label='i18n("部分")' value="part"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                  <template v-if="costAll == 'part' && (index != costParties.length - 1 || index == 0)">
                    &nbsp;&nbsp;
                    <span>{{ i18n('最多为') }}</span>
                    &nbsp;&nbsp;
                    <el-form-item style="display:inline-block;height: 56px;width: 120px;white-space:nowrap" :prop="'AMOUNT'+index"
                      :ref="'AMOUNT'+index" :rules="[
                          {
                            validator: amountValRule,
                            require: true,
                            trigger: ['change', 'blur']
                          }
                        ]">
                      <el-input @blur="doThisAmount(index)" :placeholder="formatI18n('/营销/券礼包活动/核销第三方券/请输入')" style="width: 120px"
                        v-model="partyInputArray[index]">
                        <template slot="append">{{i18n('元')}}</template>
                      </el-input>
                    </el-form-item>
                  </template>
                  <template v-else-if="costAll == 'part'">
                    &nbsp;&nbsp;
                    <span>{{ i18n('剩余金额') }}</span>
                    &nbsp;&nbsp;
                  </template>
                </template>
              </i18n>
            </template>
            <i class="el-icon-minus" v-if="showDelBtn(index)" @click="doDeleteCostParties(index)"></i>
            <i class="el-icon-plus" @click="doAddCostParties()"
              v-if="(typeCparties == 'PROPORTION' || (typeCparties == 'AMOUNT' && costAll == 'part')) && index == costParties.length - 1"></i>
          </el-form>
        </div>
        <div v-if="typeCparties == 'AMOUNT'">
          <template v-if="specialGoods.length">
            <i18n k="/公用/券模板/已设置{0}个特殊商品">
              <span style="font-weight: 600;color: #FFAA00;" slot="0">{{specialGoods.length}}</span>
            </i18n>
          </template>
          <el-button type="text" @click="setSpecialGoods">
            <span v-if="specialGoods.length">{{ i18n('修改设置') }}</span>
            <span v-else>{{ i18n('设置特殊商品') }}</span>
          </el-button>
        </div>
      </div>
    </div>
    <!-- 设置特殊商品 -->
    <CBearSpecialGoods :data="specialGoods" ref="CBearSpecialGoods" :title="i18n('设置')" @submit="specialGoodsSubmit"></CBearSpecialGoods>
    <!-- 查看特殊商品 -->
    <!-- <SpecialGoodsDialog ref="SpecialGoodsDialog" :data="specialGoods"></SpecialGoodsDialog> -->
  </div>
</template>

<script lang="ts" src="./CouponBear.ts">
</script>

<style lang="scss" scoped>
.coupon-bear {
  .bear-container {
    padding: 8px;
    background: #f7f9fc;
    border-radius: 4px;
  }
  .el-icon-plus,
  .el-icon-minus {
    color: #007eff;
    width: 21px;
    height: 21px;
    line-height: 19px;
    text-align: center;
    border-radius: 50%;
    border: 1px solid #d7dfeb;
    font-size: 12px;
    font-weight: 600;
    margin-left: 8px;
    background-color: #ffffff;
    &:hover {
      cursor: pointer;
      border: 1px solid #007eff;
    }
  }
}

::v-deep .el-dialog {
  min-width: 1050px;
}
</style>