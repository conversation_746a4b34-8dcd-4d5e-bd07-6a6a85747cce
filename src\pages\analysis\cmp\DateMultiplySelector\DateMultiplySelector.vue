<!--
 * @Author: 黎钰龙
 * @Date: 2024-02-29 14:10:44
 * @LastEditTime: 2024-11-06 10:46:47
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\analysis\cmp\DateMultiplySelector\DateMultiplySelector.vue
 * 记得注释
-->
<template>
  <div class="date-selector-container">
    <FormItem :label="label">
      <div class="date-block">
        <el-select v-model="dateType" @change="doChange" style="margin-right:2%;width:40%">
          <el-option :label="i18n('日')" value="day"></el-option>
          <el-option :label="i18n('周')" value="week"></el-option>
          <el-option :label="i18n('月')" value="month"></el-option>
        </el-select>
        <template v-if="dateType === 'day'">
          <el-date-picker v-model="dayRange" @change="doChange" type="daterange" range-separator="-" start-placeholder="开始日期"
            value-format="yyyy-MM-dd" end-placeholder="结束日期" style="width:58%" key="day" :picker-options="dayOptions">
          </el-date-picker>
        </template>
        <template v-else-if="dateType === 'week'">
          <el-date-picker v-model="weekRangeBegin" @change="doChange" type="week" placeholder="选择周" :format="i18n('yyyy第WW周')" style="width:35%"
            key="weekBegin" :picker-options="weekOptions">
          </el-date-picker>
          <span style="margin: 0 6px">{{i18n('/会员/洞察/公共/操作符/至')}}</span>
          <el-date-picker v-model="weekRangeEnd" @change="doChange" type="week" placeholder="选择周" :format="i18n('yyyy第WW周')" style="width:35%"
            key="weekEnd" :picker-options="weekOptions">
          </el-date-picker>
        </template>
        <template v-else-if="dateType === 'month'">
          <el-date-picker v-model="monthRangeBegin" @change="doChange" type="month" placeholder="选择月" key="monthBegin" style="width:35%"
            :picker-options="monthOptions">
          </el-date-picker>
          <span style="margin: 0 6px">{{i18n('/会员/洞察/公共/操作符/至')}}</span>
          <el-date-picker v-model="monthRangeEnd" @change="doChange" type="month" placeholder="选择月" key="monthEnd" style="width:35%"
            :picker-options="monthOptions">
          </el-date-picker>
        </template>
      </div>
    </FormItem>
  </div>
</template>

<script lang="ts" src="./DateMultiplySelector.ts">
</script>

<style lang="scss" scoped>
.date-selector-container {
  width: 100%;
  .date-block {
    display: flex;
    align-items: center;

    ::v-deep .el-date-editor .el-range__icon {
      display: flex;
      align-items: center;
    }
    ::v-deep .el-date-editor .el-range-separator {
      display: flex;
      align-items: center;
    }
  }
}
</style>