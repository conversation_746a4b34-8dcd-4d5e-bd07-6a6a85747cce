<template>
  <div class="consume-flow">
    <div no-i18n>
      <el-radio-group v-model="date">
        <el-radio-button :label="i18n('今天')"></el-radio-button>
        <el-radio-button :label="i18n('昨天')"></el-radio-button>
        <el-radio-button :label="i18n('近7天')"></el-radio-button>
        <el-radio-button v-if="!daqiaoshihuaDingkai" :label="i18n('近30天')"></el-radio-button>
        <el-radio-button v-if="!daqiaoshihuaDingkai" :label="i18n('近90天')"></el-radio-button>
        <el-radio-button v-if="!daqiaoshihuaDingkai" :label="i18n('自定义')"></el-radio-button>
        <el-date-picker
        v-if="!daqiaoshihuaDingkai"
          style="position: relative;top: 5px;left: 10px"
          @change="doDateChange"
          :end-placeholder="i18n('结束日期')"
          format="yyyy-MM-dd"
          :clearable="false"
          range-separator="-"
          size="small"
          :start-placeholder="i18n('起始日期')"
          type="daterange"
          v-model="customDate"
          value-format="yyyy-MM-dd"
        ></el-date-picker>
      </el-radio-group>
    </div>
    <div>
      <ListWrapper>
        <template slot="query">
          <QueryCondition @reset="onReset" @search="onSearch" @toggle="doToggle">
            <el-row>
              <el-col :span="8">
                <form-item label="会员信息">
                  <el-input placeholder="手机号/会员号/实体卡号等于" v-model="query.memberCode"></el-input>
                </form-item>
              </el-col>
              <el-col :span="8">
                <form-item label="储值消费">
                  <div style="display: flex">
                    <div style="flex: 1">
                      <el-input v-model="query.balanceChangeBegin"></el-input>
                    </div>
                    <div style="line-height: 26px">&nbsp;-&nbsp;</div>
                    <div style="flex: 1">
                      <el-input v-model="query.balanceChangeEnd"></el-input>
                    </div>
                  </div>
                </form-item>
              </el-col>
              <el-col :span="8">
                <form-item label="交易门店">
                  <SelectStores v-model="query.storeIdEquals" :isOnlyId="true" :hideAll="false" width="174px"
                    :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
                  </SelectStores>
                </form-item>
              </el-col>
            </el-row>
            <template slot="opened">
              <el-row>
                <el-col :span="8">
                  <form-item label="会员信息">
                    <el-input placeholder="手机号/会员号/实体卡号等于" v-model="query.memberCode"></el-input>
                  </form-item>
                </el-col>
                <el-col :span="8">
                  <form-item label="储值消费">
                    <div style="display: flex">
                      <div style="flex: 1">
                        <el-input v-model="query.balanceChangeBegin"></el-input>
                      </div>
                      <div style="line-height: 26px">&nbsp;-&nbsp;</div>
                      <div style="flex: 1">
                        <el-input v-model="query.balanceChangeEnd"></el-input>
                      </div>
                    </div>
                  </form-item>
                </el-col>
                <el-col :span="8">
                  <form-item :label="i18n('交易门店')" no-i18n>
                    <SelectStores v-model="query.storeIdEquals" :isOnlyId="true" :hideAll="false" width="174px"
                      :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
                    </SelectStores>
                  </form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <form-item label="交易号">
                    <el-input :placeholder="i18n('/公用/查询条件/提示/等于')" v-model="query.transNoEquals"></el-input>
                  </form-item>
                </el-col>
                <el-col :span="8">
                  <form-item :label="i18n('发生渠道')">
                    <el-select
                      v-model="channelEquals"
                      :placeholder="formatI18n('/资料/渠道/请选择')"
                      style="width: 200px"
                      clearable
                      filterable
                    >
                      <el-option
                        v-for="(item,index) in channelTypes"
                        :key="index"
                        :value="getKey(item.channel)"
                        :label="item.name"
                      >
                        <!-- <span style="float: left">{{ item.label }}</span> -->
                      </el-option>
                    </el-select>
                  </form-item>
                </el-col>
                <el-col :span="8" v-if="isMoreMarketing">
                  <form-item :label="formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水/发生区域')">
                    <el-select placeholder="不限" style="width: 174px" v-model="query.zoneIdEquals">
                      <el-option
                        :key="item.zone.id"
                        :label="'['+item.zone.id+']'+item.zone.name"
                        :value="item.zone.id"
                        v-for="item in areaData"
                      >[{{item.zone.id}}]{{item.zone.name}}</el-option>
                    </el-select>
                  </form-item>
                </el-col>
              </el-row>
            </template>
          </QueryCondition>
        </template>
        <template slot="btn" v-if="isShowSum">
          <div style="display:flex;align-items:center">
            <i class="iconfont ic-info" style="font-size: 18px;color: #20A0FF"></i>
            &nbsp;&nbsp;{{ date }}
            <i18n k="/储值/会员储值/会员储值报表/消费流水/储值支付{0}元，实充减少{1}元、返现减少{2}元">
              <template slot="0">{{ sum.total | fmt }}</template>
              <template slot="1">{{ sum.amount | fmt }}</template>
              <template slot="2">{{ sum.giftAmount | fmt }}</template>
            </i18n>&nbsp;&nbsp;
          </div>
        </template>
        <template slot="list">
          <el-table :data="queryData" border v-loading="loading">
            <el-table-column fixed label="消费时间" prop="occurredTime" width="140">
              <template slot-scope="scope">
                <span no-i18n>{{ scope.row.occurredTime | dateFormate3 }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed label="会员信息" prop="mbrcode" width="140">
              <template slot-scope="scope">
                <span no-i18n>
                  {{
                  scope.row.mobile ? scope.row.mobile : (scope.row.crmCode ? scope.row.crmCode : (scope.row.hdCardCardNum ? scope.row.hdCardCardNum : '--'))
                  }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="储值消费（元）" prop="totalAmount" width="140">
              <template slot-scope="scope">
                <span no-i18n>{{ scope.row.totalAmount | fmt }}</span>
              </template>
            </el-table-column>
            <el-table-column label="实充减少（元）" prop="mbrcode" width="250">
              <template slot-scope="scope">
                <span no-i18n>{{ scope.row.amount | fmt }}</span>
              </template>
            </el-table-column>
            <el-table-column label="返现减少（元）" prop="mbrcode" width="250">
              <template slot-scope="scope">
                <span no-i18n>{{ scope.row.giftAmount | fmt }}</span>
              </template>
            </el-table-column>
            <el-table-column label="交易号" prop="transNo" width="160">
              <template slot-scope="scope">
                <span no-i18n>{{ scope.row.transNo }}</span>
              </template>
            </el-table-column>
            <el-table-column label="交易门店" prop="store">
              <template slot-scope="scope">
                <span no-i18n>{{ scope.row.store }}</span>
              </template>
            </el-table-column>
            <el-table-column v-if="isMoreMarketing" :label="formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水/发生区域')">
            <template slot-scope="scope">
              <div v-if="scope.row.zone!==null && scope.row.zone!==''">
                <el-tooltip class="item" effect="light"  placement="right-end">
                  <div>{{scope.row.zone}}</div>
                  <div slot="content">
                    {{scope.row.zone}}
                  </div>
                </el-tooltip>
              </div>
              <div v-else>-</div>
            </template>
            </el-table-column>
            <el-table-column label="发生渠道" prop="channelName">
              <template slot-scope="scope">
                <span no-i18n>{{ scope.row.channelName | nullable }}</span>
              </template>
            </el-table-column>
          </el-table>
        </template>
        <template slot="page">
          <el-pagination
            no-i18n
            :current-page="page.currentPage"
            :page-size="page.size"
            :page-sizes="[10, 20, 30, 40]"
            :total="page.total"
            @current-change="onHandleCurrentChange"
            @size-change="onHandleSizeChange"
            background
            :layout="getPageLayout('total, prev, pager, next, sizes, jumper',page.probeEnabled)"
          ></el-pagination>
        </template>
      </ListWrapper>
    </div>
    <DownloadCenterDialog :dialogvisiable="dialogvisiable" @dialogClose="doDialogClose"></DownloadCenterDialog>
  </div>
</template>

<script lang="ts" src="./ConsumeFlow.ts">
</script>

<style lang="scss">
.consume-flow {
  .qf-form-item .qf-form-label {
  }

  .qf-form-item .qf-form-content {
    width: 200px !important;
  }
}
</style>