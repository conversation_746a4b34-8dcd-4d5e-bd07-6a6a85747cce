import Vue from "vue"
import DateUtil from "../util/DateUtil.js"
import NumberUtil from "../util/NumberUtil.js"
import ThumbUtil from "../util/ThumbUtil.js"
import ThumbFormatNumber from "../util/ThumbFormatNumber.js"
import ConstantMgr from "../mgr/ConstantMgr"
import AutoTagFilter from './cmpFilter/AutoTagFilter';

/**
 * 过滤器定制
 */
export default class Filter {
  static init () {
    AutoTagFilter.init()
    Vue.filter("amount", function (value) {
      if (value === null || value === undefined) {
        return "-"
      }
      return Number.parseFloat(value).toFixed(2)
    })
    Vue.filter("fmt", function (value, format) {
      if (!value && value !== 0) return "-"
      if (value instanceof Date) {
        return DateUtil.format(value, format)
      } else if (typeof value === "number") {
        return NumberUtil.format(value, format)
      }
      return value
    })
    Vue.filter("idName", function (value) {
      if (!value) {
        return "-"
      }
      let id = value.id ? value.id : "-"
      let name = value.name ? value.name : "-"
      return `[${id}] ${name}`
    })
    Vue.filter("nullable", function (value) {
      if (value === null || value === undefined || value === "") {
        return "--"
      }
      return value
    })
    Vue.filter("dateFormate", function (value) {
      if (value) {
        return DateUtil.format(value, "yyyy/MM/dd")
      } else {
        return "-"
      }
    })
    Vue.filter("dateFormate2", function (value) {
      if (value) {
        return DateUtil.format(value, "yyyy-MM-dd")
      } else {
        return "-"
      }
    })
    Vue.filter("dateFormate3", function (value) {
      if (value) {
        return DateUtil.format(value, "yyyy-MM-dd HH:mm:ss")
      } else {
        return "-"
      }
    })
    Vue.filter("yyyyMMddHHmmss", function (value) {
      if (value) {
        return DateUtil.format(value, "yyyy-MM-dd HH:mm:ss")
      } else {
        return "-"
      }
    })
    Vue.filter("yyyyMMdd", function (value) {
      if (value) {
        return DateUtil.format(value, "yyyy-MM-dd")
      } else {
        return "-"
      }
    })
    Vue.filter("HHmm", function (value) {
      if (value) {
        return DateUtil.format(value, "HH:mm")
      } else {
        return "-"
      }
    })
    Vue.filter("strFormat", function (value, date) {
      if (value || value === 0) {
        return value
      } else {
        return "--"
      }
    })
    Vue.filter("strFormatNoEmol", function (value, date) {
      if (value) {
        let str = value.substring(0, value.length - 1)
        return str
      } else {
        return "-"
      }
    })
    Vue.filter("fmtThumb", (value) => {
      return ThumbUtil(value)
    })
    Vue.filter("fmtThumbNumber", (value) => {
      return ThumbFormatNumber(value)
    })

    Vue.filter("couponTypeFmt", function (value, id) {
      if (value) {
        if (value === "MEMBER_REGISTER_COUPON") {
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "行为发券")
        }
        if (value === "WEIXIN_ACTIVATION_COUPON") {
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "行为发券")
        }
        if (value === "GAIN_COUPON") {
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "交易发券")
        }
        if (value === "GOODS_GAIN_COUPON") {
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "交易发券")
        }
        if (value === "GOODS_QTY_GAIN_COUPON") {
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "交易发券")
        }
        if (value === "MINI_PROGRAM_COUPON") {
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "小程序领券")
        }
        if (value === "WEIXIN_COUPON") {
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/微信扫码领券", "微信扫码领券")
        }
        if (value === "MANUAL_COUPON") {
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "群发券")
        }
        if (value === "THIRD_ISSUE_COUPON") {
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "第三方发券")
        }
        if (value === "THIRD_CODE_ISSUE_COUPON") {
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "核销第三方券")
        }
        if (value === "MEMBER_REGISTER_GIFT") {
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "注册发大礼包")
        }
        if (value === "WEIXIN_ACTIVATION_GIFT") {
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "微信激活发大礼包")
        }
        if (value === "MEMBER_INVITE_REGISTER_GIFT") {
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "邀请有礼")
        }
        if (value === "GAIN_GIFT") {
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "全场满额发大礼包")
        }
        if (value === "GOODS_GAIN_GIFT") {
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "商品满额发大礼包")
        }
        if (value === "EXPORT_COUPON_CODE") {
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "导出券码发券")
        }
        if (value === "USER_GROUP_GIFT") {
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "客群发大礼包")
        }
        if (value === "PRECISION_ISSUE_COUPON") {
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "开屏券包")
        }
        if (value === "GAIN_POINTS_GOODS") {
          return new ConstantMgr.MenusFuc().format("/公用/过滤器", "不积分商品")
        }
        if (value === "GOODS_GAIN_ADDITIONAL_POINTS_BY_QTY") {
          return new ConstantMgr.MenusFuc().format("/公用/过滤器", "单品满数量加送积分")
        }
        if (value === "GOODS_GROUP_GAIN_ADDITIONAL_POINTS_BY_QTY") {
          return new ConstantMgr.MenusFuc().format("/公用/过滤器", "商品组合满数量加送积分")
        }
        if (value === "GOODS_GAIN_ADDITIONAL_POINTS") {
          return new ConstantMgr.MenusFuc().format("/公用/过滤器", "商品满额加送积分")
        }
        if (value === "GOODS_GAIN_POINTS_SPEED") {
          return new ConstantMgr.MenusFuc().format("/公用/过滤器", "商品满额积分加倍")
        }
        if (value === "USE_COUPON_ISSUE_GIFT") {
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "用券发券")
        }
        if (value === "ALI_APPLET_ISSUE_COUPON") {
          return new ConstantMgr.MenusFuc().format("/公用/过滤器", "小程序领支付宝券")
        }
        if (value === "ALI_APPLET_CONSUME_GIFT") {
          return new ConstantMgr.MenusFuc().format("/公用/过滤器", "支付有礼")
        }
        if (value === "ALI_PLT_GROUP_COUPON") {
          return new ConstantMgr.MenusFuc().format("/公用/过滤器", "平台团购券")
        }
        if (value === "WEI_XIN_APPLET_ISSUE_COUPON") {
          return new ConstantMgr.MenusFuc().format("/券/延期申请", "小程序领微信券")
        }
        if (value === "DirectionalIssueCouponActivityRule") {
          return new ConstantMgr.MenusFuc().format("/公用/过滤器", "定向发券")
        }
        if (value === "COLLECT_POINTS_ACTIVITY") {
          return new ConstantMgr.MenusFuc().format("/营销/集点活动", "集点活动")
        }
        if (value === "UPGRADE_GIFT") {
          return new ConstantMgr.MenusFuc().format("/营销/升级有礼", "升级有礼")
        }
        if (value === 'IMPROVE_PROFILES_GIFT') {
          return new ConstantMgr.MenusFuc().format("/公用/菜单", "完善资料有礼")
        }
        if (value === 'WEIXIN_BRAND_ACTIVITY') {
          return new ConstantMgr.MenusFuc().format("/公用/过滤器", "开屏推广")
        }
        if (value === 'POINTS_EXCHANGE_COUPON') {
          return new ConstantMgr.MenusFuc().format("/公用/过滤器", "积分兑换券")
        }
        if (value === 'POINTS_EXCHANGE_GOODS') {
          return new ConstantMgr.MenusFuc().format("/营销/积分活动", "积分兑换商品")
        }
        if (value === 'CONSUME_CALENDAR_GIFT') {
          return new ConstantMgr.MenusFuc().format("/公用/券模板/微盟适用商品", "限量抢购")
        }
        if (value === 'CUMULATIVE_CONSUME_ACTIVITY') {
          return new ConstantMgr.MenusFuc().format("/营销/累计消费有礼", "累计消费有礼活动")
        }
        if (value === 'BIG_WHEEL_ACTIVITY') {
          return new ConstantMgr.MenusFuc().format("/营销/大转盘活动", "大转盘")
        }
        if (value === 'GROUP_BOOKING_ACTIVITY') {
          return new ConstantMgr.MenusFuc().format("/营销/拼团抽奖活动", "抽奖团")
        }
        if (value === 'MEMBER_BALANCE_REDUCTION') {
          return new ConstantMgr.MenusFuc().format("/储值/会员储值/储值支付活动/列表页面", "储值支付立减")
        }
        if (value === 'MEMBER_BALANCE_DISCOUNT') {
          return new ConstantMgr.MenusFuc().format("/储值/会员储值/储值支付活动/列表页面", "储值支付折扣")
        }
        if (value === 'DEPOSIT_GIFT') {
          return new ConstantMgr.MenusFuc().format("/公用/菜单", "充值有礼")
        }
        if (value === 'POINTS_CHARGE_V2') {
          return new ConstantMgr.MenusFuc().format("/公用/菜单", "积分抵现活动")
        }
        if (value === 'GROUP_TARGETED_GIFTS') {
          return new ConstantMgr.MenusFuc().format("/公用/菜单", "客群定向发礼")
        }
        if (value === 'WEI_XIN_PAY_GIFTS_ACTIVITY') {
          return new ConstantMgr.MenusFuc().format("/营销/微信营销/微信支付有礼", "微信支付有礼")
        }
        if (value === "LUCKY_DRAW_ACTIVITY") {
          return new ConstantMgr.MenusFuc().format("/营销/抽锦鲤活动", "抽锦鲤")
        }
        if (value === 'WEI_XIN_PAY_MARKETING_ACTIVITY') {
          return new ConstantMgr.MenusFuc().format("/微信营销/微信支付营销", "微信支付营销")
        }
        return "-"
      } else {
        return "-"
      }
    })
    Vue.filter("giftTypeFmt", function (value, id) {
      if (value) {
        if (value === "MEMBER_REGISTER_GIFT") {
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "注册发大礼包")
        }
        if (value === "WEIXIN_ACTIVATION_GIFT") {
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "微信激活发大礼包")
        }
        if (value === "MEMBER_INVITE_REGISTER_GIFT") {
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "邀请有礼")
        }
        if (value === "GAIN_GIFT") {
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "全场满额发大礼包")
        }
        if (value === "GOODS_GAIN_GIFT") {
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "商品满额发大礼包")
        }
        return "-"
      } else {
        return "-"
      }
    })
    Vue.filter("storeValueFmt", function (value, date) {
      // INITAIL——未审核；UNSTART——未开始；PROCESSING——进行中；STOPED——已结束
      if (value) {
        if (value === "INITAIL") {
          return new ConstantMgr.MenusFuc().format("/公用/过滤器", "未审核")
        }
        if (value === "UNSTART") {
          return new ConstantMgr.MenusFuc().format("/公用/过滤器", "未开始")
        }
        if (value === "PROCESSING") {
          return new ConstantMgr.MenusFuc().format("/公用/过滤器", "进行中")
        }
        if (value === "STOPED") {
          return new ConstantMgr.MenusFuc().format("/公用/过滤器", "已结束")
        }
        if (value === "SUSPEND") {
          return new ConstantMgr.MenusFuc().format("/公用/过滤器", "暂停中")
        }
        if (value === "AUDITING") {
          return new ConstantMgr.MenusFuc().format("/营销/营销申请", "审核中")
        }
        if (value === "REJECTED") {
          return new ConstantMgr.MenusFuc().format("/营销/营销申请", "已驳回")
        }
        if (value === "AUDITED") {
          return new ConstantMgr.MenusFuc().format("/公用/券核销", "已审核")
        }
        if (value === "PLATFORM_AUDIT_FAIL") {
          return new ConstantMgr.MenusFuc().format("/营销/视频号发券", "平台审核失败")
        }
        return ""
      } else {
        return ""
      }
    })
    Vue.filter("weekFmt", function (value, date) {
      if (value && value.length > 0) {
        let arr = value
        value.forEach((item, index) => {
          if (item === 1) {
            arr[index] = "一"
          }
          if (item === 2) {
            arr[index] = "二"
          }
          if (item === 3) {
            arr[index] = "三"
          }
          if (item === 4) {
            arr[index] = "四"
          }
          if (item === 5) {
            arr[index] = "五"
          }
          if (item === 6) {
            arr[index] = "六"
          }
          if (item === 7) {
            arr[index] = "日"
          }
        })
        return arr
      } else {
        return []
      }
    })
    Vue.filter("weekFmt2", function (value) {
      if (value) {
        if (value === 1) {
          return "一"
        }
        if (value === 2) {
          return "二"
        }
        if (value === 3) {
          return "三"
        }
        if (value === 4) {
          return "四"
        }
        if (value === 5) {
          return "五"
        }
        if (value === 6) {
          return "六"
        }
        if (value === 7) {
          return "日"
        }
      } else {
        return ""
      }
    })
    Vue.filter("lineCouponType", function (value, date) {
      // INITAIL——未审核；UNSTART——未开始；PROCESSING——进行中；STOPED——已结束
      if (value) {
        if (value === "all_cash" || value === "goods_cash") {
          return new ConstantMgr.MenusFuc().format("/公用/券模板", "现金券")
        }
        // if (value === "goods_cash") {
        // 	return new ConstantMgr.MenusFuc().format("/公用/券模板", "商品现金券");
        // }
        if (value === "all_discount" || value === "goods_discount" || value === "rfm_type") {
          return new ConstantMgr.MenusFuc().format("/公用/券模板", "折扣券")
        }
        // if (value === "goods_discount") {
        // 	return new ConstantMgr.MenusFuc().format("/公用/券模板", "单品折扣券");
        // }
        // if (value === "rfm_type") {
        // 	return new ConstantMgr.MenusFuc().format("/公用/券模板", "商品折扣券");
        // }
        if (value === "goods") {
          return new ConstantMgr.MenusFuc().format("/公用/券模板", "提货券")
        }
        if (value === "freight") {
          return new ConstantMgr.MenusFuc().format("/公用/券模板", "运费券")
        }
        if (value === "random_cash") {
          return new ConstantMgr.MenusFuc().format("/公用/券模板", "随机金额券")
        }
        if (value === "exchange_goods") {
          return new ConstantMgr.MenusFuc().format("/公用/券模板", "兑换券")
        }
        if (value === "points") {
          return new ConstantMgr.MenusFuc().format("/公用/券模板", "积分券")
        }
        if (value === "special_price") {
          return new ConstantMgr.MenusFuc().format("/公用/券模板", "特价券")
        }
        if (value === "equity") {
          return new ConstantMgr.MenusFuc().format("/公用/券模板", "权益券")
        }
        return ""
      } else {
        return ""
      }
    })
    Vue.filter("storeValueTypeFmt", function (value, date) {
      // INITAIL——未审核；UNSTART——未开始；PROCESSING——进行中；STOPED——已结束
      if (value) {
        if (value === "store") {
          return new ConstantMgr.MenusFuc().format("/公用/券模板", "门店")
        }
        if (value === "weixin") {
          return new ConstantMgr.MenusFuc().format("/会员/会员资料", "微信")
        }
        if (value === "alipay") {
          return new ConstantMgr.MenusFuc().format("/会员/会员资料", "支付宝")
        }
        if (value === "third") {
          // formatI18n('/会员/会员资料', '第三方')
          return new ConstantMgr.MenusFuc().format("/会员/会员资料", "第三方")
        }
        if (value === "weixinApp") {
          // formatI18n('/会员/会员资料', '第三方')
          return new ConstantMgr.MenusFuc().format("/会员/会员资料", "小程序")
        }
        return ""
      } else {
        return ""
      }
    })
    Vue.filter("memberState", function (value) {
      if (value) {
        let str = ""
        switch (value) {
          case "using":
            str = "使用中"
            break
          case "blocked":
            str = "已冻结"
            break
          case "unactivated":
            str = "未激活"
            break
          case "canceled":
            str = "已注销"
            break
          default:
            break
        }
        return str
      } else {
        return "-"
      }
    })
    Vue.filter("scoreActiveFilter", function (value) {
      if (value) {
        let menusFunc = new ConstantMgr.MenusFuc()
        let str = ""
        switch (value) {
          case "POINTS_EXCHANGE_GOODS":
            str = menusFunc.format("/营销/积分活动", "积分兑换商品")
            break
          case "POINTS_EXCHANGE_COUPON":
            str = menusFunc.format("/公用/过滤器", "积分兑换券")
            break
          case "POINTS_CHARGE_V2":
            str = menusFunc.format("/公用/过滤器", "积分抵现")
            break
          case "GOODS_GAIN_ADDITIONAL_POINTS_BY_QTY":
            str = menusFunc.format("/公用/过滤器", "单品满数量加送积分")
            break
          case "GOODS_GROUP_GAIN_ADDITIONAL_POINTS_BY_QTY":
            str = menusFunc.format("/公用/过滤器", "商品组合满数量加送积分")
            break
          case "GOODS_GAIN_POINTS_SPEED":
            str = menusFunc.format("/公用/过滤器", "商品满额积分加倍")
            break
          case "GOODS_GAIN_ADDITIONAL_POINTS":
            str = menusFunc.format("/公用/过滤器", "商品满额加送积分")
            break
          case "GAIN_POINTS_SPEED":
            str = menusFunc.format("/公用/过滤器", "全场积分加倍")
            break
          case "GAIN_POINTS_GOODS":
            str = menusFunc.format("/公用/过滤器", "不积分商品")
            break
          default:
            break
        }
        return str
      } else {
        return "-"
      }
    })
    Vue.filter("customMbrState", function (value) {
      if (value) {
        let str = ""
        switch (value) {
          case "Using":
            str = "使用中"
            break
          case "Blocked":
            str = "已冻结"
            break
          case "Unactivated":
            str = "未激活"
            break
          case "Canceled":
            str = "已注销"
            break
          default:
            break
        }
        return str
      } else {
        return "-"
      }
    })
    Vue.filter("downloadModule", function (value, type) {
      switch (value) {
        case "pointsAdjustBill":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "积分调整单导入")
        case "score_adjust_bill_upload":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "积分调整单导入")
        case "cardAdjustBill":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "预付卡调整单")
        case "adjustBill":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "储值调整单导入")
        case "memberCard":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "会员和卡关联导入")
        case "memberTag":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "会员标签导入")
        case "paidBenefitCardMemberImport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "付费会员卡导入")
        case "manualTagMember":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "会员资料导出（按标签）")
        case "tagMember":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "会员资料导出（按标签）")
        case "member-dqsh":
          if (type === "download") {
            return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "会员资料导出")
          }
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "会员资料导入")
        case "member":
          if (type === "download") {
            return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "会员资料导出")
          }
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "会员资料导入")
        case "memberTradeReport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "门店会员交易概况导出")
        case "memberIncreaseReport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "门店会员增长概况导出")
        case "store":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "门店导入")
        case "thirdIssueCouponByCode":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "核销第三方券导入")
        case "couponList":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "券导出")
        case "couponIssueHstReport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "发券流水导出")
        case "couponUseHstReport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "用券流水导出")
        case "couponIssueDailyReport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "发券日报导出")
        case "couponUseDailyReport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "用券日报导出")
        case "couponIssueGoodsReport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "发券商品明细导出")
        case "couponUseGoodsReport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "用券商品明细导出")
        case "pointsList":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "积分查询导出")
        case "pointsHstList":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "积分流水导出")
        case "pointsUseHst":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "积分抵现流水导出")
        case "pointsAggReport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "门店积分统计导出")
        case "zone":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "区域导入")
        case "pointsExchangeHst":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "积分兑换流水导出")
        case "aliMbrActivateQRCode":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "门店支付宝领卡二维码")
        case "exportCouponActivityCodes":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "导出券码发券活动券码导出")
        case "exchangeableCardSalesHst":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "储值卡售卡流水导出")
        case "exchangeableCardRefundCardHst":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "储值卡退卡流水导出")
        case "exchangeableCardConsumeHst":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "储值卡消费流水导出")
        case "exchangeableCardRefundHst":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "储值卡退款流水导出")
        case "onlineGiftCardSalesHst":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "电子礼品卡售卡流水导出")
        case "onlineGiftCardRefundCardHst":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "电子礼品卡退卡流水导出")
        case "onlineGiftCardConsumeHst":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "电子礼品卡消费流水导出")
        case "onlineGiftCardRefundHst":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "电子礼品卡退款流水导出")
        case "offlineGiftCardSalesHst":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "实体礼品卡售卡流水导出")
        case "offlineGiftCardConsumeHst":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "实体礼品卡消费流水导出")
        case "offlineGiftCardRefundHst":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "实体礼品卡退款流水导出")
        case "balanceAccountList":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "会员储值查询导出")
        case "mbrStoreValueRechargeHst":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "会员储值充值流水导出")
        case "mbrStoreValueRechargeRefundHst":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "会员储值充值退款流水导出")
        case "mbrStoreValueConsumeHst":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "会员储值消费流水导出")
        case "mbrStoreValueConsumeRefundHst":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "会员储值消费退款流水导出")
        case "adjustMbrStoreHst":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "调整会员储值流水导出")
        case "rechargeHstByPayment":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "充值流水-按支付方式导出")
        case "rechargeRefundHstByPayment":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "充值退款流水-按支付方式导出")
        case "changeAllMemberOwnStore":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "批量修改归属门店")
        case "changeMemberOwnStore":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "批量修改归属门店")
        case "imprestCardSalesHst":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "充值卡售卡流水导出")
        case "imprestCardRefundCardHst":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "充值卡退卡流水导出")
        case "imprestCardConsumeHst":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "充值卡余额转出流水导出")
        case "orgBalanceConfig":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "门店储值管理限额导入")
        case "onlineGiftCardDailyHst":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "电子礼品卡对账报表导出")
        case "offlineGiftCardDailyHst":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "实体礼品卡对账报表导出")
        case "orgCardSetting":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "卡回收设置导入")
        case "memberOperationBill":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "会员批量操作单导入")
        case "batchManualHandOutGift":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "群发券会员导入")
        case "batchManualHandOutGift2":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "群发券会员导入")
        case "cardDepositBill":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "预付卡充值单导入")
        case "OnlineGiftCard":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "电子礼品卡导出")
        case "OfflineGiftCard":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "实体礼品卡导出")
        case "ImprestCard":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "充值卡导出")
        case "RechargeableCard":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "储值卡导出")
        case "exchangeableCardRechargeHst":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "储值卡充值流水导出")
        case "storeOilGunPromotion":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "门店促销设置导入")
        case "platStore":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "平台门店导入")
        case "CouponDelayApply":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "延期申请导出")
        case "CouponTemplate":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "券模板导出")
        case "PurchaseCouponTrade":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "购券交易导出")
        case "PurchaseCouponTradeAfterSales":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "退款申请导出")
        case "GFCouponReconciliation":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "广发券对账文件导出")
        case "equityCard":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "权益卡导出")
        case "orgCycleConsumptionReport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "门店会员复购分析导出")
        case "org":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "门店资料导出")
        case "platformOrg":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "渠道门店资料导出")
        case "couponTemplateProNumImport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "券模板促销单导入")
        case "couponTemplateImport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "券模板导入")
        case "takePartMember":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "消费日历人群导入")
        case "thirdCouponCode":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "消费日历兑换码导入")
        case "TakePartRecord":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "消费日历参与记录导出")
        case "countingCardSalesHst":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "次卡售卡流水导出")
        case "countingCardRefundCardHst":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "次卡退卡流水导出")
        case "countingCardConsumeHst":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "次卡消费流水导出")
        case "countingCardRefundHst":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "次卡退款流水导出")
        case "CountingCard":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "次卡导出")
        case "MagCardMediumExport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "磁卡制卡单卡号密码导出")
        case "BarCardMediumExport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "条码卡制卡单卡号密码导出")
        case "BarCardMediumTxtExport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "条码卡制卡单卡号密码导出")
        case "OnlineCardMediumExport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "电子卡制卡单卡号密码导出")
        case "platformCouponUseDetailReport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "平台券核销数据导出")
        case "CardSaleBillDetail":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "售卡单明细导出")
        case "RecoverCardBillLine":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "卡回收单卡号导出")
        case "RecoverCardBillDifferentLine":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "卡回收单差异数据导出")
        case "collectPointsReward":
          return new ConstantMgr.MenusFuc().format("/营销/集点活动", '集点活动奖品管理导出')
        case "collectPointsActivityQueryGoodsImport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", '集点活动查询条件导入')
        case "collectPointsActivityGoodsExport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", '集点活动查询结果导出')
        case "memberBlacklist":
          if (type === "upload") {
            return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "黑名单导入")
          }
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "黑名单导出")
        case "bigWheelAwardManagement":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "大转盘活动奖品管理导出")
        case "bigWheelTakePartRecord":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "大转盘活动参与记录导出")
        case "bigWheelRaffleChanceLog":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "大转盘活动参与记录导出")
        case "exchangeableCardTransferHst":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "储值卡转出流水导出")
        case "changeMemberState":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "批量更新会员状态导入")
        case "memberBalanceRechargeBillImportMember":
          return new ConstantMgr.MenusFuc().format("/储值/会员储值/储值充值单", "导入充值会员")
        case "weiXinAppletIssueCouponActivityImport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "导入小程序领微信券活动")
        case "aliAppletIssueCouponActivityImport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "导入小程序领支付宝券活动")
        case "redemptionCode":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "兑换码导出");
        case "batchRedemptionCodeBill":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "导入兑换码值")
        case "receiveCardDetailExport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "领卡单明细导出")
        case "CouponSettleBillExport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "平台券账单导出")
        case "PrepaySettleBillExport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "储值账单导出")
        case "PrepayCardSettleBillExport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "礼品卡账单导出")
        case "DepositCardSettleBillExport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "储值卡账单导出")
        case "PhoenixCouponSettleBillExport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "CRM券账单导出")
        case "PointsSettleBillExport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "积分账单导出")
        case "groupBookingRecord":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "抽奖团拼团记录导出")
        case "groupBookingWonRecord":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "抽奖团中奖记录导出")
        case "RficCardMediumExport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "RFIC卡密导出")
        case "memberTagV2":
          return new ConstantMgr.MenusFuc().format("/公用/标签人数明细列表", "标签人数明细导出")
        case "MemberPointsAnalysisReport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "会员积分分析报表导出")
        case "MemberDepositAnalysisReport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "会员储值分析报表导出")
        case "employeePromotionCode":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "员工推广码")
        case "storePromotionCode":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "门店推广码")
        case "storesQrCode":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "微信会员卡门店投放码")
        case "weixinCardEmployeeCode":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "微信会员卡员工投放码")
        case "employeeBalanceCode":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "储值二维码")
        case "employeeInviteCode":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "小程序二维码")
        case "orgMemberAnalysisReport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "会员增长分析导出")
        case "prepayAdjustBillLine":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "储值调整单明细导出")
        case "orgTradeAnalysisReport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "会员交易分析导出")
        case "platformCouponAnalysisReport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "平台券分析导出")

        case "ImprestCardAnalysisExport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "充值卡分析报表导出")
        case "RechargeableCardAnalysisExport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "储值卡分析报表导出")
        case "GiftCardAnalysisExport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "礼品卡分析报表导出")
        case "tagSummaryExport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "标签分布导出")
        case "rfmUserDetailExport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "RFM分析联动明细导出")
          case "memberAnalysisReport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "会员分析导出")
        case "freeBenefitCard":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "权益卡查询导出")
        case "paidBenefitCard":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "付费会员卡查询导出")
        case "freeBenefitCardTransaction":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "权益卡流水导出")
        case "paidBenefitCardTransaction":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "付费会员卡流水导出")
        case "I18nCodeTableExport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "国际化码表导出")
        case "I18nCodeTableImport":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "国际化码表导入")
        case "collectPointsTakePartRecord":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "集点活动参与记录导出")
        case "modifyCardBuyer":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "导入购卡人")
        case "luckyDrawPrizeRecord":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "抽锦鲤奖品管理导出")
        case "luckyDrawParticipatesRecord":
          return new ConstantMgr.MenusFuc().format("/公用/预约文件列表", "抽锦鲤参与记录导出")
        default:
          return ""
      }
    })
    Vue.filter("areaState", function (val) {
      const areaState = ["CANCEL", "NORMAL"]
      if (val === "CANCEL") {
        return new ConstantMgr.MenusFuc().format("/资料/区域", "已作废")
      } else {
        return new ConstantMgr.MenusFuc().format("/资料/区域", "正常")
      }
    })
    Vue.filter("subjectApprotionTypeFilter", function (val) {
      const subjectApprotionType = ["FAV", "PAY", "COLLOCATION"]
      if (val === "FAV") {
        return new ConstantMgr.MenusFuc().format("/公用/券模板", "优惠方式")
      } else if (val === "PAY") {
        return new ConstantMgr.MenusFuc().format("/公用/券模板", "支付方式")
      } else if (val === "COLLOCATION") {
        return new ConstantMgr.MenusFuc().format("/公用/券模板", "组合方式")
      }
    })

    Vue.filter("couponWriteOffState", function (val) {
      if (val === "INITIAL") {
        return new ConstantMgr.MenusFuc().format("/公用/过滤器", "未审核")
      } else if (val === "EFFECTED") {
        return new ConstantMgr.MenusFuc().format("/公用/过滤器", "已审核")
      }
    })
    //根据卡模板介质展示有效期格式
    Vue.filter("cardTemplateValidityTime", function (val) {
      function getValidNum (validityInfo) {
        if (validityInfo.validityDays !== null) {
          return `${validityInfo.validityDays}${new ConstantMgr.MenusFuc().format('/储值/预付卡/卡模板/编辑页面', '天')}`
        }
        if (validityInfo.validityYears !== null) {
          return `${validityInfo.validityYears}${new ConstantMgr.MenusFuc().format('/储值/预付卡/卡模板/编辑页面', '年')}`
        }
      }
      if (!val.validityInfo) {
        return '-'
      }
      if (val.validityInfo.validityType === 'FIXED') {
        return DateUtil.format(val.validityInfo.endDate, 'yyyy-MM-dd')
      }
      if (val.validityInfo.validityType === 'RALATIVE') {
        if (val.cardMedium === 'online' && val.cardTemplateType === 'GIFT_CARD') {  //目前只有电子礼品卡能激活
          const str = new ConstantMgr.MenusFuc().format('/储值/预付卡/卡模板/列表页面', '激活后{0}内有效')
          return str.replace(/\{0\}/g, getValidNum(val.validityInfo));
        } else {
          const str = new ConstantMgr.MenusFuc().format('/储值/预付卡/卡模板/编辑页面', '发售后{0}内有效')
          return str.replace(/\{0\}/g, getValidNum(val.validityInfo));
        }
      }
    })

    //卡模板类型
    Vue.filter("cardType", function (val) {
      switch (val) {
        case 'GiftCard':
          return new ConstantMgr.MenusFuc().format("/储值/预付卡/电子礼品卡活动/编辑页面", "礼品卡")
        case 'RechargeableCard':
          return new ConstantMgr.MenusFuc().format("/储值/预付卡/电子礼品卡活动/编辑页面", "储值卡")
        case 'ImprestCard':
          return new ConstantMgr.MenusFuc().format("/储值/预付卡/电子礼品卡活动/编辑页面", "充值卡")
        case 'CountingCard':
          return new ConstantMgr.MenusFuc().format("/储值/预付卡/电子礼品卡活动/编辑页面", "次卡")
        default:
          return '--'
      }
    })
    // // 全部活动
    // Vue.filter("AllActivityType", function (val) {
    //   switch (val) {
    //     case "GainPointsGoodsActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/公用/过滤器", "不积分商品");
    //     case "PointsChargeActivityRuleV2":
    //       return new ConstantMgr.MenusFuc().format("/公用/菜单", "积分抵现活动");
    //     case "PrepayDepositActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/公用/菜单", "充值有礼");
    //     case "MemberBalanceReductionActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/储值/会员储值/储值支付活动/列表页面", "储值支付立减");
    //     case "MemberBalanceDiscountActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/储值/会员储值/储值支付活动/列表页面", "储值支付折扣");
    //     case "GiftCardActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/公用/菜单", "电子卡售卡活动");
    //     case "OffLineGiftCardActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/公用/菜单", "实体卡售卡活动");
    //     case "CardBalanceReductionActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/储值/预付卡/预付卡支付活动/列表页面", "预付卡支付立减");
    //     case "CardBalanceDiscountActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/储值/预付卡/预付卡支付活动/列表页面", "预付卡支付折扣");
    //     case "PrepayCardDepositActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/储值/预付卡/预付卡充值有礼", "预付卡充值有礼");
    //     case "GoodsGainAdditionalPointsByQtyActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/公用/过滤器", "单品满数量加送积分");
    //     case "GoodsGroupGainAdditionalPointsByQtyActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/公用/过滤器", "商品组合满数量加送积分");
    //     case "GoodsGainAdditionalPointsActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/公用/过滤器", "商品满额加送积分");
    //     case "GoodsGainPointsSpeedActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/公用/过滤器", "商品满额积分加倍");
    //     case "GoodsGainCouponActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "商品满额发券");
    //     case "GoodsGainCouponByQtyActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "商品满数量发券");
    //     case "UseCouponIssueGiftBagActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "用券发券");
    //     case "GoodsGainGiftBagActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "商品满额发大礼包");
    //     case "ConsumeCalendarActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/营销/日历活动", "消费日历");
    //     case "CollectPointsActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/营销/集点活动", "集点活动");
    //     case "CumulativeConsumeActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/营销/累计消费有礼", "累计消费有礼");
    //     case "MemberInviteRegisterGiftActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "邀请有礼");
    //     case "UpgradeGiftActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/营销/升级有礼", "升级有礼");
    //     case "MemberRegisterGiftActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "注册发大礼包");
    //     case "WeixinActiveGiftActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "微信激活发大礼包");
    //     case "ImproveProfilesGiftRule":
    //       return new ConstantMgr.MenusFuc().format("/公用/菜单", "完善资料有礼");
    //     case "PointsExchangeGoodsActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/营销/积分活动", "积分兑换商品");
    //     case "PointsExchangeCouponActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/营销/积分活动/积分活动", "积分兑换券");
    //     case "MiniProgramGainCouponActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "小程序领券");
    //     case "WeixinCouponActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "微信投放券");
    //     case "ManualHandOutGiftActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "群发券");
    //     case "ThirdIssueCouponActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "第三方发券");
    //     case "ThirdIssueCouponByCodeActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "核销第三方券");
    //     case "UserGroupHandOutGiftActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "客群发大礼包");
    //     case "SingleTimePushPlanActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/营销/运营/推送计划/新建页", "定时-单次推送");
    //     case "RepeatPushPlanActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/营销/运营/推送计划/新建页", "定时-重复推送");
    //     case "ExportCouponCodeActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "导出券码发券");
    //     case "PrecisionIssueCouponActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/公用/菜单", "精准营销");
    //     case "AliAppletIssueCouponActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/券/延期申请", "支付宝小程序发券");
    //     case "AliAppletConsumeGiftRule":
    //       return new ConstantMgr.MenusFuc().format("/公用/菜单", "消费有礼");
    //     case "WeiXinAppletIssueCouponActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/券/延期申请", "小程序领微信券");
    //     case "WeixinBrandActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/公用/过滤器", "开屏推广");
    //     case "WeChatVideoIssueCouponActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/公用/菜单", "视频号发券");
    //     case "DirectionalIssueCouponActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/公用/过滤器", "定向发券");
    //     case "BigWheelActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/营销/大转盘活动", "大转盘");
    //     case "GroupBookingActivityRule":
    //       return new ConstantMgr.MenusFuc().format("/营销/拼团抽奖活动", "抽奖团");
    //     case "PlatformGroupCouponRule":
    //       return new ConstantMgr.MenusFuc().format("/公用/过滤器", "平台团购券");
    //     default:
    //       return "--";
    //   }
    // });

    // 全部活动
    Vue.filter("AllActivityType", function (val) {
      switch (val) {
        case "GAIN_POINTS_GOODS":
          return new ConstantMgr.MenusFuc().format("/公用/过滤器", "不积分商品");
        case "POINTS_CHARGE_V2":
          return new ConstantMgr.MenusFuc().format("/公用/菜单", "积分抵现活动");
        case "DEPOSIT_GIFT":
          return new ConstantMgr.MenusFuc().format("/公用/菜单", "充值有礼");
        case "MEMBER_BALANCE_REDUCTION":
          return new ConstantMgr.MenusFuc().format("/储值/会员储值/储值支付活动/列表页面", "储值支付立减");
        case "MEMBER_BALANCE_DISCOUNT":
          return new ConstantMgr.MenusFuc().format("/储值/会员储值/储值支付活动/列表页面", "储值支付折扣");
        case "GIFT_CARD":
          return new ConstantMgr.MenusFuc().format("/公用/菜单", "电子卡售卡活动");
        case "OFF_LINE_GIFT_CARD":
          return new ConstantMgr.MenusFuc().format("/公用/菜单", "实体卡售卡活动");
        case "CARD_BALANCE_REDUCTION":
          return new ConstantMgr.MenusFuc().format("/储值/预付卡/预付卡支付活动/列表页面", "预付卡支付立减");
        case "CARD_BALANCE_DISCOUNT":
          return new ConstantMgr.MenusFuc().format("/储值/预付卡/预付卡支付活动/列表页面", "预付卡支付折扣");
        case "CARD_DEPOSIT_GIFT":
          return new ConstantMgr.MenusFuc().format("/储值/预付卡/预付卡充值有礼", "预付卡充值有礼");
        case "GOODS_GAIN_ADDITIONAL_POINTS_BY_QTY":
          return new ConstantMgr.MenusFuc().format("/公用/过滤器", "单品满数量加送积分");
        case "GOODS_GROUP_GAIN_ADDITIONAL_POINTS_BY_QTY":
          return new ConstantMgr.MenusFuc().format("/公用/过滤器", "商品组合满数量加送积分");
        case "GOODS_GAIN_ADDITIONAL_POINTS":
          return new ConstantMgr.MenusFuc().format("/公用/过滤器", "商品满额加送积分");
        case "GOODS_GAIN_POINTS_SPEED":
          return new ConstantMgr.MenusFuc().format("/公用/过滤器", "商品满额积分加倍");
        case "GOODS_GAIN_COUPON":
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "商品满额发券");
        case "GOODS_QTY_GAIN_COUPON":
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "商品满数量发券");
        case "USE_COUPON_ISSUE_GIFT":
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "用券发券");
        case "GOODS_GAIN_GIFT":
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "商品满额发大礼包");
        case "CONSUME_CALENDAR_GIFT":
          return new ConstantMgr.MenusFuc().format("/营销/日历活动", "消费日历");
        case "COLLECT_POINTS_ACTIVITY":
          return new ConstantMgr.MenusFuc().format("/营销/集点活动", "集点活动");
        case "CUMULATIVE_CONSUME_ACTIVITY":
          return new ConstantMgr.MenusFuc().format("/营销/累计消费有礼", "累计消费有礼");
        case "MEMBER_INVITE_REGISTER_GIFT":
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "邀请有礼");
        case "UPGRADE_GIFT":
          return new ConstantMgr.MenusFuc().format("/营销/升级有礼", "升级有礼");
        case "MEMBER_REGISTER_GIFT":
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "注册发大礼包");
        case "WEIXIN_ACTIVATION_GIFT":
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "微信激活发大礼包");
        case "IMPROVE_PROFILES_GIFT":
          return new ConstantMgr.MenusFuc().format("/公用/菜单", "完善资料有礼");
        case "POINTS_EXCHANGE_GOODS":
          return new ConstantMgr.MenusFuc().format("/营销/积分活动", "积分兑换商品");
        case "POINTS_EXCHANGE_COUPON":
          return new ConstantMgr.MenusFuc().format("/营销/积分活动/积分活动", "积分兑换券");
        case "MINI_PROGRAM_COUPON":
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "小程序领券");
        case "WEIXIN_COUPON":
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "微信投放券");
        case "MANUAL_COUPON":
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "群发券");
        case "THIRD_ISSUE_COUPON":
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "第三方发券");
        case "THIRD_CODE_ISSUE_COUPON":
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "核销第三方券");
        case "USER_GROUP_GIFT":
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "客群发大礼包");
        case "SINGLE_TIME_PUSH_PLAN":
          return new ConstantMgr.MenusFuc().format("/营销/运营/推送计划/新建页", "定时-单次推送");
        case "REPEAT_PUSH_PLAN":
          return new ConstantMgr.MenusFuc().format("/营销/运营/推送计划/新建页", "定时-重复推送");
        case "EXPORT_COUPON_CODE":
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "导出券码发券");
        case "PRECISION_ISSUE_COUPON":
          return new ConstantMgr.MenusFuc().format("/公用/菜单", "精准营销");
        case "ALI_APPLET_ISSUE_COUPON":
          return new ConstantMgr.MenusFuc().format("/券/延期申请", "支付宝小程序发券");
        case "ALI_APPLET_CONSUME_GIFT":
          return new ConstantMgr.MenusFuc().format("/公用/菜单", "消费有礼");
        case "WEI_XIN_APPLET_ISSUE_COUPON":
          return new ConstantMgr.MenusFuc().format("/券/延期申请", "小程序领微信券");
        case "WEIXIN_BRAND_ACTIVITY":
          return new ConstantMgr.MenusFuc().format("/公用/过滤器", "开屏推广");
        case "WeChatVideoIssueCouponActivityRule":
          return new ConstantMgr.MenusFuc().format("/公用/菜单", "视频号发券");
        case "DirectionalIssueCouponActivityRule":
          return new ConstantMgr.MenusFuc().format("/公用/过滤器", "定向发券");
        case "BIG_WHEEL_ACTIVITY":
          return new ConstantMgr.MenusFuc().format("/营销/大转盘活动", "大转盘");
        case "GROUP_BOOKING_ACTIVITY":
          return new ConstantMgr.MenusFuc().format("/营销/拼团抽奖活动", "抽奖团");
        case "ALI_PLT_GROUP_COUPON":
          return new ConstantMgr.MenusFuc().format("/公用/过滤器", "平台团购券");
        default:
          return "--";
      }
    });
    // 营销活动日历
    Vue.filter("CalendarActivityType", function (val) {
      switch (val) {
        case "GainPointsGoodsActivityRule":
          return new ConstantMgr.MenusFuc().format("/公用/过滤器", "不积分商品");
        case "PointsChargeActivityRuleV2":
          return new ConstantMgr.MenusFuc().format("/公用/菜单", "积分抵现活动");
        case "PrepayDepositActivityRule":
          return new ConstantMgr.MenusFuc().format("/公用/菜单", "充值有礼");
        case "MemberBalanceReductionActivityRule":
          return new ConstantMgr.MenusFuc().format("/储值/会员储值/储值支付活动/列表页面", "储值支付立减");
        case "CardBalanceDiscountActivityRule":
          return new ConstantMgr.MenusFuc().format("/储值/会员储值/储值支付活动/列表页面", "储值支付折扣");
        case "GiftCardActivityRule":
          return new ConstantMgr.MenusFuc().format("/公用/菜单", "电子卡售卡活动");
        case "OffLineGiftCardActivityRule":
          return new ConstantMgr.MenusFuc().format("/公用/菜单", "实体卡售卡活动");
        case "CardBalanceReductionActivityRule":
          return new ConstantMgr.MenusFuc().format("/储值/预付卡/预付卡支付活动/列表页面", "预付卡支付立减");
        case "CardBalanceDiscountActivityRule":
          return new ConstantMgr.MenusFuc().format("/储值/预付卡/预付卡支付活动/列表页面", "预付卡支付折扣");
        case "PrepayCardDepositActivityRule":
          return new ConstantMgr.MenusFuc().format("/储值/预付卡/预付卡充值有礼", "预付卡充值有礼");
        case "GoodsGainAdditionalPointsByQtyActivityRule":
          return new ConstantMgr.MenusFuc().format("/公用/过滤器", "单品满数量加送积分");
        case "GoodsGroupGainAdditionalPointsByQtyActivityRule":
          return new ConstantMgr.MenusFuc().format("/公用/过滤器", "商品组合满数量加送积分");
        case "GoodsGainAdditionalPointsActivityRule":
          return new ConstantMgr.MenusFuc().format("/公用/过滤器", "商品满额加送积分");
        case "GoodsGainPointsSpeedActivityRule":
          return new ConstantMgr.MenusFuc().format("/公用/过滤器", "商品满额积分加倍");
        case "GoodsGainCouponActivityRule":
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "商品满额发券");
        case "GoodsGainCouponByQtyActivityRule":
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "商品满数量发券");
        case "UseCouponIssueGiftBagActivityRule":
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "用券发券");
        case "GoodsGainGiftBagActivityRule":
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "商品满额发大礼包");
        case "ConsumeCalendarActivityRule":
          return new ConstantMgr.MenusFuc().format("/营销/日历活动", "消费日历");
        case "CollectPointsActivityRule":
          return new ConstantMgr.MenusFuc().format("/营销/集点活动", "集点活动");
        case "CumulativeConsumeActivityRule":
          return new ConstantMgr.MenusFuc().format("/营销/累计消费有礼", "累计消费有礼");
        case "MemberInviteRegisterGiftActivityRule":
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "邀请有礼");
        case "UpgradeGiftActivityRule":
          return new ConstantMgr.MenusFuc().format("/营销/升级有礼", "升级有礼");
        case "MemberRegisterGiftActivityRule":
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "注册发大礼包");
        case "WeixinActiveGiftActivityRule":
          return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "微信激活发大礼包");
        case "ImproveProfilesGiftRule":
          return new ConstantMgr.MenusFuc().format("/公用/菜单", "完善资料有礼");
        default:
          return "--";
      }
    });
    // 卡介质
    Vue.filter("cardMediumStr", function (val) {
      switch (val) {
        case 'online':
          return new ConstantMgr.MenusFuc().format("/卡/卡管理/卡介质", "电子卡")
        case 'bar':
          return new ConstantMgr.MenusFuc().format("/卡/卡管理/卡介质", "条码卡")
        case 'mag':
          return new ConstantMgr.MenusFuc().format("/卡/卡管理/卡介质", "磁条卡")
        case 'rfic':
          return new ConstantMgr.MenusFuc().format("/卡/卡管理/卡介质", "RFIC卡")
        case 'ic':
          return "ic卡"
        default:
          break;
      }
    })

    // 卡介质
    Vue.filter("dateTypeName", function (val) {
      let str=''
      switch (val) {
        case 'days':
          str= new ConstantMgr.MenusFuc().format("/会员/付费会员", "天")
          break;
        case 'month':
          str= new ConstantMgr.MenusFuc().format("/会员/标签客群/标签", "月")
          break;
        case 'year':
          str= new ConstantMgr.MenusFuc().format("/会员/标签客群/标签", "年")
          break;
        default:
          str= val
          break;
      }
      return str
    })
  }
}
