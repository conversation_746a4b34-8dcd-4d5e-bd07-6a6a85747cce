import CardAdjustBillLine from 'model/card/adjustbill/CardAdjustBillLine'
import CardAdjustBillLog from 'model/card/adjustbill/CardAdjustBillLog'
import IdName from 'model/common/IdName'
import PaymentMethod from "model/card/adjustbill/PaymentMethod";

export default class CardAdjustBill {
    // 单号
    billNumber: Nullable<string> = null
    // 状态INITIAL：未审核；AUDITED：已审核
    state: Nullable<string> = null
    // 来源：impt-导入；create-界面新建
    source: Nullable<string> = null
    // 发生组织
    occurredOrg: Nullable<IdName> = null
    // 营销中心
    marketingCenter: Nullable<string> = null
    // 审核时间
    audited: Nullable<Date> = null
    // 审核人
    auditor: Nullable<string> = null
    // 备注
    remark: Nullable<string> = null
    // 明细备注只用于展示： 返回格式：增加卡余额额数，扣减卡余额数；
    detailRemark: string[] = []
    // 创建时间
    created: Nullable<Date> = null
    // 最后修改时间
    lastModified: Nullable<Date> = null
    // 明细
    lines: CardAdjustBillLine[] = []
    // 操作日志
    logs: CardAdjustBillLog[] = []
    // 充值类型
    depositType: Nullable<string> = null
    // 客户ID
    clientId: Nullable<string> = null
    // 客户代码
    clientCode: Nullable<string> = null
    // 客户名称
    clientName: Nullable<string> = null
    // 优惠金额
    favAmount: Nullable<number> = null
    // 单卡本金金额
    lineAmount: Nullable<number> = null
    // 单卡赠金金额
    lineGiftAmount: Nullable<number> = null
    // 支付方式
    payments: PaymentMethod[] = []
}