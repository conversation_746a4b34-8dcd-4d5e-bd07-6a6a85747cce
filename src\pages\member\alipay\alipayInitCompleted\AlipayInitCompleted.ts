import {Component, Vue} from 'vue-property-decorator'
import WechatHeader from 'cmp/wechatheader/WechatHeader.vue'
import StoreCodeDownload from 'pages/member/wx/wechatinit/cmp/StoreCodeDownload.vue'
import CopyCardLink from 'pages/member/wx/wechatinit/cmp/CopyCardLink.vue'
import AlipayInitApi from 'http/aliPay/v2/AlipayInitApi'
import VueQrcode from 'vue-qrcode'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import I18nPage from 'common/I18nDecorator'

@Component({
  name: 'AlipayInitCompleted',
  components: {
    WechatHeader,
    StoreCodeDownload,
    CopyCardLink,
    VueQrcode,
    BreadCrume
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/设置/支付宝会员初始化'
  ],
  auto: true
})
export default class AlipayInitCompleted extends Vue {
  qrCodeUrl = ''
  initFlag = false
  dialogCodeVisible = false
  refs: any
  cardUrl: string = ''
  loginInfo: any // 登录返回数据
  code: Nullable<string> = null
  timer: number = 0

  mounted() {
    this.loginInfo = localStorage.getItem('ucenterUser'); // 从localStorage中获取登录返回数据
    if (this.loginInfo) {
      this.loginInfo = JSON.parse(this.loginInfo);
      this.code = this.loginInfo.uuid
    }

  }

  // 跳转已授权登录成功页面
  toWechatAuthorizeAfter() {
    this.$router.push({name: 'alipay-member-card-edit-dtl'})
  }

  // 跳转已创建会员卡资料页面
  toWechatMemberCard() {
    // this.$router.push({name: 'alipay-member-qr-code'})
    this.dialogCodeVisible = true
  }

  // 下载全部门店二维码
  goAllStoresQrCode() {
    AlipayInitApi.getStoreQrCode().then((res: any) => {
      if (res && res.code === 2000) {
        this.qrCodeUrl = res.data
        setTimeout(() => {
          this.doDownLoadQRCode()
        }, 100)
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  // 复制全部门店领卡链接
  doCopyLink() {
    // 复制领卡链接
    (this.$refs.copyCardLink as any).open()
  }

  // 下载指定门店二维码
  goStoreQrCode() {
    AlipayInitApi.downLoadStoreQrCodes().then((res: any) => {
      if (res && res.code === 2000) {
        this.$message.success('已成功加入预约文件列表，请前往查看')
      } else {
        this.$message.error(res.msg);
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  doDownLoadQRCode() {
    /* 下载二维码*/
    let img: any = document.getElementById('qrcode')
    let link = document.createElement('a')
    let url = img.getAttribute('src')
    link.setAttribute('href', url as any)
    link.setAttribute('download', '支付宝卡包领卡链接二维码.png')
    link.click()
  }
}