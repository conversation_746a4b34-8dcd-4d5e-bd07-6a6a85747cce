import {Component, Inject} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import RSCategory from 'model/common/RSCategory'
import RSCategoryFilter from 'model/common/RSCategoryFilter'
import CategoryApi from 'http/category/CategoryApi'
import AbstractSelectDialog from './AbstractSelectDialog'

@Component({
  name: 'CatogorySelectorDialog',
  components: {
    FormItem
  }
})
export default class CatogorySelectorDialog extends AbstractSelectDialog<RSCategory> {
  categoryFilter: RSCategoryFilter = new RSCategoryFilter()

  reset() {
    this.categoryFilter = new RSCategoryFilter()
  }

  getId(ins: RSCategory): string {
    // @ts-ignore
    return ins.category.id;
  }

  getName(ins: RSCategory): string {
    // @ts-ignore
    return ins.category.name;
  }

  getResponseData(response: any): any {
    return response.data
  }

  queryFun(): Promise<any> {
    this.categoryFilter.page = this.page.currentPage - 1
    this.categoryFilter.pageSize = this.page.size
    let oldMarketCenter = sessionStorage.getItem('marketCenter') || ''
		sessionStorage.setItem('marketCenter', this.marketCenter)
    return CategoryApi.query(this.categoryFilter).finally(()=>{
			sessionStorage.setItem('marketCenter', oldMarketCenter as string)
		});
  }
}
