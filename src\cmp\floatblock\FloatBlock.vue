<template>
    <div>
      <div :class="{ 'float-block': isFloat }" :style="{top: top + 'px'}" id="float-block">
        <slot name="ctx"></slot>
      </div>
      <div id="foo" v-show="isFloat">&nbsp;</div>
    </div>
</template>

<script lang="ts" src="./FloatBlock.ts">
</script>

<style lang="scss">
    .float-block {
        z-index: 999;
        background-color: white;
        position: fixed;
        box-shadow: 2px 4px 6px #bbbbbb;
        padding: 10px 0;
    }
</style>
