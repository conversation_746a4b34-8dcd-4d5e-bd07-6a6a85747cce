import { Vue, Component, Prop, Watch } from "vue-property-decorator";
import Data from "./JumpPageData";
import NavigationSettingApi from "http/navigation/NavigationSettingApi";
import JumpPageInfo from "model/navigation/JumpPageInfo";
import ContentTemplateApi from "http/template/ContentTemplateApi";
import MenuSetting from "model/navigation/MenuSetting";
import ContentTemplateFilter from "model/template/ContentTemplateFilter";
import NavigationSetting from "model/navigation/NavigationSetting";
import ContentTemplate from "model/template/ContentTemplate";
import { ContentTemplateState } from "model/template/ContentTemplateState";
import ElasticLayerPage from "../../elastic-layer-page/ElasticLayerPage.vue";
import { PageVO } from "./PageVO";
import UnionActivityQuery from "model/promotion/UnionActivityQuery";
import GiftCardActivityApi from "http/card/activity/GiftCardActivityApi";
import PointsActivity<PERSON>pi from "http/points/activity/PointsActivityApi";
import UnionActivityApi from "http/promotion/UnionActivityApi";
import GiftCardActivityFilter from "model/card/activity/GiftCardActivityFilter";
import ActivityGroupType from "model/common/ActivityGroupType";
import PointsActivityFilter from "model/points/activity/PointsActivityFilter";
import ActivityStateTag from "cmp/activity-state-tag/ActivityStateTag";
import I18nPage from "common/I18nDecorator";
import { CmsConfigChannel } from "model/template/CmsConfig";

@Component({
  name: "PageJump",
  mixins: [],
  components: { ElasticLayerPage, ActivityStateTag },
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/公用/菜单',
    '/页面/页面管理',
    '/公用/过滤器',
    '/会员/会员资料',
    '/设置/页面管理'
  ],
  auto: true
})
export default class PageJump extends Vue {
  @Prop({ type: Object }) value: any; // 数据模型
  @Prop({ type: Boolean }) showJumpPage: any; // 数据模型
  @Prop({ type: Boolean }) showTitile: any; // 数据模型
  @Prop({ type: Boolean }) isMenuSet: any; // 是否是菜单组件
  @Prop() linkWidth: Nullable<number>; //链接宽度
  @Prop({ type: Array, default: () => [CmsConfigChannel.WEIXIN, CmsConfigChannel.ALIPAY, CmsConfigChannel.H5] }) advertiseChannel: Nullable<CmsConfigChannel[]>; //当前页面可用的投放渠道
  dialogShow: Boolean = false;
  JumpPageDatas: any = null;
  stairIndex: number = 0; // 一级菜单当前选项
  secondIndex: number = 0; // 二级菜单当前选项
  jumpPageInfo: JumpPageInfo = new JumpPageInfo(); // 页面信息
  MenuSetting: MenuSetting[] = []; // 菜单设置
  NavigationSetting: NavigationSetting = new NavigationSetting(); // 活动模型
  ContentTemplateFilter: ContentTemplateFilter = new ContentTemplateFilter();
  ContentTemplate: ContentTemplate[] = []; // 活动模型
  radio: number = 0;
  activityRange: string = "all";
  copyTemplateName: string = "";
  sysPageChecked: boolean = false;
  h5Val: string = "";
  $refs: any;
  ruleForm: any = {
    appJumpType: "",
    appId: "",
    appLink: "",
    checkList: [],
  };

  isOpened: any = {};
  activeChildType: Nullable<string> = null;
  activeSub: any[] = [];
  featuresNameRadio: string = "";
  featurePageName: string = "";
  featurePageNameInput: string = '';
  customizationPageName: string = ''
  customizationPageNameRadio: string = ''

  activityList: any[] = [];
  activityListLoading: boolean = false;
  filter = {
    state: "PROCESSING",
    keyWords: null as null | string,
    activeNo: null as null | string,
  };
  activityListRadio: string = "";

  // 功能页面
  FeaturePageList = [
    {
      name: this.i18n("会员码"),
      templateId: "member/member-code/member-code",
    },
    {
      name: this.i18n("会员中心"),
      templateId: "member/member-center/MemberCenter",
    },
    {
      name: this.i18n("付费会员卡中心"),
      templateId: "member/paid-member-card/paid-member-card",
    },
    {
      name: this.i18n("权益卡中心"),
      templateId: "member/equity-member-card/equity-member-card",
    },
    {
      name: this.i18n("我的优惠券"),
      templateId: "pages/coupon/CouponList",
    },
    {
      name: this.i18n("我的礼品卡"),
      templateId: "giftCard/list/List",
    },
    {
      name: this.i18n("我的储值"),
      templateId: "pagesSub/deposit/DepositDtl",
    },
    {
      name: this.i18n("积分付款码"),
      templateId: "point/point-code",
    },
    {
      name: this.i18n("储值付款码"),
      templateId: "pages/balance/balance-code",
    },
    {
      name: this.i18n("卡充值"),
      templateId: "pagesSub/deposit/CardRecharge",
    },
    {
      name: this.i18n("充值有礼"),
      templateId: "pagesSub/deposit/DepositCharge",
    },
    {
      name: this.i18n("门店列表页"),
      templateId: "pages/shop/index",
    },
    {
      name: this.i18n("修改个人资料"),
      templateId: "member/modify-member/modify-member",
    },
    {
      name: this.i18n("等级规则"),
      templateId: "member/member-rules/member-rules",
    },
    {
      name: this.i18n("兑换码"),
      templateId: "coupon/code/ExchangeCode",
    },
    {
      name: this.i18n("/页面/页面管理/大转盘中奖记录"),
      templateId: "",
    },
    {
      name: this.i18n("抽锦鲤参与记录"),
      templateId: "/pagesSub/raffle-koi/RaffleKoiDtl",
    },
    {
      name: this.i18n("微信支付"),
      templateId: "",
    },
    {
      name: this.i18n("订单"),
      templateId: "member/order/OrderList",
    },
  ];

  // 定制页面
  get customizationList() {
    const array = []
    if (this.hasOptionPermission('/小程序定制页/百联储值/百联储值', '开通')) {
      array.push({
        name: this.i18n("/储值/会员储值/百联储值"),
        templateId: "member/stored-detail/stored-detail",
      })
    }
    if (this.hasOptionPermission('/小程序定制页/百联储值付款码/百联储值付款码', '开通')) {
      array.push({
        name: this.i18n("/储值/会员储值/百联储值付款码"),
        templateId: "bailianPayCode",
      })
    }
    return array
  }

  // get hasBailianPermission() {
  //   return this.hasOptionPermission('/小程序定制页/百联储值/百联储值', '开通') || this.hasOptionPermission('/小程序定制页/百联储值付款码/百联储值付款码', '开通')
  // }

  get activeSubItem() {
    return this.activeSub[this.secondIndex];
  }

  get isEnglish() {
    return sessionStorage.getItem("locale") === "en_US";
  }

  options: any = [
    { caption: this.i18n("全部活动"), key: "all" },
    { caption: this.i18n("指定活动"), key: "part" },
  ];
  pageName: string = "";
  rules = {
    firstLevelPage: [{ required: true, message: this.i18n("请选择跳转页面"), trigger: "change" }],
  };
  page = {
    currentPage: 1,
    total: 0,
    size: 20,
  };
  @Watch("value", { deep: true, immediate: true })
  async handleValue(value: any) {
    if (this.isMenuSet) {
      this.jumpPageInfo = new JumpPageInfo();
    }
    console.log('value watch触发了');
    await this.getList();
    this.setPageInfo();
  }

  @Watch("radio", { immediate: true })
  handleRadio() {
    if (!this.radio) {
      this.jumpPageInfo.templateName = null;
      this.jumpPageInfo.templateId = null;
    } else {
      this.jumpPageInfo.templateId = this.radio?.toString();
      const find = this.ContentTemplate.find((item) => {
        return item?.id?.toString() === this.radio?.toString();
      });

      if (find) {
        this.jumpPageInfo.templateName = find.name;
      }
    }
  }

  get linkTitleStr() {
    const firstLevel = this.jumpPageInfo?.firstLevelPage;
    const secondLevel = this.jumpPageInfo?.secondLevelPage;
    const templateName = this.jumpPageInfo?.templateName;
    if (!firstLevel) {
      return this.i18n("添加跳转链接")+" >";
    }
    const thirdLevel = this.pageName || templateName || ""
    return `${firstLevel}/${secondLevel || ""}${thirdLevel ? '/' +thirdLevel : ""}`;
  }

  created() {
    this.initData();
  }

  mounted() {
    this.setPageInfo();
    if (this.jumpPageInfo.activityRange) {
      this.activityRange = this.jumpPageInfo.activityRange as any;
      this.pageName = this.jumpPageInfo.activityRange === "all" ? this.i18n("全部活动") : this.jumpPageInfo.activityRange === "part" ?  this.i18n("指定活动") : '';
    }
  }

  initData() {
    this.JumpPageDatas = Data.data(this.advertiseChannel);
    this.activeSub = Data.data(this.advertiseChannel)[0].sub as Array<any>;
    Data.data(this.advertiseChannel).forEach((item) => {
      if (item.children) {
        this.$set(this.isOpened, item.type, false);
      }
    });

    if (!this.hasOptionPermission("/设置/小程序装修/页面管理", "查看")) {
      this.JumpPageDatas = this.JumpPageDatas.filter((item: any) => item.type !== PageVO.Page);
    }

    if(!(this.advertiseChannel?.length === 1 && this.advertiseChannel[0] === CmsConfigChannel.WEIXIN)) {
      const index = this.FeaturePageList.findIndex((item)=>item.name == this.i18n("微信支付"))
      if(index>=0){
        this.FeaturePageList.splice(index, 1)
      }
    }
  }

  async changeLinkDialog(flag: boolean) {
    this.clearFrom(false);
    await this.getList();
    this.setPageInfo();
    if (flag) {
      this.setJumpPageInfo();
      this.activityRange = this.jumpPageInfo.activityRange as any;
    }
    this.dialogShow = flag;
  }

  setPageInfo() {
    if (this.value?.firstLevelPage) {
      this.jumpPageInfo = JSON.parse(JSON.stringify(this.value));
    } else {
      let jumpPageInfo = new JumpPageInfo();
      this.stairIndex = 0;
      this.secondIndex = 0;
      this.jumpPageInfo = jumpPageInfo;
    }
  }

  clearFrom(isUpData: boolean) {
    this.radio = 0;
    this.sysPageChecked = false;
    this.h5Val = "";
    this.page.currentPage = 1;
    this.page.size = 20;
    this.page.total = 0;
    this.activityListRadio = "";
    this.activityList = [];
    const firstLevelPage = this.jumpPageInfo.firstLevelPage;
    this.jumpPageInfo = new JumpPageInfo();
    if(!isUpData){
          this.jumpPageInfo.firstLevelPage = firstLevelPage;
    }

    this.filter.state = "PROCESSING";
    this.filter.keyWords = null;
    this.filter.activeNo = null;
    this.featuresNameRadio = "";
    this.featurePageName = "";
    this.featurePageNameInput = '';
    this.customizationPageName = "";
    this.customizationPageNameRadio = ''

    if (isUpData) {
      this.$emit("change", this.jumpPageInfo);
    }
  }

  // 手动更新回显
  setJumpPageInfo() {
    if(!this.value){
      return;
    }

    if (this.transOldData()) {
      return
    }
    if (this.value?.firstLevelPage) {
      for (let i = 0; i < this.JumpPageDatas.length; i++) {
        const element = this.JumpPageDatas[i];
        let breakFlag = false;
        if (element.children && element.children.length > 0) {
          for (let j = 0; j < element.children.length; j++) {
            const child = element.children[j];
            if (child.name === this.value.firstLevelPage) {
              this.stairIndex = i;
              breakFlag = true;
              this.setSecondIndex(child.sub, this.value.secondLevelPage);
              this.activeSub = child.sub;
              this.activeChildType = child.type
              break;
            }
          }
        } else if (this.value?.firstLevelPage === element.name) {
          this.stairIndex = i;
          this.setSecondIndex(element.sub, this.value.secondLevelPage);
          this.activeSub = element.sub;
          break;
        }

        if (breakFlag) {
          break;
        }
      }

      if (this.value.firstLevelPage === this.i18n("页面")) {
        if (this.value.secondLevelPage === this.i18n("功能页面")) {
          this.featuresNameRadio = this.value.templateName;
        } else if (this.value.secondLevelPage === this.i18n("自定义页面")) {
            this.radio= this.value.templateId;
        }else if(this.value.secondLevelPage === this.formatI18n("/页面/页面管理/定制页面")){
          this.customizationPageNameRadio = this.value.templateName;
        }
      }

      if(this.value.secondLevelPage === this.i18n("活动详情")){
        this.queryActiveDetailList(true);
        this.activityListRadio = this.value.activityIds?.[0]
      }

      if(this.value.secondLevelPage === this.i18n('邀请有礼')) {
        this.sysPageChecked = true
      }else {
        this.sysPageChecked = false
      }
      if(this.value.secondLevelPage === this.i18n('异业合作')) {
        this.jumpPageInfo.h5Link = this.value.h5Link || ''
      }
    }
  }

  private setSecondIndex(subList: any, secondLevelPage: string) {
    if (subList && subList.length > 0) {
      for (let i = 0; i < subList.length; i++) {
        const element = subList[i];
        if (element.name === secondLevelPage) {
          this.secondIndex = i;
          break;
        }
      }
    }
  }

  /**
   * 获取列表数据
   */
  private getList() {
    this.ContentTemplateFilter.page = this.page.currentPage - 1;
    this.ContentTemplateFilter.pageSize = this.page.size;
    this.ContentTemplateFilter.stateEquals = ContentTemplateState.published;
    this.ContentTemplateFilter.channelContainsIn = this.advertiseChannel;
    return ContentTemplateApi.query(this.ContentTemplateFilter)
      .then((res) => {
        if (res.code === 2000) {
          this.ContentTemplate = res.data || [];
          this.page.total = res.total || 0;
        } else {
          this.$message.error(res.msg!);
        }
      })
      .catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
  }

  // 获取页面配置
  // getNavigationSetting() {
  //   NavigationSettingApi.get().then((res) => {
  //     if (res.data) {
  //       this.NavigationSetting = res.data;
  //     }
  //   });
  // }

  /**
   * 查询
   */
  doSearch() {
    this.page.currentPage = 1;
    if (this.activeSubItem.name === this.i18n("活动详情")) {
      this.queryActiveDetailList();
    } else if(this.activeSubItem.name === '功能页面'){
      this.featurePageName = this.featurePageNameInput
    }else {
      this.getList();
    }
  }

  /**
   * 重置
   */
  doReset() {
    if (this.activeSubItem.name === this.i18n("活动详情")) {
      this.filter.activeNo = null;
      this.filter.keyWords = null;
      this.filter.state = "PROCESSING";
      this.queryActiveDetailList();
    } else {
      this.ContentTemplateFilter = new ContentTemplateFilter();
      this.getList();
    }
  }
  get levelOneData() {
    return this.JumpPageDatas[this.stairIndex];
  }

  // 一级菜单点击
  onClickStair(item: any, index: any) {
    if (item.children) {
      this.isOpened[item.type] = !this.isOpened[item.type];
    } else {
      this.clearFrom(false);
      this.stairIndex = index;
      this.jumpPageInfo = new JumpPageInfo();
      this.jumpPageInfo.firstLevelPage = item.name;
      this.secondIndex = 0;
      this.activityRange = "all";
      this.activeChildType = null;
      this.activeSub = item.sub;
    }
  }

  // 一级菜单子节点点击
  onClickChildItem(item: any, index: number) {
    this.clearFrom(false);
    this.stairIndex = index;
    this.jumpPageInfo = new JumpPageInfo();
    this.jumpPageInfo.firstLevelPage = item.name;
    this.secondIndex = 0;
    this.activityRange = "all";
    this.activeChildType = item.type;
    this.activeSub = item.sub;

    //PHX-14700 抽锦鲤没有活动列表，所以当点击菜单时右侧直接加载活动详情列表
    if (item.name == this.i18n("抽锦鲤"))
      this.queryActiveDetailList(true);
  }

  // 二级菜单点击
  onClickSecond(item: any, index: any) {
    this.clearFrom(false);

    this.secondIndex = index;
    this.activityRange = "all";
    if (this.activeSubItem.name === this.i18n("活动详情")) {
      this.queryActiveDetailList(true);
    }else if(this.activeSubItem.name === this.i18n('自定义页面')){
      this.ContentTemplateFilter.nameLike =  null
      this.getList()
    }
  }

  // 过滤三级页为活动
  filterActivity() {
    // let name = '';
    if (
      this.levelOneData.name === this.i18n("/页面/页面管理/券活动") ||
      this.levelOneData.name === this.i18n("/页面/页面管理/卡活动") ||
      (this.levelOneData.name === this.i18n("/页面/页面管理/营销活动") && this.activeChildType !== PageVO.InvitedGift)
    ) {
      return this.activeSubItem.name === this.i18n("活动列表");
    } else {
      return false;
    }
  }

  // 过滤三级页为SysPage
  filterSysPage() {
    if (this.activeSubItem.name === this.i18n("/页面/页面管理/邀请有礼")) {
      return true;
    } else {
      return false;
    }

    // let arr = ["我的券列表", "充值有礼", "卡充值", "邀请有礼", "兑换码", "微信支付", "会员码", "储值付款码", "积分付款码", "微信会员卡"];
    // return arr.filter((item) => item === name).length > 0 ? true : false;
  }

  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val;
    if (this.activeSubItem.name === this.i18n("活动详情")) {
      this.queryActiveDetailList();
    } else {
      this.getList();
    }
  }

  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val;
    if (this.activeSubItem.name === this.i18n("活动详情")) {
      this.queryActiveDetailList();
    } else {
      this.getList();
    }
  }

  // 设置默认传出页面参数
  setParams() {
    if (this.levelOneData.name === this.i18n("页面")) {
      if (this.activeSubItem.name === this.i18n("功能页面")) {
        this.jumpPageInfo.templateName = this.featuresNameRadio;
        const find = this.FeaturePageList.find((item) => item.name === this.featuresNameRadio);
        this.jumpPageInfo.templateId = find ? find.templateId : "";
        this.jumpPageInfo.templateName = find?.name;
      }else if(this.activeSubItem.name === this.formatI18n("/页面/页面管理/定制页面")) {
        this.jumpPageInfo.templateName = this.customizationPageNameRadio;
        const find = this.customizationList.find((item) => item.name === this.customizationPageNameRadio);
        this.jumpPageInfo.templateId = find ? find.templateId : "";
        this.jumpPageInfo.templateName = find?.name;
      }
    }

    if (this.levelOneData.name === this.i18n("页面") || this.levelOneData.name === this.i18n("卡活动") || this.levelOneData.name === this.i18n("自定义链接") || this.levelOneData.name === this.i18n('异业合作')) {
      this.jumpPageInfo.firstLevelPage = this.levelOneData.name;
    } else {
      this.jumpPageInfo.firstLevelPage = this.levelOneData.children.find((item: any) => item.type === this.activeChildType)?.name; //.children.find((item)=>item.type ==== '')
    }
    if (this.activeSubItem.name === this.i18n('异业合作')) {
      this.jumpPageInfo.h5Link = this.jumpPageInfo.h5Link;
    }

    this.jumpPageInfo.secondLevelPage = this.activeSubItem.name;
    this.$emit("change", this.jumpPageInfo);
    this.$emit("input", this.jumpPageInfo);
    this.dialogShow = false;
  }

  // 确认选择
  doModalClose() {
    if (this.levelOneData.name === this.i18n("页面")) {
      // 为页面
      if (this.activeSubItem.name === this.i18n("功能页面")) {
        if (this.featuresNameRadio) {
          this.setParams();
        } else {
          this.$message({
            message: this.i18n("请选择"),
            type: "warning",
          });
        }
      } else  if(this.activeSubItem.name === this.formatI18n("/页面/页面管理/定制页面")){
        if (this.customizationPageNameRadio) {
          this.setParams();
        } else {
          this.$message({
            message: this.i18n("请选择"),
            type: "warning",
          });
        }
      }else{
        // 自定义页面
        if (this.radio) {
          this.setParams();
        } else {
          this.$message({
            message: this.i18n("请选择"),
            type: "warning",
          });
        }
      }

      this.jumpPageInfo.activityRange = null;
      this.jumpPageInfo.activityIds = [];
    } else if (this.filterActivity()) {
      // 为活动
      this.jumpPageInfo.activityRange = this.activityRange;
      if (this.jumpPageInfo.activityRange) {
        if (this.jumpPageInfo.activityRange === "all") {
          this.pageName = this.i18n("全部活动");
          this.jumpPageInfo.activityIds = [];
          this.radio = 0;
          this.setParams();
        } else {
          if (this.jumpPageInfo.activityIds.length > 0) {
            this.pageName = this.i18n("指定活动");
            this.radio = 0;
            this.setParams();
          } else {
            this.$message({
              message: this.i18n("请选择"),
              type: "warning",
            });
          }
        }
      } else {
        this.$message({
          message: this.i18n("请选择"),
          type: "warning",
        });
      }
    } else if (this.filterSysPage()) {
      // 为SysPage
      if (this.sysPageChecked) {
        this.setParams();
        this.jumpPageInfo.templateName = this.activeSubItem.name;
        this.jumpPageInfo.templateId = this.activeSubItem.templateId;
      } else {
        this.$message({
          message: this.i18n("请选择"),
          type: "warning",
        });
      }
    } else if (this.activeSubItem.name === this.i18n("H5链接")) {
      //自定义H5链接
      if (this.jumpPageInfo.h5Link) {
        this.setParams();
        const child = this.activeSubItem.children[0]
        this.jumpPageInfo.templateName = child.templateName
        this.jumpPageInfo.templateId = child.templateId
      } else {
        this.$message({
          message: this.i18n("请填写链接"),
          type: "warning",
        });
      }
    } else if (this.activeSubItem.name === this.i18n("小程序路径")) {
      //自定义小程序链接
      this.$refs["ruleForm"].validate((valid: any) => {
        if (valid) {
          this.setParams();
          this.jumpPageInfo.templateName = this.i18n("小程序路径")
          this.jumpPageInfo.templateId = ''
        }
      });
    } else if (this.activeSubItem.name === this.i18n("活动详情")) {
      if (this.activityListRadio) {
        this.setParams();
      } else {
        this.$message.warning(this.i18n("请选择"));
      }
    } else if (this.activeSubItem.name === this.i18n('异业合作')) {
      if (this.jumpPageInfo.h5Link) {
        this.setParams();
      } else {
        this.$message.warning(this.i18n("请选择"));
      }
    }

    if (!this.filterActivity()) {
      this.pageName = ''
    }

  }

  goUp() {
    this.$refs.childRef.open(this.jumpPageInfo.activityIds || []);
  }

  getActivityName() {
    if (this.jumpPageInfo.activityRange === "all") {
      return this.i18n("全部活动");
    } else if (this.jumpPageInfo.activityRange === "part") {
      return this.i18n("指定活动");
    }
  }
  doSubmit(val: string[]) {
    this.jumpPageInfo.activityIds = val || [];
  }

  handleChange() {
    if (this.activityRange == "all") {
      this.jumpPageInfo.activityIds = [];
    }
  }

  queryActiveDetailList(resetPage = false) {
    if (resetPage) {
      this.page.currentPage = 1;
      this.page.size = 10;
    }

    // 券活动
    if (this.levelOneData.name === this.i18n("券活动") || this.levelOneData.name === this.i18n("营销活动")) {
      const filter = new UnionActivityQuery();
      filter.page = this.page.currentPage - 1;
      filter.pageSize = this.page.size;

      filter.stateEquals = this.filter.state;
      filter.nameLike = this.filter.keyWords;
      // 积分兑换卷
      if (this.jumpPageInfo.firstLevelPage=== this.i18n("积分兑换券")) {
        filter.typeIn = ["PointsExchangeCouponActivityRule"];
      } else if (this.jumpPageInfo.firstLevelPage === this.i18n("小程序领微信券")) {
        // 小程序领微信券
        filter.typeIn = ["WeiXinAppletIssueCouponActivityRule"];
      } else if (this.jumpPageInfo.firstLevelPage === this.i18n("小程序领支付宝券")) {
        // 小程序领支付宝券
        filter.typeIn = ["AliAppletIssueCouponActivityRule"];
      } else if (this.jumpPageInfo.firstLevelPage === this.i18n("小程序领券")) {
        // 小程序领券
        filter.typeIn = ["MiniProgramGainCouponActivityRule"];
      } else if (this.jumpPageInfo.firstLevelPage === this.i18n("大转盘")) {
        // 大转盘
        filter.typeIn = ["BigWheelActivityRule"];
      } else if (this.jumpPageInfo.firstLevelPage === this.i18n("抽奖团")) {
        // 抽奖团
        filter.typeIn = ["GroupBookingActivityRule"];
      } else if (this.jumpPageInfo.firstLevelPage === this.i18n("邀请有礼")) {
        // 邀请有礼
        filter.typeIn = ["MemberInviteRegisterGiftActivityRule"];
      } else if (this.jumpPageInfo.firstLevelPage === this.i18n("抽锦鲤")) {
        // 抽锦鲤
        filter.typeIn = ["LuckyDrawActivityRule"];
      }

      if (this.filter.activeNo) {
        filter.numberEquals = this.filter.activeNo;
      }

      if (this.jumpPageInfo.firstLevelPage === this.i18n("集点活动")) {
        // 集点活动
        const pointsActivityFilter = new PointsActivityFilter();
        pointsActivityFilter.groupType = "CONSUME_GIFT" as unknown as ActivityGroupType;
        pointsActivityFilter.typeEquals = "COLLECT_POINTS_ACTIVITY";
        pointsActivityFilter.page = this.page.currentPage - 1;
        pointsActivityFilter.pageSize = this.page.size;

        pointsActivityFilter.stateEquals = this.filter.state;
        pointsActivityFilter.nameLike = this.filter.keyWords;

        if (this.filter.activeNo) {
          pointsActivityFilter.numberEquals = this.filter.activeNo;
        }
        this.activityListLoading = true;
        PointsActivityApi.query(pointsActivityFilter)
          .then((response) => {
            if (response && response.data && response.data.list) {
              this.activityList = response.data.list;
            } else {
              this.activityList = [];
            }
            this.page.total = response.total;
          })
          .finally(() => {
            this.activityListLoading = false;
          });
      } else {
        this.activityListLoading = true;
        UnionActivityApi.queryByType(filter)
          .then((response) => {
            if (response.data && response.data.list) {
              this.activityList = response.data.list;
            } else {
              this.activityList = [];
            }
            this.page.total = response.total;
          })
          .finally(() => {
            this.activityListLoading = false;
          });
      }
    } else if (this.levelOneData.name === this.i18n("卡活动")) {
      // 电子卡制卡活动
      const filter = new GiftCardActivityFilter();
      filter.page = this.page.currentPage - 1;
      filter.pageSize = this.page.size;
      filter.stateEquals = this.filter.state;
      filter.nameLikes = this.filter.keyWords;
      filter.activityIdLikes = this.filter.activeNo
      this.activityListLoading = true;
      GiftCardActivityApi.query(filter)
        .then((response) => {
          this.page.total = response.data?.total!;
          if (response.data && response.data.result) {
            this.activityList = response.data.result.map((item) => {
              return item.body;
            });
          } else {
            this.activityList = [];
          }
        })
        .finally(() => {
          this.activityListLoading = false;
        });
    }
  }

  activityListRadioChange(value: string) {
    if (value) {
      this.jumpPageInfo.activityIds = [value];
      const find = this.activityList.find((item) => item.activityId === value);
      if (find) {
        this.jumpPageInfo.templateName = find.name;

        if (this.jumpPageInfo.firstLevelPage === this.i18n('小程序领微信券') || this.jumpPageInfo.firstLevelPage === this.i18n('小程序领支付宝券')){
          if(find.batchNumber){
            this.jumpPageInfo.path = 'pages/coupon/CouponDetail'
          }else {
            this.jumpPageInfo.path = 'coupon/monthly-coupon-detail/MonthlyCouponDetail'
          }
        }

      } else {
        this.jumpPageInfo.templateName = "";
      }
    } else {
      this.jumpPageInfo.activityIds = [];
      this.jumpPageInfo.templateName = "";
    }
  }

  // 将旧的链接格式转换为新链接
  transOldData() {
    let transFlag = false // 是否触发了转换流程（触发了代表是旧链接）
    const linkInfo: JumpPageInfo = JSON.parse(JSON.stringify(this.value))
    // 自定义页面（装修页面）
    if (linkInfo.firstLevelPage === 'Page' && linkInfo.secondLevelPage === 'Page.DeliveryPage') {
      transFlag = true
      linkInfo.firstLevelPage = this.i18n('页面')
      linkInfo.secondLevelPage = this.i18n('自定义页面')
      linkInfo.templateName = this.i18n('页面')
    }
    // 小程序领券
    else if (linkInfo.firstLevelPage === 'Coupon' && linkInfo.secondLevelPage === 'Coupon.GetCoupon') {
      transFlag = true
      linkInfo.firstLevelPage = this.i18n('小程序领券')
      linkInfo.secondLevelPage = this.i18n('活动列表')
      linkInfo.templateName = linkInfo.activityRange === 'all' ? this.i18n('全部活动') : this.i18n('指定活动')
    }
    // 小程序领微信券
    else if (linkInfo.firstLevelPage === "Coupon" && linkInfo.secondLevelPage === "Coupon.GetWeiXinCoupon") {
      transFlag = true
      linkInfo.firstLevelPage = this.i18n('小程序领微信券')
      linkInfo.secondLevelPage = this.i18n('活动列表')
      linkInfo.templateName = linkInfo.activityRange === 'all' ? this.i18n('全部活动') : this.i18n('指定活动')
    }
    // 我的券列表
    else if (linkInfo.firstLevelPage === "Coupon" && linkInfo.secondLevelPage === "Coupon.MyCoupon") {
      transFlag = true
      linkInfo.firstLevelPage = this.i18n('页面')
      linkInfo.secondLevelPage = this.i18n('功能页面')
      linkInfo.templateName = this.i18n("我的优惠券")
    }
    // 积分兑换券活动
    else if (linkInfo.firstLevelPage === "Points" && linkInfo.secondLevelPage === "Points.ExchangeCoupon") {
      transFlag = true
      linkInfo.firstLevelPage = this.i18n("积分兑换券")
      linkInfo.secondLevelPage = this.i18n('活动列表')
      linkInfo.templateName = linkInfo.activityRange === 'all' ? this.i18n('全部活动') : this.i18n('指定活动')
    }
    // 充值有礼
    else if (linkInfo.firstLevelPage === "Prepay" && linkInfo.secondLevelPage === "Prepay.DepositGift") {
      transFlag = true
      linkInfo.firstLevelPage = this.i18n("页面")
      linkInfo.secondLevelPage = this.i18n("功能页面")
      linkInfo.templateName = this.i18n("充值有礼")
    }
    // 卡充值
    else if (linkInfo.firstLevelPage === "Card" && linkInfo.secondLevelPage === "Card.CardDeposit") {
      transFlag = true
      linkInfo.firstLevelPage = this.i18n("页面")
      linkInfo.secondLevelPage = this.i18n("功能页面")
      linkInfo.templateName = this.i18n("卡充值")
    }
    // 电子卡售卡活动
    else if (linkInfo.firstLevelPage === "Card" && linkInfo.secondLevelPage === "Card.GiftCardActivity") {
      transFlag = true
      linkInfo.firstLevelPage = this.i18n("卡活动")
      linkInfo.secondLevelPage = this.i18n('活动列表')
      linkInfo.templateName = linkInfo.activityRange === 'all' ? this.i18n('全部活动') : this.i18n('指定活动')
    }
    // 集点活动
    else if (linkInfo.firstLevelPage === "Promotion" && linkInfo.secondLevelPage === "Promotion.CollectPointsActivity") {
      transFlag = true
      linkInfo.firstLevelPage = this.i18n("集点活动")
      linkInfo.secondLevelPage = this.i18n('活动列表')
      linkInfo.templateName = linkInfo.activityRange === 'all' ? this.i18n('全部活动') : this.i18n('指定活动')
    }
    // 大转盘活动
    else if (linkInfo.firstLevelPage === "Promotion" && linkInfo.secondLevelPage === "Promotion.BigWheel") {
      transFlag = true
      linkInfo.firstLevelPage = this.i18n("大转盘")
      linkInfo.secondLevelPage = this.i18n('活动列表')
      linkInfo.templateName = linkInfo.activityRange === 'all' ? this.i18n('全部活动') : this.i18n('指定活动')
    }
    // 抽奖团活动
    else if (linkInfo.firstLevelPage === "Promotion" && linkInfo.secondLevelPage === "Promotion.GroupBooking") {
      transFlag = true
      linkInfo.firstLevelPage = this.i18n("抽奖团")
      linkInfo.secondLevelPage = this.i18n('活动列表')
      linkInfo.templateName = linkInfo.activityRange === 'all' ? this.i18n('全部活动') : this.i18n('指定活动')
    }
    // 邀请有礼
    else if (linkInfo.firstLevelPage === "Promotion" && linkInfo.secondLevelPage === "Promotion.InvitedGift") {
      transFlag = true
      linkInfo.firstLevelPage = this.i18n("邀请有礼")
      linkInfo.secondLevelPage = this.i18n("邀请有礼")
      linkInfo.templateName = this.i18n("邀请有礼")
    }
    // 兑换码
    else if (linkInfo.firstLevelPage === "Promotion" && linkInfo.secondLevelPage === "Promotion.ExchangeCode") {
      transFlag = true
      linkInfo.firstLevelPage = this.i18n("页面")
      linkInfo.secondLevelPage = this.i18n("功能页面")
      linkInfo.templateName = this.i18n("兑换码")
    }
    // 会员码
    else if (linkInfo.firstLevelPage === "MemberCode" && linkInfo.secondLevelPage === "MemberCode.MemberCodeQr") {
      transFlag = true
      linkInfo.firstLevelPage = this.i18n("页面")
      linkInfo.secondLevelPage = this.i18n("功能页面")
      linkInfo.templateName = this.i18n("会员码")
    }
    // 微信支付码
    else if (linkInfo.firstLevelPage === "MemberCode" && linkInfo.secondLevelPage === "MemberCode.WeiXinPay") {
      transFlag = true
      linkInfo.firstLevelPage = this.i18n("页面")
      linkInfo.secondLevelPage = this.i18n("功能页面")
      linkInfo.templateName = this.i18n("微信支付")
    }
    // 储值付款码
    else if (linkInfo.firstLevelPage === "MemberCode" && linkInfo.secondLevelPage === "MemberCode.PrepayPayCode") {
      transFlag = true
      linkInfo.firstLevelPage = this.i18n("页面")
      linkInfo.secondLevelPage = this.i18n("功能页面")
      linkInfo.templateName = this.i18n("储值付款码")
    }
    // 积分付款码
    else if (linkInfo.firstLevelPage === "MemberCode" && linkInfo.secondLevelPage === "MemberCode.PointsPayCode") {
      transFlag = true
      linkInfo.firstLevelPage = this.i18n("页面")
      linkInfo.secondLevelPage = this.i18n("功能页面")
      linkInfo.templateName = this.i18n("积分付款码")
    }
    // H5链接
    else if (linkInfo.firstLevelPage === "CustomLink" && linkInfo.secondLevelPage === "H5Link") {
      transFlag = true
      linkInfo.firstLevelPage = this.i18n("自定义链接")
      linkInfo.secondLevelPage = this.i18n("H5链接")
      linkInfo.templateName = this.i18n("H5链接页面")
    }
    // 小程序路径
    else if (linkInfo.firstLevelPage === "CustomLink" && linkInfo.secondLevelPage === "WeappLink") {
      transFlag = true
      linkInfo.firstLevelPage = this.i18n("自定义链接")
      linkInfo.secondLevelPage = this.i18n("小程序路径")
      linkInfo.templateName = this.i18n("小程序路径")
    }
    console.log('是否识别旧链接', transFlag, linkInfo);
    if (transFlag) {
      console.log('检测到旧链接，emit', linkInfo);
      this.$emit("change", linkInfo)
    }
    return transFlag
  }

  doCancel() {
    this.initData()
    this.dialogShow = false
  }
}
