import ApiClient from 'http/ApiClient'
import OperationalResp from 'model/precisionmarketing/tag/OperationalResp'
import Response from 'model/common/Response'
import TagTemplate from 'model/precisionmarketing/tag/TagTemplate'
import TagTemplateFilter from 'model/precisionmarketing/tag/TagTemplateFilter'
import TagTemplateSummary from 'model/precisionmarketing/tag/TagTemplateSummary'
import TagItem from 'model/precisionmarketing/tag/TagItem';
import TagSummary from 'model/precisionmarketing/tag/TagSummary';
import TagItemFilter from 'model/precisionmarketing/tag/TagItemFilter';
import TagTemplateLog from 'model/precisionmarketing/tag/TagTemplateLog';
import TagData from "model/precisionmarketing/tag/TagData";
import TagTemplateMetricsFilter from "model/precisionmarketing/tagv2/TagTemplateMetricsFilter";

export default class TagTemplateApi {


  /**
   * 标签分布汇总信息导出
   *
   */
  static tagTemplateMetricsExport(body: TagTemplateMetricsFilter): Promise<Response<void>> {
    return ApiClient.server()
      .post(`/v1/precision-marketing/tag-template/tagTemplateMetricsExport`, body, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 手动执行标签模板计算
   *
   */
  static executeManually(templateId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/precision-marketing/tag-template/executeManually/${templateId}`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询标签日志信息
   * 查询标签日志信息。
   *
   */
  static queryLog(tagTemplateUuid: string, page: number, pageSize: number): Promise<Response<TagTemplateLog[]>> {
    return ApiClient.server().post(`/v1/precision-marketing/tag-template/queryLog/${tagTemplateUuid}/${page}/${pageSize}`, {}, {
      }).then((res) => {
        return res.data
    })
  }

  /**
   * 获取标签分布汇总信息
   *
   */
  static queryTagItems(templateUuid: string, page: number, pageSize: number): Promise<Response<TagItem[]>> {
    return ApiClient.server().get(`/v1/precision-marketing/tag-template/queryTagItems/${templateUuid}/${page}/${pageSize}`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 获取标签分布汇总信息
   *
   */
  static getTagSummary(body: TagItemFilter): Promise<Response<TagSummary>> {
    return ApiClient.server().post(`/v1/precision-marketing/tag-template/getTagSummary`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 批量移动标签
   *
   */
  static batchMove(body: string[], categoryId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/precision-marketing/tag-template/batchMove/${categoryId}`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 批量禁用标签模板,传标签名称数据
   *
   */
  static batchDisable(body: string[]): Promise<Response<OperationalResp>> {
    return ApiClient.server().post(`/v1/precision-marketing/tag-template/batchDisable`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 批量启用标签模板,传标签名称数据
   *
   */
  static batchEnable(body: string[]): Promise<Response<OperationalResp>> {
    return ApiClient.server().post(`/v1/precision-marketing/tag-template/batchEnable`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 批量修改模板分类
   *
   */
  static batchUpdateCategory(body: string[], categoryId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/precision-marketing/tag-template/batchUpdateCategory/${categoryId}`, body, {}).then((res) => {
      return res.data
    })
  }


  /**
   * 根据标签模板名称删除标签模板
   *
   */
  static delete(tagTemplateName: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/precision-marketing/tag-template/delete`, {}, {
      params: {
        tagTemplateName: tagTemplateName
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据uuid查询标签模板信息
   *
   */
  static get(uuid: string): Promise<Response<TagTemplate>> {
    return ApiClient.server().get(`/v1/precision-marketing/tag-template/get/${uuid}`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询用户信息
   *
   */
  static listUser(): Promise<Response<string[]>> {
    return ApiClient.server().post(`/v1/precision-marketing/tag-template/listUser`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询标签模板
   *
   */
  static query(body: TagTemplateFilter): Promise<Response<TagTemplate[]>> {
    return ApiClient.server().post(`/v1/precision-marketing/tag-template/query`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 修改或保存标签模板，以uuid区分新建或修改,返回uuid
   *
   */
  static saveOrUpdate(body: TagTemplate): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/precision-marketing/tag-template/saveOrUpdate`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 标签模板查询汇总信息
   *
   */
  static summary(body: TagTemplateFilter): Promise<Response<TagTemplateSummary>> {
    return ApiClient.server().post(`/v1/precision-marketing/tag-template/summary`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 导出会员
   *
   */
  static exportTagMember(tagData: TagData): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/precision-marketing/tag-template/exportTagMember`, tagData, {}).then((res) => {
      return res.data
    })
  }

}
