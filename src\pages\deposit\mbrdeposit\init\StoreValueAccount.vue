<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:03
 * @LastEditTime: 2024-04-24 14:21:25
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\mbrdeposit\init\StoreValueAccount.vue
 * 记得注释
-->
<template>
  <div class="store-value-account">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button size="small" type="primary" v-if="data.initial && hasOptionPermission('/储值/储值管理/多储值账户', '账户信息维护')" @click="edit">{{i18n('立即完善')}}</el-button>
        <el-button size="small" type="primary" v-if="!data.initial && hasOptionPermission('/储值/储值管理/多储值账户', '账户信息维护')" @click="edit">{{i18n('编辑')}}</el-button>
      </template>
    </BreadCrume>
    <div style="overflow: auto;height: 95%">
      <div class="current-page">
        <el-table :data="data.accounts">
          <el-table-column fixed label="序" width="50">
            <template slot-scope="scope">
              {{scope.$index + 1}}
            </template>
          </el-table-column>
          <el-table-column fixed label="会员储值账户/账户类型" width="250">
            <template slot-scope="scope">
              {{scope.row.account | idName}}
            </template>
          </el-table-column>
          <el-table-column fixed label="适用范围" v-if="data.initial">
            <template>
              <el-row><span>适用商品：&nbsp;&nbsp;</span><span style="color: red">待完善</span></el-row>
              <el-row><span>适用门店：&nbsp;&nbsp;</span><span style="color: red">待完善</span></el-row>
            </template>
          </el-table-column>
          <el-table-column fixed label="适用范围" v-if="!data.initial">
            <template slot-scope="scope">
              <el-row class="line">
                <el-col :span="2" class="label">适用商品：</el-col>
                <el-col :span="16">
                  <GoodsScopeDtl no-i18n :goods="scope.row.useGoods" :goodsMatchRuleMode="goodsMatchRuleMode" :showAll="true"/>
                </el-col>
              </el-row>
              <el-row class="line">
                <el-col :span="2" class="label" style="line-height: 36px">适用门店：</el-col>
                <el-col :span="16">
                  <ActiveStoreDtl style="width: 60%;min-width: 800px;font-size:12px" no-i18n :data="scope.row.useStores"></ActiveStoreDtl>
                </el-col>
              </el-row>
              <el-row class="line">
                <el-col :span="2" class="label" style="line-height: 36px">{{i18n('/储值/预付卡/预付卡查询/列表页面/次数')}}：</el-col>
                <el-col :span="16">
                  {{scope.row.dailyPayTimes ? scope.row.dailyPayTimes + ' ' + i18n('/营销/券礼包活动/券礼包活动/次') : i18n('/公用/券模板/不限制')}}
                </el-col>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

  </div>
</template>

<script lang="ts" src="./StoreValueAccount.ts">
</script>

<style lang="scss">
  .store-value-account {
    width: 100%;
    height: 100%;
    background-color: white;
    overflow: hidden;
    .current-page {
      height: calc(100% - 10px);
      overflow: auto;
      padding: 20px;

      .el-table {
        td {
          border-bottom: 1px solid #ECEEF5;
        }

        .cell {
          .el-row {
            margin-top: 15px;

            .label {
              min-width: 120px;
              max-width: 120px;
            }
          }
        }
      }

      .line {
        display: flex;
        align-items: center;
      }
    }
  }
</style>