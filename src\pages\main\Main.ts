import { Component, Vue } from "vue-property-decorator";
import NavNew from "cmp/navnew/NavNew.vue";
import ChangePwd from "cmp/changepwd/ChangePwd.vue";
import DownloadCenterDialog from "pages/main/dowmload_center_dialog/DownloadCenterDialog.vue";
import I18nApi from "http/i18n/I18nApi";
import CommonUtil from "util/CommonUtil";
import { Action, State } from "vuex-class";
import ShortcutMgr from "mgr/ShortcutMgr";
import LoginApi from "http/login/LoginApi";
import BreadCrume from "cmp/bread-crumb/BreadCrume.vue";
import MarketingCenterApi from "http/marketingcenter/MarketingCenterApi";
import RSMarketingCenterFilter from "model/common/RSMarketingCenterFilter";
import RSMarketingCenter from "model/common/RSMarketingCenter";
import BrowserMgr, { LocalStorage } from "mgr/BrowserMgr";
import PermissionMgr from "mgr/PermissionMgr";
import store from "store/index";
import RenewMessageDialog from './renewMessageDialog/RenewMessageDialog.vue';
import UserLoginResult from "model/login/UserLoginResult";

@Component({
	components: {
		NavNew,
		ChangePwd,
		DownloadCenterDialog,
    RenewMessageDialog,
		BreadCrume,
	},
})
export default class Main extends Vue {
	@State("i18n") i18n: any;
	@Action("i18n") actionI18n: any;
  @State('token') token: any;
  @State("loginInfo") loginInfo: UserLoginResult;
	customer: any = "";
	flag: boolean = false;
	popoverFlag = false;
	showPwdDialog: boolean = false; // 显示修改弹框
	user: LoginUser = new LoginUser();
	$refs: any;
	dialogvisiable = false;
	count = 0;
	language = "";
	languageNames: any = [];
	languageFlag: boolean = false;
	refreshing: boolean = false;
	org: any = "";
	orgName: any = "";
	orgList: RSMarketingCenter[] = [];
	promotionCenter: Boolean = false;

  get projectVersion() {
    return 'V' + LocalStorage.getItem('version')
  }

	created() {
		if (!sessionStorage.getItem("locale")) {
			location.replace("/");
		}
		let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
		if (sysConfig) {
			this.promotionCenter = sysConfig.enableMultiMarketingCenter;
		}
		if (this.promotionCenter) {
			sessionStorage.setItem("isMultipleMC", "1");
		} else {
			sessionStorage.setItem("isMultipleMC", "0");
		}
		this.customer = localStorage.getItem("customer");
		this.getLanguageNames();
	}
	mounted() {
		this.getLocal();
		if (this.promotionCenter) {
			this.getOrgList();
		}
    // 登录后，展示续费消息弹窗
    if (sessionStorage.getItem("needRemindExpire") === "true" && this.loginInfo.expirationFunctionPackages?.length) {
      this.doMessageOpen()
      sessionStorage.setItem("needRemindExpire", "false")
    }
	}

  //如果是uni方式登录，则不允许修改密码
  get canModifyPWD() {
    return !this.token.accessToken
  }

	doBack() {
    let currentRoutePath = this.$router.currentRoute.fullPath
    if (currentRoutePath.indexOf('coupon-template') > -1 && currentRoutePath.indexOf('coupon-template-list') == -1) {  //检查是否需要指定地址回退
      return this.$router.push({ name: 'coupon-template-list' })
    }
		this.$router.back();
	}
	getOrg() {
		const org = sessionStorage.getItem("marketCenter");
		if (org) {
			this.org = org;
		}
	}
	getOrgList() {
		const account = JSON.parse(sessionStorage.getItem("vuex") as string)?.loginInfo.user?.account;

		MarketingCenterApi.queryUserMarketCenter(account).then((res) => {
			this.orgList = res.data || [];

			this.getOrg();
			const item: Nullable<RSMarketingCenter> = this.orgList ? this.orgList[0] : null;
			if (sessionStorage.getItem("marketCenter")) {
				this.org = sessionStorage.getItem("marketCenter");
				this.orgName = sessionStorage.getItem("marketCenterName");
			} else {
				this.org = item!.marketingCenter!.id;
				this.orgName = item!.marketingCenter!.name || "";
				sessionStorage.setItem("marketCenter", this.org);
				sessionStorage.setItem("marketCenterName", item!.marketingCenter!.name || "");
			}

			// this.orgList.forEach((item: RSMarketingCenter) => {
			// 	if (!this.org && item.marketingCenter!.id === "-") {
			// 		this.org = item.marketingCenter!.id;
			// 		this.orgName = item.marketingCenter!.name || "";
			// 		sessionStorage.setItem("marketCenter", this.org);
			// 		sessionStorage.setItem("marketCenterName", item.marketingCenter!.name || "");
			// 	}
			// });
		});
	}
	/**
	 * 获取缓存中用户信息
	 */
	getLocal() {
		let userInfo = localStorage.getItem("ucenterUser");
		if (userInfo) {
			this.user = JSON.parse(userInfo);
		}
	}
	doChangeLan() {
		sessionStorage.setItem("locale", this.language);
		I18nApi.gets(sessionStorage.getItem("locale") as any).then((resp: any) => {
			if (resp && resp.code === 2000) {
				this.actionI18n(resp.data);
				window.location.reload();
			}
		});
	}

	doChangeOrg(e: any) {
		sessionStorage.setItem("marketCenter", this.org);
		this.orgList.forEach((item: RSMarketingCenter) => {
			if (item.marketingCenter!.id === this.org) {
				this.orgName = item.marketingCenter!.name || "";
				sessionStorage.setItem("marketCenterName", item.marketingCenter!.name || "");
				sessionStorage.setItem("headquarters", item.headquarters? 'true': 'false');
			}
		});

		PermissionMgr.refreshPermission().then((menus: any[]) => {
			if (
				!menus ||
				menus.length === 0 || //
				!menus[0].children[0] ||
				menus[0].children[0].length === 0 || //
				!menus[0].children[0].children[0] ||
				menus[0].children[0].children[0].length === 0
			) {
				this.$router.push({ name: "home", query: { from: "login" } });
			} else {
				this.matchRoute(menus)
			}
    }).catch((error) => {
      this.$message.error(error.message)
    });
	}
	/**
	 * 切换多组织匹配路由
	 */
	matchRoute(menus: any[]) {
		const route = this.$route
		const routeList = route.path.split('/')
		let path = ''
		if (routeList.length > 0 && !routeList[0]) {
			routeList.splice(0, 1)
		}
		if (routeList.length > 0) {
			menus.map(item => {
				if (item.hash == routeList[0] && item.children && item.children.length > 0) {
					const childrenList = item.children.reduce((acc: any[], cur: any) => {
						if (cur.children && cur.children.length > 0) {
							acc = acc.concat(cur.children)
						}
						return acc
					}, [])
					const flag = childrenList.find((child: any) => child.hash == routeList[1])
					if (flag) {
						path = routeList[1]
					} else if (!flag && !path && routeList[2]) {
						const thirdFlag = childrenList.find((child: any) => child.hash == routeList[2])
						if (thirdFlag) {
							path = routeList[2]
						}
					}
				}
			})
		}
		if (path) {
			if (this.$route.name === path) {
				this.refresh()
			} else {
				this.$router.push({ name: path }).catch(((e) => {
					if (e.name === 'NavigationDuplicated') {
						this.$emit('refresh')
						this.refresh()
					}
				}))
			}
		} else {
			if (this.$route.path === "/member/home") {
				window.location.reload();
			}
			this.$router.push({ name: menus[0].children[0].children[0].hash, query: { from: "login" } });
		}
	}

	/**
	 * 修改密码
	 */
	openChangePwd() {
    // 如果是uni登录方式，则退出到uni页面修改密码
    if (this.token.accessToken) {
      //如果是uni登录方式，则直接退出到uni修改密码页面
      LoginApi.getUniHome().then((res) => {
        if (res.code === 2000 && res.data) {
          window.location.href = `${res.data}password`;
        }
      })
      return
    }
		this.showPwdDialog = true;
	}

	/**
	 * 修改后的回调
	 */
	changeCall(isSuccess: boolean) {
		this.showPwdDialog = false;
		if (isSuccess) {
			// 修改成功 延迟一下回到登录页 清空原用户缓存
			setTimeout(() => {
				this.$router.push({ name: "login" });
				localStorage.removeItem("ucenterUser");
			}, 800);
		}
	}
	doDownload() {
		this.dialogvisiable = true;
	}
	doDialogClose() {
		this.dialogvisiable = false;
	}
	/**
	 * 退出登录
	 * 退出登录
	 */
	onMenuClick() {
		this.$confirm(this.formatI18n("系统/上导航/点击退出/是否确认退出当前账户?"), this.formatI18n("系统/上导航/点击退出/弹框标题/退出登录"), {
			confirmButtonText: this.formatI18n("/公用/按钮", "确定"),
			cancelButtonText: this.formatI18n("/公用/按钮", "取消"),
		}).then(() => {
      if(this.token.accessToken) {
        //如果是uni登录方式，则直接退出到uni首页
        LoginApi.getUniHome().then((res) => {
          if (res.code === 2000 && res.data) {
            const href = res.data
            LoginApi.loginOut().then((resp: any) => {
              if (resp && resp.code === 2000) {
                store.commit('updateToken', {  //uni登录方式时，接入的token请求头
                  accessToken: '',
                  refreshToken: '',
                  temporaryToken: ''
                })
                ShortcutMgr.clearStorage();
                window.location.href = href;
              }
            })
          }
        })
      } else {
        LoginApi.loginOut()
          .then((resp: any) => {
            if (resp && resp.code === 2000) {
              ShortcutMgr.clearStorage();
              window.location.replace("/");
            }
          })
          .catch((error: any) => {
            if (error && error.message) {
              this.$message.error(error.message);
            }
          });
      }
		});
	}
	private getLanguageNames() {
		let that = this;
		I18nApi.language()
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					if (resp.data) {
						resp.data.forEach(function(item:any) {
							let name: any = {
								key: item.code,
								value: item.name ? item.name : item.code,
							};
							that.languageNames.push(name);
						});
					}
					if (this.languageNames && (this.languageNames.length === 0 || (this.languageNames.length === 1 && this.languageNames[0].key === "zh_CN"))) {
						this.languageFlag = false;
					} else {
						this.languageFlag = true;
					}
					// console.log(that.languageNames)
					this.language = CommonUtil.getLocale("locale") as any;
				}
			})
			.catch((error: any) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}
	private refresh() {
		this.refreshing = true;
		this.$nextTick(() => {
			this.refreshing = false;
		});
	}
  //打开续费提醒弹窗
  doMessageOpen() {
    if (!this.loginInfo.expirationFunctionPackages?.length) {
      return this.$message.warning(this.formatI18n('/公用/系统/续费提醒/暂无消息提醒'))
    }
    this.$refs.renewMessageDialog.open()
  }
}

// 存储的用户信息模型
class LoginUser {
	id: string = "";
	loginName: string = "";
	mobile: Nullable<string> = null;
	name: string = "";
	state: string = "using";
	superAdmin: boolean = false;
	superStoreManager: boolean = false;
	tenantId: string = "";
}
