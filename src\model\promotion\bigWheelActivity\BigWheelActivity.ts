/*
 * @Author: 黎钰龙
 * @Date: 2025-05-28 14:11:43
 * @LastEditTime: 2025-06-11 15:51:41
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\promotion\bigWheelActivity\BigWheelActivity.ts
 * 记得注释
 */
import BaseCouponActivity from "model/v2/coupon/BaseCouponActivity"
import BigWheelPrize from "./BigWheelPrize"
import BigWheelPrizeSetting from "./BigWheelPrizeSetting"
import PushGroup from "model/precisionmarketing/pushplan/PushGroup";
import { RaffleTaskSettingType } from "./RaffleTaskSettingType";
import NumberCalculatorItem from "./NumberCalculatorItem";
import { ConsumeGainTimeRuleType } from "./ConsumeGainTimeRuleType";
import GoodsRange from "model/common/GoodsRange";

export default class BigWheelActivity extends BaseCouponActivity {
  // 是否屏蔽黑名单
  blockBlacklist: Nullable<boolean> = null
  // 黑名单组id
  blacklistGroupId: Nullable<string> = null
  // 消耗多少积分兑换一次抽奖次数
  usePoints: Nullable<number> = null
  // 最大兑换参与次数
  maxExchangeTakePartNumber: Nullable<number> = null
  // 奖品设置
  prizes: BigWheelPrize[] = []
  // 抽奖配置
  prizeSettings: BigWheelPrizeSetting[] = []
  // 活动图片
  image: Nullable<string> = null
  // 客群
  rule: Nullable<PushGroup> = null
  // C端抽奖方式  EXCHANGE-兑换次数再抽奖   DIRECT-直接抽奖
  raffleType: Nullable<string> = null
  // 每人初始抽奖次数
  initRaffleTimes: Nullable<number> = null
  // 计算规则
  consumeGainTimeRule: NumberCalculatorItem[] = []
  // 每人累计中奖限制
  maxWinningTimes: Nullable<number> = null
  // 抽奖任务设置
  raffleTaskSettingTypes: RaffleTaskSettingType[] = []
  // 消费获得次数计算规则
  consumeGainTimeRuleType: Nullable<ConsumeGainTimeRuleType> = null
  // 商品范围信息
  goodsRange: Nullable<GoodsRange> = null
  // 转盘数量
  turntableCount: Nullable<number> = null
}