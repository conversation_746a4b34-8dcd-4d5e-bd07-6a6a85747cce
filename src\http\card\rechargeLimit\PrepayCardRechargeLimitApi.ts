/*
 * @Author: 黎钰龙
 * @Date: 2024-02-28 09:51:57
 * @LastEditTime: 2024-02-28 17:14:54
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\card\rechargeLimit\PrepayCardRechargeLimitApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import BPrepayCardRechargeLimit from 'model/prepay/rechargeLimit/BPrepayCardRechargeLimit'
import BPrepayCardRechargeLimitCreateRequest from 'model/prepay/rechargeLimit/BPrepayCardRechargeLimitCreateRequest'

export default class PrepayCardRechargeLimitApi {
  /**
   * 查询卡单次充值额度限制
   * 查询卡单次充值额度限制。
   * 
   */
  static query(): Promise<Response<BPrepayCardRechargeLimit[]>> {
    return ApiClient.server().post(`/v1/card/recharge/limit/query`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存卡单次充值额度限制
   * 保存卡单次充值额度限制。
   * 
   */
  static saveNew(body: BPrepayCardRechargeLimitCreateRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/card/recharge/limit/saveNew`, body, {
    }).then((res) => {
      return res.data
    })
  }

}