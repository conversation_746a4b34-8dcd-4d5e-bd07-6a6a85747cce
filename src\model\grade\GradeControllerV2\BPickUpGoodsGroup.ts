import PickUpGoods from './PickUpGoods'
import { Type } from './Type'

export default class BPickUpGoodsGroup {
  /**
   * 类型
   * single——单品，category——按品类，brandAndCategory——按品牌、品类
   */
  type: Nullable<Type> = null
  // 兑换商品组
  exchangeGoods: PickUpGoods[] = []
  // 兑换数量，默认为1
  exchangeQty: Nullable<number> = null
  // 商品组序号
  sequence: Nullable<number> = null
  // 是否允许兑换相同商品
  enableExchangeSameGood: Nullable<boolean> = null
}