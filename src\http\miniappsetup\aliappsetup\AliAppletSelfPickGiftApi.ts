/*
 * @Author: 黎钰龙
 * @Date: 2023-12-07 13:46:42
 * @LastEditTime: 2024-01-08 09:42:12
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\miniappsetup\aliappsetup\AliAppletSelfPickGiftApi.ts
 * 记得注释
 */
import Response from 'model/common/Response'
import ApiClient from 'http/ApiClient'
import SelfPickGift from 'model/miniappsetup/aliappsetup/SelfPickGift'
import SaveSelfPickGiftRequest from 'model/miniappsetup/aliappsetup/SaveSelfPickGiftRequest'
import IdName from 'model/common/IdName'
import ChoseSelfPickGiftActivities from 'model/miniappsetup/aliappsetup/ChoseSelfPickGiftActivities'

export default class AliAppletSelfPickGiftApi {
  /**
   * 查询
   * 查询。
   * 
   */
  static query(): Promise<Response<SelfPickGift[]>> {
    return ApiClient.server().post(`/v1/applet/selfPickGift/query`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存
   * 保存。
   * 
   */
  static save(body: SaveSelfPickGiftRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/applet/selfPickGift/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 已设置的投放活动查询
   * 已设置的投放活动查询。
   * 
   */
  static listActivities(): Promise<Response<IdName[]>> {
    return ApiClient.server().get(`/v1/applet/selfPickGift/listActivities`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
 * 保存投放活动
 * 保存投放活动。
 * 
 */
  static choseActivities(body: ChoseSelfPickGiftActivities): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/applet/selfPickGift/choseActivities`, body, {
    }).then((res) => {
      return res.data
    })
  }

}