import AddPaymentCmp from 'cmp/add-payment-cmp/AddPaymentCmp';
import FormItem from 'cmp/formitem/FormItem';
import SelectClient from 'cmp/selectclient/SelectClient';
import SelectStores from 'cmp/selectStores/SelectStores';
import I18nPage from 'common/I18nDecorator';
import BCardDepositBillImportRequest from 'model/card/depositbill/BCardDepositBillImportRequest';
import Payment from 'model/common/Payment';
import EnvUtil from 'util/EnvUtil';
import { Component, Prop, Vue } from 'vue-property-decorator';
import UploadApi from "http/upload/UploadApi";

class Form {
  rechargeType: 'DEPOSIT' | 'REFUND' = 'DEPOSIT'  //充值类型
  total: Nullable<number> = null  //优惠总金额
  payPrice: Nullable<number> = null  //应付金额
  payments: Payment[] = [] //支付方式
  customer: string = '' //客户id
  orgId: string = ''  //发生组织id
  remark: string = ''  //说明
}

@Component({
  name: 'PrepayCardImportDialog',
  components: {
    AddPaymentCmp,
    SelectClient,
    SelectStores,
    FormItem
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/储值/预付卡/预付卡充值单'
  ],
  auto: true
})
export default class PrepayCardImportDialog extends Vue {
  $refs: any
  @Prop({ type: Boolean, default: false, }) clientRequired: boolean; // 控制客户是否必填
  dialogShow: boolean = false
  ruleForm: Form = new Form()
  uploadHeaders: any = {};
  importUrl = 'v1/card-deposit-bill/importExcel' // 导入文件
  fileList: any[] = [];
  billNumber: string = '' //编辑状态有该字段
  params: any = null

  // 控制结果模态框的展示
  get getUploadUrl() {
    let url = EnvUtil.getServiceUrl() + this.importUrl
    return url
  }

  get templatePath() {
    if (location.href.indexOf('localhost') === -1) {
      return 'template_card_deposit.xlsx'
    } else {
      return 'template_card_deposit.xlsx'
    }
  }

  // 所有支付方式支付金额之和=应退(付)金额
  get amountEqual() {
    const payTypesSum: number = this.ruleForm.payments.reduce((prev, cur) => {
      return prev + Number(cur.paymentAmount)
    }, 0)
    return payTypesSum === Number(this.ruleForm.payPrice)
  }

  get rules() {
    return {
      rechargeType: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
      payPrice: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
      customer: [{ required: this.clientRequired, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
      orgId: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
    }
  }

  created() {
    let locale = sessionStorage.getItem("locale");
    this.uploadHeaders = {
      locale: locale ? locale : "zh_CN",
      time_zone: new Date().getTimezoneOffset(),
      marketingCenter: sessionStorage.getItem("marketCenter"),
    };
    const authorization = EnvUtil.getUniAuthorization()
    if (authorization) {
      this.uploadHeaders.authorization = authorization
    }
  }

  doModalClose(type: string) {
    if (type === "confirm") {
      const pArr = []
      pArr.push(this.$refs.form.validate())
      pArr.push(this.$refs.addPaymentCmp.doValidate())
      if (!this.amountEqual) {
        const msg = this.ruleForm.rechargeType === 'DEPOSIT' ? this.i18n('支付方式金额之和不等于应付金额') : this.i18n('支付方式金额之和不等于应退金额')
        return this.$message.warning(msg)
      }
      Promise.all(pArr).then(() => {
        this.params = this.doParams()
        this.$forceUpdate()
        this.$nextTick(() => {
          this.$refs.upload.submit();
        })
      })
    } else {
      this.dialogShow = false
      this.ruleForm = new Form()
      this.billNumber = ''
      this.params = null
      this.$refs.upload.clearFiles();
    }
  }

  doParams() {
    const res = new BCardDepositBillImportRequest()
    res.amount = this.ruleForm.payPrice
    res.billNumber = this.billNumber || null
    res.clientId = this.ruleForm.customer || null
    res.depositType = this.ruleForm.rechargeType
    res.favAmount = this.ruleForm.total || null
    res.orgId = this.ruleForm.orgId
    res.payTypes = this.ruleForm.payments || []
    res.remark = this.ruleForm.remark || null
    return { importRequest: JSON.stringify(res) }
  }

  open(number?: string, value?: BCardDepositBillImportRequest) {
    this.dialogShow = true
    if (number && value) {
      this.billNumber = number
      this.bindValue(value)
    }
  }

  bindValue(value: BCardDepositBillImportRequest) {
    this.ruleForm.customer = value.clientId || ''
    this.ruleForm.orgId = value.orgId || ''
    this.ruleForm.payPrice = value.amount
    this.ruleForm.payments = value.payTypes || []
    this.ruleForm.rechargeType = value.depositType as any
    this.ruleForm.remark = value.remark || ''
    this.ruleForm.total = value.favAmount
  }

  getErrorInfo(a: any, b: any, c: any) {
    this.$message.error(this.formatI18n("/公用/导入", "导入失败，请重新导入") as string);
    this.$refs.upload.clearFiles();
  }

  getSuccessInfo(a: any, b: any, c: any) {
    if (a && a.code === 2000) {
      this.$refs.upload.clearFiles();
      this.dialogShow = false
      this.$message.success(this.i18n('导入成功'))
      this.$emit("dialogClose");
      this.$emit("uploadSuccess", b);
    } else {
      this.$message.error(a.msg);
      this.$refs.upload.clearFiles();
    }
  }

  downloadTemplate() {
    UploadApi.getUrl(this.templatePath).then((resp: any) => {
      if (resp && resp.data) {
        window.open(resp.data);
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
};