<template>
    <div class="coupon-template-wrap">
        <!-- 这个选券类型的按钮，目前只是为了适配《营销》-《微信领券》-《微信扫码领券》的券模板部分表单 -->
        <FormItem class="coupon-type" v-if="showTypeRadio" :label="formatI18n('/营销/券礼包活动/核销第三方券', '券类型')" style="padding-bottom: 15px">
            <div style="line-height: 36px">
                <div v-if="copyFlag !== 'add'">{{couponType | lineCouponType}}</div>
                <el-radio-group v-else @change="doTabChange" v-model="couponType">
                    <el-radio label="all_cash" v-if="getCouponType('all_cash')">{{formatI18n('/公用/券模板', '现金券')}}</el-radio>
                    <el-radio label="all_discount" v-if="getCouponType('all_discount')">{{formatI18n('/公用/券模板', '折扣券')}}</el-radio>
                    <el-radio label="exchange_goods" v-if="getCouponType('exchange_goods')">{{formatI18n('/公用/券模板', '兑换券')}}</el-radio>
                    <el-radio label="special_price" v-if="getCouponType('special_price')">{{formatI18n('/公用/券模板', '特价券')}}</el-radio>
                    <el-radio label="goods" v-if="getCouponType('goods')">{{formatI18n('/公用/券模板', '提货券')}}</el-radio>
                    <el-radio label="freight" v-if="getCouponType('freight')">{{formatI18n('/公用/券模板', '运费券')}}</el-radio>
                    <el-radio label="random_cash" v-if="getCouponType('random_cash')">{{formatI18n('/公用/券模板', '随机金额券')}}</el-radio>
                    <el-radio label="points" v-if="getCouponType('points')">{{formatI18n('/公用/券模板', '积分券')}}</el-radio>
                    <el-radio label="equity" v-if="getCouponType('equity')">{{formatI18n('/公用/券模板', '权益券')}}</el-radio>
                </el-radio-group>
            </div>
        </FormItem>

        <div>
            <CashCoupon
                v-if="['all_cash', 'goods_cash'].includes(couponType) && isShow"
                ref="allCashCoupon"
                v-model="data"
                @change="doAllCashChange"
                :baseSettingFlag="baseSettingFlag"
                :remarkMaxlength="remarkMaxlength"
                :baseFieldEditable="baseFieldEditable"
                :weixinCouponHideFiled="weixinCouponHideFiled"
                :isPmsPayEngine="isPmsPayEngine"
                :options="options"
                :copyFlag="copyFlag"
                :sameStore="sameStore"
                :channels="channels"
                :wxScanForCoupon="wxScanForCoupon"
                :state="state"
                :hideOptions="hideOptions"
                :enableStore="enableStore"
                @changeShow="changeShow">
            </CashCoupon>
            <EquityCoupon
                v-if="['equity'].includes(couponType) && isShow"
                ref="EquityCoupon"
                v-model="data"
                @change="doEquityChange"
                :baseSettingFlag="baseSettingFlag"
                :remarkMaxlength="remarkMaxlength"
                :baseFieldEditable="baseFieldEditable"
                :options="options"
                :copyFlag="copyFlag"
                :sameStore="sameStore"
                :channels="channels"
                :wxScanForCoupon="wxScanForCoupon"
                :state="state"
                :enableStore="enableStore"
                @changeShow="changeShow">
            </EquityCoupon>
            <RandomCashCoupon
                v-if="['random_cash'].includes(couponType) && isShow"
                ref="RandomCashCoupon"
                v-model="data"
                @change="doRandomCashChange"
                :baseSettingFlag="baseSettingFlag"
                :remarkMaxlength="remarkMaxlength"
                :baseFieldEditable="baseFieldEditable"
                :options="options"
                :copyFlag="copyFlag"
                :sameStore="sameStore"
                :channels="channels"
                :wxScanForCoupon="wxScanForCoupon"
                :state="state"
                :enableStore="enableStore"
                @changeShow="changeShow">
            </RandomCashCoupon>
            <!-- 合并至CashCoupon一起展示 -->
            <!-- <GoodsCashCoupon
                v-if="couponType === 'goods_cash'"
                v-model="data"
                @change="doGoodsCashChange"
                :options="options"
                :baseFieldEditable="baseFieldEditable"
                :baseSettingFlag="baseSettingFlag"
                :sameStore="sameStore"
                :copyFlag="copyFlag"
                :remarkMaxlength="remarkMaxlength"
                :state="state"
                :channels="channels"
                :wxScanForCoupon="wxScanForCoupon"
                ref="goodsCashCoupon">
            </GoodsCashCoupon> -->
            <DiscountCoupon
                v-if="['all_discount', 'goods_discount', 'rfm_type'].includes(couponType) && isShow"
                v-model="data"
                :baseFieldEditable="baseFieldEditable"
                :weixinCouponHideFiled="weixinCouponHideFiled"
                :isPmsPayEngine="isPmsPayEngine"
                @change="doAllDiscountChange"
                :baseSettingFlag="baseSettingFlag"
                :sameStore="sameStore"
                :options="options"
                :remarkMaxlength="remarkMaxlength"
                :copyFlag="copyFlag"
                :state="state"
                :channels="channels"
                :wxScanForCoupon="wxScanForCoupon"
                :hideOptions="hideOptions"
                :enableStore="enableStore"
                ref="allDiscontCoupon">
            </DiscountCoupon>
            <SpecialCoupon
                v-if="'special_price' === couponType"
                ref="specialCoupon"
                v-model="data"
                @change="doSpecialChange"
                :baseSettingFlag="baseSettingFlag"
                :remarkMaxlength="remarkMaxlength"
                :baseFieldEditable="baseFieldEditable"
                :isPmsPayEngine="isPmsPayEngine"
                :options="options"
                :copyFlag="copyFlag"
                :sameStore="sameStore"
                :channels="channels"
                :wxScanForCoupon="wxScanForCoupon"
                :enableStore="enableStore"
                :state="state">
            </SpecialCoupon>
            <PickUpCoupon v-if="couponType === 'goods'"
                          v-model="data"
                          @change="doPickUpChange"
                          :remarkMaxlength="remarkMaxlength"
                          :baseSettingFlag="baseSettingFlag"
                          :sameStore="sameStore"
                          :copyFlag="copyFlag"
                          :state="state"
                          :channels="channels"
                          :enableStore="enableStore"
                          ref="pickUpCoupon">
            </PickUpCoupon>
          <FreightCoupon
              v-if="couponType === 'freight'"
              ref="freightCoupon"
              v-model="data"
              @change="doFreightChange"
              :baseSettingFlag="baseSettingFlag"
              :remarkMaxlength="remarkMaxlength"
              :baseFieldEditable="baseFieldEditable"
              :options="options"
              :copyFlag="copyFlag"
              :sameStore="sameStore"
              :channels="channels"
              :enableStore="enableStore"
              :state="state">
          </FreightCoupon>
          <ExchangeGoodsCoupon
                v-if="couponType === 'exchange_goods'"
                ref="exchangeGoodsCoupon"
                v-model="data"
                @change="doExchangeGoodsChange"
                :baseSettingFlag="baseSettingFlag"
                :isPmsPayEngine="isPmsPayEngine"
                :remarkMaxlength="remarkMaxlength"
                :baseFieldEditable="baseFieldEditable"
                :copyFlag="copyFlag"
                :sameStore="sameStore"
                :channels="channels"
                :enableStore="enableStore"
                :state="state">
            </ExchangeGoodsCoupon>
            <PointCoupon
                v-if="couponType === 'points'"
                ref="pointCoupon"
                v-model="data"
                @change="doPointCouponChange"
                :baseSettingFlag="baseSettingFlag"
                :remarkMaxlength="remarkMaxlength"
                :baseFieldEditable="baseFieldEditable"
                :copyFlag="copyFlag"
                :sameStore="sameStore"
                :channels="channels"
                :enableStore="enableStore"
                :state="state">
            </PointCoupon>
        </div>

    </div>
</template>

<script lang="ts" src="./CouponTemplateWrap.ts">
</script>

<style lang="scss">
.coupon-template-wrap{
    .qf-form-item .qf-form-label{
        width: 120px !important;
        padding-right: 12px !important;
        color: #354052;

    }
    .qf-form-item .qf-form-content{
        margin-left: 120px !important;
    }
    .baseSettingFlag{
      font-size: 16px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #242633;
    }
    .coupon-type{
        .qf-form-label{
            &:before{
                content: '*';
                color: #EF393F;
                margin-right: 4px;
            }
        }
    }
    .el-radio__input.is-checked+.el-radio__label {
      color: #242633;
    }
    .el-radio__label {
      font-size: 14px;
    }
}
</style>