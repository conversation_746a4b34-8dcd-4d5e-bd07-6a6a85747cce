/*
 * @Author: 黎钰龙
 * @Date: 2023-09-01 16:20:29
 * @LastEditTime: 2023-09-01 16:21:41
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\config\PointsConfigApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import PointsConfig from 'model/points/init/PointsConfig'
import Response from 'model/common/Response'

export default class PointsConfigApi {
  /**
   * 查询积分配置
   */
  static getCommonConfig(): Promise<Response<PointsConfig>> {
    return ApiClient.server().post(`/v1/points-config/getCommonConfig`, {}, {
    }).then((res) => {
      return res.data
    })
  }

}
