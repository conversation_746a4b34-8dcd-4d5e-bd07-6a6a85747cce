import I18nPage from "common/I18nDecorator";
import { Component, Prop, Watch, Vue } from 'vue-property-decorator'
import IdName from 'model/common/IdName'
import BWeimobOrgUseRule from 'model/common/weimob/BWeimobOrgUseRule'
import QueryOrgRequest from 'model/common/weimob/BQueryOrgRequest'
import WeiMobCloudOrg from 'model/common/weimob/WeiMobCloudOrg'
import ListWrapper from 'cmp/list/ListWrapper.vue'
import ApiClient from "http/ApiClient";

@Component({
  components: { ListWrapper }
})
@I18nPage({
  prefix: ["/公用/券模板", '/公用/券模板/微盟适用商品', '/会员/洞察/公共/操作符'],
  auto: true
})
export default class OrgsWeiMenEx extends Vue {
  $refs: any;
  @Prop({
    default: () => { return {} }
  })
  orgUseRule: any
  dialogShow: boolean = false; // 控制模态框的展示
  showExcludeGoods: boolean = false
  limitedOrgsType: string = 'brand'
  includeIds: IdName[] = []
  org: string = ''
  weimobOrgs: WeiMobCloudOrg[] = [];
  searchType: number = 1;
  searchValue: string = '';
  searchTypeList = [
    { value: 1, label: this.i18n("名称") }
    // { value: 2, label: this.i18n("组织ID") }
  ]
  // 用户勾选的记录
  selectedArr: IdName[] = []
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  checkboxList: boolean[] = []

  @Watch("orgUseRule", {
    deep: true
  })
  onValueChange(value: BWeimobOrgUseRule) {
    this.doBindValue(value)
  }

  search() {
    this.queryOrgList()
    this.$forceUpdate()
  }

  doChangeTab(type: string) {
    this.limitedOrgsType = type
    this.queryOrgList()
    this.$forceUpdate()
  }

  addCheckboxList() {
    this.weimobOrgs?.forEach((item: WeiMobCloudOrg, index: number) => {
      if (item && this.isSelect(item.vid)) {
        this.checkboxList[index] = true;
      }else{
        this.checkboxList[index] = false;
      }
    })
  }

  isSelect(vid: Nullable<string>) {
    return this.selectedArr.some(item => (item.id && item.id == vid));
  }

  doBindValue(value: BWeimobOrgUseRule) {
    if (value) {
      //绑定已选数据
      this.limitedOrgsType = (value && value.limitedOrgsType) ? value.limitedOrgsType : 'brand';
      this.queryOrgList();
      if (value.limitedOrgsType && value.limitedOrgsType == 'org') {
        if (value.includeOrgIds && value.includeOrgIds.length > 0) {
          value.includeOrgIds.forEach(item => {
            this.selectedArr = this.selectedArr.filter(e => e.id != item.id);
            this.selectedArr.push(item);
          })
        }
      } else if (value.limitedOrgsType && value.limitedOrgsType == 'brand') {
        if (value.includeBrandIds && value.includeBrandIds.length > 0) {
          value.includeBrandIds.forEach(item => {
            this.selectedArr = this.selectedArr.filter(e => e.id != item.id);
            this.selectedArr.push(item);
          })
        }
      }
      this.addCheckboxList();
    }
    this.$forceUpdate();
  }

  doCancel() {
    this.selectedArr = [];
    this.checkboxList = [];
    this.dialogShow = false
  }

  doClosed() {
    this.selectedArr = [];
    this.checkboxList = [];
  }

  doConfirm() {
    let params = new BWeimobOrgUseRule();
    if (this.selectedArr && this.selectedArr.length > 0) {
      params.limitedOrgsType = this.limitedOrgsType;
      if (this.limitedOrgsType == "org") {
        this.selectedArr.forEach(element => {
          params.includeOrgIds.push(element);
        });
      } else {
        this.selectedArr.forEach(element => {
          params.includeBrandIds.push(element);
        });
      }
    }
    this.$emit('change', params)
    this.dialogShow = false
  }

  delItem(id: String) {
    this.selectedArr = this.selectedArr.filter(e => e.id != id);
    this.addCheckboxList();
  }

  delAll() {
    this.selectedArr = [];
    this.checkboxList.forEach((item,index) => {
      this.$set(this.checkboxList, index, false)
    });
  }

  doCheck(event: boolean, index: number) {
    if (event) {
      this.selectedArr = this.selectedArr.filter(item => (item.id && item.id != this.weimobOrgs[index].vid));
      let org = new IdName();
      org.id = this.weimobOrgs[index].vid;
      org.name = this.weimobOrgs[index].vidName;
      this.selectedArr.push(org);
    } else {
      this.selectedArr = this.selectedArr.filter(item => (item.id && item.id != this.weimobOrgs[index].vid));
      //this.selectedArr.splice(index, 1)
    }
  }

  doCheckRow(index: number) {
    this.doCheck(!this.isSelect(this.weimobOrgs[index].vid), index);
    this.addCheckboxList();
    this.$forceUpdate();
  }

  doCheckAll(event: boolean) {
    this.weimobOrgs.forEach((item: any, index: number) => {
      this.checkboxList[index] = event;
      this.doCheck(event, index)
    });
  }

  /**
  * 每页多少条的回调
  * @param val
  */
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.queryOrgList()
  }

  /**
* 分页页码改变的回调
* @param val
*/
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.queryOrgList()
  }

  open(rule: BWeimobOrgUseRule) {
    this.selectedArr = [];
    this.dialogShow = true;
    this.doBindValue(rule);
  }


  async queryOrgList() {
    const params = new QueryOrgRequest()
    if (this.limitedOrgsType == 'org') {
      params.vidType = 10 // 查门店
    } else {
      params.vidType = 2 // 查品牌
    }
    if (this.searchValue) {
      params.vidName = this.searchValue
    }
    params.isDirect = 0
    params.pageNum = this.page.currentPage
    params.pageSize = this.page.size
    try {
      ApiClient.server().post(`/v1/coupon-template/weimob/org/list`, params, {
      }).then((res) => {
        this.page.total = res.data.total;
        this.weimobOrgs = res.data.data || [];
        this.checkboxList.length = this.weimobOrgs.length
        this.checkboxList.fill(false)
        this.addCheckboxList();
      })
    } catch (error) {
      this.$message.error((error as Error).message);
    }
  }

}
