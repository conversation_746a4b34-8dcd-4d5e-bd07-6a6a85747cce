/*
 * @Author: 黎钰龙
 * @Date: 2024-01-24 14:07:05
 * @LastEditTime: 2025-05-14 14:17:23
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\couponTemplateDtlItem\useCouponRecordMethod\UseCouponRecordMethod.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import CouponItem from 'model/common/CouponItem';
import { Component, Prop, Vue } from 'vue-property-decorator';
import SpecialGoodsDialog from 'cmp/coupontenplate/cmp/specialGoodsDialog.vue'
import RSCostParty from "model/common/RSCostParty";
import Tools from 'util/Tools';
@Component({
  name: 'UseCouponRecordMethod',
  components: {
    SpecialGoodsDialog,
  },
})

@I18nPage({
  prefix: [
    "/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡",
    '/公用/券模板详情',
    '/公用/券模板',
    "/公用/菜单",
    '/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送券的时候/点击券名称/用券记录方式为组合方式'
  ],
  auto: true
})
export default class UseCouponRecordMethod extends Vue {
  @Prop()
  data: CouponItem;

  @Prop({ default: [] })
  parties: RSCostParty[];

  viewSpecialGoods() {
    ; (this.$refs.SpecialGoodsDialog as any).open()
  }

  getPartyNameById(id: string) {
    let str = "";
    if (this.parties && this.parties.length > 0) {
      this.parties.forEach((item: any) => {
        if (item.costParty.id === id) {
          str = item.costParty.name;
        }
      });
    }
    return str;
  }

  getFavValue() {
    return Tools.getFavValue(this.data.coupons!, this.getPartyNameById)
  }
};