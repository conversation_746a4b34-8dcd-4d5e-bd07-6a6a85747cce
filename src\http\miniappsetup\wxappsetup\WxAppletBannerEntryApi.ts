/*
 * @Author: 黎钰龙
 * @Date: 2022-11-15 17:07:29
 * @LastEditTime: 2023-03-29 12:56:24
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\miniappsetup\wxappsetup\WxAppletBannerEntryApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import BannerEntry from 'model/miniappsetup/aliappsetup/BannerEntry'
import Response from 'model/common/Response'
import SaveBannerEntryRequest from 'model/miniappsetup/aliappsetup/SaveBannerEntryRequest'
import BannerEntryRequest from 'model/miniappsetup/aliappsetup/BannerEntryRequest'

export default class WxAppletBannerEntryApi {
  /**
   * 更新
   * 更新。
   * 
   */
  static modify(body: SaveBannerEntryRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/weixin-applet/banner/entry/modify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询
   * 查询。
   * 
   */
  static query(): Promise<Response<BannerEntryRequest>> {
    return ApiClient.server().post(`/v1/weixin-applet/banner/entry/query`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除
   * 删除。
   * 
   */
  static remove(id: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/weixin-applet/banner/entry/remove`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存
   * 保存。
   * 
   */
  static save(body: SaveBannerEntryRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/weixin-applet/banner/entry/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
