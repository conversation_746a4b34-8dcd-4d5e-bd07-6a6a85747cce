<template>
  <div class="nav-list">
    <div class="title">{{ i18n('导航') }}1</div>

    <el-radio v-for="item in options" :key="item.key" :label="item.key" v-model="navigationType" @change="handleChange">
      {{ item.caption }}
    </el-radio>

    <el-form inline v-if="formMode === FormModeType.form" :label-position="labelPosition" :model="value" :rules="rules"
      ref="form" style="width: 100%">
      <div v-show="navigationType === 'text'">
        <el-form-item :label="i18n('导航名称') + ':'" prop="name" style="width: 100%" class="activity-input">
          <el-input maxlength="10" :placeholder="i18n('请输入导航名称')" show-word-limit v-model="name" @change="handleChange"
            style="width: 100%"></el-input>
        </el-form-item>
        <div class="nav-icon">{{ i18n('导航图标') }}</div>
        <div class="text">
          {{ i18n('若设置导航图标，为保证前端展示效果导航名称最多设置5个字符，建议图片宽度为48像素，高度为48像素，支持jpg/jpeg/png/gif，大小不超过2M，每一张图片高度相同') }}
        </div>
        <upload-img v-model="image" width="80px" height="80px" @change="handleChange"></upload-img>
      </div>
      <div v-show="navigationType === 'img'">
        <div class="text">{{ i18n('建议图片宽度为132像素，高度为40像素，支持jpg/jpeg/png，大小不超过2M') }}</div>
        <upload-img v-model="image" width="80px" height="80px" @change="handleChange"></upload-img>
      </div>
      <el-form-item :label="i18n('导航定位设置')" prop="region" style="width: 100%">
        <el-select v-model="region" :placeholder="i18n('请选择投放活动')">
          <el-option v-for="item in options" :key="item.key" :label="item.key" v-model="navigationType"
            @change="handleChange"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="i18n('显示时间')" style="width: 100%">
        <el-date-picker v-model="value.propDateRange" type="datetimerange" range-separator="-" :start-placeholder="i18n('开始日期')"
          :end-placeholder="i18n('结束日期')" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']" :picker-options="pickerOptions"
          @change="handleChange"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-checkbox v-model="value.propCycle" class="cycle-time" @change="handleChange">
          {{ i18n('周期投放') }}
          <span>（{{ i18n('每天固定时段投放，最多不超过5个时段') }}）</span>
        </el-checkbox>
      </el-form-item>
    </el-form>
    <div class="btn">
      <el-button type="text" disabled>{{ i18n('上移') }}</el-button>
      <el-button type="text">{{ i18n('下移') }}</el-button>
      <el-button type="text">{{ i18n('删除') }}</el-button>
    </div>
  </div>
</template>

<script lang="ts" src="./NavList.ts"></script>

<style lang="scss" scoped>
.el-date-editor--datetimerange.el-input,
.el-date-editor--datetimerange.el-input__inner {
  width: 100%;
}

.el-form-item {
  margin-bottom: 0px;
}

.el-select {
  width: 100%;
}

.global-set {
  padding: 0 30px 0 0px;
}

.el-button+.el-button {
  margin-left: 0;
}

.nav-list {
  background: #f0f2f6;
  border-radius: 4px;
  padding: 12px;

  .nav-icon {
    font-weight: 400;
    font-size: 14px;
    color: #5a5f66;
    line-height: 20px;
  }

  .cycle-time {
    font-weight: 400;
    font-size: 14px;
    color: #24272b;
    line-height: 20px;

    span {
      font-weight: 400;
      font-size: 12px;
      color: #a1a6ae;
      line-height: 20px;
    }
  }

  .btn {
    font-weight: 400;
    font-size: 13px;
    line-height: 18px;
    text-align: right;
  }
}
</style>
