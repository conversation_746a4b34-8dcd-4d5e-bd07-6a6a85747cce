<!--
 * @Author: 黎钰龙
 * @Date: 2023-11-29 17:00:54
 * @LastEditTime: 2024-03-01 16:45:17
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\selectStores\SelectStores.vue
 * 记得注释
-->
<template>
  <el-select
    v-model="selectStore"
    value-key="id"
    :style="{width: width}"
    :loading="selectLoading"
    :disabled="disabled"
    clearable
    filterable
    remote
    :remote-method="doRemoteMethod"
    :placeholder="placeholder ? placeholder : i18n('请选择/输入门店')"
  >
    <el-option v-if="!hideAll" :label="i18n('全部')" :value="null">
      {{ i18n("全部") }}
    </el-option>
    <template v-if="isOnlyId">
      <el-option :label="item.org.name" :value="item.org.id" v-for="(item, index) in stores" :key="index">
        [{{ item.org.id }}]{{ item.org.name }}
      </el-option>
    </template>
    <template v-else>
      <el-option :label="item.org.name" :value="item.org" v-for="(item, index) in stores" :key="index">
        [{{ item.org.id }}]{{ item.org.name }}
      </el-option>
    </template>
  </el-select>
</template>

<script lang="ts" src="./SelectStores.ts"></script>

<style>
</style>