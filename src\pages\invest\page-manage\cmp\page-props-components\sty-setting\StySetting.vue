<!--
 * @Author: 黎钰龙
 * @Date: 2024-08-01 16:15:02
 * @LastEditTime: 2025-03-06 11:17:44
 * @LastEditors: haiding <EMAIL>
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\cmp\page-props-components\sty-setting\StySetting.vue
 * 记得注释
-->
<template>
  <div class="sty-setting">
    <div class="tips">
      <p class="tips-label">{{ i18n('组件间距') }}</p>
      <p class="tips-content">{{ i18n('为保证小程序页面美观，上下左右边距仅支持0px～30px，建议上边距0px、下边距20px、左右边距24px') }}</p>
    </div>
    <el-form
      label-position="left"
      :model="value"
      :rules="rules"
      ref="form"
    >
      <el-form-item :label="i18n('上边距')+'(px)'" prop="placeTitle" label-width="100px">
        <el-input-number v-model="value.styMarginTop" :controls="false" :min="0" :max="30"  :step="1" @change="handleChange"></el-input-number>
      </el-form-item>
      <el-form-item :label="i18n('下边距')+'(px)'" prop="placeTitle" label-width="100px">
        <el-input-number v-model="value.styMarginBottom" :controls="false" :min="0" :max="30"  :step="1" @change="handleChange"></el-input-number>
      </el-form-item>
      <el-form-item :label="i18n('左边距')+'(px)'" prop="placeTitle" label-width="100px">
        <el-input-number v-model="value.styMarginLeft" :controls="false" :step="1" :min="0" :max="30" @change="handleChange"></el-input-number>
      </el-form-item>
      <el-form-item :label="i18n('右边距')+'(px)'" prop="placeTitle" label-width="100px">
        <el-input-number  v-model="value.styMarginRight" :controls="false" :step="1" :min="0" :max="30" @change="handleChange"></el-input-number>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" src="./StySetting.ts"></script>

<style lang="scss" scoped>
.sty-setting{
  background: #F0F2F6;
  border-radius: 4px;
  padding: 12px;
  box-sizing: b;
  margin-bottom: 20px;
  .tips {
    &-label {
      font-weight: 500;
      font-size: 14px;
      color: #24272B;
      line-height: 16px;
      text-align: left;
    }
    &-content {
      font-weight: 400;
      font-size: 13px;
      color: #A1A6AE;
      line-height: 18px;
      text-align: left;
      margin: 20px 0;
    }
  }
}

</style>
