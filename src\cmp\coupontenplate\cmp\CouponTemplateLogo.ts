/*
 * @Description: 
 * @Version: 1.0
 * @Autor: 司浩
 * @Date: 2021-08-12 09:59:33
 * @LastEditors: 黎钰龙
 * @LastEditTime: 2024-04-24 15:04:45
 */
import {Component, Prop, Vue, Watch} from "vue-property-decorator";
import EnvUtil from "util/EnvUtil";
import I18nPage from "common/I18nDecorator";

@Component({
  name: 'CouponTemplateLogo',
  components: {}
})
@I18nPage({
  prefix: ['/公用/券模板',"/储值/会员储值/储值充值活动/编辑页面"],
  auto: true
})
export default class CouponTemplateLogo extends Vue {

  @Prop()
  originalLogoUrl: Nullable<string>

  ruleForm = {
    logoUrl: null,
    upload: false
  }

  @Watch('originalLogoUrl')
  onValueChange(value: string) {
    this.initUrl()
  }

  get uploadHeaders() {
    const obj:any = {
      marketingCenter: sessionStorage.getItem("marketCenter")
    }
    const authorization = EnvUtil.getUniAuthorization()
    if (authorization) {
      obj.authorization = authorization
    }
    return obj
  }

  get uploadUrl() {
    return EnvUtil.getServiceUrl() + 'v1/upload/upload'
  }

  mounted() {
    this.initUrl()
  }

  emitParams() {
    this.$emit('logoUrlCallBack', this.ruleForm.logoUrl)
  }


  private initUrl() {
    if (this.originalLogoUrl) {
      this.ruleForm.logoUrl = this.originalLogoUrl as any
    }
  }

  private resetUrl() {
    this.ruleForm.logoUrl = null
    this.emitParams();
  }

  private onUploadError() {
    this.ruleForm.upload = false
    this.$message.error(this.i18n('上传失败，请稍后重试'))
  }

  private onUploadSuccess(response: any, file: any, fileList: any) {
    if (response && response.code === 2000) {
      this.$message.success(this.formatI18n('/公用/券模板/新建券模板/券图标/上传成功'))
      this.ruleForm.logoUrl = response.data.url
      this.emitParams()
    } else {
      this.$message.error(response.msg)
    }
    this.ruleForm.upload = false
  }

  private prepareUpload(file: any) {
    const isJpg = ['image/jpeg', 'image/png', 'image/gif'].indexOf(file.type) > -1;
    const greaterMaxSize = file.size / 1024 < 300;
    if (!isJpg) {
      this.$message.error(this.formatI18n('/公用/券模板/新建券模板/券图标/上传图片只能是JPG/PNG/JPEG/GIF格式!'));
      return false;
    }
    if (!greaterMaxSize) {
      this.$message.error(this.formatI18n('/公用/券模板/新建券模板/券图标/上传图片大小不能超过300KB'));
      return false;
    }
    this.ruleForm.upload = true
    return true;
  }
}