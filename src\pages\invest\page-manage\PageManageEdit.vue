<!--
 * @Author: 黎钰龙
 * @Date: 2024-06-26 10:12:07
 * @LastEditTime: 2025-06-23 17:47:14
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\PageManageEdit.vue
 * 记得注释
-->
<template>
  <div class="page-manage-edit">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button size="large" @click="doSaveAndPublish" v-if="hasOptionPermission('/设置/小程序装修/页面管理','发布')">
          {{ i18n('发布') }}
        </el-button>
        <el-button size="large" @click="save(false)" v-if="hasOptionPermission('/设置/小程序装修/页面管理','新建') || hasOptionPermission('/设置/小程序装修/页面管理','编辑')">
          {{ i18n('/公用/按钮/保存') }}
        </el-button>
        <el-button size="large" @click="goBack">{{ i18n('取消') }}</el-button>
      </template>
    </BreadCrume>
    <el-container class="page-manage-edit-container" ref="editPage" v-if="reloadPage">
      <!-- 左侧组件列表 -->
      <el-aside class="page-manage-edit-container-panel">
        <el-scrollbar>
          <place-panel :templateList="templateList" :advertiseChannel="advertiseChannel" @dragClone="dragClone" @dragEnd="dragEnd"
            @panelMove="panelMove">
          </place-panel>
        </el-scrollbar>
      </el-aside>
      <!-- 组件渲染区 -->
      <el-main class="page-manage-edit-container-main">
        <render-page-place :renderTemplateList="renderTemplateList" :valdateArr="valdateArr" @activeCmp="activeCmp" @initPageData="initPageData"
          @toolBarClick="toolBarClick" @placeDragEnd="placeDragEnd" ref="RenderPagePlace"></render-page-place>
      </el-main>
      <!-- 右侧属性渲染区 -->
      <el-aside class="page-manage-edit-container-props">
        <render-page-props :activeProps="activeProps" :activeIndex="activeIndex" :activeUuid="activeUuid" @propsChange="propsChange"
          @validate="validate" :showComponentProp="showComponentProp" :advertiseChannel="advertiseChannel" :renderTemplateList="renderTemplateList">
        </render-page-props>
      </el-aside>
    </el-container>
  </div>
</template>

<script lang="ts" src="./PageManageEdit.ts">
</script>

<style lang="scss" scoped>
.page-manage-edit {
  height: 100%;
  width: 100%;
  overflow: overlay;
  scroll-behavior: smooth;
  background: #ebecee;
  display: flex;
  flex-direction: column;
  &-container {
    flex: 1;
    height: calc(100vh - 150px);
    &-panel {
      background: #ffffff;
      box-shadow: 4px 0px 8px 0px rgba(78, 84, 105, 0.1);
      border-radius: 8px 0px 0px 8px;
    }
    &-main {
      background: #f9f9f9;
      box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.1);
    }
    &-props {
      background: #f9fafc;
      box-shadow: -4px 0px 8px 0px rgba(78, 84, 105, 0.1);
      border-radius: 0px 8px 8px 0px;
    }
  }
  ::v-deep .el-main {
    width: 500px !important;
    padding: 0;
    flex: none;
  }
  .page-manage-edit-container-props {
    width: 380px !important;
    overflow: overlay;
    padding: 0;
    flex: none;
  }
  .page-manage-edit-container-panel {
    width: 323px !important;
  }
  .page-manage-edit-container-props::-webkit-scrollbar {
    width: 8px; /* 滚动条宽度 */
    position: fixed;
  }

  .page-manage-edit-container-props::-webkit-scrollbar-track {
    background-color: #f1f1f1; /* 轨道背景色 */
  }

  .page-manage-edit-container-props::-webkit-scrollbar-thumb {
    background-color: #888; /* 滑块背景色 */
    border-radius: 4px; /* 滑块边框圆角 */
  }

  .page-manage-edit-container-props::-webkit-scrollbar-thumb:hover {
    background-color: #555; /* 滑块悬停时的背景色 */
  }
  ::v-deep .el-aside {
    width: 315px;
    padding: 0;
    flex: none;
  }
}
</style>