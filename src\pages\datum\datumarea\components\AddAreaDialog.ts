import { Component, Prop, Vue } from "vue-property-decorator";
import { ElForm } from "fant3-hd/types/form";
import I18nPage from "common/I18nDecorator";
import ZoneApi from "http/area/ZoneApi";
import ZoneFilter from "model/datum/zone/ZoneFilter";
import SaveBatchZoneRequest from "model/datum/zone/SaveBatchZoneRequest";
import Zone from "model/datum/zone/Zone";
import BZoneTree from "model/datum/zone/BZoneTree";

@Component({
    name: "AddAreaDialog",
    components: {},
})
@I18nPage({
    auto: false,
    prefix: ["/公用/按钮", '/资料/区域', '/公用/菜单'],
})
export default class AddAreaDialog extends Vue {
    $refs: any;
    dialogVisible: boolean = false;
    loading: boolean = false
    type: Nullable<string> = '';
    bZoneTreeData: Nullable<BZoneTree> = null

    @Prop({default: ''})
    marketCenter: string

    @Prop({default: ''})
    marketCenterName: string

    form = {
        areaCode: "",
        areaName: "",
    };

    ZoneFilter: ZoneFilter = new ZoneFilter();
    SaveBatchZoneRequest: SaveBatchZoneRequest = new SaveBatchZoneRequest();

    // 表单校验规则
    rules = {
        areaCode: [{ required: true, message: this.i18n("请输入区域代码"), trigger: "blur" }],
        areaName: [{ required: true, message: this.i18n("请输入区域名称"), trigger: "blur" }],
    };

    open(data?:BZoneTree, type?: string) {
        this.dialogVisible = true;
        this.form.areaCode = '';
        this.form.areaName = '';
        const $form = this.$refs.form as InstanceType<typeof ElForm>;
        $form?.clearValidate();
        this.type = type;
        this.bZoneTreeData =  data
    }

    close() {
        this.dialogVisible = false;
    }

    // 清空
    onClear() {
        this.form.areaCode = "";
        this.form.areaName = "";
        const $form = this.$refs.form as InstanceType<typeof ElForm>;
        $form?.clearValidate();
    }

    // 添加
    onSave() {
        const $form = this.$refs.form as InstanceType<typeof ElForm>;

        $form?.validate((flag) => {
            if (flag) {
                let addFiter = new ZoneFilter();
                addFiter.zoneIdEquals = this.form.areaCode;
                this.loading = true
                ZoneApi.isExistZone(addFiter)
                    .then((res: any) => {
                        if (res.code === 2000) {
                            if (res.data && res.data.length > 0) {
                                this.$message.error(this.formatI18n("/资料/区域/区域已存在"));
                                this.loading = false
                                return;
                            } else {
                                 // 保存
                                let zones:Zone = new Zone();
                                zones.marketingCenter = sessionStorage.getItem("marketCenter") || '';
                                zones.zone = { id: this.form.areaCode, name: this.form.areaName };
                                zones.state = 'NORMAL'
                                if(this.type && this.bZoneTreeData){
                                    const zone = this.bZoneTreeData.zone

                                    zones.root = zone.root; 
                                    // 添加同级
                                    if(this.type === 'peer'){
                                        zones.upper = zone.upper
                                    }   

                                    // // 添加下级
                                    if(this.type === 'lower'){
                                        zones.upper = zone.zone.id
                                    }
                                }
                                this.SaveBatchZoneRequest.zones = [];
                                this.SaveBatchZoneRequest.zones.push(zones);
                                ZoneApi.saveBatch(this.SaveBatchZoneRequest)
                                    .then((res) => {
                                        if (res.code === 2000) {
                                            this.$message.success(this.formatI18n("/资料/门店/添加成功"));
                                            this.$emit("success")
                                            this.close();
                                        }
                                    })
                                    .catch((error) => {
                                        if (error && error.message) {
                                            this.$message.error(error.message);
                                        }
                                    }).finally(()=>{
                                        this.loading = false
                                    })
                            }
                        } else {
                            this.$message.error(res.msg);
                            this.loading = false;
                        }
                    })
                    .catch((error) => {
                        if (error && error.message) {
                            this.$message.error(error.message);
                        }
                        this.loading = false
                    });
            }
        });


    }
}
