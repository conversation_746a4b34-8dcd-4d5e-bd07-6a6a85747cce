import I18nPage from 'common/I18nDecorator';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import SpecialGoodsDialog from 'cmp/coupontenplate/cmp/specialGoodsDialog.vue'
import AmountToFixUtil from 'util/AmountToFixUtil';
import SelectGoodsDialog from 'cmp/coupontenplate/cmp/selectGoodsDialog';
import RSGoods from 'model/common/RSGoods';
import RSCostPartyFilter from "model/common/RSCostPartyFilter";
import CostPartyApi from "http/costparty/CostPartyApi";
@Component({
  name: 'UseCouponDesc',
  components: {
    SpecialGoodsDialog,
    SelectGoodsDialog
  }
})
@I18nPage({
  prefix: [
    "/公用/券模板",
    '/营销/券礼包活动/核销第三方券',
    "/公用/菜单"
  ],
  auto: true
})
export default class UseCouponDesc extends Vue {
  @Prop()
  value: any;

  @Prop()
  copyFlag: 'add' | 'edit' | 'copy';

  @Prop({
    type: Boolean,
    default: false,
  })
  baseFieldEditable: boolean; // 叠加促销、用券记录方式、券承担方、用券顺序 是否可编辑

  @Prop({
    type: String
  })
  labelWidth: string;

  @Prop()
  specialGoods: any;  //特殊商品列表

  $refs: any
  showPartSelect: boolean = false; // 是否展示承担方选择框
  parties: any = []; // 承担方信息
  partiesId: any = [];  // 券承担方法选择的id

  ruleForm: any = {
    recordWay: 'FAV',  //优惠方式--FAV  支付方式--PAY 组合方式--COLLOCATION
    discountWay: '',  //组合方式--按比例--第一个input
    payWay: '', //组合方式--按比例--第二个input
    recordType: '', //按比例-PROPORTION   按金额--AMOUNT
    specialGoods: [], //特殊商品列表
    amountPayments:[],  // 券支付金额方式
    parties: []   // 承担方
  }
  rules = {
    discountWay: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
    payWay: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
    amountPayments: {
      validator: (rule: any, value: string, callback: any) => {
        if (value === "" || value == null || value == undefined || value.length === 0) {
          callback(new Error(this.formatI18n("设置/系统设置/至少选择一种方式")));
        }
        if (this.ruleForm.amountPayments && this.ruleForm.amountPayments.includes('CUSTOM_AMOUNTS')) {
          if (!this.ruleForm.payWay && this.ruleForm.payWay !== 0) {
            callback(new Error(this.formatI18n("/公用/券模板/请输入必填项")));
          }
        }
        if (this.ruleForm.amountPayments && this.ruleForm.amountPayments.includes('BEAR_AMOUNTS')) {
          if (!this.ruleForm.parties || this.ruleForm.parties.length < 1) {
            callback(new Error(this.formatI18n("/公用/券模板/请输入必填项")));
          }
        }
        callback();
      },
      trigger: ["change", "blur"],
      }
    }


  @Watch("value", { deep: true, immediate: true })
  onValueChange(value: any) {
    if (value) {
      this.ruleForm.recordType = value.recordType
      this.ruleForm.discountWay = value.discountWay
      this.ruleForm.payWay = value.payWay
      this.ruleForm.recordWay = value.recordWay
      this.ruleForm.specialGoods = this.specialGoods
      this.ruleForm.amountPayments = value.amountPayments
      if (!this.showBearAmounts) {
        const findIndex = this.ruleForm.amountPayments.findIndex((item:any) => 'BEAR_AMOUNTS' === item);
        // 当前行处于未选中状态且已加入已选列表中，需移除
        if (findIndex != -1) {
          this.ruleForm.amountPayments.splice(findIndex, 1);
          this.doFormItemChange()
        }
      }
      if (this.ruleForm.amountPayments.includes('BEAR_AMOUNTS')) {
        this.showPartSelect = true
      } else {
        this.showPartSelect = false
      }

      this.ruleForm.parties = value.parties
      console.log(value)
      console.log(value.couponUnder)
      console.log(value.couponUnder.costPartyDetails)
      if (value.couponUnder && value.couponUnder.costPartyDetails && value.couponUnder.costPartyDetails.length > 0) {
        console.log('if通过',this.partiesId)
        this.partiesId = value.couponUnder.costPartyDetails.map((item: any) => {
          return item.party
        })
        let specialGoodsPartiesId:any = []
        if (value.couponUnder&& value.couponUnder.specialGoodsCostParties && value.couponUnder.specialGoodsCostParties.length > 0) {
          value.couponUnder.specialGoodsCostParties.forEach((item: any) => {
            item.costPartyDetails.forEach((item: any) => {
              specialGoodsPartiesId.push(item.party)
            })
          })
        }
        specialGoodsPartiesId.forEach((item: any) => {
          if (!this.partiesId.includes(item)) {
            this.partiesId.push(item)
          }
        })
      }
      let length = this.ruleForm.parties.length
      this.ruleForm.parties = this.ruleForm.parties.filter((party:any) => this.partiesId.includes(party));
      if (length > this.ruleForm.parties.length) {
        this.doFormItemChange()
      }
    }
  }


  mounted() {
    this.getCostParty()
  }

  validate() {
    if (this.$refs["form"]) {
      return this.$refs["form"].validate();
    } else {
      return true;
    }
  }

  doParams() {
    const object = {
      recordWay: this.ruleForm.recordWay,
      discountWay: this.ruleForm.discountWay,
      payWay: this.ruleForm.payWay,
      recordType: this.ruleForm.recordType,
      amountPayments: this.ruleForm.amountPayments,
      parties: this.ruleForm.parties
    }
    return { ...this.value, ...object }
  }

  getFavValue(favValue: any, payValue: any) {
    let str = ''
    if (this.value.recordType === 'AMOUNT') {
      str = this.formatI18n(
        "/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送券的时候/点击券名称/用券记录方式为组合方式",
        "{0}元支付方式，剩余算优惠方式"
      );
      str = str.replace(
        /\{0\}/g,
        `&nbsp;<span style="font-weight: bold">${payValue}</span>&nbsp;`
      );
      str = '（' + str + '）'

    } else {
      str = this.formatI18n(
        "/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送券的时候/点击券名称/用券记录方式为组合方式",
        "{0}%优惠方式+{1}%支付方式"
      );
      str = str.replace(
        /\{0\}/g,
        `&nbsp;<span style="font-weight: bold">${favValue}</span>&nbsp;`
      );
      str = str.replace(
        /\{1\}/g,
        `&nbsp;<span style="font-weight: bold">${payValue}</span>&nbsp;`
      );
    }

    return str;
  }

  viewSpecialGoods() {
    ; (this.$refs.SpecialGoodsDialog as any).open()
  }

  doRecordWayChange() {
    if (this.ruleForm.recordWay === "COLLOCATION") {
      this.ruleForm.discountWay = Number(100).toFixed(2);
      this.ruleForm.payWay = Number(0).toFixed(2);
    } else {
      this.ruleForm.discountWay = "";
      this.ruleForm.payWay = "";
      this.ruleForm.parties = []
      this.ruleForm.amountPayments = []
    }
    this.doFormItemChange()
  }

  doFormItemChange() {
    this.$emit('input',this.doParams())
    this.$emit('change')
    this.$emit('changeSpecialGoods',this.ruleForm.specialGoods)
  }

  doDiscountWay() {
    this.ruleForm.discountWay = AmountToFixUtil.formatAmount(this.ruleForm.discountWay, 100, 0, "");
    this.ruleForm.payWay = (100 - Number(this.ruleForm.discountWay)).toFixed(2);
    this.doFormItemChange()
  }

  doPayWay() {
    this.ruleForm.payWay = AmountToFixUtil.formatAmount(this.ruleForm.payWay, 100, 0, "");
    this.ruleForm.discountWay = (100 - Number(this.ruleForm.payWay)).toFixed(2);
    this.doFormItemChange()
  }

  doPayWayByAmount() {
    this.ruleForm.payWay = AmountToFixUtil.formatAmount(this.ruleForm.payWay, 999999.99, 0, 2);
    this.doFormItemChange()
  }

  // 接受特殊商品参数
  specialGoodsSubmit(goods: RSGoods[]) {
    this.ruleForm.specialGoods = [...goods]
    this.doFormItemChange()
  }

  // 用券方式 设置特殊商品
  setSpecialGoods() {
    this.$refs.SelectGoodsDialog.open(this.ruleForm.specialGoods)
  }

  private getCostParty() {
    let params: RSCostPartyFilter = new RSCostPartyFilter();
    params.page = 0;
    params.pageSize = 0;
    CostPartyApi.query(params)
        .then((resp: any) => {
          if (resp && resp.code === 2000) {
            this.parties = resp.data;
          }
        })
        .catch((error: any) => {
          this.$message.error(error.message);
        });
  }

  get getCanSelectParty() {
    return this.parties.filter((item: any) => {
      return this.partiesId.includes(item.costParty.id) === true;
    });
  }

  changeAmountPayments() {
    if (this.ruleForm.amountPayments.includes('BEAR_AMOUNTS')) {
      this.showPartSelect = true
    } else {
      this.showPartSelect = false
    }
    this.doFormItemChange()
  }

  doChangeSelect() {
    this.doFormItemChange()
  }

  get showBearAmounts() {
    return this.value.couponUnder.bearType != null && this.value.couponUnder.bearType !== 'NONE'
  }
};