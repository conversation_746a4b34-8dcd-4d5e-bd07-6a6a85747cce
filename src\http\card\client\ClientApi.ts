import ApiClient from 'http/ApiClient'
import Response from 'model/default/Response'
import ClientFilter from "model/card/client/ClientFilter";
import Client from "model/card/client/Client";

export default class ClientApi {
  /**
   * 获取客户信息
   * 获取客户信息。
   * 
   */
  static query(body: ClientFilter): Promise<Response<Client[]>> {
    return ApiClient.server().post(`/v1/client/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
