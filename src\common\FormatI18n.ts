import Vue from "vue";

/** 废弃类，老代码在用，有空再删 */
export default class FormatI18n {
  static init() {
    Vue.prototype.formatI18n = function(id: any, str: any = null, params: any = null, splitIndex: Nullable<number> = null) {
      let translated = null
      if (str) {
        translated = this.$root.$t(`m.pages.i18n[\"${id}/${str}\"]`)
      } else {
        translated = this.$root.$t(`m.pages.i18n[\"${id}\"]`)
      }
      if (params) {
        let index = 0
        for (let param of params) {
          translated = translated.replace(`{${index++}}`, param)
        }
      }
      if (splitIndex) {
        let regExp = /\{\w*}/gi;
        let res = null
        let contentSplit = []
        let content = translated
        while (res = regExp.exec(translated)) {
          let split = content.split(res + '')
          contentSplit.push(split[0])
          content = split[1]
        }
        contentSplit.push(content)
        return contentSplit[splitIndex]
      }
      return translated
    }
  }
}
