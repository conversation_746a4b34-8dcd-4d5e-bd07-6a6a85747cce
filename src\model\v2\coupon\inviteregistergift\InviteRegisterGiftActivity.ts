import BaseCouponActivity from 'model/v2/coupon/BaseCouponActivity'
import InviteRegisterRule from 'model/v2/coupon/inviteregistergift/InviteRegisterRule'
import ChannelRange from "model/common/ChannelRange";
import DateTimeCondition from "model/common/DateTimeCondition";


export default class InviteRegisterGiftActivity extends BaseCouponActivity {
  // 邀请礼包设置
  inviteRegisterRule: Nullable<InviteRegisterRule> = null
  //渠道范围
  channelRange: Nullable<ChannelRange> = null
  	// 活动时间限制
	dateTimeCondition = new DateTimeCondition();
}