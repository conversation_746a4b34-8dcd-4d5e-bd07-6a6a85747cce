import {Component, Vue} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import ListWrapper from 'cmp/list/ListWrapper.vue'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import PrePayBalanceApi from 'http/prepay/balance/PrePayBalanceApi'
import PrepayAccountFilter from 'model/prepay/balance/PrepayAccountFilter'
import PrepayAccount from 'model/prepay/balance/PrepayAccount'
import PrePayConfigApi from 'http/prepay/config/PrePayConfigApi'
import PrepayAccountApi from 'http/prepay/account/PrepayAccountApi'
import IdName from 'model/common/IdName'
import I18nPage from "common/I18nDecorator";
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import ExportConfirm from "cmp/exportconfirm/ExportConfirm";
import DownloadCenterDialog from "pages/main/dowmload_center_dialog/DownloadCenterDialog";
import PermissionMgr from 'mgr/PermissionMgr'

@Component({
  name: 'StoreValueQuery',
  components: {
    FormItem,
    ListWrapper,
    SubHeader,
    BreadCrume,
    ExportConfirm,
    DownloadCenterDialog
  }
})
@I18nPage({
  prefix: ['/储值/会员储值/会员储值查询/列表页面'],
})
export default class StoreValueQuery extends Vue {
  query: PrepayAccountFilter = new PrepayAccountFilter()
  queryData: PrepayAccount[] = []
  enableMultipleAccount: boolean = false
  accounts: IdName[] = []
  exportDialogShow = false
  showTip: boolean = false
  fileDialogVisible = false
  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  tableHeight: number = 0
  panelArray: any = []
  accountNmId: string = ''
  created() {
    this.panelArray = [
      {
        name: this.formatI18n('/公用/菜单/会员储值查询'),
        url: ''
      }
    ]
    this.getPrePermission()
    if (this.$route.query && this.$route.query.id) {
      this.query.memberCodeLikes = this.$route.query.id as string
      // 有条件过来主动查询
      this.doSearch()
    }
    this.listEnableAccountType()
  }

  mounted() {
    window.onresize = () => {
      this.setTableSize()
    };
  }

  doSearch() {
    if (PermissionMgr.daqiaoshihuaDingkai() &&!this.query.memberCodeLikes) {
      this.$message.warning(this.formatI18n('/营销/积分活动/积分查询/标题/请输入会员信息查询'))
      return
  }
    this.getList()
  }

  doReset() {
    this.query = new PrepayAccountFilter()
    this.page.currentPage = 1
    if (PermissionMgr.daqiaoshihuaDingkai()) {
    } else {
      this.getList()
    }
  }

  /**
   * 查询
   */
  onSearch() {
    this.page.currentPage = 1
    this.getList()
  }

  /**
   * 去详情
   */
  doGoDtl(row: any) {
    if (row.account) {
      this.$router.push({
        name: 'store-value-hst-detail',
        query: {uuid: row.uuid, id: row.account.id, name: row.account.name}
      })
    } else {
      this.$router.push({name: 'store-value-hst-detail', query: {uuid: row.uuid, id: row.name}})
    }
  }

  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.getList()
  }

  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.getList()
  }

  /**
   * 表格排序条件
   */
  onSortChange({column, prop, order}: any) {
    // todo
  }

  doBatchExport() {
    if (this.query.nameEquals === null && this.enableMultipleAccount === true) {
      this.$message.warning(this.formatI18n("/储值/会员储值/会员储值查询/列表页面/大数据量导出会影响系统稳定，不支持全部账户导出！"))
    } else {
      this.exportDialogShow = true
    }
  }

  changeAccount(id: any) {
    if (id === null) {
      this.accountNmId = ''
    } else {
      this.accounts.forEach((item: any)=> {
        if (id === item.id) {
          this.accountNmId = '[' + item.id + '] ' + item.name
        }
      })
    }
    this.doSearch();
  }

  doDownloadDialogClose() {
    this.fileDialogVisible = false
  }

  doExportDialogClose() {
    this.exportDialogShow = false
  }

  doSummit(flag: any) {
    PrePayBalanceApi.exportAccount(this.query)
        .then((resp: any) => {
          if (resp && resp.code === 2000) {
            this.showTip = true
            this.fileDialogVisible = true
          }
        }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private getList() {
    this.query.page = this.page.currentPage - 1
    this.query.pageSize = this.page.size
    this.query.ownerNamespaceEquals = 'member'
    PrePayBalanceApi.query(this.query).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.queryData = resp.data
        this.page.total = resp.total
        this.setTableSize()
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private setTableSize() {
    let table = document.getElementsByClassName('current-page')[0] as any
    if (table) {
      this.tableHeight = table.offsetHeight - 150
    }
  }

  private getPrePermission() {
    PrePayConfigApi.get().then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.enableMultipleAccount = resp.data.enableMultipleAccount
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private listEnableAccountType() {
    PrepayAccountApi.listEnableAccountType().then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.accounts = resp.data
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

}
