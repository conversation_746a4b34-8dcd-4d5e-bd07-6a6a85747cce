import BreadCrume from "cmp/bread-crumb/BreadCrume";
import I18nPage from "common/I18nDecorator";
import Write<PERSON>ard<PERSON>ill<PERSON>pi from "http/card/writeCard/WriteCardBillApi";
import WriteMachine<PERSON>pi from "http/card/writeCard/WriteMachineApi";
import Check<PERSON>ardResult from "model/card/writeCard/CheckCardResult";
import { WriteCardAction } from "model/card/writeCard/WriteCardAction";
import WriteCardBill from "model/card/writeCard/WriteCardBill";
import WriteCardBillCalRequest from "model/card/writeCard/WriteCardBillCalRequest";
import WriteCardBillLine from "model/card/writeCard/WriteCardBillLine";
import WriteCardMachineRequest from "model/card/writeCard/WriteCardMachineRequest";
import WriteCardRequest from "model/card/writeCard/WriteCardRequest";
import WriteCardResult from "model/card/writeCard/WriteCardResult";
import IdName from "model/common/IdName";
import { CardMedium } from "model/default/CardMedium";
import CommonUtil from "util/CommonUtil";
import { Component, Vue } from "vue-property-decorator";
import CardTemplate from "model/card/template/CardTemplate";
import CardTemplateFilter from "model/card/template/CardTemplateFilter";
import CardTemplateSelectorDialog from "cmp/selectordialogs/CardTemplateSelectorDialog";

// 写卡状态
enum ProcessState {
  UNSTART = "UNSTART", //未开始
  PROCESSING = "PROCESSING", //进行中
  PAUSE = "PAUSE", //暂停写卡
}

class Form {
  startCode: string = ""; //起始卡号
  endCode: string = ""; //结束卡号
  cardMedium: CardMedium = CardMedium.mag; //卡介质
  defaultCardTemplate: Nullable<boolean> = true; //是否默认卡模板
  cardTemplate: Nullable<IdName> = null //卡模板
}

@Component({
  name: "WriteCard",
  components: {
    BreadCrume,
    CardTemplateSelectorDialog
  },
})
@I18nPage({
  prefix: ["/公用/券模板", "/卡/写卡", "/卡/卡管理/卡介质"],
  auto: true,
})
export default class WriteCard extends Vue {
  $refs: any;
  writeCardInfo: Nullable<WriteCardResult> = null; // 当前的写卡信息
  writeCardNum: Nullable<number> = null; // 写卡数量
  form: Form = new Form();
  intervalState: "initial" | "processing" | "pause" = "initial"; // 定时器的状态
  writeState: ProcessState = ProcessState.UNSTART;
  recordList: WriteCardBillLine[] = []; // 写卡流水记录
  billNumber: Nullable<string> = null; // 写卡单号
  currentWriteCard: Nullable<string> = null; // 当前读取的实体卡sn号
  startCodeAvailable: boolean = false; //起始卡号是否可用
  topicText: string = ""; //操作指引文案
  magTopicText: string = ""; //磁条卡 操作指引文案
  timeStep: number = 500; //轮询间隔
  endCodeAvailable: boolean = true; //结束卡号是否可用
  writeCardType: Nullable<string> = null  //起始卡号的卡模板类型

  get panelArray() {
    return [
      {
        name: this.i18n("/公用/菜单/写卡单"),
        url: "",
      },
    ];
  }

  get isEnglish() {
    return sessionStorage.getItem("locale") === "en_US";
  }

  get rules() {
    return {
      startCode: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: ["blur", "change"] }],
      endCode: [
        {
          required: false,
          validator: (rule: any, val: any, callback: any) => {
            if (!this.form.startCode?.length) {
              callback();
            }
            if (this.form.endCode?.length) {
              if (this.form.startCode?.length !== this.form.endCode?.length) {
                callback(new Error(this.i18n("/卡/卡管理/制卡单/制卡单详情/结束卡号必须与起始卡号位数一致")));
              }
              const end = BigInt(this.form.endCode);
              const start = BigInt(this.form.startCode);
              if (end < start) {
                callback(new Error(this.i18n("结束卡号不能小于起始卡号")));
              }
            }
            callback();
          },
          trigger: ["blur", "change"],
        },
      ],
      cardMedium: [{ required: true, message: this.formatI18n("/公用/表单校验/请选择必选项"), trigger: ["blur", "change"] }],
      defaultCardTemplate: [
        {
          required: false,
          validator: (rule: any, val: any, callback: any) => {
            if (!this.form.defaultCardTemplate && !this.form.cardTemplate?.id) {
              callback(new Error(this.i18n('/储值/预付卡/电子礼品卡活动/编辑页面/请选择卡模板')));
            }
            callback();
          },
          trigger: ["blur", "change"],
        },
      ]
    };
  }

  get startBtnDisable() {
    const formLack = !this.form.startCode || !this.form.cardMedium || !this.writeCardNum;
    const stateFlag = this.writeState === ProcessState.UNSTART;
    const cardTemplateFlag = !this.form.defaultCardTemplate && !this.form.cardTemplate?.id
    return (formLack && stateFlag) || !this.startCodeAvailable || !this.endCodeAvailable || cardTemplateFlag
  }

  // 只有初始化的单据才能修改表单
  get formDisable() {
    return this.writeState !== ProcessState.UNSTART;
  }

  get cardTemplateFilter() {
    const filter = new CardTemplateFilter()
    filter.cardMediumIn = [this.form.cardMedium]
    filter.cardTemplateTypeEquals = this.writeCardType
    return filter
  }

  created() {
    this.getDtl();
  }

  // 查询上次写卡记录
  getDtl() {
    const loading = CommonUtil.Loading();
    WriteCardBillApi.get()
      .then((res) => {
        if (res.code === 2000) {
          if (res.data) {
            this.writeCardInfo = res.data;
            if (this.writeCardInfo.nextCardCode) {
              this.topicText = this.i18n("请操作");
              this.magTopicText = this.i18n("请操作")
            } else {
              this.haveNoNextCard();
            }
            this.billNumber = res.data.billNumber;
            this.form.startCode = res.data.startCardCode || "";
            this.form.endCode = res.data.endCardCode || "";
            this.form.cardMedium = res.data.cardMedium || CardMedium.bar;
            this.recordList = res.data.lines || [];
            this.writeCardNum = res.data.qty;
            this.form.defaultCardTemplate = res.data.defaultCardTemplate;
            this.form.cardTemplate = res.data.cardTemplateNumber ? { id: res.data.cardTemplateNumber, name: res.data.cardTemplateName } : null;
            this.writeState = ProcessState.PAUSE;
          }
        } else {
          throw new Error(res.msg!);
        }
      })
      .catch((error) => {
        this.$message.error(error.message);
      })
      .finally(() => {
        loading.close();
      });
  }

  // 每次写卡后，重新查询流水记录
  getRecordList() {
    WriteCardBillApi.get()
      .then((res) => {
        if (res.code === 2000) {
          if (res.data) {
            this.recordList = res.data.lines || [];
          }
        } else {
          throw new Error(res.msg!);
        }
      })
      .catch((error) => {
        this.$message.error(error.message);
      });
  }

  // 校验起始卡号是否可用
  doCheckStartCode() {
    if (!this.form.startCode) return;
    this.checkCodeAccess(this.form.startCode)
      .then((res: CheckCardResult) => {
        this.form.cardMedium = res.cardMedium || CardMedium.mag;
        this.startCodeAvailable = true;
        this.writeCardNum = 1;
        this.form.cardTemplate = null
        this.writeCardType = res.cardType || null
      })
      .catch(() => {
        this.startCodeAvailable = false;
      });
  }

  // 校验结束卡号是否可用
  doCheckEndCode() {
    if (!this.form.endCode) return;
    this.checkCodeAccess(this.form.endCode)
      .then((res: CheckCardResult) => {
        this.endCodeAvailable = true;
      })
      .catch(() => {
        this.endCodeAvailable = false;
      });
  }

  // 校验指定卡号是否为空白卡
  checkCodeAccess(code: string) {
    return new Promise((resolve, reject) => {
      WriteCardBillApi.checkStartCode(code)
        .then((res) => {
          if (res.code === 2000) {
            // 只要状态码是2000就可以写卡
            resolve(res.data);
            this.doCheckEndCodeQty();
          } else {
            throw new Error(res.msg!);
          }
        })
        .catch((error) => {
          this.$message.error(error.message);
          reject();
        });
    });
  }

  doCheckEndCodeQty() {
    if (!this.form.startCode?.length || !this.form.endCode?.length) return;
    if (this.form.startCode?.length !== this.form.endCode?.length) return;
    const end = BigInt(this.form.endCode);
    const start = BigInt(this.form.startCode);
    if (end < start) return;
    this.checkCardQty();
  }

  // 跳过当前卡号
  doSkip() {
    this.intervalState = "pause";
    this.$confirm(this.i18n("确定跳过当前写卡卡号吗？"), this.i18n("跳过"), {
      confirmButtonText: this.i18n("确认"),
      cancelButtonText: this.i18n("取消"),
    })
      .then(async () => {
        await this.notifyWriteCard(-3);
        this.intervalState = "processing";
        this.setLoopSet();
      })
      .catch(() => {
        this.intervalState = "processing";
        this.setLoopSet();
      });
  }

  // 开始写卡
  startWrite() {
    if (!this.form.endCode) {
      this.form.endCode = this.form.startCode;
    }
    const loading = CommonUtil.Loading();
    setTimeout(() => {
      // 必须在结束卡号blur之后实现
      if (this.startBtnDisable) return loading.close();
      const params = new WriteCardBill();
      params.cardMedium = this.form.cardMedium;
      params.startCardCode = this.form.startCode;
      params.endCardCode = this.form.endCode;
      params.marketingCenter = sessionStorage.getItem("marketCenter");
      params.qty = this.writeCardNum;
      params.defaultCardTemplate = this.form.defaultCardTemplate;
      if (this.form.defaultCardTemplate === false) {
        params.cardTemplateNumber = this.form.cardTemplate?.id;
        params.cardTemplateName = this.form.cardTemplate?.name;
      }
      WriteCardBillApi.save(params)
        .then((res) => {
          if (res.code === 2000) {
            this.billNumber = res.data?.billNumber;
            this.writeCardInfo = res.data;
            this.writeState = ProcessState.PROCESSING;
            this.startWriteProcess();
          } else {
            throw new Error(res.msg!);
          }
        })
        .catch((error) => {
          this.$message.error(error.message);
        })
        .finally(() => {
          loading.close();
        });
    }, 1000);
  }

  // 开始轮询
  async setLoopSet() {
    // 正常流程：校验是否有卡（拿到sn）=> 是否空卡 => 制卡 => 读卡 => 下一次循环
    console.log("轮询开始");
    if (this.intervalState !== "processing") return;
    if (!this.writeCardInfo?.nextCardCode) return;
    try {
      this.topicText = this.i18n("写卡中，请等待");
      const hasCard = await this.checkHasCard(); //校验是否有卡
      if (hasCard) {
        const emptyRes = await this.checkIsEmpty(); //是否空卡
        if (emptyRes) {
          const makeRes = await this.makeCard(); //制卡
          const readCardRes = await this.readCard(); //读卡
          if (makeRes && readCardRes) {
            // 制卡+读卡完成后 通知后端
            const requestRes = await this.notifyWriteCard(1);
            if (requestRes) {
              setTimeout(() => {
                this.setLoopSet();
              }, this.timeStep);
            }
          }
        } else {
          // 不为空卡，两秒后自动重试
          this.autoRetry(2000);
        }
      } else {
        // 未放卡，两秒后自动重试
        this.autoRetry(2000);
      }
    } catch (error) {
      console.log("轮询流程报错", error);
    }
    console.log("轮询结束");
  }

  // n秒后自动重试制卡流程
  autoRetry(time: number) {
    setTimeout(() => {
      this.setLoopSet();
    }, time);
  }

  // 开始与写卡硬件交互
  startWriteProcess() {
    return this.openMachine();
  }

  // 打开并初始化卡机
  openMachine() {
    this.magTopicText = this.i18n("请刷磁卡")
    return WriteMachineApi.open(this.form.cardMedium)
      .then((res) => {
        if (res.code === 2000) {
          this.intervalState = "processing";
          this.writeState = ProcessState.PROCESSING;
          this.setLoopSet();
        } else {
          throw new Error(res.msg || this.i18n("打开卡机失败，请检查"));
        }
      })
      .catch((error) => {
        let msg = error.message
        if (msg.indexOf('请检查网络设置') > -1) {
          msg = this.i18n("打开卡机失败，请检查")
        }
        this.$message.error(msg)
        this.topicText = this.i18n("打开卡机失败，请检查");
      })
  }

  // 检查卡机是否有卡，并返回实体卡号
  checkHasCard() {
    return new Promise((resolve, reject) => {
      WriteMachineApi.checkAndGet()
        .then((res) => {
          if (res.code === 2000) {
            if (res.data === 0) {
              // 0：未放置卡片
              this.topicText = this.i18n("请在卡机放置新卡或检查卡是否已经放好");
              resolve(false);
            } else {
              // 返回了sn号
              this.currentWriteCard = String(res.data);
              resolve(true);
            }
          } else {
            throw new Error(res.msg!);
          }
        })
        .catch((error) => {
          this.$message.error(this.i18n("检查是否有卡失败"));
          reject(error);
        });
    });
  }

  // 检测是否为空卡
  checkIsEmpty() {
    return new Promise((resolve, reject) => {
      if (!this.currentWriteCard) {
        reject();
        return;
      }
      WriteMachineApi.isEmpty(this.currentWriteCard)
        .then((res) => {
          if (res.code === 2000) {
            if (res.data === true) {
              resolve(true);
            } else {
              this.topicText = this.i18n("卡机上放置的卡片非空卡，请检查");
              resolve(false);
            }
          } else {
            throw new Error(res.msg!);
          }
        })
        .catch((error) => {
          this.$message.error(this.i18n("检查空卡失败"));
          reject(error);
        });
    });
  }

  // 开始写卡
  makeCard() {
    return new Promise((resolve, reject) => {
      const params = new WriteCardMachineRequest();
      params.sn = Number(this.currentWriteCard);
      params.innerCode = this.writeCardInfo?.nextCardCode;
      params.code = this.writeCardInfo?.nextCardCode;
      WriteMachineApi.make(params)
        .then((res) => {
          if (res.code === 2000 && res.data) {
            resolve(true);
          } else {
            throw new Error(res.msg!);
          }
        })
        .catch((error) => {
          this.$message.error(this.i18n("制卡失败"));
          this.writeFail(-4);
          reject(error);
        });
    });
  }

  // 读卡
  readCard() {
    return new Promise((resolve, reject) => {
      if (!this.currentWriteCard) {
        reject();
        return;
      }
      WriteMachineApi.read(this.currentWriteCard)
        .then((res) => {
          if (res.code === 2000) {
            resolve(true);
          } else {
            throw new Error(res.msg!);
          }
        })
        .catch((error) => {
          this.writeFail(-5, true);
          this.$message.error(this.i18n("读卡失败"));
          reject(error);
        });
    });
  }

  // 清卡
  clearCard(state: number, needClear: boolean = false) {
    return new Promise((resolve, reject) => {
      if (!this.currentWriteCard) {
        reject();
        return;
      }
      WriteMachineApi.clear(this.currentWriteCard)
        .then((res) => {
          if (res.code === 2000 && res.data) {
            // 清卡成功后，立刻校验空卡
            this.checkIsEmpty().then((readRes) => {
              if (readRes) {
                resolve(true);
              }
            });
          } else {
            throw new Error();
          }
        })
        .catch((error) => {
          this.writeFail(state, needClear);
          this.$message.error(this.i18n("清卡失败"));
          reject(error);
        });
    });
  }

  // 通知后端 当前卡写入结果
  notifyWriteCard(state: number) {
    return new Promise((resolve, reject) => {
      const params = new WriteCardRequest();
      params.billNumber = this.billNumber;
      params.cardCode = this.writeCardInfo?.nextCardCode;
      params.currentState = state;
      const loading = CommonUtil.Loading();
      WriteCardBillApi.writeCard(params)
        .then((res) => {
          if (res.code === 2000) {
            this.writeCardInfo = res.data;
            this.currentWriteCard = null;
            this.getRecordList();
            this.haveNoNextCard();
            resolve(true);
          } else {
            throw new Error(res.msg!);
          }
        })
        .catch((error) => {
          this.$message.error(error.message);
          reject(error);
        })
        .finally(() => {
          loading.close();
        });
    });
  }

  // 不存在下一张卡，表示卡已全部写完
  haveNoNextCard() {
    if (!this.writeCardInfo?.nextCardCode) {
      // 下一张卡code=null 说明已制卡完成
      this.topicText = this.i18n("写卡完成，请点结束写卡");
      this.magTopicText = this.i18n("写卡完成，请点结束写卡")
    }
  }

  // 写卡失败
  writeFail(state: number, needClear: boolean = false) {
    this.intervalState = "pause";
    this.$confirm(this.i18n("写卡失败，是否重试？"), this.i18n("/卡/卡管理/卡回收单/提示"), {
      confirmButtonText: this.i18n("是"),
      cancelButtonText: this.i18n("登记为坏卡"),
      showClose: false,
    } as any)
      .then(() => {
        if (needClear) {
          // 需要先清卡再重试
          this.clearCard(state, needClear).then((res) => {
            if (res) {
              // 清卡成功 重试
              this.intervalState = "processing";
              this.setLoopSet();
            }
          });
        } else {
          this.intervalState = "processing";
          this.setLoopSet();
        }
      })
      .catch(async () => {
        const res = await this.notifyWriteCard(state);
        if (res) {
          //登记成功，继续写卡
          this.intervalState = "processing";
          this.setLoopSet();
        }
      });
  }

  // 继续写卡
  continueWrite() {
    this.startWriteProcess();
  }

  // 结束写卡
  completeWrite() {
    if (!this.billNumber) return;
    if (Number(this.writeCardInfo?.writeCount) < Number(this.writeCardNum)) {
      this.$confirm(this.i18n("存在卡号未完成写卡，确定结束写卡吗？"), this.i18n("结束写卡"), {
        confirmButtonText: this.i18n("确认"),
        cancelButtonText: this.i18n("取消"),
      }).then(() => {
        this.stopWrite();
      });
    } else {
      this.stopWrite();
    }
  }

  stopWrite() {
    if (!this.billNumber) return;
    const loading = CommonUtil.Loading();
    WriteCardBillApi.finish(this.billNumber)
      .then((res) => {
        if (res.code === 2000) {
          this.initPage();
        } else {
          throw new Error(res.msg!);
        }
      })
      .catch((error) => {
        this.$message.error(error.message);
      })
      .finally(() => {
        loading.close();
      });
  }

  // 初始化写卡状态
  initPage() {
    this.intervalState = "initial";
    this.billNumber = null;
    this.recordList = [];
    this.form = new Form();
    this.writeCardNum = null;
    this.writeState = ProcessState.UNSTART;
  }

  // 查询开始卡号和结束卡号之间的可写卡数量
  checkCardQty() {
    if (!this.form.startCode || !this.form.endCode) return;
    if (!this.startCodeAvailable || !this.endCodeAvailable) return;
    const params = new WriteCardBillCalRequest();
    params.startCardCode = this.form.startCode;
    params.endCardCode = this.form.endCode;
    params.cardMedium = this.form.cardMedium;
    const loading = CommonUtil.Loading();
    WriteCardBillApi.calWriteQty(params)
      .then((res) => {
        if (res.code === 2000) {
          this.writeCardNum = res.data;
        } else {
          throw new Error(res.msg!);
        }
      })
      .catch((error) => {
        this.$message.error(error.message);
      })
      .finally(() => {
        loading.close();
      });
  }

  //前往卡模板详情页
  goCardDtl(templateCardNumber: string) {
    this.$router.push({ name: "prepay-card-tpl-dtl", query: { number: templateCardNumber } });
  }

  computeState(state: string) {
    return CommonUtil.computeState(state, [
      ["UNACTIVATED", this.i18n("/储值/预付卡/预付卡查询/列表页面/未激活"), "#FF9933"],
      ["USING", this.i18n("/储值/预付卡/预付卡查询/列表页面/使用中"), "#33CC00"],
      ["USED", this.i18n("/储值/预付卡/预付卡查询/列表页面/已使用"), "#7F36C1"],
      ["PRESENTING", this.i18n("/储值/预付卡/预付卡查询/列表页面/转赠中"), "#3366FF"],
      ["CANCELLED", this.i18n("/储值/预付卡/预付卡查询/列表页面/已作废"), "rgba(0, 0, 0, 0.25)"],
      ["LOST", this.i18n("/储值/预付卡/预付卡查询/列表页面/已挂失"), "#FFCC33"],
      ["FROZEN", this.i18n("/储值/预付卡/预付卡查询/列表页面/已冻结"), "#CC0033"],
      ["RECOVER", this.i18n("/储值/预付卡/预付卡查询/列表页面/已回收"), "#FF9933"],
      ["MADE", this.i18n("/储值/预付卡/预付卡查询/列表页面/已制卡"), "#7F36C1"],
      ["BLANK", this.i18n("空白卡"), "#FF9933"],
      ["BROKEN", this.i18n("坏卡"), "#CC0033"],
    ]);
  }

  computeAction(action: WriteCardAction) {
    switch (action) {
      case WriteCardAction.FAIL:
        return this.i18n("写卡失败");
      case WriteCardAction.SUCCESS:
        return this.i18n("写卡成功");
      case WriteCardAction.SKIP:
        return this.i18n("手动跳过");
      default:
        return "--";
    }
  }

  doSelectCardTemplate() {
    if (this.formDisable) {
      const route = this.$router.resolve({ name: 'prepay-card-tpl-dtl', query: { number: this.form.cardTemplate?.id } })
      window.open(route.href, '_blank')
      return
    }
    if (!this.writeCardType) {
      return this.$message.warning(this.i18n("请先填写起始卡号"));
    }
    const obj = new CardTemplate()
    obj.number = this.form.cardTemplate?.id
    obj.name = this.form.cardTemplate?.name
    const cardArr = this.form.cardTemplate?.id ? [obj] : []
    this.$refs.cardTemplateSelectorDialog.open(cardArr, 'single')
  }

  doCardTemplateSelected(arr: CardTemplate[]) {
    this.form.cardTemplate = new IdName()
    this.form.cardTemplate.id = arr[0].number
    this.form.cardTemplate.name = arr[0].name
    this.$refs.form.validateField('defaultCardTemplate')
  }

  defaultCardTemplateChange() {
    this.form.cardTemplate = null
    this.$refs.form.validateField('defaultCardTemplate')
  }
}
