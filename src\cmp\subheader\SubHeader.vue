<template>
    <div class="sub-header-view">
        <span class="title">{{title}}&nbsp;&nbsp;<slot name="icon"></slot></span>
        <span class="right"><slot name="btn"></slot></span>
    </div>
</template>

<script lang='ts' src='./SubHeader.ts'>

</script>

<style lang='scss'>
.sub-header-view{
    height: 60px;
    margin-top: 20px;
    line-height: 60px;
    padding-left: 20px;
    border-bottom: 1px solid #dfe2e5;
    font-size: 12px;
    background-color: white;
    .title{
        width:72px;
        height:24px;
        font-size:18px;
        font-weight:400;
        color:rgba(36,39,43,1);
        line-height:24px;
    }
    .right{
        display: inline-block;
        float: right;
        margin-right: 30px;
    }
}
</style>