<template>
  <div class="points-charge-speed-setting">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button @click="doSave" type="primary"
          v-if="hasOptionPermission('/营销/营销/节日有礼/会员日积分加速抵现', '规则维护')">{{formatI18n('/公用/按钮', '保存')}}</el-button>
        <el-button @click="doCancel" v-if="hasOptionPermission('/营销/营销/节日有礼/会员日积分加速抵现', '规则维护')">{{formatI18n('/公用/按钮', '取消')}}</el-button>
      </template>
    </BreadCrume>
    <div style="height: 95%;overflow: auto">
      <div style="margin: 30px">
        <div style="height: 46px;line-height: 46px;border: 1px solid #1cbd1c;background: #e7faf5;padding-left: 10px;margin-bottom: 20px">
          {{getTip(score, amount)}}</div>
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
          <el-form-item :label="formatI18n('/营销/积分活动/积分活动/积分抵现活动/立即新建', '抵现方式')" class="tip-form">
            <el-radio-group v-model="ruleForm.option" style="padding-top: 14px" @change="doOptionChange">
              <el-radio style="display: block;margin-bottom: 20px" label="first">
                <i18n k="/权益/积分/积分初始化/未初始化状态/积分抵现规则/会员日积分加速抵现/点击立即设置/抵现方式/按月/每月{0}日，每使用{1}积分，抵现{2}元">
                  <template slot="0">
                    &nbsp;
                    <el-form-item prop="day" style="display: inline-block" class="cur-form">
                      <el-select @change="doDayChange" :disabled="ruleForm.option !== 'first'" style="width: 80px;" v-model="ruleForm.day">
                        <el-option v-for="item in 31" :label="item" :key="item" :value="item">{{item}}</el-option>
                      </el-select>
                    </el-form-item>
                    &nbsp;
                  </template>
                  <template slot="1">
                    &nbsp;
                    <el-form-item prop="dayUse" style="display: inline-block" class="cur-form">
                      <el-input @change="doDayUseChange" :disabled="ruleForm.option !== 'first'" style="width: 80px;" v-model="ruleForm.dayUse">
                      </el-input>
                    </el-form-item>
                    &nbsp;
                  </template>
                  <template slot="2">
                    &nbsp;
                    <el-form-item prop="dayAmount" style="display: inline-block" class="cur-form">
                      <el-input @change="doDayAmountChange" :disabled="ruleForm.option !== 'first'" style="width: 80px;" v-model="ruleForm.dayAmount">
                      </el-input>
                    </el-form-item>
                    &nbsp;
                  </template>
                </i18n>
              </el-radio>
              <el-radio style="display: block;margin-bottom: 20px" label="second">
                <i18n k="/权益//权益/积分/积分初始化/未初始化状态/积分抵现规则/会员日积分加速抵现/点击立即设置/抵现方式/按周/每周{0}日，每使用{1}积分，抵现{2}元">
                  <template slot="0">
                    &nbsp;
                    <el-form-item prop="month" style="display: inline-block" class="cur-form">
                      <el-select @change="doMonthChange" :disabled="ruleForm.option !== 'second'" style="width: 80px;" v-model="ruleForm.month">
                        <el-option v-for="(item, index) in weeks" :key="item" :label="item" :value="index + 1">{{item}}</el-option>
                      </el-select>
                    </el-form-item>
                    &nbsp;
                  </template>
                  <template slot="1">
                    &nbsp;
                    <el-form-item prop="monthUse" style="display: inline-block" class="cur-form">
                      <el-input @change="doMonthUseChange" :disabled="ruleForm.option !== 'second'" style="width: 80px;" v-model="ruleForm.monthUse">
                      </el-input>
                    </el-form-item>
                    &nbsp;
                  </template>
                  <template slot="2">
                    &nbsp;
                    <el-form-item prop="monthAmount" style="display: inline-block" class="cur-form">
                      <el-input @change="doMonthAmountChange" :disabled="ruleForm.option !== 'second'" style="width: 80px;"
                        v-model="ruleForm.monthAmount">
                      </el-input>
                    </el-form-item>
                    &nbsp;
                  </template>
                </i18n>
              </el-radio>
            </el-radio-group>
            <div style="color: #909399">-
              {{formatI18n('/权益/积分/积分初始化/未初始化状态/积分抵现规则/会员日积分加速抵现/点击立即设置/如果每月3号，每使用10积分，抵现2元，当会员3号到店消费，有28个积分，那么他最多可使用20个积分抵现4元。')}}</div>
          </el-form-item>
          <!--<el-form-item label="说明" prop="remark">-->
          <!--<el-input style="width: 200px" type="textarea" v-model="ruleForm.remark"></el-input>-->
          <!--</el-form-item>-->
        </el-form>
      </div>
    </div>

  </div>
</template>

<script lang="ts" src="./PointsChargeSpeedSetting.ts">
</script>

<style lang="scss">
.points-charge-speed-setting {
  background-color: white;
  overflow: hidden;
  height: 100%;
  width: 100%;
  .el-radio-group {
    padding-top: 0px !important;
  }
  .tip-form {
    .el-form-item__label {
      &:before {
        content: "*";
        color: #ef393f;
        margin-right: 4px;
      }
    }
  }
}
</style>