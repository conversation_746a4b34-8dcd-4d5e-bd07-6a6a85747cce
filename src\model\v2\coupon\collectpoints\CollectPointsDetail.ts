import IdName from "model/common/IdName"
import MemberIdent from "model/common/member/MemberIdent"
import MutableNsid from "model/common/MutableNsid"

export default class CollectPointsDetail extends MemberIdent {
  // uuid
  uuid: Nullable<string> = null
  // 活动号
  activityNumber: Nullable<string> = null
  // 会员id
  memberId: Nullable<string> = null
  // 营销中心
  marketingCenter: Nullable<string> = null
  // 发生门店
  occurredOrg: Nullable<IdName> = null
  // 发生时间
  occurredTime: Nullable<Date> = null
  // 交易id
  tradeId: Nullable<MutableNsid> = null
  // 交易id
  originTradeId: Nullable<string> = null
  // 发生集点
  occurredPoints: Nullable<number> = null
  // 发生类型 只有OBTAIN和RETURN
  category: Nullable<OccurType> = null
  // 退回集点
  refundPoints: Nullable<number> = null
  // 创建时间
  created: Nullable<Date> = null
  // 组id
  groupId: Nullable<string> = null
  // 交易金额
  tradeAmount: Nullable<number> = null
}


export enum OccurType {
  // 获得
  OBTAIN = 'OBTAIN',
  // 消费
  DEDUCT = 'DEDUCT',
  // 清零
  CLEAN = 'CLEAN',
  // 退回
  RETURN = 'RETURN'
}

