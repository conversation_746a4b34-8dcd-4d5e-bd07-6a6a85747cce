

// 拼团记录查询
import {State} from "model/promotion/groupBookingActivity/State";


export default class GroupBookingRecordFilter {
  // uuid>
  uuidGreater: Nullable<string> = null
  // 活动号=
  activityNumberEquals: Nullable<string> = null
  // 团号=
  groupNumberEquals: Nullable<string> = null
  // 会员id=
  memberIdEquals: Nullable<string> = null
  // 拼团状态等于
  stateEquals: Nullable<State> = null
  // 拼团状态IN
  stateIn: State[] = []
  // 关团时间<
  endTimeLess: Nullable<Date> = null
  // 会员标识等于
  memberCodeEquals: Nullable<string> = null
  // 页数>=0
  page: number = 0
  // 页面大小>0
  pageSize: number = 0
}