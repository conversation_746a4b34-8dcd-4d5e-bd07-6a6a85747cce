import { Vue, Component, Prop } from 'vue-property-decorator';
import PlaceTemplateMixins from '../PlaceTemplateMixins';
import I18nPage from 'common/I18nDecorator';

@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理',
  ],
  auto: true
})
@Component({
  name: 'PageNavigation',
  components: {},
  mixins: [PlaceTemplateMixins],
})
export default class PageNavigation extends Vue {
  @Prop()
  componentItem: any;
  marginBottom = 10;
  currentIndex = 0;
  tabsActiveIndex = 0;
  // editTabs: any[] = [
  //   {
  //     name: '导航名称1',
  //     componentUuid: '1',
  //   },
  //   {
  //     name: '导航名称2',
  //     componentUuid: '2',
  //   },
  // ];

  mounted() { }
  handleClick(index) {
    // console.log(index);
    
    this.tabsActiveIndex = index;
  }
  get componentItemProps() {
    return this.componentItem.props;
  }
  get localProperty() {
    if (this.componentItem && this.componentItem.props && this.componentItem.props.propNavigationList) {
      const navigationList = this.componentItem.props.propNavigationList;
      return navigationList;
    } else {
      // 在这里添加你的默认导航列表，或者返回一个空数组
      return [];
    }
  }
  
  get ossSourceUrl() {
    return this.$store.state.credential.host + '/-/cms/thumbnail/';
  }
  get externalImage() {
    return this.ossSourceUrl + 'pic_waibuyemian.png';
  }

  get navClass() {
    if (this.localProperty.length <= 2 && this.localProperty.length >= 0) {
      return 'nav-2';
    } else if (this.localProperty.length === 3) {
      return 'nav-3';
    } else if (this.localProperty.length >= 4) {
      return 'nav-4';
    }
  }
}
