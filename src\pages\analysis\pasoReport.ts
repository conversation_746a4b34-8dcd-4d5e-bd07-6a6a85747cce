import { Component, Vue } from 'vue-property-decorator'
import store from "store/index";
@Component({
  name: 'pasoReport'
})
export default class PasoReport extends Vue {
  url: string = ''
  pasoList: any[] = []

  created() {
    console.log(store.state.pageUrls);
    let keys = Object.keys(store.state.pageUrls);
    let pasoList: any[] = [];
    keys.forEach((item)=> {
      if (item.indexOf("PASO") != -1 || item.indexOf("paso") != -1) {
				let arr = item.split("/");
				pasoList.push({
					key: item,
					url: store.state.pageUrls[item],
					title: arr[arr.length - 1],
				});
			}
    })
    console.log(pasoList);
    this.pasoList = pasoList
    this.getUrl();
  }

  getUrl() {
    this.url = decodeURI(this.$route.query.iframe as string)
  }

  doGoView(url: string) {
    this.$router.push({
      path: '/data-table/paso-report-view',
      query: {
        iframe: url
      }
    })
  }
}
