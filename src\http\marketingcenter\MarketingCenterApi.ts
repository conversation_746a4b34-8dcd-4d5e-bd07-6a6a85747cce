import ApiClient from "http/ApiClient";
import RSMarketingCenter from "model/common/RSMarketingCenter";
import RSMarketingCenterFilter from "model/common/RSMarketingCenterFilter";
import Response from "model/common/Response";
import RSSaveBatchMarketingCenterRequest from "model/common/RSSaveBatchMarketingCenterRequest";
export default class MarketingCenterApi {
	/**
	 * 查询营销中心
	 * 查询营销中心。
	 *
	 */
	static query(body: RSMarketingCenterFilter): Promise<Response<RSMarketingCenter[]>> {
		return ApiClient.server()
			.post(`/v1/marketing-center/query`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 查询当前账号的营销中心
	 *
	 */

	static queryUserMarketCenter(account: any): Promise<Response<RSMarketingCenter[]>> {
		return ApiClient.server()
			.post(`/v1/marketing-center/getByAccount?account=${account}`, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 删除营销中心
	 * 删除营销中心。
	 *
	 */
	static remove(id: string): Promise<Response<void>> {
		return ApiClient.server()
			.post(
				`/v1/marketing-center/remove`,
				{},
				{
					params: {
						id: id,
					},
				}
			)
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 批量保存
	 * 批量保存。
	 *
	 */
	static saveBatch(body: RSSaveBatchMarketingCenterRequest): Promise<Response<void>> {
		return ApiClient.server()
			.post(`/v1/marketing-center/saveBatch`, body, {})
			.then((res) => {
				return res.data;
			});
	}
}
