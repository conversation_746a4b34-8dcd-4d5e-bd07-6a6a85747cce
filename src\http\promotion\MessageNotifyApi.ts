/*
 * @Author: 黎钰龙
 * @Date: 2023-11-28 10:48:59
 * @LastEditTime: 2023-12-05 09:43:46
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\promotion\MessageNotifyApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import IdName from 'model/common/IdName'
import Response from 'model/common/Response'
import BWeixinMessageTemplateSeftSetting from 'model/promotion/BWeixinMessageTemplateSeftSetting'
import BWeixinMessageTemplateSetting from 'model/promotion/BWeixinMessageTemplateSetting'

export default class WeixinMessageNotifyApi {
  /**
   * 取得微信设置信息
   * 取得微信设置信息。
   * 
   */
  static gets(type: 'offiaccount' | 'applet'): Promise<Response<BWeixinMessageTemplateSetting[]>> {
    return ApiClient.server().post(`/v1/weixin-message-template/gets`, { type }, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 公众号模板 一键配置
   * 公众号模板 一键配置。
   * 
   */
  static setting(type: 'offiaccount' | 'applet'): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/weixin-message-template/setting`, { type }, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 用户通知 保存私有化
   * 用户通知 保存私有化。
   * 
   */
  static saveSeftSetting(body: BWeixinMessageTemplateSeftSetting): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/weixin-message-template/saveSeftSetting`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 用户通知 配置查询
   * 用户通知 配置查询。
   * 
   */
  static seftSetting(): Promise<Response<BWeixinMessageTemplateSeftSetting[]>> {
    return ApiClient.server().post(`/v1/weixin-message-template/seftSetting/get`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 查询自定义内容变量
  * 查询自定义内容变量
  * 
  */
  static getNotifyVariable(type: string): Promise<Response<IdName[]>> {
    return ApiClient.server().post(`/v1/weixin-message-template/getNotifyVariable`, { type }, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询自定义内容变量
   * 查询自定义内容变量
   *
   */
  static listNotifyVariable(types: string[]): Promise<Response<IdName[]>> {
    return ApiClient.server().post(`/v1/weixin-message-template/listNotifyVariable`, types, {
    }).then((res) => {
      return res.data
    })
  }
}