import ChannelRange from "model/common/ChannelRange";
import BBatchPointsExchangeCouponActivityLine from './BBatchPointsExchangeCouponActivityLine'
import GradeRange from 'model/common/GradeRange'
import DateTimeCondition from 'model/common/DateTimeCondition'
import StoreRange from "model/common/StoreRange";

export default class BBatchPointsExchangeCouponActivity {
  // 仅保存
  justSave: Nullable<boolean> = null
  // 活动主题代码
  topicCode: Nullable<string> = null
  // 活动主题名称
  topicName: Nullable<string> = null
  // 备注
  remark: Nullable<string> = null
  // 活动起始日期
  beginDate: Nullable<Date> = null
  // 活动截止日期
  endDate: Nullable<Date> = null
  // 活动时间条件
  dateTimeCondition: Nullable<DateTimeCondition> = new DateTimeCondition();
  // 营销中心
  marketingCenter: Nullable<string> = null
  // 渠道范围
  channelRange: Nullable<ChannelRange> = null
  // 活动门店
  stores: Nullable<StoreRange> = null
  // 会员等级范围
  gradeRange: Nullable<GradeRange> = null
  // 明细
  lines: BBatchPointsExchangeCouponActivityLine[] = []
}