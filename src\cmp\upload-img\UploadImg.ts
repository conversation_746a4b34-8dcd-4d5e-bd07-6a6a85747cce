/*
 * @Author: 黎钰龙
 * @Date: 2024-04-15 14:32:44
 * @LastEditTime: 2025-04-24 16:11:01
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\upload-img\UploadImg.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import EnvUtil from 'util/EnvUtil';
import { Component, Model, Prop, Vue } from 'vue-property-decorator';
@Component({
  name: 'UploadImg',
  components: {}
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/营销/大转盘活动'
  ],
  auto: true
})
export default class UploadImg extends Vue {
  @Model('change') imgUrl: string
  @Prop({ type: Number, default: 300 }) maximum: number;  //限制图片大小
  @Prop({ type: Boolean, default: false }) isCircular: boolean; //样式是否为圆形
  @Prop({ type: Boolean, default: true }) isShowKb: boolean; //限制图小大小提示是否为kb
  @Prop({ type: Array, default: () => [] }) customImageTypes: string[]; //自定义图片类型
  @Prop({ type: String }) customErrMsg: string; //自定义图片类型错误提示

  get imgValue() {
    return this.imgUrl
  }
  set imgValue(value: any) {
    const res = value ?? null
    this.$emit('change', res)
  }

  get uploadHeaders() {
    let locale = sessionStorage.getItem('locale')
    const headers: any = {
      locale: locale ? locale : 'zh_CN',
      time_zone: new Date().getTimezoneOffset(),
      marketingCenter: sessionStorage.getItem('marketCenter')
    }
    const authorization = EnvUtil.getUniAuthorization()
    if (authorization) {
      headers.authorization = authorization
    }
    return headers
  }

  get uploadUrl() {
    return EnvUtil.getServiceUrl() + "v1/upload/upload";
  }

  deleteImg() {
    this.imgValue = ''
  }

  onImageUploadSuccess(response: any, file: any, fileList: any) {
    if (response && response.code === 2000) {
      this.$message.success(this.i18n("/储值/预付卡/卡模板/编辑页面/上传成功"));
      this.imgValue = response.data.url;
      this.$emit('afterSuccess')
    } else {
      this.$message.error(response.msg);
    }
  }

  // 图片上传前的校验
  beforeAvatarUpload(file: any) {
    const isJPG = this.customImageTypes.length > 0 ? this.customImageTypes.includes(file.type) : ["image/jpeg", "image/png", "image/jpg", 'image/gif'].includes(file.type);
    const isLt2M = file.size / 1024 < (this.isShowKb ? this.maximum : this.maximum * 1024);
    if (!isJPG) {
      this.$message.error(this.customErrMsg || this.i18n("/页面/页面管理/上传图片只能是JPG/JPEG/GIF/PNG格式!"));
      return false;
    }
    if (!isLt2M) {
      if (this.isShowKb) {
        this.$message.error(this.i18n("/页面/页面管理/上传图片大小不能超过{0}", [this.maximum + "KB"]));
      } else {
        this.$message.error(this.i18n("/页面/页面管理/上传图片大小不能超过{0}", [this.maximum + "MB"]));
      }
      return false;
    }
    return true;
  }
};