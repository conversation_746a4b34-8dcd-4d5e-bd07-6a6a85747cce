import {Component} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import AbstractSelectDialog from './AbstractSelectDialog';
import MemberApi from "http/member_standard/MemberApi";
import Member from "model/member/Member";
import MemberFilter from 'model/member_v2/member/MemberFilter'

@Component({
  name: 'MemberSelectorDialog',
  components: {
    FormItem
  }
})
export default class MemberSelectorDialog extends AbstractSelectDialog<Member> {
  memberFilter: MemberFilter = new MemberFilter()

  identPlaceholder = ''
  identType = "all"
  identCode = null

  created() {
    this.memberFilter.stateEquals = 'Using'
    this.identTypeChange()
  }

  identTypeChange() {
    if (this.identType == 'mobile') {
      // 手机号码
      this.identPlaceholder = this.formatI18n('/会员/会员资料', '请输入手机号')
    } else if (this.identType == 'hdCardCardNumber') {
      // 实体卡号
      this.identPlaceholder = this.formatI18n('/会员/会员资料', '请输入实体卡号')
    } else if (this.identType == 'crmCode') {
      // 会员号
      this.identPlaceholder = this.formatI18n('/会员/会员资料', '请输入会员号')
    } else {
      this.identPlaceholder = this.formatI18n('/会员/会员资料', '请输入手机号/会员号/实体卡号')
    }
    this.identCode = null
  }

  getRegisterChannel(registerChannel: any) {
    // 如果type存在【third，alipay，weixin，store，phoenix】，根据type去取id
    if (registerChannel && registerChannel.type) {
      if (registerChannel.type === 'third') {
        return this.formatI18n('/会员/会员资料', '第三方')
      } else if (registerChannel.type === 'alipay') {
        return this.formatI18n('/会员/会员资料', '支付宝')
      } else if (registerChannel.type === 'weixin') {
        return this.formatI18n('/会员/会员资料', '微信')
      } else if (registerChannel.type === 'store') {
        return this.formatI18n('/会员/会员资料', '门店注册')
      } else if (registerChannel.type === 'phoenix') {
        return 'CRM'
      } else {
        if (registerChannel && registerChannel.id) {
          return registerChannel.id
        } else {
          return '--'
        }
      }
    } else {
      return '--'
    }
    // 如果type不存在，但是id存在，直接显示id
  }

  reset() {
    this.memberFilter = new MemberFilter()
    this.memberFilter.stateEquals = 'Using'
    this.identPlaceholder = ''
    this.identType = "all"
    this.identCode = null
  }

  getId(ins: Member): string {
    return ins.memberId!;
  }

  getName(ins: Member): string {
    return ins.name!;
  }

  getResponseData(response: any): any {
    return response.data
  }

  queryFun(): Promise<any> {
    this.memberFilter.page = this.page.currentPage - 1
    this.memberFilter.pageSize = this.page.size


    this.memberFilter.identCode = null
    this.memberFilter.mobileEquals = null
    this.memberFilter.identCodeEquals = null
    this.memberFilter.hdCardCardNumberEquals = null
    this.memberFilter.crmCodeEquals = null

    console.log(this.identType, this.identCode)
    if (this.identCode != null) {
      if (this.identType == 'mobile') {
        // 手机号码
        this.memberFilter.mobileEquals = this.identCode
      } else if (this.identType == 'hdCardCardNumber') {
        // 实体卡号
        this.memberFilter.hdCardCardNumberEquals = this.identCode
      } else if (this.identType == 'crmCode') {
        // 会员号
        this.memberFilter.crmCodeEquals = this.identCode
      } else {
        this.memberFilter.identCodeEquals = this.identCode
      }
    }
    return MemberApi.query(this.memberFilter)
  }
}
