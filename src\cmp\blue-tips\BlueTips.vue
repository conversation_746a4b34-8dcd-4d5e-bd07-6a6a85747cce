<template>
  <div class="blue-tips-container">
    <div class="title">
      <img src="~assets/image/auth/info3.png" />
      <slot name="title"></slot>
    </div>
    <div class="content">
      <slot name="content"></slot>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
@Component({
  name: "BlueTips",
})
export default class BlueTips extends Vue {}
</script>

<style lang="scss">
.blue-tips-container {
  width: 100%;
  padding: 12px;
  background: #eaf3ff;
  border-radius: 2px;
  border: 1px solid #318bff;
  .title {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    color: #36445a;
    margin-bottom: 4px;
    img {
      width: 20px;
      height: 20px;
      margin-right: 6px;
    }
  }
  .content {
    width: 100%;
    margin-right: 24px;
    box-sizing: border-box;
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #36445a;
  }
}
</style>