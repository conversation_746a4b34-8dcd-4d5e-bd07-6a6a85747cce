import {Component, Prop, Vue, Watch} from 'vue-property-decorator'
import ListWrapper from 'cmp/list/ListWrapper.vue'
import PrePayBalanceApi from 'http/prepay/balance/PrePayBalanceApi'
import PrepayAccountHst from 'model/prepay/balance/PrepayAccountHst'
import PrepayAccountTransaction from 'model/prepay/balance/PrepayAccountTransaction'
import PrepayAccountTransactionFilter from 'model/prepay/balance/PrepayAccountTransactionFilter'
import PrePayConfigApi from 'http/prepay/config/PrePayConfigApi'
import IdName from 'model/entity/IdName'

@Component({
  name: 'CheckWater',
  components: {
    ListWrapper
  }
})
export default class CheckWater extends Vue {
  coupons: any = []
  @Prop()
  datas: any
  @Prop()
  title: any
  @Prop()
  uuid: any

  @Prop({
    type: Boolean,
    default: false
  })
  dialogShow: boolean
  query: {
    page: 0,
    pageSize: 0
  }
  detail: PrepayAccountHst = new PrepayAccountHst()
  transactions: PrepayAccountTransaction[] = []
  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  enableMultipleAccount: boolean = false
  tableHeight: number = 0
  account: IdName = new IdName()

  @Watch('dialogShow')
  onDialogSHow(value: boolean) {
    if (value) {
      this.account.id = this.uuid as any
      this.account.name = this.$route.query.name as any
      this.getDetail()
      this.getList()
      this.getPrePermission()
    }
  }

  mounted() {
    window.onresize = () => {
      this.setTableSize()
    };
  }
  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.getList()
  }
  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.getList()
  }
  /**
   * 表格排序条件
   */
  onSortChange({ column, prop, order }: any) {
    // todo
  }
  doBeforeClose(done: any) {
    this.$emit('dialogClose')
    done()
  }
  doModalClose() {
    this.$emit('dialogClose')
  }
  doCancel() {
    this.$emit('dialogClose')
  }
  doCheckGoods() {
    // todo
  }
  private getDetail() {
    PrePayBalanceApi.querySum(this.uuid).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.detail = resp.data
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private parseCategory(category: string) {
    switch (category) {
      case 'DEPOSIT':
        return '充值'
      case 'DEPOSIT_REFUND':
        return '充值退款'
      case 'PAY':
        return '支付'
      case 'PAY_REFUND':
        return '支付退款'
      case 'PAY_ROLLBACK':
        return '支付回滚'
      case 'TRANSFER_IN':
        return '转账出'
      case 'TRANSFER_OUT':
        return '转账入'
      case 'ADJUST':
        return '调整'
      case 'MERGE':
        return '会员合并'
    }
  }

  private getList() {
    let filter = new PrepayAccountTransactionFilter()
    filter.page = this.page.currentPage - 1
    filter.pageSize = this.page.size
    filter.accountEquals = this.uuid
    PrePayBalanceApi.queryHst(filter).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.transactions = resp.data
        this.page.total = resp.total
        this.setTableSize()
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private setTableSize() {
    let table = document.getElementsByClassName('current-page')[0] as any
    if (table) {
      this.tableHeight = table.offsetHeight - 80
    }
  }

  private getPrePermission() {
    PrePayConfigApi.get().then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.enableMultipleAccount = resp.data.enableMultipleAccount
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
}