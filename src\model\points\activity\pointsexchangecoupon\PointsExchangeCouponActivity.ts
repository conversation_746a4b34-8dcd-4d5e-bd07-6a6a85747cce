/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-09-14 10:09:33
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\points\activity\pointsexchangecoupon\PointsExchangeCouponActivity.ts
 * 记得注释
 */
import ActivityBody from "model/common/ActivityBody";
import CouponItem from "model/common/CouponItem";
import ChannelRange from "model/common/ChannelRange";
import GradesRange from "model/common/GradeRange";
import DateTimeCondition from "model/common/DateTimeCondition";
import PushGroup from "model/precisionmarketing/pushplan/PushGroup";
import {ExpireRefundType} from "model/weixin/weixinIssueCouponActivity/ExpireRefundType";

export default class PointsExchangeCouponActivity {
	// 仅保存
	justSave: Nullable<boolean> = null;
	// 活动信息
	activityBody: Nullable<ActivityBody> = null;
	// 活动时间限制
	dateTimeCondition = new DateTimeCondition();
	// 每人每天限量
	maxMemberPerDayTime: Nullable<number>;
	// 活动总限
	maxStockQty: Nullable<number> = null;
	// 活动每天限量
	maxIssueDayTimes: Nullable<number> = null;
	// 每人限量
	maxMemberQuotaQty: Nullable<number> = null;
	// 活动图片
	imageId: Nullable<string> = null;
	// 兑换券信息
	couponItem: Nullable<CouponItem> = null;
	// 兑换所需积分
	points: Nullable<number> = null;
	// 兑换所需金额
	cost: Nullable<number> = null;
	//渠道范围
	channelRange: Nullable<ChannelRange> = null;
	// 参与会员等级
	gradeRange: Nullable<GradesRange> = null;
 	 // 营销中心
 	 marketingCenter: Nullable<string> = null
	// 显示排序
	sequence: Nullable<number> = null;
  	// 参与人群
  	rule: Nullable<PushGroup> = null
	// 过期退款方式
	expireRefundType: Nullable<ExpireRefundType> = ExpireRefundType.MANUAL_REFUND
	// 是否允许用户申请退款
	enableUserApplyRefund: Nullable<boolean> = false
	// 是否要人工审核
	enableManualAudit: Nullable<boolean> = true
}
