<template>
  <div class="point-coupon">
    <el-form :model="ruleForm" :rules="rules" class="demo-ruleForm" label-width="120px" ref="ruleForm">
      <div v-if="$route.query.from === 'edit'"
        style="height: 48px;line-height: 48px;background-color: #3366ff19;margin: 0 20px;padding-left: 20px;margin-bottom: 10px">
        <img style="position: relative;top: 5px;" src="~assets/image/auth/info3.png"
          alt="">{{ formatI18n('/权益/券/券模板/编辑界面/用券规则下的提示/券模板修改后，已发出的券的有效期仍以原来的为准，其余用券规则以修改后的为准。') }}
      </div>

      <div class="setting-container">
        <!-- 基础信息 -->
        <div class="setting-block">
          <div class="section-title">{{i18n('基础信息')}}</div>
          <!-- 券名称 -->
          <CouponName :ruleForm="ruleForm" :copyFlag='copyFlag'>
            <template slot="slot">
              <el-input maxlength="128" style="width: 390px" v-model="ruleForm.name" @change="doFormItemChange" :placeholder="i18n('请输入')"></el-input>
            </template>
          </CouponName>
          <!-- 券图标 -->
          <CouponTemplateLogo :original-logo-url="ruleForm.logoUrl" @logoUrlCallBack="logoUrlCallBack"></CouponTemplateLogo>
          <!-- 使用须知 -->
          <UseCouponDesc :ruleForm="ruleForm" :isShowTips="false" :descRequired="false">
            <template slot="slot">
              <el-input :maxlength="remarkMaxlength" style="width: 390px;" type="textarea" v-model="ruleForm.couponProduct"
                :placeholder="remarkPlaceholder" @change="doFormItemChange">
              </el-input>
            </template>
          </UseCouponDesc>
        </div>

        <!-- 优惠设置 -->
        <div class="setting-block">
          <div class="section-title">{{i18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/优惠设置')}}</div>
          <!-- 券可换 -->
          <el-form-item :label="formatI18n('/营销/券礼包活动/核销第三方券', '券可换')" prop="point">
            <el-input style="width: 120px;" v-model="ruleForm.pointAmount" @change="doPointsChange">
              <template slot="append">{{formatI18n('/公用/券模板/积分')}}</template>
            </el-input>
          </el-form-item>
        </div>

        <!-- 用券时间 -->
        <div class="setting-block">
          <div class="section-title">{{i18n('/营销/券礼包活动/券查询/用券时间')}}</div>
          <!-- 券有效期 -->
          <CouponEffectPeriod ref="couponEffectPeriod" :specialMode="true" v-model="ruleForm" :options="options" @change="doFormItemChange">
          </CouponEffectPeriod>
        </div>

        <!-- 用券范围 -->
        <div class="setting-block">
          <div class="section-title">{{i18n('用券范围')}}</div>
          <!-- 用券门店 -->
          <el-form-item :label="formatI18n('/公用/券模板', '用券门店')" prop="storeRange">
            <ActiveStore :isOldActivity="false" ref="activeStore" :sameStore="sameStore" :enableStore="enableStore" v-model="ruleForm.storeRange" @change="doStoreChange">
            </ActiveStore>
          </el-form-item>
        </div>
      </div>

      <div class="setting-container">
        <div class="setting-block">
          <div class="section-title">
            <span>{{i18n('高级设置')}}</span>
            <span class="telescoping" @click="telescopingChange">
              <template v-if="!telescoping">{{i18n('/公用/查询条件/收起')}}<i class="el-icon-arrow-up"></i></template>
              <template v-else>{{i18n('/公用/查询条件/展开')}}<i class="el-icon-arrow-down"></i></template>
            </span>
            <span class="gray-tips" style="margin-left:12px">{{i18n('券码生成规则、标签等')}}</span>
          </div>
          <div v-show="!telescoping">
            <!-- 券码生成规则 -->
            <CouponCodeRules :ruleForm="ruleForm" :copyFlag="copyFlag" :wxScanForCoupon="wxScanForCoupon">
              <template slot="slot">
                <el-input :placeholder="formatI18n('/营销/券礼包活动/券礼包活动/新建导出券码发券', '请输入6位以内的数字或字母')" maxlength="6" style="width: 325px"
                  v-model="ruleForm.prefix" @change="doFormItemChange">
                </el-input>
              </template>
            </CouponCodeRules>
            <!-- 标签 -->
            <CouponTemplateLabel class="cur-from-item" v-model="ruleForm.templateTag" :templateId="ruleForm.templateId" @change="doFormItemChange" />
          </div>
        </div>
      </div>
    </el-form>

  </div>
</template>

<script lang="ts" src="./PointCoupon.ts">
</script>

<style lang="scss">
.point-coupon {
  padding-bottom: 30px;
  .setting-container {
    .setting-block {
      .section-title {
        .telescoping {
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #007eff;
          margin-left: 12px;
          cursor: pointer;
          i {
            color: #242633;
          }
        }
      }
    }
  }

  .coupon-step,
  .cur-form-item {
    .el-form-item__label {
      &:before {
        content: "*";
        color: #ef393f;
        margin-right: 4px;
      }
    }
  }

  .rule-table {
    margin-top: 10px;
    width: 70%;

    .rule-table-header {
      padding: 0 10px;
      background-color: #e6e6e6;
      border: 1px solid #e6e6e6;
    }

    .rule-table-line {
      padding: 10px;
      border: 1px solid #e6e6e6;
      border-top: 0;
    }

    .opt-col {
      a + a {
        margin-left: 10px;
      }
    }
  }

  .cur-from-item {
    height: auto !important;

    .el-radio {
      height: auto !important;
      line-height: 40px;
    }
  }
}
</style>