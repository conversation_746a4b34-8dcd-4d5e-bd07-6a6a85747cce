/*
 * @Author: haiding <EMAIL>
 * @Date: 2025-02-23 18:18:07
 * @LastEditors: haiding <EMAIL>
 * @LastEditTime: 2025-03-05 11:40:30
 * @FilePath: \new-kequn\src\pages\analysis\vipMember\cmp\ConditionDateSelect.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/*
 * @Author: 黎钰龙
 * @Date: 2025-02-10 10:56:07
 * @LastEditTime: 2025-03-05 11:35:11
 * @LastEditors: haiding <EMAIL>
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\analysis\cmp\CustomRangeDialog\CustomRangeDialog.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import { IntervalType } from 'model/default/IntervalType';


@Component({
  name: 'CustomDateSelect',
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/数据/数据洞察'
  ],
  auto: true
})
export default class ConditionDateSelect extends Vue {
  $refs: any
  visible: boolean = false
  @Prop() propName:string = ''
  @Prop() propDisplayIntervalType: IntervalType
  @Prop({ type: Boolean, default: true }) editable : Boolean
  open() {
    this.visible = true
  }

  // 提交数据
  submitDateChoice() {
    if (this.displayIntervalType) {
      this.$emit('submit', this.displayIntervalType)
      this.visible = false
    }
  }

    @Watch('propDisplayIntervalType', {immediate:  true })
    handle(propDisplayIntervalType:IntervalType) {
      console.log('handle',propDisplayIntervalType)
      this.displayIntervalType = propDisplayIntervalType
    }


  displayIntervalType: IntervalType = IntervalType.YEAR
};