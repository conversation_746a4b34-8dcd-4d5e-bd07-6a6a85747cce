<template>
  <el-form inline :label-position="labelPosition" :model="value" :rules="rules" ref="form" class="global-set">
    <el-form-item :label="i18n('购买按钮')" prop="propTitle">
      <div class="text">{{ i18n('免费券无需设置，即使设置也不生效，免费券显示“免费领取”')}}</div>
      <el-radio v-for="item in options" :key="item.key" :label="item.key" v-model="value.propBuyBtn" @change="handleChange">
        {{ item.caption }}
      </el-radio>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" src="./BuyButton.ts"></script>

<style lang="scss" scoped>
.global-set {
  .text {
    font-weight: 400;
    font-size: 13px;
    color: #a1a6ae;
    line-height: 18px;
  }
}
</style>
