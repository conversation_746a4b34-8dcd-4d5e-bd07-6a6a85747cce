<template>
    <div class="batch-operate-dtl">
        <BreadCrume :panelArray="panelArray">
            <template slot="operate">
                <el-button type="primary"
                           v-if="billDtl.state !== 'AUDITED'&&hasOptionPermission('/会员/会员管理/会员批量操作单', '单据审核')"
                           @click="doAudit">
                    {{formatI18n('/公用/按钮', '审核')}}
                </el-button>
                <el-button
                        v-if="billDtl.source === 'create' && billDtl.state !== 'AUDITED' && hasOptionPermission('/会员/会员管理/会员批量操作单', '单据维护')"
                        @click="doModify">
                    {{formatI18n('/公用/按钮', '修改')}}
                </el-button>
            </template>
        </BreadCrume>
        <div style="overflow: auto;height: 95%">
            <div class="top-wrap">
                <div class="left">
                    <div class="back">
                        <img src="~assets/image/storevalue/back.png">
                    </div>
                </div>
                <div class="right">
                    <div class="top">
                        <div class="item1">
                            <div class="bill">{{formatI18n('/权益/积分/积分调整单/查询条件', '单号')}}：{{billDtl.billNumber}}</div>
                            <!-- <div class="name">{{formatI18n('/权益/积分/积分调整单/标题', '积分调整单')}}</div> -->
                        </div>
                        <div class="item2">
                            <div class="desc">{{formatI18n('/权益/积分/积分调整单/列表/列名', '状态')}}</div>
                            <div class="state">
                                <el-tag v-if="billDtl.state === 'AUDITED'" type="success">{{formatI18n('/公用/过滤器',
                                    '已审核')}}
                                </el-tag>
                                <el-tag type="warning" v-if="billDtl.state !== 'AUDITED'">{{formatI18n('/公用/过滤器',
                                    '未审核')}}
                                </el-tag>
                            </div>
                        </div>
                    </div>
                    <!-- <div class="bottom">
                        <div class="account-info"
                             v-if="billDtl && billDtl.detailRemark && billDtl.detailRemark[0] && billDtl.detailRemark[0][0]">
                        <span v-html="getDetailStr((parseInt(billDtl.detailRemark[0][0])).toFixed(0),
                        $options.filters.amount(parseFloat(billDtl.detailRemark[0][1])),$options.filters.amount(parseFloat(billDtl.detailRemark[0][2])))"></span>
                        </div>
                        <div class="account-info" v-if="showOrg">
                            <span>{{getOrgStr()}}</span>
                        </div>
                    </div> -->
                </div>
            </div>
            <div class="row-height"></div>
            <div class="center-wrap">
                <div>
                    <p class="sub-item">{{formatI18n('/权益/积分/积分调整单详情/明细', '调整明细')}}</p>
                    <el-table :data="queryDtl">
                        <el-table-column fixed :label="formatI18n('/权益/积分/积分调整单详情/明细', '序号')" prop="lineNo" width="150"
                                         align="left">
                            <template slot-scope="scope">
                                {{scope.$index + 1}}
                            </template>
                        </el-table-column>
                        <el-table-column fixed :label="formatI18n('/权益/积分/新建积分调整单/条目', '手机号')" prop="mobile" width="180"
                                         align="left">
                            <template slot-scope="scope">
                                {{scope.row.mobile ? scope.row.mobile : '--'}}
                            </template>
                        </el-table-column>
                        <el-table-column fixed :label="formatI18n('/权益/积分/新建积分调整单/条目', '会员号')" prop="mobile" width="180"
                                         align="left">
                          <template slot-scope="scope">
                            {{scope.row.hdCardMbrId ? scope.row.hdCardMbrId : '--'}}
                          </template>
                        </el-table-column>
                        <el-table-column fixed :label="formatI18n('/权益/积分/新建积分调整单/条目', '实体卡号')" prop="mobile" width="180"
                                         align="left">
                          <template slot-scope="scope">
                            {{scope.row.hdCardCardNum ? scope.row.hdCardCardNum : '--'}}
                          </template>
                        </el-table-column>
                        <el-table-column :label="formatI18n('/会员/会员资料/IC卡卡面卡号')" prop="icCardNo" width="180"
                                         align="left">
                          <template slot-scope="scope">
                            {{scope.row.icCardNo ? scope.row.icCardNo : '--'}}
                          </template>
                        </el-table-column>
                        <el-table-column :label="formatI18n('/会员/会员资料/车牌号')" prop="carNos" width="180"
                                         align="left">
                          <template slot-scope="scope">
                            <div v-if="scope.row.carNos && scope.row.carNos.length > 0">
                                <div v-for="(item, index) in scope.row.carNos" :key="index">{{item}}</div>
                            </div>
                            <div v-else>
                                --
                            </div>
                          </template>
                        </el-table-column>
                        <el-table-column :label="i18n('操作时储值金额')" prop="occurAmount"
                                         width="180">
                            <template slot-scope="scope">
                                <div v-for="(item, index) in scope.row.accountBalances" :key="index">
                                    {{item.accountName}}：{{item.balance}}
                                </div>
                                <div v-if="scope.row.accountBalances === null || scope.row.accountBalances.length === 0">
                                    -
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column :label="i18n('操作时积分余额')" prop="occurAmount"
                                         width="180"
                                         align="left">
                            <template slot-scope="scope">
                                {{scope.row.points}}
                            </template>
                        </el-table-column>
                        <el-table-column :label="i18n('操作时可用券')" prop="reason" width="180"
                                         align="left">
                            <template slot-scope="scope">
                                <span v-if="scope.row.coupons.length === 0">{{scope.row.coupons.length}} {{ formatI18n("/营销/券礼包活动/券礼包活动/张") }}</span>
                                <el-button type="text" v-else @click="showDialog(scope.row.memberId)">{{scope.row.coupons.length}} {{ formatI18n("/营销/券礼包活动/券礼包活动/张") }}</el-button>
                            </template>
                        </el-table-column>
                        <el-table-column :label="i18n('操作组织')" prop="reason" width="180"
                                         align="left">
                            <template slot-scope="scope">
                                {{billDtl.occurredOrg.name}}
                            </template>
                        </el-table-column>
                        <el-table-column :label="i18n('会员状态调整为')" prop="reason" width="180"
                                         align="left">
                            <template slot-scope="scope">
                                {{stateText(scope.row.newState)}}
                            </template>
                        </el-table-column>
                        <el-table-column :label="formatI18n('/权益/积分/新建积分调整单/条目', '说明')" prop="remark" align="center">
                            <template slot-scope="scope">
                                <div style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;"
                                     :title="scope.row.remark">{{scope.row.remark | strFormat}}
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                    <div style="margin-top: 15px">
                        <el-pagination
                                :current-page="page.currentPage"
                                :page-size="page.size"
                                :page-sizes="[10, 20, 30, 40]"
                                :total="page.total"
                                @current-change="onHandleCurrentChange"
                                @size-change="onHandleSizeChange"
                                background
                                layout="total, prev, pager, next, sizes,  jumper">
                        </el-pagination>
                    </div>
                </div>
            </div>
            <div class="row-height"></div>
            <div class="foot-wrap">
                <div>
                    <p class="sub-item">{{formatI18n('/权益/积分/积分调整单详情', '操作日志')}}</p>
                    <el-table :data="billDtl.logs">
                        <el-table-column :label="formatI18n('/权益/积分/积分调整单详情/操作日志列表', '操作类型')" prop="type">
                            <template slot-scope="scope">
                                {{scope.row.type}}
                            </template>
                        </el-table-column>
                        <el-table-column :label="formatI18n('/权益/积分/积分调整单详情/操作日志列表', '操作人')" prop="operator">
                            <template slot-scope="scope">
                                {{scope.row.operator}}
                            </template>
                        </el-table-column>
                        <el-table-column :label="formatI18n('/权益/积分/积分调整单详情/操作日志列表', '操作时间')" prop="occurredTime">
                            <template slot-scope="scope">
                                {{scope.row.occurredTime | dateFormate3}}
                            </template>
                        </el-table-column>

                    </el-table>
                </div>
            </div>
        </div>
        <CouponsDialog :dialogShow="dialogShow" :memberId="currentMemberId" @dialogClose="dialogClose" :title="formatI18n('/会员/会员资料/详情界面/会员资产/如果有可用券的点击可用券的数字', '当前会员可用券')" />
    </div>
</template>

<script lang="ts" src="./BatchOperateDtl.ts">
</script>

<style lang="scss">
    .batch-operate-dtl {
        width: 100%;
        height: 100%;
        background-color: white;
        overflow: hidden;
        .top-wrap {
            display: flex;
            flex-direction: row;
            .left {
                width: 80px;
                height: 80px;
                display: flex;
                align-items: center;
                justify-content: center;
                .back {
                    width: 48px;
                    height: 48px;
                    border-radius: 100%;
                    background-color: rgba(242, 242, 242, 1);
                    img {
                        width: 24px;
                        height: 24px;
                        position: relative;
                        top: 13px;
                        left: 12px;
                    }
                }
            }
            .right {
                display: flex;
                flex: 1;
                flex-direction: column;
                position: relative;
                .top {
                    display: flex;
                    height: 105px;
                    border-bottom: 1px solid rgba(242, 242, 242, 1);
                    margin-right: 20px;
                    .item1 {
                        .bill {
                            margin-top: 16px;
                            color: rgba(51, 51, 51, 0.***************);
                        }
                        .name {
                            font-weight: 500;
                            margin-top: 8px;
                            font-size: 20px;
                        }
                    }
                    .item2 {
                        padding-left: 70px;
                        padding-top: 16px;
                        .desc {
                            color: rgba(51, 51, 51, 0.***************);
                        }
                        .state {
                            font-weight: 500;
                            margin-top: 8px;
                            font-size: 20px;
                        }
                    }
                }
                .bottom {
                    padding-bottom: 20px;
                    .account-info {
                        margin-top: 10px;
                    }
                    .red {
                        color: red;
                    }
                    .green {
                        color: #008000;
                    }
                }
            }
        }
        .row-height {
            height: 20px;
            background-color: rgba(242, 242, 242, 1);
        }
        .center-wrap, .foot-wrap {
            padding: 20px;
        }
        .sub-item {
            font-size: 16px;
            padding-top: 20px;
            margin-bottom: 10px;
        }
        .el-table__body .el-table__row td {
            border-bottom: 1px solid #d7dfeb !important;
        }
    }
</style>