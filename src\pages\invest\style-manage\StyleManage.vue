<template>
  <div class="shop-entry-rule-container">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button @click="doSave" type="primary" v-if="hasOptionPermission('/设置/小程序装修/风格管理', '设置')">
          {{ i18n("保存") }}
        </el-button>
        <el-button @click="doRestoreDefault" v-if="hasOptionPermission('/设置/小程序装修/风格管理', '设置') ">
          {{i18n('恢复默认')}}
          <el-tooltip class="item" effect="dark" :content="i18n('恢复默认是C端恢复到系统风格，自定义设置的风格将不生效')" placement="bottom-start">
            <i class="el-icon-question" style="margin-left:2px;"></i>
          </el-tooltip>
        </el-button>
      </template>
    </BreadCrume>
    <div class="current-page">
      <el-form :model="config" :rules="rules" label-width="120px" ref="form">
        <el-form-item :label="i18n('主题颜色')" prop="themeColor">
          <el-color-picker @change="uploadColorChange('themeColor')" v-model="config.themeColor"></el-color-picker>
        </el-form-item>
        <el-form-item :label="i18n('辅助颜色')" prop="auxiliaryColor">
          <el-color-picker @change="uploadColorChange('auxiliaryColor')" v-model="config.auxiliaryColor"></el-color-picker>
        </el-form-item>
        <el-form-item :label="i18n('加载动画')" prop="loadAnimation">
          <div class="gray-tips">
            <div>
              {{ i18n('页面信息加载缓慢时会显示加载动画') }}
              <el-popover
                  placement="bottom-start"
                  trigger="click"
                  popper-class="custom-image-popover">
                <div class="popover-image-container">
                  <img
                      src="~assets/image/invest/styleManagePageExample.png"
                      alt="示例图片"
                      style="max-width: 100%; display: block;">
                  <!-- 第二张图片叠加在中心位置 -->
                  <img
                      :src="config.loadAnimation"
                  alt=""
                  class="overlay-image">
                </div>
                <span
                    style="color: #1b7eff; font-weight: bold;"
                    class="action-hover_download"
                    slot="reference">
                    {{ i18n('查看示例') }}
                </span>
              </el-popover>
            </div>
            <div style="margin-top: -15px">
                {{i18n('建议尺寸136*136px，支持.gif格式，大小不超过2M')}}
            </div>
          </div>

          <UploadImg v-model="config.loadAnimation" @change="uploadImgChange('loadAnimation')"
                     :customImageTypes='["image/gif"]'
                     :customErrMsg="i18n('上传图片只能是GIF格式!')" :isShowKb="false" :maximum="1">
          </UploadImg>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts" src="./StyleManage.ts">
</script>

<style scoped lang="scss">
.shop-entry-rule-container {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: hidden;

  .current-page {
    margin-top: 30px;
    margin-left: 30px;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 30px;
  }

  .form-item-tip {
    color: #999999;
  }
}

.action-hover_download {
  cursor: pointer;
  /* 添加手型光标 */
}

.custom-image-popover {
  .popover-image-container {
    padding: 20px 20px 0 20px !important;
    width: 254px;
    height: 500px;
    background: #F0F2F6;
    border-radius: 8px;
  }
  .overlay-image {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-width: 30%; /* 调整为适合的叠加图片大小 */
    max-height: 30%; /* 调整为适合的叠加图片大小 */
  }
}
</style>