<template>
    <div class="mbr-card-dtl">
        <div>
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="130px" class="demo-ruleForm">
                <el-form-item :label="formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/特权说明')" prop="remark" class="cur_item">
                    <el-popover
                            placement="top"
                            width="400"
                            :title="formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/示例文字')"
                            trigger="hover">
                        <div style="color:#b5b5b5">1.{{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/10元积1个积分：结帐前须出示海鼎未来店会员卡，每消费10元即可累积1个积分，不满10元不积分')}}</div>
                        <div style="color:#b5b5b5">2.{{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/20个会员积分抵1元使用：海鼎未来店会员卡，每100个积分可抵现1元，不满100积分不能抵现')}}</div>
                        <div style="color:#b5b5b5">3.{{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/每月8日会员日，可享双倍积分')}}</div>
                        <div style="color:#b5b5b5">4.{{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/每月6日会员日，可享积分加倍抵现')}}</div>
                        <div style="color:#b5b5b5">5.{{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/每年的1月1号为海鼎未来店的积分到期日，在到期日当天，一年前获取且未使用的积分将会过期失效')}}</div>
                        <el-button style="display: block;padding-top: 14px" slot="reference"  type="text">{{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/示例文字')}}</el-button>
                    </el-popover>
                    <el-input maxlength="1024" type="textarea" v-model="ruleForm.remark" style="width: 300px" @change="doChange"></el-input>
                </el-form-item>
                <el-form-item :label="formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/有效日期')">
                    <div>{{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/永久有效')}}</div>
                </el-form-item>
                <el-form-item :label="formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/使用须知（选填）')" class="cur_item">
                    <el-input maxlength="1024" type="textarea" v-model="ruleForm.tip" :placeholder="formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/商家拥有最终解释权')"  style="width: 300px" @change="doChange"></el-input>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>

<script lang="ts" src="./MbrCardDtl.ts">
</script>

<style lang="scss">
.mbr-card-dtl{
    padding-top: 30px;
    .cur_item{
        .el-input__inner{
            height: 200px !important;
        }
    }
}
</style>