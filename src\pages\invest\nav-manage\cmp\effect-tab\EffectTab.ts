
import I18nPage from 'common/I18nDecorator';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import NavigationSetting from 'model/navigation/NavigationSetting'
import MenuSetting from 'model/navigation/MenuSetting'

// 根据接口所需字段拓展前端展示所需字段
class customMenuSetting extends MenuSetting {
  showRepealce?: boolean // 已选择情况
  isUseIcon?: boolean // 是否使用的 icon
 // 默认左侧回显不选中
 isActive: boolean = false
}

class AddMenuData extends NavigationSetting {
  // 菜单设置
  menuSettings: customMenuSetting[] = []
}
// // 菜单设置
// class MenuSetting {
//     // 菜单名称
//     name: string = ''
//     // 已选中图标
//     selectedIcon: string = ''
//     // 未选中图标
//     unselectedIcon: string = ''
//     // 跳转页面信息
//     jumpPageInfo: JumpPageInfo = new JumpPageInfo()
//     //   是否处于选中状态
//     isActive: Boolean = false
// }
// class ColorForm {
//     selectedColor: string;
//     selectedFontColor: string;
//     selectedIconColor: string;
//     unselectedColor: string;
//     unselectedFontColor: string;
//     unselectedIconColor: string;
//     menuQuantity: string;
//     menuSettings?: MenuSetting[] = []
// }


@Component({
    name: 'EffectTab',
    components: {}
})


@I18nPage({
    prefix: [
        '/导航/显示'
    ],
    auto: true
})



export default class EffectTab extends Vue {
    @Prop() customData: AddMenuData
    @Prop() showModel: string
    // @Prop() iconColorType: string
    currentData: AddMenuData = new AddMenuData()

    @Watch("customData", { deep: true})
    onCustomDataChange(value: any) {
        if (value) {
            this.currentData = value
        }
        
    }

    mounted() {
        this.currentData = this.customData
        this.currentData.menuSettings.forEach((item, index) => {
            item.isActive = false
            if (index === 0) {
                item.isActive = true
            }
        })
    }

    doAheckedActive(index:number){
        this.currentData.menuSettings[index].isActive = !this.currentData.menuSettings[index].isActive
    }
}