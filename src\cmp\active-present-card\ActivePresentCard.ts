/*
 * @Author: 黎钰龙
 * @Date: 2023-10-18 10:05:24
 * @LastEditTime: 2024-01-11 10:10:18
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\active-present-card\ActivePresentCard.ts
 * 记得注释
 */
import AutoFixInput from 'cmp/autofixinput/AutoFixInput';
import CouponTemplateSelectorDialog from 'cmp/selectordialogs/CouponTemplateSelectorDialog';
import I18nPage from 'common/I18nDecorator';
import RoutePermissionMgr from 'mgr/RoutePermissionMgr';
import CardTemplate from 'model/card/template/CardTemplate';
import CouponInfo from 'model/common/CouponInfo';
import GiftInfo from 'model/common/GiftInfo';
import CouponTemplate from 'model/coupon/template/CouponTemplate';
import CouponTemplateFilter from 'model/coupon/template/CouponTemplateFilter';
import { CardMedium } from 'model/default/CardMedium';
import CouponItem from 'model/v2/coupon/improveProfiles/CouponItem';
import CardTplItem from 'pages/deposit/prepaycard/cmp/cardtplitem/CardTplItem';
import { SaleSpecFormData } from 'pages/deposit/prepaycard/entitycardactivity/EntityCardActivityEditForm';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  name: 'ActivePresentCard',
  components: {
    AutoFixInput,
    CardTplItem,
    CouponTemplateSelectorDialog
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/储值/预付卡/电子礼品卡活动/编辑页面',
  ],
  auto: true
})
export default class ActivePresentCard extends Vue {
  $refs: any
  data: any = {
    saleSpecs: []
  }
  cardTemplateFilter: CouponTemplateFilter = new CouponTemplateFilter()
  CardMedium = CardMedium
  currentCardTemplateIndex: number = 0  //当前选中的卡模板index，用来判断将选中券模板加到哪个卡模板上
  rules: any = {}

  @Prop({ type: String }) favType: 'discount' | 'amount'; //优惠类型
  @Prop() value: SaleSpecFormData[];  //卡模板列表
  @Prop({ type: Boolean, default: false }) editFlag: Boolean;
  @Prop({ type: Array, default: [] }) cardMediumIn: string[]; //卡介质
  @Prop({ type: String, default: '' }) cardType: string;  //卡类型
  @Prop({ type: Boolean, default: true }) isShowGift: boolean; //是否展示购卡赠礼
  @Prop({ type: Boolean, default: true }) isShowLimit: boolean; //是否展示发售限制

  get selectCardNo() {  //已选中的卡模板号数组
    return this.data.saleSpecs.map((item: SaleSpecFormData) => item.cardTemplateNumber)
  }

  get currentCardType() {
    switch (this.cardType) {
      case 'GiftCard':
        return 'GIFT_CARD'
      case 'RechargeableCard':
        return 'RECHARGEABLE_CARD'
      case 'ImprestCard':
        return 'IMPREST_CARD'
      case 'CountingCard':
        return 'COUNTING_CARD'
    }
  }

  computeSalePrice(row: SaleSpecFormData) {
    if (row.cardTemplateType === 'COUNTING_CARD') {
      return (Number(row.templatePrice) * (parseFloat(row.discount as any || 10) / 10)).toFixed(2)
    } else {
      return (Number(row.faceAmount) * (parseFloat(row.discount as any || 10) / 10)).toFixed(2)
    }
  }

  created() {
    this.initRules()
  }

  //回填表单（在父组件调）
  setValue(cardList: SaleSpecFormData[]) {
    this.data.saleSpecs = cardList || []
  }

  doItemChange() {
    this.$emit('change', this.data.saleSpecs || [])
    this.$emit('input', this.data.saleSpecs || [])
    this.$refs.form.validate()
  }

  doValidate() {
    return this.$refs.form?.validate()
  }

  gotoTplDtl(item: any) {
    RoutePermissionMgr.openBlank({ name: 'prepay-card-tpl-dtl', query: { number: item.cardTemplateNumber } })
  }

  //给勾选的卡模板赋默认值
  selectTpl(val: CardTemplate[]) {
    this.data.saleSpecs = [];
    (val || []).forEach((item: CardTemplate) => {
      const itemVal = new SaleSpecFormData()
      itemVal.cardTemplateName = item.name
      itemVal.cardTemplateNumber = item.number
      itemVal.discount = 0.1
      itemVal.faceAmount = item.faceAmounts[0]
      itemVal.gift = new GiftInfo()
      itemVal.price = Number(item.faceAmounts[0] || item.price)
      itemVal.templatePrice = Number(item.price)
      itemVal.cardTemplateType = item.cardTemplateType || ''
      itemVal.count = item.count || 0
      this.data.saleSpecs.push(itemVal)
    })
    this.doItemChange()
  }

  // 选卡模板
  chooseCArdTpl() {
    this.$refs.CardTplItem.showDialog();
  }
  // 删除模板
  delteTplDtl(index: number) {
    this.data.saleSpecs?.splice(index, 1)
    this.doItemChange()
  }

  //添加券模板
  doAddCoupon(index: any) {
    let arr: CouponTemplate[] = []  //已选择的券模板，用来回显
    this.currentCardTemplateIndex = index
    let spec = this.data.saleSpecs[index]
    if (spec && spec.gift) {
      if (spec.gift.couponItems && spec.gift.couponItems.length > 0) {
        spec.gift.couponItems.forEach((item: CouponItem) => {
          let obj: CouponTemplate = new CouponTemplate()
          obj.number = item.coupons!.templateId
          obj.name = item.coupons!.name
          arr.push(obj)
        })
      }
    }
    this.$refs.couponTemplate.open(arr, 'multiple')
  }

  //对应卡模板选择了优惠券赠礼
  doCardTemplateSelected(arr: CouponTemplate[]) {
    if (arr) {
      const tempQty: any = {};
      const gift = this.data.saleSpecs[this.currentCardTemplateIndex].gift!;
      const couponItems = gift.couponItems;
      for (const item of couponItems) {
        const templateId = item.coupons?.templateId;
        if (templateId) {
          tempQty[templateId] = item.qty;
        }
      }
      gift.couponItems = [];
      for (const item of arr) {
        const couponItem = new CouponItem();
        const coupons = new CouponInfo();
        const templateId = item.number;
        coupons.name = item.name;
        coupons.templateId = templateId;
        couponItem.coupons = coupons;
        couponItem.qty = templateId && tempQty[templateId] ? tempQty[templateId] : 1;
        gift.couponItems.push(couponItem);
      }
      this.doItemChange();
    }
  }

  changeGivePoints(index: number) {
    this.$refs.form.validate()
    if (!this.data.saleSpecs[index].givePoints) {
      this.data.saleSpecs[index].gift.points = null
    }
    this.$forceUpdate()
    this.doItemChange()
  }

  initRules() {
    this.rules = {
      discountRules: [
        {
          validator: (rule: any, _: any, callback: any) => {
            const index = Number.parseInt(rule.field.substring(10))
            const value = this.data.saleSpecs[index]
            if (this.favType == 'discount' && !value.discount) {
              callback(new Error(this.i18n('请输入折扣')))
            }
            callback()
          },
          trigger: ['change', 'blur']
        }
      ],
      priceRules: [
        {
          validator: (rule: any, _: any, callback: any) => {
            const index = Number.parseInt(rule.field.substring(10))
            const value = this.data.saleSpecs[index]
            if (this.favType == 'amount' && !value.price) {
              callback(new Error(this.i18n('请输入售价')))
            }
            callback()
          },
          trigger: ['change', 'blur']
        }
      ],
      specPointsRules: [
        {
          validator: (rule: any, _: any, callback: any) => {
            const index = Number.parseInt(rule.field.substring(10))
            const value = this.data.saleSpecs[index]
            if (value.givePoints && (!value.gift || !value.gift.points)) {
              callback(new Error(this.i18n('请输入赠送积分')))
            }
            callback()
          },
          trigger: ['change', 'blur']
        }
      ],
      specCouponRules: [
        {
          validator: (rule: any, _: any, callback: any) => {
            const index = Number.parseInt(rule.field.substring(10))
            const value = this.data.saleSpecs[index]
            if (value.giveCoupons && (!value.gift || !value.gift.couponItems || value.gift.couponItems.length === 0)) {
              callback(new Error(this.i18n('请添加券')))
            }
            callback()
          },
          trigger: ['change', 'blur']
        }
      ],
      specCouponQtyRules: [
        { required: true, message: this.i18n('请输入赠送优惠券数量'), trigger: ['change', 'blur'] },
      ]
    }
  }
};