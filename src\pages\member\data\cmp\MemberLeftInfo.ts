import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import MemberFormItem from "pages/member/data/cmp/MemberFormItem";
import MemberDetail from "model/member_standard/MemberDetail";
import I18nPage from "common/I18nDecorator";
import DeleteMemberIdentRequest from "model/member_standard/DeleteMemberIdentRequest";
import Member<PERSON><PERSON> from "http/member_standard/MemberApi";
import UnBindDialog from "pages/member/data/dialog/UnBindDialog.vue";
import MemberCardTitle from "pages/member/data/cmp/MemberCardTitle";
import BenefitCard from "model/benefitCard/BenefitCard";
import EquityCardApi from "http/benefit/EquityCardApi";
import EquityCardQueryRequest from "model/equityCard/EquityCardQueryRequest";
import EquityCard from "model/equityCard/EquityCard";
import MemberSimpleCard from "pages/member/data/cmp/MemberSimpleCard";
import MemberGrowthDetailDialog from "pages/member/data/cmp/MemberGrowthDetailDialog";
import MemberTipContent from "pages/member/data/cmp/MemberTipContent.vue";
import { BenefitCardState } from "model/benefitCard/BenefitCardState";

@Component({
  name: "MemberLeftInfo",
  components: {
    MemberFormItem,
    UnBindDialog,
    MemberCardTitle,
    MemberSimpleCard,
    MemberGrowthDetailDialog,
    MemberTipContent,
  },
})
@I18nPage({
  prefix: [
    "/公用/券模板",
    "/公用/菜单",
    "/会员/会员资料",
  ],
  auto: true,
})
export default class MemberLeftInfo extends Vue {

  @Prop()
  dtl: MemberDetail;

  @Watch("dtl", { immediate: true })
  dtlChanged() {
    if (!(this.dtl && this.dtl.memberId)) return;
    this.getBenefitCards();
    this.getEquityCards();
  }

  get isStandardMember() {
    return this.$route.fullPath.indexOf("/standard-member") != -1;
  }

  get permissionResourceId() {
    return this.isStandardMember ? "/会员/会员管理/会员资料" : "/会员/会员管理/营销中心会员";
  }

  bCards: BenefitCard[] = [];
  eCards: BenefitCard[] = [];

  // 获取付费会员
  getBenefitCards() {
    MemberApi.getBenefitCard(this.dtl.memberId!).then(res => {
      if (res.code === 2000) {
        this.bCards = res.data || [];
        this.bCards = this.bCards.filter(e => e.cardStatus == BenefitCardState.NORMAL);
        // @ts-ignore
        // this.bCards = [{ cardNo: "1", name: "大神卡" }, { cardNo: "2", name: "钻石级用户专属定制尊享会员" }, {
        //   cardNo: "3",
        //   name: "钻石级用户专属定制尊享会员钻石级用户专属定制尊享会员",
        // }];
      } else {
        throw new Error(res.msg!);
      }
    }).catch((error) => {
      this.$message.error(error.message);
    });
  }

  // 获取权益卡
  getEquityCards() {
    MemberApi.getEquityCard(this.dtl.memberId!).then(res => {
      if (res.code === 2000) {
        this.eCards = res.data || [];
        this.eCards = this.eCards.filter(e => e.cardStatus == BenefitCardState.NORMAL);
        // @ts-ignore
        // this.eCards = [{ cardNo: "1", name: "大神卡" }, { cardNo: "2", name: "钻石级用户专属定制尊享会员" }, {
        //   cardNo: "3",
        //   name: "钻石级用户专属定制尊享会员钻石级用户专属定制尊享会员",
        // }];
      } else {
        throw new Error(res.msg!);
      }
    }).catch((error) => {
      this.$message.error(error.message);
    });
  }

  private deleteMobile(phone: string) {
    this.$confirm(this.formatI18n("/会员/会员资料/确定删除吗？"), this.formatI18n("/公用/弹出模态框提示标题/提示"), {
      confirmButtonText: this.formatI18n("/公用/按钮/确定"),
      cancelButtonText: this.formatI18n("/公用/按钮/取消"),
    }).then(() => {
      let params: DeleteMemberIdentRequest = new DeleteMemberIdentRequest();
      params.memberId = this.dtl.memberId as string;
      params.ident.push({
        id: phone,
        type: "mobile",
        source: null,
      });
      MemberApi.deleteMemberIdent(params).then(res => {
        if (res.code === 2000) {
          this.$message({
            type: "success",
            message: this.formatI18n("/公用/活动/提示信息/操作成功"),
          });
          // @ts-ignore
          if (this.dtl.mobileList) {
            // @ts-ignore
            this.dtl.mobileList.splice(this.dtl.mobileList.indexOf(phone), 1);
          }
        } else {
          this.$message.error(res.msg as string);
        }
      });
    }).catch(() => {

    });
  }

  bindUuid: string = "";
  unBindFlag = false;


  doUnBind(id: string) {
    this.unBindFlag = true;
    this.bindUuid = id;
  }

  doUnbindClose() {
    this.unBindFlag = false;
    this.$emit("reload");
  }

  memberChannelTypes: Array<any> = [{
    name: this.i18n("微信"),
    code: "weiXin",
  }, {
    name: this.i18n("支付宝"),
    code: "aliPay",
  }, {
    name: this.i18n("微盟"),
    code: "weimob",
  }, {
    name: this.i18n("抖音"),
    code: "douYin",
  }, {
    name: this.i18n("企微"),
    code: "qiWei",
  }, {
    name: this.i18n("有赞"),
    code: "youZan",
  }];

  getMemberChannelLabel(channels: Array<string>) {
    if (!channels || channels.length == 0) return "--";
    return this.memberChannelTypes.filter(e => channels.indexOf(e.code) != -1).map(e => e.name).join(",");
  }

  get getInvited() {
    if (this.dtl.referee && this.dtl.referee.mobile) {
      return this.dtl.referee.mobile;
    } else if (this.dtl.referee && this.dtl.referee.crmCode) {
      return this.dtl.referee.crmCode;
    } else {
      return "";
    }
  }

  doInviteMember() {
    this.$router.push({
      name: this.isStandardMember ? "standard-member-dtl" : "marketcenter-member-dtl",
      query: { id: this.dtl.referee!.memberId },
    });
  }

  remarkDialog = {
    visible: false,
    remark: "",
  };

  private editRemark() {
    this.remarkDialog.remark = this.dtl.remark as string;
    this.remarkDialog.visible = true;
  }

  private updateRemark() {
    MemberApi.editRemark(this.dtl.memberId as string, this.remarkDialog.remark).then((res) => {
      this.$message.success(this.formatI18n("/会员/会员资料/编辑成功"));
      this.remarkDialog.visible = false;
      this.dtl.remark = this.remarkDialog.remark;

    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message);
      }
    });
  }

  get getDay() {
    if (this.dtl) {
      if (this.dtl.lastConsumeDay === 0) {
        return this.formatI18n("/会员/会员资料", "今天");
      } else if (this.dtl.lastConsumeDay === 1) {
        return this.formatI18n("/会员/会员资料", "昨天");
      } else {
        let str: any = this.formatI18n("/会员/会员资料", "{0}天前");
        str = str.replace(/\{0\}/g, this.dtl.lastConsumeDay);
        return str;
      }
    } else {
      return "";
    }
  }

  get getFirstNameAndLastName() {
    let str = "";
    if (!this.dtl.name && !this.dtl.lastName) {
      str = this.formatI18n("/会员/会员资料", "会员姓名未知");
    } else {
      str = (this.dtl.name ?? "") + " " + (this.dtl.lastName ?? "");
    }
    return str;
  }

  growthDialogVisible: boolean = false;
}
