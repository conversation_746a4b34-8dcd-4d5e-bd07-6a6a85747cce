import { Component, Vue } from "vue-property-decorator";
import SubHeader from "cmp/subheader/SubHeader.vue";
import OrgBalanceConfigApi from "http/deposit/store/OrgBalanceConfigApi";
import GoodsScopeDtl from "cmp/goodsscope/GoodsScopeDtl.vue";
import IdName from "model/common/IdName";
import ActiveStoreDtl from "cmp/activestoredtl/ActiveStoreDtl";
import I18nPage from "common/I18nDecorator";
import BreadCrume from "cmp/bread-crumb/BreadCrume.vue";
import ListWrapper from "cmp/list/ListWrapper.vue";
import FloatBlock from "cmp/floatblock/FloatBlock.vue";
import OrgBalanceConfig from "model/deposit/store/OrgBalanceConfig";
import OrgBalanceConfigCreateRequest from "model/deposit/store/OrgBalanceConfigCreateRequest";
import OrgBalanceConfigFilter from "model/deposit/store/OrgBalanceConfigFilter";
import OrgBalanceConfigModifyRequest from "model/deposit/store/OrgBalanceConfigModifyRequest";
import State from "model/deposit/store/State";
import StoreSelectorDialog from "cmp/selectordialogs/StoreSelectorDialog";
import AutoFixInput from "cmp/autofixinput/AutoFixInput";
import EnvUtil from "util/EnvUtil";
import DownloadCenterDialog from "pages/main/dowmload_center_dialog/DownloadCenterDialog";
import RSOrgFilter from "model/common/RSOrgFilter";
import OrgApi from "http/org/OrgApi";
import SelectStores from "cmp/selectStores/SelectStores";
import UploadApi from "http/upload/UploadApi";

@Component({
	name: "StoreValueAccount",
	components: {
		SubHeader,
		GoodsScopeDtl,
		ActiveStoreDtl,
		BreadCrume,
		ListWrapper,
		FloatBlock,
		StoreSelectorDialog,
		AutoFixInput,
		DownloadCenterDialog,
    SelectStores
	},
})
@I18nPage({
	prefix: ["/储值/会员储值/门店储值管理", "/公用/提示", '/公用/菜单'],
})
export default class StoreValueAccount extends Vue {
	i18n: (str: string, params?: string[]) => string;

	data: any[] = [];
	currentStores: IdName[] = [];
	$refs: any;
	panelArray: any = [];
	page = {
		currentPage: 1,
		total: 0,
		size: 10,
	};
	state: State = new State();
	selected: any[] = [];
	queryFilter: OrgBalanceConfigFilter = new OrgBalanceConfigFilter();
	stateShow: Boolean = false;
	stateContent: String = "";
	stateTile: string = "";

	currentStore: OrgBalanceConfig = new OrgBalanceConfig();
	limitShow: Boolean = false;
	importShow: Boolean = false;

	batchShow: Boolean = false;
	batchState: any = "";
	batchLimit: any = "100000";

	uploadHeaders: any = {};
	fileCount = 0;

	fileDialogVisible = false;
	showTip = false;
	batchParams: OrgBalanceConfigModifyRequest = new OrgBalanceConfigModifyRequest();
	limitEditRule: any = {};
	limitBatchRule: any = {};

	get uploadUrl() {
		return EnvUtil.getServiceUrl() + "v1/orgBalance/config/importConfig";
	}
	get templateUrl() {
		if (location.href.indexOf("localhost") === -1) {
			return "template_org_balance_config.xlsx";
		} else {
			return "template_org_balance_config.xlsx";
		}
	}
	created() {
		this.panelArray = [
			{
				name: this.i18n("卡限额设置"),
				url: "",
			},
		];
		var limitBalanceValidate = (rule: any, value: any, callback: any) => {
			if (!value) {
				callback(new Error(this.formatI18n("/储值/会员储值/门店储值管理/请输入储值余额限制")));
			} else {
				callback();
			}
		};
		this.limitEditRule = {
			limitBalance: [
				{ validator: limitBalanceValidate, trigger: "change" },
				{ validator: limitBalanceValidate, trigger: "blur" },
			],
		};
		var limitBalanceBatchValidate = (rule: any, value: any, callback: any) => {
			if (!value) {
				callback(new Error(this.formatI18n("/储值/会员储值/门店储值管理/请输入储值余额限制")));
			} else {
				callback();
			}
		};
		this.limitBatchRule = {
			balanceLimit: [
				{ validator: limitBalanceBatchValidate, trigger: "change" },
				{ validator: limitBalanceBatchValidate, trigger: "blur" },
			],
		};
		this.batchState = this.state.normal;
		this.query();
	}
	handleSelectionChange(val: any) {
		this.selected = val;
		console.log(this.selected);
	}

	openEdit() {
		this.$refs.storeSelectorDialog.dialogShow = true;
		this.$refs.storeSelectorDialog.title = this.formatI18n("/储值/会员储值/门店储值管理/新增");
	}
	selectStore(selected: any) {
		console.log(selected);
		if (selected.length == 0) {
			return;
		}
		let params = new OrgBalanceConfigCreateRequest();
		params.state = this.state.normal;
		params.limitBalance = 100000;
		params.orgIds = selected.map((item: any) => {
			return item.org.id;
		});
		OrgBalanceConfigApi.create(params).then((res) => {
			if (res.code == 2000) {
				this.$message.success(this.formatI18n("/储值/会员储值/门店储值管理/新增成功"));
				this.query();
			} else {
				this.$message.error(res.msg as string);
			}
		});
	}

	openState(current: any, isNormal: Boolean) {
		console.log(isNormal);
		this.currentStore = JSON.parse(JSON.stringify(current));
		if (isNormal == false) {
			this.stateTile = this.formatI18n("/储值/会员储值/门店储值管理/设为异常");
			this.stateContent = `<div>${this.formatI18n(
				"/储值/会员储值/门店储值管理/设为异常以后，储值卡充值和消费将受以下限制："
			)}</div><br><div>${this.formatI18n("/储值/会员储值/门店储值管理/1、所有卡都不能在该门店充值；")}</div><div>${this.formatI18n(
				"/储值/会员储值/门店储值管理/2、本店卡只能在本店消费，不能去其他店消费；非本店卡不能在本店消费。"
			)}</div>`;
		} else {
			this.stateTile = this.formatI18n("/储值/会员储值/门店储值管理/设为正常");
			this.stateContent = `<div>${this.formatI18n("/储值/会员储值/门店储值管理/设为正常以后，储值卡充值和消费将恢复正常")}</div>`;
		}
		this.stateShow = true;
	}
	handleStateClose() {
		this.stateShow = false;
	}
	handleStateConfirm() {
		let params = new OrgBalanceConfigModifyRequest();
		params.orgIdIn.push(this.currentStore.orgId as string);
		params.balanceLimit = this.currentStore.limitBalance;
		if (this.stateTile === this.formatI18n("/储值/会员储值/门店储值管理/设为异常")) {
			// @ts-ignore
			params.state = this.state.abnormal;
		} else {
			// @ts-ignore
			params.state = this.state.normal;
		}
		OrgBalanceConfigApi.modify(params).then((res) => {
			console.log(res);

			if (res.code === 2000) {
				this.$message.success(this.formatI18n("/资料/渠道/修改成功"));
				this.query();
			} else {
				this.$message.error(res.msg as string);
			}
		});
		this.stateShow = false;
	}

	openLimit(currentStore: OrgBalanceConfig) {
		this.currentStore = JSON.parse(JSON.stringify(currentStore));
		this.limitShow = true;
	}
	handleLimitClose() {
		this.limitShow = false;
	}
	handleLimitConfirm() {
		this.$refs.limitEdit.validate().then(() => {
			let params = new OrgBalanceConfigModifyRequest();
			params.orgIdIn.push(this.currentStore.orgId as string);
			params.balanceLimit = this.currentStore.limitBalance;
			params.state = this.currentStore.state;
			OrgBalanceConfigApi.modify(params).then((res) => {
				console.log(res);

				if (res.code === 2000) {
					this.$message.success(this.formatI18n("/资料/渠道/修改成功"));
					this.query();
				} else {
					this.$message.error(res.msg as string);
				}
			});
			this.limitShow = false;
		});
	}

	openBatch() {
		if (this.selected.length === 0) {
			this.$message.warning(this.formatI18n("/储值/会员储值/门店储值管理/请至少选择一个门店"));
		} else {
			this.batchShow = true;
		}
	}
	handleBatchClose() {
		this.batchShow = false;
	}
	handleBatchConfirm() {
		this.$refs.limitBatch.validate().then(() => {
			let params = new OrgBalanceConfigModifyRequest();
			params.orgIdIn = this.selected.map((item) => {
				return item.orgId;
			});
			params.balanceLimit = this.batchParams.balanceLimit;
			params.state = this.batchState;
			OrgBalanceConfigApi.modify(params).then((res) => {
				console.log(res);
				if (res.code === 2000) {
					this.$message.success(this.formatI18n("/资料/渠道/修改成功"));
					this.query();
				} else {
					this.$message.error(res.msg as string);
				}
			});
			this.batchShow = false;
		});
	}

	openImport() {
		this.importShow = true;
	}
	handleImportClose() {
		this.importShow = false;
	}
	handleImportConfirm() {
		this.$refs.upload.submit();
	}

	onHandleCurrentChange(val: number) {
		this.page.currentPage = val;
		this.query();
	}

	/**
	 * 每页多少条的回调
	 * @param val
	 */
	onHandleSizeChange(val: number) {
		this.page.size = val;
		this.query();
	}
	doReset() {
		this.queryFilter = new OrgBalanceConfigFilter();
		this.query();
	}
	doSearch() {
		this.query();
	}
	query() {
		this.queryFilter.page = this.page.currentPage - 1;
		this.queryFilter.pageSize = this.page.size;

		OrgBalanceConfigApi.query(this.queryFilter)
			.then((resp: any) => {
				if (resp && resp.data) {
					this.data = resp.data;
					this.page.total = resp.total;
				}
			})
			.catch((error) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}
	private initUploadHeaders() {
		let locale = sessionStorage.getItem("locale");
		this.uploadHeaders = {
			locale: locale ? locale : "zh_CN",
			time_zone: new Date().getTimezoneOffset(),
			marketingCenter: sessionStorage.getItem("marketCenter"),
		};
    const authorization = EnvUtil.getUniAuthorization()
    if (authorization) {
      this.uploadHeaders.authorization = authorization
    }
	}
	doHandleChange(file: any, fileList: any) {
		if (fileList.length > 0) {
			this.fileCount++;
		}
	}
	getErrorInfo(a: any, b: any, c: any) {
		this.$message.error(this.formatI18n("/公用/导入", "导入失败，请重新导入") as any);
		this.fileCount = 0;
		this.$refs.upload.clearFiles();
	}
	getSuccessInfo(response: any, b: any, c: any) {
		console.log(response);

		if (response.code === 2000) {
			//   this.formData.filename = response.data
			// this.$message.success(this.formatI18n('/公用/活动/提示信息/操作成功'))
			this.importShow = false;
			this.fileCount = 0;
			this.$refs.upload.clearFiles();
			this.exportAfter();
		} else {
			this.fileCount = 0;
			this.$refs.upload.clearFiles();
			this.$message.error(response.msg);
		}
	}
	doDownloadDialogClose() {
		this.showTip = false;
		this.fileDialogVisible = false;
		this.query();
	}
	exportAfter() {
		this.showTip = true;
		this.fileDialogVisible = true;
	}

	downloadTemplate() {
		UploadApi.getUrl(this.templateUrl).then((resp: any) => {
			if (resp && resp.data) {
				window.open(resp.data);
			}
		}).catch((error) => {
			if (error && error.message) {
				this.$message.error(error.message)
			}
		})
	}
}
