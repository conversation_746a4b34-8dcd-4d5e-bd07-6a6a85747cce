import BasePointsRule from "model/v2/controller/points/BasePointsRule";
import ConsumeDay from "model/v2/controller/points/ConsumeDay";
import ChannelRange from "model/common/ChannelRange";
import GradeStepValue from "model/common/GradeSameReduction";

export default class MemberDayPointsSpeedRule extends BasePointsRule {
	// 会员日
	memberDay: Nullable<ConsumeDay> = null;
	// 加速条件,空表示不限制
	miniCost: Nullable<number> = null;
	// 是否被禁用
	stopped: Nullable<boolean> = null;
	// 渠道范围
	channelRange: Nullable<ChannelRange> = null;
	// 不同等级适用相同规则
	gradeSameStepValue: Nullable<GradeStepValue> = new GradeStepValue();
	// 不同等级适用不同规则
	gradeDifferentStepValues: Nullable<GradeStepValue[]> = null;
}
