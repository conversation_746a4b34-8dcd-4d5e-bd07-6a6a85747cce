/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2023-08-30 19:17:40
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\card\template\TemplateTypeCountResult.ts
 * 记得注释
 */
export default class TemplateTypeCountResult {
  // 电子礼品卡
  onlineGiftCard: Nullable<number> = 0
  // 实体礼品卡
  offlineGiftCard: Nullable<number> = 0
  // 充值卡
  imprestCard: Nullable<number> = 0
  // 储值卡
  rechargeableCard: Nullable<number> = 0
  // 计次卡
  countingCard: Nullable<number> = 0
  // 总数
  sum: Nullable<number> = 0
}