/*
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:03
 * @LastEditTime: 2024-04-23 18:18:50
 * @LastEditors: 黎钰龙
 * @Description: 
 * 记得注释
 */
import SubHeader from 'cmp/subheader/SubHeader.vue'
import { Component, Vue } from 'vue-property-decorator'
import CardTplItem from '../cmp/cardtplitem/CardTplItem'
import I18nPage from "common/I18nDecorator";
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import CommonUtil from 'util/CommonUtil'
import MakeSaleCardBill from "model/card/makesalecardbill/MakeSaleCardBill";
import MakeSaleCardBillApi from "http/card/makesalebill/MakeSaleCardBillApi";
import ConstantMgr from "mgr/ConstantMgr";

@Component({
  name: 'MakeSaleCardBillDtl',
  components: {
    SubHeader,
    CardTplItem,
    BreadCrume
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/公用/查询条件/提示',
    '/储值/预付卡/卡模板/编辑页面',
    '/公用/活动/状态',
    '/储值/预付卡/充值卡制售单/详情页面',
    '/公用/按钮',
    '/公用/提示',
  ],
})
export default class MakeSaleCardBillDtl extends Vue {
  i18n: (str: string, params?: string[]) => string
  detail: MakeSaleCardBill = new MakeSaleCardBill()
  panelArray: any = []
  created() {
    this.panelArray = [
      {
        name: this.formatI18n('/储值/预付卡/充值卡制售单/列表页面/制售单'),
        url: 'make-sale-card-bill-list'
      },
      {
        name: this.formatI18n('/储值/预付卡/充值卡制售单/详情页面/制售单详情'),
        url: ''
      }
    ]
    this.getDetail()
  }

  private getDetail() {
    let num = this.$route.query.number as string
    const loading = CommonUtil.Loading()
    MakeSaleCardBillApi.get(num).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.detail = resp.data
      } else {
        this.$message.error(resp.msg)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    }).finally(() => {
      loading.close()
    })
  }

  private doExport() {
    const loading = this.$loading(ConstantMgr.loadingOption)

    return MakeSaleCardBillApi.export(this.detail.billNumber!).then((resp: any) => {
      if (resp && resp.code === 2000) {
        window.open(resp.data, '_blank')
      } else {
        throw new Error(resp.msg)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    }).finally(() => {
      loading.close()
    })
  }

  private doAudit() {
    const loading = this.$loading(ConstantMgr.loadingOption)

    return MakeSaleCardBillApi.audit(this.detail.billNumber!).then((resp:any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(this.i18n('操作成功'))
        this.getDetail()
      } else {
        this.$message.error(resp.msg || this.i18n('操作失败'))
      }
    }).catch((error) => {
      this.$message.error(error.message || this.i18n('内部异常'))
    }).finally(() => {
      loading.close()
    })
  }

  private doRemove() {
    const loading = this.$loading(ConstantMgr.loadingOption)
    return MakeSaleCardBillApi.remove(this.detail.billNumber!).then((resp:any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(this.i18n('操作成功'))
        this.getDetail()
      } else {
        this.$message.error(resp.msg || this.i18n('操作失败'))
      }
    }).catch((error) => {
      this.$message.error(error.message || this.i18n('内部异常'))
    }).finally(() => {
      loading.close()
    })
  }
}