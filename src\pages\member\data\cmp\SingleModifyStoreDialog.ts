import { Component, Prop, Vue, Emit, Watch } from "vue-property-decorator";
import FormItem from "cmp/formitem/FormItem.vue";
import OrgApi from "http/org/OrgApi";
import RSOrgFilter from "model/common/RSOrgFilter";
import RSOrg from "model/common/RSOrg";
import Member<PERSON><PERSON> from "http/member_standard/MemberApi";
import ModifyStore from "model/member_standard/ModifyStore";
import IdName from "model/entity/IdName";

interface RuleFrom {
	storeId: string;
	remark: string;
}

interface OwnStore {
	id: string;
	name: string;
}
@Component({
	name: "SingleModifyStoreDialog",
	components: {
		FormItem,
	},
})
export default class SingleModifyStoreDialog extends Vue {
	@Prop({ type: Boolean, default: false }) readonly dialogShow!: boolean;
	@Prop({ type: String, default: "" }) readonly memberId!: string;
	@Prop({ type: Object, default: {} }) readonly ownStore!: OwnStore;
	$refs: any;
	ruleForm: RuleFrom = {
		storeId: "",
		remark: "",
	};
	rules = {
		storeId: [{ required: true, message: this.formatI18n("/会员/会员资料", "请选择归属门店"), trigger: "change" }],
	};
	query: RSOrgFilter = new RSOrgFilter();
	queryData: RSOrg[] = [];

	doModalClose(type: string) {
		if (type === "confirm") {
			this.$refs["ruleForm"].validate((valid: any) => {
				console.log(valid);
				if (valid) {
					let params = new ModifyStore();
					params.memberIds = [this.memberId];
					params.remark = this.ruleForm.remark;
					params.store = new IdName();
					params.store.id = this.ruleForm.storeId;
					let name = this.queryData.filter((item) => {
						return (item.org ? item.org.id : null) === this.ruleForm.storeId;
					})[0];
					params.store.name = name.org ? name.org.name : null;
					console.log(params);
					MemberApi.modifyStore(params)
						.then((resp: any) => {
							if (resp && resp.code === 2000) {
								this.ruleForm.remark = "";
								this.$emit("changeSuccess");
							}
						})
						.catch((error) => {
							if (error && error.message) {
								this.$message.error(error.message);
							}
						});
				}
			});
		} else {
			if (this.ownStore) {
				this.ruleForm.storeId = this.ownStore.id;
			}

			this.ruleForm.remark = "";
			this.$emit("dialogClose");
		}
	}

	@Watch("ownStore", { immediate: true, deep: true })
	handelValChange(val: OwnStore) {
		if (val) {
			this.ruleForm.storeId = val.id;
		}
	}

	@Emit("dialogClose")
	doBeforeClose() {
		if (this.ownStore) {
			this.ruleForm.storeId = this.ownStore.id;
		}
		this.ruleForm.remark = "";
	}

	private created() {
		this.query.page = 0;
		this.query.pageSize = 0;
		OrgApi.query(this.query)
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					this.queryData = resp.data;
				}
			})
			.catch((error) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}
}
