/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-10-21 10:10:35
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\v2\controller\points\meta\PointsMetaRuleItem.ts
 * 记得注释
 */
import ChannelRange from 'model/common/ChannelRange'
import MetaGoodsGroup from 'model/v2/controller/points/meta/MetaGoodsGroup'
import StoreRange from 'model/common/StoreRange'
import TabAttributes from 'model/v2/controller/points/meta/TabAttributes'
import Channel from "model/v2/common/Channel";
import {ChannelRangeType} from "model/common/ChannelRangeType";
import PushGroup from 'model/precisionmarketing/pushplan/PushGroup';
import ParticipateMember from 'model/member/ParticipateMember';

export default class PointsMetaRuleItem extends TabAttributes {
  // 渠道适用范围
  channelRange: Nullable<ChannelRange> = null
  // 适用门店
  useStores: Nullable<StoreRange> = null
  // 积分名称
  name: Nullable<string> = null
  // 每销费多少金额或数量，不满不积分
  cost: Nullable<number> = null
  //消费门槛
  discountThreshold: Nullable<number> = null
  // 获得多少积分
  points: Nullable<number> = null
  // 特殊商品组列表
  goodsGroupList: MetaGoodsGroup[] = []
  // DEFAULT-消费金额不足部分不计积分;POINTS_DOWN-全部消费金额都纳入积分计算（应算尽算），去尾取整;POINTS_HALF_UP-全部消费金额都纳入积分计算（应算尽算），四舍五入取整 ；POINTS_DOWN_SCALE_ONE-全部消费金额都纳入积分计算（应算尽算），去尾保留一位小数;POINTS_HALF_UP_SCALE_ONE-全部消费金额都纳入积分计算（应算尽算），四舍五入保留一位小数
  strategy: Nullable<string> = null
  // 参与人群
  rule: Nullable<PushGroup> = null

  constructor(n: string) {
    super(n);
    this.rule = new PushGroup()
    this.rule.type = 'USER_GROUP_AND_MEMBER_TAG'
    this.rule.participateMember = new ParticipateMember()
  }

  public static match(channel: Channel, channelRange: Nullable<ChannelRange>) {
    if (channelRange) {
      if (channelRange.channelRangeType === ChannelRangeType.ALL) {
        return true
      } else if (channelRange.channelRangeType === ChannelRangeType.PART) {
        if (channelRange.channels.filter((c: any) => c.type === channel.type && c.id === channel.id).length > 0) {
          return true
        }
      } else if (channelRange.channelRangeType === ChannelRangeType.EXCLUDE) {
        if (channelRange.channels.filter((c: any) => c.type === channel.type && c.id === channel.id).length > 0) {
          return false
        } else {
          return true
        }
      }
    }
    return false
  }
}