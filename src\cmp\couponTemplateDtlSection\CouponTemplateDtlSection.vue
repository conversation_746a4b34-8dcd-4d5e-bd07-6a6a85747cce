<template>
  <div class="coupon-template-section" v-if="data.coupons">
    <div class="basic-info">
      <template v-if="isShowDiscountSetting">
        <div class="section-title">{{i18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/优惠设置')}}</div>
        <FormItem :label="i18n('优惠上限：')" v-if="isDiscountLimit">
          <div style="height: 36px; line-height: 36px">
            {{ getMoney(data.coupons.discountCouponAttribute.maxDiscountAmount,
						data.coupons.discountCouponAttribute.maxDiscountQty, data.coupons.useThresholdType) }}
          </div>
        </FormItem>
        <FormItem :label="i18n('券后价：')" v-if="isShowAfterUseAmount">
          <div style="height: 36px; line-height: 36px">
            {{ data.coupons.exchangeGoodsCouponAttribute.afterUseAmount | fmt}}{{i18n('元')}}
          </div>
        </FormItem>
        <FormItem :label="i18n('用券门槛：')" v-if="isShowThreshold">
          <CouponThresholdDtl :data="data"></CouponThresholdDtl>
        </FormItem>
        <FormItem :label="i18n('券承担方：')" v-if="isShowCouponBears">
          <CouponBearer :data="data" :parties="parties"></CouponBearer>
        </FormItem>
        <FormItem :label="i18n('用券记录方式：')" v-if="isShowUseMethod">
          <UseCouponRecordMethod :data="data" :parties="parties"></UseCouponRecordMethod>
        </FormItem>
        <!-- 叠加用券 -->
        <GroupMutexTemplateDtl v-if="isShowMutex" :info="data.coupons" :deep-dialog="deepDialog">
        </GroupMutexTemplateDtl>
        <!-- 叠加优惠 -->
        <template v-if="isShowSuperposition">
          <FormItem :label="i18n('促销') + '：'">
            <div class="item-height">
              {{ getCouponPromotionInfo(data.coupons) }}
              <span @click.stop="openPromotionShow" style="color: #1597FF; cursor: pointer;"
                v-if="data.coupons.promotionSuperpositionType == 'PART'">{{ i18n('查看促销单') }}</span>
            </div>
          </FormItem>
          <FormItem :label="i18n('/公用/券模板/微盟适用商品/会员价') + '：'">
            <div class="item-height">{{memberPriceFav ? i18n('叠加') : i18n('不叠加')}}</div>
          </FormItem>
          <FormItem :label="i18n('人工折扣') + '：'">
            <div class="item-height">{{manualDiscountFav ? i18n('叠加') : i18n('不叠加')}}</div>
          </FormItem>
          <FormItem :label="i18n('其他优惠') + '：'">
            <div class="item-height">{{otherDiscountFav ? i18n('叠加') : i18n('不叠加')}}</div>
          </FormItem>
        </template>
      </template>
      <template>
        <div class="section-title">{{i18n('/营销/券礼包活动/券查询/用券时间')}}</div>
        <FormItem :label="i18n('券有效期：')" v-if="data && data.coupons && data.coupons.validityInfo">
          <div class="item-height" v-if="data.coupons.validityInfo.validityType === 'RALATIVE'">
            {{ getValidateDate(data.coupons.validityInfo.delayEffectDays, data.coupons.validityInfo.validityDays,
						data.coupons.validityInfo.expiryType, data.coupons.validityInfo.months) }}
          </div>
          <div class="item-height" v-if="data.coupons.validityInfo.validityType === 'FIXED'">
            {{ i18n("固定有效期") }}，{{ data.coupons.validityInfo.beginDate | dateFormate3
					}}{{ i18n("至") }}{{ data.coupons.validityInfo.endDate | dateFormate3 }}
          </div>
        </FormItem>
        <FormItem :label="i18n('用券时段：')" v-if="['freight', 'points', 'equity'].indexOf(data.coupons.couponBasicType) === -1">
          <UseTimePeriod :data="data"></UseTimePeriod>
        </FormItem>
      </template>
      <template v-if="['equity'].indexOf(data.coupons.couponBasicType) === -1">
        <div class="section-title">{{i18n('用券范围')}}</div>
        <FormItem :label="i18n('用券渠道：')" v-if="['points', 'equity'].indexOf(data.coupons.couponBasicType) === -1">
          <div v-if="data && data.coupons && data.coupons.useChannels" style="line-height: 36px; word-break: break-word">
            {{ getChannel(data.coupons.useChannels) }}
          </div>
        </FormItem>
        <FormItem :label="i18n('用券门店：')">
          <ActiveStoreDtl style="width: 60%; min-width: 600px" v-if="data && data.coupons && data.coupons.useStores" :data="data.coupons.useStores">
          </ActiveStoreDtl>
        </FormItem>
        <!-- 用券商品 -->
        <UseCouponGoods :data="data"></UseCouponGoods>
      </template>
       <template v-if="['all_cash','all_discount','special_price','exchange_goods'].includes(data.coupons.couponBasicType) && isPmsPayEngine">
          <div class="section-title">{{i18n('用券限量')}}</div>
           <FormItem :label="i18n('用券限量')  + '：'">
            <div v-if="data && data.coupons && data.coupons.maxDailyMemberQuotaQty" style="line-height: 36px; word-break: break-word">
                {{i18n('每人每天限用')}} {{data.coupons.maxDailyMemberQuotaQty}}{{i18n('张')}}
            </div> 
            <div v-else style="line-height: 36px; word-break: break-word">
                {{formatI18n('/权益/券/券初始化','不限用券张数')}}
            </div>
           </FormItem>
       </template>

    </div>
    <div class="advanced-setting">
      <template>
        <div class="section-title">{{i18n('高级设置')}}</div>
        <FormItem :label="i18n('券码生成规则：')"
          v-if="!options.hideOuterNumberNamespace && ['freight','equity'].indexOf(data.coupons.couponBasicType) === -1 && !wxScanForCoupon">
          <div style="height: 36px; line-height: 36px" v-if="data && data.coupons && data.coupons.codePrefix">
            {{ i18n("/营销/券礼包活动/核销第三方券/固定开头") }}-{{ data.coupons.codePrefix }}
          </div>
          <div style="height: 36px; line-height: 36px" v-else>
            {{ i18n("/权益/券/券模板/券码前缀/系统随机生成") }}
          </div>
        </FormItem>
        <FormItem :label="i18n('外部券模板号') + '：'" v-if="data.coupons.outerRelations" style="height:36px">
          <div style="line-height:36px">
            {{"[" + data.coupons.outerRelations[0].channel.id + "]"}}{{data.coupons.outerRelations[0].channelName === null ? '-' : data.coupons.outerRelations[0].channelName}}
            <span style="margin-left:10px">{{data.coupons.outerRelations[0].outerNumber}}</span>
          </div>
        </FormItem>
        <FormItem :label="i18n('能否转赠') + '：'" v-if="['points','equity'].indexOf(data.coupons.couponBasicType) === -1" style="height:36px">
          <div class="can-present" v-if="data && data.coupons && data.coupons.transferable != null"
            v-html="data.coupons.transferable ? i18n('是') : i18n('否')">
          </div>
        </FormItem>
        <FormItem :label="i18n('标签') + '：'" v-if="['equity'].indexOf(data.coupons.couponBasicType) === -1">
          <template v-if="getTemplateLabel().length > 0">
            <div class="tag-block" v-for="(item,index) in getTemplateLabel()" :key="index">
              {{item}}
            </div>
          </template>
          <div style="line-height: 36px; word-break: break-word" v-else>
            --
          </div>
        </FormItem>
        <FormItem :label="i18n('核销场景')" v-if="['all_cash', 'goods_cash'].includes(data.coupons.couponBasicType)">
          <div style="line-height:36px">{{getVerificationRule}}</div>
        </FormItem>
        <FormItem :label="i18n('同步渠道') + '：'"
          v-if="['points', 'random_cash', 'goods', 'freight','equity'].indexOf(data.coupons.couponBasicType) === -1">
          <div v-if="getSychChannel().length > 0" style="line-height: 36px; word-break: break-word">
            {{ getSychChannel() }}
          </div>
          <div style="line-height: 36px; word-break: break-word" v-else>
            --
          </div>
        </FormItem>
        <FormItem :label="i18n('价格') + '：'" v-if="['points','equity'].indexOf(data.coupons.couponBasicType) === -1">
          <div v-if="data.coupons.salePrice" style="line-height: 36px; word-break: break-word">
            {{ data.coupons.salePrice }} {{i18n('元')}}
          </div>
          <div style="line-height: 36px; word-break: break-word" v-else>
            --
          </div>
        </FormItem>
        <FormItem :label="i18n('账款项目') + '：'" v-if="['points'].indexOf(data.coupons.couponBasicType) === -1">
          <div v-if="data.coupons.termsModel" style="line-height: 36px; word-break: break-word">
            {{ data.coupons.termsModel }}
          </div>
          <div style="line-height: 36px; word-break: break-word" v-else>
            --
          </div>
        </FormItem>
        <FormItem :label="i18n('C端我的券角标') + '：'" v-if="['points','equity'].indexOf(data.coupons.couponBasicType) === -1">
          <div class="can-present"
            v-if="data && data.coupons && data.coupons.couponSubscriptType != null && data.coupons.couponSubscriptType === 'ONLINE'">
            {{ i18n("线上券") }}
          </div>
          <div class="can-present"
            v-else-if="data && data.coupons && data.coupons.couponSubscriptType != null && data.coupons.couponSubscriptType === 'OFFLINE'">
            {{ i18n("线下券") }}
          </div>
          <div class="can-present"
            v-else-if="data && data.coupons && data.coupons.couponSubscriptType != null && data.coupons.couponSubscriptType === 'COMMON'">
            {{ i18n("通用券") }}
          </div>
          <div class="can-present"
            v-else-if="data && data.coupons && data.coupons.couponSubscriptType != null && data.coupons.couponSubscriptType === 'OUTSIDE'">
            {{ i18n("外部券") }}
          </div>
          <div style="line-height: 36px; word-break: break-word" v-else>
            --
          </div>
        </FormItem>

        <FormItem  :label="i18n('备注') + '：'" v-if="['all_cash','all_discount','special_price','goods','exchange_goods'].includes(data.coupons.couponBasicType)">
          <div style="line-height: 30px;word-break: break-word"
            v-html="data.coupons.notes ? data.coupons.notes.replace(/\n/g, '<br/>') : '--'">
          </div>
        </FormItem>    

        <FormItem :label="i18n('核销链接') + '：'">
          <div style="line-height: 30px;word-break: break-word"
            v-html="data.coupons.writeOffLink ? data.coupons.writeOffLink.replace(/\n/g, '<br/>') : '--'">

          </div>
        </FormItem>   
      </template>
    </div>
    <!-- 三方平台设置 -->
    <div class="three-platform" v-if="isShowWeimob || isShowRex">
      <template v-if="isShowWeimob">
        <div class="section-title">
          {{i18n('微盟平台券')}}
        </div>
        <FormItem :label="i18n('剩余库存')">
          <div class="item-height" v-if="data && data.coupons" v-html="data.coupons.total || i18n('不限制')">
          </div>
        </FormItem>
        <template v-if="data && data.coupons && data.coupons.weimobCoupon">
          <FormItem :label="i18n('每人限领')">
            <div class="item-height" v-html="getLimitReceiveText"></div>
          </FormItem>
          <FormItem :label="i18n('可领券时间')">
            <div class="item-height">
              <span v-if="data.coupons.weimobCoupon.sendTimeType == 1">{{i18n('不限制')}}</span>
              <span v-else-if="data.coupons.weimobCoupon.sendTimeType == 2">
                {{i18n('固定时间')}}：
                {{data.coupons.weimobCoupon.sendStartDate | dateFormate3}}
                {{i18n('至')}}
                {{data.coupons.weimobCoupon.sendEndDate | dateFormate3}}
              </span>
            </div>
          </FormItem>
          <FormItem :label="i18n('发放渠道')">
            <div class="item-height" v-html="getIssueChannel"></div>
          </FormItem>
          <FormItem :label="i18n('券叠加其他优惠')">
            <div class="item-height">
              <span v-if="!data.coupons.weimobCoupon.canUseDiscount.canUseWithOtherDiscount">{{formatI18n('/公用/券模板/不可叠加')}}</span>
              <span v-else>
                {{getMallDiscount}}
              </span>
            </div>
          </FormItem>
          <FormItem :label="i18n('核销场景')">
            <div class="item-height" v-html="getUseScene"></div>
          </FormItem>
          <FormItem :label="i18n('微盟适用商品')">
            <div class="item-height">
              <span v-if="goodsInfo.type == 'none'">{{ goodsInfo.str }}</span>
              <el-tooltip class="item" effect="light" placement="top" v-if="goodsInfo.type == 'tips'">
                <div slot="content">
                  <div style="width: 100%; max-height: 300px; overflow-y: auto;">
                    <span v-for="(item, index) in goodsInfo.list" :key="index">{{ item.name }} <br /></span>
                  </div>
                </div>
                <span style="border-bottom: 1px dashed rgb(182, 186, 191); cursor: pointer;">{{ goodsInfo.str }}</span>
              </el-tooltip>
              <span v-if="excludedGoodsList.length > 0">，</span>
              <el-tooltip class="item" effect="light" placement="top" v-if="excludedGoodsList.length > 0">
                <div slot="content">
                  <div style="width: 100%; max-height: 300px; overflow-y: auto;">
                    <span v-for="(item, index) in excludedGoodsList" :key="index">{{ item.name }} <br /></span>
                  </div>
                </div>
                <span style="border-bottom: 1px dashed rgb(182, 186, 191); cursor: pointer;">{{ i18n('排除')
							}}{{ excludedGoodsList.length }}{{ i18n('项商品') }}</span>
              </el-tooltip>
            </div>
          </FormItem>
          <FormItem :label="i18n('微盟适用门店')">
            <div class="item-height">
              <span v-if="data.coupons.weimobCoupon.limitOrgType === 'ALL'">{{i18n('全部门店')}}</span>
              <span v-else>{{ getStoreCount() }}</span>
            </div>
          </FormItem>
        </template>

      </template>
      <template v-if="isShowRex">
        <div class="section-title">
          {{i18n('REX平台券')}}
        </div>
        <FormItem :label="i18n('净值')">
          <div style="height: 36px; line-height: 36px">
            {{ data.coupons.rexCoupon.netWorth }}&nbsp;&nbsp;
            {{ i18n('元') }}
          </div>
        </FormItem>
        <FormItem :label="i18n('单笔订单限用')">
          <div style="height: 36px; line-height: 36px">
            {{ data.coupons.rexCoupon.limitedUse }}&nbsp;&nbsp;
            {{ i18n("张") }}
          </div>
        </FormItem>

      </template>
    </div>
    <PromotionShowDialog ref="promotionShow" :templateId="data.coupons && data.coupons.templateId || ''">
    </PromotionShowDialog>
  </div>
</template>

<script lang="ts" src="./CouponTemplateDtlSection.ts"></script>

<style lang="scss">
.coupon-template-section {
  .basic-info {
    padding: 12px 24px 12px 24px;
    background: #ffffff;
    border-radius: 8px;
  }

  .advanced-setting {
    padding: 24px;
    background: #ffffff;
    border-radius: 8px;
    margin-top: 16px;

    .can-present {
      line-height: 36px;
    }
    .tag-block {
      display: inline-block;
      text-align: center;
      min-width: 68px;
      height: 24px;
      background: #f7f9fc;
      border-radius: 14px;
      border: 1px solid #d7dfeb;
      line-height: 24px;
      word-break: break-word;
      padding: 0 6px;
      margin-top: 6px;
      margin-left: 8px;

      &:nth-child(1) {
        margin-left: 0;
      }
    }
  }

  .three-platform {
    padding: 24px;
    background: #ffffff;
    border-radius: 8px;
    margin-top: 16px;
  }
  .section-title {
    font-size: 16px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #242633;
    line-height: 24px;
    padding: 12px 0;
  }
  .item-height {
    height: 36px;
    line-height: 36px;
  }

  .overtext {
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    line-clamp: 4;
    -webkit-box-orient: vertical;
  }

  .qf-form-item .qf-form-label {
    color: #79879e;
  }
}
</style>
