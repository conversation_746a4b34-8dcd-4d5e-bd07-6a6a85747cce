import BaseCouponActivity from 'model/v2/coupon/BaseCouponActivity'
import WeiXinMerchantCouponBagInfo from './WeiXinMerchantCouponBagInfo'
import WeiXinPayCouponBagInfo from './WeiXinPayCouponBagInfo'
import WeiXinSingleCouponInfo from './WeiXinSingleCouponInfo'
import { DateType } from './DateType'
import { ReleaseType } from './ReleaseType'
import { WeiXinCouponType } from './WeiXinCouponType'
import DateTimeCondition from "model/common/DateTimeCondition";

export default class WeiXinIssueCouponActivity extends BaseCouponActivity {
  // 活动图片
  image: Nullable<string> = null
  // 数量限制日期类型
  limitPartakeDateType: Nullable<DateType> = null
  // 日期类型下，参数次数限制
  limitMemberPerTime: Nullable<number> = null
  // 投放方式
  releaseType: Nullable<ReleaseType> = null
  // 是否修改活动库存
  modifyStock: Nullable<boolean> = null
  // 是否修改活动每日最大限量库存
  modifyPerDayMaxStock: Nullable<boolean> = null
  // 微信券类型
  weiXinCouponType: Nullable<WeiXinCouponType> = null
  // 单券（代金券）信息
  payCouponSingleInfo: Nullable<WeiXinSingleCouponInfo> = null
  // 单券（其他服务商代金券）信息
  isvPayCouponSingleInfo: Nullable<WeiXinSingleCouponInfo> = null
  // 券包（商家券）信息
  merchantCouponBagInfo: Nullable<WeiXinMerchantCouponBagInfo> = null
  // 券包（代金券）信息
  payCouponBagInfo: Nullable<WeiXinPayCouponBagInfo> = null
  // 操作时间
  create: Nullable<Date> = null
  // 操作人信息
  operator: Nullable<string> = null
	// true表示参与叠加促销，false表示不参与叠加促销
	joinPromotion?: Nullable<boolean> = null
  // 外部活动号
  outerNumberId: Nullable<string> = null;
  // 时间条件
  dateTimeCondition: DateTimeCondition = new DateTimeCondition()
}