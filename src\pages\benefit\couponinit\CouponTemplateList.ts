import { Component, Vue } from "vue-property-decorator";
import FormItem from "cmp/formitem/FormItem.vue";
import ListWrapper from "cmp/list/ListWrapper.vue";
import I18nPage from "common/I18nDecorator";
import BreadCrume from "cmp/bread-crumb/BreadCrume";
import CouponTemplateApi from "http/coupon/template/CouponTemplateApi";
import CouponTemplateFilter from "model/coupon/template/CouponTemplateFilter";
import ChannelRange from "model/common/ChannelRange";
import RSChannelManagementFilter from "model/common/RSChannelManagementFilter";
import ChannelManagementApi from "http/channelmanagement/ChannelManagementApi";
import RSChannelManagement from "model/common/RSChannelManagement";
import Channel from "model/common/Channel";
import { LocalStorage } from "mgr/BrowserMgr";
import CouponCancel from "model/coupon/template/CouponCancel";
import StoreSelectorDialog from "cmp/selectordialogs/StoreSelectorDialog";
import RSOrg from "model/common/RSOrg";
import IdName from "model/common/IdName";
import BatchAddOrgRequest from "model/coupon/template/BatchAddOrgRequest";
import ZoneApi from "http/area/ZoneApi";
import ZoneFilter from "model/datum/zone/ZoneFilter";
import Zone from "model/datum/zone/Zone";
import OrgApi from "http/org/OrgApi";
import RSOrgFilter from "model/common/RSOrgFilter";
import GoodsApi from "http/goods/GoodsApi";
import RSGoodsFilter from "model/common/RSGoodsFilter";
import CategoryApi from "http/category/CategoryApi";
import RSCategoryFilter from "model/common/RSCategoryFilter";
import BrandApi from "http/brand/BrandApi";
import RSBrandFilter from "model/common/RSBrandFilter";
import RSGoods from "model/common/RSGoods";
import RSCategory from "model/common/RSCategory";
import RSBrand from "model/common/RSBrand";
import CouponTemplate from "model/coupon/template/CouponTemplate";
import CouponInfo from "model/common/CouponInfo";
import CouponTemplateTagApi from "http/coupon/template/CouponTemplateTagApi";
import CouponTemplateTagFilter from "model/coupon/CouponTemplateTagFilter";
import CouponTemplateTag from "model/coupon/CouponTemplateTag";
import CouponTemplateTagRelation from "model/coupon/CouponTemplateTagRelation";
import QueryCondition from "cmp/querycondition/QueryCondition";
import SelectStores from "cmp/selectStores/SelectStores";
import CommonUtil from "util/CommonUtil";
import PurchaseCouponManagerApi from "http/couponPurchase/PurchaseCouponManagerApi";
import ActivityState from "cmp/activitystate/ActivityState";
import DownloadCenterDialog from "pages/main/dowmload_center_dialog/DownloadCenterDialog";
import UploadFileModal from './components/UploadFileModal.vue'

@Component({
  name: "CouponTemplateList",
  components: {
    FormItem,
    ListWrapper,
    BreadCrume,
    StoreSelectorDialog,
    QueryCondition,
    SelectStores,
    UploadFileModal,
    DownloadCenterDialog
  },
})
@I18nPage({
  prefix: [
    "/储值/会员储值/储值充值活动/列表页面",
    "/营销/券礼包活动/券礼包活动",
    "/公用/按钮",
    '/营销/积分活动/积分活动/积分抵现活动/活动管理/表格上面的统计',
    '/公用/券模板',
    '/设置/权限/角色管理/新角色/新建角色',
    "/公用/活动/提示信息",
    '/权益/券/券模板',
    '/公用/券模板/商品折扣券/折扣力度'
  ],
})
export default class CouponTemplateList extends Vue {
  warnMsg = "";
  i18n: any;
  query: CouponTemplateFilter = new CouponTemplateFilter();
  selectedArr: CouponTemplate[] = [];
  activeName = "first";
  singleAll = false;
  panelArray: any = [];
  total = {
    all: 0,
    allCashTotal: 0,
    goodsCashTotal: 0,
    allDiscountTotal: 0,
    rfmTypeTotal: 0,
    goodsDiscountTotal: 0,
    goodsTotal: 0,
    freightTotal: 0,
    randomTotal: 0,
    exchangeGoodsTotal: 0,
    pointExchangeTotal: 0,
    specialCouponTotal: 0,
    equityCouponTotal: 0
  };
  $refs: any;
  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10,
  };
  tableData: CouponTemplate[] = [];
  dialogShow = false;
  channels: RSChannelManagement[] = [];
  loading: Boolean = false;
  storeShow: Boolean = false
  selectedStores: RSOrg[] = []
  storeStr: string = ''
  selectedDeductStores: RSOrg[] = []
  deductStoreStr: string = ''
  inputValueRules: any
  selectStoreForm: any = {}
  queryParamsLike: string = ''
  zoneList: Nullable<Zone[]> = []
  storeList: Nullable<RSOrg[]> = []
  selectAll: boolean = false
  auditPermission: boolean = false //是否具有审核权限
  tagList: CouponTemplateTag[] = []

  goodsQueryLike: string = ''
  goodsList: Nullable<RSGoods[] | RSCategory[] | RSBrand[]> = []
  mustHaveTagUuids: string[] = []
  showTip = false
  fileDialogVisible = false
  uploadDialogShow = false;

  get getAllCount() {
    return `${this.i18n("全部")}(${this.total.all})`;
  }

  get getAllCashCouponCount() {
    return `${this.formatI18n("/公用/券模板/现金券")}(${this.total.allCashTotal + this.total.goodsCashTotal})`;
  }

  get getGoodsCashCouponCount() {
    return `${this.formatI18n("/公用/券模板/商品现金券")}(${this.total.goodsCashTotal})`;
  }

  get getAllDiscountCouponCount() {
    return `${this.formatI18n("/公用/券模板/折扣券")}(${this.total.allDiscountTotal + this.total.rfmTypeTotal + this.total.goodsDiscountTotal})`;
  }

  get getSpecialCouponCount() {
    return `${this.formatI18n("/公用/券模板/特价券")}(${this.total.specialCouponTotal})`;
  }

  get getGoodsDiscountCouponCount() {
    return `${this.formatI18n("/公用/券模板/商品折扣券")}(${this.total.rfmTypeTotal})`;
  }
  get getSingeDiscountCouponCount() {
    return `${this.formatI18n("/公用/券模板/单品折扣券")}(${this.total.goodsDiscountTotal})`;
  }
  get getPickUpDiscountCouponCount() {
    return `${this.formatI18n("/公用/券模板/提货券")}(${this.total.goodsTotal})`;
  }
  get getFreightCouponCount() {
    return `${this.formatI18n("/公用/券模板/运费券")}(${this.total.freightTotal})`;
  }
  get getRandomCouponCount() {
    return `${this.formatI18n("/公用/券模板/随机金额券")}(${this.total.randomTotal})`;
  }

  get getExchangeCouponCount() {
    return `${this.formatI18n("/公用/券模板/兑换券")}(${this.total.exchangeGoodsTotal})`;
  }

  get getEquityCouponCount() {
    return `${this.formatI18n("/公用/券模板/权益券")}(${this.total.equityCouponTotal})`;
  }

  get getPointCouponCount() {
    return `${this.formatI18n("/公用/券模板/积分券")}(${this.total.pointExchangeTotal})`;
  }

  get showRandom() {
    return LocalStorage.getItem("sysConfig") && LocalStorage.getItem("sysConfig").enableRandomCashCouponDisplay;
  }

  get showPoint() {
    return LocalStorage.getItem("sysConfig") && LocalStorage.getItem("sysConfig").enablePointsExchangeCouponDisplay;
  }

  get showEquity() {
    return LocalStorage.getItem("sysConfig") && LocalStorage.getItem("sysConfig").enableEquityCouponDisplay;
  }

  get getIdName() {
    return (idname: IdName) => {
      return `[${idname.id}]${idname.name}`
    }
  }

  get getCodeName() {
    return (item: RSGoods) => {
      return `[${item.code}]${item.name}`
    }
  }

  created() {
    this.panelArray = [
      {
        name: this.formatI18n("/权益/券/券模板/券模板"),
        url: "",
      },
    ];
    this.inputValueRules = [
      // {
      //   validator: (rule: any, value: string, callback: any) => {
      //     if (this.selectedStores.length === 0) {
      //       callback(new Error(this.formatI18n("/营销/积分活动/积分活动/商品满额加送积分/表单校验/提示信息/请选择门店范围")));
      //     }
      //     callback();
      //   },
      //   trigger: ["change", "blur"],
      // },{
      //   validator: (rule: any, value: string, callback: any) => {
      //     if (this.selectedDeductStores.length === 0) {
      //       callback(new Error(this.formatI18n("/营销/积分活动/积分活动/商品满额加送积分/表单校验/提示信息/请选择门店范围")));
      //     }
      //     callback();
      //   },
      //   trigger: ["change", "blur"],
      // },

    ];
    this.query.stateEquals = 'EFFECTED' //默认查询已生效券模板
    this.getChannels();
    this.getAuditConfig()
    this.getTagList()
  }

  activated() {
    this.getStoreValueList();
  }

  doSelectAll() {
    if (this.selectAll) {
      for (let row of this.tableData) {
        this.$refs['table'].toggleRowSelection(row, true)
      }
    } else {
      this.$refs['table'].clearSelection();
    }
  }

  doAddCouponTemplate() {
    this.$router.push({ name: "coupon-template-add" });
  }
  doLabelCouponTemplate() {
    this.$router.push({ name: "coupon-template-label" });
  }
  doHandleClick() {
    this.selectedArr = []
    this.$refs.table.clearSelection()
    this.page.currentPage = 1;
    this.getStoreValueList();
  }
  doCopy(row: any) {
    this.$router.push({ name: "coupon-template-add", query: { id: row.number, from: "copy" } });
  }

  doModify(row: any) {
    this.$router.push({ name: "coupon-template-add", query: { id: row.number, from: "edit" } });
  }

  doCancel(row: any) {
    this.$confirm(
      this.formatI18n("/公用/券模板/作废以后，则优惠券不能领取，不能编辑优惠券内容，但不影响已经领取成功的优惠券的使用，确定要作废吗？"),
      this.formatI18n("/资料/区域/作废"),
      {
        confirmButtonText: this.formatI18n("/设置/权限/角色管理/新角色/新建角色/确认"),
        cancelButtonText: this.formatI18n("/设置/权限/角色管理/新角色/新建角色/取消"),
      }
    )
      .then(() => {
        const params = new CouponCancel();
        params.templateNum = row.number;
        CouponTemplateApi.cancel(params).then((res: any) => {
          if (res.code === 2000) {
            this.$message.success(this.formatI18n("/公用/活动/提示信息/操作成功"));
            this.getStoreValueList()
          }
        });
      })
      .catch(() => { });
  }

  //单个审核
  doAudit(row: any) {
    this.$confirm(
      this.i18n("审核通过后，券模板将生效，确定审核吗？"),
      this.i18n("审核"),
      {
        confirmButtonText: this.i18n("确认"),
        cancelButtonText: this.i18n("取消"),
      }
    ).then(() => {
      const params = {
        numbers: [row.number],
        operator: ''
      }
      CouponTemplateApi.batchAudit(params).then((res) => {
        if (res.code === 2000) {
          this.$message.success(this.i18n("操作成功"));
          this.getStoreValueList()
        }
      });
    })
  }

  /**
   * 查询
   */
  doSearch() {
    this.page.currentPage = 1;
    this.getStoreValueList();
  }

  // 关闭文件中心弹框
  doDownloadDialogClose() {
    this.showTip = false
    this.fileDialogVisible = false
  }

  async exportFile() {
    const loading = CommonUtil.Loading()
    try {
      if (this.activeName === "first") {
        this.query.typeIn = null as any;
      } else if (this.activeName === "second") {
        this.query.typeIn = ["all_cash", "goods_cash"];
      }
        // else if (this.activeName === "third") {
        // 	this.query.typeIn = "goods_cash";
      // }
      else if (this.activeName === "forth") {
        this.query.typeIn = ["all_discount", "rfm_type", "goods_discount"];
      } else if (this.activeName === "special") {
        this.query.typeIn = ["special_price"];
      } else if (this.activeName === "five") {
        this.query.typeIn = ["rfm_type"];
      } else if (this.activeName === "six") {
        this.query.typeIn = ["goods_discount"];
      } else if (this.activeName === "seven") {
        this.query.typeIn = ["goods"];
      } else if (this.activeName === "eight") {
        this.query.typeIn = ["freight"];
      } else if (this.activeName === "nine") {
        this.query.typeIn = ["random_cash"];
      } else if (this.activeName === "ten") {
        this.query.typeIn = ["exchange_goods"];
      } else if (this.activeName === "eleven") {
        this.query.typeIn = ["points"];
      } else if (this.activeName === "twelve") {
        this.query.typeIn = ["equity"];
      }
      this.query.page = this.page.currentPage - 1;
      this.query.pageSize = this.page.size;
      this.query.mustHaveTagUuids = this.mustHaveTagUuids ? this.mustHaveTagUuids : []
      const { code, msg } = await CouponTemplateApi.export(this.query)
      loading.close()
      if (code === 2000) {
        this.showTip = true
        this.fileDialogVisible = true
      } else {
        this.$message.error(msg as string)
      }
    } catch (error) {
      loading.close()
      this.$message.error((error as Error).message)
    }
  }

  handleSelectionChange(val: any) {
    this.selectedArr = val;
  }

  /**
   * 重置
   */
  doReset() {
    this.activeName = "first";
    this.query = new CouponTemplateFilter();
    this.queryParamsLike = ''
    this.goodsQueryLike = ''
    this.mustHaveTagUuids = []
    this.getStoreValueList();
  }

  /**
   * 去详情
   */
  doGoDtl(row: any) {
    this.$router.push({ name: "coupon-template-dtl", query: { id: row.billNumber } });
  }

  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val;
    this.getStoreValueList();
  }

  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val;
    this.getStoreValueList();
  }
  doToDtl(row: any) {
    this.$router.push({ name: "coupon-template-dtl", query: { id: row.number } });
  }
  getChannel(curChannel: ChannelRange) {
    let str: string = "";
    if (curChannel.channelRangeType === "ALL") {
      str = this.formatI18n("/公用/券模板/用券渠道", "全部渠道");
      return str;
    } else {
      if (this.channels && this.channels.length > 0) {
        if (curChannel && curChannel.channels && curChannel.channels.length > 0) {
          curChannel.channels.forEach((sub: Channel) => {
            this.channels.forEach((item: RSChannelManagement) => {
              if (item && item.channel && sub.id === item.channel.id && sub.type === item.channel.type) {
                str += `${item.name}，`;
              }
            });
          });
        }
      }
      str = str.substring(0, str.length - 1);
    }
    return str;
  }

  tableConfigTicket(outerRelations: any) {
    let channelType = "";
    if (outerRelations.length > 1) {
      let newArr = outerRelations.map((item: any) => {
        item.channel["typeName"] = "";
        if ((item.channel.type == "weimob")) {
          item.channel.typeName = "微盟";
        } else if (item.channel.type == 'rex') {
          item.channel.typeName = 'REX'
        } else if (item.channel.type == 'weixin'){
          item.channel.typeName = "微信";
        } else if (item.channel.type == 'DouYin') {
          item.channel.typeName = "抖音";
        } else if (item.channel.type == 'MeiTuan') {
          item.channel.typeName = "美团";
        } else {
          item.channel.typeName = "-";
        }
        return item.channel.typeName + "-" + item.channel.id + "-" + item.outerNumber;
      });
      return newArr;
    } else if (outerRelations.length == 1) {
      let newOuterRelations = outerRelations[0];
      if (newOuterRelations.channel.type == "weimob") {
        channelType = "微盟";
      } else if (newOuterRelations.channel.type == 'rex') {
        channelType = 'REX'
      } else if (newOuterRelations.channel.type == 'weixin') {
        channelType = "微信";
      } else if (newOuterRelations.channel.type == 'DouYin') {
        channelType = "抖音";
      } else if (newOuterRelations.channel.type == 'MeiTuan') {
        channelType = "美团";
      } else {
        channelType = "-";
      }
      return channelType + "-" + newOuterRelations.channel.id + "-" + newOuterRelations.outerNumber;
    }
  }

  getUnit(amount: number, type: string) {
    if (type === "all_cash" || type === "goods_cash" || type === "freight") {
      let str: any = this.formatI18n("/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级套餐", "{0}元");
      str = str.replace(/\{0\}/g, Number(amount).toFixed(2));
      return str;
    } else if (type === "all_discount" || type === "rfm_type" || type === "goods_discount") {
      let str: any = this.formatI18n("/会员/等级/等级管理/已初始化状态的免费等级/表格/折扣", "{0}折");
      str = str.replace(/\{0\}/g, Number(amount).toFixed(1));
      return str;
    } else {
      return "--";
    }
  }
  private getStoreValueList() {
    this.loading = true;
    if (this.activeName === "first") {
      this.query.typeIn = null as any;
    } else if (this.activeName === "second") {
      this.query.typeIn = ["all_cash", "goods_cash"];
    }
    // else if (this.activeName === "third") {
    // 	this.query.typeIn = "goods_cash";
    // } 
    else if (this.activeName === "forth") {
      this.query.typeIn = ["all_discount", "rfm_type", "goods_discount"];
    } else if (this.activeName === "special") {
      this.query.typeIn = ["special_price"];
    } else if (this.activeName === "five") {
      this.query.typeIn = ["rfm_type"];
    } else if (this.activeName === "six") {
      this.query.typeIn = ["goods_discount"];
    } else if (this.activeName === "seven") {
      this.query.typeIn = ["goods"];
    } else if (this.activeName === "eight") {
      this.query.typeIn = ["freight"];
    } else if (this.activeName === "nine") {
      this.query.typeIn = ["random_cash"];
    } else if (this.activeName === "ten") {
      this.query.typeIn = ["exchange_goods"];
    } else if (this.activeName === "eleven") {
      this.query.typeIn = ["points"];
    } else if (this.activeName === "twelve") {
      this.query.typeIn = ["equity"];
    }
    this.query.page = this.page.currentPage - 1;
    this.query.pageSize = this.page.size;
    this.query.mustHaveTagUuids = this.mustHaveTagUuids ? this.mustHaveTagUuids : []
    CouponTemplateApi.query(this.query)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.tableData = resp.data.templateList;
          this.total.all = resp.data.stats.total;
          this.total.allCashTotal = resp.data.stats.allCashTotal;
          this.total.goodsCashTotal = resp.data.stats.goodsCashTotal;
          this.total.allDiscountTotal = resp.data.stats.allDiscountTotal;
          this.total.rfmTypeTotal = resp.data.stats.rfmTypeTotal;
          this.total.goodsDiscountTotal = resp.data.stats.goodsDiscountTotal;
          this.total.freightTotal = resp.data.stats.freightTotal;
          this.total.goodsTotal = resp.data.stats.goodsTotal;
          this.total.randomTotal = resp.data.stats.randomTotal;
          this.total.exchangeGoodsTotal = resp.data.stats.exchangeGoodsTotal
          this.total.pointExchangeTotal = resp.data.stats.pointExchangeTotal
          this.total.specialCouponTotal = resp.data.stats.specialCouponTotal
          this.total.equityCouponTotal = resp.data.stats.equityCouponTotal
          if (this.query.typeIn === null) {
            this.page.total = resp.data.stats.total;
          } else if (this.query.typeIn!.includes('all_cash')) {
            this.page.total = resp.data.stats.allCashTotal + resp.data.stats.goodsCashTotal;
          }
          // else if (this.query.typeIn === "goods_cash") {
          // 	this.page.total = resp.data.stats.goodsCashTotal;
          // } 
          else if (this.query.typeIn!.includes("all_discount")) {
            this.page.total = resp.data.stats.allDiscountTotal + resp.data.stats.rfmTypeTotal + resp.data.stats.goodsDiscountTotal;
          }
          //  else if (this.query.typeIn!.includes("rfm_type")) {
          // 	this.page.total = resp.data.stats.rfmTypeTotal;
          // } else if (this.query.typeIn!.includes("goods_discount")) {
          // 	this.page.total = resp.data.stats.goodsDiscountTotal;
          // } 
          else if (this.query.typeIn!.includes("goods")) {
            this.page.total = resp.data.stats.goodsTotal;
          } else if (this.query.typeIn!.includes("freight")) {
            this.page.total = resp.data.stats.freightTotal;
          } else if (this.query.typeIn!.includes("random_cash")) {
            this.page.total = resp.data.stats.randomTotal;
          } else if (this.query.typeIn!.includes("exchange_goods")) {
            this.page.total = resp.data.stats.exchangeGoodsTotal;
          } else if (this.query.typeIn!.includes("points")) {
            this.page.total = resp.data.stats.pointExchangeTotal;
          } else if (this.query.typeIn!.includes("special_price")) {
            this.page.total = resp.data.stats.specialCouponTotal;
          } else if (this.query.typeIn!.includes("equity")) {
            this.page.total = resp.data.stats.equityCouponTotal;
          }
        }
      })
      .catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      })
      .finally(() => {
        this.loading = false;
      });
  }
  private getChannels() {
    let param: RSChannelManagementFilter = new RSChannelManagementFilter();
    ChannelManagementApi.query(param)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.channels = resp.data;
          this.getStoreValueList();
        }
      })
      .catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
  }

  private showStore() {
    if (this.selectedArr.length === 0) {
      this.$message.warning(this.formatI18n('/公用/券模板/请选择券模板'))
    } else {
      this.storeShow = true
    }
  }

  private storeClose() {
    this.storeShow = false
    this.selectedStores = []
    this.storeStr = ''
    this.selectedDeductStores = []
    this.deductStoreStr = ''
  }

  private storeConfirm() {
    if (this.selectedStores.length > 0 || this.selectedDeductStores.length > 0 ) {
      let params = new BatchAddOrgRequest()
      if (this.selectedStores.length > 0) {
        params.marketingCenter = this.selectedStores[0].marketingCenter
      }
      if (this.selectedDeductStores.length > 0) {
        params.marketingCenter = this.selectedDeductStores[0].marketingCenter
      }
      params.operator = JSON.parse(sessionStorage.getItem('vuex') as string)?.loginInfo.user?.account
      params.orgs = this.selectedStores.map((item: RSOrg) => {
        return item.org
      })
      params.deductOrgs = this.selectedDeductStores.map((item: RSOrg) => {
        return item.org
      })
      params.templateNos = this.selectedArr.map((item: any) => {
        return item.number
      })
      CouponTemplateApi.batchAddOrg(params).then(res => {
        if (res.code === 2000) {
          this.$message.success(this.formatI18n("/公用/活动/提示信息/操作成功"));
          this.storeShow = false
          this.selectedStores = []
          this.selectedDeductStores = []
          this.storeStr = ''
          this.deductStoreStr = ''
        } else {
          this.$message.error(res.msg as string);
        }
      })
    } else {
      this.$message.warning(this.formatI18n('/储值/会员储值/门店储值管理/请至少选择一个门店'))
    }

  }

  getStoreCount(count: number) {
    let str: any = this.formatI18n("/公用/券模板", "已选择{0}家门店");
    str = str.replace(/\{0\}/g, count);
    return str;
  }

  showSelectStoreDialog() {
    this.$refs.storeSelectDialog.open(this.selectedStores, "multiple")
  }

  showSelectDeductStoreDialog() {
    this.$refs.storeSelectDialog1.open(this.selectedDeductStores, "multiple")
  }

  doSubmitGoods(arr: RSOrg[]) {
    console.log(arr)
    this.selectedStores = arr

    let str = "";
    if (arr && arr.length > 0) {
      arr.forEach((item: any) => {
        if (item && item.org && item.org.id) {
          str += item.org.id + `[${item.org.name}];`;
        }
        if (item && item.id) {
          str += item.id + `[${item.name}];`;
        }
      });
    }
    this.storeStr = str

    this.$refs.selectStoreForm.validate();
  }

  doSubmitGoods2(arr: RSOrg[]) {
    console.log(arr)
    this.selectedDeductStores = arr

    let str = "";
    if (arr && arr.length > 0) {
      arr.forEach((item: any) => {
        if (item && item.org && item.org.id) {
          str += item.org.id + `[${item.org.name}];`;
        }
        if (item && item.id) {
          str += item.id + `[${item.name}];`;
        }
      });
    }
    this.deductStoreStr = str

    this.$refs.selectStoreForm.validate();
  }

  queryParamsLikeChange() {
    this.query.zoneLikes = this.query.storeLikes = null
    if (this.queryParamsLike === 'zone') {
      let param = new ZoneFilter()
      ZoneApi.query(param).then((res => {
        this.zoneList = res.data
      }))
    } else if (this.queryParamsLike === 'store') {
      let param = new RSOrgFilter()
      OrgApi.query(param).then((res => {
        this.storeList = res.data
      }))
    } else {
      this.zoneList = []
      this.storeList = []
    }
  }

  goodsQueryChange() {
    this.query.goodsLikes = ''
    this.goodsList = []
    switch (this.goodsQueryLike) {
      case 'goods':
        this.query.goodsTypeEquals = 'GOODS'
        break;
      case 'category':
        this.query.goodsTypeEquals = 'CATEGORY'
        break;
      case 'brand':
        this.query.goodsTypeEquals = 'BRAND'
        break;
      default:
        break;
    }
  }

  selectRemoteFilter(filter: any) {
    if (filter) {
      switch (this.goodsQueryLike) {
        case 'goods':
          let params = new RSGoodsFilter()
          params.page = 0
          params.pageSize = 100
          params.key = filter
          GoodsApi.query(params).then(res => {
            this.goodsList = res.data
          })
          break;
        case 'category':
          let params1 = new RSCategoryFilter()
          params1.page = 0
          params1.pageSize = 100
          params1.key = filter
          CategoryApi.query(params1).then(res => {
            this.goodsList = res.data
          })
          break;
        case 'brand':
          let params2 = new RSBrandFilter()
          params2.page = 0
          params2.pageSize = 100
          params2.key = filter
          BrandApi.query(params2).then(res => {
            this.goodsList = res.data
          })
          break;

        default:
          break;
      }
    } else {
      this.goodsList = []
    }

  }

  //批量审核
  doBatchAudit() {
    if (this.selectedArr.length === 0) return this.$message.warning(this.i18n("请选择券模板"))
    this.$confirm(
      this.i18n("审核通过后，券模板将生效，确定审核吗？"),
      this.i18n('批量审核'),
      {
        confirmButtonText: this.i18n("确认"),
        cancelButtonText: this.i18n("取消"),
      }
    ).then(() => {
      const params = {
        numbers: this.selectedArr.map(item => item.number),
        operator: ''
      }
      CouponTemplateApi.batchAudit(params).then((res) => {
        if (res.code === 2000) {
          let arr = res.data || []
          let message = this.i18n('审核成功{0}条，失败{1}条')
            .replace(/\{0\}/g, arr.length as any)
            .replace(/\{1\}/g, this.selectedArr.length - Number(arr.length) as any)
          this.$message.success(message)
        } else {
          this.$message.error(res.msg || '')
        }
      }).catch(err => {
        this.$message.error(err.message)
      }).finally(() => {
        this.getStoreValueList()
      })
    })
  }

  //获取是否具有审核权限配置
  getAuditConfig() {
    CouponTemplateApi.getEnabledAuditTemplateConfig().then(res => {
      if (res.code === 2000) {
        this.auditPermission = res.data || false
      } else {
        this.$message.error(res.msg || this.i18n('获取审核权限失败'))
      }
    }).catch((err) => {
      this.$message.error(err.message)
    }).finally(() => {})
  }

  //获取标签列表
  getTagList() {
    const params = new CouponTemplateTagFilter()
    params.page = 0
    params.pageSize = 0
    CouponTemplateTagApi.query(params).then((res)=>{
      if(res.code === 2000) {
        this.tagList = res.data || []
      } else {
        this.$message.error( res.msg || '' )
      }
    }).catch((err) => {
      this.$message.error(err.message)
    }).finally(() => {})
  }

  showFaceInfo(row: any) {
    let type = row.type //券类型
    if (['all_cash', 'freight', 'special_price'].indexOf(type) > -1) { //现金券/运费券/特价券显示金额
      return row.faceAmountOrDiscount + this.i18n('元')
    } else if (['all_discount', 'rfm_type', 'goods_discount'].indexOf(type) > -1) { //折扣券展示折扣力度
      return row.faceAmountOrDiscount + this.i18n('折')
    } else {
      return '-'
    }
  }

  getTemplateTag(templateTag: CouponTemplateTagRelation[]) {
    let str = ''
    if (templateTag) {
      str = templateTag[0].tagValue || ''
    } else {
      str = '--'
    }
    return str
  }

  importFile(){
    this.uploadDialogShow = true;
    this.$forceUpdate()
  }

  doDialogClose() {
    this.uploadDialogShow = false;
  }

  doUploadSuccess() {
    this.uploadDialogShow = false;
    this.fileDialogVisible = true;
    this.showTip = true;
  }
}
