import ApiClient from 'http/ApiClient'
import LuckyDrawActivity from 'model/promotion/luckydraw/LuckyDrawActivity'
import LuckyDrawLotteryCode from 'model/promotion/luckydraw/LuckyDrawLotteryCode'
import LuckyDrawLotteryCodeFilter from 'model/promotion/luckydraw/LuckyDrawLotteryCodeFilter'
import LuckyDrawParticipatesRecord from 'model/promotion/luckydraw/LuckyDrawParticipatesRecord'
import LuckyDrawParticipatesRecordFilter from 'model/promotion/luckydraw/LuckyDrawParticipatesRecordFilter'
import LuckyDrawPrizeRecord from 'model/promotion/luckydraw/LuckyDrawPrizeRecord'
import LuckyDrawPrizeRecordFilter from 'model/promotion/luckydraw/LuckyDrawPrizeRecordFilter'
import Response from 'model/default/Response'

export default class LuckyDrawActivityApi {
  /**
   * 批量导出大转盘奖品管理
   * 批量导出大转盘奖品管理。
   *
   */
  static exportAwardManagement(body: LuckyDrawPrizeRecordFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/lucky-draw-activity/exportAwardManagement`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量导出大转盘参与记录
   * 批量导出大转盘参与记录。
   *
   */
  static exportTakePartRecord(body: LuckyDrawLotteryCodeFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/lucky-draw-activity/exportTakePartRecord`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 抽锦鲤活动详情
   * 抽锦鲤活动详情。
   *
   */
  static getBigWheelActivity(id: string): Promise<Response<LuckyDrawActivity>> {
    return ApiClient.server().get(`/v1/lucky-draw-activity/getLuckyDrawActivity/${id}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 抽奖码查询
   * 抽奖码查询
   *
   */
  static queryLotteryCode(body: LuckyDrawLotteryCodeFilter): Promise<Response<LuckyDrawLotteryCode[]>> {
    return ApiClient.server().post(`/v1/lucky-draw-activity/queryLotteryCode`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 中奖记录查询
   * 中奖记录查询
   *
   */
  static queryPrizeRecord(body: LuckyDrawPrizeRecordFilter): Promise<Response<LuckyDrawPrizeRecord[]>> {
    return ApiClient.server().post(`/v1/lucky-draw-activity/queryPrizeRecord`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 大转盘参与记录列表查询
   * 大转盘参与记录列表查询
   *
   */
  static queryTakePartRecord(body: LuckyDrawParticipatesRecordFilter): Promise<Response<LuckyDrawParticipatesRecord[]>> {
    return ApiClient.server().post(`/v1/lucky-draw-activity/queryTakePartRecord`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建或修改抽锦鲤活动
   * 新建或修改抽锦鲤活动
   *
   */
  static saveBigWheelActivity(body: LuckyDrawActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/lucky-draw-activity/saveLuckyDrawActivity`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
