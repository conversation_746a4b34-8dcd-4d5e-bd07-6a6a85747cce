<template>
  <el-dialog
    class="dom-dialog"
    :class="[className]"
    :title="title"
    :visible.sync="isDialogShow"
    :width="width"
    :show-close="showClose"
    :append-to-body="appendToBody"
    @close="doCancel"
    :close-on-click-modal="closeOnClickModal"
    :destroy-on-close="destroyOnClose"
    :before-close="doBeforeClose"
  >
    <slot></slot>
    <div slot="footer" class="dialog-footer" v-if="isCancelShow || isConfirmShow || $slots.other">
      <span
        :class="{ 'btn-padding': otherFloat === 'right' }"
        :style="{ float: !!otherFloat ? otherFloat : 'left' }"
      >
        <slot name="other"></slot>
      </span>
      <el-button v-if="isCancelShow" @click="doCancel">{{ cancelText }}</el-button>
      <el-button
        v-if="isConfirmShow"
        :type="confirmType"
        @click="doConfirm"
        :disabled="isConfirmDisabled"
        :loading="buttonLoading"
      >
        {{ confirmText }}
      </el-button>
    </div>
  </el-dialog>
</template>
<script lang="ts" src="./DomDialog.ts"></script>

<style lang="scss">
.dom-dialog {
  .el-dialog {
    .el-dialog__header {
      font-weight: bold;
      border-bottom: 1px solid #d7dfeb;
    }

    .el-dialog__body {
      padding-top: 20px;
      padding-bottom: 20px;
      overflow: auto;
      max-height: 700px;
    }

    .el-dialog__footer {
      border-top: 1px solid #d7dfeb;
      position: static;
      width: 100%;
      bottom: 0;
    }

    .btn-padding {
      padding-left: 10px;
    }

    .dialog-footer {
      height: 28px;
    }
  }
}
</style>
