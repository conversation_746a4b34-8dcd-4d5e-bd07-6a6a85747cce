/*
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:05
 * @LastEditTime: 2024-01-30 14:44:26
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\member\alipay\Alipay.ts
 * 记得注释
 */
import {Component, Vue} from 'vue-property-decorator'
import AlipayInitApi from 'http/aliPay/v2/AlipayInitApi'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import AlipayInitCompleted from './alipayInitCompleted/AlipayInitCompleted'

@Component({
  name: 'Alipay',
  components: {
    BreadCrume,
    AlipayInitCompleted
  }
})
export default class Alipay extends Vue {
  loading = false
  hasAuthed: boolean = false // 是否已授权
  hasCard: boolean = false  //是否存在会员卡
  active = 0
  created() {
    this.auth()
  }

  /**
   * 查看页面是否已授权
   */
  auth() {
    this.loading = true
    AlipayInitApi.appToAuth().then((res: any) => {
      if (res && res.code === 2000) {
        this.hasAuthed = res.data.hasAuthed
        if (this.hasAuthed) {
          AlipayInitApi.getTemplate().then((res: any) => {
            if (res.code === 2000) {
              if (res.data) { // 授权后存在会员卡
                this.hasCard = true
              }
            }
          }).finally(() => {
            this.loading = false
          })
        } else {
          this.loading = false
        }
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  /**
   * 跳转创建会员卡页面
   */
  onToCard() {
    if (!this.hasAuthed) {
      this.$confirm('授权成功后才可以进行下一步，请先完成支付宝授权', '授权提示', {
        confirmButtonText: '确定',
        showCancelButton: false
      })
    } else {
      this.$router.push({ name: 'alipay-member-card-edit' })
    }
  }

}