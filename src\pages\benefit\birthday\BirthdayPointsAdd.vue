<template>
  <div class="birthday-points-add">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button v-if="hasOptionPermission('/营销/营销/节日有礼/生日积分加倍', '规则维护')" @click="doModify" type="primary">
          {{ formatI18n('/公用/按钮/保存并启用') }}
        </el-button>
        <el-button @click="doBack">
          {{ formatI18n('/公用/按钮', '取消') }}
        </el-button>
      </template>
    </BreadCrume>
    <div style="height: 95%;overflow: auto">
      <div class="content" style="padding: 50px">
        <FormItem :label="formatI18n('/权益/生日权益初始化/生日权益初始化','权益生效时间')">
          <div style="padding-top: 10px">
            <span style="color: red;position: relative;" :class="[isActive ? 'effActive' : 'effUnActive']">*</span>
            <el-radio-group v-model="pointsRule.effectType">
              <el-radio label="BY_DAY">{{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/权益生效时间', '生日当天') }}</el-radio>
              <el-radio label="BY_WEEK">{{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/权益生效时间', '生日当周') }}</el-radio>
              <el-radio label="BY_MONTH"> {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/权益生效时间', '生日当月') }}</el-radio>
            </el-radio-group>
          </div>
        </FormItem>
        <FormItem>
          - {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/新建/按自然周、自然月计算') }}
        </FormItem>
        <FormItem :label="getLabel" style="position: relative">
          <span style="color: red;position: absolute;" :class="[isActive ? 'active' : 'unActive']">*</span>
          <el-radio-group @change="doGiftTypeChange" v-model="giftType" style="padding-top: 12px">
            <el-radio label="same">{{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/会员生日权益/不同等级会员享相同权益') }}</el-radio>
            <el-radio label="different">{{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/会员生日权益/不同等级会员享不同权益') }}</el-radio>
          </el-radio-group>
          <div v-if="giftType=='same'" style="border:1px solid #ddd;padding:20px;width: 70%;">
            <el-form :model="pointsRule" ref="sameForm" :rules="sameRules">
              <el-form-item prop="samePointsTimes">
                {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/礼包设置/新建', '享') }}
                <AutoFixInput :min="1" :max="99999999" :fixed="2" style="width: 120px"
                              v-model="pointsRule.samePointsTimes"/>
                {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/礼包设置/新建', '倍积分') }}
              </el-form-item>
            </el-form>
          </div>
          <div v-else-if="giftType==='different'" style="border:1px solid #ddd;padding:20px;width: 70%;margin-left: 10px">
            <el-form :model="pointsItemsBody" ref="diffForm">
              <el-form-item
                      v-for="(dt, index) in pointsItemsBody.data"
                      :label="dt.name+formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/礼包设置/新建','享')"
                      :key="dt.key"
                      :prop="'data.' + index + '.pointsTimes'"
                      :rules="{
                  required: true, message: formatI18n('/权益/生日权益初始化/生日权益初始化','该字段不能为空'), trigger: 'blur'
                }">
                <AutoFixInput :min="1" :max="99999999" :fixed="2" style="width: 120px" v-model="dt.pointsTimes"/>
                {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/礼包设置/新建', '倍积分') }}
              </el-form-item>
            </el-form>
          </div>
        </FormItem>
        <FormItem>
          - {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/新建/基础积分基础上加倍') }}
        </FormItem>
        <FormItem>
          {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/新建/如：生日当月到店消费10元，按基础积分是10分，因为享受会员生日权益的2倍积分，最终可获得20积分') }}
        </FormItem>
        <FormItem :label="formatI18n('/权益/生日权益初始化/生日权益初始化/首页/生日积分加倍/每人每个生效时段得权益次数：')">
          <div style="padding-top: 3px">
          <span style="color: red;position: relative;"
                :class="[isActive ? 'limitTypeEffActive' : 'limitTypeEffUnActive']">*</span>
            <el-radio-group v-model="limitType" @change="limitTypeChange">
              <el-radio label="UN_LIMIT">{{ formatI18n('/权益/生日权益初始化/生日权益初始化/首页/生日积分加倍/不限制') }}</el-radio>
              <el-radio label="LIMIT">
                {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/修改/限') }}
                <AutoFixInput :disabled="limitType=='UN_LIMIT'"
                              :min="1" :max="$store.state.activityNumLimit"
                              :fixed="0" style="width: 120px"
                              v-model="pointsRule.memberMaxGainPointsTimes"/>
                {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/修改/次') }}
              </el-radio>
            </el-radio-group>
          </div>
        </FormItem>
      </div>
    </div>

  </div>
</template>

<script src="./BirthdayPointsAdd.ts">
</script>

<style lang="scss">
.birthday-points-add {
  background-color: white;
  overflow: hidden;
  height: 100%;
  width: 100%;

  .subTitle {
    font-size: 16px;
    padding-top: 20px;
    padding-left: 30px;
  }

  .content {
    padding: 70px;

    .qf-form-item .qf-form-label {
      width: 220px !important;
    }

    .qf-form-item .qf-form-content {
      margin-left: 200px !important;
    }
  }

  .el-switch__core {
    background-color: grey;
  }

  .el-switch.is-checked .el-switch__core {
    background-color: #33cc00
  }

  .active {
    top: 10px;
    left: 108px;
  }

  .unActive {
    top: 9px;
    left: 20px;
  }

  .limitTypeEffActive {
    top: -2px;
    left: -210px;
  }

  .limitTypeEffUnActive {
    top: -2px;
    left: -210px;
  }

  .effActive {
    left: -112px;
  }

  .effUnActive {
    left: -102px;
  }
}
</style>