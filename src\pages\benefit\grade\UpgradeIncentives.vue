<template>
  <div class="upgrade-incentives">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button size="small" v-if="!anyLoading && initial" @click="save" type="primary">
          {{ i18n('保存') }}
        </el-button>
        <el-button size="small" v-if="!anyLoading" @click="saveAndEnable" type="primary"
                   style="background-color: #52c41a !important;border-color: #52c41a !important;">
          {{ i18n('保存并启用') }}
        </el-button>
        <el-button size="small" v-if="!initial" @click="cancel">
          {{ i18n('取消') }}
        </el-button>
      </template>
    </BreadCrume>
    <div style="margin: 20px">
      <div class="tip">
        <el-row style="padding: 15px">
          <el-col :span="2" style="max-width: 40px;text-align: center;line-height: 24px">
            <i class="el-icon-warning"/>
          </el-col>
          <el-col :span="22">
            <p>• 系统将在满足升级激励规则的交易上传之后，实时更新升级为新等级，并从升级当日起计算等级到期日。</p>
            <p>• 升级的新等级有效期到期后，仍将按照累计成长值进行等级评定。</p>
          </el-col>
        </el-row>
      </div>
      <div class="content">
        <el-form label-width="180px" :model="data" :rules="rules" ref="form">
          <el-form-item label="升级激励规则" :required="true">
            <i18n advance k="/权益/等级/单笔消费升级激励/编辑页/当前等级为{0}的会员，单笔消费满{1}元及以上，可直接升级至{2}" class="float-left-i18n-line">
              <template slot-scope="{items}">
                <div class="text">{{ items.s0.prefix }}&nbsp;</div>
                <el-form-item prop="sourceGrade">
                  <el-select :placeholder="i18n('请选择等级')" v-model="data.sourceGrade" @change="validateGrade">
                    <el-option :value="grade.code" :label="grade.name" :key="grade.name" v-for="grade of gradeList">{{ grade.name }}
                    </el-option>
                  </el-select>
                </el-form-item>
                <div class="text">&nbsp;{{ items.s0.suffix }}&nbsp;</div>
                <el-form-item prop="amount">
                  <AutoFixInput style="width: 150px" :min="0.01" :max="99999999.00" :fixed="2" v-model="data.amount"></AutoFixInput>
                </el-form-item>
                <div class="text">&nbsp;{{ items.s1.suffix }}&nbsp;</div>
                <el-form-item prop="targetGrade">
                  <el-select :placeholder="i18n('请选择等级')" v-model="data.targetGrade" @change="validateGrade">
                    <el-option :value="grade.code" :label="grade.name" :key="grade.name" v-for="grade of gradeList">{{ grade.name }}
                    </el-option>
                  </el-select>
                </el-form-item>
                <div class="text">&nbsp;{{ items.s2.suffix }}</div>
              </template>
            </i18n>
          </el-form-item>
          <el-form-item label="升级后等级有效期" :required="true" prop="validDate">
            <AutoFixInput placeholder="请输入数字" style="width: 150px" :min="1" :max="3650" :fixed="0" type="number" v-model="data.validDate"></AutoFixInput>
            &nbsp;<span>天</span>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./UpgradeIncentives.ts">
</script>

<style lang="scss">
.upgrade-incentives {
  width: 100%;
  height: 100%;
  background: white;
  overflow: auto;

  .tip {
    background-color: rgba(51, 102, 255, 0.098);
  }

  .content {
    margin-top: 25px;

    .el-form-item__label {
      color: rgba(51, 51, 51, 0.64);
    }
  }

  .float-left-i18n-line {
    .text {
      height: 41px;
      float: left;
    }

    .el-form-item {
      float: left;
    }

  }

}
</style>