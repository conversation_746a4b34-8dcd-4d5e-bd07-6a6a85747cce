import MemberIdent from "model/common/member/MemberIdent";

export default class PointsUseReportLine extends MemberIdent {
  // 交易时间
  tranTime: Nullable<Date> = null
  // 发生组织
  occurredOrg: Nullable<string> = null
  // 会员识别码
  identId: Nullable<string> = null
  // 发生积分
  occur: Nullable<number> = null
  // 抵扣金额
  amount: Nullable<number> = null
  // 积分类型
  type: Nullable<string> = null
  // 说明
  remark: Nullable<string> = null
  // 交易号
  transNo: Nullable<string> = null
  // 活动名称
  activityName: Nullable<string> = null
  // 发生渠道名称
  channelName: Nullable<string> = null
}