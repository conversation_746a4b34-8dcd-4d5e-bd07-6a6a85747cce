<!--
 * @Author: haiding <EMAIL>
 * @Date: 2025-02-19 10:03:52
 * @LastEditors: 黎钰龙
 * @LastEditTime: 2025-05-22 14:57:52
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\cmp\page-props-components\draggable-img\DraggableImg.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <el-form :label-position="labelPosition" :model="form" :rules="rules" ref="form" class="main-products">
      <el-form-item :label="label" prop="propImages">
        <div class="tips">
          {{ config.prefixDescribe }}
        </div>
        <draggable v-model="form.propImages" animation="500" @change="handleChange">
          <!-- <transition-group> -->
          <el-card v-for="(item, index) in form.propImages" class="product-card" :key="item.id + index" shadow="never">
            <div class="nightfury-flex-left-center card-wrap">
              <el-form-item :prop="`propImages[${index}].imageUrl`" :rules="{ required: true, message: i18n('请上传图片'), trigger: ['change', 'blur'] }">
                <upload-img
                  v-model="item.imageUrl"
                  :signature-result="credential"
                  @validate="handleValidate"
                  width="80px"
                  height="80px"
                  :isDelete="false"
                  @change="handleChange"
                  :imgFormat="imgFormat"
                  :jumpPageInfo="item.jumpPageInfo"
                  :advertiseChannel="advertiseChannel"
                  @changeJumpPageInfo="changeJumpPageInfo($event, index)"
                  :index="index"
                  :showIndex="form.id === 'rotation' && form.id === 'gif' ? true : false"
                ></upload-img>
                <div class="move" v-if="config.multipleNumber > 1 && config.showDelete">
                  <div @click="upInsideData(index, form.propImages, item.jumpPageInfo)" v-if="index + 1 > 1">{{ i18n('上移') }}</div>
                  <div class="gray" v-else>{{ i18n('上移') }}</div>
                  <div @click="downInsideData(index, form.propImages)" v-if="index + 1 < form.propImages.length">{{ i18n('下移') }}</div>
                  <div class="gray" v-else>{{ i18n('下移') }}</div>
                  <div @click="deleteImg(index, form.propImages)" v-if="config.showDelete">{{ i18n('删除') }}</div>
                </div>
              </el-form-item>
              <!-- <el-form-item v-if="config.delete">
                  <el-button type="primary" @click="deleteImg(index, form.propImages)" circle class="el-icon-delete"></el-button>
                </el-form-item> -->
            </div>
          </el-card>
          <!-- </transition-group> -->
        </draggable>

        <el-button v-if="isAddImg" type="primary" style="width: 100%; margin-top: 10px" @click="addPic" plain>+{{ i18n('添加轮播图') }}</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" src="./DraggableImg.ts"></script>

<style lang="scss" scoped>
.main-products {
  .tips {
    // margin-top: 40px;
    // margin-left: -20px;
    font-weight: 400;
    font-size: 13px;
    color: #a1a6ae;
    line-height: 18px;
  }
  .product-card {
    position: relative;
    margin-top: 10px;
    .move {
      position: absolute;
      right: 0;
      bottom: 0;
      display: flex;
      color: #007eff;
      font-weight: 400;
      font-size: 13px;
      div {
        cursor: pointer;
        margin-right: 12px;
      }
      .gray {
        color: #a1a6ae;
      }
    }
  }
  .jump-page {
    margin: 0 10px;
  }
}
</style>
