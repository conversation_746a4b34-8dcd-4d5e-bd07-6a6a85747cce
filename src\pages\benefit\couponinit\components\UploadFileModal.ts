import { Component, Vue, Prop } from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import EnvUtil from 'util/EnvUtil'
import UploadApi from "http/upload/UploadApi";
@Component({
  name: "UploadFile",
  components: {
    FormItem,
  },
})
export default class UploadFileModal extends Vue {
  $refs: any;
  @Prop({
    type: Boolean,
    default: false,
  })
  dialogShow: boolean;
  @Prop({ type: Boolean, default: false }) readonly isMarketCenter!: boolean;
  imgFileKey: string = '';
  showImport: boolean = false
  fileCount = 0;
  uploadHeaders: any = {};
  bindData:any = {
    marketingCenter:''
  }
  // 活动模板
  get activityTemplate() {
    return "template_coupon_template.xlsx";
  }
 
  created() {
    let locale = sessionStorage.getItem("locale");
    this.uploadHeaders = {
      locale: locale ? locale : "zh_CN",
      time_zone: new Date().getTimezoneOffset(),
      marketingCenter: sessionStorage.getItem("marketCenter"),
    };
    const authorization = EnvUtil.getUniAuthorization()
    if (authorization) {
      this.uploadHeaders.authorization = authorization
    }
    this.bindData.marketingCenter = sessionStorage.getItem("marketCenter");
    if(!this.bindData.marketingCenter || this.bindData.marketingCenter == 'null') {
      this.bindData.marketingCenter = '-'; // 默认总部
    }
  }
  doModalClose(type: string) {
    if (type === "confirm") {
      console.log(this.fileCount);

      if (this.fileCount > 0) {
        this.$refs.upload.submit();
      } else {
        this.$message.warning(this.formatI18n("/公用/导入/请先选择文件") as any);
      }
      this.$emit("dialogClose");
    } else {
      this.$emit("dialogClose");
    }
    // this.uploadClear();
  }

  uploadClear(){
    this.$refs.upload.clearFiles();
  }

  doHandleChange(file: any, fileList: any) {
    if (fileList.length > 0) {
      this.fileCount++;
    }
  }
  doBeforeClose(done: any) {
    this.$emit("dialogClose");
    done();
    this.uploadClear();
  }
  getSuccessInfo(a: any, b: any, c: any) {
    if (a && a.code === 2000) {
      this.$refs.upload.clearFiles();
      this.fileCount = 0;
      this.$emit("dialogClose");
      this.$emit("upload-success");
      this.uploadClear();
    } else {
      this.$message.error(a.msg)
    }
  }

  getErrorInfo(a: any, b: any, c: any) {
    this.$message.error(this.formatI18n("/公用/导入", "导入失败，请重新导入") as any);
    this.fileCount = 0;
    this.$refs.upload.clearFiles();
  }

  get uploadUrl() {
    return EnvUtil.getServiceUrl() + `v1/coupon-template/importExcel`;
 }

  get getLimitInfo() {
    let str: any = this.formatI18n("/权益/券/券模板", "仅支持导入兑换券券模板，为保障上传成功，建议每次最多上传{0}条信息");
    str = str.replace(/\{0\}/g, "500");
    return str;
  }

  downloadTemplate() {
    UploadApi.getUrl(this.activityTemplate).then((resp: any) => {
      if (resp && resp.data) {
        window.open(resp.data);
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

}