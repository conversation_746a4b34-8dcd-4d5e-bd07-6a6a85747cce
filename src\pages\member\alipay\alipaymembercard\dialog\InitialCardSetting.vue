<template>
    <div>
        <el-dialog  :close-on-click-modal="false" :title="formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/开卡信息设置')" :visible.sync="dialogShow" :before-close="doBeforeClose" class="initial-dialog-center">
            <el-table :data="cardData">
                <el-table-column :label="formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/点击开卡信息设置按钮/表格/填写字段')">
                    <template slot-scope="scope">
                        <el-checkbox v-model="scope.row.cardChecked"
                                     :disabled="scope.$index === 0"
                                     @change="cardSelectedChanged(scope.$index)">
                            {{ scope.row.cardName }}
                        </el-checkbox>
                    </template>
                </el-table-column>
                <el-table-column :label="formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/点击开卡信息设置按钮/表格/必填属性')">
                    <template slot-scope="scope" >
                        <el-radio-group @change="doOptionChange(scope.$index)" :disabled="scope.$index === 0 || !scope.row.cardChecked" v-model="scope.row.cardSelected">
                            <el-radio :label="1">{{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/点击开卡信息设置按钮/表格/必填')}}</el-radio>
                            <el-radio :label="2">{{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/点击开卡信息设置按钮/表格/选填')}}</el-radio>
                        </el-radio-group>
                    </template>
                </el-table-column>
            </el-table>
            <div slot="footer" class="dialog-footer">
                <el-button @click="doCancel">{{formatI18n('/公用/按钮', '取消')}}</el-button>
                <el-button type="primary" @click="doConfirmClose">{{formatI18n('/公用/按钮', '确定')}}</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script lang="ts" src="./InitialCardSetting.ts">
</script>

<style lang="scss">
.initial-dialog-center{
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>