/*
 * @Author: 黎钰龙
 * @Date: 2024-07-11 15:52:10
 * @LastEditTime: 2024-07-16 16:21:34
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\deposit\activity\BMemberBalanceRechargeBill.ts
 * 记得注释
 */
import BMemberBalanceRechargeBillLine from "./BMemberBalanceRechargeBillLine"
import BPayInfo from "./BPayInfo"

// 储值充值单
export default class BMemberBalanceRechargeBill {
  // 储值充值单号
  billNumber: Nullable<string> = null
  // 充值类型: mobie, //填写手机号 file //文件导入
  rechargeType: Nullable<string> = null
  // 客户信息
  customInfo: Nullable<string> = null
  // 发生门店id
  occurredOrgId: Nullable<string> = null
  // 发生营销中心
  marketingCenter: Nullable<string> = null
  // 发生门店名称
  occurredOrgName: Nullable<string> = null
  // 付款总金额
  total: Nullable<string> = null
  // 备注
  remark: Nullable<string> = null
  // 文件导入任务id
  importTask: Nullable<string> = null
  // 支付明细
  payInfo: BPayInfo[] = []
  // 
  operator: Nullable<string> = null
  // 明细
  lines: BMemberBalanceRechargeBillLine[] = []
  // 创建人
  creator: Nullable<string> = null
  // 创建时间
  create: Nullable<Date> = null
  // 最后修改时间
  lastModified: Nullable<Date> = null
  // 审核人
  reviewer: Nullable<string> = null
  // 状态 INITIAL未审核、ADUIT已审核
  state: Nullable<string> = null
}