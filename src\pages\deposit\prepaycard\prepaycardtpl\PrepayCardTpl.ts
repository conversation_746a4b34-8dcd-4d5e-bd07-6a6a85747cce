import FormItem from 'cmp/formitem/FormItem.vue'
import ListWrapper from 'cmp/list/ListWrapper.vue'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import CardTemplateApi from 'http/card/template/CardTemplateApi'
import PrepayAccountApi from 'http/prepay/account/PrepayAccountApi'
import CardTemplate from 'model/card/template/CardTemplate'
import CardTemplateFilter from 'model/card/template/CardTemplateFilter'
import TemplateTypeCountResult from 'model/card/template/TemplateTypeCountResult'
import IdName from 'model/common/IdName'
import { Component, Vue } from 'vue-property-decorator'
import DataUtil from '../common/DataUtil'
import PrePayConfigApi from 'http/prepay/config/PrePayConfigApi'
import EditType from "common/EditType";
import DateUtil from "util/DateUtil";
import PrepayCardTplPermission from "pages/deposit/prepaycard/prepaycardtpl/PrepayCardTplPermission";
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import I18nPage from 'common/I18nDecorator'

@Component({
  name: 'PrepayCardTpl',
  components: {
    FormItem,
    ListWrapper,
    SubHeader,
    BreadCrume
  }
})
@I18nPage({
  prefix: [
    '/储值/预付卡/卡模板/公共/卡类型',
    '/卡/卡管理/卡介质'
  ],
  auto: true
})
export default class PrepayCardTpl extends Vue {
  dataUtil: DataUtil = new DataUtil()
  query: CardTemplateFilter = new CardTemplateFilter()
  queryData: CardTemplate[] = []
  countResult: TemplateTypeCountResult = new TemplateTypeCountResult()
  accounts: IdName[] = []
  cardTemplateType: string = 'ALL'
  enableMultipleAccount: boolean = false
  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  permission = new PrepayCardTplPermission()
  panelArray: any = []
  created() {
    this.panelArray = [
      {
        name: this.formatI18n('/储值/预付卡/卡模板/列表页面/卡模板'),
        url: ''
      }
    ]
    this.getPrePermission()
    this.listEnableAccountType()
    this.setDefault()
    this.getList()
  }

  setDefault() {
    if (this.permission.allViewable) {
      this.cardTemplateType = 'ALL'
    } else {
      if (this.permission.GIFT_CARD_viewable) {
        this.cardTemplateType = 'GIFT_CARD'
        return
      }
      if (this.permission.IMPREST_CARD_viewable) {
        this.cardTemplateType = 'IMPREST_CARD'
        return
      }
      if (this.permission.RECHARGEABLE_CARD_viewable) {
        this.cardTemplateType = 'RECHARGEABLE_CARD'
        return
      }
      this.cardTemplateType = 'NONE'
    }
  }

  doSearch() {
    this.page.currentPage = 1
    this.getList()
  }

  doReset() {
    this.query = new CardTemplateFilter()
    this.page.currentPage = 1
    this.getList()
  }

  /**
   * 查询
   */
  onSearch() {
    this.page.currentPage = 1
    this.getList()
  }

  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.getList()
  }

  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.getList()
  }

  /**
   * 表格排序条件
   */
  onSortChange({ column, prop, order }: any) {
    // todo
  }

  validatyInfo(row: CardTemplate) {
    if (!row.validityInfo) {
      return '-'
    }
    if (row.validityInfo.validityType === 'FIXED') {
      return DateUtil.format(row.validityInfo.endDate as Date, 'yyyy-MM-dd')
    }
    if (row.validityInfo.validityType === 'RALATIVE') {
      if (row.cardMedium === 'online' && row.cardTemplateType === 'GIFT_CARD') {
        return this.formatI18n('/储值/预付卡/卡模板/列表页面/激活后{0}内有效', null, [this.dataUtil.getValidNum(row.validityInfo)])
      } else {
        return this.formatI18n('/储值/预付卡/卡模板/编辑页面/发售后{0}内有效', null, [this.dataUtil.getValidNum(row.validityInfo)])
      }
    }
  }

  get allCount() {
    return `${this.formatI18n('/储值/预付卡/卡模板/公共/卡类型/全部')}(${this.countResult.sum})`
  }

  get giftCardCount() {
    return `${this.i18n('礼品卡')}(${Number(this.countResult.onlineGiftCard) + Number(this.countResult.offlineGiftCard)})`
  }

  get onlineGiftCardCount() {
    return `${this.formatI18n('/储值/预付卡/卡模板/公共/卡类型/电子礼品卡')}(${this.countResult.onlineGiftCard})`
  }

  get offlineGiftCardCount() {
    return `${this.formatI18n('/储值/预付卡/卡模板/公共/卡类型/实体礼品卡')}(${this.countResult.offlineGiftCard})`
  }

  get imprestCardCount() {
    return `${this.formatI18n('/储值/预付卡/卡模板/公共/卡类型/充值卡')}(${this.countResult.imprestCard})`
  }

  get rechargeableCardCount() {
    return `${this.formatI18n('/储值/预付卡/卡模板/公共/卡类型/储值卡')}(${this.countResult.rechargeableCard})`
  }

  get countCardCount() {
    return `${this.i18n('次卡')}(${this.countResult.countingCard})`
  }

  private listEnableAccountType() {
    PrepayAccountApi.listEnableAccountType().then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.accounts = resp.data
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private getList() {
    if (this.cardTemplateType === 'NONE') return  //所有卡模板都没权限
    if (this.cardTemplateType === 'ALL') {
      this.query.typeIn = null
    } else {
      if (this.cardTemplateType === 'GIFT_CARD') {
        this.query.typeIn = ['ONLINE_GIFT_CARD','OFFLINE_GIFT_CARD']
      } else {
        this.query.typeIn = [this.cardTemplateType]
      }
    }
    this.query.page = this.page.currentPage - 1
    this.query.pageSize = this.page.size
    CardTemplateApi.query(this.query).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.queryData = resp.data.result
        this.countResult = resp.data.countResult
        this.page.total = resp.data.total
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private getPrePermission() {
    PrePayConfigApi.get().then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.enableMultipleAccount = resp.data.enableMultipleAccount
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private changeTab(tab: any, event: any) {
    this.getList()
  }

  private add() {
    this.$router.push({ name: 'prepay-card-tpl-edit', query: { cardType: this.cardTemplateType} })
  }

  private edit(num: any, cardTemplateType: any) {
    this.$router.push({ name: 'prepay-card-tpl-edit', query: { number: num, editType: EditType.EDIT, cardType: cardTemplateType } })
  }

  private copy(num: any, cardTemplateType: any) {
    this.$router.push({ name: 'prepay-card-tpl-edit', query: { number: num, editType: EditType.COPY, cardType: cardTemplateType } })
  }

  private gotoDtl(row: any) {
    this.$router.push({ name: 'prepay-card-tpl-dtl', query: { number: row.number, cardType: row.cardTemplateType } })
  }
}
