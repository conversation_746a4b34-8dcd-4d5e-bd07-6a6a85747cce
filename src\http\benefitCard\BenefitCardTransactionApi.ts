import ApiClient from 'http/ApiClient'
import Response from 'model/default/Response'
import BenefitCardTransactionQueryRequest from "model/benefitCard/BenefitCardTransactionQueryRequest";
import BenefitCardTransaction from "model/benefitCard/BenefitCardTransaction";

export default class BenefitCardTransactionApi {
  /**
   * 批量导出
   * 批量导出。
   * 
   */
  static export(body: BenefitCardTransactionQueryRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/benefitCard/transaction/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询权益卡流水
   * 查询权益卡流水。
   * 
   */
  static query(body: BenefitCardTransactionQueryRequest): Promise<Response<BenefitCardTransaction[]>> {
    return ApiClient.server().post(`/v1/benefitCard/transaction/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
