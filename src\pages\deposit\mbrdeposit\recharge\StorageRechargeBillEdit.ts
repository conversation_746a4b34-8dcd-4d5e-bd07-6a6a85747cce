/*
 * @Author: 黎钰龙
 * @Date: 2024-07-11 18:11:09
 * @LastEditTime: 2024-07-26 15:08:21
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\mbrdeposit\recharge\StorageRechargeBillEdit.ts
 * 记得注释
 */
import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import SelectStores from 'cmp/selectStores/SelectStores';
import I18nPage from 'common/I18nDecorator';
import MemberBalanceRechargeBillApi from 'http/deposit/activity/MemberBalanceRechargeBillApi';
import SystemConfigApi from 'http/systemConfig/SystemConfigApi';
import IdName from 'model/common/IdName';
import BMemberBalanceRechargeBill from 'model/deposit/activity/BMemberBalanceRechargeBill';
import BMemberBalanceRechargeBillLine from 'model/deposit/activity/BMemberBalanceRechargeBillLine';
import BMemberBalanceRechargeBillLineFilter from 'model/deposit/activity/BMemberBalanceRechargeBillLineFilter';
import BPayInfo from 'model/deposit/activity/BPayInfo';
import CommonUtil from 'util/CommonUtil';
import { Component, Vue } from 'vue-property-decorator';
@Component({
  name: 'StorageRechargeBillEdit',
  components: {
    BreadCrume,
    SelectStores
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/储值/会员储值/储值充值单'
  ],
  auto: true
})
export default class StorageRechargeBillEdit extends Vue {
  $refs: any
  form: BMemberBalanceRechargeBill = new BMemberBalanceRechargeBill()
  payMethodList: IdName[] = []  //支付方式列表
  editType: 'create' | 'edit' = 'create'
  billNumber: Nullable<string> = null
  occurredOrg: Nullable<IdName> = null

  get panelArray() {
    return [
      {
        name: this.formatI18n('/公用/菜单/储值充值单'),
        url: 'storage-recharge-bill-list'
      },
      {
        name: this.i18n('新建储值充值单'),
        url: ''
      }
    ]
  }

  get rules() {
    return {
      rechargeType: [{
        required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: 'change'
      }],
      occurredOrgId: [{
        required: true, validator: (rule: any, value: string, callback: any) => {
          if (!this.occurredOrg?.id) {
            callback(new Error(this.formatI18n('/公用/查询条件/提示', '请选择发生组织')));
          }
          callback();
        }, trigger: 'change'
      }],
      payInfo: [{
        required: true,
        validator: (rule: any, value: string, callback: any) => {
          if (!value?.length) {
            callback(new Error(this.i18n("请添加付款方式")));
          }
          callback();
        },
        trigger: ["change", "blur"],
      }],
      phoneForm: [{
        required: true,
        validator: (rule: any, value: string, callback: any) => {
          if (this.form.rechargeType === 'mobie' && !this.form.lines?.length) {
            callback(new Error(this.i18n("请添加充值信息")));
          }
          callback();
        },
        trigger: ["change", "blur"],
      }]
    };
  }

  get payInfoRule() {
    return [{
      validator: (rule: any, value: string, callback: any) => {
        if (!value) {
          callback(new Error(this.formatI18n("/公用/券模板", "请输入必填项")));
        }
        callback();
      },
      trigger: ["change", "blur"],
    }]
  }

  get payAmountRule() {
    return [{
      validator: (rule: any, value: string, callback: any) => {
        if (!value) {
          callback(new Error(this.formatI18n("/公用/券模板", "请输入必填项")));
        }
        callback();
      },
      trigger: ["change", "blur"],
    }]
  }

  get phoneGiftRule() {
    return [{
      validator: (rule: any, value: string, callback: any) => {
        if (!value) {
          callback(new Error(this.formatI18n("/公用/券模板", "请输入必填项")));
        }
        callback();
      },
      trigger: ["change", "blur"],
    }]
  }

  get getTotalPrice() {
    return this.form.payInfo.reduce((prev, cur) => {
      return prev + (Number.isNaN(Number(cur.payAmount)) ? 0 : Number(cur.payAmount))
    }, 0).toFixed(2)
  }

  get isDisabled() {
    // const isImported = this.$route.query.importState === 'complete'  // 是否为已导入的单据
    return this.editType === 'edit'
  }

  created() {
    this.form.rechargeType = 'file'
    if (this.$route.query.billNumber) {
      this.editType = 'edit'
      this.billNumber = this.$route.query.billNumber as string
      this.getDtl()
    }
    this.getPayList()
  }

  getDtl() {
    const loading = CommonUtil.Loading()
    MemberBalanceRechargeBillApi.detail(this.billNumber!).then(async (res) => {
      if (res.code === 2000) {
        this.form = res.data || new BMemberBalanceRechargeBill()
        if (!this.form.payInfo) this.form.payInfo = []
        if (!this.form.lines) this.form.lines = []
        await this.queryLine()
        this.occurredOrg = {
          id: this.form.occurredOrgId,
          name: this.form.occurredOrgName
        }
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    }).finally(() => {
      loading.close()
    })
  }

  queryLine() {
    const params = new BMemberBalanceRechargeBillLineFilter()
    params.page = 0
    params.pageSize = 10
    params.numberEquals = this.billNumber
    params.memberIdIn = null as any
    return MemberBalanceRechargeBillApi.queryLines(params).then((res) => {
      if (res.code === 2000) {
        this.form.lines.push(...(res.data || []))
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    })
  }

  // 查询支付方式
  getPayList() {
    SystemConfigApi.getPayMethodConfig().then((res) => {
      if (res.code === 2000) {
        this.payMethodList = res.data?.payMethods || []
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    })
  }

  // 删除付款方式
  removePayInfo(index: number) {
    this.form.payInfo.splice(index, 1)
  }

  removePhoneGift(index: number) {
    this.form.lines?.splice(index, 1)
  }

  // 校验充值会员的本金金额和赠送金额
  validateLineAmount() {
    return new Promise<void>((resolve, reject) => {
      if (this.form.rechargeType === 'mobie' && this.form.lines.some(item => !item.amount && !item.giftAmount)) {
        this.$message.warning(this.i18n('本金金额和赠送金额不能同时为空'))
        reject()
      } else {
        resolve()
      }
    })
  }

  doSave() {
    Promise.all([this.$refs.ruleForm.validate(), this.validateLineAmount()])
      .then(() => {
        const params = new BMemberBalanceRechargeBill()
        params.billNumber = this.billNumber
        params.rechargeType = this.form.rechargeType
        params.customInfo = this.form.customInfo
        params.occurredOrgId = this.occurredOrg?.id
        params.occurredOrgName = this.occurredOrg?.name
        params.marketingCenter = sessionStorage.getItem("marketCenter")
        params.payInfo = JSON.parse(JSON.stringify(this.form.payInfo))
        params.total = this.getTotalPrice
        params.remark = this.form.remark
        if (this.form.rechargeType === 'mobie') {
          params.lines = this.form.lines.length ? this.form.lines : null as any
        }
        const saveFunc = this.editType === 'edit' ? MemberBalanceRechargeBillApi.saveModify : MemberBalanceRechargeBillApi.save
        saveFunc(params).then((res) => {
          if (res.code === 2000) {
            this.$message.success(this.i18n('保存成功'))
            this.$router.push({
              name: 'storage-recharge-bill-dtl',
              query: {
                billNumber: res.data
              }
            })
          } else {
            throw new Error(res.msg!)
          }
        }).catch((error) => {
          this.$message.error(error.message)
        })
      })
  }

  doCancel() {
    this.$router.back()
  }

  // 增加付款方式
  doAddPay() {
    this.form.payInfo.push(new BPayInfo())
    this.$refs.ruleForm.validateField('payInfo')
  }

  // 增加手机号赠送信息
  doAddPhoneGift() {
    this.form.lines?.push(new BMemberBalanceRechargeBillLine())
    this.$refs.ruleForm.validateField('phoneForm')
  } 
};