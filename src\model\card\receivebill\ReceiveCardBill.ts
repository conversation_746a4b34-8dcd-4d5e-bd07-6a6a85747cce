import IdName from "model/common/IdName";
import ReceiveCardBillLine from "model/card/receivebill/ReceiveCardBillLine";


export default class ReceiveCardBill {
  // 单号
  billNumber: Nullable<string> = null
  // 起始卡号
  startCardCode: Nullable<string> = null
  // 结束卡号
  endCardCode: Nullable<string> = null
  // 领出组织
  outOrg: Nullable<IdName> = null
  // 领入组织
  inOrg: Nullable<IdName> = null
  // 状态
  state: Nullable<string> = null
  // 备注
  remark: Nullable<string> = null
  // 创建时间
  created: Nullable<Date> = null
  // 创建人标识
  creator: Nullable<string> = null
  // 最后修改时间
  lastModified: Nullable<Date> = null
  // 最后修改人
  lastModifier: Nullable<string> = null
  // 领卡明细
  lines: ReceiveCardBillLine[] = []
}