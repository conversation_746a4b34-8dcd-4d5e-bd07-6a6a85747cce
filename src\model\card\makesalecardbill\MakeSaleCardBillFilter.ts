import { State } from 'model/default/State'

// 制售单查询条件
export default class MakeSaleCardBillFilter {
  // 制售单号等于
  billNumberEquals: Nullable<string> = null
  // 制售单号类似等于
  billNumberLikes: Nullable<string> = null
  // 制售单号起始于
  billNumberStartsWith: Nullable<string> = null
  // 制售单状态等于
  stateEquals: Nullable<State> = null
  // 卡面额等于
  cardFaceAmountEquals: Nullable<number> = null
  // 营销中心等于
  marketingCenterEquals: Nullable<string> = null
  // 卡模板号类似于或卡模板名称类似于
  cardTemplateNumberLikesOrCardTemplateNameLikes: Nullable<string> = null
  // 卡模板号等于或卡模板名称等于
  cardTemplateNumberEqualsOrCardTemplateNameEquals: Nullable<string> = null
  // 页码
  page: Nullable<number> = null
  // 页面大小，大于0
  pageSize: Nullable<number> = null
}