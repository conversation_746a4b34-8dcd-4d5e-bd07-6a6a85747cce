class DayLine {
  beginDate: Nullable<Date> = null
  endDate: Nullable<Date> = null
}

class WeekLine {
  weeks: number[] = []
  beginDate: Nullable<Date> = null
  endDate: Nullable<Date> = null
}

class MonthLine {
  days: number[] = []
  beginDate: Nullable<Date> = null
  endDate: Nullable<Date> = null
}

export default class DateTimeCondition {
  anytime: boolean = false;
  dayLines: DayLine[] = []
  weekLines: WeekLine[] = []
  monthLines: MonthLine[] = []
}

export {
  DayLine,
  WeekLine,
  MonthLine,
}