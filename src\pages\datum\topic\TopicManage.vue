<template>
  <div class="channel-view">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate"></template>
    </BreadCrume>
    <div class="current-page">
      <div style="margin: 20px; line-height: 40px" v-if="editable">
        <el-row>
          <el-col
            :span="2"
            style="min-width: 100px; text-align: right; padding-right: 10px"
            >{{ i18n("主题代码") }}</el-col
          >
          <el-col :span="4">
            <el-input
              style="width: 250px"
              v-model="newTopic.topic.topic.id"
              :placeholder="i18n('请输入主题代码')"
              :maxlength="20"
            ></el-input>
          </el-col>
        </el-row>
        <el-row>
          <el-col
            :span="2"
            style="min-width: 100px; text-align: right; padding-right: 10px"
            >{{ i18n("主题名称") }}</el-col
          >
          <el-col :span="18">
            <el-input
              v-model="newTopic.topic.topic.name"
              :placeholder="i18n('请输入主题名称')"
              style="width: 250px"
              :maxlength="20"
            ></el-input>
          </el-col>
        </el-row>
        <el-row v-if="marketCenter && marketCenterName">
          <el-col
            :span="2"
            style="min-width: 100px; text-align: right; padding-right: 10px"
            >{{ formatI18n("/资料/门店/所属营销中心") }}</el-col
          >
          <el-col :span="6">
            <span>{{ "[" + marketCenter + "]" }}{{ marketCenterName }}</span>
            <br />
            <span style="color: #666666; font-size: 13px">
              <i class="el-icon-warning" />
              {{ i18n("代码不允许与已有主题重复") }}
            </span>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="2" style="min-width: 100px">&nbsp;</el-col>
          <el-col :span="18">
            <el-button @click="add" type="primary">{{
              i18n("添加")
            }}</el-button>
            <el-button type="normal" @click="clear">{{
              i18n("清空")
            }}</el-button>
          </el-col>
        </el-row>
      </div>
      <ListWrapper style="overflow: initial" :showQuery="editable">
        <template slot="list">
          <FloatBlock refClass="current-page" :top="95" style="padding: 5px">
            <template slot="ctx">
              <el-row>
                <el-col :span="12" style="line-height: 36px" v-if="editable">
                  <el-checkbox
                    :disabled="loading"
                    v-model="checkedAll"
                    style="padding-left: 10px"
                    @change="checkedAllRow"
                  />
                  <i18n k="/资料/主题/已选择{0}个主题">
                    <template slot="0">
                      <span style="font-weight: 500">{{
                        selected.length
                      }}</span>
                    </template> </i18n
                  >&nbsp;&nbsp;
                  <el-button
                    type="danger"
                    @click="batchDelete()"
                    :loading="loading"
                    >{{ i18n("批量删除") }}</el-button
                  >
                </el-col>
                <el-col :span="12" style="line-height: 36px" v-if="!editable"
                  >&nbsp;</el-col
                >
                <el-col :span="12">
                  <div style="float: right">
                    <el-input
                      :placeholder="i18n('搜索主题代码/名称')"
                      @change="doSearch"
                      v-model="query.idOrNameLikes"
                      suffix-icon="el-icon-search"
                      style="width: 310px"
                    />
                  </div>
                </el-col>
              </el-row>
            </template>
          </FloatBlock>
          <el-table
            v-loading="loading"
            ref="table"
            :data="queryData"
            style="width: 100%; margin-top: 10px"
            @selection-change="handleSelectionChange"
          >
            <el-table-column
              v-if="editable"
              type="selection"
              width="55"
            ></el-table-column>
            <el-table-column
              :label="i18n('主题代码')"
            >
              <template slot-scope="scope">
                {{scope.row.topic.id}}
              </template>
            </el-table-column>
            <el-table-column :label="i18n('主题名称')" prop="topic.name">
              <template slot-scope="scope">
                {{scope.row.topic.name}}
              </template>
            </el-table-column>
            <el-table-column :label="i18n('操作')">
              <template slot-scope="scope">
                <el-button
                  @click="showUpdateDialog(scope.row)"
                  type="text"
                  v-if="editable"
                  >{{ i18n("修改") }}</el-button
                >
                <el-button
                  @click="deleteItem(scope.row)"
                  type="text"
                  v-if="editable"
                  >{{ i18n("删除") }}</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </template>
        <!--{{i18n('分页栏')}}-->
        <template slot="page">
          <el-pagination
            :current-page="page.currentPage"
            :page-size="page.size"
            :page-sizes="[10, 20, 30, 40]"
            :total="page.total"
            @current-change="onHandleCurrentChange"
            @size-change="onHandleSizeChange"
            background
            layout="total, prev, pager, next, sizes,  jumper"
          ></el-pagination>
        </template>
      </ListWrapper>
    </div>
    <el-dialog
      :title="i18n('修改')"
      :visible.sync="modifyDialog.visible"
      class="cosparty-dialog-center"
      width="750px"
    >
      <div style="margin: 20px;line-height: 30px">
        <el-row>
          <el-col :span="4" style="min-width: 100px">{{
            i18n("主题代码")
          }}</el-col>
          <el-col :span="17">{{
            currentTopic.topic.id
          }}</el-col>
        </el-row>
        
        <el-row style="margin-top: 20px">
          <el-col :span="4" style="min-width: 100px">{{
            i18n("主题名称")
          }}</el-col>
          <el-col :span="17">
            <el-input
              v-model="currentTopic.topic.name"
              :maxlength="20"
              style="width: 250px"
            ></el-input>
          </el-col>
        </el-row>

        <el-row style="margin-top: 20px">
          <el-col :span="4" style="min-width: 100px">{{
            i18n("/资料/门店/所属营销中心")
          }}</el-col>
          <el-col :span="17">{{
            marketCenterName
          }}</el-col>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="modifyDialog.visible = false">{{
          i18n("取消")
        }}</el-button>
        <el-button type="primary" @click="update">{{ i18n("确定") }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script lang="ts" src="./TopicManage.ts">
</script>

<style lang="scss">
.channel-view {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: hidden;
  .cosparty-dialog-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .total {
    margin: 20px;
  }

  .current-page {
    height: calc(100% - 77px);
    overflow-y: scroll;

    .el-select {
      width: 100%;
    }
  }
}
</style>
