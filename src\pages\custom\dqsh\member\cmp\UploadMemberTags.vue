<!--
 * @Author: 黎钰龙
 * @Date: 2023-08-09 14:53:04
 * @LastEditTime: 2023-08-14 17:35:07
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\custom\dqsh\member\cmp\UploadMemberTags.vue
 * 记得注释
-->
<template>
  <el-dialog :before-close="doBeforeClose" :close-on-click-modal="false" :visible.sync="dialogShow" class="upload-member-tag-dialog" title="导入会员标签">
    <div class="wrap">
      <FormItem :label="i18n('实例模板')">
        <a @click="downloadTemplate" class="action-hover_download download-link">{{i18n("会员标签模板")}}</a>
      </FormItem>
      <FormItem :label="i18n('选择文件')">
        <div style="line-height:36px">
          <div class="gray-tips">{{i18n('支持xls、xlsx、csv格式文件，为保障上传成功，建议每次最多上传20000条信息')}}</div>
          <el-upload :headers="uploadHeaders" :action="getUploadUrl" :auto-upload="false" :on-change="doHandleChange" :on-error="getErrorInfo"
            :on-success="getSuccessInfo" :with-credentials="true" :limit="1" multiple="false" class="upload-demo" ref="upload">
            <el-button slot="trigger" type="default">{{i18n('选取文件')}}</el-button>
          </el-upload>
        </div>
      </FormItem>
    </div>
    <div class="dialog-footer" slot="footer">
      <el-button @click="doModalClose('cancel')">{{i18n("取消")}}</el-button>
      <el-button @click="doModalClose('confirm')" type="primary">{{i18n("确认导入")}}</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./UploadMemberTags.ts">
</script>

<style lang="scss">
.upload-member-tag-dialog {
  display: flex;
  justify-content: center;
  align-items: center;
  .wrap {
    margin-top: 30px;

    .download-link {
      color: #318bff;
      font-size: 13px;
      text-decoration: none;
      line-height: 36px;
    }
  }
  .el-dialog {
    width: 650px;
    height: 300px;
    margin: 0 !important;
  }
  .el-dialog .el-dialog__body {
    height: 185px;
  }
}
.action-hover_download {
  cursor: pointer;
  /* 添加手型光标 */
}
</style>