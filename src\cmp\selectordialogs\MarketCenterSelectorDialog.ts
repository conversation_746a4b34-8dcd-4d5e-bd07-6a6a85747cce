import {Component, Prop} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import AbstractSelectDialog from './AbstractSelectDialog';
import RSOrg from 'model/common/RSOrg';
import RSMarketingCenterFilter from 'model/common/RSMarketingCenterFilter';
import MarketingCenterApi from 'http/marketingcenter/MarketingCenterApi';

@Component({
  name: 'MarketCenterSelectorDialog',
  components: {
    FormItem
  }
})
export default class MarketCenterSelectorDialog extends AbstractSelectDialog<RSOrg> {
  marketCenterFilter: RSMarketingCenterFilter = new RSMarketingCenterFilter()
  // title: String = this.formatI18n('/公用/公共组件/门店选择弹框组件/标题/选择区域')
  title: String = this.formatI18n('/公用/门店组件/选择营销中心')
  
  
  reset() {
    this.marketCenterFilter = new RSMarketingCenterFilter()
  }

  getId(ins: RSOrg): string {
    // @ts-ignore
    return ins.marketingCenter.id;
  }

  getName(ins: RSOrg): string {
    // @ts-ignore
    return ins.marketingCenter.name;
  }

  getResponseData(response: any): any {
    return response.data
  }

  queryFun(): Promise<any> {
    this.marketCenterFilter.page = this.page.currentPage - 1
    this.marketCenterFilter.pageSize = this.page.size
		return MarketingCenterApi.query(this.marketCenterFilter)
  }
}
