import ChildItem from 'model/member/ChildItem'

export default class MemberDetailResult {
  id: Nullable<string>
  channel: Nullable<string>
  source: Nullable<string>
  email: Nullable<string>
  name: Nullable<string>
  grade: Nullable<string>
  gradeValidate: Nullable<string>
  registerTime: Nullable<string>
  registerStoreId: Nullable<string>
  created: Nullable<string>
  modifed: Nullable<string>
  modifier: Nullable<string>
  state: Nullable<string>
  gender: Nullable<string>
  birthday: Nullable<string>
  activateTime: Nullable<string>
  area: Nullable<string>
  spareMobile: Nullable<string>
  age: Nullable<string>
  attribute: Nullable<string>
  weBankType: Nullable<string>
  idcard: Nullable<string>
  children: ChildItem[] = []
  mbrFeeExpireDate: Nullable<string>
  modifyReason: Nullable<string>
  hdcardMbrId: Nullable<string>
  weixinCardNo: Nullable<string>
  weixinOpenId: Nullable<string>
  hdcardCardNum: Nullable<string>
  alipayUserId: Nullable<string>
  gradeDate: Nullable<string>
  telephone: Nullable<string>
  sex: Nullable<string>
  memberCode: Nullable<string>
  store: Nullable<string>
  createDate: Nullable<string>
  lastModStr: Nullable<string>
  lastModifed: Nullable<string>
  registerDate: Nullable<string>
  activateDate: Nullable<string>
  tagDatas: []
  tagString: Nullable<string>
  lastConsumeTime: Nullable<string>
  education: Nullable<string>
  address: Nullable<string>
  industry: Nullable<string>
  hobbies: Nullable<string>
  annualIncome: Nullable<string>
  totalCount: number = 0
  totalPrice: number = 0
  totalAmount: number = 0
  postgresqlException: boolean
  payPassword: Nullable<string>
  specialRegisterChannel: Nullable<string>
  unionRegisterChannel: Nullable<string>
  unionRegisterMbrCode: Nullable<string>
  multipleBirth: Nullable<string>
  useSms: Nullable<string>
  mbrFeeValid: Nullable<string>
  inviterName: Nullable<string>
  inviterMobile: Nullable<string>
  inviterMbrCode: Nullable<string>
  freeMbr: boolean
}