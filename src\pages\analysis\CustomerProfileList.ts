/*
 * @Author: 黎钰龙
 * @Date: 2025-01-22 13:53:54
 * @LastEditTime: 2025-04-30 14:19:31
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\analysis\CustomerProfileList.ts
 * 记得注释
 */
import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import FormItem from 'cmp/formitem/FormItem';
import MyQueryCmp from 'cmp/querycondition/MyQueryCmp';
import SelectCustomerGroup from 'cmp/select-customer-group/SelectCustomerGroup';
import I18nPage from 'common/I18nDecorator';
import CustomerProfileApi from 'http/analysis/CustomerProfileApi';
import BCustomerProfile from 'model/analysis/BCustomerProfile';
import BCustomerProfileFilter from 'model/analysis/BCustomerProfileFilter';
import CommonUtil from 'util/CommonUtil';
import { Component, Vue } from 'vue-property-decorator';

class Form {
  name: string = ''
  targetCustomerId: string = ''
  compareCustomerId: string = ''
}

@Component({
  name: 'CustomerProfileList',
  components: {
    BreadCrume,
    SelectCustomerGroup,
    MyQueryCmp,
    FormItem
  }
})
@I18nPage({
  prefix: [
    '/公用/菜单',
    '/公用/券模板',
    '/数据/客群画像'
  ],
  auto: true
})
export default class CustomerProfileList extends Vue {
  $refs: any
  profileList: BCustomerProfile[] = []  // 画像列表
  hasData: boolean = true  // 是否有数据
  createVisible: boolean = false  // 新建画像弹窗
  createForm: Form = new Form()  // 新建画像表单
  editUuid: string = ''  // 编辑画像uuid
  addFlag: boolean = false  // 当前是否在新增画像
  nameLikes: Nullable<string> = null  //画像名称类似于
  page = {
    page: 1,
    pageSize: 10,
    total: 0
  }

  get panelArray() {
    return [
      {
        name: this.i18n('客群画像'),
        url: ''
      }
    ]
  }

  get createRules() {
    return {
      name: [
        { required: true, message: this.i18n('请输入必填项') as string, trigger: 'blur' }
      ],
      targetCustomerId: [
        { required: true, message: this.i18n('请选择目标客群') as string, trigger: ['change', 'blur'] }
      ],
      compareCustomerId: [
        {
          required: false,
          validator: (rule: any, val: any, callback: any) => {
            if (val && val === this.createForm.targetCustomerId) {
              callback(new Error(this.i18n('对比客群不能与目标客群相同') as string))
            }
            callback()
          },
          trigger: 'change'
        }
      ]
    }
  }

  created() {
    this.resetList()
  }

  resetList() {
    this.page.page = 1
    this.page.pageSize = 10
    this.doQueryList()
  }

  // 新建客群画像
  doCreate() {
    this.createVisible = true
    this.addFlag = true
  }

  doQueryList() {
    const loading = CommonUtil.Loading()
    const params = new BCustomerProfileFilter()
    params.page = this.page.page - 1
    params.pageSize = this.page.pageSize
    params.nameLikes = this.nameLikes
    CustomerProfileApi.list(params).then((res) => {
      if (res.code === 2000) {
        this.profileList = res.data || []
        this.page.total = res.total || 0
      } else {
        throw new Error(res.msg!)
      }
    }).catch((err) => {
      this.$message.error(err.message)
    }).finally(() => {
      this.hasData = this.profileList.length > 0
      loading.close()
    })
  }

  onSearch() {
    this.page.page = 1
    this.doQueryList()
  }

  doReset() {
    this.nameLikes = null
    this.page.page = 1
    this.doQueryList()
  }

  closeDialog() {
    this.createVisible = false
    this.addFlag = false
    this.createForm = new Form()
  }

  confirmCreate() {
    this.$refs.form.validate().then(() => {
      if (!this.addFlag) {
        return this.confirmChangeTitle(this.editUuid)
      }
      this.$router.push({
        name: 'customer-profile-edit',
        query: {
          name: this.createForm.name,
          targetCustomerId: this.createForm.targetCustomerId,
          compareCustomerId: this.createForm.compareCustomerId
        }
      })
    })
  }

  // 编辑客群
  doEdit(id: string) {
    const profile = this.profileList.find(item => item.uuid === id)
    this.$router.push({
      name: 'customer-profile-edit',
      query: {
        id: id,
        name: profile?.name,
        targetCustomerId: profile?.targetCustomer,
        compareCustomerId: profile?.compareCustomer,
        editType: 'edit'
      }
    })
  }

  // 前往画像详情页
  doToDtl(id: string) {
    const profile = this.profileList.find(item => item.uuid === id)
    this.$router.push({
      name: 'customer-profile-edit',
      query: {
        id: id,
        name: profile?.name,
        targetCustomerId: profile?.targetCustomer,
        compareCustomerId: profile?.compareCustomer,
        editType: 'view'
      }
    })
  }

  // 删除客群
  doDelete(id: string) {
    this.$confirm(this.i18n("删除报表后不可恢复，是否确认删除？") as string, this.i18n('/公用/提示/提示'), {
      confirmButtonText: this.formatI18n("/公用/按钮", "确定") as string,
      cancelButtonText: this.formatI18n("/公用/按钮", "取消") as string,
    }).then(() => {
      const loading = CommonUtil.Loading()
      CustomerProfileApi.remove(id).then((res) => {
        if (res.code === 2000) {
          this.$message.success(this.i18n('/公用/活动/提示信息/删除成功'))
          this.doQueryList()
        } else {
          throw new Error(res.msg!)
        }
      }).catch((err) => {
        this.$message.error(err.message)
      }).finally(() => {
        loading.close()
      })
    });
  }

  // 确认修改标题
  confirmChangeTitle(uuid: string) {
    const profile = this.profileList.find(item => item.uuid === uuid)
    if (profile) {
      profile.name = this.createForm.name
      CustomerProfileApi.modify(profile).then((res) => {
        if (res.code === 2000) {
          this.$message.success(this.i18n('/资料/券承担方/修改成功'))
          this.closeDialog()
          this.resetList()
        } else {
          throw new Error(res.msg!)
        }
      }).catch((err) => {
        this.$message.error(err.message)
      })
    }
  }

  doEditTitle(uuid: string) {
    this.editUuid = uuid
    this.addFlag = false
    this.createVisible = true
  }

  onHandleCurrentChange(val: number) {
    this.page.page = val;
    this.doQueryList();
  }

  onHandleSizeChange(val: number) {
    this.page.page = 1;
    this.page.pageSize = val;
    this.doQueryList();
  }
};