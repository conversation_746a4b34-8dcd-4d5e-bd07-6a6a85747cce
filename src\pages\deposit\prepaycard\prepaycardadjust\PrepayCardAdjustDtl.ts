import {Component, Vue} from 'vue-property-decorator'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import FormItem from 'cmp/formitem/FormItem.vue'
import ConstantMgr from 'mgr/ConstantMgr';
import PrePayConfigApi from 'http/prepay/config/PrePayConfigApi';
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import CardAdjustBillApi from 'http/card/adjustbill/CardAdjustBillApi'
import CardAdjustBill from 'model/card/adjustbill/CardAdjustBill'
import CardAdjustBillLine from 'model/card/adjustbill/CardAdjustBillLine'
import I18nPage from "common/I18nDecorator";
import SysConfigApi from "http/config/SysConfigApi";

@Component({
  name: 'PrepayCardAdjustReason',
  components: {
    SubHeader,
    FormItem,
    BreadCrume
  }
})
@I18nPage({
  prefix: [
    '/储值/预付卡/预付卡调整单/预付卡调整单详情', '/公用/按钮', '/公用/提示'
  ],
})
export default class PrepayCardAdjustDtl extends Vue {
  i18n: (str: string, params?: string[]) => string
  switchFlag = false
  panelArray: any
  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  dtlTableData = []
  oparatorTableData = []
  billDtl: CardAdjustBill = new CardAdjustBill()
  queryDtl: CardAdjustBillLine[] = []
  showOrg = false // 控制模态框的展示

  created() {
    this.panelArray  = [
      {
        name: this.i18n('预付卡调整单'),
        url: 'prepay-card-adjust'
      },
      {
        name: this.i18n('预付卡调整单详情'),
        url: ''
      }
    ]
    this.getStoreValueDtl()
    this.getQueryDetail()
    this.getPrePermission()
    this.getConfig()
  }
  /**
   * 分页页码改变的回调
   * @param val
   */
  doBack() {
    this.$router.push({name: 'prepay-card-adjust'})
  }
  doModify() {
    this.$router.push({name: 'prepay-card-adjust-add', query: { id: this.$route.query.id, from: 'edit'}})
  }
  doAudit() {
    console.log('nmsl')
    CardAdjustBillApi.audit(this.$route.query.id.toString()).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(this.i18n('审核成功'))
        this.getStoreValueDtl()
        this.getQueryDetail()
      } else {
        throw new Error(resp.msg || this.i18n('操作失败'))
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message({
              dangerouslyUseHTMLString: true,
              message: error.message.replace(/\n/g, "<br/>"),
              type: 'error'
          })
      }
    })
  }
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.getQueryDetail()
  }
  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.getQueryDetail()
  }
  private getStoreValueDtl() {
    CardAdjustBillApi.get(this.$route.query.id.toString()).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.billDtl = resp.data
        this.dtlTableData = resp.data.lines
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  private getQueryDetail() {

    CardAdjustBillApi.queryDetail(this.$route.query.id.toString(), this.page.currentPage - 1, this.page.size).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.queryDtl = resp.data
        this.page.total = resp.total
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  private getPrePermission() {
    const loading = this.$loading(ConstantMgr.loadingOption)
    PrePayConfigApi.get().then((resp: any) => {
      if (resp && resp.code === 2000) {
        loading.close()
        if (resp.data && resp.data.enableMultipleAccount) { // 开启多账户
          this.switchFlag = true
        } else {
          this.switchFlag = false // 未开启多账户
        }
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
      loading.close()
    })
  }

  private getConfig() {
    SysConfigApi.get().then((resp: any) => {
      if (resp && resp.data) {
        this.showOrg = resp.data.enableMultiMarketingCenter
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
}
