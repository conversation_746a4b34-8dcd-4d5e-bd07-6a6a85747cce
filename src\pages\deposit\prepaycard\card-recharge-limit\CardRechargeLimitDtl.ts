import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import FormItem from 'cmp/formitem/FormItem';
import I18nPage from 'common/I18nDecorator';
import PrepayCardRechargeLimitApi from 'http/card/rechargeLimit/PrepayCardRechargeLimitApi';
import ChannelManagement<PERSON>pi from 'http/channelmanagement/ChannelManagementApi';
import ChannelManagementFilter from 'model/channel/ChannelManagementFilter';
import { ChannelState } from 'model/channel/ChannelState';
import RSChannelManagement from 'model/common/RSChannelManagement';
import BPrepayCardRechargeLimit from 'model/prepay/rechargeLimit/BPrepayCardRechargeLimit';
import CommonUtil from 'util/CommonUtil';
import { Component, Vue } from 'vue-property-decorator';
@Component({
  name: 'CardRechargeLimitDtl',
  components: {
    BreadCrume,
    FormItem
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/卡/卡充值限额设置',
    '/公用/按钮'
  ],
  auto: true
})
export default class CardRechargeLimitDtl extends Vue {
  panelArray: any = []
  hasRule: Nullable<boolean> = null  //是否已经设置规则
  dtl: BPrepayCardRechargeLimit[] = [] //规则列表
  channels: RSChannelManagement[] = []

  created() {
    this.panelArray = [
      {
        name: this.i18n('/公用/菜单/卡充值限额设置'),
        url: ''
      }
    ]
    this.getChannels()
    this.getDtl()
  }

  getDtl() {
    const loading = CommonUtil.Loading()
    PrepayCardRechargeLimitApi.query().then((res) => {
      if (res.code === 2000) {
        this.dtl = res.data || []
        this.hasRule = res.data?.length ? true : false
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    }).finally(() => {
      loading.close()
    })
  }

  getChannelName(index: number) {
    let str = ''
    this.dtl[index].body?.rechargeChannels?.forEach((item) => {
      const idType = item.id! + item.type!
      str += this.channels.find((val) => {
        return (val.channel?.id! + val.channel?.type!) === idType
      })?.name + '；'
    })
    str = str.slice(0, -1)
    return str
  }

  getChargeLimit(index: number) {
    let str = ''
    const min = this.dtl[index].body?.minAmount
    const max = this.dtl[index].body?.maxAmount
    if (min) {
      str += this.i18n('最小充值金额') + String(min) + this.i18n('元') + ';'
    } else {
      str += this.i18n('最小充值金额不限制') + ';'
    }
    if (max) {
      str += this.i18n('最大充值金额') + String(max) + this.i18n('元')
    } else {
      str += this.i18n('最大充值金额不限制')
    }
    return str
  }

  doSet() {
    this.$router.push({
      name: 'card-recharge-limit-edit'
    })
  }

  //查询渠道列表
  getChannels() {
    let filter = new ChannelManagementFilter();
    filter.stateEquals = ChannelState.ENABLED
    ChannelManagementApi.query(filter).then((resp) => {
      if (resp && resp.code === 2000) {
        this.channels = resp.data || []
      } else {
        throw new Error(resp.msg!)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  doEdit() {
    this.$router.push({
      name: 'card-recharge-limit-edit',
      query: {
        editType: 'edit'
      }
    })
  }
};