<template>
  <div class="ImproveProfiles">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button type="primary" @click="doAdd" v-if='hasOptionPermission("/营销/营销/会员成长/完善资料有礼", "活动维护")'>
          {{ formatI18n('/营销/券礼包活动/券礼包活动', '新建活动') }}
        </el-button>
      </template>
    </BreadCrume>
    <div class="coupon-template-view">
      <ListWrapper class="current-page">
        <template slot="query">
          <QueryCondition @reset="doReset" @search="doSearch" :showExpand="false">
            <el-row>
              <el-col :span="8">
                <form-item :label="formatI18n('/营销/券礼包活动/券礼包活动', '活动名称')">
                  <el-input v-model="query.nameLike"></el-input>
                </form-item>
              </el-col>
              <el-col :span="8">
                <form-item :label="formatI18n('/营销/券礼包活动/券礼包活动', '活动号')">
                  <el-input v-model="query.numberLike"></el-input>
                </form-item>
              </el-col>
              <el-col :span="8">
                <form-item :label="formatI18n('/营销/券礼包活动/券礼包活动', '活动时间')">
                  <el-date-picker
                    :end-placeholder="formatI18n('/营销/券礼包活动/券礼包活动', '结束时间')"
                    format="yyyy-MM-dd"
                    range-separator="-"
                    ref="selectDate"
                    size="small"
                    :start-placeholder="formatI18n('/营销/券礼包活动/券礼包活动', '开始时间')"
                    type="daterange"
                    v-model="selectDate"
                    value-format="yyyy-MM-dd"
                  >
                  </el-date-picker>
                </form-item>
              </el-col>
            </el-row>
          </QueryCondition>
        </template>
        <template slot="list">
          <el-tabs @tab-click="doHandleClick" v-model="activeName">
            <el-tab-pane :label="getAllCount" name="first">
              <el-checkbox @change="checkedAllRow" style="margin-left: 14px;margin-right: 10px" v-model="singleAll"> </el-checkbox>{{ getSelectActive }}
              <el-button v-if="getBatchAuditPermmsion()" @click="doBatchAudit">{{ formatI18n("/营销/券礼包活动/券礼包活动", "批量审核") }}</el-button>
              <el-button v-if="getBatchStopPermission()" @click="doBatchEnd">{{ formatI18n("/营销/券礼包活动/券礼包活动", "批量终止") }}</el-button>
              <el-button v-if="getBatchDeletePermission()" @click="doBatchDelete" style="color: red">{{
                formatI18n("/营销/券礼包活动/券礼包活动", "批量删除")
              }}</el-button>
            </el-tab-pane>
            <el-tab-pane :label="getNoAudit" name="second">
              <el-checkbox @change="checkedAllRow" style="margin-left: 14px;margin-right: 10px" v-model="singleAll"> </el-checkbox>{{ getSelectActive }}
              <el-button v-if="getBatchAuditPermmsion()" @click="doBatchAudit">{{ formatI18n("/营销/券礼包活动/券礼包活动", "批量审核") }}</el-button>
              <el-button v-if="getBatchDeletePermission()" @click="doBatchDelete" style="margin-left: 20px;color: red">{{
                formatI18n("/营销/券礼包活动/券礼包活动", "批量删除")
              }}</el-button>
            </el-tab-pane>
            <el-tab-pane :label="getNoStart" name="third">
              <el-checkbox @change="checkedAllRow" style="margin-left: 14px;margin-right: 10px" v-model="singleAll"> </el-checkbox>{{ getSelectActive }}
              <el-button v-if="getBatchStopPermission()" @click="doBatchEnd">{{ formatI18n("/营销/券礼包活动/券礼包活动", "批量终止") }}</el-button>
            </el-tab-pane>
            <el-tab-pane :label="getDoing" name="forth">
              <el-checkbox @change="checkedAllRow" style="margin-left: 14px;margin-right: 10px" v-model="singleAll"> </el-checkbox
              >{{ getSelectActive }}&nbsp;
              <el-button v-if="getBatchStopPermission()" @click="doBatchEnd">{{ formatI18n("/营销/券礼包活动/券礼包活动", "批量终止") }}</el-button>
            </el-tab-pane>
            <el-tab-pane :label="getEnd" name="five">
              <div v-show="activeName !== 'five'">{{ getSelectActive }}</div>
            </el-tab-pane>
          </el-tabs>
          <el-table :data="tableData" ref="table" @selection-change="handleSelectionChange" style="width: 100%;margin-top: 10px">
            <el-table-column v-if="activeName !== 'five'" type="selection" width="55"> </el-table-column>
            <el-table-column :label="formatI18n('/营销/券礼包活动/券礼包活动', '活动号/活动名称')" fixed="left" prop="activityId" width="360">
              <template slot-scope="scope">
                <div>{{ scope.row.activityId | strFormat }}</div>
                <div
                  v-if="hasCheckPermission(scope.row.type)"
                  :title="scope.row.name"
                  @click="doToDtl(scope.row)"
                  style="color: #20a9ff;text-align: left;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 145px;cursor: pointer"
                >
                  {{ scope.row.name | strFormat }}
                </div>
              </template>
            </el-table-column>
            <el-table-column :label="formatI18n('/营销/券礼包活动/券礼包活动', '活动时间')" width="360">
              <template slot-scope="scope">
                <div
                  v-if="
                    (scope.row.type !== 'THIRD_CODE_ISSUE_COUPON' && scope.row.type !== 'EXPORT_COUPON_CODE') ||
                      ((scope.row.type === 'THIRD_CODE_ISSUE_COUPON' || scope.row.type === 'EXPORT_COUPON_CODE') && scope.row.state !== 'INITAIL')
                  "
                >
                  {{ scope.row.beginDate | dateFormate2 }}&nbsp;{{ formatI18n("/营销/券礼包活动/券礼包活动", "至") }}&nbsp;{{
                    scope.row.endDate | dateFormate2
                  }}
                </div>
                <div v-else>--</div>
              </template>
            </el-table-column>
            <el-table-column :label="formatI18n('/营销/券礼包活动/券礼包活动', '状态')" align="center" prop="state" width="300">
              <template slot-scope="scope">
                <el-tag type="warning" v-if="scope.row.state === 'INITAIL'">{{ scope.row.state | storeValueFmt }}</el-tag>
                <el-tag type="warning" v-if="scope.row.state === 'SUSPEND'">{{ scope.row.state | storeValueFmt }}</el-tag>
                <el-tag type="" v-if="scope.row.state === 'UNSTART'">{{ scope.row.state | storeValueFmt }}</el-tag>
                <el-tag type="success" v-if="scope.row.state === 'PROCESSING'">{{ scope.row.state | storeValueFmt }}</el-tag>
                <el-tag type="info" v-if="scope.row.state === 'STOPED'">{{ scope.row.state | storeValueFmt }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column :label="formatI18n('/营销/券礼包活动/券礼包活动', '操作')" fixed="right" min-width="300">
              <template slot-scope="scope">
                <el-button
                  @click="doAudit(scope.row)"
                  type="text"
                  v-if="
                      scope.row.state === 'INITAIL' &&
                      scope.row.type !== 'MANUAL_COUPON' &&
                      hasAuditPermission(scope.row.type)
                  "
                >
                  {{ formatI18n("/营销/券礼包活动/券礼包活动", "审核") }}
                </el-button>
                <el-button
                  @click="doModifyOrCopy(scope.row, 'copy')"
                  type="text"
                  v-if="
                    (scope.row.state === 'INITAIL' ||
                      scope.row.state === 'UNSTART' ||
                      scope.row.state === 'PROCESSING' ||
                      scope.row.state === 'SUSPEND' ||
                      scope.row.state === 'STOPED') &&
                      hasCopyPermission(scope.row.type)
                  "
                >
                  {{ formatI18n("/营销/券礼包活动/券礼包活动", "复制") }}
                </el-button>
                <el-button
                  @click="doModifyOrCopy(scope.row, 'modify')"
                  type="text"
                  v-if="
                    (scope.row.state === 'INITAIL' ||
                      (scope.row.state === 'UNSTART' && scope.row.type !== 'MEMBER_INVITE_REGISTER_GIFT') ||
                      (scope.row.state === 'SUSPEND') ||
                      (scope.row.state === 'PROCESSING')) &&
                      hasCopyPermission(scope.row.type)
                  "
                >
                  {{ formatI18n("/营销/券礼包活动/券礼包活动", "修改") }}
                </el-button>
                <el-button
                  @click="doDelete(scope.row)"
                  type="text"
                  v-if="scope.row.state === 'INITAIL' && hasCopyPermission(scope.row.type)"
                >
                  {{ formatI18n("/营销/券礼包活动/券礼包活动", "删除") }}</el-button
                >
                <el-button
                  @click="doStop(scope.row)"
                  type="text"
                  v-if="
                    (scope.row.state === 'UNSTART' ||
                      scope.row.state === 'SUSPEND' ||
                      scope.row.state === 'PROCESSING') &&
                      scope.row.state !== 'STOPED' &&
                      hasStopPermission(scope.row.type)
                  "
                >
                  {{ formatI18n("/营销/券礼包活动/券礼包活动", "终止") }}</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </template>
        <!--分页栏-->
        <template slot="page">
          <el-pagination
            :current-page="page.currentPage"
            :page-size="page.size"
            :page-sizes="[10, 20, 30, 40]"
            :total="page.total"
            @current-change="onHandleCurrentChange"
            @size-change="onHandleSizeChange"
            background
            layout="total, prev, pager, next, sizes,  jumper"
          >
          </el-pagination>
        </template>
      </ListWrapper>
    </div>
		<DownloadCenterDialog :dialogvisiable="dialogvisiable" :showTip="true" @dialogClose="doDownloadDialogClose"> </DownloadCenterDialog>

  </div>
</template>
<script lang="ts" src="./ImproveProfiles.ts">
</script>
<style lang="scss" scoped>
.ImproveProfiles {
  width: 100%;
  .coupon-template-view {
    overflow: auto;
    height: 95%;
    .el-date-editor.el-input,
    .el-date-editor.el-input__inner {
      width: 100% !important;
    }
  }
}
</style>