<template>
  <el-form
    v-if="formMode === FormModeType.form"
    :label-position="labelPosition"
    :model="value"
    :rules="rules"
    ref="form"
    label-width="100px"
    class="global-set"
  >
    <el-form-item :label="i18n('导航类型')">
      <el-radio
        v-for="item in options"
        :key="item.key"
        :label="item.key"
        v-model="value.propNavigationType"
        @change="handleChange"
      >
        {{ item.caption }}</el-radio
      >
    </el-form-item>
  </el-form>
</template>

<script lang="ts" src="./NavType.ts"></script>

<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 0px;
}
.el-form--label-top .el-form-item__label {
  padding: 0;
}
.global-set {
  padding: 0 30px 0 0px;
}
</style>
