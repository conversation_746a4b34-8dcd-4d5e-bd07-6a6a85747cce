<template>
  <div class="single-discount-coupon">
    <el-form :model="ruleForm" :rules="rules" class="demo-ruleForm" label-width="120px" ref="ruleForm">
      <el-form-item :label="formatI18n('/公用/券模板/商品折扣券', '折扣力度')" prop="discount">
        <div v-if="copyFlag === 'edit'">{{ getDiscountRate(ruleForm.discount) }}</div>
        <div v-else>
          <div style="color: #cccccc">- {{ formatI18n('/公用/券模板/商品折扣券/折扣力度后边的文案', '请填写0~9.9之间的数字，精确到小数点后1位') }}</div>
          <el-input @change="doDiscount" style="width: 100px"
                    v-model="ruleForm.discount"></el-input>&nbsp;&nbsp;{{ formatI18n('/公用/券模板/商品折扣券/折扣力度/折') }}
        </div>

      </el-form-item>
      <el-form-item :label="formatI18n('/公用/券模板', '券名称')" prop="name">
        <div v-if="copyFlag === 'edit'">
          {{ ruleForm.name }}
        </div>
        <div v-else>
          <div style="color: #cccccc">- {{ formatI18n('/公用/券模板', '留空则显示为：折扣力度+券类型') }}</div>
          <el-input maxlength="128" style="width: 300px" v-model="ruleForm.name" @change="doNameChange"></el-input>&nbsp;&nbsp;
        </div>
      </el-form-item>
      <CouponTemplateLogo :original-logo-url="ruleForm.logoUrl" @logoUrlCallBack="logoUrlCallBack"></CouponTemplateLogo>

      <el-form-item :label="formatI18n('/营销/券礼包活动/核销第三方券', '券码生成规则')">
        <div v-if="copyFlag === 'edit'">
          <span v-if="ruleForm.prefix">{{formatI18n('/营销/券礼包活动/核销第三方券/固定开头')}} {{ruleForm.prefix}}</span>
          <span v-else>{{formatI18n('/权益/券/券模板/券码前缀/系统随机生成')}}</span>
        </div>
        <div v-else>
          <div style="color: #cccccc">- {{ formatI18n('/营销/券礼包活动/核销第三方券', '为空表示系统随机生成，券模板保存后此字段不可修改。') }}</div>
          <el-form-item label-width="68px" :label="formatI18n('/营销/券礼包活动/核销第三方券', '固定开头')" prop="prefix">
            <el-input :placeholder="formatI18n('/营销/券礼包活动/券礼包活动/新建导出券码发券', '请输入6位以内的数字或字母')"
                      maxlength="6" style="width: 325px" v-model="ruleForm.prefix" @change="doPrefixChange"></el-input>
          </el-form-item>
        </div>
      </el-form-item>

      <div v-if="baseSettingFlag" style="height: 20px;width: 100%;background-color: #f2f2f2"></div>
      <div v-if="baseSettingFlag" class="baseSettingFlag">{{ formatI18n('/权益/券/券模板/编辑界面/用券规则') }}</div>
      <div v-if="$route.query.from === 'edit'"
           style="height: 48px;line-height: 48px;background-color: #3366ff19;margin: 0 20px;padding-left: 20px;margin-bottom: 10px">
        <img style="position: relative;top: 5px;" src="~assets/image/auth/info3.png"
             alt="">{{ formatI18n('/权益/券/券模板/编辑界面/用券规则下的提示/券模板修改后，已发出的券的有效期仍以原来的为准，其余用券规则以修改后的为准。') }}
      </div>
      <el-form-item :label="formatI18n('/公用/券模板', '券有效期')" class="cur-form-item">
        <el-select :placeholder="formatI18n('/公用/券模板', '请选择')" @change="doCouponValidateChange"
                   v-model="ruleForm.dateType">
          <el-option :label="formatI18n('/公用/券模板', '相对有效期')" value="RALATIVE"></el-option>
          <el-option :label="formatI18n('/公用/券模板', '固定有效期')" value="FIXED"></el-option>
        </el-select>
        <div style="display: inline-block" v-if="ruleForm.dateType === 'RALATIVE'">
          &nbsp;{{ formatI18n('/公用/券模板', '发券后') }}&nbsp;&nbsp;
          <el-form-item class="cur-day" prop="dateFrom" style="display: inline-block">
            <el-input @change="doCouponChange(0)" style="width: 100px" v-model="ruleForm.dateFrom"></el-input>
          </el-form-item>
          &nbsp;&nbsp;{{ formatI18n('/公用/券模板', '天生效，有效期') }}&nbsp;&nbsp;
          <el-form-item class="cur-day" prop="dateTo" style="display: inline-block">
            <el-input @change="doCouponChange(1)" style="width: 100px" v-model="ruleForm.dateTo"></el-input>
          </el-form-item>
          &nbsp;&nbsp;{{ formatI18n('/公用/券模板', '天') }}
        </div>
        <div style="display: inline-block;margin-left: 10px" v-if="ruleForm.dateType === 'FIXED'">
          <el-form-item class="fix_content" prop="dateFix">
            <el-date-picker
                @change="doDateFixChange"
                @focus="doDateFocus"
                :end-placeholder="formatI18n('/公用/券模板', '结束日期')"
                :picker-options="dateRangeOption"
                format="yyyy-MM-dd"
                range-separator="-"
                size="small"
                :start-placeholder="formatI18n('/公用/券模板', '开始日期')"
                type="daterange"
                v-model="ruleForm.dateFix"
                value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
        </div>
      </el-form-item>
      <el-form-item :label="formatI18n('/公用/券模板', '用券时段')" class="cur-form-item">
        <TimeRange
            @change="doTimeChange"
            v-model="ruleForm.time"
            ref="timeRange">
        </TimeRange>
      </el-form-item>
      <el-form-item :label="formatI18n('/公用/券模板', '用券渠道')" class="cur-from-item" required>
        <el-radio-group @change="doUseFromChange" v-model="ruleForm.useFrom">
          <el-radio label="step1" style="display: block">
            {{ formatI18n('/公用/券模板/用券渠道', '全部渠道') }}
          </el-radio>
          <el-radio label="step2" style="display: block">
            <span>{{ formatI18n('/公用/券模板/用券渠道', '指定渠道适用') }}</span>
            <div style="position: relative;top: -8px;left: 24px">
              <el-form-item class="cur-from-item" prop="useFrom">
                <el-select style="width: 250px" :disabled="ruleForm.useFrom === 'step1'" multiple @change="doFromChange"
                           v-model="ruleForm.from"
                           :placeholder="formatI18n('/公用/券模板/用券渠道', '请至少选择一个渠道')">
                  <el-option v-for="(item, index) in channels" :key="'channel' + index" :label="item.name"
                             :value="item.channel.typeId">{{ item.name }}
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item :label="formatI18n('/公用/券模板', '同步渠道')" class="cur-from-item">
        <div style="position: relative;top: -8px;left: 24px">
              <el-form-item class="cur-from-item" prop="useFrom">
                <el-select clearable style="width: 250px" @change="doSychChannelChange"
                           v-model="ruleForm.sychChannel"
                           :placeholder="formatI18n('/公用/券模板/同步渠道')" value-key="id">
                  <el-option v-for="(item, index) in channels" :key="`channel${index}`" :label="item.name"
                             :value="item.channel">{{ item.name }}
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
      </el-form-item>

      <el-form-item :label="formatI18n('/公用/券模板', '用券门店')" prop="storeRange">
        <ActiveStore
            ref="activeStore"
            :isOldActivity="false"
            :internal-validate="false"
            :sameStore="sameStore"
            v-model="ruleForm.storeRange"
            @change="doStoreChange">
        </ActiveStore>
      </el-form-item>
      <el-form-item :label="formatI18n('/公用/券模板', '用券商品')" class="cur-form-item" prop="useCouponGood">
        <!--<GoodsScopeEx ref="goodsScope" v-model="ruleForm.useCouponGood" @change="doGoodsRange"></GoodsScopeEx>-->
        <el-input :placeholder="formatI18n('/公用/券模板/单品折扣券/用券商品输入框提示信息', '请点击选择商品')" @focus="doFocus" class="width-auto"
                  style="width: 300px" @change="doUseCouponGoodsChange" v-model="ruleForm.useCouponGood"
                  ref="singleGoodsRef"></el-input>
                  <el-button type="text" @click="doClear" style="margin-left: 15px">{{formatI18n('/公用/导入/清空')}}</el-button>
                  <el-button type="text" @click="doImport" style="margin-left: 15px">{{formatI18n('/公用/券模板/导入')}}</el-button>
      </el-form-item>
      <el-form-item :label="formatI18n('/公用/券模板', '用券门槛')" class="coupon-step">
        <div v-if="copyFlag === 'edit'">
          <div v-html="getGoodsDiscount(ruleForm.otherStep, ruleForm.discount, ruleForm.otherStepValue)"></div>
        </div>

        <div v-else>{{ formatI18n('/公用/券模板/单品折扣券/用券门槛', '用券商品满') }}&nbsp;&nbsp;
          <el-form-item prop="otherStep" style="display: inline-block">
            <el-input @change="doSingleCountDiscount" style="width: 100px" v-model="ruleForm.otherStep"></el-input>
          </el-form-item>
          &nbsp;&nbsp;{{ formatI18n('/公用/券模板/单品折扣券/用券门槛', '件可享受其中') }}
          <el-form-item prop="otherStepValue" style="display: inline-block" class="long-error">
            <el-input @change="doSingleCountDiscountValue" style="width: 100px" v-model="ruleForm.otherStepValue"></el-input>
          </el-form-item>
          {{ formatI18n('/公用/券模板/单品折扣券/用券门槛', '件') }}
          <el-input disabled style="width: 100px" v-model="ruleForm.discount"></el-input>
          {{ formatI18n('/公用/券模板/单品折扣券/用券门槛/件可享受其中1件', '折') }}
          <el-tooltip effect="light" placement="bottom" :offset="-100">
            <i class="iconfont  ic-info" style="font-size: 18px;color: #20A0FF;position: relative;top: 2px;"></i>
            <template slot="content">
              <div style="width: 350px">{{ formatI18n('/公用/券模板/单品折扣券/用券门槛/最右边图标提示信息', '折扣设置原则') }}
                <div>1、{{ formatI18n('/公用/券模板/单品折扣券/用券门槛/最右边图标提示信息', '某商品5折券设置为“满1件”享1件“5折”') }}</div>

                <div>2、{{ formatI18n('/公用/券模板/单品折扣券/用券门槛/最右边图标提示信息', '某商品第二件半价券，设置为“满2件”享1件“5折”') }}</div>

                <div>3、{{ formatI18n('/公用/券模板/单品折扣券/用券门槛/最右边图标提示信息', '某商品买一送一券，设置为“满2件”享1件“0折”') }}</div>

                <div>4、{{ formatI18n('/公用/券模板/单品折扣券/用券门槛/最右边图标提示信息', '某商品买二送一券，设置为“满3件”享1件“0折”') }}</div>

              </div>
            </template>
          </el-tooltip>
        </div>
      </el-form-item>
      <el-form-item prop="promotion" :label="formatI18n('/公用/券模板', '叠加促销')">
        <div v-if="copyFlag === 'edit' && !baseFieldEditable">
          {{ ruleForm.promotion ? formatI18n('/公用/券模板', '是') : formatI18n('/公用/券模板', '否') }}
        </div>
        <el-radio-group v-else v-model="ruleForm.promotion" @change="doPromotionChange">
          <el-radio :label="true">{{ formatI18n('/公用/券模板', '是') }}</el-radio>
          <el-radio :label="false">{{ formatI18n('/公用/券模板', '否') }}</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 叠加用券-->
      <GroupMutexTemplate ref="singleDiscountMutexTemplate" v-model="ruleForm.groupMutex"
                          @change="doSingleDiscountMutexTemplate"></GroupMutexTemplate>

      <el-form-item :label="formatI18n('/公用/券模板', '用券记录方式')" prop="recordWay" required>
        <div v-if="copyFlag === 'edit' && !baseFieldEditable">
          <div v-if="ruleForm.recordWay === 'FAV'">{{ formatI18n('/公用/券模板', '优惠方式') }}</div>
          <div v-else-if="ruleForm.recordWay === 'PAY'">{{ formatI18n('/公用/券模板', '支付方式') }}</div>
          <div v-else>{{ formatI18n('/公用/券模板', '组合方式') }},<span
              v-html="getFavValue(ruleForm.discountWay, ruleForm.payWay)"></span></div>
        </div>
        <div v-else>
          <el-radio-group v-model="ruleForm.recordWay" @change="doRecordWayChange">
            <el-radio label="FAV">{{ formatI18n('/公用/券模板', '优惠方式') }}</el-radio>
            <el-radio label="PAY">{{ formatI18n('/公用/券模板', '支付方式') }}</el-radio>
            <el-radio label="COLLOCATION">{{ formatI18n('/公用/券模板', '组合方式') }}</el-radio>
          </el-radio-group>
          <div class="cur_record" v-if="ruleForm.recordWay === 'COLLOCATION'">
            <div style="color: #cccccc">- {{ formatI18n('/营销/券礼包活动/核销第三方券', '若未升级jpos版本,则设置的比例不生效') }}</div>
            <el-form-item prop="discountWay" style="display: inline-block">
              <el-input @change="doDiscountWay" style="width: 100px" v-model="ruleForm.discountWay"></el-input>
            </el-form-item>
            % {{ formatI18n('/公用/券模板', '优惠方式') }} +
            <el-form-item prop="payWay" style="display: inline-block">
              <el-input @change="doPayWay" style="width: 100px" v-model="ruleForm.payWay"></el-input>
            </el-form-item>
            % {{ formatI18n('/公用/券模板', '支付方式') }}
          </div>
        </div>
      </el-form-item>
      <el-form-item :label="formatI18n('/公用/券模板', '券承担方')" >
        <!-- 使用组件内部验证方法，不使用外部prop="couponUnder" -->
        <!-- <div v-if="copyFlag === 'edit' && !baseFieldEditable" style="line-height: 36px">
          <div style="height: 36px;line-height: 36px" v-if="!ruleForm.couponUnder || ruleForm.couponUnder.length === 0">
            --
          </div>
          <div v-else>
            <div v-for="item  in ruleForm.couponUnder">
              <div v-html="getCostPartr(item.party, item.percent)"></div>
            </div>
          </div>
        </div> -->
        <CouponBear ref="CouponBear" :state="curState" v-model="ruleForm.couponUnder" @change="doBearChange"></CouponBear>
        <div>
        </div>

      </el-form-item>
      <el-form-item :label="formatI18n('/公用/券模板', '用券顺序')" prop="couponOrder">
        <div v-if="copyFlag === 'edit' && !baseFieldEditable">{{ ruleForm.couponOrder }}</div>
        <div v-else>
          <div style="color: #cccccc"> - {{ formatI18n('/公用/券模板', '请输入1-99之间的整数') }}</div>
          <div>
            <el-input @change="doThisOrder" style="width: 100px" v-model="ruleForm.couponOrder"></el-input>
          </div>
        </div>

      </el-form-item>
      <!--<el-form-item :label="formatI18n('/营销/券礼包活动/核销第三方券', '用券商品说明')" prop="couponGoodsDesc">-->
      <!--<div v-if="copyFlag === 'edit'">{{ruleForm.couponGoodsDesc | strFormat}}</div>-->
      <!--<el-input v-else :placeholder="formatI18n('/营销/券礼包活动/核销第三方券', '向用户简要描述用券商品范围')" maxlength="400" style="width: 500px;" type="textarea" v-model="ruleForm.couponGoodsDesc" @change="doGoodsRemarkChange"></el-input>-->
      <!--</el-form-item>-->
      <el-form-item :label="formatI18n('/营销/券礼包活动/核销第三方券', '用券说明')" prop="couponProduct">
        <el-input :maxlength="remarkMaxlength" :placeholder="remarkPlaceholder" style="width: 500px;" type="textarea" v-model="ruleForm.couponProduct"
                  @change="doRemarkChange"></el-input>
      </el-form-item>

      <el-form-item :label="formatI18n('/公用/券模板', '是否支持转赠')">
        <el-radio-group v-model="ruleForm.transferable" @change="doTransferableChange">
          <el-radio :label="true">{{ formatI18n('/公用/券模板', '是') }}</el-radio>
          <el-radio :label="false">{{ formatI18n('/公用/券模板', '否') }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <GoodsSelectorDialog ref="goodsSelect" @summit="doGoodsSelectSummit"></GoodsSelectorDialog>
    <ImportDialog
        ref="importDialog"
        @upload-success="doUploadSuccess"
        :importNumber="importNumber"
        :importUrl="getImportUrl"
        :templatePath="templatePath"
        :templateName="formatI18n('/公用/券模板/全场现金券/用券商品/指定不可用商品/点击导入/导入商品范围模板')"
        :showClear="true"
        @clearChange="clearChange"
        :title="formatI18n('/公用/券模板/导入')">
    </ImportDialog>
  </div>
</template>

<script lang="ts" src="./SingleDiscountCoupon.ts">
</script>

<style lang="scss">
.single-discount-coupon {
  .coupon-step, .cur-form-item {
    .el-form-item__label {
      &:before {
        content: '*';
        color: #EF393F;
        margin-right: 4px;
      }
    }
  }

  .cur-from-item {
    height: auto !important;

    .el-radio {
      height: auto !important;
      line-height: 40px;
    }
  }

  .long-error .el-form-item__error{
    width: 300px;
  }
}
</style>