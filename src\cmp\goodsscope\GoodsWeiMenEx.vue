<template>
  <el-dialog :title="formatI18n('/公用/券模板', '选择商品')" class="select-goods-dialog" append-to-body width="1122px"
    :close-on-click-modal="false" :visible.sync="dialogShow" @close="doClosed">
    <div class="select-goods-box">
      <div class="goods-box-left">
        <div class="left-li" :class="{ 'active': limitedGoodsType == 'goods' }" @click="doChangeTab('goods')">{{
          formatI18n("/资料", "商品") }}
        </div>
        <div class="left-li" :class="{ 'active': limitedGoodsType == 'goodsCategory' }"
          @click="doChangeTab('goodsCategory')">{{ formatI18n("/公用/券模板/微盟适用商品", "类目") }}
        </div>
        <div class="left-li" :class="{ 'active': limitedGoodsType == 'goodsGroup' }" @click="doChangeTab('goodsGroup')">{{
          formatI18n("/公用/券模板", "分组") }}
        </div>
      </div>
      <div class="goods-box-right" v-if="dialogShow">
        <div class="goods-tab">
          <el-radio-group v-if="limitedGoodsType == 'goods' && !showExcludeGoods" v-model="limitGoodsTypeRule.goodsRange"
            @input="doChangeSpecify">
            <el-radio label="part">
              {{ formatI18n("/公用/券模板", "指定商品") }}
            </el-radio>
            <el-radio label="all">
              {{ formatI18n("/公用/公共组件/商品范围控件/表单/全部商品") }}
            </el-radio>
          </el-radio-group>
          <!-- <el-input v-if="limitedGoodsType == 'goodsCategory' && !showExcludeGoods" style="width: 175px;"
            placeholder="请输入关键词搜索泪目" suffix-icon="el-icon-search" v-model="input1">
          </el-input>
          <el-input v-if="limitedGoodsType == 'goodsGroup' && !showExcludeGoods" style="width: 175px;" placeholder="请输入关键词搜索"
            suffix-icon="el-icon-search" v-model="input1">
          </el-input> -->
          <div v-if="showExcludeGoods" @click="showExcludeGoods = false" class="select-excluded-goods"><i
              class="el-icon-arrow-left"></i> <span class="vertical-line"></span>{{ formatI18n("/公用/券模板/微盟适用商品", "选择排除商品")
              }}</div>
          <el-checkbox
            v-if="(limitedGoodsType == 'goods' && limitGoodsTypeRule.goodsRange == 'all') || limitedGoodsType !== 'goods'"
            v-model="existExcludeGoods" style="margin-left: 16px;">{{ formatI18n("/公用/券模板/微盟适用商品", "包含下级自建商品")
            }}</el-checkbox>
        </div>
        <div class="goods-content">
          <goods-sku v-if="(limitedGoodsType == 'goods' && limitGoodsTypeRule.goodsRange == 'part') || showExcludeGoods"
            @change="changeGoods" :groupList="groupList" :skuIds="skuIds" :height="450" />
          <goods-category v-if="limitedGoodsType == 'goodsCategory' && !showExcludeGoods" @change="changeCategory"
            :categoryList="categoryList" :limitGoodsCategoryTypeRule="limitGoodsCategoryTypeRule" :org="org"
            :height="450" />
          <goods-group v-if="limitedGoodsType == 'goodsGroup' && !showExcludeGoods" @change="changeGroup"
            :classifyList="groupList" :limitGoodsGroupRule="limitGoodsGroupRule" :org="org" :height="450" />
          <!-- {{ formatI18n("/公用/提示/暂无数据") }} -->
          <empty v-if="limitedGoodsType == 'goods' && limitGoodsTypeRule.goodsRange == 'all' && !showExcludeGoods">
            <div style="font-weight: 700; color: #000000; font-size: 14px; line-height: 22px; text-align: center;">{{
              formatI18n("/公用/券模板/微盟适用商品", "全部在售商品") }}
            </div>
            <div style="color: #8a9099; font-size: 14px; line-height: 22px; text-align: center;">{{
              formatI18n("/公用/券模板/微盟适用商品", "现有全部在售商品和后续上架的全部商品均可用") }}
            </div>
          </empty>
        </div>
      </div>
    </div>
    <div class="select-goods-footer">
      <div class="footer-info"
        v-if="limitedGoodsType == 'goods' && limitGoodsTypeRule.goodsRange == 'part' && limitGoodsTypeRule.includeGoodsIds.length > 0">
        {{ formatI18n("/营销/券礼包活动/核销第三方券", "已选") }} {{ limitGoodsTypeRule.includeGoodsIds.length }} {{
          formatI18n("/公用/券模板", "件商品") }} <span style="margin-left: 12px; color: #006aff;" class="cursor"
          @click="doClearRule('part')">{{ formatI18n("/公用/按钮", "清空") }}</span>
      </div>
      <div class="footer-info" v-if="limitedGoodsType == 'goods' && limitGoodsTypeRule.goodsRange == 'all'">
        {{ formatI18n("/营销/券礼包活动/核销第三方券", "已选") }} <span style="color: #1e2226; padding-left: 4px;">{{
          formatI18n("/公用/公共组件/商品范围控件/表单/全部商品") }}</span>
        <span v-if="limitGoodsTypeRule.excludeGoodsIds.length == 0" style="margin-left: 12px; color: #006aff;"
          class="cursor" @click="showExcludeGoods = true">{{ formatI18n("/公用/券模板/微盟适用商品", "排除商品") }} <i
            class="el-icon-arrow-right"></i></span>
        <span v-if="limitGoodsTypeRule.excludeGoodsIds.length > 0" class="line">{{ formatI18n("/公用/券模板/微盟适用商品", "已排除") }}
          <span style="color: #1e2226; padding: 0 4px;">{{ limitGoodsTypeRule.excludeGoodsIds.length }}</span> {{
            formatI18n("/公用/券模板", "件商品") }}</span>
        <span v-if="limitGoodsTypeRule.excludeGoodsIds.length > 0" style="padding-left: 12px; color: #006aff;"
          class="cursor" @click="doClearRule('all')">{{ formatI18n("/公用/按钮", "清空") }}</span>
        <span v-if="limitGoodsTypeRule.excludeGoodsIds.length > 0 && !showExcludeGoods"
          style="padding-left: 12px; color: #006aff;" class="cursor" @click="showExcludeGoods = true">{{
            formatI18n("/公用/按钮", "修改") }} <i class="el-icon-arrow-right"></i></span>
      </div>
      <div class="footer-info" v-if="limitedGoodsType == 'goodsCategory' && goodsCategoryName">
        {{ formatI18n("/公用/券模板/微盟适用商品", "已选类目") }} <span style="color: #1e2226; padding-left: 4px;">{{ goodsCategoryName
        }}</span>
        <span v-if="goodsCategoryName" style="padding-left: 12px; color: #006aff;" class="cursor"
          @click="doClearRule('goodsCategory')">{{ formatI18n("/公用/按钮", "清空") }}</span>
        <span v-if="limitGoodsCategoryTypeRule.excludeGoodsIds.length == 0" style="margin-left: 12px; color: #006aff;"
          class="cursor" @click="showExcludeGoods = true">{{ formatI18n("/公用/券模板/微盟适用商品", "排除商品") }} <i
            class="el-icon-arrow-right"></i></span>
        <span v-if="limitGoodsCategoryTypeRule.excludeGoodsIds.length > 0" class="line">{{ formatI18n("/公用/券模板/微盟适用商品",
          "已排除") }} <span style="color: #1e2226; padding: 0 4px;">{{ limitGoodsCategoryTypeRule.excludeGoodsIds.length
  }}</span>
          {{ formatI18n("/公用/券模板", "件商品") }}</span>
        <span v-if="limitGoodsCategoryTypeRule.excludeGoodsIds.length > 0 && !showExcludeGoods"
          style="padding-left: 12px; color: #006aff;" class="cursor" @click="showExcludeGoods = true">{{
            formatI18n("/公用/按钮", "修改") }} <i class="el-icon-arrow-right"></i></span>
      </div>
      <div class="footer-info" v-if="limitedGoodsType == 'goodsGroup' && limitGoodsGroupRule.ruleInfos.length > 0">
        {{ formatI18n("/公用/券模板/微盟适用商品", "已选分组") }}{{ groupListNum }}{{ formatI18n('/营销/升级有礼', '个') }}
        <span v-if="limitGoodsGroupRule.excludeGoodsIds.length > 0" class="line">{{ formatI18n("/公用/券模板/微盟适用商品", "已排除") }}
          <span style="color: #1e2226; padding: 0 4px;">{{ limitGoodsGroupRule.excludeGoodsIds.length }}</span> {{
            formatI18n("/公用/券模板", "件商品") }}</span>
        <span v-if="limitGoodsGroupRule.excludeGoodsIds.length > 0 || limitGoodsGroupRule.ruleInfos.length > 0"
          style="padding-left: 12px; color: #006aff;" class="cursor" @click="doClearRule('goodsGroup')">{{
            formatI18n("/公用/按钮", "清空") }}</span>
        <span v-if="limitGoodsGroupRule.excludeGoodsIds.length == 0" style="margin-left: 12px; color: #006aff;"
          class="cursor" @click="showExcludeGoods = true">{{ formatI18n("/公用/券模板/微盟适用商品", "排除商品") }} <i
            class="el-icon-arrow-right"></i></span>
        <span v-if="limitGoodsGroupRule.excludeGoodsIds.length > 0 && !showExcludeGoods"
          style="padding-left: 12px; color: #006aff;" class="cursor" @click="showExcludeGoods = true">{{
            formatI18n("/公用/按钮", "修改") }} <i class="el-icon-arrow-right"></i></span>
      </div>
      <span></span>
      <div class="footer-btn">
        <el-button @click="doCancel">{{ formatI18n("/公用/按钮", "取消") }}</el-button>
        <el-button type="primary" @click="doConfirm">{{ formatI18n("/公用/按钮", "确定") }}</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./GoodsWeiMenEx.ts" />

<style lang="scss" scoped>
.select-goods-dialog {
  display: flex;
  align-items: center;
  justify-content: center;

  .select-goods-box {
    width: 100%;
    height: 500px;
    border: 1px solid #edeef2;
    display: flex;
    justify-content: space-between;

    .goods-box-left {
      width: 112px;
      border-right: 1px solid #edeef2;
      padding: 4px;

      .left-li {
        color: #595961;
        padding-left: 12px;
        height: 32px;
        line-height: 32px;
        cursor: pointer;
        margin-bottom: 4px;

        &.active {
          background: #20A0FF;
          color: #ffffff;
          font-weight: 600;
        }
      }
    }

    .goods-box-right {
      flex: 1;

      .goods-tab {
        height: 48px;
        padding: 0 16px;
        background: #f5f7fa;
        display: flex;
        align-items: center;

        .select-excluded-goods {
          width: 150px;
          cursor: pointer;

          .vertical-line {
            display: inline-block;
            width: 1px;
            height: 14px;
            margin: 0 8px;
            background: #e9ecf0;
          }
        }
      }

      .goods-content {
        height: 450px;
      }
    }
  }

  .select-goods-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 24px;

    .footer-info {
      flex: 1;
      color: #8a9099;
      position: relative;

      .line {
        position: relative;
        padding-left: 8px;
        margin-left: 8px;

        &::before {
          content: "";
          position: absolute;
          width: 1px;
          height: 14px;
          background: #dfe2e6;
          top: 2px;
          left: 0;
        }
      }
    }
  }

  .cursor {
    cursor: pointer;
  }
}
</style>
