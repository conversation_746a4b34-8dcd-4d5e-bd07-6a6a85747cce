import { Component, Vue } from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import ListWrapper from 'cmp/list/ListWrapper.vue'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import FloatBlock from 'cmp/floatblock/FloatBlock.vue'
import BreadCrume from "cmp/bread-crumb/BreadCrume";
import I18nPage from "common/I18nDecorator";
import SaveChannelRequest from "model/channel/SaveChannelRequest";
import ChannelManagementApi from "http/channelmanagement/ChannelManagementApi";
import ChannelManagement from "model/channel/ChannelManagement";
import Channel from "model/common/Channel";
import ChannelManagementFilter from "model/channel/ChannelManagementFilter";
import JsonBeautifyUtil from 'util/JsonBeautifyUtil'
import ChannelBatchRequest from "model/channel/ChannelBatchRequest";
import ChannelStateCmp from "pages/datum/channel/state/ChannelStateCmp";
import { ChannelState } from "model/channel/ChannelState";
import BrowserMgr from 'mgr/BrowserMgr'
import TopicApi from 'http/topic/TopicApi'
import TopicFilter from 'model/datum/topic/TopicFilter'
import Topic from 'model/datum/topic/Topic'
import SaveTopicRequest from 'model/datum/topic/SaveTopicRequest'

@Component({
  name: 'TopicManage',
  components: {
    ChannelStateCmp,
    FormItem,
    ListWrapper,
    SubHeader,
    FloatBlock,
    BreadCrume,
  }
})
@I18nPage({
  auto: false,
  prefix: [
    '/资料/渠道',
    '/公用/按钮',
    '/资料/主题',

  ],
})
export default class TopicManage extends Vue {
  i18n: I18nFunc
  query: TopicFilter = new TopicFilter()
  queryData: Topic[] = []
  selected: Topic[] = []
  $refs: any
  hasOptionPermission: any
  panelArray: any = []
  checkedAll: boolean = false
  hideDisabled: boolean = false
  newIns: SaveChannelRequest = new SaveChannelRequest()
  updateIns: SaveChannelRequest = new SaveChannelRequest()
  loading = false
  isWeimob: boolean = false
  kgDia: boolean = false
  extendInfo = {
    defaultOrgId: null,
    defaultOuterOrgId: null,
    visible: false,
  }
  modifyDialog = {
    visible: false,
  }
  infoDialog: any = {
    row: {},
    visible: false
  }
  channelTypes: any
  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  marketCenter: any = []
  marketCenterName: any = []
  isMoreMarketing: boolean = false // 是否开启多营销中心 true 开启 false 不开启
  enableMultiMarketingCenter: boolean = false
  newTopic: SaveTopicRequest = new SaveTopicRequest()
  currentTopic: Topic = new Topic()

  get editable() {
    return this.hasOptionPermission('/营销/营销/活动主题', '主题维护')
  }

  created() {
    let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
    if (sysConfig) {
      this.isMoreMarketing = sysConfig.enableMultiMarketingCenter;
    }
    const id = sessionStorage.getItem('marketCenter') || null
    const name = sessionStorage.getItem('marketCenterName') || null
    // this.marketingCenters = {id: id, name: name}
    if (sessionStorage.getItem('isMultipleMC') == '1') {
      this.enableMultiMarketingCenter = true
    } else {
      this.enableMultiMarketingCenter = false
    }
    this.marketCenter = sessionStorage.getItem("marketCenter")
    this.marketCenterName = sessionStorage.getItem("marketCenterName")
    this.panelArray = [
      {
        name: this.i18n('主题'),
        url: ''
      },
    ]
    this.getList()
  }
  extendDialog(row: any) {
    this.extendInfo.visible = true
    this.extendInfo.defaultOrgId = row.defaultOrgId
    this.extendInfo.defaultOuterOrgId = row.defaultOuterOrgId
  }

  handleClose(close: any) {
    close()
  }
  isWeimobFn() {
    console.log('channelTypes',)
    if (this.newIns.channelManagement!.channel.type == 'weimob') {
      this.isWeimob = true
    } else {
      this.isWeimob = false
    }
  }

  doSearch() {
    this.page.currentPage = 1
    this.getList()
  }

  doReset() {
    this.query = new TopicFilter()
    this.page.currentPage = 1
    this.getList()
  }

  /**
   * 查询
   */
  onSearch() {
    this.page.currentPage = 1
    this.getList()
  }

  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.getList()
  }

  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.getList()
  }

  /**
   * 表格排序条件
   */
  onSortChange({ column, prop, order }: any) {
    // todo
  }

  handleSelectionChange(val: any) {
    this.selected = val
  }

  private getList() {
    let query = new TopicFilter();
    query.page = this.page.currentPage - 1
    query.pageSize = this.page.size
    query.marketingCenterEquals = this.marketCenter
    query.idOrNameLikes = this.query.idOrNameLikes
    this.loading = true
    TopicApi.query(query).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.queryData = resp.data
        this.page.total = resp.total
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    }).finally(() => {
      this.loading = false
    })
  }

  private add() {
    if (!this.newTopic.topic.topic.id) {
      this.$message.error(this.i18n('请输入主题代码'))
      return
    }
    if (!this.newTopic.topic.topic.name) {
      this.$message.error(this.i18n('请输入主题名称'))
      return
    }
    this.newTopic.topic.marketingCenter = this.marketCenter
    this.newTopic.operator = JSON.parse(sessionStorage.getItem('vuex') as string)?.loginInfo.user?.account
    TopicApi.save(this.newTopic).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(this.i18n('添加成功！'))
        this.getList()
        this.clear()
      } else {
        this.$message.error(resp.msg)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private deleteItem(row: Topic) {
    this.$confirm(this.i18n('如果有活动引用该主题，主题删除以后，活动将没有活动主题，确定删除吗'), this.i18n('删除'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消'),
    }).then(() => {
      TopicApi.remove(row.topic.id as string).then(res=>{
        this.$message({
          type: 'success',
          message: this.i18n('操作成功')
        });
        this.getList()
      })
    })
  }

  private showInfoDialog(row: ChannelManagement) {
    this.infoDialog.row = row
    this.infoDialog.visible = true
  }

  private showUpdateDialog(row: Topic) {
    this.currentTopic = JSON.parse(JSON.stringify(row))
    this.modifyDialog.visible = true
  }

  private update() {
    if (!this.currentTopic.topic.name) {
      this.$message.error(this.i18n('请填写主题名称'))
      return
    }
    let params = new SaveTopicRequest()
    params.operator = JSON.parse(sessionStorage.getItem('vuex') as string)?.loginInfo.user?.account
    params.topic = this.currentTopic
    params.topic.uuid = this.currentTopic.uuid
    TopicApi.modify(params).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(this.i18n('修改成功'))
        this.getList()
        this.modifyDialog.visible = false
      } else {
        this.$message.error(resp.msg)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private batchDelete() {
    if (!this.selected || this.selected.length === 0) {
      this.$message.warning(this.i18n('请至少选择一条数据'))
      return
    }
    let params = this.selected.map((item:Topic)=>{return item.topic.id})
    TopicApi.deleteBatch(params as string[]).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(this.i18n('批量删除成功'))
        this.checkedAll = false
        this.getList()
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private clear() {
    this.newTopic = new SaveTopicRequest()
  }

  private checkedAllRow() {
    if (this.checkedAll) {
      for (let row of this.queryData) {
        this.$refs.table.toggleRowSelection(row, true)
      }
    } else {
      this.$refs.table.clearSelection();
    }
  }

  private formatJson() {
    if (this.infoDialog.row && this.infoDialog.row.channel) {
      return JsonBeautifyUtil.beautifyJson(JSON.stringify({
        channel: {
          type: this.infoDialog.row.channel.type,
          id: this.infoDialog.row.channel.id,
        },
        namespace: this.infoDialog.row.channel.typeId
      }, null, '\t'))
    }
    return ''
  }

  private formatJsonExtend() {
    if (this.extendInfo.defaultOrgId) {
      return JsonBeautifyUtil.beautifyJson(JSON.stringify({
        defaultOrgId: this.extendInfo.defaultOrgId,
        defaultOuterOrgId: this.extendInfo.defaultOuterOrgId
      }, null, '\t'))
    }
    return ''
  }


  private copyAndClose() {
    navigator.clipboard.writeText(this.formatJson().replace(/<br\/>/g, '\n').replace(/&nbsp;/g, ' '))
    this.infoDialog.visible = false
  }

  private copyAndCloseExtend() {
    navigator.clipboard.writeText(this.formatJsonExtend().replace(/<br\/>/g, '\n').replace(/&nbsp;/g, ' '))
    this.extendInfo.visible = false
  }

  private changeDisable() {
    this.doSearch()
  }

  private editButtonIsDisabled(row: ChannelManagement) {
    return row!.channel!.type && ['weixin', 'weixinApp', 'alipay', 'store'].indexOf(row!.channel!.type) > -1 && row!.channel!.id === '-'
  }
}
