import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import UpGradeGiftActivity from 'model/grade/upgradegift/UpGradeGiftActivity'
import UpGradeGiftActivityFilter from 'model/grade/upgradegift/UpGradeGiftActivityFilter'
import UpGradeGiftActivityQueryResult from 'model/grade/upgradegift/UpGradeGiftActivityQueryResult'

export default class UpgradeGiftActivityApi {
  /**
   * 审核活动
   * 审核活动。
   * 
   */
  static audit(number: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/upgradeGift-activity/audit/${number}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量审核储值活动
   * 批量审核储值活动。
   * 
   */
  static batchAudit(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/upgradeGift-activity/batch/audit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量删除储值活动
   * 批量删除储值活动。
   * 
   */
  static batchRemove(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/upgradeGift-activity/batch/remove`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量终止储值活动
   * 批量终止储值活动。
   * 
   */
  static batchStop(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/upgradeGift-activity/batch/stop`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 检查冲突
   * 检查冲突。
   * 
   */
  static checkConflict(body: UpGradeGiftActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/upgradeGift-activity/checkConflict`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 创建活动
   * 创建活动。
   * 
   */
  static create(body: UpGradeGiftActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/upgradeGift-activity/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 活动详情
   * 查询活动。
   * 
   */
  static detail(number: string): Promise<Response<UpGradeGiftActivity>> {
    return ApiClient.server().get(`/v1/upgradeGift-activity/detail/${number}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改活动
   * 修改活动。
   * 
   */
  static modify(body: UpGradeGiftActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/upgradeGift-activity/modify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询活动
   * 查询活动。
   * 
   */
  static query(body: UpGradeGiftActivityFilter): Promise<Response<UpGradeGiftActivityQueryResult>> {
    return ApiClient.server().post(`/v1/upgradeGift-activity/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除活动
   * 删除活动。
   * 
   */
  static remove(number: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/upgradeGift-activity/remove/${number}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存并审核活动
   * 保存并审核活动。
   * 
   */
  static saveAndAudit(body: UpGradeGiftActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/upgradeGift-activity/saveAndAudit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 终止活动
   * 终止活动。
   * 
   */
  static stop(number: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/upgradeGift-activity/stop/${number}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

}
