/*
 * @Author: 黎钰龙
 * @Date: 2024-07-18 11:31:46
 * @LastEditTime: 2025-04-24 16:21:17
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\v2\coupon\collectpoints\CollectPointsActivity.ts
 * 记得注释
 */
import BaseCouponActivity from 'model/coupon/activity/BaseCouponActivity'
import CouponItem from 'model/common/CouponItem'
import CouponThreshold from 'model/common/CouponThreshold'
import GoodsRange from 'model/common/GoodsRange'
import PushGroup from 'model/precisionmarketing/pushplan/PushGroup'
import { ActivityThresholdType } from 'model/default/ActivityThresholdType'
import DateTimeCondition from "model/common/DateTimeCondition";

export default class CollectPointsActivity extends BaseCouponActivity {
  // 活动图片id
  imageId: Nullable<string> = null
  // 商品范围
  goodsRange: Nullable<GoodsRange> = null
  // 适用客群
  rule: Nullable<PushGroup> = null
  // 集点获取方式
  thresholdType: Nullable<ActivityThresholdType> = null
  // 集点获取条件
  threshold: Nullable<CouponThreshold> = null
  // 集点获取礼包条件
  obtainGiftCondition: Nullable<number> = null
  // 赠券
  couponItems: CouponItem[] = []
  // 活动时间条件
  dateTimeCondition: Nullable<DateTimeCondition> = null
  // 集点规则
  collectPointsRuleType: Nullable<CollectPointsRuleType> = null
  // 不同商品消费规则设
  differentGoodsConsumeRuleSettings: DifferentGoodsConsumeRuleSetting[] = []
  // 赠礼设置
  giftBagSettings: CollectPointsGiftBagSetting[] = []
}

export enum CollectPointsRuleType {
  // 适应商品统一规则
  GOOD_SAME_RULE = 'GOOD_SAME_RULE',
  // 适应商品不同规则
  GOOD_DIFFERENT_RULE = 'GOOD_DIFFERENT_RULE'
}

export class CollectPointsGiftBagSetting {
  // 礼包序号
  itemNo: Nullable<number> = null
  // 集点条件
  collectPointsCondition: Nullable<number> = null
  // 赠礼设置
  giftBag: Nullable<CollectPointsGiftBag> = new CollectPointsGiftBag()
}

export class CollectPointsGiftBag {
  // 实体礼包
  entityGift: Nullable<EntityGift> = null
  // 券礼包
  couponGift: CouponGift[] = []
  // 积分礼包
  pointGift: Nullable<PointGift> = null
}

export class PointGift {
  // 积分礼包id
  id: Nullable<string> = null
  // 积分礼包库存
  stock: Nullable<number> = null
  // 积分礼包积分
  points: Nullable<number> = null
}

export class EntityGift {
  // 实体礼包名称
  entityName: Nullable<string> = null
  // 实体礼包图片
  image: Nullable<string> = null
  // 实体礼包id
  id: Nullable<string> = null
  // 实体礼包库存
  stock: Nullable<number> = null
}

export class CouponGift {
  // 券礼包数量
  qty: Nullable<number> = null
  // 券模板号
  couponTemplateNumber: Nullable<string> = null
  // 券模板名称
  couponTemplateName: Nullable<string> = null
  // 券礼包id
  id: Nullable<string> = null
  // 券礼包库存
  stock: Nullable<number> = null
}

export class DifferentGoodsConsumeRuleSetting {
  // 组id
  groupId: Nullable<string> = null
  // 商品范围
  goodsRange: Nullable<GoodsRange> = null
  // 集点获取方式
  thresholdType: Nullable<ActivityThresholdType> = null
  // 集点获取条件
  threshold: Nullable<CouponThreshold> = null
  // 活动总限
  maxTime: Nullable<number> = null
  // 每人限量
  maxMemberTime: Nullable<number> = null
  // 每人每天限量
  limitMemberPerTime: Nullable<number> = null
}