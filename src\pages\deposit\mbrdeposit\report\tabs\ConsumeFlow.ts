import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import QueryCondition from 'cmp/querycondition/QueryCondition.vue'
import FormItem from 'cmp/formitem/FormItem.vue'
import ListWrapper from 'cmp/list/ListWrapper.vue'
import PrePayReportApi from 'http/prepay/report/prepay/PrePayReportApi'
import PrePayReportFilter from 'model/prepay/report/prepay/PrePayReportFilter'
import PrePayConsumeOrRefundData from 'model/prepay/report/prepay/trans/PrePayConsumeOrRefundData'
import OrgApi from 'http/org/OrgApi'
import RSOrgFilter from 'model/common/RSOrgFilter'
import RSOrg from 'model/common/RSOrg'
import PrePayReportSum from 'model/prepay/report/prepay/PrePayReportSum'
import DownloadCenterDialog from 'pages/main/dowmload_center_dialog/DownloadCenterDialog.vue'
import DateUtil from 'util/DateUtil'
import I18nPage from "common/I18nDecorator";
import ChannelManagementFilter from "model/channel/ChannelManagementFilter";
import ChannelManagementApi from "http/channelmanagement/ChannelManagementApi";
import ChannelManagement from "model/channel/ChannelManagement";
import Channel from "model/common/Channel";
import ZoneApi from 'http/area/ZoneApi';
import ZoneFilter from 'model/datum/zone/ZoneFilter'
import PermissionMgr from 'mgr/PermissionMgr'
import SelectStores from 'cmp/selectStores/SelectStores'

@Component({
  name: 'ConsumeFlow',
  components: {
    QueryCondition,
    FormItem,
    ListWrapper,
    DownloadCenterDialog,
    SelectStores
  }
})
@I18nPage({
  prefix: ['/储值/会员储值/会员储值报表/消费流水', '/公用/按钮', '/储值/会员储值/会员储值报表/调整流水', '/公用/提示'],
})
export default class ConsumeFlow extends Vue {
  i18n: I18nFunc
  recordAccount = ''
  dialogvisiable = false
  query: PrePayReportFilter = new PrePayReportFilter()
  queryData: PrePayConsumeOrRefundData[] = []
  sum: PrePayReportSum = new PrePayReportSum()
  stores: RSOrg[] = []
  customDate: any = []
  areaData: any = []
  ZoneFilter: ZoneFilter = new ZoneFilter()
  isMoreMarketing: boolean = false // 是否开启多营销中心 true 开启 false 不开启
  date = this.i18n('今天')
  loading: boolean = false
  daqiaoshihuaDingkai = PermissionMgr.daqiaoshihuaDingkai()
  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10,
    probeEnabled: null
  }
  channelTypes: any
  channelEquals: any = ''
  channelMap: Map<string, ChannelManagement> = new Map<string, ChannelManagement>();
  @Prop()
  account: string

  @Watch('date')
  onDateChange(value: string) {
    if (value) {
      this.setResetDate(value)
    }
    this.getTotal()
    this.geList()
  }

  created() {
    if (sessionStorage.getItem('isMultipleMC') == '1') {
      this.isMoreMarketing = true
    } else {
      this.isMoreMarketing = false
    }
    this.date = this.i18n(this.i18n('今天'))
    this.query.occurredTimeBegin = this.getDateRange()[0][0]
    this.query.occurredTimeEnd = this.getDateRange()[0][1]
    this.getChannelList();
    this.geList()
    this.getTotal()
    this.getAreaList()
  }

  /**
  * 查询区域
  */
  getAreaList() {
    // this.ZoneFilter.page = 0
    // this.ZoneFilter.pageSize = 10
    ZoneApi.query(this.ZoneFilter).then((res) => {
      if (res.code === 2000) {
        this.areaData = res.data
      } else {
        this.$message.error(res.msg as string)
      }
    })
  }

  onSearch() {
    this.page.currentPage = 1
    this.geList()
    this.getTotal()
  }

  onReset(account: string) {
    this.date = this.i18n('今天')
    this.recordAccount = account
    this.query = new PrePayReportFilter()
    this.page.currentPage = 1
    if (this.date === this.i18n('自定义')) {
      this.query.occurredTimeBegin = this.customDate[0]
      this.query.occurredTimeEnd = this.customDate[1]
    } else {
      this.setResetDate(this.date)
    }
    this.channelEquals = ''
    this.geList()
    this.getTotal()
  }

  doToggle() {
    // todo
  }

  doDialogClose() {
    this.dialogvisiable = false
  }

  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.geList()
    this.getTotal()
  }

  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.geList()
    this.getTotal()
  }

  /**
   * 表格排序条件
   */
  onSortChange({ column, prop, order }: any) {
    // todo
  }

  doExport() {
    this.$confirm(this.i18n('导出为预约下载，预约成功后，请在预约中心查看下载结果！'), this.i18n('导出提示'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消')
    }).then(() => {
      this.dialogvisiable = true
    })
  }

  doDateChange(value: any) {
    let today = this.getDateRange()[0]
    let yestoday = this.getDateRange()[1]
    let week = this.getDateRange()[2]
    let month = this.getDateRange()[3]
    let threeMonth = this.getDateRange()[4]
    if (value[0] === yestoday[0] && value[1] === yestoday[1]) {
      this.date = this.i18n('昨天')
    } else if (value[0] === today[0] && value[1] === today[1]) {
      this.date = this.i18n('今天')
    } else if (value[0] === week[0] && value[1] === week[1]) {
      this.date = this.i18n('近7天')
    } else if (value[0] === month[0] && value[1] === month[1]) {
      this.date = this.i18n('近30天')
    } else if (value[0] === threeMonth[0] && value[1] === threeMonth[1]) {
      this.date = this.i18n('近90天')
    } else {
      this.date = this.i18n('自定义')
    }
    this.query.occurredTimeBegin = value[0] as any
    this.query.occurredTimeEnd = value[1] as any
  }

  private getKey(channel: Channel) {
    if (channel && channel.type && channel.id) {
      return channel.type as any + channel.id
    }
    return channel.typeId
  }

  private getChannelList() {
    let query = new ChannelManagementFilter();
    query.page = 0
    query.pageSize = 0
    ChannelManagementApi.query(query).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.channelTypes = resp.data
        for (let channel of this.channelTypes) {
          if (channel.channel && channel.channel.type && channel.channel.id) {
            this.channelMap.set(this.getKey(channel.channel) as string, channel)
          }
        }
        console.log(this.channelMap);

      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private getDateRange() {
    // let date = new Date()
    // date.setMonth(date.getMonth() - 3)
    let arr: any = [
			[DateUtil.format(new Date(), "yyyy-MM-dd"), DateUtil.format(new Date(), "yyyy-MM-dd")], // 今天都传今天
			[DateUtil.format(DateUtil.prevDate(new Date(), 1), "yyyy-MM-dd"), DateUtil.format(DateUtil.prevDate(new Date(), 1), "yyyy-MM-dd")], // 昨天都是昨天
			[DateUtil.format(DateUtil.prevDate(new Date(), 6), "yyyy-MM-dd"), DateUtil.format(new Date(), "yyyy-MM-dd")], // 7天前到今天
			[DateUtil.format(DateUtil.prevDate(new Date(), 29), "yyyy-MM-dd"), DateUtil.format(new Date(), "yyyy-MM-dd")], // 近30天
			[DateUtil.format(DateUtil.prevDate(new Date(), 89), "yyyy-MM-dd"), DateUtil.format(new Date(), "yyyy-MM-dd")], // 今天减去89天
		];
    return arr
  }

  private setResetDate(value: string) {
    if (value === this.i18n('今天')) {
      this.query.occurredTimeBegin = this.getDateRange()[0][0]
      this.query.occurredTimeEnd = this.getDateRange()[0][1]
      this.customDate = [this.query.occurredTimeBegin, this.query.occurredTimeEnd]
    } else if (value === this.i18n('昨天')) {
      this.query.occurredTimeBegin = this.getDateRange()[1][0]
      this.query.occurredTimeEnd = this.getDateRange()[1][1]
      this.customDate = [this.query.occurredTimeBegin, this.query.occurredTimeEnd]
    } else if (value === this.i18n('近7天')) {
      this.query.occurredTimeBegin = this.getDateRange()[2][0]
      this.query.occurredTimeEnd = this.getDateRange()[2][1]
      this.customDate = [this.query.occurredTimeBegin, this.query.occurredTimeEnd]
    } else if (value === this.i18n('近30天')) {
      this.query.occurredTimeBegin = this.getDateRange()[3][0]
      this.query.occurredTimeEnd = this.getDateRange()[3][1]
      this.customDate = [this.query.occurredTimeBegin, this.query.occurredTimeEnd]
    } else if (value === this.i18n('近90天')) {
      this.query.occurredTimeBegin = this.getDateRange()[4][0]
      this.query.occurredTimeEnd = this.getDateRange()[4][1]
      this.customDate = [this.query.occurredTimeBegin, this.query.occurredTimeEnd]
    } else {  // 自定义
      this.customDate = [this.query.occurredTimeBegin, this.query.occurredTimeEnd]
    }
  }

  private geList() {
    this.query.page = this.page.currentPage - 1
    this.query.pageSize = this.page.size
    this.query.occurredTimeOrder = false
    this.query.accountIdEquals = this.recordAccount ? this.recordAccount : this.account
    if (this.channelEquals) {
      this.query.channelEquals = this.channelMap.get(this.channelEquals)!.channel
    } else {
      this.query.channelEquals = null
    }
    this.loading = true
    PrePayReportApi.queryConsume(this.query, false).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.queryData = resp.data
        this.page.total = resp.total
        this.page.probeEnabled = resp.fields ? resp.fields.probeEnabled : null
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    }).finally(() => {
      this.loading = false
    })
  }

  isShowSum:boolean = false //是否展示汇总信息
  private getTotal() {
    // let params: PrePayReportFilter = new PrePayReportFilter()
    // params.occurredTimeBegin = this.query.occurredTimeBegin
    // params.occurredTimeEnd = this.query.occurredTimeEnd
    this.query.accountIdEquals = this.recordAccount ? this.recordAccount : this.account
    PrePayReportApi.queryConsumeSum(this.query, false).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.sum = resp.data
        this.isShowSum = true
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
}
