export default class RSBrandFilter {
  //
  key: Nullable<string> = null
  //
  brandIdEquals: Nullable<string> = null
  //
  brandIdLikes: Nullable<string> = null
  //
  brandNameLikes: Nullable<string> = null
  //
  upperEquals: Nullable<string> = null
  //
  upperLikes: Nullable<string> = null
  //
  upperIn: Nullable<string[]> = null
  //
  brandIdIn: Nullable<string[]> = null
  //
  upperIsNull: Nullable<boolean> = null
  //
  brandIdOrder: Nullable<boolean> = null
  //
  brandNameOrder: Nullable<boolean> = null
  //
  page: Nullable<number> = null
  //
  pageSize: Nullable<number> = null
  //
  sorters = {
    brandId: 'asc'
  }
}