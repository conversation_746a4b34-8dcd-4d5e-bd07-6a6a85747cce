import TreasureChestSendGiftInfo from "./TreasureChestSendGiftInfo"

/*
 * @Author: 黎钰龙
 * @Date: 2023-08-24 14:29:33
 * @LastEditTime: 2023-08-24 14:36:55
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\promotion\ConsumeCalendarTakePartRecord.ts
 * 记得注释
 */
export default class ConsumeCalendarTakePartRecord {
  // 活动号
  activityNumber: Nullable<string> = null
  // 活动版本
  revision: Nullable<number> = null
  // 会员id
  memberId: Nullable<string> = null
  // 首次参与时间
  firstTime: Nullable<Date> = null
  // 最近参与时间
  recentlyTime: Nullable<Date> = null
  // 参与次数
  times: Nullable<number> = null
  // 奖品
  prize: Nullable<string> = null
  // 券码
  codes: string[] = []
  // 会员标识
  memberCode: Nullable<string> = null
  // 奖品明细
  treasureChestSendGiftInfo: Nullable<TreasureChestSendGiftInfo> = null
}