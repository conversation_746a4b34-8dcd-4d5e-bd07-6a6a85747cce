<template>
  <div class="prepay-card-charge-gift-dtl">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button key="1" @click="audit" type="primary" v-if="permission.discountAuditable && data.body.state === 'INITAIL'">审核</el-button>
        <el-button key="2" @click="copy" v-if="permission.discountEditable">复制</el-button>
        <el-button key="3" @click="stop" v-if="
            permission.discountTerminable &&
              ['UNSTART', 'PROCESSING'].indexOf(data.body.state) > -1
          ">终止</el-button>
        <el-button key="4" @click="edit" v-if="permission.discountEditable && data.body.state === 'INITAIL'">修改</el-button>
        <el-button key="5" @click="del" v-if="permission.discountEditable && data.body.state === 'INITAIL'" type="danger">删除</el-button>
      </template>
    </BreadCrume>
    <div style="overflow: auto;height: 95%">
      <div style="margin: 20px">
        <el-row>
          <el-col :span="8" style="padding-right: 20px">
            <p class="text-secondary">
              <span>活动号：</span>{{ data.body.activityId }}
            </p>
            <p class="text-primary" :title="data.body.name" no-i18n>
              {{ data.body.name }}
            </p>
          </el-col>
          <el-col :span="8">
            <p class="text-secondary">状态</p>
            <p class="text-primary">
              <ActivityState :state="data.body.state" />
            </p>
          </el-col>
          <el-col :span="8">
            <p class="text-secondary">所属主题</p>
            <p class="text-primary" no-i18n>
              {{ data.body.topicName | nullable }}
            </p>
          </el-col>
        </el-row>
        <hr />
        <el-row>
          <el-col :span="2" class="text-secondary">
            {{
              formatI18n(
                "/营销/积分活动/积分活动/商品满额加送积分/详情页面/活动信息/活动时间："
              )
            }}
          </el-col>
          <el-col :span="18" style="display: flex;">
            <div>
              {{ data.body.beginDate | dateFormate2 }}
              {{
                formatI18n(
                  "/营销/积分活动/积分活动/商品满额加送积分/新建页面/活动信息/至"
                )
              }}
              {{ data.body.endDate | dateFormate2 }}
            </div>
            <DateTimeConditionDtl style="margin-left: 30px" :value="data.dateTimeCondition"></DateTimeConditionDtl>
          </el-col>
        </el-row>
        <!-- <el-row style="display: flex;margin-top: 25px">
          <div class="text-secondary" style="min-width: 70px">{{ i18n('/营销/积分活动/积分活动/商品满额加送积分/详情页面/活动信息/活动门店：') }}</div>
          <ActiveStoreDtl style="width: 60%;min-width: 800px;margin-top: -8px;" no-i18n :data="data.body.stores"></ActiveStoreDtl>
        </el-row> -->
        <el-row style="margin-top: 25px">
          <el-col :span="2" class="text-secondary" style="line-height: 36px;">
            {{
              formatI18n(
                "/营销/积分活动/积分活动/商品满额加送积分/详情页面/活动信息/活动门店："
              )
            }}
          </el-col>
          <el-col :span="18">
            <ActiveStoreDtl style="width: 60%;min-width: 800px;" :data="data.body.stores"></ActiveStoreDtl>
          </el-col>
        </el-row>
      </div>
      <div style="background-color: #eeeff1;height: 15px;"></div>
      <div style="margin: 20px">
        <div style="font-weight: 600">{{formatI18n('/储值/预付卡/预付卡充值有礼/充值规则')}}</div>
        <div style="margin-top: 20px;">
          <el-row>
            <el-col :span="2" class="text-secondary">适用卡模板：</el-col>
            <el-col :span="12">
              <el-row v-for="(item, index) of data.cardTemplates" style="margin-bottom: 10px" :key="index">
                <el-button no-i18n type="text" @click="gotoTplDtl(item.id)" style="padding: 0">{{ item.name }}</el-button>
              </el-row>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="text-secondary">{{ i18n("赠礼规则") }}：</el-col>
            <el-col :span="12">
              <el-row v-for="(item, index) of data.lines" style="margin-bottom: 10px" :key="index">
                <el-col :span="24">
                  {{ i18n("充值金额满") }} {{ item.faceAmount }}
                  {{ i18n("元") }}，
                  <span v-if="item.rebateAmount || item.rebatePercentage">{{ i18n("返现") }}
                    <span v-if="item.rebateType === 'amount'">
                      {{ item.rebateAmount }}
                      {{ i18n("元") }}；
                    </span>
                    <span v-if="item.rebateType === 'percentage'">
                      {{ item.rebatePercentage }}%；
                    </span>
                  </span>
                  <span v-if="item.discount">
                    {{ i18n("打") }} {{ item.discount }}{{ i18n("折") }}；
                  </span>
                </el-col>

                <el-col :span="24">

                    <template v-if="item.gift && item.gift.couponItems && item.gift.couponItems.length > 0">
                      <div v-for="(item1,index) in item.gift.couponItems" :key="index">
                        {{getQty(item1.qty)}}&nbsp;&nbsp;
                        <a style="overflow: hidden;
                                    white-space: nowrap;
                                    text-overflow: ellipsis;color: rgb(32, 160, 255);
                                    width: 90%;
                                    display: inline-block;
                                    position: relative;
                                    top: 4px;cursor: pointer;
                                    " @click="doCheckCoupon(item1)" :title="item1.coupons.name" type="text">{{item1.coupons.name}}</a>
                      </div>
                    </template>
                    <template v-else>-</template>

                </el-col>

              </el-row>
              <el-row v-if="data.amount && data.points">
                <el-col :span="24">
                  {{ i18n('每充值') }} {{data.amount}} {{ i18n('元，送')}} {{data.points}} {{ i18n('积分') }}
                </el-col>
              </el-row>
            </el-col>
          </el-row>
          <el-row style="margin-top: 25px">
            <el-col :span="2" class="text-secondary">{{i18n("充值说明")}}：</el-col>
            <el-col :span="12">
              <template v-if="data.description">
                <div v-html="xss(data.description.replace(/\n/g, '<br/>'))"></div>
              </template>
              <template v-else>--</template>
            </el-col>
          </el-row>
          <el-row style="margin-top: 25px">
            <el-col :span="2" class="text-secondary">{{i18n("充值协议")}}：</el-col>
            <el-col :span="12">
              <div v-html="data.agreement" style="padding:10px;border:1px solid #eee"></div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./PrepayCardChargeGiftDtl.ts"></script>

<style lang="scss">
.prepay-card-charge-gift-dtl {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: hidden;

  .text-primary {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    font-weight: 500;
    font-style: normal;
    font-size: 20px;
    color: #515151;
  }

  .text-secondary {
    /*text-overflow: ellipsis;*/
    /*white-space: nowrap;*/
    /*overflow: hidden;*/
    color: rgba(51, 51, 51, 0.65);
  }

  .rule-table {
    width: 70%;

    .rule-table-header {
      padding: 10px;
      background-color: #e6e6e6;
      border: 1px solid #e6e6e6;
    }

    .rule-table-line {
      padding: 10px;
      border: 1px solid #e6e6e6;
    }
  }
}
</style>
