/*
 * @Author: 黎钰龙
 * @Date: 2025-04-02 18:43:10
 * @LastEditTime: 2025-04-29 10:23:55
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\grade\GradeControllerV2\GradeRuleDataV2.ts
 * 记得注释
 */
import DownGradeStrategy from "./DownGradeStrategy"
import GradeRule from "./GradeRule"
import PurchaseStrategy from "./PurchaseStrategy"


export default class GradeRuleDataV2 {
  // 等级规则uuid
  uuid: Nullable<string> = null
  // 状态, ENABLED:启用，DISABLED：禁用
  state: Nullable<string> = null
  // 等级有效期(月)
  validMonth: Nullable<number> = null
  // 付费升级策略
  purchaseStrategy: Nullable<PurchaseStrategy> = null
  // 降级策略
  downGradeStrategy: Nullable<DownGradeStrategy> = null
  // 规则
  rules: GradeRule[] = []
  // 当前是否处于初始化状态
  initial: Nullable<boolean> = null
  // 评定状态
  rateState: Nullable<string> = null
} 