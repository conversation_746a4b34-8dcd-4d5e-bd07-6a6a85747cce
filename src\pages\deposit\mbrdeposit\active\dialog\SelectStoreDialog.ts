import {Component, Prop, Vue, Watch} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import OrgApi from 'http/org/OrgApi'
import RSOrgFilter from 'model/common/RSOrgFilter'

@Component({
  name: 'SelectStoreDialog',
  components: {
    FormItem
  }
})
export default class SelectStoreDialog extends Vue {
  query: RSOrgFilter = new RSOrgFilter()
  selected: any[] = []
    stores: any[] = []
    $refs: any
    // 分页
    page = {
        currentPage: 1,
        total: 0,
        size: 10
    }
  @Prop()
  data: any
  @Prop()
  title: string

  @Prop({
    type: Boolean,
    default: false
  })
  dialogShow: boolean

  @Watch('dialogShow')
  onDataChange(value: any) {
    if (value && this.data) {
      if (this.data.length > 0 || (this.data.stores && this.data.stores.length) > 0) {
        this.setStoreParams(this.data)
      } else {
        this.$refs.storeTable.clearSelection()
      }
    } else {
      this.$refs.storeTable.clearSelection()
    }
  }

  created() {
    this.getList()
  }
  doSearch() {
    this.page.currentPage = 1
    this.getList()
  }

  doReset() {
    this.page.currentPage = 1
    this.query = new RSOrgFilter()
    this.getList()
    }
    doBeforeClose(done: any) {
        this.$emit('dialogClose')
        done()
    }
    doModalClose() {
        this.$emit('summit', this.selected)
        this.$emit('dialogClose')
    }
    handleSelectionChange(val: any) {
        this.selected = val
    }
    /**
     * 分页页码改变的回调
     * @param val
     */
    onHandleCurrentChange(val: number) {
        this.page.currentPage = val
        this.getList()
    }
    /**
     * 每页多少条的回调
     * @param val
     */
    onHandleSizeChange(val: number) {
        this.page.size = val
        this.getList()
    }
    private getList() {
      this.query.page = this.page.currentPage - 1
      this.query.pageSize = this.page.size
      OrgApi.query(this.query).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.stores = resp.data
          this.page.total = resp.total
        }
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    }
    private setStoreParams(value: any) {
        setTimeout(() => {
          if (value && value.length > 0) {
            value.forEach((item: any) => {
              this.stores.forEach((subItem: any, subIndex: number) => {
                if (item.id === subItem.org.id && item.name === subItem.org.name) {
                  if (this.$refs.storeTable) {
                    this.$refs.storeTable.toggleRowSelection(subItem, true)
                  }
                }
              })
            })
          }
          if (value && value.stores && value.stores.length > 0) {
              value.stores.forEach((item: any) => {
                  this.stores.forEach((subItem: any, subIndex: number) => {
                      if (item.id === subItem.org.id && item.name === subItem.org.name) {
                          if (this.$refs.storeTable) {
                              this.$refs.storeTable.toggleRowSelection(subItem, true)
                          }
                      }
                  })
              })
          }
        }, 300)
    }
}
