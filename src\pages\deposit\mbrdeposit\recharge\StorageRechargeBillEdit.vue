<!--
 * @Author: 黎钰龙
 * @Date: 2024-07-11 18:11:09
 * @LastEditTime: 2024-07-26 16:18:07
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\mbrdeposit\recharge\StorageRechargeBillEdit.vue
 * 记得注释
-->
<template>
  <div class="storage-recharge-edit">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button type="primary" @click="doSave" v-if="hasOptionPermission('/储值/储值管理/储值充值单','单据维护')" size="large">
          {{ i18n('/公用/按钮/保存') }}
        </el-button>
        <el-button @click="doCancel" size="large">
          {{ i18n('/公用/按钮/取消') }}
        </el-button>
      </template>
    </BreadCrume>
    <div class="setting-container">
      <div class="section-title">{{i18n('基础信息')}}</div>
      <el-form ref="ruleForm" :model="form" :rules="rules" label-width="130px">
        <el-form-item :label="i18n('充值会员')" prop="rechargeType">
          <div class="gray-tips" v-if="form.rechargeType === 'file'">
            -{{i18n('请先在当前页面中维护活动内容，活动创建完成后，再于活动查看页导入充值对象')}}
          </div>
          <el-radio v-model="form.rechargeType" :disabled="isDisabled" label="file">
            {{i18n('批量导入')}}
          </el-radio>
          <el-radio v-model="form.rechargeType" :disabled="isDisabled" label="mobie">
            {{i18n('/营销/券礼包活动/群发券新建界面/填写手机号')}}
          </el-radio>
          <el-form-item label="" prop="phoneForm">
            <template v-if="form.rechargeType === 'mobie'">
              <span class="plain-btn-blue" @click="doAddPhoneGift" v-if="form.lines && form.lines.length < 10">
                {{i18n('添加')}}
              </span>
              <el-table :data="form.lines">
                <el-table-column :label="i18n('/会员/会员资料/手机号')" width="200">
                  <template slot-scope="scope">
                    <el-form-item label="" :prop="`lines[${scope.$index}].mobile`" :rules="phoneGiftRule">
                      <el-input v-model.trim="scope.row.mobile" :placeholder="formatI18n('/会员/会员资料', '请输入手机号')">
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column :label="i18n('本金金额')" width="256">
                  <template slot-scope="scope">
                    <AutoFixInput :min="0" :max="5000.00" :fixed="2" style="width: 210px" v-model="scope.row.amount"
                      :appendTitle="formatI18n('/券/购券管理','元')" :placeholder="formatI18n('/营销/券礼包活动/核销第三方券/请输入')" />
                  </template>
                </el-table-column>
                <el-table-column :label="i18n('赠送金额')" width="256">
                  <template slot-scope="scope">
                    <AutoFixInput :min="0" :max="5000.00" :fixed="2" style="width: 210px"
                      v-model="scope.row.giftAmount" :appendTitle="formatI18n('/券/购券管理','元')" :placeholder="formatI18n('/营销/券礼包活动/核销第三方券/请输入')" />
                  </template>
                </el-table-column>
                <el-table-column :label="i18n('操作')" width="120">
                  <template slot-scope="scope">
                    <span class="span-btn" @click="removePhoneGift(scope.$index)">{{i18n('删除')}}</span>
                  </template>
                </el-table-column>
              </el-table>
            </template>
          </el-form-item>
        </el-form-item>
        <el-form-item :label="i18n('客户信息')">
          <el-input type="textarea" v-model="form.customInfo" maxlength="50" :placeholder="i18n('请填写客户信息')" style="width:400px" :rows="4">
          </el-input>
        </el-form-item>
        <el-form-item :label="i18n('/公用/券核销/发生组织')" prop="occurredOrgId">
          <SelectStores v-model="occurredOrg" :isOnlyId="false" :hideAll="true" width="400px" :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
          </SelectStores>
        </el-form-item>
        <el-form-item :label="i18n('/储值/会员储值/会员储值报表/充值流水-按支付方式/付款方式')" prop="payInfo">
          <span class="plain-btn-blue" @click="doAddPay" v-if="form.payInfo && form.payInfo.length < 10">
            {{i18n('添加付款方式')}}
          </span>
          <el-table :data="form.payInfo">
            <el-table-column :label="i18n('/储值/会员储值/会员储值报表/充值流水-按支付方式/付款方式')" width="200">
              <template slot-scope="scope">
                <el-form-item :prop="`payInfo[${scope.$index}].payInfo`" :rules="payInfoRule">
                  <el-select :placeholder="formatI18n('/公用/券模板', '请选择')" v-model="scope.row.payInfo" value-key="id">
                    <el-option v-for="(item,index) in payMethodList" :key="index" :label="item.name" :value="item">
                    </el-option>
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column :label="i18n('付款金额')" width="256">
              <template slot-scope="scope">
                <el-form-item :prop="`payInfo[${scope.$index}].payAmount`" :rules="payAmountRule">
                  <AutoFixInput :min="0" :max="99999999.99" :fixed="2" style="width: 210px" v-model="scope.row.payAmount"
                    :appendTitle="formatI18n('/券/购券管理','元')" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column :label="i18n('操作')" width="120">
              <template slot-scope="scope">
                <span class="span-btn" @click="removePayInfo(scope.$index)">{{i18n('删除')}}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        <el-form-item :label="i18n('付款总金额')">
          {{getTotalPrice}}
        </el-form-item>
        <el-form-item :label="i18n('/卡/卡管理/卡回收单/备注')">
          <el-input type="textarea" maxlength="500" style="width: 300px" v-model="form.remark" :placeholder="i18n('/卡/卡管理/卡回收单/备注')">
          </el-input>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts" src="./StorageRechargeBillEdit.ts">
</script>

<style lang="scss" scoped>
.storage-recharge-edit {
  width: 100%;
  height: 100%;
}
::v-deep .el-table::before {
  height: 0 !important;
}
</style>