import {Component, Vue} from 'vue-property-decorator'
import I18nPage from "common/I18nDecorator";
import Drawerx from "cmp/drawer/Drawerx";
import MemberDetail from "model/member_v2/member/MemberDetail";
import MemberSelectorDialog from "cmp/selectordialogs/MemberSelectorDialog";
import Member from "model/member/Member";
import MemberApi from "http/member_v2/MemberApi";
import TransferBenefitItems from "model/member_v2/member/TransferBenefitItems";

class MemberAssetsFormData {
  arr = ['POINTS', 'COUPON', 'PREPAY', 'CARD']
}

@Component({
  name: 'ConditionDrawer',
  components: {
    Drawerx,
    MemberSelectorDialog,
  }
})
@I18nPage({
  auto: false,
  prefix: [
    '/会员/会员资料/详情界面/会员资产转移',
    '/会员/会员资料',
    '/公用/按钮',
    '/公用/提示',
  ],
})
export default class MemberAssetsDrawer extends Vue {
  $refs: any
  visible: boolean = false
  dtl: MemberDetail = new MemberDetail()
  form = new MemberAssetsFormData();
  targetMember = new MemberDetail()
  availableArr = ['POINTS', 'COUPON', 'PREPAY', 'CARD']

  confirmDialog = {
    visible: false
  }

  page = {
    currentPage: 1,
    total: 0,
    size: 20
  }

  created() {
  }

  show(dtl: MemberDetail) {
    MemberApi.getMemberAndBenefit(dtl.memberId!).then((res) => {
      if (res.data) {
        this.visible = true
        this.dtl = res.data
        this.form.arr = []
        this.availableArr = []
        if (this.dtl.points && this.dtl.points > 0) {
          this.form.arr.push('POINTS')
          this.availableArr.push('POINTS')
        }
        if (this.dtl.couponCount && this.dtl.couponCount > 0) {
          this.form.arr.push('COUPON')
          this.availableArr.push('COUPON')
        }
        if (this.dtl.balance && this.dtl.balance > 0) {
          this.form.arr.push('PREPAY')
          this.availableArr.push('PREPAY')
        }
        if (this.dtl.cardCount && this.dtl.cardCount > 0) {
          this.form.arr.push('CARD')
          this.availableArr.push('CARD')
        }
        this.targetMember = new MemberDetail()
      }
    })
  }

  close() {
    this.visible = false
    this.targetMember = new MemberDetail()
    this.$emit('closed')
  }

  showMemberSelector() {
    this.$refs.memberSelectorDialog.open([], 'single')
  }

  doSubmitGoods(arr: Member[]) {
    if (arr[0].memberId === this.dtl.memberId) {
      this.$message.error(this.i18n('不能选择当前会员'))
      return
    }
    MemberApi.getMemberAndBenefit(arr[0].memberId!).then((res) => {
      if (res.data) {
        this.targetMember = res.data
      }
    })
  }

  transferMemberBenefit() {
    if (!this.targetMember.memberId) {
      this.$message.error(this.i18n('请选择需要转入的会员'))
      return
    }
    if (this.form.arr.length === 0) {
      this.$message.error(this.i18n('请选择要转移的权益资产'))
      return
    }
    this.confirmDialog.visible = true
  }

  doConfirm() {
    let items = new TransferBenefitItems()
    items.sourceMemberId = this.dtl.memberId!
    items.targetMemberId = this.targetMember.memberId!
    items.items = this.form.arr
    MemberApi.transferMemberBenefit(items).then((res) => {
      if (res && res.code === 2000) {
        this.$message.success(this.i18n('转移成功'))
        this.confirmDialog.visible = false
        this.close()
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }


}
