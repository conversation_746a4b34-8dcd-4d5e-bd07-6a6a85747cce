import ApiClient from 'http/ApiClient'
import CustomizeEntry from 'model/miniappsetup/aliappsetup/CustomizeEntry'
import Response from 'model/common/Response'
import SaveCustomizeEntryRequest from 'model/miniappsetup/aliappsetup/SaveCustomizeEntryRequest'

export default class AliAppletCustomizeEntryApi {
  /**
   * 更新
   * 更新。
   * 
   */
  static modify(body: SaveCustomizeEntryRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/applet/customize/entry/modify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询
   * 查询。
   * 
   */
  static query(): Promise<Response<CustomizeEntry[]>> {
    return ApiClient.server().post(`/v1/applet/customize/entry/query`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除
   * 删除。
   * 
   */
  static remove(id: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/applet/customize/entry/remove`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存
   * 保存。
   * 
   */
  static save(body: SaveCustomizeEntryRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/applet/customize/entry/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
