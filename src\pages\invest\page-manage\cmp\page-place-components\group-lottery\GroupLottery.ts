import I18nPage from 'common/I18nDecorator';
import { Component, Prop, Vue } from 'vue-property-decorator';
import PlaceTemplateMixins from '../PlaceTemplateMixins';

@Component({
  name: 'GroupLottery',
  components: {},
  mixins: [PlaceTemplateMixins],
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/设置/页面管理',
    '/页面/页面管理'
  ],
  auto: true
})
export default class GroupLottery extends Vue {
  @Prop() componentItem: any;
  get localProperty() {
    return this.componentItem.props;
  }
};