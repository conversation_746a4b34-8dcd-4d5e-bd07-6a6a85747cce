<template>
  <div class="member-card">
    <member-card-title :icon="require('~assets/image/member/<EMAIL>')"
                       :title="title"></member-card-title>
    <template v-if="channels.length > 0">
    <div class="member-channel-container">
      <div class="member-channel-item"
           v-for="(item,index) in channels"
           :key="index">
        <member-channel-icon class="channel-icon"
                             :type="item.channelType"></member-channel-icon>
        <div>
          <div class="channel-name">
            {{ i18n(item.channel) }}
            <el-tooltip v-if="item.channelId" :content="item.channelId" placement="top"><i class="el-icon-warning"
                                           style="color: #79879E;margin-left: 4px"></i></el-tooltip>
          </div>
          <member-form-item v-for="id in item.identityList"
                            :key="id.ident.id"
                            :label="id.ident.type + '：'">
            {{ id.ident.id }}
          </member-form-item>
          <member-form-item :label="i18n('时间') + '：'">
            {{ item.date | dateFormate3 }}
          </member-form-item>
          <member-form-item :label="i18n('昵称') + '：'">
            {{ item.nickName | strFormat }}
          </member-form-item>
        </div>
      </div>
    </div>
    </template>
    <empty-data v-else></empty-data>
  </div>
</template>
<script lang="ts"
        src="./MemberInfoChannel.ts">
</script>
<style lang="scss"
       scoped>
.member-channel-container {
  max-height: 330px;
  overflow-y: auto;
}

.member-channel-item {
  display: flex;
  align-items: start;

  & + & {
    margin-top: 12px;
  }

  ::v-deep .member-form-item {
    line-height: 22px !important;
  }

  .channel-icon {
    width: 48px;
    height: 48px;
    margin-right: 12px;
  }

  .channel-name {
    display: inline-flex;
    align-items: center;
    font-weight: 600;
    font-size: 14px;
    color: #242633;
    line-height: 22px;
  }
}
</style>
