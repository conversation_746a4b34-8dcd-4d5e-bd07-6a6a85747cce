/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-04-02 11:23:12
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\prepay\card\PrePayCard.ts
 * 记得注释
 */
import IdName from "model/common/IdName";

export default class PrePayCard {
	// 卡号
	code: Nullable<string> = null;
	// 账户uuid
	accountUuid: Nullable<string> = null;
	// 卡状态
	state: Nullable<string> = null;
	// 购卡人会员uuid
	buyerMemberId: Nullable<string> = null;
	// 是否存在购卡人会员uuid
	hasBuyerMemberId: Nullable<boolean> = null;
	// 购卡人会员手机号
	buyerMobile: Nullable<string> = null;
	// 购卡人会员号
	buyerHdCardMbrId: Nullable<string> = null;
	// 购卡人实体卡号
	buyerHdCardCardNum: Nullable<string> = null;
	// 持卡人会员uuid
	ownerMemberId: Nullable<string> = null;
	// 持卡人会员手机号
	ownerMobile: Nullable<string> = null;
	// 持卡人会员号
	ownerHdCardMbrId: Nullable<string> = null;
	// 持卡人实体卡号
	ownerHdCardCardNum: Nullable<string> = null;
	// 账户类型
	account: Nullable<IdName> = null;
	// 有效期
	expireDate: Nullable<Date> = null;
	// 卡模板号
	templateNumber: Nullable<string> = null;
	// 卡模板名
	templateName: Nullable<string> = null;
	// 活动
	activity: Nullable<IdName> = null;
	// 卡面额
	faceAmount: Nullable<number> = null;
	// 当前卡余额
	total: Nullable<number> = null;
	// 当前实充余额
	balance: Nullable<number> = null;
	// 当前返现余额
	giftBalance: Nullable<number> = null;
	// 期初实充余额
	openingBalance: Nullable<number> = null;
	// 期初返现余额
	openingGiftBalance: Nullable<number> = null;
	// 期初卡余额
	openingTotal: Nullable<number> = null;
	// 卡类型
	cardType: Nullable<string> = null;
	// 是否存在转赠流水
	hasPresent: Nullable<boolean> = null;
	// 充值卡预售单
	preSaleOrderNumber: Nullable<string> = null;
	// 发卡门店
	issueOrg: Nullable<IdName> = null
  // 最近一次核销门店
  useOrg: Nullable<IdName> = null
	// 最近一次使用时间
  useTime: Nullable<Date> = null
  // 次数
  totalTimes: Nullable<number> = null
  // 剩余次数
  remainderTimes: Nullable<number> = null
  remark: Nullable<string> = null
  // 卡介质
  cardMedium: Nullable<string> = null
}
