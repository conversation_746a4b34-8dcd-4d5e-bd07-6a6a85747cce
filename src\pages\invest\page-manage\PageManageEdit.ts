import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import I18nPage from 'common/I18nDecorator';
import { Component, Vue, Watch } from 'vue-property-decorator';
import PlacePanel from './cmp/place-panel/PlacePanel';
import WidgetConfig from './cmp/template.map'
import DateUtil from 'util/DateUtil';
import RenderPagePlace from './cmp/render-page-place/RenderPagePlace.vue'
import RenderPageProps from './cmp/render-page-props/RenderPageProps.vue';
import DefaultPagePlaceProperty from './cmp/page-props-components/DefaultPagePlaceProperty';
import { EditMode } from 'model/local/EditMode';
import ContentTemplateApi from 'http/template/ContentTemplateApi';
import CreateRequest from 'model/template/CreateRequest'
import PublishRequest from 'model/template/PublishRequest'
import ObjectUtil from 'util/ObjectUtil';
import PlaceWidgetVO from 'model/local/PlaceWidgetVO';
import RenderTemplatesMap from './cmp/render-page-place/RenderTemplates.map';
import { PageComponentsVO } from './cmp/page-place-components/PageComponentsVO';
import UpdateRequest from 'model/template/UpdateRequest'
import ConstantMgr from 'mgr/ConstantMgr';
import CommonUtil from 'util/CommonUtil';

@Component({
  name: 'PageManageEdit',
  components: {
    BreadCrume, PlacePanel, RenderPagePlace, RenderPageProps
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理'
  ],
  auto: true
})
export default class PageManageEdit extends Vue {
  templateList: Array<any> = [];
  renderTemplateList: Array<any> = [];
  clonItem: any = {};
  activeComId: any = PageComponentsVO.TitleCmp; // 默认顶部导航栏
  activeUuid: any =  PageComponentsVO.TitleCmp; // 默认顶部导航栏
  activeIndex: number = -11; // 当前激活资源位索引 ,默认顶部导航栏
  activeProps: any = {}; // 当前激活资源位属性
  movePlaceIndex: number = 0;
  reloadPage: boolean = false
  titleProps: any = DefaultPagePlaceProperty.pageTitle();
  editModel: EditMode = EditMode.create
  editId: string = ''
  // 保存参数
  saveParams: CreateRequest = new CreateRequest()
  // 编辑参数
  updateParams: UpdateRequest = new UpdateRequest()
  // 发布参数
  publishRequestId: PublishRequest = new PublishRequest()
  valdateArr: any = []
  get panelArray() {
    return [
      {
        name: this.i18n('/公用/菜单/页面管理'),
        url: ''
      }
    ]
  }
  /**新建*/
  get isCreate() {
    return this.editModel === EditMode.create;
  }

  /**编辑*/
  get isEdit() {
    return this.editModel === EditMode.edit;
  }
  get globalTitleProps() {
    return this.$store.state.globalInvestTitle
  }
  // 获取右侧属性组件渲染列表
  get showComponentProp() {
    const showComponentProp = RenderTemplatesMap.getTemplates().find((v) => {
      return v.id ===this.activeComId;
    });
    let propsCmp: string[] = [];
    if (showComponentProp) {
      propsCmp = showComponentProp.showComponentProp as string[];
    } else {
      if(this.activeIndex === -11) {
        propsCmp = WidgetConfig.titleWidgets()[0].showComponentProp as any
      } else {
        propsCmp = [];
      }
    }
    return propsCmp
  }
  // 当前装修页的投放渠道
  get advertiseChannel() {
    if (this.$route.query.channel) {
      return JSON.parse(decodeURIComponent(this.$route.query.channel as string))
    }
    return null
  }
  mounted() {
    this.templateList = this.handlePanelList()
    this.renderTemplateList = [];
    this.initPageData();
    const loading = this.$loading(ConstantMgr.loadingOption)
    if(this.$route.query.editModel) {
      this.editModel = this.$route.query.editModel as EditMode
    }
    if (this.editModel === EditMode.create) {
      this.reloadPage = true
      this.resetGlobalTitle()
      this.$nextTick(() => {
        loading.close()
        const renderPageRef = this.$refs.RenderPagePlace as any
        renderPageRef.titleClick()
      })
    } else if (this.editModel === EditMode.edit) {
      if (this.$route.query.editId) {
        this.editId = this.$route.query.editId as string
      }
      this.loadData()
    }
  }
  // 编辑get参数
  loadData() {
    const loading = this.$loading(ConstantMgr.loadingOption)
    ContentTemplateApi.get(this.editId).then(res => {
      if (res.data) {
        this.reloadPage = true
        this.updateParams = res.data as any
        if(res.data.content) {
          const details = JSON.parse(res.data.content)
          this.setGlobalTitle(details.props)
          this.titleProps = details.props
          this.renderTemplateList = details.widgets
          this.renderTemplateList.forEach((item, index) => {
            if (!item.name) {
              item.props.name =
                item.props.propWidgetName + DateUtil.format(new Date()) + '-' + index;
            } else {
              item.props.name = item.name;
            }
          });
          this.initValidateArr()
        }
      }
    }).catch((err) => {
      this.$message.error(err.message || this.i18n('数据加载失败'))
    }).finally(() => {
      loading.close()
    })
  }
  initValidateArr() {
    if(this.valdateArr.length === 0) {
      this.valdateArr = this.renderTemplateList.map(({uuid}) => ({
        cmpUuid: uuid,
        validateRes: false
      }))
      this.valdateArr.push({
        cmpUuid: 'titleCmp',
        validateRes: false
      })
      return
    }
    this.valdateArr = this.filterChildArrayByIds(this.renderTemplateList, this.valdateArr)
  }
  filterChildArrayByIds(parentArray: any[], childArray: any[]) {  
    // 创建一个Set来存储父数组中的所有id  
    const idsSet = new Set(parentArray.map(item => item.uuid));  
    // 创建一个新数组来存储符合条件的子数组元素  
    const filteredChildArray = childArray.filter(item => idsSet.has(item.cmpUuid));  
    return filteredChildArray;  
} 
  resetGlobalTitle() {
    const defaultTitle = {
      propShareImageUrl: '',
      propShareStrategy: 'support',
      propShowCart: true,
      propShowTop: true,
      propTitle: '',
      styBgColor: '#F5F6F5',
      styCustomBgColor: "",
      id: 'titleCmp',
    }
    this.$store.dispatch('globalInvestTitleAction', defaultTitle)
  }
  setGlobalTitle(params: any) {
    this.$store.dispatch('globalInvestTitleAction', params)
  }
  initPageData() {
  }
  handlePanelList() {
    // this.currentPlatPlaces = this.allPlceSources.filter(
    //   (item) => item.platformId === this.platformId
    // );
    // return this.originGroups.map(({ widgets, ...others }) => ({
    //   widgets: widgets.filter((widget) =>
    //     this.currentPlatPlaces.some((palce) => palce.id === widget.id)
    //   ),
    //   ...others,
    // }));
    return WidgetConfig.GROUPS()
  }
  // 左侧组件移动
  panelMove(params: any) {
    let currentIndex = params;
    this.initPageData();
    this.movePlaceIndex = currentIndex;
    if (currentIndex <= 1) {
      this.movePlaceIndex = currentIndex + 1;
    }
  }
  dragClone(item: any) {
    this.clonItem = item;
  }
  dragEnd(item: any) {
    if (item.from !== item.to) {
      let temItem: PlaceWidgetVO = new PlaceWidgetVO();
      temItem.uuid = ObjectUtil.uuid();
      temItem.props = ObjectUtil.clone(this.clonItem.defaultProp);
      temItem.id = this.clonItem.id;
      temItem.name = this.clonItem.name + DateUtil.format(new Date()); //组件名称
      temItem.props.name = this.clonItem.name + DateUtil.format(new Date());
      temItem.props.uuid = temItem.uuid

      // temItem.props.id = temItem.id
      // 该地方为浅拷贝temItem，会对popPlaces数组影响，在temItem中的值发生变化时，对数组中的对应项修改
      this.renderTemplateList.splice(item.newIndex, 0, temItem);
      this.$nextTick(() => {
        const renderPagePlaceRef: any = this.$refs['RenderPagePlace']
        renderPagePlaceRef.activeCom({index:item.newIndex, item: temItem })
      })
      this.initValidateArr()
    }
  }
  // 组件渲染区域
  activeCmp(params: {index: number; item: PlaceWidgetVO }) {
    this.activeComId = params.item.id;
    this.activeUuid = params.item.uuid
    this.activeIndex = params.index;
    this.activeProps = params.item.props;
  }
  // 资源位组件操作按钮操作类型
  toolBarClick(params: any) {
    if (params.action === 'delete') {
      this.delPagePlaces(params.activeUuid);
    }
  }
  delPagePlaces(activeUuid: number) {
    this.$confirm(
      this.i18n('确认删除吗？')
    ).then(() => {
      this.renderTemplateList = this.renderTemplateList.filter(item => item.uuid !== activeUuid)
      this.afterDelWidget()
      this.initValidateArr()
    });
  }
  afterDelWidget() {
    const refs: any = this.$refs['RenderPagePlace'] 
    if (this.renderTemplateList.length === 0) {
      refs.activeCom({
        index: -11,
        item: {
          props: this.globalTitleProps,
          uuid: 'titleCmp',
          id: this.globalTitleProps.id,
          type: '',
          name: this.i18n('顶部导航栏')
        },
      });
    } else {
      if (this.activeIndex === 0) {
        refs.activeCom({
          index: -11,
          item: {
            props: this.globalTitleProps,
            uuid: 'titleCmp',
            id: this.globalTitleProps.id,
            type: '',
            name: this.i18n('顶部导航栏')
          },
        });
      } else {
        refs.activeCom({
          index: this.activeIndex - 1,
          item: {
            props: this.renderTemplateList[this.activeIndex - 1].props,
            uuid: this.renderTemplateList[this.activeIndex - 1].uuid,
            id: this.renderTemplateList[this.activeIndex - 1].id,
            type: '',
            name: this.renderTemplateList[this.activeIndex - 1].name
          },
        });
      }
    }
  }
  placeDragEnd(params: any[]) {
    this.renderTemplateList = params;
    this.initPageData();
  }
  // 右侧属性组件区域
  propsChange(props: { activeProps: any; activeIndex: any; }) {
    if (this.activeIndex === -11) {
      this.titleProps = props.activeProps;
    } else {
      let currentProp = props.activeProps;
      this.activeProps = props.activeProps;
      this.renderTemplateList[props.activeIndex].props = props.activeProps
      this.renderTemplateList[props.activeIndex].name = props.activeProps.name
    }
  }
  validate(params: any) {
    console.log('子组件的validate触发了吗', params);
    if(this.valdateArr.some((item: any) => item.cmpUuid === params.cmpUuid)) {
      this.valdateArr.forEach((item: { cmpUuid: any; validateRes: boolean  }) => {
        if(item.cmpUuid === params.cmpUuid) {
          item.validateRes = params.validateRes
        }
      })
    } else {
      this.valdateArr.push(params)
    }
  }
  // 调用保存接口
  save(isNeedPublish:boolean = false) {
    const loading = CommonUtil.Loading()
    setTimeout(() => {
      this.handleBeforeSaveData((cb) => {
        if (cb) {
          this.doSave(isNeedPublish);
        }
      });
      loading.close()
    }, 300);
  }
  handleBeforeSaveData(cb = (v: any) => v) {
    if(!this.globalTitleProps.propTitle || this.globalTitleProps.propTitle === '') {
      this.$message.error(this.i18n('请填写页面名称！'));
      cb(false);
      return false;
    }
    if(this.renderTemplateList.length === 0) {
      this.$message.error(this.i18n('请放置组件！'));
      cb(false);
      return false;
    }
    this.valdateArr.some((item : any) => {
      return item.validateRes
    })
    if(this.valdateArr.some((item : any) => item.validateRes)) {
      this.$message.error(this.i18n('请完善投放信息！'));
      cb(false);
      return false;
    }
    // 广告组件单独校验
    // if (this.renderTemplateList.some((item) => item.id === 'imageAd')) {
    //   let imageAdHotArea = this.renderTemplateList.find((item) => item.id === 'imageAd').props
    //     .propItems;
    //   if (imageAdHotArea.some((i: { validateResult: boolean; }) => i.validateResult === false)) {
    //     this.renderTemplateList[
    //       this.renderTemplateList.findIndex((item) => item.id === 'imageAd')
    //     ].validateResult = false;
    //     this.$message.error('图片广告热区未配置完成');
    //     cb(false);
    //     return false;
    //   }
    // }
    if (this.isCreate) {
      let content: any = {};
      content.props = this.globalTitleProps;
      content.id = 'global';
      content.uuid = ObjectUtil.uuid();
      content.widgets = this.renderTemplateList
      this.saveParams.content = content;
      this.saveParams.image = '';
      this.saveParams.name = this.globalTitleProps.propTitle;
      this.saveParams.placeName = '启动页';
      this.saveParams.channels = this.advertiseChannel
    } else {
      let content: any = JSON.parse(this.updateParams.content);
      content.props = this.globalTitleProps;
      content.widgets = this.renderTemplateList;
      this.updateParams.name = this.globalTitleProps.propTitle;
      this.updateParams.content = content;
    }

    cb(true);
  }
  async doSave(isNeedPublish:boolean = false) {
    if(this.isCreate) {
      this.doCreate(isNeedPublish)
    } else {
      this.doUpdate(isNeedPublish)
    }
  }
  async doCreate(isNeedPublish: boolean = false) {
    const loading = CommonUtil.Loading()
    try {
      let res = await ContentTemplateApi.create(this.saveParams);
      if(res.code === 2000) {
        if(isNeedPublish) {
          this.publishRequestId.id = res.data
          this.publish()
          return
        }
        this.resetGlobalTitle()
        this.$message.success(this.i18n('保存成功'))
        this.$router.push({name: 'page-manage'})
      } else {
        this.$message.error(res.msg || this.i18n('保存失败'))
      }
    } catch (error) {
      this.$message.error((error as Error).message || this.i18n('保存失败'))
    } finally {
      loading.close()
    }
  }
  async doUpdate(isNeedPublish: boolean = false) {
    const loading = CommonUtil.Loading()
    try {
      let res = await ContentTemplateApi.update(this.updateParams);
      if(res.code === 2000) {
        if(isNeedPublish) {
          this.publishRequestId.id = this.updateParams.id
          this.publish()
          return
        }
        this.resetGlobalTitle()
        this.$message.success(this.i18n('保存成功'))
        this.$router.push({name: 'page-manage'})
      } else {
        this.$message.error(res.msg || this.i18n('保存失败'))
      }
    } catch (error) {
      this.$message.error((error as Error).message || this.i18n('保存失败'))
    } finally {
      loading.close()
    }
  }
  // 保存发布操作
  doSaveAndPublish() {
    this.save(true)
  }
  // 取消返回
  goBack() {
    this.$router.push({name: 'page-manage'})
  }
  // 调用发布接口
  publish() {
    ContentTemplateApi.publish(this.publishRequestId).then((res) => {
      if(res.code === 2000) {
        this.resetGlobalTitle()
        this.$message.success(this.i18n('发布成功'))
        this.$router.push({name: 'page-manage'})
      } else {
        this.$message.error(res.msg || this.i18n('发布失败'))
      }
    }).catch(error => {
      let e = error as any;
      this.$message.error(e.message || this.i18n('发布失败'));
    })
  }
};