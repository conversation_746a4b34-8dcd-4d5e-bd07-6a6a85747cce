<template>
  <el-dialog :title="i18n('/公用/公共组件/门店选择弹框组件/标题/选择门店')" class="select-stores-dialog" append-to-body width="1122px" :close-on-click-modal="false"
    :visible.sync="dialogShow" @close="doClosed">
    <div class="wrap">
      <el-form label-width="80px">
        <el-row>
          <el-select v-model="searchType" style="width: 104px;">
            <el-option v-for="item in searchTypeList" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
          <el-input style="width: 175px;" :placeholder="i18n('/公用/菜单/搜索')" v-model="searchValue">
            <i slot="suffix" class="el-input__icon el-icon-search" @click.stop="search"></i>
          </el-input>
        </el-row>
      </el-form>
    </div>
    <div class="select-stores-box">
      <div class="stores-box-left">
        <div class="left-li" :class="{ 'active': limitedOrgsType == 'brand' }" @click="doChangeTab('brand')">{{i18n("/公用/券模板/品牌") }}</div>
        <div class="left-li" :class="{ 'active': limitedOrgsType == 'org' }" @click="doChangeTab('org')">{{ i18n("/资料/门店/门店") }}</div>
      </div>
      <div class="stores-box-center">
        <el-row class="thead">
          <el-col :span="1">
            <el-checkbox @change="doCheckAll($event)" />
          </el-col>
          <el-col :span="7">{{i18n('/资料/门店/门店名称')}}</el-col>
          <el-col :span="6">{{i18n('/公用/公共组件/商品选择弹框组件/查询/代码')}}</el-col>
          <el-col :span="7">{{i18n('ID')}}</el-col>
          <el-col :span="3">{{i18n('/资料/门店/门店状态')}}</el-col>
        </el-row>
        <el-row class="tbody" v-if="weimobOrgs && weimobOrgs.length">
          <el-row v-for="(item, index) of weimobOrgs" :key="item.vid" class="trow">
            <el-col :span="1">
              <el-checkbox v-model="checkboxList[index]" @change="doCheck($event, index)" />
            </el-col>
            <el-col :span="7" @click.native="doCheckRow(index)" :title="item.vidName">{{ item.vidName?item.vidName:"-" }}</el-col>
            <el-col :span="6" @click.native="doCheckRow(index)" :title="item.vidCode">{{ item.vidCode?item.vidCode:"-" }}</el-col>
            <el-col :span="7" @click.native="doCheckRow(index)" :title="item.vid">{{ item.vid?item.vid:"-" }}</el-col>
            <el-col :span="3" @click.native="doCheckRow(index)"
              :title="item.vidStatus">{{ item.vidStatus == 1?i18n("/资料/渠道/启用"):i18n("/资料/渠道/禁用") }}</el-col>
          </el-row>
        </el-row>
      </div>
      <div class="stores-box-right">
        <el-row class="thead">
          {{formatI18n('/公用/公共组件/门店选择弹框组件/表格/已选门店：')}}{{selectedArr?(selectedArr.filter(e=>e.id)).length: 0}}
          <a @click="delAll">{{ formatI18n('/公用/公共组件/品牌选择弹框组件/表格/清除') }}</a>
        </el-row>
        <el-row class="tbody" v-if="selectedArr && selectedArr.length > 0">
          <el-row class="trow" v-for="(item) of selectedArr" :key="item.id" style="position: relative;display: flex;align-items: center">
            <div class="left">{{ "[" + (item.id?item.id:'-') + "]" + item.name }}</div>
            <a @click="delItem(item.id)">{{ formatI18n('/公用/公共组件/品牌选择弹框组件/表格/清除') }}</a>
          </el-row>
          <el-row v-if="!selectedArr || selectedArr.length === 0" class="trow" style="text-align: center;color: #909399">
            {{ formatI18n('/公用/提示/暂无数据') }}
          </el-row>
        </el-row>
      </div>
    </div>
    <div class="select-stores-footer">
      <div class="wrap">
        <el-pagination :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
          @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background layout="total, prev, pager, next, sizes,  jumper">
        </el-pagination>
      </div>
      <br />
      <div class="footer-btn">
        <el-button @click="doCancel">{{ formatI18n("/公用/按钮", "取消") }}</el-button>
        <el-button type="primary" @click="doConfirm">{{ formatI18n("/公用/按钮", "确定") }}</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./OrgsWeiMenEx.ts" />

<style lang="scss" scoped>
.select-stores-dialog {
  display: flex;
  align-items: center;
  justify-content: center;
  .current-page {
    .el-select {
      width: 100%;
    }
  }

  .wrap {
    width: 60%;
    padding-left: 15%;
    height: 35px;
    line-height: 34px;
  }

  .select-stores-box {
    width: 100%;
    height: 500px;
    border: 1px solid #edeef2;
    display: flex;
    justify-content: space-between;

    .stores-box-left {
      width: 15%;
      border-right: 1px solid #edeef2;
      padding: 4px;

      .left-li {
        color: #595961;
        padding-left: 12px;
        height: 32px;
        line-height: 32px;
        cursor: pointer;
        margin-bottom: 4px;

        &.active {
          background: #20a0ff;
          color: #ffffff;
          font-weight: 600;
        }
      }
    }

    .stores-box-center {
      width: 60%;
      .thead {
        padding-left: 5px;
        height: 35px;
        line-height: 34px;
        background-color: var(--border-color);
      }

      .tbody {
        // height: 100%;
        height: calc(100% - 35px);
        overflow-y: auto;

        .trow {
          height: 35px;
          line-height: 34px;

          &:hover {
            background-color: #f8f9fc;
          }

          .el-col {
            &:first-child {
              text-align: center;
            }

            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: flex;
            flex-direction: column;
            flex-wrap: wrap;
          }
        }
      }

      .stores-tab {
        height: 48px;
        padding: 0 16px;
        background: #f5f7fa;
        display: flex;
        align-items: center;

        .select-excluded-goods {
          width: 150px;
          cursor: pointer;

          .vertical-line {
            display: inline-block;
            width: 1px;
            height: 14px;
            margin: 0 8px;
            background: #e9ecf0;
          }
        }
      }

      .stores-content {
        height: 450px;
      }
    }

    .stores-box-right {
      width: 30%;
      flex: 1;
      .thead {
        height: 35px;
        line-height: 34px;
        background-color: var(--border-color);

        .el-col {
          &:first-child {
            text-align: center;
          }
        }
      }

      .tbody {
        // height: 100%;
        height: calc(100% - 35px);
        overflow-y: auto;

        .trow {
          height: 35px;
          line-height: 34px;

          &:hover {
            background-color: #f8f9fc;
          }

          .el-col {
            &:first-child {
              text-align: center;
            }

            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: flex;
            flex-direction: column;
            flex-wrap: wrap;
          }
        }
      }

      .goods-tab {
        height: 48px;
        padding: 0 16px;
        background: #f5f7fa;
        display: flex;
        align-items: center;

        .select-excluded-goods {
          width: 150px;
          cursor: pointer;

          .vertical-line {
            display: inline-block;
            width: 1px;
            height: 14px;
            margin: 0 8px;
            background: #e9ecf0;
          }
        }
      }

      .goods-content {
        height: 450px;
      }
    }
  }

  .select-stores-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 24px;

    .footer-info {
      flex: 1;
      color: #8a9099;
      position: relative;

      .line {
        position: relative;
        padding-left: 8px;
        margin-left: 8px;

        &::before {
          content: "";
          position: absolute;
          width: 1px;
          height: 14px;
          background: #dfe2e6;
          top: 2px;
          left: 0;
        }
      }
    }
  }

  .cursor {
    cursor: pointer;
  }
}
</style>
