/*
 * @Author: 黎钰龙
 * @Date: 2024-05-06 11:18:53
 * @LastEditTime: 2025-04-22 14:07:23
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\invest\nav-manage\NavManage.ts
 * 记得注释
 */
import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import I18nPage from 'common/I18nDecorator';
import { MenuIconsCommon, MenuIconsRudder } from 'model/local/MenuIcons';
import { Component, Vue, Watch } from 'vue-property-decorator';
import CustomTab from './cmp/custom-tab/CustomTab.vue';
import EffectTab from './cmp/effect-tab/EffectTab.vue';

import ConstantMgr from "mgr/ConstantMgr";
import NavigationSettingApi from "http/navigation/NavigationSettingApi";
import NavigationSetting from 'model/navigation/NavigationSetting'
import JumpPageInfo from 'model/navigation/JumpPageInfo'
import MenuSetting from 'model/navigation/MenuSetting'
import NavigationSettingInit from './NavManageInit'
import { CmsConfigChannel } from 'model/template/CmsConfig';

// 根据接口所需字段拓展前端展示所需字段
class customMenuSetting extends MenuSetting {
  showRepealce?: boolean // 已选择情况
  isUseIcon?: boolean // 是否使用的 icon
  // 默认左侧回显不选中
  isActive: boolean = false
}

class AddMenuData extends NavigationSetting {
  // 菜单设置
  menuSettings: customMenuSetting[] = []
}

// class colorForm {
//   // 选中的颜色
//   selectedColor: Nullable<string> = null
//   // 未选中的颜色
//   unselectedColor: Nullable<string> = null
//   // 选中的文本颜色
//   selectedFontColor: Nullable<string> = null
//   // 选中的图标颜色
//   selectedIconColor: Nullable<string> = null
//   // 未选中的文本颜色
//   unselectedFontColor: Nullable<string> = null
//   // 未选中的图标颜色
//   unselectedIconColor: Nullable<string> = null
//   // 菜单数量
//   menuQuantity: Nullable<number> = null
//   // 菜单设置
//   menuSettings: customMenuSetting[] = []
// }

// class Form {
//   // 导航样式
//   navigationType: Nullable<NavigationStyle> = null
//   // 图标配色
//   iconColorType: Nullable<IconColorStyle> = null
//   // nromalSystem: colorForm = new colorForm()
//   // nromalCustom: colorForm = new colorForm()
//   // rudderSystem: colorForm = new colorForm()
//   // rudderCustom: colorForm = new colorForm()
//   selectedColor: Nullable<string>;
//   selectedFontColor: Nullable<string>;
//   selectedIconColor: Nullable<string>;
//   unselectedColor: Nullable<string>;
//   unselectedFontColor: Nullable<string>;
//   unselectedIconColor: Nullable<string>;
//   menuQuantity: Nullable<number>;
// }
// interface ColorForm {
//   selectedColor: Nullable<string>;
//   selectedFontColor: Nullable<string>;
//   selectedIconColor: Nullable<string>;
//   unselectedColor: Nullable<string>;
//   unselectedFontColor: Nullable<string>;
//   unselectedIconColor: Nullable<string>;
//   menuQuantity: Nullable<number>;
// }
// 所有的key
// enum AllKey {
//   nromalSystem = 'nromalSystem',
//   nromalCustom = 'nromalCustom',
//   rudderSystem = 'rudderSystem',
//   rudderCustom = 'rudderCustom'
// }

@Component({
  name: 'NavManage',
  components: {
    BreadCrume,
    CustomTab,
    EffectTab
  }
})
@I18nPage({
  prefix: [
    '/导航',
    '/公用/券模板',
    '/公用/按钮',
    '/页面/页面管理'
  ],
  auto: true
})

export default class NavManage extends Vue {
  custom: AddMenuData = new AddMenuData() //当前自定义数据
  demoMenuIconsCommon: any[] = Object.keys(MenuIconsCommon)
  demoMenuIconsRudder: any[] = Object.keys(MenuIconsRudder)
  $refs: any
  get panelArray() {
    return [
      {
        name: this.navType == CmsConfigChannel.ALIPAY? this.i18n('/公用/菜单/支付宝导航设置') : this.navType == CmsConfigChannel.H5 ? this.i18n('/公用/菜单/H5导航设置') : this.i18n('/公用/菜单/微信导航设置'),   // this.i18n('/公用/菜单/导航设置'),  
        url: ''
      }
    ]
  }



  navType: CmsConfigChannel = CmsConfigChannel.WEIXIN
  created() {
    this.pageActivated()
  }

  @Watch('$route.name') 
  routeNameChange() {
    this.showFlag = false
    this.pageActivated()
  }

  showFlag: boolean = true

  pageActivated(){
    const routeName =  this.$route.name
    if(routeName == 'nav-manage-ali') {
      this.navType = CmsConfigChannel.ALIPAY
    }else if(routeName == 'nav-manage-h5') {
      this.navType = CmsConfigChannel.H5
    }else if(routeName == 'nav-manage-weixin') {
      this.navType = CmsConfigChannel.WEIXIN
    }
    this.init()
  }

  // 子组件数据更新，同步到当前页面
  updateData() {
    let [res] = this.$refs['customTab'].toParams()
    this.applyCustom(res)
  }
  // getKey(params: any) {
  //   let key: AllKey | any = ''
  //   if (params.navigationType === 'normal' && params.iconColorType === 'system') {
  //     key = AllKey.nromalSystem
  //   } else if (params.navigationType === 'normal' && params.iconColorType === 'custom') {
  //     key = AllKey.nromalCustom
  //   } else if (params.navigationType === 'rudder' && params.iconColorType === 'system') {
  //     key = AllKey.rudderSystem
  //   } else if (params.navigationType === 'rudder' && params.iconColorType === 'custom') {
  //     key = AllKey.rudderCustom
  //   }

  //   this.customKey = key
  //   return key;
  // }

  // 处理自定义的展示数据
  applyCustom(params: NavigationSetting) {
    // let key: AllKey | string = this.getKey(params)
    // 只有修改上一次数据时才更新颜色
    // if (lastKey === key) {
    // this.custom.iconColorType = params.iconColorType
    // this.custom.navigationType = params.navigationType
    // const paramsList: (keyof NavigationSetting)[] = ['selectedColor', 'selectedFontColor', 'selectedIconColor', 'unselectedColor', 'unselectedFontColor', 'unselectedIconColor', 'menuQuantity']
    // for (const i of paramsList) {
    // this.custom[i] = params[i]
    // if (params.menuQuantity) {
    Object.assign(this.custom, params)
    this.custom.menuQuantity = [3, 5].find(e => e === params.menuQuantity) || 3
    // }
    // }
    // } else {
    // this.custom.menuQuantity = [3, 5].find(e => e === params.menuQuantity) || 3
    // 清空表单验证
    this.$refs.customTab.$children[0].clearValidate()
    // 切换状态仅修改状态值
    // this.custom.iconColorType = params.iconColorType
    // this.custom.navigationType = params.navigationType
    // }

    // this.applyAllColor(params)
    this.applyMenuData()
  }

  // 处理刚开始颜色的默认值
  // applyAllColor(params: NavigationSetting,key:AllKey | string) {
  //   const paramsList: (keyof NavigationSetting)[] = ['selectedColor', 'selectedFontColor', 'selectedIconColor', 'unselectedColor', 'unselectedFontColor', 'unselectedIconColor', 'menuQuantity']
  //   for (const i of paramsList) {
  //     switch (i) {
  //       case 'selectedColor':
  //         this.custom[key][i] = params[key][i] ? params[key][i] : '#FC5312';
  //         break;
  //       case 'unselectedColor':
  //         this.custom[key][i] = params[key][i] ? params[key][i] : '#A9A9A9';
  //         break;
  //       case 'selectedFontColor':
  //         this.custom[key][i] = params[key][i] ? params[key][i] : '#FC5312';
  //         break;
  //       case 'unselectedFontColor':
  //         this.custom[key][i] = params[key][i] ? params[key][i] : '#9F9F9F';
  //         break;
  //       case 'selectedIconColor':
  //         this.custom[key][i] = params[key][i] ? params[key][i] : '#FC5312';
  //         break;
  //       case 'unselectedIconColor':
  //         this.custom[key][i] = params[key][i] ? params[key][i] : '#A9A9A9';
  //         break;
  //     }
  //   }
  // }

  // 处理菜单部分数据
  applyMenuData() {
    // 当前存在菜单配置数据
    let num: number = this.custom.menuSettings.length;
    const length: number = [3, 5].find(e => e === this.custom.menuQuantity) || 3

    // 舵式菜单数据情况下
    if (this.custom.navigationType === 'rudder') {
      if (this.custom.menuQuantity != this.custom.menuSettings.length) {
        [this.custom.menuSettings[1], this.custom.menuSettings[2]] = [this.custom.menuSettings[2], this.custom.menuSettings[1]]
      }
      this.custom.menuSettings = this.menuAddData(length, this.custom)
    } else if (this.custom.navigationType === 'normal') {
      // 如初始时后端无菜单配置数据 前端自行添加展示数据 （通用少于2条则需添加,不少于2条则按原来的数据展示）
      let needNum = 2 - num;
      if (needNum > 0) {
        this.custom.menuSettings = this.menuAddData(needNum, this.custom)
        // console.log(this.custom[key].menuSettings)
      }
    }
    // 添加可替换展示字段
    this.custom.menuSettings.forEach((item: customMenuSetting) => {
      let flag: boolean = item.hasOwnProperty('showReplace')
      if (!flag) {
        item.showRepealce = false;
      }
    });
  }

  init() {
    const loading = this.$loading(ConstantMgr.loadingOption);
    NavigationSettingApi.get(this.navType).then((res: any) => {
      loading.close()
      if (res.code === 2000) {
        //如果接口没有数据 则使用初始化的数据
        let params = res.data == null ? new NavigationSettingInit() : res.data;
        // 根据后端字段 处理渲染前端界面
        this.applyRenderPage(params)
      } else {
        this.$message.error(res.msg)
      }
    }).catch(err => {
      loading.close()
      this.$message.error(err)
    }).finally(()=>{
      this.showFlag = true
    })
  }

  // 根据接口字段处理前端渲染字段
  applyRenderPage(params: NavigationSetting) {
    this.custom.iconColorType = params.iconColorType
    this.custom.navigationType = params.navigationType
    const paramsList: (keyof NavigationSetting)[] = ['selectedColor', 'selectedFontColor', 'selectedIconColor', 'unselectedColor', 'unselectedFontColor', 'unselectedIconColor', 'menuQuantity']
    for (const i of paramsList) {
      // 如果后端第一次没传颜色 处理默认颜色
      switch (i) {
        case 'selectedColor':
          this.custom[i] = params[i] ? params[i] : '#FC5312';
          break;
        case 'unselectedColor':
          this.custom[i] = params[i] ? params[i] : '#A9A9A9';
          break;
        case 'selectedFontColor':
          this.custom[i] = params[i] ? params[i] : '#FC5312';
          break;
        case 'unselectedFontColor':
          this.custom[i] = params[i] ? params[i] : '#9F9F9F';
          break;
        case 'selectedIconColor':
          this.custom[i] = params[i] ? params[i] : '#FC5312';
          break;
        case 'unselectedIconColor':
          this.custom[i] = params[i] ? params[i] : '#A9A9A9';
          break;
      }

      if (i === 'menuQuantity') {
        this.custom[i] = [3, 5].find(e => e === params.menuQuantity) || 3
      }
    }
    let num: number = params.menuSettings.length;
    const length: number = this.custom.menuQuantity ?? 3;
    if (this.custom.navigationType === 'rudder') {
      params.menuSettings = this.menuAddData(length, params)
    } else if (this.custom.navigationType === 'normal') {
      // 如初始时后端无菜单配置数据 前端自行添加展示数据 （通用少于2条则需添加）
      let needNum = 2 - num;
      if (needNum > 0) {
        params.menuSettings = this.menuAddData(needNum, params)
      }
    }
    // console.log(2);
    // 添加可替换展示字段
    params.menuSettings.forEach((item: customMenuSetting) => {
      let flag: boolean = item.hasOwnProperty('showReplace')
      if (!flag) {
        item.showRepealce = false;
      }
      // 默认都是用系统图标
      item.isUseIcon = true
      // 当前数据存在图标
      if (item.selectedIcon && item.unselectedIcon) {
        // 图片
        let regex = /.jpg|.jpeg|.png|.gif/i;
        if (regex.test(item.selectedIcon)) {
          item.isUseIcon = false
        } else {
          item.isUseIcon = true
        }
      }
    });
    // 给前端展示数据处理菜单部分数据
    this.custom.menuSettings = params.menuSettings as any
    // console.log(this.custom)
  }

  // 菜单数据操作
  menuAddData(length: number, params: NavigationSetting) {
    let obj = {
      // 菜单名称
      name: '',
      // 已选中图标
      selectedIcon: '',
      // 未选中图标
      unselectedIcon: '',
      // 跳转页面信息
      jumpPageInfo: new JumpPageInfo(),
      // 默认使用系统图标
      isUseIcon: true,
      // 默认左侧回显不选中
      isActive: false
    }
    return new Array(length).fill(obj).map((e, i) => {
      try {
        // 防止对象浅拷贝指针重复
        e.jumpPageInfo = new JumpPageInfo()
        if (i === 0) { obj.isActive = true } else { obj.isActive = false }
        return { ...e, ...params.menuSettings[i] };
      } catch (error) {
        return { ...e };
      }
    })
  }

  // 保存
  doSave() {
    // this.$refs['customTab']
    this.$refs.customTab.$children[0].validate().then(() => {
      const loading = this.$loading(ConstantMgr.loadingOption);
      let sendBody: NavigationSetting = {
        ...this.custom
      }

      sendBody.navigationChannel = this.navType

      NavigationSettingApi.save(sendBody).then((res) => {
        loading.close()
        if (res.code === 2000) {
          this.$message.success(this.i18n('保存成功'))
          this.$router.push({ name: "page-manage" })
        } else {
          this.$message.error(res.msg!)
        }
      }).catch(err => {
        loading.close()
        this.$message.error(err.message)
      })

    }).catch(() => {
      this.$message.warning(this.i18n('有必填项未填写'))
    })
  }

  // 取消
  doCancel() {
    this.$router.push({ name: "page-manage" })
  }

};