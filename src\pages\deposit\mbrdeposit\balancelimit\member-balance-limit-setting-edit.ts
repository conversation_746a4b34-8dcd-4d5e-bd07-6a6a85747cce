import Bread<PERSON>rume from 'cmp/bread-crumb/BreadCrume';
import ChannelSelect from 'cmp/channelselect/ChannelSelect';
import I18nPage from 'common/I18nDecorator';
import { Component, Vue } from 'vue-property-decorator';
import Channel from "model/common/Channel";
import AutoFixInput from 'cmp/autofixinput/AutoFixInput';
import SystemConfigApi from "http/systemConfig/SystemConfigApi";
import UrlParamUtil from "util/UrlParamUtil";
import BMemberBalanceAccountLimitConfig from 'model/systemConfig/BMemberBalanceAccountLimitConfig'
import BMemberSingleRechargeLimitConfig from 'model/systemConfig/BMemberSingleRechargeLimitConfig'
import BMemberSingleRechargeLimitInfo from 'model/systemConfig/BMemberSingleRechargeLimitInfo'
import RSChannelManagement from "model/common/RSChannelManagement";
import RSChannelManagementFilter from "model/common/RSChannelManagementFilter";
import ChannelManagement<PERSON><PERSON> from "http/channelmanagement/ChannelManagementApi";
import AmountToFixUtil from 'util/AmountToFixUtil'


//页面使用
class FormItem {
    rechargeChannels: Channel[] = []  //充值渠道
    hasCardholder: boolean = true //是否有持卡人
    singleRechargeLimit: any = {
      minLimit: {
        hasMinLimit: false, //是否有最小充值金额
        minAmount: null,  //最小充值金额
      },
      maxLimit: {
        hasMaxLimit: false, //是否有最大充值金额
        maxAmount: null //最大充值金额
      }
    }
  }

  
@Component({
    name: 'member-balance-limit-setting-edit',
    components: {
      BreadCrume,
      ChannelSelect,
      AutoFixInput
    }
  })
  @I18nPage({
    prefix: [
      '/公用/券模板',
      '/卡/卡充值限额设置',
      '/公用/表单校验',
      '/公用/按钮'
    ],
    auto: true
  })
  export default class MemberBalanceLimitSettingEdit extends Vue {
    $refs: any
    panelArray: any = []
    editType: 'create' | 'edit' = 'create'
    activeName: string;
    ruleForm: any = {
      data: [new FormItem()]
    }
    rules: any = {}
    memberBalanceAccountLimitRule: any = {};
    memberBalanceAccountLimitConfig : BMemberBalanceAccountLimitConfig = new BMemberBalanceAccountLimitConfig();
    memberSingleRechargeLimitConfig : BMemberSingleRechargeLimitConfig = new BMemberSingleRechargeLimitConfig();
    channels: RSChannelManagement[];
    channelMap: Map<string, RSChannelManagement> = new Map<string, RSChannelManagement>();
  
    created() {
      this.activeName = UrlParamUtil.get("activeName")?UrlParamUtil.get("activeName"):"memberSingleRechargeLimit";
      this.editType = this.$route.query.editType === 'edit' ? 'edit' : 'create'
      this.panelArray = [
        {
          name: this.i18n('储值限额设置'),
          url: ''
        }
      ]
      this.initRules();
      this.initData();
    }
  
    handleTabClick() {
        if(this.activeName == "memberBalanceAccountLimit"){
            this.getBalanceAccountLimitConfig();
        }else if(this.activeName == "memberSingleRechargeLimit") {
            this.getSingleRechargeLimitConfig();
        }
        UrlParamUtil.store("activeName", this.activeName);  
    }

    getBalanceAccountLimitConfig() {
        SystemConfigApi.getBalanceAccountLimitConfig()
            .then((res) => {
            if (res.data) {
                this.memberBalanceAccountLimitConfig.maxAmount = AmountToFixUtil.formatAmount(res.data.maxAmount, ********, 0.01, '');
            }})
            .catch((rej) => {
            this.$message.error(rej.message);
        });
    }

    getSingleRechargeLimitConfig() {
        SystemConfigApi.getSingleRechargeLimitConfig()
            .then((res) => {
            if (res.data) {
                this.memberSingleRechargeLimitConfig = res.data;
                this.doBindValue();
            }})
            .catch((rej) => {
            this.$message.error(rej.message);
        });
    }

  doBindValue() {
    this.ruleForm.data = []
    this.memberSingleRechargeLimitConfig.infos.forEach((item) => {
      const ruleFormItem = new FormItem()
      ruleFormItem.rechargeChannels = item.channels as any || []
     
      if (item?.minAmount) {
        ruleFormItem.singleRechargeLimit.minLimit.hasMinLimit = true
        ruleFormItem.singleRechargeLimit.minLimit.minAmount = item.minAmount
      }
      if (item?.maxAmount) {
        ruleFormItem.singleRechargeLimit.maxLimit.hasMaxLimit = true
        ruleFormItem.singleRechargeLimit.maxLimit.maxAmount = item.maxAmount
      }
      this.ruleForm.data.push(ruleFormItem)
    })
  }

    private getChannels() {
        let param: RSChannelManagementFilter = new RSChannelManagementFilter();
        ChannelManagementApi.query(param)
          .then((resp: any) => {
            if (resp && resp.code === 2000) {
              this.channels = resp.data;
              for (let channel of this.channels) {
                if (channel.channel && channel.channel.type && channel.channel.id) {
                    this.channelMap.set(this.getKey(channel.channel) as string, channel);
                }
              }
            }
          })
          .catch((error: any) => {
            if (error && error.message) {
              this.$message.error(error.message);
            }
          });
      }


      private getKey(channel: Channel) {
        if (channel && channel.type && channel.id) {
          return (channel.type as any) + channel.id;
        }
        return channel.typeId;
      }

    doAdd() {
      this.ruleForm.data.push(new FormItem())
    }
  
    doRemove(index: number) {
      this.ruleForm.data.splice(index, 1)
    }
  
    //其他组已经选择了的充值渠道
    selectedChannels(index: number) {
      const arr: string[] = []
      this.ruleForm.data.forEach((item: FormItem, ind: number) => {
        if (index == ind) return  //当前组已经选择的，不用过滤
        if (item.hasCardholder !== this.ruleForm.data[index].hasCardholder) return
        item.rechargeChannels.forEach((channel) => arr.push(channel.type! + channel.id!))
      })
      return arr
    }
  
    doSave() {
        if(this.activeName == "memberBalanceAccountLimit"){
            Promise.all([
                this.$refs.memberBalanceAccountLimitConfig.validate(),
              ]).then(async (valid: any) => {
                SystemConfigApi.saveBalanceAccountLimitConfig(this.memberBalanceAccountLimitConfig)
                .then((res) => {
                  this.$message.success("保存成功");
                  this.doToDtl();
                })
                .catch((rej) => {
                  this.$message.error(rej.message);
                }); 
            }); 
        }else if(this.activeName == "memberSingleRechargeLimit"){
            this.$refs.form.validate().then(() => {
                const params = this.doParams(this.ruleForm.data)
                SystemConfigApi.saveSingleRechargeLimitConfig(params).then((res) => {
                  if (res.code === 2000) {
                    this.$message.success(this.i18n('保存成功'))
                    this.doToDtl();
                  } else {
                    throw new Error(res.msg!)
                  }
                }).catch((error) => this.$message.error(error.message))
              })

        }else{
            this.$message.success("不支持的保存类型");
        }

        
    }
  
    doCancel() {
      this.doToDtl()
    }
  
    doToDtl() {
      this.$router.push({
        name: 'member-balance-limit-setting'
      })
    }
  
    //在切换“是否有持卡人”字段时，如果其他设置组的“充值渠道”存在与当前组“充值渠道”重复的已选项（且“是否有持卡人”字段也相同），则需要清空当前充值渠道
    doCardholderChange(index: number) {
      const currentChannel = this.ruleForm.data[index].rechargeChannels
      const flag = this.ruleForm.data.some((item: FormItem, ind: number) => {
        if (index === ind || item.hasCardholder !== this.ruleForm.data[index].hasCardholder) return false
        if (item.rechargeChannels.some((val) => { //判断是否有重复的充值渠道
          return currentChannel.some((v: Channel) => {
            return (val.id! + val.type!) === (v.id! + v.type!)
          })
        })) {
          return true
        }
      })
      if (flag) {
        this.$message.warning(this.i18n('"是否有持卡人"相同时，充值渠道不能与其他设置重复'))
        this.ruleForm.data[index].rechargeChannels = []
      }
    }
  
    doCheckLimitChange(index: number, mode: 'min' | 'max') {
      if (mode === 'max' && !this.ruleForm.data[index].singleRechargeLimit.maxLimit.hasMaxLimit) {
        this.ruleForm.data[index].singleRechargeLimit.maxLimit.maxAmount = null
      }
      if (mode === 'min' && !this.ruleForm.data[index].singleRechargeLimit.minLimit.hasMinLimit) {
        this.ruleForm.data[index].singleRechargeLimit.minLimit.minAmount = null
      }
      this.$refs.form.validateField(`data[${index}].singleRechargeLimit`)
      this.$refs.form.validateField(`data[${index}].singleRechargeLimit.maxLimit`)
      this.$refs.form.validateField(`data[${index}].singleRechargeLimit.minLimit`)
    }
  
    doParams(value: FormItem[]) {
      const params: BMemberSingleRechargeLimitConfig = new BMemberSingleRechargeLimitConfig()
      value.forEach((item, index) => {
        const resItem = new BMemberSingleRechargeLimitInfo()
        resItem.channels = item.rechargeChannels;
        resItem.maxAmount = item.singleRechargeLimit.maxLimit.hasMaxLimit ? item.singleRechargeLimit.maxLimit.maxAmount : null;
        resItem.minAmount = item.singleRechargeLimit.minLimit.hasMinLimit ? item.singleRechargeLimit.minLimit.minAmount : null; 
        params.infos.push(resItem)
      })
      return params
    }

    private initData(){
        let param: RSChannelManagementFilter = new RSChannelManagementFilter();
        ChannelManagementApi.query(param)
          .then((resp: any) => {
            if (resp && resp.code === 2000) {
              this.channels = resp.data;
              for (let channel of this.channels) {
                if (channel.channel && channel.channel.type && channel.channel.id) {
                    this.channelMap.set(this.getKey(channel.channel) as string, channel);
                }
              }
              this.getSingleRechargeLimitConfig();
              this.getBalanceAccountLimitConfig();
            }
          })
          .catch((error: any) => {
            if (error && error.message) {
              this.$message.error(error.message);
            }
          });      
      }
  
    initRules() {
      this.rules = {
        rechargeChannels: {
          validator: (rule: any, value: Channel[], callback: any) => {
            if (!value?.length) {
              callback(new Error(this.i18n("请填写必填项")));
            }
            callback();
          },
          required: true,
          trigger: ["change", "blur"],
        },
        singleRechargeLimit: {
          validator: (rule: any, value: any, callback: any) => {
            if (!value.minLimit.hasMinLimit && !value.maxLimit.hasMaxLimit) {
              callback(new Error(this.i18n("请至少勾选一种限额")));
            }
            if (value.minLimit.hasMinLimit && value.maxLimit.hasMaxLimit && Number(value.minLimit.minAmount) > Number(value.maxLimit.maxAmount)) {
              callback(new Error(this.i18n('最小金额不能大于最大金额')))
            }
            callback();
          },
          required: true,
          trigger: ["change", "blur"],
        },
        minAmount: {
          validator: (rule: any, value: any, callback: any) => {
            if (value.hasMinLimit && !value.minAmount) {
              callback(new Error(this.i18n("请填写必填项")))
            }
            callback();
          },
          trigger: ["change", "blur"],
        },
        maxAmount: {
          validator: (rule: any, value: any, callback: any) => {
            if (value.hasMaxLimit && !value.maxAmount) {
              callback(new Error(this.i18n("请填写必填项")))
            }
            callback();
          },
          trigger: ["change", "blur"],
        }
      }
      this.memberBalanceAccountLimitRule = {
        
      };
    }
  };