<template>
    <el-dialog :before-close="doBeforeClose" :close-on-click-modal="false"
               :title="title"
               :visible.sync="dialogShow" append-to-body class="edit-tag-info-dialog">
        <div class="wrap">
            <div class="tag-selected">
                <el-tag
                        :key="index"
                        @close="doTagClose(index, item)"
                        closable
                        effect="plain" style="margin-right: 15px;margin-bottom: 15px"
                        type="info"
                        v-for="(item, index) in selectedTags">{{item.tagName}}:{{item.tagValue}}
                </el-tag>
                <el-tag
                        :key="index"
                        @close="doRadioTagClose(index, item)"
                        closable
                        effect="plain" style="margin-right: 15px;margin-bottom: 15px"
                        type="info"
                        v-for="(item, index) in selectedRadio">{{item.tagName}}:{{item.tagValue}}
                </el-tag>
                <el-tag
                        :key="index"
                        @close="doInputTagClose(index, item)"
                        closable
                        effect="plain" style="margin-right: 15px;margin-bottom: 15px"
                        type="info"
                        v-for="(item, index) in selectInput">{{item.tagId}}:{{item.tagValues? item.tagValues.toString(): '-'}}
                </el-tag>
            </div>
            <div style="margin: 20px">
                <FormItem :label="item.tagId" v-for="(item, index) in tags" :title="item.tagId" labelWidth="170px" :key="index">
                    <div v-if="item.tagType === 'checkbox'">
                        <el-checkbox
                            @change="doTagSelected(index, subIndex)"
                            v-for="(subItem, subIndex) in item.tagValues"
                            :key="subItem + subIndex"
                            v-model="tagSelect[index][subIndex]"
                            ><span class="sub-item" :title="subItem">{{subItem}}</span>
                        </el-checkbox>
                    </div>
                    <div v-else-if="item.tagType === 'singleChoice'">
                        <el-radio-group v-model="tagSelect[index]" @change="doRadioSelected(index, tagSelect[index])">
                            <el-radio :label="subItem" v-for="(subItem, subIndex) in item.tagValues" :key="'radio' + subIndex">
                                <span class="sub-item" :title="subItem">{{subItem}}</span>
                            </el-radio>
                        </el-radio-group>
                    </div>
                    <div v-else-if="item.tagType === 'date'">
                        <el-date-picker
                            v-model="item.tagValues[0]"
                            @change="doInputSelect(index, item, item.tagValues[0])"
                            type="date"
                            value-format="yyyy-MM-dd"
                            placeholder="选择日期">
                        </el-date-picker>
                    </div>
                    <div v-else-if="item.tagType === 'text'">
                        <el-input
                            @change="doInputSelect(index, item, item.tagValues[0])"
                            style="width: 220px"
                            v-model="item.tagValues[0]"
                            :maxlength="10"
                            placeholder="填写文本">
                        </el-input>
                    </div>
                    <div v-else-if="item.tagType === 'number'">
                        <AutoFixInput
                            @change="doInputSelect(index, item, item.tagValues[0])"
                            :min="-99999.99"
                            :max="99999.99"
                            :fixed="2"
                            style="width: 220px"
                            v-model="item.tagValues[0]"
                            placeholder="填写数字"
                        ></AutoFixInput>
                    </div>
                </FormItem>
            </div>
        </div>
        <div class="dialog-footer" slot="footer">
            <el-button @click="doCancel">取消</el-button>
            <el-button @click="doModalClose" size="small" type="primary">确定</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts" src="./EditTagInfoDialog.ts">
</script>

<style lang="scss">
.edit-tag-info-dialog{
    display: flex;
    align-items: center;
    justify-content: center;
    .wrap{
        height: 420px;
        overflow: auto;
    }
    .el-dialog{
        width: 1000px;
        height: 600px;
    }
    .tag-selected{
        border-bottom: 1px solid #cccccc;
        padding: 10px 0;
    }
    .qf-form-label {
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
    .qf-form-item .qf-form-content{
        line-height: 36px !important;
        // margin-left: 120px !important;
    }
    .el-checkbox{
        margin-right: 0 !important;
    }
    .sub-item{
        display: inline-block;
        width: 140px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        /* line-height: 11px; */
        position: relative;
        top: 6px;
    }
}
</style>