<template>
  <div class="get-goods-range">
    <div>
      <span> {{i18n('赠品') }}</span>
      <span class="plain-btn-blue" @click="doAdd" v-if="data.length <= 5" style="border: 0px">
        {{ '+' + i18n('添加赠品') }}
      </span>
    </div>
    <div>
      <el-table v-if="data.length > 0" :data="data" style="width: 100%" stripe>
        <el-table-column :label="i18n('商品')" fixed prop="goods">
          <template slot-scope="scope">
            <template>[{{scope.row.goods.id}}]{{scope.row.goods.name}}</template>
          </template>
        </el-table-column>
        <el-table-column :label="formatI18n('/公用/券模板/提货券/用券商品', '售价(元)')" fixed prop="price">
          <template slot-scope="scope">
            <div v-if="scope.row.price">￥{{scope.row.price | fmt}}</div>
            <div v-else>--</div>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('商品数量(件)')" fixed prop="qty">
          <template slot-scope="scope">
            <div style="display: flex; justify-content: flex-start;">
              <el-input style="width: 80px" class="number-type" v-model="scope.row.qty" type="number" @change="doChange(scope.$index)">
              </el-input>
              <el-select v-model="scope.row.isDisp" style="width: 70px" @change="doChange(scope.$index)">
                <el-option v-for="item in unitList" :key="item.label" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="formatI18n('/设置/权限/角色管理/包含用户','操作')" fixed prop="option">
          <template slot-scope="scope">
            <el-button type="text" @click="doClear(scope.$index)">{{formatI18n('/公用/按钮','清空')}}</el-button>
            <el-button type="text" @click="doDelete(scope.$index)">{{formatI18n('/公用/按钮','删除')}}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <GoodsSelectorDialog ref="selectGoodsScopeDialog"
                         :appreciationGoods="appreciationGoods" :chooseGoodType="chooseGoodType"
                         :goodsMatchRuleMode="goodsMatchRuleMode" @summit="doSubmitGoods"/>
  </div>
</template>

<script lang="ts" src="./GetGiftGoodsRange.ts">
</script>

<style lang="scss" scoped>
.get-goods-range {
  width: 740px;
  div {
    border: none;
  }
}
</style>