import GoodsRange from "model/common/GoodsRange";
import CardBalancePromotionActivity, { Strategy } from "model/payment/member/MemberBalancePromotionActivityNew";
import StoreRange from "model/common/StoreRange";
import ActivityBody from "model/common/ActivityBody";
import DateUtil from "util/DateUtil";
import RSGrade from "model/common/RSGrade";
import IdName from "model/common/IdName";
import GradeStepValue from "model/common/GradeSameReductionNew";
import GradeStepValues from "model/common/GradeStepValues";
import { CardType } from "model/payment/member/CardType";
import ActivityDateTimeCondition from "model/common/ActivityDateTimeCondition";


class CardBalancePromotionFormData {
	// 活动id
	activityId: Nullable<string> = null;
	// 名称
	name: Nullable<string> = null;
	// 活动主题代码
	topicCode: Nullable<string> = null;
	// 活动时间
	timeRange: Date[] = [];
	// 活动门店
	stores: Nullable<StoreRange> = new StoreRange();
	// 商品范围
	goods: Nullable<GoodsRange> = new GoodsRange();
	// 卡模板
	cardTemplates: IdName[] = [];
	favThresholdLimit = false;
	favThreshold: Nullable<number> = null;
	// 立减策略
	strategy: Nullable<Strategy> = "BY_AMOUNT";
	// 优惠形式
	favType: Nullable<"stairs" | "step"> = "stairs";
	// 优惠规则
	favRule: Nullable<"gradeSame" | "gradeDiff"> = "gradeSame";
	// 不同等级规则
	gradeDifferentStepValue: GradeStepValues[] = [];
	// 相同等级规则
	gradeSameStepValue: GradeStepValues = new GradeStepValues();
	// 是否参与前台促销
	excludePromotion: Nullable<boolean> = false;
	 // 
	cardType: CardType = CardType.none
  // 每天卡最大使用次数
  maxDailyCardJoinTime: Nullable<number> = null
  // 卡最大使用次数
  maxCardJoinTimes: Nullable<number> = null
  // 活动最大次数
  maxActivityTimes: Nullable<number> = null
    	// 活动时间和时间限制
	activityDateTimeCondition = new ActivityDateTimeCondition();
}

export default class CardBalancePromotionForm {
	data: CardBalancePromotionFormData = new CardBalancePromotionFormData();
	rules: any;
	amountRules: any;
	qtyRules: any;
	discountRules: any;
	init(master: any) {
		this.rules = {
			name: [
				{ required: true, message: master.i18n("请输入活动名称"), trigger: ["change", "blur"] },
				{ min: 1, max: 80, message: master.i18n("长度在80个字符以内"), trigger: ["change", "blur"] },
			],
			favRule: [
				{
					validator: (rule: any, value: StoreRange, callback: any) => {
						callback();
					},
					trigger: ["change", "blur"],
				},
			],
			favThreshold: [
				{
					validator: (rule: any, value: any, callback: any) => {
						if (this.data.favThresholdLimit && !value) {
							callback(master.i18n("/公用/js提示信息/请填写必填项"));
						}
						callback();
					},
					trigger: ["change", "blur"],
				},
			],
			// cardTemplates: [
			// 	{
			// 		validator: (rule: any, value: IdName[], callback: any) => {
			// 			if (value.length === 0) {
			// 				callback(master.i18n("请选择卡模板"));
			// 			}
			// 			callback();
			// 		},
			// 		trigger: ["change", "blur"],
			// 	},
			// ],
			gradeDifferentStepValue: [
				{
					validator: (rule: any, value: any[], callback: any) => {
						if (value.filter((e) => e.checked).length === 0) {
							callback(master.i18n("/公用/js提示信息/请填写必填项"));
						}
						callback();
					},
					trigger: ["change", "blur"],
				},
			],
		};

		this.amountRules = [{ required: true, message: master.i18n("请输入金额"), trigger: ["change", "blur"] }];
		this.discountRules = [{ required: true, message: master.i18n("请输入折扣"), trigger: ["change", "blur"] }];

		this.qtyRules = [{ required: true, message: master.i18n("请输入数量"), trigger: ["change", "blur"] }];
	}

	toParams() {
		let formData: CardBalancePromotionFormData = JSON.parse(JSON.stringify(this.data));
		let params = new CardBalancePromotionActivity();
		params.body = new ActivityBody();
		params.body.name = formData.name;
		params.body.topicCode = formData.topicCode;
		// 活动时间
		params.dateTimeCondition = formData.activityDateTimeCondition.dateTimeCondition
		params.body.beginDate = formData.activityDateTimeCondition.beginDate
		params.body.endDate = formData.activityDateTimeCondition.endDate
		params.body.stores = formData.stores;
    	params.maxDailyCardJoinTime = formData.maxDailyCardJoinTime
    	params.maxCardJoinTimes = formData.maxCardJoinTimes
    	params.maxActivityTimes = formData.maxActivityTimes
		params.goods = formData.goods;
		params.cardTemplates = formData.cardTemplates;
		params.strategy = formData.strategy;
		params.goods = formData.goods;
		params.strategy = formData.strategy;
		params.excludePromotion = formData.excludePromotion;
		params.cardType = formData.cardType
		if (formData.favThresholdLimit) {
			params.favThreshold = formData.favThreshold;
		}
		if (formData.favType === "stairs" && formData.favRule === "gradeSame") {
			params.gradeSameStepValue = formData.gradeSameStepValue;
		} else if (formData.favType === "stairs" && formData.favRule === "gradeDiff") {
			params.gradeDifferentStepValue = formData.gradeDifferentStepValue.filter((e) => e.checked);
		}
		return params;
	}

	of(activity: CardBalancePromotionActivity, gradeList: RSGrade[]) {
		if (!activity || !activity.body) {
			return;
		}
		this.data.name = activity.body.name;
		this.data.stores = activity.body.stores;
    	this.data.maxDailyCardJoinTime = activity.maxDailyCardJoinTime
    	this.data.maxCardJoinTimes = activity.maxCardJoinTimes
    	this.data.maxActivityTimes = activity.maxActivityTimes
 		this.data.activityDateTimeCondition.beginDate = activity.body.beginDate
		this.data.activityDateTimeCondition.endDate = activity.body.endDate
		this.data.activityDateTimeCondition.dateTimeCondition = activity.dateTimeCondition 
		this.data.goods = activity.goods;
		this.data.cardTemplates = activity.cardTemplates;
		this.data.strategy = activity.strategy;
		this.data.excludePromotion = activity.excludePromotion;
		this.data.cardType = activity.cardType
		if (activity.favThreshold) {
			this.data.favThresholdLimit = true;
			this.data.favThreshold = activity.favThreshold;
		} else {
			this.data.favThresholdLimit = false;
		}
		if (activity.gradeSameStepValue) {
			this.data.favType = "stairs";
			this.data.favRule = "gradeSame";
			this.data.gradeSameStepValue = activity.gradeSameStepValue;
		} else if (activity.gradeDifferentStepValue) {
			this.data.favType = "stairs";
			this.data.favRule = "gradeDiff";
			this.data.gradeDifferentStepValue = [];
			for (let grade of gradeList) {
				let filterReduction = activity.gradeDifferentStepValue.filter((e) => e.grade === grade.code);
				let result = new GradeStepValue();
				if (filterReduction.length > 0) {
					result.grade = grade.code;
					result.gradeName = grade.name;
					result.checked = true;
					result.stepValues = filterReduction[0].stepValues;
					// result.threshold = filterReduction[0].threshold
					// result.value = filterReduction[0].value
				} else {
					result.grade = grade.code;
					result.gradeName = grade.name;
					result.checked = false;
					result.stepValues = [];
					// result.threshold = null
					// result.value = null
				}
				this.data.gradeDifferentStepValue.push(result);
			}
		}
	}
}
