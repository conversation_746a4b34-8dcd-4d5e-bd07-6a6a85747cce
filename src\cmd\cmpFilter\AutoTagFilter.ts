/*
 * @Author: 黎钰龙
 * @Date: 2025-01-06 20:02:16
 * @LastEditTime: 2025-02-14 15:05:12
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmd\cmpFilter\AutoTagFilter.ts
 * 记得注释
 */
import ConstantMgr from "mgr/ConstantMgr"
import { RFMValue } from "model/autoTag/RFMValue"
import { TagCalculatorTaskState } from "model/autoTag/TagCalculatorTaskState"
import { TagCalculatorTaskType } from "model/autoTag/TagCalculatorTaskType"
import { TagTypeEnum } from "model/common/TagTypeEnum"
import Vue from "vue"

export default class AutoTagFilter {
  static init() {
    // 智能打标 创建方式
    Vue.filter("autoTagCreateMethod", function (val: TagCalculatorTaskType) {
      switch (val) {
        case TagCalculatorTaskType.hierarchical_tag:
          return new ConstantMgr.MenusFuc().format("/会员/智能打标", "分层标签")
        case TagCalculatorTaskType.preference_tag:
          return new ConstantMgr.MenusFuc().format("/会员/智能打标", "偏好类标签")
        case TagCalculatorTaskType.first_and_last_tag:
          return new ConstantMgr.MenusFuc().format("/会员/智能打标", "首末次特征标签")
        case TagCalculatorTaskType.stats_tag:
          return new ConstantMgr.MenusFuc().format("/会员/智能打标", "统计标签")
        case TagCalculatorTaskType.rfm_tag:
          return new ConstantMgr.MenusFuc().format("/会员/智能打标", "RFM标签")
        default:
          return "--"
      }
    })
    // 智能打标 标签类型
    Vue.filter("tagType", function (val: TagTypeEnum) {
      switch (val) {
        case TagTypeEnum.checkbox:
          return new ConstantMgr.MenusFuc().format("/会员/选择人群", "多选")
        case TagTypeEnum.singleChoice:
          return new ConstantMgr.MenusFuc().format("/会员/选择人群", "单选")
        case TagTypeEnum.date:
          return new ConstantMgr.MenusFuc().format("/会员/标签客群/标签", "日期")
        case TagTypeEnum.text:
          return new ConstantMgr.MenusFuc().format("/会员/标签客群/标签", "文本")
        case TagTypeEnum.number:
          return new ConstantMgr.MenusFuc().format("/会员/标签客群/标签", "数值")
        default:
          return '--'
      }
    })
    // 智能打标 任务状态
    Vue.filter("autoTagTaskState", function (val: TagCalculatorTaskState) {
      switch (val) {
        case TagCalculatorTaskState.initial:
          return new ConstantMgr.MenusFuc().format("/会员/智能建群", "待审核")
        case TagCalculatorTaskState.running:
          return new ConstantMgr.MenusFuc().format("/会员/智能建群", "正常运行")
        case TagCalculatorTaskState.stopped:
          return new ConstantMgr.MenusFuc().format("/会员/智能建群", "已停止")
        default:
          return "--"
      }
    })
    // 智能打标 R\F\M层级
    Vue.filter("RFMlevel", function (val: RFMValue) {
      switch (val) {
        case RFMValue.HIGH:
          return new ConstantMgr.MenusFuc().format("/会员/智能打标", "高")
        case RFMValue.MIDDLE:
          return new ConstantMgr.MenusFuc().format("/会员/智能打标", "中")
        case RFMValue.LOW:
          return new ConstantMgr.MenusFuc().format("/会员/智能打标", "低")
        default:
          return "--"
      }
    })
  }
}