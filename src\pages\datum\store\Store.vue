<template>
  <div class="store-view">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button size="large" type="primary" icon="el-icon-plus" @click="doAddShop" v-if="hasOptionPermission('/设置/资料/门店', '资料维护')">
          {{ formatI18n('/资料/门店/新增门店') }}
        </el-button>
        <el-button size="large" @click="$refs.uploadFileModal.show(marketingCenters,enableMultiMarketingCenter)"
          v-if="hasOptionPermission('/设置/资料/门店', '资料维护')">
          {{ formatI18n('/资料/门店/导入门店') }}
        </el-button>
        <el-button size="large" @click="$refs.uploadFileModalPlatform.show()" v-if="hasOptionPermission('/设置/资料/门店', '资料维护')">
          {{ formatI18n('/资料/门店/导入渠道门店') }}
        </el-button>
        <el-button size="large" v-if="hasOptionPermission('/设置/资料/门店', '批量导出')" @click="exportFile">{{ formatI18n('/资料/门店/批量导出') }}</el-button>
      </template>
    </BreadCrume>
    <div class="current-page">
      <el-tabs v-model="activeTab" @tab-click="doChangeTab">
        <el-tab-pane :label="formatI18n('/资料/区域/全部')" name="all"></el-tab-pane>
        <el-tab-pane :label="item.platForm.name" :name="item.channelType" v-for="item in platformList" :key="item.channelType"></el-tab-pane>
      </el-tabs>
      <div class="page-content">
        <div class="table-query">
          <el-row style="height: 32px;" v-if="activeTab == 'all'">
            <el-col :span="8">
              <span style="margin-right:8px">
                <i18n k="/资料/门店/已选择{0}家门店">
                  <template slot="0">
                    <span class="number-text">{{ selected.length }}</span>
                  </template>
                </i18n>
              </span>
              <el-button v-if="hasOptionPermission('/设置/资料/门店', '资料维护')" @click="showUpdateCenterDialog">
                {{ formatI18n('/资料/门店/修改所属区域') }}
              </el-button>
            </el-col>
            <el-col :span="6" class="flexCE" style="padding-right: 30px;">
              <el-checkbox v-model="showNoMarketingCenter" @change="doChangeCenter">
                {{ formatI18n('/资料/门店/只看无所属区域的门店') }}
              </el-checkbox>
            </el-col>
            <el-col :span="10" class="flexCE">
              <el-select @change="doShopSearch" v-model="areaSelectItem" :placeholder="formatI18n('/资料/渠道/请选择')" style="width: 280px">
                <el-option :value="null" :label="formatI18n('/资料/门店/所属区域不限')">{{ formatI18n('/资料/门店/所属区域不限')
                }}</el-option>
                <el-option v-for="(value) in areaData" :key="value.zone.id" :value="value.zone.id"
                  :label="'[' + value.zone.id + ']' + value.zone.name">[{{ value.zone.id }}]{{ value.zone.name
                  }}</el-option>
              </el-select>
              <el-input :placeholder="formatI18n('/资料/门店/搜索门店代码\\/名称')" @change="doShopSearch" v-model="query.idNameLikes"
                suffix-icon="el-icon-search" style="width: 280px" />
              <el-select @change="doStateSearch" v-model="orgStateItem" :placeholder="formatI18n('按门店状态')" style="width: 280px">
                <el-option :label="i18n('/营销/运营/推送计划/列表页/全部')" :value="null"></el-option>
                <el-option v-for="item in OrgState" :key="item" :label="i18n(OrgStateMap[item])" :value="item"></el-option>
              </el-select>

            </el-col>
          </el-row>
          <el-row style="height: 32px;" v-else>
            <el-col :span="24" class="flexCS">
              <div class="col-label">
                {{ formatI18n('/资料/门店/门店') }}：
              </div>
              <el-input :placeholder="formatI18n('/储值/预付卡/预付卡查询/列表页面/门店代码/名称')" style="width: 180px;" v-model="queryPlat.idNameLikes">
              </el-input>
              <div class="col-label">
                {{ formatI18n('/资料/门店/渠道门店ID') }}：
              </div>
              <el-input :placeholder="formatI18n('/会员/洞察/公共/操作符/等于')" style="width: 180px;" v-model="queryPlat.channelStoreIdEquals">
              </el-input>
              <div class="col-label">
                {{ formatI18n('/资料/渠道/渠道ID') }}：
              </div>
              <el-input :placeholder="formatI18n('/会员/洞察/公共/操作符/等于')" style="width: 180px;" v-model="queryPlat.channelIdEquals">
              </el-input>
              <el-button class="btn-search" type="primary" @click.stop="doPlatSearch"
                style="margin-left: 20px;">{{ formatI18n('/设置/权限/角色管理/查询') }}</el-button>
              <el-button class="btn-reset" @click.stop="doPlatReast">{{ formatI18n('/设置/权限/角色管理/重置') }}</el-button>
            </el-col>
          </el-row>
        </div>
        <el-table v-if="activeTab == 'all'" key="all" v-loading="loading" height="calc(100% - 80px)" ref="storeTable" :data="shopData"
          @select-all="selectAll" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55">
          </el-table-column>
          <el-table-column width="160" :label="formatI18n('/资料/门店/门店代码')" prop="org.id">
            <template slot-scope="scope">
              {{ scope.row.org.id }}

                <el-tag class="tag-block" v-if="scope.row.defaultStore" type="info">
                  {{formatI18n('/资料/门店/默认门店')}}
                </el-tag>
            </template>
          </el-table-column>
          <el-table-column width="200" :label="formatI18n('/资料/门店/门店名称')" prop="org.name" />
          <el-table-column width="200" :label="formatI18n('/资料/门店/所属区域')">
            <template slot-scope="scope">
              <div v-if="scope.row.zoneId">[{{ scope.row.zoneId }}]{{ scope.row.zoneName }}</div>
              <div v-else style="color: red;">{{ formatI18n("/资料/门店/无所属区域") }}</div>
            </template>
          </el-table-column>
          <el-table-column min-width="200" :label="formatI18n('/资料/门店/门店地址')" prop="address">
            <template slot-scope="scope">
              <div v-if="scope.row.address">
                {{ buildStoreAddress(scope.row.address) }}</div>
              <div v-else>{{ '-' }}</div>
            </template>
          </el-table-column>
          <el-table-column width="150" :label="formatI18n('/资料/门店/联系电话')" prop="phoneNumber">
            <template slot-scope="scope">
              <div v-if="scope.row.telephone">
                {{ scope.row.telephone }}
              </div>
              <div v-else>{{ '-' }}</div>
            </template>
          </el-table-column>
          <el-table-column width="300" :label="formatI18n('/公用/券模板/标签')" prop="tags">
            <template slot-scope="scope">
              <template v-if="scope.row.orgTags && scope.row.orgTags.length">
                <el-tag class="tag-block" v-for="item in scope.row.orgTags" :key="item.tagName" type="info">
                  {{item.tagName}}
                </el-tag>
              </template>
              <template v-else>-</template>
            </template>
          </el-table-column>
          <el-table-column width="200" :label="formatI18n('/资料/门店/坐标')" prop="lat,lng">
            <template slot-scope="scope">
              <div>{{ buildStoreCoordinate(scope.row.lat, scope.row.lng) }}</div>
            </template>
          </el-table-column>
          <el-table-column width="200" :label="i18n('渠道类型')" prop="channelTypeName">
            <template slot-scope="scope">
              <div>{{ buildChannelType(scope.row.platOrgInfos) }}</div>
            </template>
          </el-table-column>
          <el-table-column width="200" :label="formatI18n('/资料/门店/门店状态')" prop="orgState">
            <template slot-scope="scope">
              <div :type="handelOrgState(scope.row.orgState)">{{OrgStateMap[scope.row.orgState]}}</div>
            </template>
          </el-table-column>
          <el-table-column width="150" fixed="right" :label="formatI18n('/资料/门店/操作')" v-if="hasOptionPermission('/设置/资料/门店', '资料维护')">
            <template slot-scope="scope">
              <span class="span-btn" style="margin-right:8px" @click.stop="goShopDetails(scope.row)">{{ formatI18n('/会员/会员首页', '查看详情') }}</span>
              <span class="span-btn" style="margin-right:8px" @click.stop="goShopDetailsEdit(scope.row)">{{ formatI18n('/资料/门店/修改') }}</span>
              <span class="span-btn" v-if="handelOrgState(scope.row.orgState) === 'disable'" @click.stop="enableShop(scope.row)">{{ formatI18n('/资料/门店/启用') }}</span>
              <span class="span-btn" v-if="handelOrgState(scope.row.orgState) === 'enable'" @click.stop="disableShop(scope.row)">{{ formatI18n('/资料/门店/停用') }}</span>
            </template>
          </el-table-column>
        </el-table>
        <el-table v-else :key="activeTab" v-loading="loading" height="calc(100% - 80px)" ref="platTable" :data="plateData">
          <el-table-column width="200" :label="i18n('渠道类型')" prop="platformName">
          </el-table-column>
          <el-table-column width="200" :label="i18n('渠道ID')" prop="channelId">
          </el-table-column>
          <el-table-column width="200" :label="i18n('渠道名称')" prop="channelName">
          </el-table-column>
          <el-table-column width="200" :label="formatI18n('/资料/门店/渠道门店ID')" prop="platformStoreId">
          </el-table-column>
          <el-table-column :label="formatI18n('/资料/门店/绑定门店')" prop="orgId">
            <template slot-scope="scope">
              <div>【{{ scope.row.orgId }}】{{ scope.row.orgName }}</div>
            </template>
          </el-table-column>
          
          <el-table-column width="200" v-if="showAuthState" :label="formatI18n('授权状态')">
              <template slot-scope="scope">
                <div v-if="scope.row.authorization">{{formatI18n('已授权')}}</div>
                <div v-if="!scope.row.authorization">{{formatI18n('未授权')}}</div>
              </template>
          </el-table-column>
           <el-table-column width="200" v-if="meiTuanPT && hasOptionPermission('/设置/资料/门店', '资料维护')" :label="formatI18n('/资料/门店/操作')">
              <template slot-scope="scope">
                <div>
                  <span class="span-btn" style="margin-right:8px" @click.stop="revokeAuthorization(scope.row)">{{ formatI18n('解除授权') }}</span>
                </div>
            </template>
          </el-table-column>
           <!-- <el-table-column width="200" v-if="meiTuanShopCatering && hasOptionPermission('/设置/资料/门店', '资料维护')" :label="formatI18n('/资料/门店/操作')">
              <template slot-scope="scope">
                <div>
                  <span class="span-btn" style="margin-right:8px" @click.stop="secureAuthorization(scope.row)">{{ formatI18n('解除授权') }}</span>
                </div>
            </template>
          </el-table-column> -->
        </el-table>
      </div>
      <el-pagination :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
        @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background layout="total, prev, pager, next, sizes,  jumper"
        style="margin:10px 0">
      </el-pagination>
    </div>

    <el-dialog :title="formatI18n('/资料/门店/修改所属区域')" :visible.sync="updateCenterDialogVisible" class="store-dialog-center" width="30%"
      @close="clear('modifyOrgMarketingCenter')">
      <div style="margin: 20px;">
        <el-row style="line-height:30px">
          <el-col :span="7" style="height: 50px; line-height: 30px">{{ formatI18n('/资料/门店/修改所属区域') }}</el-col>
          <el-col :span="10">
            <el-select value-key="id" v-model="updateAreaDataId" :placeholder="formatI18n('/资料/渠道/请选择')" style="width: 280px">
              <el-option :value="null" :label="formatI18n('/资料/门店/空')">{{ formatI18n('/资料/门店/空') }}</el-option>
              <el-option v-for="(value) in areaData" :key="value.zone.id" :value="value.zone"
                :label="'[' + value.zone.id + ']' + value.zone.name">[{{ value.zone.id }}]{{ value.zone.name }}</el-option>
            </el-select>
          </el-col>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelUpdateCenter">{{ formatI18n('/资料/门店/取 消') }}</el-button>
        <el-button type="primary" @click="updateOrgsForMarketingCenter" :loading="modifyLoading">{{ formatI18n('/资料/门店/确 定')}}</el-button>
      </span>
    </el-dialog>
    <UploadFileModal ref="uploadFileModal" @getList="doShopSearch"></UploadFileModal>
    <UploadFileModalPlatform ref="uploadFileModalPlatform" @getList="doPlatSearch"></UploadFileModalPlatform>
    <DownloadCenterDialog :dialogvisiable="fileDialogVisible" :showTip="showTip" @dialogClose="doDownloadDialogClose">
    </DownloadCenterDialog>
  </div>
</template>

<script lang="ts" src="./Store.ts">
</script>

<style lang="scss">
.store-view {
  background-color: white;
  height: 100%;
  width: 100%;

  .current-page {
    padding: 10px;
    height: calc(100% - 60px);
    overflow-y: auto;

    .table-query {
      padding: 8px 22px 24px;

      .flexCS {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        height: 32px;
      }

      .col-label {
        height: 32px;
        font-size: 12px;
        color: #36445a;
        line-height: 32px;
        padding-left: 10px;
      }

      .flexCE {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        height: 32px;
      }
    }

    .tag-block {
      display: inline-block;
      margin-right: 8px;
      &:nth-last-child {
        margin-right: 0;
      }
    }

    .page-content {
      width: 100%;
      height: calc(100% - 77px);
    }
  }

  .store-dialog-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .el-tabs__content {
    width: 0;
    height: 0;
    padding: 0;
  }

  .el-tabs--border-card {
    box-shadow: none;
  }

  .el-range-editor.el-input__inner {
    width: 100%;
  }

  .el-tabs--border-card > .el-tabs__header {
    border-bottom: none;
  }
}
</style>