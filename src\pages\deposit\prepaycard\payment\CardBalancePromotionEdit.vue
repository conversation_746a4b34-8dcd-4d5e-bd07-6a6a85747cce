<template>
  <div class="card-balance-promotion-edit">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button key="1" v-if="permission.editable" @click="doSave(null)" type="primary">保存</el-button>
        <el-button key="2" v-if="permission.editable && permission.auditable" @click="doSaveAudit" type="primary">
          保存并审核
        </el-button>
        <el-button key="3" @click="doCancel">取消</el-button>
      </template>
    </BreadCrume>
    <div class="current-page">
      <el-form :model="form.data" :rules="form.rules" ref="form" label-width="150px">
        <div class="panel">
          <div class="header">
            活动信息
          </div>
          <div class="content">
            <el-form-item label="活动名称" :required="true" prop="name">
              <el-input maxlength="80" placeholder="请输入不超过80个字符" style="width: 350px" v-model="form.data.name"></el-input>
            </el-form-item>
            <el-form-item label="所属主题" prop="topicCode">
              <el-select clearable placeholder="请选择" style="width: 350px" v-model="form.data.topicCode">
                <el-option no-i18n :label="item.name" :value="item.code" :key="item.code" v-for="item in themes">{{ item.name }}
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="活动时间" :required="true">
              <ActivityDateTimeConditionPicker v-model="form.data.activityDateTimeCondition" ref="activityDateTimeConditionPicker">
                  </ActivityDateTimeConditionPicker>
              <!-- <el-date-picker type="daterange" range-separator="-" :picker-options="dateRangeOption" v-model="form.data.timeRange"
                :start-placeholder="i18n('开始日期')" :end-placeholder="i18n('结束日期')">
              </el-date-picker> -->
            </el-form-item>
            <el-form-item label="活动门店" :required="true">
              <ActiveStore no-i18n ref="storeScope" :enableStore="true" :sameStore="false" v-model="form.data.stores" />
            </el-form-item>
            <el-form-item :label="i18n('/营销/券礼包活动/券礼包活动/活动次数限制')">
              <div class="gray-tips">- {{i18n('留空，均表示不限制')}}</div>
              <div>
                <i18n k="/储值/预付卡/预付卡支付优惠/单卡限参与{0}次，单卡单日限参与{1}次，活动总限制{2}次">
                  <template slot="0">
                    <AutoFixInput :min="1" :max="99999999" :fixed="0" v-model="form.data.maxCardJoinTimes" style="width: 98px" />
                  </template>
                  <template slot="1">
                    <AutoFixInput :min="1" :max="99999999" :fixed="0" v-model="form.data.maxDailyCardJoinTime" style="width: 98px" />
                  </template>
                  <template slot="2">
                    <AutoFixInput :min="1" :max="99999999" :fixed="0" v-model="form.data.maxActivityTimes" style="width: 98px" />
                  </template>
                </i18n>
              </div>
            </el-form-item>
          </div>
        </div>

        <div class="split"></div>

        <div class="panel">
          <div class="header">
            支付优惠规则
          </div>
          <div class="content">
            <el-form-item prop="cardTemplates">
              <div slot="label">
                <span style="color: red">*</span>
                {{i18n('适用卡模板')}}
              </div>
              <select-card-template ref="selectCardTemplate" :card-templates-prop="form.data.cardTemplates" :card-type-prop="form.data.cardType"
                @change="selectCardChange"></select-card-template>
            </el-form-item>
            <el-form-item :label="i18n('适用商品')" :required="true" prop="goods">
              <GoodsScopeEx no-i18n v-model="form.data.goods" :goodsMatchRuleMode="goodsMatchRuleMode" ref="goodsScope" />
            </el-form-item>
            <el-form-item label="优惠门槛" :required="true">
              <el-row class="favor-row">
                <el-radio v-model="form.data.favThresholdLimit" :label="false" @change="changeFavThresholdLimit">{{ i18n('不限制') }}</el-radio>
              </el-row>
              <el-row class="favor-row">
                <el-radio v-model="form.data.favThresholdLimit" :label="true" @change="changeFavThresholdLimit"
                  style="display: flex;align-items: center">
                  <i18n advance k="/储值/预付卡/预付卡支付活动/编辑页面/适用商品消费满{0}元及以上" style="display: flex;align-items: center">
                    <template slot-scope="{items}">
                      <div class="text">{{ items.s0.prefix }}&nbsp;&nbsp;</div>
                      <el-form-item prop="favThreshold">
                        <AutoFixInput :disabled="!form.data.favThresholdLimit" v-model="form.data.favThreshold" style="width: 150px" :min="0.01"
                          :max="99999999.00" :fixed="2"></AutoFixInput>
                      </el-form-item>
                      <div class="text">&nbsp;&nbsp;{{ items.s0.suffix }}</div>
                    </template>
                  </i18n>
                </el-radio>
              </el-row>
            </el-form-item>
            <el-form-item label="优惠计算" :required="true" prop="newStrategy">
              <el-radio-group v-model="form.data.newStrategy" @change="changeStrategy">
                <el-radio label="BY_FULL_AMOUNT">{{ i18n('商品满金额设置') }}</el-radio>
                <el-radio label="BY_FULL_QTY">{{ i18n('商品满数量设置') }}</el-radio>
                <el-radio label="BY_AMOUNT">{{ i18n('商品每满金额设置') }}</el-radio>
                <el-radio label="BY_QTY">{{ i18n('商品每满数量设置') }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="优惠规则设置">
              <el-radio-group v-model="form.data.favRule" v-if="gradeList.length > 0">
                <el-radio label="gradeSame">{{ i18n('不同等级会员适用 相同规则') }}</el-radio>
                <el-radio label="gradeDiff">{{ i18n('不同等级会员适用 不同规则') }}</el-radio>
              </el-radio-group>
              <template v-if="form.data.favRule === 'gradeSame'">
                <div v-for="(sameItem,index) in form.data.gradeSameStepValue.stepValues" :key="index" style="margin-bottom:15px">
                  <el-row>
                    <i18n advance :k="sameGradeRuleStr">
                      <template slot-scope="{items}">
                        <div style="float: left">{{ items.s0.prefix }}</div>
                        <el-form-item :rules="['BY_AMOUNT','BY_FULL_AMOUNT'].indexOf(form.data.newStrategy) > -1 ? form.amountRules : form.qtyRules"
                          :prop="`gradeSameStepValue.stepValues[${index}].threshold`" style="width: 80px;float: left;margin: 0 5px">
                          <AutoFixInput style="width: 80px" size="small" @change="refresh"
                            v-if="['BY_AMOUNT','BY_FULL_AMOUNT'].indexOf(form.data.newStrategy) > -1" :min="0.01" :max="999999" :fixed="2"
                            v-model="sameItem.threshold" />
                          <AutoFixInput style="width: 80px" size="small" @change="refresh"
                            v-if="['BY_QTY','BY_FULL_QTY'].indexOf(form.data.newStrategy) > -1" :min="1" :max="999999" :fixed="0"
                            v-model="sameItem.threshold" />
                        </el-form-item>
                        <div style="float: left">{{ items.s0.suffix }}</div>
                        <el-form-item :rules="form.amountRules" :prop="`gradeSameStepValue.stepValues[${index}].value`"
                          style="width: 80px;float: left;margin: 0 5px">
                          <AutoFixInput v-model="sameItem.value" size="small" :min="0.01" :max="999999" :fixed="2" style="width: 80px"></AutoFixInput>
                        </el-form-item>
                        <div style="float: left">{{ items.s1.suffix }}</div>
                      </template>
                    </i18n>
                    <i class="el-icon-delete" @click="doDeleteSameStep(index)" v-if="form.data.gradeSameStepValue.stepValues.length > 1"></i>
                  </el-row>
                </div>
                <span class="plain-btn-blue" @click="addSameStep"
                  v-if="form.data.gradeSameStepValue.stepValues.length < 10 && ['BY_FULL_AMOUNT','BY_FULL_QTY'].indexOf(form.data.newStrategy) > -1">
                  {{i18n('/营销/日历活动/添加阶梯')}}
                </span>
              </template>
              <el-row v-if="form.data.favRule === 'gradeDiff' && gradeList.length > 0" class="rule-table">
                <el-row class="rule-table-header">
                  <el-col :span="4">会员等级</el-col>
                  <el-col :span="17">预付卡支付优惠规则</el-col>
                  <el-col :span="3"></el-col>
                </el-row>
                <el-row class="rule-table-line" v-for="(stepValue, index) of form.data.gradeDifferentStepValue" :key="index">
                  <el-col :span="4">{{ '[' + stepValue.grade + '] ' + stepValue.gradeName }}</el-col>
                  <el-col :span="17">
                    <el-checkbox v-model="stepValue.checked" @change="doCheckDiffStep(index,$event)">{{ i18n('可参与预付卡支付优惠') }}
                    </el-checkbox>
                    <br />
                    <div v-if="stepValue.checked">
                      <el-row v-for="(diffItem,ind) in stepValue.stepValues" :key="ind" style="margin-bottom:15px">
                        <i18n advance :k="sameGradeRuleStr">
                          <template slot-scope="{items}">
                            <div style="float: left">{{ items.s0.prefix }}</div>
                            <el-form-item :rules="['BY_AMOUNT','BY_FULL_AMOUNT'].indexOf(form.data.newStrategy) > -1 ? form.amountRules : form.qtyRules"
                              :prop="`gradeDifferentStepValue[${index}].stepValues[${ind}].threshold`" style="width: 80px;float: left;margin: 0 5px">
                              <AutoFixInput style="width: 80px" size="small" @change="refresh" v-if="['BY_AMOUNT','BY_FULL_AMOUNT'].indexOf(form.data.newStrategy) > -1" :min="0.01"
                                :max="999999" :fixed="2" v-model="diffItem.threshold" />
                              <AutoFixInput style="width: 80px" size="small" @change="refresh" v-if="['BY_QTY', 'BY_FULL_QTY'].indexOf(form.data.newStrategy) > -1" :min="1"
                                :max="999999" :fixed="0" v-model="diffItem.threshold" />
                            </el-form-item>
                            <div style="float: left">{{ items.s0.suffix }}</div>
                            <el-form-item :rules="form.amountRules" :prop="`gradeDifferentStepValue[${index}].stepValues[${ind}].value`"
                              style="width: 80px;float: left;margin: 0 5px">
                              <AutoFixInput style="width: 80px" size="small" @change="refresh" :min="0.01" :max="999999" :fixed="2"
                                v-model="diffItem.value"></AutoFixInput>
                            </el-form-item>
                            <div style="float: left">{{ items.s1.suffix }}</div>
                          </template>
                        </i18n>
                        <i class="el-icon-delete" @click="doDeleteDiffStep(index,ind)"
                          v-if="form.data.gradeDifferentStepValue[index].stepValues.length > 1"></i>
                      </el-row>
                      <span class="plain-btn-blue" @click="addDiffStep(index)"
                        v-if="form.data.gradeDifferentStepValue[index].stepValues.length < 10 && ['BY_FULL_AMOUNT','BY_FULL_QTY'].indexOf(form.data.newStrategy) > -1">
                        {{i18n('/营销/日历活动/添加阶梯')}}
                      </span>
                    </div>
                  </el-col>
                  <el-col :span="3" v-if="stepValue.checked">
                    <a @click="doCopy(stepValue.grade, index)"><i class="el-icon-document-copy"></i>&nbsp;
                      <span>复制给</span>
                    </a>
                  </el-col>
                </el-row>
                <el-form-item label="" prop="gradeDifferentStepValue"></el-form-item>
              </el-row>
            </el-form-item>
            <el-form-item label="叠加促销">
              <el-radio-group v-model="form.data.excludePromotion">
                <el-radio :label="true">{{i18n('是')}}</el-radio>
                <el-radio :label="false">{{i18n('否')}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>
    <CopyStepValue no-i18n ref="copyStepValue" @confirm="copyStepValue"></CopyStepValue>
    <CardTemplateSelectorDialog no-i18n ref="cardTemplateSelectorDialog" :filter="cardTemplateFilter" @summit="doCardTemplateSelected">
    </CardTemplateSelectorDialog>
  </div>
</template>

<script lang="ts" src="./CardBalancePromotionEdit.ts">
</script>

<style lang="scss">
.card-balance-promotion-edit {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: hidden;

  .current-page {
    height: calc(100% - 48px) !important;
    overflow: auto;

    .panel {
      .header {
        font-weight: 500;
        padding: 20px 20px 0 20px;
        font-size: 18px;
      }

      .content {
        padding: 20px;
      }
    }

    .split {
      height: 20px;
      background-color: #eeeff1;
    }

    .el-range__icon {
      line-height: 26px;
    }

    .el-range-separator {
      line-height: 26px;
    }

    .el-range__close-icon {
      line-height: 26px;
    }

    .rule-table {
      width: 70%;

      .rule-table-header {
        padding: 0 10px 0 10px;
        background-color: #e6e6e6;
        border: 1px solid #e6e6e6;
      }

      .rule-table-line {
        padding: 10px 10px 20px 10px;
        border: 1px solid #e6e6e6;
      }
    }

    .favor-row {
      display: flex;
      align-items: center;
      font-size: 12px;
      .el-radio {
        margin-right: 0 !important;
      }
      .el-radio__label {
        padding-left: 8px !important;
      }
    }

    .float-left-i18n-line {
      .text {
        height: 41px;
        float: left;
      }

      .el-form-item {
        float: left;
      }
    }
  }

  .el-icon-delete {
    color: red;
    cursor: pointer;
    font-size: 15px;
    margin-left: 8px;
  }
}
</style>
