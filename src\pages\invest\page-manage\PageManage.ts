/*
 * @Author: 黎钰龙
 * @Date: 2024-06-13 11:16:50
 * @LastEditTime: 2025-03-05 10:03:05
 * @LastEditors: haiding <EMAIL>
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\PageManage.ts
 * 记得注释
 */
import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import I18nPage from 'common/I18nDecorator';
import UrlParamUtil from 'util/UrlParamUtil';
import { Component, Vue } from 'vue-property-decorator';
import PageManageList from './PageManageList';
import SysManageList from './SysManageList';
import SysPathDialog from './cmp/sys-path-dialog/SysPathDialog';
@Component({
  name: 'PageManage',
  components: {
    BreadCrume,
    PageManageList,
    SysManageList,
    SysPathDialog
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    "/公用/菜单",
    '/公用/按钮',
    '/页面/页面管理'
  ],
  auto: true
})
export default class PageManage extends Vue {
  $refs: any
  panelArray: any = []
  activeName: string;

  created() {
    this.activeName = UrlParamUtil.get("activeName", "page-manage-list" as any);
    this.panelArray = [{
      name: this.i18n("页面管理"),
      url: "",
    },]
  }

  handleTabClick() {
    UrlParamUtil.store("activeName", this.activeName);
  }

  doGetPath() {
    this.$refs.sysPathDialog.open()
  }
};