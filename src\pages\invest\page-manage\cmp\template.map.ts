import DefaultPagePlaceProperty from './page-props-components/DefaultPagePlaceProperty';
import { PRODUCT_CATEGORY_BUTTONS } from './setting-toolbar/ButtonsData';
import { PagePropsComponents } from './page-props-components/PagePropsComponents.map';
import { PageComponentsVO } from './page-place-components/PageComponentsVO';
import ConstantMgr from 'mgr/ConstantMgr';
import { CmsConfigChannel } from 'model/template/CmsConfig';

const urlOss = '/-/cms/thumbnail/';
const testImg = require('../../../../assets/image/ic_danlantupian.png')
const membersImg = require('@/assets/image/fellow/ic_huiyaun.png')
const couponImg = require('@/assets/image/fellow/ic_huodongtoufang.png')
const navImg = require('@/assets/image/fellow/ic_yemiandaohang.png')
const storesImg = require('@/assets/image/fellow/ic_shipin.png')
const advImg = require('@/assets/image/fellow/ic_tupianguanggao.png')
const lunbotu = require('@/assets/image/fellow/cutting_ic_lunbotu.png')
const duolantupian = require('@/assets/image/fellow/cutting_ic_duolantupian.png')
const shuanglantupian = require('@/assets/image/fellow/cutting_ic_shuanglantupian.png')
const wenBenImg = require('@/assets/image/fellow/ic_wenben.png')
const electronicCard = require('@/assets/image/fellow/electronicCard.png')
const collectionPoint = require('@/assets/image/fellow/collectionPoint.png')
const roulette = require('@/assets/image/fellow/roulette.png')
const groupBuying = require('@/assets/image/fellow/ic_pintuan.png')
const equityCardImg = require('@/assets/image/fellow/<EMAIL>')

type widgetVo<T> = {
  id: string;
  placeName?: string;
  component: PageComponentsVO;
  defaultProp: T;
  toolbarBtns: Array<any>;
  name?: any;
  isHover?: boolean;
  iconUrl?: string;
  hoverIconUrl?: string;
  showComponentProp?: any[];
  availableChannels: CmsConfigChannel[],  //支持的投放渠道
};

class WidgetConfig {
  // 头部标题
  static titleWidgets() {
    const i18nFunc = new ConstantMgr.MenusFuc()
    return [
      {
        id: 'titleCmp',
        name: i18nFunc.format("/页面/页面管理", "全局设置"),
        iconUrl: urlOss + 'pic_rotation_nor.png',
        hoverIconUrl: urlOss + 'pic_rotation_hover.png',
        component: PageComponentsVO.TitleCmp,
        defaultProp: DefaultPagePlaceProperty.pageTitle(),
        toolbarBtns: [],
        showComponentProp: [
          {
            header: i18nFunc.format("/页面/页面管理", "全局设置"),
            component: PagePropsComponents.GlobalSet,
            label: i18nFunc.format("/页面/页面管理", "全局设置"),
            type: 'global',
            config: {
              options: [],
            },
          }
        ],
        availableChannels: [CmsConfigChannel.WEIXIN, CmsConfigChannel.ALIPAY, CmsConfigChannel.H5],
      },
    ] as Array<widgetVo<any>>
  }
  // 图片组件
  static imageWidgets() {
    const i18nFunc = new ConstantMgr.MenusFuc()
    return [
      {
        id: 'gif',
        name: i18nFunc.format("/页面/页面管理", "单栏图片"),
        isHover: false,
        iconUrl: testImg,
        hoverIconUrl: testImg,
        component: PageComponentsVO.SingleImage,
        defaultProp: DefaultPagePlaceProperty.shouyeimage(),
        showComponentProp: [
          {
            header: i18nFunc.format("/页面/页面管理", "全局设置"),
            component: PagePropsComponents.GlobalSet,
            label: i18nFunc.format("/页面/页面管理", "全局设置"),
            type: 'global',
            config: {
              options: [],
            },
          },
          {
            id: 1,
            type: 'content',
            component: PagePropsComponents.ActivityPlaceName,
            label: i18nFunc.format("/页面/页面管理", "组件名称"),
            config: {},
          },
          {
            id: 9,
            component: PagePropsComponents.UploadImg,
            label: i18nFunc.format("/页面/页面管理", "图片设置"),
            type: 'content',
            config: {
              delete: true,
              multiple: true,
              multipleNumber: 1, // 多选数量，0默认为不限制
              select: true,
              prefixDescribe:
                i18nFunc.format("/页面/页面管理", "建议图片宽度为750像素，高度为250像素，支持jpg/jpeg/png/gif，大小不超过2M，每一张图片高度相同"),
            },
          },
          // 周期投放
          {
            id: 20,
            type: 'content',
            component: PagePropsComponents.ShowTime,
            label: i18nFunc.format("/页面/页面管理", "显示时间"),
          },
          {
            id: 201,
            type: 'content',
            component: PagePropsComponents.CycleConditions,
            label: '',
          },
          {
            component: PagePropsComponents.StySetting,
            label: i18nFunc.format("/页面/页面管理", "组件间距"),
            type: 'sty',
            config: {
              options: [],
            },
          },
        ],
        toolbarBtns: PRODUCT_CATEGORY_BUTTONS,
        availableChannels: [CmsConfigChannel.WEIXIN, CmsConfigChannel.ALIPAY, CmsConfigChannel.H5],
      },
      {
        id: 'rotation',
        name: i18nFunc.format("/页面/页面管理", "轮播图"),
        isHover: false,
        iconUrl: lunbotu,
        hoverIconUrl: lunbotu,
        component: PageComponentsVO.SwiperImage,
        defaultProp: DefaultPagePlaceProperty.swiperImage(),
        showComponentProp: [
          {
            header: i18nFunc.format("/页面/页面管理", "全局设置"),
            component: PagePropsComponents.GlobalSet,
            label: i18nFunc.format("/页面/页面管理", "全局设置"),
            type: 'global',
            config: {
              options: [],
            },
          },
          {
            id: 1,
            component: PagePropsComponents.ActivityPlaceName,
            label: i18nFunc.format("/页面/页面管理", "组件名称"),
            type: 'content',
            config: {},
          },
          {
            id: 9,
            component: PagePropsComponents.DraggableImg,
            label: i18nFunc.format("/页面/页面管理", "图片设置"),
            type: 'content',
            config: {
              delete: true,
              multiple: true,
              multipleNumber: 10, // 多选数量，0默认为不限制
              select: true,
              prefixDescribe:
                i18nFunc.format("/页面/页面管理", "图片最多添加10张，添加后可拖动排序。建议图片比例4:3、1:1、16:9"),
              showDelete: true,
            },
          },
          // 轮播时间
          {
            id: 10,
            component: PagePropsComponents.RotationInterval,
            label: i18nFunc.format("/页面/页面管理", "轮播间隔"),
            type: 'content',
          },
          // 周期投放
          {
            id: 20,
            component: PagePropsComponents.ShowTime,
            label: i18nFunc.format("/页面/页面管理", "显示时间"),
            type: 'content',
          },
          {
            id: 201,
            component: PagePropsComponents.CycleConditions,
            type: 'content',
            label: '',
          },
          {
            component: PagePropsComponents.StySetting,
            label: i18nFunc.format("/页面/页面管理", "组件间距"),
            type: 'sty',
            config: {
              options: [],
            },
          },
        ],
        toolbarBtns: PRODUCT_CATEGORY_BUTTONS,
        availableChannels: [CmsConfigChannel.WEIXIN, CmsConfigChannel.ALIPAY, CmsConfigChannel.H5],
      },
      {
        id: 'doubleColumnImage',
        name: i18nFunc.format("/页面/页面管理", "双栏图片"),
        isHover: false,
        iconUrl: shuanglantupian,
        hoverIconUrl: shuanglantupian,
        component: PageComponentsVO.DoubleColumnImage,
        defaultProp: DefaultPagePlaceProperty.doubleColumnImage(),
        showComponentProp: [
          {
            header: i18nFunc.format("/页面/页面管理", "全局设置"),
            component: PagePropsComponents.GlobalSet,
            label: i18nFunc.format("/页面/页面管理", "全局设置"),
            type: 'global',
            config: {
              options: [],
            },
          },
          {
            id: 1,
            component: PagePropsComponents.ActivityPlaceName,
            label: i18nFunc.format("/页面/页面管理", "组件名称"),
            type: 'content',
            config: {},
          },
          {
            id: 9,
            component: PagePropsComponents.DraggableImg,
            label: i18nFunc.format("/页面/页面管理", "图片设置"),
            type: 'content',
            config: {
              delete: true,
              multiple: true,
              multipleNumber: 2, // 多选数量，0默认为不限制
              select: true,
              prefixDescribe:
                i18nFunc.format("/页面/页面管理", "建议图片宽度为340像素，高度不限，支持gif/jpg/jpeg/png，大小不超过2M"),
            },
          },
          // 周期投放
          {
            id: 20,
            component: PagePropsComponents.ShowTime,
            label: i18nFunc.format("/页面/页面管理", "显示时间"),
            type: 'content',
          },
          {
            id: 201,
            component: PagePropsComponents.CycleConditions,
            type: 'content',
            label: '',
          },
          {
            component: PagePropsComponents.StySetting,
            label: i18nFunc.format("/页面/页面管理", "组件间距"),
            type: 'sty',
            config: {
              options: [],
            },
          },
        ],
        toolbarBtns: PRODUCT_CATEGORY_BUTTONS,
        availableChannels: [CmsConfigChannel.WEIXIN, CmsConfigChannel.ALIPAY, CmsConfigChannel.H5],
      },
      {
        id: 'multiColumnImage',
        name: i18nFunc.format("/页面/页面管理", "多栏图片"),
        isHover: false,
        iconUrl: duolantupian,
        hoverIconUrl: duolantupian,
        component: PageComponentsVO.MultiColumnImage,
        defaultProp: DefaultPagePlaceProperty.multiColumnImage(),
        showComponentProp: [
          {
            header: i18nFunc.format("/页面/页面管理", "全局设置"),
            component: PagePropsComponents.GlobalSet,
            label: i18nFunc.format("/页面/页面管理", "全局设置"),
            type: 'global',
            config: {
              options: [],
            },
          },
          {
            id: 1,
            component: PagePropsComponents.ActivityPlaceName,
            label: i18nFunc.format("/页面/页面管理", "组件名称"),
            type: 'content',
            config: {},
          },
          {
            id: 9,
            component: PagePropsComponents.DraggableImg,
            label: i18nFunc.format("/页面/页面管理", "图片设置"),
            type: 'content',
            config: {
              delete: true,
              multiple: true,
              multipleNumber: 3, // 多选数量，0默认为不限制
              select: true,
              prefixDescribe:
                i18nFunc.format("/页面/页面管理", "建议图片宽度为340像素，高度不限，支持gif/jpg/jpeg/png，大小不超过2M"),
            },
          },
          // 周期投放
          {
            id: 20,
            component: PagePropsComponents.ShowTime,
            label: i18nFunc.format("/页面/页面管理", "显示时间"),
            type: 'content',
          },
          {
            id: 201,
            component: PagePropsComponents.CycleConditions,
            type: 'content',
            label: '',
          },
          {
            component: PagePropsComponents.StySetting,
            label: i18nFunc.format("/页面/页面管理", "组件间距"),
            type: 'sty',
            config: {
              options: [],
            },
          },
        ],
        toolbarBtns: PRODUCT_CATEGORY_BUTTONS,
        availableChannels: [CmsConfigChannel.WEIXIN, CmsConfigChannel.ALIPAY, CmsConfigChannel.H5],
      },
      {
        id: 'imageAd',
        name: i18nFunc.format("/页面/页面管理", "图片广告"),
        isHover: false,
        iconUrl: advImg,
        hoverIconUrl: advImg,
        component: PageComponentsVO.ImageAd,
        defaultProp: DefaultPagePlaceProperty.imagead(),
        showComponentProp: [
          {
            header: i18nFunc.format("/页面/页面管理", "全局设置"),
            component: PagePropsComponents.GlobalSet,
            label: i18nFunc.format("/页面/页面管理", "全局设置"),
            type: 'global',
            config: {
              options: [],
            },
          },
          {
            id: 1,
            type: 'content',
            component: PagePropsComponents.ActivityPlaceName,
            label: i18nFunc.format("/页面/页面管理", "组件名称"),
            config: {},
          },
          {
            id: 9,
            type: 'content',
            component: PagePropsComponents.UploadImg,
            label: i18nFunc.format("/页面/页面管理", "图片设置"),
            config: {
              delete: true,
              multiple: true,
              multipleNumber: 1, // 多选数量，0默认为不限制
              select: true,
              prefixDescribe:
                i18nFunc.format("/页面/页面管理", "建议图片宽度为750像素，高度为250像素，支持jpg/jpeg/png/gif，大小不超过2M，每一张图片高度相同"),
            },
          },
          {
            id: 122,
            type: 'content',
            component: PagePropsComponents.HotspotSet,
            label: i18nFunc.format("/页面/页面管理", "热区设置"),
          },
          // 周期投放
          {
            id: 20,
            type: 'content',
            component: PagePropsComponents.ShowTime,
            label: i18nFunc.format("/页面/页面管理", "显示时间"),
          },
          {
            id: 201,
            type: 'content',
            component: PagePropsComponents.CycleConditions,
            label: '',
          },
          {
            component: PagePropsComponents.StySetting,
            label: i18nFunc.format("/页面/页面管理", "组件间距"),
            type: 'sty',
            config: {
              options: [],
            },
          },
        ],
        toolbarBtns: PRODUCT_CATEGORY_BUTTONS,
        availableChannels: [CmsConfigChannel.WEIXIN, CmsConfigChannel.ALIPAY, CmsConfigChannel.H5],
      },
    ] as Array<widgetVo<any>>
  }
  // 会员组件
  static membersWidgets() {
    const i18nFunc = new ConstantMgr.MenusFuc()
    return [
      {
        id: 'memberRotation',
        name: i18nFunc.format("/页面/页面管理", "会员组件+轮播图"),
        isHover: false,
        iconUrl: membersImg,
        hoverIconUrl: membersImg,
        component: PageComponentsVO.MembersSwiperImage,
        defaultProp: DefaultPagePlaceProperty.membersSwiperImage(),
        showComponentProp: [
          {
            header: i18nFunc.format("/页面/页面管理", "全局设置"),
            component: PagePropsComponents.GlobalSet,
            label: i18nFunc.format("/页面/页面管理", "全局设置"),
            type: 'global',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.ActivityPlaceName,
            label: i18nFunc.format("/页面/页面管理", "组件名称"),
            type: 'content',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.MemberCard,
            label: i18nFunc.format("/页面/页面管理", "会员卡片"),
            type: 'content',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.Carousel,
            label: i18nFunc.format("/页面/页面管理", "轮播图"),
            type: 'content',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.StySetting,
            label: i18nFunc.format("/页面/页面管理", "组件间距"),
            type: 'sty',
            config: {
              options: [],
            },
          },
        ],
        toolbarBtns: PRODUCT_CATEGORY_BUTTONS,
        availableChannels: [CmsConfigChannel.WEIXIN, CmsConfigChannel.ALIPAY, CmsConfigChannel.H5],
      },{
        id: 'equityCard',
        name: i18nFunc.format("/会员/会员资料/权益卡", "权益卡"),
        isHover: false,
        iconUrl: equityCardImg,
        hoverIconUrl: equityCardImg,
        component: PageComponentsVO.EquityCard,
        defaultProp: DefaultPagePlaceProperty.equityCard(),
        showComponentProp:[
          {
            component: PagePropsComponents.ActivityPlaceName,
            label: i18nFunc.format("/页面/页面管理", "组件名称"),
            type: 'content',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.ActivityEquityCard,
            // label: "选择权益卡", //i18nFunc.format("/页面/页面管理", "选择权益卡"),
            type: 'content',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.EquityCardContentColor,
            label: i18nFunc.format("/页面/页面管理", "内容颜色"),
            type: 'sty',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.StySetting,
            label: i18nFunc.format("/页面/页面管理", "组件间距"),
            type: 'sty',
            config: {
              options: [],
            },
          },
        ],
        toolbarBtns: PRODUCT_CATEGORY_BUTTONS,
        availableChannels: [CmsConfigChannel.WEIXIN, CmsConfigChannel.ALIPAY, CmsConfigChannel.H5],
      }
    ] as Array<widgetVo<any>>
  }
  // 活动组件
  static activityWidgets() {
    const i18nFunc = new ConstantMgr.MenusFuc()
    return [
      {
        id: 'crmCoupon',
        name: i18nFunc.format("/页面/页面管理", "优惠券"),
        isHover: false,
        iconUrl: couponImg,
        hoverIconUrl: couponImg,
        component: PageComponentsVO.Coupon,
        defaultProp: DefaultPagePlaceProperty.coupon(),
        showComponentProp: [
          {
            header: i18nFunc.format("/页面/页面管理", "全局设置"),
            component: PagePropsComponents.GlobalSet,
            label: i18nFunc.format("/页面/页面管理", "全局设置"),
            type: 'global',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.ActivityPlaceName,
            label: i18nFunc.format("/页面/页面管理", "组件名称"),
            type: 'content',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.AngleSet,
            label: i18nFunc.format("/页面/页面管理", "投放内容"),
            type: 'content',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.BuyButton,
            label: i18nFunc.format("/页面/页面管理", "购买按钮"),
            type: 'content',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.ShowTime,
            label: i18nFunc.format("/页面/页面管理", "显示时间"),
            type: 'content',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.LineShowQty,
            label: i18nFunc.format("/页面/页面管理", "展示方式"),
            type: 'sty',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.StySetting,
            label: i18nFunc.format("/页面/页面管理", "组件间距"),
            type: 'sty',
            config: {
              options: [],
            },
          },
          // 周期投放
          {
            component: PagePropsComponents.CycleConditions,
            label: '',
            type: 'content',
            config: {
              options: [],
            },
          },
          // {
          //   component: PagePropsComponents.SingleCouponStyle,
          //   label: i18nFunc.format("/页面/页面管理", "单券样式"),
          //   type: 'sty',
          //   config: {
          //     options: [],
          //   },
          // },
        ],
        toolbarBtns: PRODUCT_CATEGORY_BUTTONS,
        availableChannels: [CmsConfigChannel.WEIXIN, CmsConfigChannel.ALIPAY, CmsConfigChannel.H5],
      },
      {
        id: 'electronicCard',
        name: i18nFunc.format("/页面/页面管理", "电子卡"),
        isHover: false,
        iconUrl: electronicCard,
        hoverIconUrl: electronicCard,
        component: PageComponentsVO.ElectronicCard,
        defaultProp: DefaultPagePlaceProperty.electroniccard(),
        showComponentProp: [
          {
            header: i18nFunc.format("/页面/页面管理", "全局设置"),
            component: PagePropsComponents.GlobalSet,
            label: i18nFunc.format("/页面/页面管理", "全局设置"),
            type: 'global',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.ActivityPlaceName,
            label: i18nFunc.format("/页面/页面管理", "组件名称"),
            type: 'content',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.SelectActivity,
            label: i18nFunc.format("/页面/页面管理", "投放内容"),
            type: 'content',
            config: {
              options: [],
              id: 'electronicCard'
            },
          },
          {
            component: PagePropsComponents.ShowTime,
            label: i18nFunc.format("/页面/页面管理", "显示时间"),
            type: 'content',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.StySetting,
            label: i18nFunc.format("/页面/页面管理", "组件间距"),
            type: 'sty',
            config: {
              options: [],
            },
          },
          // 周期投放
          {
            component: PagePropsComponents.CycleConditions,
            label: '',
            type: 'content',
            config: {
              options: [],
            },
          },
        ],
        toolbarBtns: PRODUCT_CATEGORY_BUTTONS,
        availableChannels: [CmsConfigChannel.WEIXIN, CmsConfigChannel.ALIPAY, CmsConfigChannel.H5],
      },
      {
        id: 'collectionPoint',
        name: i18nFunc.format("/页面/页面管理", "集点"),
        isHover: false,
        iconUrl: collectionPoint,
        hoverIconUrl: collectionPoint,
        component: PageComponentsVO.CollectPoints,
        defaultProp: DefaultPagePlaceProperty.collectionPoint(),
        showComponentProp: [
          {
            header: i18nFunc.format("/页面/页面管理", "全局设置"),
            component: PagePropsComponents.GlobalSet,
            label: i18nFunc.format("/页面/页面管理", "全局设置"),
            type: 'global',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.ActivityPlaceName,
            label: i18nFunc.format("/页面/页面管理", "组件名称"),
            type: 'content',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.SelectActivity,
            label: i18nFunc.format("/页面/页面管理", "投放内容"),
            type: 'content',
            config: {
              options: [],
              id: 'collectionPoint'
            },
          },
          {
            component: PagePropsComponents.ShowTime,
            label: i18nFunc.format("/页面/页面管理", "显示时间"),
            type: 'content',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.StySetting,
            label: i18nFunc.format("/页面/页面管理", "组件间距"),
            type: 'sty',
            config: {
              options: [],
            },
          },
          // 周期投放
          {
            component: PagePropsComponents.CycleConditions,
            label: '',
            type: 'content',
            config: {
              options: [],
            },
          },
        ],
        toolbarBtns: PRODUCT_CATEGORY_BUTTONS,
        availableChannels: [CmsConfigChannel.WEIXIN, CmsConfigChannel.ALIPAY, CmsConfigChannel.H5],
      },
      {
        id: 'bigTurntable',
        name: i18nFunc.format("/页面/页面管理", "大转盘"),
        isHover: false,
        iconUrl: roulette,
        hoverIconUrl: roulette,
        component: PageComponentsVO.Roulette,
        defaultProp: DefaultPagePlaceProperty.roulette(),
        showComponentProp: [
          {
            header: i18nFunc.format("/页面/页面管理", "全局设置"),
            component: PagePropsComponents.GlobalSet,
            label: i18nFunc.format("/页面/页面管理", "全局设置"),
            type: 'global',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.ActivityPlaceName,
            label: i18nFunc.format("/页面/页面管理", "组件名称"),
            type: 'content',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.SelectActivity,
            label: i18nFunc.format("/页面/页面管理", "投放内容"),
            type: 'content',
            config: {
              options: [],
              id: 'bigTurntable'
            },
          },
          {
            component: PagePropsComponents.ShowTime,
            label: i18nFunc.format("/页面/页面管理", "显示时间"),
            type: 'content',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.StySetting,
            label: i18nFunc.format("/页面/页面管理", "组件间距"),
            type: 'sty',
            config: {
              options: [],
            },
          },
          // 周期投放
          {
            component: PagePropsComponents.CycleConditions,
            label: '',
            type: 'content',
            config: {
              options: [],
            },
          },
        ],
        toolbarBtns: PRODUCT_CATEGORY_BUTTONS,
        availableChannels: [CmsConfigChannel.WEIXIN, CmsConfigChannel.ALIPAY, CmsConfigChannel.H5],
      },
      {
        id: 'shareGroupLottery',
        name: i18nFunc.format("/页面/页面管理", "拼团抽奖"),
        isHover: false,
        iconUrl: groupBuying,
        hoverIconUrl: groupBuying,
        component: PageComponentsVO.GroupLottery,
        defaultProp: DefaultPagePlaceProperty.groupBooking(),
        showComponentProp: [
          {
            header: i18nFunc.format("/页面/页面管理", "全局设置"),
            component: PagePropsComponents.GlobalSet,
            label: i18nFunc.format("/页面/页面管理", "全局设置"),
            type: 'global',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.ActivityPlaceName,
            label: i18nFunc.format("/页面/页面管理", "组件名称"),
            type: 'content',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.SelectActivity,
            label: i18nFunc.format("/页面/页面管理", "投放内容"),
            type: 'content',
            config: {
              options: [],
              id: 'shareGroupLottery'
            },
          },
          {
            component: PagePropsComponents.ShowTime,
            label: i18nFunc.format("/页面/页面管理", "显示时间"),
            type: 'content',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.StySetting,
            label: i18nFunc.format("/页面/页面管理", "组件间距"),
            type: 'sty',
            config: {
              options: [],
            },
          },
          // 周期投放
          {
            component: PagePropsComponents.CycleConditions,
            label: '',
            type: 'content',
            config: {
              options: [],
            },
          },
        ],
        toolbarBtns: PRODUCT_CATEGORY_BUTTONS,
        availableChannels: [CmsConfigChannel.WEIXIN, CmsConfigChannel.ALIPAY, CmsConfigChannel.H5],
      }
    ] as Array<widgetVo<any>>
  }
  // 其他组件
  static othersWidgets() {
    const i18nFunc = new ConstantMgr.MenusFuc()
    return [
      {
        id: 'pageNavigation',
        name: i18nFunc.format("/页面/页面管理", "页面导航"),
        isHover: false,
        iconUrl: navImg,
        hoverIconUrl: navImg,
        component: PageComponentsVO.PageNavigation,
        defaultProp: DefaultPagePlaceProperty.pageNavigation(),
        showComponentProp: [
          {
            header: i18nFunc.format("/页面/页面管理", "全局设置"),
            component: PagePropsComponents.GlobalSet,
            label: i18nFunc.format("/页面/页面管理", "全局设置"),
            type: 'global',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.ActivityPlaceName,
            label: i18nFunc.format("/页面/页面管理", "组件名称"),
            type: 'content',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.NavType,
            label: i18nFunc.format("/页面/页面管理", "导航类型"),
            type: 'content',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.NavigationSettings,
            label: '',
            type: 'content',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.NavStyle,
            label: i18nFunc.format("/页面/页面管理", "导航样式设置"),
            type: 'sty',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.ShowTime,
            label: i18nFunc.format("/页面/页面管理", "显示时间"),
            type: 'content',
            config: {
              options: [],
            },
          },
          // 周期投放
          {
            component: PagePropsComponents.CycleConditions,
            label: '',
            type: 'content',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.StySetting,
            label: i18nFunc.format("/页面/页面管理", "组件间距"),
            type: 'sty',
            config: {
              options: [],
            },
          },
        ],
        toolbarBtns: PRODUCT_CATEGORY_BUTTONS,
        availableChannels: [CmsConfigChannel.WEIXIN, CmsConfigChannel.ALIPAY, CmsConfigChannel.H5],
      },
      {
        id: 'text',
        name: i18nFunc.format("/页面/页面管理", "文本"),
        iconUrl: wenBenImg,
        hoverIconUrl: wenBenImg,
        component: PageComponentsVO.TextFron,
        defaultProp: DefaultPagePlaceProperty.textFron(),
        showComponentProp: [
          {
            header: i18nFunc.format("/页面/页面管理", "全局设置"),
            component: PagePropsComponents.GlobalSet,
            label: i18nFunc.format("/页面/页面管理", "全局设置"),
            type: 'global',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.ActivityPlaceName,
            label: i18nFunc.format("/页面/页面管理", "组件名称"),
            type: 'content',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.TextSet,
            label: i18nFunc.format("/页面/页面管理", "文本内容"),
            type: 'content',
            config: {
              options: [],
            },
          },
          // 周期投放
          {
            id: 20,
            type: 'content',
            component: PagePropsComponents.ShowTime,
            label: i18nFunc.format("/页面/页面管理", "显示时间"),
          },
          {
            id: 201,
            type: 'content',
            component: PagePropsComponents.CycleConditions,
            label: '',
          },
          {
            component: PagePropsComponents.TextFont,
            label: i18nFunc.format("/页面/页面管理", "字体样式设置"),
            type: 'sty',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.StySetting,
            label: i18nFunc.format("/页面/页面管理", "组件间距"),
            type: 'sty',
            config: {
              options: [],
            },
          },
        ],
        toolbarBtns: PRODUCT_CATEGORY_BUTTONS,
        availableChannels: [CmsConfigChannel.WEIXIN, CmsConfigChannel.ALIPAY, CmsConfigChannel.H5],
      },
      {
        id: 'myCoupon',
        name: i18nFunc.format("/页面/页面管理", "我的优惠券"),
        isHover: false,
        iconUrl: couponImg,
        hoverIconUrl: couponImg,
        component: PageComponentsVO.MyCoupon,
        defaultProp: DefaultPagePlaceProperty.myCoupon(),
        showComponentProp: [
          {
            header: i18nFunc.format("/页面/页面管理", "全局设置"),
            component: PagePropsComponents.GlobalSet,
            label: i18nFunc.format("/页面/页面管理", "全局设置"),
            type: 'global',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.StySetting,
            label: i18nFunc.format("/页面/页面管理", "组件间距"),
            type: 'sty',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.CouponStyle,
            label: i18nFunc.format("/页面/页面管理", "券样式"),
            type: 'sty',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.ActivityPlaceName,
            label: i18nFunc.format("/页面/页面管理", "组件名称"),
            type: 'content',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.CouponTab,
            label: '',
            type: 'content',
            config: {
              options: [],
            },
          },
        ],
        toolbarBtns: PRODUCT_CATEGORY_BUTTONS,
        availableChannels: [CmsConfigChannel.WEIXIN, CmsConfigChannel.ALIPAY, CmsConfigChannel.H5],
      },
      {
        id: 'menu',
        name: i18nFunc.format("/页面/页面管理", "菜单"),
        isHover: false,
        iconUrl: couponImg,
        hoverIconUrl: couponImg,
        component: PageComponentsVO.Menu,
        defaultProp: DefaultPagePlaceProperty.menu(),
        showComponentProp: [
          {
            header: i18nFunc.format("/页面/页面管理", "全局设置"),
            component: PagePropsComponents.GlobalSet,
            label: i18nFunc.format("/页面/页面管理", "全局设置"),
            type: 'global',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.StySetting,
            label: i18nFunc.format("/页面/页面管理", "组件间距"),
            type: 'sty',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.ActivityPlaceName,
            label: i18nFunc.format("/页面/页面管理", "组件名称"),
            type: 'content',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.MenuSet,
            label: i18nFunc.format("/页面/页面管理", "菜单内容"),
            type: 'content',
            config: {
              options: [],
            },
          },
        ],
        toolbarBtns: PRODUCT_CATEGORY_BUTTONS,
        availableChannels: [CmsConfigChannel.WEIXIN, CmsConfigChannel.ALIPAY, CmsConfigChannel.H5],
      },
      {
        id: 'switchStore',
        name: i18nFunc.format("/页面/页面管理", "切换门店"),
        isHover: false,
        iconUrl: storesImg,
        hoverIconUrl: storesImg,
        component: PageComponentsVO.SwitchStores,
        defaultProp: DefaultPagePlaceProperty.switchStores(),
        showComponentProp: [
          {
            header: i18nFunc.format("/页面/页面管理", "全局设置"),
            component: PagePropsComponents.GlobalSet,
            label: i18nFunc.format("/页面/页面管理", "全局设置"),
            type: 'global',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.ContentColor,
            label: i18nFunc.format("/页面/页面管理", "内容颜色"),
            type: 'sty',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.StySetting,
            label: i18nFunc.format("/页面/页面管理", "组件间距"),
            type: 'sty',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.ActivityPlaceName,
            label: i18nFunc.format("/页面/页面管理", "组件名称"),
            type: 'content',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.Auxiliary,
            label: i18nFunc.format("/页面/页面管理", "辅助文案"),
            type: 'content',
            config: {
              options: [],
            },
          },
          {
            component: PagePropsComponents.ShopStyle,
            label: i18nFunc.format("/页面/页面管理", "样式"),
            type: 'sty',
            config: {
              options: []
            }
          },
        ],
        toolbarBtns: PRODUCT_CATEGORY_BUTTONS,
        availableChannels: [CmsConfigChannel.WEIXIN, CmsConfigChannel.ALIPAY],
      },
    ] as Array<widgetVo<any>>
  }
  static GROUPS() {
    const i18nFunc = new ConstantMgr.MenusFuc()
    return [
      { name: i18nFunc.format("/页面/页面管理", "图片组件"), type: 'image', widgets: WidgetConfig.imageWidgets() },
      { name: i18nFunc.format("/页面/页面管理", "会员组件"), type: 'image', widgets: WidgetConfig.membersWidgets() },
      { name: i18nFunc.format("/页面/页面管理", "活动组件"), type: 'product', widgets: WidgetConfig.activityWidgets() },
      { name: i18nFunc.format("/页面/页面管理", "其他组件"), type: 'text', widgets: WidgetConfig.othersWidgets() },
    ]
  }
}

export default WidgetConfig;
