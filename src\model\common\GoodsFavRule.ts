/*
 * @Author: 黎钰龙
 * @Date: 2024-12-05 14:55:39
 * @LastEditTime: 2024-12-05 14:58:32
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\common\GoodsFavRule.ts
 * 记得注释
 */
export default class GoodsFavRule {
  // 优惠类型
  favType: Nullable<GoodsFavType> = null
  // 是否叠加
  superimposed: Nullable<boolean> = null
}

export enum GoodsFavType {
  MANUAL_DISCOUNT = 'MANUAL_DISCOUNT', //人工折扣
  MEMBER_PRICE = 'MEMBER_PRICE',  //会员价
  OTHER = 'OTHER' //其他优惠
}