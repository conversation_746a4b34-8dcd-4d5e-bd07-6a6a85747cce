<template>
    <el-dialog :before-close="doBeforeClose" :close-on-click-modal="false"  :title="formatI18n('/会员/会员资料', '导入会员')" :visible.sync="dialogShow" class="upload-file-dialog">
        <div class="wrap">
            <FormItem :label="formatI18n('/公用/导入', '实例模板')">
                <el-select v-model="templateType">
                    <el-option :label="formatI18n('/会员/会员资料', '会员资料')" value="member_upload">{{formatI18n('/会员/会员资料', '会员资料')}}</el-option>
                    <el-option :label="formatI18n('/会员/会员资料', '实体卡号')" value="card_member_upload">{{formatI18n('/会员/会员资料', '实体卡号')}}</el-option>
                </el-select>
            </FormItem>
            <FormItem label="">
                {{formatI18n('/公用/导入', '实例模板')}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <!--<el-button v-if="templateType === 'member_upload'" type="text" @click="doDownLoadTemplate('member')">会员资料模板</el-button>-->
                <!--<el-button v-if="templateType === 'card_member_upload'" type="text" @click="doDownLoadTemplate('member')">会员和卡关联模板</el-button>-->
                <a
                        class="action-hover_download" @click="downloadTemplate(member)"
                        style="line-height: 12px; color: #318BFF;font-size: 12px;text-decoration: none"
                        v-if="templateType === 'member_upload'">{{formatI18n('/会员/会员资料', '会员资料模板')}}</a>
                <a
                        class="action-hover_download" @click="downloadTemplate(cardMember)"
                        style="line-height: 12px; color: #318BFF;font-size: 12px;text-decoration: none"
                        v-if="templateType === 'card_member_upload'">{{formatI18n('/会员/会员资料/批量导入/选择实体卡号/会员和卡关联模板')}}</a>
            </FormItem>
            <FormItem :label="formatI18n('/公用/导入', '选择文件')">
                <div style="position: relative;top: 9px; margin-bottom: 20px;color: #9a9fa8;
    font-size: 12px;">{{getLimitInfo}}</div>
                <el-upload
                        :action="getUploadUrl"
                        :auto-upload="false"
                        :on-change="doHandleChange"
                        :on-error="getErrorInfo"
                        :headers="uploadHeaders"
                        :on-success="getSuccessInfo"
                        :with-credentials="true"
                        class="upload-demo"
                        ref="upload">
                    <el-button size="small" slot="trigger" type="default">{{formatI18n('/公用/导入', '选择文件')}}</el-button>
                </el-upload>
            </FormItem>
        </div>
        <div class="dialog-footer" slot="footer">
            <el-button @click="doModalClose('cancel')">{{formatI18n('/公用/按钮', '取消')}}</el-button>
            <el-button @click="doModalClose('confirm')" size="small" type="primary">{{formatI18n('/公用/导入', '确认导入')}}</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts" src="./UploadFileModal.ts">
</script>

<style lang="scss">
.upload-file-dialog{
    display: flex;
    justify-content: center;
    align-items: center;
    .wrap{
        margin-top: 30px;
    }
    .el-dialog{
        width: 600px;
        height: 400px;
        margin: 0 !important;
    }
    .el-dialog .el-dialog__body{
        height: 255px;
    }
}
</style>