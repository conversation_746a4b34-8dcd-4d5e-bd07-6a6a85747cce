/*
 * @Author: 黎钰龙
 * @Date: 2023-08-09 14:53:12
 * @LastEditTime: 2023-11-01 17:27:52
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\custom\dqsh\member\cmp\UploadMemberTags.ts
 * 记得注释
 */
import { Component, Prop, Vue } from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import EnvUtil from 'util/EnvUtil'
import I18nPage from 'common/I18nDecorator'
import UploadApi from "http/upload/UploadApi";

@Component({
  name: 'UploadMemberTags',
  components: {
    FormItem
  }
})
@I18nPage({
  prefix: [
    "/公用/券模板",
    '/会员/会员资料',
    '/会员/会员资料/会员资料导入',
    '/公用/导入',
    '/公用/按钮'
  ],
  auto: true
})
export default class UploadMemberTags extends Vue {
  $refs: any
  @Prop({
    type: Boolean,
    default: false
  })
  dialogShow: boolean
  fileCount = 0
  uploadHeaders: any = {}
  // 会员资料模板
  get member() {
    if (location.href.indexOf('localhost') === -1) {
      return 'template_member_tags.xlsx'
    } else {
      return 'template_member_tags.xlsx'
    }
  }

  get getUploadUrl() {
    return EnvUtil.getServiceUrl() + `v1/member/importMemberTag`
  }

  created() {
    let locale = sessionStorage.getItem('locale')
    this.uploadHeaders = {
      locale: locale ? locale : "zh_CN",
      time_zone: new Date().getTimezoneOffset(),
      marketingCenter: sessionStorage.getItem("marketCenter"),
    };
    const authorization = EnvUtil.getUniAuthorization()
    if (authorization) {
      this.uploadHeaders.authorization = authorization
    }
  }
  doModalClose(type: string) {
    if (type === 'confirm') {
      if (this.fileCount > 0) {
        this.$refs.upload.submit();
      } else {
        this.$message.warning('请先选择文件')
      }
    } else {
      this.$emit('dialogClose')
    }
  }
  doHandleChange(file: any, fileList: any) {
    if (fileList.length > 0) {
      this.fileCount++
    }
  }
  doBeforeClose(done: any) {
    this.$emit('dialogClose')
    done()
  }
  getSuccessInfo(a: any, b: any, c: any) {
    if (a && a.code === 2000) {
      this.$refs.upload.clearFiles()
      this.fileCount = 0
      this.$emit('dialogClose')
      this.$emit('upload-success')
    }
  }
  getErrorInfo(a: any, b: any, c: any) {
    this.$message.error('导入失败，请重新导入')
    this.fileCount = 0
    this.$refs.upload.clearFiles()
  }

  downloadTemplate() {
    UploadApi.getUrl(this.member).then((resp: any) => {
      if (resp && resp.data) {
        window.open(resp.data);
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
}