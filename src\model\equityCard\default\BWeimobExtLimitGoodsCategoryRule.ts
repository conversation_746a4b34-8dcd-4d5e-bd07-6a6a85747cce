import BWeimobExtLimitGoodsCategoryRuleInfo from 'model/equityCard/default/BWeimobExtLimitGoodsCategoryRuleInfo'
import IdName from 'model/equityCard/default/IdName'

// 适用商品类别规则
export default class BWeimobExtLimitGoodsCategoryRule {
  // 是否限制是否不可用商品
  existExcludeGoods: Nullable<boolean> = null
  // 不可用商品
  excludeGoodsIds: IdName[] = []
  // 是否包含下级自建商品
  includeChildGoods: Nullable<boolean> = null
  // 限制类别明细
  ruleInfos: BWeimobExtLimitGoodsCategoryRuleInfo[] = []
}