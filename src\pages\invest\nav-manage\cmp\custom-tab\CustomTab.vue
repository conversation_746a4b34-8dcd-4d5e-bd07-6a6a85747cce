<template>
  <div class="custom-tab-container">
    <div class="form-title">{{ i18n('基础设置') }}</div>
    <el-form ref="ruleForm" :model="currentForm" label-width="100px" :rules="rules">
      <el-form-item :label="i18n('导航样式')">
        <el-radio-group @change="doFormChange" v-model="currentForm.navigationType" style="margin-top: 13px;">
          <el-radio label="normal" class="radio-block">
            <span class="nav-class-tag">{{ i18n('通用样式') }}</span>
            <img class="nav-style-img" src="~assets/image/nav/img_navigation_1.png" />
          </el-radio>
          <el-radio label="rudder" class="radio-block">
            <span class="nav-class-tag">{{ i18n('舵式样式') }}</span>
            <img class="nav-style-img" src="~assets/image/nav/img_navigation_2.png" />
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="i18n('图标配色')"
        :style="{ minHeight: currentForm.iconColorType === 'system' ? '100px' : '180px' }">
        <el-radio-group @change="doFormChange" v-model="currentForm.iconColorType">
          <el-radio label="system" class="radio-block">
            <span class="nav-class-tag">{{ i18n('系统配色') }}</span>
            <div class="custom-color">
              <div :class="['set-color-box set-system', customForm.iconColorType]"
                v-if="currentForm.iconColorType === 'system'">
                <div class="set-block">
                  <span class="set-status">{{ i18n('选中') }}</span>
                  <el-color-picker size="mini" @change="doFormChange"
                    v-model="currentForm.selectedColor"></el-color-picker>
                </div>
                <div class="set-block">
                  <span class="set-status">{{ i18n('未选') }}</span>
                  <el-color-picker size="mini" @change="doFormChange"
                    v-model="currentForm.unselectedColor"></el-color-picker>
                </div>
              </div>
            </div>
          </el-radio>
          <el-radio label="custom" class="radio-block">
            <span class="nav-class-tag">{{ i18n('自定义配置') }}</span>
            <div class="custom-color">
              <div :class="['set-color-box set-rudder', customForm.iconColorType]"
                v-if="currentForm.iconColorType === 'custom'">
                <div class="set-box">
                  <span class="set-status">{{ i18n('选中') }}</span>
                  <div class="set-block">
                    <span class="demonstration">{{ i18n('文本色') }}</span>
                    <el-color-picker size="mini" @change="doFormChange" v-model="currentForm.selectedFontColor"
                      :predefine="predefineColors"></el-color-picker>
                  </div>
                  <div class="set-block">
                    <span class="demonstration">{{ i18n('图标色') }}</span>
                    <el-color-picker size="mini" @change="doFormChange" v-model="currentForm.selectedIconColor"
                      :predefine="predefineColors"></el-color-picker>
                  </div>
                </div>
                <div class="set-box">
                  <span class="set-status">{{ i18n('未选') }}</span>
                  <div class="set-block">
                    <span class="demonstration">{{ i18n('文本色') }}</span>
                    <el-color-picker size="mini" @change="doFormChange" v-model="currentForm.unselectedFontColor"
                      :predefine="predefineColors"></el-color-picker>
                  </div>
                  <div class="set-block">
                    <span class="demonstration">{{ i18n('图标色') }}</span>
                    <el-color-picker size="mini" @change="doFormChange" v-model="currentForm.unselectedIconColor"
                      :predefine="predefineColors"></el-color-picker>
                  </div>
                </div>
              </div>
            </div>
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <div class="form-title">{{ i18n('菜单设置') }}</div>
      <el-form-item :label="i18n('菜单数量')" prop="menuQuantity" v-if="currentForm.navigationType === 'rudder'">
        <el-radio-group @change="doFormChange" v-model="currentForm.menuQuantity">
          <el-radio :label="3" class="radio-block">
            3{{ i18n('个') }}
          </el-radio>
          <el-radio :label="5" class="radio-block">
            5{{ i18n('个') }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="i18n('菜单配置')">
        <div class="menu-set" v-for="(item, index) in currentForm.menuSettings" :key="index">
          <div class="menu-title">
            {{ i18n('菜单') }}{{ index + 1 }}
            <span class="sub-title"
              v-if="currentForm.navigationType === 'rudder' && ((index == 1 && currentForm.menuSettings.length == 3) || (index == 2 && currentForm.menuSettings.length == 5))">{{
                i18n('默认为中心菜单') }}</span>
          </div>
          <el-col :span="18">
            <el-form-item :label="i18n('菜单名称')" :prop="`menuSettings.${index}.name`" :rules="rules.menuName">
              <el-input show-word-limit type="text" v-model="item.name" :maxlength="channel==='h5' ? 10 : 4"></el-input>
            </el-form-item>
          </el-col>
          <!-- 中心菜单 -->
          <el-col :span="18"
            v-if="currentForm.navigationType === 'rudder' && ((index == 1 && currentForm.menuSettings.length == 3) || (index == 2 && currentForm.menuSettings.length == 5))">
            <el-form-item :label="i18n('菜单图标')" class="menu-icon" :prop="`menuSettings.${index}`"
              :rules="rules.menuIcon">
              <!-- 已有菜单图片的状态 -->
              <p class="rudder-tips">{{ i18n('中心菜单仅支持自定义图标，不支持系统图标') }}</p>
              <div class="center-upload">
                <div>
                  <upload-img :index="index" :imgUrl="item.unselectedIcon" @afterSuccess="rudderUnHandleChange"
                    @afterDel="delCenterUnImg"></upload-img>
                  <span class="upload-font">{{ i18n('未选中') }}</span>
                </div>
                <div>
                  <upload-img :index="index" :imgUrl="item.selectedIcon" @afterSuccess="rudderHandleChange"
                    @afterDel="delCenterImg"></upload-img>
                  <span class="upload-font">{{ i18n('选中') }}</span>
                </div>
              </div>
            </el-form-item>
          </el-col>
          <!-- 普通菜单 -->
          <el-col :span="18" v-else>
            <el-form-item :label="i18n('菜单图标')" class="menu-icon" :prop="`menuSettings.${index}`"
              :rules="rules.menuIcon">
              <!-- 已有菜单图片的状态 -->
              <div @mouseenter="item.showRepealce = true" @mouseleave="item.showRepealce = false" class="icon-has"
                v-if="item.selectedIcon || item.unselectedIcon">
                <div class="img-box" @click="doCheckedIcon(item, index)">
                  <div v-if="item.unselectedIcon">
                    <i v-if="item.isUseIcon" :class="['iconfont', item.unselectedIcon]"
                      :style="{ color: currentForm.iconColorType === 'custom' ? currentForm.unselectedIconColor : currentForm.unselectedColor }"
                      style="font-size: 24px;"></i>
                    <img v-else :src="item.unselectedIcon" class="icon-img">
                  </div>
                  <span class="line"></span>
                  <div v-if="item.selectedIcon">
                    <i v-if="item.isUseIcon" :class="['iconfont', item.selectedIcon]"
                      :style="{ color: currentForm.iconColorType === 'custom' ? currentForm.selectedIconColor : currentForm.selectedColor }"
                      style="font-size: 24px;"></i>
                    <img v-else :src="item.selectedIcon" class="icon-img">
                  </div>
                </div>
                <div class="img-tips">
                  <span class="upload-font">{{ i18n('未选中') }}</span>
                  <span class="upload-font">{{ i18n('选中') }}</span>
                </div>
                <div class="replace-box" v-show="item.showRepealce">
                  <span>{{ item.isUseIcon ? i18n('替换图标') : i18n('替换图片') }}</span>
                </div>
              </div>
              <!-- 需要上传菜单图片的状态 -->
              <div class="icon-none" v-else>
                <div class="upload-box" @click="doCheckedIcon(item, index)">
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                  <span class="avatar-uploader-title">{{ i18n('上传图片') }}</span>
                </div>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item class="page-box" :label="i18n('跳转链接')"
              :prop="`menuSettings.${index}.jumpPageInfo.secondLevelPage`" :rules="rules.menuLink">
              <div class="menu-link">
                <!-- 左边使用链接组件 -->
                <div class="link-box">
                  <!-- <div @click="changeLinkDialog(index)" style="cursor: pointer">{{ item.jumpPageInfo && item.jumpPageInfo.templateName? `${item.jumpPageInfo.firstLevelPage} ${item.jumpPageInfo.templateName}` : '请选择跳转页面' }}<i class="el-icon-arrow-right"></i></div> -->
                  <page-jump ref="jumpPage" v-model="item.jumpPageInfo" @change="changeLink($event, index)"
                    :advertiseChannel="[channel]"
                    :showTitile="true"></page-jump>
                </div>
                <!-- 右边操作按钮组 -->
                <div class="menu-operation" v-if="currentForm.navigationType === 'normal'">
                  <span :class="{ 'gray': index === 0 }" @click="menuChange('up', index)"
                    :style="{ 'word-break': isEnglish ? 'keep-all' : 'normal' }">{{ i18n('上移') }}</span>
                  <span :class="{ 'gray': index === (currentForm.menuSettings.length - 1) }"
                    @click="menuChange('down', index)" :style="{ 'word-break': isEnglish ? 'keep-all' : 'normal' }">{{
                    i18n('下移') }}</span>
                  <span @click="menuChange('remove', index)" :class="{ 'gray': currentForm.menuSettings.length <= 2 }"
                    :style="{ 'word-break': isEnglish ? 'keep-all' : 'normal' }">{{ i18n('删除') }}</span>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </div>

        <div :class="{ 'gray': currentForm.menuSettings.length >= 5, 'menu-add': true }"
          v-if="currentForm.navigationType === 'normal'" @click="menuChange('add')">+{{ i18n('添加菜单（最多添加5个）') }}</div>
      </el-form-item>
    </el-form>
    <!-- 考虑后续抽出组件 -->
    <el-dialog :title="i18n('选择图标')" :visible.sync="uploadDialogVisible" width="800px"
      :class="{ 'english-active': isEnglish }">
      <el-tabs tab-position="left" v-model="dialogModelName">
        <el-tab-pane :label="i18n('系统图标')" name="system">
          <p class="system-title">{{ i18n('当前图标风格') }}: {{ currentForm.navigationType === 'normal' ?
            i18n('通用') :i18n('舵式')}}</p>
          <ul class="icon-container" v-if="currentForm.navigationType === 'normal'">
            <li class="system-icon-box" :class="{ 'active': key.ischecked }" @click="doSelectedIcon('normal', key)"
              v-for="(key) in demoMenuIconsCommon" :key="key.icon">
              <i :class="['iconfont', key.icon]" style="font-size: 24px;"></i>
              <img class="checked-icon" src="~assets/image/navcustom/ic_selected_fill.png">
            </li>
          </ul>
          <ul class="icon-container" v-if="currentForm.navigationType === 'rudder'">
            <li class="system-icon-box" :class="{ 'active': key.ischecked }" @click="doSelectedIcon('rudder', key)"
              v-for="(key) in demoMenuIconsRudder" :key="key.icon">
              <i :class="['iconfont', key.icon]" style="font-size: 24px;"></i>
              <img class="checked-icon" src="~assets/image/navcustom/ic_selected_fill.png">
            </li>
          </ul>
        </el-tab-pane>
        <el-tab-pane :label="i18n('自定义图标')" name="custom">
          <!-- 上传自定义未选中 选中 -->
          <div class="upload-main">
            <div class="unselected-box">
              <upload-img ref="dialogUnUploadDom" :index="menuOperationIndex" :imgUrl="menuOperationUnSelectedIcon"
                @afterSuccess="dialogUnUploadImg"></upload-img>
              <span class="upload-status">{{ i18n('未选中图标') }}</span>
              <p class="upload-tips" :style="{ 'word-break': isEnglish ? 'keep-all' : 'normal' }">{{
                i18n('支持JPG,PNG和GIF格式') }}
                {{ i18n('尺寸建议200px*200px') }}</p>
            </div>
            <div class="selected-box">
              <upload-img ref="dialogUploadDom" :index="menuOperationIndex" :imgUrl="menuOperationSelectedIcon"
                @afterSuccess="dialogUploadImg"></upload-img>
              <span class="upload-status">{{ i18n('选中图标') }}</span>
              <p class="upload-tips" :style="{ 'word-break': isEnglish ? 'keep-all' : 'normal' }">{{
                i18n('支持JPG,PNG和GIF格式') }}
                {{ i18n('尺寸建议200px*200px') }}</p>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer" class="dialog-footer">
        <el-button @click="doCancel">{{ i18n('取消') }}</el-button>
        <el-button type="primary" @click="doDialogComfirm">{{ i18n('确定') }}</el-button>
      </span>
    </el-dialog>
    <!-- 跳转链接弹层 -->
    <!-- <jump-page ref="jumpPage" @changeLink="changeLink" :JumpPageInfo.sync="customForm.menuSettings[menuOperationIndex].jumpPageInfo"></jump-page> -->
  </div>
</template>

<script lang="ts" src="./CustomTab.ts">
</script>

<style lang="scss" scoped>
.custom-tab-container {
  width: 645px;
  margin-left: 50px;

  .page-box {
    display: flex;
    align-items: center;

    ::v-deep .el-form-item__content {
      margin-left: 0 !important;
    }
  }

  .form-title {
    font-weight: 500;
    font-size: 16px;
    color: #24272b;
    margin-bottom: 6px;
  }

  .nav-style-img {
    display: block;
    margin-top: 8px;
  }

  .set-color {
    width: 200px;
    height: 120px;
    background: #f0f2f6;
    border-radius: 2px;
    display: flex;
    flex-direction: column;
    padding: 16px 12px;
    box-sizing: border-box;
    margin-left: 10px;

    .set-status {
      font-weight: 500;
      font-size: 14px;
      color: #24272b;
      margin-bottom: 12px;
    }
  }

  .set-color-box {
    margin-top: 8px;
    display: flex;
  }

  .set-system,
  .set-rudder {
    position: absolute;
    left: 0;
    top: 20px;

    .set-status {
      font-weight: 500;
      font-size: 14px;
      color: #24272b;
    }

    .demonstration {
      font-size: 14px;
      color: #5a5f66;
    }
  }

  .custom {
    left: -102px;
  }

  .set-system {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    .set-block {
      width: 112px;
      height: 52px;
      background: #f0f2f6;
      border-radius: 2px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 12px;
      margin-right: 24px;
    }
  }

  .set-rudder {
    .set-box {
      flex-direction: column;
      width: 200px;
      height: 120px;
      background: #f0f2f6;
      border-radius: 2px;
      align-items: flex-start;
      padding: 12px;
      box-sizing: border-box;
      margin-right: 24px;

      .set-block {
        height: 42px;
        border-radius: 2px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 12px;

        .demonstration {
          font-size: 14px;
          color: #5a5f66;
        }
      }
    }
  }
}

.menu-set {
  width: 460px;
  min-height: 244px;
  background: #f0f2f6;
  border-radius: 4px;
  padding: 12px;
  box-sizing: border-box;
  margin-bottom: 12px;
  overflow: hidden;

  .el-col {
    &:first-child {
      margin-bottom: 0;
    }

    &:last-child {
      margin-bottom: 12px;
    }
  }

  .menu-title {
    font-weight: 500;
    font-size: 14px;
    color: #24272b;

    .sub-title {
      font-weight: 400;
      font-size: 14px;
      color: #5a5f66;
    }
  }

  // 上传已有图片部分
  .icon-has {
    position: relative;
    cursor: pointer;

    .img-box {
      width: 160px;
      height: 80px;
      background: #ffffff;
      border-radius: 2px;
      border: 1px solid #d7dfeb;
      display: flex;

      >div {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;

        .icon-img {
          width: 80px;
        }
      }

      .line {
        position: absolute;
        width: 1px;
        height: 40px;
        background: #d7dfeb;
        left: 80px;
        top: 20px;
      }
    }

    .img-tips {
      width: 160px;
      display: flex;
    }

    .replace-box {
      width: 160px;
      height: 80px;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 2px;
      position: absolute;
      left: 0;
      top: 0;
      pointer-events: none;

      span {
        display: block;
        margin: 23px auto;
        width: 74px;
        height: 32px;
        background: #ffffff;
        border-radius: 2px;
        border: 1px solid #d7dfeb;
        font-size: 13px;
        color: #36445a;
        line-height: 32px;
        text-align: center;
      }
    }
  }

  // 上传图标部分
  .icon-none {
    display: flex;
    cursor: pointer;

    .upload-box {
      width: 80px;
      height: 80px;
      background: #ffffff;
      border-radius: 2px;
      border: 1px dashed #d7dfeb;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      color: #a1a6ae;
      line-height: 26px;
    }
  }

  .menu-icon {
    margin-top: 12px;

    ::v-deep .el-form-item__content {
      margin-top: 6px;
    }

    .rudder-tips {
      font-size: 14px;
      color: #354052;
      text-wrap: nowrap;
      line-height: 28px;
      margin-bottom: 10px;
    }

    .center-upload {
      display: flex;

      div {
        width: 80px;
        text-align: center;
        margin-right: 10px;
      }
    }
  }

  .upload-font {
    flex: 1;
    text-align: center;
    width: 36px;
    font-size: 12px;
    color: #a1a6ae;
    line-height: 20px;
  }

  .menu-link {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .link-box {
      cursor: pointer;
      font-size: 13px;
      color: #007eff;
    }

    .menu-operation {
      display: flex;

      span {
        cursor: pointer;
        font-size: 13px;
        color: #007eff;
        margin-left: 12px;
      }
    }
  }
}

.menu-add {
  width: 480px;
  height: 36px;
  line-height: 36px;
  border-radius: 4px;
  border: 1px dashed#007EFF;
  font-family: PingFangSC, PingFang SC;
  font-size: 12px;
  color: #007eff;
  text-align: center;
  font-style: normal;
  cursor: pointer;
}

.gray {
  color: #cccccc !important;
  pointer-events: none !important;
}

// 弹层部分样式 （写完再考虑处理成组件）
::v-deep .el-tabs {
  width: 752px;
  height: 388px;
  background: #ffffff;
  border-radius: 2px;
  border: 1px solid #d7dfeb;

  .el-tabs__header {
    padding: 12px;
    border-right: 1px solid #dde2eb;

    .el-tabs__nav-scroll {
      padding: 8px;
      background: #f9fafc;
      border-radius: 4px;
    }

    .el-tabs__nav-wrap::after {
      background: transparent;
    }

    .el-tabs__item {
      margin-bottom: 9px;
    }

    .el-tabs__item.is-active {
      width: 120px;
      height: 41px;
      background: #007eff;
      border-radius: 4px;
      font-size: 14px;
      color: #ffffff !important;
      line-height: 41px;
      text-align: left;
    }
  }
}

::v-deep {
  .el-tabs--left .el-tabs__header.is-left {
    margin-right: 0;
  }
}

.system-title {
  background: rgba(0, 0, 0, 0.1);
  font-size: 16px;
  line-height: 50px;
  padding-left: 25px;
}

.icon-container {
  padding: 15px;
  display: flex;
  flex-wrap: wrap;
}

.system-icon-box {
  width: 80px;
  height: 80px;
  background: #ffffff;
  border-radius: 4px;
  list-style: none;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 11px;

  .checked-icon {
    width: 20px;
    position: absolute;
    bottom: 0;
    right: 0;
    display: none;
  }

  &.active {
    border: 1px solid #007eff;

    .checked-icon {
      display: block;
    }
  }
}

.upload-main {
  display: flex;
  height: 100%;

  .unselected-box {
    border-right: 1px solid #dde2eb;
  }
}

.upload-container {
  width: 80px;
  height: 80px;
  background: #ffffff;
  border-radius: 2px;
  border: 1px solid #d7dfeb;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  ::v-deep .el-upload {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    color: #a1a6ae;
  }
}

.unselected-box,
.selected-box,
.center-box {
  height: 388px;
  padding: 0 70px;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .upload-status {
    font-weight: 500;
    font-size: 14px;
    color: #24272b;
    margin: 16px 0;
  }

  .upload-tips {
    max-width: 150px;
    font-size: 13px;
    color: #a1a6ae;
  }
}

.english-active ::v-deep .el-tabs {
  .el-tabs__item.is-active {
    width: 150px;
  }
}
</style>