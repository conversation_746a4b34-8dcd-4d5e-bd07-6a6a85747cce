/*
 * @Author: haiding <EMAIL>
 * @Date: 2025-02-19 10:03:52
 * @LastEditors: haiding <EMAIL>
 * @LastEditTime: 2025-03-07 09:40:37
 * @FilePath: \new-kequn\src\pages\invest\page-manage\cmp\share-config-dialog\ShareConfigDialog.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import UploadImg from 'cmp/upload-img/UploadImg';
import I18nPage from 'common/I18nDecorator';
import AppShareInfo from 'model/template/AppShareInfo';
import { Component, Vue } from 'vue-property-decorator';

@Component({
  name: 'ShareConfigDialog',
  components: {
    UploadImg
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理'
  ],
  auto: true
})
export default class ShareConfigDialog extends Vue {
  visible: boolean = false
  formData: AppShareInfo = new AppShareInfo()
  isEdit: boolean = false
  templateIds: string[]

  get title() {
    return this.isEdit ? this.i18n('修改分享信息') : this.i18n('分享信息')
  }

  get rules() {
    return {
      isShare: [
        { required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: ['blur', 'change'] }
      ]
    }
  }

  open(ids: string[], type?: 'edit', originValue?: AppShareInfo) {
    this.templateIds = ids || []
    if (type === 'edit') {
      this.isEdit = true
      this.formData = originValue || new AppShareInfo()
    }
    this.visible = true
  }

  close() {
    this.visible = false
    this.isEdit = false
    this.formData = new AppShareInfo()
  }

  doConfirm() {
    this.$emit('submit', this.templateIds, JSON.parse(JSON.stringify(this.formData)))
  }
};