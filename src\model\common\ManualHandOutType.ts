/*
 * @Author: 申鹏渤
 * @Date: 2023-12-05 09:38:26
 * @LastEditTime: 2024-10-09 15:16:43
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\common\ManualHandOutType.ts
 * 记得注释
 */
export enum ManualHandOutType {
  // 
  IMPORT_MEMBER = 'IMPORT_MEMBER',
  // 
  MEMBER_PROP = 'MEMBER_PROP',
  // 
  ENTER_MOBILE = 'ENTER_MOBILE',
  // 
  MEMBER_TAG = 'MEMBER_TAG',
  //
  USER_GROUP_AND_MEMBER_TAG = 'USER_GROUP_AND_MEMBER_TAG'
}