import { Component, Vue } from "vue-property-decorator";
import EnvUtil from "util/EnvUtil.js";
import PhoenixButton from "cmp/button/PhoenixButton.vue";
import LoginApi from "http/login/LoginApi";
import UserLogin from "model/login/UserLogin";
import SysConfigApi from "http/config/SysConfigApi";
import BrowserMgr, { LocalStorage, SessionStorage } from "mgr/BrowserMgr";
import CommonUtil from "util/CommonUtil.js";
import I18nApi from "http/i18n/I18nApi";
import { Action } from "vuex-class";
import PermissionMgr from "mgr/PermissionMgr";
import CryptoJS from "crypto-js";
import ChangePwd from "cmp/changepwd/ChangePwd";
import UserLoginResult from "model/login/UserLoginResult";
import User from "model/user/User";
import MarketingCenterApi from "http/marketingcenter/MarketingCenterApi";
import RSMarketingCenter from "model/common/RSMarketingCenter";
import I18nPage from "common/I18nDecorator";
import BWebAppearanceConfig from "model/systemConfig/BWebAppearanceConfig";
import { BAppearanceType } from "model/common/BAppearanceType";
import ShortcutMgr from "mgr/ShortcutMgr";


@Component({
  components: {
    PhoenixButton,
    ChangePwd,
  },
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/登录/用户名未输入时输入其它内容点击保存',
    '/登录/密码未输入时输入其它内容点击保存',
    '/登录/验证码未输入时输入其它内容点击保存',
    '/登录'
  ],
  auto: true
})
export default class Login extends Vue {
  $t: any;
  name = "";
  password = "";
  code = "";
  resetToken = ""; // 密码过期，修改密码时使用的token
  useStrict = false; // 严格校验密码
  showNameImage = false;
  showPasswordImage = false;
  showCodeImage = false;
  showWarning = false;
  showEyeImage = false;
  warnMsg = "";
  $refs: any;
  imgPath = "";
  createUuid = CommonUtil.uuid();
  language = "";
  languageNames: any = [];
  languageFlag: boolean = false;
  @Action("i18n") actionI18n: any;
  @Action("loginInfo") setLoginInfo: any;
  @Action("permissions") actionPermissions: any;
  sysConfig: any = {};
  webAppearanceConfig: BWebAppearanceConfig = this.defaultBWebAppearanceConfig();;

  showPwdDialog: boolean = false; // 显示修改弹框
  orgList: any[] = [];


  defaultBWebAppearanceConfig(): BWebAppearanceConfig {
    return {
      logo: null,
      title: null,
      appearanceTypes: BAppearanceType.all,
    };
  }

  get projectVersion() {
    return 'V' + LocalStorage.getItem('version')
  }

  get getNameFlag() {
    if (this.name && this.name.length > 0) {
      return true;
    } else {
      return false;
    }
  }

  get getPwdFlag() {
    if (this.password && this.password.length > 0) {
      return true;
    } else {
      return false;
    }
  }

  get showTip() {
    return this.name && this.password && this.code;
  }

  get getYear() {
    return new Date().getFullYear();
  }

  created() {
    this.createUuid = CommonUtil.uuid();
    this.imgPath = EnvUtil.getServiceUrl() + `v1/login/captchaCode/${this.createUuid}`;
    this.getImg();
    this.checkIfLogin().finally(() => { //先清除所有缓存数据，再重新获取
      this.getLanguageNames();
      this.getSysConfig();
    })
  }

  mounted() {
    this.$refs.name.focus();
  }

  // 不论以什么方式进入到登录页面，都需要清除登录信息
  checkIfLogin() {
    if (SessionStorage.getItem('isUniLogin')) {
      // 如果是uni登录方式，则不进行登录信息清除
      return Promise.resolve();
    }
    return LoginApi.loginOut()
      .then((resp: any) => {
        if (resp.code === 2000) {
          ShortcutMgr.clearStorage();
        }
      })
      .catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
  }

  doFocus(str: string) {
    if (str === "name") {
      this.showNameImage = true;
    } else if (str === "password") {
      this.showPasswordImage = true;
    } else {
      this.showCodeImage = true;
    }
  }

  doBlur(str: string) {
    if (str === "name") {
      this.showNameImage = false;
    } else if (str === "password") {
      this.showPasswordImage = false;
    } else {
      this.showCodeImage = false;
    }
  }

  onLogin() {
    if (!this.doCheck()) {
      return;
    }
    this.doLogin();
  }

  doCheck() {
    if (!this.name) {
      this.showWarning = true;
      this.warnMsg = this.i18n("请输入用户名") as string;
      return false;
    }

    if (!this.password) {
      this.showWarning = true;
      this.warnMsg = this.i18n("请输入密码") as string;
      return false;
    }
    if (!this.code) {
      this.showWarning = true;
      this.warnMsg = this.i18n("请输入验证码") as string;
      return false;
    }
    return true;
  }

  getOrgList() {
    let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
    let promotionCenter = false;
    if (sysConfig) {
      promotionCenter = sysConfig.enableMultiMarketingCenter;
    }

    if (promotionCenter) {
      const account = JSON.parse(sessionStorage.getItem("vuex") as string)?.loginInfo.user?.account;

      return MarketingCenterApi.queryUserMarketCenter(account).then((res) => {
        if (res.code === 2000) {
          this.orgList = res.data || [];
          const item: Nullable<RSMarketingCenter> = this.orgList ? this.orgList[0] : null;
          const org = item!.marketingCenter!.id || "";
          const orgName = item!.marketingCenter!.name || "";
          const headquarters = item!.headquarters
          sessionStorage.setItem("marketCenter", org);
          sessionStorage.setItem("marketCenterName", orgName);
          sessionStorage.setItem("headquarters", headquarters ? 'true' : 'false');
          return Promise.resolve()
        } else {
          throw new Error(res.msg || this.i18n('查询营销中心失败'))
        }
      }).catch((error) => this.$message.error(error.message || this.i18n('内部异常')))
    } else {
      return Promise.resolve();
    }
  }

  /**
   * 登录
   */
  doLogin() {
    let params = this.buildParams();
    LoginApi.login(params)
      .then((resp) => {
        if (resp.code === 2000 && resp.data!.state === "NORMAL") {
          this.setLoginInfo(resp.data);
          console.log('登录成功了');
          localStorage.setItem("customer", resp.data!.customer!);
          localStorage.setItem("ucenterUser", JSON.stringify(resp.data!.user));
          localStorage.setItem("fileBaseUrl", EnvUtil.getServiceUrl());
          sessionStorage.setItem("needRemindExpire", "true")
          this.getOrgList().then(() => {
            PermissionMgr.refreshPermission().then((menus: any[]) => {
              if (
                !menus ||
                menus.length === 0 || //
                !menus[0].children[0] ||
                menus[0].children[0].length === 0 || //
                !menus[0].children[0].children[0] ||
                menus[0].children[0].children[0].length === 0
              ) {
                this.$router.push({ name: "home", query: { from: "login" } });
              } else {
                this.$router.push({ name: menus[0].children[0].children[0].hash, query: { from: "login" } });
              }
            }).catch((error) => {
              this.$message.error(error)
            });
          });
        } else if (resp.data && resp.data!.useStrict) {
          let loginInfo = new UserLoginResult();
          loginInfo.resetToken = resp.data!.resetToken;
          loginInfo.useStrict = resp.data!.useStrict;
          loginInfo.user = new User();
          loginInfo.user.account = params.account;
          this.setLoginInfo(loginInfo);
          this.resetToken = resp.data!.resetToken!;
          this.useStrict = resp.data!.useStrict!;
          if (resp.data!.state === "PWD_EXPIRE") {
            this.$alert(this.formatI18n("/登录/你的密码已到期，请修改密码后，重新登录"), this.formatI18n("/公用/提示/提示"), {
              confirmButtonText: this.formatI18n("/公用/按钮/确定"),
            } as any).then(() => {
              this.showPwdDialog = true;
            });
          } else if (resp.data!.state === "LOGIN_EXPIRE") {
            this.$alert(this.formatI18n("/登录/你的账户长时间未登录，请修改密码后，重新登录"), this.formatI18n("/公用/提示/提示"), {
              confirmButtonText: this.formatI18n("/公用/按钮/确定"),
            } as any).then(() => {
              this.showPwdDialog = true;
            });
          }
        } else if (resp.data && resp.data!.state === "NEW_ACCOUNT") {
          let info = {
            account: params.account,
          };
          localStorage.setItem("ucenterUser", JSON.stringify(info));
          let loginInfo = new UserLoginResult();
          loginInfo.resetToken = resp.data!.resetToken;
          loginInfo.user = new User();
          loginInfo.user.account = params.account;
          this.setLoginInfo(loginInfo);
          this.$alert(this.formatI18n("初次登录请修改密码后再登录!"), this.formatI18n("/公用/提示/提示"), {
            confirmButtonText: this.formatI18n("/公用/按钮/确定"),
          } as any).then(() => {
            this.showPwdDialog = true;
          });
        } else if (resp.data && resp.data!.state === "PWD_INCORRECT_FORMAT") {
          let info = {
            account: params.account,
          };
          localStorage.setItem("ucenterUser", JSON.stringify(info));
          let loginInfo = new UserLoginResult();
          loginInfo.resetToken = resp.data!.resetToken;
          loginInfo.user = new User();
          loginInfo.user.account = params.account;
          this.setLoginInfo(loginInfo);
          this.$alert(this.formatI18n("密码至少8位，且包含大小写字母，数字，特殊字符"), this.formatI18n("/公用/提示/提示"), {
            confirmButtonText: this.formatI18n("/公用/按钮/确定"),
          } as any).then(() => {
            this.showPwdDialog = true;
          });
        } else if (resp.code !== 2000) {
          this.doChangeImg();
          this.$message.error(resp.msg as string);
        }
        SessionStorage.setItem('isUniLogin', false)
      })
      .catch((error) => {
        this.doChangeImg();
        if (error && error.message) {
          this.$message.error(error.message);
        }
        if (error && error.msg) {
          this.$message.error(error.msg);
        }
      });
  }

  changeCall(isSuccess: boolean) {
    if (isSuccess) {
      this.showPwdDialog = false;
      this.name = "";
      this.password = "";
      this.code = "";
      this.doChangeImg();
    }
  }

  buildParams() {
    let iv = this.createUuid.substr(0, 16);
    let encryptKey = iv.substr(0, 12) + "" + this.code;
    let params: UserLogin = new UserLogin();
    params.account = this.name;
    params.password = this.encrypt(this.password, encryptKey, iv);
    params.captchaCode = this.code;
    params.captchaUid = this.createUuid;
    return params;
  }

  encrypt(message: string, key: string, iv: string) {
    // utf8字符串—>WordArray对象，WordArray是一个保存32位整数的数组，相当于转成了二进制
    let keyHex = CryptoJS.enc.Utf8.parse(key); //
    let ivHex = CryptoJS.enc.Utf8.parse(iv);
    let messageHex = CryptoJS.enc.Utf8.parse(message);
    // let encrypted = // base64结果
    // return encrypted.toString() ;   // 二进制结果
    // console.log('message', message, 'key', key, 'iv', iv, 'encrypted', encrypted)
    return CryptoJS.AES.encrypt(messageHex, keyHex, {
      iv: ivHex,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    }).ciphertext.toString();
  }

  /**
   * 回车事件
   * @param event
   * @param type
   */
  onConfirm(event: any, type: string) {
    if (event.which === 13) {
      switch (type) {
        case "name":
          this.$refs.pwd.focus();
          break;
        case "pwd":
          this.$refs.code.focus();
          break;
        case "code":
          !this.$refs.login.disabled && this.$refs.login.$el.click();
          break;
      }
    }
  }

  /**
   * 获取新验证码
   */
  doChangeImg() {
    this.createUuid = CommonUtil.uuid();
    this.getImg();
  }

  doCheckPwd() {
    this.showEyeImage = !this.showEyeImage;
  }

  doDeleteName() {
    this.name = "";
  }

  doDeletePassword() {
    this.password = "";
  }

  doChangeLan() {
    sessionStorage.setItem("locale", this.language);
    I18nApi.gets(sessionStorage.getItem("locale") as any).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.actionI18n(resp.data);
        window.location.reload();
      }
    });
  }

  getUserNameI18n() {
    try {
      return this.formatI18n("/登录/请输入您的用户名");
    } catch (e) { }
  }

  getPwdI18n() {
    try {
      return this.formatI18n("/登录/请输入您的密码");
    } catch (e) { }
  }

  getCodeI18n() {
    try {
      return this.formatI18n("/登录/验证码");
    } catch (e) { }
  }

  getLoginI18n() {
    try {
      return this.i18n("登录");
    } catch (e) {
      console.log('登录报错', e);

    }
  }

  getHdI18n() {
    try {
      return this.formatI18n("/公用/系统/上海海鼎公司.版权所有.");
    } catch (e) { }
  }

  private getImg() {
    this.imgPath = EnvUtil.getServiceUrl() + `v1/login/captchaCode/${this.createUuid}?timestamp=` + new Date().getTime();
  }

  private getSysConfig() {
    SysConfigApi.get()
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          let data = resp.data;
          LocalStorage.setItem("sysConfig", data);
          this.sysConfig = resp.data;
          this.webAppearanceConfig = this.sysConfig?.crmConfig;
          // console.log("webAppearanceConfig:", this.webAppearanceConfig)
          if (this.webAppearanceConfig == null || (!this.webAppearanceConfig.title && !this.webAppearanceConfig.logo)) {
            console.log("111")
            document.title = '海鼎CRM';
            var link = document.querySelector("link[rel*='icon']") || document.createElement('link');
            link.setAttribute("rel", "icon");
            link.setAttribute("href", './favicon.ico')
            document.getElementsByTagName('head')[0].appendChild(link);
          } else {
            document.title = '';
            if (this.webAppearanceConfig && this.webAppearanceConfig.title && this.webAppearanceConfig.title) {
              document.title = this.webAppearanceConfig.title;
            }
            if (this.webAppearanceConfig && this.webAppearanceConfig.logo != null && this.webAppearanceConfig.logo) {
              var link = document.querySelector("link[rel*='icon']") || document.createElement('link');
              link.setAttribute("rel", "icon");
              link.setAttribute("href", this.webAppearanceConfig.logo)
              document.getElementsByTagName('head')[0].appendChild(link);
            }
          }
          if (this.sysConfig?.portalHomeUrl) {
            // 重定向为外部链接
            window.location.href = this.sysConfig?.portalHomeUrl;
          }
        }
      })
      .catch((error: any) => {
        // this.$message.error(error.message)
      });
  }

  private getLanguageNames() {
    let that = this;
    I18nApi.language()
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          if (resp.data) {
            resp.data.forEach(function (item: any) {
              let name: any = {
                key: item.code,
                value: item.name ? item.name : item.code
              };
              that.languageNames.push(name);
            });
          }
          // console.log(that.languageNames)
          this.language = CommonUtil.getLocale("locale") as any;
          if (!this.language && this.languageNames && this.languageNames.length > 0) {
            this.language = this.languageNames[0].key;
            sessionStorage.setItem("locale", this.language);
          }
          if (this.languageNames && (this.languageNames.length === 0 || (this.languageNames.length === 1 && this.languageNames[0].key === "zh_CN"))) {
            this.languageFlag = false;
          } else {
            this.languageFlag = true;
          }
        }
      })
      .catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
  }
}
