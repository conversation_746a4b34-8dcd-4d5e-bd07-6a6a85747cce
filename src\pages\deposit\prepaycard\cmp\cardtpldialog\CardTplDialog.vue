<template>
  <el-dialog title="选择卡模板" class="select-cardtpl-dialog"
            append-to-body width="1000px"
             :close-on-click-modal="false" :visible.sync="dialogShow" :before-close="doBeforeClose">
    <div class="wrap">
      <el-form :inline="true" style="white-space: nowrap" label-width="160px">
        <el-row>
          <el-col :span="colVal">
            <el-form-item label="卡模板号/名称">
              <el-input v-model="query.numberOrNameLikes" placeholder="请输入卡模板号/名称"  style="width: auto"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="colVal" v-if="headquarters === false && canSelectCenter">
						<!-- <el-form-item :label="formatI18n('/公用/公共组件/商品选择弹框组件/查询/单品来源')"> -->
						<el-form-item :label="formatI18n('/营销/券礼包活动/券查询/卡模板来源')">
							<el-select v-model="marketCenter" :placeholder="formatI18n('/资料/渠道/请选择')" style="width: auto" @change="doSearch()">
								<el-option v-for="(value,index) in marketingCentersList" :key="index" :value="value.marketingCenter.id" :label="'['+value.marketingCenter.id+']'+value.marketingCenter.name">[{{value.marketingCenter.id}}]{{value.marketingCenter.name}}</el-option>
							</el-select>
						</el-form-item>
					</el-col>
          <el-col :span="8" style="text-align: left">
            <el-form-item>
              <el-button type="primary" @click="doSearch">查询</el-button>
              <el-button @click="doReset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div :class="{'table-wrap': true, 'multiple': !isSelectMultiple}">
        <el-table
            ref="storeTable"
            :data="queryData"
            border
            v-loading="loading"
            @selection-change="handleSelectionChange"
            @select="handleSelect"
            @select-all="handleSelectAll"
            style="width: 100%;margin-top: 20px"
            :row-key="getRowKey">
            <!-- @current-change="selectTpl" highlight-current-row -->
            <el-table-column
              reserve-selection
              :selectable="rowCanSelect"
              align='center'
              type="selection"
              width="50">
            </el-table-column>
          <el-table-column label="卡模板号" prop="number">
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.number}}</span>
            </template>
          </el-table-column>
          <el-table-column label="名称" prop="name">
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.name}}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="page" style="margin-top: 15px">
      <el-pagination
          no-i18n
          :current-page="page.currentPage"
          :page-size="page.size"
          :page-sizes="[10, 20, 30, 40]"
          :total="page.total"
          @current-change="onHandleCurrentChange"
          @size-change="onHandleSizeChange"
          background
          layout="total, prev, pager, next, sizes,  jumper">
      </el-pagination>
    </div>
    <div slot="footer" class="dialog-footer">
      <!--<el-button @click="dialogFormVisible = false">取 消</el-button>-->
      <el-button size="small" type="primary" @click="doModalClose">确定</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./CardTplDialog.ts">
</script>

<style lang="scss" scoped>
.select-cardtpl-dialog{
  display: flex;
  align-items: center;
  justify-content: center;
  .el-dialog{
    width: 1024px;
    height: 840px;
    margin: 0 !important;
  }
  .wrap{
    height: 440px;
    overflow: auto;
    .item{
      width: 228px;
      height: 108px;
      border: 1px solid #c7c7c7;
      border-radius: 10px;
      display: inline-block;
      margin-bottom: 24px;

      &:nth-child(odd) {
        margin-right: 12px;

      }
      &:nth-child(even) {
        margin-left: 12px;
      }
    }
  }

  ::v-deep .multiple .el-table .el-table__header-wrapper .el-checkbox {
    display: none;
  }
}
</style>