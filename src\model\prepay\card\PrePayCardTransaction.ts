/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2023-09-08 10:34:37
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\prepay\card\PrePayCardTransaction.ts
 * 记得注释
 */
import IdName from 'model/common/IdName'

export default class PrePayCardTransaction {
  // 交易时间
  occurredTime: Nullable<Date> = null
  // 发生组织
  occurredOrg: Nullable<IdName> = null
  // 交易类型
  category: Nullable<string> = null
  // 卡原余额
  originAmount: Nullable<number> = null
  // 发生金额
  occurAmount: Nullable<number> = null
  // 发生后余额
  occurredAmount: Nullable<number> = null
  // 卡原次数
  originTimes: Nullable<number> = null
  // 发生次数
  occurTimes: Nullable<number> = null
  // 发生后次数
  occurredTimes: Nullable<number> = null
  // 交易流水号
  transNo: Nullable<string> = null
}