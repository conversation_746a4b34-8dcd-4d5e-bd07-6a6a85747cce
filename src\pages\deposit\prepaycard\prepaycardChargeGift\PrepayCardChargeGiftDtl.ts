import {Component, Vue} from 'vue-property-decorator'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import GoodsScopeDtl from "cmp/goodsscope/GoodsScopeDtl";
import RSGrade from "model/common/RSGrade";
import Card<PERSON>alan<PERSON>PromotionApi from "http/payment/card/CardBalancePromotionApi";
import CardBalancePromotionActivity from "model/payment/card/CardBalancePromotionActivity";
import ActivityState from "cmp/activitystate/ActivityState";
import EditType from "common/EditType";
import ActiveStoreDtl from "cmp/activestoredtl/ActiveStoreDtl";
import I18nPage from "common/I18nDecorator";
import PrepayCardChargeGiftPermission from "./PrepayCardChargeGiftPermission";
import RoutePermissionMgr from "mgr/RoutePermissionMgr";
import CardBalancePromotionActivityNew from 'model/payment/card/CardBalancePromotionActivityNew';
import CardDepositActivityApi from 'http/cardDepositActivity/CardDepositActivityApi';
import DateTimeConditionDtl from 'cmp/date-time-condition-picker/DateTimeConditionDtl';
import xss from 'xss'
import CouponItem from "model/common/CouponItem";

@Component({
  name: 'PrepayCardChargeGiftDtl',
  components: {
    BreadCrume,
    GoodsScopeDtl,
    ActiveStoreDtl,
    ActivityState,
    DateTimeConditionDtl
  }
})
@I18nPage({
  prefix: [
    '/储值/预付卡/预付卡支付活动/详情页面',
    '/公用/活动/状态',
    '/公用/活动/活动信息',
    '/公用/活动/提示信息',
    '/公用/按钮',
    "/储值/预付卡/预付卡充值有礼"
  ],
})
export default class PrepayCardChargeGiftDtl extends Vue {
  i18n: (str: string, params?: string[]) => string
  activityId: string = ''
  child: CouponItem = new CouponItem();
  data: CardBalancePromotionActivityNew = new CardBalancePromotionActivityNew()
  gradeDifferentStepValueMap: any = {} // grade -> gradeDifferentStepReduction
  gradeList: RSGrade[] = []
  permission = new PrepayCardChargeGiftPermission()
  xss: Function = xss
  $refs: any
  panelArray: any
  locale: any = sessionStorage.getItem('locale')

  created() {
    this.panelArray = [
			{
				name: this.i18n("预付卡充值有礼"),
				url: "prepay-card-charge-gift-list",
			},
			{
				name: this.locale === 'en_US'?'Details of prepaid card recharge and gift activities':this.i18n("预付卡充值有礼活动详情"),
				url: "",
			},
		];
    this.activityId = this.$route.query.activityId as string
    this.getDetail()
  }

  doCheckCoupon(item: any) {
    this.child = item;
    RoutePermissionMgr.openBlank({ name: "coupon-template-dtl", query: { id: this.child!.coupons!.templateId, lastEffected: 'true' } });
  }

  private getDetail() {
    CardDepositActivityApi.info(this.activityId).then((res: any) => {
      if (res.code === 2000) {
        this.data = res.data
      }
    })
  }

  getQty(qty: number) {
    let str: any = this.formatI18n("/营销/券礼包活动/新建注册发大礼包/详情界面/赠送券", "{0}张");
    str = str.replace(/\{0\}/g, " " + qty + " ");
    return str;
  }

  private audit() {
    this.$alert(this.i18n('确认要审核吗？'), this.i18n('提示'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消'),
      type: 'warning',
      callback: (action) => {
        if (action === 'confirm') {
            CardDepositActivityApi.audit(this.activityId).then((resp: any) => {
            if (resp && resp.code === 2000) {
              this.$message.success(this.i18n('审核成功'))
              this.getDetail()
            } else {
              this.$message.error(resp.msg)
            }
          }).catch((error) => {
            if (error && error.message) {
              this.$message.error(error.message)
            }
          })
        }
      }
    });
  }

  private copy() {
      this.$router.push({
        name: 'prepay-card-charge-gift-edit',
        query: {activityId: this.activityId, editType: EditType.COPY}
      })
  }

  private edit() {
      this.$router.push({
        name: 'prepay-card-charge-gift-edit',
        query: {activityId: this.activityId, editType: EditType.EDIT}
      })
  }

  private stop() {
    this.$alert(this.i18n('确认要停止吗？'), this.i18n('提示'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消'),
      type: 'warning',
      callback: (action) => {
        if (action === 'confirm') {
            CardDepositActivityApi.stop(this.activityId).then((resp: any) => {
            if (resp && resp.code === 2000) {
              this.$message.success(this.i18n('停止成功'))
              this.getDetail()
            }
          }).catch((error) => {
            if (error && error.message) {
              this.$message.error(error.message)
            }
          })
        }
      }
    });
  }

  private del() {
    this.$alert(this.i18n('确认要删除吗？'), this.i18n('提示'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消'),
      type: 'warning',
      callback: (action) => {
        if (action === 'confirm') {
            CardDepositActivityApi.remove(this.activityId).then((resp: any) => {
            if (resp && resp.code === 2000) {
              this.$message.success(this.i18n('删除成功'))
              this.$router.push({name:'prepay-card-charge-gift-list'})
            }
          }).catch((error) => {
            if (error && error.message) {
              this.$message.error(error.message)
            }
          })
        }
      }
    });
  }

  private gotoTplDtl(num: string) {
    RoutePermissionMgr.openBlank({name: 'prepay-card-tpl-dtl', query: {number: num}})
  }
}
