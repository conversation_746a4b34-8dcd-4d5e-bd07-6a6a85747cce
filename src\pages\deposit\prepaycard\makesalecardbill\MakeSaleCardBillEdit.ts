import CardPicList from 'cmp/cardpiclist/CardPicList.vue'
import FormItem from 'cmp/formitem/FormItem.vue'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import CardTemplate from 'model/card/template/CardTemplate'
import { Component, Vue } from 'vue-property-decorator'
import CardTplItem from '../cmp/cardtplitem/CardTplItem'
import MakeSaleCardBill from 'model/card/makesalecardbill/MakeSaleCardBill'
import IdName from 'model/common/IdName'
import MakeSaleCardBillApi from 'http/card/makesalebill/MakeSaleCardBillApi'
import TemplateValidity from 'model/card/template/TemplateValidity'
import I18nPage from "common/I18nDecorator";
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import AutoFixInput from 'cmp/autofixinput/AutoFixInput'
import CommonUtil from 'util/CommonUtil'
import MakeCardBillApi from 'http/prepay/card/MakeCardBillApi'
import MakeSaleCardBillLine from "model/card/salebill/ImprestCardSaleBillLine";
import MakeSaleCardBillCreateRequest from "model/card/makesalecardbill/MakeSaleCardBillCreateRequest";
import ConstantMgr from "mgr/ConstantMgr";

class MakeSaleCardBillEditForm {
  cardTemplate: Nullable<IdName> = new IdName()
  validityInfo: Nullable<TemplateValidity> = new TemplateValidity()
  lines: MakeSaleCardBillLine[] = []
  remark: Nullable<string> = null
  cardPictureUrl: Nullable<string> = null
  startCardCode: Nullable<string> = null
  endCardCode: Nullable<string> = null
}

@Component({
  name: 'MakeSaleCardBillEdit',
  components: {
    SubHeader,
    FormItem,
    CardPicList,
    CardTplItem,
    BreadCrume,
    AutoFixInput
  }
})
@I18nPage({
  prefix: [
    '/储值/预付卡/充值卡制售单/编辑页面',
    '/公用/按钮',
    '/卡/卡管理/制卡单/制卡单详情'
  ],
})
export default class MakeSaleCardBillEdit extends Vue {
  editType = '新建' // 新建,修改,复制,批量制卡
  i18n: (str: string, params?: string[]) => string
  form: MakeSaleCardBillEditForm = new MakeSaleCardBillEditForm()
  $refs: any
  cardNumber: Nullable<string> = null
  panelArray: any = []
  rules = {}
  selectCardType: string = '' //选择的卡类型
  selectCountPrice: number = 0 //如果选择了次卡，次卡价格
  cardNumLength: number = 0 //  选中卡模板的卡号长度

  get getTotalPrice() {
    if (this.form.lines?.length && this.form.lines[0].price && this.form.lines[0].total) {
      return Number(this.form.lines[0].price) * Number(this.form.lines[0].total)
    } else {
      return '--'
    }
  }

  created() {
    this.editType = this.$route.query.editType as string
    this.panelArray = [
      {
        name: this.formatI18n('/储值/预付卡/充值卡制售单/列表页面/制售单'),
        url: 'make-sale-card-bill-list'
      },
      {
        name: this.formatI18n('/储值/预付卡/充值卡制售单/列表页面/新建制售单'),
        url: ''
      }
    ]
    if (this.editType === '批量制卡') {
      this.cardNumber = this.$route.query.cardNumber as string
    }
    this.initRule()
  }

  private save() {
    this.$refs['form'].validate((valid: any) => {
      if (valid) {
        MakeSaleCardBillApi.save(this.parseParam()).then((resp: any) => {
          if (resp && resp.code === 2000) {
            this.$router.push({ name: 'make-sale-card-bill-dtl', query: { number: resp.data } })
          } else {
            throw new Error(resp.msg)
          }
        }).catch((error) => {
          if (error && error.message) {
            this.$message.error(error.message)
          }
        })
      }
    })
  }

  private saveAndAudit() {
    this.$refs['form'].validate((valid: any) => {
      if (valid) {
        const loading = this.$loading(ConstantMgr.loadingOption)

        return MakeSaleCardBillApi.saveAndAudit(this.parseParam()).then((resp: any) => {
          if (resp && resp.code === 2000) {
            this.$router.push({ name: 'make-sale-card-bill-dtl', query: { number: resp.data } })
          } else {
            throw new Error(resp.msg)
          }
        }).catch((error) => {
          if (error && error.message) {
            this.$message.error(error.message)
          }
        }).finally(() => {
          loading.close()
        })
      }
    })
  }

  private parseParam() {
    let formData: MakeSaleCardBillEditForm = JSON.parse(JSON.stringify(this.form))
    let param: MakeSaleCardBillCreateRequest = new MakeSaleCardBillCreateRequest()
    param.cardTemplateNumber = formData.cardTemplate?.id
    param.cardTemplateName = formData.cardTemplate?.name
    param.startCardCode = formData.startCardCode
    param.endCardCode = formData.endCardCode

    if (formData.lines?.length) {
      param.cardCount = formData.lines[0]?.count
      param.cardPrice = formData.lines[0]?.price
      param.cardFaceAmount = formData.lines[0]?.faceAmount
      param.makeSaleQty = formData.lines[0]?.total
    }
    param.remark = formData.remark
    return param
  }

  private selectTpl(val: CardTemplate) {
    let idName = new IdName()
    idName.id = val.number
    idName.name = val.name
    this.selectCardType = val.cardTemplateType!
    this.form.cardTemplate = idName
    this.form.validityInfo = val.validityInfo
    this.form.cardPictureUrl = val.cardPictureUrls[0]
    this.form.lines = []
    this.cardNumLength = val.cardCodeLength || 0

    let line = new MakeSaleCardBillLine()
    if (this.selectCardType === 'COUNTING_CARD') {
      line.count = val.count
    } else {
      line.faceAmount = val.faceAmounts?val.faceAmounts[0] : 0;
    }
    this.form.lines.push(line)
    this.$refs['form'].validateField('cardTemplateNumber')
  }

  //起始卡号change
  doStartCodeChange(val: string) {
    this.form.startCardCode = val.replace(/[^\d]/g, '')
    if (this.form.startCardCode.length > 18) {
      this.form.startCardCode = this.form.startCardCode.slice(0, 18)
    }
    this.computeEndCode()
  }
  //计算结束卡号
  computeEndCode() {
    const makeSaleQty = this.form.lines[0]?.total
    if (makeSaleQty && this.form.startCardCode) {
      this.form.endCardCode = CommonUtil.largeNumAdd(this.form.startCardCode, String(makeSaleQty - 1))
    }
  }

  computeStartNumber() {
    if (this.cardNumLength) {
      return this.i18n('/卡/卡管理/制卡单/制卡单详情/卡号长度需为{0}位').replace(/\{0\}/g, String(this.cardNumLength))
    } else {
      return this.i18n('/卡/卡管理/制卡单/制卡单详情/请输入起始卡号')
    }
  }

  //自动生成起始卡号
  autoMakeStartCode() {
    if (!this.form.cardTemplate?.id) {
      return this.$message.warning(this.i18n('/储值/预付卡/电子礼品卡活动/编辑页面/请选择卡模板'))
    }
    MakeSaleCardBillApi.autoMaxCardCode(this.cardNumLength).then((res) => {
      if (res.data || res.code === 2000) {
        this.form.startCardCode = res.data || ''
        this.computeEndCode()
      } else {
        this.$message.error(res.msg || this.i18n('/卡/卡管理/制卡单/制卡单详情/生成卡号失败'))
      }
    }).catch(error => this.$message.error(error.message || this.i18n('/卡/卡管理/制卡单/制卡单详情/生成卡号失败')))
  }

  get total() {
    let count = 0
    for (let line of this.form.lines) {
      if (line.total) {
        count += parseInt(line.total as any, 10)
      }
    }
    return 1000 - count
  }

  private checkPrice(row: MakeSaleCardBillLine, index: number) {
    let price = Number.parseFloat(row.price + '')
    let faceAmount = Number.parseFloat(row.faceAmount + '')
    this.$nextTick(()=>{
      if (row.faceAmount) {
        if (!Number.isNaN(price) && !Number.isNaN(faceAmount) && price > faceAmount) {
          this.$set(this.form.lines[index], 'price', row.faceAmount)
          this.$forceUpdate()
        }
      }
    })
  }

  initRule() {
    this.rules = {
      cardTemplateNumber: [
        {
          validator: (rule: any, value: any, callback: any) => {
            if (!this.form.cardTemplate || !this.form.cardTemplate.id) {
              callback(new Error(this.i18n('请选择卡模板')))
              return
            }
            callback()
          }, trigger: ['blur', 'change']
        },
      ],
      startCardCode: [{
        required: false,
        validator: (rule: any, value: any, callback: any) => {
          if (this.form.startCardCode && this.form.cardTemplate?.id && this.form.startCardCode?.length !== this.cardNumLength) {
            callback(new Error(this.i18n('卡号长度需为{0}位').replace(/\{0\}/g, String(this.cardNumLength))))
          }
          callback()
        },
        trigger: "change",
      }],
      endCardCode: [{
        required: false,
        validator: (rule: any, value: any, callback: any) => {
          if (this.form.startCardCode?.length !== this.form.endCardCode?.length) {
            callback(new Error(this.i18n('结束卡号必须与起始卡号位数一致')))
          }
          callback()
        },
        trigger: "change",
      }],
      specs: [
        {
          validator: (rule: any, value: any, callback: any) => {
            let checkedLines = this.form.lines
            if (checkedLines.length === 0) {
              callback(new Error(this.i18n('请选择至少选择一个卡面额')))
              return
            }
            let totalTotal = 0
            for (let line of checkedLines) {
              let price = Number.parseFloat(line.price + '')
              let total = Number.parseFloat(line.total + '')
              if (Number.isNaN(price) || Number.isNaN(total)) {
                callback(new Error(this.i18n('请填写售价或制售数量')))
                return
              }
              totalTotal += total
              // @ts-ignore
              let isPriceOut = (price > line.faceAmount && this.selectCardType !== 'COUNTING_CARD') || price < 0
              if (isPriceOut) {
                callback(new Error(this.i18n('售价不能小于0或者大于卡面额')))
                return
              }
              // @ts-ignore
              let isTotalOut = total > 1000 || total < 1
              if (isTotalOut) {
                callback(new Error(this.i18n('制售数量不能小于1或者大于1000')))
                return
              }
            }
            if (totalTotal > 1000) {
              callback(new Error(this.i18n('总制售数量不能大于1000')))
              return
            }
            callback()
          },
          trigger: ['blur', 'change']
        }
      ]
    }
  }

}
