import BaseCouponActivity from "model/v2/coupon/BaseCouponActivity";
import GiftInfo from "model/common/GiftInfo";
import MemberRule from "model/precisionmarketing/tag/tagrule/customize/member/MemberRule";
import IdName from "model/common/IdName";

export default class ManualHandOutGiftActivity extends BaseCouponActivity {
	// 启用微信模板消息
	enableWeiXinMessage: Nullable<boolean> = null;
	// 券礼包
	giftInfo: Nullable<GiftInfo> = null;
	// 复制对象券码
	prevNumber: Nullable<string> = null;
	// 是否携带复制对象的发券信息，如果为ture则必须传入复制对象券码
	withPrevIssueObj: Nullable<boolean> = null;
	// 是否导入了手机号文件：true是false不是，null未导入过文件
	importMobile: Nullable<string> = null;
	// 当前已经导入的数量
	importCount: Nullable<number> = null;
	// 当前已经发放数量
	issueCount: Nullable<number> = null;
	// 发券时间
	handOutTime: Nullable<Date> = null;
	// 会员属性规则
	memberRule: Nullable<MemberRule> = null;
	// 类型 IMPORT_MEMBER--导入会员， MEMBER_PROP--按会员属性， ENTER_MOBILE--填写手机号
	manualHandOutType: Nullable<string> = null;
	// 是否完成会员计算
	memberCalculationComplete: Nullable<boolean> = null;
	// 发生组织
	occurredOrg: Nullable<IdName> = null;
	// 是否允许在CRM后台核销
	enableWriteOffByPhx: Nullable<boolean> = false;
	// 填写手机号list
	mobiles: Nullable<string[]> = null
  // 枚举类型（MEMBER_SAME 会员发相同券,MEMBER_DIFF 会员发不同券）
  issueType: string = 'MEMBER_SAME'
}
