<!--
 * @Author: 黎钰龙
 * @Date: 2025-01-06 20:02:17
 * @LastEditTime: 2025-05-22 15:02:21
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\cmp\page-props-components\carousel\Carousel.vue
 * 记得注释
-->
<template>
  <div class="carousel">
    <div class="title">
      <span class="text">{{ i18n('轮播图') }}</span>
      <el-switch @change="handleChange" inactive-color="#D0D4DA" v-model="form.propShowRotation"></el-switch>
    </div>
    <div v-show="value.propShowRotation" class="wz">1、{{ i18n('支持最多设置10张图片，建议尺寸：750*460像素，支持jpg/jpeg/png，不超过1M') }}</div>
    <div v-show="value.propShowRotation" class="wz">2、{{ i18n('序号数字越小，图片排序越靠前，可拖动排序') }}</div>

    <el-form v-if="value.propShowRotation" :label-position="labelPosition" :model="form" :rules="rules" ref="form" class="main-products">
      <el-form-item prop="propImages">
        <draggable v-model="form.propImages" animation="500" @change="handleChange">
            <el-card v-for="(item, index) in form.propImages" class="product-card" :key="item.id + index" shadow="never">
              <div class="nightfury-flex-left-center card-wrap">
                <el-form-item :prop="`propImages[${index}].imageUrl`" :rules="{ required: true, message: i18n('请上传图片'), trigger: ['change', 'blur'] }">
                  <upload-img
                    v-model="item.imageUrl"
                    :signature-result="credential"
                    @validate="handleValidate"
                    width="80px"
                    height="80px"
                    :isDelete="false"
                    @change="handleChange"
                    :imgFormat="imgFormat"
                    :jumpPageInfo="item.jumpPageInfo"
                    :advertiseChannel="advertiseChannel"
                    @changeJumpPageInfo="changeJumpPageInfo($event, index)"
                  ></upload-img>
                  <el-button type="primary" @click="deleteImg(index, form.propImages)" circle class="el-icon-delete"></el-button>
                </el-form-item>
              </div>
            </el-card>
        </draggable>
      </el-form-item>
    </el-form>
    <div v-show="value.propShowRotation && value.propImages.length < 10" class="btn" @click="addPic">+{{ i18n('添加轮播图') }}</div>
  </div>
</template>

<script lang="ts" src="./Carousel.ts"></script>

<style lang="scss" scoped>
.carousel {
  background: #f0f2f6;
  border-radius: 4px;
  padding: 12px;
  margin-top: 20px;
  .title {
    display: flex;
    align-content: center;
    margin-bottom: 10px;
    span {
      margin-right: 12px;
    }
  }
  .wz {
    font-weight: 400;
    font-size: 13px;
    color: #a1a6ae;
    line-height: 18px;
  }
  .btn {
    height: 36px;
    background: #ffffff;
    border-radius: 4px;
    font-weight: 400;
    font-size: 12px;
    color: #007eff;
    line-height: 36px;
    text-align: center;
    cursor: pointer;
  }

  .el-form-item {
    margin-top: 8px;
    ::v-deep .el-card__body {
      padding: 0px;
    }
    ::v-deep .uploader-imgbox .up-box {
      padding: 8px;
      background-color: #fff;
    }
    .el-icon-delete {
      position: absolute;
      top: 50%;
      right: 8px;
      transform: translateY(-50%);
      background: transparent !important;
      border: 0;
      color: #79879e;
    }
  }
}
</style>
