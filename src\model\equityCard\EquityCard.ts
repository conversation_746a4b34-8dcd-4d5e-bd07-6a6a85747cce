/*
 * @Autor: 司浩
 * @Description:
 * @Date: 2023-03-02 17:38:12
 * @LastEditors: 司浩
 * @LastEditTime: 2023-03-07 15:59:19
 * @FilePath: \phoenix-web-ui\src\model\equityCard\EquityCard.ts
 */
import { EquityCardState } from 'model/equityCard/EquityCardState'

// 权益卡
export default class EquityCard {
  // 发卡营销中心
  issueMarketingCenter: Nullable<string> = null
  // 发卡组织
  issueOrgId: Nullable<string> = null
  // 发卡组织
  issueOrgName: Nullable<string> = null
  // 发卡渠道
  issueChannelId: Nullable<string> = null
  // 发卡渠道
  issueChannelType: Nullable<string> = null
  // 卡模板号
  templateNumber: Nullable<string> = null
  // 卡模板名
  templateName: Nullable<string> = null
  // 发卡交易id
  issueTransIdId: Nullable<string> = null
  // 发卡交易id
  issueTransIdNamespace: Nullable<string> = null
  // 发卡时间
  issueTime: Nullable<Date> = null
  // 持卡会员id
  memberId: Nullable<string> = null
  // 卡号
  code: Nullable<string> = null
  // 卡名称
  name: Nullable<string> = null
  // 卡状态
  state: Nullable<EquityCardState> = null
  // 有效期，开始时间
  beginTimeInclusive: Nullable<string> = null
  // 有效期，结束时间
  endTimeExclusive: Nullable<string> = null
  // 会员号
  crmCode: Nullable<string> = null
  // 会员手机号
  mobile: Nullable<string> = null
}
