// 付费会员卡
import IdName from "model/common/IdName";

export default class BenefitCard {
  // 发卡营销中心
  issueMarketingCenter: Nullable<string> = null
  // 持卡会员id
  memberId: Nullable<string> = null
  // crm会员号
  crmCode: Nullable<string> = null
  // 手机号
  mobile: Nullable<string> = null
  // 卡名称
  name: Nullable<string> = null
  //
  templateId: Nullable<string> = null
  // 卡模板号
  templateCode: Nullable<string> = null
  // 卡号
  cardNo: Nullable<string> = null
  // 发卡时间
  openCardTime: Nullable<Date> = null
  // 有效期，开始时间
  expireStartTime: Nullable<Date> = null
  // 有效期，结束时间
  expireStopTime: Nullable<Date> = null
  // 卡状态
  cardStatus: Nullable<string> = null

  // 开卡门店
  issueOrg: Nullable<IdName> = null
}
