import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import WeixinCardV2 from 'model/weixin/cardV2/WeixinCardV2'

export default class WeixinCardV2Api {
  /**
   * 微信会员卡版本
   *
   */
  static cardVersions(): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/weixin-version/get`, {}).then((res) => {
      return res.data
    })
  }


  /**
   * 创建微信会员卡
   *
   */
  static create(body: WeixinCardV2): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/weixin-card2/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 更新微信会员卡
   *
   */
  static update(body: WeixinCardV2): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/weixin-card2/update`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 会员卡模板查询
   *
   */
  static select(): Promise<Response<void>> {
    return ApiClient.server().get(`/v1/weixin-card2/get`, {
    }).then((res) => {
      return res.data
    })
  }
}
