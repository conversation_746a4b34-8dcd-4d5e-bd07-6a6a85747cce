import I18nPage from "common/I18nDecorator";
import WeimobCoupon from "model/common/weimobCoupon/WeimobCoupon";
import BWeimobExtGoodsUseRule from 'model/common/weimob/BWeimobExtGoodsUseRule'
import B<PERSON>eimobOrgUseRule from 'model/common/weimob/BWeimobOrgUseRule'
import AmountToFixUtil from "util/AmountToFixUtil";
import DateUtil from "util/DateUtil";
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import GoodsWeiMenEx from "cmp/goodsscope/GoodsWeiMenEx.vue";
import IdName from 'model/common/IdName'
import CanUseDiscount from "model/common/weimob/CanUseDiscount";
import MallDiscount from "model/common/weimob/MallDiscount";
import OrgsWeiMenEx from "cmp/orgsscope/OrgsWeiMenEx.vue";

let UseScene: any = []

@Component({
  name: "WeimobCouponInfo",
  components: { GoodsWeiMenEx, OrgsWeiMenEx },
})
@I18nPage({
  prefix: ["/公用/券模板", '/公用/券模板/微盟适用商品', '/会员/洞察/公共/操作符'],
  auto: true
})
export default class WeimobCouponInfo extends Vue {
  @Prop()
  value: any;
  @Prop({ type: Boolean, default: false })
  autoCheckall: boolean;

  @Prop({ type: Boolean, default: false })
  lockApiCheck: boolean;

  @Prop({ type: Boolean, default: false })
  isExchang: boolean;

  @Prop({ type: Boolean, default: true }) canSelectAllGoods: boolean;  //微盟用券商品 是否可以选择全部商品

  @Prop({ type: Boolean, default: false }) disabled: boolean;

  @Watch('isExchang')
  onisExchangChange(value: any) {
    if (value) {
      this.UseScenes = [
        {
          key: this.i18n('网店订单'),
          value: 1
        },
        {
          key: this.i18n('商家开单'),
          value: 10
        },
        {
          key: this.i18n('APP&收银台核销'),
          value: 3
        },
        {
          key: this.i18n('API核销'),
          value: 7
        },
        {
          key: this.i18n('扫码购'),
          value: 12
        }
      ]
    }
  }

  @Watch('lockApiCheck')
  onLockApiCheckChange(value: any) {
    if (value) {
      this.form.useSceneType = false
      this.UseScene = [7]
      UseScene = [7]
    }
  }

  @Watch("value")
  onValueChange(value: any) {
    this.doBindValue(value)
  }

  @Watch('selectData', { deep: true })
  handle() {
    this.form.weimobCoupon.recommendStartTime = this.selectData.beginDate
    this.form.weimobCoupon.recommendEndTime = this.selectData.endDate
    this.form.weimobCoupon.enableRecommend = this.selectData.enableRecommend
  }

  $refs: any
  form: any = {
    isPerLimit: true,
    dayLimitType: "unlimited",
    total: "",
    weimobCoupon: new WeimobCoupon(),
    useSceneType: true,
    goodsUseRule: new BWeimobExtGoodsUseRule(),
    useCouponTimeRange: []
  };
  weimobOrgTotal: number = 0;
  selectData: any = { //直接领取推荐时间
    enableRecommend: false,
    beginDate: new Date(),
    endDate: new Date()
  }
  disabledTotal: Boolean = false
  disabledRecommend: Boolean = false
  useSceneType: boolean = true
  isIndeterminateUseScene: boolean = true
  UseSceneCheckAll: boolean = false
  UseScene: number[] = []
  UseScenes: any[] = [
    {
      key: this.i18n('网店订单'),
      value: 1
    },
    {
      key: this.i18n('商家开单'),
      value: 10
    },
    {
      key: this.i18n('买家直接消费'),
      value: 8
    },
    {
      key: this.i18n('商家直接消费'),
      value: 9
    },
    {
      key: this.i18n('APP&收银台核销'),
      value: 3
    },
    {
      key: this.i18n('API核销'),
      value: 7
    },
    {
      key: this.i18n('扫码购'),
      value: 12
    }
  ]
  rules: any = {}
  MallDiscount: any[] = MallDiscount(this.i18n) //券叠加其他优惠,指定活动可叠加,前端写死
  created() {
    this.initRule()
    if (this.disabled) {
      this.disabledRecommend = true
    }

    if (this.lockApiCheck) {
      this.form.useSceneType = false
      this.UseScene = [7]
      UseScene = [7]
    }

    if (this.isExchang) {
      this.UseScenes = [
        {
          key: this.i18n('网店订单'),
          value: 1
        },
        {
          key: this.i18n('商家开单'),
          value: 10
        },
        {
          key: this.i18n('APP&收银台核销'),
          value: 3
        },
        {
          key: this.i18n('API核销'),
          value: 7
        },
        {
          key: this.i18n('扫码购'),
          value: 12
        }
      ]
    }
  }

  getStoreCount(count: number) {
    let str: any = this.formatI18n("/公用/券模板", "已选择{0}家门店");
    str = str.replace(/\{0\}/g, count);
    return str;
  }

  // 微盟适用商品排除
  get excludedGoodsList(): IdName[] {
    if (this.form.weimobCoupon.goodsUseRule.limitedGoodsType == 'goods') {
      const limitGoodsTypeRule = this.form.weimobCoupon.goodsUseRule.limitGoodsTypeRule
      return limitGoodsTypeRule.existExcludeGoods && limitGoodsTypeRule.excludeGoodsIds
    }
    if (this.form.weimobCoupon.goodsUseRule.limitedGoodsType == 'goodsCategory') {
      const limitGoodsCategoryTypeRule = this.form.weimobCoupon.goodsUseRule.limitGoodsCategoryTypeRule
      return limitGoodsCategoryTypeRule.existExcludeGoods && limitGoodsCategoryTypeRule.excludeGoodsIds
    }
    if (this.form.weimobCoupon.goodsUseRule.limitedGoodsType == 'goodsGroup') {
      const limitGoodsGroupRule = this.form.weimobCoupon.goodsUseRule.limitGoodsGroupRule
      return limitGoodsGroupRule.existExcludeGoods && limitGoodsGroupRule.excludeGoodsIds
    }
    return []
  }
  get goodsInfo() {
    if (this.form.weimobCoupon.goodsUseRule.limitedGoodsType == 'goods') {
      const limitGoodsTypeRule = this.form.weimobCoupon.goodsUseRule.limitGoodsTypeRule
      if (limitGoodsTypeRule.goodsRange == 'all') {
        return {
          str: this.i18n('全部商品'),
          list: [],
          type: 'none'
        }
      } else {
        if (limitGoodsTypeRule.includeGoodsIds && limitGoodsTypeRule.includeGoodsIds.length > 0) {
          return {
            str: `${this.i18n('已选择')}${limitGoodsTypeRule.includeGoodsIds.length}${this.i18n('件商品')}`,
            list: limitGoodsTypeRule.includeGoodsIds,
            type: 'tips'
          }
        }
      }
    } else if (this.form.weimobCoupon.goodsUseRule.limitedGoodsType == 'goodsCategory') {
      const limitGoodsCategoryTypeRule = this.form.weimobCoupon.goodsUseRule.limitGoodsCategoryTypeRule
      let str = ''
      if (limitGoodsCategoryTypeRule.ruleInfos && limitGoodsCategoryTypeRule.ruleInfos.length > 0) {
        const categoryInfo = limitGoodsCategoryTypeRule.ruleInfos[0]
        str = `${this.i18n('指定类目')}：` + categoryInfo.categoryName
        if (categoryInfo.childs && categoryInfo.childs.length > 0) {
          str = `${str} > ${categoryInfo.childs[0].categoryName}`
        }
        return {
          str: str,
          list: [],
          type: 'none'
        }
      }
    } else if (this.form.weimobCoupon.goodsUseRule.limitedGoodsType == 'goodsGroup') {
      const limitGoodsGroupRule = this.form.weimobCoupon.goodsUseRule.limitGoodsGroupRule
      let str = ''
      if (limitGoodsGroupRule.ruleInfos && limitGoodsGroupRule.ruleInfos.length > 0) {
        let childsNum = 0
        const list = limitGoodsGroupRule.ruleInfos.reduce((acc: any, cur: any) => {
          const firstName = cur.name
          if (cur.childs && cur.childs.length > 0) {
            cur.childs.map((item: any) => {
              childsNum = childsNum + 1
              acc.push({
                id: '',
                name: `${firstName} > ${item.name}`
              })
            })
          } else {
            childsNum = childsNum + 1
            acc.push({
              id: '',
              name: firstName
            })
          }
          return acc
        }, [])
        str = `${this.i18n('已选择')}${childsNum}${this.i18n('个分组')}`
        return {
          str,
          list,
          type: 'tips'
        }
      }
    }
    return {
      str: '',
      list: [],
      type: 'none'
    }
  }
  get dateRangeOption() {
    return {
      disabledDate(time: any) {
        return time.getTime() < DateUtil.nowDayTime();
      },
    };
  }

  mounted() {
    this.doBindValue(this.value)
    this.doFormChange()
  }
  doTransParams() {
    if (this.form.useSceneType) {
      this.form.weimobCoupon.useScene = {
        allSceneDTO: true,
        shoppingMallSceneList: []
      }
    } else {
      this.form.weimobCoupon.useScene = {
        allSceneDTO: false,
        shoppingMallSceneList: [...this.UseScene]
      }
    }
    //可领券时间 固定时间段
    if (this.form.weimobCoupon.sendTimeType == 2 && this.form.useCouponTimeRange?.length) {
      this.form.weimobCoupon.sendStartDate = this.form.useCouponTimeRange[0]
      this.form.weimobCoupon.sendEndDate = this.form.useCouponTimeRange[1]
    }
    return this.form;
  }
  doBindValue(value: any) {
    if (!value?.weimobCoupon) {
      if (!this.canSelectAllGoods) {
        this.form.weimobCoupon.goodsUseRule.limitedGoods = true
      }
      return
    }
    // 微盟券参数
    this.form.weimobCoupon = value.weimobCoupon || new WeimobCoupon();
    //当后端传canUseDiscount为null时，前端兼容
    this.form.weimobCoupon.canUseDiscount = value.weimobCoupon?.canUseDiscount || new CanUseDiscount()
    this.form.total = value.total
    if (this.disabled === true && this.form.total) {
      this.rules.total = [
        {
          required: true,
          message: this.formatI18n("/公用/表单校验/请选择必选项"),
          trigger: "change",
        },
      ]
    }
    if (this.disabled === true && !this.form.total) {
      this.disabledTotal = true
    }
    // 每人限领
    if (this.form.weimobCoupon && this.form.weimobCoupon.perLimit) {
      this.form.isPerLimit = false;
    } else {
      this.form.isPerLimit = true;
    }
    // 可领券时间 固定时间
    if (this.form.weimobCoupon?.sendTimeType == 2) {
      this.form.useCouponTimeRange = [this.form.weimobCoupon.sendStartDate, this.form.weimobCoupon.sendEndDate]
    }
    // 每天可领取
    if (this.form.weimobCoupon && this.form.weimobCoupon.dayLimit) {
      this.form.dayLimitType = 'limited'
    } else {
      this.form.dayLimitType = 'unlimited'
    }
    // 适用商品
    if (!this.form.weimobCoupon.goodsUseRule) {
      this.form.weimobCoupon.goodsUseRule = new BWeimobExtGoodsUseRule()
    }
    // 使用场景
    if (this.form.weimobCoupon && this.form.weimobCoupon.useScene) {
      this.form.useSceneType = !!this.form.weimobCoupon.useScene.allSceneDTO
      this.useSceneType = !!this.form.weimobCoupon.useScene.allSceneDTO
      if (!this.useSceneType) {
        this.UseScene = this.form.weimobCoupon.useScene.shoppingMallSceneList || []
        UseScene = [...this.UseScene]
        if (this.UseScene.length == this.UseScenes.length) {
          this.isIndeterminateUseScene = false
          this.UseSceneCheckAll = true
        } else {
          this.isIndeterminateUseScene = true
          this.UseSceneCheckAll = false
        }
      }
    }
    if (value.weimobCoupon.enableRecommend) {
      //回填 直接领取的推荐时间
      this.selectData.beginDate = new Date(value.weimobCoupon.recommendStartTime)
      this.selectData.endDate = new Date(value.weimobCoupon.recommendEndTime)
      this.selectData.enableRecommend = value.weimobCoupon.enableRecommend
    } else {
      //如果之前没有开启推荐 则不禁用推荐开始时间
      this.disabledRecommend = false
    }
    //券叠加其他优惠
    if (value.weimobCoupon.canUseDiscount) {
      this.form.weimobCoupon.canUseDiscount.canUseWithOtherDiscount = value.weimobCoupon.canUseDiscount.canUseWithOtherDiscount || false
      this.form.weimobCoupon.canUseDiscount.shoppingMallDiscount = value.weimobCoupon.canUseDiscount.shoppingMallDiscount || []
    }
    //微盟适用门店
    this.form.weimobCoupon.limitOrgType = value.weimobCoupon.limitOrgType || 'ALL'
    if (value.weimobCoupon.limitOrgType == 'PART' && value.weimobCoupon.orgsUseRule && value.weimobCoupon.orgsUseRule.limitedOrgsType) {
      this.weimobOrgTotal = (value.weimobCoupon.orgsUseRule?.includeOrgIds?.length + value.weimobCoupon.orgsUseRule?.includeBrandIds?.length) || 0
    }
  }
  //当前组件表单内容改变时，通知父组件同步数据
  doFormChange() {
    this.doValidate()
    this.$emit("change", this.doTransParams())
  }
  totalChange() {
    this.form.total = AmountToFixUtil.formatNumber(
      this.form.total,
      10000000,
      1
    );
    this.$refs.weimobCouponRef.validate()
    let temp = this.form.useSceneType
    this.doFormChange();
    this.form.useSceneType = temp
  }

  isPerLimitChange() {
    if (this.form.isPerLimit === true) {
      this.form.weimobCoupon.perLimit = this.form.weimobCoupon.dayLimit = "";

      let temp = this.form.useSceneType
      this.doFormChange();
      this.form.useSceneType = temp
    }
  }

  dayLimitTypeChange() {
    if (this.form.dayLimitType === 'unlimited') {
      this.form.weimobCoupon.dayLimit = ''
      let temp = this.form.useSceneType
      this.doFormChange();
      this.form.useSceneType = temp
    }
  }

  doPerLimitChange() {
    this.form.weimobCoupon.perLimit = AmountToFixUtil.formatNumber(
      this.form.weimobCoupon.perLimit,
      999999,
      1
    );
    if (this.form.weimobCoupon.perLimit) {
      let temp = this.form.useSceneType
      this.doFormChange();
      this.form.useSceneType = temp
    } else {
      this.form.weimobCoupon.perLimit = 1;
    }
  }

  dayLimitChange() {
    this.form.weimobCoupon.dayLimit = AmountToFixUtil.formatNumber(
      this.form.weimobCoupon.dayLimit,
      999999,
      1
    );
    if (!this.form.weimobCoupon.dayLimit) {
      this.form.weimobCoupon.dayLimit = 1;
    }

    let temp = this.form.useSceneType
    this.doFormChange();
    this.form.useSceneType = temp
  }

  sendTimeTypeChange() {

  }

  issueChannelChange() {
    let temp = this.form.useSceneType
    this.doFormChange();
    this.form.useSceneType = temp
  }

  freeChannelsChange() {
    let temp = this.form.useSceneType
    this.doFormChange();
    this.form.useSceneType = temp
  }

  checkQty() {
    let promise = new Promise((resolve, reject) => {
      if (this.form.weimobCoupon.dayLimit > this.form.weimobCoupon.perLimit) {
        this.$message.warning(this.i18n('每日限领数不能大于总限领数'))
        reject()
      } else {
        resolve(true)
      }
    })
    return promise
  }

  doValidate() {
    let arr = [
      this.$refs.weimobCouponRef.validate(),
      this.checkQty()
    ]
    let promise = Promise.all(arr)
    return promise
  }

  // 核销场景新增代码
  UseSceneTtypeChange(val: any) {
    if (!val) {
      // 现金券/折扣券/兑换券默认全部场景；特价券写死API核销
      if (this.autoCheckall) {
        this.UseSceneCheckAll = false
        this.isIndeterminateUseScene = true
        // this.UseScene = this.UseScenes.map(x => {
        //   return x.value
        // })
        this.UseScene = [7]
        UseScene = [...this.UseScene]
      }
      if (this.lockApiCheck) {
        this.UseScene = [7]
        UseScene = [...this.UseScene]
      }
    }
    let temp = this.form.useSceneType
    this.$refs.weimobCouponRef.validate()
    this.doFormChange();
    this.form.useSceneType = temp
  }
  // 指定场景全选
  UseSceneCheckAllChange(val: any) {
    this.UseScene = val ? this.UseScenes.map(x => {
      return x.value
    }) :
      this.autoCheckall || this.lockApiCheck ? [7] :
        [];
    this.isIndeterminateUseScene = false;
    UseScene = [...this.UseScene]
    let temp = this.form.useSceneType
    this.$refs.weimobCouponRef.validate()
    this.doFormChange();
    this.form.useSceneType = temp
  }
  // 指定场景选择
  UseSceneChange(val: any) {
    this.UseScene = [...val]
    UseScene = [...this.UseScene]
    let checkedCount = val.length;
    this.UseSceneCheckAll = checkedCount === this.UseScenes.length;
    this.isIndeterminateUseScene = checkedCount > 0 && checkedCount < this.UseScenes.length;
    let temp = this.form.useSceneType
    this.$refs.weimobCouponRef.validate()
    this.doFormChange();
    this.form.useSceneType = temp
  }

  openOrgDiaLog() {
    this.$refs.OrgsWeiMenExRef.open(this.form.weimobCoupon.orgsUseRule);
  }

  doStoreRange() {
    if (this.form.weimobCoupon.limitOrgType == "ALL") {
      this.form.weimobCoupon.orgsUseRule = new BWeimobOrgUseRule();
      this.weimobOrgTotal = 0;
    } else if (this.form.weimobCoupon.limitOrgType == "PART") {

    }
  }

  doChangeWeiOrgs(params: BWeimobOrgUseRule) {
    this.form.weimobCoupon.orgsUseRule = params
    if (this.form.weimobCoupon.orgsUseRule) {
      this.weimobOrgTotal = (this.form.weimobCoupon.orgsUseRule?.includeOrgIds?.length + this.form.weimobCoupon.orgsUseRule?.includeBrandIds?.length) || 0
    } else {
      this.weimobOrgTotal = 0;
      this.form.weimobCoupon.limitOrgType = 'ALL';
    }
    console.log('看看本地的数据', this.weimobOrgTotal);
    this.doFormChange();
  }


  doChangeGoods() {
    if (!this.form.weimobCoupon.goodsUseRule.limitedGoods) {
      this.form.weimobCoupon.goodsUseRule = new BWeimobExtGoodsUseRule()
    }
    this.doFormChange();
  }

  openGoodsDiaLog() {
    this.$refs.GoodsWeiMenExRef.dialogShow = true
  }

  doChangeWeiGoods(params: BWeimobExtGoodsUseRule) {
    this.form.weimobCoupon.goodsUseRule = params
    this.doFormChange();
    (this.$refs["weimobCouponRef"] as any).validateField("weimobCoupon.goodsUseRule");
  }

  doChangePileDiscount() {
    this.form.weimobCoupon.canUseDiscount.shoppingMallDiscount = []
    this.doFormChange();
  }

  pickerOptions: any = {
    startTime: {
      disabledDate: (time: any) => {
        return time.getTime() + 24 * 3600 * 1000 < DateUtil.nowTime() //今天以前的时间都不能选
      }
    },
    endTime: {
      disabledDate: (time: any) => {
        if (this.selectData.beginDate) {
          const curDate = this.selectData.beginDate
          return time.getTime() + 24 * 3600 * 1000 < new Date(curDate)
        } else {
          return time.getTime() + 24 * 3600 * 1000 < DateUtil.nowTime() //没选的时候，今天以前的时间都不能选
        }
      }
    }
  }

  initRule() {
    this.rules = {
      "weimobCoupon.perLimit": [
        {
          required: true,
          message: this.formatI18n("/公用/券模板", "请输入必填项"),
          trigger: "blur",
        },
      ],
      "weimobCoupon.dayLimit": [
        {
          required: true,
          message: this.formatI18n("/公用/券模板", "请输入必填项"),
          trigger: "blur",
        },
      ],
      "weimobCoupon.freeChannels": [
        {
          required: true,
          message: this.formatI18n("/公用/表单校验/请选择必选项"),
          trigger: "blur",
        },
        {
          validator: (rule: any, value: any, callback: any) => {
            if (this.form.weimobCoupon.freeChannels.includes('directly_receive')) { //开启了直接领取
              if (!this.form.weimobCoupon.enableRecommend) return callback() //如果没有开启系统勾选，则不校验
              const beginDate = new Date(this.form.weimobCoupon.recommendStartTime)
              const endDate = new Date(this.form.weimobCoupon.recommendEndTime)
              if (!beginDate || !endDate) {
                callback('请填写推荐时间')
              } else if (beginDate >= endDate || endDate < new Date()) {
                callback('请填写正确的格式')
              } else {
                callback()
              }
            } else {
              callback()
            }
          },
          trigger: "blur",
        }
      ],
      useSceneType: [
        {
          validator: (rule: any, value: any, callback: any) => {
            if (value) {
              callback();
            } else {
              if (!UseScene.length) {
                callback(this.i18n('指定场景必须选择一个场景'));
              } else {
                callback();
              }
            }
          },
          trigger: "blur",
        }
      ],
      issueChannel: [
        {
          validator: (rule: any, value: any, callback: any) => {
            if (this.form.weimobCoupon.issueChannel === 'FREE') {
              if (this.form.weimobCoupon.freeChannels.length == 0) {
                callback(this.i18n('请至少选择一种发放方式'))
              }
            }
            callback()
          },
          trigger: "blur",
        }
      ],
      useCouponTimeRange: [
        {
          required: true,
          validator: (rule: any, value: any, callback: any) => {
            if (this.form.weimobCoupon.sendTimeType != 2) return callback()
            if (value && value.length > 0 && value[0] !== "--") {
              let start = new Date(DateUtil.format(new Date(value[0]), "yyyy-MM-dd HH:mm:ss")).getTime();
              let end = new Date(DateUtil.format(new Date(value[1]), "yyyy-MM-dd HH:mm:ss")).getTime();
              let now = new Date(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss")).getTime();
              if (start > end) {
                callback(new Error(this.formatI18n("/营销/券礼包活动/核销第三方券", "开始时间不允许大于结束时间") as string));
              } else if (end < now) {
                callback(new Error(this.i18n("结束时间不允许小于现在")));
              } else {
                callback();
              }
            } else {
              callback(new Error(this.formatI18n("/公用/券模板", "请输入必填项") as string));
            }
          },
          trigger: "blur",
        },
      ],
      'weimobCoupon.orgsUseRule': [
        {
          validator: (rule: any, value: any, callback: any) => {
            console.log('为什么是0', this.weimobOrgTotal);
            if (this.form.weimobCoupon.limitOrgType == 'PART' && this.weimobOrgTotal === 0) {
              callback(this.i18n('/营销/积分活动/积分活动/商品满额加送积分/表单校验/提示信息/请选择门店范围'));
            } else {
              callback();
            }
          },
          trigger: "change",
        }
      ],
      'weimobCoupon.goodsUseRule': [
        {
          validator: (rule: any, value: any, callback: any) => {
            if (!this.form.weimobCoupon.goodsUseRule.limitedGoods) {
              callback();
            } else {
              if (this.goodsInfo.str) {
                callback();
              } else {
                callback(this.i18n('指定商品列表不能为空'));
              }
            }
          },
          trigger: "blur",
        }
      ],
      'weimobCoupon.canUseWithOtherDiscount': [
        {
          validator: (rule: any, value: any, callback: any) => {
            const canUseDiscount = this.form.weimobCoupon.canUseDiscount
            if (canUseDiscount.canUseWithOtherDiscount && canUseDiscount.shoppingMallDiscount?.length === 0) {
              callback(this.i18n('请至少选择一个活动'))
            } else {
              callback()
            }
          },
          trigger: "blur",
        }
      ]
    }
  }

}
