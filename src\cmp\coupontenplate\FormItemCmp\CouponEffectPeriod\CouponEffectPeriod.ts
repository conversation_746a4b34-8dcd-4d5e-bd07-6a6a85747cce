/*
 * @Author: 黎钰龙
 * @Date: 2023-07-11 10:49:46
 * @LastEditTime: 2023-12-15 15:51:05
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\coupontenplate\FormItemCmp\CouponEffectPeriod\CouponEffectPeriod.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import { LocalStorage } from 'mgr/BrowserMgr';
import { ExpiryType } from 'model/common/ExpiryType';
import AmountToFixUtil from 'util/AmountToFixUtil';
import DateUtil from 'util/DateUtil';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
@Component({
  name: 'CouponEffectPeriod'
})
@I18nPage({
  prefix: [
    "/公用/券模板",
    '/营销/券礼包活动/核销第三方券'
  ],
  auto: true
})
export default class CouponEffectPeriod extends Vue {
  @Prop()
  value: any;

  @Prop({
    default: () => {
      return {
        maxAmount: 99999999,
        maxValidDay: 36500,
        maxUseThreshold: 99999999,
        fixedTime: false,
      };
    },
  })
  options: {
    // 指定最大券面额，可选配置，用于微信扫码领券
    maxAmount: number; // 指定最大券面额
    maxValidDay: number; // 指定最大券有效天数
    maxUseThreshold: number; // 指定最大用券门槛
    fixedTime: boolean; // 固定用券时段为全部时段
  };

  @Prop({
    type: Boolean,
    default: false
  })
  specialMode: boolean; //为true，表示固定单位为“天”

  ruleForm: any = {
    dateType: '', //相对有效期-RALATIVE  固定有效期-FIXED
    dateFrom: '',  //相对有效期 第一个input
    expiryType: '',  //相对有效期 按天数/当月/按月数/永久有效 选择类型
    dateTo: '', //相对有效期  持续X天或X月
    dateFix: '',  //固定有效期 时间段
    selectModel: ''  //领取后 立即生效 或 自定义
  };
  $refs: any
  rules = {
    dateFrom: [
      {
        required: true,
        validator: (rule: any, value: any, callback: any) => {
          if ((!value && value !== 0) && this.ruleForm.dateType === 'RALATIVE') {
            callback(new Error(this.formatI18n("/公用/券模板", "请输入必填项")))
          } else {
            callback()
          }
        }
      }
    ],
    dateTo: [
      {
        required: true,
        validator: (rule: any, value: any, callback: any) => {
          if (!value && this.ruleForm.dateType === 'RALATIVE') {
            callback(new Error(this.formatI18n("/公用/券模板", "请输入必填项")))
          } else {
            callback()
          }
        }
      }
    ],
    dateFix: [
      {
        required: true,
        validator: (rule: any, value: any, callback: any) => {
          if (this.ruleForm.dateType !== 'FIXED') return callback()
          if (value && value.length > 0 && value[0] !== "--") {
            let start = new Date(DateUtil.format(new Date(value[0]), "yyyy-MM-dd HH:mm:ss")).getTime();
            let end = new Date(DateUtil.format(new Date(value[1]), "yyyy-MM-dd HH:mm:ss")).getTime();
            if (start > end) {
              callback(new Error(this.formatI18n("/营销/券礼包活动/核销第三方券", "开始时间不允许大于结束时间") as string));
            } else {
              callback();
            }
          } else {
            callback(new Error(this.formatI18n("/公用/券模板", "请输入必填项") as string));
          }
        },
        trigger: "blur",
      },
    ],
    selectModel: [
      {
        required: true,
        validator: (rule: any, value: any, callback: any) => {
          if (this.ruleForm.selectModel == '' && this.ruleForm.dateType === 'RALATIVE') {
            callback(new Error(this.formatI18n("/公用/券模板", "请输入必填项")))
          } else {
            callback()
          }
        }
      }
    ]
  }

  @Watch('value', { deep: true, immediate: true })
  handle(value: any) {
    if (value) {
      this.ruleForm.dateType = value.dateType
      this.ruleForm.dateFrom = value.dateFrom
      this.ruleForm.expiryType = value.expiryType
      this.ruleForm.dateTo = value.dateTo
      this.ruleForm.dateFix = value.dateFix
      if (this.ruleForm.dateFrom === 0) {
        this.ruleForm.selectModel = 'NOW'
      } else {
        this.ruleForm.selectModel = 'CUSTOM'
      }
    }
  }

  get getDateType() {
    let str = ''
    if ([ExpiryType.DAYS, ExpiryType.NATURAL_DAY].indexOf(this.ruleForm.expiryType) > -1) {
      str = this.formatI18n("/公用/券模板", "天");
    } else if (this.ruleForm.expiryType === ExpiryType.MONTHS) {
      str = this.formatI18n("/公用/券模板", "月");
    } else {
      str = ''
    }
    return str
  }
  get dateRangeOption() {
    return {
      disabledDate(time: any) {
        return time.getTime() < DateUtil.nowDayTime();
      },
    };
  }

  hasCheckCouponValidData: Nullable<Number> = null  //是否限制相对有效期和固定有效期的天数

  //根据配置控制，如果是相对有效期，则输入框内不能超过配置的天数，固定有效期则需要计算天数
  get isExceedDateLimit() {
    this.hasCheckCouponValidData = LocalStorage.getItem('templateValidateLimit')
    if (!this.hasCheckCouponValidData) return false
    if (this.ruleForm.dateType === 'RALATIVE') {
      if (this.ruleForm.expiryType === 'DAYS' || !this.ruleForm.expiryType) {
        return this.ruleForm.dateTo && this.ruleForm.dateTo > this.hasCheckCouponValidData
      } else {
        return false
      }
    } else {
      return this.ruleForm.dateFix && ((((new Date(this.ruleForm.dateFix[1]) as any) - (new Date(this.ruleForm.dateFix[0]) as any)) / 1000 / 60 / 60 / 24) as any) > this.hasCheckCouponValidData;
    }
  }
  get warnDataLimitText() {
    if (this.ruleForm.dateType === 'RALATIVE') {
      return this.i18n('券有效期必须≤{0}天').replace(/\{0\}/g, String(this.hasCheckCouponValidData))
    } else {
      return this.i18n('券有效期必须≤{0}天，结束时间不能超过：开始时间+{1}天')
        .replace(/\{0\}/g, String(this.hasCheckCouponValidData))
        .replace(/\{1\}/g, String((this.hasCheckCouponValidData as any) - 1))
    }
  }

  isDisabled(type: "RALATIVE" | "FIXED") {
    return this.ruleForm.dateType === type
  }

  doCouponValidateChange() {
    this.ruleForm.dateFix = [];
    this.ruleForm.dateFrom = "";
    this.ruleForm.dateTo = "";
    this.validate()
  }

  doDateFixChange() {
    this.doFormItemChange()
  }

  doCouponChange(type: number) {
    if (type === 1) {
      this.ruleForm.dateTo = AmountToFixUtil.formatNumber(this.ruleForm.dateTo, this.options.maxValidDay, 1);
    } else {
      this.ruleForm.dateFrom = AmountToFixUtil.formatNumber(this.ruleForm.dateFrom, 365, 0);
    }
    this.doFormItemChange()
  }

  doSelectChange(value: string) {
    if (value === 'NOW') {
      this.ruleForm.dateFrom = 0
      this.ruleForm.dateFrom = AmountToFixUtil.formatNumber(this.ruleForm.dateFrom, 365, 0);
      this.doFormItemChange()
    }
  }

  doParams() {
    let object = {
      dateType: this.ruleForm.dateType,
      dateFrom: this.ruleForm.dateFrom,
      expiryType: (this.specialMode && this.ruleForm.dateType === 'RALATIVE') ? 'DAYS' : this.ruleForm.expiryType,
      dateTo: this.ruleForm.dateTo,
      dateFix: this.ruleForm.dateFix
    }
    console.log('给父组件的参数：', { ...this.value, ...object });
    return { ...this.value, ...object }
  }

  doFormItemChange() {
    this.$emit('input', this.doParams())
    this.$emit('change')
  }

  validate() {
    if (this.$refs["form"]) {
      return this.$refs["form"].validate();
    } else {
      return true;
    }
  }
};