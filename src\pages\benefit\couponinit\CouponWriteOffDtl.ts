import { Component, Vue } from "vue-property-decorator";
import BreadCrume from "cmp/bread-crumb/BreadCrume.vue";
import BlockTitle from "cmp/block-title/BlockTitle.vue";
import CouponActivityApi from "http/v2/coupon/CouponActivityApi";
import CheckStoreDialog from "pages/deposit/mbrdeposit/active/dialog/CheckStoreDialog.vue";
import Channel from "model/common/Channel";
import FormItem from "cmp/formitem/FormItem.vue";
import SelectStoreActiveDtlDialog from "pages/deposit/mbrdeposit/active/dialog/SelectStoreActiveDtlDialog.vue";
import ActiveStoreDtl from "cmp/activestoredtl/ActiveStoreDtl.vue";
import InviteRegisterGiftActivity from "model/v2/coupon/inviteregistergift/InviteRegisterGiftActivity";
import InviteRegisterRule from "model/v2/coupon/inviteregistergift/InviteRegisterRule";
import GiftInfo from "model/common/GiftInfo";
import RoutePermissionMgr from "mgr/RoutePermissionMgr";
import ChannelRange from "model/common/ChannelRange";
import MultipleChannelDtl from "cmp/channelselect/multiple/MultipleChannelDtl";
import UpgradeGiftActivityApi from "http/grade/upgradeGift/UpgradeGiftActivityApi";
import UpGradeGiftActivity from "model/grade/upgradegift/UpGradeGiftActivity";
import I18nPage from "common/I18nDecorator";
import GradeApi from "http/grade/grade/GradeApi";
import Grade from "model/grade/Grade";
import UpgradeLine from "model/grade/upgradegift/UpgradeLine";
import CouponWriteoffBill from "model/coupon/CouponWriteOff/CouponWriteoffBill";
import CouponWriteoffBillApi from "http/coupon/CouponWriteOff/CouponWriteoffBillApi";

@Component({
  name: "InviteSendGiftDtl",
  components: {
    BreadCrume,
    BlockTitle,
    CheckStoreDialog,
    MultipleChannelDtl,
    FormItem,
    SelectStoreActiveDtlDialog,
    ActiveStoreDtl,
  },
})
@I18nPage({
  prefix: [
    "/储值/预付卡/预付卡调整单/编辑页面",
    "/公用/按钮",
    "/卡/卡管理/预付卡充值单",
    "/营销/升级有礼",
    '/公用/券核销'
  ],
})
export default class InviteSendGiftDtl extends Vue {
  panelArray: any = [];
  dtl: CouponWriteoffBill = new CouponWriteoffBill();
  number: any = "";
  logData: any[] = [];
  created() {
    this.number = this.$route.query.number;
    this.panelArray = [
      {
        name: this.i18n("券核销单"),
        url: "coupon-write-off",
      },
      {
        name: this.i18n("券核销单详情"),
        url: "",
      },
    ];
    this.getDtl();
    this.getLog()
  }

  getLog() {
    CouponWriteoffBillApi.getLog(this.$route.query.number as string)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.logData = resp.data;
        }
      })
      .catch((error: any) => {
        if (error && error.msg) {
          this.$message.error(error.msg);
        }
      });
  }

  doAudut() {
    CouponWriteoffBillApi.audit(this.$route.query.number as string)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(
            this.formatI18n("/营销/券礼包活动/券礼包活动", "审核成功") as string
          );
          this.getDtl();
          this.getLog()
        } else {
            this.$message.error(resp.msg);
        }
      })
      .catch((error: any) => {
        if (error && error.msg) {
          this.$message.error(error.msg);
        }
      });
  }

  private getDtl() {
    CouponWriteoffBillApi.detail(this.$route.query.number as string)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.dtl = resp.data;
        }
      })
      .catch((error: any) => {
        if (error && error.msg) {
          this.$message.error(error.msg);
        }
      });
  }
}
