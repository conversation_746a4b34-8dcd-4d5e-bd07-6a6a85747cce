<template>
  <div class="add-payment">
    <el-button @click="addPaymentRow" ref="myButton" class="add-payment-row-button">{{i18n('添加支付方式')}}</el-button>
    <span class="add-payment-context">{{i18n('最多支持添加10个支付方式')}}</span>
    <el-table :data="payments" style="width: 500px" v-if="payments.length > 0">
      <el-table-column prop="paymentId" :label="i18n('支付方式')" width="180">
        <template slot-scope="scope">
          <el-select v-model="scope.row.paymentId" :placeholder="i18n('选择支付方式')" @change="handleSelectPayment(scope.row)">
            <el-option v-for="item in queryPaymentData" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="paymentAmount" :label="i18n('支付金额')" width="180">
        <template slot-scope="scope">
          <el-input v-model="scope.row.paymentAmount" :placeholder="i18n('输入金额')" @change="paymentAmountChange(scope)">
            <template slot="append">{{ i18n('元') }}</template>
          </el-input>
        </template>
      </el-table-column>
      <el-table-column :label="i18n('操作')" width="80">
        <template slot-scope="scope">
          <el-button type="text" @click="deletePaymentRow(scope.$index)">{{formatI18n('/公用/按钮','删除')}}</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script lang="ts" src="./AddPaymentCmp.ts">
</script>

<style lang="scss">
.add-payment {
  .add-payment-row-button {
    border-radius: 20px;
    padding: 10px 20px;
    color: #409eff;
    background-color: white;
    border: 2px solid #409eff;
  }
  .add-payment-row-button:hover {
    background-color: #ecf5ff;
    color: #409eff;
  }
  .add-payment-context {
    margin-left: 10px;
    color: #909399;
  }
  .el-table::before {
    height: 0;
  }
}
</style>