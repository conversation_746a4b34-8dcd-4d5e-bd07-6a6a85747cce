import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import CouponTemplateSelectorDialog from "cmp/selectordialogs/CouponTemplateSelectorDialog";
import CouponTemplateFilter from "model/coupon/template/CouponTemplateFilter";
import IdName from "model/common/IdName";
import CouponTemplate from "model/coupon/template/CouponTemplate";
import CouponTemplateApi from "http/coupon/template/CouponTemplateApi";
import GroupMutexTemplateData from "cmp/coupontenplate/cmp/GroupMutexTemplateData";
import CouponInfo from "model/common/CouponInfo";
import BrowserMgr from "mgr/BrowserMgr";
import { SuperpositionLevel } from "model/coupon/CouponSuperposition/SuperpositionLevel";
import { SuperpositionType } from "model/coupon/CouponSuperposition/SuperpositionType";
import CouponSuperposition from "model/coupon/CouponSuperposition/CouponSuperposition";
import I18nPage from "common/I18nDecorator";

@Component({
  name: "GroupMutexTemplate",
  components: {
    CouponTemplateSelectorDialog,
  },
})
@I18nPage({
  prefix: [
    "/公用/券模板",
    '/营销/券礼包活动/核销第三方券'
  ],
  auto: true
})
export default class GroupMutexTemplate extends Vue {
  @Prop({
    default: false,
    type: Boolean,
  })
  isSpecial: Boolean;
  @Prop({
    default: false,
    type: Boolean,
  })
  isDiscount: Boolean;
  @Prop({
    required: true,
    type: GroupMutexTemplateData,
  })
  value: GroupMutexTemplateData;
  @Prop({
    type: Boolean,
    default: false
  })
  canPileOtherCoupon: boolean;
  @Prop({
    type: Boolean,
    default: false
  })
  canPileSelfCoupon: boolean;
  @Prop({
    type: Boolean,
    default: true
  })
  showCurrentCoupon: boolean;

  $refs: any;
  copyFlag = "";
  rules: any = {};
  superpositionVersion = '';
  excludedTypes: string[] = ["goods", "freight"];
  templateFilter = new CouponTemplateFilter();
  ruleForm: any = {
    templateId: "",
    groupMutexFlag: true,
    groupMutexTemplates: [],
    superpositionLevel: SuperpositionLevel.TRADE,
    superpositionType: "NONREUSEABLE",
    superpositionTypeValue: [],
    nonSuperpositionTypeValue: SuperpositionType.CURRENT_COUPON,
    multipleNonSuperpositionTypeValue: []
  };
  reuseableCurrentDisable: Boolean = false;
  $eventHub: any;
  noLimit: Boolean = false

  @Watch('canPileOtherCoupon', { immediate: true })
  handle() {  //禁用时取消勾选“可与其他券模板叠加”
    if (this.canPileOtherCoupon) {
      this.ruleForm.superpositionTypeValue = this.ruleForm.superpositionTypeValue.filter((item: string) => item !== 'OTHER_COUPON')
      this.superpositionTypeValueChange()
    }
  }

  @Watch('showCurrentCoupon', { immediate: true })
  handleCanPileOtherCoupon() {  // 是否展示可与当前模板叠加
    if (!this.showCurrentCoupon) {
      this.ruleForm.superpositionTypeValue = this.ruleForm.superpositionTypeValue.filter((item: string) => item !== 'CURRENT_COUPON')
      this.superpositionTypeValueChange()
    }
  }

  created() {
    this.listenStep();
    this.rules = {
      superpositionTypeValue: [
        {
          required: true,
          validator: (rule: any, value: any, callback: any) => {
            if (
              this.ruleForm.superpositionLevel === "TRADE" &&
              this.ruleForm.superpositionType === "REUSEABLE"
            ) {
              if (!this.ruleForm.superpositionTypeValue?.length) {
                callback(
                  new Error(this.formatI18n("/公用/表单校验/请选择必选项"))
                );
              } else {
                callback();
              }
            } else {
              callback();
            }
          },
          trigger: "blur",
        },
      ],
      groupMutexTemplates: [
        {
          required: true,
          validator: (rule: any, value: any, callback: any) => {
            if (
              (this.ruleForm.superpositionType === "NONREUSEABLE" &&
                this.ruleForm.nonSuperpositionTypeValue === "OTHER_COUPON" &&
                this.noLimit)
              ||
              (this.ruleForm.superpositionType === "NONREUSEABLE" && this.ruleForm.multipleNonSuperpositionTypeValue.indexOf('OTHER_COUPON') > -1 && this.noLimit === false)
            ) {
              if (this.ruleForm.groupMutexTemplates.length === 0) {
                callback(
                  new Error(this.formatI18n("/公用/表单校验/请选择必选项"))
                );
              } else {
                callback();
              }
            } else {
              callback();
            }
          },
          trigger: "blur",
        },
      ],
      multipleNonSuperpositionTypeValue: [
        {
          required: true,
          validator: (rule: any, value: any, callback: any) => {
            if (
              this.ruleForm.superpositionType === 'NONREUSEABLE' && this.ruleForm.superpositionLevel === 'GOODS' && !this.noLimit
            ) {
              if (this.ruleForm.multipleNonSuperpositionTypeValue.length === 0) {
                callback(
                  new Error(this.formatI18n("/公用/表单校验/请选择必选项"))
                );
              } else {
                callback();
              }
            } else {
              callback();
            }
          },
          trigger: "blur",
        }
      ]
    };
    this.getShowFlag();
    // 叠加用券，前端控制展示单品还是订单级别
    this.ruleForm = {
      templateId: this.value.templateId || '',
      groupMutexFlag: this.value.groupMutexFlag || true,
      groupMutexTemplates: this.value.groupMutexTemplates || [],
      superpositionLevel: this.value.couponSuperposition?.superpositionLevel || SuperpositionLevel.TRADE,
      superpositionType: this.value.couponSuperposition?.superpositionType || "REUSEABLE",
      superpositionTypeValue: this.value.couponSuperposition?.superpositionTypeValue || [],
      nonSuperpositionTypeValue: this.value.couponSuperposition?.nonSuperpositionTypeValue || SuperpositionType.CURRENT_COUPON,
      multipleNonSuperpositionTypeValue: []
    };
  }

  listenStep() {
    setTimeout(() => {
      if (this.isSpecial === false || this.isDiscount === false) {
        this.$eventHub.$on("limitChange", (params: any) => {
          this.noLimit = params;
          let from = this.$route.query.from as string;
          if (from !== "edit" && from !== 'copy') {
            this.setDefaultByNoLimit();
          } else {
            this.setDefaultType();
          }
          this.doEmitParams()
        });
        this.$emit('checkLimit')
      }
    }, 0);
  }

  setDefaultType() {
    if (this.noLimit === true) {
      this.reuseableCurrentDisable = true;
    } else {
      this.reuseableCurrentDisable = false;
    }

    if (
      this.noLimit === true &&
      this.ruleForm.superpositionLevel === "GOODS" &&
      this.ruleForm.superpositionType === "NONREUSEABLE"
    ) {
      this.ruleForm.nonSuperpositionTypeValue = SuperpositionType.OTHER_COUPON;
    }

    if (this.ruleForm.superpositionLevel === "TRADE") {
      this.ruleForm.superpositionType = this.value.couponSuperposition?.superpositionType || "NONREUSEABLE"
      if (!this.ruleForm.superpositionTypeValue || this.ruleForm.superpositionTypeValue.length === 0) {
        this.ruleForm.superpositionTypeValue.push(SuperpositionType.CURRENT_COUPON);
      }
    }
  }

  setDefaultByNoLimit() {
    if (this.ruleForm.superpositionLevel === "TRADE") {
      this.ruleForm.superpositionType = "REUSEABLE";
      if (
        !this.ruleForm.superpositionTypeValue ||
        this.ruleForm.superpositionTypeValue.length != 2
      ) {
        this.ruleForm.superpositionTypeValue = [];
        this.ruleForm.superpositionTypeValue.push(
          SuperpositionType.CURRENT_COUPON
        );
      }

      this.reuseableCurrentDisable = true;
    } else {
      this.reuseableCurrentDisable = false;
    }

    if (
      this.noLimit === true &&
      this.ruleForm.superpositionLevel === "GOODS" &&
      this.ruleForm.superpositionType === "NONREUSEABLE"
    ) {
      this.ruleForm.nonSuperpositionTypeValue = SuperpositionType.OTHER_COUPON;
    }
  }

  public initValue2(coupon: CouponInfo, copyFlag: string) {
    this.copyFlag = this.$route.query.from as string;

    if (this.copyFlag) {
      // this.copyFlag = copyFlag;
      this.value.name = coupon.name;
      this.value.templateId = coupon.templateId;
      this.value.groupMutexFlag = coupon.groupMutexFlag;
      this.value.groupMutexTemplates = coupon.groupMutexTemplates;
      this.value.couponSuperposition = coupon.couponSuperposition;
      // this.initRuleFormValue(this.value);
      this.initRuleFormValueNew(this.value);
    }
  }

  initRuleFormValueNew(value: GroupMutexTemplateData) {
    console.log(value);

    if (value && value.couponSuperposition) {
      this.ruleForm.superpositionLevel =
        value.couponSuperposition.superpositionLevel;
      this.ruleForm.superpositionType =
        value.couponSuperposition.superpositionType;
      this.ruleForm.superpositionTypeValue =
        value.couponSuperposition.superpositionTypeValue;
      this.ruleForm.nonSuperpositionTypeValue =
        value.couponSuperposition.nonSuperpositionTypeValue;
      if (value.couponSuperposition.nonSuperpositionTypeValue) {
        this.ruleForm.multipleNonSuperpositionTypeValue = [value.couponSuperposition.nonSuperpositionTypeValue]
      }

      this.ruleForm.groupMutexTemplates =
        value.couponSuperposition.groupMutexTemplates;
      if (value.couponSuperposition.nonSuperpositionTypeValue === SuperpositionType.ALL_COUPON && this.ruleForm.superpositionType === 'NONREUSEABLE' && this.ruleForm.superpositionLevel === 'GOODS' && !this.noLimit) {
        this.ruleForm.multipleNonSuperpositionTypeValue = [SuperpositionType.CURRENT_COUPON, SuperpositionType.OTHER_COUPON]
      }
    }
    this.doEmitParams()
    // if (value.firstInit) {
    // 	value.firstInit = false;
    // 	this.doEmitParams();
    // }
  }

  initRuleFormValue(value: GroupMutexTemplateData) {
    this.templateFilter.typeExcluded = ["goods"];
    if (this.copyFlag == "copy") {
      this.ruleForm.groupMutexFlag = true;
      this.ruleForm.groupMutexTemplates = [];
    } else {
      this.ruleForm.templateId = value.templateId;
      if (value.groupMutexFlag === null) {
        this.ruleForm.groupMutexFlag = true;
      } else {
        this.ruleForm.groupMutexFlag = value.groupMutexFlag;
      }
      if (
        !value.groupMutexFlag &&
        (!value.groupMutexTemplates || value.groupMutexTemplates.length === 0)
      ) {
        this.ruleForm.groupMutexTemplates = [this.initFirstTemplate()];
      } else {
        this.ruleForm.groupMutexTemplates = this.resortTemplate(
          value.groupMutexTemplates
        );
      }
    }
    if (value.firstInit) {
      value.firstInit = false;
      this.doEmitParams();
    }
  }

  doGroupMutexFlagChange() {
    this.ruleForm.groupMutexTemplates = [];
    if (!this.ruleForm.groupMutexFlag) {
      this.ruleForm.groupMutexTemplates = [this.initFirstTemplate()];
    }
    this.doEmitParams();
  }

  doGroupMutexTypeChange() {
    this.ruleForm.groupMutexTemplates = [];
    this.doEmitParams();
  }
  doMultipleNonSuperpositionTypeValueChange() {
    if (this.ruleForm.multipleNonSuperpositionTypeValue.indexOf('OTHER_COUPON') === -1) {
      this.ruleForm.groupMutexTemplates = []
    }
    this.doEmitParams();
  }

  superpositionTypeValueChange() {
    this.ruleForm.groupMutexTemplates = [];
    this.doEmitParams();
  }

  levelChange() {
    this.ruleForm.superpositionType = "REUSEABLE";
    this.ruleForm.superpositionTypeValue = [];
    this.ruleForm.nonSuperpositionTypeValue = SuperpositionType.CURRENT_COUPON;
    if (this.ruleForm.superpositionLevel === SuperpositionLevel.TRADE) {
      this.ruleForm.superpositionTypeValue = [];
      if (this.showCurrentCoupon) {
        this.ruleForm.superpositionTypeValue.push(
            SuperpositionType.CURRENT_COUPON
        );
      }
    }
    this.ruleForm.multipleNonSuperpositionTypeValue = []
    this.doEmitParams();
  }

  doTypeChange(value: 'NONREUSEABLE' | 'REUSEABLE') {
    this.ruleForm.superpositionTypeValue = [];
    if (
      this.ruleForm.superpositionLevel === "GOODS" &&
      this.ruleForm.superpositionType === "NONREUSEABLE" &&
      this.noLimit === false
    ) {
        this.ruleForm.nonSuperpositionTypeValue =
            SuperpositionType.CURRENT_COUPON;
    }
    // 无门槛状态下，订单级别 从“不可叠加”切换到“可叠加”，需要默认勾选并禁用“可与当前券模板叠加”
    if (this.ruleForm.superpositionLevel === 'TRADE' && value === 'REUSEABLE') {
      if (this.showCurrentCoupon) {
        this.ruleForm.superpositionTypeValue.push(SuperpositionType.CURRENT_COUPON);
      }
    }
    this.doEmitParams();
  }

  delMutexLine(lines: any[], index: number) {
    lines.splice(index, 1);
    this.doEmitParams();
  }

  doValidate() {
    if (this.$refs.groupMutexTemplateRuleForm) {
      let p0 = new Promise<void>((resolve, reject) => {
        this.$refs.groupMutexTemplateRuleForm.validate((valid: any) => {
          if (valid) {
            resolve();
          }
        });
      });
      return p0;
    } else {
      let p0 = new Promise<void>((resolve, reject) => {
        resolve();
      });
      return p0;
    }
  }

  doSelectCouponTemplate() {
    let templates: CouponTemplate[] = [];
    if (
      this.ruleForm.groupMutexTemplates &&
      this.ruleForm.groupMutexTemplates.length > 0
    ) {
      this.ruleForm.groupMutexTemplates.forEach((item: IdName) => {
        if (item.id && item.name) {
          let obj: CouponTemplate = new CouponTemplate();
          obj.number = item.id;
          obj.name = item.name;
          templates.push(obj);
        }
      });
    }
    let alwaysSelectedIds: any[] = [];
    // if (this.value.templateId && this.copyFlag !== "copy") {
    // 	let obj: CouponTemplate = new CouponTemplate();
    // 	obj.number = this.value.templateId;
    // 	obj.name = this.value.name;
    // 	templates.unshift(obj);
    // 	alwaysSelectedIds.push(this.value.templateId);
    // }
    this.$refs.couponTemplate.open(
      templates,
      "multiple",
      alwaysSelectedIds,
      true
    );
  }

  doSetSelectedTemplates(arr: CouponTemplate[]) {
    console.log(arr);

    this.ruleForm.groupMutexTemplates = [];
    if (arr && arr.length > 0) {
      let numbers: any = [];
      let idNames: any = [];
      let groupIds: any = [];
      arr.forEach((item: CouponTemplate) => {
        if (item.groupId) {
          groupIds.push(item.groupId);
        } else {
          if (this.ruleForm.templateId !== item.number) {
            idNames.push(this.newIdName(item.number, item.name));
          }
          numbers.push(item.number);
        }
      });
      if (groupIds.length > 0) {
        CouponTemplateApi.getTemplatesGroup(groupIds)
          .then((resp: any) => {
            if (resp && resp.code === 2000) {
              if (resp.data && resp.data.length > 0) {
                resp.data.forEach((item: IdName) => {
                  if (!numbers.includes(item.id)) {
                    idNames.push(item);
                  }
                });
              }
              this.setSelectTemplates(idNames);
            }
          })
          .catch((error: any) => {
            this.$message.error(error.message);
          });
      } else {
        this.setSelectTemplates(idNames);
      }
    } else {
      this.setSelectTemplates([]);
    }
  }

  doEmitParams() {
    let templates = [];
    if (this.ruleForm.groupMutexTemplates) {
      this.ruleForm.groupMutexTemplates.forEach((obj: any, idx: number) => {
        if (idx != 1) {
          templates.push(this.newIdName(obj.id, obj.name));
        }
      });
    }
    // this.value.groupMutexFlag = this.ruleForm.groupMutexFlag;
    if (!this.value.couponSuperposition) {
      this.value.couponSuperposition = new CouponSuperposition();
    }
    if (this.ruleForm.superpositionType === "REUSEABLE") {
      //可叠加，删除不可叠加相关的值
      this.ruleForm.groupMutexTemplates = [];
      this.ruleForm.nonSuperpositionTypeValue = null;
      if (this.ruleForm.superpositionLevel === "GOODS") {
        this.ruleForm.superpositionTypeValue = [];
      }
    } else {
      //  不可叠加，删除可叠加相关的值
      this.ruleForm.superpositionTypeValue = [];
      if (this.ruleForm.superpositionLevel === "TRADE") {
        this.ruleForm.nonSuperpositionTypeValue = null;
      }
    }
    this.value.couponSuperposition.superpositionLevel = this.ruleForm.superpositionLevel;
    this.value.couponSuperposition.superpositionType = this.ruleForm.superpositionType;
    this.value.couponSuperposition.superpositionTypeValue = this.ruleForm.superpositionTypeValue;
    this.value.couponSuperposition.nonSuperpositionTypeValue = this.ruleForm.nonSuperpositionTypeValue;
    if (this.ruleForm.superpositionType === 'NONREUSEABLE' && this.ruleForm.superpositionLevel === 'GOODS' && !this.noLimit) {
      console.log(this.ruleForm.multipleNonSuperpositionTypeValue);
      if (this.ruleForm.multipleNonSuperpositionTypeValue.length === 1) {
        this.value.couponSuperposition.nonSuperpositionTypeValue = this.ruleForm.multipleNonSuperpositionTypeValue[0]
      } else {
        this.value.couponSuperposition.nonSuperpositionTypeValue = SuperpositionType.ALL_COUPON
      }
    }
    this.value.couponSuperposition.groupMutexTemplates = this.ruleForm.groupMutexTemplates;
    console.log('最终emit的数据',this.value);
    this.$emit("input", this.value);
    this.$emit("change");
  }

  newIdName(id: any, name: any) {
    let idName: IdName = new IdName();
    idName.id = id;
    idName.name = name;
    return idName;
  }

  private setSelectTemplates(idNames: IdName[]) {
    console.log(idNames);

    idNames.sort();
    // idNames.unshift(this.initFirstTemplate());
    this.ruleForm.groupMutexTemplates = idNames;
    this.clearValidate();
    this.doEmitParams();
  }

  private resortTemplate(templates: any[]) {
    if (this.value.templateId) {
      let sortedData: IdName[] = [];
      if (templates && templates.length > 0) {
        templates.forEach((item: IdName) => {
          if (this.value.templateId !== item.id) {
            sortedData.push(item);
          }
        });
        sortedData.sort();
        sortedData.unshift(
          this.newIdName(this.value.templateId, this.value.name)
        );
      }
      return sortedData;
    } else {
      return templates;
    }
  }

  private clearValidate() {
    if (this.$refs.groupMutexTemplateRuleForm) {
      this.$refs.groupMutexTemplateRuleForm.clearValidate();
    }
  }

  private initFirstTemplate() {
    let idName: IdName = new IdName();
    if (this.value.templateId && this.copyFlag !== "copy") {
      idName.name = this.value.name;
      idName.id = this.value.templateId;
    }
    return idName;
  }

  private getShowFlag() {
    let superpositionVersion = BrowserMgr.LocalStorage.getItem("superpositionVersion") || 'V1'
    this.superpositionVersion = superpositionVersion;
  }
}
