import {Component, Vue} from "vue-property-decorator";
import UserLoginResult from "model/login/UserLoginResult";
import ZoneFilter from "model/datum/zone/ZoneFilter";
import ZoneApi from "http/area/ZoneApi";
import OrgApi from "http/org/OrgApi";
import FormItem from "cmp/formitem/FormItem.vue";
import BreadCrume from "cmp/bread-crumb/BreadCrume";
import {State} from "vuex-class";
import I18nPage from "common/I18nDecorator";
import AddressSelector from "cmp/addressselector/AddressSelector";
import RSSaveBatchOrgRequest from "model/common/RSSaveBatchOrgRequest";
import RSOrg from 'model/common/RSOrg'
import Address from "model/common/Address";
import IdName from "model/common/IdName";
import RSOrgFilter from "model/common/RSOrgFilter";
import CommonUtil from 'util/CommonUtil'
import RSChannelManagementFilter from "model/common/RSChannelManagementFilter";
import Channel<PERSON>anagement<PERSON><PERSON> from "http/channelmanagement/ChannelManagementApi";
import OrgTag from "model/datum/org/OrgTag";
import {OrgState} from "model/common/OrgState";
import SelectEmployees from "cmp/selectEmployees/selectEmployees";

@Component({
  name: "Store",
  components: {
    FormItem,
    BreadCrume,
    AddressSelector,
    SelectEmployees
  },
})
@I18nPage({
  auto: false,
  prefix: [
    '/资料/渠道',
    '/公用/按钮',
    '/资料/门店'
  ],
})
export default class StoreAdd extends Vue {
  i18n: I18nFunc
  @State("loginInfo")
  loginInfo: UserLoginResult;
  panelArray: any = [];
  marketCenter: any = [];
  marketCenterName: any = [];
  storeFlag: string = 'add'
  $refs: any;
  rules: any = {
    'org.id': [
      { required: true, message: this.formatI18n("/资料/门店/请输入门店代码"), trigger: 'blur' }
    ],
    'org.name': [
      { required: true, message: this.formatI18n("/资料/门店/请输入门店名称"), trigger: 'blur' }
    ],
  };
  areaData: any = []; // 区域查询
  orgId: any = ''
  tagValue: string = ''
  tagMaxLimit: number = 3 //门店标签数量限制
  memberStore: boolean = false  //是否为会员店，前端不处理，只保存回传
  ZoneFilter: ZoneFilter = new ZoneFilter();
  // 平台门店 参数
  platformList: any[] = [];
  platOrgInfos: any[] = [];
  // 已选平台门店code
  selectedCode: any[] = [];
  ruleForm: any = {
    org: {
      id: '',
      name: ''
    },
    zoneId: '',
    address: null,
    addressInfo: '',
    lng: '',
    lat: '',
    phoneNumber: '',
    tags: [],
    areaManager: null, //区域主管
    operationManager: null //营运经理
  }
  selectCascader: any = []
  propsCascader = {
    value: 'value',
    label: 'name',
    children: 'children',
    disabled: 'disabled'
  }
  enableMultiMarketingCenter: boolean = true
  orgState: OrgState = OrgState.enable

  async created() {
    this.panelArray = [
      {
        name: this.formatI18n("/公用/菜单/门店"),
        url: "",
      },
    ];
    // this.platOrgInfos.push({ selectCascader: [], platformStoreId: '' });
    this.storeFlag = this.$route.query.storeFlag as string
    this.orgId = this.$route.query.orgId as string
    this.enableMultiMarketingCenter = this.$route.query.enableMultiMarketingCenter == '1' ? true : false
    // sessionStorage.setItem("isMultipleMC", "0");
    this.marketCenter = sessionStorage.getItem("marketCenter");
    this.marketCenterName = sessionStorage.getItem("marketCenterName");
    this.getAreaList()
    await this.getPlatform()
    this.queryTagsNumberLimit()
    if (this.orgId) {
      this.query()
    }
  }
  getAreaList() {
    ZoneApi.query(this.ZoneFilter).then((res) => {
      if (res.code === 2000) {
        this.areaData = res.data;
      } else {
        this.$message.error(res.msg as string);
      }
    });
  }
  doSave() {
    this.$refs.ruleForm.validate((valid: any) => {
      if (valid) {
        if (this.storeFlag == 'edit') {
          this.modify()
        } else {
          this.add()
        }
      }
    })
  }
  doCancel() {
    this.$router.go(-1)
  }
  addPlatformStore() {
    this.platOrgInfos.push({ selectCascader: [], platformStoreId: '' });
  }
  removePlatformStore(index: number) {
    this.platformList.forEach((item: any) => {
      if (this.platOrgInfos![index].selectCascader && this.platOrgInfos![index].selectCascader.length > 0 && this.platOrgInfos![index].selectCascader[0] === item.value) {
        item.disabled = false;
      }
    });
    this.platOrgInfos!.splice(index, 1);
  }
  checkDisabled(list: any[], platOrgInfos: any[], index: number, code: any) {
    list.forEach((v: { disabled: boolean; code: any; }) => {
      v.disabled = false;
      for (var i = 0; i < platOrgInfos!.length; i++) {
        if (platOrgInfos![i].platformCode === v.code) {
          v.disabled = true;
        }
      }
    });
  }
  //已经选过的平台，不能重复选
  changeCascader(ind: any) {
    this.$nextTick(() => {
      //platformList 联级选择器的列表， platOrgInfos  已选择的列表
      this.platformList.forEach((item) => {
        item.disabled = this.platOrgInfos.findIndex((v, i) => v.selectCascader[0] === item.value && i != ind) > -1
      })
    })
  }
  handleItemChange(val: any) {
    let channelType = val[0] || ''
    let flagIndex = this.platformList.findIndex(item => item.value === channelType)
    if (flagIndex > -1 && this.platformList[flagIndex].children.length == 0) {
      this.lazyLoad(channelType, flagIndex)
    }
  }
  async lazyLoad(type: string, flagIndex: number) {
    let param: RSChannelManagementFilter = new RSChannelManagementFilter();
    param.channelTypeEquals = type
    param.page = 0
    param.pageSize = 50
    try {
      const { data } = await ChannelManagementApi.query(param)
      this.platformList[flagIndex].children = (data || []).reduce((acc: any, cur: any) => {
        acc.push({
          value: cur.channel.id,
          name: "[" + cur.channel.id + "]" + cur.name ,
          channelName: cur.name
        })
        return acc
      }, [])
      this.$forceUpdate()
    } catch (err) {
      console.log(err)
    }
  }
  async getPlatform() {
    this.platformList = []
    try {
      const res: any = await OrgApi.getPlatform()
      if (res.data && res.data.length > 0) {
        res.data.forEach((item: any) => {
          if(item.channelType != 'MeiTuanShopCatering' && item.channelType != 'MeiTuanPT') {
            this.platformList.push({
              value: item.channelType,
              name: item.platForm.name,
              disabled: false,
              children: []
            });
          } 
        });
      }
    } catch (err) {
      console.log(err)
    }
  }
  // 编辑门店
  modify() {
    const body = this.doSaveParams()
    if (!body) return
    let req = new RSSaveBatchOrgRequest();
    req.list = [body];
    req.operator = this.loginInfo.user!.account || "";
    let filter = new RSOrgFilter();
    filter.orgIdEquals = body.org.id;
    const loading = CommonUtil.Loading()
    OrgApi.saveBatch(req)
      .then((resp: any) => {
        loading.close()
        if (resp && resp.code === 2000) {
          this.$message.success(this.formatI18n("/资料/门店/修改成功"));
          this.$router.replace({ name: 'StoreDtl', query: { orgId: body.org.id } })
        } else {
          this.$message.error(resp.msg);
        }
      })
      .catch((error) => {
        loading.close()
        if (error && error.message) {
          this.$message.error(error.message);
        }
      })
  }
  // 新增门店
  add() {
    const body = this.doSaveParams()
    if(!body) return
    let req = new RSSaveBatchOrgRequest();
    req.list = [body];
    req.operator = this.loginInfo.user!.account || "";
    let filter = new RSOrgFilter();
    filter.orgIdEquals = body.org.id;
    const loading = CommonUtil.Loading()
    OrgApi.isExistOrg(filter)
      .then((res: any) => {
        if (res.code === 2000) {
          loading.close()
          if (res.data && res.data.length > 0) {
            this.$message.error(this.formatI18n("/资料/门店/门店已存在"));
            return;
          } else {
            OrgApi.saveBatch(req)
              .then((resp: any) => {
                loading.close()
                if (resp && resp.code === 2000) {
                  this.$message.success(this.formatI18n("/资料/门店/添加成功"));
                  this.$router.replace({ name: 'StoreDtl', query: { orgId: body.org.id } })
                } else {
                  this.$message.error(resp.msg);
                }
              })
              .catch((error) => {
                loading.close()
                if (error && error.message) {
                  this.$message.error(error.message);
                }
              })
          }
        } else {
          loading.close()
          this.$message.error(res.msg);
        }
      })
      .catch((error) => {
        loading.close()
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
  }
  // 查询门店详情
  async query() {
    try {
      const { data } = await OrgApi.detail(this.orgId)
      this.$nextTick(() => {
        this.ruleForm.lat = data.lat
        this.ruleForm.lng = data.lng
        this.ruleForm.org = data.org
        this.ruleForm.tags = data.orgTags || []
        this.memberStore = data.memberStore
        this.ruleForm.phoneNumber = data.telephone
        this.ruleForm.marketCenter = data.marketCenter
        this.ruleForm.marketingCenterName = data.marketingCenterName
        this.ruleForm.zoneId = data.zoneId
        this.ruleForm.addressInfo = data.address.address
        this.ruleForm.address = []
        this.ruleForm.address.push(data.province)
        this.ruleForm.address.push(data.city)
        this.ruleForm.address.push(data.district)
        this.ruleForm.address.push(data.street)
        if (data.areaLeader) {  //区域主管
          this.ruleForm.areaManager = new IdName()
          this.ruleForm.areaManager.id = data.areaLeader
          this.ruleForm.areaManager.name = data.areaLeaderName
        }
        if (data.operationManager) {  //营运经理
          this.ruleForm.operationManager = new IdName()
          this.ruleForm.operationManager.id = data.operationManager
          this.ruleForm.operationManager.name = data.operationManagerName
        }
        this.platOrgInfos = (data.platOrgInfos || []).reduce((acc: any[], cur: any) => {
          acc.push({
            selectCascader: [cur.channelType, cur.channelId],
            platformStoreId: cur.platformStoreId
          })
          let currentIndex = this.platformList.findIndex(platform => platform.value == cur.channelType)
          this.lazyLoad(cur.channelType, currentIndex)
          return acc
        }, [])
        this.marketCenter = data.marketingCenter
        this.marketCenterName = data.marketingCenterName
        this.orgState = data.orgState
      })
    } catch (error: any) {
      if (error && error.message) {
        this.$message.error(error.message);
      }
    }
  }
  getChannels() {
    let param: RSChannelManagementFilter = new RSChannelManagementFilter();
    ChannelManagementApi.query(param)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          // this.channels = resp.data;
        }
      })
      .catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
  }
  //查询门店标签数量限制
  queryTagsNumberLimit() {
    OrgApi.getTagLimitCount().then((res) => {
      if (res.code === 2000) {
        this.tagMaxLimit = res.data || 3
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => this.$message.error(error.message))
  }
  //添加标签
  doAddTags() {
    if (this.ruleForm.tags.indexOf(this.tagValue) > -1) {
      return this.$message.warning(this.i18n('不可添加重复标签'))
    }
    if (this.tagValue) {
      const obj = new OrgTag()
      obj.tagName = this.tagValue
      this.ruleForm.tags.push(obj)
      this.tagValue = ''
    }
  }
  // 标签长度不能超过8个字符，中文算两个字符
  doTagValueChange(value: string) {
    this.tagValue = CommonUtil.truncateString(value.trim(), 8)
  }
  // 去除标签
  handleClose(index: number) {
    this.ruleForm.tags.splice(index, 1)
  }
  doSaveParams() {
    const params = new RSOrg()
    params.marketingCenter = this.marketCenter
    params.marketingCenterName = this.marketCenterName
    params.lng = this.ruleForm.lng
    params.lat = this.ruleForm.lat
    params.org = this.ruleForm.org
    params.telephone = this.ruleForm.phoneNumber ? this.ruleForm.phoneNumber : null
    params.orgTags = this.ruleForm.tags || null
    params.memberStore = this.memberStore
    params.orgState = this.orgState
    if (this.ruleForm.areaManager?.id) {  //区域主管
      params.areaLeader = this.ruleForm.areaManager.id
      params.areaLeaderName = this.ruleForm.areaManager.name
    }
    if (this.ruleForm.operationManager?.id) {  //营运经理
      params.operationManager = this.ruleForm.operationManager.id
      params.operationManagerName = this.ruleForm.operationManager.name
    }
    const area = this.areaData.find((item: any) => item.zone.id === this.ruleForm.zoneId)
    if (area) {
      params.zone = area.zone
    }
    if (this.ruleForm.address && this.ruleForm.address.length > 0) {
      let address = new Address();
      let idName = new IdName();
      if (this.ruleForm.address[0]) {
        idName = this.ruleForm.address[0];
        address.province = idName.name
      }
      if (this.ruleForm.address[1]) {
        idName = this.ruleForm.address[1];
        address.city = idName.name
      }
      if (this.ruleForm.address[2]) {
        idName = this.ruleForm.address[2];
        address.district = idName.name
      }
      if (this.ruleForm.address[3]) {
        idName = this.ruleForm.address[3];
        address.street = idName.name
      }
      if (this.ruleForm.addressInfo) {
        address.address = this.ruleForm.addressInfo;
      }
      params.address = address;
    }
    if (this.platOrgInfos && this.platOrgInfos.length > 0) {
      let pltCount = 0;
      let idCount = 0;
      let platOrgInfos: any = []
      this.platOrgInfos.forEach((item: any) => {
        if (item.selectCascader.length == 0) {
          pltCount++;
        }
        if (!item.platformStoreId) {
          idCount++;
        }
        let children = []
        let channelName = ''
        if (item.selectCascader.length > 0) {
          children = this.platformList.find(platform => platform.value == item.selectCascader[0])?.children || []
          channelName = children.find((child: any) => child.value == item.selectCascader[1])?.channelName || ''
        }
        platOrgInfos.push({
          platformStoreId: item.platformStoreId,
          channelId: item.selectCascader[1],
          channelType: item.selectCascader[0],
          channelName,
        })
      });
      if (pltCount > 0) {
        this.$message.error(this.formatI18n("/资料/门店/请选择平台"));
        return;
      }
      if (idCount > 0) {
        this.$message.error(this.formatI18n("/资料/门店/请填写平台门店id"));
        return;
      }
      params.platOrgInfos = platOrgInfos;
    }
    return params
  }
}

