/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-04-12 14:57:41
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\locale\Locale.js
 * 记得注释
 */
import Vue from 'vue'
import VueI18n from 'vue-i18n'
import locale from 'fant3-hd/lib/locale'
import enLocale from 'fant3-hd/lib/locale/lang/en'
import zhLocale from 'fant3-hd/lib/locale/lang/zh-CN'
import twLocale from 'fant3-hd/lib/locale/lang/zh-TW';


Vue.use(VueI18n)

const messages = {
  en_US: {
    ...require('./lang/en'),
    ...enLocale
  },
  zh_CN: {
    ...require('./lang/zh'),
    ...zhLocale
  },
  zh_TW: {
    ...require('./lang/tw'),
    ...twLocale
  }
}

const i18n = new VueI18n({
  locale: 'zh_CN',    // 语言标识
  messages,
  silentTranslationWarn: true
})
locale.i18n((key, value) => i18n.t(key, value)) //重点：为了实现element插件的多语言切换

export default i18n
