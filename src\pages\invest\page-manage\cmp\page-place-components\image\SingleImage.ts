import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import PlaceTemplateMixins from '../PlaceTemplateMixins';
import I18nPage from 'common/I18nDecorator';

@Component({
  name: 'SingleImage',
  components: {},
  mixins: [PlaceTemplateMixins],
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理',
  ],
  auto: true
})
export default class SingleImage extends Vue {
  @Prop()
  componentItem: any;
  get bgUrl() {
    return   'delivery/home_page_top_new.png';
  }
  get localProperty() {
    return this.componentItem.props;
  }
  // toolbarClick(e) {
  //   this.$emit('toolBarClick', {
  //     clickName: e,
  //     activeIndex: this.activeIndex,
  //   });
  // }
}
