/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-09-12 18:19:37
 * @LastEditTime: 2024-09-12 18:51:13
 * @LastEditors: fang<PERSON><PERSON><PERSON>
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\precisionmarketing\userGroup\SortedCategory.ts
 * 记得注释
 */
export default class SortedCategory {
  // uuid
  uuid: Nullable<string> = null
  // 排序值
  sequence: Nullable<number> = null
}