import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import I18nPage from "common/I18nDecorator";
import GrowthValueFilter from "model/member/GrowthValueFilter";
import MemberApi from "http/member_standard/MemberApi";
import BGrowthValue from "model/member/BGrowthValue";
import ListWrapper from "cmp/list/ListWrapper";

@Component({
  name: "MemberGrowthDetailDialog",
  components: { ListWrapper },
})
@I18nPage({
  prefix: [
    "/会员/会员资料",
    "/会员/选择人群",
    "/公用/券模板",
  ],
  auto: true,
})
export default class MemberGrowthDetailDialog extends Vue {

  @Prop({
    type: Boolean,
    default: false,
  })
  value: boolean;
  @Prop()
  memberId: string;

  @Watch("value")
  onShow() {
    if (this.value)
      this.onSearch();
  }

  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10,
    probeEnabled: null,
  };
  query: GrowthValueFilter = new GrowthValueFilter();
  queryData: BGrowthValue[] = [];

  onSearch() {
    this.page.currentPage = 1;
    this.getList();
  }

  private getList() {
    this.query.memberId = this.memberId;
    this.query.page = this.page.currentPage - 1;
    this.query.pageSize = this.page.size;
    MemberApi.queryGrowthValue(this.query).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.queryData = resp.data;
        this.page.total = resp.total;
        this.page.probeEnabled = resp.fields ? resp.fields.probeEnabled : null;
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message);
      }
    });
  }

  getGrowthValueColor(value: number) {
    // 随便给个值
    if (!value) return "none";
    return (value > 0 ? "#27CB7B" : "#FF5379");
  }

  getGrowthValueLabel(value: number) {
    if (!value) return "0";
    return (value > 0 ? "+" : "") + value;
  }

  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val;
    this.getList();
  }

  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val;
    this.getList();
  }

  beforeClose() {
    this.$emit("input", false);
  }
}
