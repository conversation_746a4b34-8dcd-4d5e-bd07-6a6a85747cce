import ApiClient from 'http/ApiClient'
import ChannelBatchRequest from 'model/channel/ChannelBatchRequest'
import RSChannelManagementFilter from 'model/common/RSChannelManagementFilter';
import RSChannelManagement from 'model/common/RSChannelManagement';
import Response from 'model/common/Response'
import SaveChan<PERSON>Request from 'model/channel/SaveChannelRequest'
import IdName from 'model/common/IdName';

export default class ChannelManagementApi {
    /**
     * 禁用
     * 禁用。
     *
     */
    static disable(body: ChannelBatchRequest): Promise<Response<void>> {
        return ApiClient.server().post(`/v1/channel-management/disable`, body, {}).then((res) => {
            return res.data
        })
    }

    /**
     * 启用
     * 启用。
     *
     */
    static enable(body: ChannelBatchRequest): Promise<Response<void>> {
        return ApiClient.server().post(`/v1/channel-management/enable`, body, {}).then((res) => {
            return res.data
        })
    }

    /**
     * 更新
     * 更新。
     *
     */
    static modify(body: SaveChannelRequest): Promise<Response<void>> {
        return ApiClient.server().post(`/v1/channel-management/modify`, body, {}).then((res) => {
            return res.data
        })
    }

    /**
     * 查询券承担方
     * 查询券承担方。
     *
     */
    static async query(body: RSChannelManagementFilter): Promise<Response<RSChannelManagement[]>> {
        return await ApiClient.server().post(`/v1/channel-management/query`, body, {}).then((res) => {
            return res.data
        })
    }

    /**
     * 保存
     * 保存。
     *
     */
    static save(body: SaveChannelRequest): Promise<Response<void>> {
        return ApiClient.server().post(`/v1/channel-management/save`, body, {}).then((res) => {
            return res.data
        })
    }

    /**
     * 批量保存
     * 批量保存。
     *
     */
    static saveBatch(body: ChannelBatchRequest): Promise<Response<void>> {
        return ApiClient.server().post(`/v1/channel-management/saveBatch`, body, {}).then((res) => {
            return res.data
        })
    }
    
    /** 
     * 查询渠道类型
     * 
     * **/
  static getChannelTypes(): Promise<Response<IdName[]>> {
    return ApiClient.server().get(`/v1/channel/type/gets`, {}).then((res) => {
      return res.data
    })
  }
}
