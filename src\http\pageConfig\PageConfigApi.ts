import ApiClient from 'http/ApiClient'
import CrmPageConfig from "model/pageConfig/CrmPageConfig";
import Response from 'model/common/Response'

export default class PageConfigApi {
  /**
   * 获取CRM页面配置
   * 获取CRM页面配置
   * 
   */
  static getConfig(): Promise<Response<CrmPageConfig>> {
    return ApiClient.server().get(`/v1/page-config/getConfig`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 是否展示“允许CRM后台核销”按钮
   * 是否展示“允许CRM后台核销”按钮
   * 
   */
  static showEnableWriteOffByPhx(): Promise<Response<boolean>> {
    return ApiClient.server().get(`/v1/page-config/showEnableWriteOffByPhx`, {
    }).then((res) => {
      return res.data
    })
  }

}
