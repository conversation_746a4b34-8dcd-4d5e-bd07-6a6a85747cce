/*
 * @Author: l<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-05 16:17:24
 * @LastEditors: liyulong <EMAIL>
 * @LastEditTime: 2023-03-05 16:17:29
 * @FilePath: \phoenix-web-ui\src\model\benefit\BEquityCardValidity.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { ExpiryType } from 'model/common/ExpiryType'

export default class BEquityCardValidity {
  // 有效期类型：FIXED——固定有效期； RALATIVE——相对有效期;
  validityType: Nullable<string> = null
  // 有效日期
  validityDays: Nullable<number> = null
  // 相对有效期下的具体有效类型DAYS("按天数"),MONTHS("按月数"),YEARS("按年数"),
  expiryType: Nullable<ExpiryType> = null
}