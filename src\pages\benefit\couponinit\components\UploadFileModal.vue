<template>
  <el-dialog :before-close="doBeforeClose" :close-on-click-modal="false"
             :title="formatI18n('/储值/预付卡/预付卡调整单/列表页面/导入')" :visible.sync="dialogShow"
             class="import-coupon-template-dialog">
    <div class="wrap">

      <FormItem label=" ">
        {{ formatI18n("/公用/导入", "实例模板") }}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <a class="action-hover_download" @click="downloadTemplate"
           style="line-height: 12px; color: #318BFF;font-size: 12px;text-decoration: none">{{ formatI18n("导入兑换券券模板") }}</a>
      </FormItem>
      <FormItem>
        <div style="position: relative;top: 9px; margin-bottom: 20px;font-size: 12px;">{{ getLimitInfo }}</div>
        <el-upload
          :action="uploadUrl"
          :auto-upload="false"
          :on-change="doHandleChange"
          :on-error="getErrorInfo"
          :data="bindData"
          :headers="uploadHeaders"
          :on-success="getSuccessInfo"
          :with-credentials="true"
          class="upload-demo"
          ref="upload">
          <el-button size="small" slot="trigger" type="default">{{ formatI18n("/公用/导入", "选择文件") }}</el-button>
        </el-upload>
      </FormItem>
      <FormItem>
        <div style="margin-left: 280px;">
          <el-button @click="doModalClose('cancel')">{{ formatI18n("/公用/按钮", "取消") }}</el-button>
          <el-button @click="doModalClose('confirm')" size="small" type="primary">
            {{ formatI18n("/公用/导入", "确认导入") }}
          </el-button>
        </div>
      </FormItem>
    </div>


  </el-dialog>
</template>

<script lang="ts" src="./UploadFileModal.ts">
</script>

<style lang="scss">
.import-coupon-template-dialog {
  display: flex;
  justify-content: center;
  align-items: center;

  .wrap {
    margin-top: 30px;
  }

  .el-dialog {
    width: 600px;
    margin: 0 !important;
  }

  .el-dialog .el-dialog__body {
    height: 300px;
  }
}
.action-hover_download {
  cursor: pointer;
  /* 添加手型光标 */
}
</style>