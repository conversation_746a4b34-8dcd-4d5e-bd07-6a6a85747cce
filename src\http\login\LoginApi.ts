/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:19
 * @LastEditTime: 2023-09-15 13:36:35
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\login\LoginApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import UserLogin from 'model/login/UserLogin'
import UserLoginResult from 'model/login/UserLoginResult'
import ChangePassword from "model/login/ChangePassword";

export default class LoginApi {

  /**
   * 修改密码
   *
   */
  static changePassword(body: ChangePassword): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/login/changePassword`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 获取图片验证码（新）
   *
   */
  static captchaCode(uuid: string): Promise<void> {
    return ApiClient.server().get(`/v1/login/captchaCode/${uuid}`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 登录（新）
   *
   */
  static login(body: UserLogin): Promise<Response<UserLoginResult>> {
    return ApiClient.server().post(`/v1/login/login`, body, {}).then((res) => {
      return res.data
    })
  }
  /**
   * 登出
   * 登出
   *
   */
  static loginOut(): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/login/loginOut`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 白名单请求
   * 过期修改密码
   * 过期修改密码
   *
   */
  static expireChangePassword(body: ChangePassword): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/login/expireChangePassword`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * uni登录时刷新token
   * uni登录时刷新token
   *
   */
  static refreshToken(oldToken: string): Promise<Response<void>> {
    return ApiClient.server().get(`/v2/uni/token?cors-token=${oldToken}&action=change-token`, {}).then((res) => {
      return res.data
    })
  }

  /**
  * uni登录时获取用户信息
  * uni登录时获取用户信息
  *
  */
  static getLoginInfo(): Promise<Response<UserLoginResult>> {
    return ApiClient.server().get(`/v2/uni/getLoginInfo`, {}).then((res) => {
      return res.data
    })
  }

  /**
  * uni登录时 获取uni主页
  * uni登录时 获取uni主页
  *
  */
  static getUniHome(): Promise<Response<string>> {
    return ApiClient.server().get(`/v2/uni/getUniHome`, {}).then((res) => {
      return res.data
    })
  }
}
