<!--
 * @Author: haiding <EMAIL>
 * @Date: 2025-02-18 09:27:12
 * @LastEditors: 黎钰龙
 * @LastEditTime: 2025-05-08 17:00:01
 * @FilePath: \phoenix-web-ui\src\pages\analysis\vipMember\VipAnalysisDtl.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="vip-analysis-dtl">
    <!-- 详情 -->
    <div class="vip-dtl-header">
      <BreadCrume :panelArray="panelArray">
        <template slot="operate">
          <el-button v-if="hasOptionPermission('/数据/数据洞察/会员分析', '数据维护') && !isDtl" type="primary" class="fo-button" @click.stop="handleOpenDialog">
            {{ i18n('/公用/按钮/保存')}}
          </el-button>
          <el-button v-if="hasOptionPermission('/数据/数据洞察/会员分析', '数据导出') && isShowExportButton" class="fo-button" @click.stop="handleExport">
            {{ i18n('/数据/储值账单/导出') }}
          </el-button>
          <el-button class="fo-button" @click.stop="handleReturnBack">
            {{ i18n('/公用/券模板/返回列表') }}
          </el-button>
        </template>
      </BreadCrume>
    </div>
    <div class="vip-dtl-container" v-if="!isDtl">
      <div style="display: flex;justify-content: space-between;margin-bottom: 16px;">
        <span class="vip-dtl-title">{{ i18n('/公用/菜单/设置') }}</span>
      </div>
      <div class="setting-part">
        <el-form :model="formData" ref="form" :rules="rules">
          <div class="setting-item">
            <div class="item-header">
              <span class="item-title">{{ i18n('/数据/数据洞察/详情页/指标') }}</span>
              <div class="clear-part" @click="clearItem('target')" v-if="!isDtl">
                <i class="el-icon-delete"></i>
                <span class="item-clear">{{ i18n('/公用/导入/清空') }}</span>
              </div>
            </div>
            <el-form-item prop="metrics">
              <div class="item-content">

                <div class="tag-item" v-for="(item, index) in formData.metrics" :key="index">
                  <span style="margin-left: 12px;">{{ getMetricsName(item) }}</span>
                  <span @click="removeMetrics(index)" class="close-tag" v-if="!isDtl">
                    <img src="~assets/image/icons/ic_closethick.png" alt="">
                  </span>
                </div>

                <PortraitIndicatorSelect v-if="!isDtl" @submit="doSubmitMetrics" :isShowMemberCount="true" ref="portraitIndicatorMetrics"
                  type="target" where="vipAnalysis">
                </PortraitIndicatorSelect>
              </div>
            </el-form-item>
          </div>
          <div class="setting-item">
            <div class="item-header">
              <span class="item-title">{{ i18n('/数据/数据洞察/详情页/维度') }}</span>
              <div class="clear-part" @click="clearItem('dimension')" v-if="!isDtl">
                <i class="el-icon-delete"></i>
                <span class="item-clear">{{ i18n('/公用/导入/清空') }}</span>
              </div>
            </div>

            <el-form-item prop="dimensions" class="el-form-item">
              <div class="item-content">
                <div class="tag-item" v-for="(item, index) in formData.dimensions" :key="index">
                  <span style="margin-left: 12px;">{{ getDimensionName(item) }}</span>
                  <el-button v-if="isShowDataTong(item)" type="text" @click="addDataTong(item, index)">+ {{ i18n('/数据/数据洞察/数据分桶') }}</el-button>

                  <span @click="removeDimension(index)" class="close-tag" v-if="!isDtl">
                    <img src="~assets/image/icons/ic_closethick.png" alt="">
                  </span>
                </div>
                <PortraitIndicatorSelect v-if="!isDtl" @submit="doSubmitDimension" :isShowMemberCount="false" ref="portraitIndicatorDimension"
                  type="dimension" where="vipAnalysis">
                </PortraitIndicatorSelect>

              </div>
            </el-form-item>

          </div>
          <div class="setting-item">
            <div class="item-header">
              <span class="item-title">{{ i18n('/数据/数据洞察/详情页/筛选条件') }}</span>
              <div class="clear-part" @click="clearItem('condition')" v-if="!isDtl">
                <i class="el-icon-delete"></i>
                <span class="item-clear">{{ i18n('/公用/导入/清空') }}</span>
              </div>
            </div>

            <el-form-item prop="conditions">
              <div class="item-content">
                <div class="tag-item" v-for="(item, index) in formData.conditions" :key="index" closable :disable-transitions="false">
                  <span style="margin-left: 12px;">{{ getConditionName(item) }}</span>
                  <span @click="removeCondition(index)" class="close-tag" v-if="!isDtl">
                    <img src="~assets/image/icons/ic_closethick.png" alt="">
                  </span>
                </div>
                <PortraitIndicatorSelect v-if="!isDtl" @submit="doSubmitCondition" :isShowMemberCount="false" ref="portraitIndicatorCondition"
                  type="condition" where="vipAnalysis">
                </PortraitIndicatorSelect>
              </div>
            </el-form-item>
          </div>
          <div class="footer">
            <el-button v-if="hasOptionPermission('/数据/数据洞察/会员分析', '数据维护') && !isDtl" class="analysis-button" type="primary"
              @click.stop="handleSubmit">{{
                                i18n('/数据/数据洞察/详情页/生成分析')
                            }}</el-button>
            <!-- <el-button class="analysis-button" type="primary" @click.stop="handleSubmit">{{
                            i18n('/数据/数据洞察/详情页/生成分析')
                            }}</el-button> -->
          </div>
        </el-form>
      </div>

      <!-- 新建弹窗，填写名称 -->
      <el-dialog :title="i18n('/数据/数据洞察/列表页/新建会员分析')" :visible.sync="dialogVisible" width="640px" :before-close="handleClose">
        <el-form :model="labelForm" ref="labelForm" label-width="120px" :rules="dialogRules">
          <el-form-item :label="i18n('/数据/数据洞察/列表页/报表名称')" prop="labelName" required>
            <el-input v-model="labelForm.labelName" :placeholder="i18n('/公用/菜单/请输入')" :maxLength="40" show-word-limit type="text"
              style="width: 442px;"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false" class="fo-button">{{ i18n('/公用/按钮/取消') }}</el-button>
          <el-button type="primary" class="fo-button" @click="handleSave">{{ i18n('/公用/按钮/确定')
                    }}</el-button>
        </div>
      </el-dialog>

      <!--数值型，按默认区间，按自定义区间  -->
      <CustomRangeDialog ref="customRangeDialog" :rangeList="rangeList" :titleList="dimensionTitleList" :editable="editable" @submit="doSubmitRange">
        <!-- 时间型，按年，按月，按日 -->
      </CustomRangeDialog>
      <ConditionDateSelect ref="conditionDateSelect" @submit="doSubmitDateRange" :propName="propName"
        :propDisplayIntervalType="propDisplayIntervalType" :editable="editable"></ConditionDateSelect>
    </div>

    <div class="table-part" v-if="isShowSearchCondition">
      <div class="search-part">
        <el-form class="search-item" v-if="searchForm.searchCondition && searchForm.searchCondition.length" :model="searchForm" ref="searchForm"
          :validate-on-rule-change="false">
          <div v-for="(condition, index) in searchForm.searchCondition" :key="index" class="search-condition">
            <el-form-item :rules="itemRule(index)" :validate-event="false" :prop="`searchCondition[${index}]`">
              <span>{{ condition.memberPropName || condition.tagName }}</span>
              <div v-if="condition.fieldType === 'date'" class="search-condition-date">
                <el-select v-model="condition.relation" :placeholder="i18n('请选择')" size="small" @change="handleTimeChange(condition)"
                  style="width: 180px;">
                  <template v-for="option in selectDateOptions">
                    <el-option :label="option.label" :value="option.value" :key="option.value"></el-option>
                  </template>
                </el-select>
                <!-- 绝对时间 -->
                <div v-if="condition.relation === 'absolute'">
                  <el-select v-model="condition.operate" :placeholder="i18n('请选择')" size="small" style="width: 120px;">
                    <template v-for="option in selectOptions">
                      <el-option v-if="getTimeLabel(option)" :label="getTimeLabel(option)" :value="option" :key="option"></el-option>
                    </template>
                  </el-select>
                  <el-date-picker v-if="condition.operate === 'BETWEEN'" :end-placeholder="formatI18n('/公用/券模板', '结束日期')" format="yyyy-MM-dd HH:mm:ss"
                    :range-separator="i18n('/公用/活动/活动信息/至')" size="small" :start-placeholder="formatI18n('/公用/券模板', '开始日期')" type="datetimerange"
                    :default-time="['00:00:00', '23:59:59']" v-model="tempDateRange" @change="handleDateChange(condition)"
                    value-format="yyyy-MM-dd HH:mm:ss" key="dateRange">
                  </el-date-picker>
                  <el-date-picker v-else v-model="condition.beginDate" :placeholder="i18n('/会员/会员资料/请选择')" size="small"
                    style="width: 190px;margin-left:10px" type="datetime" key="singleDate">
                  </el-date-picker>

                </div>

                <!-- 相对当前时间点 -->
                <div v-if="condition.relation === 'relative_to_now'">
                  <span>{{ i18n('在') }}</span>
                  <el-select v-model="condition.feature" :placeholder="i18n('请选择')" size="small" style="width: 120px;"
                    @change="(val) => handleFeatureChange(condition, val)">
                    <el-option :label="i18n('/数据/数据洞察/未来')" value="未来"></el-option>
                    <el-option :label="i18n('/数据/数据洞察/过去')" value="过去"></el-option>
                  </el-select>
                  <el-tooltip :content="tooltipContent" placement="top">

                    <AutoFixInput :min="1" :max="99999" @change="handleTimeChange(condition)" v-model="condition.number" :fixed="0"
                      style="width: 98px;margin:0 4px" />
                  </el-tooltip>
                  <span style="margin-left: 2px;">{{ i18n('/数据/数据洞察/天') }}</span>
                  <el-select v-if="condition.feature === i18n('过去')" v-model="condition.range" :placeholder="i18n('请选择')" size="small"
                    style="width: 120px;" @change="handleTimeChange(condition)">
                    <el-option :label="i18n('/数据/数据洞察/之内')" value="之内"></el-option>
                    <el-option :label="i18n('/数据/数据洞察/之前')" value="之前"></el-option>
                  </el-select>
                  <el-select v-if="condition.feature === i18n('未来')" v-model="condition.range" :placeholder="i18n('请选择')" size="small"
                    style="width: 120px;" @change="handleTimeChange(condition)">
                    <el-option :label="i18n('/数据/数据洞察/之内')" value="之内"></el-option>
                    <el-option :label="i18n('/会员/智能建群/之后')" value="之后"></el-option>
                  </el-select>
                </div>
                <!-- 相对当前时间区间 -->
                <div v-if="condition.relation === 'relative_to_now_range'">
                  <span>{{ i18n('在') }}</span>

                  <el-select v-model="condition.feature" :placeholder="i18n('请选择')" size="small" @change="handleTimeChange(condition)"
                    style="width: 120px;">
                    <el-option :label="i18n('/数据/数据洞察/未来')" value="未来"></el-option>
                    <el-option :label="i18n('/数据/数据洞察/过去')" value="过去"></el-option>
                  </el-select>
                  <el-tooltip :content="tooltipRangeContent" placement="top">
                    <AutoFixInput :min="1" :max="Number(searchForm.searchCondition[index].endNumber) || 99999" @change="handleTimeChange(condition)"
                      v-model="condition.beginNumber" :fixed="0" style="width: 98px;margin:0 4px" />
                  </el-tooltip>
                  <span style="margin-left: 2px;">{{ i18n('/数据/数据洞察/天') }}</span>
                  <span style="margin-left: 5px;">{{ i18n('/数据/数据洞察/至') }}</span>
                  <span style="margin-left: 5px;">{{ condition.feature }}</span>
                  <AutoFixInput :min="Number(searchForm.searchCondition[index].beginNumber) || 1" :max="99999" @change="handleTimeChange(condition)"
                    v-model="condition.endNumber" :fixed="0" style="width: 98px;margin:0 4px" />
                  <span style="margin-left: 2px;">{{ i18n('/数据/数据洞察/天') }}</span>
                  <span style="margin-left: 5px;">{{ i18n('/数据/数据洞察/之内') }}</span>

                </div>

              </div>

              <div v-else-if="condition.fieldType === 'num'" class="search-condition-date">
                <el-select v-model="condition.operate" :placeholder="i18n('请选择')" size="small" style="width: 120px;">
                  <template v-for="option in selectOptions">
                    <el-option v-if="getNumLabel(option)" :label="getNumLabel(option)" :value="option" :key="option"></el-option>
                  </template>
                </el-select>
                <div v-if="condition.operate === 'BETWEEN'">
                  <AutoFixInput :min="0" :max="Number(searchForm.searchCondition[index].endValue) || 99999999.99" v-model="condition.beginValue"
                    :fixed="2" style="width: 98px;margin-right:4px" />
                  <span style="margin-left: 5px;">{{ i18n('与') }}</span>
                  <AutoFixInput :min="0 || Number(searchForm.searchCondition[index].beginValue)" :max="99999999.99" v-model="condition.endValue"
                    :fixed="2" style="width: 98px;margin-right:4px" />
                  <span style="margin-left: 5px;">{{ i18n('之间') }}</span>
                </div>
                <div v-else-if="condition.operate !== 'HAVE_VALUE' && condition.operate !== 'NOT_HAVE_VALUE'">
                  <AutoFixInput :min="0" :max="99999999.99" v-model="condition.value" :fixed="2" style="width: 98px;margin-right:4px" />
                </div>
              </div>

              <div v-else class="search-condition-date">
                <el-select v-model="condition.operate" :placeholder="i18n('请选择')" size="small" style="width: 120px;">
                  <template v-for="option in selectOptions">
                    <el-option v-if="getOtherLabel(option)" :label="getOtherLabel(option)" :value="option" :key="option"></el-option>
                  </template>
                </el-select>
                <template v-if="condition.operate !== 'HAVE_VALUE' && condition.operate !== 'NOT_HAVE_VALUE'">
                  <template v-if="condition.memberPropName === i18n('等级') || condition.memberPropName === '等级'">
                    <el-select v-model="condition.value" v-if="condition.operate !== 'REGEXP' && condition.operate !== 'NOT_REGEXP'" clearable
                      :placeholder="i18n('请选择')" size="small" style="width: 120px;">
                      <el-option :label="formatI18n('/公用/下拉框/提示', '全部')" :value="null">{{
                                                formatI18n('/公用/下拉框/提示', '全部') }}
                      </el-option>
                      <template v-for="option in memberLevel">
                        <el-option :label="option.name" :value="option.code" :key="option.code"></el-option>
                      </template>
                    </el-select>
                    <el-input v-else :placeholder="i18n('请输入')" v-model="condition.value" size="small" style="width: 80px;margin-left: 5px;" />
                  </template>

                  <template v-else-if="condition.memberPropName === i18n('性别') || condition.memberPropName === '性别'">
                    <el-select v-if="condition.operate !== 'REGEXP' && condition.operate !== 'NOT_REGEXP'" v-model="condition.value" clearable
                      :placeholder="i18n('请选择')" size="small" style="width: 120px;">
                      <el-option :label="i18n('/数据/数据洞察/不限')" :value="null">{{ i18n('不限')
                                                }}</el-option>
                      <el-option :label="i18n('/数据/数据洞察/男')" :value="i18n('男')">{{ i18n('男')
                                                }}</el-option>
                      <el-option :label="i18n('/数据/数据洞察/女')" :value="i18n('女')">{{ i18n('女')
                                                }}</el-option>
                      <el-option :label="i18n('/会员/会员资料/未知')" :value="i18n('未知')">{{ i18n('未知')
                                                }}</el-option>
                    </el-select>
                    <el-input v-else :placeholder="i18n('请输入')" v-model="condition.value" size="small" style="width: 80px;margin-left: 5px;" />
                  </template>

                  <template
                    v-else-if="(condition.memberPropName === i18n('归属门店') || condition.memberPropName === '归属门店') || (condition.memberPropName === i18n('归属门店名称') || condition.memberPropName === '归属门店名称') || (condition.memberPropName === i18n('归属门店代码') || condition.memberPropName === '归属门店代码')">
                    <el-select v-if="condition.operate !== 'REGEXP' && condition.operate !== 'NOT_REGEXP'" clearable filterable remote
                      :remote-method="doRemoteMethod" :placeholder="formatI18n('/会员/会员资料', '请输入归属门店')" v-model="condition.value">
                      <el-option :label="formatI18n('/公用/下拉框/提示', '全部')" :value="null">{{
                                                formatI18n('/公用/下拉框/提示', '全部') }}
                      </el-option>
                      <el-option v-if="condition.memberPropName === i18n('归属门店名称') || condition.memberPropName === '归属门店名称'" :label="item.org.name"
                        :value="item.org.name" v-for="(item, index) in stores" :key="index">
                        {{ item.org.name }}
                      </el-option>
                      <el-option v-if="condition.memberPropName === i18n('归属门店代码') || condition.memberPropName === '归属门店代码'" :label="item.org.id"
                        :value="item.org.id" v-for="(item, index) in stores" :key="index">
                        {{ item.org.id }}
                      </el-option>
                    </el-select>
                    <el-input v-else :placeholder="i18n('请输入')" v-model="condition.value" size="small" style="width: 80px;margin-left: 5px;" />
                  </template>

                  <template v-else-if="condition.memberPropName === i18n('会员状态') || condition.memberPropName === '会员状态'">
                    <el-select v-if="condition.operate !== 'REGEXP' && condition.operate !== 'NOT_REGEXP'"
                      :placeholder="formatI18n('/公用/下拉框/提示', '全部')" v-model="condition.value">
                      <el-option :label="formatI18n('/公用/下拉框/提示', '全部')" :value="null">{{
                                                formatI18n('/公用/下拉框/提示', '全部') }}
                      </el-option>
                      <el-option :label="formatI18n('/会员/会员资料', '使用中')" value="Using">{{
                                                formatI18n('/会员/会员资料', '使用中') }}
                      </el-option>
                      <el-option :label="formatI18n('/会员/会员资料', '已冻结')" value="Blocked">
                        {{ formatI18n('/会员/会员资料', '已冻结') }}
                      </el-option>
                      <el-option :label="formatI18n('/会员/会员资料', '未激活')" value="Unactivated">
                        {{ formatI18n('/会员/会员资料', '未激活') }}
                      </el-option>
                    </el-select>
                    <el-input v-else :placeholder="i18n('请输入')" v-model="condition.value" size="small" style="width: 80px;margin-left: 5px;" />
                  </template>
                  <template
                    v-else-if="(condition.memberPropName === i18n('注册渠道') || condition.memberPropName === '注册渠道') || (condition.memberPropName === i18n('注册渠道类型') || condition.memberPropName === '注册渠道类型')">
                    <el-select v-if="condition.operate !== 'REGEXP' && condition.operate !== 'NOT_REGEXP'"
                      :placeholder="formatI18n('/公用/下拉框/提示', '全部')" v-model="condition.value">
                      <el-option :label="formatI18n('/公用/下拉框/提示', '全部')" :value="null">{{
                                                formatI18n('/公用/下拉框/提示', '全部') }}
                      </el-option>
                      <el-option :label="item.name" :value="item.channel ? item.channel.typeId : null" v-for="(item, index) in channels" :key="index"
                        v-if="channels.length > 0">
                      </el-option>
                    </el-select>
                    <el-input v-else :placeholder="i18n('请输入')" v-model="condition.value" size="small" style="width: 80px;margin-left: 5px;" />
                  </template>
                  <el-input v-else :placeholder="i18n('请输入')" v-model="condition.value" size="small" style="width: 80px;margin-left: 5px;" />
                </template>

              </div>
            </el-form-item>

          </div>
        </el-form>

        <div class="search-button-part">
          <el-button class="search-button" type="primary" @click="handleSearch(null)">{{ i18n('查询')}}</el-button>
          <el-button class="search-button" @click="handleResetSearchCondition">{{ i18n('重置') }}</el-button>
        </div>
      </div>

      <div class="table-container">
        <el-table v-if="tableData.length > 0" ref="storeTable" stripe style="width: 100%" :data="tableData">
          <el-table-column v-for="(column, index) in columns" :key="index" :prop="column.prop" :label="column.label" />
        </el-table>
        <!-- <el-table ref="storeTable" stripe style="width: 100%" :data="processedData"
                    v-if="processedData.length > 0">
                   
                    <el-table-column v-for="(dimension, index) in dimensionHeaders" :key="index + 'd'" :prop="dimension.prop"
                        :label="getHeaderLabel(dimension)" />

                    
                    <el-table-column v-for="(metric, index) in metricHeaders" :key="index + 'm'" :prop="metric.prop"
                        :label="getHeaderLabel(metric)" />
                </el-table> -->
        <div v-else class="empty">
          <img src="~assets/image/auth/ct_empty.png" alt="">
          <div>
            {{ i18n('/会员/会员资料/暂无数据') }}
          </div>
        </div>

        <div style="height: 50px;margin-top: 16px;margin-right: 16px;">
          <!--分页栏-->
          <el-pagination :current-page="page.page + 1" :page-size="page.pageSize" :page-sizes="[10, 20, 30, 40]"
            @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" :total="page.total" background
            layout="total, prev, pager, next, sizes,  jumper">
          </el-pagination>
        </div>
      </div>

      <!-- <div v-else class="table-container-empty">
                <div class="no-data">{{ i18n('/详情页/表格/暂无数据') }}</div>
                </div> -->
    </div>
  </div>
</template>
<script lang="ts" src="./VipAnalysisDtl.ts">
</script>

<style lang="scss">
.vip-analysis-dtl {
  // height: 99%;

  .vip-analysis-header {
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: space-between;
  }

  .vip-dtl-container {
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    width: 100%;
    padding: 24px 32px;
    border-radius: 8px;

    .vip-dtl-title {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #222222;
      line-height: 20px;
      margin-bottom: 16px;
    }

    .setting-part {
      display: flex;
      flex-direction: column;

      .setting-item {
        padding: 0;
        margin-bottom: 16px;
        width: 100%;
        border-radius: 4px;
        border: 1px solid #d7dfeb;

        .item-header {
          width: 100%;
          height: 44px;
          background: #f9fafc;
          border-radius: 4px 4px 0px 0px;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .item-title {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 14px;
            color: #222222;
            line-height: 20px;
            margin: 12px 20px;
          }

          .clear-part {
            display: flex;
            align-items: center;
            gap: 5px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;

            &:hover {
              cursor: pointer;
              color: #007eff;
            }

            .el-icon-delete:hover {
              cursor: pointer;
              color: #007eff;
            }

            // .el-icon-delete {
            //     width: 10px;
            //     height: 11px;
            // }

            .item-clear {
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              font-size: 12px;
              color: #969799;
              line-height: 17px;
              margin-right: 20px;
            }
          }
        }

        .el-form-item {
          padding-top: 12px;
          padding-left: 12px;
        }

        .item-content {
          width: 100%;

          display: flex;
          flex-wrap: wrap;
          gap: 12px;
          align-items: center;

          .tag-item {
            height: 28px;
            background: #f7f9fc;
            border-radius: 16px;
            border: 1px solid #d7dfeb;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #36445a;
            line-height: 18px;
            // padding-top: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            span {
              margin-right: 12px;
            }

            .close-tag {
              width: 16px;
              height: 16px;
              padding-left: 4px;
            }

            .close-tag:hover {
              background-color: #d7dfeb;
              border-radius: 50%;
            }
          }

          .indicator-part {
            padding-left: 12px;
            padding-right: 12px;
            border: 1px solid #007eff;
            border-radius: 2px;
          }

          .choice-part {
            overflow: scroll;
            background: #ffffff;
            position: absolute;
            z-index: 999;
            top: 32px;
            width: 340px;
            height: 400px;
            background: #ffffff;
            border-radius: 2px;
            border: 1px solid #d7dfeb;
            padding: 6px 12px;

            .choice-tag-list {
              margin-top: 16px;
              display: flex;
              gap: 12px;
            }

            .block {
              display: block;
              margin-top: 10px;
              width: 100%;

              .choice-title {
                color: #a8a8b6;
                width: 48px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 12px;
                color: #a8a8b6;
                line-height: 17px;
              }

              .line {
                width: 231px;
                height: 1px;
                border: 1px solid #f1f5f9;
                margin-left: 6px;
              }

              .block-list {
                display: flex;
                flex-direction: column;
                gap: 12px;
                padding-top: 12px;
              }
            }
          }
        }
      }

      .footer {
        display: flex;
        justify-content: center;

        .analysis-button {
          height: 36px;
          background: #007eff;
          border-radius: 4px;
          padding: 8px 12px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #ffffff;
          line-height: 20px;
        }
      }
    }
  }

  .table-part {
    background: #ffffff;
    height: 94%;
    width: 100%;
    margin-top: 20px;
    border-radius: 8px;
    display: flex;
    flex-direction: column;

    .search-part {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 24px;

      .search-item {
        margin-left: 12px;
        display: flex;
        align-items: center;
        gap: 16px;
        flex-wrap: wrap;

        .search-condition {
          display: flex;
          gap: 4px;
          align-items: center;

          .search-condition-date {
            display: flex;
            // background-color: #969799;
            gap: 4px;
          }
        }
      }

      .search-button-part {
        gap: 12px;

        .search-button {
          width: 76px;
          height: 32px;
          border-radius: 2px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 13px;
          line-height: 20px;
        }
      }
    }

    .table-container {
      width: 95%;
      height: 100%;
      margin: 16px 32px;
    }

    .empty {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
    }
  }

  .fo-button {
    height: 36px;
    padding: 8px 12px;
    font-size: 14px;
    line-height: 20px;
  }
}
</style>