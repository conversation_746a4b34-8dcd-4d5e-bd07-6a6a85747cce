import { Vue, Component, Prop, Watch } from "vue-property-decorator";
import emitter from "util/emitter";
import { FormMode } from "model/local/FormMode";
import FormDefUtil from "util/FormDefUtil";
import ElasticLayer from "../../elastic-layer-activity/ElasticLayerActivity.vue";
import I18nPage from 'common/I18nDecorator';

@Component({
  name: "SelectActivity",
  mixins: [emitter],
  components: { ElasticLayer },
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/公用/菜单',
    '/页面/页面管理',
    '/公用/过滤器',
  ],
  auto: true
})
export default class AngleSet extends Vue {
  @Prop({ type: Boolean, default: false })
  readonly: boolean; //
  @Prop({ type: String, default: "AngleSet" })
  validateName: string; // 组件校验名称，editPage通过该名称进行组件校验及校验信息清除
  @Prop({ type: Object })
  value: any; // 数据模型
  @Prop({ type: String, default: "页面标题名称" })
  label: string; // label名
  @Prop({ type: String })
  prefixDescribe: string; // 前置描述
  @Prop({ type: String })
  suffixDescribe: string; // 后置描述

  @Prop({ type: Boolean, default: true })
  required: boolean; // 是否必填
  @Prop({ type: String })
  relativeValidateName: string; // 联动校验名称
  @Prop()
  formKey: any;
  @Prop({
    type: Object,
    default: () => {
      return {
        isShowColor: true,
      };
    },
  })
  config: any; // 配置项
  FormModeType = FormMode;
  $refs: any;
  labelPosition: string = "top";
  imageList: any[] = [];
  options = [
    { caption: this.i18n("全部活动"), key: "all" },
    { caption: this.i18n("指定活动"), key: "part" },
  ];

  rules = {
    content: [
      {
        trigger: "change",
        validator: (rule: any, value: any, callback: any) => {
          if (this.value.propActivityRange == "part" && this.value.propActivityIds.length == 0) {
            callback(new Error(this.i18n("请选择指定活动")));
          } else {
            callback();
          }
        },
      },
    ],
  };
  oldVal: string = "";
  @Watch("value.propActivityRange")
  onActivityRange() {
    this.validate(() => {});
  }
  get formMode() {
    if (this.validateName === "mainTitle") {
      return FormDefUtil.getFormMode(this.readonly, this.validateName, this.formKey);
    } else {
      return FormDefUtil.getFormMode(this.readonly, this.validateName + "mainTitle", this.formKey);
    }
  }
  get signatureResult() {
    return this.$store.state.credential;
  }
  handleChange() {
    if (!this.value.propActivityRange) this.value.propActivityRange = "all";
    if (this.value.propActivityRange == 'all') {
      this.value.propActivityIds = []
    }
    this.$emit("input", this.value);
    this.$emit("change", this.value);
  }

  handleSubmit(ids: string[]) {
    this.value.propActivityIds = ids || []
    this.handleChange()
  }
  goUp() {
    this.$refs.childRef.open(JSON.parse(JSON.stringify(this.value.propActivityIds)))
  }

  validate(callback: any) {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.validate(callback);
    }
  }

  clearValidate() {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.clearValidate();
    }
  }
}
