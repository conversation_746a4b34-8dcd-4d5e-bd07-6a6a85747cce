/*
 * @Author: haiding <EMAIL>
 * @Date: 2025-02-19 10:03:52
 * @LastEditors: haiding <EMAIL>
 * @LastEditTime: 2025-03-06 14:16:37
 * @FilePath: \new-kequn\src\pages\invest\page-manage\cmp\page-props-components\content-color\ContentColor.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Vue, Component, Prop, Watch } from "vue-property-decorator";
import emitter from "util/emitter";
import { FormMode } from "model/local/FormMode";
import I18nPage from "common/I18nDecorator";
@Component({
  name: "EquityCardContentColor",
  mixins: [emitter],
  components: {},
})
@I18nPage({
  prefix: ["/公用/券模板", "/页面/页面管理", "/页面/导航设置"],
  auto: true,
})
export default class EquityCardContentColor extends Vue {
  @Prop({ type: Boolean, default: false })
  readonly: boolean; //
  @Prop({ type: String, default: "EquityCardContentColor" })
  validateName: string; // 组件校验名称，editPage通过该名称进行组件校验及校验信息清除
  @Prop({ type: Object })
  value: any; // 数据模型
  @Prop({ type: String, default: "导航设置" })
  label: string; // label名
  @Prop({ type: String })
  prefixDescribe: string; // 前置描述
  @Prop({ type: String })
  suffixDescribe: string; // 后置描述

  @Prop({ type: Boolean, default: true })
  required: boolean; // 是否必填
  @Prop({ type: String })
  relativeValidateName: string; // 联动校验名称
  @Prop()
  formKey: any;
  @Prop()
  activityWidgets: any;

  FormModeType = FormMode;
  $refs: any;
  labelPosition: string = "left";
  optionsOut: string[] = [this.i18n("活动页面")];

  @Watch("value.propSelectColorStyle")
  onDataChange(value: string) {
    if (value === "default") {
      this.value.styContentBgColor = "#FDD3A0";
      this.value.styContentTextColor = "#4F4E5D";
      this.value.styContentButtonColor = "#5E5E6C";
    }
  }

  handleValidate() {}

  get credential() {
    return this.$store.state.credential;
  }

  get options() {
    return [
      {
        id: "styContentBgColor",
        label: this.i18n("/会员/会员资料/背景"),
      },
      {
        id: "styContentTextColor",
        label: this.i18n("文本"),
      },
      {
        id: "styContentButtonColor",
        label: this.i18n("按钮"),
      },
    ];
  }

  handleChange() {
    // 设置默认值
    if (!this.value.styContentBgColor) {
      this.value.styContentBgColor = "#FDD3A0";
    }

    if (!this.value.styContentTextColor) {
      this.value.styContentTextColor = "#4F4E5D";
    }

    if (!this.value.styContentButtonColor) {
      this.value.styContentButtonColor = "#5E5E6C";
    }

    this.$emit("input", this.value);
    this.$emit("change", this.value);
    this.$nextTick(() => {
      this.validate(() => {});
    });
  }

  mounted() {}

  validate(callback: any) {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.validate(callback);
    }
  }

  clearValidate() {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.clearValidate();
    }
  }
}
