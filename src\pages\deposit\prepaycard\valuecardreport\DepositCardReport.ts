import {Component, Vue} from 'vue-property-decorator'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import SalesCardsReport from './salescards/SalesCardsReport';
import ConsumeReport from './consume/ConsumeReport';
import TransferReport from './transfer/TransferReport';
import RefundReport from './refund/RefundReport';
import I18nPage from "common/I18nDecorator";
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import RechargeableCardReportApi from "http/prepay/report/card/RechargeableCardReportApi";
import PointsReportExportConfirm from "pages/deposit/prepaycard/cmp/export/exchangeablecard/PointsReportExportConfirm.vue";
import DownloadCenterDialog from "pages/main/dowmload_center_dialog/DownloadCenterDialog";
import GiftCardFilter from 'model/prepay/report/card/GiftCardFilter'
import RechargeCardsReport from 'pages/deposit/prepaycard/valuecardreport/recharge/RechargeCardsReport'
import RefundCardReport from "pages/deposit/prepaycard/valuecardreport/refundcard/RefundCardReport";
import RSChannelManagementFilter from "model/common/RSChannelManagementFilter";
import ChannelManagementApi from "http/channelmanagement/ChannelManagementApi";
import { SessionStorage } from "mgr/BrowserMgr";
import GiftCardReportApi from "http/prepay/report/card/GiftCardReportApi";


@Component({
  name: 'DepositCardReport',
  components: {
    SubHeader,
    SalesCardsReport,
    ComsumeReport: ConsumeReport,
    RefundCardReport,
    TransferReport,
    RefundReport,
    BreadCrume,
    PointsReportExportConfirm,
    DownloadCenterDialog,
    RechargeCardsReport
  }
})
@I18nPage({
  prefix: [
    '/储值/预付卡/实体礼品卡报表',
    '/储值/预付卡/电子礼品卡报表',
    '/公用/提示',
    '/公用/查询条件',
    '/公用/按钮',
    '/储值/预付卡/储值卡报表',
  ],
  auto: false
})
export default class DepositCardReport extends Vue {
  activeName: string = '售卡流水'
  panelArray: any = []
  exportDialogShow = false
  fileDialogVisible = false
  showTip = false
  created() {
    this.panelArray = [
      {
        name: this.formatI18n('/公用/菜单/储值卡报表'),
        url: ''
      }
    ]
    this.getChannels();
    // PHX-14618需求：会员资料 -> 会员资产 -> 可用预付卡 -> 明细按钮
    if (this.$route.query.from == "member-asset") {
      this.activeName = "消费流水";
    }
  }

  private getChannels() {
    let param: RSChannelManagementFilter = new RSChannelManagementFilter();
    ChannelManagementApi.query(param)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          SessionStorage.setItem("channels", resp.data);
        }
      })
      .catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
  }

  get getExportDialogShow() {
    return this.exportDialogShow
  }

  doExportDialogClose() {
    this.exportDialogShow = false
  }

  doBatchExport() {
    this.exportDialogShow = true
  }

  doDownloadDialogClose() {
    this.showTip = false
    this.fileDialogVisible = false
  }

  doExportSubmit(type: string, filter: GiftCardFilter) {
    if (!type || !filter) {
      return
    }
    if (type === 'SALES_HST') {
      RechargeableCardReportApi.exportSalesHst(filter).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.exportAfter()
        }
      }).catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })

    } else if (type === 'CONSUME_HST') {
      RechargeableCardReportApi.exportConsumeHst(filter).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.exportAfter()
        }
      }).catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })

    } else if (type === 'REFUND_HST') {
      RechargeableCardReportApi.exportRefundHst(filter).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.exportAfter()
        }
      }).catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    } else if (type === 'RECHARGE_HST') {
      RechargeableCardReportApi.exportRechargeHst(filter).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.exportAfter()
        }
      }).catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    } else if (type === 'TRANSFER_HST') {
      RechargeableCardReportApi.exportTransferHst(filter).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.exportAfter()
        }
      }).catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    } else if (type === "REFUND_CARD_HST") {
      RechargeableCardReportApi.exportRefundCard(filter as GiftCardFilter)
        .then((resp: any) => {
          if (resp && resp.code === 2000) {
            this.exportAfter();
          }
        })
        .catch((error: any) => {
          if (error && error.message) {
            this.$message.error(error.message);
          }
        });
    }
  }

  exportAfter() {
    this.showTip = true
    this.fileDialogVisible = true
  }
}
