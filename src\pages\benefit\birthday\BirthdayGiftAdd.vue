<template>
  <div class="birthday-gift-add">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button v-if="hasOptionPermission('/营销/营销/节日有礼/生日送礼', '规则维护')" @click="doModify" type="primary">
          {{ formatI18n('/公用/按钮/保存并启用') }}
        </el-button>
        <el-button @click="doBack" type="primary">{{ formatI18n('/公用/按钮', '取消') }}</el-button>
      </template>
    </BreadCrume>
    <div style="overflow: auto;height: 95%">
      <div class="content">
        <div class="cus-class">
          <FormItem :label="formatI18n('/权益/生日权益初始化/生日权益初始化','权益生效时间')">
            <!-- <div style="padding-top: 3px">
              <span style="color: red;position: relative;top: 0px;left:-112px;">*</span>
              {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/新建/每月') }}
              <AutoFixInput :min="1" :max="31" :fixed="0" style="width: 78px" v-model="giftRuleData.dayNo"/>
              {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/新建/号给当月过生日的会员送礼包') }}
            </div> -->
            <div style="padding-top: 3px">
              {{ formatI18n('/会员/会员资料/生日') }}
              <el-select v-model="giftRuleData.giftDay.type" style="width: 100px" class="space-between" @change="typeChange">
                <el-option value="MONTH_DAY" :label="formatI18n('/权益/生日权益初始化/生日权益初始化/当月')"></el-option>
                <el-option value="BIRTHDAY" :label="formatI18n('/权益/生日权益初始化/生日权益初始化/当天')"></el-option>
                <el-option value="BEFORE_DAY" :label="formatI18n('/权益/生日权益初始化/生日权益初始化/前')"></el-option>
              </el-select>
              <AutoFixInput class="space-between" v-if="giftRuleData.giftDay.type !== 'BIRTHDAY'" :min="1" :max="31" :fixed="0" style="width: 78px" v-model="giftRuleData.giftDay.dayNo"/>
              <span v-if="giftRuleData.giftDay.type === 'BEFORE_DAY'">&nbsp; {{formatI18n('/权益/生日权益初始化/生日权益初始化/天的')}} </span>
              <span v-if="giftRuleData.giftDay.type === 'MONTH_DAY'">&nbsp; {{formatI18n('/权益/生日权益初始化/生日权益初始化/号')}}</span>
              <el-time-picker
                class="space-between"
                style="width:200px"
                v-model="giftRuleData.giftDay.time"
                :picker-options="{
                  format: 'HH:mm'
                }"
                format="HH:mm"
                value-format="HH:mm"
                @change="pickerChange"
                placeholder="任意时间点">
              </el-time-picker>
              <span class="tip" v-if="giftRuleData.giftDay.type === 'BEFORE_DAY'">{{formatI18n('/权益/生日权益初始化/生日权益初始化/请填写1-31天之间的整数')}}</span>
              <span class="tip" v-if="giftRuleData.giftDay.type === 'MONTH_DAY'">{{formatI18n('/权益/生日权益初始化/生日权益初始化/设置的时间如果当月没有，则是月末发送赠礼')}}</span>
            </div>
          </FormItem>
          <FormItem :label="formatI18n('/权益/生日权益初始化/生日权益初始化/礼包设置')" style="position: relative;">
            <span style="color:red;position:absolute;" :class="[isActive ? 'active' : 'unActive']">*</span>
            <el-radio-group style="padding-top: 12px" @change="doGiftRadioChange" v-model="giftBagType">
              <el-radio label="same">{{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/新建/礼包设置/不同等级会员送相同礼包') }}</el-radio>
              <el-radio label="different">{{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/新建/礼包设置/不同等级会员送不同礼包') }}</el-radio>
            </el-radio-group>
            <div v-if="giftBagType=='same'"
                 style="border:1px solid #ddd;padding:2em;width: 70%;margin-top:10px">
              <div class="item">
                <el-checkbox @change="resetPointsVal(sameGiftData)" v-model="sameGiftData.sendScore">
                  {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/新建/赠送积分') }}&nbsp;&nbsp;
                </el-checkbox>
                <AutoFixInput style="width: 80px" :min="1" :max="999999999" :fixed="0" v-model="sameGiftData.scoreCount"
                              :disabled="!sameGiftData.sendScore"/>
                {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/新建/赠送积分/个') }}
              </div>
              <div class="item">
                <el-checkbox v-model="sameGiftData.sendCoupon" @change="resetCouponData(null)">
                  {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/新建/赠送券：') }}
                </el-checkbox>
                <el-button @click="doAddCoupon(null)" type="text"
                           v-if="sameGiftData.sendCoupon && sameGiftData.couponData.length === 0">
                  +{{ formatI18n('/会员/等级/等级管理/未初始化状态/点击立即开始免费等级初始化/输入所有必填项点击下一步/然后输入所有必填项点击下一步/等级月礼下的表格', '添加券') }}
                </el-button>
                <ActiveAddCoupon ref="addCoupon" v-model="sameGiftData.couponData"></ActiveAddCoupon>
              </div>
            </div>
            <div v-else-if="giftBagType==='different'"
                 style="border:1px solid #ddd;width: 70%;margin-top:10px" class="diff-bag">
              <el-table :data="diffGiftData" stripe>
                <el-table-column :label="formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/详情/礼包设置/表格','会员等级')" prop="name"
                                 width="180">
                </el-table-column>
                <el-table-column :label="formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/详情/礼包设置/表格','礼包设置')">
                  <template slot-scope="scope">
                    <div class="item">
                      <el-checkbox @change="resetPointsVal(scope.row)" v-model="scope.row.sendScore">
                        {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/新建/赠送积分') }}&nbsp;&nbsp;
                      </el-checkbox>
                      <AutoFixInput style="width: 80px" :min="1" :max="999999999" :fixed="0"
                                    v-model="scope.row.scoreCount"
                                    :disabled="!scope.row.sendScore"/>
                      {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/新建/赠送积分/个') }}
                    </div>
                    <div class="item">
                      <el-checkbox v-model="scope.row.sendCoupon" @change="resetCouponData(scope.row)">
                        {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/新建/赠送券：') }}
                      </el-checkbox>
                      <el-button @click="doAddCoupon(scope.row.code)" type="text"
                                 v-if="scope.row.sendCoupon && scope.row.couponData.length === 0">
                        +{{
                        formatI18n('/会员/等级/等级管理/未初始化状态/点击立即开始免费等级初始化/输入所有必填项点击下一步/然后输入所有必填项点击下一步/等级月礼下的表格', '添加券')
                        }}
                      </el-button>
                      <ActiveAddCoupon
                              v-model="scope.row.couponData"
                              :dialogShow="scope.row.dialogShow"
                              :ref="scope.row.code+'addCoupon'"
                      ></ActiveAddCoupon>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </FormItem>
        </div>
      </div>
    </div>

  </div>
</template>

<script src="./BirthdayGiftAdd.ts">
</script>

<style lang="scss">
.birthday-gift-add {
  background-color: white;
  overflow: hidden;
  height: 100%;
  width: 100%;

  .subTitle {
    font-size: 16px;
    padding-top: 20px;
    padding-left: 30px;
  }

  .cus-class {
    padding: 50px;

    .qf-form-item .qf-form-label {
      width: 200px !important;
    }

    .qf-form-item .qf-form-content {
      margin-left: 200px !important;
    }
  }

  .el-switch__core {
    background-color: grey;
  }

  .el-checkbox {
    margin-right: 1px;
  }

  .el-switch.is-checked .el-switch__core {
    background-color: #33cc00
  }

  .el-checkbox__label {
    padding-left: 5px;
  }

  .active {
    top: 9px;
    left: 116px;
  }

  .unActive {
    top: 10px;
    left: 8px;
  }

  .diff-bag {
    .coupon-name {
      top: 8px !important;
    }

    .add-coupon-btn {
      top: 0px !important;
    }
  }

  .coupon-name {
    top: 4px !important;
  }

  .add-coupon-btn {
    top: 0px !important;
  }
  .space-between {
    margin-left: 10px;
  }
  .tip {
    padding-left: 10px;
    color: #888888;
  }
}

</style>