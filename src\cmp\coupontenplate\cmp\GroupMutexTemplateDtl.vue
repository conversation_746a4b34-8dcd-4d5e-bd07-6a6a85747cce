<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:02
 * @LastEditTime: 2024-05-09 15:03:12
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\coupontenplate\cmp\GroupMutexTemplateDtl.vue
 * 记得注释
-->
<template>
  <div style="line-height: 36px">
    <FormItem :label="formatI18n('/权益/券/券模板/新建券模板/叠加用券:')" v-if="enableActivityExclusion">
      <div v-if="coupon.superpositionLevel === 'TRADE'">
        {{formatI18n('/公用/券模板/订单级别')}}
        <span v-if="coupon.superpositionType === 'REUSEABLE'">
          {{formatI18n('/公用/券模板/可叠加')}}
          <span v-for="(item, index) in coupon.superpositionTypeValue" :key="index">
            <span v-if="item === 'CURRENT_COUPON'"> {{formatI18n('/公用/券模板/可与当前券模板叠加')}}</span>&nbsp;&nbsp;
            <span v-if="item === 'OTHER_COUPON'"> {{formatI18n('/公用/券模板/可与其他券模板叠加')}}</span>&nbsp;&nbsp;
          </span>
        </span>
        <span v-if="coupon.superpositionType === 'NONREUSEABLE'">
          {{formatI18n('/公用/券模板/不可叠加')}}
        </span>
      </div>
      <div v-else-if="coupon.superpositionLevel === 'GOODS'">
        {{formatI18n('/公用/券模板/单品级别')}}
        <span v-if="coupon.superpositionType === 'REUSEABLE'">
          {{formatI18n('/公用/券模板/可叠加')}}
        </span>
        <span v-if="coupon.superpositionType === 'NONREUSEABLE'">
          {{formatI18n('/公用/券模板/不可叠加')}}
          <span v-if="coupon.nonSuperpositionTypeValue === 'CURRENT_COUPON'"> {{formatI18n('/公用/券模板/不可与当前券模板叠加')}}</span>
          <span v-if="coupon.nonSuperpositionTypeValue === 'OTHER_COUPON'"> {{formatI18n('/公用/券模板/不可与其他券模板叠加')}}</span>
          <span v-if="coupon.nonSuperpositionTypeValue === 'ALL_COUPON'"> {{formatI18n('/公用/券模板/不可与当前券模板叠加')}}
            {{formatI18n('/公用/券模板/不可与其他券模板叠加')}}</span>
        </span>
      </div>
      <div class="group-mutex-template-dtl"
        v-if="coupon.superpositionLevel === 'GOODS' && coupon.superpositionType === 'NONREUSEABLE' && (coupon.nonSuperpositionTypeValue === 'OTHER_COUPON' || coupon.nonSuperpositionTypeValue === 'ALL_COUPON')">
        <el-table :data="coupon.groupMutexTemplates" stripe>
          <el-table-column prop="templateNumber" :label="formatI18n('/权益/券/券模板/新建券模板/叠加用券/券模板号')" width="360">
            <template slot-scope="scope">
              {{ scope.row.id }}
            </template>
          </el-table-column>
          <el-table-column prop="templateNumber" :label="formatI18n('/权益/券/券模板/新建券模板/叠加用券/券名称')" width="360">
            <template slot-scope="scope">
              <div :title="scope.row.name" class="coupon-name" @click="doEditMutexCouponTemplate(scope.row.id)">
                {{ scope.row.name }}
              </div>
            </template>
          </el-table-column>
        </el-table>
        <SelectStoreActiveDtlDialog :baseSettingFlag="true" :child="child" ref="mutexCouponTemplateDialog" @dialogClose="doDialogClose"
          :dialogShow="mutexCouponTemplateDialogShow">
        </SelectStoreActiveDtlDialog>
      </div>
    </FormItem>
  </div>
</template>

<script lang="ts" src="./GroupMutexTemplateDtl.ts">
</script>

<style lang="scss">
.group-mutex-template-dtl {
  // height: 36px;
  line-height: 36px;

  .coupon-name {
    display: inline-block;
    color: #20a0ff;
    cursor: pointer;
    width: 115px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    position: relative;
  }

  .rule-table {
    width: 50%;

    .rule-table-header {
      margin-top: 10px;
      padding: 10px;
      background-color: #e6e6e6;
      border: 1px solid #e6e6e6;
    }

    .rule-table-line {
      padding: 10px;
      border: 1px solid #e6e6e6;
      border-top: 0;
    }
  }
}
</style>
<style lang='scss' scoped>
::v-deep .el-table th {
  font-size: 13px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #242633;
}
</style>