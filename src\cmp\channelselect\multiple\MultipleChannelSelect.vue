<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:02
 * @LastEditTime: 2023-09-08 11:19:06
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\channelselect\multiple\MultipleChannelSelect.vue
 * 记得注释
-->
<template>
  <div class="multiple-channel-select">
    <el-form class="promotion-center-store" ref="multipleChannelSelect" :model="ruleForm" :rules="rules">
      <el-form-item :label="formLabel" required>
        <el-radio-group @change="onTypeChange" v-model="ruleForm.selectType" :disabled="disabled">
          <el-row class="el-row-35" v-if="showAll">
            <el-radio label="ALL">
              {{ formatI18n('/公用/券模板/用券渠道', '全部渠道') }}
            </el-radio>
          </el-row>
          <el-form-item prop="partSelected">
            <el-row class="el-row-60">
              <el-radio label="PART">
                <span>{{ formatI18n('/公用/券模板/用券渠道', '指定渠道适用') }}</span>
                <div class="el-div">
                  <el-select class="el-select" :disabled="ruleForm.selectType !== 'PART' || disabled" multiple @change="onChange"
                    v-model="ruleForm.partSelected" :placeholder="formatI18n('/公用/券模板/用券渠道', '请至少选择一个渠道')" :popper-append-to-body="false">
                    <el-option v-for="(item, index) in channels" :key="'channel' + index" :label="item.name" :value="getKey(item.channel)">
                      {{ item.name }}
                    </el-option>
                  </el-select>
                </div>
              </el-radio>
            </el-row>
          </el-form-item>
          <el-form-item prop="excludeSelected" v-if="showExclude">
            <el-row class="el-row-60" style="margin-top: 25px">
              <el-radio label="EXCLUDE" style="display: block">
                <span>{{ formatI18n('/公用/券模板/用券渠道', '指定渠道不适用') }}</span>
                <div class="el-div">
                  <el-select class="el-select" :disabled="ruleForm.selectType !== 'EXCLUDE' || disabled" multiple @change="onChange"
                    v-model="ruleForm.excludeSelected" :placeholder="formatI18n('/公用/券模板/用券渠道', '请至少选择一个渠道')" :popper-append-to-body="false">
                    <el-option v-for="(item, index) in channels" :key="`channel${index}`" :label="item.name" :value="getKey(item.channel)">
                      {{ item.name }}
                    </el-option>
                  </el-select>
                </div>
              </el-radio>
            </el-row>
          </el-form-item>
        </el-radio-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" src="./MultipleChannelSelect.ts">
</script>

<style lang="scss">
.multiple-channel-select {
  margin-top: 15px;
  margin-left: 22px;

  .el-form-item__error {
    margin-left: 22px;
    margin-top: 15px;
  }

  .el-row-35 {
    display: flex;
    align-items: center;
    position: relative;
  }

  .el-row-60 {
    top: 10px;
    display: flex;
    align-items: center;
    position: relative;
  }

  .el-div {
    top: 5px;
    left: 24px;
    position: relative;
  }

  .el-select {
    width: 360px;
  }

  .el-radio-group {
    margin-top: 15px;
  }
}
</style>