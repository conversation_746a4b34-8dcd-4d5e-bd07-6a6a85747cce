import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import TagCategory from 'model/precisionmarketing/tagcategory/TagCategory'
import TagCategoryFilter from "model/precisionmarketing/tagcategory/TagCategoryFilter";

export default class TagCategoryApi {

  /**
   * 修改标签分类排序，changedUuid为移动的分类节点uuid,nextUuid为移动后此节点的下一个节点uuid,如果下一节点不存在传 ”-“
   *
   */
  static changeOrder(changedUuid: string, nextUuid: string): Promise<Response<void>> {
    return ApiClient.server().get(`/v1/precision-marketing/tag-template-category/changeOrder/${changedUuid}/${nextUuid}`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 新建标签分类信息
   *
   */
  static create(body: TagCategory): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/precision-marketing/tag-template-category/create`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 删除标签分类信息
   *
   */
  static delete(uuid: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/precision-marketing/tag-template-category/delete/${uuid}`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 根据名称查询分类信息
   *
   */
  static getBy(name: string): Promise<Response<TagCategory>> {
    return ApiClient.server().get(`/v1/precision-marketing/tag-template-category/getBy/${name}`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 修改标签分类信息
   *
   */
  static modify(body: TagCategory): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/precision-marketing/tag-template-category/modify`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询标签分类信息
   *
   */
  static query(body: TagCategoryFilter): Promise<Response<TagCategory[]>> {
    return ApiClient.server().post(`/v1/precision-marketing/tag-template-category/query`, body, {}).then((res) => {
      return res.data
    })
  }

}
