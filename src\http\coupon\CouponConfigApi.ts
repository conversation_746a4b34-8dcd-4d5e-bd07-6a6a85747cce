/*
 * @Author: 黎钰龙
 * @Date: 2023-06-16 10:14:33
 * @LastEditTime: 2023-09-18 11:07:19
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\coupon\CouponConfigApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import CouponPayConfig from 'model/default/CouponPayConfig'
import Response from "model/common/Response";

export default class CouponConfigApi {
  /**
   * 获取券支付配置
   * 
   */
  static get(): Promise<Response<CouponPayConfig>> {
    return ApiClient.server().get(`/v1/coupon-config/getPayConfig`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取现金券可编辑字段配置
  */
  static getEnabledCashFaceAmountEditTemplateConfig(): Promise<Response<boolean>> {
    return ApiClient.server().get(`/v1/coupon-template/getEnabledCashFaceAmountEditTemplateConfig`, {
    }).then((res) => {
      return res.data
    })
  }
}