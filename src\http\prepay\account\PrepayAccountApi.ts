/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:19
 * @LastEditTime: 2023-10-30 17:49:29
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\prepay\account\PrepayAccountApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import IdName from 'model/common/IdName'
import Response from 'model/common/Response'

export default class PrepayAccountApi {
  /**
   * 获取所有已初始化账户类型
   *
   */
  static listEnableAccountType(): Promise<Response<IdName[]>> {
    return ApiClient.server().get(`/v1/prepay/pre-account/listEnableAccountType`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 当为true时，以下字段固定逻辑展示及传值,适用商品（写死全部商品）、适用门店（写死全部门店）、支付是否需要密码（写死不需要）、转出是否需要密码（写死需要）
   * 当为true时，以下字段固定逻辑展示及传值,适用商品（写死全部商品）、适用门店（写死全部门店）、支付是否需要密码（写死不需要）、转出是否需要密码（写死需要）
   * 后端写死，前端传值不变
  */
  static cardAttributeFix(): Promise<Response<boolean>> {
    return ApiClient.server().post(`/v1/card-template/cardAttributeFix`, {
    }).then((res) => {
      return res.data
    })
  }

}
