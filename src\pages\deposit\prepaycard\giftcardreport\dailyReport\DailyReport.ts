import { Component, Vue } from "vue-property-decorator";
import TimeRange from "../../cmp/timerange/TimeRange";
import ListWrapper from "cmp/list/ListWrapper.vue";
import SubHeader from "cmp/subheader/SubHeader.vue";
import I18nPage from "common/I18nDecorator";
import ZoneApi from "http/area/ZoneApi";
import ZoneFilter from "model/datum/zone/ZoneFilter";
import GiftCardFilter from "model/prepay/report/card/GiftCardFilter";
import OffLineGiftCardReportApi from "http/prepay/report/card/OffLineGiftCardReportApi";
import GiftCardDailyReportFilter from "model/prepay/report/card/GiftCardDailyReportFilter";
import GiftCardDailyHst from "model/prepay/report/card/GiftCardDailyHst";
import ChannelManagementFilter from "model/channel/ChannelManagementFilter";
import ChannelManagement<PERSON>pi from "http/channelmanagement/ChannelManagementApi";
import Channel from "model/common/Channel";
import DateUtil from "util/DateUtil";
import ChannelRange from "model/common/ChannelRange";
import RSOrg from "model/common/RSOrg";
import RSOrgFilter from "model/common/RSOrgFilter";
import OrgApi from "http/org/OrgApi";
import { SessionStorage } from "mgr/BrowserMgr";
import CouponTemplate from "model/coupon/template/CouponTemplate";
import RSChannelManagement from "model/common/RSChannelManagement";
import GiftCardReportApi from "http/prepay/report/card/GiftCardReportApi";
import FormItem from "cmp/formitem/FormItem";
@Component({
	name: "DailyReport",
	components: {
    FormItem,
		ListWrapper,
		SubHeader,
		TimeRange,
	},
})
@I18nPage({
	prefix: ["/储值/预付卡/电子礼品卡报表/售卡流水", "/公用/按钮", "/公用/提示",'/公用/查询条件/提示',],
})
export default class DailyReport extends Vue {
	expandQuery: boolean = false;
	query: GiftCardDailyReportFilter = new GiftCardDailyReportFilter();
	page: any = {
		currentPage: 1,
		pageSize: 10,
		total: 0,
		probeEnabled: null
	};
	areaData: any = [];
	ZoneFilter: ZoneFilter = new ZoneFilter();
	queryData: GiftCardDailyHst[] = [];
	channelTypes: any;
	DateUtil: any = DateUtil;
	stores: RSOrg[] = [];
	channelIdType: any = "";

	created() {
		this.query.occurredTimeAfterOrEqual = DateUtil.format(new Date(), 'yyyy-MM-dd')
        this.query.occurredTimeBefore = DateUtil.format(new Date(), 'yyyy-MM-dd')
		this.getStore();
		this.getAreaList();
		this.getChannelList();
		this.getDailyReport();
	}
	handleTimeRange(dateRange: any[]) {
		console.log(dateRange);
		if (dateRange && dateRange.length > 1) {
			this.query.occurredTimeAfterOrEqual = dateRange[0];
			this.query.occurredTimeBefore = dateRange[1];
			this.getDailyReport();
		}
	}

	private getStore() {
		let params: RSOrgFilter = new RSOrgFilter();
		params.page = 0;
		params.pageSize = 0;
		OrgApi.query(params)
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					this.stores = resp.data;
				}
			})
			.catch((error) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}

	private getKey(channel: Channel) {
		if (channel && channel.type && channel.id) {
			return (channel.type as any) + channel.id;
		}
		return channel.typeId;
	}

	getIdAndType(): any {
		let obj = {
			type: null,
			id: null,
		};
		if (this.channelTypes) {
			this.channelTypes.forEach((item: any) => {
				if (item.channel.type + item.channel.id === this.channelIdType || item.channel.typeId === this.channelIdType) {
					obj = {
						type: item.channel.type,
						id: item.channel.id,
					};
				}
			});
		}
		return obj
	}

	private getChannelList() {
		let query = new ChannelManagementFilter();
		query.page = 0;
		query.pageSize = 0;
		ChannelManagementApi.query(query)
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					this.channelTypes = resp.data;
				}
			})
			.catch((error) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}

	getDailyReport() {
		this.query.channelIdEquals = this.getIdAndType().id || null;
		this.query.channelTypeEquals = this.getIdAndType().type || null;
		this.query.page = this.page.currentPage - 1;
		this.query.pageSize = this.page.pageSize;
		GiftCardReportApi.queryCardDailyReport(this.query).then((res) => {
			this.queryData = this.getResponseData(res) || [];
			console.log(this.queryData);
			this.page.total = res.total;
			this.page.probeEnabled = res.fields ? res.fields.probeEnabled : null;
		});
	}

	/**
	 * 查询区域
	 */
	getAreaList() {
		// this.ZoneFilter.page = 0
		// this.ZoneFilter.pageSize = 10
		ZoneApi.query(this.ZoneFilter).then((res) => {
			if (res.code === 2000) {
				this.areaData = res.data;
			} else {
				this.$message.error(res.msg as string);
			}
		});
	}
	/**
	 * 查询
	 */
	doSearch() {
		this.page.currentPage = 1;
		this.getDailyReport();
	}

	/**
	 * 重置
	 */
	doReset() {
		this.query = new GiftCardDailyReportFilter();
		this.channelIdType = ''
		// @ts-ignore
		this.$refs.timeRange.reset();
	}

	/**
	 * 分页页码改变的回调
	 * @param val
	 */
	onHandleCurrentChange(val: number) {
		this.page.currentPage = val;
		this.getDailyReport();
	}

	/**
	 * 每页多少条的回调
	 * @param val
	 */
	onHandleSizeChange(val: number) {
		this.page.pageSize = val;
		this.getDailyReport();
	}

	getResponseData(response: any): any {
		let channel = SessionStorage.getItem("channels");
		if (channel) {
			if (response && response.data && response.data.length > 0) {
				response.data.forEach((item: any) => {
					if (item && item.channel) {
						if (channel && channel.length > 0) {
							channel.forEach((sub: RSChannelManagement) => {
								if (sub.channel && sub.channel.type === item.channel.type && sub.channel.id === item.channel.id) {
									item.channel.type = sub.name;
									console.log(sub.name);
								}
							});
						}
						if(item){
							if(!item.different){
								item.different = 0;
							}
							if(!item.inactiveTotalBalance){
								item.inactiveTotalBalance = 0;
							}
							if(!item.inactiveBalance){
								item.inactiveBalance = 0;
							}
							if(!item.inactiveGiftBalance){
								item.inactiveGiftBalance = 0;
							}
							if(!item.presentingTotalBalance){
								item.presentingTotalBalance = 0;
							}
							if(!item.presentingBalance){
								item.presentingBalance = 0;
							}
							if(!item.presentingGiftBalance){
								item.presentingGiftBalance = 0;
							}
							if(!item.usingTotalBalance){
								item.usingTotalBalance = 0;
							}
							if(!item.usingBalance){
								item.usingBalance = 0;
							}
							if(!item.usingGiftBalance){
								item.usingGiftBalance = 0;
							}
							if(!item.frozenTotalBalance){
								item.frozenTotalBalance = 0;
							}
							if(!item.frozenBalance){
								item.frozenBalance = 0;
							}
							if(!item.frozenGiftBalance){
								item.frozenGiftBalance = 0;
							}
						}
					}
				});
			}
		}
		return response.data;
	}
}
