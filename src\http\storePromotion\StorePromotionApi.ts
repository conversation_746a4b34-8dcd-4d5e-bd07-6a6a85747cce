import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import QueryStoreOilGunPromotionFilter from 'model/storePromotion/QueryStoreOilGunPromotionFilter'
import StoreOilGunPromotionInfo from 'model/storePromotion/StoreOilGunPromotionInfo'
import BatchSaveStoreOilGunPromotionRequest from 'model/storePromotion/BatchSaveStoreOilGunPromotionRequest'

export default class StorePromotionApi {
  /**
   * 查询
   *
   */
  static query(body: QueryStoreOilGunPromotionFilter): Promise<Response<StoreOilGunPromotionInfo[]>> {
    return ApiClient.server().post(`/v1/custom/store-oil-gun/promotion/query`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 保存
   *
   */
   static saveOrModify(body: BatchSaveStoreOilGunPromotionRequest): Promise<Response<any>> {
    return ApiClient.server().post(`/v1/custom/store-oil-gun/promotion/saveOrModify`, body, {}).then((res) => {
      return res.data
    })
  }


  /**
   * 根据门店查询营销信息
   *
   */
  static stats(orgId: string): Promise<Response<StoreOilGunPromotionInfo>> {
    return ApiClient.server().get(`/v1/custom/store-oil-gun/promotion/get/${orgId}`, {}).then((res) => {
      return res.data
    })
  }

}