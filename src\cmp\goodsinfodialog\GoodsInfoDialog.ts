import {Component, Prop, Vue} from 'vue-property-decorator'
import RSGoods from "model/common/RSGoods";
import RSGoodsFilter from "model/common/RSGoodsFilter";
import GoodsApi from "http/goods/GoodsApi";
import BrandApi from "http/brand/BrandApi";
import RSBrandFilter from "model/common/RSBrandFilter";
import RSBrand from "model/common/RSBrand";
import RSCategory from "model/common/RSCategory";
import RSCategoryFilter from "model/common/RSCategoryFilter";
import CategoryApi from "http/category/CategoryApi";

@Component({
  name: 'GoodsInfoDialog',
  components: {}
})
export default class GoodsInfoDialog extends Vue {
  @Prop({
    type: String,
    default: "barcode",
  })
  goodsMatchRuleMode: "barcode" | "code";
  visible = false
  goods: RSGoods = new RSGoods()
  brand: RSBrand = new RSBrand()
  category: RSCategory = new RSCategory()
  loading = false

  show(barcode: string) {
    if (!barcode) {
      return
    }
    this.visible = true
    let goodsFilter: RSGoodsFilter = new RSGoodsFilter()
    if (this.goodsMatchRuleMode === 'barcode') {
      goodsFilter.barcodeEquals = barcode
    } else {
      goodsFilter.codeEquals = barcode
    }
    this.loading = true
    GoodsApi.query(goodsFilter).then((res: any) => {
      if (res.code === 2000) {
        if (res.data.length > 0) {
          this.goods = res.data[0]
          this.queryBrand(this.goods.brandId)
          this.queryCategory(this.goods.categoryId)
        } else {
          this.$message.error(this.formatI18n('/公用/公共组件/商品详情控件/提示/商品不存在'))
          this.visible = false
        }
      }
    }).finally(() => {
      this.loading = false
    })
  }

  queryBrand(brandId: Nullable<string>) {
    if (brandId === null) {
      return
    }
    let brandFilter: RSBrandFilter = new RSBrandFilter()
    brandFilter.brandIdEquals = brandId
    BrandApi.query(brandFilter).then((res: any) => {
      if (res.code === 2000 && res.data.length > 0) {
        this.brand = res.data[0]
      }
    })
  }

  queryCategory(categoryId: Nullable<string>) {
    if (categoryId === null) {
      return
    }
    let filter: RSCategoryFilter = new RSCategoryFilter()
    filter.categoryIdEquals = categoryId
    CategoryApi.query(filter).then((res: any) => {
      if (res.code === 2000 && res.data.length > 0) {
        this.category = res.data[0]
      }
    })
  }

  doBeforeClose(done: any) {
    this.goods = new RSGoods()
    this.brand = new RSBrand()
    this.category = new RSCategory()
    done()
  }
}