<template>
  <el-dialog :title="formatI18n('/公用/公共组件/会员选择弹框组件/选择会员')" class="select-member-dialog" append-to-body :close-on-click-modal="false"
    :visible.sync="dialogShow">
    <div class="wrap">
      <el-form label-width="100px">
        <el-row class="query">
          <el-col :span="10">
            <el-form-item :label="formatI18n('/会员/会员资料', '会员')">
              <div style="width: 100%;display: flex">
                <el-select v-model="identType" @change="identTypeChange()" style="width: 150px;">
                  <el-option :label="formatI18n('/会员/会员资料','不限')" value="all"></el-option>
                  <el-option :label="formatI18n('/会员/会员资料', '手机号')" value="mobile"></el-option>
                  <el-option :label="formatI18n('/会员/会员资料', '实体卡号')" value="hdCardCardNumber"></el-option>
                  <el-option :label="formatI18n('/会员/会员资料', '会员号')" value="crmCode"></el-option>
                </el-select>
                <el-input :placeholder=identPlaceholder v-model="identCode" style="height: 30px">
                </el-input>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :title="formatI18n('/会员/会员资料', '会员状态')" :label="formatI18n('/会员/会员资料', '会员状态')">
              <el-select v-model="memberFilter.stateEquals" :disabled="true">
                <el-option :label="formatI18n('/会员/会员资料', '使用中')" value="Using">
                  {{ formatI18n('/会员/会员资料', '使用中') }}
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item>
              <el-button type="primary" @click="doSearch()">{{ formatI18n('/公用/按钮/查询') }}</el-button>
              <el-button @click="doReset()">{{ formatI18n('/公用/按钮/重置') }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-row>
        <el-col :span="24">
          <el-row class="table-wrap" v-loading="loading.query">
            <el-row class="thead" style="padding-right: 15px">
              <el-col :span="6" style="text-align: left!important;padding-left: 10px">{{ formatI18n('/会员/会员资料/会员') }}
              </el-col>
              <el-col :span="3" class="text-overflow-ellipsis" :title="formatI18n('/会员/会员资料/姓名')">
                {{ formatI18n('/会员/会员资料/姓名') }}
              </el-col>
              <el-col :span="2" class="text-overflow-ellipsis" :title="formatI18n('/会员/会员资料/会员等级')">
                {{ formatI18n('/会员/会员资料/会员等级') }}
              </el-col>
              <el-col :span="3" class="text-overflow-ellipsis" :title="formatI18n('/会员/会员资料/会员状态')">
                {{ formatI18n('/会员/会员资料/会员状态') }}
              </el-col>
              <el-col :span="4" class="text-overflow-ellipsis" :title="formatI18n('/会员/会员资料/归属门店')">
                {{ formatI18n('/会员/会员资料/归属门店') }}
              </el-col>
              <el-col :span="4" class="text-overflow-ellipsis" :title="formatI18n('/会员/会员资料/注册日期')">
                {{ formatI18n('/会员/会员资料/注册日期') }}
              </el-col>
              <el-col :span="2" class="text-overflow-ellipsis" :title="formatI18n('/会员/会员资料/注册渠道')">
                {{ formatI18n('/会员/会员资料/注册渠道') }}
              </el-col>
            </el-row>
            <el-row class="tbody" v-if="!loading.query">
              <template v-if="currentList && currentList.length > 0">
                <el-row v-for="(item, index) of currentList" class="trow" :key="index"
                  :style="{backgroundColor: selected[0] === item ? '#eeeeee': ''}">
                  <el-col @click.native="doCheckRow(index)" :span="6" :title="item.name" style="text-align: left!important;padding-left: 10px;">
                    <div style="line-height: 28px" class="text-overflow-ellipsis">{{ formatI18n('/会员/会员资料/会员号') }}: <span
                        :title="item.crmCode">{{ item.crmCode }}</span></div>
                    <div style="line-height: 28px" class="text-overflow-ellipsis">{{ formatI18n('/会员/会员资料/手机号') }}: <span
                        :title="item.mobile">{{ item.mobile }}</span></div>
                    <div style="line-height: 28px;display: flex;align-items: start">
                      <div>{{ formatI18n('/会员/会员资料/实体卡号') }}:</div>
                      <div v-if="item.hdCardCardNumList && item.hdCardCardNumList.length > 0" class="text-overflow-ellipsis">
                        <p v-for="(item, index) in item.hdCardCardNumList" :key="index" style="line-height: 28px" class="text-overflow-ellipsis">
                          <span :title="item">{{ item | nullable }}</span>&nbsp;&nbsp;
                        </p>
                      </div>
                      <div v-else>--</div>
                    </div>
                  </el-col>
                  <el-col @click.native="doCheckRow(index)" :span="3" :title="item.name" style="line-height: 84px">
                    {{ item.name|nullable }}
                  </el-col>
                  <el-col @click.native="doCheckRow(index)" :span="2" :title="item.gradeName" style="line-height: 84px">
                    {{ item.gradeName|nullable }}
                  </el-col>
                  <el-col @click.native="doCheckRow(index)" :span="3" style="line-height: 84px">
                    <el-tag size="small" type="success" v-if="item.state === 'Using'">
                      {{ formatI18n('/会员/会员资料', '使用中') }}
                    </el-tag>
                    <el-tag size="small" type="danger" v-if="item.state === 'Blocked'">
                      {{ formatI18n('/会员/会员资料', '已冻结') }}
                    </el-tag>
                    <el-tag size="small" type="warning" v-if="item.state === 'Unactivated'">
                      {{ formatI18n('/会员/会员资料', '未激活') }}
                    </el-tag>
                    <el-tag size="small" type="info" v-if="item.state === 'Canceled'">
                      {{ formatI18n('/会员/会员资料', '已注销') }}
                    </el-tag>
                    &nbsp;
                  </el-col>
                  <el-col @click.native="doCheckRow(index)" :span="4" :title="$options.filters.idName(item.ownStore)"
                    style="line-height: 84px">{{ item.ownStore|idName }}
                  </el-col>
                  <el-col @click.native="doCheckRow(index)" :span="4" style="line-height: 84px">
                    {{ item.registerTime | dateFormate3 }}
                  </el-col>
                  <el-col @click.native="doCheckRow(index)" :span="2" style="line-height: 84px"
                    :title="getRegisterChannel(item.registerChannel)">{{ getRegisterChannel(item.registerChannel) }}
                  </el-col>
                </el-row>
              </template>
              <el-row v-if="!currentList || currentList.length === 0" class="trow" style="text-align: center;color: #909399">
                {{ formatI18n('/公用/提示/暂无数据') }}
              </el-row>
            </el-row>
          </el-row>
        </el-col>
      </el-row>
    </div>
    <div class="page" style="margin-top: 15px">
      <el-pagination :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
        @current-change="handleCurrentChange($event)" @size-change="handleSizeChange($event)" background
        layout="total, prev, pager, next, sizes,  jumper">
      </el-pagination>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="dialogShow = false">{{ formatI18n('/公用/按钮/取消') }}</el-button>
      <el-button size="small" type="primary" @click="doModalClose()">{{ formatI18n('/公用/按钮/确定') }}</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./MemberSelectorDialog.ts"/>

<style lang="scss" scoped>
.select-member-dialog {
  display: flex;
  align-items: center;
  justify-content: center;
  @import "SelectorDialogCommon";

  .trow {
    height: auto !important;
    border-bottom: 1px solid #eeeeee;
  }

  .center-value {
    display: flex;
    align-items: center;
    vertical-align: center;
  }
}
</style>