/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2025-06-11 10:10:43
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\member_standard\MemberDetail.ts
 * 记得注释
 */
import Member from 'model/member_standard/Member'
import MemberIdent from 'model/common/member/MemberIdent'
import TagOption from 'model/common/TagOption'
import IdName from "model/common/IdName";

export default class MemberDetail extends Member {
  // 邀请人
  referee: Nullable<MemberIdent> = null
  // 最后修改人
  modifier: Nullable<string> = null
  // 最后修改时间
  modifed: Nullable<Date> = null
  // 等级有效期
  gradeValidate: Nullable<Date> = null
  // 会员到效日期
  expiredDate: Nullable<Date> = null
  // 呢称
  nickName: Nullable<string> = null
  // 年龄
  age: Nullable<string> = null
  // 身份证
  idCard: Nullable<string> = null
  // 学历
  education: Nullable<string> = null
  // 学历国际化
  educationI18n: Nullable<string> = null
  // 行业
  industry: Nullable<string> = null
  // 行业国际化
  industryI18n: Nullable<string> = null
  // 年收入
  annualIncome: Nullable<string> = null
  // 年收入
  annualIncomeI18n: Nullable<string> = null
  // 爱好
  hobbies: Nullable<string> = null
  // 备用手机号
  spareMobile: Nullable<string> = null
  // 邮件
  email: Nullable<string> = null
  // 地址
  address: Nullable<string> = null
  // 生活区域
  area: Nullable<string> = null
  // 图片
  image: Nullable<string> = null
  // 员工号
  employeeID: Nullable<string> = null
  // 职位
  office: Nullable<string> = null
  // 国籍
  nationality: Nullable<string> = null
  // 宗教
  religion: Nullable<string> = null
  // 省
  province: Nullable<IdName> = null
  // 市
  city: Nullable<IdName> = null
  // 区
  district: Nullable<IdName> = null
  // 街道
  street: Nullable<IdName> = null
  // 预付卡数（张）
  cardCount: Nullable<number> = null
  // 可用券数（张）
  couponCount: Nullable<number> = null
  // 积分）
  points: Nullable<number> = null
  // 储值余额（元）
  balance: Nullable<number> = null
  // 单账户下储值账户uuid
  balanceAccountUid: Nullable<string> = null
  // 成长值
  growthValue: Nullable<number> = null
  // 累计消费(笔)
  consumeQty: Nullable<number> = null
  // 累计消费
  totalConsume: Nullable<number> = null
  // 客单价(元)
  avgAmount: Nullable<number> = null
  // 最近一次消费
  lastConsumeDate: Nullable<Date> = null
  // 最近一次距今天数
  lastConsumeDay: Nullable<number> = null
  // 原会员手机号修改时必传
  oldMobile: string = ''
  // 变更说明
  remark: Nullable<string> = null
  // 标签信息
  tags: TagOption[] = []
  // 邮箱是否已校验
  emailChecked: Nullable<Boolean> = null
  // 邮箱是否已校验
  mobileChecked: Nullable<Boolean> = null
  // 归属导购
  guider: Nullable<IdName> = null
  // 自定义字段
  extObj: Nullable<string> = null
}