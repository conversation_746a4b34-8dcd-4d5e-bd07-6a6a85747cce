<template>
  <div class="douyin-member">
    <BreadCrume :panelArray="panelArray"></BreadCrume>
    <div class="content">
      <div class="authorize">{{formatI18n('/设置/抖音会员授权/抖音会员授权')}}</div>
      <div class="block">
        <h5>{{formatI18n('/会员/微信会员初始化/未授权/初始化第一步微信授权/授权后可以使用什么功能？')}}</h5>
        <p class="text">{{i18n('授权以后，可以打通抖音会员，顾客在抖音上开通商家会员同步至CRM，CRM里的存量会员可以去抖音上绑定会员并查看等级和积分')}}</p>
      </div>
      <div class="block">
        <h5>{{formatI18n('/设置/抖音会员授权/提示')}}</h5>
        <p class="text">{{i18n('选择渠道，点击开始授权前往抖音页面进行授权')}}</p>
      </div>
      <div style="display:flex;flex-direction:column;justify-content:center;align-items: center" >
        <form-item :label="i18n('解决方案')">
          <div style="line-height: 36px">
            <el-radio-group v-model="solutionKey">
              <el-radio label="4">{{i18n('到综')}}</el-radio>
              <el-radio label="1">{{i18n('餐饮')}}</el-radio>
            </el-radio-group>
          </div>
        </form-item>
        <p class="text" style="margin-top: 10px;margin-bottom: 10px">{{i18n('选择后不可更改，跟抖音确认解决方案后再选择')}}</p>
      </div>
      <div style="display:flex;justify-content:center;align-items: center" >
        <form-item :label="i18n('/会员/微信会员初始化/未授权/初始化第一步微信授权/授权渠道')">
          <ChannelSelect v-model="selectChannel" :appendAttr="{channelTypeEquals: 'DouYin'}" :multiple="false" :isOnlyId="false" :hideAll="true" width="310px" :placeholder="i18n('请选择抖音渠道')">
          </ChannelSelect>
        </form-item>
      </div>

    </div>
    <div class="authorize-next" @click="doAuth">{{formatI18n('/会员/微信会员初始化/未授权/初始化第一步微信授权/开始授权')}}</div>
  </div>
</template>

<script lang="ts" src="./DouYinMemberAuth.ts">
</script>

<style lang="scss" scoped>
.douyin-member {
  width: 100%;
  height: 100%;
  background-color: white;
  padding: 0;
  overflow: auto;
  .step-bottom1 {
    border-bottom: 2px solid rgb(49, 137, 253);
  }
  .content {
    padding: 15px 75px 75px;
    font-family: "微软雅黑", Helvetica, Arial, sans-serif;
  }
  .status {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-weight: 400;
    margin: 30px 10% 10px;
  }
  .authorize {
    text-align: center;
    font-size: 16px;
    font-weight: 600;
    color: white;
    line-height: 2.8;
    background: #39c9c9;
    margin: 0 10%;
    cursor: pointer;
  }
  .block {
    margin: 20px 10% 30px;
    color: #999999;
  }
  h5 {
    font-size: 12px;
    font-weight: 600;
    line-height: 2.4;
    color: #999999 !important;
  }
  .text {
    font-size: 13px;
    font-weight: 500;
    text-indent: 2em;
    line-height: 2;
  }
  .authorize-next {
    width: 140px;
    display: flex;
    justify-content: center;
    color: #fff;
    background-color: #3189fd;
    border: 1px solid #3189fd;
    border-radius: 5px;
    margin: 0px 0px 120px 960px;
    padding: 5px 20px;
    cursor: pointer;
    white-space: nowrap;
  }
  .authorize-after {
    width: 100px;
    display: flex;
    justify-content: center;
    color: #fff;
    background-color: #8e8c8c;
    border: 1px solid #8e8c8c;
    border-radius: 5px;
    margin: 0px auto 120px;
    padding: 5px 20px;
    cursor: pointer;
  }
}
</style>