<template>
  <div class="render-page-place">
    <el-form :model="localValue" :rules="rules" ref="form" inline label-width="100px" class="render-page-place-form">
      <el-form-item :label="i18n('页面名称')" prop="propTitle">
        <el-input v-model="localValue.propTitle" @change="handleChange" :placeholder="i18n('请输入标题名称')" :maxlength="15" show-word-limit></el-input>
      </el-form-item>
      <el-form-item label-position="left" :label="i18n('顶部导航栏')">
        <el-switch @change="handleChange" v-model="localValue.propShowTop" active-color="#13ce66">
        </el-switch>
        {{localValue.propShowTop ? i18n('启用'):i18n('不启用')}}
      </el-form-item>
    </el-form>
    <div class="render-page-place-box" :style="getPageStyle">
      <div :class="['render-page-place-box-top', {activeClass: -11 === activeIndex},{ errClass: titleValidate || !titleValid}]" @click="titleClick"
        :style="{ backgroundImage: pageTopImage}">
        {{ globalInvestTitle.propShowTop ? (globalInvestTitle.propTitle ? globalInvestTitle.propTitle : i18n('标题')) : '' }}
      </div>
      <el-scrollbar class="render-page-place-box-content">
        <draggable class="draggable-content" group="componentsGroup" @start="dragStart" @end="placeDragEnd" :list="renderTemplateList" item-key="id"
          :move="onPlaceMove" animation="500">
          <component v-for="(item, index) in renderTemplateList" :key="item.id + index" :is="getComponent(item.id)" :activeComponentId="activeComId"
            :activeIndex="activeIndex" :componentItem="item" :index="index" :toolBarBtns="getToolBarBtns(item.id)" @toolBarClick="toolBarClick"
            @activeCom="activeCom" :class="{activeClass: index === activeIndex, errClass: validateErr(item.uuid) }" :ref="'tempateComponent' + index">
          </component>
        </draggable>
        <template v-if="renderTemplateList.length === 0">
          <div class="empty-content">{{ i18n('组件放置区域') }}</div>
        </template>
      </el-scrollbar>
    </div>
  </div>
</template>

<script lang="ts" src="./RenderPagePlace.ts"></script>

<style lang="scss" scoped>
.render-page-place {
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;
  &-form {
    height: 58px;
    background: #ffffff;
  }
  &-box {
    flex: 1;
    width: 400px;
    margin: auto;
    overflow: hidden;
    height: 100%;
    &-top {
      height: 90px;
      color: #242633;
      font-size: 16px;
      font-weight: 600;
      line-height: 90px;
      text-align: center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    &-content {
      height: calc(100% - 90px);
      position: relative;
      flex: 1;
      .draggable-content {
        width: 100%;
        height: 100%;
        min-height: 200px;
        cursor: move;
      }
    }
  }

  .empty-content {
    height: 48px;
    width: 100%;
    border: 1.6px dashed #4d63ec;
    text-align: center;
    color: #4d63ec;
    padding-top: 15px;
    background-color: #edeffd;
    position: absolute;
    top: 20px;
  }
}
.activeClass {
  border: 2px solid #4d63ec !important;
}
.errClass {
  border: 2px solid #ec4d4d;
}
::deep .el-scrollbar__wrap {
  overflow-x: hidden;
}

:v-deep .el-scrollbar__view {
  height: 100%;
}
</style>
