import { Component, Vue } from 'vue-property-decorator'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import FormItem from 'cmp/formitem/FormItem.vue'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import CardAdjustBill from 'model/card/adjustbill/CardAdjustBill'
import I18nPage from "common/I18nDecorator";
import SysConfigApi from "http/config/SysConfigApi";
import CardDepositBillApi from 'http/card/depositbill/CardDepositBillApi';
import CardDepositBillLine from 'model/card/depositbill/CardDepositBillLine';
import CardDepositBillAmountSummary from "model/card/depositbill/CardDepositBillAmountSummary";
import PrepayCardImportDialog from './BatchImportDialog/PrepayCardImportDialog'
import BCardDepositBillImportRequest from 'model/card/depositbill/BCardDepositBillImportRequest'
import DownloadCenterDialog from 'pages/main/dowmload_center_dialog/DownloadCenterDialog'

@Component({
  name: 'PrepayCardPayDtl',
  components: {
    SubHeader,
    FormItem,
    BreadCrume,
    PrepayCardImportDialog,
    DownloadCenterDialog
  }
})
@I18nPage({
  prefix: [
    '/储值/预付卡/预付卡调整单/预付卡调整单详情', '/公用/按钮', '/公用/提示',
    '/储值/预付卡/预付卡充值单', '/储值/预付卡/预付卡调整单/编辑页面',
  ],
})
export default class PrepayCardPayDtl extends Vue {
  $refs: any
  i18n: (str: string, params?: string[]) => string
  panelArray: any
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  dtlTableData = []
  oparatorTableData = []
  billDtl: CardAdjustBill = new CardAdjustBill()
  queryDtl: CardDepositBillLine[] = []
  queryDtlTotalAmount: CardDepositBillAmountSummary = new CardDepositBillAmountSummary()
  showOrg = false // 控制模态框的展示
  clientRequired: boolean = false  // 是否开启客户必填
  fileDialogvisiable = false

  // 支付总金额
  get totalPayAmount() {
    if (this.billDtl.payments.length > 0) {
      return this.billDtl.payments.reduce((totalPayAmount, payment) => {
        if (payment.paymentAmount) {
          return (Number(totalPayAmount) + Number(payment.paymentAmount)).toFixed(2)
        }
        return totalPayAmount;
      }, 0)
    } else {
      return 0;
    }
  }
  get clientInfo() {
    let str = '';
    if (this.billDtl.clientCode != '' && this.billDtl.clientCode != null && this.billDtl.clientCode != undefined) {
      str = str + '[' + this.billDtl.clientCode + ']';
    }
    if (this.billDtl.clientName != '' && this.billDtl.clientName != null && this.billDtl.clientName != undefined) {
      str = str + this.billDtl.clientName;
    }
    return str;
  }

  created() {
    this.panelArray = [
      {
        name: this.i18n('预付卡充值单'),
        url: 'prepay-card-pay'
      },
      {
        name: this.i18n('预付卡充值单详情'),
        url: ''
      }
    ]
    this.getStoreValueDtl()
    this.getQueryDetail()
    this.getConfig()
    this.queryDetailTotalAmount()
  }
  /**
   * 分页页码改变的回调
   * @param val
   */
  doBack() {
    this.$router.push({ name: 'prepay-card-pay' })
  }
  doModify() {
    this.$router.push({ name: 'prepay-card-pay-add', query: { id: this.$route.query.id, from: 'edit' } })
  }
  doAudit() {
    CardDepositBillApi.audit(this.$route.query.id.toString()).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(this.i18n('审核成功'))
        this.getStoreValueDtl()
        this.getQueryDetail()
        this.queryDetailTotalAmount()
      } else {
        this.$message.error(resp.msg)
      }
    }).catch((error) => {
      if (error && error.message) {
        console.log(error.message)
        this.$message({
          dangerouslyUseHTMLString: true,
          message: error.message.replace(/\n/g, "<br/>"),
          type: 'error'
        })
      }
    })
  }
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.getQueryDetail()
  }
  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.getQueryDetail()
  }
  private getStoreValueDtl() {
    CardDepositBillApi.get(this.$route.query.id.toString()).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.billDtl = resp.data
        this.dtlTableData = resp.data.lines
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  private getQueryDetail() {

    CardDepositBillApi.queryDetail(this.$route.query.id.toString(), this.page.currentPage - 1, this.page.size).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.queryDtl = resp.data
        this.page.total = resp.total
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  private queryDetailTotalAmount() {
    CardDepositBillApi.getAmountSummary(this.$route.query.id.toString()).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.queryDtlTotalAmount = resp.data
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  // private getPrePermission() {
  //   const loading = this.$loading(ConstantMgr.loadingOption)
  //   PrePayConfigApi.get().then((resp: any) => {
  //     if (resp && resp.code === 2000) {
  //       loading.close()
  //       if (resp.data && resp.data.enableMultipleAccount) { // 开启多账户
  //         this.switchFlag = true
  //       } else {
  //         this.switchFlag = false // 未开启多账户
  //       }
  //     }
  //   }).catch((error) => {
  //     if (error && error.message) {
  //       this.$message.error(error.message)
  //     }
  //     loading.close()
  //   })
  // }

  private getConfig() {
    SysConfigApi.get().then((resp: any) => {
      if (resp && resp.data) {
        this.showOrg = resp.data.enableMultiMarketingCenter
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  // 查询储值卡配置
  getCardDepositConfig() {
    CardDepositBillApi.getConfig().then((resp) => {
      if (resp.code === 2000) {
        this.clientRequired = resp.data?.clientRequired || false
      } else {
        throw new Error(resp.msg!)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  doUploadSuccess() {
    this.fileDialogvisiable = true
    this.getStoreValueDtl()
    this.getQueryDetail()
    this.queryDetailTotalAmount()
  }

  doImportModify() {
    const body = new BCardDepositBillImportRequest()
    body.amount = Number(this.queryDtlTotalAmount.totalOccurAmount) - Number(this.billDtl.favAmount)
    body.billNumber = this.billDtl.billNumber
    body.clientId = this.billDtl.clientId
    body.depositType = this.billDtl.depositType
    body.favAmount = this.billDtl.favAmount
    body.orgId = this.billDtl.occurredOrg?.id
    body.payTypes = this.billDtl.payments || []
    body.remark = this.billDtl.remark
    this.$refs.prepayCardImportDialog.open(this.billDtl.billNumber, body)
  }

  doDownloadDialogClose() {
    this.fileDialogvisiable = false
  }
}
