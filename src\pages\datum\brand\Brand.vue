<template>
    <div class="brand-view">
        <BreadCrume :panelArray="panelArray">
            <template slot="operate">
            </template>
        </BreadCrume>
        <div class="current-page">
            <div class="query">
                <FloatBlock refClass="current-page" :top="95" style="padding: 10px 10px;">
                    <template slot="ctx">
                        <el-row>
                            <el-col :span="12" style="line-height: 36px">
                                {{formatI18n('/资料/品牌/共')}} <span class="number-text">{{total}}</span>
                                {{formatI18n('/资料/品牌/个品牌')}}
                            </el-col>
                            <el-col :span="12" style="text-align: right">
                                <form-item label="">
                                    <el-input :placeholder="formatI18n('/资料/品牌/搜索品牌代码/名称')" @change="doSearch"
                                              v-model="query.key"
                                              suffix-icon="el-icon-search" style="width: 280px"/>
                                </form-item>
                            </el-col>
                        </el-row>
                    </template>
                </FloatBlock>
            </div>
            <div class="list">
                <el-table
                    :data="queryData"
                    row-key="brand.id"
                    default-expand-all
                    :tree-props="{children: 'children'}">
                    <el-table-column
                        fixed
                        prop="brand.id"
                        :label="formatI18n('/资料/品牌/品牌代码')"
                        width="300">
                        <template slot-scope="scope">
                            <span v-if="scope.row.upper === null && scope.row.children === null" style="margin-left: 22px"></span>
                            {{scope.row.brand.id}}
                        </template>
                    </el-table-column>
                    <el-table-column
                        fixed
                        prop="brand.name"
                        :label="formatI18n('/资料/品牌/品牌名称')"
                        width="300">
                    </el-table-column>
                    <el-table-column
                        fixed
                        prop="remark"
                        :label="formatI18n('/资料/品牌/备注')">
                        <template slot-scope="scope">
                            <div :title="scope.row.remark" class="cell">{{scope.row.remark|nullable}}</div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="page">
                <el-pagination
                    :current-page="page.currentPage"
                    :page-size="page.size"
                    :page-sizes="[10, 20, 30, 40]"
                    :total="page.total"
                    @current-change="onHandleCurrentChange"
                    @size-change="onHandleSizeChange"
                    background
                    layout="total, prev, pager, next, sizes,  jumper">
                </el-pagination>
            </div>
        </div>
    </div>
</template>

<script lang="ts" src="./Brand.ts">
</script>

<style lang="scss">
    .brand-view {
        background-color: white;
        height: 100%;
        width: 100%;
        overflow: hidden;

        .total {
            margin: 20px;
        }

        .current-page {
            height: calc(100% - 77px);
            overflow: auto;
            padding: 10px;

            .query {
            }

            .list {
                .el-table {
                    width: calc(100% - 20px);
                    margin: 0 10px;
                }
            }

            .page {
                padding: 10px;
            }

            .el-select {
                width: 100%;
            }

            .el-col {
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }

            .cell {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }

        .el-range-editor.el-input__inner {
            width: 100%;
        }
    }
</style>
