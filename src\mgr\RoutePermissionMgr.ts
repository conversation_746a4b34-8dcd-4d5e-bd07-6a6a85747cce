/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2023-10-09 10:28:40
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\mgr\RoutePermissionMgr.ts
 * 记得注释
 */
import router from "../router";
import Fant from 'fant3-hd'
import I18nTool from "common/I18nTool";
import PrepayCardTplPermission from "pages/deposit/prepaycard/prepaycardtpl/PrepayCardTplPermission";
import CardTemplateApi from "http/card/template/CardTemplateApi";
import PermissionMgr from "mgr/PermissionMgr";
import store from "store/index";

// routeName -> checker
const routeComplexPermissionCheckers: any = {
  'prepay-card-tpl-dtl': (router: { name: string, path: string, query: any }) => {
    return new Promise<void>((resolve, reject) => {
      let prepayCardTplPermission = new PrepayCardTplPermission()
      if (router.query.cardTemplateType) { // 如果有卡模板类型直接用卡模板类型判断
        if (prepayCardTplPermission.viewable(router.query.cardTemplateType)) {
          resolve()
        } else {
          reject()
        }
      } else if (router.query.number) {
        CardTemplateApi.info(router.query.number).then((resp: any) => {
          if (resp && resp.code === 2000 && prepayCardTplPermission.viewable(resp.data.cardTemplateType as any)) {
            resolve()
          } else {
            reject()
          }
        })
      } else if (prepayCardTplPermission.anyEditable) {
        resolve()
      }
    })
  },
}


export default class RoutePermissionMgr {
  /** 检查某个路由是否有权限跳转，没权限就报错 */
  static checkRoutePermission(router: any) {
    return new Promise<void>((resolve) => {
      if (store.state.token && store.state.token.accessToken && router.name === 'login') {
        console.log('uni登录不允许进入login页面');
        resolve()
        return
      }
      if (!store.state.permissions) {
        resolve()
        return
      }
      if (router.meta && router.meta.authFunc) {
        router.meta.authFunc(router).then(() => {
          resolve()
          return
        }).catch((e: any) => {
          Fant.Message.error(I18nTool.match('/会员/会员资料/您未被授权访问此页面'))
          console.error('通过router.meta.authFunc检查无跳转该路由的权限')
        })
      } else if (router.meta && router.meta.permissions) {
        for (let permission of router.meta.permissions) {
          if (PermissionMgr.hasOptionPermission(permission.resourceId, permission.action)) {
            resolve()
            return
          }
        }
        Fant.Message.error(I18nTool.match('/会员/会员资料/您未被授权访问此页面'))
        console.error('通过router.meta.permissions检查无跳转该路由的权限')
      } else {
        resolve()
      }
    })
  }

  /** 新窗口打开一个路由，打开前检查是否有权限，没权限就不打开并报错，注意，要使用路由的name属性进行跳转！ */
  static openBlank(route: any) {
    let resolved = router.resolve(route)
    RoutePermissionMgr.checkRoutePermission(resolved.route).then(() => {
      window.open(resolved.href, '_blank')
    })
  }
}

export {
  routeComplexPermissionCheckers
}