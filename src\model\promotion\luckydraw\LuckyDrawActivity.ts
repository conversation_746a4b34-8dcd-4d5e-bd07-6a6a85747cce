import BaseCouponActivity from 'model/coupon/activity/BaseCouponActivity'
import GoodsRange from 'model/common/GoodsRange'
import LuckyPrizeSetting from 'model/promotion/luckydraw/LuckyPrizeSetting'
import NumberCalculatorItem from 'model/points/activity/goodsgainadditionalpoints/NumberCalculatorItem'
import PushGroup from 'model/precisionmarketing/pushplan/PushGroup'
import StoreRange from 'model/common/StoreRange'
import { ConsumeGainTimeRuleType } from "model/promotion/luckydraw/ConsumeGainTimeRuleType";
import { WhiteListSettingType } from "model/promotion/luckydraw/WhiteListSettingType";
import WhiteListSetting from "model/promotion/luckydraw/WhiteListSetting";
import WinningTimeSetting from "model/promotion/luckydraw/WinningTimeSetting";
import { LuckyPrizeSettingType } from "model/promotion/luckydraw/LuckyPrizeSettingType";
import ChannelRange from "model/common/ChannelRange";

export default class LuckyDrawActivity extends BaseCouponActivity {
  // 活动图片
  image: Nullable<string> = null
  // 适用客群
  rule: Nullable<PushGroup> = null
  // 免费领取数量
  freeReceiveCount: Nullable<number> = null
  // 是否开启积分兑换
  enablePointsExchange: Nullable<boolean> = null
  // 免费领取消耗多少积分兑换一次数量
  usePoints: Nullable<number> = null
  // 最大兑换次数
  maxExchangeTimes: Nullable<number> = null
  // 每人最大兑换次数
  perPersonMaxExchangeTimes: Nullable<number> = null
  // 是否开启消费领取
  enableConsumeReceive: Nullable<boolean> = null
  // 消费最大领取数量
  maxConsumeReceiveCount: Nullable<number> = null
  // 每人消费最大领取数量
  perPersonMaxConsumeReceiveCount: Nullable<number> = null
  // 消费获得次数计算规则
  consumeGainTimeRuleType: Nullable<ConsumeGainTimeRuleType> = null
  // 商品范围信息
  goodsRange: Nullable<GoodsRange> = null
  // 消费渠道
  consumeChannels: Nullable<ChannelRange> = null
  // 消费门店
  consumeStores: Nullable<StoreRange> = null
  // 计算规则
  consumeGainTimeRule: NumberCalculatorItem[] = []
  // 是否支持分享
  enableShare: Nullable<boolean> = null
  // 分享标题
  shareTitle: Nullable<string> = null
  // 分享图片
  shareImage: Nullable<string> = null
  // 分享多次活动1个兑换码
  shareTimes: Nullable<number> = null
  // 分享最大领取数量
  maxShareReceiveCount: Nullable<number> = null
  // 每人分享最大领取数量
  perPersonMaxShareReceiveCount: Nullable<number> = null
  // 开奖次数
  winningCount: Nullable<number> = null
  // 中奖白名单设置类型
  whiteListSettingType: Nullable<WhiteListSettingType> = null
  // 中奖白名单设置
  whiteListSettings: WhiteListSetting[] = []
  // 开奖时间设置
  winningTimeSetting: Nullable<WinningTimeSetting> = null
  // 奖品设置类型
  luckyPrizeSettingType: Nullable<LuckyPrizeSettingType> = null
  // 奖品设置
  prizeSettings: LuckyPrizeSetting[] = []
  // 是否支持页面分享
  enablePageShare: Nullable<boolean> = true
}
