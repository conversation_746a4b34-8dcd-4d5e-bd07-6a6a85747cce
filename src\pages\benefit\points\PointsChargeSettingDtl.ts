/*
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:03
 * @LastEditTime: 2024-02-07 13:55:08
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\benefit\points\PointsChargeSettingDtl.ts
 * 记得注释
 */
import { Component, Vue } from 'vue-property-decorator'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import FormItem from 'cmp/formitem/FormItem.vue'
import PointsChargeMetaActivityApi from 'http/points/init/pointschargemeta/PointsChargeMetaActivityApi'
import PointsChargeMetaActivity from 'model/points/init/pointschargemeta/PointsChargeMetaActivity'
import ActivityBody from 'model/common/ActivityBody'
import I18nPage from 'common/I18nDecorator'
import CommonUtil from 'util/CommonUtil'

@Component({
  name: 'PointsChargeSettingDtl',
  components: {
    BreadCrume,
    FormItem
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板'
  ],
  auto: true
})
export default class PointsChargeSettingDtl extends Vue {
  id = ''
  dtl: Nullable<PointsChargeMetaActivity> = null
  panelArray: any = []

  get hasRule() {
    return this.dtl
  }

  created() {
    this.panelArray = [
      {
        name: this.formatI18n("/权益/积分/积分初始化/未初始化状态/积分抵现规则", "积分抵现"),
        url: "",
      },
    ];
    this.getDtl()
  }
  doSet() {
    this.$router.push({ name: 'points-charge-setting' })
  }
  doEdit() {
    this.$router.push({ name: 'points-charge-setting', query: { id: this.id } })
  }
  doBack() {
    this.$router.back()
  }
  getDtl() {
    const loading = CommonUtil.Loading()
    PointsChargeMetaActivityApi.detail().then((resp: any) => {
      if (resp && resp.code === 2000) {
        if (resp.data) {
          this.id = resp.data.body.activityId
          this.dtl = resp.data
        }
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    }).finally(() => {
      loading.close()
    })
  }
}