/*
 * @Author: l<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-11-25 09:18:27
 * @LastEditors: liyulong <EMAIL>
 * @LastEditTime: 2022-11-28 15:24:18
 * @FilePath: \phoenix-web-ui\src\model\common\ActivityGroupType.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export default class ActivityGroupType {
	USER_POINT: "USER_POINT"; //积分消耗
	CONSUME_GIFT: "CONSUME_GIFT"; //消费有礼
	GET_CARD_GIFT: "GET_CARD_GIFT"; //开卡有礼
	DEPOSIT_GIFT: "DEPOSIT_GIFT"; //充值有礼
	ONLINE_GIFT_CARD_ISSUE: "ONLINE_GIFT_CARD_ISSUE"; //电子礼品卡发售
	INVITE_GIFT: "INVITE_GIFT"; //邀请有礼
	BIRTHDAY_GIFT: "BIRTHDAY_GIFT"; //生日有礼
	MEMBER_DAY_GIFT: "MEMBER_DAY_GIFT"; //会员日有礼
	THREE_ISSUE_COUPON: "THREE_ISSUE_COUPON"; //三方发券
	WEIXIN_ISSUE_COUPON: "WEIXIN_ISSUE_COUPON"; //微信领券
	WEIXIN_BRAND_ACTIVITY: "WEIXIN_BRAND_ACTIVITY";	//开屏推广
	MANUAL_COUPON: "MANUAL_COUPON"; //群发券
	MEMBER_BALANCE_FAVOURABLE: "MEMBER_BALANCE_FAVOURABLE"; //储值支付优惠
	CARD_BALANCE_FAVOURABLE: "CARD_BALANCE_FAVOURABLE"; //预付卡支付优惠
	PRECISION_MARKETING:"PRECISION_MARKETING"; //精准营销
  IMPROVE_PROFILES_GIFT:'IMPROVE_PROFILES_GIFT';//完善资料有礼
  ALI_ISSUE_COUPON_ACTIVITY: 'ALI_ISSUE_COUPON_ACTIVITY'//支付宝发券
}
