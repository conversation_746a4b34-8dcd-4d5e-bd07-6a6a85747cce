import {Component, Vue} from 'vue-property-decorator'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import SalesCardsReport from './salescards/SalesCardsReport';
import ComsumeReport from './consume/ComsumeReport';
import RefundReport from './refund/RefundReport';
import DailyReport from './dailyReport/DailyReport';
import I18nPage from "common/I18nDecorator";
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import OffLineGiftCardReportExportConfirm
  from "pages/deposit/prepaycard/offlinegiftcardreport/export/OffLineGiftCardReportExportConfirm.vue";
import DownloadCenterDialog from "pages/main/dowmload_center_dialog/DownloadCenterDialog";
import GiftCardFilter from "model/prepay/report/card/GiftCardFilter";
import OffLineGiftCardReportApi from "http/prepay/report/card/OffLineGiftCardReportApi";
import GiftCardDailyReportFilter from 'model/prepay/report/card/GiftCardDailyReportFilter';
import RSChannelManagementFilter from 'model/common/RSChannelManagementFilter';
import ChannelManagementApi from 'http/channelmanagement/ChannelManagementApi';
import { SessionStorage } from 'mgr/BrowserMgr';
import GiftReportSum from '../cmp/giftReportSum/GiftReportSum';

@Component({
	name: "OfflineGiftCardReportReport",
	components: {
		SubHeader,
		SalesCardsReport,
		ComsumeReport,
		RefundReport,
		BreadCrume,
		OffLineGiftCardReportExportConfirm,
		DownloadCenterDialog,
		DailyReport,
		GiftReportSum,
	},
})
@I18nPage({
	prefix: ["/储值/预付卡/实体礼品卡报表", "/储值/预付卡/电子礼品卡报表", "/公用/提示", "/公用/查询条件", "/公用/按钮"],
	auto: false,
})
export default class OfflineGiftCardReportReport extends Vue {
	activeName: string = "售卡流水";
	panelArray: any = [];
	exportDialogShow = false;
	fileDialogVisible = false;
	showTip = false;
	created() {
		this.panelArray = [
			{
				name: this.formatI18n("/公用/菜单/实体礼品卡报表"),
				url: "",
			},
		];
		this.getChannels();
	}

	get getExportDialogShow() {
		return this.exportDialogShow;
	}

	private getChannels() {
		let param: RSChannelManagementFilter = new RSChannelManagementFilter();
		ChannelManagementApi.query(param)
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					SessionStorage.setItem("channels", resp.data);
				}
			})
			.catch((error: any) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}

	doExportDialogClose() {
		this.exportDialogShow = false;
	}

	doBatchExport() {
		this.exportDialogShow = true;
	}

	doDownloadDialogClose() {
		this.showTip = false;
		this.fileDialogVisible = false;
	}
	exportAfter() {
		this.showTip = true;
		this.fileDialogVisible = true;
	}

	doExportSubmit(type: string, filter: GiftCardFilter | GiftCardDailyReportFilter) {
		if (!type || !filter) {
			return;
		}
		if (type === "SALES_HST") {
			OffLineGiftCardReportApi.exportSalesHst(filter as GiftCardFilter)
				.then((resp: any) => {
					if (resp && resp.code === 2000) {
						this.exportAfter();
					}
				})
				.catch((error: any) => {
					if (error && error.message) {
						this.$message.error(error.message);
					}
				});
		} else if (type === "CONSUME_HST") {
			OffLineGiftCardReportApi.exportConsumeHst(filter as GiftCardFilter)
				.then((resp: any) => {
					if (resp && resp.code === 2000) {
						this.exportAfter();
					}
				})
				.catch((error: any) => {
					if (error && error.message) {
						this.$message.error(error.message);
					}
				});
		} else if (type === "REFUND_HST") {
			OffLineGiftCardReportApi.exportRefundHst(filter as GiftCardFilter)
				.then((resp: any) => {
					if (resp && resp.code === 2000) {
						this.exportAfter();
					}
				})
				.catch((error: any) => {
					if (error && error.message) {
						this.$message.error(error.message);
					}
				});
		} else if (type === "DAY_HST") {
			OffLineGiftCardReportApi.exportCardDailyReport(filter as GiftCardDailyReportFilter)
				.then((resp: any) => {
					if (resp && resp.code === 2000) {
						this.exportAfter();
					}
				})
				.catch((error: any) => {
					if (error && error.message) {
						this.$message.error(error.message);
					}
				});
		}
	}
}