/*
 * @Author: 黎钰龙
 * @Date: 2024-09-14 10:07:01
 * @LastEditTime: 2024-09-14 10:07:11
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\points\activity\pointsexchangecoupon\BBatchPointsExchangeCouponActivityResult.ts
 * 记得注释
 */
import CouponItem from "model/v2/coupon/improveProfiles/CouponItem"

export default class BBatchPointsExchangeCouponActivityResult {
  // 成功数量
  success: Nullable<number> = null
  // 失败数量
  fail: Nullable<number> = null
  // 失败券券信息
  couponItem: CouponItem[] = []
}