<!--
 * @Author: 黎钰龙
 * @Date: 2024-05-14 19:13:31
 * @LastEditTime: 2024-05-27 17:56:53
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\MarketingBudget\MarketingBudgetEdit.vue
 * 记得注释
-->
<template>
  <div class="setting-container" v-if="isPermit">
    <div class="setting-block">
      <div class="section-title">{{i18n('营销预算')}}</div>
      <el-form :model="currentVal" :rules="rules" ref="ruleForm" label-width="120px" class="demo-ruleForm">
        <el-form-item :label="i18n('是否总部费用')" prop="headquartersCost">
          <el-radio-group :disabled="disabled" v-model="currentVal.headquartersCost">
            <el-radio :label="true">{{i18n('是')}}</el-radio>
            <el-radio :label="false">{{i18n('否')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="i18n('门店属性')" prop="orgProperties">
          <el-radio-group :disabled="disabled" v-model="currentVal.orgProperties">
            <el-radio label="DIRECT_SALES">{{i18n('直营')}}</el-radio>
            <el-radio label="FRANCHISE">{{i18n('特许')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="i18n('预计销售额')" prop="estimatedSales">
          <AutoFixInput :disabled="disabled" :min="0.01" :max="999999999.99" :fixed="2" v-model="currentVal.estimatedSales" style="width: 148px"
            :appendTitle="formatI18n('/券/购券管理','元')" @change="doComputeRate">
          </AutoFixInput>
        </el-form-item>
        <el-form-item :label="i18n('预计折扣费用')" prop="estimatedDiscountCost">
          <AutoFixInput :disabled="disabled" :min="0.01" :max="999999999.99" :fixed="2" v-model="currentVal.estimatedDiscountCost" style="width: 148px"
            :appendTitle="formatI18n('/券/购券管理','元')" @change="doComputeRate">
          </AutoFixInput>
        </el-form-item>
        <el-form-item :label="i18n('预计折扣率')" prop="estimatedDiscountRate">
          <AutoFixInput :disabled="disabled" :min="0.01" disabled :max="100" :fixed="2" v-model="currentVal.estimatedDiscountRate" style="width: 100px" appendTitle="%">
          </AutoFixInput>
        </el-form-item>
        <el-form-item :label="i18n('预估费用')">
          <AutoFixInput :disabled="disabled" :min="0.01" :max="999999999.99" :fixed="2" v-model="currentVal.estimatedCost" style="width: 148px"
            :appendTitle="formatI18n('/券/购券管理','元')">
          </AutoFixInput>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts" src="./MarketingBudgetEdit.ts">
</script>

<style lang="scss" scoped>
</style>