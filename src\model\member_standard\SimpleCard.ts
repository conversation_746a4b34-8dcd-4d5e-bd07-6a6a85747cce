export default class SimpleCard {
    // 卡名称=卡面额+卡名称
    name: Nullable<string> = null
    // 卡面额
    faceAmount: Nullable<number> = null
    // 卡号
    code: Nullable<string> = null
    // 卡状态
    state: Nullable<string> = null
    // 有效期
    expireDate: Nullable<Date> = null
    // 是否已经过期
    expired: Nullable<boolean> = null
    // 余额
    balance: Nullable<number> = null
    // 是否默认卡
    defaultCard: Nullable<boolean> = null
    // 卡类型:CountingCard (次卡)ImprestCard  (充值卡)OfflineGiftCard (礼品卡)"OnlineGiftCard (礼品卡)RechargeableCard (储值卡)
    cardType: Nullable<string> = null
}
