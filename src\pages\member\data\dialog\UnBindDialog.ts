import { Component, Vue, Prop } from 'vue-property-decorator'
import MemberApi from 'http/member_standard/MemberApi'

@Component({
  name: 'UnbindDialog',
  components: {}
})
export default class UnBindDialog extends Vue {
  coupons: any = []
  @Prop()
  uuid: any
  @Prop()
  memberId: any
  @Prop()
  title: any

  @Prop({
    type: Boolean,
    default: false
  })
  dialogShow: boolean

  doBeforeClose(done: any) {
    this.$emit('dialogClose')
    done()
  }
  doModalClose() {
      MemberApi.unbind(this.uuid, this.memberId as string).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success('解绑成功')
          this.$emit('dialogClose')
        }
      }).catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
  }
  doCancel() {
    this.$emit('dialogClose')
  }
}