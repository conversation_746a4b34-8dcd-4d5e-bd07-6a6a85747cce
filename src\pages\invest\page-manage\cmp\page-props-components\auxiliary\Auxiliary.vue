<template>
  <div class="activity_place_name">
    <el-form :model="value" :rules="rules" ref="form">
      <el-form-item :label="label" prop="name" style="width: 100%" class="activity-input">
        <el-input maxlength="4" show-word-limit v-model="value.propAuxiliaryText" @change="handleChange" style="width: 100%"></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" src="./Auxiliary.ts"></script>

<style lang="scss" scoped>
.activity_place_name {
  .activity-input {
    margin-bottom: 10px;
    ::v-deep .el-form-item__content {
      width: 100% !important;
    }
  }
}
</style>
