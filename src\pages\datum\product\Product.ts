import {Component, Vue} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import ListWrapper from 'cmp/list/ListWrapper.vue'
import Dot from 'cmp/dot/Dot.vue'
import UploadFileModal from 'pages/member/data/cmp/UploadFileModal.vue'
import DownloadCenterDialog from 'pages/main/dowmload_center_dialog/DownloadCenterDialog.vue'
import RSGoodsFilter from 'model/common/RSGoodsFilter';
import RSEmployee from 'model/common/RSEmployee';
import GoodsApi from 'http/goods/GoodsApi';
import SubHeader from 'cmp/subheader/SubHeader.vue';
import BreadCrume from "cmp/bread-crumb/BreadCrume";

@Component({
    name: 'Brand',
    components: {
        FormItem,
        ListWrapper,
        Dot,
        UploadFileModal,
        DownloadCenterDialog,
        SubHeader,
        BreadCrume
    }
})
export default class Product extends Vue {
    query: RSGoodsFilter = new RSGoodsFilter()
    queryData: RSEmployee[] = []
    tableHeight: number = 0
    // 分页
    page = {
        currentPage: 1,
        total: 0,
        size: 10
    }
    $refs: any
    panelArray: any = []
    created() {
        this.panelArray = [
            {
                name: this.formatI18n('/公用/菜单/商品'),
                url: ''
            },
        ]
        this.getList()
    }

    mounted() {
        window.onresize = () => {
            this.setTableSize()
        };
    }

    /**
     * 查询
     */
    doSearch() {
        this.page.currentPage = 1
        this.getList()
    }

    /**
     * 重置
     */
    doReset() {
        this.query = new RSGoodsFilter()
        this.getList()
        for (let item of this.queryData) {
            this.$refs.dataTable.toggleRowExpansion(item, false)
        }
    }

    /**
     * 分页页码改变的回调
     * @param val
     */
    onHandleCurrentChange(val: number) {
        this.page.currentPage = val
        this.getList()
    }

    /**
     * 每页多少条的回调
     * @param val
     */
    onHandleSizeChange(val: number) {
        this.page.size = val
        this.getList()
    }

    private getList() {
        this.query.page = this.page.currentPage - 1
        this.query.pageSize = this.page.size
        GoodsApi.query(this.query).then((resp: any) => {
            if (resp && resp.code === 2000) {
                this.queryData = resp.data
                this.page.total = resp.total
                this.setTableSize()
            }
        }).catch((error) => {
            if (error && error.message) {
                this.$message.error(error.message)
            }
        })
    }

    private setTableSize() {
        let table = document.getElementsByClassName('current-page')[0] as any
        if (table) {
            this.tableHeight = table.offsetHeight - 200
        }
    }
}
