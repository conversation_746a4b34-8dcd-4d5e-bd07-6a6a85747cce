/*
 * @Author: 黎钰龙
 * @Date: 2022-12-01 16:23:27
 * @LastEditTime: 2022-12-02 11:00:17
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\open-promotion\dialog\CouponAppletDialogFilter.ts
 * 记得注释
 */
export default class CouponAppletDialogFilter {
  // 名称类似于
  nameLike: Nullable<string> = null
  // 活动号类似于
  numberLike: Nullable<string> = null
  // 状态类似于：INITAIL——未审核；UNSTART——未开始；PROCESSING——进行中；STOPED——已结束
  stateEquals: Nullable<string> = null
  // 页数>=0
  page: number = 0
  // 页面大小>0
  pageSize: number = 0
}