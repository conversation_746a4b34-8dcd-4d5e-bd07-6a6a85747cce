<template>
  <div class="member-birthday-home">
    <BreadCrume :panelArray="panelArray"></BreadCrume>
    <div class="content">
      <div class="item" v-if="hasOptionPermission('/营销/营销/节日有礼/生日积分加倍', '规则查看')">
        <div class="title">{{ formatI18n('/权益/生日权益初始化/生日权益初始化', '生日积分加倍') }}</div>
        <div class="desc">
          • {{ formatI18n('/权益/生日权益初始化/生日权益初始化', '权益生效时间') }}
          <span v-if="pointsRule && pointsRule.effectType==='BY_DAY'">
            {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/权益生效时间', '生日当天') }}
          </span>
          <span v-else-if="pointsRule&& pointsRule.effectType==='BY_WEEK'">
            {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/权益生效时间', '生日当周') }}
          </span>
          <span v-else-if="pointsRule&& pointsRule.effectType==='BY_MONTH'">
            {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/权益生效时间', '生日当月') }}
          </span>
          <span v-else>-</span>
        </div>
        <div class="desc">
          • {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍', '会员生日权益') }}：
          <span v-if="pointsRule && pointsRule.samePointsTimes">{{
              formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/礼包设置/新建/享')
            }}{{ pointsRule.samePointsTimes }}
            {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/礼包设置/新建/倍积分') }}
          </span>
          <span v-else-if="pointsRule && pointsRule.differentPointsTimes">{{
              formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/会员生日权益', '不同等级会员享不同权益')
            }}</span>
          <span v-else>-</span>
        </div>
        <div class="desc">
          • {{ formatI18n('/权益/生日权益初始化/生日权益初始化/首页/生日积分加倍/每人每个生效时段得权益次数：') }}
          <span v-if="pointsRule && pointsRule.memberMaxGainPointsTimes">
            {{ limitNumber(pointsRule.memberMaxGainPointsTimes) }}
          </span>
          <span v-else>{{ formatI18n('/权益/生日权益初始化/生日权益初始化/首页/生日积分加倍/不限制') }}</span>
        </div>
        <div class="btn" style="position: absolute;bottom: 20px">
          <el-button type="primary" @click="navigateTo('points')" v-if="pointsRule && hasOptionPermission('/营销/营销/节日有礼/生日积分加倍', '规则查看')">
            <span>{{ formatI18n('/权益/生日权益初始化/生日权益初始化', '查看详情') }}</span>
          </el-button>
          <el-button type="primary" @click="navigateTo('points')" v-if="!pointsRule && hasOptionPermission('/营销/营销/节日有礼/生日积分加倍', '规则维护')">
            <span>{{ formatI18n('/权益/生日权益初始化/生日权益初始化', '去完善') }}</span>
          </el-button>
        </div>
      </div>
      <div class="item" v-if="hasOptionPermission('/营销/营销/节日有礼/生日送礼', '规则查看')">
        <div class="title">{{ formatI18n('/权益/生日权益初始化/生日权益初始化/title', '生日送礼') }}</div>
        <div class="desc" v-if="giftRule">
          • {{ formatI18n('/权益/生日权益初始化/生日权益初始化', '权益生效时间') }}{{ sendDayI18n }}
        </div>
        <div v-if="giftRule">
          <div class="desc">
            <div v-if="giftRule && giftRule.differentGiftBag">
              • {{ formatI18n('/权益/生日权益初始化/生日权益初始化', '礼包设置') }}
              {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/首页/不同等级会员享不同礼包') }}
            </div>
            <div v-else-if="giftRule && giftRule.allSameGiftBag">
              <div style="display: flex">
                <div>• {{ formatI18n('/权益/生日权益初始化/生日权益初始化', '礼包设置') }}</div>
                <div style="flex: 1;width: 70%">
                  <div v-if="giftRule && giftRule.allSameGiftBag">
                    <div>{{ getAllSameGiftBagPoints }}</div>
                    <div :title="getAllSameGiftBagCoupons" style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">
                      {{ getAllSameGiftBagCoupons }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="desc" v-if="!giftRule">
          • {{formatI18n('/权益/生日权益初始化/生日权益初始化/支持自定义送赠礼时间')}}
        </div>
        <div class="btn" style="position: absolute;bottom: 20px">
          <el-button type="primary" @click="navigateTo('gift')" v-if="giftRule && hasOptionPermission('/营销/营销/节日有礼/生日送礼', '规则查看')">
            <span>
              {{ formatI18n('/权益/生日权益初始化/生日权益初始化', '查看详情') }}
            </span>
          </el-button>
          <el-button type="primary" @click="navigateTo('gift')" v-if="!giftRule && hasOptionPermission('/营销/营销/节日有礼/生日送礼', '规则维护')">
            <span>{{ formatI18n('/权益/生日权益初始化/生日权益初始化', '去完善') }}</span>
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./BirthdayHome.ts">
</script>

<style scoped lang="scss">
.member-birthday-home {
  background-color: white;
  overflow: auto;
  height: 100%;
  width: 100%;

  .subTitle {
    font-size: 16px;
    padding-top: 20px;
    padding-left: 30px;
  }

  .content {
    display: flex;
    margin: 30px;

    .item {
      width: 50%;
      flex: 1;
      margin-right: 20px;
      padding-left: 20px;
      padding-top: 20px;
      position: relative;
      background-color: #eeeff1;
      padding-bottom: 90px;
      width: 50%;

      &:last-child {
        margin-right: 0px;
      }

      .title {
        font-size: 16px;
        font-weight: 500;
        padding-bottom: 20px;
      }

      .desc {
        margin: 10px;
        white-space: normal;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .btn {
        margin-top: 12px;
      }
    }
  }

  .el-switch__core {
    background-color: grey;
  }

  .el-switch.is-checked .el-switch__core {
    background-color: #33cc00;
  }
}
</style>