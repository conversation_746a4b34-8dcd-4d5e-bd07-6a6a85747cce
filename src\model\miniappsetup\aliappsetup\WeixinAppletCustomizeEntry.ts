// 微信小程序功能入口
export default class WeixinAppletCustomizeEntry  {
  // 入口类型；APPLET——小程序跳转；SUBPAGE——子页面
  type: Nullable<string> = null
  // 标题
  title: Nullable<string> = null
  // 介绍
  remark: Nullable<string> = null
  // 图片
  image: Nullable<string> = null
  // 子页面图片
  subpageImage: Nullable<string> = null
  // 目标小程序appId
  appId: Nullable<string> = null
  // 目标小程序路径
  path: Nullable<string> = null
  // 排序
  sort: Nullable<number> = null
  // 
  state: Nullable<string> = null
  // 
  customizeType: Nullable<string> = null
  // 
  style: Nullable<number> = null
}