<template>
  <div
    class="page-top"
    :style="{
      padding:
        localProperty.styMarginTop +
        'px ' +
        localProperty.styMarginRight +
        'px ' +
        localProperty.styMarginBottom +
        'px ' +
        localProperty.styMarginLeft +
        'px',
    }"
    :class="[{ activeCom: activeIndex === index }]"
    @click="activeTemplate"
  >
    <div class="toolBar" v-if="activeIndex === index">
      <setting-toolbar :btns="toolBarBtns" @click="toolbarClick"></setting-toolbar>
    </div>
    <!-- <div class="page-top-bg" :style="{ backgroundImage: `url(${bgUrl})` }">
      <p class="page-top-title">{{ localProperty.placeTitle }}</p>
    </div> -->
    <div class="double-column-image-widget">
      <div class="defaul-image" v-for="item in imgList" :key="item.id">
        <img v-if="!item.imageUrl" src="@/assets/image/icons/cutting_pic_empty.png" style="width: 84px; height: 72px" />
        <div class="no-image-tip" v-if="!item.imageUrl">{{ i18n('请在右侧添加图片') }}</div>
        <el-image style="width: 100%;" :src="item.imageUrl" fit="contain" v-if="item.imageUrl"></el-image>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./DoubleColumnImage.ts"></script>

<style lang="scss" scoped>
.page-top {
  width: 100%;
  position: relative;
  margin-bottom: 15px;
  background: white;
  &-bg {
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-size: 100%;
  }
  &-title {
    text-align: center;
    padding-top: 42px;
    font-size: 20px;
    color: #fff;
  }
  .toolBar {
    position: absolute;
    top: 0;
    right: 1px;
    z-index: 999;
    cursor: pointer;
  }
  .double-column-image-widget {
    width: 100%;
    display: flex;
    .defaul-image {
      width: 50%;
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      text-align: center;
      img {
        margin-top: 25px;
      }
      .no-image-tip {
        width: 100%;
        margin-top: 12px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #a1a6ae;
      }
    }
  }
}
</style>
