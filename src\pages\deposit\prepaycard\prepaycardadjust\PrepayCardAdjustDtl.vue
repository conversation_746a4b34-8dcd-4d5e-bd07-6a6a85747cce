<template>
  <div class="prepay-card-adjust-dtl">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button key="1" type="primary" @click="doAudit"
          v-if="billDtl.state !== 'AUDITED' && hasOptionPermission('/卡/卡管理/预付卡调整单', '单据审核')">审核</el-button>
        <el-button key="2" @click="doModify"
          v-if="billDtl.source === 'create' && billDtl.state !== 'AUDITED' && hasOptionPermission('/卡/卡管理/预付卡调整单', '单据维护')">修改</el-button>
      </template>
    </BreadCrume>
    <div class="top-wrap">
      <div class="left">
        <div class="back">
          <img src="~assets/image/storevalue/back.png">
        </div>
      </div>
      <div class="right">
        <div class="top">
          <div class="item1">
            <div class="bill"><span>单号：</span>{{billDtl.billNumber}}</div>
            <div class="name">预付卡调整单</div>
          </div>
          <div class="item2">
            <div class="desc">状态</div>
            <div class="state">
              <el-tag type="success" v-if="billDtl.state === 'AUDITED'">{{i18n('已审核')}}</el-tag>
              <el-tag type="warning" v-if="billDtl.state !== 'AUDITED'">{{i18n('未审核')}}</el-tag>
            </div>
          </div>
        </div>
        <div class="bottom">
          <div class="account-info">
            <div>
              <i18n k="/储值/预付卡/预付卡调整单/预付卡调整单详情/增加卡余额{0}元">
                <template slot="0">
                  <span style="color: red">{{billDtl.detailRemark[0] | fmt}}</span>
                </template>
              </i18n>,
              <i18n k="/储值/预付卡/预付卡调整单/预付卡调整单详情/扣减卡余额{0}元">
                <template slot="0">
                  <span style="color: green">{{billDtl.detailRemark[1] | fmt}}</span>
                </template>
              </i18n>
            </div>
            <div>
              <i18n k="/储值/预付卡/预付卡调整单/预付卡调整单详情/增加卡次数{0}次">
                <template slot="0">
                  <span style="color: red">
                    <template v-if="billDtl.countingCardDetailRemark">
                      {{Number(billDtl.countingCardDetailRemark[0]).toFixed(0)}}
                    </template>
                    <template v-else>-</template>
                  </span>
                </template>
              </i18n>,
              <i18n k="/储值/预付卡/预付卡调整单/预付卡调整单详情/扣减卡次数{0}次">
                <template slot="0">
                  <span style="color: green">
                    <template v-if="billDtl.countingCardDetailRemark">
                      {{Number(billDtl.countingCardDetailRemark[1]).toFixed(0)}}
                    </template>
                    <template v-else>-</template>
                  </span>
                </template>
              </i18n>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div style=" height: 20px;background-color: rgba(242, 242, 242, 1);"></div>
    <div class="center-wrap">
      <div>
        <p class="sub-item">调整明细</p>
        <el-table :data="queryDtl">
          <el-table-column align="left" fixed label="序号" prop="lineNo" width="140">
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.lineNo}}</span>
            </template>
          </el-table-column>
          <el-table-column align="left" fixed label="卡号" prop="accountOwner" width="200">
            <template slot-scope="scope">
              <span v-if="scope.row.accountOwner && scope.row.accountOwner.id" no-i18n>{{scope.row.accountOwner.id}}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="switchFlag" align="left" fixed label="账户类型" prop="accountType" width="140">
            <template slot-scope="scope">
              <span v-if="scope.row.accountType && scope.row.accountType.id && scope.row.accountType.name"
                no-i18n>[{{scope.row.accountType.id}}]{{scope.row.accountType.name}}</span>
            </template>
          </el-table-column>
          <el-table-column align="left" fixed label="卡类型" prop="cardType" width="140">
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.cardType === 'CountingCard' ? i18n('次卡') : scope.row.cardType}}</span>
            </template>
          </el-table-column>
          <el-table-column align="right" label="调整前卡余额" prop="occurAmount" width="140">
            <template slot-scope="scope">
              <span no-i18n>{{(Number(scope.row.oldAmount) + Number(scope.row.oldGiftAmount)) | fmt}}</span>
            </template>
          </el-table-column>
          <el-table-column align="right" label="调整前实充余额" prop="occurAmount" width="140">
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.oldAmount | fmt}}</span>
            </template>
          </el-table-column>
          <el-table-column align="right" label="调整前返现余额" prop="oldGiftAmount" width="140">
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.oldGiftAmount | fmt}}</span>
            </template>
          </el-table-column>
          <el-table-column align="right" label="卡余额调整" prop="totalAmount" width="140">
            <template slot-scope="scope">
              <div no-i18n style="color: red" v-if="Number(scope.row.totalAmount) > 0">+{{scope.row.totalAmount | fmt}}</div>
              <div no-i18n style="color: green" v-else>{{scope.row.totalAmount | fmt}}</div>
            </template>
          </el-table-column>
          <el-table-column align="right" label="实充调整" prop="occurAmount" width="140">
            <template slot-scope="scope">
              <div no-i18n style="color: red" v-if="Number(scope.row.occurAmount) > 0">+{{scope.row.occurAmount | fmt}}</div>
              <div no-i18n style="color: green" v-else>{{scope.row.occurAmount | fmt}}</div>
            </template>
          </el-table-column>
          <el-table-column align="right" label="返现调整" prop="occurGiftAmount" width="140">
            <template slot-scope="scope">
              <div no-i18n style="color: red" v-if="Number(scope.row.occurGiftAmount) > 0">+{{scope.row.occurGiftAmount | fmt}}</div>
              <div no-i18n style="color: green" v-else>{{scope.row.occurGiftAmount | fmt}}</div>
            </template>
          </el-table-column>
          <el-table-column align="left" label="发生组织" prop="reason" width="180" v-if="showOrg">
            <template slot-scope="scope">
              <div no-i18n :title="$options.filters.idName(scope.row.occurredOrg)"
                style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">{{scope.row.occurredOrg | idName}}</div>
            </template>
          </el-table-column>
          <el-table-column align="right" label="调整原因" prop="reason" width="140">
            <template slot-scope="scope">
              <div no-i18n :title="scope.row.reason" style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">
                {{scope.row.reason | strFormat}}</div>
            </template>
          </el-table-column>
          <el-table-column align="right" label="说明" prop="remark">
            <template slot-scope="scope">
              <div no-i18n :title="scope.row.remark" style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">
                {{scope.row.remark | strFormat}}</div>
            </template>
          </el-table-column>
        </el-table>
        <div style="margin-top: 15px">
          <el-pagination no-18n :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
            @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background layout="total, prev, pager, next, sizes,  jumper">
          </el-pagination>
        </div>
      </div>
    </div>
    <div class="row-height"></div>
    <div class="foot-wrap">
      <div>
        <p class="sub-item">操作日志</p>
        <el-table :data="billDtl.logs">
          <el-table-column label="操作类型" prop="type">
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.type}}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作人" prop="operator">
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.operator}}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作时间" prop="occurredTime">
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.occurredTime | dateFormate3}}</span>
            </template>
          </el-table-column>

        </el-table>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./PrepayCardAdjustDtl.ts">
</script>

<style lang="scss">
.prepay-card-adjust-dtl {
  width: 100%;
  height: 100%;
  background-color: white;
  overflow: auto;
  .top-wrap {
    display: flex;
    flex-direction: row;
    .left {
      width: 80px;
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
      .back {
        width: 48px;
        height: 48px;
        border-radius: 100%;
        background-color: rgba(242, 242, 242, 1);
        img {
          width: 24px;
          height: 24px;
          position: relative;
          top: 13px;
          left: 12px;
        }
      }
    }
    .right {
      display: flex;
      flex: 1;
      flex-direction: column;
      position: relative;
      .top {
        display: flex;
        height: 105px;
        border-bottom: 1px solid rgba(242, 242, 242, 1);
        margin-right: 20px;
        .item1 {
          .bill {
            margin-top: 16px;
            color: rgba(51, 51, 51, 0.***************);
          }
          .name {
            font-weight: 500;
            margin-top: 8px;
            font-size: 20px;
          }
        }
        .item2 {
          padding-left: 70px;
          padding-top: 16px;
          .desc {
            color: rgba(51, 51, 51, 0.***************);
          }
          .state {
            font-weight: 500;
            margin-top: 8px;
            font-size: 20px;
          }
        }
      }
      .bottom {
        padding-bottom: 20px;
        .account-info {
          margin-top: 10px;
        }
        .red {
          color: red;
        }
        .green {
          color: #008000;
        }
      }
    }
  }
  .row-height {
    height: 20px;
    background-color: rgba(242, 242, 242, 1);
  }
  .center-wrap,
  .foot-wrap {
    padding: 20px;
  }
  .sub-item {
    font-size: 16px;
    padding-top: 20px;
    margin-bottom: 10px;
  }
  .el-table__body .el-table__row td {
    border-bottom: 1px solid #d7dfeb !important;
  }
}
</style>