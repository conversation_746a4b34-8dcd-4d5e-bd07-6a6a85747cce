/*
 * @Author: 黎钰龙
 * @Date: 2023-10-11 14:23:35
 * @LastEditTime: 2023-12-12 13:51:07
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\prepaycard\sale-card\SaleCardDtl.ts
 * 记得注释
 */
import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import FormItem from 'cmp/formitem/FormItem';
import I18nPage from 'common/I18nDecorator';
import SaleCardBillApi from 'http/prepay/card/SaleCardBillApi';
import ConstantMgr from 'mgr/ConstantMgr';
import RoutePermissionMgr from 'mgr/RoutePermissionMgr';
import CardTemplate from 'model/card/template/CardTemplate';
import SaleCardBill from 'model/prepay/card/SaleCardBill';
import { Component, Vue } from 'vue-property-decorator';
import CardTplItem from '../cmp/cardtplitem/CardTplItem';
import UploadApi from 'http/upload/UploadApi';
import CardTemplateApi from 'http/card/template/CardTemplateApi';
@Component({
  name: 'SaleCardDtl',
  components: {
    BreadCrume,
    FormItem,
    CardTplItem
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/卡/卡管理/售卡单',
    '/储值/预付卡/充值卡制售单/列表页面',
    '/营销/券礼包活动/券礼包活动',
    '/会员/洞察/标签管理/列表页',
    '/储值/预付卡/预付卡充值单',
    '/会员/洞察/公共/最近消费属性',
    '/储值/预付卡/卡模板/编辑页面'
  ],
  auto: true
})
export default class SaleCardDtl extends Vue {
  dtl: SaleCardBill = new SaleCardBill()
  currentCardTemplate: CardTemplate = new CardTemplate()
  panelArray: any = [
    {
      name: this.i18n('售卡单'),
      url: 'sale-card-list'
    },
    {
      name: this.i18n('售卡单详情'),
      url: ''
    }
  ]

  created() {
    this.getDtl()
  }

  getDtl() {
    const loading = this.$loading(ConstantMgr.loadingOption)
    SaleCardBillApi.get(this.$route.query.billNumber as string).then((res) => {
      if (res.code === 2000) {
        this.dtl = res.data || new SaleCardBill()
        this.currentCardTemplate.number = res.data?.cardTemplateNumber
        this.getTemplateInfo()
      } else {
        this.$message.error(res.msg || this.i18n('查询单据详情失败'))
      }
    }).catch((error) => this.$message.error(error.message || this.i18n('内部异常')))
      .finally(() => loading.close())
  }

  getTemplateInfo() {
    CardTemplateApi.info(this.currentCardTemplate.number as any).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.currentCardTemplate = resp.data
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  goCardTemplateDtl() {
    RoutePermissionMgr.openBlank({ name: 'prepay-card-tpl-dtl', query: { number: this.dtl.cardTemplateNumber } })
  }

  gotoTplDtl() {
    RoutePermissionMgr.openBlank({ name: 'prepay-card-tpl-dtl', query: { number: this.currentCardTemplate?.number } })
  }

  doExport() {
    if (!this.dtl.downOssKey) return this.$message.error(this.i18n('当前单据缺少downOssKey'))
    UploadApi.getUrl(this.dtl.downOssKey).then((resp: any) => {
      if (resp && resp.data) {
        window.open(resp.data);
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  doAudit() {
    this.$confirm(
      this.i18n("确定审核当前售卡单吗？"),
      this.i18n("审核"),
      {
        confirmButtonText: this.formatI18n("/公用/按钮", "确定"),
        cancelButtonText: this.formatI18n("/公用/按钮", "取消"),
      }
    ).then(() => {
      SaleCardBillApi.audit(this.dtl.billNumber!).then((res) => {
        if (res.code === 2000) {
          this.$message.success(this.i18n('操作成功'))
          this.getDtl()
        } else {
          this.$message.error(res.msg || this.i18n('操作失败'))
        }
      }).catch((error) => {
        this.$message.error(error.message || this.i18n('内部异常'))
      })
    });
  }

  doEdit() {
    this.$router.push({
      name: 'sale-card-edit',
      query: {
        editType: 'edit',
        billNumber: this.dtl.billNumber
      }
    })
  }

  doRemove() {
    this.$confirm(
      this.i18n("确定要删除当前售卡单吗？"),
      this.i18n("删除"),
      {
        confirmButtonText: this.formatI18n("/公用/按钮", "确定"),
        cancelButtonText: this.formatI18n("/公用/按钮", "取消"),
      }
    ).then(() => {
      SaleCardBillApi.remove(this.dtl.billNumber!).then((res) => {
        if (res.code === 2000) {
          this.$message.success(this.i18n('操作成功'))
          this.$router.push({
            name: 'sale-card-list'
          })
        } else {
          this.$message.error(res.msg || this.i18n('操作失败'))
        }
      }).catch((error) => {
        this.$message.error(error.message || this.i18n('操作失败'))
      })
    });
  }

  // 支付总金额
  get totalPayAmount() {
    if (this.dtl.payments.length > 0) {
      return this.dtl.payments.reduce((totalPayAmount, payment) => {
        if (payment.paymentAmount) {
          return (Number(totalPayAmount) + Number(payment.paymentAmount)).toFixed(2)
        }
        return totalPayAmount;
      }, 0)
    }
  }
};