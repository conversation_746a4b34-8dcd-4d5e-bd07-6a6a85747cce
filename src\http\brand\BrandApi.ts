import ApiClient from 'http/ApiClient'
import BrandTree from 'model/brand/Brand'
import RSBrand from 'model/common/RSBrand'
import RSBrandFilter from 'model/common/RSBrandFilter'
import RSSaveBatchBrandRequest from 'model/common/RSSaveBatchBrandRequest'
import Response from 'model/common/Response'

export default class BrandApi {
  /**
   * 查询品牌
   *
   */
  static query(body: RSBrandFilter): Promise<Response<RSBrand[]>> {
    return ApiClient.server().post(`/v1/brand/query`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询品牌
   *
   */
  static queryTree(body: RSBrandFilter): Promise<Response<BrandTree[]>> {
    return ApiClient.server().post(`/v1/brand/queryTree`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 删除品牌
   *
   */
  static remove(id: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/brand/remove`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量保存
   *
   */
  static saveBatch(body: RSSaveBatchBrandRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/brand/saveBatch`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
