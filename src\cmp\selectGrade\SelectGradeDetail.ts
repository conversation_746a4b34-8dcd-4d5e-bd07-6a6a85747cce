/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:19
 * @LastEditTime: 2023-04-23 16:47:51
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\selectGrade\SelectGradeDetail.ts
 * 记得注释
 */
import GradeApi from "http/grade/grade/GradeApi";
import GradesRange from "model/common/GradeRange";
import Grade from "model/grade/Grade";
import { Component, Prop, Vue, Watch } from "vue-property-decorator";

@Component({
  name: "SelectGradeDetail",
  components: {},
})
export default class SelectGradeDetail extends Vue {
  @Prop()
  value: GradesRange;

  memberLevel: Grade[] = [];
  $refs: any;
  valueText: any = "";
  @Watch("value", { deep: true, immediate: true })
  valueChange(val: GradesRange) {
    if (val) {
      if (val.type === "ALL") {
        this.valueText = this.formatI18n("/公用/公共组件/全部等级");
      } else {
        if (this.memberLevel.length == 0) {
          this.getGradeList().then(() => {
            this.getParticalLevel();
          });
        } else {
          this.getParticalLevel();
        }
      }
    }
  }
  created() {
    this.getGradeList();
  }

  getGradeList() {
    return GradeApi.listGrade("")
      .then((resp: any) => {
        this.memberLevel = resp.data;
      })
      .catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
  }

  getParticalLevel() {
    let valuetext = "";
    for (const item of this.memberLevel) {
      for (const item1 of this.value.gradeCodes) {
        if (item1 === item.code) {
          valuetext += `[${item1}]${item.name}；`;
        }
      }
    }
    this.valueText = valuetext.slice(0, valuetext.length - 1);
  }
}
