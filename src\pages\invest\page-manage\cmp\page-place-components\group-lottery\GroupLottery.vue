<template>
  <div class="group-lottery-container"
    :style="{ padding: localProperty.styMarginTop + 'px ' + localProperty.styMarginRight + 'px ' + localProperty.styMarginBottom + 'px ' + localProperty.styMarginLeft + 'px' }"
    @click="activeTemplate">
    <div class="toolBar" v-if="activeIndex === index">
      <setting-toolbar :btns="toolBarBtns" @click="toolbarClick"></setting-toolbar>
    </div>
    <div class="container">
      <el-image class="top" :src="require('@/assets/image/fellow/img_choujiangtuan.png')"></el-image>
      <div class="container-bottom">
        <div class="container-bottom-top">
          <div class="container-bottom-label">{{ i18n('/公用/过滤器/进行中') }}</div>
            <div class="container-bottom-title">{{ i18n('五一抽奖活动五一抽奖活动五一抽奖活动') }}</div>
        </div>
        <div class="container-bottom-center">
            {{ i18n('距拼团结束') }}<span class="container-bottom-center-tag">23</span>{{ i18n('天') }}<span  class="container-bottom-center-tag">08</span>:<span  class="container-bottom-center-tag">08</span>:<span  class="container-bottom-center-tag">08</span>
        </div>
        <div class="container-bottom-bottom">
          <div  class="container-bottom-bottom-tag">{{ i18n('100人团') }}</div>
          <div class="container-bottom-bottom-right">
            <div class="first">{{ i18n('100积分') }}</div>
            <el-image class="image" :src="require('@/assets/image/fellow/img_choujiangtuan_icon.png')"></el-image>
            <div class="second">{{ i18n('去拼团') }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./GroupLottery.ts">
</script>

<style lang="scss" scoped>
.group-lottery-container {
  width: 100%;
  min-height: 211px;
  background: #f9f9f9;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  position: relative;
  &-bg {
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-size: 100%;
  }

  &-title {
    text-align: center;
    padding-top: 42px;
    font-size: 20px;
    color: #fff;
  }

  .container {
    width: 351px;
    height: 242px;
    background: #ffffff;
    border-radius: 12px;
    display: block;
    margin: 0 auto;
    position: relative;

    .top {
      width: 351px;
      height: 242px;
    }

    &-bottom {
      position: absolute;
      top: 140px;
      left: 3px;
      width: 100%;
      padding: 10px 18px;
      height: 100px;

      &-label {
        text-align: center;
        width: auto;
        height: 14px;
        background: #0075FF;
        border-radius: 3px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 10px;
        color: #FFFFFF;
        line-height: 14px;
        font-style: normal;
        margin-right: 6px;
        margin-top: 3px;
        padding: 0 3px;
      }

      &-top {
        display: flex;
      }

      &-title {
        line-height: 20px;
        font-weight: bold;
        font-size: 15px;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      &-center {
        margin-top: 6px;
        &-tag {
          background-color: #fdebed;
          color: #ff7c7d;
          padding: 2px;
          border-radius: 3px;
          margin: 0 3px;
          font-weight: bold;
        }
      }

      &-bottom {
        display: flex;
        margin-top: 10px;
        justify-content: space-between;

        &-tag {
          color: #ff7c7d;
          font-weight: bold;
          font-size: 15px;
          max-width: 100px;
        }

        &-right {
          display: flex;
          // background-color: #fdebed;
          // color: #ff7c7d;
          height: 24px;
          line-height: 24px;
          position: relative;
          min-width: 150px;
          top: -5px;
          // padding:0 8px;
          border-radius: 12px;
          text-align: center;

          .first{
            // background-color: #ff9139;
            // color: #ff7c7d;
            width: 100%;
            background-color: #fdebed;
            color: #ff7c7d;
            padding-left: 8px;
            border-top-left-radius: 12px;
            border-bottom-left-radius: 12px;
          }

          .second {
            width: 100%;
            background-color: #ff5230;
            color: #fff;
            padding-right: 8px;
            border-top-right-radius: 12px;
            border-bottom-right-radius: 12px;
          }
        }
      }
    }
  }
}
</style>