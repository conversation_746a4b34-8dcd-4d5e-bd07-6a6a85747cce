import { Component, Vue } from "vue-property-decorator";
import BreadCrume from "cmp/bread-crumb/BreadCrume.vue";
import FormItem from "cmp/formitem/FormItem.vue";
import ListWrapper from "cmp/list/ListWrapper.vue";
import QueryCondition from "cmp/querycondition/QueryCondition.vue";
import CouponActivityApi from "http/v2/coupon/CouponActivityApi";
import CouponActivityFilter from "model/v2/coupon/CouponActivityFilter";
import PermissionMgr from "mgr/PermissionMgr";
import DownloadCenterDialog from "pages/main/dowmload_center_dialog/DownloadCenterDialog.vue";

@Component({
	name: "ImproveProfiles",
	components: {
		BreadCrume,
    FormItem,
    ListWrapper,
    QueryCondition,
    DownloadCenterDialog
	},
})
export default class ImproveProfiles extends Vue {
  panelArray: any[] = []
  query: CouponActivityFilter = new CouponActivityFilter();
	selectedArr: any[] = [];
	selectDate = [];
	activeName = "first";
	singleAll = false;
	total = {
		all: 0,
		initial: 0,
		audit: 0,
		doing: 0,
		end: 0,
		suspend: 0
	};
	$refs: any;
	// 分页
	page = {
		currentPage: 1,
		total: 0,
		size: 10,
	};
	tableData: any[] = [];
  moduleConfigs: any = {};
	dialogvisiable = false;
  typeList: any[] = ['IMPROVE_PROFILES_GIFT']
  get getAllCount() {
		return `${this.formatI18n("/公用/下拉框/提示", "全部")}(${this.total.all})`;
	}

	get getNoAudit() {
		return `${this.formatI18n("/营销/券礼包活动/券礼包活动", "未审核")}(${this.total.initial})`;
	}

	get getNoStart() {
		return `${this.formatI18n("/营销/券礼包活动/券礼包活动", "未开始")}(${this.total.audit})`;
	}

	get getDoing() {
		return `${this.formatI18n("/营销/券礼包活动/券礼包活动", "进行中")}(${this.total.doing})`;
	}

	get getSuspend() {
    return `${this.formatI18n("/公用/过滤器", "暂停中")}(${this.total.suspend})`;
	}

	get getEnd() {
		return `${this.formatI18n("/营销/券礼包活动/券礼包活动", "已结束")}(${this.total.end})`;
	}

	get getShowPause() {
		return this.$route.name === 'wechat-receive-coupon'
	}

	get getSelectActive() {
		let str: any = this.formatI18n("/营销/券礼包活动/券礼包活动", "已选择{0}个活动");
		str = str.replace(/\{0\}/g, this.selectedArr.length);
		return str;
	}

  created() {
		this.panelArray = [
			{
				name: this.formatI18n("/公用/菜单", "完善资料有礼"),
				url: "",
			},
		];
    this.getStoreValueList()
    this.initModuleConfig()
  }

  doAdd() {
    this.$router.push({ name: 'improveProfiles-add' })
  }
	doDownloadDialogClose() {
		this.dialogvisiable = false;
	}

	checkedAllRow() {
		if (this.singleAll) {
			for (let row of this.tableData) {
				this.$refs.table.toggleRowSelection(row, true);
			}
		} else {
			this.$refs.table.clearSelection();
		}
	}

	doBatchDelete() {
		if (this.selectedArr.length <= 0) {
			this.$message.warning(this.formatI18n("/营销/券礼包活动/券礼包活动", "请先勾选要删除的单据") as string);
			return;
		}
		this.$confirm(this.formatI18n("/营销/券礼包活动/券礼包活动", "是否批量删除这些单据?") as string, "批量删除", {
			confirmButtonText: this.formatI18n("/公用/按钮", "确定") as string,
			cancelButtonText: this.formatI18n("/公用/按钮", "取消") as string,
		}).then(() => {
			this.submitBatchDelete();
		});
	}

	doBatchAudit() {
		if (this.selectedArr.length <= 0) {
			this.$message.warning(this.formatI18n("/营销/券礼包活动/券礼包活动", "请先勾选要审核的单据") as string);
			return;
		}
		this.$confirm(
			this.formatI18n("/营销/券礼包活动/券礼包活动", "是否批量审核这些单据?") as string,
			this.formatI18n("/营销/券礼包活动/券礼包活动", "批量审核") as string,
			{
				confirmButtonText: this.formatI18n("/公用/按钮", "确定") as string,
				cancelButtonText: this.formatI18n("/公用/按钮", "取消") as string,
			}
		).then(() => {
			this.submitBatchAudit();
		});
	}

	doBatchEnd() {
		if (this.selectedArr.length <= 0) {
			this.$message.warning(this.formatI18n("/营销/券礼包活动/券礼包活动", "请先勾选要终止的单据") as string);
			return;
		}
		this.$confirm(
			this.formatI18n("/营销/券礼包活动/券礼包活动", "是否批量终止这些单据?") as string,
			this.formatI18n("/营销/券礼包活动/券礼包活动", "批量终止") as string,
			{
				confirmButtonText: this.formatI18n("/公用/按钮", "确定") as string,
				cancelButtonText: this.formatI18n("/公用/按钮", "取消") as string,
			}
		).then(() => {
			this.submitBatchEnd();
		});
	}

	doHandleClick() {
		this.page.currentPage = 1;
		if (this.activeName === "first") {
			this.query.stateEquals = null;
		} else if (this.activeName === "second") {
			this.query.stateEquals = "INITAIL";
		} else if (this.activeName === "third") {
			this.query.stateEquals = "UNSTART";
		} else if (this.activeName === "forth") {
			this.query.stateEquals = "PROCESSING";
		} else if (this.activeName === "suspend") {
			this.query.stateEquals = "SUSPEND";
		} else {
			this.query.stateEquals = "STOPED";
		}
		this.getStoreValueList();
	}

	doAudit(row: any) {
		this.$confirm(this.formatI18n("/营销/券礼包活动/券礼包活动", "是否确定审核该单据?"), this.formatI18n("/公用/按钮", "审核"), {
			confirmButtonText: this.formatI18n("/公用/按钮", "确定") as any,
			cancelButtonText: this.formatI18n("/公用/按钮", "取消") as any,
		}).then(() => {
			if (row.type === "THIRD_CODE_ISSUE_COUPON") {
				// 核销第三方券
				if (row.fileUrl) {
					this.submitAudit(row.activityId);
				} else {
					this.$message.warning(
						this.formatI18n("/营销/券礼包活动/核销第三方券/活动详情没有导入过券码点击审核js提示信息", "未通过审核，活动尚未导入券号，审核失败！")
					);
				}
			} else {
				this.submitAuditNo(row.activityId);
			}
		});
	}

	doToDtl(row: any) {
		console.log(row);
		if (this.moduleConfigs[row.type]) {
			this.$router.push({ name: this.moduleConfigs[row.type].dtl, query: { id: row.activityId } });
		}
	}

	doModifyOrCopy(row: any, type: string) {
		if (this.moduleConfigs[row.type]) {
			if (type === "modify") {
				this.moduleConfigs[row.type].gotoModify(row);
			} else {
				this.moduleConfigs[row.type].gotoCopy(row);
			}
		}
	}

	doDelete(row: any) {
		this.$confirm(
			this.formatI18n("/营销/券礼包活动/券礼包活动", "是否确定删除该单据?") as string,
			this.formatI18n("/营销/券礼包活动/券礼包活动", "删除") as string,
			{
				confirmButtonText: this.formatI18n("/公用/按钮", "确定") as string,
				cancelButtonText: this.formatI18n("/公用/按钮", "取消") as string,
			}
		).then(() => {
			this.submitDelete(row.activityId);
		});
	}

	doStop(row: any) {
		this.$confirm(
			this.formatI18n("/营销/券礼包活动/券礼包活动", "是否确定终止该单据?") as string,
			this.formatI18n("/营销/券礼包活动/券礼包活动", "终止") as string,
			{
				confirmButtonText: this.formatI18n("/公用/按钮", "确定") as string,
				cancelButtonText: this.formatI18n("/公用/按钮", "取消") as string,
			}
		).then(() => {
			this.submitStop(row.activityId);
		});
	}

	doAbolish(row: any) {
		if (row.state === "PROCESSING" || row.state === 'SUSPEND') {
			CouponActivityApi.getCouponAbortSum(row.activityId)
				.then((resp: any) => {
					if (resp && resp.code === 2000) {
						if (resp.data.issueTotal === 0) {
							this.$message.warning(this.formatI18n("/营销/券礼包活动/券礼包活动", "活动已发券数为0，无需作废") as string);
						} else {
							this.$confirm(
								this.formatI18n("/营销/券礼包活动/券礼包活动", "作废券，相应的活动也将终止，确定要作废券（不含已使用券）吗？") as string,
								this.formatI18n("/公用/弹出模态框提示标题", "提示") as string,
								{
									confirmButtonText: this.formatI18n("/公用/按钮", "确定") as string,
									cancelButtonText: this.formatI18n("/公用/按钮", "取消") as string,
								}
							).then(() => {
								this.$message.success(this.formatI18n("/营销/券礼包活动/券礼包活动", "批量作废中，请稍后查看") as string);
								CouponActivityApi.abort(row.activityId)
									.then((resp: any) => {
										if (resp && resp.code === 2000) {
											this.getStoreValueList();
										}
									})
									.catch((error: any) => {
										if (error && error.message) {
											this.$message.error(error.message);
										}
									});
							});
						}
						// 发券张数＞0，弹框提示"作废券，相应的活动也将中终止，确定要作废券（不含已使用券）吗？"用户确认后，toast提示"批量作废中，请稍后查看"，并调用作废券及终止活动接口，作废所有券及活动状态变为已结束；
					}
				})
				.catch((error: any) => {
					if (error && error.message) {
						this.$message.error(error.message);
					}
				});
		} else if (row.state === "STOPED") {
			CouponActivityApi.getCouponAbortSum(row.activityId)
				.then((resp: any) => {
					if (resp && resp.code === 2000) {
						if (resp.data.issueTotal === 0) {
							this.$message.warning(this.formatI18n("/营销/券礼包活动/券礼包活动", "活动已发券数为0，无需作废") as string);
						} else {
							let canStopNumber = resp.data.issueTotal - resp.data.abortTotal - resp.data.usedTotal;
							if (canStopNumber > 0) {
								let str = this.formatI18n("/营销/券礼包活动/券礼包活动/当前有{0}张券可作废，请确认是否作废券？");
								str = str.replace(/\{0\}/g, " " + canStopNumber + " ");
								this.$confirm(str, this.formatI18n("/公用/弹出模态框提示标题", "提示") as string, {
									confirmButtonText: this.formatI18n("/公用/按钮", "确定") as string,
									cancelButtonText: this.formatI18n("/公用/按钮", "取消") as string,
								}).then(() => {
									CouponActivityApi.abort(row.activityId)
										.then((resp: any) => {
											if (resp && resp.code === 2000) {
												this.getStoreValueList();
											}
										})
										.catch((error: any) => {
											if (error && error.message) {
												this.$message.error(error.message);
											}
										});
								});
							} else {
								let str = this.formatI18n("/营销/券礼包活动/券礼包活动/当前无可作废券，此活动券历史共计作废 {0} 张");
								str = str.replace(/\{0\}/g, resp.data.abortTotal);
								this.$confirm(str, "提示", {
									confirmButtonText: this.formatI18n("/公用/按钮", "确定") as string,
									cancelButtonText: this.formatI18n("/公用/按钮", "取消") as string,
								}).then(() => {
									// todo 什么都不用做
								});
							}
						}
					}
				})
				.catch((error: any) => {
					if (error && error.message) {
						this.$message.error(error.message);
					}
				});
			// 发券数量=0，toast提示"活动已发券数为0，无需作废"。
			// 发券张数＞0且当前无可作废券（注：可作废券数量=发券数量-已使用数量-已作废数量），弹框提示"当前无可作废券，此活动券历史共计作废 X 张"，用户点确定关闭弹框；
			// 发券张数＞0且当前有可作废券，弹框提示"当前有 Y 张券可作废，请确认是否作废券？"用户确认后，调用作废券接口。
		} else {
			// 其它
		}
	}


	/**
	 * 查询
	 */
	doSearch() {
		this.page.currentPage = 1;
		this.getStoreValueList();
	}

	handleSelectionChange(val: any) {
		this.selectedArr = val;
	}

	/**
	 * 重置
	 */
	doReset() {
		this.activeName = "first";
		this.selectDate = [];
		this.query = new CouponActivityFilter();
		this.getStoreValueList();
	}

	/**
	 * 分页页码改变的回调
	 * @param val
	 */
	onHandleCurrentChange(val: number) {
		this.page.currentPage = val;
		this.getStoreValueList();
	}

	/**
	 * 每页多少条的回调
	 * @param val
	 */
	onHandleSizeChange(val: number) {
		this.page.size = val;
		this.getStoreValueList();
	}


	getBatchAuditPermmsion() {
		for (let prop of this.typeList) {
			if (this.moduleConfigs[prop] && !this.moduleConfigs[prop].auditPermission) {
				return false;
			}
		}
		return true;
	}

	getBatchStopPermission() {
		for (let prop of this.typeList) {
			if (this.moduleConfigs[prop] && !this.moduleConfigs[prop].stopPermission) {
				return false;
			}
		}
		return true;
	}

	getBatchDeletePermission() {
		for (let prop of this.typeList) {
			if (this.moduleConfigs[prop] && !this.moduleConfigs[prop].editPermission) {
				return false;
			}
		}
		return true;
	}

	hasCheckPermission(value: string) {
		if (this.moduleConfigs[value]) {
			return this.moduleConfigs[value].viewPermission;
		}
		return false;
	}

	hasAuditPermission(value: string) {
		if (this.moduleConfigs[value]) {
			return this.moduleConfigs[value].auditPermission;
		}
		return false;
	}

	hasCopyPermission(value: string) {
		if (this.moduleConfigs[value]) {
			return this.moduleConfigs[value].editPermission;
		}
		return false;
	}

	hasStopPermission(value: string) {
		if (this.moduleConfigs[value]) {
			return this.moduleConfigs[value].stopPermission;
		}
		return false;
	}

	private getStoreValueList() {
		if (this.selectDate && this.selectDate.length > 0) {
			this.query.begin = this.selectDate[0];
			this.query.end = this.selectDate[1];
		} else {
			this.query.begin = null;
			this.query.end = null;
		}
		;(this.query.groupType as any) = 'IMPROVE_PROFILES_GIFT';
		this.query.page = this.page.currentPage - 1;
		this.query.pageSize = this.page.size;
		CouponActivityApi.query(this.query)
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					this.tableData = resp.data.list;
					this.page.total = resp.total;
					this.total.all = resp.data.summary.sum;
					this.total.initial = resp.data.summary.initail;
					this.total.audit = resp.data.summary.unstart;
					this.total.doing = resp.data.summary.processing;
					this.total.end = resp.data.summary.stoped;
					this.total.suspend = resp.data.summary.suspend
				}
			})
			.catch((error) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}

	private submitBatchDelete() {
		let ids: string[] = [];
		if (this.selectedArr && this.selectedArr.length > 0) {
			this.selectedArr.forEach((item) => {
				ids.push(item.activityId);
			});
		}
		CouponActivityApi.batchRemove(ids)
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					this.$message.success(resp.data);
					this.getStoreValueList();
					this.singleAll = false;
				}
			})
			.catch((error) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}

	private submitBatchAudit() {
		let ids: string[] = [];
		let isThird = false;
		if (this.selectedArr && this.selectedArr.length > 0) {
			this.selectedArr.forEach((item) => {
				if (item.type === "THIRD_CODE_ISSUE_COUPON") {
					isThird = true;
				}
				ids.push(item.activityId);
			});
		}
		CouponActivityApi.batchAudit(ids)
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					this.$message.success(resp.data);
					this.getStoreValueList();
					this.singleAll = false;
					isThird && (this.dialogvisiable = true);
				}
			})
			.catch((error) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}

	private submitBatchEnd() {
		let ids: string[] = [];
		if (this.selectedArr && this.selectedArr.length > 0) {
			this.selectedArr.forEach((item) => {
				ids.push(item.activityId);
			});
		}
		CouponActivityApi.batchStop(ids)
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					this.$message.success(resp.data);
					this.getStoreValueList();
					this.singleAll = false;
				} else {
					this.$message.error(resp.msg);
				}
			})
			.catch((error) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}

	private submitAudit(uuid: string) {
		CouponActivityApi.audit(uuid)
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					this.$message.success(this.formatI18n("/营销/券礼包活动/券礼包活动", "审核成功") as string);
					this.getStoreValueList();
					this.dialogvisiable = true;
				}
			})
			.catch((error) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}

	private submitAuditNo(uuid: string) {
		CouponActivityApi.audit(uuid)
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					this.$message.success(this.formatI18n("/营销/券礼包活动/券礼包活动", "审核成功") as string);
					this.getStoreValueList();
				} else {
					this.$message.error(resp.msg);
				}
			})
			.catch((error) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}

	private submitDelete(uuid: string) {
		CouponActivityApi.remove(uuid)
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					this.$message.success(this.formatI18n("/营销/券礼包活动/券礼包活动", "删除成功") as string);
					this.getStoreValueList();
				}
			})
			.catch((error) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}

	private submitStop(uuid: string) {
		CouponActivityApi.stop(uuid)
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					this.$message.success(this.formatI18n("/营销/券礼包活动/核销第三方券", "终止成功") as string);
					this.getStoreValueList();
				} else {
					this.$message.error(resp.msg);
				}
			})
			.catch((error) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}

	private initModuleConfig() {
		let normalGotoModify = (row: any, name: string) => this.$router.push({ name: name, query: { list: "true", id: row.activityId, from: "edit" } });
		let normalGotoCopy = (row: any, name: string) => this.$router.push({ name: name, query: { list: "true", id: row.activityId, from: "copy" } });
		this.moduleConfigs = {
      IMPROVE_PROFILES_GIFT: {
        dtl: "improveProfiles-dtl",
				gotoModify: (row: any) => normalGotoModify(row, "improveProfiles-add"),
				gotoCopy: (row: any) => normalGotoCopy(row, "improveProfiles-add"),
				viewPermission: PermissionMgr.hasOptionPermission("/营销/营销/会员成长/完善资料有礼", "活动查看"),
				auditPermission: PermissionMgr.hasOptionPermission("/营销/营销/会员成长/完善资料有礼", "活动审核"),
				editPermission: PermissionMgr.hasOptionPermission("/营销/营销/会员成长/完善资料有礼", "活动维护"),
				stopPermission: PermissionMgr.hasOptionPermission("/营销/营销/会员成长/完善资料有礼", "活动终止"),
				suspendPermission: PermissionMgr.hasOptionPermission("/营销/营销/会员成长/完善资料有礼", "活动暂停"),
      }
		};
	}

	// isPointActivity(type: any) {
	// 	if (
	// 		type === "GAIN_POINTS_GOODS" ||
	// 		type === "GOODS_GAIN_ADDITIONAL_POINTS_BY_QTY" ||
	// 		type === "GOODS_GROUP_GAIN_ADDITIONAL_POINTS_BY_QTY" ||
	// 		type === "GOODS_GAIN_ADDITIONAL_POINTS" ||
	// 		type === "GOODS_GAIN_POINTS_SPEED" ||
	// 		type === "USE_COUPON_ISSUE_GIFT"
	// 	) {
	// 		return true;
	// 	} else {
	// 		return false;
	// 	}
	// }


	getStopPermission(value: string) {
		if (value) {
      let str: boolean = false;
			str = PermissionMgr.hasOptionPermission("/营销/营销/营销工具/积分兑换商品", "活动终止");
			return str;
		}
	}

	getCopyPermission(value: string) {
		if (value) {
			let str: boolean = false;
			str = PermissionMgr.hasOptionPermission("/营销/营销/营销工具/积分兑换商品", "活动维护");
			return str;
		}
	}

	doDtl(row: any) {
		this.$router.push({ name: "improveProfiles-dtl", query: { id: row.activityId } });
	}

	doCopy(row: any) {
		this.$router.push({ name: "improveProfiles-add", query: { id: row.activityId, editType: "copy" } });
	}

	doModify(row: any) {
		this.$router.push({ name: "improveProfiles-add", query: { id: row.activityId, editType: "modify" } });
	}

}