import {Component, Vue} from 'vue-property-decorator'
import ListWrapper from 'cmp/list/ListWrapper.vue'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import MakeSaleCardBill from 'model/card/makesalecardbill/MakeSaleCardBill';
import MakeSaleCardBillFilter from 'model/card/makesalecardbill/MakeSaleCardBillFilter';
import MakeSaleCardBillApi from 'http/card/makesalebill/MakeSaleCardBillApi';
import DataUtil from '../common/DataUtil';
import DateUtil from 'util/DateUtil';
import I18nPage from "common/I18nDecorator";
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import PrepayCardTplPermission from "pages/deposit/prepaycard/prepaycardtpl/PrepayCardTplPermission";
import ConstantMgr from "mgr/ConstantMgr";
import SaleCardBillApi from "http/prepay/card/SaleCardBillApi";

@Component({
  name: 'MakeSaleCardBill',
  components: {
    ListWrapper,
    SubHeader,
    BreadCrume
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/公用/查询条件/提示',
    '/储值/预付卡/卡模板/编辑页面',
    '/公用/活动/状态',
    '/储值/预付卡/卡模板/编辑页面',
    '/储值/预付卡/充值卡制售单/列表页面',
    '/公用/按钮',
    '/公用/提示',
  ],
})
export default class MakeSaleCardBillList extends Vue {
  i18n: any
  query: MakeSaleCardBillFilter = new MakeSaleCardBillFilter()
  queryData: MakeSaleCardBill[] = []
  prepayCardTplPermission = new PrepayCardTplPermission()
  dataUtil: DataUtil = new DataUtil()
  panelArray: any = []
  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }

  created() {
    this.panelArray = [
      {
        name: this.formatI18n('/储值/预付卡/充值卡制售单/列表页面/制售单'),
        url: ''
      }
    ]
    this.getList()
  }

  doSearch() {
    this.getList()
  }

  doReset() {
    this.query = new MakeSaleCardBillFilter()
    this.page.currentPage = 1
    this.getList()
  }

  /**
   * 查询
   */
  onSearch() {
    this.page.currentPage = 1
    this.getList()
  }

  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.getList()
  }

  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.getList()
  }

  private getList() {
    this.query.page = this.page.currentPage - 1
    this.query.pageSize = this.page.size
    MakeSaleCardBillApi.query(this.query).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.queryData = resp.data
        this.page.total = resp.total
      } else {
        throw new Error(resp.msg)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private gotoDtl(row: MakeSaleCardBill) {
    this.$router.push({name: 'make-sale-card-bill-dtl', query: {number: row.billNumber}})
  }

  private gotoTplDtl(row: MakeSaleCardBill) {
    let type = ''
    switch (row.cardType) {
      case 'GiftCard':
        type = 'GIFT_CARD'
        break;
      case 'RechargeableCard':
        type = 'RECHARGEABLE_CARD'
        break;
      case 'ImprestCard':
        type = 'IMPREST_CARD'
        break;
      case 'CountingCard':
        type = 'COUNTING_CARD'
        break;
      default:
        return
    }
    this.$router.push({ name: 'prepay-card-tpl-dtl', query: { number: row.cardTemplateNumber, cardTemplateType: type }})
  }

  private audit(billNumber: string) {
    const loading = this.$loading(ConstantMgr.loadingOption)
    return MakeSaleCardBillApi.audit(billNumber).then((resp:any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(this.i18n('操作成功'))
        this.getList()
      } else {
        this.$message.error(resp.msg || this.i18n('操作失败'))
      }
    }).catch((error) => {
      this.$message.error(error.message || this.i18n('内部异常'))
    }).finally(() => {
      loading.close()
    })
  }

  private remove(billNumber: string) {
    this.$confirm(
      this.i18n("确定删除吗，删除后不可恢复"),
      this.i18n("删除制售单"),
      {
        confirmButtonText: this.formatI18n("/公用/按钮", "确定"),
        cancelButtonText: this.formatI18n("/公用/按钮", "取消"),
      }
    ).then(() => {
      const loading = this.$loading(ConstantMgr.loadingOption)
      return MakeSaleCardBillApi.remove(billNumber).then((resp:any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.i18n('操作成功'))
          this.getList()
        } else {
          this.$message.error(resp.msg || this.i18n('操作失败'))
        }
      }).catch((error) => {
        this.$message.error(error.message || this.i18n('内部异常'))
      }).finally(() => {
        loading.close()
      })
    });
  }
  private queryCard(row: MakeSaleCardBill) {
    this.$router.push({ name: 'prepay-card', query: { billNumber: row.billNumber, cardType: row.cardType }})
  }

  private exportCard(row: any) {
    MakeSaleCardBillApi.export(row.billNumber).then((resp: any) => {
      if (resp && resp.code === 2000) {
        window.open(resp.data, '_blank')
      } else {
        throw new Error(resp.msg)
      }
    }).catch((error) => {
      this.$message.error(error.message || this.i18n('内部异常'))
    })
  }

  private add() {
    this.$router.push({name: 'make-sale-card-bill-edit'})
  }

  private formatData(date: any) {
    return DateUtil.format(date)
  }

  computeState(state: string) {
    let str = '-'
    let color = '#A1B0C8'
    if (state === 'INITIAL') {
      str = this.i18n('未审核')
      color = '#FFAA00'
    } else if (state === 'AUDITED') {
      str = this.i18n('已审核')
      color = '#0CC66D'
    }
    return {
      state: str,
      color: color
    }
  }
}