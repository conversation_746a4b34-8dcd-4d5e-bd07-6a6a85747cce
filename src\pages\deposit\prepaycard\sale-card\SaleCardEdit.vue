<!--
 * @Author: 黎钰龙
 * @Date: 2023-10-12 16:18:40
 * @LastEditTime: 2023-11-30 17:52:36
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\prepaycard\sale-card\SaleCardEdit.vue
 * 记得注释
-->
<template>
  <div class="sale-card-edit-container">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button type="primary" v-if="hasOptionPermission('/卡/卡管理/售卡单', '单据审核')" @click="saveAndAudit">
          {{ i18n('保存并审核') }}
        </el-button>
        <el-button @click="save">
          {{ formatI18n('/储值/预付卡/卡模板/编辑页面/保存') }}
        </el-button>
        <el-button @click="$router.go(-1)">{{ formatI18n('/公用/按钮', '取消') }}</el-button>
      </template>
    </BreadCrume>
    <el-form :model="ruleForm" :rules="rules" class="demo-ruleForm" label-width="120px" ref="form">
      <div class="setting-container">
        <div class="section-block" style="padding-bottom:100px">
          <el-form-item :label="i18n('起始卡号')" prop="startCardCode">
            <el-input v-model.trim="ruleForm.startCardCode" @blur="doSearchCardTemplate" :placeholder="i18n('请录入已制卡的卡号')" style="width: 220px">
            </el-input>
            <div v-if="currentCardTemplate">
              <span class="span-btn" @click="gotoTplDtl">{{currentCardTemplate.name}}</span>
              <FormItem :label="i18n('次数') + ':'" v-if="currentCardTemplate.cardTemplateType === 'COUNTING_CARD'">
                <div style="line-height:36px">{{currentCardTemplate.count}}</div>
              </FormItem>
              <FormItem :label="i18n('卡面额') + ':'" v-else>
                <div style="line-height:36px">{{currentCardTemplate.faceAmounts[0] | fmt}}{{i18n('元')}}</div>
              </FormItem>
              <FormItem :label="i18n('价格') + ':'">
                <div style="line-height:36px">{{currentCardTemplate.price | fmt}}{{i18n('元')}}</div>
              </FormItem>
              <FormItem :label="i18n('有效期') + ':'">
                <div style="line-height:36px">{{currentCardTemplate | cardTemplateValidityTime}}</div>
              </FormItem>
            </div>
          </el-form-item>
          <el-form-item :label="i18n('售卡数量')" prop="makeQty">
            <AutoFixInput :min="1" :max="9999" :fixed="0" @blur="doComputePrice" :appendTitle="i18n('张')" style="width: 220px"
              v-model="ruleForm.makeQty" :placeholder="i18n('请录入1-9999之间的整数')" :disabled="!isExist">
            </AutoFixInput>
          </el-form-item>
          <el-form-item :label="i18n('总售价')" prop="total">
            <AutoFixInput :min="0" :max="99999999" :fixed="2" @blur="doComputePrice" :appendTitle="i18n('元')" style="width: 220px"
              v-model="ruleForm.total" :placeholder="i18n('请录入0-99999999之间的数')" :disabled="!isExist">
            </AutoFixInput>
          </el-form-item>
          <el-form-item :label="i18n('售价')" prop="price">
            <span>{{ruleForm.price | fmt}}</span>
          </el-form-item>
          <el-form-item :label="i18n('折扣')" prop="discount">
            <span v-if="ruleForm.discount">{{ruleForm.discount*100 | fmt}}%</span>
            <span v-if="!ruleForm.discount">--</span>
          </el-form-item>
          <el-form-item :label="i18n('发生门店')" prop="occurredOrg">
            <SelectStores v-model="ruleForm.occurredOrg" :isOnlyId="true" :hideAll="false" width="300px"
              :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
            </SelectStores>
          </el-form-item>
          <el-form-item :label="i18n('支付方式')" style="margin-top: 20px" :required="false" prop="payments">
        <div class="add-payment">
          <el-button @click="addPaymentRow" ref="myButton" :disabled="!isExist" class="add-payment-row-button">{{i18n('添加支付方式')}}</el-button>
          <span :style="{ marginLeft: '10px' }" class="add-payment-context">{{i18n('最多支持添加10个支付方式')}}</span>
          <el-table :data="ruleForm.payments" style="width: 500px" v-if="ruleForm.payments.length > 0">
            <el-table-column prop="paymentId" :label="i18n('支付方式')" width="180">
              <template slot-scope="scope">
                <el-select v-model="scope.row.paymentId" :placeholder="i18n('选择支付方式')" :disabled="!isExist" @change="handleSelectPayment(scope.row)">
                  <el-option
                      v-for="item in queryPaymentData"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id">
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="paymentAmount" :label="i18n('支付金额')" width="180">
              <template slot-scope="scope">
                <el-input v-model="scope.row.paymentAmount" :placeholder="i18n('输入金额')" :disabled="!isExist" @change="paymentAmountChange(scope)">
                  <template slot="append">{{ i18n('元') }}</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column :label="i18n('操作')" width="80">
              <template slot-scope="scope">
                <el-button type="text" @click="deletePaymentRow(scope.$index)">{{formatI18n('/公用/按钮','删除')}}</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-form-item>
      <el-form-item :label="i18n('支付总金额')">
        <div style="line-height: 36px; padding-left: 10px">
          {{ totalPayAmount === 0 ? '0' : totalPayAmount || '-' }}&emsp;{{i18n('元')}}
        </div>
      </el-form-item>
      <el-form-item :label="i18n('购买客户')" prop="buyer">
         <el-input type="textarea" v-model.trim="ruleForm.buyer" :placeholder="i18n('请输入不超过{0}个字符', ['50'])" show-word-limit style="width: 350px" maxlength="50" :rows="3"> </el-input>
      </el-form-item>
      <el-form-item :label="i18n('售卖员工')" style="margin-top: 10px">
        <el-select v-model="salesclerkId" size="medium" filterable :disabled="!isExist" :placeholder="i18n('请选择')" :filter-method="querySalesclerk">
          <el-option
              v-for="item in querySalesclerkData"
              :key="item.id"
              :label="item.name"
              :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
          <el-form-item :label="i18n('备注')" prop="remark">
            <el-input type="textarea" v-model.trim="ruleForm.remark" :placeholder="i18n('请输入不超过{0}个字符', ['140'])" show-word-limit style="width: 350px" maxlength="140" :rows="4"> </el-input>
          </el-form-item>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script lang="ts" src="./SaleCardEdit.ts">
</script>

<style lang="scss" scoped>
.sale-card-edit-container {
  width: 100%;
  
  ::v-deep .qf-form-item .qf-form-label {
    text-align: left !important;
    width: 80px !important;
  }
  ::v-deep .qf-form-content {
    margin-left: 80px !important;
  }
}

.add-payment {
    .add-payment-row-button {
      margin-top: 5px;
      margin-left: 10px;
      border-radius: 20px;
      padding: 10px 20px;
      color: #409eff;
      background-color: white;
      border: 2px solid #409eff;
    }
    .add-payment-row-button:hover {
      background-color: #ecf5ff;
      color: #409eff;
    }
    .add-payment-context {
      margin-left: 10px;
      color: #909399;
    }
    .el-table::before {
      height: 0;
    }

  }
</style>