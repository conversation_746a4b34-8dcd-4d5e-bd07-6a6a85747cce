<template>
  <div class="points-charge-setting">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button @click="doSave" type="primary" v-if="hasOptionPermission('/积分/积分规则/积分抵现', '规则维护')">
          {{ formatI18n('/公用/按钮', '保存') }}
        </el-button>
        <el-button @click="doCancel" v-if="hasOptionPermission('/积分/积分规则/积分抵现', '规则维护')">
          {{ formatI18n('/公用/按钮', '取消') }}
        </el-button>
      </template>
    </BreadCrume>
    <div style="height: 95%;overflow: auto">
      <div class="form-wrap">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
          <el-form-item :label="formatI18n('/营销/积分活动/积分活动/积分抵现活动/立即新建', '抵现方式')" class="tip-form">
            <i18n k="/权益/积分/积分初始化/积分抵现/立即设置/抵现方式/每使用{0}积分，抵现{1}元，不满{2}积分不抵现">
              <template slot="0">
                &nbsp;
                <el-form-item prop="score" style="display: inline-block" class="cur-form">
                  <el-input @blur="doScoreBlur" @change="doScoreChange" style="width: 80px"
                            v-model="ruleForm.score"></el-input>
                </el-form-item>
                &nbsp;
              </template>
              <template slot="1">
                &nbsp;
                <el-form-item prop="amount" style="display: inline-block" class="cur-form">
                  <el-input @change="doAmountChange" style="width: 80px" v-model="ruleForm.amount"></el-input>
                </el-form-item>
                &nbsp;
              </template>
              <template slot="2">
                &nbsp;
                {{ scoreCopy | strFormat }}
                &nbsp;
              </template>
            </i18n>
            <div style="margin-top: 10px;color: rgb(144, 147, 153)">-
              {{ formatI18n('/权益/积分/积分初始化/积分抵现/立即设置/抵现方式/如果每100积分抵1元，且不满100积分不能抵现，当会员有399积分时，那么他只能使用300积分抵3元现金') }}
            </div>
          </el-form-item>
          <!--<el-form-item class="cur-form">-->
          <!--<ChargeOption v-model="ruleForm.option" ref="chargeOption"></ChargeOption>-->
          <!--</el-form-item>-->
          <el-form-item :label="formatI18n('/营销/积分活动/积分活动/积分抵现活动/立即新建', '抵现条件')" class="tip-form">
            <el-radio-group v-model="ruleForm.option" style="padding-top: 14px" @change="doOptionChange">
              <el-radio style="display: block;margin-bottom: 20px" label="first">
                {{ formatI18n('/营销/积分活动/门店积分兑换/不限制') }}
              </el-radio>
              <el-radio style="display: block;margin-bottom: 20px" label="second">
                {{ formatI18n('/权益/积分/积分初始化/积分抵现/立即设置/抵现条件/整单消费满') }}&nbsp;&nbsp;
                <el-form-item prop="overAmount" style="display: inline-block" class="cur-form">
                  <el-input @change="doOverAmountChange" :disabled="ruleForm.option === 'first'" style="width: 80px;"
                            v-model="ruleForm.overAmount"></el-input>
                </el-form-item>
                &nbsp;&nbsp;{{ formatI18n('/权益/积分/积分初始化/积分抵现/立即设置/抵现条件/元及以上') }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="formatI18n('/营销/积分活动/积分活动/积分抵现活动/立即新建', '单笔抵现上限')" class="tip-form">
            <el-radio-group v-model="ruleForm.single" style="padding-top: 14px" @change="doSingleChange">
              <el-radio style="display: block;margin-bottom: 20px" label="first">
                {{ formatI18n('/权益/积分初始化/积分抵现/单笔抵现上限/无上限') }}
              </el-radio>
              <el-radio style="display: block;margin-bottom: 20px" label="second">
                <el-form-item prop="singleAmount" class="cur-form" style="display: inline-block">
                  <i18n k="/权益/积分初始化/积分抵现/单笔抵现上限/限不超过{0}元">
                    <template slot="0">&nbsp;&nbsp;
                      <el-input @change="doSingleAmountChange" :disabled="ruleForm.single !== 'second'"
                                style="width: 80px;"
                                v-model="ruleForm.singleAmount"></el-input>&nbsp;&nbsp;
                    </template>&nbsp;&nbsp;
                  </i18n>
                </el-form-item>
              </el-radio>
              <el-radio style="display: block;margin-bottom: 20px;" label="third">
                <i18n k="/权益/积分初始化/积分抵现/单笔抵现上限/限不超过适用商品金额的{0}%">
                  <template slot="0">
                    &nbsp;&nbsp;
                    <el-form-item prop="singlePercent" style="display: inline-block" class="cur-form">
                      <el-input @change="doSinglePercentChange" :disabled="ruleForm.single !== 'third'"
                                style="width: 80px;"
                                v-model="ruleForm.singlePercent"></el-input>
                    </el-form-item>&nbsp;&nbsp;
                  </template>
                </i18n>
              </el-radio>
              <el-radio style="display: block;" label="fourth">
                <i18n k="/权益/积分初始化/积分抵现/单笔抵现上限/限不超过{0}元，且不超过适用商品金额的{1}%">
                  <template slot="0">&nbsp;&nbsp;
                    <el-form-item prop="withAmount" style="display: inline-block" class="cur-form">
                      <el-input @change="doWithAmountChange" :disabled="ruleForm.single !== 'fourth'"
                                style="width: 80px;" v-model="ruleForm.withAmount"></el-input>
                    </el-form-item>&nbsp;&nbsp;
                  </template>
                  <template slot="1">
                    &nbsp;&nbsp;
                    <el-form-item prop="withPercent" style="display: inline-block" class="cur-form">
                      <el-input @change="doWithPercentChange" :disabled="ruleForm.single !== 'fourth'"
                                style="width: 80px;" v-model="ruleForm.withPercent"></el-input>
                    </el-form-item>&nbsp;&nbsp;
                  </template>
                </i18n>
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="formatI18n('/营销/积分活动/积分活动/积分抵现活动/立即新建', '每人每天积分抵现次数')" class="tip-form">
            <el-radio-group v-model="ruleForm.number" style="padding-top: 14px" @change="doNumberChange">
              <el-radio style="display: block;margin-bottom: 20px" label="first">
                {{ formatI18n('/权益/积分初始化/积分抵现/单笔抵现上限/无上限') }}
              </el-radio>
              <el-radio style="display: block;margin-bottom: 20px" label="second">
                {{ formatI18n('/权益/积分/积分初始化/积分抵现/立即设置/每人每天积分抵现次数/限') }}&nbsp;&nbsp;
                <el-form-item prop="limit" style="display: inline-block" class="cur-form">
                  <el-input @change="doLimitChange" :disabled="ruleForm.number === 'first'" style="width: 80px;"
                            v-model="ruleForm.limit"></el-input>
                </el-form-item>
                &nbsp;&nbsp;{{ formatI18n('/权益/积分/积分初始化/积分抵现/立即设置/每人每天积分抵现次数/次') }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <div style="margin-left: 100px"><span style="color: springgreen">注：</span>
        {{ formatI18n('/权益/积分/积分初始化/积分抵现/立即设置/退回的积分有效期不变；若交易使用的积分已过期清除，该部分积分不予退还。') }}
      </div>
    </div>

  </div>
</template>

<script lang="ts" src="./PointsChargeSetting.ts">
</script>

<style lang="scss">
.points-charge-setting {
  background-color: white;
  overflow: hidden;
  height: 100%;
  width: 100%;

  .form-wrap {
    margin: 30px;
  }

  .el-form-item__label {
    width: 170px !important;
  }

  .el-form-item__content {
    margin-left: 170px !important;
  }

  .cur-form {
    .el-form-item__label {
      width: 100px !important;
    }

    .el-form-item__content {
      margin-left: 0px !important;
    }
  }

  .tip-form {
    .el-form-item__label {
      &:before {
        content: '*';
        color: #EF393F;
        margin-right: 4px;
      }
    }
  }
}
</style>