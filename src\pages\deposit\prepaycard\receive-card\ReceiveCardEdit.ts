/*
 * @Author: 黎钰龙
 * @Date: 2023-10-09 15:39:14
 * @LastEditTime: 2025-04-30 16:14:00
 * @LastEditors: 黎钰龙
 * @Description:
 * @FilePath: \phoenix-web-ui\src\pages\deposit\prepaycard\receive-card\ReceiveCardEdit.ts
 * 记得注释
 */
import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import EditType from 'common/EditType';
import I18nPage from 'common/I18nDecorator';
import { Component, Vue, Watch } from 'vue-property-decorator';
import CardTplItem from '../cmp/cardtplitem/CardTplItem';

import FormItem from 'cmp/formitem/FormItem.vue'
import ImprestCardSaleBillLine from 'model/card/salebill/ImprestCardSaleBillLine';
import MakeCardBill from 'model/prepay/card/MakeCardBill';
import AutoFixInput from 'cmp/autofixinput/AutoFixInput';
import MakeCardBillApi from 'http/prepay/card/MakeCardBillApi';
import PrepayAccountApi from 'http/prepay/account/PrepayAccountApi';
import SelectStores from 'cmp/selectStores/SelectStores';
import ReceiveCardBill from "model/card/receivebill/ReceiveCardBill";
import PrePayCardApi from "http/prepay/card/PrePayCardApi";
import PrePayCardFilter from "model/prepay/card/PrePayCardFilter";
import PrePayCard from "model/prepay/card/PrePayCard";
import ReceiveCardBillApi from "http/card/receivebill/ReceiveCardBillApi";
import ReceiveCardBillPrepareResponse from "model/card/receivebill/ReceiveCardBillPrepareResponse";
import RSOrgFilter from "model/common/RSOrgFilter";
import OrgApi from "http/org/OrgApi";
import RSOrg from "model/common/RSOrg";
import IdName from "model/common/IdName";
import CommonUtil from 'util/CommonUtil';

@Component({
  name: 'ReceiveCardEdit',
  components: {
    BreadCrume,
    CardTplItem,
    FormItem,
    AutoFixInput,
    SelectStores
  }
})
@I18nPage({
  prefix: [
    '/公用/按钮',
    '/公用/券模板',
    '/卡/卡管理/领卡单/领卡单列表',
    '/卡/卡管理/领卡单/领卡单详情',
    '/卡/卡管理/领卡单/领卡单编辑',
    '/储值/预付卡/预付卡充值单',
    '/储值/预付卡/预付卡查询/列表页面',
    '/会员/洞察/公共/最近消费属性',
    '/资料/品牌',
    '/储值/预付卡/充值卡制售单/编辑页面'
  ],
  auto: true
})
export default class ReceiveCardEdit extends Vue {
  panelArray: any = []
  editType: string = EditType.CREATE
  rules: any = {}
  stores: RSOrg[] = []

  startCart: PrePayCard = new PrePayCard()
  endCode: PrePayCard = new PrePayCard()
  isEmpty: Boolean = true // 卡为空
  isExist: Nullable<boolean> = null // 卡不存在
  isMade: Nullable<boolean> = null // 卡是否为已制卡
  isEndEmpty: Boolean = true // 卡为空
  isEndExist: Boolean = false // 卡不存在
  isEndMade: Boolean = false // 卡是否为预制卡
  lengthEquals: Boolean = true  // 卡长度一致
  params: ReceiveCardBill = new ReceiveCardBill()
  prepareData: ReceiveCardBillPrepareResponse = new ReceiveCardBillPrepareResponse()
  // 卡数量
  cardCount: Nullable<number> = null
  loading = false
  inOrg: IdName = new IdName()
  outOrg: IdName = new IdName()
  org: IdName = new IdName()

  created() {
    this.initPanelArray()
    this.initEditType();
  }

  @Watch("params", { deep: true, immediate: true })
  private watchParams(value: boolean) {
    if (!this.params.startCardCode) {
      this.cardCount = 0
    }
    if (!this.params.endCardCode && this.isMade) {
      this.cardCount = 1
    }
  }



  initPanelArray() {
    this.panelArray = [
      {
        name: this.i18n('领卡单'),
        url: 'receive-card-list'
      },
      {
        name: this.editType === 'edit' ? this.i18n('编辑领卡单') : this.i18n('新建领卡单'),
        url: ''
      }
    ]
  }

  initEditType() {
    let editType = this.$route.query.editType as string
    if (editType) {
      this.editType = editType as string
    }
    if (this.editType === 'edit') {
      this.panelArray[1].name = this.i18n('编辑领卡单')
      this.getBillDel(this.$route.query.billNumber as string)
    }
  }

  getBillDel(billNumber: string) {
    ReceiveCardBillApi.get(billNumber).then((res) => {
      if (res.code === 2000) {
        this.params = res.data || new ReceiveCardBill()
        this.doStartCodeChange(this.params.startCardCode!);
        this.doEndCodeChange(this.params.endCardCode!);
        this.getCartCount();
        this.getInOrgAndOutOrg(this.params.inOrg!.id as any, this.params.outOrg!.id as any);
      } else {
        this.$message.error(res.msg || this.i18n('获取领卡单详情失败'))
      }
    }).catch((error) => {
      this.$message.error(error.message || this.i18n('获取领卡单详情失败'))
    })
  }


  //只能输入数字
  async doStartCodeChange(val: string) {
    const loading = CommonUtil.Loading()
    this.params.startCardCode = val.replace(/[^\d]/g, '')
    if (this.params.startCardCode) {
      this.isEmpty = false
      const filter = new PrePayCardFilter()
      filter.codeEquals = this.params.startCardCode
      filter.page = 0
      filter.pageSize = 1
      await PrePayCardApi.query(filter).then(res => {
        if (res.data && res.data.length > 0) {
          this.isExist = true
          this.startCart = res.data[0]
          if (this.startCart.state === 'MADE' || this.startCart.state === 'RECOVER' || this.startCart.state === 'BLANK' || this.startCart.state === 'BROKEN') {
            // 卡状态只能为 已制卡、已回收、空白卡、坏卡
            this.isMade = true
          } else {
            this.isMade = false
          }
        } else {
          this.isExist = false
        }
      })
      if (this.params.endCardCode) {
        this.getCartCount();
      } else {
        await ReceiveCardBillApi.calculateCartCount(this.params).then(res => {
          if (res.code === 2000) {
            if (res.data) {
              this.cardCount = Number(res.data)
            } else {
              this.cardCount = Number(0)
            }
          } else {
            this.cardCount = 0
            this.$message.warning(res.msg || '-')
          }
        }).catch((error: any) => {
          this.$message.error(error.message)
        })
      }
    } else {
      this.isEmpty = true
      this.isExist = null
      this.cardCount = 0
    }
    loading.close()
  }

  getCartCount() {
    if (this.params.startCardCode && this.params.endCardCode) {
      if (this.params.endCardCode.length == this.params.startCardCode.length) {
        this.lengthEquals = true
        ReceiveCardBillApi.calculateCartCount(this.params).then(res => {
          if (res.code === 2000) {
            if (res.data) {
              this.cardCount = Number(res.data)
            } else {
              this.cardCount = Number(0)
            }
          } else {
            this.cardCount = 0
            this.$message.warning(res.msg || '-')
          }
        }).catch((error: any) => {
          this.$message.error(error.message)
        })
      }
      else {
        this.cardCount = 0
        this.lengthEquals = false
      }
    }
  }

  //只能输入数字
  doEndCodeChange(val: string) {
    this.params.endCardCode = val.replace(/[^\d]/g, '')
    if (this.params.endCardCode) {
      this.isEndEmpty = false
      const filter = new PrePayCardFilter()
      filter.codeEquals = this.params.startCardCode
      filter.page = 0
      filter.pageSize = 1
      PrePayCardApi.query(filter).then(res => {
        if (res.data && res.data.length > 0) {
          this.isEndExist = true
          this.endCode = res.data[0]
          if (this.endCode.state === 'MADE') {
            this.isEndMade = true
          } else {
            this.isEndMade = false
          }
        } else {
          this.isEndExist = false
        }
      })
      if (this.params.startCardCode) {
        this.getCartCount()
      } else {
        this.cardCount = 0
      }
    } else {
      this.isEndExist = false
      this.isEndEmpty = true
      if (this.params.startCardCode && this.isMade) {
        this.cardCount = 1
      } else {
        this.cardCount = 0
      }
    }
  }

  doSaveAndAudit() {
    if (!this.doValidate()) {
      return
    }
    const params = this.getRequestParams();
    ReceiveCardBillApi.prepare(params).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.prepareData = resp.data
        if (this.prepareData.cardCount === this.prepareData.passedCount) {
          this.saveOrModifyAudit()
        } else if (this.prepareData.cardCount === this.prepareData.unPassedCount) {
          this.$message.error(this.i18n('卡的所属组织与领出组织不一致'))
        } else {
          this.$confirm(
            this.i18n("存在{0}张卡所属组织与领出组织不一致，则这部分卡将领出失败，是否继续", [(this.prepareData.unPassedCount ?? 0).toString()]),
            this.i18n("审核"),
            {
              confirmButtonText: this.formatI18n("/公用/按钮", "确定"),
              cancelButtonText: this.formatI18n("/公用/按钮", "取消"),
            }
          ).then(() => {
            this.saveOrModifyAudit()
          }).catch(() => {
            this.$router.push('receive-card-list')
          });
        }
      } else {
        this.$message.error(this.i18n(resp.msg))
      }
    })
  }

  saveOrModifyAudit() {
    if (this.editType === 'edit') {
      this.modifyAndAudit()
    } else {
      this.saveAndAudit()
    }
  }

  saveAndAudit() {
    const params = this.getRequestParams();
    ReceiveCardBillApi.saveAndAudit(params).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(this.i18n('保存成功'))
        this.$router.push({ name: 'receive-card-dtl', query: { billNumber: this.params.billNumber || resp.data } })
      } else {
        this.$message.error(this.i18n(resp.msg!) || this.i18n('保存失败'))
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(this.i18n(error.message) || this.i18n('保存失败'))
      }
    })
  }

  modifyAndAudit() {
    const params = this.getRequestParams();
    ReceiveCardBillApi.saveModify(params).then((resp: any) => {
      if (resp && resp.code === 2000) {
        ReceiveCardBillApi.audit(this.params.billNumber!).then((res) => {
          if (res.code === 2000) {
            this.$message.success(this.i18n('保存成功'))
            this.$router.push({ name: 'receive-card-dtl', query: { billNumber: this.params.billNumber || resp.data } })
          } else {
            this.$message.error(this.i18n(res.msg!) || this.i18n('保存失败'))
          }
        }).catch((error) => {
          this.$message.error(this.i18n(error.message) || this.i18n('保存失败'))
        })
      } else {
        this.$message.error(this.i18n(resp.msg!) || this.i18n('保存失败'))
      }
    }).catch((error: any) => {
      this.$message.error(this.i18n(error.message))
    })
  }

  doSave() {
    if (!this.doValidate()) {
      return
    }
    const params = this.getRequestParams();
    ReceiveCardBillApi.prepare(params).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.prepareData = resp.data
        if (this.prepareData.cardCount === this.prepareData.passedCount) {
          this.saveOrModify()
        } else if (this.prepareData.cardCount === this.prepareData.unPassedCount) {
          this.$message.error(this.i18n('卡的所属组织与领出组织不一致'))
        } else {
          this.$confirm(
            this.i18n("存在{0}张卡所属组织与领出组织不一致，则这部分卡将领出失败，是否继续", [(this.prepareData.unPassedCount ?? 0).toString()]),
            this.i18n("审核"),
            {
              confirmButtonText: this.formatI18n("/公用/按钮", "确定"),
              cancelButtonText: this.formatI18n("/公用/按钮", "取消"),
            }
          ).then(() => {
            this.saveOrModify()
          }).catch(() => {
            this.$router.push('receive-card-list')
          });
        }
      } else {
        this.$message.error(this.i18n(resp.msg))
      }
    })
  }

  saveOrModify() {
    if (this.editType === 'edit') {
      this.modify()
    } else {
      this.save()
    }
  }

  modify() {
    const params = this.getRequestParams();
    ReceiveCardBillApi.saveModify(params).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(this.i18n('保存成功'))
        this.$router.push({ name: 'receive-card-dtl', query: { billNumber: this.params.billNumber || resp.data } })
      } else {
        this.$message.error(this.i18n(resp.msg!) || this.i18n('保存失败'))
      }
    }).catch((error: any) => {
      this.$message.error(this.i18n(error.message))
    })
  }

  save() {
    const params = this.getRequestParams();
    ReceiveCardBillApi.save(params).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(this.i18n('保存成功'))
        this.$router.push({ name: 'receive-card-dtl', query: { billNumber: this.params.billNumber || resp.data } })
      } else {
        this.$message.error(this.i18n(resp.msg!) || this.i18n('保存失败'))
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(this.i18n(error.message) || this.i18n('保存失败'))
      }
    })
  }

  doCancel() {
    this.$router.back()
  }

  getRequestParams() {
    const params = JSON.parse(JSON.stringify(this.params))
    params.lines = []
    if (!params.outOrg!.id) {
      params.outOrg = this.outOrg
    }
    if (!params.inOrg!.id) {
      params.inOrg = this.inOrg
    }
    return params;
  }

  //  async getInOrg(id: any) {
  //     if(!id) return null
  //     const idName = new IdName()
  //     idName.id = id
  //     await this.getOrgDetail(id)
  //     idName.name = this.org.name
  //     this.inOrg = idName;
  // }
  // async getOutOrg(id: any) {
  //     if(!id) return null
  //     const idName = new IdName()
  //     idName.id = id
  //     await this.getOrgDetail(id)
  //     idName.name = this.org.name
  //     this.outOrg = idName;
  // }
  //
  //
  //  getOrgDetail(id: string) {
  //      OrgApi.detail(id).then((resp: any) => {
  //         if (resp && resp.code === 2000) {
  //             if (resp.data) {
  //                 this.org = resp.data.org
  //             }
  //         }
  //     }).catch((error: any) => {
  //         if (error && error.message) {
  //             this.$message.error(error.message)
  //         }
  //     })
  // }

  getInOrgAndOutOrg(inOrgId: string, outOrgId: string) {
    OrgApi.detail(inOrgId).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.inOrg = resp.data.org
        this.params.inOrg = resp.data?.org?.name as any
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
    OrgApi.detail(outOrgId).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.outOrg = resp.data.org
        this.params.outOrg = resp.data?.org?.name as any
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  doValidate() {
    if (!this.params.startCardCode) {
      this.$message.warning(this.i18n('起始卡号不能为空'))
      return false
    }
    if (!this.isExist) {
      this.$message.warning(this.i18n('卡号不存在'))
      return false
    }
    if (!this.isMade) {
      this.$message.warning(this.i18n('起始卡号为非已制卡'))
      return false
    }
    if (!this.isEndExist && !this.isEndEmpty) {
      this.$message.warning(this.i18n('结束卡号不存在'))
      return false
    }
    if (this.params.startCardCode.length !== 0 && !this.isEndEmpty && this.params.startCardCode.length !== this.params.endCardCode!.length) {
      this.$message.warning(this.i18n('结束卡号和起始卡号长度需一致'))
      return false
    }
    if (!this.params.outOrg) {
      this.$message.warning(this.i18n('请选择领出组织'))
      return false
    }
    if (!this.params.inOrg) {
      this.$message.warning(this.i18n('请选择领入组织'))
      return false
    }
    return true
  }


};