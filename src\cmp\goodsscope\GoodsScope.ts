import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import CatogorySelectorDialog from "../selectordialogs/CatogorySelectorDialog.vue";
import GoodsSelectorDialog from "../selectordialogs/GoodsSelectorDialog.vue";
import BrandSelectorDialog from "../selectordialogs/BrandSelectorDialog.vue";
import ImportDialog from "cmp/importdialogs/ImportDialog.vue";
import RSGoods from "model/common/RSGoods";
import RSBrand from "model/common/RSBrand";
import RSCategory from "model/common/RSCategory";
import GoodsRange from "model/common/GoodsRange";
import IdName from "model/common/IdName";
import Connective1 from "pages/member/insight/cmp/connective/Connective1";
import I18nTool from "common/I18nTool";
import EnvUtil from "util/EnvUtil";
import I18nPage from "common/I18nDecorator";
import SelectGoodsTag from "cmp/selectGoodsTag/SelectGoodsTag";
import GoodsCodeInfo from "model/common/GoodsCodeInfo";

class GoodsScopeFormRow {
  cond: string = "";
  text: string = "";
  items: any[] = [];
}

class GoodsScopeForm {
  connective: string;
  rows: GoodsScopeFormRow[] = [];
}

@Component({
  name: "GoodsScope",
  components: {
    CatogorySelectorDialog,
    GoodsSelectorDialog,
    BrandSelectorDialog,
    ImportDialog,
    Connective1,
    SelectGoodsTag
  },
})
@I18nPage({
  prefix: [
    "/公用/券模板",
    '/会员/洞察/客群管理/导入建群'
  ],
  auto: true
})
export default class GoodsScope extends Vue {
  get existsCond() {
    return this.ruleForm.rows.map((e: any) => e.cond);
  }

  get templatePath() {
    if (this.getType) {
      // 全场券
      return "template_specify_overall_goods.xlsx"; // 缺少新模板
    } else {
      return "template_specify_goods.xlsx";
    }
  }

  get getType() {
    if (this.type === "all_cash" || this.type === "all_discount") {
      return true;
    } else if (this.type === this.formatI18n("/公用/券模板", "全场现金券") || this.type === this.formatI18n("/公用/券模板", "全场折扣券")) {
      return true;
    }
    return false;
  }

  get getImportUrl() {
    if (this.getType) {
      // 全场券
      return "v1/goods/importOverallExcel";
    } else {
      return "v1/goods/importExcel";
    }
  }

  importUrl = "v1/goods/importExcel";
  dialogShow = false;
  ruleForm: GoodsScopeForm = new GoodsScopeForm();
  $refs: any;
  currentType: string = "";
  currentIndex: number = 0;
  rules: any;

  categoryIn = I18nTool.match("/公用/券模板/品类") + (EnvUtil.isZh_Cn() ? "" : " ") + I18nTool.match("/公用/券模板/属于");
  categoryNotIn = I18nTool.match("/公用/券模板/品类") + (EnvUtil.isZh_Cn() ? "" : " ") + I18nTool.match("/公用/券模板/不属于");
  brandIn = I18nTool.match("/公用/券模板/品牌") + (EnvUtil.isZh_Cn() ? "" : " ") + I18nTool.match("/公用/券模板/属于");
  brandNotIn = I18nTool.match("/公用/券模板/品牌") + (EnvUtil.isZh_Cn() ? "" : " ") + I18nTool.match("/公用/券模板/不属于");
  goodsIn = I18nTool.match("/公用/券模板/单品") + (EnvUtil.isZh_Cn() ? "" : " ") + I18nTool.match("/公用/券模板/属于");
  goodsNotIn = I18nTool.match("/公用/券模板/单品") + (EnvUtil.isZh_Cn() ? "" : " ") + I18nTool.match("/公用/券模板/不属于");
  tagsIn = I18nTool.match("/公用/券模板/商品标签") + (EnvUtil.isZh_Cn() ? "" : " ") + I18nTool.match("/公用/券模板/属于")
  tagsNotIn = I18nTool.match("/公用/券模板/商品标签") + (EnvUtil.isZh_Cn() ? "" : " ") + I18nTool.match("/公用/券模板/不属于")

  supportedCond: any = [];

  @Prop({
    type: String,
    default: "barcode",
  })
  goodsMatchRuleMode: "barcode" | "code";
  @Prop({
    type: String,
    default: "standard",
  })
  theme: "standard" | "precisionmarketing";
  @Prop({
    type: Boolean,
    default: true,
  })
  validateForm: boolean;
  @Prop()
  type: string;
  @Prop({
    type: Boolean,
    default: true,
  })
  innerTitle: boolean;
  @Prop({
    type: Boolean,
    default: false,
  })
  hideTitle: boolean;

  @Prop({
    type: Number,
    default: 5000,
  })
  importNumber: string;

  @Prop({
    default: '',
    type: String
  })
  goodsLimit: string

  @Prop()
  value: GoodsRange;

  @Prop({ type: Boolean }) disabled: boolean; //是否展示

  @Prop({ type: Boolean, default: false })
  appreciationGoods: boolean;
  @Prop({ type: String, default: 'normal' })
  chooseGoodType: String;

  @Watch("goodsLimit")
  onGoodsLimitChange(value: string) {
    this.computeCond()
  }

  @Watch("type")
  onTypeChange(value: string) {
    if (value) {
      this.ruleForm.rows = [];
      if (this.ruleForm.rows.length >= 3) {
        return;
      }
      let row = new GoodsScopeFormRow();
      row.cond = this.nextType() as any;
      this.ruleForm.rows.push(row);
    }
  }

  @Watch("value")
  onValueChange(value: GoodsRange) {
    if (!value) {
      this.$emit("input", new GoodsRange());
    } else {
      if (
        value &&
        value.includeBrands.length === 0 &&
        value.excludeBrands.length === 0 &&
        value.includeGoods.length === 0 &&
        value.excludeGoods.length === 0 &&
        value.includeCategories.length === 0 &&
        value.excludeCategories.length === 0 &&
        value.includeTags.length === 0 &&
        value.excludeTags.length === 0 &&
        value.includeCodes.length === 0 &&
        value.excludeCodes.length === 0
      ) {
        if (this.ruleForm.rows && this.ruleForm.rows.length > 0) {
          return;
        }
        let row = new GoodsScopeFormRow();
        row.cond = this.nextType() as any;
        this.ruleForm.rows.push(row);
      }
    }
  }

  isShowAddBtn(index: any) {
    if (index === this.supportedCond.length - 1) {
      return false
    }
    if (this.goodsLimit === '指定商品不适用') {
      return index === this.ruleForm.rows.length - 1
    } else {
      return index === this.ruleForm.rows.length - 1 && !this.getType
    }
  }

  computeCond() {
    const goodsInOrNot = [this.goodsIn, this.goodsNotIn]
    if (this.appreciationGoods && this.chooseGoodType === 'appreciation') {
      return this.supportedCond = goodsInOrNot
    }
    const normalUnsupportCond = [this.categoryIn, this.brandIn, this.goodsIn, this.tagsIn]
    const normalSupportCond = [this.categoryIn, this.categoryNotIn, this.brandIn, this.brandNotIn, this.goodsIn, this.goodsNotIn, this.tagsIn, this.tagsNotIn]
    // if (this.)
    if (["all_cash", "cash", "discount", 'special'].indexOf(this.type) > -1 && this.goodsLimit === '指定商品不适用') {
      //现金券、折扣券、特价券、随机金额券
      return this.supportedCond = normalUnsupportCond;
    } else if (this.type === "lucky_draw") {
      // 需求：抽锦鲤在现金券券模板基础上去掉商品标签属于/不属于
      const luckyDrawNormalUnSupportCond = [this.categoryIn, this.brandIn, this.goodsIn];
      const luckyDrawNormalSupportCond = [this.categoryIn, this.categoryNotIn, this.brandIn, this.brandNotIn, this.goodsIn, this.goodsNotIn];
      return this.supportedCond = this.goodsLimit === "指定商品不适用" ? luckyDrawNormalUnSupportCond : luckyDrawNormalSupportCond;
    } else {
      return this.supportedCond = normalSupportCond;
    }
  }

  created() {
    this.computeCond()
    this.rules = [
      {
        validator: (rule: any, value: any, callback: any) => {
          console.log('value是啥', value)
          if (!value || !value.items || value.items.length === 0) {
            if (value && value.cond.indexOf(this.formatI18n("/公用/券模板", "品牌")) !== -1) {
              callback(new Error(this.formatI18n("/公用/公共组件/商品范围控件/表单校验/请选择品牌范围")));
            } else if (value && value.cond.indexOf(this.formatI18n("/公用/券模板", "品类")) !== -1) {
              callback(new Error(this.formatI18n("/公用/公共组件/商品范围控件/表单校验/请选择品类范围")));
            } else if (value && value.cond.indexOf(this.formatI18n("/公用/券模板", "商品标签")) !== -1 && !value.text) {
              callback(new Error(this.formatI18n("/公用/表单校验/请填写必填项")));
            } else if (value && value.cond.indexOf(this.formatI18n("/公用/券模板", "单品")) !== -1 && value.cond.indexOf(this.formatI18n("/公用/券模板", "商品标签")) === -1) {
              callback(new Error(this.formatI18n("/公用/公共组件/商品范围控件/表单校验/请选择单品范围")));
            }
          }
          callback();
        },
        trigger: ["blur"],
      },
    ];
  }

  reset() {
    this.ruleForm.rows = [];
    this.doAdd();
  }

  mounted() {
    this.parseForm(this.value);
    if (this.ruleForm.rows.length === 0) {
      this.doAdd();
    }
  }

  doClear(index: number) {
    this.ruleForm.rows[index].text = "";
    this.ruleForm.rows[index].items = [];
    this.submit();
  }

  doDelete(index: number) {
    this.ruleForm.rows[index].text = "";
    this.ruleForm.rows[index].items = [];
    this.ruleForm.rows.splice(index, 1);
    this.$refs.ruleForm.clearValidate();
    this.$refs.ruleForm.validate();
    if (this.ruleForm.rows.length == 1) {
      console.log(this.ruleForm);
      this.ruleForm.connective = "and"; // 删到最后一个，变默认and
    }
    this.submit();
  }

  getSelectType(type: string) {
    if ([this.brandIn, this.brandNotIn].indexOf(type) > -1) {
      return this.formatI18n("/公用/券模板", "请点击选择品牌");
    } else if ([this.categoryIn, this.categoryNotIn].indexOf(type) > -1) {
      return this.formatI18n("/公用/券模板", "请点击选择品类");
    } else if ([this.goodsIn, this.goodsNotIn].indexOf(type) > -1) {
      return this.formatI18n("/公用/券模板", "请点击选择单品");
    }
  }

  doAdd() {
    if (this.ruleForm.rows.length >= this.supportedCond.length) {
      return;
    }
    let row = new GoodsScopeFormRow();
    row.cond = this.nextType() as any;
    this.ruleForm.rows.push(row);
    this.submit();
  }

  doImport() {
    this.$refs.importDialog.show();
  }

  doUploadSuccess(res: any) {
    if (res.response.code === 2000) {
      this.parseForm(res.response.data);
      this.submit();
    } else {
      this.$message.error(res.response.msg);
    }
  }

  get isEnglish() {
    return sessionStorage.getItem("locale") === "en_US";
  }

  doFocus(index: number) {
    this.currentIndex = index;
    if (this.ruleForm.rows[index].cond.indexOf(this.formatI18n("/公用/券模板", "单品")) > -1) {
      if(this.isEnglish) {
        // 如果是英文，单品为 Product， 商品标签为 Product labels，会判断错误
        if(this.ruleForm.rows[index].cond.indexOf(this.formatI18n("/公用/券模板", "商品标签")) > -1) {
          // 如果是英文下的 商品标签，是输入框不用打开弹窗
          return
        }
      }
      this.$refs.selectGoodsScopeDialog.open(this.ruleForm.rows[index].items);
    }
    if (this.ruleForm.rows[index].cond.indexOf(this.formatI18n("/公用/券模板", "品牌")) > -1) {
      // @ts-ignore
      this.$refs.selectBrandScopeDialog.open(this.ruleForm.rows[index].items);
    }
    if (this.ruleForm.rows[index].cond.indexOf(this.formatI18n("/公用/券模板", "品类")) > -1) {
      // @ts-ignore
      this.$refs.selectCatogoryScopeDialog.open(this.ruleForm.rows[index].items);
    }
  }

  checkScope(item: any) {
    let str = ''
    if (item.cond.indexOf(this.formatI18n("/公用/券模板", "单品")) > -1) {
      str = this.i18n('单品')
    }
    if (item.cond.indexOf(this.formatI18n("/公用/券模板", "品牌")) > -1) {
      str = this.i18n('品牌')
    }
    if (item.cond.indexOf(this.formatI18n("/公用/券模板", "品类")) > -1) {
      str = this.i18n('品类')
    }
    return str
  }

  doSubmitGoods(arr: RSGoods[]) {
    console.log('submit了啥',arr);
    if (this.goodsMatchRuleMode == 'code') {
      let str = "";
      if (arr && arr.length > 0) {
        arr.forEach((item: any) => {
          const qtyStr = item.qpcStr ? `[${item.qpcStr}]` : ''
          str += item.code + `[${item.name}]` + `${qtyStr};`;
        });
      }
      this.ruleForm.rows[this.currentIndex].text = str;
      this.ruleForm.rows[this.currentIndex].items = arr;
      this.$refs.ruleForm.validate();
      this.submit();
    } else {
      let str = "";
      if (arr && arr.length > 0) {
        arr.forEach((item: any) => {
          str += item.barcode + `[${item.name}];`;
        });
      }
      this.ruleForm.rows[this.currentIndex].text = str;
      this.ruleForm.rows[this.currentIndex].items = arr;
      this.$refs.ruleForm.validate();
      this.submit();
    }
  }

  doSubmitBrands(arr: RSBrand[]) {
    let str = "";
    if (arr && arr.length > 0) {
      arr.forEach((item: any) => {
        str += item.brand.id + `[${item.brand.name}];`;
      });
    }
    this.ruleForm.rows[this.currentIndex].text = str;
    this.ruleForm.rows[this.currentIndex].items = arr;
    this.$refs.ruleForm.validate();
    this.submit();
  }

  doSubmitCategorys(arr: RSCategory[]) {
    let str = "";
    if (arr && arr.length > 0) {
      arr.forEach((item: any) => {
        str += item.category.id + `[${item.category.name}];`;
      });
    }
    this.ruleForm.rows[this.currentIndex].text = str;
    this.ruleForm.rows[this.currentIndex].items = arr;
    this.$refs.ruleForm.validate();
    this.submit();
  }

  doSubmitTags(arr: IdName[], index: number) {
    let str = "";
    if (arr && arr.length > 0) {
      arr.forEach((item) => {
        str += item.id + `[${item.name}];`;
      });
    }
    this.ruleForm.rows[index].text = str;
    this.ruleForm.rows[index].items = arr;
    this.$refs.ruleForm.validate();
    this.submit();
  }

  changeType(index: number) {
    this.doClear(index);
    this.validate()
    this.submit();
  }

  submit() {
    this.$emit("input", this.toGoodsRange(this.ruleForm.rows));
  }

  //转换格式并提交给父组件
  toGoodsRange(selectedArray: GoodsScopeFormRow[]) {
    let goodsRange: GoodsRange = new GoodsRange();
    if (this.theme === "precisionmarketing") {
      // 精准营销固定为限制
      goodsRange.limit = true;
      goodsRange.relation = "and";
    } else {
      goodsRange.relation = this.ruleForm.connective;
    }
    for (let row of selectedArray) {
      if (row.cond === this.brandIn) {
        let arr = [];
        for (let one of row.items) {
          arr.push(one.brand);
        }
        goodsRange.includeBrands = goodsRange.includeBrands.concat(arr);
      }
      if (row.cond === this.brandNotIn) {
        let arr = [];
        for (let one of row.items) {
          arr.push(one.brand);
        }
        goodsRange.excludeBrands = goodsRange.excludeBrands.concat(arr);
      }
      if (row.cond === this.goodsIn) {
        if (this.goodsMatchRuleMode == 'code') {
          let arr = [];
          for (let one of row.items) {
            let codeInfo = new GoodsCodeInfo();
            codeInfo.code = one.code;
            codeInfo.name = one.name;
            codeInfo.qpcStr = one.qpcStr;
            arr.push(codeInfo);
          }
          goodsRange.includeCodes = goodsRange.includeCodes.concat(arr);
        } else {
          let arr = [];
          for (let one of row.items) {
            let idName = new IdName();
            idName.id = one.barcode;
            idName.name = one.name;
            arr.push(idName);
          }
          goodsRange.includeGoods = goodsRange.includeGoods.concat(arr);
        }
      }
      if (row.cond === this.goodsNotIn) {
        if (this.goodsMatchRuleMode == 'code') {
          let arr = [];
          for (let one of row.items) {
            let codeInfo = new GoodsCodeInfo();
            codeInfo.code = one.code;
            codeInfo.name = one.name;
            codeInfo.qpcStr = one.qpcStr;
            arr.push(codeInfo);
          }
          goodsRange.excludeCodes = goodsRange.excludeCodes.concat(arr);
        } else {
          let arr = [];
          for (let one of row.items) {
            let idName = new IdName();
            idName.id = one.barcode;
            idName.name = one.name;
            arr.push(idName);
          }
          goodsRange.excludeGoods = goodsRange.excludeGoods.concat(arr);
        }
      }
      if (row.cond === this.categoryIn) {
        let arr = [];
        for (let one of row.items) {
          arr.push(one.category);
        }
        goodsRange.includeCategories = goodsRange.includeCategories.concat(arr);
      }
      if (row.cond === this.categoryNotIn) {
        let arr = [];
        for (let one of row.items) {
          arr.push(one.category);
        }
        goodsRange.excludeCategories = goodsRange.excludeCategories.concat(arr);
      }
      if (row.cond === this.tagsIn) {
        goodsRange.includeTags = row.items || []
      }
      if (row.cond === this.tagsNotIn) {
        goodsRange.excludeTags = row.items || []
      }
    }
    console.log('最终的商品范围', goodsRange);

    return goodsRange;
  }

  validate() {
    return this.$refs.ruleForm.validate();
  }

  // 回填表单
  private parseForm(data: GoodsRange) {
    if (!data) {
      return;
    }
    this.ruleForm.connective = data.relation;
    if (this.type === this.formatI18n("/公用/券模板", "全场现金券") || this.type === this.formatI18n("/公用/券模板", "全场折扣券")) {
      this.ruleForm.rows = [];
      if (data.includeCategories && data.includeCategories.length > 0) {
        this.parseCategory(this.categoryIn, data.includeCategories);
      } else if (data.includeBrands && data.includeBrands.length > 0) {
        this.parseBrand(this.brandIn, data.includeBrands);
      } else if (data.includeGoods && data.includeGoods.length > 0) {
        this.parseGoods(this.goodsIn, data.includeGoods);
      } else if (data.includeCodes && data.includeCodes.length > 0) {
        this.parseGoodsCode(this.goodsIn, data.includeCodes);
      }
    } else {
      this.ruleForm.rows = [];
      if (data.includeGoods && data.includeGoods.length > 0) {
        this.parseGoods(this.goodsIn, data.includeGoods);
      }
      if (data.excludeGoods && data.excludeGoods.length > 0) {
        this.parseGoods(this.goodsNotIn, data.excludeGoods);
      }
      if (data.includeBrands && data.includeBrands.length > 0) {
        this.parseBrand(this.brandIn, data.includeBrands);
      }
      if (data.excludeBrands && data.excludeBrands.length > 0) {
        this.parseBrand(this.brandNotIn, data.excludeBrands);
      }
      if (data.includeCategories && data.includeCategories.length > 0) {
        this.parseCategory(this.categoryIn, data.includeCategories);
      }
      if (data.excludeCategories && data.excludeCategories.length > 0) {
        this.parseCategory(this.categoryNotIn, data.excludeCategories);
      }
      if (data.includeTags?.length > 0) {
        this.parseTags(this.tagsIn, data.includeTags)
      }
      if (data.excludeTags?.length > 0) {
        this.parseTags(this.tagsNotIn, data.excludeTags)
      }
      if (data.includeCodes && data.includeCodes.length > 0) {
        this.parseGoodsCode(this.goodsIn, data.includeCodes);
      }
      else if (data.excludeCodes && data.excludeCodes.length > 0) {
        this.parseGoodsCode(this.goodsNotIn, data.excludeCodes);
      }
      if (this.ruleForm.rows.length === 0) {
        this.doAdd();
      }
    }
  }

  private parseGoodsCode(cond: string, items: any[]) {
    let row = new GoodsScopeFormRow();
    row.cond = cond;
    items.forEach((item: any) => {
      let str = item.code + "[" + item.name + "]"+ "[" + item.qpcStr + "]";
      row.text += str + ";";
      let ins: any = {
        code: item.code,
        name: item.name,
        qpcStr: item.qpcStr,
      };
      row.items.push(ins);
    });
    this.ruleForm.rows.push(row);
  }

  private parseGoods(cond: string, items: any[]) {
    let row = new GoodsScopeFormRow();
    row.cond = cond;
    items.forEach((item: any) => {
      let str = item.id + "[" + item.name + "]";
      row.text += str + ";";
      let ins: any = {
        barcode: item.id,
        name: item.name,
      };
      row.items.push(ins);
    });
    this.ruleForm.rows.push(row);
  }

  private parseBrand(cond: string, items: any[]) {
    let row = new GoodsScopeFormRow();
    row.cond = cond;
    items.forEach((item: any) => {
      let str = item.id + "[" + item.name + "]";
      row.text += str + ";";
      let ins: any = {
        brand: {
          id: item.id,
          name: item.name,
        },
      };
      row.items.push(ins);
    });
    this.ruleForm.rows.push(row);
  }

  private parseCategory(cond: string, items: any[]) {
    let row = new GoodsScopeFormRow();
    row.cond = cond;
    items.forEach((item: any) => {
      let str = item.id + "[" + item.name + "]";
      row.text += str + ";";
      let ins: any = {
        category: {
          id: item.id,
          name: item.name,
        },
      };
      row.items.push(ins);
    });
    this.ruleForm.rows.push(row);
  }

  private parseTags(cond: string, items: any[]) {
    let row = new GoodsScopeFormRow();
    row.cond = cond;
    items.forEach((item: any) => {
      let str = item.id + "[" + item.name + "]";
      row.text += str + ";";
      let ins: IdName = new IdName()
      ins.id = item.id
      ins.name = item.name
      row.items.push(ins);
    });
    this.ruleForm.rows.push(row);
  }

  private nextType() {
    for (let item of this.supportedCond) {
      if (this.existsCond.indexOf(item) === -1) {
        return item;
      }
    }
  }
}
