<template>
  <div class="active-present-card">
    <span class="plain-btn-blue" @click="chooseCArdTpl" v-if="!editFlag">{{i18n('添加卡模板')}}</span>
    <div style="width: 0; height: 0; overflow: hidden">
      <CardTplItem ref="CardTplItem" :canSelectCenter="false" no-i18n @submit="selectTpl" :isSelectMultiple="true" :cardMedium="cardMediumIn"
        :type="currentCardType" :readonly="editFlag" :selectNo="selectCardNo" :maxSel="10" />
    </div>
    <el-form :model="data" :rules="rules" ref="form">
      <el-table ref="storeTable" :data="data.saleSpecs" stripe style="width: 100%">
        <el-table-column label="卡模板" key="1" min-width="120px" prop="name" fixed="left">
          <template slot-scope="scope">
            <span class="span-btn" @click="gotoTplDtl(scope.row)" no-i18n>
              {{ scope.row.cardTemplateName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column :label="formatI18n('/卡/卡管理/售卡单','卡面额/次数')" key="2" min-width="150px" prop="faceAmount">
          <template slot-scope="scope">
            <span style="margin:0 4px" v-if="scope.row.cardTemplateType === 'COUNTING_CARD'">
              {{ scope.row.count }} {{formatI18n('/储值/预付卡/预付卡查询/列表页面','次')}}
            </span>
            <i18n k="/储值/预付卡/电子礼品卡活动/编辑页面/卡面额{0}元" v-else>
              <template slot="0">
                <span style="margin:0 4px">{{ scope.row.faceAmount }}</span>
              </template>
            </i18n>
          </template>
        </el-table-column>
        <el-table-column label="折扣" key="3" v-if="favType == 'discount'" min-width="120px" prop="discount">
          <template slot-scope="scope">
            <el-form-item label="" :prop="`saleSpecs[${scope.$index}].discount`" :rules="rules.discountRules">
              <AutoFixInput :min="0.1" :max="9.9" :fixed="1" @change="doItemChange" style="width: 98px" v-model="scope.row.discount"
                :appendTitle="i18n('折')" />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="指定售价" key="4" min-width="120px" prop="price">
          <template slot-scope="scope">
            <span v-if="favType == 'discount'" no-i18n>{{computeSalePrice(scope.row)}}</span>
            <template v-else>
              <el-form-item label="" :prop="`saleSpecs[${scope.$index}].price`" :rules="rules.priceRules">
                <AutoFixInput :min="0.01" :max="scope.row.faceAmount || scope.row.templatePrice" :fixed="2" @change="doItemChange" style="width: 98px"
                  v-model="scope.row.price" :appendTitle="formatI18n('/券/购券管理','元')" />
              </el-form-item>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="购卡赠礼" key="5" min-width="500px" prop="gift" v-if="isShowGift">
          <template slot-scope="scope">
            <div style="display: flex; align-items: center">
              <el-checkbox @change="changeGivePoints(scope.$index)" v-model="data.saleSpecs[scope.$index].givePoints" style="margin-right: 5px" />
              <span>{{i18n('赠送积分')}}</span>
              <el-form-item :prop="`saleSpecs[${scope.$index}].gift.points`" :rules="rules.specPointsRules">
                <AutoFixInput :min="1" :max="999999" :fixed="0" @change="doItemChange" style="width: 98px;margin-right:6px"
                  v-model="scope.row.gift.points" :disabled="!scope.row.givePoints" :appendTitle="i18n('个')" />
              </el-form-item>
              <el-checkbox v-model="data.saleSpecs[scope.$index].giveCoupons" @change="changeGiveCoupons(scope.$index)" style="margin: 0 10px" />
              <el-form-item :prop="`saleSpecs[${scope.$index}].gift.couponItems`" :rules="rules.specCouponRules">
                <span>{{i18n('赠优惠券')}}</span>
                <el-button :disabled="!scope.row.giveCoupons" type="text" @click="doAddCoupon(scope.$index)">
                  {{i18n('添加')}}
                </el-button>
              </el-form-item>
            </div>
            <div style="padding-left: 176px">
              <template v-for="(gift, giftIndex) in scope.row.gift.couponItems">
                <div v-if="gift" class="gift-item" :key="gift.coupons.number">
                  <el-form-item :prop="`saleSpecs[${scope.$index}].gift.couponItems[${giftIndex}].qty`" :rules="rules.specCouponQtyRules">
                    <AutoFixInput :min="1" :max="999999" :fixed="0" @change="doItemChange" style="width: 98px;margin-right:4px" v-model="gift.qty"
                      :disabled="!scope.row.giveCoupons" :appendTitle="i18n('张')" />
                    <span>{{ gift.coupons.name }}</span>
                  </el-form-item>
                  <i class="el-icon-delete" @click="scope.row.gift.couponItems.splice(giftIndex, 1)"></i>
                </div>
              </template>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="发售限制" key="6" min-width="380px" prop="total" v-if="isShowLimit">
          <template slot-scope="scope">
            <div class="gray-tips">
              <i class="el-icon-warning" style="margin-right:4px"></i>
              {{i18n('/营销/券礼包活动/券礼包活动/留空，均表示不限制')}}
            </div>
            <div style="margin-top:4px">
              <i18n k="/营销/券礼包活动/新建商品满额发券活动/详情界面/发券数限制/每人限{0}张，活动总限{1}张">
                <template slot="0">
                  <AutoFixInput :min="1" :max="99999999" @change="doItemChange" :fixed="0" style="width: 80px;margin:0 4px"
                    v-model="scope.row.totalPerMan">
                  </AutoFixInput>
                </template>
                <template slot="1">
                  <AutoFixInput :min="1" :max="99999999" @change="doItemChange" :fixed="0" style="width: 80px;margin:0 4px" v-model="scope.row.total">
                  </AutoFixInput>
                </template>
              </i18n>
            </div>
          </template>
        </el-table-column>
        <el-table-column v-if="!editFlag" key="7" label="操作" prop="name" min-width="80px" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" @click="delteTplDtl(scope.$index)" style="font-size: 15px" no-i18n>{{i18n('删除')}}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <CouponTemplateSelectorDialog ref="couponTemplate" :filter="cardTemplateFilter" @summit="doCardTemplateSelected">
    </CouponTemplateSelectorDialog>
  </div>
</template>

<script lang="ts" src="./ActivePresentCard.ts">
</script>

<style lang="scss" scoped>
.active-present-card {
  .gift-item {
    padding-right: 32px;
    position: relative;
    margin-bottom: 12px;
    .el-icon-delete {
      font-size: 16px;
      cursor: pointer;
      position: absolute;
      right: 6px;
      top: 12px;
      color: #f00;
    }
  }
}
</style>