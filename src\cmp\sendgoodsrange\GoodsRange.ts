import {Component, Prop, Vue} from 'vue-property-decorator'
import SelectGoodsRangeDialog from 'pages/deposit/mbrdeposit/active/dialog/SelectGoodsRangeDialog.vue'
import ImportDialog from 'cmp/importdialog/ImportDialog.vue'
import ImportResultDialog from 'pages/deposit/mbrdeposit/active/dialog/ImportResultDialog.vue'

@Component({
    name: 'GoodsRange',
    components: {
        SelectGoodsRangeDialog,
        ImportDialog,
        ImportResultDialog
    }
})
export default class GoodsRange extends Vue {
    importResultDialogClose = false
    importResultData: any = {}
    goodsData: any = []
    selectType: any = []
    recordSelectType: any = []
    recordBrands: any = []
    recordIndex = 0
    recordName = ''
    goods = {
        includeGoods: [],
        excludeGoods: [],
        includeBrands: [],
        excludeBrands: [],
        includeCategories: [],
        excludeCategories: []
    }
    importUrl = 'v1/goods/importExcel'
    dialogShow = false
    importDialogShow = false
    dynamicValidateForm: any = {}
    $refs: any

    @Prop()
    data: any

    @Prop({
        default: true,
        type: Boolean
    })
    showDesc: boolean

    get templatePath() {
        if (location.href.indexOf('localhost') === -1) {
            return 'template_specify_goods.xlsx'
        } else {
            return 'template_specify_goods.xlsx'
        }
    }
    created() {
        this.selectType = [this.formatI18n('/公用/券模板', '品牌'), this.formatI18n('/公用/券模板', '单品'), this.formatI18n('/公用/券模板', '品类')]
        this.recordSelectType = [this.formatI18n('/公用/券模板', '品牌'), this.formatI18n('/公用/券模板', '单品'), this.formatI18n('/公用/券模板', '品类')]
        this.recordBrands = [this.formatI18n('/公用/券模板', '品牌') as string, '', '']
        this.dynamicValidateForm = {
            domains: [{
                value: '',
                brand: this.formatI18n('/公用/券模板', '品牌'),
                belong: this.formatI18n('/公用/券模板', '属于'),
                goods: []
            }],

        }
    }
    mounted() {
        this.setBindValue('')
    }
    doBelongChange(index: number, type: string, belong: string) {
        if (type === this.formatI18n('/公用/券模板', '品牌')) {
            if (belong === this.formatI18n('/公用/券模板', '属于')) {
                this.goods.includeBrands = this.goods.excludeBrands
                this.goods.excludeBrands = []
            } else {
                this.goods.excludeBrands = this.goods.includeBrands
                this.goods.includeBrands = []
            }
        } else if (type === this.formatI18n('/公用/券模板', '品类')) {
            if (belong === this.formatI18n('/公用/券模板', '属于')) {
                this.goods.includeCategories = this.goods.excludeCategories
                this.goods.excludeCategories = []
            } else {
                this.goods.excludeCategories = this.goods.includeCategories
                this.goods.includeCategories = []
            }
        } else {
            if (belong === this.formatI18n('/公用/券模板', '属于')) {
                this.goods.includeGoods = this.goods.excludeGoods
                this.goods.excludeGoods = []
            } else {
                this.goods.excludeGoods = this.goods.includeGoods
                this.goods.includeGoods = []
            }
        }
    }
    doClearDomain(index: number) {
        this.dynamicValidateForm.domains[index].value = ''
    }
    doTypeChange(index: number, type: string) {
        this.dynamicValidateForm.domains[index].value = ''
        let newArr = JSON.parse(JSON.stringify(this.recordSelectType))
        for (let i = 0; i < this.dynamicValidateForm.domains.length; i++) {
            for (let j = 0; j < newArr.length; j++) {
                if (this.dynamicValidateForm.domains[i].brand === newArr[j]) {
                    newArr.splice(j, 1)
                }
            }
        }
        this.selectType = newArr
    }

    /**
     * 不可删除，外部调用
     */
    getDomainValue() {
        if (this.dynamicValidateForm  && this.dynamicValidateForm.domains && this.dynamicValidateForm.domains.length > 0) {
            let count = 0
            this.dynamicValidateForm.domains.forEach((item: any) => {
                if (!item.value) {
                    count++
                }
            })
            if (count > 0) {
                return false
            } else {
                return true
            }
        }
    }
    doAddDomain(index: number, type: string) {
        if (this.dynamicValidateForm.domains.length >= 3) {
            return
        }
        let brand = ''
        let newArr = JSON.parse(JSON.stringify(this.recordSelectType))
        for (let i = 0; i < this.dynamicValidateForm.domains.length; i++) {
            for (let j = 0; j < newArr.length; j++) {
                if (this.dynamicValidateForm.domains[i].brand === newArr[j]) {
                    newArr.splice(j, 1)
                }
            }
        }
        brand = newArr[0]
        this.dynamicValidateForm.domains.push({
            value: '',
            brand: brand,
            belong: this.formatI18n('/公用/券模板', '属于'),
            goods: []
        });
        // todo
        for (let i = 0; i < this.dynamicValidateForm.domains.length; i++) {
            for (let j = 0; j < this.selectType.length; j++) {
                if (this.dynamicValidateForm.domains[i].brand === this.selectType[j]) {
                    this.selectType.splice(j, 1)
                }
            }
        }
        this.$emit('add')
    }
    doDeleteDomain(index: number) {
        if (this.dynamicValidateForm.domains.length === 1) {
            return
        }
        this.selectType.push(this.dynamicValidateForm.domains[index].brand)
        this.dynamicValidateForm.domains.splice(index, 1)
        this.goods = {
            includeGoods: [],
            excludeGoods: [],
            includeBrands: [],
            excludeBrands: [],
            includeCategories: [],
            excludeCategories: []
        }
        this.dynamicValidateForm.domains.forEach((item: any, index: number) => {
            if (item.brand === this.formatI18n('/公用/券模板', '品牌')) {
                if (item.belong === this.formatI18n('/公用/券模板', '属于')) {
                    let curArr: any = []
                    let arr = this.dynamicValidateForm.domains[index].value.split(';')
                    for (let i = 0; i < arr.length; i++) {
                        let arrSplit = arr[i].split('[')
                        if (arrSplit[0] && arrSplit[1]) {
                            let obj = {
                                id: arrSplit[0],
                                name: arrSplit[1].split(']')[0]
                            }
                            curArr.push(obj)
                        }
                    }

                    this.goods.includeBrands = curArr
                } else {
                    let curArr: any = []
                    let arr = this.dynamicValidateForm.domains[index].value.split(';')
                    for (let i = 0; i < arr.length; i++) {
                        let arrSplit = arr[i].split('[')
                        if (arrSplit[0] && arrSplit[1]) {
                            let obj = {
                                id: arrSplit[0],
                                name: arrSplit[1].split(']')[0]
                            }
                            curArr.push(obj)
                        }
                    }
                    this.goods.excludeBrands = curArr
                }
            }
            if (item.brand === this.formatI18n('/公用/券模板', '品类')) {
                if (item.belong === this.formatI18n('/公用/券模板', '属于')) {
                    let curArr: any = []
                    let arr = this.dynamicValidateForm.domains[index].value.split(';')
                    for (let i = 0; i < arr.length; i++) {
                        let arrSplit = arr[i].split('[')
                        if (arrSplit[0] && arrSplit[1]) {
                            let obj = {
                                id: arrSplit[0],
                                name: arrSplit[1].split(']')[0]
                            }
                            curArr.push(obj)
                        }
                    }
                    this.goods.includeCategories = curArr
                } else {
                    let curArr: any = []
                    let arr = this.dynamicValidateForm.domains[index].value.split(';')
                    for (let i = 0; i < arr.length; i++) {
                        let arrSplit = arr[i].split('[')
                        if (arrSplit[0] && arrSplit[1]) {
                            let obj = {
                                id: arrSplit[0],
                                name: arrSplit[1].split(']')[0]
                            }
                            curArr.push(obj)
                        }
                    }
                    this.goods.excludeCategories = curArr
                }
            }
            if (item.brand === this.formatI18n('/公用/券模板', '单品')) {
                if (item.belong === this.formatI18n('/公用/券模板', '属于')) {
                    let curArr: any = []
                    let arr = this.dynamicValidateForm.domains[index].value.split(';')
                    for (let i = 0; i < arr.length; i++) {
                        let arrSplit = arr[i].split('[')
                        if (arrSplit[0] && arrSplit[1]) {
                            let obj = {
                                id: arrSplit[0],
                                name: arrSplit[1].split(']')[0]
                            }
                            curArr.push(obj)
                        }
                    }
                    this.goods.includeGoods = curArr
                } else {
                    let curArr: any = []
                    let arr = this.dynamicValidateForm.domains[index].value.split(';')
                    for (let i = 0; i < arr.length; i++) {
                        let arrSplit = arr[i].split('[')
                        if (arrSplit[0] && arrSplit[1]) {
                            let obj = {
                                id: arrSplit[0],
                                name: arrSplit[1].split(']')[0]
                            }
                            curArr.push(obj)
                        }
                    }
                    this.goods.excludeGoods = curArr
                }
            }
        })
        this.$emit('goodsRangeHasValue', this.goods)
    }
    doClear() {
        // this.ruleForm.goods = ''
        // this.$emit('goodsRangeHasValue', this.ruleForm.goods, '', this.ruleForm)
    }
    doImport() {
        this.importDialogShow = true
    }
    doImportDialogClose() {
        this.importDialogShow = false
    }
    doImportResultDialogClose() {
        this.importResultDialogClose = false
    }
    doUploadSuccess(value: any) {
        if (value.response.code === 2000) {
            this.importResultDialogClose = true
            this.importResultData = {
                importResult : value.response.data.success, // 导入结果
                backUrl:  value.response.data.backUrl,
                errorCount: value.response.data.errorCount,
                ignoreCount: value.response.data.ignoreCount,
                successCount: value.response.data.successCount
            }
            // this.data = value.response.data
            this.setBindValue(value.response.data)
            this.$emit('importRangeValue', value)
        } else {
            this.$message.error(value.response.msg)
        }
    }
    getPlaceholder(value: string) {
        if (value === this.formatI18n('/公用/券模板', '品牌')) {
            return this.formatI18n('/公用/券模板', '请点击选择品牌')
        } else if (value === this.formatI18n('/公用/券模板', '品类')) {
            return this.formatI18n('/公用/券模板', '请点击选择品类')
        } else {
            return this.formatI18n('/公用/券模板', '请点击选择单品')
        }
    }
    doFocus(index: number, name: string) {
        this.recordIndex = index
        this.recordName = name
        this.dialogShow = true
        // todo 处理数据 如果是编辑
        this.goodsData = []
        if (this.dynamicValidateForm.domains[index].value &&
          this.dynamicValidateForm.domains[index].value.length > 0) {
            // 0000[3231313];1111[3231313];
            let arr = this.dynamicValidateForm.domains[index].value.split(';')
            for (let i = 0; i < arr.length; i++) {
                let obj = {
                    id: arr[i].split('[')[0],
                    name: ''
                }
                this.goodsData.push(obj)
            }
        }
    }
    doDialogClose() {
        this.dialogShow = false
    }
    doSummit(arr: any, type: string) {
        this.goodsData = []
        let str = ''
        if (arr && arr.length > 0) {
            arr.forEach((item: any) => {
                if (type === this.formatI18n('/公用/券模板', '品牌')) {
                    str += item.brand.id + `[${item.brand.name}];`
                    let obj = {
                        id: item.brand.id,
                        name: item.brand.name
                    }
                    this.goodsData.push(obj)
                } else if (type === this.formatI18n('/公用/券模板', '单品')) {
                    str += item.barcode + `[${item.name}];`
                    let obj = {
                        id: item.barcode,
                        name: item.name
                    }
                    this.goodsData.push(obj)
                } else if (type === this.formatI18n('/公用/券模板', '品类')) {
                    str += item.category.id + `[${item.category.name}];`
                    let obj = {
                        id: item.category.id,
                        name: item.category.name
                    }
                    this.goodsData.push(obj)
                }
            })
        }
        // 显示界面的值
        this.dynamicValidateForm.domains[this.recordIndex].value = str
        // 记录选择的单品、品类、品牌
        this.dynamicValidateForm.domains[this.recordIndex].goods = arr
        // 处理回调出去的参数
        this.dynamicValidateForm.domains.forEach((item: any) => {
            if (item.brand === this.formatI18n('/公用/券模板', '品牌')) {
                if (item.belong === this.formatI18n('/公用/券模板', '属于')) {
                    let curArr: any = []
                    if (item.goods && item.goods.length > 0) {
                        item.goods.forEach((subItem: any) => {
                            let obj = {
                                name: subItem.brand.name,
                                id: subItem.brand.id
                            }
                            curArr.push(obj)
                        })
                    }
                    this.goods.includeBrands = curArr
                } else {
                    let curArr: any = []
                    if (item.goods && item.goods.length > 0) {
                        item.goods.forEach((subItem: any) => {
                            let obj = {
                                name: subItem.brand.name,
                                id: subItem.brand.id
                            }
                            curArr.push(obj)
                        })
                    }
                    this.goods.excludeBrands = curArr
                }
            }
            if (item.brand === this.formatI18n('/公用/券模板', '品类')) {
                if (item.belong === this.formatI18n('/公用/券模板', '属于')) {
                    let curArr: any = []
                    if (item.goods && item.goods.length > 0) {
                        item.goods.forEach((subItem: any) => {
                            let obj = {
                                name: subItem.category.name,
                                id: subItem.category.id
                            }
                            curArr.push(obj)
                        })
                    }
                    this.goods.includeCategories = curArr
                } else {
                    let curArr: any = []
                    if (item.goods && item.goods.length > 0) {
                        item.goods.forEach((subItem: any) => {
                            let obj = {
                                name: subItem.category.name,
                                id: subItem.category.id
                            }
                            curArr.push(obj)
                        })
                    }
                    this.goods.excludeCategories = curArr
                }
            }
            if (item.brand === this.formatI18n('/公用/券模板', '单品')) {
                if (item.belong === this.formatI18n('/公用/券模板', '属于')) {
                    let curArr: any = []
                    if (item.goods && item.goods.length > 0) {
                        item.goods.forEach((subItem: any) => {
                            let obj = {
                                name: subItem.name,
                                id: subItem.barcode
                            }
                            curArr.push(obj)
                        })
                    }
                    this.goods.includeGoods = curArr
                } else {
                    let curArr: any = []
                    if (item.goods && item.goods.length > 0) {
                        item.goods.forEach((subItem: any) => {
                            let obj = {
                                name: subItem.name,
                                id: subItem.barcode
                            }
                            curArr.push(obj)
                        })
                    }
                    this.goods.excludeGoods = curArr
                }
            }
        })
        this.$emit('goodsRangeHasValue', this.goods)
        // this.$refs['dynamicValidateForm'].validate((valid: any) => {
        //     // todo
        // })
    }
    private setBindValue(data: any) {
        // this.$refs['dynamicValidateForm'].resetFields()
        if (data) {
            this.dynamicValidateForm = {
                domains: [{
                    value: '',
                    brand: this.formatI18n('/公用/券模板', '品牌'),
                    belong: this.formatI18n('/公用/券模板', '属于'),
                    goods: []
                }]
            }
            this.goods = {
                includeGoods: [],
                excludeGoods: [],
                includeBrands: [],
                excludeBrands: [],
                includeCategories: [],
                excludeCategories: []
            }
            if (data.includeGoods && data.includeGoods.length > 0) {
                this.goods.includeGoods = data.includeGoods
                let str = ''
                let oArr: any = []
                data.includeGoods.forEach((item: any) => {
                    str += item.id + '[' + item.name + '];'
                    oArr.push({
                        name: item.name,
                        barcode: item.id
                    })
                })
                if (this.dynamicValidateForm.domains && this.dynamicValidateForm.domains.length > 0) {
                    let count = 0
                    this.dynamicValidateForm.domains.forEach((subItem: any) => {
                        if (subItem.brand !== this.formatI18n('/公用/券模板', '单品')) {
                            count++
                        }
                    })
                    if (count === this.dynamicValidateForm.domains.length) {
                        this.dynamicValidateForm.domains.push(
                          {
                              value: '',
                              brand: this.formatI18n('/公用/券模板', '单品'),
                              belong: this.formatI18n('/公用/券模板', '属于'),
                              goods: oArr
                          }
                        )
                    }
                    this.dynamicValidateForm.domains.forEach((subItem: any) => {
                        if (subItem.brand === this.formatI18n('/公用/券模板', '单品') && subItem.belong === this.formatI18n('/公用/券模板', '属于')) {
                            this.$set(subItem, 'value', str)
                        }
                    })
                }
            }
            if (data.excludeGoods && data.excludeGoods.length > 0) {
                this.goods.excludeGoods = data.excludeGoods
                let str = ''
                let oArr: any = []
                data.excludeGoods.forEach((item: any) => {
                    str += item.id + '[' + item.name + '];'
                    oArr.push({
                        name: item.name,
                        barcode: item.id
                    })
                })
                if (this.dynamicValidateForm.domains && this.dynamicValidateForm.domains.length > 0) {
                    let count = 0
                    this.dynamicValidateForm.domains.forEach((subItem: any) => {
                        if (subItem.brand !== this.formatI18n('/公用/券模板', '单品')) {
                            count++
                        }
                    })
                    if (count === this.dynamicValidateForm.domains.length) {
                        this.dynamicValidateForm.domains.push(
                          {
                              value: '',
                              brand: this.formatI18n('/公用/券模板', '单品'),
                              belong: this.formatI18n('/公用/券模板', '不属于'),
                              goods: oArr
                          }
                        )
                    }
                    this.dynamicValidateForm.domains.forEach((subItem: any) => {
                        if (subItem.brand === this.formatI18n('/公用/券模板', '单品') && subItem.belong === this.formatI18n('/公用/券模板', '不属于')) {
                            this.$set(subItem, 'value', str)
                        }
                    })
                }
            }
            if (data.includeBrands && data.includeBrands.length > 0) {
                this.goods.includeBrands = data.includeBrands
                let str = ''
                let oArr: any = []
                data.includeBrands.forEach((item: any) => {
                    str += item.id + '[' + item.name + '];'
                    oArr.push({
                        brand: {
                            id: item.id,
                            name: item.name
                        }
                    })
                })
                if (this.dynamicValidateForm.domains && this.dynamicValidateForm.domains.length > 0) {
                    let count = 0
                    this.dynamicValidateForm.domains.forEach((subItem: any) => {
                        if (subItem.brand !== this.formatI18n('/公用/券模板', '品牌')) {
                            count++
                        } else {
                            subItem.goods = oArr
                        }
                    })
                    if (count === this.dynamicValidateForm.domains.length) {
                        this.dynamicValidateForm.domains.push(
                          {
                              value: '',
                              brand: this.formatI18n('/公用/券模板', '品牌'),
                              belong: this.formatI18n('/公用/券模板', '属于'),
                              goods: oArr
                          }
                        )
                    }
                    this.dynamicValidateForm.domains.forEach((subItem: any) => {
                        if (subItem.brand === this.formatI18n('/公用/券模板', '品牌') && subItem.belong === this.formatI18n('/公用/券模板', '属于')) {
                            this.$set(subItem, 'value', str)
                        }
                    })
                }
            }
            if (data.excludeBrands && data.excludeBrands.length > 0) {
                this.goods.excludeBrands = data.excludeBrands
                let str = ''
                let oArr: any = []
                data.excludeBrands.forEach((item: any) => {
                    str += item.id + '[' + item.name + '];'
                    oArr.push({
                        brand: {
                            id: item.id,
                            name: item.name
                        }
                    })
                })
                if (this.dynamicValidateForm.domains && this.dynamicValidateForm.domains.length > 0) {
                    let count = 0
                    this.dynamicValidateForm.domains.forEach((subItem: any) => {
                        if (subItem.brand !== this.formatI18n('/公用/券模板', '品牌')) {
                            count++
                        }
                        if (subItem.brand === this.formatI18n('/公用/券模板', '品牌')) {
                            subItem.belong = this.formatI18n('/公用/券模板', '不属于')
                        }
                    })
                    if (count === this.dynamicValidateForm.domains.length) {
                        this.dynamicValidateForm.domains.push(
                          {
                              value: '',
                              brand: this.formatI18n('/公用/券模板', '品牌'),
                              belong: this.formatI18n('/公用/券模板', '不属于'),
                              goods: oArr
                          }
                        )
                    }
                    this.dynamicValidateForm.domains.forEach((subItem: any) => {
                        if (subItem.brand === this.formatI18n('/公用/券模板', '品牌')) {
                            this.$set(subItem, 'value', str)
                        }
                    })
                }
            }
            if (data.includeCategories && data.includeCategories.length > 0) {
                this.goods.includeCategories = data.includeCategories
                let str = ''
                let oArr: any = []
                data.includeCategories.forEach((item: any) => {
                    str += item.id + '[' + item.name + '];'
                    oArr.push({
                        category: {
                            id: item.id,
                            name: item.name
                        }
                    })
                })
                if (this.dynamicValidateForm.domains && this.dynamicValidateForm.domains.length > 0) {
                    let count = 0
                    this.dynamicValidateForm.domains.forEach((subItem: any) => {
                        if (subItem.brand !== this.formatI18n('/公用/券模板', '品类')) {
                            count++
                        }
                    })
                    if (count === this.dynamicValidateForm.domains.length) {
                        this.dynamicValidateForm.domains.push(
                          {
                              value: '',
                              brand: this.formatI18n('/公用/券模板', '品类'),
                              belong: this.formatI18n('/公用/券模板', '属于'),
                              goods: oArr
                          }
                        )
                    }
                    this.dynamicValidateForm.domains.forEach((subItem: any) => {
                        if (subItem.brand === this.formatI18n('/公用/券模板', '品类') && subItem.belong === this.formatI18n('/公用/券模板', '属于')) {
                            this.$set(subItem, 'value', str)
                        }
                    })
                }
            }
            if (data.excludeCategories && data.excludeCategories.length > 0) {
                this.goods.excludeCategories = data.excludeCategories
                let str = ''
                let oArr: any = []
                data.excludeCategories.forEach((item: any) => {
                    str += item.id + '[' + item.name + '];'
                    oArr.push({
                        category: {
                            id: item.id,
                            name: item.name
                        }
                    })
                })
                if (this.dynamicValidateForm.domains && this.dynamicValidateForm.domains.length > 0) {
                    let count = 0
                    this.dynamicValidateForm.domains.forEach((subItem: any) => {
                        if (subItem.brand !== this.formatI18n('/公用/券模板', '品类')) {
                            count++
                        }
                    })
                    if (count === this.dynamicValidateForm.domains.length) {
                        this.dynamicValidateForm.domains.push(
                          {
                              value: '',
                              brand: this.formatI18n('/公用/券模板', '品类'),
                              belong: this.formatI18n('/公用/券模板', '不属于'),
                              goods: oArr
                          }
                        )
                    }
                    this.dynamicValidateForm.domains.forEach((subItem: any) => {
                        if (subItem.brand === this.formatI18n('/公用/券模板', '品类') && subItem.belong === this.formatI18n('/公用/券模板', '不属于')) {
                            this.$set(subItem, 'value', str)
                        }
                    })
                }
            }
            if (this.dynamicValidateForm.domains && this.dynamicValidateForm.domains.length > 0) {
                let newArray: any = []
                this.dynamicValidateForm.domains.forEach((item: any) => {
                    if (item.value) {
                        newArray.push(item)
                    }
                })
                this.dynamicValidateForm.domains = newArray
                if (this.dynamicValidateForm.domains.length === 0) {
                    this.dynamicValidateForm = {
                        domains: [{
                            value: '',
                            brand: this.formatI18n('/公用/券模板', '品牌'),
                            belong: this.formatI18n('/公用/券模板', '属于'),
                            goods: []
                        }]
                    }
                }
            }
        } else {
            this.goods = {
                includeGoods: [],
                excludeGoods: [],
                includeBrands: [],
                excludeBrands: [],
                includeCategories: [],
                excludeCategories: []
            }
            this.dynamicValidateForm = {
                domains: [{
                    value: '',
                    brand: this.formatI18n('/公用/券模板', '品牌'),
                    belong: this.formatI18n('/公用/券模板', '属于'),
                    goods: []
                }],

            }
        }

        this.$emit('goodsRangeHasValue', this.goods)
    }
}
