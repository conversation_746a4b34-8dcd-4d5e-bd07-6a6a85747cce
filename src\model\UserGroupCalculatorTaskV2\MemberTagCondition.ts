import NumberPropValues from "model/autoTag/NumberPropValues"
import { PropType } from "model/autoTag/PropType"
import StringPropValues from "model/autoTag/StringPropValues"
import TimePropValues from "model/autoTag/TimePropValues"

export default class MemberTagCondition {
  // 标签Id
  prop: Nullable<string> = null
  // 标签类型
  propType: Nullable<PropType> = null
  // 字符串属性值
  stringPropValue: Nullable<StringPropValues> = null
  // 数值属性值
  numberPropValues: Nullable<NumberPropValues> = null
  // 时间属性值
  timePropValues: Nullable<TimePropValues> = null
}