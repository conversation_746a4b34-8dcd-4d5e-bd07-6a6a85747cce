<!--
 * @Author: 黎钰龙
 * @Date: 2024-04-29 14:22:54
 * @LastEditTime: 2024-07-02 10:01:22
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\selectEquity\SelectEquity.vue
 * 记得注释
-->
<template>
  <div class="select-equity-container">
    <div class="plain-btn-blue" @click="doAdd" v-if="isEmpty">{{i18n('添加权益')}}</div>
    <div v-else>
      {{i18n('已选择{0}项权益',[listValue.length])}}
      <span class="span-btn" style="margin-left:6px" @click="doAdd">{{i18n('修改')}}</span>
      <draggable v-model="listValue" animation="300" @change="handleDragChange" v-if="!isShowList">
        <el-tag v-for="(item,index) in listValue" style="margin-right:6px;" :key="index">
          {{item.name}}
        </el-tag>
      </draggable>
    </div>
    <div class="equity-block" v-if="isShowList">
      <div class="empty" v-if="isEmpty">
        <img src="~assets/image/auth/ct_empty.png" />
        <div style="margin-top:-24px">{{i18n('请添加权益')}}</div>
      </div>
      <draggable v-model="listValue" animation="300" @change="handleDragChange">
        <div class="item-block" v-for="(item,index) in listValue" :key="index">
          <el-tooltip placement="top-start" effect="light" :open-delay="500">
            <template v-if="item.remark">
              <div slot="content" v-html="item.remark.replace(/\n/g,'<br/>')">
              </div>
            </template>
            <template v-else>
              <div slot="content">--</div>
            </template>
            <div class="equity-item">
              <img :src="item.imagePath" class="icon">
              {{item.name}}
            </div>
          </el-tooltip>
        </div>
      </draggable>
    </div>
    <EquitySelectDialog ref="equitySelectDialog" @summit="doSubmit"></EquitySelectDialog>
  </div>
</template>

<script lang="ts" src="./SelectEquity.ts">
</script>

<style lang="scss">
.select-equity-container {
  width: 100%;
  padding: 8px;
  .equity-block {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    padding: 24px;
    box-sizing: border-box;
    width: 1000px;
    min-height: 200px;
    background-color: #f7f9fc;
    margin-top: 12px;
    .empty {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: gray;
      image {
        width: 60px;
        height: 60px;
      }
    }
    .equity-item {
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #ffffff;
      padding: 12px;
      margin-right: 12px;
      box-sizing: border-box;
      height: 50px;
      border-radius: 6px;
      color: gray;
      margin-bottom: 8px;
      .icon {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        margin-right: 6px;
      }
    }
    .item-block {
      display: inline-flex;
      justify-content: center;
      align-items: center;
    }
  }
}
.el-tooltip__popper {
  max-width: 50% !important;
}
</style>