// 参团记录
export default class GroupBookingJoinedRecord {
  // uuid
  uuid: Nullable<string> = null
  // 版本
  version: Nullable<number> = null
  // 活动号
  activityNumber: Nullable<string> = null
  // 团号
  groupNumber: Nullable<string> = null
  // 会员Id
  memberId: Nullable<string> = null
  // 会员昵称
  memberNickName: Nullable<string> = null
  // 参团时间
  joinedTime: Nullable<Date> = null
  // 是否中奖
  won: Nullable<number> = null
  // 手机号
  mobile: Nullable<string> = null
  // 会员号
  crmCode: Nullable<string> = null
}