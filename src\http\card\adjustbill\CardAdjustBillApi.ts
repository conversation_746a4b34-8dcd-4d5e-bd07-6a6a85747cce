import ApiClient from 'http/ApiClient'
import CardAccount from 'model/card/adjustbill/CardAccount'
import Card<PERSON>djustBill from 'model/card/adjustbill/CardAdjustBill'
import CardAdjustBillFilter from 'model/card/adjustbill/CardAdjustBillFilter'
import CardAdjustBillLine from 'model/card/adjustbill/CardAdjustBillLine'
import CardAdjustBillStats from 'model/card/adjustbill/CardAdjustBillStats'
import Response from 'model/common/Response'

export default class CardAdjustBillApi {
  /**
   * 审核预付卡调整单
   *
   */
  static audit(billNumber: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/card-adjust-bill/audit/${billNumber}`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 审核预付卡调整单
   *
   */
  static batchAudit(body: string[]): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/card-adjust-bill/batch/audit`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 删除预付卡调整单
   *
   */
  static batchRemove(body: string[]): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/card-adjust-bill/batch/remove`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 获取预付卡调整单详情
   *
   */
  static get(billNumber: string): Promise<Response<CardAdjustBill>> {
    return ApiClient.server().get(`/v1/card-adjust-bill/get/${billNumber}`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询卡账户信息
   *
   */
  static getCardInfo(code: string): Promise<Response<CardAccount>> {
    return ApiClient.server().get(`/v1/card-adjust-bill/getCardInfo/${code}`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 获取预付卡调整单修改详情
   *
   */
  static getModify(billNumber: string): Promise<Response<CardAdjustBill>> {
    return ApiClient.server().get(`/v1/card-adjust-bill/get/modify/${billNumber}`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 批量预付卡预付卡调整单
   *
   */
  static importExcel(body: any): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/card-adjust-bill/importExcel`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询预付卡调整单
   *
   */
  static query(body: CardAdjustBillFilter): Promise<Response<CardAdjustBill[]>> {
    return ApiClient.server().post(`/v1/card-adjust-bill/query`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 获取预付卡调整单明细详情
   *
   */
  static queryDetail(billNumber: string, page: number, pageSize: number): Promise<Response<CardAdjustBillLine[]>> {
    return ApiClient.server().get(`/v1/card-adjust-bill/queryDetail/${billNumber}`, {
      params: {
        page: page,
        pageSize: pageSize
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建预付卡调整单
   *
   */
  static save(body: CardAdjustBill): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/card-adjust-bill/save`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 新建并审核预付卡调整单
   *
   */
  static saveAndAudit(body: CardAdjustBill): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/card-adjust-bill/saveAndAudit`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 修改预付卡调整单
   *
   */
  static saveModify(body: CardAdjustBill): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/card-adjust-bill/saveModify`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 预付卡调单汇总
   *
   */
  static stats(body: CardAdjustBillFilter): Promise<Response<CardAdjustBillStats>> {
    return ApiClient.server().post(`/v1/card-adjust-bill/stats`, body, {}).then((res) => {
      return res.data
    })
  }

}
