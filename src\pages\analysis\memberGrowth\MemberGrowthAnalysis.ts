/*
 * @Author: 黎钰龙
 * @Date: 2024-02-29 13:47:05
 * @LastEditTime: 2025-01-15 10:40:37
 * @LastEditors: haiding <EMAIL>
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\analysis\memberGrowth\MemberGrowthAnalysis.ts
 * 记得注释
 */
import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import FormItem from 'cmp/formitem/FormItem';
import MyQueryCmp from 'cmp/querycondition/MyQueryCmp';
import I18nPage from 'common/I18nDecorator';
import { Component } from 'vue-property-decorator';
import AnalysisDateSelector from '../cmp/AnalysisDateSelector/AnalysisDateSelector.vue';
import ChannelSelect from 'cmp/channelselect/ChannelSelect';
import SelectStores from 'cmp/selectStores/SelectStores';
import MemberLineChart from '../cmp/MemberLineChart/MemberLineChart';
import SelectEmployees from 'cmp/selectEmployees/selectEmployees';
import OrgMemberAnalysisReportQuery from 'model/analysis/OrgMemberAnalysisReportQuery';
import AnalysisReportApi from 'http/analysis/AnalysisReportApi';
import CommonUtil from 'util/CommonUtil';
import OrgMemberAnalysisReport from 'model/analysis/OrgMemberAnalysisReport';
import AbstractLineChart from '../cmp/AbstractLineChart';
import DownloadCenterDialog from 'pages/main/dowmload_center_dialog/DownloadCenterDialog';

class Filter {
  dataRange: any = null  //时间
  channel: any = null  //渠道
  store: any = null  //门店
  areaLeaderEquals: any = null  //区域主管
  operationEquals: any = null //营运经理
}

@Component({
  name: 'MemberGrowthAnalysis',
  components: {
    BreadCrume,
    MyQueryCmp,
    FormItem,
    AnalysisDateSelector,
    ChannelSelect,
    SelectStores,
    MemberLineChart,
    SelectEmployees,
    DownloadCenterDialog
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/数据/会员增长分析'
  ],
  auto: true
})
export default class MemberGrowthAnalysis extends AbstractLineChart {
  $refs: any
  panelArray: any = []
  filter: Filter = new Filter()
  detail: OrgMemberAnalysisReport = new OrgMemberAnalysisReport()
  downloadCenterFlag: boolean = false; //文件下载中心弹窗

  /* 每个item的第一项：数据名
  /  第二项：数据值
  /  第三项：是否展示在右y轴上 */
  get valueArray() {
    const arr = [
      [this.i18n('新会员数'), this.detail.newMemberData, 0, true],
      [this.i18n('有手机新会员数'), this.detail.newMemberHasPhoneData, 0, true],
      [this.i18n('会员数'), this.detail.memberData, 0, true],
      [this.i18n('有手机会员数'), this.detail.memberHasPhoneData, 0, true],
      [this.i18n('有消会员数量'), this.detail.consumedMemberData, 0, true],
      [this.i18n('有消新会员数量'), this.detail.firstConsumedMemberData, 0, true],
      [this.i18n('新会员有消占比'), this.doDivideGetPercent(this.detail.firstConsumedMemberData, this.detail.newMemberData), 1, false],
      [this.i18n('复购会员数量'), this.detail.repurchasedMemberData, 0, true],
      [this.i18n('复购率'), this.doDivideGetPercent(this.detail.repurchasedMemberData, this.detail.consumedMemberData), 1, false],
    ]
    return this.doTransValueArray(arr)
  }

  get summaryViewArr() {
    return [
      {
        label: this.i18n('新会员数'),
        value: this.detail.summary?.newMemberCount,
        ringValue: Number(this.detail.summary?.newMemberRingGrowth)
      },
      {
        label: this.i18n('有手机新会员数'),
        value: this.detail.summary?.newMemberHasPhoneCount,
        ringValue: Number(this.detail.summary?.newMemberHasPhoneRingGrowth)
      },
      {
        label: this.i18n('会员数'),
        value: this.detail.summary?.memberCount,
        ringValue: Number(this.detail.summary?.memberRingGrowth)
      },
      {
        label: this.i18n('有手机会员数'),
        value: this.detail.summary?.memberHasPhoneCount,
        ringValue: Number(this.detail.summary?.memberHasPhoneRingGrowth)
      },
    ]
  }

  // 需要显示百分号的数据名称
  get showPercentName() {
    return [this.i18n('新会员有消占比'), this.i18n('复购率')]
  }

  created() {
    this.panelArray = [
      {
        name: this.i18n('/公用/菜单/会员增长分析'),
        url: ""
      }
    ]
  }

  mounted() {
    this.onSearch()
  }

  doExport() {
    this.$confirm(this.i18n("将根据当前查询条件生成报表，确认导出吗？"), this.i18n('/储值/预付卡/充值卡制售单/列表页面/导出'), {
      confirmButtonText: this.formatI18n("/公用/按钮", "确定") as string,
      cancelButtonText: this.formatI18n("/公用/按钮", "取消") as string,
    }).then(() => {
      const body = this.doFilterParams()
      AnalysisReportApi.orgMemberExport(body).then((res) => {
        if (res.code === 2000) {
          this.downloadCenterFlag = true;
        } else {
          throw new Error(res.msg!)
        }
      }).catch((error) => {
        this.$message.error(error.message)
      })
    });
  }

  doReset() {
    this.filter = new Filter()
    this.$refs.analysisDateSelector.doReset()
    this.onSearch()
  }

  onSearch() {
    const body = this.doFilterParams()
    const loading = CommonUtil.Loading()
    AnalysisReportApi.orgMemberReport(body).then((res) => {
      if (res.code === 2000) {
        this.detail = res.data || new OrgMemberAnalysisReport()
      } else {
        throw new Error(res.msg as any)
      }
    }).catch((error) => {
      this.$message.error(error.message || this.i18n('内部异常'))
    }).finally(() => {
      loading.close()
    })
  }

  // 查询条件
  doFilterParams() {
    const params = new OrgMemberAnalysisReportQuery()
    params.dateUnitEquals = this.filter.dataRange?.type
    if (this.filter.dataRange?.date?.length) {
      params.startDate = this.filter.dataRange.date[0]
      params.endDate = this.filter.dataRange.date[1]
    }
    params.operationEquals = this.filter.operationEquals?.id || null
    params.areaLeaderEquals = this.filter.areaLeaderEquals?.id || null
    //渠道
    if (this.filter.channel?.length) {
      params.channelTypeEquals = this.filter.channel[0].type
      params.channelIdEquals = this.filter.channel[0].id
    }
    params.storeIdEquals = this.filter.store
    return params
  }

  doDateChange(value: any) { // {type: 'DAY' | 'WEEK' | 'MONTH', date:['2024-03-05','2024-03-06']}
    this.filter.dataRange = value
  }

  doDownloadDialogClose() {
    this.downloadCenterFlag = false;
  }
};