import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import CouponItem from 'model/common/CouponItem'
import AmountToFixUtil from 'util/AmountToFixUtil'
import TimeRange from 'cmp/coupontenplate/cmp/TimeRange.vue'
import GoodsScopeEx from 'cmp/goodsscope/GoodsScopeEx.vue'
import UseCouponStep from 'cmp/coupontenplate/cmp/UseCouponStep.vue'
import CouponBear from 'cmp/coupontenplate/cmp/CouponBear.vue'
import CouponInfo from 'model/common/CouponInfo'
import CashCouponAttribute from 'model/common/CashCouponAttribute'
import ValidityInfo from 'model/common/ValidityInfo'
import DateTimeRange from 'model/common/DateTimeRange'
import StoreRange from 'model/common/StoreRange'
import GetGoodsRange from 'cmp/coupontenplate/cmp/GetGoodsRange.vue'
import PickUpCouponAttribute from 'model/common/PickUpCouponAttribute'
import DateUtil from 'util/DateUtil'
import ChannelRange from 'model/common/ChannelRange'
import CouponTemplateLogo from "cmp/coupontenplate/cmp/CouponTemplateLogo";
import ActiveStore from "cmp/activestore/ActiveStore";
import CouponTemplateLabel from "cmp/coupontenplate/cmp/CouponTemplateLabel.vue"
import { LocalStorage } from 'mgr/BrowserMgr'
import I18nPage from 'common/I18nDecorator'
import CouponName from '../FormItemCmp/CouponName/CouponName'
import UseCouponDesc from '../FormItemCmp/UseCouponDesc/UseCouponDesc'
import CouponEffectPeriod from '../FormItemCmp/CouponEffectPeriod/CouponEffectPeriod'
import CouponCodeRules from '../FormItemCmp/CouponCodeRules/CouponCodeRules'
import BrowserMgr from "mgr/BrowserMgr";
import SelectCostParty from 'cmp/selectCostParty/SelectCostParty';
import { ExpiryType } from "model/common/ExpiryType";
import CouponInitialApi from "http/v2/coupon/init/CouponInitialApi";
import CouponConfig from 'model/v2/coupon/init/CouponConfig'


@Component({
  name: 'PickUpCoupon',
  components: {
    CouponName,
    UseCouponDesc,
    CouponEffectPeriod,
    CouponCodeRules,
    TimeRange,
    ActiveStore,
    GoodsScopeEx,
    UseCouponStep,
    CouponBear,
    GetGoodsRange,
    CouponTemplateLogo,
    CouponTemplateLabel,
    SelectCostParty
  }
})
@I18nPage({
  prefix: [
    "/公用/券模板",
  ],
  auto: true
})
export default class PickUpCoupon extends Vue {
  dtl: CouponConfig = new CouponConfig()
  queryCostParyRange:string = 'customize'
  goodsMatchRuleMode: string = "barcode"
  timeParam: any = ''
  ruleForm: any = {
    name: '',
    dateType: 'RALATIVE',
    dateFrom: '',
    dateTo: '',
    dateFix: '',
    useDate: '',
    storeRange: '{}',
    useCouponGood: [],
    couponProduct: '',
    type: '',
    time: '',
    templateId: '',
    useFrom: 'step2',
    logoUrl: '',
    from: [],
    prefix: '',
    transferable: true,  // 是否可转赠
    pickQty: null,
    reserve: false, //是否预约提货
    reserveStartDay: null,
    reserveEndDay: null,
    templateTag: [],
    price: null,
    termsModel: null,
    couponSubscriptType: "COMMON",
    notes:"",
    maxDailyMemberQuotaQty: null,
    writeOffLink: "",
  }
  $refs: any
  rules: any = {}
  curState = ''
  enablePayApportion: boolean = false
  canChooseItems: any[] = []
  telescoping: boolean = true  //为true收起高级设置，为false展开
  @Prop()
  sameStore: boolean // 与活动门店一致
  @Prop()
  state: string
  @Prop()
  channels: any
  @Prop()
  value: CouponItem
  @Prop({
    type: Boolean,
    default: false
  })
  baseSettingFlag: boolean
  @Prop({
    type: String,
    default: 'add'
  })
  copyFlag: string // 是否是复制\新建\编辑
  @Prop({
    type: String,
    default: '400'
  })
  remarkMaxlength: string
  @Prop({
    type: Boolean,
    default: false
  })
  enableStore: boolean

  @Prop({
    default: () => {
      return {
        maxAmount: 99999999,
        maxValidDay: 36500,
        maxUseThreshold: 99999999,
        fixedTime: false,
      }
    }
  })
  options: {  // 指定最大券面额，可选配置，用于微信扫码领券
    maxAmount: number,// 指定最大券面额
    maxValidDay: number, // 指定最大券有效天数
    maxUseThreshold: number,// 指定最大用券门槛
    fixedTime: boolean, // 固定用券时段为全部时段
  }

  @Watch('state')
  onStateChange(value: string) {
    this.curState = value
  }

  @Watch('value')
  onDataChange(value: CouponItem) {
    if (value && value.coupons) {
      this.doBindValue(JSON.parse(JSON.stringify(value)))
    }
  }

  get remarkPlaceholder() {
    let str = this.formatI18n('/营销/积分活动/积分活动/积分兑换券/编辑页面', '请输入不超过{0}个字符')
    return str.replace(/\{0\}/g, this.remarkMaxlength);
  }

  get accountItemRange() {
    if (this.queryCostParyRange) {
      return this.queryCostParyRange;
    }
      return 'customize';
  }

  created() {
    this.queryCostParyRange = LocalStorage.getItem("accountItemRange");
    let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
		if (sysConfig && this.goodsMatchRuleMode) {
			this.goodsMatchRuleMode = sysConfig.goodsMatchRuleMode;
		}
    this.rules = {
      name: [
        { required: true, message: this.formatI18n('/公用/券模板', '请输入必填项'), trigger: 'blur' },
      ],
      useCouponGood: [
        {
          required: true, validator: (rule: any, value: any, callback: any) => {
            if (value && value && value.length > 0) {
              let count = 0
              value.forEach((item: any) => {
                if (!item.goods.name || !item.qty) {
                  count++
                }
              })
              if (count > 0) {
                callback(new Error(this.formatI18n('/公用/券模板', '请输入必填项') as string))
              } else {
                callback()
              }
            } else {
              callback(new Error(this.formatI18n('/公用/券模板', '请输入必填项') as string))
            }
          }, trigger: 'blur'
        }
      ],
      useFrom: [
        {
          validator: (rule: any, value: any, callback: any) => {
            if (value) {
              if (value === 'step1') {
                callback()
              } else {
                if (this.ruleForm.from && this.ruleForm.from.length > 0) {
                  callback()
                } else {
                  callback(this.formatI18n('/公用/券模板', '请输入必填项'))
                }
              }
            }
          }, trigger: 'blur'
        },
      ],
      couponProduct: [
        { required: true, message: this.formatI18n('/公用/券模板', '请输入必填项'), trigger: 'blur' },
      ],
      prefix: [
        {
          validator: (rule: any, value: any, callback: any) => {
            let re = /^[0-9a-zA-Z]*$/g;  // 判断字符串是否为数字和字母组合
            if (!re.test(value)) {
              callback(this.i18n('请输入数字或字母'));
            } else {
              callback();
            }
          }, tirgger: 'blur'

        },
      ],
      reserveStartDay: [
        { required: true, message: this.formatI18n('/公用/券模板', '请输入必填项'), trigger: 'blur' },
      ],
      reserveEndDay: [
        { required: true, message: this.formatI18n('/公用/券模板', '请输入必填项'), trigger: 'blur' },
      ],
    }
    if (this.value && this.value.coupons) {
      this.doBindValue(JSON.parse(JSON.stringify(this.value)))
    }
    this.curState = this.state
    this.getCouponPrefix("goods");
  }

  private getCouponPrefix(type: string){
    if (["edit", "copy"].includes(this.copyFlag)) {
			return;
		}
		CouponInitialApi.get().then((resp: any) => {
		  if (resp && resp.code === 2000) {
			this.dtl = resp.data;
			if(!this.dtl || !this.dtl.couponCodePrefixes) {
				return "";
			  }
        const coupon = this.dtl.couponCodePrefixes.find(
          item => item.couponType === type
        );
        this.ruleForm.prefix = coupon ? coupon.prefix : "";
		  }
		})
	}

  doFormItemChange() {
    this.$emit("input", this.doTransParams());
    this.$emit("change", this.channels);
  }

  doFromChange() {
    this.$refs.ruleForm.validateField('useFrom')
    this.doFormItemChange()
  }

  doStoreChange() {
    this.doFormItemChange()
    if (this.$refs['ruleForm'] && (this.$refs['ruleForm'] as any).validateField) {
      (this.$refs['ruleForm'] as any).validateField('storeRange')
    }
  }

  logoUrlCallBack(url: any) {
    this.ruleForm.logoUrl = url
    this.doFormItemChange()
  }

  doUseCouponGoodsChange() {
    this.doFormItemChange()
    this.canChooseItems = []
    this.ruleForm.useCouponGood.forEach((item: any, index: number) => {
      if (index === 0) {
        this.canChooseItems.push({
          label: '全部可选',
          value: this.ruleForm.useCouponGood.length
        })
      } else {
        this.canChooseItems.push({
          label: `${this.ruleForm.useCouponGood.length}选${this.ruleForm.useCouponGood.length - index}`,
          value: this.ruleForm.useCouponGood.length - index
        })
      }
    })
    if (this.ruleForm.useCouponGood.length > 0) {
      this.ruleForm.pickQty = this.ruleForm.useCouponGood.length
    } else {
      this.ruleForm.pickQty = null
      this.canChooseItems = []
    }
  }

  doSelectPickQtyChange(e: any) {
    this.ruleForm.pickQty = e
    this.doFormItemChange()
  }

  doUseFromChange() {
    if (this.ruleForm.useFrom === 'step1') {
      this.ruleForm.from = []
      this.doFormItemChange()
    }
    this.$refs.ruleForm.validateField('useFrom')
  }

  changeSwitch(flag: any) {
    this.enablePayApportion = flag;
    this.doFormItemChange()
  }

  doValidate() {
    this.telescoping = false
    let arr: any = []
    let p0 = new Promise<void>((resolve, reject) => {
      this.$refs.ruleForm.validate((valid: any) => {
        if (valid) {
          resolve()
        }
      })
    })
    arr.push(p0)
    arr.push(this.$refs.activeStore.validate())
    // // 用券门槛
    // let p1 = this.$refs.useCouponStep.doValidate()
    // arr.push(p1)

    // 用券时段
    if (this.$refs.timeRange) {
      let p2 = this.$refs.timeRange.doValidate()
      arr.push(p2)
    }
    // 用券商品
    // if (this.$refs.goodsScope) {
    //   let p3 = this.$refs.goodsScope.validate()
    //   arr.push(p3)
    // }
    //券有效期校验
    if (this.$refs.couponEffectPeriod) {
      arr.push(this.$refs.couponEffectPeriod.validate());
    }
    return arr
  }

  private doTransParams() {
    let params: CouponItem = new CouponItem()
    params.coupons = new CouponInfo()
    params.coupons.enablePayApportion = this.enablePayApportion;

    params.coupons.couponBasicType = 'goods' as any
    params.coupons.name = this.ruleForm.name
    params.coupons.templateId = this.ruleForm.templateId
    params.coupons.cashCouponAttribute = new CashCouponAttribute()
    params.coupons.cashCouponAttribute.faceAmount = this.ruleForm.amount
    // 券有效期
    params.coupons.validityInfo = new ValidityInfo();
    params.coupons.validityInfo.validityType = this.ruleForm.dateType;
    if (this.ruleForm.dateType === "RALATIVE") {
      params.coupons.validityInfo.delayEffectDays = this.ruleForm.dateFrom;
      params.coupons.validityInfo.expiryType = this.ruleForm.expiryType
      if ([ExpiryType.DAYS, ExpiryType.NATURAL_DAY].indexOf(this.ruleForm.expiryType) > -1) {
        params.coupons.validityInfo.validityDays = this.ruleForm.dateTo;
        params.coupons.validityInfo.months = null
      } else if (this.ruleForm.expiryType === ExpiryType.MONTHS) {
        params.coupons.validityInfo.months = this.ruleForm.dateTo;
        params.coupons.validityInfo.validityDays = null
      } else {
        params.coupons.validityInfo.months = null;
        params.coupons.validityInfo.validityDays = null;
      }
    } else {
      // 固定有效期
      if (this.ruleForm.dateFix && this.ruleForm.dateFix[0]) {
        params.coupons.validityInfo.beginDate = (this.ruleForm.dateFix[0]) as any;
      }
      if (this.ruleForm.dateFix && this.ruleForm.dateFix[1]) {
        params.coupons.validityInfo.endDate = (this.ruleForm.dateFix[1]) as any;
      }
    }
    // 用券时段
    params.coupons.useTimeRange = new DateTimeRange()
    if (this.ruleForm.time) {
      // params.coupons.useTimeRange = this.timeTemplate
      params.coupons.useTimeRange = this.ruleForm.time
    } else {
      params.coupons.useTimeRange = new DateTimeRange()
      params.coupons.useTimeRange.dateTimeRangeType = 'ALL' as any
    }
    // todo 用券渠道
    params.coupons.useChannels = new ChannelRange()
    if (this.ruleForm.useFrom === 'step1') {
      params.coupons.useChannels.channelRangeType = 'ALL' as any
      params.coupons.useChannels.channels = []
    } else {
      params.coupons.useChannels.channelRangeType = 'PART' as any
      params.coupons.useChannels.channels = this.ruleForm.from
    }
    // 用券门店
    if (this.ruleForm.storeRange === '{}') {
      let storeRange: StoreRange = new StoreRange()
      if (this.sameStore) {
        storeRange.storeRangeType = 'SAME' as any
      } else {
        storeRange.storeRangeType = 'ALL' as any
      }
      params.coupons.useStores = storeRange
    } else {
      params.coupons.useStores = this.ruleForm.storeRange
    }
    // 用券商品
    params.coupons.pickUpCouponAttribute = new PickUpCouponAttribute()

    params.coupons.pickUpCouponAttribute.pickUpGoods = this.ruleForm.useCouponGood
    params.coupons.pickUpCouponAttribute.pickQty = this.ruleForm.pickQty
    params.coupons.pickUpCouponAttribute.reserve = this.ruleForm.reserve
    params.coupons.pickUpCouponAttribute.reserveStartDay = this.ruleForm.reserve === true ? this.ruleForm.reserveStartDay : null
    params.coupons.pickUpCouponAttribute.reserveEndDay = this.ruleForm.reserve === true ? this.ruleForm.reserveEndDay : null
    // 用券说明
    params.coupons.remark = this.ruleForm.couponProduct
    // 券logo
    params.coupons.logoUrl = this.ruleForm.logoUrl
    // 券码前缀
    params.coupons.codePrefix = this.ruleForm.prefix;
    // 是否支持转赠
    params.coupons.transferable = this.ruleForm.transferable
    params.coupons.templateTag = this.ruleForm.templateTag
    //价格
    params.coupons.salePrice = this.ruleForm.price
    //账款项目
    params.coupons.termsModel = this.ruleForm.termsModel
    //券角标
    params.coupons.couponSubscriptType = this.ruleForm.couponSubscriptType
    //核销链接
    params.coupons.writeOffLink = this.ruleForm.writeOffLink
    //备注
    params.coupons.notes = this.ruleForm.notes
    //每人每天限量
    params.coupons.maxDailyMemberQuotaQty = this.ruleForm.maxDailyMemberQuotaQty    
    return params
  }

  private doBindValue(value: CouponItem) {
    if (value && value.coupons) {
      let coupon: CouponInfo = value.coupons
      this.enablePayApportion = value.coupons.enablePayApportion
      this.ruleForm.name = coupon.name
      this.ruleForm.templateId = coupon.templateId
      if (coupon && coupon.cashCouponAttribute) {
        this.ruleForm.amount = coupon.cashCouponAttribute!.faceAmount
      }
      this.ruleForm.dateType = coupon.validityInfo!.validityType;
      if (this.ruleForm.dateType === "RALATIVE") {
        this.ruleForm.dateFrom = coupon.validityInfo!.delayEffectDays;
        this.ruleForm.expiryType = coupon.validityInfo!.expiryType
        this.ruleForm.dateTo = coupon.validityInfo!.validityDays || coupon.validityInfo!.months;
      } else {
        this.ruleForm.dateFix = [
          DateUtil.format(coupon.validityInfo!.beginDate, "yyyy-MM-dd HH:mm:ss"),
          DateUtil.format(coupon.validityInfo!.endDate, "yyyy-MM-dd HH:mm:ss"),
        ];
      }
      // todo 用券渠道
      if (coupon.useChannels && coupon.templateId) {
        if (coupon.useChannels.channelRangeType === "ALL") {
          this.ruleForm.useFrom = "step1";
          this.ruleForm.from = [];
        } else {
          this.ruleForm.useFrom = "step2";
          if (coupon.useChannels.channels && coupon.useChannels.channels.length > 0) {
            let arrs: string[] = [];
            coupon.useChannels.channels.forEach((item: any) => {
              if (item.id || item.type) {
                if (item.id && item.id !== "-") {
                  arrs.push(item.type + item.id);
                } else {
                  arrs.push(item.type);
                }
              } else {
                arrs.push(item);
              }
            });
            this.ruleForm.from = arrs;
          } else {
            this.ruleForm.useFrom = "step1";
            this.ruleForm.from = [];
          }
        }
      }
      // 用券门店
      this.ruleForm.storeRange = coupon.useStores
      // 用券商品
      if (coupon && coupon.pickUpCouponAttribute) {
        this.ruleForm.useCouponGood = coupon.pickUpCouponAttribute.pickUpGoods
        this.canChooseItems = []
        this.ruleForm.useCouponGood.forEach((item: any, index: number) => {
          if (index === 0) {
            this.canChooseItems.push({
              label: this.formatI18n('/公用/券模板/提货券/用券商品/全部可选'),
              value: this.ruleForm.useCouponGood.length
            })
          } else {
            this.canChooseItems.push({
              label: `${this.ruleForm.useCouponGood.length}${this.formatI18n('/公用/券模板/提货券/用券商品/选')}${this.ruleForm.useCouponGood.length - index}`,
              value: this.ruleForm.useCouponGood.length - index
            })
          }
        })
        if (coupon.pickUpCouponAttribute.pickQty) {
          this.ruleForm.pickQty = coupon.pickUpCouponAttribute.pickQty
          this.ruleForm.reserve = coupon.pickUpCouponAttribute.reserve
          this.ruleForm.reserveStartDay = coupon.pickUpCouponAttribute.reserveStartDay
          this.ruleForm.reserveEndDay = coupon.pickUpCouponAttribute.reserveEndDay
        } else {
          this.ruleForm.pickQty = this.ruleForm.useCouponGood.length
          this.ruleForm.reserve = coupon.pickUpCouponAttribute.reserve || false
          this.ruleForm.reserveStartDay = coupon.pickUpCouponAttribute.reserveStartDay || null
          this.ruleForm.reserveEndDay = coupon.pickUpCouponAttribute.reserveEndDay || null
        }

      }
      // 用券时段
      this.ruleForm.time = coupon.useTimeRange
      // 用券说明
      this.ruleForm.couponProduct = coupon.remark
      // 券logo
      this.ruleForm.logoUrl = coupon.logoUrl
      // 券码前缀
      this.ruleForm.prefix = coupon.codePrefix
      // 是否支持转赠
      this.ruleForm.transferable = coupon.transferable
      this.ruleForm.templateTag = coupon.templateTag
      //价格
      this.ruleForm.price = coupon.salePrice
      //账款项目
      this.ruleForm.termsModel = coupon.termsModel
      //券角标
      this.ruleForm.couponSubscriptType = coupon.couponSubscriptType
      //核销链接
      this.ruleForm.writeOffLink = coupon.writeOffLink
      //备注
      this.ruleForm.notes =coupon.notes
      //每人每天限量
      this.ruleForm.maxDailyMemberQuotaQty = coupon.maxDailyMemberQuotaQty      
    }
  }

  telescopingChange() {
    this.telescoping = !this.telescoping
  }
}