<!--
 * @Author: 黎钰龙
 * @Date: 2023-08-08 17:40:15
 * @LastEditTime: 2025-04-25 10:47:15
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\tabs-edit\TabsEdit.vue
 * 记得注释
-->
<template>
  <div class="tabs-edit-container" :style="{border: border ? '1px solid #d7dfeb' : 'none'}">
    <div class="tabs-container">
      <div class="tabs-header">
        <div :class="currentGoodsTab == index ? 'tabs-item active' : 'tabs-item'" v-for="(item,index) in value" @click="doClickTabs(index)">
          <span>{{i18n('第{0}组').replace(/\{0\}/g,index + 1)}}</span>
          <i class="el-icon-close" v-if="value.length > 1 && !disableRemove" @click.stop="doRemoveTabs(index)"></i>
        </div>
        <div class="tabs-item" @click="doAddTabs" v-if="value.length < maxLength && !disableAdd">
          <i class="el-icon-circle-plus-outline" style="margin-right:6px"></i>
          <span>{{i18n('添加')}}</span>
        </div>
      </div>
      <div ref="slotDiv" v-if="isShowSlot">
        <div v-for="(item,index) in value">
          <!-- el-tabs源码是创建不同的slot，每次切换tab重新挂载组件，而不是更新同一个tab body里的数据 -->
          <slot name="body" :row="value[currentGoodsTab]" :index="index" v-if="index == currentGoodsTab"></slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./TabsEdit.ts">
</script>

<style lang="scss" scoped>
.tabs-edit-container {
  min-width: 650px;
  max-width: 900px;

  .tabs-header {
    display: flex;
    align-items: center;
    min-width: 710px;
    border-bottom: 1px solid #d7dfeb;
  }
  .tabs-item {
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #d7dfeb;
    background-color: rgb(247 249 252);
    width: 90px;
    height: 34px;
    margin-bottom: -1px;
    cursor: pointer;

    .el-icon-close {
      margin-left: 4px;
      color: #d7dfeb;
      font-size: 16px;
      &:hover {
        color: #20a0ff;
      }
    }

    .el-icon-circle-plus-outline {
      font-size: 14px;
    }
  }
  .active {
    border-bottom: 1px solid #ffffff;
    color: #007eff;
    background-color: #ffffff;
  }
}
</style>