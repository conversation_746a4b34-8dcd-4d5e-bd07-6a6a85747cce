import Channel from 'model/common/Channel'
import {ChannelRangeType} from 'model/common/ChannelRangeType'

export default class ChannelRange {
  // 渠道范围类型：ALL——全部； PART——部分；EXCLUDE-排除
  channelRangeType: Nullable<ChannelRangeType> = null
  // 渠道范围
  channels: Channel[] = []
  //忽略更新
  ignore: boolean = false

  constructor(channelRangeType?:Nullable<ChannelRangeType>){
    this.channelRangeType = channelRangeType
  }
}