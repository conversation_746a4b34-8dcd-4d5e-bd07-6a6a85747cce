export enum StateEqualsType {
  INITAIL = 'INITAIL',  //未审核
  INITIAL = 'INITIAL', //未审核
  UNSTART = 'UNSTART',  //未开始
  PROCESSING = 'PROCESSING',  //进行中
  SUSPEND = 'SUSPEND',  //暂停中
  STOPED = 'STOPED',  //已结束
  PLATFORM_AUDIT_ING = 'PLATFORM_AUDIT_ING',
  PLATFORM_AUDIT_FAIL = 'PLATFORM_AUDIT_FAIL',
  AUDITING = 'AUDITING',  //审核中
  REJECTED = 'REJECTED', //已驳回
  AUDITED = 'AUDITED' //审核通过
}

export const StateEqualsTypeMap = {
  [StateEqualsType.INITAIL]: '未审核',
  [StateEqualsType.INITIAL]: '未审核',
  [StateEqualsType.UNSTART]: '未开始',
  [StateEqualsType.PROCESSING]: '进行中',
  [StateEqualsType.SUSPEND]: '暂停中',
  [StateEqualsType.STOPED]: '已结束',
  [StateEqualsType.PLATFORM_AUDIT_ING]: '平台审核中',
  [StateEqualsType.PLATFORM_AUDIT_FAIL]: '平台审核失败',
  [StateEqualsType.AUDITING]: '审核中',
  [StateEqualsType.REJECTED]: '已驳回',
  [StateEqualsType.AUDITED]: '审核通过',
}
