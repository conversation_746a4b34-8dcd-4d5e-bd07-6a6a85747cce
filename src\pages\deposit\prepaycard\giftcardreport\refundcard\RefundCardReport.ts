import {Component, Vue} from 'vue-property-decorator'
import ListWrapper from 'cmp/list/ListWrapper.vue'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import TimeRange from '../../cmp/timerange/TimeRange';
import Gift<PERSON>ardReportApi from 'http/prepay/report/card/GiftCardReportApi';
import GiftCardFilter from 'model/prepay/report/card/GiftCardFilter';
import GiftCardCardHst from 'model/prepay/report/card/GiftCardCardHst';
import CardReportSum from 'model/prepay/report/card/CardReportSum';
import DataUtil from 'pages/deposit/prepaycard/common/DataUtil';
import RSOrg from 'model/common/RSOrg';
import RSOrgFilter from 'model/common/RSOrgFilter';
import OrgApi from 'http/org/OrgApi';
import I18nPage from "common/I18nDecorator";
import PrepayCardTplPermission from "pages/deposit/prepaycard/prepaycardtpl/PrepayCardTplPermission";
import Zone<PERSON>pi from 'http/area/ZoneApi';
import ZoneFilter from 'model/datum/zone/ZoneFilter'
import ChannelManagementFilter from 'model/channel/ChannelManagementFilter';
import ChannelManagementApi from 'http/channelmanagement/ChannelManagementApi';
import ChannelManagement from 'model/channel/ChannelManagement';
import Channel from 'model/common/Channel';
import PermissionMgr from 'mgr/PermissionMgr'
import FormItem from 'cmp/formitem/FormItem';
import SelectStores from 'cmp/selectStores/SelectStores';

@Component({
  name: 'RefundCardReport',
  components: {
    FormItem,
    ListWrapper,
    SubHeader,
    TimeRange,
    SelectStores
  }
})
@I18nPage({
    prefix: [
        '/储值/预付卡/电子礼品卡报表/售卡流水',
        '/储值/预付卡/电子礼品卡报表/退卡流水',
        '/公用/按钮',
        '/公用/提示',
        '/公用/查询条件/提示',
    ],
})
export default class RefundCardReport extends Vue {
    i18n: (str: string, params?: string[]) => string
    expandQuery: boolean = false
    query: GiftCardFilter = new GiftCardFilter()
    queryData: GiftCardCardHst[] = []
    prepayCardTplPermission = new PrepayCardTplPermission()
    sum: CardReportSum = new CardReportSum()
    dataUtil: DataUtil = new DataUtil()
    areaData: any = []
    ZoneFilter: ZoneFilter = new ZoneFilter()
    hasOptionPermission: any
    $refs: any
    // 分页
    page = {
        currentPage: 1,
        total: 0,
        size: 10,
        probeEnabled: null
    }

    channelTypes: any
    channelEquals: any = ''
    channelMap: Map<string, ChannelManagement> = new Map<string, ChannelManagement>();

    get da1qiaoPermission() {
        return PermissionMgr.daqiaoshihuaDingkai()
    }

    created() {
        this.query.occurredTimeAfterOrEqual = this.dataUtil.format(new Date(), 'yyyy-MM-dd')
        this.query.occurredTimeBefore = this.dataUtil.format(new Date(), 'yyyy-MM-dd')
        this.getChannelList()
        this.getList()
        this.getAreaList()
    }

    doSearch() {
        this.page.currentPage = 1
        this.getList()
    }

    doReset() {
        this.query = new GiftCardFilter()
        this.channelEquals = ''
        this.page.currentPage = 1
        this.$refs['timeRange'].reset()
    }
      /**
  * 查询区域
  */
       getAreaList() {
        // this.ZoneFilter.page = 0
        // this.ZoneFilter.pageSize = 10
        ZoneApi.query(this.ZoneFilter).then((res) => {
          if (res.code === 2000) {
            this.areaData = res.data
          } else {
            this.$message.error(res.msg as string)
          }
        })
      }

    /**
     * 查询
     */
    onSearch() {
        this.page.currentPage = 1
        this.getList()
    }

    /**
     * 分页页码改变的回调
     * @param val
     */
    onHandleCurrentChange(val: number) {
        this.page.currentPage = val
        this.getList()
    }

    /**
     * 每页多少条的回调
     * @param val
     */
    onHandleSizeChange(val: number) {
        this.page.size = val
        this.getList()
    }

    /**
     * 表格排序条件
     */
    onSortChange({ column, prop, order }: any) {
        // todo
    }

    private getList() {
        if (this.channelEquals) {
            this.query.channelIdEquals = this.channelMap.get(this.channelEquals)!.channel.id
            this.query.channelTypeEquals = this.channelMap.get(this.channelEquals)!.channel.type
        } else {
            this.query.channelIdEquals = null
            this.query.channelTypeEquals = null
        }
        this.query.page = this.page.currentPage - 1
        this.query.pageSize = this.page.size
        GiftCardReportApi.queryRefundCardHst(this.query).then((resp: any) => {
            if (resp && resp.code === 2000) {
                this.queryData = resp.data
                this.page.total = resp.total
                this.page.probeEnabled = resp.fields ? resp.fields.probeEnabled : null
                //this.getSum()
            } else {
                this.$message.error(resp.msg)
            }
        }).catch((error) => {
            if (error && error.message) {
                this.$message.error(error.message)
            }
        })
    }

    private getKey(channel: Channel) {
        if (channel && channel.type && channel.id) {
          return channel.type as any + channel.id
        }
        return channel.typeId
      }

    private getChannelList() {
        let query = new ChannelManagementFilter();
        query.page = 0
        query.pageSize = 0
        ChannelManagementApi.query(query).then((resp: any) => {
          if (resp && resp.code === 2000) {
            this.channelTypes = resp.data
            for (let channel of this.channelTypes) {
              if (channel.channel && channel.channel.type && channel.channel.id) {
                this.channelMap.set(this.getKey(channel.channel) as string, channel)
              }
            }
            console.log(this.channelMap);
    
          }
        }).catch((error) => {
          if (error && error.message) {
            this.$message.error(error.message)
          }
        })
      }

    isShowSum:boolean = false
    private getSum() {
        GiftCardReportApi.cardHstSum(this.query).then((resp: any) => {
            if (resp && resp.code === 2000) {
                this.sum = resp.data
                this.isShowSum = true
            } else {
              if(resp.code === 2404) return
              this.$message.error(resp.msg)
            }
        }).catch((error) => {
            if (error && error.message) {
                this.$message.error(error.message)
            }
        })
    }

    private handleTimeRange(dateArr: Date[]) {
        this.query.occurredTimeAfterOrEqual = dateArr[0]
        this.query.occurredTimeBefore = dateArr[1]
        this.getList()
    }

    private gotoTplDtl(num: any) {
        this.$router.push({name: 'prepay-card-tpl-dtl', query: {number: num, cardTemplateType: 'GIFT_CARD'}})
    }

    private gotoActivityDtl(id: any) {
        this.$router.push({name: 'gift-card-activity-dtl', query: {activityId: id}})
    }
}
