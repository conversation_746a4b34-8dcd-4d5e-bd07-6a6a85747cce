<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:05
 * @LastEditTime: 2023-12-11 11:19:09
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\main\Air.vue
 * 记得注释
-->
<template>
  <div class="air-container">
    <keep-alive :max="1">
      <router-view :key="key" v-if="$route.meta.keepAlive"></router-view>
    </keep-alive>
    <router-view :key="key" v-if="!$route.meta.keepAlive"></router-view>
  </div>
</template>
<script lang="ts" src="./Air.ts"></script>
<style scoped>
.air-container {
  width: 100%;
  height: 100%;
}
</style>