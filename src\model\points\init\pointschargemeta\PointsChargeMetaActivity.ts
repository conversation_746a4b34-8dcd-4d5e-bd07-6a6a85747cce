import ActivityBody from 'model/common/ActivityBody'

export default class PointsChargeMetaActivity {
  // 活动内容
  body: Nullable<ActivityBody> = null
  // 每花多少积分
  points: Nullable<number> = null
  // 抵多少金额
  amount: Nullable<number> = null
  // 抵现类型：UN_LIMIT-不限制；LIMIT-限制为 consumeAmount元；
  consumeType: Nullable<string> = null
  // 抵现条件金额
  consumeAmount: Nullable<number> = null
  // 单笔抵现条件：UN_LIMIT-不限制；LIMIT-限制为 consumeAmount元；PERCENT_RATE-整单比例
  upperPayType: Nullable<string> = null
  // 抵现条件金额
  upperPayValue: Nullable<number> = null
  // 抵现条件百分比
  upperPayRate: Nullable<number> = null
  // 每人每天积分抵现次数类型：UN_LIMIT-不限制；LIMIT-限memberDailyPayTimesType次数
  memberDailyPayTimesType: Nullable<string> = null
  // 每人每天积分抵现次数
  memberDailyPayTimes: Nullable<number> = null
}