/*
 * @Author: 黎钰龙
 * @Date: 2024-07-05 13:53:37
 * @LastEditTime: 2025-05-29 18:30:45
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\cmp\page-spread-dialog\PageSpreadDialog.ts
 * 记得注释
 */
import FormItem from 'cmp/formitem/FormItem';
import I18nPage from 'common/I18nDecorator';
import { CmsConfigChannel, CmsConfigUtils } from 'model/template/CmsConfig';
import ContentTemplate from 'model/template/ContentTemplate';
import PopularizeResponse from 'model/template/PopularizeResponse';
import CommonUtil from 'util/CommonUtil';
import EnvUtil from 'util/EnvUtil';
import { Component, Vue } from 'vue-property-decorator';
import VueQrcode from 'vue-qrcode'
@Component({
  name: 'PageSpreadDialog',
  components: {
    VueQrcode,
    FormItem
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理',
    '/设置/页面管理'
  ],
  auto: true
})
// 自定义页面 推广弹窗
export default class PageSpreadDialog extends Vue {
  visible: boolean = false
  templateId: string = '' //页面id
  linkInfo: PopularizeResponse[] = []
  channel: CmsConfigChannel | 'ALL' = CmsConfigChannel.WEIXIN //当前弹窗可展示哪些渠道
  selectChannel: Nullable<CmsConfigChannel> = null //当前选择的渠道

  // 当前推广渠道信息
  get currentLinkInfo() {
    if (this.linkInfo?.length === 1) {
      return this.linkInfo[0]
    } else if (this.linkInfo?.length > 1) {
      return this.linkInfo.find(item => item.channel === this.selectChannel)
    } else {
      return new PopularizeResponse()
    }
  }


  get isH5() {
    return this.selectChannel === CmsConfigChannel.H5
  }

  get isWeixin() {
    return this.selectChannel === CmsConfigChannel.WEIXIN
  }

  get isAliApplet() {
    return this.selectChannel === CmsConfigChannel.ALIPAY
  }

  get channelOptions() {
    return this.linkInfo?.map(item => item.channel) || []
  }

  get h5Url() {
    const baseUrl = `${window.location.origin}/phoenix-uni-h5/index.html`
    const path = this.linkInfo.find(item => item.channel === CmsConfigChannel.H5)?.appletPath
    if (path) {
      return `${baseUrl}#${path}`
    } else {
      return ''
    }
  }

  open(pageInfo: ContentTemplate, spreadInfo: PopularizeResponse[], channel: CmsConfigChannel | 'ALL') {
    this.visible = true
    this.channel = channel || 'ALL'
    this.selectChannel = channel === 'ALL' ? (spreadInfo?.length ? spreadInfo[0].channel : CmsConfigChannel.WEIXIN) : channel  //全部渠道 默认选择微信
    this.templateId = pageInfo?.id || ''
    this.linkInfo = spreadInfo as PopularizeResponse[]
  }

  handleClose(done: Function) {
    this.templateId = ''
    done();
  }

  getLabel(channel: CmsConfigChannel) {
    return CmsConfigUtils.getLabel(channel)
  }

  // 复制H5路径
  copyH5Path() {
    if (this.h5Url) {
      CommonUtil.copyToClipboard(this.h5Url)
      this.$message.success(this.i18n('复制成功'))
    } else {
      return this.$message.error(this.i18n('获取H5路径失败'))
    }
  }

  // 复制小程序路径
  copyPath() {
    if (this.currentLinkInfo?.appletPath) {
      CommonUtil.copyToClipboard(this.currentLinkInfo.appletPath)
      this.$message.success(this.i18n('复制成功'))
    } else {
      return this.$message.error(this.i18n('获取小程序路径失败'))
    }
  }

  // 复制短链接
  copyShortLink() {
    if (this.currentLinkInfo?.shortLinkResponse?.shortLink) {
      CommonUtil.copyToClipboard(this.currentLinkInfo?.shortLinkResponse?.shortLink)
      this.$message.success(this.i18n('复制成功'))
    } else {
      return this.$message.error(this.currentLinkInfo?.shortLinkResponse?.errorMsg || this.i18n('获取短链接失败'))
    }
  }

  // 复制小程序链接
  copyAppLink() {
    if (this.currentLinkInfo?.urlSchemeResponse?.urlScheme) {
      const resLink = EnvUtil.getOssUrl() + 'scheme.html?'
      const encodeStr = 't=' + encodeURIComponent(this.currentLinkInfo?.urlSchemeResponse?.urlScheme)
      CommonUtil.copyToClipboard(resLink + encodeStr)
      this.$message.success(this.i18n('复制成功'))
    } else {
      return this.$message.error(this.currentLinkInfo?.urlSchemeResponse?.errorMsg || this.i18n('获取小程序链接失败'))
    }
  }

  doDownLoadQRCode() {
    /* 下载二维码*/
    let img: any = document.getElementById('qrcode')
    let link = document.createElement('a')
    let url = img.getAttribute('src')
    link.setAttribute('href', url as any)
    link.setAttribute('download', 'H5' + this.i18n('二维码') + '.png')
    link.click()
  }

  // 白底二维码下载
  downloadCode() {

    if (this.isH5) {
      this.doDownLoadQRCode()
      return
    }

    var image = new Image();
    image.setAttribute("crossOrigin", "anonymous");
    image.onload = function () {
      var canvas = document.createElement("canvas");
      canvas.width = image.width;
      canvas.height = image.height;
      var context = canvas.getContext("2d");
      context?.drawImage(image, 0, 0, image.width, image.height);
      var url = canvas.toDataURL("image/png"); //得到图片的base64编码数据
      var a = document.createElement("a"); // 生成一个a元素
      var event = new MouseEvent("click"); // 创建一个单击事件
      a.download = "photo"; // 设置图片名称
      a.href = url; // 将生成的URL设置为a.href属性
      a.dispatchEvent(event); // 触发a的单击事件
    };
    image.src = this.currentLinkInfo?.qrCodeResponse?.qrCodeUrl!;
  }
};