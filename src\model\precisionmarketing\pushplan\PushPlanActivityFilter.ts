// 券活动查询过滤
export default class PushPlanActivityFilter {
  // 计划号类似于
  numberLike: Nullable<string> = null
  // 推送计划名称类似于
  nameLike: Nullable<string> = null
  // 类型等于:RepeatPushPlanActivityRule-重复推送;SingleTimePushPlanActivityRule-单次推送;
  typeIn: string[] = []
  // 状态等于: INITAIL-未审核;UNSTART-未开始;PROCESSING-进行中；STOPED-已结束
  stateEquals: Nullable<string> = null
  // 计划开始日期小于
  end: Nullable<Date> = null
  // 计划开始日期大于等于
  begin: Nullable<Date> = null
  // 页数
  page: Nullable<number> = null
  // 页面大小
  pageSize: Nullable<number> = null
}