import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import SaveTopicRequest from '../../model/datum/topic/SaveTopicRequest'
import Topic from '../../model/datum/topic/Topic'
import TopicFilter from '../../model/datum/topic/TopicFilter'

export default class TopicApi {
  /**
   * 删除主题
   * 删除主题。
   * 
   */
  static deleteBatch(idSet: Array<string>): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/topic/deleteBatch`, {}, {
      params: {
        idSet: idSet
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 更新
   * 更新。
   * 
   */
  static modify(body: SaveTopicRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/topic/modify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询主题
   * 查询主题。
   * 
   */
  static query(body: TopicFilter): Promise<Response<Topic[]>> {
    return ApiClient.server().post(`/v1/topic/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除主题
   * 删除主题。
   * 
   */
  static remove(id: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/topic/remove`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存
   * 保存。
   * 
   */
  static save(body: SaveTopicRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/topic/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
