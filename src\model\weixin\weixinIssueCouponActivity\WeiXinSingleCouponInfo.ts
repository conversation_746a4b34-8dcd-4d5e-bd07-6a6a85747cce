import { ExpireRefundType } from './ExpireRefundType'
import { RefundAlgorithm } from './RefundAlgorithm'

export default class WeiXinSingleCouponInfo {
  // 服务商编号
  merchantPid: Nullable<string> = null
  // 券批次号
  batchNumber: Nullable<string> = null
  // 券售价
  price: Nullable<number> = null
  // 使用须知
  notice: Nullable<string> = null
  // 过期退款方式
  expireRefundType: Nullable<ExpireRefundType> = ExpireRefundType.MANUAL_REFUND
  // 退款金额算法
  refundAlgorithm: Nullable<RefundAlgorithm> = RefundAlgorithm.BY_FAV
  // 图片
  image: Nullable<string> = null
  couponInfo?: any
}