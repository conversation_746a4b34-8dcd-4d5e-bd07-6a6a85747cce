/*
 * @Autor: 司浩
 * @Description: 
 * @Date: 2022-02-23 16:45:15
 * @LastEditors: 黎钰龙
 * @LastEditTime: 2024-02-05 10:09:11
 * @FilePath: \phoenix-web-ui\src\model\promotion\UnionActivityQuery.ts
 */
import ActivityGroupType from 'model/common/ActivityGroupType';
// 聚合活动查询过滤
export default class UnionActivityQuery {
  // 活动号类似于
  numberLike: Nullable<string> = null
  numberEquals: Nullable<string> = null
  // 活动名称类似于
  nameLike: Nullable<string> = null
  // 活动名称等于
  nameStartsWith: Nullable<string> = null
  // 主题名称类似于
  topicNameLikes: Nullable<string> = null
  // 主题名称等于
  topicNameEquals: Nullable<string> = null
  // 状态等于: INITAIL-未审核;UNSTART-未开始;PROCESSING-进行中数量；STOPED-已结束,PLATFORM_AUDIT_ING- 平台审核中,PLATFORM_AUDIT_FAIL-平台审核失败
  stateEquals: Nullable<string> = null
  // 活动开始日期小于
  end: Nullable<Date> = null
  // 活动开始日期大于等于
  begin: Nullable<Date> = null
  // 页数
  page: Nullable<number> = null
  // 页面大小
  pageSize: Nullable<number> = null
  //活动状态不包括
  stateNotEquals?: any
  // 活动状态包括
  activityStatesIn?: Nullable<string[]> = null
  // 活动类型包括
  typeIn: Nullable<string[]> = null
  // 外部活动号
  outerNumberIdLike: Nullable<string> = null
}