import BWeimobExtLimitGoodsCategoryRule from 'model/equityCard/default/BWeimobExtLimitGoodsCategoryRule'
import BWeimobExtLimitGoodsGroupRule from 'model/equityCard/default/BWeimobExtLimitGoodsGroupRule'
import BWeimobExtLimitGoodsRule from 'model/equityCard/default/BWeimobExtLimitGoodsRule'
import { LimitedGoodsType } from 'model/equityCard/default/LimitedGoodsType'

// 微盟平台券适用商品规则
export default class BWeimobExtGoodsUseRule {
  /**
   * 是否限制商品
   * false 不限商品, true 限制商品
   */
  limitedGoods: Nullable<boolean> = null
  /**
   * 限制商品类型
   * goods 限制商品类型 ,goodsCategory 限制商品类别,  goodsGroup 限制商品组
   */
  limitedGoodsType: Nullable<LimitedGoodsType> = null
  // 限制商品规则
  limitGoodsTypeRule: Nullable<BWeimobExtLimitGoodsRule> = null
  // 限制商品类别规则
  limitGoodsCategoryTypeRule: Nullable<BWeimobExtLimitGoodsCategoryRule> = null
  // 限制商品类别规则
  limitGoodsGroupRule: Nullable<BWeimobExtLimitGoodsGroupRule> = null
}