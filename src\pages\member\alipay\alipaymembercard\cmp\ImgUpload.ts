/*
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:05
 * @LastEditTime: 2023-11-01 17:30:11
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\member\alipay\alipaymembercard\cmp\ImgUpload.ts
 * 记得注释
 */
import {Component, Prop, Vue, Watch} from 'vue-property-decorator'
import EnvUtil from 'util/EnvUtil'

@Component({
  name: 'ImgUpload',
  components: {}
})
export default class ImgUpload extends Vue {
  uploadHeaders: any = {}
  logo = ''
  @Prop()
  value: string
  @Prop()
  limit: number
  @Watch('value')
  onValueChange(value: string) {
    if (value) {
      this.logo = value
    }
  }
  get uploadUrl() {
    return EnvUtil.getServiceUrl() + 'v1/upload/upload'
  }
  created() {
    let locale = sessionStorage.getItem('locale')
    this.uploadHeaders = {
			locale: locale ? locale : "zh_CN",
			time_zone: new Date().getTimezoneOffset(),
			marketingCenter: sessionStorage.getItem("marketCenter"),
		};
    const authorization = EnvUtil.getUniAuthorization()
    if (authorization) {
      this.uploadHeaders.authorization = authorization
    }
  }
  onUploadLogo(response: any, file: any, fileList: any) {
    if (response && response.code === 2000) {
      // this.$message.success('上传成功')
      this.logo = response.data.url
      this.$emit('input', this.logo)
    } else {
      this.$message.error(response.msg)
    }
  }
  beforeAvatarUpload(file: any) {
    const isJPG = ['image/jpeg', 'image/png', 'image/jpg', 'image/bmp', 'image/gif'].indexOf(file.type) > -1
    const isLt2M = file.size / (1024 * 1000) < Number(this.limit)
    if (!isJPG) {
      this.$message.error('上传头像图片只能是BMP/JPG/PNG/JPEG/GIF格式!')
    }
    if (!isLt2M) {
      this.$message.error(`上传图片大小不能超过${this.limit}M`)
    }
    return isJPG && isLt2M
  }
  doRemove() {
    this.logo = ''
    this.$emit('input', this.logo)
  }
}