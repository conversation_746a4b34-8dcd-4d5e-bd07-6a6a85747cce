import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import BLuodaoCustomWhiteList from 'model/grade/BLuodaoCustomWhiteList'

export default class WhiteListApi {
  /**
   * 查询白名单信息
   * 查询白名单信息
   * 
   */
  static get(): Promise<Response<BLuodaoCustomWhiteList>> {
    return ApiClient.server().post(`/v1/luodao/custom/whiteList/get`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存白名单信息
   * 保存白名单信息
   * 
   */
  static save(body: BLuodaoCustomWhiteList): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/luodao/custom/whiteList/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

}