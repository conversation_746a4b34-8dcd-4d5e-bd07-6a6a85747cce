<template>
  <div class="member-balance-promotion-dtl">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button key="1" v-if="permission.auditable && data.body.state === 'INITAIL' && !isOaActivity" @click="audit" type="primary">审核</el-button>
        <el-button key="2" v-if="permission.editable" @click="copy">复制</el-button>
        <el-button key="3" v-if="['UNSTART', 'PROCESSING'].indexOf(data.body.state) > -1 && permission.terminable" @click="stop">终止</el-button>
        <el-button key="4" v-if="['INITAIL','REJECTED','UNSTART', 'PROCESSING'].indexOf(data.body.state) > -1 && permission.editable" @click="edit">修改</el-button>
        <el-button key="5" v-if="['INITAIL','REJECTED'].indexOf(data.body.state) > -1 && permission.editable" @click="del"
          type="danger">删除</el-button>
      </template>
    </BreadCrume>
    <div style="margin: 20px;">
      <el-row>
        <el-col :span="8" style="padding-right: 20px">
          <p class="text-secondary">
            <span>活动号：</span>
            <span no-i18n>{{ data.body.activityId }}</span>
          </p>
          <p class="text-primary" :title="data.body.name" no-i18n>{{ data.body.name }}</p>
        </el-col>
        <el-col :span="8">
          <p class="text-secondary">状态</p>
          <p class="text-primary">
            <ActivityStateTag :stateEquals="data.body.state"></ActivityStateTag>
          </p>
        </el-col>
        <el-col :span="8">
          <p class="text-secondary">所属主题</p>
          <p class="text-primary" no-i18n>
            {{ data.body.topicName|nullable }}
          </p>
        </el-col>
      </el-row>
      <hr />
      <el-row style="padding-left: 20px">
        <el-col :span="16">
          <el-col :span="3" class="text-secondary">
            {{ formatI18n('/营销/积分活动/积分活动/商品满额加送积分/详情页面/活动信息/活动时间：') }}
          </el-col>
          <el-col :span="18" style="display: flex;">
            <div>
              {{ data.body.beginDate|dateFormate2 }}
              {{ formatI18n('/营销/积分活动/积分活动/商品满额加送积分/新建页面/活动信息/至') }}
              {{ data.body.endDate|dateFormate2 }}
            </div>
            <DateTimeConditionDtl style="margin-left: 30px" :value="data.dateTimeCondition"></DateTimeConditionDtl>
          </el-col>  
          <!-- <span class="text-secondary">活动时间：</span>
          <span no-i18n>{{ data.body.beginDate|dateFormate2 }}</span>
          <span>至</span>
          <span no-i18n>{{ data.body.endDate|dateFormate2 }}</span> -->
        </el-col>
      </el-row>
      <el-row style="padding-left: 20px;margin-top:20px">
        <el-col :span="16" style="display: flex;">
          <div class="text-secondary" style="min-width: 70px">{{ i18n('/营销/积分活动/积分活动/商品满额加送积分/详情页面/活动信息/活动门店：') }}</div>
          <ActiveStoreDtl style="width: 60%;min-width: 800px;margin-top: -8px" no-i18n :data="data.body.stores"></ActiveStoreDtl>
        </el-col>
      </el-row>
      <el-row style="padding-left: 20px;margin-top:20px">
        <el-col :span="16" style="display: flex;">
          <div class="text-secondary" style="min-width: 70px">{{ formatI18n('/储值/预付卡/电子礼品卡活动/编辑页面', '活动说明') + '：' }}</div>
          <div no-i18n v-html="data.body.remark ? data.body.remark.replace(/\n/g, '<br/>'): '--'"></div>
        </el-col>
      </el-row>
    </div>
    <div style="background-color: #eeeff1;height: 15px;"></div>
    <div style="margin: 20px">
      <div style="font-weight: 600">支付优惠规则</div>
      <div style="margin-top: 20px;padding-left: 20px">
        <el-row>
          <el-col :span="3" class="text-secondary">适用商品：</el-col>
          <el-col :span="12">
            <GoodsScopeDtl no-i18n :goodsMatchRuleMode="goodsMatchRuleMode" :goods="data.goods" />
          </el-col>
        </el-row>
        <el-row style="margin-top: 25px">
          <el-col :span="3" class="text-secondary">优惠门槛：</el-col>
          <el-col :span="12" v-if="data.favThreshold">
            <i18n k="/储值/会员储值/储值支付活动/详情页面/适用商品消费满{0}元及以上">
              <template slot="0">
                <span style="font-weight: 600">&nbsp;{{data.favThreshold|amount}}&nbsp;</span>
              </template>
            </i18n>
          </el-col>
          <el-col :span="12" v-else>
            不限制
          </el-col>
        </el-row>
        <el-row style="margin-top: 25px">
          <el-col :span="3" class="text-secondary">优惠规则设置：</el-col>
          <el-col :span="12" v-if="data.gradeSameStepValue !== null">
            <i18n v-if="data.strategy === 'BY_AMOUNT'" k="/储值/会员储值/储值支付活动/详情页面/适用商品消费每满{0}元，立减{1}元，不足部分不优惠">
              <template slot="0">&nbsp;{{ data.gradeSameStepValue.stepValues[0].threshold|amount }}&nbsp;</template>
              <template slot="1">&nbsp;{{ data.gradeSameStepValue.stepValues[0].value|amount }}&nbsp;</template>
            </i18n>
            <i18n v-if="data.strategy === 'BY_QTY'" k="/储值/会员储值/储值支付活动/详情页面/适用商品中每单品消费每满{0}件，立减{1}元，不足部分不优惠">
              <template slot="0">&nbsp;{{ data.gradeSameStepValue.stepValues[0].threshold }}&nbsp;</template>
              <template slot="1">&nbsp;{{ data.gradeSameStepValue.stepValues[0].value|amount }}&nbsp;</template>
            </i18n>
            <i18n v-if="data.strategy === 'BY_FULL_AMOUNT'" k="/储值/预付卡/预付卡支付活动/编辑页面/适用商品消费满{0}元，立减{1}元，不足部分不优惠">
              <template slot="0">&nbsp;{{ data.gradeSameStepValue.stepValues[0].threshold|amount }}&nbsp;</template>
              <template slot="1">&nbsp;{{ data.gradeSameStepValue.stepValues[0].value|amount }}&nbsp;</template>
            </i18n>
            <i18n v-if="data.strategy === 'BY_FULL_QTY'" k="/储值/预付卡/预付卡支付活动/编辑页面/适用商品中每单品消费满{0}件，立减{1}元，不足部分不优惠">
              <template slot="0">&nbsp;{{ data.gradeSameStepValue.stepValues[0].threshold }}&nbsp;</template>
              <template slot="1">&nbsp;{{ data.gradeSameStepValue.stepValues[0].value|amount }}&nbsp;</template>
            </i18n>

          </el-col>
          <el-col :span="12" v-if="data.gradeDifferentStepValue != null" class="rule-table">
            {{i18n('/储值/会员储值/储值支付活动/编辑页面/不同等级会员适用 不同规则')}}<br /><br />
            <el-row class="rule-table-header">
              <el-col :span="4">会员等级</el-col>
              <el-col :span="20">储值支付优惠规则</el-col>
            </el-row>
            <el-row class="rule-table-line" v-for="grade of gradeList" :key="grade.code">
              <el-col :span="4" no-i18n>{{ '[' + grade.code + '] ' + grade.name }}</el-col>
              <el-col :span="20" v-if="gradeDifferentStepValueMap.hasOwnProperty(grade.code)">
                <i18n v-if="data.strategy === 'BY_AMOUNT'" k="/储值/会员储值/储值支付活动/详情页面/适用商品消费每满{0}元，立减{1}元，不足部分不优惠">
                  <template slot="0">&nbsp;{{ gradeDifferentStepValueMap[grade.code].stepValues[0].threshold|amount }}&nbsp;</template>
                  <template slot="1">&nbsp;{{ gradeDifferentStepValueMap[grade.code].stepValues[0].value|amount }}&nbsp;</template>
                </i18n>
                <i18n v-if="data.strategy === 'BY_QTY'" k="/储值/会员储值/储值支付活动/详情页面/适用商品中每单品消费每满{0}件，立减{1}元，不足部分不优惠">
                  <template slot="0">&nbsp;{{ gradeDifferentStepValueMap[grade.code].stepValues[0].threshold|amount }}&nbsp;</template>
                  <template slot="1">&nbsp;{{ gradeDifferentStepValueMap[grade.code].stepValues[0].value|amount }}&nbsp;</template>
                </i18n>
              </el-col>
              <el-col :span="20" v-if="!gradeDifferentStepValueMap.hasOwnProperty(grade.code)">
                不可参与储值支付优惠
              </el-col>
            </el-row>
          </el-col>
        </el-row>
        <el-row style="margin-top: 25px">
          <el-col :span="3" class="text-secondary">叠加促销：</el-col>
          <el-col :span="21">{{data.excludePromotion?i18n('是'):i18n('否')}}</el-col>
        </el-row>

        <el-row style="margin-top: 25px">
          <el-col :span="3" class="text-secondary">{{i18n("/储值/会员储值/储值支付活动/编辑页面/是否需要整单储值支付")}}:</el-col>
          <el-col :span="21">{{data.fullBalancePay?i18n('是'):i18n('否')}}</el-col>
        </el-row>
      </div>
    </div>
    <div style="background-color: #eeeff1;height: 15px;" v-if="data.body.budget"></div>
    <MarketingBudgetDtl :budget="data.body.budget" activityType="MemberBalanceReductionActivityRule"></MarketingBudgetDtl>
  </div>
</template>

<script lang="ts" src="./MemberBalancePromotionDtl.ts">
</script>

<style lang="scss">
.member-balance-promotion-dtl {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: auto;

  .text-primary {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    font-weight: 500;
    font-style: normal;
    font-size: 20px;
    color: #515151;
  }

  .text-secondary {
    // text-overflow: ellipsis;
    // white-space: nowrap;
    // overflow: hidden;
    color: rgba(51, 51, 51, 0.65);
  }

  .rule-table {
    width: 70%;

    .rule-table-header {
      padding: 10px;
      background-color: #e6e6e6;
      border: 1px solid #e6e6e6;
    }

    .rule-table-line {
      padding: 10px;
      border: 1px solid #e6e6e6;
    }
  }
}
</style>