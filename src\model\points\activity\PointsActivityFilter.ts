import ActivityGroupType from 'model/common/ActivityGroupType';

export default class PointsActivityFilter {
	// 活动号类似于
	numberLike: Nullable<string> = null;
	numberEquals: Nullable<string> = null;
	// 活动名称类似于
	nameLike: Nullable<string> = null;
	// 主题名称类似于
	topicNameLikes: Nullable<string> = null;
	// 类型等于:积分兑换商品规则-PointsExchangeGoodsActivityRule 积分商品范围规则-GainPointsGoodsActivityRule；
	// 商品满数量加送积分规则-GoodsGainAdditionalPointsByQtyActivityRule
	// 商品积分加速规则-GoodsGainPointsSpeedActivityRule；
	// 全场积分加速规则-GainPointsSpeedActivityRule；
	// 全场积分加速规则- GoodsGainAdditionalPointsActivityRule；
	// 积分抵现活动规则-PointsChargeMetaActivityRule；
	typeEquals: Nullable<string> = null;
	stateEquals: Nullable<string> = null;
	stateNotEquals: Nullable<string> = null;
	// 活动开始日期小于
	end: Nullable<Date> = null;
	// 活动开始日期大于等于
	begin: Nullable<Date> = null;
	// 页数
	page: Nullable<number> = null;
	// 页面大小
	pageSize: Nullable<number> = null;
	// 活动组类型
	groupType: Nullable<ActivityGroupType> = null;
	// 活动说明类似于
	remarkLikes: Nullable<string> = null;
}
