import { ExpireRefundType } from './ExpireRefundType'
import { RefundAlgorithm } from './RefundAlgorithm'

class BatchNumberInfo {
  // 批次号
  batchNumber: Nullable<string> = null
  // 图片
  image: Nullable<string> = null
  // 券信息
  couponInfo?: any
}
export default class WeiXinPayCouponBagInfo {
  // 券包图片
  images: string[] = []
  // 券售价
  price: Nullable<number> = null
  // 使用须知
  notice: Nullable<string> = null
  // 过期退款方式
  expireRefundType: Nullable<ExpireRefundType> = ExpireRefundType.MANUAL_REFUND
  // 退款金额算法
  refundAlgorithm: Nullable<RefundAlgorithm> = RefundAlgorithm.BY_FAV
  // 券批次
  // batchNumbers: string[] = []
  batchNumbers: BatchNumberInfo[] = []
  // 券批次数量
  batchCount: Nullable<number> = null
  // 发券总数量
  totalCount: Nullable<number> = null
}