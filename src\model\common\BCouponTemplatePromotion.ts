/*
 * @Author: 黎钰龙
 * @Date: 2023-12-01 13:44:03
 * @LastEditTime: 2024-01-18 15:48:20
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\common\BCouponTemplatePromotion.ts
 * 记得注释
 */
export default class BCouponTemplatePromotion {
    // 券模板号/卡模板号
    templateNumber: Nullable<string> = null
    // 促销单号 billNumber、topicName
    proNums: any[] = []
    // 是否追加数据： false:删除原数据，重新保存
    append: Nullable<boolean> = null
  }
  