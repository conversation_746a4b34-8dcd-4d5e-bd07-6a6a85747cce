<template>
  <div class="cmp-time-range">
    <el-radio-group @change="doUseDateChange" v-model="ruleForm.useDate">
      <el-radio label="ALL">{{ i18n('任意时段可用') }}</el-radio>
      <el-radio label="USEABLE" v-if="!fixedAll">{{ i18n('指定时间段内可用') }}</el-radio>
      <el-radio label="UNUSEABLE" v-if="!fixedAll">{{ i18n('指定时间段内不可用') }}</el-radio>
    </el-radio-group>
    <div style="background:#F7F9FC;padding:10px 8px;min-width:630px" v-if="ruleForm.useDate !== 'ALL'">
      <el-form v-if="ruleForm.useDate !== 'ALL'" :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm"
        style="position: relative">
        <el-radio-group @change="doUseCouponTime" v-model="ruleForm.useCouponTime">
          <el-row style="display: flex;align-items: center;margin-top:-15px">
            <el-radio label="day">{{ i18n('每日') }}</el-radio>
            <template v-if="ruleForm.useCouponTime === 'day'">
              <el-form-item prop="day" style="display: inline-block;margin-left: 3px" class="cur-time">
                <el-time-picker size="mini" :default-value="defaultValue" :editable="false" ref="day" style="width: 250px !important;" is-range
                  v-model="ruleForm.day" :disabled="ruleForm.useCouponTime !== 'day'" value-format="HH:mm" format="HH:mm" @change="doEmitParams"
                  :end-placeholder="formatI18n('/公用/券模板', '结束时间')" :start-placeholder="formatI18n('/公用/券模板', '开始时间')">
                </el-time-picker>
              </el-form-item>
            </template>
          </el-row>
          <el-row style="display: flex;margin-top:-20px">
            <el-radio label="month">{{ formatI18n('/公用/券模板', '每月') }}</el-radio>
            <div v-if="ruleForm.useCouponTime === 'month'" style="margin-top:10px">
              <el-form-item class="cur-time" prop="whichDay">
                <el-select size="mini" @change="doEmitParams" multiple v-model="ruleForm.whichDay" style="width:550px">
                  <el-option :label="item" :value="item" v-for="item in 31" :key="item">{{ item }}</el-option>
                </el-select>
              </el-form-item>
              <el-form-item prop="month" style="position: relative;top: 5px;" class="cur-time">
                <el-time-picker size="mini" :default-value="defaultValue" :editable="false" ref="month" style="width: 240px !important;" is-range
                  v-model="ruleForm.month" range-separator="~" @change="doEmitParams" value-format="HH:mm" format="HH:mm"
                  :end-placeholder="formatI18n('/公用/券模板', '结束时间')" :start-placeholder="formatI18n('/公用/券模板', '开始时间')">
                </el-time-picker>
              </el-form-item>
            </div>
          </el-row>
          <el-row style="display: flex;flex-wrap:wrap;margin-top:-20px">
            <el-radio label="week" style="padding-bottom:0 !important">{{ formatI18n('/公用/券模板', '每周') }}</el-radio>
            <template v-if="ruleForm.useCouponTime === 'week'">
              <div style="margin-top:10px">
                <el-form-item class="cur-time" prop="weekWhich">
                  <el-checkbox-group size="small" ref="checkBtn" @change="doEmitParams" style="display: inline-block;margin-right: 10px"
                    v-model="ruleForm.weekWhich">
                    <el-checkbox-button :disabled="ruleForm.useCouponTime !== 'week'" :key="1" border :label="1">
                      {{ formatI18n('/公用/券模板', '周一') }}
                    </el-checkbox-button>
                    <el-checkbox-button :disabled="ruleForm.useCouponTime !== 'week'" :key="2" :label="2">
                      {{ formatI18n('/公用/券模板', '周二') }}
                    </el-checkbox-button>
                    <el-checkbox-button :disabled="ruleForm.useCouponTime !== 'week'" :key="3" :label="3">
                      {{ formatI18n('/公用/券模板', '周三') }}
                    </el-checkbox-button>
                    <el-checkbox-button :disabled="ruleForm.useCouponTime !== 'week'" :key="4" :label="4">
                      {{ formatI18n('/公用/券模板', '周四') }}
                    </el-checkbox-button>
                    <el-checkbox-button :disabled="ruleForm.useCouponTime !== 'week'" :key="5" :label="5">
                      {{ formatI18n('/公用/券模板', '周五') }}
                    </el-checkbox-button>
                    <el-checkbox-button :disabled="ruleForm.useCouponTime !== 'week'" :key="6" :label="6">
                      {{ formatI18n('/公用/券模板', '周六') }}
                    </el-checkbox-button>
                    <el-checkbox-button :disabled="ruleForm.useCouponTime !== 'week'" :key="7" :label="7">
                      {{ formatI18n('/公用/券模板', '周日') }}
                    </el-checkbox-button>
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item prop="week" class="cur-time">
                  <el-time-picker size="mini" :default-value="defaultValue" :editable="false" ref="week"
                    style="width: 250px !important" :disabled="ruleForm.useCouponTime !== 'week'" is-range
                    v-model="ruleForm.week" :range-separator="i18n('至')" value-format="HH:mm" format="HH:mm" @change="doEmitParams"
                    :end-placeholder="formatI18n('/公用/券模板', '结束时间')" :start-placeholder="formatI18n('/公用/券模板', '开始时间')">
                  </el-time-picker>
                </el-form-item>
              </div>
            </template>
          </el-row>
        </el-radio-group>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts" src="./TimeRange.ts">
</script>

<style lang="scss">
.cmp-time-range {
  display: inline-block;
  .cur-time {
    margin-bottom: 0 !important;
    .el-form-item__content {
      margin-left: 0 !important;
    }
  }

  .demo-ruleForm {
    .el-radio {
      padding-top: 20px;
      padding-bottom: 20px;
    }
  }
  .el-checkbox-button.is-focus .el-checkbox-button__inner {
    // border: var(--border-solid);
  }

  .el-date-editor .el-range-separator {
    line-height: 24px !important;
    width: auto !important;
  }
  .el-date-editor .el-range__icon {
    line-height: 24px !important;
  }

  .el-checkbox-button .el-checkbox-button__inner {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 68px !important;
    height: 28px !important;
  }
}
</style>