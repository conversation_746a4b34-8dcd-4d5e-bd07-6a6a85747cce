<template>
  <div class="birthday-gift-dtl">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <span v-if="hasOptionPermission('/营销/营销/节日有礼/生日送礼', '规则维护')">
           <el-button v-if="!giftRule.stopped" @click="doModify"
                      type="primary">{{ formatI18n('/公用/按钮', '修改') }}</el-button>
        <el-button @click="switchState" :type="giftRule.stopped?'success':'danger'">
          <span v-if="giftRule.stopped">{{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/详情', '启用') }}</span>
          <span v-else>{{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/详情', '禁用') }}</span>
        </el-button>
        </span>
      </template>
    </BreadCrume>
    <div style="overflow: auto;height: 95%">
      <div class="content">
        <FormItem :label="formatI18n('/权益/生日权益初始化/生日权益初始化','权益生效时间')">
          {{ giftDayI18n }}
        </FormItem>
        <FormItem :label="formatI18n('/权益/生日权益初始化/生日权益初始化','礼包设置')">
          <div v-if="giftRule&&giftRule.allSameGiftBag">
            <div> {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/新建/礼包设置/不同等级会员送相同礼包') }}</div>
            <div class="gift-table">
              <div>{{ giftPointCountI18n(pointsCount) }}</div>
              <div style="padding-bottom: 30px;" v-if="giftRule.allSameGiftBag
                    && giftRule.allSameGiftBag.couponItems
                    && giftRule.allSameGiftBag.couponItems.length > 0">
                {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/新建/赠送券：') }}
                <span style="display: inline-grid;position: relative;">
                <div v-for="item in giftRule.allSameGiftBag.couponItems">
                     {{ item.qty }} {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/详情/礼包设置', '张') }}
                  <el-button :title="item.coupons.name" style="text-align: left;width: 200px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap" @click="doCheckDtl({}, item)" type="text">{{ item.coupons.name }}</el-button>
                </div>
              </span>
              </div>
            </div>
          </div>
          <div v-else-if="giftRule&&giftRule.differentGiftBag">
            {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/新建/礼包设置/不同等级会员送不同礼包') }}
            <el-table :data="giftBagList" style="width: 70%">
              <el-table-column :label="formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/详情/礼包设置/表格','会员等级')" prop="gradeName"
                               width="180"/>
              <el-table-column :label="formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/详情/礼包设置/表格','礼包设置')">
                <template slot-scope="scope">
                  <div>
                    <div>
                      {{ giftPointCountI18n(scope.row.gift.points) }}
                    </div>
                    <div v-if="scope.row.gift.couponItems && scope.row.gift.couponItems.length > 0">
                      {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/新建/赠送券：') }}
                      <span style="display: inline-grid">
                        <div v-if="scope.row.gift.couponItems && scope.row.gift.couponItems.length > 0"
                             v-for="item in scope.row.gift.couponItems">{{ item.qty }}
                      {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/详情/礼包设置', '张') }}
                      <el-button :title="item.coupons.name" style="width: 200px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;text-align: left" @click="doCheckDtl({}, item)" type="text">{{ item.coupons.name }}
                      </el-button>
                    </div>
                      </span>

                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </FormItem>
      </div>
    </div>

    <SelectStoreActiveDtlDialog
        :child="child"
        :dialogShow="dialogShow"
        :parent="parent"
        @dialogClose="doDialogClose">
    </SelectStoreActiveDtlDialog>
  </div>
</template>

<script lang="ts" src="./BirthdayGiftDtl.ts">
</script>


<style lang="scss">
.birthday-gift-dtl {
  background-color: white;
  overflow: hidden;
  height: 100%;
  width: 100%;

  .subTitle {
    font-size: 16px;
    padding-top: 20px;
    padding-left: 30px;
  }

  .content {
    padding: 50px;

    .el-table {
      border-left: 1px solid #d7dfeb;
      border-right: 1px solid #d7dfeb;
      border-top: 1px solid #d7dfeb;
    }

    .el-table::before {
      height: 0;
    }

    .el-table td {
      border-bottom: 1px solid #d7dfeb;
    }

    .qf-form-item .qf-form-label {
      width: 170px !important;
    }

    .qf-form-item .qf-form-content {
      margin-left: 170px !important;
      line-height: 36px;
    }

    .gift-table {
      border: 1px solid #ddd;
      padding: 2em;
    }
  }

  .el-switch__core {
    background-color: grey;
  }

  .el-switch.is-checked .el-switch__core {
    background-color: #33cc00
  }
}
</style>