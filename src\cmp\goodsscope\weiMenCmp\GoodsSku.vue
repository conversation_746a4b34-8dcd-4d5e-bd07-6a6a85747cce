<template>
    <div class="goods-sku">
        <div class="search-box">
            <div class="box-item">
                <el-select v-model="searchType" style="width: 104px;">
                    <el-option v-for="item in searchTypeList" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-input style="width: 175px;" :placeholder="formatI18n('/公用/券模板/微盟适用商品', '搜索商品')" v-model="searchValue">
                    <i slot="suffix" class="el-input__icon el-icon-search" @click.stop="queryGoods"></i>
                </el-input>
            </div>
            <!-- <div class="box-item" style="margin-left: 12px;">
                <el-select v-model="groupType" style="width: 104px;">
                    <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-cascader v-model="classifyId" :options="classifyList" @change="handleChange">
                </el-cascader>
            </div> -->
            <div class="box-item" style="margin-left: 12px;">
                <el-input v-model="minSalePrice" @change="minInputChange"
                    :placeholder="formatI18n('/公用/券模板/微盟适用商品', '最低价格')" type="number" style="width: 88px;"></el-input>
                <span style="margin: 0 5px;">-</span>
                <el-input v-model="maxSalePrice" @change="maxInputChange"
                    :placeholder="formatI18n('/公用/券模板/微盟适用商品', '最高价格')" type="number" style="width: 88px;"></el-input>
            </div>
            <div class="box-item" style="margin-left: 12px;">
                <el-checkbox v-model="shelfChecked" style="font-size: 14px;">{{ formatI18n('/公用/券模板/微盟适用商品', '仅上架中')
                }}</el-checkbox>
            </div>
            <div class="box-item" style="margin-left: 12px; flex: 1; display: flex; justify-content: flex-end;">
                <el-button type="primary" @click="queryGoods">{{ formatI18n('/公用/菜单', '搜索') }}</el-button>
            </div>
        </div>
        <div class="sku-box" :style="{ height: (height - 110) + 'px' }" v-loading="loading">
            <div class="sku-list" v-if="skuList.length > 0">
                <div class="sku-item" v-for="item in skuList" @click.stop="doSelectGood(item)"
                    :class="{ active: isSelected(item) }">
                    <div class="sku-img">
                        <img :src="item.defaultImageUrl" alt="">
                    </div>
                    <div class="sku-content">
                        <div class="sku-title">{{ item.title }}</div>
                        <div class="sku-price" v-if="item.goodsPrice">¥ {{ item.goodsPrice.maxSalePrice }}</div>
                    </div>
                    <i class="el-icon-check ic-active"></i>
                </div>
            </div>
            <empty :height="height - 110" v-else :emptyText="formatI18n('/公用/提示/暂无数据')" />
        </div>
        <div class="change-page">
            <el-checkbox v-model="allSelect" style="margin-right: 16px;" @change="doChangeAll">{{ formatI18n('/公用/券模板/全选')
            }}</el-checkbox>
            <span style="flex: 1">{{ formatI18n('/公用/券模板/微盟适用商品', '共') }}{{ skuTotal }}{{ formatI18n('/公用/券模板/微盟适用商品', '条')
            }}</span>
            <el-pagination @current-change="handleCurrentChange" :current-page.sync="currentPage" :page-size="20" background
                layout="prev, pager, next,  jumper" :total="skuTotal">
            </el-pagination>
        </div>
    </div>
</template>
<script lang="ts">
import { Vue, Component, Prop, Watch } from "vue-property-decorator";
import AmountToFixUtil from 'util/AmountToFixUtil'
import BQueryGoodsRequest from 'model/common/weimob/BQueryGoodsRequest'
import BCategory from 'model/common/weimob/BCategory'
import BWeimobClassify from 'model/common/weimob/BWeimobClassify'
import BGoods from 'model/common/weimob/BGoods'
import IdName from 'model/common/IdName'
import WeimobApi from 'http/coupon/template/WeimobApi'
import empty from './empty.vue'
@Component({
    components: { empty },
})
export default class GoodsSku extends Vue {
    @Prop({
        default: 400
    })
    height: number
    @Prop()
    skuIds: IdName[]
    @Prop()
    categoryList: BCategory[]
    @Prop()
    groupList: BWeimobClassify[]

    searchTypeList = [
        { value: 1, label: this.formatI18n("/公用/券模板/微盟适用商品/商品名称") },
        { value: 2, label: this.formatI18n("/公用/券模板/微盟适用商品/商品编码") },
        { value: 3, label: this.formatI18n("/公用/券模板/微盟适用商品/规格条码") },
        { value: 4, label: this.formatI18n("/公用/券模板/微盟适用商品/规格编码") }
    ]
    searchType: number = 1
    searchValue: string = ''
    classifyList: []
    classifyId: any = []
    shelfChecked: boolean = false
    currentPage: number = 1
    pageSize: number = 20
    minSalePrice: any = ''
    maxSalePrice: any = ''

    skuList: BGoods[] = []
    includeGoodsIds: IdName[] = []
    skuTotal: number = 0
    isIndeterminate: boolean = true
    allSelect: boolean = false
    loading: boolean = false

    @Watch('skuIds', {
        deep: true
    })
    onValueChange(value: IdName[]) {
        this.doBindValue()
    }
    mounted() {
        this.queryGoodsList()
        this.doBindValue()
    }

    doBindValue() {
        this.includeGoodsIds = this.skuIds
        this.checkAllSelect()
    }

    handleSizeChange(val: number) {
        this.pageSize = val
        this.currentPage = 1
        this.queryGoodsList()
    }
    handleCurrentChange(val: number) {
        this.currentPage = val
        this.queryGoodsList()
    }

    handleChange(value: any) {
        console.log(value)
    }

    minInputChange() {
        this.minSalePrice = AmountToFixUtil.formatAmount(this.minSalePrice, 9999999999, 0, '')
    }

    maxInputChange() {
        this.maxSalePrice = AmountToFixUtil.formatAmount(this.maxSalePrice, 9999999999, 0, '')
    }

    isSelected(good: BGoods) {
        return this.includeGoodsIds.findIndex(item => item.id == good.goodsId) > -1
    }

    doSelectGood(good: BGoods) {
        const index = this.includeGoodsIds.findIndex(item => item.id == good.goodsId)
        if (index == -1) {
            this.includeGoodsIds.push({
                id: good.goodsId + '',
                name: good.title
            })
        } else {
            this.includeGoodsIds.splice(index, 1)
        }
        this.checkAllSelect()
        this.$emit('change', this.includeGoodsIds)
    }

    doChangeAll() {
        if (this.allSelect) {
            this.skuList.map(good => {
                const index = this.includeGoodsIds.findIndex(item => item.id == good.goodsId)
                if (index == -1) {
                    this.includeGoodsIds.push({
                        id: good.goodsId + '',
                        name: good.title
                    })
                }
            })
        } else {
            this.skuList.map(good => {
                const index = this.includeGoodsIds.findIndex(item => item.id == good.goodsId)
                if (index > -1) {
                    this.includeGoodsIds.splice(index, 1)
                }
            })
        }
        this.$emit('change', this.includeGoodsIds)
    }

    queryGoods() {
        this.queryGoodsList()
    }

    checkAllSelect() {
        const flagIndex = this.skuList.findIndex(item => {
            const id = item.goodsId
            return this.includeGoodsIds.findIndex(good => good.id == id) == -1
        })
        this.allSelect = flagIndex == -1 && this.skuList.length > 0
    }

    async queryGoodsList() {
        const params = new BQueryGoodsRequest()
        params.pageNum = this.currentPage
        params.pageSize = this.pageSize
        params.searchType = this.searchType
        params.search = this.searchValue
        params.minSalePrice = this.minSalePrice
        params.maxSalePrice = this.maxSalePrice
        if (this.shelfChecked) params.goodsStatus = 0
        this.loading = true
        try {
            const { data, total } = await WeimobApi.queryGoodsList(params)
            this.skuTotal = total || 0
            this.skuList = data || []
            this.checkAllSelect()
            this.loading = false
        } catch (error) {
            this.loading = false
            this.$message.error((error as Error).message);
        }
    }

}
</script>
<style lang="scss" scoped>
.goods-sku {
    width: 100%;

    .search-box {
        width: 100%;
        height: 56px;
        padding: 0 16px;
        border-bottom: 1px solid #edeef2;
        border-top: 1px solid #edeef2;
        background: #f7f8fa;
        display: flex;
        align-items: center;

        .box-item {
            display: flex;
            align-items: center;
            ::v-deep .el-input__inner {
                line-height: 1px !important;
            }
        }
    }

    .sku-box {
        width: 100%;
        overflow-y: auto;

        .sku-list {
            display: flex;
            justify-content: flex-start;
            flex-wrap: wrap;
            padding-top: 12px;
            padding-left: 12px;
        }

        .sku-item {
            width: calc(33.33% - 12px);
            margin-bottom: 12px;
            margin-right: 12px;
            height: 94px;
            border-radius: 2px;
            overflow: hidden;
            cursor: pointer;
            border: 1px solid #edeef2;
            padding: 11px 12px;
            display: flex;
            display: flex;
            justify-content: space-between;

            &:nth-child(3n) {
                margin-right: 0;
            }

            &:hover {
                border: 1px solid #006aff;
            }

            .ic-active {
                opacity: 0;
            }

            &.active {
                border: 1px solid #006aff;
                position: relative;

                &::after {
                    content: "";
                    position: absolute;
                    bottom: 0;
                    right: 0;
                    width: 0;
                    height: 0;
                    z-index: 9;
                    border-left: 30px solid transparent;
                    border-right: 0 solid transparent;
                    border-bottom: 30px solid #006aff;
                }

                .ic-active {
                    opacity: 1;
                    font-size: 16px;
                    color: #ffffff;
                    position: absolute;
                    bottom: 0;
                    right: 0;
                    z-index: 10;
                }
            }

            .sku-img {
                width: 70px;
                height: 70px;
                border-radius: 2px;
                margin-right: 8px;

                img {
                    width: 100%;
                    height: 100%;
                }
            }

            .sku-content {
                flex: 1;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                line-height: 20px;
                color: #626573;

                .sku-title {
                    overflow: hidden;
                    -webkit-line-clamp: 2;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                }
            }
        }
    }

    .change-page {
        width: 100%;
        height: 54px;
        padding: 0 16px;
        border: 1px solid #edeef2;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
}
</style>