import ActivityStateCountResult from 'model/common/ActivityStateCountResult'
import MemberBalancePromotionActivity from 'model/payment/member/MemberBalancePromotionActivity'

export default class MemberBalancePromotionActivityQueryResult {
  // 计数结果
  countResult: Nullable<ActivityStateCountResult> = null
  // 总数
  total: Nullable<number> = null
  // 总页数
  pageCount: Nullable<number> = null
  // 页码
  page: Nullable<number> = null
  // 页面大小，大于0
  pageSize: Nullable<number> = null
  // 活动
  result: MemberBalancePromotionActivity[] = []
}