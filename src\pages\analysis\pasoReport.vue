<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:03
 * @LastEditTime: 2023-08-03 17:03:05
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\analysis\pasoReport.vue
 * 记得注释
-->
<template>
	<div class="paso-report">
		<!-- <iframe v-if="url" width="100%" height="100%" :src="url" frameborder="0"></iframe> -->
		<div class="coupon-content">
			<div class="content-title">{{ formatI18n('/分析/paso报表', 'paso报表')}}</div>
			<el-row style="margin-top: 10px">
				<el-col :span="8" style="padding: 10px" v-for="(item,index) in pasoList" :key="index" v-show="item.url">
					<div class="content-block">
						<div class="content-subtitle">
							{{item.title}}
						</div>
						<div class="content-text"></div>
						<div style="margin-top: 10px">
							<el-button
								@click="doGoView(item.url)"
								type="primary"
								>{{ formatI18n('/分析/paso报表', '立即查看')}}</el-button
							>
						</div>
					</div>
				</el-col>
			</el-row>
		</div>
	</div>
</template>
<script lang="ts" src="./pasoReport.ts"></script>
<style lang="scss" scoped>
.paso-report {
	width: 100%;
	.coupon-content {
		padding: 0 30px 0 20px;
		.content-title {
			font-size: 18px;
      color: #242633;
			padding-left: 10px;
			padding-top: 15px;
		}
		.content-flex {
			display: flex;
			justify-content: flex-start;
			margin: 30px 0;
		}
		.content-block {
			// height: 184px;
			margin-right: 2%;
			padding: 30px 20px;
			background-color: #f9f9f9;
			.content-subtitle {
				font-size: 18px;
				color: #333333;
				margin-bottom: 10px;
			}
			.content-text {
                // height: 60px;
                height: 10px;
			}
		}
	}
}
</style>