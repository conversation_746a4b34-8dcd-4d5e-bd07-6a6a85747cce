/*
 * @Author: 申鹏渤
 * @Date: 2023-11-17 13:37:16
 * @LastEditTime: 2025-04-24 10:13:45
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\v2\coupon\collectpoints\CollectPointsReward.ts
 * 记得注释
 */
import MemberIdent from 'model/member_v2/common/MemberIdent'
import AwardValue from 'model/promotion/bigWheelActivity/AwardValue'
import { PrizeType } from 'model/promotion/groupBookingActivity/PrizeType'

export default class CollectPointsReward extends MemberIdent {
  // uuid
  uuid: Nullable<string> = null
  // 活动号
  activityNumber: Nullable<string> = null
  // 活动名称
  activityName: Nullable<string> = null
  // 会员id
  memberId: Nullable<string> = null
  // 发生时间
  occurredTime: Nullable<Date> = null
  // 奖品类型
  prizeType: Nullable<PrizeType> = null
  // 奖品id
  awardId: Nullable<string> = null
  // 奖品名称
  awardName: Nullable<string> = null
  // 收货人姓名
  consigneeName: Nullable<string> = null
  // 收货人手机号
  consigneePhone: Nullable<string> = null
  // 收货人手机号
  consigneeAddress: Nullable<string> = null
  // 版本
  version: Nullable<number> = null
  // 奖品图片
  image: Nullable<string> = null
  // 奖品说明
  remark: Nullable<string> = null
  // 奖品值
  awardValue: Nullable<AwardValue> = null
  // 交易id
  tradeId: Nullable<string> = null
}