<!--
 * @Author: 黎钰龙
 * @Date: 2024-03-18 10:29:24
 * @LastEditTime: 2024-03-18 10:36:17
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\couponTemplateDtlItem\couponTemplateDtlBtn\CouponDtlBtn.vue
 * 记得注释
-->
<template>
  <div style="display:inline-block">
    <span class="span-btn" @click="doViewCouponDetail">{{title}}</span>
    <SelectStoreActiveDtlDialog :child="child" :dialogShow="dialogShow" @dialogClose="doDialogClose">
    </SelectStoreActiveDtlDialog>
  </div>
</template>

<script lang="ts" src="./CouponDtlBtn.ts">
</script>

<style lang="scss" scoped>
</style>