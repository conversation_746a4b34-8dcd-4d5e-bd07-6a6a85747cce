import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import FloatBlock from 'cmp/floatblock/FloatBlock';
import ListWrapper from 'cmp/list/ListWrapper';
import I18nPage from 'common/I18nDecorator';
import SystemConfigApi from 'http/systemConfig/SystemConfigApi';
import IdName from 'model/common/IdName';
import RSCostPartyFilter from 'model/common/RSCostPartyFilter';
import BPayMethodConfig from 'model/systemConfig/BPayMethodConfig';
import BPayMethodConfigUpdate from 'model/systemConfig/BPayMethodConfigUpdate';
import { Component, Vue } from 'vue-property-decorator';
@Component({
  name: 'PayMethodList',
  components: {
    BreadCrume,
    ListWrapper,
    FloatBlock
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/储值/会员储值/储值充值单'
  ],
  auto: true
})
export default class PayMethodList extends Vue {

  query: any = {
    idNameLikes: ''
  }
  queryData: IdName[] = []  //当前列表展示数据
  originData: IdName[] = [] //原始表单数据
  selected: IdName[] = []
  $refs: any
  panelArray: any = []
  checkedAll: boolean = false
  newIns: IdName = new IdName() //新增支付方式信息对象
  oldUpdateIns: IdName = new IdName()  //修改支付方式信息原对象
  updateIns: IdName = new IdName()  //修改支付方式信息对象
  dialogVisible: boolean = false  //修改支付方式的弹窗

  created() {
    this.panelArray = [
      {
        name: this.formatI18n('/公用/菜单/支付方式'),
        url: ''
      },
    ]
    this.getList()
  }

  doSearch() {
    if (!this.query.idNameLikes) {
      return this.queryData = this.originData || []
    }
    this.queryData = this.originData.filter((item) => {
      return item.id?.indexOf(this.query.idNameLikes)! > -1 || item.name?.indexOf(this.query.idNameLikes)! > -1
    })
  }

  doReset() {
    this.query = new RSCostPartyFilter()
    this.getList()
  }

  /**
   * 查询
   */
  onSearch() {
    this.getList()
  }

  handleSelectionChange(val: any) {
    this.selected = val
  }

  deleteBatch() {
    if (this.selected.length === 0) {
      this.$message.warning(this.formatI18n('/资料/券承担方/请选择需要删除的记录'))
      return
    }
    this.$confirm(this.formatI18n('/资料/券承担方/是否确认删除？'), this.formatI18n('/资料/券承担方/提示'), {
      confirmButtonText: this.formatI18n('/资料/券承担方/确定'),
      cancelButtonText: this.formatI18n('/资料/券承担方/取消'),
      type: 'warning'
    }).then(() => {
      const body = new BPayMethodConfig()
      body.payMethods = this.selected
      SystemConfigApi.deletePayMethodConfig(body).then((resp: any) => {
        if (resp.code === 2000) {
          this.$message.success(this.formatI18n('/资料/券承担方/删除成功'))
          this.getList()
        }
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    });
  }

  getList() {
    SystemConfigApi.getPayMethodConfig().then((resp) => {
      if (resp.code === 2000) {
        this.queryData = resp.data?.payMethods || []
        this.originData = resp.data?.payMethods || []
      } else {
        throw new Error(resp.msg!)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  // 新增支付方式
  add() {
    if (!this.newIns.id || !this.newIns.name) {
      return this.$message.error(this.i18n('请输入支付方式代码和名称'))
    }
    if (this.queryData.some(item => item.id === this.newIns.id)) {
      return this.$message.error(this.i18n('代码不允许与已有支付方式重复'))
    }
    const body = new BPayMethodConfig()
    body.payMethods = [this.newIns]
    SystemConfigApi.savePayMethodConfig(body).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(this.formatI18n('/资料/券承担方/添加成功'))
        this.getList()
        this.clear()
      } else {
        this.$message.error(resp.msg)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  showUpdateDialog(row: IdName) {
    this.oldUpdateIns = JSON.parse(JSON.stringify(row))
    this.updateIns = JSON.parse(JSON.stringify(row))
    this.dialogVisible = true
  }

  update() {
    if (!this.updateIns?.id || !this.updateIns?.name) {
      return this.$message.warning(this.i18n("/公用/js提示信息/请填写必填项"))
    }
    if (this.originData.some((item) => item.id === this.updateIns.id && this.updateIns.id !== this.oldUpdateIns.id)) {
      return this.$message.error(this.i18n('代码不允许与已有支付方式重复'))
    }
    const body = new BPayMethodConfigUpdate()
    body.newPayMethod = this.updateIns
    body.oldPayMethod = this.oldUpdateIns
    SystemConfigApi.updatePayMethodConfig(body).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(this.formatI18n('/资料/券承担方/修改成功'))
        this.getList()
        this.dialogVisible = false
      } else {
        this.$message.error(resp.msg)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  clear() {
    this.newIns = new IdName()
  }

  remove(id: string) {
    this.$confirm(this.formatI18n('/资料/券承担方/是否确认删除？'), this.formatI18n('/资料/券承担方/提示'), {
      confirmButtonText: this.formatI18n('/资料/券承担方/确定'),
      cancelButtonText: this.formatI18n('/资料/券承担方/取消'),
      type: 'warning'
    }).then(() => {
      const body = new BPayMethodConfig()
      body.payMethods = this.queryData.filter((item) => item.id === id)
      SystemConfigApi.deletePayMethodConfig(body).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.formatI18n('/资料/券承担方/删除成功'))
          this.getList()
        }
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    });
  }

  checkedAllRow() {
    if (this.checkedAll) {
      for (let row of this.queryData) {
        this.$refs.table.toggleRowSelection(row, true)
      }
    } else {
      this.$refs.table.clearSelection();
    }
  }
};