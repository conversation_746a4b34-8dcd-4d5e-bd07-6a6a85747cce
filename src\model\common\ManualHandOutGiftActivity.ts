/*
 * @Author: 申鹏渤
 * @Date: 2023-12-05 09:36:51
 * @LastEditTime: 2024-10-09 14:12:42
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\common\ManualHandOutGiftActivity.ts
 * 记得注释
 */
import BaseCouponActivity from 'model/coupon/activity/BaseCouponActivity'
import GiftInfo from 'model/common/GiftInfo'
import IdName from 'model/common/IdName'
import MemberRule from 'model/precisionmarketing/tag/tagrule/customize/member/MemberRule'
import TagInfo from 'model/common/TagInfo'
import { ManualHandOutType } from 'model/common/ManualHandOutType'
import PushGroup from 'model/precisionmarketing/pushplan/PushGroup'

export default class ManualHandOutGiftActivity extends BaseCouponActivity {
  // 手机号
  mobiles: string[] = []
  // 会员标签
  tags: TagInfo[] = []
  // 启用微信模板消息
  enableWeiXinMessage: Nullable<boolean> = null
  // 券礼包
  giftInfo: Nullable<GiftInfo> = null
  // 发生组织
  occurredOrg: Nullable<IdName> = null
  // 复制对象活动号
  prevNumber: Nullable<string> = null
  // 是否携带复制对象的发券信息，如果为ture则必须传入复制对象活动号
  withPrevIssueObj: Nullable<boolean> = null
  // 是否导入了手机号文件：true是false不是，null未导入过文件
  importMobile: Nullable<string> = null
  // 当前已经导入的数量
  importCount: Nullable<number> = null
  // 当前已经发放数量
  issueCount: Nullable<number> = null
  // 当前已经发放数量
  handOutTime: Nullable<Date> = null
  // 会员属性
  memberRule: Nullable<MemberRule> = null
  // 类型
  manualHandOutType: Nullable<ManualHandOutType> = null
  // 是否完成会员计算
  memberCalculationComplete: Nullable<boolean> = null
  // 是否允许在CRM后台核销
  enableWriteOffByPhx: Nullable<boolean> = null
  // 发券类型：MEMBER_SAME 会员相同券,MEMBER_DIFF 会员不同券
  issueType: Nullable<string> = null
  // 参与人群
  rule: Nullable<PushGroup> = null
}