<template>
  <div class="member-card">
    <member-card-title :icon="require('~assets/image/member/<EMAIL>')"
                       :title="i18n('会员资产')"></member-card-title>
    <div class="member-asset-table">
      <div class="asset-cell">
        <member-form-item :label="i18n('可用积分')">
          <el-button type="text" @click="onAssetDetail('score-query-list')">{{ i18n("明细") }}</el-button>
        </member-form-item>
        <div class="cell-number">{{ dtl.points | fmt }}</div>
      </div>
      <div class="cell-divider"></div>
      <div class="asset-cell">
        <member-form-item :label="i18n('可用券（张）')">
          <el-button type="text"
                     class="btn-small" @click="onAssetDetail('coupon-list')">{{ i18n("明细") }}
          </el-button>
          <el-button type="text"
                     class="btn-small"
                     @click="onSendCoupon"
                     v-if="isStandardMember && dtl.state === 'Using' && hasOptionPermission('/会员/会员管理/会员资料', '发券')">
            {{ formatI18n("/会员/会员资料/详情界面/会员资产转移", "发券") }}
          </el-button>
        </member-form-item>
        <div class="cell-number">
          <template v-if="dtl.couponCount && dtl.couponCount > 0">
          {{ dtl.couponCount }}
          </template>
          <template v-else>--</template>
        </div>
      </div>
      <div class="cell-divider"></div>
      <div class="asset-cell">
        <member-form-item :label="i18n('可用储值（元）')">
          <el-button type="text" @click="onAssetDetail('store-value-query')">{{ i18n("明细") }}</el-button>
        </member-form-item>
        <div class="cell-number">{{ dtl.balance | fmt }}</div>
      </div>
    </div>

    <member-card-title :title="i18n('可用预付卡（张）')"
                       style="margin-top: 24px"></member-card-title>
    <template v-if="prePayCards.length > 0">
    <el-row class="member-prepay-card-container"
            :gutter="16">
      <el-col :span="8"
              v-for="item in prePayCards"
              :key="item.code">
        <div class="member-prepay-card">
          <member-form-item>
            {{ i18n("卡号") }}：{{ item.code }}
            <el-button @click="onPrepayCardDetail(item)" type="text"
                       slot="right">{{ i18n("明细") }}
            </el-button>
          </member-form-item>
          <div class="amount">
            <span class="amount-label">{{ i18n("余额(元)") }}</span>
            <span class="amount-number">{{ item.balance | amount }}</span>
          </div>
        </div>

      </el-col>
    </el-row>
    </template>
    <empty-data v-else></empty-data>
  </div>
</template>
<script lang="ts"
        src="./MemberInfoAsset.ts">
</script>
<style lang="scss"
       scoped>
.member-asset-table {
  height: 80px;
  background: #F8F9FC;
  border-radius: 4px;
  display: flex;
  align-items: center;

  .asset-cell {
    width: 33%;
    padding: 0 16px;

    .cell-number {
      font-weight: 600;
      font-size: 18px;
      color: #242633;
      line-height: 24px;
    }
  }

  .cell-divider {
    height: 48px;
    width: 1px;
    background: #D7DFEB;
  }

  .btn-small + .btn-small {
    margin-left: 0 !important;
    padding: 0 !important;
  }
}

.member-prepay-card-container {
  max-height: 220px;
  overflow-y: auto;
  margin-bottom: -15px;
}

.member-prepay-card {
  padding: 10px 16px;
  background: linear-gradient(135deg, #FAFCFF 0%, #E0EFFF 100%);
  border-radius: 8px;
  border: 1px solid #D7DFEB;
  margin-bottom: 16px;

  .amount {
    margin-top: 10px;
    .amount-label {
      font-weight: 400;
      font-size: 12px;
      color: #79879E;
      line-height: 18px;
    }
    .amount-number {
      font-weight: 600;
      font-size: 24px;
      color: #242633;
      line-height: 32px;
      margin-left: 8px;
    }
  }


  ::v-deep .member-form-content {
    font-weight: 600;
    font-size: 14px;
    color: #242633;
    line-height: 22px;
  }
}
</style>
