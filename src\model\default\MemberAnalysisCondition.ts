import { CustomerProfileCategory } from 'model/analysis/CustomerProfileCategory'
import { AnalysisPropType } from 'model/default/AnalysisPropType'
import { ConditionOperationType } from 'model/default/ConditionOperationType'

export default class MemberAnalysisCondition {
  // 
  type: Nullable<AnalysisPropType> = null
  // 
  memberProp: Nullable<string> = null
  //
  memberPropName: Nullable<string> = null
  // 
  tagId: Nullable<string> = null
  //
  tagName: Nullable<string> = null
  // 
  operate: Nullable<ConditionOperationType> = null
  // 
  value: Nullable<string> = null
  // 
  beginDate: Nullable<Date> = null
  // 
  endDate: Nullable<Date> = null
  // 
  beginValue: Nullable<string> = null
  // 
  endValue: Nullable<string> = null


  //
  fieldType: Nullable<string> = null
  // 
  feature: Nullable<string> = null
  //
  relation: Nullable<string> = null
  //
  range: Nullable<string> = null
  //
  number: Nullable<string> = null
  //
  beginNumber: Nullable<string> = null
  //
  endNumber: Nullable<string> = null
}