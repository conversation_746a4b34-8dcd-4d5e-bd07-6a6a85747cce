<template>
  <div class="drawer-view">
    <div class="drawer-view-left" :style="visible?'':'display: none;'" @click="close">&nbsp;</div>
    <div class="drawer-view-right" :style="{width: width, right: visible?'0': '-' + width}" v-show="visible">
      <div class="drawer-view-content" :style="contentStyle">
        <div class="drawer-view-head">
          <div class="drawer-view-head-title" v-if="$slots.title">
            <slot name="title"></slot>
          </div>
          <div class="drawer-view-head-title" v-else>
            {{title}}
          </div>
          <a class="drawer-view-head-close" href="javascript: void(0)" @click="close">×</a>
        </div>
        <div style="height: calc(100% - 65px);padding: 0 20px">
          <slot name="content"></slot>
          <slot></slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./Drawerx.ts">
</script>

<style lang="scss">
.drawer-view {
  .drawer-view-left {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 998;
    background-color: black;
    opacity: 0.5;
    transition: opacity 0.5s ease-in-out;
  }

  .drawer-view-right {
    position: absolute;
    top: 0;
    right: -40%;
    height: 100%;
    z-index: 999;
    transition: all 0.5s ease-in-out;

    .drawer-view-content {
      width: 100%;
      height: 100%;

      .drawer-view-head {
        display: flex;
        padding: 20px;

        .drawer-view-head-title {
          font-size: 18px;
          width: calc(100% - 50px);
        }

        .drawer-view-head-close {
          font-size: 35px;
          width: 50px;
          line-height: 20px;
          text-align: right;
        }
      }
    }
  }
}

</style>
