import User from 'model/user/User'
import FunctionPackagesExpireInfo from './FunctionPackagesExpireInfo'

export default class UserLoginResult {
  // 租户id,兼容鼎力云老前端代码
  tenantId: Nullable<string> = null
  // 用户信息
  user: Nullable<User> = null
  // license客户名称
  customer: Nullable<string> = null
  // 是否开启了严格校验模式
  useStrict: Nullable<boolean> = null
  // 密码最大有效天数，-1表示永久有效
  passwordValidityPeriod: Nullable<number> = null
  // 未登录最大天数限制，-1表示不限制
  maxLoginValidityPeriod: Nullable<number> = null
  // 登录状态：NORMAL-正常；PWD_EXPIRE-密码已到期；LOGIN_EXPIRE-指定时间内未登录
  state: Nullable<String> = null
  // 过期重置密码需要的token，如果登录状态不为NORMAL，则返回对应重置密码token，重置密码时候需要携带此token信息
  resetToken: Nullable<string> = null
  // 功能包续费提醒
  expirationFunctionPackages: Nullable<FunctionPackagesExpireInfo[]> = null
}