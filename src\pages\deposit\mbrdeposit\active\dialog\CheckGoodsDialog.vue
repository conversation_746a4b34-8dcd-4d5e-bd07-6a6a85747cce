<template>
    <el-dialog :before-close="doBeforeClose" :close-on-click-modal="false"
               :visible.sync="dialogShow"
               :title="title" append-to-body class="select-store-active-dtl-dialog">
       <div class="wrap">
           <el-table
                   :data="data"
                   border>
               <el-table-column :label="formatI18n('/权益/积分/积分调整单详情/明细', '序号')" type="index">

               </el-table-column>
               <el-table-column :label="this.formatI18n('/公用/券模板', '代码')" prop="id">
               </el-table-column>
               <el-table-column :label="title === formatI18n('/公用/券模板', '用券商品') ? formatI18n('/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送全场现金券的时候并且该券用券商品不是全部商品的时候/点击券名称/点击查看商品', '商品名称') : formatI18n('/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送全场现金券的时候并且该券用券门店不是全部门店的时候/点击券名称/点击查看门店', '门店名称')" prop="name">
               </el-table-column>
           </el-table>
       </div>
    </el-dialog>
</template>

<script lang="ts" src="./CheckGoodsDialog.ts">
</script>

<style lang="scss">
    .select-store-active-dtl-dialog{
        display: flex;
        align-items: center;
        justify-content: center;
        .el-dialog{
          width: 980px;
          height: 640px;
          margin: 0 !important;
        }
        .wrap{
            height: 440px;
            overflow: auto;
            .item{
                width: 228px;
                height: 108px;
                border: 1px solid #c7c7c7;
                border-radius: 10px;
                display: inline-block;
                margin-bottom: 24px;

                &:nth-child(odd) {
                    margin-right: 12px;

                }
                &:nth-child(even) {
                    margin-left: 12px;
                }
            }
        }
    }
</style>