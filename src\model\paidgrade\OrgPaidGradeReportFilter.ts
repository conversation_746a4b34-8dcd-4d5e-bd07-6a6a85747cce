export default class OrgPaidGradeReportFilter {
    // 会员标识等于
    memberIdEquals: Nullable<string> = null
    // 发生时间开始,格式：yyyy-MM-dd
    occurredTimeGreaterOrEquals: Nullable<Date> = null
    // 发生时间结束,格式：yyyy-MM-dd
    occurredTimeLess: Nullable<Date> = null
    // 发生组织
    storeIdEquals: Nullable<string> = null
    // 发生区域
    zoneIdEquals: Nullable<string> = null
    // 套餐名称类似于
    gradeFeeSetNameLikes: Nullable<string> = null
    // 套餐名称起始于
    gradeFeeSetNameStartsWith: Nullable<string> = null
    // 支付方式类似于
    payTypeLikes: Nullable<string> = null
    // 支付方式等于
    payTypeEquals: Nullable<string> = null
    // 交易号类似于
    transNoLikes: Nullable<string> = null
    // 交易号等于
    transNoEquals: Nullable<string> = null
    // 页数
    page: Nullable<number> = null
    // 页面大小
    pageSize: Nullable<number> = null
}