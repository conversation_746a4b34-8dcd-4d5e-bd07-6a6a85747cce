/*
 * @Author: 黎钰龙
 * @Date: 2025-01-06 20:02:17
 * @LastEditTime: 2025-05-22 11:16:21
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\cmp\place-panel\PlacePanel.ts
 * 记得注释
 */
import { Vue, Component, Prop } from 'vue-property-decorator';
import CollapseTransition from 'util/collapse-transition';
import draggable from 'vuedraggable';
import { CmsConfigChannel } from 'model/template/CmsConfig';
@Component({
  name: 'PlacePanel',
  components: {
    CollapseTransition,
    draggable,
  },
})
export default class PlacePanel extends Vue {
  $refs: any;
  @Prop({ type: Array, default: () => [] })
  templateList: Array<any>;
  @Prop() advertiseChannel: Nullable<CmsConfigChannel[]>; //当前页面可用的投放渠道
  openedItems: Array<any> = [0, 1, 2, 3, 4];
  isHover: boolean = false;
  hoverActiveId: string = '';

  // get ossSourceUrl() {
  //   return this.$store.state.credential?.host + '/-/cms/thumbnail/';
  // }

  // get picNocontent() {
  //   return this.ossSourceUrl + 'pic_nocontent.png';
  // }

  // 根据渠道过滤组件列表，当前展示的组件取多个渠道可展示组件的交集
  get filterTemplateList() {
    const resArr = JSON.parse(JSON.stringify(this.templateList))
    resArr.forEach((item: any) => {
      item.widgets = item.widgets.filter((widget: any) => {
        return this.advertiseChannel?.every((channel: CmsConfigChannel) => {
          return widget.availableChannels.includes(channel);
        });
      });
    });
    return resArr
  }

  mounted() {}
  clickSub(e: any, subIndex: any) {
    const i = this.openedItems.indexOf(subIndex);
    if (i !== -1) {
      this.openedItems.splice(i, 1);
    } else {
      this.openedItems.push(subIndex);
    }
  }
  isOpen(subIndex: any) {
    return this.openedItems.indexOf(subIndex) > -1;
  }
  itemMouseenter(templateId: string) {
    this.isHover = true;
    this.hoverActiveId = templateId;
  }
  itemMouseleave() {
    this.isHover = false;
  }
  itemClick(item: any) {}
  cloneItem(item: any) {
    console.log(item);
    this.$emit('dragClone', item);
  }
  onEnd(item: any) {
    console.log('dragEnd ==>', item);
    this.$emit('dragEnd', item);
  }
  panelMove(evt: any) {
    this.$emit('panelMove', evt.relatedContext.index);
  }
}
