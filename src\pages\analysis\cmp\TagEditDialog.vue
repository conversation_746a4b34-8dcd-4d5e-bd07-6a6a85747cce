<template>
  <el-dialog :before-close="doBeforeClose" :close-on-click-modal="false" :visible.sync="dialogShow"
             class="tag-edit-dialog" :title="formatI18n('/分析/自定义标签/自定义标签/修改/标签编辑')">
    <div class="wrap">
      <FormItem :label="formatI18n('/分析/自定义标签/自定义标签/标签名称')">
        <div style="line-height: 36px">{{ tagId }}</div>
      </FormItem>
      <FormItem :label="formatI18n('/分析/自定义标签/自定义标签/标签值')">
        <CodeInput
            :recieveArray="tags"
            @blur="doBlur"
            @selectCode="onSelectCodeDialog"
            style="width: 400px">
        </CodeInput>
        <div style="color: red" v-if="flag">{{ formatI18n('/分析/自定义标签/自定义标签/修改/标签选项不能为空') }}</div>
      </FormItem>
    </div>
    <div class="dialog-footer" slot="footer">
      <el-button @click="doModalClose('cancel')">{{ formatI18n('/公用/按钮/取消') }}</el-button>
      <el-button @click="doModalClose('confirm')" size="small" type="primary">{{
          formatI18n('/公用/按钮/确定')
        }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./TagEditDialog.ts">
</script>

<style lang="scss">
.tag-edit-dialog {
  display: flex;
  align-items: center;
  justify-content: center;

  .el-dialog {
    width: 582px;
    margin: 0 !important;
  }

  .wrap {
    width: 560px;
    margin: 0 auto;
    overflow: auto;

    .item {
      width: 228px;
      height: 108px;
      border: 1px solid #c7c7c7;
      border-radius: 10px;
      display: inline-block;
      margin-bottom: 24px;

      &:nth-child(odd) {
        margin-right: 12px;

      }

      &:nth-child(even) {
        margin-left: 12px;
      }
    }
  }

  .el-dialog__body {
    height: 200px !important;
    overflow-x: hidden;
    overflow-y: auto;
  }

  .qf-form-item .qf-form-label {
    &:before {
      content: '*';
      color: red;
      margin-right: 4px;
    }
  }
}
</style>