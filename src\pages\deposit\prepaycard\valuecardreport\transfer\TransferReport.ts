import {Component, Vue} from 'vue-property-decorator'
import ListWrapper from 'cmp/list/ListWrapper.vue'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import TimeRange from '../../cmp/timerange/TimeRange';
import PrePayConsumeOrRefundData from 'model/prepay/report/prepay/trans/PrePayConsumeOrRefundData';
import GiftCardFilter from 'model/prepay/report/card/GiftCardFilter';
import CardReportSum from 'model/prepay/report/card/CardReportSum';
import DataUtil from 'pages/deposit/prepaycard/common/DataUtil';
import RSOrg from 'model/common/RSOrg';
import RSOrgFilter from 'model/common/RSOrgFilter';
import OrgApi from 'http/org/OrgApi';
import RechargeableCardReportApi from 'http/prepay/report/card/RechargeableCardReportApi';
import I18nPage from "common/I18nDecorator";
import PrepayCardTplPermission from "pages/deposit/prepaycard/prepaycardtpl/PrepayCardTplPermission";
import Zone<PERSON>pi from 'http/area/ZoneApi';
import ZoneFilter from 'model/datum/zone/ZoneFilter'
import BrowserMgr from 'mgr/BrowserMgr'
import FormItem from 'cmp/formitem/FormItem';
import SelectStores from 'cmp/selectStores/SelectStores';

@Component({
    name: 'ConsumeReport',
    components: {
        FormItem,
        ListWrapper,
        SubHeader,
        TimeRange,
      SelectStores
    }
})
@I18nPage({
    prefix: [
        '/储值/预付卡/实体礼品卡报表',
        '/储值/预付卡/电子礼品卡报表',
        '/储值/预付卡/充值卡报表',
        '/储值/预付卡/电子礼品卡报表/售卡流水',
        '/储值/预付卡/预付卡查询/列表页面',
        '/储值/预付卡/充值卡报表/余额转出流水',
        '/储值/预付卡/电子礼品卡报表/消费流水',
        '/公用/菜单',
        '/公用/提示',
        '/公用/查询条件',
        '/公用/按钮',
        '/会员/会员资料'
    ],
})
export default class ConsumeReport extends Vue {
    i18n: (str: string, params?: string[]) => string
    expandQuery: boolean = false
    prepayCardTplPermission = new PrepayCardTplPermission()
    areaData: any = []
    ZoneFilter: ZoneFilter = new ZoneFilter()
    isMoreMarketing: boolean = false // 是否开启多营销中心 true 开启 false 不开启
    query: GiftCardFilter = new GiftCardFilter()
    queryData: PrePayConsumeOrRefundData[] = []
    sum: CardReportSum = new CardReportSum()
    dataUtil: DataUtil = new DataUtil()
    $refs: any
    // 分页
    page = {
        currentPage: 1,
        total: 0,
        size: 10,
        probeEnabled: null
    }

    created() {
        let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
        if (sysConfig) {
          this.isMoreMarketing = sysConfig.enableMultiMarketingCenter;
        }
        this.query.occurredTimeAfterOrEqual = this.dataUtil.format(new Date(), 'yyyy-MM-dd')
        this.query.occurredTimeBefore = this.dataUtil.format(new Date(), 'yyyy-MM-dd')
        this.getList()
        this.getAreaList()
    }
  /**
  * 查询区域
  */
   getAreaList() {
    // this.ZoneFilter.page = 0
    // this.ZoneFilter.pageSize = 10
    ZoneApi.query(this.ZoneFilter).then((res) => {
      if (res.code === 2000) {
        this.areaData = res.data
      } else {
        this.$message.error(res.msg as string)
      }
    })
  }
    doSearch() {
        this.page.currentPage = 1
        this.getList()
    }

    doReset() {
        this.query = new GiftCardFilter()
        this.page.currentPage = 1
        this.$refs['timeRange'].reset()
    }

    /**
     * 查询
     */
    onSearch() {
        this.page.currentPage = 1
        this.getList()
    }

    /**
     * 分页页码改变的回调
     * @param val
     */
    onHandleCurrentChange(val: number) {
        this.page.currentPage = val
        this.getList()
    }

    /**
     * 每页多少条的回调
     * @param val
     */
    onHandleSizeChange(val: number) {
        this.page.size = val
        this.getList()
    }

    /**
     * 表格排序条件
     */
    onSortChange({column, prop, order}: any) {
        // todo
    }

    private getList() {
        this.query.page = this.page.currentPage - 1
        this.query.pageSize = this.page.size
        RechargeableCardReportApi.queryTransferHst(this.query).then((resp: any) => {
            if (resp && resp.code === 2000) {
                this.queryData = resp.data
                this.page.total = resp.total
                this.page.probeEnabled = resp.fields ? resp.fields.probeEnabled : null
                this.getSum()
            }
        }).catch((error) => {
            if (error && error.message) {
                this.$message.error(error.message)
            }
        })
    }

    isShowSum:boolean = false
    private getSum() {
        RechargeableCardReportApi.transferHstSum(this.query).then((resp: any) => {
            if (resp && resp.code === 2000) {
                this.sum = resp.data
                this.isShowSum = true
            } else {
                if (resp.code === 2404) return
                this.$message.error(resp.msg)
            }
        }).catch((error) => {
            if (error && error.message) {
                this.$message.error(error.message)
            }
        })
    }

    private handleTimeRange(dateArr: Date[]) {
        this.query.occurredTimeAfterOrEqual = dateArr[0]
        this.query.occurredTimeBefore = dateArr[1]
        this.getList()
    }

    private gotoTplDtl(num: string) {
      this.$router.push({ name: 'prepay-card-tpl-dtl', query: { number: num, cardTemplateType: 'RECHARGEABLE_CARD'}})
    }
}
