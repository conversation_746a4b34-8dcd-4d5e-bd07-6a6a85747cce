<template>
	<div>
		<el-dialog :visible.sync="dialogShow" append-to-body title="复制规则" class="copy-step-value">
			<el-row class="no-break" style="background-color: #ECF0FE;line-height: 60px;padding: 0 15px;" v-if="dialogShow">
				<i18n
					v-if="strategy === 'BY_AMOUNT' && isDiscount === false"
					k="/储值/会员储值/储值支付活动/编辑页面/适用商品消费每满{0}元，立减{1}元，不足部分不优惠"
				>
					<template slot="0">&nbsp;{{ reduction.threshold | amount }}&nbsp;</template>
					<template slot="1">&nbsp;{{ reduction.value | amount }}&nbsp;</template>
				</i18n>
				<div v-if="strategy === 'BY_AMOUNT' && isDiscount === true" style="line-height: 30px">
					<div v-for="(item, index) in reduction.stepValues" :key="index">
						<i18n k="/储值/会员储值/储值支付活动/编辑页面/适用商品消费满{0}元，打{1}折">
							<template slot="0">&nbsp;{{ item.threshold | amount }}&nbsp;</template>
							<template slot="1">&nbsp;{{ item.value | amount }}&nbsp;</template>
						</i18n>
					</div>
				</div>

				<i18n v-if="strategy === 'BY_QTY'" k="/储值/会员储值/储值支付活动/编辑页面/适用商品中每单品消费每满{0}件，立减{1}元，不足部分不优惠">
					<template slot="0">&nbsp;{{ reduction.threshold }}&nbsp;</template>
					<template slot="1">&nbsp;{{ reduction.value | amount }}&nbsp;</template>
				</i18n>
			</el-row>
			<el-row style="color: #969696;height: 60px;line-height: 60px;padding: 0 15px">{{ i18n("将此规则复制给以下等级：") }}</el-row>
			<el-row style="padding: 15px;border: 1px solid #E9E9E9">
				<el-tree
					ref="gradeTree"
					:data="gradeTree"
					show-checkbox
					node-key="id"
					:default-expanded-keys="defaultExpandedKeys"
					:default-checked-keys="defaultCheckedKeys"
				>
				</el-tree>
			</el-row>
			<div class="dialog-footer" slot="footer">
				<el-button @click="dialogShow = false">取消</el-button>
				<el-button @click="confirm" size="small" type="primary">确定</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script lang="ts" src="./CopyStepValue.ts"></script>

<style lang="scss" scoped>
.copy-step-value {
	display: flex;
	align-items: center;
	justify-content: center;
}
.no-break {
	word-wrap: break-word;
	word-break: normal;
}
</style>
