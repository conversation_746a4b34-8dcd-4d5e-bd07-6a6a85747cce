<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:02
 * @LastEditTime: 2025-03-07 14:33:11
 * @LastEditors: haiding <EMAIL>
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\goodsscope\GoodsScopeInMemPage.vue
 * 记得注释
-->
<template>
  <div class="goods-scope-in-mem-page">
    <InMemPage :data="data" :sort="(e1,e2)=>{return e1.id>e2.id?1:-1}" :isShowTotal="false">
      <template slot="data" slot-scope="{data}">
        <div class="goods-scope-in-mem-page-content">
          <div class="line" v-for="line of data" :key="line.id">
            [{{line.id}}]{{line.name}}
          </div>
        </div>
      </template>
    </InMemPage>
  </div>
</template>

<script lang="ts" src="./GoodsScopeInMemPage.ts">
</script>

<style lang="scss" scoped>
  .goods-scope-in-mem-page {
    line-height: normal;

    .goods-scope-in-mem-page-content {
      width: 100%;
      max-height: 500px;
      overflow: auto;
      display: flex;
      flex-wrap: wrap;
      padding-left: 16px;

      .line {
        width: 50%;
        line-height: 20px;
      }
    }
  }
</style>