<template>
  <div class="all-cash-coupon">
    <el-form :model="ruleForm" :rules="rules" class="demo-ruleForm" label-width="120px" ref="ruleForm">
      <el-form-item v-if="couponType === 'all_cash'" :label="formatI18n('/公用/券模板', '券面额')" prop="amount">
        <div v-if="copyFlag === 'edit'">
          {{ getFaceAmount(ruleForm.amount) }}
        </div>
        <div v-else>
          <el-input @change="doAmountChange"
                    style="width: 100px"
                    v-model="ruleForm.amount">
          </el-input>&nbsp;&nbsp;{{ formatI18n('/公用/券模板', '元') }}
        </div>
      </el-form-item>

      <el-form-item :label="formatI18n('/公用/券模板', '券名称')" prop="name">
        <div v-if="copyFlag === 'edit'">
          {{ ruleForm.name }}
        </div>
        <div v-else>
          <div style="color: #cccccc">- {{ formatI18n('/公用/券模板', nameTip) }}</div>
          <el-input maxlength="128" style="width: 300px" v-model="ruleForm.name" @change="doNameChange"></el-input>&nbsp;&nbsp;
        </div>
      </el-form-item>

      <el-form-item v-if="couponType === 'random_cash'" :label="formatI18n('/公用/券模板', '券面额范围')">
        <div style="display: inline-block">
          <el-form-item class="cur-day" prop="minFaceAmount" style="display: inline-block">
            <el-input @change="doMinFaceAmountChange" style="width: 100px" v-model="ruleForm.minFaceAmount"></el-input>
          </el-form-item>
          &nbsp;&nbsp;{{ formatI18n('/公用/券模板', '元') }}&nbsp;&nbsp;
          &nbsp;&nbsp;———&nbsp;&nbsp;&nbsp;
          <el-form-item class="cur-day" prop="maxFaceAmount" style="display: inline-block">
            <el-input @change="doMaxFaceAmountChange" style="width: 100px" v-model="ruleForm.maxFaceAmount"></el-input>
          </el-form-item>
          &nbsp;&nbsp;{{ formatI18n('/公用/券模板', '元') }}&nbsp;&nbsp;
        </div>
      </el-form-item>

      <CouponTemplateLogo :original-logo-url="ruleForm.logoUrl" @logoUrlCallBack="logoUrlCallBack"></CouponTemplateLogo>

      <el-form-item :label="formatI18n('/营销/券礼包活动/核销第三方券', '券码生成规则')" v-if="!wxScanForCoupon">
        <div v-if="copyFlag === 'edit'">
          <span v-if="ruleForm.prefix">{{ formatI18n('/营销/券礼包活动/核销第三方券/固定开头') }} {{ ruleForm.prefix }}</span>
          <span v-else>{{ formatI18n('/权益/券/券模板/券码前缀/系统随机生成') }}</span>
        </div>
        <div v-else>
          <div style="color: #cccccc">- {{ formatI18n('/营销/券礼包活动/核销第三方券', '为空表示系统随机生成，券模板保存后此字段不可修改。') }}</div>
          <el-form-item label-width="68px" :label="formatI18n('/营销/券礼包活动/核销第三方券', '固定开头')" prop="prefix">
            <el-input :placeholder="formatI18n('/营销/券礼包活动/券礼包活动/新建导出券码发券', '请输入6位以内的数字或字母')"
                      maxlength="6" style="width: 325px" v-model="ruleForm.prefix" @change="doPrefixChange"></el-input>
          </el-form-item>
        </div>
      </el-form-item>

      <div v-if="baseSettingFlag" style="height: 20px;width: 100%;background-color: #f2f2f2"></div>
      <div v-if="baseSettingFlag" class="baseSettingFlag">{{ formatI18n('/权益/券/券模板/编辑界面/用券规则') }}</div>
      <div v-if="$route.query.from === 'edit'&& !wxScanForCoupon"
           style="height: 48px;line-height: 48px;background-color: #3366ff19;margin: 0 20px;padding-left: 20px;margin-bottom: 10px">
        <img style="position: relative;top: 5px;" src="~assets/image/auth/info3.png"
             alt="">{{ formatI18n('/权益/券/券模板/编辑界面/用券规则下的提示/券模板修改后，已发出的券的有效期仍以原来的为准，其余用券规则以修改后的为准。') }}
      </div>
      <el-form-item :label="formatI18n('/公用/券模板', '券有效期')" class="cur-form-item">
        <el-select :placeholder="formatI18n('/公用/券模板', '请选择')" @change="doCouponValidateChange"
                   v-model="ruleForm.dateType">
          <el-option :label="formatI18n('/公用/券模板', '相对有效期')" value="RALATIVE"></el-option>
          <el-option :label="formatI18n('/公用/券模板', '固定有效期')" value="FIXED"></el-option>
        </el-select>
        <div style="display: inline-block" v-if="ruleForm.dateType === 'RALATIVE'">
          &nbsp;{{ formatI18n('/公用/券模板', '发券后') }}&nbsp;&nbsp;
          <el-form-item class="cur-day" prop="dateFrom" style="display: inline-block">
            <el-input @change="doCouponChange(0)" style="width: 100px" v-model="ruleForm.dateFrom"></el-input>
          </el-form-item>
          &nbsp;&nbsp;{{ formatI18n('/公用/券模板', '天生效，有效期') }}&nbsp;&nbsp;
          <el-form-item class="cur-day" prop="dateTo" style="display: inline-block">
            <el-input @change="doCouponChange(1)" style="width: 100px" v-model="ruleForm.dateTo"></el-input>
          </el-form-item>
          &nbsp;&nbsp;{{ formatI18n('/公用/券模板', '天') }}
        </div>
        <div style="display: inline-block;margin-left: 10px" v-if="ruleForm.dateType === 'FIXED'">
          <el-form-item class="fix_content" prop="dateFix">
            <el-date-picker
                @change="doDateFixChange"
                @focus="doDateFocus"
                :end-placeholder="formatI18n('/公用/券模板', '结束日期')"
                :picker-options="dateRangeOption"
                format="yyyy-MM-dd"
                range-separator="-"
                size="small"
                :start-placeholder="formatI18n('/公用/券模板', '开始日期')"
                type="daterange"
                v-model="ruleForm.dateFix"
                value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
        </div>
      </el-form-item>
      <el-form-item :label="formatI18n('/公用/券模板', '用券时段')" class="cur-form-item">
        <TimeRange
            :fixedAll="options.fixedTime"
            @change="doTimeChange"
            v-model="ruleForm.time"
            ref="timeRange">
        </TimeRange>
      </el-form-item>
      <el-form-item :label="formatI18n('/公用/券模板', '用券渠道')" class="cur-from-item" required>
        <el-radio-group @change="doUseFromChange" v-model="ruleForm.useFrom">
          <el-radio label="step1" style="display: block">
            {{ formatI18n('/公用/券模板/用券渠道', '全部渠道') }}
          </el-radio>
          <el-radio label="step2" style="display: block">
            <span>{{ formatI18n('/公用/券模板/用券渠道', '指定渠道适用') }}</span>
            <div style="position: relative;top: -8px;left: 24px">
              <el-form-item class="cur-from-item" prop="useFrom">
                <el-select style="width: 250px" :disabled="ruleForm.useFrom === 'step1'" multiple @change="doFromChange"
                           v-model="ruleForm.from"
                           :placeholder="formatI18n('/公用/券模板/用券渠道', '请至少选择一个渠道')">
                  <el-option v-for="(item, index) in channels" :key="`channel${index}`" :label="item.name"
                             :value="item.channel.typeId">{{ item.name }}
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item :label="formatI18n('/公用/券模板', '同步渠道')" class="cur-from-item">
        <div style="position: relative;top: -8px;left: 24px">
              <el-form-item class="cur-from-item" prop="useFrom">
                <el-select clearable style="width: 250px" @change="doSychChannelChange"
                           v-model="ruleForm.sychChannel"
                           :placeholder="formatI18n('/公用/券模板/同步渠道')" value-key="id">
                  <el-option v-for="(item, index) in channels" :key="`channel${index}`" :label="item.name"
                             :value="item.channel">{{ item.name }}
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
      </el-form-item>

      <el-form-item :label="formatI18n('/公用/券模板', '用券门店')" prop="storeRange">
        <ActiveStore
            :isOldActivity="false"
            ref="activeStore"
            :sameStore="sameStore"
            v-model="ruleForm.storeRange"
            @change="doStoreChange">
        </ActiveStore>
      </el-form-item>
      <el-form-item :label="formatI18n('/公用/券模板', '用券商品')" class="cur-form-item">
        <GoodsScopeEx
            type="all_cash"
            :hideUse="true"
            ref="goodsScope"
            :importNumber="20000"
            v-model="ruleForm.useCouponGood"
            :goodsMatchRuleMode="goodsMatchRuleMode" 
            @change="doGoodsRange">
        </GoodsScopeEx>
      </el-form-item>
      <el-form-item
          class="coupon-step"
          :label="formatI18n('/公用/券模板', '用券门槛')">
        <!--用券门槛-->
        <div v-if="copyFlag === 'edit'">
          <div v-if="ruleForm.type.useThresholdType === 'AMOUNT'">
            <div style="height: 40px;line-height: 40px">
              <span>{{ formatI18n("/公用/券模板/单品折扣券/用券门槛/用券商品满") + ruleForm.type.value + formatI18n("/公用/券模板/单品折扣券/用券门槛/元及以上可用")}}</span>
            </div>
          </div>
          <div v-else-if="ruleForm.type.useThresholdType === 'QTY'">
            <div style="height: 36px;line-height: 36px">
              <span>{{ formatI18n("/公用/券模板/单品折扣券/用券门槛/用券商品满") + ruleForm.type.value + formatI18n("/公用/券模板/单品折扣券/用券门槛/件及以上可用")}}</span>
            </div>
          </div>
          <div v-else>
            <div style="height: 36px;line-height: 36px">
              <span>{{ formatI18n("/公用/券模板/无门槛") }}</span>
            </div>
          </div>
        </div>
        <div v-else>
          <UseCouponStep
              :max="options.maxUseThreshold"
              :amount="ruleForm.amount"
              @change="doStepChange"
              @limitChange="limitChange"
              ref="useCouponStep"
              v-model="ruleForm.type"
              :type="formatI18n('/公用/券模板', '全场现金券')">
          </UseCouponStep>
        </div>
      </el-form-item>
      <el-form-item prop="promotion" :label="formatI18n('/公用/券模板', '叠加促销')">
        <div v-if="copyFlag === 'edit'&& !baseFieldEditable">
          {{ ruleForm.promotion ? formatI18n('/公用/券模板', '是') : formatI18n('/公用/券模板', '否') }}
        </div>
        <el-radio-group v-else v-model="ruleForm.promotion" @change="doPromotionChange">
          <el-radio :label="true">{{ formatI18n('/公用/券模板', '是') }}</el-radio>
          <el-radio :label="false">{{ formatI18n('/公用/券模板', '否') }}</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 叠加用券-->
      <GroupMutexTemplate ref="groupMutexTemplate" v-model="ruleForm.groupMutex"
                          @change="doMutexTemplateChange" :noLimit="noLimit"></GroupMutexTemplate>

      <el-form-item :label="formatI18n('/公用/券模板', '用券记录方式')" required>
        <div v-if="copyFlag === 'edit' && !baseFieldEditable">
          <div v-if="ruleForm.recordWay === 'FAV'">{{ formatI18n('/公用/券模板', '优惠方式') }}</div>
          <div v-else-if="ruleForm.recordWay === 'PAY'">{{ formatI18n('/公用/券模板', '支付方式') }}</div>
          <div v-else>{{ formatI18n('/公用/券模板', '组合方式') }},<span
              v-html="getFavValue(ruleForm.discountWay, ruleForm.payWay)"></span></div>
        </div>
        <div v-else>
          <el-radio-group v-model="ruleForm.recordWay" @change="doRecordWayChange">
            <el-radio label="FAV">{{ formatI18n('/公用/券模板', '优惠方式') }}</el-radio>
            <el-radio label="PAY">{{ formatI18n('/公用/券模板', '支付方式') }}</el-radio>
            <el-radio label="COLLOCATION">{{ formatI18n('/公用/券模板', '组合方式') }}</el-radio>
          </el-radio-group>
          <div class="cur_record" v-if="ruleForm.recordWay === 'COLLOCATION'">
            <el-radio-group v-model="ruleForm.recordType" @change="doRecordTypeChange">
              <el-radio label="PROPORTION">{{ formatI18n('/公用/券模板', '按比例') }}</el-radio>
              <el-radio label="AMOUNT">{{ formatI18n('/公用/券模板', '按金额') }}</el-radio>
            </el-radio-group>
            <div v-if="ruleForm.recordType === 'PROPORTION'">
              <div style="color: #cccccc">- {{ formatI18n('/营销/券礼包活动/核销第三方券', '若未升级jpos版本,则设置的比例不生效') }}</div>
                <el-form-item prop="discountWay" style="display: inline-block">
                  <el-input @change="doDiscountWay" style="width: 100px" v-model="ruleForm.discountWay"></el-input>
                </el-form-item>
                % {{ formatI18n('/公用/券模板', '优惠方式') }} +
                <el-form-item prop="payWay" style="display: inline-block">
                  <el-input @change="doPayWay" style="width: 100px" v-model="ruleForm.payWay"></el-input>
                </el-form-item>
                % {{ formatI18n('/公用/券模板', '支付方式') }}
            </div>
            <div v-else>
              <el-form-item prop="payWay" style="display: inline-block">
                <el-input @change="doPayWay" style="width: 100px" v-model="ruleForm.payWay"></el-input>
              </el-form-item>
              &nbsp;{{formatI18n('/公用/券模板/元支付方式，剩余算优惠方式')}}
            </div>
          </div>
        </div>
      </el-form-item>
      <el-form-item :label="formatI18n('/公用/券模板', '券承担方')" >
        <!-- 使用组件内部验证方法，不使用外部prop="couponUnder" -->
        <!-- <div v-if="copyFlag === 'edit'&& !baseFieldEditable" style="line-height: 36px">
          <div style="height: 36px;line-height: 36px" v-if="!ruleForm.couponUnder || ruleForm.couponUnder.length === 0">
            --
          </div>
          <div v-else>
            <div v-for="(item, index)  in ruleForm.couponUnder" :key="index">
              <div v-html="getCostPartr(item.party, item.value)"></div>
            </div>
          </div>
        </div> -->
        <CouponBear ref="CouponBear" :state="curState" v-model="ruleForm.couponUnder" @change="doBearChange"></CouponBear>
        <div>
        </div>

      </el-form-item>
      <el-form-item :label="formatI18n('/公用/券模板', '用券顺序')" prop="couponOrder">
        <div v-if="copyFlag === 'edit' && !baseFieldEditable">{{ ruleForm.couponOrder }}</div>
        <div v-else>
          <div style="color: #cccccc"> - {{ formatI18n('/公用/券模板', '请输入1-99之间的整数') }}</div>
          <div>
            <el-input @change="doThisOrder" style="width: 100px" v-model="ruleForm.couponOrder"></el-input>
          </div>
        </div>

      </el-form-item>
      <!--<el-form-item :label="formatI18n('/营销/券礼包活动/核销第三方券', '用券商品说明')" prop="couponGoodsDesc">-->
      <!--<div v-if=copyFlag === 'edit'>{{ruleForm.couponGoodsDesc | strFormat}}</div>-->
      <!--<el-input v-else :placeholder="formatI18n('/营销/券礼包活动/核销第三方券', '向用户简要描述用券商品范围')" maxlength="400" style="width: 500px;" type="textarea" v-model="ruleForm.couponGoodsDesc" @change="doGoodsRemarkChange"></el-input>-->
      <!--</el-form-item>-->
      <el-form-item :label="formatI18n('/营销/券礼包活动/核销第三方券', '用券说明')" prop="couponProduct">
        <el-input :maxlength="remarkMaxlength" style="width: 500px;" type="textarea" v-model="ruleForm.couponProduct"
                  :placeholder="remarkPlaceholder"
                  @change="doRemarkChange">
        </el-input>
      </el-form-item>

      <el-form-item :label="formatI18n('/公用/券模板', '是否支持转赠')">
        <el-radio-group v-model="ruleForm.transferable" @change="doTransferableChange">
          <el-radio :label="true">{{ formatI18n('/公用/券模板', '是') }}</el-radio>
          <el-radio :label="false">{{ formatI18n('/公用/券模板', '否') }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

  </div>
</template>

<script lang="ts" src="./AllCashCoupon.ts">
</script>

<style lang="scss">
.all-cash-coupon {
  .coupon-step, .cur-form-item {
    .el-form-item__label {
      &:before {
        content: '*';
        color: #EF393F;
        margin-right: 4px;
      }
    }
  }

  .rule-table {
    margin-top: 10px;
    width: 70%;

    .rule-table-header {
      padding: 0 10px;
      background-color: #E6E6E6;
      border: 1px solid #E6E6E6;
    }

    .rule-table-line {
      padding: 10px;
      border: 1px solid #E6E6E6;
      border-top: 0;
    }

    .opt-col {
      a + a {
        margin-left: 10px;
      }
    }
  }

  .cur-from-item {
    height: auto !important;

    .el-radio {
      height: auto !important;
      line-height: 40px;
    }
  }
}
</style>