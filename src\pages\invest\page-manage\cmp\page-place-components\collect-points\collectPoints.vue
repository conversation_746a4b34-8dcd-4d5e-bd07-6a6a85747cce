<template>
  <div class="collection-point"
  :style="{ padding: localProperty.styMarginTop + 'px ' + localProperty.styMarginRight + 'px ' + localProperty.styMarginBottom + 'px ' + localProperty.styMarginLeft + 'px' }"
  @click="activeTemplate">
    <div class="toolBar" v-if="activeIndex === index">
      <setting-toolbar :btns="toolBarBtns" @click="toolbarClick"></setting-toolbar>
    </div>
    <div class="container">
      <el-image class="top" :src="require('@/assets/image/fellow/img_jidian.png')"></el-image>
      <div class="innerTitle">
        <div class="innerTitle_l">
          <div class="innerTitle_label">{{ i18n('进行中') }}</div>
          <div class="innerTitle_text">{{ i18n('集点活动集点活动') }}</div>
        </div>
        <div class="innerTitle_r">
          <el-image :src="require('@/assets/image/fellow/ic_integral.png')"></el-image>
          <p><span>0</span>/10</p>
        </div>
      </div>
      <div class="innerTime">
        <el-image  :src="require('@/assets/image/fellow/ic_time_line.png')"></el-image>
        <p>{{ i18n('活动将于4天后结束') }}</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./collectPoints.ts"></script>

<style lang="scss" scoped>
.collection-point {
  min-height: 211px;
  width: 100%;
  background: #f9f9f9;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  border: 2px solid transparent;
  position: relative;
  margin-bottom: 10px;

  &-bg {
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-size: 100%;
  }

  &-title {
    text-align: center;
    padding-top: 42px;
    font-size: 20px;
    color: #fff;
  }

  .toolBar {
    position: absolute;
    top: 0;
    right: 1px;
    z-index: 999;
    cursor: pointer;
  }

  .container {
    width: 351px;
    height: 201px;
    background: #FFFFFF;
    border-radius: 12px;
    display: block;
    margin: 0 auto;

    .top {
      width: 351px;
      height: 140px;
    }

    .innerTitle {
      display: flex;
      justify-content: space-between;
      padding: 10px 12px 0;
      box-sizing: border-box;

      .innerTitle_l {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .innerTitle_label {
          text-align: center;
          width: 36px;
          height: 14px;
          background: #0075FF;
          border-radius: 3px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 10px;
          color: #FFFFFF;
          line-height: 14px;
          font-style: normal;
          margin-right: 6px;
        }

        .innerTitle_text {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 15px;
          color: #111111;
          line-height: 21px;
          text-align: left;
          font-style: normal;
        }
      }

      .innerTitle_r {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .el-image {
          width: 20px;
          height: 20px;
          margin-right: 5px;
        }

        p {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 15px;
          line-height: 21px;
          text-align: center;
          font-style: normal;
          color: #111111;

          span {
            color: #FF3B3D;
          }
        }
      }
    }

    .innerTime {
      display: flex;
      justify-content: flex-start;
      padding: 4px 12px 0;
      box-sizing: border-box;
      align-items: center;
      .el-image {
        width: 14px;
        height: 14px;
        margin-right: 5px;
      }

      p {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #FC5312;
        line-height: 0px;
        text-align: center;
        font-style: normal;
      }
    }
  }
}
</style>
