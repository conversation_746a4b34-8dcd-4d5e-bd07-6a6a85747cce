<template>
  <span class="stores-dtl">
    <el-button type="text" @click="dialogVisible = true"
               v-if="!hideButton">{{formatI18n('/营销/券礼包活动/核销第三方券/查看门店')}}</el-button>
    <el-dialog
        :title="formatI18n('/公用/菜单/门店')"
        :visible.sync="dialogVisible"
        class="inner-dialog-center"
        append-to-body
        width="800px">
      <InMemPage :data="data" :query="doQuery" :sort="(e1,e2)=>{return e1.id>e2.id?1:-1}">
        <template slot="filter" slot-scope="{filter, search, reset}">
          <el-form label-width="80px">
            <el-row>
              <el-col :span="12">
            <el-form-item :label="formatI18n('/公用/公共组件/门店选择弹框组件/查询/门店')">
              <el-input v-model="filter.codeName"
                        :placeholder="formatI18n('/公用/公共组件/门店选择弹框组件/查询/请输入门店代码/名称')"/>
            </el-form-item>
          </el-col>
              <el-col :span="8">
                <el-form-item>
                  <el-button type="primary" @click="search">{{formatI18n('/公用/按钮/查询')}}</el-button>
                  <el-button @click="reset">{{formatI18n('/公用/按钮/重置')}}</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </template>
        <template slot="data" slot-scope="{data}">
          <el-table :data="data" height="500px">
            <el-table-column prop="id" :label="formatI18n('/资料/门店/门店代码')"/>
            <el-table-column prop="name" :label="formatI18n('/资料/门店/门店名称')"/>
          </el-table>
        </template>
      </InMemPage>
    </el-dialog>
  </span>
</template>

<script lang="ts" src="./StoresDtl.ts">
</script>

<style lang="scss">
  .stores-dtl {

  }
  .inner-dialog-center{
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
