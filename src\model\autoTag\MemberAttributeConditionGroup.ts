/*
 * @Author: 黎钰龙
 * @Date: 2024-11-21 19:31:52
 * @LastEditTime: 2024-12-09 15:34:26
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\autoTag\MemberAttributeConditionGroup.ts
 * 记得注释
 */
import { ConnectiveType } from "./ConnectiveType"
import MemberAttributeCondition from "./MemberAttributeCondition"

export default class MemberAttributeConditionGroup {
  // 关系连接符：或者/ 且
  connective: Nullable<ConnectiveType> = null
  // 会员属性条件行
  memberAttributeConditions: MemberAttributeCondition[] = []
}