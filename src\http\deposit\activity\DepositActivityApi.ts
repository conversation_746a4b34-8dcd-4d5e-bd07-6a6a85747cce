import ApiClient from 'http/ApiClient'
import DepositActivity from 'model/deposit/activity/DepositActivity'
import DepositActivityFilter from 'model/deposit/activity/DepositActivityFilter'
import DepositActivityQueryResult from 'model/deposit/activity/DepositActivityQueryResult'
import Response from 'model/common/Response'
import DepositEvaluation from 'model/deposit/activity/DepositEvaluation';

export default class DepositActivityApi {
  /**
   * 审核活动
   *
   */
  static audit(activityId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/deposit-activity/audit/${activityId}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量审核储值活动
   *
   */
  static batchAudit(body: string[]): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/deposit-activity/batch/audit`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 批量删除储值活动
   *
   */
  static batchRemove(body: string[]): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/deposit-activity/batch/remove`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 批量终止储值活动
   *
   */
  static batchStop(body: string[]): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/deposit-activity/batch/stop`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 创建活动
   *
   */
  static create(body: DepositActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/deposit-activity/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 活动效果评估
   *
   */
  static evaluate(activityId: string): Promise<Response<DepositEvaluation>> {
    return ApiClient.server().get(`/v1/deposit-activity/evaluate/${activityId}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询活动
   *
   */
  static info(activityId: string): Promise<Response<DepositActivity>> {
    return ApiClient.server().get(`/v1/deposit-activity/info/${activityId}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改活动
   *
   */
  static modify(body: DepositActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/deposit-activity/modify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询活动
   *
   */
  static query(body: DepositActivityFilter): Promise<Response<DepositActivityQueryResult>> {
    return ApiClient.server().post(`/v1/deposit-activity/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除活动
   *
   */
  static remove(activityId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/deposit-activity/remove/${activityId}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存并审核活动
   *
   */
  static saveAndAudit(body: DepositActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/deposit-activity/saveAndAudit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 终止活动
   *
   */
  static stop(activityId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/deposit-activity/stop/${activityId}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 检查冲突
   *
   */
  static checkConflict(body: DepositActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/deposit-activity/checkConflict`, body, {
    }).then((res) => {
      return res.data
    })
  }
}
