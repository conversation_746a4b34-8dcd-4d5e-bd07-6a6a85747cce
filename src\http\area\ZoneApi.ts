import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import BZoneTreeData from 'model/datum/zone/BZoneTreeData'
import SaveBatchZoneRequest from 'model/datum/zone/SaveBatchZoneRequest'
import Zone from 'model/datum/zone/Zone'
import ZoneFilter from 'model/datum/zone/ZoneFilter'

export default class ZoneApi {
  /**
   * 批量导入区域信息到数据库
   * 批量导入区域信息到数据库。
   * 
   */
  static importZone(body: any, targetMarketingCenter?: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/zone/importZone`, body, {
      params: {
        targetMarketingCenter: targetMarketingCenter
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询区域
   * 查询区域。
   * 
   */
  static query(body: ZoneFilter): Promise<Response<Zone[]>> {
    return ApiClient.server().post(`/v1/zone/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除区域
   * 删除区域。
   * 
   */
  static remove(id: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/zone/remove`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量保存
   * 批量保存。
   * 
   */
  static saveBatch(body: SaveBatchZoneRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/zone/saveBatch`, body, {
    }).then((res) => {
      return res.data
    })
  }
  /**
 * 判断组织是否存在
 * 判断组织是否存在，忽略营销中心。
 * 
 */
  static isExistZone(body: ZoneFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/zone/isExistZone`, body, {
    }).then((res) => {
      return res.data
    })
  }
  /**
     * 获取区域树
     * 获取区域树
     * 
     */
  static getTree(): Promise<Response<BZoneTreeData>> {
    return ApiClient.server().post(`/v1/zone/getTree`, {}, {
    }).then((res) => {
      return res.data
    })
  }
}
