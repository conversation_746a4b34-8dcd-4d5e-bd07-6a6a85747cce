<template>
  <div>
    <el-dialog :before-close="doBeforeClose" :close-on-click-modal="false" :title="formatI18n('/会员/会员资料/批量导出/弹框/风险提示')" :visible.sync="dialogShow"
      append-to-body class="export-confirm">
      <div>
        <el-alert :closable="false" style="margin-bottom: 15px" type="warning" title="" show-icon>
          <strong :style="{color: 'red', 'word-break': isEnglish ? 'keep-all' : 'normal'}">
            {{ warnMsg ? warnMsg: formatI18n('/会员/会员资料/批量导出/弹框/风险提示/大数据量导出可能会影响系统稳定，请务必在业务非高峰期执行！')}}
          </strong>
        </el-alert>
        <div class="form-item" v-if="accountNm!=''">
          {{formatI18n("/储值/会员储值/会员储值查询/列表页面/会员储值账户")}}： &emsp;{{accountNm}}
        </div>
        <div class="form-item">
          <el-checkbox v-model="agreeFlag">{{ checkMsg?checkMsg:formatI18n('/会员/会员资料/批量导出/弹框/风险提示/已知晓风险，并确认继续执行导出')}}</el-checkbox>
        </div>
      </div>
      <div class="dialog-footer" slot="footer">
        <el-button @click="doModalClose" size="small">{{ formatI18n('/公用/按钮', '取消') }}</el-button>
        <el-button @click="doModalConfirm" size="small" type="primary" :disabled="!agreeFlag">
          {{ confirmBtnMsg?confirmBtnMsg:formatI18n('/会员/会员资料/批量导出/弹框/风险提示/确认导出') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" src="./ExportConfirm.ts">
</script>

<style lang="scss">
.export-confirm {
  display: flex;
  align-items: center;
  justify-content: center;
  .el-dialog {
    width: 520px;
    // margin-top: 350px !important;
  }
  .form-item {
    margin-top: 15px;
    font-size: 14px;
    margin-left: 40px;
  }
  .el-checkbox__label {
    font-size: 14px;
  }
}
</style>