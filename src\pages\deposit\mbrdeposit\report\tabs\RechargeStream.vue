<template>
  <div class="recharge-stream">
    <el-radio-group v-model="date">
      <div no-i18n>
        <el-radio-button :label="i18n('今天')"></el-radio-button>
        <el-radio-button :label="i18n('昨天')"></el-radio-button>
        <el-radio-button :label="i18n('近7天')"></el-radio-button>
        <el-radio-button v-if="!daqiaoshihuaDingkai" :label="i18n('近30天')"></el-radio-button>
        <el-radio-button v-if="!daqiaoshihuaDingkai" :label="i18n('近90天')"></el-radio-button>
        <el-radio-button v-if="!daqiaoshihuaDingkai" :label="i18n('自定义')"></el-radio-button>
        <el-date-picker v-if="!daqiaoshihuaDingkai" style="position: relative;top: 5px;left: 10px" @change="doDateChange"
          :end-placeholder="i18n('结束日期')" format="yyyy-MM-dd" :clearable="false" range-separator="-" size="small" :start-placeholder="i18n('起始日期')"
          type="daterange" v-model="createCouponDate" value-format="yyyy-MM-dd"></el-date-picker>
      </div>
    </el-radio-group>
    <div>
      <ListWrapper>
        <template slot="query">
          <QueryCondition @reset="onReset" @search="onSearch" @toggle="doToggle">
            <el-row>
              <el-col :span="8">
                <form-item label="会员信息">
                  <el-input placeholder="手机号/会员号/实体卡号等于" v-model="query.memberCode" style="width: 200px"></el-input>
                </form-item>
              </el-col>
              <el-col :span="8">
                <form-item label="实充增加" class="customer-label">
                  <div style="display: flex">
                    <div style="flex: 0">
                      <el-input v-model="query.amountBegin" style="width: 80px"></el-input>
                    </div>
                    <div style="line-height: 26px">&nbsp;-&nbsp;</div>
                    <div style="flex: 1">
                      <el-input v-model="query.amountEnd" style="width: 80px"></el-input>
                    </div>
                  </div>
                </form-item>
              </el-col>
              <el-col :span="8">
                <form-item label="充值面额">
                  <div style="display: flex">
                    <div style="flex: 0">
                      <el-input v-model="query.rechargeDenominationBegin" style="width: 80px"></el-input>
                    </div>
                    <div style="line-height: 26px">&nbsp;-&nbsp;</div>
                    <div style="flex: 1">
                      <el-input v-model="query.rechargeDenominationEnd" style="width: 80px"></el-input>
                    </div>
                  </div>
                </form-item>
              </el-col>
            </el-row>
            <template slot="opened">
              <el-row>
                <el-col :span="8">
                  <form-item label="会员信息">
                    <el-input placeholder="手机号/会员号/实体卡号等于" v-model="query.memberCode" style="width: 200px"></el-input>
                  </form-item>
                </el-col>
                <el-col :span="8">
                  <form-item label="实充增加" class="customer-label">
                    <div style="display: flex">
                      <div style="flex: 0">
                        <el-input v-model="query.amountBegin" style="width: 95px"></el-input>
                      </div>
                      <div style="line-height: 26px">&nbsp;-&nbsp;</div>
                      <div style="flex: 0">
                        <el-input v-model="query.amountEnd" style="width: 95px"></el-input>
                      </div>
                    </div>
                  </form-item>
                </el-col>
                <el-col :span="8">
                  <form-item label="充值面额">
                    <div style="display: flex">
                      <div style="flex: 0">
                        <el-input v-model="query.rechargeDenominationBegin" style="width: 95px"></el-input>
                      </div>
                      <div style="line-height: 26px">&nbsp;-&nbsp;</div>
                      <div style="flex: 0">
                        <el-input v-model="query.rechargeDenominationEnd" style="width: 95px"></el-input>
                      </div>
                    </div>
                  </form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <form-item label="交易号">
                    <el-input placeholder="类似于" v-model="query.transNoLikes" style="width: 200px"></el-input>
                  </form-item>
                </el-col>
                <el-col :span="8">
                  <form-item label="活动名称">
                    <el-input placeholder="类似于" v-model="query.activityNameLikes" style="width: 200px"></el-input>
                  </form-item>
                </el-col>
                <el-col :span="8">
                  <!-- 线上充值、收银端充值、收银端零钱转储值、第三方充值、充值卡充值、储值卡充值、退货充值-->
                  <form-item :label="i18n('充值类型')" class="customer-label" no-i18n>
                    <el-select :placeholder="i18n('不限')" v-model="query.depositTypeEquals" style="width: 200px">
                      <el-option :label="i18n('不限')" value>{{ i18n('不限') }}</el-option>
                      <el-option :label="i18n('线上充值')" value="member-h5">{{ i18n('线上充值') }}</el-option>
                      <el-option :label="i18n('第三方充值')" value="third_party">{{ i18n('第三方充值') }}</el-option>
                      <el-option :label="i18n('收银现金充值')" value="hdcardcash">{{ i18n('收银现金充值') }}</el-option>
                      <el-option :label="i18n('收银零钱转储')" value="hdcardcash_small_change">{{i18n('收银零钱转储')}}</el-option>
                      <el-option :label="i18n('充值卡充值')" value="hdcard_imprest">{{ i18n('充值卡充值') }}</el-option>
                      <el-option :label="i18n('匿名实体储值卡充值')" value="hdcard_anonymous">{{ i18n('匿名实体储值卡充值') }}</el-option>
                      <el-option :label="i18n('退货充值')" value="refund_recharge">{{ i18n('退货充值') }}</el-option>
                      <el-option :label="i18n('礼品卡充值')" value="hdcard_gift">{{ i18n('礼品卡充值') }}</el-option>
                    </el-select>
                  </form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <form-item label="充值门店">
                    <SelectStores v-model="query.storeIdEquals" :isOnlyId="true" :hideAll="false" width="200px"
                      :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
                    </SelectStores>
                  </form-item>
                </el-col>
                <el-col :span="8" v-if="isMoreMarketing">
                  <form-item :label="formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水/发生区域')">
                    <el-select placeholder="不限" style="width: 171px" v-model="query.zoneIdEquals">
                      <el-option :key="item.zone.id" :label="'['+item.zone.id+']'+item.zone.name" :value="item.zone.id"
                        v-for="item in areaData">[{{item.zone.id}}]{{item.zone.name}}</el-option>
                    </el-select>
                  </form-item>
                </el-col>
              </el-row>
            </template>
          </QueryCondition>
        </template>
        <template slot="btn" v-if="isShowSum">
          <div style="display:flex;align-items:center">
            <i class="iconfont ic-info" style="font-size: 18px;color: #20A0FF"></i>
            &nbsp;&nbsp;{{ date }}
            <i18n k="/储值/会员储值/会员储值报表/充值流水/充值交易额（实充增加）{0}元，返现增加{1}元">
              <template slot="0">{{ sum.amount | fmt }}</template>
              <template slot="1">{{ sum.giftAmount | fmt }}</template>
            </i18n>&nbsp;&nbsp;
          </div>
        </template>
        <template slot="list">
          <el-table :data="queryData" border v-loading="loading">
            <el-table-column fixed label="充值时间" prop="occurredTime" width="140">
              <template slot-scope="scope">
                <span no-i18n>{{ scope.row.occurredTime | dateFormate3 }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed label="会员" prop="mbrcode" width="140">
              <template slot-scope="scope">
                <span no-i18n>
                  {{
                  scope.row.mobile ? scope.row.mobile : (scope.row.crmCode ? scope.row.crmCode : (scope.row.hdCardCardNum ? scope.row.hdCardCardNum : '--'))
                  }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="储值增加（元）" prop="totalAmount" width="140">
              <template slot-scope="scope">
                <span no-i18n>{{ scope.row.totalAmount | fmt }}</span>
              </template>
            </el-table-column>
            <el-table-column label="实充增加（元）" prop="amount" width="140">
              <template slot-scope="scope">
                <span no-i18n>{{ scope.row.amount | fmt }}</span>
              </template>
            </el-table-column>
            <el-table-column label="返现增加（元）" prop="giftAmount" width="140">
              <template slot-scope="scope">
                <span no-i18n>{{ scope.row.giftAmount | fmt }}</span>
              </template>
            </el-table-column>
            <el-table-column label="充值面额（元）" prop="faceAmount" width="140">
              <template slot-scope="scope">
                <span no-i18n>{{ scope.row.faceAmount | fmt }}</span>
              </template>
            </el-table-column>
            <el-table-column label="充值类型" prop="depositType" width="140">
              <template slot-scope="scope">
                <span no-i18n>{{ i18n(scope.row.depositType) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="交易号" prop="transNo" width="140">
              <template slot-scope="scope">
                <span no-i18n>{{ scope.row.transNo }}</span>
              </template>
            </el-table-column>
            <el-table-column label="活动名称" prop="mbrcode" width="140">
              <template slot-scope="scope">
                <span no-i18n>{{ scope.row.activityName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="充值门店" prop="store">
              <template slot-scope="scope">
                <span no-i18n>{{ scope.row.store }}</span>
              </template>
            </el-table-column>
            <el-table-column v-if="isMoreMarketing" :label="formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水/发生区域')">
              <template slot-scope="scope">
                <div v-if="scope.row.zone!==null && scope.row.zone!==''">
                  <el-tooltip class="item" effect="light" placement="right-end">
                    <div>{{scope.row.zone}}</div>
                    <div slot="content">
                      {{scope.row.zone}}
                    </div>
                  </el-tooltip>
                </div>
                <div v-else>-</div>
              </template>
            </el-table-column>
          </el-table>
        </template>
        <template slot="page">
          <el-pagination no-i18n :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
            @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background
            :layout="getPageLayout('total, prev, pager, next, sizes, jumper', page.probeEnabled)"></el-pagination>
        </template>
      </ListWrapper>
    </div>
    <DownloadCenterDialog :dialogvisiable="dialogvisiable" @dialogClose="doDialogClose"></DownloadCenterDialog>
  </div>
</template>

<script lang="ts" src="./RechargeStream.ts">
</script>

<style lang="scss">
.recharge-stream {
  .list-wrapper-query {
    padding: 12px 0 !important;
  }
}
</style>