/*
 * @Author: 黎钰龙
 * @Date: 2024-04-15 14:32:44
 * @LastEditTime: 2025-05-22 14:01:52
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\cmp\page-props-components\upload-img\UploadImg.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import EnvUtil from 'util/EnvUtil';
import { Component, Model, Prop, Vue } from 'vue-property-decorator';
import JumpPageInfo from 'model/navigation/JumpPageInfo'
import { CmsConfigChannel } from 'model/template/CmsConfig';


@Component({
  name: 'UploadImg',
  components: {}
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/公用/菜单',
    '/页面/页面管理',
    '/营销/大转盘活动'
  ],
  auto: true
})

export default class UploadImg extends Vue {
  @Model('change') imgUrl: any
  @Prop({ type: String, default: '' })
  label: string; // label名
  @Prop({ type: Number, default: 2048 }) maximum: number;  //限制图片大小showIndex
  @Prop({ type: Number, default: 0 }) index: number;  //限制图片大小
  @Prop({ type: Boolean, default: true }) showIndex: boolean;  //限制图片大小
  @Prop({ type: Boolean, default: false }) isCircular: boolean; //样式是否为圆形
  @Prop() advertiseChannel: Nullable<CmsConfigChannel[]>; //当前页面可用的投放渠道
  @Prop({
    type: Object,
    default: () => {
      return {
        delete: true,
        multiple: true,
        select: true,
      };
    },
  })
  config: any; // 配置项
  @Prop({ type: Object })
  jumpPageInfo: any; // 数据模型
  @Prop({ type: Object })
  value: any; // 数据模型
  rules = {
    imgValue: [{ required: true, message: this.i18n('请输入'), trigger: ['blur', 'change'] }],
  };
  from: any = {
    imgValue: ''
  }
  // JumpPageInfo: JumpPageInfo = new JumpPageInfo()
  copyVal: any
  $refs: any;
  get imgValue() {
    this.copyVal = this.imgUrl
    // return this.imgUrl
    if (this.imgUrl) {
      if (this.imgUrl.propImages && this.imgUrl.propImages.length > 0) {
        this.from.imgValue = this.imgUrl.propImages[0].imageUrl
        return this.imgUrl.propImages[0].imageUrl
      } else if (this.imgUrl.propImageUrl) {
        this.from.imgValue = this.imgUrl.propImageUrl
        return this.imgUrl.propImageUrl
      } else {
        this.from.imgValue = this.isObject(this.imgUrl) ? this.imgUrl.propImageUrl ? this.imgUrl.propImageUrl : '' : this.imgUrl
        return this.isObject(this.imgUrl) ? this.imgUrl.propImageUrl ? this.imgUrl.propImageUrl : '' : this.imgUrl
      }
    } else {
      this.from.imgValue = this.imgUrl
      return this.imgUrl
    }
  }
  get JumpPageInfo() {
    if (this.imgUrl) {
      if (this.imgUrl.propImages && this.imgUrl.propImages.length > 0) {
        return this.imgUrl.propImages[0].jumpPageInfo
      } else if (this.imgUrl.propItems && this.imgUrl.propItems.length > 0) {
        return this.imgUrl.propItems[0].jumpPageInfo
      } else {
        return this.jumpPageInfo
      }
    } else {
      return this.jumpPageInfo
    }
  }
  set JumpPageInfo(val: any) {
    if (this.imgUrl) {
      if (this.imgUrl.propImages && this.imgUrl.propImages.length > 0) {
        this.imgUrl.propImages[0].jumpPageInfo = val
      } else if (this.imgUrl.propItems && this.imgUrl.propItems.length > 0) {
        this.imgUrl.propItems[0].jumpPageInfo = val
      }
    }
    this.$emit('changeJumpPageInfo', val)
  }
  set imgValue(value: any) {
    const res = value ?? null
    this.$emit('change', res)
  }

  get uploadHeaders() {
    let locale = sessionStorage.getItem('locale')
    const headers: any = {
      locale: locale ? locale : 'zh_CN',
      time_zone: new Date().getTimezoneOffset(),
      marketingCenter: sessionStorage.getItem('marketCenter')
    }
    const authorization = EnvUtil.getUniAuthorization()
    if (authorization) {
      headers.authorization = authorization
    }
    return headers
  }

  get uploadUrl() {
    return EnvUtil.getServiceUrl() + "v1/upload/upload";
  }
  isObject(input: any) {
    return typeof input === 'object' && input !== null;
  }
  deleteImg() {
    let res = this.copyVal
    if (this.imgUrl) {
      if (this.imgUrl.propImages) {
        res.propImages[0].imageUrl = ''
        this.$emit('change', res)
      } else if (this.imgUrl.propItems) {
        res.propImageUrl = ''
        this.$emit('change', res)
      } else {
        this.imgValue = ''
        this.from.imgValue = ''
      }
    } else {
      this.imgValue = ''
      this.from.imgValue = ''
    }
  }
  replaceImg() {
    // this.$refs.upload.upload()
    this.$refs["upload"].$refs["upload-inner"].handleClick()
  }
  changeLinkDialog() {
    this.$refs.jumpPage.setJumpPageInfo()
    this.$refs.jumpPage.dialogShow = true
  }
  onImageUploadSuccess(response: any, file: any, fileList: any) {
    if (response && response.code === 2000) {
      this.$message.success(this.i18n("上传成功"));
      let res = this.copyVal
      if (this.imgUrl) {
        if (this.imgUrl.propImages) {
          res.propImages[0].imageUrl = response.data.url
          this.$emit('change', res)
        } else if (this.imgUrl.propItems) {
          res.propImageUrl = response.data.url
          this.$emit('change', res)
        } else {
          this.imgValue = response.data.url;
          this.from.imgValue = response.data.url
        }
      } else {
        this.imgValue = response.data.url;
        this.from.imgValue = response.data.url

      }
      this.$emit('afterSuccess')
    } else {
      this.$message.error(response.msg);
    }
  }
  // 选择跳转页面回调
  changeLink(val: JumpPageInfo) {
    // 
    let res = this.copyVal
    this.JumpPageInfo = val
    if (this.imgUrl) {
      if (this.imgUrl.propImages) {
        res.propImages[0].jumpPageInfo = val
        this.$emit('change', res)
      } else if (this.imgUrl.propItems) {
        res.propItems[0].jumpPageInfo = val
        this.$emit('change', res)
      } else {
        this.$emit('changeJumpPageInfo', val)
      }
    } else {
      this.$emit('changeJumpPageInfo', val)
    }
    this.$refs.jumpPage.dialogShow = false
  }
  // 图片上传前的校验
  beforeAvatarUpload(file: any) {
    const isJPG = ["image/jpeg", "image/gif", "image/png", "image/jpg"].indexOf(file.type) > -1;
    const isLt2M = file.size / 1024 < this.maximum;
    if (!isJPG) {
      this.$message.error(this.i18n("上传图片只能是JPG/JPEG/GIF/PNG格式!"));
      return false;
    }
    if (!isLt2M) {
      this.$message.error(this.i18n("上传图片大小不能超过{0}", [this.maximum / 1024 + "MB"]));
      return false;
    }
    return true;
  }
  validate(callback: () => void) {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.validate(callback);
    }
  }

  clearValidate() {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.clearValidate();
    }
  }
};