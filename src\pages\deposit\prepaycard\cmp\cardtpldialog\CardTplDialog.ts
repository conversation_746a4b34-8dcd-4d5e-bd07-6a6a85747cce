/*
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:03
 * @LastEditTime: 2024-01-09 15:47:53
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\prepaycard\cmp\cardtpldialog\CardTplDialog.ts
 * 记得注释
 */
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import CardTemplate from 'model/card/template/CardTemplate'
import CardTemplateFilter from 'model/card/template/CardTemplateFilter'
import CardTemplateApi from 'http/card/template/CardTemplateApi'
import I18nPage from "common/I18nDecorator";
import RSMarketingCenterFilter from 'model/common/RSMarketingCenterFilter';
import MarketingCenterApi from 'http/marketingcenter/MarketingCenterApi';
import RSMarketingCenter from 'model/common/RSMarketingCenter';
import CommonUtil from 'util/CommonUtil';


@Component({
  name: 'CardTplDialog',
  components: {}
})
@I18nPage({
  prefix: [
    '/储值/预付卡/电子礼品卡活动/编辑页面/选择卡模板组件',
    '/公用/提示',
    '/公用/按钮',
  ],
})
export default class CardTplDialog extends Vue {

  @Prop({ type: Boolean, default: false })
  isSelectMultiple: any;

  @Prop({ type: Boolean, default: true })
  canSelectCenter: any; //是否可以选择其他营销中心的卡模板

  query: CardTemplateFilter = new CardTemplateFilter()
  queryData: CardTemplate[] = []
  // selected: CardTemplate
  selected: CardTemplate[] = []
  $refs: any
  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  marketCenter: any = sessionStorage.getItem('marketCenter')
  headquarters: Boolean = sessionStorage.getItem('headquarters') === 'true' ? true : false
  marketingCentersList: RSMarketingCenter[] = []
  colVal: any = 12
  selectNoTmp: any = []
  loading: boolean = false

  @Prop()
  data: any

  @Prop()
  type: any

  @Prop({ type: Array, default: () => { return [] } })
  cardMedium: string[];  //需要查询的卡介质

  @Prop({
    type: Boolean,
    default: false
  })
  dialogShow: boolean

  @Prop({ type: Array, default: () => { return [] } })
  selectNo: string[];  //选中的模板
  @Prop({
    type: Number,
    default: 0
  })
  maxSel: number
  @Watch('dialogShow')
  dialogShowChange() {
    this.$refs.storeTable && this.$refs.storeTable.clearSelection()
    if (this.dialogShow) {
      //
      // this.selectNo.forEach((item:any) => {
      //   let obj = this.queryData.find((x:any) => x.number == item)
      //   if (obj) {
      //     this.$refs.storeTable.toggleRowSelection(obj, true)
      //   }
      // })
      this.getList()
    }
  }
  @Watch('selectNo', { deep: true, immediate: true })
  selectNoChange() {
    // 
    this.selectNoTmp = [...this.selectNo]
  }
  created() {
    if (sessionStorage.getItem('locale') === 'en_US') {
      this.colVal = 24
    }
    this.getMarketCenter()
    // this.getList()
  }
  doSearch() {
    this.page.currentPage = 1
    this.getList()
  }
  doReset() {
    this.page.currentPage = 1
    this.query = new CardTemplateFilter()
    this.getList()
  }
  doBeforeClose(done: any) {
    this.$emit('dialogClose')
    done()
  }
  doModalClose() {
    let objTmp: any = {}
    let arr: any[] = []
    this.selected.forEach((item: any) => {
      if (!objTmp['t' + item.number]) {
        arr.push(item)
        objTmp['t' + item.number] = 1
      }
    })
    if (this.maxSel && (arr.length > this.maxSel || this.selectNoTmp.length > this.maxSel)) {
      this.$message.warning(this.i18n('最多只能选' + this.maxSel + '个模板!'))
      return
    }
    this.$emit('summit', arr, this.selectNoTmp)
    // this.$emit('summit', this.selected)
    this.$emit('dialogClose')
  }
  selectTpl(val: any) {
    this.selected = val
    this.doModalClose()
  }
  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.getList()
  }
  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.getList()
  }
  getMarketCenter() {
    let query = new RSMarketingCenterFilter()
    MarketingCenterApi.query(query).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.marketingCentersList = resp.data
        console.log(this.marketingCentersList);

      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  private getList() {
    if (this.type) {
      this.query.cardTemplateTypeEquals = this.type
    }
    if (this.cardMedium.length) {
      this.query.cardMediumIn = this.cardMedium || null
    }
    this.query.page = this.page.currentPage - 1
    this.query.pageSize = this.page.size
    let oldMarketCenter = sessionStorage.getItem('marketCenter') || ''
    this.loading = true
    sessionStorage.setItem('marketCenter', this.marketCenter)
    CardTemplateApi.query(this.query).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.queryData = resp.data.result
        this.page.total = resp.data.total
        if (this.dialogShow && this.selectNo.length && this.queryData.length) {
          //
          this.selectNo.forEach((item: any) => {
            let obj = this.queryData.find((x: any) => x.number == item)
            if (obj) {
              this.$refs.storeTable.toggleRowSelection(obj, true)
            }
          })
        }
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    }).finally(() => {
      this.loading = false
      sessionStorage.setItem('marketCenter', oldMarketCenter as string)
    });
  }
  // 多选
  handleSelectionChange(val: any) {
    this.selected = val;
  }
  getRowKey(row: any) {
    return row.number
  }

  //如果为单选模式，则最多只允许勾选一个卡模板
  rowCanSelect(row: CardTemplate) {
    if (this.selected.length >= 1 && !this.isSelectMultiple) {
      return this.selected.some((item) => { return item.number === row.number })
    }
    return true
  }
  // 选择相关
  handleSelect(sel: any, row: any) {
    if (this.isSelectMultiple) {
      // selectNo 选中的模板
      if (sel.find((x: any) => x.number == row.number)) {
        // 选中
        this.selectNoTmp.push(row.number)
      } else {
        // 取消选中
        let index: number = this.selectNoTmp.findIndex((item: any) => item == row.number)
        if (index > -1) {
          this.selectNoTmp.splice(index, 1)
        }
      }
    }
  }
  handleSelectAll(sel: any) {
    if (this.isSelectMultiple) {
      if (!sel.length) {
        // 表格数据全部取消
        this.queryData.forEach((item: any) => {
          // 
          let index: number = this.selectNoTmp.findIndex((x: any) => x == item.number)
          if (index > -1) {
            this.selectNoTmp.splice(index, 1)
          }
        })
      } else {
        if (this.selectNoTmp.length > sel.length) {
          // 取消全选时，已有选项，sel会返回除此页外的其他选择，
          this.queryData.forEach((item: any) => {
            // 
            let index: number = this.selectNoTmp.findIndex((x: any) => x == item.number)
            if (index > -1) {
              this.selectNoTmp.splice(index, 1)
            }
          })
        } else {
          this.selectNoTmp = [...new Set([...this.selectNoTmp, ...sel.map((x: any) => x.number)])]
        }
      }
    }
  }
}