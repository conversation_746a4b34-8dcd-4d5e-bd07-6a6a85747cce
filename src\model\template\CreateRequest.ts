/*
 * @Author: 黎钰龙
 * @Date: 2025-01-06 20:02:16
 * @LastEditTime: 2025-05-22 11:29:38
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\template\CreateRequest.ts
 * 记得注释
 */
import { ContentTemplateState } from 'model/template/ContentTemplateState'
import { CmsConfigChannel } from './CmsConfig'

// 创建内容模板请求
export default class CreateRequest {
  // 名称
  name: Nullable<string> = null
  // 图片
  image: Nullable<string> = null
  // 资源位名称
  placeName: Nullable<string> = null
  // 备注
  remark: Nullable<string> = null
  // 内容
  content: any
  // 页面状态
  state: Nullable<ContentTemplateState> = null
  // type 
  type: Nullable<string> = null 
  // 投放渠道
  channels: Nullable<CmsConfigChannel[]> = null
}