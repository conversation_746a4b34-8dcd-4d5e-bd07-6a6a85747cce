<!--
 * @Author: 黎钰龙
 * @Date: 2024-04-26 10:38:47
 * @LastEditTime: 2024-04-26 14:17:56
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\selectGoodsTag\SelectGoodsTag.vue
 * 记得注释
-->
<template>
  <el-select
    v-model="tagsValue"
    multiple
    value-key="id"
    :style="{width: width}"
    :loading="selectLoading"
    :disabled="disabled"
    clearable
    filterable
    collapse-tags
    remote
    :remote-method="doRemoteMethod"
    :placeholder="placeholder ? placeholder : i18n('请选择/输入商品标签')"
  >
    <template v-if="isOnlyId">
      <el-option :label="item.name" :value="item.id" v-for="(item, index) in tags" :key="index">
        [{{ item.id }}]{{ item.name }}
      </el-option>
    </template>
    <template v-else>
      <el-option :label="item.name" :value="item" v-for="(item, index) in tags" :key="index">
        [{{ item.id }}]{{ item.name }}
      </el-option>
    </template>
  </el-select>
</template>

<script lang="ts" src="./SelectGoodsTag.ts">
</script>

<style lang="scss" scoped>

</style>