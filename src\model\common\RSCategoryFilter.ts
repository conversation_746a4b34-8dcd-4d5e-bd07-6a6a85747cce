export default class RSCategoryFilter {
  //
  key: Nullable<string> = null
  //
  categoryIdEquals: Nullable<string> = null
  //
  categoryIdLikes: Nullable<string> = null
  //
  categoryNameLikes: Nullable<string> = null
  //
  upperEquals: Nullable<string> = null
  //
  upperLikes: Nullable<string> = null
  //
  upperIn: Nullable<string[]> = null
  //
  categoryIdIn: Nullable<string[]> = null
  //
  upperIsNull: Nullable<boolean> = null
  //
  categoryIdOrder: Nullable<boolean> = null
  //
  categoryNameOrder: Nullable<boolean> = null
  //
  page: Nullable<number> = null
  //
  pageSize: Nullable<number> = null
  //
  sorters = {
    categoryId: 'asc'
  }
}