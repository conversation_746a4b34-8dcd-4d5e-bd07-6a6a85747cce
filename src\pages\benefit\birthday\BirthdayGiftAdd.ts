import { Component, Vue } from 'vue-property-decorator'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import FormItem from 'cmp/formitem/FormItem.vue'
import GiftRule from 'model/benefit/GiftRule'
import ActiveAddCoupon from 'cmp/activeaddcoupon/ActiveAddCoupon.vue'
import GradeApi from 'http/grade/grade/GradeApi'
import BirthdayGiftItem from "pages/benefit/birthday/BirthdayGiftItem";
import BirthdayB<PERSON>fit<PERSON><PERSON> from 'http/benefit/BirthdayBenefitApi'
import GiftInfo from "model/common/GiftInfo";
import Grade from "model/grade/Grade";

@Component({
  name: 'BirthdayGiftAdd',
  components: {
    BreadCrume,
    FormItem,
    ActiveAddCoupon,
  }
})
export default class BirthdayGiftAdd extends Vue {
  isActive = false
  $refs: any
  giftBagType = 'same'
  panelArray: any = []
  diffGiftData: any[] = []
  couponTemplateDialogShow = false
  giftRuleData: GiftRule = new GiftRule()
  sameGiftData: BirthdayGiftItem = new BirthdayGiftItem()
  grades: Grade[] = []

  created() {
    if (sessionStorage.getItem('locale') === 'zh_CN') {
      this.isActive = true
    } else {
      this.isActive = false
    }
    this.panelArray = [
      {
        name: this.formatI18n('/公用/菜单', '节日有礼'),
        url: 'score-init'
      },
      {
        name: this.formatI18n("/权益/生日权益初始化/生日权益初始化/title", "生日送礼"),
        url: "",
      },
    ];
  }

  mounted() {
    if (this.$route.query.from === 'edit') {
      this.resetData()
    } else {
      this.getGrade()
    }
  }

  pickerChange() {
    console.log(this.giftRuleData.giftDay!.time);

  }

  typeChange() {
    this.giftRuleData.giftDay!.dayNo = null;
  }

  async doModify() {
    this.initSubmitParams()
    if (!this.giftRuleData.giftDay!.dayNo && this.giftRuleData.giftDay!.type !== 'BIRTHDAY') {
      this.$message.warning(this.formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/新建/校验', '权益生效时间不能为空'))
      return
    }
    if (!this.giftRuleData.giftDay!.time) {
      this.$message.warning(this.formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/新建/校验', '权益生效时间不能为空'))
      return
    }
    if (this.giftBagType === 'same' && !this.sameGiftData.sendScore && !this.sameGiftData.sendCoupon) {
      this.$message.warning(this.formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/新建/校验', '请至少选择一个礼包'))
      return
    }
    let errInfo = await this.doValidate()
    if (errInfo) {
      if (errInfo == '请至少选择一个礼包') {
        this.$message.warning(this.formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/新建/校验', '请至少选择一个礼包'))
      } else {
        this.$message.error(this.formatI18n("/储值/会员储值/储值充值活动/编辑页面", errInfo))
      }
      return
    }

    BirthdayBenefitApi.saveOrModifyGiftRule(this.giftRuleData)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.formatI18n('/公用/js提示信息', '保存成功'))
          this.$router.push({ name: 'birthday-gift-dtl' })
        }
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
  }

  resetCouponData(row: BirthdayGiftItem) {
    if (!row) {
      this.sameGiftData.couponData = []
    } else {
      row.couponData = []
    }
  }

  doGiftRadioChange() {
    if (this.giftBagType === 'different') {
      if (!this.grades || this.grades.length == 0) {
        this.getGrade()
      }
      if (this.diffGiftData && this.diffGiftData.length > 0) {
        this.diffGiftData.forEach((item: any) => {
          item.sendCoupon = false
          item.sendScore = false
          item.scoreCount = null
          this.$set(item, 'couponData', [])
        })
      }
    } else {
      if (this.sameGiftData && this.sameGiftData.couponData) {
        this.sameGiftData.sendCoupon = false
        this.sameGiftData.sendScore = false
        this.sameGiftData.scoreCount = null
        this.$set(this.sameGiftData, 'couponData', [])
      }
    }
  }

  doBack() {
    this.$router.back()
  }

  doAddCoupon(code: string) {
    let ref = 'addCoupon'
    if (code) {
      ref = code + ref
    }
    // this.$refs[ref].couponTemplateDialogShow = true
    this.$refs[ref].doAddCoupon(0)
  }

  resetPointsVal(value: BirthdayGiftItem) {
    if (value) {
      value.scoreCount = null
    }
  }

  private resetData() {
    BirthdayBenefitApi.giftRuleDetail()
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.giftRuleData = resp.data
          if (this.giftRuleData.allSameGiftBag) {
            let sameData = this.giftRuleData.allSameGiftBag
            this.giftBagType = 'same'
            if (sameData.points) {
              this.sameGiftData.sendScore = true
              this.sameGiftData.scoreCount = sameData.points
            }
            if (sameData.couponItems && sameData.couponItems.length > 0) {
              this.sameGiftData.sendCoupon = true
              this.sameGiftData.couponData = sameData.couponItems;
            }
          } else if (this.giftRuleData.differentGiftBag) {
            this.giftBagType = 'different'
            this.getGrade()
          }
        }
      }).catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
  }

  private initSubmitParams() {
    // 校验
    if (this.giftBagType === 'same') {
      let giftInfo = this.getGiftInfo(this.sameGiftData);
      this.giftRuleData.differentGiftBag = null
      this.giftRuleData.allSameGiftBag = giftInfo
    } else if (this.giftBagType === 'different') {
      let giftMap: any = {}
      let count = 0;
      for (let item of this.diffGiftData) {
        let key = item.code
        let giftInfo = this.getGiftInfo(item);
        if (giftInfo) {
          giftMap[key] = giftInfo
          count++
        }
      }
      this.giftRuleData.allSameGiftBag = null
      this.giftRuleData.differentGiftBag = count > 0 ? giftMap : null
    }
  }

  private getGiftInfo(item: BirthdayGiftItem) {
    let points = item.scoreCount
    let couponItems = item.couponData
    if (points == null && (couponItems == null || couponItems.length == 0)) {
      return null
    }
    let giftInfo: GiftInfo = new GiftInfo();
    if (item.sendScore) {
      giftInfo.points = points
    }
    if (item.sendCoupon) {
      giftInfo.couponItems = couponItems
    }
    return giftInfo
  }

  private initGiftData() {
    this.diffGiftData = []
    if (this.grades) {
      for (let item of this.grades) {
        let data: BirthdayGiftItem = new BirthdayGiftItem()
        data.code = item.code
        data.name = item.name
        this.diffGiftData.push(data)
      }
    }
    if (this.giftRuleData && this.giftRuleData.differentGiftBag) {
      let diffBagMap = this.giftRuleData.differentGiftBag
      for (let item of this.diffGiftData) {
        let giftInfo = diffBagMap[item.code]
        if (giftInfo) {
          if (giftInfo.points) {
            item.sendScore = true
            item.scoreCount = giftInfo.points
          }
          if (giftInfo.couponItems && giftInfo.couponItems.length > 0) {
            item.sendCoupon = true
            item.couponData = giftInfo.couponItems;
          }
        }
      }
    }
  }

  private getGrade() {
    GradeApi.listGrade('')
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.grades = resp.data
          this.initGiftData()
        }
      }).catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
  }

  doValidate() {
    return new Promise((resolve, reject) => {
      if (this.giftBagType === 'same') {
        if (this.sameGiftData.sendScore && !this.sameGiftData.scoreCount) {
          resolve('请填写积分')
        }
        if (this.sameGiftData.sendCoupon && this.sameGiftData.couponData.length === 0) {
          resolve('请添加券')
        }
      }
      if (this.giftBagType === 'different') {
        let flag = 0
        this.diffGiftData.forEach((item, index) => {
          if (item.sendScore && !item.scoreCount) {
            resolve('请填写积分')
          }
          if (item.sendCoupon && item.couponData.length == 0) {
            resolve('请添加券')
          }
          if (item.sendCoupon || item.sendScore) {
            flag++
          }
        })
        if (flag == 0) {
          resolve('请至少选择一个礼包')
        }
      }
      resolve('')
    })
  }
}