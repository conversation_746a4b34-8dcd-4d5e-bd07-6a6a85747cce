<!--
 * @Author: shi<PERSON><PERSON>i
 * @Date: 2024-01-23 10:05:05
 * @LastEditTime: 2024-01-23 15:22:28
 * @LastEditors: shikailei
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\importdialogs\ImportDialog.vue
 * 记得注释
-->
<template>
  <div>
    <el-dialog append-to-body :close-on-click-modal="false" :title="title" :visible.sync="dialogShow" class="import-dialog-view">
      <div class="dialog_tips" v-if="tips">{{ tips }}</div>
      <div class="wrap">
        <div class="left">{{ formatI18n('/公用/导入', '实例模板') }}：
          <a style="line-height: 12px; color: #318BFF;font-size: 12px;text-decoration: none"
             class="action-hover_download" @click="downloadTemplate">{{ templateName }}</a>
        </div>
        <div class="activite_type" v-if="activeKey && activeVal">
          <span>{{ activeKey }}</span>
          <span class="mgl_15">{{ activeVal }}</span>
        </div>
        <div class="left">{{ getImportDesc() }}</div>
        <el-upload
            :headers="uploadHeaders"
            :action="getUploadUrl"
            :auto-upload="false"
            :on-error="getErrorInfo"
            :on-success="getSuccessInfo"
            :with-credentials="true"
            class="upload-demo"
            ref="upload">
          <el-button :loading="loading" size="small" slot="trigger" type="default">{{
              formatI18n('/公用/导入', '选择文件')
            }}
          </el-button>
        </el-upload>
        <div class="left margin-top10" v-if="showClear">
          <div style="display: flex">
            {{formatI18n('/公用/导入/是否清空原有商品')}}：
            <!-- 是否清空原有商品 -->
            <el-radio-group v-model="isClear" @change="clearChange" style="margin-top: 2px;margin-left: 10px">
              <el-radio :label="true">{{formatI18n('/公用/导入/清空')}}</el-radio>
              <el-radio :label="false">{{formatI18n('/公用/导入/不清')}}</el-radio>
              <!-- <el-radio :label="true">清空</el-radio>
              <el-radio :label="false">不清</el-radio> -->
            </el-radio-group>
          </div>
          <div class="gray-text margin-top10">
            {{formatI18n('/公用/导入/清空是指清空原来商品，以本次导入商品为准；不清空是指在原有商品基础上增加本次导入商品')}}
          </div>
        </div>
      </div>
      <div class="dialog-footer" slot="footer">
        <el-button :loading="loading" @click="doModalClose('cancel')">{{ formatI18n('/公用/按钮', '取消') }}</el-button>
        <el-button :loading="loading" @click="doModalClose('confirm')" size="small" type="primary">
          {{ btnSubmitTxt }}
        </el-button>
      </div>
    </el-dialog>
    <ImportResultDialog ref="importResultDialog"/>
  </div>
</template>

<script lang="ts" src="./ImportDialog.ts">
</script>

<style lang="scss">
.import-dialog-view {
  display: flex;
  align-items: center;
  justify-content: center;

  .el-dialog {
    width: 500px;
    height: 355px;
    margin-top: 0px !important;

    .el-dialog__body {
      height: 240px;
      word-break: keep-all;
    }
  }

  .wrap {
    padding-top: 15px;
    padding-left: 70px;

    .left {
      text-align: left;
      margin-bottom: 10px;
    }
  }
  .gray-text {
    color: #868383;
  }
  .margin-top10 {
    margin-top: 10px;
  }
  .dialog_tips {
    padding-left: 70px;
    color: rgba(0,0,0,0.6);
  }
  .activite_type {
    margin-bottom: 10px;
  }
  .mgl_15 {
    margin-left: 5px;
  }
}
.action-hover_download {
  cursor: pointer;
  /* 添加手型光标 */
}
</style>