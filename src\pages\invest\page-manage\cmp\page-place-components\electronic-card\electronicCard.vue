<!--
 * @Author: haiding <EMAIL>
 * @Date: 2025-02-19 10:03:52
 * @LastEditors: haiding <EMAIL>
 * @LastEditTime: 2025-03-13 11:36:10
 * @FilePath: \new-kequn\src\pages\invest\page-manage\cmp\page-place-components\electronic-card\electronicCard.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="electronic-card"
  :style="{ padding: localProperty.styMarginTop + 'px ' + localProperty.styMarginRight + 'px ' + localProperty.styMarginBottom + 'px ' + localProperty.styMarginLeft + 'px' }"
  @click="activeTemplate">
    <div class="toolBar" v-if="activeIndex === index">
      <setting-toolbar :btns="toolBarBtns" @click="toolbarClick"></setting-toolbar>
    </div>
    <div class="list">
      <div class="item">
        <div class="item-top">
          <img src="@/assets/image/fellow/img_card.png" />
          <span>{{ i18n('储值卡') }}</span>
        </div>
        <div class="item-name">{{ i18n('电子卡')  }} </div>
        <p class="item-title">…</p>
        <div class="item-btn">{{ i18n('立即购买') }}</div>
      </div>
      <div class="item">
        <div class="item-top">
          <img src="@/assets/image/fellow/img_card.png" />
          <span>{{ i18n('储值卡') }}</span>
        </div>
        <div  class="item-name">{{ i18n('电子卡')  }}</div>
        <p class="item-title">{{ i18n('活动名称活动名称') }}…</p>
        <div class="item-btn">{{ i18n('立即购买') }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./electronicCard.ts"></script>

<style lang="scss" scoped>
.electronic-card {
  width: 100%;
  background: #f9f9f9;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  border: 2px solid transparent;
  position: relative;
  margin-bottom: 10px;

  &-bg {
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-size: 100%;
  }

  &-title {
    text-align: center;
    padding-top: 42px;
    font-size: 20px;
    color: #fff;
  }

  .toolBar {
    position: absolute;
    top: 0;
    right: 1px;
    z-index: 999;
    cursor: pointer;
  }

  .list {
    display: flex;
    justify-content: space-around;
    // width: 375px;
    height: 209px;

    // background: #F9F9F9;
    // box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    // border: 2px solid #007EFF;
    .item {
      width: 170px;
      height: 199px;
      background: linear-gradient(128deg, #FFFBFB 0%, #FFE6E6 100%);
      border-radius: 12px;
      border: 1px solid #FFCBCB;
      padding: 5px;
      box-sizing: border-box;
      position: relative;

      .item-name {
        position: absolute;
        top: 30px;
        font-weight: bold;
        font-size: 16px;
        color: #fff;
        left: 20px;
      }

      .item-top {
        // width: 160px;
        width: auto;
        height: 120px;
        border-radius: 8px;
        overflow: hidden;
        position: relative;

        img {
          width: 100%;
          height: auto;
        }

        span {
          position: absolute;
          left: 5px;
          top: 5px;
          padding: 0 2px;
          max-width: calc(100% - 10px);
          height: 14px;
          background: linear-gradient(180deg, #FFF2CA 0%, #FFE38D 100%);
          border-radius: 3px;
          font-weight: 500;
          font-size: 10px;
          color: #2D2D2D;
          line-height: 14px;
          text-align: center;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }
      }

      .item-title {
        width: 146px;
        height: 22px;
        font-weight: 500;
        font-size: 15px;
        color: #111111;
        line-height: 21px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        margin: 8px auto;
      }

      .item-btn {
        width: 146px;
        height: 28px;
        background: linear-gradient(90deg, #FF5413 0%, #FF3B3D 100%);
        border-radius: 20px;
        text-align: center;
        font-weight: 500;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 28px;
        margin:0 auto;
      }
    }
  }
}
</style>
