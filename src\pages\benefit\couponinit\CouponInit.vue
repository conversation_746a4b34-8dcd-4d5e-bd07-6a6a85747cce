<template>
    <div class="coupon-init">
        <BreadCrume :panelArray="panelArray"></BreadCrume>
        <div style="margin: 20px">
            <div style="border: 1px solid #e3e3e3;height: 154px">
                <div class="title">{{formatI18n('/权益/券/券初始化','券核销结果在零售交易中的记录方式')}}
                    <el-tooltip content="Bottom center" effect="light" placement="bottom">
                        <i class="iconfont  ic-info" style="font-size: 18px;color: #20A0FF;position: relative;top: 2px;"></i>
                        <template slot="content">
                            <div style="width: 300px;line-height: 32px;">{{formatI18n('/权益/券/券初始化','记录方式规则说明')}}</div>
                            <div style="width: 300px;color: #888">
                                {{formatI18n('/权益/券/券初始化','若记作优惠方式，则在优惠类型中体现，券抵用的金额不作为主营业务收入，不用纳税，商品毛利率会下降。')}}

                            </div>
                            <div style="width: 300px;color: #888;margin-top: 10px">
                                {{formatI18n('/权益/券/券初始化','若记作支付方式，则在支付方式中体现，券支付作为不找零支付方式，记入主营业务收入，需要纳税，不影响商品毛利')}}

                            </div>
                        </template>
                    </el-tooltip>
                </div>
                <div style="margin: 20px">
                    <p class="p-padding" v-if="dtl.subjectApportion === 'pay'">{{formatI18n('/公用/券模板','支付方式')}}</p>
                    <p class="p-padding" v-else>{{formatI18n('/公用/券模板','优惠方式')}}</p>
                </div>
            </div>
            <div style="border: 1px solid #e3e3e3;height: 154px;margin-top: 30px">
                <div class="title">{{formatI18n('/权益/券/券初始化','用券配置')}}</div>
                <div style="margin: 20px">
                    <p class="p-padding" v-if="(dtl.useLimit === 0 || !dtl.useLimit)">{{formatI18n('/权益/券/券初始化','交易用券')}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{formatI18n('/权益/券/券初始化','不限用券张数')}}</p>
                    <p class="p-padding" v-else>{{formatI18n('/权益/券/券初始化','交易用券')}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{getLimit(dtl.useLimit)}}</p>
                </div>
            </div>
            <div style="border: 1px solid #e3e3e3;height: 154px;margin-top: 30px">
              <div class="title">{{formatI18n('/权益/券/券初始化','叠加优惠')}}
                <el-tooltip content="Bottom center" effect="light" placement="bottom">
                  <i class="iconfont  ic-info" style="font-size: 18px;color: #20A0FF;position: relative;top: 2px;"></i>
                  <template slot="content">
                    <div style="width: 300px;line-height: 32px;">{{formatI18n('/权益/券/券初始化','叠加优惠规则说明')}}</div>
                    <div style="width: 300px;color: #888">
                      {{formatI18n('/权益/券/券初始化','用券优先，在核销券时，若多个商品参加前台优惠，只要存在商品满足券设置的优惠规则，则使用券。')}}
                    </div>
                    <div style="width: 300px;color: #888;margin-top: 10px">
                      {{formatI18n('/权益/券/券初始化','优惠优先，在核销券时，若多个商品参加前台优惠，只要存在商品不满足券设置的优惠规则，则不使用券。')}}

                    </div>
                  </template>
                </el-tooltip>
              </div>
              <div style="margin: 20px">
                <p class="p-padding" v-if="dtl.superpositionFavRule === 'coupon'">{{formatI18n('/权益/券/券初始化','用券优先')}}</p>
                <p class="p-padding" v-else>{{formatI18n('/权益/券/券初始化','优惠优先')}}</p>
              </div>
            </div>
            <div style="border: 1px solid #e3e3e3;height: 154px;margin-top: 30px">
              <div class="title">{{formatI18n('/公用/券模板','券承担方')}}</div>
              <div style="margin: 10px">
                <p class="p-padding" >{{formatI18n('/权益/券/券初始化','选项内容')}}: {{getCostPartyOptionsValue()}}</p>
              </div>
              <div style="margin: 10px">
                <p class="p-padding" >{{formatI18n('/权益/券/券初始化','选项内容默认值')}}: {{getCostPartyDefaultValue()}}</p>
              </div>
            </div>

            <div style="border: 1px solid #e3e3e3;height: 164px;margin-top: 30px">
              <div class="title">{{formatI18n('/公用/券模板','券码前缀')}}</div>
                <el-row>
                  <el-col :span="6">
                    <div style="margin: 10px">
                      <p class="p-padding" >{{formatI18n('/公用/券模板','现金券')}}: {{getCouponPrefix("cash")}}</p>
                      </div>
                  </el-col>
                  <el-col :span="6">
                    <div style="margin: 10px">
                    <p class="p-padding" >{{formatI18n('/公用/券模板','折扣券')}}: {{getCouponPrefix("discount")}}</p>
                  </div>
                  </el-col>
                  <el-col :span="6">
                     <div style="margin: 10px">
                      <p class="p-padding" >{{formatI18n('/公用/券模板','特价券')}}: {{getCouponPrefix("special_price")}}</p>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div style="margin: 10px">
                    <p class="p-padding" >{{formatI18n('/公用/券模板','提货券')}}: {{getCouponPrefix("goods")}}</p>
                  </div>
                  </el-col>
                </el-row>
                <el-row>
                  <!-- <el-col :span="6">
                    <div style="margin: 10px">
                      <p class="p-padding" >{{formatI18n('/公用/券模板','运费券')}}: {{getCouponPrefix("freight")}}</p>
                    </div>
                  </el-col> -->
                  <el-col :span="6">
                  <div style="margin: 10px">
                    <p class="p-padding" >{{formatI18n('/公用/券模板','随机金额券')}}: {{getCouponPrefix("random_cash")}}</p>
                  </div>
                  </el-col>
                  <el-col :span="6">
                    <div style="margin: 10px">
                      <p class="p-padding" >{{formatI18n('/公用/券模板','兑换券')}}: {{getCouponPrefix("exchange_goods")}}</p>
                    </div>
                  </el-col>
                  <el-col :span="6">
                  <div style="margin: 10px">
                    <p class="p-padding" >{{formatI18n('/公用/券模板','积分券')}}: {{getCouponPrefix("points")}}</p>
                  </div>
                  </el-col>
                  <!-- <el-col :span="6">
                  <div style="margin: 10px">
                    <p class="p-padding" >{{formatI18n('/公用/券模板','权益券')}}: {{getCouponPrefix("equity")}}</p>
                  </div>
                  </el-col> -->
                </el-row>
            </div>
        </div>
    </div>
</template>

<script lang="ts" src="./CouponInit.ts">
</script>

<style lang="scss">
.coupon-init{
    width: 100%;
    height: 100%;
    background: white;
    overflow: auto;
    .title{
        height: 36px;
        line-height: 36px;
        font-size: 16px;
        margin: 0 20px;
        border-bottom: 1px solid #e3e3e3;
    }
    .p-padding{
        padding-top: 20px;
    }
}
</style>