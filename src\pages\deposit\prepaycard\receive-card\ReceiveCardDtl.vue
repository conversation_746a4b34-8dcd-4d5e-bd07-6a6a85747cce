<!--
 * @Author: 袁智鹏
 * @Date: 2024-07-29 10:40:06
 * @LastEditTime: 2024-07-29 10:40:06
 * @LastEditors: 袁智鹏
 * @Description:
 * @FilePath: \phoenix-web-ui\src\pages\deposit\prepaycard\receive-card\ReceiveCardDtl.vue
 * 记得注释
-->
<template>
  <div class="receive-card-dtl-container">
    <BreadCrume :panelArray="panelArray" :fontSize="18">
      <template slot="operate">
        <el-button
            @click="doAudit"
            type="primary"
            v-if="
            billDtl.state !== 'AUDITED' &&
            hasOptionPermission('/卡/卡管理/领卡单', '单据审核')"
        >{{ i18n('审核') }}
        </el-button>
        <el-button
            @click="doEdit"
            v-if="
            billDtl.state !== 'AUDITED' &&
            hasOptionPermission('/卡/卡管理/领卡单', '单据维护')"
        >{{ i18n('修改') }}
        </el-button>
      </template>
    </BreadCrume>
    <div class="top-wrap">
      <div class="left">
      </div>
      <div class="right">
        <div class="top">
          <div class="item1">
            <div class="bill"><span>{{i18n('单号')}}：</span>{{ billDtl.billNumber }}</div>
            <div class="name"><B>{{i18n('领卡单')}}</B></div>
          </div>
          <div class="item2">
            <div class="desc">{{i18n('状态')}}</div>
            <div class="state">
              <el-tag type="success" v-if="billDtl.state === 'AUDITED'">{{
                  i18n("已审核")
                }}</el-tag>
              <el-tag type="warning" v-if="billDtl.state !== 'AUDITED'">{{
                  i18n("未审核")
                }}</el-tag>
            </div>
          </div>
        </div>
        <div class="bottom">
          <el-row>
            <el-col :span="8">
              <div class="bottom-receive-card-bill-dtl">{{i18n('起始卡号')}}：&emsp; {{billDtl.startCardCode || '-'}}</div>
            </el-col>
            <el-col :span="8">
              <div class="bottom-receive-card-bill-dtl">{{i18n('结束卡号')}}：&emsp; {{billDtl.endCardCode || '-'}}</div>
            </el-col>
            <el-col :span="8">
              <div class="bottom-receive-card-bill-dtl">{{i18n('领卡数量')}}：&emsp; {{lineCount}}</div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <div class="bottom-receive-card-bill-dtl" v-if="billDtl.outOrg">{{i18n('领出组织')}}：&emsp; {{('['+billDtl.outOrg.id+']'+billDtl.outOrg.name) || '-'}}</div>
            </el-col>
            <el-col :span="8">
              <div class="bottom-receive-card-bill-dtl" v-if="billDtl.inOrg">{{i18n('领入组织')}}：&emsp; {{('['+billDtl.inOrg.id+']'+billDtl.inOrg.name) || '--'}}</div>
            </el-col>
          </el-row>
          <el-row>
            <div class="bottom-receive-card-bill-dtl">{{i18n('备注')}}：&emsp; {{billDtl.remark}} </div>
          </el-row>
        </div>
      </div>
    </div>
    <div style="height: 20px; background-color: rgba(242, 242, 242, 1)"></div>
    <div class="center-wrap">
      <div>
        <p class="sub-item" style="display: flex; justify-content: space-between;">
          <span><B>{{i18n('领卡明细')}}</B></span>
          <span style="margin-left: 20px">
            <el-button
                @click="doExport"
                v-if="
            hasOptionPermission('/卡/卡管理/领卡单', '单据导出')"
            >{{ i18n('导出明细') }}
          </el-button>
          </span>
        </p>
        <el-table :data="billLines">
          <el-table-column fixed :label="i18n('卡号')" prop="">
            <template slot-scope="scope">
              <span>{{ scope.row.cardCode }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed :label="i18n('领出状态')">
            <template slot-scope="scope">
              <div style="display:flex;align-items:center">
                <span class="dot" :style="{background: computeState(scope.row.state).color}"></span>
                <span>{{computeState(scope.row.state).state}}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('领出失败原因')">
            <template slot-scope="scope">
              <span>{{ scope.row.result || '--' }} </span>
            </template>
          </el-table-column>
        </el-table>
        <div style="margin-top: 15px">
          <el-pagination
              no-18n
              :current-page="page.page"
              :page-size="page.size"
              :page-sizes="[10, 20, 30, 40]"
              :total="page.total"
              @current-change="onHandleCurrentChange"
              @size-change="onHandleSizeChange"
              background
              layout="total, prev, pager, next, sizes,  jumper"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <DownloadCenterDialog :dialogvisiable="fileDialogVisible" :showTip="showTip" @dialogClose="doDownloadDialogClose"></DownloadCenterDialog>
  </div>

</template>


<script lang="ts" src="./ReceiveCardDtl.ts">
</script>

<style lang="scss" scoped>
.receive-card-dtl-container {
  width: 100%;
  height: 100%;
  background-color: white;
  overflow: auto;
  .top-wrap {
    display: flex;
    flex-direction: row;
    .left {
      width: 40px;
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
  }
    .right {
      display: flex;
      flex: 1;
      flex-direction: column;
      position: relative;
      .top {
        display: flex;
        height: 105px;
        border-bottom: 1px solid rgba(242, 242, 242, 1);
        margin-right: 20px;
        .item1 {
          .bill {
            margin-top: 16px;
            color: rgba(51, 51, 51, 0.***************);
        }
          .name {
            font-weight: 500;
            margin-top: 8px;
            font-size: 20px;
        }
      }
        .item2 {
          padding-left: 70px;
          padding-top: 16px;
          .desc {
            color: rgba(51, 51, 51, 0.***************);
        }
          .state {
            font-weight: 500;
            margin-top: 8px;
            font-size: 20px;
        }
      }
    }
      .bottom {
        .bottom-receive-card-bill-dtl {
          margin-top: 10px;
          .el-table::before {
            height: 0;
        }
          .el-table__body .el-table__row td {
            border-bottom: 0 !important;
        }
      }
        padding-bottom: 20px;
        .account-info {
          margin-top: 10px;
      }
        .red {
          color: red;
      }
        .green {
          color: #008000;
      }
    }
  }
}
  .row-height {
    height: 20px;
    background-color: rgba(242, 242, 242, 1);
}
  .center-wrap,
  .foot-wrap {
    padding: 20px;
}
  .sub-item {
    font-size: 16px;
    padding-top: 10px;
    margin-bottom: 10px;
}
  .el-table__body .el-table__row td {
    border-bottom: 1px solid #d7dfeb !important;
}
}
.dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin: 0 4px 2px;
}
</style>