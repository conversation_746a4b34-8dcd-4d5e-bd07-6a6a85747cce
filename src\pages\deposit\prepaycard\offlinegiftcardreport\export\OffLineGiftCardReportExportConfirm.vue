<template>
  <div>
    <el-dialog :before-close="doBeforeClose" :close-on-click-modal="false"
               :title="formatI18n('/储值/预付卡/实体礼品卡报表/实体礼品卡报表导出')"
               :visible.sync="dialogShow" append-to-body class="coupon-report-export-confirm">
      <div>
        <el-alert
            :closable="false"
            style="margin-bottom: 15px"
            type="warning"
            title=""
            show-icon>
          <strong style="color: red">{{ formatI18n('/会员/会员资料/批量导出/弹框/风险提示/大数据量导出可能会影响系统稳定，请务必在业务非高峰期执行！') }}</strong>
        </el-alert>
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" class="dialog-form-class">
          <el-form-item :label="formatI18n('/储值/预付卡/实体礼品卡报表/实体礼品卡报表')" label-width="120px" prop="type">
            <el-row>
              <el-select @change="doTypeChange" v-model="ruleForm.type">
                <el-option :label="formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水')" value="SALES_HST"></el-option>
                <el-option :label="formatI18n('/储值/预付卡/电子礼品卡报表/消费流水')" value="CONSUME_HST"></el-option>
                <el-option :label="formatI18n('/储值/预付卡/电子礼品卡报表/退款流水')" value="REFUND_HST"></el-option>
                <el-option :label="formatI18n('/储值/预付卡/电子礼品卡报表/对账报表')" value="DAY_HST"></el-option>
              </el-select>
            </el-row>
            <!-- <el-row>
              <el-alert
                  :closable="false"
                  style="width: 100%;height: 25px;left: -12px;"
                  type="info"
                  :title="formatI18n('/储值/预付卡/实体礼品卡报表/批量导出/所有实体礼品卡报表都支持按月份导出')"
                  show-icon>
              </el-alert>
            </el-row> -->
          </el-form-item>
          <el-form-item class="is-required" :label="formatI18n('/储值/会员储值/会员储值查询/列表页面/导出方式')" label-width="120px">
            <el-radio-group v-model="dateType" @change="clearValidate">
              <el-radio label="month">{{formatI18n('/储值/会员储值/会员储值查询/列表页面/按月份')}}</el-radio>
              <el-radio label="day">
                {{formatI18n('/储值/会员储值/会员储值查询/列表页面/按日期')}}
                <div v-show="dateType=='day'" style="color:#7B7B7B;position:absolute;top:22px">-{{formatI18n('/储值/会员储值/会员储值查询/列表页面/日期跨度不能超过32天')}}</div>
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item class="is-required" v-show="dateType=='month'" :label="formatI18n('/营销/积分报表/批量导出/导出月份')" label-width="120px" prop="month">
            <el-date-picker
                size="small"
                :editable="false"
                type="month"
                v-model="ruleForm.month"
                :placeholder="formatI18n('/营销/积分报表/批量导出/导出月份/请选择月份')">
            </el-date-picker>
          </el-form-item>
          <el-form-item class="is-required" v-show="dateType=='day'" :label="formatI18n('/营销/积分报表/批量导出/导出日期')" label-width="120px" prop="dateRange">
            <el-date-picker
                size="small"
                :editable="false"
                type="daterange"
                range-separator="-"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                :start-placeholder="formatI18n('/会员/会员资料/开始日期')"
                :end-placeholder="formatI18n('/会员/会员资料/结束日期')"
                v-model="ruleForm.dateRange"
                :picker-options="dateRangeOption"
                :placeholder="formatI18n('/营销/积分报表/批量导出/导出月份/请选择月份')">
            </el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <div class="dialog-footer" slot="footer">
        <el-button @click="doModalClose" size="small">{{ formatI18n('/公用/按钮', '取消') }}</el-button>
        <el-button @click="doModalConfirm" size="small" type="primary">{{
            formatI18n('/会员/会员资料/批量导出/弹框/风险提示/确认导出')
          }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" src="./OffLineGiftCardReportExportConfirm.ts">
</script>

<style lang="scss">
.coupon-report-export-confirm {
  display: flex;
  align-items: center;
  justify-content: center;
  .el-dialog {
    width: 670px;
    //margin-top: 350px !important;
  }

  .dialog-form-class {
    .el-date-editor.el-input, .el-date-editor.el-input__inner {
      width: 90%;
    }

    .el-alert--info.is-light {
      border: none;
      background-color: white;
      color: var(--font-color-primary);
    }

    .el-select {
      width: 90%;
    }
  }
}
</style>