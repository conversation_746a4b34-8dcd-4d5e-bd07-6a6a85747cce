<template>
  <div class="mini-program-gain-coupon-activity-add">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button :loading="!allDataLoaded || loading.save" v-if="permission.editable" @click="doSave(true)" type="primary">{{ i18n('保存') }}
        </el-button>
        <el-button :loading="!allDataLoaded || loading.save" v-if="permission.editable && permission.auditable && (!state || state === 'INITAIL')"
          @click="doSave(false)">{{ i18n('保存并审核') }}
        </el-button>
        <el-button @click="doCancel">{{ i18n('取消') }}</el-button>
      </template>
    </BreadCrume>
    <div style="overflow: auto;height: 95%">
      <el-row class="current-page">
        <el-form :model="form.data" :rules="form.rules" ref="form" label-width="170px" v-loading="!allDataLoaded">
          <div class="panel">
            <div class="header">
              <div class="title">{{ i18n('活动信息') }}</div>
            </div>
            <div class="content">
              <el-form-item :label="i18n('活动名称')" :required="true" prop="name" v-if="!state || state === 'INITAIL'">
                <el-input maxlength="20" :placeholder="i18n('请输入不超过{0}个字符', ['20'])" style="width: 350px" v-model="form.data.name"></el-input>
              </el-form-item>
              <el-form-item :label="i18n('活动名称')" v-else>
                {{ form.data.name }}
              </el-form-item>
              <el-form-item :required="true" :label="i18n('活动时间')" prop="dateRange">
                <el-date-picker :end-placeholder="i18n('结束时间')" :start-placeholder="i18n('开始时间')" :picker-options="pickerOptions"
                  format="yyyy-MM-dd HH:mm:ss" range-separator="-" ref="selectDate" size="small" style="width: 350px" type="datetimerange"
                  v-model="form.data.dateRange" value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00', '23:59:59']" @change="changeTime">
                </el-date-picker>
              </el-form-item>
              <!-- 活动门店 -->
              <el-form-item :label="i18n('/公用/活动/活动信息/活动门店')" prop="stores">
                <ActiveStore ref="storeScope" v-model="form.data.stores" />
              </el-form-item>
              <el-form-item prop="groups" :label="formatI18n('/营销/积分活动/积分活动/商品满额加送积分/新建页面/活动信息/参与人群')">
                <SelectGroupCmp ref="selectGroupCmp" v-model="form.data.groups"></SelectGroupCmp>
              </el-form-item>
              <el-form-item :required="true" prop="needImprove" :label="formatI18n('/公用/菜单/需完善资料')">
                <dir style="display: flex;align-items: center;">
                  <span>
                    {{i18n('/会员/智能打标/已选择{0}项', [form.data.needImprove.length])}}
                  </span>
                  <span class="span-btn" style="margin-left:6px" @click="showSelectMemberInfoDialog">
                    {{form.data.needImprove.length ? i18n('修改') : i18n('请选择')}}
                  </span>
                </dir>
                <div class="tag-form" v-if="form.data.needImprove.length">
                  <el-tag v-for="(tag, index) of form.data.needImprove" closable @close="memberInfoDelete(index)" :key="tag"
                    style="margin-right: 8px">
                    {{ transMemberInfoName(tag) ? i18n(transMemberInfoName(tag)) : tag }}
                  </el-tag>
                </div>
                <div style="color:#aaa;">{{ i18n('勾选的资料全部完善才送赠礼') }}</div>
              </el-form-item>
            </div>
          </div>
          <div class="split"></div>

          <div class="panel">
            <div class="header">
              {{ i18n('礼包设置') }}
            </div>
            <div class="content upgrade-gift-edit" style="padding: 10px">
              <el-form ref="giftBag" label-width="50px" v-model="UpgradeLines" :rules="giftBagRules">
                <el-form-item :label="i18n('赠礼')" prop="gift" class="giftForm">
                  <el-form ref="gift" label-width="0" v-model="UpgradeLines.gift">
                    <div class="inner-checks">
                      <el-checkbox v-model="UpgradeLines.pointCheck" @change="pointCheckChange(UpgradeLines.pointCheck)"> </el-checkbox>
                      {{ i18n("赠送积分") }}
                      <el-form-item ref="points" style="width: 100px;display: inline-block;" prop="points" :rules="[
                        {
                          validator: pointsRule,
                          trigger: ['change', 'blur']
                        }
                      ]">
                        <el-input class="short" v-model="UpgradeLines.gift.points" @change="pointChange"
                          :disabled="!UpgradeLines.pointCheck"></el-input>
                      </el-form-item>
                      {{i18n('个')}}
                    </div>
                    <div class="inner-checks">
                      <el-checkbox v-model="UpgradeLines.couponCheck" @change="couponCheckChange(UpgradeLines.couponCheck)"> </el-checkbox>
                      {{ i18n("赠送券") }}
                      <el-form-item ref="couponItems" style="width: 100px;display: inline-block;" prop="couponItems" :rules="[
                        {
                          validator: couponItemsRule,
                          trigger: ['change', 'blur']
                        }
                      ]">
                        <el-button @click="doAddCoupon" v-if="
                            UpgradeLines.couponCheck &&
                            UpgradeLines.gift.couponItems &&
                            UpgradeLines.gift.couponItems.length <= 0
                          " type="text">+{{
                            formatI18n(
                              "/会员/等级/等级管理/未初始化状态/点击立即开始免费等级初始化/输入所有必填项点击下一步/然后输入所有必填项点击下一步/等级月礼下的表格",
                              "添加券"
                            )
                          }}</el-button>
                      </el-form-item>
                      <ActiveAddCoupon state="INITAIL" v-if="UpgradeLines.couponCheck" :ref="'firAddCoupon'" v-model="UpgradeLines.gift.couponItems">
                      </ActiveAddCoupon>
                    </div>
                    <div class="inner-checks">
                      <el-checkbox v-model="UpgradeLines.growthValueCheck" @change="growthValueCheckChange(UpgradeLines.growthValueCheck)">
                      </el-checkbox>
                      {{ i18n("赠送成长值") }}
                      <el-form-item ref="growthValue" style="width: 100px;display: inline-block;" prop="growthValue" :rules="[
                        {
                          validator: growthValueRule,
                          trigger: ['change', 'blur']
                        }
                      ]">
                        <el-input class="short" v-model="UpgradeLines.growthValue" :disabled="!UpgradeLines.growthValueCheck"
                          @change="growChange"></el-input>
                      </el-form-item>
                      {{i18n('个')}}
                    </div>
                  </el-form>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </el-form>
      </el-row>
    </div>
    <SelectMemberInfoDialog ref="selectMemberInfoDialog" @submit="doSubmitMemberInfo"></SelectMemberInfoDialog>
  </div>
</template>

<script lang="ts" src="./ImproveProfilesAdd.ts">
</script>

<style lang="scss" scoped>
.mini-program-gain-coupon-activity-add {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: hidden;

  a {
    cursor: pointer;
  }

  .current-page {
    height: 100% !important;
    overflow: auto;

    .panel {
      .header {
        font-weight: 500;
        padding: 20px 20px 0 30px;
        font-size: 18px;
        position: relative;
        &::before {
          content: "";
          display: block;
          width: 3px;
          height: 12px;
          position: absolute;
          top: 27px;
          left: 20px;
          background: #3189fd;
        }
      }

      .content {
        /*padding: 20px;*/

        .upload {
          position: relative;
          width: 100px;
          height: 100px;

          .el-upload {
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
          }

          .el-upload:hover {
            border-color: #409eff;
          }

          .avatar-uploader-icon {
            font-size: 28px;
            color: #8c939d;
            width: 100px;
            height: 100px;
            line-height: 100px;
            text-align: center;
          }

          .avatar {
            width: 100px;
            height: 100px;
            display: block;
          }

          &:hover {
            .del-item {
              display: initial !important;
            }
          }

          .del-item {
            display: none;
            position: absolute;
            width: 102px;
            height: 102px;
            background: rgba(0, 0, 0, 0.5);
            top: 0;
            left: 0;
            text-align: center;
            line-height: 100px;
            color: white;
            cursor: pointer;
          }
        }
      }
    }

    .split {
      height: 20px;
      background-color: #eeeff1;
    }

    .el-range__icon {
      line-height: 26px;
    }

    .el-range-separator {
      line-height: 26px;
    }

    .el-range__close-icon {
      line-height: 26px;
    }
  }

  .el-radio-button__inner {
    font-size: 14px;
  }

  .el-date-editor .el-range__icon {
    line-height: 24px !important;
  }

  .el-date-editor .el-range-separator {
    line-height: 24px !important;
  }
}
// 礼包
.upgrade-gift-edit {
  background: #f5f7fa;
  margin-left: 30px;
  margin-top: 30px;
  padding: 0;
  width: 580px;
  //height: 100%;
  overflow: auto;
  .content {
    padding: 30px;
  }
  .width-78 {
    width: 78px;
  }
  .width-298 {
    width: 298px;
  }
  .el-textarea__inner {
    height: 100px;
  }
  .inner-content {
    background-color: rgba(249, 249, 249, 1);
    padding-left: 10px;
    margin: -7px 20px 20px 30px;
    height: auto;
    padding-bottom: 20px;
    width: 50%;
  }
  .el-checkbox {
    margin-right: 10px;
  }
  .weight {
    font-weight: 600;
    color: #515151;
  }
  .qf-form-item .qf-form-content {
    position: relative;
    margin-left: 105px !important;
  }
  .qf-form-item .qf-form-label {
    width: 110px !important;
  }
  .red {
    color: #f56c6c;
  }
  .short {
    width: 100px;
  }
  .inputs-area {
    margin-left: 40px;
    padding-top: 20px;
  }
  .inner-checks {
    margin-top: 15px;
    margin-left: 10px;
  }
}
.giftForm.is-error ::v-deep input.el-input__inner {
  border-color: #d9d9d9 !important;
}
</style>