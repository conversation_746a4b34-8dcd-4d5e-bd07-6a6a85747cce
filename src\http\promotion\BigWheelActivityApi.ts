/*
 * @Author: 黎钰龙
 * @Date: 2024-04-11 09:36:15
 * @LastEditTime: 2024-04-12 16:36:45
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\promotion\BigWheelActivityApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import BigWheelActivity from 'model/promotion/bigWheelActivity/BigWheelActivity'
import BigWheelAwardManagement from 'model/promotion/bigWheelActivity/BigWheelAwardManagement'
import BigWheelAwardManagementFilter from 'model/promotion/bigWheelActivity/BigWheelAwardManagementFilter'
import BigWheelTakePartRecord from 'model/promotion/bigWheelActivity/BigWheelTakePartRecord'
import BigWheelTakePartRecordFilter from 'model/promotion/bigWheelActivity/BigWheelTakePartRecordFilter'
import PopularizeResponse from "model/template/PopularizeResponse";
import BigWheelRaffleChanceLogFilter
  from "model/promotion/bigWheelActivity/BigWheelRaffleChanceLogFilter";
import BigWheelRaffleChanceLog from "model/promotion/bigWheelActivity/BigWheelRaffleChanceLog";

export default class BigWheelActivityApi {
  /**
 * 批量导出大转盘奖品管理
 * 批量导出大转盘奖品管理。
 * 
 */
  static exportAwardManagement(body: BigWheelAwardManagementFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/big-wheel-activity/exportAwardManagement`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量导出大转盘参与记录
   * 批量导出大转盘参与记录。
   * 
   */
  static exportTakePartRecord(body: BigWheelTakePartRecordFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/big-wheel-activity/exportTakePartRecord`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 大转盘奖品管理查询
  * 大转盘奖品管理查询
  * 
  */
  static queryAwardManagement(body: BigWheelAwardManagementFilter): Promise<Response<BigWheelAwardManagement[]>> {
    return ApiClient.server().post(`/v1/big-wheel-activity/queryAwardManagement`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 大转盘参与记录列表查询
   * 大转盘参与记录列表查询
   * 
   */
  static queryTakePartRecord(body: BigWheelTakePartRecordFilter): Promise<Response<BigWheelTakePartRecord[]>> {
    return ApiClient.server().post(`/v1/big-wheel-activity/queryTakePartRecord`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 大转盘活动详情
   * 大转盘活动详情。
   * 
   */
  static getBigWheelActivity(id: string): Promise<Response<BigWheelActivity>> {
    return ApiClient.server().get(`/v1/big-wheel-activity/getBigWheelActivity/${id}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建或修改大转盘活动
   * 新建或修改大转盘活动
   * 
   */
  static saveBigWheelActivity(body: BigWheelActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/big-wheel-activity/saveBigWheelActivity`, body, {
    }).then((res) => {
      return res.data
    })
  }


  /**
   * 内容模板推广
   *
   */
  static popularize(id: string): Promise<Response<PopularizeResponse>> {
    return ApiClient.server().get(`/v1/big-wheel-activity/popularize/${id}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量导出大转盘抽奖机会日志列表查询
   * 批量导出大转盘抽奖机会日志列表查询。
   *
   */
  static exportRaffleChanceLog(body: BigWheelRaffleChanceLogFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/big-wheel-activity/exportRaffleChanceLog`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 大转盘抽奖机会日志列表查询
   * 大转盘抽奖机会日志列表查询
   *
   */
  static queryRaffleChanceLog(body: BigWheelRaffleChanceLogFilter): Promise<Response<BigWheelRaffleChanceLog[]>> {
    return ApiClient.server().post(`/v1/big-wheel-activity/queryRaffleChanceLog`, body, {
    }).then((res) => {
      return res.data
    })
  }
}
