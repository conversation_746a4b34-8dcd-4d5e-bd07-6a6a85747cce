/*
 * @Author: 黎钰龙
 * @Date: 2024-02-05 09:56:55
 * @LastEditTime: 2024-02-05 10:09:21
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\promotion\UnionActivityApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import PointsActivityBody from 'model/points/activity/PointsActivityBody'
import UnionActivityQuery from 'model/promotion/UnionActivityQuery'
export default class UnionActivityApi {
  /**
 * 根据类型查询活动
 * 根据类型查询活动。
 * 
 */
  static queryByType(body: UnionActivityQuery): Promise<Response<PointsActivityBody>> {
    return ApiClient.server().post(`/v1/web/activity/queryByType`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量审核活动
   * 批量审核活动。
   * 
   */
  static batchAudit(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/web/activity/batch/audit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量删除活动
   * 批量删除活动。
   * 
   */
  static batchRemove(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/web/activity/batch/remove`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量终止活动
   * 批量终止活动。
   * 
   */
  static batchStop(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/web/activity/batch/stop`, body, {
    }).then((res) => {
      return res.data
    })
  }
}