import { Component, Inject, Prop } from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import AbstractSelectDialog from './AbstractSelectDialog'
import CouponTemplate from 'model/coupon/template/CouponTemplate'
import CouponTemplateFilter from 'model/coupon/template/CouponTemplateFilter'
import CouponTemplateApi from 'http/coupon/template/CouponTemplateApi'
import ChannelRange from 'model/common/ChannelRange'
import SelectStoreActiveDtlDialog from 'pages/deposit/mbrdeposit/active/dialog/SelectStoreActiveDtlDialog.vue'
import CouponItem from 'model/common/CouponItem'
import BrowserMgr, { SessionStorage } from 'mgr/BrowserMgr'
import Channel from 'model/common/Channel'
import RSChannelManagement from 'model/common/RSChannelManagement'
import CommonUtil from 'util/CommonUtil'
import CouponTemplateTagApi from 'http/coupon/template/CouponTemplateTagApi'
import CouponTemplateTagFilter from 'model/coupon/CouponTemplateTagFilter'
import CouponTemplateTag from 'model/coupon/CouponTemplateTag'
import CouponTemplateTagRelation from 'model/coupon/CouponTemplateTagRelation'
import ActivityBody from "model/common/ActivityBody";
import PointsActivityFilter from "model/points/activity/PointsActivityFilter";
import PointsActivityApi from "http/points/activity/PointsActivityApi";

@Component({
  name: 'ActivitySelectorDialog',
  components: {
    FormItem,
    SelectStoreActiveDtlDialog
  }
})
export default class ActivitySelectorDialog extends AbstractSelectDialog<
  ActivityBody
> {
  child: CouponItem = new CouponItem()
  // couponDialogShow = false
  @Prop({ default: () => new CouponTemplateFilter() })
  filter: PointsActivityFilter
  @Prop()
  couponType: string
  @Prop({
    default: false
  })
  hideSource: Boolean

  @Prop({
    type: Array,
    default() {
      return []
    }
  })
  excludedTypes: string[]
  labelWidthMap: any = {
    zh_CN: '100px',
    en_US: '180px',
    default: '180px'
  }

  created() {

  }

  get labelWidth() {
    return this.labelWidthMap[
      CommonUtil.getLocale('locale') as string
    ] as string
  }


  reset(): void {
    this.filter.nameLike = null
    this.filter.numberEquals = null
  }

  getId(ins: ActivityBody): string {
    // @ts-ignore
    return ins.activityId
  }

  getName(ins: ActivityBody): string {
    // @ts-ignore
    return ins.name
  }

  getResponseData(response: any): any {
    return response.data.list
  }

  queryFun(): Promise<any> {
    this.filter.page = this.page.currentPage - 1
    this.filter.pageSize = this.page.size

    let oldMarketCenter = sessionStorage.getItem('marketCenter') || ''
    sessionStorage.setItem('marketCenter', this.marketCenter)

    let params = JSON.parse(JSON.stringify(this.filter))
    return PointsActivityApi.queryAll(params).finally(() => {
      sessionStorage.setItem('marketCenter', oldMarketCenter as string)
    })
  }
}
