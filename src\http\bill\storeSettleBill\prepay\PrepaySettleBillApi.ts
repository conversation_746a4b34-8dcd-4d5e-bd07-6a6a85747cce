import ApiClient from 'http/ApiClient'
import Response from 'model/default/Response'
import PrepayLedgerSettleBill from "model/bill/storeSettleBill/prepay/PrepayLedgerSettleBill";
import PrepaySettleBillFilter from "model/bill/storeSettleBill/prepay/PrepaySettleBillFilter";

export default class PrepaySettleBillApi {
  /**
   * 分页查询快手券账单
   * 查询快手券账单。
   * 
   */
  static query(body: PrepaySettleBillFilter): Promise<Response<PrepayLedgerSettleBill[]>> {
    return ApiClient.server().post(`/v1/platform-ledger/prepay/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导出快手券账单
   * 导出快手券账单。
   *
   */
  static export(body: PrepaySettleBillFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/platform-ledger/prepay/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
