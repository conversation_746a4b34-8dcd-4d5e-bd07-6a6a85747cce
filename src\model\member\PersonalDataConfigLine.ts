/*
 * @Author: 黎钰龙
 * @Date: 2025-04-03 16:20:12
 * @LastEditTime: 2025-04-03 16:20:22
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\member\PersonalDataConfigLine.ts
 * 记得注释
 */
export default class PersonalDataConfigLine {
  // 排序号
  sequence: Nullable<number> = null
  // 是否系统定义
  systemDefined: Nullable<boolean> = null
  // 字段名称
  fieldName: Nullable<string> = null
  // 字段格式 [text,date,select,image]
  fieldType: Nullable<string> = null
  // 字段选项
  fieldOptions: string[] = []
  // 默认选项
  defaultFieldOption: Nullable<string> = null
  // 提示文案
  tips: Nullable<string> = null
  // 是否必填
  required: Nullable<boolean> = null
  // 是否可修改
  modifiable: Nullable<boolean> = null
  // 启用状态
  enabled: Nullable<boolean> = null
}