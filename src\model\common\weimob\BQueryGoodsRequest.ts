// 查询微盟商品
export default class BQueryGoodsRequest {
    // 商品状态。状态包括：0-上架；1-下架；2-已售罄。
    goodsStatus: Nullable<number> = null
    // 商品分组 ID，传入二级分组 ID。可以通过 weimob_shop/goods/classify/getList 接口获取该 ID。
    classifyId: Nullable<number> = null
    // 查询商品的搜索内容，支持商品名称搜索（可模糊搜索）。
    search: Nullable<string> = null
    // 排序方式，取值范围[1,4]。1-商品销量销量 2-上下架时间 3-商品价格 4-商品排序值。
    sort: Nullable<number> = null
    // 搜索类型。支持的类型包括：1-商品名称；2-商品编码；3-规格条码；4-规格编码。不传默认为 1。
    searchType: Nullable<number> = null
    // 最小销售价，精确到 2 位小数。单位：元。例如：750.50，表示 750 元 5 角。
    minSalePrice: Nullable<number> = null
    // 最大销售价，精确到 2 位小数。单位：元。例如：800.15，表示 800 元 1 角 5 分。
    maxSalePrice: Nullable<number> = null
    // 搜索匹配类型。支持的类型包括：1-模糊匹配；2-精确匹配 （条码只支持精确匹配）。 不传默认为 1。
    searchOptionType: Nullable<number> = null
    // 标签ID，最多20个。可以通过 weimob_shop/goods/tag/getList 接口获取此 ID。
    goodsTagIdList: string[] = []
    // 商品更新起始时间, 时间戳，单位：毫秒
    startUpdateTime: Nullable<Date> = null
    // 商品更新结束时间， 时间戳，单位：毫秒
    endUpdateTime: Nullable<Date> = null
    // 分页页码，上限为500。
    pageNum: Nullable<number> = null
    // 每页包含的数据条数，上限为20。
    pageSize: Nullable<number> = null
  }