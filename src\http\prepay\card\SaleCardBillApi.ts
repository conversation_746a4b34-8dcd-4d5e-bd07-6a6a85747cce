import ApiClient from 'http/ApiClient'
import CardTemplate from 'model/card/template/CardTemplate'
import Response from 'model/common/Response'
import SaleCardBill from 'model/prepay/card/SaleCardBill'
import SaleCardBillFilter from 'model/prepay/card/SaleCardBillFilter'

export default class SaleCardBillApi {
  /**
   * 新建售卡单
   * 新建售卡单
   * 
   */
  static save(body: SaleCardBill): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/card-sale-bill/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 审核售卡单
   * 审核售卡单
   *
   */
  static audit(billNumber: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/card-sale-bill/audit/${billNumber}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 新建并审核售卡单
  * 新建并审核售卡单
  * 
  */
  static saveAndAudit(body: SaleCardBill): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/card-sale-bill/saveAndAudit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 修改售卡单
  * 修改售卡单
  * 
  */
  static saveModify(body: SaleCardBill): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/card-sale-bill/saveModify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 查询售卡单列表
  * 查询售卡单列表
  * 
  */
  static query(body: SaleCardBillFilter): Promise<Response<SaleCardBill[]>> {
    return ApiClient.server().post(`/v1/card-sale-bill/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询售卡单详情
   * 查询售卡单详情
   *
   */
  static get(billNumber: string): Promise<Response<SaleCardBill>> {
    return ApiClient.server().get(`/v1/card-sale-bill/get/${billNumber}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 删除售卡单
  * 删除售卡单
  *
  */
  static remove(billNumber: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/card-sale-bill/remove/${billNumber}`, {
    }).then((res) => {
      return res.data
    })
  }


  /**
  * 批量删除售卡单
  * 批量删除售卡单
  *
  */
  static batchRemove(body: Array<string>): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/card-sale-bill/removes`, { billNums: body }, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 根据起始卡号查卡模板
  * 根据起始卡号查卡模板
  *
  */
  static getByStartCode(cardCode: string): Promise<Response<CardTemplate>> {
    return ApiClient.server().post(`/v1/card-template/get/${cardCode}`, {
    }).then((res) => {
      return res.data
    })
  }
}