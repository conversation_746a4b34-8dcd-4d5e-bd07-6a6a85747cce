<template>
    <el-dialog :before-close="doBeforeClose" :close-on-click-modal="false"
               :title="title"
               :visible.sync="dialogShow" append-to-body class="unbind-dialog">
        <div class="wrap">
            <div class="flex-wrap">
                <img src="~assets/image/member/tip.png" style="position: relative;top: 18px;">
                <div style="margin-left: 45px;margin-top: -30px">
                    <div>{{formatI18n('/会员/会员资料/详情/实体卡号点击解绑/请确认是否解绑此卡？')}}</div>
                    <div style="font-size: 16px;margin-top: 10px;">{{formatI18n('/会员/会员资料/详情/实体卡号点击解绑/实体会员卡：')}}{{uuid}}</div>
                </div>
            </div>
        </div>
        <div class="dialog-footer" slot="footer">
            <el-button @click="doCancel">{{formatI18n('/公用/按钮', '取消')}}</el-button>
            <el-button @click="doModalClose" size="small" type="primary">{{formatI18n('/会员/会员资料', '确认解绑')}}</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts" src="./UnBindDialog.ts">
</script>

<style lang="scss">
.unbind-dialog{
    display: flex;
    align-items: center;
    justify-content: center;
    .wrap{
        height: 70px;
        .item{
            width: 228px;
            height: 108px;
            border-radius: 5px;
            display: inline-block;
            margin-bottom: 24px;
            background: rgba(0, 0, 0, 0.0470588235294118);
            margin-right: 10px;
            .content{
                text-align: center;
            }
        }
    }
    .el-dialog{
        width: 440px !important;
        height: 228px !important;
    }
}
</style>