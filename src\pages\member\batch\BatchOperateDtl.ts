import { Component, Vue } from 'vue-property-decorator'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import FormItem from 'cmp/formitem/FormItem.vue'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import PointsAdjustBill from 'model/points/adjustbill/PointsAdjustBill'
import PointsAdjustBillLine from 'model/points/adjustbill/PointsAdjustBillLine'
import PointsAdjustBillApi from 'http/points/adjustbill/PointsAdjustBillApi'
import SysConfigApi from "http/config/SysConfigApi";
import I18nPage from 'common/I18nDecorator'
import MemberOperationBillApi from 'http/batchOperate/MemberOperationBillApi'
import CouponsDialog from './dialog/CouponsDialog'
@I18nPage({
    auto: false,
    prefix: [
        '/会员/会员批量操作单'
    ]
})

@Component({
    name: 'BatchOperateDtl',
    components: {
        SubHeader,
        FormItem,
        Bread<PERSON>rume,
        CouponsDialog
    }
})
export default class BatchOperateDtl extends Vue {
    panelArray = [
        {
            name: '',
            url: 'batch-operate-list'
        },
        {
            name: '',
            url: ''
        },
    ]
    // 分页
    page = {
        currentPage: 1,
        total: 0,
        size: 10
    }
    dtlTableData = []
    oparatorTableData = []
    billDtl: PointsAdjustBill = new PointsAdjustBill()
    queryDtl: PointsAdjustBillLine[] = []
    showOrg: boolean = false
    dialogShow: Boolean = false
    currentMemberId: any = ''

    created() {
        this.panelArray[0].name = this.i18n('会员批量操作单')
        this.panelArray[1].name = this.i18n('操作单详情')
        this.getScoreBillDtl()
        this.getQueryDetail()
        this.getConfig()
    }

    /**
     * 分页页码改变的回调
     * @param val
     */
    doBack() {
        this.$router.push({ name: 'batch-operate-list' })
    }

    doModify() {
        this.$router.push({ name: 'batch-operate-edit', query: { id: this.$route.query.id, from: 'edit' } })
    }

    doAudit() {
        MemberOperationBillApi.audit(this.$route.query.id.toString()).then((resp: any) => {
            if (resp && resp.code === 2000) {
                this.$message.success(this.formatI18n('/权益/积分/积分调整单详情/审核按钮', '审核成功') as string)
                this.getScoreBillDtl()
                this.getQueryDetail()
            }
        }).catch((error) => {
            if (error && error.message) {
                this.$message.error(error.message)
            }
        })
    }

    onHandleCurrentChange(val: number) {
        this.page.currentPage = val
        this.getQueryDetail()
    }

    /**
     * 每页多少条的回调
     * @param val
     */
    onHandleSizeChange(val: number) {
        this.page.size = val
        this.getQueryDetail()
    }

    stateText(state: any) {
        let str = ''
        switch (state) {
            case 'Using':
                str = this.formatI18n("/会员/会员资料", "使用中")
                break;
            case 'Blocked':
                str = this.formatI18n("/会员/会员资料", "已冻结")
                break;
            case 'Unactivated':
                str = this.formatI18n("/会员/会员资料", "未激活")
                break;
            case 'Canceled':
                str = this.formatI18n("/会员/会员资料", "已注销")
                break;
            default:
                break;
        }
        return str
    }

    private getScoreBillDtl() {
        MemberOperationBillApi.get(this.$route.query.id.toString()).then((resp: any) => {
            if (resp && resp.code === 2000) {
                this.billDtl = resp.data
                this.dtlTableData = resp.data.lines
            }
        }).catch((error) => {
            if (error && error.message) {
                this.$message.error(error.message)
            }
        })
    }

    private getQueryDetail() {

        MemberOperationBillApi.queryDetail(this.$route.query.id.toString(), this.page.currentPage - 1, this.page.size).then((resp: any) => {
            if (resp && resp.code === 2000) {
                this.queryDtl = resp.data
                this.page.total = resp.total
            }
        }).catch((error) => {
            if (error && error.message) {
                this.$message.error(error.message)
            }
        })
    }

    private getDetailStr(total: string, add: string, deduct: string) {
        let str: any = this.formatI18n('共调整{0}条,增加积分{1}个,扣减积分{2}个。')
        str = str.replace(/\{0\}/g, `<span style="font-weight: 700;color: orange">${total}</span>`)
        str = str.replace(/\{1\}/g, `<span style="font-weight: 700;color: red">${add}</span>`)
        str = str.replace(/\{2\}/g, `<span style="font-weight: 700;color: green">${deduct}</span>`)
        return str
    }

    private getOrgStr() {
        let str: any = this.formatI18n('/权益/积分/积分调整单详情/单头/发生组织：{0}')
        str = str.replace(/\{0\}/g,
            `${this.billDtl.occurredOrg ? this.billDtl.occurredOrg.name : ''}[${this.billDtl.occurredOrg ? this.billDtl.occurredOrg.id : ''}]`)
        return str
    }

    private getConfig() {
        SysConfigApi.get().then((resp: any) => {
            if (resp && resp.data) {
                this.showOrg = resp.data.enableMultiMarketingCenter
            }
        }).catch((error) => {
            if (error && error.message) {
                this.$message.error(error.message)
            }
        })
    }

    showDialog(id: any) {
        this.currentMemberId = id
        this.dialogShow = true
    }

    dialogClose() {
        this.dialogShow = false
        this.currentMemberId = ''
    }
}
