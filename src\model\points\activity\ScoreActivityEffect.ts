import ActivityBody from 'model/common/ActivityBody'
import OrgRankData from 'model/points/activity/OrgRankData'

export default class ScoreActivityEffect {
  // 
  activityBody: Nullable<ActivityBody> = null
  // 用积分数最高的三家店
  topThreeStores: OrgRankData[] = []
  // 用积分数最低的三家店
  bottomThreeStores: OrgRankData[] = []
  // 统计开始时间
  countBeginDate: Nullable<Date> = null
  // 统计结束时间
  countEndDate: Nullable<Date> = null
  // 参数活动人数
  numOfPeople: Nullable<number> = null
  // 用/发积分数
  scores: Nullable<number> = null
  // 统计天数
  countDays: Nullable<number> = null
  // 活动天数
  activityDays: Nullable<number> = null
}