/*
 * @Author: 黎钰龙
 * @Date: 2023-06-29 16:09:59
 * @LastEditTime: 2025-05-13 16:55:37
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\couponTemplateDtlSection\CouponTemplateDtlSection.ts
 * 记得注释
 */
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import CouponItem from "model/common/CouponItem";
import RSCostPartyFilter from "model/common/RSCostPartyFilter";
import CostPartyApi from "http/costparty/CostPartyApi";
import RSCostParty from "model/common/RSCostParty";
import RSChannelManagementFilter from "model/common/RSChannelManagementFilter";
import ChannelManagementApi from "http/channelmanagement/ChannelManagementApi";
import RSChannelManagement from "model/common/RSChannelManagement";
import Channel from "model/common/Channel";
import ChannelRange from "model/common/ChannelRange";
import { ExpiryType } from "model/common/ExpiryType";
import { IssueChannel } from "model/common/weimobCoupon/IssueChannel";
import { FreeChannel } from "model/common/weimobCoupon/FreeChannel";
import { ApportionType } from "model/common/ApportionType";
import xss from "xss";
import DateUtil from "util/DateUtil";
import I18nPage from "common/I18nDecorator";
import CouponInfo from 'model/common/CouponInfo'
import CouponThresholdDtl from "cmp/couponTemplateDtlItem/couponThresholdDtl/CouponThresholdDtl";
import FormItem from "cmp/formitem/FormItem";
import UseCouponRecordMethod from "cmp/couponTemplateDtlItem/useCouponRecordMethod/UseCouponRecordMethod.vue";
import CouponBearer from "cmp/couponTemplateDtlItem/couponBearer/CouponBearer";
import GroupMutexTemplateDtl from "cmp/coupontenplate/cmp/GroupMutexTemplateDtl";
import ActiveStoreDtl from "cmp/activestoredtl/ActiveStoreDtl";
import UseTimePeriod from "cmp/couponTemplateDtlItem/useTimePeriod/UseTimePeriod";
import UseCouponGoods from "cmp/couponTemplateDtlItem/useCouponGoods/UseCouponGoods";
import PromotionShowDialog from 'cmp/coupontenplate/cmp/PromotionShowDialog.vue'
import MallDiscount from "model/common/weimob/MallDiscount";
import { GoodsFavType } from "model/common/GoodsFavRule";
import Tools from "util/Tools";
import CouponTemplateApi from 'http/coupon/template/CouponTemplateApi'


@Component({
  name: "ActiveAddCouponDtl",
  components: {
    CouponThresholdDtl,
    FormItem,
    UseCouponRecordMethod,
    CouponBearer,
    GroupMutexTemplateDtl,
    ActiveStoreDtl,
    UseTimePeriod,
    UseCouponGoods,
    PromotionShowDialog
  },
})

@I18nPage({
  prefix: [
    "/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡",
    '/公用/券模板详情',
    '/公用/券模板',
    '/营销/券礼包活动/券查询',
    '/营销/券礼包活动/券礼包活动',
    '/营销/券礼包活动/核销第三方券',
    '/权益/券/券模板/券码前缀/',
    '/储值/预付卡/电子礼品卡活动/编辑页面',
    '/权益/券/券模板',
    '/公用/券模板/微盟适用商品',
    '/会员/洞察/公共/操作符'
  ],
  auto: true
})
export default class CouponTemplateDtlSection extends Vue {
  checkGoodsDialog = false;
  isPmsPayEngine = false;
  AllAmount: number = 0;
  @Prop()
  data: CouponItem;
  @Prop()
  externalCode: string;

  @Prop({
    default: () => {
      return {
        hideTitle: false,
        hideState: false,
        hideFaceAmount: false,
        hideType: false,
        hideName: false,
        hideOuterNumberNamespace: false,
      };
    },
  })
  options: {
    hideTitle: boolean;
    hideState: boolean;
    hideFaceAmount: boolean;
    hideType: boolean;
    hideName: boolean;
    hideOuterNumberNamespace: boolean;
  };

  @Prop({
    type: Boolean,
    default: false,
  })
  isTopPanel: boolean;

  @Prop({
    type: Boolean,
    default: false,
  })
  activityDtl: boolean;

  @Prop({
    type: Boolean,
    default: false,
  })
  deepDialog: boolean;

  @Prop({
    type: Boolean,
    default: false,
  })
  wxScanForCoupon: boolean;

  parties: RSCostParty[] = [];
  channels: RSChannelManagement[] = [];
  labelWidth: string = '110px'
  MallDiscount: any[] = MallDiscount(this.i18n) //券叠加其他优惠,指定活动可叠加,前端写死
  xss: Function = xss
  get getAllAmount() {
    let num: number = 0;
    if (
      this.data.coupons != null &&
      this.data.coupons.pickUpCouponAttribute != null &&
      this.data.coupons.pickUpCouponAttribute.pickUpGoods != null &&
      this.data.coupons.enablePayApportion == true
    ) {
      for (
        let i = 0;
        i < this.data.coupons.pickUpCouponAttribute.pickUpGoods.length;
        i++
      ) {
        const item = this.data.coupons.pickUpCouponAttribute.pickUpGoods[i];
        if (item.bookPayPrice != null && item.qty != null) {
          num += item.bookPayPrice * item.qty;
        }
      }
    }
    // this.AllAmount = num
    return num.toFixed(2);
  }

  get getLimitReceiveText() {
    let str = ''
    if (this.data && this.data.coupons && this.data.coupons.weimobCoupon) {
      if (this.data.coupons.weimobCoupon.perLimit) {
        str = this.formatI18n("/公用/券模板", "每位用户只能领取") + " " + this.data.coupons.weimobCoupon.perLimit + " " + this.formatI18n("/营销/券礼包活动/券礼包活动/张")
        if (this.data.coupons.weimobCoupon.dayLimit) {
          str += ' ' + this.formatI18n("/公用/券模板", "每天可领取") + ' ' + this.data.coupons.weimobCoupon.dayLimit + this.formatI18n("/营销/券礼包活动/券礼包活动/张")
        } else {
          str += ' ' + this.formatI18n("/公用/券模板", "每天可领取") + ' ' + this.formatI18n('/公用/券模板', '不限制数量')
        }
      } else {
        str = this.formatI18n('/储值/预付卡/电子礼品卡活动/编辑页面/不限制')
      }
    }
    return str
  }

  get isShowAfterUseAmount() {
    return this.data?.coupons?.exchangeGoodsCouponAttribute && this.data.coupons.couponBasicType === 'exchange_goods'
  }

  get formateThresholdStr() {
    let str = ''
    str = this.i18n('元减{0}元，最多可减')
    str = str.replace(/\{0\}/g, String(this.data?.coupons?.cashCouponAttribute?.faceAmount) || '--');
    return str
  }

  get getVerificationRule() {
    if (this.data.coupons?.writeOffScene) {
      let str = ''
      if (this.data.coupons.writeOffScene.payment) {
        str += `${this.i18n('购买商品')}、`
      }
      if (this.data.coupons.writeOffScene.recharge) {
        str += `${this.i18n('储值充值')}、`
      }
      if (this.data.coupons.writeOffScene.buyPrepayCard) {
        str += `${this.i18n('购买预付卡')}、`
      }
      str = str.slice(0, -1)
      return str || '--'
    } else {
      return '--'
    }
  }

  getStoreCount() {

    var count = 0;
    if (this.data && this.data.coupons && this.data.coupons.weimobCoupon
      && this.data.coupons.weimobCoupon.limitOrgType != 'ALL' && this.data.coupons.weimobCoupon.orgsUseRule
      && this.data.coupons.weimobCoupon.orgsUseRule.limitedOrgsType) {
      if (this.data.coupons.weimobCoupon.orgsUseRule.limitedOrgsType == 'org' && this.data.coupons.weimobCoupon.orgsUseRule.includeOrgIds) {
        count = this.data.coupons.weimobCoupon.orgsUseRule.includeOrgIds.length;
      }

      if (this.data.coupons.weimobCoupon.orgsUseRule.limitedOrgsType == 'brand' && this.data.coupons.weimobCoupon.orgsUseRule.includeBrandIds) {
        count = this.data.coupons.weimobCoupon.orgsUseRule.includeBrandIds.length;
      }
    }
    let str: any = this.formatI18n("/公用/券模板", "已选择{0}家门店");
    str = str.replace(/\{0\}/g, count);
    return str;
  }

  // 微盟适用商品排除
  get excludedGoodsList() {
    const goodsUseRule: any = this.data && this.data.coupons && this.data.coupons.weimobCoupon && this.data.coupons.weimobCoupon.goodsUseRule || {}
    if (goodsUseRule.limitedGoodsType == 'goods') {
      const limitGoodsTypeRule = goodsUseRule.limitGoodsTypeRule
      return limitGoodsTypeRule.existExcludeGoods && limitGoodsTypeRule.excludeGoodsIds
    }
    if (goodsUseRule.limitedGoodsType == 'goodsCategory') {
      const limitGoodsCategoryTypeRule = goodsUseRule.limitGoodsCategoryTypeRule
      return limitGoodsCategoryTypeRule.existExcludeGoods && limitGoodsCategoryTypeRule.excludeGoodsIds
    }
    if (goodsUseRule.limitedGoodsType == 'goodsGroup') {
      const limitGoodsGroupRule = goodsUseRule.limitGoodsGroupRule
      return limitGoodsGroupRule.existExcludeGoods && limitGoodsGroupRule.excludeGoodsIds
    }
    return []
  }

  //是否展示微盟设置
  get isShowWeimob() {
    return this.data.coupons?.weimobCoupon || this.data.coupons?.total || this.data.coupons?.weimobCoupon?.canUseDiscount
  }

  //是否展示逸刻设置
  get isShowRex() {
    return this.data.coupons?.rexCoupon
  }

  get goodsInfo() {
    const goodsUseRule: any = this.data && this.data.coupons && this.data.coupons.weimobCoupon && this.data.coupons.weimobCoupon.goodsUseRule || {}
    if (!goodsUseRule.limitedGoods) {
      return {
        str: '全部商品',
        list: [],
        type: 'none'
      }
    }
    if (goodsUseRule.limitedGoodsType == 'goods') {
      const limitGoodsTypeRule = goodsUseRule.limitGoodsTypeRule
      if (limitGoodsTypeRule!.goodsRange == 'all') {
        return {
          str: '全部商品',
          list: [],
          type: 'none'
        }
      } else {
        if (limitGoodsTypeRule.includeGoodsIds && limitGoodsTypeRule.includeGoodsIds.length > 0) {
          return {
            str: `已选择${limitGoodsTypeRule.includeGoodsIds.length}件商品`,
            list: limitGoodsTypeRule.includeGoodsIds,
            type: 'tips'
          }
        }
      }
    } else if (goodsUseRule.limitedGoodsType == 'goodsCategory') {
      const limitGoodsCategoryTypeRule = goodsUseRule.limitGoodsCategoryTypeRule
      let str = ''
      if (limitGoodsCategoryTypeRule.ruleInfos && limitGoodsCategoryTypeRule.ruleInfos.length > 0) {
        const categoryInfo = limitGoodsCategoryTypeRule.ruleInfos[0]
        str = '指定类目：' + categoryInfo.categoryName
        if (categoryInfo.childs && categoryInfo.childs.length > 0) {
          str = `${str} > ${categoryInfo.childs[0].categoryName}`
        }
        return {
          str: str,
          list: [],
          type: 'none'
        }
      }
    } else if (goodsUseRule.limitedGoodsType == 'goodsGroup') {
      const limitGoodsGroupRule = goodsUseRule.limitGoodsGroupRule
      let str = ''
      if (limitGoodsGroupRule.ruleInfos && limitGoodsGroupRule.ruleInfos.length > 0) {
        let childsNum = 0
        const list = limitGoodsGroupRule.ruleInfos.reduce((acc: any, cur: any) => {
          const firstName = cur.name
          if (cur.childs && cur.childs.length > 0) {
            cur.childs.map((item: any) => {
              childsNum = childsNum + 1
              acc.push({
                id: '',
                name: `${firstName} > ${item.name}`
              })
            })
          } else {
            childsNum = childsNum + 1
            acc.push({
              id: '',
              name: firstName
            })
          }
          return acc
        }, [])
        str = `${this.i18n('已选择')}${childsNum}${this.i18n('个分组')}`
        return {
          str,
          list,
          type: 'tips'
        }
      }
    }
    return {
      str: '',
      list: [],
      type: 'none'
    }
  }

  get isShowThreshold() {
    return this.data && this.data.coupons && this.data.coupons.couponBasicType && ['random_cash', 'goods_cash', 'exchange_goods', 'all_cash', 'all_discount', 'goods_discount', 'rfm_type', 'freight'].includes(this.data.coupons.couponBasicType)
  }

  get isShowUseMethod() {
    return this.data && this.data.coupons && this.data.coupons.couponBasicType && ['freight', 'goods', 'points', 'equity'].indexOf(this.data.coupons.couponBasicType) === -1
  }

  get isShowCouponBears() {
    return this.data && this.data.coupons && this.data.coupons.couponBasicType && ['freight', 'goods', 'points', 'equity'].indexOf(this.data.coupons.couponBasicType) === -1
  }

  get isShowMutex() {
    return this.data && this.data.coupons && this.data.coupons.couponBasicType && ['freight', 'goods', 'exchange_goods', 'points', 'equity'].indexOf(this.data.coupons.couponBasicType) === -1
  }

  get isShowSuperposition() {
    return this.data && this.data.coupons && this.data.coupons.couponBasicType && ['freight', 'goods', 'points', 'random_cash', 'equity'].indexOf(this.data.coupons.couponBasicType) === -1
  }

  get isDiscountLimit() {
    return this.data && this.data.coupons && this.data.coupons.couponBasicType && (this.data.coupons.couponBasicType === 'all_discount' || this.data.coupons.couponBasicType === 'rfm_type')
  }

  get isShowDiscountSetting() {
    return this.isShowThreshold || this.isShowUseMethod || this.isShowCouponBears || this.isShowMutex || this.isShowSuperposition || this.isDiscountLimit
  }

  // 会员价叠加
  get memberPriceFav() {
    return this.data.coupons?.goodsFavRules?.find((item) => item.favType === GoodsFavType.MEMBER_PRICE)?.superimposed || false
  }
  // 人工折扣
  get manualDiscountFav() {
    return this.data.coupons?.goodsFavRules?.find((item) => item.favType === GoodsFavType.MANUAL_DISCOUNT)?.superimposed || false
  }
  // 其他优惠
  get otherDiscountFav() {
    return this.data.coupons?.goodsFavRules?.find((item) => item.favType === GoodsFavType.OTHER)?.superimposed || false
  }

  created() {
    if (sessionStorage.getItem('locale') === 'en_US') {
      this.labelWidth = '180px'
    }
    this.getCostParty();
    this.getChannels();
    this.pmsPayEngine();

  }

  getCouponPromotionInfo(coupon: CouponInfo) {
    if (coupon.excludePromotion) {
      return coupon.promotionSuperpositionType == 'PART' ? this.formatI18n('/公用/券模板详情/指定促销活动叠加') : this.formatI18n('/公用/券模板详情/全部促销活动叠加')
    } else {
      return coupon.promotionSuperpositionType == 'PART' ? this.formatI18n('/公用/券模板详情/指定促销活动不叠加') : this.formatI18n('/公用/券模板详情/全部促销活动不叠加')
    }
  }

  getValidateDate(
    delayEffectDays: number,
    validityDays: number,
    expiryType: ExpiryType,
    months: number
  ) {
    return Tools.getValidateDate(delayEffectDays, validityDays, expiryType, months)
  }

  getGoodsNo(threshold: any) {
    let str: any = this.formatI18n(
      "/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送全场现金券的时候/用券门槛/不可叠加使用",
      "商品数量满{0}件及以上可用1张券"
    );
    str = str.replace(
      /\{0\}/g,
      `&nbsp;<span style="font-weight: bold">${threshold}</span>&nbsp;`
    );
    return str;
  }

  getGoodsLength(length: number) {
    let str: any = this.formatI18n(
      "/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送券的时候/点击券名称/用券商品为非全部商品时",
      "已选择{0}个商品"
    );
    str = str.replace(/\{0\}/g, length);
    return str;
  }

  getChannel(curChannel: ChannelRange) {
    let str: string = "";
    if (curChannel.channelRangeType === "ALL") {
      str = this.formatI18n("/公用/券模板/用券渠道", "全部渠道");
      return str;
    } else {
      // let channel = SessionStorage.getItem("channels");
      let channel = this.channels;
      if (channel && channel.length > 0) {
        if (
          curChannel &&
          curChannel.channels &&
          curChannel.channels.length > 0
        ) {
          curChannel.channels.forEach((sub: Channel) => {
            channel.forEach((item: RSChannelManagement) => {
              if (
                item &&
                item.channel &&
                sub.id === item.channel.id &&
                sub.type === item.channel.type
              ) {
                str += `${item.name}，`;
              }
            });
          });
        }
      }
      str = str.substring(0, str.length - 1);
    }
    return str;
  }
  getTemplateLabel() {
    const labelList = this.data.coupons?.templateTag || []
    return labelList.reduce((acc: any, cur) => {
      acc.push(cur.tagValue)
      return acc
    }, [])
  }

  getSychChannel() {
    const sychChannelIds = this.data?.coupons?.sychChannel?.map(item => this.channelId(item)) ?? []
    const channles = this.channels.filter(item => sychChannelIds?.includes(this.channelId(item.channel!)))
    const channelNames = channles.map(item => item.name)
    return channelNames.join('、')
  }

  getMoney(money: number, qty: number, useThresholdType: any) {
    if (money) {
      let str: any = this.formatI18n(
        "/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级套餐",
        "{0}元"
      );
      str = str.replace(/\{0\}/g, money);
      return str;
    } else if (qty && useThresholdType === 'QTY') {
      let str: any = this.formatI18n(
        "/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级套餐",
        "{0}件"
      );
      str = str.replace(/\{0\}/g, qty);
      return str;
    } else {
      return this.formatI18n(
        "/营销/积分活动/积分活动/单品满数量加送积分活动/编辑页面/积分加送规则/不限制"
      );
    }
  }

  getAllCash(threshold: any) {
    let str: any = this.formatI18n(
      "/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送全场现金券的时候/用券门槛/可叠加使用",
      "可叠加使用，全场消费每满{0}元可用1张券"
    );
    str = str.replace(
      /\{0\}/g,
      `&nbsp;<span style="font-weight: bold">${Number(threshold).toFixed(
        2
      )}</span>&nbsp;`
    );
    return str;
  }

  getAllCashNo(threshold: any) {
    let str: any = this.formatI18n(
      "/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送全场现金券的时候/用券门槛/不可叠加使用",
      "不可叠加使用，全场消费满{0}元及以上可用1张券"
    );
    str = str.replace(
      /\{0\}/g,
      `&nbsp;<span style="font-weight: bold">${Number(threshold).toFixed(
        2
      )}</span>&nbsp;`
    );
    return str;
  }

  getGoodsCash(threshold: any) {
    let str: any = this.formatI18n(
      "/公用/券模板详情/商品现金券/用券门槛/可叠加使用，用券商品消费每满{0}元可用1张券"
    );
    str = str.replace(
      /\{0\}/g,
      `&nbsp;<span style="font-weight: bold">${Number(threshold).toFixed(
        2
      )}</span>&nbsp;`
    );
    return str;
  }

  getGoodsCashNo(threshold: any) {
    let str: any = this.formatI18n(
      "/公用/券模板详情/商品现金券/用券门槛/不可叠加使用，用券商品消费满{0}元及以上可用1张券"
    );
    str = str.replace(
      /\{0\}/g,
      `&nbsp;<span style="font-weight: bold">${Number(threshold).toFixed(
        2
      )}</span>&nbsp;`
    );
    return str;
  }

  getAllDiscountCoupon(threshold: any) {
    let str: any = this.formatI18n(
      "/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送全场折扣券的时候/用券门槛",
      "全场消费满{0}元及以上可以用1张券"
    );
    str = str.replace(
      /\{0\}/g,
      `&nbsp;<span style="font-weight: bold">${Number(threshold).toFixed(
        2
      )}</span>&nbsp;`
    );
    return str;
  }

  getGoodsDiscount(threshold: any, discount: any, value: any) {
    let str: any = this.formatI18n(
      "/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级月礼/当有送单品折扣券的时候/用券门槛/用券商品满{0}件可享受其中{1}件{2}折"
    );
    str = str.replace(
      /\{0\}/g,
      `&nbsp;<span style="font-weight: bold">${threshold}</span>&nbsp;`
    );
    str = str.replace(
      /\{1\}/g,
      `&nbsp;<span style="font-weight: bold">${value}</span>&nbsp;`
    );
    str = str.replace(
      /\{2\}/g,
      `&nbsp;<span style="font-weight: bold">${discount}</span>&nbsp;`
    );
    return str;
  }

  getRfmTypeStep(amount: number) {
    let str: any = this.formatI18n(
      "/公用/券模板/商品折扣券/用券门槛/用券商品满{0}元及以上可用1张券"
    );
    str = str.replace(
      /\{0\}/g,
      `&nbsp;<span style="font-weight: bold">${Number(amount).toFixed(
        2
      )}</span>&nbsp;`
    );
    return str;
  }

  getMenkan(threshold: any) {
    if (threshold.thresholdType === "NONE") {
      return this.formatI18n("/公用/券模板/无门槛");
    } else {
      return `${this.formatI18n("/公用/券模板/满")}${threshold.threshold
        }${this.formatI18n("/公用/券模板/元")}${this.formatI18n(
          "/公用/券模板/可兑换"
        )}`;
    }
  }

  get getPointsAmount() {
    if (this.data.coupons!.pointsCouponAttribute) {
      return (
        this.formatI18n("/公用/券模板/券可换") +
        " " +
        this.data.coupons!.pointsCouponAttribute!.pointAmount +
        " " +
        this.formatI18n("/公用/券模板/积分")
      );
    }
  }

  get getIssueChannel() {
    let str = ''
    if (this.data && this.data.coupons && this.data.coupons.weimobCoupon) {
      if (this.data.coupons.weimobCoupon.issueChannel === IssueChannel.FREE) {
        str = this.formatI18n('/公用/券模板/仅免费发放') + ': '
        this.data.coupons.weimobCoupon.freeChannels.forEach((item, index, array) => {
          let split = index === array.length - 1 ? '' : '、'
          switch (item) {
            case FreeChannel.activity_issue:
              str += this.formatI18n('/公用/券模板/活动发券') + split
              break;
            case FreeChannel.directly_receive:
              const startTime = DateUtil.format(this.data.coupons?.weimobCoupon?.recommendStartTime!, "yyyy-MM-dd HH:mm:ss") || ''
              const endTime = DateUtil.format(this.data.coupons?.weimobCoupon?.recommendEndTime!, "yyyy-MM-dd HH:mm:ss") || ''
              const recommend = this.data.coupons?.weimobCoupon?.enableRecommend ? ` 开启推荐，推荐时间${startTime}至${endTime}` : '未开启推荐'
              str += this.formatI18n('/公用/券模板/直接领取') + recommend + split
              break;
            case FreeChannel.merchants_issue:
              str += this.formatI18n('/公用/券模板/商家发券') + split
              break;
            case FreeChannel.qiwei_issue:
              str += this.formatI18n('/公用/券模板/企微助手可发券') + split
              break;
            default:
              break;
          }
        });
      } else {
        str = this.formatI18n('/公用/券模板/付费购买')
      }
    }
    return str
  }

  get getUseScene() {
    let str = ''
    if (this.data && this.data.coupons && this.data.coupons.weimobCoupon) {
      const useScene = this.data.coupons.weimobCoupon.useScene
      if (useScene) {
        if (useScene.allSceneDTO) {
          str = this.i18n('全部场景')
        } else {
          let UseScenes: any[] = [
            {
              key: this.i18n('网店订单'),
              value: 1
            },
            {
              key: this.i18n('商家开单'),
              value: 10
            },
            {
              key: this.i18n('买家直接消费'),
              value: 8
            },
            {
              key: this.i18n('商家直接消费'),
              value: 9
            },
            {
              key: this.i18n('APP&收银台核销'),
              value: 3
            },
            {
              key: this.i18n('API核销'),
              value: 7
            },
            {
              key: this.i18n('扫码购'),
              value: 12
            }
          ]
          let scenes = (useScene.shoppingMallSceneList || []).map(x => {
            let obj = UseScenes.find(i => i.value == x)
            return obj ? obj.key : ''
          })
          str = this.i18n('指定场景') + '：' + scenes.join(', ')
        }
      }
      return str
    }
  }

  get getSpecialSetup() {
    let str = ''
    if (this.data.coupons && this.data.coupons!.useThreshold && this.data.coupons.specialPriceCouponAttribute) {
      str = `${this.formatI18n("/公用/券模板", "订单满")} 
          ${this.data.coupons.useThreshold.threshold} 
          ${this.formatI18n("/公用/券模板", "元")}，
          ${this.formatI18n("/公用/券模板", "用券商品第")} 
          ${this.data.coupons.specialPriceCouponAttribute.favSegment} 
          ${this.formatI18n("/公用/券模板", "件享")} 
          ${this.data.coupons.specialPriceCouponAttribute.specialPrice} 
          ${this.formatI18n("/公用/券模板", "元购")}
          `
    }

    return str
  }

  get getMallDiscount() {
    let str = this.i18n('指定活动可叠加') + '：'
    this.data.coupons?.weimobCoupon?.canUseDiscount?.shoppingMallDiscount?.forEach((item: number) => {
      str += this.MallDiscount.find((val) => val.value === item).key + '、'
    })
    str = str.slice(0, -1)
    return str
  }

  getSendCouponLimit(maxPerIssueTimes: number, maxIssueTimes: number) {
    if (maxIssueTimes && maxIssueTimes) {
      let str: any = this.formatI18n(
        "/营销/券礼包活动/新建商品满额发券活动/详情界面/发券数限制",
        "每人限{0}张，活动总限{1}张"
      );
      str = str.replace(/\{0\}/g, maxPerIssueTimes);
      str = str.replace(/\{1\}/g, maxIssueTimes);
      return str;
    } else {
      return "--";
    }
  }

  getendCouponStep(type: string, threshold: number, value: number) {
    if (type === "NONREUSEABLE") {
      let str: any = this.formatI18n(
        "/营销/券礼包活动/新建商品满额发券活动/详情界面/发券商品",
        "不可累加发券，发券商品消费满{0}元及以上发{1}张券"
      );
      str = str.replace(/\{0\}/g, threshold);
      str = str.replace(/\{1\}/g, value);
      return str;
    } else {
      let str: any = this.formatI18n(
        "/营销/券礼包活动/新建商品满额发券活动/详情界面/发券商品",
        "可累加发券，发券商品消费每满{0}元发{1}张券"
      );
      str = str.replace(/\{0\}/g, threshold);
      str = str.replace(/\{1\}/g, value);
      return str;
    }
  }

  getImportCount(count: number) {
    let str: any = this.formatI18n(
      "/营销/券礼包活动/群发券详情界面/导入发券对象导入成功后",
      "当前已导入{0}个会员，每个活动最多支持给20000个会员发券。"
    );
    str = str.replace(/\{0\}/g, " " + count + " ");
    str = str.replace(
      /20000/g,
      `<span style="color: #FF9933">&nbsp;20000&nbsp;</span>`
    );
    return str;
  }

  getCountByPerson(count: number) {
    let str: any = this.formatI18n(
      "/营销/券礼包活动/群发券详情界面/发券数量",
      "{0}张/人"
    );
    str = str.replace(/\{0\}/g, count);
    return str;
  }

  getWx(count: number) {
    if (count > 0) {
      let str: any = this.formatI18n(
        "/营销/券礼包活动/群发券详情界面/投放渠道",
        "微信模版消息  额外送{0}积分"
      );
      str = str.replace(/\{0\}/g, count);
      return str;
    } else {
      return "--";
    }
  }

  getSendCouponObj(importCount: number, issueCount: number) {
    let str: any = this.formatI18n(
      "/营销/券礼包活动/群发券详情界面/进行中或者已结束状态下",
      "共{0}条发券对象，已发{1}张券"
    );
    str = str.replace(
      /\{0\}/g,
      `<span style="font-weight: 700;color: #0000FF">&nbsp;&nbsp;${importCount}&nbsp;&nbsp;</span>`
    );
    if (issueCount <= 0 || !issueCount) {
      str = str.replace(
        /\{1\}/g,
        `<span style="font-weight: 700;color: #0000FF">&nbsp;&nbsp;0&nbsp;&nbsp;</span>`
      );
    } else {
      str = str.replace(
        /\{1\}/g,
        `<span style="font-weight: 700;color: #0000FF">&nbsp;&nbsp;${issueCount}&nbsp;&nbsp;</span>`
      );
    }

    return str;
  }

  getNoStart(importCount: number) {
    let str: any = this.formatI18n(
      "/营销/券礼包活动/群发券详情界面/未开始状态下",
      "当前共有{0}条发券对象"
    );
    str = str.replace(
      /\{0\}/g,
      `<span style="font-weight: 700;color: #0000FF">&nbsp;&nbsp;${importCount}&nbsp;&nbsp;</span>`
    );
    return str;
  }

  private channelId(item: Channel) {
    return `${item.type}${item.id}`
  }

  private getCostParty() {
    let params: RSCostPartyFilter = new RSCostPartyFilter();
    params.page = 0;
    params.pageSize = 0;
    CostPartyApi.query(params)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.parties = resp.data;
        }
      })
      .catch((error: any) => {
        this.$message.error(error.message);
      });
  }

  private pmsPayEngine(){
    CouponTemplateApi.pmsPayEngine()
    .then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.isPmsPayEngine = resp.data;
      }
    })
    .catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message);
      }
    });
  }


  private getChannels() {
    let param: RSChannelManagementFilter = new RSChannelManagementFilter();
    ChannelManagementApi.query(param)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.channels = resp.data;
        }
      })
      .catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
  }
  viewSpecialGoods() {
    ; (this.$refs.SpecialGoodsDialog as any).open()
  }
  openPromotionShow() {
    ; (this.$refs.promotionShow as any).open()
  }
};