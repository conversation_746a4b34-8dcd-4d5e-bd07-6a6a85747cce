import ApiClient from 'http/ApiClient'
import GainPointsGoodsActivity from 'model/points/activity/gainpointsgoods/GainPointsGoodsActivity'
import Response from 'model/common/Response'

export default class GainPointsGoodsActivityApi {
  /**
   * 不积分商品修改或保存
   * 不积分商品修改或保存。
   *
   */
  static saveOrModify(body: GainPointsGoodsActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/points-activity/gain-points-goods/saveOrModify`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 不积分商品详情
   * 不积分商品详情。
   *
   */
  static detail(activityId: string): Promise<Response<GainPointsGoodsActivity>> {
    return ApiClient.server().get(`/v1/points-activity/gain-points-goods/detail/${activityId}`, {}).then((res) => {
      return res.data
    })
  }

}
