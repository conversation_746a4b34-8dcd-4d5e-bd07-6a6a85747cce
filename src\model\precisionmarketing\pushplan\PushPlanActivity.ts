import ActivityBody from 'model/common/ActivityBody'
import PushGroup from 'model/precisionmarketing/pushplan/PushGroup'
import PushTime from 'model/precisionmarketing/pushplan/PushTime'
import IdName from "model/common/IdName";

export default class PushPlanActivity {
  // true表示只保存，false表示保存并审核
  justSave: Nullable<boolean> = null
  // 推送人群规则
  rule: Nullable<PushGroup> = null
  // 活动类型
  body: Nullable<ActivityBody> = null
  // 消息模板
  template: Nullable<IdName> = null
  // 推送时间
  pushTime: Nullable<PushTime> = null
}