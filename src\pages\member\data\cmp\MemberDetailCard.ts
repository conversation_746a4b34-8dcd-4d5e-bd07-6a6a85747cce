import { Component, Prop, Vue } from "vue-property-decorator";
import MemberFormItem from "pages/member/data/cmp/MemberFormItem";
import I18nPage from "common/I18nDecorator";
import { BenefitCardState } from "model/benefitCard/BenefitCardState";
import I18nTool from "common/I18nTool";

@Component({
  name: "MemberDetailCard",
  components: { MemberFormItem },
})
@I18nPage({
  prefix: [
    "/会员/会员资料",
    "/会员/付费会员",
  ],
  auto: true,
})
export default class MemberDetailCard extends Vue {
  @Prop()
  card: any;

  // 是否是付费会员卡，样式不一样
  @Prop({ default: true })
  benefit: boolean;

  get stateLabel() {
    switch (this.card.cardStatus) {
      case BenefitCardState.NORMAL:
        return I18nTool.match("/储值/预付卡/预付卡查询/列表页面/使用中");
      case BenefitCardState.ABORT:
        return I18nTool.match("/储值/预付卡/预付卡查询/列表页面/已作废");
      case BenefitCardState.EXPIRED:
        return I18nTool.match("/会员/权益卡/已过期");
      default:
        return this.card.cardStatus;
    }
  }

  get stateColor() {
    switch (this.card.cardStatus) {
      case BenefitCardState.NORMAL:
        return '#0CC66D';
      case BenefitCardState.ABORT:
        return '#7F36C1';
      case BenefitCardState.EXPIRED:
        return '#36445A';
      default:
        return '';
    }
  }
}
