<!--
 * @Author: haiding <EMAIL>
 * @Date: 2025-02-27 14:14:45
 * @LastEditors: haiding <EMAIL>
 * @LastEditTime: 2025-03-05 15:01:59
 * @FilePath: \new-kequn\src\pages\analysis\cmp\CustomRangeDialog\cmp\CustomRangeForm.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="number-label-dropdown">
    <el-radio-group @change="changeDefaultGroup" v-model="isDefaultGroup" :disabled="!editable">
      <el-radio class="number-label-dropdown-radio" :label="true">{{ i18n("/会员/标签客群/标签/使用默认区间") }}</el-radio>
      <br />
      <el-radio class="number-label-dropdown-radio" :label="false">{{ i18n("/会员/标签客群/标签/使用自定义区间") }}</el-radio>
    </el-radio-group>
    <template v-if="!isDefaultGroup">
      <div v-for="(item, index) in currentSelectGroupSections" :key="index" class="number-label-dropdown-item">
        <span class="number-label-dropdown-item-text">{{ i18n("/会员/标签客群/标签/区间") }}{{ index }}：</span>
        <span class="number-label-dropdown-item-number">
          <span>(</span>
          <span v-if="index === 0" class="number-label-dropdown-item-number-inf">-∞</span>
          <span v-else class="number-label-dropdown-item-number" style="width: 80px">
            <el-input-number :controls="false" :precision="2" :value="currentSelectGroupSections[index - 1].tagValueGroupEnd" disabled
              style="width: 100%; display: inline-block"></el-input-number>
          </span>
        </span>
        <span style="margin: 0 4px">,</span>
        <span class="number-label-dropdown-item-number">
          <span v-if="index === currentSelectGroupSections.length - 1" class="number-label-dropdown-item-number-inf">+∞</span>
          <AutoFixInput @change="doFormChange" v-else-if="index === 0" v-model="currentSelectGroupSections[index].tagValueGroupEnd" :fixed="2"
            :placeholder="i18n('请输入')" :disabled="!editable"/>
          <AutoFixInput @change="doFormChange" v-else-if="currentSelectGroupSections[index - 1].tagValueGroupEnd"
            v-model="currentSelectGroupSections[index].tagValueGroupEnd" :fixed="2" :disabled="!editable"
            :min="parseFloat(currentSelectGroupSections[index - 1].tagValueGroupEnd) + 0.01" :placeholder="i18n('请输入')"></AutoFixInput>
          <AutoFixInput @change="doFormChange" v-else v-model="currentSelectGroupSections[index].tagValueGroupEnd" :fixed="2"
            :placeholder="i18n('请输入')" :disabled="!editable">
          </AutoFixInput>
        </span>
        <span>)</span>
        <el-button type="text" :disabled="!editable">
          <i @click="removeCurrentSelectGroupSectionsByIndex(index)" style="color: red" v-if="showDelete(index)" class="el-icon-delete"></i>
        </el-button>
      </div>
    </template>
    <el-button v-if="!isDefaultGroup && currentSelectGroupSections.length < 15" @click="addGroupSection" type="text" icon="el-icon-plus" :disabled="!editable">
      {{ i18n("/会员/标签客群/标签/增加区间") }}
    </el-button>
  </div>
</template>

<script lang="ts" src="./CustomRangeForm.ts">
</script>

<style lang="scss" scoped>
.number-label-dropdown {
  padding: 10px;

  &-radio {
    margin-bottom: 16px;
  }

  &-item {
    padding-left: 10px;
    color: #5a5f66;
    &-text {
      width: 50px;
    }

    &-number {
      width: 90px;
      display: inline-block;
      font-size: 14px;

      &-inf {
        text-align: center;
        width: 80px;
        display: inline-block;
        // font-weight: bolder;
        font-size: 18px;
      }
    }
  }
}
</style>