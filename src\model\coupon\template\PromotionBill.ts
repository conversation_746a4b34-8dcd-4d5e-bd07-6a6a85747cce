// class ExecState {
//     yet = 'yet'; //未开始
//     sleeping = 'sleeping'; //休眠中
//     executing = 'executing';  //执行中
//     finish = 'finish';
//     hybrid = ‘hybrid’
// }

export default class PromotionBill {
    uuid: Nullable<string> = null;
    billNumber: Nullable<string> = null;
    topicName: Nullable<string> = null;
    activityDesc: Nullable<string> = null;
    start: Nullable<Date> = null
    finish: Nullable<Date> = null
    execState: Nullable<string> = null;
  }