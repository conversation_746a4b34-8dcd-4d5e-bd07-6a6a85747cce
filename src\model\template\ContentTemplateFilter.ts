/*
 * @Author: 黎钰龙
 * @Date: 2025-01-06 20:02:16
 * @LastEditTime: 2025-05-22 15:16:34
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\template\ContentTemplateFilter.ts
 * 记得注释
 */
import PageRequest from 'model/default/PageRequest'
import { ContentTemplateState } from 'model/template/ContentTemplateState'
import { CmsConfigChannel } from './CmsConfig'

// 内容模板查询请求
export default class ContentTemplateFilter extends PageRequest {
  // 名称类似于
  nameLike: Nullable<string> = null
  // 页面状态等于,
  stateEquals: Nullable<ContentTemplateState> = null
  // 是否抓内容，默认不返回content的内容
  fetchContent: Nullable<boolean> = null
  // ID等于
  idEquals: Nullable<string> = null
  // ID在...范围内
  idIn: string[] = []
  // 名称/id类似于
  keyword: Nullable<string> = null
  // 内容资源位名称类似于
  placeNameLike: Nullable<string> = null
  // 模板更新时间起始于
  lastModifiedGreaterThan: Nullable<Date> = null
  // 模板更新时间截止于
  lastModifiedLessThan: Nullable<Date> = null
  // 模板创建时间起始于
  createGreaterThan: Nullable<Date> = null
  // 模板创建时间截止于
  createLessThan: Nullable<Date> = null
  // 
  typeEquals : Nullable<string> = null
  // 投放渠道包含
  channelContainsIn: Nullable<CmsConfigChannel[]> = null
}