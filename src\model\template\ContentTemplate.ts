/*
 * @Author: 黎钰龙
 * @Date: 2024-08-01 16:15:01
 * @LastEditTime: 2025-05-23 11:26:59
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\template\ContentTemplate.ts
 * 记得注释
 */
import { ContentTemplateState } from 'model/template/ContentTemplateState'
import AppShareInfo from './AppShareInfo'
import { CmsConfigChannel } from './CmsConfig'

// 内容模板响应
export default class ContentTemplate {
  // 模板ID
  id: Nullable<string> = null
  // 名称
  name: Nullable<string> = null
  // 图片
  image: Nullable<string> = null
  // 资源位名称
  placeName: Nullable<string> = null
  // 备注
  remark: Nullable<string> = null
  // 内容
  content: any
  // 页面状态
  state: Nullable<ContentTemplateState> = null
  // 版本号
  version: Nullable<number> = null
  // 最后修改人
  lastModifier: Nullable<string> = null
  // 最后修改时间
  lastModified: Nullable<Date> = null
  // 追加参数（B端传什么，C端就返回什么）
  extInfo: Nullable<AppShareInfo> = null
  // 投放渠道
  channels: Nullable<CmsConfigChannel[]> = null
  // 使用导航栏渠道
  usedNavChannels: Nullable<CmsConfigChannel[]> = null
}