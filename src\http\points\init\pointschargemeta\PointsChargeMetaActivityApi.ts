import ApiClient from 'http/ApiClient'
import PointsChargeMetaActivity from 'model/points/init/pointschargemeta/PointsChargeMetaActivity'
import Response from 'model/common/Response'

export default class PointsChargeMetaActivityApi {
  /**
   * 详情
   * 详情。
   *
   */
  static detail(): Promise<Response<PointsChargeMetaActivity>> {
    return ApiClient.server().get(`/v1/activity/points-charge-meta/detail`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 修改或保存
   * 修改或保存。
   *
   */
  static saveOrModify(body: PointsChargeMetaActivity): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/activity/points-charge-meta/saveOrModify`, body, {}).then((res) => {
      return res.data
    })
  }

}
