<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:02
 * @LastEditTime: 2025-04-18 13:39:52
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\date-time-condition-picker\ActivityDateTimeConditionPicker.vue
 * 记得注释
-->
<template>
  <el-form class="activity-date-time-condition-picker" ref="form" :model="form.data">
    <el-form-item prop="timeRange" class="auto-expand-form-item" :rules="dateRangeRules">
      <el-date-picker
          :disabled="disabled"
          type="daterange"
          @change="submit"
          range-separator="-"
          :picker-options="dateRangeOption"
          v-model="form.data.timeRange"
          :start-placeholder="formatI18n('/公用/查询条件/提示/开始日期')"
          :end-placeholder="formatI18n('/公用/查询条件/提示/结束日期')"
      >
      </el-date-picker>
      <span style="color: #888888; font-size: 12px"
            v-if="form.data.dateTimeCondition && form.data.dateTimeCondition.anytime && form.data.timeRange && form.data.timeRange.length > 0 && form.data.timeRange[0] !== null && form.data.timeRange[1] !== null ">&nbsp;&nbsp;
                  <i18n k="/营销/积分活动/积分活动/商品组合满数量加送积分活动/编辑页面/活动信息/注：{0}的{1}到{2}的{3}">
                    <template slot="0">{{form.data.timeRange[0]|yyyyMMdd}}</template>
                    <template slot="1">00:00</template>
                    <template slot="2">{{form.data.timeRange[1]|yyyyMMdd}}</template>
                    <template slot="3">23:59</template>
                  </i18n>
                </span>
    </el-form-item>
    <DateTimeConditionPicker :disabled="disabled" :isHideWeek="isHideWeek" :isHideMonth="isHideMonth" v-model="form.data.dateTimeCondition" @change="submit"
                             ref="dateTimeConditionPicker"></DateTimeConditionPicker>
  </el-form>
</template>

<script lang="ts" src="./ActivityDateTimeConditionPicker.ts">
</script>

<style lang="scss" scoped>
  .activity-date-time-condition-picker {
    ::v-deep .el-date-editor {
      .el-range__icon, .el-range-separator, .el-range__close-icon {
        line-height: 24px !important;
      }
    }
  }
</style>
