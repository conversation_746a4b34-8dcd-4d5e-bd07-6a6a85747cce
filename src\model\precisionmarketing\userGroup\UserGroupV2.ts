/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-09-13 14:38:24
 * @LastEditTime: 2024-09-19 10:29:19
 * @LastEditors: fan<PERSON><PERSON><PERSON><PERSON>
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\precisionmarketing\userGroup\UserGroupV2.ts
 * 记得注释
 */
import IdName from "model/v2/common/IdName"

export default class UserGroupV2 {
  // 客群ID
  uuid: Nullable<string> = null
  // 分类ID
  categoryId: Nullable<string> = null
  // 分类名称
  categoryName: Nullable<string> = null
  // 客群名称
  name: Nullable<string> = null
  // 来源渠道 ['phoenix-CRM','QiWei-企微']
  sourceChannel: Nullable<IdName> = null
  // 描述
  description: Nullable<string> = null
  // 外部客群ID
  outerId: Nullable<string> = null
  // 外部客群版本号
  outerVersion: Nullable<string> = null
  // 覆盖人数
  coveredMemberCount: Nullable<number> = null
  // 覆盖率
  coveredMemberRate: Nullable<number> = null
  // 创建时间
  created: Nullable<Date> = null
  // 创建人标识
  creator: Nullable<string> = null
  // 最后修改时间
  lastModified: Nullable<Date> = null
  // 最后修改人
  lastModifier: Nullable<string> = null
  // 客群版本号
  version: Nullable<string> = null
}