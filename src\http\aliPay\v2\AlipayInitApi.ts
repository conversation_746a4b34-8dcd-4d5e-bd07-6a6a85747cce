import ApiClient from 'http/ApiClient'
import Ali<PERSON><PERSON><PERSON><PERSON> from 'model/alipay/v2/AliPassAuth'
import Ali<PERSON>ass<PERSON>ardTemplate from 'model/alipay/v2/AliPassCardTemplate'
import Response from 'model/common/Response'

export default class AlipayInitApi {
  /**
   * 获取支付宝授权url
   *
   */
  static appToAuth(): Promise<Response<AliPassAuth>> {
    return ApiClient.server().get(`/v1/ali-pay-init/app_to_auth`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 会员卡模版创建
   *
   */
  static createTemplate(body: AliPassCardTemplate): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/ali-pay-init/create_template`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 获取所有门店会员卡的投放连接;待实现
   *
   */
  static downLoadStoreQrCodes(): Promise<Response<void>> {
    return ApiClient.server().get(`/v1/ali-pay-init/download/store_qrcode`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 获取所有门店使用同一个会员卡投放二维码链接
   *
   */
  static getStoreQrCode(): Promise<Response<string>> {
    return ApiClient.server().get(`/v1/ali-pay-init/get_store_qrcode`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 根据商户标识查询会员卡模版
   *
   */
  static getTemplate(): Promise<Response<AliPassCardTemplate>> {
    return ApiClient.server().get(`/v1/ali-pay-init/get_template`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 会员卡模版修改
   *
   */
  static modifyTemplate(body: AliPassCardTemplate): Promise<Response<boolean>> {
    return ApiClient.server().post(`/v1/ali-pay-init/modify_template`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询入会活动id
   * 
   */
  static getMembershipActivityId(): Promise<Response<string>> {
    return ApiClient.server().get(`/v1/ali-pay-init/getMembershipActivityId`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 更新入会活动id
   * 
   */
  static updateMembershipActivityId(membershipActivityId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/ali-pay-init/updateMembershipActivityId`, {}, {
      params: {
        membershipActivityId: membershipActivityId
      }
    }).then((res) => {
      return res.data
    })
  }

}
