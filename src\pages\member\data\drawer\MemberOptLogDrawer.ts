import {Component, Vue} from 'vue-property-decorator'
import I18nPage from "common/I18nDecorator";
import Drawerx from "cmp/drawer/Drawerx";
import MemberApi from 'http/member_standard/MemberApi'
import MemberLog from "model/member_standard/log/MemberLog";

class MemberLogFormData extends MemberLog {
  expanded?: boolean = false
}

@Component({
  name: 'ConditionDrawer',
  components: {
    Drawerx,
  }
})
@I18nPage({
  auto: false,
  prefix: [
    '/会员/会员资料/详情界面/会员操作日志',
    '/公用/按钮',
    '/公用/提示',
  ],
})
export default class MemberOptLogDrawer extends Vue {
  $refs: any
  visible: boolean = false
  memberId: string
  page = {
    currentPage: 1,
    total: 0,
    size: 20
  }
  logs: MemberLogFormData[] = []

  created() {
  }

  show(memberId: string) {
    this.visible = true
    this.memberId = memberId
    this.getList()
  }

  close() {
    this.visible = false
    this.logs = []
  }

  getList() {
    MemberApi.queryLog(this.memberId, this.page.currentPage - 1, this.page.size).then((res) => {
      if (res.data) {
        this.logs = res.data
        this.page.total = res.total
        for (let item of this.logs) {
          item.expanded = false
        }
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.getList()
  }

  onHandleSizeChange(val: number) {
    this.page.size = val
    this.getList()
  }

  expand(row: MemberLogFormData) {
    row.expanded = !row.expanded
    this.$forceUpdate()
  }
}
