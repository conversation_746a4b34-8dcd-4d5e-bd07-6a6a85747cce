export default class CircleFilter {
  // 商圈id等于
  circleIdEquals: Nullable<string> = null
  // 商圈id位于..中
  circleIdIn: string[] = []
  // 商圈id类似于
  circleIdLikes: Nullable<string> = null
  // 商圈名称等于
  circleNameEquals: Nullable<string> = null
  // 商圈名称类似于
  circleNameLikes: Nullable<string> = null
  // id或name近似于
  idNameLikes: Nullable<string> = null
  // 
  page: Nullable<number> = null
  // 
  pageSize: Nullable<number> = null
}