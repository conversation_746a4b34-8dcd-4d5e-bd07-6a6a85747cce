import { Vue, Component, Prop } from 'vue-property-decorator';
import PlaceTemplateMixins from '../PlaceTemplateMixins';
const testImg = require('@/assets/image/ic_danlantupian.png')
import I18nPage from 'common/I18nDecorator';
import BenefitCardTemplateFilter from 'model/equityCard/default/BenefitCardTemplateFilter';
import { enefitCardTemplateStatus } from 'model/equityCard/default/enefitCardTemplateStatus';
import { enefitCardTemplateType } from 'model/equityCard/default/enefitCardTemplateType';
import BenefitCardTemplate from 'model/equityCard/BenefitCardTemplate';
import BenefitCardTemplateApi from 'http/equityCard/BenefitCardTemplateApi';

@Component({
  name: 'MembersSwiperImage',
  components: {},
  mixins: [PlaceTemplateMixins],
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理',
  ],
  auto: true
})
export default class MembersSwiperImage extends Vue {
  @Prop()
  componentItem: any;

  flag: boolean = false

    // 付费会员列表
  benefitCardTemplateList: BenefitCardTemplate[] = [];
  loadingBenefitCardTemplateList: boolean = false;

  mounted() {
    console.log(this.componentItem, 'componentItem');
    this.flag = true
    this.getMemberPayCardList()
  }
  get localProperty() {
    return this.componentItem.props;
  }

  // 展示图片数据
  get imgList() {
    this.flag = false
    this.$nextTick(() => {
      this.flag = true
    })
    if (this.localProperty.propImages.length > 0) {
      return this.localProperty.propImages;
    } else {
      return [
        {
          id: 1,
        },
      ];
    }
  }
  get imgUrl() {
    return testImg
  }

  // 头像图标
  get avatarUrl() {
    return this.localProperty.propAvatarIcon || require('@/assets/image/fellow/morentouxiang.png')
  }

  // 积分图标
  get pointIcon() {
    return this.localProperty.propPointIcon || require('@/assets/image/fellow/ic_jifenshangcheng.png')
  }

  // 储值图标
  get balanceIcon() {
    return this.localProperty.propBalanceIcon || require('@/assets/image/fellow/ic_chuzhichongzhi.png')
  }

  // 优惠券图标
  get couponIcon() {
    return this.localProperty.propCouponIcon || require('@/assets/image/fellow/ic_lingquanzhongxin.png')
  }

  // 预付卡图标
  get prepaidCardIcon() {
    return this.localProperty.propPrepaidCardIcon || require('@/assets/image/fellow/ic_lipinkashangcheng.png')
  }
  
    // 查询付款会员卡列表
    getMemberPayCardList() {
      const params = new BenefitCardTemplateFilter();
      // 查询全部
      params.page = 0;
      params.pageSize = 0;
      // 仅已启用
      params.statusEquals = enefitCardTemplateStatus.start;
      params.typeEquals = enefitCardTemplateType.paid;
      params.marketingCenterEquals = sessionStorage.getItem("marketCenter");
      this.loadingBenefitCardTemplateList= true
      BenefitCardTemplateApi.query(params).then((response) => {
        if (response.data?.length) {
          this.benefitCardTemplateList = response.data;
        } else {
          this.benefitCardTemplateList = [];
        }
      }).finally(()=>{
        this.loadingBenefitCardTemplateList= false
      })
    }

    getEquityCardNameByCode(code: string){
      let result =  '--'
      if(this.localProperty.propshowPaidMember){
          const find = this.benefitCardTemplateList.find((item)=>{
              return item.code === code
          })
          if(find){
              return find.name 
          }
      }

      return result
  }
}
