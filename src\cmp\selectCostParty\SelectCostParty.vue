<!--
 * @Author: L
 * @Date: 2024-10-16 16:45:58
 * @LastEditTime: 2024-10-16 16:45:58
 * @LastEditors: L
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\selectCostParty\SelectCostParty.ts
 * 记得注释
-->
<template>
  <el-select
    v-model="selectCostParty"
    value-key="id"
    :style="{width: width}"
    :loading="selectLoading"
    :disabled="disabled"
    clearable
    filterable
    remote
    :remote-method="doRemoteMethod"
    :placeholder="placeholder ? placeholder : i18n('请选择/输入承担方')"
  >
    <el-option v-if="!hideAll" :label="i18n('全部')" :value="null">
      {{ i18n("全部") }}
    </el-option>
    <template v-if="isOnlyId">
      <el-option :label="item.costParty.name" :value="item.costParty.id" v-for="(item, index) in costParties" :key="index">
        [{{ item.costParty.id }}]{{ item.costParty.name }}
      </el-option>
    </template>
    <template v-else>
      <el-option :label="item.costParty.name" :value="item.costParty" v-for="(item, index) in costParties" :key="index">
        [{{ item.costParty.id }}]{{ item.costParty.name }}
      </el-option>
    </template>
  </el-select>
</template>

<script lang="ts" src="./SelectCostParty.ts"></script>

<style>
</style>