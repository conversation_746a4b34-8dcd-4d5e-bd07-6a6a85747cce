<template>
  <div class="score-adjust">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button
          v-if="hasOptionPermission('/会员/会员管理/会员批量操作单', '单据维护')"
          @click="doStoreValueAdd"
          size="small"
          type="primary"
          >{{ i18n("新建操作单") }}
        </el-button>
        <el-button
          v-if="hasOptionPermission('/会员/会员管理/会员批量操作单', '单据维护')"
          @click="doBatchImport"
          size="small"
          type="primary"
          >{{ formatI18n("/权益/积分/积分调整单/按钮", "批量导入") }}
        </el-button>
      </template>
    </BreadCrume>
    <ListWrapper class="current-page">
      <template slot="query">
        <el-row class="current-query">
          <el-col :span="8">
            <form-item
              :label="formatI18n('/权益/积分/积分调整单/查询条件', '单号')"
            >
              <el-input v-model="query.billNumberLike"></el-input>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item
              :label="formatI18n('/权益/积分/积分调整单/查询条件', '创建时间')"
            >
              <el-date-picker
                v-model="query.createdBetweenClosedClosed"
                :end-placeholder="
                  formatI18n('/权益/积分/积分调整单/查询条件', '结束日期')
                "
                format="yyyy-MM-dd"
                range-separator="-"
                ref="selectDate"
                size="small"
                :start-placeholder="
                  formatI18n('/权益/积分/积分调整单/查询条件', '开始日期')
                "
                type="daterange"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </form-item>
          </el-col>
          <!--</el-row>-->
          <!--<el-row style="margin-top: 10px">-->
          <el-col :span="8">
            <form-item
              :label="
                formatI18n('/权益/积分/积分调整单/查询条件', '最后修改时间')
              "
            >
              <el-date-picker
                v-model="query.lastModifiedBetweenClosedClosed"
                :end-placeholder="
                  formatI18n('/权益/积分/积分调整单/查询条件', '结束日期')
                "
                format="yyyy-MM-dd"
                range-separator="-"
                ref="selectDate"
                size="small"
                :start-placeholder="
                  formatI18n('/权益/积分/积分调整单/查询条件', '开始日期')
                "
                type="daterange"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item
              :label="formatI18n('/公用/菜单/会员')"
            >
              <el-input v-model="query.identityId" :placeholder="formatI18n('/会员/会员资料/请输入手机号/会员号/IC卡内部卡号/IC卡卡面卡号')"></el-input>
            </form-item>
          </el-col>
        </el-row>
        <el-row style="margin-top: 10px">
          <el-col :span="8">
            <form-item>
              <el-button @click="doSearch" type="primary"
                >{{ formatI18n("/公用/按钮", "查询") }}
              </el-button>
              <el-button @click="doReset">{{
                formatI18n("/公用/按钮", "重置")
              }}</el-button>
            </form-item>
          </el-col>
        </el-row>
      </template>
      <template slot="btn"> </template>
      <template slot="list">
        <el-tabs @tab-click="doHandleClick" v-model="activeName">
          <el-tab-pane :label="getAllCount" name="first">
            <el-checkbox
              @change="doSelectAll"
              style="margin-right: 0px; margin-left: 14px"
              v-model="selectAll"
            ></el-checkbox
            >&nbsp; &nbsp;<span>{{ getBillLength(selectedArr.length) }}</span>
            <span>&nbsp;&nbsp;</span>
            <el-button
              v-if="hasOptionPermission('/会员/会员管理/会员批量操作单', '单据审核')"
              @click="doBatchAudit"
            >
              {{ formatI18n("/权益/积分/积分调整单/列表/按钮", "批量审核") }}
            </el-button>
            <el-button
              v-if="hasOptionPermission('/会员/会员管理/会员批量操作单', '单据维护')"
              @click="doBatchDelete"
              style="margin-left: 20px; color: red"
            >
              {{ formatI18n("/权益/积分/积分调整单/列表/按钮", "批量删除") }}
            </el-button>
          </el-tab-pane>
          <el-tab-pane :label="getNoAudit" name="second">
            <el-checkbox
              @change="doSelectAll"
              style="margin-right: 0px; margin-left: 14px"
              v-model="selectAll"
            ></el-checkbox
            >&nbsp;
            <span>{{ getBillLength(selectedArr.length) }}</span>
            <span>&nbsp;&nbsp;</span>
            <el-button
              v-if="hasOptionPermission('/会员/会员管理/会员批量操作单', '单据审核')"
              @click="doBatchAudit"
            >
              {{ formatI18n("/权益/积分/积分调整单/列表/按钮", "批量审核") }}
            </el-button>
            <el-button
              v-if="hasOptionPermission('/会员/会员管理/会员批量操作单', '单据维护')"
              @click="doBatchDelete"
              style="margin-left: 20px; color: red"
            >
              {{ formatI18n("/权益/积分/积分调整单/列表/按钮", "批量删除") }}
            </el-button>
          </el-tab-pane>
          <el-tab-pane :label="getAudit" name="third"> </el-tab-pane>
        </el-tabs>
        <el-table
          :data="tableData"
          @selection-change="handleSelectionChange"
          ref="table"
          style="width: 100%; margin-top: 10px"
        >
          <el-table-column
            type="selection"
            v-if="activeName !== 'third'"
            width="55"
          >
          </el-table-column>
          <el-table-column
            :label="formatI18n('/权益/积分/积分调整单/查询条件', '单号')"
            prop="billNumber"
            width="140"
          >
            <template slot-scope="scope">
              <el-button
                v-if="hasOptionPermission('/会员/会员管理/会员批量操作单', '单据查看')"
                @click="doGoDtl(scope.row)"
                type="text"
                >{{ scope.row.billNumber }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column
            :label="formatI18n('/权益/积分/积分调整单/列表/列名', '状态')"
            prop="state"
            width="150"
          >
            <template slot-scope="scope">
              <div>
                <el-tag type="success" v-if="scope.row.state === 'AUDITED'">
                  {{ formatI18n("/公用/过滤器", "已审核") }}
                </el-tag>
                <el-tag type="warning" v-else>{{
                  formatI18n("/公用/过滤器", "未审核")
                }}</el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            :label="formatI18n('/权益/积分/积分调整单/查询条件', '创建时间')"
            prop="created"
            width="220"
          >
            <template slot-scope="scope">
              <div>{{ scope.row.created | dateFormate3 }}</div>
            </template>
          </el-table-column>
          <el-table-column
            :label="
              formatI18n('/权益/积分/积分调整单/查询条件', '最后修改时间')
            "
            prop="lastModified"
            width="220"
          >
            <template slot-scope="scope">
              <div>{{ scope.row.lastModified | dateFormate3 }}</div>
            </template>
          </el-table-column>
          <el-table-column
            :label="formatI18n('/权益/积分/积分调整单/列表/列名', '摘要')"
            prop="remark"
          >
            <template slot-scope="scope">
              <div
                :title="scope.row.summary"
                style="
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                "
              >
                {{ scope.row.summary | strFormat }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <!--分页栏-->
      <template slot="page">
        <div style="margin-bottom: 10px" v-if="activeName !== 'third'">
          <el-checkbox
            @change="doSelectAll"
            style="margin-right: 0px; margin-left: 14px"
            v-model="selectAll"
          ></el-checkbox
          >&nbsp;
          <span>{{ getBillLength(selectedArr.length) }}</span>
          <span>&nbsp;&nbsp;</span>
          <el-button
            v-if="
              activeName !== 'third' &&
              hasOptionPermission('/会员/会员管理/会员批量操作单', '单据审核')
            "
            @click="doBatchAudit"
            >{{ formatI18n("/权益/积分/积分调整单/列表/按钮", "批量审核") }}
          </el-button>
          <el-button
            v-if="
              activeName !== 'third' &&
              hasOptionPermission('/会员/会员管理/会员批量操作单', '单据维护')
            "
            @click="doBatchDelete"
            style="margin-left: 20px; color: red"
            >{{ formatI18n("/权益/积分/积分调整单/列表/按钮", "批量删除") }}
          </el-button>
        </div>
        <el-pagination
          :current-page="page.currentPage"
          :page-size="page.size"
          :page-sizes="[10, 20, 30, 40]"
          :total="page.total"
          @current-change="onHandleCurrentChange"
          @size-change="onHandleSizeChange"
          background
          layout="total, prev, pager, next, sizes,  jumper"
        >
        </el-pagination>
      </template>
    </ListWrapper>
    <BatchOperateImportDialog
      :orgs="orgs"
      :showOrg="showOrg"
      :dialogShow="dialogShow"
      :importNumber="50000"
      :importUrl="importUrl"
      :templatePath="templatePath"
      @dialogClose="doDialogClose"
      @upload-success="doUploadSuccess"
      :templateName="
        i18n(
          '调整单模板'
        )
      "
      :title="
        formatI18n('/权益/积分/积分调整单/按钮/点击批量导入/弹出框', '导入')
      "
    >
    </BatchOperateImportDialog>
    <DownloadCenterDialog
      :dialogvisiable="dialogvisiable"
      @dialogClose="doDialogClose"
    ></DownloadCenterDialog>
  </div>
</template>

<script lang="ts" src="./BatchOperateList.ts">
</script>

<style lang="scss">
.score-adjust {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: hidden;
  .current-page {
    overflow: auto;
    height: calc(100% - 48px);
    .el-select {
      width: 100%;
    }

    /*.query {*/
    /*.current-query {*/
    /*width: 90%;*/
    /*}*/
    /*}*/
  }

  .el-range-editor.el-input__inner {
    width: 100%;
  }
  .el-table__body .el-table__row td {
    border-bottom: 1px solid #d7dfeb !important;
  }

  .current-query {
    .el-input__inner {
      width: 90%;
    }
  }
}
</style>