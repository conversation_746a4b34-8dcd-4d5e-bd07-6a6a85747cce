import { Component, Prop, Vue } from "vue-property-decorator";
import I18nPage from "common/I18nDecorator";

@Component({
  name: "MemberNavbar",
  components: {},
})
@I18nPage({
  prefix: [
    "/会员/会员资料",
  ],
  auto: true,
})
export default class MemberNavbar extends Vue {
  @Prop()
  currentIndex: number;

  @Prop({ default: () => [] })
  tabs: Array<string>;

  onChange(index: number) {
    if (this.currentIndex == index) return;
    this.$emit("change", index);
  }
}
