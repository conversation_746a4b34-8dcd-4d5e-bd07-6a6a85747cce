/*
 * @Author: 黎钰龙
 * @Date: 2023-12-07 10:14:19
 * @LastEditTime: 2023-12-07 10:16:29
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\miniappsetup\aliappsetup\SelfPickGift.ts
 * 记得注释
 */

// 传给后端的自提卡投放结构
export default class SelfPickGift {
  // 投放方式， SELF_PICK_CARD——自提卡，LOCAL_LIFESTYLE_GOODS——本地生活商品
  pickType: Nullable<string> = null
  // 图片链接
  picture: string = ''
  // 投放开始时间
  startTime: Date = new Date()
  // 投放结束时间
  endTime: Date = new Date()
  // 自提卡Id/商家侧编码
  code: string = ''
  // 排序
  sort: Nullable<number> = null
}