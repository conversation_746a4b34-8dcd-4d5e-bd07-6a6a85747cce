/*
 * @Autor: 司浩
 * @Description:
 * @Date: 2023-03-09 16:10:51
 * @LastEditors: 司浩
 * @LastEditTime: 2023-03-09 16:10:57
 * @FilePath: \phoenix-web-ui\src\model\analysis\v2\OrgCycleReportFilter.ts
 */
import { DateRangeType } from 'model/analysis/v2/DateRangeType'

// 交易组织周期报表查询条件
export default class OrgCycleReportFilter {
  // 门店代码等于
  storeIdEquals: Nullable<string> = null
  // 营销中心等于
  marketingCenterEquals: Nullable<string> = null
  // 日期范围：TODAY-今天；LST_SEVEN_DAY-仅七天；LST_THIRTY_DAY-近三十天；PREVIOUS_WEEK-上周；PREVIOUS_MONTH-上月；PREVIOUS_QUARTER-上季度；PREVIOUS_HALF_YEAR-上半年；PREVIOUS_YEAR-上年；
  dateRangeType: Nullable<DateRangeType> = null
  // 页码
  page: Nullable<number> = null
  // 页面大小
  pageSize: Nullable<number> = null
}
