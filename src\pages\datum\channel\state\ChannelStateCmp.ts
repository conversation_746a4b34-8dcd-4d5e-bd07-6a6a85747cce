import {Component, Prop, Vue} from 'vue-property-decorator'
import I18nPage from "common/I18nDecorator";


@Component({
    name: 'ChannelState',
})
@I18nPage({
    auto: false,
    prefix: [
        '/资料/渠道',
    ],
})
export default class ChannelStateCmp extends Vue {
    @Prop()
    state: string
    i18n: I18nFunc

    parseState(state: string) {
        switch (state) {
            case 'ENABLED':
                return this.i18n('启用');
            case 'DISABLED':
                return this.i18n('禁用');
        }
    }

    parseStateColor(state: string) {
        switch (state) {
            case 'ENABLED':
                return 'green';
            case 'DISABLED':
                return 'red';
        }
    }
}
