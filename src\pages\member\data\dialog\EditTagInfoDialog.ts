import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import FormItem from "cmp/formitem/FormItem.vue";
import TagOption from "model/tag/TagOption";
import MemberApi from "http/member_standard/MemberApi";
import MemberTags from "model/member_v2/member/MemberTags";
import TagV2Api from "http/tag/TagV2Api";

@Component({
  name: "EditTagInfoDialog",
  components: {
    FormItem,
  },
})
export default class EditTagInfoDialog extends Vue {
  tags: any = [];
  selectedTags: any = [];
  selectedRadio: any[] = [];
  selectInput: any[] = [];
  tagSelect: any = [];
  value: any = []
  @Prop()
  memberId: string;
  @Prop()
  data: any;
  @Prop()
  title: any;

  @Prop({
    type: Boolean,
    default: false,
  })
  dialogShow: boolean;

  // @Watch("dialogShow")
  // onDataChange(value: any) {
  //   console.log('data');
    
  //   this.initValue(this.data)
  // }



  initValue(value : any) {
    this.value = value
    if (value && value.length > 0) {
      this.selectedTags = [];
      this.selectedRadio = [];
      this.tags.forEach((item: any, index: number) => {
        value.forEach((item1: any) => {
          if (item.tagId === item1.tagId) {
            item1.tagType = item.tagType;
            if (item.tagType === "singleChoice") {
              item1.index = index;
            }
            if (item.tagType !== "singleChoice" && item.tagType !== "checkbox") {
              this.$set(this.tags[index], 'tagValues', item1.tagValues)
            }
          }
        });
      });
      console.log(value);

      value.forEach((item: any) => {
        let obj: any = {};
        if (item && item.tagValues && item.tagValues.length > 0) {
          item.tagValues.forEach((subItem: any) => {
            obj = {
              tagName: item.tagId,
              tagValue: "",
            };
            obj.tagValue = subItem;
            if (item.tagType === "checkbox") {
              this.selectedTags.push(obj);
            } else if (item.tagType === "singleChoice") {
              obj.index = item.index
              this.selectedRadio.push(obj)
            }
          });
          if (item.tagType !== "singleChoice" && item.tagType !== "checkbox") {
            this.selectInput.push(item)
          }
        }
      });
      // 首先全部清空勾选
      if (this.selectedTags && this.selectedTags.length > 0) {
        this.selectedTags.forEach((item: any, index: number) => {
          if (this.tags && this.tags.length > 0) {
            this.tags.forEach((subItem: any, subIndex: number) => {
              if (
                subItem &&
                subItem.tagValues &&
                subItem.tagValues.length > 0 &&
                this.tagSelect[subIndex]!== ''
              ) {
                subItem.tagValues.forEach(
                  (thirdItem: any, thirdIndex: number) => {
                    this.tagSelect[subIndex][thirdIndex] = false;
                  }
                );
              }
            });
          }
        });
      }
      // 处理默认勾选问题
      if (this.selectedTags && this.selectedTags.length > 0) {
        this.selectedTags.forEach((item: any, index: number) => {
          if (this.tags && this.tags.length > 0) {
            this.tags.forEach((subItem: any, subIndex: number) => {
              if (item.tagName === subItem.tagId) {
                if (
                  subItem &&
                  subItem.tagValues &&
                  subItem.tagValues.length > 0
                ) {
                  subItem.tagValues.forEach(
                    (thirdItem: any, thirdIndex: number) => {
                      if (item.tagValue === thirdItem) {
                        this.tagSelect[subIndex][thirdIndex] = true;
                      }
                    }
                  );
                }
              }
            });
          }
        });
      }
      console.log(this.selectedRadio);
      
      if (this.selectedRadio && this.selectedRadio.length > 0) {
        this.selectedRadio.forEach((item: any, index: number) => {
          this.tags.forEach((subItem: any, subIndex: number) => {
            if (item.tagName === subItem.tagId) {
              this.tagSelect[subIndex] = item.tagValue
            }
          })
        })
      }
    }
  }

  created() {
    TagV2Api.list()
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.tags = resp.data;
          if (this.tags && this.tags.length > 0) {
            this.tags.forEach((item: any, index: number) => {
              if (item.tagType === 'date' || item.tagType === 'text' || item.tagType === 'number') {
                item.tagValues = [null] //老数据兼容，清掉这几种类型的类型值
              }
              if (item.tagType !== "singleChoice") {
                this.tagSelect[index] = [];
              } else {
                this.tagSelect[index] = "";
              }

              if (
                item &&
                item.tagValues &&
                item.tagValues.length > 0 &&
                item.tagType !== "singleChoice"
              ) {
                item.tagValues.forEach((subItem: any, subIndex: number) => {
                  this.tagSelect[index][subIndex] = false;
                });
              }
            });
          }
          this.initValue(this.data)
        }
      })
      .catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
  }
  doBeforeClose(done: any) {
    this.$emit("dialogClose");
    done();
  }
  doModalClose() {
    let arr: any = [];
    this.selectedTags.forEach((item: any, index: number) => {
      if (arr && arr.length > 0) {
        let count = 0;
        arr.forEach((arrItem: any) => {
          if (arrItem.tagId === item.tagName) {
            arrItem.tagValues.push(item.tagValue);
          } else {
            count++;
          }
        });
        if (count === arr.length) {
          // 不存在
          let obj: TagOption = new TagOption();
          obj.tagId = item.tagName;
          obj.tagValues = [];
          obj.tagValues.push(item.tagValue);
          obj.tagType = item.tagType;
          arr.push(obj);
        }
      } else {
        let obj: TagOption = new TagOption();
        obj.tagId = item.tagName;
        obj.tagValues = [];
        obj.tagType = item.tagType;
        obj.tagValues.push(item.tagValue);
        arr.push(obj);
      }
    });
    this.selectedRadio.forEach((item: any) => {
      console.log(item);

      let obj: TagOption = new TagOption();
      obj.tagId = item.tagName;
      obj.tagType = item.tagType;
      obj.tagValues = [];
      obj.tagValues.push(item.tagValue);
      arr.push(obj);
    });
    this.selectInput.forEach((item: any) => {
      console.log(item);
      let obj: TagOption = new TagOption();
      obj.tagId = item.tagId;
      obj.tagType = item.tagType;
      obj.tagValues = item.tagValues;
      arr.push(obj);
    });
    let params: MemberTags = new MemberTags();
    params.memberId = this.$route.query.id as string;
    params.tags = arr;
    MemberApi.saveMemberTag(params)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success("标签保存成功");
          this.$emit("dialogClose");
        }
      })
      .catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
      this.$forceUpdate()
  }
  doCancel() {
    this.$emit("dialogClose");
  }
  doCheckGoods() {
    // todo
  }
  doTagSelected(index: number, subIndex: number) {
    if (this.tagSelect[index][subIndex]) {
      let obj = {
        tagName: this.tags[index].tagId,
        tagValue: this.tags[index].tagValues[subIndex],
        tagType: this.tags[index].tagType,
      };
      this.selectedTags.push(obj);
    } else {
      if (this.selectedTags && this.selectedTags.length > 0) {
        this.selectedTags.forEach((item: any, pos: number) => {
          if (
            item.tagName === this.tags[index].tagId &&
            item.tagValue === this.tags[index].tagValues[subIndex]
          ) {
            this.selectedTags.splice(pos, 1);
          }
        });
      }
    }
  }

  doRadioSelected(index: number, value: any) {
    console.log(index, value);
    if (this.tagSelect[index]) {
      let obj = {
        tagName: this.tags[index].tagId,
        tagValue: value,
        index: index,
        tagType: this.tags[index].tagType,
      };
      console.log(obj);

      if (this.selectedRadio.length === 0) {
        this.selectedRadio.push(obj);
      } else {
        let sameFlag = false;
        this.selectedRadio.forEach((item: any, indexRadio: number) => {
          if (item.index === index) {
            sameFlag = true;
            this.$set(this.selectedRadio, indexRadio, obj);
          }
        });
        if (sameFlag === false) {
          this.selectedRadio.push(obj);
        }
      }
    }
  }

  doTagClose(index: number, obj: any) {
    this.selectedTags.splice(index, 1);
    // 处理联动问题
    if (this.tags && this.tags.length > 0) {
      this.tags.forEach((item: any, index: number) => {
        if (item.tagId === obj.tagName) {
          if (item.tagValues && item.tagValues.length > 0) {
            item.tagValues.forEach((subItem: any, subIndex: number) => {
              if (subItem === obj.tagValue) {
                // this.tagSelect[index][subIndex] = false
                this.$set(this.tagSelect[index], subIndex, false);
              }
            });
          }
        }
      });
    }
  }

  doRadioTagClose(index: number, obj: any) {
    this.selectedRadio.splice(index, 1);
    // 处理联动问题
    if (this.tags && this.tags.length > 0) {
      this.tags.forEach((item: any, index: number) => {
        if (item.tagId === obj.tagName && item.tagType === "singleChoice") {
          item.tagValues.forEach((item: any) => {
            if (item === obj.tagValue) {
              this.$set(this.tagSelect, index, "");
            }
          });
        }
      });
    }
  }

  doInputTagClose(index: number, item: any) {
    this.selectInput.splice(index, 1)
    console.log(item);
    this.tags.forEach((item1: any, index1: number) => {
      if (item.tagId === item1.tagId) {
        this.$set(this.tags[index1], 'tagValues', [null])
      }
    })
  }

  doInputSelect(index: number, item: any, inputValue: any) {
    if (inputValue !== null && inputValue !== '' && inputValue !== undefined) {
      this.$set(this.tagSelect, index, [true])
      let flag = false
      this.selectInput.forEach((input: any, inputIndex: number)=>{
        if (input.tagId === item.tagId) {
          flag = true
          this.selectInput[inputIndex].tagValues = item.tagValues
        }
      })
      if (flag === false) {
        this.selectInput.push(item)
      }
    } else {
      this.$set(this.tagSelect, index, [false])
      this.selectInput.forEach((input: any, inputIndex: number)=>{
        if (input.tagId === item.tagId) {
          this.selectInput.splice(inputIndex, 1)
        }
      })
    }
  }
}
