/*
 * @Author: 黎钰龙
 * @Date: 2025-04-02 17:58:21
 * @LastEditTime: 2025-04-03 17:20:05
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\benefit\improveProfiles\select-member-info\SelectMemberInfoDialog.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import SystemConfigApi from 'http/systemConfig/SystemConfigApi';
import IdName from 'model/common/IdName';
import PersonalDataConfigLine from 'model/member/PersonalDataConfigLine';
import { MemberInfoFieldName, MemberInfoFieldNameMap } from 'model/systemConfig/MemberInfoFieldName';
import MemberPersonalDataConfigLine from 'model/systemConfig/MemberPersonalDataConfigLine';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  name: 'SelectMemberInfoDialog',
  components: {}
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/设置/系统设置'
  ],
  auto: true
})
export default class SelectMemberInfoDialog extends Vue {

  // 设置手机号禁止取消选择
  @Prop({
		default: false,
    type: Boolean
	}) disabledPhone: boolean;

  visible: boolean = false
  checkList: string[] = []
  memberInfoList: MemberPersonalDataConfigLine[] = []

  // 查询已启用的会员资料字段
  queryMemberInfo() {
    SystemConfigApi.getEnable().then((res) => {
      if (res.code === 2000) {
        this.memberInfoList = res.data?.configLines || []
      } else {
        throw new Error(res.msg!)
      }
    }).catch((err) => {
      this.$message.error(err.message)
    })
  }

  open(ids?: MemberInfoFieldName[]) {
    this.queryMemberInfo()
    this.visible = true
    if (ids?.length) {
      this.checkList = ids || []
    }
  }

  doSubmit() {
    this.$emit('submit', this.checkList)
    this.visible = false
    this.$nextTick(() => {
      this.checkList = []
    })
  }

  doCancel() {
    this.checkList = []
    this.visible = false
  }

  transIdName(id: MemberInfoFieldName) {
    return MemberInfoFieldNameMap[id]
  }

  doClear() {
    this.checkList = this.disabledPhone ? ['PHONE'] : []
  }

  doDelete(id: MemberInfoFieldName) {
    const index = this.checkList.findIndex(item => item === id)
    this.checkList.splice(index, 1)
  }

  doEditInfo() {
    const route = this.$router.resolve({ name: 'set', query: { activeName: 'member-info-config' } })
    window.open(route.href, '_blank')
  }
};