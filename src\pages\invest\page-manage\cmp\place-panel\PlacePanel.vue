<template>
  <div>
    <ul class="template-list" v-for="(item, subIndex) in filterTemplateList" :key="subIndex">
      <li @click="clickSub($event, subIndex)" class="template-list-sub">
        <i :class="isOpen(subIndex) ? 'el-icon-caret-bottom' : 'el-icon-caret-right'"></i>
        <span class="subtile">{{ item.name }}</span>
      </li>
      <collapse-transition>
        <ul v-show="isOpen(subIndex)" class="template-list-content">
          <draggable class="draggable-panel" :list="item.widgets"
            :group="{ name: 'componentsGroup', pull: 'clone', put: false }" :sort="false" :clone="cloneItem"
            @end="onEnd" :move="panelMove">
            <li class="template-list-content-item" v-for="(widget, index) in item.widgets" :key="index"
              @click="itemClick(widget)" @mouseenter="itemMouseenter(widget.id)" @mouseleave="itemMouseleave">
              <img :src="isHover && hoverActiveId === widget.id ? widget.hoverIconUrl : widget.iconUrl" />
              <span class="ellipsis" :title="widget.name">{{ widget.name }}</span>
            </li>
          </draggable>
        </ul>
      </collapse-transition>
    </ul>
  </div>
</template>
<script lang="ts" src="./PlacePanel.ts"></script>
<style lang="scss" scoped>
.no-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 100px;
  color: #a0abbc;

  .picNocontent {
    height: 80px;
    width: 114px;
  }

  .text {
    line-height: 17px;
    width: 206px;
    margin-top: 50px;
    text-align: center;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #79879e;
  }
}

.template-list {
  border-bottom: 1px solid #d7dfeb;

  &-sub {
    color: #242633;
    cursor: pointer;
    line-height: 16px;
    position: relative;
    padding: 12px;
    display: flex;

    .subtile {
      padding-left: 6px;
      font-size: 12px;
    }
  }

  &-content {
    overflow: auto;
    padding: 0 16px;

    &-item {
      cursor: move;
      float: left;
      width: 126px;
      height: 110px;
      background: #f9fafc;
      border-radius: 8px;
      border: 1px solid #eeeff1;
      text-align: center;
      color: #1f375d;
      position: relative;
      padding-top: 5px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-right: 10px;
      margin-bottom: 12px;

      img {
        width: 110px;
        height: auto;
        margin-bottom: 10px;
      }

      span {
        font-weight: 400;
        font-size: 14px;
        color: #5a5f66;
        line-height: 20px;
        text-align: center;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .ellipsis {
        display: inline-block;
        max-width: 100%;
        text-overflow: ellipsis;
        overflow: hidden;
        vertical-align: middle;
      }

      &:nth-child(2n) {
        margin-right: 0;
        /* 每隔两个元素不设置右边距 */
      }
    }
  }
}
</style>
