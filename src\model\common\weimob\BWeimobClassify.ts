// 微盟商品分组信息
export default class BWeimobClassify {
    // 分组层级，取值范围为 [1,2]。
    classifyLevel: Nullable<string> = null
    // 分组 ID，全局唯一。
    classifyId: Nullable<string> = null
    // 分组名称
    name: Nullable<string> = null
    // 是否叶子结点，true-叶子节点，false-非叶子节点。该分组下无子分组则为叶子节点。二级分组是叶子节点。
    isLeaf: Nullable<boolean> = null
    // 上级分组 ID，一级分组的上级分组 ID 为空。
    parentId: Nullable<string> = null
    // 分组图片，一级分组无图片，二级分组图片必填。
    imageUrl: Nullable<string> = null
    // 是否在 C 端显示，默认显示。true-显示，false-隐藏。
    isShow: Nullable<boolean> = null
    // 是否热门展示。true-热门展示，false-不展示。开启后该分组将在店铺分类页置顶展示。
    isHot: Nullable<boolean> = null
    // 商品分组的排序值，新增分组时传入，默认为 0。该字段数值越大，展示靠前。
    sort: Nullable<number> = null
  }
  