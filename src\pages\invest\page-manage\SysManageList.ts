import Bread<PERSON>rume from 'cmp/bread-crumb/BreadCrume';
import I18nPage from 'common/I18nDecorator';
import { Component, Vue } from 'vue-property-decorator';
import ContentTemplateApi from 'http/template/ContentTemplateApi'
import NavigationSetting from 'model/navigation/NavigationSetting'
import ContentTemplateFilter from 'model/template/ContentTemplateFilter'
import ContentTemplate from 'model/template/ContentTemplate'
import ListWrapper from 'cmp/list/ListWrapper.vue'
import FormItem from 'cmp/formitem/FormItem.vue'
import { EditMode } from 'model/local/EditMode';
import { ContentTemplateState } from 'model/template/ContentTemplateState'
import UpdateRequest from 'model/template/UpdateRequest'
import ConstantMgr from 'mgr/ConstantMgr'
import CreateRequest from 'model/template/CreateRequest'
import CommonUtil from 'util/CommonUtil'


import { BigWheelImageVO } from './BigWheelImageVO'
import bigWheelImageLink from './BigWheelImageUrlLink'
import InvestPageList from './mixin/InvestPageList';
import ShareConfigDialog from './cmp/share-config-dialog/ShareConfigDialog';
import MemberCodeDecorationModal from './template/MemberCodeDecorationModal';
import PointCodeDecorationModal from './template/PointCodeDecorationModal';
import BalanceCodeDecorationModal from './template/BalanceCodeDecorationModal';
import MemberCompleteProfile from './template/MemberCompleteProfile';
import SignAndLoginModal from './template/SignAndLoginModal';
const CardBackgroundImage = bigWheelImageLink[BigWheelImageVO.img_card_default_3] || require('@/assets/image/invest/img_card_default.png')
const backgroundImage = bigWheelImageLink[BigWheelImageVO.bigwheel_bg] || require('@/assets/image/fellow/img_bg.png')
const baseBackgroundImage = bigWheelImageLink[BigWheelImageVO.img_zhuanpan] || require('@/assets/image/fellow/img_zhuanpandizuo.png')
const turntablePointerImage = bigWheelImageLink[BigWheelImageVO.img_chou] || require('@/assets/image/fellow/img_chou.png')
const turntableWinArea = bigWheelImageLink[BigWheelImageVO.img_zhuanpanjiangpin_normal1] || require('@/assets/image/fellow/img_zhuanpanjiangpin_normal1.png')
const turntableWinAreas = bigWheelImageLink[BigWheelImageVO.img_zhuanpanjiangpin_normal2] || require('@/assets/image/fellow/img_zhuanpanjiangpin_normal2.png')
const turntableNoWinArea = bigWheelImageLink[BigWheelImageVO.img_zhuanpanjiangpin_normal3] || require('@/assets/image/fellow/img_zhuanpanjiangpin_normal3.png')
const clickHandImage = bigWheelImageLink[BigWheelImageVO.img_finger] || require('@/assets/image/fellow/img_finger.png')
const myPrize = bigWheelImageLink[BigWheelImageVO.ic_dazhuanpan_gift] || require('@/assets/image/fellow/ic_dazhuanpan_gift.png')
const obtainOpportunity = bigWheelImageLink[BigWheelImageVO.cutting] || require('@/assets/image/fellow/cutting.png')
const buttonBg = bigWheelImageLink[BigWheelImageVO.btn_start] || require('@/assets/image/fellow/btn_start.png')
const promptBg = bigWheelImageLink[BigWheelImageVO.bg_tips] || require('@/assets/image/fellow/bg_tips.png')
@Component({
  name: 'SysManageList',
  components: {
    BreadCrume,
    ListWrapper,
    FormItem,
    ShareConfigDialog
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理',
    '/会员/付费会员'
  ],
  auto: true
})
export default class SysManageList extends InvestPageList {
  ContentTemplate: ContentTemplate[] = []// 活动模型
  ContentTemplateFilter: ContentTemplateFilter = new ContentTemplateFilter()
  ContentTemplateState = ContentTemplateState
  updateParams: UpdateRequest = new UpdateRequest()
  // 保存参数
  saveParams: CreateRequest = new CreateRequest()
  editId: any = ''// 编辑id
  widgets: any = []
  // 大转盘
  imgData: any = {
    id: "bigWheel",
    type: 'default',
    uuid: '',
    propBackgroundImage: backgroundImage,
    propBaseBackgroundImage: baseBackgroundImage,
    propTurntablePointerImage: turntablePointerImage,
    propTurntableWinArea: {
      backgroundImages: [turntableWinArea],
      image: '',
      fontColor: '#FFFFFF',
      backgroundColor: '',
    },
    propTurntableNoWinArea: {
      backgroundImages: [turntableWinAreas, turntableNoWinArea],
      image: '',
      fontColor: '#F64602',
      backgroundColor: '',
    },
    propRuleButton: {
      backgroundImages: [],
      image: '',
      fontColor: '#784313',
      backgroundColor: '#FFFFFF',
    },
    propClickHandImage: clickHandImage,
    propLotteryButton: {
      backgroundImages: [buttonBg],
      image: '',
      fontColor: '#FFFFFF',
      backgroundColor: '',
    },
    propTimesPrompt: {
      backgroundImages: [promptBg],
      image: '',
      fontColor: '#242633',
      backgroundColor: '',
    },
    propMyPrize: {
      backgroundImages: [],
      image: myPrize,
      fontColor: '#784313',
      backgroundColor: '#FFF0C4',
    },
    propObtainOpportunity: {
      backgroundImages: [],
      image: obtainOpportunity,
      fontColor: '#784313',
      backgroundColor: '#FFF0C4',
    },
  }
  // 会员中心
  memberCenterImgData: any = {
    id: "memberCenter",
    uuid: "",
    propTitle: "会员中心",
    propCardElement: {
      // 卡面元素显隐
      showCardNumber: false, // 卡号
      showPeriod: true, // 有效期
      showGrowth: true, // 成长值
    },
    propBackgroundType: "unified", // 'unified': 统一背景; 'byGrade': 按等级
    propUnifiedCardBackground: {
      // 统一背景
      gradeName: "",
      backgroundImage: CardBackgroundImage,
      fontColor: "#FFF4DC",
    },
    propByGradeCardBackgrounds: [
      {
        // 按等级设置背景
        gradeName: "",
        backgroundImage: CardBackgroundImage,
        fontColor: "#FFF4DC",
      },
    ],
    propMemberCode: {
      showBarcode: true, // 条形码
      showQrcode: true, // 二维码
    },
    propMemberBenefits: {
      imageType: "default", //会员权益: custom: 自定义
      gradeImages: [
        {
          id: "",
          imageUrl: "",
        },
      ], // 自定义图片列表
    },
    propVisibleLevel: "all", // 'all' 'only'
    propMarketingPosition: {
      enabled: false,
      items: [
        {
          name: "",
          prompt: "",
          jumpPageInfo: null,
        },
      ],
    }, // 推荐位
  };
  paidMemberImgData: any = {
    id: "paidMemberCardCenter",
    type: "member",
    uuid: null,
    name: "付费会员中心",
    paidMemberCards: [
      {
        cardElement: {
          showCardNumber: false,
          showPeriod: false
        },
        paidMemberCard: "",
        memberBenefits: {
          imageType: "",
          notActivatedImage: "",
          activatedImage: ""
        },
        marketingPosition: {
          enabled: false,
          items: [
            {
              name: "",
              prompt: "",
              jumpPageInfo: null
            }
          ]
        }
      }
    ]
  }
  memberCodeData: MemberCodeDecorationModal = new MemberCodeDecorationModal() //会员码默认装修内容
  pointCodeData: PointCodeDecorationModal = new PointCodeDecorationModal() //积分码默认装修内容
  balanceCodeData: BalanceCodeDecorationModal = new BalanceCodeDecorationModal() //余额码默认装修内容
  memberCompleteProfileData: MemberCompleteProfile= new MemberCompleteProfile() //完善资料有礼默认装修内容
  SignAndLoginData: SignAndLoginModal= new SignAndLoginModal() //H5登录注册装修内容
  page = {
    currentPage: 1,
    total: 0,
    size: 20
  }
  get panelArray() {
    return [
      {
        name: this.i18n('/公用/菜单/页面管理'),
        url: ''
      }
    ]
  }

  mounted() {
    this.getList()
  }
  /**
    * 获取列表数据
    */
  getList() {
    this.ContentTemplateFilter.typeEquals = 'system'
    this.ContentTemplateFilter.page = this.page.currentPage - 1
    this.ContentTemplateFilter.pageSize = this.page.size
    return ContentTemplateApi.query(this.ContentTemplateFilter).then(async res => {
      if (res.code === 2000) {
        this.ContentTemplate = res.data || []
        this.page.total = res.total
        const bigWheelObj = res.data?.find(item => item.placeName === '大转盘')
        let createFlag = false
        if (!bigWheelObj) {
          // 没数据调新建接口
          this.saveParams.name = this.i18n('大转盘');
          this.saveParams.placeName = '大转盘';
          await this.doDreserve('bigWheel', this.imgData)
          createFlag = true
        }
        const memberCenterObj = res.data?.find(item => item.placeName === 'memberCenter') // 会员中心
        if (!memberCenterObj) {
          // 没数据调新建接口
          this.saveParams.name = this.i18n('会员中心');
          this.saveParams.placeName = 'memberCenter';
          await this.doDreserve('memberCenter', this.memberCenterImgData)
          createFlag = true
        }
        const paidMemberObj = res.data?.find(item => item.placeName === 'paidMemberCardCenter') // 付费会员中心
        if (!paidMemberObj) {
          // 没数据调新建接口
          this.saveParams.name = this.i18n('付费会员中心');
          this.saveParams.placeName = 'paidMemberCardCenter';
          this.paidMemberImgData.id = 'paidMemberCardCenter'
          this.paidMemberImgData.name = '付费会员中心'
          await this.doDreserve('paidMemberCardCenter', this.paidMemberImgData)
          createFlag = true
        }
        const equityCardObj = res.data?.find(item => item.placeName === 'equityCardCenter') // 权益卡中心
        if (!equityCardObj) {
          // 没数据调新建接口
          this.saveParams.name = this.i18n('权益卡中心');
          this.saveParams.placeName = this.i18n('equityCardCenter');
          this.paidMemberImgData.id = 'equityCardCenter'
          this.paidMemberImgData.name = '权益卡中心'
          await this.doDreserve('equityCardCenter', this.paidMemberImgData)
          createFlag = true
        }
        const memberCodeObj = res.data?.find(item => item.placeName === 'memberPayCode') // 会员码
        if (!memberCodeObj) {
          // 没数据调新建接口
          this.saveParams.name = this.i18n('会员码');
          this.saveParams.placeName = 'memberPayCode';
          await this.doDreserve('memberPayCode', this.memberCodeData)
          createFlag = true
        }
        const pointCodeObj = res.data?.find(item => item.placeName === 'scorePayCode') // 积分付款码
        if (!pointCodeObj) {
          // 没数据调新建接口
          this.saveParams.name = this.i18n('积分支付');
          this.saveParams.placeName = 'scorePayCode';
          await this.doDreserve('scorePayCode', this.pointCodeData)
          createFlag = true
        }
        const balanceCodeObj = res.data?.find(item => item.placeName === 'depositPayCode') // 储值支付
        if (!balanceCodeObj) {
          // 没数据调新建接口
          this.saveParams.name = this.i18n('储值支付');
          this.saveParams.placeName = 'depositPayCode';
          await this.doDreserve('depositPayCode', this.balanceCodeData)
          createFlag = true
        }
        const memberCompleteProfileObj = res.data?.find(item => item.placeName === 'memberCompleteProfile') // 完善资料有礼
        if( !memberCompleteProfileObj) {
          // 没数据调新建接口
          this.saveParams.name = this.i18n('个人资料');
          this.saveParams.placeName = 'memberCompleteProfile'; 
          await this.doDreserve('memberCompleteProfile', this.memberCompleteProfileData)
          createFlag = true
        }
        const loginCodeObj = res.data?.find(item => item.placeName === 'signAndLogin') // 会员码
        if (!loginCodeObj) {
          // 没数据调新建接口
          this.saveParams.name = this.i18n('H5登录注册');
          this.saveParams.placeName = 'signAndLogin';
          await this.doDreserve('signAndLogin', this.SignAndLoginData)
          createFlag = true
        }
        if (createFlag) {
          await this.getList()
        }
      } else {
        this.$message.error(res.msg!)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  /**
   * 查询
   */
  doSearch() {
    this.page.currentPage = 1
    this.getList()
  }

  /**
   * 重置
   */
  doReset() {
    this.ContentTemplateFilter = new ContentTemplateFilter()
    this.getList()
    // for (let item of this.queryData) {
    //   this.$refs.dataTable.toggleRowExpansion(item, false)
    // }
  }
  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.getList()
  }
  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.getList()
  }
  /**
   * 编辑
   */
  doEdit(row: any) {
    const toPage = {
      name: '',
      id: row.id
    }
    if (row.placeName === '大转盘') {
      toPage.name = 'page-turntable-edit'
    } else if (row.placeName === 'memberCenter') {
      toPage.name = 'page-member-center-edit'
    } else if (row.placeName === 'paidMemberCardCenter') {
      toPage.name = 'page-paid-member-edit'
    } else if (row.placeName === 'equityCardCenter') {
      toPage.name = 'page-benefit-card-edit'
    } else if (row.placeName === 'memberPayCode') {
      toPage.name = 'page-member-code-edit'
    } else if (row.placeName === 'scorePayCode') {
      toPage.name = 'page-point-code-edit'
    } else if (row.placeName === 'depositPayCode') {
      toPage.name = 'page-balance-code-edit'
    }else if(row.placeName === 'memberCompleteProfile') {
      toPage.name = 'page-member-complete-profile-edit'
    }else if(row.placeName === 'signAndLogin') {
      toPage.name = 'page-member-login-edit'
    }

    this.$router.push({
      name: toPage.name,
      query: {
        id: toPage.id
      }
    });
  }
  // 新建保存模型
  async doDreserve(id: string, imageData: any) {
    try {
      let content: any = {};
      content.id = id;
      this.widgets = [imageData];
      content.widgets = this.widgets;
      this.widgets[0].type = 'default';
      this.saveParams.type = 'system';
      this.saveParams.content = content;
      this.saveParams.image = '';
      let res = await ContentTemplateApi.create(this.saveParams);
      if (res.code === 2000) {
        this.$message.success(this.i18n('保存成功'))
      } else {
        this.$message.error(res.msg || this.i18n('保存失败'))
      }
    } catch (error) {
      this.$message.error((error as Error).message || this.i18n('保存失败'))
    }
  }
  // 重置设置
  async doDefault() {
    const loading = CommonUtil.Loading()
    try {
      let content: any = JSON.parse(this.updateParams.content);
      this.updateParams.type = 'system';
      if (this.updateParams.placeName === '大转盘') {
        this.updateParams.name = this.i18n('大转盘');
        this.widgets[0].props = this.imgData;
      } else if (this.updateParams.placeName === 'memberCenter') {
        this.updateParams.name = this.i18n('会员中心');
        this.widgets[0].props = this.memberCenterImgData;
      } else if (this.updateParams.placeName === 'paidMemberCardCenter') {
        this.updateParams.name = this.i18n('付费会员中心');
        this.paidMemberImgData.id = 'paidMemberCardCenter'
        this.paidMemberImgData.name = '付费会员中心'
        this.widgets[0].props = this.paidMemberImgData;
      } else if (this.updateParams.placeName === 'equityCardCenter') {
        this.updateParams.name = this.i18n('权益卡中心');
        this.paidMemberImgData.id = 'equityCardCenter'
        this.paidMemberImgData.name = '权益卡中心'
        this.widgets[0].props = this.paidMemberImgData;
      } else if (this.updateParams.placeName === 'memberPayCode') {
        this.updateParams.name = this.i18n('会员码');
        this.memberCodeData.id = 'memberPayCode'
        this.memberCodeData.name = this.i18n('会员码')
        this.widgets[0].props = this.memberCodeData;
      } else if (this.updateParams.placeName === 'scorePayCode') {
        this.updateParams.name = this.i18n('积分支付');
        this.pointCodeData.id = 'scorePayCode'
        this.pointCodeData.name = this.i18n('积分支付')
        this.widgets[0].props = this.pointCodeData;
      } else if (this.updateParams.placeName === 'depositPayCode') {
        this.updateParams.name = this.i18n('储值支付');
        this.balanceCodeData.id = 'depositPayCode'
        this.balanceCodeData.name = this.i18n('储值支付')
        this.widgets[0].props = this.balanceCodeData;
      } else if (this.updateParams.placeName === 'signAndLogin') {
        this.updateParams.name = this.i18n('H5登录注册');
        this.SignAndLoginData.id = 'signAndLogin'
        this.widgets[0].props = this.SignAndLoginData;
      }
      this.widgets[0].type = 'default';
      content.widgets = this.widgets;
      this.updateParams.content = content;
      let res = await ContentTemplateApi.update(this.updateParams);
      if (res.code === 2000) {
        this.$message.success(this.i18n('重置默认成功'))
      } else {
        this.$message.error(res.msg || this.i18n('重置默认失败'))
      }
    } catch (error) {
      this.$message.error((error as Error).message || this.i18n('重置默认失败'))
    } finally {
      loading.close()
    }
  }
  // 重置获取模型参数
  loadData() {
    const loading = this.$loading(ConstantMgr.loadingOption)
    ContentTemplateApi.get(this.editId).then(res => {
      if (res.data) {
        this.updateParams = res.data as any
        if (res.data.content) {
          const details = JSON.parse(res.data.content)
          this.widgets = details.widgets
          this.widgets = this.widgets.slice(0, 1)
          this.doDefault()
        }
      }
    }).catch((err) => {
      this.$message.error(err.message || this.i18n('数据加载失败'))
    }).finally(() => {
      loading.close()
    })
  }
  /**
 * 重置
 */
  reset(row: any) {
    this.editId = row.id
    this.loadData()
  }
  /**
  * 发布 
  */
  doPublish(row: any) {
    ContentTemplateApi.publish({ id: row.id }).then(res => {
      if (res && res.code === 2000) {
        this.$message.success(this.i18n('发布成功'))
        this.getList()
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  /**
 * 推广
 */
  doSpread(row: any) {

  }
  /**
  * 删除
  */
  doRemove(row: any) {
    this.$confirm(this.i18n('是否确认删除？'), this.i18n('提示'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消'),
      type: 'warning'
    }).then(() => {
      ContentTemplateApi.remove({ id: row.id }).then(res => {
        if (res && res.code === 2000) {
          this.$message.success(this.i18n('删除成功'))
          this.getList()
        }
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    });

  }
};