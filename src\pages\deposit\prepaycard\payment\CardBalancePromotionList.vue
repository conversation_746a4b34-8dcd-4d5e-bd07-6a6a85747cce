<template>
  <div class="card-balance-promotion-list">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
      </template>
    </BreadCrume>
    <div style="flex: 1;overflow: auto" class="current-page-parent">
      <div class="current-page">
        <div class="create-activity-block" v-if="permission.viewable || permission.discountViewable">
          <el-row style="padding: 25px;">
            <el-col :span="8" v-if="permission.viewable">
              <div class="btn-item">
                <p class="btn-item-title">预付卡支付立减</p>
                <p class="btn-item-small-title">购买商品满金额或满数量，预付卡支付享立减</p>
                <br>
                <el-button type="primary" size="small" @click="createMbp" v-if="permission.editable">新建活动</el-button>
              </div>
            </el-col>
            <el-col :span="8" v-if="permission.discountViewable">
              <div class="btn-item">
                <p class="btn-item-title">预付卡支付折扣</p>
                <p class="btn-item-small-title">购买商品满金额，预付卡支付享折扣</p>
                <br>
                <el-button v-if="permission.discountEditable" type="primary" size="small" @click="createMbdp">新建活动</el-button>
              </div>
            </el-col>
          </el-row>
        </div>
        <div style="height: 15px;background-color: #EEEFF1" v-if="permission.viewable || permission.discountViewable"></div>
        <ListWrapper style="overflow: hidden">
          <template slot="query">
            <el-form label-width="130px" class="queryForm">
              <el-row>
                <el-col :span="8">
                  <el-form-item label="活动名称">
                    <el-input v-model="query.nameLikes" style="width:80%"/>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="所属主题">
                    <el-select
                            clearable
                            placeholder="请选择"
                            v-model="query.topicNameLikes" style="width:80%">
                      <el-option no-i18n :label="item.name"
                                 :value="item.name" :key="item.name"
                                 v-for="item in themes">{{item.name}}
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="活动号">
                    <el-input v-model="query.activityIdLikes" style="width:80%"/>
                  </el-form-item>
                </el-col>
                <el-col :span="8" v-if="permission.viewable && permission.discountViewable">
                  <el-form-item label="活动类型">
                    <el-select
                            clearable
                            placeholder="请选择"
                            v-model="query.activityTypeEquals">
                      <el-option no-i18n :label="item.label"
                                 :value="item.code"
                                 v-for="(item, index) in activityTypes"
                                :key="index">{{ item.label }}
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <!-- <el-col :span="8">
                  <el-form-item label="卡模板名称">
                    <el-input v-model="query.nameLikes" style="width:80%"/>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="卡模板号">
                    <el-input v-model="query.nameLikes" style="width:80%"/>
                  </el-form-item>
                </el-col> -->
              </el-row>
              <el-row>
                <el-form-item label=" ">
                  <el-button type="primary" @click="doSearch">查询</el-button>
                  <el-button @click="doReset">重置</el-button>
                </el-form-item>
              </el-row>
            </el-form>
          </template>
          <template slot="list">
            <el-row class="row">
              <el-tabs v-model="tabName" @tab-click="handleTabClick">
                <el-tab-pane :label="allTab" name="ALL"/>
                <el-tab-pane :label="initialTab" name="INITAIL"/>
                <el-tab-pane :label="unstartTab" name="UNSTART"/>
                <el-tab-pane :label="processingTab" name="PROCESSING"/>
                <el-tab-pane :label="stopedTab" name="STOPED"/>
              </el-tabs>
            </el-row>
            <FloatBlock :top="95" refClass="current-page-parent" v-if="tabName !== 'STOPED'">
              <template slot="ctx">
                <el-row class="row">
                  <el-checkbox v-model="checkedAll" @change="checkedAllRow" style="margin-left: 15px"/>
                  <i18n k="/储值/预付卡/预付卡支付活动/列表页面/已选择{0}个活动">
                    <template slot="0">
                      &nbsp;{{selected.length}}&nbsp;
                    </template>
                  </i18n>
                  <span style="margin-left: 30px"></span>
                  <el-button key="1" @click="auditBatch"
                             v-if="(permission.auditable && permission.discountAuditable) && ['ALL', 'INITAIL'].indexOf(tabName) > -1">批量审核
                  </el-button>
                  <el-button key="2" @click="stopBatch" v-if="(permission.terminable && permission.discountTerminable) && ['ALL', 'UNSTART', 'PROCESSING'].indexOf(tabName) > -1">批量终止
                  </el-button>
                  <el-button key="3" type="danger" @click="delBatch" v-if="(permission.editable && permission.discountEditable) && ['ALL', 'INITAIL'].indexOf(tabName) > -1">批量删除
                  </el-button>
                </el-row>
              </template>
            </FloatBlock>
            <el-row class="row">
              <el-table
                      :data="queryData"
                      style="width: 100%;margin-top: 20px"
                      ref="table"
                      @selection-change="handleSelectionChange"
              >
                <el-table-column
                        v-if="tabName !== 'STOPED'"
                        type="selection"
                        width="55"/>
                <el-table-column
                        label="活动名称"
                        fixed="left"
                        width="200"
                >
                  <template slot-scope="scope">
                    <div class="div-flex">
                      <a no-i18n href="javascript: void(0)" :title="scope.row.body.activityId" @click="gotoDtl(scope.row)" style="color:var(--font-color-primary)">{{ scope.row.body.activityId }}</a>
                      <a no-i18n href="javascript: void(0)" @click="gotoDtl(scope.row)"
                        :title="scope.row.body.name">{{ scope.row.body.name }}</a>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                        label="所属主题"
                        prop="body.topicName"
                        width="150"
                >
                  <template slot-scope="scope">
                    <span no-i18n>{{scope.row.body.topicName|nullable}}</span>
                  </template>
                </el-table-column>
                <el-table-column
                        label="活动时间"
                        width="200"
                >
                  <template slot-scope="scope">
                    <span no-i18n>{{activityTime(scope.row)}}</span>
                  </template>
                </el-table-column>
                <el-table-column
                        label="状态"
                        width="150"
                >
                  <template slot-scope="scope">
                    <ActivityState no-i18n :state="scope.row.body.state"/>
                  </template>
                </el-table-column>
                <el-table-column
                        label="活动类型"
                        :width="activityTypeWidth"
                        prop="type"
                >
                  <template slot-scope="scope">
                    <span no-i18n>{{i18n(scope.row.type)}}</span>
                  </template>
                </el-table-column>
                <el-table-column
                        label="适用卡模板"
                        width="400"
                        prop="type"
                >
                  <template slot-scope="scope">
                  <span no-i18n :title="scope.row.cardTemplates.map((e)=>e.name).join(';')" v-if="scope.row.cardType === 'none'">
                    <span v-for="(cardTemplate,index) of scope.row.cardTemplates" :key="index">
                      <a style="margin-left: 2px" href="javascript: void(0)" @click="gotoCardTplDtl(cardTemplate.id)">{{cardTemplate.name}}</a>;
                    </span>
                  </span>
                  <span v-if="scope.row.cardType === 'rechargeableCard'">
                    全部储值卡
                  </span>
                  <span v-if="scope.row.cardType === 'all'">
                    全部预付卡（储值卡和礼品卡）
                  </span>
                  </template>
                </el-table-column>
                <el-table-column
                        fixed="right"
                        :min-width="activityOptWidth"
                        label="操作"
                >
                  <template slot-scope="scope">
                    <!--                  <el-button type="text" @click="gotoEvaluatePage(scope.row.body.activityId)"-->
                    <!--                             v-if="['INITAIL', 'UNSTART'].indexOf(scope.row.body.state) === -1">效果评估-->
                    <!--                  </el-button>-->


                    <!-- 立减 -->
                    <div v-if="scope.row.body.type === 'CARD_BALANCE_REDUCTION'">
                      <el-button key="1" type="text" @click="audit(scope.row.body.activityId)" class="opt-btn"
                               v-if="permission.auditable && scope.row.body.state === 'INITAIL'">审核
                      </el-button>
                      <el-button v-if="permission.editable" key="2" type="text" @click="copy(scope.row.body.activityId,scope.row.body.type)" class="opt-btn">复制
                      </el-button>
                      <el-button key="3" type="text" v-if="permission.terminable && ['UNSTART', 'PROCESSING'].indexOf(scope.row.body.state) > -1"
                                class="opt-btn"
                                @click="stop(scope.row.body.activityId)">终止
                      </el-button>
                      <el-button key="4" type="text" v-if="permission.editable && scope.row.body.state === 'INITAIL'" class="opt-btn"
                                @click="edit(scope.row.body.activityId,scope.row.body.type)">修改
                      </el-button>
                      <el-button key="5" type="text" v-if="permission.editable && scope.row.body.state === 'INITAIL'" class="opt-btn"
                                @click="del(scope.row.body.activityId)">删除
                      </el-button>
                    </div>
                    <!-- 折扣 -->
                    <div v-else>
                      <el-button key="1" type="text" @click="audit(scope.row.body.activityId)" class="opt-btn"
                               v-if="permission.discountAuditable && scope.row.body.state === 'INITAIL'">审核
                      </el-button>
                      <el-button v-if="permission.discountEditable" key="2" type="text" @click="copy(scope.row.body.activityId,scope.row.body.type)" class="opt-btn">复制
                      </el-button>
                      <el-button key="3" type="text" v-if="permission.discountTerminable && ['UNSTART', 'PROCESSING'].indexOf(scope.row.body.state) > -1"
                                class="opt-btn"
                                @click="stop(scope.row.body.activityId)">终止
                      </el-button>
                      <el-button key="4" type="text" v-if="permission.discountEditable && scope.row.body.state === 'INITAIL'" class="opt-btn"
                                @click="edit(scope.row.body.activityId,scope.row.body.type)">修改
                      </el-button>
                      <el-button key="5" type="text" v-if="permission.discountEditable && scope.row.body.state === 'INITAIL'" class="opt-btn"
                                @click="del(scope.row.body.activityId)">删除
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </el-row>
          </template>
          <template slot="page">
            <el-pagination
                    no-i18n
                    :current-page="page.currentPage"
                    :page-size="page.size"
                    :page-sizes="[10, 20, 30, 40]"
                    :total="page.total"
                    @current-change="onHandleCurrentChange"
                    @size-change="onHandleSizeChange"
                    background
                    layout="total, prev, pager, next, sizes,  jumper"
                    class="pagin"
            >
            </el-pagination>
          </template>
        </ListWrapper>
      </div>
    </div>

  </div>
</template>

<script lang="ts" src="./CardBalancePromotionList.ts">
</script>

<style lang="scss">
  .card-balance-promotion-list {
    background-color: white;
    height: 100%;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .total {
      margin: 20px;
    }

    .current-page {
      /*height: 100%;*/
      /*overflow: auto;*/

      .opt-btn {
        margin-left: 0 !important;
        margin-right: 15px !important;
      }

      .create-activity-block {
        background-color: white;

        .btn-item {
          width: 95%;
          background-color: #F9F9F9;
          padding: 20px;

          .btn-item-title {
            font-weight: 600;
          }

          .btn-item-small-title {
            color: #797979;
          }
        }
      }

      .el-form-item {
        margin-bottom: 0;
      }

      .el-select {
        width: 100%;
      }

      .query {
        padding: 20px 20px 0 20px;
      }

      .row {
        padding: 0 20px;

        .state {
          width: 7px;
          height: 7px;
          border-radius: 10px;
          float: left;
          margin-top: 7px;
          margin-right: 5px;
        }
      }

      .list {
        /*height: calc(100% - 150px);*/
        overflow: hidden;

        .el-col {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }

      .cell {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .pagin {
        /*padding: 20px;*/
      }
    }
    .list-wrapper{
      display: flex;
      flex-direction: column;
    }
    .div-flex{
      display: flex;
      flex-direction: column
    }
  }
</style>
