/*
 * @Author: 黎钰龙
 * @Date: 2024-08-11 13:42:17
 * @LastEditTime: 2024-08-13 10:40:05
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\card\writeCard\WriteCardBillApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import CheckCardResult from 'model/card/writeCard/CheckCardResult'
import WriteCardBill from 'model/card/writeCard/WriteCardBill'
import WriteCardBillCalRequest from 'model/card/writeCard/WriteCardBillCalRequest'
import WriteCardProcessingResult from 'model/card/writeCard/WriteCardProcessingResult'
import WriteCardRequest from 'model/card/writeCard/WriteCardRequest'
import WriteCardResult from 'model/card/writeCard/WriteCardResult'
import Response from 'model/common/Response'

export default class WriteCardBillApi {
  /**
   * 校验起始卡号
   * 校验起始卡号。
   * 
   */
  static checkStartCode(code: string): Promise<Response<CheckCardResult>> {
    return ApiClient.server().post(`/v1/write-card-bill/checkStartCode/${code}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 查询可写卡数量
  * 查询可写卡数量。
  * 
  */
  static calWriteQty(body: WriteCardBillCalRequest): Promise<Response<number>> {
    return ApiClient.server().post(`/v1/write-card-bill/calWriteQty`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 制卡完成
   * 制卡完成。
   * 
   */
  static finish(billNumber: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/write-card-bill/finish/${billNumber}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取进行中写卡单
   * 获取进行中写卡单。
   * 
   */
  static get(): Promise<Response<WriteCardProcessingResult>> {
    return ApiClient.server().get(`/v1/write-card-bill/get`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建预付卡写卡单
   * 新建预付卡写卡单。
   * 
   */
  static save(body: WriteCardBill): Promise<Response<WriteCardResult>> {
    return ApiClient.server().post(`/v1/write-card-bill/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 写卡
   * 导出卡列表信息
   * 
   */
  static writeCard(body: WriteCardRequest): Promise<Response<WriteCardResult>> {
    return ApiClient.server().post(`/v1/write-card-bill/writeCard`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
