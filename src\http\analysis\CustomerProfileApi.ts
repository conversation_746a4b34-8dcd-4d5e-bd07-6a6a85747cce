import ApiClient from "http/ApiClient"
import BCustomerProfile from "model/analysis/BCustomerProfile"
import BCustomerProfileFilter from "model/analysis/BCustomerProfileFilter"
import BCustomerProfileLine from "model/analysis/BCustomerProfileLine"
import BCustomerProfileLineSaveRequest from "model/analysis/BCustomerProfileLineSaveRequest"
import BMemberItem from "model/analysis/BMemberItem"
import BMemberPropFilter from "model/analysis/BMemberPropFilter"
import IdName from "model/common/IdName"
import Response from 'model/common/Response'

export default class CustomerProfileApi {
  /**
   * 查询自定义信息
   * 查询自定义信息。
   * 
   */
  static list(body: BCustomerProfileFilter): Promise<Response<BCustomerProfile[]>> {
    return ApiClient.server().post(`/v1/datainsight/customerprofile/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 卡片明细查询
   * 卡片明细查询。
   * 
   */
  static listLine(customerProfileId: string): Promise<Response<BCustomerProfileLine[]>> {
    return ApiClient.server().post(`/v1/datainsight/customerprofile/listLine`, {}, {
      params: {
        customerProfileId: customerProfileId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 画像修改
   * 画像修改
   * 
   */
  static modify(body: BCustomerProfile): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/datainsight/customerprofile/modify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 会员属性查询
   * 会员属性查询
   * 
   */
  static queryMemberProp(body: BMemberPropFilter): Promise<Response<BMemberItem[]>> {
    return ApiClient.server().post(`/v1/datainsight/customerprofile/queryMemberProp`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 画像删除
   * 画像删除。
   * 
   */
  static remove(id: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/datainsight/customerprofile/remove`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 画像保存
   * 画像保存
   * 
   */
  static save(body: BCustomerProfile): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/datainsight/customerprofile/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 画像计算
   * 画像计算
   * 
   */
  static cal(body: BCustomerProfileLine): Promise<Response<BCustomerProfileLine>> {
    return ApiClient.server().post(`/v1/datainsight/customerprofile/cal`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 画像明细保存
   * 画像明细保存
   * 
   */
  static saveLine(body: BCustomerProfileLineSaveRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/datainsight/customerprofile/saveLine`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
