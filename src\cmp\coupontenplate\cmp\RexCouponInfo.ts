import I18nPage from "common/I18nDecorator";
import WeimobCoupon from "model/common/weimobCoupon/WeimobCoupon";
import RexCoupon from "model/coupon/template/RexCoupon";
import AmountToFixUtil from "util/AmountToFixUtil";
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
@I18nPage({
  prefix: ["/公用/券模板"],
  auto: true,
})
@Component({
  name: "RexCouponInfo",
  components: {},
})
export default class RexCouponInfo extends Vue {
  @Prop()
  value: RexCoupon;

  $refs: any;

  rules: any = {
    limitedUse: [
      {
        required: true,
        message: this.formatI18n("/公用/券模板", "请输入必填项"),
        trigger: "blur",
      },
    ],
  };

  handleNetWorthBlur() {
    this.value.netWorth = AmountToFixUtil.formatAmount(this.value.netWorth, 99999.99, 0.01, 2);
    this.$emit("input",this.value)
  }
  handlelimitedUseBlur() {
    this.value.limitedUse = AmountToFixUtil.formatNumber(this.value.limitedUse, 100, 1);
    this.$emit("input",this.value)
  }

  doValidate() {
    return this.$refs.couponRef.validate();
  }
}
