/*
 * @Author: 申鹏渤
 * @Date: 2023-12-05 09:39:48
 * @LastEditTime: 2023-12-07 16:15:36
 * @LastEditors: 申鹏渤
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\common\TagInfo.ts
 * 记得注释
 */
import { TagTypeEnum } from 'model/common/TagTypeEnum'

// 标签信息前端对象
export default class TagInfo {
  // 对应的标签名称
  name: Nullable<string> = null
  // 分类名称
  categoryName: Nullable<string> = null
  // 是否为手工标签
  isManual: Nullable<boolean> = null
  // 标签ID
  tagId: Nullable<string> = null
  // esFieldName
  esFieldName: Nullable<string> = null
  // 标值列表
  tagValues: string[] = []
  // 标签类型
  tagType: Nullable<TagTypeEnum> = null
}