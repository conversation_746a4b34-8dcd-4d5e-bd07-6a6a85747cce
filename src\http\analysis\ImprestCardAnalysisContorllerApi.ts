import ApiClient from 'http/ApiClient'
import BImprestCardAnalysisReportGraphic from 'model/analysis/BImprestCardAnalysisReportGraphic'
import BPrepayCardAnalysisFilter from 'model/analysis/BPrepayCardAnalysisFilter'
import BImprestCardAnalysisReport from 'model/analysis/BImprestCardAnalysisReport'

import Response from 'model/default/Response'

export default class ImprestCardAnalysisContorllerApi {
  /**
   * 导出
   * 导出
   * 
   */
  static export(body: BPrepayCardAnalysisFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/analysis-report/imprest-card/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 充值卡分析报表
   * 充值卡分析报表
   * 
   */
  static query(body: BPrepayCardAnalysisFilter): Promise<Response<BImprestCardAnalysisReportGraphic>> {
    return ApiClient.server().post(`/v1/analysis-report/imprest-card/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

    /**
   * 汇总查询
   * 汇总查询
   * 
   */
     static querySum(body: BPrepayCardAnalysisFilter): Promise<Response<BImprestCardAnalysisReport>> {
      return ApiClient.server().post(`/v1/analysis-report/imprest-card/querySum`, body, {
      }).then((res) => {
        return res.data
      })
    }
  

}
