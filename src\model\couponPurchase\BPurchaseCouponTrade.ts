import Channel from 'model/common/Channel'
import IdName from 'model/common/IdName'
import Ident from 'model/member_standard/Ident'
import MutableNsid from 'model/common/MutableNsid'
import { OrderType } from 'model/common/OrderType'
import PurchaseCouponUseDetail from './PurchaseCouponUseDetail'
import PurchaseCouponTradeComplaint from 'model/couponPurchase/PurchaseCouponTradeComplaint'
import BPurchaseCouponUseDetail from 'model/couponPurchase/BPurchaseCouponUseDetail'
import RefundInfo from './RefundInfo'

// 购券管理交易详情
export default class BPurchaseCouponTrade {
  // 营销中心
  uuid: Nullable<string> = null
  // 营销中心
  marketingCenter: Nullable<string> = null
  // 交易ID
  tradeId: Nullable<MutableNsid> = null
  // 商户订单号
  tradeNo: Nullable<string> = null
  // 发生时间
  occurredTime: Nullable<Date> = null
  // 会员Id
  memberId: Nullable<string> = null
  // 会员code
  memberCode: Nullable<Ident> = null
  // 手机号
  mobile: Nullable<string> = null
  // 发生组织
  occurredOrg: Nullable<IdName> = null
  // 发生区域
  zone: Nullable<IdName> = null
  // 发生渠道
  channel: Nullable<Channel> = null
  /**
   * 订单类型
   * 订单：payOrder/退款单：refundOrder
   */
  orderType: Nullable<OrderType> = null
  // 是否付费交易
  isPay: Nullable<boolean> = null
  // 支付订单号
  payOrderNo: Nullable<string> = null
  // 订单金额
  amount: Nullable<number> = null
  // 订单金额
  payState: Nullable<string> = null
  // 发券状态
  issueSuccess: Nullable<boolean> = null
  // 实际退款金额
  refundAmount: Nullable<number> = null
  // 申请金额
  applyAmount: Nullable<number> = null
  // 原交易ID
  originalTradeId: Nullable<MutableNsid> = null
  // 原交易号
  originalTradeNo: Nullable<string> = null
  // 购券数量
  purchaseCouponCount: Nullable<number> = null
  // 可用券数量
  usableCouponCount: Nullable<number> = null
  // 过期券数量
  expiredCouponCount: Nullable<number> = null
  // 应退金额
  shouldRefundAmount: Nullable<number> = null
  // 审核时间
  auditTime: Nullable<Date> = null
  /**
   * EXT0
   * 用作记录活动子类型
   */
  ext0: Nullable<string> = null
  /**
   * EXT1
   * 用作记录活动类型
   */
  ext1: Nullable<string> = null
  /**
   * EXT2
   * 用作记录使用了多少积分
   */
  ext2: Nullable<string> = null
  //
  ext3: Nullable<string> = null
  //
  ext4: Nullable<string> = null
  //
  ext5: Nullable<string> = null
  //
  ext6: Nullable<string> = null
  //
  ext7: Nullable<string> = null
  //
  ext8: Nullable<string> = null
  //
  ext9: Nullable<string> = null
  // 推送微盟的状态，1：成功，0：失败
  sycnWeimobResult: Nullable<string> = null
  // 推送微盟的结果：成功时记录请求的劵码；失败时，记录失败的信息（取前254个字符）
  sycnWeimobMessage: Nullable<string> = null
  // 推送微盟的结果的最后时间（格式yyyy-MM-dd HH:mm:ss）
  sycnDate: Nullable<string> = null
  // 购券交易使用明细记录
  useDetail: BPurchaseCouponUseDetail[] = []
  // 交易纠纷单
  complaint: Nullable<PurchaseCouponTradeComplaint> = null
  // 售后信息
  refundInfos: Nullable<RefundInfo[]> = null
  // 推广人id
  employeeCode: Nullable<string> = null
  // 推广人姓名
  employeeName: Nullable<string> = null
  // 用于积分兑换券交易单是否展示应退金额
  showRefund:  Nullable<boolean> = null
}
