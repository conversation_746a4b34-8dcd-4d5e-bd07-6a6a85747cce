import I18nPage from 'common/I18nDecorator';
import {Component, Vue} from 'vue-property-decorator';
import RSOrg from 'model/common/RSOrg'
import WeixinQrCodeApi from 'http/weixin/qrcode/WeixinQrCodeApi'
import RSEmployeeFilter from "model/common/RSEmployeeFilter";
import {CmsConfigChannel, CmsConfigUtils} from 'model/template/CmsConfig';
import FormItem from 'cmp/formitem/FormItem';
import RSOrgFilter from "../../../../../model/common/RSOrgFilter";
import OrgApi from "../../../../../http/org/OrgApi";
import StorePromotionCode from "model/member/StorePromotionCode";


@Component({
  name: 'StoreCodeDialog',
  components: {
    FormItem
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理',
    '/设置/页面管理'
  ],
  auto: true
})
export default class StoreCodeDialog extends Vue {
  code = ''
  dialogShow: boolean = false
  queryData: RSOrg[] = []
  total: number = 0
  query: RSEmployeeFilter = new RSEmployeeFilter()
  stores: RSOrg[] = []
  templateId: string = ''
  isLoading: boolean = false
  channel: Nullable<CmsConfigChannel> = null
  channelOptions: CmsConfigChannel[] = []

  getLabel(channel: string) {
    return CmsConfigUtils.getLabel(channel as CmsConfigChannel);
  }

  initData(code: string) {
    if(code == '') {
      return;
    }
    let params: RSOrgFilter = new RSOrgFilter();
    params.idNameLikes = code
    params.page = 0
    params.pageSize = 0
    this.isLoading = true
    OrgApi.query(params).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.queryData = resp.data || []
        this.total = resp.total
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    }).finally(() => {
      this.isLoading = false
    })
  }


  doBeforeClose(done: any) {
    this.dialogShow = false
    this.queryData = []
    done()
  }

  isEmptyArray() {
    return !this.queryData || this.queryData.length === 0;
  }

  // 下载所有员工推广码
  doConfirmClose() {
    if (!this.channel) {
      this.$message.warning(this.i18n('/设置/页面管理/请选择投放渠道'))
      return
    }
    this.dialogShow = false
    this.queryData = []
    const params = this.doParams('')
    this.isLoading = true
    WeixinQrCodeApi.downloadStorePromotionCode(params).then((res: any) => {
      if (res.code === 2000) {
        this.$emit('openDownload')
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    }).finally(() => {
      this.isLoading = false
    })
  }


  doCancel() {
    this.dialogShow = false
    this.queryData = []
  }

  open(id: string, channel?: CmsConfigChannel[]) {
    this.dialogShow = true
    this.code = ''
    this.total = 0
    this.channel = null
    this.templateId = id
    this.channelOptions = channel || []
  }

  close() {
    this.dialogShow = false
    this.queryData = []
  }

  doSearch() {
    this.initData(this.code)
  }

  // 下载单个员工推广码
  doDownloadStore(code: string) {
    if (!this.channel) {
      this.$message.warning(this.i18n('/设置/页面管理/请选择投放渠道'))
      return
    }
    const params = this.doParams(code)
    this.isLoading = true
    WeixinQrCodeApi.downloadStorePromotionCode(params).then((res: any) => {
      if (res.code === 2000 && res.data) {
        window.open(res.data, '_blank')
      } else {
        throw new Error(res.msg!)
      }
    }).catch((error: any) => {
      this.$message.error(error.message)
    }).finally(() => {
      this.isLoading = false
    })
  }

  doParams(code: string) {
    const res = new StorePromotionCode()
    res.templateId = this.templateId
    res.storeId = code
    res.marketingCenter = sessionStorage.getItem("marketCenter")
    res.path = `pages/custom-page/customPage`
    res.channel = this.channel
    return res
  }
};