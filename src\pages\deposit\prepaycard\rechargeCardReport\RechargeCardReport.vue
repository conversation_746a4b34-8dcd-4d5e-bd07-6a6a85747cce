<template>
  <div class="recharge-card-report">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button size="large" @click="doBatchExport" v-if="hasOptionPermission('/数据/报表/充值卡报表','报表导出')">批量导出</el-button>
      </template>
    </BreadCrume>
    <!-- tab切换 -->
    <div style="padding: 20px 0 0">
      <el-tabs v-model="activeName" class="tabs">
        <el-tab-pane label="售卡流水" name="售卡流水" />
        <el-tab-pane label="转出流水" name="转出流水" />
        <el-tab-pane label="退卡流水" name="退卡流水" />
      </el-tabs>
    </div>
    <div class="scroll-wrap">
      <!-- 条件搜索区域 -->
      <div class="report-search">
        <el-form label-width="140px" v-if="activeName === '售卡流水'">
          <el-row class="query">
            <TimeRange no-i18n @submit="handleTimeRange" ref="timeRange"></TimeRange>
          </el-row>
          <el-row class="query" style="margin-top: 8px">
            <el-col :span="8">
              <form-item :label="i18n('购卡人')">
                <el-input :placeholder="i18n('输入手机号/会员号')" style="width:80%" v-model="query.memberIdEquals" />
              </form-item>
            </el-col>
            <el-col :span="8">
              <form-item :label="i18n('交易号')">
                <el-input placeholder="等于" style="width:80%" v-model="query.transNoEquals" />
              </form-item>
            </el-col>
            <el-col :span="8">
              <form-item :label="i18n('卡面额')">
                <div class="el-date-editor" style="width: 80%;display:flex">
                  <el-input :placeholder="i18n('最小值')" class="el-range-input" v-model="query.faceAmountGreaterOrEquals" />
                  <span class="el-range-separator" style="padding:0px 15px;height:32px;">-</span>
                  <el-input :placeholder="i18n('最大值')" class="el-range-input" v-model="query.faceAmountLessOrEquals" />
                </div>
              </form-item>
            </el-col>
          </el-row>
          <el-row class="query" style="margin-top: 8px" v-if="expandQuery">
            <el-col :span="8">
              <form-item label="售价">
                <div class="el-date-editor" style="width: 80%;display: flex">
                  <el-input placeholder="最小值" class="el-range-input" v-model="query.priceGreaterOrEquals" />
                  <span class="el-range-separator" style="padding:0px 15px;height:32px;">-</span>
                  <el-input placeholder="最大值" class="el-range-input" v-model="query.priceLessOrEquals" />
                </div>
              </form-item>
            </el-col>
            <el-col :span="8">
              <form-item label="卡模板">
                <el-input placeholder="等于" style="width:80%" v-model="query.templateEquals" />
              </form-item>
            </el-col>
            <el-col :span="8">
              <form-item label="卡号">
                <el-input placeholder="等于" style="width:80%" v-model="query.codeEquals" />
              </form-item>
            </el-col>
          </el-row>
          <el-row class="query" style="margin-top: 8px" v-if="expandQuery">
            <el-col :span="8">
              <form-item label="发生组织">
                <SelectStores v-model="query.orgIdEquals" :isOnlyId="true" :hideAll="false" width="80%"
                  :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
                </SelectStores>
              </form-item>
            </el-col>
            <el-col :span="8" v-if="isMoreMarketing">
              <form-item :label="formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水/发生区域')">
                <el-select placeholder="不限" style="width:80%" v-model="query.zoneIdEquals">
                  <el-option :label="formatI18n('/公用/查询条件/下拉列表/不限')" :value="null">{{ formatI18n('/公用/查询条件/下拉列表/不限') }}</el-option>
                  <el-option :key="item.zone.id" :label="'['+item.zone.id+']'+item.zone.name" :value="item.zone.id"
                    v-for="item in areaData">[{{item.zone.id}}]{{item.zone.name}}</el-option>
                </el-select>
              </form-item>
            </el-col>
          </el-row>
        </el-form>

        <el-form label-width="140px" v-else-if="activeName === '退卡流水'">
          <el-row class="query">
            <TimeRange no-i18n @submit="handleTimeRange" ref="timeRange"></TimeRange>
          </el-row>
          <el-row class="query" style="margin-top: 8px">
            <el-col :span="8">
              <form-item :label="i18n('卡模板')">
                <el-input placeholder="等于" v-model="query.templateEquals" />
              </form-item>
            </el-col>
            <el-col :span="8">
              <form-item :label="i18n('卡号')">
                <el-input placeholder="等于" v-model="query.codeEquals" />
              </form-item>
            </el-col>
            <el-col :span="8">
              <form-item :label="i18n('交易号')">
                <el-input placeholder="等于" v-model="query.transNoEquals" />
              </form-item>
            </el-col>
          </el-row>
          <el-row class="query" style="margin-top: 8px" v-if="expandQuery">
            <el-col :span="8">
              <form-item label="卡面额">
                <el-input style="width: calc(50% - 7px)" v-model="query.faceAmountGreaterOrEquals" />-
                <el-input style="width: calc(50% - 6px)" v-model="query.faceAmountLessOrEquals" />
              </form-item>
            </el-col>
            <el-col :span="8">
              <form-item label="发生组织">
                <SelectStores v-model="query.orgIdEquals" :isOnlyId="true" :hideAll="false" width="100%" :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
                </SelectStores>
              </form-item>
            </el-col>
            <el-col :span="8">
              <form-item :label="formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水/发生区域')">
                <el-select placeholder="不限" v-model="query.zoneIdEquals">
                  <el-option :label="formatI18n('/公用/查询条件/下拉列表/不限')" :value="null">{{ formatI18n('/公用/查询条件/下拉列表/不限') }}</el-option>
                  <el-option :key="item.zone.id" :label="'['+item.zone.id+']'+item.zone.name" :value="item.zone.id"
                             v-for="item in areaData">[{{item.zone.id}}]{{item.zone.name}}</el-option>
                </el-select>
              </form-item>
            </el-col>
          </el-row>
          <el-row class="query" style="margin-top:8px" v-show="expandQuery">
            <el-col :span="8">
              <form-item :label="formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水/发生渠道')">
                <el-select v-model="channelEquals" :placeholder="formatI18n('/资料/渠道/请选择')" clearable filterable>
                  <el-option v-for="(item,index) in channelTypes" :key="index" :value="getKey(item.channel)" :label="item.name">
                    <!-- <span style="float: left">{{ item.label }}</span> -->
                  </el-option>
                </el-select>
              </form-item>
            </el-col>
          </el-row>
        </el-form>

        <el-form label-width="140px" v-else>
          <el-row class="query">
            <TimeRange no-i18n @submit="handleTimeRange" ref="timeRange"></TimeRange>
          </el-row>
          <el-row class="query" style="margin-top: 8px">
            <el-col :span="8">
              <form-item :label="i18n('卡号')">
                <el-input :placeholder="i18n('等于')" style="width:80%" v-model="query.codeEquals" />
              </form-item>
            </el-col>
            <el-col :span="8">
              <form-item :label="i18n('发生组织')">
                <SelectStores v-model="query.orgIdEquals" :isOnlyId="true" :hideAll="false" width="80%"
                  :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
                </SelectStores>
              </form-item>
            </el-col>
            <el-col :span="8" v-if="isMoreMarketing">
              <form-item :label="formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水/发生区域')">
                <el-select placeholder="不限" style="width:80%" v-model="query.zoneIdEquals">
                  <el-option :label="formatI18n('/公用/查询条件/下拉列表/不限')" :value="null">{{ formatI18n('/公用/查询条件/下拉列表/不限') }}</el-option>
                  <el-option :key="item.zone.id" :label="'['+item.zone.id+']'+item.zone.name" :value="item.zone.id"
                    v-for="item in areaData">[{{item.zone.id}}]{{item.zone.name}}</el-option>
                </el-select>
              </form-item>
            </el-col>

          </el-row>
          <el-row class="query" style="margin-top: 8px" v-if="expandQuery">
            <el-col :span="8">
              <form-item :label="i18n('余额减少')">
                <div class="el-date-editor" style="width: 80%;display:flex">
                  <el-input :placeholder="formatI18n('/储值/预付卡/预付卡查询/列表页面/最小值')" class="el-range-input" v-model="query.balanceGreaterOrEquals" />
                  <span class="el-range-separator" style="padding:0px 15px;height:32px;">-</span>
                  <el-input :placeholder="formatI18n('/储值/预付卡/预付卡查询/列表页面/最大值')" class="el-range-input" v-model="query.balanceLessOrEquals" />
                </div>
              </form-item>
            </el-col>
            <el-col :span="8">
              <form-item label="卡模板">
                <el-input placeholder="等于" style="width:80%" v-model="query.templateEquals" />
              </form-item>
            </el-col>
            <el-col :span="8">
              <form-item label="交易号">
                <el-input placeholder="等于" style="width:80%" v-model="query.transNoEquals" />
              </form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-form>
          <el-row class="query" style="margin-top: 8px">
            <el-col :span="9">
              <form-item label="">
                <el-button class="btn-search" type="primary" @click="doSearch">查询</el-button>
                <el-button class="btn-reset" @click="doReset">重置</el-button>
                <el-button type="text" @click="expandQuery=!expandQuery">
                  <span v-if="!expandQuery"><i class="el-icon-arrow-down"></i>{{formatI18n('/公用/查询条件/展开')}}</span>
                  <span v-if="expandQuery"><i class="el-icon-arrow-up"></i>{{formatI18n('/公用/查询条件/收起')}}</span>
                </el-button>
              </form-item>
            </el-col>
          </el-row>
        </el-form>
        <hr />
        <el-row style="line-height: 35px" v-if="isShowSum">
          <i class="el-icon-warning" />
          <i18n k="/储值/预付卡/充值卡报表/售卡流水/共售出{0}张充值卡，销售额为{1}元" v-if="activeName === '售卡流水'">
            <template slot="0">&nbsp;<span style="color: red">{{sum.qty}}</span>&nbsp;</template>
            <template slot="1">&nbsp;<span style="color: red">{{dataUtil.showTotalAmount(sum.totalAmount)}}</span>&nbsp;</template>
          </i18n>
          <i18n  v-if="activeName === '退卡流水'">

          </i18n>
          <i18n k="/储值/预付卡/充值卡报表/售卡流水/充值卡余额转出{0}元" v-else>
            <template slot="0">&nbsp;<span style="color: red">{{dataUtil.showTotalAmount(sum.totalAmount)}}</span>&nbsp;</template>
          </i18n>
        </el-row>
      </div>
      <div class="report-table">
        <!-- 售卡流水表格 -->
        <el-table v-if="activeName === '售卡流水'" :data="queryData" border @header-dragend="tableDragend" style="width: 100%;margin-top: 20px">
          <el-table-column label="卡号" prop="code" :width="getColumnWidth('code', 180)">
          </el-table-column>
          <el-table-column :label="i18n('售卡时间')" prop="occurredTime" :width="getColumnWidth('occurredTime', 140)">
            <template slot-scope="scope">
              <span no-i18n>{{dataUtil.format(scope.row.occurredTime, 'yyyy-MM-dd HH:mm:ss')}}</span>
            </template>
          </el-table-column>
          <el-table-column label="发生组织" prop="occurredOrg" :width="getColumnWidth('occurredOrg', 150)">
            <template slot-scope="scope">
              <span no-i18n :title="dataUtil.showIdName(scope.row.occurredOrg)">{{dataUtil.showIdName(scope.row.occurredOrg)}}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="isMoreMarketing" :width="getColumnWidth('zone')" prop="zone" :label="formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水/发生区域')">
            <template slot-scope="scope">
              <div v-if="scope.row.zone!==null && scope.row.zone!==''">
                <el-tooltip class="item" effect="light" placement="right-end">
                  <div> {{'['+scope.row.zone.id+']'}}{{scope.row.zone.name}}</div>
                  <div slot="content">
                    {{'['+scope.row.zone.id+']'}}{{scope.row.zone.name}}
                  </div>
                </el-tooltip>
              </div>
              <div v-else>-</div>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('购卡人')" prop="memberId" :width="getColumnWidth('memberId', 100)">
            <template slot-scope="scope">
              <span no-i18n :title="dataUtil.showMemberId(scope.row)">{{dataUtil.showMemberId(scope.row)}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('卡面额(元)')" prop="faceAmount" :width="getColumnWidth('faceAmount', 100)" align="center">
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.faceAmount.toFixed(2)}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('售价(元)')" prop="price" :width="getColumnWidth('price', 100)" align="center">
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.price.toFixed(2)}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('返现(元)')" :width="getColumnWidth('giftAmount', 100)" prop="giftAmount" align="center">
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.giftAmount.toFixed(2)}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('交易号')" prop="transNo" :width="getColumnWidth('transNo')">
            <template slot-scope="scope">
              <span no-i18n :title="scope.row.transNo">{{scope.row.transNo}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('支付方式')" prop="payType" :width="getColumnWidth('payType',100)">
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.payType}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('卡模板')" prop="cardTemplate" :width="getColumnWidth('cardTemplate')">
            <template slot-scope="scope">
              <el-button no-i18n type="text" @click="gotoTplDtl(scope.row.templateNumber)">{{scope.row.templateName}}</el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-table v-else-if="activeName === '退卡流水'" :data="queryData3" border @header-dragend="tableDragend" style="width: 100%;margin-top: 20px">
          <el-table-column label="卡号" prop="code" :width="getColumnWidth('code', 180)">
          </el-table-column>
          <el-table-column :label="i18n('退卡时间')" prop="occurredTime" :width="getColumnWidth('occurredTime', 140)">
            <template slot-scope="scope">
              <span no-i18n>{{dataUtil.format(scope.row.occurredTime, 'yyyy-MM-dd HH:mm:ss')}}</span>
            </template>
          </el-table-column>
          <el-table-column label="发生组织" prop="occurredOrg" :width="getColumnWidth('occurredOrg', 150)">
            <template slot-scope="scope">
              <span no-i18n :title="dataUtil.showIdName(scope.row.occurredOrg)">{{dataUtil.showIdName(scope.row.occurredOrg)}}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="isMoreMarketing" :width="getColumnWidth('zone')" prop="zone" :label="formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水/发生区域')">
            <template slot-scope="scope">
              <div v-if="scope.row.zone!==null && scope.row.zone!==''">
                <el-tooltip class="item" effect="light" placement="right-end">
                  <div> {{'['+scope.row.zone.id+']'}}{{scope.row.zone.name}}</div>
                  <div slot="content">
                    {{'['+scope.row.zone.id+']'}}{{scope.row.zone.name}}
                  </div>
                </el-tooltip>
              </div>
              <div v-else>-</div>
            </template>
          </el-table-column>
          <el-table-column :label="formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水/发生渠道')" prop="channelName" :width="getColumnWidth('channelName', 200)">
            <template slot-scope="scope">
              <span no-i18n>{{ scope.row.channelName | nullable }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('卡面额(元)')" prop="faceAmount" :width="getColumnWidth('faceAmount', 100)" align="center">
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.faceAmount.toFixed(2)}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('退卡本金(元)')" prop="refundDepositAmount" align="center" :width="getColumnWidth('refundDepositAmount', 100)">
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.refundDepositAmount ? scope.row.refundDepositAmount.toFixed(2) : 0}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('退卡赠金(元)')" prop="refundGiftAmount" align="center" :width="getColumnWidth('refundGiftAmount', 100)">
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.refundGiftAmount ? scope.row.refundGiftAmount.toFixed(2) : 0}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('退卡金额(元)')" prop="refundAmount" :width="getColumnWidth('refundAmount', 100)" align="center">
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.refundAmount ? scope.row.refundAmount.toFixed(2) : 0}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('交易号')" prop="transNo" :width="getColumnWidth('transNo')">
            <template slot-scope="scope">
              <span no-i18n :title="scope.row.transNo">{{scope.row.transNo}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('卡模板')" prop="cardTemplate" :width="getColumnWidth('cardTemplate')">
            <template slot-scope="scope">
              <el-button no-i18n type="text" @click="gotoTplDtl(scope.row.templateNumber)">{{scope.row.templateName}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="卡模板号" prop="cardTemplateNumber" :width="getColumnWidth('cardTemplateNumber', 100)">
            <template slot="header">
              <span title="卡模板号">卡模板号</span>
            </template>
            <template slot-scope="scope">
              <span no-i18n :title="scope.row.templateNumber">{{scope.row.templateNumber}}</span>
            </template>
          </el-table-column>
        </el-table>

        <el-table v-else :data="queryData2" border @header-dragend="tableDragend" style="width: 100%;margin-top: 20px">
          <el-table-column label="卡号" prop="code" :width="getColumnWidth('code', 180)">
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.code}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('转出时间')" prop="occurredTime" :width="getColumnWidth('occurredTime', 140)">
            <template slot-scope="scope">
              <span no-i18n>{{dataUtil.format(scope.row.occurredTime, 'yyyy-MM-dd HH:mm:ss')}}</span>
            </template>
          </el-table-column>
          <el-table-column label="发生组织" prop="occurredOrg" :width="getColumnWidth('occurredOrg')">
            <template slot-scope="scope">
              <span no-i18n :title="dataUtil.showIdName(scope.row.occurredOrg)">{{dataUtil.showIdName(scope.row.occurredOrg)}}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="isMoreMarketing" :width="getColumnWidth('zone')" prop="zone" :label="formatI18n('/储值/预付卡/电子礼品卡报表/售卡流水/发生区域')">
            <template slot-scope="scope">
              <div v-if="scope.row.zone!==null && scope.row.zone!==''">
                <el-tooltip class="item" effect="light" placement="right-end">
                  <div> {{'['+scope.row.zone.id+']'}}{{scope.row.zone.name}}</div>
                  <div slot="content">
                    {{'['+scope.row.zone.id+']'}}{{scope.row.zone.name}}
                  </div>
                </el-tooltip>
              </div>
              <div v-else>-</div>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('持卡人')" prop="mobile" :width="getColumnWidth('mobile', 140)">
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.mobile || '-'}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('转出到会员')" prop="tranMemberCode" :width="getColumnWidth('tranMemberCode', 140)">
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.tranMemberCode || '-'}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('卡模板')" prop="templateName" :width="getColumnWidth('templateName')">
            <template slot-scope="scope">
              <el-button no-i18n type="text" @click="gotoTplDtl(scope.row.templateNumber)">{{scope.row.templateName}}</el-button>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('卡面额(元)')" prop="faceAmount" align="center" :width="getColumnWidth('faceAmount', 140)">
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.faceAmount.toFixed(2)}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('余额变动(元)')" prop="totalAmount" :width="getColumnWidth('totalAmount', 140)" align="center">
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.totalAmount.toFixed(2)}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('实充变动(元)')" prop="amount" :width="getColumnWidth('amount', 140)" align="center">
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.amount.toFixed(2)}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('返现变动(元)')" prop="giftAmount" :width="getColumnWidth('giftAmount', 140)" align="center">
            <template slot-scope="scope">
              <span no-i18n>{{scope.row.giftAmount.toFixed(2)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="交易号" prop="transNo" :width="getColumnWidth('transNo')">
            <template slot-scope="scope">
              <span no-i18n :title="scope.row.transNo">{{scope.row.transNo}}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination :current-page="query.page + 1" :page-size="query.pageSize" :page-sizes="[10, 20, 30, 40]" :total="pageTotal"
        @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background :layout="getPageLayout('total, prev, pager, next, sizes, jumper',probeEnabled)"
        class="report-pagin">
      </el-pagination>
    </div>
    <reportExport :dialogShow="exportDialogShow" @dialogClose="exportDialogShow = false" @doSubmit="doExportSubmit">
    </reportExport>
    <DownloadCenterDialog :dialogvisiable="fileDialogVisible" :showTip="showTip" @dialogClose="doDownloadDialogClose">
    </DownloadCenterDialog>
  </div>
</template>

<script lang='ts' src='./RechargeCardReport.ts'></script>

<style scoped lang='scss'>
.recharge-card-report {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: auto;

  .tabs {
    padding: 0 20px;
  }

  .report-search {
    padding: 0 20px;

    & ::v-deep .query {
      .el-form-item {
        margin-bottom: 0;
      }
    }

    & ::v-deep .el-range-input {
      flex: 1;
    }
  }

  .report-table {
    padding: 0 20px 20px 20px;
  }

  .scroll-wrap {
    height: calc(100% - 150px);
    overflow-y: auto;
    padding: 0;
  }

  .report-pagin {
    margin-top: 25px;
  }

  & ::v-deep tbody {
    .cell {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>