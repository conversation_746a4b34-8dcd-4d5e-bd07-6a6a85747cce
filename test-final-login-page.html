<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>H5登录注册页面 - 最终版本</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            gap: 24px;
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .panel-left {
            flex: 1;
            max-width: 850px;
        }
        .backDecoration {
            padding: 26px 20px 40px;
            margin-bottom: 12px;
            border-radius: 8px;
            background: #ffffff;
            border: 1px solid #e0e0e0;
        }
        .title {
            font-weight: 600;
            font-size: 16px;
            color: #242633;
            line-height: 24px;
            margin-bottom: 16px;
        }
        .content {
            margin-left: 8px;
        }
        .form-item {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        .form-label {
            width: 140px;
            line-height: 32px;
            color: #606266;
            font-size: 14px;
        }
        .form-content {
            flex: 1;
            line-height: 32px;
        }
        .panel-right {
            flex-shrink: 0;
            width: 400px;
            height: 764px;
            border-radius: 8px;
            background: #ffffff;
            padding: 30px 25px;
            box-sizing: border-box;
            border: 1px solid #e0e0e0;
        }
        .pageBox {
            width: 100%;
            height: 100%;
            background: #f0f2f6;
            border: 8px solid #000000;
            border-radius: 50px;
            box-sizing: border-box;
            overflow: hidden;
        }
        .header-bar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            height: 74px;
            font-size: 18px;
            font-weight: 600;
            color: #262626;
        }
        .back-btn {
            font-size: 20px;
            cursor: pointer;
            width: 30px;
        }
        .header-title {
            flex: 1;
            text-align: center;
        }
        .header-right {
            width: 30px;
        }
        .login-form-container {
            width: 280px;
            margin: 20px auto 0;
            padding: 30px 20px 20px;
            background-color: #ffffff;
            border-radius: 20px;
            box-sizing: border-box;
        }
        .login-type-tabs {
            display: flex;
            margin-bottom: 30px;
            background-color: #f8f8f8;
            border-radius: 8px;
            padding: 4px;
        }
        .tab-item {
            flex: 1;
            text-align: center;
            padding: 12px 0;
            font-size: 16px;
            color: #666;
            cursor: pointer;
            border-radius: 6px;
            transition: all 0.3s ease;
        }
        .tab-item.active {
            color: #ff4444;
            font-weight: 600;
            background-color: #ffffff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .input-group {
            margin-bottom: 30px;
        }
        .country-selector {
            margin-bottom: 16px;
        }
        .country-select {
            width: 100%;
            height: 48px;
            padding: 0 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            outline: none;
            box-sizing: border-box;
            background-color: #ffffff;
            color: #333;
        }
        .country-select:focus {
            border-color: #ff4444;
        }
        .input-item {
            position: relative;
        }
        .login-input {
            width: 100%;
            height: 48px;
            padding: 0 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            outline: none;
            box-sizing: border-box;
        }
        .login-input:focus {
            border-color: #ff4444;
        }
        .login-btn {
            width: 100%;
            height: 48px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-bottom: 20px;
            background-color: #000000;
            color: #FFFFFF;
            transition: all 0.3s ease;
        }
        .login-btn:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }
        .login-btn:active {
            transform: translateY(0);
        }
        .description {
            font-size: 12px;
            color: #999;
            line-height: 1.4;
            text-align: center;
            padding: 0 10px;
        }
        .description a {
            color: #ff4444;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="panel-left">
            <div class="backDecoration">
                <div class="title">页面设置</div>
                <div class="content">
                    <div class="form-item">
                        <div class="form-label">顶部标题形式</div>
                        <div class="form-content">
                            <label><input type="radio" name="titleType" value="text" checked> 文字</label>
                            <label><input type="radio" name="titleType" value="image"> 图片</label>
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label">登录方式</div>
                        <div class="form-content">
                            <label><input type="checkbox" checked> 手机号</label>
                            <label><input type="checkbox" checked> 邮箱</label>
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label">按钮背景色</div>
                        <div class="form-content">
                            <input type="color" value="#000000" style="width: 100px; height: 32px;">
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label">按钮字体色</div>
                        <div class="form-content">
                            <input type="color" value="#FFFFFF" style="width: 100px; height: 32px;">
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label">发送验证码提示文字颜色</div>
                        <div class="form-content">
                            <input type="color" value="#999999" style="width: 100px; height: 32px;">
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label">说明内容</div>
                        <div class="form-content">
                            <textarea style="width: 300px; height: 80px; padding: 8px; border: 1px solid #e0e0e0; border-radius: 4px;" placeholder="请输入说明内容">点击继续即表示您同意我们的服务条款和隐私政策</textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="panel-right">
            <div class="pageBox">
                <div class="header-bar">
                    <div class="back-btn">←</div>
                    <div class="header-title">登录/注册</div>
                    <div class="header-right"></div>
                </div>
                
                <div class="login-form-container">
                    <div class="login-type-tabs">
                        <div class="tab-item active" onclick="switchTab('mobile')">
                            手机号
                        </div>
                        <div class="tab-item" onclick="switchTab('email')">
                            邮箱
                        </div>
                    </div>
                    
                    <div class="input-group">
                        <div class="country-selector" id="countrySelector">
                            <select class="country-select">
                                <option value="+886">+886 Taiwan, China</option>
                                <option value="+86">+86 中国大陆</option>
                                <option value="+852">+852 香港</option>
                                <option value="+853">+853 澳门</option>
                                <option value="+1">+1 美国</option>
                            </select>
                        </div>
                        <div class="input-item">
                            <input type="text" id="loginInput" placeholder="请输入手机号码" class="login-input" />
                        </div>
                    </div>
                    
                    <button class="login-btn">继续</button>
                    
                    <div class="description">
                        点击继续即表示您同意我们的<a href="#">服务条款</a>和<a href="#">隐私政策</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function switchTab(type) {
            const tabs = document.querySelectorAll('.tab-item');
            const loginInput = document.getElementById('loginInput');
            const countrySelector = document.getElementById('countrySelector');
            
            tabs.forEach(tab => tab.classList.remove('active'));
            
            if (type === 'mobile') {
                tabs[0].classList.add('active');
                loginInput.placeholder = '请输入手机号码';
                countrySelector.style.display = 'block';
            } else {
                tabs[1].classList.add('active');
                loginInput.placeholder = '请输入邮箱地址';
                countrySelector.style.display = 'none';
            }
        }
    </script>
</body>
</html>
