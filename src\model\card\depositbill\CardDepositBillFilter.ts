import { State } from 'model/common/State'

export default class CardDepositBillFilter {
  // 卡号类似于
  codeLikes: Nullable<string> = null
  // 卡号等于
  codeEquals: Nullable<string> = null
  // 单号类似于
  billNumberLikes: Nullable<string> = null
  // 单号等于
  billNumberEquals: Nullable<string> = null
  // 状态等于: 未审核:INITIAL,已审核: AUDITED
  stateEquals: Nullable<'INITIAL'|'AUDITED'> = null
  // 创建时间范围>=
  createTimeGreaterOrEquals: Nullable<Date> = null
  // 创建时间范围<
  createTimeLess: Nullable<Date> = null
  // 最后修改时间范围>=
  lastModifiedTimeGreaterOrEquals: Nullable<Date> = null
  // 最后修改时间范围<
  lastModifiedTimeLess: Nullable<Date> = null
  // 营销中心等于
  marketingCenterEquals: Nullable<string> = null
  // 页码
  page: Nullable<number> = null
  // 页面大小，大于0
  pageSize: Nullable<number> = null
}