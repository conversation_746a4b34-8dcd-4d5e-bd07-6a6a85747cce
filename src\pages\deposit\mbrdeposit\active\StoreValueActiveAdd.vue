<template>
  <div class="store-value-active-add">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button v-if="hasOptionPermission('/储值/储值活动/充值有礼/储值充值活动', '活动维护')" @click="doSave" size="small" type="primary">保存</el-button>
        <el-button v-if="!disabled && !isOaActivity && hasOptionPermission('/储值/储值活动/充值有礼/储值充值活动', '活动审核')" @click="doSaveAndAudit" size="small" type="default">保存并审核</el-button>
        <el-button @click="doCancel">取消</el-button>
      </template>
    </BreadCrume>
    <div class="setting-container">
      <div class="setting-block">
        <div class="section-title">活动信息</div>
        <div class="content-inner">
          <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="120px" class="demo-ruleForm">
            <el-form-item label="活动名称" prop="name">
              <el-input :disabled="disabled" v-model="ruleForm.name" class="input-width" placeholder="请输入不超过80个字符"></el-input>
            </el-form-item>
            <el-form-item :required="true" label="活动时间">
              <ActivityDateTimeConditionPicker :required="true" :disabled="disabled" v-model="ruleForm.activityDateTimeCondition" ref="activityDateTimeConditionPicker">
                  </ActivityDateTimeConditionPicker>
              <!-- <el-date-picker :disabled="disabled" v-model="ruleForm.date" class="input-width" end-placeholder="结束时间" format="yyyy-MM-dd" range-separator="-"
                :picker-options="dateRangeOption" ref="selectDate" size="small" start-placeholder="开始时间" type="daterange" value-format="yyyy-MM-dd">
              </el-date-picker> -->
            </el-form-item>
            <el-form-item label="活动渠道" prop="resource">
              <el-radio-group :disabled="disabled" v-model="ruleForm.useHdPosChannel" @change="changeChannelType">
                <el-row style="height: 40px;display: flex;align-items: center">
                  <el-radio :label="true" style="margin-right: 0">{{ formatI18n("/资料/渠道/海鼎POS")
										}}</el-radio>
                </el-row>
                <el-row style="height: 30px;">
                  <el-radio :label="false" style="margin-right: 0">{{ formatI18n("/资料/渠道/其他渠道")
										}}</el-radio>
                </el-row>
                <ChannelSelect @change="$refs.ruleForm.validateField('resource')" :disabled="ruleForm.useHdPosChannel" :hideHdPos="true"
                  v-model="ruleForm.resource">
                </ChannelSelect>
              </el-radio-group>
            </el-form-item>
            <el-form-item class="store_range" label="门店范围" prop="range" v-if="ruleForm.useHdPosChannel">
              <ActiveStore no-i18n ref="activeStore" :sameStore="false" v-model="ruleForm.range" @change="doStoreChange"> </ActiveStore>
            </el-form-item>
            <el-form-item :label="formatI18n('/营销/积分活动/积分活动/商品满额加送积分/新建页面/活动信息/参与会员等级')" :required="true">
              <SelectGrade :disabled="disabled" ref="selectGrade" :selected="ruleForm.gradeRange" @change="gradeChange" />
            </el-form-item>
            <el-form-item :label="formatI18n('/营销/券礼包活动/券礼包活动/活动图片')">
              <el-row v-if="pictureUrls.length < 8">
                <el-upload :disabled="disabled" :headers="uploadHeaders" :action="uploadUrl" :with-credentials="true" :show-file-list="false" :on-success="onUploadSuccess"
                  :before-upload="beforeAvatarUpload">
                  <el-button v-show="!disabled" type="primary">{{ formatI18n('/储值/预付卡/卡模板/编辑页面/上传') }}</el-button>
                </el-upload>
              </el-row>
              <el-row style="line-height: 35px"><i class="el-icon-warning" />
                {{ formatI18n('/储值/会员储值/储值充值活动/编辑页面',
										'最多可维护8张图片，图片比例支持4:3（建议尺寸800*600像素），支持jpg/jpeg/png，大小不超过300KB') }}
              </el-row>
              <el-row>
                <CardPicList :readonly="disabled" :picList="pictureUrls" />
              </el-row>
            </el-form-item>
            <el-form-item label="活动说明">
              <el-input :disabled="disabled" v-model="ruleForm.remark" type="textarea" placeholder="请输入不超过500个字" :rows="10" maxlength="500" style="width: 500px;" />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <div class="setting-container">
      <div class="setting-block">
        <div class="section-title">充值赠礼</div>
        <div class="coupon-panel" v-for="(item, index) in faceAmount" :key="index">
          <div class="desc">
            <i18n k="/储值/会员储值/储值充值活动/编辑页面/充值面额{0}元，售价{1}元">
              <template slot="0"> {{ item }}</template>
              <template slot="1">
                <span v-if="ruleForm.resource === '门店'">{{ item }}</span>
                <el-input v-if="ruleForm.resource !== '门店'" :disabled="ruleForm.useHdPosChannel" v-model="saleAmountArray[index]"
                  style="width: 100px" @blur="doSaleAmountChange(index)"></el-input>
              </template>
            </i18n>
            <el-button key="1" @click="doToggle(index)" class="right-btn" v-if="faceAmountFlag[index]">收起设置
            </el-button>
            <el-button key="2" @click="doToggle(index)" class="right-btn" v-if="!faceAmountFlag[index]">展开设置
            </el-button>
          </div>
          <div v-if="faceAmountFlag[index]">
            <div style="border-bottom: 1px solid #cccccc;margin-right: 20px;margin-bottom: 10px"></div>
            <form-item label="赠礼设置">
              <div class="margin-bottom-10">
                <el-checkbox :disabled="disabled" v-model="selecteArray[index][0]"></el-checkbox>
                <i18n k="/储值/会员储值/储值充值活动/编辑页面/赠送积分{0}个">
                  <template slot="0">
                    &nbsp;&nbsp;
                    <el-input v-model="score[index][0]" @change="doScoreChange(index, 0)" style="width: 80px" :disabled="disabled || !selecteArray[index][0]">
                    </el-input>&nbsp;&nbsp;
                  </template>
                </i18n>
              </div>
              <div class="margin-bottom-10">
                <el-checkbox :disabled="disabled || saleAmountArray[index] != item" v-model="selecteArray[index][1]"></el-checkbox>
                <i18n k="/储值/会员储值/储值充值活动/编辑页面/赠送返现{0}元">
                  <template slot="0">
                    &nbsp;&nbsp;
                    <el-input v-model="score[index][1]" @change="doScoreChange(index, 1)" style="width: 80px" :disabled="disabled || !selecteArray[index][1] || saleAmountArray[index] != item">
                    </el-input>&nbsp;&nbsp;
                  </template>
                </i18n>
              </div>
              <div class="margin-bottom-10" v-for="(sub, subIndex) in receiveParamsArray[index]" :key="subIndex">
                <el-checkbox :disabled="disabled" v-if="subIndex === 0" v-model="selecteArray[index][2]" @change="doSendCouponChange(index,$event)"></el-checkbox>
                <i18n advance k="/储值/会员储值/储值充值活动/编辑页面/赠优惠券{0}张">
                  <template slot-scope="{ items }">
                    <span :style="{ marginLeft: subIndex === 0 ? '0px' : '48px' }">{{
												items.s0.prefix }}</span>&nbsp;&nbsp;
                    <el-input v-if="receiveParamsArray[index] && receiveParamsArray[index].length > 0" v-model="score[index][subIndex + 2]"
                      @change="doScoreChange(index, subIndex + 2)" style="width: 80px" :disabled="disabled || !selecteArray[index][2]">
                    </el-input>&nbsp;&nbsp; <span v-if="receiveParamsArray[index] && receiveParamsArray[index].length > 0">{{
													items.s0.suffix }}</span>&nbsp;&nbsp; &nbsp;&nbsp;
                    <el-button v-if="receiveParamsArray[index] && receiveParamsArray[index].length === 1 && !receiveParamsArray[index][0]" type="text"
                      @click="doAddCoupon(index)" :disabled="disabled || !selecteArray[index][2]">
                      + {{ i18n("添加优惠券") }}
                    </el-button>
                    &nbsp;&nbsp;
                  </template>
                </i18n>
                <a :title="receiveNameArray[index][subIndex]" type="text"
                  style="width: 100px;cursor: pointer;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;display: inline-block;position: relative;top: 4px;"
                  @click="doEditStoreValueActive(index, subIndex)">{{ receiveNameArray[index][subIndex] }}</a>
                &nbsp;&nbsp;
                <el-button v-if="receiveNameArray[index].length > 0 && !disabled" @click="doDeleteCoupon(index, subIndex)" type="text" style="color: red">删除
                </el-button>
                &nbsp;&nbsp;
                <el-button v-if="subIndex === receiveParamsArray[index].length - 1 && receiveNameArray[index].length > 0 && !disabled" type="text"
                  @click="doAddCoupon(index, subIndex)">添加</el-button>
              </div>

              <div class="margin-bottom-10">
                <el-checkbox :disabled="disabled" v-model="selecteArray[index][3]"></el-checkbox>
                <!-- <i18n k="/储值/会员储值/储值充值活动/编辑页面/赠付费会员卡">
                  <template slot="0">
                    &nbsp;&nbsp;
                    <el-input v-model="score[index][0]" @change="doScoreChange(index, 0)" style="width: 80px" :disabled="disabled || !selecteArray[index][0]">
                    </el-input>&nbsp;&nbsp;
                  </template>
                </i18n> -->
                <span style="left: -5px;display: inline-block;">
                    {{ i18n('赠付费会员卡') }}
                    <span v-if="paidBenefitCards[index] && paidBenefitCards[index].length > 0">
                      <template v-if="paidBenefitCards[index][0] && paidBenefitCards[index][0].benefitCardTemplate">
                        <el-button :disabled="disabled"  @click="openBenefitCard(index)"  type="text"  style="margin-left: 10px;margin-right: 10px;">{{ paidBenefitCards[index][0].benefitCardTemplate.name }}</el-button>
                        <template v-if="paidBenefitCards[index][0].payRule">
                          <span style="margin-left: 10px">{{ paidBenefitCards[index][0].payRule.value }}</span>
                          <span v-if="paidBenefitCards[index][0].payRule">{{ paidBenefitCards[index][0].payRule.type | dateTypeName }}</span>
                        </template>
                        <el-button :disabled="disabled"  type="text" style="color: red" @click="deletePaidBenefitCards(index)">
                          {{ i18n('/公用/按钮/删除') }}
                        </el-button>
                      </template>
                    </span>
                    <el-button v-else  @click="openBenefitCard(index)" type="text" icon="el-icon-plus" :disabled="disabled || !selecteArray[index][3]" >{{ i18n('/会员/会员资料/添加付费会员卡') }}</el-button>
                </span>
              </div>
            </form-item>
            <form-item label="充值说明" style="padding-bottom: 20px">
              <el-input type="textarea" :disabled="disabled || !canEditRemark" :maxlength="25" v-model="depositRemarks[index]" placeholder="请输入不超过25个字"
                style="width: 220px"></el-input>
            </form-item>
            <form-item label="充值图片" style="padding-bottom: 20px">
              <el-row v-if="!linePictureUrl[index]">
                <el-upload :before-remove="((file, fileList) => { onRemove(file, fileList, index) })" :headers="uploadHeaders" :action="uploadUrl"
                  :with-credentials="true" :show-file-list="false"
                  :on-success="((response, file, fileList) => { onLineUploadSuccess(response, file, fileList, index) })"
                  :before-upload="beforeAvatarUpload">
                  <el-button v-show="!disabled" type="primary">{{ formatI18n('/储值/预付卡/卡模板/编辑页面/上传') }}</el-button>
                </el-upload>
              </el-row>
              <el-row style="line-height: 35px"><i class="el-icon-warning" />
                {{ formatI18n('/储值/会员储值/储值充值活动/编辑页面',
										'图片比例支持4:3（建议尺寸800*600像素），支持jpg/jpeg/png，大小不超过300KB') }}
              </el-row>
              <el-row style="line-height: 35px">
                <CardPicList :readonly="disabled" :picList="linePictureUrl[index] ? [linePictureUrl[index]] : []" @delete="onRemove(index)" />
              </el-row>
            </form-item>
            <form-item :label="formatI18n('/储值/会员储值/储值充值活动/编辑页面/C端角标')" style="padding-bottom: 20px">
              <el-row v-if="!lineCornerImageUrl[index]">
                <el-upload :before-remove="((file, fileList) => { onCornerRemove(file, fileList, index) })" :headers="uploadHeaders" :action="uploadUrl"
                  :with-credentials="true" :show-file-list="false"
                  :on-success="((response, file, fileList) => { onLineCornerUploadSuccess(response, file, fileList, index) })"
                  :before-upload="beforeAvatarUpload">
                  <el-button v-show="!disabled" type="primary">{{ formatI18n('/储值/预付卡/卡模板/编辑页面/上传') }}</el-button>
                </el-upload>
              </el-row>
              <el-row style="line-height: 35px"><i class="el-icon-warning" />
                {{ formatI18n('/储值/会员储值/储值充值活动/编辑页面',
										'图片比例支持5:2（建议尺寸70*28像素），支持jpg/jpeg/png，大小不超过300KB') }}
              </el-row>
              <el-row style="line-height: 35px">
                <CardPicList :readonly="disabled" :picList="lineCornerImageUrl[index] ? [lineCornerImageUrl[index]] : []" @delete="onCornerRemove(index)" />
              </el-row>
            </form-item>
          </div>
        </div>
      </div>
    </div>
    <!-- 营销预算 -->
    <MarketingBudgetEdit :disabled="disabled" v-model="budget" ref="marketingBudget" activityType="PrepayDepositActivityRule">
    </MarketingBudgetEdit>

    <!--选择门店弹框-->
    <SelectStoreDialog no-i18n title="设置活动门店" :dialogShow="dialogShow" @summit="doSummit" @dialogClose="doDialogClose">
    </SelectStoreDialog>
    <!--门店导入的dialog-->
    <ImportDialog no-i18n @upload-success="doUploadSuccess" @dialogClose="doImportDialogClose" :importUrl="importUrl" :templatePath="templatePath"
      templateName="门店模板" :dialogShow="importDialogShow" title="导入">
    </ImportDialog>
    <CouponTemplateSelectorDialog ref="couponTemplate" :filter="cardTemplateFilter" @summit="doCardTemplateSelected">
    </CouponTemplateSelectorDialog>
    <SelectStoreActiveDtlDialog :child="child" :dialogShow="couponDialogShow" @dialogClose="doCouponDialogClose">
    </SelectStoreActiveDtlDialog>
    <ImportResultDialog no-i18n :dialogShow="importResultDialogClose" :data="importResultData" @importResultDialogClose="doImportResultDialogClose">
    </ImportResultDialog>
    <ExportConfirm :dialogShow="exportDialogShow" :warnMsg="warnMsg" :confirmBtnMsg="formatI18n('/公用/按钮', '审核')"
      :checkMsg="formatI18n('/储值/会员储值/储值充值活动/新建界面/存在冲突活动/点击保存并审核/弹框提示', '已知晓冲突活动，并确认继续执行审核')" @dialogClose="doExportDialogClose"
      @summit="doConfirmSummit"></ExportConfirm>
    <!-- 充值赠礼，赠送付费会员卡和权益卡选择弹窗 -->
    <SelectBenefitCardDialog v-if="paidBenefitCards && paidBenefitCards.length>0" ref="selectBenefitCardDialogRef"></SelectBenefitCardDialog>
  </div>
</template>

<script lang="ts" src="./StoreValueActiveAdd.ts"></script>

<style lang="scss">
.store-value-active-add {
  width: 100%;

  .content-inner {
    margin-top: 20px;
  }

  .coupon-panel {
    position: relative;
    background: rgba(249, 249, 249, 1);
    padding-left: 20px;
    margin-top: 20px;

    .desc {
      height: 60px;
      line-height: 60px;

      .right-btn {
        position: absolute;
        right: 20px;
        top: 16px;
      }
    }

    .margin-bottom-10 {
      margin-bottom: 20px;
    }
  }

  .input-width {
    width: 320px !important;
  }

  .divide {
    height: 10px;
    background-color: grey;
    opacity: 0.2;
  }

  //.store_range{
  //    .el-form-item__label{
  //        width: 80px !important;
  //    }
  //}
}
</style>
