<template>
	<div class="sales-cards-report">
		<div class="current-page">
			<el-form label-width="150px">
				<el-row  class="query">
					<TimeRange no-i18n @submit="handleTimeRange" ref="timeRange"></TimeRange>
				</el-row>
				<el-row  class="query" style="margin-top: 8px">
					<el-col :span="8">
						<form-item label="卡模板号">
							<el-input placeholder="类似于" v-model="query.templateNumberLikes" />
						</form-item>
					</el-col>
					<el-col :span="8">
						<form-item label="卡模板">
							<el-input placeholder="类似于" v-model="query.templateNameLikes" />
						</form-item>
					</el-col>
				</el-row>
				<el-row  class="query" style="margin-top: 8px">
					<el-col :span="8">
						<form-item label=" ">
							<el-button class="btn-search" type="primary" @click="doSearch">查询</el-button>
							<el-button class="btn-reset" @click="doReset">重置</el-button>
						</form-item>
					</el-col>
				</el-row>
			</el-form>
			<hr />
			<el-row class="table">
				<el-table :data="queryData" style="width: 100%;margin-top: 20px">
					<div slot="empty">
						<p>暂无数据</p>
					</div>
					<el-table-column label="卡模板号" prop="templateNumber" width="150">
						<template slot-scope="scope">
							<span :title="scope.row.templateNumber">{{ scope.row.templateNumber }}</span>
						</template>
					</el-table-column>
					<el-table-column label="卡模板" width="150" prop="templateName"> </el-table-column>
					<el-table-column label="期初余额" width="150" prop="beginningBalance"> </el-table-column>
					<el-table-column label="期初实充" width="180" prop="beginningAmount"> </el-table-column>
					<el-table-column label="期初返现" width="150" prop="beginningGiftAmount"> </el-table-column>
					<el-table-column label="期末余额" prop="endingBalance" width="200"> </el-table-column>
					<el-table-column label="期末实充" prop="endingAmount" width="150"> </el-table-column>
					<el-table-column label="期末返现" prop="endingGiftAmount" width="200"> </el-table-column>
					<el-table-column label="差异" width="150" prop="different"></el-table-column>					
					<el-table-column label="售卡" width="150" prop="sale"></el-table-column>
					<el-table-column label="售卡实充" width="150" prop="saleAmount"></el-table-column>
					<el-table-column label="售卡返现" width="150" prop="saleGiftAmount"></el-table-column>
					<el-table-column label="消费" width="150" prop="pay"></el-table-column>
					<el-table-column label="消费实充" width="150" prop="payAmount"></el-table-column>
					<el-table-column label="消费返现" width="150" prop="payGiftAmount"></el-table-column>
					<el-table-column label="退款" width="150" prop="payRefund"></el-table-column>
					<el-table-column label="退款实充" width="150" prop="payRefundAmount"></el-table-column>
					<el-table-column label="退款返现" width="150" prop="payRefundGiftAmount"></el-table-column>
					<el-table-column label="作废" width="150" prop="cancel"></el-table-column>
					<el-table-column label="作废实充" width="150" prop="cancelAmount"></el-table-column>
					<el-table-column label="作废返现" width="150" prop="cancelGiftAmount"></el-table-column>
					<el-table-column label="退卡" width="150" prop="saleRefund"></el-table-column>
					<el-table-column label="退卡实充" width="200" prop="saleRefundAmount"></el-table-column>
					<el-table-column label="退卡返现" width="150" prop="saleRefundGiftAmount"></el-table-column>
					<el-table-column label="调整" width="150" prop="adjust"></el-table-column>
					<el-table-column label="调整实充" width="150" prop="adjustAmount"></el-table-column>
					<el-table-column label="调整返现" width="150" prop="adjustGiftAmount"></el-table-column>
				<!-- 新增字段 -->
					<el-table-column label="未激活余额" width="150" prop="inactiveTotalBalance"></el-table-column>
					<el-table-column label="未激活实充" width="150" prop="inactiveBalance"></el-table-column>										
					<el-table-column label="未激活赠送" width="150" prop="inactiveGiftBalance"></el-table-column>
					<el-table-column label="转赠中余额" width="150" prop="presentingTotalBalance"></el-table-column>
					<el-table-column label="转赠中实充" width="150" prop="presentingBalance"></el-table-column>
					<el-table-column label="转赠中赠送" width="150" prop="presentingGiftBalance"></el-table-column>
					<el-table-column label="使用中余额" width="150" prop="usingTotalBalance"></el-table-column>
					<el-table-column label="使用中实充" width="150" prop="usingBalance"></el-table-column>
					<el-table-column label="使用中赠送" width="150" prop="usingGiftBalance"></el-table-column>
					<el-table-column label="已冻结余额" width="150" prop="frozenTotalBalance"></el-table-column>
					<el-table-column label="已冻结实充" width="150" prop="frozenBalance"></el-table-column>
					<el-table-column label="已冻结赠送" width="150" prop="frozenGiftBalance"></el-table-column>
				</el-table>
			</el-row>
			<el-pagination
				no-i18n
				:current-page="page.currentPage"
				:page-size="page.size"
				:page-sizes="[10, 20, 30, 40]"
				:total="page.total"
				@current-change="onHandleCurrentChange"
				@size-change="onHandleSizeChange"
				background
				layout="total, prev, pager, next, sizes,  jumper"
				class="pagin"
			></el-pagination>
		</div>
	</div>
</template>

<script lang="ts" src="./DailyReport.ts"></script>

<style lang="scss">
.sales-cards-report {
	background-color: white;
	height: 100%;
	width: 100%;
	overflow: auto;
	.total {
		margin: 20px;
	}

	.current-page {
		height: calc(100% - 150px);
		padding: 0 20px 20px 20px;
		.el-select {
			width: 100%;
		}

		.query {
			.el-form-item {
				margin-bottom: 0;
			}
		}

		.pagin {
			margin-top: 25px;
		}

		.el-col {
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}

		.cell {
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}

		.el-form-item__label {
			//overflow: hidden;
			//text-overflow: ellipsis;
			white-space: normal;
		}
	}
}
</style>
