

// 拼团记录
import {State} from "model/promotion/groupBookingActivity/State";

export default class GroupBookingRecord {
  // uuid
  uuid: Nullable<string> = null
  // 版本
  version: Nullable<number> = null
  // 活动号
  activityNumber: Nullable<string> = null
  // 团号
  groupNumber: Nullable<string> = null
  // 状态
  state: Nullable<State> = null
  // 团主
  owner: Nullable<string> = null
  // 团主昵称
  ownerNickName: Nullable<string> = null
  // 开团门店Id
  orgId: Nullable<string> = null
  // 开团门店名称
  orgName: Nullable<string> = null
  // 开团限制人数
  memberLimit: Nullable<string> = null
  // 开团时间
  beginTime: Nullable<Date> = null
  // 关团时间
  endTime: Nullable<Date> = null
  // 成团时间
  successTime: Nullable<Date> = null
  // 已参团人数
  joinedMemberCount: Nullable<number> = null
  // 会员标识
  memberCode: Nullable<string> = null
  // 团队最多人数
  succeedMemberLimit: Nullable<number> = null
}