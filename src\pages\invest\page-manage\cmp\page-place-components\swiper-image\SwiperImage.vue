<template>
  <div
    class="template-rotation"
    :style="{ padding: localProperty.styMarginTop + 'px ' + localProperty.styMarginRight + 'px ' + localProperty.styMarginBottom + 'px ' + localProperty.styMarginLeft + 'px' }"
    :class="[{ activeCom: activeIndex === index }]"
    @click="activeTemplate"
  >
    <div class="toolBar" v-if="activeIndex === index">
      <setting-toolbar :btns="toolBarBtns" @click="toolbarClick"></setting-toolbar>
    </div>
    <el-carousel height="200px" class="carousel-box" v-if="flag">
      <el-carousel-item v-for="item in imgList" :key="item.id">
        <el-image style="width: 100%;" :src="item.imageUrl ? item.imageUrl : imgUrl" fit="contain"></el-image>
      </el-carousel-item>
    </el-carousel>

    <!-- <div
      class="image-widget"
      v-if="localProperty.propImages && localProperty.propImages.length > 0"
    >
      <img :src="localProperty.propImages[currentIndex].imageUrl" class="image-widget-img" />
      <div class="index-border">
        <div
          class="image-index"
          v-for="(item, index) in localProperty.propImages"
          :key="index"
          @click="selectImage(i)"
        ></div>
      </div>
    </div>
    <div class="image-widget" v-else>
      <img :src="imgUrl" style="width: 100%;height: 100%;object-fit: contain;" />
    </div> -->
  </div>
</template>

<script lang="ts" src="./SwiperImage.ts"></script>

<style lang="scss" scoped>
.carousel-box {
  width: 100%;
  overflow: hidden;
  ::v-deep.el-carousel__indicators {
    width: 200px;
    .el-carousel__indicator {
      .el-carousel__button {
        opacity: 0 !important;
      }
    }
  }
}

.template-rotation {
  width: 100%;
  position: relative;
  box-sizing: border-box;
  // &-img {
  //   width: 100%;
  //   object-fit: contain;
  // }
  .toolBar {
    position: absolute;
    top: 0;
    right: 1px;
    z-index: 999;
    cursor: pointer;
  }
  .image-widget {
    width: 100%;
    height: 100%;
    position: relative;

    &-img {
      width: 100%;
      display: block;
    }

    .index-border {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: center;
      position: absolute;
      bottom: 20px;

      .image-index {
        position: relative;
        margin-left: 2px;
        margin-right: 2px;
        border: ghostwhite 1px solid;
        width: 16px;
        height: 4px;
        background-color: #fff;
        border-radius: 2px;
        opacity: 0.6;
      }
    }
  }
}
.activeCom {
  border: 2px solid #4d63ec;
}
.el-carousel__item h3 {
  color: #475669;
  font-size: 14px;
  opacity: 0.75;
  line-height: 200px;
  margin: 0;
}
</style>
