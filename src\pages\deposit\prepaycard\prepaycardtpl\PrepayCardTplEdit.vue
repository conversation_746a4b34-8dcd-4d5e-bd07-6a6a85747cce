<template>
  <div class="cardtpl-edit-view">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button type="primary" @click="save">{{ formatI18n('/储值/预付卡/卡模板/编辑页面/保存') }}
        </el-button>
        <el-button @click="$router.go(-1)">{{ formatI18n('/公用/按钮', '取消') }}</el-button>
      </template>
      <template slot="warning">
        <div class="edit-warning">{{i18n('卡模板修改后，已发出的卡的卡面额、次数、有效期、支付时是否要密码、转出时是否要密码，仍以原来的为准，是否一次性消费、适用商品和适用门店以修改后的为准')}}</div>
      </template>
    </BreadCrume>
    <div class="current-page">
      <el-form :model="detail" :rules="rules" ref="detail" label-width="170px">
        <div class="panel">
          <div class="header">
            {{ formatI18n('/储值/预付卡/卡模板/编辑页面/基础设置') }}
          </div>
          <div class="content">
            <el-form-item :label="formatI18n('/储值/预付卡/卡模板/编辑页面/卡模板号')" :required="true" prop="number">
              <el-input type="text" style="width:298px" :disabled="editType === 'edit'" v-model="detail.number" :placeholder="i18n('请输入数字，也可由系统自动填充')"
                maxlength="80" />&nbsp;&nbsp;
              <el-button type="text" @click="generateNumber" v-if="editType !== 'edit'">
                {{ formatI18n('/储值/预付卡/卡模板/编辑页面/自动填充') }}
              </el-button>
            </el-form-item>
            <el-form-item :label="formatI18n('/储值/预付卡/卡模板/编辑页面/卡模板名称')" :required="true" prop="name">
              <el-input style="width:298px" v-model="detail.name" :placeholder="formatI18n('/储值/预付卡/卡模板/编辑页面/请输入不超过20个字')" maxlength="20" />
            </el-form-item>
            <el-form-item v-if="isShowLength" :label="i18n('卡号长度')" :required="true" prop="cardCodeLength">
              <AutoFixInput :min="6" :max="20" :fixed="0" :placeholder="i18n('请输入6-20之间的整数')" v-model="detail.cardCodeLength" style="width:186px"
                :appendTitle="i18n('位')">
              </AutoFixInput>
            </el-form-item>
            <el-form-item :label="i18n('卡号规则')" prop="prefix">
              <el-input :placeholder="i18n('请输入{0}位以内的数字或字母').replace(/\{0\}/g,4)" maxlength="4" style="width: 325px" v-model="detail.codePrefix"
                v-if="editType !== 'edit'">
              </el-input>
              <div v-else>{{detail.codePrefix || '--'}}</div>
            </el-form-item>
            <el-form-item :label="formatI18n('/储值/预付卡/卡模板/编辑页面/账户类型')" :required="true" v-if="enableMultipleAccount" prop="accountType">
              <div style="line-height: 35px">
                <el-radio-group v-model="detail.accountType">
                  <el-radio v-for="account of accounts" :label="account" :key="account.id">[{{account.id}}]
                    {{account.name}}
                  </el-radio>
                  <span style="color: red">{{ formatI18n('/储值/预付卡/卡模板/编辑页面/必填') }}</span>
                </el-radio-group>
              </div>
              <div><i class="el-icon-warning" /> {{ formatI18n('/储值/预付卡/卡模板/编辑页面/只有账户类型一致的账户间才允许转账') }}</div>
            </el-form-item>
            <el-form-item :label="formatI18n('/储值/预付卡/卡模板/编辑页面/卡类型')" :required="true" prop="cardTemplateType">
              <el-row style="line-height: 40px">
                <el-radio-group :disabled="editType === 'edit'" v-model="detail.cardTemplateType" @change="onTypeChange">
                  <el-radio label="GIFT_CARD" v-if="hasOptionPermission('/卡/卡管理/卡模板/礼品卡', '卡模板维护')">
                    {{ i18n('礼品卡') }}
                  </el-radio>
                  <el-radio label="IMPREST_CARD" v-if="hasOptionPermission('/卡/卡管理/卡模板/充值卡', '卡模板维护')">
                    {{ formatI18n('/储值/预付卡/卡模板/公共/卡类型/充值卡') }}
                  </el-radio>
                  <el-radio label="RECHARGEABLE_CARD" v-if="hasOptionPermission('/卡/卡管理/卡模板/储值卡', '卡模板维护')">
                    {{ formatI18n('/储值/预付卡/卡模板/公共/卡类型/储值卡') }}
                  </el-radio>
                  <el-radio label="COUNTING_CARD" v-if="hasOptionPermission('/卡/卡管理/卡模板/次卡', '卡模板维护')">
                    {{ i18n('次卡') }}
                  </el-radio>
                  <span style="color: red">{{ formatI18n('/储值/预付卡/卡模板/编辑页面/必填') }}</span>
                </el-radio-group>
              </el-row>
              <el-row style="line-height: 35px">
                <i class="el-icon-warning" /> {{ formatI18n('/储值/预付卡/卡模板/编辑页面/各卡类型业务限制如下：') }}
              </el-row>
              <el-row style="width: 471px">
                <el-table :data="bizTable" border>
                  <el-table-column prop="name" label="" fixed width="150">
                  </el-table-column>
                  <el-table-column prop="pay" :label="formatI18n('/储值/预付卡/卡模板/编辑页面/消费支付')" fixed width="80">
                    <template slot-scope="scope">
                      <el-row style="text-align: center">
                        <span v-if="scope.row.pay" style="color:green">√</span>
                        <span v-if="!scope.row.pay" style="color:red">×</span>
                      </el-row>
                    </template>
                  </el-table-column>
                  <el-table-column prop="encharge" :label="formatI18n('/储值/预付卡/卡模板/编辑页面/被充值')" fixed width="80">
                    <template slot-scope="scope">
                      <el-row style="text-align: center">
                        <span v-if="scope.row.encharge" style="color:green">√</span>
                        <span v-if="!scope.row.encharge" style="color:red">×</span>
                      </el-row>
                    </template>
                  </el-table-column>
                  <el-table-column prop="out" :label="formatI18n('/储值/预付卡/卡模板/编辑页面/余额转出')" fixed width="80">
                    <template slot-scope="scope">
                      <el-row style="text-align: center">
                        <span v-if="scope.row.out" style="color:green">√</span>
                        <span v-if="!scope.row.out" style="color:red">×</span>
                      </el-row>
                    </template>
                  </el-table-column>
                  <el-table-column prop="in" :label="formatI18n('/储值/预付卡/卡模板/编辑页面/余额转入')" fixed width="80">
                    <template slot-scope="scope">
                      <el-row style="text-align: center">
                        <span v-if="scope.row.in" style="color:green">√</span>
                        <span v-if="!scope.row.in" style="color:red">×</span>
                      </el-row>
                    </template>
                  </el-table-column>
                </el-table>
              </el-row>
            </el-form-item>
            <el-form-item :label="i18n('卡介质')" :required="true" prop="cardMedium">
              <div style="line-height: 40px">
                <el-radio-group v-model="detail.cardMedium" :disabled="editType === 'edit'">
                  <el-radio label="online" v-if="hasCardMediumPermission('online')">{{i18n('电子卡')}}</el-radio>
                  <el-radio label="bar" v-if="hasCardMediumPermission('bar')">{{i18n('条码卡')}}</el-radio>
                  <el-radio label="mag" v-if="hasCardMediumPermission('mag')">{{i18n('磁条卡')}}</el-radio>
                  <el-radio label="rfic" v-if="hasCardMediumPermission('rfic')">{{i18n('rfic卡')}}</el-radio>
                  <el-radio label="ic" v-if="hasCardMediumPermission('ic')">{{i18n('ic卡')}}</el-radio>
                </el-radio-group>
              </div>
            </el-form-item>
          </div>
        </div>

        <div class="split"></div>

        <div class="panel">
          <div class="header">
            {{ formatI18n('/储值/预付卡/卡模板/编辑页面/卡面信息与使用规则') }}
          </div>
          <div class="content">
            <el-form-item :label="formatI18n('/储值/预付卡/卡模板/编辑页面/卡样')" prop="cardPictureUrls">
              <el-row class="gray-tips" style="line-height: 40px">
                {{ i18n('只能上传1张图片，图片比例支持4:3（建议尺寸800*600像素），支持jpg/jpeg/png/gif，大小不超过300KB') }}
              </el-row>
              <el-row v-if="detail.cardPictureUrls.length < 1">
                <el-upload :headers="uploadHeaders" :action="uploadUrl" :with-credentials="true" :show-file-list="false" :on-success="onUploadSuccess"
                  :before-upload="beforeAvatarUpload">
                  <el-button style="width:80px">{{ formatI18n('/储值/预付卡/卡模板/编辑页面/上传') }}</el-button>
                </el-upload>
              </el-row>
              <el-row>
                <CardPicList :picList="detail.cardPictureUrls" />
              </el-row>
            </el-form-item>
            <template v-if="detail.cardTemplateType !== 'COUNTING_CARD'">
              <el-form-item :label="formatI18n('/储值/预付卡/卡模板/编辑页面/卡面额')" :required="true" prop="faceAmounts">
                <div class="cardDeno">
                  <el-row v-for="(amount, index) of detail.faceAmounts" :key="index" style="height: 40px">
                    <!-- <el-input style="width:150px" v-model="detail.faceAmounts[index]" type="number" min="0" @change="doChangeFaceAmount(amount)">
                      <template slot="append">{{formatI18n('/储值/预付卡/卡模板/编辑页面/元')}}</template>
                    </el-input> -->
                    <AutoFixInput :min="0" :max="99999999.00" :disabled="editType === 'edit'" :fixed="2" v-model="detail.faceAmounts[index]"
                      @blur="doChangeFaceAmount" style="width:150px" :appendTitle="i18n('元')">
                    </AutoFixInput>
                    <!-- <el-button type="text" @click="deleteFaceAmount(index)" v-if="detail.faceAmounts.length !== 1">
                      {{ formatI18n('/公用/按钮/删除') }}
                    </el-button>
                    <el-button type="text" @click="addFaceAmount()" v-if="index === detail.faceAmounts.length - 1 && index < 9">
                      {{ formatI18n('/储值/预付卡/卡模板/编辑页面/+添加') }}
                    </el-button> -->
                  </el-row>
                  <!-- <div><i class="el-icon-warning" /> {{ formatI18n('/储值/预付卡/卡模板/编辑页面/最多可添加10个卡面额') }}</div> -->
                </div>
              </el-form-item>
            </template>
            <template v-else>
              <el-form-item :label="i18n('次数')" :required="true" prop="count">
                <AutoFixInput :min="1" :max="99999999" :fixed="0" :disabled="editType === 'edit'" v-model="detail.count" style="width:132px"
                  :appendTitle="i18n('次')">
                </AutoFixInput>
              </el-form-item>
            </template>
            <el-form-item :label="i18n('价格')" :required="true" prop="price">
              <div class="gray-tips">{{i18n('价格即卡原始零售价')}}</div>
              <AutoFixInput :min="0" :max="99999999" :fixed="2" v-model="detail.price" style="width:150px" :appendTitle="i18n('元')">
              </AutoFixInput>
            </el-form-item>
            <el-form-item :label="formatI18n('/储值/预付卡/卡模板/编辑页面/有效期')">
              <el-row style="line-height: 35px;margin-bottom:10px">
                <el-radio label="RALATIVE" v-model="validityInfo.type" @input="doRelativeChange">
                  <span style="font-size: 14px">
                    <i18n advance k="/储值/预付卡/卡模板/编辑页面/激活后{0}内有效" v-if="detail.cardMedium === 'online' && detail.cardTemplateType === 'GIFT_CARD'">
                      <template slot-scope="{items}">{{items.s0.prefix}}</template>
                    </i18n>
                    <i18n advance k="/储值/预付卡/卡模板/编辑页面/发售后{0}内有效" v-else>
                      <template slot-scope="{items}">{{items.s0.prefix}}</template>
                    </i18n>
                  </span>
                </el-radio>
                <el-form-item prop="days" style="display:inline-block">
                  <AutoFixInput :min="1" :max="999999" @change="checkValidityInfoNum(validityInfo)" :fixed="0"
                    :placeholder="formatI18n('/储值/预付卡/卡模板/编辑页面/请输入数字')" v-model="validityInfo.num" style="width:180px">
                  </AutoFixInput>
                </el-form-item>&nbsp;&nbsp;
                <el-select style="width: 80px" v-model="validityInfo.unit">
                  <el-option :label="formatI18n('/储值/预付卡/卡模板/编辑页面/年')" value="年" />
                  <el-option :label="formatI18n('/储值/预付卡/卡模板/编辑页面/天')" value="天" />
                </el-select>&nbsp;&nbsp;
                <i18n advance k="/储值/预付卡/卡模板/编辑页面/激活后{0}内有效">
                  <template slot-scope="{items}">{{items.s0.suffix}}</template>
                </i18n>
              </el-row>
              <el-row style="line-height: 45px">
                <el-radio label="FIXED" v-model="validityInfo.type" @input="doFixedChange">
                  <span
                    v-if="detail.cardMedium === 'online' && detail.cardTemplateType === 'GIFT_CARD'">{{ formatI18n('/储值/预付卡/卡模板/编辑页面/自激活起有效期至') }}</span>
                  <span v-else>{{ formatI18n('/储值/预付卡/卡模板/编辑页面/自发售起有效期至') }}</span>
                </el-radio>
                <el-form-item prop="timeRange" style="display:inline-block">
                  <el-date-picker type="date" :picker-options="pickerOptions" :placeholder="formatI18n('/储值/预付卡/卡模板/编辑页面/选择日期')"
                    v-model="validityInfo.endDate">
                  </el-date-picker>
                </el-form-item>
              </el-row>
            </el-form-item>
            <el-form-item :label="i18n('用卡门槛')" v-if="detail.cardTemplateType === 'RECHARGEABLE_CARD' && showCardExtRule && detail.extInfo">
              {{i18n('用卡商品满')}}
              <AutoFixInput :min="0" :max="99999.99" :fixed="2" v-model="detail.extInfo.threshold.threshold" @change="handleThresholdChange"
                style="width:150px" :appendTitle="i18n('元')">
              </AutoFixInput>
              {{i18n('及以上可用')}}
              <span class="gray-tips" style="margin-left:16px">{{i18n('不填表示无门槛')}}</span>
            </el-form-item>
            <el-form-item :label="formatI18n('/储值/预付卡/卡模板/编辑页面/适用商品')" v-if="detail.cardTemplateType !== 'IMPREST_CARD' && !isCardAttributeFix"
              prop="useGoods">
              <template v-if="detail.cardTemplateType === 'COUNTING_CARD'">
                <ChooseGoodsRange ref="chooseGoodsRange" v-model="goodsRange.exchangeGoods" :canAddCategory="false" type="PART"
                  @change="doUseCouponGoodsChange" :isImport="false" :goodsMatchRuleMode="goodsMatchRuleMode">
                </ChooseGoodsRange>
                <div class="flex" v-if="goodsRange.exchangeGoods.length">
                  <span>{{ formatI18n('/资料/门店', '共') + goodsRange.exchangeGoods.length + formatI18n('/资料/员工', '项') }}</span>
                  <el-select v-model="goodsRange.exchangeQty" style="margin: 0 10px">
                    <el-option v-for="(item) in canChooseItems" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                  <el-checkbox v-model="goodsRange.enableExchangeSameGood">{{i18n('可重复选')}}</el-checkbox>
                </div>
              </template>
              <GoodsScopeEx v-else v-model="detail.goods" ref="goodsScope" :goodsMatchRuleMode="goodsMatchRuleMode" />
            </el-form-item>
            <el-form-item :label="formatI18n('/储值/预付卡/卡模板/编辑页面/适用门店')" v-if="detail.cardTemplateType !== 'IMPREST_CARD' && !isCardAttributeFix"
              class="">
              <ActiveStore ref="storeScope" :isOldActivity="false" :enableStore="true" :sameStore="false" v-model="detail.stores">
              </ActiveStore>
            </el-form-item>
            <!-- 叠加促销 -->
            <!-- <template v-if="['COUNTING_CARD','IMPREST_CARD'].indexOf(detail.cardTemplateType) === -1">
              <StackCardPromotion v-model="promotionInfo" :editType="editType" :templateId="detail.number" ref="stackPromotion">
              </StackCardPromotion>
            </template> -->
            <!-- 叠加优惠 -->
            <template v-if="isGiftOrRechargeable">
              <StackPromotion  v-model="promotionInfo" :copyFlag="editType" :templateId="detail.number" promotionType="Card"  ref="stackPromotion"  @input="changeStackPromotion"></StackPromotion>
            </template>
            <el-form-item v-if="isGiftOrRechargeable" :label="formatI18n('/卡模板/支付是否得积分')" :required="true" prop="payObtainPoints">
              <el-radio-group v-model="detail.payObtainPoints">
                <el-radio :label="true">{{ formatI18n('/公用/券模板/是') }}</el-radio>
                <el-radio :label="false">{{ formatI18n('/公用/券模板/否') }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item :label="formatI18n('/储值/卡模板/是否允许绑为会员卡')" :required="true" prop="allowBind">
              <el-radio-group v-model="detail.allowBind">
                <el-radio :label="true">{{ formatI18n('/设置/系统设置/能') }}</el-radio>
                <el-radio :label="false">{{ formatI18n('/设置/系统设置/不能') }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item :label="i18n('支付时是否要输密码')" :required="true" v-if="isNeedPayPwd && !isCardAttributeFix" prop="enablePayPassword">
              <el-radio-group v-model="detail.enablePayPassword">
                <el-radio :label="true">{{ formatI18n('/储值/预付卡/卡模板/编辑页面/需要') }}</el-radio>
                <el-radio :label="false">{{ formatI18n('/储值/预付卡/卡模板/编辑页面/不需要') }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item :label="i18n('转出时是否需要密码')" :required="true" v-if="isNeedTransPwd && !isCardAttributeFix" prop="enableTransferOutPassword">
              <el-radio-group v-model="detail.enableTransferOutPassword">
                <el-radio :label="true">{{ formatI18n('/储值/预付卡/卡模板/编辑页面/需要') }}</el-radio>
                <el-radio :label="false">{{ formatI18n('/储值/预付卡/卡模板/编辑页面/不需要') }}</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item :label="formatI18n('/储值/预付卡/卡模板/编辑页面/是否一次性消费')" prop="oneConsumption"
              v-if="['RECHARGEABLE_CARD'].indexOf(detail.cardTemplateType) > -1">
              <el-radio-group v-model="detail.enableOneTimeConsume">
                <el-radio :label="false">{{ formatI18n('/储值/预付卡/卡模板/编辑页面/否') }}</el-radio>
                <el-radio :label="true">{{ formatI18n('/储值/预付卡/卡模板/编辑页面/是') }}</el-radio>
              </el-radio-group>
              <div><i class="el-icon-warning" /> {{ formatI18n('/储值/预付卡/卡模板/编辑页面/用于控制卡余额是否一笔交易里用完。如果设置是，则卡余额小于等于待支付金额才能用卡支付') }}</div>
            </el-form-item>

            <el-form-item :label="i18n('整单支付是否享受会员价')" prop="enjoyMembershipPrice"
              v-if="['RECHARGEABLE_CARD','GIFT_CARD'].indexOf(detail.cardTemplateType) > -1">
              <el-radio-group v-model="detail.enjoyMembershipPrice">
                <el-radio :label="false">{{ formatI18n('/储值/预付卡/卡模板/编辑页面/否') }}</el-radio>
                <el-radio :label="true">{{ formatI18n('/储值/预付卡/卡模板/编辑页面/是') }}</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item :label="formatI18n('/储值/预付卡/卡模板/编辑页面/使用须知')" prop="theme">
              <el-input v-model="detail.remark" type="textarea"
                :placeholder="formatI18n('/储值/预付卡/卡模板/编辑页面/请输入不超过500个字\n示例文本：\n1. 此卡自激活之日起有效期2年。\n2. 此卡仅限购买指定商品时使用。\n3. 此卡可在任意门店使用。\n4. 此卡可多次使用，不可兑换现金，不找零。')"
                :rows="10" maxlength="500" style="width: 500px;" />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts" src="./PrepayCardTplEdit.ts">
</script>

<style lang="scss">
.cardtpl-edit-view {
  width: 100%;
  height: 100%;
  background-color: white;
  overflow: auto;

  .current-page {
    height: calc(100% - 80px) !important;
    overflow-y: scroll;

    .panel {
      .header {
        font-weight: 500;
        padding: 20px 20px 0 20px;
        font-size: 18px;
      }

      .content {
        padding: 20px;
      }
    }

    .split {
      height: 20px;
      background-color: #eeeff1;
    }
  }
}
</style>
