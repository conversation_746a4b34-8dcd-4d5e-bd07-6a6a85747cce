export default class MetricProp {
  // 订单维度指标:frequency-总次数；days-天数；orderStdAmount-订单商品实付金额；orderFavAmount-订单商品优惠金额；orderGoodsQty-订单商品数量；orderGoodsItemQty-订单商品品项数；消费指标：frequency-总次数；days-天数；stdAmount-商品实付金额；favAmount-商品优惠金额；goodsQty-商品数量；goodsItemQty-商品品项数；
  prop: Nullable<string> = null
  // 逻辑运算：≥、＞、≤、＜、＝、区间为，默认：≥
  operator: Nullable<string> = null
  // 订单维度聚合类型:max-最大值;average-次均值
  aggType: Nullable<string> = null
  // 区间开始
  start: Nullable<number> = null
  // 区间结束
  end: Nullable<number> = null
}