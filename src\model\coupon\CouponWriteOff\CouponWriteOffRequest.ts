export default class CouponWriteOffRequest {
  // @ApiModelProperty(value = "活动制定所属营销中心")
  marketingCenter: Nullable<string> = null; // 活动制定所属营销中心
  // @ApiModelProperty(value = "单号")
  number: Nullable<string> = null; // 单号
  // @ApiModelProperty(value = "版本，客户端只读")
  revision: Nullable<string> = null; // 活动版本，客户端只读
  // @ApiModelProperty(value = "是否最新版本")
  lastRevision: Nullable<Boolean> = null; // 是否最新版本
  // @ApiModelProperty(value = "核销单状态，客户端只读")
  state: Nullable<"INITIAL" | "EFFECTED"> = null; // 核销单状态，客户端只读
  // @ApiModelProperty(value = "核销门店")
  issueOrgId: Nullable<string> = null; // 活动开始日期，精确到秒
  // @ApiModelProperty(value = "核销门店名")
  issueOrgName: Nullable<string> = null; // 活动结束日期，精确到秒
  // @ApiModelProperty(value = "券码组合")
  couponCodes: Nullable<string[]> = []; // 券码组合
  // @ApiModelProperty(value = "发生时间")
  occurredTime: Nullable<string> = null; // 发生时间
  // @ApiModelProperty(value = "操作人")
  operator: Nullable<string> = null; // 发生时间
  // @ApiModelProperty(value = "操作人")
  remark: Nullable<string> = null; // 发生时间
}
