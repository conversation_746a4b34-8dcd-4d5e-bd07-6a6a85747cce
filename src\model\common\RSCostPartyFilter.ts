export default class RSCostPartyFilter {
  //
  idNameLikes: Nullable<string> = null
  //
  costPartyIdEquals: Nullable<string> = null
  //
  costPartyIdIsNull: Nullable<boolean> = null
  //
  costPartyIdIn: string[] = []
  //
  costPartyIdStartsWith: Nullable<string> = null
  //
  costPartyIdEndsWith: Nullable<string> = null
  //
  costPartyIdLikes: Nullable<string> = null
  //
  costPartyNameEquals: Nullable<string> = null
  //
  costPartyNameIsNull: Nullable<boolean> = null
  //
  costPartyNameIn: string[] = []
  //
  costPartyNameStartsWith: Nullable<string> = null
  //
  costPartyNameEndsWith: Nullable<string> = null
  //
  costPartyNameLikes: Nullable<string> = null
  //
  stateEquals: Nullable<string> = null
  //
  stateIsNull: Nullable<boolean> = null
  //
  stateIn: string[] = []
  //
  stateGreater: Nullable<string> = null
  //
  stateGreaterOrEquals: Nullable<string> = null
  //
  stateLess: Nullable<string> = null
  //
  stateLessOrEquals: Nullable<string> = null
  //
  stateBetween: Nullable<string> = null
  //
  stateBetweenClosedClosed: Nullable<string> = null
  //
  stateBetweenClosedOpen: Nullable<string> = null
  //
  stateBetweenOpenClosed: Nullable<string> = null
  //
  stateBetweenOpenOpen: Nullable<string> = null
  //
  stateStartsWith: Nullable<string> = null
  //
  stateEndsWith: Nullable<string> = null
  //
  stateLikes: Nullable<string> = null
  //
  stateAfter: Nullable<string> = null
  //
  stateAfterOrEqual: Nullable<string> = null
  //
  stateBefore: Nullable<string> = null
  //
  stateBeforeOrEqual: Nullable<string> = null
  //
  logoEquals: Nullable<string> = null
  //
  logo$IsNull: Nullable<boolean> = null
  //
  logoIn: string[] = []
  //
  logoStartsWith: Nullable<string> = null
  //
  logoEndsWith: Nullable<string> = null
  //
  logoLikes: Nullable<string> = null
  //
  remarkEquals: Nullable<string> = null
  //
  remark$IsNull: Nullable<boolean> = null
  //
  remarkIn: string[] = []
  //
  remarkStartsWith: Nullable<string> = null
  //
  remarkEndsWith: Nullable<string> = null
  //
  remarkLikes: Nullable<string> = null
  //
  costPartyIdOrder: Nullable<boolean> = null
  //
  costPartyNameOrder: Nullable<boolean> = null
  //
  stateOrder: Nullable<boolean> = null
  //
  logoOrder: Nullable<boolean> = null
  //
  remarkOrder: Nullable<boolean> = null
  //
  page: Nullable<number> = null
  //
  pageSize: Nullable<number> = null
}