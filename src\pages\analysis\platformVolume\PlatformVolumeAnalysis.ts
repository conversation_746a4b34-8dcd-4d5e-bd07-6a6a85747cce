/*
 * @Author: 黎钰龙
 * @Date: 2024-02-29 13:47:05
 * @LastEditTime: 2024-06-03 17:08:53
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\analysis\memberGrowth\MemberGrowthAnalysis.ts
 * 记得注释
 */
import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import FormItem from 'cmp/formitem/FormItem';
import MyQueryCmp from 'cmp/querycondition/MyQueryCmp';
import AnalysisDateSelector from '../cmp/AnalysisDateSelector/AnalysisDateSelector.vue';
import ChannelSelect from 'cmp/channelselect/ChannelSelect';
import SelectStores from 'cmp/selectStores/SelectStores';
import MemberLineChart from '../cmp/MemberLineChart/MemberLineChart';
import SelectEmployees from 'cmp/selectEmployees/selectEmployees';
import DownloadCenterDialog from 'pages/main/dowmload_center_dialog/DownloadCenterDialog';
import I18nPage from 'common/I18nDecorator';
import { Component } from 'vue-property-decorator';
import CommonUtil from 'util/CommonUtil';
import AnalysisReportApi from 'http/analysis/AnalysisReportApi';
// import OrgTradeAnalysisReport from 'model/analysis/OrgTradeAnalysisReport';
import PlatformCouponAnalysisReport from 'model/analysis/PlatformCouponAnalysisReport';
import AbstractLineChart from '../cmp/AbstractLineChart';
import PlatformCouponAnalysisReportQuery from "model/analysis/PlatformCouponAnalysisReportQuery";
import OrgApi from "http/org/OrgApi";

class Filter {
    dataRange: any = null  //时间
    channel: any = null  //渠道
    store: any = null  //门店
    areaLeaderEquals: any = null  //区域主管
    operationEquals: any = null //营运经理
    issueChannelTypeEquals: any = null
}

@Component({
    name: 'PlatformVolumeAnalysis',
    components: {
        BreadCrume,
        MyQueryCmp,
        FormItem,
        AnalysisDateSelector,
        ChannelSelect,
        SelectStores,
        MemberLineChart,
        SelectEmployees,
        DownloadCenterDialog
    }
})
@I18nPage({
    prefix: [
        '/公用/券模板',
        '/数据/平台券分析',
        '/公用/下拉框/提示',
        '/数据/券报表',
    ],
    auto: true
})
export default class PlatformVolumeAnalysis extends AbstractLineChart {
    $refs: any
    panelArray: any = []
    filter: Filter = new Filter()
    detail: PlatformCouponAnalysisReport = new PlatformCouponAnalysisReport()
    downloadCenterFlag: boolean = false; //文件下载中心弹窗
    platformList: any = []
    /* 每个item的第一项：数据名
    /  第二项：数据值
    /  第三项：是否展示在右y轴上 */
    get valueArray() {
        const arr = [
            [this.i18n('用券数量'), this.detail.usedCouponCountData, 0],
            [this.i18n('连带销售额'), this.detail.tradeAmountData, 0],
            [this.i18n('抵用金额'), this.detail.deductAmountData, 0]
        ]
        return this.doTransValueArray(arr)
    }

    get summaryViewArr() {
        return [
            {
                label: this.i18n('用券数量'),
                value: this.detail.summary?.usedCouponCount
            },
            {
                label: this.i18n('连带销售额'),
                value: this.detail.summary?.tradeAmount
            },
            {
                label: this.i18n('抵用金额'),
                value: this.detail.summary?.deductAmount
            }
        ]
    }

    // 需要显示百分号的数据名称
    get showPercentName() {
        return []
    }

    created() {
        this.getPlatformList()
        this.panelArray = [
            {
                name: this.i18n('/公用/菜单/平台券分析'),
                url: ""
            }
        ]
    }

    getPlatformList() {
        OrgApi.getPlatform().then((res)=>{
            if(res.code === 2000) {
                this.platformList = (res.data || []).map((item:any)=>{
                    if (item.supportedCouponPay) {
                        return {
                            label: item?.platForm?.name || '-',
                            value: item?.platForm?.id || '-'
                        }
                    }
                }).filter((item)=>item)
            } else {
                throw new Error(res.msg || this.i18n('查询平台失败'))
            }
        }).catch((error) => this.$message.error(error.message))
    }

    mounted() {
        this.onSearch()
    }

    doExport() {
        this.$confirm(this.i18n("将根据当前查询条件生成报表，确认导出吗？"), this.i18n('/储值/预付卡/充值卡制售单/列表页面/导出'), {
            confirmButtonText: this.formatI18n("/公用/按钮", "确定") as string,
            cancelButtonText: this.formatI18n("/公用/按钮", "取消") as string,
        }).then(() => {
            const body = this.doFilterParams()
            AnalysisReportApi.exportPlatformCouponReport(body).then((res) => {
                if (res.code === 2000) {
                    this.downloadCenterFlag = true;
                } else {
                    throw new Error(res.msg!)
                }
            }).catch((error) => {
                this.$message.error(error.message)
            })
        });
    }

    doReset() {
        this.filter = new Filter()
        this.$refs.analysisDateSelector.doReset()
        this.onSearch()
    }

    onSearch() {
        const body = this.doFilterParams()
        const loading = CommonUtil.Loading()
        AnalysisReportApi.platformCouponReport(body).then((res) => {
            if (res.code === 2000) {
                this.detail = res.data || new PlatformCouponAnalysisReport()
            } else {
                throw new Error(res.msg as any)
            }
        }).catch((error) => {
            this.$message.error(error.message || this.i18n('内部异常'))
        }).finally(() => {
            loading.close()
        })
    }

    // 查询条件
    doFilterParams() {
        const params = new PlatformCouponAnalysisReportQuery()
        params.dateUnitEquals = this.filter.dataRange?.type
        if (this.filter.dataRange?.date?.length) {
            params.startDate = this.filter.dataRange.date[0]
            params.endDate = this.filter.dataRange.date[1]
        }
        params.operationEquals = this.filter.operationEquals?.id || null
        params.areaLeaderEquals = this.filter.areaLeaderEquals?.id || null
        //渠道
        if (this.filter.channel?.length) {
            params.channelTypeEquals = this.filter.channel[0].type
            params.channelIdEquals = this.filter.channel[0].id
        }
        params.issueChannelTypeEquals = this.filter.issueChannelTypeEquals
        params.storeIdEquals = this.filter.store
        return params
    }

    doDateChange(value: any) { // {type: 'DAY' | 'WEEK' | 'MONTH', date:['2024-03-05','2024-03-06']}
        this.filter.dataRange = value
    }

    doDownloadDialogClose() {
        this.downloadCenterFlag = false;
    }
    // doDivideGetPercentFour(value:string,value2:number){

    // }
};