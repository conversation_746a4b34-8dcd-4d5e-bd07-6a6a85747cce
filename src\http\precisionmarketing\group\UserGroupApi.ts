/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:19
 * @LastEditTime: 2024-10-21 17:45:42
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\precisionmarketing\group\UserGroupApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import UserGroup from 'model/precisionmarketing/group/UserGroup'
import UserGroupBody from 'model/precisionmarketing/group/UserGroupBody'
import UserGroupFilter from 'model/precisionmarketing/group/UserGroupFilter'
import UserGroupSum from 'model/precisionmarketing/group/UserGroupSum'
import OperationalResp from "model/precisionmarketing/tag/OperationalResp";
import MemberPortrait from "model/precisionmarketing/group/MemberPortrait";
import UserGroupLog from 'model/precisionmarketing/group/UserGroupLog';
import UserGroupImportBody from "model/precisionmarketing/group/UserGroupImportBody";
import MemberFilter from "model/member_standard/MemberFilter";
import Member from "model/member/Member";
import UserGroupV2ImportUpdate from 'model/precisionmarketing/group/UserGroupV2ImportUpdate'

export default class UserGroupApi {

  /**
   * 获取客群画像
   *
   */
  static getMemberPortrait(groupId: string): Promise<Response<MemberPortrait>> {
    return ApiClient.server().get(`/v1/precision-marketing/user-group/${groupId}`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 手动执行客群计算
   *
   */
  static executeManually(groupId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/precision-marketing/user-group/executeManually/${groupId}`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 批量禁用
   *
   */
  static batchDisable(body: string[]): Promise<Response<OperationalResp>> {
    return ApiClient.server().post(`/v1/precision-marketing/user-group/batchDisable`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 批量启用
   *
   */
  static batchEnable(body: string[]): Promise<Response<OperationalResp>> {
    return ApiClient.server().post(`/v1/precision-marketing/user-group/batchEnable`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 删除客群信息
   *
   */
  static delete(uuid: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/precision-marketing/user-group/delete/${uuid}`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 启用禁用
   *
   */
  static disableOrEnable(uuid: string, disable: boolean): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/precision-marketing/user-group/disableOrEnable/${uuid}/${disable}`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询客群详情信息
   *
   */
  static get(uuid: string): Promise<Response<UserGroupBody>> {
    return ApiClient.server().get(`/v1/precision-marketing/user-group/get/${uuid}`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询客群组信息
   *
   */
  static query(body: UserGroupFilter): Promise<Response<UserGroup[]>> {
    return ApiClient.server().post(`/v1/precision-marketing/user-group/query`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询客群日志信息
   * 查询客群日志信息。
   *
   */
  static queryLog(userGroupUuid: string, page: number, pageSize: number): Promise<Response<UserGroupLog[]>> {
    return ApiClient.server().post(`/v1/precision-marketing/user-group/queryLog/${userGroupUuid}/${page}/${pageSize}`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 新建或修改客群信息
   *
   */
  static saveOrUpdate(body: UserGroupBody): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/precision-marketing/user-group/saveOrModify`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 导入或修改导入客群信息
   *
   */
  static importOrUpdate(body: UserGroupImportBody): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/precision-marketing/user-group/importOrUpdate`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
  * 导入更新客群信息
  * 
  */
  static importUpdate(body: UserGroupV2ImportUpdate): Promise<Response<void>> {
    return ApiClient.server().post(`/v2/precision-marketing/user-group/importUpdate`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 汇总查询客群组信息
   *
   */
  static summary(body: UserGroupFilter): Promise<Response<UserGroupSum>> {
    return ApiClient.server().post(`/v1/precision-marketing/user-group/summary`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 会员列表信息查询
   *
   */
  static queryGroupMember(groupId: string, body: MemberFilter): Promise<Response<Member[]>> {
    return ApiClient.server().post(`/v1/precision-marketing/user-group/query/${groupId}`, body, {}).then((res) => {
      return res.data
    })
  }
}
