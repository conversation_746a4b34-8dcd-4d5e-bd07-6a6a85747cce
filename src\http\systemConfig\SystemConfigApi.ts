import ApiClient from 'http/ApiClient'
import BWebAppearanceConfig from 'model/systemConfig/BWebAppearanceConfig'
import BMemberFreezeConfig from 'model/systemConfig/BMemberFreezeConfig'
import PayRegisterMemberConfig from 'model/systemConfig/PayRegisterMemberConfigRequest'
import Response from 'model/common/Response'
import ShopEntryRuleConfig from 'model/systemConfig/ShopEntryRuleConfig'
import BMemberAgreementConfig from 'model/system/BMemberAgreementConfig'
import MiniProgramSetting from 'model/systemConfig/MiniProgramSetting'
import BMemberBalanceAccountLimitConfig from 'model/systemConfig/BMemberBalanceAccountLimitConfig'
import BMemberSingleRechargeLimitConfig from 'model/systemConfig/BMemberSingleRechargeLimitConfig'


import PrepayCardConfig from "model/systemConfig/PrepayCardConfig";
import SettleRuleConfig from "model/systemConfig/SettleRuleConfig";
import BPayMethodConfig from 'model/systemConfig/BPayMethodConfig'
import BPayMethodConfigUpdate from 'model/systemConfig/BPayMethodConfigUpdate'
import NoGainPointsConfig from "model/systemConfig/NoGainPointsConfig";

import ExpireRemindConfig from "model/systemConfig/ExpireRemindConfig";
import MemberPersonalDataConfig from "model/systemConfig/MemeberPersonalDataConfig";
import BPersonalDataConfig from 'model/member/BPersonalDataConfig'
import I18nSettingConfig from "model/member/I18nSettingConfig";
import ExceptionPushConfig from 'model/systemConfig/ExceptionPushConfig'
import CmsConfig from 'model/template/CmsConfig'


export default class SystemConfigApi {

  /**
   * 查询会员协议设置
   * 查询会员协议设置。
   * 
   */
  static getBalanceAccountLimitConfig(): Promise<Response<BMemberBalanceAccountLimitConfig>> {
    return ApiClient.server().post(`/v1/systemConfig/balanceAccountLimitConfig/get`, {}, {
    }).then((res) => {
      return res.data
    })
  }


  /**
   * 会员账户限额设置
   * 会员账户限额设置。
   * 
   */
  static saveBalanceAccountLimitConfig(body: BMemberBalanceAccountLimitConfig): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/systemConfig/balanceAccountLimitConfig/save`, body, {
    }).then((res) => {
      return res.data
    })
  }


  /**
   * 单次充值限额设置
   * 单次充值限额设置。
   * 
   */
  static saveSingleRechargeLimitConfig(body: BMemberSingleRechargeLimitConfig): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/systemConfig/singleRechargeLimitConfig/save`, body, {
    }).then((res) => {
      return res.data
    })
  }


  /**
     * 查询单次充值限额设置
     * 查询单次充值限额设置。
     * 
     */
  static getSingleRechargeLimitConfig(): Promise<Response<BMemberSingleRechargeLimitConfig>> {
    return ApiClient.server().post(`/v1/systemConfig/singleRechargeLimitConfig/get`, {}, {
    }).then((res) => {
      return res.data
    })
  }


  /**
   * 查询系统外观设置
   * 查询系统外观设置。
   *
   */
  static getWebAppearanceConfig(): Promise<Response<BWebAppearanceConfig>> {
    return ApiClient.server().post(`/v1/systemConfig/webAppearanceConfig/get`, {}, {
    }).then((res) => {
      return res.data
    })
  }
  /**
    * 查询会员协议设置
    * 查询会员协议设置。
    * 
    */
  static getMemberFreezeConfig(): Promise<Response<BMemberFreezeConfig>> {
    return ApiClient.server().post(`/v1/systemConfig/memberFreezeConfig/get`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询支付即会员配置
   * 查询支付即会员配置。
   * 
   */
  static getPayRegisterMemberConfig(): Promise<Response<PayRegisterMemberConfig>> {
    return ApiClient.server().post(`/v1/systemConfig/payRegisterMemberConfig/get`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 进店规则配置
   * 进店规则配置。
   * 
   */
  static getShopEntryRule(): Promise<Response<ShopEntryRuleConfig>> {
    return ApiClient.server().post(`/v1/systemConfig/shopEntryRule/get`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 支付即会员配置保存
   * 支付即会员配置保存。
   * 
   */
  static savePayRegisterMemberConfig(body: PayRegisterMemberConfig): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/systemConfig/payRegisterMemberConfig/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 进店规则配置保存
   * 进店规则配置保存。
   * 
   */
  static saveShopEntryRule(body: ShopEntryRuleConfig): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/systemConfig/shopEntryRule/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 
  * 会员协议管理保存
  * 
  */
  static saveMemberAgreement(body: BMemberAgreementConfig): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/systemConfig/memberAgreementConfig/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   *
   * 查询会员协议管理
   *
   */
  static getMemberAgreementConfig(): Promise<Response<BMemberAgreementConfig>> {
    return ApiClient.server().post(`/v1/systemConfig/memberAgreementConfig/get`, {}, {
    }).then((res) => {
      return res.data;
    });
  }

  /**
 * 会员协议设置
 * 会员协议设置。
 * 
 */
  static saveMemberFreezeConfig(body: BMemberFreezeConfig): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/systemConfig/memberFreezeConfig/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 系统外观设置
   * 系统外观设置。
   *
   */
  static saveWebAppearanceConfig(body: BWebAppearanceConfig): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/systemConfig/webAppearanceConfig/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询小程序设置
   * 查询小程序设置。
   * 
   */
  static getMiniProgramConfig(): Promise<Response<MiniProgramSetting>> {
    return ApiClient.server().post(`/v1/systemConfig/miniProgramConfig/get`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 小程序设置
   * 小程序设置。
   * 
   */
  static saveMiniProgramConfig(body: MiniProgramSetting): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/systemConfig/miniProgramConfig/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询预付卡设置
   * 查询预付卡设置。
   *
   */
  static getPrepayCardConfig(): Promise<Response<PrepayCardConfig>> {
    return ApiClient.server().post(`/v1/systemConfig/prepayCardConfig/get`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 预付卡设置
   * 预付卡设置。
   *
   */
  static savePrepayCardConfig(body: PrepayCardConfig): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/systemConfig/prepayCardConfig/save`, body, {
    }).then((res) => {
      return res.data
    })
  }


  /**
   * 查询不积分设置设置
   * 查询不积分设置设置。
   *
   */
  static getNoGainPointsConfig(): Promise<Response<NoGainPointsConfig>> {
    return ApiClient.server().post(`/v1/systemConfig/noGainPointsConfig/get`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 不积分设置设置
   * 不积分设置设置。
   *
   */
  static saveNoGainPointsConfig(body: NoGainPointsConfig): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/systemConfig/noGainPointsConfig/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

    /**
   * 获取临期通知设置
   * 获取临期通知设置
   * 
   */
    static getCouponExpireRemind(): Promise<Response<ExpireRemindConfig>> {
      return ApiClient.server().post(`/v1/systemConfig/expire_remind/get`, {}, {
      }).then((res) => {
        return res.data
      })
    }

    /**
     * 保存临期通知设置
     * 保存临期通知设置
     * 
     */
    static setCouponExpireRemind(body: ExpireRemindConfig): Promise<Response<void>> {
      return ApiClient.server().post(`/v1/systemConfig/expire_remind/save`, body, {
      }).then((res) => {
        return res.data
      })
    }



  /**
   * 获取门店结算设置
   * 获取门店结算设置
   *
   */
  static getSettleRuleConfig(): Promise<Response<SettleRuleConfig>> {
    return ApiClient.server().post(`/v1/systemConfig/settle_rule/get`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存门店结算设置
   * 保存门店结算设置
   *
   */
  static setSettleRuleConfig(body: SettleRuleConfig): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/systemConfig/settle_rule/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 查询支付方式
  * 查询支付方式。
  * 
  */
  static getPayMethodConfig(): Promise<Response<BPayMethodConfig>> {
    return ApiClient.server().post(`/v1/systemConfig/payMethodConfig/get`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 支付方式新增
  * 支付方式新增。
  * 
  */
  static savePayMethodConfig(body: BPayMethodConfig): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/systemConfig/payMethodConfig/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 支付方式修改
   * 支付方式修改。
   * 
   */
  static updatePayMethodConfig(body: BPayMethodConfigUpdate): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/systemConfig/payMethodConfig/update`, body, {
    }).then((res) => {
      return res.data
    })
  }


  /**
    * 支付方式删除
    * 支付方式删除。
    * 
    */
  static deletePayMethodConfig(body: BPayMethodConfig): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/systemConfig/payMethodConfig/delete`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询会员资料设置
   * 查询会员资料设置。
   *
   */
  static getPersonalDataConfig(): Promise<Response<MemberPersonalDataConfig>> {
    return ApiClient.server().get(`/v1/systemConfig/memberDataConfig/get`, {
    }).then((res) => {
      return res.data
    })
  }


  /**
   * 会员资料设置保存
   * 会员资料设置保存。
   *
   */
  static savePersonalDataConfig(body: MemberPersonalDataConfig): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/systemConfig/memberDataConfig/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 查询已启用的会员资料
  * 查询已启用的会员资料。
  *
  */
  static getEnable(): Promise<Response<BPersonalDataConfig>> {
    return ApiClient.server().get(`/v1/systemConfig/memberDataConfig/getEnable`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存国际化设置
   * 保存国际化设置
   *
   */
  static getI18nSettingConfig(): Promise<Response<I18nSettingConfig>> {
    return ApiClient.server().post(`/v1/systemConfig/i18n/get`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存国际化设置
   * 保存国际化设置
   *
   */
  static saveI18nSettingConfig(body: I18nSettingConfig): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/systemConfig/i18n/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存异常推送设置
   * 保存异常推送设置
   *
   */
  static saveExceptionPushConfig(body: ExceptionPushConfig): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/systemConfig/exception_push/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取异常推送设置
   * 获取异常推送设置
   *
   */
  static getExceptionPushConfig(): Promise<Response<ExceptionPushConfig>> {
    return ApiClient.server().post(`/v1/systemConfig/exception_push/get`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询投放设置
   * 查询投放设置。
   * 
   */
  static getCmsConfig(): Promise<Response<CmsConfig>> {
    return ApiClient.server().post(`/v1/systemConfig/cmsConfig/get`, {}, {
    }).then((res) => {
      return res.data
    })
  }

}




