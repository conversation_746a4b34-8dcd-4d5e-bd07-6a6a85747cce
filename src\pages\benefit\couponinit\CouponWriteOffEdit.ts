import { Component, Vue } from "vue-property-decorator";
import BreadCrume from "cmp/bread-crumb/BreadCrume.vue";
import BlockTitle from "cmp/block-title/BlockTitle.vue";
import ActiveAddCoupon from "cmp/activeaddcoupon/ActiveAddCoupon.vue";
import ActiveStore from "cmp/activestore/ActiveStore.vue";
import ActiveStoreDtl from "cmp/activestoredtl/ActiveStoreDtl.vue";
import MultipleChannelSelect from "cmp/channelselect/multiple/MultipleChannelSelect";
import CouponWriteoffBill from "model/coupon/CouponWriteOff/CouponWriteoffBill";
import I18nPage from "common/I18nDecorator";
import RSOrg from "model/common/RSOrg";
import RSOrgFilter from "model/common/RSOrgFilter";
import OrgApi from "http/org/OrgApi";
import CouponWriteOffRequest from "model/coupon/CouponWriteOff/CouponWriteOffRequest";
import CouponWriteoffBillA<PERSON> from "http/coupon/CouponWriteOff/CouponWriteoffBillApi";
import AmountToFixUtil from "util/AmountToFixUtil";
import SelectStores from "cmp/selectStores/SelectStores";

@Component({
	name: "InviteSendGift",
	components: {
		BreadCrume,
		BlockTitle,
		ActiveAddCoupon,
		ActiveStore,
		MultipleChannelSelect,
		ActiveStoreDtl,
    SelectStores
	},
})
@I18nPage({
    prefix: ['/公用/券核销']
})
export default class InviteSendGift extends Vue {
    i18n: any
	panelArray: any = [];
	$refs: any;
	rules: any
    form: CouponWriteOffRequest = new CouponWriteOffRequest()
    state: Nullable<string> = null
    stores: RSOrg[] = []
    isCurrentCouponStore: Boolean = false
	created() {
		this.form.couponCodes = []
        this.form.couponCodes.push('')
        this.getStore()
		this.panelArray = [
			{
				name: this.i18n("券核销单"),
				url: "coupon-write-off",
			},
			{
				name: this.i18n('新建券核销单'),
				url: "",
			},
		];
		this.rules = {
            issueOrgId: [
				{ required: true, message: this.formatI18n('/公用/表单校验/请选择必选项'), trigger: 'change' },
			]
		};
	}
	doSave(type: string) {
		this.$refs["ruleForm"].validate((valid: any) => {
			if (valid) {
				this.stores.forEach((item: RSOrg)=>{
					if (this.form.issueOrgId && this.form.issueOrgId === item.org.id) {
						this.form.issueOrgName = item.org.name
					}
				})
				CouponWriteoffBillApi.create(this.form).then(res=>{
					if (res.code === 2000) {
						if (type === 'save') {
							this.$message.success(this.formatI18n('/公用/js提示信息', '保存成功'))
							setTimeout(() => {
								this.$router.push({
									path: 'coupon-write-off-dtl',
									query:{
										number: res.data
									}
								})
							}, 1000);
						} else {
							console.log(res.data);
							CouponWriteoffBillApi.audit(res.data).then(res1=>{
								if (res1.code === 2000) {
									this.$message.success(this.formatI18n('/公用/js提示信息', '保存成功'))
									setTimeout(() => {
										this.$router.push({
											path: 'coupon-write-off-dtl',
											query:{
												number: res.data
											}
										})
									}, 1000);
								} else {
									this.$message.error(res.msg as string)
								}
							})
						}
					} else {
						this.$message.error(res.msg as string)
					}
				})
			} else {
				return false;
			}
		});
	}
	doBack() {
        this.$router.back();
	}

	couponCodeChange(index: number) {
		console.log(index);
		let str = this.form.couponCodes![index].replace(/[^\a-\z\A-\Z0-9]/g, '');
		this.$set(this.form.couponCodes as string[], index, str)
	}

    addCouponCode() {
        this.form.couponCodes!.push('')
    }
    delCouponCode(index: number) {
        this.form.couponCodes!.splice(index, 1)
    }

    private getStore() {
        let params: RSOrgFilter = new RSOrgFilter()
        params.page = 0
        params.pageSize = 0
        OrgApi.query(params).then((resp: any) => {
            if (resp && resp.code === 2000) {
                this.stores = resp.data
            }
        }).catch((error) => {
            if (error && error.message) {
                this.$message.error(error.message)
            }
        })
    }
}
