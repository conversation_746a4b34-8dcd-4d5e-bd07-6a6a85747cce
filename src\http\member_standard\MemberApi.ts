import ApiClient from "http/ApiClient";
import Coupon from "model/member_standard/Coupon";
import Member from "model/member/Member";
import MemberDetail from "model/member_standard/MemberDetail";
import MemberFilter from "model/member_standard/MemberFilter";
import MemberPass from "model/member_standard/MemberPass";
import MemberTags from "model/member_standard/MemberTags";
import Response from "model/common/Response";
import SimpleCard from "model/member_standard/SimpleCard";
import MemberLog from "model/member_standard/log/MemberLog";
import ModifyStore from "model/member_standard/ModifyStore";
import MainAndSubCard from "model/member_standard/MainAndSubCard";
import DeleteMemberIdentRequest from "model/member_standard/DeleteMemberIdentRequest";
import UnbindSubMemberRequest from "model/member_standard/UnbindSubMemberRequest";
import ReissueCouponRequest from "model/member/ReissueCouponRequest";
import MemberEditConfig from "model/member_standard/MemberEditConfig";
import BenefitCard from "model/benefitCard/BenefitCard";
import BMemberTrade from "model/member/BMemberTrade";
import TradeFilter from "model/member/TradeFilter";
import BMemberChannel from "model/member/BMemberChannel";
import UserGroupV2 from "model/precisionmarketing/userGroup/UserGroupV2";
import BUserGroupRemoveRequest from "model/member/BUserGroupRemoveRequest";
import TagOption from "model/tag/TagOption";
import BGrowthValue from "model/member/BGrowthValue";
import GrowthValueFilter from "model/member/GrowthValueFilter";
import BTradeDetailFilter from "model/member/BTradeDetailFilter";

export default class MemberApi {
  /**
   * 主副卡信息查询
   *
   */
  static getMainAndSubCard(memberId: string): Promise<Response<MainAndSubCard>> {
    return ApiClient.server()
      .post(`/v1/member/mainAndSubCard/${memberId}`, {}, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 查询会员标签
   * 查询会员标签。
   *
   */
  static getMemberTag(memberId: string): Promise<Response<TagOption[]>> {
    return ApiClient.server()
      .get(`/v1/member/getMemberTag/${memberId}`, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 修改会员备注信息
   * 修改会员备注信息。
   *
   */
  static editRemark(memberId: string, remark: string): Promise<Response<void>> {
    return ApiClient.server()
      .post(
        `/v1/member/editRemark/${memberId}`,
        {},
        {
          params: {
            remark: remark,
          },
        }
      )
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 查询会员日志信息
   * 查询会员日志信息。
   *
   */
  static queryLog(memberId: string, page: number, pageSize: number): Promise<Response<MemberLog[]>> {
    return ApiClient.server()
      .post(`/v1/member/queryLog/${memberId}/${page}/${pageSize}`, {}, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 批量导出会员信息
   * 批量导出会员信息。
   *
   */
  static exportMember(body: MemberFilter): Promise<Response<void>> {
    return ApiClient.server()
      .post(`/v1/member/exportMember`, body, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 冻结会员
   *
   */
  static block(memberId: string): Promise<Response<void>> {
    return ApiClient.server()
      .post(`/v1/member/block/${memberId}`, {}, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 检查要修改的手机号是否已经存在，存在返回true，修改为自身原有手机号返回false
   *
   */
  static checkMobile(mobile: string, memberId: string): Promise<Response<boolean>> {
    return ApiClient.server()
      .get(`/v1/member/checkMobile/${mobile}/${memberId}`, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 会员详情
   *
   */
  static editData(memberId: string): Promise<Response<MemberDetail>> {
    return ApiClient.server()
      .post(`/v1/member/editData/${memberId}`, {}, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 会员详情
   *
   */
  static detail(memberId: string): Promise<Response<MemberDetail>> {
    return ApiClient.server()
      .post(`/v1/member/detail/${memberId}`, {}, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 获取会员卡信息
   *
   */
  static getCards(memberId: string): Promise<Response<SimpleCard[]>> {
    return ApiClient.server()
      .get(`/v1/member/getCards/${memberId}`, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 获取会员券信息
   *
   */
  static getCoupon(memberId: string): Promise<Response<Coupon[]>> {
    return ApiClient.server()
      .get(`/v1/member/getCoupons/${memberId}`, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 批量导入会员信息
   *
   */
  static importMember(body: any, card: boolean): Promise<Response<void>> {
    return ApiClient.server()
      .post(`/v1/member/importMember`, body, {
        params: {
          card: card,
        },
      })
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 修改会员支付密码
   *
   */
  static modifyPayPass(body: MemberPass): Promise<Response<void>> {
    return ApiClient.server()
      .post(`/v1/member/modifyPayPass`, body, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 会员列表信息查询
   *
   */
  static query(body: MemberFilter): Promise<Response<Member[]>> {
    return ApiClient.server()
      .post(`/v1/member/query`, body, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 保存会员基础信息
   *
   */
  static saveMemberBasicData(body: MemberDetail, memberId: string): Promise<Response<void>> {
    return ApiClient.server()
      .post(`/v1/member/saveMemberBasicData/${memberId}`, body, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 保存会员等级相关信息
   *
   */
  static saveMemberGradeData(body: MemberDetail, memberId: string): Promise<Response<void>> {
    return ApiClient.server()
      .post(`/v1/member/saveMemberGradeData/${memberId}`, body, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 保存会员标签信息
   *
   */
  static saveMemberTag(body: MemberTags): Promise<Response<void>> {
    return ApiClient.server()
      .post(`/v1/member/saveMemberTag`, body, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 解冻结会员
   *
   */
  static unBlock(memberId: string): Promise<Response<void>> {
    return ApiClient.server()
      .post(`/v1/member/unBlock/${memberId}`, {}, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 解绑实体卡号
   *
   */
  static unbind(cardNumber: string, memberId: string): Promise<Response<void>> {
    return ApiClient.server()
      .post(`/v1/member/unbind/${memberId}/${cardNumber}`, {}, {})
      .then((res) => {
        return res.data;
      });
  }

  static modifyStore(body: ModifyStore): Promise<Response<void>> {
    return ApiClient.server()
      .post(`/v1/member/changeOwnerStore`, body, {})
      .then((res) => {
        return res.data;
      });
  }

  static allModifyStore(body: any): Promise<Response<void>> {
    return ApiClient.server()
      .post(`/v1/member/batchChangStore`, body, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 会员列表信息查询-区分营销中心
   *
   */
  static queryByMarketcenter(body: MemberFilter): Promise<Response<Member[]>> {
    return ApiClient.server()
      .post(`/v1/marketingCenterMember/query`, body, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 批量导出会员信息 区分营销中心
   * 批量导出会员信息。 区分营销中心
   *
   */
  static exportMarketCenterMember(body: MemberFilter): Promise<Response<void>> {
    return ApiClient.server()
      .post(`/v1/marketingCenterMember/exportMember`, body, {})
      .then((res) => {
        return res.data;
      });
  }

  static cancelMember(memberId: string): Promise<Response<void>> {
    return ApiClient.server()
      .post(`/v1/member/cancel/${memberId}`, {}, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 删除会员身份标识
   *
   */
  static deleteMemberIdent(body: DeleteMemberIdentRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/member/deleteMemberIdent`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 解绑子会员
   *
   */
  static unbindSubMember(body: UnbindSubMemberRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/member/unbindSubMember`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 手动给会员发券
  *
  */
  static reissueCoupon(body: ReissueCouponRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/member/reissueCoupon`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 查询会员名称最大长度
  * 查询会员名称最大长度。
  *
  */
  static getNameLengthLimit(): Promise<Response<number>> {
    return ApiClient.server()
      .post(`/v1/member/getNameLengthLimit`, {}, {})
      .then((res) => {
        return res.data;
      });
  }

  /**
   * 获取编辑会员资料信息配置
   * 获取编辑会员资料信息配置
   */
  static getEditConfig(): Promise<Response<MemberEditConfig>> {
    return ApiClient.server().get(`/v1/member/edit/config`, {
    }).then((res) => {
      return res.data
    })
  }

    /**
   * 批量导入付费会员卡
   * 批量导入付费会员卡。
   *
   */
    static paidBenefitCardImportExcel(body: any, orgId: string): Promise<Response<string>> {
      return ApiClient.server().post(`/v1/member/paidBenefitCardImportExcel`, body, {
        params: {
          orgId: orgId
        }
      }).then((res) => {
        return res.data
      })
    }

  /**
   * 会员付费会员卡查询
   *
   */
  static getBenefitCard(memberId: string): Promise<Response<BenefitCard[]>> {
    return ApiClient.server().get(`/v1/member/benefitCard/${memberId}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 交易明细查询
   *   销售 = "sale" ；销售冲正   = "killSale";  销售退  = "refund";  销售退冲正= "killRefund";
   */
  static queryTrade(body: TradeFilter): Promise<Response<BMemberTrade[]>> {
    return ApiClient.server().post(`/v1/member/trade/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 会员渠道身份信息
   *
   */
  static getChannelIdentity(memberId: string): Promise<Response<BMemberChannel[]>> {
    return ApiClient.server().get(`/v1/member/channel/${memberId}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 会员客群查询
   *
   */
  static getUserGroup(memberId: string): Promise<Response<UserGroupV2[]>> {
    return ApiClient.server().get(`/v1/member/user/group/${memberId}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 会员客群移除
   *
   */
  static removeUserGroup(body: BUserGroupRemoveRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/member/user/group/remove`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 成长值明细查询
   *
   */
  static queryGrowthValue(body: GrowthValueFilter): Promise<Response<BGrowthValue[]>> {
    return ApiClient.server().post(`/v1/member/growthValue/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 交易明细查询
   *
   */
  static tradeDetail(body: BTradeDetailFilter): Promise<Response<BMemberTrade>> {
    return ApiClient.server().post(`/v1/member/trade/detail`, body, {
    }).then((res) => {
      return res.data
    })
  }


  /**
   * 会员权益卡查询
   *
   */
  static getEquityCard(memberId: string): Promise<Response<BenefitCard[]>> {
    return ApiClient.server().get(`/v1/member/benefitCard/free/${memberId}`, {
    }).then((res) => {
      return res.data
    })
  }
}
