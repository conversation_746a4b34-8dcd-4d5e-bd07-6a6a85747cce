<template>
  <div class="dqsh-member-dtl">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button @click="doAddWhiteList" v-if="hasOptionPermission('/会员/会员管理/会员资料（大桥石化）', '加入白名单') && whiteState === false">
          加入白名单
        </el-button>
        <el-button @click="doEdit" type="primary" v-if="hasOptionPermission('/会员/会员管理/会员资料（大桥石化）', '编辑资料')">
          编辑资料
        </el-button>
        <el-button @click="doAdjust" v-if="hasOptionPermission('/会员/会员管理/会员资料（大桥石化）', '调整会员等级')">
          调整会员等级
        </el-button>
        <el-button @click="doReset" v-if="hasOptionPermission('/会员/会员管理/会员资料（大桥石化）', '重置支付密码')">
          重置支付密码
        </el-button>
        <el-button @click="doFreezon" style="color: red" v-if="dtl.state === 'Blocked'&& hasOptionPermission('/会员/会员管理/会员资料（大桥石化）', '冻结/解冻会员')">
          解冻
        </el-button>
        <el-button @click="doFreezon" style="color: red"
          v-if="dtl.state !== 'Blocked' && dtl.state !== 'Unactivated'&& hasOptionPermission('/会员/会员管理/会员资料（大桥石化）', '冻结/解冻会员')">
          冻结
        </el-button>
      </template>
    </BreadCrume>
    <div style="overflow: auto;height: 100%">
      <div class="dtl-block">
        <div style="width: 100px">
          <img alt="" src="~assets/image/member/member_bg.jpg" style="position: relative;left: 10px;top: 20px;">
        </div>
        <div class="dtl-block-item">
          <div class="dtl-block-subItem" style="border-bottom: 1px solid #eeeeee;margin-right: 15px">
            <div class="content-block">
              <div class="title">{{dtl.name ? dtl.name : '会员姓名未知'}}</div>
              <div>会员号：{{dtl.crmCode}}</div>
              <div>手机号：{{dtl.mobile | strFormat}}</div>
            </div>
            <div class="content-block">
              <div class="title" v-if="dtl.gradeCode">[{{dtl.gradeCode}}]{{dtl.gradeName}}</div>
              <div class="title" v-else>无会员等级</div>
              <div>成长值：{{dtl.growthValue | strFormat}}</div>
              <div v-if="dtl.gradeValidate && dtl.gradeCode">等级有效期：{{dtl.gradeValidate | dateFormate3}}</div>
              <div v-else>等级有效期：--</div>
            </div>
            <div style="width: 500px;padding-top: 20px">
              <el-block-panel>
                <el-block-panel-item>
                  <div class="item">累计消费(元)</div>
                  <div class="item" style="font-size: 28px;">{{dtl.totalConsume | fmt}}</div>
                </el-block-panel-item>
                <el-block-panel-item>
                  <div class="item">累计消费(笔)</div>
                  <div class="item" style="font-size: 28px;">{{dtl.consumeQty}}</div>
                </el-block-panel-item>
                <el-block-panel-item>
                  <div class="item">客单价(元)</div>
                  <div class="item" style="font-size: 28px;">{{dtl.avgAmount | fmt}}</div>
                </el-block-panel-item>
              </el-block-panel>
              <div style="text-align: right;margin-right: 45px" v-if="dtl.lastConsumeDate">最近一次消费：{{dtl.lastConsumeDate | dateFormate2}} {{getDay}}
              </div>
              <div style="text-align: right;margin-right: 45px" v-else>最近一次消费：--</div>
            </div>
          </div>
          <div class="dtl-block-subItem">
            <div class="content-block">
              <div>会员状态：
                <el-tag size="small" type="success" v-if="dtl.state === 'Using'">使用中</el-tag>
                <el-tag size="small" type="danger" v-if="dtl.state === 'Blocked'">已冻结</el-tag>
                <el-tag size="small" type="warning" v-if="dtl.state === 'Unactivated'">未激活</el-tag>
                <el-tag size="small" type="info" v-if="dtl.state === 'Canceled'">已注销</el-tag>
              </div>
              <div>注册日期：{{dtl.registerTime | dateFormate3}}</div>
              <!--常浩说是type字段不需要转义，绑定原值-->
              <!--<div v-if="dtl.registerChannel && dtl.registerChannel.type">注册渠道：{{dtl.registerChannel.type | registerChannel( dtl.registerChannel.type,  dtl.registerChannel.id)}}</div>-->
              <!--<div v-else>注册渠道：&#45;&#45;</div>-->
              <div>注册渠道：{{getRegisterChannel}}</div>
              <div>最近修改用户：{{dtl.modifier}}</div>
            </div>
            <div class="content-block">
              <!--<div>会员有效期：{{dtl.expiredDate | dateFormate3}}</div>-->
              <div>会员有效期：永久</div>
              <div>激活日期：{{dtl.activateTime | dateFormate3}}</div>
              <div>招募方式：{{dtl.registerScene | strFormat}}</div>
              <div>最近修改时间：{{dtl.modifed | dateFormate3}}</div>
            </div>
            <div class="other" style="width: 500px;">
              <div v-if="dtl.ownStore && dtl.ownStore.id">归属门店：[{{dtl.ownStore.id}}]{{dtl.ownStore.name}}</div>
              <div v-else>归属门店：--</div>
              <div>创建日期：{{dtl.created | dateFormate3}}</div>
              <div v-if="getInvited&&hasOptionPermission('/会员/会员管理/会员资料（大桥石化）', '会员资料查看')">邀请人会员：
                <el-button @click="doInviteMember" type="text">{{ getInvited }}</el-button>
              </div>
              <div v-else>邀请人会员：--</div>
              <div>是否加入白名单：{{whiteState ? '是' : '否'}}</div>
            </div>
          </div>
        </div>
      </div>
      <div style="background-color: #eeeff1;height: 15px;"></div>
      <div class="tag-block">
        <div class="sub-title">标签信息&nbsp;&nbsp;
          <el-button @click="doEditTag" type="text" v-if="hasOptionPermission('/会员/会员管理/会员资料（大桥石化）', '编辑标签')">编辑
          </el-button>
        </div>
        <div style="margin: 0 0 15px 15px">
          <el-tag :key="index" @close="doTagClose(index)" effect="plain" style="margin-right: 15px;margin-bottom: 15px" type="info"
            v-for="(item, index) in tags">{{item.tagName}}:{{item.tagValue}}
          </el-tag>
        </div>
      </div>
      <div style="background-color: #eeeff1;height: 15px;"></div>
      <div class="ic-block">
        <div class="sub-title">已绑定IC卡({{icCards.length}})</div>
        <div style="margin: 15px">
          <el-table :data="icCards" ref="userTable" style="width: 100%">
            <el-table-column label="内部卡号" prop="internalCode">
              <template slot-scope="scope">
                <div style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">
                  {{scope.row.internalCode}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="卡面卡号" prop="faceCode">
              <template slot-scope="scope">
                <div style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">
                  {{scope.row.faceCode}}
                </div>
              </template>
            </el-table-column>
            <el-table-column align="center" label="IC卡状态" prop="state">
              <template slot-scope="scope">
                <!--<el-tag type="success" v-if="scope.row.state === 'Using'">使用中</el-tag>-->
                <!--<el-tag type="danger" v-if="scope.row.state === 'Blocked'">已冻结</el-tag>-->
                <!--<el-tag type="success" v-if="scope.row.state === 'Unactivated'">未激活</el-tag>-->
                <!--<el-tag type="danger" v-if="scope.row.state === 'Canceled'">已注销</el-tag>-->
                {{scope.row.state}}
              </template>
            </el-table-column>
            <el-table-column align="left" label="持卡人" prop="state" width="400">
              <template slot-scope="scope">
                <div style="text-align: left">姓名：{{scope.row.name | strFormat}}</div>
                <div style="text-align: left">手机号：{{scope.row.mobile | strFormat}}</div>
                <div style="text-align: left">身份证号：{{scope.row.idCard | strFormat}}</div>
              </template>
            </el-table-column>
            <el-table-column align="left" label="IC卡类型" prop="cardTypeId">
              <template slot-scope="scope">
                [{{scope.row.cardTypeId}}]{{scope.row.cardTypeName}}
              </template>
            </el-table-column>
            <el-table-column align="left" label="账户类型" prop="state">
              <template slot-scope="scope">
                [{{scope.row.accountTypeId}}]{{scope.row.accountTypeName}}
              </template>
            </el-table-column>
            <el-table-column align="left" label="卡模板" prop="state">
              <template slot-scope="scope">
                <div>{{scope.row.cardTemplateId}}</div>
                <div>
                  <el-button @click="doCardTemplate(scope.row.cardTemplateId)" type="text">{{scope.row.templateName}}</el-button>
                </div>
              </template>
            </el-table-column>
            <el-table-column align="center" fixed="right" label="操作" prop="state">
              <template slot-scope="scope">
                <el-button @click="doUnBind(scope.row.faceCode)" type="text" v-if="hasOptionPermission('/会员/会员管理/会员资料（大桥石化）', '解绑IC卡')">解绑
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div style="margin: 15px">
            <el-pagination :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
              @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background layout="total, prev, pager, next, sizes,  jumper">
            </el-pagination>
          </div>
        </div>
      </div>
      <div style="background-color: #eeeff1;height: 15px;"></div>
      <div class="ic-block">
        <div class="sub-title">
          <span :style="{marginRight: '20px'}">车牌付签约信息({{carInfo.length}})</span>
          <el-radio-group v-model="signStatus" @change="getCarInfo">
            <el-radio-button label="SIGNING">已签约</el-radio-button>
            <el-radio-button label="CANCEL">已作废</el-radio-button>
          </el-radio-group>
        </div>
        <div style="margin: 15px">
          <el-table :data="carInfo" ref="userTable" style="width: 100%">
            <el-table-column label="签约时间" prop="contractTime">
              <template slot-scope="scope">{{scope.row.contractTime | yyyyMMddHHmmss}}</template>
            </el-table-column>
            <el-table-column label="车牌号" prop="number">
              <template slot-scope="scope">{{scope.row.number || '--'}}</template>
            </el-table-column>
            <el-table-column label="车辆类型" prop="type">
              <template slot-scope="scope">{{scope.row.type || '--'}}</template>
            </el-table-column>
            <el-table-column label="车辆颜色" prop="color">
              <template slot-scope="scope">{{scope.row.color || '--'}}</template>
            </el-table-column>
            <el-table-column label="车牌类型" prop="numberType">
              <template slot-scope="scope">{{scope.row.numberType || '--'}}</template>
            </el-table-column>
            <el-table-column label="支付方式" prop="state">
              <template slot-scope="scope">{{scope.row.payType || '--'}}</template>
            </el-table-column>
            <el-table-column v-if="signStatus === 'CANCEL'" label="作废时间" prop="cancelTime">
              <template slot-scope="scope">{{scope.row.cancelTime | yyyyMMddHHmmss}}</template>
            </el-table-column>
          </el-table>
          <div style="margin: 15px">
            <el-pagination :current-page="carPage.currentPage" :page-size="carPage.size" :page-sizes="[10, 20, 30, 40]" :total="carPage.total"
              @current-change="carCurrentPageChange" @size-change="carPageSizeChange" background layout="total, prev, pager, next, sizes,  jumper">
            </el-pagination>
          </div>
        </div>
      </div>
      <div style="background-color: #eeeff1;height: 15px;"></div>
      <div class="member-owner-block">
        <div class="sub-title">会员资产</div>
        <div style="margin: 20px">
          <el-block-panel>
            <el-block-panel-item>
              <div class="item">积分</div>
              <div class="item" style="font-size: 28px;">{{dtl.points | fmt}}</div>
            </el-block-panel-item>
            <el-block-panel-item>
              <div class="item">可用券(张)</div>
              <div @click="doCheckCoupon" class="item" style="font-size: 28px;color: #0000FF;cursor: pointer"
                v-if="dtl.couponCount && dtl.couponCount > 0">{{dtl.couponCount}}</div>
              <div class="item" style="font-size: 28px;text-align: center" v-else>0</div>
            </el-block-panel-item>
            <el-block-panel-item>
              <div class="item">储值余额(元)</div>
              <div @click="doCheckWater" class="item" style="font-size: 28px;color: #0000FF;cursor: pointer"
                v-if="dtl.balance && dtl.balance > 0&&dtl.balanceAccountUid">
                {{ dtl.balance | fmt }}
              </div>
              <div style="font-size: 28px;text-align: center" v-else-if="dtl.balance && dtl.balance > 0&& !dtl.balanceAccountUid">
                {{ dtl.balance | fmt }}
              </div>
              <div style="font-size: 28px;text-align: center" v-else>0.00</div>
            </el-block-panel-item>
            <el-block-panel-item>
              <div class="item">预付卡(张)</div>
              <div @click="doCheckPrePayCard" class="item" style="font-size: 28px;color: #0000FF;cursor: pointer"
                v-if="dtl.cardCount && dtl.cardCount > 0">{{dtl.cardCount}}</div>
              <div style="font-size: 28px;text-align: center" v-else>0</div>
            </el-block-panel-item>
          </el-block-panel>
        </div>
      </div>
      <div style="background-color: #eeeff1;height: 15px;"></div>
      <div class="info-block">
        <div class="sub-title">资料信息</div>
        <div style="margin: 20px">
          <el-row>
            <el-col :span="8">
              <div style="line-height: 36px">昵称：{{dtl.nickName | strFormat}}</div>
            </el-col>
            <el-col :span="8">
              <div style="line-height: 36px">性别：{{dtl.gender ? dtl.gender : '未知'}}</div>
            </el-col>
            <el-col :span="8">
              <div style="line-height: 36px">生日：{{dtl.birthday | dateFormate2}}</div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <div style="line-height: 36px">年龄：{{dtl.age | strFormat}}</div>
            </el-col>
            <el-col :span="8">
              <div style="line-height: 36px">身份证号：{{dtl.idCard | strFormat}}</div>
            </el-col>
            <el-col :span="8">
              <div style="line-height: 36px">学历：{{dtl.education | strFormat}}</div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <div style="line-height: 36px">行业：{{dtl.industry | strFormat}}</div>
            </el-col>
            <el-col :span="8">
              <div style="line-height: 36px">年收入：{{dtl.annualIncomeI18n | strFormat}}</div>
            </el-col>
            <el-col :span="8">
              <div style="line-height: 36px">爱好：{{dtl.hobbies | strFormat}}</div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <div style="line-height: 36px">备用手机号：{{dtl.spareMobile | strFormat}}</div>
            </el-col>
            <el-col :span="8">
              <div :title="dtl.email" style="line-height: 36px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">
                邮箱：{{dtl.email | strFormat}}</div>
            </el-col>
            <el-col :span="8">
              <div style="line-height: 36px">生活区域：{{dtl.area | strFormat}}</div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <div style="line-height: 36px">地址：{{dtlAddress | strFormat}}</div>
            </el-col>
            <el-col :span="8">
            </el-col>
            <el-col></el-col>
          </el-row>
          <el-row style="height: 30px"></el-row>
        </div>
        <div style="margin: 20px" v-if="cars && cars.length > 0">
          <el-table :data="cars" ref="userTable" style="width: 100%">
            <el-table-column label="车辆信息" prop="name">
              <template slot-scope="scope">
                <div style="font-size: 16px;font-weight: 500;margin: 10px;margin-left: 0px">车牌号：{{scope.row.number | strFormat}}</div>
                <div>车牌类型：{{scope.row.numberType | strFormat}}</div>
                <div>油品标号：{{scope.row.oils | strFormat}}</div>
              </template>
            </el-table-column>
            <el-table-column prop="loginName">
              <template slot-scope="scope">
                <div style="margin-top: 40px">车辆类型：{{scope.row.type | strFormat}}</div>
                <div>常用单笔加油金额：{{scope.row.defaultOilMoney | fmt}}</div>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="state">
              <template slot-scope="scope">
                <div style="margin-top: 15px">车辆颜色：{{scope.row.color | strFormat}}</div>
              </template>
            </el-table-column>车辆颜色：
          </el-table>
        </div>
      </div>
    </div>

    <EditDataDialog :data="dtl" :dialogShow="editDataFlag" @dialogClose="doEditDataClose" title="编辑会员资料">

    </EditDataDialog>
    <AdjustMemberLevelDialog :data="adjustParams" :dialogShow="adjustMemberLevelFlag" @dialogClose="doEditAjustMemberLevelClose" title="调整会员等级">
    </AdjustMemberLevelDialog>
    <ResetPasswordDialog :dialogShow="resetPasswordFlag" @dialogClose="doResetPwdClose" title="重置支付密码">

    </ResetPasswordDialog>
    <EditTagInfoDialog v-if="editTagFlag" :data="dtl.tags" :dialogShow="editTagFlag" :memberId="dtl.memberId" @dialogClose="doEditTagClose"
      title="编辑会员标签信息">
    </EditTagInfoDialog>
    <CheckCouponDialog :dialogShow="couponFlag" @dialogClose="doCouponClose" title="当前会员可用券"></CheckCouponDialog>
    <CheckPrePayCardDialog :dialogShow="prepayCardFlag" @dialogClose="doPrepayCardClose" title="当前会员持有预付卡">

    </CheckPrePayCardDialog>
    <CheckWater :dialogShow="waterFlag" :uuid="dtl.balanceAccountUid" @dialogClose="doWaterClose" title="会员储值流水">

    </CheckWater>
  </div>
</template>

<script lang="ts" src="./DqshMemberDtl.ts">
</script>

<style lang="scss">
.dqsh-member-dtl {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: hidden;
  .el-table--enable-row-transition .el-table__body td {
    border-bottom: 1px solid #eeeeee !important;
  }
  .dtl-block {
    display: flex;
    background-color: white;
    height: 380px;
    .dtl-block-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      .dtl-block-subItem {
        flex: 1;
        display: flex;
        .content-block {
          flex: 1;
          padding-top: 33px;
          div {
            margin: 5px;
          }
          .title {
            font-size: 20px;
            font-weight: 500;
            margin-bottom: 20px;
          }
        }
        .other {
          padding-top: 30px;
          div {
            margin: 5px;
          }
        }
      }
    }
  }
  .tag-block {
    background-color: white;
    height: auto;
  }
  .ic-block {
    background-color: white;
    height: auto !important;
  }
  .member-owner-block {
    background-color: white;
    height: 200px;
  }
  .sub-title {
    padding: 15px;
  }
  .item {
    text-align: center;
    margin-bottom: 15px;
  }
  .block-panel {
    box-shadow: none !important;
  }
}
</style>