/*
 * @Author: 黎钰龙
 * @Date: 2023-09-19 10:32:52
 * @LastEditTime: 2023-09-19 10:34:08
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\v2\coupon\videonumberActivity\WeiXinBrandResponse.ts
 * 记得注释
 */
import WeiXinBrand from "./WeiXinBrand"

// 微信品牌响应
export default class WeiXinBrandResponse {
  // 品牌库中的品牌信息
  weiXinBrand: WeiXinBrand[] = []
  // 本次翻页的上下文，用于请求下一页
  nextKey: Nullable<string> = null
  // 品牌总数
  totalNum: Nullable<number> = null
}