import IdName from 'model/common/IdName'

export default class PrepayAccount {
  //
  memberId: Nullable<string> = null
  //
  uuid: Nullable<string> = null
  //
  crmCode: Nullable<string> = null
  //
  mobile: Nullable<string> = null
  //
  name: Nullable<string> = null
  account: Nullable<IdName> = null
  //
  state: Nullable<string> = null
  //
  openOrg: Nullable<IdName> = null
  //
  openTime: Nullable<Date> = null
  //
  total: Nullable<number> = null
  //
  balance: Nullable<number> = null
  //
  giftBalance: Nullable<number> = null
}