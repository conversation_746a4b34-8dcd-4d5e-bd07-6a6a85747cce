/*
 * @Author: haiding <EMAIL>
 * @Date: 2025-02-19 10:03:51
 * @LastEditors: haiding <EMAIL>
 * @LastEditTime: 2025-03-21 11:24:01
 * @FilePath: \new-kequn\src\model\grade\growthvalue\GrowthValueConfig.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import GoodsQtyLine from 'model/grade/growthvalue/GoodsQtyLine'
import TradeAmountLine from 'model/grade/growthvalue/TradeAmountLine'
import TradeQtyLine from 'model/grade/growthvalue/TradeQtyLine'
import GoodsRange from "model/common/GoodsRange";
import BDeductionRulesCalculator from 'model/grade/growthvalue/BDeductionRulesCalculator';

export default class GrowthValueConfig {
  // 成长值上限：null为无上限
  upperLimited: Nullable<number> = null
  // 客单价成长值
  tradeAmountLines: TradeAmountLine[] = []
  // 消费频次成长值
  tradeQtyLines: TradeQtyLine[] = []
  // 购买数量成长值
  goodsQtyLines: GoodsQtyLine[] = []
  // 状态
  state: Nullable<string> = null
  // 操作人
  operator: Nullable<string> = null
  // 活动商品
  tradeAmountUseGoods: Nullable<GoodsRange> = new GoodsRange();
  // C端成长值规则展示
  growthValueRuleDesc: Nullable<string> = null

  // 新增 
  // 成长值名称
  title: Nullable<string> = null
  // 扣减规则
  deductionRule: Nullable<BDeductionRulesCalculator> = null;
}