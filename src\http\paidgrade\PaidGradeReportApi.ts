import ApiClient from 'http/ApiClient'
import OrgPaidGradeDailyData from 'model/paidgrade/OrgPaidGradeDailyData'
import OrgPaidGradeData from 'model/paidgrade/OrgPaidGradeData'
import OrgPaidGradeHst from 'model/paidgrade/OrgPaidGradeHst'
import OrgPaidGradeReportFilter from 'model/paidgrade/OrgPaidGradeReportFilter'
import OrgPaidGradeStats from 'model/paidgrade/OrgPaidGradeStats'
import Response from 'model/common/Response'

export default class PaidGradeReportApi {
  /**
   * 付费等级日报
   *
   */
  static queryDaily(body: OrgPaidGradeReportFilter): Promise<Response<OrgPaidGradeDailyData[]>> {
    return ApiClient.server().post(`/v1/member/paid/grade/report/queryDaily`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 付费等级流水报表
   *
   */
  static queryHst(body: OrgPaidGradeReportFilter): Promise<Response<OrgPaidGradeHst[]>> {
    return ApiClient.server().post(`/v1/member/paid/grade/report/queryHst`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 付费等级日报
   *
   */
  static querySum(body: OrgPaidGradeReportFilter): Promise<Response<OrgPaidGradeData[]>> {
    return ApiClient.server().post(`/v1/member/paid/grade/report/querySum`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 付费登记报表数据总览
   *
   */
  static stats(): Promise<Response<OrgPaidGradeStats>> {
    return ApiClient.server().get(`/v1/member/paid/grade/report/get/stats`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 付费等级日报汇总查询
   *
   */
  static statsDaily(body: OrgPaidGradeReportFilter): Promise<Response<OrgPaidGradeStats>> {
    return ApiClient.server().post(`/v1/member/paid/grade/report/statsDaily`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 付费等级流水报表汇总查询
   *
   */
  static statsHst(body: OrgPaidGradeReportFilter): Promise<Response<OrgPaidGradeStats>> {
    return ApiClient.server().post(`/v1/member/paid/grade/report/statsHst`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 付费等级汇总查询
   *
   */
  static statsSum(body: OrgPaidGradeReportFilter): Promise<Response<OrgPaidGradeStats>> {
    return ApiClient.server().post(`/v1/member/paid/grade/report/statsSum`, body, {}).then((res) => {
      return res.data
    })
  }

}
