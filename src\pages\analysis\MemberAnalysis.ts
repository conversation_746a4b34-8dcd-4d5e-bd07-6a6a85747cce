import { Component, Vue, Watch } from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import ListWrapper from 'cmp/list/ListWrapper.vue'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import DateUtil from 'util/DateUtil'
import ReportExportApi from 'http/v2/report/ReportExportApi'
import MemberExportFilter from 'model/v2/report/MemberExportFilter'
import DownloadCenterDialog from 'pages/main/dowmload_center_dialog/DownloadCenterDialog.vue'
import RSOrgFilter from 'model/common/RSOrgFilter'
import OrgApi from 'http/org/OrgApi'
import DataAnalysisV2Api from 'http/analysis/DataAnalysisV2Api'
import DataAnalysisFilter from 'model/analysis/v2/DataAnalysisFilter'
import MemberStats from 'model/analysis/v2/MemberStats'
import MemberIncreaseStats from 'model/analysis/v2/MemberIncreaseStats'
import Echart from 'echarts/lib/echarts'
import 'echarts/lib/chart/bar'
import BreadCrume from 'cmp/bread-crumb/BreadCrume'
import EnvUtil from 'util/EnvUtil'
import OrgCycleReportFilter from 'model/analysis/v2/OrgCycleReportFilter'
import { DateRangeType } from 'model/analysis/v2/DateRangeType'
import OrgCycleData from 'model/analysis/v2/OrgCycleData'
import OrgCycleStats from 'model/analysis/v2/OrgCycleStats'
import OrgCycleReportExportFilter from 'model/v2/report/OrgCycleReportExportFilter'
import CommonUtil from 'util/CommonUtil'
import SelectStores from 'cmp/selectStores/SelectStores'

@Component({
  name: 'MemberAnalysis',
  components: {
    FormItem,
    ListWrapper,
    SubHeader,
    DownloadCenterDialog,
    BreadCrume,
    SelectStores
  }
})
export default class MemberAnalysis extends Vue {
  time = ''
  curStore = ''
  $echarts: any
  barDataArray: any[] = []
  selectDate: any = []
  barChart: any = ''
  $refs: any
  dialogvisiable = false
  options: any = {}
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  analysisList: any[] = []
  stores: any = []
  storeLoading = false
  recordStoreArr: any = []
  analysisCount: MemberIncreaseStats = new MemberIncreaseStats()
  analysisAllCount: MemberStats = new MemberStats()
  showTip: boolean = false
  lineWidth = '450px'
  panelArray: any = []
  orgCycleReportPage = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  orgCycleReportList: OrgCycleData[] = []
  orgCycleReportLoading: boolean = false
  orgCycleReportTime: DateRangeType = DateRangeType.LST_SEVEN_DAY
  DateRangeType = DateRangeType
  statsOrgCycleLoading: boolean = false
  orgCycleStats = new OrgCycleStats()
  orgCurStore: Nullable<string> = null
  analysisTableLoading: boolean = false
  statsMemberIncreaseLoading: boolean = false
  statsMemberConsumeLoading: boolean = false
  analysisSummaryLoading: boolean = false

  get getFirstPercent() {
    if (
      !this.analysisCount.consumeCount ||
      this.analysisCount.consumeCount === 0
    ) {
      return 0
    }
    if (
      !this.analysisCount.consumeFirstCount ||
      this.analysisCount.consumeFirstCount === 0
    ) {
      return 0
    }
    return Number(
      (
        Number(
          this.analysisCount.consumeFirstCount / this.analysisCount.consumeCount
        ) * 100
      ).toFixed(2)
    )
  }

  get getSecPercent() {
    if (
      !this.analysisCount.consumeCount ||
      this.analysisCount.consumeCount === 0
    ) {
      return 0
    }
    if (
      !this.analysisCount.consumeGE2Count ||
      this.analysisCount.consumeGE2Count === 0
    ) {
      return 0
    }
    return Number(
      (
        Number(
          this.analysisCount.consumeGE2Count / this.analysisCount.consumeCount
        ) * 100
      ).toFixed(2)
    )
  }

  get getOrgCycleStatsPercent() {
    if (
      !this.orgCycleStats.consumeCount ||
      this.orgCycleStats.consumeCount === 0
    ) {
      return 0
    }
    if (
      !this.orgCycleStats.consumeGE2Count ||
      this.orgCycleStats.consumeGE2Count === 0
    ) {
      return 0
    }
    return Number(
      (
        Number(
          this.orgCycleStats.consumeGE2Count / this.orgCycleStats.consumeCount
        ) * 100
      ).toFixed(2)
    )
  }

  @Watch('time')
  OnTimeChange(value: string) {
    if (
      value ===
      this.formatI18n('/分析/门店会员增长概况/门店会员增长概况', '自定义')
    ) {
      return
    }
    this.time = value
    this.getAnalysisData()
    this.getStatsMemberIncrease()
    this.getStatsMemberConsume()
  }

  @Watch('orgCycleReportTime', { immediate: true })
  OnOrgCycleReportTimeChange(value: string) {
    this.orgCycleReportPage.currentPage = 1
    this.getOrgCycleReport()
    this.getStatsOrgCycle()
  }

  created() {
    this.panelArray = [
      {
        name: this.formatI18n('/公用/菜单/门店会员增长概况'),
        url: ''
      }
    ]
    this.$echarts = Echart
    if (EnvUtil.isZh_Cn()) {
      this.lineWidth = '450px'
    } else {
      this.lineWidth = '700px'
    }
    this.time = this.formatI18n('/公用/日期', '今天')
    this.options = {
      color: ['#0CC66D', '#5cdff7', '#016CFF'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          // 坐标轴指示器，坐标轴触发有效
          type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
        }
      },
      grid: {
        left: '0%',
        right: '5%',
        bottom: '10%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          data: [
            this.formatI18n(
              '/分析/门店会员增长概况/门店会员增长概况',
              '新增会员'
            ),
            this.formatI18n(
              '/分析/门店会员增长概况/门店会员增长概况/新增门店会员'
            ),
            this.formatI18n(
              '/分析/门店会员增长概况/门店会员增长概况',
              '新增手机号会员'
            )
          ],
          axisTick: {
            alignWithLabel: true
          }
        }
      ],
      yAxis: [
        {
          type: 'value'
        }
      ],
      series: [
        {
          name: '',
          type: 'bar',
          barWidth: '10%',
          data: [],
          itemStyle: {
            normal: {
              // 这里是重点
              color: function(params: any) {
                // 注意，如果颜色太少的话，后面颜色不会自动循环，最好多定义几个颜色
                let colorList = ['#0CC66D', '#5cdff7', '#016CFF']
                return colorList[params.dataIndex]
              }
            }
          }
        }
      ]
    }
    this.getAnalysisSummary()
  }

  mounted() {
    let timer = 0
    document.getElementById('content')!.addEventListener('scroll', () => {
      if (timer) {
        clearTimeout(timer)
      }
      timer = setTimeout(() => {
        this.$refs.storeSelect.blur()
      })
    })
    const ele = document.getElementById('myEcharts')
    this.barChart = this.$echarts.init(ele)
    if (this.barChart) {
      this.barChart.setOption(this.options)
    }
  }

  doStoreChange() {
    this.page.currentPage = 1
    this.getAnalysisData()
    this.getStatsMemberIncrease()
    this.getStatsMemberConsume()
  }

  doOrgStoreChange() {
    this.orgCycleReportPage.currentPage = 1
    this.getOrgCycleReport()
    this.getStatsOrgCycle()
  }

  doDialogClose() {
    this.dialogvisiable = false
  }

  doCustomQuery() {
    if (!this.selectDate || this.selectDate.length <= 0) {
      this.$message.warning(this.formatI18n('/会员/会员资料/请选择日期'))
      return
    }
    this.getAnalysisData()
    this.getStatsMemberIncrease()
    this.getStatsMemberConsume()
  }

  doExport() {
    let params: MemberExportFilter = new MemberExportFilter()
    params.storeIdEquals = this.curStore
    if (this.getTimeParams() === 'TODAY') {
      params.begin = this.getDateRange()[0][0]
      params.end = this.getDateRange()[0][1]
    } else if (this.getTimeParams() === 'LST_SEVEN_DAY') {
      params.begin = this.getDateRange()[1][0]
      params.end = this.getDateRange()[1][1]
    } else if (this.getTimeParams() === 'LST_THIRTY_DAY') {
      params.begin = this.getDateRange()[2][0]
      params.end = this.getDateRange()[2][1]
    } else if (this.getTimeParams() === 'CUSTOM') {
      params.begin = this.selectDate[0]
      params.end = this.selectDate[1]
    } else {
      // todo
    }
    const loading = CommonUtil.Loading()
    ReportExportApi.exportMemberIncrease(params)
      .then((resp: any) => {
        loading.close()
        if (resp && resp.code === 2000) {
          this.showTip = true
          this.dialogvisiable = true
        }
      })
      .catch((error: any) => {
        loading.close()
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
  }

  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.getAnalysisData()
  }

  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.getAnalysisData()
  }

  getDateRange() {
    let arr: any = [
      [
        DateUtil.format(new Date(), 'yyyy-MM-dd'),
        DateUtil.format(new Date(), 'yyyy-MM-dd')
      ],
      [
        DateUtil.format(DateUtil.prevDate(new Date(), 7), 'yyyy-MM-dd'),
        DateUtil.format(DateUtil.prevDate(new Date(), 1), 'yyyy-MM-dd')
      ],
      [
        DateUtil.format(DateUtil.prevDate(new Date(), 30), 'yyyy-MM-dd'),
        DateUtil.format(DateUtil.prevDate(new Date(), 1), 'yyyy-MM-dd')
      ]
    ]
    return arr
  }

  getTimeParams() {
    if (this.time === this.formatI18n('/公用/日期', '今天')) {
      return 'TODAY'
    } else if (this.time === this.formatI18n('/公用/日期', '近7天')) {
      return 'LST_SEVEN_DAY'
    } else if (this.time === this.formatI18n('/公用/日期', '近30天')) {
      return 'LST_THIRTY_DAY'
    } else if (
      this.time ===
      this.formatI18n('/分析/门店会员增长概况/门店会员增长概况', '自定义')
    ) {
      return 'CUSTOM'
    } else {
      return ''
    }
  }

  getAnalysisSummary() {
    this.analysisSummaryLoading = true
    DataAnalysisV2Api.statsMember()
      .then((resp: any) => {
        this.analysisSummaryLoading = false
        if (resp && resp.code === 2000) {
          this.analysisAllCount = resp.data
        }
      })
      .catch((error: any) => {
        this.analysisSummaryLoading = false
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
  }

  // 获取增长概况table数据
  getAnalysisData() {
    this.analysisTableLoading = true
    DataAnalysisV2Api.queryMemberIncrease(this.getParams())
      .then((resp: any) => {
        this.analysisTableLoading = false
        if (resp && resp.code === 2000) {
          this.populateList(resp)
        }
      })
      .catch((error: any) => {
        this.analysisTableLoading = false
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
  }

  // 获取会员增长概况
  getStatsMemberIncrease() {
    this.statsMemberIncreaseLoading = true
    DataAnalysisV2Api.statsMemberIncrease(this.getParams())
      .then((resp: any) => {
        this.statsMemberIncreaseLoading = false
        if (resp) {
          this.analysisCount.newMemberCount = resp.data.newMemberCount
          this.analysisCount.newMobileMemberCount =
            resp.data.newMobileMemberCount
          this.analysisCount.newStoreMemberCount = resp.data.newStoreMemberCount
        }
        this.populateChart()
      })
      .catch((error: any) => {
        this.statsMemberIncreaseLoading = false
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
  }

  // 获取会员交易合计
  getStatsMemberConsume() {
    this.statsMemberConsumeLoading = true
    DataAnalysisV2Api.statsMemberConsume(this.getParams())
      .then((resp: any) => {
        this.statsMemberConsumeLoading = false
        if (resp) {
          this.analysisCount.consumeCount = resp.data.consumeCount
          this.analysisCount.consumeFirstCount = resp.data.consumeFirstCount
          this.analysisCount.consumeGE2Count = resp.data.consumeGE2Count
        }
      })
      .catch((error: any) => {
        this.statsMemberConsumeLoading = false
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
  }

  getParams() {
    let params = new DataAnalysisFilter()
    let dateType = this.getTimeParams()
    if (this.curStore) {
      params.storeIdEquals = this.curStore
    }
    if (dateType === 'CUSTOM') {
      params.beginAfterOrEquals = this.selectDate[0]
      params.endBefore = this.selectDate[1]
    } else {
      params.dateRangeType = dateType
    }
    params.page = this.page.currentPage - 1
    params.pageSize = this.page.size
    return params
  }

  populateList(response: any) {
    if (response && response.data) {
      this.analysisList = response.data
      this.page.total = response.total
    }
  }

  populateChart() {
    // 新增会员数量
    this.barDataArray = [
      this.analysisCount.newMemberCount,
      this.analysisCount.newStoreMemberCount,
      this.analysisCount.newMobileMemberCount
    ]
    if (this.barChart) {
      this.barChart.setOption({
        series: [
          {
            data: this.barDataArray
          }
        ]
      })
    }
  }

  validateDate() {
    if (
      this.time !==
      this.formatI18n('/分析/门店会员增长概况/门店会员增长概况', '自定义')
    ) {
      return true
    }
    if (!this.selectDate[0] || !this.selectDate[1]) {
      this.$message.warning(
        this.formatI18n('/会员/会员资料', '请选择日期') as string
      )
      this.$refs.selectDate.focus()
      return
    }
    let pre = this.selectDate[0]
    let next = this.selectDate[1]
    let threeMonth = DateUtil.addMonth(new Date(), -3)
    if (
      DateUtil.clearTime(new Date(next)).getTime() >
      DateUtil.clearTime(new Date()).getTime()
    ) {
      this.$message.warning(
        this.formatI18n(
          '/分析/门店会员增长概况/门店会员增长概况/筛选时段点击自定义/开始时间和结束时间间隔大于3个月或者结束日期大于今天的提示信息',
          '只支持查询近三个月的数据且不支持查询明天和明天以后的数据'
        ) as string
      )
      this.$refs.selectDate.focus()
      return false
    }
    if (
      DateUtil.clearTime(new Date(pre)).getTime() <
      DateUtil.clearTime(new Date(threeMonth)).getTime()
    ) {
      this.$message.warning(
        this.formatI18n(
          '/分析/门店会员增长概况/门店会员增长概况/筛选时段点击自定义/开始时间和结束时间间隔大于3个月或者结束日期大于今天的提示信息',
          '只支持查询近三个月的数据且不支持查询明天和明天以后的数据'
        ) as string
      )
      this.$refs.selectDate.focus()
      return false
    }
    return true
  }

  // 查询复购分析列表
  async getOrgCycleReport() {
    try {
      this.orgCycleReportLoading = true
      const params = new OrgCycleReportFilter()
      params.page = this.orgCycleReportPage.currentPage - 1
      params.pageSize = this.orgCycleReportPage.size
      params.dateRangeType = this.orgCycleReportTime
      params.storeIdEquals = this.orgCurStore
      params.marketingCenterEquals = sessionStorage.getItem('marketCenter')
      const {
        code,
        data,
        total,
        msg
      } = await DataAnalysisV2Api.queryOrgCycleReport(params)
      this.orgCycleReportLoading = false
      if (code === 2000) {
        this.orgCycleReportList = data ?? []
        this.orgCycleReportPage.total = total ?? 0
      } else {
        this.$message.error(msg as string)
      }
    } catch (error) {
      this.orgCycleReportLoading = false
      this.$message.error((error as Error).message)
    }
  }

  // 查询复购分析图表
  async getStatsOrgCycle() {
    try {
      this.statsOrgCycleLoading = true
      const params = new OrgCycleReportFilter()
      params.dateRangeType = this.orgCycleReportTime
      params.storeIdEquals = this.orgCurStore
      params.marketingCenterEquals = sessionStorage.getItem('marketCenter')
      const { code, data, total, msg } = await DataAnalysisV2Api.statsOrgCycle(
        params
      )
      this.statsOrgCycleLoading = false
      if (code === 2000) {
        this.orgCycleStats = data ?? new OrgCycleStats()
      } else {
        this.$message.error(msg as string)
      }
    } catch (error) {
      this.statsOrgCycleLoading = false
      this.$message.error((error as Error).message)
    }
  }

  // 导出报表
  async doOrgCycleReportExport() {
    const loading = CommonUtil.Loading()
    try {
      const params = new OrgCycleReportExportFilter()
      params.dateRangeType = this.orgCycleReportTime
      params.storeIdEquals = this.orgCurStore
      params.marketingCenterEquals = sessionStorage.getItem('marketCenter')
      const { code, msg } = await ReportExportApi.exportOrgCycleConsumption(
        params
      )
      loading.close()
      if (code === 2000) {
        this.showTip = true
        this.dialogvisiable = true
      } else {
        this.$message.error(msg as string)
      }
    } catch (error) {
      loading.close()
      this.$message.error((error as Error).message)
    }
  }

  orgCycleReportCurrentChange(val: number) {
    this.orgCycleReportPage.currentPage = val
    this.getOrgCycleReport()
  }

  orgCycleReportSizeChange(val: number) {
    this.orgCycleReportPage.size = val
    this.getOrgCycleReport()
  }
}
