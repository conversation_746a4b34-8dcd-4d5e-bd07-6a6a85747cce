/*
 * @Autor: 司浩
 * @Description:
 * @Date: 2023-02-09 15:09:10
 * @LastEditors: 司浩
 * @LastEditTime: 2023-02-10 16:59:54
 * @FilePath: \phoenix-web-ui\src\model\couponPurchase\PurchaseCouponTradeComplaint.ts
 */
// 购券交易纠纷单
export default class PurchaseCouponTradeComplaint {
  // 交易id
  tradeIdId: Nullable<string> = null
  // 交易号命名空间
  tradeIdNamespace: Nullable<string> = null
  // 交易号
  tradeNo: Nullable<string> = null
  // 纠纷单id
  complaintId: Nullable<string> = null
  // 会员id
  memberId: Nullable<string> = null
  // 手机号
  mobile: Nullable<string> = null
  // 状态
  state: Nullable<string> = null
  // 操作类型
  category: Nullable<string> = null
  // 售后原因
  afterSaleReason: Nullable<string> = null
  // 操作人
  operator: Nullable<string> = null
  // 创建时间
  created: Nullable<Date> = null
  // 最后修改时间
  lastModified: Nullable<Date> = null
}
