import { Component, Vue } from 'vue-property-decorator'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import FormItem from 'cmp/formitem/FormItem.vue'
import EditDataDialog from 'pages/custom/dqsh/member/dialog/EditDataDialog.vue'
import AdjustMemberLevelDialog from 'pages/custom/dqsh/member/dialog/AdjustMemberLevelDialog.vue'
import ResetPasswordDialog from 'pages/custom/dqsh/member/dialog/ResetPasswordDialog.vue'
import MemberApi from 'http/member_v2/MemberApi'
import MemberDetail from 'model/member_v2/member/MemberDetail'
import CheckCouponDialog from 'pages/custom/dqsh/member/dialog/CheckCouponDialog.vue'
import CheckPrePayCardDialog from 'pages/custom/dqsh/member/dialog/CheckPrePayCardDialog.vue'
import CheckWater from 'pages/custom/dqsh/member/dialog/CheckWater.vue'
import PrePayConfigApi from 'http/prepay/config/PrePayConfigApi'
import CommonUtil from 'util/CommonUtil'
import EditTagInfoDialog from 'pages/member/data/dialog/EditTagInfoDialog'
@Component({
  name: 'DqshMemberDtl',
  components: {
    BreadCrume,
    FormItem,
    EditDataDialog,
    AdjustMemberLevelDialog,
    ResetPasswordDialog,
    EditTagInfoDialog,
    CheckCouponDialog,
    CheckPrePayCardDialog,
    CheckWater
  }
})
export default class DqshMemberDtl extends Vue {
  get getInvited() {
    if (this.dtl.referee && this.dtl.referee.mobile) {
      return this.dtl.referee.mobile
    } else if (this.dtl.referee && this.dtl.referee.crmCode) {
      return this.dtl.referee.crmCode
    } else {
      return ''
    }
  }
  get getRegisterChannel() {
    // 如果type存在【third，alipay，weixin，store，phoenix】，根据type去取id
    if (this.dtl.registerChannel && this.dtl.registerChannel.type) {
      if (this.dtl.registerChannel.type === 'third') {
        return '第三方'
      } else if (this.dtl.registerChannel.type === 'alipay') {
        return '支付宝'
      } else if (this.dtl.registerChannel.type === 'weixin') {
        return '微信'
      } else if (this.dtl.registerChannel.type === 'store') {
        return '门店注册'
      } else if (this.dtl.registerChannel.type === 'phoenix') {
        return 'CRM'
      } else {
        if (this.dtl.registerChannel && this.dtl.registerChannel.id) {
          return this.dtl.registerChannel.id
        } else {
          return '--'
        }
      }
    } else {
      return '--'
    }
    // 如果type不存在，但是id存在，直接显示id
  }
  get getDay() {
    if (this.dtl) {
      if (this.dtl.lastConsumeDay === 0) {
        return '今天'
      } else if (this.dtl.lastConsumeDay === 1) {
        return '昨天'
      } else {
        return `${this.dtl.lastConsumeDay}天前`
      }
    } else {
      return ''
    }
  }
  dtlAddress = ''
  switchFlag = false
  waterFlag = false
  couponFlag = false
  prepayCardFlag = false
  tags: any = []
  cars: any = []
  adjustParams: any = {}
  dtl: MemberDetail = new MemberDetail()
  icCards: any = []
  editDataFlag = false
  adjustMemberLevelFlag = false
  resetPasswordFlag = false
  editTagFlag = false
  carInfo = []
  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  carPage = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  panelArray = [
    {
      name: '会员资料',
      url: 'dqsh-member-list'
    },
    {
      name: '会员详情',
      url: ''
    }
  ]
  signStatus: string = 'SIGNING'
  whiteState: Nullable<boolean> = null //是否已加入白名单

  created() {
    this.getDtl()
    this.getPrePermission()
  }
  //查询白名单
  getWhiteState(memberId: string) {
    MemberApi.getBalanceAccountWhitelisting(memberId).then((res)=>{
      if(res.code === 2000) {
        this.whiteState = res.data || false
      } else {
        this.$message.error(res.msg || '获取白名单状态失败')
      }
    }).catch((err)=>{
      this.$message.error(err.message || '获取白名单状态失败')
    })
  }
  //加入白名单
  doAddWhiteList() {
    this.$confirm('加入白名单后，该会员将不受消费次数限制，确定要加入吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    }).then(() => {
      MemberApi.addBalanceAccountWhitelisting(this.dtl.memberId || '').then((res)=>{
        if(res.code === 2000) {
          this.$message.success('操作成功')
        } else {
          this.$message.error('操作失败')
        }
      }).catch((err)=>{
        this.$message.error(err.message || '操作失败')
      }).finally(()=>{
        this.getDtl()
      })
    })
  }
  doEdit() {
    this.editDataFlag = true
  }
  doAdjust() {
    this.adjustMemberLevelFlag = true
  }
  doReset() {
    this.resetPasswordFlag = true
  }
  doFreezon() {
    let str = '会员冻结后将无法继续使用和获得权益。请确认是否冻结此会员？'
    if (this.dtl.state === 'Blocked') {
      str = '会员解冻后将可正常使用和获得权益。请确认是否解冻此会员？'
    }
    this.$confirm(str, '提示', {
      confirmButtonText: this.dtl.state === 'Blocked' ? '解冻' : '冻结',
      cancelButtonText: '取消'
    }).then(() => {
      if (this.dtl.state === 'Blocked') {
        MemberApi.unBlock(this.$route.query.id as string).then((resp: any) => {
          if (resp && resp.code === 2000) {
            this.$message.success('解冻成功')
            this.getDtl()
          }
        }).catch((error: any) => {
          if (error && error.message) {
            this.$message.error(error.message)
          }
        })
      } else {
        MemberApi.block(this.$route.query.id as string).then((resp: any) => {
          if (resp && resp.code === 2000) {
            this.$message.success('冻结成功')
            this.getDtl()
          }
        }).catch((error: any) => {
          if (error && error.message) {
            this.$message.error(error.message)
          }
        })
      }
    })
  }
  doTagClose(index: number) {
    this.tags.splice(index, 1)
  }
  doCardTemplate(cardId: string) {
    this.$router.push({ name: 'prepay-card-tpl-dtl', query: { number: cardId } })
  }
  doCheckCoupon() {
    this.couponFlag = true
  }
  doCheckPrePayCard() {
    this.prepayCardFlag = true
  }
  doEditTag() {
    this.editTagFlag = true
  }
  doWaterClose() {
    this.waterFlag = false
  }
  doCheckWater() {
    if (this.switchFlag) { // 开启多账户
      this.$router.push({ name: 'store-value-query', query: { id: this.dtl.crmCode } })
    } else {
      this.waterFlag = true
    }
  }
  doInviteMember() {
    this.$router.push({ name: 'dqsh-member-dtl', query: { id: this.dtl.referee!.memberId } })
    this.getDtl()
  }
  doResetPwdClose() {
    this.resetPasswordFlag = false
  }
  doEditTagClose() {
    this.editTagFlag = false
    this.getDtl()
  }
  doCouponClose() {
    this.couponFlag = false
  }
  doPrepayCardClose() {
    this.prepayCardFlag = false
  }
  doEditDataClose() {
    this.editDataFlag = false
    this.getDtl()
    this.getIcCard()
  }
  doEditAjustMemberLevelClose() {
    this.adjustMemberLevelFlag = false
    this.getDtl()
  }
  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.getIcCard()
  }
  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.getIcCard()
  }
  doUnBind(faceCode: string) {
    this.$confirm('解绑后，系统将重新为IC卡生成新会员，而已有积分、券、等级、成长值均会留在当前会员账户上。请确认是否解绑此IC卡？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    }).then(() => {
      MemberApi.unbind(this.$route.query.id as string, faceCode).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success('解绑成功')
          this.getDtl()
        }
      }).catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    })
  }
  carCurrentPageChange(val: number) {
    this.carPage.currentPage = val
    this.getCarInfo()
  }
  carPageSizeChange(val: number) {
    this.carPage.size = val
    this.getCarInfo()
  }
  private getDtl() {
    const loading = CommonUtil.Loading()
    MemberApi.detail(this.$route.query.id as string).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.dtl = resp.data
        if (this.dtl.province) {
          if (this.dtl.address) {
            this.dtlAddress = this.dtl.province.name + (this.dtl.city ? '/' + this.dtl.city!.name : '') + (this.dtl.district ? '/' + this.dtl.district!.name : '') + (this.dtl.street ? '/' + this.dtl.street!.name : '') + '/' + this.dtl.address
          } else {
            this.dtlAddress = this.dtl.province.name + (this.dtl.city ? '/' + this.dtl.city!.name : '') + (this.dtl.district ? '/' + this.dtl.district!.name : '') + (this.dtl.street ? '/' + this.dtl.street!.name : '')
          }

        } else {
          if (this.dtl.address) {
            this.dtlAddress = this.dtl.address
          } else {
            this.dtlAddress = ''
          }
        }
        this.cars = resp.data.cars
        this.adjustParams = {
          level: this.dtl.gradeCode ? `[${this.dtl.gradeCode}]${this.dtl.gradeName}` : '',
          date: this.dtl.gradeValidate
        }
        this.getTags()
        this.getIcCard()
        this.getCarInfo()
        this.getWhiteState(this.dtl.memberId ||'')
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    }).finally(()=>{
      loading.close()
    })
  }

  private getTags() {
    this.tags = []
    MemberApi.getMemberTag(this.$route.query.id as string).then((res) => {
      if (res.code === 2000) {
        this.dtl.tags = res.data || []
        res.data?.forEach((item: any) => {
          let obj: any = {}
          if (item && item.tagValues && item.tagValues.length > 0) {
            item.tagValues.forEach((subItem: any) => {
              obj = {
                tagName: item.tagId,
                tagValue: ''
              }
              obj.tagValue = subItem
              this.tags.push(obj)
            })
          }
        })
      } else {
        this.$message.error(res.msg || this.i18n('获取标签信息失败'))
      }
    }).catch((error) => {
      this.$message.error(error.message || this.i18n('获取标签信息失败'))
    })
  }
  private getIcCard() {
    MemberApi.queryCard(this.dtl.memberId as string, this.page.currentPage - 1, this.page.size).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.icCards = resp.data
        this.page.total = resp.total
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  private getCarInfo() {
    MemberApi.queryCarInfo(this.dtl.memberId as string, this.carPage.currentPage - 1, this.carPage.size, this.signStatus).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.carInfo = resp.data
        this.carPage.total = resp.total
      } else {
        this.carInfo = []
        this.carPage.total = 0
      }
    }).catch((error: any) => {
      this.carInfo = []
      this.carPage.total = 0
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  private getPrePermission() {
    PrePayConfigApi.get().then((resp: any) => {
      if (resp && resp.code === 2000) {
        if (resp.data && resp.data.enableMultipleAccount) { // 开启多账户
          this.switchFlag = true
        } else {
          this.switchFlag = false // 未开启多账户
        }
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
}