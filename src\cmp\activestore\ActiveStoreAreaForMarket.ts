import { Component, Inject, Prop, Vue, Watch } from "vue-property-decorator";
import ImportDialog from "cmp/importdialog/ImportDialog.vue";
import IdName from "model/entity/IdName";
import StoreSelectorDialog from "cmp/selectordialogs/StoreSelectorDialog.vue";
import RSOrg from "model/common/RSOrg";
import ImportResultDialog from "pages/deposit/mbrdeposit/active/dialog/ImportResultDialog.vue";
import PromotionCenterSelectorDialog from "cmp/selectordialogs/PromotionCenterSelectorDialog.vue";
import StoreRange from "model/common/StoreRange";
import BrowserMgr from "mgr/BrowserMgr";
import RSMarketingCenter from "model/common/RSMarketingCenter";
import StoreMulPromotionSelectorDialog from "cmp/selectordialogs/StoreMulPromotionSelectorDialog.vue";
import LimitedMarketingCenter from "model/common/LimitedMarketingCenter";
import NoMarketCenter from "./NoMarketCenter";
import Zone from "model/datum/zone/Zone";
import ZoneSelectorDialog from "cmp/selectordialogs/ZoneSelectorDialog";
import OrgFilter from "model/datum/org/OrgFilter";
import OrgApi from "http/org/OrgApi";
import RSOrgFilter from "model/common/RSOrgFilter";
import LimitedZones from "model/common/LimitedZones";
import I18nPage from "common/I18nDecorator";

class CustomStore {
	// 控制营销中心展示
	promotionCenter: boolean;
	storeRange: StoreRange;
}

class ExportResult {
	importResult: boolean;
	backUrl: string;
	errorCount: number;
	ignoreCount: number;
	successCount: number;
}

class AreaStoreRange extends Zone {
	store: Nullable<StoreRange> = null
}

export { ExportResult };

@Component({
	name: "ActiveStoreForMarket",
	components: {
		ImportDialog,
		StoreSelectorDialog,
		ImportResultDialog,
		PromotionCenterSelectorDialog,
		StoreMulPromotionSelectorDialog,
		NoMarketCenter,
		ZoneSelectorDialog
	},
})
@I18nPage({
  prefix: [
    "/公用/券模板",
    '/公用/门店组件',
    '/权益/积分/积分初始化/未初始化状态/得积分规则/基础得积分/点击立即设置',
    '/资料/区域',
    '/营销/积分活动/门店积分兑换/新建门店积分兑换活动',
    '/储值/预付卡/预付卡充值有礼'
  ],
  auto: true
})
export default class ActiveStoreAreaForMarket extends Vue {
	promotionCenter = false; // 是否开启营销中心
	$refs: any;
	store: StoreRange = new StoreRange();
	currentMarketCenter: LimitedMarketingCenter = new LimitedMarketingCenter();
	importDialogShow = false;
	importUrl = "v1/org/importExcel";
	@Prop()
	showTip: boolean;
	// 是否展示与活动门店一致
	@Prop({
		type: Boolean,
		default: false,
	})
	sameStore: boolean;
	reloadList = false;
	inputValueRules: any;
	marketCenterRules: any;

	comeValue: any = {};

	@Prop({
		type: Boolean,
		default: true,
	})
	internalValidate: boolean;
	@Prop()
	value: CustomStore;
	importResult: ExportResult = new ExportResult();
	importResultDialogShow = false;
	headquarters: Nullable<string> = null
	areaList: AreaStoreRange[] = []

	@Prop({
		type: String,
		default: sessionStorage.getItem('marketCenter')
	})
	marketCenter: string

	@Inject({
		from: 'showAll',
		default: false
	})
	showAll: Boolean

	@Watch("value", { deep: true, immediate: true })
	onValueChange(value: StoreRange) {
		console.log(value);
		this.comeValue = value;
		let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
		if (sysConfig) {
			this.promotionCenter = sysConfig.enableMultiMarketingCenter;
		}
		console.log(this.promotionCenter);
		if (this.promotionCenter) {
			if (!value.storeRangeType) {
				this.store = new StoreRange();
				this.store.storeRangeType = "PART";
			} else {
				this.store = JSON.parse(JSON.stringify(value));
				this.doSetIdName();
				this.doBindValue()
			}
			// this.setMarketCenter();
		}
	}
	get marketCenterId() {
		if (this.currentMarketCenter && this.currentMarketCenter.marketingCenter) {
			return this.currentMarketCenter.marketingCenter.id;
		}
		return null;
	}
	get marketCenterName() {
		if (this.currentMarketCenter && this.currentMarketCenter.marketingCenter) {
			return this.currentMarketCenter.marketingCenter.name;
		}
		return null;
	}
	get templatePath() {
		if (location.href.indexOf("localhost") === -1) {
			return "template_specify_stores.xlsx";
		} else {
			return "template_specify_stores.xlsx";
		}
	}
	created() {
		if (this.sameStore) {
			this.store.storeRangeType = "SAME";
		}
		let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
		if (sysConfig) {
			this.promotionCenter = sysConfig.enableMultiMarketingCenter;
		}
		this.headquarters = sessionStorage.getItem('headquarters')
		this.inputValueRules = [
			{
				validator: (rule: any, value: string, callback: any) => {
					if (this.store.storeRangeType !== "ALL" && this.store.marketingCenters[0].stores!.stores.length === 0) {
						callback(new Error(this.formatI18n("/营销/积分活动/积分活动/商品满额加送积分/表单校验/提示信息/请选择门店范围")));
					}
					callback();
				},
				trigger: ["change", "blur"],
			},
		];
		this.marketCenterRules = [
			{
				validator: (rule: any, value: string, callback: any) => {
					if (this.promotionCenter) {
						if (!this.store.marketingCenters || this.store.marketingCenters.length === 0) {
							callback(new Error(this.formatI18n("/营销/积分活动/积分活动/商品满额加送积分/表单校验/提示信息/请选择营销中心范围")));
							return;
						}
					}
					callback();
				},
				trigger: ["change", "blur"],
			},
		];
	}

	doBindValue() {
		console.log(this.store);
		this.areaList = []
		this.store.zones.forEach((item: LimitedZones, index: number) => {
			let str = ''
			item.stores!.stores.forEach((item: IdName) => {
				if (item && item.id) {
					str += item.id + `[${item.name}];`;
				}
			});

			// let innerStore = new StoreRange()
			// innerStore.storeRangeType = item.stores!.storeRangeType
			// innerStore.zones.push({
			// 	stores: item.stores,
			// 	zones: item.zones,
			// 	storesValue: str
			// })

			// this.store..push({
			// 	store: innerStore,
			// 	zone: item.zones as IdName,
			// 	marketingCenter: '',
			// 	state: null
			// })
			item.storesValue = str
		})
		// console.log(this.areaList);

	}

	validate() {
		if (this.promotionCenter) {
			return this.$refs.form.validate();
		} else {
			return this.$refs.noMarket.validate();
		}
	}

	doClearStore(index: number) {
		let item = this.store.zones[index]
		if (item.stores) {
			item.stores.stores = [];
			item.storesValue = "";
			this.doCommitData()
		}
		this.$forceUpdate()
	}

	// setMarketCenter() {
	// 	// created方法会比这个晚 所以先取一遍
	// 	let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
	// 	if (sysConfig) {
	// 		this.promotionCenter = sysConfig.enableMultiMarketingCenter;
	// 	}
	// 	if (this.promotionCenter) {
	// 		if (!this.store.marketingCenters || this.store.marketingCenters.length === 0) {
	// 			const mc: LimitedMarketingCenter = new LimitedMarketingCenter();
	// 			mc.stores = new StoreRange();
	// 			mc.marketingCenter = {
	// 				id: sessionStorage.getItem("marketCenter"),
	// 				name: sessionStorage.getItem("marketCenterName"),
	// 			};
	// 			const arr: LimitedMarketingCenter[] = [];
	// 			mc.stores.storeRangeType = "ALL";
	// 			arr.push(mc);
	// 			this.store.marketingCenters = arr;
	// 			this.doEmitInput();
	// 		}
	// 	}
	// }

	deleteZone(index: number) {
    this.$confirm(this.formatI18n('/公用/活动/提示信息/确认要删除吗？'), this.formatI18n('/公用/活动/提示信息/提示'), {
      confirmButtonText: this.formatI18n('/权益/券/券模板/确定'),
      cancelButtonText: this.formatI18n('/权益/券/券模板/取消'),
		}).then(() => {
			this.store.zones.splice(index, 1)
			this.doCommitData()
		})

	}

	setMarketCenterIndex(index: number) {
		let item = this.areaList[index]
		if (item.store) {
			if (!item.store.marketingCenters || item.store.marketingCenters.length === 0) {
				const mc: LimitedMarketingCenter = new LimitedMarketingCenter();
				mc.stores = new StoreRange();
				mc.marketingCenter = {
					id: sessionStorage.getItem("marketCenter"),
					name: sessionStorage.getItem("marketCenterName"),
				};
				const arr: LimitedMarketingCenter[] = [];
				mc.stores.storeRangeType = "ALL";
				mc.stores.zones.push({
					zones: item.zone,
					stores: new StoreRange(),
					storesValue: ''
				})
				arr.push(mc);
				item.store.marketingCenters = arr;
				this.doEmitInput();
			}
		}

	}

	doImport() {
		this.importDialogShow = true;
	}
	doSelect(index: number) {
		let item = this.store.zones[index]
		console.log(item);

		let goodsData: RSOrg[] = [];
		if (item.stores && item.stores.stores && item.stores.stores.length > 0) {
			item.stores.stores.forEach((item: IdName) => {
				let obj: RSOrg = new RSOrg();
				obj.org = new IdName();
				obj.org.id = item.id;
				obj.org.name = item.name;
				goodsData.push(obj);
			});
		}
		this.$refs.selectGoodsScopeDialog[index].open(goodsData, "multiple");
	}
	openPromotionCenterDialog() {
		// todo 这里缺少  营销中心类型  需要调整
		this.$refs.selectPromotionCenterSelectorDialog.open(this.store.marketingCenters, "multiple");
	}
	openZone() {
		this.store.zones.forEach((item, index)=>{
			// @ts-ignore
			item.zone = {
				id: item.zones!.id,
				name: item.zones!.name
			}
		})
		this.$refs.zoneSelectorDialog.open(this.store.zones, 'multiple')
	}
	doPromotionSubmitGoods(arr: RSMarketingCenter[]) {
		// todo 这里缺少  营销中心类型  需要调整
		let marketingCentersMap: any = {};
		for (let item of this.store.marketingCenters) {
			if (item!.marketingCenter!.id) {
				marketingCentersMap[item!.marketingCenter!.id] = item;
			}
		}
		let result = [];
		for (let mc of arr) {
			let id = mc!.marketingCenter!.id;
			if (!id) {
				continue;
			}
			if (marketingCentersMap[id]) {
				// 没选就添加
				result.push(marketingCentersMap[id]);
			} else {
				let center = new LimitedMarketingCenter();
				center.stores = new StoreRange();
				center.marketingCenter = mc.marketingCenter;
				result.push(center);
			}
		}
		this.store.marketingCenters = result;
		this.reloadList = true;
		this.$nextTick(() => {
			this.doEmitInput();
		});
		setTimeout(() => (this.reloadList = false), 10);
	}
	doSubmitGoods(arr: RSOrg[], index: number) {
		let item = this.store.zones[index]
		let stores: IdName[] = [];
		let str = "";
		if (arr && arr.length > 0) {
			arr.forEach((item: any) => {
				if (item && item.org && item.org.id) {
					str += item.org.id + `[${item.org.name}];`;
					let obj: IdName = new IdName();
					obj.id = item.org.id;
					obj.name = item.org.name;
					stores.push(obj);
				}
				if (item && item.id) {
					str += item.id + `[${item.name}];`;
					let obj: IdName = new IdName();
					obj.id = item.id;
					obj.name = item.name;
					stores.push(obj);
				}
			});
		}
		// this.store.stores = stores;
		if (item.stores && item.stores.stores) {
			item.stores.stores = stores;
			item.storesValue = str;
		}
		console.log(item);
		
		this.$nextTick(() => {
			this.$forceUpdate()
			this.doEmitInput();
			this.$refs.form.validate();
			this.doCommitData()
		});
		
	}
	doSubmitZones(arr: LimitedZones[]) {
		console.log(arr)
		let array: LimitedZones[] = []
		arr.forEach((item: LimitedZones) => {
			if (!item.stores) {
				item.stores = new StoreRange()
				item.stores.storeRangeType = 'ALL'
				this.doEmitInput();
			}
			if (!item.zones) {
				item.zones = {
					// @ts-ignore
					id: item.zone.id,
					// @ts-ignore
					name: item.zone.name
				}
			}
			array.push(item)
		})
		this.store.zones = array
		console.log(this.store)
		this.$forceUpdate()
		this.doCommitData()
	}
	doStoreRange(index: number) {
		let item = this.store.zones[index]
		console.log(item)
		if (item.stores) {
			if (item.stores.storeRangeType === "ALL") {
				item.stores = new StoreRange();
				item.stores.storeRangeType = 'ALL'
				this.doEmitInput();
			} else if (item.stores.storeRangeType === "PART") {
				item.stores = new StoreRange();
				item.stores.storeRangeType = 'PART'
				this.doEmitInput();
			} else if (item.stores.storeRangeType === "EXCLUDE") {
				item.stores = new StoreRange();
				item.stores.storeRangeType = 'EXCLUDE'
				this.doEmitInput();
			} else {
				item.stores = new StoreRange();
				this.doEmitInput();
			}

			item.storesValue = ''
			this.$refs.form.validate();
			console.log(item)
			this.$forceUpdate()
			this.doCommitData()
		}
	}
	doPromCenterStoreRange(marketCenter: LimitedMarketingCenter) {
		let storeRangeType = marketCenter!.stores!.storeRangeType;
		marketCenter.storesValue = "";
		marketCenter.stores = new StoreRange();
		marketCenter.stores.storeRangeType = storeRangeType;
		marketCenter.stores.stores = [];
		this.doEmitInput();
		this.$refs.form.validate();
	}
	/**
	 * 导入成之后
	 * @param response
	 */
	doUploadSuccess(response: any) {
		// if (response.response.code === 2000) {
		// 	this.importResultDialogShow = true;
		// 	this.importResult = new ExportResult();
		// 	if (response.response.data) {
		// 		this.importResult.importResult = response.response.data.success;
		// 		this.importResult.backUrl = response.response.data.backUrl;
		// 		this.importResult.errorCount = response.response.data.errorCount;
		// 		this.importResult.ignoreCount = response.response.data.ignoreCount;
		// 		this.importResult.successCount = response.response.data.successCount;
		// 		this.doSubmitGoods(response.response.data.orgs);
		// 	}
		// } else {
		// 	this.$message.error(response.response.msg);
		// }
	}
	getStoreCount(count: number) {
		let str: any = this.formatI18n("/公用/券模板", "已选择{0}家门店");
		str = str.replace(/\{0\}/g, count);
		return str;
	}
	private doSetIdName() {
		if (!this.store.marketingCenters) {
			return;
		}
		for (let marketingCenter of this.store.marketingCenters) {
			let arr = marketingCenter!.stores!.stores;
			let str = "";
			if (arr && arr.length > 0) {
				if (arr && arr.length > 0) {
					arr.forEach((item: any) => {
						if (item && item.org && item.org.id) {
							str += item.org.id + `[${item.org.name}];`;
						}
						if (item && item.id) {
							str += item.id + `[${item.name}];`;
						}
					});
				}
			}
			marketingCenter.storesValue = str;
		}
	}
	private doEmitInput() {
		this.doSetStoreRangeLimitType();
		// this.submit();
	}
	// private submit() {
	// 	if (this.promotionCenter) {
	// 		this.$emit("input", this.store);
	// 	} else {
	// 		this.$emit("input", this.comeValue);
	// 	}
	// 	this.$emit("change");
	// }
	private doSetStoreRangeLimitType() {
		// if (this.promotionCenter) {
		// 	this.store.storeRangeLimitType = "MARKETING_CENTER";
		// } else {
		// 	this.store.storeRangeLimitType = "STORE";
		// }
		if (this.store.marketingCenters && this.store.marketingCenters.length > 0) {
			for (let item of this.store.marketingCenters) {
				item.stores!.storeRangeLimitType = "ZONE";
			}
		}
		this.store.storeRangeLimitType = "ZONE";
	}

	private parentChange() {
		this.$emit("change");
	}

	commitNoMC(val: any) {
		this.$emit('input', val)
	}

	doCommitData() {
		this.$nextTick(()=>{
			this.$forceUpdate()
		})
		console.log(this.store)
		this.$emit('change', this.store)
	}

	doValidate() {
		if (this.store.zones.length === 0) {
			this.$message.warning(this.formatI18n('/公用/门店组件/请至少选择一个区域'))
			return Promise.reject()
		}
		for (let index = 0; index < this.store.zones.length; index++) {
			const item = this.store.zones[index];
			console.log(item);
			
			if (item.stores!.storeRangeType !== 'ALL' && (item.stores!.stores === null || item.stores!.stores.length === 0)) {
				console.log('???');
				this.$message.warning(this.formatI18n('/储值/会员储值/门店储值管理/请至少选择一个门店'))
				return Promise.reject()
			}
		}
		// this.store.zones.forEach((item, index) => {
		// 	console.log(item);
			
		// 	if (item.stores!.storeRangeType !== 'ALL' && (item.stores!.stores === null || item.stores!.stores.length === 0)) {
		// 		console.log('???');
				
		// 		this.$message.warning('请至少选择一个门店')
		// 		return Promise.reject()
		// 	}
		// })
	}
}
