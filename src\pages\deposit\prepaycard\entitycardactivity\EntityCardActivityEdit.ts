import { Component, Vue } from 'vue-property-decorator'
import GiftCardActivity from 'model/card/activity/GiftCardActivity'
import { EntityCardActivityEditForm, SaleSpecFormData } from './EntityCardActivityEditForm'
import DateUtil from "util/DateUtil";
import I18nPage from "common/I18nDecorator";
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import ActiveStore from "cmp/activestore/ActiveStore";
import ActivityTopic from 'model/v2/controller/points/topic/ActivityTopic'
import ActivityTopicApi from 'http/v2/controller/points/topic/ActivityTopicApi'
import ActiveStoreDtl from 'cmp/activestoredtl/ActiveStoreDtl'
import ActivePresentCard from 'cmp/active-present-card/ActivePresentCard'
import EntityCardActivityApi from 'http/card/activity/EntityCardActivityApi';
import ActivityDateTimeConditionPicker from "cmp/date-time-condition-picker/ActivityDateTimeConditionPicker";

@Component({
  name: 'EntityCardActivityEdit',
  components: {
    BreadCrume,
    ActiveStore,
    ActiveStoreDtl,
    ActivePresentCard,
    ActivityDateTimeConditionPicker
  }
})
@I18nPage({
  prefix: [
    '/储值/预付卡/电子礼品卡活动/编辑页面',
    '/储值/预付卡/电子礼品卡活动/编辑页面/选中的卡模板信息组件',
    '/卡/卡活动/实体卡售卡活动',
    '/公用/活动/状态',
    '/公用/活动/活动信息',
    '/公用/活动/提示信息',
    '/公用/js提示信息',
    '/公用/按钮',
  ],
})
export default class EntityCardActivityEdit extends Vue {
  i18n: (str: string, params?: string[]) => string
  panelArray: any = []
  activityId: string = '' //活动号
  editType: string = '' // 新建,修改,复制
  $refs: any
  form: EntityCardActivityEditForm = new EntityCardActivityEditForm()
  detail: GiftCardActivity = new GiftCardActivity() // 用于编辑复制回显数据
  loading = false
  themes: ActivityTopic[] = [];
  editFlag: Boolean = false

  get dateRangeOption() {
    return {
      disabledDate(time: any) {
        return time.getTime() < DateUtil.nowDayTime()
      }
    }
  }

  get isShowSaveAndAudit() {
    const permission = this.hasOptionPermission('/卡/卡活动/实体卡售卡活动', '活动审核')
    return permission && !this.loading && !this.editFlag && (this.detail.body?.state !== 'UNSTART' || this.editType === '复制')
  }

  created() {
    this.form.init(this)
    this.editType = '新建'
    let editType = this.$route.query.editType
    this.panelArray = [
      {
        name: this.i18n("实体卡售卡活动"),
        url: "entity-card-activity",
      },
      {
        name:
          editType === "新建" || editType === "复制" || !editType
            ? this.i18n("新建实体卡售卡活动")
            : this.i18n("修改实体卡售卡活动"),
        url: "",
      },
    ];
    this.getTheme()
  }

  mounted() {
    this.activityId = this.$route.query.activityId as string
    let editType = this.$route.query.editType
    if (editType) {
      this.editType = editType as string
    }
    if (['修改', '复制'].indexOf(this.editType) > -1) {
      this.getDetail()
    }
  }

  getTheme() {
    ActivityTopicApi.listTopic().then((resp: any) => {
      if (resp.code === 2000) {
        this.themes = resp.data;
      } else {
        throw new Error(resp.msg)
      }
    }).catch((error) => this.$message.error(error.message || this.i18n('内部异常')))
  }

  getDetail() {
    this.loading = true
    EntityCardActivityApi.info(this.activityId).then((resp: any) => {
      if (resp.code === 2000) {
        this.form.data.saleSpecs = []
        this.detail = resp.data
        this.form.of(resp.data)
        if (this.editType === '修改' && this.detail.body!.state !== 'INITAIL') {
          this.editFlag = true
        }
        let arr: any[] = [...this.form.data.saleSpecs]
        this.form.data.saleSpecs = []
        arr.forEach((item: any) => {
          let spec = new SaleSpecFormData()
          spec.cardTemplateNumber = item.cardTemplateNumber
          spec.cardTemplateName = item.cardTemplateName
          spec.discount = item.discount
          spec.templatePrice = item.templatePrice
          spec.total = item.total
          spec.gift = item.gift
          spec.faceAmount = item.faceAmount
          spec.checked = false // 是否选中
          spec.limit = item.limit // 发售限制
          spec.givePoints = item.givePoints // 赠送积分
          spec.giveCoupons = item.giveCoupons
          spec.priceTmp = item.price
          spec.price = item.price
          spec.count = item.count
          spec.cardTemplateType = item.cardTemplateType
          this.form.data.saleSpecs.push(spec)
        })
        this.$forceUpdate()
        this.$refs.activePresentCard?.setValue(this.form.data.saleSpecs)
      } else {
        this.$message.error(resp.msg)
      }
    }).catch((error) => this.$message.error(error.message || this.i18n('内部异常'))).finally(() => {
      this.loading = false
    })
  }

  save() {
    const promiseArr = []
    promiseArr.push(this.$refs.form?.validate())
    promiseArr.push(this.$refs.activePresentCard?.doValidate())
    promiseArr.push(this.$refs.activityDateTimeConditionPicker.validate())
    Promise.all(promiseArr).then((res) => {
      let body = this.form.toParams()
      let saveMethod = ['修改'].indexOf(this.editType) > -1 ? EntityCardActivityApi.modify : EntityCardActivityApi.create
      this.loading = true
      if (['修改'].indexOf(this.editType) > -1 && body.body) {
        body.body.activityId = this.detail.body?.activityId
      }
      saveMethod(body).then((resp: any) => {
        if (resp.code === 2000) {
          this.$message.success(this.i18n('保存成功'))
          this.$router.push({ name: 'entity-card-activity-dtl', query: { activityId: resp.data } })
        } else {
          this.$message.error(resp.msg)
        }
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      }).finally(() => {
        this.loading = false
      })
    })
  }

  saveAndAudit() {
    let m = ['修改'].indexOf(this.editType) > -1 ? EntityCardActivityApi.modify : EntityCardActivityApi.create
    let body = this.form.toParams()
    if (['修改'].indexOf(this.editType) > -1 && body.body) {
      body.body.activityId = this.detail.body?.activityId
    }
    const promiseArr = []
    promiseArr.push(this.$refs.form?.validate())
    promiseArr.push(this.$refs.activePresentCard?.doValidate())
    Promise.all(promiseArr).then((res) => {
      this.loading = true
      m(body).then(async (resp: any) => {
        if (resp.code === 2000) {
          await this.audit(resp.data)
        } else {
          this.$message.error(resp.msg)
        }
      }).catch((error) => this.$message.error(error.message || this.i18n('内部异常'))).finally(() => {
        this.loading = false
      })
    })
  }

  audit(activityId: string) {
    return EntityCardActivityApi.audit(activityId).then((resp: any) => {
      if (resp.code === 2000) {
        this.$message.success(this.i18n('保存并审核成功'))
        this.$router.push({ name: 'entity-card-activity-dtl', query: { activityId: activityId } })
      } else {
        this.$message.error(resp.msg)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  //卡类型切换后，已选择的卡模板要清空
  cardTypeChange() {
    this.form.data.saleSpecs = []
    this.$refs.activePresentCard?.setValue([])
  }
}
