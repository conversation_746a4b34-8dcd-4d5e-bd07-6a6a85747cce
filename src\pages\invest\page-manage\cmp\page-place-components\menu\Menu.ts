/*
 * @Author: 黎钰龙
 * @Date: 2024-08-01 16:15:02
 * @LastEditTime: 2025-06-24 11:02:07
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\cmp\page-place-components\menu\Menu.ts
 * 记得注释
 */
import { Vue, Component, Prop } from 'vue-property-decorator';
import PlaceTemplateMixins from '../PlaceTemplateMixins';

@Component({
  name: 'Menu',
  components: {},
  mixins: [PlaceTemplateMixins],
})
export default class Menu extends Vue {
  @Prop()
  componentItem: any;
  mounted() {
    console.log(this.componentItem, 'componentItem');
  }
  get bgUrl() {
    return   'delivery/home_page_top_new.png';
  }
  get localProperty() {
    return this.componentItem.props;
  }

  getIcon(item: any) {
    const iconMap: Record<string, string> = {
      personalData: require('@/assets/image/fellow/ic_gerenziliao.png'),
      accountSettings: require('@/assets/image/fellow/ic_account.png'),
      myPoints: require('@/assets/image/fellow/ic_jifenshangcheng.png'),
      myStoredValue: require('@/assets/image/fellow/ic_chuzhichongzhi.png'),
      myCoupon: require('@/assets/image/fellow/ic_lingquanzhongxin.png'),
      myGiftCard: require('@/assets/image/fellow/ic_lipinkashangcheng.png'),
      exchangeCode: require('@/assets/image/fellow/ic_duihuanma.png'),
    };

    if (item.systemMenu) {
      return item.icon || iconMap[item.id] || null;
    } else {
      return item.icon || null;
    }
  }
}
