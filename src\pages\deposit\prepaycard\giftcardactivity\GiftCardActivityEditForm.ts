import GiftCardSaleSpecs from 'model/card/activity/GiftCardSaleSpecs';
import GiftCardActivity from 'model/card/activity/GiftCardActivity';
import ActivityBody from 'model/common/ActivityBody';
import GiftCardRuleDetail from 'model/card/activity/GiftCardRuleDetail';
import DateUtil from 'util/DateUtil';
import StoreRange from "model/common/StoreRange";
import ActivityDateTimeCondition from "model/common/ActivityDateTimeCondition";


class SaleSpecFormData extends GiftCardSaleSpecs {
  checked: boolean = false // 是否选中
  limit: boolean = false // 发售限制
  givePoints: boolean = false // 赠送积分
  giveCoupons: boolean = false // 赠送券
  priceTmp: number = 0
  cardTemplateType: string = '' // 卡类型
  count: number = 0 //次数
}

class GiftCardActivityFormData {

  stores: Nullable<StoreRange> = new StoreRange() // 活动门店
  name: Nullable<string> = null // 活动名称
  timeRange: Date[] = [] // 活动时间
  themePictureUrls: string[] = [] // 活动封面
  defaultGiftMsg: Nullable<string> = null // 祝福语
  saleSpecs: SaleSpecFormData[] = [] // 面额
  limit: boolean = false // 购买限制
  maxBuyQty: Nullable<number> = null // 每人限购
  cardTemplateNumber: Nullable<string> = null // 卡模板名称
  // cardTemplateName: Nullable<string> = null // 卡模板代码
  remark: Nullable<string> = null // 使用须知
  topic: Nullable<any> = null
  // ---
  favType: string= 'discount'
  cardMedium: string = 'online'
  cardType: string = 'GiftCard'
    // 活动时间和时间限制
	activityDateTimeCondition = new ActivityDateTimeCondition()
}

class GiftCardActivityEditForm {
  data: GiftCardActivityFormData = new GiftCardActivityFormData()
  master: any
  rules: any
  discountRules: any
  priceRules: any
  specLimitRules: any
  specPointsRules: any
  specCouponQtyRules: any
  specCouponRules: any

  get discountNeed() {
    return this.master.form.data.favType == 'discount'
  }

  get priceNeed() {
    return this.master.form.data.favType == 'amount'
  }

  init(master: any) {
    this.master = master
    this.rules = {
      name: [
        { required: true, message: this.master.i18n('请输入活动名称'), trigger: ['change', 'blur'] },
        { min: 1, max: 80, message: this.master.i18n('长度在80个字符以内'), trigger: ['change', 'blur'] }
      ],
      themePictureUrls: [
        { required: true, message: this.master.i18n('请上传活动封面'), trigger: ['change', 'blur'] }
      ],
      cardTemplateNumber: [
        { required: true, message: this.master.i18n('请选择卡模板'), trigger: ['change', 'blur'] }
      ],
      saleSpecs: [
        { required: true, type: 'array', message: this.master.i18n('请选择卡模板'), trigger: ['change', 'blur'] }
      ],
      maxBuyQty: [
        { required: true, message: this.master.i18n('请输入限购数量'), trigger: ['change', 'blur'] },
      ],
      // 优惠类型
      cardType: [
        { required: true, trigger: ['change', 'blur'] }
      ],
      favType: [
        { required: true, trigger: ['change', 'blur'] }
      ]
    }
  }

  toParams() {
    let formData: GiftCardActivityFormData = JSON.parse(JSON.stringify(this.data))
    let activity = new GiftCardActivity()
    activity.body = new ActivityBody()
    activity.detail = new GiftCardRuleDetail()
    // 活动名称
    activity.body.name = formData.name
    // 活动门店
    activity.body.stores = formData.stores
    activity.detail.dateTimeCondition = formData.activityDateTimeCondition.dateTimeCondition
    activity.body.beginDate = formData.activityDateTimeCondition.beginDate
    activity.body.endDate = formData.activityDateTimeCondition.endDate
    if (formData.topic) {
      activity.body.topicCode = formData.topic.code
      activity.body.topicName = formData.topic.name
    }
    // 卡样
    activity.detail.themePictureUrls = formData.themePictureUrls
    // 祝福语
    activity.detail.defaultGiftMsg = formData.defaultGiftMsg
    activity.detail.cardType = formData.cardType
    ;(activity.detail.cardMedium as any) = formData.cardMedium
    ;(activity.detail.favType as any) = formData.favType
    // 卡面额
    let formSpecs = [...formData.saleSpecs]
    let arr = []
    for (let spec of formSpecs) {
      if (spec.gift) {
        spec.gift.points = spec.givePoints ? spec.gift.points : null
        spec.gift.couponItems = spec.giveCoupons ? spec.gift.couponItems : []
      }
      let specTmp = new GiftCardSaleSpecs()
      specTmp.cardTemplateNumber = (spec as any).cardTemplateNumber
      specTmp.cardTemplateName = (spec as any).cardTemplateName
      specTmp.faceAmount = (spec as any).faceAmount
      specTmp.discount = (spec as any).discount ? parseFloat((spec as any).discount) : null
      specTmp.price = formData.favType == 'discount' ? (parseFloat(spec.discount as any) * parseFloat(spec.cardTemplateType === 'COUNTING_CARD' ? spec.templatePrice : spec.faceAmount as any) / 10) : (spec.price ? parseFloat(spec.price as any) : null)
      specTmp.total = (spec as any).total ? parseFloat((spec as any).total) : null
      specTmp.totalPerMan = (spec as any).totalPerMan ? parseFloat((spec as any).totalPerMan) : null
      specTmp.templatePrice = spec.templatePrice
      specTmp.gift = (spec as any).gift
      specTmp.validityInfo = spec.validityInfo
      specTmp.cardTemplateType = spec.cardTemplateType
      specTmp.count = spec.cardTemplateType === 'COUNTING_CARD' ? spec.count : null
      arr.push(specTmp)
    }
    activity.detail.saleSpecs = [...arr]
    // 购买限制，写死不限制 PHX-10376
    activity.detail.maxBuyQty = null
    // 备注
    activity.body.remark = formData.remark
    return activity
  }

  of(activity: GiftCardActivity) {
    if (!activity || !activity.body || !activity.detail) {
      return
    }
    this.data.name = activity.body.name
    this.data.activityDateTimeCondition.beginDate = activity.body.beginDate
    this.data.activityDateTimeCondition.endDate = activity.body.endDate
    this.data.activityDateTimeCondition.dateTimeCondition = activity.detail.dateTimeCondition;
    this.data.themePictureUrls = activity.detail.themePictureUrls || []
    this.data.defaultGiftMsg = activity.detail.defaultGiftMsg
    // 购买限制，写死不限制 PHX-10376
    this.data.limit = false
    this.data.maxBuyQty = null
    this.data.remark = activity.body.remark
    this.data.stores = activity.body.stores
    this.data.topic = {code: activity.body.topicCode, name: activity.body.topicName}
    
    ;(this.data.cardType as any) = activity.detail.cardType
    ;(this.data.cardMedium as any)  = activity.detail.cardMedium
    ;(this.data.favType as any) = activity.detail.favType
    ;(this.data.saleSpecs as any) = activity.detail.saleSpecs
    ;(this.data.saleSpecs || []).forEach((element: any) => {
      element.checked = false
      element.limit = element.total !== null
      element.givePoints = element.gift.points !== null
      element.giveCoupons = element.gift.couponItems.length !== 0
      element.templatePrice = element.templatePrice
      element.name = element.cardTemplateName
      element.number = element.cardTemplateNumber
      element.total = element.total
      element.totalPerMan = element.totalPerMan
    });
  }
}

export {SaleSpecFormData, GiftCardActivityFormData, GiftCardActivityEditForm}
