import { UpdateCycle } from "model/autoTag/UpdateCycle"
import { UpdateMode } from "./UpdateMode"
import UserGroupCalculatorRule from "./UserGroupCalculatorRule"
import { DayOfWeek } from "model/autoTag/DayOfWeek"

export default class UserGroupCalculatorTaskV2SaveOrModifyRequest {
  // uuid
  uuid: Nullable<string> = null
  // 客群uuid
  userGroupUuid: Nullable<string> = null
  // 客群名称
  userGroupName: Nullable<string> = null
  // 客群分类
  userGroupCategory: Nullable<string> = null
  // 计算规则
  rule: Nullable<UserGroupCalculatorRule> = null
  // 更新方式 [手动更新，周期更新]
  updateMode: Nullable<UpdateMode> = null
  // 更新周期
  updateCycle: Nullable<UpdateCycle> = null
  // 每周更新日期
  dayOfWeek: Nullable<DayOfWeek> = null
  // 每月更新日期
  dayOfMonth: Nullable<number> = null
  // 是否是每月最后一天
  lastOfMonth: Nullable<boolean> = null
  // 说明
  remark: Nullable<string> = null
}