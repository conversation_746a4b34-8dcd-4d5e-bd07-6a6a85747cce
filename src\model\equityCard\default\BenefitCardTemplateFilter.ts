import PageRequest from 'model/equityCard/default/PageRequest'
import { enefitCardTemplateStatus } from 'model/equityCard/default/enefitCardTemplateStatus'
import { enefitCardTemplateType } from 'model/equityCard/default/enefitCardTemplateType'

// 会员权益卡模版分页查询条件
export default class BenefitCardTemplateFilter extends PageRequest {
  // 营销中心等于
  marketingCenterEquals: Nullable<string> = null
  // 模板号或名称类似于
  key: Nullable<string> = null
  // 状态等于
  statusEquals: Nullable<enefitCardTemplateStatus> = null
  // 类型等于
  typeEquals: Nullable<enefitCardTemplateType> = null
  // 排序，key表示排序的字段，可选值：code,lastModifyInfoTime；value表示排序方向，可选值为：asc, desc
  sorts: any
}