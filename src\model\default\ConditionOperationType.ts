export enum ConditionOperationType {
  // 
  EQUALS = 'EQUALS',
  // 
  NOT_EQUALS = 'NOT_EQUALS',
  // 
  CONTAIN = 'CONTAIN',
  // 
  NOT_CONTAIN = 'NOT_CONTAIN',
  // 
  HAVE_VALUE = 'HAVE_VALUE',
  // 
  NOT_HAVE_VALUE = 'NOT_HAVE_VALUE',
  // 
  REGEXP = 'REGEXP',
  // 
  NOT_REGEXP = 'NOT_REGEXP',
  // 
  LESS = 'LESS',
  // 
  LESS_EQUALS = 'LESS_EQUALS',
  // 
  GREATER = 'GREATER',
  // 
  GREATER_EQUALS = 'GREATER_EQUALS',
  // 
  BETWEEN = 'BETWEEN'
}