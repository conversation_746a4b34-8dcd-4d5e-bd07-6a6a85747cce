import CouponItem from 'model/equityCard/default/CouponItem'
import IdName from 'model/equityCard/default/IdName'
import StoreRange from 'model/equityCard/default/StoreRange'

export default class GiftInfo {
  // 赠送积分
  points: Nullable<number> = null
  // 返现金额
  rebateAmount: Nullable<number> = null
  // 赠券
  couponItems: CouponItem[] = []
  // 赠送权益卡信息
  equityCard: Nullable<IdName> = null
  // 兑换码个数
  exchangeCodeQty: Nullable<number> = null
  // 适用门店
  giftStore: Nullable<StoreRange> = null
}