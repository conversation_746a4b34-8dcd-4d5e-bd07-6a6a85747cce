import BaseCouponActivity from 'model/v2/coupon/BaseCouponActivity'
import CouponItem from 'model/common/CouponItem'
import IdName from 'model/common/IdName'
import ChannelRange from 'model/common/ChannelRange'
import DateTimeCondition from "model/common/DateTimeCondition";


export default class ThirdPartyIssueCouponActivity extends BaseCouponActivity {
  // 第三方发券平台
  platform: Nullable<IdName> = null
  // 券详情信息
  couponItem: Nullable<CouponItem> = null
// 活动时间限制
dateTimeCondition = new DateTimeCondition();
  	// 每天活动限量
	maxDayIssueTimes: Nullable<number> = null;

   //限量时间类型，DAY——每天；WEEK——每周,MONTH——每月，YEAR——每年;,可用值:DAY,WEEK,MONTH,YEAR
  dateLimitType: Nullable<string> = null;
	// 每天每天活动限量
  maxPerDateRangeIssueTimes: Nullable<number> = null;
}
