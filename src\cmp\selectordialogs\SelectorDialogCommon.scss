::v-deep .el-icon-circle-close {
	display: none;
}

::v-deep .el-icon-circle-check {
	display: none;
}

::v-deep .el-dialog {
	width: 1024px;
	height: 700px;
}

::v-deep .el-date-editor {
	.el-range__icon,
	.el-range-separator,
	.el-range__close-icon {
		line-height: 24px !important;
	}
}

.el-form-item {
	margin-bottom: 10px;
}

.wrap {
	.query {
		::v-deep .el-form-item__label {
			white-space: nowrap;
			text-overflow: ellipsis;
			overflow: hidden;
		}
	}

	.table-wrap {
		height: 390px;
		border: 1px solid var(--border-color);

		.thead {
			height: 35px;
			line-height: 34px;
			background-color: var(--border-color);

			.el-col {
				&:first-child {
					text-align: center;
				}
			}
		}

		.tbody {
			// height: 100%;
			height: calc(100% - 35px);
			overflow-y: auto;

			.trow {
				height: 35px;
				line-height: 34px;

				&:hover {
					background-color: #f8f9fc;
				}

				.el-col {
					&:first-child {
						text-align: center;
					}

					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
					display: flex;
					flex-direction: column;
					flex-wrap: wrap;
				}
			}
		}
	}

	.right-table {
		height: 390px;
		border: 1px solid var(--border-color);

		.thead {
			padding-left: 5px;
			height: 35px;
			line-height: 34px;
			background-color: var(--border-color);
		}

		.tbody {
			padding: 5px;
			width: 100%;
			height: calc(100% - 75px);
			overflow-y: auto;

			.trow {
				height: 35px;
				line-height: 34px;

				.left {
					width: 177px;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}

				&:hover {
					background-color: #f8f9fc;

					.clear-btn {
						display: initial !important;
						position: absolute;
						right: 0;
					}
				}
			}
		}
	}
}
