<!--
 * @Author: 黎钰龙
 * @Date: 2023-08-31 16:10:22
 * @LastEditTime: 2023-08-31 17:28:59
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\prepaycard\countCardReport\exchangeGoodsDialog\ExchangeGoodsDialog.vue
 * 记得注释
-->
<template>
  <el-dialog :title="i18n('兑换商品')" :visible.sync="exchangeDialogShow">
    <el-table :data="listData" v-loading="tableLoading" height="400">
      <el-table-column label="商品名称" width="200">
        <template slot-scope="scope">
          {{scope.row.name || '-'}}
        </template>
      </el-table-column>
      <el-table-column label="商品条码" width="160">
        <template slot-scope="scope">
          {{scope.row.barcode || '-'}}
        </template>
      </el-table-column>
      <el-table-column label="商品数量" width="100">
        <template slot-scope="scope">
          {{scope.row.qty || '-'}}
        </template>
      </el-table-column>
      <el-table-column label="抵扣金额" width="140">
        <template slot-scope="scope">
          {{scope.row.deductAmount || '-'}}
        </template>
      </el-table-column>
      <el-table-column label="支付金额" width="140">
        <template slot-scope="scope">
          {{scope.row.payAmount || '-'}}
        </template>
      </el-table-column>
      <el-table-column label="优惠金额">
        <template slot-scope="scope">
          {{scope.row.favAmount || '-'}}
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-if="listData.length" :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
      @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background layout="total, prev, pager, next, sizes,  jumper"
      class="pagin"></el-pagination>
  </el-dialog>
</template>

<script lang="ts" src="./ExchangeGoodsDialog.ts">
</script>

<style lang="scss" scoped>
</style>