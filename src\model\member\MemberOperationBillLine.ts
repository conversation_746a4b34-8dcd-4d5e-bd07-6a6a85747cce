import IdName from "model/common/IdName";

class AccountBalance {
    accountName: Nullable<string> = null
    balance: Nullable<string> = null
}

export default class MemberOperationBillLine {
    // 会员手机号，只在修改时候显示使用
    mobile: Nullable<string> = null;
    // 会员号，只在修改时候显示使用
    hdCardMbrId: Nullable<string> = null;
    // 实体卡号，只在修改时候显示使用
    hdCardCardNum: Nullable<string> = null;
    // 会员id
    memberId: Nullable<string> = null;
    // 会员名称
    name: Nullable<string> = null;
    // 调整前会员状态
    oldState: Nullable<string> = null;
    // 会员状态调整为
    newState: Nullable<string> = null;
    // 调整时账户金额
    accountBalances: Nullable<AccountBalance[]> = null;
    // 调整时积分余额
    points: Nullable<string> = null;
    // 调整时可用券
    coupons: Nullable<IdName[]> = null;
    // 操作原因
    remark: Nullable<string> = null;
    // 操作类型
    category: Nullable<'BLOCK'|'UNBLOCK'> = null
}