<template>
  <div class="coupon-style">
    <el-form label-position="left" :model="value" :rules="rules" ref="form">
      <el-form-item :label="i18n('券样式')" prop="propCouponStyle" label-width="100px">
        <el-radio-group v-model="value.propCouponStyle" @change="handleChange">
          <el-radio label="1">{{ i18n('样式一') }}</el-radio>
          <el-radio label="2">{{ i18n('样式二') }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" src="./CouponStyle.ts">
</script>

<style lang="scss" scoped>
.coupon-style {
  padding: 12px;
  background: #f0f2f6;
  border-radius: 4px;
  margin-bottom: 20px;
}
</style>