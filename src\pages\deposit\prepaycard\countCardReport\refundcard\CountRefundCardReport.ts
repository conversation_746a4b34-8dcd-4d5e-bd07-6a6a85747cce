import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import TimeRange from '../../cmp/timerange/TimeRange';
import CountingCardReportApi from 'http/prepay/card/CountingCardReportApi';
import GiftCardFilter from 'model/prepay/report/card/GiftCardFilter';
import RSOrg from 'model/common/RSOrg';
import DataUtil from '../../common/DataUtil';
import FormItem from 'cmp/formitem/FormItem';
import I18nPage from 'common/I18nDecorator';
import BrowserMgr from 'mgr/BrowserMgr';
import Zone from 'model/datum/zone/Zone';
import GiftCardCardHst from 'model/prepay/report/card/GiftCardCardHst';
import SelectStores from 'cmp/selectStores/SelectStores';
import ChannelManagement from "model/channel/ChannelManagement";
import ChannelManagementFilter from "model/channel/ChannelManagementFilter";
import ChannelManagementApi from "http/channelmanagement/ChannelManagementApi";
import Channel from "model/common/Channel";
@Component({
  name: 'CountRefundCardReport',
  components: {
    TimeRange,
    FormItem,
    SelectStores
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/公用/查询条件/提示',
    '/储值/预付卡/预付卡查询/列表页面',
    '/储值/预付卡/电子礼品卡报表/消费流水',
    '/储值/预付卡/电子礼品卡报表/售卡流水',
    '/储值/预付卡/电子礼品卡报表/退卡流水',
    '/储值/预付卡/次卡报表'
  ],
  auto: true
})
export default class CountRefundCardReport extends Vue {
  @Prop({ default() { return [] } }) areaData: Zone[];
  query: GiftCardFilter = new GiftCardFilter()
  dataUtil: DataUtil = new DataUtil()
  queryData: GiftCardCardHst[] = []
  expandQuery: boolean = false
  isMoreMarketing: boolean = false // 是否开启多营销中心 true 开启 false 不开启
  sum: Nullable<number> = null //报表汇总信息
  page = {
    currentPage: 1,
    total: 0,
    size: 10,
    probeEnabled: null
  }
  $refs: any
  channelTypes: any
  channelEquals: any = ''
  channelMap: Map<string, ChannelManagement> = new Map<string, ChannelManagement>();


  @Watch('page', { deep: true, immediate: true })
  handle(value: any) {
    this.query.page = value.currentPage - 1
    this.query.pageSize = value.size
  }

  created() {
    let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
    if (sysConfig) {
      this.isMoreMarketing = sysConfig.enableMultiMarketingCenter;
    }
    this.query.occurredTimeAfterOrEqual = this.dataUtil.format(new Date(), 'yyyy-MM-dd')
    this.query.occurredTimeBefore = this.dataUtil.format(new Date(), 'yyyy-MM-dd')
    this.queryList()
    this.getChannelList()
  }

  private getChannelList() {
    let query = new ChannelManagementFilter();
    query.page = 0
    query.pageSize = 0
    ChannelManagementApi.query(query).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.channelTypes = resp.data
        for (let channel of this.channelTypes) {
          if (channel.channel && channel.channel.type && channel.channel.id) {
            this.channelMap.set(this.getKey(channel.channel) as string, channel)
          }
        }
        console.log(this.channelMap);

      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  queryList() {
    if (this.channelEquals) {
      this.query.channelIdEquals = this.channelMap.get(this.channelEquals)!.channel.id
      this.query.channelTypeEquals = this.channelMap.get(this.channelEquals)!.channel.type
    } else {
      this.query.channelIdEquals = null
      this.query.channelTypeEquals = null
    }
    CountingCardReportApi.queryRefundCardHst(this.query).then((res) => {
      if (res.code === 2000) {
        this.queryData = res.data || []
        this.page.total = res.total
        this.page.probeEnabled = res.fields ? res.fields.probeEnabled : null
        //this.getSum()
      } else {
        this.$message(res.msg || this.i18n('查询售卡流水失败'))
      }
    }).catch((err) => {
      this.$message(err.message || this.i18n('查询售卡流水失败'))
    })
  }

  private getKey(channel: Channel) {
    if (channel && channel.type && channel.id) {
      return channel.type as any + channel.id
    }
    return channel.typeId
  }


  handleTimeRange(dateArr: Date[]) {
    this.query.occurredTimeAfterOrEqual = dateArr[0]
    this.query.occurredTimeBefore = dateArr[1]
    this.queryList()
  }

  doSearch() {
    this.page.currentPage = 1
    this.queryList()
  }

  doReset() {
    this.query = new GiftCardFilter()
    this.page.currentPage = 1
    this.channelEquals = '';
    this.$refs['timeRange'].reset()
  }

  gotoTplDtl(num: any) {
    this.$router.push({ name: 'prepay-card-tpl-dtl', query: { number: num, cardTemplateType: 'COUNTING_CARD' } })
  }

  isShowSum: boolean = false
  private getSum() {
    CountingCardReportApi.cardHstSum(this.query).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.sum = resp.data
        this.isShowSum = true
      } else {
        if (resp.code === 2404) return
        this.$message.error(resp.msg)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.queryList()
  }
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.queryList()
  }
};