<?xml version="1.0" encoding="UTF-8"?>
<svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 60 (88103) - https://sketch.com -->
    <title>ic_fenxi_selected</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <path d="M19,9 C21.7614237,9 24,11.2385763 24,14 L24,14 L19,14 Z" id="path-1"></path>
        <filter x="-140.0%" y="-100.0%" width="380.0%" height="380.0%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0.494117647   0 0 0 0 1  0 0 0 0.301491477 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="🔪icon" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="切图" transform="translate(-166.000000, -189.000000)">
            <g id="ic_fenxi_selected" transform="translate(166.000000, 189.000000)">
                <g id="cutting/ic_fenxi_selected">
                    <g>
                        <path d="M15.000274,6.04481833 L15,18 L26.9550916,18.0007248 C26.4495221,23.6068787 21.7377953,28 16,28 C9.92486775,28 5,23.0751322 5,17 C5,11.261864 9.39364311,6.5499183 15.000274,6.04481833 Z" id="形状结合" fill="#FFFFFF"></path>
                        <path d="M16,5 C22.627417,5 28,10.372583 28,17 L28,17 L16,17 Z" id="形状结合" fill="#FFFFFF"></path>
                        <g id="形状结合备份-2">
                            <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                            <use fill="#007EFF" fill-rule="evenodd" xlink:href="#path-1"></use>
                        </g>
                        <rect x="0" y="0" width="32" height="32"></rect>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>