import CouponWriteoffBillResult from './CouponWriteoffBillResult'
import { State } from 'model/common/State'

// 券核销单信息
export default class CouponWriteoffBill {
  // 活动制定所属营销中心
  marketingCenter: Nullable<string> = null
  // 单号
  number: Nullable<string> = null
  // 版本，客户端只读
  revision: Nullable<number> = null
  // 是否最新版本
  lastRevision: Nullable<boolean> = null
  // 核销单状态，客户端只读
  state: Nullable<State> = null
  // 核销门店
  issueOrgId: Nullable<string> = null
  // 核销门店名
  issueOrgName: Nullable<string> = null
  // 券码组合
  couponCodes: string[] = []
  // 发生时间
  occurredTime: Nullable<Date> = null
  // writeoffResult
  writeoffResult: Nullable<CouponWriteoffBillResult> = null
  // 操作人
  operator: Nullable<string> = null
  // 创建人
  creator: Nullable<string> = null
  // 最后修改时间
  lastModified: Nullable<Date> = null
  // 照=摘要
  remark: Nullable<string> = null
}