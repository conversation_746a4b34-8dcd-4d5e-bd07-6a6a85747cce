export default class ClientFilter {
  // 自定义关键字
  key: Nullable<string> = null
  // 客户id等于
  clientIdEquals: Nullable<string> = null
  // 客户Id类似于
  clientIdLikes: Nullable<string> = null
  // 客户名称类似于
  clientNameLikes: Nullable<string> = null
  // 客户Id in
  clientIdIn: string[] = []
  // 
  page: Nullable<number> = null
  // 
  pageSize: Nullable<number> = null
  // 排序，key表示排序的字段，可选值：clientId,clientCode,clientName；value表示排序方向，可选值为：asc, desc
  sorters: any
}