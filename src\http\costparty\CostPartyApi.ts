import ApiClient from 'http/ApiClient'
import RSCostParty from 'model/common/RSCostParty'
import RSCostPartyFilter from 'model/common/RSCostPartyFilter'
import RSDisableCostPartyRequest from 'model/common/RSDisableCostPartyRequest'
import RSEnableCostPartyRequest from 'model/common/RSEnableCostPartyRequest'
import RSSaveBatchCostPartyRequest from 'model/common/RSSaveBatchCostPartyRequest'
import RSSaveCostPartyRequest from 'model/common/RSSaveCostPartyRequest'
import Response from 'model/common/Response'

export default class CostPartyApi {
  /**
   * 删除券承担方
   *
   */
  static deleteBatch(idSet: string[]): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/costparty/deleteBatch`, {}, {
      params: {
        idSet: idSet
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 禁用券承担方
   *
   */
  static disable(body: RSDisableCostPartyRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/costparty/disable`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 启用券承担方
   *
   */
  static enable(body: RSEnableCostPartyRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/costparty/enable`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询券承担方
   *
   */
  static query(body: RSCostPartyFilter): Promise<Response<RSCostParty[]>> {
    return ApiClient.server().post(`/v1/costparty/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除券承担方
   *
   */
  static remove(id: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/costparty/remove`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存
   *
   */
  static save(body: RSSaveCostPartyRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/costparty/save`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 更新
   *
   */
  static modify(body: RSSaveCostPartyRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/costparty/modify`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 批量保存
   *
   */
  static saveBatch(body: RSSaveBatchCostPartyRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/costparty/saveBatch`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
