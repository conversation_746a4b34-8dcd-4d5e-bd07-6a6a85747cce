import {Component, Prop, Vue, Watch} from 'vue-property-decorator'
import InitialCardSetting from 'pages/member/alipay/alipaymembercard/dialog/InitialCardSetting.vue'
import CardTpl from "pages/member/entities/CardTpl";

@Component({
  name: 'InitialCardInfo',
  components: {
    InitialCardSetting
  }
})
export default class InitialCardInfo extends Vue {
  // // 开卡信息
  $refs: any
  dialogCardInfo = false
  requiredInfo: CardTpl[] = []
  optionalInfo: CardTpl[] = []
  @Prop()
  value: any
  @Watch('value')
  onValueChange(value: any) {
    if (value) {
      if (typeof value.requiredInfo[0] === 'string') {
        if (value.requiredInfo && value.requiredInfo.length > 0) {
          this.requiredInfo = []
          value.requiredInfo.forEach((item: string) => {
            let obj: CardTpl = new CardTpl()
            obj.cardName = this.doGetNameByCode(item)
            obj.cardCode = item
            this.requiredInfo.push(obj)
          })
        }
        if (value.optionalInfo && value.optionalInfo.length > 0) {
          this.optionalInfo = []
          value.optionalInfo.forEach((item: string) => {
            let obj: CardTpl = new CardTpl()
            obj.cardName = this.doGetNameByCode(item)
            obj.cardCode = item
            this.optionalInfo.push(obj)
          })
        }
      } else {
        this.requiredInfo = value.requiredInfo
        this.optionalInfo = value.optionalInfo
      }
      // this.$refs.cardSetting.doSetArray(this.requiredInfo, this.optionalInfo)
      // this.$emit('input', {
      //   requiredInfo: this.requiredInfo,
      //   optionalInfo: this.optionalInfo
      // })
    }
  }
  doCardInfoClose() {
    this.dialogCardInfo = false
  }
  doCardInfoSubmit(must: any, option: any) {
    this.requiredInfo = must
    this.optionalInfo = option
    this.$emit('input', {
      requiredInfo: must,
      optionalInfo: option
    })
  }
  doSetCardInfo() {
    this.dialogCardInfo = true
    this.$refs.cardSetting.doSetArray(this.requiredInfo, this.optionalInfo)
  }
  doGetNameByCode(code: string) {
    let obj: any = {
      OPEN_FORM_FIELD_MOBILE: this.formatI18n('/会员/会员资料/手机号'),
      OPEN_FORM_FIELD_GENDER: this.formatI18n('/会员/会员资料/性别'),
      OPEN_FORM_FIELD_NAME: this.formatI18n('/会员/会员资料/姓名'),
      OPEN_FORM_FIELD_BIRTHDAY_WITH_YEAR: this.formatI18n('/会员/会员资料/生日'),
      OPEN_FORM_FIELD_ADDRESS: this.formatI18n('/会员/会员资料/地址'),
      OPEN_FORM_FIELD_CITY: '城市',
      OPEN_FORM_FIELD_EMAIL: this.formatI18n('/会员/会员资料/邮箱'),
      OPEN_FORM_FIELD_IS_STUDENT: this.formatI18n('/会员/会员资料/学历')
    }
    return obj[code]
  }
  getMbrAttribute(mbrAttribute: any) {
    let str = ''
    if (mbrAttribute && mbrAttribute.length > 0) {
      mbrAttribute.forEach((item: any) => {
        str += item.cardName + '、'
      })
    }
    if (str) {
      str = str.substring(0, str.length - 1)
    }
    return str
  }
}