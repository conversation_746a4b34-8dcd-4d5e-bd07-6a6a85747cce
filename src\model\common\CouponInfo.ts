/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-04-26 17:05:26
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\common\CouponInfo.ts
 * 记得注释
 */
import CashCouponAttribute from "model/common/CashCouponAttribute";
import CostParty from "model/common/CostParty";
import CouponThreshold from "model/common/CouponThreshold";
import DateTimeRange from "model/common/DateTimeRange";
import DiscountCouponAttribute from "model/common/DiscountCouponAttribute";
import GoodsRange from "model/common/GoodsRange";
import PickUpCouponAttribute from "model/common/PickUpCouponAttribute";
import StoreRange from "model/common/StoreRange";
import SubjectApportion from "model/common/SubjectApportion";
import ValidityInfo from "model/common/ValidityInfo";
import { CouponBasicType } from "model/common/CouponBasicType";
import ChannelRange from "model/common/ChannelRange";
import IdName from "model/common/IdName";
import MixCoupon from 'model/coupon/template/MixCoupon';
import CouponTemplateOuterRelation from "model/coupon/template/CouponTemplateOuterRelation";
import ExchangeGoodsCouponAttribute from "./ExchangeGoodsCouponAttribute";
import BPointsCouponAttribute from "./BPointsCouponAttribute";
import BRandomCouponAttribute from "model/common/BRandomCouponAttribute";
import RexCoupon from 'model/coupon/template/RexCoupon';
import Channel from "./Channel";
import CouponSuperposition from "model/coupon/CouponSuperposition/CouponSuperposition";
import SpecialPriceCouponAttribute from "./SpecialPriceCouponAttribute";
import WeimobCoupon from "./weimobCoupon/WeimobCoupon";
import CouponTemplateTagRelation from 'model/coupon/CouponTemplateTagRelation'
import BCouponTemplatePromotion from './BCouponTemplatePromotion'
import {CouponSubscriptType} from "model/common/CouponSubscriptType";
import GoodsFavRule from "./GoodsFavRule";
import WriteOffScene from "model/coupon/template/WriteOffScene";
export default class CouponInfo {
	// 模板id
	templateId: Nullable<string> = null;
	// 券名称
	name: Nullable<string> = null;
	// 券基础类型：all_cash——全场现金券； goods_cash——all_cash； all_discount——全场折扣券； rfm_type——商品折扣券； goods_discount——单品折扣券;goods——提货券;freight--运费券；
	couponBasicType: Nullable<CouponBasicType> = null;
	// 券有效期
	validityInfo: Nullable<ValidityInfo> = null;
	// 用券时段
	useTimeRange: Nullable<DateTimeRange> = null;
	// 用券门店
	useStores: Nullable<StoreRange> = null;
	// 用券商品
	useGoods: Nullable<GoodsRange> = null;
	// 用券门槛
	useThreshold: Nullable<CouponThreshold> = null;
	// 门槛类型
	useThresholdType: Nullable<"AMOUNT" | "QTY"> = null;
	// 是否参与促销叠加，true表示叠加优惠，false表示不可参与叠加
	excludePromotion: Nullable<boolean> = null;
	// 促销叠加类型 ALL("全部"),PART("部分")
	promotionSuperpositionType: Nullable<"ALL" | "PART"> = null;
	// 促销单信息
	promotion: Nullable<BCouponTemplatePromotion> = null
  // 叠加优惠：会员价、人工折扣、其他优惠
  goodsFavRules: Nullable<GoodsFavRule[]> = null
	// 是否支持转赠
	transferable: Nullable<boolean> = null;
	// 用券记录方式
	useApporion: Nullable<SubjectApportion> = null;
	// 券承担方
	costParties?: CostParty[] = [];
  // 券承担方(修改后的)
  costParty: Nullable<CostParty> = null
	// 用券顺序
	priority: Nullable<number> = null;
	// 用券商品说明
	goodsRemark: Nullable<string> = null;
	// 用券说明
	remark: Nullable<string> = null;
	// 现金券属性
	cashCouponAttribute: Nullable<CashCouponAttribute> = null;
	// 折扣券属性
	discountCouponAttribute: Nullable<DiscountCouponAttribute> = null;
	// 提货券属性
	pickUpCouponAttribute: Nullable<PickUpCouponAttribute> = null;
	// 券码前缀，为null或者空串时忽略
	codePrefix: Nullable<string> = null;
	// 用券渠道
	useChannels: Nullable<ChannelRange> = null;
	//同步渠道
	sychChannel: Channel[] = [];
	//不可与以下优惠券叠加，同一单品只可按最大优惠力度使用其中一种券
	groupMutexFlag: Nullable<boolean> = null;
	// 商品限制叠加模板信息
	groupMutexTemplates: IdName[] = [];
	// 券logo地址
	logoUrl: Nullable<string> = null;
	// 券模板状态
	state: Nullable<string> = null;
	// 外部券模板号idwei-xin-coupon
	outerNumberId: Nullable<string> = null;
	// 外部券模板号namespace
	outerNumberNamespace: Nullable<string> = null;
	// 应用范围，为weixin时不可编辑
	scope: Nullable<string> = null;
	// 提货券记账开关
	enablePayApportion: boolean = false;
	// 营销中心
	marketingCenter: Nullable<string> = null;
	// 券模板外部关系
  outerRelations: Nullable<CouponTemplateOuterRelation[]> = [];
	// 兑换券参数
	exchangeGoodsCouponAttribute: Nullable<ExchangeGoodsCouponAttribute> = new ExchangeGoodsCouponAttribute();
	// 积分券 参数
	pointsCouponAttribute: Nullable<BPointsCouponAttribute> = new BPointsCouponAttribute();
	// 随机金额券 参数
	randomCouponAttribute: Nullable<BRandomCouponAttribute> = new BRandomCouponAttribute();
	// 叠加用券6.29
	couponSuperposition: Nullable<CouponSuperposition> = null;
	// 特价券参数
	specialPriceCouponAttribute: Nullable<SpecialPriceCouponAttribute> = null
	// 微盟平台券
	weimobCoupon: Nullable<WeimobCoupon> = null
	// REX平台券
	rexCoupon: Nullable<RexCoupon> = null
	// 剩余库存
	total: Nullable<number> = null
	// 标签关系
	templateTag: CouponTemplateTagRelation[] = []
  // 价格
  salePrice: Nullable<number> = null
  // 账款项目
  termsModel: Nullable<string> = null
	// 券角标
	couponSubscriptType: Nullable<string> = null;
	// 是否增值商品
	appreciationGoods:Nullable<boolean> = null;
	//备注
	notes: Nullable<string> = null;
	//每人每天限量
    maxDailyMemberQuotaQty:Nullable<number> = null;
	// 核销链接
	writeOffLink: Nullable<string> = null;
	// 核销场景
	writeOffScene: Nullable<WriteOffScene> = null;
}
