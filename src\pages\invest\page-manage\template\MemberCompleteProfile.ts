
// 完善资料有礼
export default class MemberCompleteProfile {
  id: string = 'memberCompleteProfile'
   /** 组件类型 */
  type: 'member'
  uuid: Nullable<string> = ""
  // 组件名称
  name: Nullable<string> = '完善资料有礼'
  /** 顶部背景图 */
  propTopBackgroundImage: Nullable<string> = ''
  /** 赠礼角标 */
  propGiftIcon: Nullable<string> = ''
  /** 弹窗赠礼背景图 */
  propPopGiftBackgroundImage: Nullable<string> = ''
  /** 赠礼图标：优惠券 */
  propCouponIcon: Nullable<string> = ''
  /** 赠礼图标：积分 */
  propScoreIcon: Nullable<string> = ''
  // private String scoreIcon;
  /** 赠礼图标：成长值 */
  propGrowthIcon: Nullable<string> = ''
  /** 赠礼背景：背景色 */
  propGiftBgColor: Nullable<string> = '#FF3B3D'
  /** 赠礼背景：文字颜色 */
  propGiftBgFontColor: Nullable<string> = '#FFFFFF'
}