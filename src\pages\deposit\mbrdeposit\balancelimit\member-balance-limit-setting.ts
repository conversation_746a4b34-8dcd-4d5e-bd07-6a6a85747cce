import { Component,Prop, Vue } from "vue-property-decorator";
import BreadCrume from "cmp/bread-crumb/BreadCrume.vue";
import I18nPage from 'common/I18nDecorator';
import SystemConfigApi from "http/systemConfig/SystemConfigApi";
import UrlParamUtil from "util/UrlParamUtil";
import memberManagement from "pages/member/miniapp/memberManagement/memberManagement.vue";
import BMemberBalanceAccountLimitConfig from 'model/systemConfig/BMemberBalanceAccountLimitConfig'
import BMemberSingleRechargeLimitConfig from 'model/systemConfig/BMemberSingleRechargeLimitConfig'
import BMemberSingleRechargeLimitInfo from 'model/systemConfig/BMemberSingleRechargeLimitInfo'
import Channel from "model/common/Channel";
import RSChannelManagement from "model/common/RSChannelManagement";
import RSChannelManagementFilter from "model/common/RSChannelManagementFilter";
import ChannelManagementApi from "http/channelmanagement/ChannelManagementApi";
import AmountToFixUtil from 'util/AmountToFixUtil'
import FormItem from 'cmp/formitem/FormItem';

@Component({
    name: "MemberBalanceLimitSetting",
    components: { BreadCrume, memberManagement,FormItem },
  })

  @I18nPage({
    prefix: [
      '/公用/券模板',
      '/卡/卡充值限额设置',
      '/公用/按钮'
    ],
    auto: true
  })
  export default class MemberBalanceLimitSetting extends Vue {

    panelArray: any = [];
    hasRule: Nullable<boolean> = null  //是否已经设置规则
    
    modelType: string = "view";
    memberBalanceAccountLimitConfig : BMemberBalanceAccountLimitConfig = new BMemberBalanceAccountLimitConfig();
    memberSingleRechargeLimitConfig : BMemberSingleRechargeLimitConfig = new BMemberSingleRechargeLimitConfig();
    activeName: string;
    memberBalanceAccountLimitRule: any = {};
    memberSingleRechargeLimitRule: any = {};
    channels: RSChannelManagement[];
    channelMap: Map<string, RSChannelManagement> = new Map<string, RSChannelManagement>();

    created() {
        this.activeName = UrlParamUtil.get("activeName")?UrlParamUtil.get("activeName"):"memberSingleRechargeLimit";
        this.panelArray = [
            {
              name: this.formatI18n("储值限额设置"),
              url: "",
            },
          ];
        this.memberSingleRechargeLimitRule = {
            channel:[]
            
        };
        this.init();
     }


     getChannelName(index: number) {
        let str = ''
        this.memberSingleRechargeLimitConfig.infos[index]?.channels?.forEach((item) => {
          const idType = item.id! + item.type!
          str += this.channels.find((val) => {
            return (val.channel?.id! + val.channel?.type!) === idType
          })?.name + '；'
        })
        str = str.slice(0, -1)
        return str
      }


      getChargeLimit(index: number) {
        let str = ''
        const min = this.memberSingleRechargeLimitConfig.infos[index]?.minAmount
        const max = this.memberSingleRechargeLimitConfig.infos[index]?.maxAmount
        if (min) {
          str += this.i18n('最小充值金额') + String(min) + this.i18n('元') + ';'
        } else {
          str += this.i18n('最小充值金额不限制') + ';'
        }
        if (max) {
          str += this.i18n('最大充值金额') + String(max) + this.i18n('元')
        } else {
          str += this.i18n('最大充值金额不限制')
        }
        return str
      }

     getBalanceAccountLimitConfig() {
        SystemConfigApi.getBalanceAccountLimitConfig()
            .then((res) => {
            if (res.data) {
                this.memberBalanceAccountLimitConfig.maxAmount = AmountToFixUtil.formatAmount(res.data.maxAmount, ********, 0.01, '');
                this.hasRule = true;
            }else{
              if(this.activeName == "memberBalanceAccountLimit") {
                this.hasRule = false;
              }
            }})
            .catch((rej) => {
            this.$message.error(rej.message);
        });
    }

    handleTabClick() {
        if(this.activeName == "memberBalanceAccountLimit"){
            this.getBalanceAccountLimitConfig();
        }else if(this.activeName == "memberSingleRechargeLimit") {
            this.getSingleRechargeLimitConfig();
        }
        UrlParamUtil.store("activeName", this.activeName);  
    }

    
  doSet() {
    this.$router.push({
      name: 'member-balance-limit-setting-edit'
    })
  }

  doEdit() {
    this.$router.push({
      name: 'member-balance-limit-setting-edit',
      query: {
        editType: 'edit'
      }
    })
  }

    getSingleRechargeLimitConfig() {
        SystemConfigApi.getSingleRechargeLimitConfig()
            .then((res) => {
            if (res.data) {
                this.memberSingleRechargeLimitConfig = res.data;
                this.toModel();
                this.hasRule = res.data? true : false
            }else{
              if(this.activeName == "memberSingleRechargeLimit") {
                this.hasRule = false;
              }
            }})
            .catch((rej) => {
            this.$message.error(rej.message);
        });
    }

    toModel(){
        if(this.memberSingleRechargeLimitConfig && this.memberSingleRechargeLimitConfig.infos) {
            this.memberSingleRechargeLimitConfig.infos.forEach((item: BMemberSingleRechargeLimitInfo, index: number) => {
                var selectChannels: string[]= [];
                item.channels.forEach((e: Channel, index: number) => {
                    selectChannels.push(e.type + "" +  e.id);
                });
                this.memberSingleRechargeLimitConfig.infos[index].selectChannels = selectChannels;
            });
        }
    }

     private init(){
        let param: RSChannelManagementFilter = new RSChannelManagementFilter();
        ChannelManagementApi.query(param)
          .then((resp: any) => {
            if (resp && resp.code === 2000) {
              this.channels = resp.data;
              for (let channel of this.channels) {
                if (channel.channel && channel.channel.type && channel.channel.id) {
                    this.channelMap.set(this.getKey(channel.channel) as string, channel);
                }
              }
              this.getSingleRechargeLimitConfig();
              this.getBalanceAccountLimitConfig();
            }
          })
          .catch((error: any) => {
            if (error && error.message) {
              this.$message.error(error.message);
            }
          });      
      }

      private getKey(channel: Channel) {
        if (channel && channel.type && channel.id) {
          return (channel.type as any) + channel.id;
        }
        return channel.typeId;
      }

  }