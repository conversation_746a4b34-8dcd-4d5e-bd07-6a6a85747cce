import IdName from 'model/common/IdName'
import RSPrepayAdjustBillLine from 'model/common/RSPrepayAdjustBillLine'

export default class RSPrepayAdjustBill {
  //
  billNumber: Nullable<string> = null
  //
  state: Nullable<string> = null
  //
  source: Nullable<string> = null
  //
  occurredOrg: Nullable<IdName> = null
  //
  marketingCenter: Nullable<string> = null
  //
  audited: Nullable<Date> = null
  //
  auditor: Nullable<string> = null
  //
  remark: Nullable<string> = null
  //
  created: Nullable<Date> = null
  //
  lastModified: Nullable<Date> = null
  //
  lines: RSPrepayAdjustBillLine[] = []
}