/*
 * @Autor: 司浩
 * @Description:
 * @Date: 2023-01-17 14:34:29
 * @LastEditors: 司浩
 * @LastEditTime: 2023-02-10 17:01:58
 * @FilePath: \phoenix-web-ui\src\model\couponPurchase\BPurchaseCouponTradeLog.ts
 */
import MutableNsid from 'model/common/MutableNsid'
import MaterialProofInfo from 'model/couponPurchase/MaterialProofInfo'

// 操作日志
export default class BPurchaseCouponTradeLog {
  // 交易id
  tradeId: Nullable<MutableNsid> = null
  // 申请退款交易id
  rollbackTradeId: Nullable<MutableNsid> = null
  // 交易号
  tradeNo: Nullable<string> = null
  // 操作时间
  occurredTime: Nullable<Date> = null
  // 会员id
  memberId: Nullable<string> = null
  // 会员手机号
  mobile: Nullable<string> = null
  // 操作类型
  category: Nullable<string> = null
  // 原因
  remark: Nullable<string> = null
  // 留言举证信息
  ext0: Nullable<MaterialProofInfo> = null
}
