import BaseCouponActivity from '../BaseCouponActivity'
import ChannelRange from 'model/common/ChannelRange'
import GiftInfo from 'model/common/GiftInfo'
import IdName from 'model/common/IdName'

export default class UseCouponGiftActivity extends BaseCouponActivity {
  // 礼包
  giftInfo: Nullable<GiftInfo> = null
  // 用券模板
  useCoupons: IdName[] = []
  // 渠道范围
  channelRange: Nullable<ChannelRange> = null
  // 用券渠道
  issueChannelRange:Nullable<ChannelRange> = null
}