<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:02
 * @LastEditTime: 2023-09-14 15:43:02
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\querycondition\QueryCondition.vue
 * 记得注释
-->
<template>
  <div class="query-condition">
    <el-row v-show="!opened" class="query-line" style="padding: 0">
      <el-col :span="24">
        <slot></slot>
      </el-col>
    </el-row>
    <el-row v-show="!opened">
      <el-col :span="24" class="closed-action">
        <el-button @click="onSearch" class="btn-search" size="medium" type="primary">{{formatI18n('/公用/券模板', '查询')}}</el-button>
        <el-button @click="onReset" class="btn-reset" size="medium">{{formatI18n('/公用/券模板', '重置')}}</el-button>
        <span @click="onToggle" class="ellipsis-btn" v-if="showExpand">
          <img src="~assets/image/member/ic_down.png">
          <span class="no-hover" style="border: none;color: #007EFF">{{formatI18n('/公用/查询条件', '展开')}}</span>
        </span>
      </el-col>
    </el-row>
    <el-row v-show="opened">
      <slot name="opened"></slot>
      <el-row>
        <el-col :span="24">
          <div class="opened-action">
            <el-button @click="onSearch" class="btn-search" size="medium" type="primary">{{formatI18n('/公用/券模板', '查询')}}</el-button>
            <el-button @click="onReset" class="btn-reset" size="medium">{{formatI18n('/公用/券模板', '重置')}}</el-button>
            <span @click="onToggle" class="ellipsis-btn" v-if="showExpand">
              <img src="~assets/image/member/ic_zhankai.png">
              <span class="no-hover" style="border: none;color: #007EFF">{{formatI18n('/公用/查询条件', '收起')}}&nbsp;</span>
            </span>
          </div>
        </el-col>
      </el-row>
    </el-row>
  </div>
</template>

<script lang="ts" src="./QueryCondition.ts"/>

<style lang="scss">
.query-condition {
  padding: 0 0px;

  .el-row {
    margin-bottom: 8px;
    &:last-child {
      margin-bottom: 0;
    }
  }

  .ellipsis-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    height: 32px;
    margin-left: 12px;
    img {
      margin-right: 4px;
    }

  }

  .el-input__inner {
    font-size: 13px !important;
  }

  .query-line {
    padding: 5px;
  }

  .closed-action {
    padding-left: 130px;
    display: flex;
  }
  .opened-action {
    display: flex;
    float: left;
    margin-left: 130px;
  }
  .no-hover {
    font-size: 13px;
    &:hover {
      background-color: white;
    }
  }
}
</style>
