import BWeimobExtLimitGoodsCategoryRule from 'model/common/weimob/BWeimobExtLimitGoodsCategoryRule'
import BWeimobExtLimitGoodsGroupRule from 'model/common/weimob/BWeimobExtLimitGoodsGroupRule'
import BWeimobExtLimitGoodsRule from 'model/common/weimob/BWeimobExtLimitGoodsRule'

export default class BWeimobExtGoodsUseRule {
  /**
   * 是否限制商品
   * false 不限商品, true 限制商品
   */
  limitedGoods: Nullable<boolean> = false
  /**
   * 限制商品类型
   * goods 限制商品类型 ,goodsCategory 限制商品类别,  goodsGroup 限制商品组
   */
  limitedGoodsType: Nullable<string> = null
  // 限制商品规则
  limitGoodsTypeRule: Nullable<BWeimobExtLimitGoodsRule> = null
  // 限制商品类别规则
  limitGoodsCategoryTypeRule: Nullable<BWeimobExtLimitGoodsCategoryRule> = null
  // 限制商品类别规则
  limitGoodsGroupRule: Nullable<BWeimobExtLimitGoodsGroupRule> = null
}