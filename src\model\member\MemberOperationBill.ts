import IdName from "model/common/IdName";
import MemberOperationBillLine from "./MemberOperationBillLine";
import MemberOperationBillLog from "./MemberOperationBillLog";

export default class MemberOperationBill {
    // "单号
    billNumber: Nullable<string> = null;
    // "状态INITIAL：未审核；AUDITED：已审核
    state: Nullable<'INITIAL' | 'AUDITED'> = null;
    // "来源：impt-导入；create-界面新建
    source: Nullable<string> = null;
    // "发生组织
    occurredOrg: Nullable<IdName> = null;;
    // "营销中心
    marketingCenter: Nullable<string> = null;
    // "审核时间
    audited: Nullable<string> = null;;
    // "审核人
    auditor: Nullable<string> = null;
    // "创建时间
    created: Nullable<string> = null;;
    // "最后修改时间
    lastModified: Nullable<string> = null;;
    // "明细
    lines: Nullable<MemberOperationBillLine[]> = null;
    // 摘要
    summary: Nullable<string> = null
    // 日志
    log: Nullable<MemberOperationBillLog[]> = null
}