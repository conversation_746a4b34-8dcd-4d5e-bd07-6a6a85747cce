<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:05
 * @LastEditTime: 2024-05-06 14:02:49
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\member\data\dialog\AdjustMemberLevelDialog.vue
 * 记得注释
-->
<template>
    <el-dialog :before-close="doBeforeClose" :close-on-click-modal="false"
               :title="title"
               :visible.sync="dialogShow" append-to-body class="adjust-member-level-dialog">
        <div class="wrap">
            <el-form :model="ruleForm" :rules="rules" class="demo-ruleForm" label-width="100px" ref="ruleForm">
                <el-form-item :label="formatI18n('/会员/会员资料', '当前等级')">
                    <el-input disabled v-model="ruleForm.level"></el-input>
                </el-form-item>
                <el-form-item :label="formatI18n('/会员/会员资料', '调整后等级')" prop="adjustLevel">
                    <el-select :placeholder="formatI18n('/会员/会员资料', '请选择')" @change="doLevelChange" v-model="ruleForm.adjustLevel">
                        <el-option :label="item.name" :value="item.code" :key="item.code" v-for="item in memberLevel">[{{item.code}}]{{item.name}}</el-option>
                    </el-select>
                </el-form-item>
                <el-form-item :label="formatI18n('/会员/会员资料', '当前等级有效期')">
                    <el-date-picker :placeholder="formatI18n('/会员/会员资料', '请选择日期')"
                                    disabled
                                    format="yyyy-MM-dd"
                                    style="width: 100%;"
                                    v-model="ruleForm.curLevel"
                                    value-format="yyyy-MM-dd">
                    </el-date-picker>
                </el-form-item>
                <el-form-item :label="formatI18n('/会员/会员资料', '调整后等级有效期')" prop="adjustDate">
                    <el-date-picker :disabled="adjustDateDisabled"
                                    :placeholder="formatI18n('/会员/会员资料', '请选择日期')"
                                    format="yyyy-MM-dd"
                                    style="width: 100%;"
                                    v-model="ruleForm.adjustDate"
                                    value-format="yyyy-MM-dd">
                    </el-date-picker>
                </el-form-item>
                <el-form-item :label="formatI18n('/会员/会员资料', '调整后说明')">
                    <el-input :placeholder="formatI18n('/会员/会员资料', '请输入不超过50个字')" maxlength="50" type="textarea" v-model="ruleForm.remark"></el-input>
                </el-form-item>
            </el-form>
        </div>
        <div class="dialog-footer" slot="footer">
            <el-button @click="doCancel">{{formatI18n('/公用/按钮', '取消')}}</el-button>
            <el-button @click="doModalClose" size="small" type="primary">{{formatI18n('/公用/按钮', '确定')}}</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts" src="./AdjustMemberLevelDialog.ts">
</script>

<style lang="scss">
.adjust-member-level-dialog{
    display: flex;
    align-items: center;
    justify-content: center;
    .wrap{
        .el-form-item__label{
            width: 140px !important;
        }
        .el-form-item__content{
          margin-left: 140px !important;
        }
        .el-select{
            width: 100% !important;
        }
    }
    .el-dialog{
        width: 500px;
    }
}
</style>