/*
 * @Author: 黎钰龙
 * @Date: 2024-08-13 10:39:26
 * @LastEditTime: 2024-08-13 11:39:26
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\card\writeCard\WriteCardProcessingResult.ts
 * 记得注释
 */
import WriteCardBill from "./WriteCardBill"

export default class WriteCardProcessingResult extends WriteCardBill {
  // 下次写卡卡号
  nextCardCode: Nullable<string> = null
  // 下次状态:-6——清卡失败，-5——读卡失败，-4——写卡失败，-3——空卡校验失败，-2——检查插卡失败，-1——打卡卡机失败，0——未开始，1——读卡成功，2——开始下次写卡，3——清卡成功
  nextState: Nullable<number> = null
  // 剩余数量
  remainCount: Nullable<number> = null
  // 下下次写卡卡号
  afterNextCardCode: Nullable<string> = null
  // 写卡成功数量
  successCount: Nullable<number> = null
  // 已写卡数量
  writeCount: Nullable<number> = null
}