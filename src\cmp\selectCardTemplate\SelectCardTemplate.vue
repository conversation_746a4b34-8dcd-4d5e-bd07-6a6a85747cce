<template>
  <div class="seletc-card-template">
    <el-form ref="cardForm" :model="form" :rules="rules">
        <div>
            <span class="tips">
                {{i18n('选择全部包括后续新增的卡模板')}}
            </span>
        </div>
        <div>
            <el-radio-group v-model="cardType" @change="cardTypeChange">
                <el-radio label="none">{{i18n('指定预付卡')}}</el-radio>
                <el-radio label="rechargeableCard">{{i18n('全部储值卡')}}</el-radio>
                <el-radio label="all">{{i18n('全部预付卡（储值卡和礼品卡）')}}</el-radio>
            </el-radio-group>
        </div>
        <div v-if="cardType === 'none'">
            <div>
                <el-form-item prop="check">
                    <el-button type="primary" @click="$refs.cardTemplateSelectorDialog.open(JSON.parse(JSON.stringify(prevSelectedCardTemplates)))">
                            选择
                    </el-button>
                </el-form-item>
            </div>
            <div>
                <el-row v-for="(item, index) of cardTemplates" :key="index">
                    <el-button type="text" no-i18n @click="gotoTplDtl(item.id)">{{ item.name }}</el-button>&nbsp;&nbsp;
                    <i class="el-icon-close" @click="cardTemplates.splice(index, 1);prevSelectedCardTemplates.splice(index, 1)"></i>
                </el-row>
            </div>
        </div>
    </el-form>
    <CardTemplateSelectorDialog no-i18n ref="cardTemplateSelectorDialog" :filter="cardTemplateFilter" @summit="doCardTemplateSelected"></CardTemplateSelectorDialog>
  </div>
</template>

<script lang="ts" src="./SelectCardTemplate.ts"></script>

<style lang="scss">
    .seletc-card-template {
        .tips {
            color: #cccccc;
        }
    }
</style>