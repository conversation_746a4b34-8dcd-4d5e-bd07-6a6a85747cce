import { Vue, Component, Prop } from 'vue-property-decorator';

// 投放工具栏
@Component({
  name: 'SettingToolbar',
  components: {},
})
export default class SettingToolbar extends Vue {
  @Prop({
    default: () => {
      return { top: 0, right: 0 };
    },
  })
  position: any;
  @Prop({ default: 'top-left' })
  placement: 'top-left' | 'top-right' | 'bottom-right' | 'bottom-left' | string[];
  @Prop({ default: () => [] })
  btns: any[];
  @Prop({ type: Boolean, default: true })
  isShowPutIn: boolean;

  get borderRadius() {
    switch (this.placement) {
      case 'top-left':
        return '10px 0 0 0';
      case 'top-right':
        return '0 10px 0 0';
      case 'bottom-right':
        return '0 0 10px 0';
      case 'bottom-left':
        return '0 0 0 10px';
      default:
        return `${this.placement.includes('top-left') ? '10px' : '0'} ${
          this.placement.includes('top-right') ? '10px' : '0'
        } ${this.placement.includes('bottom-right') ? '10px' : '0'} ${
          this.placement.includes('bottom-left') ? '10px' : '0'
        }`;
    }
  }

  onClick(action: string) {
    this.$emit('click', action);
  }
}
