import {Component, Prop, Vue, Watch} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import MemberApi from 'http/member_v2/MemberApi'
import MemberDetail from 'model/member_v2/member/MemberDetail'
import DateUtil from 'util/DateUtil'
import AddressSelector from "cmp/addressselector/AddressSelector";

@Component({
  name: 'EditDataDialog',
  components: {
    FormItem,
    AddressSelector,
  }
})
export default class EditDataDialog extends Vue {
  options: any = []
  ruleForm: any = {
    mobile: '',
    name: '',
    gender: '',
    birthday: '',
    idCard: '',
    education: '',
    industry: '',
    annualIncome: '',
    hobbies: '',
    spareMobile: '',
    email: '',
    address: [],
    remark: '',
    addressInfo: ''
  }
  $refs: any
  query: MemberDetail = new MemberDetail()
  @Prop()
  data: MemberDetail
  @Prop()
  title: any
  @Prop({
    type: Boolean,
    default: false
  })
  dialogShow: boolean

  rules = {
    mobile: [
      {
        validator: (rule: any, value: any, callback: any) => {
          if (!value) {
            callback(new Error('请输入手机号'))
          } else {
            let regex = /^[0-9]\d*$/g
            if (regex.test(value as string)) {
              MemberApi.checkMobile(value as string, this.data.memberId as any).then((resp: any) => {
                if (resp && resp.code === 2000) {
                  if (resp.data) {
                    callback(new Error('手机号重复'))
                  } else {
                    callback()
                  }
                }
              }).catch((error: any) => {
                if (error && error.message) {
                  this.$message.error(error.message)
                }
              })
            } else {
              if (!value) {
                callback(new Error('手机号最少1位最多18位数字'))
              } else {
                callback(new Error('手机号输入不合法'))
              }
            }
          }
        }, trigger: 'blur'
      }
    ],
    idCard: [
      {
        validator: (rule: any, value: any, callback: any) => {
          if (value) {
            let regex = /^[0-9a-zA-Z]+$/
            if (regex.test(value as string)) {
              callback()
            } else {
              callback(new Error('请输入正确的身份证号'))
            }
          } else {
            callback()
          }
        }, trigger: 'blur'
      }
    ],
    email: [
      {
        validator: (rule: any, value: any, callback: any) => {
          if (value) {
            let regex = /^([a-zA-Z]|[0-9])(\w|\-|\.)+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/
            if (regex.test(value as string)) {
              callback()
            } else {
              callback(new Error('邮箱格式不合法'))
            }
          } else {
            callback()
          }
        }, trigger: 'blur'
      }
    ],
    spareMobile: [
      {
        validator: (rule: any, value: any, callback: any) => {
          if (value) {
            let regex = /^[0-9]\d*$/g
            if (regex.test(value as string)) {
              callback()
            } else {
              callback(new Error('手机号输入不合法'))
            }
          } else {
            callback()
          }
        }, trigger: 'blur'
      }
    ]
  }

  @Watch('dialogShow')
  onDataChange(value: any) {
    if (value) {
      if (this.data) {
        this.ruleForm.mobile = this.data.mobile as any
        this.ruleForm.name = this.data.name as any
        this.ruleForm.gender = this.data.gender as any
        if (this.data.birthday) {
          this.ruleForm.birthday = DateUtil.format(this.data.birthday, 'yyyy-MM-dd')
        }
        this.ruleForm.idCard = this.data.idCard as any
        this.ruleForm.education = this.data.education as any
        this.ruleForm.industry = this.data.industry as any
        this.ruleForm.annualIncome = this.data.annualIncome as any
        this.ruleForm.hobbies = this.data.hobbies as any
        this.ruleForm.spareMobile = this.data.spareMobile as any
        this.ruleForm.email = this.data.email as any
        this.ruleForm.remark = ''
        if (this.data.province && this.data.province.name) {
          this.ruleForm.address = [this.data.province, this.data.city, this.data.district, this.data.street]
        }
        this.ruleForm.addressInfo = this.data.address as any
      }
    }
  }

  created() {
  }

  doBeforeClose(done: any) {
    this.$emit('dialogClose')
    done()
  }

  doModalClose() {
    this.$refs['ruleForm'].validate((valid: any) => {
      if (valid) {
        this.transPrams()
        MemberApi.saveMemberBasicData(this.query, this.data.memberId as any).then((resp: any) => {
          if (resp && resp.code === 2000) {
            this.$message.success('编辑成功')
            this.$emit('dialogClose')
          }
        }).catch((error: any) => {
          if (error && error.message) {
            this.$message.error(error.message)
          }
        })
      } else {
        return false;
      }
    })
  }

  doCheckGoods() {
    // todo
  }

  doCancel() {
    this.$emit('dialogClose')
  }

  private transPrams() {
    this.query.memberId = this.data.memberId
    this.query.oldMobile = this.data.mobile as any  // 常浩说默认是mobile
    this.query.mobile = this.ruleForm.mobile as any
    this.query.name = this.ruleForm.name
    this.query.gender = this.ruleForm.gender
    if (this.ruleForm.birthday) {
      this.query.birthday = DateUtil.format(this.ruleForm.birthday, 'yyyy-MM-dd')
    } else {
      this.query.birthday = null
    }
    this.query.idCard = this.ruleForm.idCard
    this.query.education = this.ruleForm.education
    this.query.industry = this.ruleForm.industry
    this.query.annualIncome = this.ruleForm.annualIncome
    this.query.hobbies = this.ruleForm.hobbies
    this.query.spareMobile = this.ruleForm.spareMobile
    this.query.email = this.ruleForm.email
    this.query.remark = this.ruleForm.remark
    if (this.ruleForm.address && this.ruleForm.address.length > 0) {
      this.query.province = this.ruleForm.address[0]
      this.query.city = this.ruleForm.address[1]
      this.query.district = this.ruleForm.address[2]
      this.query.street = this.ruleForm.address[3]
    } else {
      this.query.province = null
      this.query.city = null
      this.query.district = null
      this.query.street = null
    }
    if (this.ruleForm.addressInfo && this.ruleForm.addressInfo.length > 0) {
      this.query.address = this.ruleForm.addressInfo
    } else {
      this.query.address = ''
    }
  }
}