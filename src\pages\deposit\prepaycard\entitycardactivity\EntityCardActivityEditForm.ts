/*
 * @Author: 黎钰龙
 * @Date: 2023-12-13 18:01:06
 * @LastEditTime: 2025-03-26 19:48:03
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\prepaycard\entitycardactivity\EntityCardActivityEditForm.ts
 * 记得注释
 */
import GiftCardSaleSpecs from 'model/card/activity/GiftCardSaleSpecs';
import GiftCardActivity from 'model/card/activity/GiftCardActivity';
import ActivityBody from 'model/common/ActivityBody';
import GiftCardRuleDetail from 'model/card/activity/GiftCardRuleDetail';
import DateUtil from 'util/DateUtil';
import StoreRange from "model/common/StoreRange";
import { CardMedium } from 'model/default/CardMedium';
import { FavType } from 'model/default/FavType';
import ActivityDateTimeCondition from "model/common/ActivityDateTimeCondition";

class SaleSpecFormData extends GiftCardSaleSpecs {
  checked: boolean = false // 是否选中
  limit: boolean = false // 发售限制
  givePoints: boolean = false // 赠送积分
  giveCoupons: boolean = false // 赠送券
  priceTmp: number = 0
  cardTemplateType: string = '' // 卡类型
  count: number = 0 //次数
}

class EntityCardActivityFormData {
  stores: Nullable<StoreRange> = new StoreRange() // 活动门店
  name: Nullable<string> = null // 活动名称
  timeRange: Date[] = [] // 活动时间
  saleSpecs: SaleSpecFormData[] = [] // 多选卡模板数组
  cardTemplateNumber: Nullable<string> = null // 卡模板号
  remark: Nullable<string> = null // 使用须知
  topic: Nullable<any> = null
  favType: string = 'discount'
  cardMedium: string = 'online'
  cardType: string = 'GiftCard'
    	// 活动时间和时间限制
	activityDateTimeCondition = new ActivityDateTimeCondition()
}

class EntityCardActivityEditForm {
  data: EntityCardActivityFormData = new EntityCardActivityFormData()
  master: any
  rules: any

  get discountNeed() {
    return this.master.form.data.favType == 'discount'
  }

  get priceNeed() {
    return this.master.form.data.favType == 'amount'
  }

  init(master: any) {
    this.master = master
    this.rules = {
      name: [
        { required: true, message: this.master.i18n('请输入活动名称'), trigger: ['change', 'blur'] },
        { min: 1, max: 80, message: this.master.i18n('长度在80个字符以内'), trigger: ['change', 'blur'] }
      ],
      timeRange: [
        {
          validator: (rule: any, value: any, callback: any) => {
            if (!this.data.timeRange || this.data.timeRange.length === 0) {
              callback(new Error(this.master.i18n('请选择活动时间')))
            }
            callback()
          },
          trigger: ['change', 'blur']
        }
      ],
      cardTemplateNumber: [
        {
          required: true,
          validator: (rule: any, value: any, callback: any) => {
            if (!this.data.saleSpecs.length) {
              callback(new Error(this.master.i18n('请选择卡模板')))
            }
            callback()
          },
          trigger: ['change', 'blur']
        }
      ],
      saleSpecs: [
        { required: true, type: 'array', message: this.master.i18n('请选择卡模板'), trigger: ['change', 'blur'] }
      ],
      // 优惠类型
      cardType: [
        { required: true, trigger: ['change', 'blur'] }
      ],
      favType: [
        { required: true, trigger: ['change', 'blur'] }
      ]
    }
  }

  toParams() {
    let formData: EntityCardActivityFormData = JSON.parse(JSON.stringify(this.data))
    let activity = new GiftCardActivity()
    activity.body = new ActivityBody()
    activity.detail = new GiftCardRuleDetail()
    // 活动名称
    activity.body.name = formData.name
    // 活动门店
    activity.body.stores = formData.stores
    // 活动时间
    activity.detail.dateTimeCondition = formData.activityDateTimeCondition.dateTimeCondition
    activity.body.beginDate = formData.activityDateTimeCondition.beginDate
    activity.body.endDate = formData.activityDateTimeCondition.endDate
    //活动主题
    activity.body.topicCode = formData.topic?.code
    activity.body.topicName = formData.topic?.name
    //卡类型
    activity.detail.cardType = formData.cardType
    //卡介质
    activity.detail.cardMedium = formData.cardMedium as CardMedium
    //优惠类型
    activity.detail.favType = formData.favType as FavType
    //卡模板
    let formSpecs = [...formData.saleSpecs]
    let arr = []
    for (let spec of formSpecs) {
      if (spec.gift) {
        spec.gift.points = spec.givePoints ? spec.gift.points : null
        spec.gift.couponItems = spec.giveCoupons ? spec.gift.couponItems : []
      }
      if (!spec.limit) {
        spec.total = null
      }
      let specTmp = new GiftCardSaleSpecs()
      specTmp.cardTemplateNumber = spec.cardTemplateNumber
      specTmp.cardTemplateName = spec.cardTemplateName
      specTmp.faceAmount = spec.faceAmount
      specTmp.discount = formData.favType === 'discount' ? spec.discount : null
      specTmp.price = (formData.favType == 'discount' ? (parseFloat(spec.discount as any) * parseFloat(spec.cardTemplateType === 'COUNTING_CARD' ? spec.templatePrice : spec.faceAmount as any) / 10) : (spec.price ? parseFloat(spec.price as any) : null))?.toFixed(2) as any
      specTmp.total = spec.total ? parseFloat((spec as any).total) : null
      specTmp.templatePrice = spec.templatePrice
      specTmp.gift = spec.gift
      specTmp.validityInfo = spec.validityInfo
      specTmp.cardTemplateType = spec.cardTemplateType
      specTmp.count = spec.cardTemplateType === 'COUNTING_CARD' ? spec.count : null
      arr.push(specTmp)
    }
    activity.detail.saleSpecs = arr
    // 备注
    activity.body.remark = formData.remark
    return activity
  }

  of(activity: GiftCardActivity) {
    if (!activity?.body || !activity?.detail) return
    this.data.name = activity.body.name
    this.data.activityDateTimeCondition.beginDate = activity.body.beginDate
    this.data.activityDateTimeCondition.endDate = activity.body.endDate
    this.data.activityDateTimeCondition.dateTimeCondition = activity.detail.dateTimeCondition
    this.data.remark = activity.body.remark
    this.data.stores = activity.body.stores
    this.data.topic = { code: activity.body.topicCode, name: activity.body.topicName }
    this.data.cardType = activity.detail.cardType || ''
    this.data.cardMedium = activity.detail.cardMedium || ''
    this.data.favType = activity.detail.favType || ''
    this.data.saleSpecs = activity.detail.saleSpecs as any
    (this.data.saleSpecs || []).forEach((element: any) => {
      element.checked = false
      element.limit = element.total !== null
      element.givePoints = element.gift.points !== null
      element.giveCoupons = element.gift.couponItems.length !== 0
      element.templatePrice = element.templatePrice
      element.name = element.cardTemplateName
      element.number = element.cardTemplateNumber
      element.total = element.total
    })
  }
}

export { SaleSpecFormData, EntityCardActivityFormData, EntityCardActivityEditForm }
