import BaseCouponActivity from 'model/coupon/activity/BaseCouponActivity'
import WeiXinMerchantCoupon from 'model/weixin/weixinIssueCouponActivity/WeiXinMerchantCoupon'

export default class WeiXinPayMarketingActivity extends BaseCouponActivity {
  // 券信息
  merchantCoupon: Nullable<WeiXinMerchantCoupon> = null
  // 微信商家券核销方式
  weiXinWriteOffType: Nullable<string> = null
  // 小程序appid
  appId: Nullable<string> = null
  // 调整路径
  path: Nullable<string> = null
}