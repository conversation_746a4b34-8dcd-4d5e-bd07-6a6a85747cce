import { Component, Vue, Watch } from 'vue-property-decorator'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import GoodsScopeEx from 'cmp/goodsscope/GoodsScopeEx.vue'
import RSGrade from "model/common/RSGrade";
import CardTemplateSelectorDialog from "cmp/selectordialogs/CardTemplateSelectorDialog";
import CardTemplate from "model/card/template/CardTemplate";
import IdName from "model/common/IdName";
import CardTemplateFilter from "model/card/template/CardTemplateFilter";
import DateUtil from "util/DateUtil";
import ActivityTopicApi from "http/v2/controller/points/topic/ActivityTopicApi";
import ActivityTopic from "model/v2/controller/points/topic/ActivityTopic";
import EditType from 'common/EditType';
import ActiveStore from "cmp/activestore/ActiveStore";
import I18nPage from "common/I18nDecorator";
import PrepayCardChargeGiftPermission from "./PrepayCardChargeGiftPermission";
import RoutePermissionMgr from "mgr/RoutePermissionMgr";
import ActivityDateTimeConditionPicker from 'cmp/date-time-condition-picker/ActivityDateTimeConditionPicker';
import CardDepositActivityApi from 'http/cardDepositActivity/CardDepositActivityApi';
import PrepayCardChargeGiftForm from './PrepayCardChargeGiftForm'
import CardDepositRuleLine from 'model/cardDepositActivity/CardDepositRuleLine';
import AutoFixInput from 'cmp/autofixinput/AutoFixInput';
import PageConfigApi from "http/pageConfig/PageConfigApi";
import RichText from 'cmp/rich-text/RichText';
import ActiveAddCoupon from "cmp/activeaddcoupon/ActiveAddCoupon.vue";
import GiftInfo from "model/common/GiftInfo";

@Component({
	name: "PrepayCardChargeGiftEdit",
	components: {
		BreadCrume,
		GoodsScopeEx,
		ActiveStore,
		CardTemplateSelectorDialog,
		ActiveAddCoupon,
		ActivityDateTimeConditionPicker,
		AutoFixInput,
    RichText
	},
})
@I18nPage({
	prefix: ["/储值/预付卡/预付卡支付活动/编辑页面", "/公用/活动/状态", "/公用/活动/活动信息", "/公用/活动/提示信息", "/公用/js提示信息", "/公用/按钮", '/储值/预付卡/预付卡充值有礼', '/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡'],
})
export default class PrepayCardChargeGiftEdit extends Vue {
	get dateRangeOption() {
		return {
			disabledDate(time: any) {
				return time.getTime() < DateUtil.nowDayTime();
			},
		};
	}
	i18n: (str: string, params?: string[]) => string;
	$refs: any;
	panelArray: any = [
		{
			name: this.i18n("预付卡充值有礼"),
			url: "prepay-card-charge-gift",
		},
		{
			name: "",
			url: "",
		},
	];
	state = "";
	giftData: any = [];
	form: PrepayCardChargeGiftForm = new PrepayCardChargeGiftForm();
	editType: EditType = EditType.CREATE;
	activityId: Nullable<string> = null;
	gradeList: RSGrade[] = [];
	cardTemplateFilter: CardTemplateFilter = new CardTemplateFilter();
	prevSelectedCardTemplates: CardTemplate[] = [];
	permission = new PrepayCardChargeGiftPermission();
	themes: ActivityTopic[] = [];
	editable: Boolean = true
	locale: any = sessionStorage.getItem('locale')
  showPrepayCardChargeGiftActivityDiscount: boolean = false
	customRules: any = {
		required: true, message: this.i18n('请填写必填项'), trigger: 'blur'
	}

	created() {
	this.giftData = [
		{
			stepValue: "",
			couponQty: "",
			coupon: [],
			rebateAmount: "",
			points: '',
			step: '',
			equityCards: [] //权益卡列表
		},
	];
    this.form.init(this);
    this.getPageConfig()
	}

	get customDiscountRule() {
		return ((index: number) =>{
			let rule = {
				validator: ((rule: any, value: any, callback: any)=>{
					if (!this.form.data.lines[index].rebateCheck && !this.form.data.lines[index].discount) {
						callback(new Error(this.i18n('请填写必填项')))
					} else {
						callback()
					}
				}), trigger: 'blur'
			}
			return rule
		})
	}

	get customAmountRules() {
		return ((index: number) =>{
			let rule = {
				validator: ((rule: any, value: any, callback: any)=>{
					if (this.form.data.lines[index].rebateCheck && !this.form.data.lines[index].rebateAmount) {
						callback(new Error(this.i18n('请填写必填项')))
					} else {
						callback()
					}
				}), trigger: 'blur'
			}
			return rule
		})
	}

	get customPerRules() {
		return ((index: number) =>{
			let rule = {
				validator: ((rule: any, value: any, callback: any)=>{
					if (this.form.data.lines[index].rebateCheck && !this.form.data.lines[index].rebatePercentage) {
						callback(new Error(this.i18n('请填写必填项')))
					} else {
						callback()
					}
				}), trigger: 'blur'
			}
			return rule
		})
	}
  @Watch('form.data.cardTemplates')
  onCardChange(value: any) {
    this.$refs.form.validateField('cardTemplates')
  }
  @Watch('form.data.pointCheck')
  onPointChange(value: any) {
    this.$refs.form.validateField('amount')
    this.$refs.form.validateField('points')
  }

	mounted() {
		let number = 52.555
		console.log(number.toFixed(2));
		
		this.cardTemplateFilter.typeIn = ['RECHARGEABLE_CARD'];
		this.editType = this.$route.query.editType as EditType || EditType.CREATE;;
		this.activityId = this.$route.query.activityId as string;
		this.getTheme();
		this.panelArray[0].name = this.i18n("预付卡充值有礼");
		if (this.editType === EditType.EDIT) {
			this.getDetail()
			this.panelArray[1].name = this.i18n("修改预付卡充值有礼活动");
		}else if (this.editType === EditType.COPY) {
			this.getDetail()
			this.panelArray[1].name = this.i18n("新建预付卡充值有礼活动");
		} else {
			this.panelArray[1].name = this.i18n("新建预付卡充值有礼活动");
		}
		
	}

	isRepeat(arr: any[], key: any) {
		var hash: any = {};
		for(var i in arr) {
		  if(hash[arr[i][key]]) {
			return true;
		  }
		  // 不存在该元素，则赋值为true，可以赋任意值，相应的修改if判断条件即可
		  hash[arr[i][key]] = true;
		}
		return false;
	  }

	validateLines() {
		if (this.form.data.lines.length === 0) {
			this.$message.warning('至少有一个阶梯')
			return false
		}
		for (const item of this.form.data.lines) {
			if (!item.faceAmount) {
				this.$message.warning('请填写充值金额')
				return false
			}
      if (this.showPrepayCardChargeGiftActivityDiscount) {
        if (!item.discount && !item.rebateAmount && !item.rebatePercentage) {
          this.$message.warning('折扣和赠送返现金额至少要填写一项')
          return false
        }
      } else {
        if (
          !item.rebateAmount && !item.rebatePercentage && 
          !this.form.data.pointCheck && !this.form.data.amount && !this.form.data.points
        ) {
          this.$message.warning('赠送返现金额和赠送积分至少要填写一项')
          return false
        }
      }
			
		}
		for (const item of this.form.data.lines) {
			// @ts-ignore
			item.faceAmount = Number(item.faceAmount)!.toFixed(2)
			console.log(item.faceAmount);
			
		}
		if (this.isRepeat(this.form.data.lines, 'faceAmount')) {
			this.$message.warning('充值金额不能重复')
			return false
		}
		return true
	}

	doSave(cb: any) {
		if (this.giftData) {
			let check = true;
			this.giftData.forEach((item: any, index: number) => {
				if (item.coupon.length > 10) {
					this.$message.error("每个面额券模板最多选10个");
					check = false;
					return;
				}
				item.coupon.forEach((item3: any, index: number) => {
					if (item3.qty > 10) {
						this.$message.error("每个券模板最多送10张券");
						check = false;
						return;
					}
				});
			});
			if (!check) {
				return;
			}
		}

		Promise.all([this.$refs.storeScope.validate(), this.$refs.form.validate(), this.$refs.activityDateTimeConditionPicker.validate()]).then((res: any[]) => {
			if (res.filter((e) => !e).length === 0) {
				if (!this.validateLines()) {
					return
				}
				let method: any = null;
				let requestParams = this.form.toParams();
				requestParams.lines.forEach((item: any, index: number) => {
					let giftBag = new GiftInfo();
					item.gift = giftBag;
					item.gift.couponItems = this.giftData[index].coupon;

				});
				for (const item of this.themes) {
					if (item.code === this.form.data.topicCode) {
						requestParams.body!.topicName = item.name
					}
				}
				if (this.editType === EditType.CREATE || this.editType === EditType.COPY) {
					method = CardDepositActivityApi.create;
				}
				if (this.editType === EditType.EDIT) {
					method = CardDepositActivityApi.modify;
					if (requestParams.body) {
						requestParams.body.activityId = this.activityId;
					}
				}
				if (method === null) {
					return;
				}
				method(requestParams)
					.then((res: any) => {
						if (res.code === 2000) {
							if (cb) {
								cb(res);
							} else {
								this.$message.success(this.i18n("保存成功"));
								this.$router.push({ name: "prepay-card-charge-gift-dtl", query: { activityId: res.data } });
							}
						} else {
							this.$message.error(res.msg);
						}
					})
					.catch((reason: any) => {
						this.$message.error(reason.message);
					});
			}
		});
	}

	doSaveAudit() {
		this.doSave((res: any) => {
			CardDepositActivityApi.audit(res.data)
				.then((res: any) => {
					if (res.code === 2000) {
						this.$message.success(this.i18n("审核成功"));
					} else {
						this.$message.error(this.i18n("审核失败，原因：") + res.msg);
					}
				})
				.catch((reason: any) => {
					this.$message.error(this.i18n("审核失败，原因：") + reason.message);
				})
				.finally(() => {
					this.$router.push({ name: "prepay-card-charge-gift-dtl", query: { activityId: res.data } });
				});
		});
	}

	doCancel() {
		this.$router.back();
	}

	doCardTemplateSelected(arr: CardTemplate[]) {
		this.prevSelectedCardTemplates = arr;
		this.form.data.cardTemplates = [];
		for (let tpl of arr) {
			let idName = new IdName();
			idName.id = tpl.number;
			idName.name = tpl.name;
			this.form.data.cardTemplates.push(idName);
		}
	}

	private validateGoodsScope() {
		this.$refs.form.validateField("goods");
	}

	doAddCoupon(index: number) {
		(this.$refs.addCoupon as any)[index].doAddCoupon(index);
	}

	private getDetail() {
		if (!this.activityId) {
			return;
		}
		CardDepositActivityApi.info(this.activityId)
			.then((res: any) => {
				if (res.code === 2000) {
					this.form.of(res.data);
					res.data.lines.forEach((item: any, index: number) => {
						let giftData1 =
							{
								stepValue: "",
								couponQty: "",
								coupon: [],
								rebateAmount: "",
								points: '',
								step: '',
								equityCards: [] //权益卡列表
							};
						this.$set(this.giftData,index,giftData1)
						if (item.gift != null) {
							this.giftData[index].coupon = item.gift.couponItems;
						}
						if (this.showPrepayCardChargeGiftActivityDiscount) {
							item.rebatePercentage = null;
							item.rebateAmount = null;
						} else {
							item.discount = null;
						}
					});
					if (this.editType === "edit") {
						this.state = res.data.body.state;
					}
					this.prevSelectedCardTemplates = [];
					for (let item of this.form.data.cardTemplates) {
						let cardTpl = new CardTemplate();
						cardTpl.number = item.id;
						cardTpl.name = item.name;
						this.prevSelectedCardTemplates.push(cardTpl);
					}
					if (this.editType === EditType.EDIT && (this.form.data.state === 'PROCESSING' || this.form.data.state === 'UNSTART')) {
						this.editable = false
					}
					this.$refs.form.validate()
				} else {
					this.$message.error(res.message);
				}
			})
			.catch((reason: any) => {
				this.$message.error(reason.message);
			});
	}

	private gotoTplDtl(num: string) {
		RoutePermissionMgr.openBlank({ name: "prepay-card-tpl-dtl", query: { number: num } });
	}

	private getTheme() {
		ActivityTopicApi.listTopic().then((resp: any) => {
			if (resp && resp.code === 2000) {
				this.themes = resp.data;
			}
		});
	}

  private getPageConfig() {
    PageConfigApi.getConfig().then((res) => {
      this.showPrepayCardChargeGiftActivityDiscount = res.data?.showPrepayCardChargeGiftActivityDiscount || false
    })
  }

	addLine() {
		if (this.form.data.lines.length === 10) {
			this.$message.warning(this.i18n('最多添加10个阶梯规则'))
		} else {
			let giftData1 =
				{
					stepValue: "",
					couponQty: "",
					coupon: [],
					rebateAmount: "",
					points: '',
					step: '',
					equityCards: [] //权益卡列表
				};
			this.giftData.push(giftData1)
			this.form.data.lines.push(new CardDepositRuleLine())
		}
	}
	removeLine(index: number){
		if (this.form.data.lines.length === 1) {
			this.$message.warning('至少有一个阶梯')
			return 
		}
		this.form.data.lines.splice(index, 1)
		this.giftData.splice(index, 1)
	}
	checkChange(check: Boolean, index: number) {
		console.log(check, index);
		if (check === false) {
			this.$set(this.form.data.lines[index], 'rebateAmount', null)
			this.$set(this.form.data.lines[index], 'rebatePercentage', null)
		}
		this.$refs.form.validate()
	}
	rebateTypeChange() {
		this.form.data.lines.forEach((item: CardDepositRuleLine) => {
			item.rebateAmount = null;
			item.rebatePercentage = null
			item.rebateType = this.form.data.rebateType
		})
	}
}
