<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:02
 * @LastEditTime: 2023-10-08 14:45:13
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\autofixinput\AutoFixInput.vue
 * 记得注释
-->
<template>
  <el-input ref="autoInput" :type="type" v-model="val" @blur="handleBlur" @change="doChange" :disabled="disableVal" :size="size" :placeholder="placeholder">
    <template slot="append" v-if="appendTitle">{{appendTitle}}</template>
  </el-input>
</template>

<script lang='ts' src='./AutoFixInput.ts'/>