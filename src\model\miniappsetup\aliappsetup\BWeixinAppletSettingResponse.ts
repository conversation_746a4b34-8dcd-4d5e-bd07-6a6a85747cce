/*
 * @Author: 黎钰龙
 * @Date: 2024-01-24 14:07:05
 * @LastEditTime: 2024-03-28 14:30:00
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\miniappsetup\aliappsetup\BWeixinAppletSettingResponse.ts
 * 记得注释
 */
import BCouponEntryResponse from './BCouponEntryResponse'
import BWeixinAppletConfig from './BWeixinAppletConfig'
import BannerEntryResponse from './BannerEntryResponse'
import BCustomizeEntryResponse from './BCustomizeEntryResponse'
import SaveActivityDeliveryResponse from './SaveActivityDeliveryResponse'
import WeixinAppNavigation from './WeixinAppNavigation'
// ued配置
export default class BWeixinAppletSettingResponse {
  // 功能入口
  customizeEntry: Nullable<BCustomizeEntryResponse> = new BCustomizeEntryResponse()
  // 会员卡片
  weixinAppletConfig: Nullable<BWeixinAppletConfig> = new BWeixinAppletConfig()
  // 我的优惠券
  couponEntry: Nullable<BCouponEntryResponse> = new BCouponEntryResponse()
  // 轮播图
  bBannerEntry: Nullable<BannerEntryResponse> = new BannerEntryResponse()
  // 活动投放
  activityDelivery: Nullable<SaveActivityDeliveryResponse> = null
  // 小程序导航
  appNavigation: Nullable<WeixinAppNavigation> = null
}