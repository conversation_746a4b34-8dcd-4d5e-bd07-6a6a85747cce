/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-10-18 17:08:09
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\v2\controller\points\GainPointsMetaRule.ts
 * 记得注释
 */
import PushGroup from 'model/precisionmarketing/pushplan/PushGroup'
import BasePointsRule from 'model/v2/controller/points/BasePointsRule'
import PointsMetaRuleItem from 'model/v2/controller/points/meta/PointsMetaRuleItem'

export default class GainPointsMetaRule extends BasePointsRule {
  // 首次初始化标识
  flagId: Nullable<string> = null
  // 每人每天得积分次数上限
  maxDailyGainPointsTimes: Nullable<number> = null
  //  超过每天得积分次数上限，是否冻结会员，true为冻结，false不冻结
  blockMemberExceedMaxDailyGainPointsTimes: Nullable<boolean> = null
  // 每人每天得加倍积分上限:空表示无上限
  maxDailyGainSpeedPoints: Nullable<number> = null
  // 每人每天得积分上限:空表示无上限
  maxDailyGainPoints: Nullable<number> = null
  // 是否开启支付即积分，true开启，false不开启
  payGainPoints: Nullable<boolean> = null
  // 渠道规则类型 SAME-全部渠道适用 相同规则;DIFFER-不同渠道适用 不同规则 ORG 相同渠道不同门店 不同规则
  strategy: Nullable<string> = 'SAME'
  // 规则项
  ruleItems: PointsMetaRuleItem[] = [new PointsMetaRuleItem("1"),new PointsMetaRuleItem("2")]
  // 排除优惠商品
  excludeFavourGoodTypes: string[] = []
  // 排除优惠金额
  excludeFavourAmountTypes: string[] = ["PROMOTION","COUPON","MEMBER","OTHER"]
  // 参与人群
  rule: Nullable<PushGroup> = null
}