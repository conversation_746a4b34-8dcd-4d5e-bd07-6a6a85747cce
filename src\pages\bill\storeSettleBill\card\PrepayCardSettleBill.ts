import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import FormItem from 'cmp/formitem/FormItem';
import MyQueryCmp from 'cmp/querycondition/MyQueryCmp';
import I18nPage from 'common/I18nDecorator';
import {Component, Vue} from "vue-property-decorator";
import IdName from "model/common/IdName";
import SelectStores from 'cmp/selectStores/SelectStores';

import ListWrapper from "cmp/list/ListWrapper";
import DownloadCenterDialog from "pages/main/dowmload_center_dialog/DownloadCenterDialog";
import CardLedgerFilter from "model/bill/storeSettleBill/card/CardLedgerFilter";
import CardLedger from "model/bill/storeSettleBill/card/CardLedger";
import CardLedgerApi from "http/bill/storeSettleBill/card/CardLedgerApi";
@Component({
    name: 'PrepayCardSettleBill',
    components: {
        BreadCrume,
        MyQueryCmp,
        FormItem,
        SelectStores,
        ListWrapper,
        DownloadCenterDialog,
    }
})
@I18nPage({
    prefix: [
        '/公用/券模板',
        '/公用/查询条件/提示',
        '/营销/券礼包活动/券礼包活动',
        '/公用/过滤器',
        '/公用/下拉框/提示',
        '/储值/预付卡/卡模板/编辑页面',
        '/公用/券核销',
        '/储值/预付卡/卡模板/列表页面',
        '/公用/按钮',
        '/公用/活动/提示信息',
        '/数据/快手券账单',
        '/数据/平台券账单',
        '/数据/储值账单',
        '/数据/礼品卡账单'
    ],
    auto: false
})

export default class PrepayCardSettleBill extends Vue {
    $refs: any
    panelArray: any = []
    occurredOrg: Nullable<IdName> = null
    billDate: any = []
    query: CardLedgerFilter = new CardLedgerFilter()
    tableData: CardLedger[] = []
    fileDialogVisible: boolean = false
    showTip: boolean = false
    page: any = {
        pageSize: 10,
        page: 1,
        total: 0,
        probeEnabled: null
    }
    created() {
        this.panelArray = [
            {
                name: this.i18n("礼品卡账单"),
                url: "",
            },
        ]
        this.getList()
    }
    // 获取礼品卡账单信息
    getList() {
        const params = this.getParam()

        CardLedgerApi.prepayCardQuery(params).then(res => {
            if (res.code === 2000) {
                this.tableData = res.data || []
                this.page.total = res.total
                this.page.probeEnabled = res.fields ? res.fields.probeEnabled : null
                console.log('probeEnabled',this.page.probeEnabled)
            } else {
                this.$message.error(res.msg as string)
            }
        }).catch((error) => {
            this.$message.error(error.message)
        })
    }

    // 导出明细行
    doExport() {
        const params = this.getParam()
        params.page = 0
        CardLedgerApi.prepayCardExport(params).then((res) => {
            if (res.code === 2000) {
                this.exportAfter()
            } else {
                this.$message.error(this.i18n(res.msg!) || this.i18n('导出失败'))
            }
        }).catch((error) => {
            this.$message.error(this.i18n(error.message) || this.i18n('导出失败'))
        })
    }

    getParam() {
        const params = new CardLedgerFilter()
        if (this.billDate != null) {
            if (this.billDate[0]) {
                params.settleTimeGreaterOrEquals = (this.billDate[0] + " 00:00:00") as any
            }
            if (this.billDate[1]) {
                params.settleTimeLessOrEquals = (this.billDate[1]  + " 23:59:59") as any
            }
        }
        params.page = this.page.page - 1
        params.pageSize = this.page.pageSize
        if (this.occurredOrg) {
            params.occurredOrgIdEquals = this.occurredOrg.id || null
        }
        return params
    }

    onReset() {
        this.page.page = 1
        this.page.pageSize = 10
        this.occurredOrg = new IdName()
        this.billDate = []
        this.getList()
    }

    onSearch() {
        this.page.page = 1
        this.getList()
    }

    onHandleCurrentChange(val: number) {
        this.page.page = val
        this.getList()
    }

    onHandleSizeChange(val: number) {
        this.page.pageSize = val
        this.getList()
    }

    showIdName(id: String, name: String) {
        if (!id && !name) {
            return '--'
        }
        let orgId = id ? id : '-'
        let orgName = name ? name : '-'
        return `[${orgId}] ${orgName}`
    }

    showChannel(id: String, type: String) {
        if (!id && !type) {
            return '--'
        }
        let orgId = id ? id : '-'
        let orgName = type ? type : '-'
        return `[${orgId}] ${orgName}`
    }

    exportAfter() {
        this.showTip = true;
        this.fileDialogVisible = true;
    }

    getNumber(num:number) {
        if (!num && num !== 0) {
            return '--'
        } else {
            return num
        }
    }

    // 关闭文件中心弹框
    doDownloadDialogClose() {
        this.showTip = false
        this.fileDialogVisible = false
    }

    getCateGory(pltSettleType: any) {
        if (pltSettleType) {
            switch (pltSettleType) {
                case 'PAY':
                    return '消费';
                case 'PAY_ROLLBACK':
                    return '消费冲账';
                case 'PAY_REFUND':
                    return '消费回滚';
                default:
                    return '--';
            }
        }
        return '--';
    }
}