import ApiClient from 'http/ApiClient'
import ExchangeRuleImportResult from 'model/points/activity/pointsexchangegoods/ExchangeRuleImportResult'
import PointsExchangeGoodsActivity from 'model/points/activity/pointsexchangegoods/PointsExchangeGoodsActivity'
import Response from 'model/common/Response'

export default class PointsExchangeGoodsApi {
  /**
   * 积分兑换商品详情
   * 积分兑换商品详情。
   * 
   */
  static detail(activityId: string): Promise<Response<PointsExchangeGoodsActivity>> {
    return ApiClient.server().get(`/v1/points-exchange-goods-activity/detail/${activityId}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 积分兑换商品规则导入
   * 积分兑换商品规则导入。
   * 
   */
  static importRule(body: any): Promise<Response<ExchangeRuleImportResult>> {
    return ApiClient.server().post(`/v1/points-exchange-goods-activity/importRule`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 积分兑换商品修改或保存
   * 积分兑换商品修改或保存。
   * 
   */
  static saveOrModify(body: PointsExchangeGoodsActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/points-exchange-goods-activity/saveOrModify`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
