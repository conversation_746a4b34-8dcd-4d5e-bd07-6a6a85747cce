import ApiClient from 'http/ApiClient'
import FreeGradeDetailBody from 'model/grade/freegrade/FreeGradeDetailBody'
import FreeGradeRatingRule from 'model/grade/freegrade/FreeGradeRatingRule'
import FreeGradeRatingRuleBody from 'model/grade/freegrade/FreeGradeRatingRuleBody'
import GradeBenefitRule from 'model/grade/GradeBenefitRule'
import Response from 'model/common/Response'

export default class FreeGradeApi {
  /**
   * 追加免费等级信息
   *
   */
  static appendRule(body: FreeGradeRatingRuleBody): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/grade/free/appendRule`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 检查成长值是否已经存在
   *
   */
  static checkGrowthValueExist(value: number): Promise<Response<boolean>> {
    return ApiClient.server().get(`/v1/grade/free/checkGrowthValueExist`, {
      params: {
        value: value
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询免费等级权益规则
   *
   */
  static getGradeBenefitRule(): Promise<Response<GradeBenefitRule>> {
    return ApiClient.server().get(`/v1/grade/free/getGradeBenefitRule`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询免费等级评定规则
   *
   */
  static getRatingRule(): Promise<Response<FreeGradeRatingRule>> {
    return ApiClient.server().get(`/v1/grade/free/getRatingRule`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询免费等级信息
   *
   */
  static getRule(): Promise<Response<FreeGradeDetailBody>> {
    return ApiClient.server().get(`/v1/grade/free/get`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 修改免费等级权益规则
   *
   */
  static modifyBenefitRule(body: GradeBenefitRule): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/grade/free/modifyBenefitRule`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 修改免费等级评定规则
   *
   */
  static modifyRatingRule(body: FreeGradeRatingRule): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/grade/free/modifyRatingRule`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 追加免费等级前置信息
   *
   */
  static preAppendRule(): Promise<Response<FreeGradeRatingRuleBody>> {
    return ApiClient.server().get(`/v1/grade/free/preAppendRule`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 保存免费等级信息
   *
   */
  static saveRule(body: FreeGradeRatingRuleBody): Promise<Response<boolean>> {
    return ApiClient.server().post(`/v1/grade/free/save`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询是否开启按累积成长计算等级规则
   * 查询是否开启按累积成长计算等级规则
   * 
   */
   static enableCumulativeGrowthValueSetConfig(): Promise<Response<boolean>> {
    return ApiClient.server().get(`/v1/grade/free/enableCumulativeGrowthValueSetConfig`, {
    }).then((res) => {
      return res.data
    })
  }

}
