<template>
  <div class="member-card">
    <div class="avatar">
      <img v-if="dtl.image"
           :src="dtl.image">
      <img v-else
           src="~assets/image/member/<EMAIL>">
    </div>
    <member-form-item :label="i18n('会员号')"
                      content-align="right">
      <img slot="prefix"
           src="~assets/image/member/<EMAIL>">
      {{ dtl.crmCode }}
    </member-form-item>
    <member-form-item :label="i18n('手机号')"
                      content-align="right" :tooltip="false">
      <img slot="prefix"
           src="~assets/image/member/<EMAIL>">
      <template v-if="dtl.mobileList && dtl.mobileList.length > 0">
        <div v-for="(item, index) in dtl.mobileList"
             :key="index" class="horizontal-layout">
          <member-tip-content class="match" :content="item"></member-tip-content>
          <el-button class="wrap" type="text"
                     v-if="hasOptionPermission(permissionResourceId, '删除手机号')"
                     @click="deleteMobile(item)">
            {{ formatI18n("/公用/按钮/删除") }}
          </el-button>
        </div>
      </template>
      <div v-else>
        --
      </div>
    </member-form-item>
    <member-form-item :label="i18n('实体卡号')"
                      content-align="right">
      <img slot="prefix"
           src="~assets/image/member/<EMAIL>">
      <div style="display: inline-block;margin-top: -9px;"
           v-if="dtl.hdCardCardNumList && dtl.hdCardCardNumList.length > 0">
        <div v-for="(item, index) in dtl.hdCardCardNumList"
             :key="index">
          <span>{{ item }}</span>&nbsp;&nbsp;
          <el-button @click="doUnBind(item)"
                     type="text"
                     v-if="hasOptionPermission(permissionResourceId, '解绑实体会员卡')">
            {{ formatI18n("/会员/会员资料", "解绑") }}
          </el-button>
        </div>
      </div>
      <span style="display: inline-block;"
            v-else>&nbsp;--</span>
    </member-form-item>
    <div class="divider"></div>
    <member-form-item :label="i18n('首次注册渠道') + '：'">
      {{ dtl.registerChannelName | strFormat }}
    </member-form-item>
    <member-form-item :label="i18n('首次注册时间') + '：'">
      {{ dtl.registerTime | dateFormate3 }}
    </member-form-item>
    <member-form-item :label="i18n('首次激活时间') + '：'">
      {{ dtl.activateTime | dateFormate3 }}
    </member-form-item>
    <member-form-item :label="i18n('首次注册门店') + '：'"
                      :tooltip="true">
      <template v-if="dtl.registerStore && dtl.registerStore.id">
      [{{ dtl.registerStore.id }}]{{ dtl.registerStore.name }}
      </template>
      <template v-else>--</template>
    </member-form-item>
    <member-form-item :label="i18n('归属门店') + '：'"
                      :tooltip="true">
      <template v-if="dtl.ownStore && dtl.ownStore.id">
      [{{ dtl.ownStore.id }}]{{ dtl.ownStore.name }}
      </template>
      <template v-else>--</template>
    </member-form-item>
    <member-form-item :label="i18n('会员渠道') + '：'">
      {{ getMemberChannelLabel(dtl.memberChannels) }}
    </member-form-item>
    <member-form-item :label="i18n('邀请人会员') + '：'">
      <el-button @click="doInviteMember" type="text" v-if="hasOptionPermission(permissionResourceId, '会员资料查看') && getInvited">
        {{ getInvited }}
      </el-button>
      <template v-else>--</template>
    </member-form-item>
    <member-form-item :label="i18n('邀请人员工') + '：'">
      <template v-if="dtl.referredEmployee && dtl.referredEmployee.id">
      [{{ dtl.referredEmployee.id }}]{{ dtl.referredEmployee.name }}
      </template>
      <template v-else>--</template>
    </member-form-item>
    <member-form-item :label="i18n('归属导购') + '：'">
      <template v-if="dtl.guider && dtl.guider.id && dtl.guider.name">
      [{{ dtl.guider.id }}]{{ dtl.guider.name }}
      </template>
      <template v-else>--</template>
    </member-form-item>
    <member-form-item :label="i18n('最近修改用户') + '：'"
                      :tooltip="true">
      {{ dtl.modifier | strFormat }}
    </member-form-item>
    <member-form-item :label="i18n('最近修改时间') + '：'">
      {{ dtl.modifed | dateFormate3 }}
    </member-form-item>
    <member-form-item :label="i18n('备注') + '：'"
                      :tooltip="true">
      {{ dtl.remark | strFormat }}
      <el-button slot="right"
                 type="text"
                 @click="editRemark"
                 v-if="hasOptionPermission(permissionResourceId, '编辑资料')">
        {{ formatI18n("/会员/会员资料", "编辑") }}
      </el-button>
    </member-form-item>
    <div class="divider"></div>
    <member-card-title :icon="require('~assets/image/member/<EMAIL>')"
                       :title="i18n('等级')"></member-card-title>
    <member-form-item :label="i18n('等级') + '：'">
      <template v-if="dtl.gradeCode">
      [{{ dtl.gradeCode }}]{{ dtl.gradeName }}
      </template>
      <template v-else>--</template>
    </member-form-item>
    <member-form-item :label="i18n('等级有效期') + '：'">
      {{ dtl.gradeValidate | dateFormate3 }}
    </member-form-item>
    <member-form-item :label="i18n('成长值') + '：'">
      {{ dtl.growthValue | strFormat }}
      <el-button type="text"
                 @click="growthDialogVisible = true">{{ i18n("明细") }}
      </el-button>
    </member-form-item>
    <member-form-item :label="i18n('会员状态') + '：'">
      <el-tag size="small"
              type="success"
              v-if="dtl.state === 'Using'">{{ formatI18n("/会员/会员资料", "使用中") }}
      </el-tag>
      <el-tag size="small"
              type="danger"
              v-if="dtl.state === 'Blocked'">{{ formatI18n("/会员/会员资料", "已冻结") }}
      </el-tag>
      <el-tag size="small"
              type="warning"
              v-if="dtl.state === 'Unactivated'">
        {{ formatI18n("/会员/会员资料", "未激活") }}
      </el-tag>
      <el-tag size="small"
              type="info"
              v-if="dtl.state === 'Canceled'">{{ formatI18n("/会员/会员资料", "已注销") }}
      </el-tag>
    </member-form-item>
    <template v-if="bCards.length > 0">
    <div class="divider"></div>
    <member-card-title :icon="require('~assets/image/member/<EMAIL>')"
                       :title="i18n('付费会员')"
                       :subtitle="i18n('使用中')"></member-card-title>
    <div class="card-container">
      <member-simple-card v-for="item in bCards"
                          :key="item.cardNo"
                          :card="item"></member-simple-card>
    </div>
    </template>
    <template v-if="eCards.length > 0">
    <div class="divider"></div>
    <member-card-title :icon="require('~assets/image/member/<EMAIL>')"
                       :title="i18n('权益卡')"
                       :subtitle="i18n('使用中')"></member-card-title>
    <div class="card-container">
      <member-simple-card v-for="item in eCards"
                          :key="item.cardNo"
                          :card="item"
                          :benefit="false"></member-simple-card>
    </div>
    </template>
    <div class="divider"></div>
    <member-card-title :icon="require('~assets/image/member/<EMAIL>')"
                       :title="i18n('消费数据')"></member-card-title>
    <member-form-item>
      <template slot="label">
      {{ i18n("累计消费(元)") }}
      <el-tooltip effect="light"
                  placement="top">
        <i class="el-icon-warning"></i>
        <div slot="content">
          <div>{{ formatI18n("/会员/会员资料", "截至昨天，顾客累计消费金额") }}</div>
        </div>
      </el-tooltip>
      ：
      </template>
      {{ dtl.totalConsume | strFormat }}
    </member-form-item>
    <member-form-item>
      <template slot="label">
      {{ i18n("累计消费(笔)") }}
      <el-tooltip effect="light"
                  placement="top">
        <i class="el-icon-warning"></i>
        <div slot="content">
          <div>{{ formatI18n("/会员/会员资料", "截至昨天，顾客累计消费笔数") }}</div>
        </div>
      </el-tooltip>
      ：
      </template>
      {{ dtl.consumeQty }}
    </member-form-item>
    <member-form-item>
      <template slot="label">
      {{ i18n("客单价(元)") }}
      <el-tooltip effect="light"
                  placement="top">
        <i class="el-icon-warning"></i>
        <div slot="content">
          <div>{{ formatI18n("/会员/会员资料", "截至昨天，顾客消费的客单价") }}</div>
        </div>
      </el-tooltip>
      ：
      </template>
      {{ dtl.avgAmount | fmt }}
    </member-form-item>
    <member-form-item :label="i18n('最近消费时间') + '：'">
      <template v-if="dtl.lastConsumeDate">
      {{ dtl.lastConsumeDate | dateFormate2 }} {{ getDay }}
      </template>
      <template v-else>--</template>
    </member-form-item>

    <UnBindDialog :dialogShow="unBindFlag"
                  :memberId="dtl.memberId"
                  :title="formatI18n('/公用/弹出模态框提示标题', '提示')"
                  :uuid="bindUuid"
                  @dialogClose="doUnbindClose">
    </UnBindDialog>
    <el-dialog :title="formatI18n('/会员/会员资料', '编辑会员备注')"
               :visible.sync="remarkDialog.visible"
               class="cosparty-dialog-center"
               width="550px">
      <div style="display: flex;align-items: start">
        <div>
          {{ formatI18n("/会员/会员资料", "备注") }}
        </div>
        <div style="width: 10px">&nbsp;</div>
        <el-input type="textarea"
                  :placeholder="formatI18n('/公用/表单校验/请输入不超过{0}个字符', null, [80])"
                  maxlength="80"
                  show-word-limit
                  v-model="remarkDialog.remark"
                  style="width: 450px"></el-input>
      </div>
      <div class="dialog-footer"
           slot="footer">
        <el-button @click="() => remarkDialog.visible = false">{{ formatI18n("/公用/按钮", "取消") }}</el-button>
        <el-button @click="updateRemark"
                   size="small"
                   type="primary">{{ formatI18n("/公用/按钮", "确定") }}
        </el-button>
      </div>
    </el-dialog>
    <member-growth-detail-dialog v-model="growthDialogVisible"
                                 :member-id="dtl.memberId"></member-growth-detail-dialog>
  </div>
</template>
<script lang="ts"
        src="./MemberLeftInfo.ts">
</script>
<style lang="scss"
       scoped>
.member-card {
  padding-top: 80px !important;
  position: relative;

  .avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: white;
    border: solid 8px white;
    position: absolute;
    left: 50%;
    top: -60px;
    transform: translate(-50%);
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
    }
  }
}

.card-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 16px;
}
</style>
