import { Component, Vue, Prop } from 'vue-property-decorator'
import MemberApi from 'http/member_standard/MemberApi'
import MainAndSubCard from 'model/member_standard/MainAndSubCard'

@Component({
  name: 'UnBindSubDialog',
  components: {}
})
export default class UnBindSubDialog extends Vue {
  coupons: any = []
  @Prop()
  uuid: any
  @Prop()
  memberId: any
  @Prop()
  title: any

  @Prop({
    type: Boolean,
    default: false
  })
  dialogShow: boolean

  @Prop()
  params: any

  doBeforeClose(done: any) {
    this.$emit('dialogClose')
    done()
  }
  doModalClose() {
    this.$emit('confirm')
  }
  doCancel() {
    this.$emit('dialogClose')
  }
}