import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import BMemberBalanceRechargeBill from 'model/deposit/activity/BMemberBalanceRechargeBill'
import BMemberBalanceRechargeBillBatchRequest from 'model/deposit/activity/BMemberBalanceRechargeBillBatchRequest'
import BMemberBalanceRechargeBillBatchResponse from 'model/deposit/activity/BMemberBalanceRechargeBillBatchResponse'
import BMemberBalanceRechargeBillFilter from 'model/deposit/activity/BMemberBalanceRechargeBillFilter'
import BMemberBalanceRechargeBillLine from 'model/deposit/activity/BMemberBalanceRechargeBillLine'
import BMemberBalanceRechargeBillLineFilter from 'model/deposit/activity/BMemberBalanceRechargeBillLineFilter'
import BMemberBalanceRechargeBillLog from 'model/deposit/activity/BMemberBalanceRechargeBillLog'
import BMemberBalanceRechargeBillLogFilter from 'model/deposit/activity/BMemberBalanceRechargeBillLogFilter'
import BMemberBalanceRechargeStatistics from 'model/deposit/activity/BMemberBalanceRechargeStatistics'

export default class MemberBalanceRechargeBillApi {
  /**
   * 审核
   * 审核。
   * 
   */
  static audit(billNumber: string): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/member/balance/recharge/bill/audit/${billNumber}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除
   * 删除。
   * 
   */
  static remove(billNumber: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/member/balance/recharge/bill/remove/${billNumber}`, {}, {
    }).then((res) => {
      return res.data
    })
  }


  /**
   * 批量审核
   * 批量审核。
   * 
   */
  static batchAudit(body: BMemberBalanceRechargeBillBatchRequest): Promise<Response<BMemberBalanceRechargeBillBatchResponse>> {
    return ApiClient.server().post(`/v1/member/balance/recharge/bill/batchAudit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量删除
   * 批量删除。
   * 
   */
  static batchRemove(body: BMemberBalanceRechargeBillBatchRequest): Promise<Response<BMemberBalanceRechargeBillBatchResponse>> {
    return ApiClient.server().post(`/v1/member/balance/recharge/bill/batchRemove`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 储值充值单详情
   * 储值充值单详情。
   * 
   */
  static detail(billNumber: string): Promise<Response<BMemberBalanceRechargeBill>> {
    return ApiClient.server().post(`/v1/member/balance/recharge/bill/detail/${billNumber}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 储值充值单查询
   * 储值充值单查询。
   * 
   */
  static query(body: BMemberBalanceRechargeBillFilter): Promise<Response<BMemberBalanceRechargeBill[]>> {
    return ApiClient.server().post(`/v1/member/balance/recharge/bill/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 储值充值单明细查询
   * 储值充值单明细查询。
   * 
   */
  static queryLines(body: BMemberBalanceRechargeBillLineFilter): Promise<Response<BMemberBalanceRechargeBillLine[]>> {
    return ApiClient.server().post(`/v1/member/balance/recharge/bill/queryLines`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 储值充值单操作日志
   * 储值充值单操作日志。
   * 
   */
  static queryLogs(body: BMemberBalanceRechargeBillLogFilter): Promise<Response<BMemberBalanceRechargeBillLog[]>> {
    return ApiClient.server().post(`/v1/member/balance/recharge/bill/queryLogs`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存
   * 保存
   * 
   */
  static save(body: BMemberBalanceRechargeBill): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/member/balance/recharge/bill/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改
   * 修改。
   * 
   */
  static saveModify(body: BMemberBalanceRechargeBill): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/member/balance/recharge/bill/saveModify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询各状态单据数量
   * 查询各状态单据数量。
   * 
   */
  static statistics(body: BMemberBalanceRechargeBillFilter): Promise<Response<BMemberBalanceRechargeStatistics>> {
    return ApiClient.server().post(`/v1/member/balance/recharge/bill/statistics`, body, {
    }).then((res) => {
      return res.data
    })
  }

}