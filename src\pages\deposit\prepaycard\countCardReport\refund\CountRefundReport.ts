import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import TimeRange from '../../cmp/timerange/TimeRange';
import CountingCardReportApi from 'http/prepay/card/CountingCardReportApi';
import GiftCardFilter from 'model/prepay/report/card/GiftCardFilter';
import RSOrg from 'model/common/RSOrg';
import DataUtil from '../../common/DataUtil';
import FormItem from 'cmp/formitem/FormItem';
import I18nPage from 'common/I18nDecorator';
import BrowserMgr from 'mgr/BrowserMgr';
import Zone from 'model/datum/zone/Zone';
import GiftCardCardHst from 'model/prepay/report/card/GiftCardCardHst';
import SelectStores from 'cmp/selectStores/SelectStores';
@Component({
  name: 'CountRefundReport',
  components: {
    TimeRange,
    FormItem,
    SelectStores
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/公用/查询条件/提示',
    '/储值/预付卡/预付卡查询/列表页面',
    '/储值/预付卡/电子礼品卡报表/消费流水',
    '/储值/预付卡/电子礼品卡报表/退款流水',
    '/储值/预付卡/次卡报表'
  ],
  auto: true
})
export default class CountRefundReport extends Vue {
  @Prop({ default() { return [] } }) areaData: Zone[];
  query: GiftCardFilter = new GiftCardFilter()
  dataUtil: DataUtil = new DataUtil()
  queryData: GiftCardCardHst[] = []
  expandQuery: boolean = false
  isMoreMarketing: boolean = false // 是否开启多营销中心 true 开启 false 不开启
  sum: Nullable<number> = null //报表汇总信息
  page = {
    currentPage: 1,
    total: 0,
    size: 10,
    probeEnabled: null
  }
  $refs: any

  @Watch('page', { deep: true, immediate: true })
  handle(value: any) {
    this.query.page = value.currentPage - 1
    this.query.pageSize = value.size
  }

  created() {
    let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
    if (sysConfig) {
      this.isMoreMarketing = sysConfig.enableMultiMarketingCenter;
    }
    this.query.occurredTimeAfterOrEqual = this.dataUtil.format(new Date(), 'yyyy-MM-dd')
    this.query.occurredTimeBefore = this.dataUtil.format(new Date(), 'yyyy-MM-dd')
    this.queryList()
  }

  queryList() {
    CountingCardReportApi.queryConsumeRefundHst(this.query).then((res) => {
      if (res.code === 2000) {
        this.queryData = res.data || []
        this.page.total = res.total
        this.page.probeEnabled = res.fields ? res.fields.probeEnabled : null
      } else {
        this.$message(res.msg || this.i18n('查询退款流水失败'))
      }
    }).catch((err) => {
      this.$message(err.message || this.i18n('查询退款流水失败'))
    })
  }

  handleTimeRange(dateArr: Date[]) {
    this.query.occurredTimeAfterOrEqual = dateArr[0]
    this.query.occurredTimeBefore = dateArr[1]
    this.queryList()
  }

  doSearch() {
    this.page.currentPage = 1
    this.queryList()
  }

  doReset() {
    this.query = new GiftCardFilter()
    this.page.currentPage = 1
    this.$refs['timeRange'].reset()
  }

  gotoTplDtl(num: any) {
    this.$router.push({ name: 'prepay-card-tpl-dtl', query: { number: num, cardTemplateType: 'COUNTING_CARD' } })
  }

  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.queryList()
  }
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.queryList()
  }
};