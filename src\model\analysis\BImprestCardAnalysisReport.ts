import { AnalysisReportDateUnit } from 'model/analysis/AnalysisReportDateUnit'


// 充值卡分析结果
export default class BImprestCardAnalysisReport {
  // 单位
  dateUnit: Nullable<AnalysisReportDateUnit> = null
  // 周
  week: Nullable<number> = null
  // 月
  month: Nullable<number> = null
  // 日期
  analysisDate: Nullable<String> = null
  // 门店
  occurredOrgId: Nullable<string> = null
  // 模板
  templateNumber: Nullable<string> = null
  // 买卡
  buyCard: Nullable<number> = null
  // 转出
  transferOut: Nullable<number> = null
  // 作废
  cancel: Nullable<number> = null
  //可用总金
  usableTotalAmount: Nullable<number> = null
  //可用本金
  usableAmount: Nullable<number> = null
  //可用赠金
  usableGiftAmount: Nullable<number> = null
 
}