import I18nPage from 'common/I18nDecorator';
import SystemConfigApi from 'http/systemConfig/SystemConfigApi';
import IdName from 'model/common/IdName';
import Payment from 'model/common/Payment';
import AmountToFixUtil from 'util/AmountToFixUtil';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
@Component({
  name: 'AddPaymentCmp',
  components: {}
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/储值/预付卡/预付卡充值单'
  ],
  auto: true
})
export default class AddPaymentCmp extends Vue {
  @Prop() value: Nullable<Payment[]>;
  payments: Payment[] = []
  queryPaymentData: IdName[] = []  //当前列表展示数据
  originPaymentData: IdName[] = [] //原始表单数据

  @Watch('value', { deep: true, immediate: true })
  bindWatch() {
    this.bindValue()
  }

  created() {
    this.getPaymentList()
  }

  valueChange() {
    this.$emit('input', this.payments)
    this.$emit('change', this.payments)
  }

  bindValue() {
    if (this.value?.length) {
      this.payments = JSON.parse(JSON.stringify(this.value))
    } else {
      this.payments = []
    }
  }

  addPaymentRow() {
    if (this.payments.length < 10) {
      // 添加新行到cashData数组
      this.payments.push({
        paymentId: '', // 默认支付方式为空字符串
        paymentName: '',
        paymentAmount: null  // 默认金额为null
      });
      this.valueChange()
    } else {
      this.$message.warning(this.i18n('最多支持添加10个支付方式'));
    }
  }

  // 查询支付方式
  getPaymentList() {
    SystemConfigApi.getPayMethodConfig().then((resp) => {
      if (resp.code === 2000) {
        this.queryPaymentData = resp.data?.payMethods || []
        this.originPaymentData = resp.data?.payMethods || []
      } else {
        throw new Error(resp.msg!)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  paymentAmountChange(scope: any) {
    scope.row.paymentAmount = AmountToFixUtil.formatAmount(scope.row.paymentAmount, 99999999.99, 0.01, '')
    this.valueChange()
  }

  handleSelectPayment(row: any) {
    const selectedOption = this.queryPaymentData.find(option => option.id === row.paymentId);
    if (selectedOption) {
      row.paymentName = selectedOption.name;
    } else {
      row.paymentName = '';
    }
    this.valueChange()
  }

  // 删除
  deletePaymentRow(index: number) {
    this.payments.splice(index, 1);
    this.valueChange()
  }

  doValidate() {
    return new Promise<void>((resolve, reject) => {
      for (let i = 0; i < this.payments.length; i++) {
        const item = this.payments[i];
        if (item.paymentId == null || item.paymentId == undefined || item.paymentId == '') {
          this.$message.warning(this.i18n('请选择支付方式'))
          return reject();
        }
        if (item.paymentAmount == null || item.paymentAmount == undefined) {
          this.$message.warning(this.i18n('请输入支付金额'))
          return reject();
        }
      }
      return resolve(); // 所有元素都检查完毕，没有发现空值
    })
  }
};