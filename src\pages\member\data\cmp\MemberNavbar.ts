import { Component, Prop, Vue } from "vue-property-decorator";
import I18nPage from "common/I18nDecorator";

@Component({
  name: "MemberNavbar",
  components: {},
})
@I18nPage({
  prefix: [
    "/会员/会员资料",
  ],
  auto: true,
})
export default class MemberNavbar extends Vue {
  @Prop()
  currentIndex: number;

  navbar: Array<string> = [this.i18n("信息概览"), this.i18n("交易明细"), this.i18n("会员卡详情")];

  onChange(index: number) {
    if (this.currentIndex == index) return;
    this.$emit("change", index);
  }
}
