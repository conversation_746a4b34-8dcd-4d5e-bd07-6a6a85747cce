/*
 * @Author: hl-cool <EMAIL>
 * @Date: 2024-08-01 13:55:11
 * @LastEditors: hl-cool <EMAIL>
 * @LastEditTime: 2024-08-01 13:55:22
 * @FilePath: \phoenix-web-ui\src\model\promotion\exchangeCodeValue\RedemptionCodeBillFilter.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

export default class RedemptionCodeBillFilter {
  // 单号等于
  numberEquals: Nullable<string> = null
  // 名称类似于
  nameLikes: Nullable<string> = null
  // 状态等于: INITAIL-未审核;UNSTART-未开始;PROCESSING-进行中数量；STOPED-已结束
  stateEquals: Nullable<string> = null
  // 
  lastModifiedBetweenClosedClosed: Date[] = []
  // 
  createdBetweenClosedClosed: Date[] = []
  // 
  marketingCenterEquals: Nullable<string> = null
  // 
  page: Nullable<number> = null
  // 
  pageSize: Nullable<number> = null
}