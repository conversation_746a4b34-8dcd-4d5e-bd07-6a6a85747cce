import {Component, Vue} from 'vue-property-decorator'
import I18nApi from 'http/i18n/I18nApi'
import CommonUtil from 'util/CommonUtil'
import BrowserMgr from 'mgr/BrowserMgr'


@Component({
  name: 'NoContent',
  components: {}
})
export default class NoContent extends Vue {
  mounted() {
    I18nApi.gets(CommonUtil.getLocale('locale') as string).then((resp: any) => {
      if (resp && resp.code === 2000) {
        BrowserMgr.SessionStorage.setItem('i18n', resp.data)
        this.$router.push({name: 'login'})
      }
    })
  }
}