import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import FormItem from 'cmp/formitem/FormItem';
import MyQueryCmp from 'cmp/querycondition/MyQueryCmp';
import I18nPage from 'common/I18nDecorator';
import { Component } from 'vue-property-decorator';
import AnalysisDateSelector from '../cmp/AnalysisDateSelector/AnalysisDateSelector.vue';
import ChannelSelect from 'cmp/channelselect/ChannelSelect';
import SelectStores from 'cmp/selectStores/SelectStores';
import MemberLineChart from '../cmp/MemberLineChart/MemberLineChart';
import SelectEmployees from 'cmp/selectEmployees/selectEmployees';
import OrgMemberAnalysisReportQuery from 'model/analysis/OrgMemberAnalysisReportQuery';
import AnalysisReportApi from 'http/analysis/AnalysisReportApi';
import CommonUtil from 'util/CommonUtil';
import DecimalFormatterUtil from 'util/DecimalFormatterUtil';
import OrgMemberAnalysisReport from 'model/analysis/OrgMemberAnalysisReport';
import Abstract<PERSON>ine<PERSON><PERSON> from '../cmp/AbstractLineChart';
import DownloadCenterDialog from 'pages/main/dowmload_center_dialog/DownloadCenterDialog';
import MemberPointsAnalysisReport from "model/analysis/MemberPointsAnalysisReport";

class Filter {
  dataRange: any = null  //时间
  store: any = null  //门店
}

@Component({
  name: 'MemberPointsAnalysis',
  components: {
    BreadCrume,
    MyQueryCmp,
    FormItem,
    AnalysisDateSelector,
    ChannelSelect,
    SelectStores,
    MemberLineChart,
    SelectEmployees,
    DownloadCenterDialog
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/数据/会员增长分析',
    '/数据/会员积分分析'
  ],
  auto: true
})
export default class MemberPointsAnalysis extends AbstractLineChart {
  $refs: any
  panelArray: any = []
  filter: Filter = new Filter()
  detail: MemberPointsAnalysisReport = new MemberPointsAnalysisReport()
  downloadCenterFlag: boolean = false; //文件下载中心弹窗

  /* 每个item的第一项：数据名
  /  第二项：数据值
  /  第三项：是否展示在右y轴上 */
  get valueArray() {
    const arr = [
      [this.i18n('发放'), this.detail.occurredPointsData, 0],
      [this.i18n('消耗'), this.detail.consumePointsData, 0],
      [this.i18n('调整增加'), this.detail.adjustAddPointsData, 0],
      [this.i18n('调整减少'), this.detail.adjustSubPointsData, 0],
      [this.i18n('过期'), this.detail.expiredPointsData, 0],
      [this.i18n('可用积分'), this.detail.usablePointsData, 0],
    ]
    return this.doTransValueArray(arr)
  }

  get summaryViewArr() {
    return [
      {
        label: this.i18n('发放'),
        value: DecimalFormatterUtil.formatNumber(this.detail.summary?.occurredPoints),
      },
      {
        label: this.i18n('消耗'),
        value: DecimalFormatterUtil.formatNumber(this.detail.summary?.consumePoints),
      },
      {
        label: this.i18n('调整增加'),
        value: DecimalFormatterUtil.formatNumber(this.detail.summary?.adjustAddPoints),
      },
      {
        label: this.i18n('调整减少'),
        value: DecimalFormatterUtil.formatNumber(this.detail.summary?.adjustSubPoints),
      },
      {
        label: this.i18n('过期'),
        value: DecimalFormatterUtil.formatNumber(this.detail.summary?.expiredPoints),
      },
      {
        label: this.i18n('可用积分'),
        value: DecimalFormatterUtil.formatNumber(this.detail.summary?.usablePoints),
      }
    ]
  }

  get getSumUsablePoints() {
    let value = this.detail.summary?.sumUsablePoints
    if (value) {
      return DecimalFormatterUtil.formatNumber(value);
    }
    return 0
  }

  get getSumLastExpiredPoints() {
    let value = this.detail.summary?.sumLastExpiredPoints
    if (value) {
      return DecimalFormatterUtil.formatNumber(value);
    }
    return 0
  }

  // 需要显示百分号的数据名称
  get showPercentName() {
    return [this.i18n('新会员有消占比'), this.i18n('复购率')]
  }

  created() {
    this.panelArray = [
      {
        name: this.i18n('/公用/菜单/会员积分分析'),
        url: ""
      }
    ]
  }

  mounted() {
    this.onSearch()
  }


  doExport() {
    this.$confirm(this.i18n("将根据当前查询条件生成报表，确认导出吗？"), this.i18n('/储值/预付卡/充值卡制售单/列表页面/导出'), {
      confirmButtonText: this.formatI18n("/公用/按钮", "确定") as string,
      cancelButtonText: this.formatI18n("/公用/按钮", "取消") as string,
    }).then(() => {
      const body = this.doFilterParams()
      AnalysisReportApi.memberPointsExport(body).then((res) => {
        if (res.code === 2000) {
          this.downloadCenterFlag = true;
        } else {
          throw new Error(res.msg!)
        }
      }).catch((error) => {
        this.$message.error(error.message)
      })
    });
  }

  doReset() {
    this.filter = new Filter()
    this.$refs.analysisDateSelector.doReset()
    this.onSearch()
  }

  onSearch() {
    const body = this.doFilterParams()
    const loading = CommonUtil.Loading()
    AnalysisReportApi.memberPointReport(body).then((res) => {
      if (res.code === 2000) {
        this.detail = res.data || new MemberPointsAnalysisReport()
        console.log('====this.detail', this.detail)
      } else {
        throw new Error(res.msg as any)
      }
    }).catch((error) => {
      this.$message.error(error.message || this.i18n('内部异常'))
    }).finally(() => {
      loading.close()
    })
  }

  // 查询条件
  doFilterParams() {
    const params = new OrgMemberAnalysisReportQuery()
    params.dateUnitEquals = this.filter.dataRange?.type
    if (this.filter.dataRange?.date?.length) {
      params.startDate = this.filter.dataRange.date[0]
      params.endDate = this.filter.dataRange.date[1]
    }
    params.storeIdEquals = this.filter.store
    return params
  }

  doDateChange(value: any) { // {type: 'DAY' | 'WEEK' | 'MONTH', date:['2024-03-05','2024-03-06']}
    this.filter.dataRange = value
  }

  doDownloadDialogClose() {
    this.downloadCenterFlag = false;
  }
};