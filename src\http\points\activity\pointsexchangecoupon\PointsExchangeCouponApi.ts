/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:19
 * @LastEditTime: 2024-09-14 10:09:55
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\points\activity\pointsexchangecoupon\PointsExchangeCouponApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import PointsExchangeCouponActivity from 'model/points/activity/pointsexchangecoupon/PointsExchangeCouponActivity'
import Response from 'model/common/Response'
import BBatchPointsExchangeCouponActivity from 'model/points/activity/pointsexchangecoupon/BBatchPointsExchangeCouponActivity'
import BBatchPointsExchangeCouponActivityResult from 'model/points/activity/pointsexchangecoupon/BBatchPointsExchangeCouponActivityResult'

export default class PointsExchangeCouponApi {
  /**
   * 积分兑换券详情
   * 积分兑换券详情。
   *
   */
  static detail(activityId: string): Promise<Response<PointsExchangeCouponActivity>> {
    return ApiClient.server().get(`/v1/points-exchange-coupon-activity/detail/${activityId}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 积分兑换券修改或保存
   * 积分兑换券修改或保存。
   *
   */
  static saveOrModify(body: PointsExchangeCouponActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/points-exchange-coupon-activity/saveOrModify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 审核积分兑换券活动
   * 审核积分兑换券活动。
   *
   */
  static audit(activityId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/points-activity/audit/${activityId}`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 终止积分兑换券活动
   * 终止积分兑换券活动。
   *
   */
  static stop(activityId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/points-activity/stop/${activityId}`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 删除积分兑换券活动
   * 删除积分兑换券活动。
   *
   */
  static remove(activityId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/points-activity/remove/${activityId}`, {}, {}).then((res) => {
      return res.data
    })
  }
  /**
   *
   *
   * */
  static getShowPrice(): Promise<Response<boolean>> {
    return ApiClient.server().get("/v1/points-exchange-coupon-activity/showPrice",{}).then((res) => {
      return res.data
    })
  }

  /**
  * 批量创建积分兑换券
  * 批量创建积分兑换券
  * 
  */
  static batchSave(body: BBatchPointsExchangeCouponActivity): Promise<Response<BBatchPointsExchangeCouponActivityResult>> {
    return ApiClient.server().post(`/v1/points-exchange-coupon-activity/batch/saveOrModify`, body, {
    }).then((res) => {
      return res.data
    })
  }
}
