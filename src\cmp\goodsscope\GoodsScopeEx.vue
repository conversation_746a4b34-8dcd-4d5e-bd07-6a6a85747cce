<template>
  <div class="goods-scope-ex">
    <el-radio-group :disabled="disabled" v-model="goodsLimit" v-if="!hideTitle" @change="handleChange">
      <el-radio v-if="!isMultipleMC || showAll" label="全部商品" @click.native="handleClick(goodsLimit, '全部商品')">{{formatI18n('/公用/公共组件/商品范围控件/表单/全部商品')}}
      </el-radio>
      <el-radio v-else label="全部商品" @click.native="handleClick(goodsLimit, '全部商品')">{{headquarters === true? formatI18n('/公用/公共组件/商品范围控件/表单/全部商品') : formatI18n('/公用/公共组件/商品范围控件/表单/当前营销中心全部商品')}}
      </el-radio>
      <el-radio label="指定商品适用" v-if="hideUse !== true" @click.native="handleClick(goodsLimit, '指定商品适用')">
        {{formatI18n('/公用/公共组件/商品范围控件/表单/指定商品适用')}}
      </el-radio>
      <el-radio label="指定商品不适用" v-if="hideNoUse !== true" @click.native="handleClick(goodsLimit, '指定商品不适用')">
        {{formatI18n('/公用/公共组件/商品范围控件/表单/指定商品不适用')}}
      </el-radio>
    </el-radio-group>
    <GoodsScope
        :disabled="disabled"
        v-if="goodsLimit !== '全部商品'"
        :importNumber="importNumber"
        :type="type"
        :value="data"
        :appreciationGoods="appreciationGoods"
        :chooseGoodType="chooseGoodType"
        :validateForm="validateForm"
        :innerTitle="innerTitle"
        :goodsLimit="goodsLimit"
        :goodsMatchRuleMode="goodsMatchRuleMode"
        ref="goodsScope"
        @input="submit"/>
  </div>
</template>

<script lang="ts" src="./GoodsScopeEx.ts">
</script>

<style lang="scss" scoped>
  .goods-scope-ex {
    .el-form-item {
      margin-bottom: 0;
    }
  }
</style>
