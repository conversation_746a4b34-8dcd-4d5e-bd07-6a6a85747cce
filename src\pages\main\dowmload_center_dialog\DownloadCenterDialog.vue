<template>
    <el-dialog :title="formatI18n('/公用/预约文件列表', '文件中心')"
               :visible.sync="flag"
               class="download-center"
               :close-on-click-modal="false"
               :destroy-on-close="true"
               :before-close="doBeforeClose"
               :append-to-body="appendToBody"
               >
        <!-- <el-alert
                :closable="false"
                style="margin-top: 10px"
                :title="formatI18n('/公用/预约文件列表', '报表文件生成后系统自动保留7天，过期自动删除')"
                type="warning"
                show-icon>
        </el-alert> -->
        <el-button @click="doRefresh" type="primary">{{formatI18n('/公用/预约文件列表', '刷新')}}</el-button>
        <div class="progress-wrap">
            <span class="progress">{{formatI18n('/公用/预约文件列表', '处理进度')}}:</span>
            <el-select :placeholder="formatI18n('/公用/预约文件列表', '请选择处理进度')" @change="doProgressChange" v-model="progress">
                <el-option :label="formatI18n('/公用/下拉框/提示', '全部')" value=""></el-option>
                <el-option :label="formatI18n('/公用/预约文件列表', '待处理')" value="initial"></el-option>
                <el-option :label="formatI18n('/公用/预约文件列表', '处理中')" value="processing"></el-option>
                <el-option :label="formatI18n('/公用/预约文件列表', '处理成功')" value="success"></el-option>
                <el-option :label="formatI18n('/公用/预约文件列表', '处理失败')" value="fail"></el-option>
            </el-select>
        </div>
        <div>
            <el-table :data="fileArray" border height="400">
                <el-table-column :label="formatI18n('/公用/预约文件列表', '申请时间')" align="center" property="created" width="150">
                    <template slot-scope="scope">
                        <div>{{ scope.row.created | dateFormate3 }}</div>
                    </template>
                </el-table-column>
                <el-table-column :label="formatI18n('/公用/预约文件列表', '类型')" property="module" width="200">
                    <template slot-scope="scope">
                        <div>{{ scope.row.module | downloadModule(scope.row.type) }}</div>
                    </template>
                </el-table-column>
                <el-table-column :label="formatI18n('/公用/预约文件列表', '文件名')" property="downUrl">
                    <template slot-scope="scope">
                        <div :title="scope.row.downUrl">{{ scope.row.downUrl }}</div>
                    </template>
                </el-table-column>
                <el-table-column :label="formatI18n('/公用/预约文件列表', '处理进度')" align="center" property="process">
                    <template slot-scope="scope">
                        <div style="padding-top: 10px" v-if="scope.row.processResult === 'success' && scope.row.state === 'finished'"><i style="font-size: 20px;color: #1cba34" class="el-icon-success"></i></div>
                        <div style="padding-top: 10px" v-if="scope.row.processResult === 'fail' && scope.row.state === 'finished'"><i style="color: #ee1c25;font-size: 20px" class="el-icon-error"></i></div>
                        <div style="padding-top: 10px" v-if="scope.row.state === 'processing'"><el-progress type="circle" :width="30" :stroke-width="1" :percentage="scope.row.process"></el-progress></div>
                    </template>
                </el-table-column>
                <el-table-column :label="formatI18n('/公用/预约文件列表', '操作')" align="center">
                    <template slot-scope="scope">
                        <el-button v-if="scope.row.process == 100 && scope.row.downUrl" type="text"
                                   @click="doDownload(scope.row.downUrl)">{{ formatI18n('/公用/导入', '下载') }}
                        </el-button>
                        <el-button @click="doDelete(scope.row.id)" type="text">{{ formatI18n('/公用/预约文件列表', '删除') }}</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-sizes="[10, 20, 30, 40]"
                :page-size="pageSize"
                layout="total, prev, pager, next, sizes,  jumper"
                :total="pageTotal">
        </el-pagination>
    </el-dialog>
</template>

<script lang="ts" src="./DownloadCenterDialog.ts">
</script>

<style lang="scss">
.download-center{
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 200;
    .progress-wrap{
        margin-bottom: 20px;
        margin-top: 10px;
    }
    .el-progress__text {
      font-size: 11px !important;
    }
    .progress{
        margin-right: 20px;
    }
    .el-alert__icon{
        color: #1dafec;
    }
    .el-alert--info{
        background-color: white;
        margin-top: 10px;
        padding-left: 0px;
    }
    .el-dialog .el-dialog__body{
        padding-left: 30px;
        padding-right: 30px;
    }
    .el-pagination{
        text-align: left;
        margin-top: 10px;
    }
    .el-button--primary{
        position: absolute;
        top: 75px;
        right: 30px;
    }
    /*.el-table{*/
        /*height: 400px;*/
        /*overflow: auto;*/
    /*}*/
    .el-dialog {
        padding: 0;
        height: 620px;
        margin: 0 !important;
        width: 920px;
        .el-dialog__header {
            height: 48px;
            line-height: 48px;
            padding: 0 20px;
            font-size: 16px;
            font-weight: bold;
            border-bottom: 1px solid #eceaea;
        }
        .el-dialog__body {
            height: 580px;
            padding: 0 20px;
        }
        .el-dialog__footer {
            height: 57px;
        }
    }

    .el-dialog__wrapper {
        overflow: hidden !important;
    }
}
</style>