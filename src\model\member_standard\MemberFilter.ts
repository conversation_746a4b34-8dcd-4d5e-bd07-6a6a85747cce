import TagData from "model/member_standard/TagData";

export default class MemberFilter {
    // 身份识别码等于
    identCodeEquals: Nullable<string> = null
    // 姓名类似于
    nameLikes: Nullable<string> = null
    // 等级代码等于
    gradeEquals: Nullable<string> = null
    // 会员状态等于 取值：Using-使用中；Blocked-已冻结；Unactivated-未激活；Canceled-已注销
    stateEquals: Nullable<string> = null
    // 归属门店等于
    ownerStoreIdEquals: Nullable<string> = null
    // 注册渠道等于
    registerChannelEquals: Nullable<string> = null
    // 招募方式等于
    recruitmentMethodEquals: Nullable<string> = null
    // 邀请人会员等于
    refereeCodeEquals: Nullable<string> = null
    // 注册日期大于等于
    registerTimeGreaterOrEquals: Nullable<Date> = null
    // 注册日期小于
    registerTimeLess: Nullable<Date> = null
    // 激活日期大于等于
    activeTimeGreaterOrEquals: Nullable<Date> = null
    // 激活日期小于
    activeTimeLess: Nullable<Date> = null
    // 创建日期大于等于
    createdGreaterOrEquals: Nullable<Date> = null
    // 创建日期小于
    createdLess: Nullable<Date> = null
    // 生日大于等于
    birthMonthDayGreaterOrEquals: Nullable<string> = null
    // 生日小于等于
    birthMonthDayLessOrEquals: Nullable<string> = null
    // 性别等于 取值：男,女
    genderEquals: Nullable<string> = null
    // 邮箱类似于
    mailLikes: Nullable<string> = null
    // 标签
    mustHaveTags: Nullable<TagData[]> = null
    // 客群
    userGroupNameEquals: Nullable<string> = null
    // 页码
    page: Nullable<number> = null
    // 页面大小
    pageSize: Nullable<number> = null
}