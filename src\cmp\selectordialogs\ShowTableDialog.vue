<!--
 * @Author: 黎钰龙
 * @Date: 2023-03-23 17:26:12
 * @LastEditTime: 2023-03-30 16:04:31
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\selectordialogs\ShowTableDialog.vue
 * 记得注释
-->
<template>
  <el-dialog :title="i18n('过期券数明细')" :visible.sync="dialogShow">
    <el-table :data="tableData" height="450">
      <el-table-column :label="i18n('券号')" width="250">
        <template slot-scope="{ row }">
          {{row.code}}
        </template>
      </el-table-column>
      <el-table-column :label="i18n('券名称')" width="250">
        <template slot-scope="{ row }">
          {{row.name}}
        </template>
      </el-table-column>
      <el-table-column :label="i18n('券有效期')">
        <template slot-scope="{ row }">
          {{ row.beginDate | dateFormate3 }} - {{ row.endDate | dateFormate3 }}
        </template>
      </el-table-column>
    </el-table>
    <div class="page" style="margin-top: 15px">
      <el-pagination :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
        @current-change="handleCurrentChange($event)" @size-change="handleSizeChange($event)" background
        layout="total, prev, pager, next, sizes,  jumper">
      </el-pagination>
    </div>
  </el-dialog>
</template>
<script src="./ShowTableDialog.ts">
</script>

<style >
</style>