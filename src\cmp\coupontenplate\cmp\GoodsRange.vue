<template>
    <div class="goods-range">
        <div class="top" v-if="showDesc">{{formatI18n('/公用/公共组件/商品范围控件/标题/选择商品范围')}}<el-button @click="doImport" class="btn" type="text">{{formatI18n('/公用/券模板', '导入')}}</el-button></div>
        <div class="content">
            <el-form :model="dynamicValidateForm" class="demo-dynamic" label-width="100px" ref="dynamicValidateForm">
                <el-form-item
                        :key="index"
                        :prop="'domains.' + index + '.value'"
                        :rules="{required: true, message: formatI18n('/公用/券模板', '请输入必填项'), trigger: 'blur'}"
                        class="cur-goods-range"
                        style="margin-bottom: 15px !important;"
                        v-for="(domain, index) in dynamicValidateForm.domains"
                >
                    <span v-if="index !== 0" style="position: relative;left: -18px;color: rgb(153, 153, 153);">{{formatI18n('/公用/公共组件/商品选择弹框组件/表格/并且')}}</span>
                    <span v-if="index === 0" style="visibility: hidden;position: relative;left: -18px;color: rgb(153, 153, 153);">{{formatI18n('/公用/公共组件/商品选择弹框组件/表格/并且')}}</span>
                    <el-select @change="doTypeChange(index, domain.brand)" class="width-150 cur-input" v-model="domain.brand">
                        <el-option :label="item" :key="index" :value="item" v-for="(item,index) in selectType">{{item}}</el-option>
                    </el-select>
                    <el-select @change="doBelongChange(index, domain.brand, domain.belong)" class="width-150 cur-input" v-model="domain.belong">
                        <el-option :label="formatI18n('/公用/券模板', '属于')" :value="formatI18n('/公用/券模板', '属于')">{{formatI18n('/公用/券模板', '属于')}}</el-option>
                        <el-option :label="formatI18n('/公用/券模板', '不属于')" :value="formatI18n('/公用/券模板', '不属于')">{{formatI18n('/公用/券模板', '不属于')}}</el-option>
                    </el-select>
                    <el-input :placeholder=getPlaceholder(domain.brand)
                              @focus="doFocus(index, domain.brand)"
                              class="width-auto"
                              ref="focusInput"
                              :belong="domain.belong"
                              :type="domain.brand"
                              v-model="domain.value">
                    </el-input>
                    &nbsp;&nbsp;
                    <span style="width: 150px;display: inline-block;text-align: left;">
                            <el-button @click="doClearDomain(index)" type="text">{{formatI18n('/公用/按钮', '清空')}}</el-button>
                        <el-button @click="doDeleteDomain(index)" type="text" v-show="dynamicValidateForm.domains.length > 1">{{formatI18n('/公用/按钮', '删除')}}</el-button>
                        <el-button @click="doAddDomain(index, domain.brand)" type="text" v-show="dynamicValidateForm.domains.length - 1 === index && index !== 2">+&nbsp;{{formatI18n('/营销/积分活动/新建门店积分兑换活动/表格/添加')}}</el-button></span>
                </el-form-item>
            </el-form>
        </div>
        <GoodsSelectorDialog ref="selectGoodsScopeDialog" @summit="doSubmitGoods"/>
        <BrandSelectorDialog ref="selectBrandScopeDialog" @summit="doSubmitBrand"></BrandSelectorDialog>
        <CatogorySelectorDialog ref="selectCatogoryScopeDialog" @summit="doSubmitCatogory"></CatogorySelectorDialog>
        <ImportDialog
                :dialogShow="importDialogShow"
                :importUrl="importUrl"
                :templateName="formatI18n('/公用/券模板/全场现金券/用券商品/指定不可用商品/点击导入', '导入商品范围模板')"
                :templatePath="templatePath"
                :title="formatI18n('/公用/券模板', '导入')"
                @dialogClose="doImportDialogClose" @upload-success="doUploadSuccess">
        </ImportDialog>
        <ImportResultDialog
                :data="importResultData"
                :dialogShow="importResultDialogClose"
                @importResultDialogClose="doImportResultDialogClose">
        </ImportResultDialog>
    </div>
</template>

<script lang="ts" src="./GoodsRange.ts">
</script>

<style lang="scss">
    .goods-range{
        width: 820px;
        .top{
            position: relative;
            height: 32px;
            line-height: 32px;
            background: #ced0da;
            padding-left: 20px;
            .btn{
                position: absolute;
                right: 20px;
                top: 2px;
            }
        }
        .content{
            border: 1px solid #EEEEEE;
            text-align: center;
            padding-top: 10px;
            padding-bottom: 10px;
            .width-150{
                width: 150px !important;
                margin-right: 15px;
            }
            .width-auto{
                width: 250px;
            }
            .clear{
                margin-left: 20px;
                cursor: pointer;
            }
        }
        .el-form-item__content{
            margin-bottom: 0px !important;
        }
        .el-form-item__error{
            margin-left: 365px !important;
        }
        .cur_form{
            .el-form-item__content{
                margin-left: 0 !important;
            }
        }
        .cur-goods-range{
            margin-bottom: 0px !important;
            height: 40px !important;
            .el-form-item__content{
                margin-left: 30px !important;
            }
        }
        .cur-input{
            .el-input__inner{
                border-color: #d7dfeb !important;
            }
        }
    }
</style>