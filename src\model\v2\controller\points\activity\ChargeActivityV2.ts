import ActivityBody from 'model/common/ActivityBody'
import PointsChargeActivityRuleV2 from 'model/v2/controller/points/activity/PointsChargeActivityRuleV2'
import ChannelRange from "model/common/ChannelRange";
import GradesRange from 'model/common/GradeRange';
import DateTimeCondition from "model/common/DateTimeCondition";

export default class ChargeActivityV2 {
    // 提交审核标识: true表示保存， false表示保存并审核
    justSave: Nullable<boolean> = null
    // 活动信息
    activity: Nullable<ActivityBody> = null
    // 规则信息
    rule: Nullable<PointsChargeActivityRuleV2> = null
    //渠道范围
    channelRange: Nullable<ChannelRange> = null
    // 参与会员等级
    gradeRange: Nullable<GradesRange> = null
    // 活动时间限制
	dateTimeCondition = new DateTimeCondition();
}