import ApiClient from "http/ApiClient";
import CardReportSum from "model/prepay/report/card/CardReportSum";
import GiftCardCardHst from "model/prepay/report/card/GiftCardCardHst";
import GiftCardConsumeOrRefundHst from "model/prepay/report/card/GiftCardConsumeOrRefundHst";
import GiftCardFilter from "model/prepay/report/card/GiftCardFilter";
import Response from "model/common/Response";
import GiftCardDailyReportFilter from "model/prepay/report/card/GiftCardDailyReportFilter";
import GiftCardDailyHst from "model/prepay/report/card/GiftCardDailyHst";

export default class GiftCardReportApi {
	/**
	 * 售卡流水汇总
	 *
	 */
	static cardHstSum(body: GiftCardFilter): Promise<Response<CardReportSum>> {
		return ApiClient.server()
			.post(`/v1/prepay/gift/card/report/card/hst/sum`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 消费流水汇总
	 *
	 */
	static consumeHstSum(body: GiftCardFilter): Promise<Response<CardReportSum>> {
		return ApiClient.server()
			.post(`/v1/prepay/gift/card/report/consume/hst/sum`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 消费流水汇总
	 *
	 */
	static consumeRefundHstSum(body: GiftCardFilter): Promise<Response<CardReportSum>> {
		return ApiClient.server()
			.post(`/v1/prepay/gift/card/report/consume/refund/hst/sum`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 售卡流水
	 *
	 */
	static queryCardHst(body: GiftCardFilter): Promise<Response<GiftCardCardHst[]>> {
		return ApiClient.server()
			.post(`/v1/prepay/gift/card/report/card/hst/query`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 退卡流水
	 *
	 */
	static queryRefundCardHst(body: GiftCardFilter): Promise<Response<GiftCardCardHst[]>> {
		return ApiClient.server()
			.post(`/v1/prepay/gift/card/report/refund/card/hst/query`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 消费流水
	 *
	 */
	static queryConsumeHst(body: GiftCardFilter): Promise<Response<GiftCardConsumeOrRefundHst[]>> {
		return ApiClient.server()
			.post(`/v1/prepay/gift/card/report/consume/hst/query`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 消费流水
	 *
	 */
	static queryConsumeRefundHst(body: GiftCardFilter): Promise<Response<GiftCardConsumeOrRefundHst[]>> {
		return ApiClient.server()
			.post(`/v1/prepay/gift/card/report/consume/refund/hst/query`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 储值卡售卡流水报表导出
	 * 储值卡售卡流水报表导出
	 *
	 */
	static exportSalesHst(body: GiftCardFilter): Promise<Response<GiftCardConsumeOrRefundHst[]>> {
		return ApiClient.server()
			.post(`/v1/prepay/gift/card/report/exportSales`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 储值卡消费流水报表导出
	 * 储值卡售卡流水报表导出
	 *
	 */
	static exportConsumeHst(body: GiftCardFilter): Promise<Response<GiftCardConsumeOrRefundHst[]>> {
		return ApiClient.server()
			.post(`/v1/prepay/gift/card/report/exportConsume`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 储值卡退款流水报表导出
	 * 储值卡售卡流水报表导出
	 *
	 */
	static exportRefundHst(body: GiftCardFilter): Promise<Response<GiftCardConsumeOrRefundHst[]>> {
		return ApiClient.server()
			.post(`/v1/prepay/gift/card/report/exportRefund`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 储值卡退卡流水报表导出
	 * 储值卡退卡流水报表导出
	 *
	 */
	static exportRefundCardHst(body: GiftCardFilter): Promise<Response<GiftCardConsumeOrRefundHst[]>> {
		return ApiClient.server()
			.post(`/v1/prepay/gift/card/report/exportRefundCard`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 电子礼品卡日报
	 * 电子礼品卡日报。
	 *
	 */
	static queryCardDailyReport(body: GiftCardDailyReportFilter): Promise<Response<GiftCardDailyHst[]>> {
		return ApiClient.server()
			.post(`/v1/prepay/gift/card/report/card/dailyReport/query`, body, {})
			.then((res) => {
				return res.data;
			});
	}

	/**
	 * 礼品卡日报导出
	 * 礼品卡日报导出。
	 *
	 */
	static exportCardDailyReport(body: GiftCardDailyReportFilter): Promise<Response<void>> {
		return ApiClient.server()
			.post(`/v1/prepay/gift/card/report/card/dailyReport/export`, body, {})
			.then((res) => {
				return res.data;
			});
	}
}
