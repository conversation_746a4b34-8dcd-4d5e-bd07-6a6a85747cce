/*
 * @Author: haiding <EMAIL>
 * @Date: 2025-02-19 10:03:52
 * @LastEditors: 黎钰龙
 * @LastEditTime: 2025-04-28 10:56:32
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\cmp\page-props-components\line-show-qty\LineShowQty.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Vue, Component, Prop } from 'vue-property-decorator';
import emitter from 'util/emitter';
import FormDefUtil from 'util/FormDefUtil';
import I18nPage from 'common/I18nDecorator';

@Component({
  name: 'LineShowQty',
  mixins: [emitter],
  components: {},
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/公用/菜单',
    '/页面/页面管理',
    '/设置/页面管理'
  ],
  auto: true
})
export default class LineShowQty extends Vue {
  @Prop({ type: Boolean, default: false }) readonly: boolean;
  @Prop({ type: String, default: 'LineShowQty' }) validateName: string; // 组件校验名称，editPage通过该名称进行组件校验及校验信息清除
  @Prop({ type: Object }) value: any; // 数据模型
  formKey: any;
  $refs: any;

  rules = {
    propLayoutStyle: [{ required: true, message: this.i18n('请选择'), trigger: ['blur', 'change'] }],
  };

  get formMode() {
    if (this.validateName === 'LineShowQty') {
      return FormDefUtil.getFormMode(this.readonly, this.validateName, this.formKey);
    } else {
      return FormDefUtil.getFormMode(this.readonly, this.validateName + 'LineShowQty', this.formKey);
    }
  }

  created() {
    if (!this.value.propLayoutStyle) {
      this.value.propLayoutStyle = '1'
      this.handleChange()
    }
    if (!this.value.propShowStyle) {
      this.value.propShowStyle = '1'
      this.handleChange()
    }
  }

  handleChange() {
    this.$emit('input', this.value);
    this.$emit('change', this.value);
    this.validate(() => { });
  }

  validate(callback: () => void) {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.validate(callback);
    }
  }

  clearValidate() {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.clearValidate();
    }
  }
};