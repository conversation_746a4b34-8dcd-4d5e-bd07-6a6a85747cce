<!--
 * @Author: 黎钰龙
 * @Date: 2024-11-01 15:41:35
 * @LastEditTime: 2025-05-09 09:54:12
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\prepaycard\prepaycardpay\BatchImportDialog\PrepayCardImportDialog.vue
 * 记得注释
-->
<template>
  <el-dialog append-to-body :close-on-click-modal="false" :title="i18n('导入')" :visible.sync="dialogShow" class="import-dialog-view"
    :close-on-press-escape="false" :show-close="false" width="800px">
    <el-form :model="ruleForm" :rules="rules" ref="form" label-width="130px">
      <el-form-item :label="i18n('/储值/预付卡/预付卡充值单/充值类型') + '：'" prop="rechargeType">
        <el-radio-group v-model="ruleForm.rechargeType">
          <el-radio label="DEPOSIT">{{i18n('充值')}}</el-radio>
          <el-radio label="REFUND">{{i18n('充值退')}}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="(ruleForm.rechargeType === 'DEPOSIT' ? i18n('优惠总金额') : i18n('优惠退总金额')) + '：'" prop="total">
        <AutoFixInput :min="0" :max="9999999.99" :fixed="2" v-model="ruleForm.total" :appendTitle="i18n('元')" style="width: 120px;">
        </AutoFixInput>
      </el-form-item>
      <el-form-item :label="(ruleForm.rechargeType === 'DEPOSIT' ? i18n('应付金额') : i18n('应退金额')) + '：'" prop="payPrice">
        <AutoFixInput :min="0" :max="9999999.99" :fixed="2" v-model="ruleForm.payPrice" :appendTitle="i18n('元')" style="width: 120px;">
        </AutoFixInput>
      </el-form-item>
      <el-form-item :label="i18n('支付方式') + '：'" class="require-item">
        <AddPaymentCmp ref="addPaymentCmp" v-model="ruleForm.payments"></AddPaymentCmp>
      </el-form-item>
      <el-form-item :label="i18n('客户') + '：'" prop="customer">
        <SelectClient v-model="ruleForm.customer" @change="$forceUpdate()" :hideAll="true" width="190px"
          :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
        </SelectClient>
      </el-form-item>
      <el-form-item :label="i18n('发生组织') + '：'" prop="orgId">
        <SelectStores v-model="ruleForm.orgId" @change="$forceUpdate()" :isOnlyId="true" :hideAll="true" width="190px"
          :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
        </SelectStores>
      </el-form-item>
      <el-form-item :label="i18n('/储值/会员储值/储值调整单/详情/说明') + '：'">
        <el-input type="textarea" v-model.trim="ruleForm.remark" :placeholder="i18n('/公用/表单校验/请输入不超过{0}个字符',['200'])" maxlength="200"
          style="width: 350px" :rows="4">
        </el-input>
      </el-form-item>
    </el-form>
    <FormItem :label="i18n('/公用/导入/实例模板') + '：'" label-width="130px">
      <div style="line-height: 36px">
        <a class="span-btn" style="text-decoration: none" class="action-hover_download" @click="downloadTemplate">{{i18n('预付卡充值单模板')}}</a>
      </div>
    </FormItem>
    <FormItem label-width="130px">
      <div class="gray-tips" style="margin-bottom: 6px; word-break: normal">
        {{i18n("/公用/导入/为保障上传成功，建议每次最多上传{0}条信息",['2000'])}}
      </div>
      <el-upload :headers="uploadHeaders" :action="getUploadUrl" :auto-upload="false" :file-list="fileList" ref="upload" :on-error="getErrorInfo"
        :on-success="getSuccessInfo" :with-credentials="true" :data="params" class="upload-demo">
        <el-button size="small" slot="trigger" type="default">{{formatI18n('/公用/导入', '选择文件')}}</el-button>
      </el-upload>
    </FormItem>
    <div class="dialog-footer">
      <el-button @click="doModalClose('cancel')">{{formatI18n('/公用/按钮', '取消')}}</el-button>
      <el-button @click="doModalClose('confirm')" size="small" type="primary">{{formatI18n('/公用/按钮', '确定')}}</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./PrepayCardImportDialog.ts">
</script>

<style lang="scss" scoped>
.import-dialog-view {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  ::v-deep .el-dialog {
    height: 650px !important;
    .el-dialog__body {
      height: 510px!important;
      overflow-y: scroll;
    }
  }
  .dialog-footer {
    display: flex;
    justify-content: end;
    margin-top: 50px;
    padding-bottom: 10px;
  }
}
.action-hover_download {
  cursor: pointer;
  /* 添加手型光标 */
}
</style>