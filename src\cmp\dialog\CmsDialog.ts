import { Vue, Component, Prop } from 'vue-property-decorator';

@Component({
  name: 'NfDialog',
  components: {},
})
export default class NfDialog extends Vue {
  @Prop({ type: String, default: '提示' })
  title: string; // 提示文案
  @Prop({ type: String, default: '确定' })
  confirmText: string; // 确定按钮文案
  @Prop({ type: String, default: 'primary' })
  confirmType: string; // 自定义按钮位置
  @Prop({ type: Boolean, default: true }) showClose: boolean; // 是否显示右上角X
  @Prop({ type: Boolean, default: true })
  isConfirmShow: boolean; // 确定按钮显示
  @Prop({ type: Boolean, default: false })
  isConfirmDisabled: boolean; // 确定按钮是否禁用
  @Prop({ type: String, default: '取消' })
  cancelText: string; // 取消按钮文案
  @Prop({ type: Boolean, default: true })
  isCancelShow: boolean; // 取消按钮显示
  @Prop({ type: Boolean, default: false })
  isDialogShow: boolean; // 显示模态框
  @Prop({ type: String, default: '' })
  width: string; // 模态框宽度
  @Prop({ type: Boolean, default: false })
  doCloseByClickModel: boolean; // 点击周围是否可以关闭弹框
  @Prop({ type: String, default: 'left' })
  otherFloat: string; // 自定义按钮位置

  doBeforeClose() {
    this.$emit('before-close');
    // this.isDialogShow = false;
  }

  doCancel() {
    this.$emit('cancel');
  }

  doConfirm() {
    this.$emit('confirm');
  }
}
