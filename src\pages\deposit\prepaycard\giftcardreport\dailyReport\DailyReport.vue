<template>
  <div class="sales-cards-report">
    <div class="current-page">
      <el-form label-width="150px">
        <el-row class="query">
          <TimeRange no-i18n @submit="handleTimeRange" ref="timeRange"></TimeRange>
        </el-row>
        <el-row class="query" style="margin-top:8px">
          <el-col :span="8">
            <form-item label="卡模板号">
              <el-input placeholder="等于" v-model="query.templateNumberEquals" />
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item label="卡模板">
              <el-input placeholder="等于" v-model="query.templateNameEquals" />
            </form-item>
          </el-col>
        </el-row>
        <el-row class="query" style="margin-top:8px">
          <el-col :span="16">
            <form-item label=" ">
              <el-button class="btn-search" type="primary" @click="doSearch">查询</el-button>
              <el-button class="btn-reset" @click="doReset">重置</el-button>
            </form-item>
          </el-col>
        </el-row>
      </el-form>
      <hr />
      <el-row class="table">
        <el-table :data="queryData" border @header-dragend="tableDragend" style="width: 100%;margin-top: 20px">
          <div slot="empty">
            <p>暂无数据</p>
          </div>
          <el-table-column label="卡模板号" prop="cardTemplateNumber" :width="getColumnWidth('cardTemplateNumber', 150)">
            <template slot-scope="scope">
              <span :title="scope.row.templateNumber">{{ scope.row.templateNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column label="卡模板" prop="templateName" :width="getColumnWidth('templateName', 150)"> </el-table-column>
          <el-table-column label="期初余额" prop="beginningBalance" :width="getColumnWidth('beginningBalance', 150)"> </el-table-column>
          <el-table-column label="期初实充" prop="beginningAmount" :width="getColumnWidth('beginningAmount', 180)"> </el-table-column>
          <el-table-column label="期初返现" prop="beginningGiftAmount" :width="getColumnWidth('beginningGiftAmount', 150)"> </el-table-column>
          <el-table-column label="期末余额" prop="endingBalance" :width="getColumnWidth('endingBalance', 200)"> </el-table-column>
          <el-table-column label="期末实充" prop="endingAmount" :width="getColumnWidth('endingAmount', 150)"> </el-table-column>
          <el-table-column label="期末返现" prop="endingGiftAmount" :width="getColumnWidth('endingGiftAmount', 200)"> </el-table-column>
          <el-table-column label="差异" prop="different" :width="getColumnWidth('different', 150)"></el-table-column>
          <el-table-column label="售卡" prop="sale" :width="getColumnWidth('sale', 150)"></el-table-column>
          <el-table-column label="售卡实充" prop="saleAmount" :width="getColumnWidth('saleAmount', 150)"></el-table-column>
          <el-table-column label="售卡返现" prop="saleGiftAmount" :width="getColumnWidth('saleGiftAmount', 150)"></el-table-column>
          <el-table-column label="消费" prop="pay" :width="getColumnWidth('pay', 150)"></el-table-column>
          <el-table-column label="消费实充" prop="payAmount" :width="getColumnWidth('payAmount', 150)"></el-table-column>
          <el-table-column label="消费返现" prop="payGiftAmount" :width="getColumnWidth('payGiftAmount', 150)"></el-table-column>
          <el-table-column label="退款" prop="payRefund" :width="getColumnWidth('payRefund', 150)"></el-table-column>
          <el-table-column label="退款实充" prop="payRefundAmount" :width="getColumnWidth('payRefundAmount', 150)"></el-table-column>
          <el-table-column label="退款返现" prop="payRefundGiftAmount" :width="getColumnWidth('payRefundGiftAmount', 150)"></el-table-column>
          <el-table-column label="作废" prop="cancel" :width="getColumnWidth('cancel', 150)"></el-table-column>
          <el-table-column label="作废实充" prop="cancelAmount" :width="getColumnWidth('cancelAmount', 150)"></el-table-column>
          <el-table-column label="作废返现" prop="cancelGiftAmount" :width="getColumnWidth('cancelGiftAmount', 150)"></el-table-column>
          <el-table-column label="退卡" prop="saleRefund" :width="getColumnWidth('saleRefund', 150)"></el-table-column>
          <el-table-column label="退卡实充" prop="saleRefundAmount" :width="getColumnWidth('saleRefundAmount', 200)"></el-table-column>
          <el-table-column label="退卡返现" prop="saleRefundGiftAmount" :width="getColumnWidth('saleRefundGiftAmount', 150)"></el-table-column>
          <el-table-column label="调整" prop="adjust" :width="getColumnWidth('adjust', 150)"></el-table-column>
          <el-table-column label="调整实充" prop="adjustAmount" :width="getColumnWidth('adjustAmount', 150)"></el-table-column>
          <el-table-column label="调整返现" prop="adjustGiftAmount" :width="getColumnWidth('adjustGiftAmount', 150)"></el-table-column>
          <!-- 新增字段 -->
          <el-table-column label="未激活余额" prop="inactiveTotalBalance" :width="getColumnWidth('inactiveTotalBalance', 150)"></el-table-column>
          <el-table-column label="未激活实充" prop="inactiveBalance" :width="getColumnWidth('inactiveBalance', 150)"></el-table-column>
          <el-table-column label="未激活赠送" prop="inactiveGiftBalance" :width="getColumnWidth('inactiveGiftBalance', 150)"></el-table-column>
          <el-table-column label="转赠中余额" prop="presentingTotalBalance" :width="getColumnWidth('presentingTotalBalance', 150)"></el-table-column>
          <el-table-column label="转赠中实充" prop="presentingBalance" :width="getColumnWidth('presentingBalance', 150)"></el-table-column>
          <el-table-column label="转赠中赠送" prop="presentingGiftBalance" :width="getColumnWidth('presentingGiftBalance', 150)"></el-table-column>
          <el-table-column label="使用中余额" prop="usingTotalBalance" :width="getColumnWidth('usingTotalBalance', 150)"></el-table-column>
          <el-table-column label="使用中实充" prop="usingBalance" :width="getColumnWidth('usingBalance', 150)"></el-table-column>
          <el-table-column label="使用中赠送" prop="usingGiftBalance" :width="getColumnWidth('usingGiftBalance', 150)"></el-table-column>
          <el-table-column label="已冻结余额" prop="frozenTotalBalance" :width="getColumnWidth('frozenTotalBalance', 150)"></el-table-column>
          <el-table-column label="已冻结实充" prop="frozenBalance" :width="getColumnWidth('frozenBalance', 150)"></el-table-column>
          <el-table-column label="已冻结赠送" prop="frozenGiftBalance" :width="getColumnWidth('frozenGiftBalance', 150)"></el-table-column>
        </el-table>
      </el-row>
      <el-pagination no-i18n :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
        @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background :layout="getPageLayout('total, prev, pager, next, sizes, jumper',page.probeEnabled)"
        class="pagin"></el-pagination>
    </div>
  </div>
</template>

<script lang="ts" src="./DailyReport.ts"></script>

<style lang="scss">
.sales-cards-report {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: auto;
  .total {
    margin: 20px;
  }

  .current-page {
    height: calc(100% - 150px);
    padding: 0 20px 20px 20px;
    .el-select {
      width: 100%;
    }

    .query {
      .el-form-item {
        margin-bottom: 0;
      }
    }

    .pagin {
      margin-top: 25px;
    }

    .el-col {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .cell {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .el-form-item__label {
      //overflow: hidden;
      //text-overflow: ellipsis;
      white-space: normal;
    }
  }
}
</style>
