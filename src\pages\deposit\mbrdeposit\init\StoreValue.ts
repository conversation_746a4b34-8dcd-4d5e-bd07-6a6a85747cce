/*
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:03
 * @LastEditTime: 2024-05-08 15:24:58
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\mbrdeposit\init\StoreValue.ts
 * 记得注释
 */
import {Component, Vue} from 'vue-property-decorator'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import PrePayConfigApi from 'http/prepay/config/PrePayConfigApi'
import ConstantMgr from 'mgr/ConstantMgr'
import FormItem from 'cmp/formitem/FormItem.vue'
import I18nPage from "common/I18nDecorator";
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import FaceAmount from 'model/prepay/config/FaceAmount'
import xss from "xss";

@Component({
  name: 'StoreValue',
  components: {
    SubHeader,
    FormItem,
    BreadCrume
  }
})
@I18nPage({
  prefix: ['/储值/会员储值/充值面额设置/详情页面', '/储值/会员储值/充值面额设置/编辑页面']
})
export default class StoreValue extends Vue {
  prePermission = false
  panelArray: any = []
  detail: FaceAmount = new FaceAmount()
  xss: Function = xss

  created() {
    this.panelArray = [
      {
        name: this.formatI18n('/公用/菜单/充值面额设置'),
        url: ''
      }
    ]
    this.getPrePermission()
    this.getDtl()
  }

  doEdit() {
    this.$router.push({name: 'store-value-edit', query: {from: 'edit'}})
  }

  doAdd() {
    this.$router.push({name: 'store-value-edit', query: {from: 'add'}})
  }

  private getPrePermission() {
    const loading = this.$loading(ConstantMgr.loadingOption)
    PrePayConfigApi.get().then((resp: any) => {
      if (resp && resp.data) {
        loading.close()
        this.prePermission = resp.data.enableDepositFromCard
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
      loading.close()
    })
  }

  private getDtl() {
    const loading = this.$loading(ConstantMgr.loadingOption)
    PrePayConfigApi.getConfig().then((resp: any) => {
      if (resp.code === 2000 && resp.data) {
        loading.close()
        this.detail = resp.data
      }
    }).catch((error) => {
      loading.close()
      this.$message.error(error.message)
    })
  }
}
