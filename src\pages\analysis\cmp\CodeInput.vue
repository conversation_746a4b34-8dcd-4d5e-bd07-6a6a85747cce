<template>
  <div class="code-input-view">
    <fieldset :style="{'background': disabled ? '#f0f2f5' : 'white'}" class="input-wrap">
      <el-tag
              :key=index
              @close="onClose(index)"
              class="margin-right"
              closable
              size="small"
              type="info"
              v-for="(item, index) in bindArray"
              v-if="!disabled">
        {{item}}
      </el-tag>
      <el-tag
              :key=index
              @close="onClose(index)"
              class="margin-right"
              size="small"
              type="info"
              v-for="(item, index) in bindArray"
              v-if="disabled">
        {{item}}
      </el-tag>
      <div class="icon-position" v-if="!disabled">
        <i class="iconfont ic-plus" style="position: absolute;left: 3px;top: 6px;" v-if="isShowIcon && bindArray.length < 20"></i>
        <input :style="{'padding-left' : isShowIcon ? '20px' : '5px'}"
               @blur="onBlur"
               @focus="onFocus"
               @keydown.enter="onAdd"
               maxlength="128"
               :placeholder="formatI18n('/分析/自定义标签/自定义标签/修改/标签编辑/请输入标签值并回车')"
               style="width:280px"
               v-if="bindArray.length < 20"
               v-model="code"/>
      </div>
    </fieldset>
    <el-tip closable v-if="isShowTip">
      <div class="tip">条码{{codeString}}与其它商品条码重复</div>
    </el-tip>
  </div>
</template>
<script lang="ts" src='./CodeInput.ts'></script>
<style lang="scss">
  .code-input-view {
    width: 100%;
    height: auto;
    display: inline-block;
    .input-wrap {
      min-height: 36px;
      box-sizing: border-box;
      border-radius: 4px;
      border: 1px solid #dcdfe6;
      background-color: #fff;
      padding: 0 12px;
      width: 100%;
      display: inline-block;
      .icon-position {
        position: relative;
        display: inline-block;
        margin: 5px;
        .add-icon {
          position: absolute;
          left: 5px;
        }
        input {
          padding-left: 20px;
          width: 125px;
          height: 28px;
          border-radius: 5px;
          border: 1px solid #ccc;
        }
        input::-webkit-input-placeholder{
          color:#979cb3;
        }
      }
      .margin-right {
        margin: 5px;
      }
    }
    .qf-tip-info {
      padding: 0 11px;
      margin-top: 5px;
    }
  }
</style>
