/*
 * @Author: 黎钰龙
 * @Date: 2023-12-01 13:44:04
 * @LastEditTime: 2024-01-17 10:40:56
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\datum\store\StoreDtl.ts
 * 记得注释
 */
import { Component, Vue } from "vue-property-decorator";
import FormItem from "cmp/formitem/FormItem.vue";
import BreadCrume from "cmp/bread-crumb/BreadCrume";
import { State } from "vuex-class";
import I18nPage from "common/I18nDecorator";
import UserLoginResult from "model/login/UserLoginResult";
import OrgApi from "http/org/OrgApi";
import { OrgState, OrgStateMap } from 'model/common/OrgState';

@Component({
  name: "Store",
  components: {
    FormItem,
    BreadCrume
  },
})
@I18nPage({
  auto: false,
  prefix: [
    '/资料/渠道',
    '/公用/按钮',
  ],
})
export default class StoreDtl extends Vue {
  i18n: I18nFunc
  @State("loginInfo")
  loginInfo: UserLoginResult;
  panelArray: any = [];
  orgId: string = ''
  shopDetail: any = {}
  enableMultiMarketingCenter: boolean = true

  OrgStateMap = OrgStateMap

  handelOrgState(state: OrgState) {
    const mapObj: {
      [key: string]: string
    } = {
      [OrgState.enable]: 'enable',
      [OrgState.disable]: 'disable'
    }
    return mapObj[state]
  }

  get platOrgInfos() {
    return this.shopDetail.platOrgInfos || []
  }
  created() {
    this.panelArray = [
      {
        name: this.formatI18n("/公用/菜单/门店"),
        url: "store",
      },
      {
        name: this.formatI18n('/资料/门店/门店详情'),
        url: "",
      }
    ];
    this.orgId = this.$route.query.orgId as string
    this.enableMultiMarketingCenter = this.$route.query.enableMultiMarketingCenter == '1' ? true : false
    if (this.orgId) {
      this.query()
    }
  }
  buildStoreAddress(address: any) {
    let str = '';
    str = str + this.nullToEmpty(address.country) + this.nullToEmpty(address.province) +
      this.nullToEmpty(address.city) + this.nullToEmpty(address.district) + this.nullToEmpty(address.street) +
      this.nullToEmpty(address.address);
    if (str.length == 0) {
      str = '-'
    }
    return str
  }
  nullToEmpty(str: any) {
    return str == null ? "" : str;
  }
  buildStoreCoordinate(lat: number, lng: number) {
    let str = '-';
    if (lat && lng) {
      str = this.formatI18n("/资料/门店/经度：") + lng + ' ' + this.formatI18n("/资料/门店/纬度：") + lat;
    }
    return str;
  }
  async query() {
    try {
      const { data } = await OrgApi.detail(this.orgId)
      this.shopDetail = data
    } catch (error:any) {
      if (error && error.message) {
        this.$message.error(error.message);
      }
    }
  }
}
