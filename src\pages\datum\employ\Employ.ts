import {Component, Vue} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import ListWrapper from 'cmp/list/ListWrapper.vue'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import RSEmployee from 'model/common/RSEmployee';
import RSEmployeeFilter from 'model/common/RSEmployeeFilter';
import Employee<PERSON>pi from 'http/employee/EmployeeApi';
import FloatBlock from 'cmp/floatblock/FloatBlock.vue'
import BreadCrume from "cmp/bread-crumb/BreadCrume";

@Component({
  name: 'Employ',
  components: {
    FormItem,
    ListWrapper,
    SubHeader,
    FloatBlock,
    BreadCrume
  }
})
export default class Employ extends Vue {
  query: RSEmployeeFilter = new RSEmployeeFilter()
  queryData: RSEmployee[] = []
  tableHeight: number = 0
  panelArray: any = []
  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }

    created() {
      this.panelArray = [
        {
          name: this.formatI18n('/公用/菜单/员工'),
          url: ''
        },
      ]
      this.getList()
    }

    doSearch() {
      this.page.currentPage = 1
      this.getList()
    }

    doReset() {
        this.query = new RSEmployeeFilter()
        this.page.currentPage = 1
        this.getList()
    }

    /**
     * 分页页码改变的回调
     * @param val
     */
    onHandleCurrentChange(val: number) {
        this.page.currentPage = val
        this.getList()
    }

    /**
     * 每页多少条的回调
     * @param val
     */
    onHandleSizeChange(val: number) {

        this.page.size = val
        this.getList()
    }

    /**
     * 表格排序条件
     */
    onSortChange({column, prop, order}: any) {
        // todo
    }

    private getList() {
        this.query.page = this.page.currentPage - 1
        this.query.pageSize = this.page.size
        EmployeeApi.query(this.query).then((resp: any) => {
            if (resp && resp.code === 2000) {
                this.queryData = resp.data
                this.page.total = resp.total
            }
        }).catch((error) => {
          if (error && error.message) {
            this.$message.error(error.message)
          }
        })
    }
}
