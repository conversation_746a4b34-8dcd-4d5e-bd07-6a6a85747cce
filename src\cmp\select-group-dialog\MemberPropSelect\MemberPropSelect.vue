<!--
 * @Author: 黎钰龙
 * @Date: 2025-01-06 20:02:16
 * @LastEditTime: 2025-05-09 14:29:56
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\select-group-dialog\MemberPropSelect\MemberPropSelect.vue
 * 记得注释
-->
<template>
  <div class="select-group-member-prop">
    <div style="font-size: 14px; font-weight: 500;">
      <div style="background-color: #efefef;padding-left: 12px;">{{ i18n('会员属性满足') }}</div>
    </div>
    <el-form ref="form" style="margin-top: 15px;display: flex" :model="data" v-if="data">
      <div>
        <div style="display: flex;width: 645px;margin-bottom: 10px" :key="index" v-for="(prop, index) of data.props">
          <span style="width: 40px; display: inline-block;color: #969799;padding-right: 8px;flex-shrink: 0;">
            {{ index > 0 ? i18n('/营销/券礼包活动/新建商品满额发券活动详情界面/发券商品/并且') : '' }}
          </span>
          <el-select v-model="data.props[index].prop" style="width: 120px;flex-shrink: 0;" @change="handlePropChange(index)">
            <el-option-group :label="i18n('基本属性')">
              <el-option :value="key" :label="value" :key="value" v-for="(value, key) of memberBasePropMap"
                :disabled="existsProps && existsProps.indexOf(key) > -1">{{ value }}
              </el-option>
            </el-option-group>
            <el-option-group :label="i18n('资料信息')">
              <el-option :value="key" :key="value" :label="value" v-for="(value, key) of memberInfoPropMap"
                :disabled="existsProps && existsProps.indexOf(key) > -1">{{ value }}
              </el-option>
            </el-option-group>
          </el-select>
          <div class="select-right">
            <div style="display: flex;width: calc(100% - 40px);">
              <MemberGradeSelector ref="selectors" theme="SysCustomTag" v-if="prop.prop === 'grade'" v-model="data.props[index]" @input="submit" />
              <MemberStateSelector ref="selectors" theme="SysCustomTag" v-if="prop.prop === 'state'" v-model="data.props[index]" @input="submit" />
              <MemberRegisterSceneSelector ref="selectors" theme="SysCustomTag" v-if="prop.prop === 'registerScene'" v-model="data.props[index]"
                @input="submit" />
              <MemberGenderSelector class="select-right-input " ref="selectors" theme="SysCustomTag" v-if="prop.prop === 'gender'"
                v-model="data.props[index]" @input="submit" />
              <TimeRangeSelector ref="selectors" theme="SysCustomTag" v-if="prop.prop === 'registerTime'" class="member-select-date"
                v-model="data.props[index]" @input="submit" />
              <TimeRangeSelector ref="selectors" theme="SysCustomTag" class="member-select-date" v-if="prop.prop === 'firstConsumeTime'"
                v-model="data.props[index]" @input="submit" />
              <MemberBirthdaySelector class="select-right-input " ref="selectors" theme="SysCustomTag" v-if="prop.prop === 'birthday'"
                v-model="data.props[index]" @input="submit" />
              <MemberStoreScopeSelector style="width: 435px;" class="select-right-input " ref="selectors" theme="SysCustomTag"
                v-if="prop.prop === 'store'" :operator="data.props[index].operator" :value="data.props[index].storeProp"
                @input="handleStoreScopeSubmit(index, $event)" />
              <MemberRegisterChannelSelector ref="selectors" theme="SysCustomTag" v-if="prop.prop === 'registerChannelType'"
                v-model="data.props[index]" @input="submit" />
              <PaidMemberCardSelector v-if="prop.prop === 'benefitCardTemplateCodes'" type="all" ref="selectors" v-model="data.props[index]"
                @input="submit">
              </PaidMemberCardSelector>
            </div>
            <div style="width: 50px;text-align: center">
              <el-button type="text" @click="handlePropChange(index)">{{ i18n('/公用/按钮/清空') }}</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-form>
    <el-button v-if="addable" :loading="loading" style="float: right;margin-right: 10px;" size="small" type="text" @click="handleAdd">+
      {{ i18n('添加') }}
    </el-button>
  </div>
</template>

<script lang="ts" src="./MemberPropSelect.ts">
</script>

<style lang="scss">
.select-group-member-prop {
  // background-color: #efefef;
  // padding: 15px;
  width: 645px;

  .select-right {
    display: flex;
    align-items: start;
    margin-left: 10px;
    width: calc(100% - 160px);
    flex-shrink: 0;

    .select-right-input {
      input.el-input__inner {
        height: 32px !important;
        line-height: 32px !important;
      }
    }

    .member-select-date {
      .el-date-editor {
        width: 245px !important;
      }
    }
  }
}
</style>