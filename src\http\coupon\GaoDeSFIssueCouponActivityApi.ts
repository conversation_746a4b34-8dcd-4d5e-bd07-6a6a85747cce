import ApiClient from 'http/ApiClient'
import BGaoDeSFIssueCouponActivity from 'model/default/BGaoDeSFIssueCouponActivity'
import BGaoDeSFIssueCouponActivitySummary from 'model/default/BGaoDeSFIssueCouponActivitySummary'
import Response from 'model/default/Response'
import UnionActivityQuery from 'model/promotion/UnionActivityQuery'

export default class GaoDeSFIssueCouponActivityApi {
  /**
   * 作废高德三方发券活动
   * 作废高德三方发券活动。
   * 
   */
  static abort(activityId: string): Promise<Response<void>> {
    return ApiClient.server().get(`/v1/gaodesf/issue-coupon-activity/abort/${activityId}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除高德三方发券活动
   * 删除高德三方发券活动。
   * 
   */
  static remove(activityId: string): Promise<Response<void>> {
    return ApiClient.server().get(`/v1/gaodesf/issue-coupon-activity/remove/${activityId}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 审核高德三方发券活动
   * 审核高德三方发券活动。
   * 
   */
  static audit(activityId: string): Promise<Response<void>> {
    return ApiClient.server().get(`/v1/gaodesf/issue-coupon-activity/audit/${activityId}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量审核活动
   * 批量审核活动。
   * 
   */
  static batchAudit(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/gaodesf/issue-coupon-activity/batch/audit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量删除活动
   * 批量删除活动。
   * 
   */
  static batchRemove(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/gaodesf/issue-coupon-activity/batch/remove`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量终止活动
   * 批量终止活动。
   * 
   */
  static batchStop(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/gaodesf/issue-coupon-activity/batch/stop`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 高德三方发券活动详情
   * 高德三方发券活动详情。
   * 
   */
  static getDtl(id: string): Promise<Response<BGaoDeSFIssueCouponActivity>> {
    return ApiClient.server().get(`/v1/gaodesf/issue-coupon-activity/getGaoDeSFIssueCouponActivity/${id}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 高德三方发券活动分页查询
   * 高德三方发券活动分页查询。
   * 
   */
  static query(body: UnionActivityQuery): Promise<Response<BGaoDeSFIssueCouponActivitySummary>> {
    return ApiClient.server().post(`/v1/gaodesf/issue-coupon-activity/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 恢复高德三方发券活动
   * 恢复高德三方发券活动。
   * 
   */
  static recovery(activityId: string): Promise<Response<void>> {
    return ApiClient.server().get(`/v1/gaodesf/issue-coupon-activity/recovery/${activityId}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建或修改高德三方发券活动
   * 新建或修改高德三方发券活动
   * 
   */
  static saveGaoDeSFIssueCouponActivity(body: BGaoDeSFIssueCouponActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/gaodesf/issue-coupon-activity/saveGaoDeSFIssueCouponActivity`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 终止高德三方发券活动
   * 停止高德三方发券活动。
   * 
   */
  static stop(activityId: string): Promise<Response<void>> {
    return ApiClient.server().get(`/v1/gaodesf/issue-coupon-activity/stop/${activityId}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 暂停高德三方发券活动
   * 暂停高德三方发券活动。
   * 
   */
  static suspend(activityId: string): Promise<Response<void>> {
    return ApiClient.server().get(`/v1/gaodesf/issue-coupon-activity/suspend/${activityId}`, {
    }).then((res) => {
      return res.data
    })
  }

}
