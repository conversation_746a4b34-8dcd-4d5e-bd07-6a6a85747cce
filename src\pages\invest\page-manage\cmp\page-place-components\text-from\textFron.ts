import { Vue, Component, Prop } from 'vue-property-decorator';
import PlaceTemplateMixins from '../PlaceTemplateMixins';
import I18nPage from 'common/I18nDecorator';


@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理',
  ],
  auto: true
})
@Component({
  name: 'TextFrom',
  components: {},
  mixins: [PlaceTemplateMixins],
})
export default class TextFrom extends Vue {
  @Prop()
  componentItem: any;
  mounted() {
    console.log(this.componentItem, 'componentItem');
  }
  get bgUrl() {
    return   'delivery/home_page_top_new.png';
  }
  get localProperty() {
    return this.componentItem.props;
  }
}
