/*
 * @Author: 黎钰龙
 * @Date: 2024-11-21 19:31:52
 * @LastEditTime: 2024-11-28 16:13:24
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\autoTag\EventBehaviorCondition.ts
 * 记得注释
 */
import BooleanPropValues from "./BooleanPropValues"
import NumberPropValues from "./NumberPropValues"
import { OperatorType } from "./OperatorType"
import { PropType } from "./PropType"
import StringPropValues from "./StringPropValues"
import TimePropValues from "./TimePropValues"

export default class EventBehaviorCondition {
  // 行为属性
  prop: Nullable<string> = null
  // 属性类型
  propType: Nullable<PropType> = null
  // 运算符
  operator: Nullable<OperatorType> = null
  // 字符串属性值
  stringPropValue: Nullable<StringPropValues> = null
  // 布尔属性值
  boolPropValues: Nullable<BooleanPropValues> = null
  // 数值属性值
  numberPropValues: Nullable<NumberPropValues> = null
  // 时间属性值
  timePropValues: Nullable<TimePropValues> = null
}