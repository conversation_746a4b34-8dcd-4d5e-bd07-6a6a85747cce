import {Component, Prop, Vue} from 'vue-property-decorator'
import StoreRange from 'model/common/StoreRange'
import CheckStoreDialog from 'pages/deposit/mbrdeposit/active/dialog/CheckStoreDialog.vue'
import IdName from 'model/entity/IdName'

@Component({
  name: 'GoodsDtl',
  components: {
    CheckStoreDialog
  }
})
export default class GoodsDtl extends Vue {
  @Prop()
  data: StoreRange
  getMarketIdName(arr: IdName[]) {
    let strArray: string[] = []
    if (arr && arr.length > 0) {
        arr.forEach((item: IdName) => {
          if (item) {
            let str = ''
            str += `[${item.id}]${item.name};`
            strArray.push(str)
          }
        })
    }
    return strArray
  }
  getStoreLength(length: number, storeRangeType: string) {
    if (storeRangeType === 'PART') {
      let str: any = this.formatI18n('/权益/积分/积分初始化/已初始化状态/得积分规则/基础得积分/适用门店/已选{0}家门店适用')
      str = str.replace(/\{0\}/g, length)
      return str
    } else {
      let str: any = this.formatI18n('/权益/积分/积分初始化/已初始化状态/得积分规则/基础得积分/适用门店/已选{0}家门店不适用')
      str = str.replace(/\{0\}/g, length)
      return str
    }
  }
  getStoreUse(type: string) {
    if (type === 'PART') {
      return this.formatI18n('/权益/积分/积分初始化/未初始化状态/得积分规则/基础得积分/点击立即设置/指定门店适用')
    } else {
      return this.formatI18n('/权益/积分/积分初始化/未初始化状态/得积分规则/基础得积分/点击立即设置/指定门店不适用')
    }
  }
}