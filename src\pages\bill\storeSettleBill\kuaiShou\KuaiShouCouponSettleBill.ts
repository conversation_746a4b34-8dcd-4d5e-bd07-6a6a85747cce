import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import FormItem from 'cmp/formitem/FormItem';
import MyQueryCmp from 'cmp/querycondition/MyQueryCmp';
import I18nPage from 'common/I18nDecorator';
import {Component, Vue} from "vue-property-decorator";
import IdName from "model/common/IdName";
import SelectStores from 'cmp/selectStores/SelectStores';

import ListWrapper from "cmp/list/ListWrapper";
import KuaiShouCouponSettleBillFilter
    from "../../../../model/bill/storeSettleBill/kuaiShou/KuaiShouCouponSettleBillFilter";
import KuaiShouSettleBill from "model/bill/storeSettleBill/kuaiShou/KuaiShouSettleBill";
import DateUtil from "util/DateUtil";
import KuaiShouCouponSettleBillApi
    from "http/bill/storeSettleBill/kuaiShou/KuaiShouCouponSettleBillApi";
import DownloadCenterDialog from "pages/main/dowmload_center_dialog/DownloadCenterDialog";
import OrgApi from "http/org/OrgApi";
@Component({
    name: 'KuaiShouCouponSettleBill',
    components: {
        BreadCrume,
        MyQueryCmp,
        FormItem,
        SelectStores,
        ListWrapper,
        DownloadCenterDialog,
    }
})
@I18nPage({
    prefix: [
        '/公用/券模板',
        '/公用/查询条件/提示',
        '/营销/券礼包活动/券礼包活动',
        '/公用/过滤器',
        '/公用/下拉框/提示',
        '/储值/预付卡/卡模板/编辑页面',
        '/公用/券核销',
        '/储值/预付卡/卡模板/列表页面',
        '/公用/按钮',
        '/公用/活动/提示信息',
        '/数据/快手券账单',
        '/数据/平台券账单'
    ],
    auto: true
})

export default class KuaiShouCouponSettleBill extends Vue {
    $refs: any
    panelArray: any = []
    occurredOrg: Nullable<IdName> = null
    billDate: any = []
    query: KuaiShouCouponSettleBillFilter = new KuaiShouCouponSettleBillFilter()
    tableData: KuaiShouSettleBill[] = []
    fileDialogVisible: boolean = false
    showTip: boolean = false
    page: any = {
        pageSize: 10,
        page: 1,
        total: 0,
        probeEnabled: null
    }
    platformIn: any = null
    platformList: any = [] // 平台

    created() {
        this.panelArray = [
            {
                name: this.i18n("平台券账单"),
                url: "",
            },
        ]
        this.getPlatformList()
    }
    // 获取快手券账单信息
    getList() {
        const params = this.getParam()
        if (!params.platformIn) {
            this.$message.error(this.i18n('请选择平台'))
            return
        }
        KuaiShouCouponSettleBillApi.query(params).then(res => {
            if (res.code === 2000) {
                this.tableData = res.data || []
                this.page.total = res.total
                this.page.probeEnabled = res.fields ? res.fields.probeEnabled : null
                console.log('probeEnabled',this.page.probeEnabled)
            } else {
                this.$message.error(res.msg as string)
            }
        }).catch((error) => {
            this.$message.error(error.message)
        })
    }

    // 导出明细行
    doExport() {
        const params = this.getParam()
        params.page = 0
        KuaiShouCouponSettleBillApi.export(params).then((res) => {
            if (res.code === 2000) {
                this.exportAfter()
            } else {
                this.$message.error(this.i18n(res.msg!) || this.i18n('导出失败'))
            }
        }).catch((error) => {
            this.$message.error(this.i18n(error.message) || this.i18n('导出失败'))
        })
    }

    getParam() {
        const params = new KuaiShouCouponSettleBillFilter()
        if (this.billDate != null) {
            if (this.billDate[0]) {
                params.settleTimeGreaterOrEquals = (this.billDate[0] + " 00:00:00") as any
            }
            if (this.billDate[1]) {
                params.settleTimeLessOrEquals = (this.billDate[1]  + " 23:59:59") as any
            }
        }
        params.page = this.page.page - 1
        params.pageSize = this.page.pageSize
        if (this.occurredOrg) {
            params.occurredOrgIdEquals = this.occurredOrg.id || null
        }
        if (this.platformIn) {
            params.platformIn = [this.platformIn]
        } else {
            if (this.platformList.length > 0) {
                this.platformIn = this.platformList[0].value
                params.platformIn = [this.platformIn]
            }
        }

        return params
    }

    onReset() {
        this.page.page = 1
        this.page.pageSize = 10
        this.occurredOrg = new IdName()
        this.billDate = []
        this.getList()
    }

    onSearch() {
        this.page.page = 1
        this.getList()
    }

    onHandleCurrentChange(val: number) {
        this.page.page = val
        this.getList()
    }

    onHandleSizeChange(val: number) {
        this.page.pageSize = val
        this.getList()
    }

    showIdName(id: String, name: String) {
        if (!id && !name) {
            return '--'
        }
        let orgId = id ? id : '-'
        let orgName = name ? name : '-'
        return `[${orgId}] ${orgName}`
    }
// 场景类型：1：核销结算、2：支付结算、3：结算后退款、4：订单校准、5：平台赔付、6：重复核销
    getCategory(item: KuaiShouSettleBill) {
        if (!item.category) {
            return '--'
        }
        if(item.platform === 'KuaiShou') {
            switch (item.category) {
                case '1':
                    return '核销结算'
                case '2':
                    return '支付结算'
                case '3':
                    return '结算后退款'
                case '4':
                    return '订单校准'
                case '5':
                    return '平台赔付'
                case '6':
                    return '重复核销'
                default:
                    return '--'
            }
        } else if (item.platform === 'DouYin') {
            switch (item.category) {
                case "1":
                    return "正向冲单";
                case "2":
                    return "退款单";
                case "3":
                    return "退款手续费";
                default:
                    return "--";
            }
        } else {
            return item.category
        }
    }

    exportAfter() {
        this.showTip = true;
        this.fileDialogVisible = true;
    }

    getNumber(num:number) {
        if (!num && num !== 0) {
            return '--'
        } else {
            return num
        }
    }

    // 关闭文件中心弹框
    doDownloadDialogClose() {
        this.showTip = false
        this.fileDialogVisible = false
    }

    getPlatformList() {
        OrgApi.getPlatform().then((res)=>{
            if(res.code === 2000) {
                this.platformList = (res.data || []).map((item:any)=>{
                    if (item.supportedCouponPay) {
                        return {
                            label: item?.platForm?.name || '-',
                            value: item?.platForm?.id || '-'
                        }
                    }
                }).filter((item)=>item)
                this.getList()
            } else {
                throw new Error(res.msg || this.i18n('查询平台失败'))
            }
        }).catch((error) => this.$message.error(error.message))
    }
}