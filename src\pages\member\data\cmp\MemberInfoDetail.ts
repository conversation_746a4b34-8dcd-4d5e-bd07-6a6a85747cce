import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import MemberInfoTagGroup from "pages/member/data/cmp/info/MemberInfoTagGroup";
import MemberInfoChannel from "pages/member/data/cmp/info/MemberInfoChannel";
import MemberInfoAsset from "pages/member/data/cmp/info/MemberInfoAsset";
import MemberInfoBase from "pages/member/data/cmp/info/MemberInfoBase";
import MemberInfoPet from "pages/member/data/cmp/info/MemberInfoPet";
import MemberDetail from "model/member_standard/MemberDetail";
import I18nPage from "common/I18nDecorator";

@Component({
  name: "MemberInfoDetail",
  components: {
    MemberInfoTagGroup,
    MemberInfoChannel,
    MemberInfoAsset,
    MemberInfoBase,
    MemberInfoPet,
  },
})
@I18nPage({
  prefix: [
    "/会员/会员资料",
  ],
  auto: true,
})
export default class MemberInfoDetail extends Vue {
  @Prop()
  dtl: MemberDetail;

  @Prop({ default: () => [] })
  customMemberAttr: any[];

  @Prop()
  showMemberCustomGroupInfo: boolean;

  @Prop()
  showMobileAndEmailCheckInfo: boolean;


  @Watch("dtl", { immediate: true })
  dtlChanged() {
    if (!(this.dtl && this.dtl.memberId)) return;
  }

  onEdit() {
    this.$emit("edit");
  }

  onSendCoupon() {
    this.$emit("send-coupon");
  }
}
