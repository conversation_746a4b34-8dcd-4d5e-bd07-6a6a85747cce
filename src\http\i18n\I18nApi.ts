import ApiClient from 'http/ApiClient'
import Response from 'model/response/Response'
import I18nLanguage from "model/i18n/I18nLanguage";
import I18nCodeTableFilter from "model/i18n/I18nCodeTableFilter";
import ResponseValue from 'model/default/Response'

export default class I18nApi {
  /**
   * 获取国际化资源
   * 获取国际化资源
   * 
   */
  static gets(locale?: string): Promise<Response<any>> {
    return ApiClient.server().get(`/v1/i18n/gets`, {
      headers: {
        locale: locale
      },
      params: {
        locale: locale
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取国际化资源名称
   * 获取国际化资源名称
   * 
   */
  static names(): Promise<Response<any>> {
    return ApiClient.server().get(`/v1/i18n/names`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取国际化语言种类
   * 获取国际化语言种类
   *
   */
  static language(): Promise<ResponseValue<I18nLanguage[]>> {
    return ApiClient.server().get(`/v1/i18n/language`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 下载国际化码表
   * 下载国际化码表
   *
   */
  static codeTableExport(body: I18nCodeTableFilter): Promise<ResponseValue<string>> {
    return ApiClient.server().post(`/v1/i18n/codeTable/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
