export default class GiftCardDailyReportFilter {
  // 营销中心
  marketingCenterEquals: Nullable<string> = null
  // 发生时间开始
  occurredTimeAfterOrEqual: Nullable<Date> = null
  // 发生时间结束
  occurredTimeBefore: Nullable<Date> = null
  // 发生组织
  orgIdEquals: Nullable<string> = null
  // 发生区域等于
  zoneIdEquals: Nullable<string> = null
  // 渠道Id等于
  channelIdEquals: Nullable<string> = null
  // 渠道类型等于
  channelTypeEquals: Nullable<string> = null
  // 卡模板号类似于
  templateNumberLikes: Nullable<string> = null
  // 卡模板号类等于
  templateNumberEquals: Nullable<string> = null
  // 卡模板名称类似于
  templateNameLikes: Nullable<string> = null
  // 卡模板名称等于
  templateNameEquals: Nullable<string> = null
  // 页码
  page: Nullable<number> = null
  // 页面大小
  pageSize: Nullable<number> = null
}