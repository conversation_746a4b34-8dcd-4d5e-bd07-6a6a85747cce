import CostPartyDetail from 'model/equityCard/default/CostPartyDetail'
import SpecialGoodsCostParty from 'model/equityCard/default/SpecialGoodsCostParty'
import { ApportionType } from 'model/equityCard/default/ApportionType'

export default class CostParty {
  // 承担类型：PROPORTION——按比例； AMOUNT——按金额；
  bearType: Nullable<ApportionType> = null
  // 按金额类型：all-全部；part-部分
  amountType: Nullable<string> = null
  // 成本承担方信息
  costPartyDetails: CostPartyDetail[] = []
  // 特殊商品设置
  specialGoodsCostParties: SpecialGoodsCostParty[] = []
}