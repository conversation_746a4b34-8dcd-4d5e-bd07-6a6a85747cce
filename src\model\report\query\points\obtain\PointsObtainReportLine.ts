import MemberIdent from "model/common/member/MemberIdent";

export default class PointsObtainReportLine extends MemberIdent {
  // 交易时间
  tranTime: Nullable<Date> = null
  // 发生组织
  occurredOrg: Nullable<string> = null
  // 发生积分
  occur: Nullable<number> = null
  // 交易金额
  amount: Nullable<number> = null
  // 积分类型
  type: Nullable<string> = null
  // 区域
  zoneIdEquals: Nullable<string> = null
  // 原因
  reason: Nullable<string> = null
  // 说明
  remark: Nullable<string> = null
  // 交易号
  transNo: Nullable<string> = null
  // 活动名称
  activityName: Nullable<string> = null
  // 过期时间
  expire: Nullable<Date> = null
  // 积分场景
  scene: Nullable<string> = null
}