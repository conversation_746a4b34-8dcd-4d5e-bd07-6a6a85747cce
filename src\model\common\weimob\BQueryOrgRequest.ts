// 门店查询
export default class BQueryOrgRequest {
  // 待查询组织的上级组织 ID。通过上级组织可以查询其子级组织列表，不可与 childVid、vidType 同时入参。
  parentVid: Nullable<number> = null
  // 待查询组织的子级组织 ID。通过子级组织查询其上级，不可与 parentVid、vidType 同时入参。
  childVid: Nullable<number> = null
  /**
   * 查询层级模式。当传入了 parentVid 或 childVid 时，可通过此参数指定向上或向下查询的层级模式。
   * 枚举值 1-只查一级；0-查所有级。
   */
  isDirect: Nullable<number> = null
  /**
   * 待查询组织的组织类型。
   * 枚举值
   * 1-集团、2-品牌、3-区域、4部门、5-商场、6-楼层、10-门店、11-网点、100-自提点
   */
  vidType: Nullable<number> = null
  // 待查询组织的状态。1-启用；0-停用。
  vidStatus: Nullable<number> = null
  // 每页的数据条数，最多 50 条
  pageSize: Nullable<number> = null
  // 待查询组织的上级组织 ID。通过上级组织可以查询其子级组织列表，不可与 childVid、vidType 同时入参。
  pageNum: Nullable<number> = null
  // 待查询组织的组织名称，支持模糊查询。
  vidName: Nullable<string> = null
  // 待查询组织的上级组织编号，组织编号是组织的自定义编号，一个店铺下组织编号是唯一的。通过上级组织可以查询其子级组织列表，不可与childVid/childVidCode、vidType 同时入参。parentVidCode与parentVid同时入参以parentVid为准。
  parentVidCode: Nullable<string> = null
  // 待查询组织的子级组织编号，组织编号是组织的自定义编号，一个店铺下组织编号是唯一的。通过子级组织查询其上级，不可与parentVid/parentVidCode、vidType 同时入参。childVidCode与childVid同时入参以childVid为准。
  childVidCode: Nullable<string> = null
}