import {Component, Prop, Vue, Watch} from 'vue-property-decorator'
import GradeApi from 'http/grade/grade/GradeApi'
import MemberApi from 'http/member_standard/MemberApi'
import MemberDetail from 'model/member_standard/MemberDetail'
import DateUtil from 'util/DateUtil'

@Component({
  name: 'AdjustMemberLevelDialog',
  components: {}
})
export default class AdjustMemberLevelDialog extends Vue {
  memberLevel: any = []
  adjustDateDisabled = false
  $refs: any
  ruleForm = {
    level: '',
    adjustLevel: '',
    curLevel: '',
    adjustDate: '',
    remark: ''
  }
  rules: any = {}

  @Prop()
  data: any
  @Prop()
  title: any

  @Prop({
    type: Boolean,
    default: false
  })
  dialogShow: boolean

  @Watch('dialogShow')
  onDialogShow(value: boolean) {
    if (value) {
      if (this.$refs['ruleForm']) {
        this.$refs['ruleForm'].resetFields()
      }
      this.ruleForm.remark = ''
    }
  }
  @Watch('data')
  onDataChange(value: any) {
    if (value) {
      this.ruleForm.level = value.level
      this.ruleForm.curLevel = DateUtil.format(value.date, 'yyyy-MM-dd')
    }
  }
  created() {
    this.rules = {
      adjustLevel: [
        { required: true, message: this.formatI18n('/会员/会员资料/调整会员等级直接点击保存js提示信息', '请选择调整后等级'), trigger: 'change' }
      ],
      adjustDate: [
        { required: true, validator: (rule: any, value: any, callback: any) => {
            if (value) {
              let most = new Date('2038-1-01 00:00:00').getTime()
              let bind = new Date(this.ruleForm.adjustDate + ' 00:00:00').getTime()
              if (bind > most) {
                callback(new Error(this.formatI18n('/会员/会员资料', '大于最大有效期') as any + '2038-1-01'))
              } else {
                callback()
              }
            } else {
              callback(new Error(this.formatI18n('/会员/会员资料', '请选择调整后等级有效期') as any))
            }
          }, trigger: 'change' }
      ]
    }
    this.getMemberLevel()
  }
  doBeforeClose(done: any) {
    this.$emit('dialogClose')
    done()
  }
  doLevelChange() {
    if (this.memberLevel && this.memberLevel.length > 0) {
      let count = 0
      this.memberLevel.forEach((item: any) => {
        if (item.code === this.ruleForm.adjustLevel) {
          if (item.type === 'SPECIAL') {
            this.ruleForm.adjustDate = '2038-01-01'
            this.adjustDateDisabled = true
          } else {
            this.adjustDateDisabled = false
          }
        }
      })
    }
  }
  doModalClose() {
    this.$refs['ruleForm'].validate((valid: any) => {
      if (valid) {
        let param: MemberDetail = new MemberDetail()
        param.gradeCode = this.ruleForm.adjustLevel
        if (this.memberLevel && this.memberLevel.length > 0) {
          this.memberLevel.forEach((item: any) => {
            if (item.code === this.ruleForm.adjustLevel) {
              param.gradeName = item.name
            }
          })
        }

        param.gradeValidate = this.ruleForm.adjustDate as any
        param.remark = this.ruleForm.remark
        MemberApi.saveMemberGradeData(param, this.$route.query.id as string).then((resp: any) => {
          if (resp && resp.code === 2000) {
            this.$message.success(this.formatI18n('/会员/会员资料', '调整成功') as any)
            this.$emit('dialogClose')
          } else {
            throw new Error(resp.msg)
          }
        }).catch((err) => {
          this.$message.error(err.message)
        })
      } else {
        return false;
      }
    })
    // this.$emit('dialogClose')
  }
  doCheckGoods() {
    // todo
  }
  doCancel() {
    this.$emit('dialogClose')
  }
  private getMemberLevel() {
    GradeApi.listGrade('').then((resp: any) => {
      this.memberLevel = resp.data
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
}