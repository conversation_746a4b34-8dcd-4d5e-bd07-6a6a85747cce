<template>
    <div>
        <el-dialog :before-close="doBeforeClose" :close-on-click-modal="false"  :title="formatI18n('/公用/导入', '导入结果')" :visible.sync="importResultDialogShow" append-to-body class="import-result-dialog-view">
            <div class="wrap">
                <div>导入结果：<el-button @click="doDownload" style="margin-left:15px" type="text" v-if="importResult">下载</el-button></div>
                <div>导入成功：<span style="color: green">{{successCount}}</span></div>
                <div>导入失败：<span style="color: red">{{errorCount}}</span></div>
                <div>导入忽略：<span>{{ignoreCount}}</span></div>
            </div>
            <div class="dialog-footer" slot="footer">
                <el-button @click="doModalClose">取消</el-button>
                <el-button @click="doModalClose" size="small" type="primary">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script lang="ts" src="./ImportResultDialog.ts">
</script>

<style lang="scss">
.import-result-dialog-view{
    display: flex;
    align-items: center;
    justify-content: center;
    .el-dialog{
        width: 350px;
        height: 250px;
        margin-top: 0px !important;
        .el-dialog__body{
            height: 137px;
        }
    }
    .wrap{
        padding-left: 75px;
        padding-top: 20px;
    }
}
</style>