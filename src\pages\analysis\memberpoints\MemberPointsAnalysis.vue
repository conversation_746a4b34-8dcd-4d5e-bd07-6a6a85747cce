<template>
  <div class="member-growth-analysis-container">
    <BreadCrume :panelArray="panelArray">
    </BreadCrume>
    <div class="sum-points">
      <div class="search-block1">
        <div>
<!--          {{i18n('/储值/预付卡/电子礼品卡活动/效果评估/可用积分')}}-->
          {{i18n('可用积分')}}
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              <div style="color: #36445A;line-height: 20px;font-size: 13px;">
                <div style="font-size: 15px;margin-bottom:8px">{{i18n('/储值/预付卡/电子礼品卡活动/效果评估/指标说明')}}</div>
                <div>
                  {{i18n('可用积分：截止昨日，会员可用积分总数')}}；<br />
                </div>
              </div>
            </div>
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </div>
        <div style="font-size: 20px">
          <B>{{ getSumUsablePoints }}</B>
        </div>
      </div>
      <div class="search-block1">
        <div>
          <!--          {{i18n('/储值/预付卡/电子礼品卡活动/效果评估/可用积分')}}-->
          {{i18n('临期积分')}}
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              <div style="color: #36445A;line-height: 20px;font-size: 13px;">
                <div style="font-size: 15px;margin-bottom:8px">{{i18n('/储值/预付卡/电子礼品卡活动/效果评估/指标说明')}}</div>
                <div>
                  {{i18n('临期积分：最近一批即将过期的积分总数')}}；<br />
                </div>
              </div>
            </div>
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </div>
        <div style="font-size: 20px">
          <B>{{ getSumLastExpiredPoints }}</B>
        </div>
      </div>
    </div>
<!--    <div>1111</div>-->
    <div class="search-block">
      <MyQueryCmp @reset="doReset" @search="onSearch" :showExpand="false">
        <el-row>
          <el-col :span="16">
            <AnalysisDateSelector ref="analysisDateSelector" :label="i18n('日期粒度')" @change="doDateChange">
            </AnalysisDateSelector>
          </el-col>
          <el-col :span="8">
            <FormItem :label="i18n('门店')">
              <SelectStores v-model="filter.store" :isOnlyId="true" :hideAll="false" width="100%" :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
              </SelectStores>
            </FormItem>
          </el-col>
        </el-row>
      </MyQueryCmp>
    </div>
    <!-- 数据概览 -->
    <div class="overview-block" v-if="detail.summary">
      <div class="overview-title">
        <div>
          {{i18n('/储值/预付卡/电子礼品卡活动/效果评估/数据概览')}}
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              <div style="color: #36445A;line-height: 20px;font-size: 13px;">
                <div style="font-size: 15px;margin-bottom:8px">{{i18n('/储值/预付卡/电子礼品卡活动/效果评估/指标说明')}}</div>
                <div>
                  {{i18n('发放：在查询条件下，发放的积分')}}；<br />
                  {{i18n('消耗：在查询条件下，消耗的积分')}}；<br />
                  {{i18n('调整增加：查询条件下，调整增加的积分')}}；<br />
                  {{i18n('调整增加：查询条件下，调整减少的积分')}}；<br />
                  {{i18n('过期：在查询日期内，过期的积分')}}；<br />

                  <!-- TOTO 增加国际化指标说明 -->
                  {{i18n('可用积分：截止查询日期结束日期，查询门店的可用积分')}}；<br />
                </div>
              </div>
            </div>
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </div>
        <div>
          <el-button style="margin-right: 20px" v-if="hasOptionPermission('/数据/资产/会员积分分析','数据导出')" @click="doExport" size="large" type="primary">
            {{formatI18n('/储值/预付卡/充值卡制售单/列表页面','导出')}}
          </el-button>
        </div>
      </div>
      <div class="overview-info">
        <div class="info-item" v-for="(item,index) in summaryViewArr" :key="index">
          <div class="item-title">{{item.label}}</div>
          <div class="item-number">{{item.value}}</div>
        </div>
      </div>
    </div>

    <div class="chart-block">
      <MemberLineChart :legendNames="legendNames" :xAxisArray="xAxisArray" :dateType="detail.dateUnit" :valueArray="valueArray" :showPercentName="showPercentName">
      </MemberLineChart>
    </div>
    <DownloadCenterDialog :dialogvisiable="downloadCenterFlag" :showTip="true" @dialogClose="doDownloadDialogClose">
    </DownloadCenterDialog>
  </div>
</template>

<script lang="ts" src="./MemberPointsAnalysis.ts">
</script>

<style lang="scss" scoped>
.member-growth-analysis-container {
  width: 100%;
  padding-bottom: 30px;
  overflow: auto;
  .search-block {
    width: 100%;
    border-radius: 8px;
    background-color: #fff;
    padding: 24px;
  }
  .sum-points {
    height: 100px;
    display: flex;
    justify-content: space-between;
    .search-block1 {
      display: flex;
      justify-content: space-between;
      width: 49%;
      border-radius: 8px;
      background-color: #fff;
      padding: 24px;
      margin-bottom: 20px;
    }
  }
  .overview-block {
    width: 100%;
    border-radius: 8px;
    background-color: #fff;
    padding: 24px 0 0 24px;
    margin-top: 16px;
    .overview-title {
      display: flex;
      align-items: center;
      font-weight: 600;
      font-family: PingFangSC, PingFang SC;
      font-size: 16px;
      color: #111111;
      line-height: 24px;
      margin-bottom: 20px;
      justify-content: space-between;
      .el-icon-warning-outline {
        cursor: pointer;
        margin-left: 4px;
        &:hover {
          color: #2878ff;
        }
      }
    }
    .overview-info {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
    }
    .info-item {
      width: 250px;
      margin-bottom: 24px;
      .item-title {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #898fa3;
        line-height: 20px;
        text-align: left;
      }
      .item-number {
        font-family: DINAlternate, DINAlternate;
        font-weight: bold;
        font-size: 20px;
        color: #111111;
        line-height: 24px;
        text-align: left;
        margin-top: 2px;
      }
      .item-trend {
        display: flex;
        align-items: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #969799;
        line-height: 17px;
        margin: 12px 4px 0 0;
      }
    }
  }
  .chart-block {
    padding: 0px 24px 24px;
    background: #ffffff;
    border-radius: 8px;
    margin-top: 16px;
  }
}
</style>