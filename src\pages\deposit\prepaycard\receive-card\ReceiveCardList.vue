<!--
 * @Author: 袁智鹏
 * @Date: 2024-07-29 10:40:06
 * @LastEditTime: 2024-07-29 10:40:06
 * @LastEditors: 袁智鹏
 * @Description:
 * @FilePath: \phoenix-web-ui\src\pages\deposit\prepaycard\receive-card\ReceiveCardList.vue
 * 记得注释
-->
<template>
  <div class="receive-card-list-container">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button size="large" type="primary" @click="doCreate" v-if="hasOptionPermission('/卡/卡管理/领卡单', '单据维护')">
          {{ i18n('新建领卡单') }}
        </el-button>
      </template>
    </BreadCrume>
    <div class="setting-container">
      <MyQueryCmp @reset="onReset" @search="onSearch">
        <el-row>
          <el-col :span="8">
            <form-item :label="i18n('领卡单号')">
              <el-input v-model="query.billNumberEquals" :placeholder="i18n('请输入领卡单号')"></el-input>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="i18n('状态')">
              <el-select placeholder="不限" v-model="query.stateEquals" style="width:100%">
                <el-option :label="i18n('全部')" :value="null"></el-option>
                <el-option :label="i18n('未审核')" value="INITIAL"></el-option>
                <el-option :label="i18n('已审核')" value="AUDITED"></el-option>
              </el-select>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="i18n('卡号')">
              <el-input v-model="query.cardCodeEquals" :placeholder="i18n('请输入卡号')"></el-input>
            </form-item>
          </el-col>
        </el-row>
        <template slot="opened">
          <el-row>
            <el-col :span="8">
              <form-item :label="i18n('领出组织')">
                <SelectStores v-model="outOrg" @change="$forceUpdate()" :isOnlyId="false" :hideAll="true" width="100%"
                              :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
                </SelectStores>
              </form-item>
            </el-col>
            <el-col :span="8">
              <form-item :label="i18n('领入组织')">
                <SelectStores v-model="inOrg" @change="$forceUpdate()" :isOnlyId="false" :hideAll="true" width="100%"
                              :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
                </SelectStores>
              </form-item>
            </el-col>
          </el-row>
        </template>
      </MyQueryCmp>
      <el-table :data="tableData" ref="table" style="width: 100%;margin-top:20px"
                fixed>
        <el-table-column width="55">
        </el-table-column>
        <el-table-column :label="i18n('领卡单号')" width="200" fixed>
          <template slot-scope="scope">
            <span class="span-btn" @click="goDtl(scope.row.billNumber)">{{scope.row.billNumber ||'-'}}</span>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('状态')" width="200">
          <template slot-scope="scope">
            <div style="display:flex;align-items:center">
              <span class="dot" :style="{background: computeState(scope.row.state).color}"></span>
              <span>{{computeState(scope.row.state).state}}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('起始卡号')" width="200">
          <template slot-scope="scope">
            {{scope.row.startCardCode || '--'}}
          </template>
        </el-table-column>
        <el-table-column :label="i18n('结束卡号')" width="200">
          <template slot-scope="scope">
            {{scope.row.endCardCode || '--'}}
          </template>
        </el-table-column>
        <el-table-column :label="i18n('领出组织')" width="200">
          <template slot-scope="scope">
            {{('['+scope.row.outOrg.id+']'+scope.row.outOrg.name) || '--'}}
          </template>
        </el-table-column>
        <el-table-column :label="i18n('领入组织')" width="200">
          <template slot-scope="scope">
            {{('['+scope.row.inOrg.id+']'+scope.row.inOrg.name) || '--'}}
          </template>
        </el-table-column>
        <el-table-column :label="i18n('创建时间')" width="200">
          <template slot-scope="scope">
            {{scope.row.created | dateFormate3}}
          </template>
        </el-table-column>
        <el-table-column :label="i18n('最后修改时间')" width="200">
          <template slot-scope="scope">
            {{scope.row.lastModified | dateFormate3}}
          </template>
        </el-table-column>
        <el-table-column label="创建人" prop="created" width="140">
          <template slot-scope="scope">
            {{scope.row.creator || '--'}}
          </template>
        </el-table-column>
        <el-table-column :label="i18n('操作')" width="200">
          <template slot-scope="scope">
            <div>
              <div v-if="['INITIAL'].indexOf(scope.row.state) > -1">
                <span class="span-btn" v-if="hasOptionPermission('/卡/卡管理/领卡单', '单据审核')">
                  <el-button type="primary" @click="doAudit(scope.row.billNumber)">{{i18n('审核')}}</el-button>
                </span>
              </div>
              <div v-else>
                {{'--'}}
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!--分页栏-->
      <el-pagination :current-page="page.page" :page-size="page.pageSize" :page-sizes="[10, 20, 30, 40]" :total="page.total"
                     @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background layout="total, prev, pager, next, sizes,  jumper">
      </el-pagination>
    </div>
  </div>
</template>

<script lang="ts" src="./ReceiveCardList.ts">
</script>

<style lang="scss" scoped>
.receive-card-list-container {
  width: 100%;

  .dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin: 0 4px 2px;
  }
  .span-btn {
    margin-right: 8px;
  }

  ::v-deep .el-range__icon {
    line-height: 26px;
  }

  ::v-deep .el-range-separator {
    line-height: 26px;
  }

  ::v-deep .el-range__close-icon {
    line-height: 26px;
  }
}
</style>