<template>
  <div class="produce-card-dtl-container">
    <BreadCrume :panelArray="panelArray" :fontSize="18">
      <template slot="operate">
        <el-button @click="doExportPwd" v-if="hasOptionPermission('/卡/卡管理/制卡单', '导出卡密') && ['AUDITED'].indexOf(dtl.state) > -1">
          {{ i18n('导出卡密') }}
        </el-button>
        <el-button @click="doViewPwd" v-if="hasOptionPermission('/卡/卡管理/制卡单', '查看文件密码') && ['AUDITED'].indexOf(dtl.state) > -1 && dtl.exportFinish">
          {{ i18n('查看加密密码') }}
        </el-button>
        <el-button @click="doMakeFinish" v-if="hasOptionPermission('/卡/卡管理/制卡单', '制卡完成') && ['AUDITED'].indexOf(dtl.state) > -1">
          {{ i18n('制卡完成') }}
        </el-button>
        <el-button @click="doViewLogs" v-if="['AUDITED', 'FINISH', 'CANCELED'].indexOf(dtl.state) > -1">
          {{ i18n('操作日志') }}
        </el-button>
        <el-button @click="doCancel" v-if="hasOptionPermission('/卡/卡管理/制卡单', '单据维护') && dtl.state === 'AUDITED'">
          {{ i18n('作废') }}
        </el-button>
        <el-button @click="doAudit" v-if="hasOptionPermission('/卡/卡管理/制卡单', '单据审核') && ['INITIAL'].indexOf(dtl.state) > -1">
          {{i18n('审核')}}
        </el-button>
        <el-button @click="doEdit" v-if="hasOptionPermission('/卡/卡管理/制卡单', '单据维护') && ['INITIAL'].indexOf(dtl.state) > -1">
          {{i18n('修改')}}
        </el-button>
        <el-button @click="doRemove" v-if="hasOptionPermission('/卡/卡管理/制卡单', '单据维护') && ['INITIAL'].indexOf(dtl.state) > -1">
          {{i18n('删除')}}
        </el-button>
      </template>
    </BreadCrume>

    <div style="overflow: auto">
      <div class="top-wrap">
        <div class="top-header">
          <div class="avatar">
            <el-image v-if="dtl.logoUrl" class="avatar-img" :src="dtl.logoUrl" fit="fill">
            </el-image>
            <div class="back" v-else>
              <img src="~assets/image/storevalue/img_default_no_picture.png">
            </div>
          </div>
          <div class="header-info">
            <div class="header-title">
              <div class="coupon-name" :title="dtl.cardTemplateName" v-if="dtl.cardTemplateName">
                {{ dtl.cardTemplateName }}
              </div>
              <template v-if="dtl && dtl.state">
                <div class="state-block" v-if="dtl.state === 'INITIAL'" style="background:#FFAA00">
                  {{ i18n('未审核') }}
                </div>
                <div class="state-block" v-if="dtl.state === 'MAKING'" style="background:#FFAA00">
                  {{ i18n('制卡中') }}
                </div>
                <div class="state-block" v-if="dtl.state === 'AUDITED'" style="background:#1597FF">
                  {{ i18n('已审核') }}
                </div>
                <div class="state-block" v-if="dtl.state === 'FINISH'" style="background:#0CC66D">
                  {{ i18n('已制卡') }}
                </div>
                <div class="state-block" v-if="dtl.state === 'CANCELED'" style="background:#A1B0C8">
                  {{ i18n('已作废') }}
                </div>
              </template>
            </div>
            <div class="coupon-number" v-if="dtl.billNumber">
              {{ i18n('制卡单号') }}：{{ dtl.billNumber }}
            </div>
          </div>
        </div>
        <el-row class="top-body">
          <el-col :span="5" class="body-section">
            <div class="body-container">
              <div class="body-title">{{ i18n('制卡数量') }}</div>
              <div class="body-info">{{ dtl.makeQty }}</div>
            </div>
          </el-col>
          <!-- <el-col :span="5" class="body-section">
            <div class="body-container">
              <div class="body-title">{{ i18n('已制卡数量') }}</div>
              <div class="body-info">{{ dtl.makeQty }}</div>
            </div>
          </el-col> -->
          <el-col :span="10" class="body-section">
            <div class="body-container">
              <div class="body-title">{{ i18n('创建信息') }}</div>
              <div class="body-info">
                <template>
                  <span v-if="dtl.logs && dtl.logs.length === 1 && (dtl.logs[0].type==='新建信息' || dtl.logs[0].type==='Create')">
                    {{ dtl.logs[0].operator }}
                  </span>
                  <span v-if="dtl.logs!==null && dtl.logs.length > 1">
                    <span v-for="(item,index) in dtl.logs" :key="index">
                      <span v-if="item.type==='新建信息' || item.type==='Create'">
                        {{ item.operator || '--' }}
                      </span>
                    </span>
                  </span>
                </template>
                <span>{{dtl.created | dateFormate3}}</span>
              </div>
            </div>
          </el-col>
        </el-row>
        <div class="top-footer" v-if=" dtl && dtl.remark">
          <FormItem :label="i18n('备注') + '：'">
            <el-tooltip effect="light" placement="top-start">
              <div slot="content" v-html="xss(dtl.remark.replace(/\n/g, '<br/>'))"></div>
              <div class="overtext" v-html="xss(dtl.remark.replace(/\n/g, '<br/>'))"></div>
            </el-tooltip>
          </FormItem>
        </div>
      </div>
      <div class="setting-container" style="margin-top: 16px">
        <FormItem :label="i18n('卡模板') + '：'">
          <CardTplItem no-i18n :isShowFitGoods="!cardAttributeFix" :number="dtl.cardTemplateNumber" :readonly="true" />
        </FormItem>
        <FormItem :label="i18n('制卡数量') + '：'">
          <div style="line-height:36px">{{(dtl.makeQty || '--') + i18n('张')}}</div>
        </FormItem>
        <FormItem :label="i18n('起始卡号') + '：'">
          <div style="line-height:36px">{{dtl.startCardCode || '--'}}</div>
        </FormItem>
        <FormItem :label="i18n('结束卡号') + '：'">
          <div style="line-height:36px">{{dtl.endCardCode || '--'}}</div>
        </FormItem>
        <FormItem :label="i18n('/公用/券核销/发生组织') + '：'" v-if="dtl.occurredOrg && !cardAttributeFix">
          <div style="line-height:36px">
            <template v-if="!dtl.occurredOrg">{{i18n('不限')}}</template>
            <template v-else>{{`[${dtl.occurredOrg.id}]${dtl.occurredOrg.name}`}}</template>
          </div>
        </FormItem>
        <FormItem :label="i18n('写卡类型') + '：'">
          <div style="line-height:36px">{{getWriteCardType(dtl.writeCardType) || '--'}}</div>
        </FormItem>
      </div>
      <div class="setting-container" style="margin-top: 16px">
        <div class="section-title">{{i18n('已制卡信息')}}</div>
        <el-table :data="makeTableData" style="width: 100%">
          <el-table-column :label="i18n('已制卡时间')" width="200">
            <template slot-scope="scope">
              {{scope.row.makeTime | dateFormate3}}
            </template>
          </el-table-column>
          <el-table-column :label="i18n('已制卡卡号段')" width="300">
            <template slot-scope="scope">
              <span class="ellipse" style="width:300px" :title="scope.row.startCardCode + '—' + scope.row.endCardCode">
                {{scope.row.startCardCode + '—' + scope.row.endCardCode}}
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('已制卡数量')" width="200">
            <template slot-scope="scope">
              {{scope.row.madeCount}}
            </template>
          </el-table-column>
          <el-table-column :label="i18n('制卡失败数量')" width="200">
            <template slot-scope="scope">
              <span class="span-btn" @click="doViewFailNum" v-if="scope.row.madeFailCount">
                {{scope.row.madeFailCount}}
              </span>
              <span v-else>0</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('操作人')" width="200">
            <template slot-scope="scope">
              {{scope.row.operator || '--'}}
            </template>
          </el-table-column>
          <el-table-column :label="i18n('操作')" width="200">
            <template slot-scope="scope">
              {{scope.row.operate | '--'}}
            </template>
          </el-table-column>
        </el-table>
        <!--分页栏-->
        <el-pagination :current-page="makeTablePage.page" :page-size="makeTablePage.pageSize" :page-sizes="[10, 20, 30, 40]"
          :total="makeTablePage.total" @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background
          layout="total, prev, pager, next, sizes,  jumper">
        </el-pagination>
      </div>
    </div>
    <DownloadCenterDialog :dialogvisiable="fileDialogVisible" :showTip="showTip" @dialogClose="doDownloadDialogClose"></DownloadCenterDialog>
    <el-dialog :title="i18n('操作日志')" :visible.sync="logsDialogVisible">
      <el-table :data="dtl.logs" style="width: 100%" height="500">
        <el-table-column :label="i18n('操作时间')">
          <template slot-scope="scope">
            <span>{{scope.row.occurredTime | dateFormate3}}</span>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('操作人')">
          <template slot-scope="scope">
            <span>{{scope.row.operator || '--'}}</span>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('操作')">
          <template slot-scope="scope">
            <span>{{scope.row.type || '--'}}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <el-dialog :title="i18n('制卡失败明细')" :visible.sync="failDialogVisible">
      <el-table :data="makeFailData" style="width: 100%" height="500">
        <el-table-column :label="i18n('单号')">
          <template slot-scope="scope">
            <span>{{scope.row.billNumber || '--'}}</span>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('卡号')">
          <template slot-scope="scope">
            <span>{{scope.row.cardCode || '--'}}</span>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('原因')">
          <template slot-scope="scope">
            <span>{{scope.row.reason || '--'}}</span>
          </template>
        </el-table-column>
      </el-table>
      <!--分页栏-->
      <el-pagination :current-page="makeFailPage.page" :page-size="makeFailPage.pageSize" :page-sizes="[10, 20, 30, 40]" :total="makeFailPage.total"
        @current-change="onHandleFailCurrentChange" @size-change="onHandleFailSizeChange" background layout="total, prev, pager, next, sizes,  jumper">
      </el-pagination>
    </el-dialog>
  </div>
</template>

<script lang="ts" src="./ProduceCardDtl.ts">
</script>

<style lang="scss" scoped>
.produce-card-dtl-container {
  width: 100%;
  overflow: auto;

  ::v-deep .qf-form-item .qf-form-label {
    text-align: left !important;
    width: 130px !important;
  }

  .top-wrap {
    border-radius: 8px;
    background-color: white;
    padding: 24px 24px 0 24px;

    .top-header {
      display: flex;
      .avatar {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 56px;
        height: 56px;
        border-radius: 4px;
        overflow: hidden;
        margin-right: 16px;
        .avatar-img {
          width: 48px;
          height: 48px;
          border-radius: 4px;
        }
        .back {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 56px;
          height: 56px;
          background-color: rgba(242, 242, 242, 1);
          img {
            width: 56px;
            height: 56px;
          }
        }
      }
      .header-info {
        .header-title {
          display: flex;
          align-items: center;
          .coupon-name {
            max-width: 300px;
            height: 28px;
            font-size: 20px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #242633;
            line-height: 28px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
        .state-block {
          display: flex;
          justify-content: center;
          height: 22px;
          border-radius: 4px;
          padding: 0 4px;
          margin-left: 4px;
          font-size: 14px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #ffffff;
          line-height: 22px;
        }
        .coupon-number {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #79879e;
          line-height: 22px;
          margin-top: 8px;
        }
      }
    }

    .top-body {
      display: flex;
      width: 100%;
      height: 80px;
      background: #f7f9fc;
      border-radius: 4px;
      margin-top: 26px;

      .body-section {
        padding: 12px 0 16px 16px;
        &:nth-last-child(1) {
          .body-container {
            border: none !important;
          }
        }

        .body-container {
          border-right: 1px solid #d7dfeb;

          .body-title {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #79879e;
            line-height: 22px;
          }
          .body-info {
            font-size: 18px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #242633;
            line-height: 24px;
            margin-top: 6px;
          }
        }
      }
    }

    .top-footer {
      margin-top: 20px;
      .overtext {
        font-size: 13px;
        line-height: 22px;
        clear: both;
        position: relative;
        top: -30px;
        width: 530px;
      }
    }

    .right {
      margin-top: 13px;
      display: flex;
      flex: 1;
      flex-direction: column;
      position: relative;

      .top {
        padding: 15px 0;
        display: flex;
        border-bottom: 1px solid rgba(242, 242, 242, 1);

        .bill {
          color: rgba(51, 51, 51, 0.***************);
        }

        .name {
          font-weight: 500;
          margin-top: 8px;
          font-size: 20px;
        }

        .desc {
          color: rgba(51, 51, 51, 0.***************);
        }

        .state {
          font-weight: 500;
          margin-top: 8px;
          font-size: 20px;
        }
      }

      .bottom {
        padding-bottom: 20px;

        .account-info {
          margin-top: 10px;
        }

        .red {
          color: red;
        }

        .green {
          color: #008000;
        }
      }
    }
  }
}
</style>