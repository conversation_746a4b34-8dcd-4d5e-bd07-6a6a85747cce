<template>
  <el-dialog :before-close="doBeforeClose"
      append-to-body
      :close-on-click-modal="false"  :title="title" :visible.sync="dialogShow" class="selectGoods-dialog-view">
    <div style="text-align:right;">
      <el-button type="primary" @click="doAdd">{{ i18n('添加商品') }}</el-button>
      <el-button type="primary" @click="openUploadGood">{{ i18n('批量导入') }}</el-button>
    </div>
    <div style="margin-bottom:10px;">
      <span>
        <!-- 已选{{checked.filter(x => x).length}}行 -->
        {{ getSelecRowStr }}
      </span>
      <el-button @click="deleteBatch">{{ i18n('批量删除') }}</el-button>
    </div>
    <div style="max-height: 600px;overflow:auto;">
      <!-- <el-row class="sgdHeader">
        <el-col :span="1" class="sgdCell">
					<el-checkbox v-model="checkedAll" @change="changeAll" style="display:inline-block;max-height: calc(100% - 5px);height: 28px;"></el-checkbox>
        </el-col>
        <el-col :span="5" class="sgdCell">{{ i18n('商品条码') }}</el-col>
        <el-col :span="14" class="sgdCell">{{ i18n('用券记录方式') }}</el-col>
        <el-col :span="4" class="sgdCell">
          {{ i18n('操作') }}
        </el-col>
      </el-row> -->
      <table class="tableHeader">
        <tr class="sgdHeader">
          <td>
            <el-checkbox v-model="checkedAll" @change="changeAll" style="display:inline-block;max-height: calc(100% - 5px);height: 28px;"></el-checkbox>
          </td>
          <td>
            {{ i18n('商品条码') }}
          </td>
          <td>
            {{ formatI18n('/公用/券模板', '券承担方') }}
          </td>
          <td>
            {{ i18n('操作') }}
          </td>
        </tr>
      </table>
      <div class="tableDataCon">
        <el-row v-if="!selectData.length">
          <el-col :span="24" class="noGoods">{{ i18n('未选择特殊商品') }}</el-col>
        </el-row>
        <template v-else>
          <el-form label-width="0" ref="specialGoodsForm">
            <table class="tableBody">
              <tr v-for="(item, index) in selectData" :key="index" class="sgdline">
                <td>
                  <el-checkbox @change="changeCheck" v-model="checked[index]" style="display:inline-block;max-height: calc(100% - 5px);height: 28px;"></el-checkbox>
                </td>
                <td>
                  {{ item.barcode }}
                </td>
                <td>
                  <CBearSGForm :key="'CBearSGForm'+index" v-model="item.costPartyDetails" :parties="parties" :ref="'CBearSGForm'+index"></CBearSGForm>
                </td>
                <td>
                  <el-button type="text" @click="deleteGood(index)">
                    {{ i18n('删除') }}
                  </el-button>
                </td>
              </tr>
            </table>
          </el-form>
        </template>
      </div>
    </div>
    <div class="dialog-footer" slot="footer">
    <el-button @click="doModalClose('cancel')">{{formatI18n('/公用/按钮', '取消')}}</el-button>
    <el-button @click="doModalClose('confirm')" size="small" type="primary">{{formatI18n('/公用/按钮', '确定')}}</el-button>
    </div>
    <GoodsSelectorDialog ref="selectGoodsScopeDialog" @summit="doSubmitGoods"/>
    <ImportDialog
      ref="ImportDialogGoods"
      :importUrl="importUrl"
      :templateName="i18n('导入商品模板')"
      :templatePath="templatePath"
      :title="formatI18n('/公用/券模板', '导入')"
      @dialogClose="doImportDialogClose" @upload-success="doUploadSuccess">
    </ImportDialog>
  </el-dialog>
</template>
<script lang="ts">
import { Component, Prop, Provide, Vue, Watch } from "vue-property-decorator";
import GoodsSelectorDialog from 'cmp/selectordialogs/GoodsSelectorDialog.vue'
import ImportDialog from 'cmp/importdialogs/ImportDialog.vue'
import RSGoods from 'model/common/RSGoods'
import AmountToFixUtil from "util/AmountToFixUtil";
import I18nPage from 'common/I18nDecorator'
import RSCostPartyFilter from 'model/common/RSCostPartyFilter'
import CostPartyApi from 'http/costparty/CostPartyApi'
import CBearSGForm from 'cmp/coupontenplate/cmp/cBearSGForm.vue'
@Component({
	name: "SelectGoodsDialog",
	components: {
		GoodsSelectorDialog,
    ImportDialog,
    CBearSGForm
	}
})
@I18nPage({
  prefix: ['/储值/会员储值/储值充值活动/列表页面','/营销/券礼包活动/券礼包活动', '/公用/按钮',"/公用/券模板"],
})
export default class SelectGoodsDialog extends Vue {
  $refs: any;
	@Prop()
	title: string; // dialog标题
  @Prop()
	data: string; // dialog标题

  dialogShow: boolean = false
  // 选中的数据
  selectData: any[] = []
  importDialogShow: boolean = false
  importUrl: string = 'v1/goods/importBarcodes'
  checkedAll: boolean = false
  checked: boolean[] = []
  deleteSelectData: any[] = []
  // 承担金额表单
  copyParties: any = []
  costParties: any = []
  partySelectArray: any[] = []
  partyInputArray: any[] = []
  parties: any[] = []
  // 承担类型：PROPORTION——按比例； AMOUNT——按金额
  typeCparties: string = 'AMOUNT'
  costAll: string = 'all'
  get templatePath() {
    if (location.href.indexOf('localhost') === -1) {
        return 'template_barcodes.xlsx'
    } else {
        return 'template_barcodes.xlsx'
    }
  }

  get getSelecRowStr() {
    return this.i18n('已选{0}行').replace(/\{0\}/g, String(this.checked.filter(x => x).length))
  }

  get showDelBtn() {
    let show: any
    return (index: any) => {
      if (this.costParties.length == 1) {
        show = false
      }
      // 按比例
      if (this.typeCparties == 'PROPORTION') {
        show = index != 0
      }
      // 按金额
      if (this.typeCparties == 'AMOUNT') {
        if (this.costAll == 'all') {
          show = false
        } else if (this.costAll == 'part') {
          show = this.costParties.length == 2 ? false : (index != 0 && index < this.costParties.length - 1)
        }
      }
      return show
    }
  }

  @Watch('partySelectArray', { deep: true })
  onPartySelectArrayChange(value: any) {}
  created() {
    this.getCostParty()
  }
  // 导入开启
  openUploadGood() {
    this.importDialogShow = true
    this.$refs.ImportDialogGoods.show()
  }
  // 导入关闭
  doImportDialogClose() {
    this.importDialogShow = false
  }
  // 导入成功
  doUploadSuccess(value: any) {
    // console.log('上传成功', value);
    if (value.response.code === 2000) {
      if (value.response.data && value.response.data.barcodes && value.response.data.barcodes.length) {
        value.response.data.barcodes.forEach((item: any) => {
          // 不存在，则加入
          if (!this.selectData.find((i: any) => i.barcode == item)) {
            let good: any = {}
            good.barcode = item
            // good.price = 0.01
            this.selectData.push(good)
          }
        })
      }
    } else {
      this.$message.error(value.response.msg)
    }
  }
  // 输入最多金额变化
  doPayWayByAmount(index: number) {
    let maxPrice: any = this.selectData[index].price
    maxPrice = parseFloat(maxPrice) || 0.01
    maxPrice = AmountToFixUtil.formatAmount(maxPrice, 999999.99, 0, 2);
    this.$set(this.selectData[index], 'price', maxPrice)
  }
  // 打开选择商品
  open(arr: any[]) {
    if (!this.parties.length) {
      this.getCostParty()
    }
    this.checked = []
    ;(arr || []).forEach((item: any) => {
      this.checked.push(false)
    })
    this.selectData = [...arr]
    // this.selectDataTmp = [...arr]
    this.dialogShow = true
  }
  // 弹框操作
  doBeforeClose(done: any) {
		this.$emit("dialogClose");
		done();
	}
  // 确认按钮
	doModalClose(type: string) {
		if (type === "confirm") {
      let arr: any[] = []
			// 确认校验数据
      this.selectData.forEach((item: any, index: number) => {
        if (this.$refs['CBearSGForm'+index] && this.$refs['CBearSGForm'+index]) {
          arr.push(this.$refs['CBearSGForm'+index][0].formValiPromise())
        }
      })
      console.log('jiaoyanfn', this);
      Promise.all(arr).then(res => {
        console.log('校验通过', res);
        // 校验通过 返回数据
        this.$emit('submit', this.selectData);
        this.dialogShow = false
      }).catch(e => {
        console.log(e);
      })
		} else {
			// 取消，清除数据(可不清，重新打开时，数据会重置)
      this.dialogShow = false
		}
	}

  // 打开弹框
  doAdd() {
		this.$refs.selectGoodsScopeDialog.open(this.selectData);
	}
  // 接受选择的商品
  doSubmitGoods(arr: any[]) {
    console.log('选择返回', arr);
		if (arr && arr.length > 0) {
      let arrTmp: any = []
			arr.forEach((item: any) => {
        let obj = this.selectData.find((i: any) => i.barcode == item.barcode)
        if(obj) {
          arrTmp.push(obj)
        } else {
          // item.price = 0.01
          arrTmp.push({
            barcode: item.barcode,
            costPartyDetail: item.costPartyDetail || []
          })
        }
      })
      this.selectData = [...arrTmp]
		} else {
      this.selectData = []
    }
	}
  // 单个删除商品
  deleteGood(index: number) {
    this.selectData.splice(index, 1)
  }
  // 校验规则
  trRule(rule: any, value: any, callback: any) {
    if(!this.selectData[rule.field]) {
      callback(this.i18n('此项必填！'))
    } else {
      // 0.01-99999.99
      if (parseFloat(this.selectData[rule.field].price as any) >= 0.01 && parseFloat(this.selectData[rule.field].price as any) <= 99999.99) {
        callback()
      } else {
        callback(this.i18n('输入值区间为：0.01-99999.99'))
      }
    }
  }
  // 全选变化
  changeAll(e: any) {
    console.log('全选', e);
    if (e) {
      this.checked = this.selectData.map(i => {
        return true
      })
    } else {
      this.checked = this.selectData.map(i => {
        return false
      })
    }
  }
  // 单选
  changeCheck(e: any) {
    console.log('单选', e, this.checked);
    if (this.checked.filter(x => x).length == this.selectData.length) {
      this.checkedAll = true
    } else {
      this.checkedAll = false
    }
    
  }
  // 批量删除
  deleteBatch() {
    let arr: any = []
    this.checked.forEach((item: any, index: number) => {
      if (item) {
        arr.push({...this.selectData[index], index: index})
      }
    })
    if (!arr.length) {
      this.$message.warning(this.i18n('请先勾选要删除的单据'))
    } else if (arr.length == this.selectData.length) {
      this.selectData = []
      this.checked = []
    } else {
      arr.forEach((item: any) => {
        let index = this.selectData.findIndex(i => i.barcode == item.barcode)
        if (index > -1) {
          this.selectData.splice(index, 1)
        }
      })
      this.checked = this.selectData.map(i => {
        return false
      })
    }
    this.checkedAll = false
  }

  // ====
  private getCostParty() {
    let params: RSCostPartyFilter = new RSCostPartyFilter()
    params.page = 0
    params.pageSize = 0
    CostPartyApi.query(params).then((resp: any) => {
      if (resp && resp.code === 2000) {
        let dataTmp = resp.data.map((x: any) => {
          return {
            ...x,
            id: x.costParty.id,
            name: x.costParty.name
          }
        })
        this.parties.push(dataTmp)
        this.copyParties = dataTmp
      }
    }).catch((error: any) => {
      this.$message.error(error.message)
    })
  }
}

</script>
<style lang="scss" scoped>
::v-deep .el-dialog {
  min-width: 1000px;
}
.selectGoods-dialog-view {
  .tableHeader, .tableBody {
    width: 100%;
    border-collapse:collapse;
    .sgdHeader, .sgdline {
      text-align: center;
      line-height: 30px;
    }
    .sgdline {
      &:nth-child(1) {
        td {
          border-top: 0;
        }
      }
      td {
        border-right:0;
      }
    }
    td {
      border: 1px solid #aaa;
      &:nth-child(1) {
        width: 3%;
      }
      &:nth-child(2) {
        width: 10%;
      }
      &:nth-child(3) {
        width: 77%;
      }
      &:nth-child(4) {
        width: 10%;
      }
    }
  }
  .sgdHeader {
    //margin-top: 10px;
    border-right: 1px solid #aaa;
    border-top: 1px solid #aaa;
  }
  .sgdCell {
    line-height: 30px;
    text-align: center;
    border-left: 1px solid #aaa;
    border-bottom: 1px solid #aaa;
  }
  .tableDataCon {
    border-right: 1px solid #aaa;
    max-height: 500px;
    //overflow: auto;
    overflow-y: overlay;
    //border-bottom: 1px solid #aaa;
    .sgdCell {
      //height: 72px;
      line-height: 72px;
    }
  }
  .noGoods {
    line-height: 38px;
    text-align: center;
    border-left: 1px solid #aaa;
    border-bottom: 1px solid #aaa;
  }
}
</style>