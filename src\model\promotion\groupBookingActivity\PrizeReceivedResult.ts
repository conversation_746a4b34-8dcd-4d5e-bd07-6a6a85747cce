import CouponReceivedResult from "model/promotion/groupBookingActivity/CouponReceivedResult";
import EntityReceivedResult from "model/promotion/groupBookingActivity/EntityReceivedResult";
import PointsReceivedResult from "model/promotion/groupBookingActivity/PointsReceivedResult";
import AwardValue from "model/promotion/bigWheelActivity/AwardValue";


export default class PrizeReceivedResult {
  // 
  prizeType: Nullable<string> = null
  // 
  entityReceivedResult: Nullable<EntityReceivedResult> = null
  // 
  pointsReceivedResult: Nullable<PointsReceivedResult> = null
  // 
  couponReceivedResult: CouponReceivedResult[] = []
}