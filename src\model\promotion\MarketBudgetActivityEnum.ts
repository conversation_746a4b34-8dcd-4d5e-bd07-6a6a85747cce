/*
 * @Author: 黎钰龙
 * @Date: 2024-05-17 16:43:00
 * @LastEditTime: 2024-05-20 10:28:43
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\promotion\MarketBudgetActivityEnum.ts
 * 记得注释
 */
//营销预算（OA可审批的活动），活动类型枚举
export enum MarketBudgetActivityEnum {
  //充值有礼
  PrepayDepositActivityRule = 'PrepayDepositActivityRule',
  //储值支付立减
  MemberBalanceReductionActivityRule = 'MemberBalanceReductionActivityRule',
  //储值支付折扣
  MemberBalanceDiscountActivityRule = 'MemberBalanceDiscountActivityRule',
  //单品满数量加送积分
  GoodsGainAdditionalPointsByQtyActivityRule = 'GoodsGainAdditionalPointsByQtyActivityRule',
  //商品组合满数量加送积分
  GoodsGroupGainAdditionalPointsByQtyActivityRule = 'GoodsGroupGainAdditionalPointsByQtyActivityRule',
  //商品满额加送积分
  GoodsGainAdditionalPointsActivityRule = 'GoodsGainAdditionalPointsActivityRule',
  //商品满额积分加倍
  GoodsGainPointsSpeedActivityRule = 'GoodsGainPointsSpeedActivityRule',
  //商品满额发大礼包
  GoodsGainGiftBagActivityRule = 'GoodsGainGiftBagActivityRule',
  //注册发大礼包
  MemberRegisterGiftActivityRule = 'MemberRegisterGiftActivityRule',
  //积分兑换商品
  PointsExchangeGoodsActivityRule = 'PointsExchangeGoodsActivityRule',
  //导出券码发券
  ExportCouponCodeActivityRule = 'ExportCouponCodeActivityRule',
  //群发券
  ManualHandOutGiftActivityRule = 'ManualHandOutGiftActivityRule',
  //积分抵现
  PointsChargeActivityRuleV2 = 'PointsChargeActivityRuleV2'
}

//营销预算（OA可审批的活动），活动类型中午映射
export enum MarketBudgetLabelEnum {
  //充值有礼
  PrepayDepositActivityRule = '充值有礼',
  //储值支付立减
  MemberBalanceReductionActivityRule = '储值支付立减',
  //储值支付折扣
  MemberBalanceDiscountActivityRule = '储值支付折扣',
  //单品满数量加送积分
  GoodsGainAdditionalPointsByQtyActivityRule = '单品满数量加送积分',
  //商品组合满数量加送积分
  GoodsGroupGainAdditionalPointsByQtyActivityRule = '商品组合满数量加送积分',
  //商品满额加送积分
  GoodsGainAdditionalPointsActivityRule = '商品满额加送积分',
  //商品满额积分加倍
  GoodsGainPointsSpeedActivityRule = '商品满额积分加倍',
  //商品满额发大礼包
  GoodsGainGiftBagActivityRule = '商品满额发大礼包',
  //注册发大礼包
  MemberRegisterGiftActivityRule = '注册发大礼包',
  //积分兑换商品
  PointsExchangeGoodsActivityRule = '积分兑换商品',
  //导出券码发券
  ExportCouponCodeActivityRule = '导出券码发券',
  //群发券
  ManualHandOutGiftActivityRule = '群发券',
  PointsChargeActivityRuleV2 = "积分抵现"
}

export enum MarketBudgetTypeEnum {
  //充值有礼
  PrepayDepositActivityRule = 'DEPOSIT_GIFT',
  //储值支付立减
  MemberBalanceReductionActivityRule = 'MEMBER_BALANCE_REDUCTION',
  //储值支付折扣
  MemberBalanceDiscountActivityRule = 'MEMBER_BALANCE_DISCOUNT',
  //单品满数量加送积分
  GoodsGainAdditionalPointsByQtyActivityRule = 'GOODS_GAIN_ADDITIONAL_POINTS_BY_QTY',
  //商品组合满数量加送积分
  GoodsGroupGainAdditionalPointsByQtyActivityRule = 'GOODS_GROUP_GAIN_ADDITIONAL_POINTS_BY_QTY',
  //商品满额加送积分
  GoodsGainAdditionalPointsActivityRule = 'GOODS_GAIN_ADDITIONAL_POINTS',
  //商品满额积分加倍
  GoodsGainPointsSpeedActivityRule = 'GOODS_GAIN_POINTS_SPEED',
  //商品满额发大礼包
  GoodsGainGiftBagActivityRule = 'GOODS_GAIN_GIFT',
  //注册发大礼包
  MemberRegisterGiftActivityRule = 'MEMBER_REGISTER_GIFT',
  //积分兑换商品
  PointsExchangeGoodsActivityRule = 'POINTS_EXCHANGE_GOODS',
  //导出券码发券
  ExportCouponCodeActivityRule = 'EXPORT_COUPON_CODE',
  //群发券
  ManualHandOutGiftActivityRule = 'MANUAL_COUPON',
    //积分抵现
    PointsChargeActivityRuleV2 = 'POINTS_CHARGE_V2'
}