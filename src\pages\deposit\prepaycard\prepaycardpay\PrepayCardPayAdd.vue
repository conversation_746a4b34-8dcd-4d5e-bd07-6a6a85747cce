<template>
  <div class="prepay-card-pay-add">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button
          :loading="loading"
          v-if="hasOptionPermission('/卡/卡管理/预付卡充值单', '单据维护')"
          @click="doSave"
          type="primary"
          >保存</el-button
        >
        <el-button
          :loading="loading"
          v-if="hasOptionPermission('/卡/卡管理/预付卡充值单', '单据审核')"
          @click="doSaveAndAudit"
          >保存并审核</el-button
        >
        <el-button
          v-if="hasOptionPermission('/卡/卡管理/预付卡充值单', '单据维护')"
          @click="doCancel"
          >取消</el-button
        >
      </template>
    </BreadCrume>
    <div class="content">
      <FormItem label="起始卡号" :required="true">
        <el-input
          @change="getCardInfo"
          class="width-298"
          v-model="params.startCardCode"
        ></el-input>
        <div v-if="isEmpty" class="red">卡号不能为空</div>
        <div v-else-if="!isExist && !isEmpty" class="red">卡号不存在</div>
        <div v-else-if="!isDeposit && !isEmpty && isExist" class="red">{{otherTip}}</div>
        <div v-else-if="isDeposit && !isEmpty && isExist && !isUsing" class="red">只能给使用中的储值卡进行充值</div>
      </FormItem>
      <FormItem label="结束卡号">
        <el-input
          @change="getEndCardInfo"
          class="width-298"
          v-model="params.endCardCode"
        ></el-input>
        <div class="red" v-if="!isEndExist && !isEndEmpty">卡号不存在</div>
        <div v-else-if="!isEndDeposit && !isEndEmpty && isEndExist" class="red">{{otherEndTip}}</div>
        <div v-else-if="isEndDeposit && !isEndEmpty && isEndExist && !isEndUsing" class="red">只能给使用中的储值卡进行充值</div>
      </FormItem>
      <FormItem label="卡数量">
        <div
          style="line-height: 36px; padding-left: 10px"
        >
          {{ cardCount }}
        </div>
      </FormItem>
      <FormItem label="卡类型">
        <div
            style="line-height: 36px; padding-left: 10px"
            v-if="params.accountType"
        >
          {{ params.accountType.name || '-' }}
        </div>
      </FormItem>
      <FormItem label="充值类型" style="margin-top: 10px" :required="true">
        <el-radio-group v-model="params.depositType" style="margin-top: 10px">
          <el-radio label="DEPOSIT">{{i18n('充值')}}</el-radio>
          <el-radio label="REFUND">{{i18n('充值退')}}</el-radio>
        </el-radio-group>
      </FormItem>
      <template v-if="params.depositType === 'DEPOSIT'">
        <FormItem :label="i18n('单卡充值金额')" style="margin-top: 10px" :required="true">
          <div class="inputs-area">
            {{i18n('实充')}} &emsp;<el-input
              :disabled="!isExist"
              v-model="params.occurAmount"
              class="short"
              @change="amountChange"
          ></el-input
          >&emsp;{{i18n('元')}} + {{i18n('返现')}} &emsp;<el-input
              :disabled="!isExist"
              v-model="params.occurGiftAmount"
              class="short"
              @change="giftAmountChange"
          ></el-input
          >&emsp; {{i18n('元')}} = {{i18n('总金额')}} &emsp;<el-input
              class="short"
              disabled
              :value="sumAmount"
          ></el-input
          >&emsp; {{i18n('元')}}
          </div>
        </FormItem>
        <FormItem :label="i18n('本金总金额')">
          <div
              style="line-height: 36px; padding-left: 10px"
          >
            {{ totalOccurAmount === 0 ? '0' : totalOccurAmount || '-' }}&emsp;{{i18n('元')}}
          </div>
        </FormItem>
        <FormItem :label="i18n('赠金总金额')">
          <div
              style="line-height: 36px; padding-left: 10px"
          >
            {{ totalGiftOccurAmount === 0 ? '0' : totalGiftOccurAmount || '-' }}&emsp;{{i18n('元')}}
          </div>
        </FormItem>
        <FormItem :label="i18n('优惠总金额')" style="margin-top: 10px">
          <div style="margin-left: 10px; margin-top: 3px">
            <el-input
              :disabled="!isExist"
              v-model="params.favAmount"
              class="short"
              @change="discountAmountChange"
          ></el-input
          >&emsp;{{i18n('元')}}
          </div>
        </FormItem>
        <FormItem :label="i18n('应付金额')">
          <div
              style="line-height: 36px; padding-left: 10px"
          >
            {{ dueAmountOrRefundAmount === 0 ? '0' : dueAmountOrRefundAmount || '-' }}&emsp;{{i18n('元')}}
          </div>
          <div style="margin-left: 20px">{{i18n('应付金额=本金总金额-优惠总金额')}}</div>
        </FormItem>
      </template>
      <template v-if="params.depositType === 'REFUND'">
        <FormItem :label="i18n('单卡充值退金额')" style="margin-top: 10px" :required="true">
          <div class="inputs-area">
            {{i18n('本金退')}} &emsp;<el-input
              :disabled="!isExist"
              v-model="params.occurAmount"
              class="short"
              @change="amountChange"
          ></el-input
          >&emsp;{{i18n('元')}} + {{i18n('赠金退')}} &emsp;<el-input
              :disabled="!isExist"
              v-model="params.occurGiftAmount"
              class="short"
              @change="giftAmountChange"
          ></el-input
          >&emsp; {{i18n('元')}} = {{i18n('总金额退')}} &emsp;<el-input
              class="short"
              disabled
              :value="sumAmount"
          ></el-input
          >&emsp; {{i18n('元')}}
          </div>
        </FormItem>
        <FormItem :label="i18n('本金退总金额')">
          <div
              style="line-height: 36px; padding-left: 10px"
          >
            {{ totalOccurAmount === 0 ? '0' : totalOccurAmount || '-' }}&emsp;{{i18n('元')}}
          </div>
        </FormItem>
        <FormItem :label="i18n('赠金退总金额')">
          <div
              style="line-height: 36px; padding-left: 10px"
          >
            {{ totalGiftOccurAmount === 0 ? '0' : totalGiftOccurAmount || '-' }}&emsp;{{i18n('元')}}
          </div>
        </FormItem>
        <FormItem :label="i18n('优惠退总金额')" style="margin-top: 10px">
          <div style="margin-left: 10px; margin-top: 3px">
            <el-input
              :disabled="!isExist"
              v-model="params.favAmount"
              class="short"
              @change="discountAmountChange"
          ></el-input
          >&emsp;{{i18n('元')}}
          </div>
        </FormItem>
        <FormItem :label="i18n('应退金额')">
          <div
              style="line-height: 36px; padding-left: 10px"
          >
            {{ dueAmountOrRefundAmount === 0 ? '0' : dueAmountOrRefundAmount || '-' }}&emsp;{{i18n('元')}}
          </div>
          <div style="margin-left: 20px">{{i18n('应退金额=本金退总金额-优惠退金额')}}</div>
        </FormItem>
      </template>
      <FormItem label="支付方式" style="margin-top: 20px" :required="true">
        <div class="add-payment">
          <el-button @click="addPaymentRow" ref="myButton" :disabled="!isExist" class="add-payment-row-button">{{i18n('添加支付方式')}}</el-button>
          <span class="add-payment-context">{{i18n('最多支持添加10个支付方式')}}</span>
          <el-table :data="params.payments" style="width: 500px" v-if="params.payments.length > 0">
            <el-table-column prop="paymentId" :label="i18n('支付方式')" width="180">
              <template slot-scope="scope">
                <el-select v-model="scope.row.paymentId" :placeholder="i18n('选择支付方式')" :disabled="!isExist" @change="handleSelectPayment(scope.row)">
                  <el-option
                      v-for="item in queryPaymentData"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id">
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="paymentAmount" :label="i18n('支付金额')" width="180">
              <template slot-scope="scope">
                <el-input v-model="scope.row.paymentAmount" :placeholder="i18n('输入金额')" :disabled="!isExist" @change="paymentAmountChange(scope)">
                  <template slot="append">{{ i18n('元') }}</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column :label="i18n('操作')" width="80">
              <template slot-scope="scope">
                <el-button type="text" @click="deletePaymentRow(scope.$index)">{{formatI18n('/公用/按钮','删除')}}</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </FormItem>
      <FormItem label="支付总金额">
        <div style="line-height: 36px; padding-left: 10px">
          {{ totalPayAmount === 0 ? '0' : totalPayAmount || '-' }}&emsp;{{i18n('元')}}
        </div>
      </FormItem>
      <FormItem label="客户" :required="clientRequired" style="margin-top: 10px">
        <SelectClient v-model="params.clientId" :disabled="!isExist" @change="$forceUpdate()" :hideAll="true" width="214px"
                      :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
        </SelectClient>
      </FormItem>
      <div class="inner-content">
        <FormItem label="发生组织" :required="true" :labelWidth="'70px !important'">
          <SelectStores v-model="params.occurredOrg" :appendAttr="getOrgAppendAttr" :disabled="!isExist" @change="$forceUpdate()" :isOnlyId="false" :hideAll="true" width="214px"
              :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
            </SelectStores>
        </FormItem>
        <FormItem label="说明" :labelWidth="'70px !important'">
          <el-input
            type="textarea"
            :disabled="!isExist"
            v-model="params.remark"
            class="width-298"
            :placeholder="i18n('/公用/表单校验/请输入不超过{0}个字符',['200'])"
            maxlength="200"
          ></el-input>
        </FormItem>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./PrepayCardPayAdd.ts">
</script>

<style lang="scss">
.prepay-card-pay-add {
  width: 100%;
  height: 100%;
  background-color: white;
  overflow: auto;
  .content {
    padding: 30px;
  }
  .width-78 {
    width: 78px;
  }
  .width-298 {
    width: 298px;
  }
  .el-textarea__inner {
    height: 100px;
  }
  .inner-content {
    width: 400px;
    background-color: rgba(249, 249, 249, 1);
    margin-left: 40px;
    height: auto;
    padding: 10px 0;
  }
  .el-checkbox {
    margin-right: 10px;
  }
  .weight {
    font-weight: 600;
    color: #515151;
  }
  .qf-form-item .qf-form-content {
    position: relative;
    margin-left: 105px !important;
  }
  .qf-form-item .qf-form-label {
    width: 110px !important;
  }
  .red {
    color: #f56c6c;
  }
  .short {
    width: 100px;
  }
  .inputs-area {
    margin-left: 20px;
    margin-bottom: 20px;
    padding-top: 3px;

  }
  .add-payment {
    .add-payment-row-button {
      margin-top: 5px;
      margin-left: 10px;
      border-radius: 20px;
      padding: 10px 20px;
      color: #409eff;
      background-color: white;
      border: 2px solid #409eff;
    }
    .add-payment-row-button:hover {
      background-color: #ecf5ff;
      color: #409eff;
    }
    .add-payment-context {
      margin-left: 10px;
      color: #909399;
    }
    .el-table::before {
      height: 0;
    }

  }
}
</style>