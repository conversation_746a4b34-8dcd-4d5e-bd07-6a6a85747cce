import {Component, Vue} from 'vue-property-decorator'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import GoodsScopeDtl from "cmp/goodsscope/GoodsScopeDtl.vue"
import BDepositUseLimitActivity from "model/deposit/store/BDepositUseLimitActivity";
import DepositUseLimitActivityApi from "http/prepay/applicableGoods/DepositUseLimitActivityApi";
import I18nPage from 'common/I18nDecorator';
import BrowserMgr from "mgr/BrowserMgr";

@Component({
  name: 'StoreValueApplicableGoodsDtl',
  components: {
    BreadCrume,
    GoodsScopeDtl,
  }
})
@I18nPage({
  prefix: ['/储值/会员储值/储值适用商品设置'],
})
export default class StoreValueApplicableGoodsDtl extends Vue {
  goodsMatchRuleMode: string = "barcode"
  data: BDepositUseLimitActivity = new BDepositUseLimitActivity()
  loading = false
  $refs: any
  panelArray: any = []
  created() {
    let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
		if (sysConfig && this.goodsMatchRuleMode) {
			this.goodsMatchRuleMode = sysConfig.goodsMatchRuleMode;
		}
    this.panelArray = [
      {
        name: this.formatI18n('/储值/会员储值', '储值适用商品设置'),
        url: ''
      }
    ]
    this.getDetail()
  }

  private getDetail() {
    this.loading = true
    DepositUseLimitActivityApi.byType().then((res: any) => {
      if (res.code === 2000) {
        this.data = res.data
        if (!this.data || !this.data.goodsRange) {
          this.$router.push({name: 'store-value-applicable-goods-add', query: { from: 'edit' }})
          return
        }
      }
    }).finally(() => {
      this.loading = false
    })
  }

  private doModify() {
    this.$router.push({name: 'store-value-applicable-goods-add', query: { from: 'edit' }})
  }
}