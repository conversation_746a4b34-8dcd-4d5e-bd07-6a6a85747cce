import ApiClient from 'http/ApiClient'
import BPrepayCardAnalysisFilter from 'model/analysis/BPrepayCardAnalysisFilter'
import BPrepayCardAnalysisSummaryReport from 'model/analysis/BPrepayCardAnalysisSummaryReport'
import Response from 'model/default/Response'

export default class CardSummaryAnalysisContorllerApi {
  /**
   * 汇总查询
   * 汇总查询
   * 
   */
  static query(body: BPrepayCardAnalysisFilter): Promise<Response<BPrepayCardAnalysisSummaryReport>> {
    return ApiClient.server().post(`/v1/analysis-report/summary/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
