<template>
  <div class="area-view">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button
          size="large"
          @click="$refs.uploadFileModal.show(marketingCenters,enableMultiMarketingCenter)"
          v-if="hasOptionPermission('/设置/资料/区域', '资料维护')"
        >{{formatI18n('/资料/门店/批量导入')}}</el-button>
      </template>
    </BreadCrume>
    <div class="current-page">
      <div style="margin: 20px;line-height: 40px" v-if="hasOptionPermission('/设置/资料/区域', '资料维护')">
        <el-row>
          <el-col :span="2" style="min-width: 100px">{{formatI18n('/资料/区域/区域代码')}}</el-col>
          <el-col :span="6">
            <el-input maxlength="20" v-model.trim="areaCode" style="width: 280px" />
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="2" style="min-width: 100px">{{formatI18n('/资料/区域/区域名称')}}</el-col>
          <el-col :span="6">
            <el-input maxlength="20" v-model.trim="areaName" style="width: 280px" />
          </el-col>
        </el-row>
        <el-row v-if="isMoreMarketing">
          <el-col :span="2" style="min-width: 100px">{{formatI18n('/资料/区域/所属营销中心')}}</el-col>
          <el-col :span="6">
            {{ '['+marketCenter+']'}}{{marketCenterName}}
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="2" style="min-width: 100px">&nbsp;</el-col>
          <el-col :span="18">
            <i class="el-icon-warning" />
            <span>{{formatI18n('/资料/区域/代码不允许与已有区域重复')}}。</span>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="2" style="min-width: 100px">&nbsp;</el-col>
          <el-col :span="4">
            <el-button class="btn-search" @click="add" type="primary">{{formatI18n('/资料/区域/添加')}}</el-button>
            <el-button class="btn-reset" type="normal" @click="clear">{{formatI18n('/资料/区域/清空')}}</el-button>
          </el-col>
        </el-row>
      </div>
      <ListWrapper style="overflow: initial">
        <template slot="list">
          <FloatBlock refClass="current-page" :top="95" style="padding: 5px;">
            <template slot="ctx">
              <el-row>
                <el-col :span="12" style="line-height: 36px">
                  <el-checkbox
                    :disabled="loading"
                    v-model="checkedAll"
                    style="padding-left: 10px;"
                    @change="checkedAllRow"
                  />
                  <i18n k="/资料/营销中心/已选择{0}个区域">
                    <template slot="0">
                      <span class="number-text">{{selected.length}}</span>
                    </template>
                  </i18n>&nbsp;&nbsp;
                </el-col>

                <el-col :span="12">
                  <div style="width: 490px;float: right;display: flex;align-items: center">
                    <span style="width: 80px">{{formatI18n('/资料/区域/区域状态')}}</span>
                    <el-select
                      style="width: 250px"
                      v-model="areaStateSelect"
                      @change="doSearch"
                    >
                      <el-option value="null" :label="formatI18n('/资料/区域/全部')">{{formatI18n('/资料/区域/全部')}}</el-option>
                      <el-option value="NORMAL" :label="formatI18n('/资料/区域/正常')">{{formatI18n('/资料/区域/正常')}}</el-option>
                      <el-option value="CANCEL" :label="formatI18n('/资料/区域/已作废')">{{formatI18n('/资料/区域/已作废')}}</el-option>
                    </el-select>&nbsp;&nbsp;&nbsp;
                    <el-input
                      :placeholder="formatI18n('/资料/门店/搜索区域代码\\/名称')"
                      @change="onSearch"
                      v-model="areaCodeOrName"
                      suffix-icon="el-icon-search"
                      style="width: 280px"
                    />
                  </div>
                </el-col>
              </el-row>
            </template>
          </FloatBlock>
          <el-table
            v-loading="loading"
            ref="areaTable"
            :data="areaData"
            style="width: 100%;margin-top: 10px;"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column fixed :label="formatI18n('/资料/区域/区域代码')" prop="zone.id" />
            <el-table-column fixed :label="formatI18n('/资料/区域/区域名称')" prop="zone.name" />
            <el-table-column fixed :label="formatI18n('/资料/区域/区域状态')" prop="marketingCenterName">
              <template slot-scope="scope">
                <div v-if="scope.row.state === 'CANCEL'">
                  {{formatI18n('/资料/区域/已作废')}}
                </div>
                <div v-if="scope.row.state === 'NORMAL'">
                  {{formatI18n('/资料/区域/正常')}}
                </div>
              </template>
            </el-table-column>
            <el-table-column fixed :label="formatI18n('/资料/区域/操作')" align='left' v-if="hasOptionPermission('/设置/资料/区域', '资料维护')">
              <template slot-scope="scope">
                <span
                  class="span-btn"
                  style="margin-right:8px"
                  @click="showModifyDialog(scope.row)"
                >{{formatI18n('/资料/区域/修改')}}</span>
                <span
                  class="span-btn"
                  v-if="scope.row.state !== 'CANCEL' "
                  @click="cancellationDialog(scope.row)"
                >{{formatI18n('/资料/区域/作废')}}</span>
              </template>
            </el-table-column>
          </el-table>
        </template>
        <!--{{formatI18n('/资料/门店/分页栏')}}-->
        <template slot="page">
          <el-pagination
            :current-page="page.currentPage"
            :page-size="page.pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :total="page.total"
            @current-change="onHandleCurrentChange"
            @size-change="onHandleSizeChange"
            background
            layout="total, prev, pager, next, sizes,  jumper"
          ></el-pagination>
        </template>
      </ListWrapper>
    </div>
    <el-dialog
      :title="isModification===true ? formatI18n('/资料/区域/修改') :formatI18n('/资料/区域/作废')"
      :visible.sync="updateDialog"
      class="store-dialog-center"
      width="30%"
    >
      <div v-if="isModification===false">
        <span>
          {{formatI18n('/资料/区域/作废以后，该区域下的门店将没有归属区域，此操作不可恢复，请谨慎操作')}}
        </span>
      </div>
      <div v-if="isModification===true" style="margin: 20px;">
        <el-row>
          <el-col :span="8" style="height: 50px; line-height: 30px">{{formatI18n('/资料/区域/区域代码')}}</el-col>
          <el-col :span="16">
            <el-input :disabled="true" v-model="updateIns.areaCode" />
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8" style="height: 50px; line-height: 30px">{{formatI18n('/资料/区域/区域名称')}}</el-col>
          <el-col :span="16">
            <el-input v-model="updateIns.areaName" />
          </el-col>
        </el-row>
        <el-row style="line-height:30px" v-if="isMoreMarketing">
          <el-col :span="8" style="height: 50px; line-height: 30px">{{formatI18n('/资料/区域/所属营销中心')}}</el-col>
          <el-col :span="16">
            {{ '['+marketCenter+']'}}{{marketCenterName}}
          </el-col>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="updateDialog = false">{{formatI18n('/资料/区域/取 消')}}</el-button>
        <el-button
          type="primary"
          @click="modify"
          :loading="modifyLoading"
        >{{formatI18n('/资料/区域/确 定')}}</el-button>
        <!-- <el-popover placement="bottom" width="160" v-model="visible" v-if="!isModification">
          <p>确认要将此区域作废吗？</p>
          <div style="text-align: right; margin: 0">
            <el-button size="mini" type="text" @click="visible = false">{{formatI18n('/资料/区域/取 消')}}</el-button>
            <el-button type="primary" size="mini" @click="modify">{{formatI18n('/资料/区域/确 定')}}</el-button>
          </div>
          <el-button type="primary" slot="reference">{{formatI18n('/资料/区域/确 定')}}</el-button>
        </el-popover> -->

      </span>
    </el-dialog>
    <UploadFileModal ref="uploadFileModal" @getList="getList"></UploadFileModal>
  </div>
</template>
<script lang="ts" src='./Area.ts'>
</script>
<style lang="scss" scope>
.area-view {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: hidden;
  .current-page {
    height: calc(100% - 77px);
    overflow: auto;
    padding: 10px;
  }
}
</style>