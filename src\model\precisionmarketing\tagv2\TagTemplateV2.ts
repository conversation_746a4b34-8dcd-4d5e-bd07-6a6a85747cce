import Channel from "model/common/Channel";
import { SyncChannelTypeEnum } from "model/common/SyncChannelTypeEnum";
import { TagTypeEnum } from "model/common/TagTypeEnum";

// 标签模板v2
export default class TagTemplateV2 {
  // 标签模板uuid
  uuid: Nullable<string> = null;
  // 标签名称
  name: Nullable<string> = null;
  // 分类id
  category: Nullable<string> = null;
  // 分类名称
  categoryName: Nullable<string> = null;
  // 说明
  remark: Nullable<string> = null;
  // 创建人
  creator: Nullable<string> = null;
  // 创建时间
  created: Nullable<Date> = null;
  // 最后修改人
  lastModifier: Nullable<string> = null;
  // 最后修改时间
  lastModified: Nullable<Date> = null;
  // 来源渠道
  channel: Nullable<Channel> = null;
  // 标签类型
  tagType: Nullable<TagTypeEnum> = null;
  // 同步渠道
  syncChannels: Channel[] = [];
  // ALL-全部渠道，PART-指定渠道 
  syncType: SyncChannelTypeEnum = SyncChannelTypeEnum.ALL
  // 标签覆盖人数
  coveredCount: Nullable<number> = null;
  // 标签覆盖率
  coveredPercentage: Nullable<number> = null;
  // 标签值
  tagValues: string[] = [];
}
