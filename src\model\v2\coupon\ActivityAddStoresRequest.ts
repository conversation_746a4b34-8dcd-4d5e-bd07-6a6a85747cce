/*
 * @Author: mazhengfa <EMAIL>
 * @Date: 2023-02-06 16:16:46
 * @LastEditors: mazhengfa <EMAIL>
 * @LastEditTime: 2023-02-08 14:32:23
 * @FilePath: \phoenix-web-ui\src\model\v2\coupon\ActivityAddStoresRequest.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import IdName from 'model/v2/common/IdName'
// import { State } from 'model/default/State'

// 活动追加门店请求
export default class ActivityAddStoresRequest {
  // 营销中心
  marketingCenter: Nullable<string> = null
  // 门店
  stores: IdName[] = []
  // 活动类型
  activityTypes: string[] = []
  // 活动状态
  activityStates?: string[] = []
  ActivityState: string[] = []
  // 例外活动
  excludeActivities: string[] = []
}