import MenuSetting from 'model/navigation/MenuSetting'
import { IconColorStyle } from 'model/navigation/IconColorStyle'
import { NavigationStyle } from 'model/navigation/NavigationStyle'
import { CmsConfigChannel } from 'model/template/CmsConfig'

export default class NavigationSetting {
  // 导航样式
  navigationType: Nullable<NavigationStyle> = null
  // 导航渠道 渠道枚举：weixinApp：微信小程序；aliApplet：支付宝小程序；h5：H5；
  navigationChannel: Nullable<CmsConfigChannel> = null
  // 图标配色
  iconColorType: Nullable<IconColorStyle> = null
  // 选中的颜色
  selectedColor: Nullable<string> = null
  // 未选中的颜色
  unselectedColor: Nullable<string> = null
  // 选中的文本颜色
  selectedFontColor: Nullable<string> = null
  // 选中的图标颜色
  selectedIconColor: Nullable<string> = null
  // 未选中的文本颜色
  unselectedFontColor: Nullable<string> = null
  // 未选中的图标颜色
  unselectedIconColor: Nullable<string> = null
  // 菜单数量
  menuQuantity: Nullable<number> = null
  // 菜单设置
  menuSettings: MenuSetting[] = []
}