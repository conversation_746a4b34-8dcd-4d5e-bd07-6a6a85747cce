<template>
  <el-dialog :before-close="doBeforeClose"
      append-to-body
      :close-on-click-modal="false"  :title="title" :visible.sync="dialogShow" class="selectGoods-dialog-view">
    <div style="text-align:right;">
      <el-button type="primary" @click="doAdd">{{ i18n('添加商品') }}</el-button>
      <el-button type="primary" @click="openUploadGood">{{ i18n('批量导入') }}</el-button>
    </div>
    <div style="margin-bottom:10px;">
      <span>
        <!-- 已选{{checked.filter(x => x).length}}行 -->
        {{ getSelecRowStr }}
      </span>
      <el-button @click="deleteBatch">{{ i18n('批量删除') }}</el-button>
    </div>
    <div style="max-height: 600px;overflow:auto;">
      <el-row class="sgdHeader">
        <el-col :span="1" class="sgdCell">
					<el-checkbox v-model="checkedAll" @change="changeAll" style="display:inline-block;max-height: calc(100% - 5px);height: 28px;"></el-checkbox>
        </el-col>
        <el-col :span="5" class="sgdCell">{{ i18n('商品条码') }}</el-col>
        <el-col :span="14" class="sgdCell">{{ i18n('用券记录方式') }}</el-col>
        <el-col :span="4" class="sgdCell">
          {{ i18n('操作') }}
        </el-col>
        <!-- <ei-col :span="1"></ei-col> -->
      </el-row>
      <div class="tableDataCon">
        <el-row v-if="!selectData.length">
          <el-col :span="24" class="noGoods">{{ i18n('未选择特殊商品') }}</el-col>
        </el-row>
        <template v-else>
          <el-form label-width="0" ref="specialGoodsForm">
            <el-row v-for="(item, index) in selectData" :key="index">
              <el-col :span="1" class="sgdCell">
                <el-checkbox @change="changeCheck" v-model="checked[index]" style="display:inline-block;max-height: calc(100% - 5px);height: 28px;"></el-checkbox>
              </el-col>
              <el-col :span="5" class="sgdCell">{{ item.barcode }}</el-col>
              <el-col :span="14" class="sgdCell">
                <el-form-item style="margin-bottom: 16px;margin-top: 12px;display: inline-block;vertical-align: top;"
                  :ref="'tr'+index"
                  :prop="index+''"
                  :rules="[
                    {
                      validator: trRule,
                      trigger: ['change', 'blur'],
                      type: 'array'
                    }
                  ]">
                  {{ i18n('最多') }}
                  <el-input size="mini" @blur="doPayWayByAmount(index)" style="width: 100px" v-model="item.price"></el-input>
                  {{ i18n('元') }} {{ i18n('算支付方式，剩余算优惠方式') }}
                </el-form-item>
              </el-col>
              <el-col :span="4" class="sgdCell">
                <el-button type="text" @click="deleteGood(index)">
                  {{ i18n('删除') }}
                </el-button>
              </el-col>
              <!-- <ei-col :span="1"></ei-col> -->
            </el-row>
          </el-form>
        </template>
      </div>
    </div>
    <div class="dialog-footer" slot="footer">
    <el-button @click="doModalClose('cancel')">{{formatI18n('/公用/按钮', '取消')}}</el-button>
    <el-button @click="doModalClose('confirm')" size="small" type="primary">{{formatI18n('/公用/按钮', '确定')}}</el-button>
    </div>
    <GoodsSelectorDialog ref="selectGoodsScopeDialog" @summit="doSubmitGoods"/>
    <ImportDialog
      ref="ImportDialogGoods"
      :importUrl="importUrl"
      :templateName="i18n('导入商品模板')"
      :templatePath="templatePath"
      :title="formatI18n('/公用/券模板', '导入')"
      @dialogClose="doImportDialogClose" @upload-success="doUploadSuccess">
    </ImportDialog>
  </el-dialog>
</template>
<script lang="ts" src="./selectGoodsDialog.ts"></script>
<style lang="scss" scoped>
::v-deep .el-dialog {
  min-width: 1000px;
}
.selectGoods-dialog-view {
  .sgdHeader {
    //margin-top: 10px;
    border-right: 1px solid #aaa;
    border-top: 1px solid #aaa;
  }
  .sgdCell {
    line-height: 30px;
    text-align: center;
    border-left: 1px solid #aaa;
    border-bottom: 1px solid #aaa;
  }
  .tableDataCon {
    border-right: 1px solid #aaa;
    max-height: 500px;
    //overflow: auto;
    overflow-y: overlay;
    //border-bottom: 1px solid #aaa;
    .sgdCell {
      //height: 72px;
      line-height: 72px;
    }
  }
  .noGoods {
    line-height: 38px;
    text-align: center;
    border-left: 1px solid #aaa;
    border-bottom: 1px solid #aaa;
  }
}
</style>