/*
 * @Author: 黎钰龙
 * @Date: 2025-05-13 09:58:31
 * @LastEditTime: 2025-05-13 10:12:28
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\card\oldCard\RemakingCardBillApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import BRemakingCardBill from 'model/card/writeCard/BRemakingCardBill'
import BRemakingCardBillFilter from 'model/card/writeCard/BRemakingCardBillFilter'
import RemakingCardRequest from 'model/card/writeCard/RemakingCardRequest'
import Response from 'model/default/Response'
import PrePayCard from 'model/prepay/card/PrePayCard'

export default class RemakingCardBillApi {
  /**
   * 查询已回收卡
   * 查询已回收卡。
   * 
   */
  static getRecover(cardCode: string): Promise<Response<PrePayCard[]>> {
    return ApiClient.server().get(`/v1/remaking-card-bill/getRecover`, {
      params: {
        cardCode: cardCode
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 制卡完成
   * 制卡完成。
   * 
   */
  static finish(billNumber: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/remaking-card-bill/finish/${billNumber}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 制卡单查询
   * 制卡单查询。
   * 
   */
  static query(body: BRemakingCardBillFilter): Promise<Response<BRemakingCardBill[]>> {
    return ApiClient.server().post(`/v1/remaking-card-bill/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 制卡
   * 制卡
   * 
   */
  static remakingCard(body: RemakingCardRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/remaking-card-bill/remakingCard`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建预付卡重制单
   * 新建预付卡重制单。
   * 
   */
  static save(body: BRemakingCardBill): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/remaking-card-bill/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

}