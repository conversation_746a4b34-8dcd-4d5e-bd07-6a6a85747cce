/*
 * @Author: 黎钰龙
 * @Date: 2023-10-13 14:42:45
 * @LastEditTime: 2023-10-13 14:43:09
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\prepay\card\MakeCardFailReasonFilter.ts
 * 记得注释
 */
export default class MakeCardFailReasonFilter {
  // 单号等于
  billNumberEquals: Nullable<string> = null
  // 卡号等于
  cardCodeEquals: Nullable<string> = null
  // 页码
  page: Nullable<number> = null
  // 页面大小，大于0
  pageSize: Nullable<number> = null
}