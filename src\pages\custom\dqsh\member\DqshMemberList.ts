import {Component, Vue} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import ListWrapper from 'cmp/list/ListWrapper.vue'
import MemberApi from 'http/member_v2/MemberApi'
import Dot from 'cmp/dot/Dot.vue'
import DownloadCenterDialog from 'pages/main/dowmload_center_dialog/DownloadCenterDialog.vue'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import MemberFilter from 'model/member_v2/member/MemberFilter'
import RSOrgFilter from 'model/common/RSOrgFilter'
import OrgApi from 'http/org/OrgApi'
import GradeApi from 'http/grade/grade/GradeApi'
import UploadFileModal from 'pages/custom/dqsh/member/cmp/UploadFileModal.vue'
import ExportConfirm from "cmp/exportconfirm/ExportConfirm";
import MarketingCenterApi from 'http/marketingcenter/MarketingCenterApi';
import RSMarketingCenterFilter from 'model/common/RSMarketingCenterFilter'
import IdName from 'model/common/IdName'
import MyQueryCmp from 'cmp/querycondition/MyQueryCmp'
import SelectStores from 'cmp/selectStores/SelectStores'

@Component({
  name: 'DqshMemberList',
  components: {
    MyQueryCmp,
    FormItem,
    ListWrapper,
    Dot,
    UploadFileModal,
    DownloadCenterDialog,
    BreadCrume,
    ExportConfirm,
    SelectStores
  }
})
export default class DqshMemberList extends Vue {
  uploadDialogShow = false
  exportDialogShow = false
  panelArray = [
    {
      name: '会员资料',
      url: ''
    }
  ]
  query: MemberFilter = new MemberFilter()
  registerDate: any = []
  createDate: any = []
  activeDate: any = []
  birthdayDate: any = []
  childDate: any = []
  tableHeight = 450
  fileDialogvisiable = false
  memberLevel: any = []
  rsMarketingCenterFilter: RSMarketingCenterFilter = new RSMarketingCenterFilter()
  fromMarketingCenter: any = null // 所属营销中心
  marketingCenterData: any = [] // 所有营销中心
  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  tableData: any[] = []
  dialogShow = false
  showTip: boolean = false

  created() {
    this.query.stateEquals = 'Using'
    this.getMemberList()
    this.getMemberLevel()
  }

  // 查询营销中心
  getMarketingCenter() {
    MarketingCenterApi.query(this.rsMarketingCenterFilter).then((res)=>{
      if(res.code === 2000) {
        const newMarketingCenterData:any = res.data
        for (let value in newMarketingCenterData) {
          this.marketingCenterData.push(newMarketingCenterData[value].marketingCenter as IdName)
        }
      }
    })
  }

  doBatchImport() {
    this.uploadDialogShow = true
  }

  doUploadSuccess() {
    this.uploadDialogShow = false
    this.fileDialogvisiable = true
    this.showTip = true
  }

  doDownloadDialogClose() {
    this.fileDialogvisiable = false
  }

  doDialogClose() {
    this.uploadDialogShow = false
  }

  doExportDialogClose() {
    this.exportDialogShow = false
  }

  doBatchExport() {
    this.exportDialogShow = true
  }

  doSummit(flag: any) {
    this.transParams()
    this.query.page = this.page.currentPage - 1
    this.query.pageSize = this.page.size
    MemberApi.exportMember(this.query).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.showTip = true
        this.fileDialogvisiable = true
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  doToggle(flag: boolean) {
    if (flag) {
      this.tableHeight = 195
    } else {
      this.tableHeight = 450
    }
  }

  doExport() {
    this.dialogShow = true
  }

  /**
   * 查询
   */
  onSearch() {
    this.page.currentPage = 1
    this.getMemberList()
  }

  formatter(row: any, column: any) {
    if (row.registerChannel && row.registerChannel.type) {
      if (row.registerChannel.type === 'third') {
        return '第三方'
      } else if (row.registerChannel.type === 'alipay') {
        return '支付宝'
      } else if (row.registerChannel.type === 'weixin') {
        return '微信'
      } else if (row.registerChannel.type === 'store') {
        return '门店注册'
      } else if (row.registerChannel.type === 'phoenix') {
        return 'CRM'
      } else {
        if (row.registerChannel && row.registerChannel.id) {
          return row.registerChannel.id
        } else {
          return '--'
        }
      }
    } else {
      return '--'
    }
  }

  /**
   * 重置
   */
  onReset() {
    // this.fromMarketingCenter = null
    this.registerDate = ''
    this.createDate = []
    this.activeDate = ''
    this.birthdayDate = ''
    this.childDate = ''
    this.query = new MemberFilter()
    this.getMemberList()
  }

  /**
   * 去详情
   */
  doGoDtl(row: any) {
    this.$router.push({name: 'dqsh-member-dtl', query: {id: row.memberId}})
  }

  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.getMemberList()
  }

  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.getMemberList()
  }

  /**
   * 表格排序条件
   */
  onSortChange({column, prop, order}: any) {
    // todo
  }

  doDateChange() {
    this.$forceUpdate()
  }

  getMemberList() {
    if (this.page.currentPage > 1000) {
      this.$message.warning('会员分页查询上限为10000条，若想获取完整结果，请使用批量导出功能 或 输入更多查询条件。')
      return
    }
    this.transParams()
    this.query.page = this.page.currentPage - 1
    this.query.pageSize = this.page.size
    MemberApi.query(this.query).then((resp: any) => {
      this.page.total = resp.total
      this.tableData = resp.data
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private getMemberLevel() {
    GradeApi.listGrade('').then((resp: any) => {
      this.memberLevel = resp.data
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  /**
   * 参数转换
   */
  private transParams() {
    if (!this.childDate) {
      this.childDate = []
    }
    if (this.createDate && this.createDate.length > 0) {
      this.query.createdGreaterOrEquals = new Date(this.createDate[0] ? this.createDate[0] + ' 00:00:00' : '')
      this.query.createdLess = new Date(this.createDate[1] ? this.createDate[1] + ' 23:59:59' : '')
    } else {
      this.query.createdGreaterOrEquals = null
      this.query.createdLess = null
    }
    if (this.activeDate && this.activeDate.length > 0) {
      this.query.activeTimeGreaterOrEquals = new Date(this.activeDate[0] ? this.activeDate[0] + ' 00:00:00' : '')
      this.query.activeTimeLess = new Date(this.activeDate[1] ? this.activeDate[1] + ' 23:59:59' : '')
    } else {
      this.query.activeTimeGreaterOrEquals = null
      this.query.activeTimeLess = null
    }
    if (this.registerDate && this.registerDate.length > 0) {
      this.query.registerTimeGreaterOrEquals = new Date(this.registerDate[0] ? this.registerDate[0] + ' 00:00:00' : '')
      this.query.registerTimeLess = new Date(this.registerDate[1] ? this.registerDate[1] + ' 23:59:59' : '')
    } else {
      this.query.registerTimeGreaterOrEquals = null
      this.query.registerTimeLess = null
    }

    if (this.birthdayDate && this.birthdayDate.length > 0) {
      this.query.birthMonthDayGreaterOrEquals = this.birthdayDate[0] ? this.birthdayDate[0] : null
      this.query.birthMonthDayLessOrEquals = this.birthdayDate[1] ? this.birthdayDate[1] : null
    } else {
      this.query.birthMonthDayGreaterOrEquals = null
      this.query.birthMonthDayLessOrEquals = null
    }
  }

}