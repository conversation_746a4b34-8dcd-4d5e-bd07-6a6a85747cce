import ApiClient from 'http/ApiClient'
import Account from 'model/account/initial/Account'
import AccountBody from 'model/account/initial/AccountBody'
import Response from 'model/common/Response'

export default class AccountInitialApi {
  /**
   * 查询多账户列表
   *
   */
  static list(): Promise<Response<AccountBody>> {
    return ApiClient.server().get(`/v1/account/initial/list`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 保存多账户配置信息
   *
   */
  static save(body: Account[]): Promise<Response<string[]>> {
    return ApiClient.server().post(`/v1/account/initial/save`, body, {}).then((res) => {
      return res.data
    })
  }

}
