import ApiClient from 'http/ApiClient'
import BenefitCardTemplate from 'model/equityCard/BenefitCardTemplate'
import BenefitCardTemplateFilter from 'model/equityCard/default/BenefitCardTemplateFilter'
import GiftInfoBenefitCard from 'model/equityCard/default/GiftInfoBenefitCard'
import Response from 'model/equityCard/default/Response'

export default class BenefitCardTemplateApi {
  /**
   * 新建权益券模板
   * 新建权益券模板
   * 
   */
  static create(body: BenefitCardTemplate): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/benefit_card_template/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 权益卡禁用
   * 权益卡禁用
   * 
   */
  static disable(uuid: string, version: number): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/benefit_card_template/disable`, {}, {
      params: {
        uuid: uuid,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 权益卡启用
   * 权益卡启用
   * 
   */
  static enable(uuid: string, version: number): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/benefit_card_template/enable`, {}, {
      params: {
        uuid: uuid,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据uuid获取模版
   * 根据uuid获取模版
   * 
   */
  static get(uuid: string): Promise<Response<BenefitCardTemplate>> {
    return ApiClient.server().get(`/v1/benefit_card_template/get`, {
      params: {
        uuid: uuid
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改权益券模板
   * 修改权益券模板
   * 
   */
  static modify(body: BenefitCardTemplate): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/benefit_card_template/modify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询卡模板
   * 分页查询卡模板
   * 
   */
  static query(body: BenefitCardTemplateFilter): Promise<Response<BenefitCardTemplate[]>> {
    return ApiClient.server().post(`/v1/benefit_card_template/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

    /**
   * 赠礼赠送权益卡列表展示查询
   * 赠礼赠送权益卡列表展示查询
   * 
   */
    static queryGiftInfoBenefitCard(body: BenefitCardTemplateFilter): Promise<Response<GiftInfoBenefitCard[]>> {
      return ApiClient.server().post(`/v1/benefit_card_template/queryGiftInfoBenefitCard`, body, {
      }).then((res) => {
        return res.data
      })
    }
}
