import Channel from 'model/common/Channel'
import IdName from 'model/common/IdName'
import MutableNsid from 'model/common/MutableNsid'

export default class PrepayAccountTransaction {
  //
  transNo: Nullable<string> = null
  //
  category: Nullable<string> = null
  //
  owner: Nullable<MutableNsid> = null
  //
  occurredOrg: Nullable<IdName> = null
  //
  channel: Nullable<Channel> = null
  //
  occurredTime: Nullable<Date> = null
  //
  originalAmount: Nullable<number> = null
  //
  amount: Nullable<number> = null
  //
  originalGiftAmount: Nullable<number> = null
  //
  giftAmount: Nullable<number> = null
  //
  totalAmount: Nullable<number> = null
  //
  offsetAmount: Nullable<number> = null
  //
  offsetGiftAmount: Nullable<number> = null
}