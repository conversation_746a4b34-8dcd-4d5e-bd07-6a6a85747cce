/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-05-16 10:37:44
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\common\ActivityStateCountResult.ts
 * 记得注释
 */
export default class ActivityStateCountResult {
  // 未审核数量
  initail: Nullable<number> = 0
  //已驳回
  platformAuditFail: Nullable<number> = 0
  //审核中
  platformAuditing: Nullable<number> = 0
  // 未开始数量
  unstart: Nullable<number> = 0
  // 进行中数量
  processing: Nullable<number> = 0
  // 已结束数量
  stoped: Nullable<number> = 0
  // 总数
  sum: Nullable<number> = 0
  // 暂停中
  suspend: Nullable<number> = 0
}
