<template>
    <div class="home-view">
        <div style="text-align: right;margin-top: 20px;">
            <el-radio-group size="small" v-model="activeTime" @change="doActiveChange">
            <el-radio-button :label="formatI18n('/公用/日期', '昨日')"></el-radio-button>
            <el-radio-button :label="formatI18n('/公用/日期', '本周')" ></el-radio-button>
            <el-radio-button :label="formatI18n('/公用/日期', '本月')"></el-radio-button>
        </el-radio-group>
        </div>
        <div class="wrap1">
            <div class="title">{{formatI18n('/会员/会员首页', '会员增长概况')}}
              <span v-if="isMoreMarketingCenter" style="font-size:13px">{{formatI18n('/会员/会员首页', '（只统计当前营销中心的数据）')}}</span>
              <el-button v-if="hasOptionPermission('/数据/分析/会员增长分析', '数据查看')" @click="doCheckDtl('member')" type="text"><img alt="" src="~assets/image/home/<USER>" style="vertical-align: bottom">{{formatI18n('/会员/会员首页', '查看详情')}}</el-button></div>
            <div class="member-flex">
                <div class="flex-item" v-loading="loading1">
                    <div class="desc">{{getActiveTime}}</div>
                    <div class="number" v-if="homeInfo && homeInfo.increaseStats && (homeInfo.increaseStats.newMemberCount || homeInfo.increaseStats.newMemberCount === 0)">{{homeInfo.increaseStats.newMemberCount | fmtThumbNumber}}</div>
                    <div class="subDesc1"><img alt="" class="img" src="~assets/image/home/<USER>">&nbsp;{{formatI18n('/会员/会员首页', '新增手机号会员（人）：')}}<span class="value" v-if="homeInfo && homeInfo.increaseStats && (homeInfo.increaseStats.newMobileMemberCount || homeInfo.increaseStats.newMobileMemberCount === 0)">{{homeInfo.increaseStats.newMobileMemberCount ? homeInfo.increaseStats.newMobileMemberCount : 0}}</span></div>
                    <div class="subDesc2"><img alt="" class="img" src="~assets/image/home/<USER>">&nbsp;{{formatI18n('/会员/会员首页', '新增手机号会员占比：')}}<span class="progress-wrap"><el-progress :percentage="Number.parseFloat(mobileMemberRate)"></el-progress></span></div>
                </div>
                <div class="flex-item" v-loading="loading3">
                    <div class="desc">{{getOtherActiveTime}}</div>
                    <div class="number" v-if="homeInfo && homeInfo.increaseStats && (homeInfo.increaseStats.consumeCount || homeInfo.increaseStats.consumeCount === 0)">{{homeInfo.increaseStats.consumeCount | fmtThumbNumber}}</div>
                    <div class="subDesc1"><img alt="" class="img" src="~assets/image/home/<USER>">&nbsp;{{formatI18n('/会员/会员首页', '首次消费会员（人）：')}}<span class="value" v-if="homeInfo && homeInfo.increaseStats && (homeInfo.increaseStats.consumeFirstCount || homeInfo.increaseStats.consumeFirstCount === 0)">{{homeInfo.increaseStats.consumeFirstCount ? homeInfo.increaseStats.consumeFirstCount : 0}}</span></div>
                    <div class="subDesc2"><img alt="" class="img" src="~assets/image/home/<USER>">&nbsp;{{formatI18n('/会员/会员首页', '首次消费会员占比：')}}<span class="progress-wrap"><el-progress :percentage="Number.parseFloat(consumeFirstRate)"></el-progress></span></div>
                </div>
            </div>
        </div>
        <div class="wrap1">
            <div class="title">{{formatI18n('/会员/会员首页', '会员交易概况')}}
              <span v-if="isMoreMarketingCenter" style="font-size:13px">{{formatI18n('/会员/会员首页', '（只统计当前营销中心的数据）')}}</span>
              <el-button v-if="hasOptionPermission('/数据/分析/会员交易分析', '数据查看')" @click="doCheckDtl('tran')" type="text"><img alt="" src="~assets/image/home/<USER>" style="vertical-align: bottom">{{formatI18n('/会员/会员首页', '查看详情')}}</el-button></div>
            <div class="member-flex">
                  <div class="flex-item" v-loading="loading2">
                    <div class="desc">{{formatI18n('/会员/会员首页', '会员交易额（元）')}}</div>
                    <div class="number" v-if="homeInfo && homeInfo.tradeStats && (homeInfo.tradeStats.memberTradeAmount || !homeInfo.tradeStats.memberTradeAmount)">{{homeInfo.tradeStats.memberTradeAmount | fmtThumb}}</div>
                    <div class="subDesc1"><img alt="" class="img" src="~assets/image/home/<USER>">&nbsp;{{formatI18n('/会员/会员首页', '交易总额（元）：')}}
                      <span class="value"
                            v-if="homeInfo && homeInfo.tradeStats && (homeInfo.tradeStats.tradeAmount || homeInfo.tradeStats.tradeAmount === 0)">
                            {{homeInfo.tradeStats.tradeAmount | fmtThumb}}
                        </span>
                      <span v-else style="font-weight: bold;color: black">0.00</span>
                    </div>
                    <div class="subDesc2"><img alt="" class="img" src="~assets/image/home/<USER>">&nbsp;{{formatI18n('/会员/会员首页', '会员交易总额占比：')}}<span class="progress-wrap"><el-progress :percentage="Number.parseFloat(tradeAmountRate)" color="#FC0049"></el-progress></span></div>
                  </div>
                  <div class="flex-item" v-loading="loading2">
                    <div class="desc">{{formatI18n('/会员/会员首页', '会员交易数（笔）')}}</div>
                    <div class="number" v-if="homeInfo && homeInfo.tradeStats && (homeInfo.tradeStats.memberTradeCount || homeInfo.tradeStats.memberTradeCount === 0)">{{homeInfo.tradeStats.memberTradeCount | fmtThumbNumber}}</div>
                    <div class="subDesc1"><img alt="" class="img" src="~assets/image/home/<USER>">&nbsp;{{formatI18n('/会员/会员首页', '交易总数（笔）：')}}<span class="value" v-if="homeInfo && homeInfo.tradeStats && (homeInfo.tradeStats.tradeCount || homeInfo.tradeStats.tradeCount === 0)">{{homeInfo.tradeStats.tradeCount | fmtThumbNumber}}</span></div>
                    <div class="subDesc2"><img alt="" class="img" src="~assets/image/home/<USER>">&nbsp;{{formatI18n('/会员/会员首页', '会员交易数占比：')}}<span class="progress-wrap"><el-progress :percentage="Number.parseFloat(tradeCountRate)" color="#0CC66D"></el-progress></span></div>
                  </div>
                  <div class="flex-item" v-loading="loading2">
                    <div class="desc">{{formatI18n('/会员/会员首页', '会员客单价（元）')}}</div>
                    <div class="number" v-if="homeInfo && homeInfo.tradeStats && (homeInfo.tradeStats.memberPerPrice || homeInfo.tradeStats.memberPerPrice === 0)">{{homeInfo.tradeStats.memberPerPrice | fmtThumb}}</div>
                    <div class="subDesc1"><img alt="" class="img" src="~assets/image/home/<USER>">&nbsp;{{formatI18n('/会员/会员首页', '客单价（元）：')}}<span class="value" v-if="homeInfo && homeInfo.tradeStats && (homeInfo.tradeStats.perPrice || homeInfo.tradeStats.perPrice === 0)">{{homeInfo.tradeStats.perPrice | fmtThumb}}</span></div>
                  </div>
            </div>
          <div class="member-flex">
            <div class="flex-item" v-loading="loading2">
              <div class="desc">{{formatI18n('/会员/会员首页', '会员客单件（件）')}}</div>
              <div class="number" v-if="homeInfo && homeInfo.tradeStats && (homeInfo.tradeStats.memberPerQty || homeInfo.tradeStats.memberPerQty === 0)">{{homeInfo.tradeStats.memberPerQty | fmtThumb}}</div>
              <div class="subDesc1"><img alt="" class="img" src="~assets/image/home/<USER>">&nbsp;{{formatI18n('/会员/会员首页', '客单件（件）：')}}<span class="value" v-if="homeInfo && homeInfo.tradeStats && (homeInfo.tradeStats.perQty || homeInfo.tradeStats.perQty === 0)">{{homeInfo.tradeStats.perQty | fmtThumb}}</span></div>
            </div>
            <div class="flex-item" v-loading="loading2">
              <div class="desc">{{formatI18n('/会员/会员首页', '会员件单价（元）')}}</div>
              <div class="number" v-if="homeInfo && homeInfo.tradeStats && (homeInfo.tradeStats.memberPerQtyPrice || homeInfo.tradeStats.memberPerQtyPrice === 0)">{{homeInfo.tradeStats.memberPerQtyPrice | fmtThumb}}</div>
              <div class="subDesc1"><img alt="" class="img" src="~assets/image/home/<USER>">&nbsp;{{formatI18n('/会员/会员首页', '件单价（元）：')}}<span class="value" v-if="homeInfo && homeInfo.tradeStats && (homeInfo.tradeStats.perQtyPrice || homeInfo.tradeStats.perQtyPrice === 0)">{{homeInfo.tradeStats.perQtyPrice | fmtThumb}}</span></div>
            </div>
          </div>
        </div>
    </div>
</template>

<script lang="ts" src="./Home.ts">
</script>

<style lang="scss">
.home-view{
    width: 100%;
    height: 100%;
    overflow: auto;
    .wrap1{
        margin-top: 21px;
    }
    .title{
        height:22px;
        font-size:16px;
        font-weight:500;
        color:rgba(90,95,102,1);
        line-height:22px;
        margin-bottom: 13px;
        margin-right: 10px;
    }
    .member-flex{
        display: flex;
        .flex-item{
            width: 372px;
            background-color: white;
            margin: 8px;
            height: 200px;
            border-radius: 10px;
            &:first-child{
                margin-left: 0px;
            }
            &:last-child{
                margin-right: 0px;
            }
            .img{
                vertical-align: bottom;
            }
            .desc{
                font-size:14px;
                font-weight:500;
                color:rgba(90,95,102,1);
                margin-left: 20px;
                margin-top: 28px;
            }
            .number{
                font-size:32px;
                font-weight:bold;
                color:rgba(36,39,43,1);
                margin-left: 20px;
                margin-top: 16px;
            }
            .subDesc1{
                font-size:12px;
                font-weight:400;
                color:rgba(161,166,174,1);
                margin-left: 20px;
                margin-top: 24px;
                .value{
                    font-size:14px;
                    font-weight:bold;
                    color:rgba(90,95,102,1);
                }
            }
            .subDesc2{
                font-size:12px;
                font-weight:400;
                color:rgba(161,166,174,1);
                margin-left: 20px;
                margin-top: 12px;
                .value{
                    font-size:14px;
                    font-weight:bold;
                    color:rgba(90,95,102,1);
                }
            }
            .progress-wrap{
                display: inline-block;
                width: 175px;
            }
        }
    }
    .el-progress{
        display: flex;
    }
    .el-progress-bar{
        padding-top: 6px;
        width: 90% !important;
    }
}
</style>