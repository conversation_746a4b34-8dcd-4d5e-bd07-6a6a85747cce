/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2023-06-29 16:13:52
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\benefit\couponinit\CouponTemplateDtl.ts
 * 记得注释
 */
import { Component, Vue } from 'vue-property-decorator'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import FormItem from 'cmp/formitem/FormItem.vue'
import CouponItem from 'model/common/CouponItem'
import ActiveAddCouponDtl from 'cmp/activeAddcoupondtl/ActiveAddCouponDtl.vue'
import CouponTemplateApi from 'http/coupon/template/CouponTemplateApi'
import CouponInfo from 'model/common/CouponInfo'
import I18nPage from "common/I18nDecorator";
import xss from 'xss'
//@ts-ignore
import CouponTemplateDtlSection from 'cmp/couponTemplateDtlSection/CouponTemplateDtlSection'

@Component({
  name: 'CouponTemplateDtl',
  components: {
    BreadCrume,
    FormItem,
    ActiveAddCouponDtl,
    CouponTemplateDtlSection
  }
})
@I18nPage({
  prefix: [
    "/公用/券模板",
    '/公用/过滤器',
    '/权益/券/券模板',
    '/公用/券模板/商品折扣券',
    '/公用/券模板/商品折扣券/折扣力度',
    '/营销/券礼包活动/核销第三方券',
    '/营销/券礼包活动/券查询/券状态下拉选项',
    '/权益/券/券模板/券模板状态下拉选项',
    '/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡',
    '/会员/等级/等级管理/点击付费等级tab页/未初始化状态下/点击立即开始付费等级初始化/表格/付费等级套餐/套餐定价'
  ],
  auto: true
})
export default class CouponTemplateDtl extends Vue {
  activitiId = ''
  child: CouponItem = new CouponItem()
  dtl: CouponInfo = new CouponInfo()
  loaded = false
  state = 1 // length = 1 : 1  > 1 : 2
  lastEffected: boolean = false
  panelArray: any = [
    {
      name: '',
      url: 'coupon-template-list'
    },
    {
      name: '',
      url: ''
    }
  ]
  xss: Function = xss

  created() {
    this.panelArray[0].name = this.formatI18n('/权益/券/券模板/券模板')
    this.panelArray[1].name = this.formatI18n('/权益/券/券模板/详情界面/面包屑/券模板详情')
    this.activitiId = this.$route.query.id as string
    this.lastEffected = this.$route.query.lastEffected == 'true'  //如果传true，表示查询的是“已审核”的最新券模板，传false或不传，则查询最新券模板
    this.getDtl()
  }

  doEdit() {
    this.$router.push({ name: 'coupon-template-add', query: { id: this.$route.query.id, from: 'edit' } })
  }

  doCopy() {
    this.$router.push({ name: 'coupon-template-add', query: { id: this.$route.query.id, from: 'copy' } })
  }
  tableConfigTicket() {
    let channelType = ''
    let outerRelations = JSON.parse(JSON.stringify(this.dtl.outerRelations)) || []
    if (outerRelations.length > 1) {
      this.state = 2
      let newArr = outerRelations.map((item: any) => {
        item.channel['typeName'] = ''
        if (item.channel.type == 'weimob') {
          item.channel.typeName = '微盟'
        } else if (item.channel.type == 'rex') {
          item.channel.typeName = 'REX'
        } else {
          item.channel.typeName = '微信'
        }
        return item.channel.typeName + '-' + item.channel.id + '-' + item.outerNumber
      })
      return newArr
    } else if (outerRelations.length == 1) {
      this.state = 1
      let newOuterRelations = outerRelations[0]
      if (newOuterRelations.channel!.type === 'weimob') {
        channelType = '微盟'
      } else if (newOuterRelations.channel.type == 'rex') {
        channelType = 'REX'
      } else {
        channelType = '微信'
      }
      return channelType + '-' + newOuterRelations.channel!.id + '-' + newOuterRelations.outerNumber
    }
  }

  private getDtl() {
    this.loaded = false
    CouponTemplateApi.detail(this.$route.query.id as string, this.lastEffected).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.dtl = resp.data
        console.log('dtl------>', resp.data)
        this.child.coupons = this.dtl
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    }).finally(() => {
      this.loaded = true
    })
  }

  get getSpecialSetup() {
    let str = ''
    if (this.dtl && this.dtl.useThreshold && this.dtl.specialPriceCouponAttribute) {
      str = `${this.i18n("订单满")} 
          ${this.dtl.useThreshold.threshold} 
          ${this.i18n("元")}，
          ${this.i18n("用券商品第")} 
          ${this.dtl.specialPriceCouponAttribute.favSegment} 
          ${this.i18n("件享")} 
          ${this.dtl.specialPriceCouponAttribute.specialPrice} 
          ${this.i18n("元购")}
          `
    }
    return str
  }
}