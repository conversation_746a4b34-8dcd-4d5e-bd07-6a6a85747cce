import Car from 'model/member_v2/member/Car'
import Channel from 'model/common/Channel'
import IdName from 'model/common/IdName'
import MemberIdent from 'model/member_v2/common/MemberIdent'
import TagOption from 'model/member_v2/tag/TagOption'

export default class MemberDetail extends MemberIdent {
    // 邀请人
    referee: Nullable<MemberIdent> = null
    // 最后修改人
    modifier: Nullable<string> = null
    // 最后修改时间
    modifed: Nullable<Date> = null
    // 名
    name: Nullable<string> = null
    // 姓
    lastName: Nullable<string> = null
    // 等级代码
    gradeCode: Nullable<string> = null
    // 等级名称
    gradeName: Nullable<string> = null
    // 等级有效期
    gradeValidate: Nullable<Date> = null
    // 状态 取值：Using-使用中；Blocked-已冻结；Unactivated-未激活；Canceled-已注销
    state: Nullable<string> = null
    // 会员到效日期
    expiredDate: Nullable<Date> = null
    // 归属门店
    ownStore: Nullable<IdName> = null
    // 邀请人员工
    referredEmployee: Nullable<IdName> = null
    // 注册日期
    registerTime: Nullable<Date> = null
    // 激活时间
    activateTime: Nullable<Date> = null
    // 系统创建时间
    created: Nullable<Date> = null
    //  注册渠道，渠道类型参见 {@link MemberConstants},允许第三方自定义
    registerChannel: Nullable<Channel> = null
    // 注册方式，参见 {@link MemberConstants},允许第三方自定义
    registerScene: Nullable<string> = null
    // 呢称
    nickName: Nullable<string> = null
    // 性别
    gender: Nullable<string> = null
    // 生日
    birthday: Nullable<Date> = null
    // 年龄
    age: Nullable<number> = null
    // 身份证
    idCard: Nullable<string> = null
    // 学历
    education: Nullable<string> = null
    // 行业
    industry: Nullable<string> = null
    // 年收入
    annualIncome: Nullable<string> = null
    // 爱好
    hobbies: Nullable<string> = null
    // 备用手机号
    spareMobile: Nullable<string> = null
    // 邮件
    email: Nullable<string> = null
    // 地址
    address: Nullable<string> = null
    // 生活区域
    area: Nullable<string> = null
    // 预付卡数（张）
    cardCount: Nullable<number> = null
    // 可用券数（张）
    couponCount: Nullable<number> = null
    // 积分）
    points: Nullable<number> = null
    // 储值余额（元）
    balance: Nullable<number> = null
    // 单账户下储值账户uuid
    balanceAccountUid: Nullable<string> = null
    // 成长值
    growthValue: Nullable<number> = null
    // 累计消费(笔)
    consumeQty: Nullable<number> = null
    // 累计消费
    totalConsume: Nullable<number> = null
    // 客单价(元)
    avgAmount: Nullable<number> = null
    // 最近一次消费
    lastConsumeDate: Nullable<Date> = null
    // 最近一次距今天数
    lastConsumeDay: Nullable<number> = null
    // 原会员手机号修改时必传
    oldMobile: string = ''
    // 变更说明
    remark: Nullable<string> = null
    // 图片
    image: Nullable<string> = null
    // 标签信息
    tags: TagOption[] = []
    // 车辆信息
    cars: Car[] = []
    // 省
    province: Nullable<IdName> = null
    // 市
    city: Nullable<IdName> = null
    // 街道
    street: Nullable<IdName> = null
    // 区
    district: Nullable<IdName> = null
    // 邮箱是否已校验
    emailChecked: Nullable<Boolean> = null
    // 邮箱是否已校验
    mobileChecked: Nullable<Boolean> = null
    // 归属导购
    guider: Nullable<IdName> = null
    // 扩展信息，包含自定义字段
    extObj: Nullable<string> = null
}