import ApiClient from 'http/ApiClient'
import MemberOperationBill from 'model/member/MemberOperationBill'
import MemberOperationBillFilter from 'model/member/MemberOperationBillFilter'
import MemberOperationBillLine from 'model/member/MemberOperationBillLine'
import OperateMemberInfo from 'model/member/OperateMemberInfo'
import Response from 'model/common/Response'
import MemberOperationBillStats from 'model/member/MemberOperationBillStats'

export default class MemberOperationBillApi {
  /**
   * 审核会员操作单
   * 审核会员操作单。
   * 
   */
  static audit(billNumber: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/custom/member-Operation-Bill/audit/${billNumber}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量审核会员操作单
   * 批量审核会员操作单。
   * 
   */
  static batchAudit(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/custom/member-Operation-Bill/batch/audit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除会员操作单
   * 删除会员操作单。
   * 
   */
  static batchRemove(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/custom/member-Operation-Bill/batch/remove`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取会员操作单详情
   * 获取会员操作单详情。
   * 
   */
  static get(billNumber: string): Promise<Response<MemberOperationBill>> {
    return ApiClient.server().get(`/v1/custom/member-Operation-Bill/get/${billNumber}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询会员账户和券信息
   * 查询会员账户和券信息。
   * 
   */
  static getMemberAccount(identCode: string): Promise<Response<OperateMemberInfo>> {
    return ApiClient.server().get(`/v1/custom/member-Operation-Bill/getMemberAccount/${identCode}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量导入会员操作单
   * 批量导入会员操作单。
   * 
   */
  static importExcel(body: any): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/custom/member-Operation-Bill/importExcel`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询会员操作单
   * 分页查询会员操作单。
   * 
   */
  static query(body: MemberOperationBillFilter): Promise<Response<MemberOperationBill[]>> {
    return ApiClient.server().post(`/v1/custom/member-Operation-Bill/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取会员操作单明细详情
   * 获取会员操作单明细详情
   * 
   */
  static queryDetail(billNumber: string, page: number, pageSize: number): Promise<Response<MemberOperationBillLine[]>> {
    return ApiClient.server().get(`/v1/custom/member-Operation-Bill/queryDetail/${billNumber}`, {
      params: {
        page: page,
        pageSize: pageSize
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建会员操作单
   * 新建会员操作单。
   * 
   */
  static save(body: MemberOperationBill): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/custom/member-Operation-Bill/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建并审核会员操作单
   * 新建并审核会员操作单。
   * 
   */
  static saveAndAudit(body: MemberOperationBill): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/custom/member-Operation-Bill/saveAndAudit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改会员操作单
   * 修改会员操作单。
   * 
   */
  static saveModify(body: MemberOperationBill): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/custom/member-Operation-Bill/saveModify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取汇总
   * 
   */
  static getTotal(body: MemberOperationBillFilter): Promise<Response<MemberOperationBillStats>> {
    return ApiClient.server().post(`/v1/custom/member-Operation-Bill/stats`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
