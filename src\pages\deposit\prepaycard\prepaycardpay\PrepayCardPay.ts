import {Component, Vue} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import ListWrapper from 'cmp/list/ListWrapper.vue'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import ImportDialog from 'cmp/importdialog/ImportDialog.vue'
import I18nPage from "common/I18nDecorator"
import DownloadCenterDialog from 'pages/main/dowmload_center_dialog/DownloadCenterDialog.vue'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import RSOrg from "model/common/RSOrg";
import RSOrgFilter from "model/common/RSOrgFilter";
import OrgApi from "http/org/OrgApi";
import SysConfigApi from "http/config/SysConfigApi";
import CardDepositBillApi from 'http/card/depositbill/CardDepositBillApi'
import CardDepositBillFilter from 'model/card/depositbill/CardDepositBillFilter'
import CardDepositBill from 'model/card/depositbill/CardDepositBill'
import PrepayCardImportDialog from './BatchImportDialog/PrepayCardImportDialog'

@Component({
  name: 'PrepayCardPay',
  components: {
    FormItem,
    ListWrapper,
    SubHeader,
    ImportDialog,
    DownloadCenterDialog,
    BreadCrume,
    PrepayCardImportDialog
  }
})
@I18nPage({
  prefix: [
    '/储值/预付卡/预付卡调整单/列表页面',
    '/营销/券礼包活动/券礼包活动',
    '/公用/活动/提示信息',
    '/公用/活动/状态',
    '/公用/按钮',
    '/储值/预付卡/预付卡充值单'
  ],
})
export default class PrepayCardPay extends Vue {
  fileDialogvisiable = false
  i18n: (str: string, params?: string[]) => string
  query: CardDepositBillFilter = new CardDepositBillFilter()
  createdDate: any = []
  lastModifyDate: any = []
  selectedArr: any[] = []
  activeName = 'first'
  switchFlag = false
  $refs: any
  selectAll = ''
  types: any = []
  total = {
    all: 0,
    initial: 0,
    audit: 0
  }
  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  tableData: CardDepositBill[] = []
  panelArray: any = []
  showOrg = false // 控制模态框的展示
  clientRequired: boolean = false  // 是否开启客户必填

  get getAllCount() {
    return `${this.i18n('全部')}(${this.total.all})`
  }
  get getNoAudit() {
    return `${this.i18n('未审核')}(${this.total.initial})`
  }
  get getAudit() {
    return `${this.i18n('已审核')}(${this.total.audit})`
  }
  created() {
    this.panelArray = [
      {
        name: this.i18n('预付卡充值单'),
        url: ''
      }
    ]
    this.getState()
    this.getPrepayCardList()
    this.getConfig()
    this.getCardDepositConfig()
  }
  doEdit() {
    // todo
  }
  doSelectAll() {
    if (this.selectAll) {
      for (let row of this.tableData) {
        this.$refs.table.toggleRowSelection(row, true)
      }
    } else {
      this.$refs.table.clearSelection();
    }
  }
  doDownloadDialogClose() {
    this.fileDialogvisiable = false
  }
  doUploadSuccess() {
    this.fileDialogvisiable = true
    this.getPrepayCardList()
  }
  // 批量导入
  doBatchImport() {
    this.$refs.prepayCardImportDialog.open()
  }
  doStoreValueAdd() {
    this.$router.push({ name: 'prepay-card-pay-add', query: { from: 'add'}})
  }
  doStoreValueReason() {
    this.$router.push({name: 'prepay-card-adjust-reason'})
  }
  doBatchDelete() {
    if (this.selectedArr.length <= 0) {
      this.$message.warning(this.i18n('请先勾选要删除的单据'))
      return
    }
    this.$confirm(this.i18n('是否批量删除这些单据?'), this.i18n('批量删除'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消')
    }).then(() => {
      this.submitBatchDelete()
    })
  }
  doBatchAudit() {
    if (this.selectedArr.length <= 0) {
      this.$message.warning(this.i18n('请先勾选要审核的单据'))
      return
    }
    this.$confirm(this.i18n('是否批量审核这些单据?'), this.i18n('批量审核'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消')
    }).then(() => {
      this.submitBatchAudit()
    })
  }
  doHandleClick() {
    this.page.currentPage = 1
    if (this.activeName === 'first') {
      this.query.stateEquals = null
    } else if (this.activeName === 'second') {
      this.query.stateEquals = 'INITIAL'
    } else {
      this.query.stateEquals = 'AUDITED'
    }
    this.getPrepayCardList()
  }
  /**
   * 查询
   */
  doSearch() {
    this.page.currentPage = 1
    this.getState()
    this.getPrepayCardList()
  }
  handleSelectionChange(val: any) {
    this.selectedArr = val
  }
  /**
   * 重置
   */
  doReset() {
    this.query = new CardDepositBillFilter()
    this.createdDate = []
    this.lastModifyDate = []
    this.getState()
    this.getPrepayCardList()
  }

  /**
   * 去详情
   */
  doGoDtl(row: any) {
    this.$router.push({name: 'prepay-card-pay-dtl', query: {id: row.billNumber}})
  }
  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.getPrepayCardList()
  }
  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.getPrepayCardList()
  }
  /**
   * 表格排序条件
   */
  onSortChange({ column, prop, order }: any) {
    // todo
  }
  private getPrepayCardList() {
    this.query.createTimeGreaterOrEquals = this.createdDate[0] ? this.createdDate[0] : null
    this.query.createTimeLess = this.createdDate[1] ? this.createdDate[1] : null
    this.query.lastModifiedTimeGreaterOrEquals = this.lastModifyDate[0] ? this.lastModifyDate[0] : null
    this.query.lastModifiedTimeLess = this.lastModifyDate[1] ? this.lastModifyDate[1] : null
    this.query.page = this.page.currentPage - 1
    this.query.pageSize = this.page.size
    this.query.marketingCenterEquals = sessionStorage.getItem('marketCenter')
    CardDepositBillApi.query(this.query).then((resp: any) => {
      if (resp && resp.data) {
        this.tableData = resp.data
        this.page.total = resp.total
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  private submitBatchDelete() {
    let ids: string[] = []
    if (this.selectedArr && this.selectedArr.length > 0) {
      this.selectedArr.forEach((item) => {
        // if (item.state !== 'AUDITED') {
        //   ids.push(item.billNumber!)
        // }
        ids.push(item.billNumber!)
      })
    }
    CardDepositBillApi.batchRemove(ids).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(resp.data)
        this.getState()
        this.getPrepayCardList()
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  private submitBatchAudit() {
    let ids: string[] = []
    if (this.selectedArr && this.selectedArr.length > 0) {
      this.selectedArr.forEach((item) => {
        // if (item.state !== 'AUDITED') {
        //   ids.push(item.billNumber!)
        // }
        ids.push(item.billNumber!)
      })
    }
    CardDepositBillApi.batchAudit(ids).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(resp.data)
        this.getState()
        this.getPrepayCardList()
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  private getState() {
    this.query.createTimeGreaterOrEquals = this.createdDate[0] ? this.createdDate[0] : null
    this.query.createTimeLess = this.createdDate[1] ? this.createdDate[1] : null
    this.query.lastModifiedTimeGreaterOrEquals = this.lastModifyDate[0] ? this.lastModifyDate[0] : null
    this.query.lastModifiedTimeLess = this.lastModifyDate[1] ? this.lastModifyDate[1] : null
    CardDepositBillApi.stats(this.query).then((resp: any) => {
      if (resp && resp.data) {
        this.total.all = resp.data.total
        this.total.initial = resp.data.sumInitial
        this.total.audit = resp.data.sumAudited
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private getConfig() {
    SysConfigApi.get().then((resp: any) => {
      if (resp && resp.data) {
        this.showOrg = resp.data.enableMultiMarketingCenter
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  // 查询储值卡配置
  getCardDepositConfig() {
    CardDepositBillApi.getConfig().then((resp) => {
      if (resp.code === 2000) {
        this.clientRequired = resp.data?.clientRequired || false
      } else {
        throw new Error(resp.msg!)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
}
