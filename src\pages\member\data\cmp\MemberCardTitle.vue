<template>
  <div class="member-card-title">
    <div class="icon wrap" v-if="icon"><img :src="icon"></div>
    <div class="title match">{{ title }}<span class="sub-title"
                                              v-if="subtitle">{{ subtitle }}</span></div>
    <div class="right wrap"
         v-if="$slots.right">
      <slot name="right"></slot>
    </div>
  </div>
</template>
<script lang="ts"
        src="./MemberCardTitle.ts">
</script>
<style lang="scss"
       scoped>
.member-card-title {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  margin-bottom: 18px;

  .match {
    flex-grow: 1;
  }

  .wrap {
    flex-shrink: 0;
  }

  .icon {
    width: 28px;
    height: 28px;
    background: #E6F2FF;
    border-radius: 8px;
    padding: 4px;
    margin-right: 8px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .title {
    font-weight: 600;
    font-size: 16px;
    color: #242633;
    line-height: 24px;

    .sub-title {
      margin-left: 4px;
      display: inline-block;
      font-size: 14px;
      color: #79879E;
      font-weight: 400;
    }
  }

  .right {

  }
}
</style>
