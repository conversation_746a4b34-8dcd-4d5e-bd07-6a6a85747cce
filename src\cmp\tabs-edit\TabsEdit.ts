/*
 * @Author: 黎钰龙
 * @Date: 2023-08-08 17:40:21
 * @LastEditTime: 2025-04-23 14:46:23
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\tabs-edit\TabsEdit.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  name: 'TabsEdit'
})
@I18nPage({
  prefix: ["/公用/券模板", "/营销/大转盘活动", "/公用/按钮"],
  auto: true
})
export default class TabsEdit extends Vue {
  $refs: any
  @Prop({ type: Array, default: [] }) value: any[];
  @Prop({ type: Number, default: 10 }) maxLength: number; //最多几组
  @Prop({ type: Number }) currentGoodsTab: number;
  @Prop({ type: Boolean, default: false }) border: boolean;
  @Prop({ type: Boolean, default: false }) disableRemove: boolean;  //是否禁用删除
  @Prop({ type: Boolean, default: false }) disableAdd: boolean;  //是否禁用添加
  isShowSlot: boolean = true

  doClickTabs(index: any, force: boolean = false) {
    this.$emit('change', 'change', index, force)
  }

  doAddTabs() {
    this.$emit('change', 'add')
  }

  //删除tab的逻辑
  doRemoveTabs(index: any) {
    if (index == this.currentGoodsTab) {
      if (this.value[index + 1]) {  //删除当前tab，选中后面的tab
        this.$emit('change', 'remove', index)
        this.doClickTabs(index)
      } else {  //后面没有tab，就选中前面的tab
        this.$emit('change', 'remove', index)
        this.doClickTabs(index - 1 >= 0 ? index - 1 : 0, true)
      }
      this.reloadSlot() //当删除选中的tab时，由于currentGoodsTab没有变，slot并没有重新挂载，所以需要刷新slot内容
    } else if (index < this.currentGoodsTab) {  //删除前面的tab，当前选中tab也要-1
      this.$emit('change', 'remove', index)
      this.doClickTabs(this.currentGoodsTab - 1 >= 0 ? this.currentGoodsTab - 1 : 0, true)
    } else {
      this.$emit('change', 'remove', index)
    }
  }

  // 重新渲染页面dom，这样写太蠢了 但是暂时没有找到更好的办法
  reloadSlot() {
    this.isShowSlot = false
    this.$nextTick(() => {
      this.isShowSlot = true
    })
  }
};