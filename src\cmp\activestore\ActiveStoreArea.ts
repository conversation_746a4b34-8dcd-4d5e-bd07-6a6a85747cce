import { Component, Prop, Provide, Vue, Watch } from "vue-property-decorator";
import ImportDialog from "cmp/importdialog/ImportDialog.vue";
import IdName from "model/entity/IdName";
import StoreSelectorDialog from "cmp/selectordialogs/StoreSelectorDialog.vue";
import RSOrg from "model/common/RSOrg";
import ImportResultDialog from "pages/deposit/mbrdeposit/active/dialog/ImportResultDialog.vue";
import PromotionCenterSelectorDialog from "cmp/selectordialogs/PromotionCenterSelectorDialog.vue";
import StoreRange from "model/common/StoreRange";
import BrowserMgr from "mgr/BrowserMgr";
import RSMarketingCenter from "model/common/RSMarketingCenter";
import StoreMulPromotionSelectorDialog from "cmp/selectordialogs/StoreMulPromotionSelectorDialog.vue";
import LimitedMarketingCenter from "model/common/LimitedMarketingCenter";
import Zone from "model/datum/zone/Zone";
import ZoneSelectorDialog from "cmp/selectordialogs/ZoneSelectorDialog";
import OrgFilter from "model/datum/org/OrgFilter";
import OrgApi from "http/org/OrgApi";
import RSOrgFilter from "model/common/RSOrgFilter";
import LimitedZones from "model/common/LimitedZones";
import I18nPage from "common/I18nDecorator";

class CustomStore {
  // 控制营销中心展示
  promotionCenter: boolean;
  storeRange: StoreRange;
}

class ExportResult {
  importResult: boolean;
  backUrl: string;
  errorCount: number;
  ignoreCount: number;
  successCount: number;
}

class AreaStoreRange extends Zone {
  store: Nullable<StoreRange> = null
}

export { ExportResult };

@Component({
  name: "ActiveStore",
  components: {
    ImportDialog,
    StoreSelectorDialog,
    ImportResultDialog,
    PromotionCenterSelectorDialog,
    StoreMulPromotionSelectorDialog,
    ZoneSelectorDialog
  },
})
@I18nPage({
  prefix: [
    "/公用/券模板",
    '公用/门店组件',
    '/权益/积分/积分初始化/未初始化状态/得积分规则/基础得积分/点击立即设置',
    '/资料/区域',
    '/营销/积分活动/门店积分兑换/新建门店积分兑换活动',
    '/储值/预付卡/预付卡充值有礼',
    '/公用/券模板详情',
    '/资料/门店'
  ],
  auto: true
})
export default class ActiveStoreArea extends Vue {
  @Provide('maxLimit') maxLimit = 99999
  promotionCenter = false; // 是否开启营销中心
  $refs: any;
  store: StoreRange = new StoreRange();
  currentMarketCenter: LimitedMarketingCenter = new LimitedMarketingCenter();
  importDialogShow = false;
  importUrl = "v1/org/importExcel";
  @Prop()
  showTip: boolean;
  // 是否展示与活动门店一致
  @Prop({
    type: Boolean,
    default: false,
  })
  sameStore: boolean;
  reloadList = false;
  inputValueRules: any;
  marketCenterRules: any;

  comeValue: any = {};

  @Prop({
    type: Boolean,
    default: true,
  })
  internalValidate: boolean;
  @Prop()
  value: CustomStore;
  importResult: ExportResult = new ExportResult();
  importResultDialogShow = false;
  headquarters: Nullable<string> = null
  areaList: AreaStoreRange[] = []

  @Watch("value", { deep: true, immediate: true })
  onValueChange(value: StoreRange) {
    console.log('ActiveStoreArea从父组件拿到的数据',value);
    this.comeValue = value;
    let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
    if (sysConfig) {
      this.promotionCenter = sysConfig.enableMultiMarketingCenter;
    }
    if (this.promotionCenter) { //多营销中心
      if (!value.storeRangeType) {
        this.store = new StoreRange();
        this.store.storeRangeType = "PART";
      } else {
        this.store = JSON.parse(JSON.stringify(value));
        this.doSetIdName();
        this.doBindValue()
      }
      // this.setMarketCenter();
    } else {
      this.store = JSON.parse(JSON.stringify(value));
      this.doBindZoneValue()
    }
  }
  get marketCenterId() {
    if (this.currentMarketCenter && this.currentMarketCenter.marketingCenter) {
      return this.currentMarketCenter.marketingCenter.id;
    }
    return null;
  }
  get marketCenterName() {
    if (this.currentMarketCenter && this.currentMarketCenter.marketingCenter) {
      return this.currentMarketCenter.marketingCenter.name;
    }
    return null;
  }
  get templatePath() {
    if (location.href.indexOf("localhost") === -1) {
      return "template_specify_stores.xlsx";
    } else {
      return "template_specify_stores.xlsx";
    }
  }
  created() {
    if (this.sameStore) {
      this.store.storeRangeType = "SAME";
    }
    let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
    if (sysConfig) {
      this.promotionCenter = sysConfig.enableMultiMarketingCenter;
    }
    this.headquarters = sessionStorage.getItem('headquarters')
    this.inputValueRules = [
      {
        validator: (rule: any, value: string, callback: any) => {
          if (this.store.storeRangeType !== "ALL" && this.store.marketingCenters[0].stores?.stores?.length === 0) {
            callback(new Error(this.formatI18n("/营销/积分活动/积分活动/商品满额加送积分/表单校验/提示信息/请选择门店范围")));
          }
          callback();
        },
        trigger: ["change", "blur"],
      },
    ];
    this.marketCenterRules = [
      {
        validator: (rule: any, value: string, callback: any) => {
          if (this.promotionCenter) {
            if (!this.store.marketingCenters || this.store.marketingCenters.length === 0) {
              callback(new Error(this.formatI18n("/营销/积分活动/积分活动/商品满额加送积分/表单校验/提示信息/请选择营销中心范围")));
              return;
            }
          }
          callback();
        },
        trigger: ["change", "blur"],
      },
    ];
  }

  doBindValue() {
    console.log(this.store);
    this.areaList = []
    this.store.marketingCenters[0].stores!.zones.forEach((item: LimitedZones, index: number) => {
      let store = new StoreRange()
      let str = ''
      item.stores!.stores.forEach((item: IdName) => {
        if (item && item.id) {
          str += item.id + `[${item.name}];`;
        }
      });

      let innerStore = new StoreRange()
      innerStore.storeRangeType = item.stores!.storeRangeType
      innerStore.zones.push({
        stores: item.stores,
        zones: item.zones
      })

      store.marketingCenters.push({
        marketingCenter: this.store.marketingCenters[0].marketingCenter,
        stores: innerStore,
        storesValue: str
      })

      this.areaList.push({
        store: store,
        zone: item.zones as IdName,
        marketingCenter: this.store.marketingCenters[0].marketingCenter?.id as string,
        state: null
      })
    })
    console.log('doBindValue拿到的areaList', this.areaList, this.store);

  }

  doBindZoneValue() {
    this.store.marketingCenters.push(new LimitedMarketingCenter())
    this.store.marketingCenters[0].stores = new StoreRange()
    this.store.marketingCenters[0].stores.zones = this.store.zones
    this.store.zones = []
    this.doBindValue()
  }

  validate() {
    if (this.promotionCenter) {
      return this.$refs.form.validate();
    } else {
      return this.$refs.noMarket.validate();
    }
  }

  doClearStore(index: number) {
    let item = this.areaList[index]
    if (item.store) {
      item.store.stores = [];
      item.store.marketingCenters[0].stores!.stores = []
      item.store.marketingCenters[0].storesValue = "";
      item.store!.marketingCenters[0].stores!.zones[0].stores!.stores = []
      this.doCommitData()
    }
    this.$forceUpdate()
  }

  // setMarketCenter() {
  // 	// created方法会比这个晚 所以先取一遍
  // 	let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
  // 	if (sysConfig) {
  // 		this.promotionCenter = sysConfig.enableMultiMarketingCenter;
  // 	}
  // 	if (this.promotionCenter) {
  // 		if (!this.store.marketingCenters || this.store.marketingCenters.length === 0) {
  // 			const mc: LimitedMarketingCenter = new LimitedMarketingCenter();
  // 			mc.stores = new StoreRange();
  // 			mc.marketingCenter = {
  // 				id: sessionStorage.getItem("marketCenter"),
  // 				name: sessionStorage.getItem("marketCenterName"),
  // 			};
  // 			const arr: LimitedMarketingCenter[] = [];
  // 			mc.stores.storeRangeType = "ALL";
  // 			arr.push(mc);
  // 			this.store.marketingCenters = arr;
  // 			this.doEmitInput();
  // 		}
  // 	}
  // }

  deleteZone(index: number) {
    this.$confirm(this.formatI18n('/公用/活动/提示信息/确认要删除吗？'), this.formatI18n('/公用/活动/提示信息/提示'), {
      confirmButtonText: this.formatI18n('/权益/券/券模板/确定'),
      cancelButtonText: this.formatI18n('/权益/券/券模板/取消'),
    }).then(() => {
      this.areaList.splice(index, 1)
      this.doCommitData()
    })

  }

  setMarketCenterIndex(index: number) {
    let item = this.areaList[index]
    if (item.store) {
      if (!item.store.marketingCenters || item.store.marketingCenters.length === 0) {
        const mc: LimitedMarketingCenter = new LimitedMarketingCenter();
        mc.stores = new StoreRange();
        mc.marketingCenter = {
          id: sessionStorage.getItem("marketCenter"),
          name: sessionStorage.getItem("marketCenterName"),
        };
        const arr: LimitedMarketingCenter[] = [];
        mc.stores.storeRangeType = "ALL";
        mc.stores.zones.push({
          zones: item.zone,
          stores: new StoreRange()
        })
        arr.push(mc);
        item.store.marketingCenters = arr;
        this.doEmitInput();
      }
    }

  }

  doImport() {
    this.importDialogShow = true;
  }
  doSelect(index: number) {
    let item = this.areaList[index]
    console.log(item);

    let goodsData: RSOrg[] = [];
    if (item.store && item.store!.marketingCenters[0].stores!.zones[0].stores!.stores && item.store!.marketingCenters[0].stores!.zones[0].stores!.stores.length > 0) {
      item.store!.marketingCenters[0].stores!.zones[0].stores!.stores.forEach((item: IdName) => {
        let obj: RSOrg = new RSOrg();
        obj.org = new IdName();
        obj.org.id = item.id;
        obj.org.name = item.name;
        goodsData.push(obj);
      });
    }
    this.$refs.selectGoodsScopeDialog[index].open(goodsData, "multiple");
  }
  openPromotionCenterDialog() {
    // todo 这里缺少  营销中心类型  需要调整
    this.$refs.selectPromotionCenterSelectorDialog.open(this.store.marketingCenters, "multiple");
  }
  openZone() {
    console.log(this.areaList)
    this.$refs.zoneSelectorDialog.open(this.areaList, 'multiple')
  }
  doPromotionSubmitGoods(arr: RSMarketingCenter[]) {
    // todo 这里缺少  营销中心类型  需要调整
    let marketingCentersMap: any = {};
    for (let item of this.store.marketingCenters) {
      if (item!.marketingCenter!.id) {
        marketingCentersMap[item!.marketingCenter!.id] = item;
      }
    }
    let result = [];
    for (let mc of arr) {
      let id = mc!.marketingCenter!.id;
      if (!id) {
        continue;
      }
      if (marketingCentersMap[id]) {
        // 没选就添加
        result.push(marketingCentersMap[id]);
      } else {
        let center = new LimitedMarketingCenter();
        center.stores = new StoreRange();
        center.marketingCenter = mc.marketingCenter;
        result.push(center);
      }
    }
    this.store.marketingCenters = result;
    this.reloadList = true;
    this.$nextTick(() => {
      this.doEmitInput();
    });
    setTimeout(() => (this.reloadList = false), 10);
  }
  doSubmitGoods(arr: RSOrg[], index: number) {
    let item = this.areaList[index]
    let stores: IdName[] = [];
    let str = "";
    if (arr && arr.length > 0) {
      arr.forEach((item: any) => {
        if (item && item.org && item.org.id) {
          str += item.org.id + `[${item.org.name}];`;
          let obj: IdName = new IdName();
          obj.id = item.org.id;
          obj.name = item.org.name;
          stores.push(obj);
        }
        if (item && item.id) {
          str += item.id + `[${item.name}];`;
          let obj: IdName = new IdName();
          obj.id = item.id;
          obj.name = item.name;
          stores.push(obj);
        }
      });
    }
    // this.store.stores = stores;
    if (item.store) {
      console.log(item.store);

      item.store.marketingCenters[0].stores!.zones[0].stores!.stores = stores;
      item.store.marketingCenters[0].storesValue = str;
    }
    this.$nextTick(() => {
      this.doEmitInput();
    });
    this.$refs.form.validate();
    this.doCommitData()
    this.$forceUpdate()
  }
  //通过dialog选出区域后，对areaList中的新item做数据初始化
  doSubmitZones(arr: AreaStoreRange[]) {
    console.log(arr)
    arr.forEach((item: AreaStoreRange) => {
      if (!item.store) {
        item.store = new StoreRange()
        if (!item.store.marketingCenters || item.store.marketingCenters.length === 0) {
          const mc: LimitedMarketingCenter = new LimitedMarketingCenter();
          mc.stores = new StoreRange();
          mc.marketingCenter = {
            id: sessionStorage.getItem("marketCenter"),
            name: sessionStorage.getItem("marketCenterName"),
          };
          const arr: LimitedMarketingCenter[] = [];
          mc.stores.storeRangeType = "ALL";
          mc.stores.zones.push({
            zones: item.zone,
            stores: new StoreRange()
          })
          arr.push(mc);
          item.store.marketingCenters = arr;
          this.doEmitInput();
        }
      }
    })
    this.areaList = arr
    this.doCommitData()
  }
  doStoreRange(index: number) {
    let item = this.areaList[index]
    console.log(item)
    if (item.store) {
      if (item.store.marketingCenters[0].stores!.storeRangeType === "ALL") {
        item.store = new StoreRange();
        this.setMarketCenterIndex(index);
        item.store.storeRangeType = "PART";
        item.store.marketingCenters[0].stores!.storeRangeType = "ALL";
        // this.$emit("input", {});
        // this.$emit("change");
        this.doEmitInput();
      } else if (item.store.marketingCenters[0].stores!.storeRangeType === "PART") {
        item.store = new StoreRange();
        this.setMarketCenterIndex(index);
        item.store.storeRangeType = "PART";
        item.store.marketingCenters[0].stores!.storeRangeType = "PART";
        this.doEmitInput();
      } else if (item.store.marketingCenters[0].stores!.storeRangeType === "EXCLUDE") {
        item.store = new StoreRange();
        this.setMarketCenterIndex(index);
        item.store.storeRangeType = "PART";
        item.store.marketingCenters[0].stores!.storeRangeType = "EXCLUDE";
        this.doEmitInput();
      } else {
        item.store = new StoreRange();
        this.setMarketCenterIndex(index);
        item.store.storeRangeType = "SAME";
        this.doEmitInput();
      }
      this.$refs.form.validate();
      this.doCommitData()
    }
    this.$forceUpdate()
  }
  doPromCenterStoreRange(marketCenter: LimitedMarketingCenter) {
    let storeRangeType = marketCenter!.stores!.storeRangeType;
    marketCenter.storesValue = "";
    marketCenter.stores = new StoreRange();
    marketCenter.stores.storeRangeType = storeRangeType;
    marketCenter.stores.stores = [];
    this.doEmitInput();
    this.$refs.form.validate();
  }
  /**
   * 导入成之后
   * @param response
   */
  doUploadSuccess(response: any) {
    // if (response.response.code === 2000) {
    // 	this.importResultDialogShow = true;
    // 	this.importResult = new ExportResult();
    // 	if (response.response.data) {
    // 		this.importResult.importResult = response.response.data.success;
    // 		this.importResult.backUrl = response.response.data.backUrl;
    // 		this.importResult.errorCount = response.response.data.errorCount;
    // 		this.importResult.ignoreCount = response.response.data.ignoreCount;
    // 		this.importResult.successCount = response.response.data.successCount;
    // 		this.doSubmitGoods(response.response.data.orgs);
    // 	}
    // } else {
    // 	this.$message.error(response.response.msg);
    // }
  }
  getStoreCount(count: number) {
    let str: any = this.formatI18n("/公用/券模板", "已选择{0}家门店");
    str = str.replace(/\{0\}/g, count);
    return str;
  }
  private doSetIdName() {
    if (!this.store.marketingCenters) {
      return;
    }
    for (let marketingCenter of this.store.marketingCenters) {
      let arr = marketingCenter!.stores!.stores;
      let str = "";
      if (arr && arr.length > 0) {
        if (arr && arr.length > 0) {
          arr.forEach((item: any) => {
            if (item && item.org && item.org.id) {
              str += item.org.id + `[${item.org.name}];`;
            }
            if (item && item.id) {
              str += item.id + `[${item.name}];`;
            }
          });
        }
      }
      marketingCenter.storesValue = str;
    }
  }
  private doEmitInput() {
    this.doSetStoreRangeLimitType();
    // this.submit();
  }
  // private submit() {
  // 	if (this.promotionCenter) {
  // 		this.$emit("input", this.store);
  // 	} else {
  // 		this.$emit("input", this.comeValue);
  // 	}
  // 	this.$emit("change");
  // }
  private doSetStoreRangeLimitType() {
    // if (this.promotionCenter) {
    // 	this.store.storeRangeLimitType = "MARKETING_CENTER";
    // } else {
    // 	this.store.storeRangeLimitType = "STORE";
    // }
    if (this.store.marketingCenters && this.store.marketingCenters.length > 0) {
      for (let item of this.store.marketingCenters) {
        item.stores!.storeRangeLimitType = "STORE";
      }
    }
    this.store.storeRangeLimitType = "ZONE";
  }

  private parentChange() {
    this.$emit("change");
  }

  commitNoMC(val: any) {
    this.$emit('input', val)
  }

  doCommitData() {
    console.log(this.areaList)
    let data = new StoreRange()
    data.storeRangeType = 'PART'
    data.storeRangeLimitType = 'MARKETING_CENTER'
    const mc: LimitedMarketingCenter = new LimitedMarketingCenter();
    mc.stores = new StoreRange();
    mc.marketingCenter = {
      id: sessionStorage.getItem("marketCenter"),
      name: sessionStorage.getItem("marketCenterName"),
    };
    const arr: LimitedMarketingCenter[] = [];
    mc.stores.storeRangeType = "PART";
    mc.stores.storeRangeLimitType = 'ZONE'
    arr.push(mc);
    data.marketingCenters = arr;
    console.log(data)

    this.areaList.forEach((item: AreaStoreRange, index: number) => {
      data.marketingCenters[0].stores!.zones.push({
        zones: item.zone,
        stores: new StoreRange()
      })
      if (item.store!.marketingCenters[0].stores!.storeRangeType === 'ALL') {
        data.marketingCenters[0].stores!.zones[index].stores!.storeRangeType = 'ALL'
      } else {
        console.log(item.store!.marketingCenters[0].stores!.storeRangeType);

        data.marketingCenters[0].stores!.zones[index].stores!.storeRangeType = item.store!.marketingCenters[0].stores!.storeRangeType //PART 或 EXCLUDE
        for (const item1 of item.store!.marketingCenters[0].stores!.zones[0].stores!.stores) {
          data.marketingCenters[0].stores!.zones[index].stores!.stores.push({
            id: item1.id,
            name: item1.name
          })
        }
      }
    })
    console.log('ActiveStoreArea提交的数据',data);

    this.$emit('change', data)
  }

  doValidate() {
    if (this.areaList.length === 0) {
      this.$message.warning(this.formatI18n('/公用/门店组件/请至少选择一个区域'))
      return Promise.reject()
    }
    for (let index = 0; index < this.areaList.length; index++) {
      const item = this.areaList[index];
      console.log('校验数据',item);
      if (item.store!.marketingCenters[0].stores!.zones[0].stores!.storeRangeType !== 'ALL' && (item.store!.marketingCenters[0].stores!.zones[0].stores!.stores === null || item.store!.marketingCenters[0].stores!.zones[0].stores!.stores.length === 0)) {
        this.$message.warning(this.formatI18n('/储值/会员储值/门店储值管理/请至少选择一个门店'))
        return Promise.reject()
      }
    }
  }
}
