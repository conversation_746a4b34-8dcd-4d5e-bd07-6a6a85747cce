/*
 * @Autor: 司浩
 * @Description: 
 * @Date: 2022-02-23 16:45:15
 * @LastEditors: 黎钰龙
 * @LastEditTime: 2024-01-08 16:16:12
 * @FilePath: \phoenix-web-ui\src\model\v2\coupon\CouponActivityFilter.ts
 */
import ActivityGroupType from 'model/common/ActivityGroupType';
// 券活动查询过滤
export default class CouponActivityFilter {
  // 活动号类似于
  numberLike: Nullable<string> = null
  // 活动号等于
  numberEquals: Nullable<string> = null
  // 活动名称类似于
  nameLike: Nullable<string> = null
  // 活动名称起始于
  nameStartsWith:  Nullable<string> = null
  // 主题名称类似于
  topicNameLikes: Nullable<string> = null
  // 类型等于:action-行为发券;trade-交易发券;小程序领券-MINI_PROGRAM_COUPON;微信投放券-WEIXIN_COUPON;群发券-MANUAL_COUPON;第三方发券-THIRD_ISSUE_COUPON;核销第三方券-THIRD_CODE_ISSUE_COUPON;
  typeEquals: Nullable<string> = null
  // 状态等于: INITAIL-未审核;UNSTART-未开始;PROCESSING-进行中数量；STOPED-已结束,PLATFORM_AUDIT_ING- 平台审核中,PLATFORM_AUDIT_FAIL-平台审核失败
  stateEquals: Nullable<string> = null
  // 活动开始日期小于
  end: Nullable<Date> = null
  // 活动开始日期大于等于
  begin: Nullable<Date> = null
  // 页数
  page: Nullable<number> = null
  // 页面大小
  pageSize: Nullable<number> = null
  
  groupType: Nullable<ActivityGroupType> = null;
  stateNotEquals?: any
  // 活动状态包括
  activityStatesIn?: Nullable<string[]> = null

  outerNumberIdLike: Nullable<string> = null //外部活动号
  outerNumberIdEquals: Nullable<string> = null //等于
}