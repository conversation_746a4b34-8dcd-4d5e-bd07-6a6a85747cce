<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:02
 * @LastEditTime: 2024-05-08 16:35:27
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\coupontenplate\CashCoupon\CashCoupon.vue
 * 记得注释
-->
<template>
  <div class="cash-coupon">
    <el-form :model="ruleForm" :rules="rules" class="demo-ruleForm" label-width="120px" ref="ruleForm">
      <div v-if="$route.query.from === 'edit' && !wxScanForCoupon"
        style="height: 48px;line-height: 48px;background-color: #3366ff19;margin: 0 20px;padding-left: 20px;margin-bottom: 10px">
        <img style="position: relative;top: 5px;" src="~assets/image/auth/info3.png" alt="" />{{
					formatI18n("/权益/券/券模板/编辑界面/用券规则下的提示/券模板修改后，已发出的券的有效期仍以原来的为准，其余用券规则以修改后的为准。")
				}}
      </div>
      <div class="setting-container">
        <!-- 基础信息 -->
        <div class="setting-block">
          <div class="section-title">{{i18n('基础信息')}}</div>
          <!-- 券名称 -->
          <CouponName :ruleForm="ruleForm" :copyFlag='copyFlag'>
            <template slot="slot">
              <el-input maxlength="128" style="width: 390px" v-model="ruleForm.name" @change="doFormItemChange"
                :placeholder="i18n('/营销/券礼包活动/核销第三方券/请输入')"></el-input>
            </template>
          </CouponName>
          <!-- 券图标 -->
          <CouponTemplateLogo :original-logo-url="ruleForm.logoUrl" @logoUrlCallBack="logoUrlCallBack"></CouponTemplateLogo>
          <!-- 使用须知 -->
          <UseCouponDesc :ruleForm="ruleForm" :isShowTips="true">
            <template slot="slot">
              <el-input :maxlength="remarkMaxlength" style="width: 390px;" type="textarea" v-model="ruleForm.couponProduct"
                :placeholder="remarkPlaceholder" @change="doFormItemChange">
              </el-input>
            </template>
          </UseCouponDesc>
        </div>

        <!-- 用券时间 -->
        <div class="setting-block">
          <div class="section-title">{{i18n('/营销/券礼包活动/券查询/用券时间')}}</div>
          <!-- 券有效期 -->
          <CouponEffectPeriod ref="couponEffectPeriod" v-model="ruleForm" :options="options" @change="doFormItemChange"></CouponEffectPeriod>
        </div>

      </div>

      <div class="setting-container">
        <div class="setting-block">
          <div class="section-title">
            <span>{{i18n('高级设置')}}</span>
          </div>
          <div>
            <!-- 账款项目 -->
            <el-form-item :label="i18n('账款项目')" prop="termsModel">
              <div v-if="queryCostParyRange=='cost_party'">
                <SelectCostParty @change="doFormItemChange" v-model="ruleForm.termsModel" :isOnlyId="true" :hideAll="true" width="20%" :placeholder="formatI18n('/公用/下拉框/提示', '请选择')" >
                </SelectCostParty>
              </div>
              <el-input v-if="queryCostParyRange=='customize'" :placeholder="i18n('/储值/预付卡/卡模板/详情页面/请输入{0}位以内的数字或字母',['128'])" maxlength="128" style="width: 325px"
                        v-model="ruleForm.termsModel" @change="doFormItemChange">
              </el-input>
            </el-form-item>
          </div>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script lang="ts" src="./EquityCoupon.ts"></script>

<style lang="scss">
.cash-coupon {
  padding-bottom: 30px;
  .setting-container {
    .setting-block {
      .section-title {
        .telescoping {
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #007eff;
          margin-left: 12px;
          cursor: pointer;
          i {
            color: #242633;
          }
        }
      }
    }
  }

  .coupon-step,
  .cur-form-item {
    .el-form-item__label {
      &:before {
        content: "*";
        color: #ef393f;
        margin-right: 4px;
      }
    }
  }

  .rule-table {
    margin-top: 10px;
    width: 70%;

    .rule-table-header {
      padding: 0 10px;
      background-color: #e6e6e6;
      border: 1px solid #e6e6e6;
    }

    .rule-table-line {
      padding: 10px;
      border: 1px solid #e6e6e6;
      border-top: 0;
    }

    .opt-col {
      a + a {
        margin-left: 10px;
      }
    }
  }

  .cur-from-item {
    .el-radio {
      height: auto !important;
      line-height: 40px;
    }
  }
}
</style>
