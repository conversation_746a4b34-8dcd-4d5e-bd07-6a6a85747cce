/*
 * @Author: 黎钰龙
 * @Date: 2025-02-05 11:04:30
 * @LastEditTime: 2025-02-24 16:37:06
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\analysis\cmp\AddPortraitInfoDialog.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import BCustomerProfileLine, { PortraitInfoType } from 'model/analysis/BCustomerProfileLine';
import { Component, Prop, Vue } from 'vue-property-decorator';
import PortraitIndicatorSelect from './PortraitIndicatorSelect/PortraitIndicatorSelect';
import BCustomerProfileTarget, { ProfileTargetTypeUtil } from 'model/analysis/BCustomerProfileTarget';
import BCustomerProfileDimension from 'model/analysis/BCustomerProfileDimension';
import CommonUtil from 'util/CommonUtil';

export class PortraitForm {
  // uuid
  uuid: string = CommonUtil.uuid()
  // 选择类型
  infoType: PortraitInfoType = PortraitInfoType.AttributeDistributionCalculationRule
  // 指标
  target: Nullable<BCustomerProfileTarget>[] = []
  // 维度
  dimension: BCustomerProfileDimension[] = []
}

@Component({
  name: 'AddPortraitInfoDialog',
  components: {
    PortraitIndicatorSelect
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/数据/客群画像'
  ],
  auto: true
})
export default class AddPortraitInfoDialog extends Vue {
  $refs: any
  @Prop({ type: Number, default: 10 }) leftCardNum: number; //还可以添加多少卡片
  editType: 'create' | 'edit' = 'create'
  groupVisible: boolean = false
  formData: PortraitForm = new PortraitForm()
  editIndex: number = -1

  get title() {
    return this.editType === 'edit' ? this.i18n('修改画像信息') : this.i18n('添加画像信息')
  }

  // 是否展示指标（属性统计、交叉统计需要展示）
  get isShowTarget() {
    return [PortraitInfoType.AttributeStatisticsCalculationRule, PortraitInfoType.CrossStatisticsCalculationRule].includes(this.formData.infoType)
  }

  get rules() {
    return {
      infoType: [
        { required: true, message: this.i18n('请选择类型'), trigger: 'change' }
      ],
      target: [
        {
          required: true,
          validator: (rule: any, val: Nullable<BCustomerProfileTarget>[], callback: any) => {
            if (!val?.length) {
              callback(this.i18n('请添加指标'))
            }
            const arr: string[] = []
            val.forEach((item) => {
              if (arr.some((val) => val === JSON.stringify(item))) {
                callback(this.i18n('不允许添加重复的指标'))
              } else {
                arr.push(JSON.stringify(item))
              }
            })
            callback()
          },
          trigger: 'change'
        }
      ],
      dimension: [
        {
          required: true,
          validator: (rule: any, val: BCustomerProfileDimension[], callback: any) => {
            if (!val?.length) {
              callback(this.i18n('请添加维度'))
            }
            const arr: string[] = []
            val.forEach((item) => {
              if (arr.some((val) => val === JSON.stringify(item))) {
                callback(this.i18n('不允许添加重复的维度'))
              } else {
                arr.push(JSON.stringify(item))
              }
            })
            callback()
          },
          trigger: 'change'
        }
      ]
    }
  }

  closeDialog() {
    this.closePopover()
    this.groupVisible = false
    this.reset()
    this.$emit('close')
  }

  reset() {
    this.formData.infoType = PortraitInfoType.AttributeDistributionCalculationRule
    this.formData = new PortraitForm()
    this.editIndex = -1
  }

  confirm() {
    if ([PortraitInfoType.AttributeDistributionCalculationRule, PortraitInfoType.AttributeStatisticsCalculationRule].includes(this.formData.infoType)) {
      // 属性分布 属性统计 由于可添加的卡片数量限制，需要限制维度的数量
      if (this.formData.dimension.length > this.leftCardNum) {
        return this.$message.warning(this.i18n('画像卡片数量最大为10，目前最多可选{0}个维度', [String(this.leftCardNum)]))
      }
    }
    this.closePopover()
    this.doValidate()
  }

  doValidate() {
    this.$refs.form.validate().then(() => {
      const data = JSON.parse(JSON.stringify(this.formData))
      if ([PortraitInfoType.CrossDistributionCalculationRule, PortraitInfoType.CrossStatisticsCalculationRule].includes(this.formData.infoType) && this.formData.dimension.length !== 2) {
        // 交叉分布、交叉统计 只能选两个维度
        return this.$message.warning(this.i18n('交叉分布、交叉统计类型，仅能选两个维度'))
      }
      this.$emit('doSubmit', data, this.editIndex)
      this.closeDialog()
    })
  }

  open(value?: BCustomerProfileLine, index?: number) {
    this.groupVisible = true
    if (value) {
      // 回填本地数据
      this.doBindValue(value)
      this.editIndex = index!
    } else {
      this.editType = 'create'
    }
  }

  doBindValue(val: BCustomerProfileLine) {
    this.editType = 'edit'
    this.formData.uuid = val.uuid!
    this.formData.infoType = val.ruleType!
    this.formData.target = val.target ? [val.target] : []
    if (val.dimension1) {
      this.formData.dimension.push(val.dimension1)
      if (val.dimension2) {
        this.formData.dimension.push(val.dimension2)
      }
    }
  }

  // 提交指标item
  doSubmitTarget(obj: Nullable<BCustomerProfileTarget>) {
    this.formData.target.push(obj)
  }

  // 提交维度item
  doSubmitDimension(obj: BCustomerProfileDimension) {
    this.formData.dimension.push(obj)
  }

  // 获取指标名
  getTargetName(item: Nullable<BCustomerProfileTarget>) {
    let str = ''
    if (item?.tagId) {
      str = item.tagName || '--'
    } else if (item?.memberProp) {
      str = item.memberPropName || '--'
    }
    if (ProfileTargetTypeUtil.getTypeName(item?.type)) {
      str += '：' + ProfileTargetTypeUtil.getTypeName(item?.type)
    }
    return str
  }

  // 获取维度名
  getDimensionName(item: BCustomerProfileDimension) {
    if (item?.tagId) {
      return item.tagName || '--'
    } else if (item?.memberProp) {
      return item.memberPropName || '--'
    } else {
      return '--'
    }
  }

  // 删除指标
  removeTarget(index: number) {
    this.formData.target.splice(index, 1)
  }

  // 删除维度
  removeDimension(index: number) {
    this.formData.dimension.splice(index, 1)
  }

  closePopover() {
    this.$refs.portraitIndicatorTarget?.close()
    this.$refs.portraitIndicatorDimension?.close()
  }
};