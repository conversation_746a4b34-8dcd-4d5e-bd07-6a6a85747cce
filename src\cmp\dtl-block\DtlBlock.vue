<template>
    <div class="dtl-block-view">
        <span class="tip"></span>
        <span class="content"><slot></slot></span>
        <div class="btn">
            <slot name="btn"></slot>
        </div>
    </div>
</template>

<script lang="ts" src="./DtlBlock.ts">
</script>

<style lang="scss">
.dtl-block-view{
    height: 60px;
    border-bottom: 1px solid #e7eaec;
    line-height: 60px;
    .tip{
        width: 6px;
        height: 16px;
        background-color: #3189fd;
        display: inline-block;
        margin-right: 10px;
        position: relative;
        top: 2px;
    }
    .content{
        display: inline-block;
        font-size: 16px;
    }
    .btn{
        float: right;
    }
}
</style>