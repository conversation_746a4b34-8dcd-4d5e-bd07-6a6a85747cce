import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import GoodsRange from 'model/common/GoodsRange'
import InMemPage from "cmp/in-mem-page/InMemPage";
import GoodsScopeInMemPage from "cmp/goodsscope/GoodsScopeInMemPage";
import IdName from "model/common/IdName";
import Connective1 from "pages/member/insight/cmp/connective/Connective1";

class Line {
  label: string = ''
  include: string = ''
  items: IdName[] = []
}

@Component({
  name: 'GoodsScopeDtl',
  components: {
    InMemPage,
    GoodsScopeInMemPage,
    Connective1,
  }
})
export default class GoodsScopeDtl extends Vue {
  @Prop()
  goods: GoodsRange

  @Prop({
    type: String,
    default: 'standard'
  })
  theme: 'standard' | 'precisionmarketing'

  @Prop({
    type: Boolean,
    default: false
  })
  hideTitle: boolean // 只显示选择商品部分

  @Prop({
    type: Boolean,
    default: false
  })
  showAll: Boolean //是否展示全部商品

  lines: Line[] = []

  @Watch("goods", { deep: true, immediate: true })
  watchGoods() {
    this.lines = []
    if (this.goods && this.goods.includeCodes && this.goods.includeCodes.length > 0) {
      this.lines.push({
        label: this.formatI18n('/公用/券模板', '单品'),
        include: this.formatI18n('/公用/券模板', '属于'),
        items: this.goods.includeCodes.map((item) => {
          const obj = new IdName()
          obj.id = item.code
          obj.name = item.name
          return obj
        }),
      })
    }
    if (this.goods && this.goods.excludeCodes && this.goods.excludeCodes.length > 0) {
      this.lines.push({
        label: this.formatI18n('/公用/券模板', '单品'),
        include: this.formatI18n('/公用/券模板', '不属于'),
        items: this.goods.excludeCodes.map((item) => {
          const obj = new IdName()
          obj.id = item.code
          obj.name = item.name
          return obj
        }),
      })
    }
    if (this.goods && this.goods.includeGoods && this.goods.includeGoods.length > 0) {
      this.lines.push({
        label: this.formatI18n('/公用/券模板', '单品'),
        include: this.formatI18n('/公用/券模板', '属于'),
        items: this.goods.includeGoods,
      })
    }
    if (this.goods && this.goods.excludeGoods && this.goods.excludeGoods.length > 0) {
      this.lines.push({
        label: this.formatI18n('/公用/券模板', '单品'),
        include: this.formatI18n('/公用/券模板', '不属于'),
        items: this.goods.excludeGoods,
      })
    }

    if (this.goods && this.goods.includeBrands && this.goods.includeBrands.length > 0) {
      this.lines.push({
        label: this.formatI18n('/公用/券模板', '品牌'),
        include: this.formatI18n('/公用/券模板', '属于'),
        items: this.goods.includeBrands,
      })
    }
    if (this.goods && this.goods.excludeBrands && this.goods.excludeBrands.length > 0) {
      this.lines.push({
        label: this.formatI18n('/公用/券模板', '品牌'),
        include: this.formatI18n('/公用/券模板', '不属于'),
        items: this.goods.excludeBrands,
      })
    }

    if (this.goods && this.goods.includeCategories && this.goods.includeCategories.length > 0) {
      this.lines.push({
        label: this.formatI18n('/公用/券模板', '品类'),
        include: this.formatI18n('/公用/券模板', '属于'),
        items: this.goods.includeCategories,
      })
    }
    if (this.goods && this.goods.excludeCategories && this.goods.excludeCategories.length > 0) {
      this.lines.push({
        label: this.formatI18n('/公用/券模板', '品类'),
        include: this.formatI18n('/公用/券模板', '不属于'),
        items: this.goods.excludeCategories,
      })
    }
    if (this.goods && this.goods.includeTags && this.goods.includeTags.length > 0) {
      this.lines.push({
        label: this.formatI18n('/公用/券模板', '商品标签'),
        include: this.formatI18n('/公用/券模板', '属于'),
        items: this.goods.includeTags,
      })
    }
    if (this.goods && this.goods.excludeTags && this.goods.excludeTags.length > 0) {
      this.lines.push({
        label: this.formatI18n('/公用/券模板', '商品标签'),
        include: this.formatI18n('/公用/券模板', '不属于'),
        items: this.goods.excludeTags,
      })
    }
  }

  getTotal(lines: Line) {
    let str: any = this.formatI18n('/会员/会员资料/共{0}条')
    str = str.replace(/\{0\}/g, lines.items?.length || 0)
    return str
  }

}
