import {Component, Prop, Vue} from 'vue-property-decorator'
import MemberApi from 'http/member_standard/MemberApi'
import MemberPass from 'model/member_v2/member/MemberPass'

@Component({
  name: 'ResetPasswordDialog',
  components: {}
})
export default class ResetPasswordDialog extends Vue {
  $refs: any
  ruleForm = {
    password: '',
    confirmPassword: ''
  }
  rules: any = {}
  @Prop()
  data: any
  @Prop()
  title: any

  @Prop({
    type: Boolean,
    default: false
  })
  dialogShow: boolean

  created() {
    this.rules = {
      password: [
        { required: true, message: this.formatI18n('/设置/权限/用户管理/新用户/新建用户/登录密码', '请输入密码'), trigger: 'change' }
      ],
      confirmPassword: [
        { required: true, message: this.formatI18n('/会员/会员资料/详情界面/重置密码js提示信息', '请输入确认密码'), trigger: 'change' }
      ]
    }
  }
  doBeforeClose(done: any) {
    this.$emit('dialogClose')
    done()
  }
  doModalClose() {
    if(this.ruleForm.password !== this.ruleForm.confirmPassword) {
      this.$message.warning(this.formatI18n('/会员/会员资料', '密码不一致，请重新输入') as any)
      return
    }
    if (this.ruleForm.password && this.ruleForm.password.length !== 6) {
      this.$message.warning(this.formatI18n('/会员/会员资料/详情界面/重置密码js提示信息', '密码为6位数字') as any)
      return
    }
    this.$refs['ruleForm'].validate((valid: any) => {
      if (valid) {
        let param: MemberPass = new MemberPass()
        param.memberId = this.$route.query.id as string
        param.password = this.ruleForm.password
        MemberApi.modifyPayPass(param).then((resp: any) => {
          if (resp && resp.code === 2000) {
            this.$message.success(this.formatI18n('/会员/会员资料', '重置密码成功') as any)
            this.$emit('dialogClose')
          }
        }).catch((error: any) => {
          if (error && error.message) {
            this.$message.error(error.message)
          }
        })
      } else {
        return false;
      }
    })
  }
  doPwdChange() {
    let regex = /^[0-9]\d*$/g
    if (!regex.test(this.ruleForm.password)) {
      this.$message.warning(this.formatI18n('/会员/会员资料/详情界面/重置密码js提示信息', '密码输入不合法') as any)
      this.ruleForm.password = ''
    } else {
      if (this.ruleForm.password.length !== 6) {
        this.$message.warning(this.formatI18n('/会员/会员资料/详情界面/重置密码js提示信息', '密码为6位数字') as any)
      }
    }
  }
  doConfirmPwdChange() {
    let regex = /^[0-9]\d*$/g
    if (!regex.test(this.ruleForm.confirmPassword)) {
      this.$message.warning(this.formatI18n('/会员/会员资料/详情界面/重置密码js提示信息', '密码输入不合法') as any)
      this.ruleForm.confirmPassword = ''
    } else {
      if (this.ruleForm.confirmPassword.length !== 6) {
        this.$message.warning(this.formatI18n('/会员/会员资料/详情界面/重置密码js提示信息', '密码为6位数字') as any)
      }
    }
  }
  doCancel() {
    this.$emit('dialogClose')
  }
  doCheckGoods() {
    // todo
  }
}