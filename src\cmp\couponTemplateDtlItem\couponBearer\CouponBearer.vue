<!--
 * @Author: 黎钰龙
 * @Date: 2024-01-24 14:07:05
 * @LastEditTime: 2025-05-13 14:26:21
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\couponTemplateDtlItem\couponBearer\CouponBearer.vue
 * 记得注释
-->
<template>
  <div class="coupon-bears-container">
    <div style="height: 36px; line-height: 36px"
      v-if="!data.coupons.costParty || !data.coupons.costParty.costPartyDetails || data.coupons.costParty.costPartyDetails.length === 0">
      -
    </div>
    <div class="bears-info" v-else>
      <div v-for="item in data.coupons.costParty.costPartyDetails" :key="item.party" class="cur-party">
        <div v-html="getCostPartr(item.party, item.value)"></div>
      </div>
      <el-button @click="viewCBSpecialGoods"
        v-if="data.coupons && data.coupons.costParty && data.coupons.costParty.specialGoodsCostParties && data.coupons.costParty.specialGoodsCostParties.length"
        type="text">
        {{ i18n('查看特殊商品') }}
      </el-button>
    </div>
    <CBearSpecialGoodsDtl ref="CBearSpecialGoodsDtl" :parties="parties"
      :data="data.coupons && data.coupons.costParty ? (data.coupons.costParty.specialGoodsCostParties || []) : []">
    </CBearSpecialGoodsDtl>
  </div>
</template>

<script lang="ts" src='./CouponBearer.ts'>
</script>

<style lang="scss" >
.coupon-bears-container {
  .bears-info {
    padding-top: 8px;
  }
}
</style>