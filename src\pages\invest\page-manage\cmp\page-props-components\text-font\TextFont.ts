/*
 * @Author: haiding <EMAIL>
 * @Date: 2025-02-19 10:03:52
 * @LastEditors: haiding <EMAIL>
 * @LastEditTime: 2025-03-06 11:22:12
 * @FilePath: \new-kequn\src\pages\invest\page-manage\cmp\page-props-components\text-font\TextFont.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import emitter from 'util/emitter';
import { FormMode } from 'model/local/FormMode';
import FormDefUtil from 'util/FormDefUtil';
import I18nPage from 'common/I18nDecorator';

@Component({
  name: 'TextFont',
  mixins: [emitter],
  components: {  },
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/公用/菜单',
    '/页面/页面管理',
  ],
  auto: true
})
export default class TextFont extends Vue {
  @Prop({ type: Boolean, default: false })
  readonly: boolean; //
  @Prop({ type: String, default: 'PlaceTitle' })
  validateName: string; // 组件校验名称，editPage通过该名称进行组件校验及校验信息清除
  @Prop({ type: Object })
  value: any; // 数据模型
  formKey: any;
  FormModeType = FormMode;
  $refs: any;
  labelPosition: string = 'top';
  styTextAlignList:any[] = [{
    id:0,
    lable:'left',
    text:this.i18n('左对齐')
  },{
    id:1,
    lable:'center',
    text:this.i18n('居中对齐')
  },{
    id:2,
    lable:'right',
    text:this.i18n('右对齐')
  }]
  styFontSizeList:any[] = [{
    id:0,
    lable:'large',
    text:this.i18n('大')
  },{
    id:1,
    lable:'medium',
    text:this.i18n('中')
  },{
    id:2,
    lable:'small',
    text:this.i18n('小')
  }]

  handleChange() {
    this.$emit('input', this.value);
    this.$emit('change', this.value);
    this.validate(() => {});
  }

  mounted() {
    console.log(this.value,'value');
    
    if (this.validateName) {
      // this['dispatch']('EditPage', 'nf.edit.addForm', [this]);
    }
  }

  beforeDestroy() {
    // this['dispatch']('EditPage', 'nf.edit.removeForm', [this]);
  }

  validate(callback: () => void) {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.validate(callback);
    }
  }

  clearValidate() {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.clearValidate();
    }
  }
}
