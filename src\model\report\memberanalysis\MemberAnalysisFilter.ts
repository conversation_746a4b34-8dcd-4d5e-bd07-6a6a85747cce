/*
 * @Author: haiding <EMAIL>
 * @Date: 2025-02-19 09:27:29
 * @LastEditors: haiding <EMAIL>
 * @LastEditTime: 2025-03-05 09:24:05
 * @FilePath: \new\src\model\report\MemberAnalysisFilter.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import MemberAnalysis from 'model/report/memberanalysis/MemberAnalysis'
import MemberAnalysisCondition from 'model/default/MemberAnalysisCondition'

// 会员分析请求
export default class MemberAnalysisFilter {
  // 会员分析
  memberAnalysis: Nullable<MemberAnalysis> = null
  // 条件
  conditions: MemberAnalysisCondition[] = []

  page: number = 0
  pageSize: number = 10
}