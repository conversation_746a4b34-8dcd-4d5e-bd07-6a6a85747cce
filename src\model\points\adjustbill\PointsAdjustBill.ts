import IdName from 'model/common/IdName'
import PointsAdjustBillLine from 'model/points/adjustbill/PointsAdjustBillLine'
import PointsAdjustBillLog from 'model/points/adjustbill/PointsAdjustBillLog'

export default class PointsAdjustBill {
  // 会员手机号，只在修改时候显示使用
  mobile: Nullable<string> = null
  // 会员号，只在修改时候显示使用
  hdCardMbrId: Nullable<string> = null
  // 实体卡号，只在修改时候显示使用
  hdCardCardNum: Nullable<string> = null
  // 会员名称
  name: Nullable<string> = null
  // 单号
  billNumber: Nullable<string> = null
  // 状态INITIAL：未审核；AUDITED：已审核
  state: Nullable<string> = null
  // 来源：impt-导入；create-界面新建
  source: Nullable<string> = null
  // 发生组织
  occurredOrg: Nullable<IdName> = new IdName()
  // 营销中心
  marketingCenter: Nullable<string> = null
  // 审核时间
  audited: Nullable<Date> = null
  // 审核人
  auditor: Nullable<string> = null
  // 备注
  remark: Nullable<string> = null
  // 明细备注只用于展示：增加积分数，扣减积分数
  detailRemark: string[][] = []
  // 创建时间
  created: Nullable<Date> = null
  // 最后修改时间
  lastModified: Nullable<Date> = null
  // 明细
  lines: PointsAdjustBillLine[] = []
  // 操作日志
  logs: PointsAdjustBillLog[] = []
}