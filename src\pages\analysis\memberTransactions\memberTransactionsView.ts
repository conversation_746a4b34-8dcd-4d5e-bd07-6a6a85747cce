/*
 * @Author: 黎钰龙
 * @Date: 2024-02-29 13:47:05
 * @LastEditTime: 2024-06-03 17:08:53
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\analysis\memberGrowth\MemberGrowthAnalysis.ts
 * 记得注释
 */
import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import FormItem from 'cmp/formitem/FormItem';
import MyQueryCmp from 'cmp/querycondition/MyQueryCmp';
import I18nPage from 'common/I18nDecorator';
import { Component } from 'vue-property-decorator';
import AnalysisDateSelector from '../cmp/AnalysisDateSelector/AnalysisDateSelector.vue';
import ChannelSelect from 'cmp/channelselect/ChannelSelect';
import SelectStores from 'cmp/selectStores/SelectStores';
import MemberLineChart from '../cmp/MemberLineChart/MemberLineChart';
import SelectEmployees from 'cmp/selectEmployees/selectEmployees';
import OrgTradeAnalysisReportQuery from 'model/analysis/OrgTradeAnalysisReportQuery';
import AnalysisReportApi from 'http/analysis/AnalysisReportApi';
import CommonUtil from 'util/CommonUtil';
import OrgTradeAnalysisReport from 'model/analysis/OrgTradeAnalysisReport';
import AbstractLineChart from '../cmp/AbstractLineChart';
import DownloadCenterDialog from 'pages/main/dowmload_center_dialog/DownloadCenterDialog';

class Filter {
    dataRange: any = null  //时间
    channel: any = null  //渠道
    store: any = null  //门店
    areaLeaderEquals: any = null  //区域主管
    operationEquals: any = null //营运经理
}

@Component({
    name: 'MemberTransactionsView',
    components: {
        BreadCrume,
        MyQueryCmp,
        FormItem,
        AnalysisDateSelector,
        ChannelSelect,
        SelectStores,
        MemberLineChart,
        SelectEmployees,
        DownloadCenterDialog
    }
})
@I18nPage({
    prefix: [
        '/公用/券模板',
        '/数据/会员交易分析'
    ],
    auto: true
})
export default class MemberTransactionsView extends AbstractLineChart {
    $refs: any
    panelArray: any = []
    filter: Filter = new Filter()
    detail: OrgTradeAnalysisReport = new OrgTradeAnalysisReport()
    downloadCenterFlag: boolean = false; //文件下载中心弹窗

    /* 每个item的第一项：数据名
    /  第二项：数据值
    /  第三项：是否展示在右y轴上 */
    get valueArray() {
        const arr = [
            [this.i18n('交易金额'), this.detail.tradeTotalData, 0],
            [this.i18n('交易数量'), this.detail.tradeGoodsQtyData, 0],
            [this.i18n('客单数'), this.detail.tradeQtyData, 0],
            [this.i18n('客单价'), this.detail.perPriceData, 0],
            [this.i18n('客单件'), this.detail.perQtyData, 0],
            [this.i18n('件单价'), this.detail.perQtyPriceData, 0],
            [this.i18n('会员交易金额'), this.detail.memberTradeTotalData, 0],
            [this.i18n('会员交易金额占比'), this.doDivideGetPercent(this.detail.memberTradeTotalData, this.detail.tradeTotalData), 1],
            [this.i18n('会员交易数量'), this.detail.memberTradeGoodsQtyData, 0],
            [this.i18n('会员客单数'), this.detail.memberTradeQtyData, 0],
            [this.i18n('会员客单数占比'), this.doDivideGetPercent(this.detail.memberTradeQtyData, this.detail.tradeQtyData) , 1],
            [this.i18n('会员客单价'), this.detail.memberPerPriceData, 0],
            [this.i18n('会员客单件'), this.detail.memberPerQtyData, 0],
            [this.i18n('会员件单价'), this.detail.memberPerQtyPriceData, 0],
        ]
        return this.doTransValueArray(arr)
    }

    get summaryViewArr() {
        return [
            {
                label: this.i18n('交易金额'),
                value: this.detail.summary?.tradeTotal,
                ringValue: Number(this.detail.summary?.tradeTotalRingGrowth)
            },
            {
                label: this.i18n('交易数量'),
                value: this.detail.summary?.tradeGoodsQty,
                ringValue: Number(this.detail.summary?.tradeGoodsQtyRingGrowth)
            },
            {
                label: this.i18n('客单数'),
                value: this.detail.summary?.tradeQty,
                ringValue: Number(this.detail.summary?.tradeQtyRingGrowth)
            },
            {
                label: this.i18n('客单价'),
                value: this.detail.summary?.perPrice,
                ringValue: Number(this.detail.summary?.perPriceRingGrowth)
            },
            {
                label: this.i18n('客单件'),
                value: this.detail.summary?.perQty,
                ringValue: Number(this.detail.summary?.perQtyRingGrowth)
            },
            {
                label: this.i18n('件单价'),
                value: this.detail.summary?.perQtyPrice,
                ringValue: Number(this.detail.summary?.perQtyPriceRingGrowth)
            },
            {
                label: this.i18n('会员交易额/占比'),
                value: this.detail.summary?.memberTradeTotal + '/' + this.detail.summary?.memberTradeTotalRatio + '%',
                ringValue: Number(this.detail.summary?.memberTradeTotalRingGrowth)
            },
            {
                label: this.i18n('会员交易数量'),
                value: this.detail.summary?.memberTradeGoodsQty,
                ringValue: Number(this.detail.summary?.memberTradeGoodsQtyRingGrowth)
            },
            {
                label: this.i18n('会员客单数/占比'),
                value: this.detail.summary?.memberTradeQty + '/' + this.detail.summary?.memberTradeQtyRatio + '%',
                ringValue: Number(this.detail.summary?.memberTradeQtyRingGrowth)
            },
            {
                label: this.i18n('会员客单价'),
                value: this.detail.summary?.memberPerPrice,
                ringValue: Number(this.detail.summary?.memberPerPriceRingGrowth)
            },
            {
                label: this.i18n('会员客单件'),
                value: this.detail.summary?.memberPerQty,
                ringValue: Number(this.detail.summary?.memberPerQtyRingGrowth)
            },
            {
                label: this.i18n('会员件单价'),
                value: this.detail.summary?.memberPerQtyPrice,
                ringValue: Number(this.detail.summary?.memberPerQtyPriceRingGrowth)
            },
        ]
    }

    // 需要显示百分号的数据名称
    get showPercentName() {
        return [this.i18n('会员客单数占比'), this.i18n('会员交易金额占比')]
    }

    created() {
        this.panelArray = [
            {
                name: this.i18n('/公用/菜单/会员交易分析'),
                url: ""
            }
        ]
    }

    mounted() {
        this.onSearch()
    }

    doExport() {
        this.$confirm(this.i18n("将根据当前查询条件生成报表，确认导出吗？"), this.i18n('/储值/预付卡/充值卡制售单/列表页面/导出'), {
            confirmButtonText: this.formatI18n("/公用/按钮", "确定") as string,
            cancelButtonText: this.formatI18n("/公用/按钮", "取消") as string,
        }).then(() => {
            const body = this.doFilterParams()
            AnalysisReportApi.exportOrgTradeReport(body).then((res) => {
                if (res.code === 2000) {
                    this.downloadCenterFlag = true;
                } else {
                    throw new Error(res.msg!)
                }
            }).catch((error) => {
                this.$message.error(error.message)
            })
        });
    }

    doReset() {
        this.filter = new Filter()
        this.$refs.analysisDateSelector.doReset()
        this.onSearch()
    }

    onSearch() {
        const body = this.doFilterParams()
        const loading = CommonUtil.Loading()
        AnalysisReportApi.orgTradeReport(body).then((res) => {
            if (res.code === 2000) {
                this.detail = res.data || new OrgTradeAnalysisReport()
            } else {
                throw new Error(res.msg as any)
            }
        }).catch((error) => {
            this.$message.error(error.message || this.i18n('内部异常'))
        }).finally(() => {
            loading.close()
        })
    }

    // 查询条件
    doFilterParams() {
        const params = new OrgTradeAnalysisReportQuery()
        params.dateUnitEquals = this.filter.dataRange?.type
        if (this.filter.dataRange?.date?.length) {
            params.startDate = this.filter.dataRange.date[0]
            params.endDate = this.filter.dataRange.date[1]
        }
        params.operationEquals = this.filter.operationEquals?.id || null
        params.areaLeaderEquals = this.filter.areaLeaderEquals?.id || null
        //渠道
        if (this.filter.channel?.length) {
            params.channelTypeEquals = this.filter.channel[0].type
            params.channelIdEquals = this.filter.channel[0].id
        }
        params.storeIdEquals = this.filter.store
        return params
    }

    doDateChange(value: any) { // {type: 'DAY' | 'WEEK' | 'MONTH', date:['2024-03-05','2024-03-06']}
        this.filter.dataRange = value
    }

    doDownloadDialogClose() {
        this.downloadCenterFlag = false;
    }
    // doDivideGetPercentFour(value:string,value2:number){

    // }
};