import {Component, Prop, Vue} from 'vue-property-decorator'

@Component({
  name: 'ImportResultDialog',
  components: {}
})
export default class ImportResultDialog extends Vue {
  @Prop()
  importResultDialogShow: boolean

  @Prop()
  importResultCallback: any

  importResult = false // 导入结果
  backUrl = ''
  errorCount = 0
  ignoreCount = 0
  successCount = 0

  mounted() {
    this.importResultCallback().then((resp: any) => {
      if (resp && resp.success) {
        this.backUrl = resp.data.backUrl
        this.errorCount = resp.data.errorCount
        this.ignoreCount = resp.data.ignoreCount
        this.successCount = resp.data.successCount
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  doBeforeClose(done: any) {
    this.$emit('importResultDialogClose')
    done()
  }
  doModalClose(type: string) {
    this.$emit('importResultDialogClose')
  }
  doDownload() {
    window.open(this.backUrl, '_blank')
  }
}