/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2023-12-06 13:46:56
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\common\PickUpGoods.ts
 * 记得注释
 */
import IdName from 'model/common/IdName'

export default class PickUpGoods {
  //商品
  goods: Nullable<IdName> = null
  // 品牌
  brand: Nullable<IdName> = null
  // 品类
  category: Nullable<IdName> = null
  //价格
  price: Nullable<number> = null
  //提货数量(或限量件数)
  qty: Nullable<number> = null
  // 商品规格，格式如：1*1
  qpcStr: Nullable<string> = null
  // 记账金额
  bookPayPrice: Nullable<number> = null
  // 是否称重商品 isDisp，默认false,代表件数, true代表千克
  isDisp: Nullable<boolean> = null
  // 商品代码
  code: Nullable<string> = null
  // 是否是增值商品
  appreciationGoods: Nullable<boolean> = false
}