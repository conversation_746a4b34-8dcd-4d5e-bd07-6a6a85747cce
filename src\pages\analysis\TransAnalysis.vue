<template>
  <div class="tran-analysis">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button size="large" @click="doExport" v-if="hasOptionPermission('/数据/分析/门店会员交易概况', '数据导出')">
          {{ formatI18n('/分析/门店会员增长概况/门店会员增长概况', '导出报表') }}
        </el-button>
      </template>
    </BreadCrume>
    <div class="content">
      <el-row style="margin-top: 15px">
        <el-col :span="18">
          <form-item :label="formatI18n('/分析/门店会员增长概况/门店会员增长概况', '筛选时段')" style="margin-left: -10px">
            <el-radio-group style="position: relative; top: 4px;" v-model="time">
              <el-radio-button :label="formatI18n('/公用/日期', '今天')"></el-radio-button>
              <el-radio-button :label="formatI18n('/公用/日期', '近7天')"></el-radio-button>
              <el-radio-button :label="formatI18n('/公用/日期', '近30天')"></el-radio-button>
              <el-radio-button :label="formatI18n('/分析/门店会员增长概况/门店会员增长概况', '自定义')"></el-radio-button>
            </el-radio-group>
            <el-date-picker :end-placeholder="formatI18n('/公用/券模板', '结束时间')" format="yyyy-MM-dd" range-separator="-" ref="selectDate" size="small"
              :start-placeholder="formatI18n('/公用/券模板', '开始时间')" style="margin-left: 20px;margin-right: 10px;    position: relative; top: 4px;"
              type="daterange" v-if="time === formatI18n('/分析/门店会员增长概况/门店会员增长概况', '自定义')" v-model="selectDate" value-format="yyyy-MM-dd">
            </el-date-picker>
            <el-button style="position: relative; top: 3px;" @click="doCustomQuery" size="small" type="primary"
              v-if="time === formatI18n('/分析/门店会员增长概况/门店会员增长概况', '自定义')">{{ formatI18n('/公用/券模板', '查询') }}
            </el-button>
          </form-item>

        </el-col>
        <el-col :span="6">
          <form-item :label="formatI18n('/公用/券模板', '门店')" style="float: right;margin-right: 24px;">
            <SelectStores v-model="curStore" @change="doStoreChange" :isOnlyId="true" :hideAll="false" width="300px"
              :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
            </SelectStores>
          </form-item>
        </el-col>
      </el-row>
      <div class="gird" v-loading="barChartLoading">
        <div class="grid-item">
          <div id="myEcharts" style="height: 350px;width: 600px" :style="{width: lineWidth}"></div>
          <div class="bottom-desc" style="bottom: -3px">
            <div class="left"><span class="dot color3"></span>{{ formatI18n('/分析/门店会员增长概况/门店会员交易概况', '会员客单价（元）') }}<span
                class="count">{{ analysisCount.memberPerPrice | fmt }}</span></div>
            <div class="left"><span class="dot color4"></span>{{ formatI18n('/分析/门店会员增长概况/门店会员交易概况', '客单价（元）') }}<span
                class="count">{{ analysisCount.perPrice | fmt }}</span></div>
          </div>
        </div>
        <div class="grid-item left-border">
          <el-progress :percentage="getFirstPercent" :stroke-width="18" :width="220" color="#FC044C" type="circle">
          </el-progress>
          <div class="bottom-desc">
            <div class="left"><span class="dot color1"></span>{{ formatI18n('/分析/门店会员增长概况/门店会员交易概况', '会员交易额（元）') }}
              <span class="count" v-if="analysisCount.memberTradeAmount || analysisCount.memberTradeAmount === 0">{{
                  analysisCount.memberTradeAmount | fmt
                }}</span>
              <span class="count" v-else>0.00</span>
            </div>
            <div class="left"><span class="dot"></span>{{ formatI18n('/分析/门店会员增长概况/门店会员交易概况', '交易总额（元）') }}
              <span class="count" v-if="analysisCount.tradeAmount || analysisCount.tradeAmount === 0">{{
                  analysisCount.tradeAmount | fmt
                }}</span>
              <span class="count" v-else>0.00</span>
            </div>
          </div>
        </div>
        <div class="grid-item left-border">
          <el-progress :percentage="getSecPercent" :stroke-width="18" :width="220" color="#0CC66D" type="circle">
          </el-progress>
          <div class="bottom-desc">
            <div class="left"><span class="dot color2"></span>{{ formatI18n('/分析/门店会员增长概况/门店会员交易概况', '会员交易数（笔）') }}<span
                class="count">{{ analysisCount.memberTradeCount }}</span></div>
            <div class="left"><span class="dot"></span>{{ formatI18n('/分析/门店会员增长概况/门店会员交易概况', '交易笔数（笔）') }}<span
                class="count">{{ analysisCount.tradeCount }}</span></div>
          </div>
        </div>
      </div>
      <div class="table-wrap">
        <el-table :data="analysisList" border style="width: 100%" v-loading="tableLoading">
          <el-table-column fixed :label="formatI18n('/公用/券模板', '门店')" prop="store" width="150" />
          <el-table-column :label="formatI18n('/分析/门店会员增长概况/门店会员交易概况', '会员客单价（元）')" fixed prop="memberPerPrice" width="120">
            <template slot-scope="scope">
              {{ scope.row.memberPerPrice | fmt }}
            </template>
          </el-table-column>
          <el-table-column :label="formatI18n('/分析/门店会员增长概况/门店会员交易概况', '客单价（元）')" fixed prop="perPrice" width="110">
            <template slot-scope="scope">
              {{ scope.row.perPrice | fmt }}
            </template>
          </el-table-column>
          <el-table-column :label="formatI18n('/分析/门店会员增长概况/门店会员交易概况', '会员交易额（元）')" prop="memberTradeAmount" width="120" />
          <el-table-column :label="formatI18n('/分析/门店会员增长概况/门店会员交易概况', '交易总额（元）')" prop="tradeAmount" width="110" />
          <el-table-column :label="formatI18n('/分析/门店会员增长概况/门店会员交易概况', '会员交易额占比')" width="120">
            <template slot-scope="scope">
              <div> {{ (scope.row.tradeAmountRate * 100) | fmt }}%</div>
            </template>
          </el-table-column>
          <el-table-column :label="formatI18n('/分析/门店会员增长概况/门店会员交易概况', '会员交易数（笔）')" prop="memberTradeCount" width="120" />
          <el-table-column :label="formatI18n('/分析/门店会员增长概况/门店会员交易概况', '交易总数（笔）')" prop="tradeCount" width="110"></el-table-column>
          <el-table-column :label="formatI18n('/分析/门店会员增长概况/门店会员交易概况', '会员交易数占比')" width="120">
            <template slot-scope="scope">
              <div> {{ (scope.row.tradeCountRate * 100) | fmt }}%</div>
            </template>
          </el-table-column>
          <el-table-column :label="formatI18n('/分析/门店会员增长概况/门店会员交易概况', '会员退货额(元)')" prop="refundMemberAmount" width="120">
            <template slot-scope="scope">
              {{ scope.row.refundMemberAmount | fmt }}
            </template>
          </el-table-column>
          <el-table-column :label="formatI18n('/分析/门店会员增长概况/门店会员交易概况', '退货总额(元)')" prop="refundAmount" width="120">
            <template slot-scope="scope">
              {{ scope.row.refundAmount | fmt }}
            </template>
          </el-table-column>
          <el-table-column :label="formatI18n('/分析/门店会员增长概况/门店会员交易概况', '会员退货数(笔)')" prop="refundMemberTradeQty" width="120" />
          <el-table-column :label="formatI18n('/分析/门店会员增长概况/门店会员交易概况', '退货总数(笔)')" prop="refundTradeQty" width="120" />
        </el-table>
      </div>
      <div class="footer">
        <el-pagination :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
          @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background layout="total, prev, pager, next, sizes,  jumper">
        </el-pagination>
      </div>
    </div>
    <DownloadCenterDialog :dialogvisiable="dialogvisiable" :showTip="showTip" @dialogClose="doDialogClose">
    </DownloadCenterDialog>
  </div>
</template>

<script lang="ts" src="./TransAnalysis.ts">
</script>

<style lang="scss">
.tran-analysis {
  width: 100%;
  height: 100%;
  background-color: white;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .gird {
    display: flex;
    margin-left: 24px;
    margin-right: 24px;
    margin-top: 20px;

    .grid-item {
      height: 380px;
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      .bottom-desc {
        width: 100%;
        position: relative;
        bottom: -50px;
        padding-left: 44px;
        padding-right: 44px;
        color: rgba(90, 95, 102, 1);

        .left {
          text-align: left;
          margin-top: 10px;
        }

        .dot {
          display: inline-block;
          width: 16px;
          height: 16px;
          border-radius: 100%;
          position: relative;
          top: 2px;
          margin-right: 5px;
        }

        .color1 {
          background-color: #fc044c;
        }

        .color2 {
          background-color: #0cc66d;
        }

        .color3 {
          background-color: #016cff;
        }

        .color4 {
          background-color: #ffaa00;
        }

        .count {
          float: right;
          font-size: 14px;
          font-weight: 500;
        }
      }
    }

    .left-border {
      border-left: 1px solid rgba(238, 239, 241, 1);
    }
  }

  .content {
    background-color: white;
    flex: 1;
    overflow: auto;

    .title {
      margin: 24px;
      font-size: 16px;
      font-weight: 500;
      color: rgba(36, 39, 43, 1);
      line-height: 24px;
    }
  }

  .table-wrap {
    margin: 30px 24px;
  }

  .footer {
    margin: 24px;
  }

  .line {
    height: 16px;
    background-color: #eeeff1;
  }

  .vertical {
    height: 100%;
    width: 16px;
    background-color: #eeeff1;
  }

  .sketch {
    flex: 1;
    border-radius: 10px;

    .name {
      margin-top: 28px;
      margin-left: 20px;
      font-size: 14px;
      font-weight: 500;
      color: rgba(90, 95, 102, 1);
    }

    .value {
      margin-top: 16px;
      margin-left: 20px;
      font-size: 32px;
      font-weight: bold;
      color: rgba(36, 39, 43, 1);
    }
  }

  .top-tab {
    height: 136px;
    border-radius: 10px;
    display: flex;

    .back {
    }
  }
}
</style>