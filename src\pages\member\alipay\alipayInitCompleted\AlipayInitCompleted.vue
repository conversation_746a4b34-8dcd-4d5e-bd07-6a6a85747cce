<template>
  <div class="alipay-init">
    <div class="flex-init">
      <div class="wechat-item">
        <div class="item-num">1</div>
        <div class="item-right">
          <h4 class="item-title">{{i18n('支付宝会员卡')}}</h4>
          <p class="item-word">{{i18n('用以维护支付宝会员卡卡面信息、会员识别方式、栏位、开卡信息等')}}</p>
          <div class="plain-btn-blue" @click="toWechatAuthorizeAfter">查看详情</div>
        </div>
      </div>
      <div class="wechat-item">
        <div class="item-num">2</div>
        <div class="item-right">
          <h4 class="item-title">{{i18n('会员卡投放')}}</h4>
          <p class="item-word">{{i18n('可根据需求选择不同的投放方式，生成领卡二维码')}}</p>
          <div class="plain-btn-blue" @click="toWechatMemberCard">{{i18n('/会员/微信会员初始化/已授权已初始化/微信会员卡投放/投放')}}</div>
        </div>
      </div>
    </div>

    <el-dialog title="会员卡投放" :close-on-click-modal="false" :visible.sync="dialogCodeVisible" width="40%" height="60%" class="inner-dialog-center">
      <div class="init-code-define">{{i18n('点击下载按钮下载二维码，您可以将其设计成海报张贴到门店来吸引顾客领卡成为会员，也可通过其他渠道来投放')}}</div>
      <div class="init-code-item" @click="goAllStoresQrCode">
        <i class="iconfont ic-download"></i>
        {{formatI18n('/会员/微信会员初始化/已授权/初始化第三步投放微信会员卡/所有门店使用同一个二维码')}}
        <VueQrcode v-if="qrCodeUrl" id="qrcode" :value="qrCodeUrl" style="display: none"></VueQrcode>
      </div>
      <div class="init-code-item" @click="goStoreQrCode">
        <i class="iconfont ic-download"></i>
        {{formatI18n('/会员/微信会员初始化/已授权/初始化第三步投放微信会员卡/不同门店使用不同二维码')}}
      </div>
      <div class="init-code-item" @click="doCopyLink">
        <i class="iconfont ic-download"></i>
        {{formatI18n('/会员/微信会员初始化/已授权已初始化/微信会员卡投放弹框/点击复制领卡链接/复制领卡链接')}}
      </div>
    </el-dialog>
    <StoreCodeDownload ref="downloadDialog"></StoreCodeDownload>
    <CopyCardLink type="ali" ref="copyCardLink"></CopyCardLink>
  </div>
</template>

<script lang="ts" src="./AlipayInitCompleted.ts">
</script>

<style lang="scss">
.alipay-init {
  width: 100%;
  height: 100%;
  background-color: white;
  padding: 0;
  .inner-dialog-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .flex-init {
    display: flex;
    padding: 30px 50px;
    .wechat-item {
      display: flex;
      width: 30%;
      height: 200px;
      border: 1px solid #dfe2e5;
      font-family: "微软雅黑", Helvetica, Arial, sans-serif;
      text-align: center;
      margin-right: 16px;
      .item-num {
        width: 110px;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 100px;
        font-family: DIN, DIN;
        font-weight: bold;
        color: #d0d4da;
        background: linear-gradient(10deg, #f1f5f9 0%, #ffffff 100%);
      }
      .item-right {
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding-left: 40px;
        .item-title {
          font-size: 20px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 600;
          color: #24272b;
          width: 100%;
          text-align: left;
        }
        .item-word {
          margin-top: 12px;
          width: 100%;
          text-align: left;
          font-family: PingFangSC, PingFang SC;
          font-size: 14px;
          font-weight: 400;
          color: #5a5f66;
          line-height: 1.5;
        }
        .plain-btn-blue {
          width: 110px;
          margin-top: 20px;
        }
      }
    }
  }
  .el-dialog__header {
    height: auto;
    border-bottom: 1px solid #e2e2e2;
    padding: 5px 20px;
    line-height: 2;
  }
  .el-dialog__headerbtn .el-dialog__close {
    font-size: 16px;
    font-weight: 600;
    padding-top: 5px;
  }
  .init-code-define {
    padding: 10px;
    font-size: 13px;
    font-weight: 500;
    line-height: 1.6;
    color: #676a6c;
  }
  .init-code-item {
    width: 300px;
    display: flex;
    justify-self: center;
    align-items: center;
    font-size: 16px;
    font-weight: 400;
    color: #676a6c;
    border: 1px solid #e2e2e2;
    border-radius: 5px;
    margin: 20px 0 20px 30px;
    padding: 8px 15px;
    cursor: pointer;
    .ic-download {
      font-size: 25px;
      font-weight: 600;
      color: #ffff;
      border-radius: 50%;
      background-color: #33cb98;
      padding: 5px;
      margin-right: 15px;
    }
  }
}
</style>