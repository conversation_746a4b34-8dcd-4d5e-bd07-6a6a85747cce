import Payment from "model/common/Payment"

// 预付卡充值单请求
export default class BCardDepositBillImportRequest {
  // 发生组织
  orgId: Nullable<string> = null
  // 客户
  clientId: Nullable<string> = null
  // 充值类型
  depositType: Nullable<string> = null
  // 单号
  billNumber: Nullable<string> = null
  // 优惠总金额
  favAmount: Nullable<number> = null
  // 应付金额
  amount: Nullable<number> = null
  // 支付方式
  payTypes: Payment[] = []
  // 备注
  remark: Nullable<string> = null
}