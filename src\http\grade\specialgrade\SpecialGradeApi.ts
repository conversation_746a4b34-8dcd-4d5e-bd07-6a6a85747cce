/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:19
 * @LastEditTime: 2024-04-29 16:56:57
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\grade\specialgrade\SpecialGradeApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import GradeBenefitRule from 'model/grade/GradeBenefitRule'
import Response from 'model/common/Response'
import SpecialGrade from 'model/grade/specialgrade/SpecialGrade'
import SpecialGradeDetailBody from 'model/grade/specialgrade/SpecialGradeDetailBody'

export default class SpecialGradeApi {
  /**
   * 追加特殊等级信息
   *
   */
  static appendRule(body: SpecialGrade): Promise<Response<void>> {
      return ApiClient.server().post(`/v1/grade/special/appendRule`, body, {}).then((res) => {
          return res.data
      })
  }

  /**
   * 查询特殊等级权益规则
   *
   */
  static getGradeBenefitRule(): Promise<Response<GradeBenefitRule>> {
      return ApiClient.server().get(`/v1/grade/special/getGradeBenefitRule`, {}).then((res) => {
          return res.data
      })
  }

    /**
     * 查询特殊等级信息
     *
     */
    static getRule(): Promise<Response<SpecialGradeDetailBody>> {
        return ApiClient.server().get(`/v1/grade/special/get`, {}).then((res) => {
            return res.data
        })
    }

  /**
   * 修改特殊等级权益规则
   *
   */
  static modifyBenefitRule(body: GradeBenefitRule): Promise<Response<void>> {
      return ApiClient.server().post(`/v1/grade/special/modifyBenefitRule`, body, {}).then((res) => {
          return res.data
      })
  }

  /**
   * 追加特殊等级前置信息
   *
   */
  static preAppendRule(): Promise<Response<SpecialGrade>> {
      return ApiClient.server().get(`/v1/grade/special/preAppendRule`, {}).then((res) => {
          return res.data
      })
  }

  /**
   * 保存特殊等级信息
   *
   */
  static saveRule(body: SpecialGrade): Promise<Response<boolean>> {
      return ApiClient.server().post(`/v1/grade/special/save`, body, {}).then((res) => {
          return res.data
      })
  }

}
