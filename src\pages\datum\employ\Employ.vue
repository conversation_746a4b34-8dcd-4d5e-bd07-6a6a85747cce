<template>
    <div class="employee-view">
        <BreadCrume :panelArray="panelArray">
            <template slot="operate">
            </template>
        </BreadCrume>
        <div class="current-page">
            <div class="query">
                <FloatBlock refClass="current-page" :top="95" style="padding: 10px 10px;">
                    <template slot="ctx">
                        <el-row>
                          <el-col :span="12" style="line-height: 36px">
                            {{formatI18n('/资料/员工/共')}}
                            <span class="number-text">{{page.total}}</span>
                            {{formatI18n('/资料/员工/名员工')}}
                          </el-col>
                            <el-col :span="12" style="text-align: right">
                                <form-item label="">
                                  <el-input :placeholder="formatI18n('/资料/员工/搜索员工代码/姓名')" @change="doSearch"
                                            v-model="query.idNameLikes"
                                            suffix-icon="el-icon-search" style="width: 280px"></el-input>
                                </form-item>
                            </el-col>
                        </el-row>
                    </template>
                </FloatBlock>
            </div>
            <div class="list">
              <el-table
                  :data="queryData">
                <el-table-column fixed :label="formatI18n('/资料/员工/员工代码')" prop="employee.id"/>
                <el-table-column fixed :label="formatI18n('/资料/员工/姓名')" prop="employee.name"/>
                <el-table-column fixed :label="formatI18n('/资料/员工/联系方式')" prop="mobile"/>
                <el-table-column fixed :label="formatI18n('/资料/员工/所属组织')" prop="affiliatedOrgCode"/>
              </el-table>
            </div>
            <div class="page">
                <el-pagination
                    :current-page="page.currentPage"
                    :page-size="page.size"
                    :page-sizes="[10, 20, 30, 40]"
                    :total="page.total"
                    @current-change="onHandleCurrentChange"
                    @size-change="onHandleSizeChange"
                    background
                    layout="total, prev, pager, next, sizes,  jumper">
                </el-pagination>
            </div>
        </div>
    </div>
</template>

<script lang="ts" src="./Employ.ts">
</script>

<style lang="scss">
    .employee-view{
        background-color: white;
        height: 100%;
        width: 100%;
        overflow: hidden;
        .total {
            margin: 20px;
        }

        .current-page {
            height: calc(100% - 77px);
            overflow: auto;
            padding: 10px;

            .query {
            }

            .list {
                .el-table {
                    width: calc(100% - 20px);
                    margin: 0 10px;
                }
            }

            .page {
                padding: 10px 10px;
            }

            .el-select {
                width: 100%;
            }

            .el-col {
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }

        .el-range-editor.el-input__inner{
            width: 100%;
        }
    }
</style>
