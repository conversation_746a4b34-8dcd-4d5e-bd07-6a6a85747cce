import {Component, Prop, Vue} from 'vue-property-decorator'

@Component({
  name: 'ImportResultDialog',
  components: {}
})
export default class ImportResultDialog extends Vue {
  @Prop()
  data: any
  @Prop()
  dialogShow: boolean

  doBeforeClose(done: any) {
    this.$emit('importResultDialogClose')
    done()
  }
  doModalClose(type: string) {
    this.$emit('importResultDialogClose')
  }
  doDownload() {
    window.open(this.data.backUrl, '_blank')
  }
  doClose() {
    this.$emit('importResultDialogClose')
  }
}