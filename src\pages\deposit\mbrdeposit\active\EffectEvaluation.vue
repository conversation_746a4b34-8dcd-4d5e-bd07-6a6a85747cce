<template>
    <div class="effect-evaluation-view">
        <SubHeader title="储值充值活动效果评估">
            <template slot="btn">
                <!--                <el-button type="primary" @click="doExport">导出图片</el-button>-->
                <el-button @click="doBack">返回</el-button>
            </template>
        </SubHeader>
        <div class="top-wrap">
            <div class="item">
                <el-tag v-if="row && row.body">{{row.body.state | storeValueFmt}}</el-tag>
            </div>
            <div class="item">
                <div v-if="row && row.body" class="margin margin1">{{row.body.name}}</div>
                <div v-if="row && row.body" class="margin margin2">
                  <i18n k="/储值/会员储值/储值充值活动/效果评估/活动时间：{0}至{1}，共{2}天">
                    <template slot="0">{{row.body.beginDate | dateFormate2}}</template>
                    <template slot="1">{{getEndDate | dateFormate2}}</template>
                    <template slot="2">{{getDay}}</template>
                  </i18n>
                </div>
            </div>
        </div>
        <div class="content-wrap">
            <div style="margin-top: 20px;margin-bottom: 20px">
                <span style="position: relative;top:5px;display: inline-block;height: 25px;background: #318bff;width: 5px;"></span>
                <span style="font-size: 20px;margin-left: 10px">数据总览</span>
                <span style="margin-left: 20px">指标说明</span>
                <el-tooltip  effect="light" placement="bottom">
                    <i class="iconfont  ic-info" style="font-size: 18px;color: #20A0FF"></i>
                    <template slot="content">
                        <div><span>{{i18n('指标说明')}}</span>
                            <div>{{i18n('充值储值增加=充值实充增加+充值返现增加')}}</div>
                            <div>{{i18n('实充=充值实充增加')}}</div>
                            <div>{{i18n('返现=充值返现增加')}}</div>
                            <div>{{i18n('充值客单价=平均每次实充增加')}}</div>
                        </div>
                    </template>
                </el-tooltip>
            </div>
            <el-block-panel>
                <el-block-panel-item>
                    <div class="height-80">
                        <div class="amount color-default"><div class="amount">{{evaluate.amount | fmt}}</div></div>
                        <div class="amount-desc">实充金额（元）</div>
                    </div>
                </el-block-panel-item>
                <el-block-panel-item>
                    <div class="height-80">
                        <div class="amount color-default"><div class="amount">{{evaluate.totalAmount | fmt}}</div></div>
                        <div class="amount-desc">储值金额（元）</div>
                    </div>
                </el-block-panel-item>
                <el-block-panel-item>
                    <div class="height-80">
                        <div class="amount color-default"><div class="amount">{{evaluate.giftAmount | fmt}}</div></div>
                        <div class="amount-desc">返现金额（元）</div>
                    </div>
                </el-block-panel-item>
                <el-block-panel-item>
                    <div class="height-80">
                        <div class="amount color-default"><div class="amount">{{evaluate.depositTime}}</div></div>
                        <div class="amount-desc">充值次数（次）</div>
                    </div>
                </el-block-panel-item>
                <el-block-panel-item>
                    <div class="height-80">
                        <div class="amount color-default"><div class="amount">{{evaluate.avtAmount | fmt}}</div></div>
                        <div class="amount-desc">充值客单价（元）</div>
                    </div>
                </el-block-panel-item>
            </el-block-panel>
        </div>
    </div>
</template>

<script lang="ts" src="./EffectEvaluation.ts">
</script>

<style lang="scss">
    .effect-evaluation-view{
        width: 100%;
        height: 100%;
        background-color: white;
        overflow: hidden;
        .top-wrap{
            display: flex;
            padding-top: 30px;
            padding-left: 30px;
            .item{
                .margin{
                    margin-left: 20px;
                }
                .margin1{
                    font-size: 18px;
                    font-weight: bold;
                }
                .margin2{
                    margin-top: 10px;
                }
            }
        }
        .content-wrap{
            margin: 30px;
        }
        .height-80{
            height: 80px;
            .amount{
                font-size: 24px;
                font-weight: 500;
                display: block;
                padding: 0 10px;
                margin-top: 10px;
                text-align: center;
            }
            .amount-desc{
                margin-top: 10px;
                text-align: center;
            }
            .color-default{
                color: black;
            }
        }
    }
</style>