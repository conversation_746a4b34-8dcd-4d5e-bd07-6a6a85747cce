<template>
  <div class="costparty-view">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
      </template>
    </BreadCrume>
    <div class="current-page">
      <div style="margin: 20px;line-height: 40px" v-if="hasOptionPermission('/设置/资料/券承担方', '资料维护')">
        <el-row>
          <el-col :span="2" style="min-width: 100px">{{formatI18n('/资料/券承担方/券承担方代码')}}</el-col>
          <el-col :span="4">
            <el-input :placeholder="formatI18n('/资料/券承担方/请输入券承担方拼音或英文名')" v-model="newIns.costParty.costParty.id"
                      style="width: 280px"/>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="2" style="min-width: 100px">{{formatI18n('/资料/券承担方/券承担方名称')}}</el-col>
          <el-col :span="4">
            <el-input v-model="newIns.costParty.costParty.name"
                      style="width: 280px"/>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="2" style="min-width: 100px">&nbsp;</el-col>
          <el-col :span="8">
            <i class="el-icon-warning"/> {{formatI18n('/资料/券承担方/代码不允许与已有券承担方重复')}}
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="2" style="min-width: 100px">&nbsp;</el-col>
          <el-col :span="4">
            <el-button class="btn-search" @click="add" type="primary">{{formatI18n('/营销/积分活动/新建门店积分兑换活动/表格/添加')}}</el-button>
            <el-button class="btn-reset" type="normal" @click="clear">{{formatI18n('/资料/券承担方/清空')}}</el-button>
          </el-col>
        </el-row>
      </div>
      <ListWrapper style="overflow: initial" :showQuery="hasOptionPermission('/设置/资料/券承担方', '资料维护')">
        <template slot="list">
          <FloatBlock refClass="current-page" :top="95" style="padding: 5px;">
            <template slot="ctx">
              <el-row>
                <el-col :span="12" style="line-height: 36px" v-if="hasOptionPermission('/设置/资料/券承担方', '资料维护')">
                  <el-checkbox v-model="checkedAll" style="padding-left: 10px;" @change="checkedAllRow"/>
                  {{formatI18n('/资料/券承担方/已选择')}}
                  <span class="number-text">{{selected.length}}</span>
                  {{formatI18n('/资料/券承担方/个券承担方')}}
                  <el-button
                      @click="deleteBatch" type="danger" style="margin-left:6px">{{formatI18n('/资料/券承担方/批量删除')}}
                  </el-button>
                </el-col>
                <el-col :span="12" style="line-height: 36px" v-if="!hasOptionPermission('/设置/资料/券承担方', '资料维护')">
                  &nbsp;
                </el-col>
                <el-col :span="12" style="text-align: right">
                  <el-input :placeholder="formatI18n('/资料/券承担方/搜索券承担方代码/名称')" @change="doSearch"
                            v-model="query.idNameLikes"
                            suffix-icon="el-icon-search" style="width: 280px"/>
                </el-col>
              </el-row>
            </template>
          </FloatBlock>
          <el-table
              ref="table"
              :data="queryData"
              style="width: 100%;margin-top: 10px;"
              @selection-change="handleSelectionChange">
            <el-table-column
                v-if="hasOptionPermission('/设置/资料/券承担方', '资料维护')"
                type="selection"
                width="55">
            </el-table-column>
            <el-table-column fixed :label="formatI18n('/资料/券承担方/券承担方代码')" prop="costParty.id"/>
            <el-table-column fixed :label="formatI18n('/资料/券承担方/券承担方名称')" prop="costParty.name"/>
            <el-table-column fixed :label="formatI18n('/资料/券承担方/操作')" v-if="hasOptionPermission('/设置/资料/券承担方', '资料维护')">
              <template slot-scope="scope">
                <span class="span-btn" v-if="scope.row.costParty.id != 'headquarters' && scope.row.costParty.id != 'store'" @click="showUpdateDialog(scope.row)">{{formatI18n('/资料/券承担方/修改')}}</span>
                <span class="span-btn" v-if="scope.row.costParty.id != 'headquarters' && scope.row.costParty.id != 'store'" style="margin-left:8px" @click="del(scope.row.costParty.id)">{{formatI18n('/资料/券承担方/删除')}}</span>
              </template>
            </el-table-column>
          </el-table>
        </template>
        <!--{{formatI18n('/资料/券承担方/分页栏')}}-->
        <template slot="page">
          <el-pagination
              :current-page="page.currentPage"
              :page-size="page.size"
              :page-sizes="[10, 20, 30, 40]"
              :total="page.total"
              @current-change="onHandleCurrentChange"
              @size-change="onHandleSizeChange"
              background
              layout="total, prev, pager, next, sizes,  jumper">
          </el-pagination>
        </template>
      </ListWrapper>
    </div>
    <el-dialog
        :title="formatI18n('/资料/券承担方/修改')"
        :visible.sync="dialogVisible"
        class="cosparty-dialog-center"
        width="30%">
      <div style="margin: 20px;">
        <el-row>
          <el-col :span="8" style="height: 50px; line-height: 30px">{{formatI18n('/资料/券承担方/券承担方代码')}}</el-col>
          <el-col :span="16">
            <el-input :placeholder="formatI18n('/资料/券承担方/请输入券承担方拼音或英文名')" v-model="updateIns.costParty.costParty.id"/>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8" style="height: 50px; line-height: 30px">{{formatI18n('/资料/券承担方/券承担方名称')}}</el-col>
          <el-col :span="16">
            <el-input v-model="updateIns.costParty.costParty.name"/>
          </el-col>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">{{formatI18n('/资料/券承担方/取 消')}}</el-button>
                <el-button type="primary" @click="update">{{formatI18n('/资料/券承担方/确 定')}}</el-button>
              </span>
    </el-dialog>
  </div>
</template>

<script lang="ts" src="./Costparty.ts">
</script>

<style lang="scss">
  .costparty-view {
    background-color: white;
    height: 100%;
    width: 100%;
    overflow: hidden;
    .cosparty-dialog-center{
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .total {
      margin: 20px;
    }

    .current-page {
      height: calc(100% - 77px);
      overflow: auto;

      .el-select {
        width: 100%;
      }
    }
  }
</style>
