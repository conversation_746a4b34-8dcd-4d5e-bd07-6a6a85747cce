/*
 * @Author: 黎钰龙
 * @Date: 2024-03-06 14:50:02
 * @LastEditTime: 2024-05-31 09:40:55
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\analysis\OrgMemberAnalysisReportQuery.ts
 * 记得注释
 */
import { AnalysisReportDateUnit } from "./AnalysisReportDateUnit"

// 门店会员分析报表查询条件
export default class PlatformCouponAnalysisReportQuery {
  // 日期单位
  dateUnitEquals: Nullable<AnalysisReportDateUnit> = null
  // 开始日期
  startDate: Nullable<string> = null
  // 结束日期
  endDate: Nullable<string> = null
  // 运营经理
  operationEquals: Nullable<string> = null
  // 区域主管
  areaLeaderEquals: Nullable<string> = null
  // 核销渠道类型等于
  channelTypeEquals: Nullable<string> = null
  // 核销渠道ID等于
  channelIdEquals: Nullable<string> = null
  // 归属门店ID等于
  storeIdEquals: Nullable<string> = null
  // 发券渠道等于
  issueChannelTypeEquals: Nullable<string> = null
}