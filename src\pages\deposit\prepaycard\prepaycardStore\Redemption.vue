<template>
    <div>
        <el-dialog :visible.sync="dialogVisible" width="1100px" height="658px" :title="i18n('/营销/兑换码单据/码库')">
            <div>
                <el-form :inline="true" :model="searchForm" class="demo-form-inline">
                    <el-row>
                        <el-col :span="8">
                            <el-form-item :label="i18n('/会员/会员资料/权益卡/状态')">
                                <el-select v-model="searchForm.stateEquals" placeholder="请选择状态">
                                    <el-option :label="i18n('/公用/活动/状态/全部')" :value="null"></el-option>
                                    <el-option :label="i18n('/营销/券礼包活动/券查询/券状态下拉选项/未使用')" value="ISSUED"></el-option>
                                    <el-option :label="i18n('/营销/券礼包活动/券查询/券状态下拉选项/已使用')" value="WRITTEN_OFF"></el-option>
                                    <el-option :label="i18n('/营销/券礼包活动/券查询/券状态下拉选项/已过期')" value="EXPIRE"></el-option>
                                    <el-option :label="i18n('/权益/券/券模板/券模板状态下拉选项/已作废')" value="CANCELLED"></el-option>
                                    <el-option :label="i18n('/公用/过滤器/未开始')" value="NOT_STARTED"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item :label="i18n('/营销/兑换码单据/码值')" width="150">
                                <el-input v-model="searchForm.codeIn" :placeholder="i18n('/营销/兑换码单据/请输入完整码值')"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item :label="i18n('/营销/兑换码单据/使用人')">
                                <el-input v-model="searchForm.memberIdentEquals" :placeholder="i18n('/营销/兑换码单据/请输入手机号或会员号')"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20" style="margin-top: 5px;">
                            <el-col :span="24" class="button-group">
                                <el-form-item>
                                    <el-button type="primary" @click="handleSearch">{{formatI18n('/公用/券模板', '查询')}}</el-button>
                                    <el-button @click="handleReset">{{formatI18n('/公用/券模板', '重置')}}</el-button>
                                </el-form-item>
                            </el-col>
                            <el-col :span="24" style="margin-top: -10px;">
                                <el-form-item>
                                    <el-button plain v-if="hasOptionPermission('/营销/营销/兑换码/兑换码', '作废码库')" @click="batchCancel">{{formatI18n('/营销/券礼包活动/券查询', '批量作废')}}</el-button>
                                    <el-button plain v-if="hasOptionPermission('/营销/营销/兑换码/兑换码', '导出码库')" @click="exportData">{{formatI18n('/储值/预付卡/充值卡制售单/列表页面', '导出')}}</el-button>
                                </el-form-item>
                            </el-col>
                        </el-row>
                </el-form>

                <el-table :data="tableData" style="width: 100%"  @selection-change="handleSelectionChange" >
                    <el-table-column type="selection" width="55"></el-table-column>
                    <el-table-column prop="code" :label="i18n('/营销/兑换码单据/码值')" width="180"></el-table-column>

                    <el-table-column prop="crmCode" :label="i18n('/会员/会员资料/会员号')" width="150">
                        <template slot-scope="scope">
                            <span>{{ scope.row.crmCode ? scope.row.crmCode : '--' }}</span>
                        </template>
                    </el-table-column>

                    <el-table-column prop="mobile" :label="i18n('/会员/会员资料/手机号')" width="200">
                        <template slot-scope="scope">
                            <span>{{ scope.row.mobile ? scope.row.mobile : '--' }}</span>
                        </template>
                    </el-table-column>

                    <el-table-column :label="i18n('/会员/会员资料/更新时间')" width="150">
                        <template slot-scope="scope">
                            <span>{{ formatUpdateTime(scope.row) }}</span>
                        </template>
                    </el-table-column>

                    <el-table-column :label="i18n('/会员/会员资料/权益卡/状态')" width="150">
                        <template slot-scope="scope">
                            <div style="display:flex;align-items:center">
                                <span class="dot" :style="{background: computeState(scope.row.codeState).color}"></span>
                                <span>{{computeState(scope.row.codeState).state}}</span>
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column :label="i18n('/会员/会员资料/操作')" width="150">
                        <template slot-scope="scope">
                            <template v-if="scope.row.codeState === 'WRITTEN_OFF'">
                                <el-tooltip
                                    class="item"
                                    effect="light"
                                    placement="right"
                                    trigger="click">
									<div slot="content" v-html="formatBenefits(scope.row.gifts)"></div>
                                    <span class="span-btn" >
                                        {{formatI18n('/会员/会员资料', '查看已兑换权益')}}
                                    </span>
                                </el-tooltip>
                            </template>
                            <template v-else-if="scope.row.codeState === 'ISSUED'&&hasOptionPermission('/营销/营销/兑换码/兑换码', '作废码库')">
                                <span class="span-btn" @click="confirmCancel(scope.row.code)">
                                    {{formatI18n('/营销/券礼包活动/券查询/批量作废/title', '作废')}}
                                </span>
                            </template>
                            <template v-else>
                                <span>--</span>
                            </template>
                        </template>
                    </el-table-column>
                </el-table>

                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="paging.page"
                    :page-sizes="[10, 20, 30, 40]"
                    :page-size="paging.pageSize"
                    layout="total, prev, pager, next, sizes,  jumper"
                    :total="total">
                </el-pagination>
            </div>
        </el-dialog>

		<DownloadCenterDialog :dialogvisiable="fileDialogVisible"  @dialogClose="doDownloadDialogClose">
		</DownloadCenterDialog>
    </div>
</template>

<script lang="ts" src="./Redemption.ts"></script>
