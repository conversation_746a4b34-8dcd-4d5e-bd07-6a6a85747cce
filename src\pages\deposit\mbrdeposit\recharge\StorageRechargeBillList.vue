<template>
  <div class="storage-recharge-list-container">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button type="primary" @click="doCreate" v-if="hasOptionPermission('/储值/储值管理/储值充值单','单据维护')" size="large">
          {{ i18n('新建储值充值单') }}
        </el-button>
      </template>
    </BreadCrume>
    <ListWrapper class="current-page" style="height: 95%">
      <template slot="query">
        <el-row>
          <el-col :span="6">
            <form-item :label="i18n('/公用/券核销/单号')">
              <el-input :placeholder="i18n('/储值/预付卡/预付卡查询/列表页面/请输入单号')" v-model="query.numberEquals" style="width:100%" />
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="i18n('/会员/会员资料/会员')">
              <el-input :placeholder="i18n('/储值/预付卡/预付卡查询/列表页面/请输入手机号/会员号')" v-model="query.memberCodeLikes" style="width:100%" />
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="i18n('/公用/券核销/创建时间')">
              <el-date-picker v-model="createTime" :end-placeholder="i18n('结束日期')" format="yyyy-MM-dd" range-separator="-" ref="selectDate"
                size="small" :start-placeholder="i18n('开始日期')" type="daterange" value-format="yyyy-MM-dd">
              </el-date-picker>
            </form-item>
          </el-col>
        </el-row>
        <el-row style="margin-top: 10px">
          <el-col :span="8">
            <form-item>
              <el-button @click="doSearch" type="primary">{{i18n('查询')}}</el-button>
              <el-button @click="doReset">{{i18n('重置')}}</el-button>
            </form-item>
          </el-col>
        </el-row>
      </template>
      <template slot="list">
        <el-tabs @tab-click="doHandleClick" v-model="activeName">
          <el-tab-pane no-i18n :label="getAllCount" name="first">
            <el-checkbox @change="doSelectAll" style="margin-right: 0px" v-model="selectAll"></el-checkbox>&nbsp;&nbsp;
            <i18n k="/储值/会员储值/储值调整单/列表/已选择{0}张单据">
              <template slot="0">&nbsp;{{selectedArr.length}}&nbsp;</template>
            </i18n>
            <el-button style="margin-left: 15px" v-if="hasOptionPermission('/储值/储值管理/储值充值单','单据审核')" @click="doBatchAudit">
              {{i18n('/营销/券礼包活动/券礼包活动/批量审核')}}
            </el-button>
            <el-button v-if="hasOptionPermission('/储值/储值管理/储值充值单','单据维护')" @click="doBatchDelete" style="margin-left: 20px;color: red">
              {{i18n('批量删除')}}
            </el-button>
          </el-tab-pane>
          <el-tab-pane no-i18n :label="getNoAudit" name="second">
            <el-checkbox @change="doSelectAll" style="margin-right: 0px;margin-left: 14px" v-model="selectAll"></el-checkbox>&nbsp;&nbsp;
            <i18n k="/储值/会员储值/储值调整单/列表/已选择{0}张单据">
              <template slot="0">&nbsp;{{selectedArr.length}}&nbsp;</template>
            </i18n>
            <el-button style="margin-left: 15px" v-if="hasOptionPermission('/储值/储值管理/储值充值单','单据审核')" @click="doBatchAudit">
              {{i18n('/营销/券礼包活动/券礼包活动/批量审核')}}
            </el-button>
            <el-button v-if="hasOptionPermission('/储值/储值管理/储值充值单','单据维护')" @click="doBatchDelete" style="margin-left: 20px;color: red">
              {{i18n('批量删除')}}
            </el-button>
          </el-tab-pane>
          <el-tab-pane no-i18n :label="getAudit" name="third"></el-tab-pane>
        </el-tabs>
        <el-table ref="table" :data="tableData" @selection-change="handleSelectionChange" style="width: 100%;margin-top: 20px">
          <el-table-column v-if="activeName !== 'third'" type="selection" width="55"></el-table-column>
          <el-table-column no-i18n fixed :label="i18n('/公用/券核销/单号')" prop="billNumber" width="200">
            <template slot-scope="scope">
              <span no-i18n class="span-btn" @click="doDtl(scope.row.billNumber)">
                {{ scope.row.billNumber || '--' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column no-i18n :label="i18n('/会员/会员资料/权益卡/状态')" prop="state" width="150">
            <template slot-scope="scope">
              <div style="display:flex;align-items:center">
                <span class="dot" :style="{background: computeState(scope.row.state).color}"></span>
                <span>{{computeState(scope.row.state).state}}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column no-i18n :label="i18n('/公用/券核销/创建时间')" width="200" prop="createTime">
            <template slot-scope="scope">
              <span no-i18n>{{ scope.row.create | dateFormate3 }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('/会员/会员资料/最近修改时间')" prop="lastModifyTime" width="150">
            <template slot-scope="scope">
              <span no-i18n>{{ scope.row.lastModified | dateFormate3 }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('/公用/券核销/创建人')" prop="creator" width="150">
            <template slot-scope="scope">
              <span no-i18n>{{ scope.row.creator || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('/公用/券核销/审核人')" prop="auditor" width="150">
            <template slot-scope="scope">
              <span no-i18n>{{ scope.row.reviewer || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('/公用/券核销/发生组织')" prop="occurredOrgId">
            <template slot-scope="scope">
              <span no-i18n>[{{ scope.row.occurredOrgId || '--' }}]{{scope.row.occurredOrgName || '--'}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="i18n('/会员/会员资料/操作')" prop="operation">
            <template slot-scope="scope">
              <span class="span-btn" v-if="canAudit(scope.row)" @click="doAudit(scope.row)">
                {{i18n('/营销/券礼包活动/券礼包活动/审核')}}
              </span>
              <span class="span-btn" v-if="scope.row.state === 'INITIAL' && hasOptionPermission('/储值/储值管理/储值充值单','单据维护')"
                @click="doModify(scope.row)">
                {{i18n('/营销/券礼包活动/券礼包活动/修改')}}
              </span>
              <span class="span-btn" v-if="scope.row.state === 'INITIAL' && hasOptionPermission('/储值/储值管理/储值充值单','单据维护')"
                @click="doRemove(scope.row)">
                {{i18n('/营销/券礼包活动/券礼包活动/删除')}}
              </span>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <!--分页栏-->
      <template slot="page">
        <el-pagination no-i18n :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
          @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background layout="total, prev, pager, next, sizes,  jumper">
        </el-pagination>
      </template>
    </ListWrapper>
  </div>
</template>

<script lang="ts" src="./StorageRechargeBillList.ts">
</script>

<style lang="scss" scoped>
.storage-recharge-list-container {
  width: 100%;
  .span-btn {
    margin: 0 3px;
  }
}
</style>