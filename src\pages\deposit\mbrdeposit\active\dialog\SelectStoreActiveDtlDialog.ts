import {Component, Prop, Vue} from 'vue-property-decorator'
import ActiveAddCouponDtl from "cmp/activeAddcoupondtl/ActiveAddCouponDtl";

@Component({
  name: 'SelectStoreActiveDtlDialog',
  components: {
    ActiveAddCouponDtl,
  }
})
export default class SelectStoreActiveDtlDialog extends Vue {
  @Prop()
  child: any
  @Prop({
    type: Boolean,
    default: false
  })
  dialogShow: boolean
  @Prop({
    type: Boolean,
    default: false
  })
  baseSettingFlag: boolean

  doBeforeClose(done: any) {
    this.$emit('dialogClose')
    done()
  }

  doModalClose() {
    this.$emit('dialogClose')
  }
}
