export default class SettleRuleConfig {
  // 启用劵分账
  couponEnable: Nullable<boolean> = null
  // 出账单的券
  billedCoupons: string[] = []
  // 选择的发券活动
  activities: string[] = []
  // 劵-服务-费率，1表示1%
  rateOfCouponService: Nullable<number> = null
  // 启用储值卡分账
  prepayEnable: Nullable<boolean> = null
  // 储值-本金支付金额-比例，1表示1%
  rateOfPrepayAmount: Nullable<number> = null
  // 储值-增金支付金额-比例，1表示1%
  rateOfPrepayGiftAmount: Nullable<number> = null
  // 储值-服务-费率，1表示1%
  rateOfPrepayService: Nullable<number> = null
  // 启用预付卡分账
  cardEnable: Nullable<boolean> = null
  // 预付卡-本金支付金额-比例，1表示1%
  rateOfCardAmount: Nullable<number> = null
  // 预付卡-增金支付金额-比例，1表示1%
  rateOfCardGiftAmount: Nullable<number> = null
  // 预付卡-服务-费率，1表示1%
  rateOfCardService: Nullable<number> = null
  // 启用积分分账
  pointsEnable: Nullable<boolean> = null
  // 积分-消费得积分-积分数
  consumeObtainPoints: Nullable<number> = null
  //积分-消费得积分-金额
  consumeObtainAmount: Nullable<number> = null
  // 积分-积分兑换商品折现-积分数
  exchangeGoodsPoints: Nullable<number> = null
  // 积分-积分兑换商品折现-消费得积分-金额
  exchangeGoodsAmount: Nullable<number> = null
  // 积分-调整积分折现-积分数
  adjustPoints: Nullable<number> = null
  // 积分-调整积分折现-金额
  adjustAmount: Nullable<number> = null
  // 积分-注册有礼得积分折现-积分数
  registerObtainPoints: Nullable<number> = null
  // 积分-注册有礼得积分折现-金额
  registerObtainAmount: Nullable<number> = null
  // 积分-消费得积分-选择活动
  consumeObtainPointsActivities: string[] = []
  // 积分-消费用积分-积分数
  consumeUserPointsActivities: string[] = []
  // 积分-调整积分折现-积分数
  adjustPointsActivities: string[] = []
  // 积分-注册有礼得积分折现
  registerGiftPointsActivities: string[] = []

  // 积分-服务-费率，1表示1%
  rateOfPointsService: Nullable<number> = null
  // 启用储值卡分账
  rechargeableCardEnable: Nullable<boolean> = null
  // 储值卡-本金支付金额-比例，1表示1%
  rateOfRechargeableCardAmount: Nullable<number> = null
  // 储值卡-增金支付金额-比例，1表示1%
  rateOfRechargeableCardGiftAmount: Nullable<number> = null
  // 储值卡-服务-费率，1表示1%
  rateOfRechargeableCardService: Nullable<number> = null
}