/*
 * @Author: 黎钰龙
 * @Date: 2024-04-11 09:36:16
 * @LastEditTime: 2024-06-06 13:44:11
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\promotion\bigWheelActivity\BigWheelPrize.ts
 * 记得注释
 */
import GiftInfo from "model/common/GiftInfo"
import IdName from "model/common/IdName";

export default class BigWheelPrize {
  // 奖品类型 COUPON_BAG-优惠券；ENTITY-实物奖品；POINTS-积分
  prizeType: Nullable<string> = null
  // 券礼包
  giftBag: Nullable<GiftInfo> = null;
  // 奖品id/名称
  prize: Nullable<IdName> = null
  // 奖品数量
  prizeCount: Nullable<number> = null
  // 中奖限制门槛次数
  thresholdCount: Nullable<number> = null
  // 每人最多中奖次数
  maxPerPersonWinJackpot: Nullable<number> = null
  // 奖品图片
  prizeImage: Nullable<string> = null
  // 奖品说明
  prizeRemark: Nullable<string> = null
}