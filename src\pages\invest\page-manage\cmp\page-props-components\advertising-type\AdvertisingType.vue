<template>
  <div class="activity_place_name">
    <el-form inline :label-position="labelPosition" :model="value" :rules="rules" ref="form">
      <el-form-item :label="label" prop="region" style="width: 100%" class="activity-input">
        <el-select @change="handleChange" v-model="value.propActivityType" placeholder="请选择">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" src="./AdvertisingType.ts"></script>

<style lang="scss" scoped>
.activity_place_name {
  .activity-input {
    margin-bottom: 10px;
    ::v-deep .el-form-item__content {
      width: 100% !important;
      .el-select {
        width: 100%;
      }
    }
  }
}
</style>
