import I18nPage from 'common/I18nDecorator';
import ActivityStateCountResult from 'model/common/ActivityStateCountResult';
import { StateEqualsType } from 'model/v2/coupon/videonumberActivity/StateEqualsType';
import { Vue } from 'vue-property-decorator';
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/公用/活动/状态'
  ],
  auto: true
})
// 基础活动list页面抽象类
export default abstract class AbstractOaActivity extends Vue {
  total = {
    all: 0, //全部
    initial: 0, //未审核
    unstart: 0, //未开始
    doing: 0, //进行中
    end: 0, //已结束
    platformAuditing: 0, //审核中
    platformAuditFail: 0, //已驳回
    suspend: 0, //暂停中
  }

  get getAllCount() {
    return `${this.i18n('/公用/活动/状态/全部')}(${this.total.all})`
  }

  get getNoAudit() {
    return `${this.i18n('/公用/活动/状态/未审核')}(${this.total.initial})`
  }

  get getAuditing() {
    return `${this.i18n('/营销/营销申请/审核中')}(${this.total.platformAuditing})`
  }

  get getReject() {
    return `${this.i18n('/营销/营销申请/已驳回')}(${this.total.platformAuditFail})`
  }

  get getNoStart() {
    return `${this.i18n('/公用/活动/状态/未开始')}(${this.total.unstart})`
  }

  get getDoing() {
    return `${this.i18n('/公用/活动/状态/进行中')}(${this.total.doing})`
  }

  get getEnd() {
    return `${this.i18n('/公用/活动/状态/已结束')}(${this.total.end})`
  }

  get getSuspend() {
    return `${this.formatI18n("/公用/过滤器", "暂停中")}(${this.total.suspend})`;
  }

  handleSumAmount(sum: ActivityStateCountResult) {
    this.total.all = sum.sum || 0
    this.total.initial = sum.initail || 0
    this.total.unstart = sum.unstart || 0
    this.total.doing = sum.processing || 0
    this.total.end = sum.stoped || 0
    this.total.platformAuditFail = sum.platformAuditFail || 0
    this.total.platformAuditing = sum.platformAuditing || 0
    this.total.suspend = sum.suspend || 0
  }

  // tab映射关系
  computeStateEquals(activeName: string) {
    let stateValue = null
    switch (activeName) {
      case 'first': //全部
        stateValue = null
        break;
      case 'second':  //未审核
        stateValue = StateEqualsType.INITAIL
        break;
      case 'third': //未开始
        stateValue = StateEqualsType.UNSTART
        break;
      case 'forth': //进行中
        stateValue = StateEqualsType.PROCESSING
        break;
      case 'five':  //已结束
        stateValue = StateEqualsType.STOPED
        break;
      case 'auditing':  //审核中
        stateValue = StateEqualsType.AUDITING
        break;
      case 'reject':  //已驳回
        stateValue = StateEqualsType.REJECTED
        break;
      case 'suspend':  //暂停中
        stateValue = StateEqualsType.SUSPEND
        break;
      default:
        break;
    }
    return stateValue
  }
};