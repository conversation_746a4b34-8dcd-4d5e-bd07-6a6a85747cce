<!--
 * @Author: 黎钰龙
 * @Date: 2024-08-11 10:09:43
 * @LastEditTime: 2025-05-07 09:57:32
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\prepaycard\writeCard\WriteCard.vue
 * 记得注释
-->
<template>
  <div class="card-write-container">
    <BreadCrume :panelArray="panelArray">
    </BreadCrume>
    <div class="top-header" :style="{height: isEnglish ? '420px' : '340px'}">
      <div class="header-left">
        <el-form ref="form" :model="form" :rules="rules" :label-width="isEnglish ? '120px' : '100px'">
          <el-form-item :label="i18n('/储值/预付卡/预付卡充值单/起始卡号')" prop="startCode">
            <el-input v-model="form.startCode" :disabled="formDisable" style="width:257px" @blur="doCheckStartCode"
              :placeholder="i18n('/卡/卡管理/制卡单/制卡单详情/请填写起始卡号')">
            </el-input>
          </el-form-item>
          <el-form-item :label="i18n('/储值/预付卡/预付卡充值单/结束卡号')" prop="endCode">
            <el-input v-model="form.endCode" :disabled="formDisable" style="width:257px" @blur="doCheckEndCode"
              :placeholder="i18n('/卡/卡管理/制卡单/制卡单详情/请填写结束卡号')">
            </el-input>
          </el-form-item>
          <el-form-item :label="i18n('/卡/卡管理/卡介质/卡介质')">
            <el-radio-group v-model="form.cardMedium" :disabled="formDisable" @change="defaultCardTemplateChange">
              <el-radio label="mag">{{i18n('/卡/卡管理/卡介质/磁条卡')}}</el-radio>
              <el-radio label="rfic">{{i18n('/卡/卡管理/卡介质/RFIC卡')}}</el-radio>
              <el-radio label="ic">{{i18n('IC卡')}}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="i18n('/储值/预付卡支付优惠/指定卡模板')" prop="defaultCardTemplate">
            <el-radio-group v-model="form.defaultCardTemplate" @change="defaultCardTemplateChange" :disabled="formDisable">
              <el-radio :label="true">{{i18n('默认卡模板')}}</el-radio>
              <el-radio :label="false">
                {{i18n('/储值/预付卡支付优惠/指定卡模板')}}
                <span class="span-btn" v-if="!form.defaultCardTemplate" @click="doSelectCardTemplate" style="margin-left: 6px">
                  {{ form.cardTemplate ? form.cardTemplate.name : i18n('选择其他卡模板')}}
                </span>
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="i18n('写卡数量')">
            <div>{{writeCardNum | nullable}}</div>
          </el-form-item>
        </el-form>
        <div class="btn-block" v-if="hasOptionPermission('/卡/卡管理/写卡单', '单据维护')">
          <template v-if="writeState === 'UNSTART'">
            <el-button type="primary" class="start-btn" :disabled="startBtnDisable" @click="startWrite">
              {{i18n('开始写卡')}}
            </el-button>
          </template>
          <template v-else-if="writeState === 'PAUSE' && writeCardInfo && writeCardInfo.nextCardCode">
            <el-button type="primary" class="start-btn" @click="continueWrite">
              {{i18n('继续写卡')}}
            </el-button>
            <el-button class="start-btn" @click="completeWrite">
              {{i18n('结束写卡')}}
            </el-button>
          </template>
          <template v-else-if="writeState === 'PROCESSING' || (writeCardInfo && !writeCardInfo.nextCardCode)">
            <el-button type="primary" class="start-btn" @click="completeWrite">
              {{i18n('结束写卡')}}
            </el-button>
          </template>
        </div>
      </div>
      <div class="header-right">
        <div class="empty-info" v-if="writeState === 'UNSTART'">
          <img src="@/assets/image/icons/cutting_pic_empty.png" class="empty-img" />
          <span class="gray-tips">{{i18n('请在左侧输入信息，点击开始写卡')}}</span>
        </div>
        <template v-else>
          <div class="info-header">
            <img src="@/assets/image/icons/ic_infofill.png" class="info-icon">
            <div v-if="form.cardMedium === 'mag'" class="info-text">
              {{ magTopicText }}
            </div>
            <div class="info-text" v-else>{{topicText}}</div>
          </div>
          <div class="info-form">
            <div class="form-child">
              <div class="child-label">{{i18n('当前写卡的卡号')}}:</div>
              <div class="current-code" v-if="writeCardInfo.nextCardCode">
                <span>{{writeCardInfo.nextCardCode.slice(0, -4)}}</span>
                <span style="color: #007EFF;font-size: 23px">{{writeCardInfo.nextCardCode.slice(-4)}}</span>
                <el-button style="margin-left: 8px" @click="doSkip" v-if="writeState !== 'PAUSE' && hasOptionPermission('/卡/卡管理/写卡单', '单据维护')">
                  {{i18n('跳过')}}
                </el-button>
              </div>
            </div>
            <div class="form-child">
              <div class="child-label">{{i18n('下张待写卡卡号')}}:</div>
              <div class="child-text">{{writeCardInfo.afterNextCardCode || '--'}}</div>
            </div>
            <div class="form-child" style="margin-top: 16px">
              <span class="child-label">{{i18n('已操作数量')}}:</span>
              <span class="child-text" style="margin-left: 6px">{{writeCardInfo.writeCount}}</span>
            </div>
            <div class="form-child" style="margin-top: 16px">
              <span class="child-label">{{i18n('剩余数量')}}:</span>
              <span class="child-text" style="margin-left: 6px">{{writeCardInfo.remainCount}}</span>
            </div>
            <div class="form-child" style="margin-top: 16px">
              <span class="child-label">{{i18n('写卡成功数量')}}:</span>
              <span class="child-text" style="margin-left: 6px">{{writeCardInfo.successCount}}</span>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div class="setting-container" style="margin-top: 20px">
      <div class="section-title">{{i18n('写卡流水记录')}}</div>
      <el-table :data="recordList">
        <el-table-column :label="i18n('/储值/预付卡/预付卡调整单/列表页面/卡号')" width="214px">
          <template slot-scope="scope">
            {{scope.row.cardCode || '--'}}
          </template>
        </el-table-column>
        <el-table-column :label="i18n('/储值/预付卡/预付卡查询/列表页面/卡模板')" width="310px">
          <template slot-scope="scope">
            <span class="span-btn" @click="goCardDtl(scope.row.cardTemplateNumber)">
              [{{scope.row.cardTemplateNumber || '--'}}]{{scope.row.cardTemplateName || '--'}}
            </span>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('/储值/预付卡/预付卡查询/列表页面/卡状态')" width="214px">
          <template slot-scope="scope">
            <div style="display:flex;align-items:center">
              <span class="dot" :style="{background: computeState(scope.row.cardState).color}"></span>
              <span>{{computeState(scope.row.cardState).state}}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('操作内容')" width="214px">
          <template slot-scope="scope">
            {{computeAction(scope.row.action)}}
          </template>
        </el-table-column>
        <el-table-column :label="i18n('操作信息')">
          <template slot-scope="scope">
            {{scope.row.occurredTime | dateFormate3}} / {{scope.row.operator || '--'}}
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!--选择卡模板弹框-->
    <CardTemplateSelectorDialog no-i18n ref="cardTemplateSelectorDialog" :filter="cardTemplateFilter" @summit="doCardTemplateSelected">
    </CardTemplateSelectorDialog>
  </div>
</template>

<script lang="ts" src="./WriteCard.ts">
</script>

<style lang="scss" scoped>
.card-write-container {
  width: 100%;
  .top-header {
    display: flex;
    justify-content: space-between;
    height: 300px;
    .header-left {
      width: 450px;
      height: 100%;
      margin-right: 20px;
      background-color: #fff;
      border-radius: 8px;
      padding: 24px;
      box-sizing: border-box;
      .btn-block {
        width: 263px;
        float: right;
        margin-right: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .start-btn {
        width: 100%;
        height: 36px;
      }
    }
    .header-right {
      position: relative;
      flex: 1;
      height: 100%;
      background-color: #fff;
      border-radius: 8px;
      padding: 24px;
      box-sizing: border-box;
      .empty-info {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        .empty-img {
          width: 84px;
          height: 72px;
          margin-bottom: 12px;
        }
      }
      .info-header {
        display: flex;
        padding: 14px;
        width: 100%;
        background: #e6f2ff;
        border-radius: 4px;
        .info-icon {
          width: 24px;
          height: 24px;
          margin-right: 8px;
        }
        .info-text {
          font-family: PingFangSC, PingFang SC;
          font-weight: 600;
          font-size: 20px;
          color: #007eff;
          line-height: 22px;
        }
      }
      .info-form {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
        .form-child {
          min-width: 230px;
          flex-basis: 50%;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 13px;
          .child-label {
            color: #79879e;
          }
          .child-text {
            color: #242633;
            margin-top: 12px;
          }
        }
        .current-code {
          display: flex;
          align-items: baseline;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 20px;
          color: #242633;
          margin-top: 8px;
        }
      }
    }
  }
  ::v-deep .el-form-item {
    margin-bottom: 12px;
  }
  ::v-deep .el-button--primary.is-disabled {
    background-color: #90d0ff !important;
  }
  ::v-deep .el-radio__input.is-checked + .el-radio__label {
    color: #1f375d;
  }
}
</style>