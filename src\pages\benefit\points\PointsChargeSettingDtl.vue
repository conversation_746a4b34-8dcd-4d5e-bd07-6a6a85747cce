<template>
  <div class="points-charge-setting-dtl">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button @click="doEdit" type="primary" v-if="hasOptionPermission('/积分/积分规则/积分抵现', '规则维护') && hasRule">{{formatI18n('/公用/按钮','修改')}}</el-button>
        <!-- <el-button @click="doBack" v-if="hasOptionPermission('/积分/积分规则/积分抵现', '规则维护')">{{formatI18n('/公用/按钮','返回')}}</el-button> -->
      </template>
    </BreadCrume>
    <div style="height: 95%;overflow: auto">
      <template v-if="hasRule">
        <div style="margin: 30px" v-if="dtl">
          <FormItem :label="formatI18n('/营销/积分活动/积分活动/积分抵现活动/立即新建/抵现方式')">
            <div style="line-height: 36px">
              <i18n k="/权益/积分/积分初始化/积分抵现/立即设置/抵现方式/每使用{0}积分，抵现{1}元，不满{2}积分不抵现">
                <template slot="0">
                  &nbsp;
                  {{ dtl.points }}
                  &nbsp;
                </template>
                <template slot="1">
                  &nbsp;
                  {{ dtl.amount | fmt }}
                  &nbsp;
                </template>
                <template slot="2">
                  &nbsp;
                  {{ dtl.points }}
                  &nbsp;
                </template>
              </i18n>
              <div style="color: rgb(144, 147, 153)">-
                {{ formatI18n('/权益/积分/积分初始化/积分抵现/立即设置/抵现方式/如果每100积分抵1元，且不满100积分不能抵现，当会员有399积分时，那么他只能使用300积分抵3元现金') }}
              </div>
            </div>
          </FormItem>
          <FormItem :label="formatI18n('/营销/积分活动/积分活动/积分抵现活动/立即新建', '抵现条件')">
            <div style="line-height: 36px" v-if="dtl.consumeType === 'UN_LIMIT'">
              {{ formatI18n('/营销/积分活动/门店积分兑换/不限制') }}
            </div>
            <div style="line-height: 36px" v-else>
              {{ formatI18n('/权益/积分/积分初始化/积分抵现/立即设置/抵现条件/整单消费满') }} {{ dtl.consumeAmount | fmt }}
              {{ formatI18n('/权益/积分/积分初始化/积分抵现/立即设置/抵现条件/元及以上') }}
            </div>
          </FormItem>
          <FormItem :label="formatI18n('/营销/积分活动/积分活动/积分抵现活动/立即新建', '单笔抵现上限')">
            <div style="line-height: 36px" v-if="dtl.upperPayType === 'UN_LIMIT'">
              {{ formatI18n('/权益/积分初始化/积分抵现/单笔抵现上限/无上限') }}
            </div>
            <div style="line-height: 36px" v-else-if="dtl.upperPayType === 'LIMIT'">
              <i18n k="/权益/积分初始化/积分抵现/单笔抵现上限/限不超过{0}元">
                <template slot="0">
                  &nbsp;{{ dtl.upperPayValue }}&nbsp;
                </template>
              </i18n>
            </div>
            <div style="line-height: 36px" v-else-if="dtl.upperPayType==='PERCENT_RATE'">
              <i18n k="/权益/积分初始化/积分抵现/单笔抵现上限/限不超过适用商品金额的{0}%">
                <template slot="0">
                  &nbsp;{{ dtl.upperPayRate }}&nbsp;
                </template>
              </i18n>
            </div>
            <div style="line-height: 36px" v-else-if="dtl.upperPayType==='LIMIT_AMOUNT_RATE'">
              <i18n k="/权益/积分初始化/积分抵现/单笔抵现上限/限不超过{0}元，且不超过适用商品金额的{1}%">
                <template slot="0">
                  &nbsp;{{ dtl.upperPayValue }}&nbsp;
                </template>
                <template slot="1">
                  &nbsp;{{ dtl.upperPayRate }}&nbsp;
                </template>
              </i18n>
            </div>
          </FormItem>
          <FormItem :label="formatI18n('/营销/积分活动/积分活动/积分抵现活动/立即新建', '每人每天积分抵现次数')">
            <div style="line-height: 36px" v-if="dtl.memberDailyPayTimesType === 'UN_LIMIT'">
              {{ formatI18n('/权益/积分初始化/积分抵现/单笔抵现上限/无上限') }}
            </div>
            <div style="line-height: 36px" v-else>
              {{ formatI18n('/权益/积分/积分初始化/积分抵现/立即设置/每人每天积分抵现次数/限') }}{{
              dtl.memberDailyPayTimes
            }}{{ formatI18n('/权益/积分/积分初始化/积分抵现/立即设置/每人每天积分抵现次数/次') }}
            </div>
          </FormItem>
        </div>
        <div style="margin-left:170px"><span style="color: springgreen">{{formatI18n('/权益/积分/积分初始化/积分抵现/立即设置/注：')}}</span>
          {{ formatI18n('/权益/积分/积分初始化/积分抵现/立即设置/退回的积分有效期不变；若交易使用的积分已过期清除，该部分积分不予退还。') }}
        </div>
      </template>
      <div class="empty" v-else>
        <img src="~assets/image/auth/ct_empty.png" alt="">
        <div class="empty-desc">{{i18n('暂无规则，请前往设置')}}</div>
        <el-button type="primary" @click="doSet" v-if="hasOptionPermission('/积分/积分规则/积分抵现', '规则维护')">
          {{formatI18n("/权益/积分/积分初始化/未初始化状态/得积分规则/基础得积分", "立即设置")}}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./PointsChargeSettingDtl.ts">
</script>

<style lang="scss">
.points-charge-setting-dtl {
  background-color: white;
  overflow: hidden;
  height: 100%;
  width: 100%;

  .qf-form-item .qf-form-label {
    width: 170px !important;
    text-align: left;
  }

  .qf-form-item .qf-form-content {
    margin-left: 170px !important;
  }
  .empty {
    position: absolute;
    top: 150px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    img {
      width: 160px;
      height: 160px;
    }
    .empty-desc {
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      color: #36445a;
      margin-bottom: 12px;
    }
  }
}
</style>