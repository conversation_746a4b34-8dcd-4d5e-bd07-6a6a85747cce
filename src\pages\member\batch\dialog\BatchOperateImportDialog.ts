import {Component, Prop, Vue} from 'vue-property-decorator'
import EnvUtil from 'util/EnvUtil'
import ImportResultDialog from 'cmp/importdialog/ImportResultDialog.vue'
import SelectStores from 'cmp/selectStores/SelectStores'
import UploadApi from "http/upload/UploadApi";

@Component({
    name: 'BatchOperateImportDialog',
    components: {
        ImportResultDialog,
      SelectStores
    }
})
export default class BatchOperateImportDialog extends Vue {
    $refs: any

    @Prop()
    title: string // dialog标题

    @Prop({
        type: Number,
        default: 50000
    })
    importNumber: string // dialog标题

    @Prop()
    templateName: string // 模板名称

    @Prop()
    templatePath: string // 模板路径

    @Prop()
    importUrl: string // 导入文件接口的url

    @Prop({
        type: Boolean,
        default: false
    })
    dialogShow: boolean // 控制模态框的展示
    importResultDialogShow = false
    uploadHeaders: any = {}
    @Prop({
        type: Boolean,
        default: false
    })
    showOrg: boolean // 控制模态框的展示
    orgId: string = ''
    url: string = ''

    // 控制结果模态框的展示
    getUploadUrl() {
        return EnvUtil.getServiceUrl() + this.importUrl
    }

    created() {
        let locale = sessionStorage.getItem('locale')
        this.uploadHeaders = {
					locale: locale ? locale : "zh_CN",
					time_zone: new Date().getTimezoneOffset(),
					marketingCenter: sessionStorage.getItem("marketCenter"),
				};
      const authorization = EnvUtil.getUniAuthorization()
      if (authorization) {
        this.uploadHeaders.authorization = authorization
      }
        this.url = this.getUploadUrl()
    }

    getImportDesc() {
        let str: any = this.formatI18n('/公用/导入', '为保障上传成功，建议每次最多上传{0}条信息')
        str = str.replace(/\{0\}/g, this.importNumber ? this.importNumber : 5000)
        return str
    }

    getSuccessInfo(a: any, b: any, c: any) {
        if (a && a.code === 2000) {
            this.$refs.upload.clearFiles()
            this.$emit('dialogClose')
            this.$emit('upload-success', b)
        } else {
            this.$message.error(a.msg.replace('java.lang.Exception:', '').replace('com.hd123.phoenix.crm.api.CrmException:', '').trimEnd())
        }
    }

    getErrorInfo(a: any, b: any, c: any) {
        this.$message.error(this.formatI18n('/公用/导入', '导入失败，请重新导入') as string)
        this.$refs.upload.clearFiles()
    }

    doHandleChange(file: any, fileList: any) {
        // console.log('fileList', fileList)
    }

    doBeforeClose(done: any) {
        this.$emit('dialogClose')
        done()
    }

    doModalClose(type: string) {
        if (type === 'confirm') {
            if (this.$refs.upload.uploadFiles.length <= 0) {
                this.$message.warning(this.formatI18n('/公用/导入', '请先选择文件') as string)
                return
            }
            if (!this.orgId && this.showOrg) {
                this.$message.warning(this.formatI18n('/公用/查询条件/提示', '请选择发生组织') as string)
                return
            }
            this.$refs.upload.submit();
        } else {
            this.$refs.upload.clearFiles()
        }
        this.$emit('dialogClose', type)
    }

    doImportResultDialogClose() {
        this.importResultDialogShow = false
        this.$emit('dialogClose')
    }

    beforeUpload() {
        return new Promise((resolve, reject) => {
            if (this.orgId) {
                this.url = this.getUploadUrl() + '?orgId=' + this.orgId;
            }
            // dom上传地址更新完成后，触发上传
            this.$nextTick(() => resolve());
        });
    }

    downloadTemplate() {
        UploadApi.getUrl(this.templatePath).then((resp: any) => {
            if (resp && resp.data) {
                window.open(resp.data);
            }
        }).catch((error) => {
            if (error && error.message) {
                this.$message.error(error.message)
            }
        })
    }
}
