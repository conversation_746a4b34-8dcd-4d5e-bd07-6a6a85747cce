import ApiClient from 'http/ApiClient'
import MemberPointsAccount from 'model/points/adjustbill/MemberPointsAccount'
import PointsAdjustBill from 'model/points/adjustbill/PointsAdjustBill'
import PointsAdjustBillFilter from 'model/points/adjustbill/PointsAdjustBillFilter'
import PointsAdjustBillLine from 'model/points/adjustbill/PointsAdjustBillLine'
import PointsAdjustBillReason from 'model/points/adjustbill/PointsAdjustBillReason'
import PointsAdjustBillStats from 'model/points/adjustbill/PointsAdjustBillStats'
import PointsReasonFilter from 'model/points/adjustbill/PointsReasonFilter'
import Response from 'model/common/Response'
import PointsAdjustBillConfig from 'model/points/adjustbill/PointsAdjustBillConfig'

export default class PointsAdjustBillApi {
  /**
   * 审核积分调整单
   * 审核积分调整单。
   * 
   */
  static audit(billNumber: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/points-adjust-bill/audit/${billNumber}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 审核积分调整单
   * 审核积分调整单。
   * 
   */
  static batchAudit(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/points-adjust-bill/batch/audit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除积分调整单
   * 删除积分调整单。
   * 
   */
  static batchRemove(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/points-adjust-bill/batch/remove`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取积分调整单详情
   * 获取积分调整单详情。
   * 
   */
  static get(billNumber: string): Promise<Response<PointsAdjustBill>> {
    return ApiClient.server().get(`/v1/points-adjust-bill/get/${billNumber}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询会员账户信息
   * 查询会员账户信息。
   * 
   */
  static getMemberAccount(identCode: string): Promise<Response<MemberPointsAccount>> {
    return ApiClient.server().get(`/v1/points-adjust-bill/getMemberAccount/${identCode}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取积分调整单修改详情
   * 获取积分调整单修改详情。
   * 
   */
  static getModify(billNumber: string): Promise<Response<PointsAdjustBill>> {
    return ApiClient.server().get(`/v1/points-adjust-bill/get/modify/${billNumber}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量导入积分调整单
   * 批量导入积分调整单。
   * 
   */
  static importExcel(body: any): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/points-adjust-bill/importExcel`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询积分调整原因
   * 查询积分调整原因。
   * 
   */
  static listReason(body: PointsReasonFilter): Promise<Response<PointsAdjustBillReason[]>> {
    return ApiClient.server().post(`/v1/points-adjust-bill/listReason`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改积分调原因
   * 修改积分调原因。
   * 
   */
  static modifyReason(id: string, reason: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/points-adjust-bill/${id}/modifyReason`, {}, {
      params: {
        reason: reason
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询积分调整单
   * 分页查询积分调整单。
   * 
   */
  static query(body: PointsAdjustBillFilter): Promise<Response<PointsAdjustBill[]>> {
    return ApiClient.server().post(`/v1/points-adjust-bill/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取积分调整单明细详情
   * 获取积分调整单明细详情
   * 
   */
  static queryDetail(billNumber: string, page: number, pageSize: number): Promise<Response<PointsAdjustBillLine[]>> {
    return ApiClient.server().get(`/v1/points-adjust-bill/queryDetail/${billNumber}`, {
      params: {
        page: page,
        pageSize: pageSize
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除积分调原因
   * 删除积分调原因。
   * 
   */
  static removeReason(body: Array<string>): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/points-adjust-bill/removeReason`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建积分调整单
   * 新建积分调整单。
   * 
   */
  static save(body: PointsAdjustBill): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/points-adjust-bill/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建并审核积分调整单
   * 新建并审核积分调整单。
   * 
   */
  static saveAndAudit(body: PointsAdjustBill): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/points-adjust-bill/saveAndAudit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改积分调整单
   * 修改积分调整单。
   * 
   */
  static saveModify(body: PointsAdjustBill): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/points-adjust-bill/saveModify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建积分调原因
   * 新建积分调原因。
   * 
   */
  static saveReason(reason: string): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/points-adjust-bill/saveReason`, {}, {
      params: {
        reason: reason
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 积分调整单汇总
   * 积分调整单汇总。
   * 
   */
  static stats(body: PointsAdjustBillFilter): Promise<Response<PointsAdjustBillStats>> {
    return ApiClient.server().post(`/v1/points-adjust-bill/stats`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
