/*
 * @Author: 黎钰龙
 * @Date: 2024-08-11 13:47:26
 * @LastEditTime: 2024-08-11 13:47:31
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\card\writeCard\WriteCardRequest.ts
 * 记得注释
 */
export default class WriteCardRequest {
  // 单号
  billNumber: Nullable<string> = null
  // 本次写卡卡号
  cardCode: Nullable<string> = null
  // 当前状态:-6——清卡失败，-5——读卡失败，-4——写卡失败，-3——空卡校验失败，-2——检查插卡失败，-1——打卡卡机失败，0——未开始，1——读卡成功，2——开始下次写卡，3——清卡成功
  currentState: Nullable<number> = null
  // 备注
  remark: Nullable<string> = null
}