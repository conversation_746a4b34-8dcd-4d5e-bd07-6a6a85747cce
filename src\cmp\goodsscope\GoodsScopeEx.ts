import {Component, Prop, Provide, Inject,  Vue, Watch} from 'vue-property-decorator'
import GoodsScope from 'cmp/goodsscope/GoodsScope.vue'
import GoodsRange from 'model/common/GoodsRange'
import BrowserMgr from 'mgr/BrowserMgr'

@Component({
	name: "GoodsScopeEx",
	components: {
		GoodsScope,
	},
	model: {
		prop: "data",
		event: "change",
	},
})
export default class GoodsScopeEx extends Vue {
	@Prop({
		type: String,
		default: "barcode",
	  })
	  goodsMatchRuleMode: "barcode" | "code";
	@Prop({
		type: Boolean,
		default: true,
	})
	validateForm: boolean;
	@Prop()
	type: string;
	@Prop()
	hideUse: boolean;
	@Prop()
	hideNoUse: boolean;
	@Prop({
		type: Boolean,
		default: false,
	})
	hideTitle: boolean; // 只显示选择商品部分
	@Prop({
		type: Boolean,
		default: true,
	})
	innerTitle: boolean; // 只显示选择商品部分

	@Prop()
	data: GoodsRange; // 已选商品

	@Prop({ type: Boolean }) disabled: boolean; //是否展示

	@Prop({
		type: Number,
		default: 5000,
	})
	importNumber: string;

	@Inject({
		from: 'showAll',
		default: false
	})
	showAll: Boolean //是否固定展示全部商品
	@Prop({ type: Boolean, default: false })
	appreciationGoods: boolean;
	@Prop({ type: String, default: 'normal' })
	chooseGoodType: String;
	

	goodsLimit: string = "";
	$refs: any;
	isMultipleMC: Boolean = false;
	headquarters: Boolean = sessionStorage.getItem('headquarters') === 'true'? true: false

	created() {
		this.goodsLimit = "全部商品";
		if (this.data) {
			this.setGoodsLimit(this.data);
		}
		let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
		if (sysConfig) {
			this.isMultipleMC = sysConfig.enableMultiMarketingCenter;
			// this.promotionCenter = false
			// this.promotionCenter = true
		}
	}

	handleChange() {
		this.submit(new GoodsRange());
	}

	validate() {
		if (this.$refs["goodsScope"]) {
			return this.$refs["goodsScope"].validate();
		} else {
			return true;
		}
	}

	@Watch("data", { deep: true })
	private watchData(value: GoodsRange) {
		this.setGoodsLimit(value);
	}

	@Watch("goodsLimit")
	private watchGoodsLimit(value: boolean) {
		this.submit(JSON.parse(JSON.stringify(this.data)));
	}

	private handleClick(oldType: string, newType: string) {
		if (this.disabled) {
			return;
		}
		if (oldType !== newType && this.$refs.goodsScope) {
			this.$refs.goodsScope.reset();
		}
	}

	private setGoodsLimit(value: GoodsRange) {
		if (this.hideTitle || !this.innerTitle) {
			value.limit = true;
		}
		if (this.type && (this.type === this.formatI18n("/公用/券模板", "商品现金券") || this.type === this.formatI18n("/公用/券模板", "商品折扣券"))) {
			this.goodsLimit = "指定商品适用";
		} else {
			if (!value.limit) {
				if (this.type !== "goods_cash" && this.type !== "rfm_type") {
					this.goodsLimit = "全部商品";
				} else {
					this.goodsLimit = "";
				}
			}
			if (value.limit && value.excludePrecondition) {
				this.goodsLimit = "指定商品不适用";
			}
			if (value.limit && !value.excludePrecondition) {
				this.goodsLimit = "指定商品适用";
			}
		}
	}

	private submit(data: GoodsRange) {
    if(!data) return
		if (!this.goodsLimit) {
			data.limit = true;
			data.excludePrecondition = false;
		}
		if (this.goodsLimit === "全部商品") {
			data.limit = false;
			data.excludePrecondition = false;
		}
		if (this.goodsLimit === "指定商品适用") {
			data.limit = true;
			data.excludePrecondition = false;
		}
		if (this.goodsLimit === "指定商品不适用") {
			data.limit = true;
			data.excludePrecondition = true;
		}
		this.$emit("change", data);
	}
}
