/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2023-07-18 15:52:40
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\deposit\store\BDepositUseLimitActivity.ts
 * 记得注释
 */
import ChannelRange from 'model/common/ChannelRange'
import GoodsRange from 'model/common/GoodsRange'
import StoreRange from 'model/common/StoreRange'

// 储值限制商品活动
export default class BDepositUseLimitActivity {
  // 活动门店
  stores: Nullable<StoreRange> = null
  // 商品范围信息
  goodsRange: Nullable<GoodsRange> = null
  // 渠道范围
  channelRange: Nullable<ChannelRange> = null
  // 营销中心
  marketingCenter: Nullable<string> = null
  // 储值使用须知
  remark: Nullable<string> = null
}