/*
 * @Author: 黎钰龙
 * @Date: 2024-04-14 13:39:09
 * @LastEditTime: 2024-04-15 11:52:51
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\promotion\bigWheelActivity\BigWheelAward.ts
 * 记得注释
 */
import AwardValue from "./AwardValue";

export default class BigWheelAward {
  // 实体奖品名称
  awardName: Nullable<string> = null
  // 实体奖品数量
  awardQty: Nullable<number> = null
  // 
  awardValues: AwardValue[] = []
}