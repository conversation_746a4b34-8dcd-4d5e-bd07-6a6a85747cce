/*
 * @Author: 黎钰龙
 * @Date: 2024-11-21 19:31:52
 * @LastEditTime: 2024-12-13 13:27:20
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\autoTag\TimePropValues.ts
 * 记得注释
 */
import { BeforeOrAfter } from "./BeforeOrAfter"
import { LastOrFuture } from "./LastOrFuture"
import { OperatorType } from "./OperatorType"
import { TimeOperatorType } from "./TimeOperatorType"

export default class TimePropValues {
  // 时间类型:绝对时间、相对当前时间点、相对当前时间区间、有值、没值
  dateType: Nullable<OperatorType.absolute_time | OperatorType.relative_time_point | OperatorType.relative_time_range | OperatorType.value | OperatorType.no_value> = null
  // 运算符；
  operator: Nullable<TimeOperatorType> = TimeOperatorType.eq
  // 绝对时间值
  date: Date[] = []
  // 相对时间时间点/相对当前时间区间，过去或未来
  lastOrFuture: Nullable<LastOrFuture> = LastOrFuture.last
  // 相对时间属性，之前/之后/之内
  beforeOrAfter: Nullable<BeforeOrAfter> = BeforeOrAfter.within
  // 相对时间属性 天数
  day: Nullable<number> = null
  // 相对当前时间区间 开始天数
  fromDay: Nullable<number> = null
  // 相对当前时间区间 结束天数
  toDay: Nullable<number> = null
}