import ApiClient from 'http/ApiClient'
import Category from 'model/category/Category'
import RSCategory from 'model/common/RSCategory'
import RSCategoryFilter from 'model/common/RSCategoryFilter'
import RSSaveBatchCategoryRequest from 'model/common/RSSaveBatchCategoryRequest'
import Response from 'model/common/Response'

export default class CategoryApi {
  /**
   * 查询类别
   *
   */
  static query(body: RSCategoryFilter): Promise<Response<RSCategory[]>> {
    return ApiClient.server().post(`/v1/category/query`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询类别
   *
   */
  static queryTree(body: RSCategoryFilter): Promise<Response<Category[]>> {
    return ApiClient.server().post(`/v1/category/queryTree`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 删除类别
   *
   */
  static remove(id: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/category/remove`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量保存
   *
   */
  static saveBatch(body: RSSaveBatchCategoryRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/category/saveBatch`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
