import ApiClient from 'http/ApiClient'
import Response from "model/common/Response";
import RedemptionCodeBill from 'model/promotion/exchangeCode/RedemptionCodeBill';
import RedemptionCodeBillFilter from 'model/promotion/exchangeCode/RedemptionCodeBillFilter';

export default class RedemptionCodeBillApi {
  /**
   * 审核兑换码单据
   * 审核兑换码单据。
   * 
   */
  static audit(billNumber: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/redemption-code-bill/audit/${billNumber}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量审核兑换码单据
   * 批量审核兑换码单据。
   * 
   */
  static batchAudit(body: Array<string>): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/redemption-code-bill/batch/audit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取兑换码单据详情
   * 获取兑换码单据详情。
   * 
   */
  static get(billNumber: string): Promise<Response<RedemptionCodeBill>> {
    return ApiClient.server().get(`/v1/redemption-code-bill/get/${billNumber}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询兑换码单据
   * 分页查询兑换码单据。
   * 
   */
  static query(body: RedemptionCodeBillFilter): Promise<Response<RedemptionCodeBill[]>> {
    return ApiClient.server().post(`/v1/redemption-code-bill/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除兑换码单据
   * 删除兑换码单据。
   * 
   */
  static remove(billNumber: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/redemption-code-bill/remove/${billNumber}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建兑换码单据
   * 新建兑换码单据。
   * 
   */
  static save(body: RedemptionCodeBill): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/redemption-code-bill/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建并审核兑换码单据
   * 新建并审核兑换码单据。
   * 
   */
  static saveAndAudit(body: RedemptionCodeBill): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/redemption-code-bill/saveAndAudit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改兑换码单据
   * 修改兑换码单据。
   * 
   */
  static saveModify(body: RedemptionCodeBill): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/redemption-code-bill/saveModify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 检查是否导入过码值
   * 检查是否导入过码值
   * 
   */
  static checkImportCode(number: string): Promise<Response<boolean>> {
    return ApiClient.server().get(`/v1/redemption-code-bill/checkImportCode/${number}`, {
    }).then((res) => {
      return res.data
    })
  }
}
