<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: 司浩
 * @Date: 2021-08-12 09:59:33
 * @LastEditors: 黎钰龙
 * @LastEditTime: 2025-03-12 10:31:35
-->
<template>
    <el-dialog :title="formatI18n('/权益/券/券模板/详情界面/面包屑/券模板详情')" class="select-store-active-dtl-dialog"
               append-to-body
               :close-on-click-modal="false" :visible.sync="dialogShow" :before-close="doBeforeClose">
        <ActiveAddCouponDtl
                :data="child"
                :options="{
                  hideTitle: true,
                  hideState: false,
                  hideFaceAmount: true,
                  hideType: true,
                  hideName: true,
                  hideOuterNumberNamespace: false,
                }"
                :isTopPanel="true"></ActiveAddCouponDtl>
        <div slot="footer" class="dialog-footer">
            <el-button size="small" type="primary" @click="doModalClose()">{{formatI18n('/公用/按钮/确定')}}</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts" src="./SelectStoreActiveDtlDialog.ts">
</script>

<style lang="scss">
    .select-store-active-dtl-dialog{
        display: flex;
        align-items: center;
        justify-content: center;
        .el-dialog{
            width: 1200px;
            height: 700px;
            margin: 0 !important;
        }
        .wrap{
            height: 540px;
            overflow: auto;
            .item{
                width: 228px;
                height: 108px;
                border: 1px solid #c7c7c7;
                border-radius: 10px;
                display: inline-block;
                margin-bottom: 24px;

                &:nth-child(odd) {
                    margin-right: 12px;

                }
                &:nth-child(even) {
                    margin-left: 12px;
                }
            }
        }
        .qf-form-item .qf-form-label{
            word-break: break-word !important;
        }
        .qf-form-item .qf-form-content{
            word-break: break-word !important;
        }
    }
</style>