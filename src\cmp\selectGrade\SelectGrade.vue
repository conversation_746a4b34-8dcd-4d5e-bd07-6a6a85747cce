<template>
	<div class="select-grade">
		<el-radio-group :disabled="disabled" v-model="isAll" @change="isAllChange">
			<el-radio :label="true">{{ formatI18n("/公用/公共组件/全部等级") }}</el-radio>
			<el-radio :label="false">{{ formatI18n("/公用/公共组件/部分等级") }}</el-radio>
		</el-radio-group>
		<br />
		<el-form :model="model" ref="gradeForm" :rules="rule">
			<el-form-item prop="selectedGrade">
				<el-select
          :disabled="disabled"
					v-model="model.selectedGrade"
					@change="levelChange"
					multiple
					v-if="!isAll"
					:placeholder="formatI18n('/营销/积分活动/积分活动/不积分商品/新建不积分商品活动/活动信息/等级/请选择等级')"
				>
					<el-option :label="item.name" :value="item.code" v-for="(item, index) in memberLevel" :key="index">
						<span v-if="item.code">[{{ item.code }}]</span>
						{{ item.name }}
					</el-option>
				</el-select>
			</el-form-item>
		</el-form>
	</div>
</template>

<script lang="ts" src="./SelectGrade.ts"></script>

<style lang="scss"></style>
