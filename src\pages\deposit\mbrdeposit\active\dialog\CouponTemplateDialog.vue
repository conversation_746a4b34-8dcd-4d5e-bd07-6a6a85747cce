<template>
    <el-dialog :before-close="doBeforeClose"
               :close-on-click-modal="false"
               :title="formatI18n('/公用/券模板', '添加优惠券')"
               :visible.sync="dialogShow"
               v-if="dialogShow"
               class="coupon-template-dialog"
               :append-to-body="true"
               :destroy-on-close="true"
               :lock-scroll="false">
        <div class="wrap">
            <!--<CouponTemplate ref="couponTpl"-->
                            <!--:state="state"-->
                            <!--:couponInfo="couponInfo"-->
                            <!--:data="data"-->
                            <!--@params="getParams">-->
            <!--</CouponTemplate>-->
            <CouponTemplateWrap
                    v-model="coupon"
                    :couponInfo="couponInfo"
                    :state="state"
                    ref="couponTemplate">
            </CouponTemplateWrap>
        </div>
        <div slot="footer" class="dialog-footer">
            <!--<el-button @click="dialogFormVisible = false">取 消</el-button>-->
            <el-button @click="doModalClose" size="small" type="primary">{{formatI18n('/公用/按钮', '确定')}}</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts" src="./CouponTemplateDialog.ts">
</script>

<style lang="scss">
    .coupon-template-dialog{
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: left;
        .el-form-item__label{
            /*width: 110px !important;*/
        }
        .el-form-item__content{
            /*margin-left: 120px !important;*/
            white-space: normal;
        }
        .fix_content{
            .el-form-item__content{
                margin-left: 0px !important;
            }
        }
        .cur_record,.cur-day{
            .el-form-item__label{
                width: 0px !important;
            }
            .el-form-item__content{
                margin-left: 0px !important;
            }
        }
        .el-dialog{
            width: 1100px;
            height: 640px;
            margin: 0 !important;
        }
        .wrap{
            height: 486px;
            overflow: auto;
            .item{
                width: 228px;
                height: 108px;
                border: 1px solid #c7c7c7;
                border-radius: 10px;
                display: inline-block;
                margin-bottom: 24px;

                &:nth-child(odd) {
                    margin-right: 12px;

                }
                &:nth-child(even) {
                    margin-left: 12px;
                }
            }
        }
        .el-textarea__inner{
            height: 100px;
        }
        .coupon-step{
            .el-radio{
                height: 60px;
            }
        }
        .the-date-range{
            width: 900px;
            border: 1px solid #CCCCCC;
            padding-left: 20px;
            padding-top: 15px;
            padding-bottom: 15px;
        }
        .line-height-30{
            line-height: 30px;
            color: red;
        }
        .el-date-editor .el-range__icon, .el-date-editor .el-range-separator, .el-date-editor .el-range__close-icon{
            line-height: 24px;
        }
        .cur-form-item{
            .el-form-item__label:before {
                content: '*';
                color: #EF393F;
                margin-right: 4px;
            }
        }
    }
</style>