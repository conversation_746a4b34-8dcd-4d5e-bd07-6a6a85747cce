import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import MemberDetail from "model/member_standard/MemberDetail";
import I18nPage from "common/I18nDecorator";
import MemberCardTitle from "pages/member/data/cmp/MemberCardTitle";
import MemberFormItem from "pages/member/data/cmp/MemberFormItem";
import EmptyData from "pages/member/data/cmp/EmptyData";
import MemberApi from "http/member_standard/MemberApi";
import SimpleCard from "model/member_standard/SimpleCard";

@Component({
  name: "MemberInfoAsset",
  components: { MemberCardTitle, MemberFormItem, EmptyData },
})
@I18nPage({
  prefix: [
    "/会员/会员资料",
  ],
  auto: true,
})
export default class MemberInfoAsset extends Vue {
  @Prop()
  dtl: MemberDetail;

  @Watch("dtl", { immediate: true })
  dtlChanged() {
    if (!(this.dtl && this.dtl.memberId)) return;
    this.getCards();
  }
  get isStandardMember() {
    return this.$route.fullPath.indexOf("/standard-member") != -1;
  }

  get permissionResourceId() {
    return this.isStandardMember ? "/会员/会员管理/会员资料" : "/会员/会员管理/营销中心会员";
  }

  prePayCards: SimpleCard[] = [];

  getCards() {
    MemberApi.getCards(this.dtl.memberId!).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.prePayCards = resp.data;
        // @ts-ignore
        // this.prePayCards = [{ code: "998053187804", balance: 0, cardType: "CountingCard" },
        //   { code: "8888888888888831", balance: 5, cardType: "ImprestCard" },
        //   { code: "24594003597141606422", balance: 100, cardType: "OfflineGiftCard" },
        //   { code: "993202871000", balance: 5, cardType: "RechargeableCard" }];
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message);
      }
    });
  }

  onSendCoupon() {
    this.$emit("send-coupon");
  }

  onAssetDetail(name: string) {
    this.$router.push({ name, query: { id: this.dtl.crmCode } });
  }

  onPrepayCardDetail(card: SimpleCard) {
    let name = null;
    switch (card.cardType) {
      case "CountingCard":
        name = "count-card-report";
        break;
      case "ImprestCard":
        name = "recharge-card-report";
        break;
      case "OfflineGiftCard":
      case "OnlineGiftCard":
        name = "gift-card-activity-report";
        break;
      case "RechargeableCard":
        name = "value-card-activity-report";
        break;
    }
    if (name) {
      this.$router.push({ name, query: { from: "member-asset", code: card.code } });
    }
  }
}
