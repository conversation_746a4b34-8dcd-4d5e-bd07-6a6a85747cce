/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-09-14 18:24:22
 * @LastEditTime: 2024-09-18 14:26:08
 * @LastEditors: fang<PERSON><PERSON><PERSON>
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\precisionmarketing\userGroup\UserGroupCategoryFilter.ts
 * 记得注释
 */
import PageRequest from 'model/default/PageRequest'

export default class UserGroupCategoryFilter extends PageRequest {
  // 分类名称类似于
  nameLike: Nullable<string> = null
  // 客群分类Id in
  categoryIdIn: Nullable<string[]> = null
}