<template>
  <div class="date-time-condition-dtl">
    <div class="day-row" v-if="form.data.dayLines && form.data.dayLines.length > 0">
      <div class="left-row">{{formatI18n('/公用/公共组件/时间条件控件/表单/每天')}}</div>
      <div class="right">
        <div class="right-row" v-for="line of form.data.dayLines">
          {{line[0]|HHmm}} - {{line[1]|HHmm}}
        </div>
      </div>
    </div>
    <div class="day-row" v-if="form.data.weekLines && form.data.weekLines.length > 0">
      <div class="left-row">{{formatI18n('/公用/公共组件/时间条件控件/表单/每周')}}</div>
      <div class="right">
        <div class="right-row" v-for="line of form.data.weekLines">
          <span v-for="week of line.weeks">{{translateWeek(week)}}&nbsp;&nbsp;</span>
          {{line.dateRange[0]|HHmm}} - {{line.dateRange[1]|HHmm}}
        </div>
      </div>
    </div>
    <div class="day-row" v-if="form.data.monthLines && form.data.monthLines.length > 0">
      <div class="left-row">{{formatI18n('/公用/公共组件/时间条件控件/表单/每月')}}</div>
      <div class="right">
        <div class="right-row" v-for="line of form.data.monthLines" style="line-height: 20px">
          <i18n k="/公用/公共组件/时间条件控件/表单/日" style="display: flex;align-items: center">
            <template slot="0">&nbsp;
              <div v-if="line.days && line.days.length < 31" class="text-overflow-ellipsis" style="max-width: 400px"
                   :title="line.days"><span v-for="day of line.days">{{ day }}&nbsp;&nbsp;</span>
              </div>
              <div v-else class="text-overflow-ellipsis" style="max-width: 400px">{{ formatI18n('/公用/公共组件/时间条件控件/表单/全部时间') }}&nbsp;&nbsp;</div>
            </template>
          </i18n>
          &nbsp;
          {{ line.dateRange[0]|HHmm }} - {{ line.dateRange[1]|HHmm }}
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./DateTimeConditionDtl.ts">
</script>

<style lang="scss" scoped>
  .date-time-condition-dtl {
    .day-row {
      display: flex;

      .left-row {
        width: 90px;
      }

      .right {
        .right-row {
          display: flex;
          align-items: center;
        }

        .right-row + .right-row {
          margin-top: 5px;
        }
      }
    }
  }
</style>
