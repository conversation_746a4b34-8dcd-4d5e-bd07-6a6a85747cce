<template>
    <div class="wechat-upload">
        <el-upload
            :headers="uploadHeaders"
            :action="getUploadUrl"
            :auto-upload="false"
            :on-change="doHandleChange"
            :on-error="getErrorInfo"
            :on-success="getSuccessInfo"
            :show-file-list="false"
            :with-credentials="true"
            class="avatar-uploader"
            ref="upload"
        >
            <img :src="imageUrl" class="avatar" v-if="imageUrl">
            <i class="el-icon-plus avatar-uploader-icon" v-else></i>
        </el-upload>
    </div>
</template>

<script lang="ts" src="./WechatUpload.ts">
</script>

<style lang="scss">
    .wechat-upload {
        font-size: 13px;
        font-weight: 400;
        font-family: "微软雅黑", Helvetica, Arial, sans-serif;
        margin: 0px;
        .avatar-uploader .el-upload {
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        .avatar-uploader .el-upload:hover {
            border-color: #409EFF;
        }
        .avatar-uploader-icon {
            font-size: 28px;
            color: #8c939d;
            width: 100px;
            height: 100px;
            line-height: 100px;
            text-align: center;
        }
        .avatar {
            width: 100px;
            height: 100px;
            display: block;
        }
    }
</style>