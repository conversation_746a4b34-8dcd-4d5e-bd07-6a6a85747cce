import {Component, Vue} from 'vue-property-decorator'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import MemberBalancePromotionApi from 'http/payment/member/MemberBalancePromotionApi'
import GoodsScopeDtl from 'cmp/goodsscope/GoodsScopeDtl.vue'
import MemberBalancePromotionActivity from 'model/payment/member/MemberBalancePromotionActivity'
import RSGrade from 'model/common/RSGrade'
import EditType from "common/EditType";
import ActiveStoreDtl from "cmp/activestoredtl/ActiveStoreDtl";
import I18nPage from "common/I18nDecorator";
import MemberBalancePromotionPermissions from "./MemberBalancePromotionPermissions";
import MemberBalancePromotionActivityNew from 'model/payment/member/MemberBalancePromotionActivityNew'
import MarketingBudgetDtl from 'cmp/MarketingBudget/MarketingBudgetDtl'
import ActivityMgr from 'mgr/ActivityMgr'
import { MarketBudgetActivityEnum } from 'model/promotion/MarketBudgetActivityEnum'
import ActivityStateTag from 'cmp/activity-state-tag/ActivityStateTag'
import DateTimeConditionDtl from "cmp/date-time-condition-picker/DateTimeConditionDtl";
import BrowserMgr from "mgr/BrowserMgr";

@Component({
	name: "MemberBalancePromotionDtl",
	components: {
		BreadCrume,
		GoodsScopeDtl,
		ActiveStoreDtl,
    MarketingBudgetDtl,
    ActivityStateTag,
	DateTimeConditionDtl
	},
})
@I18nPage({
	prefix: ["/储值/会员储值/储值支付活动/详情页面", "/公用/按钮", "/公用/券模板", "/公用/菜单"],
})
export default class MemberBalancePromotionDtl extends Vue {
	goodsMatchRuleMode: string = "barcode"
	i18n: I18nFunc;
	activityId: string = "";
	data: MemberBalancePromotionActivityNew = new MemberBalancePromotionActivityNew();
	gradeDifferentStepValueMap: any = {}; // grade -> gradeDifferentStepReduction
	gradeList: RSGrade[] = [];
	permission = new MemberBalancePromotionPermissions();

	$refs: any;
	get panelArray() {
    return [
      {
        name: this.i18n("储值支付优惠"),
        url: "member-balance-promotion-list",
      },
      {
        name: this.i18n("储值支付折扣活动详情"),
        url: "",
      },
    ]
  };

  get isOaActivity() {
    return ActivityMgr.isOaActivity(MarketBudgetActivityEnum.MemberBalanceDiscountActivityRule)
  }

	created() {
		let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
		if (sysConfig && this.goodsMatchRuleMode) {
			this.goodsMatchRuleMode = sysConfig.goodsMatchRuleMode;
		}
		this.activityId = this.$route.query.activityId as string;
		this.getDetail();
	}

	private getGradeList() {
		MemberBalancePromotionApi.gradeList().then((res: any) => {
			if (res.code === 2000) {
				this.gradeList = res.data;
				// this.gradeList.sort((a: any, b: any) => {
				//   if (a.code > b.code) {
				//     return 1
				//   }
				//   return -1
				// })
				this.gradeList.sort((a: any, b: any) => {
					if (a.type !== b.type) {
						let typeMap: any = {
							FREE: 1,
							PAID: 2,
							SPECIAL: 3,
						};
						return typeMap[a.type] - typeMap[b.type];
					} else {
						return a.no - b.no;
					}
				});
			}
		});
	}

	private getDetail() {
		MemberBalancePromotionApi.infoDiscount(this.activityId).then((res: any) => {
			if (res.code === 2000) {
				Object.assign(this.data, res.data);
				this.data.strategy = "BY_AMOUNT";
				if (this.data.gradeDifferentStepValue) {
					for (let item of this.data.gradeDifferentStepValue) {
						this.gradeDifferentStepValueMap[item.grade as string] = item;
					}
					console.log(this.gradeDifferentStepValueMap);
					this.getGradeList();
				}
			}
		});
	}

	private audit() {
		this.$alert(this.i18n("确认要审核吗？"), this.i18n("提示"), {
			confirmButtonText: this.i18n("确定"),
			cancelButtonText: this.i18n("取消"),
			type: "warning",
			callback: (action) => {
				if (action === "confirm") {
					MemberBalancePromotionApi.audit(this.activityId)
						.then((resp: any) => {
							if (resp && resp.code === 2000) {
								this.$message.success(this.i18n("审核成功"));
								this.getDetail();
							}
						})
						.catch((error) => {
							if (error && error.message) {
								this.$message.error(error.message);
							}
						});
				}
			},
		});
	}

	private copy() {
		if (this.data.body && this.data.body.type === "MEMBER_BALANCE_DISCOUNT") {
			this.$router.push({
				name: "member-balance-promotion-discount-edit",
				query: { activityId: this.activityId, editType: EditType.COPY },
			});
		}
	}

	private edit() {
		if (this.data.body && this.data.body.type === "MEMBER_BALANCE_DISCOUNT") {
			this.$router.push({
				name: "member-balance-promotion-discount-edit",
				query: { activityId: this.activityId, editType: EditType.EDIT },
			});
		}
	}

	private stop() {
		this.$alert(this.i18n("确认要停止吗？"), this.i18n("提示"), {
			confirmButtonText: this.i18n("确定"),
			cancelButtonText: this.i18n("取消"),
			type: "warning",
			callback: (action) => {
				if (action === "confirm") {
					MemberBalancePromotionApi.stop(this.activityId)
						.then((resp: any) => {
							if (resp && resp.code === 2000) {
								this.$message.success(this.i18n("停止成功"));
								this.getDetail();
							}
						})
						.catch((error) => {
							if (error && error.message) {
								this.$message.error(error.message);
							}
						});
				}
			},
		});
	}

	private del() {
		this.$alert(this.i18n("确认要删除吗？"), this.i18n("提示"), {
			confirmButtonText: this.i18n("确定"),
			cancelButtonText: this.i18n("取消"),
			type: "warning",
			callback: (action) => {
				if (action === "confirm") {
					MemberBalancePromotionApi.remove(this.activityId)
						.then((resp: any) => {
							if (resp && resp.code === 2000) {
								this.$message.success(this.i18n("删除成功"));
								this.$router.push({ name: "member-balance-promotion-list" });
							}
						})
						.catch((error) => {
							if (error && error.message) {
								this.$message.error(error.message);
							}
						});
				}
			},
		});
	}
}