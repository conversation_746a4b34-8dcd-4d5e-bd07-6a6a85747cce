/*
 * @Author: 黎钰龙
 * @Date: 2023-03-23 17:25:36
 * @LastEditTime: 2023-03-24 14:27:42
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\selectordialogs\ShowTableDialog.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import CouponDelayApplyApi from 'http/couponDelayApply/CouponDelayApplyApi';
import DelayCouponLine from 'model/couponDelayApply/DelayCouponLine';
import QueryDelayCouponLineRequest from 'model/couponDelayApply/QueryDelayCouponLineRequest';
import { Component, Vue } from 'vue-property-decorator';
@Component({
  name: 'ShowTableDialog'
})
@I18nPage({
  prefix: [
    '/券/延期申请',
    '/公用/券核销',
    '/公用/券模板',
  ]
})
export default class ShowTableDialog extends Vue {
  dialogShow: boolean = false
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  tableData: DelayCouponLine[] = []
  transNo: string = ''


  query() {
    const params = new QueryDelayCouponLineRequest()
    params.transNo = this.transNo
    params.page = this.page.currentPage - 1
    params.pageSize = this.page.size
    CouponDelayApplyApi.queryCoupons(params).then(res => {
      (this.tableData as any) = res.data || []
      this.page.total = res.total
    })
  }

  open(transNo: string) {
    this.transNo = transNo
    this.dialogShow = true
    this.query()
  }

  close() {
    this.dialogShow = false
  }

  handleCurrentChange(val: number) {
    this.page.currentPage = val
    this.query()
  }

  handleSizeChange(val: number) {
    this.page.size = val
    this.query()
  }
};