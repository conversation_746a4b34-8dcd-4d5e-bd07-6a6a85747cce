<template>
    <el-dialog :title="title" class="select-store-dialog"
               append-to-body
               :close-on-click-modal="false" :visible.sync="dialogShow" :before-close="doBeforeClose">
        <div class="wrap">
            <el-row>
                <el-col :span="8">
                    <form-item :label="formatI18n('/公用/券模板', '门店')">
                        <el-input :placeholder="formatI18n('/公用/券模板', '请输入代码、名称')" v-model="query.idNameLikes"></el-input>
                    </form-item>
                </el-col>
                <el-col :span="8">
                    <form-item>
                        <el-button @click="doSearch" type="primary">{{formatI18n('/公用/券模板', '查询')}}</el-button>
                        <el-button @click="doReset">{{formatI18n('/公用/券模板', '重置')}}</el-button>
                    </form-item>
                </el-col>
            </el-row>
            <div class="table-wrap">
                <el-table
                        ref="storeTable"
                        :data="stores"
                        border
                        @selection-change="handleSelectionChange"
                        style="width: 100%;margin-top: 20px">
                    <el-table-column
                            type="selection"
                            width="55">
                    </el-table-column>
                    <el-table-column :label="formatI18n('/公用/券模板', '代码')" prop="org.id" width="110"/>
                    <el-table-column :label="formatI18n('/公用/券模板', '名称')" prop="org.name">
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <div class="page" style="margin-top: 15px">
            <el-pagination
                    :current-page="page.currentPage"
                    :page-size="page.size"
                    :page-sizes="[10, 20, 30, 40]"
                    :total="page.total"
                    @current-change="onHandleCurrentChange"
                    @size-change="onHandleSizeChange"
                    background
                    layout="total, prev, pager, next, sizes,  jumper">
            </el-pagination>
        </div>
        <div slot="footer" class="dialog-footer">
            <!--<el-button @click="dialogFormVisible = false">取 消</el-button>-->
            <el-button @click="doModalClose" size="small" type="primary">{{formatI18n('/公用/按钮', '确定')}}</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts" src="./SelectStoreDialog.ts">
</script>

<style lang="scss">
    .select-store-dialog{
        display: flex;
        align-items: center;
        justify-content: center;
        .el-dialog{
            width: 1024px;
            height: 640px;
            margin: 0 !important;
        }
        .wrap{
            height: 440px;
            overflow: auto;
            .item{
                width: 228px;
                height: 108px;
                border: 1px solid #c7c7c7;
                border-radius: 10px;
                display: inline-block;
                margin-bottom: 24px;

                &:nth-child(odd) {
                    margin-right: 12px;

                }
                &:nth-child(even) {
                    margin-left: 12px;
                }
            }
        }
    }
</style>