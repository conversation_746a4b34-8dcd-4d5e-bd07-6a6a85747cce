/*
 * @Author: 黎钰龙
 * @Date: 2025-01-23 11:09:43
 * @LastEditTime: 2025-02-28 17:53:31
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\analysis\CustomerProfileEdit.ts
 * 记得注释
 */
import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import FormItem from 'cmp/formitem/FormItem';
import SelectCustomerGroup from 'cmp/select-customer-group/SelectCustomerGroup';
import I18nPage from 'common/I18nDecorator';
import { Component, Vue } from 'vue-property-decorator';
import AddPortraitInfoDialog, { PortraitForm } from './cmp/AddPortraitInfoDialog';
import UserGroupV2 from 'model/precisionmarketing/userGroup/UserGroupV2';
import UserGroupV2Api from 'http/precisionmarketing/userGroup/UserGroupV2Api';
import BCustomerProfileLine, { PortraitInfoType } from 'model/analysis/BCustomerProfileLine';
import CustomerProfileApi from 'http/analysis/CustomerProfileApi';
import { SortEnum } from 'model/analysis/SortEnum';
import PortraitDrawChart from './cmp/PortraitDrawChart/PortraitDrawChart';
import CommonUtil from 'util/CommonUtil';
import BCustomerProfile from 'model/analysis/BCustomerProfile';
import BCustomerProfileLineSaveRequest from 'model/analysis/BCustomerProfileLineSaveRequest';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
@Component({
  name: 'CustomerProfileEdit',
  components: {
    BreadCrume,
    SelectCustomerGroup,
    FormItem,
    AddPortraitInfoDialog,
    PortraitDrawChart
  }
})
@I18nPage({
  prefix: [
    '/公用/菜单',
    '/公用/券模板',
    '/数据/客群画像'
  ],
  auto: true
})
export default class CustomerProfileEdit extends Vue {
  $refs: any
  editType: 'create' | 'edit' | 'view' = 'create'
  targetCustomerInfo: UserGroupV2 = new UserGroupV2() // 目标客群
  compareCustomerInfo: UserGroupV2 = new UserGroupV2() // 对比客群
  temCompareCustomerId: string = '' // 弹窗选择 对比客群id
  groupVisible: boolean = false // 客群选择弹窗
  chartList: BCustomerProfileLine[] = []  //客群画像卡片列表（目标客群）
  compareChartList: BCustomerProfileLine[] = []  //客群画像卡片列表（对比客群）

  get panelArray() {
    return [
      {
        name: this.i18n('客群画像'),
        url: 'customer-profile'
      },
      {
        name: this.editType === 'edit' ? this.i18n('修改客群画像') : this.editType === 'view' ? this.i18n('客群画像详情') : this.i18n('新建客群画像'),
        url: ''
      }
    ]
  }

  get isOnlyView() {
    return this.editType === 'view'
  }

  // 剩余可添加的卡片数量
  get leftCardNum() {
    return 10 - this.chartList.length
  }

  async created() {
    const loading = CommonUtil.Loading()
    this.editType = this.$route.query.editType as any || 'create'
    try {
      this.targetCustomerInfo = await this.getCustomerInfo(this.$route.query.targetCustomerId as any) as UserGroupV2
      if (this.$route.query.compareCustomerId) {
        this.compareCustomerInfo = await this.getCustomerInfo(this.$route.query.compareCustomerId as any) as UserGroupV2
      }
      if (this.editType !== 'create') {
        await this.getDtl()
      }
    } catch (err) {
      console.log('created', err);
    }
    loading.close()
  }

  // 获取指定客群信息
  getCustomerInfo(id: string) {
    return new Promise((resolve, reject) => {
      UserGroupV2Api.get(id).then((res) => {
        if (res.code === 2000) {
          resolve(res.data || new UserGroupV2())
        } else {
          throw new Error(res.msg!)
        }
      }).catch((err) => {
        this.$message.error(err.message)
        reject()
      })
    })
  }

  getDtl() {
    const id = this.$route.query.id as string
    return CustomerProfileApi.listLine(id).then(async (res) => {
      if (res.code === 2000 && res.data) {
        for (let index = res.data.length - 1; index >= 0; index--) {
          const item = res.data[index]
          await this.doChartChange(item, item.uuid!)
        }
      } else {
        throw new Error(res.msg!)
      }
    }).catch((err) => {
      this.$message.error(err.message)
    })
  }

  doRemoveCompareCustomer() {
    this.compareCustomerInfo = new UserGroupV2()
    this.compareChartList = []
  }

  doAddCompareCustomer() {
    this.groupVisible = true
  }

  closeGroupDialog() {
    this.groupVisible = false
    this.temCompareCustomerId = ''
  }

  async confirmGroupCreate() {
    if (!this.temCompareCustomerId) {
      return this.$message.warning(this.i18n('请选择客群'))
    }
    if (this.temCompareCustomerId === this.targetCustomerInfo.uuid) {
      return this.$message.warning(this.i18n('对比客群不能与目标客群相同'))
    }
    this.compareCustomerInfo = await this.getCustomerInfo(this.temCompareCustomerId) as UserGroupV2
    this.temCompareCustomerId = ''
    this.groupVisible = false
    this.calAll()
  }

  // 重新计算当前页面的所有画像信息
  calAll() {
    const arr = JSON.parse(JSON.stringify(this.chartList))
    this.chartList = []
    this.compareChartList = []
    arr.forEach(async (item: BCustomerProfileLine) => {
      await this.doChartChange(item, item.uuid!)
    })
  }

  // 添加画像信息
  doAddPortraitInfo() {
    this.$refs.addPortraitInfoDialog.open()
  }

  // 删除画像卡片
  doChartRemove(uuid: string) {
    const index = this.chartList.findIndex((item) => item.uuid === uuid)
    this.chartList.splice(index, 1)
  }

  // 编辑画像
  doChartEdit(uuid: string) {
    const index = this.chartList.findIndex((item) => item.uuid === uuid)
    const item = this.chartList[index]
    this.$refs.addPortraitInfoDialog.open(item, index)
  }

  // 修改画像信息，重新提交计算（不是编辑画像，所以不需要拆分维度）
  async doChartChange(value: BCustomerProfileLine, uuid: string) {
    const loading = CommonUtil.Loading()
    const index = this.chartList.findIndex((item) => item.uuid === uuid)
    const params: BCustomerProfileLine = JSON.parse(JSON.stringify(value))
    params.calculationResult = null
    params.customerId = this.targetCustomerInfo.uuid!
    this.$set(this.chartList, index, JSON.parse(JSON.stringify(params)))
    try {
      let targetData
      // 计算目标客群
      await CustomerProfileApi.cal(params).then((res) => {
        if (res.code === 2000) {
          targetData = res.data
        } else {
          throw new Error(res.msg!)
        }
      }).catch((err) => {
        this.$message.error(err.message)
      })
      // 计算对比客群
      if (this.compareCustomerInfo.uuid) {
        params.customerId = this.compareCustomerInfo.uuid
        const compareIndex = this.compareChartList.findIndex((item) => item.uuid === uuid)
        await CustomerProfileApi.cal(params).then((res) => {
          if (res.code === 2000 && res.data) {
            if (compareIndex > -1) {
              this.$set(this.compareChartList, compareIndex, res.data)
            } else {
              this.compareChartList.push(res.data)
            }
          } else {
            throw new Error(res.msg!)
          }
        }).catch((err) => {
          this.$message.error(err.message)
        })
      }
      if (targetData) {
        // 之所以要把赋值放到这里，是因为计算是异步的，刚计算完目标客群就赋值，会导致数据不同步
        if (index > -1) {
          this.$set(this.chartList, index, targetData)
        } else {
          this.chartList.push(targetData)
        }
      }
    } catch (error) {
      console.error('计算失败', error)
    }
    loading.close()
  }

  // 提交计算画像
  async portraitSubmit(val: PortraitForm, removeIndex?: number) {
    const loading = CommonUtil.Loading()
    // removeIndex > -1 表示编辑图表
    try {
      if ([PortraitInfoType.AttributeDistributionCalculationRule, PortraitInfoType.AttributeStatisticsCalculationRule].includes(val.infoType)) {
        // 属性分布/属性统计：n个维度，需要拆分成n个卡片
        const arr: PortraitForm[] = []
        val.dimension.forEach((item) => {
          const obj: PortraitForm = JSON.parse(JSON.stringify(val))
          obj.uuid = CommonUtil.uuid()
          obj.dimension = [item]
          arr.push(obj)
        })
        if (removeIndex !== undefined && removeIndex > -1) {
          this.chartList.splice(removeIndex, 1)
          if (this.compareCustomerInfo.uuid) {
            const index = this.compareChartList.findIndex((com) => com.uuid === val.uuid)
            if (index > -1) {
              this.compareChartList.splice(index, 1)
            }
          }
        }
        for (let index = 0; index < arr.length; index++) {
          const item = arr[index]
          const data = await this.doCalculate(item, this.targetCustomerInfo.uuid!)
          if (this.compareCustomerInfo.uuid) {
            // 如果存在对比客群，计算对比客群
            const compareData = await this.doCalculate(item, this.compareCustomerInfo.uuid!)
            this.compareChartList.push(compareData)
          }
          if (removeIndex !== undefined && removeIndex > -1) {
            // 编辑条件下，在指定位置添加图表
            this.chartList.splice(removeIndex, 0, data)
          } else {
            // 新增图表到列表末尾
            this.chartList.splice(0, 0, data)
          }
        }
      } else if ([PortraitInfoType.CrossDistributionCalculationRule, PortraitInfoType.CrossStatisticsCalculationRule].includes(val.infoType)) {
        // 交叉分布、交叉统计 两个维度
        if (removeIndex !== undefined && removeIndex > -1) {
          this.chartList.splice(removeIndex, 1)
          if (this.compareCustomerInfo.uuid) {
            const index = this.compareChartList.findIndex((com) => com.uuid === val.uuid)
            if (index > -1) {
              this.compareChartList.splice(index, 1)
            }
          }
        }
        const data = await this.doCalculate(val, this.targetCustomerInfo.uuid!)
        if (this.compareCustomerInfo.uuid) {
          // 如果存在对比客群，计算对比客群
          const compareData = await this.doCalculate(val, this.compareCustomerInfo.uuid!)
          this.compareChartList.push(compareData)
        }
        if (removeIndex !== undefined && removeIndex > -1) {
          // 编辑条件下，在指定位置添加图表
          this.chartList.splice(removeIndex, 0, data)
        } else {
          // 新增图表到列表末尾
          this.chartList.splice(0, 0, data)
        }
      }
    } catch (error) {
      console.error('计算失败', error)
    }
    loading.close()
  }

  // 计算生成卡片
  doCalculate(val: PortraitForm, customerId: string) {
    console.log('计算入参', val);
    return new Promise<BCustomerProfileLine>((resolve, reject) => {
      const params = new BCustomerProfileLine()
      params.uuid = val.uuid || null
      params.ruleType = val.infoType
      params.sort = SortEnum.coverageDesc
      params.range = 7
      params.dimension1 = val.dimension[0]
      if (val.dimension[1]) {
        params.dimension2 = val.dimension[1]
      }
      params.target = val.target[0]
      params.customerId = customerId
      CustomerProfileApi.cal(params).then((res) => {
        if (res.code === 2000) {
          resolve(res.data || new BCustomerProfileLine())
        } else {
          throw new Error(res.msg!)
        }
      }).catch((err) => {
        this.$message.error(err.message)
        reject()
      })
    })
  }

  // 对比客群画像卡片item数据项
  compareItemData(uuid: string) {
    return this.compareChartList.find((item) => item.uuid === uuid)
  }

  async doSave() {
    if (!this.chartList.length) {
      return this.$message.warning(this.i18n('请设置画像信息'))
    }
    const loading = CommonUtil.Loading()
    try {
      const id = await this.doSaveItem() as string
      await this.doSaveLine(id).then(() => {
        this.$router.push({
          name: 'customer-profile'
        })
      })
    } catch (error) {
      console.error('保存失败', error)
    }
    loading.close()
  }

  // 画像保存
  doSaveItem() {
    return new Promise((resolve, reject) => {
      const params = new BCustomerProfile()
      params.uuid = this.$route.query.id as string || null
      params.targetCustomer = this.targetCustomerInfo.uuid!
      params.targetCustomerName = this.targetCustomerInfo.name
      params.compareCustomer = this.compareCustomerInfo.uuid
      params.compareCustomerName = this.compareCustomerInfo.name
      params.name = this.$route.query.name as string
      const func = this.editType === 'edit' ? CustomerProfileApi.modify : CustomerProfileApi.save
      func(params).then((res) => {
        if (res.code === 2000) {
          // 只有save接口会返回画像id
          resolve(res.data || params.uuid)
        } else {
          throw new Error(res.msg!)
        }
      }).catch((err) => {
        this.$message.error(err.message)
        reject(err)
      })
    })
  }

  // 画像明细保存
  doSaveLine(id: string) {
    const params = new BCustomerProfileLineSaveRequest()
    params.lines = JSON.parse(JSON.stringify(this.chartList))
    params.lines.forEach((item) => {
      item.customerProfileId = id
    })
    return CustomerProfileApi.saveLine(params).then((res) => {
      if (res.code === 2000) {
        this.$message.success(this.i18n('保存成功'))
      } else {
        this.$message.error(res.msg!)
      }
    }).catch((err) => {
      this.$message.error(err.message)
    })
  }

  // 导出
  async doExport() {
    const loading = CommonUtil.Loading();
    const canvas = await html2canvas(this.$refs.contentToExport, {
      scale: 2, // 提高图片清晰度
      useCORS: true, // 允许跨域图片
    });
    const imgData = canvas.toDataURL('image/png');
    const pdf = new jsPDF('p', 'mm', 'a4', true); // A4 纸，纵向
    const imgWidth = 210; // A4 纸宽度
    const pageHeight = 295; // A4 纸高度
    const imgHeight = (canvas.height * imgWidth) / canvas.width; // 图片高度
    let heightLeft = imgHeight; // 剩余高度
    let position = 0;
    pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
    heightLeft -= pageHeight; // 减去一页的高度
    while (heightLeft >= 0) {
      position = heightLeft - imgHeight; // 更新位置
      pdf.addPage(); // 添加新的一页
      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight; // 继续减去一页的高度
    }
    pdf.save(this.$route.query.name + '.pdf', { returnPromise: true }).finally(() => {
      loading.close();
    }); // 保存 PDF 文件
    this.calAll() //重新渲染当前页面图片
  }

  doGroupDetail(id: string) {
    const route = this.$router.resolve({ name: 'customer-group-detail', query: { uuid: id } })
    window.open(route.href, '_blank')
  }
};