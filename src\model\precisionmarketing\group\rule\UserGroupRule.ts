import UserConsumerProp from 'model/precisionmarketing/group/rule/UserConsumerProp'
import UserTagRule from 'model/precisionmarketing/group/rule/UserTagRule'
import MemberRule from "model/precisionmarketing/tag/tagrule/customize/member/MemberRule";
import LastMetricRule from "model/precisionmarketing/tag/tagrule/customize/last/LastMetricRule";

export default class UserGroupRule {
  // 会员属性信息
  memberProps: Nullable<MemberRule> = null
  // 周期发生消费信息
  consumerProps: Nullable<UserConsumerProp> = null
  // 周期发生消费信息
  consumerGoodsProps: Nullable<UserConsumerProp> = null
  // 最后消费信息
  lstConsumerProps: Nullable<LastMetricRule> = null
  // 最后消费信息
  tagRuleProps: Nullable<UserTagRule> = null
  // 条件连接符：and-且；or-或
  connective: Nullable<string> = null
}