<template>
  <div class="page-turntable-edit">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button size="large" type="primary"
          v-if="hasOptionPermission('/设置/小程序装修/页面管理', '编辑') && hasOptionPermission('/设置/小程序装修/页面管理', '发布')"
          @click="preserve(true)">
          {{ i18n('保存并发布') }}
        </el-button>
        <el-button size="large" @click="preserve(false)">{{ i18n('/公用/按钮/保存') }}</el-button>
        <el-button size="large" @click="goBack">{{ i18n('取消') }}</el-button>
      </template>
    </BreadCrume>
    <!-- 设置 -->
    <div class="panel">
      <div class="panel-left">
        <el-form :model="imgData" :rules="rules" ref="form" label-width="100px" class="demo-ruleForm">
          <!-- 背景装修 -->
          <div class="backDecoration">
            <div class="title">{{ i18n('背景装修') }}</div>
            <div class="prompt becorationContent">
              <!-- <div style="position: absolute; left: -58px; top: 0;"> -->
              <div style="position: absolute; top: 0;" :style="{ 'left': isEnglish ? '-120px' : '-58px' }">
                <span style="color: red">*</span>
                {{ i18n('背景图') }}
              </div>
              <span>{{ i18n('页面背景建议尺寸750*1624px，转盘底座建议尺寸为750*840px，支持jpg/jpeg/png，大小不超过1M') }}</span>
            </div>
            <div class="becorationContent" style="display: flex;">
              <el-form-item prop="propBackgroundImage" style="width: 100px">
                <upload-img v-model="imgData.propBackgroundImage" :signature-result="credential" @change="handleChange"
                  width="80px" height="80px" :imgFormat="imgFormat"></upload-img>
              </el-form-item>
              <el-form-item prop="propBaseBackgroundImage" style="width: 100px">
                <upload-img v-model="imgData.propBaseBackgroundImage" :signature-result="credential" width="80px"
                  height="80px" @change="handleChange" :imgFormat="imgFormat"></upload-img>
              </el-form-item>
            </div>
          </div>
          <!-- 转盘装修 -->
          <div class="turntableDecoration" style="margin-top: 24px">
            <div class="title">{{ i18n('转盘装修') }}</div>
            <!-- <div class="prompt becorationContent">
            <div style="position: absolute; left: -70px; top: 0">*转盘区块</div>
            <span>支持装修区块背景、区块文字颜色；区域图片尺寸建议198*271像素，支持jpg/jpeg/png，大小不超过1M</span>
          </div>
          <div class="content">
            <div class="jackpotBg">
              <div class="select">
                <span>中奖区块背景</span>
                <upload-img
                  v-model="imgData.propTurntableWinArea.backgroundImages[0]"
                  :signature-result="credential"
                  @validate="handleValidate"
                  width="80px"
                  height="80px"
                  @change="handleChange"
                  :imgFormat="imgFormat"
                ></upload-img>
              </div>
              <div class="unchecked">
                <span>未中奖区块背景</span>
                <img class="img" src="@/assets/image/fellow/ic_info.png" alt="" />
                <upload-img
                  v-model="imgData.propTurntableNoWinArea.backgroundImages[0]"
                  :signature-result="credential"
                  @validate="handleValidate"
                  width="80px"
                  height="80px"
                  @change="handleChange"
                  :imgFormat="imgFormat"
                  style="margin-right: 8px"
                ></upload-img>
                <upload-img
                  v-model="imgData.propTurntableNoWinArea.backgroundImages[1]"
                  :signature-result="credential"
                  @validate="handleValidate"
                  width="80px"
                  height="80px"
                  @change="handleChange"
                  :imgFormat="imgFormat"
                ></upload-img>
              </div>
            </div>
            <div class="jackpotText">
              <p class="set">
                <span>中奖区块文字颜色</span>
              </p>
              <el-color-picker
                style="width: 32px; margin-left: 8px"
                @change="handleChange"
                v-model="imgData.propTurntableWinArea.fontColor"
                size="small"
              ></el-color-picker>
              <p class="set" style="margin-left: 133px">
                <span>未中奖区块文字颜色</span>
              </p>
              <el-color-picker
                style="width: 32px; margin-left: 8px"
                @change="handleChange"
                v-model="imgData.propTurntableNoWinArea.fontColor"
                size="small"
              ></el-color-picker>
            </div>
          </div> -->
            <div class="prompt becorationContent">
              <div style="position: absolute;  top: 0" :style="{ 'left': isEnglish ? '-135px' : '-95px' }">
                <span style="color: red">*</span>
                {{ i18n('转盘指针图片') }}
              </div>
              <span>{{ i18n('请确保图片指针方向朝上，图片比例支持1:1（建议尺寸276*276像素），支持jpg/jpeg/png，大小不超过1M') }}</span>
            </div>
            <div class="becorationContent">
              <el-form-item prop="propTurntablePointerImage">
                <upload-img v-model="imgData.propTurntablePointerImage" :signature-result="credential"
                  @validate="handleValidate" width="80px" height="80px" @change="handleChange"
                  :imgFormat="imgFormat"></upload-img>
              </el-form-item>
            </div>
          </div>
          <!-- 按钮装修 -->
          <div class="buttonDecoration" style="margin-top: 30px">
            <div class="title">{{ i18n('按钮装修') }}</div>
            <div class="prompt becorationContent">
              <div style="position: absolute; top: 0" :style="{ 'left': isEnglish ? '-100px' : '-70px' }">
                <span style="color: red">*</span>
                {{ i18n('规则按钮') }}
              </div>
              <span>{{ i18n('支持配置规则按钮颜色、按钮文字颜色') }}</span>
            </div>
            <div class="content">
              <div class="jackpotText">
                <p class="set" style="margin-left: 52px">
                  <span>{{ i18n('文字颜色') }}</span>
                </p>
                <el-form-item prop="propRuleButton.fontColor">
                  <el-color-picker style="width: 80px; margin-left: 8px" @change="handleChange"
                    v-model="imgData.propRuleButton.fontColor" size="small"></el-color-picker>
                </el-form-item>
                <p class="set" style="margin-left: 100px">
                  <span>{{ i18n('背景颜色') }}</span>
                </p>
                <el-form-item prop="propRuleButton.backgroundColor">
                  <el-color-picker style="width: 80px; margin-left: 8px" @change="handleChange"
                    v-model="imgData.propRuleButton.backgroundColor" size="small"></el-color-picker>
                </el-form-item>
              </div>
            </div>

            <div class="prompt becorationContent">
              <div style="position: absolute; top: 0" :style="{ 'left': isEnglish ? '-100px' : '-70px' }">
                <span style="color: red">*</span>
                {{ i18n('分享按钮') }}
              </div>
              <span>{{ i18n('支持配置分享按钮颜色、按钮文字颜色') }}</span>
            </div>
            <div class="content">
              <div class="jackpotText">
                <p class="set" style="margin-left: 52px">
                  <span>{{ i18n('文字颜色') }}</span>
                </p>
                <el-form-item prop="propShareButton.fontColor">
                  <el-color-picker style="width: 80px; margin-left: 8px" @change="handleChange"
                    v-model="imgData.propShareButton.fontColor" size="small"></el-color-picker>
                </el-form-item>
                <p class="set" style="margin-left: 100px">
                  <span>{{ i18n('背景颜色') }}</span>
                </p>
                <el-form-item prop="propShareButton.backgroundColor">
                  <el-color-picker style="width: 80px; margin-left: 8px" @change="handleChange"
                    v-model="imgData.propShareButton.backgroundColor" size="small"></el-color-picker>
                </el-form-item>
              </div>
            </div>

            <div class="prompt becorationContent">
              <div style="position: absolute; top: 0" :style="{ 'left': isEnglish ? '-120px' : '-88px' }">
                <span style="color: red">*</span>
                {{ i18n('转盘按钮') }}
                <el-tooltip placement="right-start" effect="light">
                  <div slot="content">
                    <div style="font-weight: 700; font-size: 12px; color: #36445a; margin-bottom: 10px">{{
                      i18n('转盘按钮')}}
                    </div>
                    <img style="width: 326px" src="@/assets/image/fellow/img_zhuanpananniu.png" alt="" />
                  </div>
                  <img class="img" src="@/assets/image/fellow/ic_info.png" alt="" />
                </el-tooltip>
              </div>
              <span>{{ i18n('支持配置抽奖按钮区域图片、抽奖机会区域图片以及对应的文字颜色；图片支持jpg/jpeg/png，大小不超过1M') }}</span>
            </div>
            <div class="content">
              <div class="hands" style="margin-left: 22px">
                {{ i18n('点击小手图片') }}
                <span>{{ i18n('尺寸建议152*152像素') }}</span>
              </div>
              <el-form-item prop="propClickHandImage" style="margin-left: 112px">
                <upload-img v-model="imgData.propClickHandImage" :signature-result="credential" width="80px"
                  height="80px" @change="handleChange" :imgFormat="imgFormat"></upload-img>
              </el-form-item>
              <div class="btnBox" style="margin-top: 20px">
                <div>
                  <div class="hands">
                    {{ i18n('抽奖按钮背景图片') }}
                    <span>{{ i18n('尺寸建议488*152像素') }}</span>
                  </div>
                  <el-form-item prop="propLotteryButton.backgroundImages[0]" style="margin-left: 112px">
                    <upload-img v-model="imgData.propLotteryButton.backgroundImages[0]" :signature-result="credential"
                      @validate="handleValidate" width="80px" height="80px" @change="handleChange"
                      :imgFormat="imgFormat"></upload-img>
                  </el-form-item>
                </div>
                <div style="margin-left: 45px">
                  <div class="hands">
                    {{ i18n('次数提示背景图片') }}
                    <span>{{ i18n('尺寸建议248*40像素') }}</span>
                  </div>
                  <el-form-item prop="propTimesPrompt.backgroundImages[0]" style="margin-left: 112px">
                    <upload-img v-model="imgData.propTimesPrompt.backgroundImages[0]" :signature-result="credential"
                      @validate="handleValidate" width="80px" height="80px" @change="handleChange"
                      :imgFormat="imgFormat"></upload-img>
                  </el-form-item>
                </div>
              </div>
              <div class="jackpotText">
                <p class="set">
                  <span>{{ i18n('抽奖按钮文字颜色') }}</span>
                </p>
                <el-form-item prop="propLotteryButton.fontColor" style="margin-left: 8px">
                  <el-color-picker style="width: 140px" @change="handleChange"
                    v-model="imgData.propLotteryButton.fontColor" size="small"></el-color-picker>
                </el-form-item>
                <p class="set" style="margin-left: 20px">
                  <span>{{ i18n('次数提示文字颜色') }}</span>
                </p>
                <el-form-item prop="propTimesPrompt.fontColor" style="margin-left: 8px">
                  <el-color-picker style="width: 140px" @change="handleChange"
                    v-model="imgData.propTimesPrompt.fontColor" size="small"></el-color-picker>
                </el-form-item>
              </div>
            </div>
            <div class="prompt becorationContent">
              <div style="position: absolute; top: 0" :style="{ 'left': isEnglish ? '-120px' : '-88px' }">
                <span style="color: red">*</span>
                {{ i18n('底部按钮') }}
                <el-tooltip placement="right-start" effect="light">
                  <div slot="content">
                    <div style="font-weight: 700; font-size: 12px; color: #36445a; margin-bottom: 10px">{{
                      i18n('底部按钮')}}
                    </div>
                    <img style="width: 326px" src="@/assets/image/fellow/img_dibuanniu.png" alt="" />
                  </div>
                  <img class="img" src="@/assets/image/fellow/ic_info.png" alt="" />
                </el-tooltip>
              </div>
              <span>{{ i18n('支持配置底部按钮的背景颜色、图标、文字颜色') }}</span>
            </div>
            <div class="content">
              <div class="btnText">{{ i18n('按钮1：我的奖品') }}</div>
              <div class="btnIcon">
                {{ i18n('图标') }}
                <span>{{ i18n('图片比例支持1:1（建议尺寸40*40像素），支持jpg/jpeg/png，大小不超过1M') }}</span>
              </div>
              <el-form-item prop="propMyPrize.image" style="margin-left: 112px">
                <upload-img v-model="imgData.propMyPrize.image" :signature-result="credential" width="80px"
                  height="80px" @change="handleChange" :imgFormat="imgFormat"></upload-img>
              </el-form-item>
              <div class="jackpotText" style="margin-top: 12px">
                <p class="set" style="margin-left: 48px">
                  <span>{{ i18n('按钮颜色') }}</span>
                </p>
                <el-form-item prop="propMyPrize.backgroundColor" style="margin-left: 8px">
                  <el-color-picker style="width: 100px" @change="handleChange"
                    v-model="imgData.propMyPrize.backgroundColor" size="small"></el-color-picker>
                </el-form-item>
                <p class="set" style="margin-left: 70px">
                  <span>{{ i18n('文案颜色') }}</span>
                </p>
                <el-form-item prop="propMyPrize.fontColor" style="margin-left: 8px">
                  <el-color-picker style="width: 100px" @change="handleChange" v-model="imgData.propMyPrize.fontColor"
                    size="small"></el-color-picker>
                </el-form-item>
              </div>
            </div>
            <div class="content">
              <div class="btnText">{{ i18n('按钮2：获取机会') }}</div>
              <div class="btnIcon">
                {{ i18n('图标') }}
                <span>{{ i18n('图片比例支持1:1（建议尺寸40*40像素），支持jpg/jpeg/png，大小不超过1M') }}</span>
              </div>
              <el-form-item prop="propObtainOpportunity.image" style="margin-left: 112px">
                <upload-img v-model="imgData.propObtainOpportunity.image" :signature-result="credential" width="80px"
                  height="80px" @change="handleChange" :imgFormat="imgFormat"></upload-img>
              </el-form-item>
              <div class="jackpotText" style="margin-top: 12px">
                <p class="set" style="margin-left: 48px">
                  <span>{{ i18n('按钮颜色') }}</span>
                </p>
                <el-form-item prop="propObtainOpportunity.backgroundColor" style="margin-left: 8px">
                  <el-color-picker style="width: 100px" @change="handleChange"
                    v-model="imgData.propObtainOpportunity.backgroundColor" size="small"></el-color-picker>
                </el-form-item>
                <p class="set" style="margin-left: 70px">
                  <span>{{ i18n('文案颜色') }}</span>
                </p>
                <el-form-item prop="propObtainOpportunity.fontColor" style="margin-left: 8px">
                  <el-color-picker style="width: 100px" @change="handleChange"
                    v-model="imgData.propObtainOpportunity.fontColor" size="small"></el-color-picker>
                </el-form-item>
              </div>
            </div>
          </div>
        </el-form>
        <el-form :model="imgData" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm"></el-form>
      </div>
      <div class="panel-right">
        <div class="effect">{{ i18n('预览效果') }}</div>
        <div class="explain">{{ i18n('查看设计说明') }}</div>
        <div class="imgBox">
          <div class="top"></div>
          <div class="bottom">
            <img class="bg" v-show="imgData.propBackgroundImage" :src="imgData.propBackgroundImage" alt="" />
            <img class="baseBack" v-show="imgData.propBaseBackgroundImage" :src="imgData.propBaseBackgroundImage"
              alt="" />
            <img class="chou" v-show="imgData.propTurntablePointerImage" :src="imgData.propTurntablePointerImage"
              alt="" />
            <div class="sectorImg">
              <div class="sector">
                <img :src="imgData.propTurntableWinArea.backgroundImages[0]" alt="" />
                <span :style="{ color: imgData.propTurntableWinArea.fontColor }">{{ i18n('饮品满减券') }}</span>
                <img class="icon" src="@/assets/image/fellow/ic_dazhuanpan_gift.png" alt="" />
              </div>
              <div class="sector" style="transform: rotate(45deg); transform-origin: bottom center">
                <img :src="imgData.propTurntableNoWinArea.backgroundImages[1]" alt="" />
                <span :style="{ color: imgData.propTurntableNoWinArea.fontColor }">{{ i18n('饮品满减券') }}</span>
                <img class="icon" src="@/assets/image/fellow/ic_dazhuanpan_gift.png" alt="" />
              </div>
              <div class="sector" style="transform: rotate(90deg); transform-origin: bottom center">
                <img :src="imgData.propTurntableNoWinArea.backgroundImages[0]" alt="" />
                <span :style="{ color: imgData.propTurntableNoWinArea.fontColor }">{{ i18n('饮品满减券') }}</span>
                <img class="icon" src="@/assets/image/fellow/ic_dazhuanpan_gift.png" alt="" />
              </div>
              <div class="sector" style="transform: rotate(135deg); transform-origin: bottom center">
                <img :src="imgData.propTurntableNoWinArea.backgroundImages[1]" alt="" />
                <span :style="{ color: imgData.propTurntableNoWinArea.fontColor }">{{ i18n('饮品满减券') }}</span>
                <img class="icon" src="@/assets/image/fellow/ic_dazhuanpan_gift.png" alt="" />
              </div>
              <div class="sector" style="transform: rotate(180deg); transform-origin: bottom center">
                <img :src="imgData.propTurntableNoWinArea.backgroundImages[0]" alt="" />
                <span :style="{ color: imgData.propTurntableNoWinArea.fontColor }">{{ i18n('饮品满减券') }}</span>
                <img class="icon" src="@/assets/image/fellow/ic_dazhuanpan_gift.png" alt="" />
              </div>
              <div class="sector" style="transform: rotate(225deg); transform-origin: bottom center">
                <img :src="imgData.propTurntableNoWinArea.backgroundImages[1]" alt="" />
                <span :style="{ color: imgData.propTurntableNoWinArea.fontColor }">{{ i18n('饮品满减券') }}</span>
                <img class="icon" src="@/assets/image/fellow/ic_dazhuanpan_gift.png" alt="" />
              </div>
              <div class="sector" style="transform: rotate(270deg); transform-origin: bottom center">
                <img :src="imgData.propTurntableNoWinArea.backgroundImages[0]" alt="" />
                <span :style="{ color: imgData.propTurntableNoWinArea.fontColor }">{{ i18n('饮品满减券') }}</span>
                <img class="icon" src="@/assets/image/fellow/ic_dazhuanpan_gift.png" alt="" />
              </div>
              <div class="sector" style="transform: rotate(315deg); transform-origin: bottom center">
                <img :src="imgData.propTurntableNoWinArea.backgroundImages[1]" alt="" />
                <span :style="{ color: imgData.propTurntableNoWinArea.fontColor }">{{ i18n('饮品满减券') }}</span>
                <img class="icon" src="@/assets/image/fellow/ic_dazhuanpan_gift.png" alt="" />
              </div>
            </div>
            <div class="btns">
              <div :style="{ background: imgData.propMyPrize.backgroundColor }">
                <img v-show="imgData.propMyPrize.image" :src="imgData.propMyPrize.image" alt="" />
                <span :style="{ color: imgData.propMyPrize.fontColor }">{{ i18n('我的奖品') }}</span>
              </div>
              <div :style="{ background: imgData.propObtainOpportunity.backgroundColor }">
                <img v-show="imgData.propObtainOpportunity.image" :src="imgData.propObtainOpportunity.image" alt="" />
                <span :style="{ color: imgData.propObtainOpportunity.fontColor }">{{ i18n('获取机会') }}</span>
              </div>
            </div>
            <div class="clickLottery">
              <span :style="{ color: imgData.propLotteryButton.fontColor }">{{ i18n('点击抽奖') }}</span>
              <img v-show="imgData.propLotteryButton.backgroundImages[0]"
                :src="imgData.propLotteryButton.backgroundImages[0]" alt="" />
            </div>
            <div class="frequency">
              <span :style="{ color: imgData.propTimesPrompt.fontColor }" class="ellipsis" :title="i18n('你还有3次抽奖机会')">{{
                i18n('你还有3次抽奖机会')}}</span>
              <img v-show="imgData.propTimesPrompt.backgroundImages[0]"
                :src="imgData.propTimesPrompt.backgroundImages[0]" alt="" />
            </div>
            <div class="ruleWz"
              :style="{ color: imgData.propRuleButton.fontColor, background: imgData.propRuleButton.backgroundColor }">
              {{
              i18n('规则')}}</div>
            <div class="shareWz"
              :style="{ color: imgData.propShareButton.fontColor, background: imgData.propShareButton.backgroundColor }">
              {{
              i18n('分享')}}</div>
          
            <img class="finger" :src="imgData.propClickHandImage" alt="" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./PageTurntableEdit.ts">
</script>

<style lang="scss" scoped>
::v-deep .el-form-item__content {
  margin-left: 0 !important;
}

::v-deep .el-form-item {
  margin-bottom: 0 !important;
}

.page-turntable-edit {
  width: 100%;

  .panel {
    background: #ffffff;
    border-radius: 8px;
    padding: 24px;
    display: flex;

    .panel-left {
      width: 750px;
      margin-right: 42px;
    }

    .title {
      font-weight: 600;
      font-size: 16px;
      color: #242633;
      line-height: 24px;
    }

    .prompt {
      position: relative;
      font-weight: 400;
      font-size: 13px;
      color: #36445a;
      line-height: 20px;
      margin-left: 57px;
      margin-top: 20px;

      span {
        font-weight: 400;
        font-size: 13px;
        color: #79879e;
        line-height: 20px;

      }

      .img {
        width: 16px;
        height: 16px;
        cursor: pointer;
        vertical-align: middle;
        margin-bottom: 3px;
      }
    }

    .becorationContent {
      margin-left: 108px;
      margin-top: 6px;
    }

    .content {
      margin-left: 108px;
      background: #f6f7f9;
      padding: 20px;
      border-radius: 4px;
      margin-top: 6px;

      .set {
        margin-top: 7px;
      }

      .jackpotBg {
        display: flex;
        margin-bottom: 24px;

        .select {
          display: flex;
          margin: 0 80px 0 46px;

          span {
            margin-right: 8px;
            font-weight: 400;
            font-size: 13px;
            color: #36445a;
            line-height: 20px;
          }
        }

        .unchecked {
          display: flex;

          span {
            font-weight: 400;
            font-size: 13px;
            color: #36445a;
            line-height: 20px;
          }

          .img {
            width: 16px;
            height: 16px;
            cursor: pointer;
            margin-top: 2px;
            margin-right: 8px;
          }
        }
      }

      .jackpotText {
        font-weight: 400;
        font-size: 13px;
        color: #36445a;
        line-height: 20px;
        display: flex;
      }

      .hands {
        font-weight: 400;
        font-size: 13px;
        color: #36445a;
        line-height: 20px;
        margin-bottom: 6px;

        span {
          margin-left: 12px;
          font-size: 13px;
          color: #79879e;
          line-height: 20px;
        }
      }

      .btnBox {
        display: flex;
        margin-bottom: 20px;
      }

      .btnText {
        font-weight: 600;
        font-size: 13px;
        color: #36445a;
        line-height: 20px;
      }

      .btnIcon {
        margin-left: 74px;
        margin-top: 18px;
        margin-bottom: 8px;
        font-weight: 400;
        font-size: 13px;
        color: #36445a;
        line-height: 20px;

        span {
          margin-left: 12px;
          font-weight: 400;
          font-size: 13px;
          color: #79879e;
          line-height: 20px;
        }
      }
    }

    .panel-right {
      position: relative;
      width: 310px;
      height: 634px;
      background: #f0f2f6;

      .effect {
        position: absolute;
        left: 20px;
        top: 24px;
        font-weight: 500;
        font-size: 16px;
        color: #242633;
        line-height: 22px;
      }

      .explain {
        position: absolute;
        right: 20px;
        top: 27px;
        font-weight: 400;
        font-size: 13px;
        color: #007eff;
        line-height: 18px;
      }

      .imgBox {
        position: relative;
        width: 250px;
        height: 541px;
        background: #fff;
        margin: 62px auto 0;
        overflow: hidden;

        .bottom {
          width: 100%;
          height: 100%;
          position: absolute;
          bottom: 0;
          left: 0;

          .bg {
            width: 100%;
            height: 483px;
            position: absolute;
            bottom: 0;
            left: 0;
          }

          .baseBack {
            width: 100%;
            height: 280px;
            position: absolute;
            top: 168px;
            left: 50%;
            transform: translateX(-50%);
          }

          .chou {
            width: 96px;
            height: 96px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1;
          }

          .sectorImg {
            width: 190px;
            height: 190px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);

            .sector {
              height: 95px;
              position: absolute;
              top: 0;
              left: 60px;

              img {
                height: 100%;
              }

              .icon {
                width: 15px;
                height: 15px;
                position: absolute;
                top: 40px;
                left: 50%;
                transform: translateX(-50%);
              }

              span {
                width: 100%;
                text-align: center;
                position: absolute;
                top: 15px;
                left: 50%;
                font-size: 10px;
                transform: translateX(-50%);
              }
            }
          }

          .btns {
            width: 185px;
            position: absolute;
            bottom: 60px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            justify-content: space-between;

            div {
              width: 90px;
              height: 30px;
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 15px;

              img {
                width: 15px;
                height: 15px;
                margin-right: 5px;
              }

              span {
                font-size: 12px;
              }
            }
          }

          .clickLottery {
            width: 130px;
            height: 42px;
            position: absolute;
            bottom: 110px;
            left: 50%;
            transform: translateX(-50%);

            span {
              position: absolute;
              top: 10px;
              left: 50%;
              transform: translateX(-50%);
              font-weight: 700;
              color: #fff;
              font-size: 15px;
              white-space: nowrap;
            }

            img {
              width: 100%;
              height: 42px;
            }
          }

          .frequency {
            width: 80px;
            height: 13px;
            position: absolute;
            bottom: 145px;
            left: 105px;

            span {
              width: 100%;
              position: absolute;
              text-align: center;
              top: 2px;
              font-size: 8px;
              white-space: nowrap;
              overflow: hidden;
            }


            .ellipsis {
              display: inline-block;
              max-width: 100%;
              text-overflow: ellipsis;
              overflow: hidden;
              vertical-align: middle;
            }

            img {
              width: 100%;
              height: 100%;
            }
          }

          .ruleWz {
            width: 25px;
            height: 25px;
            line-height: 25px;
            text-align: center;
            border-radius: 50%;
            position: absolute;
            right: 10px;
            top: 70px;
            font-size: 9px;
          }

          .shareWz {
              width: 25px;
            height: 25px;
            line-height: 25px;
            text-align: center;
            border-radius: 50%;
            position: absolute;
            right: 10px;
            top: 102px;
            font-size: 9px;
          }

          .finger {
            width: 60px;
            position: absolute;
            right: 35px;
            bottom: 85px;
          }
        }
      }
    }
  }
}
</style>