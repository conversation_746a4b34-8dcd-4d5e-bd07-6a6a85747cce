<!--
 * @Author: 黎钰龙
 * @Date: 2024-03-12 11:01:03
 * @LastEditTime: 2024-03-20 13:37:01
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\selectAllStoreByDialog\SelectAllStoreByDialog.vue
 * 记得注释
-->
<template>
  <div class="select-store-by-dialog">
    <FormItem :label="label ? label : undefined">
      <slot name="label" slot="label"></slot>
      <div style="height: 36px; line-height: 36px">
        <template v-if="isAllStore">{{i18n('全部门店')}}</template>
        <template v-else>{{i18n('部分门店')}}</template>
        <span v-if="canEdit" class="span-btn" style="margin-left:6px" @click="doEdit">
          {{formatI18n('/公用/按钮','修改')}}
        </span>
      </div>
    </FormItem>
    <el-dialog ref="storeDialog" append-to-body :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false"
      :title="formatI18n('/公用/按钮','修改') + formatI18n('/资料/门店','门店')" :visible.sync="dialogShow" width="1000px">
      <div style="padding:12px">
        <ActiveStore no-i18n :isOldActivity="false" ref="store" v-model="bindStoreValue">
        </ActiveStore>
      </div>
      <div style="display:flex;flex-direction:row-reverse;margin-top:24px">
        <el-button @click="doConfirm" size="large" type="primary" style="margin-left:12px">{{formatI18n('/公用/按钮', '确定')}}</el-button>
        <el-button @click="doCancel" size="large">{{formatI18n('/公用/按钮', '取消')}}</el-button>
      </div>
    </el-dialog>
  </div>

</template>

<script lang="ts" src="./SelectAllStoreByDialog.ts">
</script>

<style lang="scss" scoped>
</style>