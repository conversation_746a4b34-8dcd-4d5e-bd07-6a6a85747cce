// 支付商品
export default class BTradeGoods {
  // 商品行号
  itemNo: string = ''
  // 商品ID
  id: Nullable<string> = null
  // 商品代码
  code: Nullable<string> = null
  // 商品名称
  name: Nullable<string> = null
  // 商品条码
  barcode: string = ''
  // 品牌代码
  brandCode: Nullable<string> = null
  // 类别代码
  categoryCode: Nullable<string> = null
  // 规格
  qpcStr: Nullable<string> = null
  // 单价
  price: Nullable<number> = null
  // 数量
  qty: number = 0
  // 原总金额
  stdAmount: Nullable<number> = null
  // 优惠金额
  favAmount: Nullable<number> = null
}
