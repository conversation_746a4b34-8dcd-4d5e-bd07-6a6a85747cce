<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:02
 * @LastEditTime: 2023-07-28 17:26:34
 * @LastEditors: 黎钰龙
 * @Description:
 * @FilePath: \phoenix-web-ui\src\cmp\formitem\FormItem.vue
 * 记得注释
-->
<template>
  <div class="member-form-item">
    <div class="member-form-icon wrap"
         v-if="$slots.prefix || prefixIcon">
      <slot name="prefix"></slot>
      <i v-if="prefixIcon"
         :class="prefixIcon"></i>
    </div>
    <label class="member-form-label wrap"
           v-if="label || $slots.label">
      <slot name="label">{{ label }}</slot>
    </label>
    <el-tooltip class="member-form-content match"
                :class="{'text-right':alignRight}"
                placement="top-start" :disabled="!(tooltip && hasEllipsis)">
      <div slot="content" v-if="tooltip && hasEllipsis"><slot></slot></div>
      <div ref="content"><slot></slot></div>
    </el-tooltip>
    <div class="member-form-right wrap"
         v-if="$slots.right">
      <slot name="right"></slot>
    </div>
  </div>
</template>

<script lang="ts"
        src="./MemberFormItem.ts">
</script>

<style lang="scss"
       scoped>
.member-form-item {
  box-sizing: border-box;
  display: flex;
  font-weight: 400;
  font-size: 14px;
  line-height: 30px;
  align-items: center;

  & + & {
    margin-top: 2px;
  }

  .match {
    flex-grow: 1;
  }

  .wrap {
    flex-shrink: 0;
  }

  .text-right {
    text-align: right;
  }

  .member-form-icon {
    width: 18px;
    height: 18px;
    margin-right: 8px;
    display: inline-flex;
  }

  .member-form-label {
    color: #79879E;
    //margin-right: 8px;
  }

  .member-form-content {
    color: #242633;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .member-form-right {
    margin-left: 8px;
  }
}
</style>
