

// 会员积分分析报表响应
import {AnalysisReportDateUnit} from "model/analysis/AnalysisReportDateUnit";
import MemberPointsAnalysisSummary from "model/analysis/MemberPointsAnalysisSummary";
import AnalysisChartData from "model/analysis/AnalysisChartData";

export default class MemberPointsAnalysisReport {
  // 时间维度
  dateUnit: Nullable<AnalysisReportDateUnit> = null
  // 数据概览
  summary: Nullable<MemberPointsAnalysisSummary> = null
  // 发放积分指标
  occurredPointsData: AnalysisChartData[] = []
  // 消耗积分指标
  consumePointsData: AnalysisChartData[] = []
  // 调整增加积分指标
  adjustAddPointsData: AnalysisChartData[] = []
  // 调整减少积分指标
  adjustSubPointsData: AnalysisChartData[] = []
  // 过期积分指标
  expiredPointsData: AnalysisChartData[] = []
  // 可用积分指标（截止查询结束日期）
  usablePointsData: AnalysisChartData[] = []
}