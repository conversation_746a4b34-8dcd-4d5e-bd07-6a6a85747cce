export default class CardTemplateFilter {
  // 模板号类似于
  numberLikes: Nullable<string> = null
  // 名称类似于
  nameLikes: Nullable<string> = null
  // 模板号或名称类似与
  numberOrNameLikes: Nullable<string> = null
  // 模板号或名称起始于
  numberOrNameStartsWith: Nullable<string> = null
  // 卡类型等于
  cardTemplateTypeEquals: Nullable<string> = null
  // 卡类型等于
  typeIn: Nullable<string[]> = null
  // 卡介质包含
  cardMediumIn: Nullable<string[]> = null
  // 账户类型id等于
  accountTypeIdEquals: Nullable<string> = null
  // 页码
  page: Nullable<number> = null
  // 页面大小，大于0
  pageSize: Nullable<number> = null
  // 排序字段
  sortKey: Nullable<string> = null
  // 是否倒序
  desc: Nullable<boolean> = null
  cardMediumEquals: Nullable<string>= null
}