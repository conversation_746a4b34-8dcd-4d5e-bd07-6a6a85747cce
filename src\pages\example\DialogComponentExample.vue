<template>
  <div>
    <el-dialog title="示例模态框" :visible.sync="dialogShow" append-to-body>
      <span>hello</span>
      <div class="dialog-footer" slot="footer">
        <el-button @click="doCancel">取消</el-button>
        <el-button @click="doModalClose" size="small" type="primary">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" src="./DialogComponentExample.ts">
</script>

<style lang="scss">
.dialog-example {
}
</style>