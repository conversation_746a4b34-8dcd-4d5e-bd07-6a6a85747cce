<template>
  <div class="mbr-card-title">
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="160px"
      class="demo-ruleForm"
    >
      <el-form-item
        :label="formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/会员卡标题')"
        class="cur_item"
        prop="title"
      >
        <el-input @change="doChange" maxlength="10" type="text" v-model="ruleForm.title"></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" src="./MbrCardTitle.ts">
</script>

<style lang="scss">
.mbr-card-title {
  .el-form-item__label {
    padding-right: 50px;
    color: #7f8fa4 !important;
  }
}
</style>