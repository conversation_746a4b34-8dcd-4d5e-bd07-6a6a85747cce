/*
 * @Author: 黎钰龙
 * @Date: 2025-02-18 16:29:31
 * @LastEditTime: 2025-02-28 15:38:28
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\analysis\BCustomerProfileDimension.ts
 * 记得注释
 */
import { CustomerProfileCategory } from "./CustomerProfileCategory"
import CustomerProfileGoupSection from "./CustomerProfileGoupSection"
import { CustomerProfileMemberProp } from "./CustomerProfileMemberProp"
import { CustomerProfileType } from "./CustomerProfileType"
import { DateGroupType } from "./DateGroupType"

// 维度
export default class BCustomerProfileDimension {
  // 维度
  category: Nullable<CustomerProfileCategory> = null
  // 标签id
  tagId: Nullable<string> = null
  // 标签名
  tagName: Nullable<string> = null
  // 会员属性：枚举
  memberProp: Nullable<CustomerProfileMemberProp> = null
  // 会员属性名
  memberPropName: Nullable<string> = null
  // 维度类型：date 日期,  num 数值, other 其它
  type: Nullable<CustomerProfileType> = null
  // 维度类型：num 数值，维度自定义分组
  customerProfileGoupSection: CustomerProfileGoupSection[] = []
  // 是否为默认分组
  useDefaultGoupSection: Nullable<boolean> = true
  // 维度类型：date 日期，维度自定义分组
  dateGroupType: Nullable<DateGroupType> = null
}