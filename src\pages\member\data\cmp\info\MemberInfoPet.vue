<template>
  <div class="member-card">
    <member-card-title :icon="require('~assets/image/member/<EMAIL>')"
                       :title="i18n('宠物资料')"></member-card-title>
    <template v-if="groupedData && groupedData.length > 0">
    <div v-for="(group, index) in groupedData"
         :key="index">
      <el-row>
        <el-col :span="8"
                v-for="(field, index) in group.fields"
                :key="index">
          <member-form-item :label="field.fieldKey + '：'">
            <template v-if="!isImgStr(field.value)">{{ field.value }}</template>
            <img v-else
                 :src="field.value"
                 style="width: 70px;height: 70px;">
          </member-form-item>
        </el-col>
      </el-row>
    </div>
    </template>
    <empty-data v-else></empty-data>
  </div>
</template>
<script lang="ts"
        src="./MemberInfoPet.ts">
</script>
<style lang="scss"
       scoped>
</style>
