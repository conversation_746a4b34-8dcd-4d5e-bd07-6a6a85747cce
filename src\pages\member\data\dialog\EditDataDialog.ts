import {Component, Prop, Vue, Watch} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import MemberApi from 'http/member_standard/MemberApi'
import MemberDetail from 'model/member_standard/MemberDetail'
import DateUtil from 'util/DateUtil'
import AddressSelector from "cmp/addressselector/AddressSelector";
import BrowserMgr from 'mgr/BrowserMgr'
import I18nPage from 'common/I18nDecorator'
import MemberEditConfig from "model/member_standard/MemberEditConfig";
import SystemConfigApi from 'http/systemConfig/SystemConfigApi'
import MemberPersonalDataConfigLine from 'model/systemConfig/MemberPersonalDataConfigLine'

@Component({
  name: 'EditDataDialog',
  components: {
    FormItem,
    AddressSelector,
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/会员/会员资料',
    '/公用/菜单'
  ],
  auto: true
})
export default class EditDataDialog extends Vue {
  options: any = []
  ruleForm: any = {
    mobile: '',
    name: '',
    lastName: '',
    gender: '',
    birthday: '',
    idCard: '',
    education: '',
    industry: '',
    annualIncome: '',
    hobbies: '',
    spareMobile: '',
    email: '',
    address: [],
    remark: '',
    nationality: '',
    religion: '',
    employeeID: '',
    office: '',
    addressInfo: ''
  }
  $refs: any
  query: MemberDetail = new MemberDetail()
  rules: any = {}
  oldMobile: Nullable<string> = null
  isShowFirstAndLastName: boolean = false //是否将姓和名拆开显示
  editConfig: MemberEditConfig = new MemberEditConfig();
  memberInfoList: MemberPersonalDataConfigLine[] = [] //已启用的会员资料
  customFieldValues: Record<string, string | null> = {} //自定义字段值

  @Prop()memberId: Nullable<string>
  @Prop()title: any
  @Prop({type: Boolean,default: false})dialogShow: boolean
  @Prop({ type: Number, default: null }) nameMaxLength: Nullable<number>;
  validateEmail: (rule: any, value: any, callback: any) => void
  validateMoile: (rule: any, value: any, callback: any) => void
  validateIdCard: (rule: any, value: any, callback: any) => void
  validateSpareMoile: (rule: any, value: any, callback: any) => void

  @Watch('dialogShow')
  onDataChange(value: any) {
    if (value) {
      if (this.memberId) {
        this.reloadMemberData(this.memberId)
      }
    }
  }

  // 已启用的自定义会员资料
  get enableCustomMemberAttr() {
    return this.memberInfoList.filter((item) => item.source === 'custom')
  }


  created() {
    this.getMemberEditConfig()
    this.validateMoile = (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error(this.formatI18n('/会员/会员资料', '请输入手机号') as any))
      } else {
        let regex = /^[0-9]\d*$/g
        if (regex.test(value as string)) {
          MemberApi.checkMobile(value as string, this.memberId as any).then((resp: any) => {
            if (resp && resp.code === 2000) {
              if (resp.data) {
                callback(new Error(this.formatI18n('/会员/会员资料', '手机号重复') as any))
              } else {
                callback()
              }
            }
            if (resp && resp.code !== 2000) {
              this.$message.error(resp.message)
            }
          }).catch((error: any) => {
            if (error && error.message) {
              this.$message.error(error.message)
            }
          })
        } else {
          if (!value) {
            callback(new Error(this.formatI18n('/会员/会员资料', '手机号最少1位最多18位数字') as any))
          } else {
            callback(new Error(this.formatI18n('/会员/会员资料', '手机号输入不合法') as any))
          }
        }
      }
    }

    this.validateSpareMoile = (rule: any, value: any, callback: any) => {
      if (value) {
        let regex = /^[0-9]\d*$/g
        if (regex.test(value as string)) {
          callback()
        } else {
          callback(new Error(this.formatI18n('/会员/会员资料', '手机号输入不合法') as any))
        }
      } else {
        callback()
      }
    }

    this.validateIdCard = (rule: any, value: any, callback: any) => {
      if (value) {
        let regex = /^[0-9a-zA-Z]+$/
        if (regex.test(value as string)) {
          callback()
        } else {
          callback(new Error(this.formatI18n('/会员/会员资料', '请输入正确的身份证号') as any))
        }
      } else {
        callback()
      }
    }

    this.validateEmail = (rule: any, value: any, callback: any) => {
      if (value) {
        let regex = /^([a-zA-Z]|[0-9])(\w|\-|\.)+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/
        if (regex.test(value as string)) {
          callback()
        } else {
          callback(new Error(this.formatI18n('邮箱格式不合法') as any))
        }
      } else {
        callback()
      }
    }
    this.rules = {
      mobile: [
        {validator: this.validateMoile, trigger: 'blur'}
      ],
      idCard: [
        {validator: this.validateIdCard, trigger: 'blur'}
      ],
      email: [
        {validator: this.validateEmail, trigger: 'blur'}
      ],
      spareMobile: [
        {validator: this.validateSpareMoile, trigger: 'blur'}
      ]
    }
    this.queryMemberInfo()
    this.initConfig()
  }

  initConfig() {
    this.isShowFirstAndLastName = BrowserMgr.SessionStorage.getItem("isShowFirstAndLastName");
  }

  // 查询已启用的会员资料字段
  queryMemberInfo() {
    SystemConfigApi.getEnable().then((res) => {
      if (res.code === 2000) {
        this.memberInfoList = res.data?.configLines || []
        this.$nextTick(() => {
          // 初始化自定义字段值映射
          this.enableCustomMemberAttr.forEach((config) => {
            if (config.customFieldName) {
              this.customFieldValues[config.customFieldName] = null
            }
          })
        })
      } else {
        throw new Error(res.msg!)
      }
    }).catch((err) => {
      this.$message.error(err.message)
    })
  }

  doBeforeClose(done: any) {
    this.$emit('dialogClose')
    done()
  }

  doModalClose() {
    this.$refs['ruleForm'].validate((valid: any) => {
      if (valid) {
        this.transPrams()
        MemberApi.saveMemberBasicData(this.query, this.memberId as any).then((resp: any) => {
          if (resp && resp.code === 2000) {
            this.$message.success(this.formatI18n('/会员/会员资料', '编辑成功') as any)
            this.$emit('dialogClose')
          }
        }).catch((error: any) => {
          if (error && error.message) {
            this.$message.error(error.message)
          }
        })
      } else {
        return false;
      }
    })
  }

  doCancel() {
    this.$emit('dialogClose')
  }

  private resetMemberData(data: MemberDetail) {
    console.log('看看data', data);
    this.ruleForm.mobile = data.mobile as any
    this.ruleForm.name = data.name as any
    this.ruleForm.lastName = data.lastName || ''
    this.ruleForm.gender = data.gender as any
    if (data.birthday) {
      this.ruleForm.birthday = DateUtil.format(data.birthday, 'yyyy-MM-dd')
    }
    this.ruleForm.idCard = data.idCard as any
    this.ruleForm.education = data.education as any
    this.ruleForm.industry = data.industry as any
    this.ruleForm.annualIncome = data.annualIncome as any
    this.ruleForm.hobbies = data.hobbies as any
    this.ruleForm.spareMobile = data.spareMobile as any
    this.ruleForm.email = data.email as any
    this.ruleForm.remark = ''
    this.ruleForm.religion = data.religion as any
    this.ruleForm.nationality = data.nationality as any
    this.ruleForm.employeeID = data.employeeID as any
    this.ruleForm.office = data.office as any
    if (data.province && data.province.name) {
      this.ruleForm.address = [data.province, data.city, data.district, data.street]
    }
    this.oldMobile = data.mobile
    this.ruleForm.addressInfo = data.address as any
    // 回填自定义字段
    if (data.extObj && JSON.parse(data.extObj)?.customFields) {
      const extObj = JSON.parse(data.extObj)
      Object.keys(extObj.customFields).forEach((key) => {
        if (Object.prototype.hasOwnProperty.call(this.customFieldValues, key)) {
          this.customFieldValues[key] = extObj.customFields[key]
        }
      })
    }
  }

  private reloadMemberData(member: Nullable<string>) {
    if (member == null) {
      return
    }
    MemberApi.editData(member).then((resp: any) => {
      if (resp && resp.code === 2000) {
        if (resp.data) {
          this.resetMemberData(resp.data)
        }
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })

  }

  private transPrams() {
    this.query.memberId = this.memberId
    this.query.oldMobile = this.oldMobile as any  // 常浩说默认是mobile
    this.query.mobile = this.ruleForm.mobile as any
    this.query.name = this.ruleForm.name
    this.query.lastName = this.ruleForm.lastName
    this.query.gender = this.ruleForm.gender
    this.query.nationality = this.ruleForm.nationality
    this.query.religion = this.ruleForm.religion
    if (this.ruleForm.birthday) {
      this.query.birthday = DateUtil.format(this.ruleForm.birthday, 'yyyy-MM-dd')
    } else {
      this.query.birthday = null
    }
    this.query.idCard = this.ruleForm.idCard
    this.query.education = this.ruleForm.education
    this.query.industry = this.ruleForm.industry
    this.query.annualIncome = this.ruleForm.annualIncome
    this.query.hobbies = this.ruleForm.hobbies
    this.query.spareMobile = this.ruleForm.spareMobile
    this.query.email = this.ruleForm.email
    this.query.remark = this.ruleForm.remark
    if (this.ruleForm.address && this.ruleForm.address.length > 0) {
      this.query.province = this.ruleForm.address[0]
      this.query.city = this.ruleForm.address[1]
      this.query.district = this.ruleForm.address[2]
      this.query.street = this.ruleForm.address[3]
    } else {
      this.query.province = null
      this.query.city = null
      this.query.district = null
      this.query.street = null
    }
    if (this.ruleForm.addressInfo && this.ruleForm.addressInfo.length > 0) {
      this.query.address = this.ruleForm.addressInfo
    } else {
      this.query.address = ''
    }
    this.query.employeeID = this.ruleForm.employeeID
    this.query.office = this.ruleForm.office
    this.query.extObj = JSON.stringify({ customFields: this.customFieldValues})
  }

  getMemberEditConfig() {
    MemberApi.getEditConfig().then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.editConfig = resp.data
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
}