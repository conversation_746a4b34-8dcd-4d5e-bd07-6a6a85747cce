/*
 * @Author: 黎钰龙
 * @Date: 2023-10-09 15:39:14
 * @LastEditTime: 2024-08-19 15:02:56
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\prepaycard\produce-card\ProduceCardEdit.ts
 * 记得注释
 */
import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import EditType from 'common/EditType';
import I18nPage from 'common/I18nDecorator';
import { Component, Vue, Watch } from 'vue-property-decorator';
import CardTplItem from '../cmp/cardtplitem/CardTplItem';
import CardTemplate from 'model/card/template/CardTemplate';
import IdName from 'model/common/IdName';
import ImprestCardSaleBillLine from 'model/card/salebill/ImprestCardSaleBillLine';
import MakeCardBill from 'model/prepay/card/MakeCardBill';
import AutoFixInput from 'cmp/autofixinput/AutoFixInput';
import CommonUtil from 'util/CommonUtil';
import MakeCardBillApi from 'http/prepay/card/MakeCardBillApi';
import RSOrgFilter from 'model/common/RSOrgFilter';
import OrgApi from 'http/org/OrgApi';
import RSOrg from 'model/common/RSOrg';
import ConstantMgr from 'mgr/ConstantMgr';
import PrepayAccountApi from 'http/prepay/account/PrepayAccountApi';
import SelectStores from 'cmp/selectStores/SelectStores';
import { CardMedium } from 'model/default/CardMedium';


class ImprestCardSaleBillFormLine extends ImprestCardSaleBillLine {
  checked: boolean = false
}

@Component({
  name: 'ProduceCardEdit',
  components: {
    BreadCrume,
    CardTplItem,
    AutoFixInput,
    SelectStores
  }
})
@I18nPage({
  prefix: [
    '/公用/按钮',
    '/公用/券模板',
    '/卡/卡管理/制卡单/制卡单列表',
    '/卡/卡管理/制卡单/制卡单详情',
    '/储值/预付卡/预付卡充值单',
    '/储值/预付卡/预付卡查询/列表页面',
    '/会员/洞察/公共/最近消费属性',
    '/资料/品牌',
    '/储值/预付卡/充值卡制售单/编辑页面'
  ],
  auto: true
})
export default class ProduceCardEdit extends Vue {
  panelArray: any = []
  editType: string = EditType.CREATE
  ruleForm: MakeCardBill = new MakeCardBill()
  rules: any = {}
  stores: RSOrg[] = []
  cardNumLength: number = 0 //  选中卡模板的卡号长度
  cardAttributeFix: boolean = false //是否隐藏 适用商品和发生门店
  selectCardMedium: Nullable<CardMedium> = null //选中卡模板的卡介质
  $refs: any

  @Watch('ruleForm.makeQty', { deep: true, immediate: true })
  handle() {
    this.computeEndCode()
  }

  get hasWriteType() {
    return this.selectCardMedium === CardMedium.mag
  }

  created() {
    this.getConfig()
    this.initPanelArray()
    this.initRules()
    this.getStore()
    this.initEditType()
  }

  private initPanelArray() {
    this.panelArray = [
      {
        name: this.i18n('制卡单'),
        url: 'produce-card-list'
      },
      {
        name: this.editType === 'edit' ? this.i18n('编辑制卡单') : this.i18n('新建制卡单'),
        url: ''
      }
    ]
  }

  private getConfig() {
    PrepayAccountApi.cardAttributeFix().then((res) => {
      this.cardAttributeFix = res.data || false
    })
  }

  private initEditType() {
    let editType = this.$route.query.editType as string
    if (editType) {
      this.editType = editType as string
    }
    if (editType === 'edit') {
      this.panelArray[1].name = this.i18n('编辑制卡单')
      this.getDetail(this.$route.query.billNumber as string)
    }
  }

  getDetail(billNumber: string) {
    const loading = this.$loading(ConstantMgr.loadingOption)
    MakeCardBillApi.get(billNumber).then((res) => {
      if (res.code === 2000) {
        this.ruleForm = res.data || new MakeCardBill()
        this.ruleForm.occurredOrg = res.data?.occurredOrg?.id as any
        this.cardNumLength = res.data?.startCardCode?.length || 0
        this.selectCardMedium = res.data?.cardMedium as any || null
      } else {
        this.$message.error(res.msg || this.i18n('获取详情失败'))
      }
    }).catch((error) => {
      this.$message.error(error.message || this.i18n('获取详情失败'))
    }).finally(() => {
      loading.close()
    })
  }

  private selectTpl(val: CardTemplate) {
    console.log('最终提交的卡模板', val);
    if (val) {
      this.ruleForm.cardTemplateName = val.name
      this.ruleForm.cardTemplateNumber = val.number
      this.ruleForm.cardMedium = val.cardMedium
      this.cardNumLength = val.cardCodeLength || 0
      this.selectCardMedium = val.cardMedium as any
      this.ruleForm.writeCardType = 'SYS'
    }
  }

  // 结束卡号 = 起始卡号 + 制卡数量 - 1
  computeStartNumber() {
    if (this.cardNumLength) {
      return this.i18n('卡号长度需为{0}位').replace(/\{0\}/g, String(this.cardNumLength))
    } else {
      return this.i18n('请输入起始卡号')
    }
  }

  //只能输入数字
  doStartCodeChange(val: string) {
    this.ruleForm.startCardCode = val.replace(/[^\d]/g, '')
    if (this.ruleForm.startCardCode.length > 18) {
      this.ruleForm.startCardCode = this.ruleForm.startCardCode.slice(0, 18)
    }
    this.computeEndCode()
  }

  //计算结束卡号
  computeEndCode() {
    if (this.ruleForm.makeQty && this.ruleForm.startCardCode) {
      this.ruleForm.endCardCode = CommonUtil.largeNumAdd(this.ruleForm.startCardCode, String(this.ruleForm.makeQty - 1))
    }
  }

  saveAndAudit() {
    this.$refs.form.validate().then(() => {
      const params = this.ruleForm
      params.occurredOrg = this.handleSelectStores(this.ruleForm.occurredOrg as any)
      if ([CardMedium.bar, CardMedium.online].indexOf(params.cardMedium as any) > -1) {
        params.writeCardType = 'NONE' //如果是电子卡或条码卡，写卡类型为 无需写卡
      } else if (params.cardMedium === CardMedium.rfic) {
        params.writeCardType = 'SYS'  //rfic只能是商家写卡
      }
      if (this.editType === 'edit') { //如果是修改并审核，要调两个接口
        return this.modifyAndAudit(params)
      }
      MakeCardBillApi.saveAndAudit(params).then((res) => {
        if (res.code === 2000) {
          this.$message.success(this.i18n('保存并审核成功'))
          this.$router.push({ name: 'produce-card-dtl', query: { billNumber: res.data } })
        } else {
          this.$message.error(res.msg || this.i18n('保存并审核失败'))
        }
      }).catch((error) => {
        this.$message.error(error.message || this.i18n('保存并审核失败'))
      })
    })
  }

  //修改制卡单 保存并审核
  modifyAndAudit(params: MakeCardBill) {
    MakeCardBillApi.saveModify(params).then((res) => {
      if (res.code === 2000) {
        this.doAudit(params.billNumber!)
      } else {
        this.$message.error(res.msg || this.i18n('保存失败'))
      }
    }).catch((error) => this.$message.error(error.message || this.i18n('保存失败')))
  }

  //审核
  doAudit(billNumber: string) {
    MakeCardBillApi.audit(billNumber).then((resp) => {
      if (resp.code === 2000) {
        this.$message.success(this.i18n('保存并审核成功'))
        this.$router.push({ name: 'produce-card-dtl', query: { billNumber: billNumber } })
      } else {
        this.$message.error(resp.msg || this.i18n('审核失败'))
      }
    }).catch((error) => this.$message.error(error.message || this.i18n('审核失败')))
  }

  //自动生成起始卡号
  autoMakeStartCode() {
    MakeCardBillApi.autoMaxCardCode(this.cardNumLength).then((res) => {
      if (res.data || res.code === 2000) {
        this.ruleForm.startCardCode = res.data || ''
        this.computeEndCode()
      } else {
        this.$message.error(res.msg || this.i18n('生成卡号失败'))
      }
    }).catch(error => this.$message.error(error.message || this.i18n('生成卡号失败')))
  }

  private getStore() {
    let params: RSOrgFilter = new RSOrgFilter()
    params.page = 0
    params.pageSize = 0
    OrgApi.query(params).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.stores = resp.data
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  save() {
    this.$refs.form.validate().then(() => {
      let saveFunc: any = MakeCardBillApi.save
      const params = this.ruleForm
      params.occurredOrg = this.handleSelectStores(this.ruleForm.occurredOrg as any)
      if ([CardMedium.bar, CardMedium.online].indexOf(params.cardMedium as any) > -1) {
        params.writeCardType = 'NONE' //如果是电子卡或条码卡，写卡类型为 无需写卡
      } else if (params.cardMedium === CardMedium.rfic) {
        params.writeCardType = 'SYS'  //rfic只能是商家写卡
      }
      if (this.editType === 'edit') {
        saveFunc = MakeCardBillApi.saveModify
      }
      saveFunc(params).then((res: any) => {
        if (res.code === 2000) {
          this.$message.success(this.i18n('保存成功'))
          this.$router.push({ name: 'produce-card-dtl', query: { billNumber: this.ruleForm.billNumber || res.data } })
        } else {
          this.$message.error(res.msg || this.i18n('保存失败'))
        }
      }).catch((error: any) => {
        this.$message.error(error.message || this.i18n('保存失败'))
      })
    })
  }

  handleSelectStores(id: any) {
    if (!id) return null
    const idName = new IdName()
    idName.id = id
    idName.name = this.stores.find(item => item.org.id === id)?.org.name
    return idName
  }

  initRules() {
    this.rules = {
      cardNumber: [{
        required: true,
        validator: (rule: any, value: any, callback: any) => {
          if (!this.ruleForm.cardTemplateNumber) {
            callback(new Error(this.i18n('请选择卡模板')))
          }
          callback()
        },
        trigger: "change",
      }],
      makeQty: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
      startCardCode: [{
        required: true,
        validator: (rule: any, value: any, callback: any) => {
          if (!this.ruleForm.cardTemplateNumber && !this.ruleForm.startCardCode) {
            callback(new Error(this.i18n('请填写起始卡号')))
          }
          if (this.ruleForm.cardTemplateNumber && this.ruleForm.startCardCode?.length !== this.cardNumLength) {
            callback(new Error(this.i18n('卡号长度需为{0}位').replace(/\{0\}/g, String(this.cardNumLength))))
          }
          callback()
        },
        trigger: "change",
      }],
      endCardCode: [{
        required: true,
        validator: (rule: any, value: any, callback: any) => {
          if (!this.ruleForm.endCardCode) {
            callback(new Error(this.i18n('请填写结束卡号')))
          }
          if (this.ruleForm.startCardCode?.length !== this.ruleForm.endCardCode?.length) {
            callback(new Error(this.i18n('结束卡号必须与起始卡号位数一致')))
          }
          callback()
        },
        trigger: "change",
      }],
      writeCardType: [{ required: true, message: this.formatI18n("/公用/js提示信息/请选择必选项"), trigger: "blur" }],
    }
  }
};