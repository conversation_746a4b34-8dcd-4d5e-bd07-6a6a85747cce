/*
 * @Author: 黎钰龙
 * @Date: 2024-05-14 13:32:40
 * @LastEditTime: 2024-05-14 13:32:46
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\promotion\marketApply\MarketingApplyActivity.ts
 * 记得注释
 */
import ActivityBody from 'model/common/ActivityBody'

// 营销申请 - 活动
export default class MarketingApplyActivity {
  // 活动
  activityBody: Nullable<ActivityBody> = null
  // 申请单号
  applyBillNumber: Nullable<string> = null
  // 审核原因
  auditReason: Nullable<string> = null
}