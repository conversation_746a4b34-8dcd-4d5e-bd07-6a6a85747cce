/*
 * @Author: 黎钰龙
 * @Date: 2025-04-10 16:09:52
 * @LastEditTime: 2025-04-10 16:09:59
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\default\GradePurchaseRecords.ts
 * 记得注释
 */

import GradeBasket from "./GradeBasket"

// 等级付费记录
export default class GradePurchaseRecords {
  // 交易UUID
  uuid: Nullable<string> = null
  // 交易ID
  tradeId: Nullable<string> = null
  // 会员ID
  memberId: Nullable<string> = null
  // 会员号
  crmCode: Nullable<string> = null
  // 手机号码
  mobile: Nullable<string> = null
  // 实体卡号
  hdCardCardNum: Nullable<string> = null
  // 姓名
  memberName: Nullable<string> = null
  // 支付方式
  payType: Nullable<string> = null
  // 支付时间
  payTime: Nullable<Date> = null
  // 支付金额
  payAmount: Nullable<number> = null
  // 交易内容
  basket: Nullable<GradeBasket> = null
  // 支付单号
  payNo: Nullable<string> = null
}