/*
 * @Author: 黎钰龙
 * @Date: 2024-08-01 16:15:02
 * @LastEditTime: 2025-03-13 16:09:27
 * @LastEditors: haiding <EMAIL>
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\cmp\page-props-components\global-set\GlobalSet.ts
 * 记得注释
 */
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import DefaultPagePlaceProperty from 'pages/invest/page-manage/cmp/page-props-components/DefaultPagePlaceProperty';
import ValidateForm from '../ValidateForm';
import I18nPage from 'common/I18nDecorator';

@Component({
  name: 'GlobalSet',
  mixins: [ValidateForm],
  components: {},
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理',
    '/页面/导航设置',
  ],
  auto: true
})
export default class GlobalSet extends Vue {
  @Prop({ type: Boolean, default: false })
  readonly: boolean; //
  @Prop({ type: String, default: 'GlobalSet' })
  validateName: string; // 组件校验名称，editPage通过该名称进行组件校验及校验信息清除
  @Prop({ type: Object })
  value: any; // 数据模型
  @Prop({ type: String, default: '页面标题名称' })
  label: string; // label名
  @Prop()
  formKey: any;
  @Prop({
    type: Object,
    default: () => {
      return {
        isShowColor: true,
      };
    },
  })
  config: any; // 配置项
  $refs: any;
  labelPosition: string = 'top';
  rules = {
    propTitle: [{ required: true, message: this.i18n('请输入页面标题名称'), trigger: ['blur', 'change'] }],
  };
  localValue = DefaultPagePlaceProperty.pageTitle()

  get globalInvestTitle() {
    return this.$store.state.globalInvestTitle
  }

  get customPlaceholderStr() {
    return `${this.i18n('请填写背景色css代码。示例')}:
      background: rgb(204,174,118);
      background: linear-gradient(0deg, rgba(204,174,118,1) 0%, rgba(111,84,43,1) 100%);`
  }

  handleChange() {
    this.$store.dispatch('globalInvestTitleAction', this.localValue)
    this.$emit('input', this.localValue);
    this.$emit('change', this.localValue);
  }

  mounted() {
    this.localValue = this.globalInvestTitle
    console.log('this.globalInvestTitle ==>', this.globalInvestTitle)
  }

  beforeDestroy() {
    // this.$bus.off('cms-addForm');
  }

  validate(callback: any) {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.validate();
    }
  }

  clearValidate() {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.clearValidate();
    }
  }

  showCustomAlert() {
    const contentHtml = `<div>${this.i18n('自定义颜色可以设置渐变色，操作如下')}:</div>
    <div>1. ${this.i18n('打开网址')}: <a href="https://lingdaima.com/jianbianse" target="_blank">https://lingdaima.com/jianbianse</a></div>
    <div>2. ${this.i18n('进入上述网页，根据需要设置渐变色，设置完成后，点击最底部按钮')}“Copy to Clipboard"，${this.i18n('复制出来的的内容即是背景色css代码')}</div>
    <div>3. ${this.i18n('将上述复制的css代码粘贴到CRM自定义颜色字段即可')}</div>`
    this.$alert(contentHtml, this.i18n('自定义颜色'), {
      confirmButtonText: this.i18n('确定'),
      dangerouslyUseHTMLString: true
    });
  }
}
