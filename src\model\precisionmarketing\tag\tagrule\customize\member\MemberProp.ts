import BirthdayPropValue from 'model/precisionmarketing/tag/tagrule/customize/member/props/BirthdayPropValue'
import StorePropValue from 'model/precisionmarketing/tag/tagrule/customize/member/props/StorePropValue'
import StringPropValue from 'model/precisionmarketing/tag/tagrule/customize/member/props/StringPropValue'
import TimePropValue from 'model/precisionmarketing/tag/tagrule/customize/member/props/TimePropValue'

export default class MemberProp {
  // 会员属性，grade-等级；state-状态；store-归属门店；（storeId-单店；营销中心-marketingCenter；circle-商圈；area-区域；）；registerChannelType-注册渠道；registerScene-招募方式；gender-性别；birthday-生日；registerTime-注册时间；firstConsumeTime-首次消费时间
  prop: Nullable<string> = null
  // 操作符，in-属于; !in-不属于; =-等于；!=-不等于；=null-为空；!=null-不为空；fixed-固定时间；relative-相对时间；
  operator: Nullable<string> = null
  // 字符串值类型
  strProp: Nullable<StringPropValue> = null
  // 门店类型
  storeProp: Nullable<StorePropValue> = null
  // 生日类型
  birthdayProp: Nullable<BirthdayPropValue> = null
  // 时间类型
  timeProp: Nullable<TimePropValue> = null
}