import BalanceSetting from 'model/weixin/cardV2/BalanceSetting'
import FavorSetting from 'model/weixin/cardV2/FavorSetting'
import GradeCardStyle from 'model/weixin/card/GradeCardStyle'
import PointSetting from 'model/weixin/cardV2/PointSetting'
// import { CodeType } from 'model/default/CodeType'

export default class WeixinCardV2 {
  // 品牌名称
  brandName?: Nullable<string> = null
  // 商户logo
  logo: Nullable<string> = null
  // 会员卡背景
  image: Nullable<string> = null
  // 等级卡样
  gradeCardStyles: GradeCardStyle[] = []
  // 会员卡标题
  title: Nullable<string> = null
  // 开卡必填信息
  requireInfo: string[] = []
  // 开卡选填信息
  optionalInfo: string[] = []
  // 会员卡类型
  codeType: Nullable<string> = null
  // 会员权益
  memberRights: string[] = []
  // 使用须知
  notice: Nullable<string> = null
  // 服务电话
  serviceMobile: Nullable<string> = null
  // 积分配置
  pointSetting: Nullable<PointSetting> = null
  // 券配置
  favorSetting: Nullable<FavorSetting> = null
  // 储值配置
  bBalanceSetting: Nullable<BalanceSetting> = null
}