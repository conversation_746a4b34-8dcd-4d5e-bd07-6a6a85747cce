import {Component, Prop, Vue} from 'vue-property-decorator'
import MemberApi from 'http/member_v2/MemberApi'

@Component({
  name: 'CheckPrePayCardDialog',
  components: {}
})
export default class CheckPrePayCardDialog extends Vue {
  prePayCards: any = []
  @Prop()
  data: any
  @Prop()
  title: any

  @Prop({
    type: Boolean,
    default: false
  })
  dialogShow: boolean
  created() {
    MemberApi.getCards(this.$route.query.id as string).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.prePayCards = resp.data
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  doBeforeClose(done: any) {
    this.$emit('dialogClose')
    done()
  }
  doModalClose() {
    this.$emit('dialogClose')
  }
  doCancel() {
    this.$emit('dialogClose')
  }
  doCheckGoods() {
    // todo
  }
}