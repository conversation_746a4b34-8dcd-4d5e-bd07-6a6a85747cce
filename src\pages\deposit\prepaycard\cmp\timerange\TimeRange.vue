<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:03
 * @LastEditTime: 2023-08-23 14:01:18
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\prepaycard\cmp\timerange\TimeRange.vue
 * 记得注释
-->
<template>
  <div class="gift-card-report-time-range">
    <el-radio-group v-model="currentTab" @change="radioChanged">
      <el-radio-button :label="formatI18n('/公用/日期/今天')">{{formatI18n('/公用/日期/今天')}}</el-radio-button>
      <el-radio-button :label="formatI18n('/公用/日期/昨天')">{{formatI18n('/公用/日期/昨天')}}</el-radio-button>
      <el-radio-button :label="formatI18n('/公用/日期/近7天')">{{formatI18n('/公用/日期/近7天')}}</el-radio-button>
      <el-radio-button v-if="!da1qiaoPermission" :label="formatI18n('/公用/日期/近30天')">{{formatI18n('/公用/日期/近30天')}}</el-radio-button>
      <el-radio-button v-if="!da1qiaoPermission" :label="formatI18n('/公用/日期/近90天')">{{formatI18n('/公用/日期/近90天')}}</el-radio-button>
      <el-radio-button :label="formatI18n('/公用/日期/自定义')">{{formatI18n('/公用/日期/自定义')}}</el-radio-button>
    </el-radio-group>
    &nbsp;&nbsp;
    <el-date-picker
        v-model="datetimerange"
        @change="pickerChanged"
        type="daterange"
        :clearable="false"
        range-separator="-"
        :start-placeholder="formatI18n('/公用/查询条件/提示/开始日期')"
        :end-placeholder="formatI18n('/公用/查询条件/提示/结束日期')">
    </el-date-picker>
  </div>
</template>

<script lang="ts" src="./TimeRange.ts">
</script>

<style lang="scss">
.gift-card-report-time-range {
  height: 40px;
  vertical-align: middle;
  display: inline-block;

  .el-radio-button__inner {
    font-size: 14px;
  }

  .el-date-editor .el-range__icon {
    line-height: 24px !important;
  }

  .el-date-editor .el-range-separator {
    line-height: 24px !important;
  }
}
</style>
