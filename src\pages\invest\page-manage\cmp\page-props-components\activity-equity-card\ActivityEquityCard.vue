<template>
  <div class="activity_equity_card" v-loading="loadingEquityCardTemplateList">
    <el-form :label-position="labelPosition" label-width="100px" :model="value" :rules="rules" ref="form">
      <el-form-item :label="i18n('/会员/权益卡/选择权益卡')" prop="propEquityCard" class="activity-input">
        <!-- <el-input maxlength="40" show-word-limit v-model.trim="value.propEquityCard" @change="handleChange" style="width: 100%"></el-input> -->
        <el-select v-model.sync="value.propEquityCard" @change="handleChange" size="mini" style="width: 100%;">
          <el-option v-for="equityCard in equityCardTemplateList" :key="equityCard.uuid" :label="equityCard.name"
            :value="equityCard.code"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="i18n('/会员/会员资料/开卡文案')" prop="propOpenCardText" class="activity-input">
        <el-input maxlength="15" show-word-limit v-model.trim="value.propOpenCardText" @change="handleChange" :placeholder="formatI18n('/页面/页面管理/立即开卡，尊享超值权益')"
          style="width: 100%"></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" src="./ActivityEquityCard.ts"></script>

<style lang="scss" scoped>
.activity_equity_card {
  padding: 12px;
  background-color: #F0F2F6;

  .activity-input {
    //margin-bottom: 10px;
    // ::v-deep .el-form-item__content {
    //   // width: 100% !important;
    // }
  }
}
</style>
