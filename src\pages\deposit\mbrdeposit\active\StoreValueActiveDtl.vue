<template>
  <div class="store-value-active-dtl">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button key="1" @click="doAudit()"
          v-if="billDtl.body.state === 'INITAIL' && !isOaActivity && hasOptionPermission('/储值/储值活动/充值有礼/储值充值活动', '活动审核')">审核</el-button>
        <el-button key="2" @click="doCopy()" v-if="hasOptionPermission('/储值/储值活动/充值有礼/储值充值活动', '活动维护')">复制</el-button>
        <el-button key="3" @click="doModify()"
          v-if="['REJECTED','INITAIL','UNSTART', 'PROCESSING'].indexOf(billDtl.body.state) > -1 && hasOptionPermission('/储值/储值活动/充值有礼/储值充值活动', '活动维护')">修改</el-button>
        <el-button key="4" @click="doDelete()"
          v-if="['REJECTED','INITAIL'].indexOf(billDtl.body.state) > -1 && hasOptionPermission('/储值/储值活动/充值有礼/储值充值活动', '活动维护')">删除</el-button>
        <el-button key="5" @click="doValidate()" v-if="billDtl.body.state === 'PROCESSING' ||  billDtl.body.state === 'STOPED'">效果评估</el-button>
        <el-button key="6" @click="doStop()"
          v-if="(billDtl.body.state === 'PROCESSING' || billDtl.body.state === 'UNSTART') && hasOptionPermission('/储值/储值活动/充值有礼/储值充值活动', '活动终止')">终止</el-button>
      </template>
    </BreadCrume>
    <div style="overflow: auto;height: 95%">
      <div class="top-wrap">
        <div class="left">
          <div class="back">
            <img src="~assets/image/storevalue/back.png">
          </div>
        </div>
        <div class="right">
          <div class="top" style="padding-bottom: 10px;border-bottom: 1px solid rgba(242, 242, 242, 1);">
            <div class="item1">
              <div class="bill title"><span>单号：</span>{{ billDtl.body ? billDtl.body.activityId : '' }}</div>
              <div class="name" style="width: 235px;word-break: break-all;">{{ billDtl.body.name|nullable }}
              </div>
            </div>
            <div class="item2">
              <div class="desc">状态</div>
              <div class="state">
                <ActivityStateTag :stateEquals="billDtl.body.state"></ActivityStateTag>
              </div>
            </div>
          </div>
          <div class="bottom" style="margin-top: 15px">
            <el-row class="top" style="height: auto;padding-bottom: 10px;display: flex;line-height: 23px">
              <div class="title">活动时间：</div>
              <el-col :span="18" style="display: flex;">
                <div>
                  {{ billDtl.body.beginDate|dateFormate2 }}
                  {{ formatI18n('/营销/积分活动/积分活动/商品满额加送积分/新建页面/活动信息/至') }}
                  {{ billDtl.body.endDate|dateFormate2 }}
                </div>
                <DateTimeConditionDtl style="margin-left: 30px" :value="billDtl.dateTimeCondition"></DateTimeConditionDtl>
              </el-col>
              <!-- <div style="max-width: calc(100% - 180px)" no-i18n>
                {{ billDtl.body.beginDate | dateFormate2 }} <span>至</span> {{ billDtl.body.endDate | dateFormate2 }}
              </div> -->
            </el-row>
            <el-row class="top" style="height: auto;padding-bottom: 10px;display: flex;line-height: 23px">
              <div class="title">活动渠道：</div>
              <div style="max-width: calc(100% - 180px)" no-i18n>{{ getSource }}</div>
            </el-row>
            <el-row class="top" style="height: auto;padding-bottom: 10px;display: flex;line-height: 23px"
              v-if="billDtl.body && billDtl.body.channels[0] && billDtl.body.channels[0].type === 'store' && billDtl.body.channels[0].id === '-'">
              <div class="title">门店范围：</div>
              <div style="max-width: calc(100% - 180px)">
                <ActiveStoreDtl style="width: 60%;min-width: 800px;margin-top: -8px;" v-if="billDtl && billDtl.body && billDtl.body.stores"
                  :data="billDtl.body.stores"></ActiveStoreDtl>
              </div>
            </el-row>
            <el-row class="top" style="height: auto;padding-bottom: 10px;display: flex;line-height: 23px">
              <div class="title">{{formatI18n('/营销/积分活动/积分活动/商品满额加送积分/新建页面/活动信息/参与会员等级')}}：</div>
              <div style="max-width: calc(100% - 180px)">
                <SelectGradeDetail :value="billDtl.body.gradeRange" />
              </div>
            </el-row>
            <el-row class="top" style="height: auto;padding-bottom: 10px;display: flex;line-height: 23px">
              <div class="title">{{i18n('/营销/券礼包活动/券礼包活动/活动图片')}}：</div>
              <div style="max-width: calc(100% - 180px)">
                <CardPicList :picList="billDtl.body.pictureUrls" :readonly="true" />
              </div>
            </el-row>
            <el-row class="top" style="height: auto;padding-bottom: 10px;display: flex;line-height: 23px">
              <div class="title">活动说明：</div>
              <div style="max-width: calc(100% - 180px)" no-i18n v-html="billDtl.body.remark ? billDtl.body.remark.replace(/\n/g, '<br/>'): '--'">
              </div>
            </el-row>
          </div>
          <span style="position: absolute;right: 20px;top: 37px;">
          </span>
        </div>
      </div>
      <div class="row-height"></div>
      <div class="center-wrap">
        <div>
          <p class="sub-item">充值赠礼</p>
          <div style="background-color: rgba(249, 249, 249, 1);width: 640px;padding: 20px;margin-bottom: 20px" v-for="(item,index) in billDtl.lines"
            :key="index">
            <div style="border-bottom: 1px solid #EEEEEE;padding-bottom: 10px">
              <i18n k="/储值/会员储值/储值充值活动/详情页面/充值面额{0}元，售价{1}元">
                <template slot="0"> {{ item.faceAmount }}</template>
                <template slot="1"> {{ item.price }}</template>
              </i18n>
            </div>
            <div style="padding-top: 10px">
              <FormItem label="赠礼设置">
                <div v-if="(!item.gift.couponItems || item.gift.couponItems.length === 0) && !item.gift.points && !item.gift.rebateAmount &&(!item.gift.paidBenefitCards || item.gift.paidBenefitCards.length===0)"
                  style="height: 36px;line-height: 36px;">暂无
                </div>
                <div style="height: 36px;line-height: 36px;" v-if="item.gift.points">
                  <i18n k="/储值/会员储值/储值充值活动/详情页面/赠送积分{0}个">
                    <template slot="0"> {{ item.gift.points }}</template>
                  </i18n>
                </div>
                <div style="height: 36px;line-height: 36px;" v-if="item.gift.rebateAmount">
                  <i18n k="/储值/会员储值/储值充值活动/详情页面/赠送返现{0}元">
                    <template slot="0"> {{ item.gift.rebateAmount | fmt }}</template>
                  </i18n>
                </div>
                <div v-if="item.gift.couponItems && item.gift.couponItems.length > 0" style="line-height: 36px;">
                  <span style="display: block" v-for="(sub,index) in item.gift.couponItems" :key="index">
                    <span style="position: relative;top: -13px;">{{ sub.qty }}<span>张</span></span>&nbsp;&nbsp;<a :title="sub.coupons.name"
                      style="width: 300px;cursor: pointer;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;display: inline-block;position: relative;top: 0px;"
                      @click="doCheckDtl(item, sub)">{{ sub.coupons.name }}</a>
                  </span>
                </div>
                <div v-if="item.gift.paidBenefitCards && item.gift.paidBenefitCards.length > 0" style="line-height: 36px;">
                  <span style="display: block" v-for="(sub,index) in item.gift.paidBenefitCards" :key="index">
                    <span>{{ sub.benefitCardTemplate.name }}</span>
              
                    <template v-if="sub.payRule">
                      <span style="margin-left: 10px">{{ sub.payRule.value }}</span>
                      <span>{{ sub.payRule.type | dateTypeName }}</span>
                    </template>
                  </span>
                </div>
              </FormItem>
              <FormItem label="充值说明">
                <span style="height: 36px;line-height: 36px;">{{item.depositRemark || '暂无'}}</span>
              </FormItem>
              <FormItem label="充值图片">
                <CardPicList v-if="item.pictureUrl" :picList="[item.pictureUrl]" :readonly="true" />
                <span style="height: 36px;line-height: 36px;" v-else>暂无</span>
              </FormItem>
              <FormItem :label="formatI18n('/储值/会员储值/储值充值活动/编辑页面/C端角标')">
                <CardPicList v-if="item.cornerImageUrl" :picList="[item.cornerImageUrl]" :readonly="true" />
                <span style="height: 36px;line-height: 36px;" v-else>
                  {{formatI18n('/储值/会员储值/储值充值活动/详情页面/暂无')}}
                </span>
              </FormItem>
            </div>
          </div>
        </div>
      </div>
      <div class="row-height"></div>
      <MarketingBudgetDtl :budget="billDtl.body.budget" activityType="PrepayDepositActivityRule"></MarketingBudgetDtl>
    </div>

    <SelectStoreActiveDtlDialog :data="billDtl" :parent="parent" :child="child" :dialogShow="dialogShow" @dialogClose="doDialogClose">
    </SelectStoreActiveDtlDialog>
    <CheckStoreDialog :data="billDtl.body.stores.stores" :dialogShow="checkdialogShow" @dialogClose="doCheckDialogClose">
    </CheckStoreDialog>
    <ExportConfirm :dialogShow="exportDialogShow" :warnMsg="warnMsg" :confirmBtnMsg="formatI18n('/公用/按钮', '审核')"
      :checkMsg="formatI18n('/储值/会员储值/储值充值活动/新建界面/存在冲突活动/点击保存并审核/弹框提示', '已知晓冲突活动，并确认继续执行审核')" @dialogClose="doExportDialogClose"
      @summit="doConfirmSummit"></ExportConfirm>
  </div>
</template>

<script lang="ts" src="./StoreValueActiveDtl.ts">
</script>

<style lang="scss">
.store-value-active-dtl {
  width: 100%;
  height: 100%;
  background-color: white;
  overflow: hidden;

  .top-wrap {
    display: flex;
    flex-direction: row;
    .left {
      width: 80px;
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
      .back {
        width: 48px;
        height: 48px;
        border-radius: 100%;
        background-color: rgba(242, 242, 242, 1);
        img {
          width: 24px;
          height: 24px;
          position: relative;
          top: 13px;
          left: 12px;
        }
      }
    }
    .right {
      display: flex;
      flex: 1;
      flex-direction: column;
      position: relative;
      .top {
        display: flex;
        .title {
          color: rgba(51, 51, 51, 0.***************);
        }
        .item1 {
          .bill {
            margin-top: 16px;
          }
          .name {
            font-weight: 500;
            margin-top: 8px;
            font-size: 20px;
          }
        }
        .item2 {
          padding-left: 70px;
          padding-top: 16px;
          .desc {
            color: rgba(51, 51, 51, 0.***************);
          }
          .state {
            font-weight: 500;
            margin-top: 8px;
            font-size: 20px;
          }
        }
      }
      .bottom {
        padding-bottom: 20px;
        .account-info {
          margin-top: 10px;
        }
        .red {
          color: red;
        }
        .green {
          color: #008000;
        }
      }
    }
  }
  .row-height {
    height: 20px;
    background-color: rgba(242, 242, 242, 1);
  }
  .center-wrap,
  .foot-wrap {
    padding: 20px;
    height: auto;
  }
  .sub-item {
    font-size: 16px;
    padding-top: 20px;
    margin-bottom: 10px;
  }
  .active-store-dtl {
    width: auto;
    max-width: 700px;
  }
}
</style>