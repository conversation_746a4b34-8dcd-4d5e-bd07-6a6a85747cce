import { Vue, Component, Prop } from 'vue-property-decorator';
import emitter from 'util/emitter';
import FormDefUtil from 'util/FormDefUtil';
import I18nPage from 'common/I18nDecorator';

@Component({
  name: 'ShopStyle',
  mixins: [emitter],
  components: {},
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/公用/菜单',
    '/页面/页面管理',
    '/公用/过滤器',
  ],
  auto: true
})
export default class ShopStyle extends Vue {
  @Prop({ type: Boolean, default: false }) readonly: boolean;
  @Prop({ type: String, default: 'ShopStyle' }) validateName: string; // 组件校验名称，editPage通过该名称进行组件校验及校验信息清除
  @Prop({ type: Object }) value: any; // 数据模型
  formKey: any;
  $refs: any;

  rules = {
    propShowStyle: [{ required: true, message: this.i18n('请选择'), trigger: ['blur', 'change'] }],
  };

  get formMode() {
    if (this.validateName === 'ShopStyle') {
      return FormDefUtil.getFormMode(this.readonly, this.validateName, this.formKey);
    } else {
      return FormDefUtil.getFormMode(this.readonly, this.validateName + 'ShopStyle', this.formKey);
    }
  }

  created() {
    if (!this.value.propShowStyle) {
      this.value.propShowStyle = '1'
      this.handleChange()
    }
  }

  handleChange() {
    this.$emit('input', this.value);
    this.$emit('change', this.value);
    this.validate(() => { });
  }

  validate(callback: () => void) {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.validate(callback);
    }
  }

  clearValidate() {
    if (this.$refs.form && this.$refs.form) {
      return this.$refs.form.clearValidate();
    }
  }
};