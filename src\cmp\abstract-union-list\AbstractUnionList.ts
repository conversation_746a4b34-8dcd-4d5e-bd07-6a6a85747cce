import AbstractOaActivity from 'cmp/abstract-oa-activity/AbstractOaActivity';
import { Vue } from 'vue-property-decorator';

class UnionActivityItem {
  label: string = ''  //标签值
  value: string = ''  //活动筛选值
  activityType: string = '' //活动类型
  viewAble: boolean = false //查看权限
  modifyAble: boolean = false //修改权限
  auditAble: boolean = false  //审核权限
  stopAble: boolean = false //终止权限
  removeAble: boolean = false //删除权限
  goToDtl: Function = () => { }  //前往详细页
  goToCopy: Function = () => { }  //前往复制
  goToModify: Function = () => { } //前往修改
}

// 标准活动列表页 抽象类
export default abstract class AbstractUnionList<T> extends AbstractOaActivity {
  $refs: any
  activitiesInfo: UnionActivityItem[] = []
  selectedArr: T[] = [] //已选列表
  batchAuditShow: boolean = true  //是否展示批量审核按钮
  batchStopShow: boolean = true  //是否展示批量终止按钮
  batchDeleteShow: boolean = true //是否展示批量删除按钮
  page = {
    currentPage: 1,
    total: 0,
    size: 10,
  };

  //查询列表
  abstract queryList(): void

  // 获取当前列表页活动类型
  abstract getActivityTypes(): string[]

  //提交批量删除
  abstract submitBatchDelete(ids: string[]): void

  //提交批量审核
  abstract submitBatchAudit(ids: string[]): void

  //提交批量终止
  abstract submitBatchEnd(ids: string[]): void

  get getSelectActive() {
    // let str: any = this.formatI18n("/营销/券礼包活动/券礼包活动", "已选择{0}个活动");
    // str = str.replace(/\{0\}/g, this.selectedArr.length);
    // return str;
    return ''
  }

  // 初始化需要的活动信息
  initActivityInfo(items: UnionActivityItem[]) {
    this.activitiesInfo = items
  }

  //所有活动的批量审核权限
  get permissionAllAudit() {
    return this.computeAllPermission('活动审核')
  }
  //当前活动的审核权限
  hasAuditPermission(type: string) {
    return this.computeSinglePermission(type, '活动审核')
  }

  //所有活动的维护权限
  get permissionAllModify() {
    return this.computeAllPermission('活动维护')
  }
  //当前活动的维护权限
  hasModifyPermission(type: string) {
    return this.computeSinglePermission(type, '活动维护')
  }

  //所有活动的终止权限
  get permissionAllStop() {
    return this.computeAllPermission('活动终止')
  }
  //当前活动的终止权限
  hasStopPermission(type: string) {
    return this.computeSinglePermission(type, '活动终止')
  }

  //当前活动的查看权限
  hasCheckPermission(type: string) {
    return this.computeSinglePermission(type, '活动查看')
  }

  //判断单个活动的对应权限
  computeSinglePermission(type: string, action: '活动审核' | '活动维护' | '活动查看' | '活动终止' | '活动删除') {
    const targetActivity = this.activitiesInfo.find((item) => item.activityType === type)
    if (!targetActivity) return false
    if (action === '活动审核') {
      return targetActivity.auditAble
    }
    else if (action === '活动查看') {
      return targetActivity.viewAble
    }
    else if (action === '活动终止') {
      return targetActivity.stopAble
    }
    else if (action === '活动维护') {
      return targetActivity.modifyAble
    }
    else if (action === '活动删除') {
      return targetActivity.removeAble
    }
  }

  //所有活动需要满足的权限
  computeAllPermission(action: '活动审核' | '活动维护' | '活动查看' | '活动终止' | '活动删除') {
    if (action === '活动审核') {
      return this.activitiesInfo.every((item) => item.auditAble)
    }
    else if (action === '活动查看') {
      return this.activitiesInfo.every((item) => item.viewAble)
    }
    else if (action === '活动终止') {
      return this.activitiesInfo.every((item) => item.stopAble)
    }
    else if (action === '活动维护') {
      return this.activitiesInfo.every((item) => item.modifyAble)
    }
    else if (action === '活动删除') {
      return this.activitiesInfo.every((item) => item.removeAble)
    }
  }

  //前往详细页
  doDtl(row: any) {
    this.activitiesInfo.find((item) => {
      if (item.activityType === row.type) {
        item.goToDtl(row)
        return true
      }
    })
  }

  // 前往复制
  doCopy(row: any) {
    this.activitiesInfo.find((item) => {
      if (item.activityType === row.type) {
        item.goToCopy(row)
        return true
      }
    })
  }

  // 前往编辑
  doModify(row: any) {
    this.activitiesInfo.find((item) => {
      if (item.activityType === row.type) {
        item.goToModify(row)
        return true
      }
    })
  }

  // 批量删除
  doBatchDelete() {
    if (this.selectedArr.length <= 0) {
      this.$message.warning(this.formatI18n("/营销/券礼包活动/券礼包活动", "请先勾选要删除的单据") as string);
      return;
    }
    this.$confirm(this.formatI18n("/营销/券礼包活动/券礼包活动", "是否批量删除这些单据?") as string, "批量删除", {
      confirmButtonText: this.formatI18n("/公用/按钮", "确定") as string,
      cancelButtonText: this.formatI18n("/公用/按钮", "取消") as string,
    }).then(() => {
      let ids: string[] = [];
      if (this.selectedArr && this.selectedArr.length > 0) {
        this.selectedArr.forEach((item: any) => {
          ids.push(item.activityId);
        });
      }
      this.submitBatchDelete(ids);
    });
  }

  // 批量审核
  doBatchAudit() {
    if (this.selectedArr.length <= 0) {
      this.$message.warning(this.formatI18n("/营销/券礼包活动/券礼包活动", "请先勾选要审核的单据") as string);
      return;
    }
    this.$confirm(
      this.formatI18n("/营销/券礼包活动/券礼包活动", "是否批量审核这些单据?") as string,
      this.formatI18n("/营销/券礼包活动/券礼包活动", "批量审核") as string,
      {
        confirmButtonText: this.formatI18n("/公用/按钮", "确定") as string,
        cancelButtonText: this.formatI18n("/公用/按钮", "取消") as string,
      }
    ).then(() => {
      let ids: string[] = [];
      if (this.selectedArr && this.selectedArr.length > 0) {
        this.selectedArr.forEach((item: any) => {
          ids.push(item.activityId);
        });
      }
      this.submitBatchAudit(ids);
    });
  }

  // 批量终止
  doBatchEnd() {
    if (this.selectedArr.length <= 0) {
      this.$message.warning(this.formatI18n("/营销/券礼包活动/券礼包活动", "请先勾选要终止的单据") as string);
      return;
    }
    this.$confirm(
      this.formatI18n("/营销/券礼包活动/券礼包活动", "是否批量终止这些单据?") as string,
      this.formatI18n("/营销/券礼包活动/券礼包活动", "批量终止") as string,
      {
        confirmButtonText: this.formatI18n("/公用/按钮", "确定") as string,
        cancelButtonText: this.formatI18n("/公用/按钮", "取消") as string,
      }
    ).then(() => {
      let ids: string[] = [];
      if (this.selectedArr && this.selectedArr.length > 0) {
        this.selectedArr.forEach((item: any) => {
          ids.push(item.activityId);
        });
      }
      this.submitBatchEnd(ids);
    });
  }

  onHandleCurrentChange(val: number) {
    this.page.currentPage = val;
    this.queryList();
  }

  onHandleSizeChange(val: number) {
    this.page.size = val;
    this.queryList();
  }

  handleSelectionChange(val: any) {
    this.selectedArr = val;
    this.batchAuditShow = this.checkAuditPermissionByCheck()
    this.batchStopShow = this.checkStopPermissionByCheck()
    this.batchDeleteShow = this.checkDeletePermissionByCheck()
  }

  // 判断已勾选数据的审核权限
  checkAuditPermissionByCheck() {
    return this.selectedArr.every((item: any) => this.computeSinglePermission(item.type, '活动审核'))
  }

  // 判断已勾选数据的终止权限
  checkStopPermissionByCheck() {
    return this.selectedArr.every((item: any) => this.computeSinglePermission(item.type, '活动终止'))
  }

  // 判断已勾选数据的删除权限
  checkDeletePermissionByCheck() {
    return this.selectedArr.every((item: any) => this.computeSinglePermission(item.type, '活动维护'))
  }
}