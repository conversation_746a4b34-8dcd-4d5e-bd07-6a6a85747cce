# 文件作用说明
- PageManageList：投放页面列表页面
- PageManageEdit：新增编辑页面
- cmp/page-place-components: 内容渲染组件（单栏，双栏图片等，一个组件一个存放文件夹）
- cmp/page-place-components/index：循环遍历组件，并自动引入
- cmp/page-props-components: 右侧属性组件（图片设置，输入框等，有些组件未公共属性组件，可以多组件复用）
- cmp/page-props-components/DefaultPagePlaceProperty：组件的初始化属性字段在此处维护
- cmp/place-panel：左侧组件列表
- cmp/render-page-place：中间区域渲染组件
- cmp/render-page-props：右侧区域渲染组件
- cmp/setting-toolbar：中间区域渲染组件中的操作按钮
- cmp/template.map.ts： 前端组件渲染组件map，所有需要动态渲染的组件基础数据模型在此处维护