/*
 * @Author: 黎钰龙
 * @Date: 2023-08-31 16:01:53
 * @LastEditTime: 2023-08-31 16:19:27
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\prepay\report\card\PrepayCardExchangeGoods.ts
 * 记得注释
 */
import MutableNsid from "model/common/MutableNsid"

export default class PrepayCardExchangeGoods {
  // 商品行号
  itemNo: Nullable<string> = null
  // 商品ID
  id: Nullable<string> = null
  // 商品代码
  code: Nullable<string> = null
  // 商品名称
  name: Nullable<string> = null
  // 商品条码
  barcode: Nullable<string> = null
  // 品牌代码
  brandCode: Nullable<string> = null
  // 类别代码
  categoryCode: Nullable<string> = null
  // 包装规格
  qpcStr: Nullable<string> = null
  // 价格
  price: Nullable<number> = null
  // 数量
  qty: Nullable<number> = null
  // 支付金额
  payAmount: Nullable<number> = null
  // 优惠金额
  favAmount: Nullable<number> = null
  // 抵扣金额
  deductAmount: Nullable<number> = null
  //
  stdAmount: Nullable<number> = null
  // 交易id
  transId: Nullable<MutableNsid> = null
  // 交易号
  transNo: Nullable<string> = null
  // 卡号
  cardCode: Nullable<string> = null
  // 兑换类型
  exchangeType: Nullable<string> = null
  // 发生营销中心
  marketingCenter: Nullable<string> = null
  // 发生组织标识ID
  occurredOrgId: Nullable<string> = null
  // 发生组织标识名称
  occurredOrgName: Nullable<string> = null
  // 发生渠道渠道ID
  occurredChannelId: Nullable<string> = null
  // 发生渠道渠道类型
  occurredChannelType: Nullable<string> = null
  // 发生时间
  occurredTime: Nullable<Date> = null
}