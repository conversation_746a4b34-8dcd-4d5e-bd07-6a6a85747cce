/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2023-11-27 17:14:24
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\v2\coupon\goodsgift\GoodsAmountGiftActivity.ts
 * 记得注释
 */
import BaseCouponActivity from "model/v2/coupon/BaseCouponActivity";
import GoodsRange from "model/common/GoodsRange";
import StairGift from "model/common/StairGift";
import PushGroup from "model/precisionmarketing/pushplan/PushGroup";
import DateTimeCondition from "model/common/DateTimeCondition";

export default class GoodsAmountGiftActivity extends BaseCouponActivity {
	// 礼包信息
	gifts: StairGift[] = [];
	// 发券商品
	issueGoods: Nullable<GoodsRange> = null;
	// 客群
	rule: Nullable<PushGroup> = null
	// 叠加促销
	joinPromotion: Nullable<Boolean> = false
	// 满件满额
	useThresholdType: Nullable<'QTY' | 'AMOUNT'> = null
  // 活动时间限制
  dateTimeCondition: Nullable<DateTimeCondition> = new DateTimeCondition();
  // 每人每天/周/月限量
  maxPerDateRangeIssueTimes: Nullable<number> = null
	// 排除优惠商品
	excludeFavourGoodTypes: string[] = []
	// 排除优惠金额
	excludeFavourAmountTypes: string[] = ["PROMOTION","COUPON","MEMBER","OTHER"]

}
