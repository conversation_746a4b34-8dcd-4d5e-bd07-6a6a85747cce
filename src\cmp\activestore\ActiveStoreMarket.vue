<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:02
 * @LastEditTime: 2024-03-20 14:01:31
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\activestore\ActiveStoreMarket.vue
 * 记得注释
-->
<template>
  <div>
    <el-row>
      <span class="plain-btn-blue" @click="openMarketCenter" style="margin-top:4px">
        {{i18n('添加营销中心')}}
      </span>
    </el-row>
    <div class="market-active-store-view">
      <el-form :model="store" ref="form">
        <el-row v-for="(item, index) in store.marketingCenters" :key="index">
          <div class="marketing-item">
            <div class="title">
              <div class="ellipse">{{item.marketingCenter.name}}</div>
            </div>
            <div class="item-radio">
              <el-radio-group @change="doStoreRange(index)" v-model="item.stores.storeRangeTypeFront" class="radio-group-container">
                <el-radio label="ALL">{{ formatI18n("/公用/券模板", "全部门店") }}</el-radio>
                <el-radio label="PART">
                  {{ i18n("指定门店适用") + '：'}}
                  <template v-if="item.stores.storeRangeTypeFront === 'PART'">
                    <span @click="doSelect(index)" v-if="!item.stores.stores.length" style="color:#20A0FF">
                      {{i18n('选择门店')}}
                    </span>
                    <span v-else>
                      <i18n k="/公用/券模板/已选择{0}家门店">
                        <template slot="0">
                          <span class="number-text">{{item.stores.stores.length}}</span>
                        </template>
                      </i18n>
                      <span style="color:#20A0FF;margin-left:2px" @click="doSelect(index)">{{i18n('修改')}}</span>
                    </span>
                  </template>
                </el-radio>
                <el-radio label="EXCLUDE">
                  {{i18n("指定门店不适用") + '：'}}
                  <template v-if="item.stores.storeRangeTypeFront === 'EXCLUDE'">
                    <span @click="doSelect(index)" v-if="!item.stores.stores.length" style="color:#20A0FF">
                      {{i18n('选择门店')}}
                    </span>
                    <span v-else>
                      <i18n k="/公用/券模板/已选择{0}家门店">
                        <template slot="0">
                          <span class="number-text">{{item.stores.stores.length}}</span>
                        </template>
                      </i18n>
                      <span style="color:#20A0FF;margin-left:2px" @click="doSelect(index)">{{i18n('修改')}}</span>
                    </span>
                  </template>
                </el-radio>
                <el-radio label="ZONE">
                  {{ formatI18n("/公用/门店组件/指定区域适用") + '：' }}
                </el-radio>
              </el-radio-group>
              <el-row>
                <span class="plain-btn-blue" @click="openZone(index)" v-if="item.stores.storeRangeTypeFront === 'ZONE'">
                  {{i18n('添加区域')}}
                </span>
              </el-row>
              <StoreSelectorDialog ref="selectGoodsScopeDialog" @summit="doSubmitGoods($event, index)" :marketingCenterId="item.marketingCenter.id">
              </StoreSelectorDialog>
            </div>
            <el-col :span="1">
              <el-button type="text" style="color: red" @click="deleteZone(index)">{{formatI18n('/公用/预约文件列表/删除')}}</el-button>
            </el-col>
          </div>
          <div v-show="item.stores.storeRangeTypeFront === 'ZONE'">
            <ActiveStoreAreaForMarket v-model="item.stores" @change="getAreaData($event, index)" ref="activeStoreAreaForMarket"
              :marketCenter="item.marketingCenter.id"> </ActiveStoreAreaForMarket>
          </div>
        </el-row>
      </el-form>
    </div>

    <MarketCenterSelectorDialog ref="marketCenterSelectorDialog" @summit="doSubmitMarketCenters"> </MarketCenterSelectorDialog>
    <PromotionCenterSelectorDialog ref="selectPromotionCenterSelectorDialog" @summit="doPromotionSubmitGoods"></PromotionCenterSelectorDialog>

    <ImportResultDialog :data="importResult" :dialogShow="importResultDialogShow" @importResultDialogClose="importResultDialogShow = false">
    </ImportResultDialog>
  </div>
</template>

<script lang="ts" src="./ActiveStoreMarket.ts"></script>

<style lang="scss" scoped>
.market-active-store-view {
  min-width: 800px;
  .marketing-item {
    background: #f7f9fc;
    border: 1px solid #d7dfeb;
    padding: 12px;
    display: flex;
    align-items: center;
    margin-top: 10px;

    .title {
      margin-right: 20px;
    }

    .item-radio {
      flex: 1;
    }

    .radio-group-container {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      .el-radio {
        width: 45%;
        margin: 5px 0;
      }
    }
  }

  .promotion-center-store {
    max-width: 800px;
  }

  .rows {
    padding: 10px;
    border: 1px solid #ced0da;
    border-top: none;
  }
  .ellipse {
    max-width: 100%;
    font-size: 14px;
    line-height: 28px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #242633;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; /* 超出几行省略 */
    overflow: hidden;
  }
}
</style>
