<template>
  <div class="store-value-query">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button @click="doBatchExport" v-if="hasOptionPermission('/储值/储值管理/会员储值查询','批量导出')">
          {{ formatI18n('/会员/会员资料', '批量导出') }}
        </el-button>
      </template>
    </BreadCrume>
    <ListWrapper class="current-page" style="height: 95%">
      <template slot="query">
        <el-row>
          <el-col :span="6">
            <form-item label="会员">
              <el-input placeholder="请输入会员号/手机号" v-model="query.memberCodeLikes"
                        style="width:100%"/>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item label="储值余额">
              <div style="display: flex">
                <div>
                  <el-input placeholder="最小值" v-model="query.amountEgt" style="width:100%"/>
                </div>
                <div style="line-height: 26px">&nbsp;-&nbsp;</div>
                <div>
                  <el-input placeholder="最大值" v-model="query.amountElt" style="width:100%"/>
                </div>
              </div>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item label="会员储值账户" v-if="enableMultipleAccount">
              <el-select no-i18n v-model="query.nameEquals" style="width: 80%" @change="changeAccount">
                <el-option :label="i18n('全部账户类型')" :value="null"></el-option>
                <el-option v-for="account of accounts" :label="'[' + account.id + '] ' + account.name"
                        :key="account.id"  :value="account.id"/>
              </el-select>
            </form-item>
          </el-col>
        </el-row>
        <el-row style="margin-top: 10px">
          <el-col :span="8">
            <form-item>
              <el-button @click="doSearch" type="primary">查询</el-button>
              <el-button @click="doReset">重置</el-button>
            </form-item>
          </el-col>
        </el-row>
      </template>
      <template slot="btn">
        <!--<el-button @click="doExport" size="small">导出报表</el-button>-->
      </template>
      <template slot="list">
        <el-table
            :data="queryData"
            style="width: 100%;margin-top: 20px">
          <el-table-column no-i18n fixed label="会员号" prop="mbrNo" width="300">
            <template slot-scope="scope">
              <span no-i18n>{{ scope.row.crmCode }}</span>
            </template>
          </el-table-column>
          <el-table-column no-i18n fixed label="手机号" prop="mbrMobile" width="150">
            <template slot-scope="scope">
              <span no-i18n>{{ scope.row.mobile }}</span>
            </template>
          </el-table-column>
          <el-table-column no-i18n fixed label="会员储值账户" width="200" prop="name" v-if="enableMultipleAccount">
            <template slot-scope="scope">
              <span no-i18n>{{ scope.row.account | idName }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed label="储值余额(元)" prop="total" width="150" align="right">
            <template slot-scope="scope">
              <span no-i18n>{{ scope.row.total|amount }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed label="实充余额(元)" prop="balance" width="150" align="right">
            <template slot-scope="scope">
              <span no-i18n>{{ scope.row.balance|amount }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed label="返现余额(元)" prop="giftBalance" width="150" align="right">
            <template slot-scope="scope">
              <span no-i18n>{{ scope.row.giftBalance|amount }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed label="操作" prop="activatetime">
            <template slot-scope="scope">
              <el-button @click="doGoDtl(scope.row)" type="text">查看流水</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <!--分页栏-->
      <template slot="page">
        <el-pagination
            no-i18n
            :current-page="page.currentPage"
            :page-size="page.size"
            :page-sizes="[10, 20, 30, 40]"
            :total="page.total"
            @current-change="onHandleCurrentChange"
            @size-change="onHandleSizeChange"
            background
            layout="total, prev, pager, next, sizes,  jumper">
        </el-pagination>
      </template>
    </ListWrapper>

    <DownloadCenterDialog
        :dialogvisiable="fileDialogVisible"
        :showTip="showTip"
        @dialogClose="doDownloadDialogClose">
    </DownloadCenterDialog>
    <ExportConfirm :dialogShow="exportDialogShow" @dialogClose="doExportDialogClose" @summit="doSummit" :accountNm="accountNmId"></ExportConfirm>
  </div>
</template>

<script lang="ts" src="./StoreValueQuery.ts">
</script>

<style lang="scss">
.store-value-query {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: hidden;

  .current-page {
    height: 100%;

    .el-select {
      width: 100%;
    }

    .cell {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .el-range-editor.el-input__inner {
    width: 100%;
  }
}
</style>
