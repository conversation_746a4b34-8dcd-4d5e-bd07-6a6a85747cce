import MemberIdent from "model/common/member/MemberIdent";

export default class CouponIssueHst extends MemberIdent {
	// 发券日期
	issueDate: Nullable<Date> = null;
	// 发生组织
	store: Nullable<string> = null;
	// 券码
	code: Nullable<string> = null;
	// 券名称
	name: Nullable<string> = null;
	// 发券交易额
	amount: Nullable<number> = null;
	// 交易号
	tranNo: Nullable<string> = null;
	// 活动代码
	activityNumber: Nullable<string> = null;
	// 活动名称
	activityName: Nullable<string> = null;
	// 券摘要
	summary: Nullable<string> = null;
	// 券模板号
	templateNumber: Nullable<string> = null;
	// 发券渠道名称
	channelName: Nullable<string> = null;
	// 类型 -- 发券、交易冲账券作废
	type: Nullable<string> = null;
}
