import { Component, Vue, Watch } from 'vue-property-decorator'
import ListWrapper from 'cmp/list/ListWrapper.vue'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import PrePayCardApi from 'http/prepay/card/PrePayCardApi';
import PrePayCard from 'model/prepay/card/PrePayCard';
import PrePayCardFilter from 'model/prepay/card/PrePayCardFilter';
import DateUtil from 'util/DateUtil';
import PrepayAccountApi from 'http/prepay/account/PrepayAccountApi';
import IdName from 'model/common/IdName';
import PrepayCardDtl from './PrepayCardDtl';
import DataUtil from '../common/DataUtil';
import PrePayConfigApi from 'http/prepay/config/PrePayConfigApi';
import PrepayState from '../cmp/prepaystate/PrepayState.vue'
import PrePayCardPasswd from "model/prepay/card/PrePayCardPasswd";
import I18nPage from "common/I18nDecorator";
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import PrePayCardAdjust from "model/prepay/card/PrePayCardAdjust";
import PrepayCardTplPermission from "pages/deposit/prepaycard/prepaycardtpl/PrepayCardTplPermission";
import RSOrgFilter from 'model/common/RSOrgFilter';
import OrgApi from 'http/org/OrgApi';
import DownloadCenterDialog from "pages/main/dowmload_center_dialog/DownloadCenterDialog";
import PrepayCardBalanceStatistic from "model/prepay/card/PrepayCardBalanceStatistic";
import PageConfigApi from 'http/pageConfig/PageConfigApi';
import SelectStores from 'cmp/selectStores/SelectStores';
import UploadBuyerDialog from "./cmp/UploadBuyerDialog";

@Component({
  name: "PrepayCardList",
  components: {
    ListWrapper,
    SubHeader,
    PrepayCardDtl,
    PrepayState,
    BreadCrume,
    DownloadCenterDialog,
    SelectStores,
    UploadBuyerDialog
  },
})
@I18nPage({
  prefix: ["/储值/预付卡/预付卡查询/列表页面", "/储值/预付卡/卡模板/编辑页面", "/公用/活动/状态", "/公用/按钮",'/公用/查询条件'],
})
export default class PrepayCardList extends Vue {
  get dateRangeOption() {
    return {
      disabledDate(time: any) {
        return time.getTime() < DateUtil.nowDayTime();
      },
    };
  }
  uploadDialogShow = false;
  fileDialogVisible = false;
  queryMode: string = "simple"; // simple senior
  query: PrePayCardFilter = new PrePayCardFilter();
  dataRange: Date[] = [];
  queryData: PrePayCard[] = [];
  accounts: IdName[] = [];
  panelArray: any = [];
  hasOptionPermission: any;
  prepayCardTplPermission = new PrepayCardTplPermission();
  fileDialogVisible: boolean = false;
  showTip: boolean = false;
  sumBalance: PrepayCardBalanceStatistic = new PrepayCardBalanceStatistic()
  tableLoading: boolean = false
  showCardBuyerOwnerAndInnerCode: boolean = false //是否展示预付卡购卡人、持卡人、内部卡号

  dtlDialog = {
    dialogVisible: false,
    tmpCode: "",
    defaultTab: this.i18n("账户流水"),
  };
  presentDialog = {
    dialogVisible: false,
    memberMobile: "",
    code: "",
  };
  lossDialog = {
    row: new PrePayCard(),
    dialogVisible: false,
    confirmLoading: false,
  };
  unLossDialog = {
    row: new PrePayCard(),
    dialogVisible: false,
    confirmLoading: false,
  };
  freezeDialog = {
    row: new PrePayCard(),
    dialogVisible: false,
    confirmLoading: false,
  };
  unfreezeDialog = {
    row: new PrePayCard(),
    dialogVisible: false,
    confirmLoading: false,
  };
  reissueDialog = {
    row: new PrePayCard(),
    newCode: "",
    dialogVisible: false,
    confirmLoading: false,
  };
  resetPasswdDialog = {
    row: new PrePayCard(),
    passwd: "",
    passwdConfirm: "",
    dialogVisible: false,
    confirmLoading: false,
  };
  changeBuyerMemberDialog = {
    code: "",
    name: "",
    dialogVisible: false,
    confirmLoading: false,
  };
  adjustDialog = {
    code: "",
    originExpire: "",
    expire: new Date(),
    remark: "",
    dialogVisible: false,
    confirmLoading: false,
  };
  dataUtil: DataUtil = new DataUtil();
  $refs: any;
  enableMultipleAccount: boolean = false;
  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10,
  };
  presentRules = {
    memberMobile: [
      {
        validator: (rule: any, value: string, callback: any) => {
          let reg = /^\d{11}$/;
          if (!reg.test(this.presentDialog.memberMobile)) {
            callback(this.i18n("请输入正确的手机号码"));
          }
          callback();
        },
        trigger: ["change", "blur"],
      },
    ],
  };
  reissueRules: any = {
    newCode: [{ required: true, message: this.i18n("请输入新卡号"), trigger: ["change", "blur"] }],
  };
  passwdRules: any = {
    passwd: [
      { required: true, message: this.i18n("请输入新支付密码"), trigger: ["change", "blur"] },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (isNaN(value) || (value + "").length !== 6) {
            callback(new Error(this.i18n("请输入6位数数字")));
          }
          callback();
        },
        trigger: ["change", "blur"],
      },
    ],
    passwdConfirm: [
      { required: true, message: this.i18n("请再次输入新支付密码"), trigger: ["change", "blur"] },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (this.resetPasswdDialog.passwd !== this.resetPasswdDialog.passwdConfirm) {
            callback(new Error(this.i18n("两次输入的密码不相同")));
          }
          callback();
        },
        trigger: ["change", "blur"],
      },
    ],
  };
  adjustRules = {
    expire: [
      {
        validator: (rule: any, value: string, callback: any) => {
          if (!this.adjustDialog.expire) {
            callback(this.i18n("弹框/校验/请输入调整后卡有效期"));
          }
          callback();
        },
        trigger: ["change", "blur"],
      },
    ],
  };

  created() {
    this.panelArray = [
      {
        name: this.i18n("卡查询"),
        url: "",
      },
    ];
    this.getPrePermission();
    this.query.cardTypeEquals = "GiftCard";
    let queryBillNumber = this.$route.query.billNumber as string;
    let queryCardType = this.$route.query.cardType as string;
    if (queryBillNumber) {
      this.query.issueTransIdIdEquals = queryBillNumber;
    }
    if (queryCardType) {
      this.query.cardTypeEquals = queryCardType;
    }
    this.getConfig()
    this.listEnableAccountType();
  }

  doBatchExport() {
    // this.exportDialogShow = true;
    this.doExportSubmit();
  }

  doDownloadDialogClose() {
    this.showTip = false
    this.fileDialogVisible = false
  }

  doBatchImport() {
    this.$refs.uploadCardBuyer.open()
	}

  uploadSuccess() {
    this.fileDialogVisible = true
  }

  getConfig() {
    PageConfigApi.getConfig().then((res)=>{
      if(res.code === 2000) {
        this.showCardBuyerOwnerAndInnerCode = res.data?.showCardBuyerOwnerAndInnerCode || false
      }
    })
  }

  doExportSubmit() {
    if (this.query.page == null || this.query.pageSize == null) {
      this.$message.error(this.formatI18n('/储值/预付卡/批量导出', '请选择列表查询条件！'));
      return;
    }
    PrePayCardApi.exportCard(this.query)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.exportSubmitAfter()
        }
      })
      .catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
  }

  exportSubmitAfter() {
    this.showTip = true
    this.fileDialogVisible = true
  }

  getAmount(amount: number) {
    if (!amount) {
      return 0;
    }
    return amount;
  }

  getSumBalance() {
    PrePayCardApi.sumBalance(this.query).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.sumBalance = resp.data
      } else {
        this.$message.error(resp.msg)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  doSearch() {
    this.getList();
  }

  doReset() {
    this.dataRange = [];
    let currentCard = this.query.cardTypeEquals;
    this.query = new PrePayCardFilter();
    this.query.cardTypeEquals = currentCard;
    this.page.currentPage = 1;
    this.getList();
  }

  /**
   * 查询
   */
  onSearch() {
    this.page.currentPage = 1;
    this.getList();
  }

  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val;
    this.getList();
  }

  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val;
    this.getList();
  }

  /**
   * 表格排序条件
   */
  onSortChange({ column, prop, order }: any) {
    // todo
  }

  openChangeBuyerMemberDialog(row: PrePayCard) {
    this.changeBuyerMemberDialog.dialogVisible = true;
    this.changeBuyerMemberDialog.code = row.code as string;
    if (row.hasBuyerMemberId != null && row.hasBuyerMemberId) {
      this.changeBuyerMemberDialog.name = "";
    } else {
      this.changeBuyerMemberDialog.name = row.buyerMemberId as string;
    }
  }

  @Watch("dataRange")
  private onDataRangeChange() {
    if (!this.dataRange || this.dataRange.length === 0) {
      this.query.expiredAfterOrEquals = null;
      this.query.expiredBefore = null;
      return;
    }
    this.query.expiredAfterOrEquals = DateUtil.format(this.dataRange[0], "yyyy-MM-dd");
    this.query.expiredBefore = DateUtil.format(this.dataRange[1], "yyyy-MM-dd");
  }

  private listEnableAccountType() {
    PrepayAccountApi.listEnableAccountType()
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.accounts = resp.data;
        }
      })
      .catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
  }

  private changeTabs() {
    let cardTypeEquals = this.query.cardTypeEquals;
    this.dataRange = [];
    this.query = new PrePayCardFilter();
    this.query.cardTypeEquals = cardTypeEquals;
    this.page.currentPage = 1;
    // if (this.query.cardTypeEquals !== 'RechargeableCard'){
    // 	this.getList();
    // } else {
    // 	this.queryData = []
    // }
    // this.getList();
    this.queryData = []
    this.sumBalance = new PrepayCardBalanceStatistic();
  }

  private getList() {
    this.tableLoading = true
    this.queryMode = "simple";
    this.query.page = this.page.currentPage - 1;
    this.query.pageSize = this.page.size;
    if(!this.checkCardTypePermission(this.query.cardTypeEquals!)) {
      return this.$message.error(this.i18n('没有查看该卡模板的权限'))
    }
    PrePayCardApi.query(this.query)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.queryData = resp.data;
          this.page.total = resp.total;
          this.getSumBalance();
        } else {
          this.$message.error(resp.msg || 'Query failed!');
        }
      })
      .catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      }).finally(()=>{
        this.tableLoading = false
      })
  }

  //检查当前
  checkCardTypePermission(cardTypeEquals: string) {
    if (cardTypeEquals === 'GiftCard') {
      return this.hasOptionPermission('/卡/卡管理/卡模板/礼品卡','卡模板查看')
    } else if (cardTypeEquals === 'ImprestCard') {
      return this.hasOptionPermission('/卡/卡管理/卡模板/充值卡', '卡模板查看')
    } else if (cardTypeEquals === 'RechargeableCard') {
      return this.hasOptionPermission('/卡/卡管理/卡模板/储值卡', '卡模板查看')
    } else if (cardTypeEquals === 'CountingCard') {
      return this.hasOptionPermission('/卡/卡管理/卡模板/次卡', '卡模板查看')
    } else {
      return false
    }
  }

  private invalid(code: string) {
    this.$alert(this.i18n("请确认是否作废该预付卡，作废后将无法继续使用"), this.i18n("提示"), {
      confirmButtonText: this.i18n("确定"),
      cancelButtonText: this.i18n("取消"),
      type: "warning",
      callback: (action) => {
        if (action === "confirm") {
          PrePayCardApi.invalid(code)
            .then((resp: any) => {
              if (resp && resp.code === 2000) {
                this.$message.success(this.i18n("作废成功"));
                this.getList();
              }
            })
            .catch((error) => {
              if (error && error.message) {
                this.$message.error(error.message);
              }
            });
        }
      },
    });
  }

  private openLossDialog(row: any) {
    this.lossDialog.dialogVisible = true;
    this.lossDialog.row = row;
  }

  private loss(code: string) {
    this.lossDialog.confirmLoading = true;
    PrePayCardApi.loss(code)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.i18n("挂失成功"));
          this.getList();
          this.lossDialog.dialogVisible = false;
        }
      })
      .catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      })
      .finally(() => {
        this.lossDialog.confirmLoading = false;
      });
  }

  private openUnLossDialog(row: any) {
    this.unLossDialog.dialogVisible = true;
    this.unLossDialog.row = row;
  }

  private unLoss(code: string) {
    this.unLossDialog.confirmLoading = true;
    PrePayCardApi.unLoss(code)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.i18n("解挂成功"));
          this.unLossDialog.dialogVisible = false;
          this.getList();
        }
      })
      .catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      })
      .finally(() => {
        this.unLossDialog.confirmLoading = false;
      });
  }

  private openFreezeDialog(row: any) {
    this.freezeDialog.dialogVisible = true;
    this.freezeDialog.row = row;
  }

  private openAdjustDialog(row: any) {
    if (this.$refs.adjustForm) {
      this.$refs.adjustForm.clearValidate("expire");
    }
    this.adjustDialog.remark = "";
    this.adjustDialog.code = row.code;
    this.adjustDialog.expire = null as any;
    this.adjustDialog.dialogVisible = true;
    this.adjustDialog.originExpire = this.format(row.expireDate);
  }

  private freeze(code: string) {
    this.freezeDialog.confirmLoading = true;
    PrePayCardApi.freeze(code)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.i18n("冻结成功"));
          this.getList();
          this.freezeDialog.dialogVisible = false;
        }
      })
      .catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      })
      .finally(() => {
        this.freezeDialog.confirmLoading = false;
      });
  }

  private openUnfreezeDialog(row: any) {
    this.unfreezeDialog.dialogVisible = true;
    this.unfreezeDialog.row = row;
  }

  private unfreeze(code: string) {
    this.unfreezeDialog.confirmLoading = true;
    PrePayCardApi.unfreeze(code)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.i18n("解冻成功"));
          this.getList();
          this.unfreezeDialog.dialogVisible = false;
        }
      })
      .catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      })
      .finally(() => {
        this.unfreezeDialog.confirmLoading = false;
      });
  }

  private openReissueDialog(row: any) {
    this.reissueDialog.dialogVisible = true;
    this.reissueDialog.newCode = "";
    this.reissueDialog.row = row;
  }

  private reissue() {
    let doReissue = (pass: boolean) => {
      if (!pass) {
        return;
      }
      this.reissueDialog.confirmLoading = true;
      PrePayCardApi.reissue(this.reissueDialog.row.code as string, this.reissueDialog.newCode)
        .then((resp: any) => {
          if (resp && resp.code === 2000) {
            this.$message.success(this.i18n("补发成功"));
            this.getList();
            this.reissueDialog.dialogVisible = false;
          } else {
            this.$message.error(resp.msg || this.i18n(''))
          }
        })
        .catch((error) => {
          if (error && error.message) {
            this.$message.error(error.message);
          }
        })
        .finally(() => {
          this.reissueDialog.confirmLoading = false;
        });
    };
    this.$refs["reissueForm"].validate(doReissue);
  }

  private openResetPasswdDialog(row: any) {
    this.resetPasswdDialog.passwd = "";
    this.resetPasswdDialog.passwdConfirm = "";
    this.resetPasswdDialog.dialogVisible = true;
    this.resetPasswdDialog.row = row;
  }

  private resetPasswd() {
    let doResetPasswd = (pass: boolean) => {
      if (!pass) {
        return;
      }
      let passwd = new PrePayCardPasswd();
      passwd.code = this.resetPasswdDialog.row.code;
      passwd.passwd = this.resetPasswdDialog.passwd;
      this.resetPasswdDialog.confirmLoading = true;
      PrePayCardApi.resetPassWord(passwd)
        .then((resp: any) => {
          if (resp && resp.code === 2000) {
            this.$message.success(this.i18n("重置密码成功"));
            this.getList();
            this.resetPasswdDialog.dialogVisible = false;
          }
        })
        .catch((error) => {
          if (error && error.message) {
            this.$message.error(error.message);
          }
        })
        .finally(() => {
          this.resetPasswdDialog.confirmLoading = false;
        });
    };
    this.$refs["resetPasswdForm"].validate(doResetPasswd);
  }

  private doChangeBuyerMember() {
    this.changeBuyerMemberDialog.confirmLoading = true;
    PrePayCardApi.changeBuyerMember(this.changeBuyerMemberDialog.code, this.changeBuyerMemberDialog.name)
      .then((res: any) => {
        this.$message.success(this.i18n("修改成功"));
        this.changeBuyerMemberDialog.dialogVisible = false;
        this.getList();
      })
      .catch((e) => {
        if (e && e.message) {
          this.$message.error(e.message);
        }
      })
      .finally(() => {
        this.changeBuyerMemberDialog.confirmLoading = false;
      });
  }

  private doAdjustSubmit() {
    let doAdjust = (pass: boolean) => {
      if (!pass) {
        return;
      }
      this.reissueDialog.confirmLoading = true;
      this.adjustDialog.confirmLoading = true;
      let req = new PrePayCardAdjust();
      req.code = this.adjustDialog.code;
      req.remark = this.adjustDialog.remark;
      req.validDate = this.adjustDialog.expire;
      PrePayCardApi.adjustValidPeriod(req)
        .then((res: any) => {
          this.$message.success(this.i18n("调整有效期/调整成功"));
          this.adjustDialog.dialogVisible = false;
          this.getList();
        })
        .catch((e) => {
          if (e && e.message) {
            this.$message.error(e.message);
          }
        })
        .finally(() => {
          this.adjustDialog.confirmLoading = false;
        });
    };
    this.$refs["adjustForm"].validate(doAdjust);
  }

  private presentCard() {
    this.presentDialog.dialogVisible = false;
    let doPresent = (pass: boolean) => {
      if (!pass) {
        return;
      }
      PrePayCardApi.presentCard(this.presentDialog.memberMobile, this.presentDialog.code)
        .then((resp: any) => {
          if (resp && resp.code === 2000) {
            this.$message.success(this.i18n("转赠成功"));
            this.getList();
          } else {
            this.$message.error(resp.msg);
          }
        })
        .catch((error) => {
          if (error && error.message) {
            this.$message.error(error.message);
          }
        });
    };
    this.$refs["presentForm"].validate(doPresent);
  }

  private openPresentCardDialog(code: string) {
    this.presentDialog.dialogVisible = true;
    this.presentDialog.code = code;
  }

  private gotoDtl(row: any) {
    this.$router.push({ name: "-", query: { number: row.number } });
  }

  private parseStateColor(state: string) {
    switch (state) {
      case "PRESENTING":
        return "blue";
      case "UNACTIVATED":
        return "orange";
      case "USING":
        return "green";
      case "CANCELLED":
        return "grey";
      case "USED":
        return "violet";
    }
  }

  private format(d: Date) {
    return DateUtil.format(d, "yyyy-MM-dd");
  }

  private showHst(code: string, defaultTab: string) {
    this.dtlDialog.tmpCode = code;
    this.dtlDialog.defaultTab = defaultTab ? defaultTab : "账户流水";
    if (!code || "" == code) {
      this.dtlDialog.dialogVisible = false;
      this.dtlDialog.defaultTab = "";
    } else {
      this.dtlDialog.dialogVisible = true;
    }
  }

  private gotoCardTplDtl(num: string) {
    const route = this.$router.resolve({
      name: 'prepay-card-tpl-dtl',
      query: {
        number: num
      }
    })
    window.open(route.href, '_blank')
  }

  private gotoGiftActivityDtl(activityId: any) {
    this.$router.push({ name: "gift-card-activity-dtl", query: { activityId: activityId } });
  }

  private gotoImprestCardDtl(num: any) {
    this.$router.push({ name: "imprest-card-sale-bill-dtl", query: { number: num } });
  }

  private getPrePermission() {
    PrePayConfigApi.get()
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.enableMultipleAccount = resp.data.enableMultipleAccount;
        }
      })
      .catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message);
        }
      });
  }

  private isNewer(t: Date) {
    if (t === undefined || t === null) {
      return "-";
    }
    let now = DateUtil.nowDayTime();
    let time = new Date(t);
    return now.getTime() - time.getTime() > 0;
  }

  //是否展示挂失按钮
  private canShowLoss(scope: any) {
    const flag1 = scope.row.state === 'USING'
    const flag2 = (scope.row.cardMedium && ['online'].indexOf(scope.row.cardMedium) === -1)  //只有实体卡才能挂失
    // const flag2 = ([this.i18n('实体礼品卡'), this.i18n('储值卡'), this.i18n('次卡')].indexOf(scope.row.cardType) > -1)
    const flag3 = this.hasOptionPermission('/卡/卡管理/卡查询/卡查询', '挂失/解挂')
    return flag1 && flag2 && flag3
  }

  //是否展示解挂按钮
  private canShowRemoveLoss(scope: any) {
    const flag1 = scope.row.state === 'LOST'
    const flag2 = this.hasOptionPermission('/卡/卡管理/卡查询/卡查询', '挂失/解挂')
    return flag1 && flag2
  }

  //是否展示补发按钮
  private canShowSupply(scope: any) {
    const flag1 = ['USING', 'LOST'].indexOf(scope.row.state) > -1
    const flag2 = (scope.row.cardMedium && ['online'].indexOf(scope.row.cardMedium) === -1) //  只有实体卡才能补发
    const flag3 = this.hasOptionPermission('/卡/卡管理/卡查询/卡查询', '补发')
    const flag4 = scope.row.cardType !== this.i18n('次卡')
    return flag1 && flag2 && flag3 && flag4
  }

  //是否展示重置密码按钮
  private canShowResetCode(scope: any) {
    const flag1 = scope.row.state === 'USING'
    const flag2 = (scope.row.cardMedium && ['online'].indexOf(scope.row.cardMedium) === -1) //  只有实体卡才能重置密码
    const flag3 = this.hasOptionPermission('/卡/卡管理/卡查询/卡查询', '重置密码')
    const flag4 = [this.i18n('充值卡'), this.i18n('次卡')].indexOf(scope.row.cardType) === -1
    return flag1 && flag2 && flag3 && flag4
  }

  //是否展示作废按钮
  private canShowCancel(scope: any) {
    return ['UNACTIVATED', 'USING', 'PRESENTING'].indexOf(scope.row.state) > -1 && this.hasOptionPermission('/卡/卡管理/卡查询/卡查询', '作废')
  }

  // 是否展示登记坏卡按钮
  canShowBad(scope: any) {
    return ['MADE', 'BLANK'].indexOf(scope.row.state) > -1 && this.hasOptionPermission('/卡/卡管理/卡查询/卡查询', '坏卡登记')
  }

  // 登记坏卡
  registerBadCard(code: string) {
    this.$alert(this.i18n("/卡/坏卡重制/请确认是否将该卡登记为坏卡"), this.i18n("提示"), {
      confirmButtonText: this.i18n("确定"),
      cancelButtonText: this.i18n("取消"),
      type: "warning",
      callback: (action) => {
        if (action === "confirm") {
          PrePayCardApi.regBroken(code)
            .then((resp: any) => {
              if (resp && resp.code === 2000) {
                this.$message.success(this.i18n("操作成功"));
                this.getList();
              } else {
                throw new Error(resp.msg)
              }
            })
            .catch((error) => {
              this.$message.error(error.message);
            });
        }
      },
    });
  }
}
