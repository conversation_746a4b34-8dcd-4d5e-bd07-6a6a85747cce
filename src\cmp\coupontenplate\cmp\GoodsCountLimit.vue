<template>
	<div class="goods-count-limit">
		<div class="gray-tip">{{ formatI18n("/公用/券模板/用于控制购买商品数量满足条件才可用券。比如设置最低2件则购买2件及以上数量才能用券。") }}</div>
		<el-radio-group v-model="useThresholdType">
			<el-radio :label="3">{{formatI18n("/公用/券模板/不限制")}}</el-radio>
			<el-radio :label="6">
                {{formatI18n('/公用/券模板/最低')}}
                <el-input style="width: 80px"></el-input>
                {{formatI18n('/公用/券模板/件')}}
            </el-radio>
		</el-radio-group>
	</div>
</template>

<script lang="ts" src="./GoodsCountLimit.ts"></script>

<style lang="scss"></style>
