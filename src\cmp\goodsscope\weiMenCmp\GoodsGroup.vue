<template>
    <div class="goods-group" v-if="groupList.length > 0">
        <div class="group-tips" v-if="showTips">
            <i class="el-icon-info" style="color: #006aff"></i>
            <div class="tips-message">{{ formatI18n('/公用/券模板/微盟适用商品',
                '选择分组后，若已选分组更换了上级分组，再次编辑时，上级分组勾选状态可能会产生误差，为了确保数据准确性，请重新勾选') }}</div>
            <span style="margin-left: 16px; color: #006aff; cursor: pointer;" @click.stop="doClear">{{
                formatI18n('/公用/券模板/微盟适用商品', '清空已选') }}</span>
            <i class="el-icon-close" style="margin-left: 8px; font-size: 18px; cursor: pointer;"
                @click="showTips = false"></i>
        </div>
        <div class="group-content">
            <div class="group-box" :style="{ height: showTips ? (height - 40) + 'px' : height + 'px' }">
                <div class="box-li" v-for="(item, index) in groupList" :key="item.classifyId"
                    :class="{ active: selectFirstIndex == index }">
                    <el-checkbox v-model="item.selected" :indeterminate="item.childs.length > 0"
                        @change="doChange"></el-checkbox>
                    <div class="li-content" @click.stop="doSelectFirstLine(index)">{{ item.name }}</div>
                    <i class="el-icon-arrow-right li-icon" v-if="!item.isLeaf" @click.stop="doSelectFirstLine(index)" />
                </div>
            </div>
            <div class="group-box" :style="{ height: showTips ? (height - 40) + 'px' : height + 'px' }" v-loading="loading">
                <div class="box-li" v-for="(item, index) in subGroupList" :key="item.classifyId">
                    <el-checkbox v-model="item.selected" @change="doChangeSub"></el-checkbox>
                    <div class="li-content">{{ item.name }}</div>
                </div>
            </div>
        </div>
    </div>
    <empty :height="height" v-else :emptyText="formatI18n('/公用/提示/暂无数据')" />
</template>
<script lang="ts">
import { Vue, Component, Prop, Watch } from "vue-property-decorator";
import WeimobExtLimitGoodsGroupRuleInfo from "model/common/weimob/WeimobExtLimitGoodsGroupRuleInfo";
import BQueryGoodsClassifyRequest from "model/common/weimob/BQueryGoodsClassifyRequest";
import BWeimobExtLimitGoodsGroupRule from "model/common/weimob/BWeimobExtLimitGoodsGroupRule";
import BWeimobClassify from "model/common/weimob/BWeimobClassify";
import empty from "./empty.vue";
import WeimobApi from "http/coupon/template/WeimobApi";
class BWeimobClassifyList extends BWeimobClassify {
    selected: boolean = false;
    childs: any = [];
}

@Component({
    components: { empty },
})
export default class GoodsGroup extends Vue {
    @Prop({
        default: 400,
    })
    height: number;
    @Prop({
        default: [],
    })
    classifyList: BWeimobClassify[];
    @Prop()
    limitGoodsGroupRule: BWeimobExtLimitGoodsGroupRule;
    @Prop({
        type: String,
        default: "",
    })
    org: string;

    showTips: boolean = true;
    isIndeterminate: boolean = true;
    groupList: BWeimobClassifyList[] = [];
    subGroupList: BWeimobClassifyList[] = [];
    selectFirstIndex: number = -1;
    selectSecondIndex: number = -1;
    ruleInfos: WeimobExtLimitGoodsGroupRuleInfo[] = [];
    loading: boolean = false

    @Watch("limitGoodsGroupRule", { deep: true })
    onValueChange(value: BWeimobExtLimitGoodsGroupRule) {
        this.doBindValue();
    }
    @Watch("classifyList", { deep: true })
    onClassifyListChange(value: any) {
        this.groupList = this.classifyList.reduce(
            (acc: BWeimobClassifyList[], cur) => {
                acc.push({
                    selected: false,
                    childs: [],
                    ...cur,
                });
                return acc;
            },
            []
        );
        this.doBindValue();
    }
    mounted() {
        this.groupList = this.classifyList.reduce(
            (acc: BWeimobClassifyList[], cur) => {
                acc.push({
                    selected: false,
                    childs: [],
                    ...cur,
                });
                return acc;
            },
            []
        );
        this.doBindValue();
    }

    doBindValue() {
        const ruleInfos = this.limitGoodsGroupRule.ruleInfos || [];
        this.ruleInfos = ruleInfos;
        if (ruleInfos && ruleInfos.length > 0) {
            this.groupList.forEach(group => {
                const flagIndex = ruleInfos.findIndex(
                    (item) => item.classifyId == group.classifyId
                );
                group.selected = flagIndex > -1
                group.childs = flagIndex > -1 ? ruleInfos[flagIndex].childs || [] : []
            })
        } else {
            this.groupList.forEach((item) => {
                item.selected = false;
                item.childs = [];
            });
            this.subGroupList.forEach((item) => {
                item.selected = false;
            });
        }
        const groupFirstInfo = this.selectFirstIndex > -1 ? this.groupList[this.selectFirstIndex] : null;
        const childs = groupFirstInfo && groupFirstInfo.childs || []
        this.subGroupList.forEach(group => {
            if (groupFirstInfo && groupFirstInfo.selected) {
                if (childs.length == 0) {
                    group.selected = true
                } else {
                    const flagIndex = childs.findIndex((item: any) => item.classifyId == group.classifyId)
                    group.selected = flagIndex > -1
                }
            } else {
                group.selected = false
            }
        })
        this.$forceUpdate();
    }

    doSelectFirstLine(index: number) {
        this.selectFirstIndex = index;
        this.subGroupList = [];
        this.queryGroupList();
    }

    doClear() {
        const limitGoodsGroupRule = JSON.parse(
            JSON.stringify(this.limitGoodsGroupRule)
        );
        limitGoodsGroupRule.ruleInfos = [];
        limitGoodsGroupRule.excludeGoodsIds = [];
        this.$emit("change", limitGoodsGroupRule);
    }

    doChange() {
        const ruleInfos: WeimobExtLimitGoodsGroupRuleInfo[] = [];
        this.groupList.forEach((item) => {
            if (item.selected) {
                ruleInfos.push({
                    classifyId: item.classifyId,
                    classifyLevel: item.classifyLevel,
                    parentId: item.parentId,
                    name: item.name,
                    isChecked: false,
                    childs: item.childs,
                    childNum: -1,
                });
            }
        });
        const limitGoodsGroupRule = JSON.parse(
            JSON.stringify(this.limitGoodsGroupRule)
        );
        limitGoodsGroupRule.ruleInfos = ruleInfos;
        this.$emit("change", limitGoodsGroupRule);
        this.$forceUpdate();
    }

    doChangeSub() {
        const groupFirstInfo = this.groupList[this.selectFirstIndex];
        const ruleInfos = JSON.parse(JSON.stringify(this.ruleInfos));
        if (groupFirstInfo) {
            const flagIndex = this.ruleInfos.findIndex(
                (item) => item.classifyId == groupFirstInfo.classifyId
            );
            const classifyList = this.subGroupList.reduce((acc: WeimobExtLimitGoodsGroupRuleInfo[], cur) => {
                if (cur.selected) {
                    acc.push({
                        classifyId: cur.classifyId,
                        classifyLevel: cur.classifyLevel,
                        parentId: cur.parentId,
                        name: cur.name,
                        isChecked: false,
                        childs: [],
                        childNum: 0,
                    });
                }
                return acc
            }, []);
            if (classifyList.length > 0) {
                if (flagIndex == -1) {
                    ruleInfos.push({
                        classifyId: groupFirstInfo.classifyId,
                        classifyLevel: groupFirstInfo.classifyLevel,
                        parentId: groupFirstInfo.parentId,
                        name: groupFirstInfo.name,
                        isChecked: false,
                        childs: classifyList.length == this.subGroupList.length ? [] : classifyList,
                        childNum: 0,
                    });
                } else {
                    ruleInfos[flagIndex].childs = classifyList.length == this.subGroupList.length ? [] : classifyList
                }
            } else {
                if (flagIndex > -1) {
                    ruleInfos.splice(flagIndex, 1)
                }
            }
        }
        const limitGoodsGroupRule = JSON.parse(
            JSON.stringify(this.limitGoodsGroupRule)
        );
        limitGoodsGroupRule.ruleInfos = ruleInfos;
        this.$emit("change", limitGoodsGroupRule);
    }

    async queryGroupList() {
        const params = new BQueryGoodsClassifyRequest();
        if (this.org) params.marketingCenter = this.org;
        params.pageNum = 1;
        params.pageSize = 200;
        if (this.selectFirstIndex < 0) return;
        params.parentId = this.groupList[this.selectFirstIndex].classifyId;
        this.loading = true
        try {
            const { data } = await WeimobApi.queryGroupList(params);
            this.loading = false
            const groupFirstInfo = this.groupList[this.selectFirstIndex];
            this.subGroupList = (data || []).reduce(
                (acc: BWeimobClassifyList[], cur) => {
                    const flag = groupFirstInfo.childs.findIndex(
                        (item: BWeimobClassifyList) => item.classifyId == cur.classifyId
                    );
                    acc.push({
                        selected:
                            flag > -1 ||
                            (groupFirstInfo.selected && groupFirstInfo.childs.length == 0),
                        childs: [],
                        ...cur,
                    });
                    return acc;
                },
                []
            );
        } catch (error) {
            this.$message.error((error as Error).message);
            this.loading = false
        }
    }
}
</script>
<style lang="scss" scoped>
.goods-group {
    width: 100%;

    .group-tips {
        width: 100%;
        height: 38px;
        background: #f2f7fe;
        margin-top: 2px;
        padding: 0 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
        color: #1e2226;

        .tips-message {
            flex: 1;
            padding-left: 5px;
        }
    }

    .group-content {
        width: 100%;
        display: flex;
        justify-content: space-between;

        .group-box {
            flex: 1;
            overflow-y: auto;
            border-right: 1px solid #e3e2e5;

            .box-li {
                width: 100%;
                height: 36px;
                padding: 0 16px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                cursor: pointer;

                ::v-deep .el-checkbox {
                    margin-right: 15px;
                }

                &:hover {
                    background: #f5f7fa;
                }

                &.active>.li-content {
                    color: #006aff;
                }

                .li-content {
                    flex: 1;
                    color: #1e2226;
                    font-size: 14px;
                }

                .li-icon {
                    color: #b2aebc;
                    font-size: 14px;
                }
            }
        }
    }
}
</style>