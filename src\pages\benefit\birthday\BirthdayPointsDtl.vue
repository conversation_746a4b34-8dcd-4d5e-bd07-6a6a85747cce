<template>
  <div class="birthday-points-dtl">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <span v-if="hasOptionPermission('/营销/营销/节日有礼/生日积分加倍', '规则维护')">
          <el-button v-if="!pointsRule.stopped" @click="doModify"
                     type="primary">{{ formatI18n('/公用/按钮', '修改') }}</el-button>
          <el-button @click="switchState" :type="pointsRule.stopped?'success':'danger'">
            <span v-if="pointsRule.stopped">{{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/详情', '启用') }}</span>
            <span v-else>{{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/详情', '禁用') }}</span>
          </el-button>
        </span>
      </template>
    </BreadCrume>
    <div class="content" style="padding: 50px">
      <FormItem :label="formatI18n('/权益/生日权益初始化/生日权益初始化','权益生效时间')">
        <div style="padding-top: 8px" v-if="pointsRule&& pointsRule.effectType==='BY_DAY'">
          {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/权益生效时间', '生日当天') }}
        </div>
        <div style="padding-top: 8px" v-else-if="pointsRule&& pointsRule.effectType==='BY_WEEK'">
          {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/权益生效时间', '生日当周') }}
        </div>
        <div style="padding-top: 8px" v-else-if="pointsRule&& pointsRule.effectType==='BY_MONTH'">
          {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/权益生效时间', '生日当月') }}
        </div>
        <div style="padding-top: 10px" v-else>-</div>
      </FormItem>
      <FormItem :label="getLabel">
        <div v-if="pointsRule&& pointsRule.samePointsTimes" style="padding-top: 8px">
          {{ getPointsTimes(pointsRule.samePointsTimes) }}
        </div>
        <div v-else-if="pointsRule&& pointsRule.differentPointsTimes" style="padding-top: 10px">
          <div>{{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/会员生日权益', '不同等级会员享不同权益') }}：</div>
          <div style="border: 1px solid #ddd;padding: 2em; width: 70%">
            <div v-if="pointsItems&&pointsItems.length > 0" v-for="value in pointsItems">
              {{ value.name }} {{ getPointsTimes(value.value) }}
            </div>
          </div>
        </div>
      </FormItem>
      <!--/权益/生日权益初始化/生日权益初始化/每人每个生效时段得权益次数：-->
      <FormItem :label="formatI18n('/权益/生日权益初始化/生日权益初始化/首页/生日积分加倍/每人每个生效时段得权益次数：')">
        <div style="padding-top: 8px" v-if="pointsRule&& pointsRule.memberMaxGainPointsTimes">
          {{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/修改/限') }}{{
            pointsRule.memberMaxGainPointsTimes
          }}{{ formatI18n('/权益/生日权益初始化/生日权益初始化/生日积分加倍/修改/次') }}
        </div>
        <div style="padding-top: 8px" v-else>
          {{ formatI18n('/权益/生日权益初始化/生日权益初始化/首页/生日积分加倍/不限制') }}
        </div>
      </FormItem>
    </div>
  </div>
</template>

<script src="./BirthdayPointsDtl.ts">
</script>

<style lang="scss">
.birthday-points-dtl {
  background-color: white;
  overflow: auto;
  height: 100%;
  width: 100%;

  .subTitle {
    font-size: 16px;
    padding-top: 20px;
    padding-left: 30px;
  }

  .content {
    padding: 50px;

    .qf-form-item .qf-form-label {
      width: auto !important;
    }
  }
}
</style>