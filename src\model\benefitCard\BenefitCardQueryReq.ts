// 付费会员卡查询
export default class BenefitCardQueryReq {
  // 卡模板类型等于=
  templateTypeEquals: string = ''
  // 是否限制营销中心
  limitMarketingCenter: Nullable<boolean> = null
  // 营销中心
  marketingCenterEquals: Nullable<string> = null
  // 会员信息-支持手机号/会员号/实体卡号
  memberCodeEquals: Nullable<string> = null
  // 卡名称
  nameEquals: Nullable<string> = null
  // 卡号
  cardNoEquals: Nullable<string> = null
  // 开卡时间-范围开始
  openCardTimeBegin: Nullable<string> = null
  // 开卡时间-范围结束
  openCardTimeEnd: Nullable<string> = null
  // 
  memberIdEquals: Nullable<string> = null
  // 发卡门店
  issueOrgIdEquals: Nullable<string> = null
  //
  page: Nullable<number> = null
  //
  pageSize: Nullable<number> = null
}