<template>
  <div class="sale-card-list-container">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button size="large" type="primary" @click="doCreate" v-if="hasOptionPermission('/卡/卡管理/售卡单', '单据维护')">
          {{ i18n('新建售卡单') }}
        </el-button>
      </template>
    </BreadCrume>
    <div class="setting-container">
      <MyQueryCmp @reset="onReset" @search="onSearch">
        <el-row>
          <el-col :span="8">
            <form-item :label="i18n('售卡单号')">
              <el-input :placeholder="i18n('请输入售卡单号')" v-model="query.billNumberEquals"></el-input>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="i18n('状态')">
              <el-select placeholder="不限" v-model="query.stateEquals" style="width:100%">
                <el-option :label="i18n('全部')" :value="null"></el-option>
                <el-option :label="i18n('未审核')" value="INITIAL"></el-option>
                <el-option :label="i18n('售卡中')" value="SALEING"></el-option>
                <el-option :label="i18n('售卡完成')" value="FINISH"></el-option>
              </el-select>
            </form-item>
          </el-col>
          <el-col :span="8">
            <form-item :label="i18n('卡模板号')">
              <el-input :placeholder="i18n('请输入卡模板号')" v-model="query.cardTemplateNumberEquals"></el-input>
            </form-item>
          </el-col>
        </el-row>
        <template slot="opened">
          <el-row>
            <el-col :span="8">
              <form-item :label="i18n('卡模板名称')">
                <el-input :placeholder="i18n('类似于')" v-model="query.cardTemplateNameLikes"></el-input>
              </form-item>
            </el-col>
            <el-col :span="8">
              <form-item :label="i18n('创建时间')">
                <el-date-picker style="width: 100%" v-model="query.createdBetweenClosedOpen" type="daterange" range-separator="-"
                  :picker-options="pickerOptions(0)" start-placeholder="开始日期" end-placeholder="结束日期">
                </el-date-picker>
              </form-item>
            </el-col>
            <el-col :span="8">
              <form-item :label="i18n('最后修改时间')">
                <el-date-picker style="width: 100%" v-model="query.lastModifiedBetweenClosedOpen" type="daterange" range-separator="-"
                  :picker-options="pickerOptions(1)" start-placeholder="开始日期" end-placeholder="结束日期">
                </el-date-picker>
              </form-item>
            </el-col>
          </el-row>
        </template>
      </MyQueryCmp>
      <div style="margin:12px 0">
        <i18n k="/卡/卡管理/卡回收单/共选中{0}行">
          <span slot="0" class="select-num">{{selected.length}}</span>
        </i18n>
        <el-button style="margin-left:12px" v-if="hasOptionPermission('/卡/卡管理/售卡单', '单据维护')" @click="batchRemove">{{i18n('批量删除')}}</el-button>
      </div>
      <el-table :data="tableData" row-key="billNumber" style="width: 100%;margin-top:12px" @selection-change="handleSelectionChange" fixed>
        <el-table-column type="selection" width="55" reserve-selection>
        </el-table-column>
        <el-table-column :label="i18n('售卡单号')" width="200" fixed>
          <template slot-scope="scope">
            <span class="span-btn" @click="goDtl(scope.row.billNumber)">{{scope.row.billNumber ||'-'}}</span>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('状态')" width="120">
          <template slot-scope="scope">
            <div style="display:flex;align-items:center">
              <span class="dot" :style="{background: computeState(scope.row.state).color}"></span>
              <span>{{computeState(scope.row.state).state}}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('卡模板号/名称')" width="180">
          <template slot-scope="scope">
            <p style="color: rgba(51, 51, 51, 0.6);">{{scope.row.cardTemplateNumber}}</p>
            <span class="span-btn" :title="scope.row.cardTemplateName" @click="goCardDtl(scope.row.cardTemplateNumber)">
              {{scope.row.cardTemplateName}}
            </span>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('售卡数量')" width="126">
          <template slot-scope="scope">
            <span>{{scope.row.makeQty | fmt }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('卡面额/次数')" width="126">
          <template slot-scope="scope">
            {{scope.row.faceAmount | fmt}}
          </template>
        </el-table-column>
        <el-table-column :label="i18n('实际售价')">
          <template slot-scope="scope">
            {{scope.row.price | fmt}}
          </template>
        </el-table-column>
        <el-table-column :label="i18n('总售价')" width="126">
          <template slot-scope="scope">
            {{scope.row.total | fmt }}
          </template>
        </el-table-column>
        <el-table-column :label="i18n('创建时间')" width="156">
          <template slot-scope="scope">
            {{scope.row.created | dateFormate3}}
          </template>
        </el-table-column>
        <el-table-column :label="i18n('最后修改时间')" width="156">
          <template slot-scope="scope">
            {{scope.row.lastModified | dateFormate3}}
          </template>
        </el-table-column>
        <el-table-column label="创建人" prop="created" width="140">
          <template slot-scope="scope">
            <span>{{scope.row.operator || '--'}}</span>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('操作')" width="130">
          <template slot-scope="scope">
            <div>
              <template v-if="['INITIAL'].indexOf(scope.row.state) > -1">
                <span class="span-btn" v-if="hasOptionPermission('/卡/卡管理/售卡单', '单据审核')" @click="doAudit(scope.row.billNumber)">
                  {{i18n('审核')}}
                </span>
                <span class="span-btn" v-if="hasOptionPermission('/卡/卡管理/售卡单', '单据维护')" @click="doEdit(scope.row.billNumber)">
                  {{i18n('修改')}}
                </span>
                <span class="span-btn" v-if="hasOptionPermission('/卡/卡管理/售卡单', '单据维护')" @click="doRemove(scope.row.billNumber)">
                  {{i18n('删除')}}
                </span>
              </template>
              <span class="span-btn" v-if="['FINISH'].indexOf(scope.row.state) > -1 && hasOptionPermission('/卡/卡管理/售卡单', '单据导出')"
                @click="doDownLoad(scope.row)">
                {{i18n('导出')}}
              </span>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!--分页栏-->
      <el-pagination :current-page="page.page" :page-size="page.pageSize" :page-sizes="[10, 20, 30, 40]" :total="page.total"
        @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background layout="total, prev, pager, next, sizes,  jumper">
      </el-pagination>
    </div>
  </div>
</template>

<script lang="ts" src="./SaleCardList.ts">
</script>

<style lang="scss" scoped>

.sale-card-list-container {
  width: 100%;
  .span-btn {
    margin: 0 3px;
  }
}

</style>