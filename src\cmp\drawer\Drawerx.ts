import {Component, Prop, Vue} from 'vue-property-decorator'

/**
 * 这个时候fant还没支持抽屉组件，等fant升新版本后就不要用这个组件了。
 */
@Component({
  name: 'Drawerx',
  components: {
  }
})
export default class Drawerx extends Vue {
  @Prop()
  contentStyle: string
  @Prop()
  title: boolean
  @Prop()
  visible: boolean
  $refs: any

  @Prop({default: '40%'})
  width: string

  close() {
    this.$emit('update:visible', false)
  }
}
