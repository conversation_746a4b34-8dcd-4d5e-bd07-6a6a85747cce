<!--
 * @Author: 黎钰龙
 * @Date: 2024-08-01 16:15:02
 * @LastEditTime: 2024-08-19 14:01:16
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\cmp\page-place-components\menu\Menu.vue
 * 记得注释
-->
<template>
  <div class="menu" :style="{
        padding:
            localProperty.styMarginTop +
            'px ' +
            localProperty.styMarginRight +
            'px ' +
            localProperty.styMarginBottom +
            'px ' +
            localProperty.styMarginLeft +
            'px',
    }" @click="activeTemplate">
    <div class="toolBar" v-if="activeIndex === index">
      <setting-toolbar :btns="toolBarBtns" @click="toolbarClick"></setting-toolbar>
    </div>
    <div class="menu-list">
      <div class="menu-item" v-for="(item, key) in localProperty.propCustomMenus" v-show="item.enable" :key="key">
        <img class="menu-item-icon" :src="getIcon(item)" />
        <div class="menu-item-title">{{ item.name }}</div>
        <img class="menu-item-right" src="@/assets/image/fellow/ic_back_dark.png" />
      </div>
    </div>
  </div>
</template>
  
<script lang="ts" src="./Menu.ts"></script>
  
<style lang="scss" scoped>
.toolBar {
  position: absolute;
  top: 0;
  right: 1px;
  z-index: 999;
  cursor: pointer;
}

.menu {
  position: relative;

  &-list {
    background-color: #fff;
    border-radius: 5px;
  }

  &-item {
    height: 50px;
    display: flex;
    align-items: center;

    &-icon {
      margin-left: 12px;
      width: 22px;
    }

    &-title {
      margin-left: 5px;
      flex: 1;
      font-size: 14px;
    }

    &-right {
      margin-right: 10px;
      width: 18px;
    }
  }
}
</style>
  