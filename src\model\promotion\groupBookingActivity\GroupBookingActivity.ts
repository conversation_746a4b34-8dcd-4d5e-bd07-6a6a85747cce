import BaseCouponActivity from 'model/coupon/activity/BaseCouponActivity'
import PushGroup from 'model/precisionmarketing/pushplan/PushGroup';
import GroupBookingPrize from "model/promotion/groupBookingActivity/GroupBookingPrize";


export default class GroupBookingActivity extends BaseCouponActivity {
  // 活动图片
  image: Nullable<string> = null
  // 是否屏蔽黑名单
  winBlacklist: Nullable<boolean> = null
  // 黑名单组id
  winBlacklistGroupId: Nullable<string> = null
  // 开团积分
  groupCreatedPoints: Nullable<number> = null
  // 参团积分
  groupJoinedPoints: Nullable<number> = null
  // 团队人数
  succeedMemberLimit: Nullable<number> = null
  // 成团时限 - 天
  succeedDayLimit: Nullable<number> = null
  // 成团时限 - 时
  succeedHourLimit: Nullable<number> = null
  // 成团时限 - 分
  succeedMinuteLimit: Nullable<number> = null
  // 每人限拼次数
  joinedPerPersonCountLimit: Nullable<number> = null
  // 限开团个数
  createdCountLimit: Nullable<number> = null
  // 奖品设置
  prizes: GroupBookingPrize[] = []
  // 参与人群
  rule: Nullable<PushGroup> = null
}