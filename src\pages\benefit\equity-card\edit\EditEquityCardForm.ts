import BEquityCardTemplate from "model/benefit/BEquityCardTemplate"
import BEquityCardExpiryRule from "model/equityCard/BEquityCardExpiryRule"
import { DateType } from "model/weixin/weixinIssueCouponActivity/DateType"

/*
 * @Author: 黎钰龙
 * @Date: 2023-02-24 16:54:33
 * @LastEditTime: 2024-04-24 10:35:53
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\benefit\equity-card\edit\EditEquityCardForm.ts
 * 记得注释
 */
export default class Form {
  data: BEquityCardTemplate = new BEquityCardTemplate()
  rules: any = null

  init(master: any) {
    this.rules = {
      name: [
        {
          required: true,
          message: master.i18n('/公用/券模板/请输入必填项'),
          trigger: ['change', 'blur']
        }
      ],
      number: [
        {
          validator: (rule: any, value: any, callback: any) => {
            if (master.$route.query.type === 'edit') callback()
            const exp = new RegExp(/^[a-zA-Z0-9]{0,6}$/)
            if (!exp.test(master.form.data.number) || !exp.test(master.form.data.codePrefix)) {
              callback(new Error(master.i18n('请按照要求格式输入')))
            }
            callback()
          },
          trigger: ['change', 'blur']
        }
      ],
      availableDate: [
        {
          validator: (rule: any, value: any, callback: any) => {
            const indexArr = [[], [], []] //DAY、MONTH、YEAR的填写记录，不能出现单位、值都重复的情况
            master.availableDate.forEach((item: BEquityCardExpiryRule, index: any) => {
              if (item.validityInfo?.expiryType === 'DAYS') {
                (indexArr[0] as any).push(item.validityInfo.validityDays)
              } else if (item.validityInfo?.expiryType === 'MONTHS') {
                (indexArr[1] as any).push(item.validityInfo.validityDays)
              } else if (item.validityInfo?.expiryType === 'YEARS') {
                (indexArr[2] as any).push(item.validityInfo.validityDays)
              }
              if (!item.price || !item.validityInfo?.validityDays || !item.validityInfo?.expiryType) {
                callback(new Error(master.i18n('请输入权益卡有效期')))
              } else if (
                indexArr.some((item, index) => {
                  return item.length !== [...new Set(item)].length
                })
              ) {
                callback(new Error(master.i18n('有效期重复，请重新设置')))
              }
            });
            callback()
          },
          trigger: ['change', 'blur']
        }
      ],
      remark: [
        {
          validator: (rule: any, value: any, callback: any) => {
            if (master.form.data.remark && master.form.data.remark.length > 500) {
              callback(new Error(master.i18n('最多输入500字')))
            }
            callback()
          },
          trigger: ['change', 'blur']
        }
      ]
    }

    master.availableDate.forEach((item: any, index: any) => {  //有效期 默认选中‘天’
      item.validityInfo.expiryType = "DAYS"
    })
  }

  toParams(master: any) {
    //将表单数据转换为接口参数
    const params = new BEquityCardTemplate()
    params.name = master.form.data.name //卡模板
    params.number = master.form.data.number //卡模板号
    params.codePrefix = master.form.data.codePrefix //固定开头
    params.remark = master.form.data.remark //使用须知
    master.availableDate.forEach((item: any) => {
      if (item.validityInfo) {
        item.validityInfo!.validityType = 'RALATIVE'  //默认相对有效期
      }
      if (item.renewPrice as any === '') { //不填的话就传null
        item.renewPrice = null
      }
    })
    params.expiryRules = master.availableDate
    params.giftBag = {
      points: master.giftBag.points.isSelect ? master.giftBag.points.value : 0,
      coupons: master.giftBag.coupons.isSelect ? master.giftBag.coupons.list.map((item: any) => {
        return {
          qty: item.qty,
          couponTemplateNumber: item.coupons.templateId,
          couponTemplateName: item.coupons.name
        }
      }) : []
    }
    master.receiveCardChannel.forEach((item: any, index: any) => {
      master.selectChannel.forEach((val: any) => {
        if (String(item.channel?.id) + String(item.channel?.type) === val) {
          params.channels.push(item.channel!)
        }
      })

    })
    params.equity = {
      couponItems: master.cardEquity.coupons.map((item: any) => {
        return {
          qty: item.qty,
          couponTemplateNumber: item.coupons.templateId,
          couponTemplateName: item.coupons.name
        }
      }),
      dateInterval: master.cardEquity.intervalVal,
      limitPartakeDateType: master.cardEquity.intervalType,
      limitMemberPerTime: master.cardEquity.total || 1,
      weekDay: master.cardEquity.intervalType === DateType.WEEK ? master.cardEquity.weekDay : null
    }
    return params
  }

  of(master: any, data: BEquityCardTemplate) {  //将接口数据回填表单
    const form = master.form.data as BEquityCardTemplate
    form.name = data.name
    form.number = data.number
    form.codePrefix = data.codePrefix
    form.state = data.state
    form.remark = data.remark
    //领卡权益
    master.cardEquity.intervalVal = data.equity?.dateInterval || 1
    master.cardEquity.intervalType = data.equity?.limitPartakeDateType || 'MONTH'
    master.cardEquity.weekDay = data.equity?.weekDay || 1
    master.cardEquity.total = data.equity?.limitMemberPerTime || 1
    master.cardEquity.coupons = data.equity?.couponItems.map(item => {
      return {
        qty: item.qty,
        coupons: {
          name: item.couponTemplateName,
          templateId: item.couponTemplateNumber
        }
      }
    })
    //领卡奖励
    master.giftBag.points = {
      isSelect: data.giftBag?.points! > 0,
      value: data.giftBag?.points
    }
    master.giftBag.coupons.isSelect = data.giftBag?.coupons && data.giftBag?.coupons.length > 0
    master.giftBag.coupons.list = data.giftBag?.coupons.map(item => {
      return {
        qty: item.qty,
        coupons: {
          name: item.couponTemplateName,
          templateId: item.couponTemplateNumber
        }
      }
    })
    //有效期设置
    if (master.availableDate.length <= data.expiryRules.length) {
      master.availableDate.splice(0, master.availableDate.length)
      data.expiryRules.forEach((item, index) => {
        master.availableDate.push(new BEquityCardExpiryRule())
      })
    }
    data.expiryRules.forEach((item: BEquityCardExpiryRule, index: any, array: any) => {
      master.availableDate[index].price = item.price
      master.availableDate[index].renewPrice = item.renewPrice
      master.availableDate[index].validityInfo = item.validityInfo
    })
    console.log('availableDate', master.availableDate);

    //领卡渠道
    if (data.channels.length) {
      data.channels.forEach((item) => {
        master.selectChannel.push(String(item.id) + String(item.type))
      })
    }
    console.log('form', master.form);
  }
}