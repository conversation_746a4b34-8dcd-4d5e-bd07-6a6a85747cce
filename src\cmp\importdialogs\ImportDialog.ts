import {Component, Prop, Vue} from 'vue-property-decorator'
import EnvUtil from 'util/EnvUtil'
import ImportResultDialog from 'cmp/importdialogs/ImportResultDialog.vue'
import UploadApi from "http/upload/UploadApi";

@Component({
	name: "ImportDialog",
	components: {
		ImportResultDialog,
	},
})
export default class ImportDialog extends Vue {
	$refs: any;
	@Prop()
	title: string; // dialog标题
	@Prop({
		type: Number,
		default: 5000,
	})
	importNumber: string; // 最大导入数量
	@Prop()
	templateName: string; // 模板名称
	@Prop()
	templatePath: string; // 模板路径
	@Prop()
	importUrl: string; // 导入文件接口的url
	@Prop()
	tips: string;
	@Prop()
	activeKey: string;
	@Prop()
	activeVal: string;
	@Prop()
	btnSubmit: string;
	@Prop({
		type: Boolean,
		default: false
	})
	showClear: Boolean; //是否展示清空模块
	@Prop({
		type: Boolean,
		default: true
	})
	showImportResultDialog: Boolean; //是否需要展示搜索结果弹窗
	@Prop({ type: String, default: 'normal' })
	chooseGoodType: String;
	@Prop({ type: Boolean, default: false })
	appreciationGoods: String;
	dialogShow: boolean = false; // 控制模态框的展示
	uploadHeaders: any = {};
	loading = false;
	isClear: Boolean = true; //是否清空原商品

	// 控制结果模态框的展示
	get getUploadUrl() {
		if (this.chooseGoodType === 'appreciation') {
			return EnvUtil.getServiceUrl() + this.importUrl + "?appreciationGoods=true";
		} else if (this.chooseGoodType === 'normal') {
			return EnvUtil.getServiceUrl() + this.importUrl + "?appreciationGoods=false";
		}
		return EnvUtil.getServiceUrl() + this.importUrl + "?appreciationGoods=false";
	}

	created() {
		let locale = sessionStorage.getItem("locale");
		this.uploadHeaders = {
			locale: locale ? locale : "zh_CN",
			time_zone: new Date().getTimezoneOffset(),
			marketingCenter: sessionStorage.getItem("marketCenter"),
		};
    const authorization = EnvUtil.getUniAuthorization()
    if (authorization) {
      this.uploadHeaders.authorization = authorization
    }
	}

  get btnSubmitTxt() {
    if (this.btnSubmit) {
      return this.btnSubmit
    } else {
      return this.formatI18n('/公用/按钮', '确定')
    }
  }

	getImportDesc() {
		let str: any = this.formatI18n("/公用/导入", "为保障上传成功，建议每次最多上传{0}条信息");
		str = str.replace(/\{0\}/g, this.importNumber ? this.importNumber : 5000);
		return str;
	}

	getSuccessInfo(res: any, file: any, uploadFiles: any) {
		this.loading = false;
		this.dialogShow = false;
		if (res && res.code === 2000) {
			this.$refs.upload.clearFiles();
			this.$emit("upload-success", file);
      if (this.showImportResultDialog) {
        this.$refs.importResultDialog.show({
          importResult: file.response.data.success, // 导入结果
          backUrl: file.response.data.backUrl,
          errorCount: file.response.data.errorCount,
          ignoreCount: file.response.data.ignoreCount,
          successCount: file.response.data.successCount,
        });
      }
		} else {
			this.$refs.upload.clearFiles();
			this.$message.error(file.response.msg);
		}
	}

	getErrorInfo(res: any, file: any, uploadFiles: any) {
		this.loading = false;
		this.$message.error(this.formatI18n("/公用/导入", "导入失败，请重新导入") as string);
		this.$refs.upload.clearFiles();
	}

	show() {
		this.dialogShow = true;
	}

	doModalClose(type: string) {
		if (type === "confirm") {
			if (this.$refs.upload.uploadFiles.length <= 0) {
				this.$message.warning(this.formatI18n("/公用/导入", "请先选择文件") as string);
				return;
			}
			this.loading = true;
			this.$refs.upload.submit();
		} else {
			this.$refs.upload.clearFiles();
			this.dialogShow = false;
		}
	}

	clearChange(){
    this.$emit('clearChange', this.isClear)
  }

	downloadTemplate() {
		UploadApi.getUrl(this.templatePath).then((resp: any) => {
			if (resp && resp.data) {
				window.open(resp.data);
			}
		}).catch((error) => {
			if (error && error.message) {
				this.$message.error(error.message)
			}
		})
	}
}
