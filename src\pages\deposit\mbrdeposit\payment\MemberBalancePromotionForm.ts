import GoodsRange from 'model/common/GoodsRange'
import MemberBalancePromotionActivity, {Strategy} from 'model/payment/member/MemberBalancePromotionActivity'
import StoreRange from 'model/common/StoreRange'
import ActivityBody from 'model/common/ActivityBody'
import DateUtil from 'util/DateUtil'
import RSGrade from 'model/common/RSGrade'
import GradeStepValue from "model/common/GradeSameReduction";
import GradeStepValues from 'model/common/GradeStepValues'
import ActivityDateTimeCondition from "model/common/ActivityDateTimeCondition";


class MemberBalancePromotionFormData {
  // 活动id
  activityId: Nullable<string> = null
  // 名称
  name: Nullable<string> = null
  // 活动主题代码
  topicCode: Nullable<string> = null
  // 活动时间
  timeRange: Date[] = []
  // 活动门店
  stores: Nullable<StoreRange> = new StoreRange()
  // 商品范围
  goods: Nullable<GoodsRange> = new GoodsRange()
  	// 活动时间和时间限制
	activityDateTimeCondition = new ActivityDateTimeCondition();
  // 立减策略
  strategy: Nullable<Strategy> = 'BY_AMOUNT'
  favThresholdLimit = false
  favThreshold: Nullable<number> = null
  // 优惠形式
  favType: Nullable<'stairs' | 'step'> = 'step'
  // 优惠规则
  favRule: Nullable<'gradeSame' | 'gradeDiff'> = 'gradeSame'
  // 不同等级规则
  gradeDifferentStepValue: GradeStepValue[] = []
  // 相同等级规则
  gradeSameStepValue: GradeStepValue = new GradeStepValue()
  // 是否参与前台促销
  excludePromotion: Nullable<boolean> = false
  // 整单储值支付
  fullBalancePay: Nullable<boolean> = true
  // 活动说明
  remark: Nullable<string> = null
}

export default class MemberBalancePromotionForm {
  master: any
  data: MemberBalancePromotionFormData = new MemberBalancePromotionFormData()
  rules: any
  amountRules: any
  discountRules: any
  qtyRules: any
  init(master: any) {
    this.master = master
    this.rules = {
      name: [
        {required: true, message: this.master.i18n('请输入活动名称'), trigger: ['change', 'blur']},
        {min: 1, max: 80, message: this.master.i18n('长度在80个字符以内'), trigger: ['change', 'blur']}
      ],
      timeRange: [
        {
          validator: (rule: any, value: any, callback: any) => {
            if (!this.data.timeRange || this.data.timeRange.length === 0) {
              callback(new Error(this.master.i18n('请选择活动时间')))
            }
            callback()
          },
          trigger: ['change', 'blur']
        }
      ],
      favThreshold: [
        {
          validator: (rule: any, value: any, callback: any) => {
            if (this.data.favThresholdLimit && !value) {
              callback(master.i18n('/公用/js提示信息/请填写必填项'))
            }
            callback()
          }, trigger: ['change', 'blur']
        }
      ],
      favRule: [
        {
          validator: (rule: any, value: StoreRange, callback: any) => {
            callback()
          }, trigger: ['change', 'blur']
        }
      ],
      gradeDifferentStepValue: [
        {
          validator: (rule: any, value: any[], callback: any) => {
            if (value.filter((e)=>e.checked).length === 0) {
              callback(master.i18n('/公用/js提示信息/请填写必填项'))
            }
            callback()
          }, trigger: ['change', 'blur']
        }
      ]
    }

    this.amountRules = [
      {required: true, message: this.master.i18n('请输入金额'), trigger: ['change', 'blur']},
    ]
    this.discountRules = [
      {required: true, message: this.master.i18n('请输入折扣'), trigger: ['change', 'blur']},
    ]
    this.qtyRules = [
      {required: true, message: this.master.i18n('请输入数量'), trigger: ['change', 'blur']},
    ]
  }

  toParams() {
    let formData: MemberBalancePromotionFormData = JSON.parse(JSON.stringify(this.data))
    let params = new MemberBalancePromotionActivity()
    params.body = new ActivityBody()
    params.body.name = formData.name
    params.body.topicCode = formData.topicCode
    params.dateTimeCondition = formData.activityDateTimeCondition.dateTimeCondition
    params.body.beginDate = formData.activityDateTimeCondition.beginDate
    params.body.endDate = formData.activityDateTimeCondition.endDate
    params.body.stores = formData.stores
    params.body.remark = formData.remark
    params.goods = formData.goods
    params.strategy = formData.strategy
    params.excludePromotion = formData.excludePromotion
    params.fullBalancePay = formData.fullBalancePay;
    if (formData.favThresholdLimit) {
      params.favThreshold = formData.favThreshold
    }
    if (formData.favType === 'step' && formData.favRule === 'gradeSame') {
      // params.gradeSameStepValue = formData.gradeSameStepValue
      // params.gradeSameStepValue!.checked = formData.gradeSameStepValue.checked;
      params.gradeSameStepValue!.grade = formData.gradeSameStepValue.grade;
      params.gradeSameStepValue!.gradeName = formData.gradeSameStepValue.gradeName;
      params.gradeSameStepValue!.stepValues = [
				{
					threshold: formData.gradeSameStepValue.threshold,
					value: formData.gradeSameStepValue.value,
				},
			];
      params.gradeDifferentStepValue = null
    } else if (formData.favType === 'step' && formData.favRule === 'gradeDiff') {
      params.gradeDifferentStepValue = []
      formData.gradeDifferentStepValue.map((e)=>{
        if(e.checked) {
          let c = new GradeStepValues();
          c.grade = e.grade
          c.gradeName = e.gradeName;
          c.stepValues = [
            {
              threshold: e.threshold,
              value: e.value
            }
          ]
          params.gradeDifferentStepValue!.push(c)
        }
      })
      params.gradeSameStepValue = null
    }
    return params
  }

  of(activity: MemberBalancePromotionActivity, gradeList: RSGrade[]) {
    if (!activity || !activity.body) {
      return
    }
    this.data.name = activity.body.name
    this.data.stores = activity.body.stores
    this.data.activityDateTimeCondition.beginDate = activity.body.beginDate
    this.data.activityDateTimeCondition.endDate = activity.body.endDate
    this.data.activityDateTimeCondition.dateTimeCondition = activity.dateTimeCondition 
    this.data.goods = activity.goods
    this.data.strategy = activity.strategy
    this.data.excludePromotion = activity.excludePromotion
    this.data.fullBalancePay = activity.fullBalancePay
    this.data.remark = activity.body.remark
    if (activity.favThreshold) {
      this.data.favThresholdLimit = true
      this.data.favThreshold = activity.favThreshold
    } else {
      this.data.favThresholdLimit = false
    }
    if (activity.gradeSameStepValue) {
      this.data.favType = 'step'
      this.data.favRule = 'gradeSame'
      // this.data.gradeSameStepValue = activity.gradeSameStepValue
      this.data.gradeSameStepValue.grade = activity.gradeSameStepValue.grade;
      this.data.gradeSameStepValue.gradeName = activity.gradeSameStepValue.gradeName;
      this.data.gradeSameStepValue.threshold = activity.gradeSameStepValue.stepValues[0]!.threshold;
      this.data.gradeSameStepValue.value = activity.gradeSameStepValue.stepValues[0]!.value;

    } else if (activity.gradeDifferentStepValue) {
      this.data.favType = 'step'
      this.data.favRule = 'gradeDiff'
      this.data.gradeDifferentStepValue = []
      for (let grade of gradeList) {
        let filterReduction = activity.gradeDifferentStepValue.filter((e) => e.grade === grade.code)
        let result = new GradeStepValue()
        if (filterReduction.length > 0) {
          result.grade = grade.code
          result.gradeName = grade.name
          result.checked = true
          result.threshold = filterReduction[0].stepValues[0]!.threshold
          result.value = filterReduction[0].stepValues[0]!.value;
        } else {
          result.grade = grade.code
          result.gradeName = grade.name
          result.checked = false
          result.threshold = null
          result.value = null
        }
        this.data.gradeDifferentStepValue.push(result)
      }
    }
  }
}