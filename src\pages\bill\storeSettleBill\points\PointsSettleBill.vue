<template>
  <div class="prepay-card-adjust">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button size="large" type="primary" @click="doExport"
                   v-if="hasOptionPermission('/数据/门店结算账单/积分账单', '数据导出')">
          {{ i18n('导出') }}
        </el-button>
      </template>
    </BreadCrume>
      <ListWrapper class="current-page" style="height: 95%">
        <template slot="query">
          <el-row>
            <el-col :span="8">
              <form-item :label="i18n('账单日期')">
                <el-date-picker
                    :end-placeholder="formatI18n('/营销/券礼包活动/券礼包活动', '结束时间')"
                    format="yyyy-MM-dd"
                    range-separator="-"
                    ref="selectDate"
                    size="small"
                    :start-placeholder="formatI18n('/营销/券礼包活动/券礼包活动', '开始时间')"
                    type="daterange"
                    v-model="billDate"
                    value-format="yyyy-MM-dd">
                </el-date-picker>
              </form-item>
            </el-col>
            <el-col :span="8">
              <form-item :label="i18n('门店')">
                <SelectStores v-model="occurredOrg" @change="$forceUpdate()" :isOnlyId="false"
                              :hideAll="true" width="100%"
                              :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
                </SelectStores>
              </form-item>
            </el-col>
          </el-row>
          <el-row style="margin-top: 10px">
            <el-col :span="8">
              <form-item>
                <el-button @click="onSearch" type="primary">{{i18n('查询')}}</el-button>
                <el-button @click="onReset">{{i18n('重置')}}</el-button>
              </form-item>
            </el-col>
          </el-row>
        </template>
        <template slot="btn">
        </template>
        <template slot="list"style="width: 100%">
          <el-row>
            <el-table
                :data="tableData"
                ref="table"
                border
                style="
              margin-top:10px"
            >
              <el-table-column :label="i18n('门店')" prop="occurredOrgId" :width="getColumnWidth('occurredOrgId', 200)">
                <template slot-scope="scope">
                  {{ showIdName(scope.row.occurredOrgId,scope.row.occurredOrgName) || '--'}}
                </template>
              </el-table-column>
              <el-table-column :label="i18n('账单日期')" prop="settleTime" :width="getColumnWidth('settleTime', 200)">
                <template slot-scope="scope">
                  {{ scope.row.settleTime || '--' }}
                </template>
              </el-table-column>
              <el-table-column :label="i18n('发生渠道')" :width="getColumnWidth('settleTime', 150)">
                <template slot-scope="scope">
                  {{ showChannel(scope.row.channelId, scope.row.channelType) }}
                </template>
              </el-table-column>
              <el-table-column :label="i18n('发生时间')"prop="occurredTime" :width="getColumnWidth('occurredTime', 200)">
                <template slot-scope="scope">
                  {{ scope.row.occurredTime || '--' }}
                </template>
              </el-table-column>
              <el-table-column :label="i18n('业务类型')" prop="category" :width="getColumnWidth('category', 200)">
                <template slot-scope="scope">
                  {{ getCateGory(scope.row.category) }}
                </template>
              </el-table-column>
              <el-table-column :label="i18n('积分数量')" prop="occurredPoints" :width="getColumnWidth('occurredPoints', 200)">
                <template slot-scope="scope">
                  {{ getNumber(scope.row.occurredPoints) }}
                </template>
              </el-table-column>
              <el-table-column :label="i18n('积分收入')" prop="amount" :width="getColumnWidth('amount', 200)">
                <template slot-scope="scope">
                  {{ getNumber(scope.row.amount) }}
                </template>
              </el-table-column>
              <el-table-column :label="i18n('技术服务费')" prop="serviceAmount" :width="getColumnWidth('serviceAmount', 200)">
                <template slot-scope="scope">
                  {{ getNumber(scope.row.serviceAmount) }}
                </template>
              </el-table-column>
              <el-table-column :label="i18n('结算金额')" prop="settleAmount" :width="getColumnWidth('settleAmount', 200)">
                <template slot-scope="scope">
                  {{ getNumber(scope.row.settleAmount) }}
                </template>
              </el-table-column>
              <el-table-column :label="i18n('交易号')" prop="tradeNo" :width="getColumnWidth('tradeNo', 200)">
                <template slot-scope="scope">
                  {{ scope.row.tradeNo || '--' }}
                </template>
              </el-table-column>
            </el-table>
          </el-row>
        </template>
        <template slot="page">
          <el-pagination :current-page="page.page" :page-size="page.pageSize"
                         :page-sizes="[10, 20, 30, 40]" :total="page.total"
                         @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange"
                         background
                         :layout="getPageLayout('total, prev, pager, next, sizes, jumper',page.probeEnabled)">
          </el-pagination>
        </template>
      </ListWrapper>
    <DownloadCenterDialog :dialogvisiable="fileDialogVisible" :showTip="showTip" @dialogClose="doDownloadDialogClose"></DownloadCenterDialog>

  </div>
</template>

<script lang="ts" src="./PointsSettleBill.ts">
</script>

<style lang="scss" scoped>
.prepay-card-adjust{
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: hidden;
  .current-page{
    .el-select{
      width: 100%;
    }
  }

  .el-range-editor.el-input__inner{
    width: 100%;
  }
  .el-table__body .el-table__row td {
    border-bottom: 1px solid #d7dfeb !important;
  }
}
</style>