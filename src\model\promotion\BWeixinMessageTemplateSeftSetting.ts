import BWeixinNotifyContent from "./BWeixinNotifyContent"

/*
 * @Author: 黎钰龙
 * @Date: 2023-11-28 10:50:48
 * @LastEditTime: 2024-01-03 14:54:05
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\promotion\BWeixinMessageTemplateSeftSetting.ts
 * 记得注释
 */
export default class BWeixinMessageTemplateSeftSetting {
  // uuid
  uuid: Nullable<string> = null
  // 消息类型
  type: Nullable<string> = null
  // 模板
  remark: Nullable<string> = null
  // 是否开启小程序通知
  enableAppletNotify: Nullable<boolean> = null
  // 是否开启微信公众通知
  enableOffiaccountNotify: Nullable<boolean> = null
  // 是否开启邮件通知
  enableEmailNotify: Nullable<boolean> = null
  // 小程序模板内容
  appletNotifyContent: BWeixinNotifyContent[] = []
  // 小程序消息模板id
  appletMessageTemplateId: Nullable<string> = null
  // 公众号消息模板内容
  offiaccountNotifyContent: BWeixinNotifyContent[] = []
  // 公众号消息模板id
  offiaccountMessageTemplateId: Nullable<string> = null
  // 邮件消息模板id
  emailTemplateId: Nullable<string> = null
  // 邮件消息模板内容
  emailNotifyContent: Nullable<string> = null
  // 类型名称
  typeName: Nullable<string> = null
}