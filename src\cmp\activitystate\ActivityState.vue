<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:02
 * @LastEditTime: 2023-10-17 16:52:14
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\activitystate\ActivityState.vue
 * 记得注释
-->
<template>
  <div class="payment-state">
    <div class="payment-state-state" :style="{backgroundColor: parseStateColor(state)}"></div>
    {{parseState(state)}}
  </div>
</template>

<script lang="ts" src="./ActivityState.ts">
</script>

<style lang="scss" scoped>
  .payment-state {
    display: flex;
    /*justify-content:center;*/
    align-items: center;

    .payment-state-state {
      height: 5px;
      width: 5px;
      border-radius: 10px;
      color: red;
      margin-right: 4px;
    }
  }
</style>
