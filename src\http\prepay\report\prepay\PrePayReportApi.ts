import ApiClient from 'http/ApiClient'
import PrePayAdjustData from 'model/prepay/report/prepay/trans/PrePayAdjustData'
import PrePayByDepositData from 'model/prepay/report/prepay/trade/PrePayByDepositData'
import PrePayConsumeOrRefundData from 'model/prepay/report/prepay/trans/PrePayConsumeOrRefundData'
import PrePayRechargeOrRefundData from 'model/prepay/report/prepay/trade/PrePayRechargeOrRefundData'
import PrePayReportFilter from 'model/prepay/report/prepay/PrePayReportFilter'
import PrePayReportSum from 'model/prepay/report/prepay/PrePayReportSum'
import Response from 'model/common/Response'

export default class PrePayReportApi {
  /**
   * 储值调整流水报表查询
   *
   */
  static queryAdjust(body: PrePayReportFilter): Promise<Response<PrePayAdjustData[]>> {
    return ApiClient.server().post(`/v1/prepay/report/adjust/query`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 储值调整流水报表汇总查询
   *
   */
  static queryAdjustSum(body: PrePayReportFilter): Promise<Response<PrePayReportSum>> {
    return ApiClient.server().post(`/v1/prepay/report/adjust/querySum`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 消费、消费退流水报表查询
   *
   */
  static queryConsume(body: PrePayReportFilter, refund: boolean): Promise<Response<PrePayConsumeOrRefundData[]>> {
    return ApiClient.server().post(`/v1/prepay/report/consume/query/${refund}`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 消费、消费退退流水报表汇总查询
   *
   */
  static queryConsumeSum(body: PrePayReportFilter, refund: boolean): Promise<Response<PrePayReportSum>> {
    return ApiClient.server().post(`/v1/prepay/report/consume/querySum/${refund}`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 充值、充值退流水报表查询
   *
   */
  static queryRecharge(body: PrePayReportFilter, refund: boolean): Promise<Response<PrePayRechargeOrRefundData[]>> {
    return ApiClient.server().post(`/v1/prepay/report/recharge/query/${refund}`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 充值、充值退流水按支付方式报表查询
   *
   */
  static queryRechargeDeposit(body: PrePayReportFilter, refund: boolean): Promise<Response<PrePayByDepositData[]>> {
    return ApiClient.server().post(`/v1/prepay/report/recharge/Deposit/query/${refund}`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 充值、充值退流水按支付方式报表汇总查询
   *
   */
  static queryRechargeDepositSum(body: PrePayReportFilter, refund: boolean): Promise<Response<PrePayReportSum>> {
    return ApiClient.server().post(`/v1/prepay/report/recharge/Deposit/querySum/${refund}`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 充值、充值退流水报表汇总查询
   *
   */
  static queryRechargeSum(body: PrePayReportFilter, refund: boolean): Promise<Response<PrePayReportSum>> {
    return ApiClient.server().post(`/v1/prepay/report/recharge/querySum/${refund}`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 储值金额汇总信息
   *
   */
  static summary(typeId?: string): Promise<Response<PrePayReportSum>> {
    return ApiClient.server().get(`/v1/prepay/report/summary`, {
      params: {
        typeId: typeId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   *  充值流水、充值退款流水导出
   *
   */
  static export(body: PrePayReportFilter, refund: boolean,type: string): Promise<Response<null>> {
    return ApiClient.server().post(`/v1/prepay/report/recharge/export/${refund}/${type}`, body, {
    }).then((res) => {
      return res.data
    })
  }
}
