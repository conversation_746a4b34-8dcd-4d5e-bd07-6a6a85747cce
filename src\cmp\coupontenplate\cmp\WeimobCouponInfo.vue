<template>
  <div class="weimob-coupon-info">
    <div class="baseSettingFlag">{{ i18n('微盟平台券') }}</div>
    <div class="weimob-coupon-tips">
      <div class="weimob-coupon-tips-left">
        <img src="~assets/image/icons/img_alert_info.png" alt="">
      </div>
      <div class="weimob-coupon-tips-right">
        <div class="tips-title">
          {{i18n('/营销/大转盘活动/温馨提示')}}
        </div>
        <div class="tips-desc">
          {{ i18n('券模板修改不同步，需要两个系统分别维护。尤其要注意券有效期，用券商品，用券门店，两边是否一致。有效期不一致会导致两边券状态不一致，从而导致客户损失。') }}
        </div>
      </div>
    </div>
    <div>
      <el-form ref="weimobCouponRef" label-width="120px" :model="form" :rules="rules">
        <!-- 发放渠道 -->
        <el-form-item :label="i18n('发放渠道')">
          <el-radio-group v-model="form.weimobCoupon.issueChannel" @change="issueChannelChange" :disabled="disabled">
            <el-radio label="FREE" :class="form.weimobCoupon.issueChannel === 'FREE' ? 'weimob-selected channel-block' : 'channel-block'">
              <span class="channel-text">{{ i18n('仅免费发放') }}</span>
              <el-form-item v-if="form.weimobCoupon.issueChannel === 'FREE'" prop="issueChannel" style="margin-left:22px">
                <div class="free-case">
                  <el-checkbox-group :disabled="disabled" v-model="form.weimobCoupon.freeChannels" @change="freeChannelsChange">
                    <div class="flex">
                      <el-checkbox label="directly_receive">
                        {{ i18n('直接领取') }}
                      </el-checkbox>
                      <div class="gray-tip">{{ i18n('展示在商详/购物车/领券中心等页面') }}</div>
                    </div>
                    <div class="recommend" v-if="form.weimobCoupon.freeChannels.includes('directly_receive')">
                      <div>
                        <span>{{i18n('系统推荐')}}</span>
                        <el-switch v-model="selectData.enableRecommend" active-color="#007EFF" inactive-color="#D7DFEB" style="margin:10px"
                          :disabled="selectData.enableRecommend && disabledRecommend">
                        </el-switch>
                        <span class="gray-tip" style="margin-left:0">{{i18n('开启后，到达推荐时间后将会出现在商详/购物车/领券中心等页面推荐用户领取')}}</span>
                      </div>
                      <div v-if="selectData.enableRecommend">
                        <el-form-item prop="weimobCoupon.freeChannels">
                          <span>{{i18n('推荐时间')}}</span>
                          <el-date-picker v-model="selectData.beginDate" :picker-options="pickerOptions.startTime" :placeholder="i18n('开始日期')"
                            format="yyyy-MM-dd HH:mm:ss" ref="rmdBeginDate" size="small" style="width: 190px;margin-left:10px" type="datetime"
                            :default-time="['00:00', '23:59:59']" :disabled="disabledRecommend" :clearable="false">
                          </el-date-picker>
                          {{i18n('/公用/活动/活动信息/至')}}
                          <el-date-picker v-model="selectData.endDate" :picker-options="pickerOptions.endTime" :placeholder="i18n('结束日期')"
                            format="yyyy-MM-dd HH:mm:ss" ref="rmdEndDate" size="small" style="width: 190px;" type="datetime"
                            value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00', '23:59:59']" :clearable="false">
                          </el-date-picker>
                        </el-form-item>
                      </div>
                    </div>
                    <div class="flex">
                      <el-checkbox label="merchants_issue">
                        {{ i18n('商家发券') }}
                      </el-checkbox>
                      <div class="gray-tip">{{ i18n('商家主动发券给客户') }}</div>
                    </div>
                    <div class="flex">
                      <el-checkbox label="activity_issue">
                        {{ i18n('活动发券') }}
                      </el-checkbox>
                      <div class="gray-tip">{{ i18n('允许被活动方选择发放') }}</div>
                    </div>
                    <div class="flex">
                      <el-checkbox label="qiwei_issue">
                        {{ i18n('企微助手可发券') }}
                      </el-checkbox>
                      <div class="gray-tip">{{ i18n('商户可通过企微助手发券') }}</div>
                    </div>
                  </el-checkbox-group>
                </div>
              </el-form-item>
            </el-radio>
            <br>
            <el-radio label="PAY" :class="form.weimobCoupon.issueChannel === 'PAY' ? 'weimob-selected channel-block' : 'channel-block'">
              <span class="channel-text">
                {{ i18n('付费购买') }}
              </span>
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- 券叠加其他优惠 -->
        <el-form-item :label="i18n('券叠加其他优惠')">
          <el-radio-group v-model="form.weimobCoupon.canUseDiscount.canUseWithOtherDiscount" @change="doChangePileDiscount">
            <el-radio :label="false">
              {{formatI18n('/公用/券模板/不可叠加')}}
            </el-radio>
            <el-radio :label="true">
              <span style="margin-right:10px">{{i18n('指定活动可叠加')}}</span>
              <el-form-item prop="weimobCoupon.canUseWithOtherDiscount" style="display:inline-block">
                <el-select v-if="form.weimobCoupon.canUseDiscount.canUseWithOtherDiscount"
                  v-model="form.weimobCoupon.canUseDiscount.shoppingMallDiscount" multiple clearable :placeholder="i18n('请选择优惠活动')"
                  @change="doFormChange" style="width:400px">
                  <el-option v-for="item in MallDiscount" :label="item.key" :value="item.value" :key="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- 剩余库存 -->
        <el-form-item :label="formatI18n('/营销/券礼包活动/核销第三方券', '剩余库存')" prop="total">
          <div style="color: rgb(204, 204, 204);">
            {{ formatI18n("/营销/券礼包活动/核销第三方券/不填则不限制库存") }}
          </div>
          <el-input :placeholder="formatI18n('/营销/券礼包活动/核销第三方券', '请输入')" style="width: 180px;" v-model="form.total" :disabled="disabledTotal"
            @change="totalChange">
            <template slot="append">{{
              formatI18n("/营销/券礼包活动/导出券码发券新建界面/制券数量/张")
            }}</template>
          </el-input>
        </el-form-item>
        <!-- 每人限领 -->
        <el-form-item :label="formatI18n('/公用/券模板', '每人限领')">
          <el-radio-group v-model="form.isPerLimit" @change="isPerLimitChange">
            <el-radio :label="true">{{
              formatI18n("/公用/券模板", "不限制")
            }}</el-radio>
            <el-radio :label="false">{{
              formatI18n("/公用/券模板", "限制")
            }}</el-radio>
          </el-radio-group>
          <div v-if="form.isPerLimit === false">
            {{ formatI18n("/公用/券模板", "每位用户只能领取") }}
            <el-form-item prop="weimobCoupon.perLimit" style="display: inline-block">
              <el-input v-model="form.weimobCoupon.perLimit" style="width: 160px" @change="doPerLimitChange">
                <template slot="append">{{ formatI18n("/营销/券礼包活动/券礼包活动/张") }}</template>
              </el-input>
            </el-form-item>
            {{ formatI18n("/公用/券模板", "每天可领取") }}
            <el-select v-model="form.dayLimitType" @change="dayLimitTypeChange" style="width:120px">
              <el-option value="unlimited" :label="formatI18n('/公用/券模板', '不限制数量')">
              </el-option>
              <el-option value="limited" :label="formatI18n('/公用/券模板', '限制数量')">
              </el-option>
            </el-select>
            &nbsp;
            <el-form-item prop="weimobCoupon.dayLimit" style="display: inline-block" v-if="form.dayLimitType === 'limited'">
              <el-input :placeholder="formatI18n('/营销/券礼包活动/核销第三方券', '请输入')" style="width: 160px;" v-model="form.weimobCoupon.dayLimit"
                @change="dayLimitChange">
                <template slot="append">{{
                  formatI18n("/营销/券礼包活动/券礼包活动/张")
                }}</template>
              </el-input>
            </el-form-item>
          </div>
        </el-form-item>
        <!-- 可领券时间 -->
        <el-form-item :label="i18n('可领券时间')">
          <el-radio-group v-model="form.weimobCoupon.sendTimeType" @change="doFormChange">
            <el-radio :label="1">{{i18n("不限制")}}</el-radio>
            <el-radio :label="2">{{i18n("固定时间")}}</el-radio>
          </el-radio-group>
          <template v-if="form.weimobCoupon.sendTimeType == 2">
            <el-form-item prop="useCouponTimeRange" style="display: inline-block;margin-left:16px">
              <el-date-picker @change="doFormChange" :end-placeholder="formatI18n('/公用/券模板', '结束日期')" :picker-options="dateRangeOption"
                format="yyyy-MM-dd HH:mm:ss" :range-separator="i18n('至')" size="small" :start-placeholder="formatI18n('/公用/券模板', '开始日期')"
                type="datetimerange" v-model="form.useCouponTimeRange" value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00', '23:59:59']">
              </el-date-picker>
            </el-form-item>
          </template>
        </el-form-item>
        <!-- 核销场景 -->
        <el-form-item :label="i18n('核销场景')" prop="useSceneType">
          <div style="display: flex;flex-direction:row;">
            <div>
              <el-radio-group :disabled="lockApiCheck" v-model="form.useSceneType" @change="UseSceneTtypeChange">
                <el-radio :label="true">{{ i18n('全部场景') }}</el-radio>
                <el-radio :label="false">{{ i18n('指定场景') }}</el-radio>
              </el-radio-group>
              <template v-if="!form.useSceneType">
                <el-row style="background: #F0F2F6;padding:0 8px">
                  <el-col>
                    <el-checkbox :disabled="lockApiCheck" :indeterminate="isIndeterminateUseScene" v-model="UseSceneCheckAll"
                      @change="UseSceneCheckAllChange">{{ i18n('全选') }}</el-checkbox>
                  </el-col>
                </el-row>
                <div class="use-evm">
                  <el-checkbox-group :disabled="lockApiCheck" v-model="UseScene" @change="UseSceneChange">
                    <el-checkbox :disabled="(autoCheckall || lockApiCheck) && scene.key == i18n('API核销')" v-for="scene in UseScenes"
                      :label="scene.value" :key="scene.key">{{ scene.key }}</el-checkbox>
                  </el-checkbox-group>
                </div>
              </template>
            </div>
          </div>
        </el-form-item>
        <!-- 微盟适用商品 -->
        <el-form-item :label="formatI18n('/公用/券模板', '微盟适用商品')" prop="weimobCoupon.goodsUseRule">
          <el-radio v-model="form.weimobCoupon.goodsUseRule.limitedGoods" :label="false" :disabled="!canSelectAllGoods" @change="doChangeGoods">
            {{ formatI18n("/公用/公共组件/商品范围控件/表单/全部商品") }}
            <el-tooltip class="item" effect="dark" placement="top">
              <div slot="content">
                {{formatI18n('/公用/券模板', '默认不包含自建商品,如需请切换指定商品中选择')}}
                <br />
                {{i18n('兑换券无法勾选全部商品')}}
              </div>
              <i class="el-icon-warning-outline" style="color: rgb(153, 153, 153);" />
            </el-tooltip>
          </el-radio>
          <el-radio v-model="form.weimobCoupon.goodsUseRule.limitedGoods" :label="true" @change="doChangeGoods">
            {{ formatI18n("/公用/券模板", "指定商品") }}
          </el-radio>
          <span v-if="form.weimobCoupon.goodsUseRule.limitedGoods && !form.weimobCoupon.goodsUseRule.limitedGoodsType && !goodsInfo.str"
            style="color: #20A0FF; cursor: pointer;" @click="openGoodsDiaLog">{{ formatI18n("/公用/券模板", "选择商品") }}</span>
          <span v-if="form.weimobCoupon.goodsUseRule.limitedGoods && form.weimobCoupon.goodsUseRule.limitedGoodsType && goodsInfo.str">
            <span v-if="goodsInfo.type == 'none'">{{ goodsInfo.str }}</span>
            <el-tooltip class="item" effect="light" placement="top" v-if="goodsInfo.type == 'tips'">
              <div slot="content">
                <div style="width: 100%; max-height: 300px; overflow-y: auto;">
                  <span v-for="(item, index) in goodsInfo.list" :key="index">{{ item.name }} <br /></span>
                </div>
              </div>
              <span style="border-bottom: 1px dashed rgb(182, 186, 191); cursor: pointer;">{{ goodsInfo.str }}</span>
            </el-tooltip>
            <span v-if="excludedGoodsList.length > 0">，</span>
            <el-tooltip class="item" effect="light" placement="top" v-if="excludedGoodsList.length > 0">
              <div slot="content">
                <div style="width: 100%; max-height: 300px; overflow-y: auto;">
                  <span v-for="(item, index) in excludedGoodsList" :key="index">{{ item.name }} <br /></span>
                </div>
              </div>
              <span style="border-bottom: 1px dashed rgb(182, 186, 191); cursor: pointer;">{{ formatI18n('/公用/券模板', '排除')
              }}{{ excludedGoodsList.length }}{{ formatI18n('/公用/券模板', '项商品') }}</span>
            </el-tooltip>
            <span style="color: #20A0FF; cursor: pointer; margin-left: 6px;" @click="openGoodsDiaLog">{{
              formatI18n('/公用/按钮', '修改') }}</span></span>
        </el-form-item>
        <!-- 微盟适用门店 -->
        <el-form-item :label="i18n('微盟适用门店')" prop="weimobCoupon.orgsUseRule">
          <el-radio-group @change="doStoreRange" v-model="form.weimobCoupon.limitOrgType">
            <el-radio label="ALL">{{i18n('全部门店')}}</el-radio>
            <el-radio label="PART">{{i18n('/权益/积分/积分初始化/未初始化状态/得积分规则/基础得积分/点击立即设置/指定门店适用')}}</el-radio>
          </el-radio-group>
          <span v-if="form.weimobCoupon.limitOrgType == 'PART' && weimobOrgTotal == 0" style="color: #20A0FF; cursor: pointer;"
            @click="openOrgDiaLog"> {{i18n('/公用/公共组件/门店选择弹框组件/标题/选择门店')}}</span>
          <span v-if="form.weimobCoupon.limitOrgType == 'PART' && weimobOrgTotal != 0">
            {{ getStoreCount(weimobOrgTotal) }}
            <span style="color: #20A0FF; cursor: pointer; margin-left: 6px;" @click="openOrgDiaLog">{{
              formatI18n('/公用/按钮', '修改') }}</span>
          </span>

        </el-form-item>
      </el-form>
    </div>
    <GoodsWeiMenEx ref="GoodsWeiMenExRef" :goodsUseRule="form.weimobCoupon.goodsUseRule" @change="doChangeWeiGoods" />
    <OrgsWeiMenEx ref="OrgsWeiMenExRef" :orgUseRule="form.weimobCoupon.orgsUseRule" @change="doChangeWeiOrgs" />
  </div>
</template>

<script lang="ts" src="./WeimobCouponInfo.ts"></script>

<style lang="scss" scoped>
.weimob-coupon-info {
  .flex {
    height: 32px;
    display: flex;
  }
  .weimob-coupon-tips {
    display: flex;
    padding: 12px;
    background: rgba(247, 249, 252, 0.6);
    margin-top: 12px;
    .weimob-coupon-tips-right {
      width: 95%;
      margin-left: 6px;
      .tips-title {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #36445a;
      }
      .tips-desc {
        font-size: 13px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #79879e;
        margin-top: 4px;
      }
    }
  }
  .channel-block {
    margin-top: 12px;
    padding: 15px 8px 8px;
    width: 712px;
    border: 1px solid #d7dfeb;
    border-radius: 4px;
  }
  .weimob-selected {
    border: 1px solid #007eff;
  }
  .el-radio__input.is-checked + .el-radio__label {
    color: #242633 !important;
  }
  .free-case {
    width: 800px;
    .recommend {
      font-size: 14px;
      padding-left: 25px;
      margin-bottom: 10px;
      .el-form-item__error {
        padding-left: 68px;
      }
    }
  }
  .channel-text {
    font-size: 13px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #242633;
  }
  .gray-tip {
    font-size: 13px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #79879e;
    margin-left: -22px;
  }
  .use-evm {
    display: flex;
    flex-direction: row;
    flex: 1;
    flex-wrap: wrap;
    background: #f7f9fc;
    padding: 0 8px;
  }
}
</style>
