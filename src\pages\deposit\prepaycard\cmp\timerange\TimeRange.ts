/*
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:03
 * @LastEditTime: 2023-08-09 13:38:09
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\prepaycard\cmp\timerange\TimeRange.ts
 * 记得注释
 */
import {Component, Vue, Prop} from 'vue-property-decorator'
import DateUtil from 'util/DateUtil'

@Component({
    name: 'TimeRange'
})
export default class TimeRange extends Vue {
    @Prop({
        default: false
    })
    da1qiaoPermission: boolean
    currentTab: string = ''
    datetimerange: Array<Nullable<Date>> = []

    getDateStr() {
        return this.currentTab + '  '
    }

    created() {
        this.currentTab = this.formatI18n('/公用/日期/今天')
        let now = DateUtil.nowDayTime()
        this.datetimerange = [now, now]
        this.submit()
        // this.$emit('submit', [DateUtil.format(this.datetimerange[0], 'yyyy-MM-dd'), DateUtil.format(this.datetimerange[1], 'yyyy-MM-dd')])
    }

    reset() {
        this.currentTab = this.formatI18n('/公用/日期/今天')
        this.datetimerange = []
        let now = DateUtil.nowDayTime()
        this.$emit('submit', [DateUtil.format(now, 'yyyy-MM-dd'), DateUtil.format(now, 'yyyy-MM-dd')])
    }

    radioChanged() {
        this.submit()
    }

    pickerChanged() {
        this.currentTab = this.formatI18n('/公用/日期/自定义')
        this.submit()
    }

    submit() {
        if (!this.datetimerange) {
            this.datetimerange = [null, null]
            return
        }
        if (this.currentTab === this.formatI18n('/公用/日期/自定义') && this.datetimerange.length > 0) {
            let begin = DateUtil.format(this.datetimerange[0], 'yyyy-MM-dd');
            let end = DateUtil.format(this.datetimerange[1], 'yyyy-MM-dd');
            this.$emit('submit', [begin, end])
            return
        }
        let begin = DateUtil.parseDate(DateUtil.format(new Date(), 'yyyy-MM-dd'));
        let end = DateUtil.parseDate(DateUtil.format(new Date(), 'yyyy-MM-dd'));
        if (this.currentTab === this.formatI18n('/公用/日期/昨天')) {
            begin = new Date(begin.getTime() - 86400000)
            end = new Date(end.getTime() - 86400000)
        }
        if (this.currentTab === this.formatI18n('/公用/日期/近7天')) {
            begin = new Date(begin.getTime() - 6 * 86400000)
            end = new Date(end.getTime())
        }
        if (this.currentTab === this.formatI18n('/公用/日期/近30天')) {
            begin = new Date(begin.getTime() - 29 * 86400000)
            end = new Date(end.getTime())
        }
        if (this.currentTab === this.formatI18n('/公用/日期/近90天')) {
            begin = new Date(begin.getTime() - 89 * 86400000)
            end = new Date(end.getTime())
        }
        this.datetimerange = [begin, end]
        this.$emit('submit', [DateUtil.format(this.datetimerange[0], 'yyyy-MM-dd'), DateUtil.format(this.datetimerange[1], 'yyyy-MM-dd')])
    }
}
