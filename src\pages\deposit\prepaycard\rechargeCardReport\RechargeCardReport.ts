import { Component, Vue, Watch } from 'vue-property-decorator'
import BreadCrume from 'cmp/bread-crumb/Bread<PERSON>rume.vue'
import I18nPage from "common/I18nDecorator"
import TimeRange from '../cmp/timerange/TimeRange'
import GiftCardFilter from 'model/prepay/report/card/GiftCardFilter'
import RSOrg from 'model/common/RSOrg'
import RSOrgFilter from 'model/common/RSOrgFilter'
import Org<PERSON><PERSON> from 'http/org/OrgApi'
import DataUtil from 'pages/deposit/prepaycard/common/DataUtil'
import GiftCardCardHst from 'model/prepay/report/card/GiftCardCardHst'
import CardReportSum from 'model/prepay/report/card/CardReportSum'
import reportExport from './components/reportExport'
import DownloadCenterDialog from "pages/main/dowmload_center_dialog/DownloadCenterDialog"
import RechargeCardReportApi from 'http/prepay/report/prepay/RechargeCardReportApi'
import Zone<PERSON>pi from 'http/area/ZoneApi';
import ZoneFilter from 'model/datum/zone/ZoneFilter'
import BrowserMgr from 'mgr/BrowserMgr'
import FormItem from 'cmp/formitem/FormItem'
import SelectStores from 'cmp/selectStores/SelectStores'
import ChannelManagement from "model/channel/ChannelManagement";
import Channel from "model/common/Channel";
import ChannelManagementFilter from "model/channel/ChannelManagementFilter";
import ChannelManagementApi from "http/channelmanagement/ChannelManagementApi";

@Component({
  name: 'RechargeCardReport',
  components: {
    FormItem,
    BreadCrume,
    TimeRange,
    reportExport,
    DownloadCenterDialog,
    SelectStores
  }
})

@I18nPage({
  prefix: [
    '/储值/预付卡/实体礼品卡报表',
    '/储值/预付卡/电子礼品卡报表/退卡流水',
    '/储值/预付卡/电子礼品卡报表',
    '/储值/预付卡/充值卡报表',
    '/储值/预付卡/电子礼品卡报表/售卡流水',
    '/储值/预付卡/预付卡查询/列表页面',
    '/储值/预付卡/充值卡报表/余额转出流水',
    '/储值/预付卡/电子礼品卡报表/消费流水',
    '/公用/菜单',
    '/公用/提示',
    '/公用/查询条件/提示',
    '/公用/查询条件',
    '/公用/按钮',
    '/会员/会员资料'
  ]
})

export default class RechargeCardReport extends Vue {
  refs: any
  panelArray: any = [
    {
      name: this.i18n('充值卡报表'),
      url: ''
    }
  ]
  activeName: string = '售卡流水'
  query: GiftCardFilter = new GiftCardFilter()
  expandQuery: boolean = false
  dataUtil: DataUtil = new DataUtil()
  queryData: GiftCardCardHst[] = []
  queryData2: GiftCardCardHst[] = []
  queryData3: GiftCardCardHst[] = []
  pageTotal: number = 0
  probeEnabled: null
  areaData: any = []
  ZoneFilter: ZoneFilter = new ZoneFilter()
  isMoreMarketing: boolean = false // 是否开启多营销中心 true 开启 false 不开启
  sum: CardReportSum = new CardReportSum()
  exportDialogShow: boolean = false
  fileDialogVisible: boolean = false
  showTip: boolean = false
  timeRangeDate: Date[]
  channelTypes: any
  channelEquals: any = ''
  channelMap: Map<string, ChannelManagement> = new Map<string, ChannelManagement>();


  @Watch('activeName')
  onValueChange(newVal: any, oldVal: any) {
    if( this.initFromMemberAsset ) {
      this.initFromMemberAsset = false
      return
    }
    this.doReset()
    this.expandQuery = false
  }

  private created() {
    let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
    if (sysConfig) {
      this.isMoreMarketing = sysConfig.enableMultiMarketingCenter;
    }
    this.getAreaList()
    this.getChannelList()
  }

  // watch 逻辑判断
  initFromMemberAsset: boolean = false;
  mounted() {
    // PHX-14618需求：会员资料 -> 会员资产 -> 可用预付卡 -> 明细按钮
    if (this.$route.query.from == "member-asset" && this.$route.query.code) {
      this.initFromMemberAsset = true
      this.activeName = "转出流水";
      this.$nextTick(() => {
        this.query.codeEquals = this.$route.query.code as string;
        // @ts-ignore
        this.$refs["timeRange"].currentTab = this.formatI18n("/公用/日期/近90天");
        // @ts-ignore
        this.$refs["timeRange"].radioChanged();
      });
    } else {
      this.query.occurredTimeAfterOrEqual = this.dataUtil.format(new Date(), "yyyy-MM-dd");
      this.query.occurredTimeBefore = this.dataUtil.format(new Date(), "yyyy-MM-dd");
      this.query.page = 0;
      this.query.pageSize = 10;
      this.getList();
    }
  }


  private getKey(channel: Channel) {
    if (channel && channel.type && channel.id) {
      return channel.type as any + channel.id
    }
    return channel.typeId
  }

  private getChannelList() {
    let query = new ChannelManagementFilter();
    query.page = 0
    query.pageSize = 0
    ChannelManagementApi.query(query).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.channelTypes = resp.data
        for (let channel of this.channelTypes) {
          if (channel.channel && channel.channel.type && channel.channel.id) {
            this.channelMap.set(this.getKey(channel.channel) as string, channel)
          }
        }
        console.log(this.channelMap);

      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  /**
   * 查询区域
   */
  getAreaList() {
    // this.ZoneFilter.page = 0
    // this.ZoneFilter.pageSize = 10
    ZoneApi.query(this.ZoneFilter).then((res) => {
      if (res.code === 2000) {
        this.areaData = res.data
      } else {
        this.$message.error(res.msg as string)
      }
    })
  }
  /**
   * @description 获取报表数据
   * @private
   * @memberof RechargeCardReport
   */
  private async getList() {
    this.isShowSum = false
    let resData;
    if (this.activeName === '售卡流水') {
      resData = await RechargeCardReportApi.queryCardHst(this.query)
    } else if (this.activeName === '退卡流水') {
      if (this.channelEquals) {
        this.query.channelIdEquals = this.channelMap.get(this.channelEquals)!.channel.id
        this.query.channelTypeEquals = this.channelMap.get(this.channelEquals)!.channel.type
      } else {
        this.query.channelIdEquals = null
        this.query.channelTypeEquals = null
      }
      resData = await RechargeCardReportApi.queryRefundCardHst(this.query)
    } else {
      resData = await RechargeCardReportApi.queryConsumeCardHst(this.query)
    }
    //const resData = this.activeName === '售卡流水' ? await RechargeCardReportApi.queryCardHst(this.query) : await RechargeCardReportApi.queryConsumeCardHst(this.query)
    if (resData && resData.code === 2000) {
      if (this.activeName === '售卡流水') {
        this.queryData = resData.data || []
      } else if (this.activeName === '退卡流水') {
        this.queryData3 = resData.data || []
      } else {
        this.queryData2 = resData.data || []
      }
      this.pageTotal = resData.total
      this.probeEnabled = resData.fields ? resData.fields.probeEnabled : null
      this.getSum()
    } else {
      this.$message.error(resData.msg || '获取数据异常！')
    }
  }

  /**
   * @description 获取销售数据
   * @private
   * @memberof RechargeCardReport
   */
  isShowSum: boolean = false
  private async getSum() {
    if (this.activeName === '退卡流水') {
      return;
    }
    const resData = this.activeName === '售卡流水' ? await RechargeCardReportApi.cardHstSum(this.query) : await RechargeCardReportApi.consumeCardHstSum(this.query)
    if (resData && resData.code === 2000) {
      this.sum = resData.data || new CardReportSum()
      this.isShowSum = true
    } else {
      if (resData.code === 2404) return
      this.$message.error(resData.msg || '获取数据异常！')
    }
  }

  /**
   * @description 时间tab切换
   * @private
   * @param {Date[]} dateArr
   * @memberof RechargeCardReport
   */
  private handleTimeRange(dateArr: Date[]) {
    this.query.page = 0
    this.query.pageSize = 10
    this.query.occurredTimeAfterOrEqual = dateArr[0]
    this.query.occurredTimeBefore = dateArr[1]
    this.getList()
  }

  /**
   * @description 点击查询按钮
   * @private
   * @memberof RechargeCardReport
   */
  private doSearch() {
    this.query.page = 0
    this.query.pageSize = 10
    this.getList()
  }

  /**
   * @description 点击重置按钮
   * @private
   * @memberof RechargeCardReport
   */
  private doReset() {
    this.query = new GiftCardFilter()
    this.query.page = 0
    this.query.pageSize = 10;
    this.channelEquals = '';
    (this.$refs['timeRange'] as any).reset()
  }

  /**
   * @description 分页器当前页改变
   * @private
   * @param {number} val
   * @memberof RechargeCardReport
   */
  private onHandleCurrentChange(val: number) {
    this.query.page = val - 1
    this.getList()
  }

  /**
   * @description 分页器当页条数改变
   * @private
   * @param {number} val
   * @memberof RechargeCardReport
   */
  private onHandleSizeChange(val: number) {
    this.query.pageSize = val
    this.getList()
  }

  /**
   * @description 点击批量到处按钮
   * @private
   * @memberof RechargeCardReport
   */
  private doBatchExport() {
    this.exportDialogShow = true
  }

  /**
   * @description 批量导出弹框关闭
   * @private
   * @memberof RechargeCardReport
   */
  private doDownloadDialogClose() {
    this.showTip = false
    this.fileDialogVisible = false
  }

  /**
   * @description 跳转卡模板详情
   * @private
   * @param {*} num
   * @memberof RechargeCardReport
   */
  private gotoTplDtl(num: any) {
    this.$router.push({ name: 'prepay-card-tpl-dtl', query: { number: num, cardTemplateType: 'IMPREST_CARD'}})
  }

  /**
   * @description 批量导出弹框确认导出
   * @private
   * @param {string} type
   * @param {GiftCardFilter} filter
   * @memberof RechargeCardReport
   */
  private doExportSubmit(type: string, filter: GiftCardFilter) {
    if (!type || !filter) {
      return
    }
    if (type === 'SALES_HST') {
      RechargeCardReportApi.exportSalesHst(filter).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.exportAfter()
        }
      }).catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    } else if (type === 'CONSUME_HST') {
      RechargeCardReportApi.exportConsumeHst(filter).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.exportAfter()
        }
      }).catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    } else if (type === 'REFUND_CARD_HST') {
      RechargeCardReportApi.exportRefundCard(filter).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.exportAfter()
        }
      }).catch((error: any) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    }
  }

  /**
   * @description 打开文件中心
   * @private
   * @memberof RechargeCardReport
   */
  private exportAfter() {
    this.showTip = true
    this.fileDialogVisible = true
  }
}
