<template>
    <el-dialog :before-close="doBeforeClose" :close-on-click-modal="false"
               :title="title"
               :visible.sync="dialogShow" append-to-body class="check-water-view">
        <div class="wrap">
            <el-row class="header" style="padding-left: 20px">
                <el-col :span="12">
                    <div class="primary" label="姓名">
                        姓名: {{detail.mbrName}}
                    </div>
                    <el-row class="secondary">
                        <el-col :span="12">
                            会员号: {{detail.mbrNo}}
                        </el-col>
                        <el-col :span="12">
                            手机号: {{detail.mbrMobile}}
                        </el-col>
                    </el-row>
                </el-col>
                <el-col :span="12">
                    <div class="primary">
                        <span v-if="enableMultipleAccount">{{account|idName}} &nbsp;&nbsp; </span>当前储值余额:
                        {{detail.total | amount}}
                    </div>
                    <el-row class="secondary">
                        <el-col :span="12">
                            实充余额: {{detail.balance|amount}}
                        </el-col>
                        <el-col :span="12">
                            返现余额: {{detail.giftBalance|amount}}
                        </el-col>
                    </el-row>
                </el-col>
            </el-row>


            <div class="all-total">
                <el-block-panel>
                    <el-block-panel-item>
                        <div class="height-80">
                            <p class="primary">累计充值：{{detail.totalRecharge|amount}}元</p>
                            <p class="secondary">实充增加：{{detail.realRecharge|amount}}元</p>
                            <p class="secondary">返现增加：{{detail.giftRecharge|amount}}元</p>
                        </div>
                    </el-block-panel-item>
                    <el-block-panel-item>
                        <div class="height-80">
                            <p class="primary">累计消费：{{Math.abs(detail.totalConsume)|amount}}元</p>
                            <p class="secondary">实充扣减：{{Math.abs(detail.realConsume)|amount}}元</p>
                            <p class="secondary">返现扣减：{{Math.abs(detail.giftConsume)|amount}}元</p>
                        </div>
                    </el-block-panel-item>
                    <el-block-panel-item>
                        <div class="height-80">
                            <p class="primary">累计消费退款：{{Math.abs(detail.totalRefund)|amount}}元</p>
                            <p class="secondary">实充增加：{{Math.abs(detail.realRefund)|amount}}元</p>
                            <p class="secondary">返现增加：{{Math.abs(detail.giftRefund)|amount}}元</p>
                        </div>
                    </el-block-panel-item>
                </el-block-panel>
            </div>


            <ListWrapper class="current-page">
                <template slot="list">
                    <el-table
                            :data="transactions"
                            style="margin-top: 20px">
                        <el-table-column fixed label="交易时间" prop="occurredTime" width="150">
                            <template slot-scope="scope">
                                {{scope.row.occurredTime | yyyyMMddHHmmss}}
                            </template>
                        </el-table-column>
                        <el-table-column fixed label="发生组织" prop="occurredOrg.id" width="150">
                            <template slot-scope="scope">
                                {{scope.row.occurredOrg | idName}}
                            </template>
                        </el-table-column>
                        <el-table-column fixed label="交易类型" prop="category" width="150">
                            <template slot-scope="scope">
                                {{parseCategory(scope.row.category)}}
                            </template>
                        </el-table-column>
                        <el-table-column align="right"  label="原余额(元)" prop="originalAmount" width="200">
                            <template slot-scope="scope">
                                {{scope.row.originalAmount + scope.row.originalGiftAmount|amount}}
                            </template>
                        </el-table-column>
                        <el-table-column align="right"  label="发生金额(元)" prop="totalAmount" width="200">
                            <template slot-scope="scope">
                                <span style="color: red" v-if="scope.row.totalAmount >= 0">+{{scope.row.totalAmount|amount}}</span>
                                <span style="color: green" v-if="scope.row.totalAmount < 0">{{scope.row.totalAmount|amount}}</span>
                            </template>
                        </el-table-column>
                        <el-table-column align="right"  label="发生后余额(元)" prop="amount" width="200">
                            <template slot-scope="scope">
                                {{scope.row.originalAmount + scope.row.originalGiftAmount +
                                scope.row.totalAmount|amount}}
                            </template>
                        </el-table-column>
                        <el-table-column label="交易流水号" prop="transNo" width="250"/>
                    </el-table>
                </template>
                <!--分页栏-->
                <template slot="page">
                    <el-pagination
                            :current-page="page.currentPage"
                            :page-size="page.size"
                            :page-sizes="[10, 20, 30, 40]"
                            :total="page.total"
                            @current-change="onHandleCurrentChange"
                            @size-change="onHandleSizeChange"
                            background
                            layout="total, prev, pager, next, sizes,  jumper">
                    </el-pagination>
                </template>
            </ListWrapper>
        </div>
        <div class="dialog-footer" slot="footer">
            <el-button @click="doCancel">取 消</el-button>
            <el-button @click="doModalClose" size="small" type="primary">确 定</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts" src="./CheckWater.ts">
</script>

<style lang="scss">
.check-water-view{
    display: flex;
    align-items: center;
    justify-content: center;
    .wrap{
        height: 445px;
        overflow: auto;
        .item{
            width: 228px;
            height: 108px;
            border-radius: 5px;
            display: inline-block;
            margin-bottom: 24px;
            background: rgba(0, 0, 0, 0.0470588235294118);
            margin-right: 10px;
            .content{
                text-align: center;
            }
        }
    }
    .el-dialog{
        width: 1200px !important;
        height: 600px !important;
    }
}
</style>