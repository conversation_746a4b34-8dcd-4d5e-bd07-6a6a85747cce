<template>
  <div class="cardtpl-dtl-view">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button type="primary" v-if="hasOptionPermission('/储值/预付卡/电子礼品卡活动', '活动维护') && detail.cardTemplateType === 'ONLINE_GIFT_CARD'"
          @click="createActivity">
          {{ formatI18n('/储值/预付卡/卡模板/详情页面/活动发售') }}
        </el-button>
        <el-button v-if="permission.editable(detail.cardTemplateType)" @click="copy">{{ formatI18n('/公用/按钮/复制') }}</el-button>
        <el-button v-if="permission.editable(detail.cardTemplateType)" @click="edit">{{ formatI18n('/公用/按钮/修改') }}</el-button>
      </template>
    </BreadCrume>

    <div class="current-page">
      <el-row class="header">
        <el-col :span="4">
          <el-row class="secondary">
            {{ formatI18n('/储值/预付卡/卡模板/详情页面/卡模板号：') }} {{ detail.number }}
          </el-row>
          <div class="primary">
            {{detail.name}}
          </div>
        </el-col>
        <el-col :span="4" v-if="enableMultipleAccount">
          <el-row class="secondary">
            {{ formatI18n('/储值/预付卡/卡模板/详情页面/账户类型') }}
          </el-row>
          <div class="primary">
            <span v-if="detail.accountType">[{{detail.accountType.id}}] {{detail.accountType.name}}</span>
            <span v-if="!detail.accountType">-</span>
          </div>
        </el-col>
        <el-col :span="4">
          <el-row class="secondary">
            {{ formatI18n('/储值/预付卡/卡模板/详情页面/卡类型') }}
          </el-row>
          <div class="primary">
            <span v-if="detail.cardTemplateType === 'IMPREST_CARD'">{{ formatI18n('/储值/预付卡/卡模板/公共/卡类型/充值卡') }}</span>
            <span v-if="detail.cardTemplateType === 'GIFT_CARD'">{{i18n('礼品卡')}}</span>
            <span v-if="detail.cardTemplateType === 'RECHARGEABLE_CARD'">{{
                formatI18n('/储值/预付卡/卡模板/公共/卡类型/储值卡')
              }}</span>
            <span v-if="detail.cardTemplateType === 'COUNTING_CARD'">{{
              i18n('次卡')
            }}</span>
          </div>
        </el-col>

        <el-col :span="4">
          <el-row class="secondary">{{ i18n('卡介质') }}</el-row>
          <div class="primary">
            <span v-if="detail.cardMedium === 'online'">{{ i18n('电子卡') }}</span>
            <span v-if="detail.cardMedium === 'bar'">{{i18n('条码卡')}}</span>
            <span v-if="detail.cardMedium === 'mag'">{{i18n('磁条卡')}}</span>
            <span v-if="detail.cardMedium === 'rfic'">{{i18n('rfic卡')}}</span>
            <span v-if="detail.cardMedium === 'ic'">{{i18n('ic卡')}}</span>
          </div>
        </el-col>
      </el-row>

      <div class="split"></div>

      <div class="panel">
        <div class="header">
          {{ formatI18n('/储值/预付卡/卡模板/详情页面/卡面信息与使用规则') }}
        </div>
        <div class="content">
          <el-row>
            <el-col :span="2" class="label">{{ formatI18n('/储值/预付卡/卡模板/详情页面/卡样：') }}</el-col>
            <el-col :span="18" v-if="!detail.cardPictureUrls || detail.cardPictureUrls.length  === 0">
              {{ formatI18n('/储值/预付卡/卡模板/详情页面/未设置卡样') }}
            </el-col>
            <el-col :span="18" v-if="detail.cardPictureUrls && detail.cardPictureUrls.length  > 0">
              <CardPicList :picList="detail.cardPictureUrls" :readonly="true" />
            </el-col>
          </el-row>

          <el-row v-if="detail.cardCodeLength && isShowLength">
            <el-col :span="2" class="label">{{ i18n('卡号长度') + '：' }}</el-col>
            <el-col :span="18">
              {{detail.cardCodeLength}}
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="2" class="label">{{ i18n('卡号规则') + '：' }}</el-col>
            <el-col :span="18">
              {{detail.codePrefix || '--'}}
            </el-col>
          </el-row>

          <el-row>
            <template v-if="detail.cardTemplateType === 'COUNTING_CARD'">
              <el-col :span="2" class="label">{{ i18n('次数') }}：</el-col>
              <el-col :span="18">
                {{detail.count + i18n('次')}}
              </el-col>
            </template>
            <template v-else-if="detail.faceAmounts">
              <el-col :span="2" class="label">{{ formatI18n('/储值/预付卡/卡模板/详情页面/卡面额：') }}</el-col>
              <el-col :span="18">
                <div class="faceAmount" v-for="(amount,index) of detail.faceAmounts" :key="index">
                  {{ formatI18n('/储值/预付卡/卡模板/详情页面/{0}元', null, [amount]) }}
                </div>
              </el-col>
            </template>
          </el-row>

          <el-row>
            <el-col :span="2" class="label">{{ i18n('价格') + '：' }}</el-col>
            <el-col :span="18">
              <span class="text">
                <span>{{ detail.price + i18n('元') }}</span>
              </span>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="2" class="label">{{ formatI18n('/储值/预付卡/卡模板/详情页面/有效期：') }}</el-col>
            <el-col :span="18">
              <span class="text">
                <span>{{ validatyInfo(this.detail) }}</span>
              </span>
            </el-col>
          </el-row>
          <el-row v-if="detail.extInfo && detail.extInfo.threshold">
            <el-col :span="2" class="label">{{ i18n('用卡门槛') + '：' }}</el-col>
            <el-col :span="18">
              <span v-if="detail.extInfo.threshold.thresholdType === 'NONREUSEABLE'">
                {{i18n('用卡商品满')}}
                {{detail.extInfo.threshold.threshold}}
                {{i18n('元及以上可用')}}
              </span>
              <span v-else-if="detail.extInfo.threshold.thresholdType === 'NONE'">
                {{i18n('无门槛')}}
              </span>
              <span v-else>--</span>
            </el-col>
          </el-row>
          <el-row v-if="detail.cardTemplateType == 'COUNTING_CARD' && detail.countingCardGoodsGroups[0]">
            <el-col :span="2" class="label">{{ i18n('适用商品：') }}</el-col>
            <el-col :span="18">
              <div>
                <span>
                  {{formatI18n("/资料/门店", "共") + detail.countingCardGoodsGroups[0].exchangeGoods.length + formatI18n("/资料/员工", "项")}}
                </span>
                <span style="margin: 0 10px">
                  {{ detail.countingCardGoodsGroups[0].exchangeGoods.length + formatI18n("/公用/券模板/提货券/用券商品/选") + detail.countingCardGoodsGroups[0].exchangeQty}}
                </span>
                <span v-if="detail.countingCardGoodsGroups[0].enableExchangeSameGood">{{i18n('可重复选')}}</span>
                <span v-else>{{i18n('不可重复选')}}</span>
              </div>
              <el-table :data="detail.countingCardGoodsGroups[0].exchangeGoods" style="width: 800px">
                <el-table-column :label="formatI18n('/营销/积分活动/积分活动/单品满数量加送积分活动/编辑页面/积分加送规则', '商品')" fixed prop="goods.name">
                  <template slot-scope="scope">
                    <div :title="scope.row.goods.name">{{ scope.row.goods.name }}</div>
                  </template>
                </el-table-column>
                <el-table-column :label="formatI18n('/公用/券模板/提货券/用券商品', '售价(元)')" fixed prop="price">
                  <template slot-scope="scope">
                    {{ scope.row.price | fmt }}
                  </template>
                </el-table-column>
                <el-table-column :label="formatI18n('/公用/券模板/提货券/用券商品', '商品数量')" fixed prop="qty">
                  <template slot-scope="scope">
                    {{ scope.row.qty }}&nbsp;{{ scope.row.isDisp ? formatI18n("/公用/券模板/单品折扣券/用券门槛/千克") : formatI18n("/公用/券模板/单品折扣券/用券门槛/件") }}
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
          <el-row v-else-if="detail.cardTemplateType !== 'IMPREST_CARD'">
            <el-col :span="2" class="label">{{ formatI18n('/储值/预付卡/卡模板/详情页面/适用商品：') }}</el-col>
            <el-col :span="18">
              <GoodsScopeDtl :goods="detail.goods" :goodsMatchRuleMode="goodsMatchRuleMode" />
            </el-col>
          </el-row>

          <el-row v-if="detail.cardTemplateType !== 'IMPREST_CARD'" style="margin-top: 15px">
            <el-col :span="2" class="label">{{ formatI18n('/储值/预付卡/卡模板/详情页面/适用门店：') }}</el-col>
            <el-col :span="18">
              <ActiveStoreDtl style="width: 60%;min-width: 800px;" :data="detail.stores"></ActiveStoreDtl>
            </el-col>
          </el-row>

          <el-row style="margin-top: 15px" v-if="isGiftOrRechargeable">
            <el-col :span="2" class="label">{{ formatI18n('/公用/券模板/叠加优惠') + '：' }}</el-col>
            <el-col :span="18">
              <div class="item-height">
                {{ getCardPromotionInfo(detail) }}
                <span @click.stop="openPromotionShow" class="span-btn" v-if="detail.promotionSuperpositionType == 'PART'">
                  {{ i18n('/公用/券模板详情/查看促销单') }}
                </span>
              </div>
            </el-col>
          </el-row>

          <el-row v-if="isGiftOrRechargeable">
            <el-col :span="2" class="label">{{ formatI18n('/公用/券模板/微盟适用商品/会员价') + '：' }}</el-col>
            <el-col :span="18">
              {{ memberPriceFav ? formatI18n('/公用/券模板/叠加') : formatI18n('/公用/券模板/不叠加') }}
            </el-col>  
          </el-row>
          <el-row v-if="isGiftOrRechargeable">
            <el-col :span="2" class="label">{{ formatI18n('/公用/券模板/人工折扣') + '：' }}</el-col>
            <el-col :span="18">
              {{ manualDiscountFav ? formatI18n('/公用/券模板/叠加') : formatI18n('/公用/券模板/不叠加') }}
            </el-col>  
          </el-row>
          <el-row v-if="isGiftOrRechargeable">
            <el-col :span="2" class="label">{{ formatI18n('/公用/券模板/其他优惠') + '：' }}</el-col>
            <el-col :span="18">
              {{ otherDiscountFav ? formatI18n('/公用/券模板/叠加') : formatI18n('/公用/券模板/不叠加') }}
            </el-col>  
          </el-row>

          <el-row  v-if="isGiftOrRechargeable">
            <el-col :span="2" class="label">{{ formatI18n('/卡模板/支付是否得积分') + '：' }}</el-col>
            <el-col :span="18">
              {{ detail.payObtainPoints ? formatI18n('/公用/券模板/是') : formatI18n('/公用/券模板/否') }}
            </el-col>  
          </el-row>

          <el-row>
            <el-col :span="2" class="label">{{ formatI18n('/储值/卡模板/是否允许绑为会员卡') + '：' }}</el-col>
            <el-col :span="18">
              {{ detail.allowBind ? formatI18n('/设置/系统设置/能') : formatI18n('/设置/系统设置/不能') }}
            </el-col>  
          </el-row>
          <el-row v-if="isNeedPayPwd">
            <el-col :span="2" class="label">{{ formatI18n('/储值/预付卡/卡模板/详情页面/支付时是否需要密码：') }}</el-col>
            <el-col :span="18">
              {{ detail.enablePayPassword ? formatI18n('/储值/预付卡/卡模板/详情页面/需要') : formatI18n('/储值/预付卡/卡模板/详情页面/不需要') }}
            </el-col>
          </el-row>

          <el-row v-if="isNeedTransPwd">
            <el-col :span="2" class="label">{{ formatI18n('/储值/预付卡/卡模板/详情页面/转出时是否需要密码：') }}</el-col>
            <el-col :span="18">
              {{
                detail.enableTransferOutPassword ? formatI18n('/储值/预付卡/卡模板/详情页面/需要') : formatI18n('/储值/预付卡/卡模板/详情页面/不需要')
              }}
            </el-col>
          </el-row>

          <el-row v-if="['RECHARGEABLE_CARD'].indexOf(detail.cardTemplateType) > -1">
            <el-col :span="2" class="label">{{ formatI18n('/储值/预付卡/卡模板/详情页面/是否一次性消费') }}</el-col>
            <el-col :span="18">
              {{
                detail.enableOneTimeConsume ? formatI18n('/储值/预付卡/卡模板/详情页面/是') : formatI18n('/储值/预付卡/卡模板/详情页面/否')
              }}
            </el-col>
          </el-row>

          <el-row v-if="['RECHARGEABLE_CARD','GIFT_CARD'].indexOf(detail.cardTemplateType) > -1">
            <el-col :span="2" class="label">{{ i18n('整单支付是否享受会员价') }}</el-col>
            <el-col :span="18">
              {{
                detail.enjoyMembershipPrice ? formatI18n('/储值/预付卡/卡模板/详情页面/是') : formatI18n('/储值/预付卡/卡模板/详情页面/否')
              }}
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="2" class="label">{{ formatI18n('/储值/预付卡/卡模板/详情页面/使用须知：') }}</el-col>
            <el-col :span="18">
              <div style="line-height: 20px;margin-top: 14px;" no-i18n v-html="detail.remark ? detail.remark.replace(/\n/g,'<br/>'): '--'"></div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
    <!-- 查看促销单弹窗 -->
    <PromotionShowDialog ref="promotionShow" billType="card" :templateId="detail && detail.number">
    </PromotionShowDialog>
  </div>
</template>

<script lang="ts" src="./PrepayCardTplDtl.ts">
</script>

<style lang="scss" scoped>
.cardtpl-dtl-view {
  width: 100%;
  height: 100%;
  background-color: white;
  overflow: auto;

  .current-page {
    height: calc(100% - 80px) !important;
    overflow-y: auto;
    .header {
      padding: 30px 20px 30px 20px;
      .primary {
        font-weight: 500;
        font-style: normal;
        font-size: 20px;
        color: #515151;
      }
      .secondary {
        color: rgba(51, 51, 51, 0.647058823529412);
      }
    }
    .panel {
      .header {
        font-weight: 500;
        padding: 20px;
        font-size: 18px;
      }
      .content {
        padding: 20px;
        font-size: 14px;
        color: #606266;
        line-height: 50px;

        .text {
          line-height: 35px;
        }

        .label {
          min-width: 150px;
        }

        .faceAmount {
          background-color: #f9f9f9;
          padding: 0px 30px;
          float: left;
          margin-right: 15px;
          margin-top: 8px;
          border: 1px solid #e8e8e8;
          line-height: 35px;
          height: 35px;
        }
        .goods {
          border: 1px solid #f0f0f0;
          width: 100%;
          .goods-header {
            padding: 5px;
            height: 35px;
            line-height: 25px;
            background-color: #e6e6e6;
          }
          .goods-content {
            padding: 5px;
          }
        }
      }
    }
  }

  .split {
    height: 20px;
    background-color: #eeeff1;
  }
}
</style>
