import {Component, Vue} from 'vue-property-decorator'
import ListWrapper from 'cmp/list/ListWrapper.vue'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import FloatBlock from 'cmp/floatblock/FloatBlock.vue'
import DateUtil from 'util/DateUtil'
import MemberBalancePromotionActivityFilter from 'model/payment/member/MemberBalancePromotionActivityFilter'
import MemberBalancePromotionActivity from 'model/payment/member/MemberBalancePromotionActivity'
import MemberBalancePromotionApi from 'http/payment/member/MemberBalancePromotionApi'
import ActivityTopicApi from 'http/v2/controller/points/topic/ActivityTopicApi'
import ActivityTopic from 'model/v2/controller/points/topic/ActivityTopic'
import EditType from 'common/EditType'
import I18nPage from 'common/I18nDecorator'
import MemberBalancePromotionPermissions from "./MemberBalancePromotionPermissions";
import AbstractOaActivity from 'cmp/abstract-oa-activity/AbstractOaActivity'
import ActivityMgr from 'mgr/ActivityMgr'
import { MarketBudgetActivityEnum } from 'model/promotion/MarketBudgetActivityEnum'
import ActivityStateTag from 'cmp/activity-state-tag/ActivityStateTag'

@Component({
  name: 'MemberBalancePromotionList',
  components: {
    ListWrapper,
    SubHeader,
    BreadCrume,
    FloatBlock,
    ActivityStateTag
  }
})
@I18nPage({
  prefix: [
    '/储值/会员储值/储值支付活动/列表页面', '/公用/按钮', '/公用/提示'
  ],
})
export default class MemberBalancePromotionList extends AbstractOaActivity {
  i18n: I18nFunc
  query: MemberBalancePromotionActivityFilter = new MemberBalancePromotionActivityFilter()
  queryData: MemberBalancePromotionActivity[] = []
  tabName: string = 'first'
  $refs: any
  checkedAll: boolean = false
  selected: MemberBalancePromotionActivity[] = []
  permission = new MemberBalancePromotionPermissions()

  panelArray: any

  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  themes: ActivityTopic[] = []
  get activityTypes() {
    return [
      {
        code: null,
        label: this.i18n('全部')
      },
      {
        code: 'MemberBalanceReductionActivityRule',
        label: this.i18n('储值支付立减'),
        term: this.permission.viewable
      },
      {
        code: 'MemberBalanceDiscountActivityRule',
        label: this.i18n('储值支付折扣'),
        term: this.permission.discountViewable
      },
    ]
  }
  activityTypeWidth: number = 150
  activityOptWidth: number = 150

  get activityTitle() {
    return this.i18n('活动号')+'/'+this.i18n('活动名称')
  }

  // 储值支付折扣 OA权限
  get isOaActivityDiscount() {
    return ActivityMgr.isOaActivity(MarketBudgetActivityEnum.MemberBalanceDiscountActivityRule)
  }

  // 储值支付立减 OA权限
  get isOaActivityBalance() {
    return ActivityMgr.isOaActivity(MarketBudgetActivityEnum.MemberBalanceReductionActivityRule)
  }

  get isOaActivityAny() {
    return this.isOaActivityDiscount || this.isOaActivityBalance
  }

  created() {
    this.panelArray = [
      {
        name: this.i18n('储值支付优惠'),
        url: ''
      }
    ]
    let lang = sessionStorage.getItem('locale');
    if (lang === 'zh_CN') {
      this.activityTypeWidth = 150;
      this.activityOptWidth = 200;
    } else {
      this.activityTypeWidth = 340;
      this.activityOptWidth = 240;
    }
    this.getList()
    this.getTheme()
  }

  doSearch() {
    this.page.currentPage = 1
    this.getList()
  }

  doReset() {
    this.query = new MemberBalancePromotionActivityFilter()
    this.tabName = 'first'
    this.page.currentPage = 1
    this.getList()
  }

  /**
   * 查询
   */
  onSearch() {
    this.page.currentPage = 1
    this.getList()
  }

  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.getList()
  }

  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.getList()
  }

  /**
   * 表格排序条件
   */
  onSortChange({column, prop, order}: any) {
    // todo
  }

  private getList() {
    if(this.permission.viewable && !this.permission.discountViewable) {
      this.query.activityTypeEquals = 'MemberBalanceReductionActivityRule' // 只查立减
    } else if (!this.permission.viewable && this.permission.discountViewable) {
      this.query.activityTypeEquals = 'MemberBalanceDiscountActivityRule'  // 只查折扣
    }
    this.query.page = this.page.currentPage - 1
    this.query.pageSize = this.page.size
    MemberBalancePromotionApi.query(this.query).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.handleSumAmount(resp.data.countResult)
        this.queryData = resp.data.result
        this.page.total = resp.data.total
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private add() {
    this.$router.push({name: 'member-balance-promotion-edit'})
  }

  private gotoDtl(row: any) {
    if (row.type === '储值支付折扣') {
      this.$router.push({name: 'member-balance-promotion-discount-dtl', query: {activityId: row.body.activityId}})
    }
    if (row.type === '储值支付立减') {
      this.$router.push({name: 'member-balance-promotion-dtl', query: {activityId: row.body.activityId}})
    }
    
  }

  private del(activityId: string) {
    this.$alert(this.i18n('确认要删除吗？'), this.i18n('提示'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消'),
      type: 'warning',
      callback: (action) => {
        if (action === 'confirm') {
          MemberBalancePromotionApi.remove(activityId).then((resp: any) => {
            if (resp && resp.code === 2000) {
              this.$message.success(this.i18n('删除成功'))
              this.getList()
            }
          }).catch((error) => {
            if (error && error.message) {
              this.$message.error(error.message)
            }
          })
        }
      }
    });
  }

  private delBatch() {
    if (this.selectedActivityIdList.length === 0) {
      this.$message.error(this.i18n('请先勾选要删除的记录'))
      return
    }
    this.$alert(this.i18n('确认要删除吗？'), this.i18n('提示'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消'),
      type: 'warning',
      callback: (action) => {
        if (action === 'confirm') {
          MemberBalancePromotionApi.batchRemove(this.selectedActivityIdList as any).then((resp: any) => {
            if (resp && resp.code === 2000) {
              this.$message.success(resp.data)
              this.getList()
            }
          }).catch((error) => {
            if (error && error.message) {
              this.$message.error(error.message)
            }
          })
        }
      }
    });
  }

  private audit(activityId: string) {
    this.$alert(this.i18n('确认要审核吗？'), this.i18n('提示'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消'),
      type: 'warning',
      callback: (action) => {
        if (action === 'confirm') {
          MemberBalancePromotionApi.audit(activityId).then((resp: any) => {
            if (resp && resp.code === 2000) {
              this.$message.success(this.i18n('审核成功'))
              this.getList()
            } else {
              throw new Error(resp.msg)
            }
          }).catch((error) => {
            if (error && error.message) {
              this.$message.error(error.message)
            }
          })
        }
      }
    });
  }

  private auditBatch() {
    if (this.selectedActivityIdList.length === 0) {
      this.$message.error(this.i18n('请先勾选要审核的记录'))
      return
    }
    this.$alert(this.i18n('确认要审核吗？'), this.i18n('提示'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消'),
      type: 'warning',
      callback: (action) => {
        if (action === 'confirm') {
          MemberBalancePromotionApi.batchAudit(this.selectedActivityIdList as any).then((resp: any) => {
            if (resp && resp.code === 2000) {
              this.$message.success(resp.data)
              this.getList()
            }
          }).catch((error) => {
            if (error && error.message) {
              this.$message.error(error.message)
            }
          })
        }
      }
    });
  }

  private stop(activityId: string) {
    this.$alert(this.i18n('确认要停止吗？'), this.i18n('提示'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消'),
      type: 'warning',
      callback: (action) => {
        if (action === 'confirm') {
          MemberBalancePromotionApi.stop(activityId).then((resp: any) => {
            if (resp && resp.code === 2000) {
              this.$message.success(this.i18n('停止成功'))
              this.getList()
            }
          }).catch((error) => {
            if (error && error.message) {
              this.$message.error(error.message)
            }
          })
        }
      }
    });
  }

  private stopBatch() {
    if (this.selectedActivityIdList.length === 0) {
      this.$message.error(this.i18n('请先勾选要停止的记录'))
      return
    }
    this.$alert(this.i18n('确认要停止吗？'), this.i18n('提示'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消'),
      type: 'warning',
      callback: (action) => {
        if (action === 'confirm') {
          MemberBalancePromotionApi.batchStop(this.selectedActivityIdList as any).then((resp: any) => {
            if (resp && resp.code === 2000) {
              this.$message.success(resp.data)
              this.getList()
            }
          }).catch((error) => {
            if (error && error.message) {
              this.$message.error(error.message)
            }
          })
        }
      }
    });
  }

  private createMbp() {
    this.$router.push({name: 'member-balance-promotion-edit', query: {editType: EditType.CREATE}})
  }

  private createMbpd() {
    this.$router.push({name: 'member-balance-promotion-discount-edit', query: {editType: EditType.CREATE}})
  }
  private copy(activityId: string, type: string) {
    console.log(type);
    
    if (type === 'MEMBER_BALANCE_REDUCTION' ) {
      this.$router.push({
        name: 'member-balance-promotion-edit',
        query: {activityId: activityId, editType: EditType.COPY}
      })
    }
    if (type === 'MEMBER_BALANCE_DISCOUNT') {
      this.$router.push({
        name: 'member-balance-promotion-discount-edit',
        query: {activityId: activityId, editType: EditType.COPY}
      })
    }
  }

  private edit(activityId: string, type: string) {
    if (type === 'MEMBER_BALANCE_REDUCTION') {
      this.$router.push({
        name: 'member-balance-promotion-edit',
        query: {activityId: activityId, editType: EditType.EDIT}
      })
    }
    if (type === 'MEMBER_BALANCE_DISCOUNT') {
      this.$router.push({
        name: 'member-balance-promotion-discount-edit',
        query: {activityId: activityId, editType: EditType.EDIT}
      })
    }
  }

  private checkedAllRow() {
    if (this.checkedAll) {
      for (let row of this.queryData) {
        this.$refs.table.toggleRowSelection(row, true)
      }
    } else {
      this.$refs.table.clearSelection();
    }
  }

  private handleSelectionChange(val: any) {
    this.selected = val
  }

  get selectedActivityIdList() {
    return this.selected.map((e) => e.body ? e.body.activityId : null)
  }

  private activityTime(row: any) {
    return `${DateUtil.format(row.body.beginDate, 'yyyy-MM-dd')}${this.i18n('至')}${DateUtil.format(row.body.endDate, 'yyyy-MM-dd')}`
  }

  private handleTabClick(tab: any, event: any) {
    this.query.stateEquals = this.computeStateEquals(tab.name)
    this.doSearch()
  }

  private getTheme() {
    ActivityTopicApi.listTopic().then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.themes = resp.data
      }
    })
  }
}
