/*
 * @Autor: 司浩
 * @Description:
 * @Date: 2021-08-12 09:59:33
 * @LastEditors: 司浩
 * @LastEditTime: 2023-03-09 16:26:39
 * @FilePath: \phoenix-web-ui\src\http\analysis\DataAnalysisV2Api.ts
 */
import ApiClient from 'http/ApiClient'
import DataAnalysisFilter from 'model/analysis/v2/DataAnalysisFilter'
import MbrConsumeStats from 'model/analysis/v2/MbrConsumeStats'
import MbrIncreaseStats from 'model/analysis/v2/MbrIncreaseStats'
import MemberIncreaseData from 'model/analysis/v2/MemberIncreaseData'
import MemberStats from 'model/analysis/v2/MemberStats'
import MemberTradeStats from 'model/analysis/v2/MemberTradeStats'
import Response from 'model/common/Response'
import TradeIncreaseData from 'model/analysis/v2/TradeIncreaseData'
import OrgCycleReportFilter from 'model/analysis/v2/OrgCycleReportFilter'
import OrgCycleData from 'model/analysis/v2/OrgCycleData'
import OrgCycleStats from 'model/analysis/v2/OrgCycleStats'

export default class DataAnalysisV2Api {
  /**
   * 会员门店增长概况
   * 会员门店增长概况
   *
   */
  static queryMemberIncrease(
    body: DataAnalysisFilter
  ): Promise<Response<MemberIncreaseData[]>> {
    return ApiClient.server()
      .post(`/v1/data-analysis/v2/queryMemberIncrease`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 门店会员交易概况-门店交易报表
   * 门店会员交易概况-门店交易报表
   *
   */
  static queryTradeIncrease(
    body: DataAnalysisFilter
  ): Promise<Response<TradeIncreaseData[]>> {
    return ApiClient.server()
      .post(`/v1/data-analysis/v2/queryTradeIncrease`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 门店会员交易概况-图表
   * 门店会员交易概况-图表
   *
   */
  static queryTradeIncreaseStats(
    body: DataAnalysisFilter
  ): Promise<Response<MemberTradeStats>> {
    return ApiClient.server()
      .post(`/v1/data-analysis/v2/queryTradeIncreaseStats`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 门店会员增长概况-累计会员信息
   * 门店会员增长概况-累计会员信息
   *
   */
  static statsMember(): Promise<Response<MemberStats>> {
    return ApiClient.server()
      .get(`/v1/data-analysis/v2/statsMember`, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 会员消费信息合计
   * 会员消费信息合计
   *
   */
  static statsMemberConsume(
    body: DataAnalysisFilter
  ): Promise<Response<MbrConsumeStats>> {
    return ApiClient.server()
      .post(`/v1/data-analysis/v2/stats/member-consume`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 首页数据分析-会员增长情况
   * 首页数据分析-会员增长情况
   *
   */
  static statsMemberIncrease(
    body: DataAnalysisFilter
  ): Promise<Response<MbrIncreaseStats>> {
    return ApiClient.server()
      .post(`/v1/data-analysis/v2/stats/member-increase`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 首页数据分析-会员增长情况
   * 首页数据分析-会员增长情况
   *
   */
  static homeStatsMemberIncrease(
      body: DataAnalysisFilter
  ): Promise<Response<MbrIncreaseStats>> {
    return ApiClient.server()
        .post(`/v1/data-analysis/v2/home/<USER>/member-increase`, body, {})
        .then((res) => {
          return res.data
        })
  }

  /**
   * 会员交易概况
   * 会员交易概况
   *
   */
  static statsMemberTradeAmount(
    body: DataAnalysisFilter
  ): Promise<Response<MemberTradeStats>> {
    return ApiClient.server()
      .post(`/v1/data-analysis/v2/stats/member-trade`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 首页数据分析-会员交易概况
   * 首页数据分析-会员交易概况
   *
   */
  static homeStatsMemberTradeAmount(
      body: DataAnalysisFilter
  ): Promise<Response<MemberTradeStats>> {
    return ApiClient.server()
        .post(`/v1/data-analysis/v2/home/<USER>/member-trade`, body, {})
        .then((res) => {
          return res.data
        })
  }

  /**
   * 查询交易组织周期报表
   * 查询交易组织周期报表
   *
   */
  static queryOrgCycleReport(
    body: OrgCycleReportFilter
  ): Promise<Response<OrgCycleData[]>> {
    return ApiClient.server()
      .post(`/v1/data-analysis/v2/queryOrgCycleReport`, body, {})
      .then((res) => {
        return res.data
      })
  }

  /**
   * 交易组织周期报表分析
   * 交易组织周期报表分析
   *
   */
  static statsOrgCycle(
    body: OrgCycleReportFilter
  ): Promise<Response<OrgCycleStats>> {
    return ApiClient.server()
      .post(`/v1/data-analysis/v2/stats/org-cycle`, body, {})
      .then((res) => {
        return res.data
      })
  }
}
