import { Component, Prop, Vue, Emit, Watch } from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import EnvUtil from 'util/EnvUtil'
import RSOrgFilter from 'model/common/RSOrgFilter'
import RSOrg from 'model/common/RSOrg'
import AllModifyStore from 'model/member_standard/AllModifyStore'
import MemberApi from 'http/member_standard/MemberApi'
import SelectStores from 'cmp/selectStores/SelectStores'
import UploadApi from "http/upload/UploadApi";

interface RadioForm {
  radio: string
}

@Component({
	name: "ModifyStoreDialog",
	components: {
		FormItem,
    SelectStores
	},
})
export default class ModifyStoreDialog extends Vue {
	private get belongingStore(): string {
		if (location.href.indexOf("localhost") === -1) {
			return "template_belonging_store.xlsx";
		} else {
			return "template_belonging_store.xlsx";
		}
	}

	private get getLimitInfo(): string {
		let str: string = this.formatI18n("/公用/导入", "为保障上传成功，建议每次最多上传{0}条信息");
		str = str.replace(/\{0\}/g, "5000");
		return str;
	}

	@Prop({ type: Boolean, default: false }) readonly dialogShow!: boolean;
	@Prop({ type: Array, default: [] }) readonly stores!: RSOrg[];
	@Prop({ type: Boolean, default: false }) readonly isMarketCenter!: boolean;

	adjustmentResults: string  = "";
	radioForm: RadioForm = {
		radio: "1",
	};
	storeForm: AllModifyStore = {
		sourceStoreId: "",
		targetStoreId: "",
		remark: "",
	};
	rules = {
		sourceStoreId: [{ required: true, message: this.formatI18n("/会员/会员资料", "请选择门店全量会员"), trigger: "change" }],
		targetStoreId: [{ required: true, message: this.formatI18n("/会员/会员资料", "请选择归属新门店"), trigger: "change" }],
	};
	query: RSOrgFilter = new RSOrgFilter();
	$refs: any;
	getUploadUrl: string = EnvUtil.getServiceUrl() + `v1/member/importChangeStore`;
	fileCount: number = 0;
	uploadHeaders: any = {};
	excelFile: any = "";
	allStore: RSOrg[] = [];

	@Watch("stores", { immediate: true, deep: true })
	handelValChange(val: RSOrg[]) {
		if (val[0] && this.allStore.length === 0) {
			this.allStore = val;
			this.storeForm.sourceStoreId = val[0].org ? (val[0].org.id as string) : "";
			this.storeForm.targetStoreId = val[0].org ? (val[0].org.id as string) : "";
		}
	}

	@Emit("dialogClose")
	doBeforeClose() {
		this.resetStatus();
	}

	private created() {
		let path = 'template_adjustment_results.xlsx'
		UploadApi.getUrl(path).then((resp: any) => {
			if (resp && resp.data) {
				this.adjustmentResults = resp.data;
				this.getExecFile()
			}
		}).catch((error) => {
			if (error && error.message) {
				this.$message.error(error.message)
			}
		})

	}

	getExecFile() {
		if (this.isMarketCenter === true) {
			this.getUploadUrl = EnvUtil.getServiceUrl() + `v1/marketingCenterMember/importChangeStore`;
		}
		let locale: string = sessionStorage.getItem("locale") || "";
		this.uploadHeaders = {
			locale: locale ? locale : "zh_CN",
			time_zone: new Date().getTimezoneOffset(),
			marketingCenter: sessionStorage.getItem("marketCenter"),
		};
		const authorization = EnvUtil.getUniAuthorization()
		if (authorization) {
			this.uploadHeaders.authorization = authorization
		}
		this.readTextFile(this.adjustmentResults, (excelFile: any) => {
			this.excelFile = excelFile;
		});
	}

	private readTextFile(filePath: string, callback: any) {
		const xhrFile = new XMLHttpRequest();
		xhrFile.open("GET", filePath, true);
		xhrFile.responseType = "blob";
		xhrFile.onload = function() {
			const allText = xhrFile.response;
			callback(allText);
		};
		xhrFile.send();
	}

	private doModalClose(type: string) {
		if (type === "confirm") {
			if (this.radioForm.radio === "1") {
				if (this.fileCount > 0) {
					this.$refs.upload.submit();
				} else {
					this.$message.warning(this.formatI18n("/公用/导入/请先选择文件") as any);
				}
			} else {
				this.$refs["storeForm"].validate((valid: any) => {
					if (valid) {
						let formdata: FormData = new FormData();
						formdata.append("file", this.excelFile, "adjustmentResults.xlsx");
						formdata.append("sourceStoreId", this.storeForm.sourceStoreId);
						formdata.append("targetStoreId", this.storeForm.targetStoreId);
						formdata.append("remark", this.storeForm.remark);
						MemberApi.allModifyStore(formdata)
							.then((resp: any) => {
								if (resp && resp.code === 2000) {
									this.resetStatus();
									this.$emit("dialogClose");
									this.$emit("upload-success");
								}
							})
							.catch((error) => {
								if (error && error.message) {
									this.$message.error(error.message);
								}
							});
					}
				});
			}
		} else {
			this.resetStatus();
			this.$emit("dialogClose");
		}
	}

	private doHandleChange(file: any, fileList: any) {
		if (fileList.length > 0) {
			this.fileCount++;
		}
	}

	private getErrorInfo(a: any, b: any, c: any) {
		this.$message.error(this.formatI18n("/公用/导入", "导入失败，请重新导入"));
		this.fileCount = 0;
		this.$refs.upload.clearFiles();
	}

	private getSuccessInfo(a: any, b: any, c: any) {
		if (a && a.code === 2000) {
			this.$refs.upload.clearFiles();
			this.fileCount = 0;
			this.resetStatus();
			this.$emit("dialogClose");
			this.$emit("upload-success");
		} else {
			this.$message.error(a.msg);
		}
	}

	private resetStatus() {
		this.storeForm.sourceStoreId = this.allStore[0].org ? (this.allStore[0].org.id as string) : "";
		this.storeForm.targetStoreId = this.allStore[0].org ? (this.allStore[0].org.id as string) : "";
		this.storeForm.remark = "";
	}

	downloadTemplate(path: string) {
		UploadApi.getUrl(path).then((resp: any) => {
			if (resp && resp.data) {
				window.open(resp.data);
			}
		}).catch((error) => {
			if (error && error.message) {
				this.$message.error(error.message)
			}
		})
	}
}