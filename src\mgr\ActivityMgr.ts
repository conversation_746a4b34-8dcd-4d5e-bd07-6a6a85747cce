/*
 * @Author: 黎钰龙
 * @Date: 2024-05-16 10:14:09
 * @LastEditTime: 2025-02-17 18:08:56
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\mgr\ActivityMgr.ts
 * 记得注释
 */
import { MarketBudgetActivityEnum } from "model/promotion/MarketBudgetActivityEnum"
import BrowserMgr from "./BrowserMgr"
import ConstantMgr from "./ConstantMgr";

// CRM活动全集合（待补充）
export enum ActivityMap {
  // 充值赠礼
  DEPOSIT_GIFT = 'DEPOSIT_GIFT',
  // 卡充值活动
  CARD_DEPOSIT_GIFT = 'CARD_DEPOSIT_GIFT',
  // 礼品卡
  GIFT_CARD = 'GIFT_CARD',
  // 实体礼品卡
  OFF_LINE_GIFT_CARD = 'OFF_LINE_GIFT_CARD',
  // 积分抵现活动
  POINTS_CHARGE_V2 = 'POINTS_CHARGE_V2',
  // 积分商品范围
  GAIN_POINTS_GOODS = 'GAIN_POINTS_GOODS',
  // 商品满额加送积分
  GOODS_GAIN_ADDITIONAL_POINTS = 'GOODS_GAIN_ADDITIONAL_POINTS',
  // 全场积分加速
  GAIN_POINTS_SPEED = 'GAIN_POINTS_SPEED',
  // 商品满数量加送积分
  GOODS_GAIN_ADDITIONAL_POINTS_BY_QTY = 'GOODS_GAIN_ADDITIONAL_POINTS_BY_QTY',
  // 商品组合满数量加送积分
  GOODS_GROUP_GAIN_ADDITIONAL_POINTS_BY_QTY = 'GOODS_GROUP_GAIN_ADDITIONAL_POINTS_BY_QTY',
  // 积分兑换券
  POINTS_EXCHANGE_COUPON = 'POINTS_EXCHANGE_COUPON',
  // 积分兑换商品
  POINTS_EXCHANGE_GOODS = 'POINTS_EXCHANGE_GOODS',
  // 储值支付立减
  MEMBER_BALANCE_REDUCTION = 'MEMBER_BALANCE_REDUCTION',
  // 储值支付折扣
  MEMBER_BALANCE_DISCOUNT = 'MEMBER_BALANCE_DISCOUNT',
  // 预付卡支付立减
  CARD_BALANCE_REDUCTION = 'CARD_BALANCE_REDUCTION',
  // 预付卡支付折扣
  CARD_BALANCE_DISCOUNT = 'CARD_BALANCE_DISCOUNT',
  // 商品积分加速
  GOODS_GAIN_POINTS_SPEED = 'GOODS_GAIN_POINTS_SPEED',
  // 注册发券
  MEMBER_REGISTER_COUPON = 'MEMBER_REGISTER_COUPON',
  // 微信激活发券
  WEIXIN_ACTIVATION_COUPON = 'WEIXIN_ACTIVATION_COUPON',
  // 全场满额发券
  GAIN_COUPON = 'GAIN_COUPON',
  // 商品满额发券
  GOODS_GAIN_COUPON = 'GOODS_GAIN_COUPON',
  // 商品满数量发券
  GOODS_QTY_GAIN_COUPON = 'GOODS_QTY_GAIN_COUPON',
  // 小程序领券
  MINI_PROGRAM_COUPON = 'MINI_PROGRAM_COUPON',
  // 微信投放券
  WEIXIN_COUPON = 'WEIXIN_COUPON',
  // 群发券
  MANUAL_COUPON = 'MANUAL_COUPON',
  // 第三方发券
  THIRD_ISSUE_COUPON = 'THIRD_ISSUE_COUPON',
  // 核销第三方券
  THIRD_CODE_ISSUE_COUPON = 'THIRD_CODE_ISSUE_COUPON',
  // 注册送大礼包
  MEMBER_REGISTER_GIFT = 'MEMBER_REGISTER_GIFT',
  // 微信激活送大礼包
  WEIXIN_ACTIVATION_GIFT = 'WEIXIN_ACTIVATION_GIFT',
  // 客群赠礼
  USER_GROUP_GIFT = 'USER_GROUP_GIFT',
  // 推送计划-单次推送
  SINGLE_TIME_PUSH_PLAN = 'SINGLE_TIME_PUSH_PLAN',
  // 推送计划-重复推送
  REPEAT_PUSH_PLAN = 'REPEAT_PUSH_PLAN',
  // 邀请有礼
  MEMBER_INVITE_REGISTER_GIFT = 'MEMBER_INVITE_REGISTER_GIFT',
  // 全场满额发大礼包
  GAIN_GIFT = 'GAIN_GIFT',
  // 导出券码发券
  EXPORT_COUPON_CODE = 'EXPORT_COUPON_CODE',
  // 商品满额发大礼包
  GOODS_GAIN_GIFT = 'GOODS_GAIN_GIFT',
  // 精准发券
  PRECISION_ISSUE_COUPON = 'PRECISION_ISSUE_COUPON',
  // 升级有礼
  UPGRADE_GIFT = 'UPGRADE_GIFT',
  // 用券发券
  USE_COUPON_ISSUE_GIFT = 'USE_COUPON_ISSUE_GIFT',
  // 支付宝小程序消费有礼
  ALI_APPLET_CONSUME_GIFT = 'ALI_APPLET_CONSUME_GIFT',
  // 支付宝小程序发券
  ALI_APPLET_ISSUE_COUPON = 'ALI_APPLET_ISSUE_COUPON',
  // 微信小程序领微信券
  WEI_XIN_APPLET_ISSUE_COUPON = 'WEI_XIN_APPLET_ISSUE_COUPON',
  // 微信开屏推广活动
  WEIXIN_BRAND_ACTIVITY = 'WEIXIN_BRAND_ACTIVITY',
  // 微信视频号发券活动
  WECHAT_VIDEO_ISSUE_COUPON_ACTIVITY_RULE = 'WECHAT_VIDEO_ISSUE_COUPON_ACTIVITY_RULE',
  // 定向发券活动
  DIRECTIONAL_ISSUE_COUPON_ACTIVITY_RULE = 'DIRECTIONAL_ISSUE_COUPON_ACTIVITY_RULE',
  // 完善资料有礼
  IMPROVE_PROFILES_GIFT = 'IMPROVE_PROFILES_GIFT',
  // 消费日历
  CONSUME_CALENDAR_GIFT = 'CONSUME_CALENDAR_GIFT',
  // 会员商品专享
  MEMBER_GOODS_QUATO_ACTIVITY = 'MEMBER_GOODS_QUATO_ACTIVITY',
  // 集点活动
  COLLECT_POINTS_ACTIVITY = 'COLLECT_POINTS_ACTIVITY',
  // 大转盘活动
  BIG_WHEEL_ACTIVITY = 'BIG_WHEEL_ACTIVITY',
  // 拼团活动
  GROUP_BOOKING_ACTIVITY = 'GROUP_BOOKING_ACTIVITY',
  // 支付宝平台团购券
  ALI_PLT_GROUP_COUPON = 'ALI_PLT_GROUP_COUPON',
  // 累计消费有礼
  CUMULATIVE_CONSUME_ACTIVITY = 'CUMULATIVE_CONSUME_ACTIVITY',
  // 客群定向发礼
  GROUP_TARGETED_GIFTS = 'GROUP_TARGETED_GIFTS',
}

// CRM活动工具类
export default class ActivityMgr {

  //判断当前活动是否为OA审批活动类型
  static isOaActivity(activityType: MarketBudgetActivityEnum) {
    const OAActivities = BrowserMgr.LocalStorage.getItem('sysConfig')?.platformAuditActivity || []
    return OAActivities.indexOf(activityType) > -1
  }

  static getActivityName(activityType: ActivityMap): string {
    switch (activityType) {
      case ActivityMap.DEPOSIT_GIFT:
        return new ConstantMgr.MenusFuc().format("/储值/会员储值/储值充值活动/详情页面", "充值赠礼");
      case ActivityMap.CARD_DEPOSIT_GIFT:
        return new ConstantMgr.MenusFuc().format("/储值/预付卡/预付卡充值有礼", "预付卡充值有礼");
      case ActivityMap.GIFT_CARD:
        return new ConstantMgr.MenusFuc().format("/公用/菜单", "电子卡售卡活动");
      case ActivityMap.OFF_LINE_GIFT_CARD:
        return new ConstantMgr.MenusFuc().format("/公用/菜单", "实体卡售卡活动");
      case ActivityMap.POINTS_CHARGE_V2:
        return new ConstantMgr.MenusFuc().format("/公用/菜单", "积分抵现活动");
      case ActivityMap.GAIN_POINTS_GOODS:
        return new ConstantMgr.MenusFuc().format("/公用/过滤器", "不积分商品");
      case ActivityMap.GOODS_GAIN_ADDITIONAL_POINTS:
        return new ConstantMgr.MenusFuc().format("/公用/过滤器", "商品满额加送积分");
      case ActivityMap.GAIN_POINTS_SPEED:
        return new ConstantMgr.MenusFuc().format("/公用/过滤器", "全场积分加倍");
      case ActivityMap.GOODS_GAIN_ADDITIONAL_POINTS_BY_QTY:
        return new ConstantMgr.MenusFuc().format("/公用/过滤器", "单品满数量加送积分");
      case ActivityMap.GOODS_GROUP_GAIN_ADDITIONAL_POINTS_BY_QTY:
        return new ConstantMgr.MenusFuc().format("/公用/过滤器", "商品组合满数量加送积分");
      case ActivityMap.POINTS_EXCHANGE_COUPON:
        return new ConstantMgr.MenusFuc().format("/营销/积分活动/积分活动", "积分兑换券");
      case ActivityMap.POINTS_EXCHANGE_GOODS:
        return new ConstantMgr.MenusFuc().format("/营销/积分活动", "积分兑换商品");
      case ActivityMap.MEMBER_BALANCE_REDUCTION:
        return new ConstantMgr.MenusFuc().format("/储值/会员储值/储值支付活动/列表页面", "储值支付立减");
      case ActivityMap.MEMBER_BALANCE_DISCOUNT:
        return new ConstantMgr.MenusFuc().format("/储值/会员储值/储值支付活动/列表页面", "储值支付折扣");
      case ActivityMap.CARD_BALANCE_REDUCTION:
        return new ConstantMgr.MenusFuc().format("/储值/预付卡/预付卡支付活动/列表页面", "预付卡支付立减");
      case ActivityMap.CARD_BALANCE_DISCOUNT:
        return new ConstantMgr.MenusFuc().format("/储值/预付卡/预付卡支付活动/列表页面", "预付卡支付折扣");
      case ActivityMap.GOODS_GAIN_POINTS_SPEED:
        return new ConstantMgr.MenusFuc().format("/公用/过滤器", "商品满额积分加倍");
      case ActivityMap.MEMBER_REGISTER_COUPON:
        return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "注册发券");
      case ActivityMap.WEIXIN_ACTIVATION_COUPON:
        return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "微信激活发券");
      case ActivityMap.GAIN_COUPON:
        return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "全场满额发券");
      case ActivityMap.GOODS_GAIN_COUPON:
        return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "商品满额发券");
      case ActivityMap.GOODS_QTY_GAIN_COUPON:
        return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "商品满数量发券");
      case ActivityMap.MINI_PROGRAM_COUPON:
        return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "小程序领券");
      case ActivityMap.WEIXIN_COUPON:
        return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "微信投放券");
      case ActivityMap.MANUAL_COUPON:
        return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "群发券");
      case ActivityMap.THIRD_ISSUE_COUPON:
        return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "第三方发券");
      case ActivityMap.THIRD_CODE_ISSUE_COUPON:
        return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "核销第三方券");
      case ActivityMap.MEMBER_REGISTER_GIFT:
        return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "注册送大礼包");
      case ActivityMap.WEIXIN_ACTIVATION_GIFT:
        return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "微信激活发大礼包");
      case ActivityMap.USER_GROUP_GIFT:
        return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "客群发大礼包");
      case ActivityMap.SINGLE_TIME_PUSH_PLAN:
        return new ConstantMgr.MenusFuc().format("/营销/运营/推送计划/新建页", "推送计划-单次推送");
      case ActivityMap.REPEAT_PUSH_PLAN:
        return new ConstantMgr.MenusFuc().format("/营销/运营/推送计划/新建页", "推送计划-重复推送");
      case ActivityMap.MEMBER_INVITE_REGISTER_GIFT:
        return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "邀请有礼");
      case ActivityMap.GAIN_GIFT:
        return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "全场满额发大礼包");
      case ActivityMap.EXPORT_COUPON_CODE:
        return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "导出券码发券");
      case ActivityMap.GOODS_GAIN_GIFT:
        return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "商品满额发大礼包");
      case ActivityMap.PRECISION_ISSUE_COUPON:
        return new ConstantMgr.MenusFuc().format("/公用/菜单", "精准营销");
      case ActivityMap.UPGRADE_GIFT:
        return new ConstantMgr.MenusFuc().format("/营销/升级有礼", "升级有礼");
      case ActivityMap.USE_COUPON_ISSUE_GIFT:
        return new ConstantMgr.MenusFuc().format("/营销/券礼包活动/券礼包活动", "用券发券");
      case ActivityMap.ALI_APPLET_CONSUME_GIFT:
        return new ConstantMgr.MenusFuc().format("/公用/菜单", "消费有礼");
      case ActivityMap.ALI_APPLET_ISSUE_COUPON:
        return new ConstantMgr.MenusFuc().format("/券/延期申请", "支付宝小程序发券");
      case ActivityMap.WEI_XIN_APPLET_ISSUE_COUPON:
        return new ConstantMgr.MenusFuc().format("/券/延期申请", "小程序领微信券");
      case ActivityMap.WEIXIN_BRAND_ACTIVITY:
        return new ConstantMgr.MenusFuc().format("/公用/过滤器", "开屏推广");
      case ActivityMap.WECHAT_VIDEO_ISSUE_COUPON_ACTIVITY_RULE:
        return new ConstantMgr.MenusFuc().format("/券/延期申请", "视频号发券");
      case ActivityMap.DIRECTIONAL_ISSUE_COUPON_ACTIVITY_RULE:
        return new ConstantMgr.MenusFuc().format("/公用/过滤器", "定向发券");
      case ActivityMap.IMPROVE_PROFILES_GIFT:
        return new ConstantMgr.MenusFuc().format("/公用/菜单", "完善资料有礼");
      case ActivityMap.CONSUME_CALENDAR_GIFT:
        return new ConstantMgr.MenusFuc().format("/营销/日历活动", "消费日历");
      case ActivityMap.MEMBER_GOODS_QUATO_ACTIVITY:
        return "会员商品专享";
      case ActivityMap.COLLECT_POINTS_ACTIVITY:
        return new ConstantMgr.MenusFuc().format("/营销/集点活动", "集点活动");
      case ActivityMap.BIG_WHEEL_ACTIVITY:
        return new ConstantMgr.MenusFuc().format("/营销/大转盘活动", "大转盘");
      case ActivityMap.GROUP_BOOKING_ACTIVITY:
        return new ConstantMgr.MenusFuc().format("/营销/拼团抽奖活动", "抽奖团");
      case ActivityMap.ALI_PLT_GROUP_COUPON:
        return new ConstantMgr.MenusFuc().format("/公用/过滤器", "平台团购券");
      case ActivityMap.CUMULATIVE_CONSUME_ACTIVITY:
        return new ConstantMgr.MenusFuc().format("/营销/累计消费有礼", "累计消费有礼活动");
      case ActivityMap.GROUP_TARGETED_GIFTS:
        return new ConstantMgr.MenusFuc().format("/公用/菜单", "客群定向发礼");
      default:
        return "未知活动类型";
    }
  }
}