// 抽奖码
import { ObtainScene } from "model/promotion/luckydraw/ObtainScene";

export default class LuckyDrawLotteryCode {
  // uuid
  uuid: Nullable<string> = null
  // 版本
  version: Nullable<number> = null
  // 活动号
  activityNumber: Nullable<string> = null
  // 会员id
  memberId: Nullable<string> = null
  // 码值
  code: Nullable<string> = null
  // 随机数
  random: Nullable<string> = null
  // 获取场景
  obtainScene: Nullable<ObtainScene> = null
  // 获取时间
  obtainTime: Nullable<Date> = null
  // 获取门店
  obtainOrgId: Nullable<string> = null
  // 获取门店名称
  obtainOrgName: Nullable<string> = null
  // 是否已中奖
  won: Nullable<number> = null
  // 中奖轮次
  wonNo: Nullable<number> = null
  // 中奖时间
  wonTime: Nullable<Date> = null
  // 会员号
  crmCode: Nullable<string> = null
  // 手机号
  mobile: Nullable<string> = null
}
