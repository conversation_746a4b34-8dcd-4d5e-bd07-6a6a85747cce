import {Component, Vue} from 'vue-property-decorator'
import WechatHeader from 'cmp/wechatheader/WechatHeader.vue'
import WechatStep from 'cmp/wechatstep/WechatStep.vue'
import WechatUpload from 'cmp/wechatupload/WechatUpload.vue'
import Grade from 'model/grade/Grade'
import ImgScaleDialog from 'pages/member/wx/wechatauthorize/dialog/ImgScaleDialog.vue'
import AlipayInitApi from 'http/aliPay/v2/AlipayInitApi'
import AliPassCardTemplate from 'model/alipay/v2/AliPassCardTemplate'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import I18nPage from 'common/I18nDecorator'

@Component({
  name: 'AlipayMemberCardDtl',
  components: {
    WechatHeader,
    WechatStep,
    WechatUpload,
    ImgScaleDialog,
    BreadCrume
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/公用/菜单',
    '/设置/支付宝会员初始化'
  ],
  auto: true
})
export default class AlipayMemberCardDtl extends Vue {
  payRule: any = '' // 支付规则信息
  wechatCard: AliPassCardTemplate = new AliPassCardTemplate()
  grade: Grade = new Grade()
  $refs: any
  panelArray:any = []
  created() {
    this.panelArray = [
      {
        name: this.i18n('支付宝会员设置'),
        url: 'ali-member-setting'
      },
      {
        name: this.i18n('支付宝会员卡'),
        url: ""
      }
    ]
  }
  mounted() {
    this.getWechatCard()
  }

  // 取得微信会员卡
  getWechatCard() {
    AlipayInitApi.getTemplate().then((res: any) => {
      if (res.code === 2000) {
        this.wechatCard = res.data
      }
    })
  }

  // 编辑
  toWechatMemberCard() {
    this.$router.push({ name: 'alipay-member-card-edit', query: { from: 'edit' } })
  }

  // 上一步
  toWechatAuthorizeAfter() {
    this.$router.push({ name: 'wechat-authorize-after' })
  }
  doImgClick(url: string) {
    this.$refs.imgScale.open(url)
  }
  doBack() {
    this.$router.back()
  }
  // 下一步
  toWechatMemberCode() {
    this.$router.push({ name: 'wechat-member-code' })
  }
  getMbrCardBenefit(arr: string[]) {
    let obj: any = {
      score: this.formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/积分'),
      coupon: this.formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/优惠券'),
      mbrgrade: this.formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/等级')
    }
    let newArr: any = []
    if (arr && arr.length > 0) {
      arr.forEach((item: any) => {
        if (obj[item]) {
          newArr.push(obj[item])
        }
      })
    }
    return newArr
  }
  getStartCardInfo(str: string, type: string) {
    let curObj: any = {
      OPEN_FORM_FIELD_MOBILE: this.formatI18n('/会员/会员资料/手机号'),
      OPEN_FORM_FIELD_GENDER: this.formatI18n('/会员/会员资料/性别'),
      OPEN_FORM_FIELD_NAME: this.formatI18n('/会员/会员资料/姓名'),
      OPEN_FORM_FIELD_NICKNAME: this.formatI18n('/会员/会员资料/昵称'),
      OPEN_FORM_FIELD_BIRTHDAY_WITH_YEAR: this.formatI18n('/会员/会员资料/生日'),
      OPEN_FORM_FIELD_ADDRESS: this.formatI18n('/会员/会员资料/地址'),
      OPEN_FORM_FIELD_CITY: '城市',
      OPEN_FORM_FIELD_EMAIL: this.formatI18n('/会员/会员资料/邮箱'),
      OPEN_FORM_FIELD_IS_STUDENT: '是否学生认证'
    }
    let requireArr: any = []
    let optionArr: any = []
    if (str) {
      let newInfo = JSON.parse(str)
      newInfo.forEach((item: any) => {
        if (item.value === 1) { // 必填
          let obj = {
            cardChecked: true,
            cardName: curObj[item.key],
            cardCode: item.key,
            cardSelected: 1
          }
          requireArr.push(obj)
        } else {
          let obj = {
            cardChecked: true,
            cardName: curObj[item.key],
            cardCode: item.key,
            cardSelected: 2
          }
          optionArr.push(obj)
        }
      })
    }
    if (type === 'must') {
      return requireArr
    } else {
      return optionArr
    }
  }
  getWay(str: string) {
    let obj: any = {
      0: this.formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/卡号和二维码'),
      1: this.formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/卡号和条形码')
    }
    return obj[str]
  }
  getEquityType(equityType: any) {
    let str = ''
    if (equityType && equityType.length > 0) {
      equityType.forEach((item: string) => {
        str += item + '、'
      })
    }
    if (str) {
      str = str.substring(0, str.length - 1)
    }
    return str
  }
  getMbrAttribute(mbrAttribute: any) {
    let str = ''
    if (mbrAttribute && mbrAttribute.length > 0) {
      mbrAttribute.forEach((item: any) => {
        str += item.cardName + '、'
      })
    }
    if (str) {
      str = str.substring(0, str.length - 1)
    }
    return str
  }
  getCustomEnter(index: number) {
    if (index === 0) {
      let str = this.formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/自定义入口{0}')
      str = str.replace(/\{0\}/g, this.formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/自定义入口/一'))
      return str
    } else if (index === 1) {
      let str = this.formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/自定义入口{0}')
      str = str.replace(/\{0\}/g, this.formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/自定义入口/二'))
      return str
    } else if (index === 2) {
      let str = this.formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/自定义入口{0}')
      str = str.replace(/\{0\}/g, this.formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/自定义入口/三'))
      return str
    } else {
      let str = this.formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/自定义入口{0}')
      str = str.replace(/\{0\}/g, this.formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/自定义入口/四'))
      return str
    }
  }
}