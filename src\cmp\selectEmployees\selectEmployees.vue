<!--
 * @Author: 黎钰龙
 * @Date: 2024-03-05 14:13:36
 * @LastEditTime: 2024-05-31 10:15:36
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\selectEmployees\selectEmployees.vue
 * 记得注释
-->
<template>
  <el-select
    v-model="selectEmployee"
    value-key="id"
    :style="{width: width}"
    :loading="selectLoading"
    :disabled="disabled"
    clearable
    filterable
    remote
    :remote-method="doRemoteMethod"
    :placeholder="placeholder ? placeholder : i18n('/资料/员工/搜索员工代码/姓名')"
  >
    <el-option :label="i18n('全部')" :value="null">
      {{ i18n("全部") }}
    </el-option>
    <el-option :label="`[${item.employee.id}]${item.employee.name}`" :value="item.employee" v-for="(item, index) in employees" :key="index">
      [{{ item.employee.id }}]{{ item.employee.name }}
    </el-option>
  </el-select>
</template>

<script lang="ts" src="./selectEmployees.ts">
</script>

<style>

</style>