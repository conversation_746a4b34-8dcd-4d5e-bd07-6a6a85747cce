import {Component, Prop, Vue, Watch} from 'vue-property-decorator'
import SpecialTpl from "pages/member/entities/SpecialTpl";

@Component({
  name: 'MbrCardTitle',
  components: {}
})
export default class MbrCardTitle extends Vue {
  @Prop()
  value: SpecialTpl
  ruleForm: any = {
    title: ''
  }

  rules: any = {}
  @Watch('value')
  onValueChange(value: string) {
    if (value) {
      this.ruleForm.title = value
    }
  }
  created() {
    this.rules = {
      title: [
        { required: true, message: this.formatI18n('/公用/券模板', '请输入必填项'), trigger: 'blur' }
      ]
    }
  }
  doChange() {
    this.$emit('input', this.ruleForm.title)
  }
}