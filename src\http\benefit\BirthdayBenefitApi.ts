import ApiClient from 'http/ApiClient'
import BirthdayBenefit from 'model/benefit/BirthdayBenefit'
import GiftRule from 'model/benefit/GiftRule'
import PointsRule from 'model/benefit/PointsRule'
import Response from 'model/common/Response'

export default class BirthdayBenefitApi {
  /**
   * 生日送礼规则详情
   * 生日送礼规则详情
   * 
   */
  static giftRuleDetail(): Promise<Response<GiftRule>> {
    return ApiClient.server().get(`/v1/benefit/birthday/giftRuleDetail`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表汇总
   * 列表汇总
   * 
   */
  static home(): Promise<Response<BirthdayBenefit>> {
    return ApiClient.server().get(`/v1/benefit/birthday/home`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 积分加倍规则详情
   * 积分加倍规则详情
   * 
   */
  static pointsRuleDetail(): Promise<Response<PointsRule>> {
    return ApiClient.server().get(`/v1/benefit/birthday/pointsRuleDetail`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改生日送礼规则
   * 修改生日送礼规则
   * 
   */
  static saveOrModifyGiftRule(body: GiftRule): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/benefit/birthday/saveOrModifyGiftRule`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改积分加倍规则
   * 修改积分加倍规则
   * 
   */
  static saveOrModifyPointsRule(body: PointsRule): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/benefit/birthday/saveOrModifyPointsRule`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 启用禁用规则
   * 启用禁用规则
   * 
   */
  static switchRule(pointsRule: boolean): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/benefit/birthday/switch/${pointsRule}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

}
