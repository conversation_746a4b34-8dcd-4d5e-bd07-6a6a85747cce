<template>
  <div class="store-value-adjust-dtl">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button type="primary" @click="doAudit" v-if="billDtl.state === 'SUBMIT' && permission.auditable && !hasOaPermission">
          审核
        </el-button>
        <el-button @click="doModify" v-if="(billDtl.source === 'create' && billDtl.state === 'INITIAL') && permission.editable">
          修改
        </el-button>
        <el-button @click="doReject" v-if="billDtl.state === 'SUBMIT' && permission.rejectable && !hasOaPermission">
          驳回
        </el-button>
        <el-button @click="doSubmit" v-if="billDtl.state === 'INITIAL' && permission.submitable">
          提交
        </el-button>
        <el-button @click="doSubmitAndAudit" v-if="billDtl.state === 'INITIAL' && permission.submitable && permission.auditable && !hasOaPermission">
          提交并审核
        </el-button>
      </template>
    </BreadCrume>
    <div style="height: 95%;overflow: auto">
      <div class="top-wrap">
        <div class="left">
          <div class="back">
            <img src="~assets/image/storevalue/back.png">
          </div>
        </div>
        <div class="right">
          <div class="top">
            <div class="item1">
              <div class="bill"><span>单号：</span>{{billDtl.billNumber}}</div>
              <div class="name">储值调整单</div>
            </div>
            <div class="item2">
              <div class="desc">状态</div>
              <div class="state">
                <el-tag type="success" v-if="billDtl.state === 'AUDITED'">{{i18n('已审核')}}</el-tag>
                <el-tag v-else-if="billDtl.state === 'SUBMIT'">{{i18n('已提交')}}</el-tag>
                <el-tag type="danger" v-else-if="billDtl.state === 'REJECTED'">{{i18n('已驳回')}}</el-tag>
                <el-tag type="warning" v-else>{{i18n('未提交')}}</el-tag>
              </div>
            </div>
          </div>
          <div class="bottom">
            <template v-if="switchFlag && billDtl.detailRemark">
              <div class="account-info" v-for="(item, index) of billDtl.detailRemark" :key="index">
                {{item[0]}}&nbsp;&nbsp;
                <i18n k="/储值/会员储值/储值调整单/详情/增加储值{0}元">
                  <template slot="0"><span style="color: red">{{item[1]}}</span></template>
                </i18n>,
                <i18n k="/储值/会员储值/储值调整单/详情/扣减储值{0}元">
                  <template slot="0"><span style="color: green">{{item[2]}}</span></template>
                </i18n>
              </div>
            </template>
            <div class="account-info" v-if="!switchFlag && billDtl.detailRemark && billDtl.detailRemark[0]">
              <i18n k="/储值/会员储值/储值调整单/详情/增加储值{0}元">
                <template slot="0"><span style="color: red">{{billDtl.detailRemark[0][0]}}</span></template>
              </i18n>,
              <i18n k="/储值/会员储值/储值调整单/详情/扣减储值{0}元">
                <template slot="0"><span style="color: green">{{billDtl.detailRemark[0][1]}}</span></template>
              </i18n>
            </div>
            <div class="account-info" v-if="showOrg">
              <span>{{getOrgStr()}}</span>
            </div>
            <div class="account-info" v-if="billDtl.state === 'REJECTED'">
              <span>{{i18n('/券/延期申请/驳回原因')}}：{{billDtl.rejected || '--'}}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="row-height"></div>
      <div class="center-wrap">
        <div>
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <p class="sub-item" style="margin: 0;">调整明细</p>
            <el-button @click="doExport" v-if="permission.exportLine">导出明细</el-button>
          </div>
          <el-table :data="queryDtl">
            <el-table-column fixed label="序号" prop="lineNo" width="140" align="left">
              <template slot-scope="scope">
                <span no-i18n>{{scope.row.lineNo}}</span>
              </template>
            </el-table-column>
            <el-table-column fixed label="会员" prop="mobile" width="140" align="left">
              <template slot-scope="scope">
                <span
                  no-i18n>{{scope.row.mobile ? scope.row.mobile : (scope.row.crmCode ? scope.row.crmCode : (scope.row.hdCardCardNum ? scope.row.hdCardCardNum : '--'))}}</span>
              </template>
            </el-table-column>
            <el-table-column fixed v-if="switchFlag" label="账户" prop="accountName" width="140" align="left">
              <template slot-scope="scope">
                <span no-i18n>[{{scope.row.accountId}}]{{scope.row.accountName}}</span>
              </template>
            </el-table-column>
            <el-table-column label="调整前储值余额" prop="occurAmount" width="140" align="right">
              <template slot-scope="scope">
                <span no-i18n>{{(Number(scope.row.oldAmount) + Number(scope.row.oldGiftAmount)) | fmt}}</span>
              </template>
            </el-table-column>
            <el-table-column label="调整前实充余额" prop="occurAmount" width="140" align="right">
              <template slot-scope="scope">
                <span no-i18n>{{scope.row.oldAmount | fmt}}</span>
              </template>
            </el-table-column>
            <el-table-column label="调整前返现余额" prop="oldGiftAmount" width="140" align="right">
              <template slot-scope="scope">
                <span no-i18n>{{scope.row.oldGiftAmount | fmt}}</span>
              </template>
            </el-table-column>
            <el-table-column label="储值余额调整" prop="totalAmount" width="140" align="right">
              <template slot-scope="scope">
                <div no-i18n style="color: red" v-if="Number(scope.row.totalAmount) > 0">+{{scope.row.totalAmount | fmt}}</div>
                <div no-i18n style="color: green" v-else>{{scope.row.totalAmount | fmt}}</div>
              </template>
            </el-table-column>
            <el-table-column label="实充调整" prop="occurAmount" width="140" align="right">
              <template slot-scope="scope">
                <div no-i18n style="color: red" v-if="Number(scope.row.occurAmount) > 0">+{{scope.row.occurAmount | fmt}}</div>
                <div no-i18n style="color: green" v-else>{{scope.row.occurAmount | fmt}}</div>
              </template>
            </el-table-column>
            <el-table-column label="返现调整" prop="occurGiftAmount" width="140" align="right">
              <template slot-scope="scope">
                <div no-i18n style="color: red" v-if="Number(scope.row.occurGiftAmount) > 0">+{{scope.row.occurGiftAmount | fmt}}</div>
                <div no-i18n style="color: green" v-else>{{scope.row.occurGiftAmount | fmt}}</div>
              </template>
            </el-table-column>
            <el-table-column label="调整原因" prop="reason" width="140" align="right">
              <template slot-scope="scope">
                <div no-i18n :title="scope.row.reason" style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">
                  {{scope.row.reason | strFormat}}</div>
              </template>
            </el-table-column>
            <el-table-column label="说明" prop="remark" align="right">
              <template slot-scope="scope">
                <div no-i18n style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;" :title="scope.row.remark">
                  {{scope.row.remark | strFormat}}</div>
              </template>
            </el-table-column>
            <el-table-column label="状态" prop="state" align="right">
              <template slot-scope="scope">
                <div>
                  <el-tag type="warning"
                    v-if="billDtl.state === 'AUDITED' && (scope.row.state === null || scope.row.state === '')">{{i18n('成功')}}</el-tag>
                  <el-tag type="warning" v-else-if="scope.row.state === null || scope.row.state === ''">{{i18n('未审核')}}</el-tag>
                  <el-tag type="success" v-else-if="scope.row.state === 'SUCCESS'">{{i18n('成功')}}</el-tag>
                  <el-tag type="failed" v-else-if="scope.row.state === 'FAIL'">{{i18n('失败')}}</el-tag>
                  <el-tag type="warning" v-else>{{i18n('未审核')}}</el-tag>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div style="margin-top: 15px">
            <el-pagination no-i18n :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
              @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background layout="total, prev, pager, next, sizes,  jumper">
            </el-pagination>
          </div>
        </div>
      </div>
      <div class="row-height"></div>
      <div class="foot-wrap">
        <div>
          <p class="sub-item">操作日志</p>
          <el-table :data="billDtl.logs">
            <el-table-column label="操作类型" prop="type">
              <template slot-scope="scope">
                <span no-i18n>{{scope.row.type}}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作人" prop="operator">
              <template slot-scope="scope">
                <span no-i18n>{{scope.row.operator}}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作时间" prop="occurredTime">
              <template slot-scope="scope">
                <span no-i18n>{{scope.row.occurredTime | dateFormate3}}</span>
              </template>
            </el-table-column>

          </el-table>
        </div>
      </div>
    </div>
    <RejectDialog ref="rejectDialog" @submit="doSubmitReject"></RejectDialog>
    <DownloadCenterDialog :dialogvisiable="fileDialogVisible" :showTip="showTip" @dialogClose="doDownloadDialogClose">
    </DownloadCenterDialog>
  </div>
</template>

<script lang="ts" src="./StoreValueAdjustDtl.ts">
</script>

<style lang="scss">
.store-value-adjust-dtl {
  width: 100%;
  height: 100%;
  background-color: white;
  overflow: hidden;
  .top-wrap {
    display: flex;
    flex-direction: row;
    .left {
      width: 80px;
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
      .back {
        width: 48px;
        height: 48px;
        border-radius: 100%;
        background-color: rgba(242, 242, 242, 1);
        img {
          width: 24px;
          height: 24px;
          position: relative;
          top: 13px;
          left: 12px;
        }
      }
    }
    .right {
      display: flex;
      flex: 1;
      flex-direction: column;
      position: relative;
      .top {
        display: flex;
        height: 105px;
        border-bottom: 1px solid rgba(242, 242, 242, 1);
        margin-right: 20px;
        .item1 {
          .bill {
            margin-top: 16px;
            color: rgba(51, 51, 51, 0.***************);
          }
          .name {
            font-weight: 500;
            margin-top: 8px;
            font-size: 20px;
          }
        }
        .item2 {
          padding-left: 70px;
          padding-top: 16px;
          .desc {
            color: rgba(51, 51, 51, 0.***************);
          }
          .state {
            font-weight: 500;
            margin-top: 8px;
            font-size: 20px;
          }
        }
      }
      .bottom {
        padding-bottom: 20px;
        .account-info {
          margin-top: 10px;
        }
        .red {
          color: red;
        }
        .green {
          color: #008000;
        }
      }
    }
  }
  .row-height {
    height: 20px;
    background-color: rgba(242, 242, 242, 1);
  }
  .center-wrap,
  .foot-wrap {
    padding: 20px;
  }
  .sub-item {
    font-size: 16px;
    padding-top: 20px;
    margin-bottom: 10px;
  }
  .el-table__body .el-table__row td {
    border-bottom: 1px solid #d7dfeb !important;
  }
}
</style>