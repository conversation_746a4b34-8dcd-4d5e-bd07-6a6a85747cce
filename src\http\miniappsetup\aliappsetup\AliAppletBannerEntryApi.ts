import ApiClient from 'http/ApiClient'
import BannerEntry from 'model/miniappsetup/aliappsetup/BannerEntry'
import Response from 'model/common/Response'
import SaveBannerEntryRequest from 'model/miniappsetup/aliappsetup/SaveBannerEntryRequest'

export default class AliAppletBannerEntryApi {
  /**
   * 更新
   * 更新。
   * 
   */
  static modify(body: SaveBannerEntryRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/applet/banner/entry/modify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询
   * 查询。
   * 
   */
  static query(): Promise<Response<BannerEntry[]>> {
    return ApiClient.server().post(`/v1/applet/banner/entry/query`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除
   * 删除。
   * 
   */
  static remove(id: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/applet/banner/entry/remove`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存
   * 保存。
   * 
   */
  static save(body: SaveBannerEntryRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/applet/banner/entry/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
