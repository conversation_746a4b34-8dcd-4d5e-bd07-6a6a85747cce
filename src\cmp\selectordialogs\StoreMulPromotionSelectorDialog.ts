import {Component, Prop, Watch} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import OrgApi from 'http/org/OrgApi';
import RSOrgFilter from 'model/common/RSOrgFilter';
import AbstractSelectDialog from './AbstractSelectDialog';
import RSOrg from 'model/common/RSOrg';

@Component({
  name: 'StoreMulPromotionSelectorDialog',
  components: {
    FormItem
  }
})
export default class StoreMulPromotionSelectorDialog extends AbstractSelectDialog<RSOrg> {
  @Prop()
  marketCenterId: string
  @Prop()
  marketCenterName: string
  orgFilter: RSOrgFilter = new RSOrgFilter()
  marketCenterNameCopy = ''

  @Watch('marketCenterId')
  watchMarketCenterId(marketCenterId: string) {
    this.orgFilter.marketingCenterIdEquals = marketCenterId
  }

  @Watch('marketCenterName')
  watchMarketCenterName(marketCenterName: string) {
    this.marketCenterNameCopy = '[' + this.marketCenterId + ']' + marketCenterName
  }

  reset() {
    this.orgFilter.idNameLikes = ''
  }

  getId(ins: RSOrg): string {
    // @ts-ignore
    return ins.org.id;
  }

  getName(ins: RSOrg): string {
    // @ts-ignore
    return ins.org.name;
  }

  getResponseData(response: any): any {
    return response.data
  }

  queryFun(): Promise<any> {
    this.orgFilter.page = this.page.currentPage - 1
    this.orgFilter.pageSize = this.page.size
    this.orgFilter.sorters.orgId = 'asc'
    return OrgApi.query(this.orgFilter)
  }
}
