import Bread<PERSON>rume from 'cmp/bread-crumb/BreadCrume';
import FormItem from 'cmp/formitem/FormItem';
import I18nPage from 'common/I18nDecorator';
import { Component, Vue } from 'vue-property-decorator';
import 'echarts/lib/chart/treemap'
import 'echarts/lib/component/tooltip';

import PortraitIndicatorSelect from 'pages/analysis/cmp/PortraitIndicatorSelect/PortraitIndicatorSelect';


import BCustomerProfileTarget from 'model/analysis/BCustomerProfileTarget';
import BCustomerProfileDimension from 'model/analysis/BCustomerProfileDimension';

import MemberAnalysisMetrics from 'model/default/MemberAnalysisMetrics';
import MemberAnalysisDimension from 'model/default/MemberAnalysisDimension';
import MemberAnalysisCondition from 'model/default/MemberAnalysisCondition';

import MemberAnalysis from 'model/report/memberanalysis/MemberAnalysis'
import { ConditionOperationType } from 'model/default/ConditionOperationType';


import MemberAnalysisApi from 'http/analysis/MemberAnalysisApi';
import MemberAnalysisFilter from 'model/report/memberanalysis/MemberAnalysisFilter';


import CustomRangeDialog from 'pages/analysis/cmp/CustomRangeDialog/CustomRangeDialog';
import ConditionDateSelect from './cmp/ConditionDateSelect';
import { IntervalType } from 'model/default/IntervalType';

import CustomerProfileGoupSection from 'model/default/CustomerProfileGoupSection';
import CommonUtil from 'util/CommonUtil';
import { AnalysisPropType } from 'model/default/AnalysisPropType';
import MemberAnalysisQueryResponse from 'model/report/memberanalysis/MemberAnalysisQueryResponse';
import GradeApi from "http/grade/grade/GradeApi";
import Grade from "model/grade/Grade";


import { CustomerProfileCategory } from 'model/analysis/CustomerProfileCategory';

import { AnalysisOperateType } from "model/default/AnalysisOperateType";

import RSOrgFilter from "model/common/RSOrgFilter";
import OrgApi from "http/org/OrgApi";
import RSChannelManagementFilter from "model/common/RSChannelManagementFilter";
import ChannelManagementApi from "http/channelmanagement/ChannelManagementApi";
import CustomRangeInfo from 'model/default/CustomRangeInfo';
import GroupSection from 'model/default/GroupSection';
import NormalCalculationResult from 'model/analysis/NormalCalculationResult';
import RSChannelManagement from 'model/common/RSChannelManagement'
import Channel from 'model/common/Channel'
import { nextTick } from 'process';
import ConsumeReturnFlow from 'pages/deposit/mbrdeposit/report/tabs/ConsumeReturnFlow';


class Form {
    searchCondition: MemberAnalysisCondition[] = []
}


@Component({
    name: 'VipAnalysisDtl',
    components: {
        BreadCrume,
        FormItem,
        PortraitIndicatorSelect,
        CustomRangeDialog,
        ConditionDateSelect,
    }
})
@I18nPage({
    prefix: [
        '/公用/券模板',
        '/数据/数据洞察',
        '/会员/会员资料'
    ],
    auto: true
})


export default class VipAnalysisDtl extends Vue {
    $refs: any
    dtlId: any //会员分析详情id
    dtlTitle: string = '' //会员分析详情标题
    editType: string = ''
    propName: string = '' // 名称, 时间型维度数据分桶弹窗的title
    page = {
        page: 0,
        pageSize: 10,
        total: 0
    }
    tooltipContent: string = this.i18n('暂无范围') //提示框内容
    tooltipRangeContent: string = this.i18n('暂无范围') //提示框内容
    // 要提交的生成分析所有条件
    queryData: MemberAnalysisFilter = new MemberAnalysisFilter()

    // 上方表单，添加指标，维度，筛选条件
    formData: MemberAnalysis = new MemberAnalysis()
    popType: string = '' // 选择的类型，数值型/时间型, 默认数值型
    rangeList: CustomRangeInfo[] = [new CustomRangeInfo()]  //维度区间
    dimensionTitleList: string[] = [''] //维度标题列表

    //   维度区间，选择对应数据分桶
    currentDimension: MemberAnalysisDimension = new MemberAnalysisDimension()
    currentIndex: number = 0
    propDisplayIntervalType: IntervalType = IntervalType.YEAR // 维度区间类型


    dialogVisible: boolean = false
    //   新建弹窗表单
    labelForm: any = {
        labelName: ''
    }
    // 弹窗表单校验
    get dialogRules() {
        return {
            labelName: [{ required: true, message: this.i18n('/数据/数据洞察/列表页/报表名称不能为空'), trigger: ['blur', 'change'] }]
        }
    }

    isShowSearchCondition: boolean = false // 是否显示下方表格搜索条件（生成分析后展示）
    isShowExportButton: boolean = true // 是否显示导出按钮

    searchForm: Form = new Form() // 下方表格搜索条件


    // 枚举下拉框选项
    selectOptions: ConditionOperationType[] = Object.values(ConditionOperationType);

    // 下方表格数据
    tableData: MemberAnalysisQueryResponse[] = []


    isDtl: boolean = false // 是否是详情页



    selectDateOptions: any = [
        { label: this.i18n('/会员/智能打标/绝对时间'), value: 'absolute' },
        { label: this.i18n('/会员/智能打标/相对当前时间点'), value: 'relative_to_now' },
        { label: this.i18n('/会员/智能打标/相对当前时间区间'), value: 'relative_to_now_range' },
        { label: this.i18n('/会员/智能打标/有值'), value: 'have_value' },
        { label: this.i18n('/会员/智能打标/没值'), value: 'not_have_value' },
    ]
    getOtherLabel(option: ConditionOperationType) {
        switch (option) {
            case ConditionOperationType.EQUALS:
                return this.i18n('/会员/洞察/公共/操作符/等于');
            case ConditionOperationType.NOT_EQUALS:
                return this.i18n('/会员/洞察/公共/操作符/不等于');
            case ConditionOperationType.CONTAIN:
                return this.i18n('/会员/智能打标/包含');
            case ConditionOperationType.NOT_CONTAIN:
                return this.i18n('/会员/智能打标/不包含');
            case ConditionOperationType.HAVE_VALUE:
                return this.i18n('/会员/智能打标/有值');
            case ConditionOperationType.NOT_HAVE_VALUE:
                return this.i18n('/会员/智能打标/没值');
            case ConditionOperationType.REGEXP:
                return this.i18n('/会员/智能打标/正则匹配');
            case ConditionOperationType.NOT_REGEXP:
                return this.i18n('/会员/智能打标/正则不匹配');
            default:
                return false;
        }
    }

    getNumLabel(option: ConditionOperationType) {
        switch (option) {
            case ConditionOperationType.EQUALS:
                return this.i18n('/会员/洞察/公共/操作符/等于');
            case ConditionOperationType.NOT_EQUALS:
                return this.i18n('/会员/洞察/公共/操作符/不等于');
            case ConditionOperationType.HAVE_VALUE:
                return this.i18n('/会员/智能打标/有值');
            case ConditionOperationType.NOT_HAVE_VALUE:
                return this.i18n('/会员/智能打标/没值');
            case ConditionOperationType.LESS:
                return this.i18n('/储值/会员储值/门店预付卡管理/小于');
            case ConditionOperationType.LESS_EQUALS:
                return this.i18n('/储值/会员储值/门店预付卡管理/小于等于');
            case ConditionOperationType.GREATER:
                return this.i18n('/会员/智能打标/大于');
            case ConditionOperationType.GREATER_EQUALS:
                return this.i18n('/会员/智能打标/大于等于');
            case ConditionOperationType.BETWEEN:
                return this.i18n('/会员/标签客群/标签/区间');
            default:
                return false;
        }
    }

    getTimeLabel(option: ConditionOperationType) {
        switch (option) {
            case ConditionOperationType.EQUALS:
                return this.i18n('/会员/洞察/公共/操作符/等于');
            case ConditionOperationType.NOT_EQUALS:
                return this.i18n('/会员/洞察/公共/操作符/不等于');
            case ConditionOperationType.LESS:
                return this.i18n('/会员/洞察/公共/操作符/小于');
            case ConditionOperationType.LESS_EQUALS:
                return this.i18n('/会员/洞察/公共/操作符/小于等于');
            case ConditionOperationType.GREATER:
                return this.i18n('/会员/智能打标/大于');
            case ConditionOperationType.GREATER_EQUALS:
                return this.i18n('/会员/智能打标/大于等于');
            case ConditionOperationType.BETWEEN:
                return this.i18n('/会员/洞察/公共/操作符/区间为');
            default:
                return false;
        }
    }


    // 表单校验规则
    get rules() {
        return {
            metrics: [
                {
                    required: true,
                    validator: (rule: any, val: Nullable<MemberAnalysisMetrics>[], callback: any) => {
                        if (!val?.length) {
                            callback(this.i18n('/数据/数据洞察/详情页/提示/请添加指标'))
                        }
                        if (val?.length > 20) {
                            callback(this.i18n('/数据/数据洞察/详情页/提示/最多添加20个指标'))
                        }
                        const arr: string[] = []
                        val.forEach((item) => {
                            if (arr.some((val) => val === JSON.stringify(item))) {
                                callback(this.i18n('/数据/数据洞察/详情页/提示/不允许添加重复的指标'))
                            } else {
                                arr.push(JSON.stringify(item))
                            }
                        })
                        callback()
                    },
                    trigger: 'change'
                }
            ],
            dimensions: [
                {
                    required: false,
                    validator: (rule: any, val: MemberAnalysisDimension[], callback: any) => {
                        if (val?.length > 10) {
                            callback(this.i18n('/数据/数据洞察/详情页/提示/最多添加10个维度'))
                        }
                        const arr: string[] = []
                        val.forEach((item) => {
                            if (arr.some((val) => val === JSON.stringify(item))) {
                                callback(this.i18n('/数据/数据洞察/详情页/提示/不允许添加重复的维度'))
                            } else {
                                arr.push(JSON.stringify(item))
                            }
                        })
                        callback()
                    },
                    trigger: 'change'
                }
            ],
            conditions: [
                {
                    required: false,
                    validator: (rule: any, val: MemberAnalysisCondition[], callback: any) => {
                        if (val?.length > 10) {
                            callback(this.i18n('/数据/数据洞察/详情页/提示/最多添加10个筛选条件'))
                        }
                        const arr: string[] = []
                        val.forEach((item) => {
                            if (arr.some((val) => val === JSON.stringify(item))) {
                                callback(this.i18n('/数据/数据洞察/详情页/提示/不允许添加重复的筛选条件'))
                            } else {
                                arr.push(JSON.stringify(item))
                            }
                        })
                        callback()
                    },
                    trigger: 'change'
                }
            ]
        }
    }


    // 获取指标名
    getMetricsName(item: Nullable<MemberAnalysisMetrics>) {
        // console.log('获取到的item', item, item?.value);
        return item?.memberPropName || item?.tagName || '--'
    }

    // 获取维度名
    getDimensionName(item: MemberAnalysisDimension) {
        return item.memberPropName || item.tagName || '--'
    }


    // 获取筛选条件名
    getConditionName(item: MemberAnalysisCondition) {
        return item.memberPropName || item.tagName || '--'
    }


    // 是否展示数据分桶，数值型或时间型展示
    isShowDataTong(item: MemberAnalysisDimension) {
        // console.log('展示数据分桶', item.fieldType)
        if (item.fieldType === 'num' || item.fieldType === 'date') {
            return true
        }
        return false
    }


    formatRangeListType(type: Nullable<IntervalType>) {
        if (type === IntervalType.DEFAULT_INTERVAL) {
            return 'default'
        } else if (type === IntervalType.CUSTOM_INTERVAL) {
            return 'custom'
        } else {
            return 'default'
        }
    }

    formatRangeListGroupSection(item: CustomerProfileGoupSection[]) {
        return item.map((item) => {
            const result = new GroupSection();
            result.tagValueGroupStart = item.tagValueGroupStart !== null ? String(item.tagValueGroupStart) : null;
            result.tagValueGroupEnd = item.tagValueGroupEnd !== null ? String(item.tagValueGroupEnd) : null;
            return result;
        })
    }

    // 打开数据分桶弹窗
    addDataTong(item: MemberAnalysisDimension, index: number) {
        // 需要根据index找对应的item维度，赋值
        this.currentIndex = index
        this.currentDimension = item
        this.dimensionTitleList[0] = item.memberPropName || item.memberProp || '--'
        if (item.fieldType === 'num') {
            // 数值型
            if (item.displayIntervalType && item.displayIntervals) {
                this.rangeList[0].type = this.formatRangeListType(item.displayIntervalType)
                this.rangeList[0].customArr = this.formatRangeListGroupSection(item.displayIntervals)
            } else {
                this.rangeList = [new CustomRangeInfo()]
            }

            this.$refs.customRangeDialog.open(1)
        } else if (item.fieldType === 'date') {
            // 时间型
            this.propName = item.memberPropName || item.memberProp || ''
            console.log('打开时间维度弹窗', this.propName, index, this.formData.dimensions[index]);
            this.propDisplayIntervalType = this.formData.dimensions[index].displayIntervalType || IntervalType.YEAR
            this.$refs.conditionDateSelect.open()
        }
    }


    // 维度区间，数据分桶数值型，按默认区间，自定义区间
    doSubmitRange(val: CustomRangeInfo[]) {
        this.rangeList = val || []
        if (this.rangeList.length === 0) {
            this.formData.dimensions[this.currentIndex].displayIntervalType = IntervalType.DEFAULT_INTERVAL
        } else {
            this.formData.dimensions[this.currentIndex].displayIntervalType = val[0].type === 'default' ? IntervalType.DEFAULT_INTERVAL : IntervalType.CUSTOM_INTERVAL
            let newGroups: CustomerProfileGoupSection[] = val[0].customArr.map((section) => {
                return Object.assign(new CustomerProfileGoupSection(), section)
            })
            this.formData.dimensions[this.currentIndex].displayIntervals = newGroups
        }
        console.log('数据分桶，更新了维度区间', this.formData.dimensions)
    }

    // 维度区间，数据分桶时间型，按年，按月，按日
    doSubmitDateRange(data: IntervalType) {
        console.log('提交时间区间', data)
        this.formData.dimensions[this.currentIndex].displayIntervalType = data
    }


    formatAnalysisPropType(type: string) {
        let typeNew: Nullable<AnalysisPropType> = null
        switch (type) {
            case 'average':
                typeNew = AnalysisPropType.AVG
                break
            case 'sum':
                typeNew = AnalysisPropType.SUM
                break
            case 'max':
                typeNew = AnalysisPropType.MAX
                break
            case 'min':
                typeNew = AnalysisPropType.MIN
                break
            default:
                typeNew = null
        }
        return typeNew
    }

    formatAnalysisPropName(type: string, name: string) {
        let nameNew: Nullable<string> = null
        switch (type) {
            case 'average':
                nameNew = this.i18n(name) + this.i18n('/会员/智能打标/均值')
                break
            case 'sum':
                nameNew = this.i18n(name) + this.i18n('/会员/智能打标/总和')
                break
            case 'max':
                nameNew = this.i18n(name) + this.i18n('/会员/智能打标/最大值')
                break
            case 'min':
                nameNew = this.i18n(name) + this.i18n('/会员/智能打标/最小值')
                break
            default:
                nameNew = '--'
        }
        return nameNew
    }

    formatChoiceMetrics(category: CustomerProfileCategory) {
        let operateType: Nullable<AnalysisOperateType> = null
        switch (category) {
            case CustomerProfileCategory.attribute:
                // 选择了会员属性
                operateType = AnalysisOperateType.MEMBER_PROP
                break
            case CustomerProfileCategory.tag:
                // 选择了标签
                operateType = AnalysisOperateType.TAG
                break
            default:
                operateType = AnalysisOperateType.MEMBER_COUNT
        }
        return operateType

    }

    // 提交指标item
    doSubmitMetrics(obj: BCustomerProfileTarget) {
        console.log('提交指标', obj);
        // 如果选择了标签，则存在了tagName，如果选择了用户属性，则存在memberPropName

        let objNew: MemberAnalysisMetrics = new MemberAnalysisMetrics()
        if (obj === null) {
            // 用户数
            objNew.operateType = AnalysisOperateType.MEMBER_COUNT
            objNew.memberPropName = this.i18n('用户数')

        } else {
            objNew.memberProp = obj.memberProp
            objNew.tagId = obj.tagId
            if (obj.category) {
                if (obj.category === CustomerProfileCategory.attribute) {
                    // 选择了会员属性
                    if (obj.type) {
                        // 如果有 均值，最大值，总和，最小值
                        objNew.type = this.formatAnalysisPropType(obj.type)
                        objNew.memberPropName = this.formatAnalysisPropName(obj.type as string, (obj.memberPropName) as string)
                    } else {
                        objNew.memberPropName = obj.memberPropName || '--'
                    }
                } else if (obj.category === CustomerProfileCategory.tag) {
                    // 选择了标签
                    if (obj.type) {
                        // 如果有 均值，最大值，总和，最小值
                        objNew.type = this.formatAnalysisPropType(obj.type)
                        objNew.tagName = this.formatAnalysisPropName(obj.type as string, (obj.tagName) as string)
                    } else {
                        objNew.tagName = obj.tagName || '--'
                    }
                }
                objNew.operateType = this.formatChoiceMetrics(obj.category)
            }
        }

        this.formData.metrics.push(objNew)
    }

    // 提交维度item
    doSubmitDimension(obj: BCustomerProfileDimension) {
        let objNew: MemberAnalysisDimension = new MemberAnalysisDimension()

        if (obj === null) {
            // 用户数
            objNew.memberPropName = this.i18n('用户数')
        } else {
            objNew.memberProp = obj.memberProp
            objNew.tagId = obj.tagId
            if (obj.category) {
                if (obj.category === CustomerProfileCategory.attribute) {
                    // 选择了会员属性
                    objNew.memberPropName = obj.memberPropName || '--'
                } else if (obj.category === CustomerProfileCategory.tag) {
                    // 选择了标签
                    objNew.tagName = obj.tagName || '--'
                }
            }
            objNew.memberProp = obj.memberProp || null
            objNew.tagId = obj.tagId
            objNew.fieldType = obj.type === 'num' || obj.type === 'date' ? obj.type : 'other'
            this.popType = objNew.fieldType || 'other'
        }
        console.log('提交维度', objNew);
        this.isShowDataTong(objNew)

        this.formData.dimensions.push(objNew)

    }

    // 提交筛选条件item
    doSubmitCondition(obj: MemberAnalysisCondition) {
        this.formData.conditions.push(obj)
        // 增加筛选条件后，底部查询条件及时更改
        // if (obj.memberPropName === '等级' || obj.memberPropName === '性别' || obj.memberPropName === '归属门店名称' ||
        //     obj.memberPropName === '归属门店代码' || obj.memberPropName === '会员状态' || obj.memberPropName === '注册渠道' ||
        //     obj.memberPropName === '注册渠道类型'
        // ) {
        //     obj.value = 'all'
        // }
        this.searchForm.searchCondition.push(obj)
    }

    // 删除指标
    removeMetrics(index: number) {
        this.formData.metrics.splice(index, 1)
    }

    // 删除维度
    removeDimension(index: number) {
        console.log('删除的维度', index);
        this.formData.dimensions.splice(index, 1)
    }

    // 删除筛选条件
    removeCondition(index: number) {
        this.formData.conditions.splice(index, 1)
        this.searchForm.searchCondition.splice(index, 1)
    }


    // 清空
    clearItem(type: string) {
        console.log('清空', type);
        if (type === 'target') {
            this.formData.metrics = []
        } else if (type === 'dimension') {
            this.formData.dimensions = []
        } else if (type === 'condition') {
            this.formData.conditions = []
        }
    }


    handleFeatureChange(condition: MemberAnalysisCondition, newValue: string) {
        condition.feature = newValue
        if (newValue === '过去') {
            condition.range = '之内'; // 设置过去时的默认选项
        } else if (newValue === '未来') {
            condition.range = '之内'; // 设置未来时的默认选项
        }
        this.handleTimeChange(condition);
    }


    // 下方时间型筛选条件方式改变
    handleTimeChange(condition: MemberAnalysisCondition) {
        // 相对当前时间点
        if (condition.relation === 'relative_to_now') {
            this.calculateRelativeToNow(condition);
        }
        // 相对当前时间区间
        if (condition.relation === 'relative_to_now_range') {
            this.calculateRelativeRange(condition);
        } else {
            if (condition.relation === 'have_value') {
                condition.operate = ConditionOperationType.HAVE_VALUE
            } else if (condition.relation === 'not_have_value') {
                condition.operate = ConditionOperationType.NOT_HAVE_VALUE
            }
        }
        this.$nextTick(() => {
            this.$forceUpdate();
        });
    }

    formatTooltip(type: string, start: Date, end: Date | null) {
        let tooltip: string = ''
        if (type === 'between') {
            return this.i18n('时间范围：{0}至{1}，包含开始和结束时间', [start.toLocaleDateString(), end?.toLocaleDateString() || '当前时间'])
            // tooltip = this.i18n(`/会员/智能打标/时间范围：${start.toLocaleDateString()} 至 ${end?.toLocaleDateString() || '当前时间'}，包含开始和结束时间`)
        } else if (type === 'greater_equals') {
            // 大于等于
            return this.i18n('时间范围：{0}之后，包含{1}', [start.toLocaleDateString(), start.toLocaleDateString()])
            // tooltip = this.i18n(`/会员/智能打标/时间范围：${start.toLocaleDateString()} 之后，包含${start.toLocaleDateString()}`)
        } else if (type === 'less_equals') {
            // 小于等于
            return this.i18n('时间范围：{0}之前，包含{1}', [start.toLocaleDateString(), start.toLocaleDateString()])
            // tooltip = this.i18n(`/会员/智能打标/时间范围：${start.toLocaleDateString()} 之前，包含${start.toLocaleDateString()}`)
        } else {
            tooltip = this.i18n('暂无')
        }
        return tooltip
    }

    // 相对当前时间点，对应时间计算
    calculateRelativeToNow(condition: MemberAnalysisCondition) {
        const days = parseInt(condition.number as string, 10)
        if (isNaN(days)) {
            return ''
        }
        const now = new Date();
        let start: Date | null = null;
        let end: Date | null = null;

        if (condition.feature === '未来') {
            if (condition.range === '之内') {
                condition.operate = ConditionOperationType.BETWEEN // 区间
                start = new Date(now.getTime() + (1) * 86400000);
                end = new Date(now.getTime() + (days) * 86400000);
                this.tooltipContent = this.formatTooltip('between', start, end)

            } else {
                condition.operate = ConditionOperationType.GREATER_EQUALS
                start = new Date(now.getTime() + (days + 1) * 86400000);
                this.tooltipContent = this.formatTooltip('greater_equals', start, null)
            }
        } else if (condition.feature === '过去') {
            if (condition.range === '之内') {
                condition.operate = ConditionOperationType.BETWEEN // 区间
                start = new Date(now.getTime() - (days - 1) * 86400000);
                end = now;
                this.tooltipContent = this.formatTooltip('between', start, end)
            } else {
                condition.operate = ConditionOperationType.LESS_EQUALS
                start = new Date(now.getTime() - (days) * 86400000);
                this.tooltipContent = this.formatTooltip('less_equals', start, null)
            }
        }
        // 绑定到condition对象用于后续查询
        condition.beginDate = start || null;
        condition.endDate = end || null;

    }

    // 相对当前时间区间，对应时间计算
    calculateRelativeRange(condition: MemberAnalysisCondition) {
        // 类型校验
        const beginDays = Number(condition.beginNumber);
        const endDays = Number(condition.endNumber);
        if (isNaN(beginDays)) return '起始天数无效';
        if (isNaN(endDays)) return '结束天数无效';
        const now = new Date();
        let startDate: Date;
        let endDate: Date;
        condition.operate = ConditionOperationType.BETWEEN // 区间

        if (condition.feature === '过去') {
            // 处理过去时间区间
            startDate = new Date(now.getTime() - (beginDays) * 86400000);
            endDate = new Date(now.getTime() - (endDays - 1) * 86400000);

            // 自动校正日期顺序（保证startDate <= endDate）
            if (startDate > endDate) {
                [startDate, endDate] = [endDate, startDate];
            }
            this.tooltipRangeContent = this.formatTooltip('between', startDate, endDate)
        } else {
            // 处理未来时间区间
            startDate = new Date(now.getTime() + (beginDays + 1) * 86400000);
            endDate = new Date(now.getTime() + (endDays) * 86400000);

            // // 自动校正日期顺序
            // if (startDate > endDate) {
            //     [startDate, endDate] = [endDate, startDate];
            // }
            this.tooltipRangeContent = this.formatTooltip('between', startDate, endDate)
        }
        // 绑定到condition对象用于后续查询
        condition.beginDate = startDate;
        condition.endDate = endDate;
    }


    // 生成分析按钮
    handleSubmit() {
        if (this.searchForm.searchCondition.length > 0 && this.editType !== 'create') {
            this.$refs.searchForm.clearValidate();
        }
        this.$refs.form.validate().then(() => {
            this.page.page = 0;
            this.getAnalysisApi()
        })
    }


    // 生成分析调用接口
    async getAnalysisApi() {
        const loading = CommonUtil.Loading()

        this.formData.dimensions.forEach((item) => {
            if (item.displayIntervalType === null && item.fieldType === 'num')
                item.displayIntervalType = IntervalType.DEFAULT_INTERVAL
        })
        this.formData.dimensions.forEach((item) => {
            if (item.displayIntervalType === null && item.fieldType === 'date')
                item.displayIntervalType = IntervalType.YEAR
        })

        this.queryData = new MemberAnalysisFilter()
        this.queryData.memberAnalysis = this.formData
        if (this.isShowSearchCondition) {
            // this.queryData.conditions = this.searchForm.searchCondition
        } else {
            this.queryData.conditions = []
        }

        this.queryData.page = this.page.page
        this.queryData.pageSize = this.page.pageSize

        console.log('生成分析，提交的数据', JSON.stringify(this.queryData));
        MemberAnalysisApi.query(this.queryData).then((res) => {
            if (res && res.code === 2000) {
                // this.tableData = res.data || []
                this.searchForm.searchCondition = this.formData.conditions.slice()
                this.searchForm.searchCondition.forEach(async (item) => {
                    if (item.fieldType === 'date') {
                        // 处理时间型筛选条件
                        // console.log('时间型筛选条件', item);
                        await this.getTimeRange(item)

                    }
                })
                if (res.data) {
                    this.processData(res.data)
                    this.page.total = res.total || 0
                }
                // this.tableData = res.data || []
                this.$forceUpdate()
                console.log('生成分析，生成表格数据')
                this.isShowSearchCondition = true;
            } else {
                console.log('分析失败');
                throw new Error(res.msg!)
            }
        }).catch((err) => {
            this.$message.error(err.message)
        }).finally(() => {
            loading.close()
        })
    }

    // 导出
    handleExport() {
        this.$refs.form.validate().then(() => {
            this.$confirm(this.i18n("是否确认导出？") as string, "提示", {
                confirmButtonText: this.formatI18n("/公用/按钮", "确定") as string,
                cancelButtonText: this.formatI18n("/公用/按钮", "取消") as string,
            }).then(() => {
                const loading = CommonUtil.Loading()
                // 接口改为传id
                if (!this.dtlId) {

                }
                MemberAnalysisApi.exportMemberAnalysis(this.dtlId).then((res) => {
                    if (res.code === 2000) {
                        this.$message.success(this.i18n('导出成功'))
                        console.log('导出成功', res);
                    } else {
                        console.log('导出失败');
                        throw new Error(res.msg!)
                    }
                }).catch((err) => {
                    this.$message.error(err.message)
                }).finally(() => {
                    loading.close()
                })
            })
        })
    }


    // 点击保存，如果是编辑模式直接保存，新建模式需弹窗填写名称
    handleOpenDialog() {
        this.$refs.form.validate().then(() => {
            if (this.editType === 'edit') {
                this.handleSave()
                return
            }
            this.dialogVisible = true
        })
    }

    // 关闭弹窗
    handleClose() {
        this.dialogVisible = false
    }

    // 调用接口保存会员分析数据
    handleSave() {
        this.$refs.form.validate().then(() => {
            // 保存的时候，如果维度是数值型 或 时间型，且没有点击确定按默认值来，num类型的displayIntervalType为default，date类型的为year
            this.formData.dimensions.forEach((item) => {
                if (item.displayIntervalType === null && item.fieldType === 'num')
                    item.displayIntervalType = IntervalType.DEFAULT_INTERVAL
            })
            this.formData.dimensions.forEach((item) => {
                if (item.displayIntervalType === null && item.fieldType === 'date')
                    item.displayIntervalType = IntervalType.YEAR
            })

            this.formData.conditions = this.searchForm.searchCondition.filter(item => {
                if (item.fieldType === 'num') {
                    // 数值型
                    if (item.operate === ConditionOperationType.BETWEEN) {
                        return item.beginValue && item.endValue
                    } else if (item.operate !== ConditionOperationType.HAVE_VALUE && item.operate !== ConditionOperationType.NOT_HAVE_VALUE) {
                        return item.operate && item.value
                    } else {
                        return item.operate
                    }
                } else if (item.fieldType === 'date') {
                    if (item.relation === 'absolute') {
                        return item.beginDate
                    } else if (item.relation === 'relative_to_now') {
                        return item.feature && item.range && item.number
                    } else if (item.relation === 'relative_to_now_range') {
                        return item.feature && item.beginNumber && item.endNumber
                    } else {
                        // 有值，无值
                        return item.operate
                    }
                } else {
                    // 其他型
                    if (item.operate === ConditionOperationType.REGEXP || item.operate === ConditionOperationType.NOT_REGEXP) {
                        return item.value
                    } else if (item.operate === ConditionOperationType.HAVE_VALUE || item.operate === ConditionOperationType.NOT_HAVE_VALUE) {
                        return item.operate
                    } else {
                        return item.operate && item.value
                    }
                }
            })

            // this.formData.conditions = this.formData.conditions.filter(item => {
            //     if (item.fieldType !== 'date' && item.fieldType !== 'num') {
            //         if (item.memberPropName === '等级' || item.memberPropName === '性别' || item.memberPropName === '归属门店名称' ||
            //             item.memberPropName === '归属门店代码' || item.memberPropName === '会员状态' || item.memberPropName === '注册渠道' ||
            //             item.memberPropName === '注册渠道类型'
            //         ) {
            //             if (item.value === null) {
            //                 return false;
            //             }
            //         }
            //     }
            //     return true;
            // })

            if (this.editType === 'create') {
                this.$refs.labelForm.validate().then(() => {
                    const loading = CommonUtil.Loading()
                    this.formData.name = this.labelForm.labelName
                    console.log('新建', JSON.stringify(this.formData));
                    MemberAnalysisApi.save(this.formData).then((res) => {
                        if (res && res.code === 2000) {
                            console.log('保存成功', res);
                            this.$message.success(this.i18n('/公用/js提示信息/保存成功'))
                            this.dialogVisible = false

                            this.$router.push({
                                name: 'member-vip-analysis'
                            })
                        } else {
                            console.log('保存失败');
                            throw new Error(res.msg!)
                        }
                    }).catch((err) => {
                        this.$message.error(err.message)
                    }).finally(() => {
                        loading.close()
                    })

                })
            } else {
                // 修改
                console.log('修改', JSON.stringify(this.formData));
                const loading = CommonUtil.Loading()
                MemberAnalysisApi.modify(this.formData).then((res) => {
                    if (res && res.code === 2000) {
                        console.log('保存成功', res);
                        this.$message.success(this.i18n('/公用/js提示信息/保存成功'))
                        this.dialogVisible = false

                        this.$router.push({
                            name: 'member-vip-analysis'
                        })
                    } else {
                        console.log('保存失败');
                        throw new Error(res.msg!)
                    }
                }).catch((err) => {
                    this.$message.error(err.message)
                }).finally(() => {
                    loading.close()
                })
            }
        })
    }

    // 返回列表
    handleReturnBack() {
        this.$router.back();
    }


    // 重置查询条件
    handleResetSearchCondition() {
        this.isShowSearchCondition = true
        this.searchForm.searchCondition = this.formData.conditions.slice()
        this.searchForm.searchCondition.forEach((condition) => {
            condition.beginDate = null
            condition.endDate = null
            condition.number = ''
            condition.beginNumber = ''
            condition.endNumber = ''
            condition.operate = ConditionOperationType.EQUALS
            condition.feature = '过去'
            condition.range = '之内'
            condition.relation = 'absolute'
            condition.value = null

        })
        this.queryData.conditions = []
        this.queryData.page = 0
        this.queryData.pageSize = 20
        this.handleQuery()

    }


    // 下部分table筛选条件
    handleSearch(jump: string | null) {
        if (this.editType !== 'create' && this.searchForm.searchCondition.length > 0) {
            this.$refs.searchForm.clearValidate();
        }

        // this.$refs.searchForm.validate().then(() => {
        console.log('表单验证成功')

        this.queryData = new MemberAnalysisFilter()

        this.formData.dimensions.forEach((item) => {
            if (item.displayIntervalType === null && item.fieldType === 'num')
                item.displayIntervalType = IntervalType.DEFAULT_INTERVAL
        })


        this.formData.dimensions.forEach((item) => {
            if (item.displayIntervalType === null && item.fieldType === 'date')
                item.displayIntervalType = IntervalType.YEAR
        })
        let conditions: MemberAnalysisCondition[] = []


        this.queryData.memberAnalysis = this.formData
        this.queryData.conditions = this.searchForm.searchCondition.filter(item => {
            if (item.fieldType === 'num') {
                // 数值型
                if (item.operate === ConditionOperationType.BETWEEN) {
                    return item.beginValue && item.endValue
                } else if (item.operate !== ConditionOperationType.HAVE_VALUE && item.operate !== ConditionOperationType.NOT_HAVE_VALUE) {
                    return item.operate && item.value
                } else {
                    return item.operate
                }
            } else if (item.fieldType === 'date') {
                if (item.relation === 'absolute') {
                    return item.beginDate
                } else if (item.relation === 'relative_to_now') {
                    return item.feature && item.range && item.number
                } else if (item.relation === 'relative_to_now_range') {
                    return item.feature && item.beginNumber && item.endNumber
                } else {
                    // 有值，无值
                    return item.operate
                }
            } else {
                // 其他型
                if (item.operate === ConditionOperationType.REGEXP || item.operate === ConditionOperationType.NOT_REGEXP) {
                    return item.value
                } else if (item.operate === ConditionOperationType.HAVE_VALUE || item.operate === ConditionOperationType.NOT_HAVE_VALUE) {
                    return item.operate
                } else {
                    return item.operate && item.value
                }
            }
        })
        // this.queryData.memberAnalysis.conditions = this.queryData.conditions.slice()
        console.log('过滤出的conditions', this.queryData.conditions)
        if (jump === 'jump') {
            this.queryData.page = this.page.page
        } else {
            this.queryData.page = 0
            this.page.page = 0
        }
        this.queryData.pageSize = this.page.pageSize
        console.log('下部分table筛选条件，提交的数据', JSON.stringify(this.queryData));
        this.handleQuery()
        // })
    }


    handleQuery() {
        const loading = CommonUtil.Loading()
        MemberAnalysisApi.query(this.queryData).then((res) => {
            if (res && res.code === 2000) {
                console.log('返回的数据', res);
                if (res.data) {
                    this.processData(res.data)
                    this.page.total = res.total || 0
                    // this.tableData = res.data
                }
            } else {
                console.log('获取失败');
                throw new Error(res.msg!)
            }
        }).catch((err) => {
            this.$message.error(err.message)
        }).finally(() => {
            loading.close()
        })
    }



    closePopover() {
        this.$refs.portraitIndicatorMetrics?.close()
        this.$refs.portraitIndicatorDimension?.close()
        this.$refs.portraitIndicatorCondition?.close()
    }

    get panelArray() {
        return [
            {
                name: this.i18n('/公用/菜单/会员分析'),
                url: 'member-vip-analysis'
            },
            {
                name: this.dtlTitle,
                url: ''
            },
        ]
    }


    editable: boolean = true; // 详情页，数据分桶改为不可编辑

    created() {
        const { type, id } = this.$route.query

        this.dtlId = id || ''
        this.editType = (type as string) || 'create'
        if (this.editType === 'create') {
            // 新建页，导出按钮隐藏
            this.isShowExportButton = false
            this.dtlTitle = this.i18n('/数据/数据洞察/列表页/新建会员分析')
        }
        else {
            if (this.dtlId) {
                this.getInitEditData()

                if (this.editType === 'dtl') {
                    this.isDtl = true
                    this.editable = false
                }

            }
        }

        this.getGradeList()
        this.getStore("")
        this.getChannels()
    }


    // 新增临时存储变量
    tempDateRange: any = []

    // 处理日期变化
    handleDateChange(condition: any) {
        if (this.tempDateRange && this.tempDateRange.length === 2) {
            condition.beginDate = this.tempDateRange[0]
            condition.endDate = this.tempDateRange[1]
        } else {
            condition.beginDate = null
            condition.endDate = null
        }

    }


    // 编辑模式，获取详情数据
    async getInitEditData() {
        const loading = CommonUtil.Loading()
        MemberAnalysisApi.getBy(this.dtlId).then((res) => {
            if (res && res.code === 2000) {
                console.log('编辑，获取详情数据', res.data);
                this.formData = res.data || new MemberAnalysis()
                this.searchForm.searchCondition = this.formData.conditions.slice()
                this.searchForm.searchCondition.forEach(async (condition) => {
                    if (condition.fieldType === 'date') {
                        // 处理时间型筛选条件
                        // console.log('时间型筛选条件', condition);
                        await this.getTimeRange(condition)
                        if (condition.beginDate && condition.endDate) {
                            this.tempDateRange = [condition.beginDate, condition.endDate]
                        }
                    }
                })
                this.isShowSearchCondition = true // 展示底部筛选条件

                this.dtlTitle = res.data?.name || '--'


                this.queryData.memberAnalysis = this.formData
                // this.queryData.conditions = this.formData.conditions.slice()
                this.queryData.conditions = this.searchForm.searchCondition.filter(item => {
                    if (item.fieldType === 'num') {
                        // 数值型
                        if (item.operate === ConditionOperationType.BETWEEN) {
                            return item.beginValue && item.endValue
                        } else if (item.operate !== ConditionOperationType.HAVE_VALUE && item.operate !== ConditionOperationType.NOT_HAVE_VALUE) {
                            return item.operate && item.value
                        } else {
                            return item.operate
                        }
                    } else if (item.fieldType === 'date') {
                        if (item.relation === 'absolute') {
                            return item.beginDate
                        } else if (item.relation === 'relative_to_now') {
                            return item.feature && item.range && item.number
                        } else if (item.relation === 'relative_to_now_range') {
                            return item.feature && item.beginNumber && item.endNumber
                        } else {
                            // 有值，无值
                            return item.operate
                        }
                    } else {
                        // 其他型
                        if (item.operate === ConditionOperationType.REGEXP || item.operate === ConditionOperationType.NOT_REGEXP) {
                            return item.value
                        } else if (item.operate === ConditionOperationType.HAVE_VALUE || item.operate === ConditionOperationType.NOT_HAVE_VALUE) {
                            return item.operate
                        } else {
                            return item.operate && item.value
                        }
                    }
                })
                this.queryData.pageSize = this.page.pageSize
                this.queryData.page = this.page.page
                this.handleQuery()
            } else {
                console.log('获取失败');
                throw new Error(res.msg!)
            }
        }).catch((err) => {
            console.log('编辑，获取详情数据失败', err);
            this.$message.error(err.message)
        }).finally(() => {
            loading.close()
        })
    }


    // 计算时间相对天数
    calculateConditionNumber(date: Date) {
        if (!(date instanceof Date)) {
            console.error('calculateConditionNumber 方法接收到的参数不是 Date 类型:', date);
            return '0';
        }
        const now = new Date();
        let days: number = 0
        if (now > date) {
            days = Math.round((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
        } else {
            days = Math.round((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
        }

        // console.log('相对时间', days);
        return days.toString()
    }


    // 计算初始时间筛选条件
    // 逆向计算condition的属性
    async getTimeRange(condition: MemberAnalysisCondition) {
        let beginDate: Date | null = null;
        let endDate: Date | null = null;
        if (condition.beginDate) {
            beginDate = new Date(condition.beginDate);
            this.tempDateRange[0] = beginDate
        }
        if (condition.endDate) {
            endDate = new Date(condition.endDate);
            this.tempDateRange[1] = endDate
        }
        //     console.log('初始化时间', condition)
        //     const now = new Date();
        //     let beginDate: Date | null = null;
        //     let endDate: Date | null = null;
        //     console.log('beginDATE', condition.beginDate)
        //     if (condition.beginDate) {
        //         beginDate = new Date(condition.beginDate);
        //     }
        //     if (condition.endDate) {
        //         endDate = new Date(condition.endDate);
        //     }

        //     const operate = condition.operate;
        //     if (!condition.operate && !condition.beginDate && !condition.endDate) {
        //         this.$set(condition, 'relation', 'absolute');
        //         condition.operate = ConditionOperationType.EQUALS
        //         return
        //     }
        //     let begin: string | null = null;
        //     let end: string | null = null;
        //     let newNow: string | null = null;
        //     if (beginDate) {
        //         begin = beginDate.toISOString().split('T')[0];
        //     }
        //     if (endDate) {
        //         end = endDate.toISOString().split('T')[0];
        //     }
        //     if (now) {
        //         newNow = now.toISOString().split('T')[0];
        //     }
        //     if (operate === ConditionOperationType.BETWEEN) {
        //         console.log('between!!!!!!!!!!')
        //         this.$set(condition, 'relation', 'absolute')
        //         this.$set(condition, 'beginDate', condition.beginDate);
        //         this.$set(condition, 'endDate', condition.endDate);
        //     } else if (operate === ConditionOperationType.GREATER_EQUALS || operate === ConditionOperationType.GREATER) {
        //         if (beginDate && begin && newNow) {
        //             if (begin > newNow) {
        //                 // 未来之后
        //                 this.$set(condition, 'relation', 'relative_to_now');
        //                 this.$set(condition, 'feature', '未来');
        //                 this.$set(condition, 'range', '之后');
        //                 this.$set(condition, 'number', Math.floor((beginDate.getTime() - now.getTime()) / 86400000));
        //                 if (operate === ConditionOperationType.GREATER_EQUALS) {
        //                     this.tooltipContent = this.formatTooltip('greater_equals', beginDate, null)
        //                 } else {
        //                     this.tooltipContent = this.formatTooltip('greater_equals', new Date(beginDate.getTime() + 86400000), null)
        //                 }

        //             } else if (begin <= newNow) {
        //                 // 绝对时间
        //                 this.$set(condition, 'relation', 'absolute');
        //                 condition.value = new Date(beginDate.getTime() - 86400000).toString()
        //                 // this.$set(condition, 'number', Math.floor((beginDate.getTime() - now.getTime()) / 86400000));
        //             }
        //         }

        //     } else if (operate === ConditionOperationType.LESS_EQUALS || operate === ConditionOperationType.LESS) {
        //         if (beginDate && begin && newNow) {
        //             if (begin < newNow) {
        //                 // 过去之前
        //                 this.$set(condition, 'relation', 'relative_to_now');
        //                 this.$set(condition, 'feature', '过去');
        //                 this.$set(condition, 'range', '之前');
        //                 this.$set(condition, 'number', Math.floor((now.getTime() - beginDate.getTime()) / 86400000));
        //                 if (operate === ConditionOperationType.LESS_EQUALS) {
        //                     this.tooltipContent = this.formatTooltip('less_equals', beginDate, null)
        //                 } else {
        //                     this.tooltipContent = this.formatTooltip('less_equals', new Date(beginDate.getTime() - 86400000), null)
        //                 }

        //             } else if (begin >= newNow) {
        //                 this.$set(condition, 'relation', 'absolute');
        //             }
        //         }

        //     } else if (operate === ConditionOperationType.HAVE_VALUE) {
        //         condition.beginDate = null
        //         condition.endDate = null
        //         condition.value = null
        //         this.$set(condition, 'relation', 'have_value');
        //     } else if (operate === ConditionOperationType.NOT_HAVE_VALUE) {
        //         condition.beginDate = null
        //         condition.endDate = null
        //         condition.value = null
        //         this.$set(condition, 'relation', 'not_have_value');
        //     } else if (operate === ConditionOperationType.EQUALS || operate === ConditionOperationType.NOT_EQUALS) {
        //         this.$set(condition, 'relation', 'absolute');
        //     }
    }


    memberLevel: Grade[] = [];
    // 获取会员等级列表
    getGradeList() {
        GradeApi.listGrade("")
            .then((resp: any) => {
                this.memberLevel = resp.data;
            })
            .catch((error: any) => {
                if (error && error.message) {
                    this.$message.error(error.message);
                }
            });
    }

    stores: any = [];
    // 获取归属门店列表
    getStore(value: string) {
        let params: RSOrgFilter = new RSOrgFilter();
        params.idNameLikes = value;
        params.page = 0;
        params.pageSize = 0;
        OrgApi.query(params)
            .then((resp: any) => {
                if (resp && resp.code === 2000) {
                    this.stores = resp.data;
                }
            })
            .catch((error) => {
                if (error && error.message) {
                    this.$message.error(error.message);
                }
            });
    }


    channels: RSChannelManagement[] = [];
    // 获取注册渠道列表
    getChannels() {
        let param: RSChannelManagementFilter = new RSChannelManagementFilter();
        ChannelManagementApi.query(param)
            .then((resp: any) => {
                if (resp && resp.code === 2000) {
                    this.channels = resp.data
                    console.log('注册渠道列表', this.channels)
                }
            })
            .catch((error: any) => {
                if (error && error.message) {
                    this.$message.error(error.message);
                }
            });
    }



    doRemoteMethod(value: string) {
        this.getStore(value);
    }

    // doRemoteChannelMethod(value: string) {
    //     this.getChannels();
    // }





    columns: { prop: string; label: string }[] = [];

    processData(data: MemberAnalysisQueryResponse[]) {
        this.tableData = []
        // console.log('调用了吗，处理表格数据')
        if (data.length === 0) {
            this.page.total = 0
            this.page.page = 0
            return;
        }
        // 提取表头
        const firstItem = data[0];
        const allProps: string[] = [];
        [...firstItem.dimensions, ...firstItem.metrics].forEach(subItem => {
            let prop = subItem.memberPropName || subItem.tagName || subItem.memberProp || subItem.tagId;
            if (subItem.type !== null) {
                if (subItem.type === AnalysisPropType.SUM) {
                    prop += '总和'
                } else if (subItem.type === AnalysisPropType.AVG) {
                    prop += '平均值'
                } else if (subItem.type === AnalysisPropType.MAX) {
                    prop += '最大值'
                } else if (subItem.type === AnalysisPropType.MIN) {
                    prop += '最小值'
                }
            }

            if (prop) {
                allProps.push(prop);
            } else if ('operateType' in subItem) {
                if (subItem.operateType === AnalysisOperateType.MEMBER_COUNT) {
                    // 用户数
                    allProps.push(this.i18n('用户数'))
                }
            }
        });
        this.columns = allProps.map(prop => ({ prop, label: prop }));
        console.log('表头', this.columns)

        // 处理表格数据
        this.tableData = data.map(item => {
            const row: any = {};
            [...item.dimensions, ...item.metrics].forEach(subItem => {
                let prop = subItem.memberPropName || subItem.tagName || subItem.memberProp || subItem.tagId;
                // if ('operateType' in subItem) {
                //     if (subItem.operateType === AnalysisOperateType.MEMBER_COUNT) {
                //         // 用户数
                //         // prop = this.i18n('用户数')
                //     }
                // }
                if (prop) {
                    // 会员状态映射
                    if (subItem.value === 'Using') {
                        subItem.value = this.i18n('/会员/会员资料/使用中')
                    }
                    else if (subItem.value === 'Blocked') {
                        subItem.value = this.i18n('/会员/会员资料/已冻结')
                    }
                    else if (subItem.value === 'Unactivated') {
                        subItem.value = this.i18n('/会员/会员资料/未激活')
                    }
                    // 会员等级映射,memberLevel
                    this.memberLevel.forEach((level) => {
                        if (level.code === subItem.value) {
                            subItem.value = level.name
                        }
                    })
                    // 注册渠道类型映射
                    this.channels.forEach((channel) => {
                        if (channel.channel) {
                            if (channel.channel.typeId === subItem.value) {
                                subItem.value = channel.name
                            }
                        }
                    })
                    if (prop === this.i18n('用户数')) {
                        console.log('用户数', subItem.value)
                    }
                    // console.log('处理表格数据', subItem.memberPropName, subItem.tagName, subItem.value);
                    row[prop] = subItem.value || '-';
                    console.log('row[prop]', prop, row[prop])
                }
            });
            return row;
        });
        console.log('处理表格数据', JSON.stringify(this.tableData))
    }

    // // 处理后的表格数据
    // get processedData() {
    //     return this.tableData.map(item => {
    //         const row: { [key: string]: any } = {}

    //         // 处理维度
    //         item.dimensions.forEach((d, index) => {
    //             const prop = this.getPropName(d, index, 'dimensions')
    //             row[prop] = d.value !== null ? d.value : ''
    //         })

    //         // 处理指标
    //         item.metrics.forEach((m, index) => {
    //             const prop = this.getPropName(m, index, 'metrics')
    //             row[prop] = m.value !== null ? m.value : ''
    //         })

    //         return row
    //     })
    // }


    // // 处理后的表头数据
    // get dimensionHeaders() {
    //     return this.getHeaders('dimensions')
    // }
    // get metricHeaders() {
    //     return this.getHeaders('metrics')
    // }

    // // 生成表头结构
    // getHeaders(type: 'dimensions' | 'metrics') {
    //     if (this.tableData.length === 0) return []
    //     const firstItem = this.tableData[0][type]
    //     return firstItem.map((item, index) => ({
    //         prop: this.getPropName(item, index, type),
    //         source: item
    //     }))
    // }
    // // 获取表头属性名
    // getPropName(item: any, index: number, type: string) {
    //     if (item.operateType = AnalysisOperateType.MEMBER_COUNT) {
    //         // 用户数
    //         return this.i18n('用户数')

    //     }
    //     return item.memberPropName
    //         || item.tagName
    //         || item.memberProp
    //         || item.tagId
    //         || `${type}_${index}`
    // }

    // // 获取显示标签（带多语言处理）
    // getHeaderLabel(header: any) {
    //     const { memberPropName, tagName, memberProp, type } = header.source;
    //     let baseLabel = this.i18n(memberPropName || tagName || memberProp || header.prop);
    //     if (type === AnalysisPropType.MAX) {
    //         baseLabel += this.i18n('最大值');
    //     } else if (type === AnalysisPropType.MIN) {
    //         baseLabel += this.i18n('最小值');
    //     }else if (type === AnalysisPropType.SUM) {
    //         baseLabel += this.i18n('总和');
    //     }else if (type === AnalysisPropType.AVG) {
    //         baseLabel += this.i18n('平均值');
    //     }

    //     return baseLabel
    // }


    validateNumberRange(rule: any, value: number | null, callback: any) {
        if (value === null) {
            callback();
            return;
        }
        // 转换为数值类型
        const numValue = typeof value === 'string' ? parseFloat(value) : value;
        if (isNaN(numValue)) {
            callback(new Error(this.i18n('/数据/数据洞察/请输入有效数值')));
            return;
        }
        if (numValue < 0 || numValue > 99999999.99) {
            callback(new Error(this.i18n('/数据/数据洞察/数值范围在0到99999999.99之间')));
        } else if (!/^\d+(\.\d{1,2})?$/.test(numValue.toString())) {
            callback(new Error(this.i18n('/数据/数据洞察/最多只能有两位小数')));
        } else {
            callback();
        }
    }

    validateEndValueGreaterThanBeginValue(rule: any, value: number | null, callback: any, source: any) {
        const beginValue = source.beginValue;
        if (beginValue !== null && value !== null && value <= beginValue) {
            callback(new Error(this.i18n('/数据/数据洞察/结束值必须大于开始值')));
        } else {
            callback();
        }
    }

    validateIntegerRange(rule: any, value: number | null, callback: any) {
        if (value === null) {
            callback();
            return;
        }
        if (!Number.isInteger(value) || value < 1 || value > 99999) {
            callback(new Error(this.i18n('/数据/数据洞察/输入值必须是1到99999之间的整数')));
        } else {
            callback();
        }
    }



    itemRule(index: number) {
        return {
            validator: (rule: any, value: any, callback: any) => {
                const item = this.searchForm.searchCondition[index]
                let hasError = false;
                const fieldType = item.fieldType
                // 仅当条件完整时进行校验
                // if (this.shouldValidate(item)) {
                // 数值类型校验
                if (item.fieldType === 'num') {
                    if (item.operate !== 'HAVE_VALUE' && item.operate !== 'NOT_HAVE_VALUE') {
                        if (item.operate === 'BETWEEN') {
                            if (!item.beginValue || !item.endValue) {
                                callback(new Error(this.i18n('/公用/表单校验/请填写必填项')))
                                hasError = true;
                            }
                        }
                        if (!item.value) {
                            callback(new Error(this.i18n('/公用/表单校验/请选择必选项')))
                            hasError = true;
                        }
                    }
                }
                // 时间类型校验
                else if (item.fieldType === 'date') {
                    if (!item.relation) {
                        callback(new Error(this.i18n('/公用/表单校验/请选择必选项')))
                        hasError = true;
                    } else {
                        if (item.relation === 'absolute') { // 绝对时间
                            if (!item.operate || !item.beginDate) {
                                callback(new Error(this.i18n('/公用/表单校验/请填写必填项')))
                                hasError = true;
                            }
                        }
                        if (item.relation === 'relative_to_now') { // 相对当前时间点
                            if (!item.feature || !item.number || !item.range) {
                                callback(new Error(this.i18n('/公用/表单校验/请填写必填项')))
                                hasError = true;
                            }
                        }
                        if (item.relation === 'relative_to_now_range') { // 相对当前时间区间
                            if (!item.feature || !item.beginNumber || !item.endNumber) {
                                callback(new Error(this.i18n('/公用/表单校验/请选择必选项')))
                                hasError = true;
                            }
                        }
                    }
                } else {
                    // 其他类型
                    if (!item.operate) {
                        callback(new Error(this.i18n('/公用/表单校验/请选择必选项')))
                        hasError = true;
                    } else {
                        if (item.operate === 'BETWEEN') {
                            if (!item.beginValue || !item.endValue) {
                                callback(new Error(this.i18n('/公用/表单校验/请填写必填项')))
                                hasError = true;
                            }
                        }
                        else if (item.operate !== 'HAVE_VALUE' && item.operate !== 'NOT_HAVE_VALUE') {
                            if (item.operate !== 'EQUALS' && item.operate !== 'NOT_EQUALS') {
                                if (item.memberPropName !== '等级' && item.memberPropName !== '性别' && item.memberPropName !== '归属门店名称' &&
                                    item.memberPropName !== '归属门店代码' && item.memberPropName !== '会员状态' && item.memberPropName !== '注册渠道' &&
                                    item.memberPropName !== '注册渠道类型'
                                ) {
                                    if (!item.value) {
                                        callback(new Error(this.i18n('/公用/表单校验/请选择必选项')))
                                        hasError = true;
                                    }
                                }
                            }
                        }
                    }
                }
                if (!hasError) {
                    callback();
                }
            },
            trigger: [''],
        }
    }

    // handleGetList() {
    //     this.queryData = new MemberAnalysisFilter()

    //         this.formData.dimensions.forEach((item) => {
    //             if (item.displayIntervalType === null && item.fieldType === 'num')
    //                 item.displayIntervalType = IntervalType.DEFAULT_INTERVAL
    //         })
    //         this.formData.dimensions.forEach((item) => {
    //             if (item.displayIntervalType === null && item.fieldType === 'date')
    //                 item.displayIntervalType = IntervalType.YEAR
    //         })

    //         this.queryData.memberAnalysis = this.formData
    //         this.queryData.conditions = this.searchForm.searchCondition || []
    //         this.queryData.page = this.page.page
    //         this.queryData.pageSize = this.page.pageSize

    // }

    onHandleCurrentChange(val: number) {
        this.page.page = val - 1;
        this.handleSearch('jump');
    }

    onHandleSizeChange(val: number) {
        this.page.page = 0;
        this.page.pageSize = val;
        this.handleSearch(null);
    }


};

