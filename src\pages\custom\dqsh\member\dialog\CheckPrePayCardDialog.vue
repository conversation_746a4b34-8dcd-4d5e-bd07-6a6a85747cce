<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:03
 * @LastEditTime: 2025-03-19 14:14:40
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\custom\dqsh\member\dialog\CheckPrePayCardDialog.vue
 * 记得注释
-->
<template>
    <el-dialog :before-close="doBeforeClose" :close-on-click-modal="false"
               :title="title"
               :visible.sync="dialogShow" append-to-body class="check-prepay-card-dialog">
        <div class="wrap">
            <div class="flex-wrap" style="    height: 450px;
    overflow: auto;">
                <div class="item" v-for="item in prePayCards">
                    <div class="content" style="font-size: 16px;font-weight: 500;margin: 20px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;text-align: left">{{item.name}}
                        <span class="inner" style="background-color: #ff9933;text-align: center" v-if="item.state === 'UNACTIVATED'">未激活</span>
                        <span class="inner" style="background-color: #36f;text-align: center" v-if="item.state === 'PRESENTING'">转赠中</span>
                        <span class="inner"
                              style="background-color: #3c0;;text-align: center"
                              v-if="item.state === 'USING'">使用中</span>
                        <span class="inner"
                              style="background-color: #b7b7b7;;text-align: center" v-if="item.state === 'CANCELLED'">已作废</span>
                        <span class="inner"
                              style="background-color: #7f36c1;text-align: center"
                              v-if="item.state === 'USED'">已使用</span>
                        <span class="inner"
                              style="background-color: #ffcc33;text-align: center"
                              v-if="item.state === 'LOST'">已挂失</span>
                        <span class="inner"
                              style="background-color: #CC0033;text-align: center"
                              v-if="item.state === 'FROZEN'">已冻结</span>
                    </div>
                    <div class="content">
                        <div style="text-align: left;padding-left: 20px">卡号：{{item.code}}</div>
                        <div style="text-align: left;padding-left: 20px">有效期至：{{item.expireDate | dateFormate2}}
                            <span style="color: green" v-if="item.expired == null">-</span>
                            <span style="color: green" v-else-if="item.expired">已过期</span>
                            <span style="color: red" v-else>未过期</span></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="dialog-footer" slot="footer">
            <el-button @click="doCancel">取 消</el-button>
            <el-button @click="doModalClose" size="small" type="primary">确 定</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts" src="./CheckPrePayCardDialog.ts">
</script>

<style lang="scss" scoped>
.check-prepay-card-dialog{
    display: flex;
    align-items: center;
    justify-content: center;
    .wrap{
        // height: 445px;
        .item{
            width: 280px;
            // height: 108px;
            border-radius: 5px;
            display: inline-block;
            margin-bottom: 24px;
            background: rgba(0, 0, 0, 0.0470588235294118);
            margin-right: 10px;
            .content{
                text-align: center;
                .inner{
                    display: inline-block;
                    width: 48px;
                    height: 20px;
                    font-size: 14px;
                    color: white
                }
            }
        }
    }
    .el-dialog{
        width: 660px !important;
        height: 600px !important;
    }
}
</style>