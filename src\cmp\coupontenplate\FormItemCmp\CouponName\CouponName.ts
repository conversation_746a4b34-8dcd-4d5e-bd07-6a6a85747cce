/*
 * @Author: 黎钰龙
 * @Date: 2023-08-25 11:54:09
 * @LastEditTime: 2023-09-18 10:51:21
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\coupontenplate\FormItemCmp\CouponName\CouponName.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  name: 'CouponName'
})
@I18nPage({
  prefix: [
    '/公用/券模板'
  ],
  auto: true
})
export default class CouponName extends Vue {
  @Prop()
  ruleForm: any;

  @Prop()
  copyFlag: 'add' | 'edit' | 'copy';

  rules= [
    { required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" },
  ]
};