export default class BPrepayCardHistoryHstFilter {
  // 卡类型等于:储值卡-RechargeableCard,电子礼品卡-OnlineGiftCard,实体礼品卡-OfflineGiftCard,充值卡-ImprestCard
  cardTypeEquals: Nullable<string> = null
  // 卡号等于
  codeEquals: Nullable<string> = null
  // 页码
  page: Nullable<number> = null
  // 每页数量
  pageSize: Nullable<number> = null
  // 账户=
  accountEquals: Nullable<string> = null
  // 账户不等于=
  accountNotEquals: Nullable<string> = null
}