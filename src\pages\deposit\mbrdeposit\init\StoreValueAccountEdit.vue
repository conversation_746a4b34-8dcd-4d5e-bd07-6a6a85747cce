<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:03
 * @LastEditTime: 2024-04-24 14:25:03
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\mbrdeposit\init\StoreValueAccountEdit.vue
 * 记得注释
-->
<template>
  <div class="store-value-account-edit">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button v-if="hasOptionPermission('/储值/储值管理/多储值账户', '账户信息维护')" size="small" type="primary" @click="save"
          :loading="loading">保存</el-button>
        <el-button size="small" @click="$router.go(-1)">取消</el-button>
      </template>
    </BreadCrume>
    <div style="height: 95%;overflow: auto">
      <el-row class="current-page" v-loading="loading">
        <el-table :data="data.accounts">
          <el-table-column label="序号" width="80">
            <template slot-scope="scope">
              <span no-i18n>{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="会员储值账户/账户类型" width="250">
            <template slot-scope="scope">
              <span no-i18n>{{ scope.row.account | idName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="适用范围">
            <template slot-scope="{$index}">
              <el-row class="line">
                <el-col :span="2" class="label">适用商品：</el-col>
                <el-col :span="16">
                  <GoodsScopeEx no-i18n v-model="data.accounts[$index].useGoods" :goodsMatchRuleMode="goodsMatchRuleMode" :ref="`goods${$index}`" />
                </el-col>
              </el-row>
              <el-row class="line">
                <el-col :span="2" class="label">适用门店：</el-col>
                <el-col :span="18">
                  <ActiveStore no-i18n :isOldActivity="false" :ref="`store${$index}`" v-model="data.accounts[$index].useStores">
                  </ActiveStore>
                </el-col>
              </el-row>
              <el-row class="line">
                <el-col :span="2" class="label">{{i18n('/储值/预付卡/预付卡查询/列表页面/次数')}}：</el-col>
                <el-col :span="18">
                  <AutoFixInput :min="1" :max="99999" :fixed="0" v-model="data.accounts[$index].dailyPayTimes" style="width: 120px;height:24px"
                    :appendTitle="i18n('/营销/券礼包活动/券礼包活动/次')">
                  </AutoFixInput>
                  <span class="gray-tips" style="margin-left:12px">-{{i18n('/公用/券模板/商品折扣券/优惠上限/留空表示不限制')}}</span>
                </el-col>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
    </div>

  </div>
</template>

<script lang="ts" src="./StoreValueAccountEdit.ts">
</script>

<style lang="scss">
.active-store-view .content {
  width: 800px !important;
}
.store-value-account-edit {
  width: 100%;
  height: 100%;
  background-color: white;
  overflow: hidden;
  .area-active-store-view {
    width: 105%;
  }
  .current-page {
    height: calc(100% - 10px);
    overflow: auto;
    padding: 20px;

    .el-form-item {
      margin-bottom: 0;
    }

    .el-table {
      td {
        border-bottom: 1px solid #eceef5;
      }

      .cell {
        .line {
          display: flex;
          margin-top: 15px;
          align-items: center;

          .label {
            min-width: 120px;
            max-width: 120px;
          }
        }
      }
    }
  }
}
</style>
