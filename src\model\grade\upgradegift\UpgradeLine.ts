import GiftBag from './GiftBag'
import GiftInfo from "model/common/GiftInfo"

export default class UpgradeLine {
  // 等级代码
  grade: Nullable<string> = null
  // 成长值
  growthValue: Nullable<number> = null
  // 礼包
  gift: Nullable<GiftInfo> = new GiftInfo()
  // ---前端参数---
  pointCheck: Boolean = false
  couponCheck: Boolean = false
  growthValueCheck: Boolean = false
  gradeName: Nullable<string> = null
}