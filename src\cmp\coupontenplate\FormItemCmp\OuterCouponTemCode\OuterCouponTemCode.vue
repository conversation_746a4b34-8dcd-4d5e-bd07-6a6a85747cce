<!--
 * @Author: 黎钰龙
 * @Date: 2023-08-17 10:20:55
 * @LastEditTime: 2024-04-24 16:31:37
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\coupontenplate\FormItemCmp\OuterCouponTemCode\OuterCouponTemCode.vue
 * 记得注释
-->
<template>
  <el-form :model="ruleForm" ref="ruleForm" :rules="rules" label-width="120px">
    <el-form-item :label="formatI18n('/权益/券/券模板/外部券模板号')">
      <div style="display:flex" v-if="copyFlag !== 'edit'">
        <el-form-item prop="cascader">
          <el-cascader style="width: 300px" v-model="ruleForm.selectCascader" :options="platformList" :props="propsCascader" @blur="doFormChange"
            :placeholder="formatI18n('/资料/渠道/请选择')">
          </el-cascader>
        </el-form-item>
        <el-form-item prop="outerCode" style="margin-left:16px">
          <el-input v-model="ruleForm.templateCode" style="width:230px" @change="doFormChange" :placeholder="i18n('请填写平台商品ID')"></el-input>
        </el-form-item>
        <span class="span-btn" @click="doClear" style="color: red;margin-left:8px">{{i18n('/公用/按钮/清空')}}</span>
      </div>
      <div v-else>
        <template>
          <span style="margin-right:10px">{{"[" + value.channel.id + "]"}}{{ (ruleForm.channelName === null || ruleForm.channelName === '') ? "-" : ruleForm.channelName}}</span>
          <span>{{(ruleForm.templateCode === null || ruleForm.templateCode === '') ? "-" : ruleForm.templateCode}}</span>
        </template>
      </div>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" src="./OuterCouponTemCode.ts">
</script>

<style lang="scss" scoped>
</style>