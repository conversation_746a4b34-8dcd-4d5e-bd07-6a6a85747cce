import Account from 'model/prepay/adjustbill/Account'
import IdName from "model/common/IdName";

export default class MemberAccount {
  // 会员uuid
  memberId: Nullable<string> = null
  // 会员手机号
  mobile: Nullable<string> = null
  // 会员号
  hdCardMbrId: Nullable<string> = null
  // 实体卡号
  hdCardCardNum: Nullable<string> = null
  // 会员名称
  name: Nullable<string> = null
  // 会员储值账户信息
  accounts: Account[] = []
  // 所属组织
  ownStore: Nullable<IdName> = null
}