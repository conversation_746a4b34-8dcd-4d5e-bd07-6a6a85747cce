import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import FormItem from 'cmp/formitem/FormItem';
import MyQueryCmp from 'cmp/querycondition/MyQueryCmp';
import I18nPage from 'common/I18nDecorator';
import SaleCardBillApi from 'http/prepay/card/SaleCardBillApi';
import UploadApi from 'http/upload/UploadApi';
import SaleCardBill from 'model/prepay/card/SaleCardBill';
import SaleCardBillFilter from 'model/prepay/card/SaleCardBillFilter';
import DateUtil from 'util/DateUtil';
import { Component, Vue } from 'vue-property-decorator';
@Component({
  name: 'SaleCardList',
  components: {
    BreadCrume,
    MyQueryCmp,
    FormItem
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/公用/查询条件/提示',
    '/储值/预付卡/卡模板/编辑页面',
    '/储值/预付卡/预付卡调整单/列表页面',
    '/公用/活动/状态',
    '/储值/预付卡/卡模板/列表页面',
    '/储值/预付卡/充值卡制售单/列表页面',
    '/公用/按钮',
    '/卡/卡管理/售卡单'
  ],
  auto: true
})
export default class SaleCardList extends Vue {
  $refs: any
  panelArray: any = []
  query: SaleCardBillFilter = new SaleCardBillFilter()
  tableData: SaleCardBill[] = []
  selected: SaleCardBill[] = []
  selectCreateData: any = ['','']
  page: any = {
    pageSize: 10,
    page: 1,
    total: 0
  }

  pickerOptions(index:number) {
    return {
      //依据开始时间给出可选的三个月时间范畴
      onPick: ({ maxDate, minDate }: any) => {
        this.selectCreateData[index] = minDate.getTime();
        if (maxDate) {
          this.selectCreateData[index] = "";
        }
      },
      disabledDate: (time: any) => {
        if (this.selectCreateData[index]) {
          const curDate = this.selectCreateData[index];
          const three = 90 * 24 * 3600 * 1000; // 3个月
          const threeMonths = curDate + three; // 开始时间+3个月
          return time.getTime() < curDate || time.getTime() > threeMonths;
        }
      },
    }
  }

  created() {
    this.panelArray = [
      {
        name: this.i18n("售卡单"),
        url: "",
      },
    ]
    this.getList()
  }

  goDtl(billNumber: string) {
    this.$router.push({
      name: 'sale-card-dtl',
      query: { billNumber: billNumber }
    })
  }

  getList() {
    const body = this.query
    body.page = this.page.page - 1
    body.pageSize = this.page.pageSize
    if (this.query.createdBetweenClosedOpen?.length) {  //创建时间
      body.createdBetweenClosedOpen = [DateUtil.format(this.query.createdBetweenClosedOpen[0], "yyyy-MM-dd"), DateUtil.format(this.query.createdBetweenClosedOpen[1], "yyyy-MM-dd")]
    } else {
      body.createdBetweenClosedOpen = null
    }
    if (this.query.lastModifiedBetweenClosedOpen?.length) { //最后修改时间
      body.lastModifiedBetweenClosedOpen = [DateUtil.format(this.query.lastModifiedBetweenClosedOpen[0], "yyyy-MM-dd"), DateUtil.format(this.query.lastModifiedBetweenClosedOpen[1], "yyyy-MM-dd")]
    } else {
      body.lastModifiedBetweenClosedOpen = null
    }
    SaleCardBillApi.query(body).then((res)=>{
      if(res.code === 2000) {
        this.tableData = res.data || []
        this.page.total = res.total
      } else {
        this.$message.error(res.msg || this.i18n('查询售卡单列表失败'))
      }
    }).catch((error) => this.$message.error(error.message || this.i18n('内部异常')))
  }

  onReset() {
    this.page.page = 1
    this.page.pageSize = 10
    this.query = new SaleCardBillFilter()
    this.getList()
  }

  onSearch() {
    this.page.page = 1
    this.getList()
  }

  //批量删除
  batchRemove(){
    if (!this.selected?.length) return this.$message.warning(this.i18n('请先选择售卡单'))
    this.$confirm(
      this.i18n("确定批量删除选中售卡单吗？"),
      this.i18n("批量删除"),
      {
        confirmButtonText: this.formatI18n("/公用/按钮", "确定"),
        cancelButtonText: this.formatI18n("/公用/按钮", "取消"),
      }
    ).then(() => {
      const stringArr = this.selected.map(item => item.billNumber!)
      SaleCardBillApi.batchRemove(stringArr).then((res) => {
        if (res.code === 2000 && res.data) {
          this.$message.success(res.data)
          this.$refs.table?.clearSelection()
          this.getList()
        } else {
          this.$message.error(res.msg || this.i18n('批量删除失败'))
        }
      }).catch((error) => this.$message.error(error.message || this.i18n('内部异常')))
    });
  }

  handleSelectionChange(e: any[]) {
    this.selected = e
  }

  computeState(state: string) {
    let str = '-'
    let color = '#A1B0C8'
    if (state === 'INITIAL') {
      str = this.i18n('未审核')
      color = '#FFAA00'
    } else if (state === 'SALEING') {
      str = this.i18n('售卡中')
      color = '#1597FF'
    }
    else if (state === 'FINISH') {
      str = this.i18n('售卡完成')
      color = '#0CC66D'
    }
    return {
      state: str,
      color: color
    }
  }

  //前往卡模板详情页
  goCardDtl(templateCardNumber: string) {
    this.$router.push({ name: 'prepay-card-tpl-dtl', query: { number: templateCardNumber } })
  }

  doAudit(billNumber: string) {
    this.$confirm(
      this.i18n("确定审核选中售卡单吗？"),
      this.i18n("审核"),
      {
        confirmButtonText: this.formatI18n("/公用/按钮", "确定"),
        cancelButtonText: this.formatI18n("/公用/按钮", "取消"),
      }
    ).then(() => {
      SaleCardBillApi.audit(billNumber).then((res) => {
        if (res.code === 2000) {
          this.$message.success(this.i18n('操作成功'))
          this.getList()
        } else {
          this.$message.error(res.msg || this.i18n('操作失败'))
        }
      }).catch((error) => {
        this.$message.error(error.message || this.i18n('操作失败'))
      })
    });
  }

  doRemove(billNumber: string) {
    this.$confirm(
      this.i18n("确定要删除当前售卡单吗？"),
      this.i18n("删除"),
      {
        confirmButtonText: this.formatI18n("/公用/按钮", "确定"),
        cancelButtonText: this.formatI18n("/公用/按钮", "取消"),
      }
    ).then(() => {
      SaleCardBillApi.remove(billNumber).then((res) => {
        if (res.code === 2000) {
          this.$message.success(this.i18n('操作成功'))
          this.getList()
        } else {
          this.$message.error(res.msg || this.i18n('操作失败'))
        }
      }).catch((error) => {
        this.$message.error(error.message || this.i18n('操作失败'))
      })
    });
  }

  doEdit(billNumber: string) {
    this.$router.push({
      name: 'sale-card-edit',
      query: {
        editType: 'edit',
        billNumber: billNumber
      }
    })
  }

  doCreate() {
    this.$router.push({
      name: 'sale-card-edit'
    })
  }

  doDownLoad(row: SaleCardBill) {
    if (!row.downOssKey) return this.$message.error(this.i18n('当前单据缺少downOssKey'))
    UploadApi.getUrl(row.downOssKey).then((resp: any) => {
      if (resp && resp.data) {
        window.open(resp.data);
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  
  onHandleCurrentChange(val: number) {
    this.page.page = val
    this.getList()
  }

  onHandleSizeChange(val: number) {
    this.page.pageSize = val
    this.getList()
  }

};