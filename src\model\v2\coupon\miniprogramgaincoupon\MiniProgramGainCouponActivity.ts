import BGiftInfo from "model/common/BGiftInfo";
import CouponItem from "model/common/CouponItem";
import PushGroup from "model/precisionmarketing/pushplan/PushGroup";
import BaseCouponActivity from "model/v2/coupon/BaseCouponActivity";
import { ExpireRefundType } from "model/weixin/weixinIssueCouponActivity/ExpireRefundType";
import DateTimeCondition from "model/common/DateTimeCondition";



export default class MiniProgramGainCouponActivity extends BaseCouponActivity {
	// 活动图片id
	imageId: Nullable<string> = null;
	// 售价
	price: Nullable<number> = null;
	// 券
	giftInfo: Nullable<CouponItem> = null;
	// 券包
	giftBag: Nullable<BGiftInfo> = null;
  // 适用客群
  rule: Nullable<PushGroup> = null
  // 是否允许申请退款
  allowReturn: Nullable<boolean> = null
  // 过期退款方式
  expireRefundType: Nullable<ExpireRefundType> = ExpireRefundType.MANUAL_REFUND
	// 是否修改活动库存
	modifyStock: Nullable<boolean> = false
	// 是否修改活动每日最大限量库存
	modifyPerDayMaxStock: Nullable<boolean> = false
	// 是否要人工审核
	enableManualAudit: Nullable<boolean> = null
	// 是否允许分享
	allowShare: Nullable<boolean> = true
	// 时间条件
	dateTimeCondition: DateTimeCondition = new DateTimeCondition()
}
