<template>
  <div class="goods-scope">
    <div class="goods-scope-standard" v-if="theme === 'standard'">
      <div class="top" v-if="innerTitle">
        {{ formatI18n('/公用/公共组件/商品范围控件/标题/选择商品范围') }}
        <el-button :disabled="disabled" @click="doImport" class="btn" type="text">
          <img src="~assets/image/icons/ic_download.png" alt="" style="margin-bottom: 3px;">
          <span>{{ i18n('文件导入') }}</span>
        </el-button>
      </div>
      <div class="content">
        <Connective1 :disabled="disabled" style="width: 64px;margin: 10px 0 15px;" v-model="ruleForm.connective" v-if="ruleForm.rows.length > 1" @input="submit">
        </Connective1>
        <el-form ref="ruleForm" :model="ruleForm" label-width="0" style="margin:5px 0 0">
          <el-row v-for="(row, index) of ruleForm.rows" :key="index" style="display: flex;align-items: center;height: 40px;margin-top:4px">
            <el-select :disabled="disabled" class="width-150" size="mini" v-model="row.cond" style="margin-left: 10px" @change="changeType(index)">
              <template v-for="(item,ind) of supportedCond">
                <el-option :label="item" :value="item" :key="ind" :disabled="existsCond.indexOf(item) > -1">{{ item }}
                </el-option>
              </template>
            </el-select>
            <el-form-item :prop="`rows[${index}]`" class="cur_form" style="display: inline-block;margin: 0 10px" :rules="validateForm?rules:null">
              <template v-if="row.cond.indexOf(i18n('/公用/券模板/商品标签')) > -1">
                <SelectGoodsTag :disabled="disabled" v-model="row.items" :isOnlyId="false" width="250px" @change="doSubmitTags($event,index)"></SelectGoodsTag>
              </template>
              <template v-else>
                <el-input :disabled="disabled" v-model="row.text" @click.native="doFocus(index)" v-if="row.text" class="width-auto"
                  :placeholder="getSelectType(row.cond)" />
                <span class="plain-btn-blue" v-else @click="doFocus(index)">
                  {{i18n('选择') + checkScope(row)}}
                </span>
              </template>
            </el-form-item>
            <!-- 清空 -->
            <i v-show="!disabled" class="el-icon-refresh-left" @click="doClear(index)" v-if="ruleForm.rows.length !== 1"></i>
            <!-- 删除 -->
            <i v-show="!disabled" class="el-icon-minus" @click="doDelete(index)" v-if="ruleForm.rows.length !== 1"></i>
            <!-- 添加 -->
            <i v-show="!disabled" class="el-icon-plus" @click="doAdd" v-if="isShowAddBtn(index)"></i>
          </el-row>
        </el-form>
      </div>
    </div>
    <div class="goods-scope-precisionmarketing" v-if="theme === 'precisionmarketing'">
      <div style="display: flex;margin-top: 5px;">
        <div style="width: 40px;display: flex;padding-bottom: 20px">
          <div style="width: 38px">{{ formatI18n('/公用/公共组件/商品范围控件/表单/并且') }}</div>
          <div style="width: 2px;background-color: rgba(0, 0, 0, 0.24);">&nbsp;</div>
        </div>
        <div style="width: calc(100% - 60px);">
          <div style="margin: 15px">{{ formatI18n('/公用/公共组件/商品范围控件/表单/商品满足') }}</div>
          <div style="width: 100%;display: flex;">
            <div v-if="ruleForm.rows.length > 1"
              style="width: 40px;display: flex;align-items: center;justify-content: center;padding-top: 10px;padding-bottom: 20px;">
              <div style="border: 1px solid #888888;
              border-right: none;
              width: 40px;
              height: calc(100% - 20px);
              margin-right: -40px;
              display: flex;
              align-items: center">
                <div style="width: 40px;height: 20px;background-color: rgb(239, 239, 239);margin-left: -7px;white-space: nowrap">
                  {{ formatI18n('/公用/公共组件/商品范围控件/表单/并且') }}</div>
              </div>
            </div>
            <div style="width: calc(100% - 40px);display: flex">
              <div v-if="ruleForm.rows.length <= 1" style="width: 15px">&nbsp;</div>
              <el-form ref="ruleForm" :model="ruleForm" label-width="0">
                <el-row v-for="(row, index) of ruleForm.rows" :key="index" style="display: flex;align-items: start;">
                  <el-select :disabled="disabled" v-model="row.cond" style="width: 150px;" @change="changeType(index)">
                    <el-option v-for="item of supportedCond.filter((val) => existsCond.indexOf(val) === -1)" :label="item" :value="item" :key="item">{{ item }}
                    </el-option>
                  </el-select>
                  <el-form-item :prop="`rows[${index}]`" style="width: 250px;display: inline-block;margin-left: 10px"
                    :rules="validateForm?rules:null">
                    <el-input :disabled="disabled" v-model="row.text" @focus="doFocus(index)" :placeholder="getSelectType(row.cond)" />
                  </el-form-item>
                  &nbsp;&nbsp;&nbsp;
                  <el-button :disabled="disabled" type="text" @click="doClear(index)">
                    {{ formatI18n('/公用/按钮', '清空') }}
                  </el-button>
                  <el-button :disabled="disabled" @click="doDelete(index)" type="text" v-if="ruleForm.rows.length !== 1">
                    <i class="el-icon-remove" />
                  </el-button>
                  <el-button :disabled="disabled" @click="doAdd" type="text" v-if="(index === ruleForm.rows.length - 1 && index !== 5) && !getType">
                    <i class="el-icon-circle-plus" />
                  </el-button>
                </el-row>
              </el-form>
            </div>
          </div>
        </div>
      </div>
    </div>
    <GoodsSelectorDialog ref="selectGoodsScopeDialog" :goodsMatchRuleMode="goodsMatchRuleMode" @summit="doSubmitGoods" :appreciationGoods="appreciationGoods"
                         :chooseGoodType="chooseGoodType"/>
    <BrandSelectorDialog ref="selectBrandScopeDialog" @summit="doSubmitBrands" />
    <CatogorySelectorDialog ref="selectCatogoryScopeDialog" @summit="doSubmitCategorys" />
    <ImportDialog ref="importDialog" @upload-success="doUploadSuccess" :importNumber="importNumber" :importUrl="getImportUrl" :chooseGoodType="chooseGoodType" :appreciationGoods="appreciationGoods"
      :templatePath="templatePath" :templateName="formatI18n('/公用/券模板/全场现金券/用券商品/指定不可用商品/点击导入/导入商品范围模板')" :title="formatI18n('/公用/券模板/导入')">
    </ImportDialog>
  </div>
</template>

<script lang="ts" src="./GoodsScope.ts">
</script>

<style lang="scss" scoped>
.goods-scope {
  .goods-scope-standard {
    width: 800px;

    .top {
      display: flex;
      align-items: center;
      height: 32px;
      line-height: 32px;
      background: #f0f2f6;
      padding-left: 20px;

      .btn {
        margin-left: 10px;
        font-size: 13px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #007eff;
        img {
          vertical-align: middle;
        }
      }
    }

    .content {
      background-color: #f7f9fc;
      border: 1px solid #eeeeee;
      text-align: center;
      padding: 0 10px 0 10px;
      display: flex;

      .width-150 {
        width: 150px;
        min-width: 150px;
      }

      .width-auto {
        width: 250px;
      }
    }
    .el-icon-plus,
    .el-icon-minus,
    .el-icon-refresh-left {
      color: #007eff;
      width: 21px;
      height: 21px;
      line-height: 19px;
      text-align: center;
      border-radius: 50%;
      border: 1px solid #d7dfeb;
      font-size: 12px;
      font-weight: 600;
      margin-left: 8px;
      background-color: #ffffff;
      &:hover {
        cursor: pointer;
        border: 1px solid #007eff;
      }
    }
  }

  .goods-scope-precisionmarketing {
  }

  ::v-deep.el-form-item__content {
    margin-left: 0 !important;
    line-height: 30px;
  }
  ::v-deep .el-form-item__error {
    white-space: nowrap;
  }
}
</style>
