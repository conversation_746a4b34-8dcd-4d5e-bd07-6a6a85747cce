import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import FormItem from "cmp/formitem/FormItem.vue";
import IdName from "model/common/IdName";
import CouponInfo from "model/common/CouponInfo";
import SelectStoreActiveDtlDialog from "pages/deposit/mbrdeposit/active/dialog/SelectStoreActiveDtlDialog.vue";
import CouponItem from "model/common/CouponItem";
import CouponTemplateApi from "http/coupon/template/CouponTemplateApi";
import BrowserMgr from "mgr/BrowserMgr";
import { SuperpositionLevel } from "model/coupon/CouponSuperposition/SuperpositionLevel";
import { SuperpositionType } from "model/coupon/CouponSuperposition/SuperpositionType";

@Component({
	name: "GroupMutexTemplateDtl",
	components: {
		FormItem,
		SelectStoreActiveDtlDialog,
	},
})
export default class GroupMutexTemplateDtl extends Vue {
	$refs: any;
	@Prop()
	info: CouponInfo;

	@Prop({
		required: false,
		default: false,
	})
	deepDialog: boolean;
	mutexCouponTemplateDialogShow = false;
	parent: any = {};
	child: CouponItem = new CouponItem();
	enableActivityExclusion = false;
	coupon: any = {
		// groupMutexFlag: true,
		groupMutexTemplates: [],
		currentTemplateId: "",
		superpositionLevel: "",
		superpositionType: "",
		superpositionTypeValue: [],
		nonSuperpositionTypeValue: "",
		noLimit: false,
	};

	@Watch("info")
	onValueChange(value: CouponInfo) {
		if (value) {
			this.initValue();
		}
	}

	created() {
		this.getShowFlag();
		this.initValue();
	}

	doDialogClose() {
		this.mutexCouponTemplateDialogShow = false;
	}

	doEditMutexCouponTemplate(num: string) {
		CouponTemplateApi.detail(num,false)
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					this.child.coupons = resp.data;
					this.mutexCouponTemplateDialogShow = true;
				}
			})
			.catch((error: any) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}

	initValue() {
		if (this.info) {
			this.coupon.currentTemplateId = this.info.templateId;
			// this.coupon.groupMutexFlag = this.info.groupMutexFlag
			this.coupon.groupMutexTemplates = this.info.groupMutexTemplates;
			if (this.info.couponSuperposition) {
				this.coupon.superpositionLevel = this.info.couponSuperposition.superpositionLevel;
				this.coupon.superpositionType = this.info.couponSuperposition.superpositionType;
				this.coupon.superpositionTypeValue = this.info.couponSuperposition.superpositionTypeValue;
				this.coupon.nonSuperpositionTypeValue = this.info.couponSuperposition.nonSuperpositionTypeValue;
			}
			// if (this.info.useThreshold) {
			// 	this.coupon.noLimit = this.info.useThreshold.thresholdType === "NONE";
			// }
		}

		// if (this.info.templateId) {
		// 	let index = this.findIndex();
		// 	if (index > -1) {
		// 		this.coupon.groupMutexTemplates.splice(index, 1);
		// 	}
		// 	let first = new IdName();
		// 	first.name = this.formatI18n("/权益/券/券模板/新建券模板/叠加用券/当前券模板");
		// 	first.id = this.coupon.currentTemplateId;
		// 	this.coupon.groupMutexTemplates.unshift(first);
		// }
	}

	private findIndex() {
		// for (let i = 0; i < this.info.groupMutexTemplates.length; i++) {
		//   if (this.info.groupMutexTemplates[i].id == this.info.templateId) {
		//     return i
		//   }
		// }
		return -1;
	}

	private getShowFlag() {
		let permission = BrowserMgr.LocalStorage.getItem("sysConfig");
		if (permission && permission.enableActivityExclusion) {
			this.enableActivityExclusion = permission.enableActivityExclusion;
		}
	}
}
