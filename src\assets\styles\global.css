input[aria-hidden=true] {
  display: none !important;
}

.el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner {
  box-shadow: none !important;
}

.el-message-box__status {
  top: 40% !important;
}

.ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.el-table .cell {
  word-break: break-word !important;
}

.el-form-item__label {
  word-break: break-word !important;
}

.auto-expand-form-item {
  margin-bottom: 0 !important;
}

.form_required::before {
  content: "*";
  color: #ef393f;
  margin-right: 4px;
}

.text-overflow-ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.span-btn {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #007eff;
  cursor: pointer;
  word-wrap: break-word;
  word-break: keep-all;
}

.plain-btn-blue {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #007EFF;
  font-size: 13px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #007EFF;
  background-color: #ffffff;
  padding: 4px 8px;
  height: 28px;
  border-radius: 2px;
  margin-right: 4px;
  cursor: pointer;
}

.plain-btn-gray {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #D7DFEB;
  font-size: 13px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #24272B;
  background-color: #ffffff;
  padding: 4px 8px;
  height: 28px;
  border-radius: 2px;
  margin-right: 4px;
  cursor: pointer;
}

.auto-expand-form-item .el-form-item__error {
  text-align: left;
  position: initial !important;
}

.el-form-item__error {
  word-break: break-word !important;
  padding-top: 2px !important;
}

.el-form-item.is-success .el-input__inner,
.el-form-item.is-success .el-input__inner:focus,
.el-form-item.is-success .el-textarea__inner,
.el-form-item.is-success .el-textarea__inner:focus {
  border-color: #d7dfeb !important;
}

.el-form-item.is-error .el-input__inner,
.el-form-item.is-error .el-input__inner:focus,
.el-form-item.is-error .el-textarea__inner,
.el-form-item.is-error .el-textarea__inner:focus {
  border-color: #ef393f !important;
}

.center-dialog {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.label-inline .el-form-item .el-form-item__label {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  line-height: 1;
  height: 40px;
}

/* 搜索框，form标签文字大小 */
.qf-form-item .qf-form-label {
  font-size: 13px !important;
}

/* 模拟form非法提示文案,但不影响校验结果 */
.warning-form {
  color: #EF393F;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position: absolute;
  white-space: nowrap;
  top: 135%;
  left: 0;
}


.el-input-group__append,
.el-input-group__prepend {
  padding: 0 10px !important;
  color: #36445a !important;
}

.gray-tips {
  font-size: 13px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #79879E;
  word-break: keep-all;
}

.number-text {
  color: #FFAA00;
  margin: 0 4px;
}

.select-num {
  font-size: 14px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #242633;
  margin: 0 4px;
}

.el-pagination__jump {
  margin-left: -6px !important;
}

.el-button--large {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  height: 36px;
  padding: 10px 12px !important;
  font-size: 14px !important;
  font-family: PingFangSC-Regular, PingFang SC !important;
  font-weight: 400 !important;
  border-radius: 2px;
}

.el-button--medium {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  height: 28px;
  padding: 4px 8px !important;
  font-size: 13px !important;
}

.el-button--primary {
  background: rgb(0, 126, 255) !important;
}

/* 搜索栏，查询按钮 */
.btn-search {
  width: 76px !important;
  height: 32px !important;
  background: #007eff !important;
  border-radius: 2px !important;
  display: inline-flex !important;
  justify-content: center !important;
  align-items: center !important;
  font-size: 13px !important;
}

/* 搜索栏，重置按钮 */
.btn-reset {
  width: 76px !important;
  height: 32px !important;
  border-radius: 2px !important;
  display: inline-flex !important;
  justify-content: center !important;
  align-items: center !important;
  font-size: 13px !important;
}

.qf-form-content {
  margin-left: 130px !important;
}

.qf-form-label {
  width: 130px !important;
}

.el-table th>.cell {
  font-size: 13px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #242633;
}

.el-tabs__item.is-active {
  font-size: 16px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #242633 !important;
  line-height: 24px;
}

.el-tabs__item {
  font-size: 16px !important;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #79879e !important;
  line-height: 24px;
}

.el-tabs--top .el-tabs__item.is-top:nth-child(2) {
  padding-left: 20px !important;
}

.section-title {
  font-size: 16px;
  font-family: PingFangSC-Semibold, "PingFang SC";
  font-weight: 600;
  color: rgb(36, 38, 51);
  line-height: 24px;
  padding: 12px 0px;
}

/* 确认弹出框样式__this.$confirm */
.el-message-box .el-message-box__content .el-message-box__message {
  font-size: 13px;
}

.el-message-box .el-message-box__btns .el-button--default {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  height: 28px;
  padding: 4px 8px !important;
  font-size: 13px !important;
}

/* 券模板表单页 风格统一样式 */
.setting-container {
  background: #ffffff;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
}

.setting-container .section-title {
  font-size: 16px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #242633;
  margin-bottom: 12px;
  height: 24px;
  padding: 0;
}

.edit-warning {
  width: 100%;
  margin: 8px 0 8px;
  padding: 2px 12px;
  background: #EAF3FF;
  border-radius: 2px;
  border: 1px solid #318BFF;
}

.ellipse {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin: 0 4px 2px;
}

.div_1_3_of_line {
  display: inline-block;
  width: calc(100% / 3);
}

.operate-block {
  display: flex;
  flex-flow: row wrap;
}

.operate-block .span-btn {
  margin-right: 16px;
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.require-item .el-form-item__label:before {
  content: "*";
  color: #ef393f;
  margin-right: 4px;
}

.el-table--border th .cell {
  word-break: keep-all !important;
}

.ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  cursor: default;
}

.formItem-require .qf-form-label::before {
  content: "*";
  color: #ef393f;
  margin-right: 4px;
}

.column-radio {
  display: flex !important;
  flex-direction: column;
  justify-content: center;
}

.el-range-separator {
  word-break: keep-all !important;
}