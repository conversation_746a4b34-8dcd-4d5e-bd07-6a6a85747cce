/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2023-12-07 16:18:38
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\common\RSGoodsFilter.ts
 * 记得注释
 */
export default class RSGoodsFilter {
  //
  key: Nullable<string> = null
  //
  codeLikes: Nullable<string> = null
  //
  codeEquals: Nullable<string> = null
  //
  codeIn: string[] = []
  //
  nameLikes: Nullable<string> = null
  //
  barcodeEquals: Nullable<string> = null
  //
  barcodeLikes: Nullable<string> = null
  //
  brandIdEquals: Nullable<string> = null
  //
  brandIdLikes: Nullable<string> = null
  //
  categoryIdEquals: Nullable<string> = null
  //
  categoryIdLikes: Nullable<string> = null
  //
  barcodeIn: string[] = []
  //
  codeOrder: Nullable<boolean> = null
  //
  nameOrder: Nullable<boolean> = null
  //
  page: Nullable<number> = null
  //
  pageSize: Nullable<number> = null
  // 是否查询会员专享商品
  memberGoodsEquals: Nullable<boolean> = false
  // 是否用code作为商品唯一标识
  groupByCode: boolean = false
  sorters = {
    barcode: 'asc'
  }
  appreciationGoodsEquals: Nullable<boolean> = null
}