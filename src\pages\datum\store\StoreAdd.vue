<template>
  <div class="store-view">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button type="primary" @click="doSave">
          {{ formatI18n('/公用/按钮/保存') }}
        </el-button>
        <el-button @click="doCancel">
          {{ formatI18n('/公用/按钮/取消') }}
        </el-button>
      </template>
    </BreadCrume>
    <div class="current-page">
      <el-form :model="ruleForm" :rules="rules" class="demo-ruleForm" label-width="120px" ref="ruleForm">
        <el-form-item :label="formatI18n('/资料/门店/门店代码')" prop="org.id">
          <el-input maxlength="64" :disabled="storeFlag === 'edit'" style="width: 280px" v-model="ruleForm.org.id"></el-input>
          <i class="el-icon-warning" style="margin-left: 10px;"/>
          {{ formatI18n('/资料/门店/代码不允许与已有门店重复') }}。
        </el-form-item>
        <el-form-item :label="formatI18n('/资料/门店/门店名称')" prop="org.name">
          <el-input maxlength="64" style="width: 280px" v-model="ruleForm.org.name"></el-input>
        </el-form-item>
        <el-form-item :label="formatI18n('/资料/门店/所属营销中心')" v-if="enableMultiMarketingCenter">
          <span>{{ '[' + marketCenter + ']' }}{{ marketCenterName }}</span>
        </el-form-item>
        <el-form-item :label="formatI18n('/资料/门店/所属区域')">
          <el-select style="width: 280px" v-model="ruleForm.zoneId" :placeholder="formatI18n('/资料/渠道/请选择')">
            <el-option v-for="(value) in areaData" :key="value.zone.id" :value="value.zone.id" :label="'[' + value.zone.id + ']' + value.zone.name">[{{ value.zone.id }}]{{ value.zone.name
              }}</el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="formatI18n('/资料/门店/门店地址')">
          <div style="display: flex; justify-content: flex-start;">
            <AddressSelector style="width: 280px" :full="true" value-type="IdName" v-model="ruleForm.address">
            </AddressSelector>
            <el-input style="width: 280px; padding-left: 10px" :placeholder="formatI18n('/资料/门店', '详细地址')" maxlength="254"
              v-model="ruleForm.addressInfo">
            </el-input>
          </div>
        </el-form-item>
        <el-form-item :label="i18n('门店经纬度')" prop="orgId">
          <div style="display: flex; justify-content: flex-start;">
            <el-input :placeholder="formatI18n('/资料/门店', '输入经度')" v-model.trim="ruleForm.lng" type="number" maxlength="64" class="mo-input--number"
              style="width: 280px" />
            <el-input :placeholder="formatI18n('/资料/门店', '输入纬度')" v-model.trim="ruleForm.lat" type="number" maxlength="64" class="mo-input--number"
              style="width: 280px;  padding-left: 10px" />
          </div>
        </el-form-item>
        <el-form-item :label="formatI18n('/资料/门店/区域主管')" prop="areaManager">
          <SelectEmployees v-model="ruleForm.areaManager" width="280px"></SelectEmployees>
        </el-form-item>
        <el-form-item :label="formatI18n('/资料/门店/营运经理')" prop="operationManager">
          <SelectEmployees v-model="ruleForm.operationManager" width="280px"></SelectEmployees>
        </el-form-item>
        <el-form-item :label="formatI18n('/资料/门店/联系电话')" prop="phoneNumber">
          <el-input maxlength="13" style="width:280px" v-model.trim="ruleForm.phoneNumber" onkeyup="this.value=this.value.replace(/[^\d]/g,'') ">
          </el-input>
        </el-form-item>
        <el-form-item :label="formatI18n('/公用/券模板/标签')">
          <el-tag class="tag-block" v-for="(item,index) in ruleForm.tags" @close="handleClose(index)" :key="item.tagName" disable-transitions closable
            type="info">
            {{item.tagName}}
          </el-tag>
          <template v-if="ruleForm.tags.length < tagMaxLimit">
            <el-input style="width:160px" v-model.trim="tagValue" @change="doTagValueChange"></el-input>
            <el-button @click="doAddTags" type="primary" style="margin-left:6px">{{formatI18n('/资料/门店/添加')}}</el-button>
          </template>
        </el-form-item>
        <el-form-item :label="formatI18n('/资料/门店/渠道门店')" prop="orgId">
          <div style="display: flex; justify-content: flex-start;" v-for="(i, k) in platOrgInfos" :key="k">
            <el-cascader style="width: 300px" v-model="i.selectCascader" :options="platformList" :props="propsCascader" @focus="changeCascader(k)"
              @active-item-change="handleItemChange" :placeholder="formatI18n('/资料/渠道/请选择')"></el-cascader>
            <el-input :placeholder="formatI18n('/资料/门店/请填写渠道门店id')" v-model.trim="i.platformStoreId" maxlength="48"
              style="width: 280px;  padding-left: 10px" />
            <el-button type="text" style="color: red; margin-left: 20px;" @click="removePlatformStore(k)">{{
              formatI18n('/公用/按钮/删除') }}</el-button>
          </div>
          <div style="display: flex; justify-content: flex-start;" v-if="platOrgInfos.length !== platformList.length">
            <el-button type="text" @click="addPlatformStore" style="margin-top:5px">
              +{{ formatI18n('/资料/门店/添加平台门店') }}
            </el-button>
          </div>
        </el-form-item>
      </el-form>

    </div>
  </div>
</template>
  
<script lang="ts" src="./StoreAdd.ts">
</script>
  
<style lang="scss">
.store-view {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: hidden;

  .current-page {
    height: calc(100% - 46px);
    padding: 24px 32px;
    overflow-y: auto;
    .tag-block {
      display: inline-block;
      padding: 0 6px;
      margin-top: 6px;
      margin-right: 8px;

      &:nth-last-child {
        margin-right: 0;
      }
    }
  }
  ::v-deep.el-cascader {
    line-height: 40px;
  }
  .mo-input--number {
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
      -webkit-appearance: none;
    }
    input[type="number"] {
      -moz-appearance: textfield;
    }
  }
}
</style>

<style>
.el-input__inner {
  line-height: 1px !important;
}
</style>