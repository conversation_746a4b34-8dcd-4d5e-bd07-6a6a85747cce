<template>
  <div class="store-value-edit">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button v-if="hasOptionPermission('/储值/储值管理/充值面额设置', '配置维护')" @click="doSave" size="small" type="primary">
          保存
        </el-button>
        <el-button @click="doBack" size="small" type="default">取消</el-button>
      </template>
    </BreadCrume>
    <div style="overflow: auto;height: 95%">
      <div class="flex-wrap">
        <div class="flex-item left">
          <img class="size-back" src="~assets/image/storevalue/iphone.png">
          <img class="size-content" src="~assets/image/storevalue/rechargeInit.png">
        </div>
        <div class="flex-item right">
          <div style="font-size: 12px;color: rgba(51, 51, 51, 0.647058823529412);    margin-left: 22px;
    margin-bottom: 20px;"><i class="iconfont  ic-info" style="font-size: 18px;color: #20A0FF"></i>&nbsp;
            <span>设置在线充值的可选面额（如左图所示），最多可设置10个；在储值充值活动中，亦将按此面额分别设置赠礼规则。</span>
          </div>
          <el-form :model="dynamicValidateForm" class="demo-dynamic" label-width="125px" ref="dynamicValidateForm">
            <div class="price-block">
              <div class="block-item" v-for="(domain, index) in dynamicValidateForm.domains" :key="index">
                <el-form-item :prop="'domains.' + index + '.value'" :rules="faceAmountRule" label="充值面额">
                  <el-input v-model="domain.value" style="width:110px" @change="doValueChange(index)">
                    <template slot="append">元</template>
                  </el-input>
                  <el-button key="d" v-if="dynamicValidateForm.domains.length > 1" @click.prevent="doRemoveDomain(domain)" style="margin-left: 20px"
                    type="text">删除
                  </el-button>
                  <el-button key="a" @click.prevent="doAddDomain(domain)" type="text" v-if="index === dynamicValidateForm.domains.length - 1">添加
                  </el-button>
                </el-form-item>
              </div>
            </div>
            <el-form-item :label="i18n('自定义金额')" prop="customPrice" :rules="customPriceRule">
              <span class="gray-tips">{{i18n('C端是否展示自定义充值金额入口')}}</span>
              <div style="margin-left:20px">
                <el-radio-group v-model="dynamicValidateForm.customPrice.isShowEntry" @change="clearAmount">
                  <el-radio :label="false">{{i18n('不展示')}}</el-radio>
                  <el-radio :label="true">{{i18n('展示')}}</el-radio>
                </el-radio-group>
                <span style="font-size:13px;margin-left:12px">
                  {{i18n('充值基数')}}
                  <AutoFixInput :min="0.01" :max="99999.99" :fixed="2" v-model="dynamicValidateForm.customPrice.amount" style="width: 148px"
                    :appendTitle="formatI18n('/券/购券管理','元')" :disabled="!dynamicValidateForm.customPrice.isShowEntry" />
                </span>
              </div>
            </el-form-item>
            <el-form-item :label="i18n('C端默认充值面额')">
              <el-select size="mini" v-model="dynamicValidateForm.appletDefaultDepositAmount">
                <el-option v-for="(domain, index) in  filterDynamicValidateFormDomains" :key="index" :label="domain.value" :value="domain.value">
                </el-option>
              </el-select>
              <span style="margin-left: 10px;">元</span>
            </el-form-item>
            <el-form-item :label="i18n('充值须知')">
              <el-input maxlength="1000" :rows="8" type="textarea" :placeholder="i18n('/公用/表单校验/请输入不超过{0}个字符',['1000'])" style="width: 650px"
                v-model.trim="dynamicValidateForm.remark">
              </el-input>
            </el-form-item>
            <el-form-item :label="i18n('/储值/预付卡/预付卡充值有礼/充值协议')">
              <div style="padding-left:20px">
                <RichText v-model="dynamicValidateForm.agreement"></RichText>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

  </div>
</template>

<script lang="ts" src="./StoreValueEdit.ts">
</script>

<style lang="scss">
.store-value-edit {
  width: 100%;
  height: 100%;
  background-color: white;
  overflow: hidden;

  // .el-form-item__label {
  //   width: 120px !important;
  // }

  .flex-wrap {
    display: flex;
    padding: 50px;

    .flex-item {
      .size-back {
        width: 285px;
        height: 586px;
      }
      .price-block {
        display: flex;
        flex-wrap: wrap;
        .block-item {
          display: inline-block;
          width: 50%;
          flex-shrink: 0;
        }
      }
      .size-content {
        width: 255px;
        height: 455px;
        position: absolute;
        top: 59px;
        left: 13px;
      }
    }

    .left {
      width: 300px;
      position: relative;
    }

    .right {
      flex: 1;
      padding-left: 30px;

      .right-content {
        display: flex;
        flex-flow: row wrap;
        align-content: flex-start;

        .money-value {
          font-size: 18px;
          background-color: #f8f8f8;
          height: 60px;
          width: 120px;
          display: inline-block;
          border: 1px solid #bfbfbf;
          line-height: 60px;
          text-align: center;
          border-radius: 5px;
          margin-top: 15px;
          margin-left: 15px;
          line-height: 60px;
        }
      }
    }
  }
  .el-radio-group {
    vertical-align: baseline;
  }
}
</style>