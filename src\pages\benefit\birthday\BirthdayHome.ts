import {Component, Vue} from 'vue-property-decorator'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import BirthdayBenefitApi from 'http/benefit/BirthdayBenefitApi'
import GiftRule from 'model/benefit/GiftRule'
import PointsRule from 'model/benefit/PointsRule'
import FormItem from 'cmp/formitem/FormItem.vue'

@Component({
  name: 'BirthdayHome',
  components: {
    BreadCrume,
    FormItem
  }
})
export default class BirthdayHome extends Vue {
  $refs: any
  panelArray: any = []
  pointsRule: PointsRule = new PointsRule()
  giftRule: GiftRule = new GiftRule()

  created() {
    this.panelArray = [
			{
				name: this.formatI18n("/公用/菜单", "生日有礼"),
				url: "",
			},
		];
  }

  mounted() {
    this.getData()
  }

  navigateTo(type: string) {
    if (type === 'gift') {
      this.$router.push({name: this.giftRule ? 'birthday-gift-dtl' : 'birthday-gift-add'})
    } else if (type === 'points') {
      this.$router.push({name: this.pointsRule ? 'birthday-points-dtl' : 'birthday-points-add'})
    }
  }

  get getAllSameGiftBagPoints() {
    if (this.giftRule && this.giftRule.allSameGiftBag) {
      let str: any
      str = this.formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/首页/礼包设置', '赠送积分{0}个；')
      let points = this.giftRule.allSameGiftBag.points
      if (!points) {
        str = ''
      } else {
        str = str.replace(/\{0\}/g, points)
      }
      return str
    }
    return ''
  }

  get sendDayI18n() {
    if (this.giftRule && this.giftRule.giftDay) {
      let day = (this.giftRule && this.giftRule.giftDay!.dayNo) ? this.giftRule.giftDay!.dayNo : '-'
      let time = (this.giftRule && this.giftRule.giftDay!.time) ? this.giftRule.giftDay!.time : '-'
      let str: any
      if (this.giftRule.giftDay!.type === 'MONTH_DAY') { //每月x天
        str = this.formatI18n('/权益/生日权益初始化/生日权益初始化/生日当月{0}号{1}')
      } else if (this.giftRule.giftDay!.type === 'BIRTHDAY') { //生日当天
        str = this.formatI18n('/权益/生日权益初始化/生日权益初始化/生日当天{1}')
      } else if (this.giftRule.giftDay!.type === 'BEFORE_DAY') { //生日前x天
        str = this.formatI18n('/权益/生日权益初始化/生日权益初始化/生日前{0}天的{1}')
      }
      str = str.replace(/\{0\}/g, day)
      str = str.replace(/\{1\}/g, time)
      return str
    }
  }

  limitNumber(value: any) {
    let str = this.formatI18n('/权益/生日权益初始化/生日权益初始化/首页/生日积分加倍/限{0}次')
    // 每月 号给当月过生日的会员送礼包
    return str.replace(/\{0\}/g, value);
  }

  get getAllSameGiftBagCoupons() {
    let data = this.formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/新建/赠送券：')
    if (this.giftRule && this.giftRule.allSameGiftBag) {
      let items = this.giftRule.allSameGiftBag.couponItems
      if (items && items.length > 0) {
        let blank = ' '
        let unit = this.formatI18n('/权益/生日权益初始化/生日权益初始化/生日送礼/详情/礼包设置', '张')
        items.forEach((v) => {
          data = data + blank + v.qty + blank + unit + blank
          if (v.coupons) {
            data = data + v.coupons.name + '、'
          }
        })
        return data.substring(0, data.length - 1)
      }
    }
    return ''
  }

  private getData() {
    BirthdayBenefitApi.home()
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.giftRule = resp.data.giftRule
          this.pointsRule = resp.data.pointsRule
        }
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
    })
  }
}