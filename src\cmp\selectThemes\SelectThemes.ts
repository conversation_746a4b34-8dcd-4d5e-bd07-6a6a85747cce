/*
 * @Author: 黎钰龙
 * @Date: 2023-11-02 15:05:58
 * @LastEditTime: 2024-07-18 16:33:36
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\selectThemes\SelectThemes.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import ActivityTopicApi from 'http/v2/controller/points/topic/ActivityTopicApi';
import RSOrg from 'model/common/RSOrg';
import ActivityTopic from 'model/v2/controller/points/topic/ActivityTopic';
import { Component, Model, Prop, Vue } from 'vue-property-decorator';
@Component({
  name: 'SelectThemes',
  components: {}
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/公用/下拉框/提示'
  ],
  auto: true
})
export default class SelectThemes extends Vue {
  @Model('change') selectThemeIdName: ActivityTopic | string
  @Prop({ type: String, default: '400px' }) width: string;
  @Prop() placeholder: any;
  @Prop({ type: Boolean, default: false }) hideAll: boolean; //是否隐藏“全部”选项
  @Prop({ type: Boolean, default: true }) isOnlyId: boolean; //是否只绑定id
  @Prop({ type: Boolean, default: false }) disabled: boolean;
  selectLoading: boolean = false
  themes: RSOrg[] = []

  get selectTheme() {
    return this.selectThemeIdName
  }
  set selectTheme(value: any) {
    const res = value ?? null
    this.$emit('change', res)
  }

  created() {
    this.getTheme()
  }
  getTheme() {
    ActivityTopicApi.listTopic().then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.themes = resp.data;
      } else {
        throw new Error(resp.msg || this.i18n('查询主题列表失败'))
      }
    }).catch((error) => {
      this.$message.error(error.message || this.i18n('内部异常'));
    }).finally(() => this.selectLoading = false);
  }
};