/*
 * @Author: 黎钰龙
 * @Date: 2023-12-01 13:44:03
 * @LastEditTime: 2024-01-05 14:29:04
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\coupon\activity\BaseCouponActivity.ts
 * 记得注释
 */
import ActivityBody from 'model/common/ActivityBody'
// import { DateLimitType } from 'model/common/DateLimitType'
// import { ReleaseType } from 'model/default/ReleaseType'
import { ReleaseType } from "model/weixin/weixinIssueCouponActivity/ReleaseType";


export default class BaseCouponActivity {
  // true表示只保存，false表示保存并审核
  justSave: Nullable<boolean> = null
  // 活动总限
  maxIssueTimes: Nullable<number> = null
  // 非会员是否参与发券
  nonmemberIssue: Nullable<boolean> = null
  // 每人限量
  maxPerIssueTimes: Nullable<number> = null
  // 每人每天/周/月限量
  maxPerDateRangeIssueTimes: Nullable<number> = null
  // 限量时间类型，DAY——每天；WEEK——每周,MONTH——每月，YEAR——每年;
  dateLimitType: Nullable<string> = null
  // 活动类型
  body: Nullable<ActivityBody> = null
  // 营销中心
  marketingCenter: Nullable<string> = null
  // true表示参与叠加促销，false表示不参与叠加促销
  joinPromotion: Nullable<boolean> = null
  // 显示排序
  sequence: Nullable<number> = null
  // 预告天数
  advanceDay: Nullable<number> = null
  // 子活动类型
  subType: Nullable<string> = null
  // 投放方式
  releaseType: Nullable<ReleaseType> = null
  // 创建人
  creator: Nullable<string> = null
  // 创建时间
  creatTime: Nullable<Date> = null
}