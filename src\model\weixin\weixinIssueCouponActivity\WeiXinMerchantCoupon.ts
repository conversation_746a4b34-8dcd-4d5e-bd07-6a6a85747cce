/*
 * @Author: 黎钰龙
 * @Date: 2024-01-24 14:07:06
 * @LastEditTime: 2025-04-14 14:47:54
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\weixin\weixinIssueCouponActivity\WeiXinMerchantCoupon.ts
 * 记得注释
 */
export default class WeiXinMerchantCoupon {
  // 数量
  qty: Nullable<number> = null
  // 批次号
  batchNumber: Nullable<string> = null
  // 是否允许申请延期
  enableApplyDelay: Nullable<boolean> = null
  // 券模板名称
  couponTemplateName: Nullable<string> = null
  // 券模板号
  couponTemplateNumber: Nullable<string> = null
  // 价格
  templatePrice: Nullable<number> = null
  // 奖品大图
  bigImage: Nullable<string> = null
  // 奖品小图
  image: Nullable<string> = null
}