import GiftInfo from 'model/common/GiftInfo'
import IdName from "model/entity/IdName";
import {PrizeType} from "model/promotion/groupBookingActivity/PrizeType";


export default class GroupBookingPrize {
  // 奖品类型
  prizeType: Nullable<PrizeType> = null
  // 优惠券
  giftBag: Nullable<GiftInfo> = null
  // 奖品
  prize: Nullable<IdName> = null
  // 奖品数量
  prizeCount: Nullable<number> = null
  // 每个团最多中奖数量
  wonPerGroupMaxCount: Nullable<number> = null
  // 奖品图片
  prizeImage: Nullable<string> = null
  // 奖品说明
  prizeRemark: Nullable<string> = null
}