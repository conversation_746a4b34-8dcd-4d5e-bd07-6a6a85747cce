import ApiClient from 'http/ApiClient'
import Response from 'model/default/Response'
import StyleManageConfig from "model/stylemanage/StyleManageConfig";

export default class StyleManageApi {
  /**
   * 获取国际化设置
   * 获取国际化设置
   * 
   */
  static get(): Promise<Response<StyleManageConfig>> {
    return ApiClient.server().get(`/v1/style-manage/get`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 风格管理保存
   * 风格管理保存
   * 
   */
  static save(body: StyleManageConfig, restoreDefault: boolean): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/style-manage/save/${restoreDefault}`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
