<template>
  <div class="membership-growth-container">
    <BreadCrume :panelArray="panelArray"></BreadCrume>
    <div style="height: 95%;overflow: auto">
      <div class="score-header">
        <el-tabs class="main-tabs" @tab-click="handleTabClick" style="width: 100%" v-model="activeName">
          <el-tab-pane :label="formatI18n('/营销/券礼包活动/券礼包活动', '新建活动')" name="active-manage-add">
            <div class="manage-add-block">
              <!-- 邀请有礼 -->
              <div class="content-block" v-if="hasOptionPermission('/营销/营销/会员成长/邀请有礼', '活动查看')">
                <div class="content-subtitle">{{ formatI18n("/营销/券礼包活动/券礼包活动", "邀请有礼") }}</div>
                <div class="content-text">
                  {{i18n('/营销/邀请有礼/通过现有会员邀请新会员加入，以实现会员增长、品牌推广和用户参与度提升')}}
                </div>
                <div class="btn-block">
                  <span class="plain-btn-blue" @click="doActiveAdd('invite-send-gift')" v-if="hasOptionPermission('/营销/营销/会员成长/邀请有礼', '活动维护')">
                    {{ i18n("立即创建") }}
                  </span>
                </div>
              </div>
              <!-- 升级有礼 -->
              <div class="content-block" v-if="hasOptionPermission('/营销/营销/会员成长/升级有礼', '活动查看')">
                <div class="content-subtitle">{{ formatI18n("/营销/升级有礼", "升级有礼") }}</div>
                <div class="content-text">
                  {{i18n('/营销/升级有礼/为鼓励用户升级会员或账户而设置的奖励计划，以促进用户升级和提高用户忠诚度')}}
                </div>
                <div class="btn-block">
                  <span class="plain-btn-blue" @click="doActiveAdd('upgrade-gift-edit')" v-if="hasOptionPermission('/营销/营销/会员成长/升级有礼', '活动维护')">
                    {{ i18n("立即创建") }}
                  </span>
                </div>
              </div>
              <!-- 注册发大礼包 -->
              <div class="content-block" v-if="hasOptionPermission('/营销/营销/会员成长/注册发大礼包', '活动查看')">
                <div class="content-subtitle">{{ formatI18n("/营销/券礼包活动/券礼包活动", "注册发大礼包") }}</div>
                <div class="content-text">
                  {{i18n('/营销/券礼包活动/新建注册发大礼包/给不同渠道注册的会员送大礼包')}}<br />
                  {{i18n('/营销/券礼包活动/新建注册发大礼包/促进会员招募，有效拉新')}}
                </div>
                <div class="btn-block">
                  <span class="plain-btn-blue" @click="doActiveAdd('register-send-gift')"
                    v-if="hasOptionPermission('/营销/营销/会员成长/注册发大礼包', '活动维护')">
                    {{ i18n("立即创建") }}
                  </span>
                </div>
              </div>
              <!-- 微信激活发大礼包 -->
              <div class="content-block" v-if="hasOptionPermission('/营销/营销/会员成长/微信激活发大礼包', '活动查看')">
                <div class="content-subtitle">{{ formatI18n("/营销/券礼包活动/券礼包活动", "微信激活发大礼包") }}</div>
                <div class="content-text">
                  {{i18n('/营销/微信激活发大礼包/领取微信会员卡并激活后送大礼包')}}<br />
                  {{i18n('/营销/微信激活发大礼包/促进会员招募，有效拉新')}}
                </div>
                <div class="btn-block">
                  <span class="plain-btn-blue" @click="doActiveAdd('wx-active-send-gift')"
                    v-if="hasOptionPermission('/营销/营销/会员成长/微信激活发大礼包', '活动维护')">
                    {{ i18n("立即创建") }}
                  </span>
                </div>
              </div>
              <!-- 完善资料有礼 -->
              <div class="content-block" v-if="hasOptionPermission('/营销/营销/会员成长/完善资料有礼', '活动查看')">
                <div class="content-subtitle">{{ formatI18n("/公用/菜单", "完善资料有礼") }}</div>
                <div class="content-text">
                  {{i18n('/营销/完善资料有礼/鼓励用户在平台上完善个人资料，以获得相应的奖励或福利')}}<br />
                  {{i18n('/营销/完善资料有礼/提高用户粘性，为用户提供更个性化的体验')}}
                </div>
                <div class="btn-block">
                  <span class="plain-btn-blue" @click="doActiveAdd('improveProfiles-add')" v-if="hasOptionPermission('/营销/营销/会员成长/完善资料有礼', '活动维护')">
                    {{ i18n("立即创建") }}
                  </span>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane :label="formatI18n('/营销/券礼包活动/券礼包活动', '活动管理')" name="active-manage-list">
            <MembershipGrowthList></MembershipGrowthList>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./MembershipGrowth.ts">
</script>

<style lang="scss">
.membership-growth-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  .score-header {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    padding-bottom: 20px;
    .header-block {
      margin: 25px 0 0 30px;
      padding: 0 0 20px;
    }
    .header-bottom {
      color: #333333;
      border-bottom: 2px solid #333333;
    }
  }
  .score-content {
    padding: 0 30px 0 20px;
    .content-title {
      font-size: 16px;
      padding-left: 10px;
      padding-top: 15px;
    }
    .content-flex {
      display: flex;
      justify-content: flex-start;
      margin: 30px 0;
    }
  }
  .el-tabs__nav-wrap::after {
    display: none;
  }

  .main-tabs {
    .manage-add-block {
      display: flex;
      flex-wrap: wrap;
      padding: 20px 0px 20px 32px;
      .content-block {
        position: relative;
        width: 31%;
        height: 200px;
        margin: 0 20px 12px 0;
        padding: 24px;
        background: #f7f9fc url("~assets/image/marketing/img_ticket_card_bg.png") top right no-repeat;
        border-radius: 8px;
        box-sizing: border-box;
        .content-subtitle {
          font-size: 20px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #242633;
          margin-bottom: 10px;
        }
        .content-text {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #79879e;
          line-height: 20px;
          height: 60px;
        }
        .btn-block {
          position: absolute;
          left: 24px;
          bottom: 24px;
        }
      }
    }
    & > .el-tabs__header {
      display: flex;
      align-items: center;
      background-color: #ffffff;
      height: 56px;
      border-radius: 8px;
      padding-top: 6px;
      .el-tabs__item.is-active {
        color: #007eff !important;
        font-weight: 600;
      }
      .el-tabs__item {
        font-weight: 400;
      }
    }
    & > .el-tabs__content {
      background-color: #ffffff;
      border-radius: 8px;
    }
  }
}
</style>