/*
 * @Author: 黎钰龙
 * @Date: 2024-11-01 14:34:25
 * @LastEditTime: 2025-03-07 10:29:53
 * @LastEditors: haiding <EMAIL>
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\cmp\page-place-components\double-column-image\DoubleColumnImage.ts
 * 记得注释
 */
import { Vue, Component, Prop } from 'vue-property-decorator';
import PlaceTemplateMixins from '../PlaceTemplateMixins';
const testImg = require('@/assets/image/ic_danlantupian.png')
import I18nPage from 'common/I18nDecorator';

@Component({
  name: 'DoubleColumnImage',
  components: {},
  mixins: [PlaceTemplateMixins],
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理',
  ],
  auto: true
})
export default class Rotation extends Vue {
  @Prop()
  componentItem: any;
  flag: boolean = false
  mounted() {
    this.flag = true
  }
  get localProperty() {
    return this.componentItem.props;
  }

  // selectImage(index) {
  //   if (index < 0 || index >= this.localProperty.propImages.length || index === this.currentIndex) {
  //     return;
  //   }
  //   this.currentIndex = index;
  // }

  // 展示图片数据
  get imgList() {
    this.flag = false
    this.$nextTick(() => {
      this.flag = true
    })
    if (this.localProperty.propImages.length > 0) {
      return this.localProperty.propImages;
    } else {
      return [
        {
          id: 1,
        },
      ];
    }
  }

  // get ossSourceUrl() {
  //   return this.$store.state.credential.host + '/-/cms/thumbnail/';
  // }
  get imgUrl() {
    return testImg
  }
  toolbarClick(e) {
    this.$emit('toolBarClick', {
      clickName: e,
      activeIndex: this.activeIndex,
    });
  }
}
