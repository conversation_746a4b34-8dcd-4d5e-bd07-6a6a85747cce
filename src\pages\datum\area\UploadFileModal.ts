import { Component, Prop, Vue } from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import EnvUtil from 'util/EnvUtil'
import DownloadCenterDialog from 'pages/main/dowmload_center_dialog/DownloadCenterDialog.vue'
import IdName from 'model/common/IdName'
import UploadApi from "http/upload/UploadApi";

@Component({
  name: 'UploadFileModal',
  components: {
    FormItem,
    DownloadCenterDialog,
  }
})
export default class UploadFileModal extends Vue {
  $refs: any
  dialogShow = false
  templateType = 'area'
  fileCount = 0
  fileList:any = []
  loading = false
  dialogvisiable = false
  uploadHeaders: any = {}
  marketingCenter: string
  marketingCenters: IdName[] = []
  enableMultiMarketingCenter: boolean = false
  // @Prop()
  // targetMarketingCenter: Nullable<string> = null
  // url: string
  @Prop({
    default: false
  })
  fromMarketingCenter: boolean

  get storeTplFile() {
    if (this.fromMarketingCenter) {
      if (location.href.indexOf('localhost') === -1) {
        return 'template_zones.xlsx'
      } else {
        return 'template_zones.xlsx'
      }
    }
    if (!this.enableMultiMarketingCenter) {
      if (location.href.indexOf('localhost') === -1) {
        return 'template_zones.xlsx'
      } else {
        return 'template_zones.xlsx'
      }
    } else {
      if (location.href.indexOf('localhost') === -1) {
        return 'template_zones.xlsx'
      } else {
        return 'template_zones.xlsx'
      }
    }
  }

  get getUploadUrl() {
    if (this.templateType === 'area') {
      // if (this.targetMarketingCenter) {
      //   return EnvUtil.getServiceUrl() + `/v1/zone/importZone` + '?targetMarketingCenter=' + this.targetMarketingCenter
      // } else {
        return EnvUtil.getServiceUrl() + `v1/zone/importZone`
      // }
    }
  }
  created() {
    let locale = sessionStorage.getItem('locale')
    this.uploadHeaders = {
			locale: locale ? locale : "zh_CN",
			time_zone: new Date().getTimezoneOffset(),
			marketingCenter: sessionStorage.getItem("marketCenter")
		};
    const authorization = EnvUtil.getUniAuthorization()
    if (authorization) {
      this.uploadHeaders.authorization = authorization
    }
  }
  show(marketingCenters: IdName[], enableMultiMarketingCenter: boolean) {
    this.marketingCenters = marketingCenters
    this.enableMultiMarketingCenter = enableMultiMarketingCenter
    this.dialogShow = true
  }

  doModalClose(type: string) {
    if (type === 'confirm') {
      // if (this.enableMultiMarketingCenter && this.marketingCenters) {
      //   this.$message.warning(this.formatI18n("/资料/门店/修改所属营销中心/请选择营销中心") as string)
      // }
      if (this.fileCount > 0) {
        this.$refs.upload.submit();
      } else {
        this.$message.warning(this.formatI18n('/公用/导入/请先选择文件'))
      }
    } else {
      this.fileList = []
      this.dialogShow = false
      this.$emit('dialogClose')
    }
  }

  doHandleChange(file: any, fileList: any) {
    if (fileList.length > 0) {
      this.fileCount++
      this.fileList = [fileList[fileList.length - 1]]
    }
  }

  doBeforeClose(done: any) {
    this.$emit('dialogClose')
    this.fileList = []
    done()
  }

  beforeUpload() {
    this.loading = true
  }

  getSuccessInfo(a: any, b: any, c: any) {
    this.loading = false
    if (a && a.code === 2000) {
      this.$refs.upload.clearFiles()
      this.fileCount = 0
      this.$emit('dialogClose')
      this.$emit('upload-success')
      this.dialogShow = false
      this.dialogvisiable = true
    }
  }

  getErrorInfo(a: any, b: any, c: any) {
    this.loading = false
    this.$message.error(this.formatI18n('/公用/导入/导入失败，请重新导入'))
    this.fileCount = 0
    this.$refs.upload.clearFiles()
  }
  private dialogClose() {
    this.dialogvisiable = false
    this.$emit('getList')
    this.$emit('getOrgList')
  }

  downloadTemplate() {
    UploadApi.getUrl(this.storeTplFile).then((resp: any) => {
      if (resp && resp.data) {
        window.open(resp.data);
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
}