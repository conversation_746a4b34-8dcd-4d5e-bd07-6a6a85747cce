import {Component, Prop, Vue} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'

@Component({
    name: 'CheckGoodsDialog',
    components: {
        FormItem
    }
})
export default class CheckGoodsDialog extends Vue {
    @Prop()
    data: any
    @Prop()
    title: any

    @Prop({
        type: Boolean,
        default: false
    })
    dialogShow: boolean

    doBeforeClose(done: any) {
        this.$emit('dialogClose')
        done()
    }
    doModalClose() {
        this.$emit('dialogClose')
    }
}