import { AnalysisOperateType } from 'model/default/AnalysisOperateType'
import { AnalysisPropType } from 'model/default/AnalysisPropType'

export default class MemberAnalysisMetrics {
  // 
  type: Nullable<AnalysisPropType> = null
  // 
  memberProp: Nullable<string> = null
  //
  memberPropName: Nullable<string> = null
  // 
  tagId: Nullable<string> = null
  //
  tagName: Nullable<string> = null
  // 
  operateType: Nullable<AnalysisOperateType> = null
  // 
  value: Nullable<string> = null
}