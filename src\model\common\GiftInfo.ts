/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-03-14 15:07:49
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\common\GiftInfo.ts
 * 记得注释
 */
import CouponItem from 'model/common/CouponItem'
import IdName from './IdName'
import StoreRange from './StoreRange'
import GiftInfoBenefitCard from 'model/equityCard/default/GiftInfoBenefitCard'

export default class GiftInfo {
  // 赠送积分
  points: Nullable<number> = null
  // 返现金额
  rebateAmount: Nullable<number> = null
  // 赠券
  couponItems: CouponItem[] = []
  // 赠送权益卡信息
  equityCard: Nullable<IdName> = null
   // 赠送权益卡
  benefitCards: GiftInfoBenefitCard[] = []
   // 赠送付费会员卡
  paidBenefitCards: GiftInfoBenefitCard[] = []
  // 兑换码个数
  exchangeCodeQty: Nullable<number> = null
  // 赠礼门店
  giftStore: Nullable<StoreRange> = null
}