import { Component, Vue } from 'vue-property-decorator'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import FormItem from 'cmp/formitem/FormItem.vue'
import PrePayAdjustBillApi from 'http/prepay/adjustbill/PrePayAdjustBillApi'
import PrepayAdjustBill from 'model/prepay/adjustbill/PrepayAdjustBill'
import PrepayAdjustBillLine from 'model/prepay/adjustbill/PrepayAdjustBillLine'
import ConstantMgr from 'mgr/ConstantMgr'
import PrePayConfigApi from 'http/prepay/config/PrePayConfigApi'
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import I18nPage from 'common/I18nDecorator'
import StoreValueAdjustPermission from 'pages/deposit/mbrdeposit/adjust/StoreValueAdjustPermission'
import SysConfigApi from "http/config/SysConfigApi";
import CountingCardReportApi from "http/prepay/card/CountingCardReportApi";
import DownloadCenterDialog from "pages/main/dowmload_center_dialog/DownloadCenterDialog";
import RejectDialog from './dialog/RejectDialog.vue'
import BPrepayAdjustBillOperatorRequest from 'model/default/BPrepayAdjustBillOperatorRequest'
@Component({
  name: 'StoreValueAdjustDtl',
  components: {
    SubHeader,
    FormItem,
    BreadCrume,
    DownloadCenterDialog,
    RejectDialog
  }
})
@I18nPage({
  prefix: [
    '/储值/会员储值/储值调整单/详情',
    '/公用/按钮',
    '/公用/过滤器'
  ]
})
export default class StoreValueAdjustDtl extends Vue {
  $refs: any
  i18n: I18nFunc
  permission = new StoreValueAdjustPermission()
  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  dtlTableData = []
  oparatorTableData = []
  billDtl: PrepayAdjustBill = new PrepayAdjustBill()
  queryDtl: PrepayAdjustBillLine[] = []
  switchFlag = false
  showOrg = false // 控制模态框的展示
  showTip = false
  fileDialogVisible = false //文件中心弹窗
  hasOaPermission = false // 控制oa审批按钮的展示
  get panelArray() {
    return [
      {
        name: this.i18n('储值调整单'),
        url: 'store-value-adjust'
      },
      {
        name: this.i18n('储值调整单详情'),
        url: ''
      }
    ]
  }

  created() {
    this.getStoreValueDtl()
    this.getQueryDetail()
    this.getPrePermission()
    this.getConfig()
  }
  /**
   * 分页页码改变的回调
   * @param val
   */
  doBack() {
    this.$router.push({ name: 'store-value-adjust' })
  }
  doModify() {
    this.$router.push({ name: 'store-value-adjust-Add', query: { id: this.$route.query.id, from: 'edit' } })
  }
  doAudit() {
    return new Promise<void>((resolve, reject) => {
      PrePayAdjustBillApi.audit(this.$route.query.id.toString()).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.i18n('审核成功'))
          resolve()
          this.getStoreValueDtl()
          this.getQueryDetail()
        } else {
          throw new Error(resp.msg!)
        }
      }).catch((error) => {
        if (error && error.message) {
          this.$message.error(error.message)
        }
        reject()
      })
    })
  }
  doExport() {
    PrePayAdjustBillApi.exportLine(this.$route.query.id.toString()).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.exportAfter()
      }
    }).catch((error: any) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  exportAfter() {
    this.showTip = true
    this.fileDialogVisible = true
  }

  doDownloadDialogClose() {
    this.showTip = false
    this.fileDialogVisible = false
  }

  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.getQueryDetail()
  }
  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.getQueryDetail()
  }
  private getStoreValueDtl() {
    PrePayAdjustBillApi.get(this.$route.query.id.toString()).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.billDtl = resp.data
        this.dtlTableData = resp.data.lines
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  private getQueryDetail() {

    PrePayAdjustBillApi.queryDetail(this.$route.query.id.toString(), this.page.currentPage - 1, this.page.size).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.queryDtl = resp.data
        this.page.total = resp.total
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
  private getPrePermission() {
    const loading = this.$loading(ConstantMgr.loadingOption)
    PrePayConfigApi.get().then((resp: any) => {
      if (resp && resp.code === 2000) {
        loading.close()
        if (resp.data && resp.data.enableMultipleAccount) { // 开启多账户
          this.switchFlag = true
        } else {
          this.switchFlag = false // 未开启多账户
        }
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
      loading.close()
    })
  }

  private getOrgStr() {
    let str: any = this.formatI18n('/权益/积分/积分调整单详情/单头/发生组织：{0}')
    str = str.replace(/\{0\}/g,
      `${this.billDtl.occurredOrg ? this.billDtl.occurredOrg.name : ''}[${this.billDtl.occurredOrg ? this.billDtl.occurredOrg.id : ''}]`)
    return str
  }

  private getConfig() {
    // 获取储值调整单oa配置
    PrePayAdjustBillApi.getOaConfig().then((resp) => {
      if (resp.code === 2000) {
        this.hasOaPermission = resp.data || false
      } else {
        throw new Error(resp.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    })

    SysConfigApi.get().then((resp: any) => {
      if (resp && resp.data) {
        this.showOrg = resp.data.enableMultiMarketingCenter
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  doReject() {
    this.$refs.rejectDialog.visible = true
  }

  // 确认驳回
  doSubmitReject(remark: string) {
    const operatorRequest: BPrepayAdjustBillOperatorRequest = new BPrepayAdjustBillOperatorRequest()
    operatorRequest.billNum = this.$route.query.id.toString()
    operatorRequest.remark = remark
    PrePayAdjustBillApi.rejected(operatorRequest).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(this.i18n('操作成功'))
        this.getStoreValueDtl()
        this.getQueryDetail()
      } else {
        throw new Error(resp.msg!)
      }
    }).catch((error) => {
      this.$message.error(error.message)
    })
  }

  // 提交
  doSubmit() {
    return new Promise<void>((resolve, reject) => {
      const operatorRequest: BPrepayAdjustBillOperatorRequest = new BPrepayAdjustBillOperatorRequest()
      operatorRequest.billNum = this.$route.query.id.toString()
      PrePayAdjustBillApi.submit(operatorRequest).then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.$message.success(this.i18n('操作成功'))
          resolve()
          this.getStoreValueDtl()
          this.getQueryDetail()
        } else {
          throw new Error(resp.msg!)
        }
      }).catch((error) => {
        this.$message.error(error.message)
        reject()
      })
    })
  }

  // 提交并审核
  async doSubmitAndAudit() {
    await this.doSubmit()
    await this.doAudit()
  }
}
