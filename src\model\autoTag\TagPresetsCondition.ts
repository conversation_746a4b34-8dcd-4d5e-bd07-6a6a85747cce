/*
 * @Author: 黎钰龙
 * @Date: 2024-11-26 09:56:32
 * @LastEditTime: 2024-11-26 09:56:53
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\tagv2\TagPresetsCondition.ts
 * 记得注释
 */
import { PropType } from "model/autoTag/PropType"

export default class TagPresetsCondition {
  // 条件值
  value: Nullable<string> = null
  // 条件类别
  category: Nullable<string> = null
  // 条件数据类型
  type: Nullable<PropType> = null
}