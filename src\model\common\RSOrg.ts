/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-05-28 09:43:06
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\common\RSOrg.ts
 * 记得注释
 */
import IdName from 'model/common/IdName'
import platOrgInfos from './PlatOrgInfos'
import Address from "model/common/Address";
import OrgCircle from "model/common/OrgCircle";
import OrgTag from 'model/datum/org/OrgTag';
import {OrgState} from "model/common/OrgState";

export default class RSOrg {
  //
  marketingCenter: Nullable<string> = null

  marketingCenterName: Nullable<string> = null
  // 组织信息
  org: IdName = new IdName()
  // 区域
  zone?: IdName = new IdName()
  // 平台门店信息
  platOrgInfos?: Nullable<platOrgInfos[]> = null
  // 备注
  remark: Nullable<string> = null
  // 纬度
  lat: Nullable<number> = null
  // 经度
  lng: Nullable<number> = null
  // 地址
  address: Nullable<Address> = null
  // 商圈
  circles: OrgCircle[] = []
  // 省
  province: Nullable<IdName> = null
  // 市
  city: Nullable<IdName> = null
  // 区
  district: Nullable<IdName> = null
  // 街道
  street: Nullable<IdName> = null
  // 联系电话
  telephone: Nullable<string> = null
  // 是否会员店
  memberStore: Nullable<boolean> = null
  // 门店标签
  orgTags: OrgTag[] = []
  // 状态
  orgState: Nullable<OrgState> = null
  // 区域主管代码
  areaLeader: Nullable<string> = null
  // 区域主管姓名
  areaLeaderName: Nullable<string> = null
  // 营运经理代码
  operationManager: Nullable<string> = null
  // 营运经理姓名
  operationManagerName: Nullable<string> = null
  // 是否是默认门店
  defaultStore: Nullable<boolean> = null
}