<template>
  <el-dialog :title="i18n('门店推广码下载')" :close-on-click-modal="false" class="employee-code-download" :visible.sync="dialogShow"
    :before-close="doBeforeClose">
    <div class="wrap">
      <el-radio-group v-model="channel">
        <el-radio-button v-for="item in channelOptions" :key="item" :label="item">
          {{getLabel(item)}}
        </el-radio-button>
      </el-radio-group>
      <div style="margin-top: 20px">
        <el-input :placeholder="formatI18n('/会员/微信会员初始化/已授权/初始化第三步投放微信会员卡/门店员工二维码/输入名称或代码')" v-model="code" style="width: 250px"></el-input>
        <el-button :loading="isLoading" type="primary" @click="doSearch" style="margin-left: 4px">
          {{formatI18n('/会员/微信会员初始化/已授权/初始化第三步投放微信会员卡/门店员工二维码/搜索')}}
        </el-button>
      </div>
      <div class="flex-wrap">
        <template v-if="!isEmptyArray()">
          <div class="flex-item" v-for="(item,index) in queryData" :key="index" @click="doDownloadStore(item.org.id)">
            <div class="ellpisis" :title="item.org.name">{{item.org.name}}</div>
            <div class="ellpisis" :title="item.org.id">{{item.org.id}}</div>
          </div>
        </template>
        <div v-else class="centered-text">
          {{i18n('搜索某门店可以下载该门店二维码')}}
        </div>
      </div>
    </div>
    <div class="dialog-footer" slot="footer">
      <span style="padding-right: 30px">
        <i18n k="/会员/会员资料/共{0}条">
          <span slot="0" style="margin: 0 5px;">{{ total }}</span>
        </i18n>
      </span>
      <el-button :loading="isLoading" @click="doConfirmClose()" size="small" type="primary">
        {{formatI18n('/会员/微信会员初始化/已授权/初始化第三步投放微信会员卡/门店员工二维码/下载全部')}}
      </el-button>
      <el-button @click="doCancel()">{{ formatI18n('/设置/权限/角色管理/包含用户/添加/添加用户', '取消') }}</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./StoreCodeDialog.ts">
</script>

<style lang="scss" scoped>
.centered {
  width: 100%;
}

.centered-text {
  width: 100%;
  display: flex;
  justify-content: center; /* 水平居中 */
  color: #d3d3d3; /* 浅灰色文字 */
  font-size: 20px; /* 字体大小 */
  margin-top: 100px;
}
.employee-code-download {
  display: flex;
  align-items: center;
  justify-content: center;
  .el-dialog {
    height: 550px;
    width: 578px;
    .wrap {
      height: 360px;
      ::v-deep .is-active {
        .el-radio-button__inner {
          color: #fff;
          background: #20a0ff;
        }
      }
    }
  }
  .flex-wrap {
    display: flex;
    height: 300px;
    overflow: auto;
    flex-wrap: wrap;
    .flex-item {
      width: 80px;
      height: 45px;
      border: 1px solid #bfbfbf;
      border-radius: 5px;
      margin: 20px;
      .ellpisis {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        text-align: center;
        cursor: pointer;
      }
    }
  }
}
</style>