<!--
 * @Author: 黎钰龙
 * @Date: 2024-05-06 11:18:29
 * @LastEditTime: 2025-03-05 10:30:33
 * @LastEditors: haiding <EMAIL>
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\invest\nav-manage\NavManage.vue
 * 记得注释
-->
<template>
  <div class="nav-manage" v-if="showFlag">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button size="large" type="primary" v-if="navType=='weixinApp' && hasOptionPermission('/设置/小程序装修/微信导航设置','设置') || navType=='aliApplet' && hasOptionPermission('/设置/小程序装修/支付宝导航设置','设置') || navType=='h5' && hasOptionPermission('/设置/小程序装修/H5导航设置','设置')" @click="doSave">{{ i18n('保存') }}</el-button>
        <el-button size="large" @click="doCancel">{{ i18n('取消') }}</el-button>
      </template>
    </BreadCrume>
    <!-- 导航设置 -->
    <div class="nav-panel">
      <div class="phone-box">
        <img src="~assets/image/navcustom/nav_manage_header.png" class="phone-header">
        <div class="phone-btm">
          <!-- 左侧底部效果图部分 -->
          <div class="phone-tab">
            <effect-tab v-if="custom.navigationType" :showModel="custom.navigationType" :customData="custom"></effect-tab>
          </div>
          <div class="phone-line"><span></span></div>
        </div>
      </div>
      <custom-tab v-if="custom.navigationType" ref="customTab" :customForm="custom"
        :channel='navType'
        @update="updateData"></custom-tab>
    </div>
  </div>
</template>

<script lang="ts" src="./NavManage.ts">
</script>

<style lang="scss" scoped>
.nav-manage {
  width: 100%;

  .nav-panel {
    background: #FFFFFF;
    border-radius: 8px;
    padding: 24px;
    display: flex;

    .phone-box {
      position: relative;
      width: 375px;
      min-width: 330px;
      min-height: 450px;
      max-height: 780px;
      background: #F9F9F9;
      border-radius: 8px;
      border: 1px solid #D7DFEB;
      overflow: hidden;

      .phone-header {
        width: 100%;
      }

      .phone-btm {
        width: 100%;
        height: 84px;
        background: #FFFFFF;
        // overflow: hidden;
        bottom: 0;
        position: absolute;

        .phone-tab {
          width: 100%;
          height: 50px;
          background: #FFFFFF;
          border-top: 1px solid #EEEEEE;
          border-bottom: 1px solid #EEEEEE;
          box-sizing: border-box;
        }

        .phone-line {
          span {
            display: block;
            width: 134px;
            height: 5px;
            background: #2C3036;
            border-radius: 2px;
            margin: 20px auto;
          }
        }
      }
    }
  }
}
</style>