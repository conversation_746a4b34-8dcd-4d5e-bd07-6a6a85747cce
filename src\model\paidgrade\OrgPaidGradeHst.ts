import IdName from 'model/common/IdName'
import MemberIdent from 'model/common/member/MemberIdent'

export default class OrgPaidGradeHst extends MemberIdent {
    // 发生时间
    payTime: Nullable<Date> = null
    // 发生组织
    store: Nullable<IdName> = null
    // 套餐名称
    gradeFeeSetName: Nullable<string> = null
    // 等级有效天数
    memberGradeValidDay: Nullable<number> = null
    // 等级到期时间
    memberGradeValidate: Nullable<Date> = null
    // 支付金额
    payAmount: Nullable<number> = null
    // 支付方式
    payType: Nullable<string> = null
    // 交易号
    tradeNo: Nullable<string> = null
}