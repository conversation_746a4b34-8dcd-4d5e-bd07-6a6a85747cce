/**
 * 循环遍历组件，并自动引入
 */
import { Vue, Component, Prop } from 'vue-property-decorator';
const lodash = require('lodash');
const requireComponent = import.meta.glob('./**/**.vue')
Object.keys(requireComponent).forEach(file => {
  const componentConfig = requireComponent[file];
  // 获取组件的 PascalCase 命名
  const componentName = lodash.upperFirst(
    lodash.camelCase(
      file
        .split('/')
        .pop()
        ?.replace(/\.\w+$/, '')
    )
  );
  // Globally register the component
  Vue.component(componentName, componentConfig);
})
