import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import I18nPage from 'common/I18nDecorator';
import { Component, Vue } from 'vue-property-decorator';
import UploadImg from 'cmp/upload-img/UploadImg';
import ContentTemplateApi from 'http/template/ContentTemplateApi';
import CommonUtil from 'util/CommonUtil';
import PublishRequest from 'model/template/PublishRequest';
import ContentTemplate from 'model/template/ContentTemplate';
import BalanceCodeDecorationModal from './template/BalanceCodeDecorationModal';

@Component({
  name: 'PageBalanceCodeEdit',
  components: {
    BreadCrume,
    UploadImg
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理',
    '/设置/页面管理',
    '/会员/付费会员'
  ],
  auto: true
})
export default class PageBalanceCodeEdit extends Vue {
  $refs: any
  ruleForm: BalanceCodeDecorationModal = new BalanceCodeDecorationModal()
  pageDetail: ContentTemplate = new ContentTemplate()

  get panelArray() {
    return [
      {
        name: this.i18n("编辑储值付款码页面"),
        url: "",
      },
    ];
  }

  get rules() {
    return {
      name: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: ["blur", "change"] }],
      propBackgroundImage: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: ["blur", "change"] }],
      propLogo: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: ["blur", "change"] }]
    };
  }

  created() {
    if (this.$route.query.id) {
      this.loadData();
    }
  }

  loadData() {
    const loading = CommonUtil.Loading();
    ContentTemplateApi.get(this.$route.query.id as string)
      .then((res) => {
        if (res.code === 2000 && res.data) {
          this.pageDetail = res.data;
          const content = JSON.parse(this.pageDetail.content)
          console.log('content', content);
          if (content.widgets.length > 1) {
            const valueObj = content.widgets[1].props
            this.ruleForm.propBackgroundImage = valueObj.propBackgroundImage
            this.ruleForm.propLogo = valueObj.propLogo
            this.ruleForm.propOtherPayTypes = valueObj.propOtherPayTypes
            this.ruleForm.name = content.widgets[1].name
            this.ruleForm.uuid = content.widgets[1].uuid
            this.ruleForm.id = content.widgets[1].id
          }
        } else {
          throw new Error(res.msg || String(res.code));
        }
      })
      .catch((error) => {
        this.$message.error((error as Error).message);
      })
      .finally(() => {
        loading.close();
      })
  }

  // 取消返回
  goBack() {
    this.$router.push({
      name: "page-manage",
      query: { activeName: "sys-manage" },
    });
  }

  preserve(isPublish: boolean) {
    this.$refs.form.validate(async (valid: any) => {
      if (valid) {
        await this.doUpdate(isPublish);
        if (isPublish) {
          this.publish();
        }
      } else {
        this.$message.error(this.i18n("请完善装修信息！"));
      }
    });
  }

  // 调用发布接口
  publish() {
    const params = new PublishRequest()
    params.id = this.$route.query.id as string;
    console.log('发布', this.$route.query);
    ContentTemplateApi.publish(params)
      .then((res) => {
        if (res.code === 2000) {
          this.$message.success(this.i18n("发布成功"));
          this.$router.push({
            name: "page-manage",
            query: { activeName: "sys-manage" },
          });
        } else {
          this.$message.error(res.msg || this.i18n("发布失败"));
        }
      })
      .catch((error) => {
        let e = error as any;
        this.$message.error(e.message || this.i18n("发布失败"));
      });
  }

  // 编辑
  async doUpdate(isStopNavigate: boolean = true) {
    const loading = CommonUtil.Loading();
    try {
      const params = JSON.parse(JSON.stringify(this.pageDetail))
      params.type = "system"
      params.name = this.i18n("储值支付")
      const customWidget = {
        id: 'depositPayCode',
        name: this.i18n("储值支付"),
        type: "custom",
        props: this.ruleForm, //保存时多传了几个参数，后端解析会忽略，不影响最终数据
        uuid: ""
      }
      params.content = JSON.parse(params.content)
      params.content.widgets = [JSON.parse(this.pageDetail.content).widgets[0], customWidget]
      let res = await ContentTemplateApi.update(params);
      if (res.code === 2000) {
        this.$message.success(this.i18n("保存成功"));
        if (!isStopNavigate) {
          this.$router.push({
            name: "page-manage",
            query: { activeName: "sys-manage" },
          });
        }
      } else {
        this.$message.error(res.msg || this.i18n("保存失败"));
      }
    } catch (error) {
      this.$message.error((error as Error).message || this.i18n("保存失败"));
    } finally {
      loading.close();
    }
  }

  uploadImgChange(type: string) {
    this.$refs.form.validateField(type)
  }
};