/*
 * @Author: 黎钰龙
 * @Date: 2024-06-03 18:46:13
 * @LastEditTime: 2024-06-06 11:03:43
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\analysis\AnalysisReportApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import OrgMemberAnalysisReport from 'model/analysis/OrgMemberAnalysisReport'
import OrgMemberAnalysisReportQuery from 'model/analysis/OrgMemberAnalysisReportQuery'
import OrgTradeAnalysisReport from 'model/analysis/OrgTradeAnalysisReport'
import OrgTradeAnalysisReportQuery from 'model/analysis/OrgTradeAnalysisReportQuery'
import PlatformCouponAnalysisReport from 'model/analysis/PlatformCouponAnalysisReport'
import Response from 'model/common/Response'
import MemberPointsAnalysisReportQuery from "model/analysis/MemberPointsAnalysisReportQuery";
import MemberPointsAnalysisReport from "model/analysis/MemberPointsAnalysisReport";
import MemberDepositAnalysisReport from "model/analysis/MemberDepositAnalysisReport";
import MemberDepositAnalysisReportQuery from "model/analysis/MemberDepositAnalysisReportQuery";

export default class AnalysisReportApi {
  /**
   * 门店会员分析报表
   * 门店会员分析报表
   * 
   */
  static orgMemberReport(body: OrgMemberAnalysisReportQuery): Promise<Response<OrgMemberAnalysisReport>> {
    return ApiClient.server().post(`/v1/analysis-report/org-member`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 门店会员分析报表导出
  * 门店会员分析报表导出
  * 
  */
  static orgMemberExport(body: OrgMemberAnalysisReportQuery): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/analysis-report/org-member/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
 * 交易分析报表
 * 交易分析报表
 * 
 */
  static orgTradeReport(body: OrgTradeAnalysisReportQuery): Promise<Response<OrgTradeAnalysisReport>> {
    return ApiClient.server().post(`/v1/analysis-report/org-trade`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 交易分析报表导出
  * 交易分析报表导出
  * 
  */
  static exportOrgTradeReport(body: OrgTradeAnalysisReportQuery): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/analysis-report/org-trade/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 平台券分析报表
  * 平台券分析报表
  * 
  */
  static platformCouponReport(body: OrgMemberAnalysisReportQuery): Promise<Response<PlatformCouponAnalysisReport>> {
    return ApiClient.server().post(`/v1/analysis-report/platform-coupon`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 平台券分析报表导出
  * 平台券分析报表导出
  * 
  */
  static exportPlatformCouponReport(body: OrgMemberAnalysisReportQuery): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/analysis-report/platform-coupon/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 会员积分分析报表
   * 会员积分分析报表
   *
   */
  static memberPointReport(body: MemberPointsAnalysisReportQuery): Promise<Response<MemberPointsAnalysisReport>> {
    return ApiClient.server().post(`/v1/analysis-report/member-points`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 会员积分分析报表导出
   * 会员积分分析报表导出
   *
   */
  static memberPointsExport(body: MemberPointsAnalysisReportQuery): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/analysis-report/member-points/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 会员储值分析报表
   * 会员储值分析报表
   *
   */
  static memberDepositReport(body: MemberDepositAnalysisReportQuery): Promise<Response<MemberDepositAnalysisReport>> {
    return ApiClient.server().post(`/v1/analysis-report/member-deposit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 会员储值分析报表导出
   * 会员储值分析报表导出
   *
   */
  static exportMemberDepositReport(body: MemberDepositAnalysisReportQuery): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/analysis-report/member-deposit/export`, body, {
    }).then((res) => {
      return res.data
    })
  }
}
