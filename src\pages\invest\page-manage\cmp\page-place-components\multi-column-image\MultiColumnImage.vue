<template>
  <div
    class="page-top"
    :style="{
      padding:
        localProperty.styMarginTop +
        'px ' +
        localProperty.styMarginRight +
        'px ' +
        localProperty.styMarginBottom +
        'px ' +
        localProperty.styMarginLeft +
        'px',
    }"
    :class="[{ activeCom: activeIndex === index }]"
    @click="activeTemplate"
  >
    <div class="toolBar" v-if="activeIndex === index">
      <setting-toolbar :btns="toolBarBtns" @click="toolbarClick"></setting-toolbar>
    </div>
    <div class="double-column-image-widget">
      <div class="defaul-image">
        <img v-if="!imgList[0].imageUrl" src="@/assets/image/icons/cutting_pic_empty.png" style="width: 84px; height: 72px" />
        <div class="no-image-tip" v-if="!imgList[0].imageUrl">{{ i18n('请在右侧添加图片') }}</div>
        <el-image style="width: 100%" :src="imgList[0].imageUrl" fit="contain" v-if="imgList[0].imageUrl"></el-image>
      </div>
      <div class="defaul-image">
        <div v-for="(item, index) in imgList" :key="item.id" v-show="index > 0" :class="{active:item.imageUrl}">
          <img v-if="!item.imageUrl" src="@/assets/image/icons/cutting_pic_empty.png" style="width: 84px; height: 72px" />
          <div class="no-image-tip" v-if="!item.imageUrl">{{ i18n('请在右侧添加图片') }}</div>
          <el-image style="width: 100%;height:100%" :src="item.imageUrl" fit="contain" v-if="item.imageUrl"></el-image>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./MultiColumnImage.ts"></script>

<style lang="scss" scoped>
.page-top {
  width: 100%;
  position: relative;
  margin-bottom: 15px;
  background: white;
  &-bg {
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-size: 100%;
  }
  &-title {
    text-align: center;
    padding-top: 42px;
    font-size: 20px;
    color: #fff;
  }
  .toolBar {
    position: absolute;
    top: 0;
    right: 1px;
    z-index: 999;
    cursor: pointer;
  }
  .double-column-image-widget {
    width: 100%;
    // height: 310px;
    display: flex;
    justify-content: space-between;
    .defaul-image {
      width: 50%;
      &:nth-of-type(1) {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
      }
      &:nth-of-type(2) {
        // flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        // justify-content: center;
        // height: 50%;
        .active {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      img {
        margin-top: 25px;
      }
      .no-image-tip {
        width: 100%;
        margin-top: 12px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #a1a6ae;
      }
    }

  }
}
</style>
