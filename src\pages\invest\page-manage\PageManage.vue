<!--
 * @Author: 黎钰龙
 * @Date: 2024-06-13 11:16:11
 * @LastEditTime: 2024-11-01 10:15:12
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\invest\page-manage\PageManage.vue
 * 记得注释
-->
<template>
  <div class="page-manage-container">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button size="large" type="primary" @click="doGetPath" v-if="hasOptionPermission('/设置/小程序装修/页面管理','推广')">
          {{i18n('获取页面地址')}}
        </el-button>
      </template>
    </BreadCrume>
    <div style="height: 95%;overflow: auto">
      <div class="score-header">
        <el-tabs class="main-tabs" @tab-click="handleTabClick" style="width: 100%" v-model="activeName">
          <el-tab-pane :label="i18n('自定义页面')" name="page-manage-list">
            <PageManageList></PageManageList>
          </el-tab-pane>
          <el-tab-pane :label="i18n('系统页面')" name="sys-manage">
            <SysManageList></SysManageList>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <SysPathDialog ref="sysPathDialog"></SysPathDialog>
  </div>
</template>

<script src="./PageManage.ts">
</script>

<style lang="scss">
.page-manage-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  .score-header {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    padding-bottom: 20px;
    .header-block {
      margin: 25px 0 0 30px;
      padding: 0 0 20px;
    }
    .header-bottom {
      color: #333333;
      border-bottom: 2px solid #333333;
    }
  }
  .score-content {
    padding: 0 30px 0 20px;
    .content-title {
      font-size: 16px;
      padding-left: 10px;
      padding-top: 15px;
    }
    .content-flex {
      display: flex;
      justify-content: flex-start;
      margin: 30px 0;
    }
  }
  .el-tabs__nav-wrap::after {
    display: none;
  }

  .main-tabs {
    & > .el-tabs__header {
      display: flex;
      align-items: center;
      background-color: #ffffff;
      height: 56px;
      border-radius: 8px;
      padding-top: 6px;
      .el-tabs__item.is-active {
        color: #007eff !important;
        font-weight: 600;
      }
      .el-tabs__item {
        font-weight: 400;
      }
    }
    & > .el-tabs__content {
      background-color: #ffffff;
      border-radius: 8px;
    }
  }
}
</style>