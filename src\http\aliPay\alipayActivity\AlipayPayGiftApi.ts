import ApiClient from "http/ApiClient";
import AlipayCouponNotifyRecordRequest from "model/alipay/alipayActivity/AlipayCouponNotifyRecordRequest";
import BAliConsumeGiftActivity from "model/alipay/alipayActivity/BAliConsumeGiftActivity";
import Response from "model/common/Response";

export default class AlipayPayGiftApi {
  // 活动详情
  static getAliAppletConsumeGiftActivity(
    id: string
  ): Promise<Response<BAliConsumeGiftActivity>> {
    return ApiClient.server()
      .get(`/v1/coupon-activity/getAliAppletConsumeGiftActivity/${id}`, {})
      .then((res) => {
        return res.data;
      });
  }
  // 活动保存
  static saveAliAppletConsumeGiftActivity(
    body: BAliConsumeGiftActivity
  ): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/saveAliAppletConsumeGiftActivity`, body, {})
      .then((res) => {
        return res.data;
      });
  }

  // 查询阿里券模板创建结果
  static getAlipayCouponNotifyRecord(body: AlipayCouponNotifyRecordRequest): Promise<Response<string>> {
    return ApiClient.server()
      .post(`/v1/coupon-activity/getAlipayCouponNotifyRecord`, body, {})
      .then((res) => {
        return res.data;
      });
  }

  // 获取支付宝导入券码数量
  static getAliImportCount(id: string): Promise<Response<string>> {
    return ApiClient.server()
      .get(`/v1/coupon-activity/getAliAppletImportCouponResult/${id}`, {})
      .then((res) => {
        return res.data;
      });
  }

  // 获取支付宝导入券码数量
  static getAliState(id: string): Promise<Response<string>> {
    return ApiClient.server()
      .get(`/v1/coupon-activity/getAliAppletConsumeGiftActivityState/${id}`, {})
      .then((res) => {
        return res.data;
      });
  }
}
