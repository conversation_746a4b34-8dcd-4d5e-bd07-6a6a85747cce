/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2023-08-30 10:47:25
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\prepay\report\card\GiftCardCardHst.ts
 * 记得注释
 */
import GiftReportBaseData from 'model/prepay/report/card/GiftReportBaseData'
import IdName from 'model/common/IdName'

export default class GiftCardCardHst extends GiftReportBaseData {
  // 售价
  price: Nullable<number> = null
  // 返现
  giftAmount: Nullable<number> = null
  // 支付方式
  payType: Nullable<string> = null
  // 活动
  activity: Nullable<IdName> = null
  // 次数
  times: Nullable<number> = null
  // 发生次数
  occurredTimes: Nullable<number> = null
  // 退卡金额
  refundAmount: Nullable<number> = null
  // 退款赠送金额
  refundGiftAmount: Nullable<number> = null
  // 退款实充金额
  refundDepositAmount: Nullable<number> = null
}