import { Component, Prop, Watch, Vue } from 'vue-property-decorator'
import BWeimobExtGoodsUseRule from 'model/common/weimob/BWeimobExtGoodsUseRule'
import BQueryCategoryRequest from 'model/common/weimob/BQueryCategoryRequest'
import BCategory from 'model/common/weimob/BCategory'
import BQueryGoodsClassifyRequest from 'model/common/weimob/BQueryGoodsClassifyRequest'
import BWeimobClassify from 'model/common/weimob/BWeimobClassify'
import BWeimobExtLimitGoodsCategoryRule from 'model/common/weimob/BWeimobExtLimitGoodsCategoryRule'
import WeimobExtLimitGoodsCategoryRuleInfo from 'model/common/weimob/BWeimobExtLimitGoodsCategoryRuleInfo'
import BWeimobExtLimitGoodsGroupRule from 'model/common/weimob/BWeimobExtLimitGoodsGroupRule'
import WeimobExtLimitGoodsGroupRuleInfo from 'model/common/weimob/WeimobExtLimitGoodsGroupRuleInfo'
import BWeimobExtLimitGoodsRule from 'model/common/weimob/BWeimobExtLimitGoodsRule'
import WeimobApi from 'http/coupon/template/WeimobApi'
import IdName from 'model/common/IdName'
import GoodsCategory from './weiMenCmp/GoodsCategory.vue'
import GoodsGroup from './weiMenCmp/GoodsGroup.vue'
import GoodsSku from './weiMenCmp/GoodsSku.vue'
import empty from './weiMenCmp/empty.vue'

@Component({
  components: { GoodsCategory, GoodsGroup, GoodsSku, empty }
})

export default class GoodsWeiMenEx extends Vue {
  $refs: any;
  @Prop({
    default: {}
  })
  goodsUseRule: any

  dialogShow: boolean = false; // 控制模态框的展示
  showExcludeGoods: boolean = false

  limitedGoods: boolean = true // false 不限商品, true 限制商品
  limitedGoodsType: string = 'goods' // goods 限制商品类型 ,goodsCategory 限制商品类别,  goodsGroup 限制商品组
  limitGoodsTypeRule = new BWeimobExtLimitGoodsRule()
  limitGoodsCategoryTypeRule = new BWeimobExtLimitGoodsCategoryRule()
  limitGoodsGroupRule = new BWeimobExtLimitGoodsGroupRule()
  existExcludeGoods: boolean = false // 是否包含下级自建商品
  categoryList: BCategory[] = []
  groupList: BWeimobClassify[] = []
  org: string = ''
  excludeGoodsShow: boolean = false //是否显示排除商品

  get skuIds() {
    if (this.limitedGoodsType == 'goods') {
      if (this.limitGoodsTypeRule.goodsRange == 'part') {
        return this.limitGoodsTypeRule.includeGoodsIds || []
      } else {
        return this.limitGoodsTypeRule.excludeGoodsIds || []
      }
    }
    if (this.limitedGoodsType == 'goodsCategory') {
      return this.limitGoodsCategoryTypeRule.excludeGoodsIds || []
    }
    if (this.limitedGoodsType == 'goodsGroup') {
      return this.limitGoodsGroupRule.excludeGoodsIds || []
    }
    return []
  }

  get goodsCategoryName() {
    let str = ''
    if (this.limitGoodsCategoryTypeRule && this.limitGoodsCategoryTypeRule.ruleInfos && this.limitGoodsCategoryTypeRule.ruleInfos.length > 0) {
      const firstCategoryInfo = this.limitGoodsCategoryTypeRule.ruleInfos[0]
      str = firstCategoryInfo.categoryName + ''
      if (firstCategoryInfo && firstCategoryInfo.childs && firstCategoryInfo.childs.length > 0) {
        const secondCategoryInfo = firstCategoryInfo.childs[0]
        str = str + ' > ' + secondCategoryInfo.categoryName
      }
    }
    return str
  }

  get groupListNum() {
    let num = 0
    if (this.limitGoodsGroupRule && this.limitGoodsGroupRule.ruleInfos && this.limitGoodsGroupRule.ruleInfos.length > 0) {
      const ruleInfos = this.limitGoodsGroupRule.ruleInfos || []
      ruleInfos.map(item => {
          num = item.childs && item.childs.length > 0 ? num + item.childs.length : num + 1
      })
    }
    return num
  }

  @Watch("goodsUseRule", {
    deep: true
  })
  onValueChange(value: BWeimobExtGoodsUseRule) {
    this.doBindValue(value)
  }
  mounted() {
    this.getOrg()
    this.doBindValue(this.goodsUseRule)
    this.queryCategoryList()
    this.queryGroupList()
  }

  doChangeTab(type: string) {
    this.limitedGoodsType = type
    this.showExcludeGoods = false
    if (type == 'goodsCategory') {
      this.queryCategoryList()
    }
    if (type == 'goodsGroup') {
      this.queryGroupList()
    }
  }
  doChangeSpecify() {
    this.existExcludeGoods = false
  }

  doBindValue(value: BWeimobExtGoodsUseRule) {
    this.limitedGoodsType = value.limitedGoodsType || 'goods'
    this.limitGoodsTypeRule = value.limitGoodsTypeRule ? JSON.parse(JSON.stringify(value.limitGoodsTypeRule)) : new BWeimobExtLimitGoodsRule()
    this.limitGoodsCategoryTypeRule = value.limitGoodsCategoryTypeRule ? JSON.parse(JSON.stringify(value.limitGoodsCategoryTypeRule)) : new BWeimobExtLimitGoodsCategoryRule()
    this.limitGoodsGroupRule = value.limitGoodsGroupRule ? JSON.parse(JSON.stringify(value.limitGoodsGroupRule)) : new BWeimobExtLimitGoodsGroupRule()
    if (this.limitedGoodsType == 'goods') {
      this.existExcludeGoods = this.limitGoodsTypeRule.includeChildGoods || false
    }
    if (this.limitedGoodsType == 'goodsCategory') {
      this.existExcludeGoods = this.limitGoodsCategoryTypeRule.includeChildGoods || false
    }
    if (this.limitedGoodsType == 'goodsGroup') {
      this.existExcludeGoods = this.limitGoodsGroupRule.includeChildGoods || false
    }
  }

  doCancel() {
    this.dialogShow = false
    this.doBindValue(this.goodsUseRule)
  }

  doClosed() {
    this.doBindValue(this.goodsUseRule)
  }

  doConfirm() {
    const params = new BWeimobExtGoodsUseRule()
    params.limitedGoods = true
    params.limitedGoodsType = this.limitedGoodsType
    if (this.limitedGoodsType == 'goods') {
      params.limitGoodsTypeRule = this.limitGoodsTypeRule
      if (this.limitGoodsTypeRule.goodsRange == 'part' && this.limitGoodsTypeRule.includeGoodsIds.length == 0) {
        this.$message({
          message: this.formatI18n('/公用/券模板/微盟适用商品', '请选择指定商品'),
          type: 'warning'
        })
        return
      }
      params.limitGoodsTypeRule.existExcludeGoods = this.limitGoodsTypeRule.excludeGoodsIds.length > 0
      params.limitGoodsTypeRule.includeChildGoods = this.existExcludeGoods
    }
    if (this.limitedGoodsType == 'goodsCategory') {
      params.limitGoodsCategoryTypeRule = this.limitGoodsCategoryTypeRule
      if (this.limitGoodsCategoryTypeRule.ruleInfos.length == 0) {
        this.$message({
          message: this.formatI18n('/公用/券模板/微盟适用商品', '请选择指定类别'),
          type: 'warning'
        })
        return
      }
      params.limitGoodsCategoryTypeRule.existExcludeGoods = this.limitGoodsCategoryTypeRule.excludeGoodsIds.length > 0
      params.limitGoodsCategoryTypeRule.includeChildGoods = this.existExcludeGoods
    }
    if (this.limitedGoodsType == 'goodsGroup') {
      params.limitGoodsGroupRule = this.limitGoodsGroupRule
      if (this.limitGoodsGroupRule.ruleInfos.length == 0) {
        this.$message({
          message: this.formatI18n('/公用/券模板/微盟适用商品', '请选择指定分组'),
          type: 'warning'
        })
        return
      }
      params.limitGoodsGroupRule.existExcludeGoods = this.limitGoodsGroupRule.excludeGoodsIds.length > 0
      params.limitGoodsGroupRule.includeChildGoods = this.existExcludeGoods
    }
    this.$emit('change', params)
    this.dialogShow = false
  }

  getOrg() {
    const org = sessionStorage.getItem("marketCenter");
    if (org) {
      this.org = org;
    }
  }

  async queryGroupList() {
    const params = new BQueryGoodsClassifyRequest()
    if (this.org) params.marketingCenter = this.org
    params.pageNum = 1
    params.pageSize = 200
    try {
      const { data } = await WeimobApi.queryGroupList(params)
      this.groupList = data || []
    } catch (error) {
      console.log(error)
      this.$message.error((error as Error).message);
    }
  }

  async queryCategoryList() {
    const params = new BQueryCategoryRequest()
    if (this.org) params.marketingCenter = this.org
    params.parentCategoryId = 0
    try {
      const { data } = await WeimobApi.queryCategoryList(params)
      this.categoryList = data || []
    } catch (error) {
      this.$message.error((error as Error).message);
    }
  }

  changeGroup(limitGoodsGroupRule: BWeimobExtLimitGoodsGroupRule) {
    this.limitGoodsGroupRule = limitGoodsGroupRule
  }

  changeCategory(categoryInfo: WeimobExtLimitGoodsCategoryRuleInfo) {
    this.limitGoodsCategoryTypeRule.ruleInfos = [categoryInfo]
    this.limitGoodsCategoryTypeRule.excludeGoodsIds = []
  }
  changeGoods(includeGoodsIds: IdName[]) {
    if (this.limitedGoodsType == 'goods') {
      if (this.limitGoodsTypeRule.goodsRange == 'part') {
        this.limitGoodsTypeRule.includeGoodsIds = includeGoodsIds
      } else {
        this.limitGoodsTypeRule.excludeGoodsIds = includeGoodsIds
      }
    }
    if (this.limitedGoodsType == 'goodsCategory') {
      this.limitGoodsCategoryTypeRule.excludeGoodsIds = includeGoodsIds
    }
    if (this.limitedGoodsType == 'goodsGroup') {
      this.limitGoodsGroupRule.excludeGoodsIds = includeGoodsIds
    }
  }

  doClearRule(type: string) {
    if (type == 'part') {
      this.limitGoodsTypeRule.includeGoodsIds = []
    }
    if (type == 'all') {
      this.limitGoodsTypeRule.excludeGoodsIds = []
    }
    if (type == 'goodsCategory') {
      this.limitGoodsCategoryTypeRule.excludeGoodsIds = []
      this.limitGoodsCategoryTypeRule.ruleInfos = []
    }
    if (type == 'goodsGroup') {
      this.limitGoodsGroupRule.excludeGoodsIds = []
      this.limitGoodsGroupRule.ruleInfos = []
    }
    this.showExcludeGoods = false
    this.$forceUpdate()
  }
}
