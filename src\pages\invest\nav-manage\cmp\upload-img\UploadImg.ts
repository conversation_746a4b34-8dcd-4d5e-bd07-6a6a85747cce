/*
 * @Author: 黎钰龙
 * @Date: 2024-04-15 14:32:44
 * @LastEditTime: 2024-04-27 10:18:32
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\upload-img\UploadImg.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import { NULL } from 'sass';
import EnvUtil from 'util/EnvUtil';
import { Component, Model, Prop, Vue, Watch } from 'vue-property-decorator';
@Component({
  name: 'UploadImg',
  components: {}
})
@I18nPage({
  prefix: [
  ],
  auto: true
})
export default class UploadImg extends Vue {
  @Prop({ type: Number, default: 300 }) maximum: number;  //限制图片大小
  @Prop({ type: Boolean, default: false }) isCircular: boolean; //样式是否为圆形
  @Prop({ type: String, default: null }) imgUrl: null; // 图片路径
  @Prop({ type: Number, default: null }) index: null; // 当前菜单下标
  curImgUrl: Nullable<string> = ''

  @Watch("imgUrl")
  onImgUrlChange(value: any) {
    this.curImgUrl = value
  }

  mounted() {
   this.initImg()
  }


  initImg(){
    this.curImgUrl = this.imgUrl
  }
  get uploadHeaders() {
    let locale = sessionStorage.getItem('locale')
    const headers: any = {
      locale: locale ? locale : 'zh_CN',
      time_zone: new Date().getTimezoneOffset(),
      marketingCenter: sessionStorage.getItem('marketCenter')
    }
    const authorization = EnvUtil.getUniAuthorization()
    if (authorization) {
      headers.authorization = authorization
    }
    return headers
  }

  get uploadUrl() {
    return EnvUtil.getServiceUrl() + "v1/upload/upload";
  }

  deleteImg() {
    this.curImgUrl = ''
    this.$emit('afterDel', this.index)
  }

  onImageUploadSuccess(response: any, file: any, fileList: any) {
    if (response && response.code === 2000) {
      this.$message.success(this.i18n("上传成功"));
      this.curImgUrl = response.data.url;
      this.$emit('afterSuccess', this.curImgUrl, this.index)
    } else {
      this.$message.error(response.msg);
    }
  }

  // 图片上传前的校验
  beforeAvatarUpload(file: any) {
    const isJPG = ["image/png", "image/jpg", "image/jpeg", "image/gif"].indexOf(file.type) > -1;
    const isLt2M = file.size / 1024 < this.maximum;
    if (!isJPG) {
      this.$message.error(this.i18n("上传图片只能是JPG/JPEG/PNG/GIF格式!"));
      return false;
    }
    if (!isLt2M) {
      this.$message.error(this.i18n("上传图片大小不能超过{0}", [this.maximum + "KB"]));
      return false;
    }
    return true;
  }
};