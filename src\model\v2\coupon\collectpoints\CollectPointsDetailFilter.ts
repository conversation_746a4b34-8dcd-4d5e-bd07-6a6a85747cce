/*
 * @Author: 黎钰龙
 * @Date: 2025-04-24 10:22:43
 * @LastEditTime: 2025-04-24 10:22:49
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\v2\coupon\collectpoints\CollectPointsDetailFilter.ts
 * 记得注释
 */
export default class CollectPointsDetailFilter {
  // 会员标识类似于
  memberCodeLikes: Nullable<string> = null
  // uuid>
  uuidGreater: Nullable<string> = null
  // 活动号=
  activityNumberEquals: Nullable<string> = null
  // 活动号in
  activityNumberIn: string[] = []
  // 会员id=
  memberIdEquals: Nullable<string> = null
  // 页数>=0
  page: number = 0
  // 页面大小>0
  pageSize: number = 0
}