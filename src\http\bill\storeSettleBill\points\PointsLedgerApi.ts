import ApiClient from 'http/ApiClient'
import Response from 'model/default/Response'
import PointsLedgerFilter from "model/bill/storeSettleBill/points/PointsLedgerFilter";
import PointsLedger from "model/bill/storeSettleBill/points/PointsLedger";

export default class PointsLedgerApi {
  /**
   * 导出积分账单明细
   * 导出积分账单明细
   * 
   */
  static export(body: PointsLedgerFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/ledger/points/prepayCard/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询积分账单
   * 分页查询积分账单。
   * 
   */
  static query(body: PointsLedgerFilter): Promise<Response<PointsLedger[]>> {
    return ApiClient.server().post(`/v1/ledger/points/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
