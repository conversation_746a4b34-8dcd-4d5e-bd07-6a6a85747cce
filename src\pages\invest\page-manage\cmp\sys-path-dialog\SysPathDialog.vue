<template>
  <div>
    <el-dialog :title="i18n('获取页面地址')" width="1000px" :close-on-click-modal="false" :visible.sync="visible" class="page-path-dialog">
      <div class="container-block">
        <div class="left-tabs">
          <div class="left-tabs-container">
            <div class="tab-item" :class="{ 'tab_active': tabSelect === item.value }" v-for="(item, index) in selectTabs" :key="index">
              <div class="tab-item-title" @click="onTabClick(item)">
                <span class="ellipsis" :title="item.name">{{ item.name }}</span>
                <i v-if="item.children && !item.isOpened" class="el-icon-arrow-down"></i>
                <i v-if="item.children && item.isOpened" class="el-icon-arrow-up"></i>
              </div>

              <div v-if="item.children" class="tab-item-children" :class="{ hide: !item.isOpened }">
                <div @click="onTabChildClick(child)" class="tab-item-children-item" :class="{ 'tab_child_active': tabChildSelect === child.value }"
                  v-for="child in item.children" :key="child.name">
                  <span class="ellipsis" :title="child.name">{{ child.name }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="left-tabs-right">
          <ul>
            <template v-if="tabSelect === 'activePage'">
              <li :class="{ active: centerType === i18n('功能页面') }">{{ i18n('功能页面') }}</li>
            </template>
            <template v-else>
              <li v-if="tabChildSelect !== 'inviteCourtesy'" @click="changeCenterType(i18n('活动列表'))" :class="{ active: centerType === i18n('活动列表') }">
                {{ i18n('活动列表') }}</li>
              <li @click="changeCenterType(i18n('活动详情'))" :class="{ active: centerType === i18n('活动详情') }">{{ i18n('活动详情') }}</li>
            </template>
          </ul>
        </div>
        <div class="right-form">
          <template v-if="showActiveList">
            <el-input v-model="sysPathLike" @input="queryTableData(true)" :placeholder="i18n('请输入页面名称搜索')" style="width: 300px;padding-bottom: 12px;">
              <!-- <i slot="suffix" class="el-icon-search"></i> -->
            </el-input>
            <el-table :data="filterData" v-loading="loading" style="width:620px; " height="650">
              <el-table-column :label="i18n('页面名称')">
                <template slot-scope="scope">
                  {{ scope.row.name }}
                </template>
              </el-table-column>
              <el-table-column :label="i18n('推广链接')">
                <template slot-scope="scope">
                  <span class="span-btn" v-if="showWeixinBtn" @click="doSpread(scope.row, cmsChannelEnum.WEIXIN)">
                    {{ i18n('微信小程序') }}
                  </span>
                  <span class="span-btn" v-if="showAliPayBtn" @click="doSpread(scope.row, cmsChannelEnum.ALIPAY)">
                    {{ i18n('支付宝小程序') }}
                  </span>
                  <!-- 仅大转盘中奖记录有 -->
                  <span v-if="isBigTurntableDetail && scope.row.name === '大转盘中奖记录'" class="span-btn" @click="doSpread(scope.row, cmsChannelEnum.H5)">
                    H5
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </template>
          <template v-else>
            <div>
              <el-select style="width: 180px;margin-right: 12px;" v-model="filter.state" @change="queryActiveDetailList()">
                <el-option value="UNSTART" :label="i18n('未开始')"></el-option>
                <el-option value="PROCESSING" :label="i18n('进行中')"></el-option>
              </el-select>
              <el-input v-model="filter.keyWords" @input="debounceQueryMethod()" :placeholder="i18n('请输入活动名称')"
                style="width: 200px;padding-bottom: 12px;margin-right: 12px;" />
              <el-input v-model="filter.activeNo" @input="debounceQueryMethod()" :placeholder="i18n('请输入活动号')"
                style="width: 200px;padding-bottom: 12px;" />
            </div>
            <el-table :data="detailList" v-loading="detailListLoading" style="width:620px; " height="650">
              <el-table-column :label="formatI18n('/营销/券礼包活动/券礼包活动', '活动号/活动名称')" fixed="left" prop="activityId" width="170">
                <template v-slot="scope">
                  <div>{{ scope.row.activityId | strFormat }}</div>
                  <div> {{ scope.row.name }}</div>
                </template>
              </el-table-column>
              <el-table-column :label="formatI18n('/营销/券礼包活动/券礼包活动', '活动时间')" width="180">
                <template v-slot="scope">
                  <div v-if="scope.row.beginDate && scope.row.endDate">
                    {{ scope.row.beginDate | dateFormate2 }}
                    {{ formatI18n("/营销/券礼包活动/券礼包活动", "至") }}
                    {{ scope.row.endDate | dateFormate2 }}
                  </div>
                  <div v-else>--</div>
                </template>
              </el-table-column>
              <el-table-column :label="i18n('活动状态')" align="center" prop="state" width="150">
                <template slot-scope="scope">
                  <ActivityStateTag :stateEquals="scope.row.state"></ActivityStateTag>
                </template>
              </el-table-column>
              <el-table-column :label="i18n('推广链接')">
                <template slot-scope="scope">
                  <span class="span-btn" v-if="showWeixinBtn" @click="doSpread(scope.row, cmsChannelEnum.WEIXIN)">
                    {{ i18n('微信小程序') }}
                  </span>
                  <span class="span-btn" v-if="showAliPayBtn" @click="doSpread(scope.row, cmsChannelEnum.ALIPAY)">
                    {{ i18n('支付宝小程序') }}
                  </span>
                  <span v-if="showH5Btn" class="span-btn" @click="doSpread(scope.row, cmsChannelEnum.H5)">
                    H5
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </template>

          <el-pagination style="padding-top: 12px;" :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]"
            :total="page.total" @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background
            layout="total, prev, pager, next, sizes,  jumper" class="pagin">
          </el-pagination>
        </div>
      </div>
    </el-dialog>
    <PageSpreadDialog ref="pageSpreadDialog"></PageSpreadDialog>
  </div>
</template>

<script lang="ts" src="./SysPathDialog.ts">
</script>

<style lang="scss" scoped>
.page-path-dialog {
  display: flex;
  align-items: center;
  justify-content: center;

  .container-block {
    display: flex;
    justify-content: center;
    align-items: center;
    // width: 950px;
    height: 620px;
    border: 1px solid #dde2eb;

    .left-tabs {
      width: 152px;
      height: 100%;
      // border: 1px solid #dde2eb;
      padding: 8px;
      box-sizing: border-box;
      flex-shrink: 0;

      .left-tabs-container {
        background-color: #f9fafc;
        height: 100%;
      }

      .tab-item {
        position: relative;
        width: 100%;
        min-height: 40px;

        &-title {
          width: 100%;
          height: 40px;
          line-height: 40px;
          border-radius: 4px;
          color: #36445a;
          // text-align: center;
          padding-left: 20px;
          font-weight: 400;
          white-space: nowrap;
          overflow: hidden;
        }

        &.tab_active {
          .tab-item-title {
            color: #fff;
            background-color: #007eff;
          }

          // &::after {
          //   content: "";
          //   position: absolute;
          //   right: 0;
          //   width: 2px;
          //   height: 40px;
          //   background: #007eff;
          // }
        }

        &-children {
          &.hide {
            display: none;
          }

          &-item {
            width: 100%;
            height: 40px;
            line-height: 40px;
            border-radius: 4px;
            color: #36445a;
            // text-align: center;
            font-weight: 400;
            padding-left: 26px;
            white-space: nowrap;
            overflow: hidden;

            &.tab_child_active {
              background-color: #eaf3ff;
              color: #318bff;
            }

            &:hover {
              cursor: pointer;
            }
          }
        }

        &:hover {
          cursor: pointer;
        }
      }
    }

    .left-tabs-right {
      width: 152px;
      flex-shrink: 0;
      padding: 8px;
      padding-left: 0;
      height: 100%;

      ul {
        list-style: none;

        li {
          height: 40px;
          line-height: 40px;
          text-align: center;
          border-radius: 4px;
          cursor: pointer;

          &.active {
            color: #318bff;
            background-color: #eaf3ff;
          }
        }
      }
    }

    .right-form {
      flex: 1; //650px;//calc(100%  - 304px);
      display: flex;
      flex-direction: column;
      height: 100%;
      border-left: 1px solid #dde2eb;
      margin-left: -1px;
      padding: 12px;
      box-sizing: border-box;

      .el-input__suffix {
        display: flex;
        align-items: center;
        cursor: pointer;
      }
    }
  }

  .ellipsis {
    display: inline-block;
    max-width: 100%;
    text-overflow: ellipsis;
    overflow: hidden;
    vertical-align: middle;
  }

  .span-btn {
    margin-right: 10px;
  }
}
</style>