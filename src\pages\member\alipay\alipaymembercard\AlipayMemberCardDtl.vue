<template>
  <div class="alipay-member-card">
    <!--<WechatHeader headerTitle="支付宝会员卡"></WechatHeader>-->
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button type="primary" v-if="hasOptionPermission('/设置/渠道/支付宝会员设置/支付宝会员初始化', '配置维护')"
          @click="toWechatMemberCard">{{formatI18n('/会员/会员资料/编辑')}}</el-button>
        <el-button v-if="hasOptionPermission('/设置/渠道/支付宝会员设置/支付宝会员初始化', '配置维护')" @click="doBack">{{i18n('/公用/按钮/返回')}}</el-button>
      </template>
    </BreadCrume>
    <div v-if="!wechatCard">
      <WechatStep></WechatStep>
    </div>

    <div class="flex-member">
      <div class="member-left" style="width: 300px">
        <img src="~assets/image/alipay/alipay.png" style="width: 100%; height: 600px">
      </div>
      <div class="member-right" style="width: 632px;padding: 0 44px">
        <div class="item-block" style="padding-top: 0px;">
          <div class="item-title" style="border-bottom: 1px dashed rgb(195, 195, 195);padding-bottom: 20px">
            {{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/基本信息')}}</div>
          <div class="member-item">
            <div class="item-left">{{i18n('卡模板id')}}</div>
            <div class="item-right">{{ wechatCard.alipassTemplateid }}</div>
          </div>
          <div class="member-item">
            <div class="item-left">{{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/会员卡标题')}}</div>
            <div class="item-right">{{ wechatCard.title }}</div>
          </div>
          <div class="member-item">
            <div class="item-left">{{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/商户logo')}}</div>
            <div class="item-right">
              <img @click="doImgClick(wechatCard.logo)" style="width: 100px;height: 100px" :src="wechatCard.logo">
            </div>
          </div>
          <div class="member-item">
            <div class="item-left">{{i18n('会员卡封面')}}</div>
            <div class="item-right">
              <img @click="doImgClick(wechatCard.background)" style="width: 100px;height: 100px" :src="wechatCard.background">
            </div>
          </div>
          <div class="member-item">

          </div>
        </div>
        <div class="item-block">
          <div class="item-title" style="border-bottom: 1px dashed #c3c3c3;
    padding-bottom: 20px;">{{i18n('会员识别方式')}}</div>
          <div class="member-item">
            <div class="item-left">{{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/会员卡识别方式')}}</div>
            <div class="item-right">
              <div>{{getWay(wechatCard.mbrCodeType)}}</div>
              <div v-if="wechatCard.mbrCodeType === 0"><img src="~assets/image/member/qrcode.jpg" width="100px" height="57px" /></div>
              <div v-if="wechatCard.mbrCodeType === 1"><img src="~assets/image/member/barcode.jpg" width="100px" height="57px" /></div>
            </div>
          </div>
        </div>
        <div class="item-block">
          <div class="item-title" style="border-bottom: 1px dashed #c3c3c3;
    padding-bottom: 20px;">{{i18n('栏位')}}</div>
          <div class="member-item">
            <div class="item-left">{{i18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/会员权益')}}</div>
            <div class="item-right">
              <span style="display: inline-block;margin-right: 10px">{{getEquityType(getMbrCardBenefit(wechatCard.equityType))}}</span>
            </div>
          </div>
          <div class="member-item">
            <div class="item-left">{{i18n('会员权益说明')}}</div>
            <div class="item-right">
              {{wechatCard.equityRemark}}
            </div>
          </div>
        </div>
        <div class="item-block">
          <div class="item-title" style="border-bottom: 1px dashed #c3c3c3;
    padding-bottom: 20px;">{{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/开卡填写信息')}}</div>
          <div class="member-item">
            <div class="item-left">{{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/必填信息')}}</div>
            <div class="item-right">
              <span>{{getMbrAttribute(getStartCardInfo(wechatCard.mbrAttribute, 'must'))}}</span>
            </div>
          </div>
          <div class="member-item">
            <div class="item-left">{{formatI18n('/会员/微信会员初始化/已授权/初始化第二步创建微信会员卡/选填信息')}}</div>
            <div class="item-right">
              <span>{{getMbrAttribute(getStartCardInfo(wechatCard.mbrAttribute, 'option'))}}</span>
            </div>
          </div>
          <!--<div class="member-item">-->
          <!--<div class="item-left">会员卡权益</div>-->
          <!--<div class="item-right">{{ wechatCard.memberRights }}</div>-->
          <!--</div>-->
        </div>
      </div>
    </div>

    <div v-if="!wechatCard" class="member-button">
      <div class="member-back" @click="toWechatAuthorizeAfter">{{formatI18n('/会员/等级/等级管理/未初始化状态/点击立即开始免费等级初始化/输入所有必填项点击下一步/底部按钮/上一步')}}</div>
      <div class="member-next" @click="toWechatMemberCode">{{formatI18n('/会员/等级/等级管理/未初始化状态/点击立即开始免费等级初始化/输入所有必填项点击下一步/底部按钮/下一步')}}</div>
    </div>
    <ImgScaleDialog ref="imgScale">
    </ImgScaleDialog>
  </div>
</template>

<script lang="ts" src="./AlipayMemberCardDtl.ts">
</script>

<style lang="scss">
.alipay-member-card {
  width: 100%;
  height: 100%;
  background-color: white;
  padding: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .step-bottom2 {
    border-bottom: 2px solid #3189fd;
  }
  .member-edit {
    width: 62px;
    float: right;
    margin: -50px 60px 0 0;
    font-size: 14px;
    text-align: center;
    color: #fff;
    background-color: #3189fd;
    border-radius: 5px;
    padding: 5px 10px;
    cursor: pointer;
  }
  .flex-member {
    overflow: auto;
    display: flex;
    flex: 1;
    justify-content: flex-start;
    padding: 30px 60px 50px;
    .member-left {
      width: 30%;
      margin-right: 30px;
    }
    .member-right {
      width: 65%;
      .item-block {
        padding: 30px 0;
        .item-title {
          font-size: 16px;
          font-weight: 700;
          margin: 5px 0 10px;
        }
        .member-item {
          display: flex;
          justify-content: flex-start;
          margin: 30px 0 30px 30px;
          font-size: 14px;
          font-weight: 500;
          color: #999999;
          .item-left {
            width: 100px;
            margin-right: 30px;
            color: #666;
          }
          .item-right {
            width: 550px;
            color: #666;
          }
        }
      }
    }
  }
  .member-button {
    display: flex;
    justify-content: center;
    margin: 0px auto 120px;
    font-size: 14px;
    line-height: 1.6;
    text-align: center;
    .member-back {
      width: 100px;
      color: #333333;
      background-color: #fff;
      border: 1px solid #e2e2e2;
      border-radius: 5px;
      padding: 5px 20px;
      cursor: pointer;
      margin-right: 20px;
    }
    .member-next {
      width: 100px;
      color: #fff;
      background-color: #3189fd;
      border: 1px solid #3189fd;
      border-radius: 5px;
      padding: 5px 20px;
      cursor: pointer;
    }
  }
  .wechat-header {
    margin: 0 !important;
  }
}
</style>
