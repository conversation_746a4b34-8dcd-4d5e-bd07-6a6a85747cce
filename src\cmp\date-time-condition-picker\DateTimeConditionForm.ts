import DateTimeCondition, {DayLine, MonthLine, WeekLine} from "model/common/DateTimeCondition";
import DateUtil from "util/DateUtil";

class WeekLineFormData {
  weeks: number[] = []
  dateRange: Array<Nullable<Date>> = [DateUtil.nowDayTime(), new Date(DateUtil.nowDayTime().getTime() + 24 * 60 * 60 * 1000 - 1)]
}

class MonthLineFormData {
  days: number[] = []
  dateRange: Array<Nullable<Date>> = [DateUtil.nowDayTime(), new Date(DateUtil.nowDayTime().getTime() + 24 * 60 * 60 * 1000 - 1)]
}

class DateTimeConditionPickerFormData {
  type: 'all' | 'day' | 'week' | 'month' = 'all'
  dayLines: Array<Array<Nullable<Date>>> = [[DateUtil.nowDayTime(), new Date(DateUtil.nowDayTime().getTime() + 24 * 60 * 60 * 1000 - 1)]]
  weekLines: WeekLineFormData[] = [new WeekLineFormData()]
  monthLines: MonthLineFormData[] = [new MonthLineFormData()]
}

export default class DateTimeConditionForm {
  data: DateTimeConditionPickerFormData = new DateTimeConditionPickerFormData()
  master: any
  rules: any

  init(master: any) {
    this.rules = {
      timePickerRules: [
        {
          validator: (rule: any, value: Date[], callback: any) => {
            if (!value || !value[0] || !value[1]) {
              callback(master.formatI18n('/公用/公共组件/时间条件控件/表单校验/请选择时间范围'))
            }
            callback()
          }, trigger: ['change', 'blur']
        }
      ],
      arrRules: [
        {
          validator: (rule: any, value: any[], callback: any) => {
            if (!value || value.length === 0) {
              callback(master.formatI18n('/公用/公共组件/时间条件控件/表单校验/请选择必选项'))
            }
            callback()
          }, trigger: ['change', 'blur']
        }
      ]
    }
  }

  toParams() {
    let result = new DateTimeCondition()
    if (this.data.type === 'all') {
      result.anytime = true
    }
    if (this.data.type === 'day') {
      for (let line of this.data.dayLines) {
        let dayLine: DayLine = new DayLine()
        dayLine.beginDate = line ? line[0] : null
        dayLine.endDate = line ? line[1] : null
        result.dayLines.push(dayLine)
      }
    }
    if (this.data.type === 'week') {
      for (let line of this.data.weekLines) {
        let weekLine: WeekLine = new WeekLine()
        weekLine.beginDate = line.dateRange ? line.dateRange[0] : null
        weekLine.endDate = line.dateRange ? line.dateRange[1] : null
        weekLine.weeks = line.weeks
        result.weekLines.push(weekLine)
      }
    }
    if (this.data.type === 'month') {
      for (let line of this.data.monthLines) {
        let monthLine: MonthLine = new MonthLine()
        monthLine.beginDate = line.dateRange ? line.dateRange[0] : null
        monthLine.endDate = line.dateRange ? line.dateRange[1] : null
        monthLine.days = line.days
        result.monthLines.push(monthLine)
      }
    }
    return result
  }

  of(value: DateTimeCondition) {
    if (value == null) {
      return
    }
    this.data.dayLines = []
    this.data.weekLines = []
    this.data.monthLines = []
    if (value.anytime) {
      this.data.type = 'all'
      return
    }
    if (value.dayLines && value.dayLines.length > 0) {
      this.data.type = 'day'
      for (let line of value.dayLines) {
        this.data.dayLines.push([line.beginDate, line.endDate])
      }
      return
    }
    if (value.weekLines && value.weekLines.length > 0) {
      this.data.type = 'week'
      for (let line of value.weekLines) {
        let weekLineFormData = new WeekLineFormData()
        weekLineFormData.weeks = line.weeks
        weekLineFormData.dateRange = [line.beginDate, line.endDate]
        this.data.weekLines.push(weekLineFormData)
      }
      return
    }
    if (value.monthLines && value.monthLines.length > 0) {
      this.data.type = 'month'
      for (let line of value.monthLines) {
        let monthLineFormData = new MonthLineFormData()
        monthLineFormData.days = line.days
        monthLineFormData.dateRange = [line.beginDate, line.endDate]
        this.data.monthLines.push(monthLineFormData)
      }
    }
  }
}

export {
  WeekLineFormData,
  MonthLineFormData,
}