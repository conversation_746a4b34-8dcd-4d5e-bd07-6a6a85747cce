<template>
  <div class="member-info-detail">
    <member-info-tag-group :dtl="dtl"></member-info-tag-group>
    <member-info-channel :dtl="dtl"></member-info-channel>
    <member-info-asset :dtl="dtl"
                       @send-coupon="onSendCoupon"></member-info-asset>
    <member-info-base :dtl="dtl"
                      :custom-member-attr="customMemberAttr"
                      :show-mobile-and-email-check-info="showMobileAndEmailCheckInfo"
                      @edit="onEdit"></member-info-base>
    <member-info-pet :dtl="dtl"
                     v-if="showMemberCustomGroupInfo"></member-info-pet>
  </div>
</template>
<script lang="ts"
        src="./MemberInfoDetail.ts">
</script>
<style lang="scss"
       scoped>
.member-info-detail {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;

  & > * {
    width: 100%;
  }

  & > :first-child, & > :nth-child(2) {
    width: calc(50% - 8px);
  }
}
</style>
