<template>
  <el-dialog
    class="custom-dialog"
    :title="title"
    :visible.sync="isDialogShow"
    :width="width"
    :show-close="showClose"
    :close-on-click-modal="doCloseByClickModel"
    :before-close="doBeforeClose"
  >
    <slot></slot>
    <div slot="footer" class="dialog-footer" v-if="isCancelShow || isConfirmShow || $slots.other">
      <span
        :class="{ 'btn-padding': otherFloat === 'right' }"
        :style="{
          float: otherFloat === 'left' || otherFloat === 'right' ? otherFloat : 'left',
        }"
      >
        <slot name="other"></slot>
      </span>
      <el-button v-if="isCancelShow" @click="doCancel">{{ cancelText }}</el-button>
      <el-button
        v-if="isConfirmShow"
        :type="confirmType"
        @click="doConfirm"
        :disabled="isConfirmDisabled"
        >{{ confirmText }}</el-button
      >
    </div>
  </el-dialog>
</template>

<style lang="scss">
@import '../../element-variables.scss';

.custom-dialog {
  .el-dialog {
    .el-dialog__header {
      font-weight: bold;
      border-bottom: 1px solid $--border-color-base;
    }
    .el-dialog__body {
      padding-top: 20px;
      padding-bottom: 20px;
      overflow: auto;
      max-height: 700px;
    }
    .el-dialog__footer {
      border-top: 1px solid $--border-color-base;
      position: static;
      width: 100%;
      bottom: 0;
    }
    .btn-padding {
      padding-left: 10px;
    }
    .dialog-footer {
      height: 28px;
    }
  }
}
</style>
<script lang="ts" src="./CmsDialog.ts"></script>
