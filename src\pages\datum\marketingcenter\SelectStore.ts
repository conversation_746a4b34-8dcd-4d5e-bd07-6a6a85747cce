import {Component, Prop} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import OrgApi from 'http/org/OrgApi';
import RSOrgFilter from 'model/common/RSOrgFilter';
import AbstractSelectDialog from 'cmp/selectordialogs/AbstractSelectDialog';
import RSOrg from 'model/common/RSOrg';
import IdName from "model/common/IdName";

@Component({
    name: 'SelectStore',
    components: {
        FormItem
    }
})
export default class SelectStore extends AbstractSelectDialog<RSOrg> {
    orgFilter: RSOrgFilter = new RSOrgFilter()
    @Prop()
    marketingCentersForChooseOrg: string[]
    marketingCenter: IdName = new IdName()
    @Prop({
        default: null
    })
    filterMarketingCenter: IdName[]
    showNoMarketingCenter: Nullable<boolean> = null  // 只展现没有所属营销中心的门店
    @Prop()
    targetMarketingCenter: IdName;
    @Prop()
    marketingCenters: IdName[]

    reset() {
        this.orgFilter = new RSOrgFilter()
        this.page.currentPage = 1
    }

    getId(ins: RSOrg): string {
        // @ts-ignore
        return ins.org.id;
    }

    getName(ins: RSOrg): string {
        // @ts-ignore
        return ins.org.name;
    }

    getResponseData(response: any): any {
        return response.data
    }

    queryFun(): Promise<any> {
        this.orgFilter.page = this.page.currentPage - 1
        this.orgFilter.pageSize = this.page.size
        this.orgFilter.sorters.orgId = 'asc'
        // this.orgFilter.marketingCenterIdNotIn = this.marketingCentersForChooseOrg
        if (this.showNoMarketingCenter) {
            this.orgFilter.queryNoMarketingCenter = true
        } else {
            this.orgFilter.queryNoMarketingCenter = false
        }
        if (this.filterMarketingCenter) {
            // @ts-ignore
            this.orgFilter.marketingCenterIdNotEquals = this.targetMarketingCenter.id
        }
        return OrgApi.queryNotInCurrentCenter(this.orgFilter)
    }

    doModalClose() {
        if (!this.targetMarketingCenter.id) {
            this.$message.error(this.formatI18n('/资料/营销中心/请至少选中一个营销中心'))
            return
        }
        if (this.selected.length === 0) {
            this.$message.error(this.formatI18n('/资料/渠道/请选择至少一个门店'))
            return
        }
        // tslint:disable-next-line:forin
        for (let index in this.selected) {
            this.selected[index].marketingCenter = this.targetMarketingCenter.id
        }
        this.dialogShow = false
        this.$emit('modifyMarketingCenter', this.selected)
        // this.$emit('getOrgList')
    }

    private changeDisable() {
        this.doSearch()
    }

}
