<?xml version="1.0" encoding="UTF-8"?>
<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 60 (88103) - https://sketch.com -->
    <title>ic_wenjianliebiao_hover</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <filter x="-69.1%" y="-56.1%" width="228.4%" height="215.2%" filterUnits="objectBoundingBox" id="filter-1">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0.494117647   0 0 0 0 1  0 0 0 0.398683348 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#00ACFF" offset="0%"></stop>
            <stop stop-color="#016CFF" offset="100%"></stop>
        </linearGradient>
        <path d="M4.08086081,6.54545455 C4.08086081,6.0935852 4.44717329,5.72727273 4.89904263,5.72727273 C5.35091197,5.72727273 5.71722445,6.0935852 5.71722445,6.54545455 L5.71722445,9.81818182 L6.96466312,9.81818182 C7.09727137,9.81818182 7.22444832,9.87086024 7.31821651,9.96462843 C7.51347866,10.1598906 7.51347866,10.4764731 7.31821651,10.6717352 L7.31821651,10.6717352 L5.25259602,12.7373557 C5.05733387,12.9326178 4.74075138,12.9326178 4.54548924,12.7373557 L4.54548924,12.7373557 L2.47986875,10.6717352 C2.38610056,10.577967 2.33342214,10.4507901 2.33342214,10.3181818 C2.33342214,10.0420394 2.55727976,9.81818182 2.83342214,9.81818182 L2.83342214,9.81818182 L4.08086081,9.81818182 L4.08086081,6.54545455 Z" id="path-3"></path>
        <filter x="-127.5%" y="-69.5%" width="355.0%" height="294.1%" filterUnits="objectBoundingBox" id="filter-4">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.2 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <path d="M9.83821476,9.17087642 C9.83821476,8.71900708 10.2045272,8.35269461 10.6563966,8.35269461 C11.1082659,8.35269461 11.4745784,8.71900708 11.4745784,9.17087642 L11.4745784,12.4436037 L12.7220171,12.4436037 C12.8546253,12.4436037 12.9818023,12.4962821 13.0755705,12.5900503 C13.2708326,12.7853125 13.2708326,13.1018949 13.0755705,13.2971571 L13.0755705,13.2971571 L11.00995,15.3627776 C10.8146878,15.5580397 10.4981053,15.5580397 10.3028432,15.3627776 L10.3028432,15.3627776 L8.2372227,13.2971571 C8.14345451,13.2033889 8.09077609,13.0762119 8.09077609,12.9436037 C8.09077609,12.6674613 8.31463371,12.4436037 8.59077609,12.4436037 L8.59077609,12.4436037 L9.83821476,12.4436037 L9.83821476,9.17087642 Z" id="path-5"></path>
        <filter x="-127.5%" y="-125.1%" width="355.0%" height="294.1%" filterUnits="objectBoundingBox" id="filter-6">
            <feOffset dx="0" dy="-2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.197197334 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="🔪icon" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="切图" transform="translate(-413.000000, -292.000000)">
            <g id="ic_wenjianliebiao_hover" transform="translate(413.000000, 292.000000)">
                <g id="ic_wenjianliebiao">
                    <g>
                        <rect id="矩形" fill="#FFFFFF" x="0" y="0" width="40" height="40" rx="20"></rect>
                        <g id="编组-2" filter="url(#filter-1)" transform="translate(10.181818, 9.363636)">
                            <path d="M1.68807485,2.18800732 L13.2335294,0.365040813 C14.3245822,0.192769311 15.348709,0.937588752 15.5209805,2.0286416 C15.5372715,2.13181801 15.5454545,2.23611212 15.5454545,2.34056674 L15.5454545,18.9321605 C15.5454545,20.03673 14.650024,20.9321605 13.5454545,20.9321605 C13.4409999,20.9321605 13.3367058,20.9239775 13.2335294,20.9076865 L1.68807485,19.08472 C0.715970755,18.9312298 -1.84971785e-15,18.0933412 0,17.109194 L0,4.16353325 C-3.4256787e-16,3.17938611 0.715970755,2.34149744 1.68807485,2.18800732 Z" id="矩形" fill="url(#linearGradient-2)"></path>
                            <g id="形状结合">
                                <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                                <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-3"></use>
                            </g>
                            <g id="形状结合" transform="translate(10.656397, 11.949787) scale(1, -1) translate(-10.656397, -11.949787) ">
                                <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                                <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-5"></use>
                            </g>
                            <path d="M16.3636364,1.63636364 L17.6363636,1.63636364 C18.7409331,1.63636364 19.6363636,2.53179414 19.6363636,3.63636364 L19.6363636,17.6363636 C19.6363636,18.7409331 18.7409331,19.6363636 17.6363636,19.6363636 L16.3636364,19.6363636 L16.3636364,19.6363636 L16.3636364,1.63636364 Z" id="矩形" fill="#007EFF" opacity="0.299920945"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>