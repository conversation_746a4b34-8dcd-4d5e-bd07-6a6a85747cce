import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import CouponItem from 'model/common/CouponItem'
import AmountToFixUtil from 'util/AmountToFixUtil'
import TimeRange from 'cmp/coupontenplate/cmp/TimeRange.vue'
import GoodsScopeEx from 'cmp/goodsscope/GoodsScopeEx.vue'
import UseCouponStep from 'cmp/coupontenplate/cmp/UseCouponStep.vue'
import CouponBear from 'cmp/coupontenplate/cmp/CouponBear.vue'
import CouponInfo from 'model/common/CouponInfo'
import CashCouponAttribute from 'model/common/CashCouponAttribute'
import ValidityInfo from 'model/common/ValidityInfo'
import DateTimeRange from 'model/common/DateTimeRange'
import SubjectApportion from 'model/common/SubjectApportion'
import StoreRange from 'model/common/StoreRange'
import DateUtil from 'util/DateUtil'
import ChannelRange from 'model/common/ChannelRange'
import CouponInitialApi from 'http/v2/coupon/init/CouponInitialApi'
import RSCostPartyFilter from 'model/common/RSCostPartyFilter'
import CostPartyApi from 'http/costparty/CostPartyApi'
import CouponTemplateSelectorDialog from "cmp/selectordialogs/CouponTemplateSelectorDialog";
import GroupMutexTemplate from "cmp/coupontenplate/cmp/GroupMutexTemplate";
import GroupMutexTemplateData from "cmp/coupontenplate/cmp/GroupMutexTemplateData";
import CouponTemplateLogo from "cmp/coupontenplate/cmp/CouponTemplateLogo";
import ActiveStore from "cmp/activestore/ActiveStore";
import ChooseGoodsRange from '../cmp/chooseGoodsRange'
import CouponThreshold from 'model/common/CouponThreshold'
import ExchangeGoodsCouponAttribute from 'model/common/ExchangeGoodsCouponAttribute'
import { CouponBasicType } from 'model/common/CouponBasicType'
import PickUpCouponAttribute from 'model/common/PickUpCouponAttribute'
import { ExpiryType } from 'model/common/ExpiryType'
import WeimobCouponInfo from '../cmp/WeimobCouponInfo'
import { ApportionType } from 'model/common/ApportionType'
import Channel from "model/common/Channel";
import RSChannelManagement from "model/common/RSChannelManagement";
import SelectGoodsDialog from "cmp/coupontenplate/cmp/selectGoodsDialog.vue";
import RSGoods from 'model/common/RSGoods'
import SpecialGoodsDialog from 'cmp/coupontenplate/cmp/specialGoodsDialog.vue'
import I18nPage from "common/I18nDecorator";
import CouponTemplateLabel from "cmp/coupontenplate/cmp/CouponTemplateLabel.vue"
import StackPromotion from "./../cmp/StackPromotion.vue"
import CouponName from '../FormItemCmp/CouponName/CouponName'
import UseCouponDesc from '../FormItemCmp/UseCouponDesc/UseCouponDesc'
import RecordWay from '../FormItemCmp/RecordWay/RecordWay'
import CouponEffectPeriod from '../FormItemCmp/CouponEffectPeriod/CouponEffectPeriod'
import CouponCodeRules from '../FormItemCmp/CouponCodeRules/CouponCodeRules'
import TabsEdit from 'cmp/tabs-edit/TabsEdit'
import OuterCouponTemCode from '../FormItemCmp/OuterCouponTemCode/OuterCouponTemCode'
import AutoFixInput from 'cmp/autofixinput/AutoFixInput'
import PickUpGoods from 'model/common/PickUpGoods'
import BrowserMgr, {LocalStorage} from "mgr/BrowserMgr";
import GetGiftGoodsRange from "cmp/coupontenplate/cmp/GetGiftGoodsRange";
import SelectCostParty from 'cmp/selectCostParty/SelectCostParty';
import CouponConfig from 'model/v2/coupon/init/CouponConfig'

class CostPartyDetail {
  // 承担方
  party: Nullable<string> = null
  // 承担百分比/金额
  value: Nullable<number> = null
}

@Component({
  name: 'ExchangeGoodsCoupon',
  components: {
    CouponName,
    UseCouponDesc,
    RecordWay,
    CouponEffectPeriod,
    CouponCodeRules,
    TimeRange,
    ActiveStore,
    GoodsScopeEx,
    UseCouponStep,
    CouponBear,
    CouponTemplateSelectorDialog,
    GroupMutexTemplate,
    CouponTemplateLogo,
    ChooseGoodsRange,
    WeimobCouponInfo,
    SelectGoodsDialog,
    SpecialGoodsDialog,
    CouponTemplateLabel,
    StackPromotion,
    TabsEdit,
    OuterCouponTemCode,
    AutoFixInput,
    GetGiftGoodsRange,
    SelectCostParty
  }
})
@I18nPage({
  prefix: ["/公用/券模板"],
  auto: true
})
export default class ExchangeGoodsCoupon extends Vue {
  dtl: CouponConfig = new CouponConfig()
  queryCostParyRange:string = 'customize'
  goodsMatchRuleMode: string = "barcode"
  timeParam: any = ''
  ruleForm: any = {
    amount: '',
    name: '',
    dateType: 'RALATIVE',
    dateFrom: '',
    dateTo: '',
    dateFix: '',
    useDate: '',
    storeRange: '{}',
    useCouponGood: [{
      data: [],
      exchangeGoodsCouponAttribute: {
        exchangeQty: null,  //X选N的数量
        enableExchangeSameGood: false, //是否可重复选
      }
    }],
    useCouponGifGoods: [{
      data: [],
      exchangeGoodsCouponAttribute: {
        exchangeQty: null,  //X选N的数量
        enableExchangeSameGood: false, //是否可重复选
      }
    }],
    promotionInfo: {
      excludePromotion: true,
      promotionSuperpositionType: null,
      promotion: {}
    },
    recordWay: 'FAV',
    discountWay: '',
    payWay: '',
    couponOrder: '',
    couponGoodsDesc: '',
    couponProduct: '',
    type: '',
    time: '',
    couponUnder: {},
    templateId: '',
    useFrom: 'step2',
    from: [],
    sychChannel: null,
    groupMutex: new GroupMutexTemplateData(),
    logoUrl: '',
    prefix: '',
    transferable: true,  // 是否可转赠
    pickQty: null,
    useThreshold: new CouponThreshold(),
    exchangeGoodsCouponAttribute: new ExchangeGoodsCouponAttribute(),
    expiryType: "NATURAL_DAY",
    weimobCouponAndTotal: {
      total: '',
      weimobCoupon: null
    },
    weimobId: [],
    baiLianId: [],
    recordType: 'PROPORTION', //PROPORTION 按比例 AMOUNT 按金额
    templateTag: [],
    outerRelations: {
      outerNumber: null,
      channel: new Channel()
    },
    amountPayments: [],   // 券支付金额方式
    parties: [],   // 承担方
    price: null,
    termsModel: null,
    couponSubscriptType: "COMMON",
    notes:"",
    maxDailyMemberQuotaQty: null,
    writeOffLink: "",
  }
  disabledEdit: Boolean = false
  goodsType: 'ALL' | 'PART' = "ALL"
  labelWidth: string = '120px'
  telescoping: boolean = true  //为true收起高级设置，为false展开
  $refs: any
  rules: any = {}
  curState = ''
  canChooseItems: any[] = []
  currentGoodsTab: number = 0 //用券商品，当前tab
  enablePayApportion: boolean = false
  isShowAppreciationGoods: boolean = true // 是否展示增值商品
  chooseGoodType:string = 'normal'
  // 特殊商品
  specialGoods: any[] = []
  @Prop()
  sameStore: boolean // 与活动门店一致
  @Prop()
  state: string
  @Prop()
  channels: RSChannelManagement[];
  @Prop()
  value: CouponItem
  @Prop({
    type: Boolean,
    default: false
  })
  baseSettingFlag: boolean
  @Prop({
    type: String,
    default: 'add'
  })
  copyFlag: string
  @Prop( {
    type: String,
    default: '400'
  })
  remarkMaxlength: string
  @Prop({
    type: Boolean,
    default: false
  })
  baseFieldEditable: boolean // 叠加促销、用券记录方式、券承担方、用券顺序 是否可编辑

  @Prop({
    type: Boolean,
    default: false,
  })
  isPmsPayEngine: boolean; //是否pms计算模式

  @Prop({
    type: Boolean,
    default: false
  })
  enableStore: boolean
  @Prop({
    default: () => {
      return {
        maxAmount: 99999999,
        maxValidDay: 36500,
        maxUseThreshold: 99999999,
        fixedTime: false,
      }
    }
  })
  options: {  // 指定最大券面额，可选配置，用于微信扫码领券
    maxAmount: number,// 指定最大券面额
    maxValidDay: number, // 指定最大券有效天数
    maxUseThreshold: number,// 指定最大用券门槛
    fixedTime: boolean, // 固定用券时段为全部时段
  }
  @Prop({
    type: Boolean,
    default: false
  })
  wxScanForCoupon: boolean
  // @Prop({
  //     type: String,
  //     default: 'all_cash'
  // })
  // couponType: string
  parties: any = []
  // 是否是复制\新建\编辑
  @Watch('state')
  onStateChange(value: string) {
    this.curState = value
  }
  @Watch('value')
  onDataChange(value: CouponItem) {
    if (value && value.coupons && value.coupons.name) {
    }
  }



  get accountItemRange() {
    if (this.queryCostParyRange) {
      return this.queryCostParyRange;
    }
      return 'customize';
  }

  get isChooseGoods() {
    if (this.chooseGoodType == 'appreciation' && this.isShowAppreciationGoods) {
      return true;
    } else {
      return false;
    }
  }

  get isExchangeChooseGoods() {
    if (this.isShowAppreciationGoods) {
      if (this.chooseGoodType == 'appreciation') {
        return true;
      } else {
        return false;
      }
    } else {
      return null
    }
  }

  // 最终的同步渠道
  get sychChannel() {
    return this.channels.filter(item => [...this.ruleForm.weimobId, ...this.ruleForm.baiLianId].includes(this.channelId(item.channel!))).map(item => item.channel!)
  }

  get hasWeimobChannel() {
    return this.ruleForm.weimobId.length > 0
  }

  // 可选的同步渠道, 兑换券目前只支持 微盟
  get sychChannels() {
    return this.channels.filter((item) => ["weimob", 'baiLian'].includes(item.channel!.type || ''));
  }
  get weimobChannels() {
    return this.channels.filter((item) => ["weimob"].includes(item.channel!.type || ''));
  }
  get baiLianChannels() {
    return this.channels.filter((item) => ["baiLian"].includes(item.channel!.type || ''));
  }

  get dateRangeOption() {
    return {
      disabledDate(time: any) {
        return time.getTime() < DateUtil.nowDayTime()
      }
    }
  }

  // 用券商品 当前选择的是否品类
  isCategory(row: any) {
    if (row.data?.length) {
      return row.data[0].category
    } else {
      return false
    }
  }

  changeGoodsType() {
    this.canChooseItems = []
    this.ruleForm.useCouponGood = [{
      data: [],
      exchangeGoodsCouponAttribute: {
        exchangeQty: null,  //X选N的数量
        enableExchangeSameGood: false, //是否可重复选
      }
    }]
  }

  get setSGStr() {
    return this.i18n('已设置{0}个特殊商品').replace(/\{0\}/g, String(this.specialGoods.length))
  }

  get showOuterRelationsConfig() {
    const flag = sessionStorage.getItem('showOuterRelationsConfig')
    return flag === 'true'
  }

  created() {
    this.isShowAppreciationGoods = LocalStorage.getItem("appreciationGoods") === true;
    let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
		if (sysConfig && this.goodsMatchRuleMode) {
			this.goodsMatchRuleMode = sysConfig.goodsMatchRuleMode;
		}
    this.queryCostParyRange = LocalStorage.getItem("accountItemRange");
    this.disabledEdit = (this.$route.query.from === 'edit' && this.state !== 'NOT_EFFECTED')
    this.ruleForm.useThreshold.thresholdType = 'NONE'
    if (sessionStorage.getItem('locale') === 'en_US') {
      this.labelWidth = '180px'
    }
    this.rules = {
      name: [
        { required: true, message: this.formatI18n('/公用/券模板', '请输入必填项'), trigger: 'blur' },
      ],
      amount: [
        { required: true, message: this.formatI18n('/公用/券模板', '请输入必填项'), trigger: 'blur' },
      ],
      'exchangeGoodsCouponAttribute.afterUseAmount': [
        { required: true, message: this.formatI18n('/公用/券模板', '请输入必填项'), trigger: 'blur' },
      ],
      useCouponGood: [
        {
          required: true, validator: (rule: any, value: any, callback: any) => {
            if (!this.ruleForm.useCouponGood || this.ruleForm.useCouponGood?.length == 0) {
              callback(new Error(this.i18n('请选择用券商品')))
            }
            let flag = 0  //用券商品，哪些组校验通过了
            let errArr: number[] = [] //哪些tab校验没通过
            this.ruleForm.useCouponGood.forEach((val: any, ind: any) => {
              if (val.data && val.data.length > 0) {
                let count = 0
                val.data.forEach((item: any) => {
                  if (!item.qty) {
                    count++
                  }
                })
                if (count <= 0) {
                  flag++
                } else {
                  errArr.push(ind + 1)
                }
              } else {
                errArr.push(ind + 1)
              }
            })
            if (flag === this.ruleForm.useCouponGood.length) {
              callback()
            } else {
              const str = errArr.join('、')
              callback(new Error(this.i18n('第') as string + str + this.i18n('组未填写必填项')))
            }
          }, trigger: 'change'
        }
      ],
      useCouponGifGoods: [
        {
          required: true, validator: (rule: any, value: any, callback: any) => {
            if (this.ruleForm.useCouponGifGoods && this.ruleForm.useCouponGifGoods?.length > 0) {
              let arr = this.ruleForm.useCouponGifGoods[0].data
              if (arr.length > 0) {
                arr.forEach((item: any) => {
                  if (!item.qty || item <= 0) {
                    callback(new Error(this.formatI18n('/公用/券模板', '请输入赠品数量')))
                  }
                })
                callback()
              } else {
                callback()
              }
            } else {
              callback()
            }
          }, trigger: 'blur'
        }
      ],
      useFrom: [
        {
          validator: (rule: any, value: any, callback: any) => {
            if (value) {
              if (value === 'step1') {
                callback()
              } else {
                if (this.ruleForm.from && this.ruleForm.from.length > 0) {
                  callback()
                } else {
                  callback(this.formatI18n('/公用/券模板', '请输入必填项'))
                }
              }
            }
          }, trigger: 'blur'
        },
      ],
      couponOrder: [
        { required: true, message: this.formatI18n('/公用/券模板', '请输入必填项'), trigger: 'blur' },
      ],
      couponProduct: [
        { required: true, message: this.formatI18n('/公用/券模板', '请输入必填项'), trigger: 'blur' },
      ],
      discountWay: [
        { required: true, message: this.formatI18n('/公用/券模板', '请输入必填项'), trigger: 'blur' },
      ],
      payWay: [
        { required: true, message: this.formatI18n('/公用/券模板', '请输入必填项'), trigger: 'blur' },
      ],
      prefix: [
        {
          validator: (rule: any, value: any, callback: any) => {
            let re = /^[0-9a-zA-Z]*$/g;  // 判断字符串是否为数字和字母组合
            if (!re.test(value)) {
              callback(this.i18n('请输入数字或字母'));
            } else {
              callback();
            }
          }, tirgger: 'blur'

        },
      ],
      useThreshold: [
        {
          validator: (rule: any, value: any, callback: any) => {
            if (this.ruleForm.useThreshold.thresholdType === 'NONE') {
              callback()
            } else {
              if (this.ruleForm.useThreshold.threshold) {
                callback()
              } else {
                callback(this.formatI18n('/公用/券模板', '请输入必填项'))
              }
            }


          }, trigger: 'change'
        },
      ],
      recordWay: [
        { required: true, message: this.formatI18n('/公用/券模板', '请输入必填项'), trigger: 'blur' },
      ],
    }
    if (this.copyFlag) {
      this.getCostParty()
    }
    if (!this.$route.query.id) { // 新建时使用配置的用券记录方式
      this.getPayWayDtl()
    }
    this.getCouponPrefix("exchange_goods");
  }

  private getCouponPrefix(type: string){
    if (["edit", "copy"].includes(this.copyFlag)) {
			return;
		}
		CouponInitialApi.get().then((resp: any) => {
		  if (resp && resp.code === 2000) {
			this.dtl = resp.data;
			if(!this.dtl || !this.dtl.couponCodePrefixes) {
				return "";
			  }
        const coupon = this.dtl.couponCodePrefixes.find(
          item => item.couponType === type
        );
        this.ruleForm.prefix = coupon ? coupon.prefix : "";
      }
		})
	}

  mounted() {
    if (this.value && this.value.coupons) {
      console.log(111111)
      this.doBindValue(JSON.parse(JSON.stringify(this.value)))
    }
  }

  doFormItemChange() {
    this.$emit('input', this.doTransParams())
    this.$emit('change', this.channels)
  }


  //是否展示“可重复选”按钮
  isShowCanSelectSameGoods(data: PickUpGoods[]) {
    const flag = data.some((item: PickUpGoods) => item.category)
    return !flag
  }

  changeSpecialGoods(goods: any[]) {
    this.specialGoods = goods || []
    this.doFormItemChange()
  }

  doUseFromChange() {
    if (this.ruleForm.useFrom === 'step1') {
      this.ruleForm.from = []
      this.$emit('input', this.doTransParams())
      this.$emit('change', this.channels)
    }
    this.$refs.ruleForm.validateField('useFrom')
  }

  doFromChange() {
    this.$refs.ruleForm.validateField('useFrom')
    this.doFormItemChange()
  }

  logoUrlCallBack(url: any) {
    this.ruleForm.logoUrl = url
    this.doFormItemChange()
  }

  weimobChange(info: any) {
    this.ruleForm.weimobCouponAndTotal.total = info.total
    this.ruleForm.weimobCouponAndTotal.weimobCoupon = info.weimobCoupon
    this.doFormItemChange()
  }

  doStoreChange() {
    this.doFormItemChange()
    if (this.$refs['ruleForm'] && (this.$refs['ruleForm'] as any).validateField) {
      (this.$refs['ruleForm'] as any).validateField('storeRange')
    }
    // this.$forceUpdate()
  }

  menkanValueChange() {
    this.ruleForm.useThreshold.threshold = AmountToFixUtil.formatAmount(this.ruleForm.useThreshold.threshold, 999999.0, 0.01, 2)
    this.doFormItemChange()
  }

  get remarkPlaceholder() {
    let str = this.formatI18n('/营销/积分活动/积分活动/积分兑换券/编辑页面', '请输入不超过{0}个字符')
    return str.replace(/\{0\}/g, this.remarkMaxlength);
  }

  doValidate() {
    this.telescoping = false
    let arr: any = []
    let p0 = new Promise((resolve, reject) => {
      this.$refs.ruleForm.validate((valid: any) => {
        if (valid) {
          resolve(null)
        }
      })
    })
    arr.push(p0)
    // 券承担方
    if (this.$refs.CouponBear) {
      arr.push(this.$refs.CouponBear.formValiPromise())
    }
    arr.push(this.$refs.activeStore.validate())

    // 用券时段
    if (this.$refs.timeRange) {
      let p2 = this.$refs.timeRange.doValidate()
      arr.push(p2)
    }
    // 叠加促销
    if (this.$refs.stackPromotion) {
      arr.push(this.$refs.stackPromotion.validate());
    }
    // 用券商品
    // if (this.$refs.chooseGoodsRange) {
    //     let p3 = this.$refs.chooseGoodsRange.validate()
    //     arr.push(p3)
    // }
    // 券叠加组
    // if (this.$refs.groupMutexTemplate) {
    //     arr.push(this.$refs.groupMutexTemplate.doValidate())
    // }
    // 微盟校验
    if (this.$refs.weimobCouponInfo) {
      arr.push(this.$refs.weimobCouponInfo.doValidate());
    }
    // 用券记录方式校验
    if (this.$refs.recordWay) {
      arr.push(this.$refs.recordWay.validate());
    }
    //券有效期校验
    if (this.$refs.couponEffectPeriod) {
      arr.push(this.$refs.couponEffectPeriod.validate());
    }
    //外部券模板号
    if (this.$refs.outerCouponTemCode) {
      arr.push(this.$refs.outerCouponTemCode.doValidate())
    }
    return arr
  }

  private doTransParams() {
    let params: CouponItem = new CouponItem()
    params.coupons = new CouponInfo()
    // params.coupons.couponBasicType = this.couponType as any
    params.coupons.couponBasicType = CouponBasicType.exchange_goods
    params.coupons.appreciationGoods = this.isExchangeChooseGoods
    params.coupons.name = this.ruleForm.name
    params.coupons.templateId = this.ruleForm.templateId
    params.coupons.cashCouponAttribute = new CashCouponAttribute()
    params.coupons.cashCouponAttribute.faceAmount = this.ruleForm.amount
    // 券有效期
    params.coupons.validityInfo = new ValidityInfo();
    params.coupons.validityInfo.validityType = this.ruleForm.dateType;
    if (this.ruleForm.dateType === "RALATIVE") {
      params.coupons.validityInfo.delayEffectDays = this.ruleForm.dateFrom;
      params.coupons.validityInfo.expiryType = this.ruleForm.expiryType
      if ([ExpiryType.DAYS, ExpiryType.NATURAL_DAY].indexOf(this.ruleForm.expiryType) > -1) {
        params.coupons.validityInfo.validityDays = this.ruleForm.dateTo;
        params.coupons.validityInfo.months = null
      } else if (this.ruleForm.expiryType === ExpiryType.MONTHS) {
        params.coupons.validityInfo.months = this.ruleForm.dateTo;
        params.coupons.validityInfo.validityDays = null
      } else {
        params.coupons.validityInfo.months = null;
        params.coupons.validityInfo.validityDays = null;
      }
    } else {
      // 固定有效期
      if (this.ruleForm.dateFix && this.ruleForm.dateFix[0]) {
        params.coupons.validityInfo.beginDate = (this.ruleForm.dateFix[0]) as any;
      }
      if (this.ruleForm.dateFix && this.ruleForm.dateFix[1]) {
        params.coupons.validityInfo.endDate = (this.ruleForm.dateFix[1]) as any;
      }
    }
    // 用券时段
    params.coupons.useTimeRange = new DateTimeRange()
    if (this.ruleForm.time) {
      // params.coupons.useTimeRange = this.timeTemplate
      params.coupons.useTimeRange = this.ruleForm.time
    } else {
      params.coupons.useTimeRange = new DateTimeRange()
      params.coupons.useTimeRange.dateTimeRangeType = 'ALL' as any
    }
    // todo 用券渠道
    params.coupons.useChannels = new ChannelRange()
    if (this.ruleForm.useFrom === 'step1') {
      params.coupons.useChannels.channelRangeType = 'ALL' as any
      params.coupons.useChannels.channels = []
    } else {
      params.coupons.useChannels.channelRangeType = 'PART' as any
      params.coupons.useChannels.channels = this.ruleForm.from
    }

    //同步渠道
    params.coupons.sychChannel = this.ruleForm.sychChannel
    // 用券门店
    if (this.ruleForm.storeRange === '{}') {
      let storeRange: StoreRange = new StoreRange()
      if (this.sameStore) {
        storeRange.storeRangeType = 'SAME' as any
      } else {
        storeRange.storeRangeType = 'ALL' as any
      }
      params.coupons.useStores = storeRange
    } else {
      params.coupons.useStores = this.ruleForm.storeRange
    }
    // 用券商品
    params.coupons.useGoods = this.ruleForm.useCouponGood
    // 用券门槛
    params.coupons.useThreshold = this.ruleForm.useThreshold
    // 叠加促销
    params.coupons.excludePromotion = this.ruleForm.promotionInfo.excludePromotion;
    params.coupons.promotionSuperpositionType = this.ruleForm.promotionInfo.promotionSuperpositionType;
    params.coupons.promotion = this.ruleForm.promotionInfo.promotion;
    params.coupons.goodsFavRules = this.ruleForm.promotionInfo.goodsFavRules
    // 是否支持转赠
    params.coupons.transferable = this.ruleForm.transferable
    // 用券记录方式
    params.coupons.useApporion = new SubjectApportion()
    params.coupons.useApporion.subjectApprotionType = this.ruleForm.recordWay
    params.coupons.useApporion.recordType = this.ruleForm.recordType
    if (this.ruleForm.recordWay === "COLLOCATION") {
      if (this.ruleForm.recordType === 'PROPORTION') {
        params.coupons.useApporion.favValue = parseFloat(this.ruleForm.discountWay);
        params.coupons.useApporion.payValue = parseFloat(this.ruleForm.payWay);
      } else {
        params.coupons.useApporion.payValue = this.ruleForm.payWay;
        params.coupons.useApporion.parties = this.ruleForm.parties
        params.coupons.useApporion.amountPayments = this.ruleForm.amountPayments
        // 用券方式，按金额，特殊商品
        if (this.specialGoods.length) {
          params.coupons.useApporion.specialGoodsAmounts = this.specialGoods.map((item: any) => {
            return {
              barcode: item.barcode,
              amount: item.price != undefined ? item.price : item.amount
            }
          })
        }
      }

    } else {
      params.coupons.useApporion.favValue = 0;
      params.coupons.useApporion.payValue = 0;
      // params.coupons.useApporion.recordType = "PROPORTION";
      params.coupons.useApporion.specialGoodsAmounts = null;
      params.coupons.useApporion.recordType = null
    }
    // 券承担方
    // params.coupons.costParties = []
    // params.coupons.costParties = this.ruleForm.couponUnder
    ; (params.coupons.costParty as any) = {};
    if (this.ruleForm.couponUnder.bearType == 'unset') {
      this.ruleForm.couponUnder.bearType = null
      this.ruleForm.couponUnder.amountType = null
      this.ruleForm.couponUnder.costPartyDetails = null
    }
    params.coupons.costParty = this.ruleForm.couponUnder;
    // 用券顺序
    params.coupons.priority = this.ruleForm.couponOrder
    // 用券商品说明
    params.coupons.goodsRemark = this.ruleForm.couponGoodsDesc
    // 用券说明
    params.coupons.remark = this.ruleForm.couponProduct
    // 券叠加促销
    params.coupons.groupMutexFlag = this.ruleForm.groupMutex.groupMutexFlag
    params.coupons.groupMutexTemplates = this.ruleForm.groupMutex.groupMutexTemplates
    // 券logo
    params.coupons.logoUrl = this.ruleForm.logoUrl
    // 券码前缀
    params.coupons.codePrefix = this.ruleForm.prefix;
    // 兑换券参数
    params.coupons.exchangeGoodsCouponAttribute = this.doGoodsToParams() as any  //兑换商品参数
    // 提货数量
    params.coupons.pickUpCouponAttribute = new PickUpCouponAttribute()
    // 剩余库存
    if (this.ruleForm.weimobId?.length > 0) {
      params.coupons.total = this.ruleForm.weimobCouponAndTotal.total
    }
    params.coupons.templateTag = this.ruleForm.templateTag
    // 同步渠道参数
    this.hasWeimobChannel && (params.coupons.weimobCoupon = this.ruleForm.weimobCouponAndTotal.weimobCoupon)
    params.coupons.sychChannel = this.sychChannel
    //外部券模板号
    params.coupons.outerRelations = this.ruleForm.outerRelations && this.ruleForm.outerRelations.outerNumber ? [this.ruleForm.outerRelations] : null
    //价格
    params.coupons.salePrice = this.ruleForm.price
    //账款项目
    params.coupons.termsModel = this.ruleForm.termsModel
    //券角标
    params.coupons.couponSubscriptType = this.ruleForm.couponSubscriptType
    //核销链接
    params.coupons.writeOffLink = this.ruleForm.writeOffLink
    //备注
    params.coupons.notes = this.ruleForm.notes
    //每人每天限量
    params.coupons.maxDailyMemberQuotaQty = this.ruleForm.maxDailyMemberQuotaQty    
    return params
  }

  //回填表单
  private doBindValue(value: CouponItem) {
    if (value && value.coupons && value.coupons.couponBasicType === 'exchange_goods') {
      let coupon: CouponInfo = value.coupons
      if(this.isShowAppreciationGoods) {
        if (coupon.appreciationGoods === true) {
          this.chooseGoodType = 'appreciation'
        } else {
          this.chooseGoodType = 'normal'
        }
      }
      console.log('回填后的表单修改状态后的',this.chooseGoodType,this.ruleForm.useCouponGood)
      this.ruleForm.templateId = coupon.templateId
      this.ruleForm.name = coupon.name
      this.ruleForm.amount = coupon.cashCouponAttribute!.faceAmount
      this.ruleForm.dateType = coupon.validityInfo!.validityType

      if (this.ruleForm.dateType === "RALATIVE") {
        this.ruleForm.dateFrom = coupon.validityInfo!.delayEffectDays;
        this.ruleForm.expiryType = coupon.validityInfo!.expiryType
        this.ruleForm.dateTo = coupon.validityInfo!.validityDays || coupon.validityInfo!.months;
      } else {
        this.ruleForm.dateFix = [
          DateUtil.format(coupon.validityInfo!.beginDate, "yyyy-MM-dd HH:mm:ss"),
          DateUtil.format(coupon.validityInfo!.endDate, "yyyy-MM-dd HH:mm:ss"),
        ];
      }

      const weimobChannel = coupon.sychChannel.filter(item => item.type == 'weimob')[0]
      this.ruleForm.weimobId = weimobChannel ? [this.channelId(weimobChannel)] : []
      const baiLianChannel = coupon.sychChannel.filter(item => item.type == 'baiLian')[0]
      this.ruleForm.baiLianId = baiLianChannel ? [this.channelId(baiLianChannel)] : []

      this.ruleForm.sychChannel = coupon.sychChannel
      // 用券门店
      this.ruleForm.storeRange = coupon.useStores
      // 用券商品
      this.ruleForm.useCouponGood = coupon.useGoods
      // 用券时段
      this.ruleForm.time = coupon.useTimeRange
      // 用券渠道
      if (coupon.useChannels && coupon.templateId) {
        if (coupon.useChannels.channelRangeType === "ALL") {
          this.ruleForm.useFrom = "step1";
          this.ruleForm.from = [];
        } else {
          this.ruleForm.useFrom = "step2";
          if (coupon.useChannels.channels && coupon.useChannels.channels.length > 0) {
            let arrs: string[] = [];
            coupon.useChannels.channels.forEach((item: any) => {
              if (item.id || item.type) {
                if (item.id && item.id !== "-") {
                  arrs.push(item.type + item.id);
                } else {
                  arrs.push(item.type);
                }
              } else {
                arrs.push(item);
              }
            });
            this.ruleForm.from = arrs;
          }
        }
      }
      // 用券门槛类型
      this.ruleForm.useThreshold = coupon.useThreshold

      // 叠加促销
      if (coupon.excludePromotion || coupon.excludePromotion === false) {
        this.ruleForm.promotionInfo.excludePromotion = coupon.excludePromotion;
      } else {
        this.ruleForm.promotionInfo.excludePromotion = true;
      }
      this.ruleForm.promotionInfo.promotionSuperpositionType = coupon.promotionSuperpositionType
      this.ruleForm.promotionInfo.promotion = coupon.promotion
      this.ruleForm.promotionInfo.goodsFavRules = coupon.goodsFavRules
      // 用券记录方式
      if (coupon.useApporion && coupon.useApporion!.subjectApprotionType) {
        this.ruleForm.recordWay = coupon.useApporion!.subjectApprotionType
      } else {
        this.ruleForm.recordWay = 'FAV'
      }

      if (this.ruleForm.recordWay === "COLLOCATION") {
        if (coupon.useApporion!.recordType === 'AMOUNT') {
          this.ruleForm.payWay = coupon.useApporion!.payValue;
          this.specialGoods = coupon.useApporion!.specialGoodsAmounts || []
          this.ruleForm.parties = coupon.useApporion!.parties || []
          this.ruleForm.amountPayments = coupon.useApporion!.amountPayments || []
        } else {
          this.ruleForm.discountWay = coupon.useApporion!.favValue;
          this.ruleForm.payWay = coupon.useApporion!.payValue;
        }

      }
      this.ruleForm.recordType = coupon.useApporion!.recordType
      // 券承担方
      // this.ruleForm.couponUnder = coupon.costParties
      this.ruleForm.couponUnder = coupon.costParty;
      // 用券顺序
      this.ruleForm.couponOrder = coupon.priority
      // 用券商品说明
      this.ruleForm.couponGoodsDesc = coupon.goodsRemark
      // 用券说明
      this.ruleForm.couponProduct = coupon.remark
      // 是否可以叠加用券
      // this.$refs.groupMutexTemplate.initValue2(coupon, this.copyFlag)
      // 券logo
      this.ruleForm.logoUrl = coupon.logoUrl
      // 券码前缀
      this.ruleForm.prefix = coupon.codePrefix;
      // 是否支持转赠
      this.ruleForm.transferable = coupon.transferable
      // 兑换券参数
      this.ruleForm.exchangeGoodsCouponAttribute = coupon.exchangeGoodsCouponAttribute
      this.ruleForm.exchangeGoodsCouponAttribute.limitGoods = true  //PHX-8727固定选择部分商品
      this.goodsType = 'PART'
      this.doUseCouponGoodsBind(coupon.exchangeGoodsCouponAttribute!.pickUpGoodsGroups)
      this.doUseCouponGiftGoodsBind(coupon.exchangeGoodsCouponAttribute!.giftGoodsGroups)
      // 剩余库存
      this.ruleForm.weimobCouponAndTotal.total = coupon.total
      // 微盟券参数
      this.ruleForm.weimobCouponAndTotal.weimobCoupon = coupon.weimobCoupon;
      this.ruleForm.templateTag = coupon.templateTag
      if (this.$refs.weimobCouponInfo) {
        this.$refs.weimobCouponInfo.doBindValue(this.ruleForm.weimobCouponAndTotal)
      }
      //外部券模板号
      if (coupon.outerRelations && coupon.outerRelations[0] && (this.showOuterRelationsConfig || this.$route.query.from === 'edit') && this.$route.query.from !== 'copy') {
        // 配置不展示外部券模板号时，编辑券模板保留该字段，复制不保留该字段
        this.ruleForm.outerRelations = coupon.outerRelations[0]
      }
      //价格
      this.ruleForm.price = coupon.salePrice
      //账款项目
      this.ruleForm.termsModel = coupon.termsModel
      //券角标
      this.ruleForm.couponSubscriptType = coupon.couponSubscriptType
      //核销链接
      this.ruleForm.writeOffLink = coupon.writeOffLink
      //备注
      this.ruleForm.notes =coupon.notes
      //每人每天限量
      this.ruleForm.maxDailyMemberQuotaQty = coupon.maxDailyMemberQuotaQty      
    }
  }

  private channelId(item: Channel) {
    return `${item.type}${item.id}`
  }

  private getPayWayDtl() {
    CouponInitialApi.get().then((resp: any) => {
      if (resp && resp.code === 2000) {
        if (resp.data && resp.data.subjectApportion === "pay") {
          this.ruleForm.recordWay = "PAY";
        } else if (resp.data && resp.data.subjectApportion === "fav") {
          this.ruleForm.recordWay = "FAV";
        } else if (resp.data && resp.data.subjectApportion === "collection") {
          this.ruleForm.recordWay = "COLLOCATION";
          this.ruleForm.recordType = "AMOUNT"
          this.ruleForm.payWay = 0
        }
      }
    });
  }

  private getCostParty() {
    let params: RSCostPartyFilter = new RSCostPartyFilter()
    params.page = 0
    params.pageSize = 0
    CostPartyApi.query(params).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.parties = resp.data
      }
    }).catch((error: any) => {
      this.$message.error(error.message)
    })
  }

  doUseCouponGoodsChange(data: any[]) {
    this.doFormItemChange()
    this.canChooseItems = []
    this.doCanChooseItemChange()
    if (this.ruleForm.useCouponGood[this.currentGoodsTab].data.length > 0) {
      this.ruleForm.useCouponGood[this.currentGoodsTab].exchangeGoodsCouponAttribute.exchangeQty = this.ruleForm.useCouponGood[this.currentGoodsTab].exchangeGoodsCouponAttribute.exchangeQty || this.ruleForm.useCouponGood[this.currentGoodsTab].data.length
    } else {
      this.ruleForm.useCouponGood[this.currentGoodsTab].exchangeGoodsCouponAttribute.exchangeQty = null
      this.ruleForm.useCouponGood[this.currentGoodsTab].exchangeGoodsCouponAttribute.enableExchangeSameGood = false
      this.canChooseItems = []
    }
    this.doGoodsQtyChange()
    this.ruleForm.useCouponGood[this.currentGoodsTab].exchangeGoodsCouponAttribute.exchangeGoods = data
    if(!this.isShowCanSelectSameGoods(data)){
      this.ruleForm.useCouponGood[this.currentGoodsTab].exchangeGoodsCouponAttribute.enableExchangeSameGood = false
    }
  }

  doGoodsQtyChange(e?: any) {
    // 兑换数量是否大于商品种数
    const isExchangeQtyLarger = Number(this.ruleForm.useCouponGood[this.currentGoodsTab].exchangeGoodsCouponAttribute.exchangeQty) > this.ruleForm.useCouponGood[this.currentGoodsTab].data.length
    if (isExchangeQtyLarger) {
      this.ruleForm.useCouponGood[this.currentGoodsTab].exchangeGoodsCouponAttribute.enableExchangeSameGood = true
    }
    if (e) {
      this.doFormItemChange()
    }
  }

  doUseCouponGiftGoodsChange(data: any[]) {
    this.ruleForm.useCouponGifGoods = {}
    this.ruleForm.useCouponGifGoods = [{
      data: data,
      exchangeGoodsCouponAttribute: {
        exchangeQty: data.length,
        exchangeGoods: data,
        enableExchangeSameGood: false
      }
    }]
    this.doFormItemChange()
    if (this.ruleForm.useCouponGifGoods[0].data.length > 0) {
      this.ruleForm.useCouponGifGoods[0].exchangeGoodsCouponAttribute.exchangeQty = this.ruleForm.useCouponGifGoods[0].data.length
    } else {
      this.ruleForm.useCouponGifGoods[0].exchangeGoodsCouponAttribute.exchangeQty = null
    }
    this.ruleForm.useCouponGifGoods[0].exchangeGoodsCouponAttribute.exchangeGoods = data
    if(!this.isShowCanSelectSameGoods(data)){
      this.ruleForm.useCouponGifGoods[0].exchangeGoodsCouponAttribute.enableExchangeSameGood = false
    }
  }

  doUseCouponGoodsBind(data: any[]) {
    this.ruleForm.useCouponGood = []
    let goodType = this.chooseGoodType
    if (goodType === 'appreciation') {
      data.forEach(item => {
        item.exchangeGoods.forEach((good: any) => {
          good.appreciationGoods = true;
        });
      });
    }
    data.forEach((item) => {
      this.ruleForm.useCouponGood.push({
        data: item.exchangeGoods,
        exchangeGoodsCouponAttribute: {
          enableExchangeSameGood: item.enableExchangeSameGood,
          exchangeQty: item.exchangeQty
        }
      })
    })
    this.$refs.chooseGoodsRange.getDefaultValue(this.ruleForm.useCouponGood[this.currentGoodsTab]?.data)
    this.canChooseItems = []
    this.doCanChooseItemChange()
    console.log('赋值后的ruleForm.useCouponGood', this.ruleForm.useCouponGood)
  }

  doUseCouponGiftGoodsBind(data: any[]) {
    this.ruleForm.useCouponGifGoods = []
    data.forEach((item) => {
      this.ruleForm.useCouponGifGoods.push({
        data: item.exchangeGoods,
        exchangeGoodsCouponAttribute: {
          enableExchangeSameGood: item.enableExchangeSameGood,
          exchangeQty: item.exchangeQty
        }
      })
    })
    this.$refs.chooseGiftGoodsRange.getDefaultGoodValue(this.ruleForm.giftGoodsGroups ? this.ruleForm.giftGoodsGroups[0]?.data : [])
  }

  //计算用券商品"X选N"下拉框
  doCanChooseItemChange() {
    this.canChooseItems = []
    if (!this.ruleForm.useCouponGood[this.currentGoodsTab]) return
    this.ruleForm.useCouponGood[this.currentGoodsTab].data.forEach((item: any, index: number) => {
      if (index === 0) {
        this.canChooseItems.push({
          label: this.formatI18n('/公用/券模板/提货券/用券商品/全部可选'),
          value: this.ruleForm.useCouponGood[this.currentGoodsTab].data.length
        })
      } else {
        this.canChooseItems.push({
          label: `${this.ruleForm.useCouponGood[this.currentGoodsTab].data.length}${this.formatI18n('/公用/券模板/提货券/用券商品/选')}${this.ruleForm.useCouponGood[this.currentGoodsTab].data.length - index}`,
          value: this.ruleForm.useCouponGood[this.currentGoodsTab].data.length - index
        })
      }
    })
  }

  getMenkan(threshold: any) {
    if (threshold.thresholdType === 'NONE') {
      return this.formatI18n('/公用/券模板/无门槛')
    } else {
      return `${this.formatI18n('/公用/券模板/满')}${threshold.threshold}${this.formatI18n('/公用/券模板/元')}${this.formatI18n('/公用/券模板/可兑换')}`
    }
  }

  // 接受特殊商品参数
  specialGoodsSubmit(goods: RSGoods[]) {
    this.specialGoods = [...goods]
    this.doFormItemChange()
  }

  telescopingChange() {
    this.telescoping = !this.telescoping
  }

  doSynChannelChange(newValue: any[]) {
    if (this.ruleForm.weimobId && this.ruleForm.weimobId.length >= 2) {
      this.ruleForm.weimobId = this.ruleForm.weimobId.slice(-1)
    }
    this.doFormItemChange()
  }

  doBaiLianSynChannelChange(newValue: any[]) {
    if (this.ruleForm.baiLianId && this.ruleForm.baiLianId.length >= 1) {
      this.ruleForm.weimobId.splice(0)
    }
    this.doFormItemChange()
  }

  handleTabsEdit(action: string, index?: any) {
    if (action === 'change') {
      this.currentGoodsTab = Number(index)
      this.doCanChooseItemChange()
    }
    else if (action === 'add') {
      this.ruleForm.useCouponGood.push({
        data: [],
        exchangeGoodsCouponAttribute: {
          exchangeQty: null,  //X选N的数量
          enableExchangeSameGood: false, //是否可重复选
        }
      })
    }
    else if (action === 'remove') {
      this.ruleForm.useCouponGood.splice(Number(index), 1)
      if (this.currentGoodsTab == index) {
        this.currentGoodsTab = 0
      }
    }
    this.$refs.ruleForm.validateField('useCouponGood');
  }

  //用券商品，将参数转变为接口需要的结构
  doGoodsToParams() {
    const result = new ExchangeGoodsCouponAttribute()
    result.limitGoods = true
    result.afterUseAmount = this.ruleForm.exchangeGoodsCouponAttribute.afterUseAmount || 0
    result.pickUpGoodsGroups = this.ruleForm.useCouponGood.map((item: any, index: any) => {
      const goodsType = item.data[0]?.goods === null ? 'category' : 'single'
      return {
        type: goodsType,
        exchangeGoods: item.data,
        exchangeQty: item.exchangeGoodsCouponAttribute.exchangeQty,
        sequence: index + 1,
        enableExchangeSameGood: item.exchangeGoodsCouponAttribute.enableExchangeSameGood
      }
    })
    if (this.ruleForm.useCouponGifGoods && this.ruleForm.useCouponGifGoods.length > 0) {
      result.giftGoodsGroups = this.ruleForm.useCouponGifGoods.filter((item:any) => item.data && item.data.length > 0).map((item: any, index: any) => {
        return {
          type: 'single',
          exchangeGoods: item.data,
          exchangeQty: item.data?.length,
          sequence: index + 1,
          enableExchangeSameGood: item.exchangeGoodsCouponAttribute ? item.exchangeGoodsCouponAttribute.enableExchangeSameGood: false
        }
      })
    } else {
      this.ruleForm.useCouponGifGoods = []
    }
    return result
  }

  doOuterTemplateChange(data: any) {
    this.ruleForm.outerRelations = data
    this.doFormItemChange()
  }

  changeSwitch(flag: any) {
    this.enablePayApportion = flag;
    this.doFormItemChange()
  }
}