import ApiClient from 'http/ApiClient'
import Circle from 'model/circle/Circle'
import CircleFilter from 'model/circle/CircleFilter'
import Response from 'model/common/Response'

export default class CircleApi {
  /**
   * 地址查询
   * 地址查询。
   * 
   */
  static query(body: CircleFilter): Promise<Response<Circle[]>> {
    return ApiClient.server().post(`/v1/circle/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
