<template>
  <div class="member-tag">
    {{ text }}
    <div class="tag-close" @click="onClose" v-if="closable"><i class="el-icon-close"></i></div>
  </div>
</template>
<script lang="ts"
        src="./MemberTag.ts">
</script>
<style lang="scss"
       scoped>
.member-tag {
  height: 20px;
  background: #F7F9FC;
  border-radius: 16px;
  border: 1px solid #D7DFEB;
  padding: 0 8px;
  display: inline-flex;
  align-items: center;
  font-weight: 400;
  font-size: 12px;
  color: #36445A;
  line-height: 18px;

  .tag-close {
    display: inline-block;
    margin-left: 8px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    text-align: center;
    cursor: pointer;
    font-size: 0;

    &:hover {
      background: #909399;
      color: white;
    }

    i {
      font-size: 12px;
      line-height: 16px !important;
    }
  }
}
</style>
