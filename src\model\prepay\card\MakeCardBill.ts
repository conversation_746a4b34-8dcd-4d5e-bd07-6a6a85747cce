/*
 * @Author: 黎钰龙
 * @Date: 2023-09-27 14:57:19
 * @LastEditTime: 2024-08-28 15:14:30
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\prepay\card\MakeCardBill.ts
 * 记得注释
 */
import IdName from "model/common/IdName"
import MakeCardBillLog from "./MakeCardBillLog"

export default class MakeCardBill {
  // uuid
  uuid: Nullable<string> = null
  // 单号
  billNumber: Nullable<string> = null
  // 起始卡号
  startCardCode: Nullable<string> = null
  // 截至卡号
  endCardCode: Nullable<string> = null
  // 制卡数量
  makeQty: Nullable<number> = null
  // 备注
  remark: Nullable<string> = null
  // 发生组织
  occurredOrg: Nullable<IdName> = null
  // 营销中心
  marketingCenter: Nullable<string> = null
  // 卡模板号
  cardTemplateNumber: Nullable<string> = null
  // 卡模板名称
  cardTemplateName: Nullable<string> = null
  // 卡介质  online-电子卡、bar-条码卡、mag-磁条卡、rfic-RFIC卡
  cardMedium: Nullable<string> = null
  // 状态INITIAL：未审核；AUDITED：已审核；FINISH：制卡完成；CANCELED：已作废
  state: Nullable<string> = null
  // 创建时间
  created: Nullable<Date> = null
  // 最后修改时间
  lastModified: Nullable<Date> = null
  // 操作日志
  logs: MakeCardBillLog[] = []
  // 是否已经导出过卡密
  exportFinish: Nullable<boolean> = null
  // 写卡类型 NONE——无须写卡，SYS——商家写卡，MANUFACTOR——厂家写卡
  writeCardType: Nullable<string> = null
}