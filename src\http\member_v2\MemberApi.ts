import ApiClient from 'http/ApiClient'
import Card from 'model/member_v2/member/Card'
import Coupon from 'model/member_v2/member/Coupon'
import Member from 'model/member_v2/member/Member'
import MemberDetail from 'model/member_v2/member/MemberDetail'
import MemberFilter from 'model/member_v2/member/MemberFilter'
import Response from 'model/common/Response'
import SimpleCard from 'model/member_v2/member/SimpleCard'
import MemberTags from 'model/member_v2/member/MemberTags'
import MemberPass from 'model/member_v2/member/MemberPass'
import TransferBenefitItems from "model/member_v2/member/TransferBenefitItems";
import TagOption from 'model/member_v2/tag/TagOption'

export default class MemberApi {


  /**
   * 获取会员，券、积分、储值、预付款数量相关信息
   *
   */
  static transferMemberBenefit(body: TransferBenefitItems): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/member/transferMemberBenefit`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 获取会员，券、积分、储值、预付款数量相关信息
   *
   */
  static getMemberAndBenefit(memberId: string): Promise<Response<MemberDetail>> {
    return ApiClient.server().get(`/v1/member/getMemberAndBenefit/${memberId}`, {}).then((res) => {
      return res.data
    })
  }


  /**
   * 批量导出会员信息
   *
   */
  static exportMember(body: MemberFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/custom/member/exportMember`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 冻结会员
   *
   */
  static block(memberId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/custom/member/block/${memberId}`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 解冻结会员
   *
   */
  static unBlock(memberId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/custom/member/unBlock/${memberId}`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 批量导入会员信息
   *
   */
  static importMember(body: any, card: boolean): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/custom/member/importMember`, body, {
      params: {
        card: card
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 检查要修改的手机号是否已经存在，存在返回true，修改为自身原有手机号返回false
   *
   */
  static checkMobile(mobile: string, memberId: string): Promise<Response<boolean>> {
    return ApiClient.server().get(`/v1/custom/member/checkMobile/${mobile}/${memberId}`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 会员详情
   *
   */
  static detail(memberId: string): Promise<Response<MemberDetail>> {
    return ApiClient.server().post(`/v1/custom/member/detail/${memberId}`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 获取会员卡信息
   *
   */
  static getCards(memberId: string): Promise<Response<SimpleCard[]>> {
    return ApiClient.server().get(`/v1/custom/member/getCards/${memberId}`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 获取会员券信息
   *
   */
  static getCoupon(memberId: string): Promise<Response<Coupon[]>> {
    return ApiClient.server().get(`/v1/custom/member/getCoupons/${memberId}`, {}).then((res) => {
      return res.data
    })
  }


  /**
   * 修改会员支付密码
   *
   */
  static modifyPayPass(body: MemberPass): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/custom/member/modifyPayPass`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 会员列表信息查询
   *
   */
  static query(body: MemberFilter): Promise<Response<Member[]>> {
    return ApiClient.server().post(`/v1/custom/member/query`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * IC卡列表信息查询
   *
   */
  static queryCard(memberId: string, page: number, pageSize: number): Promise<Response<Card[]>> {
    return ApiClient.server().post(`/v1/custom/member/queryCard/${memberId}`, {}, {
      params: {
        page: page,
        pageSize: pageSize
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存会员基础信息
   *
   */
  static saveMemberBasicData(body: MemberDetail, memberId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/custom/member/saveMemberBasicData/${memberId}`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 保存会员等级相关信息
   *
   */
  static saveMemberGradeData(body: MemberDetail, memberId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/custom/member/saveMemberGradeData/${memberId}`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 保存会员标签信息
   *
   */
  static saveMemberTag(body: MemberTags): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/custom/member/saveMemberTag`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 解绑会员IC卡
   *
   */
  static unbind(memberId: string, cardFaceCode: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/custom/member/unbind/${memberId}/${cardFaceCode}`, {}, {}).then((res) => {
      return res.data
    })
  }

  
  /**
   * 已签约车牌付信息查询
   *
   */
  static queryCarInfo(memberId: string, page: number, pageSize: number, state: string): Promise<Response<Card[]>> {
    return ApiClient.server().post(`/v1/custom/member/getCarPayContract/${memberId}`, {}, {
      params: {
        page,
        pageSize,
        state
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 查询白名单
  *
  */
  static getBalanceAccountWhitelisting(memberId: string): Promise<Response<void>> {
    return ApiClient.server().get(`/v1/member/getBalanceAccountWhitelisting/${memberId}`, {}).then((res) => {
      return res.data
    })
  }
  
  /**
  * 添加白名单
  *
  */
  static addBalanceAccountWhitelisting(memberId: string): Promise<Response<void>> {
    return ApiClient.server().get(`/v1/member/addBalanceAccountWhitelisting/${memberId}`, {}).then((res) => {
      return res.data
    })
  }

  /**
  * 删除白名单
  *
  */
  static deleteBalanceAccountWhitelisting(memberId: string): Promise<Response<void>> {
    return ApiClient.server().get(`/v1/member/deleteBalanceAccountWhitelisting/${memberId}`, {}).then((res) => {
      return res.data
    })
  }

  /**
  * 查询会员标签
  * 查询会员标签。
  *
  */
  static getMemberTag(memberId: string): Promise<Response<TagOption[]>> {
    return ApiClient.server()
      .get(`/v1/member/getMemberTag/${memberId}`, {})
      .then((res) => {
        return res.data;
      });
  }
}
