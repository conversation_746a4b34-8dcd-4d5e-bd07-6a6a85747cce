/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:20
 * @LastEditTime: 2024-08-11 13:44:09
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\card\activity\GiftCardRuleDetail.ts
 * 记得注释
 */
import GiftCardSaleSpecs from 'model/card/activity/GiftCardSaleSpecs'
import { CardMedium } from 'model/default/CardMedium'
import { FavType } from 'model/default/FavType'
import DateTimeCondition from "model/common/DateTimeCondition";

export default class GiftCardRuleDetail {
    // 封面图片
    themePictureUrls: string[] = []
    // 默认祝福语
    defaultGiftMsg: Nullable<string> = null
    // 每人限购，null标识不限制
    maxBuyQty: Nullable<number> = null
    /**
     * 卡类型
     * GiftCard-礼品卡, RechargeableCard-储值卡，ImprestCard-充值卡，CountingCard-次卡
     */
    cardType: Nullable<string> = null
    /**
     * 卡介质
     * online-电子卡, bar-条码卡, mag-磁条卡, rfic-RFIC卡
     */
    cardMedium: Nullable<CardMedium> = null
    /**
     * 优惠类型
     * amount-按售价, discount-按折扣
     */
    favType: Nullable<FavType> = null
    // 发售规格
    saleSpecs: GiftCardSaleSpecs[] = []
    	// 活动时间限制
	dateTimeCondition = new DateTimeCondition();
}