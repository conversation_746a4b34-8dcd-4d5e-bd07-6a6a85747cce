/*
 * @Author: 黎钰龙
 * @Date: 2024-07-31 09:35:15
 * @LastEditTime: 2024-07-31 09:55:48
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\promotion\exchangeCode\RedemptionCodeBill.ts
 * 记得注释
 */
import { CodeType } from "./CodeType"
import { ExecuteState } from "./ExecuteState"
import RedemptionRule from "./RedemptionRule"

// 兑换码单据
export default class RedemptionCodeBill {
  // 单号
  number: Nullable<string> = null
  // 兑换码名称
  name: Nullable<string> = null
  // 营销中心
  marketingCenter: Nullable<string> = null
  // 所属门店
  orgId: Nullable<string> = null
  // 所属门店名称
  orgName: Nullable<string> = null
  // 使用须知
  remark: Nullable<string> = null
  // 生成方式 AUTO-系统生成；IMPORT-导入
  codeType: Nullable<CodeType> = null
  // 码值长度
  codeLength: Nullable<number> = null
  // 总库存
  total: Nullable<number> = null
  // 状态(前端不用)
  state: Nullable<string> = null
  // 兑换码生效开始日期
  beginTimeInclusive: Nullable<Date> = null
  // 兑换码生效截至日期
  endTimeExclusive: Nullable<Date> = null
  // 使用规则
  body: Nullable<RedemptionRule> = null
  // 创建时间
  created: Nullable<Date> = null
  // 最后修改时间
  lastModified: Nullable<Date> = null
  // 当前时刻的执行状态；INITIAL——未审核；WAITING——待执行；EXECUTING——执行中；OVER——已结束
  executeState: Nullable<ExecuteState> = null
}