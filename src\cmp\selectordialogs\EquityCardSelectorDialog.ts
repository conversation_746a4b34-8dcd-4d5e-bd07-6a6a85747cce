/*
 * @Author: 黎钰龙
 * @Date: 2023-08-03 09:32:30
 * @LastEditTime: 2023-08-03 18:27:42
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\selectordialogs\EquityCardSelectorDialog.ts
 * 记得注释
 */
import { Component } from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import AbstractSelectDialog from './AbstractSelectDialog'
import SelectStoreActiveDtlDialog from 'pages/deposit/mbrdeposit/active/dialog/SelectStoreActiveDtlDialog.vue'
import I18nPage from 'common/I18nDecorator'
import CommonUtil from 'util/CommonUtil'
import EquityCardQueryRequest from 'model/equityCard/EquityCardQueryRequest'
import IdName from 'model/common/IdName'
import EquityCardApi from 'http/equityCard/equityCardApi'
import BEquityCardExpiryRule from 'model/equityCard/BEquityCardExpiryRule'
import BEquityCardTemplate from 'model/benefit/BEquityCardTemplate'

@Component({
  name: 'EquityCardSelectorDialog',
  components: {
    FormItem,
    SelectStoreActiveDtlDialog
  }
})
@I18nPage({
  prefix: [
    "/公用/券模板",
    '/公用/公共组件/券模板选择弹框组件/标题',
    '/公用/查询条件/提示',
    '/公用/按钮',
    '/公用/提示',
    '/会员/权益卡',
    '/营销/券礼包活动/券查询/活动名称输入框placeholder',
    '/储值/预付卡/卡模板/编辑页面',
    '/会员/会员资料'
  ],
  auto: true
})
export default class EquityCardSelectorDialog extends AbstractSelectDialog<IdName> {
  labelWidthMap: any = {
    zh_CN: '100px',
    en_US: '180px',
    default: '180px'
  }
  index: number = 0
  listFilter: any = {
    nameLikes: ''
  }
  copyList: any = []  //完整数据列表的拷贝

  get labelWidth() {
    return this.labelWidthMap[
      CommonUtil.getLocale('locale') as string
    ] as string
  }

  queryFun(): Promise<any> {
    return EquityCardApi.query()
  }

  reset(): void {
    this.listFilter.nameLikes = null
  }

  getId(ins: IdName): string {
    // @ts-ignore
    return ins.id
  }

  getName(ins: IdName): string {
    // @ts-ignore
    return ins.name
  }

  getResponseData(response: any): any {
    let res = response.data.filter((item: BEquityCardTemplate)=>item.state !== 'stop').map((item: BEquityCardTemplate)=>{
      return {
        id: item.number,
        name: item.name,
        expiryRules: item.expiryRules
      }
    })
    this.copyList = res
    return res
  }

  //根据卡名称过滤当前列表数据
  search() {
    this.currentList = this.copyList.filter((item:any)=>{
      if (!this.listFilter.nameLikes) this.listFilter.nameLikes = ''
      return item.name?.indexOf(this.listFilter.nameLikes) > -1
    })
  }

  //有效期设置字段
  ValidityTime(item: BEquityCardExpiryRule) {
    let str = ''
    const priedType = item.validityInfo?.expiryType
    str += item.price + '元，'
    str += item.validityInfo?.validityDays! + (priedType === "DAYS" ? '天，' : priedType === "MONTHS" ? '月，' : '年，')
    str += '续费' + (item.renewPrice == item.price ? '无优惠' : item.renewPrice + '元')
    return str
  }

  doModalClose() {
    this.dialogShow = false
    this.$emit('summit', this.selected)
  }
}
