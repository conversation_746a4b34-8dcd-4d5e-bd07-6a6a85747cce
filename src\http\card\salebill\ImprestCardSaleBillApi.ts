/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:19
 * @LastEditTime: 2024-04-22 15:34:26
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\card\salebill\ImprestCardSaleBillApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import ImprestCardSaleBill from 'model/card/salebill/ImprestCardSaleBill'
import ImprestCardSaleBillFilter from 'model/card/salebill/ImprestCardSaleBillFilter'
import Response from 'model/common/Response'

export default class ImprestCardSaleBillApi {
  /**
   * 导出
   *
   */
  static export(billNumber: string): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/imprest-sale-bill/export/${billNumber}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取充值卡发售单详情
   *
   */
  static get(billNumber: string): Promise<Response<ImprestCardSaleBill>> {
    return ApiClient.server().get(`/v1/imprest-sale-bill/get/${billNumber}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询充值卡发售单
   *
   */
  static query(body: ImprestCardSaleBillFilter): Promise<Response<ImprestCardSaleBill[]>> {
    return ApiClient.server().post(`/v1/imprest-sale-bill/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取发售单已售卡
   *
   */
  static queryCards(billNumber: string): Promise<Response<string[]>> {
    return ApiClient.server().get(`/v1/imprest-sale-bill/queryCards/${billNumber}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存充值卡发售单
   *
   */
  static save(body: ImprestCardSaleBill): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/imprest-sale-bill/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 自动生成起始卡号
   * 自动生成起始卡号
   *
   */
  static autoMaxCardCode(length: number): Promise<Response<string>> {
    return ApiClient.server().get(`/v1/imprest-sale-bill/autoMaxCardCode/${length}`, {
    }).then((res) => {
      return res.data
    })
  }

}
