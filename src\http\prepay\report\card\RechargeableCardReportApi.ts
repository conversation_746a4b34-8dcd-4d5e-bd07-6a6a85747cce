import ApiClient from 'http/ApiClient'
import CardReportSum from 'model/prepay/report/card/CardReportSum'
import GiftCardCardHst from 'model/prepay/report/card/GiftCardCardHst'
import GiftCardConsumeOrRefundHst from 'model/prepay/report/card/GiftCardConsumeOrRefundHst'
import GiftCardFilter from 'model/prepay/report/card/GiftCardFilter'
import Response from 'model/common/Response'
import CardDepositHst from "model/prepay/report/card/CardDepositHst";

export default class RechargeableCardReportApi {
  /**
   * 储值卡售卡流水汇总
   *
   */
  static cardHstSum(body: GiftCardFilter): Promise<Response<CardReportSum>> {
    return ApiClient.server().post(`/v1/prepay/card/report/rechargeable/hst/sum`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 储值卡消费流水汇总
   *
   */
  static consumeHstSum(body: GiftCardFilter): Promise<Response<CardReportSum>> {
    return ApiClient.server().post(`/v1/prepay/card/report/rechargeable/consume/hst/sum`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 储值卡消费退款流水汇总
   *
   */
  static consumeRefundHstSum(body: GiftCardFilter): Promise<Response<CardReportSum>> {
    return ApiClient.server().post(`/v1/prepay/card/report/rechargeable/consume/refund/hst/sum`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 储值卡售卡流水
   *
   */
  static queryCardHst(body: GiftCardFilter): Promise<Response<GiftCardCardHst[]>> {
    return ApiClient.server().post(`/v1/prepay/card/report/rechargeable/hst/query`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 储值卡售卡流水
   *
   */
  static queryRefundCardHst(body: GiftCardFilter): Promise<Response<GiftCardCardHst[]>> {
    return ApiClient.server().post(`/v1/prepay/card/report/rechargeable/refund/hst/query`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 储值卡消费流水
   *
   */
  static queryConsumeHst(body: GiftCardFilter): Promise<Response<GiftCardConsumeOrRefundHst[]>> {
    return ApiClient.server().post(`/v1/prepay/card/report/rechargeable/consume/hst/query`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 储值卡消费退款流水
   *
   */
  static queryConsumeRefundHst(body: GiftCardFilter): Promise<Response<GiftCardConsumeOrRefundHst[]>> {
    return ApiClient.server().post(`/v1/prepay/card/report/rechargeable/consume/refund/hst/query`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 储值卡售卡流水报表导出
   * 储值卡售卡流水报表导出
   *
   */
  static exportSalesHst(body: GiftCardFilter): Promise<Response<GiftCardConsumeOrRefundHst[]>> {
    return ApiClient.server().post(`/v1/prepay/card/report/rechargeable/exportSales`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 储值卡退卡流水报表导出
   * 储值卡退卡流水报表导出
   *
   */
  static exportRefundCard(body: GiftCardFilter): Promise<Response<GiftCardConsumeOrRefundHst[]>> {
    return ApiClient.server().post(`/v1/prepay/card/report/rechargeable/exportRefundCard`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 储值卡消费流水报表导出
   * 储值卡售卡流水报表导出
   *
   */
  static exportConsumeHst(body: GiftCardFilter): Promise<Response<GiftCardConsumeOrRefundHst[]>> {
    return ApiClient.server().post(`/v1/prepay/card/report/rechargeable/exportConsume`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 储值卡退款流水报表导出
   * 储值卡售卡流水报表导出
   *
   */
  static exportRefundHst(body: GiftCardFilter): Promise<Response<GiftCardConsumeOrRefundHst[]>> {
    return ApiClient.server().post(`/v1/prepay/card/report/rechargeable/exportRefund`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 储值卡充值流水
   * 储值卡充值流水。
   *
   */
  static queryDepositHst(body: GiftCardFilter): Promise<Response<CardDepositHst[]>> {
    return ApiClient.server().post(`/v1/prepay/card/report/rechargeable/deposit/hst/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 储值卡充值流水汇总
   * 储值卡充值流水汇总。
   *
   */
  static depositHstSum(body: GiftCardFilter): Promise<Response<CardReportSum[]>> {
    return ApiClient.server().post(`/v1/prepay/card/report/rechargeable/deposit/hst/sum`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 储值卡充值流水报表导出
   * 储值卡充值流水报表导出
   *
   */
  static exportRechargeHst(body: GiftCardFilter): Promise<Response<GiftCardConsumeOrRefundHst[]>> {
    return ApiClient.server().post(`/v1/prepay/card/report/rechargeable/exportRecharge`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 储值卡转出流水导出
   * 储值卡转出流水导出。
   *
   */
  static exportTransferHst(body: GiftCardFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/prepay/card/report/rechargeable/exportTransfer`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 储值卡转出流水
   * 储值卡转出流水。
   *
   */
  static queryTransferHst(body: GiftCardFilter): Promise<Response<GiftCardConsumeOrRefundHst[]>> {
    return ApiClient.server().post(`/v1/prepay/card/report/rechargeable/transfer/hst/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 储值卡转出流水汇总
   * 储值卡转出流水汇总。
   *
   */
  static transferHstSum(body: GiftCardFilter): Promise<Response<CardReportSum>> {
    return ApiClient.server().post(`/v1/prepay/card/report/rechargeable/transfer/hst/sum`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
