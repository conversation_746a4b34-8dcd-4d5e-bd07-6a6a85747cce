<template>
  <div class="marketcenter-member-dtl-new">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <div style="padding-right: 40px">
          <el-button @click="doAdjust" v-if="hasOptionPermission('/会员/会员管理/营销中心会员', '调整会员等级')">
            {{ formatI18n('/会员/会员资料', '调整会员等级') }}
          </el-button>
          <el-button @click="doReset" v-if="hasOptionPermission('/会员/会员管理/营销中心会员', '重置支付密码')">
            {{ formatI18n('/会员/会员资料', '重置支付密码') }}
          </el-button>
          <el-button @click="doFreezon" style="color: red"
                     v-if="dtl.state === 'Blocked' && hasOptionPermission('/会员/会员管理/营销中心会员', '冻结/解冻会员')">
            {{ formatI18n('/会员/会员资料', '解冻') }}
          </el-button>
          <el-button @click="doFreezon" style="color: red"
                     v-if="dtl.state !== 'Blocked' && dtl.state !== 'Unactivated' && hasOptionPermission('/会员/会员管理/营销中心会员', '冻结/解冻会员')">
            {{ formatI18n('/会员/会员资料', '冻结') }}
          </el-button>
          <el-dropdown trigger="click" style="position: absolute;right: 0;top: 1px">
            <el-button style="padding: 0">
              <img src="~assets/image/icons/more.png" style="width: 25px"/>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="showAssets" v-if="dtl.state === 'Using' && hasOptionPermission('/会员/会员管理/营销中心会员', '转移权益资产')">
                {{ formatI18n('/会员/会员资料/详情界面/会员资产转移', '转移权益资产') }}
              </el-dropdown-item>
              <el-dropdown-item @click.native="doSinglkeModifyStore" v-if="hasOptionPermission('/会员/会员管理/营销中心会员', '修改归属门店')">
                {{ formatI18n('/会员/会员资料/详情界面/修改会员归属门店', '修改归属门店') }}
              </el-dropdown-item>
              <el-dropdown-item @click.native="showOptLog">
                {{ formatI18n('/会员/会员资料/详情界面/会员操作日志', '操作日志') }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </template>
    </BreadCrume>
    <div class="member-main">
      <member-left-info :dtl="dtl"
                        @reload="getDtl"></member-left-info>
      <div>
        <member-navbar :current-index="currentTabIndex"
                       @change="onTabChange"></member-navbar>
        <member-info-detail v-if="currentTabIndex==0"
                            :dtl="dtl"
                            :custom-member-attr="customMemberAttr"
                            :show-mobile-and-email-check-info="true"
                            :show-member-custom-group-info="true"
                            @edit="doEdit"></member-info-detail>
        <member-trade-detail v-if="currentTabIndex==1"
                             :dtl="dtl"></member-trade-detail>
        <member-card-detail v-if="currentTabIndex==2"
                            :dtl="dtl"></member-card-detail>
      </div>
    </div>

    <EditDataDialog
        :memberId="dtl.memberId"
        :data="dtl"
        :dialogShow="editDataFlag"
        :title="formatI18n('/会员/会员资料', '编辑会员资料')"
        @dialogClose="doEditDataClose">
    </EditDataDialog>
    <AdjustMemberLevelDialog
        :data="adjustParams"
        :dialogShow="adjustMemberLevelFlag"
        :title="formatI18n('/会员/会员资料', '调整会员等级')"
        @dialogClose="doEditAjustMemberLevelClose">
    </AdjustMemberLevelDialog>
    <ResetPasswordDialog
        v-if="resetPasswordFlag"
        :dialogShow="resetPasswordFlag"
        :title="formatI18n('/会员/会员资料', '重置支付密码')"
        @dialogClose="doResetPwdClose">
    </ResetPasswordDialog>
    <EditTagInfoDialog
        v-if="hasOptionPermission('/会员/标签客群/标签', '标签查看') && editTagFlag"
        :data="dtl.tags"
        :dialogShow="editTagFlag"
        :memberId="dtl.memberId"
        :title="formatI18n('/会员/会员资料', '编辑会员标签信息')"
        @dialogClose="doEditTagClose">
    </EditTagInfoDialog>
    <CheckCouponDialog
        :dialogShow="couponFlag"
        :title="formatI18n('/会员/会员资料/详情界面/会员资产/如果有可用券的点击可用券的数字', '当前会员可用券')"
        @dialogClose="doCouponClose"
    ></CheckCouponDialog>
    <CheckPrePayCardDialog
        :dialogShow="prepayCardFlag"
        :title="formatI18n('/会员/会员资料/详情界面/会员资产/如果有预付卡的点击预付卡的数字', '当前会员持有预付卡')"
        @dialogClose="doPrepayCardClose"
    >

    </CheckPrePayCardDialog>
    <CheckWater
        :dialogShow="waterFlag"
        :title="formatI18n('/会员/会员资料/详情界面/会员资产/储值余额有值时点击/标题/会员储值流水')"
        :uuid="dtl.balanceAccountUid"
        @dialogClose="doWaterClose"
    >

    </CheckWater>
    <UnBindDialog
        :dialogShow="unBindFlag"
        :memberId="dtl.memberId"
        :title="formatI18n('/公用/弹出模态框提示标题', '提示')"
        :uuid="bindUuid"
        @dialogClose="doUnbindClose">
    </UnBindDialog>
    <el-dialog
        :title="formatI18n('/会员/会员资料', '编辑会员备注')"
        :visible.sync="remarkDialog.visible"
        class="cosparty-dialog-center"
        width="550px">
      <div style="display: flex;align-items: start">
        <div>
          {{ formatI18n('/会员/会员资料', '备注') }}
        </div>
        <div style="width: 10px">&nbsp;</div>
        <el-input
            type="textarea"
            :placeholder="formatI18n('/公用/表单校验/请输入不超过{0}个字符', null, [80])"
            maxlength="80"
            show-word-limit
            v-model="remarkDialog.remark" style="width: 450px"></el-input>
      </div>
      <div class="dialog-footer" slot="footer">
        <el-button @click="() => remarkDialog.visible = false">{{ formatI18n('/公用/按钮', '取消') }}</el-button>
        <el-button @click="updateRemark" size="small" type="primary">{{ formatI18n('/公用/按钮', '确定') }}</el-button>
      </div>
    </el-dialog>
    <MemberOptLogDrawer ref="memberOptLogDrawer"></MemberOptLogDrawer>
    <MemberAssetsDrawer ref="memberAssetsDrawer" @closed="getDtl"></MemberAssetsDrawer>
    <SingleModifyStoreDialog
      :memberId="dtl.memberId"
      :ownStore="dtl.ownStore"
      :dialogShow="singleStoreDialogShow"
      @dialogClose="singleStoreDialogClose"
      @changeSuccess="singleStoreChangeSuccess">
    </SingleModifyStoreDialog>
    <UnBindSubDialog
        :dialogShow="unBindSubFlag"
        :title="formatI18n('/会员/会员资料', '解绑副卡')"
        :params="currentSubCard"
        @dialogClose="doUnbindSubClose"
        @confirm="unbindSubConfirm"
        >
    </UnBindSubDialog>
  </div>
</template>

<script lang="ts" src="./MarketCenterMemberDtl.ts">
</script>

<style lang="scss">
.marketcenter-member-dtl-new {
  padding-bottom: 24px;
  .member-main {
    display: flex;
    gap: 16px;
    align-items: flex-start;
    margin-top: 60px;

    & > :first-child {
      width: 320px;
      flex-shrink: 0;
    }

    & > :last-child {
      flex-grow: 1;
      min-width: 1px;
    }

  }

  .member-card {
    background: #FFFFFF;
    border-radius: 8px;
    padding: 24px;

    .divider {
      height: 1px;
      background: #D7DFEB;
      margin: 24px 0;
    }
  }
}
</style>
