import BRedemptionCodeFilter from 'model/promotion/exchangeCodeValue/BRedemptionCodeFilter'
import RedemptionCode<PERSON>pi from 'http/promotion/RedemptionCodeApi'

import BRedemptionCode from 'model/promotion/exchangeCodeValue/BRedemptionCode'
import { Component, Vue } from "vue-property-decorator";
import I18nPage from 'common/I18nDecorator';
import BRedemptionCodeCancel from 'model/promotion/exchangeCodeValue/BRedemptionCodeCancel';
import DateUtil from 'util/DateUtil';
import BRedemptionCodeGift from 'model/promotion/exchangeCodeValue/BRedemptionCodeGift';
import DownloadCenterDialog from 'pages/main/dowmload_center_dialog/DownloadCenterDialog';
import CommonUtil from 'util/CommonUtil';
import { error } from 'console';

// 定义状态枚举
enum State {
	ISSUED = 'ISSUED',
	WRITTEN_OFF = 'WRITTEN_OFF',
	EXPIRE = 'EXPIRE',
	CANCELLED = 'CANCELLED',
	NOT_STARTED = 'NOT_STARTED'
}

@Component({
	name: "Redemption",
	components: {  DownloadCenterDialog}
})
@I18nPage({
	prefix: [
		"/储值/会员储值/门店储值管理", "/储值/会员储值/门店预付卡管理", "/公用/提示",'/公用/菜单',
		'/营销/券礼包活动/券查询/券状态下拉选项',
		'/公用/过滤器',
		'/会员/会员资料',
		'/公用/提示',
		'/公用/公共组件/商品详情控件/操作按钮',
		'/公用/按钮'

	],
})
export default class PrepayCardStore extends Vue {
    searchForm = {
		stateEquals: null,
		codeIn: '',
		memberIdentEquals: '',
		page: 1,
		pageSize: 10
	};

	paging = {
		page: 1,
		total: 0,
		pageSize: 10,
	};


	
	dialogVisible: boolean = false;  // 控制对话框的显示状态
	queryCodeFilter: BRedemptionCodeFilter = new BRedemptionCodeFilter();
	tableData: BRedemptionCode[] = [];
	total = 0;
    exchangeCodeNumber: string = '';  // 添加一个属性来接收兑换码单号
	selectedCodes: BRedemptionCode[] = [];  // 用于存储选中的码值
	benefitsDialogVisible: boolean = false;
	selectedBenefits: BRedemptionCodeGift[] = [];  // 用于存储选中的兑换码权益
	disabled: false 
	fileDialogVisible = false

	handleSearch() {
		this.paging.page=1
		this.fetchData();
	}

	handleReset() {
		this.paging.page = 1;
		this.paging.pageSize = 10,
		this.searchForm.codeIn = ''
		this.searchForm.memberIdentEquals = ''
		this.searchForm.stateEquals = null
		console.log('After reset:', this.searchForm, this.paging);
		this.fetchData();
	}

	handleSizeChange(size: number) {
		this.paging.pageSize = size;
		this.fetchData();
	}

	handleCurrentChange(page: number) {
		this.paging.page = page;
		this.fetchData();
	}

	fetchData() {
		this.queryCodeFilter.page = this.paging.page-1;
		this.queryCodeFilter.pageSize = this.paging.pageSize;
		this.queryCodeFilter.stateEquals = this.searchForm.stateEquals
		this.queryCodeFilter.codeIn = this.searchForm.codeIn ? [this.searchForm.codeIn] : null;
		this.queryCodeFilter.memberIdentEquals = this.searchForm.memberIdentEquals || null

		const loading = CommonUtil.Loading()

		RedemptionCodeApi.query(this.queryCodeFilter).then((res) =>{
			loading.close()
			if (res.code === 2000 && res.data){
				this.total = res.total;
				this.tableData = res.data
			}else{
				this.$message.error(res.msg || 'query error')
			}
		}).catch((error) =>{
			loading.close()
			this.$message.error(error.message || 'query error')
		})

        
    }


	computeState(state: State) {
		switch (state) {
		case State.ISSUED:
			return { color: 'blue', state: this.i18n('未使用') };
		case State.WRITTEN_OFF:
			return { color: 'green', state: this.i18n('已使用') };
		case State.EXPIRE:
			return { color: 'yellow', state: this.i18n('已过期') };
		case State.CANCELLED:
			return { color: '#A1B0C8', state: this.i18n('已作废') };
		case State.NOT_STARTED:
			return { color: 'red', state: this.i18n('未开始') };
		default:
			return { color: 'gray', state: this.i18n('未知') };
		}
	}


	mounted() {
		
	}

	// 打开对话框
    openDialog(number: string) {
        this.dialogVisible = true;
        this.exchangeCodeNumber = number;
		this.paging.page=1
		this.paging.pageSize=10
		// this.queryCodeFilter.page = this.paging.page -1;
        // this.queryCodeFilter.pageSize = this.paging.pageSize
        this.queryCodeFilter.billNumberEquals = this.exchangeCodeNumber
		//重置搜索表单
		this.searchForm.codeIn = ''
		this.searchForm.memberIdentEquals = ''
		this.searchForm.stateEquals = null
		this.fetchData();
    }

    // 关闭对话框
    closeDialog() {
        this.dialogVisible = false;
    }

     // 添加新方法显示权益信息
	showBenefits(row: BRedemptionCode) {
        this.selectedBenefits = row.gifts;
        this.benefitsDialogVisible = true;
    }

    // 确认作废
    confirmCancel(number: string) {
		
        this.$confirm(this.i18n('确定要作废当前的码值吗？操作后无法恢复'), this.i18n('提示'), {
            confirmButtonText: this.i18n('确定'),
            cancelButtonText: this.i18n('取消'),
            type: 'warning'
        }).then(() => {
            this.doCancel(number);
        }).catch(() => {
            this.$message.info(this.i18n('取消作废操作'));
        });
    }

    // 作废兑换码处理逻辑
	doCancel(number: string) {
		const cancelRequest = new BRedemptionCodeCancel();
		cancelRequest.codes = [number];
		RedemptionCodeApi.cancel(cancelRequest).then(() => {
        this.$message.success(this.i18n(`作废兑换码成功`));
        this.fetchData();
		}).catch(() => {
			this.$message.error(this.i18n(`作废兑换码失败`));
		});
    }

	handleSelectionChange(selection: BRedemptionCode[]) {
        this.selectedCodes = selection;
    }

	batchCancel() {
		if (this.selectedCodes.length === 0) {
			this.$message.warning(this.i18n('请选择要作废的码值'));
			return;
		}
	
		const cancellableCodes = this.selectedCodes
			.filter(code => code.codeState === State.ISSUED)
			.map(code => code.code)
			.filter(code => code !== undefined && code !== null) as string[];
	
		if (cancellableCodes.length === 0) {
			this.$message.warning('没有可以作废的码值');
			return;
		}
	
		this.$confirm(this.i18n('确定要作废当前的码值吗？操作后无法恢复'), this.i18n('提示'), {
			confirmButtonText: this.i18n('确定'),
			cancelButtonText: this.i18n('取消'),
			type: 'warning'
		}).then(() => {
			const cancelRequest = new BRedemptionCodeCancel();
			cancelRequest.codes = cancellableCodes;
			RedemptionCodeApi.cancel(cancelRequest)
				.then(() => {
					this.$message.success(this.i18n('批量作废成功'));
					this.fetchData();
				})
				.catch(() => {
					this.$message.error(this.i18n('批量作废失败'));
				});
		}).catch(() => {
			this.$message.info(this.i18n('取消批量作废操作'));
		});
	}

	formatUpdateTime(row: BRedemptionCode): string {
        let date: Nullable<Date> = null;
        switch (row.codeState) {
			case State.NOT_STARTED:
				date = row.createTime;
                break;
            case State.ISSUED:
				date = row.createTime;
                break;
            case State.EXPIRE:
                date = row.createTime;
                break;
            case State.WRITTEN_OFF:
                date = row.useTime;
                break;
            case State.CANCELLED:
                date = row.abortTime;
                break;
        }
        return DateUtil.format(date, 'yyyy-MM-dd HH:mm:ss');
    }


    isShowCancel(row: any): boolean {
        // 判断是否显示作废按钮的逻辑，根据实际情况调整
        return row.executeState !== 'CANCELLED';
    }

	formatBenefits(gifts: BRedemptionCodeGift[]): string {
		return gifts.map(gift => `<p>${gift.idName?.name } * ${gift.qty}: ${gift.codes.join('; ')};</p>`).join('');
    }

	async exportData() {

		const loading = CommonUtil.Loading()
		try {
			const { code, msg } = await RedemptionCodeApi.export(this.exchangeCodeNumber)
			loading.close()
			if (code === 2000) {
				
				this.fileDialogVisible = true
			} else {
				this.$message.error(msg as string)
			}
			} catch (error) {
			loading.close()
			this.$message.error((error as Error).message)
			}
    }

	// 关闭文件中心弹框
	doDownloadDialogClose() {
		this.fileDialogVisible = false
	}
	
}