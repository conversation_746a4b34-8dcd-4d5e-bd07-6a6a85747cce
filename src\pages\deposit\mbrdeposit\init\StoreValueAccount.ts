/*
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:03
 * @LastEditTime: 2024-04-24 14:20:56
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\deposit\mbrdeposit\init\StoreValueAccount.ts
 * 记得注释
 */
import {Component, Provide, Vue} from 'vue-property-decorator'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import AccountBody from 'model/account/initial/AccountBody'
import AccountInitialApi from 'http/account/initial/AccountInitialApi'
import GoodsScopeDtl from 'cmp/goodsscope/GoodsScopeDtl.vue'
import IdName from 'model/common/IdName'
import ActiveStoreDtl from "cmp/activestoredtl/ActiveStoreDtl";
import I18nPage from "common/I18nDecorator";
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import BrowserMgr from "mgr/BrowserMgr";

@Component({
  name: 'StoreValueAccount',
  components: {
    SubHeader,
    GoodsScopeDtl,
    ActiveStoreDtl,
    BreadCrume
  }
})
@I18nPage({
  prefix: ['/储值/会员储值/会员储值账户管理/详情页面', '/公用/提示']
})
export default class StoreValueAccount extends Vue {
  goodsMatchRuleMode: string = "barcode"
  i18n: any

  data: AccountBody = new AccountBody()
  currentStores: IdName[] = []
  $refs: any
  panelArray: any = []

  @Provide('showAll')
	showAll: Boolean = true
  
  created() {
    let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
		if (sysConfig && this.goodsMatchRuleMode) {
			this.goodsMatchRuleMode = sysConfig.goodsMatchRuleMode;
		}
    this.panelArray = [
      {
        name: this.i18n('/公用/菜单/多储值账户'),
        url: ''
      }
    ]
    this.query()
  }

  edit() {
    this.$router.push({name: 'store-value-account-edit'})
  }

  query() {
    AccountInitialApi.list().then((resp: any) => {
      if (resp && resp.data) {
        this.data = resp.data
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  showStores(stores: IdName[]) {
    this.currentStores = stores
    this.$refs.storeDtl.dialogVisible = true
  }
}
