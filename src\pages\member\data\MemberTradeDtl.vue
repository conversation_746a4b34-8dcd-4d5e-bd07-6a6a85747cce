<template>
  <div>
    <BreadCrume :panelArray="panelArray">
    </BreadCrume>
    <div class="my-panel">
      <div class="panel-title">
        {{ i18n(isSale ? "交易单号" : "售后单号") }}：{{ dtl.tradeNo }}
      </div>
      <div class="member-asset-table">
        <template v-if="isSale">
        <div class="asset-cell">
          <div class="cell-label">{{ i18n("金额小计") }}</div>
          <div class="cell-number">{{ dtl.stdAmount | amount }}</div>
        </div>
        <div class="cell-divider"></div>
        </template>
        <div class="asset-cell">
          <div class="cell-label">{{ i18n(isSale ? "实付金额" : "售后金额") }}</div>
          <div class="cell-number">{{ dtl.total | amount }}</div>
        </div>
        <template v-if="isSale">
        <div class="cell-divider"></div>
        <div class="asset-cell">
          <div class="cell-label">{{ i18n("优惠金额") }}</div>
          <div class="cell-number">{{ dtl.favAmount | amount }}</div>
        </div>
        </template>
      </div>
      <div>
        <div class="my-form-item">
          <div class="my-form-label">{{ i18n("渠道") + "：" }}</div>
          <div class="my-form-content">{{ dtl.channelName | strFormat }}</div>
        </div>
        <div class="my-form-item">
          <div class="my-form-label">{{ i18n(isSale ? "交易时间" : "售后时间") + "：" }}</div>
          <div class="my-form-content">{{ dtl.tranTime | dateFormate3 }}</div>
        </div>
        <div class="my-form-item">
          <div class="my-form-label">{{ i18n("发生组织") + "：" }}</div>
          <div class="my-form-content">
            <template v-if="dtl.occurredOrgId">
            [{{ dtl.occurredOrgId }}]{{ dtl.occurredOrgName }}
            </template>
            <template v-else>--</template>
          </div>
        </div>
        <div class="my-form-item"
             v-if="isSale">
          <div class="my-form-label">{{ i18n("收银机号") + "：" }}</div>
          <div class="my-form-content">{{ dtl.posNo | strFormat }}</div>
        </div>
        <div class="my-form-item"
             v-if="!isSale">
          <div class="my-form-label">{{ i18n("关联原单") + "：" }}</div>
          <div class="my-form-content">
            <a href="javascript:void(0);"
               @click="onTradeNoClick(dtl.sourceTradeId)"
               v-if="dtl.sourceTradeId">
              {{ dtl.sourceTradeId.id }}
            </a>
            <template v-else>-</template>
          </div>
        </div>
      </div>
    </div>
    <div class="my-panel">
      <member-tab :tabs="tabs"
                  :current-index="currentTabIndex"
                  @change="onTabChange"
                  style="margin-bottom: 16px"
                  v-if="isSale"></member-tab>
      <div class="panel-title"
           v-else>
        {{ i18n("售后商品明细") }}
      </div>
      <el-table :data="dtl.payLines"
                style="width: 100%"
                v-if="currentTabIndex==0">
        <el-table-column :label="i18n('支付') + i18n('渠道')">
          <template slot-scope="scope">
          {{ scope.row.channelName | strFormat }}
          </template>
        </el-table-column>
        <el-table-column :label="i18n('支付金额')">
          <template slot-scope="scope">
          {{ scope.row.amount | amount }}
          </template>
        </el-table-column>
      </el-table>
      <el-table :data="dtl.goods"
                style="width: 100%"
                v-if="currentTabIndex==1">
        <el-table-column :label="i18n('商品')"
                         width="360"
                         key="1">
          <template slot-scope="scope">
          <div>{{ i18n("商品条码") }}：{{ scope.row.barcode | strFormat }}</div>
          <div>{{ i18n("商品名称") }}：{{ scope.row.name | strFormat }}</div>
          </template>
        </el-table-column>
        <el-table-column :label="i18n('规格')"
                         key="2">
          <template slot-scope="scope">
          {{ scope.row.qpcStr | strFormat }}
          </template>
        </el-table-column>
        <el-table-column :label="i18n('单价')"
                         key="3">
          <template slot-scope="scope">
          {{ scope.row.price | amount }}
          </template>
        </el-table-column>
        <el-table-column :label="i18n(isSale?'数量':'退款数量')"
                         key="4">
          <template slot-scope="scope">
          {{ scope.row.qty | strFormat }}
          </template>
        </el-table-column>
        <el-table-column :label="i18n(isSale?'金额':'退款金额')"
                         key="5">
          <template slot-scope="scope">
          {{ getTableAmount(scope.row) | amount }}
          </template>
        </el-table-column>
        <el-table-column :label="i18n('优惠金额')"
                         key="6"
                         v-if="isSale">
          <template slot-scope="scope">
          {{ scope.row.favAmount | amount }}
          </template>
        </el-table-column>
      </el-table>
      <el-table :data="dtl.favLines"
                style="width: 100%"
                v-if="currentTabIndex==2">
        <el-table-column :label="i18n('优惠商品')">
          <template slot-scope="scope">
          {{ scope.row.name | strFormat }}
          </template>
        </el-table-column>
        <el-table-column :label="i18n('优惠方式')">
          <template slot-scope="scope">
          {{ scope.row.favourType | strFormat }}
          </template>
        </el-table-column>
        <el-table-column :label="i18n('优惠金额')">
          <template slot-scope="scope">
          {{ scope.row.favAmount | amount }}
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script lang="ts"
        src="./MemberTradeDtl.ts">
</script>

<style lang="scss"
       scoped>
.my-panel {
  background: white;
  padding: 24px;
  border-radius: 8px;

  & + & {
    margin-top: 16px;
  }

  .panel-title {
    font-weight: 600;
    font-size: 20px;
    color: #242633;
    line-height: 28px;
    margin-bottom: 24px;
  }
}

.member-asset-table {
  height: 80px;
  background: #F8F9FC;
  border-radius: 4px;
  display: flex;
  align-items: center;
  margin-bottom: 24px;

  .asset-cell {
    width: 220px;
    padding: 0 16px;

    .cell-label {
      font-weight: 400;
      font-size: 14px;
      color: #79879E;
      margin-bottom: 6px;
    }

    .cell-number {
      font-weight: 600;
      font-size: 18px;
      color: #242633;
      line-height: 24px;
    }
  }

  .cell-divider {
    height: 48px;
    width: 1px;
    background: #D7DFEB;
  }
}

.my-form-item {
  display: flex;
  font-weight: 400;
  font-size: 13px;
  line-height: 20px;

  & + & {
    margin-top: 16px;
  }

  .my-form-label {
    width: 130px;
    color: #79879E;
  }

  .my-form-content {
    color: #242633;
  }
}
</style>
