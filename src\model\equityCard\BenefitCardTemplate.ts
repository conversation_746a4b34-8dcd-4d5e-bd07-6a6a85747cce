import BenefitCardTemplateBasicBenefit from 'model/equityCard/BenefitCardTemplateBasicBenefit'
import BenefitCardTemplateCardBenefit from 'model/equityCard/BenefitCardTemplateCardBenefit'
import BenefitCardTemplateCardNumRule from 'model/equityCard/BenefitCardTemplateCardNumRule'
import BenefitCardTemplateCardStyle from 'model/equityCard/BenefitCardTemplateCardStyle'
import BenefitCardTemplateCircleRule from 'model/equityCard/BenefitCardTemplateCircleRule'
import BenefitCardTemplateExpire from 'model/equityCard/BenefitCardTemplateExpire'
import BenefitCardTemplateGainRule from 'model/equityCard/BenefitCardTemplateGainRule'
import StandardEntity from 'model/equityCard/default/StandardEntity'
import StoreRange from 'model/equityCard/default/StoreRange'
import { enefitCardTemplatePayType } from 'model/equityCard/default/enefitCardTemplatePayType'
import { enefitCardTemplateStatus } from 'model/equityCard/default/enefitCardTemplateStatus'
import { enefitCardTemplateType } from 'model/equityCard/default/enefitCardTemplateType'

// 会员权益卡模版
export default class BenefitCardTemplate extends StandardEntity {
  uuid: Nullable<string> = null
  // 发卡营销中心
  marketingCenter: string = ''
  // 代码
  code: string = ''
  // 名称
  name: string = ''
  // 状态
  status: Nullable<enefitCardTemplateStatus> = null
  // 模版类型
  type: Nullable<enefitCardTemplateType> = null
  // 卡号生成规则
  codeRule: BenefitCardTemplateCardNumRule = new BenefitCardTemplateCardNumRule()
  // 用户能否可领卡，非付费会员模版使用，此时非空
  canAcquire: Nullable<boolean> = null
  // 门店范围，非付费会员模版使用，此时非空。
  storeRange: Nullable<StoreRange> = null
  // 支付类型，付费会员模版使用，此时非空
  payType: Nullable<enefitCardTemplatePayType> = null
  // 有效期定义。
  expire: BenefitCardTemplateExpire = new BenefitCardTemplateExpire()
  // 备注
  remark: Nullable<string> = null
  // 显示排序
  sort: Nullable<number> = null
  // 领卡奖励规则
  gainRule: Nullable<BenefitCardTemplateGainRule> = null
  // 卡样
  cardStyle: BenefitCardTemplateCardStyle = new BenefitCardTemplateCardStyle()
  // 周期赠礼规则
  circleRule: Nullable<BenefitCardTemplateCircleRule> = null
  // 基础权益
  basicBenefit: Nullable<BenefitCardTemplateBasicBenefit> = null
  // 权益说明
  cardBenefits: BenefitCardTemplateCardBenefit[] = []
  // 修改版本号
  revision: Nullable<number> = null
  // 是否为最新版本
  lastRevision: Nullable<boolean> = null
}