<template>
  <div class="member-card member-card-detail">
    <member-tab :tabs="tabs"
                :current-index="currentTabIndex"
                @change="onTabChange" style="margin-bottom: 8px"></member-tab>
    <div class="card-detail-container" v-if="cards && cards.length > 0">
      <member-detail-card v-for="item in cards"
                          :key="item.cardNo"
                          :card="item" :benefit="currentTabIndex==0"></member-detail-card>
      <el-pagination
        style="width: 100%"
        :current-page="page.currentPage"
        :page-size="page.size"
        :page-sizes="[10, 20, 30, 40]"
        :total="page.total"
        @current-change="onHandleCurrentChange"
        @size-change="onHandleSizeChange"
        background
        :layout="getPageLayout('total, prev, pager, next, sizes, jumper',page.probeEnabled)">
      </el-pagination>
    </div>
    <empty-data v-else></empty-data>
  </div>
</template>
<script lang="ts"
        src="./MemberCardDetail.ts">
</script>
<style lang="scss"
       scoped>
.member-card-detail {
  .card-detail-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 16px;
  }
}
</style>
