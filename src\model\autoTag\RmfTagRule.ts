import EventBehaviorRule from "./EventBehaviorRule"
import RFMUserType from "./RFMUserType"
import RMFCondition from "./RMFCondition"

export default class RmfTagRule {
  // 标签名称
  tagName: Nullable<string> = null
  // 事件行为规则
  eventBehaviorRule: Nullable<EventBehaviorRule> = null
  // r条件
  recencyCondition: Nullable<RMFCondition> = new RMFCondition()
  // F条件
  frequencyCondition: Nullable<RMFCondition> = new RMFCondition()
  // M条件
  monetaryCondition: Nullable<RMFCondition> = new RMFCondition()
  // 用户类型列表
  userTypes: RFMUserType[] = []
}