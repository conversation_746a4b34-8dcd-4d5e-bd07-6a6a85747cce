import IdName from "model/common/IdName"

export default class RecoverCardBill {
  // 回收单单号
  billNumber: Nullable<string> = null
  // 用卡开始时间
  useStartDate: Nullable<Date> = null
  // 用卡结束时间
  useEndDate: Nullable<Date> = null
  // 用卡门店
  useStore: Nullable<IdName> = null
  // 状态INITIAL：未审核；AUDITED：已审核；
  state: Nullable<string> = null
  // 营销中心
  marketingCenter: Nullable<string> = null
  // 差异数据
  differenceDate: Nullable<number> = null
  // 最后修改时间
  lastModified: Nullable<Date> = null
  // 是否弹窗提示
  prompt: Nullable<boolean> = null
  // 回收原因
  reason: Nullable<string> = null
  // 用卡开始时间
  useCardDateBegin: Nullable<Date> = null
  // 用卡结束时间
  useCardDateEnd: Nullable<Date> = null
}