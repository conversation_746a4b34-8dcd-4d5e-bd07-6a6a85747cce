import {Component, Prop, Vue, Watch} from 'vue-property-decorator'
import SelectGoodsRangeDialog from 'pages/deposit/mbrdeposit/active/dialog/SelectGoodsRangeDialog.vue'
import ImportDialog from 'cmp/importdialog/ImportDialog.vue'
import DateTimeRange from 'model/common/DateTimeRange'
import DateUtil from 'util/DateUtil'
import I18nPage from 'common/I18nDecorator'

@Component({
    name: 'TimeRange',
    components: {
        SelectGoodsRangeDialog,
        ImportDialog
    }
})
@I18nPage({
  prefix: [
    "/公用/券模板",
  ],
  auto: true
})
export default class TimeRange extends Vue {
    defaultValue = [DateUtil.nowDayTime(), new Date(DateUtil.nowDayTime().getTime() + 24 * 60 * 60 * 1000 - 1)]
    pre: any = ''
    date = []
    $refs: any
    ruleForm: any = {
        useDate: 'ALL',
        useCouponTime: 'day',
        day: [DateUtil.nowDayTime(), new Date(DateUtil.nowDayTime().getTime() + 24 * 60 * 60 * 1000 - 1)],
        month: [],
        week: [],
        weekWhich: [],
        whichDay: []
    }
    rules: any = {}

    @Prop()
    value: any
    @Prop({default: false})
    fixedAll: boolean

    @Watch('value')
    onDataChange(value: any) {
        if (value) {
            this.setTimeParams(JSON.parse(JSON.stringify(value)))
            if (JSON.stringify(this.pre) !== JSON.stringify(value)) {
                this.$emit('input', this.doTransParams())
                this.$emit('change')
            }
            this.pre = value
            this.setTimeParams(value)
        }
    }
    created() {
        this.rules = {
            day: [
                { required: true, validator: (rule: any, value: any, callback: any) => {
                    if (this.ruleForm.useDate === 'ALL') {
                        callback()
                    } else if (this.ruleForm.useDate === 'USEABLE') {
                        if (this.ruleForm.useCouponTime === 'day') {
                            if (value && value.length > 0) {
                                callback()
                            } else {
                                callback(this.formatI18n('/公用/券模板', '请输入必填项'))
                            }
                        } else if (this.ruleForm.useCouponTime === 'month') {
                            callback()
                        } else {
                            callback()
                        }
                    } else {
                        if (this.ruleForm.useCouponTime === 'day') {
                            if (value && value.length > 0) {
                                callback()
                            } else {
                                callback(this.formatI18n('/公用/券模板', '请输入必填项'))
                            }
                        } else if (this.ruleForm.useCouponTime === 'month') {
                            callback()
                        } else {
                            callback()
                        }
                    }
                    }, trigger: 'blur'}
            ],
            week: [
                {
                    required: true, validator: (rule: any, value: any, callback: any) => {
                        if (this.ruleForm.useDate === 'ALL') {
                            callback()
                        } else if (this.ruleForm.useDate === 'USEABLE') {
                            if (this.ruleForm.useCouponTime === 'week') {
                                if (value && value.length > 0) {
                                    callback()
                                } else {
                                    callback(this.formatI18n('/公用/券模板', '请输入必填项'))
                                }
                            } else if (this.ruleForm.useCouponTime === 'month') {
                                callback()
                            } else {
                                callback()
                            }
                        } else {
                            if (this.ruleForm.useCouponTime === 'week') {
                                if (value && value.length > 0) {
                                    callback()
                                } else {
                                    callback(this.formatI18n('/公用/券模板', '请输入必填项'))
                                }
                            } else if (this.ruleForm.useCouponTime === 'month') {
                                callback()
                            } else {
                                callback()
                            }
                        }
                    }, trigger: 'blur'},
            ],
            weekWhich: [ // 每周
                {
                    required: true, validator: (rule: any, value: any, callback: any) => {
                        if (this.ruleForm.useDate === 'ALL') {
                            callback()
                        } else if (this.ruleForm.useDate === 'USEABLE') {
                            if (this.ruleForm.useCouponTime === 'week') {
                                if (value && value.length > 0) {
                                    callback()
                                } else {
                                    callback(this.formatI18n('/公用/券模板', '请输入必填项'))
                                }
                            } else if (this.ruleForm.useCouponTime === 'day') {
                                callback()
                            } else {
                                callback()
                            }
                        } else {
                            if (this.ruleForm.useCouponTime === 'week') {
                                if (value && value.length > 0) {
                                    callback()
                                } else {
                                    callback(this.formatI18n('/公用/券模板', '请输入必填项'))
                                }
                            } else if (this.ruleForm.useCouponTime === 'day') {
                                callback()
                            } else {
                                callback()
                            }
                        }
                    }, trigger: 'change'
                }
            ],
            month: [
                {required: true, validator: (rule: any, value: any, callback: any) => {
                        if (this.ruleForm.useDate === 'ALL') {
                            callback()
                        } else if (this.ruleForm.useDate === 'USEABLE') {
                            if (this.ruleForm.useCouponTime === 'month') {
                                if (value && value.length > 0) {
                                    callback()
                                } else {
                                    callback(this.formatI18n('/公用/券模板', '请输入必填项'))
                                }
                            } else if (this.ruleForm.useCouponTime === 'day') {
                                callback()
                            } else {
                                callback()
                            }
                        } else {
                            if (this.ruleForm.useCouponTime === 'month') {
                                if (value && value.length > 0) {
                                    callback()
                                } else {
                                    callback(this.formatI18n('/公用/券模板', '请输入必填项'))
                                }
                            } else if (this.ruleForm.useCouponTime === 'day') {
                                callback()
                            } else {
                                callback()
                            }
                        }
                    }, trigger: 'blur'},
            ],
            whichDay: [ // 每月
                { required: true, validator: (rule: any, value: any, callback: any) => {
                        if (this.ruleForm.useDate === 'ALL') {
                            callback()
                        } else if (this.ruleForm.useDate === 'USEABLE') {
                            if (this.ruleForm.useCouponTime === 'month') {
                                if (value && value.length > 0) {
                                    callback()
                                } else {
                                    callback(this.formatI18n('/公用/券模板', '请输入必填项'))
                                }
                            } else if (this.ruleForm.useCouponTime === 'day') {
                                callback()
                            } else {
                                callback()
                            }
                        } else {
                            if (this.ruleForm.useCouponTime === 'month') {
                                if (value && value.length > 0) {
                                    callback()
                                } else {
                                    callback(this.formatI18n('/公用/券模板', '请输入必填项'))
                                }
                            } else if (this.ruleForm.useCouponTime === 'day') {
                                callback()
                            } else {
                                callback()
                            }
                        }
                    }, trigger: 'blur'},
            ]
        }
        if (this.value) {
            this.setTimeParams(this.value)
        }
    }
    doUseDateChange() {
        this.resetFormState(this.ruleForm.useDate)
    }
    doEmitParams() {
        // if (!this.ruleForm.day || this.ruleForm.day.length <= 0) {
        //     this.ruleForm.day = [DateUtil.nowDayTime(), new Date(DateUtil.nowDayTime().getTime() + 24 * 60 * 60 * 1000 - 1)]
        // }
        this.$emit('input', this.doTransParams())
        this.$emit('change')
    }
    resetFormState(str: string) {
        this.ruleForm.day = [DateUtil.nowDayTime(), new Date(DateUtil.nowDayTime().getTime() + 24 * 60 * 60 * 1000 - 1)]
        this.ruleForm.week = [DateUtil.nowDayTime(), new Date(DateUtil.nowDayTime().getTime() + 24 * 60 * 60 * 1000 - 1)]
        this.ruleForm.month = [DateUtil.nowDayTime(), new Date(DateUtil.nowDayTime().getTime() + 24 * 60 * 60 * 1000 - 1)]
        this.ruleForm.weekWhich = []
        this.ruleForm.whichDay = []
        if (str === 'ALL') {
           // todo
        } else if (str === 'USEABLE') {
            this.ruleForm.useCouponTime = 'day'
        } else {
            this.ruleForm.useCouponTime = 'day'
        }
        this.$emit('input', this.doTransParams())
        this.$emit('change')
    }
    doUseCouponTime() {
        this.$refs['ruleForm'].resetFields()
        if (this.ruleForm.useCouponTime === 'day') {
            this.ruleForm.whichDay = []
            this.ruleForm.weekWhich = []
            this.ruleForm.day =  [DateUtil.nowDayTime(), new Date(DateUtil.nowDayTime().getTime() + 24 * 60 * 60 * 1000 - 1)]
            this.ruleForm.month = []
            this.ruleForm.week = []
            this.$emit('input', this.doTransParams())
            this.$emit('change')
        } else if (this.ruleForm.useCouponTime === 'week') {
            this.ruleForm.whichDay = []
            this.ruleForm.weekWhich = []
            this.ruleForm.day = []
            this.ruleForm.month = []
            this.ruleForm.week =  [DateUtil.nowDayTime(), new Date(DateUtil.nowDayTime().getTime() + 24 * 60 * 60 * 1000 - 1)]
        } else {
            this.ruleForm.whichDay = []
            this.ruleForm.weekWhich = []
            this.ruleForm.day = []
            this.ruleForm.week = []
            this.ruleForm.month =  [DateUtil.nowDayTime(), new Date(DateUtil.nowDayTime().getTime() + 24 * 60 * 60 * 1000 - 1)]
        }
    }
    doValidate() {
        if (this.$refs.ruleForm) {
            let p0 = new Promise((resolve, reject) => {
                this.$refs.ruleForm.validate((valid: any) => {
                    if (valid) {
                        resolve()
                    }
                })
            })
            return p0
        } else {
            let p0 = new Promise((resolve, reject) => {
                resolve()
            })
            return p0
        }
    }
    doTransParams() {
        if (this.ruleForm.useDate === 'ALL') {
            let params: DateTimeRange = new DateTimeRange()
            params.dateTimeRangeType = 'ALL' as any
            params.cycleType = null
            params.beginTime = null
            params.endTime = null
            params.days = []
            return params
        } else if (this.ruleForm.useDate === 'USEABLE') {
            let params: DateTimeRange = new DateTimeRange()
            if (this.ruleForm.useCouponTime === 'day') {
                params.dateTimeRangeType = 'USEABLE' as any
                params.cycleType = 'DAYS' as any
                if (Object.prototype.toString.call(this.ruleForm.day[0]) === '[object Date]') {
                    let pre = DateUtil.format(this.ruleForm.day[0], 'yyyy-MM-dd HH:mm:ss')
                    let next = DateUtil.format(this.ruleForm.day[1], 'yyyy-MM-dd HH:mm:ss')
                    this.ruleForm.day[0] = pre.substring(11, 16)
                    this.ruleForm.day[1] = next.substring(11, 16)
                }
                params.beginTime = this.ruleForm.day[0]
                params.endTime = this.ruleForm.day[1]
                params.days = []
            } else if (this.ruleForm.useCouponTime === 'week') {
                params.dateTimeRangeType = 'USEABLE' as any
                params.cycleType = 'WEEKS' as any
                if (Object.prototype.toString.call(this.ruleForm.week[0]) === '[object Date]') {
                    let pre = DateUtil.format(this.ruleForm.week[0], 'yyyy-MM-dd HH:mm:ss')
                    let next = DateUtil.format(this.ruleForm.week[1], 'yyyy-MM-dd HH:mm:ss')
                    this.ruleForm.week[0] = pre.substring(11, 16)
                    this.ruleForm.week[1] = next.substring(11, 16)
                }
                params.beginTime = this.ruleForm.week[0]
                params.endTime = this.ruleForm.week[1]
                params.days = this.ruleForm.weekWhich
            } else {
                params.dateTimeRangeType = 'USEABLE' as any
                params.cycleType = 'MONTHS' as any
                if (Object.prototype.toString.call(this.ruleForm.month[0]) === '[object Date]') {
                    let pre = DateUtil.format(this.ruleForm.month[0], 'yyyy-MM-dd HH:mm:ss')
                    let next = DateUtil.format(this.ruleForm.month[1], 'yyyy-MM-dd HH:mm:ss')
                    this.ruleForm.month[0] = pre.substring(11, 16)
                    this.ruleForm.month[1] = next.substring(11, 16)
                }
                params.beginTime = this.ruleForm.month[0]
                params.endTime = this.ruleForm.month[1]
                params.days = this.ruleForm.whichDay
            }
            return params
        } else {
            let params: DateTimeRange = new DateTimeRange()
            if (this.ruleForm.useCouponTime === 'day') {
                params.dateTimeRangeType = 'UNUSEABLE' as any
                params.cycleType = 'DAYS' as any
                if (Object.prototype.toString.call(this.ruleForm.day[0]) === '[object Date]') {
                    let pre = DateUtil.format(this.ruleForm.day[0], 'yyyy-MM-dd HH:mm:ss')
                    let next = DateUtil.format(this.ruleForm.day[1], 'yyyy-MM-dd HH:mm:ss')
                    this.ruleForm.day[0] = pre.substring(11, 16)
                    this.ruleForm.day[1] = next.substring(11, 16)
                }
                params.beginTime = this.ruleForm.day[0]
                params.endTime = this.ruleForm.day[1]
                params.days = []
            } else if (this.ruleForm.useCouponTime === 'week') {
                params.dateTimeRangeType = 'UNUSEABLE' as any
                params.cycleType = 'WEEKS' as any
                if (Object.prototype.toString.call(this.ruleForm.week[0]) === '[object Date]') {
                    let pre = DateUtil.format(this.ruleForm.week[0], 'yyyy-MM-dd HH:mm:ss')
                    let next = DateUtil.format(this.ruleForm.week[1], 'yyyy-MM-dd HH:mm:ss')
                    this.ruleForm.week[0] = pre.substring(11, 16)
                    this.ruleForm.week[1] = next.substring(11, 16)
                }
                params.beginTime = this.ruleForm.week[0]
                params.endTime = this.ruleForm.week[1]
                params.days = this.ruleForm.weekWhich
            } else {
                params.dateTimeRangeType = 'UNUSEABLE' as any
                params.cycleType = 'MONTHS' as any
                if (Object.prototype.toString.call(this.ruleForm.month[0]) === '[object Date]') {
                    let pre = DateUtil.format(this.ruleForm.month[0], 'yyyy-MM-dd HH:mm:ss')
                    let next = DateUtil.format(this.ruleForm.month[1], 'yyyy-MM-dd HH:mm:ss')
                    this.ruleForm.month[0] = pre.substring(11, 16)
                    this.ruleForm.month[1] = next.substring(11, 16)
                }
                params.beginTime = this.ruleForm.month[0]
                params.endTime = this.ruleForm.month[1]
                params.days = this.ruleForm.whichDay
            }
            return params
        }
    }
    private setTimeParams(data: any) {
        if (data && data.cycleType) {
            this.ruleForm.useDate = data.dateTimeRangeType
            this.ruleForm.useCouponTime = data.cycleType === 'DAYS' ? 'day' : (data.cycleType === 'MONTHS' ? 'month' : 'week')
            if (this.ruleForm.useCouponTime === 'day') {
                if (!data.beginTime) {
                    this.ruleForm.day =  [DateUtil.nowDayTime(), new Date(DateUtil.nowDayTime().getTime() + 24 * 60 * 60 * 1000 - 1)]
                } else {
                    this.ruleForm.day = [data.beginTime, data.endTime] as any
                }
            } else if (this.ruleForm.useCouponTime === 'month') {
                this.ruleForm.day = []
                this.ruleForm.whichDay = data.days
                if (!data.beginTime) {
                    this.ruleForm.month =  [DateUtil.nowDayTime(), new Date(DateUtil.nowDayTime().getTime() + 24 * 60 * 60 * 1000 - 1)]
                } else {
                    this.ruleForm.month = [data.beginTime, data.endTime] as any
                }
            } else if (this.ruleForm.useCouponTime === 'week') {
                this.ruleForm.day = []
                if (data.days && data.days.length > 0) {
                    this.ruleForm.weekWhich = data.days
                } else {
                    this.ruleForm.weekWhich = [] as any
                }
                if (!data.beginTime) {
                    this.ruleForm.week =  [DateUtil.nowDayTime(), new Date(DateUtil.nowDayTime().getTime() + 24 * 60 * 60 * 1000 - 1)]
                } else {
                    this.ruleForm.week = [data.beginTime, data.endTime] as any
                }
            } else {
                this.ruleForm.day = [] as any
            }
        } else {
            this.ruleForm.useDate = 'ALL'
            // this.ruleForm.useCouponTime = 'day'
            // this.$refs['ruleForm'].resetFields()
            // this.ruleForm.day = [] as any
        }
        // this.$forceUpdate()

    }
}
