/*
 * @Author: 黎钰龙
 * @Date: 2023-06-30 15:58:12
 * @LastEditTime: 2025-01-13 17:34:53
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\couponTemplateDtlItem\useCouponGoods\UseCouponGoods.ts
 * 记得注释
 */
import FormItem from 'cmp/formitem/FormItem';
import GoodsScopeDtl from 'cmp/goodsscope/GoodsScopeDtl';
import I18nPage from 'common/I18nDecorator';
import CouponItem from 'model/common/CouponItem';
import PickUpGoods from 'model/common/PickUpGoods';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
@Component({
  name: 'UseCouponGoods',
  components: {
    GoodsScopeDtl,
    FormItem
  },
})
@I18nPage({
  prefix: [
    '/公用/券模板',
  ],
  auto: true
})
export default class UseCouponGoods extends Vue {
  @Prop()
  data: CouponItem;

  @Watch('data', {immediate:  true })
  handle(value:CouponItem) {
    const pickUpGoodsGroups = value.coupons?.exchangeGoodsCouponAttribute?.pickUpGoodsGroups[0]
    this.currentData = pickUpGoodsGroups?.exchangeGoods.slice(this.page.currentPage - 1,this.page.pageSize) || []
  }

  currentGoodsTab: number = 0 //当前选中哪一组用券商品

  page:any = {
		currentPage: 1,
		pageSize: 10
	}
  currentData: PickUpGoods[] = []

  //是否为提货券
  get isGoodsCoupon() {
    return this.data && this.data.coupons && this.data.coupons.couponBasicType && this.data.coupons.couponBasicType === 'goods'
  }

  get isSomeCoupon() {
    return this.data && this.data.coupons && this.data.coupons.couponBasicType && ['freight', 'goods', 'exchange_goods', 'points'].indexOf(this.data.coupons.couponBasicType) === -1
  }
  //是否为兑换券
  get isExchangeCoupon() {
    return this.data && this.data.coupons && this.data.coupons.couponBasicType && this.data.coupons.couponBasicType === 'exchange_goods'
  }

  get hasUseGoods() {
    return Number(this.data.coupons?.exchangeGoodsCouponAttribute?.pickUpGoodsGroups.length) > 0
  }

  // 是否为增值商品
  get isAppreciationGoods() {
    return this.data.coupons && this.data.coupons.appreciationGoods
  }

  get getAllAmount() {
    let num: number = 0;
    if (
      this.data.coupons != null &&
      this.data.coupons.pickUpCouponAttribute != null &&
      this.data.coupons.pickUpCouponAttribute.pickUpGoods != null &&
      this.data.coupons.enablePayApportion == true
    ) {
      for (
        let i = 0;
        i < this.data.coupons.pickUpCouponAttribute.pickUpGoods.length;
        i++
      ) {
        const item = this.data.coupons.pickUpCouponAttribute.pickUpGoods[i];
        if (item.bookPayPrice != null && item.qty != null) {
          num += item.bookPayPrice * item.qty;
        }
      }
    }
    // this.AllAmount = num
    return num.toFixed(2);
  }

  get isGiftGoods() {
    if (this.getGiftGoods()?.length) {
      return this.getGiftGoods()[0]?.goods
    } else {
      return false
    }
  }

  get isGiftCategory() {
    if (this.getGiftGoods()?.length) {
      return this.getGiftGoods()[0]?.category
    } else {
      return false
    }
  }

  // 是否为品类商品
  isCategory(data: PickUpGoods[]) {
    if (data?.length) {
      return data[0].category
    } else {
      return false
    }
  }

  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
		// console.log('目标长度',this.page.currentPage * this.page.pageSize,(this.page.currentPage + 1) * this.page.pageSize);
    this.updateCurrentData()
  }

  handleClick() {
    this.page.currentPage = 1
    this.updateCurrentData()
  }

  updateCurrentData() {
		this.currentData = this.data.coupons?.exchangeGoodsCouponAttribute?.pickUpGoodsGroups[this.currentGoodsTab].exchangeGoods.slice((this.page.currentPage - 1) * this.page.pageSize,this.page.currentPage * this.page.pageSize) || []
  }

  getGiftGoods() {
    if (this.data.coupons!.exchangeGoodsCouponAttribute) {
      if (this.data.coupons!.exchangeGoodsCouponAttribute.giftGoodsGroups.length > 0) {
        return this.data.coupons!.exchangeGoodsCouponAttribute.giftGoodsGroups[0].exchangeGoods;
      }
    }
    return []
  }
};