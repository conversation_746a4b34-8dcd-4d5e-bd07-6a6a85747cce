/*
 * @Author: 黎钰龙
 * @Date: 2024-01-24 14:07:06
 * @LastEditTime: 2025-05-12 11:25:10
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\v2\coupon\improveProfiles\ImproveProfilesGiftActivity.ts
 * 记得注释
 */
import BaseCouponActivity from 'model/coupon/activity/BaseCouponActivity'
import CouponItem from 'model/common/CouponItem'
import PushGroup from 'model/precisionmarketing/pushplan/PushGroup'
import { ProfileType } from 'model/default/ProfileType'
import { MemberInfoFieldName } from 'model/systemConfig/MemberInfoFieldName'

export default class ImproveProfilesGiftActivity extends BaseCouponActivity {
  // 需完善资料(系统定义)
  needImprove: MemberInfoFieldName[] = []
  // 需完善资料(自定义)
  needImproveOfCustom: string[] = []
  // 适用客群
  groups: PushGroup[] = []
  // 赠送积分
  points: Nullable<number> = null
  // 赠送券
  coupons: CouponItem[] = []
  // 赠送成长值
  growthValue: Nullable<number> = null
  // 参与人群
  rule: Nullable<PushGroup> = null
}