/*
 * @Author: 黎钰龙
 * @Date: 2023-11-02 15:05:58
 * @LastEditTime: 2023-12-01 10:27:33
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\selectStores\SelectStores.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import OrgApi from 'http/org/OrgApi';
import IdName from 'model/common/IdName';
import RSOrg from 'model/common/RSOrg';
import RSOrgFilter from 'model/common/RSOrgFilter';
import { Component, Model, Prop, Vue, Watch } from 'vue-property-decorator';
@Component({
  name: 'SelectStores',
  components: {}
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/公用/下拉框/提示'
  ],
  auto: true
})
export default class SelectStores extends Vue {
  @Model('change') selectStoreIdName: IdName | string
  @Prop({ type: String, default: '400px' }) width: string;
  @Prop() placeholder: any;
  @Prop({ type: Boolean, default: false }) hideAll: boolean; //是否隐藏“全部”选项
  @Prop({ type: Boolean, default: true}) isOnlyId: boolean; //是否只绑定id
  @Prop({ type: Boolean,  default:false}) disabled: boolean;
  @Prop({ default: () => { return {} } }) appendAttr: any; //查询门店时，需要追加的参数
  selectLoading: boolean = false
  stores: RSOrg[] = []

  @Watch('selectStore', { deep: true })
  handle(value:any) {
    if(!value) {
      this.getStore('')
    }
  }

  get selectStore() {
    return this.selectStoreIdName
  }
  set selectStore(value: any) {
    const res = value ?? null
    this.$emit('change', res)
  }

  created() {
    this.getStore()
  }
  doRemoteMethod(value: string) {
    this.getStore(value);
  }
  getStore(value?: string) {
    let params: RSOrgFilter = new RSOrgFilter();
    params.idNameLikes = value ?? null;
    params.page = 0;
    params.pageSize = 0;
    params = { ...params, ...this.appendAttr }
    this.selectLoading = true
    OrgApi.query(params)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.stores = resp.data;
          this.getAddDefaultOrg();
        } else {
          throw new Error(resp.msg || this.i18n('查询门店列表失败'))
        }
      })
      .catch((error) => {
        this.$message.error(error.message || this.i18n('内部异常'));
      }).finally(() => this.selectLoading = false)
  }

  getAddDefaultOrg(){
    OrgApi.getDefaultOrg().then((resp: any) => {
      if (resp && resp.code === 2000) {
         var org = new RSOrg()
         org.org.id = resp.data.org.id;
         org.org.name = resp.data.org.name;
         this.stores.push(org);
     
      }
     })
     .catch((error: any) => {
       if (error && error.message) {
         this.$message.error(error.message);
       }
     });
    }

};