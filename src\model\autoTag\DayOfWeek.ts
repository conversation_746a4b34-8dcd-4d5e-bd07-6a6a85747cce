import ConstantMgr from "mgr/ConstantMgr"

export enum DayOfWeek {
  // 
  MONDAY = 'MONDAY',
  // 
  TUESDAY = 'TUESDAY',
  // 
  WEDNESDAY = 'WEDNESDAY',
  // 
  THURSDAY = 'THURSDAY',
  // 
  FRIDAY = 'FRIDAY',
  // 
  SATURDAY = 'SATURDAY',
  // 
  SUNDAY = 'SUNDAY'
}

// 
export class GetWeekUtil {
  static getWeekLabel(value: DayOfWeek) {
    switch (value) {
      case DayOfWeek.MONDAY:
        return new ConstantMgr.MenusFuc().format("/公用/券模板", "周一")
      case DayOfWeek.TUESDAY:
        return new ConstantMgr.MenusFuc().format("/公用/券模板", "周二")
      case DayOfWeek.WEDNESDAY:
        return new ConstantMgr.MenusFuc().format("/公用/券模板", "周三")
      case DayOfWeek.THURSDAY:
        return new ConstantMgr.MenusFuc().format("/公用/券模板", "周四")
      case DayOfWeek.FRIDAY:
        return new ConstantMgr.MenusFuc().format("/公用/券模板", "周五")
      case DayOfWeek.SATURDAY:
        return new ConstantMgr.MenusFuc().format("/公用/券模板", "周六")
      case DayOfWeek.SUNDAY:
        return new ConstantMgr.MenusFuc().format("/公用/券模板", "周日")
      default:
        break;
    }
  }
  static getWeekOption() {
    return [
      {
        label: new ConstantMgr.MenusFuc().format("/公用/券模板", "周一"),
        value: DayOfWeek.MONDAY
      },
      {
        label: new ConstantMgr.MenusFuc().format("/公用/券模板", "周二"),
        value: DayOfWeek.TUESDAY
      },
      {
        label: new ConstantMgr.MenusFuc().format("/公用/券模板", "周三"),
        value: DayOfWeek.WEDNESDAY
      },
      {
        label: new ConstantMgr.MenusFuc().format("/公用/券模板", "周四"),
        value: DayOfWeek.THURSDAY
      },
      {
        label: new ConstantMgr.MenusFuc().format("/公用/券模板", "周五"),
        value: DayOfWeek.FRIDAY
      },
      {
        label: new ConstantMgr.MenusFuc().format("/公用/券模板", "周六"),
        value: DayOfWeek.SATURDAY
      },
      {
        label: new ConstantMgr.MenusFuc().format("/公用/券模板", "周日"),
        value: DayOfWeek.SUNDAY
      },
    ]
  }
}