<!--
 * @Author: l<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-11-25 11:42:35
 * @LastEditors: 黎钰龙
 * @LastEditTime: 2024-04-28 20:05:37
 * @FilePath: \phoenix-web-ui\src\cmp\selectordialogs\CouponAppletDialog.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-dialog :title="i18n('/券/延期申请/小程序领微信券')" class="select-store-dialog" append-to-body width="80%" :close-on-click-modal="false" :visible.sync="dialogShow">
    <div class="wrap" style="height: 550px; overflow: hidden;">
      <el-form label-width="120px">
        <el-row class="query">
          <el-col :span="6">
            <el-form-item :label="i18n('活动名称')" :title="i18n('活动名称')">
              <el-input v-model="couponActivityFilter.nameLike" :placeholder="i18n('类似于')" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="i18n('活动号')" :title="i18n('活动号')">
              <el-input v-model="couponActivityFilter.numberLike" :placeholder="i18n('等于')" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="formatI18n('/公用/公共组件/积分活动选择弹框组件/查询/活动状态')">
              <el-select v-model="couponActivityFilter.stateEquals" style="width: 100%" :disabled="true">
                <el-option :label="formatI18n('/公用/过滤器/进行中')" value="PROCESSING">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item>
              <el-button type="primary" @click="doSearch()">{{ i18n('/公用/按钮/查询') }}</el-button>
              <el-button @click="doReset()">{{ i18n('/公用/按钮/重置') }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-table :height="490" :data="currentList" ref="table" @selection-change="handleSelectionChange" class="coupon-table" :header-cell-class-name="cellClass"
        :row-key="getRowKey">
        <el-table-column type="selection" width="60" :selectable="selectChange" :reserve-selection="true"> </el-table-column>
        <el-table-column :label="formatI18n('/营销/券礼包活动/券礼包活动', '活动名称')" prop="activityId" min-width="120">
          <template slot-scope="scope">
            <div :title="scope.row.name" @click="doToDtl(scope.row)" class="activity-name">
              {{ scope.row.name | strFormat }}
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="formatI18n('/营销/券礼包活动/券礼包活动', '活动号')" min-width="120">
          <template slot-scope="scope">
            <div>{{ scope.row.activityId | strFormat }}</div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="page" style="margin-top: 15px">
      <el-pagination :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
        @current-change="handleCurrentChange($event)" @size-change="handleSizeChange($event)" background
        layout="total, prev, pager, next, sizes,  jumper">
      </el-pagination>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="dialogShow = false">{{ i18n('/公用/按钮/取消') }}</el-button>
      <el-button size="small" type="primary" @click="doModalClose()">{{ i18n('/公用/按钮/确定') }}</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./CouponAppletDialog.ts"/>

<style lang="scss" scoped>
.select-store-dialog {
  display: flex;
  align-items: center;
  justify-content: center;
  @import "SelectorDialogCommon";

  ::v-deep .el-dialog {
    width: 1024px;
    height: 750px;
  }

  ::v-deep .el-table .disabledCheck .cell .el-checkbox__inner {
    display: none;
  }

  ::v-deep .el-table .disabledCheck .cell::before {
    content: "";
    text-align: center;
    line-height: 37px;
  }
  .coupon-table {
    width: 100%;
    margin-top: 10px;
    padding: 0 30px;
    font-size: 14px;

    .activity-name {
      color: #20a9ff;
      text-align: left;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      padding: 10px;
      padding-left: 0;
      cursor: pointer;
    }
  }
}
</style>