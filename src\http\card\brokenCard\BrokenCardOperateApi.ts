/*
 * @Author: 黎钰龙
 * @Date: 2024-10-24 10:47:47
 * @LastEditTime: 2024-10-24 10:49:42
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\card\brokenCard\BrokenCardOperateApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import BrokenCardLog from 'model/brokenCard/BrokenCardLog'
import BrokenCardLogFilter from 'model/brokenCard/BrokenCardLogFilter'
import Response from 'model/common/Response'
import PrePayCard from 'model/prepay/card/PrePayCard'


export default class BrokenCardOperateApi {
  /**
   * 查询坏卡
   * 查询坏卡。
   * 
   */
  static getBroken(cardCode: string): Promise<Response<PrePayCard[]>> {
    return ApiClient.server().get(`/v1/broken-card-operate/getBroken`, {
      params: {
        cardCode: cardCode
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分页坏卡重制操作日志
   * 分页坏卡重制操作日志。
   * 
   */
  static queryLog(body: BrokenCardLogFilter): Promise<Response<BrokenCardLog[]>> {
    return ApiClient.server().post(`/v1/broken-card-operate/queryLog`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 坏卡重制
   * 坏卡重制
   * 
   */
  static remakeCard(code: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/broken-card-operate/remakeCard/${code}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

}
