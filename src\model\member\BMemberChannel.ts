import BMemberChannelIdentity from "model/member/BMemberChannelIdentity";

export default class BMemberChannel {
  // 渠道名
  channel: Nullable<string> = null
  // 身份
  identityList: BMemberChannelIdentity[] = []
  // 昵称
  nickName: Nullable<string> = null
  // 时间
  date: Nullable<Date> = null
  // 渠道类型：weiXin，aliPay，weimob，douYin，qiWei，youZan
  channelType: Nullable<string> = null
  // 渠道id
  channelId: Nullable<string> = null

}
