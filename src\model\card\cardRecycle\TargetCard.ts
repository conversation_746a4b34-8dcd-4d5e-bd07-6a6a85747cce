/*
 * @Author: 黎钰龙
 * @Date: 2024-04-03 10:46:30
 * @LastEditTime: 2024-04-07 18:30:21
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\card\cardRecycle\TargetCard.ts
 * 记得注释
 */
import IdName from "model/common/IdName";

export default class TargetCard {
  // 卡号
  cardCode: Nullable<string> = null
  // 卡类型
  cardType: Nullable<string> = null
  // 最近一次核销门店
  useOrg: Nullable<IdName> = null
  // 备注
  remark: Nullable<string> = null
}