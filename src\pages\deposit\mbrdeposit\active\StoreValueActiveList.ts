import {Component, Vue} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import ListWrapper from 'cmp/list/ListWrapper.vue'
import SubHeader from 'cmp/subheader/SubHeader.vue'
import DepositActivityApi from 'http/deposit/activity/DepositActivityApi'
import DepositActivityFilter from 'model/deposit/activity/DepositActivityFilter'
import I18nPage from 'common/I18nDecorator'
import ConstantMgr from 'mgr/ConstantMgr'
import ExportConfirm from "cmp/exportconfirm/ExportConfirm"
import BreadCrume from 'cmp/bread-crumb/BreadCrume.vue'
import ChannelManagementFilter from 'model/channel/ChannelManagementFilter'
import { ChannelState } from 'model/channel/ChannelState'
import ChannelManagementApi from 'http/channelmanagement/ChannelManagementApi'
import ChannelManagement from 'model/channel/ChannelManagement'
import ActivityMgr from 'mgr/ActivityMgr'
import { MarketBudgetActivityEnum } from 'model/promotion/MarketBudgetActivityEnum'
import AbstractOaActivity from 'cmp/abstract-oa-activity/AbstractOaActivity'
import CommonUtil from 'util/CommonUtil'
import ActivityStateTag from 'cmp/activity-state-tag/ActivityStateTag'

@Component({
  name: 'StoreValueActiveList',
  components: {
    FormItem,
    ListWrapper,
    SubHeader,
    ExportConfirm,
    BreadCrume,
    ActivityStateTag
  }
})
@I18nPage({
  prefix: ['/储值/会员储值/储值充值活动/列表页面','/营销/券礼包活动/券礼包活动', '/公用/按钮'],
})
export default class StoreValueActiveList extends AbstractOaActivity {
  recordId = ''
  exportDialogShow = false
  warnMsg = ''
  i18n: any
  query: DepositActivityFilter = new DepositActivityFilter()
  selectedArr: any[] = []
  activeName = 'first'
  singleAll = false
  $refs: any
  // 分页
  page = {
    currentPage: 1,
    total: 0,
    size: 10
  }
  tableData: any[] = []
  dialogShow = false
  panelArray: any = []
  channels: ChannelManagement[] = []
  channelMap: Map<string, ChannelManagement> = new Map<string,  ChannelManagement>();

  get isOaActivity() {
    return ActivityMgr.isOaActivity(MarketBudgetActivityEnum.PrepayDepositActivityRule)
  }

  created() {
    this.panelArray = [
      {
        name: this.formatI18n('/公用/菜单/充值有礼'),
        url: ''
      }
    ]
    this.getChannels().then(()=>{
      this.getStoreValueList()
    })
    
  }

  getChannels() {
    let filter = new ChannelManagementFilter();
    filter.stateEquals = ChannelState.ENABLED
    return ChannelManagementApi.query(filter).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.channels = resp.data
          this.channels = this.channels.filter((channel: any)=> !(channel.channel.type === 'store' && channel.channel.id === '-'))
        for (let channel of this.channels) {
          if (channel.channel && channel.channel.type && channel.channel.id) {
            this.channelMap.set(channel.channel.type + channel.channel.id, channel)
          }
        }
        console.log(this.channelMap)
        return Promise.resolve()
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  doUploadSuccess() {
    // todo
    this.getStoreValueList()
  }

  doStoreValueAddActive() {
    this.$router.push({name: 'store-value-active-add'})
  }

  checkedAllRow() {
    if (this.singleAll) {
      for (let row of this.tableData) {
        this.$refs.table.toggleRowSelection(row, true)
      }
    } else {
      this.$refs.table.clearSelection();
    }
  }
  doExportDialogClose() {
    this.exportDialogShow = false
  }
  doBatchDelete() {
    if (this.selectedArr.length <= 0) {
      this.$message.warning(this.i18n('请先勾选要删除的单据'))
      return
    }
    this.$confirm(this.i18n('是否批量删除这些单据?'), this.i18n('批量删除'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消')
    }).then(() => {
      this.submitBatchDelete()
    })
  }

  doBatchAudit() {
    if (this.selectedArr.length <= 0) {
      this.$message.warning(this.i18n('请先勾选要审核的单据'))
      return
    }
    this.$confirm(this.i18n('是否批量审核这些单据?'), this.i18n('批量审核'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消')
    }).then(() => {
      this.submitBatchAudit()
    })
  }

  doBatchEnd() {
    if (this.selectedArr.length <= 0) {
      this.$message.warning(this.i18n('请先勾选要终止的单据'))
      return
    }
    this.$confirm(this.i18n('是否批量终止这些单据?'), this.i18n('批量终止'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消')
    }).then(() => {
      this.submitBatchEnd()
    })
  }

  doHandleClick() {
    this.page.currentPage = 1
    this.query.stateEquals = this.computeStateEquals(this.activeName)
    this.getStoreValueList()
  }

  doAudit(row: any) {
    this.recordId = row.body.activityId
    this.$confirm(this.i18n('是否确定审核该单据?'), this.i18n('审核'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消')
    }).then(() => {
      const loading = this.$loading(ConstantMgr.loadingOption)
      DepositActivityApi.checkConflict(row).then((resp: any) => {
        if (resp && resp.code === 2000) {
          if (resp.data) { // 有冲突
            this.warnMsg = resp.data
            this.exportDialogShow = true
          } else { // 无冲突
            this.submitAudit(row.body.activityId)
          }
          loading.close()
        } else {
          throw new Error(resp.msg)
        }
      }).catch((error) => {
        loading.close()
        if (error && error.message) {
          this.$message.error(error.message)
        }
      })
    })
  }
  doConfirmSummit(flag: any) {
    if (flag) {
      this.submitAudit(this.recordId)
    }
  }
  doCopy(row: any) {
    this.$router.push({name: 'store-value-active-add', query: {id: row.body.activityId, from: 'copy'}})
  }

  doModify(row: any) {
    this.$router.push({name: 'store-value-active-add', query: {id: row.body.activityId, from: 'edit'}})
  }

  doDelete(row: any) {
    this.$confirm(this.i18n('是否确定删除该单据?'), this.i18n('删除'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消')
    }).then(() => {
      this.submitDelete(row.body.activityId)
    })
  }

  doValidate(row: any) {
    this.$router.push({name: 'effect-evaluation', query: {id: row.body.activityId, row: row}})
  }

  doStop(row: any) {
    this.$confirm(this.i18n('是否确定终止该单据?'), this.i18n('终止'), {
      confirmButtonText: this.i18n('确定'),
      cancelButtonText: this.i18n('取消')
    }).then(() => {
      this.submitStop(row.body.activityId)
    })
  }

  /**
   * 查询
   */
  doSearch() {
    this.page.currentPage = 1
    this.getStoreValueList()
  }

  handleSelectionChange(val: any) {
    this.selectedArr = val
  }

  /**
   * 重置
   */
  doReset() {
    this.query = new DepositActivityFilter()
    this.getStoreValueList()
  }

  /**
   * 去详情
   */
  doGoDtl(row: any) {
    this.$router.push({name: 'store-value-adjust-dtl', query: {id: row.billNumber}})
  }

  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val
    this.getStoreValueList()
  }

  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.size = val
    this.getStoreValueList()
  }

  /**
   * 表格排序条件
   */
  onSortChange({column, prop, order}: any) {
    // todo
  }

  doDtl(row: any) {
    this.$router.push({name: 'store-value-active-dtl', query: {id: row.body.activityId}})
  }

  private getStoreValueList() {
    this.query.page = this.page.currentPage - 1
    this.query.pageSize = this.page.size
    const loading = CommonUtil.Loading()
    DepositActivityApi.query(this.query).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.tableData = resp.data.result
        for (const item of this.tableData) {
          for (const item1 of this.channels) {
            if ( item.body.channels[0].type+item.body.channels[0].id === item1.channel.typeId) {
              item.channelName = item1.name
            }
          }
        }
        this.page.total = resp.data.total
        this.handleSumAmount(resp.data.countResult)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    }).finally(()=>{
      loading.close()
    })
  }

  private submitBatchDelete() {
    let ids: string[] = []
    if (this.selectedArr && this.selectedArr.length > 0) {
      this.selectedArr.forEach((item) => {
        // if (item.body.state === 'INITAIL') {
        //   ids.push(item.body.activityId)
        // }
        ids.push(item.body.activityId)
      })
    }
    DepositActivityApi.batchRemove(ids).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(resp.data)
        this.getStoreValueList()
        this.singleAll = false
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private submitBatchAudit() {
    let ids: string[] = []
    if (this.selectedArr && this.selectedArr.length > 0) {
      this.selectedArr.forEach((item) => {
        // if (item.body.state === 'INITAIL') {
        //   ids.push(item.body.activityId)
        // }
        ids.push(item.body.activityId)
      })
    }
    DepositActivityApi.batchAudit(ids).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(resp.data)
        this.getStoreValueList()
        this.singleAll = false
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private submitBatchEnd() {
    let ids: string[] = []
    if (this.selectedArr && this.selectedArr.length > 0) {
      this.selectedArr.forEach((item) => {
        // if (item.body.state === 'UNSTART' || item.body.state === 'PROCESSING') {
        //   ids.push(item.body.activityId)
        // }
        ids.push(item.body.activityId)
      })
    }
    DepositActivityApi.batchStop(ids).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(resp.data)
        this.getStoreValueList()
        this.singleAll = false
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private submitAudit(uuid: string) {
    DepositActivityApi.audit(uuid).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(this.i18n('审核成功'))
        this.getStoreValueList()
      } else {
        throw new Error(resp.msg)
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private submitDelete(uuid: string) {
    DepositActivityApi.remove(uuid).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(this.i18n('删除成功'))
        this.getStoreValueList()
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  private submitStop(uuid: string) {
    DepositActivityApi.stop(uuid).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.$message.success(this.i18n('终止成功'))
        this.getStoreValueList()
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }
}
