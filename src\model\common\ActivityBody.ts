/*
 * @Autor: 司浩
 * @Description:
 * @Date: 2023-01-09 13:31:39
 * @LastEditors: 黎钰龙
 * @LastEditTime: 2024-05-15 10:07:05
 * @FilePath: \phoenix-web-ui\src\model\common\ActivityBody.ts
 */
import Channel from 'model/common/Channel'
import StoreRange from 'model/common/StoreRange'
import GradesRange from './GradeRange'
import MarketBudget from 'model/promotion/MarketBudget'

export default class ActivityBody {
  // 活动id
  activityId: Nullable<string> = null
  // 状态：INITAIL——未审核；UNSTART——未开始；PROCESSING——进行中；STOPED——已结束
  state: Nullable<string> = null
  // 名称
  name: Nullable<string> = null
  // 活动起始日期 // 和后端保持一致 2020-06-11
  beginDate: Nullable<Date> = null
  // 活动截止日期 和后端保持一致 2020-06-11
  endDate: Nullable<Date> = null
  // 活动类型
  type: Nullable<string> = null
  // 活动主题代码
  topicCode: Nullable<string> = null
  // 活动主题名称
  topicName: Nullable<string> = null
  // 活动渠道
  channels: Channel[] = []
  // 活动门店
  stores: Nullable<StoreRange> = new StoreRange()
  // 备注
  remark: Nullable<string> = null
  // 列表发券平台
  platform: Nullable<string> = null
  // 上传文件地址
  fileUrl: Nullable<string> = null
  // 最后修改时间
  lastModified: Nullable<Date> = null
  // 参与会员等级
  gradeRange: Nullable<GradesRange> = null
  // 活动图片
  pictureUrls: Nullable<string[]> = null
  // 视频号活动剩余库存
  stock: Nullable<number> = null
  //显示排序
  sequence: Nullable<number> = null
  //营销预算
  budget: Nullable<MarketBudget> = null
}
