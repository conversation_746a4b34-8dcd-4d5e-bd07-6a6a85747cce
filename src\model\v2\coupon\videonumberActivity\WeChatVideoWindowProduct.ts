/*
 * @Autor: 司浩
 * @Description:
 * @Date: 2023-02-07 17:33:38
 * @LastEditors: 司浩
 * @LastEditTime: 2023-02-10 17:26:58
 * @FilePath: \phoenix-web-ui\src\model\v2\coupon\videonumberActivity\WeChatVideoWindowProduct.ts
 */
import { GoodsState } from 'model/v2/coupon/videonumberActivity/GoodsState'

// 视频号橱窗商品
export default class WeChatVideoWindowProduct {
  // 活动id
  activityId: Nullable<string> = null
  // 名称
  name: Nullable<string> = null
  // 活动起始日期
  beginDate: Nullable<Date> = null
  // 活动截止日期
  endDate: Nullable<Date> = null
  // 售价
  price: Nullable<number> = null
  // 剩余库存
  currentStock: Nullable<number> = null
  // 上下架状态
  goodsState: Nullable<GoodsState> = null
  // 操作时间
  create: Nullable<Date> = null
  // 操作人信息
  operator: Nullable<string> = null
  // 上下架失败原因
  goodsPutRemark: Nullable<string> = null
}
