import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import RenderTemplatesMap from './RenderTemplates.map';
import draggable from 'vuedraggable';
import '../../cmp/page-place-components/index';
import WidgetConfig from '../template.map';
import imageUrlLink from '../ImageUrlLink';
import { ImageUrlLinkVO } from '../ImageUrlLinkVO';
import DefaultPagePlaceProperty from '../page-props-components/DefaultPagePlaceProperty';
import I18nPage from 'common/I18nDecorator';



@Component({
  name: 'RenderPagePlace',
  components: { draggable },
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/页面/页面管理'
  ],
  auto: true
})
export default class RenderPagePlace extends Vue {
  @Prop()
  renderTemplateList: Array<any>;
  @Prop()
  valdateArr: any
  activeComId: string = '';
  activeIndex: any = -11;
  activeProps: any;
  renderTemplatesMap = RenderTemplatesMap.getTemplates();
  activeCmpId: string = '';
  rules = {
    propTitle: [{ required: true, message: this.i18n('请输入页面标题名称'), trigger: ['blur', 'change'] }],
  };
  titleValid: boolean = false
  localValue = DefaultPagePlaceProperty.pageTitle()
  validateErr(cmpUuid: string) {
    if (this.valdateArr.find((item: any) => item.cmpUuid === cmpUuid)) {
      return this.valdateArr.find((item: any) => item.cmpUuid === cmpUuid).validateRes
    }
    return false
  }
  get titleValidate() {
    if (this.valdateArr.find((item: any) => item.cmpUuid === 'titleCmp')) {
      return this.valdateArr.find((item: any) => item.cmpUuid === 'titleCmp').validateRes
    }
    return false
  }
  get globalInvestTitle() {
    return this.$store.state.globalInvestTitle
  }
  get pageTopImage() {
    return 'url(' + imageUrlLink[ImageUrlLinkVO.ic_page_top] + ')';
  }
  get getPageStyle() {
    let str = ''
    this.extractBackgroundValues(this.globalInvestTitle.styCustomBgColor).forEach((item) => {
      str += 'background: ' + item + ';'
    })
    if (!str.length) {
      str = 'background: ' + this.globalInvestTitle.styBgColor + ';'
    }
    return str
  }
  mounted() {
    this.localValue = this.globalInvestTitle
    this.doValidate()
    if (this.globalInvestTitle.propTitle) {
      this.titleValid = true
    }
  }
  handleChange() {
    this.$store.dispatch('globalInvestTitleAction', this.localValue)
    this.doValidate()
  }
  // 头部点击
  titleClick() {
    this.activeCom(
      {
        item: {
          id: 'titleCmp',
          props: {},
          name: this.i18n('顶部导航栏'),
          uuid: 'titleCmp'
        },
        index: -11
      },
    )
  }
  dragStart() {
    this.activeComId = '';
    this.activeIndex = -11;
    this.$emit('initPlaceData');
  }
  // 资源位移动
  onPlaceMove(evt: any, originalEvent: any) {
    if (
      ['shouyedingbu', 'productcategory', 'contentChannelTopImage', 'contentChannelCategory', 'popActivity', 'suspension'].indexOf(evt.draggedContext.element.placeId) > -1 ||
      ['shouyedingbu', 'productcategory', 'contentChannelTopImage', 'contentChannelCategory'].indexOf(evt.relatedContext.element.placeId) > -1
    ) {
      return false;
    }
    return true;
  }

  // 操作区渲染的资源位组件激活态
  activeCom(prop: any) {
    console.log('prop ==>', prop)
    this.activeIndex = prop.index
    this.activeProps = prop
    this.$emit('activeCmp', { ...prop });
  }
  // 资源位移动
  placeDragEnd() {
    this.$emit('placeDragEnd', this.renderTemplateList);
  }
  // 操作区渲染的资源位组件
  getComponent(id: any) {
    return this.renderTemplatesMap.find((v) => v.id === id)?.component;
  }

  // 获取当前资源位组件操作按钮
  getToolBarBtns(id: any) {
    let toolbarBtns: any[] = [];
    toolbarBtns = this.renderTemplatesMap.find((v) => v.id === id)
      ?.toolbarBtns;
    return toolbarBtns;
  }

  // 资源位组件操作按钮操作类型
  toolBarClick(params: any) {
    console.log('toolBarClick ==>', params.clickName)
    this.$emit('toolBarClick', { action: params.clickName, activeUuid: this.activeProps.item.uuid });
  }
  doValidate() {
    const refs: any = this.$refs.form
    refs.validate((valid: boolean) => {
      console.log('valid ==>', valid)
      this.titleValid = valid
    })
  }
  extractBackgroundValues(inputString: string) {
    const regex = /background:\s*([^;]+);/g;
    const results = [];
    let match;
    while ((match = regex.exec(inputString || '')) !== null) {
      results.push(match[1].trim());
    }
    return results;
  }
}
