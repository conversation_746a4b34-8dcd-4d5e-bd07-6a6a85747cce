import ActivityBody from 'model/common/ActivityBody'
import ExchangeRule from 'model/points/activity/pointsexchangegoods/ExchangeRule'
import DateTimeCondition from "model/common/DateTimeCondition";
import PushGroup from 'model/precisionmarketing/pushplan/PushGroup';

export default class PointsExchangeGoodsActivity {
  // 仅保存
  justSave: Nullable<boolean> = null
  // 活动信息
  activityBody: Nullable<ActivityBody> = null
  // 等级代码;空和null表示不限制
  gradeCode: Nullable<string> = null
  // 等级名称
  gradeName: Nullable<string> = null
  // 活动时间限制
	dateTimeCondition = new DateTimeCondition()
  // 会员最多兑换次数
  maxExchangeTimesPerMember: Nullable<number> = null
  // 每次最多兑换商品种类
  maxExchangeGoodsItemsPerTime: Nullable<number> = null
  // 积分兑换商品规则
  exchangeRules: ExchangeRule[] = []
  // 参与人群
  rule: Nullable<PushGroup> = null
}