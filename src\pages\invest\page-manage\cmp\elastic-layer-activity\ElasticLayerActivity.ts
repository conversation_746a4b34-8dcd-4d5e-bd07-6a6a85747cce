import I18nPage from "common/I18nDecorator";
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import ContentTemplateApi from "http/template/ContentTemplateApi";
import NavigationSetting from "model/navigation/NavigationSetting";
import ContentTemplate from "model/template/ContentTemplate";
import ListWrapper from "cmp/list/ListWrapper.vue";
import FormItem from "cmp/formitem/FormItem.vue";
import { EditMode } from "model/local/EditMode";
import CommonUtil from "util/CommonUtil";
import UnionActivityQuery from "model/promotion/UnionActivityQuery";
import GiftCardActivityFilter from 'model/card/activity/GiftCardActivityFilter'
// import PointsActivityFilter from "model/points/activity/PointsActivityFilter";
import CouponActivityFilter from 'model/v2/coupon/CouponActivityFilter'
import ActivityGroupType from 'model/common/ActivityGroupType';

import UnionActivityApi from "http/promotion/UnionActivityApi";
import CouponActivityApi from "http/v2/coupon/CouponActivityApi";

// import PointsActivityApi from "http/points/activity/PointsActivityApi"; //query
import GiftCardActivityApi from "http/card/activity/GiftCardActivityApi"; //query
// import UnionActivityApi from 'http/promotion/UnionActivityApi'  //queryByType

import ActivityBody from "model/common/ActivityBody";
import MyQueryCmp from "cmp/querycondition/MyQueryCmp";

class Query {
  nameLike: string = ""; //活动名称
  numberEquals: string = ""; //活动号
  topicNameLikes: string = ""; //所属主题
  stateEquals: Nullable<string> = ""; //状态
  typeEquals: Nullable<string> = null; //活动类型
}
class Total {
  all: number = 0;
  initial: number = 0;
  audit: number = 0;
  doing: number = 0;
  end: number = 0;
  suspend: number = 0;
}
class UnionActivityItem {
  label: string = ""; //标签值
  value: string = ""; //活动筛选值
  // activityType: string = ""; //活动类型
  viewAble: boolean = false; //查看权限
  modifyAble: boolean = false; //修改权限
  auditAble: boolean = false; //审核权限
  stopAble: boolean = false; //终止权限
  removeAble: boolean = false; //删除权限
  goToDtl: Function = () => { }; //前往详细页
  goToCopy: Function = () => { }; //前往复制
  goToModify: Function = () => { }; //前往修改
}
@Component({
  name: "ElasticLayerActivity",
  components: {
    FormItem,
    ListWrapper,
    MyQueryCmp,
  },
})
@I18nPage({
  prefix: ["/公用/券模板",'/页面/页面管理'],
  auto: true,
})
export default class ElasticLayer extends Vue {
  @Prop({ type: String }) activityType: string;
  $refs: any
  ContentTemplate: ContentTemplate[] = []; // 活动模型
  UnionActivityQuery: UnionActivityQuery = new UnionActivityQuery();
  // INITAIL——未审核；UNSTART——未开始；PROCESSING——进行中；STOPED——已结束

  dialogShow: boolean = false;
  titleString: string = "";

  query: Query = {
    nameLike: "", //活动名称
    numberEquals: "", //活动号
    topicNameLikes: "", //所属主题
    stateEquals: "PROCESSING", //活动状态
    typeEquals: null, //活动类型
  };
  total: Total = {
    all: 0,
    initial: 0,
    audit: 0,
    doing: 0,
    end: 0,
    suspend: 0,
  };
  selectDate: any = []; //活动时间
  tableData: ActivityBody[] = [];
  activitiesInfo: UnionActivityItem[] = [];
  activityList: string[] = []; //最终确定选中数据
  page = {
    currentPage: 1,
    total: 0,
    size: 10,
  };

  get ContentTemplateState() {
    let res = [
      {
        value: "UNSTART",
        label: this.i18n("未开始"),
      },
      {
        value: "PROCESSING",
        label: this.i18n("进行中"),
      },
    ]
    if (this.titleString === this.i18n('电子卡发售活动')) {
      res = res.filter((item) => item.value === 'PROCESSING')
    }
    return res
  }

  get getRowKey() {
    if (this.activityType === "electronicCard") {
      return 'body.activityId'  //电子卡
    } else {
      return 'activityId'
    }
  }

  open(ids: string[]) {
    this.dialogShow = true
    this.page.currentPage = 1
    this.query = {
      nameLike: "", //活动名称
      numberEquals: "", //活动号
      topicNameLikes: "", //所属主题
      stateEquals: "PROCESSING", //活动状态
      typeEquals: null, //活动类型
    }
    if (this.activityType === "electronicCard") {
      this.titleString = this.i18n("电子卡发售活动");
    } else if (this.activityType === "collectionPoint") {
      this.titleString = this.i18n("集点活动");
    } else if (this.activityType === "bigTurntable") {
      this.titleString = this.i18n("大转盘");
    } else if (this.activityType === "shareGroupLottery") {
      this.titleString = this.i18n("抽奖拼团")
    }
    this.activityList = ids || []
    this.getList()
  }

  canel() {
    this.dialogShow = false;
  }
  confirm() {
    this.dialogShow = false;
    this.$emit("submit", this.activityList);
  }

  // 电子卡活动列表  /v1/giftcard-activity/query   
  private getelectronicCardList(): void {
    const loading = CommonUtil.Loading();
    const params = new GiftCardActivityFilter();
    params.topicNameLikes = this.query.topicNameLikes ? this.query.topicNameLikes : null;
    params.nameLikes = this.query.nameLike ? this.query.nameLike : null
    params.activityIdLikes = this.query.numberEquals ? this.query.numberEquals : null
    // 活动时间
    if (this.selectDate && this.selectDate.length > 0) {
      params.beginDateGreaterOrEquals = this.selectDate[0];
      params.endDateLess = this.selectDate[1];
    } else {
      params.beginDateGreaterOrEquals = null;
      params.endDateLess = null;
    }
    params.page = this.page.currentPage - 1;
    params.pageSize = this.page.size;
    params.stateEquals = this.query.stateEquals ? this.query.stateEquals : null;
    this.tableData = [];
    GiftCardActivityApi.query(params)
      .then((res) => {
        if (res.code === 2000) {
          this.tableData = res.data?.result as any || [];
          this.page.total = res.data?.total || 0;
          if (this.activityList.length < 0) this.$refs.table.clearSelection();
          this.tableData.forEach((item: any) => {
            this.activityList.some((ele: any) => {
              if (item.body.activityId == ele) {
                this.$refs.table.toggleRowSelection(item, true);
                return true
              }
            })
          })
        } else {
          throw new Error(res.msg || this.i18n("查询失败"));
        }
      })
      .catch((error) => {
        this.$message.error(error.message);
      })
      .finally(() => {
        loading.close();
      });


  }
  //集点 /v1/web/activity/query
  private getcollectionPointList(): void {
    const loading = CommonUtil.Loading();
    const params: CouponActivityFilter = new CouponActivityFilter();
    params.topicNameLikes = this.query.topicNameLikes ? this.query.topicNameLikes : null;
    params.nameLike = this.query.nameLike ? this.query.nameLike : null;
    params.numberLike = this.query.numberEquals ? this.query.numberEquals : null;
    // 活动时间
    if (this.selectDate && this.selectDate.length > 0) {
      params.begin = this.selectDate[0];
      params.end = this.selectDate[1];
    } else {
      params.begin = null;
      params.end = null;
    }
    params.page = this.page.currentPage - 1;
    params.pageSize = this.page.size;
    params.stateEquals = this.query.stateEquals ? this.query.stateEquals : null;
    params.groupType = 'CONSUME_GIFT' as any
    params.typeEquals = 'COLLECT_POINTS_ACTIVITY'
    this.tableData = [];
    CouponActivityApi.query(params)
      .then((res) => {
        if (res.code === 2000) {
          this.tableData = res.data?.list || [];
          this.page.total = res.total;
          this.total.all = res.data?.summary?.sum || 0;
          this.total.initial = res.data?.summary?.initail || 0;
          this.total.audit = res.data?.summary?.unstart || 0;
          this.total.doing = res.data?.summary?.processing || 0;
          this.total.end = res.data?.summary?.stoped || 0;
          this.tableData.forEach(item => {
            this.activityList.some((ele: any) => {
              if (item.activityId == ele) {
                this.$refs.table.toggleRowSelection(item, true);
                return true
              }
            })
          })
        } else {
          throw new Error(res.msg || this.i18n("查询失败"));
        }
      })
      .catch((error) => {
        this.$message.error(error.message);
      })
      .finally(() => {
        loading.close();
      });
  }
  //   大转盘活动列表 /v1/web/activity/queryByType
  private getrouletteList(): void {
    const loading = CommonUtil.Loading();
    const params = new UnionActivityQuery();
    params.topicNameLikes = this.query.topicNameLikes ? this.query.topicNameLikes : null;
    params.nameLike = this.query.nameLike ? this.query.nameLike : null;
    params.numberEquals = this.query.numberEquals ? this.query.numberEquals : null;

    // 活动时间
    if (this.selectDate && this.selectDate.length > 0) {
      params.begin = this.selectDate[0];
      params.end = this.selectDate[1];
    } else {
      params.begin = null;
      params.end = null;
    }
    params.page = this.page.currentPage - 1;
    params.pageSize = this.page.size;
    params.stateEquals = this.query.stateEquals ? this.query.stateEquals : null;
    let arr: any = [];
    arr.push('BigWheelActivityRule');
    params.typeIn = arr;
    this.tableData = [];
    UnionActivityApi.queryByType(params)
      .then((res) => {
        if (res.code === 2000) {
          this.tableData = res.data?.list || [];
          this.page.total = res.total;
          this.total.all = res.data?.summary?.sum || 0;
          this.total.initial = res.data?.summary?.initail || 0;
          this.total.audit = res.data?.summary?.unstart || 0;
          this.total.doing = res.data?.summary?.processing || 0;
          this.total.end = res.data?.summary?.stoped || 0;
          this.tableData.forEach(item => {
            this.activityList.some((ele: any) => {
              if (item.activityId == ele) {
                this.$refs.table.toggleRowSelection(item, true);
                return true
              }
            })
          })
        } else {
          throw new Error(res.msg || this.i18n("查询失败"));
        }
      })
      .catch((error) => {
        this.$message.error(error.message);
      })
      .finally(() => {
        loading.close();
      });
  }

  // 抽奖拼团活动列表 /v1/web/activity/queryByType
  private getGroupLotteryList() {
    const loading = CommonUtil.Loading();
    const params = new UnionActivityQuery();
    params.topicNameLikes = this.query.topicNameLikes ? this.query.topicNameLikes : null;
    params.nameLike = this.query.nameLike ? this.query.nameLike : null;
    params.numberEquals = this.query.numberEquals ? this.query.numberEquals : null;
    params.begin = this.selectDate?.length > 0 ? this.selectDate[0] : null;
    params.end = this.selectDate?.length > 0 ? this.selectDate[1] : null;
    params.page = this.page.currentPage - 1;
    params.pageSize = this.page.size;
    params.stateEquals = this.query.stateEquals ? this.query.stateEquals : null;
    params.typeIn = ['GroupBookingActivityRule'];
    this.tableData = [];
    UnionActivityApi.queryByType(params)
      .then((res) => {
        if (res.code === 2000) {
          this.tableData = res.data?.list || [];
          this.page.total = res.total;
          this.total.all = res.data?.summary?.sum || 0;
          this.total.initial = res.data?.summary?.initail || 0;
          this.total.audit = res.data?.summary?.unstart || 0;
          this.total.doing = res.data?.summary?.processing || 0;
          this.total.end = res.data?.summary?.stoped || 0;
          this.tableData.forEach(item => {
            this.activityList.some((ele: any) => {
              if (item.activityId == ele) {
                this.$refs.table.toggleRowSelection(item, true);
                return true
              }
            })
          })
        } else {
          throw new Error(res.msg || this.i18n("查询失败"));
        }
      })
      .catch((error) => {
        this.$message.error(error.message);
      })
      .finally(() => {
        loading.close();
      });
  }
  // 查询活动列表
  private getList() {
    switch (this.activityType) {
      case "electronicCard": //电子卡 活动列表
        this.getelectronicCardList();
        break;
      case "collectionPoint": // 集点活动列表
        this.getcollectionPointList();
        break;
      case "bigTurntable": // 大转盘活动列表
        this.getrouletteList();
        break;
      case "shareGroupLottery": // 抽奖拼团活动列表
        this.getGroupLotteryList();
        break;
    }
  }
  handleSelectionChange(val: any[], row: any) {
    const flag = val.some((item) => this.checkBodyToActivityId(item) === this.checkBodyToActivityId(row))
    const index = this.activityList.findIndex((item: any) =>
      item == this.checkBodyToActivityId(row)
    )
    if (!flag) {
      this.activityList.splice(index, 1)
    } else {
      this.activityList.push(this.checkBodyToActivityId(row))
    }
  }
  // 获取不同结构下的activityId
  checkBodyToActivityId(obj: any) {
    if (obj.body) {
      return obj.body.activityId
    } else {
      return obj.activityId
    }
  }
  handleSelectAll(selectArr: any[]) {
    const currentPageData = selectArr.filter((item) => {
      return this.tableData.find((val) => this.checkBodyToActivityId(val) === this.checkBodyToActivityId(item))
    })
    if (currentPageData.length) {
      currentPageData.forEach((item) => {
        this.activityList.push(this.checkBodyToActivityId(item))
      })
    } else {
      this.tableData.forEach((item) => {
        const id = this.checkBodyToActivityId(item)
        const targetIndex = this.activityList.indexOf(id)
        if (targetIndex > -1) {
          this.activityList.splice(targetIndex, 1)
        }
      })
    }
    this.activityList = [...new Set(this.activityList)]
  }
  /**
   * 查询
   */
  doSearch() {
    this.page.currentPage = 1
    this.getList();
  }

  /**
   * 重置
   */
  doReset() {
    this.page.currentPage = 1;
    this.query = {
      nameLike: "", //活动名称
      numberEquals: "", //活动号
      topicNameLikes: "", //所属主题
      stateEquals: "PROCESSING", //活动状态
      typeEquals: null, //活动类型
    };
    this.getList();
  }
  /**
   * 每页多少条的回调
   * @param val
   */
  onHandleSizeChange(val: number) {
    this.page.currentPage = 1
    this.page.size = val;
    this.getList();
  }
  /**
   * 分页页码改变的回调
   * @param val
   */
  onHandleCurrentChange(val: number) {
    this.page.currentPage = val;
    this.getList();
  }
}
