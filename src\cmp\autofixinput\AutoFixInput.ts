import { Component, Prop, Vue, Watch } from 'vue-property-decorator';

@Component({
  name: "AutoFixInput",
  components: {},
  model: {
    prop: "modelValue",
    event: "change",
  },
})
export default class AutoFixInput extends Vue {
  val: Nullable<number | string> = null;
  disableVal: Nullable<boolean> = null;
  @Prop()
  modelValue: Nullable<number>;
  @Prop({
    default: null,
  })
  type: string;
  @Prop()
  size: Nullable<string>;
  @Prop({
    type: Number,
    default: null,
  })
  min: Nullable<number>;
  @Prop({
    type: Number,
    default: null,
  })
  max: Nullable<number>;
  @Prop({
    type: Number,
    default: null,
  })
  fixed: Nullable<number>;
  @Prop({
    type: Boolean,
    default: false,
  })
  disabled: Nullable<boolean>;

  @Prop({
    type: String,
    default: null,
  })
  placeholder: Nullable<string>;
  @Prop({
    type: String,
    default: null
  })
  appendTitle: string;

  $refs: any

  created() {
    this.val = this.modelValue;
    this.disableVal = this.disabled;
  }

  @Watch("modelValue")
  watchModelValue(val: number) {
    this.val = this.modelValue;
  }

  @Watch("disabled")
  watchDisabled(val: number) {
    this.disableVal = this.disabled;
  }

  @Watch("val")
  watchVal(val: number) {
    this.doChange(val)
  }

  doChange(val: number) {
    this.$emit("change", val);
  }

  private handleBlur() {
    this.autoFix();
    this.$emit("blur", this.modelValue);
  }

  private autoFix() {
    this.val = this.checkNumber(this.val, this.min, this.max, this.fixed);
  }

  private checkNumber(value: Nullable<number | string>, min: Nullable<number>, max: Nullable<number>, fixed: Nullable<number>) {
    value = Number.parseFloat(value + "");
    if (isNaN(value)) {
      return null;
    }
    // @ts-ignore
    if (min !== undefined && min !== null && value < min) {
      let result = Number.parseFloat(min + "");
      return fixed !== null ? Number.parseFloat(result.toFixed(fixed)) : result;
    }
    // @ts-ignore
    if (max !== undefined && max !== null && value > max) {
      let result = Number.parseFloat(max + "");
      return fixed !== null ? Number.parseFloat(result.toFixed(fixed)) : result;
    }
    return fixed !== null ? value.toFixed(fixed) : value;
  }

  focus() {
    this.$refs.autoInput.focus();
  }
}
