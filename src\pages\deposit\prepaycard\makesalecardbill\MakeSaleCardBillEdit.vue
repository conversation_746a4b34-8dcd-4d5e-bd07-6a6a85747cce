<template>
  <div class="gitf-card-activity-edit">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button v-if="hasOptionPermission('/卡/卡管理/制售单', '单据新建')" type="primary" @click="save">保存</el-button>
        <el-button v-if="hasOptionPermission('/卡/卡管理/制售单', '单据审核')" type="primary" @click="saveAndAudit">保存并审核</el-button>
        <el-button v-if="hasOptionPermission('/卡/卡管理/制售单', '单据新建')" @click="$router.go(-1)">取消</el-button>
      </template>
    </BreadCrume>
    <div class="current-page">
      <el-form :model="form" :rules="rules" ref="form" label-width="100px">
        <div class="panel">
          <div class="content">
            <el-form-item label="卡模板" prop="cardTemplateNumber">
              <CardTplItem no-i18n @submit="selectTpl" :number="cardNumber" :type="null" :cardMedium="['bar','online']">
              </CardTplItem>
            </el-form-item>
            <el-form-item :label="i18n('/储值/预付卡/充值卡制售单/卡信息')" class="require-item" prop="specs">
              <el-row style="line-height: 40px" class="gray-tips"><i class="el-icon-warning" />
                <i18n k="/储值/预付卡/充值卡制售单/编辑页面/本单还可制售{0}张卡" style="margin-left:4px">
                  <template slot="0">
                    {{total < 0 ? 0 : total}}
                  </template>
                </i18n>
              </el-row>
              <div class="face-amount-spec">
                <el-row v-for="(line, index) of form.lines" style="height: 52px" :key="index">
                  <div class="line">
                    <el-row style="margin-top: 0;height: 100%;">
                      <el-col :span="8">
                        <span class="amount">
                          {{i18n('/卡/卡管理/售卡单/卡面额/次数')}}：
                          <template v-if="line.count">{{line.count}}{{i18n('/营销/券礼包活动/券礼包活动/次')}}</template>
                          <template v-else>{{line.faceAmount}}{{i18n('/券/购券管理/元')}}</template>
                        </span>
                      </el-col>
                      <el-col :span="8" class="price">
                        <span style="color: red">*</span>
                        <span>售价</span>&nbsp;
                        <AutoFixInput @change="checkPrice(line,index)" :min="0" :max="99999.99" :fixed="2" v-model="line.price" style="width: 120px" :appendTitle="i18n('元')">
                        </AutoFixInput>
                      </el-col>
                      <el-col :span="8" class="price">
                        <span style="color: red">*</span>
                        <span>制售数量</span>&nbsp;
                        <AutoFixInput @change="computeEndCode" :min="1" :max="1000" :fixed="0" v-model="line.total" style="width: 120px" :appendTitle="i18n('张')">
                        </AutoFixInput>
                      </el-col>
                    </el-row>
                  </div>
                </el-row>
              </div>
            </el-form-item>
            <el-form-item :label="i18n('/卡/卡管理/售卡单/总售价')">
              {{getTotalPrice}}
            </el-form-item>
            <el-form-item :label="i18n('/储值/预付卡/预付卡充值单/起始卡号')" prop="startCardCode">
              <el-input v-model="form.startCardCode" style="width:300px" @change="doStartCodeChange" :placeholder="computeStartNumber()">
              </el-input>
              <span class="span-btn" style="margin-left:12px" @click="autoMakeStartCode">{{i18n('/卡/卡管理/制卡单/制卡单详情/自动生成')}}</span>
            </el-form-item>
            <el-form-item :label="i18n('/储值/预付卡/预付卡充值单/结束卡号')" prop="endCardCode">
              <el-input v-model="form.endCardCode" style="width:300px" disabled></el-input>
            </el-form-item>
            <el-form-item label="备注" prop="remark">
              <el-input type="textarea" placeholder="请输入不超过140个字" v-model="form.remark" maxlength="140" show-word-limit style="width: 500px" />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts" src="./MakeSaleCardBillEdit.ts">
</script>

<style lang="scss">
.gitf-card-activity-edit {
  width: 100%;
  height: 100%;
  background-color: white;
  overflow: auto;
  .current-page {
    height: calc(100% - 50px) !important;
    overflow-y: auto;
    .panel {
      .header {
        font-weight: 500;
        padding: 20px 20px 0 20px;
        font-size: 18px;
      }
      .content {
        padding: 20px;
      }
      .face-amount-spec {
        background-color: #f9f9f9;
        width: 800px;
        .line {
          height: 100%;
          padding: 0;
          margin: 0 20px;
          line-height: 50px;
          .amount {
            font-size: 15px;
            font-weight: 600;
          }
          .price {
            font-size: 14px;
          }
        }
        .error {
          color: red;
          font-size: 12px;
          margin-top: 0px;
          line-height: 25px;
        }
      }
    }

    .split {
      height: 20px;
      background-color: #eeeff1;
    }
  }
}
</style>
