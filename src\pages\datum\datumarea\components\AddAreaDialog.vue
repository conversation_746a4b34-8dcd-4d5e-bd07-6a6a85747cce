<template>
    <div class="add-area-dialog">
        <el-dialog :title="i18n('添加区域')" width="600px" :close-on-click-modal="false" :visible.sync="dialogVisible">
            <el-form ref="form" :model="form" :rules="rules" label-width="140px">
                <el-form-item :label="i18n('区域代码')" prop="areaCode">
                    <p style="padding-top: 5px;color: #79879E"> {{i18n('代码不允许与已有区域重复')}}</p>
                    <el-input v-model="form.areaCode" class="w-250" :placeholder="i18n('请输入')"></el-input>
                </el-form-item>
                <el-form-item :label="i18n('区域名称')" prop="areaName">
                    <el-input v-model="form.areaName" class="w-250" :placeholder="i18n('请输入')"></el-input>
                </el-form-item>
                <el-form-item class="select" :label="i18n('所属营销中心')" prop="syncType">
                    【{{marketCenter}}】{{ marketCenterName }}
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="close()">{{ i18n('取消') }}</el-button>
                <el-button @click="onClear()">{{ i18n('清空') }}</el-button>
                <el-button @click="onSave()" :loading="loading" type="primary">{{ i18n('确定') }}</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script lang="ts" src="./AddAreaDialog.ts">
</script>

<style lang="scss" scoped>
.add-area-dialog {

    // 
    ::v-deep .el-form-item {
        margin-bottom: 12px;
    }
}
</style>