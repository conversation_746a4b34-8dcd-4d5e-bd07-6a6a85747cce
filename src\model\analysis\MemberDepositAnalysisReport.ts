import {AnalysisReportDateUnit} from "model/analysis/AnalysisReportDateUnit";
import AnalysisChartData from "model/analysis/AnalysisChartData";
import MemberDepositAnalysisSummary from "model/analysis/MemberDepositAnalysisSummary";

// 会员储值分析报表响应
export default class MemberDepositAnalysisReport {
  // 时间维度
  dateUnit: Nullable<AnalysisReportDateUnit> = null
  // 数据概览
  summary: Nullable<MemberDepositAnalysisSummary> = null
  // 充值金额指标
  rechargeAmountData: AnalysisChartData[] = []
  // 消费金额指标
  consumeAmountData: AnalysisChartData[] = []
  // 调整增加金额指标
  adjustAddAmountData: AnalysisChartData[] = []
  // 调整减少金额指标
  adjustSubAmountData: AnalysisChartData[] = []
  // 转入金额指标
  transferInAmountData: AnalysisChartData[] = []
  // 可用余额指标（截止查询日期结束日期）
  usableTotalData: AnalysisChartData[] = []
  // 可用本金指标（截止查询日期结束日期）
  usableBalanceData: AnalysisChartData[] = []
  // 可用赠金指标（截止查询日期结束日期）
  usableGiftBalanceData: AnalysisChartData[] = []

}