import {Component} from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import AbstractSelectDialog from './AbstractSelectDialog';
import CircleApi from "http/circle/CircleApi";
import CircleFilter from "model/circle/CircleFilter";
import Circle from "model/circle/Circle";
import I18nPage from "common/I18nDecorator";

@Component({
  name: 'BizDistrictSelectorDialog',
  components: {
    FormItem
  }
})
@I18nPage({
  auto: false,
  prefix: [
      '/公用/公共组件/商圈选择弹框组件'
  ]
})
export default class BizDistrictSelectorDialog extends AbstractSelectDialog<Circle> {
  i18n: I18nFunc
  circleFilter: CircleFilter = new CircleFilter()

  reset() {
    this.circleFilter = new CircleFilter()
  }

  getId(ins: Circle): string {
    // @ts-ignore
    return ins.circle.id;
  }

  getName(ins: Circle): string {
    // @ts-ignore
    return ins.circle.name;
  }

  getResponseData(response: any): any {
    return response.data
  }

  queryFun(): Promise<any> {
    this.circleFilter.page = this.page.currentPage - 1
    this.circleFilter.pageSize = this.page.size
    return CircleApi.query(this.circleFilter)
  }
}
