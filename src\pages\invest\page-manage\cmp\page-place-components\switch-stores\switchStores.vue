<template>
  <div class="coupon" :style="{
      padding:
        localProperty.styMarginTop +
        'px ' +
        localProperty.styMarginRight +
        'px ' +
        localProperty.styMarginBottom +
        'px ' +
        localProperty.styMarginLeft +
        'px',
    }" @click="activeTemplate">
    <div class="toolBar" v-if="activeIndex === index">
      <setting-toolbar :btns="toolBarBtns" @click="toolbarClick"></setting-toolbar>
    </div>
    <div class="store" :style="{background: localProperty.styBackgroundColor, 
        justifyContent: localProperty.propShowStyle === '2' ? 'start' : 'space-between'}">
      <div style="display:flex;align-items:center">
        <i class="iconfont icon-a-ic_location" :style="{ fontSize: '20px', color: localProperty.styContentColor }"></i>
        <div class="name">
          <b :style="{ color: localProperty.styContentColor }">{{ i18n('海鼎未来测试店')}}</b>
        </div>
      </div>
      <div class="text" :style="{ color: localProperty.styContentColor }">
        {{ localProperty.propAuxiliaryText }}
        <i class="iconfont icon-ic_right" :style="{ fontSize: '14px', color: localProperty.styContentColor }"></i>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./switchStores.ts"></script>

<style lang="scss" scoped>
.coupon {
  width: 100%;
  background: #f9f9f9;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  border: 2px solid transparent;
  position: relative;
  margin-bottom: 10px;
  &-bg {
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-size: 100%;
  }
  &-title {
    text-align: center;
    padding-top: 42px;
    font-size: 20px;
    color: #fff;
  }
  .toolBar {
    position: absolute;
    top: 0;
    right: 1px;
    z-index: 999;
    cursor: pointer;
  }
  .store {
    padding: 10px 12px;
    display: flex;
    align-items: center;
    .icon {
      width: 20px;
      height: 20px;
    }
    .name {
      font-weight: 500;
      padding: 0 10px;
      font-size: 14px;
      color: #353535;
      line-height: 20px;
      text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.2);
      overflow: hidden; /* 隐藏超出部分 */
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2; /* 限制最多显示两行 */
      span {
        display: inline-block;
        background: rgba(104, 104, 104, 0.1);
        border-radius: 2px;
        padding: 2px 5px;
        font-weight: 400;
        font-size: 10px;
        color: #353535;
        line-height: 14px;
      }
      img {
        vertical-align: middle;
        width: 10px;
        height: 10px;
      }
    }
    .text {
      display: flex;
      align-items: center;
      max-width: 88px;
      font-weight: 400;
      font-size: 12px;
      color: #353535;
      line-height: 16px;
      text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.2);
      text-align: right;
      margin-right: 16px;
    }
  }
}
</style>
