import { ConnectiveType } from "model/autoTag/ConnectiveType"
import MemberTagRule from "./MemberTagRule"
import MemberAttributeRule from "model/autoTag/MemberAttributeRule"
import MemberActionRule from "model/autoTag/MemberActionRule"

export default class UserGroupCalculatorRule {
  // 关系连接符：或者/ 且
  connective: Nullable<ConnectiveType> = ConnectiveType.and
  // 用户标签规则
  memberTagRule: Nullable<MemberTagRule> = null
  // 用户属性规则
  memberAttributeRule: Nullable<MemberAttributeRule> = null
  // 用户行为规则
  memberActionRule: Nullable<MemberActionRule> = null
}