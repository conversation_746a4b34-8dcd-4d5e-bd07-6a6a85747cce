import GiftInfo from 'model/equityCard/default/GiftInfo'
import { DateType } from 'model/equityCard/default/DateType'

// 周期赠礼规则
export default class BenefitCardTemplateCircleRule {
  // 卡权益
  giftBag: Nullable<GiftInfo> = null
  // 数量限制日期类型: DAY, WEEK, MONTH, YEAR
  limitPartakeDateType: Nullable<DateType> = null
  // 日期类型下，参数次数限制
  limitMemberPerTime: Nullable<number> = null
  // 日期间隔，默认为1
  dateInterval: Nullable<number> = null
  // 周几发送赠礼，当 limitPartakeDateType = WEEK 适用
  weekDay: Nullable<number> = null
  // 发放赠礼时间类型 fixed-按指定日期 acquired-按领卡日期
  dateType: Nullable<string> = null
  // 发放赠礼时间 dateType=fixed时适用
  day: Nullable<number> = null
}