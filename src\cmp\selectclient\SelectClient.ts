/*
 * @Author: 黎钰龙
 * @Date: 2023-11-02 15:05:58
 * @LastEditTime: 2023-12-01 10:27:33
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\selectStores\SelectStores.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import IdName from 'model/common/IdName';
import { Component, Model, Prop, Vue, Watch } from 'vue-property-decorator';
import ClientFilter from "model/card/client/ClientFilter";
import ClientApi from "http/card/client/ClientApi";
import Client from "model/card/client/Client";
@Component({
  name: 'SelectClient',
  components: {}
})
@I18nPage({
  prefix: [
    '/公用/券模板',
    '/公用/下拉框/提示'
  ],
  auto: true
})
export default class SelectClient extends Vue {
  @Model('change') selectClientId: string | string
  @Prop({ type: String, default: '400px' }) width: string;
  @Prop() placeholder: any;
  @Prop({ type: Boolean, default: false }) hideAll: boolean; //是否隐藏“全部”选项
  @Prop({ type: Boolean,  default:false}) disabled: boolean;
  @Prop({ default: () => { return {} } }) appendAttr: any; //查询客户时，需要追加的参数
  selectLoading: boolean = false
  clients: Client[] = []

  @Watch('selectClient', { deep: true })
  handle(value:any) {
    if(!value) {
      this.getClients('')
    }
  }

  get selectClient() {
    return this.selectClientId
  }
  set selectClient(value: any) {
    const res = value ?? null
    this.$emit('change', res)
  }

  created() {
    this.getClients()
  }
  doRemoteMethod(value: string) {
    this.getClients(value);
  }
  getClients(value?: string) {
    const params = new ClientFilter()
    params.key = value ?? null;
    params.page = 0
    params.pageSize = 1000
    this.selectLoading = true
    ClientApi.query(params).then(res =>{
      if (res.data) {
        this.clients = res.data
      }
    }).catch((error: any) => {
      this.$message.error(error.message)
    }).finally(() => this.selectLoading = false)
  }


};