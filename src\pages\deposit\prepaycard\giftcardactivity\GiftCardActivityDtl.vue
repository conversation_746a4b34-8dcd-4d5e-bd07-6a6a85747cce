<template>
  <div class="gitf-card-activity-dtl">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button key="1" v-if="['INITAIL', 'UNSTART'].indexOf(detail.body.state) === -1 && hasOptionPermission('/卡/卡活动/电子卡售卡活动', '活动查看')"
          type="primary" @click="gotoEvaluatePage">效果评估
        </el-button>
        <el-button key="2" v-if="detail.body.state === 'INITAIL' && hasOptionPermission('/卡/卡活动/电子卡售卡活动', '活动审核')" type="primary"
          @click="audit(detail.body.activityId)">审核
        </el-button>
        <el-button key="3" @click="copy(detail.body.activityId)" v-if="hasOptionPermission('/卡/卡活动/电子卡售卡活动', '活动维护')">
          复制
        </el-button>
        <el-button key="4"
          v-if="['INITAIL','PROCESSING','UNSTART'].indexOf(detail.body.state) > -1 && hasOptionPermission('/卡/卡活动/电子卡售卡活动', '活动维护')"
          @click="edit(detail.body.activityId)">修改
        </el-button>
        <el-button key="5" v-if="['UNSTART', 'PROCESSING'].indexOf(detail.body.state) > -1 && hasOptionPermission('/卡/卡活动/电子卡售卡活动', '活动审核')"
          @click="stop(detail.body.activityId)">终止
        </el-button>
        <el-button key="6" v-if="detail.body.state === 'INITAIL' && hasOptionPermission('/卡/卡活动/电子卡售卡活动', '活动维护')"
          @click="del(detail.body.activityId)">删除
        </el-button>
      </template>
    </BreadCrume>
    <div style="overflow: auto;height: 95%">
      <el-row v-loading="loading" class="current-page">
        <el-form label-width="100px">
          <div class="panel">
            <div class="content">
              <el-row style="white-space: nowrap">
                <el-col :span="24">
                  <el-row class="banner">
                    <el-col :span="1" style="min-width: 60px;" v-if="detail.detail.themePictureUrls">
                      <img :src="detail.detail.themePictureUrls[0]" style="width: 50px;height: 50px;border-radius: 8px;" />
                    </el-col>
                    <el-col :span="10">
                      <div class="primary" :title="detail.body.name" style="display:flex; align-items: center;">
                        {{ detail.body.name }}
                        <div :style="{backgroundColor: parseStateColor(detail.body.state)}"
                          style="font-size: 13px;padding: 0 6px;color:#fff;height:22px;line-height: 22px;border-radius:3px;margin-left: 5px;">
                          {{parseState(detail.body.state)}}</div>
                      </div>
                      <el-row class="secondary">
                        <span>活动号： </span>{{ detail.body.activityId }}
                      </el-row>
                    </el-col>
                  </el-row>
                  <div style="background:#f8f9fd;border-radius:10px;padding: 12px;">
                    <div style="height: 25px;line-height: 25px;color:rgba(51, 51, 51, 0.6470588235);">
                      {{formatI18n('/营销/积分活动/积分活动/商品满额加送积分/新建页面/活动信息/活动主题')}}
                    </div>
                    <div style="font-size: 25px;color: #333;height: 35px;line-height: 35px;">
                      {{detail.body.topicName || '--'}}
                    </div>
                  </div>
                  <el-row>
                    <el-form-item label="活动时间：">
                      <el-col :span="18" style="display: flex;">
                        <div>
                          {{ detail.body.beginDate|dateFormate2 }}
                          {{ formatI18n('/营销/积分活动/积分活动/商品满额加送积分/新建页面/活动信息/至') }}
                          {{ detail.body.endDate|dateFormate2 }}
                        </div>
                        <DateTimeConditionDtl style="margin-left: 30px" :value="detail.detail.dateTimeCondition"></DateTimeConditionDtl>
                      </el-col>
                    </el-form-item>
                    <el-form-item :label="formatI18n('/储值/预付卡/卡模板/详情页面/适用门店：')">
                      <ActiveStoreDtl style="width: 60%;min-width: 800px;" :data="detail.body.stores"></ActiveStoreDtl>
                    </el-form-item>
                    <el-form-item label="活动封面：">
                      <img v-for="(pic, index) in detail.detail.themePictureUrls" :key="index" :src="pic"
                        style="width:100px;height:100px;margin-top: 15px;margin-right: 12px;" />
                    </el-form-item>
                    <el-form-item label="祝福语：">
                      <div style="line-height: 25px;margin-top: 8px;max-width: calc(100% - 180px);white-space: normal;" no-i18n
                        v-html="detail.detail.defaultGiftMsg ? detail.detail.defaultGiftMsg.replace(/\n/g,'<br/>'): '--'"></div>
                    </el-form-item>
                    <el-form-item label="活动说明：">
                      <div style="line-height: 25px;margin-top: 8px;max-width: calc(100% - 180px);white-space: normal;" no-i18n
                        v-html="detail.body.remark ? detail.body.remark.replace(/\n/g,'<br/>'): '--'"></div>
                    </el-form-item>
                  </el-row>
                </el-col>
              </el-row>
            </div>
          </div>

          <div class="split"></div>

          <div class="panel">
            <div class="header">
              发售设置
            </div>
            <div class="content">
              <el-form-item label="优惠类型" prop="favType">
                <sapn>{{detail.detail.favType == "discount" ? i18n("折扣") : 
              detail.detail.favType == "amount" ? i18n("指定售价") : ""
              }}</sapn>
              </el-form-item>
              <el-form-item label="卡类型" prop="cardType">
                <sapn>{{detail.detail.cardType | cardType}}</sapn>
              </el-form-item>
              <el-form-item label="卡模板" prop="saleSpecs">
                <ActivePresentCardTable :tableData="detail"></ActivePresentCardTable>
              </el-form-item>
            </div>
          </div>
        </el-form>
      </el-row>
    </div>

    <SelectStoreActiveDtlDialog :data="detail" :parent="couponDialog.parent" :child="couponDialog.child" :dialogShow="couponDialog.dialogShow"
      @dialogClose="doDialogClose"></SelectStoreActiveDtlDialog>
  </div>
</template>

<script lang="ts" src="./GiftCardActivityDtl.ts">
</script>

<style lang="scss">
.gitf-card-activity-dtl {
  width: 100%;
  height: 100%;
  background-color: white;
  overflow: hidden;

  a {
    cursor: pointer;
  }

  .current-page {
    height: calc(100% - 18px) !important;
    overflow-y: scroll;

    .banner {
      .primary {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        font-weight: 500;
        font-style: normal;
        font-size: 20px;
        color: #515151;

        .state {
          width: 7px;
          height: 7px;
          border-radius: 10px;
          float: left;
          margin-top: 11px;
          margin-right: 11px;
        }
      }

      .secondary {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        color: rgba(51, 51, 51, 0.647058823529412);
      }
    }

    .panel {
      .header {
        font-weight: 500;
        padding: 20px 20px 0 20px;
        font-size: 18px;
        font-weight: 600;
      }

      .content {
        padding: 20px;

        .el-range-separator {
          padding: 0;
          line-height: 25px;
        }

        .el-form-item {
          margin-bottom: 0;
        }

        .el-form-item__label {
          white-space: normal;
        }

        .face-amount-spec {
          .title-row {
            padding: 0 20px;

            .title {
              font-size: 18px;
              font-weight: 500;

              .amount {
                font-weight: 600;
              }
            }
          }

          .el-row {
            margin-top: 15px;
            background-color: #f9f9f9;
          }

          .el-form-item {
            margin-bottom: 0;
          }
        }
      }
    }

    .split {
      height: 20px;
      background-color: #eeeff1;
    }
  }
}
</style>
<style>
.gitf-card-activity-dtl .el-form-item__label {
  text-align: left;
  color: rgba(51, 51, 51, 0.6470588235);
}
</style>
