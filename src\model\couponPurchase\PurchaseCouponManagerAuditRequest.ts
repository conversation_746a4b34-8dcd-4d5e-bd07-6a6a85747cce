/*
 * @Autor: 司浩
 * @Description:
 * @Date: 2023-01-09 13:31:39
 * @LastEditors: 司浩
 * @LastEditTime: 2023-02-13 15:11:34
 * @FilePath: \phoenix-web-ui\src\model\couponPurchase\PurchaseCouponManagerAuditRequest.ts
 */
import BPurchaseCouponManagerAuditInfos from 'model/couponPurchase/BPurchaseCouponManagerAuditInfos'

export default class PurchaseCouponManagerAuditRequest {
  // 审核交易记录
  transIds: BPurchaseCouponManagerAuditInfos[] = []
  // 审核结果， true 审核通过，false 审核不通过
  audit: Nullable<boolean> = null
  // 审核结果备注
  remark: Nullable<string> = null
}
