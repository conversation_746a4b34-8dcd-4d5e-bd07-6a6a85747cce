/*
 * @Author: 黎钰龙
 * @Date: 2024-03-15 09:51:59
 * @LastEditTime: 2024-03-15 16:14:45
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\promotion\BCumulativeConsumeActivity.ts
 * 记得注释
 */
import BaseCouponActivity from 'model/coupon/activity/BaseCouponActivity'
import CouponItem from 'model/common/CouponItem'
import PushGroup from 'model/precisionmarketing/pushplan/PushGroup'

// 累计消费有礼
export default class BCumulativeConsumeActivity extends BaseCouponActivity {
  // 累计消费金额
  consumeAmount: Nullable<number> = null
  // 老会员券礼包
  oldMemberCoupons: CouponItem[] = []
  // 资源券
  sourceCoupon: Nullable<CouponItem> = null
  // 目标券
  targetCoupon: Nullable<CouponItem> = null
  // 参与人群
  rule: Nullable<PushGroup> = null
}