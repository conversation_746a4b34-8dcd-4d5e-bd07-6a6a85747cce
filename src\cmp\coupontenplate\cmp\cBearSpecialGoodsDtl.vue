<!--
 * @Author: mazheng<PERSON> <EMAIL>
 * @Date: 2023-02-22 16:38:25
 * @LastEditors: 黎钰龙
 * @LastEditTime: 2025-05-13 14:27:48
 * @FilePath: \phoenix-web-ui\src\cmp\coupontenplate\cmp\cBearSpecialGoodsDtl.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-dialog append-to-body :close-on-click-modal="false" :title="i18n('特殊商品')" :visible.sync="dialogShow">
    <div style="min-width: 500px;">
      <!-- <el-row class="sgdHeader">
        <el-col :span="5" class="sgdCell">{{ i18n('商品条码') }}</el-col>
        <el-col :span="19" class="sgdCell">{{ i18n('用券记录方式') }}</el-col>
      </el-row> -->
      <table class="tableHeader">
        <tr class="sgdHeader">
          <td>
            {{ i18n('商品条码') }}
          </td>
          <td>
            {{ formatI18n('/公用/券模板', '券承担方') }}
          </td>
        </tr>
      </table>
      <div class="tableDataCon">
        <table class="tableBody">
          <tr v-for="(item, index) in data" :key="index" class="sgdline">
            <td>
              {{ item.barcode }}
            </td>
            <td v-html="setAmounStr(item)">
            </td>
            <!-- {{ setAmounStr(item) }} -->
          </tr>
        </table>
      </div>
    </div>
  </el-dialog>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import I18nPage from "common/I18nDecorator";
import RSCostParty from "model/common/RSCostParty";

@Component({
  name: "SpecialGoodsDialog",
  components: {},
})
@I18nPage({
  prefix: ["/公用/券模板"],
  auto: true,
})
export default class SpecialGoodsDialog extends Vue {
  dialogShow: boolean = false;
  @Prop()
  data: any[];
  @Prop({ default: [] }) parties: RSCostParty[];

  open() {
    this.dialogShow = true;
  }
  get setAmounStr() {
    return (item: any) => {
      if (item.costPartyDetails && item.costPartyDetails.length == 1 && item.costPartyDetails[0].value == 0) {
        return `<div style="padding-left: 12px;text-align: left;">${this.getPartyNameById(item.costPartyDetails[0].party)} ${this.i18n("承担")} ${this.i18n(
          "全部券抵扣金额",
        )}</div>`;
      }
      if (item.costPartyDetails && item.costPartyDetails.length > 0) {
        let str: string = "";
        item.costPartyDetails.forEach((ele: any) => {
          if (ele.value > 0) {
            str += `<div style="padding-left: 12px;text-align: left;">${this.getPartyNameById(ele.party)} ${this.i18n("承担")} ${this.i18n("最多")} ${
              ele.value
            } ${this.formatI18n("/公用/券模板/元")}</div>`;
          }
          if (ele.value == -1) {
            str += `<div style="padding-left: 12px;text-align: left;">${this.getPartyNameById(ele.party)} ${this.i18n("承担")} ${this.i18n("剩余券抵扣金额")}</div>`;
          }
        });
        return str;
      }
    };
  }

  getPartyNameById(id: string) {
    let str = "";
    if (this.parties && this.parties.length > 0) {
      this.parties.forEach((item: any) => {
        if (item.costParty.id === id) {
          str = item.costParty.name;
        }
      });
    }
    return str;
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog {
  min-width: 1000px;
}
.tableHeader,
.tableBody {
  width: 100%;
  border-collapse: collapse;
  .sgdHeader,
  .sgdline {
    text-align: center;
    line-height: 30px;
  }
  .sgdline {
    &:nth-child(1) {
      td {
        border-top: 0;
      }
    }
    td {
      border-right: 0;
    }
  }
  td {
    border: 1px solid #aaa;
    &:nth-child(1) {
      width: 15%;
    }
    &:nth-child(2) {
      width: 85%;
    }
  }
}
.sgdHeader {
  // margin-top: 10px;
  border-right: 1px solid #aaa;
  border-top: 1px solid #aaa;
}
.sgdCell {
  line-height: 30px;
  text-align: center;
  border-left: 1px solid #aaa;
  border-top: 1px solid #aaa;
}
.tableDataCon {
  border-right: 1px solid #aaa;
  max-height: 500px;
  //overflow: auto;
  overflow: overlay;
  //border-bottom: 1px solid #aaa;
  .sgdCell {
    //height: 36px;
    line-height: 36px;
  }
}
</style>