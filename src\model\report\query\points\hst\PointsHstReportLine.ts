import MemberIdent from "model/common/member/MemberIdent";

export default class PointsHstReportLine extends MemberIdent {
  // 发生组织
  occurredOrg: Nullable<string> = null
  // 发生积分
  occurred: Nullable<number> = null
  // 动作
  action: Nullable<string> = null
  // 交易号
  transNo: Nullable<string> = null
  // 原积分余额
  originalBalance: Nullable<number> = null
  // 活动名称
  activityName: Nullable<string> = null
  // 过期时间
  overdueTime: Nullable<Date> = null
  // 交易时间
  tranTime: Nullable<Date> = null
}