/*
 * @Author: 黎钰龙
 * @Date: 2022-11-29 11:12:17
 * @LastEditTime: 2024-07-29 13:38:20
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\open-promotion\new-modify-activity\NewModifyActivity.ts
 * 记得注释
 */
import NewModifyBody from 'model/common/ActivityBody'
import UseTimeRange from "model/common/DateTimeRange";
import WeixinCouponBrandContent from './CouponBrandContent'
import PushGroup from "model/precisionmarketing/pushplan/PushGroup";  //客群
import GradesRange from 'model/common/GradeRange';
import JumpPageInfo from 'model/navigation/JumpPageInfo';

//新建开屏推广活动接口的参数
export default class NewModifyActivity {
  //预告天数
  advanceDay: Nullable<number> = null
  //活动信息
  body: NewModifyBody = new NewModifyBody()
  //推广内容类型,可用值:WEIXIN_COUPON_ACTIVITY
  brandType: Nullable<string> = null
  //限量时间类型，DAY——每天；WEEK——每周,MONTH——每月，YEAR——每年;,可用值:DAY,WEEK,MONTH,YEAR
  dateLimitType: Nullable<string> = null
  //参与会员等级
  gradeRange: Nullable<GradesRange> = null;
  //true表示参与叠加促销，false表示不参与叠加促销
  joinPromotion: Nullable<Boolean> = false
  // true表示保存或修改，false表示保存并审核
  justSave: Nullable<Boolean> = false
  //营销中心
  marketingCenter: Nullable<string> = null
  //活动总量
  maxIssueTimes: Nullable<Number> = null
  //每人每天/周/月限量
  maxPerDateRangeIssueTimes: Nullable<Number> = null
  //每人限量
  maxPerIssueTimes: Nullable<Number> = null
  //非会员是否参与发券
  nonmemberIssue: Nullable<Boolean> = false
  //投放方式,可用值:GET_COUPON_CENTER,LINK_CAN_SHARE,LINK_NOT_SHARE
  releaseType: Nullable<string> = null
  //参与人群
  rule: PushGroup = new PushGroup()
  //显示排序
  sequence: Nullable<Number> = null
  //子活动类型
  subType: Nullable<string> = null
  //适用时段
  useTimeRange: UseTimeRange = new UseTimeRange()
  //推广内容（领微信券活动）
  weixinCouponBrandContents: WeixinCouponBrandContent[] = []
  // 推送频率
  pushFrequency:Nullable<string> = "EVERY_DAY"
  // 关闭规则
  windowClosedType:Nullable<string> = "CLOSED_BY_USER"
  // 关闭时间
  windowClosedTime:Nullable<Number> = null
  // 单张图片url
  singlePictureBrandContents:Nullable<string>
  // 跳转链接参数
  jumpPageInfo: Nullable<JumpPageInfo> = null
}