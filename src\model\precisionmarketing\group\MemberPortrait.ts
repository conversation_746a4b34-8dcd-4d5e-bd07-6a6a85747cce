import MetricItem from 'model/precisionmarketing/group/MetricItem'

export default class MemberPortrait {
  // 会员数
  memberCount: Nullable<number> = null
  // 总消费金额
  totalTradeAmount: Nullable<number> = null
  // 人均消费金额
  avgTradeAmount: Nullable<number> = null
  // 总消费次数
  totalTradeQty: Nullable<number> = null
  // 人均消费次数
  avgTradeQty: Nullable<number> = null
  // 最近30天复购率
  rePurseRate: Nullable<number> = null
  // 性别
  genderData: MetricItem[] = []
  // 年龄
  ageData: MetricItem[] = []
  // 所在地区
  areaData: MetricItem[] = []
  // 会员等级
  gradeData: MetricItem[] = []
  // 注册时间所在周期
  registerData: MetricItem[] = []
  // 注册渠道
  channelData: MetricItem[] = []
  // 招募方式
  registerSceneData: MetricItem[] = []
}