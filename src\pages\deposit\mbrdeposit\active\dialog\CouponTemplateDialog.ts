import {Component, Prop, Vue, Watch} from 'vue-property-decorator'
import CouponItem from 'model/common/CouponItem'
import CouponTemplateWrap from 'cmp/coupontenplate/CouponTemplateWrap.vue'
import CouponInfo from 'model/common/CouponInfo'
import ValidityInfo from 'model/common/ValidityInfo'
import DateTimeRange from 'model/common/DateTimeRange'
import StoreRange from 'model/common/StoreRange'
import GoodsRange from 'model/common/GoodsRange'
import CouponThreshold from 'model/common/CouponThreshold'
import SubjectApportion from 'model/common/SubjectApportion'
import CashCouponAttribute from 'model/common/CashCouponAttribute'
import DiscountCouponAttribute from 'model/common/DiscountCouponAttribute'
import PickUpCouponAttribute from 'model/common/PickUpCouponAttribute'

@Component({
  name: 'CouponTemplateDialog',
  components: {
    CouponTemplateWrap
  }
})
export default class CouponTemplateDialog extends Vue {
  coupon: CouponItem = new CouponItem()
  $refs: any
  @Prop()
  state: string
  @Prop({
    default: false,
    type: Boolean
  })
  isShowGoodsDiscount: boolean // 是否展示商品折扣券

    @Prop()
    data: any

    @Prop()
    couponInfo: any // 来源 add/edit/copy

    @Prop({
        type: Boolean,
        default: false
    })
    dialogShow: boolean

    @Watch('dialogShow')
    onDialogShowCHange(value: boolean) {
      // if (value) {
      //   if (this.couponInfo && this.couponInfo.from === 'add') {
      //     this.coupon = new CouponItem()
      //   }
      // }
    }
  doBeforeClose(done: any) {
    if (this.$refs && this.$refs.couponTemplate) {
      if (this.$refs.couponTemplate.$refs && this.$refs.couponTemplate.$refs.allCashCoupon) {
        if (this.$refs.couponTemplate.$refs.allCashCoupon.$refs && this.$refs.couponTemplate.$refs.allCashCoupon.$refs.ruleForm) {
          this.$refs.couponTemplate.$refs.allCashCoupon.$refs.ruleForm.resetFields()
        }
      }
    }
    this.$emit('dialogClose')
    done()
  }
  doModalClose() {
    let arr = []
    arr.push(this.$refs.couponTemplate.doValidate())
    Promise.all(arr).then(() => {
      this.$emit('params', this.coupon, this.coupon.coupons!.name, this.couponInfo)
      this.$emit('dialogClose')
    })
  }
  setCouponTemplate(data: CouponItem) {
    this.coupon = data
  }
  setResetCouponTemplate() {
    this.coupon = new CouponItem()
    this.coupon.coupons = new CouponInfo()
    this.coupon.coupons.couponBasicType = 'all_cash' as any
    this.coupon.coupons.validityInfo = new ValidityInfo()
    this.coupon.coupons.validityInfo.validityType = 'RALATIVE' as any
    this.coupon.coupons.useTimeRange = new DateTimeRange()
    this.coupon.coupons.useStores = new StoreRange()
    this.coupon.coupons.useGoods = new GoodsRange()
    this.coupon.coupons.useThreshold = new CouponThreshold()
    this.coupon.coupons.useApporion = new SubjectApportion()
    this.coupon.coupons.cashCouponAttribute = new CashCouponAttribute()
    this.coupon.coupons.discountCouponAttribute = new DiscountCouponAttribute()
    this.coupon.coupons.pickUpCouponAttribute = new PickUpCouponAttribute()
    if (this.$refs && this.$refs.couponTemplate) {
      if (this.$refs.couponTemplate.$refs && this.$refs.couponTemplate.$refs.allCashCoupon) {
        if (this.$refs.couponTemplate.$refs.allCashCoupon.$refs && this.$refs.couponTemplate.$refs.allCashCoupon.$refs.ruleForm) {
          this.$refs.couponTemplate.$refs.allCashCoupon.$refs.ruleForm.resetFields()
        }
      }
    }
  }
}
