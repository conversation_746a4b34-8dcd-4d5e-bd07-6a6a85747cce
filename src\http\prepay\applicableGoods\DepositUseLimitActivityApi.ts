import ApiClient from 'http/ApiClient'
import BDepositUseLimitActivity from "model/deposit/store/BDepositUseLimitActivity";
import Response from 'model/common/Response'
import MarketingCenter from "pages/datum/marketingcenter/MarketingCenter";

export default class DepositUseLimitActivityApi {
  /**
   * 根据类型查询活动
   * 根据类型查询活动。
   *
   */
  static byType(body?: MarketingCenter): Promise<Response<BDepositUseLimitActivity>> {
    return ApiClient.server().post(`/v1/deposit/use/limit/byType`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询活动
   * 查询活动。
   *
   */
  static info(activityId: string): Promise<Response<BDepositUseLimitActivity>> {
    return ApiClient.server().get(`/v1/deposit/use/limit/info/${activityId}`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存储值适用商品配置信息
   * 保存储值适用商品配置信息。
   *
   */
  static saveOrModify(body: BDepositUseLimitActivity): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/deposit/use/limit/saveOrModify`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
