<template>
  <el-popover
    class="search-remote"
    trigger="hover"
    ref="popover"
    placement="top"
    :disabled="popoverDisabled"
  >
    <div slot="reference" :style="{ width: width, minWidth: minWidth }">
      <el-select
        :class="className"
        v-model="localValue"
        :reserve-keyword="reserveKeyword"
        :filterable="filterable"
        remote
        :size="size"
        :multiple="multiple"
        :placeholder="placeholder"
        :disabled="disabled"
        :clearable="clearable"
        :loading="loading"
        :remote-method="remoteMethod"
        :loading-text="loadingText"
        :no-data-text="noDataText"
        :popper-append-to-body="popperAppendToBody"
        :default-first-option="defaultFirstOption"
        :multiple-limit="multipleLimit"
        :popper-class="popperClass"
        :no-match-text="noMatchText"
        :collapse-tags="collapseTags"
        :value-key="valueKey"
        @focus="onFocus"
        @visible-change="onVisibleChange"
        ref="select"
      >
        <el-option
          v-for="item in options"
          :key="item[valueKey]"
          :value="optionValue(item)"
          :label="optionLabel(item)"
        >
          <slot :option="item"></slot>
        </el-option>
        <template v-slot:prefix>
          <!-- Select 组件头部内容 -->
          <slot name="prefix"></slot>
        </template>
        <template v-slot:empty>
          <!-- 	无选项时的列表 -->
          <slot name="empty"></slot>
        </template>
      </el-select>
    </div>
    <div class="popover-box" v-if="!popoverDisabled">
      <div :key="item" v-for="item in selectLabels">
        {{ item }}
      </div>
    </div>
  </el-popover>
</template>

<script src="./SearchRemote.ts"></script>

<style lang="scss" scoped>
.search-remote {
  ::v-deep .el-select__tags-text {
    display: inline-block;
    max-width: 135px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  ::v-deep .el-tag__close.el-icon-close {
    top: -6px;
  }
}
</style>
