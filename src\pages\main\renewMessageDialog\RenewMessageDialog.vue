<!--
 * @Author: 黎钰龙
 * @Date: 2024-01-31 09:55:16
 * @LastEditTime: 2024-03-05 11:48:25
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\main\renewMessageDialog\RenewMessageDialog.vue
 * 记得注释
-->
<template>
  <el-dialog :before-close="doBeforeClose" :close-on-click-modal="false" :title="i18n('/公用/系统/上导航/消息提醒')" :visible="dialogShow"
    class="renew-message-dialog" width="600px">
    <div class="msg-content-item" v-for="(item,index) in (loginInfo.expirationFunctionPackages || [])" :key="index">
      {{index + 1}}、{{getMessageStr(item)}}
    </div>
    <div class="msg-content-item">
      {{i18n('过期后平台券不能核销，请联系客户经理及时跟海鼎续费平台券')}}
    </div>
    <div class="btn-block">
      <el-button type="primary" @click="close">{{i18n('知道了')}}</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import I18nPage from "common/I18nDecorator";
import FunctionPackagesExpireInfo from "model/login/FunctionPackagesExpireInfo";
import UserLoginResult from "model/login/UserLoginResult";
import DateUtil from "util/DateUtil";
import { Component, Vue } from "vue-property-decorator";
import { State } from "vuex-class";
@Component({
  name: "RenewMessageDialog",
  components: {},
})
@I18nPage({
  prefix: ["/公用/券模板", "/公用/系统/续费提醒"],
  auto: true,
})
export default class RenewMessageDialog extends Vue {
  @State("loginInfo") loginInfo: UserLoginResult;
  dialogShow: boolean = false;

  open() {
    this.dialogShow = true;
  }

  close() {
    this.dialogShow = false;
  }

  getMessageStr(item: FunctionPackagesExpireInfo) {
    let str = item.expired ? this.formatI18n("/公用/系统/续费提醒/您购买的{0}服务已于{1}过期") : this.formatI18n("/公用/系统/续费提醒/您购买的{0}服务将于{1}过期");
    str = str.replace(/\{0\}/g, item.name!);
    str = str.replace(/\{1\}/g, DateUtil.format(item.expiration, "yyyy-MM-dd"));
    return str;
  }

  doBeforeClose(done: any) {
    this.dialogShow = false;
    done();
  }
}
</script>

<style lang="scss">
.renew-message-dialog {
  overflow: auto;
  .msg-content-item {
    font-size: 12px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #42464d;
    line-height: 18px;
    margin-bottom: 8px;
  }
  .btn-block {
    width: 100%;
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
  .el-dialog__title {
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    color: #020203;
  }
}
</style>