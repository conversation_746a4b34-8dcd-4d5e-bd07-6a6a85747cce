<template>
  <span no-i18n>
    <template v-if="hasNamedSlot">
      <template v-for="(item, index) of items">
        <span>{{ item.prefix }}</span>
        <slot :name="item.slotName" :index="index" :prefix="item.prefix" :suffix="item.suffix"></slot>
        <span v-if="index === items.length - 1">{{ item.suffix }}</span>
      </template>
    </template>
    <slot :items="itemsObj" v-else></slot>
  </span>
</template>

<script lang='ts' src='./i18n.ts'/>