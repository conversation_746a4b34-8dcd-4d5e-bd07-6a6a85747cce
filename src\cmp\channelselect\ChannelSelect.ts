/*
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:02
 * @LastEditTime: 2024-06-03 15:38:27
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\channelselect\ChannelSelect.ts
 * 记得注释
 */
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import ChannelManagementApi from "http/channelmanagement/ChannelManagementApi";
import { ChannelState } from "model/channel/ChannelState";
import ChannelManagementFilter from "model/channel/ChannelManagementFilter";
import ChannelManagement from "model/channel/ChannelManagement";
import Channel from "model/common/Channel";

@Component({
  name: 'ChannelSelect',
  components: {
  },
  model: {
    prop: 'value',
    event: 'change'
  }
})
export default class ChannelSelect extends Vue {
  channels: ChannelManagement[] = []
  channelMap: Map<string, ChannelManagement> = new Map<string, ChannelManagement>();
  @Prop() value: Channel[]
  @Prop({ default: false }) hideHdPos: boolean
  @Prop({ default: false }) hideWechetChannel: boolean
  @Prop({ default: false }) disabled: boolean
  @Prop({ default: false }) multiple: boolean
  @Prop({ default: (() => { return [] }) }) filterHideId: string[];  //不展示在选项中的渠道
  @Prop({ type: String }) placeholder: string;
  @Prop({ type: Boolean, default: false }) isShowAll: boolean;
  @Prop({ type: Boolean, default: true }) clearable: boolean;
  @Prop({ type: String, default: '300px'}) width: string;
  @Prop({ default: () => { return {} }}) appendAttr: Object;

  multiSelected: string[] | string = []
  selected: string | null = null

  @Watch("value", { immediate: true, deep: true })
  onWatchSelected(value: Channel[]) {
    let channels = JSON.parse(JSON.stringify(value))
    if (this.multiple) {
      this.multiSelected = channels.map((e: any) => e.type + e.id)
    } else {
      if (channels && channels.length > 0) {
        this.selected = channels[0].type + channels[0].id
      } else {
        this.selected = null
      }
    }
    this.$forceUpdate()
  }

  created() {
    this.getChannels()
  }

  getChannels() {
    let filter:any = new ChannelManagementFilter();
    filter.stateEquals = ChannelState.ENABLED
    filter = {...filter, ...this.appendAttr}
    ChannelManagementApi.query(filter).then((resp: any) => {
      if (resp && resp.code === 2000) {
        this.channels = resp.data
        if (this.hideHdPos) {
          this.channels = this.channels.filter((channel: any) => !(channel.channel.type === 'store' && channel.channel.id === '-'))
        }
        if (this.hideWechetChannel) {
          this.channels = this.channels.filter((channel: any) => channel.channel.type !== 'weixin')
        }
        for (let channel of this.channels) {
          if (channel.channel && channel.channel.type && channel.channel.id) {
            this.channelMap.set(channel.channel.type + channel.channel.id, channel)
          }
        }
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  onChange() {
    let result = []
    if (this.multiple) {
      for (let item of this.multiSelected) {
        if (this.channelMap.has(item)) {
          let channelMng = this.channelMap.get(item)
          if (channelMng && channelMng.channel) {
            result.push(channelMng.channel)
          }
        }
      }
    } else {
      if (this.selected && this.channelMap.has(this.selected)) {
        let channelMng = this.channelMap.get(this.selected)
        if (channelMng && channelMng.channel) {
          result.push(channelMng.channel)
        }
      }
    }
    this.$forceUpdate()
    this.$emit('change', result)
  }
}