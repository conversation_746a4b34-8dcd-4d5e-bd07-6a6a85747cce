<template>
  <div class="text-font">
    <div class="tips">
      <p class="tips-label">{{ i18n('组件间距') }}</p>
    </div>
    <el-form label-position="left" :model="value" ref="form">
      <el-form-item :label="i18n('字号')" prop="placeTitle" label-width="100px">
        <div class="right">
          <el-radio v-for="item in styFontSizeList" :key="item.lable" :label="item.lable" v-model="value.styFontSize" @change="handleChange">
            {{ item.text }}
          </el-radio>
        </div>
      </el-form-item>
      <el-form-item :label="i18n('字体色')" prop="placeTitle" label-width="100px">
        <div class="right">
          <el-color-picker style="width: 32px" @change="handleChange" v-model="value.styFontColor" size="small"></el-color-picker>
        </div>
      </el-form-item>
      <el-form-item :label="i18n('对齐方式')" prop="placeTitle" label-width="100px">
        <div class="right">
          <el-radio v-for="item in styTextAlignList" :key="item.lable" :label="item.lable" v-model="value.styTextAlign" @change="handleChange">
            {{ item.text }}
          </el-radio>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>
  
  <script lang="ts" src="./TextFont.ts"></script>
  
  <style lang="scss" scoped>
.text-font {
  background: #f0f2f6;
  border-radius: 4px;
  padding: 12px;
  box-sizing: border-box;
  height: 168px;
  margin-bottom: 20px;
  .tips {
    &-label {
      font-weight: 500;
      font-size: 14px;
      color: #24272b;
      line-height: 16px;
      text-align: left;
      margin-bottom: 10px;
    }
  }
}
::v-deep .el-form-item {
  margin-bottom: 0;
}
.el-radio {
  margin-left: 16px;
  margin-right: 0;
}
.right {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 40px;
  line-height: 40px;
}
</style>
  