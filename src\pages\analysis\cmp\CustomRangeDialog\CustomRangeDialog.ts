/*
 * @Author: 黎钰龙
 * @Date: 2025-02-10 10:56:07
 * @LastEditTime: 2025-03-05 15:03:19
 * @LastEditors: haiding <EMAIL>
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\analysis\cmp\CustomRangeDialog\CustomRangeDialog.ts
 * 记得注释
 */
import I18nPage from 'common/I18nDecorator';
import { Component, Prop, Vue } from 'vue-property-decorator';
import CustomRangeForm from './cmp/CustomRangeForm';
import FormItem from 'cmp/formitem/FormItem';
import CustomRangeInfo from 'model/default/CustomRangeInfo';

@Component({
  name: 'CustomRangeDialog',
  components: {
    CustomRangeForm,
    FormItem
  }
})
@I18nPage({
  prefix: [
    '/公用/券模板'
  ],
  auto: true
})
export default class CustomRangeDialog extends Vue {
  $refs: any
  @Prop() rangeList: CustomRangeInfo[];
  @Prop() titleList: string[];
  @Prop({ type: Boolean, default: true }) editable : Boolean
  visible: boolean = false
  list: CustomRangeInfo[] = []

  open(rangeCount: number) {
    this.list = []
    this.$nextTick(() => {
      this.list.length = rangeCount
      this.list.fill(new CustomRangeInfo())
      this.rangeList.forEach((item, index) => {
        if (this.list[index]) {
          this.$set(this.list, index, JSON.parse(JSON.stringify(item)))
        }
      })
      this.visible = true
    })
  }

  // 提交数据
  submitSelectGroupSelections() {
    const promiseArr = []
    if (this.$refs.customRangeForm?.length) {
      // 遍历校验子组件是否合法
      this.$refs.customRangeForm.forEach((dom: any) => {
        promiseArr.push(dom.doValidate())
      })
    } else {
      promiseArr.push(this.$refs.customRangeForm.doValidate())
    }
    Promise.all(promiseArr).then(() => {
      console.log('看看提交的数据', this.list);
      this.$emit('submit', JSON.parse(JSON.stringify(this.list)))
      this.visible = false
    }).catch((err) => {
      this.$message.warning(this.i18n('请将区间填写完整'));
    })
  }

  created() {
    console.log('创建了',this.editable);
  }
};