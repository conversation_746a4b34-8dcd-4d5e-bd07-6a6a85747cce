<template>
  <el-dialog :title="formatI18n('/公用/公共组件/商品详情控件/标题/商品信息')" :visible.sync="visible" width="450px"
             :before-close="doBeforeClose" class="inner-dialog-center">
    <el-row class="goods-info-dialog" v-loading="loading">
      <el-row><span>{{formatI18n('/公用/公共组件/商品详情控件/左标题/商品代码：')}}</span>{{goods.code}}</el-row>
      <el-row><span>{{formatI18n('/公用/公共组件/商品详情控件/左标题/条码：')}}</span>{{goods.barcode}}</el-row>
      <el-row><span>{{formatI18n('/公用/公共组件/商品详情控件/左标题/商品名称：')}}</span>{{goods.name}}</el-row>
      <el-row><span>{{formatI18n('/公用/公共组件/商品详情控件/左标题/规格：')}}</span>{{goods.qpcStr}}</el-row>
      <el-row><span>{{formatI18n('/公用/公共组件/商品详情控件/左标题/单价：')}}</span>{{goods.price}}</el-row>
      <el-row v-if="brand.brand"><span>{{formatI18n('/公用/公共组件/商品详情控件/左标题/品牌：')}}</span>[{{goods.brandId}}]
        {{brand.brand.name}}
      </el-row>
      <el-row v-if="category.category"><span>{{formatI18n('/公用/公共组件/商品详情控件/左标题/品类：')}}</span>[{{goods.categoryId}}]
        {{category.category.name}}
      </el-row>
      <el-row><span>{{formatI18n('/公用/公共组件/商品详情控件/左标题/备注：')}}</span>{{goods.remark}}</el-row>
    </el-row>
    <span slot="footer">
      <el-button type="primary" @click="visible = false">{{formatI18n('/公用/公共组件/商品详情控件/操作按钮/确定')}}</el-button>
    </span>
  </el-dialog>
</template>

<script lang="ts" src="./GoodsInfoDialog.ts">
</script>

<style lang="scss" scoped>
  .inner-dialog-center{
    display: flex;
    align-items: center;
    justify-content: center;
    .goods-info-dialog {
      line-height: 25px;

      span {
        color: rgba(51, 51, 51, 0.64)
      }
    }
  }
</style>