import ApiClient from 'http/ApiClient'
import Response from 'model/default/Response'
import Dou<PERSON>inAuthRequest from "model/douyin/DouYinMemberAuthRequest";

export default class DouYinAuthApi {
  /**
   * 获取抖音授权链接
   */
  static getAuthUrl(body: DouYinAuthRequest): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/douyin/auth/getAuthUrl`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
