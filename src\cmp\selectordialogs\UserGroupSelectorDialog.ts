/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:19
 * @LastEditTime: 2023-02-15 16:59:32
 * @LastEditors: mazheng<PERSON> <EMAIL>
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\selectordialogs\UserGroupSelectorDialog.ts
 * 记得注释
 */
import { Component, Prop } from 'vue-property-decorator'
import FormItem from 'cmp/formitem/FormItem.vue'
import AbstractSelectDialog from './AbstractSelectDialog';
import UserGroupFilter from "model/precisionmarketing/group/UserGroupFilter";
import UserGroupApi from "http/precisionmarketing/group/UserGroupApi";
import UserGroup from "model/precisionmarketing/group/UserGroup";
import I18nPage from "common/I18nDecorator";
import UserGroupDataDict from "pages/member/insight/common/UserGroupDataDict";
import TagDataDict from "pages/member/insight/common/TagDataDict";
import LastExecuteState from "pages/member/insight/cmp/lastexecutestate/LastExecuteState";
import UserGroupMarkFilter from "model/precisionmarketing/group/mark/UserGroupMarkFilter";
import UserGroupMarkApi from "http/precisionmarketing/group/mark/UserGroupMarkApi";
import UserGroupMark from "model/precisionmarketing/group/mark/UserGroupMark";
import { isEqual } from "lodash";

@Component({
  name: 'UserGroupSelectorDialog',
  components: {
    FormItem,
    LastExecuteState,
  }
})
@I18nPage({
  auto: false,
  prefix: [
    '/公用/公共组件/客群选择弹框组件',
  ],
})
export default class UserGroupSelectorDialog extends AbstractSelectDialog<UserGroup> {
  @Prop({
    type: String,
    default: ''
  })
  equalKey: string

  i18n: I18nFunc
  userGroupFilter: UserGroupFilter = new UserGroupFilter()
  sourceMap: any = UserGroupDataDict.getSourceMap()
  stateMap: any = TagDataDict.getStateMap()
  scheduleTypeMap: any = TagDataDict.getScheduleTypeMap()
  lastExecuteStateMap: any = TagDataDict.getLastExecuteStateMap()
  marks: UserGroupMark[] = []

  created() {
    this.initFilter()
    this.getMarks()
  }

  reset() {
    this.userGroupFilter = new UserGroupFilter()
    this.initFilter()
  }

  getId(ins: UserGroup): string {
    return ins.uuid as string;
  }

  getName(ins: UserGroup): string {
    return ins.name as string;
  }

  getResponseData(response: any): any {
    return response.data
  }

  queryFun(): Promise<any> {
    let filter = JSON.parse(JSON.stringify(this.userGroupFilter))
    filter.page = this.page.currentPage - 1
    filter.pageSize = this.page.size
    for (let key of ['sourceEquals', 'scheduleTypeEquals', 'lastExecuteStateEquals', 'stateEquals']) {
      if (filter[key] === 'all') {
        filter[key] = null
      }
    }
    return UserGroupApi.query(filter)
  }

  private getMarks() {
    let filter = new UserGroupMarkFilter()
    UserGroupMarkApi.query(filter).then((res) => {
      if (res.data) {
        this.marks = res.data
      }
    }).catch((error) => {
      if (error && error.message) {
        this.$message.error(error.message)
      }
    })
  }

  //判断两个对象是否全等
  isObjEqual(objArr: any, obj: any) {
    if (this.$listeners['compareMethods']) {  //如果父组件传了自己的比较方法，就用父组件的
      let flag = false
      function callback(isCheck: boolean) {
        flag = isCheck
      }
      this.$emit('compareMethods', objArr, obj, callback);
      return flag
    }
    return objArr.some((item: any) => {
      console.log('客户群',item);
      if (this.equalKey) {
        return item[this.equalKey] == obj[this.equalKey]
      } else {
        return isEqual(item, obj)
      }
    })
  }

  private initFilter() {
    this.userGroupFilter.sourceEquals = 'all'
    this.userGroupFilter.scheduleTypeEquals = 'all'
    this.userGroupFilter.stateEquals = 'Enable'
    this.userGroupFilter.lastExecuteStateEquals = 'Success'
  }
}
