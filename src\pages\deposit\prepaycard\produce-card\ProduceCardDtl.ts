import BreadCrume from 'cmp/bread-crumb/BreadCrume';
import FormItem from 'cmp/formitem/FormItem';
import I18nPage from 'common/I18nDecorator';
import MakeCardBillApi from 'http/prepay/card/MakeCardBillApi';
import MakeCardBill from 'model/prepay/card/MakeCardBill';
import { Component, Vue } from 'vue-property-decorator';
import xss from 'xss';
import CardTplItem from '../cmp/cardtplitem/CardTplItem';
import MemberOptLogDrawer from 'pages/member/data/drawer/MemberOptLogDrawer';
import DownloadCenterDialog from 'pages/main/dowmload_center_dialog/DownloadCenterDialog';
import MakeCardMessage from 'model/prepay/card/MakeCardMessage';
import MakeCardMessageFilter from 'model/prepay/card/MakeCardMessageFilter';
import MakeCardFailReason from 'model/prepay/card/MakeCardFailReason';
import MakeCardFailReasonFilter from 'model/prepay/card/MakeCardFailReasonFilter';
import PrepayAccountApi from 'http/prepay/account/PrepayAccountApi';
@Component({
  name: 'ProduceCardDtl',
  components: {
    BreadCrume,
    FormItem,
    CardTplItem,
    MemberOptLogDrawer,
    DownloadCenterDialog
  }
})
@I18nPage({
  prefix: [
    '/公用/按钮',
    '/公用/券模板',
    '/卡/卡管理/制卡单/制卡单列表',
    '/卡/卡管理/制卡单/制卡单详情',
    '/会员/洞察/标签管理/列表页',
    '/资料/品牌',
    '/公用/菜单',
    '/储值/预付卡/预付卡充值单',
    '/会员/洞察/公共/最近消费属性',
    '/公用/券核销',
    '/公用/活动/提示信息',
    '/营销/券礼包活动/新建注册发大礼包/赠送券',
    '/公用/活动/状态'
  ],
  auto: true
})
export default class ProduceCardDtl extends Vue {
  dtl: MakeCardBill = new MakeCardBill()
  xss: Function = xss
  $refs: any
  logsDialogVisible: boolean = false  //是否展示操作日志dialog
  fileDialogVisible: boolean = false
  failDialogVisible: boolean = false  //是否展示失败明细dialog
  cardAttributeFix: boolean = false //是否隐藏 适用商品和发生门店
  showTip: boolean = false
  makeTableData: MakeCardMessage[] = [] //制卡信息
  makeTablePage: any = {
    page: 1,
    pageSize: 10,
    total: 0
  }
  makeFailData: MakeCardFailReason[] = [] //制卡失败明细
  makeFailPage: any = {
    page: 1,
    pageSize: 10,
    total: 0
  }
  panelArray: any = [
    {
      name: this.i18n('制卡单'),
      url: 'produce-card-list'
    },
    {
      name: this.i18n('制卡单详情'),
      url: ''
    }
  ]
  created() {
    this.getConfig()
    this.getDtl()
  }

  getDtl() {
    MakeCardBillApi.get(this.$route.query.billNumber as string).then((res) => {
      if (res.code === 2000) {
        this.dtl = res.data || new MakeCardBill()
        this.getMakeInfo()
      } else {
        this.$message.error(res.msg || this.i18n('获取制卡单详情失败'))
      }
    }).catch((error) => {
      this.$message.error(error.message || this.i18n('获取制卡单详情失败'))
    })
  }

  private getConfig() {
    PrepayAccountApi.cardAttributeFix().then((res) => {
      this.cardAttributeFix = res.data || false
    })
  }

  getMakeInfo() { //获取制卡信息
    const params = new MakeCardMessageFilter()
    params.billNumberEquals = this.dtl.billNumber
    params.page = this.makeTablePage.page - 1
    params.pageSize = this.makeTablePage.pageSize
    MakeCardBillApi.queryMessage(params).then((res) => {
      if (res.code === 2000) {
        this.makeTableData = res.data || []
        this.makeTablePage.total = res.total
      } else {
        this.$message(res.msg || this.i18n('查询已制卡信息失败'))
      }
    }).catch((error) => this.$message.error(error.message || this.i18n('查询已制卡信息失败')))
  }

  //导出卡密
  doExportPwd() {
    function doExport(_this: any) {
      MakeCardBillApi.exportCard(_this.dtl.billNumber!).then((res) => {
        if (res?.code === 2000) {
          _this.exportAfter();
        } else {
          _this.$message.error(res.msg || _this.i18n('导出失败'))
        }
      }).catch(error => _this.$message.error(error.message || _this.i18n('导出失败')))
    }
    if (this.dtl.exportFinish) {
      this.$confirm(
        this.i18n("本操作不是首次导出，原有的密码将失效，以本次导出密码为准，确定导出卡密吗？"),
        this.i18n("导出卡密"),
        {
          confirmButtonText: this.formatI18n("/公用/按钮", "确定"),
          cancelButtonText: this.formatI18n("/公用/按钮", "取消"),
        }
      ).then(() => {
        doExport(this)
      });
    } else {
      doExport(this)
    }
  }

  //查看加密密码
  doViewPwd() {
    MakeCardBillApi.getExcelPassword(this.dtl.billNumber!).then((res) => {
      if (res.code === 2000) {
        this.$alert(this.i18n('当前制卡单密码为：') + res.data, this.i18n('查看加密密码'), { confirmButtonText: this.i18n('确定') })
      } else {
        this.$message.error(res.msg || this.i18n('获取卡密失败'))
      }
    }).catch(error => this.$message.error(error.message || this.i18n('获取卡密失败')))
  }

  doViewLogs() {
    this.logsDialogVisible = true
  }

  //审核
  doAudit() {
    this.$confirm(
      this.i18n("确定要审核当前制卡单吗？"),
      this.i18n("审核"),
      {
        confirmButtonText: this.formatI18n("/公用/按钮", "确定"),
        cancelButtonText: this.formatI18n("/公用/按钮", "取消"),
      }
    ).then(() => {
      MakeCardBillApi.audit(this.dtl.billNumber!).then((res) => {
        if (res.code === 2000) {
          this.$message.success(this.i18n('操作成功'))
          this.getDtl()
        } else {
          this.$message.error(res.msg || this.i18n('操作失败'))
        }
      }).catch((error) => {
        this.$message.error(error.message || this.i18n('操作失败'))
      })
    });
  }

  doEdit() {
    this.$router.push({
      name: 'produce-card-edit',
      query: {
        editType: 'edit',
        billNumber: this.dtl.billNumber
      }
    })
  }

  doRemove() {
    this.$confirm(
      this.i18n("确定要删除当前制卡单吗？"),
      this.i18n("删除"),
      {
        confirmButtonText: this.formatI18n("/公用/按钮", "确定"),
        cancelButtonText: this.formatI18n("/公用/按钮", "取消"),
      }
    ).then(() => {
      MakeCardBillApi.remove(this.dtl.billNumber!).then((res) => {
        if (res.code === 2000) {
          this.$message.success(this.i18n('操作成功'))
          this.$router.push({
            name: 'produce-card-list'
          })
        } else {
          this.$message.error(res.msg || this.i18n('操作失败'))
        }
      }).catch((error) => {
        this.$message.error(error.message || this.i18n('操作失败'))
      })
    });
  }

  doCancel() {
    this.$confirm(
      this.i18n("作废将使本次单据生成的卡密失效"),
      this.i18n("作废"),
      {
        confirmButtonText: this.formatI18n("/公用/按钮", "确定"),
        cancelButtonText: this.formatI18n("/公用/按钮", "取消"),
      }
    ).then(() => {
      MakeCardBillApi.cancel(this.dtl.billNumber!).then((res) => {
        if (res.code === 2000) {
          this.$message.success(this.i18n('操作成功'))
          this.getDtl()
        } else {
          this.$message.error(res.msg || this.i18n('操作失败'))
        }
      }).catch((error) => {
        this.$message.error(error.message || this.i18n('操作失败'))
      })
    });
  }

  doMakeFinish() {
    let msg = this.i18n('制卡完成，则本单据所有卡将为"已制卡"状态，卡可以进行发售')
    if ((this.dtl.cardMedium === 'mag' && this.dtl.writeCardType === 'SYS') || this.dtl.cardMedium === 'rfic') {
      msg = this.i18n('制卡完成，则本单据所有卡将为"空白卡"状态，可以操作写卡')
    }
    this.$confirm(
      msg,
      this.i18n("制卡完成"),
      {
        confirmButtonText: this.formatI18n("/公用/按钮", "确定"),
        cancelButtonText: this.formatI18n("/公用/按钮", "取消"),
      }
    ).then(() => {
      MakeCardBillApi.finish(this.dtl.billNumber!).then((res) => {
        if (res.code === 2000) {
          this.$message.success(this.i18n('操作成功'))
          this.getDtl()
        } else {
          this.$message.error(res.msg || this.i18n('操作失败'))
        }
      }).catch(error => this.$message.error(error.message || this.i18n('操作失败')))
    });
  }

  //查看制卡失败明细
  doViewFailNum() {
    this.failDialogVisible = true
    this.getFailReason()
  }

  getFailReason() {
    const params = new MakeCardFailReasonFilter()
    params.billNumberEquals = this.dtl.billNumber
    params.page = this.makeFailPage.page - 1
    params.pageSize = this.makeFailPage.pageSize
    MakeCardBillApi.queryFailReason(params).then((res) => {
      if (res.code === 2000) {
        this.makeFailData = res.data || []
        this.makeFailPage.total = res.total
      } else {
        this.$message.error(res.msg || this.i18n('查询明细失败'))
      }
    }).catch(error => this.$message.error(error.message || this.i18n('查询明细失败')))
  }

  exportAfter() {
    this.showTip = true;
    this.fileDialogVisible = true;
  }

  doDownloadDialogClose() {
    this.showTip = false;
    this.fileDialogVisible = false;
    this.getDtl()
  }

  getWriteCardType(type: string) {
    switch (type) {
      case 'NONE':
        return this.i18n('无需写卡')
      case 'SYS':
        return this.i18n('商家写卡')
      case 'MANUFACTOR':
        return this.i18n('厂家写卡')
      default:
        return '--'
    }
  }


  onHandleCurrentChange(val: number) {
    this.makeTablePage.page = val
    this.getMakeInfo()
  }

  onHandleSizeChange(val: number) {
    this.makeTablePage.pageSize = val
    this.getMakeInfo()
  }

  onHandleFailCurrentChange(val: number) {
    this.makeTablePage.page = val
    this.getFailReason()
  }

  onHandleFailSizeChange(val: number) {
    this.makeTablePage.pageSize = val
    this.getFailReason()
  }

};