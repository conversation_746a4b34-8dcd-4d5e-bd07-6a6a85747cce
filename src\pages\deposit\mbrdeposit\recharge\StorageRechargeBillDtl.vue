<template>
  <div class="storage-recharge-dtl">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button type="primary" v-if="dtl.state === 'INITIAL' && hasOptionPermission('/储值/储值管理/储值充值单','单据审核') && (!isShouldImport || linePage.total)" @click="doAudit" size="large">
          {{ i18n('/营销/券礼包活动/券礼包活动/审核') }}
        </el-button>
        <el-button @click="doModify" v-if="dtl.state === 'INITIAL' && hasOptionPermission('/储值/储值管理/储值充值单','单据维护')" size="large">
          {{ i18n('/营销/券礼包活动/券礼包活动/修改') }}
        </el-button>
        <el-button @click="doRemove" v-if="dtl.state === 'INITIAL' && hasOptionPermission('/储值/储值管理/储值充值单','单据维护')" size="large">
          {{ i18n('/营销/券礼包活动/券礼包活动/删除') }}
        </el-button>
      </template>
    </BreadCrume>
    <div style="overflow: auto">
      <div class="top-wrap">
        <div class="top-header">
          <div class="header-info">
            <div class="header-title">
              <div class="coupon-name">
                {{ i18n('储值充值详情') }}
              </div>
              <template v-if="dtl.state">
                <div class="state-block" v-if="dtl.state === 'INITIAL'" style="background:#FFAA00">
                  {{ i18n('/公用/活动/状态/未审核') }}
                </div>
                <div class="state-block" v-if="dtl.state === 'ADUIT'" style="background:#0CC66D">
                  {{ i18n('/公用/活动/状态/已审核') }}
                </div>
              </template>
            </div>
            <FormItem :label="i18n('/公用/券核销/单号') + ':'" labelAlign="left" style="margin-top:24px">
              <div style="line-height:36px">{{dtl.billNumber || '--'}}</div>
            </FormItem>
            <div class="import-block" v-if="isShouldImport && !linePage.total">
              <img src="~assets/image/coupon/info.png">
              <div style="flex: 1">
                <div class="import-title">{{i18n('请导入充值会员')}}</div>
                <div class="import-desc">{{i18n('为保障充值成功，每个活动最多支持给20000个会员充值')}}</div>
                <!-- <div class="import-desc" v-else>{{getImportCount}}</div> -->
              </div>
              <el-button @click="doImport" v-if="hasOptionPermission('/储值/储值管理/储值充值单','单据维护')">
                <template>{{i18n('导入充值会员')}}</template>
                <!-- <template v-else>{{i18n('继续导入')}}</template> -->
              </el-button>
            </div>
            <FormItem :label="i18n('客户信息') + ':'" labelAlign="left">
              <div style="line-height:36px">{{dtl.customInfo || '--'}}</div>
            </FormItem>
            <FormItem :label="i18n('/储值/会员储值/会员储值报表/调整流水/发生组织') + ':'" labelAlign="left">
              <div style="line-height:36px">
                <template v-if="dtl.occurredOrgId">
                  [{{dtl.occurredOrgId}}]{{dtl.occurredOrgName}}
                </template>
                <template v-else>--</template>
              </div>
            </FormItem>
            <FormItem :label="i18n('/储值/会员储值/会员储值报表/充值流水-按支付方式/付款方式') + ':'" labelAlign="left">
              <div style="line-height:36px">
                <el-table :data="dtl.payInfo">
                  <el-table-column :label="i18n('/储值/会员储值/会员储值报表/充值流水-按支付方式/付款方式')" width="200">
                    <template slot-scope="scope" v-if="scope.row.payInfo">
                      {{scope.row.payInfo.name}}
                    </template>
                  </el-table-column>
                  <el-table-column :label="i18n('付款金额')" width="156">
                    <template slot-scope="scope">
                      {{scope.row.payAmount || 0}}
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </FormItem>
            <FormItem :label="i18n('付款总金额') + ':'" labelAlign="left">
              <div style="line-height:36px">{{dtl.total || '--'}}</div>
            </FormItem>
          </div>
        </div>
      </div>
      <div class="setting-container" style="margin-top: 16px">
        <div class="section-title">{{i18n('充值明细')}}</div>
        <el-table :data="lines">
          <el-table-column :label="i18n('/券/延期申请/会员手机号')">
            <template slot-scope="scope">
              {{scope.row.mobile}}
            </template>
          </el-table-column>
          <el-table-column :label="i18n('/储值/会员储值/储值充值活动/效果评估/实充金额（元）')" width="300">
            <template slot-scope="scope">
              {{scope.row.amount || 0}}
            </template>
          </el-table-column>
          <el-table-column :label="i18n('/储值/会员储值/储值充值活动/效果评估/返现金额（元）')" width="300">
            <template slot-scope="scope">
              {{scope.row.giftAmount || 0}}
            </template>
          </el-table-column>
        </el-table>
        <el-pagination no-i18n :current-page="linePage.currentPage" :page-size="linePage.pageSize" :page-sizes="[10, 20, 30, 40]" :total="linePage.total"
          @current-change="onPageLineChange" @size-change="onLineSizeChange" background layout="total, prev, pager, next, sizes,  jumper">
        </el-pagination>
      </div>
      <div class="setting-container" style="margin-top: 16px">
        <div class="section-title">{{i18n('/储值/会员储值/储值调整单/详情/操作日志')}}</div>
        <el-table :data="logList">
          <el-table-column :label="i18n('/储值/会员储值/储值调整单/详情/操作类型')">
            <template slot-scope="scope">
              {{scope.row.category || '--'}}
            </template>
          </el-table-column>
          <el-table-column :label="i18n('/储值/会员储值/储值调整单/详情/操作人')">
            <template slot-scope="scope">
              {{scope.row.creator || '--'}}
            </template>
          </el-table-column>
          <el-table-column :label="i18n('/储值/会员储值/储值调整单/详情/操作时间')">
            <template slot-scope="scope">
              {{scope.row.lastModified | dateFormate3}}
            </template>
          </el-table-column>
        </el-table>
        <el-pagination no-i18n :current-page="logPage.currentPage" :page-size="logPage.pageSize" :page-sizes="[10, 20, 30, 40]" :total="logPage.total"
          @current-change="onPageLogChange" @size-change="onLogSizeChange" background layout="total, prev, pager, next, sizes,  jumper">
        </el-pagination>
      </div>
    </div>
    <ImportStorageRechargeDialog ref="importStorageRechargeDialog" @uploadSuccess="uploadSuccess"></ImportStorageRechargeDialog>
    <DownloadCenterDialog :dialogvisiable="downloadShow" :showTip="true" @dialogClose="doDialogClose"></DownloadCenterDialog>
  </div>
</template>

<script lang="ts" src="./StorageRechargeBillDtl.ts">
</script>

<style lang="scss" scoped>
.storage-recharge-dtl {
  width: 100%;
  .top-wrap {
    border-radius: 8px;
    background-color: white;
    padding: 24px;

    .top-header {
      display: flex;
      width: 100%;
      .header-info {
        width: 100%;
        .header-title {
          display: flex;
          align-items: center;
          .coupon-name {
            max-width: 300px;
            height: 28px;
            font-size: 20px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #242633;
            line-height: 28px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
        .state-block {
          display: flex;
          justify-content: center;
          padding: 0 8px;
          height: 22px;
          border-radius: 4px;
          margin-left: 4px;
          font-size: 14px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #ffffff;
          line-height: 22px;
        }
        .import-block {
          width: 100%;
          max-width: 1100px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          background: #fdf2e8;
          border-radius: 2px;
          border: 1px solid #ef821e;
          height: 64px;
          margin: 12px 12px 12px 0;
          padding: 18px 12px;
          box-sizing: border-box;
          img {
            width: 28px;
            height: 28px;
            margin-right: 12px;
          }
          .import-title {
            font-family: PingFangSC, PingFang SC;
            font-size: 14px;
            color: #36445a;
          }
          .import-desc {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #79879e;
            margin-top: 4px;
          }
        }
      }
    }

    .top-body {
      display: flex;
      width: 100%;
      height: 80px;
      background: #f7f9fc;
      border-radius: 4px;
      margin-top: 26px;

      .body-section {
        padding: 12px 0 16px 16px;
        &:nth-last-child(1) {
          .body-container {
            border: none !important;
          }
        }

        .body-container {
          border-right: 1px solid #d7dfeb;

          .body-title {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #79879e;
            line-height: 22px;
          }
          .body-info {
            font-size: 18px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #242633;
            line-height: 24px;
            margin-top: 6px;
          }
        }
      }
    }

    .top-footer {
      margin-top: 20px;
      .overtext {
        font-size: 13px;
        line-height: 22px;
        clear: both;
        position: relative;
        top: -30px;
        width: 530px;
      }
    }

    .right {
      margin-top: 13px;
      display: flex;
      flex: 1;
      flex-direction: column;
      position: relative;

      .top {
        padding: 15px 0;
        display: flex;
        border-bottom: 1px solid rgba(242, 242, 242, 1);

        .bill {
          color: rgba(51, 51, 51, 0.***************);
        }

        .name {
          font-weight: 500;
          margin-top: 8px;
          font-size: 20px;
        }

        .desc {
          color: rgba(51, 51, 51, 0.***************);
        }

        .state {
          font-weight: 500;
          margin-top: 8px;
          font-size: 20px;
        }
      }

      .bottom {
        padding-bottom: 20px;

        .account-info {
          margin-top: 10px;
        }

        .red {
          color: red;
        }

        .green {
          color: #008000;
        }
      }
    }
  }
}
</style>