import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import CouponItem from "model/common/CouponItem";
import AmountToFixUtil from "util/AmountToFixUtil";
import TimeRange from "cmp/coupontenplate/cmp/TimeRange.vue";
import GoodsScopeEx from "cmp/goodsscope/GoodsScopeEx.vue";
import GoodsRange from "model/common/GoodsRange";
import UseCouponStep from "cmp/coupontenplate/cmp/UseCouponStep.vue";
import CouponBear from "cmp/coupontenplate/cmp/CouponBear.vue";
import CouponInfo from "model/common/CouponInfo";
import ValidityInfo from "model/common/ValidityInfo";
import DateTimeRange from "model/common/DateTimeRange";
import SubjectApportion from "model/common/SubjectApportion";
import DiscountCouponAttribute from "model/common/DiscountCouponAttribute";
import CouponThreshold from "model/common/CouponThreshold";
import StoreRange from "model/common/StoreRange";
import DateUtil from "util/DateUtil";
import ChannelRange from "model/common/ChannelRange";
import CouponInitialApi from "http/v2/coupon/init/CouponInitialApi";
import RSCostPartyFilter from "model/common/RSCostPartyFilter";
import CostPartyApi from "http/costparty/CostPartyApi";
import GroupMutexTemplate from "cmp/coupontenplate/cmp/GroupMutexTemplate";
import GroupMutexTemplateData from "cmp/coupontenplate/cmp/GroupMutexTemplateData";
import CouponTemplateLogo from "cmp/coupontenplate/cmp/CouponTemplateLogo";
import ActiveStore from "cmp/activestore/ActiveStore";
import { ExpiryType } from "model/common/ExpiryType";
import WeimobCouponInfo from "../cmp/WeimobCouponInfo";
import Channel from "model/common/Channel";
import RSChannelManagement from "model/common/RSChannelManagement";
import RexCoupon from "model/coupon/template/RexCoupon";
import RexCouponInfo from "../cmp/RexCouponInfo.vue";
import SelectGoodsDialog from "cmp/coupontenplate/cmp/selectGoodsDialog.vue";
import RSGoods from 'model/common/RSGoods'
import I18nPage from "common/I18nDecorator";
import SpecialGoodsDialog from 'cmp/coupontenplate/cmp/specialGoodsDialog.vue'
import CouponTemplateLabel from "cmp/coupontenplate/cmp/CouponTemplateLabel.vue"
import StackPromotion from "./../cmp/StackPromotion.vue"
import CouponName from "../FormItemCmp/CouponName/CouponName";
import UseCouponDesc from "../FormItemCmp/UseCouponDesc/UseCouponDesc";
import RecordWay from "../FormItemCmp/RecordWay/RecordWay";
import CouponEffectPeriod from "../FormItemCmp/CouponEffectPeriod/CouponEffectPeriod";
import CouponCodeRules from "../FormItemCmp/CouponCodeRules/CouponCodeRules";
import OuterCouponTemCode from "../FormItemCmp/OuterCouponTemCode/OuterCouponTemCode";
import BrowserMgr, { LocalStorage } from "mgr/BrowserMgr";
import SelectCostParty from 'cmp/selectCostParty/SelectCostParty';
import CouponConfig from 'model/v2/coupon/init/CouponConfig'

class CostPartyDetail {
  // 承担方
  party: Nullable<string> = null
  // 承担百分比/金额
  value: Nullable<number> = null
}
@Component({
  name: "DiscountCoupon",
  components: {
    CouponName,
    UseCouponDesc,
    CouponEffectPeriod,
    RecordWay,
    CouponCodeRules,
    TimeRange,
    GoodsScopeEx,
    UseCouponStep,
    CouponBear,
    ActiveStore,
    GroupMutexTemplate,
    CouponTemplateLogo,
    RexCouponInfo,
    WeimobCouponInfo,
    SelectGoodsDialog,
    SpecialGoodsDialog,
    CouponTemplateLabel,
    StackPromotion,
    OuterCouponTemCode,
    SelectCostParty
  },
})
@I18nPage({
  prefix: ["/公用/券模板"],
  auto: true
})
export default class DiscountCoupon extends Vue {
  dtl: CouponConfig = new CouponConfig()
  queryCostParyRange:string = 'customize'
  goodsMatchRuleMode: string = "barcode"
  noLimit: Boolean = true;
  showCurrentCoupon: boolean = true
  ruleForm: any = {
    discount: "",
    name: "",
    dateType: "RALATIVE",
    dateFrom: "",
    dateTo: "",
    dateFix: "",
    useDate: "",
    storeRange: "{}",
    useCouponGood: new GoodsRange(),
    promotionInfo: {
      excludePromotion: true,
      promotionSuperpositionType: null,
      promotion: {}
    },
    recordWay: "FAV",
    discountWay: "",
    payWay: "",
    couponOrder: "",
    couponGoodsDesc: "",
    couponProduct: "",
    otherStep: "",
    time: "",
    couponUnder: {},
    templateId: "",
    discountAmount: "",
    useFrom: "step2",
    from: [],
    sychChannel: null,
    logoUrl: "",
    groupMutex: new GroupMutexTemplateData(true),
    prefix: "",
    state: "",
    transferable: true, // 是否可转赠
    type: "",
    expiryType: "NATURAL_DAY",
    discountQty: '',
    discountType: 'noLimit',
    rexCoupon: new RexCoupon(),
    rexId: [],
    weimobId: [],
    weimobCouponAndTotal: {
      total: '',
      weimobCoupon: null
    },
    recordType: 'PROPORTION', //PROPORTION 按比例 AMOUNT 按金额
    templateTag: [],
    outerRelations: {
      outerNumber: null,
      channel: new Channel()
    },
    amountPayments: [],   // 券支付金额方式
    parties: [],   // 承担方
    price: null,
    termsModel: null,
    couponSubscriptType: "COMMON",
    notes:"",
    maxDailyMemberQuotaQty: null,
    writeOffLink: "",
  };
  $refs: any;
  rules: any = {};
  $eventHub: any
  disabledEdit: Boolean = false
  telescoping: boolean = true  //为true收起高级设置，为false展开
  curState = "";
  // 特殊商品
  specialGoods: any[] = []
  @Prop()
  sameStore: boolean; // 与活动门店一致
  @Prop()
  state: string;
  @Prop()
  channels: RSChannelManagement[];
  @Prop()
  value: CouponItem;
  @Prop({
    type: Boolean,
    default: false,
  })
  baseSettingFlag: boolean;
  @Prop({
    type: String,
    default: "add",
  })
  copyFlag: string;

  @Prop({
    type: String,
    default: "400",
  })
  remarkMaxlength: string;

  @Prop({
    type: Boolean,
    default: false,
  })
  baseFieldEditable: false; // 叠加促销、用券记录方式、券承担方、用券顺序 是否可编辑

  @Prop({
    type: Boolean,
    default: false,
  })
  weixinCouponHideFiled: boolean; //微信扫码领券活动隐藏 是否展示用券限量、高级备注字段

  @Prop({
    type: Boolean,
    default: false,
  })
  isPmsPayEngine: boolean; //是否pms计算模式

  @Prop({
    type: Boolean,
    default: false,
  })
  enableStore: boolean;

  @Prop({
    default: () => {
      return {
        maxValidDay: 36500,
        maxUseThreshold: 99999999,
        fixedTime: false,
      };
    },
  })
  options: {
    // 指定最大券面额，可选配置，用于微信扫码领券
    maxValidDay: number; // 指定最大券有效天数
    maxUseThreshold: number; // 指定最大用券门槛
    fixedTime: boolean; // 固定用券时段为全部时段
  };

  @Prop({
    type: Boolean,
    default: false,
  })
  wxScanForCoupon: boolean;

  @Prop({
    default: {
      outerTemplateCode: true, //是否展示外部券模板号
      tags: true //是否展示标签
    }
  }) hideOptions: any;

  parties: any = [];

  // 是否是复制\新建\编辑
  @Watch("state")
  onStateChange(value: string) {
    this.curState = value;
  }

  @Watch("ruleForm",{ deep: true, immediate: true })
  onRuleFormChange(value: string) {
    if (this.ruleForm.type && this.ruleForm.type.threshold && this.ruleForm.discountQty) {
      this.showCurrentCoupon = this.ruleForm.discountQty <= this.ruleForm.type.threshold
    } else {
      this.showCurrentCoupon = true
    }
  }


  get accountItemRange() {
    if (this.queryCostParyRange) {
      return this.queryCostParyRange;
    }
      return 'customize';
  }

  // 最终的同步渠道
  get sychChannel() {
    return this.channels.filter(item => [...this.ruleForm.weimobId, ...this.ruleForm.rexId].includes(this.channelId(item.channel!))).map(item => item.channel!)
  }

  get hasWeimobChannel() {
    return this.ruleForm.weimobId.length > 0
  }

  get hasRexChannel() {
    return this.ruleForm.rexId.length > 0
  }
  // 可选的同步渠道
  get sychChannels() {
    return this.channels.filter((item) => ["weimob", 'rex'].includes(item.channel!.type || ''));
  }
  get weimobChannels() {
    return this.channels.filter((item) => ["weimob"].includes(item.channel!.type || ''));
  }
  get rexChannels() {
    return this.channels.filter((item) => ["rex"].includes(item.channel!.type || ''));
  }

  get remarkPlaceholder() {
    let str = this.formatI18n("/营销/积分活动/积分活动/积分兑换券/编辑页面", "请输入不超过{0}个字符");
    return str.replace(/\{0\}/g, this.remarkMaxlength);
  }

  get showOuterRelationsConfig() {
    const flag = sessionStorage.getItem('showOuterRelationsConfig')
    return flag === 'true'
  }

  //"用券门槛"选择了无门槛，禁用 叠加用券-订单级别-可叠加-可与当前券模板叠加 按钮
  // get canPileSelfCoupon() {
  //   return this.ruleForm.type.thresholdType === 'NONE'
  // }

	created() {
    let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
		if (sysConfig && this.goodsMatchRuleMode) {
			this.goodsMatchRuleMode = sysConfig.goodsMatchRuleMode;
		}
    this.queryCostParyRange = LocalStorage.getItem("accountItemRange");
    this.disabledEdit = (this.$route.query.from === 'edit' && this.state !== 'NOT_EFFECTED')
		this.ruleForm.type = new CouponThreshold();
		this.ruleForm.type.thresholdType = "NONE" as any;
		this.rules = {
			name: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
			otherStep: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
      discount: [{ 
        required: true, 
        validator: (rule: any, value: any, callback: any) => {
          if (value < 1 && this.ruleForm.weimobId?.length > 0) {
            this.$message.warning(this.i18n('微盟折扣最小值为1'))
            callback(this.i18n('微盟折扣最小值为1'))
          }
          if (value === undefined || value === null) {
            callback(this.formatI18n("/公用/券模板", "请输入必填项"))
          }
          if (value === 0 && this.ruleForm.type.thresholdType === "NONE") {
            callback(this.i18n("无门槛折扣力度至少为{0}", ['0.1']))
          }
          callback()
        }, 
        trigger: "blur" }],
			discountLimit: [
				{
					validator: (rule: any, value: any, callback: any) => {
						if(this.ruleForm.discountType === 'noLimit') {
							callback()
						} else if(this.ruleForm.discountType === 'amount') {
							if (this.ruleForm.discountAmount) {
								callback()
							} else {
								callback(new Error(this.formatI18n("/公用/券模板", "请输入必填项") as string))
							}
						} else {
							callback()
						}
					},
					trigger: "change",
				}
			],
			discountLimit1: [
				{
					validator: (rule: any, value: any, callback: any) => {
						if(this.ruleForm.discountType === 'noLimit') {
							callback()
            } else if (this.ruleForm.discountType === 'qty') {
              if (this.ruleForm.discountQty && Number(this.ruleForm.discountQty) <= this.$refs.useCouponStep.ruleForm.step1) {
                callback()
              } else if (Number(this.ruleForm.discountQty) > this.$refs.useCouponStep.ruleForm.step1) {
                let scene1 = this.ruleForm.groupMutex.couponSuperposition?.superpositionLevel === 'TRADE'
                    && this.ruleForm.groupMutex.couponSuperposition?.superpositionType === 'NONREUSEABLE'
                let scene2 = this.ruleForm.groupMutex.couponSuperposition?.superpositionLevel === 'TRADE'
                    && this.ruleForm.groupMutex.couponSuperposition?.superpositionType === 'REUSEABLE'
                    && this.ruleForm.groupMutex.couponSuperposition?.superpositionTypeValue.length === 1
                    && this.ruleForm.groupMutex.couponSuperposition?.superpositionTypeValue.includes('OTHER_COUPON')
                let scene3 = this.ruleForm.groupMutex.couponSuperposition?.superpositionLevel === 'GOODS'
                    && this.ruleForm.groupMutex.couponSuperposition?.superpositionType === 'NONREUSEABLE'
                    &&  ( this.ruleForm.groupMutex.couponSuperposition?.nonSuperpositionTypeValue === 'CURRENT_COUPON'
                         ||  this.ruleForm.groupMutex.couponSuperposition?.nonSuperpositionTypeValue === 'ALL_COUPON')
                console.log('scene1:', scene1, 'scene2:', scene2, 'scene3:', scene3)
                if (scene1 || scene2 || scene3) {
                  callback()
                } else {
                  callback(new Error(this.formatI18n("/公用/券模板", "优惠上限不能大于用券门槛") as string))
                }
                // callback(new Error(this.formatI18n("/公用/券模板", "优惠上限不能大于用券门槛") as string))
              } else {
                callback(new Error(this.formatI18n("/公用/券模板", "请输入必填项") as string))
              }
            } else {
              callback()
            }
          },
          trigger: "change",
        }
      ],
      useFrom: [
        {
          validator: (rule: any, value: any, callback: any) => {
            if (value) {
              if (value === "step1") {
                callback();
              } else {
                if (this.ruleForm.from && this.ruleForm.from.length > 0) {
                  callback();
                } else {
                  callback(this.formatI18n("/公用/券模板", "请输入必填项"));
                }
              }
            }
          },
          trigger: "blur",
        },
      ],
      couponOrder: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
      couponProduct: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
      discountWay: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
      payWay: [{ required: true, message: this.formatI18n("/公用/券模板", "请输入必填项"), trigger: "blur" }],
      prefix: [
        {
          validator: (rule: any, value: any, callback: any) => {
            let re = /^[0-9a-zA-Z]*$/g; // 判断字符串是否为数字和字母组合
            if (!re.test(value)) {
              callback(this.i18n("请输入数字或字母"));
            } else {
              callback();
            }
          },
          tirgger: "blur",
        },
      ],
    };

    if (this.copyFlag) {
      this.getCostParty();
    }
    if (!this.$route.query.id) {
      // 新建时使用配置的用券记录方式
      this.getPayWayDtl();
    }

    this.getCouponPrefix("discount");
  }

  mounted() {
    if (this.value && this.value.coupons && (this.value.coupons.couponBasicType === "all_discount" || this.value.coupons.couponBasicType === "goods_discount" || this.value.coupons.couponBasicType === "rfm_type") && this.value.coupons.remark) {
      this.doBindValue(JSON.parse(JSON.stringify(this.value)));
    }
  }

  doUseFromChange() {
    if (this.ruleForm.useFrom === "step1") {
      this.ruleForm.from = [];
      this.$emit("input", this.doTransParams());
      this.$emit("change", this.channels);
    }
    this.$refs.ruleForm.validateField("useFrom");
  }

  doFromChange() {
    this.$refs.ruleForm.validateField("useFrom");
    this.doFormItemChange()
  }

  doDiscount() {
    if (this.copyFlag !== 'edit') {
      if (this.ruleForm.type.thresholdType === 'NONE') {
        this.ruleForm.discount = AmountToFixUtil.formatNumberWithInteger(this.ruleForm.discount, 9.9, 0.1, 1);
      } else {
        this.ruleForm.discount = AmountToFixUtil.formatNumberWithInteger(this.ruleForm.discount, 9.9, 0, 1);
      }
    }
    if (this.ruleForm.discount === "0.0") {
      this.ruleForm.discount = 0;
    }
    this.doFormItemChange()
  }

  logoUrlCallBack(url: any) {
    this.ruleForm.logoUrl = url;
    this.doFormItemChange()
  }

  discountTypeChange() {
    this.ruleForm.discountAmount = ""
    this.ruleForm.discountQty = ""
    this.doFormItemChange()
    this.$refs.ruleForm.validate()
  }

  doDiscountAmountChange() {
    this.ruleForm.discountAmount = AmountToFixUtil.formatAmount(this.ruleForm.discountAmount, 99999999, 1, "");
    this.doFormItemChange()
  }

  doDiscountQtyChange() {
    this.ruleForm.discountQty = AmountToFixUtil.formatNumber(this.ruleForm.discountQty, 99999999, 1);
    this.doFormItemChange()
  }

  doFormItemChange() {
    this.$emit("input", this.doTransParams());
    this.$emit("change", this.channels);
  }

  doStepChange() {
    this.ruleForm.discountType = 'noLimit'
    this.ruleForm.discountAmount = ''
    this.ruleForm.discountQty = ''
    this.$refs.ruleForm.validate()
    this.doDiscount()
    this.doFormItemChange()
  }

  doStoreChange() {
    this.doFormItemChange()
    if (this.$refs.ruleForm) {
      this.$refs.ruleForm.validateField("storeRange");
    }
  }

  weimobChange(info: any) {
    this.ruleForm.weimobCouponAndTotal.total = info.total
    this.ruleForm.weimobCouponAndTotal.weimobCoupon = info.weimobCoupon
    this.doFormItemChange()
  }

  rexChange(info: any) {
    this.ruleForm.rexCoupon = info
    this.doFormItemChange()
  }

  getDiscountRate(discountRate: number) {
    // scope.row.discountRate > 0 ? scope.row.discountRate.toFixed(1) + '折' : '无折扣'
    let str: any = "";
    if (Number(discountRate) >= 0) {
      str = this.formatI18n("/会员/等级/等级管理/已初始化状态的免费等级/表格/折扣", "{0}折");
      str = str.replace(/\{0\}/g, discountRate);
    } else {
      str = this.formatI18n("/会员/等级/等级管理/已初始化状态的免费等级/表格/折扣", "无折扣");
    }
    return str;
  }

  getAmount(price: number, qty: number) {
    if (price) {
      let str: any = this.formatI18n("/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级套餐", "{0}元");
      str = str.replace(/\{0\}/g, Number(price).toFixed(2));
      return str;
    } else if (qty) {
      let str: any = this.formatI18n("/会员/等级/等级管理/点击付费等级tab页/已初始化状态下/表格/等级套餐", "{0}件");
      str = str.replace(/\{0\}/g, qty);
      return str;
    } else {
      return this.formatI18n("/营销/积分活动/积分活动/单品满数量加送积分活动/编辑页面/积分加送规则/不限制");
    }
  }

  doValidate() {
    this.telescoping = false
    let arr: any = [];
    let p0 = new Promise((resolve, reject) => {
      this.$refs.ruleForm.validate((valid: any) => {
        if (valid) {
          resolve(null);
        }
      });
    });
    arr.push(p0);
    // 券承担方
    if (this.$refs.CouponBear) {
      arr.push(this.$refs.CouponBear.formValiPromise())
    }
    arr.push(this.$refs.activeStore.validate());
    // 用券门槛
    // let p1 = this.$refs.useCouponStep.doValidate()
    // arr.push(p1)

    // 用券时段
    if (this.$refs.timeRange) {
      let p2 = this.$refs.timeRange.doValidate();
      arr.push(p2);
    }
    // 用券商品
    if (this.$refs.goodsScope) {
      let p3 = this.$refs.goodsScope.validate();
      arr.push(p3);
    }
    // 叠加促销
    if (this.$refs.stackPromotion) {
      arr.push(this.$refs.stackPromotion.validate());
    }

    // 券叠加组
    if (this.$refs.allDiscountMutexTemplate) {
      arr.push(this.$refs.allDiscountMutexTemplate.doValidate());
    }
    // 微盟校验
    if (this.$refs.weimobCouponInfo) {
      arr.push(this.$refs.weimobCouponInfo.doValidate());
    }
    // rex校验
    if (this.$refs.rexCouponInfo) {
      arr.push(this.$refs.rexCouponInfo.doValidate());
    }
    // 用券记录方式校验
    if (this.$refs.recordWay) {
      arr.push(this.$refs.recordWay.validate());
    }
    //券有效期校验
    if (this.$refs.couponEffectPeriod) {
      arr.push(this.$refs.couponEffectPeriod.validate());
    }
    //外部券模板号
    if (this.$refs.outerCouponTemCode) {
      arr.push(this.$refs.outerCouponTemCode.doValidate())
    }
    return arr;
  }

  private doTransParams() {
    let params: CouponItem = new CouponItem();
    params.coupons = new CouponInfo();
    params.coupons.couponBasicType = "all_discount" as any;
    params.coupons.name = this.ruleForm.name;
    params.coupons.templateId = this.ruleForm.templateId;
    params.coupons.discountCouponAttribute = new DiscountCouponAttribute();
    params.coupons.discountCouponAttribute.discount = this.ruleForm.discount;
    // 优惠上限
    params.coupons.discountCouponAttribute.maxDiscountAmount = this.ruleForm.discountAmount || null;
    params.coupons.discountCouponAttribute.maxDiscountQty = this.ruleForm.discountQty || null;
    // 券有效期
    params.coupons.validityInfo = new ValidityInfo();
    params.coupons.validityInfo.validityType = this.ruleForm.dateType;
    if (this.ruleForm.dateType === "RALATIVE") {
      params.coupons.validityInfo.delayEffectDays = this.ruleForm.dateFrom;
      params.coupons.validityInfo.expiryType = this.ruleForm.expiryType;
      if ([ExpiryType.DAYS, ExpiryType.NATURAL_DAY].indexOf(this.ruleForm.expiryType) > -1) {
        params.coupons.validityInfo.validityDays = this.ruleForm.dateTo;
        params.coupons.validityInfo.months = null;
      } else if (this.ruleForm.expiryType === ExpiryType.MONTHS) {
        params.coupons.validityInfo.months = this.ruleForm.dateTo;
        params.coupons.validityInfo.validityDays = null;
      } else {
        params.coupons.validityInfo.months = null;
        params.coupons.validityInfo.validityDays = null;
      }
    } else {
      // 固定有效期
      if (this.ruleForm.dateFix && this.ruleForm.dateFix[0]) {
        params.coupons.validityInfo.beginDate = (this.ruleForm.dateFix[0]) as any;
      }
      if (this.ruleForm.dateFix && this.ruleForm.dateFix[1]) {
        params.coupons.validityInfo.endDate = (this.ruleForm.dateFix[1]) as any;
      }
    }
    // 用券时段
    params.coupons.useTimeRange = new DateTimeRange();
    if (this.ruleForm.time) {
      params.coupons.useTimeRange = this.ruleForm.time;
    } else {
      params.coupons.useTimeRange = new DateTimeRange();
      params.coupons.useTimeRange.dateTimeRangeType = "ALL" as any;
    }
    // todo 用券渠道
    params.coupons.useChannels = new ChannelRange();
    if (this.ruleForm.useFrom === "step1") {
      params.coupons.useChannels.channelRangeType = "ALL" as any;
      params.coupons.useChannels.channels = [];
    } else {
      params.coupons.useChannels.channelRangeType = "PART" as any;
      params.coupons.useChannels.channels = this.ruleForm.from;
    }
    //同步渠道
    params.coupons.sychChannel = this.ruleForm.sychChannel;
    // 用券门店
    if (this.ruleForm.storeRange === "{}") {
      let storeRange: StoreRange = new StoreRange();
      if (this.sameStore) {
        storeRange.storeRangeType = "SAME" as any;
      } else {
        storeRange.storeRangeType = "ALL" as any;
      }
      params.coupons.useStores = storeRange;
    } else {
      params.coupons.useStores = this.ruleForm.storeRange;
    }
    // 用券商品
    params.coupons.useGoods = this.ruleForm.useCouponGood;
    // 用券门槛
    // params.coupons.useThreshold = new CouponThreshold()
    // params.coupons.useThreshold.threshold = this.ruleForm.otherStep
    // params.coupons.useThreshold.thresholdType = 'NONREUSEABLE' as any
    // params.coupons.useThreshold.value = 1
    // 用券门槛
    params.coupons.useThreshold = this.ruleForm.type;
    if (this.ruleForm.type.useThresholdType === 'QTY') {
      if (this.ruleForm.discountType === 'noLimit') {
        params.coupons.useThreshold!.value = params.coupons.useThreshold!.threshold
      } else if (this.ruleForm.discountType === 'qty') {
        params.coupons.useThreshold!.value = this.ruleForm.discountQty
      }
    }
    params.coupons.useThresholdType = this.ruleForm.type.useThresholdType;
    // 叠加促销
    params.coupons.excludePromotion = this.ruleForm.promotionInfo.excludePromotion;
    params.coupons.promotionSuperpositionType = this.ruleForm.promotionInfo.promotionSuperpositionType;
    params.coupons.promotion = this.ruleForm.promotionInfo.promotion;
    params.coupons.goodsFavRules = this.ruleForm.promotionInfo.goodsFavRules
    // 是否支持转赠
    params.coupons.transferable = this.ruleForm.transferable;
    // 用券记录方式
    params.coupons.useApporion = new SubjectApportion();
    params.coupons.useApporion.subjectApprotionType = this.ruleForm.recordWay;
    params.coupons.useApporion.recordType = this.ruleForm.recordType
    if (this.ruleForm.recordWay === "COLLOCATION") {
      if (this.ruleForm.recordType === 'PROPORTION') {
        params.coupons.useApporion.favValue = parseFloat(this.ruleForm.discountWay);
        params.coupons.useApporion.payValue = parseFloat(this.ruleForm.payWay);
      } else {
        params.coupons.useApporion.payValue = this.ruleForm.payWay;
        params.coupons.useApporion.parties = this.ruleForm.parties
        params.coupons.useApporion.amountPayments = this.ruleForm.amountPayments
        // 用券方式，按金额，特殊商品
        if (this.specialGoods.length) {
          params.coupons.useApporion.specialGoodsAmounts = this.specialGoods.map((item: any) => {
            return {
              barcode: item.barcode,
              amount: item.price != undefined ? item.price : item.amount
            }
          })
        }
      }

    } else {
      params.coupons.useApporion.favValue = 0;
      params.coupons.useApporion.payValue = 0;
      // params.coupons.useApporion.recordType = "PROPORTION";
      params.coupons.useApporion.specialGoodsAmounts = null;
      params.coupons.useApporion.recordType = null
    }
    // 券承担方
    // params.coupons.costParties = [];
    // params.coupons.costParties = this.ruleForm.couponUnder;
    ; (params.coupons.costParty as any) = {};
    if (this.ruleForm.couponUnder.bearType == 'unset') {
      this.ruleForm.couponUnder.bearType = null
      this.ruleForm.couponUnder.amountType = null
      this.ruleForm.couponUnder.costPartyDetails = null
    }
    params.coupons.costParty = this.ruleForm.couponUnder;
    // 用券顺序
    params.coupons.priority = this.ruleForm.couponOrder;
    // 用券商品说明
    params.coupons.goodsRemark = this.ruleForm.couponGoodsDesc;
    // 用券说明
    params.coupons.remark = this.ruleForm.couponProduct;
    // 券叠加促销
    // params.coupons.groupMutexFlag = this.ruleForm.groupMutex.groupMutexFlag;
    // params.coupons.groupMutexTemplates = this.ruleForm.groupMutex.groupMutexTemplates;
    params.coupons.couponSuperposition = this.ruleForm.groupMutex.couponSuperposition;
    // 券logo
    params.coupons.logoUrl = this.ruleForm.logoUrl;
    // 券码前缀
    params.coupons.codePrefix = this.ruleForm.prefix;
    // 剩余库存
    if (this.ruleForm.weimobId?.length > 0) {
      params.coupons.total = this.ruleForm.weimobCouponAndTotal.total
    }
    params.coupons.templateTag = this.ruleForm.templateTag
    // 同步渠道参数
    this.hasWeimobChannel && (params.coupons.weimobCoupon = this.ruleForm.weimobCouponAndTotal.weimobCoupon)
    this.hasRexChannel && (params.coupons.rexCoupon = this.ruleForm.rexCoupon)
    params.coupons.sychChannel = this.sychChannel
    //外部券模板号
    params.coupons.outerRelations = this.ruleForm.outerRelations && this.ruleForm.outerRelations.outerNumber ? [this.ruleForm.outerRelations] : null
    //价格
    params.coupons.salePrice = this.ruleForm.price
    //账款项目
    params.coupons.termsModel = this.ruleForm.termsModel
    //券角标
    params.coupons.couponSubscriptType = this.ruleForm.couponSubscriptType
    //核销链接
    params.coupons.writeOffLink = this.ruleForm.writeOffLink
        //备注
    params.coupons.notes = this.ruleForm.notes
    //每人每天限量
    params.coupons.maxDailyMemberQuotaQty = this.ruleForm.maxDailyMemberQuotaQty
    return params;
  }

  private doBindValue(value: CouponItem) {
    if (value && value.coupons) {
      let coupon: CouponInfo = value.coupons;
      this.ruleForm.name = coupon.name;
      this.ruleForm.templateId = coupon.templateId;
      this.ruleForm.discount = coupon.discountCouponAttribute!.discount;
      this.ruleForm.discountAmount = coupon.discountCouponAttribute!.maxDiscountAmount;
      this.ruleForm.discountQty = coupon.discountCouponAttribute!.maxDiscountQty;
      if (this.ruleForm.discountAmount) {
        this.ruleForm.discountType = 'amount'
      } else if (this.ruleForm.discountQty && coupon.useThresholdType === 'QTY') {
        this.ruleForm.discountType = 'qty'
      } else {
        this.ruleForm.discountType = 'noLimit'
      }
      const weimobChannel = coupon.sychChannel.filter(item => item.type == 'weimob')[0]
      this.ruleForm.weimobId = weimobChannel ? [this.channelId(weimobChannel)] : []
      const rexChannel = coupon.sychChannel.filter(item => item.type == 'rex')[0]
      this.ruleForm.rexId = rexChannel ? [this.channelId(rexChannel)] : []
      this.ruleForm.dateType = coupon.validityInfo!.validityType;
      this.ruleForm.state = coupon.state;
      this.ruleForm.sychChannel = coupon.sychChannel;
      if (this.ruleForm.dateType === "RALATIVE") {
        this.ruleForm.dateFrom = coupon.validityInfo!.delayEffectDays;
        this.ruleForm.expiryType = coupon.validityInfo!.expiryType;
        this.ruleForm.dateTo = coupon.validityInfo!.validityDays || coupon.validityInfo!.months;
      } else {
        this.ruleForm.dateFix = [
          DateUtil.format(coupon.validityInfo!.beginDate, "yyyy-MM-dd HH:mm:ss"),
          DateUtil.format(coupon.validityInfo!.endDate, "yyyy-MM-dd HH:mm:ss"),
        ];
      }
      // todo 用券渠道
      if (coupon.useChannels && coupon.templateId) {
        if (coupon.useChannels.channelRangeType === "ALL") {
          this.ruleForm.useFrom = "step1";
          this.ruleForm.from = [];
        } else {
          this.ruleForm.useFrom = "step2";
          if (coupon.useChannels.channels && coupon.useChannels.channels.length > 0) {
            let arrs: string[] = [];
            coupon.useChannels.channels.forEach((item: any) => {
              if (item.id || item.type) {
                if (item.id && item.id !== "-") {
                  arrs.push(item.type + item.id);
                } else {
                  arrs.push(item.type);
                }
              } else {
                arrs.push(item);
              }
            });
            this.ruleForm.from = arrs;
          }
        }
      }
      // 用券时段
      // this.ruleForm.useDate = coupon.useTimeRange!.dateTimeRangeType
      this.$nextTick(() => {
        if (coupon.useTimeRange && coupon.useTimeRange.beginTime === null) {
          coupon.useTimeRange.beginTime = "";
        }
        if (coupon.useTimeRange && coupon.useTimeRange.endTime === null) {
          coupon.useTimeRange.endTime = "";
        }
        this.ruleForm.time = coupon.useTimeRange;
      });
      // 用券门店
      // let custom: CustomStore = new CustomStore()
      // custom.storeRange = coupon.useStores as any
      // 用券门店
      if (coupon.useStores) {
        this.ruleForm.storeRange = coupon.useStores;
      } else {
        this.ruleForm.storeRange = "{}";
      }
      // 用券商品
      this.ruleForm.useCouponGood = coupon.useGoods;
      // 用券门槛
      // this.ruleForm.otherStep = coupon.useThreshold!.threshold;
      // 用券门槛类型
      this.ruleForm.type = coupon.useThreshold;
      this.ruleForm.type.useThresholdType = coupon.useThresholdType;
      let noLimit = false;
      // PHX-11218 无门槛 也可以选择 叠加用券-订单级别-不可叠加
      // if (this.ruleForm.type.thresholdType === 'NONE') {
      //   this.ruleForm.type.useThresholdType = null
      //   noLimit = true
      // } else {
      //   noLimit = false
      // }
      this.$eventHub.$emit('limitChange', noLimit)

      // 叠加促销
      if (coupon.excludePromotion || coupon.excludePromotion === false) {
        this.ruleForm.promotionInfo.excludePromotion = coupon.excludePromotion;
      } else {
        this.ruleForm.promotionInfo.excludePromotion = true;
      }
      this.ruleForm.promotionInfo.promotionSuperpositionType = coupon.promotionSuperpositionType
      this.ruleForm.promotionInfo.promotion = coupon.promotion
      this.ruleForm.promotionInfo.goodsFavRules = coupon.goodsFavRules
      // 用券记录方式
      if (coupon.useApporion && coupon.useApporion!.subjectApprotionType) {
        this.ruleForm.recordWay = coupon.useApporion!.subjectApprotionType;
      } else {
        this.ruleForm.recordWay = "FAV";
      }

      if (this.ruleForm.recordWay === "COLLOCATION") {
        if (coupon.useApporion!.recordType === 'AMOUNT') {
          this.ruleForm.payWay = coupon.useApporion!.payValue;
          this.specialGoods = coupon.useApporion!.specialGoodsAmounts || []
          this.ruleForm.parties = coupon.useApporion!.parties || []
          this.ruleForm.amountPayments = coupon.useApporion!.amountPayments || []
        } else {
          this.ruleForm.discountWay = coupon.useApporion!.favValue;
          this.ruleForm.payWay = coupon.useApporion!.payValue;
        }

      }
      this.ruleForm.recordType = coupon.useApporion!.recordType
      // 券承担方
      // this.ruleForm.couponUnder = coupon.costParties;
      this.ruleForm.couponUnder = coupon.costParty;
      // 用券顺序
      this.ruleForm.couponOrder = coupon.priority;
      // 用券商品说明
      this.ruleForm.couponGoodsDesc = coupon.goodsRemark;
      // 用券说明
      this.ruleForm.couponProduct = coupon.remark;
      // 券logo
      this.ruleForm.logoUrl = coupon.logoUrl;
      // 券码前缀
      this.ruleForm.prefix = coupon.codePrefix;
      // 是否支持转赠
      this.ruleForm.transferable = coupon.transferable;
      // 是否可以叠加用券
      setTimeout(() => {
        this.$refs.allDiscountMutexTemplate.initValue2(coupon, this.copyFlag)

      }, 2);
      // this.$nextTick(() => this.$refs.allDiscountMutexTemplate.initValue2(coupon, this.copyFlag));
      // 剩余库存
      this.ruleForm.weimobCouponAndTotal.total = coupon.total
      // 微盟券参数
      this.ruleForm.weimobCouponAndTotal.weimobCoupon = coupon.weimobCoupon
      this.ruleForm.templateTag = coupon.templateTag
      coupon.rexCoupon && (this.ruleForm.rexCoupon = coupon.rexCoupon)
      if (this.$refs.weimobCouponInfo) {
        this.$refs.weimobCouponInfo.doBindValue(this.ruleForm.weimobCouponAndTotal)
      }
      //外部券模板号
      if (coupon.outerRelations && coupon.outerRelations[0] && (this.showOuterRelationsConfig || this.$route.query.from === 'edit') && this.$route.query.from !== 'copy') {
        // 配置不展示外部券模板号时，编辑券模板保留该字段，复制不保留该字段
        this.ruleForm.outerRelations = coupon.outerRelations[0]
      }
      //价格
      this.ruleForm.price = coupon.salePrice
      //账款项目
      this.ruleForm.termsModel = coupon.termsModel
      //券角标
      this.ruleForm.couponSubscriptType = coupon.couponSubscriptType
      //核销链接
      this.ruleForm.writeOffLink = coupon.writeOffLink
      //备注
      this.ruleForm.notes =coupon.notes
      //每人每天限量
      this.ruleForm.maxDailyMemberQuotaQty = coupon.maxDailyMemberQuotaQty      
    }
  }

  checkLimit() {
    let noLimit = false;
    // PHX-11218 无门槛 也可以选择 叠加用券-订单级别-不可叠加
    // if (this.ruleForm.type.thresholdType === 'NONE') {
    //   this.ruleForm.type.useThresholdType = null
    //   noLimit = true
    // } else {
    //   noLimit = false
    // }
    this.$eventHub.$emit('limitChange', noLimit)
  }

  limitChange(noLimit: Boolean) {
    this.noLimit = noLimit;
  }

  private getPayWayDtl() {
    CouponInitialApi.get().then((resp: any) => {
      if (resp && resp.code === 2000) {
        if (resp.data && resp.data.subjectApportion === "pay") {
          this.ruleForm.recordWay = "PAY";
        } else if (resp.data && resp.data.subjectApportion === "fav") {
          this.ruleForm.recordWay = "FAV";
        } else if (resp.data && resp.data.subjectApportion === "collection") {
          this.ruleForm.recordWay = "COLLOCATION";
          this.ruleForm.recordType = "AMOUNT"
          this.ruleForm.payWay = 0
        }
      }
    });
  }
  private channelId(item: Channel) {
    return `${item.type}${item.id}`
  }

  private getCostParty() {
    let params: RSCostPartyFilter = new RSCostPartyFilter();
    params.page = 0;
    params.pageSize = 0;
    CostPartyApi.query(params)
      .then((resp: any) => {
        if (resp && resp.code === 2000) {
          this.parties = resp.data;
        }
      })
      .catch((error: any) => {
        this.$message.error(error.message);
      });
  }

  telescopingChange() {
    this.telescoping = !this.telescoping
  }

  changeSpecialGoods(goods: any[]) {
    this.specialGoods = goods || []
    this.doFormItemChange()
  }

  // 接受特殊商品参数
  specialGoodsSubmit(goods: RSGoods[]) {
    this.specialGoods = [...goods]
    this.doFormItemChange()
  }

  doSynChannelChange(newValue: any[]) {
    this.$refs.ruleForm.validateField('discount')
    if (this.ruleForm.weimobId && this.ruleForm.weimobId.length >= 2) {
      this.ruleForm.weimobId = this.ruleForm.weimobId.slice(-1)
    } else if (this.ruleForm.rexId && this.ruleForm.rexId.length >= 2) {
      this.ruleForm.rexId = this.ruleForm.rexId.slice(-1)
    }
    this.doFormItemChange()
  }

  doOuterTemplateChange(data: any) {
    this.ruleForm.outerRelations = data
    this.doFormItemChange()
  }

  private getCouponPrefix(type: string){
    if (["edit", "copy"].includes(this.copyFlag)) {
			return;
		}
		CouponInitialApi.get().then((resp: any) => {
		  if (resp && resp.code === 2000) {
			this.dtl = resp.data;
      if(!this.dtl || !this.dtl.couponCodePrefixes) {
        return "";
      }
			const coupon = this.dtl.couponCodePrefixes.find(
				item => item.couponType === type
			);
			this.ruleForm.prefix = coupon ? coupon.prefix : "";
      }
		  })
	  }
}
