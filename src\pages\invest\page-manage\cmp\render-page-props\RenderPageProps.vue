<template>
  <div class="container">
    <div class="activity-props-header">
      {{ localModel.name }}
    </div>
    <el-tabs v-model="activeName" @tab-click="handleClick" v-if="activeIndex !== -11">
      <el-tab-pane :label="i18n('内容设置')" name="content"></el-tab-pane>
      <el-tab-pane :label="i18n('样式设置')" name="sty"></el-tab-pane>
      <!-- <el-tab-pane label="顶部导航设置" name="global"></el-tab-pane> -->
    </el-tabs>
    <component
      v-for="(item, index) in filtershowComponents"
      :key="item.component + item.label"
      :is="item.component"
      :config="item.config"
      :activeIndex="activeIndex"
      :label="item.label"
      :advertiseChannel="advertiseChannel"
      v-model="localModel"
      :ref="index+item.component"
      @change="handleChange"
      :renderTemplateList="renderTemplateList"
    ></component>
  </div>
</template>

<script lang="ts" src="./RenderPageProps.ts"></script>

<style lang="scss" scoped>
.activity-props-header {
  width: 100%;
  height: 52px;
  line-height: 52px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #24272b;
  border-radius: 0px 8px 0px 0px;
  overflow:hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow:ellipsis;
}
.container {
  overflow: hidden;
  padding: 0 12px 10px 20px;
}
</style>
