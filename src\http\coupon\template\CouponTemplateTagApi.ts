import ApiClient from 'http/ApiClient'
import CouponTemplateTag from 'model/coupon/CouponTemplateTag'
import CouponTemplateTagFilter from 'model/coupon/CouponTemplateTagFilter'
import Response from "model/common/Response";

export default class CouponTemplateTagApi {
  /**
   * 券模板标签修改
   * 券模板标签修改
   * 
   */
  static modify(body: CouponTemplateTag): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/coupon-template-tag/modify`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 券模板标签列表查询
   * 券模板标签列表查询
   * 
   */
  static query(body: CouponTemplateTagFilter): Promise<Response<CouponTemplateTag[]>> {
    return ApiClient.server().post(`/v1/coupon-template-tag/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除券模板标签
   * 删除券模板标签。
   * 
   */
  static remove(id: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/coupon-template-tag/remove`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 券模板标签保存
   * 券模板标签保存
   * 
   */
  static save(body: CouponTemplateTag): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/coupon-template-tag/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
