<template>
    <el-dialog :before-close="doBeforeClose"
               append-to-body
               :close-on-click-modal="false"  :title="title" :visible.sync="dialogShow" class="import-dialog-view">
        <div class="wrap">
            <div class="left">{{formatI18n('/公用/导入', '实例模板')}}：
                <a class="action-hover_download" @click="downloadTemplate" style="line-height: 12px; color: #318BFF;font-size: 12px;text-decoration: none">{{templateName}}</a>
<!--                <el-button @click="doDownLoadTemplate" type="text">{{templateName}}</el-button>-->
            </div>
            <div  class="left">{{getImportDesc()}}</div>
            <div class="left" v-if="showOrg">
              <span>{{formatI18n('/权益/积分/新建积分调整单/条目', '发生组织')}}：</span>
              <SelectStores v-model="orgId" @change="$forceUpdate()" :isOnlyId="true" :hideAll="true" width="190px" 
                :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
              </SelectStores>
            </div>
          <div class="left" v-if="showClient">
            <span style="padding: 0 1em">{{formatI18n('/储值/预付卡/预付卡充值单', '客户')}}：</span>
            <SelectClient v-model="clientId" @change="$forceUpdate()" :hideAll="true" width="190px"
                          :placeholder="formatI18n('/公用/下拉框/提示', '请选择')">
            </SelectClient>
          </div>
            <div class="left" v-if="showDepositType">
              {{formatI18n('/储值/预付卡/预付卡充值单', '充值类型')}}：
              <el-radio-group v-model="depositType">
                <el-radio label="DEPOSIT">{{formatI18n('/储值/预付卡/预付卡充值单', '充值')}}</el-radio>
                <el-radio label="REFUND">{{formatI18n('/储值/预付卡/预付卡充值单', '充值退')}}</el-radio>
              </el-radio-group>
            </div>
            <el-upload
                    :headers="uploadHeaders"
                    :action="getUploadUrl"
                    :auto-upload="false"
                    :file-list="fileList"
                    :on-change="doHandleChange"
                    :on-error="getErrorInfo"
                    :on-success="getSuccessInfo"
                    :with-credentials="true"
                    class="upload-demo"
                    ref="upload">
                <el-button size="small" slot="trigger" type="default">{{formatI18n('/公用/导入', '选择文件')}}</el-button>
            </el-upload>
<!--            <ImportResultDialog-->
<!--                    :importResultCallback="importResultCallback"-->
<!--                    :importResultDialogShow="importResultDialogShow"-->
<!--                    @importResultDialogClose="doImportResultDialogClose">-->
<!--            </ImportResultDialog>-->
        </div>
        <div class="dialog-footer" slot="footer">
            <el-button @click="doModalClose('cancel')">{{formatI18n('/公用/按钮', '取消')}}</el-button>
            <el-button @click="doModalClose('confirm')" size="small" type="primary">{{formatI18n('/公用/按钮', '确定')}}</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts" src="./ImportDialog.ts">
</script>

<style lang="scss">
.import-dialog-view{
    display: flex;
    align-items: center;
    justify-content: center;
    .el-dialog{
        width: 500px;
        height: 310px;
        margin-top: 0px !important;
        .el-dialog__body{
          height: 200px;
          word-break: keep-all;
        }
    }
    .wrap{

        padding-left: 50px;
        .left{
            text-align: left;
            margin-bottom: 10px;
        }
    }
}
</style>
<style lang="scss" scoped>
.import-dialog-view{
  ::v-deep .el-dialog{
    height: auto;
    .el-dialog__body {
      padding-bottom: 0;
      height: auto;
    }
    .dialog-footer {
      margin-top: 0;
    }
  }
}
.action-hover_download {
  cursor: pointer;
  /* 添加手型光标 */
}
</style>