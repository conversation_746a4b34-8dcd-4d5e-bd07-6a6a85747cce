<template>
  <div class="entity-card-activity-view">
    <BreadCrume :panelArray="panelArray">
      <template slot="operate">
        <el-button type="primary" size="large" @click="add" v-if="hasOptionPermission('/卡/卡活动/实体卡售卡活动', '活动维护')">
          {{i18n('新建实体卡发售活动')}}
        </el-button>
      </template>
    </BreadCrume>
    <div class="current-page">
      <ListWrapper>
        <template slot="query">
          <MyQueryCmp @reset="doReset" @search="onSearch" :showExpand="false">
            <el-form :inline="true" label-width="100px" class="queryForm">
              <el-row>
                <el-col :span="8">
                  <form-item label="活动名称">
                    <el-input v-model="query.nameLikes" />
                  </form-item>
                </el-col>
                <el-col :span="8">
                  <form-item label="活动号">
                    <el-input v-model="query.activityIdLikes" />
                  </form-item>
                </el-col>
              </el-row>
            </el-form>
          </MyQueryCmp>
        </template>
        <template slot="list">
          <el-row class="row">
            <el-tabs v-model="tabName" @tab-click="handleTabClick">
              <el-tab-pane :label="allTab" name="ALL" />
              <el-tab-pane :label="initialTab" name="INITAIL" />
              <el-tab-pane :label="unstartTab" name="UNSTART" />
              <el-tab-pane :label="processingTab" name="PROCESSING" />
              <el-tab-pane :label="stopedTab" name="STOPED" />
            </el-tabs>
          </el-row>
          <FloatBlock :top="95" refClass="list-wrapper" v-if="tabName !== 'STOPED'">
            <template slot="ctx">
              <el-row class="row">
                <i18n k="/储值/预付卡/电子礼品卡活动/列表页面/已选择{0}个活动" style="margin-right: 16px">
                  <template slot="0"> {{selected.length}} </template>
                </i18n>
                <el-button v-if="['ALL', 'INITAIL'].indexOf(tabName) > -1 && hasOptionPermission('/卡/卡活动/实体卡售卡活动', '活动审核')" key="audit"
                  @click="auditBatch">
                  {{i18n("批量审核")}}
                </el-button>
                <el-button v-if="['ALL', 'UNSTART', 'PROCESSING'].indexOf(tabName) > -1 && hasOptionPermission('/卡/卡活动/实体卡售卡活动', '活动终止')"
                  key="stop" @click="stopBatch">
                  {{i18n("批量终止")}}
                </el-button>
                <el-button v-if="['ALL', 'INITAIL'].indexOf(tabName) > -1 && hasOptionPermission('/卡/卡活动/实体卡售卡活动', '活动维护')" key="del"
                  type="danger" @click="delBatch">
                  {{i18n("批量删除")}}
                </el-button>
              </el-row>
            </template>
          </FloatBlock>
          <el-row class="row">
            <el-table :data="queryData" style="width: 100%;margin-top: 20px" ref="table" v-loading="tableLoading"
              @selection-change="handleSelectionChange">
              <el-table-column v-if="tabName !== 'STOPED'" type="selection" width="55" />
              <el-table-column fixed label="活动名称" width="250">
                <template slot-scope="scope">
                  <p style="color: rgba(51, 51, 51, 0.647058823529412);">{{scope.row.body.activityId}}</p>
                  <span class="span-btn" :title="scope.row.body.name" no-i18n @click="gotoDtl(scope.row)">
                    {{scope.row.body.name}}
                  </span>
                </template>
              </el-table-column>
              <el-table-column fixed label="活动时间" width="200">
                <template slot-scope="scope">
                  <span no-i18n>{{activityTime(scope.row)}}</span>
                </template>
              </el-table-column>
              <el-table-column fixed label="状态" width="150">
                <template slot-scope="scope">
                  <ActivityState :state="scope.row.body.state" />
                </template>
              </el-table-column>
              <el-table-column fixed label="卡模板">
                <template slot-scope="scope">
                  <div class="operate-block" v-if="scope.row.detail.saleSpecs && scope.row.detail.saleSpecs.length">
                    <span v-for="(spec, indexSpec) in scope.row.detail.saleSpecs" :title="spec.cardTemplateName" :key="indexSpec" class="span-btn"
                      @click="gotoCardTplDtl(spec.cardTemplateNumber)">
                      {{spec.cardTemplateName}}
                    </span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column fixed label="卡类型" width="150">
                <template slot-scope="scope">
                  <span>{{ scope.row.detail.cardType | cardType}}</span>
                </template>
              </el-table-column>
              <el-table-column fixed label="操作" width="150">
                <template slot-scope="scope">
                  <div class="operate-block">
                    <span class="span-btn" @click="audit(scope.row.body.activityId)"
                      v-if="scope.row.body.state === 'INITAIL' && hasOptionPermission('/卡/卡活动/实体卡售卡活动', '活动审核')">
                      {{i18n('审核')}}
                    </span>
                    <span class="span-btn" @click="copy(scope.row.body.activityId)" v-if="hasOptionPermission('/卡/卡活动/实体卡售卡活动', '活动维护')">
                      {{i18n('复制')}}
                    </span>
                    <span class="span-btn" @click="stop(scope.row.body.activityId)"
                      v-if="['UNSTART', 'PROCESSING'].indexOf(scope.row.body.state) > -1 && hasOptionPermission('/卡/卡活动/实体卡售卡活动', '活动终止')">
                      {{i18n('终止')}}
                    </span>
                    <span class="span-btn" @click="edit(scope.row.body.activityId)"
                      v-if="(['INITAIL','PROCESSING','UNSTART'].indexOf(scope.row.body.state) > -1) && hasOptionPermission('/卡/卡活动/实体卡售卡活动', '活动维护')">
                      {{i18n('修改')}}
                    </span>
                    <span class="span-btn" v-if="scope.row.body.state === 'INITAIL' && hasOptionPermission('/卡/卡活动/实体卡售卡活动', '活动维护')"
                      @click="del(scope.row.body.activityId)">
                      {{i18n('删除')}}
                    </span>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </el-row>
        </template>
        <template slot="page">
          <el-pagination no-i18n :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40]" :total="page.total"
            @current-change="onHandleCurrentChange" @size-change="onHandleSizeChange" background layout="total, prev, pager, next, sizes,  jumper"
            class="pagin">
          </el-pagination>
        </template>
      </ListWrapper>
    </div>
  </div>
</template>

<script lang="ts" src="./EntityCardActivityList.ts">
</script>

<style lang="scss">
.entity-card-activity-view {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: auto;
  .total {
    margin: 20px;
  }

  .current-page {
    height: calc(100% - 46px);
    overflow: auto;
    .el-form-item {
      margin-bottom: 0;
    }
    .cell {
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .el-select {
      width: 100%;
    }
    .query {
      padding: 20px 20px 0 20px;
    }
    .row {
      padding: 0 20px;
      .state {
        width: 7px;
        height: 7px;
        border-radius: 10px;
        float: left;
        margin-top: 7px;
        margin-right: 5px;
      }
    }
    .list {
      height: calc(100% - 150px);
      overflow: hidden;
      .el-col {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .pagin {
      padding: 20px;
    }
  }
}
</style>
