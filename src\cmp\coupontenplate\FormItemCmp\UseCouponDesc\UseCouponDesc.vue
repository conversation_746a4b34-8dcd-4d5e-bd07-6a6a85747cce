<!--
 * @Author: 黎钰龙
 * @Date: 2024-01-24 14:07:05
 * @LastEditTime: 2024-04-24 15:14:05
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\coupontenplate\FormItemCmp\UseCouponDesc\UseCouponDesc.vue
 * 记得注释
-->
<template>
  <div>
    <el-form-item :label="i18n('/储值/预付卡/卡模板/编辑页面/使用须知')" prop="couponProduct" :rules="rules">
      <div style="color: #79879E" v-if="isShowTips">
        {{ i18n("同步至微盟的券最多1000字") }}
      </div>
      <!-- 由于v-model不适合直接绑子组件，所以暴露给父组件用slot绑 -->
      <slot name="slot"></slot>
    </el-form-item>
  </div>
</template>

<script lang="ts" src="./UseCouponDesc.ts">
</script>

<style>
</style>