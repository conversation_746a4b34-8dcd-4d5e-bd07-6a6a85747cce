/*
 * @Author: 黎钰龙
 * @Date: 2022-11-10 14:29:19
 * @LastEditTime: 2023-09-12 11:01:55
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\http\prepay\card\PrePayCardApi.ts
 * 记得注释
 */
import ApiClient from 'http/ApiClient'
import PrePayCard from 'model/prepay/card/PrePayCard'
import PrePayCardFilter from 'model/prepay/card/PrePayCardFilter'
import PrePayCardPresent from 'model/prepay/card/PrePayCardPresent'
import PrePayCardStateHst from 'model/prepay/card/PrePayCardStateHst'
import PrePayCardTransaction from 'model/prepay/card/PrePayCardTransaction'
import Response from 'model/common/Response'
import PrePayCardPasswd from 'model/prepay/card/PrePayCardPasswd'
import PrePayCardAdjust from "model/prepay/card/PrePayCardAdjust";
import BalanceAccountSummary from "model/prepay/card/BalanceAccountSummary";
import BPrepayCardHistoryHstFilter from "model/prepay/card/BPrepayCardHistoryHstFilter";
import PrepayCardBalanceStatistic from "model/prepay/card/PrepayCardBalanceStatistic";
import QueryCountCardHstRequest from 'model/card/template/QueryCountCardHstRequest'

export default class PrePayCardApi {

  /**
   * 调整卡有效期
   * 调整卡有效期
   *
   */
  static adjustValidPeriod(body: PrePayCardAdjust): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/prepay-card/adjustValidPeriod`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 汇总账户金额
   * 汇总账户金额
   *
   */
  static balanceAccountSum(type: string, marketingCenter?: string): Promise<Response<BalanceAccountSummary>> {
    return ApiClient.server().get(`/v1/prepay-card/balanceAccountSum`, {
      params: {
        type: type,
        marketingCenter: marketingCenter
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改购卡人
   * 修改购卡人
   *
   */
  static changeBuyerMember(code: string, name: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/prepay-card/changeBuyerMember/${code}?name=${name?name:''}`).then((res) => {
      return res.data
    })
  }

  /**
   * 查询卡转赠流水
   *
   */
  static getCardPresent(code: string): Promise<Response<PrePayCardPresent[]>> {
    return ApiClient.server().get(`/v1/prepay-card/get/present/${code}`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询卡状态流水
   *
   */
  static getCardStateHst(code: string): Promise<Response<PrePayCardStateHst[]>> {
    return ApiClient.server().get(`/v1/prepay-card/get/state/hst/${code}`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 作废卡
   *
   */
  static invalid(code: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/prepay-card/invalid/${code}`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 冻结
   *
   */
  static freeze(code: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/prepay-card/freeze/${code}`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 解冻
   *
   */
  static unfreeze(code: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/prepay-card/unfreeze/${code}`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 挂失
   *
   */
  static loss(code: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/prepay-card/loss/${code}`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 取消挂失
   *
   */
  static unLoss(code: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/prepay-card/unLoss/${code}`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 重置密码
   *
   */
  static reissue(sourceCode: string, newCode: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/prepay-card/reissue?sourceCode=${sourceCode}&newCode=${newCode}`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 重置密码
   *
   */
  static resetPassWord(passwd: PrePayCardPasswd): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/prepay-card/resetPassWord`, passwd, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 转赠卡
   *
   */
  static presentCard(mobile: string, cardCode: string): Promise<Response<string>> {
    return ApiClient.server().post(`/v1/prepay-card/present/${cardCode}/${mobile}`, {}, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 查询卡列表信息
   *
   */
  static query(body: PrePayCardFilter): Promise<Response<PrePayCard[]>> {
    return ApiClient.server().post(`/v1/prepay-card/query`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 导出卡列表信息
   *
   */
  static exportCard(body: PrePayCardFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/prepay-card/exportCard`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 统计卡余额信息
   * 统计卡余额信息
   *
   */
  static sumBalance(body: PrePayCardFilter): Promise<Response<PrepayCardBalanceStatistic>> {
    return ApiClient.server().post(`/v1/prepay-card/sumBalance`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询卡流水信息
   *
   */
  static queryHst(accountId: string, page: number, pageSize: number): Promise<Response<PrePayCardTransaction[]>> {
    return ApiClient.server().get(`/v1/prepay-card/queryHst?account=${accountId}`, {
      params: {
        page: page,
        pageSize: pageSize
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询卡流水信息
   *
   */
  static queryHistoryHst(body: BPrepayCardHistoryHstFilter): Promise<Response<PrePayCardTransaction[]>> {
    return ApiClient.server().post(`/v1/prepay-card/queryHistoryHst`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
 * 查询计次卡流水信息
 * 查询计次卡流水信息
 * 
 */
  static queryCountingCardHst(body: QueryCountCardHstRequest): Promise<Response<PrePayCardTransaction[]>> {
    return ApiClient.server().post(`/v1/prepay-card/countCardHst`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询计次卡状态流水信息
   * 查询计次卡状态流水信息
   * 
   */
  static countCardStateHst(code: string): Promise<Response<PrePayCardStateHst[]>> {
    return ApiClient.server().post(`/v1/prepay-card/countCardStateHst/${code}`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
  * 坏卡登记
  * 坏卡登记
  * 
  */
  static regBroken(code: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/prepay-card/regBroken/${code}`, {}, {
    }).then((res) => {
      return res.data
    })
  }
}
