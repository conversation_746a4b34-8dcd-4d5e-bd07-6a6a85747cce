<template>
  <el-dialog :title="formatI18n('/公用/公共组件/券模板选择弹框组件/标题/选择券模板')" class="select-coupon-template-tpl-dialog" append-to-body :close-on-click-modal="false"
    :visible.sync="dialogShow" v-if="dialogShow">
    <div class="wrap">
      <el-row>
        <el-form :label-width="labelWidth">
          <el-row>
            <el-col :span="8">
              <el-form-item :label="formatI18n('/公用/券模板', '券名称')">
                <el-input v-model="filter.nameLikes" :placeholder="formatI18n('/公用/查询条件/提示/类似于')" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="formatI18n('/权益/券/券模板/券模板号')">
                <el-input v-model="filter.numberEquals" :placeholder="formatI18n('/营销/券礼包活动/券查询/券号输入框placeholder/等于')" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="formatI18n('/营销/券礼包活动/核销第三方券', '券类型')">
                <el-select :placeholder="formatI18n('/公用/下拉框/提示', '请选择')" v-model="filter.typeEquals" style="width: 100%;" clearable>
                  <el-option :label="formatI18n('/公用/券模板', '现金券')" value="all_cash" v-if="showThisType('all_cash')">
                    {{ formatI18n('/公用/券模板', '现金券') }}
                  </el-option>
                  <el-option :label="formatI18n('/公用/券模板', '折扣券')" value="all_discount"
                    v-if="showThisType('all_discount')">{{ formatI18n('/公用/券模板', '折扣券') }}
                  </el-option>
                  <el-option :label="formatI18n('/公用/券模板', '兑换券')" value="exchange_goods" v-if="showThisType('exchange_goods')">
                    {{ formatI18n('/公用/券模板', '兑换券') }}
                  </el-option>
                  <el-option :label="formatI18n('/公用/券模板', '特价券')" value="special_price" v-if="showThisType('special_price')">
                    {{ formatI18n('/公用/券模板', '特价券') }}
                  </el-option>
                  <el-option :label="formatI18n('/公用/券模板', '提货券')" value="goods" v-if="showThisType('goods')">
                    {{ formatI18n('/公用/券模板', '提货券') }}
                  </el-option>
                  <el-option :label="formatI18n('/公用/券模板', '运费券')" value="freight" v-if="showThisType('freight')">
                    {{ formatI18n('/公用/券模板', '运费券') }}
                  </el-option>
                  <el-option :label="formatI18n('/公用/券模板', '随机金额券')" value="random_cash" v-if="isRandomCoupon && showThisType('random_cash')">
                    {{ formatI18n('/公用/券模板', '随机金额券') }}
                  </el-option>

                  <el-option :label="formatI18n('/公用/券模板', '积分券')" value="points"
                    v-if="enablePointsExchangeCouponDisplay === true && showThisType('points')">
                    {{ formatI18n('/公用/券模板', '积分券') }}
                  </el-option>
                  <el-option :label="formatI18n('/公用/券模板', '权益券')" value="equity"
                    v-if="enableEquityCouponDisplay === true && showEquityType('equity')">
                    {{ formatI18n('/公用/券模板', '权益券') }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8" v-if="!hideState">
              <el-form-item :label="formatI18n('/营销/券礼包活动/券查询', '券状态')">
                <el-select :disabled="true" :placeholder="formatI18n('/公用/下拉框/提示', '请选择')" style="width: 100%;" v-model="filter.stateEquals">
                  <el-option :label="formatI18n('/权益/券/券模板/已生效')" value="EFFECTED">
                    {{ formatI18n('/权益/券/券模板/已生效') }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="formatI18n('/营销/券礼包活动/券查询', '券模板来源')" v-if="headquarters === false && hideSource === false">
                <el-select v-model="marketCenter" :placeholder="formatI18n('/资料/渠道/请选择')" style="width: 100%" @change="doSearch()">
                  <el-option v-for="(value,index) in marketingCentersList" :key="index" :value="value.marketingCenter.id"
                    :label="'['+value.marketingCenter.id+']'+value.marketingCenter.name">[{{value.marketingCenter.id}}]{{value.marketingCenter.name}}</el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="isShowTag">
              <el-form-item :label="formatI18n('/公用/券模板', '标签')">
                <el-select v-model="mustHaveTagUuids" multiple collapse-tags placeholder="请选择" style="width: 100%;">
                  <el-option v-for="item in tagList" :key="item.uuid" :label="item.value" :value="item.uuid">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item>
                <el-button type="primary" @click="doSearch()">{{ formatI18n('/公用/按钮/查询') }}</el-button>
                <el-button @click="doReset()">{{ formatI18n('/公用/按钮/重置') }}</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-row>
      <el-row>
        <el-col :span="model === 'multiple' ? 17 : 24">
          <el-row class="table-wrap" v-loading="loading.query">
            <div class="thead" style="display: flex;">
              <div style="flex: 1;">
                <el-checkbox style="margin-left: 8px;" v-if="model === 'multiple'" @change="doCheckAll($event)" v-model="checkAll" />
                <span v-else>&nbsp;</span>
              </div>
              <div style="flex: 4">{{ formatI18n('/公用/券模板', '券名称') }}</div>
              <div style="flex: 3">{{ formatI18n('/营销/券礼包活动/核销第三方券', '券类型') }}</div>
              <div :title="formatI18n('/权益/券/券模板/券面额/折扣力度')" style="flex: 4; overflow: hidden;text-overflow: ellipsis;white-space: nowrap">
                {{ formatI18n('/权益/券/券模板/券面额/折扣力度') }}
              </div>
              <div v-if="showActivityReference" :title="formatI18n('/公用/券模板/关联券模板活动')"
                style="flex: 4; overflow: hidden;text-overflow: ellipsis;white-space: nowrap">
                {{ formatI18n('/公用/券模板/关联券模板活动') }}
              </div>
              <div v-if="isShowTag" :title="formatI18n('/公用/券模板/标签')"
                style="flex: 3; overflow: hidden;text-overflow: ellipsis;white-space: nowrap;text-align:center">
                {{ formatI18n('/公用/券模板/标签') }}
              </div>
              <div :title="formatI18n('/公用/券模板', '用券渠道')" style="flex: 3; overflow: hidden;text-overflow: ellipsis;white-space: nowrap">
                {{ formatI18n('/公用/券模板', '用券渠道') }}
              </div>
              <div :title="formatI18n('/公用/券模板', '用券门店')" style="flex: 4; overflow: hidden;text-overflow: ellipsis;white-space: nowrap">
                {{ formatI18n('/公用/券模板', '用券门店') }}
              </div>
            </div>
            <el-row class="tbody" v-if="!loading.query" style="overflow-x:auto">
              <template v-if="currentList && currentList.length > 0">
                <div v-for="(item, index) of currentList" :key="index" style="display: flex;" class="trow">
                  <!-- 勾选框 -->
                  <div style="flex: 1;">
                    <div class="height-set">
                      <span style="position: relative;top: 18px;left: 8px;">
                        <el-checkbox
                          :disabled="(couponType === 'wxCoupon' && (item.type === 'goods' || item.type === 'goods_discount'))||alwaysSelectedIds.includes(item.number)"
                          v-model="checkboxList[index]" @change="doCheck($event, index)" />
                      </span>
                    </div>
                  </div>
                  <!-- 券名称 -->
                  <div @click="doCheckRow(index)" style="flex: 4;">
                    <div class="height-set">
                      <div>{{ item.number | strFormat }}</div>
                      <div :title="item.name" @click.stop="doToDtl(item.number)"
                        style="color: #20a9ff;text-align: left;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 145px;cursor: pointer">
                        {{ item.name }}
                      </div>
                    </div>
                  </div>
                  <!-- 券类型 -->
                  <div @click="doCheckRow(index)" style="flex: 3;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 100%;"
                    :title="item.type | lineCouponType" class="height-set line-height-set">
                    {{ item.type | lineCouponType }}
                  </div>
                  <!-- 券面额/折扣力度 -->
                  <div @click="doCheckRow(index)" :title="getUnit(item.faceAmountOrDiscount, item.type)" class="height-set line-height-set"
                    style="flex: 4;padding-left: 10px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 100%;">
                    {{ getUnit(item.faceAmountOrDiscount, item.type) }}
                  </div>
                  <!-- 关联券模板活动 -->
                  <div v-if="showActivityReference" @click="doCheckRow(index)" class="height-set line-height-set"
                    style="flex: 4;padding-left: 10px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 100%;">
                    <span :title="item.activityReference[0]" v-if="item.activityReference && item.activityReference.length === 1">
                      {{ item.activityReference[0] }}
                    </span>
                    <el-popover v-else-if="item.activityReference && item.activityReference.length" placement="top-start" width="200" trigger="hover">
                      <div>
                        <div v-for="(v, i) in item.activityReference" :key="i">{{ v }}</div>
                      </div>
                      <span slot="reference">
                        <span>{{ item.activityReference[0] }}</span>
                        <el-button style="margin-left: 6px;" type="text">{{ `+${item.activityReference.length - 1}` }}</el-button>
                      </span>
                    </el-popover>
                    <span v-else>--</span>
                  </div>
                  <!-- 标签 -->
                  <div v-if="isShowTag" style="flex: 3;text-align:center;padding-left:0" class="height-set line-height-set overflow_text"
                    :title="getTemplateTag(item.templateTag)">
                    <el-tooltip class="item" placement="top-start" effect="light" :disabled="!item.templateTag || item.templateTag.length <= 1">
                      <el-button type="text" style="color:black;cursor:default">
                        <span>{{ getTemplateTag(item.templateTag) }}</span>
                        <span v-if="item.templateTag && item.templateTag.length > 1" style="font-size:18px">...</span>
                      </el-button>
                      <div slot="content" style="padding:0 15px 0 5px">
                        <span v-for="val in item.templateTag" :key="val.tagUuid">{{val.tagValue}}<br /></span>
                      </div>
                    </el-tooltip>
                  </div>
                  <!-- 用券渠道 -->
                  <div @click="doCheckRow(index)" style="flex: 3;">
                    <div class="height-set line-height-set"
                      style="text-align: left;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 100%;padding-left:0"
                      :title="getChannel(item.channels)">
                      {{ getChannel(item.channels) }}
                    </div>
                  </div>
                  <!-- 用券门店 -->
                  <div @click="doCheckRow(index)" style="flex: 4;">
                    <div class="height-set line-height-set" style="overflow: hidden;white-space: nowrap;text-overflow: ellipsis"
                      :title="storeRangeStr(item)">
                      {{storeRangeStr(item)}}
                    </div>
                  </div>
                </div>
              </template>
              <el-row v-if="!currentList || currentList.length === 0" class="trow" style="text-align: center;color: #909399">
                {{ formatI18n('/公用/提示/暂无数据') }}
              </el-row>
            </el-row>
          </el-row>
        </el-col>
        <el-col :span="model === 'multiple' ? 7 : 0" style="padding-left: 5px">
          <el-row class="right-table">
            <el-row class="thead">
              {{ formatI18n('/公用/公共组件/券模板选择弹框组件/表格/已选券模板：') }}{{
                selected ? (selected.filter(e => e.number)).length : 0
              }}
            </el-row>
            <el-row style="padding: 5px;">
              <el-input style="width: 100%;" @keyup.enter.native="filterSelected()" @change="filterSelected()" v-model="selectedFilter" clearable
                @clear="filterSelected()" :placeholder="formatI18n('/公用/公共组件/券模板选择弹框组件/查询/请输入券模板号/券名称')"></el-input>
            </el-row>
            <el-row class="tbody">
              <template v-if="filteredSelected && filteredSelected.length > 0">
                <el-row class="trow" style="position: relative;display: flex;align-items: center" v-for="(item, index) of filteredSelected"
                  :key="index" :title="{id:item.number,name:item.name}|idName">
                  <div class="left">{{ {id: item.number, name: item.name}|idName }}</div>
                  <div class="clear-btn" style="display: none">
                    <a @click="delItem(item, index)" v-if="!alwaysSelectedIds.includes(item.number)">{{ formatI18n('/公用/公共组件/品牌选择弹框组件/表格/清除') }}</a>
                  </div>
                </el-row>
              </template>
              <el-row v-if="!filteredSelected || filteredSelected.length === 0" class="trow" style="text-align: center;color: #909399">
                {{ formatI18n('/公用/提示/暂无数据') }}
              </el-row>
            </el-row>
          </el-row>
        </el-col>
      </el-row>
    </div>
    <div class="page" style="margin-top: 15px">
      <el-pagination :current-page="page.currentPage" :page-size="page.size" :page-sizes="[10, 20, 30, 40, 100]" :total="page.total"
        @current-change="handleCurrentChange($event)" @size-change="handleSizeChange($event)" background layout="total, prev, pager, next, sizes,  jumper">
      </el-pagination>
    </div>
    <div slot="footer" class="dialog-footer" style="position: relative;top: -27px;">
      <el-button size="small" @click="doCancel()">{{ formatI18n('/公用/按钮/取消') }}</el-button>
      <el-button size="small" type="primary" @click="doModalClose()">{{ formatI18n('/公用/按钮/确定') }}</el-button>
    </div>
    <SelectStoreActiveDtlDialog :baseSettingFlag="true" :child="child" :dialogShow="couponDialogShow" @dialogClose="doCouponDialogClose">
    </SelectStoreActiveDtlDialog>
  </el-dialog>
</template>

<script lang="ts" src="./CouponTemplateSelectorDialog.ts"/>

<style lang="scss" scoped>
.select-coupon-template-tpl-dialog {
  display: flex;
  align-items: center;
  justify-content: center;
  @import "SelectorDialogCommon";

  .height-set {
    height: 68px;
    border-bottom: 1px solid #eeeeee;
  }

  .line-height-set {
    line-height: 68px;
    padding-left: 7px;
  }

  .wrap {
    height: 615px;
  }

  .wrap .table-wrap .tbody .trow {
    height: 68px;
    line-height: 34px;
  }

  .overflow_text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }
}
</style>
<style>
.short-label .el-form-item__label {
  width: 100px !important;
}
.select-coupon-template-tpl-dialog .el-dialog {
  width: 1407px !important;
  height: 800px !important;
  margin-top: 0 !important;
}
</style>
