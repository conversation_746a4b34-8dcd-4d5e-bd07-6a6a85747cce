<template>
    <div class="alipay-member-code">
        <BreadCrume :panelArray="panelArray">
        </BreadCrume>
        <div style="margin-top: 30px">
            <el-steps :active="active" :align-center="true" finish-status="success" process-status="finish">
                <el-step title="支付宝应用申请"></el-step>
                <el-step title="创建会员卡"></el-step>
                <el-step title="会员卡投放"></el-step>
            </el-steps>
        </div>

        <div style="margin: 50px 0 0 30px;">您已完成会员卡的创建，可以下载以下领卡二维码图片，设计成海报张贴到门店来吸引顾客领卡成为会员，也可通过其他渠道来投放支付宝会员卡</div>
        <div style="font-size: 16px;font-weight: 700;margin-left: 30px;margin-top: 30px">
            下载二维码
        </div>
        <div class="code-item" @click="goAllStoresQrCode">
            <i class="iconfont ic-download"></i>
            {{formatI18n('/会员/微信会员初始化/已授权/初始化第三步投放微信会员卡/所有门店使用同一个二维码')}}
            <VueQrcode v-if="qrCodeUrl" id="qrcode" :value="qrCodeUrl" style="display: none"></VueQrcode>
        </div>
        <div class="code-item" @click="goStoreQrCode">
            <i class="iconfont ic-download"></i>
            {{formatI18n('/会员/微信会员初始化/已授权/初始化第三步投放微信会员卡/不同门店使用不同二维码')}}
        </div>

        <div style="margin-top: 30px;margin-left: 30px">
            <el-button  @click="toWechatMemberCard">{{formatI18n('/会员/等级/等级管理/未初始化状态/点击立即开始免费等级初始化/输入所有必填项点击下一步/底部按钮/上一步')}}</el-button>
            <!--<div class="code-next" @click="toWechatPayRuleEdit">{{formatI18n('/会员/等级/等级管理/未初始化状态/点击立即开始免费等级初始化/输入所有必填项点击下一步/底部按钮/下一步')}}</div>-->
            <el-button type="primary" @click="doCompleted">完成</el-button>
        </div>
        <StoreCodeDownload ref="downloadDialog"></StoreCodeDownload>
    </div>
</template>

<script lang="ts" src="./AlipayMemberQrCode.ts">
</script>

<style lang="scss">
    .alipay-member-code {
        width: 100%;
        height: 100%;
        background-color: white;
        padding: 0;
        overflow: auto;
        .step-bottom3 {
            border-bottom: 2px solid rgb(49, 137, 253);
        }
        .code-define {
            margin: 50px 120px;
            padding: 10px;
            font-size: 13px;
            color: #333333;
            background-color: #f5fbf9;
        }
        .code-item {
            width: 300px;
            display: flex;
            justify-self: center;
            align-items: center;
            font-size: 16px;
            font-weight: 400;
            border: 1px solid #e2e2e2;
            border-radius: 5px;
            margin-left: 30px;
            margin-top: 30px;
            padding: 8px 20px;
            cursor: pointer;
            .ic-download {
                font-size: 25px;
                font-weight: 600;
                color: #ffff;
                border-radius: 50%;
                background-color: #33cb98;
                padding: 5px;
                margin-right: 15px;
            }
        }
        .flex-code {
            display: flex;
            justify-content: center;
            margin: 75px auto 0;
            font-size: 14px;
            line-height: 1.6;
            .code-back {
                color: #333333;
                background-color: #fff;
                border: 1px solid #e2e2e2;
                border-radius: 5px;
                padding: 5px 20px;
                cursor: pointer;
                margin-right: 30px;
            }
            .code-next {
                color: #fff;
                background-color: #3189fd;
                border: 1px solid #3189fd;
                border-radius: 5px;
                padding: 5px 20px;
                cursor: pointer;
            }
        }
        .el-step__head.is-success{
            color: #20a0ff !important;
            border-color: #20a0ff !important;
        }
        .el-step__title.is-success{
            color: #a0abbc !important;
        }
        .el-step__title.is-finish{
            color: #a0abbc !important;
        }
    }
</style>