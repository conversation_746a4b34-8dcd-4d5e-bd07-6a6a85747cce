/*
 * @Author: gm-liting
 * @Date: 2024-05-17 15:13:51
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\pages\invest\nav-manage\cmp\custom-tab\CustomTab.ts
 * 记得注释
 */


import I18nPage from 'common/I18nDecorator';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import UploadImg from '../upload-img/UploadImg.vue'
import PageJump from 'pages/invest/page-manage/cmp/page-props-components/page-jump/PageJump.vue';
import { MenuIconsCommon, MenuIconsRudder } from 'model/local/MenuIcons';
import JumpPageInfo from 'model/navigation/JumpPageInfo'
import { IconColorStyle } from 'model/navigation/IconColorStyle'
import { NavigationStyle } from 'model/navigation/NavigationStyle'
import MenuSetting from 'model/navigation/MenuSetting'
import { debug } from 'console';
import NavigationSetting from 'model/navigation/NavigationSetting'
import { CmsConfigChannel } from 'model/template/CmsConfig';


// 根据接口所需字段拓展前端展示所需字段
class customMenuSetting extends MenuSetting {
  showRepealce?: boolean // 已选择情况
  isUseIcon?: boolean // 是否使用的 icon
  // 默认左侧回显不选中
  isActive: boolean = false
}

class AddMenuData extends NavigationSetting {
  // 菜单设置
  menuSettings: customMenuSetting[] = []
}

// class colorForm {
//   // 选中的颜色
//   selectedColor: Nullable<string> = null
//   // 未选中的颜色
//   unselectedColor: Nullable<string> = null
//   // 选中的文本颜色
//   selectedFontColor: Nullable<string> = null
//   // 选中的图标颜色
//   selectedIconColor: Nullable<string> = null
//   // 未选中的文本颜色
//   unselectedFontColor: Nullable<string> = null
//   // 未选中的图标颜色
//   unselectedIconColor: Nullable<string> = null
//   // 菜单数量
//   menuQuantity: Nullable<number> = null
//   // 菜单设置
//   menuSettings: customMenuSetting[] = []
// }

// class Form {
//   // navSet: NavItem = new NavItem()
//   // 导航样式
//   navigationType: Nullable<NavigationStyle> = null
//   // 图标配色
//   iconColorType: Nullable<IconColorStyle> = null
//   nromalSystem: colorForm = new colorForm()
//   nromalCustom: colorForm = new colorForm()
//   rudderSystem: colorForm = new colorForm()
//   rudderCustom: colorForm = new colorForm()
// }

// 图标展示数据
class iconType {
  icon: string
  ischecked: boolean
}


@Component({
  name: 'CustomTab',
  components: { UploadImg, PageJump }
})


@I18nPage({
  prefix: [
    '/导航',
    '/页面/导航设置',
    '/页面/页面管理'
  ],
  auto: true
})
export default class CustomTab extends Vue {
  @Prop() customForm: AddMenuData
  @Prop()channel: CmsConfigChannel
  $refs: any
  currentForm: AddMenuData = new AddMenuData()
  rules: any = {}
  uploadDialogVisible: boolean = false // 上传弹窗
  dialogModelName: string = 'system' // 系统图标system ｜ 自定义图标 custom
  systemUnSelected: boolean = false
  systemSelected: boolean = false
  customCenter: boolean = true
  demoMenuIconsCommon: any[] = [] // 通用图标
  demoMenuIconsRudder: any[] = [] // 舵式图标
  menuOperationIndex: number = 0; // 上传/替换icon 菜单的下标

  selectedIcon: string = '' // 弹层选中的图标
  unselectedIcon: string = '' // 弹层未选中的图标

  predefineColors: string[] = [
    'rgb(174, 19, 31)',
    'rgb(224, 156, 44)',
    'rgb(242, 226, 48)',
    'rgb(117, 77, 40)',
    'rgb(142, 203, 44)',
    'rgb(72, 105, 19)',
    'rgb(156, 33, 217)',
    'rgb(116, 35, 250)',
    'rgb(87, 134, 219)',
    'rgb(127, 222, 186)',
    'rgb(190, 228, 127)',
    'rgb(0, 0, 0)',
    'rgb(65, 65, 65)',
    'rgb(144, 144, 144)'
  ]
  mounted() {
    this.init()
    this.applyIconData()
  }

  get isEnglish() {
    return sessionStorage.getItem("locale") === "en_US";
  }


  @Watch("customForm", { deep: true })
  onCustomFormChange(value: any) {
    if (value) {
      this.currentForm = value
    }
  }

  // 如果导航样式切换 清空导航列表数据
  @Watch("customForm.navigationType",)
  onNavigationTypeChange(value: any, oldValue: any) {
    this.currentForm.menuSettings.splice(0, this.currentForm.menuSettings.length)
  }
  // 处理图标字段
  applyIconData() {
    Object.keys(MenuIconsCommon).forEach(item => {
      let obj: iconType = {
        icon: item,
        ischecked: false
      }
      this.demoMenuIconsCommon.push(obj)
    })

    Object.keys(MenuIconsRudder).forEach(item => {
      let obj: iconType = {
        icon: item,
        ischecked: false
      }
      this.demoMenuIconsRudder.push(obj)
    })
  }

  doFormChange() {
    // 通知父组件更新数据
    this.$emit('update')
    this.$forceUpdate()
  }

  /* 
    舵式导航
    中心菜单 未选中 (直接更新数据)
   */
  rudderUnHandleChange(res: any, index: number) {
    this.currentForm.menuSettings[index].unselectedIcon = res
    this.doFormChange()
  }
  // 舵式导航 中心菜单 选中
  rudderHandleChange(res: any, index: number) {
    this.currentForm.menuSettings[index].selectedIcon = res
    this.doFormChange()
  }

  // 删除中心菜单未选中图标
  delCenterUnImg(index: number) {
    this.currentForm.menuSettings[index].unselectedIcon = ''
    this.doFormChange()
  }

  // 删除中心菜单选中图标
  delCenterImg(index: number) {
    this.currentForm.menuSettings[index].selectedIcon = ''
    this.doFormChange()
  }

  init() {
    this.of(this.customForm)
    this.currentForm.menuQuantity = 3
    this.rules = {
      'menuQuantity': [{ required: true, validator: this.validateMenuQuantity, trigger: "change" }],
      'menuName': [{ validator: this.validateMenuName, trigger: 'blur', required: true }],
      'menuIcon': [{ validator: this.validateMenuIcon, trigger: ['blur', 'change'], required: true }],
      'menuLink': [{ validator: this.validateMenuLink, trigger: ['change'], required: true }],
    }
  }

  validateMenuQuantity(rule: any, value: any, callback: any) {
    if (this.customForm.menuQuantity != 3 && this.customForm.menuQuantity != 5) {
      callback(new Error(this.i18n('请选择菜单数量')));
    } else {
      callback();
    }
  }

  validateMenuName(rule: any, value: any, callback: any) {
    if(!value) {
      callback(new Error(this.i18n('/页面/页面管理/请输入菜单名称')))
    }else {
      callback()
    }
    // const regex = /^.{1,10}$/;
    // if (!value || regex.test(value)) {
    //   callback();
    // } else {
    //   callback(new Error(this.i18n('菜单名称长度不超过10位')));
    // }
  }

  validateMenuIcon(rule: any, value: any, callback: any) {
    if (!value.unselectedIcon || !value.selectedIcon) {
      callback(new Error(this.i18n('请选择图标')));
    } else {
      callback();
    }
  }

  validateMenuLink(rule: any, value: any, callback: any) {
    if (!value) {
      callback(new Error(this.i18n('请选择链接')));
    } else {
      callback();
    }
  }

  //子组件数据更新，同步到当前页面
  updateData() {
    this.currentForm = this.$refs['customColor'].toParams()
    this.doFormChange()
  }

  of(params: NavigationSetting) {
    Object.assign(this.currentForm, params)
  }

  toParams() {
    // 将当前组件操作数据传给父组件
    const res = JSON.parse(JSON.stringify(this.currentForm))
    return [res]
  }

  /**
   * 通用菜单增删移动
   * @param type 
   * @param index 
   */
  menuChange(type: string, index: number) {
    const list = this.currentForm.menuSettings;
    // console.log(...list.map(e=>e.name));
    if (type === 'add') {
      list.push({
        // 默认使用系统图标
        isUseIcon: true,
        // 菜单名称
        name: '',
        // 已选中图标
        selectedIcon: '',
        // 未选中图标
        unselectedIcon: '',
        // 跳转页面信息
        jumpPageInfo: new JumpPageInfo(),
        // 默认左侧回显不选中
        isActive: false
      });
    } else if (type === 'up') {
      [list[index], list[index - 1]] = [list[index - 1], list[index]]
      list.splice(index, 0);
    } else if (type === 'down') {
      [list[index], list[index + 1]] = [list[index + 1], list[index]]
      list.splice(index, 0);
    } else if (type === 'remove') {
      list.splice(index, 1);
      let hasActive = list.some(item => item.isActive)
      if (!hasActive) list[0].isActive = true

    }
    this.doFormChange();
  }

  validate() {
    return this.$refs.ruleForm.validate()
  }

  /**
   * 
   * 点击图标替换/ 弹窗出现
   * 上传/替换一套
   */
  doCheckedIcon(item: any, index: number) {
    // console.log(item, index);
    this.uploadDialogVisible = true;
    this.menuOperationIndex = index
    this.dialogModelName = 'system'

    // 存在选中图标 替换
    // if (item.selectedIcon) {
    if (this.customForm.navigationType === 'normal') {
      this.demoMenuIconsCommon.forEach(element => {
        element.ischecked = false
        if (element.icon === item.selectedIcon) {
          element.ischecked = true
        }
      });
    } else {


      this.demoMenuIconsRudder.forEach(element => {
        element.ischecked = false
        if (element.icon === item.selectedIcon) {
          element.ischecked = true
        }
      });
    }
    // }

    // 如果开始上传过图片 需要回显原来的图片
    if (this.customForm.menuSettings[index].selectedIcon) {
      this.selectedIcon = this.customForm.menuSettings[index].selectedIcon
      this.unselectedIcon = this.customForm.menuSettings[index].unselectedIcon
    }

    // 渲染图标 系统图标
    if (item.isUseIcon) {
      this.dialogModelName = 'system'
      console.log('系统图标');
    } else {
      // 自定义图片
      this.dialogModelName = 'custom'
      console.log('自定义图片');
      // 如果开始上传过图片 需要回显原来的图片
      if (this.customForm.menuSettings[index].selectedIcon) {
        // 清空图片
        this.$refs.dialogUnUploadDom?.initImg()
        this.$refs.dialogUploadDom?.initImg()
      }
    }
  }

  // 选择系统图标
  doSelectedIcon(kind: any, key: any) {
    let arr = []
    if (kind === 'normal') {
      arr = this.demoMenuIconsCommon
    } else {
      arr = this.demoMenuIconsRudder
    }
    arr.forEach(item => {
      item.ischecked = false
    })
    key.ischecked = true
  }


  // 自定义图标 未选中
  dialogUnUploadImg(res: any, index: number) {
    this.unselectedIcon = res // 弹层未选中的图标
    // 使用自定义图标
    // this.customForm.menuSettings[this.menuOperationIndex].isUseIcon = false

  }
  // 自定义图标 选中
  dialogUploadImg(res: any, index: number) {
    this.selectedIcon = res // 弹层选中的图标
    // 使用自定义图标
    // this.customForm.menuSettings[this.menuOperationIndex].isUseIcon = false
  }
  /**
   * 关闭选择图标弹窗 切换系统图标/自定义图标
  */
  doCancel() {
    // 清空选中图标/图片
    this.selectedIcon = ''
    this.unselectedIcon = ''
    this.$refs.dialogUnUploadDom.deleteImg()
    this.$refs.dialogUploadDom.deleteImg()
    this.uploadDialogVisible = false;
  }
  /*
   * 选择图标弹窗确认按钮
    确定用户采用系统图标还是自定义图标
  */
  doDialogComfirm() {
    console.log(this.dialogModelName);
    // 操作第几个菜单
    let index = this.menuOperationIndex
    // 系统图标
    if (this.dialogModelName === 'system') {
      this.customForm.menuSettings[index].isUseIcon = true

      if (this.customForm.navigationType === 'normal') {
        this.demoMenuIconsCommon.forEach(element => {
          if (element.ischecked) {
            this.selectedIcon = element.icon
            this.unselectedIcon = element.icon
          }
        });
      } else if (this.customForm.navigationType === 'rudder') {
        this.demoMenuIconsRudder.forEach(element => {
          if (element.ischecked) {
            this.selectedIcon = element.icon
            this.unselectedIcon = element.icon
          }
        });
      }

      if (!this.selectedIcon || !this.unselectedIcon) {
        this.$message.warning(this.i18n('您还未选择图标'))
        return;
      }

      // // 清空图片
      // this.$refs.dialogUnUploadDom.deleteImg()
      // this.$refs.dialogUploadDom.deleteImg()

    } else {
      let regex = /.jpg|.jpeg|.png|.gif/i;
      if (!this.selectedIcon || !this.unselectedIcon || !regex.test(this.selectedIcon) || !regex.test(this.unselectedIcon)) {
        this.$message.warning(this.i18n('您还未上传图片'))
        return;
      }
      // 使用自定义图标
      this.customForm.menuSettings[index].isUseIcon = false
    }

    // 修改当前自己组件的数据
    this.customForm.menuSettings[index].selectedIcon = this.selectedIcon
    this.customForm.menuSettings[index].unselectedIcon = this.unselectedIcon


    // 清空选中图标/图片
    this.selectedIcon = ''
    this.unselectedIcon = ''
    // 清空图片
    this.$refs.dialogUnUploadDom.deleteImg()
    this.$refs.dialogUploadDom.deleteImg()
    this.doFormChange()
    this.uploadDialogVisible = false;
    this.$refs.ruleForm.validate()
  }

  // 选中自定义上传的图标
  get menuOperationSelectedIcon() {
    let obj = this.customForm.menuSettings[this.menuOperationIndex]
    // console.log(123,obj);
    if (obj && !obj.isUseIcon) {
      return obj.selectedIcon
    }
    return ''
  }

  // 未选中自定义上传的图标
  get menuOperationUnSelectedIcon() {
    let obj = this.customForm.menuSettings[this.menuOperationIndex]
    if (obj && !obj.isUseIcon) {
      return obj.unselectedIcon
    }
    return ''
  }

  changeLinkDialog(index: number) {
    // 已知当前操作的哪条数据
    this.menuOperationIndex = index
    this.$refs.jumpPage.dialogShow = true
    this.$refs.jumpPage.setJumpPageInfo()
  }

  // 选择跳转页面回调
  changeLink(val: object, index: number) {
    // let obj = this.customForm.menuSettings[this.menuOperationIndex].jumpPageInfo
    // console.log(obj);
    let i = index
    let obj = this.customForm.menuSettings[i].jumpPageInfo
    Object.assign(obj, val)

    // this.$refs.jumpPage.dialogShow = false
    this.$refs.ruleForm.validate()
    this.doFormChange()
  }
};