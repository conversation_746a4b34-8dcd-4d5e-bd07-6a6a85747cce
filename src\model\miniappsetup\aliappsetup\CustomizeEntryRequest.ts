/*
 * @Author: 黎钰龙
 * @Date: 2023-03-29 12:42:43
 * @LastEditTime: 2023-03-30 15:32:46
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\model\miniappsetup\aliappsetup\CustomizeEntryRequest.ts
 * 记得注释
 */

import CustomizeEntry from "./CustomizeEntry";

export default class CustomizeEntryRequest {
  //功能入口
  data: CustomizeEntry[] = []
  //是否开启配置  "start" 开启  "stop" 不开启
  state: string = 'stop'
}