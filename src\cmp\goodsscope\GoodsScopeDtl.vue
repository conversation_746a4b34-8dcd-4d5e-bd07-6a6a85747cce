<template>
  <div class="goods-range-dtl">
    <span class="text"
      v-if="(!goods || !goods.limit) && theme === 'standard' && showAll === false">{{ formatI18n('/公用/公共组件/商品范围控件/表单/当前营销中心全部商品') }}</span>
    <span class="text" v-if="(!goods || !goods.limit) && theme === 'standard' && showAll === true">{{ formatI18n('/公用/公共组件/商品范围控件/表单/全部商品') }}</span>
    <template v-if="goods && (theme === 'precisionmarketing' || goods.limit)">
      <div class="goods-header" v-if="goods.excludePrecondition && !hideTitle && theme === 'standard'">{{ formatI18n('/公用/公共组件/商品范围控件/表单/指定商品不适用') }}
      </div>
      <div class="goods-header" v-if="!goods.excludePrecondition && !hideTitle && theme === 'standard'">{{ formatI18n('/公用/公共组件/商品范围控件/表单/指定商品适用') }}
      </div>
      <div class="goods" style="display: flex;margin-top: 5px;">
        <div style="width: 40px;display: flex;" v-if="theme === 'precisionmarketing'">
          <div style="width: 38px">{{ formatI18n('/公用/公共组件/商品范围控件/表单/并且') }}</div>
          <div style="width: 2px;background-color: rgba(0, 0, 0, 0.24);">&nbsp;</div>
        </div>
        <div style="width: 100%;padding-left: 10px">
          <div style="margin: 15px 0" v-if="theme === 'precisionmarketing'">{{ formatI18n('/公用/公共组件/商品范围控件/表单/商品满足') }}</div>
          <div class="goods-content" :style="{backgroundColor: theme === 'standard'?'white':'#dddddd'}">
            <Connective1 style="width: 55px;margin: 15px 0;" :readonly="true" v-model="goods.relation"
              v-if="lines.length > 1 && theme !== 'precisionmarketing'"></Connective1>
            <div style="width: 100%">
              <div v-for="(line, index) of lines" :key="line.label + line.include">
                <div v-if="theme !== 'standard'" style="text-align: center;color: rgb(153, 153, 153)">
                  <span v-if="index !== 0">{{ formatI18n('/公用/公共组件/商品选择弹框组件/表格/并且') }}</span>
                  <span v-else>&nbsp;</span>
                </div>
                <div :class="line.include === formatI18n('/公用/券模板', '不属于') ? 'unsuited line-header' : 'suit line-header'">
                  <div>
                    <span>{{ line.label }}
                    </span>
                    <span style="margin-left:6px">{{ line.include }}
                    </span>
                  </div>
                  <div class="total-goods">
                    {{line.items? `${formatI18n("/资料/门店/共")}${line.items.length}${formatI18n('/公用/券模板/微盟适用商品/条')}` : ''}}
                  </div>
                </div>
                <div class="content-col">
                  <GoodsScopeInMemPage :data="line.items"></GoodsScopeInMemPage>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script lang="ts" src="./GoodsScopeDtl.ts">
</script>

<style lang="scss" scoped>
.goods-range-dtl {
  .text {
    height: 36px;
    line-height: 36px;
  }

  .goods-header {
    padding: 5px;
    height: 35px;
    line-height: 25px;
    font-size: 13px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #242633;
  }

  .goods {
    border: 1px solid #f0f0f0;
    width: 100%;
    padding: 12px;

    .goods-content {
      background-color: white;
      padding: 5px;
      line-height: 50px;
      display: flex;

      .line-header {
        position: relative;
        display: flex;
        justify-content: space-between;
        width: 100%;
        height: 28px;
        line-height: 30px;
        font-size: 12px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        padding: 0 16px;
        .total-goods {
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #36445a;
        }
      }

      .unsuited {
        background: #fee6ed;
        color: #fc0049;
        &::before {
          position: absolute;
          top: 0;
          left: 0;
          bottom: 0;
          content: "";
          width: 8px;
          height: 28px;
          background: #fc0049;
        }
      }

      .suit {
        background: #e7f9f0;
        color: #0cc66d;
        &::before {
          position: absolute;
          top: 0;
          left: 0;
          bottom: 0;
          content: "";
          width: 8px;
          height: 28px;
          background: #0cc66d;
        }
      }
    }

    .content-col {
      padding: 8px 0 12px;
    }
  }
}
</style>