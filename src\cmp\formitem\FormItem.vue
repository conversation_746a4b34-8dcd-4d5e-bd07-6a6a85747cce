<!--
 * @Author: 黎钰龙
 * @Date: 2023-06-28 11:13:02
 * @LastEditTime: 2023-07-28 17:26:34
 * @LastEditors: 黎钰龙
 * @Description: 
 * @FilePath: \phoenix-web-ui\src\cmp\formitem\FormItem.vue
 * 记得注释
-->
<template>
  <div class="qf-form-item">
    <label class="qf-form-label" :class="[{'is-required': required}]" v-bind:style="labelStyles" v-if="label || $slots.label">
      <slot name="label">{{label}}</slot>
    </label>
    <div class="qf-form-content" :style="fieldStyles">
      <slot ref="field"></slot>
    </div>
  </div>
</template>

<script lang="ts" src="./FormItem.ts">
</script>

<style lang="scss">
.qf-form-item {
  box-sizing: border-box;

  &:after {
    clear: both;
    display: table;
    content: "";
  }

  .qf-form-label {
    vertical-align: middle;
    float: left;
    font-size: 14px;
    color: #606266;
    line-height: 36px;
    box-sizing: border-box;
    padding-right: 8px;
    text-align: right;
  }

  .qf-form-label.is-required:before {
    content: "*";
    color: #f56c6c;
  }
}
</style>