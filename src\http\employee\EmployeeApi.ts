import ApiClient from 'http/ApiClient'
import RSEmployee from 'model/common/RSEmployee'
import RSEmployeeFilter from 'model/common/RSEmployeeFilter'
import RSSaveBatchEmployeeRequest from 'model/common/RSSaveBatchEmployeeRequest'
import Response from 'model/common/Response'

export default class EmployeeApi {
  /**
   * 查询员工
   *
   */
  static query(body: RSEmployeeFilter): Promise<Response<RSEmployee[]>> {
    return ApiClient.server().post(`/v1/employee/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询员工过滤门店
   *
   */
  static queryByStores(body: RSEmployeeFilter): Promise<Response<RSEmployee[]>> {
    return ApiClient.server().post(`/v1/employee/queryByStores`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除员工
   *
   */
  static remove(id: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/employee/remove`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 批量保存
   *
   */
  static saveBatch(body: RSSaveBatchEmployeeRequest): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/employee/saveBatch`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
