import ProjectApi from "http/auth/AuthApi";
import LoginApi from "http/login/LoginApi";
import MarketingCenterApi from "http/marketingcenter/MarketingCenterApi";
import BrowserMgr from "mgr/BrowserMgr";
import PermissionMgr from "mgr/PermissionMgr";
import LoginResult from "model/auth/LoginResult";
import RSMarketingCenter from "model/common/RSMarketingCenter";
import UserLoginResult from "model/login/UserLoginResult";
import EnvUtil from "util/EnvUtil";
import { Component, Vue } from "vue-property-decorator";
import { Action } from "vuex-class";
import User from "model/user/User";
@Component({
	components: {
        
	},
})

export default class UniPortalLogin extends Vue {
    @Action("i18n") actionI18n: any;
	@Action("loginInfo") setLoginInfo: any;
	@Action("permissions") actionPermissions: any;
    
    corsToken: Nullable<string> = null
    loading: Boolean = true
    token: Nullable<string> = null
    refreshToken: Nullable<string> = null

    orgList: any[] = [];
    resetToken = ""; // 密码过期，修改密码时使用的token
	useStrict = false; // 严格校验密码

    created() {
        console.log(this.$route.query['cors-token'])
        this.corsToken = this.$route.query['cors-token'] as string
        this.changeToken()
    }
    changeToken() {
        ProjectApi.changeToken(this.corsToken).then(res=>{
            console.log(res);
            if (res.headers['access-token']) {
                this.token = res.headers['access-token']
                sessionStorage.setItem('at', this.token as string)
                this.doLogin()
            }
            this.loading = false
        })
    }

    /**
	 * 登录
	 */
	doLogin() {
        // @ts-ignore
		LoginApi.login()
			.then((resp: any) => {
				if (resp.code === 2000 && resp.data!.state === "NORMAL") {
					this.setLoginInfo(resp.data);
					localStorage.setItem("customer", resp.data!.customer!);
					localStorage.setItem("ucenterUser", JSON.stringify(resp.data!.user));
					localStorage.setItem("fileBaseUrl", EnvUtil.getServiceUrl());
					this.getOrgList().then(() => {
						PermissionMgr.refreshPermission().then((menus: any[]) => {
							if (
								!menus ||
								menus.length === 0 || //
								!menus[0].children[0] ||
								menus[0].children[0].length === 0 || //
								!menus[0].children[0].children[0] ||
								menus[0].children[0].children[0].length === 0
							) {
                                
								this.$router.push({ name: "home", query: { from: "login" } });
							} else {
                                console.log(menus[0].children[0].children[0].hash);
								this.$router.push({ name: menus[0].children[0].children[0].hash, query: { from: "login" } });
							}
						});
					});
				} else if (resp.data && resp.data!.useStrict) {
					let loginInfo = new UserLoginResult();
					loginInfo.resetToken = resp.data!.resetToken;
					loginInfo.useStrict = resp.data!.useStrict;
					loginInfo.user = new User();
					loginInfo.user.account = resp.data!.user!.account;
					this.setLoginInfo(loginInfo);
					this.resetToken = resp.data!.resetToken!;
					this.useStrict = resp.data!.useStrict!;
					if (resp.data!.state === "PWD_EXPIRE") {
						this.$alert(this.formatI18n("/登录/你的密码已到期，请修改密码后，重新登录"), this.formatI18n("/公用/提示/提示"), {
							confirmButtonText: this.formatI18n("/公用/按钮/确定"),
						} as any).then(() => {
							this.loading = true;
						});
					} else if (resp.data!.state === "LOGIN_EXPIRE") {
						this.$alert(this.formatI18n("/登录/你的账户长时间未登录，请修改密码后，重新登录"), this.formatI18n("/公用/提示/提示"), {
							confirmButtonText: this.formatI18n("/公用/按钮/确定"),
						} as any).then(() => {
							this.loading = true;
						});
					}
				} else if (resp.data && resp.data!.state === "NEW_ACCOUNT") {
					let info = {
						account: resp.data.user!.account,
					};
					localStorage.setItem("ucenterUser", JSON.stringify(info));
					let loginInfo = new UserLoginResult();
					loginInfo.resetToken = resp.data!.resetToken;
					loginInfo.user = new User();
					loginInfo.user.account = resp.data.user!.account;
					this.setLoginInfo(loginInfo);
					this.$alert(this.formatI18n("初次登录请修改密码后再登录!"), this.formatI18n("/公用/提示/提示"), {
						confirmButtonText: this.formatI18n("/公用/按钮/确定"),
					} as any).then(() => {
						this.loading = true;
					});
				} else if (resp.data && resp.data!.state === "PWD_INCORRECT_FORMAT") {
					let info = {
						account: resp.data.user!.account,
					};
					localStorage.setItem("ucenterUser", JSON.stringify(info));
					let loginInfo = new UserLoginResult();
					loginInfo.resetToken = resp.data!.resetToken;
					loginInfo.user = new User();
					loginInfo.user.account = resp.data.user!.account;
					this.setLoginInfo(loginInfo);
					this.$alert(this.formatI18n("密码至少8位，且包含大小写字母，数字，特殊字符"), this.formatI18n("/公用/提示/提示"), {
						confirmButtonText: this.formatI18n("/公用/按钮/确定"),
					} as any).then(() => {
						this.loading = true;
					});
				} else if(resp.code !== 2000) {
					this.$message.error(resp.msg as string);
				}
			})
			.catch((error: any) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
				if (error && error.msg) {
					this.$message.error(error.msg);
				}
			});
	}

    getOrgList() {
		let sysConfig = BrowserMgr.LocalStorage.getItem("sysConfig");
		let promotionCenter = false;
		if (sysConfig) {
			promotionCenter = sysConfig.enableMultiMarketingCenter;
		}

		if (promotionCenter) {
			const account = JSON.parse(sessionStorage.getItem("vuex") as string)?.loginInfo.user?.account;

			return MarketingCenterApi.queryUserMarketCenter(account).then((res) => {
				this.orgList = res.data || [];
				const item: Nullable<RSMarketingCenter> = this.orgList ? this.orgList[0] : null;
				const org = item!.marketingCenter!.id || "";
				const orgName = item!.marketingCenter!.name || "";
				const headquarters = item!.headquarters
				sessionStorage.setItem("marketCenter", org);
				sessionStorage.setItem("marketCenterName", orgName);
				sessionStorage.setItem("headquarters", headquarters? 'true': 'false');
				return Promise.resolve();
			});
		} else {
			return Promise.resolve();
		}
	}
}