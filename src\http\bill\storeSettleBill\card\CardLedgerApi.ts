import ApiClient from 'http/ApiClient'
import Response from 'model/default/Response'
import CardLedgerFilter from "model/bill/storeSettleBill/card/CardLedgerFilter";
import CardLedger from "model/bill/storeSettleBill/card/CardLedger";

export default class CardLedgerApi {
  /**
   * 导出储值卡账单明细
   * 导出储值卡账单明细
   * 
   */
  static depositCardExport(body: CardLedgerFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/ledger/card/depositCard/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询储值卡账单
   * 分页查询储值卡账单。
   * 
   */
  static depositCardQuery(body: CardLedgerFilter): Promise<Response<CardLedger[]>> {
    return ApiClient.server().post(`/v1/ledger/card/depositCard/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导出礼品卡账单明细
   * 导出礼品卡账单明细
   * 
   */
  static prepayCardExport(body: CardLedgerFilter): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/ledger/card/prepayCard/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询礼品卡账单明细
   * 分页查询礼品卡账单明细。
   * 
   */
  static prepayCardQuery(body: CardLedgerFilter): Promise<Response<CardLedger[]>> {
    return ApiClient.server().post(`/v1/ledger/card/prepayCard/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
