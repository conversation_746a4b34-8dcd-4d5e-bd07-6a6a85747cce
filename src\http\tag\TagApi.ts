import ApiClient from 'http/ApiClient'
import Response from 'model/common/Response'
import TagOption from 'model/tag/TagOption'
import TagOptionFilter from 'model/tag/TagOptionFilter'

export default class TagApi {
  /**
   * 批量删除会员标签
   *
   */
  static batchRemove(body: string[]): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/member/tag/batch/remove`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 获取会员标签
   *
   */
  static get(tagId: string): Promise<Response<TagOption>> {
    return ApiClient.server().get(`/v1/member/tag/get/${tagId}`, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询会员标签
   *
   */
  static query(body: TagOptionFilter): Promise<Response<TagOption[]>> {
    return ApiClient.server().post(`/v1/member/tag/query`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 删除会员标签
   *
   */
  static remove(tagId: string): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/member/tag/remove`, {}, {
      params: {
        tagId: tagId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建会员标签
   *
   */
  static save(body: TagOption): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/member/tag/save`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 修改会员标签
   *
   */
  static saveOrModify(body: TagOption): Promise<Response<void>> {
    return ApiClient.server().post(`/v1/member/tag/saveOrModify`, body, {}).then((res) => {
      return res.data
    })
  }

}
