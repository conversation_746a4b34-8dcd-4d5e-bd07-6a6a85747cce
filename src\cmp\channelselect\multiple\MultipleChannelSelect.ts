import ChannelManagement from "model/channel/ChannelManagement";
import {Component, Prop, Vue, Watch} from "vue-property-decorator";
import ChannelManagementFilter from "model/channel/ChannelManagementFilter";
import {ChannelState} from "model/channel/ChannelState";
import ChannelManagement<PERSON><PERSON> from "http/channelmanagement/ChannelManagementApi";
import ChannelRange from "model/common/ChannelRange";
import Channel from "model/common/Channel";
import {ChannelRangeType} from "model/common/ChannelRangeType";


@Component({
	name: "MultipleChannelSelect",
	components: {},
	model: {
		prop: "value",
		event: "change",
	},
})
export default class MultipleChannelSelect extends Vue {
	$refs: any;

	@Prop()
	source: ChannelRange;

	@Prop({
		default: false,
	})
	disabled: boolean;

	@Prop({
		default: false,
	})
	hideHdPos: boolean;

	@Prop({
		default: false,
	})
	hideWechetChannel: boolean;

	@Prop({
		default: true,
	})
	showAll: boolean;

	@Prop({
		default: true,
	})
	showExclude: boolean;

	@Prop({
		default: '',
	})
  label: any

	rules: any;
	ruleForm = {
		selectType: "ALL",
		partSelected: [] = [],
		excludeSelected: [] = [],
	};

	channels: ChannelManagement[] = [];
	channelMap: Map<string, ChannelManagement> = new Map<string, ChannelManagement>();
  formLabel: any = ''

	@Watch("source", { immediate: true, deep: true })
	onWatchSelected(value: ChannelRange) {
		if (value && !value.ignore) {
			this.ruleForm.selectType = value.channelRangeType as any;
			if (value.channelRangeType === ChannelRangeType.PART) {
				this.ruleForm.partSelected = value.channels.map((e: any) => this.getKey(e)) as any;
			} else if (value.channelRangeType === ChannelRangeType.EXCLUDE) {
				this.ruleForm.excludeSelected = value.channels.map((e: any) => this.getKey(e)) as any;
			}
		}
		this.$forceUpdate();
	}

	created() {
    this.formLabel = this.label || this.formatI18n("/储值/会员储值/储值充值活动/编辑页面/活动渠道");
		this.rules = {
			partSelected: [
				{
					required: true,
					validator: (rule: any, value: any, callback: any) => {
						if (this.ruleForm.selectType === "PART") {
							if (!this.ruleForm.partSelected || this.ruleForm.partSelected.length == 0) {
								callback(this.formatI18n("/储值/会员储值/储值充值活动/编辑页面/请选择活动渠道"));
							}
						}
						callback();
					},
					trigger: ["blur", "change"],
				},
			],
			excludeSelected: [
				{
					required: true,
					validator: (rule: any, value: any, callback: any) => {
						if (this.ruleForm.selectType === "EXCLUDE") {
							if (!this.ruleForm.excludeSelected || this.ruleForm.excludeSelected.length == 0) {
								callback(this.formatI18n("/储值/会员储值/储值充值活动/编辑页面/请选择活动渠道"));
							}
						}
						callback();
					},
					trigger: ["blur", "change"],
				},
			],
		};
		this.initChannelMap();
	}

	initChannelMap() {
		let filter = new ChannelManagementFilter();
		filter.stateEquals = ChannelState.ENABLED;
		ChannelManagementApi.query(filter)
			.then((resp: any) => {
				if (resp && resp.code === 2000) {
					this.channels = resp.data;
					if (this.hideHdPos) {
						this.channels = this.channels.filter((channel: any) => !(channel.channel.type === "store" && channel.channel.id === "-"));
					}
					if (this.hideWechetChannel) {
						this.channels = this.channels.filter((channel: any) => channel.channel.type !== "weixin");
					}
					for (let channel of this.channels) {
						if (channel.channel && channel.channel.type && channel.channel.id) {
							this.channelMap.set(this.getKey(channel.channel) as string, channel);
						}
					}
					this.$emit("getAllData", this.channels);
				}
			})
			.catch((error) => {
				if (error && error.message) {
					this.$message.error(error.message);
				}
			});
	}

	onTypeChange(value: any) {
		if (value && value === "ALL") {
			this.ruleForm.partSelected = [];
			this.ruleForm.excludeSelected = [];
		} else if (value && value === "PART") {
			this.ruleForm.excludeSelected = [];
		} else if (value && value === "EXCLUDE") {
			this.ruleForm.partSelected = [];
		}
		this.validate();
		this.onChange();
	}

	validate() {
		return this.$refs.multipleChannelSelect.validate();
	}

	onChange() {
		let result = new ChannelRange();
		if (this.ruleForm.selectType === "ALL") {
			result.channelRangeType = ChannelRangeType.ALL;
		} else if (this.ruleForm.selectType === "PART") {
			result.channelRangeType = ChannelRangeType.PART;
			result.channels = this.getSelectChannels(this.ruleForm.partSelected as any);
		} else if (this.ruleForm.selectType === "EXCLUDE") {
			result.channelRangeType = ChannelRangeType.EXCLUDE;
			result.channels = this.getSelectChannels(this.ruleForm.excludeSelected as any);
		}
		this.$forceUpdate();
		this.$emit("channelCallBack", result);
	}

	private getKey(channel: Channel) {
		if (channel && channel.type && channel.id) {
			return (channel.type as any) + channel.id;
		}
		return channel.typeId;
	}

	private getSelectChannels(selected: []) {
		let channels: Channel[] = [];
		for (let item of selected) {
			if (this.channelMap.has(item)) {
				let channelMng = this.channelMap.get(item);
				if (channelMng && channelMng.channel) {
					channels.push(channelMng.channel);
				}
			}
		}
		return channels;
	}
}